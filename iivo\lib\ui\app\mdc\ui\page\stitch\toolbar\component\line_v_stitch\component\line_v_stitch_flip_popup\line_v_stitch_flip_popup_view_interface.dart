import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'line_v_stitch_flip_popup_view_interface.freezed.dart';

@freezed
class LineVStitchFlipState with _$LineVStitchFlipState {
  const factory LineVStitchFlipState({
    required bool flipInsideState,
    required bool flipOutsideState,
  }) = _LineVStitchFlipState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class LineVStitchFlipStateViewInterface
    extends ViewModel<LineVStitchFlipState> {
  LineVStitchFlipStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// 内向きの選択ボタンをクリックします
  ///
  void onInsideClicked();

  ///
  /// 外向きの選択ボタンをクリックします
  ///
  void onOutsideClicked();
}
