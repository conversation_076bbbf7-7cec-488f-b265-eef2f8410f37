// ignore_for_file: camel_case_types

import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';

import '../../xd_component.dart';

class pre_category_header extends StatelessWidget {
  const pre_category_header({
    super.key,

    /// Zoomボタン
    this.showZoomButton = true,
    this.zoomButtonState = ButtonState.disable,
    this.onZoomButtonClicked,
    this.zoomIcon = const ico_zoom100(),

    /// Filterボタン
    this.showFilterButton = false,
    this.filterButtonInfo = "",
    this.isFilterApplied = false,
    this.onFilterButtonClicked,
    this.onFilterResetButtonClicked,

    /// Informationボタン
    this.showInformationButton = true,
    this.informationButtonState = ButtonState.disable,
    this.onInformationButtonClicked,

    /// RealPreviewボタン
    this.realPreviewButtonState = ButtonState.disable,
    this.onRealPreviewButtonClicked,
  });

  /// Zoomボタン
  final bool showZoomButton;
  final ButtonState zoomButtonState;
  final void Function()? onZoomButtonClicked;
  final Widget? zoomIcon;

  /// Filterボタン
  final bool showFilterButton;
  final String filterButtonInfo;
  final bool isFilterApplied;
  final void Function()? onFilterButtonClicked;
  final void Function()? onFilterResetButtonClicked;

  /// Informationボタン
  final bool showInformationButton;
  final ButtonState informationButtonState;
  final void Function()? onInformationButtonClicked;

  /// RealPreviewボタン
  final ButtonState realPreviewButtonState;
  final void Function()? onRealPreviewButtonClicked;

  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    return SizedBox(
      height: 63,
      width: 776,
      child: Row(
        children: [
          showZoomButton == true
              ? Expanded(
                  flex: 133,
                  child: grp_btn_zoom(
                    state: zoomButtonState,
                    onTap: onZoomButtonClicked,
                    child: zoomIcon,
                  ),
                )
              : const Spacer(flex: 133),
          const Spacer(flex: 16),
          Expanded(
            flex: showFilterButton == true && isFilterApplied ? 343 : 481,
            child: showFilterButton == true
                ? Center(
                    child: grp_btn_filter(
                      text: filterButtonInfo,
                      state: isFilterApplied
                          ? ButtonState.select
                          : ButtonState.normal,
                      isFilterApplied: isFilterApplied,
                      onTap: onFilterButtonClicked,
                    ),
                  )
                : Container(),
          ),
          showFilterButton == true && isFilterApplied
              ? Expanded(
                  flex: 138,
                  child: Row(
                    children: [
                      const Spacer(flex: 8),
                      Expanded(
                        flex: 63,
                        child: grp_btn_close_01(
                          state: ButtonState.select,
                          style: ThemeButton.btn_n_size63x63_theme7,
                          onTap: onFilterResetButtonClicked,
                        ),
                      ),
                      const Spacer(flex: 67),
                    ],
                  ),
                )
              : Container(),
          const Spacer(flex: 12),
          showInformationButton == true
              ? Expanded(
                  flex: 63,
                  child: CustomTooltip(
                    message: l10n.tt_emb_patterninfo,
                    child: grp_btn_emb_info(
                      state: informationButtonState,
                      onTap: onInformationButtonClicked,
                    ),
                  ),
                )
              : const Spacer(flex: 63),
          const Spacer(flex: 8),
          Expanded(
            flex: 63,
            child: CustomTooltip(
              message: l10n.tt_emb_realpreview,
              child: grp_btn_emb_realpreview_01(
                state: realPreviewButtonState,
                onTap: onRealPreviewButtonClicked,
              ),
            ),
          )
        ],
      ),
    );
  }
}

class pre_category_header_filter extends pre_category_header {
  const pre_category_header_filter({super.key});
}
