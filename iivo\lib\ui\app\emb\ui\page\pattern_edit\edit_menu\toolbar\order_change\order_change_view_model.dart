import 'dart:ffi';

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../model/order_model.dart';
import '../../../../../../model/pattern_model.dart';
import '../../../../../../model/redo_undo_model.dart';
import '../../../../../../model/scan_model.dart';
import '../../../../common_component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../../../pattern_edit_view_model.dart';
import 'order_change_view_interface.dart';

///
/// 表示領域のItemの高さ
///
const double _itemHeight = 134.0;

///
/// 表示領域の高さ
///
const double _listAreaHeight = 594.0;

final orderChangeViewModelProvider = StateNotifierProvider.autoDispose<
    OrderChangeViewModelInterface, OrderChangeState>(
  (ref) => OrderChangeViewModel(ref),
);

class OrderChangeViewModel extends OrderChangeViewModelInterface {
  OrderChangeViewModel(
    AutoDisposeStateNotifierProviderRef ref,
  ) : super(
            OrderChangeState(
              selectedIndex: 0,
              imageList: [],
              patternIsBorderList: [],
              isUnableForward: false,
              isUnableBackward: false,
              itemHeight: _itemHeight,
              listAreaHeight: _listAreaHeight,
              scrollController: ScrollController(),
              isEnglish: true,
              isScanPopupOpen: false,
            ),
            ref) {
    /// 状態の初期化
    _initUiState();
    _listenStatus();

    /// プロジェクト投影画面を更新する
    ProjectorLibrary().apiBinding.setProjectorEmbPatternDrawOrder(
        ProjectorEmbPatternDrawOrder.projectorEmbPatternDrawOrder_Entry);
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);

    /// 選択したItemセットアップ
    state.scrollController.dispose();
    state = state.copyWith(
        scrollController: ScrollController(
            initialScrollOffset: _getJumpPositioned(state.selectedIndex)));
  }

  @override
  void update() {
    int selectedIndex = _getSelectedPatternIndex();
    List<Widget> imageList = OrderModel().getOrderDisplayImage(
        zoomScale: PatternModel().selectedZoomScaleInEditPage);

    /// View更新
    state = state.copyWith(
      selectedIndex: selectedIndex,
      imageList: imageList,
      patternIsBorderList: OrderModel.getEmbIsBorder(),
      isEnglish: PatternModel().isEnglish,
      isScanPopupOpen: ScanModel().isEditPageScanPopupOpen,
    );
  }

  @override
  void updateByScanPopupChange() {
    state =
        state.copyWith(isScanPopupOpen: ScanModel().isEditPageScanPopupOpen);
  }

  @override
  int get getImageAreaWidth => orderItemImageAreaWidth;

  @override
  int get getImageAreaHeight => orderItemImageAreaHeight;

  @override
  void onOrderListItemClicked(int index) {
    /// Model更新
    PatternModel().selectPatternWithSewingIndex(index);

    /// View更新
    update();
    _jumpToPositioned(index);

    /// 他の画面を更新する
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onOrderForwardButtonClicked() {
    if (state.isUnableForward) {
      return;
    }

    /// Model更新
    OrderModel.changeOrderNext();

    /// View更新
    update();
    _jumpToPositioned(state.selectedIndex);

    /// 他の画面を更新する
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onOrderBackwardButtonClicked() {
    if (state.isUnableBackward) {
      return;
    }

    /// Model更新
    OrderModel.changeOrderPrevious();

    /// View更新
    update();
    _jumpToPositioned(state.selectedIndex);

    /// 他の画面を更新する
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onOrderFrontButtonClicked() {
    if (state.isUnableForward) {
      return;
    }

    /// Model更新
    OrderModel.changeOrderLast();

    /// View更新
    update();
    _jumpToPositioned(state.selectedIndex);

    /// 他の画面を更新する
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onOrderBackButtonClicked() {
    if (state.isUnableBackward) {
      return;
    }

    /// Model更新
    OrderModel.changeOrderFirst();

    /// View更新
    update();
    _jumpToPositioned(state.selectedIndex);

    /// 他の画面を更新する
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onResetButtonClicked() {
    /// Lib通知
    RedoUndoModel().undoRedo();
    PatternModel().reloadAllPattern();

    /// View更新
    update();

    /// 選択したItemセットアップ
    state.scrollController.dispose();
    state = state.copyWith(
        scrollController: ScrollController(
            initialScrollOffset: _getJumpPositioned(state.selectedIndex)));

    /// 他の画面を更新する
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onOKButtonClicked(BuildContext context) {
    /// View更新
    PopupNavigator.pop(context: context);

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);

    ProjectorLibrary().apiBinding.setProjectorEmbPatternDrawOrder(
        ProjectorEmbPatternDrawOrder
            .projectorEmbPatternDrawOrder_CurrentSelect);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  ///
  /// 指定された座標にジャンプ
  ///
  void _jumpToPositioned(int index) {
    state.scrollController.jumpTo(_getJumpPositioned(index));
  }

  ///
  /// リスト内でジャンプ可能な座標の計算
  ///
  double _getJumpPositioned(int index) {
    /// Itemの間隔
    const double itemSpace = 8;

    double positioned = index * (state.itemHeight + itemSpace);

    /// リストの最大高さ
    double maxListHeight =
        (state.itemHeight + itemSpace) * state.imageList.length;

    /// ジャンプ可能な最大座標
    double maxJumpPositioned;

    if (maxListHeight < state.listAreaHeight) {
      /// リストの全長が表示領域を超えていない場合
      maxJumpPositioned = 0;
    } else {
      /// リストの全長が表示領域を超えた場合
      maxJumpPositioned = maxListHeight - state.listAreaHeight;
    }

    return positioned.clamp(0, maxJumpPositioned);
  }

  ///
  /// Orderページ現在選択されているPatternのIndexを取得する
  /// Orderページは複数選択ではアクセスできないので、selectedIndexは1つしかありません
  ///
  int _getSelectedPatternIndex() => PatternModel().getCurrentGroupSewingIndex();

  ///
  /// ページの状態を初期化する
  ///
  void _initUiState() {
    final embEditAttrib = ref.read(
        appDisplayEmbStateProvider.select((value) => value.embEditAttrib.ref));
    int selectedIndex = _getSelectedPatternIndex();

    List<Widget> imageList = OrderModel().getOrderDisplayImage(
        zoomScale: PatternModel().selectedZoomScaleInEditPage);

    state = state.copyWith(
      selectedIndex: selectedIndex,
      imageList: imageList,
      patternIsBorderList: OrderModel.getEmbIsBorder(),
      isEnglish: PatternModel().isEnglish,
      isScanPopupOpen: ScanModel().isEditPageScanPopupOpen,
      isUnableForward: _isForwardButtonUnable(
          selectedIndex, embEditAttrib.subFrontOrderState),
      isUnableBackward: _isBackwardButtonUnable(
          selectedIndex, embEditAttrib.subBackOrderState),
    );
  }

  ///
  /// 状態をリッスンします
  ///
  void _listenStatus() {
    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.subFrontOrderState),
      (previous, nextState) {
        int selectedIndex = _getSelectedPatternIndex();
        state = state.copyWith(
          isUnableForward: _isForwardButtonUnable(selectedIndex, nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.subBackOrderState),
      (previous, nextState) {
        int selectedIndex = _getSelectedPatternIndex();
        state = state.copyWith(
          isUnableBackward: _isBackwardButtonUnable(selectedIndex, nextState),
        );
      },
    );
  }

  ///
  /// 並べ替えの前方向ボタンの状態
  ///
  bool _isForwardButtonUnable(int selectedIndex, int subOrderState) {
    bool isUnableForward =
        (selectedIndex == PatternModel().getAllPattern().length - 1);
    bool isButtonUnable = AppInfoFuncState.getValueByNumber(subOrderState) ==
        AppInfoFuncState.disable;

    return isButtonUnable || isUnableForward;
  }

  ///
  /// 並べ替えの後方向ボタンの状態
  ///
  bool _isBackwardButtonUnable(int selectedIndex, int subOrderState) {
    bool isUnableBackward = (selectedIndex == 0);
    bool isButtonUnable = AppInfoFuncState.getValueByNumber(subOrderState) ==
        AppInfoFuncState.disable;

    return isButtonUnable || isUnableBackward;
  }
}
