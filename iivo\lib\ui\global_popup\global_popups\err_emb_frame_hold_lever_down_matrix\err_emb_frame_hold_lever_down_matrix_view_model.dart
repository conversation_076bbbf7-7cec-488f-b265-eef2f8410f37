import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_emb_frame_hold_lever_down_matrix_view_interface.dart';

final errEmbFrameHoldLeverDownMatrixViewModelProvider =
    StateNotifierProvider.family.autoDispose<
            ErrEmbFrameHoldLeverDownMatrixViewInterface,
            ErrEmbFrameHoldLeverDownMatrixState,
            BuildContext>(
        (ref, context) =>
            ErrEmbFrameHoldLeverDownMatrixViewModel(ref, context));

class ErrEmbFrameHoldLeverDownMatrixViewModel
    extends ErrEmbFrameHoldLeverDownMatrixViewInterface {
  ErrEmbFrameHoldLeverDownMatrixViewModel(Ref ref, BuildContext context)
      : super(const ErrEmbFrameHoldLeverDownMatrixState(), ref, context);

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    final int errCode = TpdLibrary().apiBinding.bpIFSendDisplayDataSync(
        BPIFSendKey.KEYERROREMBFRAMEHOLDLEVERDOWNPOWERFORMONITORING);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }
}
