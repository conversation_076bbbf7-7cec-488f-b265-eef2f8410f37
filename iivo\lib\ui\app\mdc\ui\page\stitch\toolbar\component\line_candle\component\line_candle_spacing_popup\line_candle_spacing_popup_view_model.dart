import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_candle_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'line_candle_spacing_popup_view_interface.dart';

/// 読み出したSpaceデータの変換倍率 '読み出したデータの単位/10=mm'
const double _conversionRate = 10.0;

final lineCandleSpaceViewModelProvider = StateNotifierProvider.autoDispose<
    LineCandleSpaceStateViewInterface,
    LineCandleSpaceState>((ref) => LineCandleSpaceViewModel(ref));

class LineCandleSpaceViewModel extends LineCandleSpaceStateViewInterface {
  LineCandleSpaceViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineCandleSpaceState(
              spaceDisplayValue: "",
              isDefaultStyle: false,
              plusButtonValid: false,
              minusButtonValid: false,
            ),
            ref) {
    update();
  }

  ///
  /// ステップ量
  ///
  final int _stepLongPressValue = 10;
  final int _stepClickValue = 1;

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  /// Spacing値表示星
  ///
  bool _isSpaceValueDisplayStar =
      LineCandleModel().getSpace() != LineCandleModel.spacingNotUpdating
          ? false
          : true;

  ///
  /// Spacing値
  ///
  int _spaceValue = LineCandleModel().getSpace();

  @override
  void update() {
    state = state.copyWith(
      spaceDisplayValue: _getSpaceDisplayValue(),
      isDefaultStyle: _isDefaultStyle(),
      plusButtonValid: _getPlusButtonState(),
      minusButtonValid: _getMinusButtonState(),
    );
  }

  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isSpaceValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSpaceValueDisplayStar = false;

      ///  Model 更新
      _spaceValue = LineCandleModel().spaceDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_spaceValue == LineCandleModel.miniSpaceValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress == true) {
      _spaceValue -= _spaceValue % _stepLongPressValue;
      if (_spaceValue > LineCandleModel.miniSpaceValue) {
        ///  Model 更新
        _spaceValue -= _stepLongPressValue;
      }
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      _spaceValue -= _stepClickValue;
    }

    update();
    return true;
  }

  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_isSpaceValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSpaceValueDisplayStar = false;

      ///  Model 更新
      _spaceValue = LineCandleModel().spaceDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_spaceValue == LineCandleModel.maxiSpaceValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress == true) {
      _spaceValue -= _spaceValue % _stepLongPressValue;
      if (_spaceValue < LineCandleModel.maxiSpaceValue) {
        ///  Model 更新
        _spaceValue += _stepLongPressValue;
      }
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      _spaceValue += _stepClickValue;
    }

    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineCandleSpace.toString());
    if (_isSpaceValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int spaceValue = LineCandleModel().getSpace();

    /// Model 更新
    LineCandleModel().setSpace(_spaceValue);
    if (LineCandleModel().setMdcCandleWickingSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (spaceValue != _spaceValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// 間隔の表示値を取得します
  ///
  String _getSpaceDisplayValue() {
    /// cmからmmへ
    double spaceValue = _spaceValue / _conversionRate;

    if (_isSpaceValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      }
      return "*.***";
    }

    if (currentSelectedUnit == Unit.mm) {
      return spaceValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(spaceValue);
  }

  ///
  /// 間隔表示テキスト スタイルを取得します
  ///
  bool _isDefaultStyle() {
    if (_isSpaceValueDisplayStar) {
      return true;
    }

    if (_spaceValue == LineCandleModel().spaceDefaultValue) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isSpaceValueDisplayStar) {
      return true;
    }

    if (_spaceValue <= LineCandleModel.miniSpaceValue) {
      return false;
    }

    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isSpaceValueDisplayStar) {
      return true;
    }

    if (_spaceValue >= LineCandleModel.maxiSpaceValue) {
      return false;
    }

    return true;
  }
}
