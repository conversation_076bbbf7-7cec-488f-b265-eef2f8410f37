import 'dart:typed_data';

import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'usb_manager_method_channel.dart';

abstract class UsbManagerPlatform extends PlatformInterface {
  /// Constructs a UsbManagerPlatform.
  UsbManagerPlatform() : super(token: _token);

  static final Object _token = Object();

  static UsbManagerPlatform _instance = MethodChannelUsbManager();

  /// The default instance of [UsbManagerPlatform] to use.
  ///
  /// Defaults to [MethodChannelUsbManager].
  static UsbManagerPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [UsbManagerPlatform] when
  /// they register themselves.
  static set instance(UsbManagerPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<bool?> usbManagerInit() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  Future<String?> getDeviceList() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  Future<void> receiveMessage(Function refresh) {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  Future<String?> readJsonFromZip(String zipFilePath, String fileName,
      {String? password}) {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  Future<bool?> unZip(String zipFilePath, String dirPath, {String? password}) {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  Future<int> getUsbFreeSpace(String path) {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  Future<bool?> extractDirFromUpf(
      {required String upfFilepath,
      required String outputDirPath,
      required String dirName,
      String? password}) {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  Future<bool?> extractUpfHeaderFromUpf(
      {required String upfFilepath,
      required String outputDirPath,
      String? password}) {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  Future<String?> calculateSHA256(String filePath) {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  Future<bool> verifyUPFIntegrity(
      String zipFilePath, String password, Uint8List publicKeyPemDataList) {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }

  Future<bool> compressFolder(List<String> sourceFolderPathList,
      String outputZipFilePath, String password) {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
