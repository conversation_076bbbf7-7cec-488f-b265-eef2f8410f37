import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../model/device_memory_model.dart';
import '../../../../../model/write_debug_log_task_queue.dart.dart';

import '../../../../global_popup/panel_popup_route.dart';
import 'error_list_display_state.dart';

final errorListDisplayViewModelProvider =
    AutoDisposeStateNotifierProvider((ref) => ErrorListDisplayViewModel());

class ErrorListDisplayViewModel extends StateNotifier<ErrorListDisplayState> {
  ErrorListDisplayViewModel() : super(const ErrorListDisplayState());

  ///
  /// ログをUSBにコピーする
  ///
  Future<void> copyDebugLogToUSB() async {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    PanelPopupRoute().openPleaseWaitPopup(playPopupSound: false);
    await Future.wait([
      Future(() async {
        await WriteDebugLogTaskQueue.getInstance().saveDebugLogToUSB();
      }),
      Future.delayed(pleaseWaitTime)
    ]).then((value) => PanelPopupRoute().closePleaseWaitPopup());
  }
}
