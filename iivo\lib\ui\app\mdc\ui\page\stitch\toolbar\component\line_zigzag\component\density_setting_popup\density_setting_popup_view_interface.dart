import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'density_setting_popup_view_interface.freezed.dart';

@freezed
class DensitySettingPopupState with _$DensitySettingPopupState {
  const factory DensitySettingPopupState(
      {required int densityValue,
      required String densityDisplayValue,
      required bool isDefaultValue,
      required bool plusButtonValid,
      required bool minusButtonValid}) = _DensitySettingPopupState;
}

abstract class DensitySettingPopupViewInterface
    extends ViewModel<DensitySettingPopupState> {
  DensitySettingPopupViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPluButtonClicked(bool isLongPress);

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();
}
