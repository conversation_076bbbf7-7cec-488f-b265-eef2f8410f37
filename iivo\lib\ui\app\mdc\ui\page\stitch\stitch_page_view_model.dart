import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../../model/projector_model.dart';
import '../../../model/resume_history_model.dart';
import '../../../model/stitch/draw_region_model.dart';
import 'bottom/bottom_view_model.dart';
import 'projector/camera_pen/camera_pen_view_model.dart';
import 'stitch_page_view_interface.dart';
import 'toolbar/component/line_candle/component/line_candle_size_popup/line_candle_size_popup.dart';
import 'toolbar/component/line_candle/component/line_candle_spacing_popup/line_candle_spacing_popup.dart'
    as line_candle_spacing;
import 'toolbar/component/line_chain/component/line_chain_size_popup/chain_size.dart';
import 'toolbar/component/line_chain/component/line_chain_thickness_popup/chain_thickness.dart';
import 'toolbar/component/line_e_stitch/component/line_e_stitch_flip_popup/line_e_stitch_flip_popup.dart'
    as line_e_stitch_flip;
import 'toolbar/component/line_e_stitch/component/line_e_stitch_spacing_popup/line_e_stitch_spacing_popup.dart'
    as line_e_stitch_spacing;
import 'toolbar/component/line_e_stitch/component/line_e_stitch_thickness_popup/line_e_stitch_thickness_popup.dart'
    as line_e_stitch_thickness;
import 'toolbar/component/line_e_stitch/component/line_e_stitch_width_popup/line_e_stitch_width_popup.dart'
    as line_e_stitch_width;
import 'toolbar/component/line_motif/component/line_motif_flip_popup/motif_flip_popup.dart';
import 'toolbar/component/line_motif/component/line_motif_size_popup/motif_size_popup.dart';
import 'toolbar/component/line_motif/component/line_motif_spacing_popup/motif_spacing_popup.dart';
import 'toolbar/component/line_property/line_property_popup.dart';
import 'toolbar/component/line_rough_zigzag/component/density_setting_popup/density_setting_popup.dart'
    as line_rough_zigzag_density;
import 'toolbar/component/line_rough_zigzag/component/width_setting_popup/width_setting_popup.dart'
    as line_rough_zigzag_width;
import 'toolbar/component/line_running/run_pitch_popup/run_pitch_popup.dart'
    as line_running_run_pitch;
import 'toolbar/component/line_running/under_sewing_popup/under_sewing_setting_popup.dart'
    as line_running_under_sewing;
import 'toolbar/component/line_triple/run_pitch_popup/run_pitch_popup.dart'
    as line_triple_run_pitch;
import 'toolbar/component/line_triple/under_sewing_popup/under_sewing_setting_popup.dart'
    as line_triple_under_sewing;
import 'toolbar/component/line_v_stitch/component/line_v_stitch_flip_popup/line_v_stitch_flip_popup.dart'
    as line_v_stitch_flip;
import 'toolbar/component/line_v_stitch/component/line_v_stitch_spacing_popup/line_v_stitch_spacing_popup.dart'
    as line_v_stitch_spacing;
import 'toolbar/component/line_v_stitch/component/line_v_stitch_thickness_popup/line_v_stitch_thickness_popup.dart'
    as line_v_stitch_thickness;
import 'toolbar/component/line_v_stitch/component/line_v_stitch_width_popup/line_v_stitch_width_popup.dart'
    as line_v_stitch_width;
import 'toolbar/component/line_zigzag/component/density_setting_popup/density_setting_popup.dart'
    as line_zigzag_density;
import 'toolbar/component/line_zigzag/component/width_setting_popup/width_setting_popup.dart'
    as line_zigzag_width;
import 'toolbar/component/surface_decorative_fill/component/direction_setting_popup/direction_setting_popup.dart';
import 'toolbar/component/surface_decorative_fill/component/outline_setting_popup/outline_setting_popup.dart';
import 'toolbar/component/surface_decorative_fill/component/position_offset_setting_popup/position_offset_setting_popup.dart';
import 'toolbar/component/surface_decorative_fill/component/random_shift_setting_popup/random_shift_setting_popup.dart';
import 'toolbar/component/surface_decorative_fill/component/size_setting_popup/size_setting_popup.dart';
import 'toolbar/component/surface_decorative_fill/component/thickness_setting_popup/thickness_setting_popup_popup.dart';
import 'toolbar/component/surface_property/surface_property_popup.dart';
import 'toolbar/component/surface_stipple/component/stipple_distance_popup/distance_popup.dart';
import 'toolbar/component/surface_stipple/component/stipple_run_pitch_popup/run_pitch_popup.dart'
    as surface_stipple;
import 'toolbar/component/surface_stipple/component/stipple_spacing_popup/spacing_popup.dart';
import 'toolbar/component/surface_stipple/component/stipple_stitch_popup/stitch_popup.dart';
import 'toolbar/component/surface_tatami/component/density_setting_popup/density_setting_popup.dart'
    as surface_tatami_density;
import 'toolbar/component/surface_tatami/component/direction_setting_popup/direction_setting_popup.dart';
import 'toolbar/component/surface_tatami/component/pull_compensation_setting_popup/pull_compensation_setting_popup.dart';
import 'toolbar/component/surface_tatami/component/under_sewing_setting_popup/under_sewing_setting_popup.dart'
    as surface_tatami_under_sewing;
import 'toolbar/toolbar_view_model.dart';
import 'top_bar/component/magnification_popup/magnification_popup.dart';
import 'top_bar/top_bar_view_model.dart';
import 'view_area/component/projector_frame_drag_area/projector_frame_drag_area_view_model.dart';
import 'view_area/view_area_view_mode.dart';

final stitchPageViewInfoProvider =
    StateNotifierProvider.autoDispose<_StitchPageViewInfo, void>(
        (_) => _StitchPageViewInfo());

class _StitchPageViewInfo extends StateNotifier<void> {
  _StitchPageViewInfo() : super(null);
  @override
  // ignore: override_on_non_overriding_member
  void build() {}

  BuildContext? _context;
  BuildContext get context => _context!;
  set context(value) => _context = value;

  SubRoutePopupNavigatorState? _navigator;
  SubRoutePopupNavigatorState get navigator => _navigator!;
  set navigator(value) => _navigator = value;
}

/// view _modelに必要な構造
final stitchPageViewModelProvider =
    StateNotifierProvider.autoDispose<StitchPageViewModel, StitchPageState>(
        (ref) {
  final context = ref.read(stitchPageViewInfoProvider.notifier).context;
  return StitchPageViewModel(ref, context);
});

enum ModuleType {
  lineZigzag,
  lineRunning,
  lineTriple,
  lineCandle,
  surfaceStipple,
  lineEStitch,
  lineVStitch,
  lineChain,
  surfaceTatami,
  surfaceDecorativeFill,
  lineMotif,
  lineRoughZigzag,
}

enum PopupEnum {
  lineProperty,
  surfaceProperty,
  magnification,
  lineZigzagWidth,
  lineZigzagDensity,
  lineRunningRunPitch,
  lineRunningUnderSewing,
  lineTripleRunPitch,
  lineTripleUnderSewing,
  lineCandleSize,
  lineCandleSpace,
  lineChainSize,
  lineChainThickness,
  lineEStitchWidth,
  lineEStitchSpace,
  lineEStitchThickness,
  lineEStitchFlip,
  lineVStitchWidth,
  lineVStitchSpace,
  lineVStitchThickness,
  lineVStitchFlip,
  lineMotifSize,
  lineMotifSpacing,
  lineMotifFlip,
  lineRoughZigzagWidth,
  lineRoughZigzagDensity,
  surfaceTatamiDirection,
  surfaceTatamiDensity,
  surfaceTatamiPullCompensation,
  surfaceTatamiUnderSewing,
  surfaceStippleRunPitch,
  surfaceStippleSpacing,
  surfaceStippleDistance,
  surfaceStippleStitch,
  surfaceDecorativeFillSize,
  surfaceDecorativeFillDirection,
  surfaceDecorativeFillOutline,
  surfaceDecorativeFillRandomShift,
  surfaceDecorativeFillPositionOffset,
  surfaceDecorativeFillThickness,
}

List<String> get _popupEnumList => PopupEnum.values
    .map((e) => 'PopupEnum.${e.name}')
    .where((str) =>
        (str != "PopupEnum.lineProperty") &&
        (str != "PopupEnum.surfaceProperty") &&
        (str != "PopupEnum.magnification"))
    .toList();

enum ComponentType {
  topBar,
  toolbar,
  viewArea,
  bottom,
  cameraPenUI,
  projector,
  // ignore: constant_identifier_names
  topBar_ProjectorButton,
}

class StitchPageViewModel extends StitchPageViewInterface {
  StitchPageViewModel(
      AutoDisposeStateNotifierProviderRef ref, BuildContext context)
      : super(const StitchPageState(), ref, context) {
    ResumeHistoryModel().currentPageType = ResumePageType.stitch;
    DrawRegionModel().initRegionInfoAndParamDefault();
  }
  @override
  bool get hasPop => _popupEnumList.contains(ref
      .readAutoNotifierIfExists(stitchPageViewInfoProvider)
      ?.navigator
      .getTopPopupName());

  ///
  /// 子画面のView更新
  ///
  @override
  void updateStitchPageByChild() {
    bool needShow;
    if (hasPop == true) {
      needShow = false;
    } else {
      needShow = true;
    }

    /// BottomとTopBar表示設定する
    state = state.copyWith(
      isDisplayBottom: needShow,
      isDisplayToolBar: needShow,
    );
  }

  ///
  /// 名前付きルートの登録
  ///
  @override
  Map<String, PopupRouteBuilder> registerNamedPopup() => {
        PopupEnum.lineProperty.toString(): PopupRouteBuilder(
          builder: (context) => const LinePropertyPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceProperty.toString(): PopupRouteBuilder(
          builder: (context) => const MdcEditSurfacePropertyPopup(),
          barrier: false,
        ),
        PopupEnum.magnification.toString(): PopupRouteBuilder(
          builder: (context) => const MagnificationPopup(),
          barrier: false,
        ),
        PopupEnum.lineZigzagWidth.toString(): PopupRouteBuilder(
          builder: (context) => const line_zigzag_width.WidthSettingPopup(),
          barrier: false,
        ),
        PopupEnum.lineZigzagDensity.toString(): PopupRouteBuilder(
          builder: (context) => const line_zigzag_density.DensitySettingPopup(),
          barrier: false,
        ),
        PopupEnum.lineRunningRunPitch.toString(): PopupRouteBuilder(
          builder: (context) => const line_running_run_pitch.RunPitchPopup(),
          barrier: false,
        ),
        PopupEnum.lineRunningUnderSewing.toString(): PopupRouteBuilder(
          builder: (context) =>
              const line_running_under_sewing.UnderSewingSettingPopup(),
          barrier: false,
        ),
        PopupEnum.lineTripleRunPitch.toString(): PopupRouteBuilder(
          builder: (context) => const line_triple_run_pitch.RunPitchPopup(),
          barrier: false,
        ),
        PopupEnum.lineTripleUnderSewing.toString(): PopupRouteBuilder(
          builder: (context) =>
              const line_triple_under_sewing.UnderSewingSettingPopup(),
          barrier: false,
        ),
        PopupEnum.lineCandleSize.toString(): PopupRouteBuilder(
          builder: (context) => const SizePopup(),
          barrier: false,
        ),
        PopupEnum.lineCandleSpace.toString(): PopupRouteBuilder(
          builder: (context) => const line_candle_spacing.SpacePopup(),
          barrier: false,
        ),
        PopupEnum.lineChainSize.toString(): PopupRouteBuilder(
          builder: (context) => const ChainSizePopup(),
          barrier: false,
        ),
        PopupEnum.lineChainThickness.toString(): PopupRouteBuilder(
          builder: (context) => const ChainThicknessPopup(),
          barrier: false,
        ),
        PopupEnum.lineEStitchWidth.toString(): PopupRouteBuilder(
          builder: (context) => const line_e_stitch_width.WidthPopup(),
          barrier: false,
        ),
        PopupEnum.lineEStitchSpace.toString(): PopupRouteBuilder(
          builder: (context) => const line_e_stitch_spacing.SpacePopup(),
          barrier: false,
        ),
        PopupEnum.lineEStitchThickness.toString(): PopupRouteBuilder(
          builder: (context) => const line_e_stitch_thickness.ThicknessPopup(),
          barrier: false,
        ),
        PopupEnum.lineEStitchFlip.toString(): PopupRouteBuilder(
          builder: (context) => const line_e_stitch_flip.FlipPopup(),
          barrier: false,
        ),
        PopupEnum.lineVStitchWidth.toString(): PopupRouteBuilder(
          builder: (context) => const line_v_stitch_width.WidthPopup(),
          barrier: false,
        ),
        PopupEnum.lineVStitchSpace.toString(): PopupRouteBuilder(
          builder: (context) => const line_v_stitch_spacing.SpacePopup(),
          barrier: false,
        ),
        PopupEnum.lineVStitchThickness.toString(): PopupRouteBuilder(
          builder: (context) => const line_v_stitch_thickness.ThicknessPopup(),
          barrier: false,
        ),
        PopupEnum.lineVStitchFlip.toString(): PopupRouteBuilder(
          builder: (context) => const line_v_stitch_flip.FlipPopup(),
          barrier: false,
        ),
        PopupEnum.lineMotifSize.toString(): PopupRouteBuilder(
          builder: (context) => const MotifSizePopup(),
          barrier: false,
        ),
        PopupEnum.lineMotifSpacing.toString(): PopupRouteBuilder(
          builder: (context) => const MotifSpacingPopup(),
          barrier: false,
        ),
        PopupEnum.lineMotifFlip.toString(): PopupRouteBuilder(
          builder: (context) => const MotifFlipPopup(),
          barrier: false,
        ),
        PopupEnum.lineRoughZigzagWidth.toString(): PopupRouteBuilder(
          builder: (context) =>
              const line_rough_zigzag_width.WidthSettingPopup(),
          barrier: false,
        ),
        PopupEnum.lineRoughZigzagDensity.toString(): PopupRouteBuilder(
          builder: (context) =>
              const line_rough_zigzag_density.DensitySettingPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceTatamiDirection.toString(): PopupRouteBuilder(
          builder: (context) => const DirectionSettingPopupPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceTatamiDensity.toString(): PopupRouteBuilder(
          builder: (context) =>
              const surface_tatami_density.DensitySettingPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceTatamiPullCompensation.toString(): PopupRouteBuilder(
          builder: (context) => const PullCompensationSettingPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceTatamiUnderSewing.toString(): PopupRouteBuilder(
          builder: (context) =>
              const surface_tatami_under_sewing.UnderSewingSettingPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceStippleRunPitch.toString(): PopupRouteBuilder(
          builder: (context) => const surface_stipple.RunPitchPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceStippleSpacing.toString(): PopupRouteBuilder(
          builder: (context) => const SpacingPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceStippleDistance.toString(): PopupRouteBuilder(
          builder: (context) => const DistancePopup(),
          barrier: false,
        ),
        PopupEnum.surfaceStippleStitch.toString(): PopupRouteBuilder(
          builder: (context) => const StitchPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceDecorativeFillSize.toString(): PopupRouteBuilder(
          builder: (context) => const SizeSettingPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceDecorativeFillDirection.toString(): PopupRouteBuilder(
          builder: (context) => const DirectionSettingPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceDecorativeFillOutline.toString(): PopupRouteBuilder(
          builder: (context) => const OutlineSettingPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceDecorativeFillRandomShift.toString():
            PopupRouteBuilder(
          builder: (context) => const RandomShiftSettingPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceDecorativeFillPositionOffset.toString():
            PopupRouteBuilder(
          builder: (context) => const PositionOffsetSettingPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceDecorativeFillThickness.toString(): PopupRouteBuilder(
          builder: (context) => const ThicknessSettingPopup(),
          barrier: false,
        ),
      };

  ///
  /// 子画面のView更新
  ///
  @override
  void updatePageByChild(ComponentType vm) {
    switch (vm) {
      case ComponentType.topBar:
        ref.read(topBarViewModelProvider.notifier).update();
        ref.read(viewAreaViewModelProvider.notifier).update();
        break;
      case ComponentType.toolbar:
        ref.read(bottomProvider.notifier).update();
        ref.read(viewAreaViewModelProvider.notifier).update();
        break;
      case ComponentType.bottom:
        ref.read(viewAreaViewModelProvider.notifier).update();
        ref.read(toolBarViewModelProvider.notifier).update();
        break;
      case ComponentType.viewArea:
        ref.read(topBarViewModelProvider.notifier).update();
        ref.read(toolBarViewModelProvider.notifier).update();
        ref.read(bottomProvider.notifier).update();
        break;
      case ComponentType.projector:
        ref.read(mdcProjectorFrameDragAreaViewModelProvider.notifier).update();
        ref.read(viewAreaViewModelProvider.notifier).update();
        ref.read(topBarViewModelProvider.notifier).update();
        ref.read(bottomProvider.notifier).update();

        /// 現在のページを更新して、
        /// プロジェクションが開いているときにツールバーを非表示にします
        state = state.copyWith(
          isDisplayToolBar:
              ProjectorModel().mdcProjector.isMdcProjectorViewOpen == false,
        );

        break;
      case ComponentType.cameraPenUI:
        ref.read(mdcCameraPenViewModelProvider.notifier).update();
        break;
      case ComponentType.topBar_ProjectorButton:
        ref.read(topBarViewModelProvider.notifier).updateProjectorButtonState();
        break;
      default:
        break;
    }
  }

  ///
  /// パスを閉じるポップアップ
  ///
  @override
  void maybeRemovePopupRoute(String route) {
    ref
        .readAutoNotifierIfExists(stitchPageViewInfoProvider)
        ?.navigator
        .maybeRemoveRoute(routeName: route);
  }

  ///
  /// 指定された名前のポップアップを開く
  ///
  @override
  void openPopupRoute(String route, Function update) {
    ref
        .readAutoNotifierIfExists(stitchPageViewInfoProvider)
        ?.navigator
        .pushNamed(nextRouteName: route)
        .then((value) {
      update();
    });
  }

  ///
  /// ポップアップが閉じた後にページを更新する
  ///
  @override
  void updateRelatedInStitchPage(Function update) {
    /// view更新
    update();
    updateStitchPageByChild();
    updatePageByChild(ComponentType.toolbar);
    updatePageByChild(ComponentType.topBar_ProjectorButton);
  }
}
