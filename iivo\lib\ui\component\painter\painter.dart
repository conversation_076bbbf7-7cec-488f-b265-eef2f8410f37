import 'dart:async';
import 'dart:ui';

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart' hide Image;

/// A very simple widget that supports drawing using touch.
class Painter extends StatefulWidget {
  /// Creates an instance of this widget that operates on top of the supplied [PainterController].
  Painter(this.painterController)
      : super(key: <PERSON><PERSON><PERSON><PainterController>(painterController));
  final PainterController painterController;

  @override
  PainterState createState() => PainterState();
}

class PainterState extends State<Painter> {
  final Set<int> _activePointers = {};
  @override
  void initState() {
    super.initState();
    widget.painterController._widgetFinish = _finish;
    widget.painterController._widgetSize = _size;
  }

  Size _size() {
    setState(() {});
    return context.size ?? const Size(0, 0);
  }

  Size _finish() {
    setState(() {});
    return context.size ?? const Size(0, 0);
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: _handlePointerDown,
      onPointerUp: _handlePointerUpOrCancel,
      onPointerCancel: _handlePointerUpOrCancel,
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: GestureDetector(
          onPanStart: _onPanStart,
          onPanUpdate: _onPanUpdate,
          onPanEnd: _onPanEnd,
          child: ClipRect(
            child: CustomPaint(
              willChange: true,
              painter: _PainterPainter(widget.painterController._pathHistory,
                  repaint: widget.painterController),
            ),
          ),
        ),
      ),
    );
  }

  void _onPanStart(DragStartDetails start) {
    Offset pos = (context.findRenderObject() as RenderBox)
        .globalToLocal(start.globalPosition);
    widget.painterController._pathHistory.add(pos);
    widget.painterController._notifyListeners();
  }

  void _onPanUpdate(DragUpdateDetails update) {
    Offset pos = (context.findRenderObject() as RenderBox)
        .globalToLocal(update.globalPosition);
    widget.painterController._pathHistory.updateCurrent(pos);
    widget.painterController._notifyListeners();
  }

  void _onPanEnd(DragEndDetails end) {
    widget.painterController._pathHistory.endCurrent();
    widget.painterController._notifyListeners();
  }

  void _handlePointerDown(PointerDownEvent event) {
    if (_activePointers.isEmpty) {
      /// 最初に画面に触れた指のときだけ音を鳴らします
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    } else {
      /// Do Nothing
    }
    _activePointers.add(event.pointer);
  }

  void _handlePointerUpOrCancel(PointerEvent event) {
    _activePointers.remove(event.pointer);
  }
}

class _PainterPainter extends CustomPainter {
  _PainterPainter(this._path, {Listenable? repaint}) : super(repaint: repaint);
  final PathHistory _path;

  @override
  void paint(Canvas canvas, Size size) {
    _path.draw(canvas, size);
  }

  @override
  bool shouldRepaint(_PainterPainter oldDelegate) {
    return true;
  }
}

class PathHistory {
  PathHistory()
      : _paths = <MapEntry<Path, Paint>>[],
        _inDrag = false,
        _backgroundPaint = Paint()..blendMode = BlendMode.dstOver,
        currentPaint = Paint()
          ..color = Colors.black
          ..strokeWidth = 1.0
          ..style = PaintingStyle.fill;
  List<MapEntry<Path, Paint>> _paths;
  Paint currentPaint;
  final Paint _backgroundPaint;
  bool _inDrag;

  bool get isEmpty => _paths.isEmpty || (_paths.length == 1 && _inDrag);

  void setBackgroundColor(Color backgroundColor) {
    _backgroundPaint.color = backgroundColor;
  }

  void undo() {
    if (!_inDrag) {
      _paths.removeLast();
    }
  }

  void clear() {
    if (!_inDrag) {
      _paths.clear();
    }
  }

  void add(Offset startPoint) {
    if (!_inDrag) {
      _inDrag = true;
      Path path = Path();
      path.moveTo(startPoint.dx, startPoint.dy);
      _paths.add(MapEntry<Path, Paint>(path, currentPaint));
    }
  }

  void updateCurrent(Offset nextPoint) {
    if (_inDrag) {
      Path path = _paths.last.key;
      path.lineTo(nextPoint.dx, nextPoint.dy);
    }
  }

  void endCurrent() {
    _inDrag = false;
  }

  void draw(Canvas canvas, Size size) {
    canvas.saveLayer(Offset.zero & size, Paint());
    for (MapEntry<Path, Paint> path in _paths) {
      Paint p = path.value;
      canvas.drawPath(path.key, p);
    }
    canvas.drawRect(
        Rect.fromLTWH(0.0, 0.0, size.width, size.height), _backgroundPaint);
    canvas.restore();
  }

  void setPathHistory(List<MapEntry<Path, Paint>> pathHistory) {
    _paths = pathHistory;
  }

  List<MapEntry<Path, Paint>> getPathHistory() {
    return _paths;
  }
}

/// Container that holds the size of a finished drawing and the drawed data as [Picture].
class PictureDetails {
  /// Creates an immutable instance with the given drawing information.
  const PictureDetails(this.picture, this.width, this.height);

  /// The drawings data as [Picture].
  final Picture picture;

  /// The width of the drawing.
  final int width;

  /// The height of the drawing.
  final int height;

  /// Converts the [picture] to an [Image].
  Future<Image> toImage({int? imageWidth, int? imageHeight}) {
    if (imageWidth != null && imageHeight != null) {
      return picture.toImage(imageWidth, imageHeight);
    } else {
      return picture.toImage(width, height);
    }
  }
}

/// Used with a [Painter] widget to control drawing.
class PainterController extends ChangeNotifier {
  /// Creates a new instance for the use in a [Painter] widget.
  PainterController() : _pathHistory = PathHistory();
  Color _drawColor = const Color.fromARGB(255, 0, 0, 0);
  Color _backgroundColor = const Color.fromARGB(255, 255, 255, 255);
  bool _eraseMode = false;

  double _thickness = 1.0;
  PictureDetails? _cached;
  final PathHistory _pathHistory;
  ValueGetter<Size>? _widgetFinish;
  ValueGetter<Size>? _widgetSize;

  /// Returns true if nothing has been drawn yet.
  bool get isEmpty => _pathHistory.isEmpty;

  /// Returns true if the the [PainterController] is currently in erase mode,
  /// false otherwise.
  bool get eraseMode => _eraseMode;

  /// If set to true, erase mode is enabled, until this is called again with
  /// false to disable erase mode.
  set eraseMode(bool enabled) {
    _eraseMode = enabled;
    updatePaint();
  }

  /// Retrieves the current draw color.
  Color get drawColor => _drawColor;

  /// Sets the draw color.
  set drawColor(Color color) {
    _drawColor = color;
    updatePaint();
  }

  /// Retrieves the current background color.
  Color get backgroundColor => _backgroundColor;

  /// Updates the background color.
  set backgroundColor(Color color) {
    _backgroundColor = color;
    updatePaint();
  }

  /// Returns the current thickness that is used for drawing.
  double get thickness => _thickness;

  PathHistory get pathHistory => _pathHistory;

  /// Sets the draw thickness..
  set thickness(double t) {
    _thickness = t;
    updatePaint();
  }

  void updatePaint() {
    Paint paint = Paint();
    if (_eraseMode) {
      paint.blendMode = BlendMode.clear;
      paint.color = const Color.fromARGB(0, 255, 0, 0);
    } else {
      paint.color = drawColor;
      paint.blendMode = BlendMode.srcOver;
    }
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = thickness;
    _pathHistory.currentPaint = paint;
    _pathHistory.setBackgroundColor(backgroundColor);
    notifyListeners();
  }

  /// Undoes the last drawing action (but not a background color change).
  /// If the picture is already finished, this is a no-op and does nothing.
  void undo() {
    if (!isFinished()) {
      _pathHistory.undo();
      notifyListeners();
    }
  }

  void _notifyListeners() {
    notifyListeners();
  }

  /// Deletes all drawing actions, but does not affect the background.
  /// If the picture is already finished, this is a no-op and does nothing.
  void clear() {
    if (!isFinished()) {
      _pathHistory.clear();
      notifyListeners();
    }
  }

  /// Finishes drawing and returns the rendered [PictureDetails] of the drawing.
  /// The drawing is cached and on subsequent calls to this method, the cached
  /// drawing is returned.
  ///
  /// This might throw a [StateError] if this PainterController is not attached
  /// to a widget, or the associated widget's [Size.isEmpty].
  PictureDetails getPicture() {
    if (_widgetFinish != null) {
      _cached = _render(_widgetSize!());
    } else {
      throw StateError(
          'Called finish on a PainterController that was not connected to a widget yet!');
    }
    return _cached!;
  }

  PictureDetails _render(Size size) {
    if (size.isEmpty) {
      throw StateError('Tried to render a picture with an invalid size!');
    } else {
      PictureRecorder recorder = PictureRecorder();
      Canvas canvas = Canvas(recorder);
      _pathHistory.draw(canvas, size);
      return PictureDetails(
          recorder.endRecording(), size.width.floor(), size.height.floor());
    }
  }

  /// Returns true if this drawing is finished.
  ///
  /// Trying to modify a finished drawing is a no-op.
  bool isFinished() {
    return _cached != null;
  }
}
