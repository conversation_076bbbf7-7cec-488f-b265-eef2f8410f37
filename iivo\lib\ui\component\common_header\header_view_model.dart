import 'package:audio_player/audio_player_interface.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../model/machine_config_model.dart';
import '../../app/emb/model/emb_reset_model.dart';
import '../../app/emb/model/photo_stitch/photo_stitch_model.dart';
import '../../app/home/<USER>/home_model.dart';
import '../../app/mdc/model/mdc_reset_model.dart';
import '../../app/utl/model/preview_model.dart';
import '../../app/utl/model/utl_reset_model.dart';
import '../../global_popup/global_popup_route.dart';
import '../../global_popup/global_popup_route_enum.dart';
import '../../global_popup/global_popups/err_mdc_home_b/err_mdc_home_b_view_model.dart';
import '../../global_popup/global_popups/err_mdc_home_clear_all_editing_data_and_move/err_mdc_home_clear_all_editing_data_and_move_view_model.dart';
import '../../global_popup/global_popups/err_mdc_home_t/err_mdc_home_t_view_model.dart';
import '../../global_popup/global_popups/err_photo_stitch_exit/err_photo_stitch_exit_view_model.dart';
import '../../global_popup/global_popups/err_selected_pattern_cancel_ok/err_selected_pattern_cancel_ok_view_model.dart';
import '../../global_popup/global_popups/err_selected_stitch_cancel_ok/err_selected_stitch_cancel_ok_view_model.dart';
import '../../page_route/page_route.dart';

///
/// Home を確認すると lib にエラーが表示される（のみteaching and setting）
///
mixin HeaderViewModel {
  void checkGotoHome() {
    /// Home を確認すると lib にエラーが表示される
    final deviceError = TpdLibrary().apiBinding.checkGotoHome();
    if (deviceError == DirErrorCode.dirInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
        deviceError == DirErrorCode.dirInvalidPanelError) {
      return;
    }

    if (deviceError == DirErrorCode.dirRequiresConfirmation) {
      /// UIにエラーが表示されるかどうかを確認します

      switch (MachineConfigModel().baseMode) {
        case SettingBaseMode.utl:
          GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum.ERR_SELECTED_STITCH_CANCEL_OK,
              arguments: ErrSelectedStitchCancelOkArgument(
                onOKButtonClicked: (_) {
                  if (GlobalPopupRoute().resetErrorState() !=
                      DeviceErrorCode.devNoError) {
                    return;
                  }

                  /// LEDPointerを閉じます
                  DeviceLibrary().apiBinding.closeLedPtSetting();
                  PreviewDataModel().setThreadColorsIndex(0);

                  utlGoToHome();

                  /// Homeへ
                  HomeModel.geToHome();

                  MachineConfigModel().baseMode = SettingBaseMode.home;
                  PagesRoute().popUntil(nextRoute: PageRouteEnum.home);
                },
                onCancelButtonClicked: (_) =>
                    GlobalPopupRoute().resetErrorState(),
              ));
          return;

        case SettingBaseMode.emb:
          GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum.ERR_SELECTED_PATTERN_CANCEL_OK,
              arguments: ErrSelectedPatternCancelOkArgument(
                  onOKButtonClicked: (_) {
                    if (GlobalPopupRoute().resetErrorState() !=
                        DeviceErrorCode.devNoError) {
                      return;
                    }

                    /// LEDPointerを閉じます
                    DeviceLibrary().apiBinding.closeLedPtSetting();

                    embGoToHome();

                    /// Homeへ
                    HomeModel.geToHome();

                    MachineConfigModel().baseMode = SettingBaseMode.home;

                    /// 刺繍写真から退出後の動作-臨時対策
                    PhotoStitchModel().actionAfterExitingPhotoStitch();
                    PagesRoute().popUntil(nextRoute: PageRouteEnum.home);
                  },
                  onCancelButtonClicked: (_) =>
                      GlobalPopupRoute().resetErrorState()));
          return;
        case SettingBaseMode.mdc:

          /// UIにエラーが表示されるかどうかを確認します
          if (embHasData() == true) {
            GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum
                  .ERR_MDC_HOME_CLEAR_ALL_EDITING_DATA_AND_MOVE,
              arguments: ErrMdcHomeClearAllEditingDataAndMoveArgument(
                onOKButtonClicked: (_) {
                  if (GlobalPopupRoute().resetErrorState() !=
                      DeviceErrorCode.devNoError) {
                    return;
                  }

                  /// LEDPointerを閉じます
                  DeviceLibrary().apiBinding.closeLedPtSetting();
                  embGoToHome();
                  mdcGoToHome();

                  /// Homeへ
                  HomeModel.geToHome();

                  MachineConfigModel().baseMode = SettingBaseMode.home;
                  PagesRoute().popUntil(nextRoute: PageRouteEnum.home);
                },
                onCancelButtonClicked: (_) =>
                    GlobalPopupRoute().resetErrorState(),
              ),
            );
            return;
          }

          _handleMdcHasDateError();
          return;
        case SettingBaseMode.home:
          // No-op
          break;
        default:
          break;
      }
    }

    if (deviceError == DirErrorCode.dirTransitionOK) {
      switch (MachineConfigModel().baseMode) {
        case SettingBaseMode.utl:
          utlGoToHome();

          break;
        case SettingBaseMode.emb:

          /// photoStitch画面がHome画面に戻ります
          if (PhotoStitchModel().isInPhotoStitchEdit) {
            /// UIにエラーが表示されるかどうかを確認します
            GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum.errPhotoStitchExit,
              arguments: ErrPhotoStitchExitArgument(
                onOKButtonClicked: (_) {
                  DeviceErrorCode deviceError =
                      GlobalPopupRoute().resetErrorState();
                  if (deviceError != DeviceErrorCode.devNoError) {
                    return;
                  }

                  embGoToHome();

                  /// Homeへ
                  HomeModel.geToHome();

                  PhotoStitchModel().isInPhotoStitchEdit = false;
                  MachineConfigModel().baseMode = SettingBaseMode.home;

                  /// 刺繍写真から退出後の動作-臨時対策
                  PhotoStitchModel().actionAfterExitingPhotoStitch();
                  PagesRoute().popUntil(nextRoute: PageRouteEnum.home);
                },
                onCancelButtonClicked: (_) =>
                    GlobalPopupRoute().resetErrorState(),
              ),
            );
            return;
          }
          embGoToHome();

          break;
        case SettingBaseMode.mdc:
          if (mdcHasData() == true) {
            _handleMdcHasDateError();
            return;
          }
          embGoToHome();
          mdcGoToHome();

          break;
        default:
          break;
      }

      /// Homeへ
      HomeModel.geToHome();
      SystemSoundPlayer().play(SystemSoundEnum.change);
      MachineConfigModel().baseMode = SettingBaseMode.home;

      /// 刺繍写真から退出後の動作-臨時対策
      PhotoStitchModel().actionAfterExitingPhotoStitch();
      PagesRoute().popUntil(nextRoute: PageRouteEnum.home);
    } else {
      Log.e(tag: "checkGotoHome", description: "deviceError : $deviceError");
      return;
    }
  }

  ///
  /// HOMEをクリックした場合、MDCデータが存在する場合のerrorを処理します。
  ///
  void _handleMdcHasDateError() {
    bool isBrother =
        MachineConfigModel().getCurrentModel() == AppModelEnum.brother;
    GlobalPopupRoute().updateErrorState(
      nextRoute: isBrother
          ? GlobalPopupRouteEnum.ERR_MDC_HOME_B
          : GlobalPopupRouteEnum.ERR_MDC_HOME_T,
      arguments: isBrother
          ? ErrMdcHomeBArgument(
              onOKButtonClicked: (_) => _errMdcHomeArgumentOKButtonClicked(),
              onCancelButtonClicked: (_) =>
                  GlobalPopupRoute().resetErrorState(),
            )
          : ErrMdcHomeTArgument(
              onOKButtonClicked: (_) => _errMdcHomeArgumentOKButtonClicked(),
              onCancelButtonClicked: (_) =>
                  GlobalPopupRoute().resetErrorState(),
            ),
    );
  }

  ///
  /// MDCデータをクリアしてホームのerrorウィンドウに戻るか否かのOKクリックイベントです。
  ///
  void _errMdcHomeArgumentOKButtonClicked() {
    if (GlobalPopupRoute().resetErrorState() != DeviceErrorCode.devNoError) {
      return;
    }

    /// LEDPointerを閉じます
    DeviceLibrary().apiBinding.closeLedPtSetting();
    mdcGoToHome();
    embGoToHome();

    /// Homeへ
    HomeModel.geToHome();

    MachineConfigModel().baseMode = SettingBaseMode.home;
    PagesRoute().popUntil(nextRoute: PageRouteEnum.home);
  }
}
