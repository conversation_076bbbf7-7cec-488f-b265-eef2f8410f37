import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../global_popup/global_popups/err_emb_select_pattern_rotate90_f/err_emb_select_pattern_rotate90_f_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_emb_too_much_selected_go_menu/err_emb_too_much_selected_go_menu_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_no_more_select_for_emb/err_no_more_select_for_emb_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_outside_of_emb_frm_noadd/err_outside_of_emb_frm_noadd_view_model.dart';
import '../../../../../model/key_board_font_model.dart';
import '../../../../../model/pattern_data_reader/character_font_select_reader.dart';
import '../text_editing_page_view_model.dart';
import 'character_type_interface.dart';

final characterTypeViewModelProvider = StateNotifierProvider.autoDispose<
    CharacterTypeViewModel,
    CharacterTypeState>((ref) => CharacterTypeViewModel(ref));

class CharacterTypeViewModel extends CharacterTypeStateInterface {
  CharacterTypeViewModel(Ref ref) : super(const CharacterTypeState(), ref) {
    update();
  }
  @override
  void update() {
    state = state.copyWith(
      categoryState: _getKeyboardTabState(),
      deleteButtonState: _getDeleteButtonState(),
      changeLineState: _getCRButtonState(),
      keyboardButtonState: _getKeyboardAllCharacterButtonState(),
    );
  }

  ///
  /// 文字タイプの取得
  ///
  List<FontSelectCategory> getFontSelectCategory() =>
      CharacterFontSelectReader().getAllCharacterFontSelectsInfo().category;

  @override
  void onCategoryClick(int index) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    _selectCategory = index;
    update();
  }

  ///
  /// すべての文字を取得
  ///
  @override
  List<Character> getCharacter() =>
      getFontSelectCategory()[_selectCategory].character;

  ///
  /// カーソル位置に応じて対応する文字を挿入します
  ///
  @override
  void inputText(int index) {
    final EmbCharLibraryError error =
        KeyBoardFontModel().inputCharacter(getCharacter()[index].unicode);

    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      _showCharAddErrorPop(error);
      return;
    }

    if (error == EmbCharLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }
    update();

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.characterType);
  }

  ///
  ///  カーソル位置に基づいて対応する文字を削除します
  ///
  @override
  void deleteText() {
    /// 状態が無効に設定されている場合、削除は実行されません。
    if (state.deleteButtonState == ButtonState.disable) {
      return;
    }

    KeyBoardFontModel().deleteCharacter();

    update();

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.characterType);
  }

  ///
  /// 改行キー
  ///
  @override
  void lineChangeClick() {
    if (KeyBoardFontModel().isStartOfLine() == true) {
      return;
    }

    if (KeyBoardFontModel().isEndOfLine() == false &&
        KeyBoardFontModel().isOnNotStraightLine()) {
      return;
    }

    /// 最終行に文字データがあるかどうかを判断する
    if (KeyBoardFontModel().getCurrentLineData().charList.isEmpty) {
      return;
    }

    KeyBoardFontModel().inputNewLine();

    update();

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.characterType);
  }

  @override
  void blankClick() {
    final EmbCharLibraryError error =
        KeyBoardFontModel().inputCharacter(KeyBoardFontModel.blank);

    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      _showCharAddErrorPop(error);
      return;
    }

    update();

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.characterType);
  }

  ///
  /// フォント選択カテゴリの状態
  ///
  List<ButtonState> _getKeyboardTabState() {
    List<ButtonState> tabState = [];
    int index = 0;
    for (var element in KeyBoardFontModel().getTabEnableList()) {
      if (element == false) {
        tabState.add(ButtonState.disable);
      } else if (index == _selectCategory) {
        tabState.add(ButtonState.select);
      } else {
        tabState.add(ButtonState.normal);
      }
      index++;
    }

    return tabState;
  }

  ///
  /// タブの下にあるすべてのボタンのステータス
  ///
  List<ButtonState> _getKeyboardAllCharacterButtonState() {
    List<bool> enableList =
        KeyBoardFontModel().getCharactersEnableList(_selectCategory);
    List<ButtonState> buttonStateList = enableList
        .map((enable) => enable ? ButtonState.normal : ButtonState.disable)
        .toList();
    return buttonStateList;
  }

  ///
  /// 改行キーのステータスを取得する
  ///
  ButtonState _getCRButtonState() {
    /// 再編集時の Enter キーは無効です
    if (KeyBoardFontModel().isReEditMode == true) {
      return ButtonState.disable;
    }

    final int numberOfLines = KeyBoardFontModel().getAllText().length;
    final int lineIndex = KeyBoardFontModel().getLineIndex();

    /// 何もテキストスタイルが追加されていません
    if (numberOfLines <= 0) {
      return ButtonState.disable;
    }

    ///
    /// 改行を押すときに、文字が入力されていない場合、改行はグレー表示されます。
    /// 現在のカーソルが通常の文字を指しており、次の改行が改行の場合、改行もグレー表示され、クリックできません
    ///
    /// 現在のカーソルは所在行の末尾にあり、すべての文字形式の末尾ではありません。
    if (KeyBoardFontModel().isEndOfLine() == true &&
        lineIndex < numberOfLines - 1) {
      return ButtonState.disable;
    }

    /// 現在カーソルは行の先頭にあります、または現在の行に文字がありません
    if (KeyBoardFontModel().isStartOfLine() == true ||
        KeyBoardFontModel().getCurrentLineData().charList.isEmpty) {
      return ButtonState.disable;
    }
    return ButtonState.normal;
  }

  ///
  /// 削除されたキーの状態を取得する
  ///
  ButtonState _getDeleteButtonState() {
    /// 再編集時の削除キーは、文字数が 2 文字未満の場合、効果がありません
    if (KeyBoardFontModel().isReEditMode == true &&
        KeyBoardFontModel().getTextBuffer().length <= 1) {
      return ButtonState.disable;
    }

    if (KeyBoardFontModel().isStartOfLine() &&
        KeyBoardFontModel().isPrevLineNotStraight()) {
      return ButtonState.disable;
    }

    if (KeyBoardFontModel().getTextBuffer().isEmpty ||
        KeyBoardFontModel().isBeginPosition()) {
      return ButtonState.disable;
    } else {
      return ButtonState.normal;
    }
  }

  ///
  ///  文字を追加できない場合のエラーメッセージ
  ///
  void _showCharAddErrorPop(EmbCharLibraryError error) {
    if (error == EmbCharLibraryError.EMB_DATAOVER_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
          arguments: ErrEmbTooMuchSelectedGoMenuArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
      return;
    }

    /// 文字パターンの高さが大きすぎる場合のエラー
    if (error == EmbCharLibraryError.EMB_AREA_OVER_ERR) {
      /// ERR_OUTSIDE_OF_EMB_FRM_NOADDのokボタン関数
      errOutsideOfEmbFrmNoaddFunc = GlobalPopupRoute().resetErrorState;

      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_OUTSIDE_OF_EMB_FRM_NOADD);
      return;
    }

    /// エリアオーバー
    if (error == EmbCharLibraryError.EMB_FRAMESIZE_ERR ||
        error == EmbCharLibraryError.EMB_CHAR_NO_MORE_IPT_ERR) {
      /// ERR_NO_MORE_SELECT_FOR_EMBのokボタン関数
      errNoMoreSelectForEmbFunc = () => GlobalPopupRoute().resetErrorState();

      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_NO_MORE_SELECT_FOR_EMB);
      return;
    }

    /// 文字パターンの幅が大きすぎる場合のエラー
    if (KeyBoardFontModel().getCurrentLineArrayType() == ArrayTypes.flat &&
        error == EmbCharLibraryError.EMB_CHAR_ROTATE90_ERR) {
      /// ERR_EMB_SELECT_PATTERN_ROTATE90_Fのreturnボタン関数
      errEmbSelectPatternRotate90FReturnFunc = () {
        final DeviceErrorCode deviceError =
            GlobalPopupRoute().resetErrorState();

        if (deviceError != DeviceErrorCode.devNoError) {
          return;
        }
        KeyBoardFontModel().deleteCharacter();

        /// 他の画面を更新する
        ref
            .read(textEditingPageViewModelProvider.notifier)
            .updateTextEditingPageByChild(TextEditingType.characterType);
      };

      /// ERR_EMB_SELECT_PATTERN_ROTATE90_FのROTATE90ボタン関数
      errEmbSelectPatternRotate90FRotate90Func = () {
        final DeviceErrorCode deviceError =
            GlobalPopupRoute().resetErrorState();

        if (deviceError != DeviceErrorCode.devNoError) {
          return;
        }
        KeyBoardFontModel().charRotate90();

        /// 他の画面を更新する
        ref
            .read(textEditingPageViewModelProvider.notifier)
            .updateTextEditingPageByChild(TextEditingType.characterType);
      };

      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_EMB_SELECT_PATTERN_ROTATE90_F);
      return;
    }
  }

  ///
  /// 最初にカテゴリを選択する
  ///
  int _selectCategory = 0;
}
