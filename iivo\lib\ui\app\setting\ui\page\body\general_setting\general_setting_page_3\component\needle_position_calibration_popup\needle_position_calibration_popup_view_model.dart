import 'dart:async';
import 'dart:ffi';
import 'dart:typed_data';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/devedit_bindings_generated.dart';

import '../../../../../../../../../../model/projector_model.dart';
import 'needle_position_calibration_popup_state.dart';

final needlePositionCalibrationPopupViewModelProvider =
    StateNotifierProvider.autoDispose<NeedlePositionCalibrationPopupViewModel,
            NeedlePositionCalibrationPopupState>(
        (ref) => NeedlePositionCalibrationPopupViewModel());

class NeedlePositionCalibrationPopupViewModel
    extends ViewModel<NeedlePositionCalibrationPopupState> {
  NeedlePositionCalibrationPopupViewModel()
      : super(
          NeedlePositionCalibrationPopupState(
            needlePositionCalibrationResultImage: Uint8List.fromList([]),
            needlePositionCalibrationPageState:
                NeedlePositionCalibrationPageState.start,
          ),
        ) {
    ///プロジェクト
    ProjectorModel().needlePositionCalibrationProjector.openProjector();
  }

  ///
  /// スタート画面はスタートボタンのハンドラー関数をクリックします
  ///
  void onStartPageStartButtonClicked() {
    final error = DeviceLibrary().apiBinding.doCalibrationAdjustment();
    if (error == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// panelError確認
    final panelErrorCode = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    if (error == DeviceErrorCode.devInvalidPanelError ||
        panelErrorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    if (error != DeviceErrorCode.devNoError) {
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// NDPMessagePolling起動
    _startCameraNDPMessagePolling();

    /// 画面更新
    state = state.copyWith(
      needlePositionCalibrationResultImage: Uint8List.fromList([]),
      needlePositionCalibrationPageState:
          NeedlePositionCalibrationPageState.process,
    );
  }

  ///
  /// スタート画面はCancelボタンのハンドラー関数をクリックします
  ///
  void onStartPageCancelButtonClicked(BuildContext context) {
    final error = DeviceLibrary().apiBinding.cancelCalibrationAdjustment();
    if (error == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    if (error != DeviceErrorCode.devNoError) {
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 画面閉じる、前に戻る
    _closeSelf(context);
  }

  ///
  /// 処理中画面はRetryボタンのハンドラー関数をクリックします
  ///
  void onProcessingPageRetryButtonClicked() {
    /// polling閉じる、閉じる失敗なら、何もしない
    final result = _closeCameraNDPMessagePolling();
    if (result == false) {
      return;
    }

    final error = DeviceLibrary().apiBinding.retryCalibrationAdjustment();
    if (error == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// panelError確認
    final panelErrorCode = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    if (error == DeviceErrorCode.devInvalidPanelError ||
        panelErrorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    if (error != DeviceErrorCode.devNoError) {
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 画面更新
    state = state.copyWith(
      needlePositionCalibrationPageState:
          NeedlePositionCalibrationPageState.start,
    );
  }

  ///
  /// 処理中画面はCancelボタンのハンドラー関数をクリックします
  ///
  void onProcessingPageCancelButtonClicked(BuildContext context) {
    /// polling閉じる、閉じる失敗なら、何もしない
    final result = _closeCameraNDPMessagePolling();
    if (result == false) {
      return;
    }

    final error = DeviceLibrary().apiBinding.cancelCalibrationAdjustment();
    if (error == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    if (error != DeviceErrorCode.devNoError) {
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 画面閉じる、前に戻る
    _closeSelf(context);
  }

  ///
  /// 完了画面はRetryボタンのハンドラー関数をクリックします
  ///
  void onFinishedPageRetryButtonClicked() {
    final error = DeviceLibrary().apiBinding.retryCalibrationAdjustment();
    if (error == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// panelError確認
    final panelErrorCode = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    if (error == DeviceErrorCode.devInvalidPanelError ||
        panelErrorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    if (error != DeviceErrorCode.devNoError) {
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    state = state.copyWith(
      needlePositionCalibrationPageState:
          NeedlePositionCalibrationPageState.start,
    );
  }

  ///
  /// 完了画面はOKボタンのハンドラー関数をクリックします
  ///
  void onFinishedPageOkButtonClicked(BuildContext context) {
    final error = DeviceLibrary().apiBinding.doneCalibrationAdjustment();
    if (error != DeviceErrorCode.devNoError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 画面閉じる、前に戻る
    _closeSelf(context);
  }

  ///
  /// 完了画面はCancelボタンのハンドラー関数をクリックします
  ///
  void onFinishedPageCancelButtonClicked(BuildContext context) {
    final error = DeviceLibrary().apiBinding.cancelCalibrationAdjustment();
    if (error == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    if (error != DeviceErrorCode.devNoError) {
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 画面閉じる、前に戻る
    _closeSelf(context);
  }

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();

    /// プロジェクト閉じる
    ProjectorModel().needlePositionCalibrationProjector.closeProjector();
  }

  Timer? _timer;

  ///
  /// polling 開始
  ///
  void _startCameraNDPMessagePolling() {
    bool lockPolling = false;

    /// polling timer起動
    _timer = Timer.periodic(const Duration(milliseconds: 100), (_) async {
      if (_timer == null) {
        return;
      }

      if (mounted == false) {
        Log.e(tag: _debugLogTag, description: "Camera NDP Message Error[0]");
        return;
      }
      if (lockPolling == true) {
        return;
      }

      lockPolling = true;
      final CameraNDPMessage result =
          DeviceLibrary().apiBinding.getCameraNDPMessage();

      switch (result) {
        case CameraNDPMessage.DIRTY_CNDP_EM:
        case CameraNDPMessage.NG_CNDP_EM:
          _closeCameraNDPMessagePolling();
          _finishNeedlePositionCalibrationCallback(false);
          break;
        case CameraNDPMessage.OK_CNDP_EM:
          _closeCameraNDPMessagePolling();
          ProjectorLibrary().apiBinding.reloadProjectorCalibrationParameter(
              ProjectorCalibrationDataKind_t
                  .projectorCalibrationDataKind_Eeprom,
              nullptr,
              ProjectorFabricHeightKind_t.projectorFabricHeightKind_Reuse,
              0);
          _finishNeedlePositionCalibrationCallback(true);
          break;
        case CameraNDPMessage.WAITING_PRO_START:

          /// 起動待ちます
          if (ProjectorModel()
                  .needlePositionCalibrationProjector
                  .isProjectorOpened() ==
              true) {
            /// 投影画像更新
            await ProjectorModel()
                .needlePositionCalibrationProjector
                .refreshProjectorUserCalibrationImage();

            if (_timer == null || mounted == false) {
              Log.byeBye("timer or view was disposed");
              lockPolling = false;
              return;
            }

            ///  状態変更
            DeviceLibrary().apiBinding.startCalibrationProjector();
          } else {
            /// Do Nothing
          }
          break;
        case CameraNDPEMessage_t.PROCESSING_CNDP_EM:
          await getCameraImageAndRefreshUI();
          break;
        case CameraNDPEMessage_t.NO_CNDP_EM:
        case CameraNDPEMessage_t.NUP_CNDP_EM:
          break;
      }
      lockPolling = false;
    });
  }

  ///
  /// polling 閉じる
  ///
  bool _closeCameraNDPMessagePolling() {
    _timer?.cancel();
    _timer = null;
    ProjectorModel()
        .needlePositionCalibrationProjector
        .setProjectorBackLightOff();

    /// プロジェクト更新中閉じる不可
    final CameraNDPMessage result =
        DeviceLibrary().apiBinding.getCameraNDPMessage();
    if (result == CameraNDPMessage.WAITING_PRO_START) {
      return false;
    }

    return true;
  }

  ///
  /// コールバック関数の処理が終了しました
  ///
  void _finishNeedlePositionCalibrationCallback(bool status) {
    state = state.copyWith(
      needlePositionCalibrationPageState:
          NeedlePositionCalibrationPageState.finished,
      needlePositionCalibrationResultStatus: status,
    );
  }

  /// カメラ画像取得と更新します
  Future<void> getCameraImageAndRefreshUI() async {
    final cameraState = CameraProjectLibrary().apiBinding.cameraGetState();
    if (cameraState != cameraState_Ready) {
      return;
    }

    final ret = await CameraProjectLibrary().apiBinding.cameraGetImageAsync();
    if (ret.error != 0 || ret.camImageInfo.imageData.isEmpty) {
      /// Do Nothing
    } else {
      state = state.copyWith(
          needlePositionCalibrationResultImage: ret.camImageInfo.imageData);
    }
  }

  ///
  /// 自分を閉じる
  ///
  void _closeSelf(BuildContext context) {
    PopupNavigator.pop(context: context);
  }
}

const String _debugLogTag = "NeedlePositionCalibration";
