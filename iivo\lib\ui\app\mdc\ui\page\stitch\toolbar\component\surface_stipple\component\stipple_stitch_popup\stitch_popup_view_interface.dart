import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'stitch_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

///
/// 黒い背景に白いテキスト
///
const TextStyle blackTextWhiteBackground =
    TextStyle(color: Colors.white, backgroundColor: Colors.black);

///
/// 白い背景に黒いテキスト
///
const TextStyle whiteTextBlackBackground = TextStyle(
    color: Color.fromARGB(255, 51, 51, 51),
    backgroundColor: Colors.transparent);

@freezed
class StitchPopupState with _$StitchPopupState {
  const factory StitchPopupState({
    required bool isSelectSingle,
    required bool isSelectTriple,
  }) = _StitchPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class StitchPopupStateViewInterface
    extends ViewModel<StitchPopupState> {
  StitchPopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// ボタンをクリックしてリスニング
  ///
  void onSingleButtonClick();

  ///
  ///  ボタンをクリックしてリスニング
  ///
  void onTripleButtonClick();

  ///
  /// チェーンステッチのデフォルト値
  ///
  StichLine get defaultValue;
}
