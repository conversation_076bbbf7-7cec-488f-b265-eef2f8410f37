import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../memory/memory.dart';
import '../../../../network/upgrade/upgrade.dart';
import '../../../app/home/<USER>/home_model.dart';
import '../../../app/setting/ui/page/setting_page_view_model.dart';
import '../../global_popup_route.dart';
import '../../global_popup_route_enum.dart';
import '../err_processing_failed/err_processing_failed_view_model.dart';
import 'err_download_message_sound_popup_view_interface.dart';

///
/// デフォルトの開始進行状況は 0 です
/// 終了進行状況は 20 です
///
const int _defaultProgress = 0;
const int _finishProgress = 20;

class DownloadMessageSoundPageArgument {
  const DownloadMessageSoundPageArgument({
    this.openProcessingCompletePopup,
  });

  ///
  /// ページがダウンロードされ、解凍されたときのコールバック関数
  ///
  final void Function(BuildContext context)? openProcessingCompletePopup;
}

final errDownloadMessageSoundPageViewModelProvider =
    StateNotifierProvider.family.autoDispose<
            DownloadMessageSoundPageViewInterface,
            ErrDownloadMessageSoundPopupState,
            BuildContext>(
        (ref, context) => DownloadMessageSoundPageViewModel(ref, context));

class DownloadMessageSoundPageViewModel
    extends DownloadMessageSoundPageViewInterface {
  DownloadMessageSoundPageViewModel(Ref ref, this._context)
      : _argument = GlobalPopupRoute().getArgument(context: _context),
        super(
          ErrDownloadMessageSoundPopupState(
            progressValue: _defaultProgress,
            isStartUnZip: false,
            text: AppLocalizations.of(_context)!.icon_status_b2,
            tipText: AppLocalizations.of(_context)!.t_err_not_turnoff,
          ),
          ref,
          _context,
        ) {
    /// 初期化中にDownLoadに入る際、メカキーの状態はLibによって制御されます。
    if (HomeModel().isHomeInitCompleted == true) {
      /// メカキーの状態を設定する
      TpdLibrary()
          .apiBinding
          .setMatrixEnableList(MachineKeyState.machineKeyEnableAllNG);
    }
    _startDownloading();
  }

  final DownloadMessageSoundPageArgument? _argument;
  final BuildContext _context;

  ///
  /// ダウンロードをキャンセルする
  ///
  CancelToken _cancelToken = CancelToken();

  ///
  /// 音声のダウンロードを開始します
  ///
  Future<void> _startDownloading() async {
    if (Upgrade().prepareDownload(downloadVoiceOnly: true) == false) {
      _downLoadFailed();
      return;
    }
    bool isDownloadSuccess = false;
    try {
      isDownloadSuccess = await Upgrade().downloadVoice(
        _cancelToken,
        _onReceiveProgress,
      );
    } catch (e) {
      isDownloadSuccess = false;
      if (isMemoryFullException(e)) {
        // ストレージ容量不足によりダウンロードに失敗した場合、
        // ダウンロード済みの全てのキャッシュファイルを削除する
        Upgrade().deleteVoiceDownloadSaveFolder();
      } else {
        // Do nothing
      }
    }
    if (isDownloadSuccess) {
      /// これから先は一時停止は許されません
      state = state.copyWith(isStartUnZip: true);
      if (await Upgrade().updateVoice(true)) {
        _downLoadSuccess();
      } else {
        _downLoadFailed();
      }
    } else {
      _downLoadFailed();
    }
  }

  ///
  /// 「キャンセル」ボタンをクリックします。
  ///
  @override
  void onCancelButtonClicked() {
    /// ダウンロードの停止
    _cancelToken.cancel("cancelled");
    _cancelToken = CancelToken();

    /// 初期化中にDownLoadに入る際、メカキーの状態はLibによって制御されます。
    if (HomeModel().isHomeInitCompleted == true) {
      /// メカキーの状態を設定する
      TpdLibrary()
          .apiBinding
          .setMatrixEnableList(MachineKeyState.machineKeyEnableAll);
    }

    /// 割り込みを処理するポップアップを開きます
    GlobalPopupRoute().pushReplacement(
      nextRoute: GlobalPopupRouteEnum.errProcessingInterruptions,
    );
  }

  ///
  /// ダウンロードまたは抽出に失敗した場合は、ページを終了します
  ///
  void _downLoadFailed() {
    /// 他の画面更新
    ref.read(settingPageViewModelProvider.notifier).resetSettingPage();

    /// 初期化中にDownLoadに入る際、メカキーの状態はLibによって制御されます。
    if (HomeModel().isHomeInitCompleted == true) {
      /// メカキーの状態を設定する
      TpdLibrary()
          .apiBinding
          .setMatrixEnableList(MachineKeyState.machineKeyEnableAll);
    }

    /// 画面遷移
    GlobalPopupRoute().pushReplacement(
      nextRoute: GlobalPopupRouteEnum.errProcessingFailed,
      arguments: ProcessingFailedArgument(
        onOkButtonClicked: (_) {
          /// ダウンロードの停止
          _cancelToken.cancel("cancelled");
          _cancelToken = CancelToken();
          GlobalPopupRoute().pop();
        },
      ),
    );
  }

  ///
  /// ダウンロードと抽出が成功したら、ページを終了します
  ///
  void _downLoadSuccess() {
    if (!mounted) {
      return;
    }

    /// 初期化中にDownLoadに入る際、メカキーの状態はLibによって制御されます。
    if (HomeModel().isHomeInitCompleted == true) {
      /// メカキーの状態を設定する
      TpdLibrary()
          .apiBinding
          .setMatrixEnableList(MachineKeyState.machineKeyEnableAll);
    }

    /// 画面遷移
    /// 自分を閉じる
    GlobalPopupRoute().pop();

    /// オーディオプレパレーションを開いたポップアップを開きます
    _argument?.openProcessingCompletePopup?.call(_context);
  }

  ///
  /// ダウンロードプロセス中に、プログレスバーを更新します
  ///
  void _onReceiveProgress(double fraction) {
    if (!mounted) {
      return;
    }

    /// View更新
    state = state.copyWith(progressValue: (fraction * _finishProgress).toInt());
  }
}
