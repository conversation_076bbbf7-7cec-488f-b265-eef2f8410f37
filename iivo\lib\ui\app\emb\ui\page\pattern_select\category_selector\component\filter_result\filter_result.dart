import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../component/emb_footer/emb_footer.dart';
import '../../../../../component/emb_header/emb_header.dart';
import '../../../../common_component/zoom_selector_popup.dart';
import '../preview/preview.dart';
import 'filter_result_view_model.dart';

///
/// 刺しゅうパターン選択画面
///
class FilterResult extends StatefulPage {
  const FilterResult({super.key});
  @override
  PageState<FilterResult> createState() => _FilterResultState();
}

class _FilterResultState extends PageState<FilterResult> {
  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final state = ref.watch(filterResultViewModelProvider);
    final viewModel = ref.read(filterResultViewModelProvider.notifier);

    return Scaffold(
      body: Stack(
        children: [
          MultiTouchBlocker(
              child: Column(
            children: [
              const Expanded(
                flex: 71,
                child: EmbHeader(),
              ),
              Expanded(
                flex: 1148,
                child: pre_base_embroidery(
                  child: Column(
                    children: [
                      const Spacer(flex: 13),
                      Expanded(
                        flex: 63,
                        child: pre_category_header(
                          zoomIcon: viewModel.getSelectedZoomIcon(),
                          filterButtonInfo: state.isFilterApplied
                              ? l10n.icon_filterapplied
                              : l10n.icon_filter,
                          zoomButtonState: ButtonState.normal,
                          onZoomButtonClicked: viewModel.onZoomButtonClicked,
                          realPreviewButtonState: state.realPreviewButtonState,
                          onRealPreviewButtonClicked:
                              viewModel.onRealPreviewButtonClicked,
                          informationButtonState: state.formationButtonState,
                          onInformationButtonClicked: () =>
                              viewModel.onInformationButtonClicked(context),
                          showFilterButton: state.showFilterButton,
                          isFilterApplied: true,
                          onFilterButtonClicked: () =>
                              viewModel.onFilterButtonClicked(context),
                          onFilterResetButtonClicked:
                              viewModel.onFilterCloseButtonClicked,
                        ),
                      ),
                      const Spacer(flex: 12),
                      const Expanded(
                        flex: 958,
                        child: pre_base_white(),
                      ),
                      Expanded(
                        flex: 102,
                        child: Column(
                          children: [
                            const Spacer(flex: 12),
                            Expanded(
                              flex: 80,
                              child: Row(
                                children: [
                                  const Spacer(flex: 12),
                                  Expanded(
                                    flex: 152,
                                    child: grp_btn_add(
                                      onTap: viewModel.onReturnButtonClicked,
                                      text: l10n.icon_return,
                                    ),
                                  ),
                                  const Spacer(flex: 472),
                                  Expanded(
                                    flex: 152,
                                    child: grp_btn_embroidery_01(
                                      text: l10n.icon_00038_1,
                                      enable: state.setButtonEnable,
                                      onTap: viewModel.onSetButtonClicked,
                                    ),
                                  ),
                                  const Spacer(flex: 12),
                                ],
                              ),
                            ),
                            const Spacer(flex: 10),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const Expanded(
                flex: 61,
                child: EmbFooter(),
              ),
            ],
          )),

          Column(
            children: [
              const Spacer(flex: 159),
              Expanded(
                flex: 954,
                child: Column(
                  children: [
                    const Spacer(flex: 12),
                    Expanded(
                      flex: 438,
                      child: Row(
                        children: [
                          const Spacer(flex: 18),
                          Expanded(
                            flex: 770,
                            child: pre_multi_category_preview(
                              patternWidth: state.widthValue,
                              patternHeight: state.heightValue,
                              flameThumbnail:
                                  FlameThumbnailType.values[state.frameIndex],
                              colorNumber: state.numberOfColorsValue,
                              colorChange: state.colorChangesValue,
                              stitchTime: state.totalTimeValue,
                              preview: Preview(
                                patternDisplayInfoList:
                                    state.patternDisplayInfoList,
                                temporaryGroupDisplayInfoList:
                                    state.temporaryGroupDisplayInfoList,
                              ),
                            ),
                          ),
                          const Spacer(flex: 12),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 504,
                      child: Column(
                        children: [
                          const Spacer(flex: 49),
                          Expanded(
                            flex: 70,
                            child: Row(
                              children: [
                                const Spacer(flex: 666),
                                Expanded(
                                  flex: 98,
                                  child: grp_btn_filter_sort(
                                    onLeftTap:
                                        viewModel.onFilterReverseButtonClick,
                                    onRightTap:
                                        viewModel.onFilterOrderButtonClick,
                                    isSelectSortLeft: state.isSelectSortLeft,
                                  ),
                                ),
                                const Spacer(flex: 36),
                              ],
                            ),
                          ),
                          const Spacer(flex: 25),
                          Expanded(
                            flex: 360,
                            child: Row(
                              children: [
                                const Spacer(flex: 9),
                                Expanded(
                                  flex: 717,
                                  child: GridView.builder(
                                    itemCount: state.imageGroup.length,
                                    controller: viewModel.scrollController,
                                    gridDelegate:
                                        SliverGridDelegateWithMaxCrossAxisExtent(
                                            maxCrossAxisExtent: [
                                              100.0,
                                              139.0,
                                              177.0,
                                            ][state.thumbnailSize],
                                            mainAxisSpacing: 3,
                                            crossAxisSpacing: 3,
                                            childAspectRatio: [
                                              100 / 115,
                                              139 / 164,
                                              177 / 221,
                                            ][state.thumbnailSize]),
                                    itemBuilder: (context, index) =>
                                        switch (state.thumbnailSize) {
                                      0 => grp_btn_subcategory_thumunail_l_01(
                                          onTap: () =>
                                              viewModel.onPatternClick(index),
                                          number: (index + 1)
                                              .toString()
                                              .padLeft(3, "0"),
                                          patterImage: state.imageGroup[index],
                                        ),
                                      1 => grp_btn_subcategory_thumunail_m_01(
                                          onTap: () =>
                                              viewModel.onPatternClick(index),
                                          number: (index + 1)
                                              .toString()
                                              .padLeft(3, "0"),
                                          patterImage: state.imageGroup[index],
                                        ),
                                      2 ||
                                      _ =>
                                        grp_btn_subcategory_thumunail_s_01(
                                          onTap: () =>
                                              viewModel.onPatternClick(index),
                                          number: (index + 1)
                                              .toString()
                                              .padLeft(3, "0"),
                                          patterImage: state.imageGroup[index],
                                        ),
                                    },
                                  ),
                                ),
                                const Spacer(flex: 59),
                                Expanded(
                                  flex: 8,
                                  child: CustomScrollbar(
                                    key: UniqueKey(),
                                    controller: viewModel.scrollController,
                                    visibilityWhenScrollFull: false,
                                  ),
                                ),
                                const Spacer(flex: 7),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(flex: 167),
            ],
          ),

          /// zoom
          state.showZoomList
              ? MultiTouchBlocker(
                  child: Column(
                  children: [
                    const Spacer(flex: 78),
                    Expanded(
                      flex: 434,
                      child: Row(
                        children: [
                          const Spacer(flex: 6),
                          Expanded(
                            flex: 145,
                            child: ZoomSelectorPopup(
                              zoomList: viewModel.zoomDisplayList,
                              selectZoomValue: state.selectedZoomScale,
                              closePopup: viewModel.closeZoomPopup,
                              onZoomButtonClick: viewModel.onZoomPopupListClick,
                            ),
                          ),
                          const Spacer(flex: 649),
                        ],
                      ),
                    ),
                    const Spacer(flex: 768),
                  ],
                ))
              : Container(),
        ],
      ),
    );
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup(WidgetRef ref) => {};
}
