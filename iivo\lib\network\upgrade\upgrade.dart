import 'dart:async';
import 'dart:convert' show jsonDecode, jsonEncode;
import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:memory/memory_interface.dart';
import 'package:network_wifi/network_wifi.dart';
import 'package:panel_library/panel_library.dart';
import 'package:path/path.dart';
import 'package:pointycastle/digests/md5.dart';
import 'package:usb_manager/usb_manager_platform_interface.dart';

import '../../model/message_sound_model.dart';
import '../../model/upf_verify_signature_model.dart';
import '../../ui/app/setting/model/network_setting_page2_model.dart';
import '../network_log.dart';
import 'upgrade_info/serialize_new/upgrade_info.dart';

class UpgradeFileType {
  static const voice = 8;
  static const ota = 0;
  static const home = 1;
  static const panel = 2;
  static const main = 3;
  static const manual = 4;
  static const eula = 5;
}

enum ModelType {
  sew,
  emb,
  combo,
}

/// ダウンロードの終了状態
enum DownloadExitStatus {
  // ダウンロード失敗
  failed,
  // ストレージ容量不足のため、ダウンロードに失敗しました
  failedMemoryFull,
  // ダウンロード一時停止中
  paused,
  // ダウンロードは成功し、EULA または音声のみが更新され、再起動は必要ありません
  done,
  // ダウンロードは成功しました，再起動と更新を待つ必要があります
  waitForReboot
}

/// VerUp ポップアップの種類
enum VerUpPopType {
  none,
  wlanOff,
  wlanOn,
}

Ref? _wlanUpdateStateRef;

/// ステータス プロバイダーを更新します，UI はこのステータスを監視できます。
final wlanUpdateStateProvider = StateProvider<bool?>((ref) {
  _wlanUpdateStateRef = ref;
  return Upgrade().hasUpdateInLocal() ? true : null;
});

class Upgrade {
  factory Upgrade() => _instance;
  Upgrade._internal();
  static final Upgrade _instance = Upgrade._internal();

  static const logTag = "UpgradeLog";

  static const _upgradeDirName = "upgrade";

  static const _newUpgradeJsonName = "sewingfirmupload_new.json";

  static const _voiceListJsonName = "voicelist.json";

  static final String upgradeDir =
      join(memorySector.brother_dir.absolutePath, _upgradeDirName);

  static const String _wlanDownloadDirName = "wlan";

  static final String _wlanDownloadDir = join(upgradeDir, _wlanDownloadDirName);

  ///
  /// ローカル バージョン情報がない場合、デフォルトのバージョンは 0 です。
  /// これはアップグレードされることを意味します。
  ///
  static const defaultVersion = 0;

  ///
  /// サーバーの URL アドレス
  ///
  late String upgradeUrl;

  ///
  /// バージョン情報のファイル名。
  ///
  static const String upgradeJsonName = 'sewingfirmupload.json';

  /// プレースホルダー
  static const String _placeholder = "";

  ///
  /// 電源投入直後の5分間に1回のバージョン検出
  ///
  static const int _delayTimeMin = 5;

  ///
  /// 1 日に 1 回更新をチェックする
  ///
  static const int _delayDay = 1;

  ///
  /// ブラザー仕向け
  ///
  final int specBrother = 1;

  ///
  /// OEM（タコニー仕向け、…）
  ///
  final int specOem = 4;

  ///
  /// Upgrade 初期化
  ///
  Future<void> init() async {
    _initDownloadServerUrl();

    if (WifiManager().isEnabled() == false) {
      DeviceLibrary().apiBinding.addWlanOffStartUpCount();
    } else {
      /// do nothing
    }

    // 電源を入れて 5 分間待った後、アップデートを確認します。
    Timer(const Duration(minutes: _delayTimeMin), () async {
      if (WifiManager().isEnabled()) {
        await _checkWlanUpdate();
      } else {
        // この時点で WIFI がオンになっていない場合は、
        // WIFI がオンになるまで待ってからアップデートを確認してください。
        _shouldCheckOnWifiEnabled = true;
      }
      // それ以降は、24 時間ごとに更新を確認します。
      Timer.periodic(const Duration(days: _delayDay), (_) async {
        if (WifiManager().isEnabled()) {
          await _checkWlanUpdate();
        } else {
          /// WLAN がオフの場合はオンラインでアップデートをチェックしないでください
        }
      });
    });

    _restoreEulaBackupIfAvailable();
    _restoreVoiceBackupIfAvailable();
    // ローカルにインストールされたバージョン情報を解析する
    _installedUpgradeInfo = _parseInstalledUpgradeInfo();
    // ローカルに保存された最新バージョン情報を解析します
    _newUpgradeInfo = _parseSavedUpgradeInfo();

    // ローカルのバージョンがアップグレードされている可能性があります。
    // ローカルのバージョンが新しいバージョン以上の場合、アップグレードステータスをリセットします。
    // 参照：https://brothergroup.atlassian.net/browse/PHFIRMIIVO-7975
    final int? installedUpfVersion = _installedUpgradeInfo?.version;
    final int? newUpfVersion = _newUpgradeInfo?.version;
    if (installedUpfVersion != null &&
        newUpfVersion != null &&
        installedUpfVersion >= newUpfVersion) {
      isInForcedUpdateMode = false;
      resetWlanUpdateStatus(isUpdated: false);
    } else {
      // Do nothing
    }
  }

  ///
  /// ダウンロード用のサーバーURLを設定する
  ///
  void _initDownloadServerUrl() {
    // テスト用のサーバーの場合は、テスト用のURLを設定する
    const int testServerFlag = 7;
    if (DeviceLibrary().apiBinding.getKitServer() == testServerFlag) {
      upgradeUrl =
          'https://download.brother.com/pub/com/phc_pcs_oneclickupdate/frm/iivo_test';
      NetworkLog.i("Upgrade is using test server url: $upgradeUrl");
    } else {
      upgradeUrl =
          'https://download.brother.com/pub/com/phc_pcs_oneclickupdate/frm/iivo/o9BAvSs6U7RJw0uAuloLDnw4';
    }
  }

  bool _shouldCheckOnWifiEnabled = false;

  ///
  /// WIFI がオンになってから 5 分後に更新を確認します (1 回のみ)。
  ///
  void checkFirmUpdateOnWifiEnabled() {
    if (_shouldCheckOnWifiEnabled) {
      Timer(const Duration(minutes: _delayTimeMin), () async {
        await _checkWlanUpdate();
      });
      _shouldCheckOnWifiEnabled = false;
    }
  }

  /// 更新があるかどうかを判断する
  bool hasUpdateInLocal() => _newUpgradeInfo != null;

  /// 更新があるかどうかを判断する
  bool get isReadyUpgrade => hasUpdateInLocal();

  /// この更新は必須の更新ですか
  /// Relaese1.01から、強制アップデートモードは不要になったため、常にfalseを返します。
  bool _isUpdateForced() => false;

  /// 現在強制アップデート状態になっていませんか
  bool isInForcedUpdateMode = false;

  /// 端末にインストールされているバージョン情報
  UpgradeInfo? _installedUpgradeInfo;

  /// 新規アップデート情報
  UpgradeInfo? _newUpgradeInfo;

  /// UPFHeader.json 内のフィールド名
  final _keyUpfVersion = "upfversion";

  ///
  /// サーバー上にシステムアップデート（ファームアップデートまたは音声アップデート）があるかどうかを確認します。
  ///
  /// null: チェックは失敗しました。
  /// true: チェックは成功し、更新がありました。
  /// false: チェックは成功しましたが、更新はありませんでした。
  ///
  Future<bool?> hasUpdateInServer() async {
    bool? available;
    try {
      final remoteUpgradeInfo = await getUpgradeVersionInfoInServer();

      if (remoteUpgradeInfo != null) {
        if (remoteUpgradeInfo.modelCode != getMachineModelCode()) {
          available = false;
        } else {
          // 音声アップデートまたはファームウェアアップデート
          if (_hasNewVoiceVersion(remoteUpgradeInfo)) {
            available = true;
          } else {
            available = remoteUpgradeInfo.version >
                (_installedUpgradeInfo?.version ?? defaultVersion);
          }
        }
      } else {
        available = null;
      }

      // サーバー上で新しいバージョンを検出したら、ローカルに保存します。
      if (available == true) {
        if (_saveServerUpgradeInfoToFile(remoteUpgradeInfo!)) {
          _newUpgradeInfo = remoteUpgradeInfo;
        } else {
          available = null;
        }
      }
    } catch (e) {
      NetworkLog.e("Check firm update in server failed: $e");
      available = null;
    }

    return available;
  }

  /// 音声リスト
  Map<String, dynamic> _voiceList = {};

  ///
  /// 音声リストを解析します
  ///
  Future<bool> _parseVoiceListJson() async {
    final voiceJsonDownloadUrl = join(upgradeUrl, _voice, _voiceListJsonName);
    final dio = Dio();
    bool success;
    try {
      NetworkLog.i("Start to download voice list json: $voiceJsonDownloadUrl");
      final response = await dio.get(voiceJsonDownloadUrl);
      final statusCode = response.statusCode;
      if (statusCode == _httpStatusCodeOK) {
        _voiceList = response.data;
        success = true;
      } else {
        success = false;
        NetworkLog.e("Failed to download voice list json: $statusCode");
      }
    } catch (e) {
      NetworkLog.e("Failed to parse voice list json: $e");
      success = false;
    } finally {
      dio.close();
    }
    return success;
  }

  /// 更新をチェックしているかどうかを確認します
  bool isCheckingFirmUpdate = false;

  ///
  /// 更新と書き込みステータスを確認する
  ///
  Future<void> _checkWlanUpdate() async {
    if (isWlanDownloading) return;
    isCheckingFirmUpdate = true;
    bool? hasUpdate;
    if (_newUpgradeInfo != null) {
      // 新バージョン更新情報がローカルに既に存在する場合は、サーバー上で確認する必要はありません。
      // アップグレード パッケージはダウンロード前にサーバー上で再度チェックされるため、
      // ローカルのアップデート情報が古くなっても心配する必要はありません。
      hasUpdate = true;
    } else {
      hasUpdate = await hasUpdateInServer();
    }
    isCheckingFirmUpdate = false;
    if (hasUpdate != null) {
      // UI に更新を通知する
      updateWlanUpdateState(hasUpdate);
    }
  }

  ///
  /// 更新ステータスを更新します
  ///
  void updateWlanUpdateState(bool? newState) {
    _wlanUpdateStateRef
        ?.read(wlanUpdateStateProvider.notifier)
        .update((_) => newState);
  }

  ///
  /// 現在のデバイスにインストールされているファームウェアのバージョン情報を取得します
  ///
  UpgradeInfo? getInstalledUpgradeInfo() {
    return _installedUpgradeInfo;
  }

  ///
  /// 更新情報を手動で設定する
  ///
  void setNewUpgradeInfo(UpgradeInfo info) {
    if (_saveServerUpgradeInfoToFile(info)) {
      _newUpgradeInfo = info;
      updateWlanUpdateState(true);
    }
  }

  ///
  /// 更新する必要があるローカル フラグがある場合は、ポップアップ ウィンドウが表示されます。
  /// 強制アップデートの場合は、強制アップデートモードに入ります。
  ///
  /// PS: このウィンドウは再起動後にのみ表示されます
  ///
  bool isShowWlanUpdatePopup() {
    if (hasUpdateInLocal()) {
      isInForcedUpdateMode = _isUpdateForced();

      /// 強制アップデート状態では、すべての物理ボタンがロックされます。
      if (isInForcedUpdateMode) {
        TpdLibrary().apiBinding.lockAll();
      }
      return true;
    } else {
      NetworkLog.i("No update detected, do not show popup");
      return false;
    }
  }

  ///
  /// アップデート完了後、または工場出荷時の設定に復元した後、
  /// アップデートステータスをリセットします。
  ///
  /// [isUpdated] アップデートが完了したためステータスをリセット
  /// isUpdated: false:リセット、true：アップグレード完了
  ///
  bool resetWlanUpdateStatus({required bool isUpdated}) {
    // 強制更新状態では、更新ステータスをリセットすることはできません。
    if (isUpdated == false && isInForcedUpdateMode) return false;

    try {
      _deleteDownloadCache();

      final newUpgradeInfoFile = File(join(upgradeDir, _newUpgradeJsonName));
      if (newUpgradeInfoFile.existsSync()) {
        newUpgradeInfoFile.deleteSync();
      } else {
        // no-op
      }

      resetWlanOffStartUpCount();

      if (isUpdated) return true;

      if (isInForcedUpdateMode) {
        isInForcedUpdateMode = false;
        TpdLibrary().apiBinding.clearLockAll();
      } else {
        // no-op
      }

      _newUpgradeInfo = null;

      NetworkSettingPage2Model().downloadState =
          WlanUpgradeDownloadState.unknown;

      updateWlanUpdateState(null);
    } catch (e) {
      NetworkLog.e("Failed to reset wlan update status: $e");
      return false;
    }
    return true;
  }

  ///
  /// 更新情報をjsonファイルとして保存
  ///
  bool _saveServerUpgradeInfoToFile(UpgradeInfo info) {
    try {
      final upgradeDirectory = DirectoryEntity(upgradeDir);
      if (upgradeDirectory.existsSync() == false) {
        upgradeDirectory.createSync(recursive: true);
      }
      File(join(upgradeDir, _newUpgradeJsonName))
          .writeAsStringSync(jsonEncode(info.toJson()), flush: true);
      return true;
    } catch (e) {
      NetworkLog.e("Failed to save server upgrade info: $e");
      return false;
    }
  }

  /// FileUpload.json のフィールド名は次のとおりです：
  final _keyComponentList = "componentlist";
  final _keyType = "type";
  final _keyVersion = "version";

  ///
  /// ローカル ファイルから [UpgradeInfo] を解析する
  ///
  UpgradeInfo? _parseInstalledUpgradeInfo() {
    UpgradeInfo? upgradeInfo;
    try {
      // UPFHeader.json を解析する
      final upfHeaderJsonFile = File(join(upgradeDir, _upfHeaderFilename));
      if (upfHeaderJsonFile.existsSync()) {
        final Map<String, dynamic> json =
            jsonDecode(upfHeaderJsonFile.readAsStringSync());
        upgradeInfo = _createEmptyUpgradeInfo();
        upgradeInfo.version = json[_keyUpfVersion];
        if (json.containsKey(_keyComponentList)) {
          final componentList = json[_keyComponentList] as List<dynamic>;
          // ダミー値
          const int dummy = -1;
          for (var component in componentList) {
            final int filetype = component[_keyType];
            if (filetype == UpgradeFileType.voice) {
              upgradeInfo.voiceVer = component[_keyVersion];
            } else {
              final fileList = FileList(component[_keyType],
                  component[_keyVersion], _placeholder, dummy, _placeholder);
              upgradeInfo.fileList.add(fileList);
            }
          }
        } else {
          // no-op
        }
      } else {
        // no-op
      }
    } catch (e) {
      NetworkLog.e("Failed to parse installed upgrade info: $e");
      upgradeInfo = null;
    }
    return upgradeInfo;
  }

  /// versionCode を versionName に変換するための乗数
  static const _versionFormatMultiplier = 100;

  /// versionCode を versionName に変換するための乗数
  static const _versionNameFixedCount = 2;

  /// デフォルトのバージョン名
  static const defaultVersionName = "-.--";

  ///
  /// 最新のバージョン名を取得します(バージョン番号を 100 で割って、小数点以下 2 桁を保持します)
  ///
  String getLatestVersionName() {
    int? latestVersion = _newUpgradeInfo?.version;
    if (latestVersion == null) {
      return defaultVersionName;
    } else {
      return (latestVersion / _versionFormatMultiplier)
          .toStringAsFixed(_versionNameFixedCount);
    }
  }

  ///
  /// ローカル バージョン名を取得します (バージョン番号を 100 で割ったもの、小数点以下 2 桁を保持します)
  ///
  String getInstalledVersionName() {
    String versionName;
    final upfHeaderFile = File(join(upgradeDir, _upfHeaderFilename));
    if (upfHeaderFile.existsSync() == false) {
      versionName = defaultVersionName;
    } else {
      final version = _installedUpgradeInfo?.version;
      if (version != null) {
        versionName = (version / _versionFormatMultiplier)
            .toStringAsFixed(_versionNameFixedCount);
      } else {
        versionName = defaultVersionName;
      }
    }
    return versionName;
  }

  ///
  /// 音声が更新されたかどうかを確認する
  ///
  bool _hasNewVoiceVersion(UpgradeInfo? serverUpgradeInfo) {
    if (serverUpgradeInfo != null) {
      final newVersion = serverUpgradeInfo.voiceVer;
      if (_installedUpgradeInfo != null) {
        final installedVersion = _installedUpgradeInfo!.voiceVer;
        return newVersion > installedVersion;
      } else {
        return newVersion > defaultVersion;
      }
    } else {
      return false;
    }
  }

  /// 無効なインデックス
  static const _invalidIndex = -1;

  ///
  /// [UpgradeInfo] の [filetype] のバージョンを取得します
  ///
  int _getVersionFromUpgradeInfo(UpgradeInfo? info, int filetype) {
    if (info == null) return defaultVersion;

    final index =
        info.fileList.indexWhere((element) => element.filetype == filetype);
    if (index != _invalidIndex) {
      return info.fileList[index].version;
    } else {
      return defaultVersion;
    }
  }

  ///
  /// [filetype] が更新されたかどうかを確認する
  ///
  bool _hasNewVersion(int filetype) {
    return _getVersionFromUpgradeInfo(_newUpgradeInfo, filetype) >
        _getVersionFromUpgradeInfo(_installedUpgradeInfo, filetype);
  }

  ///
  /// EULA が更新されたかどうかを確認する
  ///
  bool hasNewEulaVersion() => _hasNewVersion(UpgradeFileType.eula);

  ///
  /// データをダウンロードします。キーはダウンロード ファイルの種類、
  /// 値はダウンロード ファイルのサイズです。
  ///
  final Map<int, int> _downloadStats = {};

  /// 現在のダウンロード数
  int _curDownloadCount = 0;

  ///
  /// [file] の MD5 ダイジェストが [expectedMD5] と一致しているかどうかを確認します
  ///
  Future<bool> _checkMD5(
      File file, String expectedMD5, CancelToken cancelToken) async {
    try {
      final actual =
          (await _calculateFileMD5(file, cancelToken))?.toLowerCase();
      final expected = expectedMD5.toLowerCase();
      if (expected != actual) {
        NetworkLog.i(
            "MD5 digest unmatched, expected: $expected, actual: $actual");
        return false;
      }
      return true;
    } catch (e) {
      NetworkLog.e("Failed to calculate md5 of ${file.path}: $e");
      return false;
    }
  }

  ///
  /// [file] の MD5 ダイジェストを計算します，途中でキャンセルした場合は null を返す。
  ///
  Future<String?> _calculateFileMD5(File file, CancelToken cancelToken) async {
    final md5 = MD5Digest();
    final raf = await file.open();
    try {
      // 毎回ファイルダイジェストを計算するために使用されるバッファサイズ
      // テストした結果、5MB のバッファーの方が良いことがわかりました
      const digestSizePerTime = 5 * 1024 * 1024;
      Uint8List buffer = Uint8List(digestSizePerTime);
      int read;
      while ((read = await raf.readInto(buffer)) > 0) {
        if (cancelToken.isCancelled) return null;
        md5.update(buffer, 0, read);
      }
    } finally {
      await raf.close();
    }
    final digest = Uint8List(md5.digestSize);
    md5.doFinal(digest, 0);
    // ダイジェストバイトを16進文字列に変換します
    const int md5Radix = 16;
    // 2 桁をゼロで埋めます
    const int md5PadLeft = 2;
    return digest
        .map((byte) => byte.toRadixString(md5Radix).padLeft(md5PadLeft, '0'))
        .join();
  }

  ///
  /// ファイルの整合性を確認します (存在、サイズ、MD5 チェック)。
  ///
  Future<bool> _verifyIntegrity(
      File file, FileList target, CancelToken cancelToken) async {
    try {
      return file.existsSync() &&
          file.lengthSync() == target.fileSize &&
          await _checkMD5(file, target.fileMd5, cancelToken);
    } catch (e) {
      NetworkLog.e("Failed to check integrity of ${file.path}: $e");
      return false;
    }
  }

  ///
  /// ダウンロード前の準備
  ///
  /// [downloadVoiceOnly] Voice Guidance ページからダウンロードを開始するかどうか
  ///
  bool prepareDownload({required bool downloadVoiceOnly}) {
    _curDownloadCount = 0;
    _downloadStats.clear();
    return _initDownloadStatistics(downloadVoiceOnly);
  }

  ///
  /// ダウンロード失敗時の[DownloadExitStatus]取得
  ///
  DownloadExitStatus _getFailureStatus(CancelToken cancelToken) {
    if (cancelToken.isCancelled) return DownloadExitStatus.paused;
    return DownloadExitStatus.failed;
  }

  ///
  /// [_downloadAllThrowIfMemoryFull]機能と同じです。
  ///
  /// ただし、ストレージ容量不足によりダウンロードに失敗した場合、
  /// 全てのダウンロードキャッシュが削除され、[DownloadExitStatus.failedMemoryFull]
  /// が返されます。
  ///
  Future<DownloadExitStatus> downloadAll(
      Function(int totalCount, int currentCount, double fraction) onProgress,
      Function(bool) setPauseable,
      Function(Completer<bool>) onEulaPrepared,
      CancelToken cancelToken) async {
    try {
      return await _downloadAllThrowIfMemoryFull(
          onProgress, setPauseable, onEulaPrepared, cancelToken);
    } catch (e) {
      if (isMemoryFullException(e)) {
        NetworkLog.e("Failed to download all due to memory full: $e");
        // ストレージ容量不足によりダウンロードに失敗した場合、
        // ダウンロード済みの全てのキャッシュファイルを削除する
        _deleteDownloadCache();
        return DownloadExitStatus.failedMemoryFull;
      } else {
        NetworkLog.e("Unexpected exception occurred while downloading all: $e");
        return DownloadExitStatus.failed;
      }
    }
  }

  ///
  /// すべてのアップデート ファイルをダウンロードします。
  /// EULA アップデートがある場合は、最初にダウンロードされ、
  /// 承認のためにユーザーに表示されます。その後、その他をダウンロードしてください。
  ///
  /// この方法は安全ではありません。
  /// ストレージ容量が不足してダウンロードに失敗した場合、例外をスローします。
  ///
  /// @see [downloadAll]
  ///
  Future<DownloadExitStatus> _downloadAllThrowIfMemoryFull(
      Function(int totalCount, int currentCount, double fraction) onProgress,
      Function(bool) setPauseable,
      Function(Completer<bool>) onEulaPrepared,
      CancelToken cancelToken) async {
    void notifyProgress(double fraction) =>
        onProgress(_downloadStats.length, _curDownloadCount, fraction);

    if (await _downloadUpfHeader() == false) return DownloadExitStatus.failed;

    if (hasNewEulaVersion()) {
      if (await _downloadAndReplaceEula(
          notifyProgress, setPauseable, cancelToken)) {
        final eulaAgreeCompleter = Completer<bool>();
        onEulaPrepared(eulaAgreeCompleter);
        if (await eulaAgreeCompleter.future &&
            await _updateVersion(UpgradeFileType.eula)) {
          _deleteEulaDownloadCache();
          _deleteEulaBackup();
        } else {
          _restoreEulaBackupIfAvailable();
          return DownloadExitStatus.failed;
        }
      } else {
        return _getFailureStatus(cancelToken);
      }
    } else {
      // EULA の更新が必要ない場合は何もしない
    }

    // 更新する必要がある FileList フィールド内のすべてのファイルをダウンロードします
    final pendingUpfFiles =
        await _downloadFileList(notifyProgress, cancelToken);
    if (pendingUpfFiles == null) return _getFailureStatus(cancelToken);

    // 音声をダウンロードして更新する
    final voiceDone = await _downloadAndUpdateVoice(
        notifyProgress, setPauseable, cancelToken);
    if (voiceDone == false) return _getFailureStatus(cancelToken);

    if (_deleteObsoletedUPFs(pendingUpfFiles) == false) {
      return DownloadExitStatus.failed;
    }

    if (pendingUpfFiles.isEmpty) {
      // UPFHeader.json をオーバーライドします
      if (_updateUPFHeader() == false) return DownloadExitStatus.failed;
      if (resetWlanUpdateStatus(isUpdated: true)) {
        return DownloadExitStatus.done;
      } else {
        return DownloadExitStatus.failed;
      }
    } else {
      // ファイルを目的の場所に移動し、再起動を待ちます
      if (_moveUpfFilesToDestination(pendingUpfFiles)) {
        return DownloadExitStatus.waitForReboot;
      } else {
        return DownloadExitStatus.failed;
      }
    }
  }

  ///
  /// 指定されたファイルタイプのバージョン情報を UPFHeader.json ファイル内で更新します。
  /// ローカルに UPFHeader.json が存在しない場合は、一時ファイルから読み込みます。
  /// 更新に成功した場合は true を、失敗した場合は false を返します。
  ///
  /// [filetype] 更新するファイルタイプ
  ///
  Future<bool> _updateVersion(int filetype) async {
    if (_newUpgradeInfo == null) return false;

    bool isVoice = filetype == UpgradeFileType.voice;
    final FileList target;

    if (isVoice) {
      target =
          FileList(UpgradeFileType.voice, _newUpgradeInfo!.voiceVer, "", 0, "");
    } else {
      target = _newUpgradeInfo!.fileList
          .firstWhere((element) => element.filetype == filetype);
    }

    bool succeeded;
    try {
      bool localUpfHeaderExists;
      var upfHeaderFile = File(join(upgradeDir, _upfHeaderFilename));
      if (upfHeaderFile.existsSync() == false) {
        localUpfHeaderExists = false;
        upfHeaderFile = File(join(_wlanDownloadDir, _upfHeaderFilename));
        if (upfHeaderFile.existsSync() == false) return false;
      } else {
        localUpfHeaderExists = true;
      }

      final Map<String, dynamic> json =
          jsonDecode(upfHeaderFile.readAsStringSync());

      if (json.containsKey(_keyComponentList)) {
        final List<dynamic> componentList = json[_keyComponentList];
        final index = componentList
            .indexWhere((element) => element[_keyType] == filetype);
        if (index == _invalidIndex) {
          succeeded = false;
        } else {
          if (localUpfHeaderExists == false) {
            for (var component in componentList) {
              if (component[_keyType] != filetype) {
                component[_keyVersion] = defaultVersion;
              } else {
                // no-op
              }
            }
            json[_keyUpfVersion] = defaultVersion;
          } else {
            componentList[index][_keyVersion] = target.version;
          }
        }

        final dstFile = File(join(upgradeDir, _upfHeaderFilename));
        if (dstFile.existsSync() == false) {
          dstFile.createSync();
        } else {
          // no-op
        }
        await dstFile.writeAsString(jsonEncode(json));

        if (_installedUpgradeInfo == null) {
          _installedUpgradeInfo = _createEmptyUpgradeInfo();
          if (isVoice) {
            _installedUpgradeInfo!.voiceVer = target.version;
          } else {
            _installedUpgradeInfo!.fileList.add(target);
          }
        } else {
          if (isVoice) {
            _installedUpgradeInfo!.voiceVer = target.version;
          } else {
            int index = _installedUpgradeInfo!.fileList
                .indexWhere((element) => element.filetype == filetype);
            _installedUpgradeInfo!.fileList[index] = target;
          }
        }

        succeeded = true;
      } else {
        succeeded = false;
      }
    } catch (e) {
      NetworkLog.e("Failed to update version of $filetype: $e");
      succeeded = false;
    }

    return succeeded;
  }

  ///
  /// UPFHeader.json をダウンロードする
  ///
  Future<bool> _downloadUpfHeader() async {
    if (_newUpgradeInfo == null) return false;
    final upfHeaderFile = File(join(_wlanDownloadDir, _upfHeaderFilename));
    try {
      if (upfHeaderFile.existsSync()) {
        final Map<String, dynamic> json =
            jsonDecode(upfHeaderFile.readAsStringSync());
        final int version = json[_keyUpfVersion];
        if (version == _newUpgradeInfo!.version) return true;
      }
    } catch (e) {
      NetworkLog.e("Failed to check upf header version, force to download: $e");
    }
    final dlUrl = _newUpgradeInfo!.upfHeader;
    return _downloadFile(
      urlPath: dlUrl,
      savePath: join(_wlanDownloadDir, _upfHeaderFilename),
    );
  }

  ///
  /// EULA 更新のみがある場合は、UPFHeader.json を手動で更新する必要があります
  ///
  bool _updateUPFHeader() {
    bool succeeded;
    final upfHeaderFile = File(join(_wlanDownloadDir, _upfHeaderFilename));
    try {
      if (upfHeaderFile.existsSync()) {
        upfHeaderFile.renameSync(join(upgradeDir, _upfHeaderFilename));
        succeeded = true;
      } else {
        succeeded = false;
      }
    } catch (e) {
      NetworkLog.e("Failed to update UPFHeader.json: $e");
      succeeded = false;
    }
    return succeeded;
  }

  ///
  /// 一時ダウンロードファイルをすべて削除します
  ///
  void _deleteDownloadCache() {
    try {
      final dir = Directory(_wlanDownloadDir);
      if (dir.existsSync()) {
        dir.deleteSync(recursive: true);
      } else {
        // no-op
      }
    } catch (e) {
      NetworkLog.e("Failed to delete wlan download cache: $e");
    }
  }

  ///
  /// EULA のダウンロードキャッシュを削除します
  ///
  void _deleteEulaDownloadCache() {
    try {
      final cache = File(join(_wlanDownloadDir,
          _getDownloadFilename(_requireNewFileList(UpgradeFileType.eula))));
      if (cache.existsSync()) {
        cache.deleteSync();
      }
    } catch (e) {
      NetworkLog.e("Failed to delete eula download cache: $e");
    }
  }

  ///
  /// [upfFiles] 内のファイルを指定した場所に移動し、再起動後の更新を待ちます。
  ///
  bool _moveUpfFilesToDestination(List<File> upfFiles) {
    for (var file in upfFiles) {
      final destPath = join(upgradeDir, basename(file.path));
      try {
        if (file.path != destPath) {
          file.renameSync(destPath);
        }
      } catch (e) {
        NetworkLog.e("Failed to move ${file.path} to $destPath");
        return false;
      }
    }
    return true;
  }

  ///
  /// 音声をダウンロードして更新する
  ///
  Future<bool> _downloadAndUpdateVoice(Function(double fraction) onProgress,
      Function(bool) setPauseable, CancelToken cancelToken) async {
    bool succeeded;
    if (_hasNewVoiceVersion(_newUpgradeInfo)) {
      final downloadOK = await downloadVoice(cancelToken, onProgress);
      if (downloadOK) {
        setPauseable(false);
        succeeded = await updateVoice(false);
        setPauseable(true);
      } else {
        succeeded = false;
      }
    } else {
      succeeded = true;
    }
    return succeeded;
  }

  /// WLAN によってダウンロードされた UPF のファイルプレフィックス
  final _wlanDownloadFilenamePrefix = "WLAN_UPG_T";

  ///
  /// [FileList] に基づいてダウンロードファイル名を取得します。
  ///
  String _getDownloadFilename(FileList target) {
    return "$_wlanDownloadFilenamePrefix${target.filetype}_V${target.version}.upf";
  }

  ///
  /// EULA を除く FileList 内の各アイテムのダウンロードを開始します
  ///
  Future<List<File>?> _downloadFileList(
      Function(double fraction) onProgress, CancelToken cancelToken) async {
    final List<File> downloadedFiles = [];
    for (var downloadTarget in _newUpgradeInfo!.fileList) {
      if (downloadTarget.filetype != UpgradeFileType.eula &&
          _hasNewVersion(downloadTarget.filetype)) {
        int currentDownloadSize = 0;
        int totalDownloadSize = _downloadStats[downloadTarget.filetype]!;
        final dlDestPath =
            join(upgradeDir, _getDownloadFilename(downloadTarget));
        final dlDestFile = File(dlDestPath);
        if (dlDestFile.existsSync()) {
          if (dlDestFile.lengthSync() == downloadTarget.fileSize) {
            // ターゲット ファイルがすでに存在し、検証に合格した場合は、
            // サーバーからダウンロードする必要はなく、次のファイルが直接処理されます。
            downloadedFiles.add(dlDestFile);
            currentDownloadSize += downloadTarget.fileSize;
            onProgress(currentDownloadSize / totalDownloadSize);
            continue;
          } else {
            dlDestFile.deleteSync();
          }
        }

        if (cancelToken.isCancelled) return null;

        final file =
            await _downloadFileTarget(downloadTarget, cancelToken, onProgress);
        if (file != null) {
          downloadedFiles.add(file);
        } else {
          // ダウンロードに失敗したファイルがある場合は、直接 null が返されます。
          return null;
        }
      }
    }
    return downloadedFiles;
  }

  ///
  /// 古い UPF ファイルを削除します
  ///
  bool _deleteObsoletedUPFs(List<File> pendingUpfFiles) {
    try {
      const upfFileSuffix = ".upf";
      for (var file in Directory(upgradeDir).listSync()) {
        final filename = basename(file.path);
        if (file.statSync().type == FileSystemEntityType.file &&
            filename.endsWith(upfFileSuffix) &&
            !pendingUpfFiles
                .any((element) => basename(element.path) == filename)) {
          file.deleteSync();
        } else {
          // no-op
        }
      }
    } catch (e) {
      NetworkLog.e("Failed to delete residual files: $e");
      return false;
    }
    return true;
  }

  static const _upfHeaderFilename = "UPFHeader.json";

  ///
  /// EULA をダウンロードして置き換えます，ソース ファイルはバックアップされます。
  ///
  Future<bool> _downloadAndReplaceEula(Function(double fraction) onProgress,
      Function(bool) setPauseable, CancelToken cancelToken) async {
    bool succeeded;
    File? downloadedEula;
    bool isMemoryFull = false;
    try {
      final eula = _requireNewFileList(UpgradeFileType.eula);
      downloadedEula = await _downloadFileTarget(eula, cancelToken, onProgress);

      if (downloadedEula == null) {
        succeeded = false;
      } else {
        setPauseable(false);
        if (await UPFVerifySignatureModel.verifyFileSignature(
                downloadedEula.path) &&
            await _backupEula()) {
          succeeded = await _extractEulaFromUpf(downloadedEula);
        } else {
          succeeded = false;
        }
      }
    } catch (e) {
      NetworkLog.e("Failed to download and replace eula: $e");
      succeeded = false;
      isMemoryFull = isMemoryFullException(e);
    }
    if (succeeded) {
      setPauseable(true);
    } else {
      await _restoreEulaBackupIfAvailable();
    }
    if (isMemoryFull) {
      throw FullMemoryException();
    }
    return succeeded;
  }

  /// EULA ファイル名の共通接頭辞
  final _eulaFilenamePrefix = "manEULA";

  /// EULA ファイルのバックアップディレクトリ名
  final _eulaBackupSubDirName = "EULA_BACKUP";

  ///
  /// EULA の更新に失敗した場合に復元できるように、
  /// 既存の EULA ファイルをバックアップします。
  ///
  Future<bool> _backupEula() async {
    bool succeeded;
    try {
      final eulaDir = Directory(memorySector.eula.absolutePath);
      final eulaBackupDir =
          Directory(join(eulaDir.path, _eulaBackupSubDirName));
      if (eulaBackupDir.existsSync() == false) {
        eulaBackupDir.createSync(recursive: true);
      }
      for (var file in eulaDir.listSync()) {
        final filename = basename(file.path);
        if (filename.startsWith(_eulaFilenamePrefix)) {
          await file.rename(join(eulaBackupDir.path, filename));
        } else {
          // no-op
        }
      }
      succeeded = true;
    } catch (e) {
      NetworkLog.e("Failed to backup eula files: $e");
      succeeded = false;
    }
    return succeeded;
  }

  ///
  /// 利用可能な場合は、EULA バックアップに返信します
  ///
  Future<void> _restoreEulaBackupIfAvailable() async {
    try {
      final eulaDirPath = memorySector.eula.absolutePath;
      final eulaBackupDir = Directory(join(eulaDirPath, _eulaBackupSubDirName));
      if (eulaBackupDir.existsSync() == false) return;

      for (var file in eulaBackupDir.listSync()) {
        final targetFilepath = join(eulaDirPath, basename(file.path));
        await file.rename(targetFilepath);
      }
      if (eulaBackupDir.listSync().isEmpty) {
        eulaBackupDir.deleteSync();
      }
    } catch (e) {
      NetworkLog.i("Failed to restore eula backup: $e");
    }
  }

  ///
  /// UPF から EULA PDF を抽出する
  ///
  Future<bool> _extractEulaFromUpf(File eulaUpfFile) async {
    bool succeeded;
    try {
      final ret = await UsbManagerPlatform.instance.extractDirFromUpf(
          outputDirPath: memorySector.eula.absolutePath,
          upfFilepath: eulaUpfFile.path,
          dirName: "eula",
          password: UPFVerifySignatureModel.password);
      succeeded = ret == true;
    } catch (e) {
      NetworkLog.e("Failed to extract eula: $e");
      succeeded = false;
    }
    return succeeded;
  }

  ///
  /// EULA のバックアップディレクトリを削除します
  ///
  bool _deleteEulaBackup() {
    bool succeeded;
    try {
      final eulaBackupDir = Directory(
          join(memorySector.eula.absolutePath, _eulaBackupSubDirName));
      if (eulaBackupDir.existsSync()) {
        eulaBackupDir.deleteSync(recursive: true);
      }
      succeeded = true;
    } catch (e) {
      NetworkLog.i("Failed to delete eula backup dir: $e");
      succeeded = false;
    }
    return succeeded;
  }

  ///
  /// 最新の [UpgradeInfo] から指定された [filetype] の [FileList] を取得します
  ///
  FileList _requireNewFileList(int filetype) {
    return _newUpgradeInfo!.fileList
        .firstWhere((element) => element.filetype == filetype);
  }

  /// まだダウンロードされていないファイルをマークする接尾辞
  final _downloadTmpSuffix = ".tmp";

  ///
  /// [FileList] の情報に基づいてファイルをダウンロードし、
  /// ブレークポイントによるダウンロードの再開をサポートします。
  ///
  Future<File?> _downloadFileTarget(FileList downloadTarget,
      CancelToken cancelToken, Function(double fraction) onProgress) async {
    final dlPath = join(_wlanDownloadDir, _getDownloadFilename(downloadTarget));
    final dlFile = File(dlPath);
    final tmpFile = File(dlPath + _downloadTmpSuffix);
    try {
      int downloadOffset = 0;
      int curDownloadSize = 0;
      int totalDownloadSize = _downloadStats[downloadTarget.filetype]!;
      _curDownloadCount++;

      if (dlFile.existsSync()) {
        curDownloadSize += dlFile.lengthSync();
        onProgress(curDownloadSize / totalDownloadSize);
        return dlFile;
      } else if (tmpFile.existsSync()) {
        int fileSize = tmpFile.lengthSync();
        if (fileSize < downloadTarget.fileSize) {
          downloadOffset = fileSize;
          curDownloadSize += fileSize;
          onProgress(curDownloadSize / totalDownloadSize);
        } else if (fileSize == downloadTarget.fileSize) {
          curDownloadSize += fileSize;
          onProgress(curDownloadSize / totalDownloadSize);
          if (await _verifyIntegrity(tmpFile, downloadTarget, cancelToken)) {
            tmpFile.renameSync(dlPath);
            NetworkLog.i("$dlPath is already downloaded!");
            return dlFile;
          } else {
            if (cancelToken.isCancelled == false) tmpFile.deleteSync();
            return null;
          }
        } else {
          tmpFile.deleteSync();
          NetworkLog.e("Illegal file size $fileSize of $dlPath");
          return null;
        }
      }

      final dlDir = dlFile.parent;
      if (dlDir.existsSync() == false) dlDir.createSync(recursive: true);
      bool downloaded = await _downloadFile(
          urlPath: downloadTarget.dlUrl,
          savePath: tmpFile.path,
          cancelToken: cancelToken,
          onReceiveProgress: (received) {
            onProgress((curDownloadSize + received) / totalDownloadSize);
          },
          rangeStart: downloadOffset);

      if (downloaded == false) return null;

      if (await _verifyIntegrity(tmpFile, downloadTarget, cancelToken) ==
          false) {
        NetworkLog.e("Failed to verify integrity of ${downloadTarget.dlUrl}");
        if (cancelToken.isCancelled == false) tmpFile.deleteSync();
        return null;
      } else {
        tmpFile.renameSync(dlPath);
      }
    } catch (e) {
      NetworkLog.e("Failed to download target ${downloadTarget.dlUrl}: $e");
      _rethrowIfMemoryFull(e);
      return null;
    }
    return dlFile;
  }

  ///
  /// ダウンロードするすべてのファイルとダウンロードサイズを初期化します
  ///
  bool _initDownloadStatistics(bool downloadVoiceOnly) {
    bool succeeded;
    try {
      if (downloadVoiceOnly == false) {
        _newUpgradeInfo!.fileList
            .where((element) => _hasNewVersion(element.filetype))
            .forEach((element) {
          _downloadStats[element.filetype] = element.fileSize;
        });
      } else {
        // no-op
      }

      bool shouldDownloadVoice;
      if (downloadVoiceOnly == false && _hasNewVoiceVersion(_newUpgradeInfo)) {
        downloadVoiceVersion = _newUpgradeInfo!.voiceVer;
        shouldDownloadVoice = true;
      } else {
        shouldDownloadVoice = downloadVoiceOnly;
      }

      if (shouldDownloadVoice) {
        final voiceDownloadSize = _getDownloadVoiceTotalSize();
        if (voiceDownloadSize <= 0) {
          succeeded = false;
        } else {
          _downloadStats[UpgradeFileType.voice] = voiceDownloadSize;
          succeeded = true;
        }
      } else {
        succeeded = true;
      }
    } catch (e) {
      NetworkLog.e("Failed to calculate total download size: $e");
      succeeded = false;
    }
    return succeeded;
  }

  ///
  /// Wlanダウンロードページにあるかどうか
  ///
  bool isWlanDownloading = false;

  ///
  /// 現在の機種コードを取得します
  ///
  String getMachineModelCode() =>
      DeviceLibrary().apiBinding.getDeviceSettingInfo().settingInfo.modeCode;

  ///
  /// 今回更新された朗読音のバージョン
  ///
  int downloadVoiceVersion = defaultVersion;

  ///
  /// 読み上げは、サーバーのディレクトリ名にあります
  ///
  final String _voice = "voice";

  ///
  /// ローカルの発音ファイルのパスをバックアップします
  ///
  final String _voiceBackup = "voice_backup";

  ///
  /// ダウンロードして保存した音声の発音を取得するためのパス
  ///
  String get voiceDownloadSavePath => join(_wlanDownloadDir, _voice);

  ///
  /// ダウンロードして保存した音声の発音を取得するためのパス
  ///
  String get currentVoiceDownloadSaveZipPath => join(
        voiceDownloadSavePath,
        "${MessageSoundModel().getCurrentVoiceDownloadFileName(downloadVoiceVersion)}.upf",
      );
  String get defaultVoiceDownloadSaveZipPath => join(
        voiceDownloadSavePath,
        "${MessageSoundModel().getDefaultVoiceDownloadFileName(downloadVoiceVersion)}.upf",
      );

  ///
  /// 音声ダウンロード情報を取得します
  ///
  /// [lang] 音声言語
  ///
  /// 戻り値：
  /// - 音声ダウンロード情報のMap, 見つからない場合はnull
  ///
  Map<String, dynamic>? _getVoiceDownloadInfo(MessageSoundLanguage lang) {
    final List<dynamic>? voiceInfoList = _voiceList["voicelist"];
    if (voiceInfoList == null) return null;

    final index = voiceInfoList
        .indexWhere((element) => element["kind_num"] == lang.index);
    if (index < 0) return null;

    return voiceInfoList[index];
  }

  ///
  /// 読み上げのダウンロードサイズを取得します
  ///
  int? _getVoiceDownloadSize(MessageSoundLanguage lang) =>
      _getVoiceDownloadInfo(lang)?["filesize"];

  ///
  /// 読み上げファイルの合計ダウンロードサイズを取得します
  ///
  int _getDownloadVoiceTotalSize() {
    int totalSize = 0;

    /// 現在選択されている読み上げとサンプルの読み上げのファイル サイズを取得します。
    int? size =
        _getVoiceDownloadSize(MessageSoundModel().currentSelectLanguage);
    if (size == null) return 0;
    totalSize += size;

    /// 現在の読み上げがデフォルトの読み上げであるかどうか
    if (_shouldDownloadDefaultVoice == false) {
      return totalSize;
    }

    size = _getVoiceDownloadSize(MessageSoundModel().defaultMessageSoundLang);
    if (size == null) return 0;
    totalSize += size;

    return totalSize;
  }

  ///
  /// ステータスコードが正常にダウンロードされました
  ///
  static const int _httpStatusCodeOK = 200;

  ///
  /// 目的の読み上げファイルファイルをダウンロードします
  ///
  Future<UpgradeInfo?> getUpgradeVersionInfoInServer() async {
    final Dio dio = Dio();
    try {
      final response = await dio.get(join(upgradeUrl, upgradeJsonName));

      /// サーバー情報の取得に成功しました
      if (response.statusCode == _httpStatusCodeOK) {
        /// サーバー データを解析します
        final updateInfos = getUpgradeInfoList(response.data);

        /// 現在の機種コードを取得します
        String machineModelCode = getMachineModelCode();

        /// 現在の機種コードに対応する情報を検索します
        int currentUpdateInfoIndex = updateInfos.indexWhere(
            (element) => machineModelCode.compareTo(element.modelCode) == 0);

        /// 機種コードが見つかりません
        if (currentUpdateInfoIndex < 0) {
          return null;
        }

        if (await _parseVoiceListJson() == false) return null;
        final voiceVer = _voiceList["voiceversion"];
        return updateInfos[currentUpdateInfoIndex]..voiceVer = voiceVer;
      } else {
        return null;
      }
    } catch (error) {
      NetworkLog.e("Failed to get upgrade version info in server: $error");
      return null;
    } finally {
      dio.close();
    }
  }

  ///
  /// サーバー上のアップグレード情報と音声バージョンを取得します。
  /// アップグレード情報の取得に失敗しても音声バージョンを取得します
  ///
  /// @see https://brothergroup.atlassian.net/browse/PHFIRMIIVO-7403
  ///
  /// - return: アップグレード情報と音声バージョン、
  ///           [null]を返す場合は音声バージョンの取得に失敗したことを意味します
  ///
  Future<(UpgradeInfo? serverUpgradeInfo, int serverVoiceVersion)?>
      getVoiceVersionInfoInServer() async {
    if (await _parseVoiceListJson() == false) {
      return null;
    }

    final int voiceVersionInServer;
    try {
      voiceVersionInServer = _voiceList["voiceversion"];
    } catch (e) {
      NetworkLog.e("Failed to get `voiceversion` field in voice list: $e");
      return null;
    }

    final Dio dio = Dio();
    try {
      final Response response =
          await dio.get(join(upgradeUrl, upgradeJsonName));

      final List<UpgradeInfo> updateInfos = getUpgradeInfoList(response.data);

      final String machineModelCode = getMachineModelCode();

      final int currentUpdateInfoIndex = updateInfos
          .indexWhere((element) => machineModelCode == element.modelCode);

      final UpgradeInfo? upgradeInfo;

      if (currentUpdateInfoIndex < 0) {
        upgradeInfo = null;
      } else {
        upgradeInfo = updateInfos[currentUpdateInfoIndex];
        upgradeInfo.voiceVer = voiceVersionInServer;
      }

      return (upgradeInfo, voiceVersionInServer);
    } catch (error) {
      NetworkLog.e("Failed to get upgrade version info in server: $error");
      return (null, voiceVersionInServer);
    } finally {
      dio.close();
    }
  }

  ///
  /// ローカル バージョン情報を取得します
  ///
  UpgradeInfo? _parseSavedUpgradeInfo() {
    try {
      /// ローカルバージョン情報ストレージファイルを開きます
      File localVersionJson = File(join(upgradeDir, _newUpgradeJsonName));
      if (localVersionJson.existsSync() == false) {
        return null;
      }
      return getUpgradeInfo(jsonDecode(localVersionJson.readAsStringSync()));
    } catch (e) {
      NetworkLog.e("Failed to parse $_newUpgradeJsonName");
      return null;
    }
  }

  ///
  /// 単一の音声アップデート ファイルをダウンロードする
  ///
  Future<(bool succeeded, int size)> _downloadVoiceFile(
      int currentVoiceDownloadSize,
      String dlUrl,
      String savePath,
      CancelToken cancelToken,
      Function(double fraction) onReceiveProgress) async {
    bool succeeded;
    final dlFile = File(savePath);
    final totalDownloadSize = _downloadStats[UpgradeFileType.voice]!;

    try {
      if (dlFile.existsSync()) {
        succeeded = true;
        currentVoiceDownloadSize += dlFile.lengthSync();
        onReceiveProgress(currentVoiceDownloadSize / totalDownloadSize);
      } else {
        final dlTmpFile = File("$savePath$_downloadTmpSuffix");
        int downloadOffset;
        if (dlTmpFile.existsSync()) {
          downloadOffset = dlTmpFile.lengthSync();
          currentVoiceDownloadSize += downloadOffset;
          onReceiveProgress(currentVoiceDownloadSize / totalDownloadSize);
        } else {
          downloadOffset = 0;
        }

        final dlDir = dlTmpFile.parent;
        if (dlDir.existsSync() == false) dlDir.create(recursive: true);

        int receivedSize = 0;
        succeeded = await _downloadFile(
            urlPath: dlUrl,
            savePath: dlTmpFile.path,
            cancelToken: cancelToken,
            onReceiveProgress: (r) {
              receivedSize = r;
              onReceiveProgress(
                  (currentVoiceDownloadSize + r) / totalDownloadSize);
            },
            rangeStart: downloadOffset);

        if (succeeded) {
          currentVoiceDownloadSize += receivedSize;
          dlTmpFile.renameSync(dlFile.path);
        } else {
          // no-op
        }
      }
    } catch (e) {
      succeeded = false;
      NetworkLog.e("Failed to download voice from $dlUrl to $savePath: $e");
      _rethrowIfMemoryFull(e);
    }
    return (succeeded, currentVoiceDownloadSize);
  }

  ///
  /// 音声ファイルをダウンロードする
  ///
  Future<bool> downloadVoice(
    CancelToken cancelToken,
    Function(double fraction) onReceiveProgress,
  ) async {
    bool succeeded;
    int currentVoiceDownloadSize = 0;

    _curDownloadCount++;

    /// 音声ダウンロード情報のキー
    const String voiceDownloadInfoKey = "dl_url";

    final currentLang = MessageSoundModel().currentSelectLanguage;

    /// 現在選択されている読み上げファイルのダウンロードURLを取得します
    String? downloadUrl =
        _getVoiceDownloadInfo(currentLang)?[voiceDownloadInfoKey];
    if (downloadUrl == null) return false;

    /// 現在選択されている読み上げファイルをダウンロードします
    (succeeded, currentVoiceDownloadSize) = await _downloadVoiceFile(
      currentVoiceDownloadSize,
      downloadUrl,
      currentVoiceDownloadSaveZipPath,
      cancelToken,
      onReceiveProgress,
    );
    if (succeeded == false) return false;
    if (await _verifyVoiceIntegrity(
            currentVoiceDownloadSaveZipPath, currentLang, cancelToken) ==
        false) {
      File(currentVoiceDownloadSaveZipPath).deleteSync();
      return false;
    }

    /// デフォルトの読み上げファイルをダウンロードします
    if (_shouldDownloadDefaultVoice == false) return true;

    final defaultLang = MessageSoundModel().defaultMessageSoundLang;

    /// デフォルトの読み上げファイルのダウンロードURLを取得します
    downloadUrl = _getVoiceDownloadInfo(defaultLang)?[voiceDownloadInfoKey];
    if (downloadUrl == null) return false;

    (succeeded, currentVoiceDownloadSize) = await _downloadVoiceFile(
      currentVoiceDownloadSize,
      downloadUrl,
      defaultVoiceDownloadSaveZipPath,
      cancelToken,
      onReceiveProgress,
    );

    if (succeeded == false) return false;
    if (await _verifyVoiceIntegrity(
            defaultVoiceDownloadSaveZipPath, defaultLang, cancelToken) ==
        false) {
      File(defaultVoiceDownloadSaveZipPath).deleteSync();
      return false;
    }

    return true;
  }

  ///
  /// 音声ファイルの整合性を検証します
  ///
  Future<bool> _verifyVoiceIntegrity(String filepath, MessageSoundLanguage lang,
      CancelToken cancelToken) async {
    try {
      final file = File(filepath);
      final voiceDownloadSize = _getVoiceDownloadSize(lang);
      if (voiceDownloadSize != file.lengthSync()) return false;

      final fileMd5 = _getVoiceDownloadInfo(lang)?["filemd5"];
      if (fileMd5 == null) return false;

      return _checkMD5(file, fileMd5, cancelToken);
    } catch (e) {
      NetworkLog.e("Failed to verify voice integrity: $e");
      return false;
    }
  }

  ///
  /// 更新する音声ファイルを UPF から目的の場所に抽出します。
  ///
  Future<bool> _extractVoiceFromUpf(String voiceUpfPath) async {
    bool succeeded;

    final ret = await UsbManagerPlatform.instance.extractDirFromUpf(
        upfFilepath: voiceUpfPath,
        outputDirPath: voiceDownloadSavePath,
        dirName: "",
        password: UPFVerifySignatureModel.password);
    succeeded = ret == true;

    final prefix = MessageSoundModel().currentVoiceFilePrefix;
    final voiceTmpDir = Directory(join(voiceDownloadSavePath, prefix));

    try {
      if (succeeded == false) {
        // ZIP ファイルが破損しているか、UPF 形式が正しくありません。削除してください
        File(voiceUpfPath).deleteSync();
      } else {
        for (var type in MessageSoundType.values) {
          final typeName = MessageSoundModel().getMessageSoundType(type);
          final srcDir = Directory(join(voiceTmpDir.path, typeName));
          if (srcDir.existsSync()) {
            final dstDir = Directory(join(
                memorySector.message_sound.absolutePath, typeName, prefix));
            if (dstDir.existsSync()) {
              dstDir.deleteSync(recursive: true);
            } else {
              dstDir.createSync(recursive: true);
            }
            srcDir.renameSync(dstDir.path);
          } else {
            // no-op
          }
        }
      }
    } finally {
      // 一時ディレクトリを削除します
      if (voiceTmpDir.existsSync()) voiceTmpDir.deleteSync(recursive: true);
    }
    return succeeded;
  }

  /// デフォルトの読み上げをダウンロードするかどうか
  bool get _shouldDownloadDefaultVoice =>
      MessageSoundModel().isSelectedDefaultLanguage == false &&
      _hasNewVoiceVersion(_newUpgradeInfo);

  ///
  /// すべての音声更新ファイルの署名を確認し、すべての音声更新ファイルを目標の場所に抽出する
  ///
  Future<bool> _verifyAndExtractAllVoices() async {
    bool succeeded;
    try {
      succeeded = await UPFVerifySignatureModel.verifyFileSignature(
          currentVoiceDownloadSaveZipPath);
      if (succeeded == false) {
        return false;
      }

      /// 現在の発音パッケージの解凍されたディレクトリを取得する
      succeeded = await _extractVoiceFromUpf(currentVoiceDownloadSaveZipPath);
      if (succeeded == false) {
        return false;
      }

      /// デフォルトの読み上げファイルをダウンロードします
      if (_shouldDownloadDefaultVoice == false) {
        return true;
      }

      succeeded = await UPFVerifySignatureModel.verifyFileSignature(
          defaultVoiceDownloadSaveZipPath);
      if (succeeded == false) {
        return false;
      }

      /// デフォルトの発音パッケージの解凍ディレクトリを取得する
      succeeded = await _extractVoiceFromUpf(defaultVoiceDownloadSaveZipPath);
    } catch (e) {
      NetworkLog.e("Failed to extract voice files: $e");
      succeeded = false;
    }
    return succeeded;
  }

  ///
  /// 指定した音声ディレクトリをバックアップします
  ///
  /// @see [Upgrade.backupVoice]
  ///
  void _backupVoiceDir(String dirName, String langName) {
    final Directory src = Directory(
        join(memorySector.message_sound.absolutePath, dirName, langName));
    if (src.existsSync()) {
      final Directory dst = Directory(join(
          memorySector.work.absolutePath, _voiceBackup, dirName, langName));
      if (dst.existsSync()) {
        dst.deleteSync(recursive: true);
      } else {
        dst.createSync(recursive: true);
      }
      src.rename(dst.path);
    } else {
      // バックアップディレクトリが存在しない場合はスキップします
    }
  }

  ///
  /// 現在選択されている言語とデフォルト言語（オプション）の言語ファイルをバックアップする
  ///
  /// - param:
  ///   - [backupDefaultVoice] デフォルト言語の音声ファイルをバックアップするかどうか
  ///
  /// @see [Upgrade._restoreVoiceBackupIfAvailable]
  ///
  Future<bool> backupVoice({required bool backupDefaultVoice}) async {
    try {
      /// デフォルト音声が選択され、デフォルト音声のバックアップが不要な場合は
      if (backupDefaultVoice == false &&
          MessageSoundModel().isSelectedDefaultLanguage) {
        return true;
      }

      for (final MessageSoundType type in MessageSoundType.values) {
        final String typeName = MessageSoundModel().getMessageSoundType(type);
        _backupVoiceDir(typeName, MessageSoundModel().currentVoiceFilePrefix);
        if (backupDefaultVoice == false) {
          continue;
        }
        _backupVoiceDir(typeName, MessageSoundModel().defaultVoiceFilePrefix);
      }
    } catch (e) {
      NetworkLog.e("Failed to backup voice: $e");
      return false;
    }
    return true;
  }

  ///
  /// 指定した音声ディレクトリのバックアップを復元します
  ///
  /// @see [Upgrade._restoreVoiceBackupIfAvailable]
  ///
  void _restoreVoiceDir(String dirName, String langName) {
    final Directory dst = Directory(
        join(memorySector.work.absolutePath, _voiceBackup, dirName, langName));
    if (dst.existsSync()) {
      final Directory src = Directory(
          join(memorySector.message_sound.absolutePath, dirName, langName));
      if (src.existsSync()) {
        src.deleteSync(recursive: true);
      } else {
        src.createSync(recursive: true);
      }
      dst.renameSync(src.path);
    } else {
      // バックアップディレクトリが存在しない場合はスキップします
    }
  }

  ///
  /// バックアップしたオーディオファイルを復元する
  ///
  /// 選択された言語とデフォルト言語の音声ファイルを復元します（バックアップが存在する場合）
  ///
  /// @see [Upgrade.backupVoice]
  /// @see [Upgrade.init]
  ///
  Future<void> _restoreVoiceBackupIfAvailable() async {
    try {
      for (final MessageSoundType type in MessageSoundType.values) {
        final String typeName = MessageSoundModel().getMessageSoundType(type);
        _restoreVoiceDir(typeName, MessageSoundModel().defaultVoiceFilePrefix);
        if (MessageSoundModel().isSelectedDefaultLanguage) {
          continue;
        }
        _restoreVoiceDir(typeName, MessageSoundModel().currentVoiceFilePrefix);
      }
      _deleteVoiceBackup();
    } catch (e) {
      NetworkLog.e("Failed to restore voice backup: $e");
    }
  }

  ///
  /// バックアップした発音ファイルをクリーンアップする
  ///
  bool _deleteVoiceBackup() {
    try {
      Directory directory =
          Directory(join(memorySector.work.absolutePath, _voiceBackup));
      if (directory.existsSync()) {
        directory.deleteSync(recursive: true);
      } else {
        /// Do noting
      }
    } catch (e) {
      NetworkLog.e("Failed to delete voice backup: $e");
      return false;
    }
    return true;
  }

  ///
  /// 読み上げのダウンロードファイルディレクトリを削除します
  ///
  void deleteVoiceDownloadSaveFolder() {
    try {
      Directory directory = Directory(voiceDownloadSavePath);
      if (directory.existsSync()) {
        directory.deleteSync(recursive: true);
      } else {
        /// Do noting
      }
    } catch (error, message) {
      NetworkLog.exception(error, message);
    }
  }

  ///
  /// 音声ファイルを更新する。
  ///
  /// [fromVoiceGuidanceChangePopup] 音声ガイダンス変更ポップアップからの更新かどうか。
  /// trueの場合、音声バージョンの更新は不要です。
  ///
  Future<bool> updateVoice(bool fromVoiceGuidanceChangePopup) async {
    bool succeeded;
    if (await backupVoice(backupDefaultVoice: _shouldDownloadDefaultVoice)) {
      if (await _verifyAndExtractAllVoices()) {
        if (fromVoiceGuidanceChangePopup ||
            await _updateVersion(UpgradeFileType.voice)) {
          succeeded = _deleteVoiceBackup();
        } else {
          _restoreVoiceBackupIfAvailable();
          succeeded = false;
        }
      } else {
        _restoreVoiceBackupIfAvailable();
        succeeded = false;
      }
    } else {
      succeeded = false;
    }
    if (succeeded) _deleteVoiceDownloadCache();
    return succeeded;
  }

  ///
  /// 音声ダウンロードキャッシュを削除します
  ///
  void _deleteVoiceDownloadCache() {
    try {
      final cacheDir = Directory(voiceDownloadSavePath);
      if (cacheDir.existsSync()) {
        cacheDir.deleteSync(recursive: true);
      }
    } catch (e) {
      NetworkLog.e("Failed to delete voice download cache: $e");
    }
  }

  ///
  /// 空の [UpgradeInfo] を作成します
  ///
  UpgradeInfo _createEmptyUpgradeInfo() {
    // ダミー値
    const int dummy = -1;
    return UpgradeInfo(
        getMachineModelCode(), defaultVersion, dummy, _placeholder, []);
  }

  ///
  /// 指定した接続の内容を指定したファイルにダウンロードします
  ///
  /// 引数[in]:
  /// - [String]        url                 :ダウンロード先のアドレス
  /// - [String]        path                :ダウンロードしたコンテンツがローカルに保存されているアドレス(ファイル名をパスに含める必要があります)
  /// - [CancelToken]   cancelToken         :ダウンロードでダウンロードを取り消すために使用されます。
  /// - [Function]      onReceiveProgress   :進行状況の更新をダウンロードするときのコールバック関数。(通常、プログレスバーを更新するために使用されます)
  ///
  ///
  static Future<bool> _downloadFileToUserLocal(
      {required String urlPath,
      required String savePath,
      CancelToken? cancelToken,
      Function(int received, int total)? onReceiveProgress}) async {
    final Dio dio = Dio();

    /// 接続タイムアウトを 10 秒に、読み取りタイムアウトを 30 秒に設定します
    dio.options.connectTimeout = const Duration(seconds: 10);
    dio.options.receiveTimeout = const Duration(seconds: 30);
    try {
      var response = await dio.download(urlPath, savePath,
          onReceiveProgress: onReceiveProgress,
          cancelToken: cancelToken,
          deleteOnError: false);

      /// ダウンロードが成功したかどうか
      if (response.statusCode == _httpStatusCodeOK) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      NetworkLog.e("Failed to download file from $urlPath to $savePath: $e");
      if (e is DioException) {
        _rethrowIfMemoryFull(e.error);
      } else {
        _rethrowIfMemoryFull(e);
      }
      return false;
    } finally {
      dio.close();
    }
  }

  ///
  /// ブレークポイントでダウンロードを再開します。
  /// [rangeStart] は、ダウンロードされるファイルのサイズを示します。
  ///
  static Future<bool> _downloadFileWithOffset(
      {required String urlPath,
      required String savePath,
      CancelToken? cancelToken,
      Function(int received)? onReceiveProgress,
      required int rangeStart}) async {
    bool succeeded = false;
    final Dio dio = Dio();
    try {
      final response = await dio.get<ResponseBody>(
        urlPath,
        options: Options(
          responseType: ResponseType.stream,
          followRedirects: false,
          headers: {
            // Range リクエスト ヘッダーを追加してブレークポイントの送信再開を実現します
            "Range": "bytes=$rangeStart-",
          },
        ),
      );
      File file = File(savePath);
      RandomAccessFile raf = file.openSync(mode: FileMode.append);

      int received = 0;
      Stream<Uint8List> stream = response.data!.stream;
      StreamSubscription<Uint8List>? subscription;

      final c = Completer();
      subscription = stream.listen(
        (data) {
          try {
            raf.writeFromSync(data);
            received += data.length;
            onReceiveProgress?.call(received);
          } catch (e) {
            subscription?.cancel();
            raf.close();
            c.completeError(e);
          }
        },
        onDone: () async {
          await raf.close();
          succeeded = true;
          c.complete();
        },
        onError: (e) async {
          await raf.close();
          c.completeError(e);
        },
        cancelOnError: true,
      );
      cancelToken?.whenCancel.then((_) async {
        await subscription?.cancel();
        await raf.close();
        c.complete();
      });
      await c.future;
    } catch (e) {
      NetworkLog.e("Failed to download $urlPath: $e");
      if (e is DioException) {
        _rethrowIfMemoryFull(e.error);
      } else {
        _rethrowIfMemoryFull(e);
      }
      return false;
    } finally {
      dio.close();
    }
    return succeeded;
  }

  ///
  /// ファイルをローカルエリアにダウンロード、
  /// 最初からのダウンロードと再開可能なダウンロードをサポート。
  ///
  static Future<bool> _downloadFile(
      {required String urlPath,
      required String savePath,
      CancelToken? cancelToken,
      Function(int received)? onReceiveProgress,
      int rangeStart = 0}) {
    if (rangeStart <= 0) {
      return _downloadFileToUserLocal(
          urlPath: urlPath,
          savePath: savePath,
          cancelToken: cancelToken,
          onReceiveProgress: (r, _) => onReceiveProgress?.call(r));
    } else {
      return _downloadFileWithOffset(
          urlPath: urlPath,
          savePath: savePath,
          rangeStart: rangeStart,
          cancelToken: cancelToken,
          onReceiveProgress: onReceiveProgress);
    }
  }

  ///
  /// VerUpポップアップのタイプを取得する
  ///
  VerUpPopType getVerUpPopupType() {
    if (isShowWlanUpdatePopup()) {
      return VerUpPopType.wlanOn;
    } else {
      return VerUpPopType.none;
    }
  }

  ///
  /// WLANがオフのときに起動カウントをリセットする
  ///
  static void resetWlanOffStartUpCount() =>
      DeviceLibrary().apiBinding.resetWlanOffStartUpCount();

  ///
  /// [e]がメモリ不足例外である場合、スローする。
  ///
  static void _rethrowIfMemoryFull(Object? e) {
    if (e != null && isMemoryFullException(e)) {
      throw e;
    }
  }
}
