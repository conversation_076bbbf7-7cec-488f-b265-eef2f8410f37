import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../iivo.dart';
import '../../../../../../../model/out_key_lock_model.dart';
import '../../../../../home/<USER>/component/home_header/bwd_popup/bwd_popup_view_model.dart';
import 'stop_sewing.dart';
import 'stop_sewing_view_interface.dart';

/// パターンのモード
///
/// - utility：一般的なオプション
/// - stitchRegulator: SR専用モード
/// - custom: マイイラスト
enum StopSewingPopupType {
  general,
  characterEditMenu,
  stitchRegulator,
  custom,
}

final stopSewingPopupViewModelProvider = StateNotifierProvider<
    StopSewingPopupStateViewModelInterface,
    StopSewingPopupState>((ref) => StopSewingPopupViewModel(ref));

class StopSewingPopupViewModel extends StopSewingPopupStateViewModelInterface {
  StopSewingPopupViewModel(Ref ref) : super(const StopSewingPopupState(), ref) {
    update();
  }

  @override
  void update() {
    state =
        state.copyWith(isScreenLocked: OutKeyLockModel().isScreenLock == true);
  }

  @override
  void onScreenClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.invalid);
    TpdLibrary().apiBinding.displayTouchEvent();
  }

  OverlayEntry? _stopSewingPopup;

  @override
  void openStopSewingPopup(
      {bool isAllClickDisable = true,
      StopSewingPopupType mode = StopSewingPopupType.general}) {
    update();

    /// 縫製を停止するマスクは、BWDの下にある必要があります
    OverlayEntry? bwdPopup;
    if (ref.exists(bwdPopupViewModelProvider)) {
      /// BWD が存在する場合は、BWD のマスクを取得します
      bwdPopup = ref
          .read(bwdPopupViewModelProvider.notifier)
          .getBwdPopupOverlayEntry();
    } else {
      ///do nothing
    }
    _stopSewingPopup = OverlayEntry(
      builder: (_) => StopSewing(
        isAllClickDisable: isAllClickDisable,
        mode: mode,
      ),
    );
    if (_stopSewingPopup != null) {
      globalPopupNavigatorKey.currentState?.overlay
          ?.insert(_stopSewingPopup!, below: bwdPopup);
    }
  }

  @override
  void closeStopSewingPopup() {
    _stopSewingPopup?.remove();
    _stopSewingPopup?.dispose();
    _stopSewingPopup = null;
  }
}
