import 'dart:ui';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/paint/edit_object_model.dart';
import '../../../../../../model/paint/pen_model.dart';
import '../../../../../../model/resume_history_model.dart';
import 'move_state.dart';

/// 矢印の方向
enum Arrow {
  leftUp,
  up,
  rightUp,
  left,
  center,
  right,
  leftDown,
  down,
  rightDown,
}

final moveViewModelProvider =
    StateNotifierProvider.autoDispose<MoveViewModel, MoveState>(
        (ref) => MoveViewModel());

class MoveViewModel extends ViewModel<MoveState> {
  MoveViewModel() : super(const MoveState());

  /// 移動量：0.27mm
  double _positionStep = _oneStep;

  /// 移動の最大オフセット：10.0mm
  static const double _maxPositionStep = 10.0;

  /// 移動フセット：0.27mm
  static const double _oneStep = 0.27;

  /// 移動方向表示用の画像数量
  final int arrowImageSize = 9;

  /// 矢印の方向
  Arrow _arrowType = Arrow.center;

  ///
  /// 方向移動ボタンのクリック関数
  ///
  bool onArrowButtonClick(Arrow arrowType, bool isLongPress) {
    if (EditObjectModel().hasPartsImageInfo() == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _arrowType = arrowType;
    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      _positionStep = _oneStep;
    } else {
      /// 移動のオフセット計算
      if (_positionStep < _maxPositionStep) {
        _positionStep += _oneStep;
      }
    }

    /// 中心キーを押した場合、元の位置に戻す
    if (arrowType == Arrow.center) {
      return false;
    }

    /// 部分模様はお絵描きエリア以外
    bool isInTheArea = _isPartRectInCanvas(arrowType);
    if (!isInTheArea) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    final MdcUnsettledObjectInfo posInfo = EditObjectModel().objectInfo;
    final MdcUnsettledObjectInfo originPosInfo =
        EditObjectModel().originObjectInfo;
    final posX = posInfo.centerPosX * PenModel.ratio;
    final posY = posInfo.centerPosY * PenModel.ratio;
    final posCenter = MdcPoint(x: posX, y: posY);
    final originX = originPosInfo.centerPosX * PenModel.ratio;
    final originY = originPosInfo.centerPosY * PenModel.ratio;
    final originCenter = MdcPoint(x: originX, y: originY);

    /// 中心点計算
    final MdcPoint point = _getCenterPointAfterMove(
      posCenter,
      originCenter,
      _positionStep,
      arrowType,
    );
    EditObjectModel().objectInfo = EditObjectModel().objectInfo.copyWith(
          centerPosX: point.x ~/ PenModel.ratio,
          centerPosY: point.y ~/ PenModel.ratio,
        );

    /// 部分模様表示位置更新
    final prePartImage = EditObjectModel().getMdcPartsImageInfo();
    final newPartRect = Rect.fromCenter(
        center: Offset(
          EditObjectModel().objectInfo.centerPosX.toDouble(),
          EditObjectModel().objectInfo.centerPosY.toDouble(),
        ),
        width: prePartImage.imageWidth.toDouble(),
        height: prePartImage.imageHeight.toDouble());
    final newPartImage = MdcImageInfo(
        startPointX: newPartRect.left.toInt(),
        startPointY: newPartRect.top.toInt(),
        imageWidth: prePartImage.imageWidth,
        imageHeight: prePartImage.imageHeight,
        imageSize: prePartImage.imageSize,
        imageData: prePartImage.imageData);
    EditObjectModel().setMdcPartsImageInfo(newPartImage, needUpdateUI: true);
    return true;
  }

  ///
  /// タイマーを消します
  ///
  void timerCancel() {
    if (_arrowType == Arrow.center) {
      final errorCode = EditObjectModel().moveResetUnsettledObject();
      if (errorCode == MdcLibraryError.mdcNoError) {
        ResumeHistoryModel().backSnapshot();
      } else {
        /// Do Nothing
      }
      return;
    }

    /// Model更新
    final centerPosX = EditObjectModel().objectInfo.centerPosX;
    final centerPosY = EditObjectModel().objectInfo.centerPosY;
    final errorCode =
        EditObjectModel().moveUnsettledObject(centerPosX, centerPosY);
    if (errorCode == MdcLibraryError.mdcNoError) {
      ResumeHistoryModel().backSnapshot();
    } else {
      /// Do Nothing
    }
  }

  ///
  /// 移動後の位置取得
  ///
  /// @param
  /// - point: 現在の中心位置
  /// - originPoint: 初期中心位置
  /// - step: オフセット
  /// - arrow: 矢印の方向
  ///
  MdcPoint _getCenterPointAfterMove(
    MdcPoint point,
    MdcPoint originPoint,
    double step,
    Arrow arrow,
  ) {
    double x = point.x;
    double y = point.y;
    switch (arrow) {
      case Arrow.leftUp:
        x -= step;
        y -= step;
        break;
      case Arrow.up:
        x = x;
        y -= step;
        break;
      case Arrow.rightUp:
        x += step;
        y -= step;
        break;
      case Arrow.left:
        x -= step;
        y = y;
        break;
      case Arrow.center:
        x = originPoint.x;
        y = originPoint.y;
        break;
      case Arrow.right:
        x += step;
        y = y;
        break;
      case Arrow.leftDown:
        x -= step;
        y += step;
        break;
      case Arrow.down:
        x = x;
        y += step;
        break;
      case Arrow.rightDown:
        x += step;
        y += step;
        break;
    }

    return MdcPoint(x: x, y: y);
  }

  ///
  /// 移動範囲の判断
  ///
  bool _isPartRectInCanvas(Arrow arrowType) {
    final centerPosX = EditObjectModel().objectInfo.centerPosX;
    final centerPosY = EditObjectModel().objectInfo.centerPosY;

    bool needCheck = false;
    switch (arrowType) {
      case Arrow.leftUp:
        if (centerPosX < 0 || centerPosY < 0) {
          needCheck = true;
        }
        break;
      case Arrow.up:
        if (centerPosY < 0) {
          needCheck = true;
        }
        break;
      case Arrow.rightUp:
        if (centerPosX > PenModel.baseImageWidth || centerPosY < 0) {
          needCheck = true;
        }
        break;
      case Arrow.left:
        if (centerPosX < 0) {
          needCheck = true;
        }
        break;
      case Arrow.right:
        if (centerPosX > PenModel.baseImageWidth) {
          needCheck = true;
        }
        break;
      case Arrow.leftDown:
        if (centerPosX < 0 || centerPosY > PenModel.baseImageHeight) {
          needCheck = true;
        }
        break;
      case Arrow.down:
        if (centerPosY > PenModel.baseImageHeight) {
          needCheck = true;
        }
        break;
      case Arrow.rightDown:
        if (centerPosX > PenModel.baseImageWidth ||
            centerPosY > PenModel.baseImageHeight) {
          needCheck = true;
        }
        break;
      default:
        break;
    }

    /// エリアチェック必要ない
    if (!needCheck) {
      return true;
    }

    /// エリアチェック
    const Rect canvasRect =
        Rect.fromLTWH(0, 0, PenModel.baseImageWidth, PenModel.baseImageHeight);

    final object = EditObjectModel().objectInfo;
    final partImage = EditObjectModel().getMdcPartsImageInfo();
    final topLeft = Offset(
        partImage.startPointX + object.rectanglePntX[0].toDouble(),
        partImage.startPointY + object.rectanglePntY[0].toDouble());
    final bottomLeft = Offset(
        partImage.startPointX + object.rectanglePntX[1].toDouble(),
        partImage.startPointY + object.rectanglePntY[1].toDouble());
    final bottomRight = Offset(
        partImage.startPointX + object.rectanglePntX[2].toDouble(),
        partImage.startPointY + object.rectanglePntY[2].toDouble());
    final topRight = Offset(
        partImage.startPointX + object.rectanglePntX[3].toDouble(),
        partImage.startPointY + object.rectanglePntY[3].toDouble());

    if (!canvasRect.contains(topLeft) &&
        !canvasRect.contains(topRight) &&
        !canvasRect.contains(bottomLeft) &&
        !canvasRect.contains(bottomRight)) {
      return false;
    }

    return true;
  }
}
