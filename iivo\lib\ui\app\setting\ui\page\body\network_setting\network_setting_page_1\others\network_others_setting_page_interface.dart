import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'network_others_setting_page_interface.freezed.dart';

@freezed
class NetworkOthersSettingPageState with _$NetworkOthersSettingPageState {
  const factory NetworkOthersSettingPageState(
          {@Default(defaultMacAddress) String macAddress}) =
      _NetworkOthersSettingPageState;

  static const String defaultMacAddress = "00-00-00-00-00-00";
}

abstract class NetworkOthersSettingPageInterface
    extends ViewModel<NetworkOthersSettingPageState> {
  NetworkOthersSettingPageInterface(super.state);

  ///
  /// 【Return】ボタンのクリックイベント
  ///
  void onReturnButtonClicked(BuildContext context);

  ///
  /// 【Network Diagnosis Tool】の【Start】ボタンのクリックイベント
  ///
  void onNetworkDiagnosisToolStartButtonClicked(BuildContext context);
}
