// ignore_for_file: camel_case_types

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';

import '../../xd_component.dart';

enum FlameThumbnailType {
  size00,
  size01,
  size02,
  size03,
}

enum FavoriteButtonType {
  select,
  unSelect,
}

class pre_multi_category_preview extends StatelessWidget {
  const pre_multi_category_preview({
    super.key,
    this.patternWidth = "",
    this.patternHeight = "",
    this.colorNumber = "",
    this.colorChange = "",
    this.stitchTime = "",
    this.flameThumbnail = FlameThumbnailType.size03,
    this.favoriteButtonStyle = FavoriteButtonType.unSelect,
    this.onCloseButtonTap,
    this.onFavoriteButtonTap,
    required this.preview,
  });

  final String patternWidth;
  final String patternHeight;
  final String colorNumber;
  final String colorChange;
  final String stitchTime;
  final FlameThumbnailType flameThumbnail;
  final FavoriteButtonType favoriteButtonStyle;
  final void Function()? onCloseButtonTap;
  final void Function()? onFavoriteButtonTap;
  final Widget preview;

  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    return SizedBox(
      width: 770,
      height: 438,
      child: Row(
        children: [
          Expanded(
            flex: 699,
            child: Stack(
              children: [
                const pre_square_base(),
                MultiTouchBlocker(
                  child: Row(
                    children: [
                      const Spacer(flex: 292),
                      Expanded(
                        flex: 383,
                        child: Column(
                          children: [
                            const Spacer(flex: 80),
                            Expanded(
                              flex: 63,
                              child: Row(
                                children: [
                                  const Spacer(flex: 1),
                                  Expanded(
                                    flex: 119,
                                    child: [
                                      const ico_thumnail_size00(),
                                      const ico_thumnail_size01(),
                                      const ico_thumnail_size02(),
                                      const ico_thumnail_size03()
                                    ][flameThumbnail.index],
                                  ),
                                  const Spacer(flex: 263),
                                ],
                              ),
                            ),
                            const Spacer(flex: 12),
                            Expanded(
                              flex: 85,
                              child: Row(
                                children: [
                                  const Spacer(flex: 1),
                                  Expanded(
                                    flex: 161,
                                    child: Column(
                                      children: [
                                        Expanded(
                                          flex: 34,
                                          child: grp_str_text_size(
                                            text: l10n.icon_00533,
                                          ),
                                        ),
                                        const Spacer(flex: 51),
                                      ],
                                    ),
                                  ),
                                  const Spacer(flex: 33),
                                  const Expanded(
                                    flex: 36,
                                    child: Column(
                                      children: [
                                        Expanded(
                                          flex: 36,
                                          child: icon_size_height(),
                                        ),
                                        Spacer(flex: 3),
                                        Expanded(
                                          flex: 36,
                                          child: icon_size_width(),
                                        ),
                                        Spacer(flex: 11),
                                      ],
                                    ),
                                  ),
                                  const Spacer(flex: 4),
                                  Expanded(
                                    flex: 148,
                                    child: Column(
                                      children: [
                                        Expanded(
                                          flex: 34,
                                          child: grp_str_text_number1(
                                            text: patternHeight,
                                          ),
                                        ),
                                        const Spacer(flex: 5),
                                        Expanded(
                                          flex: 34,
                                          child: grp_str_text_number2(
                                            text: patternWidth,
                                          ),
                                        ),
                                        const Spacer(flex: 12),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Column(
                                children: [
                                  const Expanded(
                                    flex: 1,
                                    child: SizedBox(
                                      height: 0.5,
                                    ),
                                  ),
                                  Expanded(
                                    flex: 1,
                                    child: Container(
                                      height: 0.5,
                                      color: const Color(0xff000000),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Spacer(flex: 12),
                            Expanded(
                              flex: 34,
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: 289,
                                    child: grp_str_text_numcolor(
                                      text: l10n.icon_00058_1,
                                    ),
                                  ),
                                  const Spacer(flex: 34),
                                  Expanded(
                                    flex: 60,
                                    child: grp_str_text_number3(
                                      text: colorNumber,
                                    ),
                                  )
                                ],
                              ),
                            ),
                            const Spacer(flex: 12),
                            Expanded(
                              flex: 1,
                              child: Column(
                                children: [
                                  const Expanded(
                                    flex: 1,
                                    child: SizedBox(
                                      height: 0.5,
                                    ),
                                  ),
                                  Expanded(
                                    flex: 1,
                                    child: Container(
                                      height: 0.5,
                                      color: const Color(0xff000000),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Spacer(flex: 12),
                            Expanded(
                              flex: 34,
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: 289,
                                    child: grp_str_text_numcolor(
                                      text: l10n.icon_colorchanges,
                                    ),
                                  ),
                                  const Spacer(flex: 34),
                                  Expanded(
                                    flex: 60,
                                    child: grp_str_text_number4(
                                      text: colorChange,
                                    ),
                                  )
                                ],
                              ),
                            ),
                            const Spacer(flex: 12),
                            Expanded(
                              flex: 1,
                              child: Column(
                                children: [
                                  const Expanded(
                                    flex: 1,
                                    child: SizedBox(
                                      width: 382.5,
                                      height: 0.5,
                                    ),
                                  ),
                                  Expanded(
                                    flex: 1,
                                    child: Container(
                                      height: 0.5,
                                      color: const Color(0xff000000),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Spacer(flex: 12),
                            Expanded(
                              flex: 34,
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: 198,
                                    child: grp_str_text_time(
                                      text: l10n.icon_00266,
                                    ),
                                  ),
                                  const Spacer(flex: 30),
                                  Expanded(
                                    flex: 127,
                                    child: grp_str_text_number5(
                                      text: stitchTime,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Spacer(flex: 36),
                          ],
                        ),
                      ),
                      const Spacer(flex: 24),
                    ],
                  ),
                ),
                Row(
                  children: [
                    const Spacer(flex: 16),
                    Expanded(
                      flex: 260,
                      child: Column(
                        children: [
                          const Spacer(flex: 15),
                          Expanded(
                            flex: 407,
                            child: Stack(
                              children: [
                                const pic_emb_preview(),
                                preview,
                              ],
                            ),
                          ),
                          const Spacer(flex: 16),
                        ],
                      ),
                    ),
                    const Spacer(flex: 423),
                  ],
                ),
              ],
            ),
          ),
          const Spacer(flex: 71),
        ],
      ),
    );
  }
}
