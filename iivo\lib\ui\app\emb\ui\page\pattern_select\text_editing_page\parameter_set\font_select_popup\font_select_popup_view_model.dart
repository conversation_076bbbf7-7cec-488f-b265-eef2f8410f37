import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../../global_popup/global_popups/err_emb_too_much_selected_go_menu/err_emb_too_much_selected_go_menu_view_model.dart';
import '../../../../../../../../global_popup/global_popups/err_no_chg_font/err_no_chg_font_view_model.dart';
import '../../../../../../../../global_popup/global_popups/err_outside_of_emb_frm_nouse/err_outside_of_emb_frm_nouse_view_model.dart';
import '../../../../../../model/key_board_font_model.dart';
import '../../../../../../model/pattern_data_reader/character_font_image_reader.dart';
import '../../text_editing_page_view_model.dart';
import 'font_select_popup_view_interface.dart';

final fontSelectPopupViewModelProvider = StateNotifierProvider.autoDispose<
    FontSelectPopupViewModelInterface,
    FontSelectPopupState>((ref) => FontSelectPopupViewModel(ref));

class FontSelectPopupViewModel extends FontSelectPopupViewModelInterface {
  FontSelectPopupViewModel(Ref ref) : super(const FontSelectPopupState(), ref) {
    update();
  }

  @override
  void update() {
    state = state.copyWith(
      hasExclusiveScriptFont: _hasExclusiveScriptFont(),
    );
  }

  @override
  List<CharacterFontImageGroup> getAllCharacterFontImagesInfo() {
    List<CharacterFontImageGroup> infos =
        CharacterFontImageReader().getAllCharacterFontImagesInfo();
    if (KeyBoardFontModel().selectedFontType ==
        KeyBoardFontModel.emcTypeSmall) {
      infos = CharacterFontImageReader()
          .getAllCharacterFontImagesInfo()
          .where((element) => element.type == KeyBoardFontModel.emcTypeSmall)
          .toList();
    } else {
      infos = CharacterFontImageReader()
          .getAllCharacterFontImagesInfo()
          .where((element) => element.type != KeyBoardFontModel.emcTypeSmall)
          .toList();
    }

    return infos;
  }

  @override
  Widget getCharacterFontImage(int index) {
    final int fontIndex = state.hasExclusiveScriptFont ? index + 1 : index;
    if (fontIndex < 0) {
      Log.assertTrace("fontIndexの値は0以上でなければなりません");
      return getAllCharacterFontImagesInfo().first.image;
    }

    return getAllCharacterFontImagesInfo()[fontIndex].image;
  }

  @override
  String getCharacterFontName(int index) {
    final int fontIndex = state.hasExclusiveScriptFont ? index + 1 : index;
    if (fontIndex < 0) {
      Log.assertTrace("fontIndexの値は0以上でなければなりません");
      return getAllCharacterFontImagesInfo().first.name;
    }

    return getAllCharacterFontImagesInfo()[fontIndex].name;
  }

  @override
  void onItemButtonClicked(int index) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    final int fontIndex = state.hasExclusiveScriptFont ? index + 1 : index;
    if (fontIndex < 0) {
      Log.assertTrace("fontIndexの値は0以上でなければなりません");
      return;
    }

    String fontName = "", fontNumber = "", selectedFontType = "";
    fontName = getAllCharacterFontImagesInfo()[fontIndex].name;
    fontNumber = getAllCharacterFontImagesInfo()[fontIndex].fontNumber;
    selectedFontType = getAllCharacterFontImagesInfo()[fontIndex].type;

    EmbCharLibraryError error =
        KeyBoardFontModel().changeFont(fontName, fontNumber, selectedFontType);

    if (error == EmbCharLibraryError.EMB_AREA_OVER_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_OUTSIDE_OF_EMB_FRM_NOUSE,
          arguments: ErrOutsideOfEmbFrmNouseArgument(
            onOKButtonClicked: () {
              DeviceErrorCode deviceError =
                  GlobalPopupRoute().resetErrorState();

              if (deviceError != DeviceErrorCode.devNoError) {
                SystemSoundPlayer().play(SystemSoundEnum.invalid);
                return;
              }

              /// 他の画面を更新する
              ref
                  .read(textEditingPageViewModelProvider.notifier)
                  .updateTextEditingPageByChild(TextEditingType.fontPopup);
            },
          ));
      return;
    } else if (error == EmbCharLibraryError.EMB_OTHER_ERR) {
      /// ERR_NO_CHG_FONTのokボタン関数
      errNoChgFontFunc = () {
        DeviceErrorCode deviceError = GlobalPopupRoute().resetErrorState();

        if (deviceError != DeviceErrorCode.devNoError) {
          return;
        }

        /// 他の画面を更新する
        ref
            .read(textEditingPageViewModelProvider.notifier)
            .updateTextEditingPageByChild(TextEditingType.fontPopup);
      };

      GlobalPopupRoute()
          .updateErrorState(nextRoute: GlobalPopupRouteEnum.ERR_NO_CHG_FONT);
      return;
    } else if (error == EmbCharLibraryError.EMB_DATAOVER_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
          arguments: ErrEmbTooMuchSelectedGoMenuArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
      return;
    }

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.fontPopup);
  }

  @override
  void onOkButtonClicked(BuildContext context) {
    scrollController.dispose();
    PopupNavigator.pop(context: context);
  }

  @override
  int getItemCount() {
    List<CharacterFontImageGroup> allCharacterFontImagesInfo =
        List.from(getAllCharacterFontImagesInfo());

    ///　exclusiveScriptを消します
    allCharacterFontImagesInfo.removeWhere((element) =>
        element.fontNumber == KeyBoardFontModel.exclusiveScriptFontNumber);

    return allCharacterFontImagesInfo.length;
  }

  ///　exclusiveScript フォントがあるかどうか
  bool _hasExclusiveScriptFont() {
    /// データに特別なフォント(exclusiveScript)が含まれているかどうかを判断します。
    final bool hasExclusiveScriptFont = CharacterFontImageReader()
        .getAllCharacterFontImagesInfo()
        .any((element) =>
            element.fontNumber == KeyBoardFontModel.exclusiveScriptFontNumber);

    /// 小さいフォントが選択されているかどうかを判断する
    final bool isSelectSmallFont =
        KeyBoardFontModel().selectedFontType == KeyBoardFontModel.emcTypeSmall;

    return isSelectSmallFont == false && hasExclusiveScriptFont;
  }
}
