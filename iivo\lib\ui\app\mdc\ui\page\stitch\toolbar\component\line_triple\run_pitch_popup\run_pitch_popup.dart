import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'run_pitch_popup_view_model.dart';

class RunPitchPopup extends ConsumerStatefulWidget {
  const RunPitchPopup({Key? key}) : super(key: key);

  @override
  ConsumerState<RunPitchPopup> createState() => _RunPitchPopupPopupState();
}

class _RunPitchPopupPopupState extends ConsumerState<RunPitchPopup> {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(runPitchPopupViewModelProvider);
    final viewModel = ref.read(runPitchPopupViewModelProvider.notifier);
    final AppLocalizations l10n = AppLocalizations.of(context)!;

    return Column(children: [
      const Spacer(flex: 159),
      Expanded(
        flex: 1052,
        child: Row(
          children: [
            const Spacer(flex: 571),
            Expanded(
              flex: 229,
              child: Material(
                color: Colors.transparent,
                child: pre_edit_toolbar_mdc(
                  child: Column(children: [
                    const Spacer(flex: 37),
                    const Expanded(
                      flex: 68,
                      child: Row(
                        children: [
                          Spacer(flex: 52),
                          Expanded(
                            flex: 126,
                            child: ico_running_stitch(),
                          ),
                          Spacer(flex: 51),
                        ],
                      ),
                    ),
                    const Spacer(flex: 24),
                    Expanded(
                      flex: 69,
                      child: Row(
                        children: [
                          const Spacer(flex: 12),
                          Expanded(
                            flex: 205,
                            child: grp_str_runpitch(text: l10n.icon_00539),
                          ),
                          const Spacer(flex: 12),
                        ],
                      ),
                    ),
                    const Spacer(flex: 8),
                    Expanded(
                      flex: 69,
                      child: Row(
                        children: [
                          const Spacer(flex: 12),
                          Expanded(
                            flex: 205,
                            child: grp_str_parameter(
                              text: state.runPitchDisplayValue,
                              isDefault: state.isDefaultValue,
                              displayText: state.isUnitMm
                                  ? l10n.icon_00225
                                  : l10n.icon_00226,
                            ),
                          ),
                          const Spacer(flex: 12),
                        ],
                      ),
                    ),
                    const Spacer(flex: 12),
                    Expanded(
                      flex: 63,
                      child: Row(
                        children: [
                          const Spacer(flex: 48),
                          Expanded(
                            flex: 63,
                            child: grp_btn_minus_01(
                              onTap: () =>
                                  viewModel.onMinusButtonClicked(false),
                              onLongPress: () =>
                                  viewModel.onMinusButtonClicked(true),
                              state: state.isMinusButtonValid
                                  ? ButtonState.normal
                                  : ButtonState.disable,
                              feedBackControl: null,
                            ),
                          ),
                          const Spacer(flex: 8),
                          Expanded(
                            flex: 63,
                            child: grp_btn_plus_01(
                              onTap: () => viewModel.onPlusButtonClicked(false),
                              onLongPress: () =>
                                  viewModel.onPlusButtonClicked(true),
                              state: state.isPlusButtonValid
                                  ? ButtonState.normal
                                  : ButtonState.disable,
                              feedBackControl: null,
                            ),
                          ),
                          const Spacer(flex: 47),
                        ],
                      ),
                    ),
                    const Spacer(flex: 620),
                    Expanded(
                      flex: 70,
                      child: Row(
                        children: [
                          const Spacer(flex: 12),
                          Expanded(
                            flex: 205,
                            child: grp_btn_positive_mdc(
                              text: l10n.icon_ok,
                              onTap: () => viewModel.onOkButtonClicked(),
                              feedBackControl: null,
                            ),
                          ),
                          const Spacer(flex: 12),
                        ],
                      ),
                    ),
                    const Spacer(flex: 12),
                  ]),
                ),
              ),
            ),
          ],
        ),
      ),
      const Spacer(flex: 69),
    ]);
  }
}
