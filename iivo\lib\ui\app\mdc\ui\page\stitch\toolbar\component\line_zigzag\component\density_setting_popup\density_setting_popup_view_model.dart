import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_zigzag_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'density_setting_popup_view_interface.dart';

final densitySettingPopupViewModelProvider = StateNotifierProvider.autoDispose<
    DensitySettingPopupViewInterface,
    DensitySettingPopupState>((ref) => DensitySettingPopupViewModel(ref));

class DensitySettingPopupViewModel extends DensitySettingPopupViewInterface {
  DensitySettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const DensitySettingPopupState(
              densityValue: 100,
              densityDisplayValue: "",
              isDefaultValue: false,
              minusButtonValid: false,
              plusButtonValid: false,
            ),
            ref) {
    update();
  }

  ///
  /// 最大密度値
  ///
  final int maxDensityIndex =
      MdcZigzagTatamiDensity.mdc_zigzag_tatami_density_110.index;

  ///
  /// 最小密度値
  ///
  final int minDensityIndex =
      MdcZigzagTatamiDensity.mdc_zigzag_tatami_density_090.index;

  ///
  /// デフォルトの密度値
  ///
  final int defaultDensityIndex =
      MdcZigzagTatamiDensity.mdc_zigzag_tatami_density_100.index;

  ///
  /// ステップ量
  ///
  final int _stepIndex = 1;

  ///
  /// ディスプレイスター
  ///
  bool _isDensityValueDisplayStar = LineZigzagModel().getDensityIndex() !=
          LineZigzagModel.densityNotUpdating.index
      ? false
      : true;

  ///
  /// 密度値
  ///
  int _densityIndex = LineZigzagModel().getDensityIndex();

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    state = state.copyWith(
      densityValue: _densityIndex,
      densityDisplayValue: _getDensityDisplayValue(),
      isDefaultValue: _isDefaultValue(),
      minusButtonValid: _getMinusButtonState(),
      plusButtonValid: _getPlusButtonState(),
    );
  }

  ///
  /// マイナスボタンをクリックする
  ///
  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isDensityValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isDensityValueDisplayStar = false;

      ///  Model 更新
      _densityIndex = defaultDensityIndex;

      /// View更新
      update();

      return false;
    }

    if (state.densityValue - _stepIndex < minDensityIndex) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if ((_densityIndex - _stepIndex) <= minDensityIndex) {
      _densityIndex = minDensityIndex;
    } else {
      _densityIndex -= _stepIndex;
    }

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  ///
  /// プラスボタンをクリックする
  ///
  @override
  bool onPluButtonClicked(bool isLongPress) {
    if (_isDensityValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isDensityValueDisplayStar = false;

      ///  Model 更新
      _densityIndex = defaultDensityIndex;

      /// View更新
      update();

      return false;
    }

    if (state.densityValue + _stepIndex > maxDensityIndex) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if ((_densityIndex + _stepIndex) >= maxDensityIndex) {
      _densityIndex = maxDensityIndex;
    } else {
      _densityIndex += _stepIndex;
    }

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// View更新
    update();

    return true;
  }

  ///
  /// OK ボタンがクリックされました
  ///
  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineZigzagDensity.toString());
    if (_isDensityValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int index = LineZigzagModel().getDensityIndex();

    ///  Model 更新
    LineZigzagModel().setDensityIndex(_densityIndex);
    if (LineZigzagModel().setMdcZigzagSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (index != _densityIndex) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// 密度表示値を取得します
  ///
  String _getDensityDisplayValue() {
    if (_isDensityValueDisplayStar) {
      return "*";
    }

    return LineZigzagModel.densityList[_densityIndex].toString();
  }

  ///
  /// 密度表示テキストスタイルを取得します
  ///
  bool _isDefaultValue() {
    if (_isDensityValueDisplayStar) {
      return true;
    }

    if (_densityIndex == defaultDensityIndex) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isDensityValueDisplayStar) {
      return true;
    }

    if (_densityIndex <= minDensityIndex) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isDensityValueDisplayStar) {
      return true;
    }

    if (_densityIndex >= maxDensityIndex) {
      return false;
    }
    return true;
  }
}
