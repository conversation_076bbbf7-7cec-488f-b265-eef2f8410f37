import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'direction_setting_popup_view_interface.freezed.dart';

@freezed
class DirectionSettingPopupState with _$DirectionSettingPopupState {
  const factory DirectionSettingPopupState({
    @Default("") String settingValue,
  }) = _DirectionSettingPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class DirectionSettingPopupStateViewInterface
    extends ViewModel<DirectionSettingPopupState> {
  DirectionSettingPopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress);

  ///
  /// 表示用のテキストスタイル取得
  /// @param
  /// - isAllValueSame: 値が同じであること
  /// - value：設定値
  /// - defaultValue：デフォルト値
  ///
  bool getDirectionTextStyle();
}
