// ignore: file_names
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';

import '../custom/grp_custom_settinglist_listbtn_wlan_others_title.dart';
import 'pre_settinglist1line.dart';

// ignore: camel_case_types
class grp_pre_wlan_others_title extends StatelessWidget {
  const grp_pre_wlan_others_title({
    super.key,
    this.onTap,
    this.voiceControlFunction,
    this.centerLText = "",
    this.state = ButtonState.normal,
    this.feedBackControl = const FeedBackControl(),
  });
  final void Function()? onTap;
  final void Function()? voiceControlFunction;
  final String centerLText;
  final ButtonState state;
  final FeedBackControl? feedBackControl;
  @override
  Widget build(BuildContext context) => Stack(
        children: [
          const pre_settinglist1line(),
          grp_custom_settinglist_listbtn_wlan_others_title(
            onTap: onTap,
            centerLText: centerLText,
            state: state,
            voiceControlFunction: voiceControlFunction,
            feedBackControl: feedBackControl,
          ),
        ],
      );
}
