import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

part 'surface_stipple_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

///
/// 白い背景に黒いテキスト
///
const TextStyle blackTextWhiteBackground =
    TextStyle(color: Colors.white, backgroundColor: Colors.black);

///
/// 黒い背景に白いテキスト
///
const TextStyle whiteTextBlackBackground =
    TextStyle(color: Colors.black, backgroundColor: Colors.white);

@freezed
class SurfaceStippleState with _$SurfaceStippleState {
  const factory SurfaceStippleState({
    required bool stippleRunPitchPopup,
    required bool stippleSpacingPopup,
    required bool stippleDistancePopup,
    required bool stippleStitchPopup,
    required StichLine mdcStichLine,
    required String runPitchDisplayValue,
    required String spacingDisplayValue,
    required String distanceDisplayValue,
    required String singleDisplayDefault,
    required bool runPitchDisplayDefault,
    required bool spacingDisplayDefault,
    required bool distanceDisplayDefault,
    required bool tMdcStichLineType,
  }) = _SurfaceStippleState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class SurfaceStippleStateViewInterface
    extends ViewModel<SurfaceStippleState> {
  SurfaceStippleStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;

  ///
  /// 面プロパティーの既定値
  ///
  int get defaultDistanceValue;

  StichLine get defaultStitchValue;

  int get defaultRunPitchValue;

  int get defaultSpacingValue;

  ///
  /// RunPitchダンパボタン
  ///
  void onCandleRunPitchClick(context);

  ///
  /// Spacingダンパボタン
  ///
  void onCandleSpacingClick(context);

  ///
  /// Distanceダンパボタン
  ///
  void onCandleDistanceClick(context);

  ///
  /// Stitchダンパボタン
  ///
  void onCandleStitchClick(context);
}
