import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'under_sewing_setting_popup_view_interface.freezed.dart';

@freezed
class UnderSewingSettingPopupState with _$UnderSewingSettingPopupState {
  const factory UnderSewingSettingPopupState({
    @Default(false) bool isOnButtonSelected,
    @Default(false) bool isOFFButtonSelected,
  }) = _UnderSewingSettingPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class UnderSewingSettingPopupStateViewInterface
    extends ViewModel<UnderSewingSettingPopupState> {
  UnderSewingSettingPopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked(BuildContext context);

  ///
  /// ONボタンがクリックされました
  ///
  void onONButtonClicked();

  ///
  /// OFFボタンがクリックされました
  ///
  void onOFFButtonClicked();
}
