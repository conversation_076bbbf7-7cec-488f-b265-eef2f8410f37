import 'dart:io';

import 'package:common_component/common_component.dart'
    show PageBuilder, PageNavigator, PageNavigatorState, pageNameTail;
import 'package:flutter/material.dart';
import 'package:log/log.dart';
import 'package:presentation_displays/presentation_displays.dart';
import 'package:synchronized/synchronized.dart';

import '../../iivo.dart';
import '../app/autotest/ui/page/auto_test_mode_page.dart';
import '../app/debug_monitor/app/debug_page/debug_page.dart';
import '../app/emb/ui/page/common_component/sew_connectsew/connect_sewing.dart';
import '../app/emb/ui/page/pattern_edit/edit_menu/color_change/color_change.dart';
import '../app/emb/ui/page/pattern_edit/pattern_edit.dart';
import '../app/emb/ui/page/pattern_select/category_selector/category_selector.dart';
import '../app/emb/ui/page/pattern_select/category_selector/component/filter_result/filter_result.dart';
import '../app/emb/ui/page/pattern_select/character_font/character_font.dart';
import '../app/emb/ui/page/pattern_select/quilt_pattern/quilt_edge_to_edge/pattern_confirm/pattern_confirm.dart';
import '../app/emb/ui/page/pattern_select/quilt_pattern/quilt_edge_to_edge/size_split/size_split.dart';
import '../app/emb/ui/page/pattern_select/quilt_pattern/quilt_sashes_hexagon/quilt_sashes_hexagon.dart';
import '../app/emb/ui/page/pattern_select/quilt_pattern/quilt_sashes_hexagon/quilt_sashes_hexagon_check/quilt_sashes_hexagon_check.dart';
import '../app/emb/ui/page/pattern_select/quilt_pattern/quilt_sashes_method/quilt_sashes_method.dart';
import '../app/emb/ui/page/pattern_select/quilt_pattern/quilt_sashes_size_split/quilt_sashes_size_split.dart';
import '../app/emb/ui/page/pattern_select/quilt_pattern/quilt_sashes_size_split/quilt_sashes_size_split_check/quilt_sashes_size_split_check.dart';
import '../app/emb/ui/page/pattern_select/text_editing_page/text_editing_page.dart';
import '../app/emb/ui/page/photo_stitch/emb_convert/emb_convert.dart';
import '../app/emb/ui/page/photo_stitch/file_selector/file_selector.dart';
import '../app/emb/ui/page/photo_stitch/photo_size_adjustment/photo_size_adjustment.dart';
import '../app/emb/ui/page/photo_stitch/photo_stitch_top/photo_stitch_top.dart';
import '../app/emb/ui/page/photo_stitch/style_change/component/custom/custom_file_selector/custom_file_selector.dart';
import '../app/emb/ui/page/photo_stitch/style_change/style_change.dart';
import '../app/emb/ui/page/sewing/bottom/mask_trace/mask_trace.dart';
import '../app/emb/ui/page/sewing/bottom/start_position/start_position.dart';
import '../app/emb/ui/page/sewing/bottom/stitch_forward_backward/stitch_forward_backward.dart';
import '../app/emb/ui/page/sewing/bottom/thread_cutting/thread_cutting.dart';
import '../app/emb/ui/page/sewing/sewing.dart';
import '../app/home/<USER>/page/demo_mode_page/demo_mode_page.dart';
import '../app/home/<USER>/page/demo_mode_page/image/show_project_image.dart';
import '../app/home/<USER>/page/home_page/home_page.dart';
import '../app/home/<USER>/page/init_page/init_copy_copy/init_copy_copy.dart';
import '../app/home/<USER>/page/init_page/init_fail_safe/init_fail_safe.dart';
import '../app/home/<USER>/page/init_page/init_failed/init_failed.dart';
import '../app/home/<USER>/page/init_wait_page.dart';
import '../app/home/<USER>/page/opening_page/opening_page.dart';
import '../app/home/<USER>/page/shut_off_page/shut_off_page.dart';
import '../app/mdc/ui/page/paint/paint_page.dart';
import '../app/mdc/ui/page/paint/top_bar/dropper_tool_page/dropper_tool_page.dart';
import '../app/mdc/ui/page/paint/top_bar/illustration_design_page/illustration_design_page.dart';
import '../app/mdc/ui/page/paint/top_bar/illustration_design_retry_page/illustration_design_retry_page.dart';
import '../app/mdc/ui/page/paint/top_bar/line_design_page/line_design_page.dart';
import '../app/mdc/ui/page/paint/top_bar/line_design_retry_page/line_design_retry_page.dart';
import '../app/mdc/ui/page/paint/top_bar/scan_page/file_selector/file_selector.dart';
import '../app/mdc/ui/page/paint/top_bar/scan_page/scan_page.dart';
import '../app/mdc/ui/page/stitch/stitch_page.dart';
import '../app/mdc/ui/page/stitch/top_bar/component/real_preview/real_preview.dart';
import '../app/setting/ui/page/body/general_setting/general_setting_page_2/component/screen_saver_setting/screen_saver_change_image/screen_saver_change_image.dart';
import '../app/setting/ui/page/body/general_setting/general_setting_page_2/component/screen_saver_setting/screen_saver_setting.dart';
import '../app/setting/ui/page/body/general_setting/general_setting_page_4/certification/certification.dart';
import '../app/setting/ui/page/body/general_setting/general_setting_page_4/certification_completed/certification_completed.dart';
import '../app/setting/ui/page/body/general_setting/general_setting_page_4/certification_method/certification_method.dart';
import '../app/setting/ui/page/body/general_setting/general_setting_page_4/certification_processing.dart';
import '../app/setting/ui/page/body/general_setting/general_setting_page_4/confirm_certification/confirm_certification.dart';
import '../app/setting/ui/page/body/general_setting/general_setting_page_4/input_activation_code/input_activation_code.dart';
import '../app/setting/ui/page/body/general_setting/general_setting_page_4/pdf_manual_menu/pdf_manual_menu_page.dart';
import '../app/setting/ui/page/body/general_setting/general_setting_page_4/pdf_preview/pdf_preview.dart';
import '../app/setting/ui/page/body/general_setting/general_setting_page_4/reset_completed.dart';
import '../app/setting/ui/page/body/general_setting/general_setting_page_4/user_reset_processing/user_reset_processing.dart';
import '../app/setting/ui/page/body/network_setting/network_setting_page_1/change/change_name.dart';
import '../app/setting/ui/page/body/network_setting/network_setting_page_1/component/network_reset_complete.dart';
import '../app/setting/ui/page/body/network_setting/network_setting_page_1/component/wireless _lan_status/wireless_lan_status.dart';
import '../app/setting/ui/page/body/network_setting/network_setting_page_1/others/network_others_setting_page.dart';
import '../app/setting/ui/page/clock/clock_setting.dart';
import '../app/setting/ui/page/setting_page.dart';
import '../app/teaching/ui/page/operation_guide/basic_operation/bobbin_winding/bobbin_winding.dart';
import '../app/teaching/ui/page/operation_guide/basic_operation/changing_the_needle/changing_the_needle.dart';
import '../app/teaching/ui/page/operation_guide/basic_operation/changing_the_presser_foot/changing_the_presser_foot.dart';
import '../app/teaching/ui/page/operation_guide/basic_operation/setting_the_bobbin/setting_the_bobbin.dart';
import '../app/teaching/ui/page/operation_guide/basic_operation/upper_threading/upper_threadingn.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/adjusting_thread_tension/correct_thread_tension.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/adjusting_thread_tension/top_page/top_page.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/adjusting_thread_tension/upper_thread_loose/upper_thread_loose.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/adjusting_thread_tension/upper_thread_taut/upper_thread_taut.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/attaching_embroidery_foot/attaching_embroidery_foot.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/attaching_embroidery_unit/attaching_embroidery_unit.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/attaching_removing_emb_frame/attaching_removing_emb_frame.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/attaching_stabilizers/attaching_stabilizers.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/correct_stabilizer_to_use/fabrics_can_be_ironed.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/correct_stabilizer_to_use/fabrics_cannot_be_ironed.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/correct_stabilizer_to_use/napped_fabrics.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/correct_stabilizer_to_use/thin_fabrics.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/correct_stabilizer_to_use/top_page/top_page.dart';
import '../app/teaching/ui/page/operation_guide/emb_basic_operation/inserting_the_fabric/inserting_the_fabric.dart';
import '../app/teaching/ui/page/operation_guide/maintenance/cleaning_race_shuttle/cleaning_race_shuttle.dart';
import '../app/teaching/ui/page/operation_guide/operation_guide_menu/operation_guide_menu.dart';
import '../app/teaching/ui/page/operation_guide/principal_buttons/automatic_threading.dart';
import '../app/teaching/ui/page/operation_guide/principal_buttons/needle_position.dart';
import '../app/teaching/ui/page/operation_guide/principal_buttons/presser_foot_lifter.dart';
import '../app/teaching/ui/page/operation_guide/principal_buttons/reinforcement_tie_off_stitch.dart';
import '../app/teaching/ui/page/operation_guide/principal_buttons/reverse_stitch.dart';
import '../app/teaching/ui/page/operation_guide/principal_buttons/start_stop.dart';
import '../app/teaching/ui/page/operation_guide/principal_buttons/thread_cutter.dart';
import '../app/teaching/ui/page/operation_guide/principal_parts/flat_bed.dart';
import '../app/teaching/ui/page/operation_guide/principal_parts/foot_controller/foot_controller.dart';
import '../app/teaching/ui/page/operation_guide/principal_parts/hand_wheel.dart';
import '../app/teaching/ui/page/operation_guide/principal_parts/knee_lifter/knee_lifter.dart';
import '../app/teaching/ui/page/operation_guide/principal_parts/presser_foot_lever.dart';
import '../app/teaching/ui/page/operation_guide/principal_parts/sewing_speed_controller.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/bobbin_thread_breaks/bobbin_thread_breaks.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/character_pattern_incorrect/character_pattern_incorrect.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/emb_pattern_incorrect/emb_pattern_incorrect.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/emb_unit_disabled/emb_unit_disabled.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/fabric_feed_disabled/fabric_feed_disabled.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/fabric_puckers/fabric_puckers.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/machine_disabled/machine_disabled.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/machine_noisy/machine_noisy.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/needle_breaks/needle_breaks.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/needle_threader_disabled/needle_threader_disabled.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/skipped_stitches/skipped_stitches.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/thread_tangled/thread_tangled.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/thread_tension_incorrect/thread_tension_incorrect.dart';
import '../app/teaching/ui/page/operation_guide/troubleshooting/upper_thread_breaks/upper_thread_breaks.dart';
import '../app/teaching/ui/page/pdf_manual_menu/pdf_manual_menu_page.dart';
import '../app/teaching/ui/page/pdf_preview/pdf_preview.dart';
import '../app/teaching/ui/page/top_page.dart';
import '../app/teaching/ui/page/video/video_menu_page.dart';
import '../app/testmode/src/test_mode_page.dart';
import '../app/utl/ui/page/pattern_explanation/character/character.dart'
    as character_explanation;
import '../app/utl/ui/page/pattern_explanation/character/making_adjustments/decrease_fine_adjust_horizontal/decrease_fine_adjust_horizontal.dart';
import '../app/utl/ui/page/pattern_explanation/character/making_adjustments/decrease_fine_adjust_vertical/decrease_fine_adjust_vertical.dart';
import '../app/utl/ui/page/pattern_explanation/character/making_adjustments/increase_fine_adjust_horizontal/increase_fine_adjust_horizontal.dart';
import '../app/utl/ui/page/pattern_explanation/character/making_adjustments/increase_fine_adjust_vertical/increase_fine_adjust_vertical.dart';
import '../app/utl/ui/page/pattern_explanation/character/making_adjustments/making_adjustments.dart';
import '../app/utl/ui/page/pattern_explanation/character/sewing_attractive_finishes/sewing_attractive_finishes.dart';
import '../app/utl/ui/page/pattern_explanation/utility/utility.dart'
    as utility_explanation;
import '../app/utl/ui/page/sewing/character/character.dart';
import '../app/utl/ui/page/sewing/common/real_preview/real_preview.dart';
import '../app/utl/ui/page/sewing/custom/custom.dart';
import '../app/utl/ui/page/sewing/custom/test_sewing/test_sewing.dart';
import '../app/utl/ui/page/sewing/utility/utility.dart';
import '../app/utl/ui/page/sewing_guide/category_page/category_page.dart';
import '../app/utl/ui/page/sewing_guide/pattern_page/pattern_page.dart';
import '../app/utl/ui/page/sewing_guide/sewing_guide_page.dart';
import '../app/utl/ui/page/stitch_regulator/stitch_regulator.dart';
import '../component/real_preview/real_preview.dart';
import '../component/real_preview/real_preview_view_interface.dart';
import '../component/sub_screen/sub_screen.dart';
import '../component/sub_screen/sub_screen_dummy.dart';

///
/// 各画面の名前
///
enum PageRouteEnum {
//////////////////////////////////////////////////////////////
//////////////////////init error check////////////////////////
//////////////////////////////////////////////////////////////
  initFailed,
  initCopyCopy,
  initFailSafe,
///////////////////////////////////////////////////////////////
//////////////////////testmode アプリ//////////////////////////
//////////////////////////////////////////////////////////////
  testModePage,
///////////////////////////////////////////////////////////////
////////////////////// auto test アプリ////////////////////////
//////////////////////////////////////////////////////////////
  autoTestMode,
//////////////////////////////////////////////////////////////
//////////////////////upgrade アプリ///////////////////////////
//////////////////////////////////////////////////////////////
  upgradeSelect,
  upgradeLoad,
  upgradeLoadProgress,
//////////////////////////////////////////////////////////////
//////////////////////home アプリ//////////////////////////////
//////////////////////////////////////////////////////////////
  initWait,
  shutOff,
  opening,
  home,
  demoMode,
//////////////////////////////////////////////////////////////
//////////////////////emb アプリ//////////////////////////////
//////////////////////////////////////////////////////////////
  patternSelect,
  patternEdit,
  patternSelectFilterResult,
  sewing,
  embRealPreview,
  threadCutting,
  startPosition,
  stitchForwardBackward,
  maskTrace,
  colorChange,
  quiltSashesMethod,
  quiltSashesSizeSplit,
  quiltSashesHexagon,
  quiltSashesHexagonCheck,
  quiltSashesSizeSplitCheck,
  quiltEdgeToEdgeSizeSplit,
  quiltEdgeToEdgeConfirm,
  characterFontSelect,
  fileSelector,
  photoStitchPopup,
  photoStitchStyle,
  photoSizeAdjustment,
  photoRemoveBackgroundPopup,
  photoStitchEmbConvert,
  customFileSelector,
  embCharacterFontCategoryPage,
  connectSew,
//////////////////////////////////////////////////////////////
//////////////////////Utl アプリ//////////////////////////////
//////////////////////////////////////////////////////////////
  sewingUtility,
  sewingCharacter,
  sewingGuide,
  sewingGuideCategory,
  sewingGuidePattern,
  utilityPatternExplanation,
  characterPatternExplanation,
  sewingAttractiveFinishes,
  makingAdjustments,
  increaseFineAdjustVertical,
  decreaseFineAdjustVertical,
  increaseFineAdjustHorizontal,
  decreaseFineAdjustHorizontal,
  stitchRegulator,
  custom,
  customTest,
  utlRealPreview,
//////////////////////////////////////////////////////////////
//////////////////////teaching アプリ/////////////////////////
//////////////////////////////////////////////////////////////
  teaching,
  pdfManualMenuPage,
  teachingPdfPreview,
  operationGuideMenu,
  videoMenu,
  correctThreadTensionPage,
  upperThreadTautPage,
  upperThreadLoosePage,
  fabricsCanBeIronedPage,
  fabricsCannotBeIronedPage,
  thinFabricsPage,
  nappedFabricsPage,
  operationGuideUpperThreadingPage,
  operationGuideBobbinWindingPage,
  operationGuideChangingTheNeedlePage,
  operationGuideChangingThePresserFootPage,
  operationGuideSettingTheBobbinPage,
  operationGuideAdjustingThreadTensionTopPage,
  operationGuideAttachingIronOnStabilizersPage,
  operationGuideInsertingTheFabricPage,
  operationGuideAttachingRemovingEmbFramePage,
  operationGuideAttachingEmbUnitPage,
  operationGuideAttachingEmbFootPage,
  operationGuideCorrectStabilizerTopPage,
  operationGuideCleaningRaceShuttlePage,
  operationGuideNeedlePositionPage,
  operationGuideThreadCutterPage,
  operationGuidePresserFootLifterPage,
  operationGuideAutomaticThreadingPage,
  operationGuideStartAndStopPage,
  operationGuideReverseStitchPage,
  operationGuideReinforcementPage,
  operationGuidePresserFootLeverPage,
  operationGuideSewingSpeedControllerPage,
  operationGuideHandWheelPage,
  operationGuideFlatBedPage,
  operationGuideKneeLifterPage,
  operationGuideFootControllerPage,
  operationGuideThreadTangledPage,
  operationGuideNeedleThreaderDisabledPage,
  operationGuideThreadTensionIncorrectPage,
  operationGuideUpperThreadBreaksPage,
  operationGuideBobbinThreadBreaksPage,
  operationGuideSkippedStitchesPage,
  operationGuideNeedleBreaksPage,
  operationGuideFabricFeedDisabledPage,
  operationGuideFabricPuckersPage,
  operationGuideMachineNoisyPage,
  operationGuideCharacterPatternIncorrectPage,
  operationGuideEmbPatternIncorrectPage,
  operationGuideMachineDisabledPage,
  operationGuideEmbUnitDisabledPage,
//////////////////////////////////////////////////////////////
///////////////////////setting アプリ/////////////////////////
//////////////////////////////////////////////////////////////
  setting,
  screenSaverSetting,
  screenSaverChangeImage,
  clockSetting,
  networkChangeName,
  certification,
  certificationMethod,
  confirmCertification,
  certificationProcessing,
  certificationCompleted,
  legalInformation,
  settingPdfPreview,
  inputActivationCode,
  wirelessLanStatus,
  networkResetComplete,
  userResetProcessing,
  userResetComplete,
  networkOthersSetting,

//////////////////////////////////////////////////////////////
//////////////////////////mdc アプリ//////////////////////////
//////////////////////////////////////////////////////////////
  paint,
  stitch,
  scan,
  mdcFileSelector,
  illustrationDesign,
  illustrationDesignRetry,
  dropperTool,
  stitchRealPreview,
  lineDesign,
  lineDesignRetry,

//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
/////////////////////Projector アプリ/////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
  subScreen,
  subScreenDummy,
  showProjectImage,

//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
////////////////////////////debug ////////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////

  debugPage1,
  ;

  ///
  /// 列挙値を取得する
  /// 例：
  /// PageRouteEnum.paint == PageRouteEnum.praseByEnumString("paint")
  ///
  static PageRouteEnum? praseByEnumString(String enumToString) {
    for (var value in PageRouteEnum.values) {
      if (value.toString() == enumToString) {
        return value;
      }
    }

    return null;
  }

  ///
  /// 列挙値を取得する
  /// 例：
  /// PageRouteEnum.paint == PageRouteEnum.praseByName("paint")
  ///
  static PageRouteEnum? praseByName(String routeName) {
    for (var value in PageRouteEnum.values) {
      if (value.name == routeName) {
        return value;
      }
    }

    return null;
  }
}

///
/// それぞれの画面のルーティング
///
final Map<String, PageBuilder> iivoPageRoutes = {
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////init error check////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
  PageRouteEnum.initFailed.toString():
      PageBuilder(builder: (context) => const InitFailed()),
  PageRouteEnum.initCopyCopy.toString():
      PageBuilder(builder: (context) => const InitCopyCopy()),
  PageRouteEnum.initFailSafe.toString():
      PageBuilder(builder: (context) => const InitFailSafe()),
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////testmode アプリ///////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
  PageRouteEnum.testModePage.toString():
      PageBuilder(builder: (context) => const TestModeTopPage()),
///////////////////////////////////////////////////////////////
////////////////////// auto test アプリ////////////////////////
//////////////////////////////////////////////////////////////
  PageRouteEnum.autoTestMode.toString():
      PageBuilder(builder: (context) => const AutoTestModePage()),
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////home アプリ//////////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
  PageRouteEnum.initWait.toString():
      PageBuilder(builder: (context) => const InitWaitPage()),
  PageRouteEnum.shutOff.toString():
      PageBuilder(builder: (context) => const ShutOffPage()),
  PageRouteEnum.opening.toString():
      PageBuilder(builder: (context) => const OpeningPage()),
  PageRouteEnum.home.toString():
      PageBuilder(builder: (context) => const HomePage()),
  PageRouteEnum.demoMode.toString():
      PageBuilder(builder: (context) => const DemoModePage()),

//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////emb アプリ//////////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
  PageRouteEnum.patternSelect.toString():
      PageBuilder(builder: (context) => const CategorySelector()),
  PageRouteEnum.patternSelectFilterResult.toString():
      PageBuilder(builder: (context) => const FilterResult()),
  PageRouteEnum.patternEdit.toString():
      PageBuilder(builder: (context) => const PatternEdit()),
  PageRouteEnum.sewing.toString():
      PageBuilder(builder: (context) => const Sewing()),
  PageRouteEnum.embRealPreview.toString(): PageBuilder(
      builder: (context) => const RealPreview(type: RealPreviewType.emb)),
  PageRouteEnum.threadCutting.toString():
      PageBuilder(builder: (context) => const ThreadCuttingPage()),
  PageRouteEnum.startPosition.toString():
      PageBuilder(builder: (context) => const StartPosition()),
  PageRouteEnum.stitchForwardBackward.toString():
      PageBuilder(builder: (context) => const StichForwardBackwardPage()),
  PageRouteEnum.maskTrace.toString():
      PageBuilder(builder: (context) => const MaskTrace()),
  PageRouteEnum.colorChange.toString():
      PageBuilder(builder: (context) => const ColorChange()),
  PageRouteEnum.quiltSashesMethod.toString():
      PageBuilder(builder: (context) => const QuiltSashesMethod()),
  PageRouteEnum.quiltSashesSizeSplit.toString():
      PageBuilder(builder: (context) => const QuiltSashesSizeSplit()),
  PageRouteEnum.quiltSashesHexagon.toString():
      PageBuilder(builder: (context) => const QuiltSashesHexagon()),
  PageRouteEnum.quiltEdgeToEdgeSizeSplit.toString():
      PageBuilder(builder: (context) => const SizeSplit()),
  PageRouteEnum.quiltEdgeToEdgeConfirm.toString():
      PageBuilder(builder: (context) => const PatternConfirm()),
  PageRouteEnum.quiltSashesHexagonCheck.toString():
      PageBuilder(builder: (context) => const QuiltSashesHexagonCheck()),
  PageRouteEnum.quiltSashesSizeSplitCheck.toString():
      PageBuilder(builder: (context) => const QuiltSashesSizeSplitCheck()),
  PageRouteEnum.characterFontSelect.toString():
      PageBuilder(builder: (context) => const TextEditingPage()),
  PageRouteEnum.fileSelector.toString():
      PageBuilder(builder: (context) => const FileSelector()),
  PageRouteEnum.photoStitchPopup.toString():
      PageBuilder(builder: (context) => const PhotoStitchTop()),
  PageRouteEnum.photoSizeAdjustment.toString():
      PageBuilder(builder: (context) => const PhotoSizeAdjustment()),
  PageRouteEnum.photoStitchStyle.toString():
      PageBuilder(builder: (context) => const StyleChange()),
  PageRouteEnum.photoStitchEmbConvert.toString():
      PageBuilder(builder: (context) => const EmbConvert()),
  PageRouteEnum.customFileSelector.toString():
      PageBuilder(builder: (context) => const CustomFileSelector()),
  PageRouteEnum.embCharacterFontCategoryPage.toString():
      PageBuilder(builder: (context) => const CharacterFontPage()),
  PageRouteEnum.connectSew.toString():
      PageBuilder(builder: (context) => const ConnectSewing()),
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////Utl アプリ//////////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
  PageRouteEnum.sewingUtility.toString():
      PageBuilder(builder: (context) => const Utility()),
  PageRouteEnum.sewingCharacter.toString():
      PageBuilder(builder: (context) => const Character()),
  PageRouteEnum.sewingGuide.toString():
      PageBuilder(builder: (context) => const SewingGuidePage()),
  PageRouteEnum.sewingGuideCategory.toString():
      PageBuilder(builder: (context) => const CategoryPage()),
  PageRouteEnum.sewingGuidePattern.toString():
      PageBuilder(builder: (context) => const PatternPage()),
  PageRouteEnum.utilityPatternExplanation.toString():
      PageBuilder(builder: (context) => const utility_explanation.Utility()),
  PageRouteEnum.characterPatternExplanation.toString(): PageBuilder(
      builder: (context) => const character_explanation.Character()),
  PageRouteEnum.sewingAttractiveFinishes.toString():
      PageBuilder(builder: (context) => const SewingAttractiveFinishes()),
  PageRouteEnum.makingAdjustments.toString():
      PageBuilder(builder: (context) => const MakingAdjustments()),
  PageRouteEnum.increaseFineAdjustVertical.toString():
      PageBuilder(builder: (context) => const IncreaseFineAdjustVertical()),
  PageRouteEnum.decreaseFineAdjustVertical.toString():
      PageBuilder(builder: (context) => const DecreaseFineAdjustVertical()),
  PageRouteEnum.increaseFineAdjustHorizontal.toString():
      PageBuilder(builder: (context) => const IncreaseFineAdjustHorizontal()),
  PageRouteEnum.decreaseFineAdjustHorizontal.toString():
      PageBuilder(builder: (context) => const DecreaseFineAdjustHorizontal()),
  PageRouteEnum.stitchRegulator.toString():
      PageBuilder(builder: (context) => const StitchRegulator()),
  PageRouteEnum.custom.toString():
      PageBuilder(builder: (context) => const CustomPage()),
  PageRouteEnum.customTest.toString():
      PageBuilder(builder: (context) => const TestSewingPage()),
  PageRouteEnum.utlRealPreview.toString():
      PageBuilder(builder: (context) => const UtlRealPreview()),
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////teaching アプリ/////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
  PageRouteEnum.teaching.toString():
      PageBuilder(builder: (context) => const TeachingTopPage()),
  PageRouteEnum.pdfManualMenuPage.toString():
      PageBuilder(builder: (context) => const TeachingPdfManualMenuPage()),
  PageRouteEnum.teachingPdfPreview.toString():
      PageBuilder(builder: (context) => const TeachingPdfPreview()),
  PageRouteEnum.operationGuideMenu.toString():
      PageBuilder(builder: (context) => const OperationGuideMenuPage()),
  PageRouteEnum.videoMenu.toString():
      PageBuilder(builder: (context) => const VideoMenuPage()),
  PageRouteEnum.correctThreadTensionPage.toString():
      PageBuilder(builder: (context) => const CorrectThreadTensionPage()),
  PageRouteEnum.upperThreadTautPage.toString():
      PageBuilder(builder: (context) => const UpperThreadTautPage()),
  PageRouteEnum.upperThreadLoosePage.toString():
      PageBuilder(builder: (context) => const UpperThreadLoosePage()),
  PageRouteEnum.fabricsCanBeIronedPage.toString():
      PageBuilder(builder: (context) => const FabricsCanBeIronedPage()),
  PageRouteEnum.fabricsCannotBeIronedPage.toString():
      PageBuilder(builder: (context) => const FabricsCannotBeIronedPage()),
  PageRouteEnum.thinFabricsPage.toString():
      PageBuilder(builder: (context) => const ThinFabricsPage()),
  PageRouteEnum.nappedFabricsPage.toString():
      PageBuilder(builder: (context) => const NappedFabricsPage()),
  PageRouteEnum.operationGuideUpperThreadingPage.toString():
      PageBuilder(builder: (context) => const UpperThreadingPage()),
  PageRouteEnum.operationGuideBobbinWindingPage.toString():
      PageBuilder(builder: (context) => const BobbinWindingPage()),
  PageRouteEnum.operationGuideChangingTheNeedlePage.toString():
      PageBuilder(builder: (context) => const ChangingTheNeedlePage()),
  PageRouteEnum.operationGuideChangingThePresserFootPage.toString():
      PageBuilder(builder: (context) => const ChangingThePresserFootPage()),
  PageRouteEnum.operationGuideSettingTheBobbinPage.toString():
      PageBuilder(builder: (context) => const SettingTheBobbinPage()),
  PageRouteEnum.operationGuideAdjustingThreadTensionTopPage.toString():
      PageBuilder(builder: (context) => const AdjustingThreadTensionTopPage()),
  PageRouteEnum.operationGuideAttachingIronOnStabilizersPage.toString():
      PageBuilder(builder: (context) => const AttachingIronOnStabilizersPage()),
  PageRouteEnum.operationGuideInsertingTheFabricPage.toString():
      PageBuilder(builder: (context) => const InsertingTheFabricPage()),
  PageRouteEnum.operationGuideAttachingRemovingEmbFramePage.toString():
      PageBuilder(builder: (context) => const AttachingRemovingEmbFramePage()),
  PageRouteEnum.operationGuideAttachingEmbUnitPage.toString():
      PageBuilder(builder: (context) => const AttachingEmbUnitPage()),
  PageRouteEnum.operationGuideAttachingEmbFootPage.toString():
      PageBuilder(builder: (context) => const AttachingEmbFootPage()),
  PageRouteEnum.operationGuideCorrectStabilizerTopPage.toString():
      PageBuilder(builder: (context) => const CorrectStabilizerTopPage()),
  PageRouteEnum.operationGuideCleaningRaceShuttlePage.toString():
      PageBuilder(builder: (context) => const CleaningRaceShuttlePage()),
  PageRouteEnum.operationGuideNeedlePositionPage.toString():
      PageBuilder(builder: (context) => const NeedlePositionPage()),
  PageRouteEnum.operationGuideThreadCutterPage.toString():
      PageBuilder(builder: (context) => const ThreadCutterPage()),
  PageRouteEnum.operationGuidePresserFootLifterPage.toString():
      PageBuilder(builder: (context) => const PresserFootLifterPage()),
  PageRouteEnum.operationGuideAutomaticThreadingPage.toString():
      PageBuilder(builder: (context) => const AutomaticThreadingPage()),
  PageRouteEnum.operationGuideStartAndStopPage.toString():
      PageBuilder(builder: (context) => const StartAndStopPage()),
  PageRouteEnum.operationGuideReverseStitchPage.toString():
      PageBuilder(builder: (context) => const ReverseStitchPage()),
  PageRouteEnum.operationGuideReinforcementPage.toString():
      PageBuilder(builder: (context) => const ReinforcementPage()),
  PageRouteEnum.operationGuidePresserFootLeverPage.toString():
      PageBuilder(builder: (context) => const PresserFootLeverPage()),
  PageRouteEnum.operationGuideSewingSpeedControllerPage.toString():
      PageBuilder(builder: (context) => const SewingSpeedControllerPage()),
  PageRouteEnum.operationGuideHandWheelPage.toString():
      PageBuilder(builder: (context) => const HandWheelPage()),
  PageRouteEnum.operationGuideFlatBedPage.toString():
      PageBuilder(builder: (context) => const FlatBedPage()),
  PageRouteEnum.operationGuideKneeLifterPage.toString():
      PageBuilder(builder: (context) => const KneeLifterPage()),
  PageRouteEnum.operationGuideFootControllerPage.toString():
      PageBuilder(builder: (context) => const FootControllerPage()),
  PageRouteEnum.operationGuideThreadTangledPage.toString():
      PageBuilder(builder: (context) => const ThreadTangledPage()),
  PageRouteEnum.operationGuideNeedleThreaderDisabledPage.toString():
      PageBuilder(builder: (context) => const NeedleThreaderDisabledPage()),
  PageRouteEnum.operationGuideThreadTensionIncorrectPage.toString():
      PageBuilder(builder: (context) => const ThreadTensionIncorrectPage()),
  PageRouteEnum.operationGuideUpperThreadBreaksPage.toString():
      PageBuilder(builder: (context) => const UpperThreadBreaksPage()),
  PageRouteEnum.operationGuideBobbinThreadBreaksPage.toString():
      PageBuilder(builder: (context) => const BobbinThreadBreaksPage()),
  PageRouteEnum.operationGuideSkippedStitchesPage.toString():
      PageBuilder(builder: (context) => const SkippedStitchesPage()),
  PageRouteEnum.operationGuideNeedleBreaksPage.toString():
      PageBuilder(builder: (context) => const NeedleBreaksPage()),
  PageRouteEnum.operationGuideFabricFeedDisabledPage.toString():
      PageBuilder(builder: (context) => const FabricFeedDisabledPage()),
  PageRouteEnum.operationGuideFabricPuckersPage.toString():
      PageBuilder(builder: (context) => const FabricPuckersPage()),
  PageRouteEnum.operationGuideMachineNoisyPage.toString():
      PageBuilder(builder: (context) => const MachineNoisyPage()),
  PageRouteEnum.operationGuideCharacterPatternIncorrectPage.toString():
      PageBuilder(builder: (context) => const CharacterPatternIncorrectPage()),
  PageRouteEnum.operationGuideEmbPatternIncorrectPage.toString():
      PageBuilder(builder: (context) => const EmbPatternIncorrectPage()),
  PageRouteEnum.operationGuideMachineDisabledPage.toString():
      PageBuilder(builder: (context) => const MachineDisabledPage()),
  PageRouteEnum.operationGuideEmbUnitDisabledPage.toString():
      PageBuilder(builder: (context) => const EmbUnitDisabledPage()),
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
///////////////////////setting アプリ/////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////

  PageRouteEnum.setting.toString():
      PageBuilder(builder: (context) => const SettingPage()),
  PageRouteEnum.screenSaverSetting.toString():
      PageBuilder(builder: (context) => const ScreenSaverSetting()),
  PageRouteEnum.screenSaverChangeImage.toString():
      PageBuilder(builder: (context) => const ScreenSaverChangeImage()),
  PageRouteEnum.clockSetting.toString():
      PageBuilder(builder: (context) => const ClockSetting()),
  PageRouteEnum.networkChangeName.toString():
      PageBuilder(builder: (context) => const ChangeName()),
  PageRouteEnum.certification.toString():
      PageBuilder(builder: (context) => const Certification()),
  PageRouteEnum.certificationMethod.toString():
      PageBuilder(builder: (context) => const CertificationMethodPage()),
  PageRouteEnum.confirmCertification.toString():
      PageBuilder(builder: (context) => const ConfirmCertification()),
  PageRouteEnum.certificationProcessing.toString():
      PageBuilder(builder: (context) => const CertificationProcessing()),
  PageRouteEnum.certificationCompleted.toString():
      PageBuilder(builder: (context) => const CertificationCompleted()),
  PageRouteEnum.legalInformation.toString(): PageBuilder(
      builder: (context) => const LegalInformationPdfManualMenuPage()),
  PageRouteEnum.settingPdfPreview.toString():
      PageBuilder(builder: (context) => const SettingPdfPreview()),
  PageRouteEnum.inputActivationCode.toString():
      PageBuilder(builder: (context) => const InputActivationCode()),
  PageRouteEnum.wirelessLanStatus.toString():
      PageBuilder(builder: (context) => const WirelessLanStatus()),
  PageRouteEnum.networkResetComplete.toString():
      PageBuilder(builder: (context) => const NetworkResetComplete()),
  PageRouteEnum.userResetProcessing.toString():
      PageBuilder(builder: (context) => const UserResetProcessing()),
  PageRouteEnum.userResetComplete.toString():
      PageBuilder(builder: (context) => const UserResetCompleted()),
  PageRouteEnum.networkOthersSetting.toString():
      PageBuilder(builder: (_) => const NetworkOthersSettingPage()),

//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////mdc アプリ/////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
  PageRouteEnum.paint.toString():
      PageBuilder(builder: (context) => const PaintPage()),
  PageRouteEnum.stitch.toString():
      PageBuilder(builder: (context) => const StitchPage()),
  PageRouteEnum.scan.toString():
      PageBuilder(builder: (context) => const ScanPage()),
  PageRouteEnum.mdcFileSelector.toString():
      PageBuilder(builder: (context) => const MdcFileSelector()),
  PageRouteEnum.illustrationDesign.toString():
      PageBuilder(builder: (context) => const IllustrationDesignPage()),
  PageRouteEnum.illustrationDesignRetry.toString():
      PageBuilder(builder: (context) => const IllustrationDesignRetryPage()),
  PageRouteEnum.dropperTool.toString():
      PageBuilder(builder: (context) => const DropperToolPage()),
  PageRouteEnum.stitchRealPreview.toString(): PageBuilder(
      builder: (context) => const MdcRealPreview(type: RealPreviewType.mdc)),
  PageRouteEnum.lineDesign.toString():
      PageBuilder(builder: (context) => const LineDesignPage()),
  PageRouteEnum.lineDesignRetry.toString():
      PageBuilder(builder: (context) => const LineDesignRetryPage()),
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
/////////////////////Projector アプリ/////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
  PageRouteEnum.subScreen.toString():
      PageBuilder(builder: (context) => const SubScreen()),
  PageRouteEnum.subScreenDummy.toString():
      PageBuilder(builder: (context) => const SubScreenDummy()),
  PageRouteEnum.showProjectImage.toString():
      PageBuilder(builder: (context) => const ShowProjectImagePage()),
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
////////////////////////////debug ////////////////////////////
//////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
  PageRouteEnum.debugPage1.toString():
      PageBuilder(builder: (context) => const DebugPage(debugId: "1")),
};

/// 初期表示の画面
final String initialPageRoute = PageRouteEnum.initWait.toString();

/// 画面遷移用クラス
class PagesRoute {
  /// Singletonで作成
  factory PagesRoute() => _instance;
  PagesRoute._internal();
  static final PagesRoute _instance = PagesRoute._internal();

  PageNavigatorState get _pageNavigator =>
      PageNavigatorState(pageNavigatorKey.currentContext!);

  final DisplayManager _displayManager = DisplayManager.getInstance();

  ///
  /// 画面移行中に転送されるデータの取得
  ///
  @optionalTypeArgs
  T? getArgument<T extends Object?>({
    required BuildContext context,
  }) =>
      _pageNavigator.getArgument(context: context);

  ///
  /// 画面の名前を取得する
  ///
  @optionalTypeArgs
  PageRouteEnum getPageName({
    required BuildContext context,
  }) {
    /// 名前の接尾辞
    const String suffix = pageNameTail;

    /// pageNameの名前を取得する
    String pageName = PageNavigator.getName(context: context).toString();
    pageName = pageName.substring(0, (pageName.length) - (suffix.length));

    /// 列挙値を名前で取得する
    final pageRouteName = PageRouteEnum.praseByEnumString(pageName);
    if (pageRouteName != null) {
      return pageRouteName;
    }

    Log.fatal(
      tag: "PageRouteEnum",
      description: "Unknown pageRouteName:$pageName",
    );
    return PageRouteEnum.home;
  }

  ///
  /// 指定した画面に移行します(Widgetについて画面を作成して、遷移する)
  ///
  /// @param
  /// - nextRoute: 遷移先ルート
  /// - arguments: 返送可能な値
  ///
  @optionalTypeArgs
  Future<T?> push<T extends Object?>({
    required Widget nextPageWidget,
    Object? arguments,
  }) =>
      _pageNavigator.push(
        nextPage: nextPageWidget,
        arguments: arguments,
      );

  ///
  /// 指定した画面に移行します
  ///
  /// @param
  /// - nextRoute: 遷移先ルート
  /// - arguments: 返送可能な値
  ///
  @optionalTypeArgs
  Future<T?> pushNamed<T extends Object?>({
    required PageRouteEnum nextRoute,
    Object? arguments,
  }) =>
      _pageNavigator.pushNamed(
        nextRouteName: nextRoute,
        arguments: arguments,
      );

  ///
  /// 指定した画面に移行します、前の画面を破棄します
  ///
  /// @param
  /// - nextRoute: 遷移先ルート
  /// - arguments: 返送可能な値
  ///
  @optionalTypeArgs
  Future<T?> pushReplacement<T extends Object?, TO extends Object?>({
    required PageRouteEnum nextRoute,
    Object? arguments,
    TO? result,
  }) =>
      _pageNavigator.pushReplacementNamed(
        nextRouteName: nextRoute,
        arguments: arguments,
        result: result,
      );

  ///
  /// 指定した画面に移行します、指定したポップアップ画面に戻ります
  ///
  /// @param
  /// - nextRouteName: 遷移先ルート
  /// - untilRouteName:  戻る先ルート
  /// - arguments: 返送可能な値
  ///
  @optionalTypeArgs
  Future<T?> pushNamedAndRemoveUntil<T extends Object?>({
    required PageRouteEnum untilRoute,
    required PageRouteEnum nextRoute,
    Object? arguments,
  }) =>
      _pageNavigator.pushNamedAndRemoveUntil(
        nextRouteName: nextRoute,
        untilRouteName: untilRoute,
        arguments: arguments,
      );

  ///
  /// 指定した画面に移行します、前の全ての画面を破棄します
  ///
  /// @param
  /// - nextRoute: 遷移先ルート
  /// - arguments: 返送可能な値
  ///
  @optionalTypeArgs
  Future<T?> pushAndRemoveAll<T extends Object?>({
    required PageRouteEnum nextRoute,
    Object? arguments,
  }) =>
      _pageNavigator.pushNamedAndRemoveAll(
        nextRouteName: nextRoute,
        arguments: arguments,
      );

  ///
  /// 前の画面に戻ります
  ///
  /// @param
  /// - result: 返送可能な値
  ///
  @optionalTypeArgs
  void pop<T extends Object?>({T? result}) {
    _pageNavigator.pop(result: result);
  }

  ///
  /// 指定した画面に戻ります
  ///
  /// @param
  /// - nextRoute: 戻る先ルート
  ///
  void popUntil({required PageRouteEnum nextRoute}) {
    _pageNavigator.popUntil(nextRouteName: nextRoute);
  }
}

///
/// SecondaryDisplayコントロール
///
/// PagesRouteの拡張として追加します
///
extension SecondaryDisplayExtension on PagesRoute {
  ///
  /// セカンダリ画面にジャンプします
  ///
  /// @param
  /// - [subScreenRoute] : 表示したいのルート
  /// - [method] : コマンド
  /// - [waitFirstFrameFinish] : [SecondDisplayViewMethodEnum.initProjector]の時に、screen first frame待ちます
  ///
  @optionalTypeArgs
  Future<bool> pushSubScreen<T extends Object?>({
    required PageRouteEnum subScreenRoute,
    required SecondDisplayViewMethodEnum method,
    bool waitFirstFrameFinish = false,
  }) async {
    if (!Platform.isAndroid) {
      return false;
    }

    /// second displayへメッセージを送信する
    if (method != SecondDisplayViewMethodEnum.initProjector) {
      Log.d(tag: "pushSubScreen", description: "transferData:$method");
      final ret =
          await _displayManager.sendViewMethodToPresentation(method.method) ??
              false;
      return ret;
    }

    /// screen確認
    final List<Display?> displays = await _displayManager.getDisplays() ?? [];
    if (displays.length <= 1 || displays.last == null) {
      Log.e(tag: "pushSubScreen", description: "only find one screen");
      return false;
    }

    /// second displayを開ける
    final int displayId = displays.last!.displayId;
    bool result = await _displayManager.showSecondaryDisplay(
          displayId: displayId,
          routerName: subScreenRoute.toString(),
        ) ??
        false;

    /// screen確認
    if (waitFirstFrameFinish == true && result != false) {
      Log.d(tag: "pushSubScreen", description: "waitFirstFrameFinish start");
      await Future.doWhile(() async {
        final bool? ret = await _displayManager
            .sendViewMethodToPresentation(
                SecondDisplayCmdMethodEnum.checkFirstFrame.method)
            .timeout(const Duration(milliseconds: 100), onTimeout: () => null);

        Log.d(
            tag: "pushSubScreen",
            description: "waitFirstFrameFinish status:$ret");
        if (ret == true) {
          /// finish

          return false;
        } else {
          await Future.delayed(const Duration(milliseconds: 50));
          return true;
        }
      }).timeout(
        const Duration(minutes: 1),
        onTimeout: () {
          result = false;
          Log.e(
              tag: "pushSubScreen",
              description: "wait FirstFrame finish timeout");
        },
      );
      Log.d(tag: "pushSubScreen", description: "waitFirstFrameFinish finish");
    }

    return result;
  }

  ///
  /// プロジェクト投影画面初期化
  ///
  Future<bool> initProjectorScreen() async {
    /// screen確認
    final List<Display?> displays = await _displayManager.getDisplays() ?? [];
    if (displays.length <= 1 || displays.last == null) {
      Log.e(
          tag: "initProjectorScreen",
          description: "projector screen is not exist");
      return false;
    }

    /// second displayを開ける
    final int displayId = displays.last!.displayId;
    bool result = await _displayManager.showSecondaryDisplay(
          displayId: displayId,
          routerName: PageRouteEnum.subScreenDummy.toString(),
        ) ??
        false;

    if (result == false) {
      Log.e(
          tag: "initProjectorScreen",
          description: "start projector screen failed");
      return false;
    }

    /// screen確認
    Log.d(tag: "pushSubScreen", description: "waitFirstFrameFinish start");
    await Future.doWhile(() async {
      final bool? ret = await _displayManager
          .sendViewMethodToPresentation(
              SecondDisplayCmdMethodEnum.checkFirstFrame.method)
          .timeout(const Duration(milliseconds: 100), onTimeout: () => null);

      Log.d(
          tag: "pushSubScreen",
          description: "waitFirstFrameFinish status:$ret");
      if (ret == true) {
        /// finish
        return false;
      } else {
        await Future.delayed(const Duration(milliseconds: 50));
        return true;
      }
    }).timeout(
      const Duration(minutes: 1),
      onTimeout: () {
        result = false;
        Log.e(
            tag: "pushSubScreen",
            description: "wait FirstFrame finish timeout");
      },
    );

    Log.d(tag: "pushSubScreen", description: "waitFirstFrameFinish finish");

    return result;
  }

  ///
  /// プロジェクト画面表示
  ///
  static final Lock _openAndCloseLock = Lock();
  Future<bool> showPageInProjectorScreen(PageRouteEnum pageName) {
    return _openAndCloseLock.synchronized(() async {
      return await sendCmdMethodToSecondDisplay(
        SecondDisplayCmdMethodEnum.pushAndRemoveAll,
        argument: pageName.name,
      );
    });
  }

  ///
  /// 投影demoModeに必要なImage画像
  ///
  Future<bool> showDemoModePageInProjectorScreen(String usbPath) {
    return _openAndCloseLock.synchronized(() async {
      return await sendCmdMethodToSecondDisplay(
        SecondDisplayCmdMethodEnum.demoModeImage,
        argument: usbPath,
      );
    });
  }

  ///
  /// プロジェクト画面表示閉じる
  ///
  Future<bool> closePageInProjectorScreen() {
    return _openAndCloseLock.synchronized(() async {
      return await sendCmdMethodToSecondDisplay(
        SecondDisplayCmdMethodEnum.removeAll,
      );
    });
  }

  Future<bool> sendViewMethodToSecondDisplay(
      {required SecondDisplayViewMethodEnum method}) async {
    /// second displayへメッセージを送信する
    Log.d(
        tag: "pushSubScreen",
        description: "sendViewMethodToPresentation:$method");
    final ret =
        await _displayManager.sendViewMethodToPresentation(method.method) ??
            false;
    return ret;
  }

  Future<bool> sendCmdMethodToSecondDisplay(SecondDisplayCmdMethodEnum method,
      {String argument = ""}) async {
    /// second displayへメッセージを送信する
    Log.d(
        tag: "pushSubScreen",
        description: "sendCmdMethodToPresentation:$method");
    final ret = await _displayManager.sendCmdMethodToPresentation(
            method.method, argument) ??
        false;
    return ret;
  }

  ///
  /// SecondaryDisplayを閉じる
  ///
  /// @param
  /// - displayId: オフにする画面Id
  ///
  Future<bool> closeProjectorScreen([int displayId = 1]) async {
    if (!Platform.isAndroid) {
      return false;
    }

    final bool result =
        await _displayManager.closeSecondaryDisplay(displayId: displayId) ??
            false;

    return result;
  }
}

///
/// SecondDisplayCmd
///
/// 画面コマンド
///
/// 一応下記定義を追加して、どんどん切り替え
///
enum SecondDisplayViewMethodEnum {
  initProjector("initProjector"),
  startAutoStop("startAutoStop"),
  pauseAutoStop("pauseAutoStop"),
  resumeAutoStop("resumeAutoStop"),
  stopAutoStop("stopAutoStop"),
  refresh("refresh"),
  off("off"),
  ;

  const SecondDisplayViewMethodEnum(this.method);
  final String method;
}

///
/// SecondDisplayCmd
///
/// 全体コマンド
///
enum SecondDisplayCmdMethodEnum {
  checkFirstFrame("checkFirstFrame"),
  pushAndRemoveAll("pushAndRemoveAll"),
  removeAll("removeAll"),
  demoModeImage("demoModeImage"),
  test("test"),
  ;

  const SecondDisplayCmdMethodEnum(this.method);
  final String method;
}
