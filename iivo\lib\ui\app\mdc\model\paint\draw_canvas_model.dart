import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:panel_library/panel_library.dart';

import '../color_table.dart';
import '../resume_history_model.dart';
import 'clip_board_model.dart';
import 'edit_object_model.dart';
import 'library_isolate.dart';
import 'magnification_model.dart';
import 'pen_model.dart';
import 'scan_model.dart';
import 'top_bar_model.dart';

///
/// ペン先の形状列挙
///
enum NibShape {
  dot,
  circle,
  square,
}

///
/// ペン先
///
enum NibType {
  /// 「線」属性用のペン先
  outLine,

  ///「面」属性用のペン先
  brush,

  /// 「消しゴム」用のペン先
  eraser,
}

class CanvasController extends ChangeNotifier {
  Paint _uiPaint = Paint();

  /// 使用中の画像
  ui.Image? _image;
  ui.Image? _backUpImage;
  ui.Image? get backUpImage => _backUpImage;

  /// 丸場合のPath
  final Path _uiPath = Path();
  Path _bckUiPath = Path();
  Path get uiPath => _uiPath;
  Path get bckUiPath => _bckUiPath;

  /// 四角場合のPath
  final List<Offset> _squarePoints = [];
  final List<Offset> _bckSquarePoints = [];
  List<Offset> get squarePoints => _squarePoints;
  List<Offset> get bckSquarePoints => _bckSquarePoints;
  bool hasSquarePoints() => _squarePoints.isNotEmpty;
  bool hasBckSquarePoints() => _bckSquarePoints.isNotEmpty;

  /// 各線の開始位置
  /// （canvasの場合、開始位置描画できないため、各開始位置保存する）
  final List<Offset> _firstPoint = [];
  final List<Offset> _bckFirstPoint = [];
  List<Offset> get firstPoints => _firstPoint;
  List<Offset> get bckFirstPoint => _bckFirstPoint;
  bool hasFirstPoint() => _firstPoint.isNotEmpty;
  bool hasBckFirstPoint() => _bckFirstPoint.isNotEmpty;

  CanvasPenState get penState => DrawCanvasModel().penState;

  /// 線終了のフラグ
  bool isPathEmpty = true;
  Ticker? _paintTicker;

  set firstPoint(Offset point) {
    _firstPoint.add(point);
    notifyListeners();
  }

  void removeFirstPoint() {
    if (_firstPoint.isNotEmpty) {
      _firstPoint.clear();
      notifyListeners();
    }
  }

  ///
  /// 前回描画結果保存する
  ///
  void setBackUpImage(ui.Image? image) {
    _backUpImage?.dispose();
    _backUpImage = image?.clone();
  }

  ///
  /// 描画用Paint設定する
  ///
  set uiPaint(Paint paint) {
    _uiPaint = paint;
    notifyListeners();
  }

  ///
  /// 描画用Paint取得する
  ///
  Paint get uiPaint => _uiPaint;

  ///
  /// ラスタライズされた画像を更新する
  ///
  set image(ui.Image? image) {
    _image?.dispose();
    _image = image;
    notifyListeners();
  }

  ///
  /// 描画中画像取得する
  ///
  ui.Image? get backgroundImage => _image;

  ///
  /// Ticker実行中の判断
  ///
  bool get isTickerActive => _paintTicker?.isActive ?? false;

  ///
  /// ラスタライズ過程で生成された点を収集する
  ///
  void addPoint(Offset offset) {
    if (isPathEmpty) {
      _uiPath.moveTo(offset.dx, offset.dy);
      isPathEmpty = false;
    } else {
      _uiPath.lineTo(offset.dx, offset.dy);
    }

    /// 四角の場合
    final nib = DrawCanvasModel().penState.nib;
    final type = DrawCanvasModel().penState.type;
    _squarePoints.clear();
    if (nib[type.index].shape == NibShape.square) {
      _getSquarePointsFromPath(_uiPath, _squarePoints);
    }
    notifyListeners();
  }

  ///
  /// ラスタライズ過程で生成された点をクリア
  ///
  void resetPath() {
    _uiPath.reset();
    isPathEmpty = true;
    notifyListeners();
  }

  ///
  /// Ticker停止
  ///
  void stop() {
    if (_paintTicker != null) {
      notifyListeners();
      _paintTicker?.stop();
    }
  }

  ///
  /// Ticker起動
  ///
  void start() {
    if (_paintTicker?.isActive == false && _paintTicker != null) {
      _paintTicker?.start();
    }
  }

  ///
  /// MDCお絵かき内容を保存する
  ///
  void backUpImageInfo() {
    if (_image == null) {
      _backUpImage = null;
      _bckFirstPoint.clear();
      _bckSquarePoints.clear();
      _bckUiPath.reset();
      return;
    }

    /// 描画したImage保存
    _backUpImage?.dispose();
    _backUpImage = _image?.clone();

    /// 描画した各線の開始位置リストの保存
    _bckFirstPoint.clear();
    _bckFirstPoint.addAll(_firstPoint);

    /// 丸場合、描画したPath保存
    _bckUiPath.reset();
    _bckUiPath = Path.from(_uiPath);

    /// 四角場合、描画したPath保存
    _bckSquarePoints.clear();
    _bckSquarePoints.addAll(_squarePoints);
  }

  ///
  /// 描画したの内容をクリアする
  ///
  void clearImageInfo() {
    _firstPoint.clear();
    _uiPath.reset();
    _squarePoints.clear();
    isPathEmpty = true;
    notifyListeners();
  }

  ///
  /// Ticker初期化
  ///
  void initTicker() {
    _paintTicker?.stop();
    _paintTicker = Ticker(_onTicker);
    _paintTicker!.start();
  }

  ///
  /// リセット
  ///
  void reset() {
    _paintTicker?.stop();

    _uiPath.reset();
    _bckUiPath.reset();

    _firstPoint.clear();
    _bckFirstPoint.clear();

    _squarePoints.clear();
    _bckSquarePoints.clear();

    _image?.dispose();
    _image = null;
    _backUpImage?.dispose();
    _backUpImage = null;

    isPathEmpty = true;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();

    _paintTicker?.stop();
    _paintTicker?.dispose();
    _paintTicker = null;

    _image?.dispose();
    _image = null;
    _backUpImage = null;

    _uiPath.reset();
    _bckUiPath.reset();

    _squarePoints.clear();
    _bckSquarePoints.clear();

    _firstPoint.clear();
    _bckFirstPoint.clear();

    isPathEmpty = true;
  }

  ///
  /// TickerのCallback
  ///
  void _onTicker(Duration elapsed) {
    notifyListeners();
  }

  ///
  /// Pathからペン先の座標を取得する
  ///
  void _getSquarePointsFromPath(Path path, List<Offset> offsets) {
    for (PathMetric pathMetric in path.computeMetrics()) {
      for (int i = 0; i < pathMetric.length; i++) {
        Tangent? tangent = pathMetric.getTangentForOffset(i.toDouble());
        if (tangent != null) {
          offsets.add(tangent.position);
        }
      }
    }
  }
}

///
/// 画面描画状態
///
class CanvasPenState {
  CanvasPenState.init()
      : position = CanvasLinePoint.init(),
        lineStatus = PointListStatus.init(),
        nib = List.filled(3, MdcNib()),
        type = NibType.outLine,
        nearArea = nearAreaDefault;

  CanvasPenState({
    required this.position,
    required this.lineStatus,
    required this.nib,
    this.type = NibType.outLine,
    this.nearArea = nearAreaDefault,
  }) {
    nib = List.filled(3, MdcNib());
    for (int i = 0; i < nib.length; i++) {
      nib[i] = MdcNib.init();
    }
  }

  CanvasLinePoint position;
  PointListStatus lineStatus;
  double nearArea;
  List<MdcNib> nib;
  NibType type;

  final Color whiteFrameColor = eraseAndNoSewColor;
  final Color redFrameColor = const Color.fromARGB(255, 255, 121, 126);
  final double whiteFrameWidth = 2.0;
  final double redFrameWidth = 1.0;
  static const double nearAreaDefault = 50;
  static const int shapeDotThickness = 1;

  ui.Image? _imageShader;

  ///
  /// 描画用Paint
  ///
  final Paint _paint = Paint()
    ..style = PaintingStyle.stroke
    ..isAntiAlias = false
    ..filterQuality = FilterQuality.none;

  /// 画像更新間隔
  static const _imageRateLimitInterval = 1000;

  /// 透過色が必要ですか
  bool isNeedTransparent = false;

  /// 消しゴムモードかどうか
  bool get isEraserMode => type == NibType.eraser || isNeedTransparent;

  CanvasController get canvasController => DrawCanvasModel().canvasController;

  ///
  /// 指定したペン先を更新する
  ///
  void updateNib(NibType updateType, MdcNib updateNib) {
    type = updateType;
    nib[updateType.index] = updateNib;
  }

  ///
  /// 指定したペン先用の色情報を更新する
  ///
  Color getNibColor(NibType kind, bool isTransparent, ColorCode colorCode) {
    isNeedTransparent = isTransparent;

    final index = colorCodeList.indexOf(colorCode);
    return getPaletteColors(isTransparent)[index];
  }

  ///
  /// ペンシル背景更新
  ///
  Future<void> updateUiDrawingShader(int targetKind, int targetNumber) async {
    var result = MdcLibrary().apiBinding.getMdcStipplingDecofillMaxDrawingImage(
          targetKind,
          targetNumber,
        );

    if (result.errorCode == MdcLibraryError.mdcNoError) {
      removeUiDrawingShader();
      _imageShader = await _loadImage(result.imageInfo.imageData);
      _paint.shader?.dispose();
      _paint.shader = ImageShader(_imageShader!, TileMode.clamp, TileMode.clamp,
          Matrix4.identity().storage);
    } else {
      removeUiDrawingShader();
    }
  }

  ///
  /// 設定したshaderを削除する
  ///
  void removeUiDrawingShader() {
    _imageShader?.dispose();
    _imageShader = null;
    _paint.shader?.dispose();
    _paint.shader = null;
  }

  ///
  /// 最後の保存画像のタイムスタンプ
  ///
  int lastSaveImageTimestamp = 0;

  ///
  /// 開始座標描画
  ///
  void saveFirstPointAsImage(Offset point) {
    canvasController.firstPoint = point;
    canvasController.addPoint(point);
  }

  /// 画像描画用Paint
  final imagePaint = Paint()
    ..isAntiAlias = false
    ..filterQuality = FilterQuality.none;

  ///
  /// 「Canvas」で指定された領域に直線を引く
  ///
  void savePointAsImage(Offset point) {
    canvasController.addPoint(point);

    /// 1sぐらい時間計算
    if (_isIntervalTime() == false) {
      return;
    }

    /// UI描画のPaint更新
    canvasController.uiPaint = _paint;

    final recorder = PictureRecorder();
    final canvas = Canvas(
      recorder,
      Rect.fromPoints(Offset.zero,
          const Offset(PenModel.baseImageWidth, PenModel.baseImageHeight)),
    );

    /// 前回イメージ描画
    ui.Image? previousImage = canvasController.backgroundImage;
    if (previousImage != null) {
      canvas.drawImage(previousImage, Offset.zero, imagePaint);
    } else {
      // 背景画像がない場合は何もしない
    }

    /// 保存した開始点描画
    canvas.drawPoints(PointMode.points, canvasController.firstPoints, _paint);

    /// 線描画
    Path drawPath = Path.from(canvasController.uiPath);
    if (nib[type.index].shape == NibShape.square) {
      final offsets = <Offset>[];
      canvasController._getSquarePointsFromPath(drawPath, offsets);
      canvas.drawPoints(PointMode.points, offsets, _paint);
    } else {
      canvas.drawPath(canvasController.uiPath, _paint);
    }

    /// 描画画像取得する
    final picture = recorder.endRecording();
    final currentImage = picture.toImageSync(
      PenModel.baseImageWidth.toInt(),
      PenModel.baseImageHeight.toInt(),
    );
    picture.dispose();

    /// 画像更新
    canvasController.image = currentImage;
    canvasController.removeFirstPoint();
    canvasController.resetPath();
    canvasController.addPoint(point);
  }

  ///
  /// 線移動Path保存
  ///
  void saveLineAsPath(Offset start, Offset end) {
    canvasController.resetPath();
    canvasController.addPoint(start);
    canvasController.addPoint(end);
  }

  ///
  /// Lineの描画画像作成
  ///
  void saveLineAsImage() {
    final recorder = PictureRecorder();
    final canvas = Canvas(
      recorder,
      Rect.fromPoints(Offset.zero,
          const Offset(PenModel.baseImageWidth, PenModel.baseImageHeight)),
    );

    /// 前回イメージ描画
    ui.Image? previousImage = canvasController.backgroundImage;
    if (previousImage != null) {
      canvas.drawImage(previousImage, Offset.zero, _paint);
    }

    canvas.drawPath(canvasController.uiPath, _paint);

    /// 描画画像取得する
    final picture = recorder.endRecording();
    final image = picture.toImageSync(
      PenModel.baseImageWidth.toInt(),
      PenModel.baseImageHeight.toInt(),
    );
    picture.dispose();

    /// 画像更新
    canvasController.image = image;
    canvasController.resetPath();
  }

  ///
  /// 描画のPaint更新
  ///
  void preparePaint() {
    final int index = TopBarModel().densityLevelIndex;
    final double opacity = ScanModel().mdcDensityLevel[index];

    _paint
      ..strokeWidth = nib[type.index].thickness.toDouble()
      ..color = nib[type.index].color.withOpacity(opacity);

    final isSquare = nib[type.index].shape == NibShape.square;
    _paint
      ..strokeCap = isSquare ? StrokeCap.square : StrokeCap.round
      ..strokeJoin = isSquare ? StrokeJoin.miter : StrokeJoin.round;

    if (isEraserMode) {
      _paint.blendMode = BlendMode.clear;
    } else {
      _paint.blendMode = BlendMode.src;
    }

    canvasController.uiPaint = _paint;
  }

  ///
  /// 描画完了
  ///
  void finishLineDraw() {
    /// 各情報初期化
    position = CanvasLinePoint.init();
    lineStatus = PointListStatus.init();

    canvasController.isPathEmpty = true;
    canvasController.stop();
  }

  ///
  /// 前回保存したのイメージを戻る
  ///
  void restoreBackUp() {
    if (canvasController.backUpImage == null) {
      canvasController.reset();
      EditObjectModel().setMdcBothImageInfo(
          updateParts: MdcImageInfo.empty(),
          updateBackground: MdcImageInfo.empty(),
          needUpdateUI: true);
      return;
    }

    Paint paint = _paint;
    paint.blendMode = BlendMode.src;
    PictureRecorder recorder = PictureRecorder();
    Canvas canvas = Canvas(
      recorder,
      Rect.fromPoints(Offset.zero,
          const Offset(PenModel.baseImageWidth, PenModel.baseImageHeight)),
    );

    /// 前回イメージ描画
    canvas.drawImage(canvasController.backUpImage!, Offset.zero, paint);

    /// 前回線開始点描画
    if (canvasController.hasBckFirstPoint()) {
      canvas.drawPoints(
          PointMode.points, canvasController.bckFirstPoint, paint);
    }

    /// 前回四角点リスト描画
    if (canvasController.hasBckSquarePoints()) {
      canvas.drawPoints(
          PointMode.points, canvasController.bckSquarePoints, paint);
    }

    /// 前回丸点リスト描画
    else {
      canvas.drawPath(canvasController.bckUiPath, paint);
    }

    /// 描画画像取得する
    var picture = recorder.endRecording();
    final image = picture.toImageSync(
      PenModel.baseImageWidth.toInt(),
      PenModel.baseImageHeight.toInt(),
    );
    picture.dispose();

    /// 画像更新
    canvasController.image = image;
    lastSaveImageTimestamp = DateTime.timestamp().millisecondsSinceEpoch;
  }

  ///
  /// Uint8ListからImageになります
  ///
  Future<ui.Image> _loadImage(Uint8List data) async {
    return await decodeImageFromList(data);
  }

  bool _isIntervalTime() {
    /// 1sぐらい時間計算
    final currentTimestamp = DateTime.timestamp().millisecondsSinceEpoch;
    if (currentTimestamp - lastSaveImageTimestamp < _imageRateLimitInterval) {
      return false;
    } else {
      lastSaveImageTimestamp = currentTimestamp;
      return true;
    }
  }
}

///
/// 描画した座標
///
class CanvasLinePoint {
  CanvasLinePoint.init()
      : firstPosition = invalidPosition,
        secondPosition = invalidPosition,
        lastPosition = invalidPosition,
        prePosition = invalidPosition;

  CanvasLinePoint({
    this.firstPosition = invalidPosition,
    this.secondPosition = invalidPosition,
    this.lastPosition = invalidPosition,
    this.prePosition = invalidPosition,
  });
  Offset firstPosition;
  Offset secondPosition;
  Offset lastPosition;
  Offset prePosition;

  static const double invalidValue = -1.0;
  static const Offset invalidPosition = Offset(invalidValue, invalidValue);
}

class MdcNib {
  MdcNib.init()
      : type = MdcLineToolTypes.freeOpen,
        shape = NibShape.dot,
        thickness = 1,
        color = Colors.black;

  MdcNib({
    this.type = MdcLineToolTypes.freeOpen,
    this.shape = NibShape.dot,
    this.thickness = 1,
    this.color = Colors.black,
  });

  /// ペン先に指定されたぬい方情報
  MdcLineToolTypes type;

  /// ペン先の形状
  NibShape shape;

  ///  ペン先の太さ (px) ※Shape=SHAPE_DOTの場合は無視
  int thickness;

  /// 現在選択されている糸色
  Color color;
}

///
/// Lib入力座標情報
///
class PointListStatus {
  PointListStatus.init() : positionList = [];

  PointListStatus({
    List<SSPoint>? positionList,
  }) : positionList = positionList ?? [];

  List<SSPoint> positionList;
}

const Color eraseAndNoSewColor = Color.fromARGB(255, 238, 238, 238);

class DrawCanvasModel {
  DrawCanvasModel._internal();

  factory DrawCanvasModel() => _instance;
  static final DrawCanvasModel _instance = DrawCanvasModel._internal();

  CanvasPenState penState = CanvasPenState.init();

  ///
  /// CallBack関数
  ///
  Function? updateFrameAndGridCallback;
  Function? updateBottomCallback;
  Function? updateUiImageInfoCallBack;

  ///
  /// 線描画Controller
  ///
  late CanvasController canvasController;

  ///
  /// 領域選択Press
  ///
  void press(Offset pos, MdcLineToolTypes type) {
    canvasController.backUpImageInfo();
    penState.preparePaint();

    /// 描画Ticker起動
    canvasController.start();

    /// Lib用のPointリスト更新
    penState.lineStatus.positionList
        .add(SSPoint(X: pos.dx.toInt(), Y: pos.dy.toInt()));

    /// 開始座標を記録する
    switch (type) {
      case MdcLineToolTypes.freeOpen:
        penState.position
          ..firstPosition = CanvasLinePoint.invalidPosition
          ..secondPosition = CanvasLinePoint.invalidPosition
          ..prePosition = pos
          ..lastPosition = pos;

        penState.saveFirstPointAsImage(pos);
        break;

      case MdcLineToolTypes.lineOpen:
        penState.position
          ..firstPosition = CanvasLinePoint.invalidPosition
          ..secondPosition = CanvasLinePoint.invalidPosition
          ..prePosition = pos
          ..lastPosition = pos;
        break;

      case MdcLineToolTypes.freeClose:
        penState.position
          ..firstPosition = pos
          ..secondPosition = pos
          ..prePosition = pos
          ..lastPosition = pos;

        penState.saveFirstPointAsImage(pos);
        break;

      case MdcLineToolTypes.lineClose:
        if (penState.position.firstPosition ==
            CanvasLinePoint.invalidPosition) {
          penState.position
            ..firstPosition = pos
            ..secondPosition = CanvasLinePoint.invalidPosition
            ..prePosition = pos
            ..lastPosition = pos;
        } else {
          penState.saveLineAsPath(penState.position.prePosition, pos);
          penState.position.prePosition = pos;
        }
        break;

      default:
        penState.finishLineDraw();
        return;
    }
  }

  ///
  /// 領域選択Move
  ///
  void move(Offset pos, MdcLineToolTypes type) {
    penState.preparePaint();

    /// 状態更新
    /// 開始座標を記録する
    switch (type) {
      case MdcLineToolTypes.freeOpen:
      case MdcLineToolTypes.freeClose:
        penState.lineStatus.positionList
            .add(SSPoint(X: pos.dx.toInt(), Y: pos.dy.toInt()));

        penState.savePointAsImage(pos);
        penState.position
          ..prePosition = pos
          ..lastPosition = pos;
        break;
      case MdcLineToolTypes.lineOpen:
        if (penState.lineStatus.positionList.length >= 2) {
          penState.lineStatus.positionList.last =
              SSPoint(X: pos.dx.toInt(), Y: pos.dy.toInt());
          penState.saveLineAsPath(penState.position.lastPosition, pos);
        } else {
          penState.lineStatus.positionList
              .add(SSPoint(X: pos.dx.toInt(), Y: pos.dy.toInt()));
        }

        penState.position.prePosition = pos;
        break;

      case MdcLineToolTypes.lineClose:
        if (penState.lineStatus.positionList.length >= 2) {
          penState.lineStatus.positionList.last =
              SSPoint(X: pos.dx.toInt(), Y: pos.dy.toInt());
        } else {
          penState.lineStatus.positionList
              .add(SSPoint(X: pos.dx.toInt(), Y: pos.dy.toInt()));
        }

        penState.position.prePosition = pos;
        penState.saveLineAsPath(penState.position.lastPosition, pos);
        break;

      default:
        penState.finishLineDraw();
        return;
    }
  }

  ///
  /// 領域選択release
  ///
  void release(Offset offset, MdcLineToolTypes type) {
    switch (type) {
      case MdcLineToolTypes.lineClose:
        if (penState.position.secondPosition ==
            CanvasLinePoint.invalidPosition) {
          penState.position
            ..secondPosition = penState.position.lastPosition
            ..lastPosition = penState.position.prePosition;
          penState.saveLineAsImage();

          /// Lib通知
          LibraryIsolate().isolateDoApi(penState.lineStatus);
          ResumeHistoryModel().addUserHistoryList(penState, isLastPoint: false);
        } else {
          int magRatio = MagnificationModel.magnificationLevelList
              .indexOf(MagnificationModel.magnificationLevel);
          double nearArea = MagnificationModel.magnificationLevel <
                  MagnificationModel.magnification_200
              ? CanvasPenState.nearAreaDefault
              : CanvasPenState.nearAreaDefault / (magRatio * 2);

          Rect frameRect = Rect.fromLTRB(
            penState.position.firstPosition.dx - nearArea.truncate(),
            penState.position.firstPosition.dy - nearArea.truncate(),
            penState.position.firstPosition.dx + nearArea.truncate(),
            penState.position.firstPosition.dy + nearArea.truncate(),
          );

          if (frameRect.contains(penState.position.prePosition)) {
            penState.lineStatus.positionList.add(SSPoint(
              X: penState.position.firstPosition.dx.toInt(),
              Y: penState.position.firstPosition.dy.toInt(),
            ));
            PointListStatus lineStatus = penState.lineStatus;

            /// 閉口線を追加する
            penState.saveLineAsPath(penState.position.lastPosition,
                penState.position.firstPosition);
            penState.saveLineAsImage();

            /// Lib通知
            LibraryIsolate().isolateDoApi(lineStatus);
            ResumeHistoryModel()
                .addUserHistoryList(penState, isLastPoint: true);

            /// 描画終了
            penState.finishLineDraw();
          } else {
            PointListStatus lineStatus = PointListStatus.init();
            lineStatus.positionList.add(SSPoint(
              X: penState.position.prePosition.dx.toInt(),
              Y: penState.position.prePosition.dy.toInt(),
            ));
            lineStatus.positionList.add(SSPoint(
              X: penState.position.lastPosition.dx.toInt(),
              Y: penState.position.lastPosition.dy.toInt(),
            ));

            penState.saveLineAsImage();
            penState.position.lastPosition = penState.position.prePosition;
            LibraryIsolate().isolateDoApi(lineStatus);
            ResumeHistoryModel()
                .addUserHistoryList(penState, isLastPoint: false);
          }
        }
        break;
      case MdcLineToolTypes.freeOpen:
        penState.lastSaveImageTimestamp = 0;
        penState.savePointAsImage(penState.position.lastPosition);
        LibraryIsolate().isolateDoApi(penState.lineStatus);

        /// 描画終了
        penState.finishLineDraw();
        break;
      case MdcLineToolTypes.lineOpen:
        penState.saveLineAsImage();
        LibraryIsolate().isolateDoApi(penState.lineStatus);

        /// 描画終了
        penState.finishLineDraw();
        break;
      case MdcLineToolTypes.freeClose:
        penState.lineStatus.positionList.add(SSPoint(
          X: penState.position.firstPosition.dx.toInt(),
          Y: penState.position.firstPosition.dy.toInt(),
        ));
        PointListStatus lineStatus = penState.lineStatus;

        /// Lib通知
        LibraryIsolate().isolateDoApi(lineStatus);

        /// 線描画
        penState.saveLineAsImage();

        /// 閉口線を追加する
        penState.saveLineAsPath(
            penState.position.lastPosition, penState.position.firstPosition);
        penState.saveLineAsImage();

        /// 描画終了
        penState.finishLineDraw();

        break;
      default:
        penState.finishLineDraw();
        break;
    }
  }

  ///
  /// 開始点に繋げられる範囲
  ///
  double getDisplayNearArea() {
    double nearArea = 0;
    if (MagnificationModel.magnificationLevel ==
        MagnificationModel.magnification_100) {
      nearArea = CanvasPenState.nearAreaDefault;
    } else {
      int magRatio = MagnificationModel.magnificationLevelList
          .indexOf(MagnificationModel.magnificationLevel);
      nearArea = CanvasPenState.nearAreaDefault / (magRatio * 2) + 1;
    }

    final List<double> scaleList = [0.5, 1, 2, 4, 8];
    double currentScale = scaleList[MagnificationModel().valueToIndex()];

    return nearArea.truncate() * currentScale;
  }

  ///
  /// 初期化
  ///
  void initUiDrawingPen() {
    ///「線」属性用のペン先
    var lineProperty =
        MdcLibrary().apiBinding.getMdcEditLineProperty().editLineProperty;
    MdcNib lineNib = MdcNib.init();
    lineNib.type = lineProperty.lineType;
    lineNib.shape = NibShape.dot;
    lineNib.thickness = 1;
    if (lineProperty.lineKind == MdcSewingKindsLine.noSew) {
      lineNib.color = eraseAndNoSewColor;
    } else {
      int colorIndex = colorCodeList.indexOf(lineProperty.color);
      lineNib.color = getPaletteColors(false)[colorIndex];
    }
    penState.nib[NibType.outLine.index] = lineNib;

    ///「面」属性用のペン先
    var surfaceProperty =
        MdcLibrary().apiBinding.getMdcEditSurfaceProperty().editSurfaceProperty;
    MdcNib brushNib = MdcNib.init();
    brushNib.type = MdcLineToolTypes.freeOpen;
    brushNib.shape = surfaceProperty.brushType == MdcBrushToolTypes.square
        ? NibShape.square
        : NibShape.circle;
    brushNib.thickness = surfaceProperty.brushSize;
    if (surfaceProperty.kind == MdcSewKindsSurface.noSew) {
      brushNib.color = Colors.transparent;
    } else {
      int colorIndex = colorCodeList.indexOf(surfaceProperty.color);
      brushNib.color = getPaletteColors(false)[colorIndex];
    }
    penState.nib[NibType.brush.index] = brushNib;

    ///「消しゴム」用のペン先
    var eraserProperty =
        MdcLibrary().apiBinding.getMdcEditEraserProperty().editEraserProperty;
    MdcNib eraserNib = MdcNib.init();
    eraserNib.type = MdcLineToolTypes.freeOpen;
    eraserNib.shape = eraserProperty.type == MdcEraserToolTypes.square
        ? NibShape.square
        : NibShape.circle;
    eraserNib.thickness = eraserProperty.size;
    eraserNib.color = Colors.transparent;
    penState.nib[NibType.eraser.index] = eraserNib;
  }

  ///
  /// 面パラメータ更新
  ///
  void updateBrushNib() {
    var property =
        MdcLibrary().apiBinding.getMdcEditSurfaceProperty().editSurfaceProperty;

    /// 指定したペン先を更新する
    Color color;
    if (property.kind != MdcSewKindsSurface.noSew) {
      color = DrawCanvasModel()
          .penState
          .getNibColor(NibType.brush, false, property.color);
    } else {
      DrawCanvasModel().penState.isNeedTransparent = true;
      color = Colors.transparent;
    }

    final newNib = MdcNib(
      type: MdcLineToolTypes.freeOpen,
      shape: property.brushType == MdcBrushToolTypes.square
          ? NibShape.square
          : NibShape.circle,
      color: color,
      thickness: property.brushSize,
    );
    penState.updateNib(NibType.brush, newNib);
    ClipBoardModel().reset();

    if (property.kind == MdcSewKindsSurface.stipring ||
        property.kind == MdcSewKindsSurface.decorativeFill) {
      var targetKind =
          MdcMaxDrawingImageTarget.mdcMaxDrawingImageTarget_Stippling;
      if (property.kind == MdcSewKindsSurface.decorativeFill) {
        if (property.decorativeType == false) {
          targetKind =
              MdcMaxDrawingImageTarget.mdcMaxDrawingImageTarget_BuiltInDecofill;
        } else {
          targetKind =
              MdcMaxDrawingImageTarget.mdcMaxDrawingImageTarget_CustomDecofill;
        }
      }

      penState.updateUiDrawingShader(targetKind.index, property.decorativeNo);
    } else {
      penState.removeUiDrawingShader();
    }

    /// Lib更新する
    MdcLibrary().apiBinding.setMdcEditSurfaceProperty(property);
  }

  ///
  /// 線パラメータ更新
  ///
  void updateOutLineNib() {
    var property =
        MdcLibrary().apiBinding.getMdcEditLineProperty().editLineProperty;

    /// 指定したペン先を更新する
    Color color;
    if (property.lineKind != MdcSewingKindsLine.noSew) {
      color = DrawCanvasModel()
          .penState
          .getNibColor(NibType.outLine, false, property.color);
    } else {
      DrawCanvasModel().penState.isNeedTransparent = false;
      color = eraseAndNoSewColor;
    }

    /// 指定したペン先を更新する
    final newNib = MdcNib(
      type: property.lineType,
      color: color,
    );

    penState.updateNib(NibType.outLine, newNib);
    ClipBoardModel().reset();

    if (property.lineKind == MdcSewingKindsLine.noSew) {
      penState.updateUiDrawingShader(
        MdcMaxDrawingImageTarget.mdcMaxDrawingImageTarget_NoSewLine.index,
        property.motifNo,
      );
    } else {
      penState.removeUiDrawingShader();
    }

    /// Lib更新する
    MdcLibrary().apiBinding.setMdcEditLineProperty(property);
  }

  ///
  /// Eraseパラメータ更新
  ///
  void updateEraserNib() {
    var eraserProperty =
        MdcLibrary().apiBinding.getMdcEditEraserProperty().editEraserProperty;
    MdcNib newNib = MdcNib(
      type: MdcLineToolTypes.freeOpen,
      shape: eraserProperty.type == MdcEraserToolTypes.square
          ? NibShape.square
          : NibShape.circle,
      thickness: eraserProperty.size,
      color: Colors.transparent,
    );
    penState.updateNib(NibType.eraser, newNib);
    penState.removeUiDrawingShader();
    ClipBoardModel().reset();

    /// Lib更新する
    MdcLibrary().apiBinding.setMdcEditEraserProperty(eraserProperty);
  }
}
