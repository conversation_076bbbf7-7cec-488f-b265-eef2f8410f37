import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

part 'surface_decorative_fill_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class SurfaceDecoFillState with _$SurfaceDecoFillState {
  const factory SurfaceDecoFillState({
    @Default(false) bool sizeSettingPopup,
    @Default(false) bool directionSettingPopup,
    @Default(false) bool outlineSettingPopup,
    @Default(false) bool randomShiftSettingPopup,
    @Default(false) bool positionOffsetSettingPopup,
    @Default(false) bool thicknessSettingPopup,
    @Default(false) bool thisPatternTooComplexPopup,
    @Default(false) bool mdcNotExchangeAreaOverPopup,
    @Default(false) bool isThickSelected,
    @Default("") String sizeSettingValue,
    @Default("") String directionSettingValue,
    @Default("") String randomShiftSettingValue,
    @Default("") String positionOffsetSettingValue,
    @Default("") String thicknessSettingValue,
  }) = _SurfaceDecoFillState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class SurfaceDecoFillStateViewInterface
    extends ViewModel<SurfaceDecoFillState> {
  SurfaceDecoFillStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// 倍率/サイズのボタンをクリックする
  ///
  void onSizeButtonClicked(context);

  ///
  /// 傾き方向のボタンをクリックする
  ///
  void onDirectionButtonClicked(context);

  ///
  /// 輪郭縫いのボタンをクリックする
  ///
  void onOutlineButtonClicked(context);

  ///
  /// ゆらぎのボタンをクリックする
  ///
  void onRandomShiftSettingClicked(context);

  ///
  /// 基点位置オフセットのボタンをクリックする
  ///
  void onPositionOffsetSettingClicked(context);

  ///
  /// 縫い回数のボタンをクリックする
  ///
  void onThicknessSettingClicked(context);

  ///
  /// 倍率/サイズのテキストスタイルを取得する
  ///
  bool getSizeDefault();

  ///
  /// 傾き方向のテキストスタイルを取得する
  ///
  bool getDirectionDefault();

  ///
  /// 輪郭縫いのテキストスタイルを取得する
  ///
  bool getOutlineDefault();

  ///
  /// ゆらぎのテキストスタイルを取得する
  ///
  bool getRandomShiftDefault();

  ///
  /// 基点位置オフセットのテキストスタイルを取得する
  ///
  bool getPositionOffsetDefault();

  ///
  /// 輪郭縫い表示用テキストを取得する
  ///
  String getOutlineText(BuildContext context);

  ///
  /// 基点位置オフセット反転範囲を取得する
  ///
  double? getPositionOffsetTextWith();

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
