import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/device_info_model.dart';
import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/line_running_model.dart';
import '../../../../../../model/stitch/toolbar_model.dart';
import '../../../stitch_page_view_model.dart';
import 'line_running_view_interface.dart';

typedef LineRunningViewModelProvider = AutoDisposeStateNotifierProvider<
    LineRunningStateViewInterface, LineRunningState>;

class LineRunningViewModel extends LineRunningStateViewInterface {
  LineRunningViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineRunningState(
              runPitchPopup: false,
              runPitchDisplayValue: "",
              isrunPitchDefaultVlue: false,
              isUnitMm: true,
              isUnderSewingValeSame: true,
              underSewingState: MDCIsOnOff.mdcIs_on,
              isUnderSewingDefaultVlue: false,
            ),
            ref) {
    update();
  }

  @override
  void update() {
    state = state.copyWith(
      runPitchDisplayValue: _getRunPitchDisplayValue(),
      isrunPitchDefaultVlue: _getRunPitchDefault(),
      isUnitMm:
          DeviceInfoModel().displayUnitType == DisplayUnit.mm ? true : false,
      isUnderSewingValeSame: LineRunningModel().getUnderSewing() !=
              LineRunningModel.underSewingNotUpdating
          ? true
          : false,
      underSewingState: LineRunningModel().getUnderSewing(),
      isUnderSewingDefaultVlue: _getUnderSewingDefault(),
    );
  }

  @override
  void onRunPitchButtonClicked(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineRunningRunPitch.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);

    /// view更新
    update();
  }

  ///
  /// ランピッチの表示値を取得する
  ///
  String _getRunPitchDisplayValue() {
    int runPitch = LineRunningModel().getRunPitch();
    double runPitchValue = runPitch / LineRunningModel.conversionRate;

    if (runPitch == LineRunningModel.pitchNotUpdating) {
      if (DeviceInfoModel().displayUnitType == DisplayUnit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }
    if (DeviceInfoModel().displayUnitType == DisplayUnit.mm) {
      return runPitchValue.toStringAsFixed(1);
    } else {
      return ToolbarModel.getDisplayInchShowValue(runPitchValue);
    }
  }

  ///
  ///  ランピッチ表示テキストスタイルを取得します
  ///
  bool _getRunPitchDefault() {
    int runPitch = LineRunningModel().getRunPitch();
    if (runPitch == LineRunningModel().runPitchDefaultValue ||
        runPitch == LineRunningModel.pitchNotUpdating) {
      return true;
    }
    return false;
  }

  ///
  ///  下縫い表示テキストスタイルを取得します
  ///
  bool _getUnderSewingDefault() {
    MDCIsOnOff underSewing = LineRunningModel().getUnderSewing();
    if (underSewing == LineRunningModel().underSewingDefaultValue ||
        underSewing == LineRunningModel.underSewingNotUpdating) {
      return true;
    }

    return false;
  }

  ///
  /// 下縫いチボタンをクリック
  /// ToDo: 表示方法仕様確認
  /// QA jira:PHBSH-2670
  ///
  @override
  void openUnderSewingSettingPopup(BuildContext context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineRunningUnderSewing.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }
}
