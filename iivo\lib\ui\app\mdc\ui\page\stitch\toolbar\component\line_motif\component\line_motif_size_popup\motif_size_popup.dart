import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'motif_size_popup_view_interface.dart';
import 'motif_size_popup_view_model.dart';

class MotifSizePopup extends ConsumerStatefulWidget {
  const MotifSizePopup({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState<MotifSizePopup> createState() => _MotifSizeState();
}

class _MotifSizeState extends ConsumerState<MotifSizePopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(motifSizeViewModelProvider);
    final viewModel = ref.read(motifSizeViewModelProvider.notifier);

    return Row(
      children: [
        const Spacer(flex: 571),
        Expanded(
          flex: 229,
          child: Column(
            children: [
              const Spacer(flex: 159),
              Expanded(
                flex: 1052,
                child: Scaffold(
                  backgroundColor: Colors.transparent,
                  body: pre_edit_toolbar_mdc(
                    child: Column(
                      children: [
                        const Spacer(flex: 38),
                        Expanded(
                          flex: 68,
                          child: Row(
                            children: [
                              const Spacer(flex: 52),
                              Expanded(
                                flex: 126,
                                child: Stack(
                                  children: [
                                    const ico_mdcstitch_motif_size_popup(),
                                    Row(
                                      children: [
                                        const Spacer(flex: 6),
                                        Expanded(
                                          flex: 107,
                                          child: Stack(
                                            children: [
                                              Container(
                                                alignment: Alignment.centerLeft,
                                                child: const pic_frame_dai(),
                                              ),
                                              Column(
                                                children: [
                                                  const Spacer(flex: 13),
                                                  Expanded(
                                                    flex: 42,
                                                    child: Image.memory(
                                                      viewModel
                                                          .selectedThumbnailData(),
                                                      fit: BoxFit.cover,
                                                      gaplessPlayback: true,
                                                      alignment:
                                                          Alignment.centerLeft,
                                                    ),
                                                  ),
                                                  const Spacer(flex: 13),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        const Spacer(flex: 13),
                                      ],
                                    )
                                  ],
                                ),
                              ),
                              const Spacer(flex: 51),
                            ],
                          ),
                        ),
                        const Spacer(flex: 23),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 205,
                                child: grp_str_size(
                                  text: l10n.icon_00533,
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 8),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 205,
                                child: grp_str_parameter(
                                  text: state.sizeInputValue,
                                  isDefault: state.isDefaultStyle,
                                  displayText:
                                      viewModel.currentSelectedUnit == Unit.mm
                                          ? l10n.icon_00225
                                          : l10n.icon_00226,
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 12),
                        Expanded(
                          flex: 63,
                          child: Row(
                            children: [
                              const Spacer(flex: 48),
                              Expanded(
                                flex: 63,
                                child: grp_btn_minus_01(
                                  onTap: () => viewModel.miniSize(false),
                                  onLongPress: () => viewModel.miniSize(true),
                                  state: state.isSizeMinusToLimit
                                      ? ButtonState.disable
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 10),
                              Expanded(
                                flex: 63,
                                child: grp_btn_plus_01(
                                  onTap: () => viewModel.plusSize(false),
                                  onLongPress: () => viewModel.plusSize(true),
                                  state: state.isSizePlusToLimit
                                      ? ButtonState.disable
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 47),
                            ],
                          ),
                        ),
                        const Spacer(flex: 620),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 205,
                                child: grp_btn_positive_mdc(
                                  onTap: viewModel.onOkButtonClicked,
                                  text: l10n.icon_ok,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 12),
                      ],
                    ),
                  ),
                ),
              ),
              const Spacer(flex: 69),
            ],
          ),
        ),
      ],
    );
  }
}
