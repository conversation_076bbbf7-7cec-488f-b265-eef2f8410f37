import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_e_stitch_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'line_e_stitch_spacing_popup_view_interface.dart';

/// 読み出したSizeデータの変換倍率 '読み出したデータの単位/10=mm'
const double _conversionRate = 10.0;

final lineEStitchSpaceViewModelProvider = StateNotifierProvider.autoDispose<
    LineEStitchSpaceStateViewInterface,
    LineEStitchSpaceState>((ref) => LineEStitchSpaceViewModel(ref));

class LineEStitchSpaceViewModel extends LineEStitchSpaceStateViewInterface {
  LineEStitchSpaceViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineEStitchSpaceState(
                spaceDisplayValue: "",
                isDefaultStyle: false,
                plusButtonValid: false,
                minusButtonValid: false),
            ref) {
    update();
  }

  ///
  /// ステップ量
  ///
  final int _stepValue = 5;

  @override
  int get maxSpaceValue => LineEStitchModel.maxiSpaceValue;

  @override
  int get minSpaceValue => LineEStitchModel.miniSpaceValue;

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  bool _isSpaceValueDisplayStar =
      LineEStitchModel().getSpace() != LineEStitchModel.spacingNotUpdating
          ? false
          : true;

  int _spaceValue = LineEStitchModel().getSpace();

  @override
  void update() {
    state = state.copyWith(
        spaceDisplayValue: _getSpaceDisplayValue(),
        isDefaultStyle: _isDefaultStyle(),
        plusButtonValid: _getPlusButtonState(),
        minusButtonValid: _getMinusButtonState());
  }

  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isSpaceValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSpaceValueDisplayStar = false;

      ///  Model 更新
      _spaceValue = LineEStitchModel().spaceDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_spaceValue <= LineEStitchModel.miniSpaceValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _spaceValue = _spaceValue - _stepValue < LineEStitchModel.miniSpaceValue
        ? LineEStitchModel.miniSpaceValue
        : _spaceValue - _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_isSpaceValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSpaceValueDisplayStar = false;

      ///  Model 更新
      _spaceValue = LineEStitchModel().spaceDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_spaceValue >= LineEStitchModel.maxiSpaceValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _spaceValue = _spaceValue + _stepValue > LineEStitchModel.maxiSpaceValue
        ? LineEStitchModel.maxiSpaceValue
        : _spaceValue + _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineEStitchSpace.toString());
    if (_isSpaceValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int spaceValue = LineEStitchModel().getSpace();

    /// Model 更新
    LineEStitchModel().setSpace(_spaceValue);
    if (LineEStitchModel().setMdcEStitchSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (spaceValue != _spaceValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// 間隔の表示値を取得します
  ///
  String _getSpaceDisplayValue() {
    /// cmからmmへ
    double lineEStitchSpaceValue = _spaceValue / _conversionRate;

    if (_isSpaceValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }
    if (currentSelectedUnit == Unit.mm) {
      return lineEStitchSpaceValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(lineEStitchSpaceValue);
  }

  ///
  /// 間隔表示テキスト スタイルを取得します
  ///
  bool _isDefaultStyle() {
    if (_isSpaceValueDisplayStar) {
      return true;
    }

    if (_spaceValue == LineEStitchModel().spaceDefaultValue) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isSpaceValueDisplayStar) {
      return true;
    }

    if (_spaceValue <= LineEStitchModel.miniSpaceValue) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isSpaceValueDisplayStar) {
      return true;
    }

    if (_spaceValue >= LineEStitchModel.maxiSpaceValue) {
      return false;
    }
    return true;
  }
}
