import 'dart:async';

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:network_wifi/network_wifi.dart';
import 'package:panel_library/panel_library.dart';
import 'package:system_config/system_config.dart';
import 'package:xd_component/l10n/app_localizations.dart';

import '../../../../../../../../model/machine_config_model.dart';
import '../../../../../../../../network/network.dart';
import '../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../global_popup/global_popups/err_machine_name_enable_wlan/err_machine_name_enable_wlan_view_model.dart';
import '../../../../../model/network_setting_page1_model.dart';
import '../../../../../model/network_setting_page3_model.dart';
import '../../../../page_route.dart';
import 'network_setting_page_1_interface.dart';

///
/// 無線LAN設定1のview _modelに必要な構造
///
final networkPage1ViewModelProvider = StateNotifierProvider.autoDispose<
    NetworkSettingPage1Interface,
    NetworkSettingPage1State>((ref) => NetworkSettingPage1ViewModel(ref));

class NetworkSettingPage1ViewModel extends NetworkSettingPage1Interface {
  NetworkSettingPage1ViewModel(Ref ref)
      : super(const NetworkSettingPage1State(), ref) {
    update();

    _cancelWifiEventSubscription =
        WifiManager().wifiEventBroadcast.addSubscriber(
      (wifiEvent) {
        update();
      },
    );
  }

  ///
  /// view更新
  ///
  @override
  void update() {
    state = state.copyWith(
      isShowLimitedConnectMessage: WifiManager().isLimitedConnection(),
      wirelessLanEnable: WifiManager().isEnabled(),
      machineName: MachineConfigModel().getMachineName(),
      connectedSSID: WifiManager().getConnectedSSID(),
      wirelessIsConnected: WifiManager().isConnected(),
    );
  }

  ///
  /// 無線LAN有効ボタンをクリック
  ///
  @override
  void onWirelessLanEnableButtonClicked(bool wirelessLanEnable) {
    ///
    /// mode更新
    ///
    WifiManager().setEnabled(wirelessLanEnable).then((_) => update());
    Upgrade().checkFirmUpdateOnWifiEnabled();

    /// WLAN切り替え時の開始カウントのリセット
    Upgrade.resetWlanOffStartUpCount();
  }

  ///
  /// 無線LAN接続ウィザードボタンをクリック
  ///
  @override
  void onWirelessLanSetupButtonClicked(BuildContext context) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    ///
    /// 画面遷移
    /// Android SystemのSettingアプリに遷移する
    ///
    SystemConfig.openSystemPage(SystemPageEnum.wlanSettingAction)
        .then((value) => update());

    /// ここはIIVO自分でWlanの実現方法です
    /// 今暫定保留です
    // /// 無線LAN接続ウィザード画面に遷移し、無線LANの接続設定をする。
    // NetworkSettingPage1Model().clearConnectParam();
    // NetworkSettingPage1Model().clearScanResultValid();

    // PagesRoute().pushNamed(
    //   nextRoute: PageRouteEnum.networkSearching,
    // );
  }

  ///
  /// マシン名ボタンクリック
  ///
  @override
  void onMachineNameChangeButtonClicked(BuildContext context) {
    /// PINコード登録済, WLAN Off時：マシン名は変更できない
    final isCanvasRegister = NetworkSettingPage1Mode3().canvasRegister;
    if (isCanvasRegister == true) {
      if (state.wirelessLanEnable == false) {
        /// WLANがONになれば変更できるので、Changeキーを有効にしておき、
        /// Changeキーを押下した時にメッセージを表示する。
        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_MACHINENAME_ENABLE_WLAN,
            arguments: ErrMachineNameEnableWlanArgument(
              onOKButtonClicked: GlobalPopupRoute().resetErrorState,
            ));
      } else {
        PagesRoute().pushNamed(
          nextRoute: PageRouteEnum.networkChangeName,
        );
      }
    } else {
      /// PINコード未登録, マシン名は、制限なく変更可能。
      PagesRoute().pushNamed(
        nextRoute: PageRouteEnum.networkChangeName,
      );
    }
  }

  @override
  void onWirelessLanSetupSoundButton() {
    if (state.wirelessLanEnable == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }
  }

  ///
  /// 無線LAN接続状態ボタンをクリック
  ///
  @override
  Future<void> onWirelessLanStatusButtonClicked(BuildContext context) async {
    /// wifi 接続されている
    if (WifiManager().isConnected() && WifiManager().isEnabled()) {
      WifiInfo wifiInfo = WifiInfo();

      WifiStandard? wifiStandard = await wifiInfo.getWifiStandard();
      int? linkSpeed = await wifiInfo.getLinkSpeed();
      int? wifiChannel = await wifiInfo.getWifiChannel();
      WifiStrength? wifiStrength = await wifiInfo.getSignalStrength();

      NetworkSettingPage1Model().wirelessLanStatusInformation =
          NetworkSettingPage1Model().wirelessLanStatusInformation.copyWith(
                standard: wifiStandard,
                wifiSpeed: linkSpeed,
                wifiChannel: wifiChannel,
                wifiStrength: wifiStrength,
              );
    }

    /// wifi 接続されていない
    else {
      NetworkSettingPage1Model().wirelessLanStatusInformation =
          NetworkSettingPage1Model.unknownWirelessLanStatusInformation;
    }

    PagesRoute().pushNamed(nextRoute: PageRouteEnum.wirelessLanStatus);
  }

  ///
  /// ネットワーク設置リセットポップアップを開く/閉じる
  ///
  @override
  void openResetPopup(BuildContext context) {
    /// TODO SNC連携の登録情報を消去する
    ///
    /// 画面遷移
    /// Android SystemのSettingアプリに遷移する
    ///
    SystemConfig.openSystemPage(SystemPageEnum.networkReset)
        .then((_) => update());
  }

  ///
  ///【Others】ボタンのクリックイベント
  ///
  @override
  void onOthersButtonClicked(BuildContext context) {
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.networkOthersSetting);
  }

  @override
  void dispose() {
    super.dispose();
    _cancelWifiEventSubscription?.call();
  }

  @override
  String getSSID(AppLocalizations l10n) =>
      state.wirelessIsConnected ? state.connectedSSID : l10n.icon_00643;

  ///
  /// サブスクリプションをキャンセル
  ///
  void Function()? _cancelWifiEventSubscription;

  ///
  /// WlanSetupConfirmationのデータを消去する
  ///
  void resetWlanSetupConfirmation() {
    DeviceLibrary()
        .apiBinding
        .setWLanGuideSettingStatus(WlanGuideSettingStatusEnum.unInit);

    /// Androidシステムに保存されているWi-Fi情報をクリアする
    WifiInfo().clearNetWorkSetting();
  }
}
