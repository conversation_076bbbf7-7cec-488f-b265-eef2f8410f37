import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_osae_up_view_interface.dart';

final errOsaeUpViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrOsaeUpViewInterface, ErrOsaeUpState, BuildContext>(
        (ref, context) => ErrOsaeUpViewModel(ref, context));

class ErrOsaeUpViewModel extends ErrOsaeUpViewInterface {
  ErrOsaeUpViewModel(Ref ref, BuildContext context)
      : super(const ErrOsaeUpState(), ref, context);

  ///
  /// Returnボタンクリク関数
  ///
  @override
  void onReturnButtonClicked() {
    final int errCode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERROSAEUP);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }
}
