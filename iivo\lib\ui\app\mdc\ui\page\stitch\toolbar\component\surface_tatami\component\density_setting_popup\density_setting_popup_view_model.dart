import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_tatami_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'density_setting_popup_view_interface.dart';

final densitySettingPopupViewModelProvider = StateNotifierProvider.autoDispose<
    DensitySettingPopupStateViewInterface,
    DensitySettingPopupState>((ref) => DensitySettingPopupViewModel(ref));

class DensitySettingPopupViewModel
    extends DensitySettingPopupStateViewInterface {
  DensitySettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const DensitySettingPopupState(), ref) {
    _densityState = SurfaceTatamiModel().getDensityIndex() !=
            SurfaceTatamiModel.densityNotUpdating.index
        ? TatamiSettingState.settingCompleted
        : TatamiSettingState.unknown;

    /// view更新
    update();
  }

  ///
  /// 最大密度値
  ///
  final int _maxDensityIndex =
      MdcZigzagTatamiDensity.mdc_zigzag_tatami_density_110.index;

  ///
  /// 最小密度値
  ///
  final int _minDensityIndex =
      MdcZigzagTatamiDensity.mdc_zigzag_tatami_density_090.index;

  ///
  /// ステップ量
  ///
  final int _stepIndex = 1;

  ///
  /// タタミ縫いの糸密度の状態
  ///
  TatamiSettingState _densityState = TatamiSettingState.settingCompleted;

  ///
  /// 密度値
  ///
  int _densityIndex = SurfaceTatamiModel().getDensityIndex();

  @override
  void update() {
    state = state.copyWith(
      densityValue: _getDensityValue(),
      isMainValue: _isMinValue(),
      isMaxValue: _isMaxValue(),
    );
  }

  @override
  bool onMinusButtonClicked(bool isLongPress) {
    int index = _densityIndex;

    if (_densityState == TatamiSettingState.unknown) {
      index = SurfaceTatamiModel().densityDefaultValue.index;
    } else {
      if (index <= _minDensityIndex) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
      index = index - _stepIndex < _minDensityIndex
          ? _minDensityIndex
          : index - _stepIndex;
    }

    _densityIndex = index;
    _densityState = TatamiSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    state = state.copyWith(
      densityValue: _getDensityValue(),
      isMaxValue: _isMaxValue(),
      isMainValue: _isMinValue(),
    );
    return true;
  }

  @override
  bool onPlusButtonClicked(bool isLongPress) {
    int index = _densityIndex;

    if (_densityState == TatamiSettingState.unknown) {
      index = SurfaceTatamiModel().densityDefaultValue.index;
    } else {
      if (index >= _maxDensityIndex) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
      index = index + _stepIndex > _maxDensityIndex
          ? _maxDensityIndex
          : index + _stepIndex;
    }

    ///  Model 更新
    _densityIndex = index;
    _densityState = TatamiSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    state = state.copyWith(
      densityValue: _getDensityValue(),
      isMaxValue: _isMaxValue(),
      isMainValue: _isMinValue(),
    );
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.surfaceTatamiDensity.toString());
    if (_densityState == TatamiSettingState.unknown) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    /// Densityの初期値
    int oldDensityValueIndex = SurfaceTatamiModel().getDensityIndex();

    /// Model更新
    SurfaceTatamiModel().setDensityIndex(_densityIndex);
    if (SurfaceTatamiModel().setTatamiSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (_densityIndex != oldDensityValueIndex) {
      ResumeHistoryModel().backSnapshot();
    } else {
      /// do nothing
    }

    _densityState = TatamiSettingState.settingCompleted;
    CreationModel().changeStitchCreation();
  }

  @override
  bool getDensityTextStyle() {
    if (_densityState == TatamiSettingState.unknown) {
      return true;
    }

    if (_densityIndex == SurfaceTatamiModel().densityDefaultValue.index) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 糸密度表示値を取得する
  ///
  String _getDensityValue() => _densityState == TatamiSettingState.unknown
      ? SurfaceTatamiModel.differentDensityValue
      : SurfaceTatamiModel.densityList[_densityIndex].toString();

  ///
  /// 最大糸密度値の判断
  ///
  bool _isMaxValue() => _densityState == TatamiSettingState.unknown
      ? false
      : _densityIndex >= _maxDensityIndex
          ? true
          : false;

  ///
  /// 最小糸密度値の判断
  ///
  bool _isMinValue() => _densityState == TatamiSettingState.unknown
      ? false
      : _densityIndex <= _minDensityIndex
          ? true
          : false;
}
