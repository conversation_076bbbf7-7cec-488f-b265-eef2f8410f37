import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'outline_setting_popup_view_model.dart';

class OutlineSettingPopup extends ConsumerStatefulWidget {
  const OutlineSettingPopup({super.key});

  @override
  ConsumerState<OutlineSettingPopup> createState() =>
      _OutlineSettingPopupState();
}

class _OutlineSettingPopupState extends ConsumerState<OutlineSettingPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(outlineSettingPopupViewModelProvider);
    final viewModel = ref.read(outlineSettingPopupViewModelProvider.notifier);

    return Row(
      children: [
        const Spacer(flex: 571),
        Expanded(
          flex: 229,
          child: Column(
            children: [
              const Spacer(flex: 159),
              Expanded(
                flex: 1052,
                child: Scaffold(
                  backgroundColor: Colors.transparent,
                  body: pre_edit_toolbar_mdc(
                    child: Column(
                      children: [
                        const Spacer(flex: 37),
                        const Expanded(
                          flex: 68,
                          child: Row(
                            children: [
                              Spacer(flex: 52),
                              Expanded(
                                flex: 126,
                                child: ico_outlin(),
                              ),
                              Spacer(flex: 51),
                            ],
                          ),
                        ),
                        const Spacer(flex: 24),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 205,
                                child: grp_str_outline(
                                  text: l10n.icon_00562,
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 89),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 98,
                                child: grp_btn_on(
                                  text: l10n.icon_on,
                                  onTap: viewModel.onONButtonClicked,
                                  state: state.isOnButtonSelected
                                      ? ButtonState.select
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 9),
                              Expanded(
                                flex: 98,
                                child: grp_btn_off(
                                  text: l10n.icon_off,
                                  onTap: viewModel.onOFFButtonClicked,
                                  state: state.isOFFButtonSelected
                                      ? ButtonState.select
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 613),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 205,
                                child: grp_btn_positive_mdc(
                                  onTap: viewModel.onOkButtonClicked,
                                  text: l10n.icon_ok,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 12),
                      ],
                    ),
                  ),
                ),
              ),
              const Spacer(flex: 69),
            ],
          ),
        ),
      ],
    );
  }
}
