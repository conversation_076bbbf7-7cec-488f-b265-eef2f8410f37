import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:panel_library/panel_library.dart' show MdcImageInfo;

import '../../../../../model/paint/clip_board_model.dart' show ClipBoardState;
import '../mdc_canvas_view_interface.dart' show PartsImage;

class ImagePartsPainter extends CustomPainter {
  ImagePartsPainter(this.imgInfoParts, this.partsImage, this.framePath,
      this.scale, this.drawState);
  final MdcImageInfo? imgInfoParts;
  final PartsImage? partsImage;
  final double scale;
  Path? framePath;
  final ClipBoardState drawState;

  final Paint imgPaint = Paint()..isAntiAlias = false;

  ///
  /// 半分のスケール
  ///
  final _halfScale = 0.5;

  ///
  /// 画像がRepaintかどうか
  ///
  bool _isPrepared() {
    final imgInfo = imgInfoParts;
    final image = partsImage;
    final isNull = imgInfo != null && image != null;
    final imageNotChanged = imgInfo?.imageData.hashCode == image?.hash;
    return isNull && !imageNotChanged;
  }

  @override
  void paint(Canvas canvas, Size size) {
    if (_isPrepared() == false) return;

    if (framePath != null) {
      imgPaint.filterQuality =
          scale == _halfScale ? ui.FilterQuality.low : ui.FilterQuality.none;

      canvas.save();
      canvas.scale(scale, scale);
      canvas.drawImage(
        partsImage!.image!,
        ui.Offset(imgInfoParts!.startPointX.toDouble(),
            imgInfoParts!.startPointY.toDouble()),
        imgPaint,
      );

      final Paint framePaint = Paint()
        ..isAntiAlias = true
        ..strokeWidth = 1.0
        ..style = PaintingStyle.stroke
        ..filterQuality = ui.FilterQuality.none
        ..color = drawState.redFrameColor
        ..strokeWidth = drawState.redFrameWidth;
      canvas.drawPath(framePath!, framePaint);
      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(ImagePartsPainter oldDelegate) =>
      framePath != oldDelegate.framePath || _isPrepared();
}
