// ignore_for_file: always_specify_types
// ignore_for_file: camel_case_types
// ignore_for_file: non_constant_identifier_names

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
import 'dart:ffi' as ffi;
import 'package:ffi/ffi.dart' as ffi;

import 'embedit_bindings_generated.dart';

/// Bindings for `src/MdcEditIFLib.h`.
///
/// Regenerate bindings with `flutter pub run ffigen --config ffigen.yaml`.
///
class MdcEditBindings {
  /// Holds the symbol lookup function.
  final ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
      _lookup;

  /// The symbols are looked up in [dynamicLibrary].
  MdcEditBindings(ffi.DynamicLibrary dynamicLibrary)
      : _lookup = dynamicLibrary.lookup {}

  /// The symbols are looked up with [lookup].
  MdcEditBindings.fromLookup(
      ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
          lookup)
      : _lookup = lookup;

  void initLibrary(ffi.Pointer<ffi.Utf8> folderPath) {
    _initLibrary(folderPath);
  }

  late final _initLibraryPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Utf8>)>>(
          'initLibrary');
  late final _initLibrary =
      _initLibraryPtr.asFunction<void Function(ffi.Pointer<ffi.Utf8>)>();

  /// 1. Mdc EditMode
  int openMdcMode() {
    return _openMdcMode();
  }

  late final _openMdcModePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('openMdcMode');
  late final _openMdcMode = _openMdcModePtr.asFunction<int Function()>();

  int closeMdcMode() {
    return _closeMdcMode();
  }

  late final _closeMdcModePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('closeMdcMode');
  late final _closeMdcMode = _closeMdcModePtr.asFunction<int Function()>();

  /// 0.3 イメージ取得後のデータ領域開放
  int MdcReleaseImageInfo(
    ffi.Pointer<MdcImageInfo_t> imgInfo,
  ) {
    return _MdcReleaseImageInfo(
      imgInfo,
    );
  }

  late final _MdcReleaseImageInfoPtr = _lookup<
          ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<MdcImageInfo_t>)>>(
      'MdcReleaseImageInfo');
  late final _MdcReleaseImageInfo = _MdcReleaseImageInfoPtr.asFunction<
      int Function(ffi.Pointer<MdcImageInfo_t>)>();

  /// 2.1. 描画種別設定
  /// 2.1.1 引数
  /// 2.1.2 関数
  int setMdcDrawingType(
    int type,
  ) {
    return _setMdcDrawingType(
      type,
    );
  }

  late final _setMdcDrawingTypePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'setMdcDrawingType');
  late final _setMdcDrawingType =
      _setMdcDrawingTypePtr.asFunction<int Function(int)>();

  /// 2.2-1.2 関数
  int setMdcEditLineProperty(
    MdcEditLineProperty_t lineProp,
  ) {
    return _setMdcEditLineProperty(
      lineProp,
    );
  }

  late final _setMdcEditLinePropertyPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(MdcEditLineProperty_t)>>(
          'setMdcEditLineProperty');
  late final _setMdcEditLineProperty = _setMdcEditLinePropertyPtr
      .asFunction<int Function(MdcEditLineProperty_t)>();

  /// 2.2.2.2. 関数
  int setMdcEditSurfaceProperty(
    MdcEditSurfaceProperty_t surfProp,
  ) {
    return _setMdcEditSurfaceProperty(
      surfProp,
    );
  }

  late final _setMdcEditSurfacePropertyPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(MdcEditSurfaceProperty_t)>>(
          'setMdcEditSurfaceProperty');
  late final _setMdcEditSurfaceProperty = _setMdcEditSurfacePropertyPtr
      .asFunction<int Function(MdcEditSurfaceProperty_t)>();

  /// 2.2.3.2. 関数
  int setMdcEditEraserProperty(
    MdcEditEraserProperty_t eraseProp,
  ) {
    return _setMdcEditEraserProperty(
      eraseProp,
    );
  }

  late final _setMdcEditEraserPropertyPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(MdcEditEraserProperty_t)>>(
          'setMdcEditEraserProperty');
  late final _setMdcEditEraserProperty = _setMdcEditEraserPropertyPtr
      .asFunction<int Function(MdcEditEraserProperty_t)>();

  /// 2.2.3.2. 関数
  int setMdcStampProperty(
    MdcStampProperty_t stampProp,
  ) {
    return _setMdcStampProperty(
      stampProp,
    );
  }

  late final _setMdcStampPropertyPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(MdcStampProperty_t)>>(
          'setMdcStampProperty');
  late final _setMdcStampProperty =
      _setMdcStampPropertyPtr.asFunction<int Function(MdcStampProperty_t)>();

  /// 2.x.x. インポートしたユーザー作成模様ファイルのファイル名リスト、件数を保存する関数
  int setMdcImportFileList(
    MdcImportFileList_t import_list,
  ) {
    return _setMdcImportFileList(
      import_list,
    );
  }

  late final _setMdcImportFileListPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(MdcImportFileList_t)>>(
          'setMdcImportFileList');
  late final _setMdcImportFileList =
      _setMdcImportFileListPtr.asFunction<int Function(MdcImportFileList_t)>();

  /// 3.3.2.2. 関数
  int editMdcDraw(
    MdcPenTouchInfo_t pInfo,
    ffi.Pointer<MdcReqProc_t> reqproc,
    ffi.Pointer<MdcImageInfo_t> imgInfo,
  ) {
    return _editMdcDraw(
      pInfo,
      reqproc,
      imgInfo,
    );
  }

  late final _editMdcDrawPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(MdcPenTouchInfo_t, ffi.Pointer<MdcReqProc_t>,
              ffi.Pointer<MdcImageInfo_t>)>>('editMdcDraw');
  late final _editMdcDraw = _editMdcDrawPtr.asFunction<
      int Function(MdcPenTouchInfo_t, ffi.Pointer<MdcReqProc_t>,
          ffi.Pointer<MdcImageInfo_t>)>();

  /// 3.3.x. 関数
  int editMdcAreaSelect(
    MdcPenTouchInfo_t pInfo,
    int pointNum,
    ffi.Pointer<SSPoint_t> pointList,
    ffi.Pointer<MdcReqProc_t> reqproc,
    ffi.Pointer<MdcUnsettledObjectInfo_t> sInfo,
    ffi.Pointer<MdcImageInfo_t> imgInfoParts,
    ffi.Pointer<MdcImageInfo_t> imgInfoBackGround,
  ) {
    return _editMdcAreaSelect(
      pInfo,
      pointNum,
      pointList,
      reqproc,
      sInfo,
      imgInfoParts,
      imgInfoBackGround,
    );
  }

  late final _editMdcAreaSelectPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              MdcPenTouchInfo_t,
              ffi.Uint32,
              ffi.Pointer<SSPoint_t>,
              ffi.Pointer<MdcReqProc_t>,
              ffi.Pointer<MdcUnsettledObjectInfo_t>,
              ffi.Pointer<MdcImageInfo_t>,
              ffi.Pointer<MdcImageInfo_t>)>>('editMdcAreaSelect');
  late final _editMdcAreaSelect = _editMdcAreaSelectPtr.asFunction<
      int Function(
          MdcPenTouchInfo_t,
          int,
          ffi.Pointer<SSPoint_t>,
          ffi.Pointer<MdcReqProc_t>,
          ffi.Pointer<MdcUnsettledObjectInfo_t>,
          ffi.Pointer<MdcImageInfo_t>,
          ffi.Pointer<MdcImageInfo_t>)>();

  /// 3.3.3.2. 関数
  int editMdcUnsettledObject(
    ffi.Pointer<MdcUnsettledObjectInfo_t> sInfo,
    ffi.Pointer<MdcReqProc_t> reqproc,
    ffi.Pointer<MdcImageInfo_t> imgInfoParts,
    ffi.Pointer<MdcImageInfo_t> imgInfoBackGround,
  ) {
    return _editMdcUnsettledObject(
      sInfo,
      reqproc,
      imgInfoParts,
      imgInfoBackGround,
    );
  }

  late final _editMdcUnsettledObjectPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<MdcUnsettledObjectInfo_t>,
              ffi.Pointer<MdcReqProc_t>,
              ffi.Pointer<MdcImageInfo_t>,
              ffi.Pointer<MdcImageInfo_t>)>>('editMdcUnsettledObject');
  late final _editMdcUnsettledObject = _editMdcUnsettledObjectPtr.asFunction<
      int Function(
          ffi.Pointer<MdcUnsettledObjectInfo_t>,
          ffi.Pointer<MdcReqProc_t>,
          ffi.Pointer<MdcImageInfo_t>,
          ffi.Pointer<MdcImageInfo_t>)>();

  /// 3.3.3.3. 関数
  int editMdcUnsettledObjectNewPaste(
    ffi.Pointer<MdcUnsettledObjectInfo_t> sInfo,
    ffi.Pointer<MdcReqProc_t> reqproc,
    ffi.Pointer<MdcImageInfo_t> imgInfoParts,
    ffi.Pointer<MdcImageInfo_t> imgInfoBackGround,
  ) {
    return _editMdcUnsettledObjectNewPaste(
      sInfo,
      reqproc,
      imgInfoParts,
      imgInfoBackGround,
    );
  }

  late final _editMdcUnsettledObjectNewPastePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<MdcUnsettledObjectInfo_t>,
              ffi.Pointer<MdcReqProc_t>,
              ffi.Pointer<MdcImageInfo_t>,
              ffi.Pointer<MdcImageInfo_t>)>>('editMdcUnsettledObjectNewPaste');
  late final _editMdcUnsettledObjectNewPaste =
      _editMdcUnsettledObjectNewPastePtr.asFunction<
          int Function(
              ffi.Pointer<MdcUnsettledObjectInfo_t>,
              ffi.Pointer<MdcReqProc_t>,
              ffi.Pointer<MdcImageInfo_t>,
              ffi.Pointer<MdcImageInfo_t>)>();

  /// 3.4.3.2. 関数
  int scanMdcImage(
    ffi.Pointer<ffi.Char> filename,
    int dens,
    ffi.Pointer<MdcImageInfo_t> imgInfo,
  ) {
    return _scanMdcImage(
      filename,
      dens,
      imgInfo,
    );
  }

  late final _scanMdcImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Char>, ffi.Int32,
              ffi.Pointer<MdcImageInfo_t>)>>('scanMdcImage');
  late final _scanMdcImage = _scanMdcImagePtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, int, ffi.Pointer<MdcImageInfo_t>)>();

  /// 3.4.4-1.2. 関数
  int getMdcImageThumbnail(
    ffi.Pointer<ffi.Char> filename,
    int width,
    int height,
    ffi.Pointer<MdcThumbImageInfo_t> imgThumbInfo,
  ) {
    return _getMdcImageThumbnail(
      filename,
      width,
      height,
      imgThumbInfo,
    );
  }

  late final _getMdcImageThumbnailPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Char>, ffi.Uint32, ffi.Uint32,
              ffi.Pointer<MdcThumbImageInfo_t>)>>('getMdcImageThumbnail');
  late final _getMdcImageThumbnail = _getMdcImageThumbnailPtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, int, int, ffi.Pointer<MdcThumbImageInfo_t>)>();


  /// 3.4.4-2.2. 関数
  int getMdcImageSubstance(
    ffi.Pointer<ffi.Char> filename,
    ffi.Pointer<MdcUnsettledObjectInfo_t> info,
    ffi.Pointer<MdcReqProc_t> reqproc,
    ffi.Pointer<MdcImageInfo_t> imgInfoParts,
    ffi.Pointer<MdcImageInfo_t> imgInfoBackGround,
  ) {
    return _getMdcImageSubstance(
      filename,
      info,
      reqproc,
      imgInfoParts,
      imgInfoBackGround,
    );
  }

  late final _getMdcImageSubstancePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<MdcUnsettledObjectInfo_t>,
              ffi.Pointer<MdcReqProc_t>,
              ffi.Pointer<MdcImageInfo_t>,
              ffi.Pointer<MdcImageInfo_t>)>>('getMdcImageSubstance');
  late final _getMdcImageSubstance = _getMdcImageSubstancePtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<MdcUnsettledObjectInfo_t>,
          ffi.Pointer<MdcReqProc_t>, ffi.Pointer<MdcImageInfo_t>, ffi.Pointer<MdcImageInfo_t>)>();

  /// 3.4.6.2. 関数
  int clearMdcAll(
    ffi.Pointer<MdcImageInfo_t> imgInfo,
  ) {
    return _clearMdcAll(
      imgInfo,
    );
  }

  late final _clearMdcAllPtr = _lookup<
          ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<MdcImageInfo_t>)>>(
      'clearMdcAll');
  late final _clearMdcAll =
      _clearMdcAllPtr.asFunction<int Function(ffi.Pointer<MdcImageInfo_t>)>();


  /// 3.4.7.1.2. 関数
  int registMdcEditHistory(
    ffi.Pointer<ffi.Char> fullPathFileName,
  ) {
    return _registMdcEditHistory(
      fullPathFileName,
    );
  }

  late final _registMdcEditHistoryPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Char>)>>(
          'registMdcEditHistory');
  late final _registMdcEditHistory = _registMdcEditHistoryPtr
      .asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  /// 履歴読み出し(Undo/Redo)
  int loadMdcEditHistory(
    ffi.Pointer<ffi.Char> fullPathFileName,
    ffi.Pointer<MdcReqProc_t> reqproc,
    ffi.Pointer<ffi.Bool> existBackImage,
    ffi.Pointer<ffi.Bool> existClipboard,
    ffi.Pointer<MdcUnsettledObjectInfo_t> objInfo,
    ffi.Pointer<MdcImageInfo_t> imgInfoParts,
    ffi.Pointer<MdcImageInfo_t> imgInfoBackGround,
    ffi.Pointer<MdcHistSubParam_t> subParam
  ) {
    return _loadMdcEditHistory(
      fullPathFileName,
      reqproc,
      existBackImage,
      existClipboard,
      objInfo,
      imgInfoParts,
      imgInfoBackGround,
      subParam
    );
  }

  late final _loadMdcEditHistoryPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<MdcReqProc_t>,
              ffi.Pointer<ffi.Bool>,
              ffi.Pointer<ffi.Bool>,
              ffi.Pointer<MdcUnsettledObjectInfo_t>,
              ffi.Pointer<MdcImageInfo_t>,
              ffi.Pointer<MdcImageInfo_t>,
              ffi.Pointer<MdcHistSubParam_t>)>>('loadMdcEditHistory');
  late final _loadMdcEditHistory = _loadMdcEditHistoryPtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<MdcReqProc_t>,
          ffi.Pointer<ffi.Bool>,
          ffi.Pointer<ffi.Bool>,
          ffi.Pointer<MdcUnsettledObjectInfo_t>,
          ffi.Pointer<MdcImageInfo_t>,
          ffi.Pointer<MdcImageInfo_t>,
          ffi.Pointer<MdcHistSubParam_t>)>();


  /// 履歴読み出し(Resume)
  int loadMdcEditHistoryResume(
    ffi.Pointer<ffi.Char> fullPathFileName,
    ffi.Pointer<MdcReqProc_t> reqproc,
    ffi.Pointer<ffi.Bool> existBackImage,
    ffi.Pointer<ffi.Bool> existClipboard,
    ffi.Pointer<MdcUnsettledObjectInfo_t> objInfo,
    ffi.Pointer<MdcImageInfo_t> imgInfoParts,
    ffi.Pointer<MdcImageInfo_t> imgInfoBackGround,
    ffi.Pointer<MdcHistSubParam_t> subParam
  ) {
    return _loadMdcEditHistoryResume(
      fullPathFileName,
      reqproc,
      existBackImage,
      existClipboard,
      objInfo,
      imgInfoParts,
      imgInfoBackGround,
      subParam
    );
  }

  late final _loadMdcEditHistoryResumePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Char>,
              ffi.Pointer<MdcReqProc_t>,
              ffi.Pointer<ffi.Bool>,
              ffi.Pointer<ffi.Bool>,
              ffi.Pointer<MdcUnsettledObjectInfo_t>,
              ffi.Pointer<MdcImageInfo_t>,
              ffi.Pointer<MdcImageInfo_t>,
              ffi.Pointer<MdcHistSubParam_t>)>>('loadMdcEditHistoryResume');
  late final _loadMdcEditHistoryResume = _loadMdcEditHistoryResumePtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Char>,
          ffi.Pointer<MdcReqProc_t>,
          ffi.Pointer<ffi.Bool>,
          ffi.Pointer<ffi.Bool>,
          ffi.Pointer<MdcUnsettledObjectInfo_t>,
          ffi.Pointer<MdcImageInfo_t>,
          ffi.Pointer<MdcImageInfo_t>,
          ffi.Pointer<MdcHistSubParam_t>)>();


  /// 3.4.8. 関数
  int setMdcAreaSelectType(
    int type,
  ) {
    return _setMdcAreaSelectType(
      type,
    );
  }

  late final _setMdcAreaSelectTypePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'setMdcAreaSelectType');
  late final _setMdcAreaSelectType =
      _setMdcAreaSelectTypePtr.asFunction<int Function(int)>();

  /// 3.4.9.2. 関数
//  int saveMdcScreen(
//    ffi.Pointer<ffi.Char> filename,
//    ffi.Pointer<MdcImageInfo_t> imgInfo,
//  ) {
//    return _saveMdcScreen(
//      filename,
//      imgInfo,
//    );
//  }
//
//  late final _saveMdcScreenPtr = _lookup<
//      ffi.NativeFunction<
//          ffi.Int32 Function(ffi.Pointer<ffi.Char>,
//              ffi.Pointer<MdcImageInfo_t>)>>('saveMdcScreen');
//  late final _saveMdcScreen = _saveMdcScreenPtr.asFunction<
//      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<MdcImageInfo_t>)>();

  /// MDC縫製設定画面へ移行する
  int changeMdcPhaseDetail(
    ffi.Pointer<ffi.Int32> regionNum,
  ) {
    return _changeMdcPhaseDetail(
      regionNum,
    );
  }

  late final _changeMdcPhaseDetailPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int32>)>>(
          'changeMdcPhaseDetail');
  late final _changeMdcPhaseDetail = _changeMdcPhaseDetailPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int32>)>();

  /// お絵描きデータから刺繍データを作成するステッチ展開処理を開始する
  int startMdcStitchCreation(
    ffi.Pointer<MemHandle_t> grpH,
  ) {
    return _startMdcStitchCreation(
      grpH,
    );
  }

  late final _startMdcStitchCreationPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<MemHandle_t>)>>(
          'startMdcStitchCreation');
  late final _startMdcStitchCreation = _startMdcStitchCreationPtr
      .asFunction<int Function(ffi.Pointer<MemHandle_t>)>();

  /// 実行中のステッチ展開処理をキャンセルさせる
  int cancelMdcStitchCreation() {
    return _cancelMdcStitchCreation();
  }

  late final _cancelMdcStitchCreationPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'cancelMdcStitchCreation');
  late final _cancelMdcStitchCreation =
      _cancelMdcStitchCreationPtr.asFunction<int Function()>();

  /// 3.5. 各種設定値の取得
  /// 3.5.1 線プロパティー取得関数
  int getMdcEditLineProperty(
    ffi.Pointer<MdcEditLineProperty_t> lineProp,
  ) {
    return _getMdcEditLineProperty(
      lineProp,
    );
  }

  late final _getMdcEditLinePropertyPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<MdcEditLineProperty_t>)>>('getMdcEditLineProperty');
  late final _getMdcEditLineProperty = _getMdcEditLinePropertyPtr
      .asFunction<int Function(ffi.Pointer<MdcEditLineProperty_t>)>();

  /// 3.5.2 面プロパティー取得関数
  int getMdcEditSurfaceProperty(
    ffi.Pointer<MdcEditSurfaceProperty_t> surfProp,
  ) {
    return _getMdcEditSurfaceProperty(
      surfProp,
    );
  }

  late final _getMdcEditSurfacePropertyPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<MdcEditSurfaceProperty_t>)>>(
      'getMdcEditSurfaceProperty');
  late final _getMdcEditSurfaceProperty = _getMdcEditSurfacePropertyPtr
      .asFunction<int Function(ffi.Pointer<MdcEditSurfaceProperty_t>)>();

  /// 3.5.3 消しゴムプロパティー取得関数
  int getMdcEditEraserProperty(
    ffi.Pointer<MdcEditEraserProperty_t> eraseProp,
  ) {
    return _getMdcEditEraserProperty(
      eraseProp,
    );
  }

  late final _getMdcEditEraserPropertyPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<MdcEditEraserProperty_t>)>>(
      'getMdcEditEraserProperty');
  late final _getMdcEditEraserProperty = _getMdcEditEraserPropertyPtr
      .asFunction<int Function(ffi.Pointer<MdcEditEraserProperty_t>)>();

  /// 3.5.4 スタンプ図形(定型図形) 取得関数
  int getMdcStampProperty(
    ffi.Pointer<MdcStampProperty_t> stampProp,
  ) {
    return _getMdcStampProperty(
      stampProp,
    );
  }

  late final _getMdcStampPropertyPtr = _lookup<
          ffi
          .NativeFunction<ffi.Int32 Function(ffi.Pointer<MdcStampProperty_t>)>>(
      'getMdcStampProperty');
  late final _getMdcStampProperty = _getMdcStampPropertyPtr
      .asFunction<int Function(ffi.Pointer<MdcStampProperty_t>)>();

  /// 3.5.5. スタンプ図形(定型図形) 初期値取得関数
//  int getMdcStampInfo(
//    int type,
//    int index,
//    ffi.Pointer<MdcUnsettledObjectInfo_t> info,
//  ) {
//    return _getMdcStampInfo(
//      type,
//      index,
//      info,
//    );
//  }
//
//  late final _getMdcStampInfoPtr = _lookup<
//      ffi.NativeFunction<
//          ffi.Int32 Function(ffi.Int32, ffi.Int32,
//              ffi.Pointer<MdcUnsettledObjectInfo_t>)>>('getMdcStampInfo');
//  late final _getMdcStampInfo = _getMdcStampInfoPtr.asFunction<
//      int Function(int, int, ffi.Pointer<MdcUnsettledObjectInfo_t>)>();

  int setMdcSelectPictureForScanInfoAndData(
    ffi.Pointer<ffi.Char> filename,
  ) {
    return _setMdcSelectPictureForScanInfoAndData(
      filename,
    );
  }

  // 外部メモリ選択の画像をスキャン形式データ変換のためにセットする
  late final _setMdcSelectPictureForScanInfoAndDataPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Char>)>>(
          'setMdcSelectPictureForScanInfoAndData');
  late final _setMdcSelectPictureForScanInfoAndData =
      _setMdcSelectPictureForScanInfoAndDataPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();


  /// スキャンモード初期化(モードの設定)
  int initScanMode(
      ffi.Pointer<CameraScanInfoAndData_t> infoAndData,
      int mode,
      ffi.Pointer<ffi.Uint32> mmsize_width,
      ffi.Pointer<ffi.Uint32> mmsize_height,
      ffi.Pointer<MdcImageInfo_t> imgInfo) {
    return _initScanMode(infoAndData, mode, mmsize_width, mmsize_height, imgInfo);
  }

  late final _initScanModePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<CameraScanInfoAndData_t>,
              ffi.Int32,
              ffi.Pointer<ffi.Uint32>,
              ffi.Pointer<ffi.Uint32>,
              ffi.Pointer<MdcImageInfo_t>)>>('initScanMode');
  late final _initScanMode = _initScanModePtr.asFunction<
      int Function(ffi.Pointer<CameraScanInfoAndData_t>, int,
          ffi.Pointer<ffi.Uint32>, ffi.Pointer<ffi.Uint32>, ffi.Pointer<MdcImageInfo_t>)>();

  ///4.4.3.2	トリミングサイズ 変更
  int changeTrimmingSize(
      SSPoint_t upperLeft,
      SSPoint_t bottomRight,
      ffi.Pointer<ffi.Uint32> widthTrimmingSize,
      ffi.Pointer<ffi.Uint32> heightTrimmingSize) {
    return _changeTrimmingSize(
        upperLeft, bottomRight, widthTrimmingSize, heightTrimmingSize);
  }

  late final _changeTrimmingSizePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(SSPoint_t, SSPoint_t, ffi.Pointer<ffi.Uint32>,
              ffi.Pointer<ffi.Uint32>)>>('changeTrimmingSize');
  late final _changeTrimmingSize = _changeTrimmingSizePtr.asFunction<
      int Function(SSPoint_t, SSPoint_t, ffi.Pointer<ffi.Uint32>,
          ffi.Pointer<ffi.Uint32>)>();

  ///4.4.3.3	アウトライン画像 作成
  int convertToOutlineImage(int grayLevel, ffi.Pointer<MdcImageInfo_t> imgInfo) {
    return _convertToOutlineImage(
        grayLevel,imgInfo);
  }

  late final _convertToOutlineImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Int32,
              ffi.Pointer<MdcImageInfo_t>)>>('convertToOutlineImage');
  late final _convertToOutlineImage = _convertToOutlineImagePtr.asFunction<
      int Function(int, ffi.Pointer<MdcImageInfo_t>)>();

  ///4.4.3.4	アウトライン変換で生成したデータを元にお絵描き部分模様を生成する
  int confirmOutlineImage(
    ffi.Pointer<MdcReqProc_t> reqproc,
    ffi.Pointer<MdcUnsettledObjectInfo_t> objInfo,
    ffi.Pointer<MdcImageInfo_t> imgInfoParts,
    ffi.Pointer<MdcImageInfo_t> imgInfoBackGround,
  ) {
    return _confirmOutlineImage(
      reqproc,
      objInfo,
      imgInfoParts,
      imgInfoBackGround,
    );
  }

  late final _confirmOutlineImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<MdcReqProc_t>,
              ffi.Pointer<MdcUnsettledObjectInfo_t>,
              ffi.Pointer<MdcImageInfo_t>,
              ffi.Pointer<MdcImageInfo_t>)>>('confirmOutlineImage');
  late final _confirmOutlineImage = _confirmOutlineImagePtr.asFunction<
      int Function(
          ffi.Pointer<MdcReqProc_t>,
          ffi.Pointer<MdcUnsettledObjectInfo_t>,
          ffi.Pointer<MdcImageInfo_t>,
          ffi.Pointer<MdcImageInfo_t>)>();

  ///4.4.3.5	イラスト画像 作成
  int convertToIllusrationImage(
      int maxColor,
      bool drawLine,
      int lineWidth,
      int lineColor,
      bool removeBack,
      ffi.Pointer<MdcImageInfo_t> imgInfo) {
    return _convertToIllusrationImage(maxColor, drawLine, lineWidth, lineColor,
        removeBack, imgInfo);
  }

  late final _convertToIllusrationImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32,
              ffi.Bool,
              ffi.Uint32,
              ffi.Uint32,
              ffi.Bool,
              ffi.Pointer<MdcImageInfo_t>)>>('convertToIllusrationImage');
  late final _convertToIllusrationImage =
      _convertToIllusrationImagePtr.asFunction<
          int Function(int, bool, int, int, bool, ffi.Pointer<MdcImageInfo_t>)>();

  ///4.4.3.6	イラスト変換で生成したデータを元にお絵描き部分模様を生成する
  int confirmIllusrationImage(
    ffi.Pointer<MdcReqProc_t> reqproc,
    ffi.Pointer<MdcUnsettledObjectInfo_t> objInfo,
    ffi.Pointer<MdcImageInfo_t> imgInfoParts,
    ffi.Pointer<MdcImageInfo_t> imgInfoBackGround,
  ) {
    return _confirmIllusrationImage(
      reqproc,
      objInfo,
      imgInfoParts,
      imgInfoBackGround,
    );
  }

  late final _confirmIllusrationImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<MdcReqProc_t>,
              ffi.Pointer<MdcUnsettledObjectInfo_t>,
              ffi.Pointer<MdcImageInfo_t>,
              ffi.Pointer<MdcImageInfo_t>)>>('confirmIllusrationImage');
  late final _confirmIllusrationImage = _confirmIllusrationImagePtr.asFunction<
      int Function(
          ffi.Pointer<MdcReqProc_t>,
          ffi.Pointer<MdcUnsettledObjectInfo_t>,
          ffi.Pointer<MdcImageInfo_t>,
          ffi.Pointer<MdcImageInfo_t>)>();

  ///4.4.3.x	お絵描き画像の描画濃度を設定
  int setMdcImageDrawingDensity(
    int drawing_density,
  ) {
    return _setMdcImageDrawingDensity(
      drawing_density,
    );
  }

  late final _setMdcImageDrawingDensityPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'setMdcImageDrawingDensity');
  late final _setMdcImageDrawingDensity =
      _setMdcImageDrawingDensityPtr.asFunction<int Function(int)>();

  ///4.4.3.x	お絵描き画像の描画濃度を取得
  int getMdcImageDrawingDensity(
    ffi.Pointer<ffi.Int32> p_drawing_density,
  ) {
    return _getMdcImageDrawingDensity(
      p_drawing_density,
    );
  }

  late final _getMdcImageDrawingDensityPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int32>)>>(
          'getMdcImageDrawingDensity');
  late final _getMdcImageDrawingDensity = _getMdcImageDrawingDensityPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int32>)>();

  ///4.6.1	取得データの確位置定関数
//  int MdcSetImagePosition(ffi.Pointer<MdcReqProc_t> reqproc,ffi.Pointer<MdcImageInfo_t> imgInfo) {
//    return _MdcSetImagePosition(reqproc,imgInfo);
//  }
//  late final _MdcSetImagePositionPtr = _lookup<
//      ffi.NativeFunction<
//          ffi.Int32 Function(ffi.Pointer<MdcReqProc_t> reqproc,ffi.Pointer<MdcImageInfo_t>)>>('MdcSetImagePosition');
//  late final _MdcSetImagePosition = _MdcSetImagePositionPtr.asFunction<
//      int Function(ffi.Pointer<MdcReqProc_t> ,ffi.Pointer<MdcImageInfo_t> )>();

  /// ジグザグ(サテン)ステッチ縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  int setMdcZigzagLineParam(
    int regionNo,
    bool targetAll,
    MdcSewZigzagParam prm,
  ) {
    return _setMdcZigzagLineParam(
      regionNo,
      targetAll,
      prm,
    );
  }

  late final _setMdcZigzagLineParamPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint32, ffi.Bool,
              MdcSewZigzagParam)>>('setMdcZigzagLineParam');
  late final _setMdcZigzagLineParam = _setMdcZigzagLineParamPtr
      .asFunction<int Function(int, bool, MdcSewZigzagParam)>();

  /// ランニングステッチ縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  int setMdcRunningStitchLineParam(
    int regionNo,
    bool targetAll,
    MdcSewRunningParam prm,
  ) {
    return _setMdcRunningStitchLineParam(
      regionNo,
      targetAll,
      prm,
    );
  }

  late final _setMdcRunningStitchLineParamPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint32, ffi.Bool,
              MdcSewRunningParam)>>('setMdcRunningStitchLineParam');
  late final _setMdcRunningStitchLineParam = _setMdcRunningStitchLineParamPtr
      .asFunction<int Function(int, bool, MdcSewRunningParam)>();

  /// トリプルステッチ縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  int setMdcTripleStitchLineParam(
    int regionNo,
    bool targetAll,
    MdcSewTripleParam prm,
  ) {
    return _setMdcTripleStitchLineParam(
      regionNo,
      targetAll,
      prm,
    );
  }

  late final _setMdcTripleStitchLineParamPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint32, ffi.Bool,
              MdcSewTripleParam)>>('setMdcTripleStitchLineParam');
  late final _setMdcTripleStitchLineParam = _setMdcTripleStitchLineParamPtr
      .asFunction<int Function(int, bool, MdcSewTripleParam)>();

  /// キャンドルウィック縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  int setMdcCandlwickingLineParam(
    int regionNo,
    bool targetAll,
    MdcSewCandleParam prm,
  ) {
    return _setMdcCandlwickingLineParam(
      regionNo,
      targetAll,
      prm,
    );
  }

  late final _setMdcCandlwickingLineParamPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint32, ffi.Bool,
              MdcSewCandleParam)>>('setMdcCandlwickingLineParam');
  late final _setMdcCandlwickingLineParam = _setMdcCandlwickingLineParamPtr
      .asFunction<int Function(int, bool, MdcSewCandleParam)>();

  /// チェーンステッチ縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  int setMdcChainStitchLineParam(
    int regionNo,
    bool targetAll,
    MdcSewChainParam prm,
  ) {
    return _setMdcChainStitchLineParam(
      regionNo,
      targetAll,
      prm,
    );
  }

  late final _setMdcChainStitchLineParamPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint32, ffi.Bool,
              MdcSewChainParam)>>('setMdcChainStitchLineParam');
  late final _setMdcChainStitchLineParam = _setMdcChainStitchLineParamPtr
      .asFunction<int Function(int, bool, MdcSewChainParam)>();

  /// Eステッチ縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  int setMdcEStitchLineParam(
    int regionNo,
    bool targetAll,
    MdcSewEParam prm,
  ) {
    return _setMdcEStitchLineParam(
      regionNo,
      targetAll,
      prm,
    );
  }

  late final _setMdcEStitchLineParamPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32, ffi.Bool, MdcSewEParam)>>('setMdcEStitchLineParam');
  late final _setMdcEStitchLineParam = _setMdcEStitchLineParamPtr
      .asFunction<int Function(int, bool, MdcSewEParam)>();

  /// Vステッチ縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  int setMdcVStitchLineParam(
    int regionNo,
    bool targetAll,
    MdcSewVParam prm,
  ) {
    return _setMdcVStitchLineParam(
      regionNo,
      targetAll,
      prm,
    );
  }

  late final _setMdcVStitchLineParamPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32, ffi.Bool, MdcSewVParam)>>('setMdcVStitchLineParam');
  late final _setMdcVStitchLineParam = _setMdcVStitchLineParamPtr
      .asFunction<int Function(int, bool, MdcSewVParam)>();

  /// 線モチーフ縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  int setMdcMotifLineParam(
    int regionNo,
    bool targetAll,
    MdcSewMotifParam prm,
  ) {
    return _setMdcMotifLineParam(
      regionNo,
      targetAll,
      prm,
    );
  }

  late final _setMdcMotifLineParamPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32, ffi.Bool, MdcSewMotifParam)>>('setMdcMotifLineParam');
  late final _setMdcMotifLineParam = _setMdcMotifLineParamPtr
      .asFunction<int Function(int, bool, MdcSewMotifParam)>();

  /// 粗いジグザグ縫い縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  int setMdcRoughZigzagLineParam(
    int regionNo,
    bool targetAll,
    MdcSewRoughZigzagParam prm,
  ) {
    return _setMdcRoughZigzagLineParam(
      regionNo,
      targetAll,
      prm,
    );
  }

  late final _setMdcRoughZigzagLineParamPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint32, ffi.Bool,
              MdcSewRoughZigzagParam)>>('setMdcRoughZigzagLineParam');
  late final _setMdcRoughZigzagLineParam = _setMdcRoughZigzagLineParamPtr
      .asFunction<int Function(int, bool, MdcSewRoughZigzagParam)>();


  /// ジグザグ(サテン)ステッチ縫製パラメータや色情報の取得
  int getMdcZigzagLineParamInfo(
    int regionNo,
    bool targetAll,
    ffi.Pointer<MdcSewZigzagParam> prm,
    ffi.Pointer<ffi.UnsignedChar> color,
  ) {
    return _getMdcZigzagLineParamInfo(
      regionNo,
      targetAll,
      prm,
      color,
    );
  }

  late final _getMdcZigzagLineParamInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32,
              ffi.Bool,
              ffi.Pointer<MdcSewZigzagParam>,
              ffi.Pointer<ffi.UnsignedChar>)>>('getMdcZigzagLineParamInfo');
  late final _getMdcZigzagLineParamInfo =
      _getMdcZigzagLineParamInfoPtr.asFunction<
          int Function(int, bool, ffi.Pointer<MdcSewZigzagParam>,
              ffi.Pointer<ffi.UnsignedChar>)>();

  /// ランニングステッチ縫製パラメータや色情報の取得
  int getMdcRunningStitchLineParamInfo(
    int regionNo,
    bool targetAll,
    ffi.Pointer<MdcSewRunningParam> prm,
    ffi.Pointer<ffi.UnsignedChar> color,
  ) {
    return _getMdcRunningStitchLineParamInfo(
      regionNo,
      targetAll,
      prm,
      color,
    );
  }

  late final _getMdcRunningStitchLineParamInfoPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  ffi.Uint32,
                  ffi.Bool,
                  ffi.Pointer<MdcSewRunningParam>,
                  ffi.Pointer<ffi.UnsignedChar>)>>(
      'getMdcRunningStitchLineParamInfo');
  late final _getMdcRunningStitchLineParamInfo =
      _getMdcRunningStitchLineParamInfoPtr.asFunction<
          int Function(int, bool, ffi.Pointer<MdcSewRunningParam>,
              ffi.Pointer<ffi.UnsignedChar>)>();

  /// トリプルステッチ縫製パラメータや色情報の取得
  int getMdcTripleStitchLineParamInfo(
    int regionNo,
    bool targetAll,
    ffi.Pointer<MdcSewTripleParam> prm,
    ffi.Pointer<ffi.UnsignedChar> color,
  ) {
    return _getMdcTripleStitchLineParamInfo(
      regionNo,
      targetAll,
      prm,
      color,
    );
  }

  late final _getMdcTripleStitchLineParamInfoPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  ffi.Uint32,
                  ffi.Bool,
                  ffi.Pointer<MdcSewTripleParam>,
                  ffi.Pointer<ffi.UnsignedChar>)>>(
      'getMdcTripleStitchLineParamInfo');
  late final _getMdcTripleStitchLineParamInfo =
      _getMdcTripleStitchLineParamInfoPtr.asFunction<
          int Function(int, bool, ffi.Pointer<MdcSewTripleParam>,
              ffi.Pointer<ffi.UnsignedChar>)>();

  /// キャンドルウィック縫製パラメータや色情報の取得
  int getMdcCandlwickingLineParamInfo(
    int regionNo,
    bool targetAll,
    ffi.Pointer<MdcSewCandleParam> prm,
    ffi.Pointer<ffi.UnsignedChar> color,
  ) {
    return _getMdcCandlwickingLineParamInfo(
      regionNo,
      targetAll,
      prm,
      color,
    );
  }

  late final _getMdcCandlwickingLineParamInfoPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  ffi.Uint32,
                  ffi.Bool,
                  ffi.Pointer<MdcSewCandleParam>,
                  ffi.Pointer<ffi.UnsignedChar>)>>(
      'getMdcCandlwickingLineParamInfo');
  late final _getMdcCandlwickingLineParamInfo =
      _getMdcCandlwickingLineParamInfoPtr.asFunction<
          int Function(int, bool, ffi.Pointer<MdcSewCandleParam>,
              ffi.Pointer<ffi.UnsignedChar>)>();

  /// チェーンステッチ縫製パラメータや色情報の取得
  int getMdcChainStitchLineParamInfo(
    int regionNo,
    bool targetAll,
    ffi.Pointer<MdcSewChainParam> prm,
    ffi.Pointer<ffi.UnsignedChar> color,
  ) {
    return _getMdcChainStitchLineParamInfo(
      regionNo,
      targetAll,
      prm,
      color,
    );
  }

  late final _getMdcChainStitchLineParamInfoPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  ffi.Uint32,
                  ffi.Bool,
                  ffi.Pointer<MdcSewChainParam>,
                  ffi.Pointer<ffi.UnsignedChar>)>>(
      'getMdcChainStitchLineParamInfo');
  late final _getMdcChainStitchLineParamInfo =
      _getMdcChainStitchLineParamInfoPtr.asFunction<
          int Function(int, bool, ffi.Pointer<MdcSewChainParam>,
              ffi.Pointer<ffi.UnsignedChar>)>();

  /// Eステッチ縫製パラメータや色情報の取得
  int getMdcEStitchLineParamInfo(
    int regionNo,
    bool targetAll,
    ffi.Pointer<MdcSewEParam> prm,
    ffi.Pointer<ffi.UnsignedChar> color,
  ) {
    return _getMdcEStitchLineParamInfo(
      regionNo,
      targetAll,
      prm,
      color,
    );
  }

  late final _getMdcEStitchLineParamInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint32, ffi.Bool, ffi.Pointer<MdcSewEParam>,
              ffi.Pointer<ffi.UnsignedChar>)>>('getMdcEStitchLineParamInfo');
  late final _getMdcEStitchLineParamInfo =
      _getMdcEStitchLineParamInfoPtr.asFunction<
          int Function(int, bool, ffi.Pointer<MdcSewEParam>,
              ffi.Pointer<ffi.UnsignedChar>)>();

  /// Vステッチ縫製パラメータや色情報の取得
  int getMdcVStitchLineParamInfo(
    int regionNo,
    bool targetAll,
    ffi.Pointer<MdcSewVParam> prm,
    ffi.Pointer<ffi.UnsignedChar> color,
  ) {
    return _getMdcVStitchLineParamInfo(
      regionNo,
      targetAll,
      prm,
      color,
    );
  }

  late final _getMdcVStitchLineParamInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint32, ffi.Bool, ffi.Pointer<MdcSewVParam>,
              ffi.Pointer<ffi.UnsignedChar>)>>('getMdcVStitchLineParamInfo');
  late final _getMdcVStitchLineParamInfo =
      _getMdcVStitchLineParamInfoPtr.asFunction<
          int Function(int, bool, ffi.Pointer<MdcSewVParam>,
              ffi.Pointer<ffi.UnsignedChar>)>();

  /// 線モチーフ縫製パラメータや色・モチーフ種類などの情報取得
  int getMdcMotifLineParamInfo(
    int regionNo,
    bool targetAll,
    ffi.Pointer<MdcSewMotifParam> prm,
    ffi.Pointer<ffi.UnsignedChar> color,
    ffi.Pointer<ffi.Bool> motifType,
    ffi.Pointer<ffi.Uint16> motifNo,
  ) {
    return _getMdcMotifLineParamInfo(
      regionNo,
      targetAll,
      prm,
      color,
      motifType,
      motifNo,
    );
  }

  late final _getMdcMotifLineParamInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32,
              ffi.Bool,
              ffi.Pointer<MdcSewMotifParam>,
              ffi.Pointer<ffi.UnsignedChar>,
              ffi.Pointer<ffi.Bool>,
              ffi.Pointer<ffi.Uint16>)>>('getMdcMotifLineParamInfo');
  late final _getMdcMotifLineParamInfo =
      _getMdcMotifLineParamInfoPtr.asFunction<
          int Function(
              int,
              bool,
              ffi.Pointer<MdcSewMotifParam>,
              ffi.Pointer<ffi.UnsignedChar>,
              ffi.Pointer<ffi.Bool>,
              ffi.Pointer<ffi.Uint16>)>();

  /// 粗いジグザグ縫い縫製パラメータや色情報の取得
  int getMdcRoughZigzagLineParamInfo(
    int regionNo,
    bool targetAll,
    ffi.Pointer<MdcSewRoughZigzagParam> prm,
    ffi.Pointer<ffi.UnsignedChar> color,
  ) {
    return _getMdcRoughZigzagLineParamInfo(
      regionNo,
      targetAll,
      prm,
      color,
    );
  }

  late final _getMdcRoughZigzagLineParamInfoPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  ffi.Uint32,
                  ffi.Bool,
                  ffi.Pointer<MdcSewRoughZigzagParam>,
                  ffi.Pointer<ffi.UnsignedChar>)>>(
      'getMdcRoughZigzagLineParamInfo');
  late final _getMdcRoughZigzagLineParamInfo =
      _getMdcRoughZigzagLineParamInfoPtr.asFunction<
          int Function(int, bool, ffi.Pointer<MdcSewRoughZigzagParam>,
              ffi.Pointer<ffi.UnsignedChar>)>();

  /// タタミ縫い縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  int setMdcTatamiSurfaceParam(
    int regionNo,
    bool targetAll,
    MdcSewTatamiParam prm,
  ) {
    return _setMdcTatamiSurfaceParam(
      regionNo,
      targetAll,
      prm,
    );
  }

  late final _setMdcTatamiSurfaceParamPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint32, ffi.Bool,
              MdcSewTatamiParam)>>('setMdcTatamiSurfaceParam');
  late final _setMdcTatamiSurfaceParam = _setMdcTatamiSurfaceParamPtr
      .asFunction<int Function(int, bool, MdcSewTatamiParam)>();

  /// スティップリング縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  int setMdcStipplingSurfaceParam(
    int regionNo,
    bool targetAll,
    MdcSewStippleParam prm,
  ) {
    return _setMdcStipplingSurfaceParam(
      regionNo,
      targetAll,
      prm,
    );
  }

  late final _setMdcStipplingSurfaceParamPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint32, ffi.Bool,
              MdcSewStippleParam)>>('setMdcStipplingSurfaceParam');
  late final _setMdcStipplingSurfaceParam = _setMdcStipplingSurfaceParamPtr
      .asFunction<int Function(int, bool, MdcSewStippleParam)>();

  /// デコラティブフィル縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  int setMdcDecorativeFillSurfaceParam(
    int regionNo,
    bool targetAll,
    MdcSewDecoParam prm,
  ) {
    return _setMdcDecorativeFillSurfaceParam(
      regionNo,
      targetAll,
      prm,
    );
  }

  late final _setMdcDecorativeFillSurfaceParamPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint32, ffi.Bool,
              MdcSewDecoParam)>>('setMdcDecorativeFillSurfaceParam');
  late final _setMdcDecorativeFillSurfaceParam =
      _setMdcDecorativeFillSurfaceParamPtr
          .asFunction<int Function(int, bool, MdcSewDecoParam)>();

  /// タタミ縫い縫製パラメータや色情報の取得
  int getMdcTatamiSurfaceParamInfo(
    int regionNo,
    bool targetAll,
    ffi.Pointer<MdcSewTatamiParam> prm,
    ffi.Pointer<ffi.UnsignedChar> color,
  ) {
    return _getMdcTatamiSurfaceParamInfo(
      regionNo,
      targetAll,
      prm,
      color,
    );
  }

  late final _getMdcTatamiSurfaceParamInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32,
              ffi.Bool,
              ffi.Pointer<MdcSewTatamiParam>,
              ffi.Pointer<ffi.UnsignedChar>)>>('getMdcTatamiSurfaceParamInfo');
  late final _getMdcTatamiSurfaceParamInfo =
      _getMdcTatamiSurfaceParamInfoPtr.asFunction<
          int Function(int, bool, ffi.Pointer<MdcSewTatamiParam>,
              ffi.Pointer<ffi.UnsignedChar>)>();

  /// スティップリング縫製パラメータや色情報の取得
  int getMdcStipplingSurfaceParamInfo(
    int regionNo,
    bool targetAll,
    ffi.Pointer<MdcSewStippleParam> prm,
    ffi.Pointer<ffi.UnsignedChar> color,
  ) {
    return _getMdcStipplingSurfaceParamInfo(
      regionNo,
      targetAll,
      prm,
      color,
    );
  }

  late final _getMdcStipplingSurfaceParamInfoPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  ffi.Uint32,
                  ffi.Bool,
                  ffi.Pointer<MdcSewStippleParam>,
                  ffi.Pointer<ffi.UnsignedChar>)>>(
      'getMdcStipplingSurfaceParamInfo');
  late final _getMdcStipplingSurfaceParamInfo =
      _getMdcStipplingSurfaceParamInfoPtr.asFunction<
          int Function(int, bool, ffi.Pointer<MdcSewStippleParam>,
              ffi.Pointer<ffi.UnsignedChar>)>();

  /// デコラティブフィル縫製パラメータや色・デコラティブフィル種類などの情報取得
  int getMdcDecorativeFillSurfaceParamInfo(
    int regionNo,
    bool targetAll,
    ffi.Pointer<MdcSewDecoParam> prm,
    ffi.Pointer<ffi.UnsignedChar> color,
    ffi.Pointer<ffi.Bool> decorativeType,
    ffi.Pointer<ffi.Uint16> decorativeNo,
  ) {
    return _getMdcDecorativeFillSurfaceParamInfo(
      regionNo,
      targetAll,
      prm,
      color,
      decorativeType,
      decorativeNo,
    );
  }

  late final _getMdcDecorativeFillSurfaceParamInfoPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  ffi.Uint32,
                  ffi.Bool,
                  ffi.Pointer<MdcSewDecoParam>,
                  ffi.Pointer<ffi.UnsignedChar>,
                  ffi.Pointer<ffi.Bool>,
                  ffi.Pointer<ffi.Uint16>)>>(
      'getMdcDecorativeFillSurfaceParamInfo');
  late final _getMdcDecorativeFillSurfaceParamInfo =
      _getMdcDecorativeFillSurfaceParamInfoPtr.asFunction<
          int Function(
              int,
              bool,
              ffi.Pointer<MdcSewDecoParam>,
              ffi.Pointer<ffi.UnsignedChar>,
              ffi.Pointer<ffi.Bool>,
              ffi.Pointer<ffi.Uint16>)>();


  /// リージョン番号を指定して線の縫い種と色を変更する
  int updateMdcLineStitchTypeForRegion(
    int regionNo,
    bool targetAll,
    int kind,
    bool motifType,
    int motifNo,
    int color,
  ) {
    return _updateMdcLineStitchTypeForRegion(
      regionNo,
      targetAll,
      kind,
      motifType,
      motifNo,
      color,
    );
  }

  late final _updateMdcLineStitchTypeForRegionPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32,
              ffi.Bool,
              ffi.Int32,
              ffi.Bool,
              ffi.Uint16,
              ffi.UnsignedChar)>>('updateMdcLineStitchTypeForRegion');
  late final _updateMdcLineStitchTypeForRegion =
      _updateMdcLineStitchTypeForRegionPtr
          .asFunction<int Function(int, bool, int, bool, int, int)>();

  /// リージョン番号を指定して面の縫い種と色を変更する
  int updateMdcSurfaceStitchTypeForRegion(
    int regionNo,
    bool targetAll,
    int kind,
    bool decorativeType,
    int decorativeNo,
    int color,
  ) {
    return _updateMdcSurfaceStitchTypeForRegion(
      regionNo,
      targetAll,
      kind,
      decorativeType,
      decorativeNo,
      color,
    );
  }

  late final _updateMdcSurfaceStitchTypeForRegionPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32,
              ffi.Bool,
              ffi.Int32,
              ffi.Bool,
              ffi.Uint16,
              ffi.UnsignedChar)>>('updateMdcSurfaceStitchTypeForRegion');
  late final _updateMdcSurfaceStitchTypeForRegion =
      _updateMdcSurfaceStitchTypeForRegionPtr
          .asFunction<int Function(int, bool, int, bool, int, int)>();


  /// 全縫製パラメータのデフォルト値を取得する
  int getMdcSewParamDefaultData(
    ffi.Pointer<MdcSewLineAllParam_t> lineParam,
    ffi.Pointer<MdcSewSurfaceAllParam_t> surfParam,
  ) {
    return _getMdcSewParamDefaultData(
      lineParam,
      surfParam,
    );
  }

  late final _getMdcSewParamDefaultDataPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<MdcSewLineAllParam_t>,
                  ffi.Pointer<MdcSewSurfaceAllParam_t>)>>(
      'getMdcSewParamDefaultData');
  late final _getMdcSewParamDefaultData =
      _getMdcSewParamDefaultDataPtr.asFunction<
          int Function(ffi.Pointer<MdcSewLineAllParam_t>,
              ffi.Pointer<MdcSewSurfaceAllParam_t>)>();


  /// 5.3-1.2. 関数
//  int drawMdcSewingPen(
//    MdcPenTouchInfo_t pinfo,
//    ffi.Pointer<MdcReqProc_t> reqproc,
//    ffi.Pointer<MdcImageInfo_t> imgInfo,
//  ) {
//    return _drawMdcSewingPen(
//      pinfo,
//      reqproc,
//      imgInfo,
//    );
//  }
//
//  late final _drawMdcSewingPenPtr = _lookup<
//      ffi.NativeFunction<
//          ffi.Int32 Function(MdcPenTouchInfo_t, ffi.Pointer<MdcReqProc_t>,
//              ffi.Pointer<MdcImageInfo_t>)>>('drawMdcSewingPen');
//  late final _drawMdcSewingPen = _drawMdcSewingPenPtr.asFunction<
//      int Function(MdcPenTouchInfo_t, ffi.Pointer<MdcReqProc_t>,
//          ffi.Pointer<MdcImageInfo_t>)>();
//
  /// 5.4-1.2. 関数
//  int setMdcEnlargedDisplay(
//    int magnification,
//    ffi.Pointer<MdcImageInfo_t> imgInfo,
//  ) {
//    return _setMdcEnlargedDisplay(
//      magnification,
//      imgInfo,
//    );
//  }
//
//  late final _setMdcEnlargedDisplayPtr = _lookup<
//      ffi.NativeFunction<
//          ffi.Int32 Function(ffi.Int32,
//              ffi.Pointer<MdcImageInfo_t>)>>('setMdcEnlargedDisplay');
//  late final _setMdcEnlargedDisplay = _setMdcEnlargedDisplayPtr
//      .asFunction<int Function(int, ffi.Pointer<MdcImageInfo_t>)>();

  /// 指定リージョン番号のリージョン情報（縫い種、領域情報）を取得する
  int getMdcEditRegionInfo(
    int regionNo,
    ffi.Pointer<RegionInfo_t> regionInfo,
  ) {
    return _getMdcEditRegionInfo(
      regionNo,
      regionInfo,
    );
  }

  late final _getMdcEditRegionInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32, ffi.Pointer<RegionInfo_t>)>>('getMdcEditRegionInfo');
  late final _getMdcEditRegionInfo = _getMdcEditRegionInfoPtr
      .asFunction<int Function(int, ffi.Pointer<RegionInfo_t>)>();


///↓★getMdcEditRegionInfoAllData()がコールされるようになったら削除する予定(2025/3/3 H.Kawasaki)
  /// 全てのリージョン情報（リージョン番号、縫い種、領域情報）を取得する
  int getMdcEditRegionInfoAll(
    ffi.Pointer<RegionInfoAndNumber_t> regionInfoBuf,
    ffi.Pointer<ffi.Uint32> bufNum,
  ) {
    return _getMdcEditRegionInfoAll(
      regionInfoBuf,
      bufNum,
    );
  }

  late final _getMdcEditRegionInfoAllPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<RegionInfoAndNumber_t>,
              ffi.Pointer<ffi.Uint32>)>>('getMdcEditRegionInfoAll');
  late final _getMdcEditRegionInfoAll = _getMdcEditRegionInfoAllPtr.asFunction<
      int Function(
          ffi.Pointer<RegionInfoAndNumber_t>, ffi.Pointer<ffi.Uint32>)>();
///↑★getMdcEditRegionInfoAllData()がコールされるようになったら削除する予定(2025/3/3 H.Kawasaki)


  /// 全てのリージョン情報（リージョン番号、縫い種、領域情報）と選択状態となっているリージョン番号を取得する
  int getMdcEditRegionInfoAllData(
    ffi.Pointer<RegionInfoAndNumber_t> regionInfoBuf,
    ffi.Pointer<ffi.Uint32> bufNum,
    ffi.Pointer<ffi.Uint32> curRegionNo,
  ) {
    return _getMdcEditRegionInfoAllData(
      regionInfoBuf,
      bufNum,
      curRegionNo,
    );
  }

  late final _getMdcEditRegionInfoAllDataPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<RegionInfoAndNumber_t>,
              ffi.Pointer<ffi.Uint32>, ffi.Pointer<ffi.Uint32>)>>('getMdcEditRegionInfoAllData');
  late final _getMdcEditRegionInfoAllData = _getMdcEditRegionInfoAllDataPtr.asFunction<
      int Function(
          ffi.Pointer<RegionInfoAndNumber_t>, ffi.Pointer<ffi.Uint32>, ffi.Pointer<ffi.Uint32>)>();



  /// ----------- ↓追加IF↓ -----------
  int getMdcBuiltInDecorativeFillThumbnail(
    int number,
    int width,
    int height,
    int line_color,
    ffi.Pointer<MdcThumbImageInfo_t> imgThumbInfo,
  ) {
    return _getMdcBuiltInDecorativeFillThumbnail(
      number,
      width,
      height,
      line_color,
      imgThumbInfo,
    );
  }

  late final _getMdcBuiltInDecorativeFillThumbnailPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Uint8, ffi.Uint32, ffi.Uint32, ffi.Uint32,
                  ffi.Pointer<MdcThumbImageInfo_t>)>>(
      'getMdcBuiltInDecorativeFillThumbnail');
  late final _getMdcBuiltInDecorativeFillThumbnail =
      _getMdcBuiltInDecorativeFillThumbnailPtr.asFunction<
          int Function(
              int, int, int, int, ffi.Pointer<MdcThumbImageInfo_t>)>();

  int getMdcBuiltInDecorativeFillTotalNum(
    ffi.Pointer<ffi.Int32> totalNum,
  ) {
    return _getMdcBuiltInDecorativeFillTotalNum(
      totalNum,
    );
  }

  late final _getMdcBuiltInDecorativeFillTotalNumPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int32>)>>(
          'getMdcBuiltInDecorativeFillTotalNum');
  late final _getMdcBuiltInDecorativeFillTotalNum =
      _getMdcBuiltInDecorativeFillTotalNumPtr
          .asFunction<int Function(ffi.Pointer<ffi.Int32>)>();

  int getMdcBuiltInLineMotifThumbnail(
    int number,
    int width,
    int height,
    int line_color,
    ffi.Pointer<MdcThumbImageInfo_t> imgThumbInfo,
  ) {
    return _getMdcBuiltInLineMotifThumbnail(
      number,
      width,
      height,
      line_color,
      imgThumbInfo,
    );
  }

  late final _getMdcBuiltInLineMotifThumbnailPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Uint8, ffi.Uint32, ffi.Uint32, ffi.Uint32,
                  ffi.Pointer<MdcThumbImageInfo_t>)>>(
      'getMdcBuiltInLineMotifThumbnail');
  late final _getMdcBuiltInLineMotifThumbnail =
      _getMdcBuiltInLineMotifThumbnailPtr.asFunction<
          int Function(
              int, int, int, int, ffi.Pointer<MdcThumbImageInfo_t>)>();

  int getMdcBuiltInLineMotifTotalNum(
    ffi.Pointer<ffi.Int32> totalNum,
  ) {
    return _getMdcBuiltInLineMotifTotalNum(
      totalNum,
    );
  }

  late final _getMdcBuiltInLineMotifTotalNumPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int32>)>>(
          'getMdcBuiltInLineMotifTotalNum');
  late final _getMdcBuiltInLineMotifTotalNum =
      _getMdcBuiltInLineMotifTotalNumPtr
          .asFunction<int Function(ffi.Pointer<ffi.Int32>)>();

  int getMdcCustomDecorativeFillThumbnail(
    ffi.Pointer<ffi.Char> filename,
    int width,
    int height,
    int line_color,
    ffi.Pointer<MdcThumbImageInfo_t> imgThumbInfo,
  ) {
    return _getMdcCustomDecorativeFillThumbnail(
      filename,
      width,
      height,
      line_color,
      imgThumbInfo,
    );
  }

  late final _getMdcCustomDecorativeFillThumbnailPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<ffi.Char>, ffi.Uint32, ffi.Uint32,
                  ffi.Uint32, ffi.Pointer<MdcThumbImageInfo_t>)>>(
      'getMdcCustomDecorativeFillThumbnail');
  late final _getMdcCustomDecorativeFillThumbnail =
      _getMdcCustomDecorativeFillThumbnailPtr.asFunction<
          int Function(ffi.Pointer<ffi.Char>, int, int, int,
              ffi.Pointer<MdcThumbImageInfo_t>)>();

  int getMdcCustomLineMotifThumbnail(
    ffi.Pointer<ffi.Char> filename,
    int width,
    int height,
    int line_color,
    ffi.Pointer<MdcThumbImageInfo_t> imgThumbInfo,
  ) {
    return _getMdcCustomLineMotifThumbnail(
      filename,
      width,
      height,
      line_color,
      imgThumbInfo,
    );
  }

  late final _getMdcCustomLineMotifThumbnailPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<ffi.Char>, ffi.Uint32, ffi.Uint32,
                  ffi.Uint32, ffi.Pointer<MdcThumbImageInfo_t>)>>(
      'getMdcCustomLineMotifThumbnail');
  late final _getMdcCustomLineMotifThumbnail =
      _getMdcCustomLineMotifThumbnailPtr.asFunction<
          int Function(ffi.Pointer<ffi.Char>, int, int, int,
              ffi.Pointer<MdcThumbImageInfo_t>)>();

  int getMdcBuiltInSpecifyStampThumbnail(
    int stampKind,
    int number,
    int width,
    int height,
    int line_color,
    int surface_color,
    ffi.Pointer<MdcThumbImageInfo_t> imgThumbInfo,
  ) {
    return _getMdcBuiltInSpecifyStampThumbnail(
      stampKind,
      number,
      width,
      height,
      line_color,
      surface_color,
      imgThumbInfo,
    );
  }

  late final _getMdcBuiltInSpecifyStampThumbnailPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Int32, ffi.Uint8, ffi.Uint32, ffi.Uint32, ffi.Uint32, ffi.Uint32,
                  ffi.Pointer<MdcThumbImageInfo_t>)>>(
      'getMdcBuiltInSpecifyStampThumbnail');
  late final _getMdcBuiltInSpecifyStampThumbnail =
      _getMdcBuiltInSpecifyStampThumbnailPtr.asFunction<
          int Function(int, int, int, int, int, int, ffi.Pointer<MdcThumbImageInfo_t>)>();

  int getMdcBuiltInSpecifyStampTotalNum(
    int stampKind,
    ffi.Pointer<ffi.Int32> totalNum,
  ) {
    return _getMdcBuiltInSpecifyStampTotalNum(
      stampKind,
      totalNum,
    );
  }

  late final _getMdcBuiltInSpecifyStampTotalNumPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Int32,
              ffi.Pointer<ffi.Int32>)>>('getMdcBuiltInSpecifyStampTotalNum');
  late final _getMdcBuiltInSpecifyStampTotalNum =
      _getMdcBuiltInSpecifyStampTotalNumPtr
          .asFunction<int Function(int, ffi.Pointer<ffi.Int32>)>();

  int getMdcOutlineStampThumbnail(
    ffi.Pointer<ffi.Char> filename,
    int width,
    int height,
    int line_color,
    ffi.Pointer<MdcThumbImageInfo_t> imgThumbInfo,
  ) {
    return _getMdcOutlineStampThumbnail(
      filename,
      width,
      height,
      line_color,
      imgThumbInfo,
    );
  }

  late final _getMdcOutlineStampThumbnailPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<ffi.Char>, ffi.Uint32, ffi.Uint32, ffi.Uint32,
                  ffi.Pointer<MdcThumbImageInfo_t>)>>(
      'getMdcOutlineStampThumbnail');
  late final _getMdcOutlineStampThumbnail =
      _getMdcOutlineStampThumbnailPtr.asFunction<
          int Function(ffi.Pointer<ffi.Char>, int, int, int,
              ffi.Pointer<MdcThumbImageInfo_t>)>();

  int getMdcCuttingStampThumbnail(
    ffi.Pointer<ffi.Char> filename,
    int width,
    int height,
    int line_color,
    ffi.Pointer<MdcThumbImageInfo_t> imgThumbInfo,
  ) {
    return _getMdcCuttingStampThumbnail(
      filename,
      width,
      height,
      line_color,
      imgThumbInfo,
    );
  }

  late final _getMdcCuttingStampThumbnailPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<ffi.Char>, ffi.Uint32, ffi.Uint32, ffi.Uint32,
                  ffi.Pointer<MdcThumbImageInfo_t>)>>(
      'getMdcCuttingStampThumbnail');
  late final _getMdcCuttingStampThumbnail =
      _getMdcCuttingStampThumbnailPtr.asFunction<
          int Function(ffi.Pointer<ffi.Char>, int, int, int,
              ffi.Pointer<MdcThumbImageInfo_t>)>();

  int delMdcThumbImage(
    ffi.Pointer<MdcThumbImageInfo_t> imgThumbInfo,
  ) {
    return _delMdcThumbImage(
      imgThumbInfo,
    );
  }

  late final _delMdcThumbImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<MdcThumbImageInfo_t>)>>('delMdcThumbImage');
  late final _delMdcThumbImage = _delMdcThumbImagePtr
      .asFunction<int Function(ffi.Pointer<MdcThumbImageInfo_t>)>();

  int isMdcUsingImportDataNumber(
    bool all_chk_flg,
    ffi.Pointer<MdcImportDataUsingNumber_t> usingNumBuff,
  ) {
    return _isMdcUsingImportDataNumber(
      all_chk_flg,
      usingNumBuff,
    );
  }

  late final _isMdcUsingImportDataNumberPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  ffi.Bool, ffi.Pointer<MdcImportDataUsingNumber_t>)>>(
      'isMdcUsingImportDataNumber');
  late final _isMdcUsingImportDataNumber =
      _isMdcUsingImportDataNumberPtr.asFunction<
          int Function(bool, ffi.Pointer<MdcImportDataUsingNumber_t>)>();

  /// 現在のお絵描きイメージを取得する
  int getMdcCurrentDrawingImage(
    ffi.Pointer<MdcReqProc_t> reqproc,
    ffi.Pointer<MdcImageInfo_t> imgInfoBackGround,
  ) {
    return _getMdcCurrentDrawingImage(
      reqproc,
      imgInfoBackGround,
    );
  }

  late final _getMdcCurrentDrawingImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<MdcReqProc_t>,
              ffi.Pointer<MdcImageInfo_t>)>>('getMdcCurrentDrawingImage');
  late final _getMdcCurrentDrawingImage =
      _getMdcCurrentDrawingImagePtr.asFunction<
          int Function(
              ffi.Pointer<MdcReqProc_t>, ffi.Pointer<MdcImageInfo_t>)>();


  /// 指定したスティップリング、デコラティブフィルの全体画像(お絵描きイメージ全体サイズの画像)を取得する
  int getMdcStipplingDecofillMaxDrawingImage(
    int targetKind,
    int targetNumber,
    ffi.Pointer<MdcImageInfo_t> imgInfo,
  ) {
    return _getMdcStipplingDecofillMaxDrawingImage(
      targetKind,
      targetNumber,
      imgInfo,
    );
  }

  late final _getMdcStipplingDecofillMaxDrawingImagePtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  ffi.Int32, ffi.Uint32, ffi.Pointer<MdcImageInfo_t>)>>(
      'getMdcStipplingDecofillMaxDrawingImage');
  late final _getMdcStipplingDecofillMaxDrawingImage =
      _getMdcStipplingDecofillMaxDrawingImagePtr
          .asFunction<int Function(int, int, ffi.Pointer<MdcImageInfo_t>)>();


  /// お絵描き模様の有無を確認する
  int isMdcDrawingPatternPresence(
    ffi.Pointer<ffi.Bool> result,
  ) {
    return _isMdcDrawingPatternPresence(
      result,
    );
  }

  late final _isMdcDrawingPatternPresencePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>('isMdcDrawingPatternPresence');
  late final _isMdcDrawingPatternPresence =
      _isMdcDrawingPatternPresencePtr.asFunction<
          int Function(
              ffi.Pointer<ffi.Bool>)>();

  /// お絵描き模様の有無を確認する([Next]用)
  int isMdcDrawingPatternPresenceForNext(
      ffi.Pointer<ffi.Bool> result,
      ffi.Pointer<MdcReqProc_t> reqproc,
      ) {
    return _isMdcDrawingPatternPresenceForNext(
      result,
      reqproc,
    );
  }

  late final _isMdcDrawingPatternPresenceForNextPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Bool>, ffi.Pointer<MdcReqProc_t>)>>('isMdcDrawingPatternPresenceForNext');
  late final _isMdcDrawingPatternPresenceForNext =
  _isMdcDrawingPatternPresenceForNextPtr.asFunction<
      int Function(
          ffi.Pointer<ffi.Bool>, ffi.Pointer<MdcReqProc_t>)>();

	int saveMdcPM9File(
    ffi.Pointer<ffi.Char> filename,
  ) {
    return _saveMdcPM9File(
      filename,
    );
  }

  late final _saveMdcPM9FilePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Char>)>>(
          'saveMdcPM9File');
  late final _saveMdcPM9File =
      _saveMdcPM9FilePtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int saveMdcPHXFile(
    ffi.Pointer<ffi.Char> filename,
  ) {
    return _saveMdcPHXFile(
      filename,
    );
  }

  late final _saveMdcPHXFilePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Char>)>>(
          'saveMdcPHXFile');
  late final _saveMdcPHXFile =
      _saveMdcPHXFilePtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int saveFcmDataToPce(
    ffi.Pointer<ffi.Uint8> fcm_data,
    int fcm_data_size,
    ffi.Pointer<ffi.Char> filename_pce,
  ) {
    return _saveFcmDataToPce(
      fcm_data,
      fcm_data_size,
      filename_pce,
    );
  }

  late final _saveFcmDataToPcePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Uint8>, ffi.Int32,
              ffi.Pointer<ffi.Char>)>>('saveFcmDataToPce');
  late final _saveFcmDataToPce = _saveFcmDataToPcePtr
      .asFunction<int Function(ffi.Pointer<ffi.Uint8>, int, ffi.Pointer<ffi.Char>)>();


  /// ↓IIVO #321 PMF/PLFファイルチェック hatanomi
  int isMdcPmfFileValid(
    ffi.Pointer<ffi.Char> filename,
    ffi.Pointer<ffi.Bool> result,
  ) {
    return _isMdcPmfFileValid(
      filename,
      result,
    );
  }

  late final _isMdcPmfFileValidPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Bool>)>>('isMdcPmfFileValid');
  late final _isMdcPmfFileValid = _isMdcPmfFileValidPtr
      .asFunction<int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Bool>)>();


  int isMdcPlfFileValid(
    ffi.Pointer<ffi.Char> filename,
    ffi.Pointer<ffi.Bool> result,
  ) {
    return _isMdcPlfFileValid(
      filename,
      result,
    );
  }

  late final _isMdcPlfFileValidPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Bool>)>>('isMdcPlfFileValid');
  late final _isMdcPlfFileValid = _isMdcPlfFileValidPtr
      .asFunction<int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Bool>)>();


  ///~~Embの関数~~
  int openEmbMode() {
    return _openEmbMode();
  }

  late final _openEmbModePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('openEmbMode');
  late final _openEmbMode = _openEmbModePtr.asFunction<int Function()>();

  int getSelectedGroupARGBImage(
    MemHandle_t groupH,
    int centerType,
    int scale,
    bool imageType,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getSelectedGroupARGBImage(
      groupH,
      centerType,
      scale,
      imageType,
      embImageInfo,
    );
  }

  late final _getSelectedGroupARGBImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(MemHandle_t, ffi.Int32, ffi.Int32, ffi.Bool,
              ffi.Pointer<embImageInfo_t>)>>('getSelectedGroupARGBImage');
  late final _getSelectedGroupARGBImage =
      _getSelectedGroupARGBImagePtr.asFunction<
          int Function(
              MemHandle_t, int, int, bool, ffi.Pointer<embImageInfo_t>)>();

  int getRealImage(
    bool emb,
    bool backDisplay,
    bool displayPosition,
    int scale,
    ffi.Pointer<realImageInfo_t> realImageInfo,
  ) {
    return _getRealImage(
      emb,
      backDisplay,
      displayPosition,
      scale,
      realImageInfo,
    );
  }

  late final _getRealImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Bool, ffi.Bool, ffi.Bool, ffi.Int32,
              ffi.Pointer<realImageInfo_t>)>>('getRealImage');
  late final _getRealImage = _getRealImagePtr.asFunction<
      int Function(bool, bool, bool, int, ffi.Pointer<realImageInfo_t>)>();

  int getInfoImage(
    bool backDisplay,
    int backColor,
    ffi.Pointer<embImg_t> infoImage,
  ) {
    return _getInfoImage(
      backDisplay,
      backColor,
      infoImage,
    );
  }

  late final _getInfoImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Bool, ffi.Uint32, ffi.Pointer<embImg_t>)>>('getInfoImage');
  late final _getInfoImage = _getInfoImagePtr
      .asFunction<int Function(bool, int, ffi.Pointer<embImg_t>)>();


  //
  int noticeMdcDraw(
      ffi.Pointer<MdcReqProc_t> reqproc,
      ) {
    return _noticeMdcDraw(
        reqproc
    );
  }
  late final _noticeMdcDrawPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<MdcReqProc_t>)>>('noticeMdcDraw');
  late final _noticeMdcDraw = _noticeMdcDrawPtr
      .asFunction<int Function( ffi.Pointer<MdcReqProc_t>)>();
  //
  int reserveMdcLineList(
      int	pointNum,
      ffi.Pointer<SSPoint_t> pointList,
      ffi.Pointer<MdcReqProc_t> reqproc
      ) {
    return _reserveMdcLineList(
        pointNum,
        pointList,
        reqproc
    );
  }
  late final _reserveMdcLineListPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32, ffi.Pointer<SSPoint_t>,ffi.Pointer<MdcReqProc_t>)>>('reserveMdcLineList');
  late final _reserveMdcLineList = _reserveMdcLineListPtr
      .asFunction<int Function( int, ffi.Pointer<SSPoint_t>,ffi.Pointer<MdcReqProc_t>)>();
  //
  int getMdcReservedLineDraw(
      ffi.Pointer<MdcReqProc_t> reqproc,
      ffi.Pointer<MdcImageInfo_t> imgInfo
      ) {
    return _getMdcReservedLineDraw(
        reqproc,
        imgInfo
    );
  }
  late final _getMdcReservedLineDrawPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
            ffi.Pointer<MdcReqProc_t>,ffi.Pointer<MdcImageInfo_t>)>>('getMdcReservedLineDraw');
  late final _getMdcReservedLineDraw = _getMdcReservedLineDrawPtr
      .asFunction<int Function( ffi.Pointer<MdcReqProc_t>,ffi.Pointer<MdcImageInfo_t>)>();
  //
  int editMdcReservedLine(
      int	pointNum,
      ffi.Pointer<SSPoint_t> pointList,
      ffi.Pointer<MdcReqProc_t> reqproc,
      ffi.Pointer<MdcImageInfo_t> imgInfo
      ) {
    return _editMdcReservedLine(
        pointNum,
        pointList,
        reqproc,
        imgInfo
    );
  }
  late final _editMdcReservedLinePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32, ffi.Pointer<SSPoint_t>,ffi.Pointer<MdcReqProc_t>,ffi.Pointer<MdcImageInfo_t>)>>('editMdcReservedLine');
  late final _editMdcReservedLine = _editMdcReservedLinePtr
      .asFunction<int Function( int, ffi.Pointer<SSPoint_t>, ffi.Pointer<MdcReqProc_t>,ffi.Pointer<MdcImageInfo_t>)>();


  /// MDC Paint→Emb Selectに戻る
  int checkMDCPaintToEmbSelect(
    bool undoRedoFlg,
    bool scanImageFlg,
  ) {
    return _checkMDCPaintToEmbSelect(
      undoRedoFlg,
      scanImageFlg,
    );
  }

  late final _checkMDCPaintToEmbSelectPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Bool, ffi.Bool)>>('checkMDCPaintToEmbSelect');
  late final _checkMDCPaintToEmbSelect = _checkMDCPaintToEmbSelectPtr
      .asFunction<int Function(bool, bool)>();

  int gotoMDCPaintToEmbSelect() {
    return _gotoMDCPaintToEmbSelect();
  }

  late final _gotoMDCPaintToEmbSelectPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
      'gotoMDCPaintToEmbSelect');
  late final _gotoMDCPaintToEmbSelect =
  _gotoMDCPaintToEmbSelectPtr.asFunction<int Function()>();

  /// MDC Paint→MDC 縫製設定へ遷移
  int gotoMDCPaintToMDCDetailSet() {
    return _gotoMDCPaintToMDCDetailSet();
  }

  late final _gotoMDCPaintToMDCDetailSetPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
      'gotoMDCPaintToMDCDetailSet');
  late final _gotoMDCPaintToMDCDetailSet =
  _gotoMDCPaintToMDCDetailSetPtr.asFunction<int Function()>();

  /// MDC 縫製設定→MDC Paintに戻る
  int gotoMDCDetailSetToMDCPaint() {
    return _gotoMDCDetailSetToMDCPaint();
  }

  late final _gotoMDCDetailSetToMDCPaintPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
      'gotoMDCDetailSetToMDCPaint');
  late final _gotoMDCDetailSetToMDCPaint =
  _gotoMDCDetailSetToMDCPaintPtr.asFunction<int Function()>();

  /// MDC 縫製設定→Emb Editへ遷移
  int checkMDCDetailSetToEmbEdit() {
    return _checkMDCDetailSetToEmbEdit();
  }

  late final _checkMDCDetailSetToEmbEditPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
      'checkMDCDetailSetToEmbEdit');
  late final _checkMDCDetailSetToEmbEdit =
  _checkMDCDetailSetToEmbEditPtr.asFunction<int Function()>();

 int gotoMDCDetailSetToEmbEdit() {
    return _gotoMDCDetailSetToEmbEdit();
  }

  late final _gotoMDCDetailSetToEmbEditPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
      'gotoMDCDetailSetToEmbEdit');
  late final _gotoMDCDetailSetToEmbEdit =
  _gotoMDCDetailSetToEmbEditPtr.asFunction<int Function()>();


}

typedef MemHandle_t = ffi.Pointer<ffi.Void>;

abstract class ColorCode_t {
  /// 透明
  static const int CLEAR = 0;

  /// 1 アイイロ
  static const int PRUSSIAN_BLUE = 1;

  /// 2 アオ
  static const int BLUE = 2;

  /// 3 アオミドリ
  static const int TEAL_GREEN = 3;

  /// 4 アオムラサキ
  static const int CORN_FLOWER_BLUE = 4;

  /// 5 アカ
  static const int RED = 5;

  /// 6 アカチャイロ
  static const int REDDISH_BROWN = 6;

  /// 7 アカムラサキ
  static const int MAGENTA = 7;

  /// 8 ウスアカムラサキ
  static const int LIGHT_LILAC = 8;

  /// 9 ウスムラサキ
  static const int LILAC = 9;

  /// 10 エメラルドグリーン
  static const int MINT_GREEN = 10;

  /// 11 オウドイロ
  static const int DEEP_GOLD = 11;

  /// 12 オレンジ
  static const int ORANGE = 12;

  /// 13 キイロ
  static const int YELLOW = 13;

  /// 14 キミドリ
  static const int LIME_GREEN = 14;

  /// 15キンチャ
  static const int BRASS = 15;

  /// 16 ギン
  static const int SILVER = 16;

  /// 17 クチバイロ
  static const int RUSSET_BROWN = 17;

  /// 18 クリームイロ
  static const int CREAM_BROWN = 18;

  /// 19 グレー
  static const int PEWTER = 19;

  /// 20 クロ
  static const int BLACK = 20;

  /// 21 グンジョウイロ
  static const int ULTRA_MARINE = 21;

  /// 22 コイアカムラサキ
  static const int ROYAL_PURPLE = 22;

  /// 23 コイグレー
  static const int DARK_GRAY = 23;

  /// 24 コイコゲチャ
  static const int DARK_BROWN = 24;

  /// 25 コイピンク
  static const int DEEP_ROSE = 25;

  /// 26 コゲチャ
  static const int LIGHT_BROWN = 26;

  /// 27 サーモンピンク
  static const int SALMON_PINK = 27;

  /// 28 シュイロ
  static const int VERMILION = 28;

  /// 29 シロ
  static const int WHITE = 29;

  /// 30 スミレイロ
  static const int VIOLET = 30;

  /// 31 セイジイロ
  static const int SEACREST = 31;

  /// 32 ソライロ
  static const int SKY_BLUE = 32;

  /// 33 ダイダイイロ
  static const int PUMPKIN = 33;

  /// 34 タマゴイロ
  static const int CREAM_YELLOW = 34;

  /// 35 ツチイロ
  static const int KHAKI = 35;

  /// 36 チャイロ
  static const int CLAY_BROWN = 36;

  /// 37 トキワイロ
  static const int LEAF_GREEN = 37;

  /// 38 ナンドイロ
  static const int PEACOCK_BLUE = 38;

  /// 39 ネズミイロ
  static const int GRAY = 39;

  /// 40 ハイイロ
  static const int WARM_GRAY = 40;

  /// 41 ハイミドリ
  static const int DARK_OLIVE = 41;

  /// 42 ハダイロ
  static const int LINEN = 42;

  /// 43 ピンク
  static const int PINK = 43;

  /// 44 フカミドリ
  static const int DEEP_GREEN = 44;

  /// 45 フジイロ
  static const int LAVENDER = 45;

  /// 46 フジムラサキ
  static const int WISTARIA_VIOLET = 46;

  /// 47 ベージュ
  static const int BEIGE = 47;

  /// 48 ベニイロ
  static const int CARMINE = 48;

  /// 49 ベニカバイロ
  static const int AMBER_RED = 49;

  /// 50 マツバイロ
  static const int OLIVE_GREEN = 50;

  /// 51 マゼンダ
  static const int DARK_FUSCHIA = 51;

  /// 52 マンダリン
  static const int TANGERINE = 52;

  /// 53 ミズイロ
  static const int LIGHT_BLUE = 53;

  /// 54 ミドリ
  static const int EMERALD_GREEN = 54;

  /// 55 ムラサキ
  static const int PURPLE = 55;

  /// 56 モスグリーン
  static const int MOSS_GREEN = 56;

  /// 57 モモイロ
  static const int FLESH_PINK = 57;

  /// 58 ヤマブキイロ
  static const int HARVEST_GOLD = 58;

  /// 59 ライトブルー
  static const int ELECTRIC_BLUE = 59;

  /// 60 レモンイロ
  static const int LEMON_YELLOW = 60;

  /// 61 ワカバイロ
  static const int FRESH_GREEN = 61;

  /// 62 アップリケピース
  static const int APPLIQUE_MATERIAL = 62;

  /// 63 アップリケ　ノ　イ
  static const int APPLIQUE_POSITION = 63;

  /// 64 アップリケ
  static const int APPLIQUE = 64;

  /// 65 透明
  static const int NO_DEFINE = 65;

  /// 66 サテン
  static const int SATIN = 66;

  /// Dmy
  static const int COLOR_MAX = 67;

  /// 無効値
  static const int INVALID_STD_CODE = 255;
}

/// MDC縫製設定画面のリージョン情報関連 ///
/// リージョン情報構造体
final class RegionInfo_t extends ffi.Struct {
  /// 線・面(0:面縫い、1:線縫い)
  @ffi.Uint32()
  external int attr;

  /// 縫い種 ※線、面によって値域が異なる
  @ffi.Uint32()
  external int sew;

  /// 線モチーフタイプ、または、デコラティブフィルタイプ(false:内蔵模様、true:ユーザー作成模様)
  @ffi.Bool()
  external bool type;

  /// 線モチーフ番号またはデコラティブフィル番号(1～)
  @ffi.Uint16()
  external int number;

  /// リージョン領域 開始位置情報(左上X座標)	※赤枠表示、タッチ位置判定などで利用する情報
  @ffi.Uint32()
  external int x;

  /// リージョン領域 開始位置情報(左上Y座標)	※赤枠表示、タッチ位置判定などで利用する情報
  @ffi.Uint32()
  external int y;

  /// リージョン領域 サイズ(幅)					※赤枠表示、タッチ位置判定などで利用する情報
  @ffi.Uint32()
  external int w;

  /// リージョン領域 サイズ(高さ)				※赤枠表示、タッチ位置判定などで利用する情報
  @ffi.Uint32()
  external int h;

  static ffi.Pointer<RegionInfo_t> allocate() {
    final pointer = ffi.calloc<RegionInfo_t>();
    pointer.ref.sew = 0;
    pointer.ref.type = false;
    pointer.ref.number = 0;
    pointer.ref.x = 0;
    pointer.ref.y = 0;
    pointer.ref.w = 0;
    pointer.ref.h = 0;
    return pointer;
  }
}

/// リージョン情報とリージョン番号の構造体
final class RegionInfoAndNumber_t extends ffi.Struct {
  /// リージョン番号
  @ffi.Uint32()
  external int regionNo;

  /// リージョン情報
  external RegionInfo_t regionInfo;

  static ffi.Pointer<RegionInfoAndNumber_t> allocate() {
    final pointer = ffi.calloc<RegionInfoAndNumber_t>();
    pointer.ref.regionNo = 0;
    return pointer;
  }

  static ffi.Pointer<ffi.Pointer<RegionInfoAndNumber_t>> allocate_p() {
    final pointer = ffi.calloc<ffi.Pointer<RegionInfoAndNumber_t>>();
    return pointer;
  }
//  // メモリを確保するための静的メソッド
//  static ffi.Pointer<RegionInfoAndNumber_t> allocate() {
//    final pointer = ffi.calloc<RegionInfoAndNumber_t>();
//    pointer.ref.regionNo = 0;
//    return pointer;
//  }
}

// エラーコード
enum MdcErrorCode_t
{
  mdcNoError,							// エラーなし
  mdcEditActiveOK,						// MDC Edit モード起動 成功
  mdcEditResumeOK,						// MDC Edit resume 成功
  mdcEditInactiveOK,						// MDC Edit モード終了 成功
  //-----------------------------------------------------------------------------
  mdcBoundaryNoError,						// ここまで 非エラー
  //-----------------------------------------------------------------------------
  mdcNoErrorNoImage,						// 画像生成対象ではない
  mdcStillPreReqInAct,					// 前の要求対応中
  mdcNoErrorReadPM9oldVersion,			// 読み込むPM9は古いバージョン ※XP,W2,W1(GUK1)
  //-----------------------------------------------------------------------------
  mdcBoundaryWarning,						// ここまで Warning 対象、以下エラー
  //-----------------------------------------------------------------------------
  mdcErrorDefault,						// 処理用初期値。この値が帰ってきたら処理もれ

  mdcEditActiveNG,						// MDC Edit モード起動 失敗
  mdcEditResumeNG,						// MDC Edit resume 失敗
  mdcEditInactiveNG,						// MDC Edit モード終了 失敗
  mdcErrorNotInMode,						// MdcEditMode になっていない
  mdcErrorNoExistRequest,					// 要求が見当たらない
  mdcErrorWrongParam,						// パラメータエラー
  mdcErrorSetParam,						// パラメータ設定失敗
  mdcErrorMemoryAllocationFailure,		// メモリ獲得失敗

  mdcErrorReadFileFailed,					// MDCデータファイル読み出し失敗

  mdcImageDataAddressNotNull,				// 画像データ格納先アドレス 初期化漏れ
  mdcMismatchProcStatus,					// 開始されていないproc 番号で継続/終了 指示が出された
  mdcErrorSelectReqProcStartingStatus,	// proc status 異常
  mdcErrorControlAreaAllocationFailure,	// 管理領域獲得失敗

  mdcErrorDataAcquisitionFailure,			// データの取得失敗

  mdcErrorImageOutOfRange,				// イメージが範囲外に移動
  mdcErrorNoImageExist,					// 描画対象データなし
  mdcErrorNoRegionExist,					// Region 対象なし
  mdcErrorNoMatchRegion,					// 該当 Region なし
  mdcErrorTransDataFailed,				// MDC->stitch データ変換失敗		☆☆以下のステッチ展開エラーを追加したので使用予定なし(削除予定)
  mdcErrorNotExchangeAreaOver,			// ステッチ展開/縫製エリアオーバー
  mdcErrorThisPatternTooComplex,			// ステッチ展開/データ不正、変換失敗
  mdcErrorTooAllocFailure,				// ステッチ展開/メモリフル
  mdcErrorCancel,							// ステッチ展開/キャンセル
  mdcErrorFileSaveFailed,					// ファイル保存失敗
  mdcErrorFileReadFailed,					// ファイル読込失敗

  mdcErrorFile, 							// ファイルエラー
  mdcNoHistoryRegist,						// 履歴ファイルなし

  mdcErrorNullPtrPointed,
  mdcErrorPartNotSelected,				// お絵描き模様を選択していない
  mdcErrorPartNotFixed,					// お絵描き模様の範囲選択中のため選択確定していない
  mdcErrorSelectedAreaNotImage,			// 選択エリアに模様なし
  mdcErrorMismatchDrawingType,			// 設定されている描画種別が不適切
  mdcErrorResetNotNeed,					// サイズ・位置などのリセット不要
  mdcErrorReservePointOver,				// お絵描き模様 描画線一括登録 点数オーバー
  mdcErrorMismatchRegionNo,				// 指定したリージョン番号が不適切(リージョンの縫い種に対応していない処理を行おうした場合)

  mdcErrorMemoryFull, 					// メモリフル
  //-----------------------------------------------------------------------------
  mdcErrorInvalid,						// 無効音　遷移不可
  mdcErrorInvalidPanel,					// エラー発生
  mdcErrorTransitionOK,					// ポップアップ確認が不要
  mdcErrorRequiresConfirmation,			// ポップアップ確認が必要

  mdcErrorMax
}


/// Sampleアプリ用のエラーコード
// ※上記のenum定義を追加したタイミングで削除したかったが、
//   Sampleアプリで多数のエラーが発生したため、暫定で名前を変えて残しておく。
//   最終的には削除する予定。(2024/7/18 H.Kawasaki)
abstract class MdcErrorCode_t_tmp {
  /// エラーなし
  static const int mdcNoError = 0;

  /// MDC Edit モード起動 成功
  static const int mdcEditActiveOK = 1;

  /// MDC Edit resume 成功
  static const int mdcEditResumeOK = 2;

  /// MDC Edit モード終了 成功
  static const int mdcEditInactiveOK = 3;

  /// ここまで 非エラー
  static const int mdcBoundaryNoError = 4;

  /// 画像生成対象ではない
  static const int mdcNoErrorNoImage = 5;

  /// 前の要求対応中
  static const int mdcStillPreReqInAct = 6;

  /// 読み込むPM9は古いバージョン ※XP,W2,W1(GUK1)		// IIVO PM9旧バージョン読込時のメッセージ表示対応 H.Kawasaki
  static const int mdcNoErrorReadPM9oldVersion = 7;

  /// ここまで Warning 対象、以下エラー
  static const int mdcBoundaryWarning = 8;

  /// 処理用初期値。この値が帰ってきたら処理もれ
  static const int mdcErrorDefault = 9;

  /// MDC Edit モード起動 失敗
  static const int mdcEditActiveNG = 10;

  /// MDC Edit resume 失敗
  static const int mdcEditResumeNG = 11;

  /// MDC Edit モード終了 失敗
  static const int mdcEditInactiveNG = 12;

  /// MdcEditMode になっていない
  static const int mdcErrorNotInMode = 13;

  /// 要求が見当たらない
  static const int mdcErrorNoExistRequest = 14;

  /// パラメータエラー
  static const int mdcErrorWrongParam = 15;

  /// パラメータ設定失敗
  static const int mdcErrorSetParam = 16;

  /// メモリ獲得失敗
  static const int mdcErrorMemoryAllocationFailure = 17;

  /// MDCデータファイル読み出し失敗
  static const int mdcErrorReadFileFailed = 18;

  /// 画像データ格納先アドレス 初期化漏れ
  static const int mdcImageDataAddressNotNull = 19;

  /// 開始されていないproc 番号で継続/終了 指示が出された
  static const int mdcMismatchProcStatus = 20;

  /// proc status 異常
  static const int mdcErrorSelectReqProcStartingStatus = 21;

  /// 管理領域獲得失敗
  static const int mdcErrorControlAreaAllocationFailure = 22;

  /// データの取得失敗
  static const int mdcErrorDataAcquisitionFailure = 23;

  /// イメージが範囲外に移動
  static const int mdcErrorImageOutOfRange = 24;

  /// 描画対象データなし
  static const int mdcErrorNoImageExist = 25;

  /// Region 対象なし
  static const int mdcErrorNoRegionExist = 26;

  /// 該当 Region なし
  static const int mdcErrorNoMatchRegion = 27;

  /// MDC->stitch データ変換失敗
  static const int mdcErrorTransDataFailed = 28;

  /// ファイル保存失敗
  static const int mdcErrorFileSaveFailed = 29;

  /// ファイル読込失敗
  static const int mdcErrorFileReadFailed = 30;

  /// ファイルエラー
  static const int mdcErrorFile = 31;

  /// 履歴ファイルなし
  static const int mdcNoHistoryRegist = 32;
  static const int mdcErrorNullPtrPointed = 33;

  /// お絵描き模様を選択していない
  static const int mdcErrorPartNotSelected = 34;

  /// お絵描き模様の範囲選択中のため選択確定していない
  static const int mdcErrorPartNotFixed = 35;

  /// 選択エリアに模様なし
  static const int mdcErrorSelectedAreaNotImage = 36;

  /// 設定されている描画種別が不適切
  static const int mdcErrorMismatchDrawingType = 37;

  /// サイズ・位置などのリセット不要
  static const int mdcErrorResetNotNeed = 38;

  /// -----------------------------------------------------------------------------
  static const int mdcErrorMax = 39;
}


/// イメージ取得構造体
final class MdcImageInfo_t extends ffi.Struct {
  /// 描画開始座標:X
  @ffi.Int32()
  external int startPointX;

  /// 描画開始座標:Y
  @ffi.Int32()
  external int startPointY;

  /// 描画領域:幅
  @ffi.Int32()
  external int imageWidth;

  /// 描画領域:高さ
  @ffi.Int32()
  external int imageHeight;

  /// 描画データ:サイズ
  @ffi.Int32()
  external int imageSize;

  /// 描画データ:データ
  external ffi.Pointer<ffi.Uint8> imageData;

  ///
  //@ffi.Int32()
  //external int requestId;
  static ffi.Pointer<MdcImageInfo_t> allocate() {
    final pointer = ffi.calloc<MdcImageInfo_t>();
    pointer.ref.startPointX = 0;
    pointer.ref.startPointY = 0;
    pointer.ref.imageWidth = 0;
    pointer.ref.imageHeight = 0;
    pointer.ref.imageSize = 0;
    // NSS No.576 メモリリーク対応
    // imageDataはC++層で確保、解放されている。
    // 現状ロジックでは以下のメモリをdart層で確保する必要がなく、解放する経路もないためnullptrにするべき
    //pointer.ref.imageData = ffi.calloc<ffi.Uint8>();
    //pointer.ref.imageData.value = 0;
    pointer.ref.imageData = ffi.nullptr;

    return pointer;
  }
}

/*----------- ↓追加IF↓ -----------*/
/// サムネイルイメージ取得構造体
final class MdcThumbImageInfo_t extends ffi.Struct {
  /// 描画領域:幅
  @ffi.Int32()
  external int imageWidth;

  /// 描画領域:高さ
  @ffi.Int32()
  external int imageHeight;

  /// 描画データ:サイズ
  @ffi.Int32()
  external int imageSize;

  /// 描画データ:データ
  external ffi.Pointer<ffi.Uint8> imageData;

  static ffi.Pointer<MdcThumbImageInfo_t> allocate() {
    final pointer = ffi.calloc<MdcThumbImageInfo_t>();
    pointer.ref.imageWidth = 0;
    pointer.ref.imageHeight = 0;
    pointer.ref.imageSize = 0;
    pointer.ref.imageData = ffi.calloc<ffi.Uint8>();
    pointer.ref.imageData.value = 0;
    return pointer;
  }
}

/// 描画種別
abstract class MdcDrawingTypes {
  static const int drawtype_invalid = 0;

  /// 線：鉛筆
  static const int drawtype_pencil = 1;

  /// 線：塗りつぶし(色・縫い種の流し込み)
  static const int drawtype_line_fill = 2;

  /// 線：色取得(色・縫い種の吸い上げ)
  static const int drawtype_line_color = 3;

  /// 面：ブラシ
  static const int drawtype_blush = 4;

  /// 面：塗りつぶし(色・縫い種の流し込み)
  static const int drawtype_surface_fill = 5;

  /// 面：色取得(色・縫い種の吸い上げ)
  static const int drawtype_surface_color = 6;

  /// 消しゴム
  static const int drawtype_eraser = 7;

  /// スタンプ
  static const int drawtype_fixed_shape = 8;

  /// PM9読み出しイメージ
  static const int drawtype_externalImage = 9;

  /// 下絵スキャンイメージ
  static const int drawtype_scanImage = 10;

  /// 範囲選択
  static const int drawtype_area_select = 11;

  /// 履歴
//  static const int drawtype_history = 12;

  /// 履歴 未確定領域あり
//  static const int drawtype_history_part = 13;

//  static const int drawtype_end = 14;
  static const int drawtype_end = 12;
}

/// 履歴種別
abstract class MdcHistoryTypes {
  /// 履歴ではない
  static const int historyType_invalid = 0;

  /// 履歴 お絵描き画面 未確定領域なし
  static const int historyType_draw_single = 1;

  /// 履歴 お絵描き画面 未確定領域あり
  static const int historyType_draw_part = 2;

  /// 履歴 模様編集 region単体選択
  static const int historyType_region_single = 3;

  /// 履歴 模様編集 region一括選択
  static const int historyType_region_multi = 4;

  static const int historyType_end = 5;
}

/// 線プロパティー
/// ツール選択
abstract class MdcLineToolTypes {
  static const int line_invalid = 0;

  /// 鉛筆ツール：開いた線(Open)
  static const int line_free_open = 1;

  /// 鉛筆ツール：閉じた線(Closed)
  static const int line_free_close = 2;

  /// 直線ツール
  static const int line_open = 3;

  /// 連続直線ツール
  static const int line_close = 4;

  static const int line_end = 5;
}

/// 縫い種選択
abstract class MdcSewKinds_line {
  static const int sewkind_line_invalid = 0;

  /// サテン縫い(ジグザグ) Zigzag
  static const int sewkind_line_zigzag = 1;

  /// １重縫い(ランニングステッチ) Running
  static const int sewkind_line_running = 2;

  /// ３重縫い(トリプルステッチ) Triple
  static const int sewkind_line_triple = 3;

  /// キャンドルウィック Candlewicking
  static const int sewkind_line_candlwicking = 4;

  /// チェーンステッチ Chain
  static const int sewkind_line_chain = 5;

  /// モチーフ
  static const int sewkind_line_motif = 6;

  /// Eステッチ
  static const int sewkind_line_e_stitch = 7;

  /// Vステッチ
  static const int sewkind_line_v_stitch = 8;

//↓PHFIRMIIVO-641 線プロパティ拡張(粗いジグザグ縫い対応) H.Kawasaki
  /// 粗いジグザグ RoughZigzag
  static const int sewkind_line_rouhg_zigzag = 9;
//↑PHFIRMIIVO-641 線プロパティ拡張(粗いジグザグ縫い対応) H.Kawasaki

  /// 縫わない
  static const int sewkind_line_noSew = 10;

  static const int sewkind_line_end = 11;
}

/// 面プロパティー
/// ブラシツール形状
abstract class MdcBrushToolTypes {
  static const int brush_invalid = 0;

  /// 丸
  static const int brush_circle = 1;

  /// 四角
  static const int brush_square = 2;
  static const int brush_end = 3;
}

/// 縫い種選択
abstract class MdcSewKinds_surface {
  static const int sewkind_surface_invalid = 0;

  /// タタミ
  static const int sewkind_surface_tatami = 1;

  /// スティップリング
  static const int sewkind_surface_stippling = 2;

  /// Decorative Fill
  static const int sewkind_surface_decorativefill = 3;

  /// Echo Fill
  static const int sewkind_surface_echofill = 4;

  /// 縫わない
  static const int sewkind_surface_noSew = 5;

  static const int sewkind_surface_end = 6;
}

/// 消しゴムプロパティー
/// 消しゴムツール形状
abstract class MdcEraserToolTypes {
  static const int eraser_invalid = 0;

  /// 丸
  static const int eraser_circle = 1;

  /// 四角
  static const int eraser_square = 2;
  static const int eraser_end = 3;
}

abstract class MdcStampTypes {
  static const int stamp_invalid = 0;

  /// 定型図形カテゴリ(線)
  static const int stamp_outline = 1;

  /// 定型図形カテゴリ(面)
  static const int stamp_surface = 2;

  /// 定型図形カテゴリ(線と面)
  static const int stamp_line_surface = 3;

  /// 閉じた線
  static const int stamp_closed_shape = 4;

  /// 開いた線
  static const int stamp_open_shape = 5;

  /// 刺繍模様アウトライン
  static const int stamp_saved_outline = 6;

  /// 枠スタンプ(枠サイズ矩形)
  static const int stamp_frame = 7;

  /// カッティングアウトライン
  static const int stamp_cutting_outline = 8;

  static const int stamp_end = 9;
}

// 枠スタンプタイプ
enum MdcFrameStampTypes
{
	f_stamp_297_465,
	f_stamp_272_408,
	f_stamp_272_272,
	f_stamp_254_254,
	f_stamp_240_360,
	f_stamp_240_240,
	f_stamp_200_300,
	f_stamp_200_200,
	f_stamp_180_360,
	f_stamp_180_300,
	f_stamp_160_260,
	f_stamp_150_150,
	f_stamp_130_180,
	f_stamp_100_300,
	f_stamp_100_180,
	f_stamp_100_100,
	f_stamp_end
}

// 定義が古いためMdcAreaSelectTypesに修正
//abstract class MdcClippingTypes {
//  static const int clip_invalid = 0;
//
//  /// 矩形選択
//  static const int clip_rectangle = 1;
//
//  /// 自由曲線(最後は閉じる)で選択
//  static const int clip_free_close = 2;
//
//  /// 直線(連続)で選択
//  static const int clip_line_multi = 3;
//
//  /// 自動選択(タッチしたところの領域を吸い出して選択)
//  static const int clip_dropper_select = 4;
//
//  /// 全選択
//  static const int clip_all_select = 5;
//  static const int clip_end = 6;
//}

// 範囲選択種別の定義
abstract class MdcAreaSelectTypes {
  static const int select_invalid = 0;

  /// 矩形選択
  static const int select_rectangle = 1;

  /// 直線(連続)で選択
  static const int select_line_multi = 2;

  /// 自由曲線(最後は閉じる)で選択
  static const int select_free_close = 3;

  /// 自動選択(タッチしたところの領域を吸い出して選択)
  static const int select_dropper_select = 4;

  /// 全選択
  static const int select_all_select = 5;

  static const int select_end = 6;
}

/// 座標入力時のペン状態
abstract class MdcPenTouchStatus {
  static const int pen_none = 0;
  static const int pen_press = 1; // 押下：始点座標でタッチ開始
  static const int pen_move = 2; // 長押：中継点座標をタッチ中
  static const int pen_release = 3; // 解放：終点座標でタッチ終了
}

// 部分模様(未確定領域)に対する編集操作
abstract class MdcEditFormInstruct {
  static const int formInst_none = 0;
  static const int formInst_resize = 1; // サイズ変更
  static const int formInst_rotate = 2; // 回転
  static const int formInst_reverse = 3; // 反転
  static const int formInst_duplicate = 4; // 複製
  static const int formInst_cut = 5; // 切り取り
  static const int formInst_paste = 6; // 貼り付け
  static const int formInst_move = 7; // 移動
  static const int formInst_size_reset = 8; // サイズリセット(編集開始時のサイズにする)
  static const int formInst_move_reset = 9; // 位置リセット(編集開始時の位置にする)
  static const int formInst_rotate_reset = 10; // 回転角度リセット(編集開始時の0°にする)
  static const int formInst_density_change = 11; // 濃度変更
}

//abstract class MdcIsOnOff {
//  static const int mdcIs_on = 0;
//  static const int mdcIs_off = 1;
//}
enum MdcIsOnOff {
	mdcIs_off,
	mdcIs_on,
	mdcIs_invalid			// 無効 ※同一縫い種指定時に不一致の場合に使用
}

// スティップリング：縫い重ね回数
//abstract class MdcStichLine {
//  static const int stichline_single = 0;
//  static const int stichline_triple = 1;
//}
enum MdcStichLine {
	mdc_stichline_single,	// 1回
	mdc_stichline_triple,	// 3回
	mdc_stichline_invalid	// 無効 ※同一縫い種指定時に不一致の場合に使用
}

//abstract class MdcFlipside {
//  static const int flip_inside = 0;
//  static const int flip_outside = 1;
//}
// 向き方向
enum MdcFlipside {
	flip_inside,		// 内向き ※デフォルト
	flip_outside,		// 外向き
	flip_invalid		// 無効 ※同一縫い種指定時に不一致の場合に使用
}

abstract class scanDispBrightness {
  static const int no_disp = 0;
  static const int light = 1;
  static const int dark = 2;
}

// isMdcUsingImportDataNumber()引数：スタンプ種別
abstract class MdcBuiltInStampType {
  static const int mdcBuiltInStampType_NormalOutline = 0; // 定型図形スタンプ(線)
  static const int mdcBuiltInStampType_NormalSurface = 1; // 定型図形スタンプ(面)
  static const int mdcBuiltInStampType_NormalLineSurface = 2; // 定型図形スタンプ(面)
  static const int mdcBuiltInStampType_CloseLine = 3; // 定型図形スタンプ(面)
  static const int mdcBuiltInStampType_OpenLine = 4; // 定型図形スタンプ(面)
  static const int mdcBuiltInStampType_Num = 5;
}

// getMdcPartPatternOriginSize()引数：サイズ種別 ※取得サイズの単位(mm、pixel)
abstract class MdcPartSizeKind {
  static const int mdcPartSizeKind_Mm = 0; // 選択サイズ種別：ミリ
  static const int mdcPartSizeKind_Px = 1; // 選択サイズ種別：ピクセル
}

// スキャン遷移モード
abstract class MdcScanModeSelect {
  static const int MdcScanModeSelect_base = 0; // 下絵スキャン
  static const int MdcScanModeSelect_line = 1; // ラインスキャン
  static const int MdcScanModeSelect_line_preset = 2; // ラインスキャン (下絵取り込み済み)
  static const int MdcScanModeSelect_illust = 3; // イラストスキャン
  static const int MdcScanModeSelect_illust_preset = 4; // イラストスキャン (下絵取り込み済み)
  static const int MdcScanModeSelect_over = 5; // イラストスキャンモードではない
}

// convertToOutlineImage()引数
abstract class MdcScanOutlineDetectLevel {
  static const int scanOutlineLevel_none = 0; // 未設定
  static const int scanOutlineLevel_thinner = 1; // 薄い線を取得
  static const int scanOutlineLevel_thin = 2; // 薄めの線を取得
  static const int scanOutlineLevel_normal = 3; // 平均
  static const int scanOutlineLevel_dark = 4; // 濃いめの線を取得
  static const int scanOutlineLevel_darker = 5; // 濃い線を取得
}

/// getMdcStipplingDecofillMaxDrawingImage()引数：全体イメージ取得対象
abstract class MdcMaxDrawingImageTarget {
  /// スティップリング
  static const int mdcMaxDrawingImageTarget_Stippling = 0;

  /// 内蔵デコラティブフィル
  static const int mdcMaxDrawingImageTarget_BuiltInDecofill = 1;

  /// ユーザー作成デコラティブフィル
  static const int mdcMaxDrawingImageTarget_CustomDecofill = 2;

  /// 縫わない線
  static const int mdcMaxDrawingImageTarget_NoSewLine = 3;
}

/// 履歴データ追加パラメータ 取得構造体
final class MdcHistSubParam_t extends ffi.Struct {
  /// 領域選択種別
  @ffi.Int32()
  external int areaSelectType;
  /// 描画種別
  @ffi.Int32()
  external int drawingType;
  /// ツール種別 ペン先:線/面/消しゴム
  @ffi.Array.multi([3])
  external ffi.Array<ffi.Int32> toolType;
  /// 履歴種別
  @ffi.Int32()
  external int historyType;
  /// 選択中 リージョン番号
  @ffi.Int32()
  external int region_id;
  /// 登録された点の数 ※領域選択種別が連続多角形選択の時、線描画で連続直線の時に使用する
  external ffi.Pointer<ffi.Int32> pointNum;
  /// 上記 点のリスト   ※領域選択種別が連続多角形選択の時(全点)、線描画で連続直線の時(始点、終点のみ)に使用する
  external ffi.Pointer<SSPoint_t> pointList;

  static ffi.Pointer<MdcHistSubParam_t> allocate() {
    final pointer = ffi.calloc<MdcHistSubParam_t>();
    pointer.ref.areaSelectType = 0;
    pointer.ref.drawingType = 0;
    for (int i = 0; i < 3; i++)
    {
      pointer.ref.toolType[i] = 0;
    }
    pointer.ref.historyType = 0;
    pointer.ref.region_id = 0;
    pointer.ref.pointNum = ffi.calloc<ffi.Int32>();
    pointer.ref.pointList = ffi.calloc<SSPoint_t>();
    return pointer;
  }
}

/// 2.2-1. 線プロパティー設定
/// 2.2-1.1 引数
final class MdcEditLineProperty_t extends ffi.Struct {
  /// ツールタイプ
  @ffi.Int32()
  external int lineType;

  /// 縫い種
  @ffi.Int32()
  external int lineKind;

  /// 線モチーフタイプ(false:内蔵模様、true:ユーザー作成模様)
  @ffi.Bool()
  external bool motifType;

  /// 線モチーフ番号(1～)
  @ffi.Uint16()
  external int motifNo;

  /// 線モチーフサイズ ※用途不明
//  @ffi.Int16()
//  external int motifSize;

  /// 色
  @ffi.Int32()
  external int color;

  static ffi.Pointer<MdcEditLineProperty_t> allocate() {
    final pointer = ffi.calloc<MdcEditLineProperty_t>();
    pointer.ref.lineType = 0;
    pointer.ref.lineKind = 0;
    pointer.ref.motifType = false;
    pointer.ref.motifNo = 0;
//    pointer.ref.motifSize = 0;
    pointer.ref.color = 0;
    return pointer;
  }
}

/// 2.2.2. 面プロパティー設定
/// 2.2.2.1. 引数
final class MdcEditSurfaceProperty_t extends ffi.Struct {
  /// ブラシ形状
  @ffi.Int32()
  external int brushType;

  /// ブラシサイズ
  @ffi.Int16()
  external int brushSize;

  /// 縫い種
  @ffi.Int32()
  external int kind;

  /// デコラティブフィルタイプ(false:内蔵模様、true:ユーザー作成模様)
  @ffi.Bool()
  external bool decorativeType;

  /// デコラティブフィル番号(1～)
  @ffi.Uint16()
  external int decorativeNo;

  /// 色
  @ffi.Int32()
  external int color;

  static ffi.Pointer<MdcEditSurfaceProperty_t> allocate() {
    final pointer = ffi.calloc<MdcEditSurfaceProperty_t>();
    pointer.ref.brushType = 0;
    pointer.ref.brushSize = 0;
    pointer.ref.kind = 0;
    pointer.ref.decorativeType = false;
    pointer.ref.decorativeNo = 0;
    pointer.ref.color = 0;
    return pointer;
  }
}

/// 2.2.3. 消しゴムプロパティー設定
/// 2.2.3.1. 引数
final class MdcEditEraserProperty_t extends ffi.Struct {
  @ffi.Int32()
  external int type;

  @ffi.Int16()
  external int size;

  static ffi.Pointer<MdcEditEraserProperty_t> allocate() {
    final pointer = ffi.calloc<MdcEditEraserProperty_t>();
    pointer.ref.type = 0;
    pointer.ref.size = 0;
    return pointer;
  }
}

final class MdcStampProperty_t extends ffi.Struct {
  @ffi.Int32()
  external int type;

  @ffi.Int32()
  external int index;

  @ffi.Int32()
  external int frame_type;

  @ffi.Double()
  external double frame_offset;

  ///PCEファイルパス
  external ffi.Pointer<ffi.Char> pce_filename;

  static ffi.Pointer<MdcStampProperty_t> allocate() {
    final pointer = ffi.calloc<MdcStampProperty_t>();
    pointer.ref.type = 0;
    pointer.ref.index = 0;
    pointer.ref.frame_type = 0;
    pointer.ref.frame_offset = -2.0;
    pointer.ref.pce_filename = ffi.calloc<ffi.Char>();
    return pointer;
  }
}

typedef MdcStampProperty = MdcStampProperty_t;

/// 2.x.x. インポートしたユーザー作成模様ファイルのファイル名リスト、件数を保存する関数
/// x.x.x.x. 引数
final class MdcImportFileList_t extends ffi.Struct {
  @ffi.Uint32()
  external int deco_import_num;

  @ffi.Array.multi([12])
  external ffi.Array<ffi.Pointer<ffi.Char>> plf_filelist;

  @ffi.Uint32()
  external int motif_import_num;

  @ffi.Array.multi([12])
  external ffi.Array<ffi.Pointer<ffi.Char>> pmf_filelist;

  static ffi.Pointer<MdcImportFileList_t> allocate() {
    final pointer = ffi.calloc<MdcImportFileList_t>();
    pointer.ref.deco_import_num = 0;
    pointer.ref.motif_import_num = 0;
    for (int i = 0; i < 12; i++) {
      pointer.ref.plf_filelist[i] = ffi.calloc<ffi.Char>();
      pointer.ref.pmf_filelist[i] = ffi.calloc<ffi.Char>();
    }
    return pointer;
  }
}

/// x.x.x. お絵描きで使用中のユーザー作成模様番号を確認する関数
/// x.x.x.x. 引数
final class MdcImportDataUsingNumber_t extends ffi.Struct {
  @ffi.Array.multi([12])
  external ffi.Array<ffi.Uint8> deco_using_data;

  @ffi.Uint8()
  external int deco_set_num;

  @ffi.Array.multi([12])
  external ffi.Array<ffi.Uint8> motif_using_data;

  @ffi.Uint8()
  external int motif_set_num;

  static ffi.Pointer<MdcImportDataUsingNumber_t> allocate() {
    final pointer = ffi.calloc<MdcImportDataUsingNumber_t>();
    pointer.ref.deco_set_num = 0;
    pointer.ref.motif_set_num = 0;

    for (int i = 0; i < 12; i++) {
      pointer.ref.deco_using_data[i] = 0;
      pointer.ref.motif_using_data[i] = 0;
    }
    return pointer;
  }
}

/// 3.3. 描画指示
/// 3.3.1. リクエストプロセス情報
abstract class progStat {
  static const int progStat_none = 0;

  /// 単発リクエスト
  static const int progStat_single = 1;

  /// 連続リクエスト：開始
  static const int progStat_beginning = 2;

  /// 連続リクエスト：継続 (移動、編集)
  static const int progStat_during = 3;

  /// 連続リクエスト：最後 (確定)
  static const int progStat_closing = 4;
}

final class MdcReqProc_t extends ffi.Struct {
  /// リクエストの処理番号
  @ffi.Uint32()
  external int proc_id;

  /// リクエスト指示の進捗状態
  @ffi.Int32()
  external int status;

  static ffi.Pointer<MdcReqProc_t> allocate() {
    final pointer = ffi.calloc<MdcReqProc_t>();
    pointer.ref.proc_id = 1;
    pointer.ref.status = 1;
    return pointer;
  }
}

/// 3.3.2. 描画指示関数
/// 3.3.2.1. 引数
final class MdcPenTouchInfo_t extends ffi.Struct {
  @ffi.Int32()
  external int posX;

  @ffi.Int32()
  external int posY;

  @ffi.Int32()
  external int pstat;

  //external ffi.Pointer<MdcImageInfo_t> imgInfo;

  static ffi.Pointer<MdcPenTouchInfo_t> allocate() {
    final pointer = ffi.calloc<MdcPenTouchInfo_t>();
    pointer.ref.posX = 0;
    pointer.ref.posY = 0;
    pointer.ref.pstat = 0;
    //pointer.ref.imgInfo = MdcImageInfo_t.allocate();
    return pointer;
  }
}

/// 3.3.3. スタンプ編集指示関数
/// 3.3.3.1. 引数
final class MdcUnsettledObjectInfo_t extends ffi.Struct {
  @ffi.Int32()
  external int inst;

  @ffi.Int32()
  external int posX;

  @ffi.Int32()
  external int posY;

  @ffi.Int32()
  external int width;

  @ffi.Int32()
  external int height;

  @ffi.Int32()
  external int center_posX;

  @ffi.Int32()
  external int center_posY;

  @ffi.Double()
  external double rotate;

  ///クリップボードファイルパス
  external ffi.Pointer<ffi.Char> clip_filename;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Int32> rectangle_pntX;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Int32> rectangle_pntY;

  static ffi.Pointer<MdcUnsettledObjectInfo_t> allocate() {
    final pointer = ffi.calloc<MdcUnsettledObjectInfo_t>();
    pointer.ref.inst = 0;
    pointer.ref.posX = 0;
    pointer.ref.posY = 0;
    pointer.ref.width = 0;
    pointer.ref.height = 0;
    pointer.ref.center_posX = 0;
    pointer.ref.center_posY = 0;
    pointer.ref.rotate = 0.0;
    pointer.ref.clip_filename = ffi.calloc<ffi.Char>();
    for (int i = 0; i < 4; i++) {
      pointer.ref.rectangle_pntX[i] = 0;
      pointer.ref.rectangle_pntY[i] = 0;
    }
    return pointer;
  }
}

/// ジグザグ(サテン)縫い縫製パラメータ構造体
@ffi.Packed(1)
final class MdcSewZigzagParam_t extends ffi.Struct {
  /// 幅
  @ffi.Uint16()
  external int width;

  /// 糸密度
  @ffi.Int32()
  external int density;

  static ffi.Pointer<MdcSewZigzagParam> allocate() {
    final pointer = ffi.calloc<MdcSewZigzagParam>();
    pointer.ref.width = 0;
    pointer.ref.density = 0;
    return pointer;
  }
}
typedef MdcSewZigzagParam = MdcSewZigzagParam_t;

/// ランニングステッチ縫製パラメータ構造体
@ffi.Packed(1)
final class MdcSewRunningParam_t extends ffi.Struct {
  /// ピッチ
  @ffi.Uint16()
  external int pitch;

  /// 下縫い有無
  @ffi.Int32()
  external int UnderSewing;

  static ffi.Pointer<MdcSewRunningParam_t> allocate() {
    final pointer = ffi.calloc<MdcSewRunningParam_t>();
    pointer.ref.pitch = 0;
    pointer.ref.UnderSewing = 0;
    return pointer;
  }
}
typedef MdcSewRunningParam = MdcSewRunningParam_t;

/// 5.1-3. トリプルステッチ縫製パラメータ構造体
@ffi.Packed(1)
final class MdcSewTripleParam_t extends ffi.Struct {
  /// ピッチ
  @ffi.Uint16()
  external int pitch;

  /// 下縫い有無
  @ffi.Int32()
  external int UnderSewing;

  static ffi.Pointer<MdcSewTripleParam> allocate() {
    final pointer = ffi.calloc<MdcSewTripleParam>();
    pointer.ref.pitch = 0;
    pointer.ref.UnderSewing = 0;
    return pointer;
  }
}
typedef MdcSewTripleParam = MdcSewTripleParam_t;

/// キャンドルウィック縫製パラメータ構造体
@ffi.Packed(1)
final class MdcSewCandleParam_t extends ffi.Struct {
  /// サイズ
  @ffi.Uint16()
  external int size;

  /// 間隔
  @ffi.Uint16()
  external int spacing;

  static ffi.Pointer<MdcSewCandleParam_t> allocate() {
    final pointer = ffi.calloc<MdcSewCandleParam_t>();
    pointer.ref.size = 0;
    pointer.ref.spacing = 0;
    return pointer;
  }
}
typedef MdcSewCandleParam = MdcSewCandleParam_t;

/// チェーンステッチ縫製パラメータ構造体
@ffi.Packed(1)
final class MdcSewChainParam_t extends ffi.Struct {
  /// サイズ
  @ffi.Uint16()
  external int size;

  /// 縫い重ね回数
  @ffi.Uint16()
  external int thickness;

  static ffi.Pointer<MdcSewChainParam_t> allocate() {
    final pointer = ffi.calloc<MdcSewChainParam_t>();
    pointer.ref.size = 0;
    pointer.ref.thickness = 0;
    return pointer;
  }
}
typedef MdcSewChainParam = MdcSewChainParam_t;

/// Eステッチ縫製パラメータ構造体
@ffi.Packed(1)
final class MdcSewEParam_t extends ffi.Struct {
  /// ステッチ幅
  @ffi.Uint16()
  external int width;

  /// 縫い間隔
  @ffi.Uint16()
  external int spacing;

  /// 厚さ（縫い重ね）
  @ffi.Uint16()
  external int thickness;

  /// 向き方向
  @ffi.Int32()
  external int side;

  static ffi.Pointer<MdcSewEParam_t> allocate() {
    final pointer = ffi.calloc<MdcSewEParam_t>();
    pointer.ref.width = 0;
    pointer.ref.spacing = 0;
    pointer.ref.thickness = 0;
    pointer.ref.side = 0;
    return pointer;
  }
}
typedef MdcSewEParam = MdcSewEParam_t;

/// Vステッチ縫製パラメータ構造体
@ffi.Packed(1)
final class MdcSewVParam_t extends ffi.Struct {
  /// ステッチ幅
  @ffi.Uint16()
  external int width;

  /// 縫い間隔
  @ffi.Uint16()
  external int spacing;

  /// 厚さ（縫い重ね）
  @ffi.Uint16()
  external int thickness;

  /// 向き方向
  @ffi.Int32()
  external int side;

  static ffi.Pointer<MdcSewVParam_t> allocate() {
    final pointer = ffi.calloc<MdcSewVParam_t>();
    pointer.ref.width = 0;
    pointer.ref.spacing = 0;
    pointer.ref.thickness = 0;
    pointer.ref.side = 0;
    return pointer;
  }
}
typedef MdcSewVParam = MdcSewVParam_t;

/// モチーフ縫製パラメータ構造体
@ffi.Packed(1)
final class MdcSewMotifParam_t extends ffi.Struct {
  @ffi.Uint16()
  external int size;

  /// モチーフ間隔
  @ffi.Int16()
  external int spacing;

  /// 向き方向
  @ffi.Int32()
  external int side;

  static ffi.Pointer<MdcSewMotifParam_t> allocate() {
    final pointer = ffi.calloc<MdcSewMotifParam_t>();
    pointer.ref.size = 0;
    pointer.ref.spacing = 0;
    pointer.ref.side = 0;
    return pointer;
  }
}
typedef MdcSewMotifParam = MdcSewMotifParam_t;


/* 粗いジグザグ縫い(糸密度) */
enum MDC_Rough_Zigzag_Density_t {
	mdc_rough_zigzag_density_invalid,				/* 無効 ※同一縫い種指定時に不一致の場合に使用 */
	mdc_rough_zigzag_density_025,					/* 0.25 [本/mm] */
	mdc_rough_zigzag_density_050,					/* 0.50 [本/mm] */
	mdc_rough_zigzag_density_075,					/* 0.75 [本/mm] */
	mdc_rough_zigzag_density_100,					/* 1.00 [本/mm] */
	mdc_rough_zigzag_density_125,					/* 1.25 [本/mm] */
	mdc_rough_zigzag_density_150,					/* 1.50 [本/mm] */
	mdc_rough_zigzag_density_175,					/* 1.75 [本/mm] */
	mdc_rough_zigzag_density_200					/* 2.00 [本/mm] */
}

/// 粗いジグザグ縫い縫製パラメータ構造体
@ffi.Packed(1)
final class MdcSewRoughZigzagParam_t extends ffi.Struct {
  /// ジグザグ縫い幅
  @ffi.Uint16()
  external int width;

  /// ジグザグ縫い密度(糸密度)
  @ffi.Int32()
  external int density;

  static ffi.Pointer<MdcSewRoughZigzagParam> allocate() {
    final pointer = ffi.calloc<MdcSewRoughZigzagParam>();
    pointer.ref.width = 0;
    pointer.ref.density = 0;
    return pointer;
  }
}
typedef MdcSewRoughZigzagParam = MdcSewRoughZigzagParam_t;

/* タタミ縫い(縫い方向) */
enum MDC_Tatami_Dir_Kind_t {
	mdc_tatami_dir_kind_invalid,					/* 無効 ※同一縫い種指定時に不一致の場合に使用 */
	mdc_tatami_dir_kind_auto,						/* Auto   */
	mdc_tatami_dir_kind_manual,						/* Manual */
}

/* サテン縫い・タタミ縫い(糸密度) */
enum MDC_Zigzag_Tatami_Density_t {
	mdc_zigzag_tatami_density_invalid,				/* 無効 ※同一縫い種指定時に不一致の場合に使用 */
	mdc_zigzag_tatami_density_090,					/* 90%  [4 本/(mm)]		*/
	mdc_zigzag_tatami_density_100,					/* 100% [4.5 本/(mm)]	*/
	mdc_zigzag_tatami_density_110					/* 110% [5 本/(mm)]		*/
}

/// タタミ縫い縫製パラメータ構造体
@ffi.Packed(1)
final class MdcSewTatamiParam_t extends ffi.Struct {
  /// タタミ縫い方向種別(Auto/Manual/invalid)
  @ffi.Int32()
  external int dirKind;

  /// タタミ縫い方向Manual指定値 ※dirKindがManualの場合に有効
  @ffi.Uint16()
  external int direction;

  /// 糸密度
  @ffi.Int32()
  external int density;

  /// 縫い縮み設定(ステッチの長さ補正)
  @ffi.Uint16()
  external int PullCompensation;

  /// 下打ち有無(下縫い有り無し指定)  ON:下打ちあり、OFF下打ちなし
  @ffi.Int32()
  external int UnderSewing;

  static ffi.Pointer<MdcSewTatamiParam_t> allocate() {
    final pointer = ffi.calloc<MdcSewTatamiParam_t>();
    pointer.ref.dirKind = 0;
    pointer.ref.direction = 0;
    pointer.ref.density = 0;
    pointer.ref.PullCompensation = 0;
    pointer.ref.UnderSewing = 0;
    return pointer;
  }
}
typedef MdcSewTatamiParam = MdcSewTatamiParam_t;



/// スティップリング縫製パラメータ構造体
@ffi.Packed(1)
final class MdcSewStippleParam_t extends ffi.Struct {
  /// 走りピッチ幅
  @ffi.Uint16()
  external int runPitch;

  /// 模様の配置間隔
  @ffi.Uint16()
  external int spacing;

  /// アウトラインからのオフセット距離
  @ffi.Uint16()
  external int distance;

  /// 縫い重ね回数
  @ffi.Int32()
  external int stitch;

  static ffi.Pointer<MdcSewStippleParam_t> allocate() {
    final pointer = ffi.calloc<MdcSewStippleParam_t>();
    pointer.ref.runPitch = 0;
    pointer.ref.spacing = 0;
    pointer.ref.distance = 0;
    pointer.ref.stitch = 0;
    return pointer;
  }
}
typedef MdcSewStippleParam = MdcSewStippleParam_t;


/* デコラティブフィル(ゆらぎタイプ) */
enum MDC_DecoFill_RandType_t {
	mdc_decofill_randtype_A,					// タイプA
	mdc_decofill_randtype_B,						// タイプB
	mdc_decofill_randtype_C,						// タイプC
	mdc_decofill_randtype_invalid					// 無効 ※同一縫い種指定時に不一致の場合に使用
}

/* デコラティブフィル(縫い回数) */
enum MDC_DecoFill_Thickness_t {
	mdc_decofill_thickness_2or3,				/* 2～3重(デフォルト) */
	mdc_decofill_thickness_1or2,					/* 1～2重 */
	mdc_decofill_thickness_invalid					/* 無効 ※同一縫い種指定時に不一致の場合に使用 */
}

/// デコラティブフィル縫製パラメータ構造体
@ffi.Packed(1)
final class MdcSewDecoParam_t extends ffi.Struct {
  /// 模様サイズ
  @ffi.Uint16()
  external int size;

  /// 縫い方向
  @ffi.Uint16()
  external int direction;

  /// 輪郭縫い
  @ffi.Int32()
  external int outline;

  /// ゆらぎ程度
  @ffi.Uint16()
  external int randomShift;

  /// ゆらぎタイプ
  @ffi.Int32()
  external int randomShiftType;

  /// 基点位置オフセット：縦方向
  @ffi.Int16()
  external int positionOffset_Y;

  /// 基点位置オフセット：横方向
  @ffi.Int16()
  external int positionOffset_X;

  /// 縫い回数
  @ffi.Int32()
  external int thickness;

  static ffi.Pointer<MdcSewDecoParam_t> allocate() {
    final pointer = ffi.calloc<MdcSewDecoParam_t>();
    pointer.ref.size = 0;
    pointer.ref.direction = 0;
    pointer.ref.outline = 0;
    pointer.ref.randomShift = 0;
    pointer.ref.randomShiftType = 0;
    pointer.ref.positionOffset_Y = 0;
    pointer.ref.positionOffset_X = 0;
    pointer.ref.thickness = 0;
    return pointer;
  }
}
typedef MdcSewDecoParam = MdcSewDecoParam_t;


@ffi.Packed(1)
final class MdcSewEchoParam_t extends ffi.Struct {
  @ffi.Int16()
  external int pitch;

  @ffi.Int16()
  external int offset;

  @ffi.Int16()
  external int space;
}

/// 線の縫い種の全縫製パラメータ構造体定義
@ffi.Packed(1)
final class MdcSewLineAllParam_t extends ffi.Struct {
  /// ジグザグ縫い縫製パラメータ
  external MdcSewZigzagParam zigzag;

  /// ランニングステッチ縫製パラメータ
  external MdcSewRunningParam running;

  /// トリプルステッチ縫製パラメータ
  external MdcSewTripleParam triple;

  /// キャンドルウィック縫製パラメータ
  external MdcSewCandleParam candle;

  /// チェーンステッチ縫製パラメータ
  external MdcSewChainParam chain;

  /// Eステッチ縫製パラメータ
  external MdcSewEParam estitch;

  /// Vステッチ縫製パラメータ
  external MdcSewVParam vstitch;

  /// 線モチーフ縫製パラメータ
  external MdcSewMotifParam motif;

  /// 粗いジグザグ縫製パラメータ
  external MdcSewRoughZigzagParam roughZigzag;

  static ffi.Pointer<MdcSewLineAllParam_t> allocate() {
    final pointer = ffi.calloc<MdcSewLineAllParam_t>();
    return pointer;
  }
}

/// 面の縫い種の全縫製パラメータ構造体定義
@ffi.Packed(1)
final class MdcSewSurfaceAllParam_t extends ffi.Struct {
  /// タタミ縫い縫製パラメータ
  external MdcSewTatamiParam tatami;

  /// スティップリング縫製パラメータ
  external MdcSewStippleParam stipple;

  /// デコラティブフィル縫製パラメータ
  external MdcSewDecoParam decofill;

  static ffi.Pointer<MdcSewSurfaceAllParam_t> allocate() {
    final pointer = ffi.calloc<MdcSewSurfaceAllParam_t>();
    return pointer;
  }
}


const int MDC_STITCHTYPE_SIZE = 2;

const int MDC_STITCHTYPE_DETAIL_SIZE = 14;

const int REGIONINFOMEMBER_ARRAY = 1;
