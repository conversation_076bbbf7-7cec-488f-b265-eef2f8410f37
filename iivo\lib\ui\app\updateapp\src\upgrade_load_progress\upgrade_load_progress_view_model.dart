import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart';
import 'package:update_grade/update_grade.dart';
import 'package:usb_manager/usb_manager.dart';
import 'package:xd_component/l10n/app_localizations.dart';

import '../../../../../model/upf_verify_signature_model.dart';
import '../../../../../network/upgrade/upgrade.dart';
import '../../iivo_export.dart';
import '../../model/upgrade_model.dart';
import '../../page/page_route.dart';
import '../../update_app_log.dart';
import 'upgrade_load_progress_view_interface.dart';

/// view _modelに必要な構造
final upgradeUpdateProgressViewModeProvider = StateNotifierProvider.autoDispose
    .family<UpgradeLoadProgressViewInterface, UpgradeLoadProgressState,
            BuildContext>(
        (ref, context) => UpgradeLoadProgressViewMode(ref, context));

class UpgradeLoadProgressViewMode extends UpgradeLoadProgressViewInterface {
  UpgradeLoadProgressViewMode(
    ref,
    context,
  ) : super(const UpgradeLoadProgressState(), ref, context) {
    state = state.copyWith(
      upgVersion: UpgradeModel().upgVersion,
      progressValue: _progress5,
    );

    /// 更新を開始するための識別ファイルを生成する
    try {
      UpdateAppLog.d("create IIVO_UPDATE");
      FileEntity updateFile =
          FileEntity(join(memorySector.brother_dir.absolutePath, _iivoUpdate));

      /// もしIIVO_UPDATEが存在しなければ、初めてのアップデートであることを示す。
      if (updateFile.existsSync() == false) {
        updateFile.createSync(recursive: true);

        /// 最初のWLAN更新では、更新する必要があるWLANのUPFの総数を保持します。
        if (isDownLoadUpgrade) {
          int totalUpfCount = UpgradeModel().downloadedUpfCollection.length;
          FileEntity saveCountFile = FileEntity(UpgradeModel().txtSavePath);
          saveCountFile.createSync();
          saveCountFile.writeAsStringSync(totalUpfCount.toString());
          state = state.copyWith(
            updatedUpfCount: 0,
            totalUpfToUpdateCount: totalUpfCount,
          );
        }
      } else {
        /// WLAN更新を継続すると、WLAN更新の総数が読み取られます。
        if (isDownLoadUpgrade) {
          FileEntity saveCountFile = FileEntity(UpgradeModel().txtSavePath);
          int totalUpfCount = int.parse(saveCountFile.readAsStringSync());
          state = state.copyWith(
            /// 更新済み数=総数-フォルダ内のUPFの数です
            updatedUpfCount:
                totalUpfCount - UpgradeModel().downloadedUpfCollection.length,
            totalUpfToUpdateCount: totalUpfCount,
          );
        }
      }
    } catch (e) {
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode9);
      UpgradeModel().endUsbTransactionIfNeeded();
      return;
    }

    /// brother_panel をアップグレード モードに切り替える
    UpdateGrade().stopBrotherPanel().then((value) async {
      if (isDownLoadUpgrade) {
        List<FolderType> downloadedUpfCollection =
            UpgradeModel().downloadedUpfCollection;

        for (int i = 0; i < downloadedUpfCollection.length; i++) {
          if (UpgradeModel().endErrorTitle.isNotEmpty) {
            continue;
          }

          state = state.copyWith(
              updatedUpfCount: state.updatedUpfCount + 1,
              progressValue: _progress5);

          _deleteUnpackedFolderAfterUpdate();

          try {
            var match = RegExp(r'T(\d+)')
                .firstMatch(downloadedUpfCollection[i].fileName);
            int type = match != null ? int.parse(match.group(1)!) : -1;

            FileEntity upgradeFile =
                FileEntity(downloadedUpfCollection[i].path);

            /// WLANDownLoadでダウンロードしたOTAアップグレードパッケージはzipファイルであり、直接アップグレードを行うことができる。
            if (type == UpgradeType.ota) {
              if (upgradeFile.existsSync() == false) {
                _upgradeError(_l10n.upg_17, UpgradeError.errorCode13);
                continue;
              }

              final int wlanDownLoadOtaVersion = _getWlanDownLoadOtaVersion();
              final int? localOtaVersion =
                  await UpgradeModel().getLocalVersion(type);

              /// OTAアップグレードパッケージのバージョンがローカルのバージョン以下の場合、スキップします
              if (localOtaVersion == null ||
                  wlanDownLoadOtaVersion > localOtaVersion) {
                /// UPFアップデート時にUPFファイルの署名を検証する
                final bool isVerified =
                    await UPFVerifySignatureModel.verifyFileSignature(
                        upgradeFile.path);

                if (isVerified == false) {
                  UpdateAppLog.e(
                      "${upgradeFile.path} signature verification failed");
                  _upgradeError(_l10n.upg_16, UpgradeError.errorCode11);
                  continue;
                }

                _totalLength = upgradeFile.lengthSync();
                bool isSuccess = await _otaUpgrade(upgradeFile);
                if (isSuccess == false) {
                  _upgradeError(_l10n.upg_16, UpgradeError.errorCode19);
                  continue;
                }
              } else {
                /// do nothing
              }
            } else {
              UpgradeModel().chooseUpf = downloadedUpfCollection[i];
              await _upgradeSystem();
            }

            if (UpgradeModel().endErrorTitle.isEmpty) {
              if (upgradeFile.existsSync()) {
                upgradeFile.deleteSync();
              }
            }
          } catch (e) {
            _upgradeError(_l10n.upg_16, UpgradeError.errorCode25);
          }
        }
      } else {
        await _upgradeSystem();
      }

      await _postUpgradeOperation();
    });
  }

  ///
  /// アップグレードを実行する
  ///
  Future<void> _upgradeSystem() async {
    /// UPFアップデート時にUPFファイルの署名を検証する
    final bool isVerified = await UPFVerifySignatureModel.verifyFileSignature(
        UpgradeModel().chooseUpf.path);

    if (isVerified == false) {
      UpdateAppLog.e(
          "${UpgradeModel().chooseUpf.path} signature verification failed");
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode11);
      return;
    }

    /// 解凍
    bool isSuccess = await _unzipAndSave();

    UpgradeModel().endUsbTransactionIfNeeded();

    if (isSuccess) {
      await Future.delayed(const Duration(seconds: 3));

      try {
        await _getTotalLength();

        /// アップグレードを開始する
        await _openFileUpdateJson();
      } catch (e) {
        UpdateAppLog.e("getTotalLength / openFileUpdateJson error : $e");
        _upgradeError(_l10n.upg_16, UpgradeError.errorCode12);
        return;
      }

      if (UpgradeModel().endErrorTitle.isNotEmpty) {
        return;
      }

      state = state.copyWith(
        progressValue: _progress100,
      );
    } else {
      UpdateAppLog.e("解凍に失敗しました");
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode10);
      return;
    }
  }

  ///
  /// 更新完了後の操作
  ///
  Future<void> _postUpgradeOperation() async {
    if (UpgradeModel().endErrorTitle.isNotEmpty) {
      return;
    }

    await Future.delayed(const Duration(seconds: 1));

    /// この更新プログラムのUPFHeader.jsonファイルを保存します
    final String upfHeaderJsonPath;
    if (isDownLoadUpgrade) {
      upfHeaderJsonPath =
          join(UpgradeModel().upgradeSavePath, _wlanKey, _upfHeaderJson);
    } else {
      upfHeaderJsonPath = join(_savePath, _upfHeaderJson);
    }

    final bool isSaveSuccess =
        await UpgradeModel().saveUpfHeaderJson(upfHeaderJsonPath);

    if (isSaveSuccess == false) {
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode21);
      UpdateAppLog.e("Saving UPFHeader.json failed");
      return;
    }

    _deleteUnpackedFolderAfterUpdate();

    /// 更新が完了したことを示す識別ファイルを生成する
    try {
      UpdateAppLog.d("create IIVO_UPEND");
      final FileEntity upEnd =
          FileEntity(join(memorySector.brother_dir.absolutePath, _iivoUpend));
      if (upEnd.existsSync() == false) {
        upEnd.createSync();
      }
    } catch (e) {
      _upgradeError(_l10n.upg_18, UpgradeError.errorCode23);
      return;
    }

    await UpdateGrade().startBrotherPanel();

    /// PROUPEND.IIVOが存在するかどうかを判断すると、Linoputatは更新と切断を終了しています
    await _isExistsProUpEnd();

    UpgradeModel().saveUpdateLogCat();

    if (UpgradeModel().needSnapshotWhenSkipOTAUpgrade &&
        isDownLoadUpgrade == false) {
      await UpdateGrade().executeCommandByBrotherUpdateDaemon(_retakeReq);
    }

    await Future.delayed(const Duration(seconds: 10));

    UpdateAppPagesRoute()
        .pushReplacement(nextRoute: UpdateAppPageRouteEnum.upgradeEnd);
  }

  ///
  /// 更新后の解凍用フォルダを削除
  ///
  void _deleteUnpackedFolderAfterUpdate() {
    /// アップグレードの残りファイルを削除する
    try {
      final directory = Directory(_savePath);

      if (directory.existsSync()) {
        directory.deleteSync(recursive: true);
        UpdateAppLog.d('$_savePath deleted successfully!');
      } else {
        UpdateAppLog.d('$_savePath does not exist.');
      }
    } catch (e) {
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode22);
      return;
    }
  }

  final _l10n = lookupAppLocalizations(AppLocale().getCurrentLocale());

  ///
  /// 進捗率
  ///
  int _initValue = 0;

  ///
  /// プログレスバーのフル値
  ///
  final int _progress100 = 99;
  final int _progress5 = 5;
  final int _progress20 = 20;
  final int _progress80 = 80;

  ///
  /// 5 分の値
  ///
  final int _minutes5 = 300;

  ///
  /// 固定値
  ///
  final String _componentListKey = "componentlist";
  final String _typeKey = "type";
  final String _fileListKey = "filelist";
  final String _versionKey = "version";
  final String _srcPathKey = "srcpath";
  final String _dirPathKey = "dirpath";
  final String _iivoUpdate = "IIVO_UPDATE";
  final String _startUpdate = "START_UPDATE";
  final String _mainUpdateOk = "IIVO_RESULT_OK";
  final String _mainUpdateNg = "IIVO_RESULT_NG";
  final String _iivoUpend = "IIVO_UPEND";
  final String _iivoProductMode = "IIVO_PRODUCTMODE";
  final String _otaPackageJson = "OtaPackage.json";
  final String _otaPackageZip = "OtaPackage.zip";
  final String _versionCheck = "versioncheck";

  final String _upfHeaderJson = "UPFHeader.json";
  final String _fileUpdateJson = "FileUpdate.json";
  final String _updateAppSubDir = "updateApp";
  final String _fileUpdate = "FileUpdate";
  final String _fileUpdatePackage = "FileUpdatePackage";
  final String _proupendIIVO = "PROUPEND.IIVO";
  final String _wlanKey = "wlan";

  ///
  /// パスを保存する
  ///
  String get _savePath =>
      join(memorySector.brother_dir.absolutePath, _updateAppSubDir);

  ///
  /// UPFHeader.json パス
  ///
  String get upfHeaderJsonPath => join(_savePath, _upfHeaderJson);

  ///
  /// FileUpdate.json パス
  ///
  String get fileUpdateJsonPath =>
      join(_savePath, _fileUpdate, _fileUpdateJson);

  ///
  /// FileUpdatePackage パス
  ///
  String get fileUpdatePackagePath =>
      join(_savePath, _fileUpdate, _fileUpdatePackage);

  String get otaPackagePath =>
      "/data/user/0/com.brother.ph.iivo.home/files/configs/";

  final methodChannel = const MethodChannel('update_grade');

  final String _syncCommand = "sync";
  final String _retakeReq = "/system/bin/brother_warp_snapshot_retake_req.sh";
  String get _repairedPath => "/mnt/brother_cache_partition_repaired.trg";
  final String _resetCommand = "/system/bin/brother_cache_partition_reset.sh";

  ///
  /// 更新ファイルの合計サイズ
  ///
  int _totalLength = 0;

  Timer? _timer;

  /// 60ミリ秒
  static const int _milliseconds60 = 60;

  /// 100ミリ秒
  static const int _milliseconds100 = 100;

  /// 10000ミリ秒
  static const int _milliseconds10000 = 10000;

  /// versioncheck == false バージョン番号の比較は不要です
  static const int _versionCheckFalseVersion = -1;

  /// PROUPEND.IIVO パス
  String get _proUpEndPath =>
      join(memorySector.line_update.absolutePath, _proupendIIVO);

  @override
  void update() {
    int progress =
        ((_initValue / _totalLength) * _progress80).toInt() + _progress20;

    if (progress < state.progressValue) {
      return;
    }

    state = state.copyWith(
      progressValue: progress > _progress100 ? _progress100 : progress,
    );
  }

  ///
  /// 更新ファイルの合計サイズを取得します
  ///
  Future<void> _getTotalLength() async {
    UpdateAppLog.d("Get the total file size start");
    try {
      int length = 0;
      final String jsonString =
          FileEntity(fileUpdateJsonPath).readAsStringSync();
      final Map<String, dynamic> jsonData = jsonDecode(jsonString);
      final List<dynamic> componentList = jsonData[_componentListKey];

      for (var element in componentList) {
        final List<Map<String, dynamic>> fileList =
            element[_fileListKey].cast<Map<String, dynamic>>();

        for (var fileData in fileList) {
          final String path =
              join(fileUpdatePackagePath, fileData[_srcPathKey].toString());
          if (_checkPathTypeIsDirectory(path)) {
            length = length + await _calculateDirectorySize(Directory(path));
          } else {
            if (FileEntity(path).existsSync()) {
              length = length + FileEntity(path).lengthSync();
            }
          }
        }
      }

      _totalLength = length;
    } catch (e) {
      UpdateAppLog.e("getTotalLength failed:$e");
      _upgradeError(_l10n.upg_16, UpgradeError.errorCodeNone);
      return;
    }
    UpdateAppLog.d("Get the total file size end :$_totalLength");
  }

  ///
  /// フォルダーのサイズを計算する関数
  ///
  Future<int> _calculateDirectorySize(Directory directory) async {
    int totalSize = 0;
    var futures = <Future<int>>[];

    await for (var entity in directory.list()) {
      if (entity is File) {
        futures.add(entity.length());
      } else if (entity is Directory) {
        futures.add(_calculateDirectorySize(entity));
      }
    }

    var sizes = await Future.wait(futures);

    for (var size in sizes) {
      totalSize += size;
    }

    return totalSize;
  }

  ///
  /// OTAパッケージ取り付け
  ///
  Future<bool> _otaUpgrade(FileEntity fileEntity) async {
    try {
      UpdateAppLog.d("update OTA start");
      int length = fileEntity.lengthSync();
      double progress = 0;

      ({bool isSuccess, UpgradeError errorCode}) isClearSuccess =
          await _clearCacheFailureRecovery();
      if (isClearSuccess.isSuccess == false) {
        _upgradeError(_cacheCleanupFailureMessage, isClearSuccess.errorCode);
        UpdateAppLog.e("/cache clear is fail:$isClearSuccess");
        return false;
      }

      if (Directory(otaPackagePath).existsSync() == false) {
        Directory(otaPackagePath).createSync(recursive: true);
      }

      String unZipOtaJsonCmd =
          "unzip  ${fileEntity.path}  $_otaPackageJson -o -d $otaPackagePath";
      String chmodJson =
          "chmod -R 777   ${join(otaPackagePath, _otaPackageJson)} ";
      bool? isCopyOtaJsonSuccess = await UpdateGrade()
          .executeCommandByBrotherUpdateDaemon(
              "$unZipOtaJsonCmd,$_syncCommand,$chmodJson");

      if (isCopyOtaJsonSuccess == null || isCopyOtaJsonSuccess == false) {
        _upgradeError(_l10n.upg_16, UpgradeError.errorCode16);
        UpdateAppLog.e("$_otaPackageJson unzip fail");
        return false;
      }

      FileEntity otaJsonFile =
          FileEntity(join(otaPackagePath, _otaPackageJson));

      final String jsonString = otaJsonFile.readAsStringSync();
      final Map<String, dynamic> otaZipData = jsonDecode(jsonString);

      String url = otaZipData["url"];
      String otaChaShDir = url.split("file://")[1];

      String unZipOtaZipCmd =
          "unzip  ${fileEntity.path}  $_otaPackageZip  -o -d ${dirname(otaChaShDir)}";
      String chmodZip = "chmod -R 777   $otaChaShDir ";

      bool? isCopyOtaZipSuccess = await UpdateGrade()
          .executeCommandByBrotherUpdateDaemon(
              "$unZipOtaZipCmd,$_syncCommand,$chmodZip");

      if (isCopyOtaZipSuccess == null || isCopyOtaZipSuccess == false) {
        _upgradeError(_l10n.upg_16, UpgradeError.errorCode17);
        UpdateAppLog.e("$_otaPackageZip unzip fail");
        return false;
      }

      if (isDownLoadUpgrade == false) {
        fileEntity.deleteSync();
      }

      /// OTAアップグレードの進捗状況を取得する
      methodChannel.setMethodCallHandler((call) async {
        if (call.method == 'updateProgress') {
          dynamic arguments = call.arguments;

          if (arguments is double) {
            UpdateAppLog.d("updateProgress: $arguments");
            progress = arguments > 1
                ? 0
                : arguments > progress
                    ? arguments
                    : progress;
            _initValue = (length * progress).toInt();
            update();
          } else {
            UpdateAppLog.e("OTAUpgrade return null");
          }
        }
        return;
      });

      await UpdateGrade().init();
      final dynamic value = await UpdateGrade().updateOTA();

      /// OTAアップグレードの残存ファイルを削除する
      String deleteOtaZip = "rm  $otaChaShDir";
      bool? isDeleteOtaZipSuccess =
          await UpdateGrade().executeCommandByBrotherUpdateDaemon(deleteOtaZip);
      if (isDeleteOtaZipSuccess == null || isDeleteOtaZipSuccess == false) {
        _upgradeError(_l10n.upg_16, UpgradeError.errorCode18);
      }
      otaJsonFile.deleteSync();

      if (value) {
        _initValue = length;
        update();
        UpdateAppLog.d("OTAUpgrade is success");
      } else {
        UpdateAppLog.e("OTAUpgrade is fail");
        _upgradeError(_l10n.upg_16, UpgradeError.errorCode19);
        return false;
      }

      UpdateAppLog.d("update OTA end");
      return true;
    } catch (e) {
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode31);
      UpdateAppLog.e("OTAUpgrade is fail:$e");
      return false;
    }
  }

  /// /cacheゴミ掃除に失敗した後に表示される文言です
  String _cacheCleanupFailureMessage = "";

  ///
  /// /cacheのゴミ掃除が動かない
  ///
  Future<({bool isSuccess, UpgradeError errorCode})>
      _clearCacheFailureRecovery() async {
    Completer<({bool isSuccess, UpgradeError errorCode})> completer =
        Completer<({bool isSuccess, UpgradeError errorCode})>();

    const Duration pollInterval = Duration(seconds: 1);

    _timer = Timer.periodic(pollInterval, (timer) async {
      /// 上記のbrother_cache_partition_repaired.trgファイルをみつけて、『/system/bin/brother_cache_partition_reset.shを実行する
      if (FileEntity(_repairedPath).existsSync()) {
        _timer?.cancel();

        bool? isSuccess = await UpdateGrade()
            .executeCommandByBrotherUpdateDaemon(_resetCommand);
        if (isSuccess == null || isSuccess == false) {
          UpdateAppLog.e("run $_resetCommand  $isSuccess");
          _cacheCleanupFailureMessage = _l10n.upg_22;
          completer.complete(
              (isSuccess: false, errorCode: UpgradeError.errorCode26));
        } else {
          completer.complete(
              (isSuccess: true, errorCode: UpgradeError.errorCodeNone));
        }
      }

      /// /mnt/brother_cache_partition_repaired.trgがあるかどうかをチェックする, 60秒チェックしてTimout場合、更新エラーで終了する
      if (timer.tick > _milliseconds60) {
        UpdateAppLog.e("/cache clear TimeOut");
        _timer?.cancel();
        _cacheCleanupFailureMessage = _l10n.upg_21;
        completer
            .complete((isSuccess: false, errorCode: UpgradeError.errorCode27));
      }
    });

    return await completer.future;
  }

  ///
  /// home.apkファイル取り付け
  ///
  Future<void> _homeUpgrade(FileEntity fileEntity, String dirPath) async {
    UpdateAppLog.d("update home.apk start");
    await Future.delayed(const Duration(seconds: 1));

    /// home.apkをコピーする
    String cpHomeApk = "cp -p  ${fileEntity.path}  $dirPath";

    /// UPFHeader.jsonを保存する
    String cpUpfHeaderJson =
        "cp -p ${join(_savePath, _upfHeaderJson)} ${UpgradeModel().upfHeaderSavePath}";

    /// IIVO_UPDATEを削除する
    String deleteIivoUpdate =
        "rm  ${join(memorySector.brother_dir.absolutePath, _iivoUpdate)}";

    /// 解凍後の更新フォルダを削除する
    String deleteUpdateApp = "rm  -rf  $_savePath";

    String deleteDownLoadUpf = "";
    if (isDownLoadUpgrade) {
      /// downLoadでダウンロードしたupfファイルを削除する
      for (var upfFolderType in UpgradeModel().downloadedUpfCollection) {
        deleteDownLoadUpf = "$deleteDownLoadUpf,rm  ${upfFolderType.path}";
      }
    }

    bool? isSuccess = false;
    if (deleteDownLoadUpf.isEmpty) {
      isSuccess = await UpdateGrade().executeCommandByBrotherUpdateDaemon(
          "$cpHomeApk,$cpUpfHeaderJson,$deleteIivoUpdate,$deleteUpdateApp,$_syncCommand,$_retakeReq");
    } else {
      isSuccess = await UpdateGrade().executeCommandByBrotherUpdateDaemon(
          "$cpHomeApk,$cpUpfHeaderJson$deleteDownLoadUpf,$deleteIivoUpdate,$deleteUpdateApp,$_syncCommand,$_retakeReq");
    }

    if (isSuccess != null && isSuccess == false) {
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode33);
      return;
    }
    _initValue = _initValue + fileEntity.lengthSync();
    update();

    UpdateAppLog.d("update home.apk end");
  }

  ///
  /// panel.apkファイル取り付け
  ///
  Future<void> _panelUpgrade(List<Map<String, dynamic>> fileList) async {
    for (var fileData in fileList) {
      await _fileUpgradeUsingService(
        fileData[_srcPathKey].toString(),
        fileData[_dirPathKey].toString(),
        UpgradeError.errorCode34,
        UpgradeError.errorCode35,
      );
    }
  }

  ///
  /// pattern_dataファイル取り付け
  ///
  Future<void> _patternDataUpgrade(List<Map<String, dynamic>> fileList) async {
    for (var fileData in fileList) {
      FileEntity fileEntity = FileEntity(
          join(fileUpdatePackagePath, fileData[_srcPathKey].toString()));

      if (_checkPathTypeIsDirectory(fileEntity.path)) {
        final String srcPath = fileData[_srcPathKey].toString();
        final String dirPath = fileData[_dirPathKey].toString();
        if (await _maybeBackupMessageSound(srcPath, dirPath) == false) {
          _upgradeError(_l10n.upg_16, UpgradeError.errorCode28);
          return;
        }
        await _directoryUpgrade(srcPath, dirPath, UpgradeError.errorCode42,
            UpgradeError.errorCode43);
      } else {
        if (fileEntity.existsSync() == false) {
          _upgradeError(_l10n.upg_17, UpgradeError.errorCode42);
          return;
        }

        try {
          fileEntity.copySync(join(
              fileData[_dirPathKey].toString(), basename(fileEntity.path)));
          _initValue = _initValue + fileEntity.lengthSync();
        } catch (e) {
          _upgradeError(_l10n.upg_16, UpgradeError.errorCode43);
          UpdateAppLog.e("${basename(fileEntity.path)} upgrade fail:$e");
        }
        update();
      }
    }
  }

  ///
  /// 必要に応じてmessage_soundをバックアップします
  /// @see [memorySector.message_sound]
  ///
  /// 必要な場合とは：更新対象フォルダがsystem_soundディレクトリ自体またはその親ディレクトリである場合です。
  ///
  /// system_soundをバックアップする理由：更新時にターゲットディレクトリ内のすべてのファイルが削除され、
  /// ユーザーがダウンロードしたボイスファイルが削除される可能性があるため、先にバックアップすることでこの問題を防ぎます。
  ///
  /// アップグレードパッケージには常にデフォルトの音声ファイルが含まれているため、
  /// ユーザーがダウンロードした音声ファイルのみをバックアップする必要があります。
  ///
  /// また、バックアップファイルは次回起動時に復元されます
  /// @see [Upgrade._restoreVoiceBackupIfAvailable]
  ///
  /// - params:
  ///   - [srcPath] FileUpdate.jsonの`srcpath`フィールドの値
  ///   - [dirPath] FileUpdate.jsonの`dirpath`フィールドの値
  ///
  /// - return: trueはバックアップ成功またはバックアップ不要、falseはバックアップが必要だがバックアップ失敗
  ///
  /// PS: **これは https://brothergroup.atlassian.net/browse/PHFIRMIIVO-8359 の暫定対策です
  /// この対策は音声バージョンと他のコンポーネントのバージョンの不一致を引き起こす可能性があります**
  ///
  /// @see [Upgrade.backupVoice]
  ///
  Future<bool> _maybeBackupMessageSound(String srcPath, String dirPath) async {
    final String messageSoundPath = memorySector.message_sound.absolutePath;
    final String destinationPath = join(dirPath, basename(srcPath));
    if (equals(destinationPath, messageSoundPath) ||
        isWithin(destinationPath, messageSoundPath)) {
      return Upgrade().backupVoice(backupDefaultVoice: false);
    }
    return true;
  }

  ///
  /// panel_libファイル取り付け
  ///
  Future<void> _panelLibUpgrade(List<Map<String, dynamic>> fileList) async {
    for (var fileData in fileList) {
      await _fileUpgradeUsingService(
        fileData[_srcPathKey].toString(),
        fileData[_dirPathKey].toString(),
        UpgradeError.errorCode44,
        UpgradeError.errorCode45,
      );
    }
  }

  ///
  /// フォルダの更新
  ///
  Future<void> _directoryUpgrade(
    String srcPath,
    String dirPath,
    UpgradeError existsErrorCode,
    UpgradeError copyErrorCode,
  ) async {
    UpdateAppLog.d("cp $srcPath to $dirPath");
    try {
      Directory directory = Directory(join(fileUpdatePackagePath, srcPath));

      if (directory.existsSync() == false) {
        _upgradeError(_l10n.upg_17, existsErrorCode);
        return;
      }

      String filePath = join(dirPath, basename(srcPath));

      if (Directory(filePath).existsSync()) {
        Directory(filePath).deleteSync(recursive: true);
      } else {
        Directory(filePath).createSync(recursive: true);
      }

      await _copyDirectory(directory.path, filePath);
    } catch (e) {
      _upgradeError(_l10n.upg_16, copyErrorCode);
      UpdateAppLog.e("${basename(srcPath)} upgrade fail:$e");
    }
  }

  ///
  /// ファイルの更新
  ///
  Future<void> _fileUpgradeUsingService(
    String srcPath,
    String dirPath,
    UpgradeError existsErrorCode,
    UpgradeError copyErrorCode,
  ) async {
    FileEntity fileEntity = FileEntity(join(fileUpdatePackagePath, srcPath));

    if (fileEntity.existsSync() == false) {
      _upgradeError(_l10n.upg_17, existsErrorCode);
      return;
    }
    bool isSuccess = await _copyDataUsingService(fileEntity.path, dirPath);
    if (isSuccess == false) {
      _upgradeError(_l10n.upg_16, copyErrorCode);
      return;
    }
    _initValue = _initValue + fileEntity.lengthSync();
    update();
  }

  ///
  /// mainファイル取り付け
  ///
  Future<void> _mainUpgrade(FileEntity fileEntity, String dirPath) async {
    UpdateAppLog.d("update main start");
    await Future.delayed(const Duration(seconds: 1));
    UpdateAppLog.d("copy ${fileEntity.path} to $dirPath");
    String mainDirPath = join(dirPath, basename(fileEntity.path));

    try {
      fileEntity.copySync(mainDirPath);
    } catch (e) {
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode37);
      return;
    }

    if (FileEntity(mainDirPath).existsSync()) {
      UpdateAppLog.d("$mainDirPath is exists");
    } else {
      UpdateAppLog.e("$mainDirPath is not exists");
    }

    bool isSuccess = await _mainCheck(fileEntity);
    FileEntity(join(dirPath, basename(fileEntity.path))).deleteSync();

    if (isSuccess == false) {
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode37);
    }

    UpdateAppLog.d("update main end");
  }

  ///
  /// メインプログラムが更新されると、パスの下のファイルが読み取られ、更新ステータスが判断されます
  ///
  Future<bool> _mainCheck(FileEntity fileEntity) async {
    await UpdateGrade().startBrotherPanel();

    Completer<bool> completer = Completer<bool>();

    const Duration pollInterval = Duration(seconds: 1);
    int counts = 0;
    _timer = Timer.periodic(pollInterval, (timer) async {
      counts = counts + 1;
      await for (var entity
          in Directory(memorySector.brother_dir.absolutePath).list()) {
        if (entity is File) {
          String fileName = basename(entity.path);

          if (fileName == _mainUpdateOk) {
            UpdateAppLog.d("Main Update OK");
            _initValue = _initValue + fileEntity.lengthSync();
            update();
            _timer?.cancel();
            completer.complete(true);
          }

          if (fileName == _mainUpdateNg) {
            UpdateAppLog.d("Main Update NG");
            _timer?.cancel();
            completer.complete(false);
          }

          if (counts >= _minutes5) {
            UpdateAppLog.d("Main Update TimeOut");
            await UpdateGrade().stopBrotherPanel();
            _timer?.cancel();
            completer.complete(false);
          }
        }
      }
    });

    return await completer.future;
  }

  ///
  /// フォルダをコピーする機能
  ///
  Future<void> _copyDirectory(String sourcePath, String destinationPath) async {
    var sourceDir = Directory(sourcePath);
    var destinationDir = Directory(destinationPath);

    if (!await destinationDir.exists()) {
      await destinationDir.create();
    }

    await for (var entity in sourceDir.list()) {
      if (entity is File) {
        /// ファイルの場合は、ファイルをコピーします
        var newPath = join(destinationDir.path, basename(entity.path));
        await entity.copy(newPath);
        _initValue = _initValue + entity.lengthSync();
        update();
      } else if (entity is Directory) {
        /// フォルダーの場合、コピー関数は再帰的に呼び出されます
        var newPath = join(destinationDir.path, basename(entity.path));
        await _copyDirectory(entity.path, newPath);
      }
    }
  }

  ///
  ///FileUpdate.jsonを開く
  ///
  Future<void> _openFileUpdateJson() async {
    UpdateAppLog.d("read ${basename(fileUpdateJsonPath)}  start");
    final String jsonString = FileEntity(fileUpdateJsonPath).readAsStringSync();
    final Map<String, dynamic> jsonData = jsonDecode(jsonString);
    UpdateAppLog.d("read ${basename(fileUpdateJsonPath)}  end");

    Map<int, int> currentUpfHeaderMap = _getCurrentUpfHeaderJson();

    if (jsonData.containsKey(_componentListKey)) {
      final List<dynamic> componentList = jsonData[_componentListKey];

      final List<dynamic> sortList = _sortFileUpdateJsonData(componentList);

      for (var component in sortList) {
        if (UpgradeModel().endErrorTitle.isNotEmpty) {
          continue;
        }

        if (component.containsKey(_typeKey) &&
            component.containsKey(_fileListKey)) {
          /// アップデート種別
          final int type = component[_typeKey];

          /// 現在のtypeのバージョン番号
          final int? currentVersion = currentUpfHeaderMap[type];

          /// 最後に保存されたバージョン番号
          final int? lastVersion = await UpgradeModel().getLocalVersion(type);

          if (currentVersion == null) {
            continue;
          }

          if (currentVersion != _versionCheckFalseVersion &&
              lastVersion != null) {
            /// OTAアップグレードパッケージのバージョンがローカルのバージョン以下の場合、スキップします
            if (type == UpgradeType.ota && currentVersion <= lastVersion) {
              /// アップデートでOSをスキップした場合もスナップショットを取り直すようにする
              UpgradeModel().needSnapshotWhenSkipOTAUpgrade = true;
              continue;
            }

            /// OTA以外のアップグレードパッケージのバージョンがローカルのバージョン以下の場合、スキップします
            if (currentVersion < lastVersion) {
              continue;
            }
          }

          final List<Map<String, dynamic>> fileList =
              component[_fileListKey].cast<Map<String, dynamic>>();

          switch (type) {
            /// OTA
            case UpgradeType.ota:
              Map<String, dynamic> map = fileList.first;
              FileEntity fileEntity = FileEntity(
                  join(fileUpdatePackagePath, map[_srcPathKey].toString()));

              if (fileEntity.existsSync()) {
                await _otaUpgrade(fileEntity);
              } else {
                _upgradeError(_l10n.upg_17, UpgradeError.errorCode30);
              }
              break;

            /// HOME APK
            case UpgradeType.home:
              Map<String, dynamic> map = fileList.first;
              FileEntity fileEntity = FileEntity(
                  join(fileUpdatePackagePath, map[_srcPathKey].toString()));

              if (fileEntity.existsSync()) {
                await _homeUpgrade(fileEntity, map[_dirPathKey].toString());
              } else {
                _upgradeError(_l10n.upg_17, UpgradeError.errorCode32);
              }
              break;

            /// PANEL APK
            case UpgradeType.panel:
              await _panelUpgrade(fileList);
              break;

            /// MAIN
            case UpgradeType.main:
              Map<String, dynamic> map = fileList.first;
              FileEntity fileEntity = FileEntity(
                  join(fileUpdatePackagePath, map[_srcPathKey].toString()));

              if (fileEntity.existsSync()) {
                await _mainUpgrade(fileEntity, map[_dirPathKey].toString());
              } else {
                _upgradeError(_l10n.upg_17, UpgradeError.errorCode36);
              }
              break;

            /// MANUAL
            case UpgradeType.manual:
              Map<String, dynamic> map = fileList.first;
              Directory fileEntity = Directory(
                  join(fileUpdatePackagePath, map[_srcPathKey].toString()));

              if (fileEntity.existsSync()) {
                await _directoryUpgrade(
                  fileEntity.path,
                  map[_dirPathKey].toString(),
                  UpgradeError.errorCode38,
                  UpgradeError.errorCode39,
                );
              } else {
                _upgradeError(_l10n.upg_17, UpgradeError.errorCode38);
              }
              break;

            /// EULA
            case UpgradeType.eula:
              Map<String, dynamic> map = fileList.first;
              Directory fileEntity = Directory(
                  join(fileUpdatePackagePath, map[_srcPathKey].toString()));

              if (fileEntity.existsSync()) {
                await _directoryUpgrade(
                  fileEntity.path,
                  map[_dirPathKey].toString(),
                  UpgradeError.errorCode40,
                  UpgradeError.errorCode42,
                );
              } else {
                _upgradeError(_l10n.upg_17, UpgradeError.errorCode40);
              }
              break;

            /// PATTERN DATA(Stitch data, thumbnail)
            case UpgradeType.panelData:
              await _patternDataUpgrade(fileList);
              break;

            ///PANEL LIB
            case UpgradeType.panelLib:
              await _panelLibUpgrade(fileList);
              break;

            default:
              _upgradeError(_l10n.upg_17, UpgradeError.errorCode15);
              break;
          }
        } else {
          return;
        }
      }
    }
  }

  ///
  /// 現在のUPFHeader.jsonファイルを読み取る
  ///
  Map<int, int> _getCurrentUpfHeaderJson() {
    final Map<int, int> currentUpfHeaderMap = {};
    if (FileEntity(upfHeaderJsonPath).existsSync() == false) {
      return {};
    }

    final String jsonString = FileEntity(upfHeaderJsonPath).readAsStringSync();
    final Map<String, dynamic> jsonData = jsonDecode(jsonString);
    if (jsonData.containsKey(_componentListKey)) {
      final List<Map<String, dynamic>> componentList =
          jsonData[_componentListKey].cast<Map<String, dynamic>>();

      for (var component in componentList) {
        if (component.containsKey(_versionCheck) &&
            component[_versionCheck] == false) {
          final int type = component[_typeKey];
          currentUpfHeaderMap.addAll({type: _versionCheckFalseVersion});
          continue;
        }

        if (component.containsKey(_typeKey) &&
            component.containsKey(_versionKey)) {
          final int type = component[_typeKey];
          final int version = component[_versionKey];
          currentUpfHeaderMap.addAll({type: version});
        }
      }

      return currentUpfHeaderMap;
    } else {
      return {};
    }
  }

  ///
  /// wlanDownLoadでダウンロードしたOTAアップグレードパッケージのバージョン番号を取得します
  ///
  int _getWlanDownLoadOtaVersion() {
    FileEntity wlanDownLoadUpfHeaderJsonFile = FileEntity(
        join(UpgradeModel().upgradeSavePath, _wlanKey, _upfHeaderJson));

    if (wlanDownLoadUpfHeaderJsonFile.existsSync() == false) {
      return _versionCheckFalseVersion;
    }

    final String jsonString = wlanDownLoadUpfHeaderJsonFile.readAsStringSync();
    final Map<String, dynamic> jsonData = jsonDecode(jsonString);

    final List<Map<String, dynamic>> componentList =
        jsonData[_componentListKey].cast<Map<String, dynamic>>();

    for (var component in componentList) {
      final int type = component[_typeKey];
      final bool versionCheck = component[_versionCheck];
      final int version = component[_versionKey];

      if (type == UpgradeType.ota) {
        if (versionCheck == false) {
          return _versionCheckFalseVersion;
        } else {
          return version;
        }
      } else {
        /// do nothing
      }
    }

    return _versionCheckFalseVersion;
  }

  ///
  /// fileUpdate.json内の更新データを並べ替える
  ///
  List<dynamic> _sortFileUpdateJsonData(List<dynamic> componentList) {
    UpdateAppLog.d("Sort the update order start");
    List<int> sortOrder = [
      UpgradeType.ota,
      UpgradeType.panel,
      UpgradeType.main,
      UpgradeType.manual,
      UpgradeType.eula,
      UpgradeType.panelData,
      UpgradeType.panelLib,
      UpgradeType.home,
    ];

    componentList.sort((a, b) {
      int typeA = a[_typeKey];
      int typeB = b[_typeKey];
      int indexA = sortOrder.indexOf(typeA);
      int indexB = sortOrder.indexOf(typeB);
      return indexA.compareTo(indexB);
    });

    UpdateAppLog.d("Sort the update order end");

    return componentList;
  }

  ///
  /// サービスを呼び出してデータをコピーする
  ///
  Future<bool> _copyDataUsingService(
      String originalPath, String dirPath) async {
    String cpCommand = "cp -p  $originalPath  $dirPath";
    String chmod = "chmod -R 777   ${join(dirPath, basename(originalPath))} ";

    bool? isSuccess = await UpdateGrade().executeCommandByBrotherUpdateDaemon(
        "$cpCommand,$_syncCommand,$chmod,$_retakeReq");

    UpdateAppLog.d("${basename(originalPath)} $isSuccess");

    if (isSuccess == null) {
      return false;
    }

    return isSuccess;
  }

  ///
  /// このパスがフォルダーかどうかを判断します
  ///
  bool _checkPathTypeIsDirectory(String path) {
    final FileSystemEntityType type = FileSystemEntity.typeSync(path);
    return type == FileSystemEntityType.directory;
  }

  ///
  /// 解凍して保存する
  ///
  Future<bool> _unzipAndSave() async {
    FolderType folderType = UpgradeModel().chooseUpf;

    ///
    /// UPF 解凍
    ///
    UpdateAppLog.d("unzip ${basename(folderType.path)}");
    final bool upfSuccess = await UsbManager().unZip(
      folderType.path,
      _savePath,
      password: UPFVerifySignatureModel.password,
    );
    if (!upfSuccess) {
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode10);
      return false;
    }

    state = state.copyWith(progressValue: _progress20);

    return true;
  }

  @override
  bool get isLineUpdate =>
      FileEntity(join(memorySector.brother_dir.absolutePath, _iivoProductMode))
          .existsSync();

  ///
  /// アップグレードエラー、ページにジャンプ
  ///
  void _upgradeError(String errorTile, UpgradeError upgradeError) {
    final directory = Directory(_savePath);

    if (directory.existsSync()) {
      directory.deleteSync(recursive: true);
      UpdateAppLog.d('${basename(directory.path)} deleted successfully!');
    } else {
      UpdateAppLog.d('${basename(directory.path)} does not exist.');
    }

    if (UpgradeModel().endErrorTitle.isEmpty) {
      String errorCodeMessage = upgradeError.errorCodeMessage;
      UpgradeModel().endErrorTitle = "$errorTile\n$errorCodeMessage";
    }
    UpdateAppLog.e(errorTile);
    UpgradeModel().saveUpdateLogCat();
    UpdateAppPagesRoute()
        .pushReplacement(nextRoute: UpdateAppPageRouteEnum.upgradeEnd);
  }

  ///
  /// PROUPEND.IIVOが存在するかどうかを判断すると、Linoputatは更新と切断を終了しています
  ///
  Future<void> _isExistsProUpEnd() async {
    Completer<void> completer = Completer<void>();
    const Duration pollInterval = Duration(milliseconds: _milliseconds100);

    _timer = Timer.periodic(pollInterval, (timer) {
      if (FileEntity(_proUpEndPath).existsSync()) {
        UpdateAppLog.d("PROUPEND.IIVO is exists");
        _deleteProductMode();

        UpdateGrade()
            .updateUsbUnMount()
            .then((value) => UpdateGrade().updateCloseConnection());

        _timer?.cancel();
        completer.complete();
      }

      /// 10秒のタイムアウト
      if (timer.tick >= (_milliseconds10000 / _milliseconds100)) {
        UpdateAppLog.d("PROUPEND.IIVO is not  exists , Time Out");
        _deleteProductMode();

        UpdateGrade()
            .updateUsbUnMount()
            .then((value) => UpdateGrade().updateCloseConnection());

        _timer?.cancel();
        completer.complete();
      }
    });

    await completer.future;
  }

  ///
  /// LineUpdate更新完了後に削除
  ///
  void _deleteProductMode() {
    final FileEntity proDuctMode = FileEntity(
        join(memorySector.brother_dir.absolutePath, _iivoProductMode));
    final FileEntity proUpChk =
        FileEntity(join(memorySector.brother_dir.absolutePath, _iivoUpend));
    final FileEntity iivoUpdateFile =
        FileEntity(join(memorySector.brother_dir.absolutePath, _iivoUpdate));
    final FileEntity startUpdateFile =
        FileEntity(join(memorySector.brother_dir.absolutePath, _startUpdate));
    final FileEntity txtFile = FileEntity(UpgradeModel().txtSavePath);

    if (proDuctMode.existsSync()) {
      UpdateAppLog.d("delete IIVO_PRODUCTMODE");
      proDuctMode.deleteSync();
    }

    if (proUpChk.existsSync()) {
      UpdateAppLog.d("delete IIVO_UPEND");
      proUpChk.deleteSync();
    }

    if (iivoUpdateFile.existsSync()) {
      UpdateAppLog.d("delete IIVO_UPDATE");
      iivoUpdateFile.deleteSync();
    }

    if (startUpdateFile.existsSync()) {
      UpdateAppLog.d("delete START_UPDATE");
      startUpdateFile.deleteSync();
    }

    if (txtFile.existsSync()) {
      UpdateAppLog.d("delete updateCount.txt");
      txtFile.deleteSync();
    }
  }

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
  }

  @override
  bool get isDownLoadUpgrade => UpgradeModel().deviceKind == DeviceKind.wLan;
}
