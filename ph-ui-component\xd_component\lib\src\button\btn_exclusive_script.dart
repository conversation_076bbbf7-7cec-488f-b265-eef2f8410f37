// ignore_for_file: camel_case_types

import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';

import '../../xd_component.dart';

class btn_exclusive_script extends StatelessWidget {
  const btn_exclusive_script({
    super.key,
    this.onTap,
    this.state = ButtonState.normal,
    this.feedBackControl = const FeedBackControl(),
    required this.exclusiveIcon,
  });
  final void Function()? onTap;
  final ButtonState state;
  final FeedBackControl? feedBackControl;
  final Widget exclusiveIcon;

  @override
  Widget build(BuildContext context) => FeedBackButton(
        onTap: state == ButtonState.disable ? null : () => onTap?.call(),
        state: state,
        style: ThemeButton.btn_n_size460x80_theme9,
        feedBackControl: feedBackControl,
        child: Center(child: exclusiveIcon),
      );
}
