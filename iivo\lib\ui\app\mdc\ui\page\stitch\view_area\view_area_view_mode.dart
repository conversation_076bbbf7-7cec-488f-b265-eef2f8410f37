import 'dart:math';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../model/frame_model.dart';
import '../../../../../../../model/projector_model.dart';
import '../../../../model/color_table.dart';
import '../../../../model/device_info_model.dart';
import '../../../../model/paint/pen_model.dart';
import '../../../../model/paint/scan_model.dart';
import '../../../../model/paint/top_bar_model.dart' as paint;
import '../../../../model/scroll_control_model.dart';
import '../../../../model/stitch/creation_isolate.dart';
import '../../../../model/stitch/draw_region_model.dart';
import '../../../../model/stitch/magnification_model.dart';
import '../../../../model/stitch/view_area_model.dart';
import '../stitch_page_view_model.dart';
import 'view_area_view_interface.dart';

final viewAreaViewModelProvider =
    StateNotifierProvider.autoDispose<ViewAreaViewInterface, ViewAreaState>(
        (ref) => ViewAreaViewModel(ref));

class ViewAreaViewModel extends ViewAreaViewInterface {
  ViewAreaViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            ViewAreaState(
              frameDrawPath: Path(),
              displayImageInfo: const DisplayImageInfo(),
              dspImgBeforeProcessed: Uint8List.fromList([]),
            ),
            ref);

  @override
  void build() {
    super.build();

    MagnificationModel().zoomEdit = EditZoom.reset();
    MagnificationModel().zoomInfoSet(MagnificationModel.magnification_100);

    /// 下絵イメージ処理
    if (ScanModel().isLoadScanImage) {
      /// view更新
      state = state.copyWith(
        backgroundImage: ScanModel().sketchesImageData,
        backgroundDensityLevel: ScanModel()
            .backgroundDensityLevel[paint.TopBarModel().densityLevelIndex],
        mdcDensityLevel:
            ScanModel().mdcDensityLevel[paint.TopBarModel().densityLevelIndex],
      );
    }

    update();
    updateAfterStitchCreation = DrawRegionModel().updateDisplayImageInfo;
    updateAfterPropertyChange = _updateAfterPropertyChange;

    /// ステッチ展開処理開始
    CreationModel().startStitchCreation();
  }

  ///
  /// Y方向のスクロールコントローラ
  ///
  final ScrollController _scrollControllerY = ScrollController();
  @override
  ScrollController get scrollControllerY => _scrollControllerY;

  ///
  /// X方向のスクロールコントローラ
  ///
  final ScrollController _scrollControllerX = ScrollController();
  @override
  ScrollController get scrollControllerX => _scrollControllerX;

  ///
  /// キャンバス幅
  ///
  final double _canvasWidth = 564;
  @override
  double get canvasWidth => _canvasWidth;

  ///
  /// キャンバスの高さ
  ///
  final double _canvasHeight = 880;

  ///
  /// Libに対応イメージデータの幅
  ///
  final double _baseImageWidth = 562;

  ///
  /// Libに対応イメージデータの幅
  ///
  final double _baseImageHeight = 880;

  /// 10mm定義
  static const int _gridLine10MmValue = 10;

  /// 25mm定義
  static const int _gridLine25MmValue = 25;

  /// 読み出したSizeデータの変換倍率   '読み出したデータの単位(0.1mm) / 10 = mm'
  static const int _conversionRate = 10;

  /// フレームの破線の一段の長さ
  static const double _dashedLineLength = 1.0;

  /// フレームの破線の間隔
  static const double _dashedLineSpace = 1.0;

  /// 単位変換率（mm/dot）
  static const double _ratio = PenModel.ratio * 2;

  /// 横方向初期化
  bool _scrollXInit = false;

  /// 縦方向初期化
  bool _scrollYInit = false;

  ///
  /// Y方向のスクロールコントローラの幅
  ///
  final double _scrollControllerYWidth = 8;
  @override
  double get scrollControllerYWidth => _scrollControllerYWidth;

  ///
  /// X方向のスクロールコントローラの高さ
  ///
  final double _scrollControllerXHeight = 8;
  @override
  double get scrollControllerXHeight => _scrollControllerXHeight;

  ///
  /// 倍率のリスト
  ///
  final List<double> _scaleList = [1, 2, 4];

  ///
  /// 1本指
  ///
  final _oneFinger = 1;

  ///
  /// 枠種類を取得する
  ///
  @override
  EmbFrameDispType get frameType => DeviceInfoModel().frameType;

  ///
  /// エラー発生
  ///
  @override
  bool get hasErrorCode => CreationModel().hasErrorCode;

  ///
  /// View 更新
  ///
  @override
  void update() {
    double preScale = state.scale;
    double currentScale = _scaleList[MagnificationModel()
        .valueToIndex(MagnificationModel.magnificationLevel)];

    /// view更新
    state = state.copyWith(
      isProjectorON: ProjectorModel().mdcProjector.isMdcProjectorViewOpen,
      isProcessImage: DrawRegionModel().isProcessImage,
      dspImgBeforeProcessed: DrawRegionModel().dspImgBeforeProcessed,
      patternInfoList: DrawRegionModel().getRegionInfoList(refresh: false),
      displayImageInfo: DrawRegionModel().displayImageInfo,
    );

    if (ScanModel().isLoadScanImage) {
      int index = paint.TopBarModel().densityLevelIndex;

      /// view更新
      state = state.copyWith(
        backgroundDensityLevel: ScanModel().backgroundDensityLevel[index],
        mdcDensityLevel: ScanModel().mdcDensityLevel[index],
      );
    }

    if (preScale == currentScale) {
      state = state.copyWith(
        gridType: DeviceInfoModel().gridType,
        gridColor: getMdcGridColor(),
        frameColor: getMdcFrameColor(),
        frameDrawPath: _getFrameDrawPath(currentScale),
      );
    } else {
      Offset centerOffset;
      double canvasWidth;
      double canvasHeight;

      if (MagnificationModel.magnificationLevel ==
          MagnificationModel.magnification_100) {
        centerOffset = Offset.zero;
        canvasWidth = _canvasWidth;
        canvasHeight = _canvasHeight;
      } else {
        centerOffset = Offset(
          state.centerOffset.dx * state.scale,
          state.centerOffset.dy * state.scale,
        );
        canvasWidth = _canvasWidth - _scrollControllerYWidth;
        canvasHeight = _canvasHeight - _scrollControllerXHeight;
      }

      /// view更新
      state = state.copyWith(
        scale: currentScale,
        canvasWidth: canvasWidth,
        canvasHeight: canvasHeight,
        imageWidth: _baseImageWidth * currentScale,
        imageHeight: _baseImageHeight * currentScale,
        gridType: DeviceInfoModel().gridType,
        gridColor: getMdcGridColor(),
        frameColor: getMdcFrameColor(),
        frameDrawPath: _getFrameDrawPath(currentScale),
        centerOffset: centerOffset,
      );

      /// 座標値を調整する
      _scrollControllerAdjust(preScale);
    }
  }

  ///
  /// ドラッグ移動
  ///
  @override
  void onDragMove(Offset offset) {
    if (ViewAreaModel().isDragMove == false) {
      return;
    }

    if (ref.read(stitchPageViewModelProvider.notifier).hasPop == true) {
      return;
    }

    final double offsetXMax = _scrollControllerX.position.maxScrollExtent;
    final double offsetYMax = _scrollControllerY.position.maxScrollExtent;
    double offsetX = _scrollControllerX.offset - offset.dx;
    double offsetY = _scrollControllerY.offset - offset.dy;
    offsetX = offsetX.clamp(0, offsetXMax);
    offsetY = offsetY.clamp(0, offsetYMax);

    _scrollControllerX.jumpTo(offsetX);
    _scrollControllerY.jumpTo(offsetY);
  }

  ///
  /// 2本指でZoom倍率を拡大する前のZoom Indexバックアップ
  ///
  int _selectedZoomIndexBack =
      MagnificationModel().valueToIndex(MagnificationModel.magnificationLevel);

  ///
  /// オンスケールスタート
  ///
  @override
  void onScaleStart(ScaleStartDetails details) {
    if (state.isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    if (ref.read(stitchPageViewModelProvider.notifier).hasPop == true) {
      return;
    }

    /// 1本指操作
    if (details.pointerCount > _oneFinger) {
      _selectedZoomIndexBack = MagnificationModel()
          .valueToIndex(MagnificationModel.magnificationLevel);
    } else {
      /// Do noting
    }
  }

  ///
  /// オンスケール更新
  ///
  @override
  void onScaleUpdate(ScaleUpdateDetails details) {
    if (state.isProcessImage) {
      return;
    }

    if (details.pointerCount < _oneFinger) {
      return;
    }

    /// 2本指操作
    if (details.pointerCount == _oneFinger) {
      if (ViewAreaModel().isDragMove == true) {
        onDragMove(details.focalPointDelta);
      }
    } else {
      if (CreationModel().hasErrorCode) {
        return;
      }

      int index = _selectedZoomIndexBack;
      int step = _getDoubleFingerStep(details.scale);
      index = (index + step)
          .clamp(0, MagnificationModel().magnificationLevelNumber() - 1);

      /// 必要でない場合は更新しない
      if (MagnificationModel.magnificationLevel !=
          MagnificationModel().indexToValue(index)) {
        /// Model更新
        MagnificationModel.magnificationLevel =
            MagnificationModel().indexToValue(index);

        if (MagnificationModel.magnificationLevel ==
            MagnificationModel.magnification_100) {
          ViewAreaModel().isDragMove = false;
        }

        /// 表示模様更新
        DrawRegionModel()
            .updateDisplayImageInfo(DrawRegionModel().handle)
            .then((value) {
          if (!mounted) {
            return;
          }

          /// 拡大中
          MagnificationModel().zoomEdit.zoomNowF = true;
          MagnificationModel()
              .zoomInfoSet(MagnificationModel.magnificationLevel);

          /// View更新
          update();

          /// 他の画面を更新する
          ref
              .read(stitchPageViewModelProvider.notifier)
              .updatePageByChild(ComponentType.viewArea);
        });
      } else {
        /// Do Nothing
      }
    }
  }

  ///
  /// オンタップダウン
  ///
  @override
  void onTapDown(TapDownDetails details) {
    if (state.isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    if (ViewAreaModel().isDragMove == true) {
      return;
    }

    if (ref.read(stitchPageViewModelProvider.notifier).hasPop == true) {
      return;
    }

    Offset position = _adjustCoordinates(details.localPosition);

    List<int> samePointClickList = [];
    for (int i = 0; i < state.patternInfoList.length; i++) {
      Rect rect = Rect.fromLTWH(
        state.patternInfoList[i].x,
        state.patternInfoList[i].y,
        state.patternInfoList[i].w,
        state.patternInfoList[i].h,
      );

      if (rect.contains(position)) {
        samePointClickList.add(i);
      }
    }

    /// 選択模様Indexの確定
    if (samePointClickList.isNotEmpty) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      /// 同じ範囲に一つ模様だけの場合
      if (samePointClickList.length == 1) {
        DrawRegionModel().changeCurrentIndex(samePointClickList.first);
      }

      /// 同じ範囲に複数模様存在の場合
      else {
        if (DrawRegionModel().currentSelectedIndex == samePointClickList.last) {
          DrawRegionModel().changeCurrentIndex(samePointClickList.first);
        } else {
          int index = samePointClickList.firstWhere(
              (element) => element > DrawRegionModel().currentSelectedIndex);
          DrawRegionModel().changeCurrentIndex(index);
        }
      }
    }

    update();
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updatePageByChild(ComponentType.viewArea);
  }

  @override
  List<double> getGridVerticalList() {
    double offsetXMax = state.imageWidth;
    List<double> verticalList = [];
    double xCenter = offsetXMax / 2;
    double xOffset = xCenter;
    int mmValue = state.gridType == EmbGridType.embGridGridLine10
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    int mm2Px = (mmValue / _ratio * state.scale).round();

    /// 中心点の左端の線の位置を計算するには
    xOffset -= mm2Px;
    while (xOffset >= 0) {
      verticalList.add(xOffset);
      xOffset -= mm2Px;
    }

    /// 中心点の右端の線の位置を計算するには
    xOffset = xCenter;
    xOffset += mm2Px;
    while (xOffset <= offsetXMax) {
      verticalList.add(xOffset);
      xOffset += mm2Px;
    }

    verticalList.sort((a, b) => a.compareTo(b));
    return verticalList;
  }

  @override
  List<double> getGridHorizontalList() {
    double offsetYMax = state.imageHeight;

    List<double> horizontalList = [];
    double yCenter = offsetYMax / 2;
    double yOffset = yCenter;
    int mmValue = state.gridType == EmbGridType.embGridGridLine10
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    int mm2Px = (mmValue / _ratio * state.scale).round();

    /// 中心点上端の線の位置を計算するには
    yOffset -= mm2Px;
    while (yOffset >= 0) {
      horizontalList.add(yOffset);
      yOffset -= mm2Px;
    }

    /// 中心点の下端の線の位置を計算するには
    yOffset = yCenter;
    yOffset += mm2Px;
    while (yOffset <= offsetYMax) {
      horizontalList.add(yOffset);
      yOffset += mm2Px;
    }

    horizontalList.sort((a, b) => a.compareTo(b));
    return horizontalList;
  }

  ///
  /// 二重指拡張時のScale値変換
  /// 拡大時、scale範囲は1より大きく、scale値が大きいほど拡大する(1精度)
  /// 縮小時、scale範囲0 ~ 1、scale値が小さいほど縮小されます(0.1精度)
  ///
  int _getDoubleFingerStep(double scale) {
    int step = 0;
    if (scale > 1) {
      /// 例：Scale=1.2   ===>   Step = 1；
      /// 例：Scale=3.2   ===>   Step = 3；
      step = scale.toInt();
    } else if (scale >= 0 && scale < 1) {
      /// 例：Scale=0.8   ===>   Step = -2；
      /// 例：Scale=0.5   ===>   Step = -5；
      step = -((1 - scale) * 10).toInt();
    } else {
      step = 0;
    }

    return step;
  }

  ///
  /// スイッチの倍率に合わせて座標値を調整する
  ///
  void _scrollControllerAdjust(double preScale) {
    /// 表示倍率100%
    if (MagnificationModel.magnificationLevel ==
        MagnificationModel.magnification_100) {
      if (_scrollControllerX.hasClients) {
        _scrollControllerX.jumpTo(0);
        _scrollXInit = false;
      }
      if (_scrollControllerY.hasClients) {
        _scrollControllerY.jumpTo(0);
        _scrollYInit = false;
      }
      MagnificationModel().zoomEdit.zoomNowF = false;
    } else {
      if (MagnificationModel().zoomEdit.zoomNowF) {
        /// 移動情報設定
        if (_scrollControllerX.hasClients) {
          /// 横方向の最大移動量
          final maxExtent = _baseImageWidth * preScale - _canvasWidth;
          final double centerPos = maxExtent / 2;
          final double offset = _scrollControllerX.offset.ceilToDouble();

          MagnificationModel().zoomEdit
            ..preOffsetX = offset
            ..preCenterX = centerPos
            ..scrollOrgX = (offset - centerPos) / preScale;
        }

        if (_scrollControllerY.hasClients) {
          /// 縦方向の最大移動量
          final maxExtent = _baseImageHeight * preScale - _canvasHeight;
          final double centerPos = maxExtent / 2;
          final double offset = _scrollControllerY.offset.ceilToDouble();

          MagnificationModel().zoomEdit
            ..preOffsetY = offset
            ..preCenterY = centerPos
            ..scrollOrgY = (offset - centerPos) / preScale;
        }

        /// 画面表示座標計算
        final EditZoom zoom = MagnificationModel().zoomEdit;

        /// 横方向移動
        if (_scrollControllerX.hasClients) {
          /// ScrollBar移動位置
          double offsetX = 0;

          /// 横方向の最大移動量
          final maxExtent = _baseImageWidth * state.scale - _canvasWidth;

          /// 初期化
          if (_scrollXInit == false) {
            offsetX = maxExtent / 2;
            _scrollControllerX.jumpTo(offsetX);
            _scrollXInit = true;
          } else {
            /// 表示エリアの半分
            final double halfScreen = _canvasWidth / 2;

            /// 移動量の計算
            final centerPose = zoom.detailCTPos.dx;
            double offset = MagnificationModel().divideAbyBWithRoundUp(
                zoom.scrollOrgX * zoom.magnification.shi,
                zoom.magnification.bo);
            offsetX = centerPose + offset - halfScreen;

            //描画エリアの末端はどこなのか
            if (offsetX < 0) {
              offset = 0;
            }
            if (offsetX > maxExtent) {
              offset = maxExtent;
            }

            /// scrollBar移動位置設定
            _scrollControllerX.jumpTo(offsetX);
          }
        }

        /// 縦方向移動
        if (_scrollControllerY.hasClients) {
          /// 縦方向の最大移動量
          final maxExtent = _baseImageHeight * state.scale - _canvasHeight;

          /// ScrollBar移動位置
          double offsetY = 0;

          /// 初期化
          if (_scrollYInit == false) {
            offsetY = maxExtent / 2;
            _scrollControllerY.jumpTo(offsetY);
            _scrollYInit = true;
          } else {
            /// 表示エリアの半分
            final double halfScreen = _canvasHeight / 2;

            /// 移動量の計算
            final centerPose = MagnificationModel().zoomEdit.detailCTPos.dy;
            double offset = MagnificationModel().divideAbyBWithRoundUp(
                zoom.scrollOrgY * zoom.magnification.shi,
                zoom.magnification.bo);
            offsetY = centerPose + offset - halfScreen;

            /// 描画エリアの末端はどこなのか
            if (offsetY < 0) {
              offset = 0;
            }
            if (offsetY > maxExtent) {
              offset = maxExtent;
            }

            /// scrollBar移動位置設定
            _scrollControllerY.jumpTo(offsetY);
          }
        }

        MagnificationModel().zoomEdit.zoomNowF = false;
      }
    }
  }

  @override
  void dispose() {
    super.dispose();
    _scrollControllerX.dispose();
    _scrollControllerY.dispose();
    CreationModel().killIsolate();
    updateAfterStitchCreation = null;
    updateAfterPropertyChange = null;
    gotoMDCDetailSetToMDCPaint = null;
    DrawRegionModel().reset();
    MagnificationModel().reset();
  }

  ///
  /// 選択した枠のプレビュー表示データを取得します
  ///
  Path _getFrameDrawPath(double scale) {
    List<FrameSizeAndArea>? frameSizeAndAreaList =
        getFrameDisplaySizeAndArea(DeviceInfoModel().frameType);
    assert(frameSizeAndAreaList != null, "対応するサイズのボックスが見つかりません");

    Path drawPath = Path();
    Path basePath = Path();

    if (frameSizeAndAreaList!.length > 1) {
      basePath = _get60x20FrameBasePath(scale,
          frameSizeAndAreaList: frameSizeAndAreaList);
    } else {
      MdcSize frameSize = MdcSize(
        width: frameSizeAndAreaList.first.width / _conversionRate,
        height: frameSizeAndAreaList.first.height / _conversionRate,
      );

      double width = frameSize.width / _ratio * scale;
      double height = frameSize.height / _ratio * scale;
      Offset widgetCenter = Offset(
        _baseImageWidth * scale / 2,
        _baseImageHeight * scale / 2,
      );

      Rect rect = Rect.fromCenter(
        center: Offset(
          widgetCenter.dx + state.centerOffset.dx,
          widgetCenter.dy + state.centerOffset.dy,
        ),
        width: width,
        height: height,
      );

      basePath.moveTo(rect.left.roundToDouble(), rect.top.roundToDouble());
      basePath.lineTo(rect.left.roundToDouble(), rect.bottom.roundToDouble());
      basePath.lineTo(rect.right.roundToDouble(), rect.bottom.roundToDouble());
      basePath.lineTo(rect.right.roundToDouble(), rect.top.roundToDouble());
      basePath.lineTo(rect.left.roundToDouble(), rect.top.roundToDouble());
    }

    /// フレームの破線の一段の長さ
    double dashWidth = _dashedLineLength;
    double dashSpace = _dashedLineSpace;

    /// 描画Pathを計算する
    double distance = 0.0;
    for (ui.PathMetric pathMetric in basePath.computeMetrics()) {
      while (distance < pathMetric.length) {
        drawPath.addPath(
          pathMetric.extractPath(distance, distance + dashWidth),
          Offset.zero,
        );
        distance += dashWidth;
        distance += dashSpace;
      }
    }
    return drawPath;
  }

  ///
  /// 60*20枠のプレビューにはデータが表示されま
  ///
  Path _get60x20FrameBasePath(double scale,
      {required List<FrameSizeAndArea> frameSizeAndAreaList}) {
    Path basePath = Path();

    /// List順：60*20 mm、50*30 mm、30*40 mm
    List<Rect> rectList = List.generate(frameSizeAndAreaList.length, (index) {
      MdcSize frameSize = MdcSize(
        width: frameSizeAndAreaList[index].width / _conversionRate,
        height: frameSizeAndAreaList[index].height / _conversionRate,
      );
      double width = frameSize.width / _ratio * scale;
      double height = frameSize.height / _ratio * scale;
      Offset widgetCenter = Offset(
        _baseImageWidth * scale / 2,
        _baseImageHeight * scale / 2,
      );

      Rect rect = Rect.fromCenter(
        center: Offset(
          widgetCenter.dx + state.centerOffset.dx,
          widgetCenter.dy + state.centerOffset.dy,
        ),
        width: width,
        height: height,
      );
      return Rect.fromLTWH(
          rect.left.roundToDouble(), rect.top.roundToDouble(), width, height);
    });

    /// 1番目と2番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*40 mm
    rectList.insert(
      1,
      Rect.fromPoints(
        Offset(rectList[1].left, rectList.first.top),
        Offset(rectList[1].right, rectList.first.bottom),
      ),
    );

    /// 2番目と3番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*30 mm、30*40 mm
    rectList.insert(
      3,
      Rect.fromPoints(
        Offset(rectList.last.left, rectList[2].top),
        Offset(rectList.last.right, rectList[2].bottom),
      ),
    );

    /// 60*20 mmの左上点から描画
    basePath.moveTo(rectList.first.left, rectList.first.top);

    /// Rectの左上点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].left, rectList[index].top);
    }

    /// Rectの右上点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].right, rectList[index].top);
    }

    /// Rectの右下点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].right, rectList[index].bottom);
    }

    /// Rectの左下点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].left, rectList[index].bottom);
    }

    /// 接続開始点
    basePath.lineTo(rectList.first.left, rectList.first.top);

    return basePath;
  }

  ///
  /// 調整座標
  ///
  Offset _adjustCoordinates(Offset offset) {
    Offset keyPos = _keyPosToView(offset);
    int x = keyPos.dx.toInt();
    int y = keyPos.dy.toInt();

    if (state.scale == _scaleList[0]) {
      /* 拡大 */
      double magRatio = _baseImageWidth / canvasWidth;
      double magRatioHalf = magRatio * state.scale;
      double valueX = offset.dx * magRatio + magRatioHalf;
      double valueY = offset.dy * magRatio + magRatioHalf;
      x = (valueX + (valueX >= 0 ? 0.5 : -0.5)).toInt();
      y = (valueY + (valueX >= 0 ? 0.5 : -0.5)).toInt();
    } else if (state.scale == _scaleList[1]) {
      /// 2倍の画面に移動座標取得
      x += scrollControllerX.offset.toInt();
      y += scrollControllerY.offset.toInt();

      /// 1倍の座標変更する
      x = x ~/ state.scale;
      y = y ~/ state.scale;
    } else {
      int scale = state.scale.toInt();
      int xx = x.toInt();
      int yy = y.toInt();
      xx -= scale >> 1;
      yy -= scale >> 1;
      xx <<= 4;
      yy <<= 4;
      xx = xx ~/ scale + 8;
      yy = yy ~/ scale + 8;
      xx >>= 4;
      yy >>= 4;

      xx += scrollControllerX.offset ~/ state.scale;
      yy += scrollControllerY.offset ~/ state.scale;
      x = xx;
      y = yy;
    }
    return Offset(x.toDouble(), y.toDouble());
  }

  ///
  /// KEY関数座標からViewRect上の座標値に変換する
  ///
  Offset _keyPosToView(Offset keyPose) {
    /// KeyPostToView
    double keyPosX = keyPose.dx;
    double keyPosY = keyPose.dy;
    double viewWidth = PenModel.canvasWidth;
    double viewHeight = PenModel.canvasHeight;

    if (MagnificationModel.magnificationLevel >
        MagnificationModel.magnification_100) {
      viewWidth -= _scrollControllerYWidth;
      viewHeight -= _scrollControllerXHeight;
    }

    keyPosX -= 1;
    keyPosX = max(0, min(keyPosX, viewWidth - 1));
    keyPosY = max(0, min(keyPosY, viewHeight - 1));

    return Offset(keyPosX, keyPosY);
  }

  ///
  /// パラメータ設定後の画面更新
  ///
  void _updateAfterPropertyChange(bool showRawData) {
    update();
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updatePageByChild(ComponentType.viewArea);
  }
}
