import 'dart:typed_data';
import 'dart:ui';

import '../../../../panel_library.dart';
import '../../../library_manage_meta/device_library_result.dart';
import '../../../library_manage_meta/library_manage_meta.dart';

abstract class DeviceLibraryAPIInterface
    with _ExtensionFunction, _PreMoveFunction, DeviceCustomExtensionFunction {
  ///
  /// スピードコントロールレバーを使用してWIDTH調節を行う。
  ///
  /// [value] : true(ON)/false(OFF)
  ///
  @DeviceApiResult(
    DeviceApiFunction.widthControl,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setWidthControl(bool value);
  @DeviceApiResult(
    DeviceApiFunction.widthControl,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getWidthControl();
  @DeviceApiResult(
    DeviceApiFunction.widthControl,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, bool value}) getWidthControlDefault();

  ///
  /// 送り調整（模様調整－縦方向）
  ///
  /// 文字・模様縫いの模様を調節する
  ///
  /// [value] : -9 ~ 9( 初期値:0,単位：１ )
  ///
  @DeviceApiResult(
    DeviceApiFunction.fineAdjustVerti,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setFineAdjustVerti(int value);
  @DeviceApiResult(
    DeviceApiFunction.fineAdjustVerti,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getFineAdjustVerti();
  @DeviceApiResult(
    DeviceApiFunction.fineAdjustVerti,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getFineAdjustVertiValueList();

  ///
  /// 横送り調整（模様調整－横方向）
  ///
  /// 文字・模様縫いの模様を調節する
  ///
  /// [value] : -9 ~ 9( 初期値:0,単位：１ )
  ///

  @DeviceApiResult(
    DeviceApiFunction.fineAdjustHoriz,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setFineAdjustHoriz(int value);
  @DeviceApiResult(
    DeviceApiFunction.fineAdjustHoriz,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getFineAdjustHoriz();
  @DeviceApiResult(
    DeviceApiFunction.fineAdjustHoriz,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValue value})
      getFineAdjustHorizValueList();

  ///
  /// 押え高さ調整
  ///
  /// 押えが上がった状態の押えの高さを調節する。
  ///
  /// [value] : PFPM_2_0mm_POSITION: 22*2
  ///           PFPM_3_2mm_POSITION: 36*2
  ///           PFPM_5mm_POSITION: 56*2  初期値
  ///           PFPM_7_5mm_POSITION: 84*2
  ///
  @DeviceApiResult(
    DeviceApiFunction.presserFootHeight,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setPresserFootHeight(int value);

  @DeviceApiResult(
    DeviceApiFunction.presserFootHeight,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getPresserFootHeight();

  @DeviceApiResult(
    DeviceApiFunction.presserFootHeight,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getPresserFootHeightValueList();

  ///
  /// SR押え高さを設定
  ///
  @DeviceApiResult(
    DeviceApiFunction.presserFootHeight,
    ApiStatus.ngLib,
    """
APIはまだ提供されていません
""",
  )
  DeviceErrorCode setSrPresserFootHeight(int height);
  @DeviceApiResult(
    DeviceApiFunction.presserFootHeight,
    ApiStatus.ngLib,
    """
APIはまだ提供されていません
""",
  )
  ({DeviceErrorCode errorCode, int value}) getSrPresserFootHeight();

  ///
  /// 押え圧力
  ///
  /// 押えの圧力を調節する。
  ///
  /// ＊DFモジュールが接続され、かつローラーが下の場合は、押え圧力の設定は2で固定となる。
  /// バージョンアップにて、＋―キーがないので、固定だとわかる手段を準備する予定。。
  ///
  /// [value] : PFPM_PRESSURE_0_5_POSITION: -12*2
  ///           PFPM_PRESSURE_1_2_POSITION: -44*2
  ///           PFPM_PRESSURE_1_6_POSITION: -72*2
  ///           PFPM_PRESSURE_2_0_POSITION: -104*2
  ///
  @DeviceApiResult(
    DeviceApiFunction.presserFootPressure,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setPresserFootPressure(int value);

  @DeviceApiResult(
    DeviceApiFunction.presserFootPressure,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getPresserFootPressure();

  @DeviceApiResult(
    DeviceApiFunction.presserFootHeight,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getPresserFootPressureValueList();

  ///
  /// センサーシステム（自動押え圧補整）
  ///
  /// ONにすると、センサーが布地の厚さを自動的に読み取って、スムーズに布送りする。
  ///
  /// [value] : true:センサーフット有効,false: センサーフット無効( 初期値:センサーフット無効 )
  ///
  ///
  @DeviceApiResult(
    DeviceApiFunction.automaticFabric,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setAutomaticFabricSensorSystem(bool value);
  @DeviceApiResult(
    DeviceApiFunction.automaticFabric,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getAutomaticFabricSensorSystem();
  @DeviceApiResult(
    DeviceApiFunction.automaticFabric,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, bool value})
      getAutomaticFabricSensorSystemDefault();

  ///
  /// 初期針位置（基線切り替え）
  ///
  /// 「1-01直線（左基線）」か「1-03直線（中基線）」かを選択する。
  ///
  /// ブラザー：「1-01直線（左基線）」、タコニー：「1-03直線（中基線）」
  ///
  /// 詳細は [BaseLineType] を参考してください
  ///
  ///
  @DeviceApiResult(
    DeviceApiFunction.initialPosition,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setBaseLine(BaseLineType value);
  @DeviceApiResult(
    DeviceApiFunction.initialPosition,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, BaseLineType value}) getBaseLine();
  @DeviceApiResult(
    DeviceApiFunction.initialPosition,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, BaseLineType value}) getBaseLineDefault();

  ///
  /// ピボット押え高さ
  ///
  /// ピボット機能を有効にした時の、縫製停止時の押えの高さを選択する。
  ///
  /// [value] : 0 ~ 3( 初期値:1)
  ///  2.0 ~ 7.5( 初期値:3.2 選択肢 2.0mm, 3.2mm, 5.0mm, 7.5mm)
  ///
  @DeviceApiResult(
    DeviceApiFunction.pivotingHeight,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setPivotingHeight(int value);
  @DeviceApiResult(
    DeviceApiFunction.pivotingHeight,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getPivotingHeight();

  ///
  /// ピボット押え高さリストを取得する
  /// {"2.0 mm", PFPM_2_0mm_POSITION}
  /// {"3.2 mm", PFPM_3_2mm_POSITION}
  /// {"5.0 mm", PFPM_5mm_POSITION}
  /// {"7.5 mm", PFPM_7_5mm_POSITION}
  /// defaultValue = PFPM_3_2mm_POSITION
  ///
  @DeviceApiResult(
    DeviceApiFunction.pivotingHeight,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getPivotingHeightValueList();

  ///
  /// フリーモーション押え高さ
  ///
  /// フリーモーションに設定した時の、押えの高さを変更する
  ///
  /// [value] : 0 ~7( 初期値:1)
  /// **(初期値:1.0 選択肢　0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.freeMotionFootHeight,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setFreeMotionFootHeight(int value);

  @DeviceApiResult(
    DeviceApiFunction.freeMotionFootHeight,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getFreeMotionFootHeight();

  @DeviceApiResult(
    DeviceApiFunction.freeMotionFootHeight,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getFreeMotionFootHeightValueList();

  ///
  /// デュアルフィード送り調整
  ///
  /// デュアルフィードの送り量を調整する
  ///
  /// [value] : -10 ~ 10( 初期値:0，単位：1)
  ///
  @DeviceApiResult(
    DeviceApiFunction.dualFeedFeedAdjustment,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setDualFeedFeedAdjustment(int value);
  @DeviceApiResult(
    DeviceApiFunction.dualFeedFeedAdjustment,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getDualFeedFeedAdjustment();
  @DeviceApiResult(
    DeviceApiFunction.dualFeedFeedAdjustment,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValue value})
      getDualFeedFeedAdjustmentValueList();

  ///
  /// 自動下げの値を設定する
  ///
  /// [value] : true(ON)/false(OFF)
  ///
  @DeviceApiResult(
    DeviceApiFunction.autoPresserFootLift,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setUtlAutoDown(bool value);

  ///
  /// 自動下げの値を取得する
  ///
  /// [value] : true(ON)/false(OFF)
  ///
  @DeviceApiResult(
    DeviceApiFunction.autoPresserFootLift,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getUtlAutoDown();
  @DeviceApiResult(
    DeviceApiFunction.autoPresserFootLift,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, bool value}) getUtlAutoDownDefault();

  ///
  /// Automatic PF Trim 設定
  ///
  /// - 自動下げ
  /// - 糸切り時
  /// - 縫製開始時の自動押え下げ
  /// - 糸切り前後の押え下げ上げ
  ///
  /// **停止時の自動押え上げは、実用のメイン画面に出す。**
  ///
  /// [value] : true(ON)/false(OFF)
  ///
  /// 従来のOFF状態を初期値とする（安全面を考慮するためQMからの指示）
  ///
  @DeviceApiResult(
    DeviceApiFunction.autoPresserFootLift,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setPressToTrim(bool value);

  ///
  /// Automatic PF Trim 状態を取得する
  /// [value] : true(ON)/false(OFF)
  ///
  @DeviceApiResult(
    DeviceApiFunction.autoPresserFootLift,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getPressToTrim();
  @DeviceApiResult(
    DeviceApiFunction.autoPresserFootLift,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, bool value}) getPressToTrimDefault();

  ///
  /// 実用の起動画面
  ///
  /// 実用のデフォルトカテゴリーを実用カテゴリーにするかキルトカテゴリーにするか設定する。
  ///
  /// [value] : true(キルトＯＦＦ)/false(キルトＯＮ)
  /// **(初期値:[getInitialStitchPageDefault])**
  ///
  @DeviceApiResult(
    DeviceApiFunction.initialStitchPage,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setInitialStitchPage(bool value);

  ///
  /// 実用の起動画面を取得する
  ///
  /// 実用のデフォルトカテゴリーを実用カテゴリーにするかキルトカテゴリーにするか設定する。
  ///
  /// [value] : true(キルトＯＦＦ)/false(キルトＯＮ)
  /// **(初期値:[getInitialStitchPageDefault])**
  ///
  @DeviceApiResult(
    DeviceApiFunction.initialStitchPage,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getInitialStitchPage();

  @DeviceApiResult(
    DeviceApiFunction.initialStitchPage,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, bool value}) getInitialStitchPageDefault();

  ///
  ///
  /// 止め縫い優先
  ///
  /// 止め縫い模様は、止め縫いキーでなく返し縫キーをおしても止め縫いする。
  ///
  /// [value] : true(止め縫い優先)/false
  /// **(初期値:[getReinforcementPriorityDefault])**
  ///
  DeviceErrorCode setReinforcementPriority(bool value);
  ({DeviceErrorCode errorCode, bool value}) getReinforcementPriority();
  @DeviceApiResult(
    DeviceApiFunction.reinforcementPriority,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, bool value}) getReinforcementPriorityDefault();

  ///
  /// マルチファンクションフットコントローラー
  ///
  /// メインフットコントローラーのヒールスイッチ機能を設定する
  ///
  /// [value] : 詳細は [HeelSwitch] を参考してください
  /// **(初期値:[getHeelSwitchValueList])**
  ///
  @DeviceApiResult(
    DeviceApiFunction.multiFunctionFootController,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setHeelSwitch(HeelSwitch value);
  @DeviceApiResult(
    DeviceApiFunction.multiFunctionFootController,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, HeelSwitch value}) getHeelSwitch();
  @DeviceApiResult(
    DeviceApiFunction.multiFunctionFootController,
    ApiStatus.ngLib,
    """
1. 文言とXP画面に表示される不一致します
    違い:Needle Position - Up/Down
    正しい:Needle Position Up/Down
""",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<HeelSwitch> valueList})
      getHeelSwitchValueList();

  ///
  /// マルチファンクションフットコントローラー
  ///
  /// メインフットコントローラーのサイドペダル機能を設定する
  ///
  /// [value] : 詳細は [SidePedal] を参考してください
  /// **(初期値:[getSidePedalValueList])**
  ///
  /// ※Heel switchと同一設定が可能。排他は行わない
  ///
  @DeviceApiResult(
    DeviceApiFunction.multiFunctionFootController,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setSidePedal(SidePedal value);
  @DeviceApiResult(
    DeviceApiFunction.multiFunctionFootController,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, SidePedal value}) getSidePedal();
  @DeviceApiResult(
    DeviceApiFunction.multiFunctionFootController,
    ApiStatus.ngLib,
    """
1. 文言とXP画面に表示される不一致します
    違い:Needle Position - Up/Down
    正しい:Needle Position Up/Down
""",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<SidePedal> valueList})
      getSidePedalValueList();

  ///
  /// 終点設定直前停止
  ///
  /// 停止位置の真上に、終点シールを貼っている場合、縫製終了の直前で一旦停止をして、
  /// 剥がしてもらうための設定。
  ///
  /// [value] : true(ON)/false(OFF)
  /// **(初期値:[getEndPointSettingTemporaryStopDefault])**
  ///
  /// ONのときに、一旦停止する
  ///
  @DeviceApiResult(
    DeviceApiFunction.endPointSettingTemporaryStop,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setEndPointSettingTemporaryStop(bool value);
  @DeviceApiResult(
    DeviceApiFunction.endPointSettingTemporaryStop,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getEndPointSettingTemporaryStop();
  @DeviceApiResult(
    DeviceApiFunction.endPointSettingTemporaryStop,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, bool value})
      getEndPointSettingTemporaryStopDefault();

  ///
  /// 言語選択
  ///
  /// 言語を切り替える。
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  /// [language] : 詳細は [Language] を参考してください **(初期値:English)**
  ///
  /// 出荷時ラインにて、Eスクエアに、仕向け別に言語を指定する。
  /// （設定画面で言語を切り替えたのと同様の状態となる。例）
  /// XVではロシア仕向けにはロシア語指定）
  ///
  /// DeviceErrorCodeについては実際の状態に応じて処理しなくてもよい
  ///
  @DeviceApiResult(DeviceApiFunction.language, ApiStatus.ok, "")
  DeviceErrorCode saveLanguage(Language language);

  @DeviceApiResult(
    DeviceApiFunction.language,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, Language value}) getLanguage();

  ///
  /// 照明
  ///
  /// ライトの明るさ調整
  ///
  /// [value] : 0 ~ 5
  /// **(初期値:5、0の時はOFFです)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.light,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setLight(int level);
  @DeviceApiResult(
    DeviceApiFunction.light,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getLight();

  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getLightValueList();

  ///
  /// 画面調整
  ///
  /// 液晶画面の明るさを調整する
  ///
  /// [level] : 1 ~ 6
  /// **(初期値:4、単位：1、1～6の6段階で調整可能。数字が大きいほど明るくなる)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.screenDisplayBrightness,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setScreenDisplayBrightness(ScreenBrightnessLevel level);

  @DeviceApiResult(
    DeviceApiFunction.screenDisplayBrightness,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, ScreenBrightnessLevel value})
      getScreenDisplayBrightness();
  @DeviceApiResult(
    DeviceApiFunction.screenDisplayBrightness,
    ApiStatus.ngLib,
    """
    戻りリストが仕様と一致しません
            {"1", 1},
            {"2", 2},
            {"3", 3},
            {"4", 4},
            {"5", 5},
            {"MAX", 6},   =>{"6", 6}を修正
    """,
  )
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<ScreenBrightnessLevel> valueList
  }) getScreenDisplayBrightnessValueList();

  ///
  /// 上糸/下糸センサー
  ///
  /// [isDisabled] : true(disable上糸/下糸センサー)/false
  ///
  @DeviceApiResult(
    DeviceApiFunction.upperAndBobbinThreadSensor,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setUpperAndBobbinThreadSensor(bool isDisabled);

  @DeviceApiResult(
    DeviceApiFunction.upperAndBobbinThreadSensor,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool isDisabled})
      getUpperAndBobbinThreadSensor();
  @DeviceApiResult(
    DeviceApiFunction.upperAndBobbinThreadSensor,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, bool isDisabled})
      getUpperAndBobbinThreadSensorDefault();

  ///
  /// ミシン音ボリューム
  ///
  /// ミシン音のスピーカーの音量を調整する。
  ///
  /// [value] : 0 ~ 5
  /// **(初期値:3、単位：1)**
  ///
  /// ※ バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.machineSpeakerVolume,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveMachineSpeakerVolume(int level);

  @DeviceApiResult(
    DeviceApiFunction.machineSpeakerVolume,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getMachineSpeakerVolume();

  ///
  /// 読み上げ音ボリューム
  ///
  /// 読み上げ音のスピーカーの音量を調整する。
  ///
  /// [value] : 0 ~ 5
  /// **(初期値:3、単位：1)**
  ///
  /// ※ バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.voiceVolume,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveVoiceVolume(int level);
  @DeviceApiResult(
    DeviceApiFunction.voiceVolume,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getVoiceVolume();

  ///
  /// SRブザー音ボリューム設定
  ///
  /// 追従不可のときに鳴らす
  ///
  /// [value] : 0 ~ 5(0:off)
  /// **(初期値:3、単位：1)**
  ///
  /// ※ 設定画面　他のボリュームと別項目
  ///
  @DeviceApiResult(
    DeviceApiFunction.srVolume,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveSRVolume(int level);
  @DeviceApiResult(
    DeviceApiFunction.srVolume,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getSRVolume();

  ///
  /// ３段階針上下
  ///
  /// 針上下スイッチ押下時の動作を変更する。
  ///
  /// [value] : true(ON 針上⇒針刺さる手前⇒針下⇒針上⇒のトグル)/false(OFF)
  /// **(初期値:OFF)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.needlePositionStitchPlacement,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setNeedlePositionStitchPlacement(bool value);
  @DeviceApiResult(
    DeviceApiFunction.needlePositionStitchPlacement,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getNeedlePositionStitchPlacement();
  @DeviceApiResult(
    DeviceApiFunction.needlePositionStitchPlacement,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, bool value})
      getNeedlePositionStitchPlacementDefault();

  ///
  /// Initial Screen（起動画面）
  ///
  /// 起動時の表示画面を選択する。
  ///
  /// [value] :選択肢は３つ 0-Opening Screen、1-Home Page、2-Sewing/Embroidery
  ///
  /// **(初期値:0)** 詳細は　[InitialScreenEnum]に参考ください
  /// ※ バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.initialScreen,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setInitialScreen(int initialScreenIndex);

  @DeviceApiResult(
    DeviceApiFunction.initialScreen,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getInitialScreen();

  ///
  /// 起動時の表示画面を選択するリスト
  /// ModelIsSewing:
  ///               |----{"Opening Screen", OM_MOVIE},
  ///               |----{"Sewing/Embroidery Screen", OM_AUTO}
  /// 他の場合:
  ///               |----{"Opening Screen", OM_MOVIE},
  ///               |----{"Home Page", OM_HOME},
  ///               |----{"Sewing/Embroidery Screen", OM_AUTO}
  ///
  /// *** 複数言語リストを返すことができませんので、このAPIを使用しないでdisplayを返します ***
  ///
  @DeviceApiResult(
    DeviceApiFunction.initialScreen,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getInitialScreenValueList();

  ///
  /// スリープ機能
  ///
  /// Eco Modeと
  ///
  /// [value] : 0(OFF) , 10 ~ 120(ON)
  /// **(初期値:OFF、単位：10min)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.ecoMode,
    ApiStatus.doLibImpUnit,
    "",
  )
  DeviceErrorCode saveEcoMode(int value);

  ///
  /// スリープ機能
  ///
  /// Eco Modeと
  ///
  /// [value] : 0(OFF) , 10 ~ 120(ON)
  /// **(初期値:OFF、単位：10min)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.ecoMode,
    ApiStatus.ok,
    """
""",
  )
  ({DeviceErrorCode errorCode, int value}) getEcoMode();
  @DeviceApiResult(
    DeviceApiFunction.ecoMode,
    ApiStatus.doLibImpUnit,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getEcoModeValueList();

  ///
  /// スリープ機能
  ///
  /// Shutoff Support Modeに入る時間を設定する。
  ///
  /// [value] : 0(OFF) , 1~12(ON)
  /// **(初期値:OFF、単位：1hour)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.shutoffSupportMode,
    ApiStatus.ok,
    """
""",
  )
  DeviceErrorCode saveShutoffSupportMode(int value);

  ///
  /// スリープ機能
  ///
  /// Shutoff Support Modeに入る時間を設定する。
  ///
  /// [value] : 0(OFF) , 1~12(ON)
  /// **(初期値:OFF、単位：1hour)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.shutoffSupportMode,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getShutoffSupportMode();

  ///
  /// スリープ機能
  ///
  /// Shutoff Support Modeに入る時間を設定する。
  ///
  /// [value] : 0(OFF) , 1~12(ON)
  ///
  @DeviceApiResult(
    DeviceApiFunction.shutoffSupportMode,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getShutoffSupportModeValueList();

  ///
  /// スクリーンセーバー画面を起動起動後どの位置の画像を使用するか
  ///
  /// [value] : 0(デフォルト) , 1(カスタム)
  /// **(初期値:0)**
  ///
  ///
  @DeviceApiResult(
    DeviceApiFunction.screenSaver,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveSettingsScreenSaverType(ScreenSaverType value);

  ///
  /// スクリーンセーバー画面を起動起動後どの位置の画像を使用するか
  ///
  /// [value] :
  /// **(初期値:0)**
  ///
  ///
  @DeviceApiResult(
    DeviceApiFunction.screenSaver,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, ScreenSaverType value})
      getSettingsScreenSaverType();

  ///
  /// マウスポインター
  ///
  /// USBマウスが接続された時の表示形状を３種類から選択できる。
  ///
  /// [value] : 0-2
  /// **(初期値:0、単位：1min)**
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.mousePointer,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveMousePointer(int type);

  ///
  /// マウスポインター
  ///
  /// USBマウスが接続された時の表示形状を３種類から選択できる。
  ///
  /// [value] : 0-2
  /// **(初期値:0、単位：1min)**
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.mousePointer,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getMousePointer();

  ///
  /// プロジェクタの明るさを設定する / 取得する / リストを取得する
  ///
  /// OUT :
  /// - [DeviceError] : エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorBrightness,
    ApiStatus.notUse,
    "",
  )
  DeviceErrorCode setProjectorBrightnessMinus();

  @DeviceApiResult(
    DeviceApiFunction.projectorBrightness,
    ApiStatus.notUse,
    "",
  )
  DeviceErrorCode setProjectorBrightnessPlus();

  ///
  /// プロジェクタの明るさを取得する
  ///
  /// 引数 :
  /// - [int] value: プロジェクタの明るさ(範囲:1-4)
  /// - [DeviceError] : エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorBrightness,
    ApiStatus.notUse,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getProjectorBrightness();

  ///
  /// プロジェクタの明るさをリストを取得する
  ///
  /// 引数 :
  /// - [int] value: プロジェクタの明るさ(範囲:1-4)
  /// - [DeviceError] : エラーコード
  /// - [UserSettingItemValueList] : ユーザー設定項目の値
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorBrightness,
    ApiStatus.notUse,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getProjectorBrightnessValueList();

  ///
  /// プロジェクターの背景色の値を設定する
  ///
  /// 引数 :
  /// - [DeviceProjectorColor] color: プロジェクターの背景色
  /// - [DeviceError] : エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorBackgroundColor,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setProjectorBackbroundColor(DeviceProjectorColor color);

  ///
  /// プロジェクターの背景色の値を取得する
  ///
  /// 引数 :
  /// - [DeviceProjectorColor] color: プロジェクターの背景色
  /// - [DeviceError] : エラーコード
  /// - [UserSettingItemValueList] : ユーザー設定項目の値
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorBackgroundColor,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, DeviceProjectorColor projectorColor})
      getProjectorBackbroundColor();

  @DeviceApiResult(
    DeviceApiFunction.projectorBackgroundColor,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getProjectorBackbroundColorValueList();

  ///
  /// プロジェクターのアウトライン設定する
  ///
  /// 引数 :
  /// - [ProjectorPatternOutlineState] outline: プロジェクターのアウトライン設定
  /// - [DeviceError] : エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorPatternOutline,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setProjectorPatternOutline(
      ProjectorPatternOutlineState outline);

  ///
  /// プロジェクターのアウトラインを取得する
  ///
  /// 引数 :
  /// - [ProjectorPatternOutlineState] outline: プロジェクターのアウトライン設定
  /// - [DeviceError] : エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorPatternOutline,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, ProjectorPatternOutlineState outlineState})
      getProjectorPatternOutline();

  ///
  /// プロジェクターのアウトラインのデフォルト値を取得する
  ///
  /// 引数 :
  /// - [ProjectorPatternOutlineState] outline: プロジェクターのアウトライン設定
  /// - [DeviceError] : エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorPatternOutline,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, ProjectorPatternOutlineState outlineState})
      getProjectorPatternOutlineDefault();

  ///
  /// プロジェクタのポインタの色を設定する
  ///
  /// 引数 :
  /// - [DeviceProjectorColor] color: ポインタの色
  /// - [DeviceError] : エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorPointerColor,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setPointerColor(DeviceProjectorColor color);

  ///
  /// プロジェクタのポインタの色を取得する
  ///
  /// 引数 :
  /// - [DeviceProjectorColor] color: ポインタの色
  /// - [DeviceError] : エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorPointerColor,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, DeviceProjectorColor projectorColor})
      getPointerColor();

  ///
  /// プロジェクタのポインタの色を設定する / 取得する / リストを取得する
  ///
  /// 引数 :
  /// - [DeviceProjectorColor] color: ポインタの色index
  /// - [DeviceError] : エラーコード
  /// - [UserSettingItemValueList] : ユーザー設定項目の値
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorPointerColor,
    ApiStatus.ok,
    """
この関数はUIに使用しなくても影響がない
""",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getPointerColorValueList();

  ///
  /// プロジェクタのポインタの形状を設定します / 取得する / リストを取得する
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorPointerSharpe,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setPointerSharpe(ProjectorPointerShapeType value);

  ///
  /// プロジェクタのポインタの形状を取得する
  ///
  /// - [pointerShapeType] : 詳細は[ProjectorPointerShapeType]に参考してください
  ///
  @DeviceApiResult(
    DeviceApiFunction.projectorPointerSharpe,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, ProjectorPointerShapeType pointerShapeType})
      getPointerSharpe();

  ///
  /// プロジェクタのポインタの形状リストを取得する
  //
  @DeviceApiResult(
    DeviceApiFunction.projectorPointerSharpe,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getPointerSharpeValueList();

  ///
  /// カメラ針落ち点設定
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraNeedlePositionSetting,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode startCalibrationAdjustment();

  ///
  /// カメラ針落ち点設定のKeyCameraCalibrationOK関数
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraNeedlePositionSetting,
    ApiStatus.ngLib,
    "doCalibrationAdjustment関数に問題があり、テストを続行できません",
  )
  DeviceErrorCode doneCalibrationAdjustment();

  ///
  /// カメラ針落ち点設定のKeyCameraCalibrationFast_Start関数
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraNeedlePositionSetting,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode doCalibrationAdjustment();

  ///
  /// カメラ針落ち点設定のretryCalibrationAdjustment関数
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraNeedlePositionSetting,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode retryCalibrationAdjustment();

  ///
  /// カメラ針落ち点設定のcancelCalibrationAdjustment関数
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraNeedlePositionSetting,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode cancelCalibrationAdjustment();

  ///
  /// カメラ針落ち点設定のgetCameraNDPMessage
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraNeedlePositionSetting,
    ApiStatus.ngLib,
    " [ERROR:flutter/runtime/dart_vm_initializer.cc(41)] Unhandled Exception: Invalid argument(s): Failed to lookup symbol 'getCameraNDPMessage': undefined symbol: getCameraNDPMessage",
  )
  CameraNDPMessage getCameraNDPMessage();

  ///
  /// プロジェクタのキャリブレーションを開始
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraProjector,
    ApiStatus.doLibImpUnit,
    "",
  )
  DeviceErrorCode startCalibrationProjector();

  ///
  /// sncアクティベーションコードの設定s
  ///
  /// kitIDはUgkType、certificKeyはサーバから取得した情報
  ///
  @DeviceApiResult(
    DeviceApiFunction.kit,
    ApiStatus.doUiImp,
    """
Artspiraに使う関数ので、今はまで検証ない
この関数の使い方分からないので、教えてもいいですか？ kitIDとcertifyKey関する「int」の意味""",
  )
  DeviceErrorCode setKitActivationCode(int kitID, int certifyKey);

  ///
  /// マシーン名を設定します
  ///
  @DeviceApiResult(
    DeviceApiFunction.machineName,
    ApiStatus.notUse,
    "APPに保存する",
  )
  DeviceErrorCode setMachineName(String value);

  ///
  /// マシーン名を取得する
  ///
  @DeviceApiResult(
    DeviceApiFunction.machineName,
    ApiStatus.notUse,
    "APPに保存する",
  )
  ({DeviceErrorCode errorCode, String machineName}) getMachineName();

  ///
  /// 刺繍縫製エリア表示設定
  ///
  /// 刺繍縫製エリアの表示を設定する。
  ///
  /// [frameDispType] : 詳細は [EmbFrameDispType]に参考してください
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFrameDisplay,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setEmbroideryFrameDisplay(EmbFrameDispType frameDispType);

  ///
  /// 刺繍縫製エリア表示設定
  ///
  /// 刺繍縫製エリアの表示を取得する。
  ///
  /// [frameDispType] : 詳細は [EmbFrameDispType]に参考してください
  ///
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFrameDisplay,
    ApiStatus.ok,
    "",
  )
  ({
    DeviceErrorCode errorCode,
    EmbFrameDispType frameDispType,
  }) getEmbroideryFrameDisplay();

  ///
  /// 刺繍縫製エリア表示設定
  ///
  /// 刺繍縫製エリアの表示リストを取得します。
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFrameDisplay,
    ApiStatus.ok,
    "",
  )
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<EmbFrameDispType> valueList
  }) getEmbroideryFrameDisplayValueLis();

  ///
  /// グリッド
  ///
  /// グリッドの種類を選択する
  ///
  /// [embGridDisplayType] : 詳細は [EmbGridType]に参考してください
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFrameDisplay,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveGrid(EmbGridType embGridDisplayType);

  ///
  /// グリッド
  ///
  /// グリッドの種類を取得する
  ///
  /// [embGridDisplayType] : 詳細は [EmbGridType]に参考してください
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFrameDisplay,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, EmbGridType gridType}) getGrid();

  ///
  /// 刺繍最高回転数を設定する
  ///
  /// 刺繍最高回転数List = [1050、900、800、700、600、500、400、350](default:350)
  ///
  /// [maxEmbroiderySpeed] : 上記刺繍最高回転数Listのindexです(例　0：1050)
  ///
  /// ★★★カウチングの場合とカウチング以外の場合は 別々別々のEEPromで保存します、APIは気にしないでもいいです。
  ///
  @DeviceApiResult(
    DeviceApiFunction.maxEmbroiderySpeed,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setMaxEmbroiderySpeed(int maxEmbroiderySpeed);

  ///
  /// 刺繍最高回転数を取得する
  ///
  /// 刺繍最高回転数List = [1050、900、800、700、600、500、400、350](default:350)
  ///
  /// [value] : 上記刺繍最高回転数Listのindexです(例　0：1050)
  /// XPの仕様としては：
  ///
  /// カウチングの場合　(default:350)
  ///
  /// カウチングの以外の場合
  ///     枠は FRAME_MAGNET_180_360 : (default:600,index:4)
  ///     枠は FRAME_MAGNET_130_180 : (default:900,index:1)
  ///     枠は FRAME_MAGNET_254_254 : (default:600,index:4)
  ///     枠は FRAME_MAGNET_180_300 : (default:800,index:2)
  ///     枠は 上記以外 : (default:1050,index:0)
  ///
  /// ★★★カウチングの以外の場合、速度制限のある枠については、制限したいリストをグレーアウト描画する
  ///     枠は FRAME_MAGNET_180_360 : [600、500、400、350]
  ///     枠は FRAME_MAGNET_130_180 : [900、800、700、600、500、400、350]
  ///     枠は FRAME_MAGNET_254_254 : [600、500、400、350]
  ///     枠は FRAME_MAGNET_180_300 : [800、700、600、500、400、350]
  ///     枠は 上記以外 : 速度制限がない
  /// ★★★速度制限のある枠の影響は libで実現しましたので、アプリそのまま取得すればいい
  ///
  @DeviceApiResult(
    DeviceApiFunction.maxEmbroiderySpeed,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getMaxEmbroiderySpeed();

  ///
  /// 刺繍最高回転数表示リストを取得します。
  ///
  /// 刺繍最高回転数List = [1050、900、800、700、600、500、400、350]
  ///
  /// [maxEmbroiderySpeed] : 上記刺繍最高回転数Listのindexです(例　0：1050)
  ///
  /// XPの仕様としては：
  ///
  /// カウチングの場合　(default:350)
  ///
  /// カウチングの以外の場合
  ///     枠は FRAME_MAGNET 180*360?: (default:350)
  ///     枠は FRAME_MAGNET_130_180 : (default:900)
  ///     枠は FRAME_MAGNET_254_254 : (default:600)
  ///     枠は FRAME_MAGNET_180_300 : (default:800)
  ///     枠は 上記以外 : (default:1050)
  ///
  ///
  /// [valueList] : ★★★戻るのvalueListはグレー以外のvalueListです、グレー表示したいなら刺繍最高回転数Listと一緒に確認します
  ///
  @DeviceApiResult(
    DeviceApiFunction.maxEmbroiderySpeed,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getMaxEmbroiderySpeedValueList();

  ///
  /// すべてのリスト値 = [1050、900、800、700、600、500、400、350]
  ///
  @DeviceApiResult(
    DeviceApiFunction.maxEmbroiderySpeed,
    ApiStatus.doTest,
    """
    Libにはまだ提供されていません
    UI側が実装されている
    """,
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getMaxEmbroiderySpeedValueAllList();

  ///
  /// 刺繍糸調子設定
  ///
  /// 刺繍の押えの高さを設定する。
  ///
  /// [embroideryTension] : -8 ~ 8
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryTension,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setEmbroideryTension(int embroideryTension);

  ///
  /// 刺繍糸調子設定
  ///
  /// 刺繍の押えの高さを取得する。
  ///
  /// [embroideryTension] : -8 ~ 8
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryTension,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getEmbroideryTension();

  ///
  /// 刺繍糸調子設定
  ///
  /// 刺繍の押えの高さ表示リストを取得します。
  ///
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryTension,
    ApiStatus.ngLib,
    """
    UI側が実装されている
    [embroideryTension] : -8 ~ 8
    戻り値が 0 未満の場合、戻り値の順序は正しくありません
    現在返される順序は次のとおりです:[0,-1,-2,-3,-4,-5,-7,-6,-8]
    """,
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getEmbroideryTensionValueList();

  ///
  /// 刺繍の押え高さ
  ///
  /// 刺繍の押えの高さを設定する。
  ///
  /// [embroideryFootHeight] :
  /// Couching模様選択すると 0~50(step:5,unit:mm*10,default:25)
  /// LongStitch模様選択すると 15~50(step:5,unit:mm*10,default:15)
  /// それ以外の場合は 0~75(step:5,unit:mm*10,default:15)
  ///
  /// ★★★もし指定以外のデータを設定すると[DeviceErrorCode.devInvalidError]に戻る
  ///
  /// ★★★三つ場合のデータは独立保存です、取得時に別々別々のデータを取得です、UIが気にしないでいいです。
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFootHeight,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setEmbroideryFootHeight(int embroideryFootHeight);

  ///
  /// 刺繍の押え高さ
  ///
  /// 刺繍の押えの高さを取得します
  ///
  /// [embroideryFootHeight] :
  /// Couching模様選択すると 0~50(step:5,unit:mm*10,default:25)
  /// LongStitch模様選択すると 15~50(step:5,unit:mm*10,default:15)
  /// それ以外の場合は 0~75(step:5,unit:mm*10,default:15)
  ///
  /// ★★★三つ場合のデータは独立保存です、取得時に別々別々のデータを取得です、UIが気にしないでいいです。
  ///
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFootHeight,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getEmbroideryFootHeight();

  ///
  /// 刺繍の押え高さ
  ///
  /// 刺繍の押えの高さ表示リストを取得します。
  ///
  ///
  /// [embroideryFootHeight] :
  /// Couching模様選択すると 0~50(step:5,unit:mm*10,default:25)
  /// LongStitch模様選択すると 15~50(step:5,unit:mm*10,default:15)
  /// それ以外の場合は 0~75(step:5,unit:mm*10,default:15)
  ///
  ///  0.0 ~ 7.5**(初期値:1.5、単位：0.5mm)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFootHeight,
    ApiStatus.ngLib,
    """
    UI側が実装されている
    ＸＰ仕様としては、以下ような仕様があります：
    Couting模様選択すると 0~50(step:5,unit:mm*10,default:25)
    LongStitch模様選択すると 15~50(step:5,unit:mm*10,default:15)
    それ以外の場合は 0~75(step:5,unit:mm*10,default:15)

    IIVOの仕様書以上ような仕様特に記載されていないです。(特に記載がないならＸＰと同じ)

    今は 以下ようなテスト結果があります,仕様と違います。
    Couting模様選択すると: 0~75(step:5,unit:mm*10,default:15)
    LongStitch模様選択すると 15~75(step:5,unit:mm*10,default:15)
    それ以外の場合は 0~75(step:5,unit:mm*10,default:15)
    """,
  )
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getEmbroideryFootHeightValueList();

  ///
  /// 刺繍時針停止位置（上下）
  ///
  /// ミシンが停止した時の針位置（下／上）を選択する。
  ///
  /// 設定値を設定します
  ///
  /// [embroideryNeedleStopPosition] : true:上/false
  /// **(初期値:針下)**
  ///
  /// 停止時の針の位置を指定する。
  /// ＊XVまでは、Needle Position UP/DOWNの設定が、実用・刺繍ともに効いていたが、
  /// XPからは、刺繍のときに限定（実用はメイン画面で設定するため）
  /// 選択肢　針下、針上
  ///

  @DeviceApiResult(
    DeviceApiFunction.embroideryNeedleStopPosition,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setEmbroideryNeedleStopPosition(
      bool embroideryNeedleStopPosition);

  ///
  /// 刺繍時針停止位置（上下）
  ///
  /// ミシンが停止した時の針位置（下／上）を選択する。
  ///
  /// 設定値を設定します
  ///
  /// [embroideryNeedleStopPosition] : true:上/false
  /// **(初期値:針下)**
  ///
  /// 停止時の針の位置を指定する。
  /// ＊XVまでは、Needle Position UP/DOWNの設定が、実用・刺繍ともに効いていたが、
  /// XPからは、刺繍のときに限定（実用はメイン画面で設定するため）
  /// 選択肢　針下、針上
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryNeedleStopPosition,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getEmbroideryNeedleStopPosition();

  ///
  /// 刺繍時針停止位置（上下）
  ///
  /// ミシンが停止した時の針位置（下／上）を選択する。
  ///
  /// デフォルト設定値を取得します
  ///
  /// [embroideryNeedleStopPosition] : true:上/false
  /// **(初期値:針下)**
  ///
  /// 停止時の針の位置を指定する。
  /// ＊XVまでは、Needle Position UP/DOWNの設定が、実用・刺繍ともに効いていたが、
  /// XPからは、刺繍のときに限定（実用はメイン画面で設定するため）
  /// 選択肢　針下、針上
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryNeedleStopPosition,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  @Deprecated("廃棄関数、削除つもりです")
  ({DeviceErrorCode errorCode, bool value})
      getEmbroideryNeedleStopPositionDefault();

  ///
  /// 刺繍の自動押え下げが設定する
  ///
  /// 設定値を設定します
  ///
  /// [embroideryFootAutoDown] : true:有効(機能NO)/false
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFootAutoDown,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setEmbAutoDown(bool embroideryFootAutoDown);

  ///
  /// 刺繍の自動押え下げが設定する
  ///
  /// 設定値を取得します
  ///
  /// [embroideryFootAutoDown] : true:有効(機能NO)/false
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFootAutoDown,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getEmbAutoDown();

  ///
  /// 刺繍の自動押え下げが設定する
  ///
  /// デフォルト設定値を取得します
  ///
  /// [embroideryFootAutoDown] : true:有効(機能NO)/false
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFootAutoDown,
    ApiStatus.notUse,
    "この関数は使用しなくても仕様と機能影響がない",
  )
  ({DeviceErrorCode errorCode, bool value}) getEmbAutoDownDefault();

  ///
  /// インチ/ミリ切り替え
  ///
  /// 設定値を設定します
  ///
  /// [unit] : 詳細は [DisplayUnit]に参考してください
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.displayUnit,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveDisplayUnit(DisplayUnit unit);

  ///
  /// インチ/ミリ切り替え
  ///
  /// 設定値を取得します
  ///
  /// [unit] : 詳細は [DisplayUnit]に参考してください
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.displayUnit,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, DisplayUnit displayUnit}) getDisplayUnit();

  ///
  /// 糸色表示設定を設定する
  ///
  /// 糸番号表示か、糸色名表示か選択する。
  ///
  /// [value] : true:糸番号/false:糸色名
  ///
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.threadColor,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveThreadColor(bool value);

  ///
  /// 糸色表示設定を取得する
  ///
  /// 糸番号表示か、糸色名表示か選択する。
  ///
  /// [value] : true:糸番号/false:糸色名
  ///
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.threadColor,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getThreadColor();

  ///
  /// 糸ブランド
  ///
  /// ブランドを指定する。
  /// また色替え画面の糸色ブランドの初期値に反映する。
  ///
  ///  [value] :
  ///
  /// 初期値: Original
  /// - 内蔵模様の時、Originalは：
  ///   - ブラザー：Embroidery
  ///   - タコニー：Madeira Poly
  /// - 外部模様の時、Originalは：PESが持つ独自の色を使用する
  ///
  ///
  ///
  @DeviceApiResult(
    DeviceApiFunction.threadBrand,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setThreadBrand(int value);

  ///
  /// ブランドを取得する。
  /// value：getThreadBrandValueListから取得した値
  ///
  @DeviceApiResult(
    DeviceApiFunction.threadBrand,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getThreadBrand();

  ///
  /// ブランド表示リストを取得する。
  /// 取得された値はSettingThreadBrandTblのindexのみです
  /// value：
  /// {"Original", 0},		// 200
  /// {"Embroidery", 1},		// 13
  /// {"Country", 2},			// 12
  /// {"Madeira Poly", 3},	//  4
  /// {"Madeira Rayon", 4},	//  5
  /// {"Sulky", 5},			//  8
  /// {"Robison-Anton\nPoly", 6},		//  6
  /// {"Robison-Anton\nRayon", 7},		//  7
  /// {"Gütermann Deko", 9},		//  2
  /// {"Isacord", 8},			//  3
  /// {"Iris", 12},			// 18
  /// {"Floriani", 13},	// 19
  /// {"Pacesetter Pro", 10}, // 16
  /// {"Polyfast", 11},		// 17
  ///
  /// *** 複数言語リストを返すことができませんので、このAPIを使用しないでdisplayを返します ***
  ///
  @DeviceApiResult(
    DeviceApiFunction.threadBrand,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getThreadBrandValueList();

  ///
  /// 背景色
  ///
  /// 刺繍イメージ表示エリア　背景色変更
  ///
  /// [value] :　パレットから選択,SettingBKGroundColorTableのindex値
  /// **(初期値:白)**
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryBackgroundColor,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveEmbroideryBackgroundColor(int value);

  ///
  /// 刺繍イメージ表示エリア　背景色取得
  ///
  /// [errorCode] :　未使用、処理不要
  /// [value] :　SettingBKGroundColorTableでcolorのindex値
  /// **(初期値:白)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryBackgroundColor,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getEmbroideryBackgroundColor();

  ///
  /// サムネイル色
  ///
  /// サムネイル背景色変更
  ///
  /// [value] :　[settingColorsTable] のindex値
  /// **(初期値:白)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.thumbnailBackgroundColor,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveThumbnailBackgroundColor(int value);
  @DeviceApiResult(
    DeviceApiFunction.thumbnailBackgroundColor,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getThumbnailBackgroundColor();

  ///
  /// サムネイルサイズ設定
  ///
  /// 矢印キーを押して、大/中/小を選択する。
  ///
  /// [value] :　選択肢は３つ。大(2)/中(1)/小(0)
  /// **(初期値:中)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.thumbnailSize,
    ApiStatus.doLibImpUnit,
    """
    実際のマシンが検証されるのを待ちます
    """,
  )
  DeviceErrorCode saveThumbnailSize(ThumbnailSizeType value);

  ///
  /// サムネイルサイズ取得
  ///
  /// 矢印キーを押して、大/中/小を選択する。
  ///
  /// [value] :　選択肢は３つ。大(2)/中(1)/小(0)
  ///
  @DeviceApiResult(
    DeviceApiFunction.thumbnailSize,
    ApiStatus.doLibImpUnit,
    """
    実際のマシンが検証されるのを待ちます
    """,
  )
  ({DeviceErrorCode errorCode, ThumbnailSizeType value}) getThumbnailSize();

  ///
  /// サムネイルサイズ表示リストを取得
  ///
  @DeviceApiResult(
    DeviceApiFunction.thumbnailSize,
    ApiStatus.ok,
    "",
  )
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<ThumbnailSizeType> valueList
  }) getThumbnailSizeLis();

  ///
  /// 刺繍しつけ距離
  ///
  /// 刺繍しつけの大きさを模様に対して調整する。
  ///
  /// [value] : 0~120
  /// 0.0 ~ 12.0**(初期値:5.0、単位：0.5mm)**
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryBastingDistance,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setEmbroideryBastingDistance(int value);

  ///
  /// 刺繍しつけ距離を取得
  ///
  /// 刺繍しつけの大きさを模様に対して調整する。
  ///
  /// [value] : 0~120
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryBastingDistance,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getEmbroideryBastingDistance();

  ///
  /// 刺繍しつけ距離を取得
  ///
  /// 刺繍しつけの大きさを模様に対して調整する。
  ///
  /// [value] :
  /// minValue = 0;	      // 0mm
  /// maxValue = 120;	    // 12.0mm
  /// defaultValue = 5.0; // 5.0mm
  /// stepValue = 5;	    // 0.5mm
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryBastingDistance,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getEmbroideryBastingDistanceValueList();

  ///
  /// 背景画像表示
  ///
  /// スキャン品質
  ///
  /// [value] : 詳細は [ScanQualityType]に参考してください
  /// **(初期値:きれい[ScanQualityType.fine])**
  ///
  @DeviceApiResult(
    DeviceApiFunction.scanQuality,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setScanQuality(ScanQualityType value);

  ///
  /// スキャン品質を取得
  ///
  /// [value] : 詳細は [ScanQualityType]に参考してください
  /// **(初期値:きれい[ScanQualityType.fine])**
  ///
  @DeviceApiResult(
    DeviceApiFunction.scanQuality,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, ScanQualityType value}) getScanQuality();

  ///
  /// スキャン品質初期値を取得
  ///
  /// [value] : ScanQualityType.fine
  ///
  @DeviceApiResult(
    DeviceApiFunction.scanQuality,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, ScanQualityType value}) getScanQualityDefault();

  ///
  /// 布厚センサー
  ///
  /// 背景スキャンのときに、
  ///
  /// OFFならば、押えを使って布厚を測定する。
  ///
  /// [value] : true(ON)/false(OFF)
  /// **(初期値:OFF)**
  /// - ONならば、スノーマンマークを使って布厚を測定する。
  /// - OFFならば、スノーマンマークを使って布厚を測定ない。
  ///
  @DeviceApiResult(
    DeviceApiFunction.fabricThicknessSensor,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setFabricThicknessSensor(bool value);

  ///
  /// 布厚センサー機能の使用状態を取得
  /// [value] : true(ON)/false(OFF)
  /// - ONならば、スノーマンマークを使って布厚を測定する。
  /// - OFFならば、スノーマンマークを使って布厚を測定ない。
  ///
  ///
  @DeviceApiResult(
    DeviceApiFunction.fabricThicknessSensor,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getFabricThicknessSensor();

  ///
  /// 布厚センサー機能の初期値を取得
  ///
  /// [value] : false
  ///
  @DeviceApiResult(
    DeviceApiFunction.scanQuality,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, bool value}) getFabricThicknessSensorDefault();

  ///
  /// LEDポインター調整開始
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFootWithLEDPointerAdjustment,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode startLedPtSetting();

  ///
  /// LEDポインター調整停止
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFootWithLEDPointerAdjustment,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode closeLedPtSetting();

  ///
  /// LEDポインター調整
  ///
  @DeviceApiResult(
    DeviceApiFunction.embroideryFootWithLEDPointerAdjustment,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setLedPtHeightMinus();

  @DeviceApiResult(
    DeviceApiFunction.embroideryFootWithLEDPointerAdjustment,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setLedPtHeightPlus();

  @DeviceApiResult(
    DeviceApiFunction.embroideryFootWithLEDPointerAdjustment,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setLedBrightnessMinus();
  @DeviceApiResult(
    DeviceApiFunction.embroideryFootWithLEDPointerAdjustment,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setLedPtBrightnessPlus();

  @DeviceApiResult(
    DeviceApiFunction.embroideryFootWithLEDPointerAdjustment,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getLedPtHeightPlus();

  @DeviceApiResult(
    DeviceApiFunction.embroideryFootWithLEDPointerAdjustment,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getLedPtHeightPlusDefault();

  @DeviceApiResult(
    DeviceApiFunction.embroideryFootWithLEDPointerAdjustment,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getLedPtBrightnessPlus();

  @DeviceApiResult(
    DeviceApiFunction.embroideryFootWithLEDPointerAdjustment,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getLedPtBrightnessPlusDefault();

  ///
  /// 背景画像の削除、登録、登録状況 / 取得する
  ///
  @DeviceApiResult(
    DeviceApiFunction.backgroundImageDisplay,
    ApiStatus.doLibImp,
    "",
  )
  DeviceErrorCode deleteCompFrameScanBackImgView();

  @DeviceApiResult(
    DeviceApiFunction.backgroundImageDisplay,
    ApiStatus.notUse,
    "",
  )
  ({DeviceErrorCode errorCode, bool isOn}) getCompFrameScanBackImgView();

  ///
  /// 起動時に取得するデバイス情報
  /// [serviceCount] :int サービスカウント
  /// [totalCount] :int トータルカウント
  /// [productNo] : String プロダクトNo
  /// [version] : int バージョン
  /// [isKit[10]] : bool Kitの有効無効情報
  /// [spec] :
  ///        	SPEC_DISNEY_US = 0,	/*	ディズニーUS			*/
  ///        	SPEC_EXPORT_US = 1,	/*	ノンキャラUS			*/
  ///        	SPEC_EXPORT_EU = 2,	/*	ノンキャラ欧州			*/
  ///        	SPEC_DISNEY_CANADA = 3,	/*	ディズニーCANADA		*/
  ///        	SPEC_EXPORT_TACONY = 4,	/*	タコニー				*/
  ///        //	SPEC_EXPORT_ETC = 5,	/*	ノンキャラその他（中南米など）*/
  ///        //	SPEC_JAPANESE = 6,	/*  ノンキャラ国内			*/
  ///        //	SPEC_JAPANESE_DISNEY = 7,	/*	ディズニー国内			*/
  ///        //	SPEC_EXPORT_ASIA = 8,	/*	ノンキャラ タイ語、中文	*/
  ///        //	SPEC_EXPORT_CHN = 9,	/*	ノンキャラ 中文あり		*/
  ///        	SPEC_DISNEY_EU = 10,	/*	欧州ディズニー */
  ///        //	SPEC_EXPORT_CANADA = 11,	/*	ノンキャラCANADA		*/
  ///        //2012-10-12 ZZS620 VcombL VER104 M61 ins start
  ///        SPEC_DISNEY_CHN = 12,	/*	ディズニーCHINA */
  ///        //2012-10-12 ZZS620 VcombL VER104 M61 ins end
  ///        //2013-03-11 SSKS248 Vpro VER108 M-16 ins start
  ///        SPEC_JAPANESE_LA = 13,	/*	ローラアシュレイ国内		*/
  /// [ModelNo]: Limited：0x80,ノーマル：その他
  ///
  ///
  @DeviceApiResult(
    DeviceApiFunction.deviceSettingInfo,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, DeviceSettingInfo settingInfo})
      getDeviceSettingInfo();

  ///
  /// デバイスGeneral情報を取得する
  ///
  @DeviceApiResult(
    DeviceApiFunction.commonUserSettingInfo,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, CommonUserSettingInfo settingInfo})
      getCommonUserSettingInfo();

  ///
  /// デバイスEmb情報を取得する
  ///
  @DeviceApiResult(
    DeviceApiFunction.embUserSettingInfo,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, EmbUserSettingInfo settingInfo})
      getEmbUserSettingInfo();

  ///
  /// デバイスUtl情報を取得する
  ///
  @DeviceApiResult(
    DeviceApiFunction.utlUserSettingInfo,
    ApiStatus.notUse,
    "この関数はUIに使用しなくても影響がない",
  )
  ({DeviceErrorCode errorCode, UtlUserSettingInfo settingInfo})
      getUtlUserSettingInfo();

  ///
  /// デフォルト設定ポップアップのUtillityStitchリセットキー関数
  ///
  @DeviceApiResult(
    DeviceApiFunction.reset,
    ApiStatus.ok,
    "",
  )
  void resetUtlUserSetting();

  ///
  /// デフォルト設定ポップアップのGeneralリセットキー関数
  ///
  @DeviceApiResult(
    DeviceApiFunction.reset,
    ApiStatus.ok,
    "",
  )
  void resetCommonUserSetting();

  ///
  /// デフォルト設定ポップアップのEmbroideryリセットキー関数
  ///
  @DeviceApiResult(
    DeviceApiFunction.reset,
    ApiStatus.ok,
    "",
  )
  void resetEmbUserSetting();

  ///
  /// すべてのユーザー設定をリセットする
  ///
  @DeviceApiResult(
    DeviceApiFunction.reset,
    ApiStatus.doLibImpUnit,
    "",
  )
  void resetAllUserSetting();

  ///
  /// startAllUserSetting
  ///
  @DeviceApiResult(
    DeviceApiFunction.reset,
    ApiStatus.libMaintenance,
    "",
  )
  DeviceErrorCode startAllUserSetting();

  ///
  /// finishAllUserSetting
  ///
  @DeviceApiResult(
    DeviceApiFunction.reset,
    ApiStatus.libMaintenance,
    "",
  )
  DeviceErrorCode finishAllUserSetting();

  ///
  /// キーの有効無効状態を取得する
  /// heelSwitch:setting page3でMulti Function機能表示の有無,MFFC+sidePedalを接続後にtrueに戻る
  /// sidePedal:setting page3でMulti Function機能表示の有無,IIVOには多機能ペダルのみが搭載されており、使用する必要はありません
  /// presserFootHeight:setting page1 presserFootHeightを使用,UTLでPivotIsOn機能を選択してfalseに戻る、falseの場合は10mmに固定する
  /// presserFootPressure:setting page1 presserFootPressureを使用,DFを接続してONにするfalseに戻る、falseの場合はdisplay valueは2に固定する
  /// baseLine:setting page２ Initial Positionを使用,Initial Stitch Pageの値はQの場合はfalseに戻る、falseの場合はグレー表示
  ///
  @DeviceApiResult(
    DeviceApiFunction.functionEnableInfo,
    ApiStatus.ok,
    "",
  )
  UserSettingEnabledInfo getUserSettingEnableInfo();

  ///
  /// ソフトVer.を取得する
  ///
  @DeviceApiResult(
    DeviceApiFunction.threadBrand,
    ApiStatus.doUiImp,
    """
ffi関数がない,現在使用されているバージョン情報,サーバ上のバージョンと比較する
""",
  )
  ModelSoftVersionInfo getModelSoftVersionInfo();

  ///
  /// 発生したエラーの履歴
  /// ※ エラー情報の蓄積ある時、子項目が入る。
  /// ※ Max30件
  ///
  @DeviceApiResult(
    DeviceApiFunction.errorList,
    ApiStatus.ngLib,
    """
ffi関数がない、この情報はArtspiraでログ用機能、XPはGetProductInfo()を参照ください。提供欲しい
""",
  )
  List<ErrorListInfo> getUserLogErrorList();

  ///
  /// 機器の動作状態を取得する
  ///
  @DeviceApiResult(
    DeviceApiFunction.artspira,
    ApiStatus.ngLib,
    """
ffi関数がない、この情報はArtspiraでログ用機能、XPはGetProductInfo()を参照ください。提供欲しい
""",
  )
  ModelStatusInfo getModelStatusInfo();

  ///
  /// カメラペンUI ON/OFF保存するAPI
  ///
  /// 引数[in]  :
  /// - [CameraUISetting]        : カメラペンUI設定値のアドレスをセットする
  /// 戻り値  :
  /// - [DeviceError] errorCode:エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraPen,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveEmbCameraUIProjectionSetting(CameraUISetting setting);

  ///
  /// カメラペンUI ON/OFF設定取得するAPI
  ///
  /// 戻り値   :
  /// - [DeviceErrorCode ] errorCode:エラーコード
  /// - [CameraUISetting] cameraUISetting: カメラペンUI設定値
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraPen,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, CameraUISetting value})
      getEmbCameraUIProjectionSetting();

  ///
  /// カメラペン投影UIの配置状態(揃えと最小化)をの設定値を保存する
  ///
  /// 引数[in]  :
  /// - [CameraUiArrangeState]
  ///
  /// 戻り値  :
  /// - [DeviceError] errorCode:エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraPen,
    ApiStatus.libMaintenance,
    "",
  )
  DeviceErrorCode saveEmbCameraUIArrangeStateSetting(
      CameraUiArrangeState value);

  ///
  /// カメラペン投影UIの配置状態(揃えと最小化)をの設定値を取得する
  ///
  /// 戻り値  :
  /// - [CameraUISetting]        : カメラペンUI設定値のアドレスをセットする
  /// - [DeviceError] errorCode:エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraPen,
    ApiStatus.libMaintenance,
    "",
  )
  ({DeviceErrorCode errorCode, CameraUiArrangeState cameraUiArrangeState})
      getCameraPenUIArrangeState();

  ///
  /// 言語設定済を取得する
  /// 戻り値   :
  /// - [bool ] true:設定済み、false:設定ない
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getSetupLangFlag();

  ///
  /// 言語設定済
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    """
    """,
  )
  DeviceErrorCode setSetupLangFlag();

  ///
  /// 言語設定済フラグをクリア
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    """
    """,
  )
  DeviceErrorCode resetSetupLang();

  ///
  /// ボイスガイダンス設定実行フラグ(初回起動)
  /// 戻り値   :
  /// - [bool ] true:設定済み、false:設定ない
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.doTest,
    """
    """,
  )
  ({DeviceErrorCode errCode, bool isSet}) getSetupVoiceGuideFlag();

  ///
  /// 言語設定済
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.doTest,
    """
    """,
  )
  DeviceErrorCode setSetupVoiceGuideFlag();

  ///
  /// 言語設定済フラグをクリア
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.doTest,
    """
    """,
  )
  DeviceErrorCode resetSetupVoiceGuideFlag();

  ///
  ///
  ///
  @DeviceApiResult(
    DeviceApiFunction.artspira,
    ApiStatus.doUiImp,
    """
    """,
  )
  ({DeviceErrorCode errorCode, UserLogProductInfo? userLogProductInfo})
      getUserLogProductInfo();

  ///
  /// EEPのRead/Wite関数 マルチスレッドで同時に呼び出すと正常動かない
  /// EEP PageWrite関数は非公開関数のまま(動作確認が不十分なため)
  /// BpifEepWrite(uint16_t adr, uint16_t size, uint16_t data, Eeprom::BOARD_TYPE type)
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.libMaintenance,
    """
    """,
  )
  DeviceErrorCode bpifEepWrite(
      int adr, int size, int data, BoardType boardType);

  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.libMaintenance,
    """
    """,
  )
  ({DeviceErrorCode errorCode, Uint16List buffer}) bpifEepRead(
      int adr, int size);

  ///
  /// RTC読み込み成功/失敗状態を取得する
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    " ",
  )
  RTCReadStatus getRTCReadErrorStatus();

  ///
  /// RTC読み込み成功/失敗状態をリセットする
  ///
  /// 戻り値:
  ///   0：リセット成功
  ///   0以外：リセット失敗
  ///   (数値はLinuxエラーコード準拠)
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    " ",
  )
  int resetRTCReadErrorStatus();

  ///
  /// 各種ログファイルの保存要求を行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.logcat,
    ApiStatus.ok,
    " ",
  )
  void writeDebugLogToFile();

  ///
  /// UserSettingItemValueListFree
  ///
  @DeviceApiResult(
    DeviceApiFunction.functionEnableInfo,
    ApiStatus.libMaintenance,
    "",
  )
  void UserSettingItemValueListFree();
}

///
/// 簡単修正為に
/// BSH追加したのAPI mixinでDeviceLibFunctionInterfaceに追加する。
///
mixin _ExtensionFunction {
  ///
  /// W+押さえ登録状態を取得する
  ///
  /// 戻る値は
  /// - true  : 登録しました。
  /// - false : まだ登録しない。
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.notUse,
    """
Libにはまだ提供されていません
""",
  )
  ({DeviceErrorCode errorCode, bool value}) getWPlusPFRegistrationStatus();

  ///
  /// W+押さえ登録状態をクリア
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.notUse,
    """
Windows simシミュレーション用機能。提供しなくても問題ありません
""",
  )
  DeviceErrorCode clearWPlusPFRegistrationStatus();

  ///
  /// デバッグ情報表示有無
  ///
  /// デバッグが有効になっている場合に、ログ表示を有効にする。
  ///
  /// 戻る値は
  /// - true  :有効
  /// - false :無効
  ///
  @DeviceApiResult(
    DeviceApiFunction.debugInfo,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) isDebugInfoEnabled();

  ///
  /// AI処理結果の画像評価のために表示有無
  ///
  /// AI処理結果の画像評価のために表示を有効にする。
  ///
  /// 戻る値は
  /// - true  :有効
  /// - false :無効
  ///
  @DeviceApiResult(
    DeviceApiFunction.photoStitchAiTest,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) isPhotoStitchAiTestEnabled();

  /// スクリーンショットの設定
  ///
  /// [level] : 0~2:
  ///0時にスクリーンショットしない
  ///1時シングルスクリーンショット
  ///2時多言語自動スクリーンショット
  ///
  @DeviceApiResult(
    DeviceApiFunction.screenShot,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setSettingScreenShot(int level);
  @DeviceApiResult(
    DeviceApiFunction.screenShot,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getSettingScreenShot();

  ///
  /// 読み上げ　Language
  ///
  /// 言語を切り替える。
  ///
  /// サポート対象言語がリスト表示され選択により確定する。
  ///※サポート対象言語は、XPモデルと同じとする。
  ///※日本語を追加
  ///※ロシア語を削除
  ///※選択リスト領域はオーバーレイ表示される。
  ///
  /// [language] : 詳細は [Language] を参考してください **(初期値:English)**
  ///
  /// 読み上げする言語の切り替えを行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.messageSpeakerVolume,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode setMessageSoundLanguage(MessageSoundLanguage language);
  @DeviceApiResult(
    DeviceApiFunction.messageSpeakerVolume,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, MessageSoundLanguage value})
      getMessageSoundLanguage();

  ///
  /// 読み上げの有無
  ///
  /// メッセージの読み上げを、「ON/OFF」で切り替えする。
  ///
  /// [isUse] : trueの場合はオン、falseの場合はオフ
  ///
  ///「ON」で、メッセージの読み上げが有効になり、「OFF」で無効となる。
  ///※読み上げ用の音声データは、表示言語で指定された１言語のみ内蔵データとして持つ。
  ///その為、表示言語（Language）を切り替えると、対象言語のデータは存在しない状態になる。
  ///
  @DeviceApiResult(
    DeviceApiFunction.messageSpeakerVolume,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveVoiceGuidanceOnOff(bool isUse);
  @DeviceApiResult(
    DeviceApiFunction.messageSpeakerVolume,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) getVoiceGuidanceOnOff();

  ///
  /// 現在実行されているバージョン番号
  /// 戻る値は
  /// - String :apkVersion取得
  ///
  @DeviceApiResult(
    DeviceApiFunction.upgrade,
    ApiStatus.ok,
    "",
  )
  Future<({DeviceErrorCode errorCode, String version})> getApkVersion();

  ///
  /// アップグレード版
  ///
  /// アップグレードの最新バージョン
  ///
  /// TODO:https://brothergroup.atlassian.net/browse/PHBSH-3177
  ///
  @DeviceApiResult(
    DeviceApiFunction.upgrade,
    ApiStatus.doUiImp,
    "廃棄関数、削除つもりです,実装時に検討しが必要",
  )
  ({DeviceErrorCode errorCode, String value}) getUpgradeVersion();
  @DeviceApiResult(
    DeviceApiFunction.upgrade,
    ApiStatus.doUiImp,
    "廃棄関数、削除つもりです,実装時に検討しが必要",
  )
  DeviceErrorCode setUpgradeVersion(String upgradeVersion);

  ///
  /// artSpira実データーの取得
  /// 引数[in]:
  ///   fcmData:サーバーからデータの復号化後での実データー
  ///   contentType: Content-type(application/x.fcm1/ application/x.pen1など)
  /// 引数[out]:
  ///   save用の実データー
  ///
  @DeviceApiResult(
    DeviceApiFunction.artspira,
    ApiStatus.doUiImp,
    """
APIまで提供しない
http://apbil2030891.ap.brothergroup.net/bsh/ph-iivo-app/-/issues/112
    """,
  )
  Uint8List embMdcConvertFCM2PCE(Uint8List fcmData, String contentType) =>
      Uint8List(0);

  ///
  /// artSpira実データーの取得
  /// 引数[in]:
  ///   save用の実データー
  ///   contentType: Content-type(application/x.fcm1/ application/x.pen1など)
  /// 引数[out]:
  ///    saveの結果、true：成功　false：失敗
  ///
  @DeviceApiResult(
    DeviceApiFunction.artspira,
    ApiStatus.ngLib,
    """
APIまで提供しない
    """,
  )
  bool embMdcStampSelectSaveSNCStamp(Uint8List pceData, String contentType) =>
      true;

  ///
  ///  SNC-MDC連携用データーのupload
  ///  upload前データをSNC用のデータへ変換する
  ///　引数[out]:
  ///       Uploadする実データー
  ///
  @DeviceApiResult(
    DeviceApiFunction.artspira,
    ApiStatus.doUiImp,
    """
saveEmbDataを使用したいと思います。
http://apbil2030891.ap.brothergroup.net/bsh/ph-iivo-app/-/issues/110
    """,
  )
  Uint8List embMakePhxFile() => Uint8List(0);

  ///
  /// AndroidでSSSS機能が必要かどうか
  /// UI の作成と Lib が提供する API が必要です。
  ///
  @DeviceApiResult(
    DeviceApiFunction.ssssFunction,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, bool value}) isAndroidNeedSSSS();

  ///
  /// スクリーンセーバー
  ///
  /// スクリーンセーバー画面を起動する時間と、表示する画像を設定する。
  ///
  /// [value] : 0(OFF) , 1~60(ON)
  /// **(初期値:5、単位：1min)**
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.screenSaver,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode saveScreenSaverTime(int value);

  ///
  /// スクリーンセーバー
  ///
  /// スクリーンセーバー画面を起動する時間と、表示する画像を設定する。
  ///
  /// [value] : 0(OFF) , 1~60(ON)
  /// **(初期値:5、単位：1min)**
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @DeviceApiResult(
    DeviceApiFunction.screenSaver,
    ApiStatus.ok,
    "",
  )
  ({DeviceErrorCode errorCode, int value}) getScreenSaverTime();

  ///
  /// 起動時eula設定実行可否
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  EULAInitStatusEnum getEulaInitStatus();
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  void setEulaInitStatus(EULAInitStatusEnum value);

  ///
  /// 起動時Wlan設定実行可否
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  WlanGuideSettingStatusEnum getWLanGuideSettingStatus();
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  void setWLanGuideSettingStatus(WlanGuideSettingStatusEnum value);

  ///
  /// 時計を表示するかどうか
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  bool getClockDisplayStatus();
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  void setClockDisplayStatus(bool value);

  ///
  /// 時計の表示タイプ(AM,PM,hour)
  ///
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  int getShowTimeTypeIndex();
  @DeviceApiResult(
    DeviceApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  void setShowTimeTypeIndex(int value);
}

///
/// 実機関連機能、TPDへの移動を計画する。
///
mixin _PreMoveFunction {
  ///
  /// 刺繡枠種類設定値を取得する
  ///
  /// 戻る値の詳細は : は [FrameSize] を参考してください
  ///
  @DeviceApiResult(
    DeviceApiFunction.upgrade,
    ApiStatus.ok,
    "",
  )
  FrameSize getEmbFrameType();

  ///
  /// 刺繡枠は フィックスしますが
  ///
  /// 戻る値は
  /// - true  :フィックスしました
  /// - false :まだフィックスしないです
  ///
  @DeviceApiResult(
    DeviceApiFunction.utlAssociation,
    ApiStatus.ok,
    "",
  )
  bool isFrameFixLeverLock();
}

mixin DeviceCustomExtensionFunction {
  ///
  /// カメラペン投影UIの配置状態(揃え)をの設定値を保存する
  ///
  /// 引数[in]  :
  /// - [CameraPenAlignType]
  ///
  /// 戻り値  :
  /// - [DeviceError] errorCode:エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraPen,
    ApiStatus.libMaintenance,
    "",
  )
  DeviceErrorCode saveEmbCameraUIAlignSetting(CameraPenAlignType value);

  ///
  /// カメラペン投影UIの配置状態(揃え)をの設定値を取得する
  ///
  /// 戻り値  :
  /// - [CameraPenAlignType] :
  /// - [DeviceError] errorCode:エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraPen,
    ApiStatus.libMaintenance,
    "",
  )
  ({DeviceErrorCode errorCode, CameraPenAlignType cameraPenAlignType})
      getCameraPenUIAlignSetting();

  ///
  /// カメラペン投影UIの配置状態(最小化)をの設定値を保存する
  ///
  /// 引数[in]  :
  /// - [bool] : false(最大)
  ///
  /// 戻り値  :
  /// - [DeviceError] errorCode:エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraPen,
    ApiStatus.libMaintenance,
    "",
  )
  DeviceErrorCode saveEmbCameraUIMinimumSetting(bool isMinimum);

  ///
  /// カメラペン投影UIの配置状態(最小化)をの設定値を取得する
  ///
  /// 戻り値  :
  /// - [bool] : false(最大)
  /// - [DeviceError] errorCode:エラーコード
  ///
  @DeviceApiResult(
    DeviceApiFunction.cameraPen,
    ApiStatus.libMaintenance,
    "",
  )
  ({DeviceErrorCode errorCode, bool isMinimum}) getCameraPenUIMinimumSetting();

  ///
  /// ミシンの仕向けを取得します
  ///
  /// 戻り値：[ModeSpec_t]に参考してください
  /// - tacony:[ModeSpec_t.SPEC_EXPORT_TACONY]
  /// - brother:[ModeSpec_t.SPEC_EXPORT_TACONY]以外全部ブラザーです
  ///
  ///
  int getSpec();

  ///
  /// 日本仕向けか
  ///
  /// 戻り値：bool
  /// - true:日本仕向
  /// - false:日本仕向ではない
  ///
  ///
  bool getProductsJapan();

  ///
  /// カメラペンのキャリブレーションポイントを設定する
  ///
  Offset getCameraPenOffset();

  ///
  /// カメラペンのキャリブレーションポイントを設定する
  /// 比率 = 実際の幅 / 元の画像の幅
  ///
  Offset getCameraPenConversionRatio();

  ///
  /// カメラペンのキャリブレーションポイントを設定する
  ///
  void setCameraPenOffset(Offset offset);

  ///
  /// カメラペンのキャリブレーションポイントを設定する
  /// 比率 = 実際の幅 / 元の画像の幅
  ///
  void setCameraPenConversionRatio(Offset offset);

  ///
  /// WLAN OFFの場合の電源オン回数
  ///
  int getWlanOffStartUpCount();

  ///
  /// WLAN OFFの場合の起動回数を増やす
  ///
  void addWlanOffStartUpCount();

  ///
  /// WLANがオフのときに起動カウントをリセットする
  ///
  void resetWlanOffStartUpCount();

  ///
  /// artSpiraのユーザー登録を行う
  /// artSpiraのユーザーログオフを行う
  ///
  void setArtspiraRegisterInfo(bool isRegister, String name,
      {String? accessToken, String? refreshToken});

  ///
  /// artSpiraのユーザー登録情報を取得する
  ///
  (bool isRegister, String name, String accessToken, String refreshToken)
      getArtspiraRegisterInfo();

  ///
  /// ユーザー情報収集への同意レベルを設定する
  void setArtspiraAgreedToTelemetry(int level);

  ///
  ///ユーザー情報収集への同意レベルを取得する
  ///
  int getArtspiraAgreedToTelemetry();

  ///
  /// MdcのResume状態の設定と取得
  ///
  bool getMdcResumeState();
  void setMdcResumeState(bool state);

  ///
  /// スナップショットの保存数の設定と取得
  /// [snapSize] defaultSize : 0
  ///
  int getMdcSnapSize();
  void setMdcSnapSize(int snapSize);

  ///
  /// Undo可能な回数の設定と取得
  /// [undoSize] defaultSize : 0
  ///
  int getMdcUndoSize();
  void setMdcUndoSize(int undoSize);

  ///
  /// 次にスナップショットを保存するIndexの設定と取得
  /// [snapPoint] defaultPoint : -1
  ///
  ///
  int getMdcSnapPoint();
  void setMdcSnapPoint(int snapPoint);

  ///
  /// mdc バックグラウンド画像の状態の取得と設定
  ///
  bool getMdcBackgroundState();
  void setMdcBackgroundState(bool state);

  ///
  /// Canvas Serverの値を取得する設定
  ///
  int getCanvasServer();
  void setCanvasServer(int value);

  ///
  /// Kit Serverの値を取得する設定
  ///
  int getKitServer();
  void setKitServer(int value);

  ///
  /// Snc Kit Serverの値を取得するを設定
  ///
  int getSncKitServer();
  void setSncKitServer(int value);

  ///
  /// 時刻を設定するかどうか
  ///
  bool getIsSetTime();
  void setIsSetTime(bool value);

  ///
  /// Snc Kit Serverの値を取得するを設定
  ///
  int getAppBadgeSenju();
  void setAppBadgeSenju(int value);

  ///
  /// machine Nameの値を取得するを設定
  ///
  String getWlanMachineName();
  void setWlanMachineName(String value);

  ///
  /// firmDownloadTimestampの値を設定する
  ///
  void setFirmDownloadTimestamp(String value);

  ///
  /// プロダクトNoの値を取得
  ///
  String getProductNo();

  ///
  /// デフォルトの「Wlan Machine Name」を取得する
  ///
  String getDefaultMachineName();

  ///
  /// mdc クリップボード有りの状態の取得と設定
  ///
  bool getMdcClipboardState();
  void setMdcClipboardState(bool state);
}
