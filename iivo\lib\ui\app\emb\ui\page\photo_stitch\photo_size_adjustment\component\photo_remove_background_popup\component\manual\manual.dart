import 'package:common_component/common/tooltip.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../../../../component/painter/painter.dart';
import 'manual_view_model.dart';

///
/// 背景を除去します
///
class Manual extends ConsumerStatefulWidget {
  const Manual({super.key});

  @override
  ConsumerState<Manual> createState() => _ManualState();
}

class _ManualState extends ConsumerState<Manual> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final viewModel = ref.read(manualViewModelProvider.notifier);
    final state = ref.watch(manualViewModelProvider);

    return Column(
      children: [
        Expanded(
          flex: 529,
          child: Row(
            children: [
              const Spacer(flex: 1),
              Expanded(
                flex: 786,
                child: pre_square_base(
                  child: Stack(
                    children: [
                      Row(
                        children: [
                          const Spacer(flex: 124),
                          Expanded(
                            flex: 539,
                            child: Center(
                              child:

                                  /// プレビュー表示
                                  SizedBox(
                                width: state.imageWidth,
                                height: state.imageHeight,
                                child: Stack(
                                  children: [
                                    Center(
                                      child: SizedBox(
                                        width: state.imageWidth,
                                        height: state.imageHeight,
                                        child: state.contentImage == null
                                            ? Container()
                                            : Image(
                                                fit: BoxFit.fill,
                                                image:
                                                    state.contentImage!.image,
                                                width: state.imageWidth,
                                                height: state.imageHeight,
                                              ),
                                      ),
                                    ),
                                    Center(
                                      child: SizedBox(
                                        width: state.imageWidth,
                                        height: state.imageHeight,
                                        child: state.gridImage == null
                                            ? Container()
                                            : Image(
                                                fit: BoxFit.fill,
                                                image: state.gridImage!.image,
                                                width: state.imageWidth,
                                                height: state.imageHeight,
                                              ),
                                      ),
                                    ),
                                    Visibility(
                                      maintainState: true,
                                      visible: state.showPenDraw,
                                      child:
                                          Painter(viewModel.painterController),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const Spacer(flex: 123),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const Spacer(flex: 1),
            ],
          ),
        ),
        Expanded(
          flex: 445,
          child: Column(
            children: [
              const Spacer(flex: 108),
              Expanded(
                flex: 70,
                child: Row(
                  children: [
                    const Spacer(flex: 80),
                    Expanded(
                      flex: 98,
                      child: CustomTooltip(
                        message: l10n.tt_emb_photostitch_backremoval_scopeplus,
                        child: grp_btn_scopeplus(
                          state: state.scopePlusButtonState,
                          onTap: viewModel.onScopePlusButtonClicked,
                        ),
                      ),
                    ),
                    const Spacer(flex: 8),
                    Expanded(
                      flex: 98,
                      child: CustomTooltip(
                        message: l10n.tt_emb_photostitch_backremoval_scopeminus,
                        child: grp_btn_scopeminous(
                          state: state.backgroundButtonState,
                          onTap: viewModel.onBackgroundButtonClicked,
                        ),
                      ),
                    ),
                    const Spacer(flex: 24),
                    Expanded(
                      flex: 98,
                      child: CustomTooltip(
                        message: l10n.tt_emb_photostitch_backremoval_erase,
                        child: grp_btn_erase(
                          state: state.eraseButtonState,
                          onTap: viewModel.onEraseButtonClicked,
                        ),
                      ),
                    ),
                    const Spacer(flex: 8),
                    Expanded(
                      flex: 98,
                      child: CustomTooltip(
                        message: l10n.tt_emb_photostitch_backremoval_trash,
                        child: grp_btn_trash(
                          state: state.penClearButtonState,
                          onTap: viewModel.onPenAllClearButtonClick,
                        ),
                      ),
                    ),
                    const Spacer(flex: 24),
                    Expanded(
                      flex: 98,
                      child: CustomTooltip(
                        message: l10n.tt_emb_photostitch_backremoval_blind,
                        child: grp_btn_blind(
                          state: state.resultButtonState,
                          onTap: viewModel.onResultButtonClick,
                        ),
                      ),
                    ),
                    const Spacer(flex: 154),
                  ],
                ),
              ),
              const Spacer(flex: 175),
              Expanded(
                flex: 80,
                child: Row(
                  children: [
                    const Spacer(flex: 12),
                    Expanded(
                      flex: 152,
                      child: grp_btn_cancel(
                        text: l10n.icon_cancel,
                        onTap: () => viewModel.onCancelButtonClicked(context),
                      ),
                    ),
                    const Spacer(flex: 142),
                    Expanded(
                      flex: 84,
                      child: grp_btn_undo(
                        state: state.undoButtonState,
                        onTap: viewModel.onUndoButtonClick,
                      ),
                    ),
                    const Spacer(flex: 8),
                    Expanded(
                      flex: 84,
                      child: grp_btn_redo(
                        state: state.redoButtonState,
                        onTap: viewModel.onRedoButtonClick,
                      ),
                    ),
                    const Spacer(flex: 142),
                    Expanded(
                      flex: 152,
                      child: grp_btn_ok(
                        buttonText: state.showPreviewButton
                            ? l10n.icon_00511_1
                            : l10n.icon_ok,
                        onTap: state.showPreviewButton
                            ? () => viewModel.onPreviewButtonClicked(context)
                            : () => viewModel.onOkButtonClicked(context),
                      ),
                    ),
                    const Spacer(flex: 12),
                  ],
                ),
              ),
              const Spacer(flex: 12),
            ],
          ),
        ),
      ],
    );
  }
}
