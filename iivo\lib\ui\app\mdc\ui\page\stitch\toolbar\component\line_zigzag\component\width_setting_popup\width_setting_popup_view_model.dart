import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_zigzag_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'width_setting_popup_view_interface.dart';

typedef Unit = DisplayUnit;

final widthSettingPopupViewModelProvider = StateNotifierProvider.autoDispose<
    WidthSettingPopupViewInterface,
    WidthSettingPopupState>((ref) => WidthSettingPopupViewModel(ref));

class WidthSettingPopupViewModel extends WidthSettingPopupViewInterface {
  WidthSettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const WidthSettingPopupState(
              widthDisplayValue: "",
              isDefaultValue: false,
              minusButtonValid: false,
              plusButtonValid: false,
            ),
            ref) {
    update();
  }

  ///
  /// 最大幅値
  final int maxWidthValue = 60;

  ///
  /// 最小幅値
  ///
  final int minWidthValue = 10;

  ///
  /// デフォルトの幅値
  ///
  final int defaultWidthValue = LineZigzagModel().defaultWidthValue;

  ///
  /// ステップ量
  ///
  final int _stepValue = 5;

  ///
  /// 単位取得する
  ///
  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  ///
  /// 幅は、値がアスタリスクであるかどうかを示します
  ///
  bool _isWidthValueDisplayStar =
      LineZigzagModel().getWidth() != LineZigzagModel.widthNotUpdating
          ? false
          : true;

  ///
  /// 幅値
  ///
  int _widthValue = LineZigzagModel().getWidth();

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    state = state.copyWith(
        widthDisplayValue: _getWidthDisplayValue(),
        isDefaultValue: _isDefaultValue(),
        minusButtonValid: _getMinusButtonState(),
        plusButtonValid: _getPlusButtonState());
  }

  ///
  /// マイナスボタンをクリックする
  ///
  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isWidthValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isWidthValueDisplayStar = false;

      ///  Model 更新
      _widthValue = defaultWidthValue;

      /// View更新
      update();

      return false;
    }

    if (_widthValue <= minWidthValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _widthValue = _widthValue - _stepValue < minWidthValue
        ? minWidthValue
        : _widthValue - _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  ///
  /// プラスボタンをクリックする
  ///
  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_isWidthValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isWidthValueDisplayStar = false;

      ///  Model 更新
      _widthValue = defaultWidthValue;

      /// View更新
      update();

      return false;
    }

    if (_widthValue >= maxWidthValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _widthValue = _widthValue + _stepValue > maxWidthValue
        ? maxWidthValue
        : _widthValue + _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  ///
  /// OKボタンがクリックされました
  ///
  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineZigzagWidth.toString());
    if (_isWidthValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int widthValue = LineZigzagModel().getWidth();

    /// Model 更新
    LineZigzagModel().setWidth(_widthValue);
    if (LineZigzagModel().setMdcZigzagSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (widthValue != _widthValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// 幅の表示値を取得する
  ///
  String _getWidthDisplayValue() {
    /// cmからmmへ
    double lineZigzagWidthValue = _widthValue / 10.0;

    if (_isWidthValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      }
      return "*.***";
    }

    if (currentSelectedUnit == Unit.mm) {
      return lineZigzagWidthValue.toStringAsFixed(1);
    }

    return ToolbarModel.getDisplayInchShowValue(lineZigzagWidthValue);
  }

  ///
  ///  幅表示テキストスタイルを取得します
  ///
  bool _isDefaultValue() {
    if (_isWidthValueDisplayStar) {
      return true;
    }

    if (_widthValue == defaultWidthValue) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isWidthValueDisplayStar) {
      return true;
    }
    if (_widthValue <= minWidthValue) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isWidthValueDisplayStar) {
      return true;
    }
    if (_widthValue >= maxWidthValue) {
      return false;
    }
    return true;
  }
}
