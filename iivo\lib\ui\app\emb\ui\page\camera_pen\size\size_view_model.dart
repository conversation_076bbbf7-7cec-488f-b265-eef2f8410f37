import 'dart:async';

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../model/projector/camera_pen/buttion_info/emb_button_info.dart';
import '../../../../../../../model/projector/camera_pen/camera_pen_type.dart';
import '../../../../../../../model/projector_model.dart';
import '../../../../model/pattern_model.dart';
import '../../common_component/function_provider/size_function_provider/size_function_interface.dart';
import '../../common_component/function_provider/size_function_provider/size_function_provider.dart';
import '../../pattern_edit/preview/preview_view_model.dart';
import 'size_view_interface.dart';

final sizeViewModelProvider =
    StateNotifierProvider.autoDispose<SizeViewModelInterface, SizeState>(
        (ref) => SizeViewModel(ref));

class SizeViewModel extends SizeViewModelInterface {
  SizeViewModel(Ref ref) : super(const SizeState(), ref) {
    ProjectorModel().embCameraPenViewInfo.embCameraPenStatus =
        EmbCameraPenStatus.size;
    _startShowStatus();
  }

  ///
  /// 現在のステータスを表示するかどうか
  ///
  bool _isDisplayStatus = false;

  @override
  void update() {
    state = state.copyWith(
      alignType: ProjectorModel().embCameraPenViewInfo.embAlignType,
      isDisplayStatus: _isDisplayStatus,
      isAspectRatioLock:
          ProjectorModel().embCameraPenViewInfo.isEmbSizeAspectRatioLock,
    );
  }

  @override
  bool shortClick(EmbCameraPenButton sizeButton) {
    final (magType: magType, changeType: changeType) =
        _embCameraPenButtonToSizeFunction(sizeButton);

    final currentEmbGroup = PatternModel().getCurrentGroup();

    /// 回数調整前の確認
    final sizeFunction = ref.read(sizeFunctionProvider.notifier);

    if (sizeFunction.state.isSizeFunctionDisable == true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (sizeFunction.isMagStepOverRange(currentEmbGroup, magType, changeType) ==
        true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 模様サイズを変更する
    /// 必要ならエラーの確認を追加します
    sizeFunction.changePatternSize(
      currentEmbGroup,
      magType,
      _step,
      changeType,
    );

    /// Lcd画面表示更新
    /// プロジェクト画面表示更新
    ref.read(previewViewModelProvider.notifier).update();
    updateProjectorCameraPenUI(redrawEmbPattern: true);
    return true;
  }

  @override
  void longClick(EmbCameraPenButton sizeButton, int longPressTick) {
    final int speed = longPressTick;
    final Magnitude magStep = _magStep;
    final sizeFunction = ref.read(sizeFunctionProvider.notifier);
    final (magType: magType, changeType: changeType) =
        _embCameraPenButtonToSizeFunction(sizeButton);

    final currentEmbGroup = PatternModel().getCurrentGroup();

    if (sizeFunction.state.isSizeFunctionDisable == true ||
        sizeFunction.isMagStepOverRange(currentEmbGroup, magType, changeType) ==
            true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 今回の増量計算します
    final (int minSpeed, int maxSpeed) = sizeFunction.getChangeStepSpeedRange(
        currentEmbGroup, magStep, MagType.xyAll, ChangeType.reduce);
    final step = speed.clamp(minSpeed, maxSpeed) * _step;

    /// 模様サイズを変更する
    /// 必要ならエラーの確認を追加します
    sizeFunction.changePatternSize(
      currentEmbGroup,
      magType,
      step,
      changeType,
    );

    /// Lcd画面表示更新
    /// プロジェクト画面表示更新
    ref.read(previewViewModelProvider.notifier).update();
    updateProjectorCameraPenUI(redrawEmbPattern: true);
  }

  @override
  void handleSizeLockButton() {
    final sizeFunction = ref.read(sizeFunctionProvider.notifier);
    if (sizeFunction.state.isSizeFunctionDisable == true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    ProjectorModel().embCameraPenViewInfo.isEmbSizeAspectRatioLock =
        !ProjectorModel().embCameraPenViewInfo.isEmbSizeAspectRatioLock;

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    ///
    updateProjectorCameraPenUI();
  }

  ///
  /// プロジェクト画面更新
  ///
  @override
  Future updateProjectorCameraPenUI({
    bool redrawEmbPattern = false,
  }) {
    update();
    final Completer completer = Completer();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Future.delayed(
          const Duration(milliseconds: ProjectorModel.projectorRefreshDelayMS));
      await ProjectorModel().embProjector.refreshEmbProjector(
          redrawEmbPattern: redrawEmbPattern, autoBacklight: false);
      completer.complete();
    });

    return completer.future;
  }

  ///
  /// ステータスの表示を開始する
  ///
  void _startShowStatus() {
    ProjectorModel().cameraPen.isCameraStatusChangePressing = true;
    _isDisplayStatus = true;

    updateProjectorCameraPenUI(redrawEmbPattern: false)
        .then((_) {
          /// プロジェクトバックライト起動待ちます
          Future.doWhile(() async {
            if (CameraProjectLibrary().apiBinding.isProjectorBackLightOn() ==
                false) {
              return false;
            }
            await Future.delayed(const Duration(
                milliseconds: ProjectorModel.projectorRefreshDelayMS));
            return true;
          });
        })
        .then((_) async => Future.delayed(const Duration(seconds: 1)))
        .then((_) async {
          _isDisplayStatus = false;
          await updateProjectorCameraPenUI();
          ProjectorModel().cameraPen.isCameraStatusChangePressing = false;
        });
  }

  ///
  /// [EmbCameraPenButton]から、回転関数必要なパラメータに変更します
  ///
  ({MagType magType, ChangeType changeType}) _embCameraPenButtonToSizeFunction(
      EmbCameraPenButton type) {
    MagType magType;
    ChangeType changeType;
    switch (type) {
      case EmbCameraPenButton.heightPlus:
        magType =
            ProjectorModel().embCameraPenViewInfo.isEmbSizeAspectRatioLock ==
                    true
                ? MagType.xyAll
                : MagType.yOnly;
        changeType = ChangeType.enlarge;
        break;
      case EmbCameraPenButton.widthPlus:
        magType =
            ProjectorModel().embCameraPenViewInfo.isEmbSizeAspectRatioLock ==
                    true
                ? MagType.xyAll
                : MagType.xOnly;
        changeType = ChangeType.enlarge;
        break;
      case EmbCameraPenButton.heightMinus:
        magType =
            ProjectorModel().embCameraPenViewInfo.isEmbSizeAspectRatioLock ==
                    true
                ? MagType.xyAll
                : MagType.yOnly;
        changeType = ChangeType.reduce;
        break;
      case EmbCameraPenButton.widthMinus:
        magType =
            ProjectorModel().embCameraPenViewInfo.isEmbSizeAspectRatioLock ==
                    true
                ? MagType.xyAll
                : MagType.xOnly;
        changeType = ChangeType.reduce;
        break;
      default:
        Log.fatal(tag: "", description: "");
        magType = MagType.xyAll;
        changeType = ChangeType.enlarge;
    }

    return (magType: magType, changeType: changeType);
  }

  ///
  /// 模様拡大のステップ値
  ///
  static const int _step = 1;

  Magnitude get _magStep => PatternModel()
      .getCurrentGroup()
      .embGroupInfo
      .embPatternInfo
      .embPatterns
      .first
      .magStep;
}
