import 'package:common_component/common_component.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../../../page_route/page_route.dart';
import '../../../../../model/key_board_font_model.dart';
import '../../../../../model/pattern_data_reader/character_font_image_reader.dart';
import '../../../../../model/pattern_data_reader/character_font_select_reader.dart';
import 'character_popup_view_interface.dart';

final characterPopupViewModelProvider = StateNotifierProvider.autoDispose<
    CharacterPopupViewModelInterface,
    CharacterPopupState>((ref) => CharacterPopupViewModel(ref));

class CharacterPopupViewModel extends CharacterPopupViewModelInterface {
  CharacterPopupViewModel(Ref ref) : super(const CharacterPopupState(), ref);

  @override
  void onCancelButtonClicked(BuildContext context) {
    PopupNavigator.pop(context: context);
  }

  @override
  void onOKButtonClicked(BuildContext context, int index) {
    String groupName = _getCharacterFontName(index);
    KeyBoardFontModel().fontName = groupName;
    CharacterFontSelectReader().updateCharacterFontSelectGroup(groupName);
    PopupNavigator.pop(context: context);
    KeyBoardFontModel().startEmbCharEdit(false);
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.characterFontSelect);

    KeyBoardFontModel().fontNumber =
        _getAllCharacterFontImagesInfo()[index].fontNumber;
    KeyBoardFontModel().selectedFontType =
        _getAllCharacterFontImagesInfo()[index].type;

    if (_getAllCharacterFontImagesInfo()[index].type ==
        KeyBoardFontModel.emcTypeAlp) {
      KeyBoardFontModel().displayFontSize =
          KeyBoardFontModel.defaultFontSizeAlp;
    } else if (_getAllCharacterFontImagesInfo()[index].type ==
        KeyBoardFontModel.emcTypeSmall) {
      KeyBoardFontModel().displayFontSize =
          KeyBoardFontModel.defaultFontSizeSmall;
    }
  }

  @override
  Widget getCharacterFontImage(int index) =>
      _getAllCharacterFontImagesInfo()[index].image;

  @override
  Image getCharacterFontPreviewImage(int index) =>
      _getAllCharacterFontImagesInfo()[index].previewImage;

  ///
  /// ExclusiveScriptフォントの判断
  ///
  @override
  bool isExclusiveScriptType(int index) =>
      CharacterFontImageReader()
          .getAllCharacterFontImagesInfo()[index]
          .fontNumber ==
      KeyBoardFontModel.exclusiveScriptFontNumber;

  ///
  /// フォントの名前を取得します
  ///
  String _getCharacterFontName(int index) =>
      _getAllCharacterFontImagesInfo()[index].name;

  ///
  /// 全てのカテゴリのイコンデータを取得する
  ///
  /// - List<CategoryImageGroup>: 順番保存されているの模様イコンデータ
  ///
  List<CharacterFontImageGroup> _getAllCharacterFontImagesInfo() =>
      CharacterFontImageReader().getAllCharacterFontImagesInfo();
}
