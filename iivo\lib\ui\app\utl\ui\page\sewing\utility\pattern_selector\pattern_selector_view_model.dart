import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../../model/app_locale.dart';
import '../../../../../../../../model/projector/camera_pen/camera_pen_type.dart';
import '../../../../../../../../model/projector_model.dart';
import '../../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../../global_popup/global_popup_export.dart';

import '../../../../../model/pattern_data_reader/utility/pattern_data_reader.dart';
import '../../../../../model/pattern_model.dart' as pattern_model;
import '../../../../../model/preview_model.dart';
import '../../../../component/utl_header/utl_header_view_model.dart';
import '../../common/camera_pen/camera_pen_view_model.dart';
import '../preview/preview_view_model.dart';
import '../utility_view_interface.dart';
import '../utility_view_model.dart';
import 'pattern_selector_view_interface.dart';

final utilityPatternSelectorViewModeProvider = StateNotifierProvider
    .autoDispose<UtilityPatternSelectorViewMode, UtilityPatternSelectorState>(
        (ref) => UtilityPatternSelectorViewMode(ref));

class UtilityPatternSelectorViewMode
    extends UtilityPatternSelectorViewModelInterface {
  UtilityPatternSelectorViewMode(AutoDisposeStateNotifierProviderRef ref)
      : super(const UtilityPatternSelectorState(), ref);

  @override
  void build() {
    super.build();
    pattern_model.PatternDataModel().getUtilityInitPattern();

    /// 模様登録
    pattern_model.PatternDataModel().loginUtilityPattern(
      pattern_model.PatternDataModel().currentSelectedCategoryIndex,
      pattern_model.PatternDataModel().currentSelectedPatternIndex,
    );

    /// View更新
    update();

    Future(() {
      if (ref.exists(utilityViewModelProvider)) {
        ref
            .read(utilityViewModelProvider.notifier)
            .updateUtilityPageByChild(UtilityModuleType.patternSelector);
      } else {
        /// Do Nothing
      }
    });

    ref.listen(
      appDisplayUtlStateProvider
          .select((value) => value.utlAttribParam.ref.freeMotion),
      (previous, next) {
        update();
      },
    );
    ref.listen(
      appDisplayUtlStateProvider.select((value) => (
            value.utlAttribParam.ref.needleNum,
            value.utlFuncSetting.ref.isConnectedDF
          )),
      (previous, nextState) {
        state = state = state.copyWith(
          patternsStyle: _updateItemStyle(
            pattern_model.PatternDataModel().currentSelectedCategoryIndex,
            pattern_model.PatternDataModel().currentSelectedPatternIndex,
          ),
        );
      },
    );
  }

  @override
  void update() {
    /// View更新
    state = state.copyWith(
      currentSelectedCategoryIndex:
          pattern_model.PatternDataModel().currentSelectedCategoryIndex,
      patternsStyle: _updateItemStyle(
        pattern_model.PatternDataModel().currentSelectedCategoryIndex,
        pattern_model.PatternDataModel().currentSelectedPatternIndex,
      ),
    );
  }

  @override
  List<PatternGroup> getAllPatternIconsInfo() =>
      PatternIconReader().getAllPatternIconsInfo();

  @override
  void onSelectCategory(int index, BuildContext context) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    wishPatternIndex = null;

    /// ポップアップ画面を閉じます
    closeAllPopup(context);

    /// カテゴリーと模様を選択します
    if (index ==
        pattern_model.PatternDataModel().currentSelectedCategoryIndex) {
      return;
    } else {
      pattern_model.PatternDataModel().currentSelectedCategoryIndex = index;
    }

    if (index == pattern_model.PatternDataModel().currentLoginCategoryIndex) {
      pattern_model.PatternDataModel().currentSelectedPatternIndex =
          pattern_model.PatternDataModel().currentLoginPatternIndex;
    } else {
      pattern_model.PatternDataModel().currentSelectedPatternIndex = null;
    }

    /// state更新
    state = state.copyWith(
      currentSelectedCategoryIndex:
          pattern_model.PatternDataModel().currentSelectedCategoryIndex,
      patternsStyle: _updateItemStyle(
        pattern_model.PatternDataModel().currentSelectedCategoryIndex,
        pattern_model.PatternDataModel().currentSelectedPatternIndex,
      ),
    );
  }

  @override
  Future<void> onSelectPattern(int patternIndex) async {
    final DirErrorCode dirError = TpdLibrary()
        .apiBinding
        .setMatrixEnableList(MachineKeyState.machineKeyEnableAllNG);
    if (dirError == DirErrorCode.dirMotorError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else if (dirError != DirErrorCode.dirNoError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      Log.errorTrace("Undefined Error Handle");
      return;
    }
    if (state.patternsStyle.elementAtOrNull(patternIndex) ==
        ButtonState.disable) {
      // ここではinvalid音を再生せず、下部のERRORポップアップが音を管理する
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    wishPatternIndex = patternIndex;

    pattern_model.PatternDataModel().loginUtilityPattern(
        pattern_model.PatternDataModel().currentSelectedCategoryIndex,
        wishPatternIndex);

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      if (bPIFErrorPointer.errorCode == ErrCode_t.ERR_EPS_FINISH.index) {
        errEpsFinishFunc = _onConfirmPopupOKButtonClick;
      } else if (bPIFErrorPointer.errorCode ==
          ErrCode_t.ERR_TAPERING_FINISH.index) {
        errTaperingFinishFunc = _onConfirmPopupOKButtonClick;
      }
      return;
    }

    /// Model更新
    pattern_model.PatternDataModel().currentSelectedPatternIndex =
        wishPatternIndex;
    pattern_model.PatternDataModel().isNotPatternFirstTime = true;

    /// View更新
    state = state.copyWith(
      patternsStyle: _updateItemStyle(
        pattern_model.PatternDataModel().currentSelectedCategoryIndex,
        pattern_model.PatternDataModel().currentSelectedPatternIndex,
      ),
    );

    /// カメラ使用不可の模様なら、カメラポーリング停止を停止
    if (pattern_model.PatternDataModel().isNotSupportCameraPattern()) {
      final viewMode = ref.read(utlHeaderViewModelProvider.notifier);
      viewMode.maybeCloseCamera();

      /// 画面更新は下記 [updateUtilityPageByChild]に更新しているので、ここは
      /// 更新必要がないです
    }

    ref
        .read(utilityViewModelProvider.notifier)
        .updateUtilityPageByChild(UtilityModuleType.patternSelector);

    TpdLibrary()
        .apiBinding
        .setMatrixEnableList(MachineKeyState.machineKeyEnableAll);

    /// プロジェクトとカメラマンを閉じる
    if (pattern_model.PatternDataModel().isNotSupportProjectorPattern()) {
      /// CameraPen閉じる
      ref
          .read(cameraPenViewModelProvider.notifier)
          .closeCameraPenAndCameraPenUI(needUpdateProjectorUI: false);

      /// プロジェクト
      if (ProjectorModel().isUtlProjectorAllClosed() == false) {
        ProjectorModel().closeAllUtlProjector();
      }
    } else {
      _reOpenCameraPenUI();
    }
  }

  ///
  /// 画面がスライドすると、ポップアップ画面を閉じます
  ///
  @override
  void closeAllPopup(BuildContext context) {
    ref
        .readAutoNotifierIfExists(utilityViewInfoProvider)
        ?.navigator
        .maybeRemoveRoute(routeName: PopupEnum.editMenu.toString());

    /// Model更新
    pattern_model.PatternDataModel().isEditMenuOn = false;

    /// view更新
    ref
        .read(utilityViewModelProvider.notifier)
        .updateUtilityPageByChild(UtilityModuleType.closeAllPopup);
  }

  @override
  void moveToLoginSelectPattern() {
    if (pattern_model.PatternDataModel().currentLoginCategoryIndex == null ||
        pattern_model.PatternDataModel().currentLoginPatternIndex == null) {
      Log.e(tag: "moveToLoginSelectPattern", description: " invalid value[0]");
      return;
    }

    final int currentLoginCategoryIndex =
        pattern_model.PatternDataModel().currentLoginCategoryIndex!;
    final int currentLoginPatternIndex =
        pattern_model.PatternDataModel().currentLoginPatternIndex!;

    /// 情報更新
    pattern_model.PatternDataModel().currentSelectedCategoryIndex =
        currentLoginCategoryIndex;
    pattern_model.PatternDataModel().currentSelectedPatternIndex =
        currentLoginPatternIndex;

    /// 画像更新
    state = state.copyWith(
      currentSelectedCategoryIndex: currentLoginCategoryIndex,
      patternsStyle: _updateItemStyle(
        currentLoginCategoryIndex,
        currentLoginPatternIndex,
      ),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      double jumpValue = _getJumpPosition(
          currentLoginPatternIndex, controller.position.maxScrollExtent);
      if (controller.offset <= controller.position.maxScrollExtent) {
        controller.jumpTo(jumpValue);
      }
    });
  }

  @override
  String getCategoryTooltip(int categoryNum) {
    Locale locale = AppLocale().getCurrentLocale();
    AppLocalizations l10n = lookupAppLocalizations(locale);
    final String value;
    switch (categoryNum) {
      case pattern_model.group1:
        value = l10n.tt_utl_category01;
        break;
      case pattern_model.group2:
        value = l10n.tt_utl_category02;
        break;
      case pattern_model.group3:
        value = l10n.tt_utl_category03;
        break;
      case pattern_model.group4:
        value = l10n.tt_utl_category04;
        break;
      case pattern_model.group5:
        value = l10n.tt_utl_category05;
        break;
      case pattern_model.groupQ:
        value = l10n.tt_utl_category_q;
        break;
      case pattern_model.groupS:
        value = l10n.tt_utl_category_s;
        break;
      case pattern_model.groupT:
        value = l10n.tt_utl_category_t;
        break;

      default:
        value = "";
    }

    return value;
  }

  @override
  void dispose() {
    super.dispose();
    controller.dispose();
  }

  ///
  /// 模様表示状態取得する
  ///
  /// ##@param
  ///
  /// - currentSelectedCategoryIndex : 画面選択した模様のカテゴリーインデックス
  /// - currentSelectedPatternIndex : 画面選択した模様のパターンインデックス
  ///
  List<ButtonState> _updateItemStyle(
    int currentSelectedCategoryIndex,
    int? currentSelectedPatternIndex,
  ) {
    final patternGroup = PatternIconReader()
        .getAllPatternIconsInfo()[currentSelectedCategoryIndex]
        .iconsGroup;
    List<int> disableDataList = UtlLibrary()
        .apiBinding
        .getDisableUtlDataList(PatternIconReader()
            .getAllPatternIconsInfo()[currentSelectedCategoryIndex]
            .groupNum)
        .disableDataList;
    List<ButtonState> patternsItemStyle =
        List.filled(patternGroup.length, ButtonState.normal);

    for (int i = 0; i < patternGroup.length; i++) {
      if (currentSelectedPatternIndex == i) {
        patternsItemStyle[i] = ButtonState.select;
      } else {
        if (disableDataList.contains(patternGroup[i].patternNum)) {
          patternsItemStyle[i] = ButtonState.disable;
        } else {
          patternsItemStyle[i] = ButtonState.normal;
        }
      }
    }

    return patternsItemStyle;
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// ポップアップのクリック関数群
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// ポップアップのOKボタンのクリック関数
  /// ぬい終わり位置設定を解除しますが、よろしいですか？
  ///
  void _onConfirmPopupOKButtonClick() {
    if (ProjectorModel().isUtlProjectorOpened(UtlProjectorType.endpoint)) {
      ProjectorModel()
          .closeUtlProjector(UtlProjectorType.endpoint)
          .then((value) {
        final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
        if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
          if (bPIFErrorPointer.errorCode == ErrCode_t.ERR_EPS_FINISH.index) {
            errEpsFinishFunc = _onConfirmPopupOKButtonClick;
          } else if (bPIFErrorPointer.errorCode ==
              ErrCode_t.ERR_TAPERING_FINISH.index) {
            errTaperingFinishFunc = _onConfirmPopupOKButtonClick;
          }
          _reOpenCameraPenUI();
          return;
        }

        final imageInfo =
            UtlLibrary().apiBinding.getCurrentPatternImage().imageInfo;
        PreviewDataModel().updatePreviewImage(imageInfo);

        /// View更新
        ref
            .read(utilityViewModelProvider.notifier)
            .updateUtilityPageByChild(UtilityModuleType.patternSelector);

        state = state.copyWith(
          patternsStyle: _updateItemStyle(
            pattern_model.PatternDataModel().currentSelectedCategoryIndex,
            pattern_model.PatternDataModel().currentSelectedPatternIndex,
          ),
        );
      });
    } else {
      final imageInfo =
          UtlLibrary().apiBinding.getCurrentPatternImage().imageInfo;
      PreviewDataModel().updatePreviewImage(imageInfo);

      ref
          .read(utilityViewModelProvider.notifier)
          .updateUtilityPageByChild(UtilityModuleType.patternSelector);

      state = state.copyWith(
        patternsStyle: _updateItemStyle(
          pattern_model.PatternDataModel().currentSelectedCategoryIndex,
          pattern_model.PatternDataModel().currentSelectedPatternIndex,
        ),
      );
    }
  }

  ///
  /// リスト内でジャンプ可能な座標の計算
  ///
  double _getJumpPosition(int patternCount, double maxOffset) {
    ///  Itemの高さ
    const double itemHeight = 168.0;

    ///  行の数
    int rowCount = patternCount ~/ 5;

    /// ジャンプ可能な最大座標
    double maxJumpPositioned = rowCount * itemHeight;

    if (maxJumpPositioned < 0) {
      maxJumpPositioned = 0;
    } else if (maxJumpPositioned > maxOffset) {
      maxJumpPositioned = maxOffset;
    }
    return maxJumpPositioned;
  }

  ///
  /// BH模様から普通模様に切り替えるには、カメラペンがオンになっていることを保証する必要があります
  ///
  void _reOpenCameraPenUI() {
    final isBHSlitLengthRecognition = TpdLibrary()
        .apiBinding
        .bpIFGetAppDisplayUtl()
        .utlFuncSetting
        .ref
        .isBHSlitLengthRecognition;
    ref
        .read(utilityPreviewViewModeProvider.notifier)
        .startOrStopUpdateBHProject(isBHSlitLengthRecognition);
    if (ProjectorModel().isUtlProjectorOpened(UtlProjectorType.sewingPattern) ==
        true) {
      if (pattern_model.PatternDataModel().checkBHPatten() != true &&
          ProjectorModel().cameraPen.isCameraOn() != true) {
        ref
            .read(cameraPenViewModelProvider.notifier)
            .initCameraPenAndShowCameraPenUI();
      } else {
        /// Do Nothing
      }
    } else {
      /// Do Nothing
    }
  }
}
