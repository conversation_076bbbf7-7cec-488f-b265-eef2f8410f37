import 'dart:ffi';
import 'dart:typed_data';

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../model/projector_model.dart';
import '../../../../model/provider/app_display_state_provider.dart';
import '../../../app/emb/model/sewing_model.dart';
import '../../../app/utl/model/camera_image_model.dart';
import '../../panel_popup_route.dart';
import 'stitch_camera_popup_interface.dart';

final stitchCameraProvider = StateNotifierProvider.autoDispose.family<
    StitchCameraPopupViewModelInterface,
    StitchCameraPopupState,
    BuildContext>((ref, context) => StitchCameraViewModel(ref, context));

class StitchCameraViewModel extends StitchCameraPopupViewModelInterface {
  StitchCameraViewModel(ref, BuildContext context)
      : super(const StitchCameraPopupState(), context, ref) {
    _openListener();

    /// カメラを起動したとき、プロジェクターの投影は一時的に停止する
    if (SewingModel().isAutoOneDirection()) {
      ProjectorModel().embProjector.temporaryStopProjectorStart();
    } else {
      /// DoNothing
    }

    CameraImageModel.cameraImagePollingControl
        .startCameraImagePollingAndRefreshUI();

    /// カメラ画像を取得して、画像表示を更新します
    CameraImageModel.cameraImagePollingControl.registerCameraImageCallBack(
      _refreshCameraImageUi,
    );
    ref.onDispose(() {
      CameraImageModel.cameraImagePollingControl
          .maybeUnregisterCameraImageCallBack(
        _refreshCameraImageUi,
      );
      CameraImageModel.cameraImagePollingControl
          .stopCameraImagePollingAndRefreshUI();

      /// カメラを閉じたとき、プロジェクターの投影は再び開始する
      if (SewingModel().isAutoOneDirection()) {
        ProjectorModel().embProjector.temporaryStopProjectorStop();
      } else {
        /// DoNothing
      }
    });
  }

  ///
  /// NeedlePosition設定
  ///
  @override
  void onNeedlePositionButtonClicked() {
    final EmbLibraryError errCode =
        EmbLibrary().apiBinding.embSewingChangeNeedleViewCameraFb();
    if (errCode == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
        errCode == EmbLibraryError.EMB_INVALID_ERR_PANEL) {
      return;
    }

    if (errCode != EmbLibraryError.EMB_NO_ERR) {
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);
  }

  ///
  /// Zoom設定 out
  ///
  @override
  void onZoomOutButtonClicked() {
    final errCode = EmbLibrary().apiBinding.embSewingZoomCameraFb();

    if (errCode == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    if (errCode != EmbLibraryError.EMB_NO_ERR) {
      Log.hello("unknown error [1]");
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
  }

  ///
  /// Zoom設定 in
  ///
  @override
  void onZooMinButtonClicked() {
    final errCode = EmbLibrary().apiBinding.embSewingZoomCameraFb();

    if (errCode == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    if (errCode != EmbLibraryError.EMB_NO_ERR) {
      Log.hello("unknown error [1]");
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
  }

  ///
  /// 自分を閉じる
  ///
  @override
  void onCloseButtonClicked(BuildContext context) {
    final EmbLibraryError errCode =
        EmbLibrary().apiBinding.embSewingCloseCameraFb();
    if (errCode == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
        errCode == EmbLibraryError.EMB_INVALID_ERR_PANEL) {
      return;
    }

    if (errCode != EmbLibraryError.EMB_NO_ERR) {
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    CameraImageModel.cameraImagePollingControl
        .stopCameraImagePollingAndRefreshUI();

    /// 自分を閉じる
    PanelPopupRoute().closeSelf(context: context);
  }

  ///
  /// 縫製情報データ変更監視がONになっている
  ///
  void _openListener() {
    /// ズーム状態か
    ref.listen(
      fireImmediately: true,
      appDisplayGlobalStateProvider
          .select((value) => value.changeViewState.ref.isZoom),
      (previous, isZoom) {
        state = state.copyWith(isZoomout: !isZoom);
      },
    );

    /// 針落ち点表示が有効か？
    ref.listen(
      fireImmediately: true,
      appDisplayGlobalStateProvider
          .select((value) => value.changeViewState.ref.isNeedlePosition),
      (previous, nextState) {
        state = state.copyWith(isNeedlePosition: nextState);
      },
    );
  }

  ///
  /// カメラ画像更新
  ///
  void _refreshCameraImageUi(Uint8List? image) {
    if (mounted == false) {
      Log.hello("mounted error");
      return;
    }

    if (image == null) {
      return;
    }

    state = state.copyWith(
      stitchImage: Image.memory(
        image,
        gaplessPlayback: true,
      ),
    );
  }
}
