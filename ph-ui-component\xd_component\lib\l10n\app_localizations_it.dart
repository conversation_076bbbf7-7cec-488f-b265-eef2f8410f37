import 'app_localizations.dart';

/// The translations for Italian (`it`).
class AppLocalizationsIt extends AppLocalizations {
  AppLocalizationsIt([String locale = 'it']) : super(locale);

  @override
  String get color_001 => 'ROSA';

  @override
  String get color_002 => 'ROSA POLVERE';

  @override
  String get color_003 => 'ROSA PETALO';

  @override
  String get color_004 => 'ROSA CHIARO';

  @override
  String get color_005 => 'CORALLO\nCHIARO';

  @override
  String get color_006 => 'VERDE\nBOTTIGLIA';

  @override
  String get color_007 => 'VIOLA ERICA';

  @override
  String get color_008 => 'CHAMPAGNE';

  @override
  String get color_009 => 'MALVA SCURO';

  @override
  String get color_010 => 'ERICA';

  @override
  String get color_011 => 'ROSA\nCOMFORT';

  @override
  String get color_012 => 'ROSA ANTICO';

  @override
  String get color_013 => 'ROSA CILIEGIA';

  @override
  String get color_014 => 'CREMISI';

  @override
  String get color_015 => 'SALMONE';

  @override
  String get color_016 => 'GAMBERO';

  @override
  String get color_017 => 'CORALLO\nSCURO';

  @override
  String get color_018 => 'SALMONE\nSCURO';

  @override
  String get color_019 => 'ROSSO\nBORGOGNA';

  @override
  String get color_020 => 'ROSSO VINO';

  @override
  String get color_021 => 'ROSSO BRUNO';

  @override
  String get color_022 => 'PRUGNA';

  @override
  String get color_023 => 'PALISSANDRO';

  @override
  String get color_024 => 'ROSSO\nARAZZO';

  @override
  String get color_025 => 'ROSA CALDO';

  @override
  String get color_026 => 'RUBINO';

  @override
  String get color_027 => 'FUCSIA SCURO';

  @override
  String get color_028 => 'ROSSO\nCARMINIO';

  @override
  String get color_029 => 'ROSA INTENSO';

  @override
  String get color_030 => 'BEGONIA';

  @override
  String get color_031 => 'AZALEA';

  @override
  String get color_032 => 'FUCSIA\nVIVACE';

  @override
  String get color_033 => 'FRAGOLA';

  @override
  String get color_034 => 'ROSSO\nINFERNO';

  @override
  String get color_035 => 'ROSSO MELA';

  @override
  String get color_036 => 'ALCEA';

  @override
  String get color_037 => 'GRANATA';

  @override
  String get color_038 => 'FUOCO';

  @override
  String get color_039 => 'ROSSO';

  @override
  String get color_040 => 'ROSSO\nPOMODORO';

  @override
  String get color_041 => 'ROSSO\nVIVACE';

  @override
  String get color_042 => 'ROSSO\nLAMPONE';

  @override
  String get color_043 => 'ROSSO VOLPE';

  @override
  String get color_044 => 'ROSSETTO';

  @override
  String get color_045 => 'ROSSO\nNATALE';

  @override
  String get color_046 => 'SCARLATTO';

  @override
  String get color_047 => 'SCARLATTO\nSCURO';

  @override
  String get color_048 => 'MIRTILLO';

  @override
  String get color_049 => 'ROSA CARNE';

  @override
  String get color_050 => 'BISCOTTO';

  @override
  String get color_051 => 'ARANCIO\nTENUE';

  @override
  String get color_052 => 'MELONE';

  @override
  String get color_053 => 'PESCA MELBA';

  @override
  String get color_054 => 'ARANCIO PURO';

  @override
  String get color_055 => 'ARANCIO\nSCURO';

  @override
  String get color_056 => 'AZZURRO\nCHIARO';

  @override
  String get color_057 => 'BLU CIELO';

  @override
  String get color_058 => 'AZZURRO\nGHIACCIO';

  @override
  String get color_059 => 'CIANO';

  @override
  String get color_060 => 'CARTA DA\nZUCCHERO';

  @override
  String get color_061 => 'AZZURRO\nPASTELLO';

  @override
  String get color_062 => 'CELESTE BABY';

  @override
  String get color_063 => 'AZZURRO\nCIELO';

  @override
  String get color_064 => 'CELESTE';

  @override
  String get color_065 => 'BLU LAGO';

  @override
  String get color_066 => 'BLU FELICITÀ';

  @override
  String get color_067 => 'BLU INTENSO';

  @override
  String get color_068 => 'BLU TROPICI';

  @override
  String get color_069 => 'BLU\nFIORDALISO';

  @override
  String get color_070 => 'BLU LUNARE';

  @override
  String get color_071 => 'ZAFFIRO';

  @override
  String get color_072 => 'BLU ARDESIA';

  @override
  String get color_073 => 'BLU SCURO';

  @override
  String get color_074 => 'BLU PROFONDO';

  @override
  String get color_075 => 'BLU FIUME';

  @override
  String get color_076 => 'BLU BALTICO';

  @override
  String get color_077 => 'BLU\nCALIFORNIA';

  @override
  String get color_078 => 'CERULEO';

  @override
  String get color_079 => 'BLU LUCE';

  @override
  String get color_080 => 'BLU ELETTRICO';

  @override
  String get color_081 => 'BLU PACIFICO';

  @override
  String get color_082 => 'AZZURRO';

  @override
  String get color_083 => 'BLU IMPERIALE';

  @override
  String get color_084 => 'BLU LAGUNA';

  @override
  String get color_085 => 'BLU\nCOCCARDA';

  @override
  String get color_086 => 'BLU MARINO\nCHIARO';

  @override
  String get color_087 => 'BLU MARINO';

  @override
  String get color_088 => 'BLU IMPERO';

  @override
  String get color_089 => 'BLU NOTTE';

  @override
  String get color_090 => 'ZAFFIRO\nSCURO';

  @override
  String get color_091 => 'BLU OPACO';

  @override
  String get color_092 => 'BLU\nOLTREMARE';

  @override
  String get color_093 => 'BLU REALE';

  @override
  String get color_094 => 'BLU COBALTO';

  @override
  String get color_095 => 'BLU DI\nPRUSSIA';

  @override
  String get color_096 => 'BLU NASSAU';

  @override
  String get color_097 => 'BLU CINA';

  @override
  String get color_098 => 'BLU PARIGI';

  @override
  String get color_099 => 'BLU LAVANDA';

  @override
  String get color_100 => 'LAVANDA\nMEDIO';

  @override
  String get color_101 => 'LAVANDA';

  @override
  String get color_102 => 'TULIPANO\nLAVANDA';

  @override
  String get color_103 => 'GLICINE';

  @override
  String get color_104 => 'VIOLA REGINA';

  @override
  String get color_105 => 'LAVANDA\nLIVIDO';

  @override
  String get color_106 => 'BLU GENZIANA';

  @override
  String get color_107 => 'VIOLA BLU';

  @override
  String get color_108 => 'VIOLA\nPROFONDO';

  @override
  String get color_109 => 'VIOLA\nINTENSO';

  @override
  String get color_110 => 'VIOLA RE';

  @override
  String get color_111 => 'ROSSO-\nVIOLACEO';

  @override
  String get color_112 => 'VIOLA MOD';

  @override
  String get color_113 => 'VIOLA';

  @override
  String get color_114 => 'VIOLETTO';

  @override
  String get color_115 => 'VIOLETTO\nPALLIDO';

  @override
  String get color_116 => 'MAGENTA';

  @override
  String get color_117 => 'LILLA CHIARO';

  @override
  String get color_118 => 'LILLA';

  @override
  String get color_119 => 'IRIS DELLA\nSIBERIA';

  @override
  String get color_120 => 'ROSA\nFANTASIA';

  @override
  String get color_121 => 'ROSA SOGNO';

  @override
  String get color_122 => 'ROSA\nESCLUSIVO';

  @override
  String get color_123 => 'ROSA\nSPLENDORE';

  @override
  String get color_124 => 'ROSA\nSELVATICO';

  @override
  String get color_125 => 'ROSSO AMBRA';

  @override
  String get color_126 => 'LAMPONE\nGHIACCIO';

  @override
  String get color_127 => 'MELANZANA';

  @override
  String get color_128 => 'BLU BAMBINO';

  @override
  String get color_129 => 'BLU TURCHESE';

  @override
  String get color_130 => 'ACQUAMARINA';

  @override
  String get color_131 => 'BLU GLICINE';

  @override
  String get color_132 => 'BLU ANGELO';

  @override
  String get color_133 => 'BLU GERMANO';

  @override
  String get color_134 => 'BLU PAVONE';

  @override
  String get color_135 => 'GIADA SCURO';

  @override
  String get color_136 => 'VERDE\nPARADISO';

  @override
  String get color_137 => 'BLU COLOMBA';

  @override
  String get color_138 => 'BLU VENERE';

  @override
  String get color_139 => 'AZZURRO\nPIUMA';

  @override
  String get color_140 => 'GRIGIOVERDE';

  @override
  String get color_141 => 'VERDE MARE';

  @override
  String get color_142 => 'TURCHESE';

  @override
  String get color_143 => 'ALZAVOLA\nMISTICO';

  @override
  String get color_144 => 'ACQUA';

  @override
  String get color_145 => 'VERDE\nOCEANO';

  @override
  String get color_146 => 'TURCHESE\nTEMPESTA';

  @override
  String get color_147 => 'VERDE MEDIO';

  @override
  String get color_148 => 'ALZAVOLA\nSCURO';

  @override
  String get color_149 => 'ACQUA\nOCEANO';

  @override
  String get color_150 => 'BLU ELEGANZA';

  @override
  String get color_151 => 'NEWPORT';

  @override
  String get color_152 => 'VERDE ALLORO';

  @override
  String get color_153 => 'CRESTA DI\nMARE';

  @override
  String get color_154 => 'VERDE KIWI';

  @override
  String get color_155 => 'VERDE OLIVA';

  @override
  String get color_156 => 'VERDE PORTO';

  @override
  String get color_157 => 'VERDE\nSPECIALE';

  @override
  String get color_158 => 'VERDE\nMILITARE SC.';

  @override
  String get color_159 => 'VERDE\nPROFONDO';

  @override
  String get color_160 => 'VERDE ALPINO';

  @override
  String get color_161 => 'VERDE ERBA';

  @override
  String get color_162 => 'VERDE NERO';

  @override
  String get color_163 => 'ALZAVOLA\nNOTTURNO';

  @override
  String get color_164 => 'AZZURRO\nALGA';

  @override
  String get color_165 => 'VERDE SALICE';

  @override
  String get color_166 => 'FOGLIA DI TÈ';

  @override
  String get color_167 => 'VERDE ISOLA';

  @override
  String get color_168 => 'PINO';

  @override
  String get color_169 => 'GIADA';

  @override
  String get color_170 => 'MENTA\nPIPERITA';

  @override
  String get color_171 => 'VERDE\nANATRA';

  @override
  String get color_172 => 'VERDE\nINTENSO';

  @override
  String get color_173 => 'VERDE\nCLASSICO';

  @override
  String get color_174 => 'VERDE PINO\nSCURO';

  @override
  String get color_175 => 'VERDE';

  @override
  String get color_176 => 'VERDE\nIRLANDA';

  @override
  String get color_177 => 'VERDE\nSMERALDO';

  @override
  String get color_178 => 'TRIFOGLIO DI\nIRLANDA';

  @override
  String get color_179 => 'VERDE\nMUSCHIO';

  @override
  String get color_180 => 'VERDE KELLY\nCHIARO';

  @override
  String get color_181 => 'VERDE KELLY';

  @override
  String get color_182 => 'VERDE PRATO';

  @override
  String get color_183 => 'VERDE TENUE';

  @override
  String get color_184 => 'QUERCIA\nVERDE';

  @override
  String get color_185 => 'VERDE MENTA';

  @override
  String get color_186 => 'VERDE MELA';

  @override
  String get color_187 => 'TERRA VERDE';

  @override
  String get color_188 => 'VERDE\nCAMPAGNA';

  @override
  String get color_189 => 'AVOCADO\nCHIARO';

  @override
  String get color_190 => 'VERDE\nRACCOLTO';

  @override
  String get color_191 => 'VERDE\nPOLVERE';

  @override
  String get color_192 => 'VERDE TIGLIO';

  @override
  String get color_193 => 'VERDE ERIN';

  @override
  String get color_194 => 'VERDE\nFOGLIAME';

  @override
  String get color_195 => 'GIRASOLE';

  @override
  String get color_196 => 'ORO';

  @override
  String get color_197 => 'VERDE\nAUTUNNO';

  @override
  String get color_198 => 'VERDONE';

  @override
  String get color_199 => 'PRATO';

  @override
  String get color_200 => 'SALVIA';

  @override
  String get color_201 => 'OLIVA SCURO';

  @override
  String get color_202 => 'VERDE\nMILITARE';

  @override
  String get color_203 => 'ORO\nBRILLANTE';

  @override
  String get color_204 => 'GIALLO LIMONE';

  @override
  String get color_205 => 'GIALLO\nVIVACE';

  @override
  String get color_206 => 'GIALLO';

  @override
  String get color_207 => 'ORO OPACO';

  @override
  String get color_208 => 'MANILA';

  @override
  String get color_209 => 'VERGA D\'ORO';

  @override
  String get color_210 => 'GIALLO SOLE';

  @override
  String get color_211 => 'GIALLO\nPOLLINE';

  @override
  String get color_212 => 'GIALLO\nMELONE';

  @override
  String get color_213 => 'ORO STELLA';

  @override
  String get color_214 => 'SOLE DORATO';

  @override
  String get color_215 => 'OTTONE';

  @override
  String get color_216 => 'ARANCIO';

  @override
  String get color_217 => 'ORO SCURO';

  @override
  String get color_218 => 'GIALLO GRANO';

  @override
  String get color_219 => 'GIALLO OPACO';

  @override
  String get color_220 => 'SENAPE';

  @override
  String get color_221 => 'RAME';

  @override
  String get color_222 => 'ARANCIO\nSALMONE';

  @override
  String get color_223 => 'ARANCIATA';

  @override
  String get color_224 => 'PAPRICA';

  @override
  String get color_225 => 'ROSSO\nVERMIGLIO';

  @override
  String get color_226 => 'ZAFFERANO';

  @override
  String get color_227 => 'RAMATO';

  @override
  String get color_228 => 'TERRA COTTA';

  @override
  String get color_229 => 'RUGGINE\nSCURO';

  @override
  String get color_230 => 'MELONE\nSCURO';

  @override
  String get color_231 => 'FULVO';

  @override
  String get color_232 => 'PESCA CHIARO';

  @override
  String get color_233 => 'TERRA DI\nSIENA';

  @override
  String get color_234 => 'ALBICOCCA\nSCURO';

  @override
  String get color_235 => 'MANDARINO';

  @override
  String get color_236 => 'GIALLO ZUCCA';

  @override
  String get color_237 => 'ARAGOSTA';

  @override
  String get color_238 => 'SPEZIE\nDORATE';

  @override
  String get color_239 => 'MIELE';

  @override
  String get color_240 => 'MANDORLA';

  @override
  String get color_241 => 'MARRONE\nBRUCIATO';

  @override
  String get color_242 => 'MARRONE\nARGILLA';

  @override
  String get color_243 => 'RUGGINE';

  @override
  String get color_244 => 'NOCCIOLA';

  @override
  String get color_245 => 'GIALLO CREMA';

  @override
  String get color_246 => 'GIALLO AMBRA';

  @override
  String get color_247 => 'PISTACCHIO';

  @override
  String get color_248 => 'TABACCO\nDORATO';

  @override
  String get color_249 => 'ZENZERO';

  @override
  String get color_250 => 'ORO\nPROFONDO';

  @override
  String get color_251 => 'TABACCO';

  @override
  String get color_252 => 'TABACCO\nCHIARO';

  @override
  String get color_253 => 'ALBICOCCA';

  @override
  String get color_254 => 'BEIGE';

  @override
  String get color_255 => 'MALACCA';

  @override
  String get color_256 => 'BEIGE SCURO';

  @override
  String get color_257 => 'BRONZO';

  @override
  String get color_258 => 'CAFFÈ';

  @override
  String get color_259 => 'LINO';

  @override
  String get color_260 => 'CONCHIGLIA';

  @override
  String get color_261 => 'ECRU';

  @override
  String get color_262 => 'ROSA\nSALMONE';

  @override
  String get color_263 => 'COCCO\nCHIARO';

  @override
  String get color_264 => 'PESCA\nPOLVERE';

  @override
  String get color_265 => 'MARRONE\nCHIARO';

  @override
  String get color_266 => 'KAKI';

  @override
  String get color_267 => 'CHICCO DI\nCAFFÈ';

  @override
  String get color_268 => 'MARRONE\nARENARIA';

  @override
  String get color_269 => 'MARRONE\nPROFONDO';

  @override
  String get color_270 => 'MARRONE\nSCURO';

  @override
  String get color_271 => 'MARRONE';

  @override
  String get color_272 => 'TERRACOTTA\nVIVACE';

  @override
  String get color_273 => 'GRIGIO\nMARRONE';

  @override
  String get color_274 => 'GRIGIO CALDO';

  @override
  String get color_275 => 'GRIGIO\nINTENSO';

  @override
  String get color_276 => 'METALLO';

  @override
  String get color_277 => 'NERO\nCROMATO';

  @override
  String get color_278 => 'CARBONE';

  @override
  String get color_279 => 'GRIGIO MEDIO';

  @override
  String get color_280 => 'GRIGIO LUCE';

  @override
  String get color_281 => 'GRIGIO FUMO';

  @override
  String get color_282 => 'PELTRO';

  @override
  String get color_283 => 'GRIGIO SCURO';

  @override
  String get color_284 => 'GRIGIO';

  @override
  String get color_285 => 'GRIGIO\nCHIARO';

  @override
  String get color_286 => 'CROMO';

  @override
  String get color_287 => 'ORO ANTICO';

  @override
  String get color_288 => 'ARGENTO';

  @override
  String get color_289 => 'NERO';

  @override
  String get color_290 => 'BIANCO\nNATURALE';

  @override
  String get color_291 => 'BIANCO';

  @override
  String get color_292 => 'ROSA NEON';

  @override
  String get color_293 => 'ROSA SCURO';

  @override
  String get color_294 => 'ANGURIA';

  @override
  String get color_295 => 'ROSA TENUE';

  @override
  String get color_296 => 'MELONE\nCANTALUPO';

  @override
  String get color_297 => 'PESCA OPACO';

  @override
  String get color_298 => 'GIALLO\nAVANA';

  @override
  String get color_299 => 'CILIEGIA\nSCURO';

  @override
  String get color_300 => 'BLU\nINCHIOSTRO';

  @override
  String get color_301 => 'MATERIALE\nAPPLICAZIONE';

  @override
  String get color_302 => 'POSIZIONE\nAPPLICAZIONE';

  @override
  String get color_303 => 'APPLICAZIONE';

  @override
  String get id_icon_test00001 => '\$\$\$\$\$';

  @override
  String get icon_00002 => 'Cucito';

  @override
  String get icon_00003_1 => 'Ricamo';

  @override
  String get icon_00006_3 => 'Punto utile';

  @override
  String get icon_00007_3 => 'Carattere/\npunto\ndecorativo';

  @override
  String get icon_stitch => 'Punto';

  @override
  String get icon_close_1 => 'Chiudi';

  @override
  String get icon_cancel => 'Cancella';

  @override
  String get icon_ok => 'OK';

  @override
  String get icon_00011_zz => '%%%icon%%%';

  @override
  String get icon_00011_zz_s => '%%%icon%%%';

  @override
  String get icon_00011 => 'Elimina';

  @override
  String get icon_00012_zz => '%%%icon%%%';

  @override
  String get icon_00012_zz_s => '%%%icon%%%';

  @override
  String get icon_reset_zz => '%%%icon%%%';

  @override
  String get icon_reset_zz_s => '%%%icon%%%';

  @override
  String get icon_reset => 'Reimposta';

  @override
  String get icon_reset_v => 'Reimposta';

  @override
  String get icon_00014_zz => '%%%icon%%%';

  @override
  String get icon_00014_zz_s => '%%%icon%%%';

  @override
  String get icon_00014 => 'Memoria';

  @override
  String get icon_save => 'Salva';

  @override
  String get icon_00015_zz => '%%%icon%%%';

  @override
  String get icon_00015_zz_s => '%%%icon%%%';

  @override
  String get icon_util_mem_retrieve => 'Recupera';

  @override
  String get icon_util_mem_memory => 'Memoria';

  @override
  String get icon_util_mem_reset => 'Reimposta';

  @override
  String get icon_util_mem_delete => 'Elimina';

  @override
  String get icon_util_mem_alldelete => 'Cancella tutto';

  @override
  String get icon_00017_zz => '%%%icon%%%';

  @override
  String get icon_00017_zz_s => '%%%icon%%%';

  @override
  String get icon_00018_zz => '%%%icon%%%';

  @override
  String get icon_00018_zz_s => '%%%icon%%%';

  @override
  String get icon_00019_zz => '%%%icon%%%';

  @override
  String get icon_00019_zz_s => '%%%icon%%%';

  @override
  String get icon_00020_zz => '%%%icon%%%';

  @override
  String get icon_00020_zz_s => '%%%icon%%%';

  @override
  String get icon_util_width => 'Larghezza';

  @override
  String get icon_util_length => 'Lunghezza';

  @override
  String get icon_util_lrshift => 'Spostam S/D';

  @override
  String get icon_util_tension => 'Tensione';

  @override
  String get icon_util_slitlength => 'Taglio';

  @override
  String get icon_00021_zz => '%%%icon%%%';

  @override
  String get icon_00021_zz_s => '%%%icon%%%';

  @override
  String get icon_00022_zz => '%%%icon%%%';

  @override
  String get icon_00022_zz_s => '%%%icon%%%';

  @override
  String get icon_00027_zz => '%%%icon%%%';

  @override
  String get icon_00027_zz_s => '%%%icon%%%';

  @override
  String get icon_00028_zz => '%%%icon%%%';

  @override
  String get icon_00028_zz_s => '%%%icon%%%';

  @override
  String get icon_00029_zz => '%%%icon%%%';

  @override
  String get icon_00029_zz_s => '%%%icon%%%';

  @override
  String get icon_00038_zz => '%%%icon%%%';

  @override
  String get icon_00038_zz_s => '%%%icon%%%';

  @override
  String get icon_00030_1 => 'Prova';

  @override
  String get icon_guidel_guideline => 'Linea guida';

  @override
  String get icon_guidel_main => 'Principale';

  @override
  String get icon_guidel_sub => 'Secondaria';

  @override
  String get icon_guidel_mainline => 'Linea principale';

  @override
  String get icon_guidel_subline => 'Linea\nsecondaria';

  @override
  String get icon_guidel_linelength => 'Lunghezza linea';

  @override
  String get icon_guidel_line_l => 'L';

  @override
  String get icon_guidel_line_m => 'M';

  @override
  String get icon_guidel_line_s => 'S';

  @override
  String get icon_guidel_color => 'Colore';

  @override
  String get icon_guidel_position => 'Posizione';

  @override
  String get icon_guidel_main_pos => 'Posizione linea principale';

  @override
  String get icon_guidel_sub_pos => 'Posizione linea secondaria';

  @override
  String get icon__guidel_sub_frommain => 'Distanza dalla linea principale';

  @override
  String get icon_guidel_gridsize => 'Dimensione griglia';

  @override
  String get icon_guidel_angle => 'Angolo';

  @override
  String get icon_guidel_seamallowance => 'Margine di cucitura';

  @override
  String get icon_guidel_spacing => 'Spaziatura';

  @override
  String get icon_guidel_lengthl_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengthl_zz_s => '%%%icon%%%';

  @override
  String get icon_guidel_lengthm_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengthm_zz_s => '%%%icon%%%';

  @override
  String get icon_guidel_lengths_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengths_zz_s => '%%%icon%%%';

  @override
  String get icon_position => 'Posizione';

  @override
  String get icon_00031_2 => 'Modifica';

  @override
  String get icon_00033_1 => 'Aggiungi';

  @override
  String get icon_00035 => 'Ricamo';

  @override
  String get icon_return => 'Ritorno';

  @override
  String get icon_00038_1 => 'Imposta';

  @override
  String get icon_00038_2 => 'Imposta';

  @override
  String get icon_00039 => 'min';

  @override
  String get icon_00041_1 => 'Selezionare';

  @override
  String get icon_select => 'Selezionare';

  @override
  String get icon_select_2 => 'Selezio-\nnare';

  @override
  String get icon_00041_2 => 'Selezio-\nnare';

  @override
  String get icon_00042 => 'Premere sempre quando\nsi rimuove unità ricamo.';

  @override
  String get icon_00046_zz => '%%%icon%%%';

  @override
  String get icon_00046_zz_s => '%%%icon%%%';

  @override
  String get icon_00048 => 'Numero';

  @override
  String get icon_00049 => 'Elenco';

  @override
  String get icon_00050 => 'Carica';

  @override
  String get icon_00051_zz => '%%%icon%%%';

  @override
  String get icon_00051_zz_s => '%%%icon%%%';

  @override
  String get icon_00052_zz => '%%%icon%%%';

  @override
  String get icon_00052_zz_s => '%%%icon%%%';

  @override
  String get icon_00053_b1 => '%%%none%%%';

  @override
  String get icon_00053_b2 => '%%%none%%%';

  @override
  String get icon_00053_t1 => 'Color Visualizer';

  @override
  String get icon_00053_t2 => 'Color\nVisualizer';

  @override
  String get icon_00055_1 => 'Casuale';

  @override
  String get icon_00055_2 => 'Casuale';

  @override
  String get icon_00056_1 => 'Gradazione';

  @override
  String get icon_00056_2 => 'Gradazione';

  @override
  String get icon_00057 => 'Vivido';

  @override
  String get icon_00054 => 'Tenue';

  @override
  String get icon_00058_1 => 'Numero di colori';

  @override
  String get icon_00059 => 'Aggiorna';

  @override
  String get icon_00060 => 'Nessuno specificato';

  @override
  String get icon_emb_tension => 'Tensione';

  @override
  String get icon_emb_threadcutting => 'Taglio del filo';

  @override
  String get icon_00063_a => 'Rifinitura fine colore';

  @override
  String get icon_00064_a => 'Rifinitura punti di collegamento';

  @override
  String get icon_00065 => 'Densità';

  @override
  String get icon_00066 => 'Dimen-sioni';

  @override
  String get icon_00067_zz => '%%%icon%%%';

  @override
  String get icon_00067_zz_s => '%%%icon%%%';

  @override
  String get icon_00068_zz => '%%%icon%%%';

  @override
  String get icon_00068_zz_s => '%%%icon%%%';

  @override
  String get icon_00070_zz => '%%%icon%%%';

  @override
  String get icon_00070_zz_s => '%%%icon%%%';

  @override
  String get icon_00071_zz => '%%%icon%%%';

  @override
  String get icon_00071_zz_s => '%%%icon%%%';

  @override
  String get icon_00072 => 'Disposizione';

  @override
  String get icon_00075_zz => '%%%icon%%%';

  @override
  String get icon_00075_zz_s => '%%%icon%%%';

  @override
  String get icon_00076_zz => '%%%icon%%%';

  @override
  String get icon_00076_zz_s => '%%%icon%%%';

  @override
  String get icon_00077_zz => '%%%icon%%%';

  @override
  String get icon_00077_zz_s => '%%%icon%%%';

  @override
  String get icon_00079 => 'Posizione discesa ago';

  @override
  String get icon_00080 => 'Avanti';

  @override
  String get icon_prev => 'Precedente';

  @override
  String get icon_segment => 'Segmento';

  @override
  String get icon_00083 => 'Impostazione punto di fine';

  @override
  String get icon_00084 => 'Regolazione lunghezza';

  @override
  String get icon_00085 => 'Impostazione punto di fine\nArresto temporaneo';

  @override
  String get icon_00088 => 'Acquisiz.';

  @override
  String get icon_00089 => 'Video';

  @override
  String get icon_00090 => 'Ripeti';

  @override
  String get icon_00091_1 => 'Selezione multipla';

  @override
  String get icon_00091_2 => 'Selezione\nmultipla';

  @override
  String get icon_00093_zz => '%%%icon%%%';

  @override
  String get icon_00093_zz_s => '%%%icon%%%';

  @override
  String get icon_00094_zz => '%%%icon%%%';

  @override
  String get icon_00094_zz_s => '%%%icon%%%';

  @override
  String get icon_00095 => 'Esci';

  @override
  String get icon_00096 => 'Divisione automatica delle fasce di trapuntatura';

  @override
  String get icon_resettodef => 'Ripristina impostazioni predefinite';

  @override
  String get icon_resettodefall => 'Ripristina impostazioni predefinite';

  @override
  String get icon_resettodefall_2 => 'Ripristina impostazioni\npredefinite';

  @override
  String get icon_00100 => 'Lingua';

  @override
  String get icon_00101_a => 'Italiano';

  @override
  String get icon_00101_b => 'Italian';

  @override
  String get icon_00102 => 'Luminosità';

  @override
  String get icon_00103 => 'Luminosità schermo';

  @override
  String get icon_00104 => 'Salva-schermo';

  @override
  String get icon_00105 => 'Predefinito';

  @override
  String get icon_00106 => 'Personalizza';

  @override
  String get icon_00107 => 'Modalità Eco';

  @override
  String get icon_00108 => 'Modalità Arresto assistito';

  @override
  String get icon_00109 => 'Luce';

  @override
  String get icon_00112 => 'Volume altoparlanti\ndella macchina';

  @override
  String get icon_00114 => 'Volume';

  @override
  String get icon_00115 => 'Puntatore del mouse';

  @override
  String get icon_00116 => 'mm / \"(inch)';

  @override
  String get icon_00118 => 'Home page';

  @override
  String get icon_00119 => 'Schermata\nCucito/Ricamo';

  @override
  String get icon_00192 => 'Schermata iniziale';

  @override
  String get icon_00121 => 'Schermata di\napertura';

  @override
  String get icon_00122 => 'Pagina iniziale Punti';

  @override
  String get icon_00123 => 'LED base multirocchetto';

  @override
  String get icon_00124 => 'Regolazione ampiezza\nzig-zag';

  @override
  String get icon_00125_1 => 'Regolazione verticale\ndi precisione';

  @override
  String get icon_00126_1 => 'Regolazione orizzontale\ndi precisione';

  @override
  String get icon_00127_1 => 'Altezza del piedino\npremistoffa';

  @override
  String get icon_00128_1 => 'Pressione del piedino\npremistoffa';

  @override
  String get icon_00129 => 'Posizione iniziale';

  @override
  String get icon_00130_1 => 'Altezza piedino\nfunzione pivot';

  @override
  String get icon_00131_1 => 'Altezza piedino\nin free motion';

  @override
  String get icon_00134 => 'Sensore di rilevamento\nautomatico spessore\ntessuto';

  @override
  String get icon_00135 => 'Sensore di rilevamento\ndello spessore del\ntessuto';

  @override
  String get icon_00136_2 => 'Posizione ago - su/giù';

  @override
  String get icon_00137 => 'Posizione ago -\nPosizionamento punto';

  @override
  String get icon_00138 => 'Sensore filo spolina\ne filo superiore';

  @override
  String get icon_00140 => 'Regolazione del\ndoppio trasporto';

  @override
  String get icon_00141 => 'Pedale reostato\nmultifunzione';

  @override
  String get icon_00142 => 'Talloniera';

  @override
  String get icon_00143 => 'Pedale laterale';

  @override
  String get icon_00144_a => 'Posizione ago\nsu/giù';

  @override
  String get icon_00145 => 'Taglio del filo';

  @override
  String get icon_00146 => 'Punto singolo';

  @override
  String get icon_00147 => 'Cucitura di\nritorno';

  @override
  String get icon_00243 => 'Piedino\npremistoffa\nsu/giù';

  @override
  String get icon_00244 => 'Nessuna\nimpostazione';

  @override
  String get icon_00249 => 'Punto di rinforzo';

  @override
  String get icon_00148 => 'Lunghezza taglio';

  @override
  String get icon_00148_zz => '%%%icon%%%';

  @override
  String get icon_00148_zz_s => '%%%icon%%%';

  @override
  String get icon_00150 => 'Priorità del rinforzo';

  @override
  String get icon_00152_1 => 'Visualizzazione del telaio\nda ricamo';

  @override
  String get icon_00155_1 => 'Dimensioni dell\'anteprima';

  @override
  String get icon_00157 => 'Colore sfondo ricamo';

  @override
  String get icon_00159 => 'Colore sfondo anteprima';

  @override
  String get icon_00163_a => 'Visualizzazione\ndell\'immagine di sfondo';

  @override
  String get icon_00163 => 'Immagine\ndello sfondo';

  @override
  String get icon_00164 => 'Acquisisci immagine';

  @override
  String get icon_00165 => 'Standard';

  @override
  String get icon_00166 => 'Migliore';

  @override
  String get icon_00167 => 'Qualità scansione';

  @override
  String get icon_00168 => 'LED base multirocchetto';

  @override
  String get icon_00178 => 'Lunghezza del punto\nimbastitura';

  @override
  String get icon_00180 => 'Distanza applicazione\ndal ricamo';

  @override
  String get icon_00182_1 => 'Velocità massima\ndi ricamo';

  @override
  String get icon_00183_1 => 'Tensione ricamo';

  @override
  String get icon_00184_1 => 'Altezza piedino da ricamo';

  @override
  String get icon_00185 => 'Dimensione telaio';

  @override
  String get icon_00186 => 'Griglia';

  @override
  String get icon_00187 => 'Modifica';

  @override
  String get icon_00188 => 'Elimina';

  @override
  String get icon_00191 => 'Colore';

  @override
  String get icon_00193 => 'Display orologio';

  @override
  String get icon_00194 => 'AM';

  @override
  String get icon_00195 => 'PM';

  @override
  String get icon_00196 => '24h';

  @override
  String get icon_clock_msg1 => 'Imposta la data corretta per la connessione di rete.';

  @override
  String get icon_00197 => 'Calibrazione funzione Sensore';

  @override
  String get icon_00199 => 'Regolazione indicatore\ndi guida';

  @override
  String get icon_00200 => 'Luminosità\nindicatore di guida';

  @override
  String get icon_00201_1 => 'Regolazione\npiedino da ricamo\ncon puntatore LED';

  @override
  String get icon_00202_p => 'Luminosità';

  @override
  String get icon_00206_1 => 'Attivazione';

  @override
  String get icon_00207_a => 'Kit';

  @override
  String get icon_00208 => 'Avvio';

  @override
  String get icon_00209 => 'Stop';

  @override
  String get icon_00211 => 'Contapunti parziale\ndal tagliando';

  @override
  String get icon_00212 => 'SCS';

  @override
  String get icon_00214 => 'Contapunti totale';

  @override
  String get icon_00218 => 'No.';

  @override
  String get icon_00220 => 'Versione';

  @override
  String get icon_00222 => 'YYYY';

  @override
  String get icon_00223 => 'MM';

  @override
  String get icon_00224 => 'DD';

  @override
  String get icon_00225 => 'mm';

  @override
  String get icon_00226 => '\"';

  @override
  String get icon_on => 'ON';

  @override
  String get icon_off => 'OFF';

  @override
  String get icon_00229 => 'KB';

  @override
  String get icon_00230 => 'bPocket';

  @override
  String get icon_00231 => '1';

  @override
  String get icon_00232 => '2';

  @override
  String get icon_00233 => '3';

  @override
  String get icon_00234 => '4';

  @override
  String get icon_00235 => '5';

  @override
  String get icon_00236 => '6';

  @override
  String get icon_00237 => '7';

  @override
  String get icon_00238 => '8';

  @override
  String get icon_00239 => '9';

  @override
  String get icon_00240 => '0';

  @override
  String get icon_00241 => 'C';

  @override
  String get icon_00242 => '%';

  @override
  String get icon_00245 => 'Sollevamento automatico\ndel piedino premistoffa';

  @override
  String get icon_00246 => 'Giù';

  @override
  String get icon_00247 => 'Su';

  @override
  String get icon_00248_zz => '%%%icon%%%';

  @override
  String get icon_00248_zz_s => '%%%icon%%%';

  @override
  String get icon_00248 => 'Premere per tagliare';

  @override
  String get icon_00251 => 'Proiettore';

  @override
  String get icon_00253 => 'Colore dello sfondo';

  @override
  String get icon_00254 => 'Cucitura:\nContorno del disegno';

  @override
  String get icon_00255 => 'Ricamo:\nColore puntatore';

  @override
  String get icon_pointershape => 'Forma del puntatore';

  @override
  String get icon_00256 => 'Fotocamera';

  @override
  String get icon_00257 => 'Calibrazione dell\'ago per\nfotocamera/proiettore';

  @override
  String get icon_recog_ok => 'OK';

  @override
  String get icon_recog_ng => 'NG';

  @override
  String get icon_00258 => 'Posizione\narresto ago ricamo';

  @override
  String get icon_00259 => 'Unità';

  @override
  String get icon_00260 => 'Colore filo';

  @override
  String get icon_00261 => 'Marchio del filo';

  @override
  String get icon_00264 => 'Nome Colore';

  @override
  String get icon_00265 => '# 123';

  @override
  String get icon_00266 => 'Tempo';

  @override
  String get icon_00268 => 'Originale';

  @override
  String get icon_00269 => 'Embroidery';

  @override
  String get icon_00269_t => 'Embroidery';

  @override
  String get icon_00270 => 'Country';

  @override
  String get icon_00270_t => 'Country';

  @override
  String get icon_00271 => 'Madeira\nPoly';

  @override
  String get icon_00272 => 'Madeira\nRayon';

  @override
  String get icon_00273 => 'Sulky';

  @override
  String get icon_00274 => 'Robison-Anton';

  @override
  String get icon_00275 => 'Robison-Anton\nPoly';

  @override
  String get icon_00276 => 'Robison-Anton\nRayon';

  @override
  String get icon_00277 => 'Isacord';

  @override
  String get icon_00278 => 'Gütermann';

  @override
  String get icon_00279 => 'Simplicity Pro';

  @override
  String get icon_00279_p => 'Pacesetter Pro';

  @override
  String get icon_00280 => 'Floriani';

  @override
  String get icon_00281 => 'Iris';

  @override
  String get icon_00282 => 'Aurifil';

  @override
  String get icon_00283 => 'WonderFil ';

  @override
  String get icon_00284 => 'Polyfast';

  @override
  String get icon_00290 => 'Se è stato acquistato un kit aggiornamento\ne si desidera attivarlo sulla macchina\npremere [ATTIVAZIONE].';

  @override
  String get icon_00291 => 'KIT I';

  @override
  String get icon_00292 => 'KIT II';

  @override
  String get icon_00293 => 'KIT III';

  @override
  String get icon_00294 => 'KIT IV';

  @override
  String get icon_00295 => 'KIT V';

  @override
  String get icon_00296 => 'KIT VI';

  @override
  String get icon_00297 => 'KIT VII';

  @override
  String get icon_00298 => 'KIT VIII';

  @override
  String get icon_00299 => 'KIT IX';

  @override
  String get icon_00300 => 'KIT X';

  @override
  String get icon_00643_s => 'Nessuno';

  @override
  String get icon_00301 => 'Guida al funzionamento';

  @override
  String get icon_00302 => 'Guida alla cucitura';

  @override
  String get icon_00303 => 'Spiegazione dei disegni';

  @override
  String get icon_manuals => 'Manuali';

  @override
  String get icon_operariong_b => 'Manuale di istruzioni';

  @override
  String get icon_operariong_t => 'Guida di istruzioni e riferimento (English)';

  @override
  String get icon_pdf => 'Manuale in PDF';

  @override
  String get icon_supportsite => 'Sito Supporto';

  @override
  String get icon_pdf_eula => 'Accordi di licenza con l\'utente\nfinale (EULA)';

  @override
  String get icon_pdf_sewing => 'Cucitura';

  @override
  String get icon_pdf_emb => 'Ricamo';

  @override
  String get icon_pdf_sewing_ef => 'Cucitura (English)';

  @override
  String get icon_pdf_emb_ef => 'Ricamo (English)';

  @override
  String get icon_pdf_sewing_t => 'Cucitura (English)';

  @override
  String get icon_pdf_emb_t => 'Ricamo (English)';

  @override
  String get icon_f_omadendum => 'Supplemento';

  @override
  String get icon_f_omadendum_ef => 'Supplemento (English)';

  @override
  String get icon_f_omadendum_l => 'Supplemento\nal Manuale di istruzioni';

  @override
  String get icon_f_om_kit1 => 'KIT I';

  @override
  String get icon_f_om_kit2 => 'KIT II';

  @override
  String get icon_f_om_kit3 => 'KIT III';

  @override
  String get icon_f_om_kit1_l => 'Manuale di istruzioni\nKIT I';

  @override
  String get icon_f_omadendum_t => 'Supplemento (English)';

  @override
  String get icon_f_om_kit1_t => 'KIT I (English)';

  @override
  String get icon_f_om_kit2_t => 'KIT II (English)';

  @override
  String get icon_f_om_kit3_t => 'KIT III (English)';

  @override
  String get icon_t_pdf_iivo_url_b => 'Se si desidera visualizzare i manuali su dispositivo mobile o PC, accedere a\nhttps://s.brother/fmraa.';

  @override
  String get icon_t_pdf_iivo_url_t => 'Se si desidera visualizzare il manuale su dispositivo mobile o PC, accedere a\nhttps://babylock.com/radiance-instruction-and-reference-guide.';

  @override
  String get icon_t_video_iivo_url_b => 'Visitare il sito\n https://s.brother/fvraa\nper visualizzare i tutorial video su questo modello.';

  @override
  String get icon_t_video_iivo_url_t => 'Visitare il sito\n https://babylock.com/radiance-training\nper visualizzare i tutorial video su questo modello.';

  @override
  String get icon_pdf_url_qr_t => 'www.babylock.com';

  @override
  String get icon_nettool => 'Strumento di diagnosi di rete';

  @override
  String get icon_iagree => 'Accetto';

  @override
  String get icon_terms_cancel => 'Annulla';

  @override
  String get icon_confirm => 'Conferma';

  @override
  String get icon_00304 => 'Parti principali';

  @override
  String get icon_00305 => 'Pulsanti principali';

  @override
  String get icon_00306 => 'Funzionamento generale';

  @override
  String get icon_00307 => 'Funzionamento generale\nricamo';

  @override
  String get icon_00308 => 'Individuazione Guasti';

  @override
  String get icon_00309 => 'Manutenzione';

  @override
  String get icon_00310 => 'Il filo risulta aggrovigliato sul\nrovescio del tessuto';

  @override
  String get icon_00311 => 'Impossibile infilare l\'ago';

  @override
  String get icon_00312 => 'Impossibile utilizzare\nl\'infila ago';

  @override
  String get icon_00313 => 'Tensione del filo non corretta';

  @override
  String get icon_00314 => 'Rottura del filo superiore';

  @override
  String get icon_00315 => 'Rotture del filo della spolina';

  @override
  String get icon_00316 => 'Punti saltati';

  @override
  String get icon_00317 => 'Rottura dell\'ago';

  @override
  String get icon_00318 => 'La macchina non funziona';

  @override
  String get icon_00320 => 'Carattere da ricamare non\nrealizzato';

  @override
  String get icon_00321 => 'Il tessuto non viene alimentato\nnella macchina';

  @override
  String get icon_00322 => 'Arricciamenti del tessuto';

  @override
  String get icon_00323 => 'La macchina è rumorosa';

  @override
  String get icon_00325 => 'Il disegno da ricamo non viene\ncucito in modo corretto';

  @override
  String get icon_00326 => 'L\'unità da ricamo non funziona';

  @override
  String get icon_00331 => 'Travetta';

  @override
  String get icon_00332 => 'Punto invisibile';

  @override
  String get icon_00333 => 'Asola';

  @override
  String get icon_00334 => 'Bottoni';

  @override
  String get icon_00335 => 'Cintura pinces';

  @override
  String get icon_00336 => 'Cucitura di ribattitura\npiatta';

  @override
  String get icon_00337 => 'Arricciatura';

  @override
  String get icon_00338 => 'Sopraggitto';

  @override
  String get icon_00339 => 'Nervatura';

  @override
  String get icon_00340 => 'Punto smerlo';

  @override
  String get icon_00341 => 'Punto diritto';

  @override
  String get icon_00342 => 'Inserimento cerniera';

  @override
  String get icon_00343 => 'Rammendo';

  @override
  String get icon_00344 => 'Trapuntatura a moto\nlibero';

  @override
  String get icon_00345 => 'Trapuntatura';

  @override
  String get icon_00346 => 'Trapuntatura a eco';

  @override
  String get icon_00347 => 'Applicazione 1';

  @override
  String get icon_00348 => 'Applicazione 2';

  @override
  String get icon_search => 'Ricerca';

  @override
  String get icon_00353 => 'Infilatura superiore della macchina';

  @override
  String get icon_00354 => 'Avvolgimento della spolina';

  @override
  String get icon_00355 => 'Cambio dell\'ago';

  @override
  String get icon_00356 => 'Cambio del piedino premistoffa';

  @override
  String get icon_00357 => 'Posizionamento della spolina';

  @override
  String get icon_00358 => 'Funzione cucitura';

  @override
  String get icon_00359 => 'Uso della funzione di taglio del filo';

  @override
  String get icon_00360 => 'Uso del cacciavite speciale';

  @override
  String get icon_00361 => 'Uso della funzione di rotazione sugli angoli';

  @override
  String get icon_00362 => 'Impostazione di larghezza e lunghezza del punto';

  @override
  String get icon_00363 => 'Uso del cacciavite multiuso';

  @override
  String get icon_00364 => 'Uso del sistema di rilevamento automatico del tessuto (pressione automatica del piedino premistoffa)';

  @override
  String get icon_00365 => 'Uso di My Custom Stitch (Punti Personalizzati)';

  @override
  String get icon_00366 => 'Uso della funzione di cucitura bordi';

  @override
  String get icon_00367 => 'Creazione di ricami a intaglio (cucito)';

  @override
  String get icon_00368 => 'Creazione di ricami a intaglio (ricamo)';

  @override
  String get icon_00369 => 'Preparativi per il ricamo a intaglio';

  @override
  String get icon_00370 => 'Preparativi per il ricamo a intaglio all\'inverso';

  @override
  String get icon_00371 => 'Creazione di ricami a intaglio all\'inverso (cucito)';

  @override
  String get icon_00372 => 'Uso della fotocamera incorporata nella modalità di cucitura';

  @override
  String get icon_00373 => 'Regolazione della posizione di discesa dell\'ago con l\'indicatore di guida nella schermata delle impostazioni';

  @override
  String get icon_00374 => 'Regolazione della luminosità dell\'indicatore di guida nella schermata delle impostazioni';

  @override
  String get icon_00375 => 'Regolazione della tensione del filo';

  @override
  String get icon_00376 => 'Applicazione degli stabilizzatori a trasferimento termico';

  @override
  String get icon_00377 => 'Inserimento del tessuto nel telaio per ricamo';

  @override
  String get icon_00378 => 'Montaggio/rimozione del telaio per ricamo';

  @override
  String get icon_00379 => 'Montaggio/rimozione dell\'unità da ricamo/prolunga base piana';

  @override
  String get icon_00380 => 'Montaggio/rimozione del supporto del piedino premistoffa';

  @override
  String get icon_00381 => 'Funzione di ricamo';

  @override
  String get icon_00382 => 'Uso della funzione di stampa e cucitura';

  @override
  String get icon_00383 => 'Uso della funzione di rimescolamento dei colori';

  @override
  String get icon_00384 => 'Uso del Centro motivi';

  @override
  String get icon_00385 => 'Acquisizione di disegni linea';

  @override
  String get icon_00386 => 'Acquisizione di illustrazioni';

  @override
  String get icon_00387 => 'Uso del telaio per acquisizione';

  @override
  String get icon_00388 => 'Visualizzazione del tessuto sul display a cristalli liquidi (acquisizione con la fotocamera incorporata)';

  @override
  String get icon_00389 => 'Allineamento della posizione di ricamo all\'adesivo di posizionamento';

  @override
  String get icon_00390 => 'Collegamento di disegni con la fotocamera incorporata';

  @override
  String get icon_00391 => 'Allineamento della posizione di ricamo con la fotocamera incorporata';

  @override
  String get icon_00392 => '';

  @override
  String get icon_00393 => 'Impostazioni';

  @override
  String get icon_00394 => 'Regolazione della posizione dell\'ago con la fotocamera';

  @override
  String get icon_00395 => 'Aggiornamento del software della macchina';

  @override
  String get icon_00396 => 'Regolazione dell\'indicatore di guida nella schermata delle impostazioni';

  @override
  String get icon_00397 => 'Impostazione di ora/data';

  @override
  String get icon_00398 => 'Uso della cucitura automatica di rinforzo';

  @override
  String get icon_00399 => 'Altro';

  @override
  String get icon_00400 => 'Visualizzazione/salvataggio di video';

  @override
  String get icon_00401 => 'Penna sensore';

  @override
  String get icon_00402 => 'Collegamento della penna sensore';

  @override
  String get icon_00403 => 'Calibrazione della penna sensore';

  @override
  String get icon_00404 => 'Specifica della posizione dell\'indicatore di guida con la penna sensore';

  @override
  String get icon_00405 => 'Specifica della posizione di discesa dell\'ago con la penna sensore';

  @override
  String get icon_00406 => 'Specifica della posizione/larghezza del punto con la penna sensore';

  @override
  String get icon_00407 => 'Specifica del punto di fine della cucitura con la penna sensore';

  @override
  String get icon_00408 => 'Specifica della posizione di ricamo con la penna sensore';

  @override
  String get icon_00409 => 'Accessorio';

  @override
  String get icon_00410 => 'Uso della ginocchiera alzapiedino';

  @override
  String get icon_00411 => 'Uso del cacciavite multiplo';

  @override
  String get icon_00412 => 'Utilizzo del cacciavite multiuso';

  @override
  String get icon_00416 => 'Installazione del pedale reostato multifunzione';

  @override
  String get icon_00417 => 'Assegnazione delle funzioni al pedale reostato multifunzione';

  @override
  String get icon_00418 => 'Montaggio/rimozione del piedino per ricamo con puntatore LED';

  @override
  String get icon_00419 => 'Regolazione del piedino per ricamo con puntatore LED';

  @override
  String get icon_00420 => 'Creazione di disegni da ricamo a puntini con la fotocamera incorporata';

  @override
  String get icon_00421 => 'Montaggio del piedino premistoffa con l\'adattatore incluso';

  @override
  String get icon_00422 => 'Uso della scatola degli accessori';

  @override
  String get icon_00423 => 'Manutenzione (pulizia della guida del crochet)';

  @override
  String get icon_00500 => 'Il mio Design Center';

  @override
  String get icon_00500_2 => 'Il mio\nDesign Center';

  @override
  String get icon_iqdesigner => 'IQ Designer';

  @override
  String get icon_00501 => 'Acquisizione linea';

  @override
  String get icon_00503_zz => '%%%icon%%%';

  @override
  String get icon_00503_zz_s => '%%%icon%%%';

  @override
  String get icon_00505 => 'Acquisizione illustrazione';

  @override
  String get icon_imagescan => 'Acquisizione immagine';

  @override
  String get icon_linedesign => 'Ricamo linea';

  @override
  String get icon_illustrationdesign => 'Ricamo illustrazione';

  @override
  String get icon_00509_zz => '%%%icon%%%';

  @override
  String get icon_00510 => 'Riconosci';

  @override
  String get icon_00511_1 => 'Anteprima';

  @override
  String get icon_00511_2 => 'Anteprima';

  @override
  String get icon_showpreview => 'Mostra anteprima';

  @override
  String get icon_00512 => 'Riprova';

  @override
  String get icon_00514 => 'Ignora dimensioni oggetto';

  @override
  String get icon_00516 => 'Livello rilevamento scala di grigi';

  @override
  String get icon_00503 => 'Linea';

  @override
  String get icon_00518 => 'Gomma';

  @override
  String get icon_00520 => 'Visualizzazione\nOriginale';

  @override
  String get icon_00521 => 'Visualizzazione\nRisultato';

  @override
  String get icon_00522 => 'Visualizzazione Risultato';

  @override
  String get icon_00523 => 'Numero max.\ndi colori';

  @override
  String get icon_00525 => 'Rimuovi\nsfondo';

  @override
  String get icon_00526 => 'Riconosci';

  @override
  String get icon_00528 => 'Impostazioni ricamo';

  @override
  String get icon_00529 => 'Proprietà linea';

  @override
  String get icon_00530 => 'Proprietà area';

  @override
  String get icon_00533 => 'Dimensioni';

  @override
  String get icon_00537 => 'Ampiezza\nZigzag';

  @override
  String get icon_00538 => 'Densità';

  @override
  String get icon_00539 => 'Lunghezza punto';

  @override
  String get icon_00540 => 'Punto di riempimento';

  @override
  String get icon_00541 => 'Direzione';

  @override
  String get icon_00544 => 'Compensazione\ntrazione';

  @override
  String get icon_00545 => 'Sotto cucitura';

  @override
  String get icon_00547 => 'Spaziatura';

  @override
  String get icon_00548_1 => 'Manuale';

  @override
  String get icon_00548_2 => 'Man.';

  @override
  String get icon_00549_1 => 'Automatica';

  @override
  String get icon_00549_2 => 'Auto';

  @override
  String get icon_00550 => 'In punto';

  @override
  String get icon_00551 => 'Inquadratura dell\'immagine';

  @override
  String get icon_00552 => 'Specifica colori';

  @override
  String get icon_00553 => 'Avanti';

  @override
  String get icon_00554 => 'Distanza';

  @override
  String get icon_00555 => 'Salvataggio contorni';

  @override
  String get icon_00556 => 'Forme chiuse';

  @override
  String get icon_00557 => 'Forme aperte';

  @override
  String get icon_00558 => 'Contorni salvati';

  @override
  String get icon_00559 => 'Aree di ricamo dei telai';

  @override
  String get icon_00562 => 'Contorno';

  @override
  String get icon_00564 => 'Spessore';

  @override
  String get icon_00565 => 'Spostamento\ncasuale';

  @override
  String get icon_00566 => 'Offset posizione';

  @override
  String get icon_inside => 'Interno';

  @override
  String get icon_outside => 'Esterno';

  @override
  String get icon_00567 => 'Capovolgi';

  @override
  String get icon_00568 => 'Larghezza\npunto';

  @override
  String get icon_00569 => 'Corrente';

  @override
  String get icon_00570 => 'Nuovo';

  @override
  String get icon_frame_297_465_mm => '297 × 465 mm';

  @override
  String get icon_frame_297_465_inch => '11-5/8\"× 18-1/4\"';

  @override
  String get icon_frame_272_408_mm => '272 × 408 mm';

  @override
  String get icon_frame_272_408_inch => '10-5/8\"× 16\"';

  @override
  String get icon_frame_254_254_mm => '254 × 254 mm';

  @override
  String get icon_frame_254_254_inch => '10\"× 10\"';

  @override
  String get icon_frame_240_360_mm => '240 × 360 mm';

  @override
  String get icon_frame_240_360_inch => '9-1/2\"× 14\"';

  @override
  String get icon_frame_180_360_mm => '180 × 360 mm';

  @override
  String get icon_frame_180_360_inch => ' 7\" × 14\"';

  @override
  String get icon_frame_180_300_mm => '180 × 300 mm';

  @override
  String get icon_frame_180_300_inch => ' 7\" × 12\"';

  @override
  String get icon_frame_200_300_mm => '200 × 300 mm';

  @override
  String get icon_frame_200_300_inch => '8\"×12\"';

  @override
  String get icon_frame_100_300_mm => '100 × 300 mm';

  @override
  String get icon_frame_100_300_inch => '4\"× 12\"';

  @override
  String get icon_frame_160_260_mm => '160 × 260 mm';

  @override
  String get icon_frame_160_260_inch => '6-1/4\"× 10-1/4\"';

  @override
  String get icon_frame_240_240_mm => '240 × 240 mm';

  @override
  String get icon_frame_240_240_inch => '9-1/2\"× 9-1/2\"';

  @override
  String get icon_frame_200_200_mm => '200 × 200 mm';

  @override
  String get icon_frame_200_200_inch => '8\"× 8\"';

  @override
  String get icon_frame_130_180_mm => '130 × 180 mm';

  @override
  String get icon_frame_130_180_inch => '5\"× 7\"';

  @override
  String get icon_frame_100_180_mm => '100 × 180 mm';

  @override
  String get icon_frame_100_180_inch => '4\"× 7\"';

  @override
  String get icon_frame_150_150_mm => '150 × 150 mm';

  @override
  String get icon_frame_150_150_inch => '6\"× 6\"';

  @override
  String get icon_frame_100_100_mm => '100 × 100 mm';

  @override
  String get icon_frame_100_100_inch => '4\"× 4\"';

  @override
  String get icon_frame_60_20_mm => '60 × 20 mm';

  @override
  String get icon_frame_60_20_inch => '2-3/8\"× 3/4\"';

  @override
  String get icon_zoom_50 => '50';

  @override
  String get icon_zoom_100 => '100';

  @override
  String get icon_zoom_125 => '125';

  @override
  String get icon_zoom_150 => '150';

  @override
  String get icon_zoom_200 => '200';

  @override
  String get icon_zoom_400 => '400';

  @override
  String get icon_zoom_800 => '800';

  @override
  String get icon_zoom_120 => '120';

  @override
  String get icon_zoom_240 => '240';

  @override
  String get icon_zoom_480 => '480';

  @override
  String get icon_zoom_960 => '960';

  @override
  String get icon_00600 => 'Abilita LAN wireless';

  @override
  String get icon_00600_1 => 'Menu abil.WLAN';

  @override
  String get icon_00601 => 'SSID';

  @override
  String get icon_00602 => 'Seleziona SSID...';

  @override
  String get icon_00603 => 'Nome macchina';

  @override
  String get icon_00604 => 'WPS (Push)';

  @override
  String get icon_00605 => 'WPS (Pin)';

  @override
  String get icon_00606 => 'Altri';

  @override
  String get icon_00608 => 'Stato LAN wireless';

  @override
  String get icon_00608_1 => 'Stato WLAN';

  @override
  String get icon_00609 => 'SSID\nsalvato';

  @override
  String get icon_00609_1 => 'SSID salvato';

  @override
  String get icon_00610 => 'Nuovo SSID';

  @override
  String get icon_wlan_title => 'LAN wireless';

  @override
  String get icon_wlan_connection => 'Connessione LAN wireless';

  @override
  String get icon_wlan_networks => 'Reti LAN wireless';

  @override
  String get icon_wlan_enable => 'Abilita LAN wireless';

  @override
  String get icon_wlan_setinfo_01 => 'Per visualizzare le reti disponibili, attiva Abilita LAN wireless.';

  @override
  String get icon_wlan_setinfo_02 => 'Ricerca di reti LAN wireless…';

  @override
  String get icon_wlan_setinfo_03 => 'Connessione per LAN wireless in corso…';

  @override
  String get icon_wlan_setinfo_05 => 'Attivazione della LAN wireless in corso…';

  @override
  String get icon_wlan_setinfo_06 => 'LAN wireless attivata';

  @override
  String get icon_wlan_setinfo_04 => 'Disattivazione della LAN wireless in corso…';

  @override
  String get icon_wlan_setinfo_07 => 'Verranno reimpostate tutte le impostazioni di rete, tra cui:·LAN wireless';

  @override
  String get icon_wlan_networkreset => 'Resettaggio della rete';

  @override
  String get icon_wlan_limitedconnect => 'Impossibile connettersi alla rete. Controllare le impostazioni dell\'orologio.';

  @override
  String get icon_00630 => 'Rete';

  @override
  String get icon_00631 => 'Impostazione guidata\nLAN wireless';

  @override
  String get icon_00631_1 => 'Imp. guidata';

  @override
  String get icon_00632 => 'Detail';

  @override
  String get icon_00633 => 'Stato';

  @override
  String get icon_00634 => 'Segnale';

  @override
  String get icon_00635 => 'Modalità Com.';

  @override
  String get icon_00636 => 'Attivo (11b)';

  @override
  String get icon_00637 => 'Attivo (11g)';

  @override
  String get icon_00638 => 'Attivo(11n)';

  @override
  String get icon_00639 => 'Errore connessione';

  @override
  String get icon_00640 => 'Forte';

  @override
  String get icon_00641 => 'Medio';

  @override
  String get icon_00642 => 'Debole';

  @override
  String get icon_00643 => 'Nessuno';

  @override
  String get icon_00644 => 'Ad-hoc';

  @override
  String get icon_00645 => 'Infrastruttura';

  @override
  String get icon_00646 => 'TCP/IP';

  @override
  String get icon_00647 => 'Indirizzo MAC';

  @override
  String get icon_00648 => 'Impost. proxy';

  @override
  String get icon_00649 => 'Metodo avvio';

  @override
  String get icon_00650 => 'Indirizzo IP';

  @override
  String get icon_00651 => 'Subnet mask';

  @override
  String get icon_00652 => 'Gateway';

  @override
  String get icon_00653 => 'Nome nodo';

  @override
  String get icon_00654 => 'Config.WINS';

  @override
  String get icon_00655 => 'Server WINS';

  @override
  String get icon_00656 => 'Server DNS';

  @override
  String get icon_00656_p => 'Server DNS Primario';

  @override
  String get icon_00656_s => 'Server DNS Secondario';

  @override
  String get icon_00657 => 'APIPA';

  @override
  String get icon_00658 => 'Conness. proxy';

  @override
  String get icon_00659 => 'Indirizzo';

  @override
  String get icon_00660 => 'Porta';

  @override
  String get icon_00661 => 'Nome utente';

  @override
  String get icon_00662 => 'Password';

  @override
  String get icon_00663 => 'Primario';

  @override
  String get icon_00664 => 'Secondario';

  @override
  String get icon_00665 => 'Ricerca SSID...';

  @override
  String get icon_00666 => 'SSID punto di accesso';

  @override
  String get icon_00667 => 'Chiave di rete';

  @override
  String get icon_00668 => 'Sì';

  @override
  String get icon_00669 => 'No';

  @override
  String get icon_00670 => 'Sel. autentic.';

  @override
  String get icon_00671 => 'Sistema aperto';

  @override
  String get icon_00672 => 'Tasto condiviso';

  @override
  String get icon_00673 => 'WPA/WPA2-PSK';

  @override
  String get icon_00674 => 'Tipo di crittografia';

  @override
  String get icon_00674_a => 'Tipo di crittografia (Sistema aperto)';

  @override
  String get icon_00674_c => 'Tipo di crittografia (WPA/WPA2-PSK)';

  @override
  String get icon_00675 => 'WEP';

  @override
  String get icon_00676 => 'AES';

  @override
  String get icon_00677 => 'TKIP';

  @override
  String get icon_00678 => 'Disattivata';

  @override
  String get icon_00679 => 'Statico';

  @override
  String get icon_00680 => 'Auto';

  @override
  String get icon_00681 => 'WPA';

  @override
  String get icon_00682 => 'Data';

  @override
  String get icon_cert_key => 'Certificazione normale';

  @override
  String get icon_cert_web => 'Certificazione\nmacchina online';

  @override
  String get icon_status_t => 'Stato';

  @override
  String get icon_status_a1 => 'Non controllato';

  @override
  String get icon_status_a2 => 'Controllo in corso';

  @override
  String get icon_status_a3 => 'Controllato: già aggiornato';

  @override
  String get icon_status_a4 => 'nuovo aggiornamento sul server';

  @override
  String get icon_status_b1 => 'Non scaricato';

  @override
  String get icon_status_b2 => 'Download in corso';

  @override
  String get icon_status_b3 => 'Scaricato';

  @override
  String get icon_cancel_downloading => 'Annulla il download';

  @override
  String get icon_pause_downloading2 => 'Metti in pausa il download\nPremere il tasto Riprendi per continuare il download';

  @override
  String get icon_status_c1 => 'Il nuovo aggiornamento non è ancora installato.';

  @override
  String get icon_status_c2 => 'Il nuovo aggiornamento è installato.';

  @override
  String get icon_app_dl_moniter => 'Scarica app di monitoraggio';

  @override
  String get icon_shape => 'Forma';

  @override
  String get icon_favorite => 'Preferiti';

  @override
  String get icon_sash_4section => '4 sezioni (2×2)';

  @override
  String get icon_sash_1direction => 'Una direzione';

  @override
  String get icon_sash_1dtotal => 'Pezzi totali';

  @override
  String get icon_offset => 'Scostamento';

  @override
  String get icon_startpoint => 'Punto di partenza';

  @override
  String get icon_endpoint => 'Punto di fine';

  @override
  String get icon_embfootdwn => 'Auto abbassamento\npiedino ricamo';

  @override
  String get icon_frame_272_272_mm => '272 × 272 mm';

  @override
  String get icon_frame_272_272_inch => '10-5/8\"× 10-5/8\"';

  @override
  String get icon_appguide_w => 'Guida dell\'app';

  @override
  String get icon_appguide => 'Guida\ndell\'app';

  @override
  String get icon_mobileapp => 'App mobile';

  @override
  String get icon_app => 'App';

  @override
  String get icon_emb1 => 'Ricamo 1';

  @override
  String get icon_emb2 => 'Ricamo 2';

  @override
  String get icon_00185_2 => 'Dimensione telaio';

  @override
  String get icon_type => 'Tipo';

  @override
  String get icon_typea => 'Tipo A';

  @override
  String get icon_typeb => 'Tipo B';

  @override
  String get icon_typec => 'Tipo C';

  @override
  String get icon_sash_typesplit => 'Tipo di divisione';

  @override
  String get icon_mystitchmonitor => 'My Stitch Monitor';

  @override
  String get icon_mydesignsnap => 'My Design Snap';

  @override
  String get icon_mystitchmonitor_t => 'IQ Intuition Monitoring';

  @override
  String get icon_mydesignsnap_t => 'IQ Intuition Positioning';

  @override
  String get icon_actcode => 'Codice di attivazione';

  @override
  String get icon_machineno => 'Numero macchina (No.)';

  @override
  String get icon_autodl => 'Download automatico';

  @override
  String get icon_updatemanu => 'Aggiorna manualmente';

  @override
  String get icon_dl_updateprogram => 'Scarica il programma di aggiornamento';

  @override
  String get icon_dl_updateprogram_2 => 'Scarica il programma di\naggiornamento';

  @override
  String get icon_chk_update => 'Verifica disponibilità aggiornamenti';

  @override
  String get icon_pause => 'Pausa';

  @override
  String get icon_resume => 'Riprendi';

  @override
  String get icon_cert_method => 'Metodo di certificazione';

  @override
  String get icon_latestver => 'Versione più aggiornata';

  @override
  String get icon_latestveravail => 'Ultima versione disponibile';

  @override
  String get icon_device_ios => 'Per dispositivi\niOS';

  @override
  String get icon_device_android => 'Per dispositivi\nAndroid™';

  @override
  String get icon_f_ios => 'Per iOS';

  @override
  String get icon_f_android => 'Per Android™';

  @override
  String get icon_cws_myconnection => 'CanvasWorkspace\n (La mia connessione)';

  @override
  String get icon_step1 => 'PASSAGGIO1:';

  @override
  String get icon_step2 => 'PASSAGGIO2:';

  @override
  String get icon_step3 => 'PASSAGGIO3:';

  @override
  String get icon_step4 => 'PASSAGGIO4:';

  @override
  String get icon_step5 => 'PASSAGGIO5:';

  @override
  String get icon_register => 'Registra';

  @override
  String get icon_loginid => 'Login ID:';

  @override
  String get icon_id => 'ID:';

  @override
  String get icon_appq1 => 'Applicazione\n (Normale)';

  @override
  String get icon_appq2 => 'Applicazione\nper i colori\nselezionati';

  @override
  String get icon_original_img => 'Immagine originale';

  @override
  String get icon_appq_stitch_1 => 'Punto zig-zag';

  @override
  String get icon_appq_stitch_2 => 'Punto pieno';

  @override
  String get icon_appq_stitch_3 => 'Punto dritto';

  @override
  String get icon_stamp_web => 'Taglio contorni';

  @override
  String get icon_cws_rgs_title => 'Ottenere il codice PIN per registrare la macchina.';

  @override
  String get icon_cws_rgs_s1 => 'Accedere a CanvasWorkspace.\nhttp://CanvasWorkspace.Brother.com';

  @override
  String get icon_cws_rgs_s2 => 'Toccare [Impostazioni account].';

  @override
  String get icon_pincode => 'Codice PIN';

  @override
  String get icon_kitsnc => 'ScanNCut (La mia connessione)';

  @override
  String get icon_snc1 => 'ScanNCut';

  @override
  String get icon_f_om_kitsnc => 'ScanNCut (La mia connessione)';

  @override
  String get icon_density_mm => 'linea/mm';

  @override
  String get icon_density_inch => 'linea/inch';

  @override
  String get icon_machineregist => 'Registrazione macchina';

  @override
  String get icon_snj_myconnection => 'Artspira / La mia connessione';

  @override
  String get icon_snj_rgs_title => 'Ottenere il codice PIN per registrare la macchina.';

  @override
  String get icon_snj_rgs_s1_iivo1 => 'Accedere a Artspira.\nhttps://s.brother/snjumq4211';

  @override
  String get icon_snj_rgs_s2 => 'Toccare [Impostazioni macchina] e successivamente [Registra] della macchina per ricamare, quindi selezionare [Modello LAN wireless].';

  @override
  String get icon_snj_rgs_s3 => 'Digitare il seguente numero su Artspira per ottenere il codice PIN.';

  @override
  String get icon_snj_rgs_pin => 'Digitare il codice PIN nella schermata successiva.';

  @override
  String get icon_cws_rgs_s3 => 'Toccare [Registrazione macchina/e] e selezionare [Registra una nuova macchina per cucire].';

  @override
  String get icon_cws_rgs_s4 => 'Digitare il seguente numero nella schermata Web per ottenere il codice PIN.';

  @override
  String get icon_cws_rgs_pin => 'Digitare il codice PIN nella schermata successiva.';

  @override
  String get icon_transfer => 'Trasferisci';

  @override
  String get icon_app_selectcolor => 'Seleziona colori patch applicazione';

  @override
  String get icon_texture => 'Consistenza';

  @override
  String get icon_userthread => 'Filo tessuto';

  @override
  String get icon_senju => 'Artspira';

  @override
  String get icon_notnow => 'Non ora';

  @override
  String get icon_builtin => 'Incorporato';

  @override
  String get icon_user => 'Personalizzato';

  @override
  String get icon_clearall => 'Cancella\ntutto';

  @override
  String get icon_taperingtitle => 'Assottigliamento';

  @override
  String get icon_tapering01 => 'Inizio';

  @override
  String get icon_tapering02 => 'Fine';

  @override
  String get icon_tapering03 => 'Finale stile';

  @override
  String get icon_tapering03_2 => 'Finale stile';

  @override
  String get icon_tapering04 => 'Angolo iniziale';

  @override
  String get icon_tapering05 => 'Angolo finale';

  @override
  String get icon_tapering06 => 'Ripetizione ricamo';

  @override
  String get icon_tapering06_s => 'Ripetizione ';

  @override
  String get icon_times => 'volte';

  @override
  String get icon_approx_s => 'Circa';

  @override
  String get icon_e2etitle => 'Edge-To-Edge Quilt';

  @override
  String get icon_e2e01 => 'Opzione Capovolgi';

  @override
  String get icon_e2e01_2 => 'Opzione\nCapovolgi';

  @override
  String get icon_e2e02 => 'riga/\nrighe';

  @override
  String get icon_e2e03 => 'pezzo/\npezzi';

  @override
  String get icon_sr_title => 'Regolatore di punti';

  @override
  String get icon_sr_mode_title => 'Modalità';

  @override
  String get icon_sr_mode_00exp => 'Passaggio 1 - Selezionare una modalità.\nPassaggio 2 - Selezionare un punto.\n  *Il punto di imbastitura viene automaticamente selezionato nella modalità 3.\nPassaggio 3 - Iniziare a cucire.';

  @override
  String get icon_sr_mode01exp => 'Modalità intermittente\n\nIn assenza di movimento del tessuto, l\'ago si arresta in alto e poi si abbassa dopo aver percorso la lunghezza specificata. Prestare attenzione a non mettere la mano sotto l\'ago.';

  @override
  String get icon_sr_mode02exp => 'Modalità continua\n\nIn assenza di movimento del tessuto, l\'ago si abbassa lentamente nella stessa posizione per il punto annodato o per la lunghezza punto più corta rispetto a quella specificata, ad esempio sugli angoli di un ricamo.';

  @override
  String get icon_sr_mode03exp => 'Modalità Imbastitura\n\nL\'ago si abbassa a intervalli più lunghi per l\'imbastitura. Prestare attenzione a non mettere la mano sotto l\'ago.';

  @override
  String get icon_sr_mode04exp => 'Modalità a moto libero\n\nCucitura alla velocità specificata';

  @override
  String get icon_sr_mem_mode01 => 'Intermittente';

  @override
  String get icon_sr_mem_mode02 => 'Continua';

  @override
  String get icon_sr_modemem_03 => 'Imbastitura';

  @override
  String get icon_sr_mem_mode04 => 'Free motion';

  @override
  String get icon_sr_sensingline => 'Rilevamento linea';

  @override
  String get icon_sr_footheight => 'Altezza SR';

  @override
  String get icon_unselect => 'Deseleziona';

  @override
  String get icon_filter => 'Filtro';

  @override
  String get icon_filterapplied => 'Filtro applicato';

  @override
  String get icon_apply => 'Applica';

  @override
  String get icon_upperlimit => 'Limite superiore';

  @override
  String get icon_lowerlimit => 'Limite inferiore';

  @override
  String get icon_all => 'Tutti';

  @override
  String get icon_bh_guide01 => 'Guida per asole';

  @override
  String get icon_bh_guide02 => 'Serie';

  @override
  String get icon_bh_guide03 => 'Spaziatura';

  @override
  String get icon_bh_guide04 => 'Guida per bordo del tessuto';

  @override
  String get icon_bh_guide05 => 'Distanza dal bordo';

  @override
  String get icon_colorchanges => 'Modifiche colore';

  @override
  String get icon_voiceguidance_title => 'Guida vocale';

  @override
  String get icon_voicevolume => 'Volume voce';

  @override
  String get icon_voice_01eng_a => 'English-A';

  @override
  String get icon_voice_01eng_b => 'English-B';

  @override
  String get icon_voice_02deu_a => 'Deutsch-A';

  @override
  String get icon_voice_02deu_b => 'Deutsch-B';

  @override
  String get icon_voice_03fra_a => 'Français-A';

  @override
  String get icon_voice_03fra_b => 'Français-B';

  @override
  String get icon_voice_04ita_a => 'Italiano-A';

  @override
  String get icon_voice_04ita_b => 'Italiano-B';

  @override
  String get icon_voice_05nld_a => 'Nederlands-A';

  @override
  String get icon_voice_05nld_b => 'Nederlands-B';

  @override
  String get icon_voice_06esp_a => 'Español-A';

  @override
  String get icon_voice_06esp_b => 'Español-B';

  @override
  String get icon_voice_07jpn_a => '日本語-A';

  @override
  String get icon_voice_07jpn_b => '日本語-B';

  @override
  String get icon_embcate_photostitch => 'Ricamo\nPicture Play';

  @override
  String get icon_photos_title => 'Funzione ricamo Picture Play';

  @override
  String get icon_photos_01 => 'Selezionare un file di immagine (JPG, BMP, PNG).';

  @override
  String get icon_photos_02 => 'Regolazione delle dimensioni';

  @override
  String get icon_photos_03 => 'Rimozione sfondo';

  @override
  String get icon_photos_04 => 'Inquadratura dell\'immagine';

  @override
  String get icon_photos_05 => 'Adatta a telaio';

  @override
  String get icon_photos_06 => 'Auto (AI)';

  @override
  String get icon_photos_07 => 'Manuale';

  @override
  String get icon_photos_08 => 'Selezionare uno stile per la conversione tramite IA.';

  @override
  String get icon_photos_09 => 'Regolazione colore';

  @override
  String get icon_photos_10 => 'Bordi accentuati';

  @override
  String get icon_photos_11 => 'Luminosità';

  @override
  String get icon_photos_12 => 'Contrasto';

  @override
  String get icon_photos_13 => 'Saturazione';

  @override
  String get icon_photos_14 => 'Importazione di un file di immagine (JPG, BMP, PNG)';

  @override
  String get icon_photos_15 => 'Impostazioni ricamo';

  @override
  String get icon_style0 => 'Originale';

  @override
  String get icon_style1 => 'Stile 1';

  @override
  String get icon_style2 => 'Stile 2';

  @override
  String get icon_style3 => 'Stile 3';

  @override
  String get icon_style4 => 'Stile 4';

  @override
  String get icon_style5 => 'Stile 5';

  @override
  String get icon_style6 => 'Stile 6';

  @override
  String get icon_style7 => 'Stile 7';

  @override
  String get icon_style8 => 'Stile 8';

  @override
  String get icon_style9 => 'Stile 9';

  @override
  String get icon_style10 => 'Stile 10';

  @override
  String get icon_style1_name => 'Icona arte';

  @override
  String get icon_style2_name => 'Art Déco';

  @override
  String get icon_style3_name => 'Schizzo a matita';

  @override
  String get icon_style4_name => 'Pastelli a olio';

  @override
  String get icon_style5_name => 'Insegna al neon';

  @override
  String get icon_style6_name => 'Art Nouveau';

  @override
  String get icon_style7_name => 'Deciso e vivace';

  @override
  String get icon_style8_name => 'Vetrata';

  @override
  String get icon_style9_name => 'Arte Geo';

  @override
  String get icon_style10_name => 'Grafica cartoni animati';

  @override
  String get icon_stylusedit => 'Modifica proiettore con stylus';

  @override
  String get icon_projectorsettings => 'Impostazioni proiettore';

  @override
  String get icon_setting_srvolume => 'Volume segnale acustico RP';

  @override
  String get icon_embcate_bt_01 => 'Quilt';

  @override
  String get icon_embcate_bt_02 => 'Applique';

  @override
  String get icon_embcate_bt_03 => 'Floreale';

  @override
  String get icon_embcate_bt_04 => 'Animali';

  @override
  String get icon_embcate_bt_05 => 'Lettere';

  @override
  String get icon_embcate_bt_06 => 'Decorativi';

  @override
  String get icon_embcate_bt_07 => 'Stagionali';

  @override
  String get icon_embcate_bt_08 => 'Pizzi 3D';

  @override
  String get icon_embcate_bt_09 => 'PizzI FSL';

  @override
  String get icon_embcate_bt_10 => 'ITH ';

  @override
  String get icon_embcate_b_01 => 'Quilt 2';

  @override
  String get icon_embcate_b_02 => 'Ricami per quilting Anna Aldmon';

  @override
  String get icon_embcate_b_03 => 'Applique 2';

  @override
  String get icon_embcate_b_04 => 'Floreale 2';

  @override
  String get icon_embcate_b_05 => 'Ricami di rose Pierre-Joseph Redouté';

  @override
  String get icon_embcate_b_06 => 'Ricami Zündt';

  @override
  String get icon_embcate_b_07 => 'Zentangle';

  @override
  String get icon_embcate_b_08 => 'Animali 2';

  @override
  String get icon_embcate_b_09 => 'Lettere 2';

  @override
  String get icon_embcate_b_10 => 'Sportivi';

  @override
  String get icon_embcate_b_11 => 'Marinari';

  @override
  String get icon_embcate_b_12 => 'Alimenti';

  @override
  String get icon_embcate_b_13 => 'Infantili';

  @override
  String get icon_embcate_b_14 => 'Decorativi 2';

  @override
  String get icon_embcate_b_15 => 'Pizzi 3D 2';

  @override
  String get icon_legalinfo => 'Informazione legale';

  @override
  String get icon_legal_opensource => 'Open Source Licensing Remarks\n(Osservazioni sulla licenza Open Source)';

  @override
  String get icon_legal_thirdpartysoft => 'Third-Party Software\n(Software di terze parti)';

  @override
  String get icon_nousb => '－－－－－－';

  @override
  String get icon_randomfill => 'Riempimento casuale';

  @override
  String get icon_selarea => 'Area di selezione';

  @override
  String get icon_maxnumber_patt => 'Distanza min.';

  @override
  String get t_err01 => 'Il dispositivo di sicurezza è entrato in funzione.\nIl filo è aggrovigliato? \nL\'ago è piegato?\"';

  @override
  String get t_err02 => 'Controllare e infilare nuovamente il filo superiore.';

  @override
  String get t_err02_emb => 'Controllare e infilare nuovamente il filo superiore.\n\n* Toccare il tasto di spostamento del telaio sulla schermata di ricamo per spostare il telaio al centro.';

  @override
  String get t_err03 => 'Sollevare la leva del piedino premistoffa.';

  @override
  String get t_err04 => 'Abbassare la leva del piedino premistoffa.';

  @override
  String get t_err05 => 'Non è stata inserita alcuna scheda ricamo nell\'apposito alloggiamento.\nInserire una scheda per ricamo.';

  @override
  String get t_err06 => 'Impossibile utilizzare questa scheda per ricamo. Le schede non utilizzabili includono quelle destinate ad altri Paesi, schede senza ricami, ecc.';

  @override
  String get t_err07 => 'Non è possibile aggiungere altri ricami.';

  @override
  String get t_err07_u => 'Non si possono combinare altri punti.';

  @override
  String get t_err08 => 'Questo pulsante non funziona se non è collegata l\'unità da ricamo. Spegnere la macchina e collegare l\'unità per ricamare.';

  @override
  String get t_err09 => 'Questo pulsante non funziona quando è inserita l\'unità per ricamare.';

  @override
  String get t_err10 => 'Questo pulsante non funziona quando è inserita l\'unità per ricamare. Prima di usare questo pulsante spegnere la macchina e togliere l\'unità per ricamare.';

  @override
  String get t_err11 => 'Il pedale reostato non funziona quando è inserita l\'unità per ricamare.\nDisinserire il pedale reostato.';

  @override
  String get t_err_corrupteddataturnoff => 'Impossibile riconoscere i dati. I dati potrebbero essere danneggiati.\n\nSpegnere e riaccendere l\'alimentazione.';

  @override
  String get t_err12 => 'Il numero dei punti di questo ricamo eccede la memoria disponibile.';

  @override
  String get t_err13 => 'Questo pulsante non funziona se l\'ago è abbassato. Prima di usare questo pulsante sollevare l\'ago.';

  @override
  String get t_err15 => 'Il pulsante \"Avvio/stop\" non funziona quando è inserito il pedale reostato.\nDisinserire il pedale reostato.';

  @override
  String get t_err16 => 'Finire di editare il ricamo prima di ricamarlo.';

  @override
  String get t_err16_e => 'Terminare la modifica dei disegni prima di ricamare.';

  @override
  String get t_err16_u => 'Terminare la modifica dei dati del punto prima di cucire.';

  @override
  String get t_err17 => 'Alzare la leva per asole.';

  @override
  String get t_err18 => 'Abbassare la leva per asole.';

  @override
  String get t_err19 => 'Impossibile modificare la configurazione dei caratteri.';

  @override
  String get t_err22 => 'Selezionare un ricamo.';

  @override
  String get t_err22_u => 'Selezionare un punto.';

  @override
  String get t_err23 => 'Salvataggio in corso…';

  @override
  String get t_err24 => 'Il filo della spolina è quasi terminato.';

  @override
  String get t_err25 => 'Il braccio dell’unità per ricamare si sposterà.\nAllontanare le mani ed eventuali oggetti dal braccio dell’unità per ricamare.';

  @override
  String get t_err26 => 'Eliminazione in corso…';

  @override
  String get t_err27 => 'Memoria insufficiente per salvare il ricamo.\nEliminare un altro ricamo?';

  @override
  String get t_err27_d => 'Memoria insufficiente per salvare il disegno.\nEliminare altri dati?';

  @override
  String get t_err61 => 'Memoria insufficiente per salvare il ricamo. ';

  @override
  String get t_err61_d => 'La memoria disponibile non è sufficiente per salvare i dati.\nCancellare alcuni dati o utilizzare un altro supporto.';

  @override
  String get t_err61_dd => 'La memoria disponibile non è sufficiente per salvare i dati.\nCancellare alcuni dati o utilizzare un altro supporto.';

  @override
  String get t_err28 => 'Caricamento del ricamo in corso.\nAttendere.';

  @override
  String get t_err28_d => 'Caricamento del ricamo in corso.\nAttendere.';

  @override
  String get t_err29 => 'Confermare l\'eliminazione del ricamo selezionato?';

  @override
  String get t_err65 => 'Confermare l\'eliminazione del ricamo selezionato?';

  @override
  String get t_err29_d => 'Eliminare i file di dati selezionati?';

  @override
  String get t_err30 => 'Confermare le impostazioni correnti?';

  @override
  String get t_err33 => 'Rimozione del telaio per ricamo.';

  @override
  String get t_err34 => 'Montaggio del telaio per ricamo.';

  @override
  String get t_err36 => 'Quando la leva di controllo velocità è impostata sul controllo della larghezza punto zig-zag, il pulsante \"Avvio/stop\" non funziona.';

  @override
  String get t_err37 => 'Il dispositivo di sicurezza dell\'avvolgi spolina è entrato in funzione.\nControllare se il filo è aggrovigliato.';

  @override
  String get t_err38 => 'Questa funzione non può essere utilizzata mentre la macchina è in modalità ad ago gemello.\nAnnullare la modalità ad ago gemello e scegliere nuovamente la funzione.';

  @override
  String get t_err_twinn_10 => 'Impossibile utilizzare la placca ago dritta con la modalità ad ago gemello.\nRimuovere l\'ago gemello e annullare la modalità ad ago gemello.';

  @override
  String get t_err_twinn_11 => 'La modalità ad ago gemello è stata annullata.\nRimuovere l\'ago gemello.';

  @override
  String get t_err_twinn_12 => 'Confermare che l\'ago gemello è stato rimosso.';

  @override
  String get t_err42 => 'Controllare il risultato e premere OK.';

  @override
  String get t_err48 => 'Impossibile riconoscere i ricami selezionato. I dati potrebbero essere danneggiati.';

  @override
  String get t_err50 => 'Impostare il supporto spolina a sinistra.';

  @override
  String get t_err53 => 'L\'ago è abbassato.\nPremere il pulsante \"Posizione ago\" per sollevare l\'ago.';

  @override
  String get t_err55 => 'Montare il piedino per asole “A＋”.\nLa fotocamera incorporata rileva il piedino per asole “A＋” in corrispondenza del segno “A＋” e dei tre puntini.';

  @override
  String get t_err56 => 'Impossibile riconoscere le dimensioni del bottone.\nVerificare che i tre punti si vedano facilmente o immettere i valori della lunghezza della fessura e riprovare.';

  @override
  String get t_err63 => 'Questa funzione di modifica non può essere utilizzata quando il ricamo è esterno al profilo rosso. Utilizzare questa funzione dopo aver spostato il ricamo all\'interno del profilo rosso.';

  @override
  String get t_err64 => 'Include un disegno speciale che non può essere salvato sulla memoria esterna.\nSalvare il disegno nella memoria della macchina.';

  @override
  String get t_err69 => 'È incluso un ricamo che non può essere salvato.';

  @override
  String get t_err76 => 'Installare un telaio per ricamo più grande.';

  @override
  String get t_err77 => 'Questo ricamo non può essere usato con questo telaio.\nSostituirlo con il telaio appropriato.';

  @override
  String get t_err82 => 'Confermare il ripristino delle precedenti modifiche al colore?';

  @override
  String get t_err83 => 'Confermare la sovrascrittura dei dati?';

  @override
  String get t_err84 => 'Confermare il richiamo e il recupero della memoria precedente?';

  @override
  String get t_err88 => 'Impossibile eseguire il ricamo. Unità per ricamare non collegata. Spegnere la macchina, quindi collegare l\'unità per ricamare.';

  @override
  String get t_err89 => 'Supporto USB non caricato.\nCaricare il supporto USB.';

  @override
  String get t_err90 => 'Impossibile utilizzare il supporto USB.';

  @override
  String get t_err_usb_notcompati => 'Il supporto USB collegato non è compatibile con la macchina.\nUtilizzare un supporto USB diverso.';

  @override
  String get t_err93 => 'È possibile che il supporto USB sia danneggiato.\nUtilizzare un altro supporto USB, quindi riprovare il salvataggio.';

  @override
  String get t_err94 => 'Spazio insufficiente.\nEliminare alcuni ricami o utilizzare un altro supporto USB.';

  @override
  String get t_err94_cmn => 'Spazio insufficiente.\nEliminare alcuni disegni o utilizzare un altro supporto.';

  @override
  String get t_err95 => 'Il supporto USB è stato sostituito. Non sostituire il supporto USB quando è in fase di lettura.';

  @override
  String get t_err95_cmn => 'Il supporto è stato cambiato.\nNon cambiare il supporto mentre viene letto.';

  @override
  String get t_err96 => 'Il supporto USB è protetto da scrittura. Impossibile salvare i dati. Rimuovere la protezione prima di provare a salvare i dati.';

  @override
  String get t_err96_cmn => 'Il supporto è protetto da scrittura. Impossibile salvare i dati.\nAnnullare la protezione da scrittura prima di provare a salvare i dati.';

  @override
  String get t_err97 => 'Il supporto USB è protetto da scrittura. Impossibile eliminare i dati.\nRimuovere la protezione prima di provare a eliminare i dati.';

  @override
  String get t_err97_cmn => 'Il supporto è protetto da scrittura. Impossibile eliminare i dati.\nAnnullare la protezione da scrittura prima di provare a eliminare i dati.';

  @override
  String get t_err98 => 'Errore supporto USB';

  @override
  String get t_err98_cmn => 'Errore supporto';

  @override
  String get t_err99 => 'Impossibile leggere il supporto USB.\nÈ possibile che il supporto USB sia danneggiato.';

  @override
  String get t_err100 => 'Formattazione del supporto USB';

  @override
  String get t_err101 => 'Trasmissione tramite USB';

  @override
  String get t_err101_cmn => 'Accesso al supporto';

  @override
  String get t_err103 => 'Attendere.';

  @override
  String get t_err104 => 'Impossibile eseguire l\'operazione quando i ricamo fuoriescono dalla cornice blu.';

  @override
  String get t_err106 => 'Ricamare il segmento successivo?';

  @override
  String get t_err107 => 'Ricamo terminato.';

  @override
  String get t_err108 => 'La memoria è piena.';

  @override
  String get t_err109 => 'Utilizzare il pulsante dell\'alza piedino premistoffa per sollevare il piedino premistoffa.';

  @override
  String get t_err110 => 'Utilizzare il pulsante dell\'alza piedino premistoffa per abbassare il piedino premistoffa.';

  @override
  String get t_err111 => 'Infilatura non corretta. Premere nuovamente il pulsante di infilatura automatica.';

  @override
  String get t_err113 => 'Il programma sarà aggiornato.\nCaricare il programma nella macchina tramite USB.';

  @override
  String get t_err116 => 'Errore di dati';

  @override
  String get t_err117 => 'Errore Flash ROM';

  @override
  String get t_err118 => 'Si è verificata un\'anomalia.\nSpegnere la macchina e riaccenderla.';

  @override
  String get t_err119 => 'Spegnere la macchina prima di montare o rimuovere la placca ago.';

  @override
  String get t_err120 => 'Confermare lo spostamento del carrello per ricamo nella posizione precedente?';

  @override
  String get t_err120_2 => 'Rimuovere il telaio per ricamo e sostituire la spolina.\nSuccessivamente, ricollegare il telaio e toccare OK per spostarlo nella posizione precedente.';

  @override
  String get t_err121 => 'Confermare la separazione del ricamo con bordo combinato?';

  @override
  String get t_err122 => 'Supporto USB non compatibile.';

  @override
  String get t_err122_cmn => 'Il supporto non è compatibile.';

  @override
  String get t_err123 => 'Il supporto USB è stato rimosso o il connettore è scollegato.';

  @override
  String get t_err124 => 'Si è verificata un\'anomalia.\nSpegnere la macchina e riaccenderla.';

  @override
  String get t_err125 => 'È possibile che il filo superiore non sia stato infilato correttamente.\nInfilare il filo superiore dall\'inizio.';

  @override
  String get t_err126 => 'Confermare l\'abbassamento automatico del piedino premistoffa?';

  @override
  String get t_err127 => 'Impossibile utilizzare il pulsante di infilatura automatica dell\'ago in modalità ago gemello.';

  @override
  String get t_err128 => 'Verificare che il telaiosia inserito fino in fondo. BLOCCARE LA LEVA DI FISSAGGIO DEL TELAIO.';

  @override
  String get t_err129 => 'Impostare il supporto spolina a destra.';

  @override
  String get t_err130 => 'Il piedino premistoffa effettua dei movimenti dal basso verso l\'alto. Tenere le mani distanti dal piedino premistoffa.';

  @override
  String get t_err131 => 'Questo ricamo non può essere usato.';

  @override
  String get t_err132 => 'Prima di ricamare verificare che il telaio sia infilato fino in fondo e leva di fissaggio del telaio bloccata. Premere \"Avvio/stop\".';

  @override
  String get t_err133 => 'Impossibile utilizzare questo telaio per ricamo.';

  @override
  String get t_err134 => 'Impossibile utilizzare questo ricamo perché eccede il limite della capacità della memoria.';

  @override
  String get t_err136 => 'Manutenzione preventiva consigliata.';

  @override
  String get t_err137 => 'Manutenzione preventiva consigliata.\nSuperate 1000 ore.';

  @override
  String get t_err208 => 'Calcolo in corso…';

  @override
  String get t_err209 => 'Il carrello dell\'unità da ricamo si muoverà.';

  @override
  String get t_err210 => 'Riconoscimento in corso…';

  @override
  String get t_err213 => 'Impossibile riconoscere il segno di posizionamento ricamo.';

  @override
  String get t_err215 => 'Rimuovere il segno di posizionamento ricamo.';

  @override
  String get t_err224 => 'Sono presenti polvere o macchie sulla carta bianca o sul tessuto. Sostituire con carta bianca o tessuto puliti.';

  @override
  String get t_err227 => 'Impossibile eseguire correttamente il posizionamento o il riconoscimento.';

  @override
  String get t_err228 => 'Il file supera la capacità dati e non può essere utilizzato.\nUtilizzare un file di dimensioni adatte.';

  @override
  String get t_err229 => 'Impossibile utilizzare il file.';

  @override
  String get t_err229_b => 'Non è possibile leggere questa versione di file.';

  @override
  String get t_err239 => 'Collegamento al PC eseguito.\nNon scollegare il cavo USB. ';

  @override
  String get t_err241 => 'Impossibile leggere il file.';

  @override
  String get t_err242 => 'Errore durante il salvataggio del file.';

  @override
  String get t_err244 => 'Eliminare l\'immagine selezionata?';

  @override
  String get t_err245 => 'Al momento non è possibile utilizzare questo tasto.';

  @override
  String get t_err246 => 'Il ricamo si estende oltre i limiti dell\'area.\nSpostare il ricamo e scansionare nuovamente l\'area.';

  @override
  String get t_err247 => 'Memoria insufficiente per salvare.';

  @override
  String get t_err248 => 'Eliminare l\'impostazione?';

  @override
  String get t_err249 => 'Impossibile riconoscere il bordo del tessuto.';

  @override
  String get t_err250 => 'Questo ricamo non può essere convertito.';

  @override
  String get t_err251 => 'Confermare il ripristino delle dimensioni e della posizione del ricamo?';

  @override
  String get t_err252 => 'Confermare il ripristino delle dimensioni del ricamo?';

  @override
  String get t_err253 => 'Rilevare lo spessore del tessuto.\nApplicare l\'adesivo di posizionamento sulla linea rossa.';

  @override
  String get t_err254 => 'Rilevamento effettuato.\nTogliere il segno di posizionamento del ricamo. Premere il tasto OK per avviare l\'acquisizione dello sfondo.';

  @override
  String get t_err255 => 'Premere il tasto OK, il telaio per ricamo si sposterà e inizierà l\'acquisizione dello sfondo.';

  @override
  String get t_err256 => 'Rilevamento non riuscito.\nRiprovare?';

  @override
  String get t_err257 => 'Autenticazione corretta.\nRiavviare la macchina da cucire.';

  @override
  String get t_err257_1 => 'Autenticazione corretta.\nRiavviare la macchina da cucire.';

  @override
  String get t_err257_2 => 'Autenticazione corretta.\nRiavviare la macchina.';

  @override
  String get t_err259 => 'Certificazione del kit di aggiornamento. \n\nPremere il numero del kit per eseguire la certificazione.';

  @override
  String get t_err260 => 'Inserire il codice di certificazione, quindi premere [IMPOSTA].';

  @override
  String get t_err261 => 'Certificazione in esecuzione…';

  @override
  String get t_err262 => 'Il codice di certificazione è errato.\nControllarlo e digitarlo nuovamente.';

  @override
  String get t_err263 => 'KIT';

  @override
  String get t_err264 => 'Eliminare l\'immagine di sfondo?';

  @override
  String get t_err265 => 'Reimpostare dimensioni, angolo e posizione originale del disegno?';

  @override
  String get t_err343 => 'Ripristinare l\'angolo e/o la posizione originari?';

  @override
  String get t_err266 => 'Kit di aggiornamento certificato.';

  @override
  String get t_err270 => 'Fissare bene sul materiale il primo adesivo di posizionamento del ricamo, in modo che l\'adesivo si trovi all\'interno della cornice rossa. Il braccio dell\'unità per ricamare si sposterà una volta premuto il tasto Acquisizione.';

  @override
  String get t_err271 => 'Fissare bene sul materiale il secondo adesivo di posizionamento del ricamo, in modo che l\'adesivo si trovi all\'interno della cornice rossa. Il braccio dell\'unità per ricamare si sposterà una volta premuto il tasto Acquisizione.';

  @override
  String get t_err273 => 'Il segno di posizionamento del ricamo non è applicato correttamente.\nRimuoverlo e applicarlo nuovamente.';

  @override
  String get t_err274 => 'I segni di posizionamento sono stati riconosciuti.\nLasciarli attaccati e inserire nuovamente il materiale. Il centro dei segni di posizionamento deve trovarsi nell\'area di ricamo; selezionare un disegno.';

  @override
  String get t_err276 => 'I segni di posizionamento del ricamo sono stati riconosciuti.\nRimuoverli.';

  @override
  String get t_err277 => '\"Annullare\" il collegamento dei ricami?';

  @override
  String get t_err278 => 'Il ricamo della sezione successiva non può essere eseguito se si esce.Terminare il collegamento dei ricami?';

  @override
  String get t_err279 => 'Il ricamo è terminato.\nConfermare il collegamento del ricamo successivo?\n\n* Non rimuovere il materiale dal telaio.\n* Se si desidera continuare in seguito, selezionare e impostare la sezione successiva del ricamo. Il ricamo può essere ripreso se la macchina lo ha letto.';

  @override
  String get t_err282 => 'Impossibile aggiungere dati.';

  @override
  String get t_err283 => 'Questa applicazione si chiuderà.';

  @override
  String get t_err284 => 'Questi dati sono troppo complicati e non possono essere convertiti.';

  @override
  String get t_err286 => 'Confermare il taglio del filo?';

  @override
  String get t_err288 => 'I segni di posizionamento del ricamo sono stati riconosciuti. Rimuoverli e applicarli nuovamente nelle nuove posizioni.';

  @override
  String get t_err290 => 'Impossibile riconoscere i segni di posizionamento del ricamo.\nRimuoverli e applicarli nuovamente.\nIl centro dei segni di posizionamento del ricamo deve trovarsi nell\'area di ricamo.';

  @override
  String get t_err291 => 'Lo spazio non è sufficiente. Utilizzare un supporto USB diverso.';

  @override
  String get t_err291_cmn => 'Spazio insufficiente.\nUtilizzare un altro supporto.';

  @override
  String get t_err292 => 'Non Ci sono abbastanza colori nella tabella dei fili per la modalità selezionata.';

  @override
  String get t_err292_s => 'Nella tabella dei colori corrente non sono presenti colori sufficienti per la modalità selezionata.';

  @override
  String get t_err297 => 'Ripetere i passaggi 3 e 4.';

  @override
  String get t_err298 => 'L\'etichetta è sporca.';

  @override
  String get t_err_cameracalib_ng_msg => 'Impossibile eseguire correttamente il riconoscimento.\nApplicare una nuova etichetta bianca.';

  @override
  String get t_err_cameracalib_ok_msg => 'Premere il tasto OK per memorizzare la posizione di discesa dell\'ago.';

  @override
  String get t_err299 => 'Se dopo diversi tentativi non si riesce ad eseguire l\'impostazione, contattare il rivenditore più vicino.';

  @override
  String get t_err300 => 'Se necessario, fare riferimento al manuale di istruzioni e alla guida di riferimento per gli aghi consigliati.';

  @override
  String get t_err_cameracalib_title => 'Calibrazione dell\'ago per fotocamera/proiettore';

  @override
  String get t_err_cameracalib_1_4 => '1. Premere il pulsante Posizione ago per sollevare l\'ago.\n2. Dopo aver rimosso l\'ago e il piedino premistoffa,\n applicare un\'etichetta bianca nella zona del punto\n di discesa dell\'ago.\n3. Inserire l\'ago (dimensioni standard 75/11 o 90/14).\n4. Premere il tasto AVVIO per iniziare il processo di\n taratura.\nPer ragioni di sicurezza, prima di premere il tasto\n \"AVVIO\" assicurarsi che l\'area attorno all\'ago sia libera.\n\n* Mantenere le mani e altri oggetti lontano dall’ago,\n in caso contrario possono verificarsi lesioni personali.';

  @override
  String get t_err303 => 'Il ricamo è terminato.\nConfermare il collegamento del ricamo successivo?';

  @override
  String get t_err304 => 'Non rimuovere il materiale dal telaio.\nPremere il tasto OK per selezionare il ricamo successivo.';

  @override
  String get t_err307 => 'Non rimuovere i segni di posizionamento del ricamo.\nReinserire il materiale in modo che il ricamo successivo e i centri dei due segni si trovino nell\'area ricamo.';

  @override
  String get t_err308 => 'Il ricamo successivo si trova al di fuori dell\'area di ricamo.\nReinserire il materiale in modo che il ricamo successivo e il centro dei due segni di posizionamento del ricamo si trovino all\'interno dell\'area di ricamo.';

  @override
  String get t_err309 => 'Impossibile riconoscere il segno di posizionamento del ricamo.\nReinserire il materiale in modo che il ricamo successivo e il centro dei due segni di posizionamento del ricamo si trovino all\'interno dell\'area di ricamo.';

  @override
  String get t_err310 => 'La posizione dei segni di posizionamento del ricamo è stata modificata.\nReinserire il materiale in modo che il ricamo successivo e il centro dei due segni di posizionamento del ricamo si trovino all\'interno dell\'area di ricamo.';

  @override
  String get t_err311 => 'I segni di posizionamento del ricamo sono stati riconosciuti.\nRimuoverli e eseguire il ricamo.';

  @override
  String get t_err311_size => 'I segni di posizionamento del ricamo sono stati riconosciuti.\nRimuoverli e ricamare il ricamo.\n\n* Le dimensioni del ricamo successivo è stata automaticamente regolata conprecisione, poiché la distanza tra i segni è cambiata mentre si stava reintelaiando il materiale.';

  @override
  String get t_err311_rehoop => 'La distanza tra i segni è fuori posizione a causa del reinserimento del tessuto.\nReintelaiare  il tessuto in modo che la distanza dei segni corrisponda alla lunghezza indicata di seguito.';

  @override
  String get t_err312 => 'Non rimuovere i segni di posizionamento del ricamo.\nOccorre applicarli nuovamente.\nReintelaiare il materiale.';

  @override
  String get t_err313 => 'Impossibile riconoscere il segno di posizionamento del ricamo.\nReintelaiare il materiale.';

  @override
  String get t_err314 => 'I segni di posizionamento del ricamo sono stati riconosciuti.\nRimuoverli.';

  @override
  String get t_err354 => 'La modalità Arresto assistito è stata attivata.\nSpegnere la macchina.';

  @override
  String get t_err356 => 'Questo punto non è compatibile con \"Modalità doppio trasporto\".';

  @override
  String get t_err359 => 'Rimuovere il dispositivo del doppio trasporto dalla macchina.';

  @override
  String get t_err360 => 'Impostare l\'orologio.';

  @override
  String get t_err361 => 'Selezionare la lingua.';

  @override
  String get t_err362 => 'Rimuovere il piedino per ricamo con il puntatore LED dalla macchina.';

  @override
  String get t_err364 => 'Errore modulo';

  @override
  String get t_err368 => 'Reimpostare l\'impostazione bordo del disegno, la posizione e/o l\'angolo?';

  @override
  String get t_err373 => 'Il telaio da ricamo è stato cambiato, sostituirlo con quello originario.';

  @override
  String get t_err380 => 'Per infilare l\'ago, rimuovere il tessuto sotto il piedino premistoffa.';

  @override
  String get t_err381 => 'Annullare l\'impostazione del punto di fine?';

  @override
  String get t_err382 => 'La modalità di regolazione dell\'impostazione del punto di fine della cucitura non è disponibile con il punto selezionato. \nSelezionare un altro punto o modificare la lunghezza del punto.';

  @override
  String get t_err383 => 'Dopo aver rimosso l\'adesivo del punto di fine, continuare a cucire.';

  @override
  String get t_err384 => 'Al momento non è possibile utilizzare la modalità di regolazione dell\'impostazione del punto di fine della cucitura. \nL\'impostazione del punto di fine verrà annullata.';

  @override
  String get t_err385 => 'Questa distanza è troppo breve per utilizzare l\'impostazione del punto finale.\nÈ possibile utilizzarlo se la distanza è maggiore o l\'impostazione “Arresto temporaneo” è impostata su OFF.';

  @override
  String get t_err386 => 'Impossibile utilizzare questa funzione con Impostazione punto di fine attivato.';

  @override
  String get t_err390 => 'Cancellare tutti i dati di modifica e passare alla schermata Home?';

  @override
  String get t_err390_old => 'Eliminare tutti i ricami e passare alla schermata Home?';

  @override
  String get t_err391 => 'Annullare la selezione del ricamo corrente?';

  @override
  String get t_err391_u => 'Annullare la selezione del punto corrente?';

  @override
  String get t_err392 => 'Trasmissione tramite scheda da ricamo.';

  @override
  String get t_err393 => 'Impossibile combinare il ricamo.';

  @override
  String get t_err394 => 'Memoria disponibile insufficiente per salvare il ricamo. \nEliminare un ricamo salvato?';

  @override
  String get t_err395 => 'Impossibile caricare il ricamo, perché si estende oltre l\'area modificabile.';

  @override
  String get t_err396 => 'Impossibile caricare il ricamo. Il ricamo si estende oltre l\'area modificabile. \nUsare i cursori per spostarlo e adattarlo all\'area modificabile.';

  @override
  String get t_err397 => 'Eliminare il punto corrente?';

  @override
  String get t_err398 => 'Salva come nuovo file…';

  @override
  String get t_err400 => 'Il disegno si estende all\'esterno del telaio per ricamo.\nSe si intendono aggiungere altri disegni, ruotare la combinazione del disegno.';

  @override
  String get t_err401 => 'Il ricamo si estende all\'esterno dell\'area da ricamo.';

  @override
  String get t_err402 => 'Il ricamo si estende all\'esterno del telaio per ricamo.\nNon aggiungere altri caratteri.';

  @override
  String get t_err403 => 'Il ricamo si estende all\'esterno del telaio per ricamo.\nQuesta funzione non è al momento utilizzabile.';

  @override
  String get t_err404 => 'Impossibile cambiare font. Alcune lettere non sono incluse nel font selezionato.';

  @override
  String get t_err405 => 'Il ricamo selezionato si estende all\'esterno dell\'area da ricamo.';

  @override
  String get t_err406 => 'Il ricamo si estende all\'esterno del telaio per ricamo selezionato. \nAnnullare il ricamo selezionato?';

  @override
  String get t_err408 => 'Funzione non utilizzabile quando i disegni si sovrappongono.';

  @override
  String get t_err410 => 'È possibile eseguire il ricamo con il centro o un angolo allineato al segno di posizionamento del ricamo. Attaccare il segno di posizionamento del ricamo nella posizione desiderata.';

  @override
  String get t_err411 => 'Completati i preparativi necessari, premere il tasto [Acquisizione].';

  @override
  String get t_err412 => 'Impossibile trovare il segno di posizionamento del ricamo nell\'area di rilevamento.';

  @override
  String get t_err413 => 'Usare il segno di posizionamento del ricamo per collegare i ricami.';

  @override
  String get t_err414 => 'Selezionare la posizione di collegamento \ndel ricamo successivo.';

  @override
  String get t_err415 => 'Impossibile leggere i dati.';

  @override
  String get t_err416 => 'I dati sono stati salvati.\nNome file:';

  @override
  String get t_err417 => 'Lettura dati in corso.\nAttendere.';

  @override
  String get t_err418 => 'Non è possibile utilizzare questo tipo di file.';

  @override
  String get t_err419 => 'File troppo grande: impossibile utilizzarlo. ';

  @override
  String get t_err420 => 'Tracciamento immagine non riuscito.';

  @override
  String get t_err421 => 'Il numero di colori di un\'immagine sarà ridotto al numero specificato qui, quindi verrà estratto il contorno.';

  @override
  String get t_err422 => 'Posizionare il foglio con l\'immagine  nel telaio per acquisizione.';

  @override
  String get t_err423 => 'Non si può utilizzare un telaio per ricamo. Utilizzare il telaio per acquisizione.';

  @override
  String get t_err424 => 'Il completamento del rilevamento potrebbe richiedere alcuni minuti.';

  @override
  String get t_err425 => 'Passare alla schermata \"Il mio Design Center\"?';

  @override
  String get t_err426 => 'Passare alla schermata \"IQ Designer\"?';

  @override
  String get t_err428 => 'I dati dell\'immagine creati in \"Il mio Design Center\" non saranno salvati. Continuare?';

  @override
  String get t_err429 => 'I dati \"IQ Designer\" non verranno salvati. Continuare?';

  @override
  String get t_err430 => 'Impossibile utilizzare il telaio per acquisizione per ricamare.\nSostituirlo con il telaio per ricamo.';

  @override
  String get t_err432 => 'Montare il telaio contenente l\'immagine da acquisire sulla macchina.';

  @override
  String get t_err433 => 'Quando si converte un\'immagine in ricamo contorno\no riempimento, utilizzare il telaio per acquisizione per \nrecuperare le informazioni sui colori dei fili appropriati.';

  @override
  String get t_err440 => 'Selezionare il file immagine.';

  @override
  String get t_err445 => 'Impossibile utilizzare il file di immagine.';

  @override
  String get t_err446 => 'Acquisizione in corso…';

  @override
  String get t_err447 => 'Riconoscimento in corso…';

  @override
  String get t_err448 => 'Elaborazione in corso…';

  @override
  String get t_err451 => 'Confermare la cancellazione di tutti i dati dell\'immagine modificati?';

  @override
  String get t_err452 => 'I file di immagine utilizzabili sono JPG, PNG o BMP di dimensioni inferiori a 5 MB, 1,2 milioni di pixel.';

  @override
  String get t_err453 => 'Ripristinare le impostazioni predefinite in questa pagina?';

  @override
  String get t_err454 => 'La combinazione dei ricami è troppo grande per questo telaio. Se si prevede di aggiungere altri ruotare, ruotare la combinazione di disegni.';

  @override
  String get t_err455 => 'La combinazione di ricami è troppo grande per questo telaio.';

  @override
  String get t_err457 => 'Impostare la visualizzazione dell\'identificazione telaio per ricamo su OFF.';

  @override
  String get t_err458 => 'Importazione di un file di immagine.';

  @override
  String get t_err459 => 'Impossibile utilizzare l\'unità di ricamo.';

  @override
  String get t_err460 => 'Impossibile convertire i disegni visualizzati nell\'area dell\'immagine.';

  @override
  String get t_err461 => 'Il telaio si sposterà per l\'acquisizione con la fotocamera incorporata.';

  @override
  String get t_err462_pp => 'Impossibile utilizzare il file di immagine.\nI file immagine che possono essere utilizzati sono file JPG, PNG o BMP inferiori a 6 MB ed entro 16 milioni di pixel.';

  @override
  String get t_err463 => 'Rimuovere il telaio per ricamo o il telaio per acquisizione.';

  @override
  String get t_err464 => 'Il disegno si estende al di fuori dell\'area di ricamo. Impossibile convertirlo.';

  @override
  String get t_err465 => 'Convertito in disegno da ricamo. Il mio Design Center verrà chiuso.\nPassare alla schermata Modifica ricamo?';

  @override
  String get t_err466 => 'Chiudere Il mio Design Center?';

  @override
  String get t_err467 => 'Impossibile utilizzare la funzione con la modalità di collegamento disegni.';

  @override
  String get t_err468 => 'PCB macchina spento';

  @override
  String get t_err469 => 'Convertito in disegno da ricamo. IQ Designer verrà chiuso. Passare alla schermata Modifica ricamo?';

  @override
  String get t_err470 => 'Chiudere IQ Designer?';

  @override
  String get t_err471 => 'Confermare l\'eliminazione dei dati selezionati?';

  @override
  String get t_err472 => 'Selezionare ricami multipli.';

  @override
  String get t_err473 => 'Ricami disposti come immagini salvati.';

  @override
  String get t_err474 => 'Contorno dei disegni disposti salvato.';

  @override
  String get t_err475 => 'Specificare la distanza offset intorno al ricamo.';

  @override
  String get t_err478 => 'Richiamare dall\'elenco ricami per timbri di Il mio Design Center.';

  @override
  String get t_err478_tc => 'Richiamare dall\'elenco ricami per timbri di \"IQ Designer\".';

  @override
  String get t_err479 => 'Un ricamo con bobbin work non può essere combinato con un ricamo di una categoria diversa.';

  @override
  String get t_err480 => 'Passa alla prima posizione dell\'ago.';

  @override
  String get t_err481 => 'Cucitura del ricamo in bobbin work terminata.';

  @override
  String get t_err482 => 'Cucitura di tutti i ricami bobbin work terminata.';

  @override
  String get t_err483 => 'Tagliare i fili.';

  @override
  String get t_err484_old => 'Prima di ricamare i ricami seguenti, controllare la quantità e il tipo di filo spolina installato.';

  @override
  String get t_err484 => 'Sostituire il filo spolina e montare il telaio per ricamo.\nIl carrello dell\'unità da ricamo si sposta dopo aver premuto OK.';

  @override
  String get t_err485 => 'Verrà ricamato il seguente ricamo con bobbin work.';

  @override
  String get t_err486 => 'Ruotare il volantino per abbassare l\'ago nel tessuto, quindi tirare verso l\'alto il filo spolina.';

  @override
  String get t_err489 => 'Nessun dato da convertire.';

  @override
  String get t_err496 => 'Applicare l\'impostazione a tutte le aree?';

  @override
  String get t_err497 => 'Contorno';

  @override
  String get t_err501 => 'Impossibile applicare le impostazioni. Memoria disponibile insufficiente per salvare gli attributi.';

  @override
  String get t_err502 => 'Distanza troppo lunga tra i ricami selezionati.';

  @override
  String get t_err503 => 'Forma troppo complessa per la linea dell\'applicazione.';

  @override
  String get t_err503_new => 'Forma troppo complessa o inadatta per creare un’applicazione.\nProva a modificare le impostazioni dell\'applicazione o seleziona una forma diversa.\n* Il risultato può variare a seconda della posizione e dell\'angolazione.';

  @override
  String get t_err504 => 'Dimensioni troppo grandi per aggiungere la linea dell\'applicazione.';

  @override
  String get t_err509 => 'La struttura dei dati non consente di visualizzare una parte della trama.';

  @override
  String get t_err505 => 'Impossibile utilizzare questa funzione se si esegue l\'ordinamento colori.';

  @override
  String get t_err506 => 'Eliminare il punto di riallineamento?';

  @override
  String get t_err508 => 'Impossibile utilizzare la funzione dei punti di riallineamento filo se si deseleziona l\'ultima area colore.\nEliminare il punto di riallineamento?';

  @override
  String get t_err507 => 'Auto';

  @override
  String get t_err510 => 'Utilizzare l\'immagine corrente?';

  @override
  String get t_err511 => 'I dati sono stati salvati nella memoria della macchina.\nProcedere con il ricamo?';

  @override
  String get t_err515 => 'Spegnere la macchina per raffreddare il proiettore incorporato.';

  @override
  String get t_err516 => 'Non è possibile utilizzare questo punto con la placca ago corrente.';

  @override
  String get t_err517 => 'Per cucire questo punto, sostituire la placca ago.';

  @override
  String get t_err518 => 'Il telaio per ricamo è stato modificato. Il braccio dell\'unità per ricamare si sposterà.\n';

  @override
  String get t_err519 => 'Il proiettore verrà spento.';

  @override
  String get t_err520 => 'Allontanare le mani ed eventuali oggetti dal braccio dell’unità per ricamare.';

  @override
  String get t_err_521 => 'È possibile scaricare i video della guida di apprendimento.';

  @override
  String get t_err574 => 'Si è verificato un cattivo funzionamento.\n Impossibile accedere a EEPROM.';

  @override
  String get t_err575 => 'Il filo della spolina è quasi terminato.\n\n* Utilizzare il pulsante “Punto di rinforzo” per cucire ripetutamente un punto singolo e fissare la cucitura.\n* Utilizzare il tasto di spostamento del telaio per spostare il braccio dell\'unità per ricamare in modo da poter rimuovere o installare il telaio per ricamo. Successivamente, il braccio torna alla posizione precedente.';

  @override
  String get t_err577 => 'Non è possibile combinare un ricamo per fissare con un ricamo di una categoria diversa.';

  @override
  String get t_err578 => 'Impossibile selezionare ulteriori combinazioni colori come preferiti.';

  @override
  String get t_err581 => 'Iniziare a cucire dall\'angolo in alto a destra del tessuto.\nApplicare il telaio per ricamo nella posizione di cucitura iniziale.';

  @override
  String get t_err581_b => 'Iniziare a cucire partendo dall\'angolo superiore sinistro del tessuto.\n Montare il telaio per ricamo nella posizione iniziale di cucitura.';

  @override
  String get t_err582 => 'Un lato è stato ricamato. Ruotare il tessuto di 90° in senso antiorario e reinserirlo nel telaio per ricamo.';

  @override
  String get t_err582_n => 'Un lato è stato ricamato. Ruotare il tessuto in senso antiorario e reinserirlo per l\'angolo successivo.';

  @override
  String get t_err582_e => 'Una riga è stata completata. Per iniziare la fila successiva, reintelaiare la stoffa fino all\'estremità sinistra della fila successiva, includendo nel telaio il segno del filo del motivo superiore.';

  @override
  String get t_err583 => 'Utilizzare i tasti di spostamento del ricamo per regolare l\'angolo interno del ricamo.';

  @override
  String get t_err583_e => 'Utilizzare i tasti di spostamento del ricamo per allineare l\'angolo superiore sinistro dell\'area del ricamo all\'angolo superiore sinistro (segno) dell\'area da ricamare.';

  @override
  String get t_err584 => 'Utilizzare i tasti di spostamento del ricamo per allineare il punto iniziale con il punto finale del ricamo precedente.';

  @override
  String get t_err584_e => 'Utilizzare i tasti di spostamento del ricamo per allineare l\'angolo superiore sinistro dell\'area del ricamo al segno di filo inferiore sinistro del ricamo superiore.';

  @override
  String get t_err585 => 'Utilizzare i tasti di rotazione del ricamo per regolare l\'angolo del ricamo tenendo d\'occhio i punti intorno al ricamo.';

  @override
  String get t_err586 => 'Regolare la dimensione del ricamo in modo che il punto inferiore sinistro corrisponda all\'angolo interno del ricamo successivo.';

  @override
  String get t_err586_b => 'Regolare le dimensioni del ricamo in modo che il punto inferiore destro corrisponda all\'angolo interno del ricamo successivo.';

  @override
  String get t_err586_e => 'Utilizzare i tasti di rotazione e delle dimensioni per regolare l\'angolazione e le dimensioni del ricamo, tenendo d\'occhio i bordi del ricamo.';

  @override
  String get t_err587 => 'Utilizzare i tasti di rotazione e i tasti di dimensionamento per allineare il punto finale con il punto iniziale del primo ricamo.';

  @override
  String get t_err588 => 'Reintelaiare il materiale.';

  @override
  String get t_err588_e => 'Reintelaiare la stoffa sul lato destro, includendo nel telaio il bordo destro del ricamo a sinistra.';

  @override
  String get t_err588_e_2 => 'Reintelaiare il tessuto verso destra, includendo il bordo destro del ricamo a destra e il segno di filo del ricamo sopra.';

  @override
  String get t_err590 => 'Premere CARICA per installare il file di aggiornamento.';

  @override
  String get t_err591 => 'È disponibile un nuovo aggiornamento. Per installare l\'aggiornamento, spegnere la macchina, tenere premuto il pulsante “Infilatura automatica” e riaccendere la macchina.';

  @override
  String get t_err_dl_updateprogram2 => 'Il nuovo programma di aggiornamento è pronto.\nPer installare l\'aggiornamento, spegnere la macchina, tenere premuto il pulsante “Infilatura automatica” e riaccendere la macchina.';

  @override
  String get t_err592 => 'Premere CARICA per salvare il file di aggiornamento.';

  @override
  String get t_err593 => 'Selezionare il dispositivo su cui salvare il file di aggiornamento.';

  @override
  String get t_err594 => 'Selezionare il dispositivo su cui è salvato il file di aggiornamento.';

  @override
  String get t_err_dl_updateprogram => 'Premere il tasto Avvio per scaricare il programma di aggiornamento.';

  @override
  String get t_err_dl_fail => 'Download non riuscito: la memoria interna è piena.';

  @override
  String get t_err_networkconnectionerr => 'Connessione di rete persa.\nVerificare che la macchina sia collegata a una rete wireless.';

  @override
  String get t_err_not_turnoff => 'Non spegnere la macchina.';

  @override
  String get t_err_pressresume_continuedl => 'Premere il tasto Riprendi per continuare il download.';

  @override
  String get t_err_updateformovie => 'Aggiornare di nuovo per installare i filmati.';

  @override
  String get t_err595 => 'Digitare il codice di attivazione di 16 cifre.';

  @override
  String get t_err596 => 'Digitare il codice di attivazione di 16 cifre e premere il tasto [Imposta].';

  @override
  String get t_err597 => 'Il numero della macchina e il codice di attivazione saranno inviati al server.';

  @override
  String get t_err598 => '\"Certificazione macchina online\" è l\'opzione consigliata per la macchina connessa alla LAN wireless.';

  @override
  String get t_err599 => 'Il codice di attivazione è errato.\nControllare la chiave e digitarla di nuovo.';

  @override
  String get t_err599_used => 'Il codice immesso è già stato registrato con una macchina diversa.';

  @override
  String get t_err601 => 'Abilita LAN wireless?';

  @override
  String get t_err602 => 'Ricerca SSID…';

  @override
  String get t_err603 => 'Applica impostazioni?';

  @override
  String get t_err604 => 'Connessione a LAN wireless riuscita.';

  @override
  String get t_err605 => 'Connessione a LAN wireless.';

  @override
  String get t_err606 => 'Connessione al server non riuscita.\nVerificare le impostazioni LAN wireless.';

  @override
  String get t_err607 => 'Connessione al server non riuscita.\nVerificare le impostazioni di rete.';

  @override
  String get t_err608 => 'Autenticazione non riuscita al server.\nVerificare le impostazioni proxy.';

  @override
  String get t_err609 => 'Si è verificato un errore di rete.';

  @override
  String get t_err611 => 'Disattivata';

  @override
  String get t_err612 => 'Si sono verificati errori nella funzione di rete.';

  @override
  String get t_err613 => 'Impossibile importare i dati.\nRipetere la procedura.';

  @override
  String get t_err614 => 'Sono presenti informazioni sul punto di accesso salvate.\nCollegarsi con queste informazioni?';

  @override
  String get t_err615 => 'Errore connessione 01';

  @override
  String get t_err616 => 'Chiave di rete errata.';

  @override
  String get t_err617 => 'Chiave di rete errata.';

  @override
  String get t_err618 => 'Ripristina rete?';

  @override
  String get t_err620 => 'Spegnere la macchina e riaccenderla. ';

  @override
  String get t_err621 => 'Autenticazione non riuscita al server.\nVerificare le impostazioni proxy.';

  @override
  String get t_err622 => 'Autenticazione non eseguita.\nConfermare Username o Password.';

  @override
  String get t_err623 => 'Annullamento';

  @override
  String get t_err624 => 'Errore di comunicazione';

  @override
  String get t_err625 => 'Completato';

  @override
  String get t_err626 => 'Controlla I/F';

  @override
  String get t_err627 => 'Err. Connes';

  @override
  String get t_err628 => 'Connessione al server non riuscita.\nVerificare le impostazioni di rete.';

  @override
  String get t_err629 => 'Connessione al server non riuscita.\nRiprovare più tardi.';

  @override
  String get t_err630 => 'Download.\nAttendere.';

  @override
  String get t_err631 => 'Errore rilevato durante il download.\nRicominciare.';

  @override
  String get t_err632 => 'Nessun punto accesso.';

  @override
  String get t_err633 => 'No dati !';

  @override
  String get t_err634 => 'Vedere la risoluzione dei problemi nella guida utente.';

  @override
  String get t_err636 => 'Impossibile trovare server, verificare nome e indirizzo o immettere un altro server LDAP.';

  @override
  String get t_err637 => 'Server scaduto.\nRiprovare più tardi.';

  @override
  String get t_err638 => 'Trasferimento in corso…';

  @override
  String get t_err697 => 'Non sono disponibili trasferimenti dati mediante cavo USB.';

  @override
  String get t_err84_mdc => 'Confermare il richiamo e il recupero della memoria precedente?\n(Il mio Design Center).';

  @override
  String get t_err84_iqd => 'Confermare il richiamo e il recupero della memoria precedente?\n(IQ Designer).';

  @override
  String get t_err703_b => 'Installare \"My Stitch Monitor\" per monitorare il ricamo.';

  @override
  String get t_err703_t => 'Installare \"IQ Intuition Monitoring\" per monitorare il ricamo.';

  @override
  String get t_err704_b => 'Installare l\'app “My Stitch Monitor” per monitorare il ricamo su un dispositivo smart. \n\nConsente di monitorare lo stato di avanzamento del ricamo su un dispositivo smart. \nConsente anche di controllare le informazioni sul colore di tutti i fili utilizzati per il ricamo.';

  @override
  String get t_err704_t => 'Installare l\'app “IQ Intuition Monitoring” per monitorare il ricamo su un dispositivo smart. \n\nConsente di monitorare lo stato di avanzamento del ricamo su un dispositivo smart. \nConsente anche di controllare le informazioni sul colore di tutti i fili utilizzati per il ricamo.';

  @override
  String get t_err705_b => 'Installare l\'app “My Design Snap” per inviare immagini da un dispositivo smart alla macchina. \n\nConsente di creare agevolmente ricami dalle immagini in Il mio Design Center.';

  @override
  String get t_err705_t => 'Installare l\'app “IQ Intuition Positioning” per inviare immagini da un dispositivo smart alla macchina. \n\nConsente di creare agevolmente ricami dalle immagini in \"IQ Designer\".';

  @override
  String get t_err708 => 'KIT1 è obbligatorio per utilizzare questa app.';

  @override
  String get t_err709 => 'KIT2 è obbligatorio per utilizzare questa app.';

  @override
  String get t_err711 => 'I dati di ricamo non contengono informazioni sufficienti sui fili.\nPer visualizzare le corrette informazioni sul filo, immettere il numero del colore del filo nella schermata di modifica del colore del filo.';

  @override
  String get t_err713 => 'Leggere i contratti di licenza con l\'utente finale (EULA) prima dell\'uso.';

  @override
  String get t_err715 => 'Accetto i termini del contratto di licenza.';

  @override
  String get t_err01_heated => 'Il surriscaldamento del motore ad albero principale ha causato l\'attivazione del dispositivo di sicurezza. Il filo è ingarbugliato?';

  @override
  String get t_err01_motor => 'Il dispositivo di sicurezza si è attivato perché il motore dell\'albero principale si è grippato. Il filo è ingarbugliato?';

  @override
  String get t_err01_npsensor => 'Il dispositivo di sicurezza si è attivato a causa del funzionamento errato del sensore di posizione dell\'ago.';

  @override
  String get t_err734 => 'Per iOS';

  @override
  String get t_err735 => 'Per Android™';

  @override
  String get t_err738 => 'L\'ordine di ricamo verrà modificato.';

  @override
  String get t_err739 => 'Impossibile utilizzare questa funzione quando si seleziona un ricamo speciale.';

  @override
  String get t_err740 => 'Il punto si sovrapporrà ai punti sull\'altro lato. Ridurre la larghezza del punto o aumentare la distanza di offset.';

  @override
  String get t_err741 => 'I dati sono stati ridotti a una dimensione compatibile.';

  @override
  String get t_err742 => 'Andare alle impostazioni per connettere la macchina a una rete wireless?';

  @override
  String get t_err743 => 'Impossibile utilizzare questa funzione perché I servizi CanvasWorkspace son attualmente inattivi per manutenzione. Attendere il completamento del ripristino.';

  @override
  String get t_err743_s => 'Impossibile utilizzare questa funzione perché I servizi Artspira son attualmente inattivi per manutenzione. Attendere il completamento del ripristino.';

  @override
  String get t_err744 => 'Impossibile registrare la macchina su CanvasWorkspace.\nRicominciare.';

  @override
  String get t_err744_s => 'Impossibile registrare la macchina su Artspira.\nRicominciare.';

  @override
  String get t_err745 => 'Codice PIN errato. Reinserire il codice PIN.';

  @override
  String get t_err746 => 'La macchina non è connessa a Internet.';

  @override
  String get t_err747 => 'Non connesso al server Internet.\nControllare l\'impostazione del proxy.';

  @override
  String get t_err748 => 'Timeout del server.\nRiprovare più tardi.';

  @override
  String get t_err749 => 'Disconnettersi da CanvasWorkspace?';

  @override
  String get t_err749_s => 'Disconnettersi da Artspira?';

  @override
  String get t_err750 => 'Impossibile disconnettere la macchina da CanvasWorkspace. Disconnettersi manualmente dalla pagina Web.';

  @override
  String get t_err750_s => 'Impossibile disconnettere la macchina da Artspira.\nDisconnettersi manualmente dall\'app.';

  @override
  String get t_err751 => 'Occorre registrare la macchina per cucire in uso su CanvasWorkspace per inviare i dati (registrazione PIN).\nAndare alla schermata delle impostazioni?';

  @override
  String get t_err751_s => 'Occorre registrare la macchina per cucire in uso su Artspira per inviare i dati (registrazione PIN).\nAndare alla schermata delle impostazioni?';

  @override
  String get t_err752 => 'La macchina potrebbe non essere registrata. Controllare su CanvasWorkspace.';

  @override
  String get t_err752_s => 'La macchina potrebbe non essere registrata. Controllare su Artspira.';

  @override
  String get t_err753 => 'Impossibile caricare i dati.\nRicominciare.';

  @override
  String get t_err754 => 'Impossibile scaricare i dati.\nRicominciare.';

  @override
  String get t_err755 => 'Autenticazione corretta.\nRiavviare la macchina da cucire.\n\nPer inviare i dati alla macchina ScanNCut, riavviare la macchina e registrarla sul server CanvasWorkspace dalla pagina 13 delle impostazioni.';

  @override
  String get t_err755_s => 'Autenticazione corretta.\nRiavviare la macchina da cucire.';

  @override
  String get t_err756 => 'Sostituire i dati esistenti con i nuovi dati?\n* I dati nella cartella dei dati temporanei verranno automaticamente cancellati dopo un determinato periodo di tempo.';

  @override
  String get t_err757 => 'Inviare i dati alla cartella dei dati temporanei sul server?\n* I dati nella cartella dei dati temporanei verranno automaticamente cancellati dopo un determinato periodo di tempo.';

  @override
  String get t_err761 => 'La cartella dati temporanea non contiene dati.';

  @override
  String get t_err762 => 'Nella cartella dei dati temporanei non erano presenti dati leggibili.';

  @override
  String get t_err763 => 'Occorre registrare ScanNCut e la macchina per cucire in uso su CanvasWorkspace per ricevere i dati di taglio (registrazione PIN).\nAndare alla schermata delle impostazioni?';

  @override
  String get t_err763_s => 'Registrare la macchina per cucire per ricevere i dati tramite Artspira. (Registrazione codice PIN) \nAndare alla schermata delle impostazioni per la registrazione?';

  @override
  String get t_err764 => 'Installare \"Artspira\" sul proprio smart device. \nLa creazione di un account Artspira amplierà il vostro mondo del ricamo. \nPer ulteriori informazioni, scansionare il codice QR.';

  @override
  String get t_err765 => 'Non è stato possibile modificare il nome della macchina.';

  @override
  String get t_err766 => 'Abilita la LAN wireless per cambiare il nome della macchina.';

  @override
  String get t_err770 => 'Impossibile eliminare.';

  @override
  String get t_err771 => 'Eliminando tutti i ricami personalizzati, i ricami utilizzati per la modifica verranno sostituiti da un altro ricamo.\nEliminare tutti i ricami personalizzati?';

  @override
  String get t_err772 => 'Eliminando o modificando il ricamo personalizzato importato dopo averlo salvato, i dati potrebbero cambiare rispetto all\'originale.';

  @override
  String get t_err773 => 'I dati che includono ricami personalizzati importati non possono essere salvati in un\'unità di memoria esterna.';

  @override
  String get t_err774 => 'Verranno salvati solo i punti di ricamo. I dati di modifica che includono ricami personalizzati non possono essere salvati in un\'unità di memoria esterna. Salvarli nell\'unità di memoria interna.';

  @override
  String get t_err775 => 'Archivio dati pieno.\nSelezionare un ricamo personalizzato per sostituirlo con il nuovo ricamo.';

  @override
  String get t_err776 => 'Sostituendo il ricamo personalizzato, potrebbe cambiare durante l\'uso del precedente ricamo. Continuare?';

  @override
  String get t_err_taper01 => 'Impossibile impostare il punto assottigliato. Aumentare la distanza o l\'angolazione.';

  @override
  String get t_err_taper02 => 'Completare l\'impostazione prima di cucire.';

  @override
  String get t_err_taper03 => 'Annullare l\'impostazione del punto rastremato?';

  @override
  String get t_err_taper04 => 'Annullare lo stato attuale del punto rastremato?';

  @override
  String get t_err_taper05 => 'Impossibile usare questa funzione con il punto rastremato.';

  @override
  String get t_err_tapering07 => 'Premere il pulsante “Cucitura di ritorno” per iniziare a cucire l\'estremità assottigliata.';

  @override
  String get t_err_tapering08 => 'L\'assottigliamento verrà interrotto dopo il numero di ripetizioni specificato.';

  @override
  String get t_err_tapering09 => 'L\'assottigliamento terminerà in corrispondenza dell\'adesivo punto finale.';

  @override
  String get t_err785 => 'Assicuratevi che ci sia abbastanza filo superiore e filo per spolina per eseguire completamente il ricamo, poiché è impossibile ottenere risultati soddisfacenti se uno dei due fili si esaurisce.';

  @override
  String get t_err790 => 'Tutti i dati, le impostazioni e le informazioni di rete salvati verranno ripristinati alle impostazioni predefinite. \nContinuare?';

  @override
  String get t_err791 => 'Eliminazione in corso…\nNon spegnere la macchina.';

  @override
  String get t_err792 => 'Reimpostazione completata.\nSpegnere la macchina.';

  @override
  String get t_err_paidcont_update => 'Per utilizzare questi dati, è necessario\naggiornare il software di questa macchina all\'ultima versione.';

  @override
  String get t_err_embcarriageevacuate => 'Toccare OK per spostare il carrello dell\'unità per ricamare nella posizione originale.';

  @override
  String get t_err_sr_01 => 'Rimuovere il modulo regolatore di punti dalla macchina.';

  @override
  String get t_err_sr_02 => 'Lo stato della cucitura è stato annullato perché non è stata rilevata alcuna operazione di trasporto del tessuto per diversi secondi.';

  @override
  String get t_err_sr_03 => 'È possibile avviare Free motion quilting/imbastitura con il regolatore di punti.\n\nPrestare attenzione a non tirare forte il tessuto perché potrebbe causare la rottura dell\'ago.';

  @override
  String get t_err_sr_04 => 'Iniziare a cucire dopo aver selezionato la modalità.';

  @override
  String get t_err_sr_05 => 'L\'ago si muove. Togliere la mano dall\'ago.';

  @override
  String get t_err_sr_06 => 'Il modulo del regolatore di punti è stato scollegato. La schermata dedicata del regolatore di punti verrà chiusa.  \nPer riaccedere alla schermata, collegarlo.';

  @override
  String get t_err_sr_08 => 'Prestare attenzione a non rompere l\'ago tirando il tessuto con forza in caso di aumento dell\'impostazione di tensione del filo.';

  @override
  String get t_err_sr_09 => 'Non è possibile utilizzare la modalità ago gemello con questa funzione.\nScollegare il connettore del modulo regolatore di punti e disattivare la modalità ago gemello per riprovare.';

  @override
  String get t_err_sr_10 => 'Quando si utilizza la cucitura per imbastitura, non utilizzare il Piedino per quilting a punta aperta con regolatore di punti. In caso contrario, l\'ago potrebbe rompersi causando lesioni.';

  @override
  String get t_err_manual_01_b => 'Se si desidera visualizzare i manuali su dispositivo mobile o PC, accedere a XXX(URL).';

  @override
  String get t_err_manual_02_t => 'Se si desidera visualizzare il manuale su dispositivo mobile o PC, accedere a XXX(URL).';

  @override
  String get t_err_proj_emb_001 => 'La funzionalità del proiettore è limitata poiché il telaio per ricamo è piccolo. “Modifica proiettore con stylus” non è supportata, ma i ricami vengono proiettati.\n\n*Il carrello dell\'unità da ricamo si sposta dopo aver premuto OK.';

  @override
  String get t_err_proj_emb_002 => 'La funzionalità del proiettore è limitata poiché il telaio per ricamo è piccolo. “Modifica proiettore con stylus” non è supportata, ma i ricami vengono proiettati.';

  @override
  String get t_err_proj_emb_003 => 'Il proiettore si spegnerà.';

  @override
  String get t_err_proj_emb_004 => 'Il proiettore si spegnerà a causa della rimozione del telaio per ricamo.\n\n* Il carrello dell\'unità da ricamo si sposta dopo aver premuto OK.';

  @override
  String get t_err_proj_emb_005 => 'Il proiettore si spegnerà a causa della rimozione del telaio per ricamo.';

  @override
  String get t_err_proj_emb_006 => 'Il proiettore si spegnerà.\n\n* Il carrello dell\'unità da ricamo si sposta dopo aver premuto OK.';

  @override
  String get t_err_proj_emb_007 => 'Annullare la selezione del ricamo corrente?\n\n* Il carrello dell\'unità da ricamo si sposta dopo aver premuto OK.';

  @override
  String get t_err_proj_emb_008 => 'Convertito in disegno da ricamo. Il mio Design Center verrà chiuso.\nPassare alla schermata Modifica ricamo?\n\n* Il proiettore si spegnerà e il braccio dell\'unità per ricamare si sposterà dopo aver premuto OK.';

  @override
  String get t_err_proj_emb_009 => 'Convertito in disegno da ricamo. IQ Designer verrà chiuso.\nPassare alla schermata Modifica ricamo?\n\n* Il proiettore si spegnerà e il braccio dell\'unità per ricamare si sposterà dopo aver premuto OK.';

  @override
  String get t_err_proj_emb_010 => 'Elaborazione in corso…';

  @override
  String get t_err_proj_emb_011 => 'Chiusura in corso…';

  @override
  String get t_err_proj_emb_012 => 'Il proiettore di accenderà.\n\n* Il carrello dell\'unità da ricamo si sposta dopo aver premuto OK.';

  @override
  String get t_err_proj_emb_013 => 'Il proiettore di accenderà.';

  @override
  String get t_err_proj_emb_014 => 'Questa funzione non può essere utilizzata mentre il proiettore è in funzione.';

  @override
  String get t_err_proj_smallframe => 'Non disponibile poiché il telaio per ricamo è piccolo.';

  @override
  String get t_err_mdc_import_01 => 'Accertarsi di regolare i dati perché le dimensioni cambiano durante il caricamento.';

  @override
  String get t_err_voiceg_01 => 'Ricerca dati guida vocale in corso...';

  @override
  String get t_err_voiceg_02 => 'La guida vocale è pronta e l\'impostazione è attivata.';

  @override
  String get t_err_photos_01 => 'Rimuovere la maschera dall\'immagine.';

  @override
  String get t_err_photos_02 => 'Reimpostare la regolazione delle dimensioni dell\'immagine?';

  @override
  String get t_err_photos_03 => 'Annullare la rimozione sfondo?';

  @override
  String get t_err_photos_04 => 'Convertire in ricamo.';

  @override
  String get t_err_photos_05 => 'Convertito in disegno da ricamo. Funzione ricamo Picture Play verrà chiusa.\nPassare alla schermata Modifica ricamo?';

  @override
  String get t_err_photos_06 => 'Attendere.\nDurante la conversione, la connessione LAN wireless viene temporaneamente disattivata.';

  @override
  String get t_err_photos_exit => 'Chiudere Funzione ricamo Picture Play';

  @override
  String get t_err_font_old_new => 'Convertire il file nel formato dati più recente, dato che è in uso il formato precedente?';

  @override
  String get t_err_font_old_lomited => 'La funzione di modifica è limitata a causa del formato dati precedente.';

  @override
  String get t_err_firstset_wlan => 'Impostare una LAN wireless.\nAndare alle impostazioni per connettere la macchina alla rete wireless?';

  @override
  String get t_err_firstset_voiceguidance => 'Impostare la funzione Istruzioni vocali.\nAndare alle impostazioni di Istruzioni vocali?';

  @override
  String get t_err_wlan_function_01 => 'Per utilizzare la funzione, occorre attivare l\'impostazione LAN wireless sulla macchina e connettere la macchina a una rete wireless.\nAndare alle impostazioni per connettere la macchina a una rete wireless?';

  @override
  String get t_err_teachingimage => 'Immagini a solo scopo illustrativo, alcune immagini possono variare a seconda del modello.';

  @override
  String get t_err_photo_disclaimers => 'Utilizzando questa funzione, l\'utente accetta di non utilizzare alcuna parte del contenuto:\n• per scopi contrari a tutte le leggi e norme applicabili (inclusi, in particolare, contenuto razzista, discriminatorio, di incitamento all\'odio, pornografico o pedopornografico e/o affermazioni che offendono l\'ordine pubblico o il buon costume);\n• per violare il diritto alla privacy o alla pubblicità di qualsiasi persona;\n• violare qualsiasi copyright, marchio o altro diritto di proprietà intellettuale di terzi;\n• includere URL o parole chiave che indirizzino gli spettatori verso siti dannosi;\nL\'utente accetta e riconosce di essere l\'unico responsabile dei contenuti utilizzati.\nConsultare le Condizioni d\'uso per i dettagli.\n\nUtilizzando i contenuti, dichiaro di aver letto e compreso appieno i Termini di servizio e le Linee guida.';

  @override
  String get t_err_framemovekey => '* Toccare il tasto di spostamento del telaio sulla schermata di ricamo per spostare il telaio al centro.';

  @override
  String get speech_colorchangeinfo => 'Cucitura terminata.\nImpostare il colore del filo successivo.';

  @override
  String get t_err_updateinfo_01 => 'Aggiornamento importante disponibile.\nAggiornare la macchina scaricando il file di aggiornamento da [Scarica il programma di aggiornamento] nelle “Impostazioni della macchina”.';

  @override
  String get t_err_updateinfo_02 => 'Collegare la macchina a una rete wireless per ricevere le notifiche sul software più recente. \nOppure visitare il sito web di supporto Brother per gli aggiornamenti software più recenti.';

  @override
  String get t_err_removecarriage => 'Spegnere la macchina prima di collegare o rimuovere l\'unità per ricamare.';

  @override
  String get t_err_filter_removed => 'Il filtro è stato cancellato perché la categoria non è supportata.';

  @override
  String get t_err_filter_cleared => 'Cancellare il filtro perché la funzione filtro non è applicabile in questa categoria.';

  @override
  String get t_principal07 => '[Leva del piedino premistoffa]';

  @override
  String get t_principal07_01 => '\nSollevare e abbassare la leva del piedino premistoffa per sollevare e abbassare il piedino premistoffa.';

  @override
  String get t_principal07_02 => '(a) Piedino premistoffa\n(b) Leva del piedino premistoffa';

  @override
  String get t_principal03 => '[Regolatore della velocità di cucitura]';

  @override
  String get t_principal03_01 => '\nUtilizzare questo regolatore per impostare la velocità di cucitura.\nSpostare la leva verso sinistra per diminuire la velocità di cucitura.\nSpostare la leva verso destra per aumentare la velocità di cucitura.';

  @override
  String get t_principal03_02 => '(a) Leva\n(b) Lento\n(c) Veloce';

  @override
  String get t_principal12 => '[Volantino manuale]';

  @override
  String get t_principal12_01 => 'Ruotare il volantino manuale verso di sé per alzare o abbassare l\'ago.\nIl volantino deve essere ruotato verso la parte anteriore della macchina.';

  @override
  String get t_principal08 => '[Prolunga base piana con scatola accessori]';

  @override
  String get t_principal08_01 => 'Riporre i piedini premistoffa e le spoline nella scatola accessori della prolunga base piana.\nQuando si cuciono parti cilindriche, rimuovere la prolunga base piana.';

  @override
  String get t_principal10 => '[Ginocchiera alzapiedino]';

  @override
  String get t_principal10_00 => '(a) Ginocchiera alzapiedino';

  @override
  String get t_principal10_01 => '\nÈ possibile utilizzare la ginocchiera alzapiedino per alzare e abbassare il piedino premistoffa con il ginocchio, lasciando entrambe le mani libere di maneggiare la stoffa.\n\n1. Allineare le linguette sulla ginocchiera alzapiedino con le fessure nella presa, quindi inserire la ginocchiera alzapiedino completamente all\'interno.';

  @override
  String get t_principal10_03_00 => '(a) Piedino premistoffa';

  @override
  String get t_principal10_03 => '\n2. Utilizzare il ginocchio per spostare la ginocchiera alzapiedino verso destra in modo da alzare il piedino premistoffa.\nRilasciare la ginocchiera alzapiedino verso sinistra per abbassare il piedino premistoffa.';

  @override
  String get t_principal11 => '[Pedale reostato]';

  @override
  String get t_principal11_00 => '\nÈ inoltre possibile utilizzare il pedale reostato per iniziare o interrompere la cucitura.\n1. Innestare la spina del pedale reostato nella relativa presa sulla macchina.';

  @override
  String get t_principal11_02 => '2. Premere lentamente il pedale reostato per iniziare a cucire.\nRilasciare il pedale reostato per arrestare la macchina.\n\n*La velocità impostata mediante il regolatore della velocità di cucitura corrisponde alla velocità di cucitura massima del pedale reostato.';

  @override
  String get t_xv_principal11_01 => 'Oltre all\'avvio e all\'interruzione della cucitura, con il pedale reostato multifunzione è possibile specificare l\'esecuzione di varie operazioni della macchina da cucire, tra cui il taglio del filo e il punto di fermatura.';

  @override
  String get t_xv_principal11_02 => '1. Allineare il lato largo della piastra di montaggio alla scanalatura sul fondo del pedale reostato principale, quindi fissarli insieme con una vite.';

  @override
  String get t_xv_principal11_03 => '2. Allineare l\'altro lato della piastra di montaggio alla scanalatura sul fondo del pedale laterale, quindi fissarli insieme con una vite.';

  @override
  String get t_xv_principal11_04 => '3. Inserire la spina per il pedale laterale nel jack sul retro del pedale reostato principale.';

  @override
  String get t_xv_principal11_05 => '4. Inserire la spina tonda per il pedale reostato principale nel jack del pedale reostato sul lato destro della macchina.';

  @override
  String get t_xv_principal11_06 => '5. Premere lentamente il pedale reostato per iniziare a cucire. Rilasciare il pedale reostato per arrestare la macchina.\n\n*La velocità impostata mediante il regolatore della velocità di cucitura corrisponde alla velocità di cucitura massima del pedale reostato.';

  @override
  String get t_principal11_01_02 => '(a) Pedale reostato\n(b) Presa per pedale reostato';

  @override
  String get t_principal09 => '[Interruttore di posizione griffa di trasporto]';

  @override
  String get t_principal09_01 => '\nAbbassare la griffa di trasporto tramite l\'interruttore di posizione griffa di trasporto.';

  @override
  String get t_principal09_02 => '(a) Interruttore di posizione griffa di trasporto';

  @override
  String get t_principal_buttons_01 => '[Pulsante \"Posizione ago\"]';

  @override
  String get t_principal_buttons_01_01 => 'Utilizzare questo pulsante quando si cambia la direzione di cucitura o per una cucitura dettagliata nelle aree di piccole dimensioni.\nPremere questo pulsante per alzare o abbassare la posizione dell\'ago.\nPremere due volte questo pulsante per cucire un punto singolo.';

  @override
  String get t_principal_buttons_02 => '[Pulsante \"Taglio del filo\"]';

  @override
  String get t_principal_buttons_02_01 => 'Dopo la cucitura, premere questo pulsante per tagliare il filo in eccesso.';

  @override
  String get t_principal_buttons_06 => '[Pulsante \"Alzapiedino premistoffa\"]';

  @override
  String get t_principal_buttons_06_01 => 'Premere questo pulsante per abbassare il piedino premistoffa e applicare pressione sul tessuto.\nPremere di nuovo questo pulsante per alzare il piedino premistoffa.\nTenere premuto il pulsante del piedino premistoffa per alzare il piedino premistoffa nella posizione di massima altezza.';

  @override
  String get t_principal_buttons_05 => '[Pulsante \"Infilatura automatica\"]';

  @override
  String get t_principal_buttons_05_01 => 'Utilizzare questo pulsante per guidare automaticamente il filo nell\'ago.';

  @override
  String get t_principal_buttons_04 => '[Pulsante \"Avvio/stop\"]';

  @override
  String get t_principal_buttons_04_01 => 'Premere questo pulsante affinché la macchina esegua alcuni punti a velocità ridotta e quindi inizi a cucire alla velocità impostata nel dispositivo di regolazione della velocità di cucitura.\nPremere nuovamente questo pulsante per arrestare la macchina.\nTenere premuto questo pulsante per cucire alla velocità minima consentita dalla macchina.\nIl pulsante cambia di colore in base alla modalità di funzionamento della macchina.\n\nVerde: la macchina è pronta per cucire o sta cucendo.\nRosso: la macchina non può cucire.';

  @override
  String get t_principal_buttons_03 => '[Pulsante \"Punti di fermatura\"]';

  @override
  String get t_principal_buttons_03_01 => 'Utilizzare questo pulsante per cucire punti di fermatura all\'inizio e alla fine della cucitura.\nPer i disegni/punti diritti e zig-zag, la macchina cucirà i punti di fermatura a bassa velocità, tenendo premuto il pulsante punto \"Fermatura\". (I punti vengono cuciti nella direzione opposta).\nPer gli altri disegni/punti, la macchina cucirà punti di rinforzo. Premere questo pulsante. La macchina cuce 3 - 5 punti nello stesso punto e si arresta automaticamente.';

  @override
  String get t_principal_buttons_07 => '[Pulsante \"Rinforzo/Fermatura\"]';

  @override
  String get t_principal_buttons_07_01 => 'Utilizzare questo pulsante per cucire punti di rinforzo all\'inizio e alla fine della cucitura.\nPer i disegni/punti utili, premere questo pulsante. La macchina cuce 3 - 5 punti nello stesso punto e si arresta automaticamente.\nPer i disegni/punti carattere/decorativi, la macchina cucirà punti di rinforzo dopo aver cucito la singola unità del disegno.';

  @override
  String get t_basic13 => '[Infilatura superiore]';

  @override
  String get t_basic13_01_02 => '(a) Pulsante \"Alzapiedino premistoffa\"\n';

  @override
  String get t_basic13_01 => 'Premere il tasto \"film\" (Motion picture) per visualizzare un video con istruzioni.\n\n1. Premere il pulsante \"Alzapiedino premistoffa\" per sollevare il piedino premistoffa. ';

  @override
  String get t_basic13_02_00 => '(a) Pulsante \"Posizione ago\"';

  @override
  String get t_basic13_02 => '\n2. Premere il pulsante \"Posizione ago\" per sollevare l\'ago.';

  @override
  String get t_basic13_03_02 => '(a) Portarocchetto\n(b) Fermo per rocchetto\n(c) Rocchetto di filo ';

  @override
  String get t_basic13_03 => '\n3. Orientare il portarocchetto in modo che sia puntato verso l\'alto.\nPorre il rocchetto di filo sul portarocchetto in modo che il filo si svolga dalla parte anteriore del rocchetto.\nInserire completamente il fermo per rocchetto sul portarocchetto, quindi riportare il portarocchetto nella posizione originale. ';

  @override
  String get t_basic13_11_02 => '(a) Fermo per rocchetto (piccolo)  \n(b) Rocchetto di filo (filo con avvolgimento intrecciato) \n(c) Spazio ';

  @override
  String get t_basic13_11 => '\nQuando si cuce con filo sottile con avvolgimento intrecciato, utilizzare il fermo piccolo per rocchetto e lasciare dello spazio tra il fermo e il rocchetto di filo.';

  @override
  String get t_basic13_04_02 => '(a) Piastra guidafilo ';

  @override
  String get t_basic13_04 => '\n4. Tenendo il filo con entrambe le mani, passarlo dal basso verso l\'alto sul lato sinistro della piastra guidafilo.';

  @override
  String get t_basic13_05 => '5. Tenendo il rocchetto di filo nella mano destra, seguire il percorso del filo tenendo l\'estremità del filo con la mano sinistra. Passare il filo dal basso verso l\'alto, quindi di nuovo in basso attraverso le scanalature.';

  @override
  String get t_basic13_06_02 => '(a) Guidafilo della barra dell\'ago';

  @override
  String get t_basic13_06 => '\n6. Passare il filo dietro il guidafilo della barra dell\'ago.\n';

  @override
  String get t_basic13_07 => '7. Premere il pulsante \"Alzapiedino premistoffa\" per abbassare il piedino premistoffa.';

  @override
  String get t_basic13_08_02 => '(a) Dischi del guidafilo ';

  @override
  String get t_basic13_08 => '\n8. Far passare il filo attraverso i dischi del guidafilo. Accertarsi che il filo passi attraverso la scanalatura nel guidafilo. Il filo deve entrare saldamente nei dischi del guidafilo. È possibile non infilare l\'ago. \n';

  @override
  String get t_basic13_09_02 => '(b) Taglio del filo';

  @override
  String get t_basic13_09 => '\n9. Tirare il filo verso l\'alto passando sotto il tagliafilo per tagliarlo. ';

  @override
  String get t_basic13_10_02 => '(a) Pulsante \"Infilatura automatica\"';

  @override
  String get t_basic13_10 => '\n10. Premere il pulsante \"Infilatura automatica\" per l\'infilatura automatica dell\'ago.';

  @override
  String get t_basic14 => '[Avvolgimento spolina]';

  @override
  String get t_basic14_01_02 => '(a) Scanalatura della spolina\n(b) Molla sull\'albero';

  @override
  String get t_basic14_00 => '\nPremere il tasto \"film\" (Motion picture) per visualizzare un video con istruzioni.\n\n1. Allineare la scanalatura nella spolina alla molla sull\'albero avvolgitore della spolina e quindi posizionare la spolina sull\'albero. ';

  @override
  String get t_basic14_02 => '\n2. Impostare il portarocchetto supplementare in posizione \"Su\". ';

  @override
  String get t_basic14_02_02 => '(a) Portarocchetto supplementare ';

  @override
  String get t_basic14_03 => '\n3. Collocare il rocchetto di filo sul portarocchetto supplementare, in modo che il filo si svolga dalla parte anteriore. Quindi, inserire completamente il fermo per rocchetto sul portarocchetto in modo da fissare il rocchetto di filo. ';

  @override
  String get t_basic14_03_02 => '(a) Fermo per rocchetto\n(b) Portarocchetto\n(c) Rocchetto di filo ';

  @override
  String get t_basic14_04 => '\n4. Con la mano destra, tenere il filo vicino al rocchetto di filo. Con la mano sinistra, tenere l\'estremità del filo ed utilizzare entrambe le mani per far scorrere il filo attorno all\'apposita guida. ';

  @override
  String get t_basic14_04_02 => '(a) Guidafilo';

  @override
  String get t_basic14_05 => '\n5. Far scorrere il filo attorno al disco di pretensionamento assicurandosi che il filo passi sotto il disco di pretensionamento.  ';

  @override
  String get t_basic14_05_02 => '(a)  Disco di pretensionamento';

  @override
  String get t_basic14_06 => '6. Avvolgere il filo in senso orario attorno alla spolina per 5 o 6 volte.';

  @override
  String get t_basic14_07 => '(a) Fessura di guida (con tagliafilo incorporato)\n(b) Alloggiamento dell\'avvolgitore spolina\n\n7. Passare l\'estremità del filo attraverso la fessura di guida nell\'alloggiamento dell\'avvolgitore spolina, quindi tirare il filo verso destra per tagliare il filo con il tagliafilo. ';

  @override
  String get t_basic14_08_02 => '\n8. Spingere l\'interruttore di avvolgimento spolina verso sinistra fino a farla scattare in posizione. Sul display LCD viene visualizzata la finestra di avvolgimento spolina.';

  @override
  String get t_basic14_08_03 => '(a) Interruttore di avvolgimento spolina';

  @override
  String get t_basic14_08_04 => '\n* Utilizzare il regolatore dell\'avvolgimento spolina per regolare la quantità del filo avvolto sulla spolina su uno dei cinque livelli.';

  @override
  String get t_basic14_08_05 => '(a) Regolatore dell\'avvolgimento spolina\n(b) Di più\n(c) Di meno';

  @override
  String get t_basic14_09 => '9. Premere il tasto \"Avvio/Stop avvolgimento spolina\". La spolina inizia ad avvolgersi automaticamente.\n';

  @override
  String get t_basic14_10 => '10. È possibile modificare la velocità di avvolgimento premendo  - o + nella finestra di avvolgimento spolina. Premere \"OK\" per ridurre a icona la finestra di avvolgimento spolina.';

  @override
  String get t_basic14_101 => '11. La spolina smette di ruotare ad avvolgimento terminato. L\'interruttore di avvolgimento spolina ritorna nella posizione iniziale.';

  @override
  String get t_basic14_102 => '12. Tagliare il filo con il tagliafilo e rimuovere la spolina. ';

  @override
  String get t_basic14_11 => '\n*Uso del portarocchetto\nÈ possibile usare il portarocchetto principale per avvolgere la spolina prima di cucire.\n\nNota: non utilizzare questo portarocchetto per avvolgere la spolina mentre la macchina cuce.\n\n1. Allineare la scanalatura nella spolina alla molla sull\'albero avvolgitore della spolina e quindi posizionare la spolina sull\'albero. ';

  @override
  String get t_basic14_11_02 => '(a) Scanalatura della spolina\n(b) Molla sull\'albero ';

  @override
  String get t_basic14_12 => '\n2. Orientare il portarocchetto in modo che sia puntato verso l\'alto.\nPorre il rocchetto di filo sul portarocchetto in modo che il filo si svolga dalla parte anteriore del rocchetto.\nInserire completamente il fermo per rocchetto sul portarocchetto, quindi riportare il portarocchetto nella posizione originale. ';

  @override
  String get t_basic14_12_02 => '(a) Portarocchetto\n(b) Fermo per rocchetto\n(c) Rocchetto di filo ';

  @override
  String get t_basic14_13 => '\n3. Tenendo il filo con le mani, far scorrere il filo nelle scanalature sulla piastra guidafilo.\nFar passare il filo attorno al guidafilo. ';

  @override
  String get t_basic14_13_02 => '(a) Piastra guidafilol\n(b) Guidafilo ';

  @override
  String get t_basic14_14 => '\n4. Far scorrere il filo attorno al disco di pretensionamento assicurandosi che il filo passi sotto il disco di pretensionamento.  ';

  @override
  String get t_basic14_15_02 => '(a) Disco di pretensionamento';

  @override
  String get t_basic14_16 => '5. Avvolgere il filo in senso orario attorno alla spolina per 5 o 6 volte.';

  @override
  String get t_basic14_17 => '(a) Fessura di guida (con tagliafilo incorporato)\n(b) Alloggiamento dell\'avvolgitore spolina\n\n6. Passare l\'estremità del filo attraverso la fessura di guida nell\'alloggiamento dell\'avvolgitore spolina, quindi tirare il filo verso destra per tagliare il filo con il tagliafilo. ';

  @override
  String get t_basic14_18 => '\n7. Spingere l\'interruttore di avvolgimento spolina verso sinistra fino a farla scattare in posizione. Sul display LCD viene visualizzata la finestra di avvolgimento spolina.';

  @override
  String get t_basic14_18_02 => '(a) Interruttore di avvolgimento spolina';

  @override
  String get t_basic14_20 => '8. Premere il tasto \"Avvio/Stop avvolgimento spolina\". La spolina inizia ad avvolgersi automaticamente.\n';

  @override
  String get t_basic14_201 => '9. È possibile modificare la velocità di avvolgimento premendo  - o + nella finestra di avvolgimento spolina. Premere \"OK\" per ridurre a icona la finestra di avvolgimento spolina.';

  @override
  String get t_basic14_202 => '10. La spolina smette di ruotare ad avvolgimento terminato. L\'interruttore di avvolgimento spolina ritorna nella posizione iniziale.';

  @override
  String get t_basic14_203 => '11. Tagliare il filo con il tagliafilo e rimuovere la spolina. ';

  @override
  String get t_basic14_21_02 => '\nQuando si cuce con filo sottile con avvolgimento intrecciato, utilizzare il fermo piccolo per rocchetto e lasciare dello spazio tra il fermo e il rocchetto di filo. ';

  @override
  String get t_basic14_21_03 => '(a) Fermo per rocchetto (piccolo)\n(b) Rocchetto di filo (filo con avvolgimento intrecciato) \n(c) Spazio ';

  @override
  String get t_basic15 => '[Sostituzione dell\'ago]';

  @override
  String get t_basic15_00 => '\nPer verificare che l\'ago non sia piegato:\nPer verificare l\'ago, posizionare la parte piana dell\'ago su una superficie piana.\nOsservare attentamente l\'ago dalla parte superiore e sui lati.\nGettare gli aghi che risultano piegati.';

  @override
  String get t_basic15_00_01 => '(a) Spazio parallelo\n(b) Superficie piana (sportello spolina, vetro, ecc.) ';

  @override
  String get t_basic15_01 => '1. Premere il pulsante \"Posizione ago\" per sollevare l\'ago.';

  @override
  String get t_basic15_02 => '2. Premere il tasto Piedino premistoffa/sostituzione ago.';

  @override
  String get t_basic15_03 => '3. Utilizzare il cacciavite per ruotare la vite verso la parte anteriore della macchina e allentarla, quindi rimuovere l\'ago.';

  @override
  String get t_basic15_04 => '\n4. Con il lato piatto dell\'ago rivolto verso la parte posteriore della macchina, inserire completamente il nuovo ago sulla parte superiore dell\'arresto per ago (finestra di visione) nel morsetto ago.\nUtilizzare un cacciavite per serrare saldamente la vite morsetto ago.';

  @override
  String get t_basic15_04_02 => '(a) Arresto per ago\n(b) Foro per inserimento ago\n(c) Lato piatto dell\'ago';

  @override
  String get t_basic15_05 => '5. Premere il tasto Piedino premistoffa/sostituzione ago per sbloccare tutti i pulsanti di funzionamento.';

  @override
  String get t_basic16 => '[Sostituzione del piedino premistoffa]';

  @override
  String get t_basic16_01 => '*Rimozione del piedino premistoffa\n\n1. Premere il pulsante \"Posizione ago\" per sollevare l\'ago.';

  @override
  String get t_basic16_02 => '2. Premere il tasto Piedino premistoffa/sostituzione ago.';

  @override
  String get t_basic16_03 => '\n3. Sollevare la leva del piedino premistoffa.';

  @override
  String get t_basic16_03_02 => '(a) Piedino premistoffa\n(b) Leva del piedino premistoffa';

  @override
  String get t_basic16_04 => '\n4. Premere il pulsante nero situato sul retro del supporto del piedino premistoffa e rimuovere il piedino.';

  @override
  String get t_basic16_04_02 => '(a) Pulsante nero\n(b) Supporto del piedino premistoffa';

  @override
  String get t_basic16_05 => '\n*Montaggio del piedino premistoffa\n\n1. Posizionare il nuovo piedino premistoffa sotto il relativo supporto, allineando il perno del piedino alla fessura sul supporto.\nAbbassare la leva del piedino premistoffa in modo che il perno del piedino si agganci nella fessura del supporto del piedino.';

  @override
  String get t_basic16_05_02 => '(a) Fessura\n(b) Perno';

  @override
  String get t_basic16_06 => '2. Premere il tasto Piedino premistoffa/sostituzione ago per sbloccare tutti i pulsanti di funzionamento.';

  @override
  String get t_basic16_07 => '(a) Piedino premistoffa\n(b) Leva del piedino premistoffa\n\n3. Sollevare la leva del piedino premistoffa.';

  @override
  String get t_basic17 => '[Inserimento della spolina]';

  @override
  String get t_basic17_01 => 'Premere il tasto \"film\" (Motion picture) per visualizzare un video con istruzioni.\n\n1. Premere il pulsante \"Alzapiedino premistoffa\" per sollevare il piedino premistoffa.';

  @override
  String get t_basic17_02 => '(a) Sportello della spolina\n(b) Fermo\n\n2. Far scorrere il fermo dello sportello della spolina verso destra. Lo sportello della spolina si aprirà.\nRimuovere lo sportello della spolina.';

  @override
  String get t_basic17_03 => '3. Tenere la spolina con la mano destra e l\'estremità del filo con la mano sinistra.';

  @override
  String get t_basic17_04 => '4. Inserire la spolina nella navette in modo che il filo si svolga verso sinistra.\nCon delicatezza, tenere ferma la spolina con la mano destra e con la sinistra guidare il filo come indicato nella figura.';

  @override
  String get t_basic17_05 => '\n5. Far scorrere il filo attraverso la guida e quindi estrarre il filo verso la parte anteriore.\nIl tagliafilo reciderà il filo.';

  @override
  String get t_basic17_05_02 => '(a) Tagliafilo';

  @override
  String get t_basic17_06 => '6. Inserire la linguetta nell\'angolo in basso a sinistra dello sportello della spolina e quindi premere leggermente verso il basso sul lato destro per chiudere lo sportello.';

  @override
  String get t_embbasic17 => '[Applicazione di materiali stabilizzatori termoadesivi (rivestimento) al tessuto]';

  @override
  String get t_embbasic17_00 => '\nPer risultati ottimali dei lavori di ricamo, utilizzare sempre del materiale stabilizzatore per ricamo.\nSeguire le istruzioni fornite per il materiale stabilizzatore utilizzato.';

  @override
  String get t_embbasic17_01 => '\n1. Utilizzare del materiale stabilizzatore che sia più ampio del telaio da ricamo.';

  @override
  String get t_embbasic17_01_02 => '(a) Dimensioni del telaio da ricamo\n(b) Materiale stabilizzatore termoadesivo (rivestimento) ';

  @override
  String get t_embbasic17_02 => '\n2. Stirare il materiale stabilizzatore termoadesivo sul rovescio del tessuto.\n\n*Quando si utilizza tessuto che non può essere stirato (ad esempio, asciugamani o tessuti che si espandono con la stiratura) o su tessuti con aree difficili da stirare, collocare il materiale stabilizzatore sotto il tessuto senza applicarlo, quindi inserire il tessuto e lo stabilizzatore nel telaio da ricamo; oppure, contattare il rivenditore locale per informazioni sul tipo appropriato di materiale stabilizzatore da utilizzare.';

  @override
  String get t_embbasic17_02_02 => '(a) Lato adesivo del materiale stabilizzatore\n(b) Tessuto (rovescio) ';

  @override
  String get t_embbasic17_03 => '\n*Quando si esegue il ricamo su tessuti sottili quali organza o batista o su tessuti ruvidi quali asciugamani o fustagno, utilizzare un materiale stabilizzatore solubile in acqua (venduto separatamente) per ottenere risultati ottimali.\nIl materiale stabilizzatore solubile si scioglierà completamente nell\'acqua, garantendo un lavoro con finitura ottimale.';

  @override
  String get t_embbasic18 => '[Inserimento del tessuto]';

  @override
  String get t_embbasic18_01 => '1. Sollevare e allentare la vite di regolazione del telaio da ricamo esterno e rimuovere il telaio interno.';

  @override
  String get t_embbasic18_02 => '\n2. Porre il lato destro del tessuto verso l\'alto sopra il telaio esterno.\nInserire nuovamente il telaio interno assicurandosi che il Δ del telaio interno sia allineato al Δ del telaio esterno.';

  @override
  String get t_embbasic18_02_02 => '(a) Telaio interno\n(b) Telaio esterno\n(c) Vite di regolazione';

  @override
  String get t_embbasic18_03 => '3. Serrare leggermente la vite di regolazione del telaio, quindi tendere il tessuto tirando sui bordi e sugli angoli.\nNon allentare la vite.';

  @override
  String get t_embbasic18_04 => '\n4. Tendere delicatamente il tessuto, quindi serrare la vite di regolazione del telaio per impedire al tessuto di allentarsi dopo essere stato teso.\n\n*Dopo aver tirato il tessuto, assicurarsi che il tessuto sia teso.\n\n* Assicurarsi che i telai interno ed esterno siano allineati prima di iniziare a ricamare.\n\n*Nota\nEstendere il tessuto da ogni angolo e ogni lato. Mentre si estende il tessuto, serrare la vite di regolazione del telaio.';

  @override
  String get t_embbasic18_04_02 => '(a) Telaio esterno\n(b) Telaio interno\n(c) Tessuto';

  @override
  String get t_embbasic18_04_11 => '* Quando si utilizza un telaio per ricamo con una leva montata.\n';

  @override
  String get t_embbasic18_04_12 => '\n1. Abbassare la leva.';

  @override
  String get t_embbasic18_04_13 => '2. Allentare manualmente la vite di regolazione del telaio e rimuovere il telaio interno.';

  @override
  String get t_embbasic18_04_14 => '\n3. Posizionare il tessuto.';

  @override
  String get t_embbasic18_04_15 => '4. Serrare manualmente la vite di regolazione del telaio.';

  @override
  String get t_embbasic18_04_16 => '5. Riportare la leva nella posizione originale.';

  @override
  String get t_embbasic18_05 => '\n[Utilizzo del foglio per ricamo]';

  @override
  String get t_embbasic18_05_01 => 'Quando si desidera ricamare un disegno in una posizione particolare, utilizzare il foglio per ricamo insieme al telaio.\n\n1. Con un gessetto, segnare l\'area del tessuto da ricamare.';

  @override
  String get t_embbasic18_05_02 => '(a) Disegno da ricamo\n(b) Segno';

  @override
  String get t_embbasic18_06 => '\n2. Posizionare il foglio per ricamo nel telaio interno. Allineare le linee guida del foglio per ricamo ai segni eseguiti sul tessuto.';

  @override
  String get t_embbasic18_06_02 => '(a) Telaio interno\n(b) Linea guida';

  @override
  String get t_embbasic18_07 => '\n3. Tendere delicatamente il tessuto in modo che non vi siano pieghe o arricciature e quindi inserire il telaio interno nel telaio esterno.';

  @override
  String get t_embbasic18_07_02 => '(a) Telaio interno\n(b) Telaio esterno';

  @override
  String get t_embbasic18_08 => '4. Rimuovere il foglio per ricamo.';

  @override
  String get t_embbasic19 => '[Montaggio del telaio da ricamo]';

  @override
  String get t_embbasic19_01 => '*Prima di montare il telaio da ricamo, avvolgere e inserire la spolina.\n\n1. Premere il pulsante \"Alzapiedino premistoffa\" per sollevare il piedino premistoffa.';

  @override
  String get t_embbasic19_02 => '\n2. Sollevare la leva di fissaggio del telaio.';

  @override
  String get t_embbasic19_03 => '\n3. Allineare la guida del telaio da ricamo al bordo destro del supporto per telaio.';

  @override
  String get t_embbasic19_03_02 => '(a) Supporto per telaio da ricamo\n(b) Guida del telaio da ricamo';

  @override
  String get t_embbasic19_04 => '4. Far scorrere il telaio da ricamo sul supporto, assicurandosi di allineare il Δ del telaio da ricamo con il Δ del supporto.';

  @override
  String get t_embbasic19_05 => '\n5. Abbassare la leva di fissaggio del telaio per fissare il telaio da ricamo al supporto del telaio da ricamo.\n\n*Se la leva di fissaggio del telaio non è abbassata, il disegno da ricamo potrebbe non essere cucito in modo corretto o il piedino premistoffa potrebbe colpire il telaio e provocare lesioni personali.';

  @override
  String get t_embbasic19_05_02 => '(a) Leva di fissaggio del telaio';

  @override
  String get t_embbasic19_06 => '\n[Rimozione del telaio da ricamo]\n1. Sollevare la leva di fissaggio del telaio.';

  @override
  String get t_embbasic19_07 => '2. Tirare il telaio da ricamo verso di sé.';

  @override
  String get t_embbasic20 => '[Montaggio dell\'unità da ricamo]';

  @override
  String get t_embbasic20_01 => 'Prima di spegnere la macchina, leggere i passaggi descritti di seguito.\n\n1. Spegnere la macchina e rimuovere la prolunga base piana (se la macchina ne è dotata).';

  @override
  String get t_embbasic20_03 => '\n2. Inserire il connettore dell\'unità da ricamo nella porta di connessione della macchina. Spingere leggermente fino alla posizione di arresto.';

  @override
  String get t_embbasic20_03_02 => '(a) Connettore dell\'unità da ricamo\n(b) Porta di connessione della macchina';

  @override
  String get t_embbasic20_04 => '(a) SPENTO\n(b) ACCESO\n\n3. Accendere l\'interruttore generale della macchina.';

  @override
  String get t_embbasic20_05 => '4. Premere il tasto OK. Il carrello si porterà nella posizione di inizializzazione.';

  @override
  String get t_embbasic20_06 => '[Rimozione dell\'unità da ricamo]\n';

  @override
  String get t_embbasic20_06_02 => '\n(a) SPENTO\n(b) ACCESO\n\nPrima di spegnere la macchina, leggere i passaggi descritti di seguito.\n\n1. Spegnere l\'interruttore generale della macchina.';

  @override
  String get t_embbasic20_07 => '(a) Pulsante di rilascio (ubicato sotto l\'unità da ricamo)\n\n2. Tenere premuto il pulsante di rilascio e rimuovere l\'unità da ricamo allontanandola dalla macchina.';

  @override
  String get t_xp_embbasic21 => '[Montaggio del piedino per ricamo \"W\"]';

  @override
  String get t_xp_embbasic21_01 => '1. Premere il pulsante \"Posizione ago\" per sollevare l\'ago.';

  @override
  String get t_xp_embbasic21_02 => '2. Premere il tasto Piedino premistoffa/sostituzione ago.';

  @override
  String get t_xp_embbasic21_03 => '\n3. Sollevare la leva del piedino premistoffa.';

  @override
  String get t_xp_embbasic21_04 => '\n4. Premere il pulsante nero del supporto del piedino premistoffa e rimuovere il piedino.';

  @override
  String get t_xp_embbasic21_04_02 => '(a) Pulsante nero\n(b) Supporto del piedino premistoffa';

  @override
  String get t_xp_embbasic21_05 => '\n5. Utilizzare il cacciavite in dotazione per allentare la vite del supporto del piedino e quindi rimuovere il supporto.';

  @override
  String get t_xp_embbasic21_05_02 => '(a) Cacciavite\n(b) Supporto del piedino premistoffa\n(c) Vite del supporto del piedino premistoffa';

  @override
  String get t_xp_embbasic21_06 => '(a) Leva del piedino premistoffa\n\n6. Abbassare la leva del piedino premistoffa.';

  @override
  String get t_xp_embbasic21_07_01 => '(a) Barra del piedino premistoffa\n';

  @override
  String get t_xp_embbasic21_07_02 => '7. Posizionare il piedino da ricamo \"W\" sulla barra del piedino premistoffa da dietro.';

  @override
  String get t_xp_embbasic21_08_01 => '(a) Vite del supporto del piedino premistoffa\n';

  @override
  String get t_xp_embbasic21_08_02 => '8. Tenere in posizione il piedino da ricamo con la mano destra, quindi utilizzare il cacciavite in dotazione per serrare saldamente la vite del supporto del piedino premistoffa.';

  @override
  String get t_xp_embbasic21_09 => '9. Premere il tasto Piedino premistoffa/sostituzione ago per sbloccare tutti i pulsanti di funzionamento.';

  @override
  String get t_embbasic21 => '[Montaggio del piedino per ricamo \"W\"]';

  @override
  String get t_embbasic21_01 => '1. Premere il pulsante \"Posizione ago\" per sollevare l\'ago.';

  @override
  String get t_embbasic21_02 => '2. Premere il tasto Piedino premistoffa/sostituzione ago.';

  @override
  String get t_embbasic21_03 => '\n3. Sollevare la leva del piedino premistoffa.';

  @override
  String get t_embbasic21_04 => '\n4. Premere il pulsante nero del supporto del piedino premistoffa e rimuovere il piedino.';

  @override
  String get t_embbasic21_04_02 => '(a) Pulsante nero\n(b) Supporto del piedino premistoffa';

  @override
  String get t_embbasic21_05 => '\n5. Utilizzare il cacciavite in dotazione per allentare la vite del supporto del piedino e quindi rimuovere il supporto.';

  @override
  String get t_embbasic21_05_02 => '(a) Cacciavite\n(b) Supporto del piedino premistoffa\n(c) Vite del supporto del piedino premistoffa';

  @override
  String get t_embbasic21_06 => '(a) Leva del piedino premistoffa\n\n6. Abbassare la leva del piedino premistoffa.';

  @override
  String get t_embbasic21_07 => '(a) Braccio\n(b) Vite morsetto ago\n(c) Vite del supporto del piedino premistoffa\n(d) Spazzola di pulizia\n\n7. Posizionare il piedino per ricamo \"W\" sulla barra del piedino premistoffa in modo che il braccio del piedino per ricamo \"W\" sia in contatto con la parte posteriore del supporto ago.';

  @override
  String get t_embbasic21_08 => '8. Utilizzare il cacciavite in dotazione per serrare saldamente la vite del supporto del piedino premistoffa.';

  @override
  String get t_embbasic21_09 => '9. Premere il tasto Piedino premistoffa/sostituzione ago per sbloccare tutti i pulsanti di funzionamento.';

  @override
  String get t_embbasic21_emb_07 => '(a) Braccio\n(b) Vite morsetto ago\n(c) Vite del supporto del piedino premistoffa\n(d) Spazzola di pulizia\n\n3. Posizionare il piedino per ricamo \"W\" sulla barra del piedino premistoffa in modo che il braccio del piedino per ricamo \"W\" sia in contatto con la parte posteriore del supporto ago.';

  @override
  String get t_embbasic21_emb_08 => '4. Utilizzare il cacciavite in dotazione per serrare saldamente la vite del supporto del piedino premistoffa.';

  @override
  String get t_embbasic21_emb_09 => '5. Premere il tasto Piedino premistoffa/sostituzione ago per sbloccare tutti i pulsanti di funzionamento.';

  @override
  String get t_xv_embbasic21 => '[Montaggio del piedino da ricamo \"W+\"]';

  @override
  String get t_xv_embbasic21_05 => '\n5. Utilizzare il cacciavite in dotazione per togliere la vite del supporto del piedino premistoffa, quindi rimuovere il supporto del piedino premistoffa.';

  @override
  String get t_xv_embbasic21_07_01 => '(a) Barra del piedino premistoffa\n';

  @override
  String get t_xv_embbasic21_07_02 => '7. Posizionare il piedino da ricamo \"W+\" sulla barra del piedino premistoffa da dietro.';

  @override
  String get t_xv_embbasic21_08_01 => '(a) Vite del supporto del piedino premistoffa\n';

  @override
  String get t_xv_embbasic21_08_02 => '8. Tenere in posizione il piedino da ricamo con la mano destra, quindi utilizzare il cacciavite in dotazione per serrare saldamente la vite del supporto del piedino premistoffa.';

  @override
  String get t_xv_embbasic21_09 => '9. Inserire il connettore del piedino da ricamo \"W+\" con puntatore LED nel jack sul lato sinistro della macchina.';

  @override
  String get t_xv_embbasic21_10 => '10. Premere il tasto Piedino premistoffa/sostituzione ago per sbloccare tutti i pulsanti di funzionamento.';

  @override
  String get t_embbasic22 => '[Corretto materiale stabilizzatore da usare]';

  @override
  String get t_embbasic22_00_01 => '1. Tessuti che possono essere stirati';

  @override
  String get t_embbasic22_00_02 => '2. Tessuti che non possono essere stirati';

  @override
  String get t_embbasic22_00_03 => '3. Tessuti sottili';

  @override
  String get t_embbasic22_00_04 => '4. Tessuti ruvidi';

  @override
  String get t_embbasic22_00_05 => '\nPer risultati ottimali dei lavori di ricamo, utilizzare sempre del materiale stabilizzatore per ricamo. Seguire le istruzioni fornite per il materiale stabilizzatore utilizzato.';

  @override
  String get t_embbasic22_01 => '\n[1. Tessuti che possono essere stirati]';

  @override
  String get t_embbasic22_01_02 => '\nStirare il materiale stabilizzatore termoadesivo sul rovescio del tessuto. Utilizzare un materiale stabilizzatore che sia più ampio del telaio da ricamo.';

  @override
  String get t_embbasic22_01_03 => '(a) Dimensioni del telaio da ricamo\n(b) Materiale stabilizzatore termoadesivo (rivestimento) ';

  @override
  String get t_embbasic22_02 => '[2. Tessuti che non possono essere stirati]';

  @override
  String get t_embbasic22_02_02 => '\nPosizionare il materiale stabilizzatore sotto il tessuto senza applicarlo e quindi posizionare il tessuto e il materiale stabilizzatore nel telaio da ricamo.';

  @override
  String get t_embbasic22_03 => '[3. Tessuti sottili]';

  @override
  String get t_embbasic22_03_02 => '\nUtilizzare un materiale stabilizzatore solubile in acqua (venduto separatamente) per ottenere risultati ottimali. Il materiale stabilizzatore solubile si scioglierà completamente nell\'acqua, garantendo un lavoro con finitura ottimale.';

  @override
  String get t_embbasic22_04 => '[4. Tessuti ruvidi]';

  @override
  String get t_embbasic22_04_02 => '\nQuando si utilizza del tessuto che non può essere stirato (ad esempio, asciugamani o tessuti che si espandono con la stiratura) , collocare il materiale stabilizzatore sotto il tessuto senza applicarlo, quindi inserire il tessuto e lo stabilizzatore nel telaio da ricamo, oppure utilizzare un materiale stabilizzatore solubile in acqua (venduto separatamente) .';

  @override
  String get t_embbasic23 => '[Regolazione della tensione del filo]';

  @override
  String get t_embbasic23_01 => 'Quando si ricama, la tensione del filo deve essere tale che il filo superiore si possa appena vedere sul rovescio del tessuto.';

  @override
  String get t_embbasic23_01_01 => '1. Corretta tensione del filo';

  @override
  String get t_embbasic23_01_02 => '2. Il filo superiore è troppo teso';

  @override
  String get t_embbasic23_01_03 => '3. Il filo superiore è poco teso';

  @override
  String get t_embbasic23_02 => '[1. Corretta tensione del filo]';

  @override
  String get t_embbasic23_02_02 => '\nIl disegno si vede dal rovescio del tessuto. Se la tensione del filo non è corretta, il disegno non verrà completato bene. Il tessuto potrebbe arricciarsi o il filo potrebbe spezzarsi.';

  @override
  String get t_embbasic23_03 => '[2. Il filo superiore è troppo teso]';

  @override
  String get t_embbasic23_03_02 => '\nLa tensione del filo superiore è eccessiva, rendendo visibile il filo della spolina sulla superficie del tessuto.';

  @override
  String get t_embbasic23_03_03 => 'Premere - per allentare la tensione del filo superiore.';

  @override
  String get t_embbasic23_04 => '[3. Il filo superiore è poco teso]';

  @override
  String get t_embbasic23_04_02 => '\nLa tensione del filo superiore è insufficiente, il filo superiore sarà poco teso e gli occhielli del filo si potrebbero allentare oppure potrebbero apparire degli arricciamenti sul dritto del tessuto.';

  @override
  String get t_embbasic23_04_03 => 'Premere + per aumentare la tensione del filo.';

  @override
  String get t_trouble22 => '[Rottura del filo superiore] ';

  @override
  String get t_trouble22_01 => '*Causa 1\nL\'infilatura della macchina non è corretta (fermo per rocchetto errato o allentato, filo non preso nell\'avvolgitore, ecc.). \n\n*Soluzione\nEseguire nuovamente l\'infilatura della macchina procedendo in modo corretto.';

  @override
  String get t_trouble22_02 => '*Causa 2\nIl filo è annodato o ingarbugliato.\n\n*Soluzione\nInfilare nuovamente i fili superiore e inferiore.';

  @override
  String get t_trouble22_03 => '*Causa 3\nIl filo è troppo spesso per l\'ago.\n\n*Soluzione\nVerificare le combinazioni di ago e filo.';

  @override
  String get t_trouble22_04 => '*Causa 4\nLa tensione del filo superiore è eccessiva.\n\n*Soluzione\nRegolare la tensione del filo.';

  @override
  String get t_trouble22_05 => '*Causa 5\nIl filo è attorcigliato.\n\n*Soluzione\nUtilizzare delle forbici per tagliare il filo attorcigliato e rimuoverlo dalla guida del crochet, ecc.';

  @override
  String get t_trouble22_06 => '*Causa 6\nL\'ago è girato, piegato o con punta smussata.\n\n*Soluzione\nSostituire l\'ago.';

  @override
  String get t_trouble22_07 => '*Causa 7\nL\'ago è installato in modo errato.\n\n*Soluzione\n7. Reinstallare l\'ago in modo corretto.';

  @override
  String get t_trouble22_08 => '*Causa 8\nVi sono graffi vicino al foro della piastra per ago.\n\n*Soluzione\nSostituire la piastra per ago o contattare il proprio rivenditore locale.';

  @override
  String get t_trouble22_09 => '*Causa 9\nVi sono graffi vicino al foro del piedino premistoffa.\n\n*Soluzione\nSostituire il piedino premistoffa o contattare il proprio rivenditore locale.';

  @override
  String get t_trouble22_10 => '*Causa 10\nVi sono graffi nella guida del crochet.\n\n*Soluzione\nSostituire la guida del crochet o contattare il proprio rivenditore locale.';

  @override
  String get t_trouble23 => '[Rotture del filo della spolina]';

  @override
  String get t_trouble23_01 => '*Causa 1\nLa spolina è installata in modo errato.\n\n*Soluzione\nImpostare il filo della spolina in modo corretto.';

  @override
  String get t_trouble23_02 => '*Causa 2\nVi sono graffi sulla spolina o la spolina non ruota in modo corretto.\n\n*Soluzione\nSostituire la spolina.';

  @override
  String get t_trouble23_03 => '*Causa 3\nIl filo è attorcigliato.\n\n*Soluzione\nUtilizzare delle forbici per tagliare il filo attorcigliato e rimuoverlo dalla guida del crochet, ecc.';

  @override
  String get t_trouble24 => '[Punti saltati]';

  @override
  String get t_trouble24_01 => '*Causa 1\nL\'infilatura della macchina non è corretta.\n\n*Soluzione\nAttenersi ai passaggi per l\'infilatura della macchina e infilare il filo in modo corretto.';

  @override
  String get t_trouble24_02 => '*Causa 2\nUso di ago o filo non appropriato per il tessuto selezionato.\n\n*Soluzione\nVerificare la tabella delle \"combinazioni Tessuto, filo e ago\".';

  @override
  String get t_trouble24_03 => '*Causa 3\nL\'ago è girato, piegato o con punta smussata.\n\n*Soluzione\nSostituire l\'ago.';

  @override
  String get t_trouble24_04 => '*Causa 4\nL\'ago è installato in modo errato.\n\n*Soluzione\nReinstallare l\'ago in modo corretto.';

  @override
  String get t_trouble24_05 => '*Causa 5\nL\'ago è difettoso.\n\n*Soluzione\nSostituire l\'ago.';

  @override
  String get t_trouble24_06 => '*Causa 6\nAccumulo di polvere o filamenti sotto la piastra per ago.\n\n*Soluzione\nRimuovere la polvere o i filamenti con una spazzolina.';

  @override
  String get t_trouble25 => '[Arricciamenti del tessuto]';

  @override
  String get t_trouble25_01 => '*Causa 1\nSi è verificato un errore nell\'infilatura superiore o nella spolina.\n\n*Soluzione\nAttenersi ai passaggi per l\'infilatura della macchina e infilare il filo in modo corretto.';

  @override
  String get t_trouble25_02 => '*Causa 2\nIl fermo per rocchetto è installato in modo errato.\n\n*Soluzione\nControllare la procedura per il montaggio del fermo per rocchetto, quindi ricollegare il fermo.';

  @override
  String get t_trouble25_03 => '*Causa 3\nUso di ago o filo non appropriato per il tessuto selezionato.\n\n*Soluzione\nVerificare la tabella delle \"combinazioni Tessuto, filo e ago\".';

  @override
  String get t_trouble25_04 => '*Causa 4\nL\'ago è girato, piegato o con punta smussata.\n\n*Soluzione\nSostituire l\'ago.';

  @override
  String get t_trouble25_05 => '*Causa 5\nI punti sono troppo lunghi quando si cuce su tessuti leggeri.\n\n*Soluzione\nDiminuire la lunghezza del punto.';

  @override
  String get t_trouble25_06 => '*Causa 6\nLa tensione del filo non è impostata in modo corretto.\n\n*Soluzione\nRegolare la tensione del filo.';

  @override
  String get t_trouble25_07 => '*Causa 7\nPiedino premistoffa errato.\n\n*Soluzione\nCollegare il piedino premistoffa corretto.';

  @override
  String get t_trouble26 => '[La macchina è rumorosa]';

  @override
  String get t_trouble26_01 => '*Causa 1\nPresenza di polvere o filamenti nella griffa di trasporto.\n\n*Soluzione\nRimuovere la polvere o i filamenti.';

  @override
  String get t_trouble26_02 => '*Causa 2\nPresenza di filamenti nella guida del crochet.\n\n*Soluzione\nPulire la guida del crochet.';

  @override
  String get t_trouble26_03 => '*Causa 3\nL\'infilatura superiore non è corretta.\n\n*Soluzione\nAttenersi ai passaggi per l\'infilatura della macchina e infilare il filo in modo corretto.';

  @override
  String get t_trouble26_04 => '*Causa 4\nVi sono graffi nella guida del crochet.\n\n*Soluzione\nSostituire la guida del crochet o contattare il proprio rivenditore locale.';

  @override
  String get t_trouble27 => '[Impossibile utilizzare l\'infila ago]';

  @override
  String get t_trouble27_01 => '*Causa 1\nL\'ago non è nella posizione corretta.\n\n*Soluzione\nPremere il pulsante \"Posizione ago\" per sollevare l\'ago.';

  @override
  String get t_trouble27_02 => '*Causa 2\nIl gancio di infilatura non passa attraverso la cruna dell\'ago.\n\n*Soluzione\nPremere il pulsante \"Posizione ago\" per sollevare l\'ago.';

  @override
  String get t_trouble27_03 => '*Causa 3\nL\'ago è installato in modo errato.\n\n*Soluzione\nReinstallare l\'ago in modo corretto.';

  @override
  String get t_trouble28 => '[Tensione del filo non corretta]';

  @override
  String get t_trouble28_01 => '*Causa 1\nL\'infilatura superiore non è corretta.\n\n*Soluzione\nAttenersi ai passaggi per l\'infilatura della macchina e infilare il filo in modo corretto.';

  @override
  String get t_trouble28_02 => '*Causa 2\nLa spolina è installata in modo errato.\n\n*Soluzione\nImpostare nuovamente la spolina.';

  @override
  String get t_trouble28_03 => '*Causa 3\nUso di ago o filo non appropriato per il tessuto selezionato.\n\n*Soluzione\nVerificare la tabella delle \"combinazioni Tessuto, filo e ago\".';

  @override
  String get t_trouble28_04 => '*Causa 4\nIl supporto del piedino premistoffa non è montato in modo corretto.\n\n*Soluzione\nMontare il supporto del piedino premistoffa in modo corretto.';

  @override
  String get t_trouble28_05 => '*Causa 5\nLa tensione del filo non è impostata in modo corretto.\n\n*Soluzione\nRegolare la tensione del filo.';

  @override
  String get t_trouble29 => '[Carattere da ricamare non realizzato]';

  @override
  String get t_trouble29_01 => '*Causa 1\nÈ stato utilizzato un piedino premistoffa errato.\n\n*Soluzione\nCollegare il piedino premistoffa corretto.';

  @override
  String get t_trouble29_02 => '*Causa 2\nLe impostazioni di regolazione del disegno sono state specificate in modo errato.\n\n*Soluzione\nRicontrollare le impostazioni di regolazione del disegno.';

  @override
  String get t_trouble29_03 => '*Causa 3\nNon è stato utilizzato il materiale stabilizzatore su tessuti leggeri o su tessuti elastici.\n\n*Soluzione\nApplicare del materiale stabilizzatore.';

  @override
  String get t_trouble29_04 => '*Causa 4\nLa tensione del filo non è impostata in modo corretto.\n\n*Soluzione\nRegolare la tensione del filo.';

  @override
  String get t_trouble30 => '[Il disegno da ricamo non viene cucito in modo corretto]';

  @override
  String get t_trouble30_01 => '*Causa 1\nIl filo è attorcigliato.\n\n*Soluzione\nUtilizzare delle forbici per tagliare il filo attorcigliato e rimuoverlo dalla guida del crochet, ecc.';

  @override
  String get t_trouble30_02 => '*Causa 2\nIl tessuto non è stato inserito correttamente nel telaio (tessuto poco teso, ecc.) .\n\n*Soluzione\nSe il tessuto non è ben teso nel telaio, il disegno potrebbe essere cucito in modo scadente o potrebbe verificarsi un restringimento del disegno. Impostare correttamente il tessuto nel telaio.';

  @override
  String get t_trouble30_03 => '*Causa 3\nNon è stato applicato del materiale stabilizzatore.\n\n*Soluzione\nUtilizzare sempre del materiale stabilizzatore, in modo particolare quando si cuce su tessuti elastici, leggeri o con ordito grezzo, oppure su tessuti che provocano il restringimento del disegno. Per suggerimenti sul materiale stabilizzatore appropriato, rivolgersi al proprio rivenditore locale.';

  @override
  String get t_trouble30_04 => '*Causa 4\nPresenza di un oggetto vicino alla macchina; il carrello o il telaio da ricamo ha urtato l\'oggetto durante la cucitura.\n\n*Soluzione\nSe il telaio urta degli oggetti durante la cucitura, il disegno verrà cucito in modo scadente. Non collocare oggetti nell\'area di cucitura in modo da evitare eventuali urti accidentali durante la cucitura.';

  @override
  String get t_trouble30_05 => '*Causa 5\nIl tessuto esterno al telaio interferisce con il braccio di cucitura con impossibilità dell\'unità di ricamo di muoversi.\n\n*Soluzione\nReinserire il tessuto nel telaio da ricamo in modo che il tessuto in eccesso risulti lontano dal braccio di cucitura e ruotare il disegno di 180 gradi.';

  @override
  String get t_trouble30_06 => '*Causa 6\nIl tessuto è troppo pesante; l\'unità di ricamo non è in grado di muoversi liberamente.\n\n*Soluzione\nCollocare un libro grande e spesso o un oggetto simile sotto la testa del braccio, in modo da sollevare leggermente il lato pesante e mantenerlo in piano.';

  @override
  String get t_trouble30_07 => '*Causa 7\nIl tessuto sporge dalla tavola.\n\n*Soluzione\nSe il tessuto sporge dal tavolo durante il ricamo, l\'unità di ricamo non potrà spostarsi liberamente. Posizionare il tessuto in modo che non sporga dal tavolo (oppure, tenere il tessuto in modo da impedirne il trascinamento) .';

  @override
  String get t_trouble30_08 => '*Causa 8\nIl tessuto è impigliato da qualche parte.\n\n*Soluzione\nFermare la macchina e posizionare il tessuto in modo che non si impigli.';

  @override
  String get t_trouble30_09 => '*Causa 9\nIl telaio da ricamo è stato rimosso durante la cucitura (ad esempio, per reimpostare la spolina) . Il piedino premistoffa è stato urtato o spostato durante la rimozione o il montaggio del telaio da ricamo, oppure il telaio da ricamo è stato spostato.\n\n*Soluzione\nSe il piedino premistoffa viene urtato o l\'unità da ricamo viene spostata durante la cucitura, il disegno non verrà cucito in modo appropriato. Fare attenzione quando si rimuove o si monta il telaio da ricamo durante la cucitura.';

  @override
  String get t_trouble31 => '[Rottura dell\'ago]';

  @override
  String get t_trouble31_01 => '*Causa 1\nL\'ago è installato in modo errato.\n\n*Soluzione\nReinstallare l\'ago in modo corretto.';

  @override
  String get t_trouble31_02 => '*Causa 2\nLa vite morsetto ago non è serrata.\n\n*Soluzione\nSerrare la vite morsetto ago.';

  @override
  String get t_trouble31_03 => '*Causa 3\nL\'ago è girato o piegato.\n\n*Soluzione\nSostituire l\'ago.';

  @override
  String get t_trouble31_04 => '*Causa 4\nUso di ago o filo non appropriato per il tessuto selezionato.\n\n*Soluzione\nVerificare la tabella delle \"combinazioni Tessuto, filo e ago\".';

  @override
  String get t_trouble31_05 => '*Causa 5\nÈ stato utilizzato un piedino premistoffa errato.\n\n*Soluzione\nCollegare il piedino premistoffa consigliato.';

  @override
  String get t_trouble31_06 => '*Causa 6\nLa tensione del filo superiore è eccessiva.\n\n*Soluzione\nRegolare l\'impostazione della tensione del filo.';

  @override
  String get t_trouble31_07 => '*Causa 7\nViene tirato il tessuto durante la cucitura.\n\n*Soluzione\nNon tirare il tessuto durante la cucitura.';

  @override
  String get t_trouble31_08 => '*Causa 8\nIl fermo per rocchetto è installato in modo errato.\n\n*Soluzione\nControllare la procedura per il montaggio del fermo per rocchetto, quindi ricollegare il fermo.';

  @override
  String get t_trouble31_09 => '*Causa 9\nVi sono graffi attorno ai fori sulla piastra per ago.\n\n*Soluzione\nSostituire la piastra per ago o contattare il proprio rivenditore locale.';

  @override
  String get t_trouble31_10 => '*Causa 10\nVi sono graffi attorno al foro(i) del piedino premistoffa.\n\n*Soluzione\nSostituire il piedino premistoffa o contattare il proprio rivenditore locale.';

  @override
  String get t_trouble31_11 => '*Causa 11\nVi sono graffi nella guida del crochet.\n\n*Soluzione\nSostituire la guida del crochet o contattare il proprio rivenditore locale.';

  @override
  String get t_trouble31_12 => '*Causa 12\nL\'ago è difettoso.\n\n*Soluzione\nSostituire l\'ago.';

  @override
  String get t_trouble32 => '[Il tessuto non viene alimentato nella macchina]';

  @override
  String get t_trouble32_01 => '*Causa 1\nLa griffa di posizione è impostata in posizione abbassata.\n\n*Soluzione\nPremere il tasto di modalità a moto libero e ruotare il volantino manuale per sollevare la griffa di trasporto.';

  @override
  String get t_trouble32_02 => '*Causa 2\nI punti sono troppo vicini tra loro.\n\n*Soluzione\nAumentare la lunghezza del punto.';

  @override
  String get t_trouble32_03 => '*Causa 3\nÈ stato utilizzato un piedino premistoffa errato.\n\n*Soluzione\nCollegare il piedino premistoffa corretto.';

  @override
  String get t_trouble32_04 => '*Causa 4\nL\'ago è girato, piegato o con punta smussata.\n\n*Soluzione\nSostituire l\'ago.';

  @override
  String get t_trouble32_05 => '*Causa 5\nIl filo è attorcigliato.\n\n*Soluzione\nTagliare il filo ingarbugliato e rimuoverlo dalla guida del crochet.';

  @override
  String get t_trouble33 => '[La macchina non funziona]';

  @override
  String get t_trouble33_01 => '*Causa 1\nNon vi sono disegni selezionati\n\n*Soluzione\nSelezionare un disegno.';

  @override
  String get t_trouble33_02 => '*Causa 2\nNon è stato premuto il pulsante \"Avvio/stop\".\n\n*Soluzione\nPremere il pulsante \"Avvio/stop\".';

  @override
  String get t_trouble33_03 => '*Causa 3\nL\'interruttore generale non è acceso.\n\n*Soluzione\nAccendere l\'interruttore di alimentazione principale.';

  @override
  String get t_trouble33_04 => '*Causa 4\nIl piedino premistoffa non è abbassato.\n\n*Soluzione\nAbbassare il piedino premistoffa.';

  @override
  String get t_trouble33_05 => '*Causa 5\nSi è premuto il pulsante \"Avvio/stop\" con il pedale reostato collegato.\n\n*Soluzione\nRimuovere il pedale reostato, o utilizzare il pedale reostato per azionare la macchina.';

  @override
  String get t_trouble33_06 => '*Causa 6\nLa leva di controllo velocità è impostata per il controllo della larghezza del punto a zig-zag.\n\n*Soluzione\nUtilizzare il pedale reostato per azionare la macchina al posto del pulsante \"Avvio/stop\".';

  @override
  String get t_trouble34 => '[L\'unità da ricamo non funziona]';

  @override
  String get t_trouble34_01 => '*Causa 1\nNon vi sono disegni selezionati.\n\n*Soluzione\nSelezionare un disegno.';

  @override
  String get t_trouble34_02 => '*Causa 2\nL\'interruttore generale non è acceso.\n\n*Soluzione\nAccendere l\'interruttore di alimentazione principale.';

  @override
  String get t_trouble34_03 => '*Causa 3\nL\'unità da ricamo non è montata in modo corretto.\n\n*Soluzione\nMontare correttamente l\'unità da ricamo.';

  @override
  String get t_trouble34_04 => '*Causa 4\nIl telaio da ricamo è stato montato prima dell\'inizializzazione dell\'unità.\n\n*Soluzione\nEseguire in modo corretto la procedura di inizializzazione.';

  @override
  String get t_trouble35 => ' [Il filo risulta aggrovigliato sul rovescio del tessuto]';

  @override
  String get t_trouble35_01 => ' *Causa 1\nIl contrasto del display non è regolato correttamente.\n\n*Soluzione\nRegolare il contrasto del display LCD.';

  @override
  String get t_maintenance36 => '[Pulizia della guida del crochet e del crochet]';

  @override
  String get t_maintenance36_00 => 'Se si accumula della polvere nella guida del crochet o nel crochet, la macchina non funzionerà in modo appropriato e non risulterà operativa la funzione di rilevamento del filo della spolina. Per risultati ottimali, mantenere pulita la macchina.\nPrima di spegnere la macchina, leggere i passaggi descritti di seguito.';

  @override
  String get t_maintenance36_01 => '\n1. Premere il pulsante \"Posizione ago\" per sollevare l\'ago.';

  @override
  String get t_maintenance36_02 => '2. Abbassare il piedino premistoffa.';

  @override
  String get t_maintenance36_03 => '(a) SPENTO\n(b) ACCESO\n\n3. Spegnere l\'interruttore generale della macchina.';

  @override
  String get t_maintenance36_04 => '4. Rimuovere l\'ago e il supporto del piedino premistoffa.';

  @override
  String get t_maintenance36_05_11 => '5. Rimuovere la prolunga base piana o l\'unità per ricamare eventualmente collegate.\nFar scorrere la leva placca ago verso di sé.\nLa placca ago si apre.';

  @override
  String get t_maintenance36_05_12 => '(a) Far scorrere verso di sé.\n';

  @override
  String get t_maintenance36_05_13 => '6. Con la mano destra, far scorrere verso l\'esterno la placca ago per rimuoverla.';

  @override
  String get t_maintenance36_05_14 => '(a) Coperchio per piastra per ago\n';

  @override
  String get t_maintenance36_05_15 => '\n7. Afferrare il cestello della spolina ed estrarlo sollevandolo delicatamente.';

  @override
  String get t_maintenance36_07_02 => '(a) Crochet';

  @override
  String get t_maintenance36_08 => '\n8. Utilizzare lo spazzolino di pulizia o un aspirapolvere per rimuovere eventuali filamenti o polvere dalla guida del crochet e dall\'area circostante.\n\n* Non oliare il crochet.';

  @override
  String get t_maintenance36_08_02 => '(a) Spazzolino di pulizia\n(b) Guida del crochet';

  @override
  String get t_embbasic18_04_21 => '\n9. Inserire il cestello della spolina in modo da allineare il segno ▲ sul cestello della spolina con il segno ● sulla macchina.';

  @override
  String get t_embbasic18_04_22 => '(a) Segno ▲ sul cestello della spolina \n(b) Segno ● sulla macchina';

  @override
  String get t_embbasic18_04_23 => '\n10. Inserire le linguette della placca ago nelle fessure della macchina.';

  @override
  String get t_embbasic18_04_24 => '(a) Linguette\n(b) Fessure';

  @override
  String get t_embbasic18_04_25 => '11. Premere sul lato destro della placca ago per fissarla.';

  @override
  String get t_sewing01_00 => 'Selezione del tipo di cucitura';

  @override
  String get t_sewing01_00_01 => '1-01:Cucitura normale\n1-05:Cucitura rinforzata\n1-06:Cucitura su tessuti elastici';

  @override
  String get t_sewing01_00_01_s_normal => 'Cucitura normale';

  @override
  String get t_sewing01_00_01_s_reinforced => 'Cucitura rinforzata';

  @override
  String get t_sewing01_00_01_s_stretch => 'Cucitura su tessuti elastici';

  @override
  String get t_sewing01 => '[Punti diritti]';

  @override
  String get t_sewing01_01 => '\n1. Collegare il piedino premistoffa \"J\".\nTenere le estremità del filo e del tessuto con la mano sinistra e ruotare il volantino con la mano destra per inserire l\'ago nel tessuto.';

  @override
  String get t_sewing01_01_02 => '(a) Posizione iniziale di cucitura';

  @override
  String get t_sewing01_02 => '\n2. Abbassare il piedino premistoffa e tenere premuto il pulsante \"Punti di fermatura/rinforzo\" per cucire 3-4 punti di fermatura. La macchina esegue dei punti di fermatura (o di rinforzo) e quindi si ferma.\nPremere il pulsante \"Avvio/stop\" per cucire in avanti. La macchina inizierà a cucire a velocità ridotta.';

  @override
  String get t_sewing01_03 => '(a) Punti di fermatura';

  @override
  String get t_sewing01_04 => '3. Al termine della cucitura, tenere premuto il pulsante \"Punti di fermatura/rinforzo\" per cucire 3-4 punti di fermatura o di rinforzo alla fine della cucitura.';

  @override
  String get t_sewing01_05 => '4. Al termine, premere il pulsante \"Taglio del filo\" per tagliare i fili.\n\n* Se sul display sono stati selezionati il tasto dei punti automatici e il tasto di cucitura automatica dei punti di fermatura/rinforzo, verranno automaticamente cuciti dei punti di fermatura o di rinforzo all\'inizio della cucitura quando si preme il pulsante \"Avvio/stop\".\nPremere il pulsante \"Punti di fermatura/rinforzo\" per eseguire dei punti di fermatura (o di rinforzo) e per tagliare automaticamente il filo al termine della cucitura.';

  @override
  String get t_sewing01_06 => '\n * Modifica della posizione dell\'ago \n Quando si selezionano punti con ago in posizione sinistra o centrale, è possibile utilizzare i tasti “+” e “-” nella schermata dello spostamento S/D per modificare la posizione dell\'ago. Far corrispondere la distanza dal bordo destro del piedino premistoffa all\'ago più il margine di cucitura, quindi allineare il bordo del piedino premistoffa al bordo del tessuto durante la cucitura per ottenere una finitura ottimale.';

  @override
  String get t_sewing01_07 => '(a) Margine di cucitura';

  @override
  String get t_sewing02 => '[Sopraggitto]';

  @override
  String get t_sewing02_00 => 'Selezione del tipo di cucitura';

  @override
  String get t_sewing02_00_01 => '1-16:Tessuti leggeri e medi\n1-17:Tessuti spessi\n1-18:Tessuti medi, pesanti e facilmente sfilacciabili\n1-19:Tessuti elastici\n1-20:Tessuti elastici spessi e medi\n1-21:Tessuti elastici';

  @override
  String get t_sewing02_00_01_f_lightandmedium => 'Tessuti leggeri e medi';

  @override
  String get t_sewing02_00_01_f_heavy => 'Tessuti spessi';

  @override
  String get t_sewing02_00_01_f_mediumstretch => 'Tessuti medi, pesanti e facilmente sfilacciabili';

  @override
  String get t_sewing02_00_01_f_stretch1 => 'Tessuti elastici';

  @override
  String get t_sewing02_00_01_f_thickandmediumstretch => 'Tessuti elastici spessi e medi';

  @override
  String get t_sewing02_00_01_f_stretch2 => 'Tessuti elastici';

  @override
  String get t_sewing02_01 => '1. Collegare il piedino premistoffa \"G\". Abbassare il piedino premistoffa in modo che la relativa guida si trovi a livello contro il bordo del tessuto.';

  @override
  String get t_sewing02_02 => '\n2. Cucire lungo la guida del piedino premistoffa.';

  @override
  String get t_sewing02_02_02 => '(a) Guida';

  @override
  String get t_sewing02_03 => '\n1. Collegare il piedino premistoffa \"J\". Cucire con l\'ago che scende leggermente oltre il bordo del tessuto.';

  @override
  String get t_sewing02_04 => '(a) Posizione di discesa dell\'ago';

  @override
  String get t_sewing02_05 => '\n*Se è stata regolata la larghezza del punto, ruotare verso di sé il volantino con la mano verificando che l\'ago non vada a toccare il piedino premistoffa. Se l\'ago colpisce il piedino premistoffa, l\'ago potrebbe rompersi e causare lesioni personali.';

  @override
  String get t_sewing02_05_02 => '(a) L\'ago non deve toccare';

  @override
  String get t_sewing04 => '[Punti smerlo]';

  @override
  String get t_sewing04_01 => 'Questo punto pieno a forma d\'onda viene chiamato punto smerlo. Utilizzare questo punto per decorare i bordi di colletti e fazzoletti o utilizzarlo per accentuare un orlo.\nPer tessuti leggeri, potrebbe rendersi necessario l\'uso di un adesivo spray temporaneo. Eseguire una cucitura di prova sul tessuto prima di cucire un progetto.';

  @override
  String get t_sewing04_02 => '1. Collegare il piedino premistoffa \"N+\". Eseguire dei punti a smerlo lungo il bordo del tessuto. Non cucire direttamente sul bordo del tessuto.';

  @override
  String get t_sewing04_03 => '2. Tagliare lungo la cucitura, assicurandosi di non recidere i punti.';

  @override
  String get t_sewing05_00 => 'Selezione del tipo di cucitura';

  @override
  String get t_sewing05_00_01 => '4-01:Tessuti sottili e medi (per fori orizzontali) \n4-07:Tessuti sottili o medi\n4-10:Tessuti elastici con ordito grezzo\n4-11:Tessuti elastici\n4-13:Abiti, soprabiti\n4-14:Jeans, pantaloni\n4-15:Giacche pesanti';

  @override
  String get t_sewing05_00_01_f_lighttomediumhorizhole => 'Tessuti sottili e medi (per fori orizzontali) ';

  @override
  String get t_sewing05_00_01_f_lighttomedium => 'Tessuti sottili o medi';

  @override
  String get t_sewing05_00_01_f_stretchweaves => 'Tessuti elastici con ordito grezzo';

  @override
  String get t_sewing05_00_01_f_stretch => 'Tessuti elastici';

  @override
  String get t_sewing05_00_01_f_suitsandovercoat => 'Abiti, soprabiti';

  @override
  String get t_sewing05_00_01_f_jeansandtrousers => 'Jeans, pantaloni';

  @override
  String get t_sewing05_00_01_f_thickcoats => 'Giacche pesanti';

  @override
  String get t_sewing05 => '[Asole]';

  @override
  String get t_sewing05_02 => '1. Segnare la posizione e la lunghezza delle asole sul tessuto.';

  @override
  String get t_sewing05_03 => '\n2. Collegare il piedino per asole \"A+\". Estrarre la piastra di supporto bottone sul piedino premistoffa e inserire il bottone che verrà passato attraverso l\'asola. Quindi serrare la piastra di supporto attorno al bottone.\n\n* Le dimensioni dell\'asola sono stabilite dalle dimensioni del bottone nel supporto che tiene il bottone.';

  @override
  String get t_sewing05_04 => '(a) Piastra di supporto bottone';

  @override
  String get t_sewing05_05 => '\n3. Allineare il piedino premistoffa al segno sul tessuto e abbassare la leva del piedino premistoffa.';

  @override
  String get t_sewing05_06 => '(a) Segno sul tessuto\n(b) Segno sul piedino premistoffa';

  @override
  String get t_sewing05_07 => '\n4. Abbassare la leva dell\'asola posizionandola dietro la staffa metallica dell\'apposito piedino.';

  @override
  String get t_sewing05_08 => '(a) Staffa metallica';

  @override
  String get t_sewing05_09 => '4. Tenere delicatamente l\'estremità del filo superiore e iniziare a cucire. Far avanzare con attenzione il tessuto con la mano durante la cucitura dell\'asola.\nDopo aver completato la cucitura, verranno automaticamente cuciti dei punti di rinforzo e la macchina si fermerà.';

  @override
  String get t_sewing05_10 => '\n5. Puntare uno spillo all\'interno di una delle travette e quindi inserire il rifilatore al centro dell\'asola e tagliare verso lo spillo.';

  @override
  String get t_sewing05_11 => '(a) Spillo di imbastitura\n(b) Taglia asole';

  @override
  String get t_sewing05_12 => '\n[Per asole a occhiello]\nPraticare un foro nell\'estremità a fessura dell\'asola con un punzone per occhielli. Quindi, puntare uno spillo lungo l\'interno di una delle travette, inserire un taglia asole nel foro praticato con il punzone e tagliare in direzione dello spillo.';

  @override
  String get t_sewing05_13 => '(a) Punzone per occhielli\n(b) Spillo di imbastitura';

  @override
  String get t_sewing05_14 => '*Cucitura su tessuti elastici\nQuando si eseguono cuciture su tessuti elastici con 4-10 o 4-11, cucire i punti per asola su un cordoncino.';

  @override
  String get t_sewing05_16 => '\n1. Agganciare il cordoncino sulla parte posteriore del piedino premistoffa \"A+\". Inserire le estremità nelle scanalature della parte anteriore del piedino e quindi legarle temporaneamente in tale posizione.';

  @override
  String get t_sewing05_17 => '(a) Filo superiore';

  @override
  String get t_sewing05_18 => '2. Abbassare il piedino premistoffa e iniziare a cucire.';

  @override
  String get t_sewing05_19 => '3. Al termine della cucitura, tirare delicatamente il cordoncino per rimuovere eventuali allentamenti e quindi tagliare la parte in eccesso.';

  @override
  String get t_sewing05_20 => '\n*Bottoni di forma particolare o bottoni che non entrano nella piastra di supporto\nUtilizzare i segni sul righello del piedino permistoffa per impostare le dimensioni dell\'asola. Un segno sul righello del piedino equivale a 5 mm (circa 3/16 in.) . Aggiungere il diametro del bottone allo spessore e quindi impostare la piastra al valore calcolato.';

  @override
  String get t_sewing05_21 => '(a) Righello del piedino\n(b) Piastra di supporto bottone\n(c) Somma completata di diametro + spessore\n(d) 5 mm (circa 3/16 in.) ';

  @override
  String get t_sewing05_22 => '\nEsempio:\nPer un bottone con diametro di 15 mm (circa 9/16 in.) e spessore di 10 mm (circa 3/8 in.) , il righello deve essere impostato su 25 mm (circa 1 in.) .';

  @override
  String get t_sewing05_23 => '(a) 10 mm (circa 3/8 in.) \n(b) 15 mm (circa 9/16 in.) ';

  @override
  String get t_sewing06 => '[Cucitura bottoni]';

  @override
  String get t_sewing06_01 => 'Non utilizzare la funzione di taglio automatico del filo quando si eseguono cuciture di bottoni. In caso contrario, si perderanno i capi del filo. \n\n1. Montare il piedino di posizionamento bottone \"M\", far scorrere il bottone lungo la piastra metallica e nel piedino e quindi abbassare la leva del piedino premistoffa.';

  @override
  String get t_sewing06_01_02 => '(a) Bottone\n(b) Piastra metallica\n';

  @override
  String get t_sewing06_02 => '2. Ruotare il volantino manuale per verificare che l\'ago scenda in corrispondenza di ciascun foro nel modo corretto. Quindi, tenere delicatamente l\'estremità del filo superiore e iniziare a cucire. Al termine della cucitura, la macchina si fermerà automaticamente.';

  @override
  String get t_sewing06_03 => '3. Dal rovescio del tessuto, tirare il capo del filo della spolina per portare il filo superiore sul rovescio del tessuto. Annodare quindi i due capi e tagliare i fili all\'inizio della cucitura.';

  @override
  String get t_sewing06_04 => '\n*Cucitura di bottoni a quattro fori';

  @override
  String get t_sewing06_05 => 'Cucire i due fori più vicini a sé. Terminata questa operazione, sollevare il piedino premistoffa, spostare il tessuto in modo che l\'ago si sistemi in corrispondenza dei due fori successivi e quindi ripetere la cucitura.';

  @override
  String get t_sewing06_06 => '*Cucitura di un bottone con gambo\n\n1. Tirare la leva per gambo verso di sé prima di cucire.';

  @override
  String get t_sewing06_07 => '(a) Leva per gambo\n';

  @override
  String get t_sewing06_08 => '2. Tenere i due capi del filo superiore che si trovano tra il bottone e il tessuto, avvolgerli intorno al gambo e quindi annodarli saldamente.\nAnnodare i capi del filo della spolina dall\'inizio alla fine della cucitura sul rovescio della cucitura.';

  @override
  String get t_sewing07 => '[Travette]';

  @override
  String get t_sewing07_01 => 'Utilizzare travette per rinforzare le aree soggette a sollecitazione, ad esempio gli angoli delle tasche.';

  @override
  String get t_sewing07_02 => '\n1. Montare il piedino per asole \"A+\" e impostare il righello sulla lunghezza della travetta da cucire.';

  @override
  String get t_sewing07_03 => '(a) Righello del piedino\n(b) Misura completata della lunghezza\n(c) 5 mm (circa 3/16 in.) ';

  @override
  String get t_sewing07_04 => '2. Impostare il tessuto in modo che la tasca si sposti verso di sé durante la cucitura.';

  @override
  String get t_sewing07_05 => '\n3. Verificare il primo punto di discesa dell\'ago e abbassare il piedino premistoffa.';

  @override
  String get t_sewing07_06 => '(a) 2 mm (circa 1/16 in.) ';

  @override
  String get t_sewing07_09 => '4. Tenere delicatamente l\'estremità del filo superiore e iniziare a cucire. Dopo aver completato la cucitura, verranno automaticamente cuciti dei punti di rinforzo e la macchina si fermerà.';

  @override
  String get t_sewing07_10 => '\n*Travette su tessuti spessi\nPorre sul retro del tessuto un\'altra pezza o del cartoncino di uguale spessore in modo da portare a filo il piedino premistoffa e procedere più agevolmente.';

  @override
  String get t_sewing07_11 => '(a) Piedino premistoffa\n(b) Cartoncino\n(c) Tessuto';

  @override
  String get t_sewing08 => '[Cucitura di cerniere]';

  @override
  String get t_sewing08_00 => '\n*Cerniera centrata\nUtilizzare per borse e per applicazioni simili.\n\n1. Montare il piedino premistoffa \"J\" ed eseguire dei punti diritti fino all\'apertura della cerniera. Passare a un punto di imbastitura e cucire fino alla parte superiore del tessuto.';

  @override
  String get t_sewing08_02 => '(a) Imbastitura\n(b) Punti di fermatura\n(c) Estremità dell\'apertura della cerniera\n(d) Rovescio';

  @override
  String get t_sewing08_03 => '\n2. Premere la giunzione di cucitura e applicare la cerniera con un punto di imbastitura al centro di ciascun lato del nastro della cerniera.';

  @override
  String get t_sewing08_04 => '(a) Imbastitura\n(b) Cerniera\n(c) Rovescio';

  @override
  String get t_sewing08_05 => '\n3. Rimuovere il piedino premistoffa \"J\". Allineare il lato destro del perno del piedino per cerniere \"I\" al supporto per piedino premistoffa e collegare il piedino per cerniere.';

  @override
  String get t_sewing08_06 => '(a) Destra\n(b) Sinistra\n(c) Posizione di discesa dell\'ago';

  @override
  String get t_sewing08_07 => '4. Cucire da 7 a 10 mm (circa 1/4 a 3/8 in.) dal bordo unito del tessuto, quindi rimuovere l\'imbastitura.';

  @override
  String get t_sewing08_08 => '\n*Cucitura di una cerniera laterale\nUtilizzare per cucire cerniere sui lati di gonne o abiti.\n\n1. Montare il piedino premistoffa \"J\" ed eseguire dei punti diritti fino all\'apertura della cerniera. Passare a un punto di imbastitura e cucire fino alla parte superiore del tessuto.';

  @override
  String get t_sewing08_11 => '(a) Punti di fermatura\n(b) Rovescio del tessuto\n(c) Punti di imbastitura\n(d) Estremità dell\'apertura della cerniera';

  @override
  String get t_sewing08_12 => '\n2. Premere per aprire la giunzione di cucitura e allineare l\'orlo piegato lungo I dentini della cerniera, mantenendo 3 mm (circa 1/8 in.) per lo spazio di cucitura.';

  @override
  String get t_sewing08_13 => '(a) Linguetta della cerniera\n(b) Rovescio del tessuto\n(c) Dentini della cerniera\n(d) Estremità dell\'apertura della cerniera\n(e) 3 mm (circa 1/8 in.) ';

  @override
  String get t_sewing08_14 => '\n3. Rimuovere il piedino premistoffa \"J\". Allineare il lato destro del perno del piedino per cerniere \"I\" al supporto per piedino premistoffa e montare il piedino premistoffa.';

  @override
  String get t_sewing08_15 => '(a) Destra\n(b) Sinistra\n(c) Posizione di discesa dell\'ago';

  @override
  String get t_sewing08_16 => '\n4. Impostare il piedino sul margine di 3 mm (1/8 in.) . Iniziare dall\'estremità dell\'apertura della cerniera. Cucire fino a circa 50 mm (circa 2 in.) dal bordo del tessuto, quindi fermare la macchina. Tirare verso il basso la linguetta della cerniera, quindi continuare a cucire fino al bordo del tessuto.';

  @override
  String get t_sewing08_17 => '(a) 50 mm (circa 2 in.) \n(b) 3 mm (circa 1/8 in.) ';

  @override
  String get t_sewing08_18 => '\n5. Chiudere la cerniera, ribaltare il tessuto e cucire un punto di imbastitura.';

  @override
  String get t_sewing08_19 => '(a) Parte anteriore della gonna (rovescio del tessuto) \n(b) Punti di imbastitura\n(c) Parte anteriore della gonna (diritto del tessuto) \n(d) Parte posteriore della gonna (diritto del tessuto) ';

  @override
  String get t_sewing08_20 => '\n6. Rimuovere il piedino e rimontarlo in modo che il lato sinistro del perno sia collegato al supporto del piedino premistoffa.\n\n* Quando si cuce il lato sinistro della cerniera, l\'ago dovrà scendere sul lato destro del piedino premistoffa. Quando si cuce il lato destro della cerniera, l\'ago dovrà scendere sul lato sinistro del piedino premistoffa.';

  @override
  String get t_sewing08_21 => '(a) Destra\n(b) Sinistra\n(c) Posizione di discesa dell\'ago';

  @override
  String get t_sewing08_22 => '\n7. Collocare il tessuto in modo che il bordo sinistro del piedino premistoffa tocchi il bordo dei dentini della cerniera. Cucire dei punti di fermatura nella parte superiore della cerniera, quindi continuare a cucire. Interrompere la cucitura a circa 50 mm (circa 2 in.) dal bordo del tessuto, lasciare l\'ago nel tessuto e rimuovere i punti di imbastitura. Aprire la cerniera e cucire il resto della giunzione.';

  @override
  String get t_sewing08_23 => '(a) Imbastitura\n(b) Da 7 a 10 mm (circa 1/4 a 3/8 in.) \n(c) Punti di fermatura\n(d) 50 mm (circa 2 in.) ';

  @override
  String get t_sewing09_00 => 'Selezione del tipo di cucitura';

  @override
  String get t_sewing09_00_01 => 'Selezionare uno di questi punti per cucire orli o manicotti di vestiti, bluse, pantaloni o gonne.';

  @override
  String get t_sewing09_00_02 => '2-01:Altri tessuti\n2-02:Tessuti elastici';

  @override
  String get t_sewing09_00_02_f_other => 'Altri tessuti';

  @override
  String get t_sewing09_00_02_f_stretch => 'Tessuti elastici';

  @override
  String get t_sewing09 => '[Punti invisibili per orlo]';

  @override
  String get t_sewing09_01 => '\n1. Posizionare il tessuto con il lato rovescio rivolto verso l\'alto, quindi piegare e imbastire il tessuto.';

  @override
  String get t_sewing09_02 => '(a) 5 mm (circa 3/16 in.) \n(b) Punti di imbastitura\n(c) Rovescio del tessuto';

  @override
  String get t_sewing09_03 => '\n2. Montare l\'apposito piedino per orlo invisibile \"R\" e abbassare la leva del piedino. Posizionare il tessuto in modo che il bordo piegato tocchi la guida del piedino premistoffa.';

  @override
  String get t_sewing09_04 => '(a) Guida\n(b) Piega';

  @override
  String get t_sewing09_05 => '\n3. Cucire il tessuto mantenendo il bordo piegato a contatto con il piedino premistoffa.';

  @override
  String get t_sewing09_06 => '(a) Posizione ago';

  @override
  String get t_sewing09_07 => '\n4. Rimuovere i punti di imbastitura e girare il tessuto.';

  @override
  String get t_sewing09_08 => '(a) Rovescio del tessuto\n(b) Diritto del tessuto';

  @override
  String get t_sewing10 => '[Applicazioni]';

  @override
  String get t_sewing10_01 => '\n1. Utilizzare della colla per tessuto, un adesivo spray temporaneo o un punto di imbastitura per attaccare l\'applicazione al tessuto. In questo modo il tessuto non si muoverà durante la cucitura.';

  @override
  String get t_sewing10_02 => '(a) Applicazione\n(b) Colla per tessuto';

  @override
  String get t_sewing10_03 => '\n2. Collegare il piedino premistoffa \"J\". Verificare che l\'ago scenda appena oltre il bordo dell\'applicazione, quindi iniziare a cucire.';

  @override
  String get t_sewing10_04 => '(a) Materiale applicazione\n(b) Posizione di discesa dell\'ago ';

  @override
  String get t_sewing10_06 => '*Cucitura di curve acute\n';

  @override
  String get t_sewing10_07 => 'Arrestare la macchina con l\'ago nel tessuto all\'esterno dell\'applicazione. Sollevare il piedino premistoffa e ruotare il tessuto poco alla volta durante la cucitura in modo da ottenere una finitura ottimale.';

  @override
  String get t_sewing11 => '[Nervature]';

  @override
  String get t_sewing11_01 => '\n1. Fare un segno lungo le pieghe sul rovescio del tessuto.';

  @override
  String get t_sewing11_01_02 => '(a) Rovescio';

  @override
  String get t_sewing11_02 => '\n2. Ribaltare il tessuto e stirare solo le parti piegate.';

  @override
  String get t_sewing11_02_02 => '(a) Superficie';

  @override
  String get t_sewing11_03 => '\n3. Collegare il piedino premistoffa \"I\".\nCucire un punto diritto lungo la piega.';

  @override
  String get t_sewing11_04_02 => '(a) Larghezza per nervature\n(b) Rovescio\n(c) Superficie';

  @override
  String get t_sewing11_05 => '4. Stirare le pieghe nella stessa direzione.';

  @override
  String get t_sewing12 => '[Arricciature]';

  @override
  String get t_sewing12_00 => '\nUtilizzare sul punto vita di gonne, maniche di camicia, ecc.';

  @override
  String get t_sewing12_01 => '\n1. Estrarre la spolina e i fili superiori di circa 50 mm (circa 1-15/16 in.) .';

  @override
  String get t_sewing12_01_02 => '(a) Filo superiore\n(b) Filo della spolina\n(c) Circa 50 mm (circa 1-15/16 in.) ';

  @override
  String get t_sewing12_02 => '\n2. Cucire due file di punti diritti paralleli alla linea della cucitura e quindi tagliare il filo in eccesso lasciando 50 mm (circa 1-15/16 in.) .';

  @override
  String get t_sewing12_02_02 => '(a) Linea della cucitura\n(b) Da 10 a 15 mm (circa 3/8 in. a 9/16 in.) \n(c) Circa 50 mm (circa 1-15/16 in.) ';

  @override
  String get t_sewing12_03 => '3. Tirare i fii delle spoline in modo da ottenere la quantità desiderata di arricciature, quindi annodare i fili.';

  @override
  String get t_sewing12_04 => '4. Stirare le arricciature.\n';

  @override
  String get t_sewing12_05 => '5. Cucire la linea della cucitura e rimuovere il punto di imbastitura.';

  @override
  String get t_sewing13 => '[Pinces]';

  @override
  String get t_sewing13_01 => '\n1. Cucire un punto di fermatura all\'inizio della pince e quindi cucire dall\'estremità più larga all\'altra estremità senza estendere il tessuto.';

  @override
  String get t_sewing13_01_02 => '(a) Imbastitura\n(b) Superficie\n(c) Rovescio';

  @override
  String get t_sewing13_02 => '2. Tagliare l\'estremità del filo lasciando 50 mm (circa 1-15/16 in.) e quindi annodare insieme le due estremità.\n\n* Non cucire un punto di fermatura alla fine.';

  @override
  String get t_sewing13_03 => '3. Inserire le estremità del filo nella pince con un ago per cucitura manuale.';

  @override
  String get t_sewing13_04 => '4. Stirare la pince da un lato in modo che si appiattisca.';

  @override
  String get t_sewing14 => '[Orlo piatto]';

  @override
  String get t_sewing14_00 => '\nUtilizzare per rinforzare le cuciture e rifinire accuratamente i bordi.\n\n1. Cucire la linea di finitura, quindi tagliare metà della giuntura consentita dal lato in cui si troverà l\'orlo piatto.';

  @override
  String get t_sewing14_01_02 => '(a) Rovescio\n(b) Circa 12 mm (circa 1/2 in.) ';

  @override
  String get t_sewing14_02 => '\n2. Spiegare il tessuto lungo la linea di finitura.';

  @override
  String get t_sewing14_02_02 => '(a) Rovescio\n(b) Linea di finitura';

  @override
  String get t_sewing14_03 => '\n3. Porre entrambe le giunture consentite sul lato della giuntura più corta (giuntura tagliata) e stirarle.';

  @override
  String get t_sewing14_03_02 => '(a) Rovescio';

  @override
  String get t_sewing14_04 => '\n4. Piegare la giuntura consentita più lunga attorno a quella più corta, quindi cucire il bordo della piega.';

  @override
  String get t_sewing14_04_01 => '(a) Rovescio';

  @override
  String get t_sewing15_00 => 'Tipo di cucitura selezionato';

  @override
  String get t_sewing15_00_01 => 'Q-01: Punto rammendo (centrale)\nQ-02: Punto rammendo (destra)\nQ-03: Punto rammendo (sinistra)';

  @override
  String get t_sewing15_00_01_s_piecingmiddle => 'Punto rammendo (centrale)';

  @override
  String get t_sewing15_00_01_s_piecingright => 'Punto rammendo (destra)';

  @override
  String get t_sewing15_00_01_s_piecingleft => 'Punto rammendo (sinistra)';

  @override
  String get t_sewing15 => '[Rammendo]';

  @override
  String get t_sewing15_01 => '(a) Margine di cucitura: 6,5 mm (circa 1/4 in)\n       (quando Q-02 è selezionato)\n(b) Allineare con il lato destro del piedino premistoffa.\n\n1. Collegare il piedino premistoffa \"J\".\nCucire con il bordo del tessuto allineato con il lato del piedino premistoffa.';

  @override
  String get t_sewing15_012 => '(a) Margine di cucitura: 7 mm\n       (quando Q-02 è selezionato) \n(b) Allineare con il lato destro del piedino premistoffa.\n\n1. Collegare il piedino premistoffa \"J\".\nCucire con il bordo del tessuto allineato con il lato del piedino premistoffa.';

  @override
  String get t_sewing15_01_02 => '(a) Margine di cucitura: 6,5 mm (circa 1/4 in)\n       (quando Q-03 è selezionato)\n(b) Allineare con il lato sinistro del piedino premistoffa.\n\n1. Collegare il piedino premistoffa \"J\".\nCucire con il bordo del tessuto allineato con il lato del piedino premistoffa.';

  @override
  String get t_sewing15_01_022 => '(a) Margine di cucitura: 7 mm\n       (quando Q-03 è selezionato)\n(b) Allineare con il lato sinistro del piedino premistoffa.\n\n1. Collegare il piedino premistoffa \"J\".\nCucire con il bordo del tessuto allineato con il lato del piedino premistoffa.';

  @override
  String get t_sewing15_02 => '(a) Guida\n\nIl piedino per trapuntatura da 1/4\" dotato di guida può cucire un margine di cucitura preciso di 1/4 in o 1/8 in. Può essere utilizzato per rammendare una trapunta o per eseguire cuciture superiori.\n\n1. Selezionare Q-01, quindi collegare il piedino per trapuntatura da 1/4\" dotato di guida.';

  @override
  String get t_sewing15_03 => '(a) Guida\n(b) 1/4 in\n\n2. Utilizzare la guida sul piedino premistoffa e i segni per eseguire margini di cucitura precisi.\n\n\"Rammendo di un margine di cucitura di 1/4 in\"\nCucire tenendo il bordo dei tessuti contro la guida.';

  @override
  String get t_sewing15_04 => '(a) Allineare questo segno con il bordo del tessuto per iniziare\n(b) Inizio della cucitura\n(c) Fine della cucitura\n(d) Bordo opposto del tessuto alla fine o alla rotazione sugli angoli\n(e) 1/4 in\n\n\"Creazione di un margine di cucitura preciso\"\nUtilizzare il segno sul piedino per iniziare, finire o eseguire la rotazione sugli angoli di 1/4 in dal bordo del tessuto.';

  @override
  String get t_sewing15_05 => '(a) Superficie del tessuto\n(b) Cucitura\n(c) 1/8 in\n\n\"Trapuntatura con cuciture superiori, 1/8 in\"\nCucire con il bordo del tessuto allineato con il lato sinistro dell\'estremità del piedino premistoffa.';

  @override
  String get t_sewing16 => '[Trapuntatura]';

  @override
  String get t_sewing16_01 => '1. Rimuovere il piedino premistoffa e allentare la vite del supporto del piedino premistoffa per rimuovere il supporto.\n\nInserire l\'adattatore sulla barra del piedino premistoffa, allineando il lato piatto dell\'apertura dell\'adattatore al lato piatto della barra del piedino. Spingerlo verso l\'alto il più possibile e serrare bene la vite con il cacciavite.';

  @override
  String get t_sewing16_02 => '(a) Leva di funzionamento\n(b) Vite morsetto ago\n(c) Forcella\n(d) Barra del piedino premistoffa\n\n2. Impostare la leva di funzionamento del piedino doppio trasporto in modo che la vite morsetto ago sia posizionata tra la forcella, quindi inserire il piedino doppio trasporto sulla barra del piedino premistoffa.';

  @override
  String get t_sewing16_03 => '3. Abbassare la leva del piedino premistoffa. Serrare saldamente la vite con il cacciavite.';

  @override
  String get t_sewing16_04 => '4. Posizionare una mano su ciascun lato del piedino premistoffa in modo da tenere saldamente il tessuto mentre si cuce.';

  @override
  String get t_sewing16_05 => '* Se \"SISTEMA DI RILEVAMENTO AUTOMATICO DEL TESSUTO\" nella schermata delle impostazioni della macchina è impostato su \"ON\", il tessuto può essere alimentato in modo uniforme per ottenere risultati di cucitura migliori.';

  @override
  String get t_sewing17 => '[Trapuntatura a moto libero]';

  @override
  String get t_sewing17_00 => '(a) Piedino per trapuntatura a moto libero \"C\"\n(b) Piedino a punta aperta per trapuntatura a moto libero \"O\"\n\nPer la trapuntatura a moto libero, utilizzare il piedino per trapuntatura a moto libero \"C\" e il piedino a punta aperta per trapuntatura a moto libero \"O\" a seconda del tipo di disegno/punto selezionato.';

  @override
  String get t_sewing17_01 => '1. Premere il tasto della modalità a moto libero per impostare la macchina sulla modalità Cucitura a moto libero.';

  @override
  String get t_sewing17_02_01 => '(a) Vite del supporto del piedino premistoffa\n(b) Fessura\n\n2. Montare il piedino per trapuntatura a moto libero \"C\" sulla parte anteriore con la vite del supporto del piedino premistoffa allineata alla fessura nel piedino per trapuntatura.\nQuindi serrare la vite del supporto del piedino premistoffa.';

  @override
  String get t_sewing17_02_02 => '(a) Perno\n(b) Vite morsetto ago\n(c) Barra del piedino premistoffa\n\nMontare il piedino a punta aperta per trapuntatura a moto libero \"O\" posizionando il perno del piedino per trapuntatura sopra la vite morsetto ago e allineando la parte inferiore sinistra del piedino per trapuntatura e la barra del piedino.\nQuindi serrare la vite del supporto del piedino premistoffa.';

  @override
  String get t_sewing17_03 => '(a) Punto\n\n3. Utilizzare entrambe le mani per tendere il tessuto, quindi spostare il tessuto a una velocità regolare in modo da cucire punti uniformi della lunghezza di circa 2-2,5 mm (circa 1/16 - 3/32 in).\n\n* Consigliamo di collegare il pedale reostato e di cucire a una velocità uniforme.';

  @override
  String get t_sewing18 => '[Trapuntatura a eco]';

  @override
  String get t_sewing18_00 => '(a) 6,4 mm (circa 1/4 in)\n(b) 9,5 mm (circa 3/8 in)\n\nPiedino per trapuntatura a eco a moto libero \"E\".';

  @override
  String get t_sewing18_01 => '2. Rimuovere il piedino premistoffa, allentare la vite del supporto del piedino premistoffa, quindi rimuovere la vite e il supporto.\n\nInserire l\'adattatore sulla barra del piedino premistoffa, allineando il lato piatto dell\'apertura dell\'adattatore al lato piatto della barra del piedino. Spingerlo verso l\'alto il più possibile e serrare bene la vite con il cacciavite.';

  @override
  String get t_sewing18_02 => '3. Posizionare il piedino per trapuntatura a eco a moto libero \"E\" a sinistra della barra del piedino allineando i fori del piedino per trapuntatura e la barra del piedino.\n\nRuotare manualmente una vite piccola fornita per 2 o 3 volte.';

  @override
  String get t_sewing18_03 => '4. Serrare la vite.';

  @override
  String get t_sewing18_04 => '(a) 6,4 mm (circa 1/4 in)\n\n5. Utilizzando la misura indicata sul piedino per trapuntatura a eco, cucire intorno al motivo.';

  @override
  String get t_sewing18_05 => 'Lavoro finito';

  @override
  String get t_sewing19 => '[Applicazioni]';

  @override
  String get t_sewing19_01 => '(a) Margine di cucitura: da 3 a 5 mm\n\n1. Tracciare il disegno sul tessuto dell\'applicazione e ritagliare lungo il bordo.';

  @override
  String get t_sewing19_02 => '2. Ritagliare un foglio di carta spessa o lo stabilizzatore in base alle dimensioni finali dell\'applicazione, posizionarlo sul retro dell\'applicazione e piegare il margine di cucitura utilizzando un ferro da stiro.';

  @override
  String get t_sewing19_03 => '3. Girare l\'applicazione e rimuovere lo stabilizzatore o la carta. Fermare con alcuni spilli o imbastire l\'applicazione sul tessuto principale.';

  @override
  String get t_sewing19_04 => '(a) Punto di discesa dell\'ago\n\n4. Collegare il piedino premistoffa \"J\".\nControllare il punto di discesa dell\'ago, quindi cucire lungo il bordo dell\'applicazione assicurandosi che l\'ago scenda appena oltre il bordo del tessuto.';

  @override
  String get t_explain_use => '[Utilizzo]';

  @override
  String get t_explain01_01 => 'Cuciture in generale, arricciature, nervature, ecc. Il punto di fermatura viene cucito tenendo premuto il pulsante \"Punti di fermatura/rinforzo\".';

  @override
  String get t_explain01_01_01 => '\n * Modifica della posizione dell\'ago \n Quando si selezionano punti con ago in posizione sinistra o centrale, è possibile utilizzare i tasti “+” e “-” nella schermata dello spostamento S/D per modificare la posizione dell\'ago. Far corrispondere la distanza dal bordo destro del piedino premistoffa all\'ago più il margine di cucitura, quindi allineare il bordo del piedino premistoffa al bordo del tessuto durante la cucitura per ottenere una finitura ottimale.';

  @override
  String get t_explain01_02 => 'Cuciture in generale, arricciature, nervature, ecc. Il punto di rinforzo viene cucito tenendo premuto il pulsante \"Punti di fermatura/rinforzo\".';

  @override
  String get t_explain01_03 => 'Cuciture in generale, arricciature, nervature, ecc. Il punto di fermatura viene cucito tenendo premuto il pulsante \"Punti di fermatura/rinforzo\".';

  @override
  String get t_explain01_04 => 'Cuciture in generale, arricciature, nervature, ecc. Il punto di rinforzo viene cucito tenendo premuto il pulsante \"Punti di fermatura/rinforzo\".';

  @override
  String get t_explain01_05 => 'Cuciture di rinforzo in generale e cuciture decorative.';

  @override
  String get t_explain01_06 => 'Punti di rinforzo, cuciture e applicazioni decorative';

  @override
  String get t_explain01_07 => 'Punti decorativi, rinforzi.';

  @override
  String get t_explain01_08 => 'Imbastitura.';

  @override
  String get t_explain01_09 => 'Sopraggitto, rammendo. Il punto di fermatura viene cucito tenendo premuto il pulsante \"Punti di fermatura/rinforzo\".';

  @override
  String get t_explain01_10 => 'Sopraggitto, rammendo. Il punto di rinforzo viene cucito tenendo premuto il pulsante \"Punti di fermatura/rinforzo\".';

  @override
  String get t_explain01_11 => 'Inizio con posizione destra dell\'ago, cucitura a zigzag a sinistra.';

  @override
  String get t_explain01_12 => 'Inizio con posizione sinistra dell\'ago, cucitura a zigzag a destra.';

  @override
  String get t_explain01_13 => 'Sopraggitto (tessuti medi ed elastici) , applicazione nastro ed elastico.';

  @override
  String get t_explain01_14 => 'Sopraggitto (tessuti medi, pesanti ed elastici) , applicazione nastro ed elastico.';

  @override
  String get t_explain01_14a => 'Sopraggitto (tessuti medi, pesanti ed elastici) , applicazione nastro ed elastico.';

  @override
  String get t_explain01_15 => 'Rinforzo di tessuti leggeri e medi.';

  @override
  String get t_explain01_16 => 'Rinforzo di tessuti pesanti.';

  @override
  String get t_explain01_17 => 'Rinforzo di tessuti medi, pesanti e facilmente sfilacciabili o per punti decorativi.';

  @override
  String get t_explain01_18 => 'Cuciture rinforzate di tessuti elastici.';

  @override
  String get t_explain01_19 => 'Rinforzo di tessuti elastici medi e tessuti pesanti, punti decorativi.';

  @override
  String get t_explain01_20 => 'Rinforzo di tessuti elastici, punti decorativi.';

  @override
  String get t_explain01_21 => 'Cuciture su tessuti elastici a maglia.';

  @override
  String get t_explain01_22 => 'Rinforzi e cuciture di tessuti elastici.';

  @override
  String get t_explain01_23 => 'Rinforzo di tessuti elastici.';

  @override
  String get t_explain01_24 => 'Punto diritto durante il taglio del tessuto.';

  @override
  String get t_explain01_25 => 'Punto zig-zag durante il taglio del tessuto.';

  @override
  String get t_explain01_26 => 'Punto sopraggitto durante il taglio del tessuto.';

  @override
  String get t_explain01_27 => 'Punto sopraggitto durante il taglio del tessuto.';

  @override
  String get t_explain01_28 => 'Punto sopraggitto durante il taglio del tessuto.';

  @override
  String get t_explain01_29 => 'Rattoppo/patchwork con giuntura destra consentita di 6,5mm (1/4 in.) .';

  @override
  String get t_explain01_292 => 'Rattoppo/patchwork con giuntura destra consentita di 7mm .';

  @override
  String get t_explain01_29a => 'Rattoppo/patchwork';

  @override
  String get t_explain01_30 => 'Rattoppo/patchwork con giuntura sinistra consentita di 6,5mm (1/4 in.) .';

  @override
  String get t_explain01_302 => 'Rattoppo/patchwork con giuntura sinistra consentita di 7mm .';

  @override
  String get t_explain01_31 => 'Punto per trapunta con aspetto simile al punto a mano.';

  @override
  String get t_explain01_32 => 'Punto a zig-zag per trapuntare e cucire su pezzi di trapunta applicati.';

  @override
  String get t_explain01_33 => 'Punto per trapunta per applicazioni invisibili o giunture.';

  @override
  String get t_explain01_34 => 'Trapuntatura su sfondo.';

  @override
  String get t_explain02_01 => 'Orli su stoffe tessute.';

  @override
  String get t_explain02_02 => 'Orli su tessuti elastici.';

  @override
  String get t_explain02_03 => 'Applicazioni, punto coperta decorativo.';

  @override
  String get t_explain02_04 => 'Finitura con orlo a conchiglia su tessuti. Aumentare la tensione del filo superiore per ottenere una finitura pregevole a smerlo dei punti orlo a conchiglia.';

  @override
  String get t_explain02_05 => 'Decorazione di colletti di camicette, orli di fazzoletti.';

  @override
  String get t_explain02_06 => 'Decorazione di colletti di camicette, orli di fazzoletti.';

  @override
  String get t_explain02_07 => 'Punti patchwork, punti decorativi.';

  @override
  String get t_explain02_08 => 'Punti patchwork, punti decorativi.';

  @override
  String get t_explain02_09 => 'Punto decorativo, fissaggio di cordicelle.';

  @override
  String get t_explain02_10 => 'Smock, punti decorativi.';

  @override
  String get t_explain02_11 => 'Punto a giorno, punti decorativi.';

  @override
  String get t_explain02_12 => 'Punto a giorno, punti decorativi e di collegamento.';

  @override
  String get t_explain02_13 => 'Applicazione di nastro su giunture su tessuto elastico.';

  @override
  String get t_explain02_14 => 'Punti decorativi';

  @override
  String get t_explain02_15 => 'Punti decorativi superiori.';

  @override
  String get t_explain02_15a => 'Punti decorativi';

  @override
  String get t_explain02_16 => 'Punti decorativi.';

  @override
  String get t_explain02_17 => 'Punti decorativi e fissaggio elastico.';

  @override
  String get t_explain02_18 => 'Punti decorativi e applicazioni.';

  @override
  String get t_explain02_19 => 'Punti decorativi';

  @override
  String get t_explain03_01 => 'Orli decorativi, punto diritto triplo a sinistra.';

  @override
  String get t_explain03_02 => 'Orli decorativi, punto diritto triplo al centro.';

  @override
  String get t_explain03_03 => 'Orli decorativi, rinforzi.';

  @override
  String get t_explain03_04 => 'Orli decorativi, cuciture di merletti, punti spillo.';

  @override
  String get t_explain03_05 => 'Orli decorativi.';

  @override
  String get t_explain03_06 => 'Orli decorativi con punto a margherita.';

  @override
  String get t_explain03_07 => 'Decorazioni tradizionali, orli decorativi.';

  @override
  String get t_explain03_08 => 'Decorazioni tradizionali, orli decorativi.';

  @override
  String get t_explain03_09 => 'Decorazioni tradizionali, orli decorativi.';

  @override
  String get t_explain03_10 => 'Decorazioni tradizionali, orli decorativi.';

  @override
  String get t_explain03_11 => 'Decorazioni tradizionali, orli decorativi.';

  @override
  String get t_explain03_12 => 'Decorazioni tradizionali, orli decorativi.';

  @override
  String get t_explain03_13 => 'Decorazioni tradizionali, orli decorativi.';

  @override
  String get t_explain03_14 => 'Decorazioni tradizionali, orli decorativi.';

  @override
  String get t_explain03_15 => 'Decorazioni tradizionali, orli decorativi.';

  @override
  String get t_explain03_16 => 'Decorazioni tradizionali, orli decorativi.';

  @override
  String get t_explain03_17 => 'Decorazioni tradizionali, orli decorativi.';

  @override
  String get t_explain03_18 => 'Decorazioni tradizionali, orli decorativi.';

  @override
  String get t_explain03_19 => 'Orli decorativi e punto di collegamento.';

  @override
  String get t_explain03_20 => 'Orli decorativi Punto a giorno, applicazione di nastri.';

  @override
  String get t_explain03_21 => 'Orli decorativi, smock.';

  @override
  String get t_explain03_22 => 'Orli decorativi, smock.';

  @override
  String get t_explain03_23 => 'Orli decorativi, smock.';

  @override
  String get t_explain03_24 => 'Orli decorativi.';

  @override
  String get t_explain03_25 => 'Punti decorativi.';

  @override
  String get t_explain04_01 => 'Asole su tessuti da leggeri a medi.';

  @override
  String get t_explain04_02 => 'Asole con spazio supplementare per bottoni grandi.';

  @override
  String get t_explain04_03 => 'Asole affusolate di chiusura con rinforzo.';

  @override
  String get t_explain04_04 => 'Asole con travetta verticale per tessuti pesanti.';

  @override
  String get t_explain04_05 => 'Asole con travetta.';

  @override
  String get t_explain04_06 => 'Asole su tessuti leggeri, medi e pesanti.';

  @override
  String get t_explain04_07 => 'Asole per tessuti da leggeri a medi.';

  @override
  String get t_explain04_08 => 'Asole con spazio supplementare per bottoni decorativi grandi.';

  @override
  String get t_explain04_09 => 'Bottoni di resistenza con travette verticali.';

  @override
  String get t_explain04_10 => 'Asole per tessuti elastici o orditi.';

  @override
  String get t_explain04_11 => 'Asole per decorazioni tradizionali e tessuti elastici.';

  @override
  String get t_explain04_12 => 'Primo passo per eseguire asole a cornice.';

  @override
  String get t_explain04_13 => 'Asole su tessuti pesanti o spessi per bottoni piatti grandi.';

  @override
  String get t_explain04_14 => 'Asole su tessuti da medi a pesanti per bottoni piatti grandi.';

  @override
  String get t_explain04_15 => 'Asole con travetta verticale per il rinforzo di tessuti pesanti spessi.';

  @override
  String get t_explain04_15a => 'Lato sinistro di asole a quattro fasi.';

  @override
  String get t_explain04_15b => 'Travetta di asole a quattro fasi.';

  @override
  String get t_explain04_15c => 'Lato destro di asole a quattro fasi.';

  @override
  String get t_explain04_15d => 'Travetta di asole a quattro fasi.';

  @override
  String get t_explain04_16 => 'Rammendo di tessuti medi.';

  @override
  String get t_explain04_17 => 'Rammendo di tessuti pesanti.';

  @override
  String get t_explain04_18 => 'Rinforzo di aperture di tasche, ecc.';

  @override
  String get t_explain04_19 => 'Applicazione di bottoni.';

  @override
  String get t_explain04_20 => 'Per esecuzione di occhielli, fori su cinture, ecc. Se la cucitura non viene eseguita in modo corretto, regolare il disegno/punto.';

  @override
  String get t_explain04_21 => 'Per l\'esecuzione di occhielli o fori a forma di stella. Se la cucitura non viene eseguita in modo corretto, regolare il disegno/punto.';

  @override
  String get t_explain05_01 => 'Per applicazioni su pezzi tubolari di tessuto e giuntura di angoli.';

  @override
  String get t_explain05_02 => 'Per applicazioni su pezzi tubolari di tessuto e giuntura di angoli.';

  @override
  String get t_explain05_03 => 'Per applicazioni su pezzi tubolari di tessuto e giuntura di angoli.';

  @override
  String get t_explain05_04 => 'Per applicazioni su pezzi tubolari di tessuto.';

  @override
  String get t_explain05_05 => 'Per applicazioni su pezzi tubolari di tessuto.';

  @override
  String get t_explain05_06 => 'Per applicazione di appliqué su pezzi tubolari di tessuto e giuntura di angoli.';

  @override
  String get t_explain05_07 => 'Per applicazione di appliqué su pezzi tubolari di tessuto e giuntura di angoli.';

  @override
  String get t_explain05_08 => 'Per applicazioni su pezzi tubolari di tessuto e giuntura di angoli.';

  @override
  String get t_explain05_09 => 'Per applicazioni su pezzi tubolari di tessuto.';

  @override
  String get t_explain05_10 => 'Per applicazioni su pezzi tubolari di tessuto.';

  @override
  String get t_explain05_11 => 'Per applicazioni su pezzi tubolari di tessuto e giuntura di angoli.';

  @override
  String get t_explain05_12 => 'Per applicazioni su pezzi tubolari di tessuto e giuntura di angoli.';

  @override
  String get t_explain06_01 => 'Per applicare filati, ecc. e creare ornamenti con filati decorativi utilizzando la cucitura a mano libera.';

  @override
  String get t_explain06_02 => 'Imbastitura a mano libera\nQuando la griffa di trasporto è abbassata, è possibile cucire l\'imbastitura muovendo liberamente il tessuto.';

  @override
  String get t_explain06_03a => 'Questo modello di punto è costituito da diversi punti corti.\nPer fare sì che il progetto sembri cucito a mano, cucire questo punto utilizzando per il filo superiore un filo di nylon trasparente o leggero di un colore che si abbini al tessuto. Se il filo spolina è di un colore diverso dal tessuto, il punto risalterà.';

  @override
  String get t_explain07_01 => 'Applicazioni, punto decorativo.';

  @override
  String get t_explain07_02 => 'Il punto può essere assottigliato all\'inizio o alla fine della cucitura.';

  @override
  String get t_explaindeco00_01 => 'Cucitura di finiture pregevoli';

  @override
  String get t_explaindeco00_02 => 'Regolazioni';

  @override
  String get t_explaindeco01_00 => '[Cucitura di finiture pregevoli]';

  @override
  String get t_explaindeco01_01 => 'Per cucire finiture pregevoli quando si eseguono punti con caratteri o decorativi, verificare le combinazioni di tessuto, ago e filo. Altri fattori, quali lo spessore del tessuto, il materiale stabilizzatore, ecc., influiscono sul punto; pertanto, occorre sempre cucire dei punti di prova prima di iniziare il lavoro effettivo.';

  @override
  String get t_explaindeco01_02 => '(a) Tessuto\n(b) Stabilizzatore\n(c) Carta sottile\n\n*Tessuto Quando si cuce su tessuti elastici, tessuti leggeri o tessuti con ordito grezzo, si consiglia di utilizzare del materiale stabilizzatore. Se non si desidera utilizzare tale materiale, posizionare il tessuto su carta sottile.\n\n*Filo\n#50 - #60\n\n*Ago\nCon tessuti leggeri, normali o elastici: ago con punta a sfera (colore dorato) \nCon tessuti pesanti: ago per macchina da cucire domestica 90/14\n\n*Piedino premistoffa\nPiedino per monogrammi \"N+\". L\'uso di un altro tipo di piedino premistoffa può fornire risultati inferiori.';

  @override
  String get t_explaindeco02_00 => '[Regolazioni]';

  @override
  String get t_explaindeco02_01 => 'L\'esecuzione di un disegno/punto può talvolta risultare scadente; ciò è dovuto al tipo di spessore del tessuto, al materiale stabilizzatore utilizzato, alla velocità di cucitura, ecc. Se la cucitura non risulta ottimale, cucire punti di prova ricreando le condizioni della cucitura reale e regolare il disegno/punto come descritto di seguito. Se il disegno non viene eseguito in modo corretto anche dopo le regolazioni basate sul motivo 6-120, effettuare individualmente le regolazioni per ciascun disegno.';

  @override
  String get t_explaindeco02_02 => '1. Selezionare 6-120. Collegare il piedino per monogrammi \"N+\" e cucire il disegno.';

  @override
  String get t_explaindeco02_03_00 => '\n2. Confrontare il disegno finito con la figura del disegno corretto riportata di seguito.';

  @override
  String get t_explaindeco02_04_00 => '[1. Se il disegno è raggruppato]';

  @override
  String get t_explaindeco02_04_01 => 'Premere + in FINE ADJUST VERTI. Cucire nuovamente il disegno/punto. Se il disegno risulta ancora non ottimale, eseguire nuovamente le regolazioni. Ripetere le regolazioni fino ad ottenere il disegno/punto corretto.';

  @override
  String get t_explaindeco02_05_00 => '[2. Se il disegno presenta spazi]';

  @override
  String get t_explaindeco02_05_01 => 'Premere - in FINE ADJUST VERTI. Cucire nuovamente il disegno/punto. Se il disegno risulta ancora non ottimale, eseguire nuovamente le regolazioni. Ripetere le regolazioni fino ad ottenere il disegno/punto corretto.';

  @override
  String get t_explaindeco02_06_00 => '[3. Se il disegno non è allineato a sinistra]';

  @override
  String get t_explaindeco02_06_01 => 'Premere + in FINE ADJUST HORIZ. Se il disegno risulta ancora non ottimale, eseguire nuovamente le regolazioni. Ripetere le regolazioni fino ad ottenere il disegno/punto corretto.';

  @override
  String get t_explaindeco02_07_00 => '[4. Se il disegno non è allineato a destra]';

  @override
  String get t_explaindeco02_07_01 => 'Premere - in FINE ADJUST HORIZ. Se il disegno risulta ancora non ottimale, eseguire nuovamente le regolazioni. Ripetere le regolazioni fino ad ottenere il disegno/punto corretto.';

  @override
  String get t_terms_read => 'Leggere attentamente le condizioni seguenti.';

  @override
  String get t_terms_cert_read => 'Leggere attentamente le condizioni seguenti.';

  @override
  String get t_terms_cert_01_00 => 'Certificazione del Kit di aggiornamento';

  @override
  String get t_terms_cert_01_01 => 'Condizioni relative alla certificazione del kit di aggiornamento\n';

  @override
  String get t_terms_cert_01_02 => ' Attivando eventuali funzioni opzionali in questo software (\"Software\"), inclusi, ma non solo, eventuali licenze a pagamento, manuali, documenti e altri materiali, nonché uno qualsiasi dei relativi aggiornamenti (collettivamente, gli \"Strumenti\"), all\'utente potrebbe essere richiesto, direttamente o indirettamente, di fornire determinati codici di licenza, il numero di prodotto, il numero di serie e altre informazioni correlate (\"Dati dell\'utente\") al fine di utilizzare gli Strumenti.\n';

  @override
  String get t_terms_cert_01_03 => '  Alcune delle informazioni incluse nei Dati dell\'utente possono essere associate ai dati che l\'utente potrebbe aver registrato sul sito Web di registrazione dei prodotti di Brother Industries, Ltd. (\"Società\") o delle sue filiali.  Tuttavia, la Società non utilizzerà i Dati dell\'utente per identificare l\'utente stesso né per qualsiasi altra finalità diversa dall\'attivazione degli Strumenti. I Dati dell\'utente potrebbero essere trasmessi al server amministrativo della Società o ai server dei provider di servizi cloud, come ad esempio Microsoft e Amazon, che potrebbero essere ubicati in paesi che non presentano un livello adeguato di protezione dei dati personali rispetto a quello del paese dell\'utente. Tuttavia, la Società proteggerà i Dati dell\'utente conformemente alla legge applicabile adottando misure di protezione adeguate per prevenire l\'uso o la divulgazione non autorizzati.';

  @override
  String get t_terms_nettool_read => 'Leggere attentamente le condizioni seguenti.';

  @override
  String get t_terms_nettool_01_00 => 'Strumento di diagnosi di rete';

  @override
  String get t_terms_nettool_01_01 => 'Condizioni relative allo strumento di diagnosi di rete\n';

  @override
  String get t_terms_nettool_01_02 => '  In caso di problemi con la connessione di rete, l\'utente potrebbe scegliere di eseguire lo strumento di diagnosi di rete incluso in questo software (\"Software\").  Eseguendo una diagnosi di rete, sullo schermo verranno visualizzate informazioni provenienti dai prodotti cuciti o creati (\"Prodotto della Società\") e dagli eventuali dispositivi connessi al Prodotto della Società, inclusi, ma non solo, indirizzo IP o MAC, informazioni sulla connessione proxy, subnet mask, gateway, server DNS e altre informazioni correlate (\"Informazioni correlate alla rete\").  ';

  @override
  String get t_terms_nettool_01_03 => '  In caso di problemi con la connessione Internet del Prodotto della Società e si desideri ricevere assistenza tecnica, all\'utente potrebbe essere richiesto di comunicare le sue Informazioni correlate alla rete ai suoi rivenditori o fornitori locali oppure a Brother Industries, Ltd. (\"Società\") e/o alle sue filiali di persona, per telefono, via e-mail, fax o Internet.  Scegliendo di fornire le sue Informazioni correlate alla rete, l\'utente accetta e prende atto che le Informazioni correlate alla rete potrebbero essere trasferite alla Società o alle sue filiali esclusivamente allo scopo di analizzare o risolvere il suo problema di rete e che le sue informazioni verranno protette conformemente alla legge applicabile.\n Le Informazioni correlate alla rete dell\'utente non verranno raccolte o memorizzate dalla Società o dalle sue filiali per scopi diversi da quelli descritti in questo documento, a meno che la Società e/o le sue filiali ottengano separatamente e preventivamente l\'approvazione dell\'utente.';

  @override
  String get t_terms_cert_read_t => 'Leggere attentamente le condizioni seguenti.';

  @override
  String get t_terms_cert_01_01_t => 'Condizioni relative alla certificazione del kit di aggiornamento\n';

  @override
  String get t_terms_cert_01_02_t => '  Quando attiva eventuali funzioni opzionali contenute nel presente software (\"Software\"), inclusi, ma non solo, eventuali licenze a pagamento, documenti e altri materiali (collettivamente gli \"Strumenti\"), all\'utente potrebbe essere richiesto, direttamente o indirettamente, di fornire determinati codici di licenza, numeri di prodotto e numeri di serie (\"Dati utente\") al fine di utilizzare gli Strumenti.\n';

  @override
  String get t_terms_cert_01_03_t => '  Alcune delle informazioni contenute nei Dati utenti possono essere associate ai dati che l\'utente potrebbe aver registrato sul sito Web per la registrazione dei prodotti di Tacony Corporation d/b/a Baby Lock (\"Società\").  Tuttavia, la Società non utilizzerà i Dati utente per identificare l\'utente né per qualsiasi altra finalità diversa dall\'attivazione degli Strumenti. I Dati utente potranno essere trasmessi al server amministrativo della Società o ai server dei provider di servizi cloud, per esempio Microsoft ed AWS, che potranno trovarsi in paesi che non garantiscono un livello adeguato di protezione dei dati personali rispetto al paese dell\'utente.  Tuttavia, la Società proteggerà i Dati utente dell\'utente conformemente alle leggi applicabili adottando misure di protezione atte a prevenire l\'uso o la divulgazione non autorizzati.';

  @override
  String get t_terms_nettool_read_t => 'Leggere attentamente le condizioni seguenti.';

  @override
  String get t_terms_nettool_01_01_t => 'Condizioni relative allo strumento di diagnosi della rete\n';

  @override
  String get t_terms_nettool_01_02_t => '  In caso di problemi con la connessione di rete, l\'utente potrà scegliere di eseguire lo strumento di diagnosi della rete contenuto nel presente software (\"Software\").  Eseguendo una diagnosi di rete, sullo schermo potranno essere visualizzate le informazioni dei prodotti per cucire (\"Prodotto della Società\") e degli eventuali dispositivi connessi al Prodotto della Società, inclusi, ma non solo, indirizzo IP o MAC, informazioni sulla connessione proxy, subnet mask, gateway, server DNS e altre informazioni correlate (\"Informazioni relative alla rete\").  ';

  @override
  String get t_terms_nettool_01_03_t => '  Se l\'utente ha problemi con la connessione Internet del Prodotto della Società o desidera ottenere assistenza tecnica, potrà essergli richiesto di comunicare le sue Informazioni relative alla rete al proprio rivenditore locale o a Tacony Corporation d/b/a Baby Lock (\"Società\") di persona, per telefono, via e-mail, fax o Internet.  Scegliendo di fornire le proprie Informazioni relative alla rete, l\'utente accetta e prende atto che le Informazioni relative alla rete potranno essere trasferire alla Società esclusivamente al fine di analizzare o risolvere il problema di rete dell\'utente e che le sue informazioni saranno protette conformemente alla legge applicabile.\n  Se non diversamente ivi stabilito, le Informazioni relative alla rete dell\'utente non verranno raccolte o conservate dalla Società a meno che la Società non abbia preventivamente e separatamente ricevuto l\'autorizzazione dell\'utente.';

  @override
  String get t_terms_mnmpinmac_01_b => 'Facendo clic su “OK”, il codice PIN, l\'indirizzo MAC e il numero della macchina verranno inviati al server di Brother per abbinare la macchina per cucire in uso al proprio ScanNCut e alle altre macchine per cucire di cui si dispone.\nLe informazioni fornite non verranno utilizzate per finalità diverse da quelle precedentemente indicate.';

  @override
  String get t_terms_snj_pair_01 => 'Facendo clic su \"OK\", il proprio Codice PIN, l\'indirizzo MAC, il nome della macchina ed il numero della macchina saranno inviati al server di Brother per associare la macchina per cucire ai propri dispositivi/servizi Brother. \nLe informazioni fornite non verranno utilizzate per finalità diverse da quelle precedentemente indicate.';

  @override
  String get upg_01 => 'Connettere il supporto USB.';

  @override
  String get upg_02 => 'Impossibile leggere il file.';

  @override
  String get upg_03 => 'Impossibile trovare il file corretto.';

  @override
  String get upg_04 => 'Errore di checksum';

  @override
  String get upg_05 => 'Tentativo di salvare il file non riuscito.';

  @override
  String get upg_06 => 'Il percorso del file non è corretto.';

  @override
  String get upg_07 => 'Connesso al PC. Non disconnettere il cavo USB.';

  @override
  String get upg_08 => 'Salvataggio del file di aggiornamento in corso. \nNon spegnere l\'interruttore generale della macchina.';

  @override
  String get update_08 => 'Salvataggio del file di aggiornamento in corso. \nNon spegnere l\'interruttore generale della macchina.';

  @override
  String get upg_09 => 'Aggiornamento terminato.';

  @override
  String get update_09 => 'Aggiornamento terminato.';

  @override
  String get upg_10 => 'Premere CARICA dopo aver connesso il supporto USB contenente il file di aggiornamento.';

  @override
  String get update_10 => 'Premere CARICA dopo aver connesso il supporto USB contenente il file di aggiornamento.';

  @override
  String get upg_12 => 'Premere CARICA per installare il file di aggiornamento.';

  @override
  String get update_13 => 'Impossibile aggiornare la versione corrente direttamente alla nuova versione.';

  @override
  String get update_14 => 'Aggiornare il software alla versione indicata di seguito spegnendo la macchina ed eseguendo l\'aggiornamento con il supporto USB contenente il file di aggiornamento.';

  @override
  String get update_15 => 'Spegnere la macchina ed eseguire l\'aggiornamento con il supporto USB contenente il file di aggiornamento.';

  @override
  String get icon_00037 => 'Ritorno';

  @override
  String get icon_00008_u => 'Chiudi';

  @override
  String get icon_00009_u => 'Cancella';

  @override
  String get icon_00010_u => 'ＯＫ';

  @override
  String get icon_00050_u => 'Carica';

  @override
  String get upg_16 => 'Aggiornamento non riuscito. \nRiprovare a installare il programma di aggiornamento. \n* Se il problema persiste, riscaricare e installare il programma.';

  @override
  String get upg_17 => 'Aggiornamento non riuscito. \nScaricare e installare di nuovo il programma di aggiornamento.';

  @override
  String get upg_18 => 'ERR_UPEND';

  @override
  String get upg_19 => 'Riavviare la macchina.\nPotrebbe volerci un po\' di tempo per avviare la macchina inizialmente. Lo schermo potrebbe oscurarsi temporaneamente.';

  @override
  String get upg_20 => 'Non spegnere la macchina anche se lo schermo si oscura.';

  @override
  String get upg_21 => 'Rilevamento danneggiamento del file system non riuscito.\nSpegnere e riaccendere l\'alimentazione.';

  @override
  String get upg_22 => 'Impossibile riparare i file di sistema danneggiati.\nSpegnere e riaccendere l\'alimentazione.';

  @override
  String get upg_23 => 'Aggiornamento non riuscito. \nDopo aver avviato normalmente la macchina, spegnerla e provare nuovamente a installare il programma di aggiornamento.';

  @override
  String get t_name_01_01 => 'Punto diritto (Sinistra)';

  @override
  String get t_name_01_02 => 'Punto diritto (Sinistra)';

  @override
  String get t_name_01_03 => 'Punto diritto (Centro)';

  @override
  String get t_name_01_04 => 'Punto diritto (Centro)';

  @override
  String get t_name_01_05 => 'Punto elastico triplo';

  @override
  String get t_name_01_06 => 'Punto elastico';

  @override
  String get t_name_01_07 => 'Punto decorativo';

  @override
  String get t_name_01_08 => 'Punto di imbastitura';

  @override
  String get t_name_01_09 => 'Punto zig-zag';

  @override
  String get t_name_01_10 => 'Punto zig-zag';

  @override
  String get t_name_01_11 => 'Punto zig-zag (Destra)';

  @override
  String get t_name_01_12 => 'Punto zig-zag (Sinistra)';

  @override
  String get t_name_01_13 => 'Punto elastico zig-zag (2 fasi)';

  @override
  String get t_name_01_14 => 'Punto elastico zig-zag (2 fasi)';

  @override
  String get t_name_01_14a => 'Punto elastico zig-zag (3 fasi)';

  @override
  String get t_name_01_15 => 'Punto sopraggitto';

  @override
  String get t_name_01_16 => 'Punto sopraggitto';

  @override
  String get t_name_01_17 => 'Punto sopraggitto';

  @override
  String get t_name_01_18 => 'Punto sopraggitto';

  @override
  String get t_name_01_19 => 'Punto sopraggitto';

  @override
  String get t_name_01_20 => 'Punto sopraggitto';

  @override
  String get t_name_01_21 => 'Punto sopraggitto';

  @override
  String get t_name_01_22 => 'Sopraggitto diamante singolo';

  @override
  String get t_name_01_23 => 'Sopraggitto diamante singolo';

  @override
  String get t_name_01_24 => 'Con taglierina laterale';

  @override
  String get t_name_01_25 => 'Con taglierina laterale';

  @override
  String get t_name_01_26 => 'Con taglierina laterale';

  @override
  String get t_name_01_27 => 'Con taglierina laterale';

  @override
  String get t_name_01_28 => 'Con taglierina laterale';

  @override
  String get t_name_01_29 => 'Punto rammendo (Destra)';

  @override
  String get t_name_01_29a => 'Punto rammendo (Centro)';

  @override
  String get t_name_01_30 => 'Punto rammendo (Sinistra)';

  @override
  String get t_name_01_31 => 'Trapunta tipo \"fatta a mano\"';

  @override
  String get t_name_01_32 => 'Punto zig-zag per applicazioni trapunte';

  @override
  String get t_name_01_33 => 'Punto per applicazioni trapunte';

  @override
  String get t_name_01_34 => 'Trapunta punteggiata';

  @override
  String get t_name_02_01 => 'Punto orlo invisibile';

  @override
  String get t_name_02_02 => 'Punto orlo invisibile elastico';

  @override
  String get t_name_02_03 => 'Punto coperta';

  @override
  String get t_name_02_03a => 'Punto coperta';

  @override
  String get t_name_02_04 => 'Punto orlo conchiglia';

  @override
  String get t_name_02_05 => 'Punto smerlo a pieno';

  @override
  String get t_name_02_06 => 'Punto smerlo';

  @override
  String get t_name_02_07 => 'Punto unione patchwork';

  @override
  String get t_name_02_08 => 'Punto sopraggitto doppio patchwork';

  @override
  String get t_name_02_09 => 'Punto di fissaggio';

  @override
  String get t_name_02_10 => 'Punto smock';

  @override
  String get t_name_02_11 => 'Punto a piuma';

  @override
  String get t_name_02_12 => 'Punto incrociato';

  @override
  String get t_name_02_13 => 'Applicazione nastri';

  @override
  String get t_name_02_14 => 'Punto a scala';

  @override
  String get t_name_02_15 => 'Punto rick-rack';

  @override
  String get t_name_02_15a => 'Punto decorativo';

  @override
  String get t_name_02_16 => 'Punto decorativo';

  @override
  String get t_name_02_17 => 'Punto a serpentina';

  @override
  String get t_name_02_18 => 'Punto decorativo';

  @override
  String get t_name_02_19 => 'Punto punteggiato decorativo';

  @override
  String get t_name_03_01 => 'Punto orlo';

  @override
  String get t_name_03_02 => 'Punto orlo';

  @override
  String get t_name_03_03 => 'Orlo zig-zag';

  @override
  String get t_name_03_04 => 'Punto orlo';

  @override
  String get t_name_03_05 => 'Punto orlo';

  @override
  String get t_name_03_06 => 'Punto orlo';

  @override
  String get t_name_03_07 => 'Punto orlo';

  @override
  String get t_name_03_08 => 'Punto orlo';

  @override
  String get t_name_03_09 => 'Punto orlo';

  @override
  String get t_name_03_10 => 'Punto orlo';

  @override
  String get t_name_03_11 => 'Punto orlo';

  @override
  String get t_name_03_12 => 'Punto a nido d\'ape';

  @override
  String get t_name_03_13 => 'Punto a nido d\'ape';

  @override
  String get t_name_03_14 => 'Punto orlo';

  @override
  String get t_name_03_15 => 'Punto orlo';

  @override
  String get t_name_03_16 => 'Punto orlo';

  @override
  String get t_name_03_17 => 'Punto orlo';

  @override
  String get t_name_03_18 => 'Punto orlo';

  @override
  String get t_name_03_19 => 'Punto orlo';

  @override
  String get t_name_03_20 => 'Punto orlo';

  @override
  String get t_name_03_21 => 'Punto orlo';

  @override
  String get t_name_03_22 => 'Punto orlo';

  @override
  String get t_name_03_23 => 'Punto orlo';

  @override
  String get t_name_03_24 => 'Punto orlo';

  @override
  String get t_name_03_25 => 'Punto a scala';

  @override
  String get t_name_04_01 => 'Asola arrotondata stretta';

  @override
  String get t_name_04_02 => 'Asola larga a estremità tonda';

  @override
  String get t_name_04_03 => 'Asola affusolata a estremità tonda';

  @override
  String get t_name_04_04 => 'Asola con estremità arrotondata';

  @override
  String get t_name_04_05 => 'Asola con estremità arrotondata';

  @override
  String get t_name_04_06 => 'Asola con estremità arrotondate';

  @override
  String get t_name_04_07 => 'Asola rettangolare stretta';

  @override
  String get t_name_04_08 => 'Asola rettangolare larga';

  @override
  String get t_name_04_09 => 'Asola rettangolare';

  @override
  String get t_name_04_10 => 'Asola elastica';

  @override
  String get t_name_04_11 => 'Asola decorativa';

  @override
  String get t_name_04_12 => 'Asola cornice';

  @override
  String get t_name_04_13 => 'Asola a serratura';

  @override
  String get t_name_04_14 => 'Asola a serratura affusolata';

  @override
  String get t_name_04_15 => 'Asola a occhiello';

  @override
  String get t_name_04_15a => 'asola 1 (4 fasi)';

  @override
  String get t_name_04_15b => 'asola 2 (4 fasi)';

  @override
  String get t_name_04_15c => 'asola 3 (4 fasi)';

  @override
  String get t_name_04_15d => 'asola 4 (4 fasi)';

  @override
  String get t_name_04_16 => 'Rammendo';

  @override
  String get t_name_04_17 => 'Rammendo';

  @override
  String get t_name_04_18 => 'Travetta';

  @override
  String get t_name_04_19 => 'Cucitura bottoni';

  @override
  String get t_name_04_20 => 'Occhiello';

  @override
  String get t_name_04_21 => 'Occhiello a stella';

  @override
  String get t_name_05_01 => 'Diagonale sinistra su (Diritto)';

  @override
  String get t_name_05_02 => 'Indietro (Diritto)';

  @override
  String get t_name_05_03 => 'Diagonale destra su (Diritto)';

  @override
  String get t_name_05_04 => 'Laterale a sinistra (Diritto)';

  @override
  String get t_name_05_05 => 'Laterale a destra (Diritto)';

  @override
  String get t_name_05_06 => 'Diagonale sinistra giù (Diritto)';

  @override
  String get t_name_05_07 => 'Avanti (Diritto)';

  @override
  String get t_name_05_08 => 'Diagonale destra giù (Diritto)';

  @override
  String get t_name_05_09 => 'Laterale a sinistra (Zig-zag)';

  @override
  String get t_name_05_10 => 'Laterale a destra (Zig-zag)';

  @override
  String get t_name_05_11 => 'Avanti (Zig-zag)';

  @override
  String get t_name_05_12 => 'Indietro (Zig-zag)';

  @override
  String get t_name_06_01 => 'Punto per fissare cordoncini a mano libera';

  @override
  String get t_name_06_02 => 'Imbastitura a mano libera';

  @override
  String get t_name_06_03 => 'Trapunta tipo \"fatta a mano\"';

  @override
  String get t_name_06_04 => 'Trapunta tipo \"fatta a mano\"';

  @override
  String get t_name_06_05 => 'Trapunta tipo \"fatta a mano\"';

  @override
  String get t_name_06_06 => 'Punto per feltratura ad ago';

  @override
  String get t_name_07_01 => 'Punto per applicazioni';

  @override
  String get t_name_07_02 => 'Punto assottigliato';

  @override
  String get t_name_sr_01 => 'Punto diritto (Centro)';

  @override
  String get t_name_sr_02 => 'Punto zig-zag';

  @override
  String get t_name_sr_03 => 'Imbastitura a mano libera';

  @override
  String get tt_head_wifi => 'Impostazioni LAN wireless';

  @override
  String get tt_head_camera => 'Visualizzazione fotocamera';

  @override
  String get tt_head_setting => 'Impostazioni macchina';

  @override
  String get tt_head_teaching => 'Guida all\'uso della macchina';

  @override
  String get tt_head_osae => 'Sostituzione ago/piedino premistoffa';

  @override
  String get tt_head_lock => 'Blocco schermo';

  @override
  String get tt_head_home => 'Home page';

  @override
  String get tt_foot_clock => 'Impostazioni Ora/Data';

  @override
  String get tt_tch_og_principal_parts1 => '[Leva del piedino premistoffa]';

  @override
  String get tt_tch_og_principal_parts2 => '[Regolatore della velocità di cucitura]';

  @override
  String get tt_tch_og_principal_parts3 => '[Volantino manuale]';

  @override
  String get tt_tch_og_principal_parts4 => '[Prolunga base piana con scatola accessori]';

  @override
  String get tt_tch_og_mb_knee_lifter => '[Ginocchiera alzapiedino]';

  @override
  String get tt_tch_og_principal_parts6 => '[Pedale reostato]';

  @override
  String get tt_tch_og_principalbuttons1 => '[Pulsante \"Posizione ago\"]';

  @override
  String get tt_tch_og_principalbuttons2 => '[Pulsante \"Taglio del filo\"]';

  @override
  String get tt_tch_og_principalbuttons3 => '[Pulsante \"Alzapiedino premistoffa\"]';

  @override
  String get tt_tch_og_principalbuttons4 => '[Pulsante \"Infilatura automatica\"]';

  @override
  String get tt_tch_og_principalbuttons5 => '[Pulsante \"Avvio/stop\"]';

  @override
  String get tt_tch_og_principalbuttons6 => '[Pulsante \"Cucitura di ritorno\"]';

  @override
  String get tt_tch_og_principalbuttons7 => '[Pulsante \"Rinforzo/Fermatura\"]';

  @override
  String get tt_tch_og_basic_operation1 => '[Infilatura superiore]';

  @override
  String get tt_tch_og_basic_operation2 => '[Avvolgimento spolina]';

  @override
  String get tt_tch_og_basic_operation3 => '[Sostituzione dell\'ago]';

  @override
  String get tt_tch_og_basic_operation4 => '[Sostituzione del piedino premistoffa]';

  @override
  String get tt_tch_og_basic_operation5 => '[Inserimento della spolina]';

  @override
  String get tt_tch_og_emb_basic_operation1 => '[Regolazione della tensione del filo]';

  @override
  String get tt_tch_og_emb_basic_operation2 => '[Applicazione di  stabilizzatore termoadesivo al tessuto]';

  @override
  String get tt_tch_og_emb_basic_operation3 => '[Inserimento del tessuto]';

  @override
  String get tt_tch_og_emb_basic_operation4 => '[Montaggio del telaio per ricamo]';

  @override
  String get tt_tch_og_emb_basic_operation5 => '[Montaggio dell\'unità ricamo]';

  @override
  String get tt_tch_og_emb_basic_operation6 => '[Montaggio del piedino per ricamo \"W\"]';

  @override
  String get tt_tch_og_emb_basic_operation7 => '[Stabilizzatore corretto da usare]';

  @override
  String get tt_tch_maintenance1 => '[Pulizia della guida del crochet e del crochet]';

  @override
  String get tt_utl_category01 => 'Punti diritti/Sopraggitto';

  @override
  String get tt_utl_category02 => 'Punti decorativi';

  @override
  String get tt_utl_category03 => 'Punti decorativi tradizionali';

  @override
  String get tt_utl_category04 => 'Asole/Travette';

  @override
  String get tt_utl_category05 => 'Cucitura multi-direzionale';

  @override
  String get tt_utl_category_q => 'Punti per trapuntatura (quilting)';

  @override
  String get tt_utl_category_s => 'Altri punti';

  @override
  String get tt_utl_category_t => 'Assottigliamento dei punti (tapering)';

  @override
  String get tt_utl_stitchpreview => 'Anteprima';

  @override
  String get tt_utl_projecter => 'Funzioni proiettore';

  @override
  String get tt_utl_guideline => 'Linea guida';

  @override
  String get tt_utl_editmenu => 'Menu Modifica';

  @override
  String get tt_utl_freemotion => 'Modalità free motion';

  @override
  String get tt_utl_repeat_stitch_atamadashi => 'Torna all\'inizio';

  @override
  String get tt_utl_alone_repeat => 'Cucitura singola/ripetuta';

  @override
  String get tt_utl_utilityflipvertical => 'Immagine riflessa';

  @override
  String get tt_utl_twinneedle => 'Ago singolo/ago gemello';

  @override
  String get tt_utl_buttonholemanual => 'Lunghezza fessura dell\'asola';

  @override
  String get tt_utl_endpointsetting => 'Impostazione di fine punto';

  @override
  String get tt_utl_tapering => 'Assottigliamento dei punti (tapering)';

  @override
  String get tt_utl_autoreverse => 'Punto di rinforzo automatico';

  @override
  String get tt_utl_scissor => 'Taglio automatico del filo';

  @override
  String get tt_utl_needlestopposition => 'Impostazione della posizione di arresto dell\'ago';

  @override
  String get tt_utl_pivot => 'Rotazione sugli angoli/Sollevamento automatico';

  @override
  String get tt_utl_threadcolor => 'Cambio colore filo';

  @override
  String get tt_utl_category06 => 'Vari e grandi';

  @override
  String get tt_utl_category07 => 'Floreali grandi';

  @override
  String get tt_utl_category08 => 'Motivi e scritte grandi';

  @override
  String get tt_utl_category09 => 'Vari e piccoli';

  @override
  String get tt_utl_category10 => 'Floreali piccoli';

  @override
  String get tt_utl_category11 => 'Nodini in rilievo';

  @override
  String get tt_utl_category12 => 'Pieni grandi';

  @override
  String get tt_utl_category13 => 'Pieni';

  @override
  String get tt_utl_category14 => 'Punti croce';

  @override
  String get tt_utl_category15 => 'Utili combinabili';

  @override
  String get tt_utl_category16 => 'Disney';

  @override
  String get tt_utl_category17 => 'Carattere gotico';

  @override
  String get tt_utl_category18 => 'Carattere corsivo';

  @override
  String get tt_utl_category19 => 'Carattere solo contorno';

  @override
  String get tt_utl_category20 => 'Carattere cirillico';

  @override
  String get tt_deco_category_pocket => 'Tasca(Memoria della macchina/esterna)';

  @override
  String get tt_deco_mycustomsititch => 'Funzione “MY CUSTOM STITCH (PUNTI PERSONALIZZATI)”';

  @override
  String get tt_deco_stitchpreview => 'Anteprima';

  @override
  String get tt_deco_projecter => 'Funzioni proiettore';

  @override
  String get tt_deco_guidline => 'Linea guida';

  @override
  String get tt_deco_editmenu => 'Menu Modifica';

  @override
  String get tt_deco_memory => 'Salvataggio punto';

  @override
  String get tt_deco_threadcolor => 'Cambio colore filo';

  @override
  String get tt_deco_stitchplus => 'Aggiungi punto';

  @override
  String get tt_deco_stitchselectall => 'Attiva/disattiva Seleziona tutti';

  @override
  String get tt_deco_pivot => 'Rotazione sugli angoli/Sollevamento automatico';

  @override
  String get tt_deco_needlestopposition => 'Impostazione Posizione di arresto dell\'ago';

  @override
  String get tt_deco_scissor => 'Taglio automatico del filo';

  @override
  String get tt_deco_autoreverse => 'Punto di rinforzo automatico';

  @override
  String get tt_deco_stitchstep1 => 'Effetto scalato';

  @override
  String get tt_deco_stitchstep2 => 'Effetto scalato';

  @override
  String get tt_deco_filemanager => 'Gestione file';

  @override
  String get tt_deco_filemanager_selectall => 'Seleziona tutti';

  @override
  String get tt_deco_filemanager_selectnone => 'Deseleziona tutto';

  @override
  String get tt_deco_filemanager_delete => 'Elimina';

  @override
  String get tt_deco_filemanager_memory => 'Salvare i punti selezionati sulla macchina.';

  @override
  String get tt_deco_freemotion => 'Modalità free motion';

  @override
  String get tt_deco_repeat_stitch_atamadashi => 'Torna all\'inizio';

  @override
  String get tt_deco_alone_repeat => 'Cucitura singola/ripetuta';

  @override
  String get tt_deco_utilityfliphorizon => 'Immagine speculare orizzontale';

  @override
  String get tt_deco_utilityflipvertical => 'Immagine speculare verticale';

  @override
  String get tt_deco_alone_single => 'Ago singolo/ago gemello';

  @override
  String get tt_deco_delete => 'Elimina';

  @override
  String get tt_deco_density => 'Densità filo';

  @override
  String get tt_deco_elongator => 'Allungamento';

  @override
  String get tt_deco_spacing => 'Spaziatura caratteri';

  @override
  String get tt_deco_stitchsizelink => 'Mantieni proporzioni';

  @override
  String get tt_deco_endpointsetting => 'Impostazione punto di fine';

  @override
  String get tt_mcs_triplesewing => 'Cucitura singola/tripla';

  @override
  String get tt_mcs_pointdelete => 'Eliminazione punto';

  @override
  String get tt_mcs_blockmove => 'Spostamento blocco';

  @override
  String get tt_mcs_insert => 'Inserisci';

  @override
  String get tt_utl_mcspointset => 'Imposta';

  @override
  String get tt_mcs_contents => 'Importa punti';

  @override
  String get tt_mcs_memory => 'Salvataggio punto';

  @override
  String get tt_utl_sr_guideline => 'Linea guida';

  @override
  String get tt_utl_sr_sensingline => 'Rilevamento linea';

  @override
  String get tt_utl_sr_srstatus => 'Stato Regolatore punto';

  @override
  String get tt_embcate_embpatterns => 'Ricami';

  @override
  String get tt_embcate_character => 'Ricami con caratteri';

  @override
  String get tt_embcate_decoalphabet => 'Ricami alfabeti decorativi';

  @override
  String get tt_embcate_frame => 'Ricami a cornice';

  @override
  String get tt_embcate_utility => 'Ricami per asole/ Ricamo di punti decorativi a telaio';

  @override
  String get tt_embcate_split => 'Ricami divisi';

  @override
  String get tt_embcate_long_stitch => 'Ricami a punto lungo';

  @override
  String get tt_embcate_quilt => 'Bordi continui per quilt';

  @override
  String get tt_embcate_b_disney => 'Ricami Disney';

  @override
  String get tt_embcate_couching => 'Ricami per couching';

  @override
  String get tt_embcate_t_exclusives => 'Exclusives';

  @override
  String get tt_embcate_memory => 'Ricami salvati nella memoria della macchina, su supporti USB, ecc.';

  @override
  String get tt_emb_pantool => 'Strumento Mano';

  @override
  String get tt_emb_backgroundscan => 'Scansione tessuto';

  @override
  String get tt_emb_realpreview => 'Anteprima';

  @override
  String get tt_emb_memory => 'Memoria';

  @override
  String get tt_emb_redo => 'Ripeti';

  @override
  String get tt_emb_undo => 'Annulla';

  @override
  String get tt_emb_delete => 'Elimina';

  @override
  String get tt_emb_select => 'Selezionare';

  @override
  String get tt_emb_multipleselect => 'Selezione multipla';

  @override
  String get tt_emb_editsize => 'Dimensioni';

  @override
  String get tt_emb_editmove => 'Sposta';

  @override
  String get tt_emb_editgroup => 'Raggruppa/separa';

  @override
  String get tt_emb_editrotate => 'Ruota';

  @override
  String get tt_emb_editflip => 'Capovolgi in orizzontale';

  @override
  String get tt_emb_editduplicate => 'Duplica';

  @override
  String get tt_emb_editdensity => 'Densità';

  @override
  String get tt_emb_editborder => 'Funzione bordo (Realizzazione di ricami ripetuti)';

  @override
  String get tt_emb_editapplique => 'Applicazione';

  @override
  String get tt_emb_editchangecolor => 'Tabella dei fili';

  @override
  String get tt_emb_edittextedit => 'Modifica ricami con caratteri';

  @override
  String get tt_emb_editalign => 'Allineamento';

  @override
  String get tt_emb_editstippling => 'Trapuntatura (stippling)';

  @override
  String get tt_emb_editoutline => 'Rilevamento contorno';

  @override
  String get tt_emb_editorder => 'Ordine di ricamo';

  @override
  String get tt_emb_editnotsew => 'Nessuna impostazione di cucitura';

  @override
  String get tt_emb_textsize => 'Dimensioni';

  @override
  String get tt_emb_textarray => 'Disponi';

  @override
  String get tt_emb_textspacing => 'Spaziatura caratteri';

  @override
  String get tt_emb_textalign => 'Allineamento';

  @override
  String get tt_emb_embfootw => 'Controllo punto caduta ago';

  @override
  String get tt_emb_emb_projectorsetting => 'Impostazioni proiettore';

  @override
  String get tt_emb_embprojector => 'Proiettore';

  @override
  String get tt_emb_embmove => 'Sposta';

  @override
  String get tt_emb_embrotate => 'Ruota';

  @override
  String get tt_emb_embbasting => 'Imbastitura';

  @override
  String get tt_emb_embsnowman => 'Posizionamento ricamo';

  @override
  String get tt_emb_embonecolorsew => 'Ricamo continuo';

  @override
  String get tt_emb_embcolorsorting => 'Ordinamento colori';

  @override
  String get tt_emb_embconnectsew => 'Collega ricamo';

  @override
  String get tt_emb_embframemove => 'Spostamento del telaio: La cornice si sposterà temporaneamente al centro.';

  @override
  String get tt_emb_embmemory => 'Memoria';

  @override
  String get tt_emb_embmasktrace => 'Traccia area';

  @override
  String get tt_emb_embstartposition => 'Punto di partenza';

  @override
  String get tt_emb_embneedlenumber => 'Avanti/Indietro';

  @override
  String get tt_emb_embfbcamera => 'Visualizzazione fotocamera';

  @override
  String get tt_emb_embthreadcutting => 'Taglio/Tensione';

  @override
  String get tt_emb_embcolorbar => 'Un colore/tutti i colori per indicatore di stato';

  @override
  String get tt_emb_patterninfo => 'Informazioni ricamo';

  @override
  String get tt_emb_previewsim => 'Simulatore punti';

  @override
  String get tt_emb_sewtrim_endcolor => 'Rifinitura fine colore';

  @override
  String get tt_emb_sewtrim_jumpstitch => 'Rifinitura punti di collegamento';

  @override
  String get tt_emb_previewframe => 'Anteprima telaio per ricamo';

  @override
  String get tt_emb_size_normalstb => 'Modificare le dimensioni del ricamo conservando il numero di punti/la densità del punto';

  @override
  String get tt_emb_edit_border_vert => 'Ripeti ricamo/elimina in verticale';

  @override
  String get tt_emb_edit_border_horiz => 'Ripeti ricamo/elimina in orizzontale';

  @override
  String get tt_emb_edit_border_dividervert => 'Taglia ricamo in verticale';

  @override
  String get tt_emb_edit_border_dividehoriz => 'Taglia ricamo in orizzontale';

  @override
  String get tt_emb_edit_border_threadmark => 'Punti di riallineamento';

  @override
  String get tt_emb_edit_border_reset => 'Reimposta';

  @override
  String get tt_emb_emb_rotate_reset => 'Reimposta';

  @override
  String get tt_emb_edit_rotate_reset => 'Reimposta';

  @override
  String get tt_emb_camera_rotate_reset => 'Reimposta';

  @override
  String get tt_emb_edit_font_spacing_reset => 'Reimposta';

  @override
  String get tt_emb_edit_align_reset => 'Reimposta';

  @override
  String get tt_emb_edit_size_reset => 'Reimposta';

  @override
  String get tt_emb_edit_order_reset => 'Reimposta';

  @override
  String get tt_emb_quiltborder_color_reset => 'Reimposta';

  @override
  String get tt_emb_edit_color_reset => 'Reimposta';

  @override
  String get tt_emb_photositich_size_change_reset => 'Reimposta';

  @override
  String get tt_emb_edit_projlcd_switch_fb_reset => 'Reimposta';

  @override
  String get tt_emb_edit_projlcd_align_reset => 'Reimposta';

  @override
  String get tt_emb_edit_projlcd_border_reset => 'Reimposta';

  @override
  String get tt_emb_edit_projlcd_rotate_reset => 'Reimposta';

  @override
  String get tt_emb_edit_projlcd_size_reset => 'Reimposta';

  @override
  String get tt_mdc_paint_rotate_reset => 'Reimposta';

  @override
  String get tt_mdc_paint_size_input_reset => 'Reimposta';

  @override
  String get tt_mdc_paint_size_reset => 'Reimposta';

  @override
  String get tt_emb_newapplique_color_selectall => 'Seleziona tutti';

  @override
  String get tt_emb_newapplique_color_selectnone => 'Deseleziona tutto';

  @override
  String get tt_emb_color_selectall => 'Seleziona colore singolo/tutti i colori';

  @override
  String get tt_emb_colorcolorshuffling => 'Color Shuffling(Rimescolamento colori)';

  @override
  String get tt_emb_colorvisualizer => 'Color Visualizer';

  @override
  String get tt_emb_editselectall => 'Seleziona tutti';

  @override
  String get tt_emb_editdeselectall => 'Deseleziona tutto';

  @override
  String get tt_emb_infoprintimage => 'In combinazione con l\'immagine di stampa';

  @override
  String get tt_emb_infooutputfiles => 'Sul supporto USB vengono copiati 3 file PDF (per tessuto stampabile/trasferimento termico/posizionamento).';

  @override
  String get tt_emb_filemanager => 'Gestione file';

  @override
  String get tt_emb_filemanager_selectall => 'Seleziona tutti';

  @override
  String get tt_emb_filemanager_selectnone => 'Deseleziona tutto';

  @override
  String get tt_emb_filemanager_delete => 'Elimina';

  @override
  String get tt_emb_filemanager_memory => 'Salvare i ricami selezionati sulla macchina.';

  @override
  String get tt_emb_easystippling_stippling => 'Ricamo con trapuntatura (stippling)';

  @override
  String get tt_emb_easystippling_echo => 'Ricamo echo quilting';

  @override
  String get tt_emb_easystippling_decorativefill => 'Riempimento decorativo';

  @override
  String get tt_emb_quitlsash_startpoint => 'Proietta punto di partenza';

  @override
  String get tt_emb_quitlsash_endtpoint => 'Proietta punto di fine';

  @override
  String get tt_emb_connect_migimawari => 'La posizione del secondo ricamo si sposta in senso orario';

  @override
  String get tt_emb_connect_hidarimawari => 'La posizione del secondo ricamo si sposta in senso antiorario';

  @override
  String get tt_emb_connect_rotate => 'Ruota';

  @override
  String get tt_emb_quiltborder_save => 'Memoria';

  @override
  String get tt_mdc_pantool => 'Strumento Mano';

  @override
  String get tt_mdc_scanmenu => 'È possibile creare ricami personalizzati utilizzando immagini acquisite o file di immagini.';

  @override
  String get tt_mdc_datacall => 'Recupera dati di tracciatura del ricamo (.pm9)';

  @override
  String get tt_mdc_linetool => 'Strumento Linea';

  @override
  String get tt_mdc_lineproperty => 'Proprietà linea';

  @override
  String get tt_mdc_linespoit => 'Strumento Preleva colore per linea';

  @override
  String get tt_mdc_linepouring => 'Strumento Secchiello per linea';

  @override
  String get tt_mdc_brushtool => 'Strumento Pennello';

  @override
  String get tt_mdc_brushproperty => 'Proprietà area';

  @override
  String get tt_mdc_brushspoit => 'Strumento Preleva colore per area';

  @override
  String get tt_mdc_brushpouring => 'Strumento Secchiello per area';

  @override
  String get tt_mdc_painteraser => 'Gomma';

  @override
  String get tt_mdc_paintstamp => 'Forme di timbro';

  @override
  String get tt_mdc_paintsize => 'Dimensioni';

  @override
  String get tt_mdc_paintrotate => 'Ruota';

  @override
  String get tt_mdc_paintflip => 'Immagine speculare';

  @override
  String get tt_mdc_paintduplicate => 'Duplicato';

  @override
  String get tt_mdc_paintcut => 'Taglia';

  @override
  String get tt_mdc_paintpaste => 'Incolla';

  @override
  String get tt_mdc_memory => 'Salvare i dati di tracciatura del ricamo (.pm9)';

  @override
  String get tt_mdc_select => 'Selezionare';

  @override
  String get tt_mdc_redo => 'Ripeti';

  @override
  String get tt_mdc_undo => 'Annulla';

  @override
  String get tt_mdc_allclear => 'Cancella tutto';

  @override
  String get tt_mdc_lineopen => 'Linea a mano libera con estremità aperta';

  @override
  String get tt_mdc_lineclose => 'Linea a mano libera con chiusura automatica dell\'estremità';

  @override
  String get tt_mdc_lineline => 'Linea retta con un tratto';

  @override
  String get tt_mdc_linepolygonal => 'Forma poligonale';

  @override
  String get tt_mdc_stitchzigzag => 'Punto zig-zag';

  @override
  String get tt_mdc_stitchrunning => 'Punto dritto';

  @override
  String get tt_mdc_stitchtriple => 'Punto triplo';

  @override
  String get tt_mdc_stitchcandle => 'Punto nodini in rilievo';

  @override
  String get tt_mdc_stitchchain => 'Punto catenella';

  @override
  String get tt_mdc_stitchestitch => 'Punto pettine';

  @override
  String get tt_mdc_stitchvsitich => 'Punto a V';

  @override
  String get tt_mdc_stitchmotif => 'Punti decorativi';

  @override
  String get tt_mdc_stitchnnotsew => 'Linea senza cucitura';

  @override
  String get tt_mdc_stitchzigzaglowdensity => 'Punto zig-zag per applicazioni';

  @override
  String get tt_mdc_regiontatami => 'Ricamo con punto di riempimento';

  @override
  String get tt_mdc_regionstippling => 'Ricamo con trapuntatura (stippling)';

  @override
  String get tt_mdc_regiondecorativefill => 'Riempimenti decorativi';

  @override
  String get tt_mdc_regionnotsew => 'Nessun punto';

  @override
  String get tt_mdc_stamp1 => 'Forme di base';

  @override
  String get tt_mdc_stamp2 => 'Forme chiuse';

  @override
  String get tt_mdc_stamp3 => 'Forme aperte';

  @override
  String get tt_mdc_stamp4 => 'Contorni salvati';

  @override
  String get tt_mdc_stamp5 => 'Area di ricamo del telaio';

  @override
  String get tt_mdc_stamp6 => 'Taglio contorni';

  @override
  String get tt_mdc_select_rectangle => 'Selezione Casella';

  @override
  String get tt_mdc_select_continuousrectangle => 'Selezione Forma poligonale';

  @override
  String get tt_mdc_select_free => 'Selezione Curva stile libero';

  @override
  String get tt_mdc_select_auto => 'Selezione automatica';

  @override
  String get tt_mdc_select_all => 'Seleziona tutti';

  @override
  String get tt_mdc_memory_drawemb => 'Salvare i dati di tracciatura del ricamo (.pm9) e i ricami (.phx).';

  @override
  String get tt_mdc_embset_pantool => 'Strumento Mano';

  @override
  String get tt_mdc_embset_patterninfo => 'Informazioni ricamo';

  @override
  String get tt_mdc_embset_realpreview => 'Anteprima';

  @override
  String get tt_mdc_embset_projector => 'Proiettore';

  @override
  String get tt_mdc_embset_projectorsetting => 'Impostazioni proiettore';

  @override
  String get tt_mdc_zigzagwidth => 'Ampiezza Zigzag';

  @override
  String get tt_mdc_zigzagdensity => 'Densità';

  @override
  String get tt_mdc_runpitch => 'Lunghezza punto';

  @override
  String get tt_mdc_running_undersew => 'Sotto cucitura';

  @override
  String get tt_mdc_candlewicksize => 'Dimensioni';

  @override
  String get tt_mdc_candlewickspacing => 'Spaziatura';

  @override
  String get tt_mdc_chainsize => 'Dimensioni';

  @override
  String get tt_mdc_chainthickness => 'Spessore';

  @override
  String get tt_mdc_estitchwidth => 'Larghezza punto';

  @override
  String get tt_mdc_estitchspacing => 'Spaziatura';

  @override
  String get tt_mdc_estitchthickness => 'Spessore';

  @override
  String get tt_mdc_estitchflip => 'Capovolgi';

  @override
  String get tt_mdc_vstitchwidth => 'Larghezza punto';

  @override
  String get tt_mdc_vstitchspacing => 'Spaziatura';

  @override
  String get tt_mdc_vstitchthickness => 'Spessore';

  @override
  String get tt_mdc_vstitchflip => 'Capovolgi';

  @override
  String get tt_mdc_motifstitchsize => 'Dimensioni';

  @override
  String get tt_mdc_motifstitchspacing => 'Spaziatura';

  @override
  String get tt_mdc_motifstitchflip => 'Capovolgi';

  @override
  String get tt_mdc_zigzagwidth_2 => 'Ampiezza Zigzag';

  @override
  String get tt_mdc_zigzagdensity_2 => 'Densità';

  @override
  String get tt_mdc_tatamiderection => 'Direzione';

  @override
  String get tt_mdc_tatamidensity => 'Densità';

  @override
  String get tt_mdc_tatamipullconpen => 'Compensazione trazione';

  @override
  String get tt_mdc_tatamiundersewing => 'Sotto cucitura';

  @override
  String get tt_mdc_stiprunpitch => 'Lunghezza punto';

  @override
  String get tt_mdc_stipspacing => 'Spaziatura';

  @override
  String get tt_mdc_stipdistance => 'Distanza';

  @override
  String get tt_mdc_stipsingletriple => 'Punto singolo/triplo';

  @override
  String get tt_mdc_decofillsize => 'Dimensioni';

  @override
  String get tt_mdc_decofilldirection => 'Direzione';

  @override
  String get tt_mdc_decofilloutline => 'Contorno per ridurre taglio del filo';

  @override
  String get tt_mdc_decofillrandomshift => 'Spostamento casuale';

  @override
  String get tt_mdc_decofillpositionoffset => 'Offset posizione';

  @override
  String get tt_mdc_decofillthickness1 => 'Spessore';

  @override
  String get tt_mdc_decofillthickness3 => 'Spessore';

  @override
  String get tt_mdc_decofillthickness1_2 => 'Singolo-doppio';

  @override
  String get tt_mdc_decofillthickness2_3 => 'Doppio-triplo';

  @override
  String get tt_mdc_stitchlink => 'Seleziona tutti gli oggetti con le stesse impostazioni punto in una volta';

  @override
  String get tt_mdc_fill_linereading => 'Le aree e i perimetri lineari vengono convertiti in contorni. Scegli lo spessore del contorno.';

  @override
  String get tt_mdc_fill_linecolor => 'I contorni estratti con il colore specificato vengono convertiti in attributi linea.';

  @override
  String get tt_emb_photostitch_backremoval => 'Rimozione sfondo';

  @override
  String get tt_emb_photostitch_framing => 'Inquadratura dell\'immagine';

  @override
  String get tt_emb_photostitch_fittoframe => 'Adatta a telaio';

  @override
  String get tt_emb_photostitch_backremoval_scopeplus => 'Aggiungere una nuova area di ritaglio:contrassegnare l\'area da ritagliare con una linea.';

  @override
  String get tt_emb_photostitch_backremoval_scopeminus => 'Cancellare l\'area di ritaglio:contrassegnare l\'area da non ritagliare con una linea.';

  @override
  String get tt_emb_photostitch_backremoval_erase => 'Eliminare la linea di disegno specificata.';

  @override
  String get tt_emb_photostitch_backremoval_trash => 'Eliminare tutte le linee di disegno.';

  @override
  String get tt_emb_photostitch_backremoval_blind => 'Mostrare/Nascondere tutte le linee tracciate con le penne.';

  @override
  String get tt_emb_photostitch_styleusecolor => 'ON: Usa i colori dell\'immagine di stile/OFF: Usa i colori della foto originale';

  @override
  String get tt_emb_photostitch_colortune => 'Regolazione colore';

  @override
  String get tt_emb_photostitch_colorlis_allselect => 'Conserva/cancella tutti i colori dei fili dell\'elenco colori';

  @override
  String get tt_emb_photostitch_colorlis_add => 'Aggiungere un colore all\'elenco dei colori';

  @override
  String get tt_emb_photostitch_colorlis_remove => 'Rimuove il colore selezionato dall\'elenco colori';

  @override
  String get tt_emb_photostitch_pantool => 'Strumento Mano';

  @override
  String get tt_emb_photostitch_memory => 'Memoria';

  @override
  String get tt_emb_edit_projectorsetting => 'Impostazioni proiettore';

  @override
  String get tt_emb_edit_projector => 'Proiettore';

  @override
  String get tt_settings_reset_3type => 'Azzera le impostazioni (Cucito/Generali/Ricamo)';

  @override
  String get tt_settings_screenimage_usb => 'Salva un\'immagine della schermata delle impostazioni sul supporto USB';

  @override
  String get tt_camera_emb_screenshot => 'Salvare un\'immagine della fotocamere sul supporto USB.';

  @override
  String get tt_camera_emb_grid => 'Mostra/Nascondi griglia';

  @override
  String get tt_camera_emb_needlepoint => 'Mostra/Nascondi il punto di rilascio dell\'ago';

  @override
  String get tt_camera_util_screenshot => 'Salvare un\'immagine della fotocamere sul supporto USB.';

  @override
  String get tt_camera_util_grid => 'Mostra/Nascondi griglia';

  @override
  String get tt_camera_util_needlepoint => 'Mostra/Nascondi il punto di rilascio dell\'ago';
}
