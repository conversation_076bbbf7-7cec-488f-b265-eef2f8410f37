import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/embedit_bindings_generated.dart';

import '../../../../../../../model/projector_model.dart';
import '../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../../ui/global_popup/global_popup_route.dart';
import '../../../../../../../ui/global_popup/global_popup_route_enum.dart';
import '../../../../../../global_popup/global_popups/err_emb_pattern_exceeded/err_emb_pattern_exceeded_view_model.dart';
import '../../../../../../global_popup/global_popups/err_pattern_cannot_save/err_pattern_cannot_save_view_model.dart';
import '../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../model/large_connected_model.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/redo_undo_model.dart';
import '../../../../model/sewing_model.dart';
import '../../../component/emb_header/emb_header_view_model.dart';
import '../../../page_route.dart';
import '../../common_component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../../common_component/memory/memory.dart';
import '../sewing_view_interface.dart';
import '../sewing_view_model.dart';
import 'bottom_state.dart';

final bottomProvider =
    StateNotifierProvider.autoDispose<BottomViewModel, BottomState>(
        (ref) => BottomViewModel(ref));

class BottomViewModel extends ViewModel<BottomState>
    with DeviceLibraryEventObserver {
  BottomViewModel(this._ref) : super(const BottomState()) {
    _openLayoutMenuListen();

    /// View更新
    update();
  }

  ///
  /// providerのref
  ///
  final AutoDisposeStateNotifierProviderRef<BottomViewModel, BottomState> _ref;

  ///
  /// ViewModel更新
  ///
  @override
  void update() {
    EmbSewingAttribParam_t embSewingAttrib =
        TpdLibrary().apiBinding.bPIFGetAppDisplayEmb().embSewingAttrib.ref;
    state = state.copyWith(
      startPositionButtonState:
          _getStartPositionButtonState(embSewingAttrib.startPositionState),
      maskTraceButtonState:
          _getMaskTraceButtonState(embSewingAttrib.maskTraceState),
      returnButtonState: _getReturnButtonState(),
      memoryButtonState: _getMemoryButtonState(embSewingAttrib.memoryState),
      threadCutButtonState:
          _getThreadCutButtonState(embSewingAttrib.threadCutState),
      isEnglish: PatternModel().isEnglish,
      isProjectorON: ProjectorModel().embProjector.isEmbProjectorViewOpen,
    );
  }

  ///
  /// Memoryボタンのクリック関数
  ///
  void onMemoryButtonClick(BuildContext context) {
    if (state.memoryButtonState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }

    /// 縫製設定でMemoryボタンが押されたときに実行される確認処理を行う
    final checkError = EmbLibrary().apiBinding.checkMemoryForEmbSewing();
    if (checkError == EmbLibraryError.EMB_NO_ERR) {
      /// do nothing
    } else if (checkError == EmbLibraryError.EMB_PATTERN_CANNOT_SAVE) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_PATTERN_CANNOT_SAVE,
        arguments: PatternCannotSaveArgument(
          onOkButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    } else if (checkError == EmbLibraryError.EMB_PATTERN_EXCEEDED) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_EMB_PATTERN_EXCEEDED,
        arguments: EmbPatternExceededArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    } else {
      /// do nothing
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// Memoryポップアップを開く
    state = state.copyWith(popupType: MemoryPopupId.save);

    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => Memory(
          onCancelClick: () => PopupNavigator.pop(context: context),
        ),
      ),
    );
  }

  ///
  /// Returnボタンのクリック関数
  ///
  Future<void> onReturnButtonClick() async {
    // TODO: error共通対応 : http://apngoap306.ap.brothergroup.net:8080/jira/browse/PHBSH-1096
    // if (_isNeedlePositionDown == true) {
    //   _openErrNeedleUpPopup(context);
    //   return;
    // }
    if (_getReturnButtonState() == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 投影機能ON時の取り扱い
    if (ProjectorModel().embProjector.isEmbProjectorViewOpen == true) {
      final embProjectorFunction =
          _ref.read(embProjectorFunctionProvider.notifier);

      /// プロジェクト起動・停止前にエラーCheckをする
      /// エラーがあれば ポープアープを表示する
      final hasError = embProjectorFunction
          .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
      if (hasError) {
        return;
      }

      embProjectorFunction.closeEmbProjectorView(
        closingHandleCallback: () {
          ProjectorModel().embProjector.isEmbProjectorViewOpen = false;
          _ref
              .read(sewingProvider.notifier)
              .updateSewingByChild(ModuleType.projector);
          _ref.read(embHeaderViewModelProvider.notifier).update();
        },
        afterClosedHandleCallback: () {},
      );
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.change);

    /// モノクロモード設定
    EmbLibrary().apiBinding.setEmbSewNonStopSewing(false);

    /// 色の並べ替えを閉じます
    EmbLibrary().apiBinding.setColorSort(false);
    LargeConnectedModel().reset();

    /// W押えのプロジェクションがオンになっている場合は、プロジェクションをオフにします
    if (ProjectorModel().embProjector.isEmbWFooterProjectorOpen() == true) {
      ProjectorModel().embProjector.closeEmbProjector();
    } else {
      /// Do Nothing
    }

    /// LEDPointerを閉じます
    DeviceLibrary().apiBinding.closeLedPtSetting();

    /// 他の画面を更新する
    _ref
        .read(sewingProvider.notifier)
        .updateSewingByChild(ModuleType.colorProgressBar);

    EmbLibraryError error = EmbLibrary().apiBinding.embSewingReturnEdit();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 画面遷移前にAPI呼び出し
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }

    PagesRoute().pushNamedAndRemoveUntil(
      nextRoute: PageRouteEnum.patternEdit,
      untilRoute: PageRouteEnum.home,
    );
  }

  ///
  /// Toolボタンのクリック関数
  /// 糸切り・糸調子設定機能
  ///
  void onSewingToolButtonClick() {
    /// ページをジャンプ
    SewingModel().isSewingPreviewCanDarg = false;
    PagesRoute()
        .pushNamed(nextRoute: PageRouteEnum.threadCutting)
        .then((value) {
      SewingModel().isSewingPreviewCanDarg = true;
    });
  }

  ///
  /// Forward/Backボタンのクリック関数
  /// 縫い目戻る／進む（ステッチForward/Back）機能
  ///
  void onForwardBackwardButtonClick() {
    EmbLibraryError error = EmbLibrary().apiBinding.embSewingSelectFb();

    if (error != EmbLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// ページをジャンプ
    SewingModel()
      ..isForwardBackwardPageOpen = true
      ..isSewingPreviewCanDarg = false;
    PagesRoute()
        .pushNamed(nextRoute: PageRouteEnum.stitchForwardBackward)
        .then((_) {
      SewingModel()
        ..isForwardBackwardPageOpen = false
        ..isSewingPreviewCanDarg = true;
    });
  }

  ///
  /// 縫い始めボタンのクリック関数
  ///
  void onStartPointButtonClick() {
    if (state.startPositionButtonState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// errorの検査
    final error = EmbLibrary().apiBinding.embSewingSelectStartPosition();

    if (error != EmbLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 縫い始め位置に移動
    SewingModel().isSewingPreviewCanDarg = false;
    PagesRoute()
        .pushNamed(nextRoute: PageRouteEnum.startPosition)
        .then((value) {
      SewingModel().isSewingPreviewCanDarg = true;
    });
  }

  ///
  /// ためしボタンのクリック関数
  ///
  void onTrialButtonClick() {
    if (state.maskTraceButtonState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// errorの検査
    final error = EmbLibrary().apiBinding.embSewingSelectMaskTrace();

    if (error != EmbLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// マスクトレースに移動
    SewingModel().isSewingPreviewCanDarg = false;
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.maskTrace).then((value) {
      SewingModel().isSewingPreviewCanDarg = true;
    });
  }

  ///
  /// EmbUnit退避
  ///
  void onRemoveEmbUnitButtonClicked() {
    var errorCode = EmbLibrary().apiBinding.embSewingEscUnit();
    if (errorCode == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else if (errorCode == EmbLibraryError.EMB_SEW_ESC_UNIT_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    } else if (errorCode == EmbLibraryError.EMB_NEEDL_DOWN_ERR ||
        errorCode == EmbLibraryError.EMB_OSAE_DOWN_ERR) {
      /// エラー画面 Do Nothing
    } else if (errorCode == EmbLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    } else {
      /// Do nothing
    }
  }

  ///
  /// メイン側から見る「刺繍針上？」ポップアップを閉じる
  ///
  void closeErrNeedleUpPopup() {
    state = state.copyWith(
      isErrNeedleUpPopup: false,
    );
  }

  ///
  /// 縫い始め位置ボタンの状態取得
  ///
  ButtonState _getStartPositionButtonState(int startPositionState) {
    if (AppInfoFuncState.values[startPositionState] ==
        AppInfoFuncState.disable) {
      return ButtonState.disable;
    } else {
      return ButtonState.normal;
    }
  }

  ///
  ///  マスクトレースボタンの状態取得
  ///
  ButtonState _getMaskTraceButtonState(int maskTraceState) {
    if (AppInfoFuncState.getValueByNumber(maskTraceState) ==
        AppInfoFuncState.disable) {
      return ButtonState.disable;
    } else {
      return ButtonState.normal;
    }
  }

  ///
  ///  マスクトレースボタンの状態取得
  ///
  ButtonState _getThreadCutButtonState(int threadCutState) {
    if (AppInfoFuncState.getValueByNumber(threadCutState) ==
        AppInfoFuncState.disable) {
      return ButtonState.disable;
    } else {
      return ButtonState.normal;
    }
  }

  ///
  ///  マスクトレースボタンの状態取得
  ///
  ButtonState _getMemoryButtonState(int memoryState) {
    if (AppInfoFuncState.getValueByNumber(memoryState) ==
        AppInfoFuncState.disable) {
      return ButtonState.disable;
    } else {
      return ButtonState.normal;
    }
  }

  ///
  /// 戻るボタンの状態を取得する
  ///
  ButtonState _getReturnButtonState() {
    if (SewingModel().isAutoOneDirection()) {
      return ButtonState.disable;
    } else {
      return ButtonState.normal;
    }
  }

  ///
  /// データ更新ページをリッスンする
  ///
  ProviderSubscription? _listener;

  ///
  /// 縫製情報データ変更監視がONになっている
  ///
  void _openLayoutMenuListen() {
    /// スタート位置メニューが有効か
    _listener = _ref.stateListen(
      appDisplayEmbStateProvider
          .select((value) => value.embSewingAttrib.ref.startPositionState),
      (previous, nextState) {
        state = state.copyWith(
          startPositionButtonState: _getStartPositionButtonState(nextState),
        );
      },
    );

    /// マスクトレースメニューが有効か
    _ref.stateListen(
      appDisplayEmbStateProvider
          .select((value) => value.embSewingAttrib.ref.maskTraceState),
      (previous, nextState) {
        state = state.copyWith(
          maskTraceButtonState: _getMaskTraceButtonState(nextState),
        );
      },
    );

    /// 保存メニューが有効か
    _ref.stateListen(
      appDisplayEmbStateProvider
          .select((value) => value.embSewingAttrib.ref.memoryState),
      (previous, nextState) {
        state = state.copyWith(
          memoryButtonState: _getMemoryButtonState(nextState),
        );
      },
    );

    /// 糸切設定ができるかどうか
    _ref.stateListen(
      appDisplayEmbStateProvider
          .select((value) => value.embSewingAttrib.ref.threadCutState),
      (previous, nextState) {
        state = state.copyWith(
          threadCutButtonState: _getThreadCutButtonState(nextState),
        );
      },
    );
  }

  @override
  void dispose() {
    super.dispose();

    /// データの停止
    _listener?.close();
  }
}
