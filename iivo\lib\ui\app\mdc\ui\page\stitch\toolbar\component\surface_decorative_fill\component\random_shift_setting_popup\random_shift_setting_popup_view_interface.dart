import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../../../../model/stitch/surface_decorative_fill_model.dart'
    show DecoFillSettingState;
part 'random_shift_setting_popup_view_interface.freezed.dart';

typedef SettingState = DecoFillSettingState;

@freezed
class RandomShiftSettingPopupState with _$RandomShiftSettingPopupState {
  const factory RandomShiftSettingPopupState({
    @Default("0") String shiftValue,
    @Default(0) int typeValue,
    @Default(SettingState.settingCompleted) SettingState typeSettingState,
    @Default(false) bool isShiftMinButtonGrey,
    @Default(false) bool isShiftPlusButtonGrey,
    @Default(false) bool isTypeLeftButtonGrey,
    @Default(false) bool isTypeRightButtonGrey,
  }) = _RandomShiftSettingPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class RandomShiftSettingPopupStateViewInterface
    extends ViewModel<RandomShiftSettingPopupState> {
  RandomShiftSettingPopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// ゆらぎ程度マイナスボタンをクリックする
  ///
  bool onShiftMinusButtonClicked(bool isLongPress);

  ///
  /// ゆらぎ程度プラスボタンをクリックする
  ///
  bool onShiftPlusButtonClicked(bool isLongPress);

  ///
  /// ゆらぎタイプ左ボタンをクリックする
  ///
  bool onTypeLeftButtonClicked(bool isLongPress);

  ///
  /// ゆらぎタイプ右ボタンをクリックする
  ///
  bool onTypeRightButtonClicked(bool isLongPress);

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// ゆらぎタイプの表示テキストを取得する
  ///
  String getTypeString(context);

  ///
  /// ゆらぎ程度はデフォルト値かどか
  ///
  bool get isDefaultShiftValue;

  ///
  /// ゆらぎタイプはデフォルト値かどか
  ///
  bool get isDefaultTypeValue;

  ///
  /// ゆらぎタイプの文字色を取得する
  ///
  bool get needGrayTextColor;
}
