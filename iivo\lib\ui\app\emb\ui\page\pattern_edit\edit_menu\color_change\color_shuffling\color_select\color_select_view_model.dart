import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart' as xd;
import '../../../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../../../global_popup/global_popups/err_emb_no_available_color_for_shuf_in_org/err_emb_no_available_color_for_shuf_in_org_view_model.dart';
import '../../../../../../../model/color_change_model.dart';
import '../../../../../../../model/color_shuffling_model.dart';
import '../../../../../../../model/pattern_model.dart';
import '../../../../../../../model/thread_model.dart';
import '../../color_change_view_model.dart';
import 'color_select_interface.dart';

final colorSelectViewInfoProvider =
    AutoDisposeNotifierProvider<_ColorSelectViewInfo, void>(
        () => _ColorSelectViewInfo());

class _ColorSelectViewInfo extends AutoDisposeNotifier<void> {
  @override
  void build() {}

  BuildContext? _context;
  BuildContext get context => _context!;
  set context(value) => _context = value;
}

final colorSelectViewModelProvider = StateNotifierProvider.autoDispose<
    ColorSelectViewModelInterface, ColorSelectState>(
  (ref) => ColorSelectViewModel(ref),
);

class ColorSelectViewModel extends ColorSelectViewModelInterface {
  ColorSelectViewModel(Ref ref) : super(const ColorSelectState(), ref) {
    /// Model更新
    ColorShufflingModel()
        .setIsManual(ColorShufflingModel().getPinColorInfo().isNotEmpty);
    ColorShufflingModel().selectedColorPositionInfo = null;

    /// ViewModel更新
    state = state.copyWith(
      paletteColorList: ColorShufflingModel().getCurrentBrandColorList(),
      scrollController: ScrollController(),
      threadBrandName: ColorShufflingModel().getThreadBrandName(),
      shufflingMode: ColorShufflingModel().getShufflingModeName(l10n),
      isOneColorBase: ColorShufflingModel().isOneColorBase(),
    );

    update();
  }

  ///
  /// 言語取得する
  ///
  xd.AppLocalizations get l10n => xd.AppLocalizations.of(
      ref.read(colorSelectViewInfoProvider.notifier).context)!;

  ///
  /// ViewModel更新
  ///
  @override
  void update() {
    state = state.copyWith(
      isManual: ColorShufflingModel().getIsManual(),
      selectedColorPositionInfo:
          ColorShufflingModel().selectedColorPositionInfo,
      pinColorPositionInfo: ColorShufflingModel().getPinColorInfo(),
      threadBrandName: ColorShufflingModel().getThreadBrandName(),
      shufflingMode: ColorShufflingModel().getShufflingModeName(l10n),
      isEnglish: PatternModel().isEnglish,
    );
  }

  @override
  void onColorBlockClicked(int index) {
    if (state.isManual && state.pinColorPositionInfo.length < 6) {
      /// ログイン時に重複する色は何も処理しません
      final List<int> indexList =
          state.pinColorPositionInfo.values.map((info) => info.index).toList();
      if (indexList.contains(index)) {
        return;
      }

      if (ColorShufflingModel().isOneColorBase()) {
        ColorShufflingModel().removePinColor(0);
      }

      /// Model更新
      ColorShufflingModel().addPinColor(index);
      ColorShufflingModel().selectedColorPositionInfo =
          SelectedColorInfo(index: index);
      ColorShufflingModel().savePinColorIntoMemory();

      /// ViewModel更新
      update();
    }
  }

  @override
  void onPinColorBlockClicked(int key) {
    if (state.isManual) {
      /// Model更新
      ColorShufflingModel().selectedColorPositionInfo = SelectedColorInfo(
          index: ColorShufflingModel().getPinColorInfo()[key]!.index);
      ColorShufflingModel().removePinColor(key);

      /// ViewModel更新
      update();
    }
  }

  @override
  void onCancelButtonClicked(BuildContext context) {
    PopupNavigator.pop(context: context);
  }

  @override
  void onOKButtonClicked(BuildContext context) {
    ColorShufflingModel().isInColorShuffling = true;

    int thumbnailNum = 0;
    thumbnailNum = ColorShufflingModel().isOneColorBase()
        ? ColorShufflingModel().shuffleGradation()
        : ColorShufflingModel().shuffleRandom();

    if (thumbnailNum > 0) {
      if ((state.isManual && state.pinColorPositionInfo.isNotEmpty) ||
          !state.isManual) {
        PopupNavigator.pushNamed(
            context: context, nextRouteName: PopupEnum.colorThumbnail);
      }
    } else {
      GlobalPopupRoute().updateErrorState(
        nextRoute:
            GlobalPopupRouteEnum.ERR_EMB_NO_AVAILABLE_COLOR_FOR_SHUF_IN_ORG,
        arguments: ErrEmbNoAvailableColorForShufInOrgArgument(
          onOKButtonClicked: (_) => GlobalPopupRoute().resetErrorState(),
        ),
      );
    }
    ColorShufflingModel().isInColorShuffling = false;
  }

  @override
  void onAutoManualButtonClicked() {
    /// Model更新
    ColorShufflingModel().setIsManual(!ColorShufflingModel().getIsManual());

    /// ViewModel更新
    update();
  }

  @override
  void onSixColorItemClicked(int index) {
    if (state.isManual) {
      /// Model更新
      ColorShufflingModel().removePinColor(index);

      /// ViewModel更新
      update();
    }
  }

  @override
  List<Color> getSixPinColors() {
    List<int> pinColorKeys =
        ColorShufflingModel().getPinColorInfo().keys.toList();
    List<Color> result = [];
    for (var i = 0; i < 6; i++) {
      if (pinColorKeys.contains(i)) {
        result.add(ColorShufflingModel().getPinColorInfo()[i]!.color);
      } else {
        result.add(Colors.transparent);
      }
    }

    return result;
  }

  @override
  List<String> getSixPinColorName(BuildContext context) {
    List<String> result = [];
    bool isThreadColorDefault = ThreadModel.getThreadColor();

    for (var loopKey = 0; loopKey < 6; loopKey++) {
      if (ColorShufflingModel().getPinColorInfo().keys.contains(loopKey)) {
        if (isThreadColorDefault == true) {
          String threadCode = ColorShufflingModel()
              .getPinColorInfo()[loopKey]!
              .threadCode
              .toString()
              .padLeft(3, "0");
          result.add(
              "$threadCode\n${ColorShufflingModel().getThreadBrandName()}");
        } else {
          int index300 =
              ColorShufflingModel().getPinColorInfo()[loopKey]!.index300;
          int threadCode =
              ColorShufflingModel().getPinColorInfo()[loopKey]!.threadCode;
          result.add(ColorChangeModel()
              .getThreadColorName(index300, threadCode, isThreadColorDefault));
        }
      } else {
        result.add("");
      }
    }
    return result;
  }

  ///
  /// カラー配列の添字の最大値
  ///
  static const int _sixPinColorsIndexMax = 5;

  ///
  /// ボタンがTapDown時、音声を再生する
  ///
  @override
  void voiceControlFunction() {
    if (state.isManual) {
      int sixPinColorIndex = 0;
      for (var i = 0; i < getSixPinColors().length; i++) {
        if (getSixPinColors()[i] != Colors.transparent) {
          sixPinColorIndex++;
        }
      }
      if (sixPinColorIndex <= _sixPinColorsIndexMax) {
        SystemSoundPlayer().play(SystemSoundEnum.accept);
      } else {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
      }
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }
  }
}
