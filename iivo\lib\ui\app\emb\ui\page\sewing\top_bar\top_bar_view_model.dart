import 'dart:async';
import 'dart:ffi';
import 'dart:math';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import '../../../../../../../model/projector_model.dart';
import '../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/sewing_model.dart';
import '../../common_component/function_provider/projector_function_provider/projector_function_provider.dart';
import 'top_bar_view_interface.dart';

final topBarViewModelProvider =
    StateNotifierProvider.autoDispose<TopBarViewModelInterface, TopBarState>(
        (ref) => TopBarViewModel(ref));

class TopBarViewModel extends TopBarViewModelInterface
    with DeviceLibraryEventObserver {
  TopBarViewModel(this._ref) : super(const TopBarState(), _ref) {
    update();

    /// 縫製情報データ変更監視がONになっている
    openSewingListen();
  }

  /// 最大表示スレッド値
  final int _maxDisplayThreadValue = 999;

  /// providerのref
  final AutoDisposeStateNotifierProviderRef _ref;

  @override
  void update() {
    final appDisplayEmb = TpdLibrary().apiBinding.bPIFGetAppDisplayEmb();
    final patternModel = PatternModel();

    state = state.copyWith(
      frameIndex: appDisplayEmb.embSetting.ref.dispFrameType,
      isInch: patternModel.isUnitMm == false,
      heightValue: patternModel
          .changeValueToDisplay(appDisplayEmb.sewingInfo.ref.height),
      widthValue:
          patternModel.changeValueToDisplay(appDisplayEmb.sewingInfo.ref.width),
      currentNeedleCount: appDisplayEmb.sewingInfo.ref.needleCurrent.toString(),
      totalNeedleCount: appDisplayEmb.sewingInfo.ref.needleAll.toString(),
      currentThreadValue: min(
        appDisplayEmb.sewingInfo.ref.threadCurrent,
        _maxDisplayThreadValue,
      ).toString(),
      totalThreadValue: min(
        appDisplayEmb.sewingInfo.ref.threadAll,
        _maxDisplayThreadValue,
      ).toString(),
      currentTimeValue: _getDisplayCurrentTimeValue(
          appDisplayEmb.sewingInfo.ref.sewTimeCurrent),
      totalTimeValue: appDisplayEmb.sewingInfo.ref.sewTimeAll.toString(),
      displayPressFootType:
          PresserFoot.values[appDisplayEmb.embSetting.ref.presserFoot],
      isProjectorON: ProjectorModel().embProjector.isEmbProjectorViewOpen,
    );
  }

  @override
  Future<void> onPresserFootButtonClicked() async {
    /// タップするとプロジェクターの電源がオンになります
    /// ここは　ＸＰの　「BTN_N_PresserFoot_W」関連のキー関数を用意します。
    /// 暫定策時に削除しない
    final errCode = TpdLibrary().apiBinding.bpIFSendDisplayDataSync(
        BPIFSendKey.BPIF_SEND_EMB_KEYEMBEDITLEDPOINTERON);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 有効音を再生する
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    PresserFoot pressFootType = _getDisplayPressFootType();

    if (pressFootType == PresserFoot.EMB_PRESSER_FOOT_WPLUS) {
      return;
    } else if (pressFootType == PresserFoot.EMB_PRESSER_FOOT_W) {
      await ProjectorModel()
          .embProjector
          .openEmbProjector(initType: ProjectorInitType.wFooter);

      /// 投影画面を更新する
      final embProjectorFunction =
          _ref.read(embProjectorFunctionProvider.notifier);
      embProjectorFunction.maybeUpdateProjectorScreen(
          redrawEmbPattern: false, isWFoot: true);
    } else {
      /// do nothing
    }
  }

  @override
  void dispose() {
    super.dispose();

    /// データの停止
    _listener.close();
  }

  ///
  /// 縫製情報データ変更監視がONになっている
  ///
  void openSewingListen() {
    /// 現在の押え金交換用リスナー
    _listener = _ref.stateListen(
      appDisplayEmbStateProvider
          .select((value) => value.embSetting.ref.presserFoot),
      (_, nextState) {
        state = state.copyWith(
          displayPressFootType: PresserFoot.values[nextState],
        );
        if (PresserFoot.values[nextState] ==
            PresserFoot.EMB_PRESSER_FOOT_WPLUS) {
          /// W押えのプロジェクションがオンになっている場合は、プロジェクションをオフにします
          if (ProjectorModel().embProjector.isEmbWFooterProjectorOpen() ==
              true) {
            ProjectorModel().embProjector.closeEmbProjector();
          } else {
            /// Do Nothing
          }
        } else {
          /// LEDPointerを閉じます
          DeviceLibrary().apiBinding.closeLedPtSetting();
        }
      },
    );

    _ref.stateListen(
      appDisplayEmbStateProvider.select((value) => (
            value.embSetting.ref.dispFrameType,
            value.sewingInfo.ref.height,
            value.sewingInfo.ref.width,
            value.sewingInfo.ref.needleCurrent,
            value.sewingInfo.ref.needleAll,
            value.sewingInfo.ref.threadCurrent,
            value.sewingInfo.ref.threadAll,
            value.sewingInfo.ref.sewTimeCurrent,
            value.sewingInfo.ref.sewTimeAll,
          )),
      (_, __) {
        update();
      },
    );
  }

  ///
  /// W+押さえの接続状態を取得する
  ///
  PresserFoot _getDisplayPressFootType() =>
      PresserFoot.values[SewingModel().getPressFootType()];

  ///
  /// 今の縫製時間
  ///
  String _getDisplayCurrentTimeValue(int sewingTime) {
    SewingModel().setCurrentTime(sewingTime);

    return sewingTime.toString();
  }

  ///
  /// データ更新ページをリッスンする
  ///
  late final ProviderSubscription _listener;
}
