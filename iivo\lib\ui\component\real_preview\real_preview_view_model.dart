import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../app/emb/model/area_model.dart';
import '../../app/emb/model/pattern_model.dart';
import '../../app/emb/model/scan_model.dart';
import '../../app/emb/model/select_information_model.dart';
import '../../app/emb/model/sewing_model.dart';
import '../../global_popup/global_popups/err_sewing_over/err_sewing_over_view_model.dart'
    show hideRealPreviewBackgroundScanFunc;
import '../../page_route/page_route.dart';
import 'component/real_preview_controller.dart';
import 'real_preview_view_interface.dart';

final realPreviewViewModelProvider = StateNotifierProvider.family.autoDispose<
    RealPreviewViewModelInterface,
    RealPreviewState,
    RealPreviewType>((ref, type) => RealPreviewViewModel(ref, type));

class RealPreviewViewModel extends RealPreviewViewModelInterface
    with DeviceLibraryEventObserver {
  RealPreviewViewModel(Ref ref, RealPreviewType type)
      : super(const RealPreviewState(), ref, type) {
    var result =
        EmbLibrary().apiBinding.getRealImageLight(type == RealPreviewType.emb);
    realPreviewController = RealPreviewController(
        type: type, stitchData: result.needlePos, onStop: _stopAnimation);
    bool isPressFootDisplay = state.isPressFootDisplay;
    RealPreviewTableType frameTableState = state.frameTableState;
    RealPreviewTableType stitchSimulatorTableState =
        state.stitchSimulatorTableState;
    List<ButtonState> frameButtonStateList =
        List.from(state.frameButtonStateList);
    PresserFoot displayPressFootType = state.displayPressFootType;

    List<FrameSize> embFrameSizeTypeList = [
      FrameSizeType.FRAME_297_465,
      FrameSizeType.FRAME_272_272,
      FrameSizeType.FRAME_130_180,
      FrameSizeType.FRAME_100_100,
    ];

    /// すべての縫製が完了した後、背景画像を閉じてください
    hideRealPreviewBackgroundScanFunc = () {
      ScanModel().setBackgroundShowStatus(false);
      realPreviewController.hideBackgroundImage();
    };

    /// 選択できないフレームをグレー表示
    for (int index = 0; index < embFrameSizeTypeList.length; index++) {
      if (AreaModel.checkPatternArea(
            embFrameSizeTypeList[index],
            realPreviewController.patternRectArea,
            const SSPoint(X: 0, Y: 0),
          ) ==
          false) {
        frameButtonStateList[index] = ButtonState.disable;
      }
    }

    /// 異なるモードのreal preview表示設定
    switch (PatternModel().realPreviewDisplayType) {
      case RealPreviewDisplayType.information:
        isPressFootDisplay = false;
        frameTableState = RealPreviewTableType.selected;
        stitchSimulatorTableState = RealPreviewTableType.hide;
        break;
      case RealPreviewDisplayType.sewing:
        isPressFootDisplay = true;
        frameTableState = RealPreviewTableType.hide;
        stitchSimulatorTableState = RealPreviewTableType.hide;
        displayPressFootType =
            PresserFoot.values[SewingModel().getPressFootType()];
        break;
      case RealPreviewDisplayType.patternSelect:
      case RealPreviewDisplayType.patternEdit:
      default:
        isPressFootDisplay = false;
        frameTableState = RealPreviewTableType.selected;
        stitchSimulatorTableState = RealPreviewTableType.unselected;
        break;
    }

    // RealPreview の ビジー状態の監視
    final void Function() removeListener =
        realPreviewController.addIsBusyListener((isBusyNow) {
      Future(() {
        if (mounted == false) {
          return;
        }
        state = state.copyWith(isBusyDrawing: isBusyNow);
      });
    });
    ref.onDispose(removeListener);

    /// view更新
    state = state.copyWith(
      isZoomOut: true,
      displayPressFootType: displayPressFootType,
      frameButtonStateList: frameButtonStateList,
      isPressFootDisplay: isPressFootDisplay,
      frameTableState: frameTableState,
      stitchSimulatorTableState: stitchSimulatorTableState,
      speedButtonStateList: _getSpeedButtonStateList(),
    );
  }

  @override
  void dispose() {
    super.dispose();
    hideRealPreviewBackgroundScanFunc = null;
  }

  ///
  /// 縮小ボタンのクリック関数
  ///
  @override
  void onZoomOutButtonClick() {
    if (realPreviewController.isBusy()) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 非選択状態の場合はクリック有効
    if (state.isZoomOut) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 選択されていないキーステータスリストを取得する
    List<ButtonState> frameButtonStateList =
        _changeListToUnselect(state.frameButtonStateList);
    frameButtonStateList[state.selectedFrame.index] = ButtonState.select;

    /// view更新
    realPreviewController.zoomOut();
    state = state.copyWith(
      isZoomOut: true,
      frameButtonStateList: frameButtonStateList,
    );
  }

  ///
  /// 拡大ボタンのクリック関数
  ///
  @override
  void onZoomInButtonClick() {
    if (realPreviewController.isBusy()) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 非選択状態の場合はクリック有効
    if (state.isZoomOut == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// view更新
    realPreviewController.zoomIn();
    state = state.copyWith(
      isZoomOut: false,
      frameButtonStateList: _changeListToUnselect(state.frameButtonStateList),
    );
  }

  ///
  /// Frame Tableボタンのクリック関数
  ///
  @override
  void onFrameTableButtonClick() {
    if (realPreviewController.isBusy()) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 非選択状態の場合はクリック有効
    if (state.frameTableState != RealPreviewTableType.unselected) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// view更新
    realPreviewController.setSpeed(RealPreviewSpeed.low);
    realPreviewController.stop();

    state = state.copyWith(
      frameTableState: RealPreviewTableType.selected,
      stitchSimulatorTableState:
          state.stitchSimulatorTableState == RealPreviewTableType.hide
              ? RealPreviewTableType.hide
              : RealPreviewTableType.unselected,
      animationPlayState: realPreviewController.animationPlayState,
      speedButtonStateList: _getSpeedButtonStateList(),
    );
  }

  ///
  /// PrePlayボタンのクリック関数
  ///
  @override
  void onRrePlayButtonClick() {
    if (realPreviewController.isBusy()) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// view更新
    realPreviewController.stop();
    state = state.copyWith(
        animationPlayState: realPreviewController.animationPlayState);
  }

  ///
  /// Frameボタンのクリック関数
  ///
  @override
  void onFrameButtonClick(RealPreviewFrameType getEmbFrameType) {
    if (realPreviewController.isBusy()) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 非選択状態の場合はクリック有効
    if (state.frameButtonStateList[getEmbFrameType.index] !=
        ButtonState.normal) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    realPreviewController.changeFrameSize(getEmbFrameType);

    /// 選択されていないキーステータスリストを取得する
    /// クリックしたキーを選択状態に設定
    List<ButtonState> frameButtonStateList =
        _changeListToUnselect(state.frameButtonStateList);
    frameButtonStateList[getEmbFrameType.index] = ButtonState.select;

    if (state.isZoomOut == false) {
      realPreviewController.zoomOut();
    } else {
      /// Do Nothing
    }

    /// view更新
    state = state.copyWith(
      isZoomOut: true,
      selectedFrame: getEmbFrameType,
      frameButtonStateList: frameButtonStateList,
    );
  }

  ///
  /// stitch simulatorボタンのクリック関数
  ///
  @override
  void onStitchSimulatorTableButtonClick() {
    if (realPreviewController.isBusy()) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// FRAME不会 invalid
    /// Speed会 invalid
    /// 非選択状態の場合はクリック有効
    if (state.stitchSimulatorTableState != RealPreviewTableType.unselected) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// view更新
    realPreviewController.stop();
    state = state.copyWith(
        frameTableState: state.frameTableState == RealPreviewTableType.hide
            ? RealPreviewTableType.hide
            : RealPreviewTableType.unselected,
        stitchSimulatorTableState: RealPreviewTableType.selected);
  }

  ///
  /// Speedボタンのクリック関数
  ///
  @override
  void onSpeedButtonClick(RealPreviewSpeed speedType) {
    if (realPreviewController.isBusy()) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 非選択状態の場合はクリック有効
    if (state.speedButtonStateList[speedType.index] != ButtonState.normal) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// view更新
    realPreviewController.setSpeed(speedType);
    state = state.copyWith(speedButtonStateList: _getSpeedButtonStateList());
  }

  ///
  /// 再生キー / 一時停止キーボタンのクリック関数
  ///
  @override
  void onPlayButtonClick() {
    if (realPreviewController.isBusy()) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// view更新
    if (realPreviewController.animationPlayState != RealPreviewAction.play) {
      realPreviewController.play();
    } else {
      realPreviewController.pause();
    }
    state = state.copyWith(
        animationPlayState: realPreviewController.animationPlayState);
  }

  @override
  FlexNum get printBackGroundFlex {
    int index =
        SelectInformationModel().printAndStitchType == PrintAndStitchType.whale
            ? 0
            : 1;
    return state.isZoomOut
        ? _zoomOutPrintBackGroundFlex[index]
        : _zoomInPrintBackGroundFlex[index];
  }

  ///
  /// 選択されていないキーステータスリストを取得する
  ///
  List<ButtonState> _changeListToUnselect(List<ButtonState> buttonStateList) {
    List<ButtonState> list = List.from(buttonStateList);

    /// 選択したすべてのキーを非選択に設定
    for (int index = 0; index < list.length; index++) {
      if (list[index] == ButtonState.select) {
        list[index] = ButtonState.normal;
      }
    }
    return list;
  }

  ///
  /// アニメーション停止時にページを更新するコールバック関数
  ///
  void _stopAnimation() {
    state = state.copyWith(
        animationPlayState: realPreviewController.animationPlayState);
  }

  ///
  /// SpeedButtonStateListを取得
  ///
  List<ButtonState> _getSpeedButtonStateList() => List.generate(
      3,
      (index) => realPreviewController.speed.index == index
          ? ButtonState.select
          : ButtonState.normal);

  ///
  /// プリント&ステッチのReal Preview画面(ノーマル)のJpegサイズ
  ///
  final List<FlexNum> _zoomOutPrintBackGroundFlex = [
    /// クジラ
    FlexNum(
      left: 210,
      centerX: 382,
      right: 208,
      top: 309,
      centerY: 562,
      bottom: 409,
    ),

    /// バンビ
    FlexNum(
      left: 260,
      centerX: 290,
      right: 250,
      top: 377,
      centerY: 430,
      bottom: 473,
    ),
  ];

  ///
  /// プリント&ステッチのReal Preview画面(マックス)のJpegサイズ
  ///
  final List<FlexNum> _zoomInPrintBackGroundFlex = [
    /// クジラ
    FlexNum(
      left: 50,
      centerX: 693,
      right: 47,
      top: 77,
      centerY: 1033,
      bottom: 170,
    ),

    /// バンビ
    FlexNum(
      left: 40,
      centerX: 750,
      right: 10,
      top: 45,
      centerY: 1120,
      bottom: 115,
    ),
  ];

  @override
  void onCloseButtonClick() {
    realPreviewController.dispose();
    PagesRoute().pop();
  }
}
