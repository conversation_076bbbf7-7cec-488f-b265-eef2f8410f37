import 'dart:ffi';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../ui/app/emb/model/photo_stitch/photo_stitch_model.dart';
import '../../../ui/app/home/<USER>/component/home_header/bwd_popup/bwd_popup_view_model.dart';

import '../app_display_state_provider.dart';

void listenBwdStateAndShowPopup(AutoDisposeStateNotifierProviderRef ref) {
  ref.listen(
    appDisplayGlobalStateProvider
        .select((value) => value.bwdPanelState.ref.IsOn),
    (previous, nextState) {
      if (PhotoStitchModel().isPhotoStitchEntered) {
        return;
      }

      if (nextState != ENUM_LIB.FALSE) {
        openBwdPopup();
      } else {
        closeBwdPopup();
      }
    },
  );
}
