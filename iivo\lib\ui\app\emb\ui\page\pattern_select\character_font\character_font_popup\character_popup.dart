import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import 'character_popup_view_model.dart';

class CharacterPopup extends ConsumerStatefulWidget {
  const CharacterPopup({super.key, this.categoryName = "", this.outIndex = 0});

  final String categoryName;
  final int outIndex;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _CharacterPopupState();
}

class _CharacterPopupState extends ConsumerState<CharacterPopup> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;

    final viewModel = ref.read(characterPopupViewModelProvider.notifier);
    ref.watch(characterPopupViewModelProvider);

    return Column(
      children: [
        const Spacer(flex: 71),
        Expanded(
          flex: 1148,
          child: Material(
            color: Colors.transparent,
            child: Row(
              children: [
                const Spacer(flex: 6),
                Expanded(
                  flex: 788,
                  child: Column(
                    children: [
                      const Spacer(flex: 8),
                      Expanded(
                        flex: 1132,
                        child: Stack(
                          children: [
                            const pic_popup_size788x1132(),
                            Column(
                              children: [
                                const Spacer(flex: 20),

                                /// トップテキストと画像
                                viewModel.isExclusiveScriptType(widget.outIndex)
                                    ? Expanded(
                                        flex: 92,
                                        child: Row(
                                          children: [
                                            const Spacer(flex: 158),
                                            Expanded(
                                              flex: 472,
                                              child: viewModel
                                                  .getCharacterFontImage(
                                                      widget.outIndex),
                                            ),
                                            const Spacer(flex: 158),
                                          ],
                                        ),
                                      )
                                    : Expanded(
                                        flex: 92,
                                        child: Row(
                                          children: [
                                            const Spacer(flex: 237),
                                            Expanded(
                                              flex: 51,
                                              child: grp_str_text_no(
                                                text: widget.categoryName,
                                              ),
                                            ),
                                            const Spacer(flex: 28),
                                            Expanded(
                                              flex: 158,
                                              child: viewModel
                                                  .getCharacterFontImage(
                                                      widget.outIndex),
                                            ),
                                            const Spacer(flex: 314),
                                          ],
                                        ),
                                      ),

                                const Spacer(flex: 20),

                                /// ミドルリスト
                                Expanded(
                                  flex: 886,
                                  child: Row(
                                    children: [
                                      const Spacer(flex: 12),
                                      Expanded(
                                        flex: 750,
                                        child: Column(
                                          children: [
                                            const Spacer(flex: 32),
                                            Expanded(
                                              flex: 758,
                                              child: LayoutBuilder(
                                                builder:
                                                    (context, constraints) =>
                                                        Container(
                                                  width: constraints.maxWidth,
                                                  height: constraints.maxHeight,
                                                  color: Colors.transparent,
                                                  child: SingleChildScrollView(
                                                    controller: viewModel
                                                        .scrollController,
                                                    child: viewModel
                                                        .getCharacterFontPreviewImage(
                                                            widget.outIndex),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const Spacer(flex: 13),
                                      Expanded(
                                        flex: 8,
                                        child: CustomScrollbar(
                                          controller:
                                              viewModel.scrollController,
                                          visibilityWhenScrollFull: false,
                                        ),
                                      ),
                                      const Spacer(flex: 5),
                                    ],
                                  ),
                                ),

                                const Spacer(flex: 22),

                                /// "Cancel"キー
                                Expanded(
                                  flex: 80,
                                  child: Row(
                                    children: [
                                      const Spacer(flex: 12),
                                      Expanded(
                                        flex: 152,
                                        child: grp_btn_cancel(
                                          onTap: () => viewModel
                                              .onCancelButtonClicked(context),
                                          text: l10n.icon_cancel,
                                        ),
                                      ),
                                      const Spacer(flex: 460),
                                      Expanded(
                                        flex: 152,
                                        child: grp_btn_ok(
                                          onTap: () =>
                                              viewModel.onOKButtonClicked(
                                                  context, widget.outIndex),
                                          buttonText: l10n.icon_ok,
                                        ),
                                      ),
                                      const Spacer(flex: 12),
                                    ],
                                  ),
                                ),
                                const Spacer(flex: 12),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const Spacer(flex: 8),
                    ],
                  ),
                ),
                const Spacer(flex: 6),
              ],
            ),
          ),
        ),
        const Spacer(flex: 61),
      ],
    );
  }
}
