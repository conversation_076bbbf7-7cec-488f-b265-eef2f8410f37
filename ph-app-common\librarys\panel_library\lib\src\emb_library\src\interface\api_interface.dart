import 'dart:typed_data';

import 'package:flutter/material.dart';

import '../../../../panel_library.dart';
import '../../../library_manage_meta/emb_library_result.dart';
import '../../../library_manage_meta/library_manage_meta.dart';

abstract class EmbLibraryAPIInterface
    with _ExtensionFunction, DeviceLibraryEventInterface {
  ///
  /// 刺繍モード初期化
  ///
  /// initEmbは openEmbModeがすでに呼び出し
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError initEmb();

  ///
  /// 刺繍モードを終了後処理をする
  ///
  /// 刺繍モードを抜けHome画面に戻る際にコールする。
  /// MDCモードに一時的に遷移する場合はコールしない。
  ///
  /// 戻り値 : なし
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  void closeEmbMode();

  ///
  /// 出力するBMPの色数を指定する
  ///
  /// initEmbroideryの中でBMP24BITを指定しているので、途中で変更することがなければ、本関数を使用する必要はない。
  ///
  /// 引数[in] :
  /// - [int] colorDepth: 24bit Color BMP　か 16bit Color BMP　を指定する
  ///              |----- BMP24BIT 0
  ///              |----- BMP16BIT 1
  ///
  @EmbApiResult(
    EmbApiFunction.emb_output_setting,
    ApiStatus.notUse,
    "",
  )
  void setOutColorDepth(int colorDepth);

  ///
  /// 出力されるBMPの色数を取得する
  ///
  /// 戻り値   :
  /// - [int] : BMP色数
  ///   |----- BMP24BIT 0
  ///   |----- BMP16BIT 1
  ///
  @EmbApiResult(
    EmbApiFunction.emb_output_setting,
    ApiStatus.notUse,
    "",
  )
  int getOutColorDepth();

  ///
  /// 模様選択
  ///
  /// 模様のカテゴリー、模様番号を指定することによりグループデータが作成され
  /// 返値にそのハンドルを返す。ハンドルがnullptrの場合は選択失敗。
  ///
  /// 引数[in]:
  /// - [int] embIndex: 模様アイコン番号(0~)
  /// - [int] category: カテゴリー
  /// - [EmbBHSizeKind] bhSize: ボタンホールサイズ(categoryがBRO_EMB_BHの時のみ有効)
  ///                           BHの場合はXSサイズ:0～Lサイズ:4となっている
  ///
  /// 戻り値   :
  /// - [EmbLibraryError] : エラーコード
  ///                     |-----EMB_NEWGROP_ERR: 空きグループ取得失敗
  ///                     |-----EMB_GRPHANDLE_ERR: ハンドルなし
  ///                     |-----EMB_INVALIDPARAM_ERR: 模様選択失敗
  /// - [MemHandle] : 模様イメージのグループハンドル
  ///
  @EmbApiResult(
    EmbApiFunction.embSelect_Category_Select,
    ApiStatus.doUiImp,
    "",
  )
  ({EmbLibraryError errorCode, MemHandle handle}) selectEmb(
      int embIndex, int category, EmbBHSizeKind bhSize);

  ///
  /// キルトイサムネイルイメージの取得
  ///
  /// キルトイサムネイルイメージの取得(リストからアイコン選択時に表示するイメージ)
  ///
  /// 引数[in]:
  /// - [int] embIndex: キルト刺繍ID(0〜19)
  /// - [int] category: キルト刺繍カテゴリー(0〜2)
  ///
  /// 戻り値   :
  /// - [EmbLibraryError] : エラーコード
  /// - [Image] : イメージ
  /// - [List<ThreadInfo>] : 模様糸情報を格納する配列のポインター
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, Image image, List<ThreadInfo> threadInfoList})
      getQuiltThumbnailImage(int embQuiltIndex, int quiltCategory);

  ///
  /// キルト糸色情報取得
  ///
  /// 引数[in]:
  /// - [int] colorNum: 短冊番号　0 ～ 1
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [EmbColorGr] : キルト糸色情報
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.ngLib,
    """
    API は間違った値を返し、stdThreadCode は<BrandColorInfo>リスト内の b の値を返します;
    apiの戻り値に問題があり、paletteBrandの戻り値は常に13です;
    apiの戻り値に問題があり、paletteCodeの戻り値は常に0です;
    """,
  )
  ({EmbLibraryError errorCode, EmbColorGr colorInfo}) getQuiltColorInfo(
      int colorNum);

  ///
  /// スティップリングのキャンセル
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.libMaintenance,
    """
    """,
  )
  EmbLibraryError cancelStipple();

  ///
  /// スティップリング作成完了
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.stipple,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError completeStipple();

  ///
  /// ボーダー処理の事前チェック
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.border,
    ApiStatus.libMaintenance,
    """
    """,
  )
  EmbLibraryError checkBeforeBorderProc();

  ///
  /// 糸印有り無しを確認する。スティップリング作成の前にチェックする
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.border,
    ApiStatus.libMaintenance,
    """
    """,
  )
  EmbLibraryError checkBorderMarkStipple();

  ///
  /// TODO:APIの作用を確認してください。
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.border,
    ApiStatus.libMaintenance,
    """
    """,
  )
  EmbLibraryError checkBeforeEasyStippleMakeProc();

  ///
  /// 外部メモリに保存できるかどうかを確認
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.libMaintenance,
    """
    """,
  )
  EmbLibraryError isEnabledSevedToExternalMemory();

  ///
  /// 内蔵メモリに保存できるかどうかを確認
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.libMaintenance,
    """
    """,
  )
  EmbLibraryError isEnabledSevedToInternalMemory();

  ///
  /// TODO:APIの作用を確認してください。
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.libMaintenance,
    """
    """,
  )
  EmbLibraryError checkMemoryForEmbSewing();

  ///
  /// TODO:APIの作用を確認してください。
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.libMaintenance,
    """
    """,
  )
  EmbLibraryError checkMemoryForEmbEdit();

  ///
  /// キルトイメージの取得
  ///
  /// キルトイサムネイルイメージの取得(リストからアイコン選択時に表示するイメージ)
  ///
  /// 引数[in]:
  /// - [int] embIndex: キルト刺繍ID(0〜19)
  /// - [int] category: キルト刺繍カテゴリー(0〜2)
  /// - [int] color: 糸色
  /// - [int] flip: 反転種類
  ///
  /// 戻り値   :
  /// - [EmbLibraryError] : エラーコード
  /// - [Image] : イメージ出力先ファイルパス
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, Image image}) getQuiltImage(
      int embQuiltIndex, int quiltCategory, int color, int flip);

  ///
  /// キルトイプレビューイメージの取得(4分割)
  ///
  /// changeQuiltColorRGBで糸色変更後、本関数を呼ぶことで、色が変更されたイメージを取り出すことができる
  ///
  /// 引数[in]:
  /// - [bool] dividingLine: 分割線(赤)描画
  ///
  /// 戻り値   :
  /// - [EmbLibraryError] : エラーコード
  /// - [int]    : 分割数(1:分割なし,2:上下,3:左右,4:4分割) 0はエラー
  /// - [Image] : イメージ
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, int division, Image image}) getQuiltPreviewImage(
      bool dividingLine);

  ///
  /// キルトイプレビューイメージの取得(1方向)
  ///
  /// changeQuiltColorRGBで糸色変更後、本関数を呼ぶことで、色が変更されたイメージを取り出すことができる
  ///
  /// 引数[in]:
  /// - [bool ] frameBorder: 枠線(赤)描画
  ///
  /// 戻り値   :
  /// - [EmbLibraryError] : エラーコード
  /// - [int]    : 分割数 0はエラー
  /// - [Image] : イメージ
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, int division, Image image})
      getQuiltPreviewExtensionImage(bool frameBorder);

  ///
  /// キルトイプレビューイメージの取得(6角形)
  ///
  /// サイズを示す矢印入り。changeQuiltColorRGBで糸色変更後、本関数を呼ぶことで、色が変更されたイメージを取り出すことができる。
  ///
  /// 引数[in]:
  /// - [bool ] frameBorder: 枠線(赤)描画
  ///
  /// 戻り値   :
  /// - [EmbLibraryError] : エラーコード
  /// - [int]    : 分割数 0はエラー
  /// - [Image]  : イメージ
  ///
  @EmbApiResult(
    EmbApiFunction.quiltHexagon,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, int division, Image image})
      getQuiltPreviewHexagonImage(bool frameBorder);

  ///
  /// キルトサイズ入力上限下限値取得
  ///
  /// 引数[in]:
  /// - [QuiltSplitType] quiltType: キルトタイプ
  /// - [EmbQuiltSashesFrameType] frameType: 刺繍枠種別
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]     : エラーコード
  /// - [QuiltSashesLimit] : 入力上下限値
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, QuiltSashesLimit limit}) getQuiltSizeLimit(
      QuiltSplitType quiltType, EmbQuiltSashesFrameType frameType);

  ///
  /// キルトサイズ幅設定
  ///
  /// 幅入力により、高さ、バンド幅の上下限値が変更する場合があるので入力上下限値
  ///
  /// 引数[in]:
  /// - [int] wValue: 幅
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]     : エラーコード
  /// - [QuiltSashesLimit] : 入力上下限値
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, QuiltSashesLimit limit}) setQuiltWidth(
      int widthValue);

  ///
  /// キルトサイズ高さ設定
  ///
  /// 高さ入力により、幅、バンド幅の上下限値が変更する場合があるので入力上下限値を再取得すること。
  ///
  /// 引数[in]:
  /// - [int] hValue: 高さ
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]     : エラーコード
  /// - [QuiltSashesLimit] : 入力上下限値
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, QuiltSashesLimit limit}) setQuiltHeight(
      int hValue);

  ///
  /// キルトサイズバンド幅設定
  ///
  /// バンド幅を設定する。
  ///
  /// 引数[in]:
  /// - [int] bValue: バンド幅
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]     : エラーコード
  /// - [QuiltSashesLimit] : 入力上下限値
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, QuiltSashesLimit limit}) setQuiltBand(
      int bValue);

  ///
  /// キルトデータ保存
  ///
  /// 引数[in]:
  /// - [QuiltSplitType] bValue: キルトタイプ
  /// - [String]       fileName: 保存先ファイルのパスおよびファイル名
  /// - [String]        sewType: 裁縫タイプ
  ///
  /// 戻り値   :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.doLibImp,
    "弹出popup的页面有误",
  )
  EmbLibraryError saveQuiltData(
      QuiltSplitType type, int sewType, String fileName);

  ///
  /// Edge-to-Edgeキルトサイズ反転オプションイメージの取得
  ///
  /// 引数[in]:
  /// - [int] embQuiltIndex: キルト刺繍ID(0〜9)
  /// - [int] flip: 反転種類
  /// - [bool] frame: 枠線
  ///
  /// 戻り値:
  /// - [EmbLibraryError] : エラーコード
  /// - [Image]          : イメージ
  ///
  @EmbApiResult(
    EmbApiFunction.quiltEdgetoEdge,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, Image image}) getFlipPart(
      int embQuiltIndex, int flip, bool frame);

  ///
  /// Edge-to-Edgeキルトの行、列、サイズの取得
  ///
  /// 「刺繍枠選択」に従って、キルトの行と列の数と模様のサイズを計算します
  ///
  /// 戻り値:
  /// - [int] : 縦の段数
  /// - [int] : 横の個数
  /// - [EmbroiderySize ] : 模様の縦サイズ
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.quiltEdgetoEdge,
    ApiStatus.ok,
    "",
  )
  ({
    int row,
    int piece,
    EmbroiderySize embroiderySize,
    EmbLibraryError errorCode
  }) getSewingInfoEdgeToEdge();

  ///
  /// キルトデータ保存
  ///
  /// 編集状態のデータを指定パスに保存する。PHXファイルを保存する。
  ///
  /// 引数[in]:
  /// - [ThumbnailSize] size: データファイルのパス
  /// - [String]        file: サムネイルサイズ
  /// - [bool]          transparent: 透明かどうか
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  /// - [SavedFileInfo] : ファイル情報
  /// - [Image]        : イメージ
  ///
  @EmbApiResult(
    EmbApiFunction.embSelect_FileRead,
    ApiStatus.ngLib,
    """
    transparentが無効に設定され、結果の画像には常に背景色が設定されます
    """,
  )
  ({EmbLibraryError errorCode, SavedFileInfo? fileInfo, Image? image})
      getThumbnailSavedFile(String file, ThumbnailSize size, bool transparent);

  ///
  /// ファイルデータの分割サムネイル取得
  ///
  /// 拡張子のないファイルは指定不可。
  ///
  /// 引数[in]:
  /// - [ThumbnailSize]  bValue: データファイルのパス
  /// - [String]           file: サムネイルサイズ
  /// - [int]             index: パーツ番号(0~)
  /// - [transparent]     transparent: 透明かどうか
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  /// - [SavedFileInfo] : ファイル情報
  /// - [Image]        : イメージ
  ///
  @EmbApiResult(
    EmbApiFunction.embSelect_FileRead,
    ApiStatus.ngLib,
    """
    transparentが無効に設定され、結果の画像には常に背景色が設定されます
    """,
  )
  ({EmbLibraryError errorCode, SavedFileInfo? fileInfo, Image? image})
      getThumbnailPartsSavedFile(
          String file, ThumbnailSize size, int index, bool transparent);

  ///
  /// ファイルデータの分割数を求める
  ///
  /// 拡張子のないファイルは指定不可。
  ///
  /// 引数[in]:
  /// - [String]    file: サムネイルサイズ
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  /// - [int]           : 分割されたパーツ数
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证",
  )
  ({EmbLibraryError errorCode, int num}) getPartsNumSavedFile(String file);

  ///
  /// ファイルデータを選択
  ///
  /// 拡張子のないファイルは指定不可。
  ///
  /// 引数[in]:
  /// - [String]    file: ファイルのパス
  /// - [int]    dataSource:
  /// - [int]    restriction:
  /// - [int]    partsNo: パーツ番号
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  /// - [MemHandle]        : 模様イメージのグループハンドル
  ///
  @EmbApiResult(
    EmbApiFunction.embSelect_Login,
    ApiStatus.ngLib,
    """
    1.自動つなぎ模様を選択すると、プレビューの模様maskに間違った範囲が表示されます

    2.メモリ内の画像にログインすると、次のエラーが表示されます。
    [ERROR] | 14:51:34 849ms | Emb Library | Function Error: 58
    """,
  )
  ({EmbLibraryError errorCode, List<MemHandle> handles}) selectEditFile(
      String file, int dataSource, int restriction, int partsNo);

  ///
  /// 刺繍模様グループパターン情報取得
  ///
  /// 引数[in]:
  /// - [MemHandle] groupH: グループハンドル
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  /// - [EmbGrp] : 模様イメージグループ情報を格納する配列のポインター
  /// - [EmbPtrnInfo] : グループに含まれるパターン情報を格納する配列のポインター
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证,需要验证所有模样,多测试",
  )
  ({
    EmbLibraryError errorCode,
    EmbInfo embInfo,
    EmbGrp embGrp,
    EmbPtrnInfo embPatternInfo
  }) getEmbGrpInfo(MemHandle groupH);

  ///
  /// 模様糸情報取得
  ///
  /// 引数[in]:
  /// - [MemHandle] groupH: グループハンドル
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]     : エラーコード
  /// - [List<ThreadInfo>] : 模様糸情報を格納する配列のポインター
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngLib,
    "どの色を設定しても、返されるindex300の値は65535、stdCode と threadCode の両方が 0 です",
  )
  ({EmbLibraryError errorCode, List<ThreadInfo> threadInfoList})
      getEmbEditThreadInfo(MemHandle groupH);

  ///
  /// 縫製画面における全模様の糸色情報
  ///
  /// 引数[in]:
  /// - ない
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]     : エラーコード
  /// - [List<ThreadInfo>] : 模様糸情報を格納する配列のポインター
  /// - [int] : threadInfoの要素数
  /// - [int] : 今の短冊の位置　縫製前は先頭（０）
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "预计要在缝纫中使用,之前使用时NG",
  )
  ({
    EmbLibraryError errorCode,
    List<ThreadInfo> threadInfoList,
    int threadInfoNum,
    int threadInfoCurrentIndex
  }) getEmbSewingThreadInfoAll();

  ///
  /// 模様追加前処理
  ///
  /// リソースの開放とイメージ再作成要求
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doTest,
    "等待实机验证,需要弹出ERR_NEEDLE_UP",
  )
  EmbLibraryError prepareAddEmb();

  ///
  /// 模様削除
  ///
  /// カレントグループに対して行う。複数グループ選択状態も対応。
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doTest,
    "等待实机验证,需要弹出ERR_NEEDLE_UP",
  )
  EmbLibraryError deleteEmb();

  ///
  /// 模様保存
  ///
  /// 編集状態のデータを指定パスに保存する。
  ///
  /// 引数[in]:
  /// - [String] fileName: 保存先ファイルのパスおよびファイル名
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证",
  )
  EmbLibraryError saveEmbData(String fileName);

  ///
  /// 模様タイプ確認
  ///
  /// checkEmbPatternType()は画面のモードが刺しゅう編集になっている場合にのみ正しく動作するように実装されている
  /// 必ず刺しゅう編集に遷移後、使用します。
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [bool] : 確認結果
  ///
  @EmbApiResult(
    EmbApiFunction.embCheckPattern,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, bool result}) checkEmbPatternType(
      MemHandle groupH, EmbPatternType embType);

  ///
  /// 模様パターン情報取得
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [EmbPtrnInfo] : パターン情報
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.notUse,
    "暂定不使用",
  )
  ({EmbLibraryError errorCode, EmbPtrnInfo embPtrnInfo}) getEmbGrpPatternInfo(
      MemHandle groupH);

  ///
  /// パーツ(短冊)の選択
  ///
  /// パーツ(短冊)の選択(糸色変更)
  ///
  /// 引数[in]:
  /// - [MemHandle] gHandle: 模様のグループハンドル
  /// - [int]       index  : パーツのインデックス
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK",
  )
  EmbLibraryError selectPartsInGroupColorChange(MemHandle gHandle, int index);

  ///
  /// 糸色変更　RGB指定
  ///
  /// 糸色変更画面へ遷移する
  ///
  ///  changeColorは使わないでください。
  ///  changeColorはすべてchangeColorByPaletteに置き換えてください。
  ///
  /// 引数[in]:
  /// - [int]  r: 赤
  /// - [int]  g: 緑
  /// - [int]  b: 青
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.notUse,
    "暂定OK,有 issue,修改后获取threadinfo有问题,UI显示tereadinfo有问题,而且品牌没切过去",
  )
  EmbLibraryError changeColor(int r, int g, int b);

  ///
  /// 糸色変更　カラーパレットの選択
  ///
  /// 引数[in]:
  /// - [ThreadBrandName]  brandCode:糸ブランドコード
  /// - [int]  index: パレットインデックス
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,有 issue,修改后获取threadinfo有问题,UI显示tereadinfo有问题,而且品牌没切过去",
  )
  EmbLibraryError changeColorByPalette(ThreadBrandName brandCode, int index);

  ///
  /// 糸色名取得
  ///
  /// 糸色変更画面へ遷移する
  ///
  /// 引数[in]:
  /// - [ThreadBrandName]  brandCode: 糸ブランド名
  /// - [int]             threadCode: 糸コード
  /// - [String]           pRGBCode: RGB値
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  /// - [String]        : 糸色名文字列
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂时未使用,可能会用到",
  )
  ({EmbLibraryError errorCode, String str}) getThreadColorName(
    int brandCode,
    int threadCode,
    String pRGBCode,
  );

  ///
  /// カラーシャッフルできる最大色数の取得
  ///
  /// 引数[in]:
  /// - [ThreadBrandName]  brandCode: ブランドコード
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  /// - [int]           : 糸色数
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,关联项color shuffie生成图片的颜色有问题",
  )
  ({EmbLibraryError errorCode, int num}) getMaxNumColorShuffle(
      ThreadBrandName brandCode);

  ///
  /// ランダム色でシャッフルする
  ///
  /// 引数[in]:
  /// - [int] shuffuleColorNum    : シャッフルする最大色数
  /// - [ShuffleBaseColor] shuffle: 基準色
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  /// - [int]           : サムネイル数
  ///
  @EmbApiResult(
    EmbApiFunction.embEdit_ChangeColor,
    ApiStatus.ngLib,
    "Android Simで色を設定した後に得られる画像は同じ色です",
  )
  ({EmbLibraryError errorCode, int num}) shuffleRandom(
      int shuffleColorNum, ShuffleBaseColor shuffle);

  ///
  /// ビビッド色でシャッフルする
  ///
  /// 引数[in]:
  /// - [int] shuffuleColorNum: シャッフルする最大色数
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  /// - [int]           : サムネイル数
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证,有issue",
  )
  ({EmbLibraryError errorCode, int num}) shuffleVivid(int shuffleColorNum);

  ///
  /// グラデーション色でシャッフルする
  ///
  /// 引数[in]:
  /// - [ShuffleBaseColor] shuffle: 基準色
  /// - [int]     shuffuleColorNum: シャッフルする最大色数
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  /// - [int]           : サムネイル数
  ///
  @EmbApiResult(
    EmbApiFunction.embEdit_ChangeColor,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, int num}) shuffleGradation(
      int shuffleColorNum, ShuffleBaseColor shuffle);

  ///
  /// ソフト色でシャッフルする
  ///
  /// 引数[in]:
  /// - [int] shuffuleColorNum: シャッフルする最大色数
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  /// - [int]           : サムネイル数
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证,有issue",
  )
  ({EmbLibraryError errorCode, int num}) shuffleSoft(int shuffleColorNum);

  ///
  /// 模様サムネイルの配色を更新追加する
  ///
  /// 前シャッフルと同じモードで再シャッフル、9パータン追加する。
  ///
  /// 戻り値   :
  /// - [EmbLibraryError] : エラーコード
  /// - [int]          : サムネイル数
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doTest,
    "暂定OK,待验证",
  )
  ({EmbLibraryError errorCode, int num}) addShufflePattern();

  ///
  /// カラーシャッフル結果画像抽出
  ///
  /// 引数[in]:
  /// - [int] thumbNum: 全ページのサムネイル通し番号
  /// - [int] type: イメージタイプ
  ///
  /// 戻り値   :
  /// - [EmbLibraryError] : エラーコード
  /// - [Image]  : イメージ出力
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doTest,
    "暂定OK,待验证",
  )
  ({EmbLibraryError errorCode, Image image}) getShuffledImage(
      int thumbNum, int type);

  ///
  /// 編集用に新色を設定する
  ///
  /// 引数[in]:
  /// - [int]  thumbNum: 全ページのサムネイル通し番号
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "待验证",
  )
  EmbLibraryError setShuffledNewColor(int thumbNum);

  ///
  /// カラーシャッフルの可用性状況を確認する
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : EMB_NO_ERR：使用可能;  EMB_INVALID_ERR：使用不可
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "待验证",
  )
  EmbLibraryError checkColorShuffle();

  ///
  /// 編集対象とする模様を選択する
  ///
  /// 引数[in]:
  /// - [MemHandle] groupH: グループハンドル
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证,API很奇怪",
  )
  EmbLibraryError selectEmbToEdit(MemHandle groupH);

  ///
  /// 模様の左右反転
  ///
  /// 模様を水平方向に反転する。回転角度も反転した値になる
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///                       EMB_REDSQUAREOVER_ERR
  ///                       EMB_PATTERN_EXCEEDED
  ///                       EMB_NO_ERR
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doTest,
    "暂定OK,待验证",
  )
  EmbLibraryError flipEmbHorizontal();

  ///
  /// 複数模様のグループ化
  ///
  /// グループ化させた模様は、サイズ変更、ミラー、複製はできない
  ///
  /// 戻り値   :
  /// - [EmbLibraryError] : エラーコード
  ///                       EMB_NOSELECT_ERR
  ///                       EMB_GRPHANDLE_ERR
  ///                       EMB_REDSQUAREOVER_ERR
  ///                       EMB_NO_ERR
  /// - [MemHandle] : ボーダーハンドルのポインタ
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doTest,
    "暂定OK,待验证,在调用这个API之前需要使用多选的API",
  )
  ({EmbLibraryError errorCode, MemHandle borderHandle})
      groupingMultiplePatterns();

  ///
  /// グループ化された模様のグループ化解除
  ///
  /// 引数[in]:
  /// - [MemHandle] borderH: ボーダーハンドル
  ///
  /// 戻り値   :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doTest,
    "暂定OK,待验证,选中状态现在在UI侧维护,需要确认,在调用这个API之前需要使用多选的API组合使用",
  )
  EmbLibraryError unGroupingGroupedPatterns(MemHandle borderHandle);

  ///
  /// 模様の複製
  ///
  /// グループ化された模様群の複製はできない。
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [List<MemHandle>] dupHandle: 複製したイメージのハンドルを格納する配列のポインタ
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doTest,
    "暂定OK,待验证,速度很慢",
  )
  ({EmbLibraryError errorCode, List<MemHandle> handleList}) duplicateEmb();

  ///
  /// アウトライン作成
  ///
  /// 引数[in]:
  /// - [int] distance: 刺繍との距離
  /// - [bool] inside : 内側の線描画
  /// - [bool] back   : 模様描画
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///                       EMB_OUTSIDE_OF_EMB_FRM_NOUSE
  ///                       EMB_TOOCOMPLEX_ERR
  ///                       EMB_DISTANCE_ERR
  ///                       EMB_MAKEOUTLINE_ERR
  ///                       EMB_NO_ERR
  /// - [Uint8List]  : イメージ
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngLib,
    "borderMarkを追加した後、クリックしてFunction Error: EMB_TOOCOMPLEX_ERR(62)を報告します",
  )
  ({EmbLibraryError errorCode, Uint8List imageDate}) makeOutline(
      int distance, bool inside, bool back);

  ///
  /// スティップリングの初期化
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.stipple,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError initMakingEasyStipple();

  ///
  /// スティップリングの作成
  ///
  /// 引数[in]:
  /// - [StippleType] type: スティップリング模様の種別
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  /// - [MemHandle]     : スティップリング模様のグループハンドル
  ///
  @EmbApiResult(
    EmbApiFunction.stipple,
    ApiStatus.ngLib,
    "borderMarkを追加した後、クリックしてFunction Error: EMB_TOOCOMPLEX_ERR(62)を報告します",
  )
  ({EmbLibraryError errorCode, MemHandle handle}) makeStipple(StippleType type);

  ///
  /// ボーダー処理を開始する
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doTest,
    "暂定OK,待验证",
  )
  EmbLibraryError startEmbBorder();

  ///
  /// 全グループハンドル取得
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [List<MemHandle>] : ボーダーハンドルの配列のポインタ
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证",
  )
  ({EmbLibraryError errorCode, List<MemHandle> handleList})
      getBorderHandleAll();

  ///
  /// 模様の拡大縮小
  ///
  /// 引数[in]:
  /// - [MagType] type: 縦、横、縦横の指定
  /// - [int]     step: 拡縮ステップ (-1縮 / +1拡)
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证",
  )
  EmbLibraryError changeSizeEmbGroup(MagType type, int step);

  ///
  /// 模様の拡大縮小STB
  ///
  /// 引数[in]:
  /// - [MagType] type: 縦、横、縦横の指定
  /// - [int]     step: 拡縮ステップ
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证",
  )
  EmbLibraryError changeSizeEmbGroupSTB(MagType type, int step);

  ///
  /// 非STBモードを設定する
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证",
  )
  EmbLibraryError resetStbMode();

  ///
  /// STBモードを設定する
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证",
  )
  EmbLibraryError setStbMode();

  ///
  /// 模様の移動
  ///
  /// 引数[in]:
  /// - [int] dirX: 横方向移動量(dirX = 0.5mm * dirX)
  /// - [int] dirY: 縦方向移動量(dirY = 0.5mm * dirY)
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证,速度太慢不太好验证全",
  )
  EmbLibraryError moveEmb(int dirX, int dirY);

  ///
  /// 模様中央に移動させる
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂定OK,待验证",
  )
  EmbLibraryError moveEmbCenter();

  ///
  /// 模様を回転させる
  ///
  /// 引数[in]:
  /// - [int] angle: 回転角度
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "有问题,模样未到边界但是调用API后返回超边界error",
  )
  EmbLibraryError rotateEmb(int angle);

  ///
  /// 刺繍密度の設定
  ///
  /// 引数[in]:
  /// - [MemHandle] groupH: 模様のグループハンドル
  /// - [int]      density: 刺繍密度
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  EmbLibraryError setEmbDensity(MemHandle groupH, int density);

  ///
  /// ボーダー模様の追加
  ///
  /// 引数[in]:
  /// - [BorderPosition] borderPos: 追加位置
  /// - [MemHandle] borderH: ボーダーハンドルのポインタ
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  ({EmbLibraryError errorCode, MemHandle handle}) addBorder(
      BorderPosition borderPos);

  ///
  /// ボーダー模様の削除
  ///
  /// 引数[in]:
  /// - [BorderPosition] borderPos: 削除位置
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  EmbLibraryError delBorder(BorderPosition borderPos);

  ///
  /// ボーダー模様の分割
  ///
  /// 引数[in]:
  /// - [DivBorder] direction: 切断方向
  /// - [int] index: 削除位置
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [MemHandle] : 切断後、新しく作成されたBorderのHandle
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "待验证",
  )
  ({EmbLibraryError errorCode, MemHandle handle}) divideBorder(
      DivBorder direction, int index);

  ///
  /// ボーダー間隔の設定
  ///
  /// 引数[in]:
  /// - [DivBorder] direction: 間隔設定方向
  /// - [int]           space: 間隔
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "待验证",
  )
  EmbLibraryError setBorderSpace(DivBorder direction, int space);

  ///
  /// ボーダー内の全グループハンドル取得
  ///
  /// 引数[in]:
  /// - [MemHandle] borderHandle: ボーダーハンドル
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]    : エラーコード
  /// - [List<MemHandle>] : ハンドル格納領域
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  ({EmbLibraryError errorCode, List<MemHandle> handleList})
      getGroupHandleInBorder(MemHandle borderHandle);

  ///
  /// ボーダー情報取得
  ///
  /// 引数[in]:
  /// - [MemHandle] borderHandle:ボーダーハンドル
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [BorderInfo] : ボーダー情報
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  ({EmbLibraryError errorCode, BorderInfo borderInfo}) getBorderInfo(
      MemHandle borderHandle);

  ///
  /// 指定したグループハンドルを含むボーダーハンドルを取得する
  ///
  /// 引数[in]:
  /// - [MemHandle] group:グループハンドル
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]     : エラーコード
  /// - [MemHandle] : ボーダーハンドル
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  ({EmbLibraryError errorCode, MemHandle handle}) getBorderHandleIncludeGroup(
      MemHandle group);

  ///
  /// ボーダー構成要素の情報取得
  ///
  /// 引数[in]:
  /// - [MemHandle] memHandle : ボーダーハンドル
  /// - [int] index : 構成要素インデックス
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [RectanArea] : マスク位置
  /// - [ThreadMarkState] : 糸印ありなし状態
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  ({
    EmbLibraryError errorCode,
    RectanArea area,
    ThreadMarkState threadMarkState
  }) getBorderCompInfo(MemHandle memHandle, int index);

  ///
  /// 糸印アサイン
  ///
  /// 引数[in]:
  /// - [int] index : 構成要素インデックス
  /// - [bool] : 設定・非設定
  /// - [ThreadMarkState] : 糸印位置
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证,API名字最前面加了个a",
  )
  EmbLibraryError signThreadMark(
      int index, bool setReset, ThreadPosition position);

  ///
  /// Applique作成
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///                       EMB_MCD_NOT_EXCHANGE_AREA_OVER
  ///                       EMB_APPLIQUE_NG_MEM_OVER
  ///                       EMB_OVERLAP_ERR
  ///                       EMB_DISTANCE_ERR
  ///                       EMB_APPLIQUE_NG_COMPLEX
  ///                       EMB_NO_ERR
  /// - [MemHandle]    : アップリケ模様のグループハンドル
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngLib,
    "Appliquéを終了して再度入力すると、アプリはクラッシュします",
  )
  ({EmbLibraryError errorCode, MemHandle handle}) makeApplique();

  ///
  /// オフセット距離設定
  ///
  /// 引数[in]:
  /// - [int] distance:模様からの距離
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [MemHandle]    : アップリケ模様のグループハンドル
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, MemHandle handle}) setDistanceApplique(
      int distance);

  ///
  /// Applique 作成完了
  ///
  /// 戻り値   :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngUi,
    "",
  )
  EmbLibraryError finishApplique();

  ///
  /// Applique 作成完了
  ///
  /// アップリケパーツのチェック及び初期化する
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngLib,
    "APiコールの順序に問題がある可能性があり、2回目はフラッシュバックに入ります",
  )
  EmbLibraryError goAppliquePartsSelection();

  ///
  /// アップリケパーツの選択
  ///
  /// アップリケのパーツを選択/非選択する
  ///
  /// 引数[in]:
  /// - [MemHandle] groupH :模様からの距離
  /// - [int]        index :模様からの距離
  /// - [bool]   selection :模様からの距離
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [String] : イメージ出力先ファイルパス
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngLib,
    "",
  )
  ({EmbLibraryError errorCode, Image image}) selectAppliqueParts(
      MemHandle groupH, int index, bool selection);

  ///
  /// パーツのアップリケ作成
  ///
  /// 選択されたパーツのアップリケの 作成
  ///
  /// 引数[in]:
  /// - [AppliquePartsParam] param :各種パラメータ
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : EMB_APPLIQUE_NG_MEM_OVER
  ///                       EMB_MCD_NOT_EXCHANGE_AREA_OVER
  ///                       EMB_OVERLAP_ERR
  ///                       EMB_DISTANCE_ERR
  ///                       EMB_APPLIQUE_NG_COMPLEX
  ///                       EMB_RGBCONVERT_ERR
  ///                       EMB_NO_ERR
  /// - [Image]       : イメージ
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, MemHandle handle, Image image})
      makeAppliqueSelectedParts(AppliquePartsParam param);

  ///
  /// 色選択Applique編集モードへ移行
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngLib,
    "",
  )
  EmbLibraryError editSelectedApplique();

  ///
  /// 色選択Applique編集モード　縫製/非縫製設定
  ///
  /// 引数[in]:
  /// - [MemHandle] groupH :刺繍模様グループハンドル
  ///                       1-通常の模様で縫合を行わない設定を行うとき、groupのhandleを渡す必要があります。
  ///                         libは、該当のパターンに指定された線色を設定します。
  ///                       2-border模様の場合、その中のあるgroupのhandleを渡すと、libはそのgroupの線色のみを設定します。
  ///                       3-border模様の場合、groupHをNullに設定すると、libはborder内の全groupの同一線色を設定します。
  /// - [int]        index :パーツindex
  /// - [bool]   notSewing :縫製の指定
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [String]       : イメージ出力先ファイルパス
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError changeSettingsSewing(
      MemHandle? groupH, int index, bool notSewing);

  ///
  /// 模様の整列
  ///
  /// 模様を左右端、上下端、中央(水平、垂直)でそろえる
  ///
  /// 引数[in]:
  /// - [int] alignment : アライメント
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "",
  )
  EmbLibraryError alignEmb(int alignment);

  ///
  /// スティップリング指定
  ///
  /// 引数[in]:
  /// - [FrameSize]    frSize :刺繍枠サイズ
  /// - [int]        distance :刺繍との距離
  /// - [int]           space :間隔
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [MemHandle]    :スティップリングイメージのグループハンドル
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngUi,
    "FrameSize这个用的不对,请参照FrameSize_t,自己定义一个显示用的列表",
  )
  ({EmbLibraryError errorCode, MemHandle handle}) setStipple(
      FrameSize frSize, int distance, int space);

  ///
  /// エコーキルト指定
  ///
  /// 引数[in]:
  /// - [FrameSize]    frSize :刺繍枠サイズ
  /// - [int]        distance :刺繍との距離
  /// - [int]           space :間隔
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [MemHandle]    :スティップリングイメージのグループハンドル
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  ({EmbLibraryError errorCode, MemHandle handle}) setEchoStipple(
      FrameSize frSize, int distance, int space);

  ///
  /// デコラティブ指定
  ///
  /// 引数[in]:
  /// - [int]                    index :模様番号index
  /// - [FrameSize]             frSize :刺繍枠サイズ
  /// - [int]                 distance :刺繍との距離
  /// - [int]                    space :間隔
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [MemHandle]    :スティップリングイメージのグループハンドル
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngLib,
    "http://apbil2030891.ap.brothergroup.net/bsh/ph-iivo-app/-/issues/330",
  )
  ({EmbLibraryError errorCode, MemHandle handle}) setDecoFillStipple(
    bool decorativeType,
    int decorativeNo,
    FrameSize frSize,
    int distance,
    int fillSize,
  );

  ///
  /// アウトラインデータ保存
  ///
  /// 編集状態のデータを指定パスに保存する。PCE, BMPの2種のファイルを同時に保存する。
  ///
  /// 引数[in]:
  /// - [String] file  :保存先ファイルのパス及びファイル名
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "待验证",
  )
  EmbLibraryError saveOutline(String file);

  ///
  /// 複数選択モードに入る
  ///
  /// 複数模様を選択してアウトラインを作成するときに必要
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  void enterMultiSelectMode();

  ///
  /// 複数選択モードを終了する
  ///
  /// 複数模様を選択してアウトラインを作成するときに必要
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  void exitMultiSelectMode();

  ///
  /// 選択/非選択状態にする
  ///
  /// 引数[in]:
  /// - [MemHandle]                   srcGroup :模様番号
  /// - [GroupSelectCondition] selectCondition :刺繍枠サイズ
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  EmbLibraryError selectMultiEmb(
      MemHandle srcGroup, GroupSelectCondition selectCondition);

  ///
  /// 模様の並び(縫製)順を一つ後ろにする
  ///
  /// カレントのグループに対して並び替えを行う
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "感觉没问题,待验证",
  )
  EmbLibraryError changeOrderNext();

  ///
  /// 模様の並び(縫製)順を一つ前にする
  ///
  /// カレントのグループに対して並び替えを行う
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "感觉没问题,待验证",
  )
  EmbLibraryError changeOrderPrevious();

  ///
  /// 模様の並び(縫製)順を最後にする
  ///
  /// カレントのグループに対して並び替えを行う
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "感觉没问题,待验证,因为选择的顺序是UI维护的",
  )
  EmbLibraryError changeOrderLast();

  ///
  /// 模様の並び(縫製)順を最初にする
  ///
  /// カレントのグループに対して並び替えを行う
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "感觉没问题,待验证,因为选择的顺序是UI维护的",
  )
  EmbLibraryError changeOrderFirst();

  ///
  /// 縫製前処理
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没有在使用,待验证,pattern编辑页面点击embrodibery时调用",
  )
  EmbLibraryError prepareSewing();

  ///
  /// しつけ糸追加
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [MemHandle] :しつけ糸イメージデータのハンドル
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "暂时没问题,待验证",
  )
  ({EmbLibraryError errorCode, MemHandle handle}) addBasting();

  ///
  /// しつけ糸削除
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "有问题,删不掉,UI点不了",
  )
  EmbLibraryError deleteBasting();

  ///
  /// 針数から針位置の座標を取得する
  ///
  /// 引数[in]:
  /// - [int] stitch   :針数
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [SSPoint]      : 針位置
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing,
    ApiStatus.doLibImp,
    "感觉没问题,待验证,UI实现有问题,0针的时候有问题,需要确认位置",
  )
  ({EmbLibraryError errorCode, SSPoint position}) getNeedlePosition(int stitch);

  ///
  /// 縫製開始位置の座標を取得する
  ///
  /// 引数[in]:
  /// - [EmbTrialStartPosition] position :位置
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [SSPoint]      : 針位置
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  ({EmbLibraryError errorCode, SSPoint position}) getNeedleStartPosition(
      int position);

  ///
  /// 現在の針位置座標を取得する
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値   :
  /// - [SSPoint]  : 針位置の画面上の座標
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing,
    ApiStatus.doLibImp,
    "",
  )
  SSPoint? getCurrentNeedlePosition();

  ///
  /// 全グループハンドル取得
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]   : 全グループハンドル取得
  /// - [List<MemHandle>]: グループハンドルを格納する領域
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  ({EmbLibraryError errorCode, List<MemHandle> handleList}) getGroupHandleAll();

  ///
  /// 指定グループのイメージデータの抽出
  ///
  /// UI側でイメージを使わなっくなったら廃棄する必要がある。
  ///
  /// 引数[in]:
  /// - [MemHandle] memHandle         : イメージ取得したいグループのハンドル
  /// - [ScrollCenterType] centerType : センタータイプ
  /// - [int] scale                   : 拡縮率
  /// - [bool] imageType              : イメージ種別
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [int]          : 内部に含まれるBMPファイルの数
  /// - [List<Uint8List>]       : イメージ出力先ファイル
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, int imageNum, List<Uint8List> imageDataList})
      getSelectedGroupARGBImage(
    MemHandle memHandle,
    int centerType,
    int scale,
    bool imageType,
  );

  ///
  /// リアルイメージデータの取得
  ///
  /// リアルイメージサイズは800(W)×838(H)pixel。拡大した際、これを超える範囲は描画されない。
  ///
  /// 引数[in]:
  /// - [bool] isMDC           :模様種別　MDC模様 true, 通常模様 false
  /// - [bool] displayPosition :表示位置
  /// - [int] scale            :拡縮率
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [Image]       : イメージ出力先ファイル
  /// - [List<RealImg>]: 画素情報(x, y, RGB)
  ///
  @EmbApiResult(
    EmbApiFunction.realPreview,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  ({EmbLibraryError errorCode, Image image, List<RealImg> realImgList})
      getRealImage(bool isMDC, bool displayPosition, int scale);

  ///
  /// インフォメーションイメージデータの取得
  ///
  /// リ768(W)×525(H)pixel。イメージは中央配置される。
  ///
  /// 引数[in]:
  /// - [bool] backDisplay : 背景表示　true 表示する, false 表示しない
  /// - [int] backColor    : 背景色　RGB値
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [String]       : イメージ出力先ファイルパス
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "待验证,例:XP中category 01 09 011是P模样,P模样保存到USB待验证",
  )
  ({EmbLibraryError errorCode, Image image}) getInfoImage(
      bool backDisplay, int backColor);

  ///
  /// カレントグループのハンドルを返す
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [MemHandle]  :  ハンドル格納エリア
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证,currentgroup变更规则,如:添加、删除、添加applique时",
  )
  ({EmbLibraryError errorCode, MemHandle handle}) getCurrentGroupHandle();

  ///
  /// 刺繍ロックレバーのON/OFF判定
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "计划废弃",
  )
  bool isEmbroideryUnitLeverDown();

  ///
  /// 針上エラー処理
  ///
  @Deprecated("廃棄関数、使用禁止")
  EmbLibraryError errorDisplayNeedleUpWhenFrameMove();

  ///
  /// 押え下エラー処理
  ///
  @Deprecated("廃棄関数、使用禁止")
  EmbLibraryError errorDisplayPresserFootDownWhenFrameMove();

  ///
  /// 文字間隔とビートクラスをリセットする
  ///
  @EmbApiResult(
    EmbApiFunction.embSelect_UintMove,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError curGroupCharArrayBack();

  ///
  /// 刺繍枠を取り外します
  ///
  /// ★★この関数の戻る値EMB_INVALID_ERR　が必要です、EMB_INVALID_ERRの時に　無効音を出して
  /// Api 呼び出しの後、刺繍ボックスを移動できるかどうかをチェックし、移動できる場合、 lib は対応するポップアップウィンドウを表示します。
  /// 移動できない場合は“ a”が返され、 ui はこのエラーを判断する必要があります。
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSelect_UintMove,
    ApiStatus.doUiImp,
    "要验证,UI实装中",
  )
  EmbLibraryError removeEmbUnit();

  ///
  /// LEDポインタON制御
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing_startPoint,
    ApiStatus.doTest,
    "",
  )
  EmbLibraryError setLedPointerON();

  ///
  /// マイナスEmbのTension
  /// stepSize*0.2ずつ増えます。1に0.2ずつ、2にすると0．4ずつになります
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingTension,
    ApiStatus.doTest,
    "",
  )
  EmbLibraryError setEmbTensionMinus(bool isLongPress);

  ///
  /// 増えるEmbのTension
  /// stepSize*0.2ずつ増えます。1に0.2ずつ、2にすると0．4ずつになります
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingTension,
    ApiStatus.doTest,
    "",
  )
  EmbLibraryError setEmbTensionPlus(bool isLongPress);

  ///
  /// ColorTrimポインタON制御
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingColorTrim,
    ApiStatus.doTest,
    "",
  )
  EmbLibraryError setEndColorTrimOn();

  ///
  /// ColorTrimポインタOFF制御
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingColorTrim,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError setEndColorTrimOff();

  ///
  /// StitchTrimポインタON制御
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingStitchTrim,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError setJumpStitchTrimOn();

  ///
  /// StitchTrimポインタOFF制御
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingStitchTrim,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError setJumpStitchTrimOff();

  ///
  /// マイナスStitchLengthMinus
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingStitchTrim,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError setJumpStitchLengthMinus();

  ///
  /// 増えるStitchLengthMinus
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingStitchTrim,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError setJumpStitchLengthPlus();

  ///
  /// 刺しゅう 編集から模様選択状態への遷移
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingStitchTrim,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError embEditReturnSelect();

  ///
  /// 自動つなぎの種類
  ///
  /// 戻り値  :
  /// - [EmbPatAutoKind] : 自動つなぎの種類
  ///
  @EmbApiResult(
    EmbApiFunction.embCheckPattern,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbPatAutoKind getEmbMarkPatCntAutoKind();

  ///
  /// つなぎ編集データ設定
  /// 引数[in]:
  /// - [SSPoint] pos                    :模様のずらし量
  /// - [MarkPatCnctSetting] cnctSetting :模様回転後のつなぎ方法
  /// - [int] angle                      :模様回転後の角度
  ///
  /// 戻り値  :
  /// - なし
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.ok,
    "",
  )
  void embMarkPatCnctEditParamSet(
      SSPoint pos, MarkPatCnctSetting cnctSetting, int angle);

  ///
  /// 刺しゅう レジュームファイルの読み出しを実行
  ///
  /// 引数[in]:
  /// - [String] resumeFilePath
  ///
  /// 戻り値  :
  /// - なし
  ///
  @EmbApiResult(
    EmbApiFunction.resume,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embResumeDataRead(String resumeFilePath);

  ///
  /// リアルイメージ針落ちデータの取得
  ///
  /// 引数[in]:
  /// - [bool] allPattern : REALIMG_SELECTED(選択中模様 true), REALIMG_ALLEMB(全模様 false)
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [List<EmbStitchPos>] : 針落ち点　単位0.1mm　最大枠の左上
  /// - [int] : データサイズ
  ///
  @EmbApiResult(
    EmbApiFunction.realPreview,
    ApiStatus.libMaintenance,
    "",
  )
  ({EmbLibraryError errorCode, List<EmbStitchPos> needlePos, int pointNum})
      getRealImageLight(bool allPattern);

  ///
  /// 大カテゴリ選択時の処理
  ///
  /// 引数[in]:
  /// - [EmbLargeCategory] cate : カウチング（C）　カウチング模様以外がセットされている場合はエラー表示
  ///                             大型分割模様（XPだと6番）　すでに模様がある場合、エラーを表示
  ///                             ロングステッチ（７）すでに模様がある場合、エラーを表示
  ///                             キルト作成（Q）　すでに模様がある場合、エラーを表示
  ///                             その他(通常模様、ディズニ模様)　カウチングの有無を確認し、カウチングがある場合はエラー表示
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSelect_Category_Select,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError checkEmbLargeCategorySelection(EmbLargeCategory cate);

  ///
  /// つなぎ マーク間の距離取得
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [double] つなぎ マーク間の距離
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.doLibImpUnit,
    "",
  )
  double embMarkPatCnctDistanceGet();

  ///
  /// 大型分割
  /// XPでのエラーメッセージ：ERR_SEWING_OVER_MARK_PAT_CNCT
  /// XPでのエラーメッセージ：ERR_SEWING_OVER_INTERNAL_LARGECONNECT
  /// XPでのエラーメッセージ：ERR_SEWING_OVER_MARK_PAT_CNCT_SOUND  =====>>>  ERR_SEWING_OVER_INTERNAL_LARGECONNECT_SOUND
  /// KeySewOverMarkPatCnctOK
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError emb1stAutoMarkPatCnctOK();

  ///
  /// 大型分割
  /// XPでのエラーメッセージ：ERR_SEWING_OVER_MARK_PAT_CNCT
  /// XPでのエラーメッセージ：ERR_SEWING_OVER_INTERNAL_LARGECONNECT
  /// XPでのエラーメッセージ：ERR_SEWING_OVER_MARK_PAT_CNCT_SOUND  =====>>>  ERR_SEWING_OVER_INTERNAL_LARGECONNECT_SOUND
  /// KeySewOverMarkPatCnctCancel
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError emb1stAutoMarkPatCnctCancel();

  ///
  /// ERR_CONFIRM_CANCEL_AUTOCNCT
  /// KeyConfirmMarkPatCnctCancel
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError emb1stAutoConfirmMarkPatCnctCancel();

  ///
  /// ERR_CONFIRM_CANCEL_AUTOCNCT
  /// KeyConfirmMarkPatCnctCancel
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError emb1stAutoConfirmMarkPatCnctOk();

  ///
  /// KeyGoToEmbRotate
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError embSewingGotoRotate();

  ///
  /// KeyGoToAllRotateQuilt
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError embSewingGotoRotateQuilt();

  ///
  /// KeyGoToEmbMove
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError embSewingGotoMove();

  ///
  /// KeyGoToEmbMoveQuilt
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError embSewingGotoMoveQuilt();

  ///
  /// embSewingReturnMoveOrRotate
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing_toolbar_layoutmenu_rotate_size_return,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError embSewingReturnMoveOrRotate();

  ///
  /// 画像表示用パラメータ取得
  ///
  /// 引数[in]:
  /// - [bool] markNo :
  ///
  /// 戻り値  :
  /// - [EmbMarkPatImgDrawParam] :
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  EmbMarkPatImgDrawParam embMarkPatCnctImgDrawPrmGet(int markNo);

  ///
  /// 自動つなぎ模様のとき、専用のOffsetを取得します
  ///
  /// 引数[in]:
  /// なし
  ///
  /// 戻り値  :
  /// - [SSPoint] :専用のOffset
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  SSPoint embMarkPatCnctSettingOffsetGet();

  ///
  /// 画像表示用パラメータ取得
  ///
  /// 引数[in]:
  /// - [bool] markNo :
  ///
  /// 戻り値  :
  /// - [EmbMarkPatImgDrawParam] :
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  EmbMarkPatSnowmanDrawParam embMarkPatCnctSnowmanDrawPrmGet(int markNo);

  ///
  /// Applique 作成取り消し
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  /// EMB_NO_ERRのみを返する。
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError cancelApplique();

  ///
  /// 色選択AppliquePreview取り消し
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError cancelPreviewApplique();

  ///
  /// TODO:APIの作用を確認してください。
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError checkUsableAppliqueParts();

  ///
  /// つなぎ設定画面に入る際、つなぎ機能で使用される画像データをLibに共有します。
  ///
  /// 引数[in]:
  /// - [Uint8List] lastImgData : 1つ目の画像RGBデータ
  /// - [int] lastImgWidth :  1つ目の画像の幅
  /// - [int] lastImgHeight :  1つ目の画像の高さ
  /// - [Uint8List] nowImgData  : 2つ目の画像RGBデータ
  /// - [int] nowImgWidth :  2つ目の画像の幅
  /// - [int] nowImgHeight :  2つ目の画像の高さ
  /// - [int] r : 赤 0 ～ 255
  /// - [int] g : 緑 0 ～ 255
  /// - [int] b : 青 0 ～ 255
  ///
  /// 戻り値  :
  /// - なし
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  void embMarkPatCnctSetScanResultImage(
      {required Uint8List lastImgData,
      required int lastImgWidth,
      required int lastImgHeight,
      required Uint8List nowImgData,
      required int nowImgWidth,
      required int nowImgHeight,
      required int r,
      required int g,
      required int b});

  ///
  /// 布張替え時等の画面の表示画像を取得します
  ///
  /// 引数[in]:
  /// なし
  ///
  /// 戻り値  :
  /// - [Uint8List]  : イメージ
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  Uint8List? embMarkPatCnctGetScanResultImage();

  ///
  /// 布張替え時等の画面の表示画像データを削除します
  ///
  /// 引数[in]:
  /// なし
  ///
  /// 戻り値  :
  /// なし
  ///
  void embMarkPatCnctDelScanResultImage();

  ///
  /// 現在のつなぎコネクト位置の取得
  ///
  /// 引数[in]:
  /// なし
  ///
  /// 戻り値  :
  /// - [MarkPatCnctSetting] :模様回転後のつなぎ方法
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  MarkPatCnctSetting embMarkPatCnctSettingGet();

  ///
  /// 刺繍つなぎ予約設定の実行結果を取得します
  ///
  /// 引数[in]:
  /// なし
  ///
  /// 戻り値  :
  /// - [int] 刺繍つなぎ予約設定の実行結果
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  int getEmbSewPatternConnect();

  ///
  /// エラーコード変換
  ///
  /// 引数[in]:
  /// - [int]  : エラーコード
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError exchangeErrCode(int errorCode);

  ///
  ///
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.cameraScan,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError embSewingSnowmanScanCancelFrameMove();

  ///
  /// つなぎ模様：画面タッチでの移動
  ///
  /// 引数[in]:
  /// - [int] posX: 横方向移動量
  /// - [int] posY: 縦方向移動量
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  void embMarkPatCnctScrSetting(int posX, int posY);

  ///
  /// 縫製-枠退避 開始
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSelect_UintMove,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError embSewingEscUnit();

  ///
  /// 縫製-枠退避 退避
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSelect_UintMove,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError embSewingMoveEscUnit();

  ///
  /// 縫製-枠退避 戻し
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSelect_UintMove,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError embSewingReturnEscUnit();

  ///
  /// 縫製-枠退避 退避中判定
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [bool]  : 判定の結果
  ///
  @EmbApiResult(
    EmbApiFunction.embSelect_UintMove,
    ApiStatus.libMaintenance,
    "",
  )
  bool isEmbSewingMoveEscUnit();

  ///
  /// 無効状態で刺しゅう 編集から模様選択状態への遷移を実行
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embCheckPattern,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError embEditReturnSelectGray();

  ///
  /// Volatile設定
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [bool]  :
  ///
  @EmbApiResult(
    EmbApiFunction.embCheckPattern,
    ApiStatus.libMaintenance,
    "",
  )
  bool isVolatileEmb();

  ///
  /// Volatile設定
  ///
  /// 引数[in]:
  /// - [bool] isVolatile:
  ///
  /// 戻り値  :
  /// - なし
  ///
  @EmbApiResult(
    EmbApiFunction.embCheckPattern,
    ApiStatus.libMaintenance,
    "",
  )
  void setVolatileCurEmbGroup(bool isVolatile);

  ///
  /// Restriction設定
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [bool]  :
  ///
  @EmbApiResult(
    EmbApiFunction.embCheckPattern,
    ApiStatus.libMaintenance,
    "",
  )
  bool isRestrictionEmb();

  ///
  /// Restriction設定
  ///
  /// 引数[in]:
  /// - [int] restriction:
  ///
  /// 戻り値  :
  /// - なし
  ///
  @EmbApiResult(
    EmbApiFunction.embCheckPattern,
    ApiStatus.libMaintenance,
    "",
  )
  void setRestrictionCurEmbGroup(int restriction);

  ///
  /// 刺しゅう 選択から編集に戻る(追加時に何も追加せずに戻る)
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSelect_Login,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError embSelectReturnEdit();

  ///
  /// 刺繍データ保存
  ///
  /// 編集状態のデータを指定パスに保存する。
  ///
  /// 引数[in]:
  /// - [String] fileName: 保存先ファイルのパスおよびファイル名
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.resume,
    ApiStatus.doLibImp,
    "",
  )
  EmbLibraryError saveEmbResumeData(String fileName);

  ///
  /// 次の模様に切り替える
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "",
  )
  EmbLibraryError selectEmbGroupNext();

  ///
  /// 前の模様に切り替える
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "",
  )
  EmbLibraryError selectEmbGroupPrev();

  ///
  /// オープニング画面から刺しゅう大カテゴリ画面へ遷移
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "",
  )
  EmbLibraryError gotoEmbFromOpening();

  ///
  /// 模様エリア外をタッチしたら 複数選択を解除する
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "",
  )
  EmbLibraryError notPatternAreaSelectWork();

  ///
  /// アウトライン終了
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "",
  )
  EmbLibraryError finishOutline();

  ///
  /// ボーダーの模様切り替え
  ///
  /// 引数[in]:
  /// - [int] :  パーツindex
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.border,
    ApiStatus.doLibImp,
    "",
  )
  EmbLibraryError setBorderCurIdx(int index);

  ///
  /// 刺繍全領域情報
  ///
  /// 戻り値   :
  /// - [EmbLibraryError]  : エラーコード
  /// - [RectanArea] :  刺繍全領域情報
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing,
    ApiStatus.doLibImp,
    "",
  )
  ({EmbLibraryError errorCode, RectanArea area}) getEmbArea();
}

///
/// 簡単修正為に
/// BSH追加したのAPI mixinでEmbLibFunctionInterfaceに追加する。
///
mixin _ExtensionFunction {
  ///
  /// EmbMode起動状況を取得
  ///
  /// true(ON)/false(Off)
  ///
  bool isEmbModeOpened();

  ///
  /// EMBからMDCに遷移する際に、EMBを閉じます。
  ///
  void closeEmbModeMdc();

  ///
  /// 現在の刺繍模様が大型分割模様か確認
  ///
  /// 戻り値  :
  /// - [bool] : はいまたはいいえ
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngUi,
    "未实装,要删除",
  )
  bool isEmbCustomDesign();

  ///
  /// 自動模様つなぎ取得
  ///
  /// 戻り値  :
  /// - [bool] : 取得成功または取得失敗
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "问题同上面",
  )
  bool getEmbMarkPatCntAutoCustom();

  ///
  /// 現在選択されている刺繍パターンの密度を取得します
  ///
  /// 引数[in]:
  /// - ない
  /// 戻り値 :
  /// - [int] : 密度サイズ
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  ({EmbLibraryError errorCode, int density}) getEmbDensity();

  ///
  /// Undo/Redoファイルを書き出す(PHX)
  ///
  /// 引数[in]:
  /// - [String] fileName: Undo/Redoファイルのパス(フルパス)
  ///
  /// 戻り値 :
  /// - [EmbLibraryError] :エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  EmbLibraryError saveUndoRedoFile(String fileName);

  ///
  /// Undo/Redoファイルを読み込む
  ///
  /// 引数[in]:
  /// - [String] fileName: Undo/Redoファイルのパス(フルパス)
  ///
  /// 戻り値 :
  /// - [EmbLibraryError] :エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "有问题,有issue,要返回选择状态",
  )
  EmbLibraryError undoRedo(String fileName);

  ///
  /// 現在の針位置を取得する
  ///
  /// 戻り値 :
  /// - [int]          :今針の位置
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "没问题,待验证",
  )
  int getCurrentStitchNumber();

  ///
  /// 現在の針位置を設定する
  ///
  /// 引数[in]:
  /// - [int] needleCount: 設定された針位置
  ///
  /// 戻り値 :
  /// - [EmbLibraryError] :エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "待验证,实机不知道",
  )
  EmbLibraryError setCurrentStitchNumber(int needleCount);

  ///
  /// 停止無し縫製（１色縫製）を設定する
  ///
  /// 引数[in]:
  /// - [bool] isOneColor: true: 単色縫製  false: ノンモノクロ縫製
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "有问题",
  )
  void setEmbSewNonStopSewing(bool isOneColor);

  ///
  /// １色縫製が設定不可であることを確認する
  ///
  /// 戻り値:
  /// - [bool] : true: 無効です  false: 有効
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "待验证",
  )
  bool isEmbSewOneColorNG();

  ///
  /// １色縫製が設定可能か確認する
  ///
  /// 戻り値:
  /// - [bool] : true: 選択された状態  false: ステータスが選択されていません
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "待验证",
  )
  bool isEmbSewOneColorON();

  ///
  /// 刺しゅうつなぎ機能を設定する
  ///
  /// 引数[in]:
  /// - なし
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "つなぎ用,api中没传参数",
  )
  int setEmbSewPatternConnectReserve();

  ///
  /// キルト縫製針位置の座標を設定する
  ///
  /// 引数[in]:
  /// - [EmbTrialStartPosition] quiltNeedlePositionValue: 針位置
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "quilt专用",
  )
  void setQuiltNeedlePosition(int quiltNeedlePositionValue);

  ///
  /// Sewing模様情報取得
  ///
  /// 戻り値:
  /// - [EmbLibraryError] : エラーコード
  /// - [EmbSewingInfo]   : Sewing模様イメージ情報を格納する配列のポインター
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "计划使用,UI用的对不对待确认",
  )
  ({EmbLibraryError errorCode, EmbSewingInfo embSewingInfo}) getEmbSewInfo();

  ///
  /// カラーソート(色まとめ)
  ///
  /// 引数[in]:
  /// - [bool] colorSort: 色をソートする必要があるかどうか
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "UI待实装,点击时会报错的",
  )
  EmbLibraryError setColorSort(bool colorSort);

  ///
  /// 位置合わせマーク(スノーマン)が使用不可であることを確認する
  ///
  /// 戻り値:
  /// - [bool] : true:無効   false: 有効
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "sewing页面中snowman使用可否API",
  )
  bool embEditPositionNG();

  ///
  /// 刺繍縫製のマスクトレースを設定する
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImp,
    "模样外轮廓四个角依次走一遍,待实装,注意动的时候panel点不了",
  )
  void setEmbSewingMaskTrace();

  ///
  /// カラーテーブルの取得
  ///
  /// 引数[in]:
  /// - [ThreadBrandName] brandCode: 糸ブランド名
  ///
  /// 戻り値:
  /// - [EmbLibraryError] : エラーコード
  /// - [List<BrandColorInfo>] : カラーテーブルのカラー情報の配列
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.ngLib,
    "リストはXPより1つ多い,最後の色選択リストに余分な色があります",
  )
  ({EmbLibraryError errorCode, List<BrandColorInfo> brandColorInfoList})
      getThreadColorTable(ThreadBrandName brandCode);

  ///
  /// カラーテーブルの取得
  ///
  /// 引数[in]:
  /// - [ThreadBrandName] brandCode: 糸ブランド名
  ///
  /// 戻り値:
  /// - [EmbLibraryError] : エラーコード
  /// - [List<BrandColorInfo>] : カラーテーブルのカラー情報の配列
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngUi,
    "UI实现有问题",
  )
  ({EmbLibraryError errorCode, List<BrandColorInfo> brandColorInfoList})
      getThreadColor61Table(ThreadBrandName brandCode);

  ///
  /// Edge-to-Edgeキルトイメージの取得
  ///
  /// changeEdgeToEdgeQuiltColorRGBで糸色変更後、本関数を呼ぶことで、色が変更されたイメージを取り出すことができる。
  ///
  /// 引数[in]:
  /// - [bool ] frameBorder: 枠線(赤)描画
  ///
  /// 戻り値:
  /// - [EmbLibraryError] : エラーコード
  /// - [int]             : 分割数 0はエラー
  /// - [Image]           : イメージ
  ///
  @EmbApiResult(
    EmbApiFunction.quiltEdgetoEdge,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, int division, Image image})
      getQuiltPreviewEdgeToEdgeImage(bool frameBorder);

  ///
  /// TODO:このAPIは削除を予定しています、ただしMDCの「getMdcBuiltInDecorativeFillTotalNum」「getMdcBuiltInDecorativeFillThumbnail」「getMdcCustomDecorativeFillThumbnail」の三つの関数を使う必要があり、それが可能かどうかを確認する必要がある
  /// https://brothergroup.atlassian.net/browse/PHBSH-3431
  ///
  /// PLFデータをBMP画像データに変換する
  ///
  /// 引数[in]:
  /// - [dynamic] bmpWidth: BMP イメージの幅
  /// - [dynamic] bmpHeight: BMP画像の高さ
  /// - [Uint8List]     plf: PLF ファイル データ
  ///
  /// 戻り値:
  /// - [Uint8List] : BMP画像
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngUi,
    "issue中:このAPIは削除を予定しています、ただしMDCの「getMdcBuiltInDecorativeFillTotalNum」「getMdcBuiltInDecorativeFillThumbnail」「getMdcCustomDecorativeFillThumbnail」の三つの関数を使う必要があり、それが可能かどうかを確認する必要がある",
  )
  Uint8List covertPlfToBmp(dynamic bmpWidth, dynamic bmpHeight, Uint8List plf);

  ///
  /// キルト糸色設定
  ///
  /// changeQuiltColorは使わないでください。
  /// changeQuiltColorはすべてchangeQuiltColorByPaletteに置き換えてください。
  ///
  /// 引数[in]:
  /// - [int]  colorNum: 短冊番号
  /// - [int]  r: 赤
  /// - [int]  g: 緑
  /// - [int]  b: 青
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.notUse,
    "",
  )
  void changeQuiltColor(int colorNum, int r, int g, int b);

  ///
  /// Edge-to-Edgeキルト糸色設定リセット
  ///
  @EmbApiResult(
    EmbApiFunction.quiltSashe,
    ApiStatus.ngLib,
    "色を変えたら、getEmbEditThreadInfo()を返されるbrandCodeの値が常に1です",
  )
  EmbLibraryError changeQuiltColorByPalette(int colorNum, int Brand, int index);

  ///
  /// アップリケパーツの全選択
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  ({EmbLibraryError errorCode, Image image}) selectAppliquePartsAll(
      MemHandle memHandle, bool selection);

  ///
  /// AppliqueNotSewingパターン状態取得
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doUiImp,
    "未确认,可能要改成全局error,作业还要继续",
  )
  EmbLibraryError isAppliqueTextureNotAllDraw();

  ///
  /// 刺繡データの合計時間を取得する
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  /// - [int] time : 刺繍の合計時間(単位: 分)
  ///
  ({EmbLibraryError errorCode, int time}) getPhotoStitchEmbTotalTime();

  ///
  /// 刺しゅう縫製previewのPNG形式画像取得
  /// 刺しゅう縫製初期化場合、全体画像PNGデータをを作成する。
  /// 最大枠サイズに相当した画像サイズ
  /// 戻り値  :
  /// - [Uint8List?] : 刺しゅう縫製previewのPNG形式画像
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doUiImp,
    "issue:http://apbil2030891.ap.brothergroup.net/bsh/ph-iivo-app/-/issues/204",
  )
  Uint8List? getMonitoringEmbImage();

  ///
  /// 刺しゅう縫製保持しているのPNG形式画像取得
  /// 1.刺しゅう縫製初期化場合、全体像PNGデータをを作成する。
  /// 2.縫製初めて場合、最初のthreadのイメージ
  /// 3.thread変更の場合、PNGでーたを更新する。（縫製しましたthreadの画像+次のthreadのイメージ）
  /// 最大枠サイズに相当した画像サイズ
  /// 戻り値  :
  /// - [Uint8List?] : 刺しゅう縫製previewのPNG形式画像
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doUiImp,
    "issue:http://apbil2030891.ap.brothergroup.net/bsh/ph-iivo-app/-/issues/204",
  )
  Uint8List? getMonitoringEmbProgImage();

  ///
  /// 縫製画面での回転
  ///
  /// 引数[in]:
  /// - [int] angle: 回転角度
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing,
    ApiStatus.doUiImp,
    "issue中",
  )
  EmbLibraryError rotateEmbAll(int angle);

  ///
  /// 縫製画面での回転リセット
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError resetEmbAllRotate();

  ///
  /// 模様の回転をリセットする
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError resetEmbRotate();

  ///
  /// 引数[in]:
  /// - [int] scale:
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError scalingQuiltEmb(int scale);

  ///
  /// 引数[in]:
  /// - [int] point:
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError startPointSetQuiltEmb(int point);

  ///
  /// 引数[in]:
  /// - [int] select:
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError selectPartsQuitEmb(int select);

  ///
  /// 縫製画面での移動
  ///
  /// 引数[in]:
  /// - [int] dirX: 横方向移動量
  /// - [int] dirY: 縦方向移動量
  /// - [bool] flgLongPress: layoutカメラ画面のみ有効
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing_PatternMove,
    ApiStatus.ngLib,
    "移动不了",
  )
  EmbLibraryError moveEmbAll(int dirX, int dirY, bool flgLongPress);

  ///
  /// 縫製画面模様全体を中央に移動させる
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing_PatternMove,
    ApiStatus.ngLib,
    "有问题",
  )
  EmbLibraryError moveEmbAllCenter();

  ///
  /// ボーダー処理の終了
  ///
  /// 戻り値  :
  /// - [EmbLibraryError]  : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngLib,
    "未提供",
  )
  EmbLibraryError endEmbBorder();

  ///
  /// キルト模様の移動
  ///
  /// 引数[in]:
  /// - [int] dirX: 横方向移動量
  /// - [int] dirY: 縦方向移動量
  /// - [bool] flgLongPress: 長押しするかどうか
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngLib,
    "未验证",
  )
  EmbLibraryError moveQuiltEmb(int dirX, int dirY, bool flgLongPress);

  ///
  /// キルト模様を回転させる
  ///
  /// 引数[in]:
  /// - [int] angle: 回転角度
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngLib,
    "未验证",
  )
  EmbLibraryError rotateQuiltEmb(int angle);

  ///
  /// キルト模様の拡大縮小
  ///
  /// 引数[in]:
  /// - [MagType] type: 縦、横、縦横の指定
  /// - [int]     step: 拡縮ステップ
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngLib,
    "未验证",
  )
  EmbLibraryError changeSizeQuiltEmb(MagType type, int step);

  ///
  /// 布厚検出スキャンする関数
  ///
  /// 引数[in]:
  ///  - void Function(Image,FabricScanStatus) callBack: スキャン結果通知関数
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doUiImp,
    "删除",
  )
  EmbLibraryError scanFabric(void Function(Image, FabricScanStatus) callBack);
}

mixin DeviceLibraryEventInterface {
  ///
  /// つなぎ設定の場合、スノーマンマークをスキャンする関数
  ///
  /// 引数[in]:
  /// - scanType: スキャンタイプ
  ///   - snowmanMark1: スノーマンマーク１
  ///   - snowmanMark2: スノーマンマーク２
  ///   - snowmanMarkDistance: スノーマンマークの位置認識
  ///
  /// - void Function(Image) callBack: スキャン結果通知関数
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doUiImp,
    "计划删除",
  )
  EmbLibraryError connectSewingScan(
    ConnectSewingScanType scanType,
    void Function(Image, ConnectSewingScanStatus) callBack,
  );

  ///
  /// 刺しゅう 編集状態への遷移
  ///
  /// 戻り値  :
  ///
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doUiImp,
    "",
  )
  EmbLibraryError embGotoEdit();

  ///
  /// 起動後の再開にはOKボタンを使用します
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.doUiImp,
    "等待UI实装",
  )
  EmbLibraryError embGotoResumeOk();

  ///
  /// 刺しゅう 模様選択状態への遷移
  ///
  /// 戻り値  :
  ///
  /// - [EmbLibraryError] : エラーコード
  ///
  /// 可能戻りエラー
  /// - EMB_INVALID_ERR キー関数実行不可
  ///
  @EmbApiResult(
    EmbApiFunction.initial,
    ApiStatus.ngLib,
    "LongStitch模様,ポップアップしませんでした",
  )
  EmbLibraryError embGotoSewing();

  ///
  /// 刺しゅう 縫製画面から編集状態への遷移
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.screen_transfer,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embSewingReturnEdit();

  ///
  /// Emb Select→MDC Paintへの遷移
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.screen_transfer,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embGotoMDCPaint();

  ///
  /// FeedBack調整への遷移
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingFeedback,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embSewingSelectFb();

  ///
  /// FeedBack調整の終了
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingFeedback,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embSewingFbEnd();

  ///
  /// カメラFB
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingFeedback,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embSewingStartCameraFb();

  ///
  /// 閉めるカメラFB
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingFeedback,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embSewingCloseCameraFb();

  ///
  /// カメラは針が着地する場所をプレビューします
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingFeedback,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embSewingChangeNeedleViewCameraFb();

  ///
  /// カメラプレビューのズームボタン
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingFeedback,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embSewingZoomCameraFb();

  ///
  /// 縫製が終わったら、次のパターンを縫い続ける必要があるかどうかを判断します
  ///
  /// 引数[in]:
  /// - [bool] : flg => false:キャンセルして縫製を続ける,true:縫製を続ける
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.doLibImpUnit,
    "",
  )
  void embSewOverMarkPatCnct(bool flg);

  ///
  /// 次の外観を再選択します
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.doLibImpUnit,
    "",
  )
  void embNextPatternSelMarkPatCnct();

  ///
  /// イメージエリア
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.doLibImpUnit,
    "",
  )
  void embMarkPatCnctMove(int dir);

  ///
  /// つなぎ模様：左回転
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embMarkPatCnctRotateLeft();

  ///
  /// つなぎ模様：右回転
  ///
  /// 戻り値  :
  ///
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embMarkPatCnctRotateRight();

  ///
  /// 押すごとに、２つめの模様を90度ずつ回転させる
  ///
  /// 戻り値  :
  ///
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embMarkPatCnctRotateRight90();

  ///
  /// つなぎモードではなく通常の縫製画面
  ///
  /// 戻り値  :
  ///
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  void embSetMarkPatCnctStdCancelBefore();

  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  void embSetBefore1stMarkPatCnctOK();

  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embConnectSewing,
    ApiStatus.libMaintenance,
    "",
  )
  void embSetBefore2ndMarkPatCnctOK();

  ///
  /// 刺しゅう縫製 FB 色進む
  ///
  /// 引数[in]：
  /// - [int] moveType
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingFeedback,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embSewingFbStitchMove(EmbSewingFbStitchMove moveType);

  ///
  /// カメラ画像表示ヘルプ画面に行く
  ///
  /// 戻り値  :
  ///
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingFeedback,
    ApiStatus.doLibImpUnit,
    "",
  )
  EmbLibraryError embSewingFbCameraView();

  ///
  /// 背景スキャン
  ///
  /// 戻り値  :
  ///
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.backgroundScan,
    ApiStatus.doTest,
    """
    """,
  )
  EmbLibraryError embSewingSelectBackgroundScan();

  ///
  /// 背景スキャン
  ///
  /// 戻り値  :
  ///
  /// - [EmbLibraryError] : エラーコード
  ///
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing,
    ApiStatus.doTest,
    """
    """,
  )
  EmbLibraryError embSewingBackgroundScanStart();

  ///
  /// 背景スキャン
  ///
  /// 戻り値  :
  ///
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing,
    ApiStatus.doTest,
    """
    """,
  )
  EmbLibraryError embSewingBackgroundScanCancel();

  ///
  /// 背景スキャン
  ///
  /// 戻り値  :
  ///
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing,
    ApiStatus.doTest,
    """
    """,
  )
  EmbLibraryError embSewingBackgroundScanEnd();

  ///
  /// MDC背景スキャン
  ///
  /// 戻り値  :
  ///
  /// - [EmbLibraryError] : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.backgroundScan,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  EmbLibraryError mdcSelectBackgroundScan();

  ///********************************************************** */
  ///                                                           */
  /// スノーマン関連API一覧                                       */
  ///                                                           */
  ///********************************************************** */
  ///
  /// スノーマンモードを選択する
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingSnowman,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError embSewingSelectSnowman();

  ///
  /// スノーマンモードを開始する
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingSnowman,
    ApiStatus.doUiImp,
    "待验证",
  )
  EmbLibraryError embSewingSnowmanStartOk();

  ///
  /// スノーマンモードをキャンセルする
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingSnowman,
    ApiStatus.doUiImp,
    "待验证",
  )
  EmbLibraryError embSewingSnowmanStartCancel();

  ///
  /// スノーマンスキャンを開始する
  ///
  /// 戻り値 : エラーコード
  ///         EMB_INVALID_ERR
  ///         EMB_INVALID_ERR_PANEL:
  ///           ERR_NEEDLE_UP
  ///           FrameErrorCodeGet() ★詳細：ライブラリーソースコードFrameErrorCodeGet
  ///           ERR_CHANGE_L_OR_LM_OR_M_FRAME
  ///           ERR_SCAN_FRAME_NOT_USE
  ///           ERR_EMB_FRAME_HOLD_LEVER_DOWN
  ///           ERR_ALL_DELETE_BORDER_OK_FOR_POSITIONING
  ///           ERR_EMB_POSITIONING_WARNING
  @EmbApiResult(
    EmbApiFunction.embSewingSnowman,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError embSewingSnowmanScanOk();

  ///
  /// スノーマンスキャンをキャンセルする
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingSnowman,
    ApiStatus.doUiImp,
    "待验证",
  )
  EmbLibraryError embSewingSnowmanScanCancel();

  ///
  /// スノーマン枠移動をキャンセルする
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingSnowman,
    ApiStatus.doUiImp,
    "待验证",
  )
  EmbLibraryError embSewingSnowmanFrameMoveCancel();

  ///
  /// スノーマン枠移動を開始する
  ///
  /// 戻り値 : エラーコード
  ///         EMB_INVALID_ERR
  ///         EMB_INVALID_ERR_PANEL:
  ///           FrameErrorCodeGet() ★詳細：ライブラリーソースコードFrameErrorCodeGet
  ///           ERR_NEEDLE_UP
  ///           ERR_CHANGE_L_OR_LM_OR_M_FRAME
  ///           ERR_EMB_FRAME_HOLD_LEVER_DOWN
  @EmbApiResult(
    EmbApiFunction.embSewingSnowman,
    ApiStatus.ok,
    "",
  )
  EmbLibraryError embSewingSnowmanFrameMoveOk();

  ///
  /// スノーマンマックを取り出す
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingSnowman,
    ApiStatus.doUiImp,
    "待验证",
  )
  EmbLibraryError embSewingSnowmanEmbMachineRemove();

  ///
  /// スノーマン機能を結束する
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewingSnowman,
    ApiStatus.doUiImp,
    "待验证",
  )
  EmbLibraryError embSewingSnowmanEnd();

  ///
  /// カメラスキャンしたデータを取得する
  ///
  /// 引数[in]:
  /// - [int]  kind : CameraScanDataKind_t
  ///
  /// 戻り値 :
  /// - [EmbLibraryError] : エラーコード
  ///                       LibにErrorを処理する
  ///                       NoError以外あるなら、前回のスキャン結果は表示せずに、何も表示しない
  /// - [CameraScanInfoAndData] : infoAndData
  ///
  @EmbApiResult(
    EmbApiFunction.cameraScan,
    ApiStatus.libMaintenance,
    """
    此API在为「getCameraScanImage」获取图像做准备,需要先调用此API获取info和data的数据,将其保存在本地文件夹中。
    再将保存的文件路径(包括文件名)提供给「getCameraScanInfoAndData」获取扫描后的结果。
    """,
  )
  ({EmbLibraryError error, CameraScanInfoAndData infoAndData})
      getCameraScanInfoAndData(CameraScanDataKind kind);

  ///
  /// 選択画像をスキャン形式データに変換するためにセットする
  /// filePathはBMPファイルパス
  ///
  @EmbApiResult(
    EmbApiFunction.cameraScan,
    ApiStatus.libMaintenance,
    """
    此API需要在使用前将选中的非bmp图片转换成bmp图片后,将bmp图片路径传入该函数
    """,
  )
  void setMdcSelectPictureForScanInfoAndData(String filePath);

  ///
  /// スキャン画像を取得する
  ///
  /// 引数[in]:
  /// - [int] scale : CameraScanImageScale_t
  /// - [String] cameraScanInfoPath
  /// - [String] cameraScanDataPath
  ///
  /// 戻り値 :
  /// - [EmbLibraryError] error : エラーコード
  /// - [Uint8List] cameraScanImage : 取得したスキャン画像
  ///
  @EmbApiResult(
    EmbApiFunction.cameraScan,
    ApiStatus.libMaintenance,
    """
    使用此API前需调用「getCameraScanInfoAndData」在本地准备图像数据。并使用保存的本地图像数据得到最终扫描结果图像
    """,
  )
  ({EmbLibraryError error, Uint8List cameraScanImage}) getCameraScanImage(
    CameraScanImageScale scale,
    String cameraScanInfoPath,
    String cameraScanDataPath,
  );

  ///
  /// スキャン画像を取得する ( テストモード64. Scan Check 専用 )
  ///
  /// 引数[in]:
  /// -[int] kind
  ///
  /// 戻り値 :
  /// -[EmbLibraryError] error : エラーコード
  /// -[Uint8List] cameraScanImage : 取得したスキャン画像
  ///
  @EmbApiResult(
    EmbApiFunction.cameraScan,
    ApiStatus.notUse,
    """
    現時点では使用されていません
    """,
  )
  ({EmbLibraryError error, Uint8List cameraScanImage})
      getCameraScanTestModeImage(int kind);

  ///
  /// 初期化Applique
  ///
  /// 戻り値 :
  /// -[EmbLibraryError] error : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.ngUi,
    "返されたエラーが正しくありません",
  )
  EmbLibraryError initAppliqueSelect();

  ///
  /// Appliqueに入るときは、BorderMarkを削除します(initAppliqueSelectの後に呼ばれる)
  ///
  /// 戻り値 :
  /// -[EmbLibraryError] error : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  EmbLibraryError deleteMarkBeforeWappen();

  ///
  /// Appliqueに入るときは、BorderMarkをキャンセルします(initAppliqueSelectの後に呼ばれる)
  ///
  /// 戻り値 :
  /// -[EmbLibraryError] error : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.ngUi,
    """
      既存のプレビュー コードと競合するため、エラーが発生します
    """,
  )
  ({EmbLibraryError errorCode, MemHandle handle}) cancelBorderBeforeWappen();

  ///
  /// setTexture
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.ok,
    """
    """,
  )
  ({EmbLibraryError errCode, Uint8List image}) setTexture(bool texture);

  ///
  /// checkTextureDrawing
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.libMaintenance,
    """
      前に呼び出された API を判別できないため、APi の戻り値を判別できません
    """,
  )
  EmbLibraryError checkTextureDrawing();

  ///
  /// getWappenPreviewTexture
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.libMaintenance,
    """
     取得した「Not Sewing」の情報に誤りがあるため、画像が間違っています。
    """,
  )
  ({EmbLibraryError errCode, Uint8List image}) getWappenPreviewTexture();

  ///
  /// completeSelectedApplique
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.libMaintenance,
    """
      editSelectedAppliqueAPi がクラッシュし、API の検証に失敗する
    """,
  )
  EmbLibraryError completeSelectedApplique();

  ///
  /// スノーマンスキャンを開始する
  ///
  /// 戻り値 : エラーコード
  ///
  EmbLibraryError embSewingSnowmanDirSelect(EmbDirSelect dir);

  ///
  /// 縫いページ遷移前の設定
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing_startPoint,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  EmbLibraryError embSewingStartPositionSet();

  ///
  /// 縫い開始のPositionを設定する
  /// マスクトレースのPositionを設定する
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing_startPoint,
    ApiStatus.doTest,
    """
    """,
  )
  EmbLibraryError embSewingMaskTracePosSet(int pos);

  ///
  /// 縫い開始
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing_startPoint,
    ApiStatus.libMaintenance,
    """
    """,
  )
  EmbLibraryError embSewingSelectStartPosition();

  ///
  /// TODO:APIの作用を確認してください。
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing_startPoint,
    ApiStatus.libMaintenance,
    "",
  )
  EmbLibraryError embSewingStartPositionClear();

  ///
  /// 縫い完了
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing_startPoint,
    ApiStatus.doTest,
    """
    Ui実装しました、画面遷移できる
    """,
  )
  EmbLibraryError embSewingStartPositionEnd();

  ///
  /// 反転不可且つ文字模様でないかどうか確認する
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embCheckPattern,
    ApiStatus.libMaintenance,
    """
    """,
  )
  ({EmbLibraryError errCode, bool attr}) checkPatternNoMirrorNoCharacter(
      MemHandle patternH);

  ///
  /// マスクトレースページ遷移前の設定
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing_maskTrace,
    ApiStatus.doLibImpUnit,
    """
    Ui実装しました、画面遷移できる
    """,
  )
  EmbLibraryError embSewingSelectMaskTrace();

  ///
  /// マスクトレースを実施
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing_maskTrace,
    ApiStatus.doLibImpUnit,
    """
    Ui実装しました
    """,
  )
  EmbLibraryError embSewingMaskTraceExe();

  ///
  /// マスクトレース実施完了
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.embSewing_maskTrace,
    ApiStatus.doTest,
    """
    """,
  )
  EmbLibraryError embSewingMaskTraceEnd();

  ///
  /// 失敗またはキャンセル時の点描模様
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.stipple,
    ApiStatus.doTest,
    """
    """,
  )
  EmbLibraryError failureCancelStipple();

  ///
  /// Border模様糸印はを削除する
  /// ERR_DELETE_BORDER_MARK_OK_EASYSTTIPLE の　OKキーでだけ　使用できます
  ///
  /// 戻り値 : エラーコード
  ///
  @EmbApiResult(
    EmbApiFunction.border,
    ApiStatus.doTest,
    """
    """,
  )
  EmbLibraryError deleteBorderMarkForStippleError();

  ///
  /// cancelSelectedApplique
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.libMaintenance,
    """
    """,
  )
  EmbLibraryError cancelSelectedApplique();

  ///
  /// goAppliqueParameterSetting
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.libMaintenance,
    """
    """,
  )
  EmbLibraryError goAppliqueParameterSetting();

  ///
  /// deleteEmbGrpPatternInfo
  ///
  @EmbApiResult(
    EmbApiFunction.applique,
    ApiStatus.libMaintenance,
    """
    """,
  )
  void deleteEmbGrpPatternInfo();
}
