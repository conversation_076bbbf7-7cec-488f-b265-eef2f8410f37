import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'thickness_setting_popup_view_model.dart';

class ThicknessSettingPopup extends ConsumerStatefulWidget {
  const ThicknessSettingPopup({super.key});

  @override
  ConsumerState<ThicknessSettingPopup> createState() =>
      _ThicknessSettingPopupState();
}

class _ThicknessSettingPopupState extends ConsumerState<ThicknessSettingPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(thicknessSettingPopupViewModelProvider);
    final viewModel = ref.read(thicknessSettingPopupViewModelProvider.notifier);

    return Row(
      children: [
        const Spacer(flex: 571),
        Expanded(
          flex: 229,
          child: Column(
            children: [
              const Spacer(flex: 159),
              Expanded(
                flex: 1052,
                child: Scaffold(
                  backgroundColor: Colors.transparent,
                  body: pre_edit_toolbar_mdc(
                    child: Column(
                      children: [
                        const Spacer(flex: 129),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 205,
                                child: grp_str_size(
                                  text: l10n.icon_00564,
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 89),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 98,
                                child: CustomTooltip(
                                  message: l10n.tt_mdc_decofillthickness2_3,
                                  child: grp_btn_mdc_decofill_thickness_thick(
                                    onTap: viewModel.onThickButtonClicked,
                                    state: state.isThickButtonSelected
                                        ? ButtonState.select
                                        : ButtonState.normal,
                                  ),
                                ),
                              ),
                              const Spacer(flex: 9),
                              Expanded(
                                flex: 98,
                                child: CustomTooltip(
                                  message: l10n.tt_mdc_decofillthickness1_2,
                                  child: grp_btn_mdc_decofill_thickness_narrow(
                                    onTap: viewModel.onNarrowButtonClicked,
                                    state: state.isNarrowButtonSelected
                                        ? ButtonState.select
                                        : ButtonState.normal,
                                    feedBackControl: null,
                                  ),
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 613),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 205,
                                child: grp_btn_positive_mdc(
                                  onTap: viewModel.onOkButtonClicked,
                                  text: l10n.icon_ok,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 12),
                      ],
                    ),
                  ),
                ),
              ),
              const Spacer(flex: 69),
            ],
          ),
        ),
      ],
    );
  }
}
