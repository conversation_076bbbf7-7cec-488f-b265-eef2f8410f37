import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../model/pattern_data_reader/category_image_reader.dart';
import '../../../../model/preview_model.dart' show PatternDisplayInfo;

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'category_selector_view_interface.freezed.dart';

enum CategorySelectorModuleType {
  patternSelector,
  selectInformation,
  bhSelector,
  memorySelectorSet,
  filter,
  preview,
}

enum CategorySelectorPageSubPopupEnum {
  characterFont,
  characterPattern,
  onePointPattern,
  disneyPattern,
  longStitch,
  exclusives,
  information,
  bhPattern,
  quiltPattern,
  frame,
  couching,
  largeConnect,
  memory,
}

///「現在のサムネイルがディズニーかどうかを判別するためのインデックス」
const int disneyIndex = 8;

@freezed
class CategorySelectorState with _$CategorySelectorState {
  const factory CategorySelectorState({
    @Default(0) int frameIndex,
    @Default(true) bool showZoomButton,
    @Default(false) bool showZoomList,
    @Default(100) int selectedZoomScale,
    @Default(false) bool showInformationButton,
    @Default('') String selectedCategoryName,
    @Default('----') String heightValue,
    @Default('----') String widthValue,
    @Default('----') String totalTimeValue,
    @Default('----') String numberOfColorsValue,
    @Default('----') String colorChangesValue,
    @Default(false) bool showFilterButton,
    @Default(false) bool isFilterApplied,
    @Default(false) bool isQuiltPattern,
    @Default(false) bool isUnitMm,
    @Default(false) bool isEnterPattern,
    @Default(null) Widget? quiltImage,
    @Default(ButtonState.disable) ButtonState formationButtonState,
    @Default(ButtonState.disable) ButtonState realPreviewButtonState,

    /// Pattern表示情報
    @Default([]) List<PatternDisplayInfo> patternDisplayInfoList,
    @Default([]) List<PatternDisplayInfo> temporaryGroupDisplayInfoList,
  }) = _CategorySelectorState;
}

abstract class CategorySelectorViewInterface
    extends ViewModel<CategorySelectorState> {
  CategorySelectorViewInterface(super.state, this.ref);

  ScrollController scrollController = ScrollController();

  Ref ref;

  ///
  /// 全てのカテゴリのイコンデータを取得する
  ///
  /// ##@return
  /// - List<CategoryImageGroup>: 順番保存されているの模様イコンデータ
  ///
  List<CategoryImageGroup> getAllCategoryImagesInfo();

  ///
  /// 倍率選択ポップアップを開くためのクリック関数
  ///
  void onZoomButtonClicked();

  ///
  /// 詳細ポップアップを開くためのクリック関数
  ///
  void onInformationButtonClicked(BuildContext context);

  ///
  /// リアルプレビューポップアップを開くためのクリック関数
  ///
  void onRealPreviewButtonClicked();

  ///
  /// 大カテゴリのクリック関数
  ///
  void onCategoryButtonClicked(int categoryIndex);

  ///
  /// フィルターボタンのクリック関数
  ///
  void onFilterButtonClicked(BuildContext context);

  ///
  /// フィルターバーの×ボタンのクリック関数
  ///
  void onFilterCloseButtonClicked();

  ///
  /// 名前付きルートの登録
  ///
  Map<String, PopupRouteBuilder> registerNamedPopup();

  void updateTopPageByChild(CategorySelectorModuleType vm);

  ///
  /// スタイル写真刺繍画面入口
  ///
  void onPicturePlayButtonClicked();

  ///
  /// MDC画面入口
  ///
  void onMDCButtonClicked();

  ///
  /// 表示倍率値リスト
  ///
  List<int> get zoomDisplayList;

  ///
  /// 倍率値リストを閉じる
  ///
  void closeZoomPopup();

  ///
  /// 倍率値変更
  ///
  void onZoomPopupListClick(int zoomValue);

  ///
  /// Returnのクリック関数
  ///
  void onReturnButtonClicked();

  ///
  /// Favoriteのクリック関数
  ///
  void onFavoriteButtonClicked();

  ///
  /// EmbThreadCuttingのクリック関数
  ///
  void onEmbThreadCuttingButtonClicked();

  ///
  /// カテゴリメモリクリック機能
  ///
  void onMemoryCategoryButtonClicked();

  ///
  /// 模様追加モードかどうか
  ///
  bool isAddModel();

  ///
  ///カテゴリーのヒントを得る
  ///
  String getCategoryTips(int index);

  ///
  /// 選択されているものを取得します icon
  ///
  Widget getSelectedZoomIcon();

  ///
  ///BrotherまたはTaconyのデバイスを入手する
  ///
  bool isBrother();

  ///
  /// カテゴリ名を更新する。
  ///
  String getSubstringBeforeSpace(int index);
}
