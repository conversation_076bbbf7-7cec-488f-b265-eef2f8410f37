import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import 'err_change_except_s_frame_view_interface.dart';

final errChangeExceptSFrameViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrChangeExceptSFrameViewInterface, ErrChangeExceptSFrameState,
            BuildContext>(
        (ref, context) => ErrChangeExceptSFrameViewModel(ref, context));

class ErrChangeExceptSFrameViewModel
    extends ErrChangeExceptSFrameViewInterface {
  ErrChangeExceptSFrameViewModel(Ref ref, BuildContext context)
      : super(const ErrChangeExceptSFrameState(), ref, context);

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() => TpdLibrary()
      .apiBinding
      .bpIFSendDisplayDataSync(BPIFSendKey.KEYERREXCEPTSFRAMESET);
}
