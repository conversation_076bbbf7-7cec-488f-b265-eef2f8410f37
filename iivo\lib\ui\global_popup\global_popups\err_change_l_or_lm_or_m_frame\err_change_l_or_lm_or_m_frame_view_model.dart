import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_change_l_or_lm_or_m_frame_view_interface.dart';

final errChangeLOrLmOrMFrameViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrChangeLOrLmOrMFrameViewInterface,
            ErrChangeLOrLmOrMFrameState, BuildContext>(
        (ref, context) => ErrChangeLOrLmOrMFrameViewModel(ref, context));

class ErrChangeLOrLmOrMFrameViewModel
    extends ErrChangeLOrLmOrMFrameViewInterface {
  ErrChangeLOrLmOrMFrameViewModel(Ref ref, BuildContext context)
      : super(const ErrChangeLOrLmOrMFrameState(), ref, context);

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    final int errCode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERRFRAMEOFF);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }
}
