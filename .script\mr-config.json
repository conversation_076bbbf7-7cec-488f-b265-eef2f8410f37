﻿{
  "GitLab": {
    "ApiUrl": "https://gitlab.phc.brother.co.jp/api/v4",
    "AccessToken": "**************************",
    "AssigneeId": 6,
    "ReviewerIds": [
      6,
      73
    ],
    "Projects": [
      {
        "Id": "12",
        "Name": "ph-iivo-app",
        "RelativePath": "../iivo"
      },
      {
        "Id": "51",
        "Name": "ph-app-common",
        "RelativePath": "../ph-app-common"
      },
      {
        "Id": "53",
        "Name": "ph-ui-component",
        "RelativePath": "../ph-ui-component"
      },
      {
        "Id": "36",
        "Name": "ph-eel-testmode",
        "RelativePath": "../iivo/lib/ui/app/testmode"
      },
      {
        "Id": "13",
        "Name": "ph-iivo-data",
        "RelativePath": "../iivo/ph-fs-data"
      }
    ]
  },
  "Jira": {
    "User": "<EMAIL>",
    "ApiToken": "ATATT3xFfGF0hL5_up_mWdWNHRlbVUifD_drwzo3wACNUapuPfN_u0PUjbtWqv-lpq706ubd65JGflRHlxF72mLSoc39LIlmV4iLbxTXMS7bxjHklPhG568Ke8tPQN5yftMTEwIEzo_oSI9divt6pXF5GDmCEmk2kfuqiQEo4v_rmAr4c7mEPLQ=1D7EC227"
  },
  "Options": {
    "BranchPrefixes": [
      {
        "Name": "fix",
        "Description": "Fixing bugs"
      },
      {
        "Name": "feat",
        "Description": "New feature development"
      },
      {
        "Name": "story",
        "Description": "Jira story development"
      },
      {
        "Name": "refactor",
        "Description": "Refactoring"
      },
      {
        "Name": "perf",
        "Description": "Performance optimization"
      },
      {
        "Name": "style",
        "Description": "Code style"
      },
      {
        "Name": "test",
        "Description": "Adding tests"
      },
      {
        "Name": "chore",
        "Description": "Changing build process, or added dependencies, tools, etc."
      },
      {
        "Name": "docs",
        "Description": "Adding documentation"
      },
      {
        "Name": "revert",
        "Description": "Reverting changes"
      },
      {
        "Name": "ci",
        "Description": "Changing CI configuration"
      },
      {
        "Name": "build",
        "Description": "Changing build process"
      },
      {
        "Name": "init",
        "Description": "Initial commit"
      }
    ],
    "AutoDeleteBranch": true,
    "TargetBranch": "dev_after_BEX25"
  }
}