import 'dart:async';
import 'dart:convert';

import 'package:network_wifi/network_wifi.dart';
import 'package:path/path.dart';
import 'package:system_config/system_config.dart';
import 'package:usb_manager/usb_manager.dart';

import '../iivo_export.dart';
import '../update_app_log.dart';

///
///メモリへ書き出し先の種類
///
enum DeviceKind {
  usb,
  wLan,
}

///
///０：OTAパッケージ
///１：HOMEアプリ
///２：パネル、SSアプリ
///３：メインプログラム
///４：取説
///５：eula
///６：模様データ
///７：パネルライブラリ
///
class UpgradeType {
  static const int ota = 0;
  static const int home = 1;
  static const int panel = 2;
  static const int main = 3;
  static const int manual = 4;
  static const int eula = 5;
  static const int panelData = 6;
  static const int panelLib = 7;
}

///
/// アップデートモードでエラー識別表示
/// errorCode: 番号を示す
/// errorCodeMessage: 標識を表す文言
///
class UpgradeError {
  const UpgradeError._internal(
      {required int errorCode, required String errorCodeMessage})
      : _errorCode = errorCode,
        _errorCodeMessage = errorCodeMessage;
  final int _errorCode;
  final String _errorCodeMessage;

  int get errorCode => _errorCode;
  String get errorCodeMessage => _errorCodeMessage;

  static const UpgradeError errorCodeNone =
      UpgradeError._internal(errorCode: 0, errorCodeMessage: "E-XX");
  static const UpgradeError errorCode1 =
      UpgradeError._internal(errorCode: 1, errorCodeMessage: "E-01");
  static const UpgradeError errorCode2 =
      UpgradeError._internal(errorCode: 2, errorCodeMessage: "E-02");
  static const UpgradeError errorCode3 =
      UpgradeError._internal(errorCode: 3, errorCodeMessage: "E-03");
  static const UpgradeError errorCode4 =
      UpgradeError._internal(errorCode: 4, errorCodeMessage: "E-04");
  static const UpgradeError errorCode5 =
      UpgradeError._internal(errorCode: 5, errorCodeMessage: "E-05");
  static const UpgradeError errorCode6 =
      UpgradeError._internal(errorCode: 6, errorCodeMessage: "E-06");
  static const UpgradeError errorCode7 =
      UpgradeError._internal(errorCode: 7, errorCodeMessage: "E-07");
  static const UpgradeError errorCode8 =
      UpgradeError._internal(errorCode: 8, errorCodeMessage: "E-08");
  static const UpgradeError errorCode9 =
      UpgradeError._internal(errorCode: 9, errorCodeMessage: "E-09");
  static const UpgradeError errorCode10 =
      UpgradeError._internal(errorCode: 10, errorCodeMessage: "E-10");
  static const UpgradeError errorCode11 =
      UpgradeError._internal(errorCode: 11, errorCodeMessage: "E-11");
  static const UpgradeError errorCode12 =
      UpgradeError._internal(errorCode: 12, errorCodeMessage: "E-12");
  static const UpgradeError errorCode13 =
      UpgradeError._internal(errorCode: 13, errorCodeMessage: "E-13");
  static const UpgradeError errorCode14 =
      UpgradeError._internal(errorCode: 14, errorCodeMessage: "E-14");
  static const UpgradeError errorCode15 =
      UpgradeError._internal(errorCode: 15, errorCodeMessage: "E-15");
  static const UpgradeError errorCode16 =
      UpgradeError._internal(errorCode: 16, errorCodeMessage: "E-16");
  static const UpgradeError errorCode17 =
      UpgradeError._internal(errorCode: 17, errorCodeMessage: "E-17");
  static const UpgradeError errorCode18 =
      UpgradeError._internal(errorCode: 18, errorCodeMessage: "E-18");
  static const UpgradeError errorCode19 =
      UpgradeError._internal(errorCode: 19, errorCodeMessage: "E-19");
  static const UpgradeError errorCode20 =
      UpgradeError._internal(errorCode: 20, errorCodeMessage: "E-20");
  static const UpgradeError errorCode21 =
      UpgradeError._internal(errorCode: 21, errorCodeMessage: "E-21");
  static const UpgradeError errorCode22 =
      UpgradeError._internal(errorCode: 22, errorCodeMessage: "E-22");
  static const UpgradeError errorCode23 =
      UpgradeError._internal(errorCode: 23, errorCodeMessage: "E-23");
  static const UpgradeError errorCode24 =
      UpgradeError._internal(errorCode: 24, errorCodeMessage: "E-24");
  static const UpgradeError errorCode25 =
      UpgradeError._internal(errorCode: 25, errorCodeMessage: "E-25");
  static const UpgradeError errorCode26 =
      UpgradeError._internal(errorCode: 26, errorCodeMessage: "E-26");
  static const UpgradeError errorCode27 =
      UpgradeError._internal(errorCode: 27, errorCodeMessage: "E-27");
  static const UpgradeError errorCode28 =
      UpgradeError._internal(errorCode: 28, errorCodeMessage: "E-28");
  static const UpgradeError errorCode29 =
      UpgradeError._internal(errorCode: 29, errorCodeMessage: "E-29");
  static const UpgradeError errorCode30 =
      UpgradeError._internal(errorCode: 30, errorCodeMessage: "E-30");
  static const UpgradeError errorCode31 =
      UpgradeError._internal(errorCode: 31, errorCodeMessage: "E-31");
  static const UpgradeError errorCode32 =
      UpgradeError._internal(errorCode: 32, errorCodeMessage: "E-32");
  static const UpgradeError errorCode33 =
      UpgradeError._internal(errorCode: 33, errorCodeMessage: "E-33");
  static const UpgradeError errorCode34 =
      UpgradeError._internal(errorCode: 34, errorCodeMessage: "E-34");
  static const UpgradeError errorCode35 =
      UpgradeError._internal(errorCode: 35, errorCodeMessage: "E-35");
  static const UpgradeError errorCode36 =
      UpgradeError._internal(errorCode: 36, errorCodeMessage: "E-36");
  static const UpgradeError errorCode37 =
      UpgradeError._internal(errorCode: 37, errorCodeMessage: "E-37");
  static const UpgradeError errorCode38 =
      UpgradeError._internal(errorCode: 38, errorCodeMessage: "E-38");
  static const UpgradeError errorCode39 =
      UpgradeError._internal(errorCode: 39, errorCodeMessage: "E-39");
  static const UpgradeError errorCode40 =
      UpgradeError._internal(errorCode: 40, errorCodeMessage: "E-40");
  static const UpgradeError errorCode41 =
      UpgradeError._internal(errorCode: 41, errorCodeMessage: "E-41");
  static const UpgradeError errorCode42 =
      UpgradeError._internal(errorCode: 42, errorCodeMessage: "E-42");
  static const UpgradeError errorCode43 =
      UpgradeError._internal(errorCode: 43, errorCodeMessage: "E-43");
  static const UpgradeError errorCode44 =
      UpgradeError._internal(errorCode: 44, errorCodeMessage: "E-44");
  static const UpgradeError errorCode45 =
      UpgradeError._internal(errorCode: 45, errorCodeMessage: "E-45");
}

///
/// ページ表示用のファイル情報構造体
///
class FolderType {
  const FolderType(
      {required this.fileName, required this.path, required this.isDirectory});
  final String fileName;
  final String path;
  final bool isDirectory;
}

class UpgradeModel {
  UpgradeModel._internal();

  factory UpgradeModel() => _instance;
  static final UpgradeModel _instance = UpgradeModel._internal();

  String _packageName = "unknown";
  String _upgVersion = "unknown";

  /// 現在使用中の USB パス
  String? _currentUsbTransactionPath;

  /// USBトランザクションが現在処理中かどうか
  bool get isInUsbTransaction => usbPath == _currentUsbTransactionPath;

  ///
  /// USBトランザクションを開始する準備をし、USBをマウントします
  ///
  Future<void> beginUsbTransactionIfNeeded() async {
    if (deviceKind == DeviceKind.usb &&
        usbPath.startsWith(memorySector.external_usb.absolutePath)) {
      _currentUsbTransactionPath = usbPath;
      await UsbManager().mountUsbDevice(usbPath);
    } else {
      // no-op
    }
  }

  ///
  /// USB トランザクションを終了し、USB をアンインストールする
  ///
  Future<void> endUsbTransactionIfNeeded() async {
    if (isInUsbTransaction) {
      await UsbManager().unmountUsbDevice(usbPath);
      _currentUsbTransactionPath = null;
    } else {
      // no-op
    }
  }

  ///
  /// upgVersion初期化の時に、一回取得します
  ///
  /// 以下二の理由でこの関数を追加しました：
  /// upgVersionはアプリ生きているの間に変更はない
  /// upgVersionの取得は 非同期です
  ///
  Future<void> initUpgVersion() async {
    /// package name
    final packageName = await SystemConfig.getCurrentAppPackageName();
    if (packageName == null) {
      return;
    } else {
      _packageName = packageName;
    }

    /// apk version
    final upgVersion =
        await SystemConfig.getApplicationVersionName(_packageName);
    if (upgVersion == null) {
      return;
    } else {
      _upgVersion = upgVersion;
    }
  }

  ///
  /// バージョン番号
  ///
  String get upgVersion => _upgVersion;

  ///
  /// UPGVersionフェッチ
  /// TODO:http://apngoap306.ap.brothergroup.net:8080/jira/browse/PHBSH-2308?jql=text%20~%20%22upgrade%22
  ///
  String version = "";

  ///
  /// 仕様切り替え番号
  ///
  String? destSpec;

  final String _upgradeSubDir = "upgrade";

  final String _upfHeaderJson = "UPFHeader.json";
  final String _componentListKey = "componentlist";
  final String _typeKey = "type";
  final String _versionKey = "version";
  final String _txtFileName = "updateCount.txt";
  final String _logcatFileName = "update_err_log.logcat";
  final String _upfHeaderJsonHash = "upfHeaderHash.txt";

  ///
  /// UPFHeader.json,FileUpdate.jsonのフォルダーパスを保存します
  ///
  String get upgradeSavePath =>
      join(memorySector.brother_dir.absolutePath, _upgradeSubDir);

  ///
  /// UPFHeader.jsonのファイルパスを保存します
  ///
  String get upfHeaderSavePath => join(upgradeSavePath, _upfHeaderJson);

  ///
  /// USBパス
  ///
  String usbPath = "";

  ///
  /// 選択済みのUPFがある
  ///
  FolderType chooseUpf =
      const FolderType(fileName: "", path: '', isDirectory: false);

  ///
  /// ローカルに対応するバージョン番号を取得
  /// type: 参考 [UpgradeType]
  /// return: ローカルのtypeのバージョン番号は、空の可能性がある
  /// null: ローカルのバージョン番号が取得できなかったので、バージョン比較を行わず直接更新を行います
  ///
  Future<int?> getLocalVersion(int type) async {
    final Map<int, int> localUpdateFileJson = await _getLocalUpfHeaderJson();
    return localUpdateFileJson[type];
  }

  ///
  /// 選択した更新プログラムの種類
  ///
  DeviceKind deviceKind = DeviceKind.usb;

  ///
  /// アップグレードモードに入っているか
  ///
  bool get isOpenWithUpgrade => false;

  ///
  /// USBが現在リンクされているかどうか
  ///
  bool get isUSBConnect => true;

  ///
  /// wifiが現在リンクされているかどうか
  ///
  bool get isWifiConnect => WifiManager().hasInternet();

  ///
  /// インストール完了画面Error表示メッセージ
  ///
  String endErrorTitle = "";

  ///
  /// ダウンロードしたupf集合
  ///
  List<FolderType> downloadedUpfCollection = [];

  ///
  /// スナップショットの再撮影を実施する必要があるかどうか
  ///
  bool needSnapshotWhenSkipOTAUpgrade = false;

  ///
  /// 更新が必要なupfの総数のファイルパスを保存
  ///
  String get txtSavePath =>
      join(memorySector.brother_dir.absolutePath, _txtFileName);

  ///
  /// logcatの保存パス
  ///
  String get _logcatSavePath => join(upgradeSavePath, _logcatFileName);

  ///
  /// 更新失敗のログ情報を保存する
  ///
  void saveUpdateLogCat() {
    try {
      FileEntity saveFile = FileEntity(_logcatSavePath);
      if (saveFile.existsSync() == false) {
        saveFile.createSync(recursive: true);
      }
      SystemConfig.saveUpdateErrLog(_logcatSavePath);
    } catch (e) {
      UpdateAppLog.e("saveUpdateLogCat fail:$e");
    }
  }

  ///
  /// この更新プログラムのUPFHeader.jsonファイルを保存します
  /// true:保存に成功した false:保存に失敗した
  ///
  Future<bool> saveUpfHeaderJson(String upfHeaderJsonFilePath) async {
    try {
      final brotherDirSaveDirectory = DirectoryEntity(upgradeSavePath);
      if (!brotherDirSaveDirectory.existsSync()) {
        brotherDirSaveDirectory.createSync();
      }
      UpdateAppLog.d("Start saving UPFHeader.json...");

      final FileEntity upfHeaderJsonFile = FileEntity(upfHeaderJsonFilePath);

      final isSaveSuccess = await _saveUpfHeaderHash(upfHeaderJsonFile.path);
      if (isSaveSuccess == false) {
        return false;
      }

      if (upfHeaderJsonFile.existsSync()) {
        upfHeaderJsonFile.copySync(upfHeaderSavePath);
      }

      return true;
    } catch (e) {
      UpdateAppLog.e("Saving UPFHeader.json failed:$e");
      return false;
    }
  }

  ///
  /// 前回インストールしたときのUPFHeader.jsonを開く
  /// Map<int, int>: Map<[UpgradeType],対応するバージョン番号>
  /// ローカルにUPFHeader.jsonが存在しないまたは破損した場合、返却値は{}です
  ///
  Future<Map<int, int>> _getLocalUpfHeaderJson() async {
    try {
      final bool isUPFHeaderIntact = await _checkLocalUpfHeaderHash();
      UpdateAppLog.d("UPFHeader.json intact: $isUPFHeaderIntact");
      if (isUPFHeaderIntact == false) {
        return {};
      }

      final Map<int, int> lastUpfHeaderMap = {};

      if (FileEntity(upfHeaderSavePath).existsSync() == false) {
        return {};
      }

      final String jsonString =
          FileEntity(upfHeaderSavePath).readAsStringSync();
      final Map<String, dynamic> jsonData = jsonDecode(jsonString);
      if (jsonData.containsKey(_componentListKey)) {
        final List<Map<String, dynamic>> componentList =
            jsonData[_componentListKey].cast<Map<String, dynamic>>();

        for (var component in componentList) {
          if (component.containsKey(_typeKey) &&
              component.containsKey(_versionKey)) {
            final int type = component[_typeKey];
            final int version = component[_versionKey];
            lastUpfHeaderMap.addAll({type: version});
          }
        }

        return lastUpfHeaderMap;
      } else {
        return {};
      }
    } catch (e) {
      UpdateAppLog.e("getLastUpfHeaderJson error:$e");
      return {};
    }
  }

  ///
  /// UPFHeader.jsonの整合性を確認する
  ///
  Future<bool> _checkLocalUpfHeaderHash() async {
    try {
      final String filaHash =
          await UsbManager().calculateSHA256(upfHeaderSavePath);
      final FileEntity fileEntity = FileEntity(_upfHeaderHashSavePath);
      if (fileEntity.existsSync()) {
        return filaHash == fileEntity.readAsStringSync();
      } else {
        return false;
      }
    } catch (e) {
      UpdateAppLog.e("checkLastUpfHeaderHash error:$e");
      return false;
    }
  }

  ///
  /// UPFHeader.json のハッシュ値を保存する
  ///
  Future<bool> _saveUpfHeaderHash(String updHeaderJsonPath) async {
    try {
      String fileHash = await UsbManager().calculateSHA256(updHeaderJsonPath);

      FileEntity upfHeaderHashFile = FileEntity(_upfHeaderHashSavePath);
      if (upfHeaderHashFile.existsSync() == false) {
        upfHeaderHashFile.createSync(recursive: true);
      }
      upfHeaderHashFile.writeAsStringSync(fileHash, flush: true);

      return true;
    } catch (e) {
      UpdateAppLog.e("Saving UPFHeader.json hashValue failed:$e");
      return false;
    }
  }

  ///
  /// UPFHeader.json のハッシュ値を保存するパス
  ///
  String get _upfHeaderHashSavePath =>
      join(upgradeSavePath, _upfHeaderJsonHash);
}
