import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_foot_controler_not_use_view_interface.dart';

final errFootControlerNotUseViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrFootControlerNotUseViewInterface,
            ErrFootControlerNotUseState, BuildContext>(
        (ref, context) => ErrFootControlerNotUseViewModel(ref, context));

class ErrFootControlerNotUseViewModel
    extends ErrFootControlerNotUseViewInterface {
  ErrFootControlerNotUseViewModel(Ref ref, BuildContext context)
      : super(const ErrFootControlerNotUseState(), ref, context);

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    final int errCode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERRORFOTJACKOUT);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }
}
