import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../model/app_locale.dart';
import '../../../../../../model/camera_model.dart';
import '../../../../../../model/header_model.dart';
import '../../../../../../model/machine_config_model.dart';
import '../../../../../../model/projector/camera_pen/camera_pen_type.dart';
import '../../../../../../model/projector_model.dart';
import '../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../component/common_header/common_header_view_interface.dart';
import '../../../../../component/common_header/common_header_view_model.dart';
import '../../../../../global_popup/global_popup_export.dart';
import '../../../../../global_popup/global_popup_route.dart';
import '../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../page_route/page_route.dart';
import '../../../../home/<USER>/home_model.dart';
import '../../../../home/<USER>/component/home_header/bwd_popup/bwd_popup_view_model.dart';
import '../../../../setting/model/setting_model.dart';
import '../../../model/camera_image_model.dart';
import '../../../model/guide_line_model.dart';
import '../../../model/pattern_data_type/pattern_property.dart';
import '../../../model/pattern_model.dart';
import '../../../model/preview_model.dart';
import '../../../model/tapering_model.dart';
import '../../../model/utl_reset_model.dart';
import '../../page/sewing/character/character_view_interface.dart' as character;
import '../../page/sewing/character/character_view_model.dart'
    as character_view_model;
import '../../page/sewing/common/camera_pen/camera_pen_view_model.dart';
import '../../page/sewing/common/top_bar/top_bar_view_model.dart';
import '../../page/sewing/utility/utility_view_interface.dart' as utility;
import '../../page/sewing/utility/utility_view_model.dart'
    as utility_view_model;
import '../../page/stitch_regulator/stitch_regulator_view_model.dart';

/// view _modelに必要な構造
final utlHeaderViewModelProvider =
    AutoDisposeStateNotifierProvider<UtlHeaderViewModel, CommonHeaderState>(
        (ref) => UtlHeaderViewModel(ref));

class UtlHeaderViewModel extends CommonHeaderViewModel
    with DeviceLibraryEventObserver {
  UtlHeaderViewModel(
    ref,
  ) : super(ref);

  @override
  void build() {
    super.build();

    /// Model更新
    MachineConfigModel().baseMode = SettingBaseMode.utl;
    MachineConfigModel().currentMode = SettingBaseMode.utl;

    /// View更新
    update();

    /// bwd接続、カメラ機能閉じる
    ref.listen(
      appDisplayGlobalStateProvider
          .select((value) => value.bwdPanelState.ref.IsOn),
      (previous, nextState) {
        if (nextState != ENUM_LIB.FALSE) {
          /// カメラ機能閉じる
          final cameraRet = maybeCloseCamera();
          if (cameraRet == true) {
            /// ボタン状態更新
            state = state.copyWith(cameraButtonState: ButtonState.normal);
          }
        } else {
          /// Do Nothing
        }
      },
    );

    /// 縫製中、カメラ機能閉じる
    ref.listen(
        appDisplayUtlStateProvider.select(
            (value) => value.utlFuncSetting.ref.isUtlSewing), (_, next) {
      if (next == true) {
        /// 実用画面プレビューカメラ画像表示している
        /// 縫製を起動なら、縫製停止した後にカメラ再開が必要なので
        /// ここは閉じらない
        if (TpdLibrary()
            .apiBinding
            .bpIFGetAppDisplayUtl()
            .utlFuncSetting
            .ref
            .isAspectUtlView) {
          return;
        }

        /// カメラ機能閉じる確認
        final cameraRet = maybeCloseCamera();
        if (cameraRet == true) {
          state = state.copyWith(cameraButtonState: ButtonState.normal);
        }
      } else {
        /// Do Nothing
      }
    });
  }

  @override
  void update() {
    if (!ref.exists(utlHeaderViewModelProvider)) {
      return;
    }

    ButtonState cameraButtonState = ButtonState.normal;

    /// 特殊模様の場合
    if (PatternDataModel().isNotSupportCameraPattern()) {
      cameraButtonState = ButtonState.disable;
    }

    /// 「終点設定」不可確認
    else if (PatternDataModel().isEndPointOn ||
        PatternDataModel().isEndPointPopupOn) {
      cameraButtonState = ButtonState.disable;
    }

    /// 「テーパリング」不可確認
    else if (TaperingModel().sewTaperingOnOFF) {
      cameraButtonState = ButtonState.disable;
    }

    /// 「プロジェクタ」不可確認
    else if (ProjectorModel().isUtlProjectorAllClosed() == false) {
      cameraButtonState = ButtonState.disable;

      /// カメラが開いている場合
    } else if (CameraModel().isCameraOpen == true) {
      cameraButtonState = ButtonState.select;
    }

    Locale locale = AppLocale().getCurrentLocale();

    /// View更新
    state = state.copyWith(
      cameraButtonState: cameraButtonState,
      cameraTipMessageString: lookupAppLocalizations(locale).tt_head_camera,
      homeTipMessageString: lookupAppLocalizations(locale).tt_head_home,
      lockTipMessageString: lookupAppLocalizations(locale).tt_head_lock,
      osaeTipMessageString: lookupAppLocalizations(locale).tt_head_osae,
      settingTipMessageString: lookupAppLocalizations(locale).tt_head_setting,
      teachingTipMessageString: lookupAppLocalizations(locale).tt_head_teaching,
      wifiTipMessageString: lookupAppLocalizations(locale).tt_head_wifi,
      isAppBadgeSenju: HeaderModel().getAppBadgeSenju(),
    );
  }

  @override
  void onCameraButtonClicked(BuildContext context) {
    /// 「Utl Preview」画面、「マイイラスト」画面でクリックできない。
    if (PreviewDataModel().isInRealPreview == true ||
        state.cameraButtonState == ButtonState.disable ||
        PatternDataModel().getPatternMode() == PatternMode.custom) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 「下糸巻き」ページをクリックした場合、無効になります
    else if (ref.exists(bwdPopupViewModelProvider) &&
        ref.read(bwdPopupViewModelProvider).bobbinPopupState == true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else {
      /// Do nothing
    }

    /// プロジェクト起動時　カメラボタン無効
    if (ProjectorModel().isUtlProjectorAllClosed() == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// カメラは開いており、閉じています
    if (state.cameraButtonState == ButtonState.select) {
      _closeUtlCamera();
      return;
    }

    /// カメラが開いていない場合は、カメラを開きます
    _openUtlCamera();
  }

  @override
  void onHomeButtonClicked(BuildContext context) {
    /// Home を確認すると lib にエラーが表示される
    final deviceError = TpdLibrary().apiBinding.checkGotoHome();
    if (deviceError == DirErrorCode.dirInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
        deviceError == DirErrorCode.dirInvalidPanelError) {
      return;
    }

    if (deviceError == DirErrorCode.dirRequiresConfirmation) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_SELECTED_STITCH_CANCEL_OK,
      );
      errSelectedStitchOkFunc = () {
        final DeviceErrorCode deviceError =
            GlobalPopupRoute().resetErrorState();
        if (deviceError != DeviceErrorCode.devNoError) {
          return;
        }

        /// カメラ機能閉じる
        final cameraRet = maybeCloseCamera();
        if (cameraRet == true) {
          state = state.copyWith(cameraButtonState: ButtonState.normal);
        }

        _onErrSelectedStitchGotoUtl();
      };

      errSelectedStitchCancelFunc = () {
        final DeviceErrorCode deviceError =
            GlobalPopupRoute().resetErrorState();
        if (deviceError != DeviceErrorCode.devNoError) {
          return;
        }
      };
      return;
    }

    if (deviceError == DirErrorCode.dirTransitionOK) {
      /// ホームに移行する前の状態のクリーンアップ
      ref
          .read(cameraPenViewModelProvider.notifier)
          .closeCameraPenAndCameraPenUI(needUpdateProjectorUI: false);

      /// カメラ機能閉じる
      final cameraRet = maybeCloseCamera();
      if (cameraRet == true) {
        state = state.copyWith(cameraButtonState: ButtonState.normal);
      }

      utlGoToHome();

      PreviewDataModel().setThreadColorsIndex(0);

      /// Homeへ
      MachineConfigModel().baseMode = SettingBaseMode.home;

      /// Homeへ
      HomeModel.geToHome();

      super.onHomeButtonClicked(context);
    } else {
      /// Do Nothing
    }

    return;
  }

  @override
  void onSettingButtonClicked(BuildContext context) {
    final errCode = TpdLibrary().apiBinding.gotoSettings();

    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    final bPIFError = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    if (bPIFError != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      if (bPIFError == ErrCode_t.ERR_EPS_FINISH.index) {
        handleErrEpsFinish();
      } else if (bPIFError == ErrCode_t.ERR_TAPERING_FINISH.index) {
        handleErrTaperingFinish();
      } else {
        /// Do Nothing
      }
      return;
    }

    if (errCode != DeviceErrorCode.devNoError) {
      return;
    }

    /// GuideLinePopupを閉じます。
    if (ref.exists(topBarViewModeProvider)) {
      if (ref.read(topBarViewModeProvider).guideLinePopupState) {
        PopupNavigator.pop(context: context);
      }
    } else if (ref.exists(stitchRegulatorViewModeProvider)) {
      if (ref.read(stitchRegulatorViewModeProvider).guideLinePopupState) {
        PopupNavigator.pop(context: context);
      }
    }

    /// プロジェクタを閉じます。
    if (ProjectorModel().isUtlProjectorAllClosed() == false) {
      GuideLineModel().guideLineShow = GuideLineShow.guideLineShowOFF;

      ProjectorModel().closeAllUtlProjector().then((value) async {
        UtlImageInfo? utlImageInfo =
            UtlLibrary().apiBinding.getCurrentPatternImage().imageInfo;
        PreviewDataModel().updatePreviewImage(utlImageInfo);
        await ref
            .read(cameraPenViewModelProvider.notifier)
            .closeCameraPenAndCameraPenUI(needUpdateProjectorUI: false);
        _updateView();
      });
    } else {
      /// Do noting
    }

    /// 「Utl Preview」画面をクリックしたら、「Utl Preview」画面を閉じます
    if (PreviewDataModel().isInRealPreview == true) {
      PagesRoute().pop();
    } else {
      /// Do noting
    }

    /// UTLはUTLの関連設定ページにジャンプする
    SettingModel().setSettingPageIndexWithOtherPage(SettingMode.utility);

    /// カメラ機能閉じる
    final cameraRet = maybeCloseCamera();
    if (cameraRet == true) {
      state = state.copyWith(cameraButtonState: ButtonState.normal);
    }

    super.onSettingButtonClicked(context);
    _updateView();
  }

  @override
  void onTeachingButtonClicked(BuildContext context) {
    final errCode = TpdLibrary().apiBinding.gotoHowToUse();
    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFError = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    if (bPIFError != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      if (bPIFError == ErrCode_t.ERR_EPS_FINISH.index) {
        errEpsFinishFunc = _errEpsFinishOKFunc;
      } else if (bPIFError == ErrCode_t.ERR_TAPERING_FINISH.index) {
        errTaperingFinishFunc = _errTaperingFinishFunc;
      } else {
        /// DO Nothing
      }
      return;
    }

    if (errCode != DeviceErrorCode.devNoError) {
      return;
    }

    /// GuideLinePopupを閉じます。
    if (ref.exists(topBarViewModeProvider)) {
      if (ref.read(topBarViewModeProvider).guideLinePopupState) {
        PopupNavigator.pop(context: context);
      }
    } else if (ref.exists(stitchRegulatorViewModeProvider)) {
      if (ref.read(stitchRegulatorViewModeProvider).guideLinePopupState) {
        PopupNavigator.pop(context: context);
      }
    }

    /// プロジェクタを閉じます
    if (ProjectorModel().isUtlProjectorAllClosed() == false) {
      GuideLineModel().guideLineShow = GuideLineShow.guideLineShowOFF;

      ProjectorModel().closeAllUtlProjector().then((_) async {
        UtlImageInfo? utlImageInfo =
            UtlLibrary().apiBinding.getCurrentPatternImage().imageInfo;
        PreviewDataModel().updatePreviewImage(utlImageInfo);
        await ref
            .read(cameraPenViewModelProvider.notifier)
            .closeCameraPenAndCameraPenUI(needUpdateProjectorUI: false);

        _updateView();
      });
    } else {
      /// Do noting
    }

    /// 「Utl Preview」画面をクリックしたら、「Utl Preview」画面を閉じます
    if (PreviewDataModel().isInRealPreview == true) {
      PagesRoute().pop();
    } else {
      /// Do noting
    }

    /// カメラ機能閉じる
    final cameraRet = maybeCloseCamera();
    if (cameraRet == true) {
      state = state.copyWith(cameraButtonState: ButtonState.normal);
    }

    super.onTeachingButtonClicked(context);
    _updateView();
  }

  @override
  void onWifiButtonClicked(BuildContext context) {
    final errCode = TpdLibrary().apiBinding.gotoWlanSetting();
    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFError = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    if (bPIFError != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      if (bPIFError == ErrCode_t.ERR_EPS_FINISH.index) {
        errEpsFinishFunc = _errEpsFinishOKFunc;
      } else if (bPIFError == ErrCode_t.ERR_TAPERING_FINISH.index) {
        errTaperingFinishFunc = _errTaperingFinishFunc;
      } else {
        /// Do Nothing
      }
      return;
    }

    if (errCode != DeviceErrorCode.devNoError) {
      return;
    }

    /// GuideLinePopupを閉じます。
    if (ref.exists(topBarViewModeProvider)) {
      if (ref.read(topBarViewModeProvider).guideLinePopupState) {
        PopupNavigator.pop(context: context);
      }
    } else if (ref.exists(stitchRegulatorViewModeProvider)) {
      if (ref.read(stitchRegulatorViewModeProvider).guideLinePopupState) {
        PopupNavigator.pop(context: context);
      }
    }

    /// プロジェクタを閉じます。
    if (ProjectorModel().isUtlProjectorAllClosed() == false) {
      GuideLineModel().guideLineShow = GuideLineShow.guideLineShowOFF;

      ProjectorModel().closeAllUtlProjector().then((value) async {
        UtlImageInfo? utlImageInfo =
            UtlLibrary().apiBinding.getCurrentPatternImage().imageInfo;
        PreviewDataModel().updatePreviewImage(utlImageInfo);
        await ref
            .read(cameraPenViewModelProvider.notifier)
            .closeCameraPenAndCameraPenUI(needUpdateProjectorUI: false);
        _updateView();
      });
    } else {
      /// Do noting
    }

    /// 「Utl Preview」画面をクリックしたら、「Utl Preview」画面を閉じます
    if (PreviewDataModel().isInRealPreview == true) {
      PagesRoute().pop();
    } else {
      /// Do noting
    }

    /// カメラ機能閉じる
    final cameraRet = maybeCloseCamera();
    if (cameraRet == true) {
      state = state.copyWith(cameraButtonState: ButtonState.normal);
    }

    super.onWifiButtonClicked(context);

    _updateView();
  }

  @override
  void onOsaeButtonClicked(BuildContext context) {
    /// CameraPopUpは開いており、閉じています
    if (TpdLibrary()
            .apiBinding
            .bpIFGetAppDisplayGlobal()
            .changeViewState
            .ref
            .isChangeView ==
        true) {
      /// カメラPopup画面閉じる
      closeCameraPopup();
    }

    /// カメラ機能閉じる
    final cameraRet = maybeCloseCamera();
    if (cameraRet == true) {
      /// ボタン状態更新
      state = state.copyWith(cameraButtonState: ButtonState.normal);
    }

    super.onOsaeButtonClicked(context);
  }

  ///
  /// カメラを閉じる
  ///
  @override
  bool maybeCloseCamera() {
    /// カメラは開いており、閉じています
    if (CameraModel().isCameraOpen || CameraModel().isUtlCameraPopupOpen) {
      /// カメラ閉じる
      CameraModel().isCameraOpen = false;
      CameraModel().isUtlCameraPopupOpen = false;

      /// カメラHard閉じる
      CameraImageModel.cameraImagePollingControl
          .stopCameraImagePollingAndRefreshUI();

      /// カメラPopup画面閉じる
      closeCameraPopup();

      return true;
    }

    return false;
  }

  ///
  /// [ERR_EPS_FINISH]時には、EndPointとプロジェクト閉じる処理が必要です
  ///
  void handleErrEpsFinish() {
    errEpsFinishFunc = _errEpsFinishOKFunc;
  }

  ///
  /// [ERR_TAPERING_FINISH]時には、TAPERINGとプロジェクト閉じる処理が必要です
  ///
  void handleErrTaperingFinish() {
    errTaperingFinishFunc = _errTaperingFinishFunc;
  }

  ///
  /// カメラを開きます
  ///
  void _openUtlCamera() {
    /// showChangeView
    final errCode = TpdLibrary().apiBinding.showChangeView();
    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      return;
    }

    if (errCode != DeviceErrorCode.devNoError) {
      return;
    }

    /// カメラ起動
    CameraModel().isCameraOpen = true;
    CameraImageModel.cameraImagePollingControl
        .startCameraImagePollingAndRefreshUI();

    /// 音再生
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// ボタン状態更新
    state = state.copyWith(cameraButtonState: ButtonState.select);
  }

  ///
  /// カメラ閉じる
  ///
  void _closeUtlCamera() {
    /// hideChangeView
    final errCode = TpdLibrary().apiBinding.hideChangeView();
    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      return;
    }

    if (errCode != DeviceErrorCode.devNoError) {
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// カメラ機能閉じる
    final cameraRet = maybeCloseCamera();
    if (cameraRet == true) {
      /// ボタン状態更新
      state = state.copyWith(cameraButtonState: ButtonState.normal);
    }
  }

  ///
  /// 画面を更新する
  ///
  void _updateView() {
    update();
    _updateOtherWidget();
  }

  ///
  /// その他画面を更新する
  ///
  void _updateOtherWidget() {
    if (PatternDataModel().getPatternMode() == PatternMode.character) {
      ref
          .read(character_view_model.characterViewModelProvider.notifier)
          .updateCharacterPageByChild(character.CharacterModuleType.header);
    } else if (PatternDataModel().getPatternMode() == PatternMode.utility) {
      ref
          .read(utility_view_model.utilityViewModelProvider.notifier)
          .updateUtilityPageByChild(utility.UtilityModuleType.header);
    } else if (PatternDataModel().getPatternMode() ==
        PatternMode.stitchRegulator) {
      ref
          .read(stitchRegulatorViewModeProvider.notifier)
          .updateSRModePageByChild(SRModuleType.header);
    } else {
      /// Do Nothing
    }
  }

  ///
  /// ポップアップのOKとcancelボタンのクリック関数
  ///
  void _onErrSelectedStitchGotoUtl() {
    utlGoToHome();

    PreviewDataModel().setThreadColorsIndex(0);

    /// Homeへ
    HomeModel.geToHome();

    PagesRoute().popUntil(nextRoute: PageRouteEnum.home);
    MachineConfigModel().baseMode = SettingBaseMode.home;
  }

  ///
  /// [ERR_TAPERING_FINISH]のokボタン
  ///
  void _errTaperingFinishFunc() {
    if (ProjectorModel().isUtlProjectorOpened(UtlProjectorType.endpoint)) {
      ProjectorModel().closeUtlProjector(UtlProjectorType.endpoint).then((_) {
        // Preview IMage更新：Preview_view_model.dartファイルのBuild内で更新を監視
        ref
            .read(utility_view_model.utilityViewModelProvider.notifier)
            .updateUtilityPageByChild(utility.UtilityModuleType.tapering);
      });
    } else {
      // Preview IMage更新：Preview_view_model.dartファイルのBuild内で更新を監視
      ref
          .read(utility_view_model.utilityViewModelProvider.notifier)
          .updateUtilityPageByChild(utility.UtilityModuleType.tapering);
    }
  }

  ///
  /// [ERR_EPS_FINISH]のokボタン
  ///
  void _errEpsFinishOKFunc() {
    if (ProjectorModel().isUtlProjectorOpened(UtlProjectorType.endpoint)) {
      ProjectorModel().closeUtlProjector(UtlProjectorType.endpoint).then((_) {
        if (PatternDataModel().getPatternMode() == PatternMode.utility) {
          /// 他の画面を更新する
          ref
              .read(utility_view_model.utilityViewModelProvider.notifier)
              .updateUtilityPageByChild(utility.UtilityModuleType.endPoint);
        } else if (PatternDataModel().getPatternMode() ==
            PatternMode.character) {
          /// 他の画面を更新する
          ref
              .read(character_view_model.characterViewModelProvider.notifier)
              .updateCharacterPageByChild(
                  character.CharacterModuleType.endPoint);
        }
      });
    } else {
      /// Do nothing
      Log.e(
          tag: "ERROR",
          description:
              "Unexpected code path reached. The 'else' block is not supposed to be executed.");
    }
  }
}
