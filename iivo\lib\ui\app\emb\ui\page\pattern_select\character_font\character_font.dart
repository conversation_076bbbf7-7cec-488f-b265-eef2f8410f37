import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../model/user_data/user_data.dart';
import '../../../../../../../model/user_data/user_data_keys.dart';
import '../../../../../../page_route/page_route.dart';
import '../../../component/emb_footer/emb_footer.dart';
import '../../../component/emb_header/emb_header.dart';
import 'character_font_view_model.dart';

class CharacterFontPage extends StatefulPage {
  const CharacterFontPage({super.key});

  @override
  PageState<StatefulPage> createState() => _CharacterFontState();
}

class _CharacterFontState extends PageState<CharacterFontPage> {
  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;

    final viewModel = ref.read(characterFontSelectorViewModelProvider.notifier);
    ref.watch(characterFontSelectorViewModelProvider);

    return Material(
      child: Column(
        children: [
          /// 上部のバー
          const Expanded(
            flex: 71,
            child: EmbHeader(),
          ),
          Expanded(
            flex: 1148,
            child: Stack(
              children: [
                const pre_base_embroidery(),
                Column(
                  children: [
                    Expanded(
                      flex: 88,
                      child: Row(
                        children: [
                          const Spacer(flex: 16),
                          Expanded(
                            flex: 43,
                            child: Transform.scale(
                              scale: 2.0,
                              child: Checkbox(
                                value: checkboxSelected,
                                onChanged: (value) {
                                  setState(() {
                                    checkboxSelected = !checkboxSelected;
                                    UserData().setBool(
                                        UserDataKeys.embIsOpenFontPreviewPopup,
                                        checkboxSelected);
                                  });
                                },
                                shape: const BeveledRectangleBorder(
                                  side: BorderSide(color: Colors.white),
                                ),
                                side: const BorderSide(color: Colors.white),
                              ),
                            ),
                          ),
                          const Spacer(flex: 14),
                          Expanded(
                            flex: 660,
                            child: Text(
                              l10n.icon_showpreview,
                              style: const TextStyle(
                                  fontSize: 28, color: Colors.white),
                            ),
                          ),
                          const Spacer(flex: 67),
                        ],
                      ),
                    ),

                    Expanded(
                      flex: 958,
                      child: Stack(
                        children: [
                          const pre_base_white(),
                          Column(
                            children: [
                              const Spacer(
                                flex: 20,
                              ),

                              /// ボックスをロックする
                              Expanded(
                                flex: 918,
                                child: Row(children: [
                                  const Spacer(flex: 16),

                                  /// リスト付きトップバナー
                                  Expanded(
                                    flex: 717,
                                    child: grp_grid_font_thumnail(
                                      controller: viewModel.scrollController,
                                      allCharacterFontImagesInfo: viewModel
                                          .getAllCharacterFontImagesInfo(),
                                      characterFontImagesLength:
                                          viewModel.characterFontImagesLength(),
                                      checkboxSelected: checkboxSelected,
                                      getCharacterFontImage: (index) =>
                                          viewModel
                                              .getCharacterFontImage(index),
                                      getCharacterFontName: (index) =>
                                          viewModel.getCharacterFontName(index),
                                      isCharacterFontName: (index) =>
                                          viewModel.isCharacterFontName(index),
                                      isExclusiveScriptType:
                                          viewModel.isExclusiveScriptType(),
                                      onItemClicked: (context, index,
                                              checkboxSelected) =>
                                          viewModel.onItemClicked(
                                              context, index, checkboxSelected),
                                      exclusiveScriptButton:
                                          btn_exclusive_script(
                                        feedBackControl:
                                            FeedBackControl.onlyDisable,
                                        onTap: () =>
                                            viewModel.onScriptButtonClicked(
                                                context, checkboxSelected),
                                        exclusiveIcon:
                                            viewModel.getCharacterFontImage(
                                          viewModel.getExclusiveScriptFontIndex,
                                        ),
                                      ),
                                    ),
                                  ),

                                  const Spacer(flex: 48),

                                  /// スクロールバー
                                  Expanded(
                                    flex: 8,
                                    child: grp_scrollbar(
                                      child: CustomScrollbar(
                                        isSoundPlayer: true,
                                        controller: viewModel.scrollController,
                                        visibilityWhenScrollFull: false,
                                      ),
                                    ),
                                  ),

                                  const Spacer(
                                    flex: 11,
                                  ),
                                ]),
                              ),
                              const Spacer(
                                flex: 20,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const Spacer(
                      flex: 12,
                    ),

                    /// "Return"キー
                    Expanded(
                      flex: 80,
                      child: Row(children: [
                        const Spacer(flex: 12),
                        Expanded(
                          flex: 152,
                          child: grp_btn_return(
                            onTap: () => PagesRoute().pop(),
                            buttonText: l10n.icon_return,
                          ),
                        ),
                        const Spacer(flex: 636),
                      ]),
                    ),
                    const Spacer(
                      flex: 10,
                    ),
                  ],
                )
              ],
            ),
          ),

          /// 下部のバー
          const Expanded(
            flex: 61,
            child: EmbFooter(),
          )
        ],
      ),
    );
  }

  /// チェックボックスのステータスを読み取る
  bool checkboxSelected =
      UserData().getBool(UserDataKeys.embIsOpenFontPreviewPopup);

  /// 後で使用する予定で、上部のチェックボックススタイル
  Container containerBox() => Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.white, width: 3),
        ),
        child: Transform.scale(
          scale: 2.2,
          child: Checkbox(
              value: checkboxSelected,
              activeColor: Colors.transparent,
              onChanged: (value) {
                setState(() {
                  checkboxSelected = !checkboxSelected;
                  UserData().setBool(
                      UserDataKeys.embIsOpenFontPreviewPopup, checkboxSelected);
                });
              }),
        ),
      );

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup(WidgetRef ref) => {};
}
