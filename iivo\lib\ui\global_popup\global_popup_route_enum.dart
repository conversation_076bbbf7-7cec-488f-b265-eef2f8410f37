// ignore_for_file: constant_identifier_names

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'global_popups/camera_popup/camera_popup.dart';
import 'global_popups/connecting_to_pc_now/connecting_to_pc_now.dart';
import 'global_popups/dmy_err_014/dmy_err_014.dart';
import 'global_popups/dmy_err_024/dmy_err_024.dart';
import 'global_popups/dmy_err_055/dmy_err_055.dart';
import 'global_popups/dmy_err_090/dmy_err_090.dart';
import 'global_popups/dmy_err_091/dmy_err_091.dart';
import 'global_popups/dmy_err_092/dmy_err_092.dart';
import 'global_popups/dmy_err_109/dmy_err_109.dart';
import 'global_popups/dmy_err_157/dmy_err_157.dart';
import 'global_popups/dmy_err_158/dmy_err_158.dart';
import 'global_popups/dmy_err_159/dmy_err_159.dart';
import 'global_popups/dmy_err_160/dmy_err_160.dart';
import 'global_popups/dmy_err_187/dmy_err_187.dart';
import 'global_popups/dmy_err_267/dmy_err_267.dart';
import 'global_popups/dmy_err_275/dmy_err_275.dart';
import 'global_popups/err_after_mark_pat_cnct_mark_div/err_after_mark_pat_cnct_mark_div.dart';
import 'global_popups/err_all_delete_border_ok/err_all_delete_border_ok.dart';
import 'global_popups/err_all_delete_border_ok_for_positioning/err_all_delete_border_ok_for_positioning.dart';
import 'global_popups/err_all_delete_border_ok_mark_pat_cnct/err_all_delete_border_ok_mark_pat_cnct.dart';
import 'global_popups/err_all_lock_ok/err_all_lock_ok.dart';
import 'global_popups/err_angou_not_use/err_angou_not_use.dart';
import 'global_popups/err_anzensouti/err_anzensouti.dart';
import 'global_popups/err_applique_ng_complex/err_applique_ng_complex.dart';
import 'global_popups/err_applique_ng_distance/err_applique_ng_distance.dart';
import 'global_popups/err_applique_ng_ex_in_overlap/err_applique_ng_ex_in_overlap.dart';
import 'global_popups/err_applique_ng_mem_over/err_applique_ng_mem_over.dart';
import 'global_popups/err_applique_ng_size_over/err_applique_ng_size_over.dart';
import 'global_popups/err_applique_some_parts_not_texture/err_applique_some_parts_not_texture.dart';
import 'global_popups/err_attatch_scan_frame/err_attatch_scan_frame.dart';
import 'global_popups/err_bad_embroidery_data/err_bad_embroidery_data.dart';
import 'global_popups/err_blue_line_is_over/err_blue_line_is_over.dart';
import 'global_popups/err_botton_anakagari_lever_up_frame_move/err_botton_anakagari_lever_up_frame_move.dart';
import 'global_popups/err_camera_bh_ng/err_camera_bh_ng.dart';
import 'global_popups/err_camera_set_when_needle_change/err_camera_set_when_needle_change.dart';
import 'global_popups/err_cancel_mark_pat_cnct/err_cancel_mark_pat_cnct.dart';
import 'global_popups/err_cannot_use_card/err_cannot_use_card.dart';
import 'global_popups/err_cannot_use_special_pattern/err_cannot_use_special_pattern.dart';
import 'global_popups/err_certificate_upgrade_kit_start/err_certificate_upgrade_kit_start.dart';
import 'global_popups/err_certification_key_incorrect/err_certification_key_incorrect.dart';
import 'global_popups/err_certification_success/err_certification_success.dart';
import 'global_popups/err_change_except_s_frame/err_change_except_s_frame.dart';
import 'global_popups/err_change_l_frame/err_change_l_frame.dart';
import 'global_popups/err_change_l_or_lm_frame/err_change_l_or_lm_frame.dart';
import 'global_popups/err_change_l_or_lm_or_m_frame/err_change_l_or_lm_or_m_frame.dart';
import 'global_popups/err_change_needle_board/err_change_needle_board.dart';
import 'global_popups/err_change_needle_board2_noclose/err_change_needle_board2_noclose.dart';
import 'global_popups/err_change_needle_board_noclose/err_change_needle_board_noclose.dart';
import 'global_popups/err_check_is_need_download_sound/err_check_is_need_download_sound.dart';
import 'global_popups/err_check_twin_needle_removed/err_check_twin_needle_removed.dart';
import 'global_popups/err_cmn_media_cannot_delete_by_protected/err_cmn_media_cannot_delete_by_protected.dart';
import 'global_popups/err_cmn_media_cannot_read/err_cmn_media_cannot_read.dart';
import 'global_popups/err_cmn_media_cannot_write_by_protected/err_cmn_media_cannot_write_by_protected.dart';
import 'global_popups/err_cmn_media_changed/err_cmn_media_changed.dart';
import 'global_popups/err_cmn_media_error/err_cmn_media_error.dart';
import 'global_popups/err_cmn_media_memory_full_memory_change/err_cmn_media_memory_full_memory_change.dart';
import 'global_popups/err_cmn_now_communicating/err_cmn_now_communicating.dart';
import 'global_popups/err_col_sort_ng/err_col_sort_ng.dart';
import 'global_popups/err_color_change_back_ok/err_color_change_back_ok.dart';
import 'global_popups/err_confirm_cancel_autocnct/err_confirm_cancel_autocnct.dart';
import 'global_popups/err_convert_emb_move_to_edit/err_convert_emb_move_to_edit.dart';
import 'global_popups/err_cur_delete_border_ok/err_cur_delete_border_ok.dart';
import 'global_popups/err_custom_thread_all_delete_ok/err_custom_thread_all_delete_ok.dart';
import 'global_popups/err_custom_thread_change_ok/err_custom_thread_change_ok.dart';
import 'global_popups/err_data_error/err_data_error.dart';
import 'global_popups/err_data_momory_full/err_data_momory_full.dart';
import 'global_popups/err_data_momory_full_delete_other_data/err_data_momory_full_delete_other_data.dart';
import 'global_popups/err_delete_artspira/err_delete_artspira.dart';
import 'global_popups/err_delete_border_mark_ok_easysttiple/err_delete_border_mark_ok_easysttiple.dart';
import 'global_popups/err_delete_border_mark_ok_group/err_delete_border_mark_ok_group.dart';
import 'global_popups/err_delete_border_mark_ok_un_group/err_delete_border_mark_ok_un_group.dart';
import 'global_popups/err_delete_border_mark_ok_wappen/err_delete_border_mark_ok_wappen.dart';
import 'global_popups/err_detect_fabric_thickness/err_detect_fabric_thickness.dart';
import 'global_popups/err_detection_failure_again/err_detection_failure_again.dart';
import 'global_popups/err_detection_success/err_detection_success.dart';
import 'global_popups/err_df_connect/err_df_connect.dart';
import 'global_popups/err_df_connect_noclose/err_df_connect_noclose.dart';
import 'global_popups/err_download_disable_pincode/err_download_disable_pincode.dart';
import 'global_popups/err_download_failure/err_download_failure.dart';
import 'global_popups/err_download_message_sound_popup/err_download_message_sound_popup.dart';
import 'global_popups/err_download_paid_cont_update/err_download_paid_cont_update.dart';
import 'global_popups/err_download_size_change/err_download_size_change.dart';
import 'global_popups/err_download_unreadable_data/err_download_unreadable_data.dart';
import 'global_popups/err_dummy/err_dummy.dart';
import 'global_popups/err_eeprom_data_change_ok/err_eeprom_data_change_ok.dart';
import 'global_popups/err_emb_all_delete_custom_palette_ok/err_emb_all_delete_custom_palette_ok.dart';
import 'global_popups/err_emb_bw_sewing_finish/err_emb_bw_sewing_finish.dart';
import 'global_popups/err_emb_bw_sewing_over/err_emb_bw_sewing_over.dart';
import 'global_popups/err_emb_bw_this_pattern_not_use/err_emb_bw_this_pattern_not_use.dart';
import 'global_popups/err_emb_carry_moving/err_emb_carry_moving.dart';
import 'global_popups/err_emb_carry_moving_frame_move/err_emb_carry_moving_frame_move.dart';
import 'global_popups/err_emb_carry_moving_frame_move_bw/err_emb_carry_moving_frame_move_bw.dart';
import 'global_popups/err_emb_carry_moving_frame_remove/err_emb_carry_moving_frame_remove.dart';
import 'global_popups/err_emb_carry_moving_not_over_write/err_emb_carry_moving_not_over_write.dart';
import 'global_popups/err_emb_change_order/err_emb_change_order.dart';
import 'global_popups/err_emb_change_to_scan_frame/err_emb_change_to_scan_frame.dart';
import 'global_popups/err_emb_couting_this_pattern_not_use/err_emb_couting_this_pattern_not_use.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_1/err_emb_edge_quilt_sashes_guide_1.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_10/err_emb_edge_quilt_sashes_guide_10.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_11/err_emb_edge_quilt_sashes_guide_11.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_12/err_emb_edge_quilt_sashes_guide_12.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_13/err_emb_edge_quilt_sashes_guide_13.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_14/err_emb_edge_quilt_sashes_guide_14.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_15/err_emb_edge_quilt_sashes_guide_15.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_16/err_emb_edge_quilt_sashes_guide_16.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_17/err_emb_edge_quilt_sashes_guide_17.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_2/err_emb_edge_quilt_sashes_guide_2.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_3/err_emb_edge_quilt_sashes_guide_3.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_4/err_emb_edge_quilt_sashes_guide_4.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_5/err_emb_edge_quilt_sashes_guide_5.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_6/err_emb_edge_quilt_sashes_guide_6.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_7/err_emb_edge_quilt_sashes_guide_7.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_8/err_emb_edge_quilt_sashes_guide_8.dart';
import 'global_popups/err_emb_edge_quilt_sashes_guide_9/err_emb_edge_quilt_sashes_guide_9.dart';
import 'global_popups/err_emb_edit_end_yet/err_emb_edit_end_yet.dart';
import 'global_popups/err_emb_flame_return_ok/err_emb_flame_return_ok.dart';
import 'global_popups/err_emb_flame_return_ok_bobin_change/err_emb_flame_return_ok_bobin_change.dart';
import 'global_popups/err_emb_flame_return_ok_bw/err_emb_flame_return_ok_bw.dart';
import 'global_popups/err_emb_frame_hold_lever_down/err_emb_frame_hold_lever_down.dart';
import 'global_popups/err_emb_frame_hold_lever_down_matrix/err_emb_frame_hold_lever_down_matrix.dart';
import 'global_popups/err_emb_frame_hold_lever_down_power_on/err_emb_frame_hold_lever_down_power_on.dart';
import 'global_popups/err_emb_frame_hold_lever_down_ultr_snic/err_emb_frame_hold_lever_down_ultr_snic.dart';
import 'global_popups/err_emb_frame_move_mark_pat_cnct/err_emb_frame_move_mark_pat_cnct.dart';
import 'global_popups/err_emb_frame_off/err_emb_frame_off.dart';
import 'global_popups/err_emb_frame_off_frame_move/err_emb_frame_off_frame_move.dart';
import 'global_popups/err_emb_frame_off_noclose/err_emb_frame_off_noclose.dart';
import 'global_popups/err_emb_frame_remove_reruen/err_emb_frame_remove_reruen.dart';
import 'global_popups/err_emb_frame_set/err_emb_frame_set.dart';
import 'global_popups/err_emb_frame_set_bw/err_emb_frame_set_bw.dart';
import 'global_popups/err_emb_kioku_memory_delete/err_emb_kioku_memory_delete.dart';
import 'global_popups/err_emb_lower_thread_decreased/err_emb_lower_thread_decreased.dart';
import 'global_popups/err_emb_no_available_color_for_shuf_in_custom/err_emb_no_available_color_for_shuf_in_custom.dart';
import 'global_popups/err_emb_no_available_color_for_shuf_in_org/err_emb_no_available_color_for_shuf_in_org.dart';
import 'global_popups/err_emb_pattern_exceeded/err_emb_pattern_exceeded.dart';
import 'global_popups/err_emb_pattern_exceeded_for_select_array/err_emb_pattern_exceeded_for_select_array.dart';
import 'global_popups/err_emb_pattern_exceeded_for_select_pattern/err_emb_pattern_exceeded_for_select_pattern.dart';
import 'global_popups/err_emb_pattern_exceeded_for_select_pattern_not_delete/err_emb_pattern_exceeded_for_select_pattern_not_delete.dart';
import 'global_popups/err_emb_positioning_warning/err_emb_positioning_warning.dart';
import 'global_popups/err_emb_prj_close_carry_moving/err_emb_prj_close_carry_moving.dart';
import 'global_popups/err_emb_prj_close_no_carry_moving/err_emb_prj_close_no_carry_moving.dart';
import 'global_popups/err_emb_prj_close_please_wait/err_emb_prj_close_please_wait.dart';
import 'global_popups/err_emb_prj_start_carry_moving/err_emb_prj_start_carry_moving.dart';
import 'global_popups/err_emb_prj_start_no_camera_ui/err_emb_prj_start_no_camera_ui.dart';
import 'global_popups/err_emb_prj_start_no_camera_ui_carry_moving/err_emb_prj_start_no_camera_ui_carry_moving.dart';
import 'global_popups/err_emb_prj_start_no_carry_moving/err_emb_prj_start_no_carry_moving.dart';
import 'global_popups/err_emb_prj_start_please_wait/err_emb_prj_start_please_wait.dart';
import 'global_popups/err_emb_projector_close_no_frame/err_emb_projector_close_no_frame.dart';
import 'global_popups/err_emb_projector_close_no_frame_emb_carry_moving/err_emb_projector_close_no_frame_emb_carry_moving.dart';
import 'global_popups/err_emb_quilt_sashes_1st_corner_of_move/err_emb_quilt_sashes_1st_corner_of_move.dart';
import 'global_popups/err_emb_quilt_sashes_1st_corner_of_rotate/err_emb_quilt_sashes_1st_corner_of_rotate.dart';
import 'global_popups/err_emb_quilt_sashes_1st_setting/err_emb_quilt_sashes_1st_setting.dart';
import 'global_popups/err_emb_quilt_sashes_1st_setting2/err_emb_quilt_sashes_1st_setting2.dart';
import 'global_popups/err_emb_quilt_sashes_before_corner_of_rotate/err_emb_quilt_sashes_before_corner_of_rotate.dart';
import 'global_popups/err_emb_quilt_sashes_continue/err_emb_quilt_sashes_continue.dart';
import 'global_popups/err_emb_quilt_sashes_corner_of_move/err_emb_quilt_sashes_corner_of_move.dart';
import 'global_popups/err_emb_quilt_sashes_corner_of_rotate/err_emb_quilt_sashes_corner_of_rotate.dart';
import 'global_popups/err_emb_quilt_sashes_corner_setting/err_emb_quilt_sashes_corner_setting.dart';
import 'global_popups/err_emb_quilt_sashes_last_of_rotate/err_emb_quilt_sashes_last_of_rotate.dart';
import 'global_popups/err_emb_quilt_sashes_move_to_rotate/err_emb_quilt_sashes_move_to_rotate.dart';
import 'global_popups/err_emb_quilt_sashes_reposition_setting/err_emb_quilt_sashes_reposition_setting.dart';
import 'global_popups/err_emb_quilt_sashes_straight_of_move/err_emb_quilt_sashes_straight_of_move.dart';
import 'global_popups/err_emb_quilt_this_pattern_not_use/err_emb_quilt_this_pattern_not_use.dart';
import 'global_popups/err_emb_select_pattern_rotate90/err_emb_select_pattern_rotate90.dart';
import 'global_popups/err_emb_select_pattern_rotate90_f/err_emb_select_pattern_rotate90_f.dart';
import 'global_popups/err_emb_select_pattern_rotate90_not_delete/err_emb_select_pattern_rotate90_not_delete.dart';
import 'global_popups/err_emb_select_pattern_rotate90_with_all_delete_close/err_emb_select_pattern_rotate90_with_all_delete_close.dart';
import 'global_popups/err_emb_sewing_thread_change/err_emb_sewing_thread_change.dart';
import 'global_popups/err_emb_stamp_make/err_emb_stamp_make.dart';
import 'global_popups/err_emb_stamp_make_tc/err_emb_stamp_make_tc.dart';
import 'global_popups/err_emb_this_pattern_not_use/err_emb_this_pattern_not_use.dart';
import 'global_popups/err_emb_too_much_data_expand_usb_host/err_emb_too_much_data_expand_usb_host.dart';
import 'global_popups/err_emb_too_much_selected/err_emb_too_much_selected.dart';
import 'global_popups/err_emb_too_much_selected_go_menu/err_emb_too_much_selected_go_menu.dart';
import 'global_popups/err_emb_ultr_snic_pos_change_frame/err_emb_ultr_snic_pos_change_frame.dart';
import 'global_popups/err_emb_unit_not_use/err_emb_unit_not_use.dart';
import 'global_popups/err_embdata/err_embdata.dart';
import 'global_popups/err_embunit_is_att/err_embunit_is_att.dart';
import 'global_popups/err_embunit_is_att2/err_embunit_is_att2.dart';
import 'global_popups/err_embunit_not_att/err_embunit_not_att.dart';
import 'global_popups/err_embunit_set/err_embunit_set.dart';
import 'global_popups/err_enter_certification_key_and_press_set/err_enter_certification_key_and_press_set.dart';
import 'global_popups/err_eps_correct_over/err_eps_correct_over.dart';
import 'global_popups/err_eps_correct_over_sskey/err_eps_correct_over_sskey.dart';
import 'global_popups/err_eps_disable_key/err_eps_disable_key.dart';
import 'global_popups/err_eps_finish/err_eps_finish.dart';
import 'global_popups/err_eps_finish_projector/err_eps_finish_projector.dart';
import 'global_popups/err_eps_length_sskey/err_eps_length_sskey.dart';
import 'global_popups/err_eps_mark_too_close/err_eps_mark_too_close.dart';
import 'global_popups/err_eps_stop_msg/err_eps_stop_msg.dart';
import 'global_popups/err_eula_confirmation/err_eula_confirmation.dart';
import 'global_popups/err_fail_pm_initial/err_fail_pm_initial.dart';
import 'global_popups/err_fail_safe/err_fail_safe.dart';
import 'global_popups/err_failed_to_save_file/err_failed_to_save_file.dart';
import 'global_popups/err_fd_error/err_fd_error.dart';
import 'global_popups/err_fd_wrong_format/err_fd_wrong_format.dart';
import 'global_popups/err_filter_cleared/err_filter_cleared.dart';
import 'global_popups/err_flash_memory_full/err_flash_memory_full.dart';
import 'global_popups/err_flash_rom_error/err_flash_rom_error.dart';
import 'global_popups/err_foot_controler_not_use/err_foot_controler_not_use.dart';
import 'global_popups/err_home_resume_ok/err_home_resume_ok.dart';
import 'global_popups/err_home_voice_guidance/err_home_voice_guidance.dart';
import 'global_popups/err_iic_communication/err_iic_communication.dart';
import 'global_popups/err_image_too_large/err_image_too_large.dart';
import 'global_popups/err_img_file_format/err_img_file_format.dart';
import 'global_popups/err_img_file_size/err_img_file_size.dart';
import 'global_popups/err_img_pixel_and_space_over/err_img_pixel_and_space_over.dart';
import 'global_popups/err_img_pixel_over/err_img_pixel_over.dart';
import 'global_popups/err_ledpt_connect_when_couting/err_ledpt_connect_when_couting.dart';
import 'global_popups/err_ledpt_connect_when_utl/err_ledpt_connect_when_utl.dart';
import 'global_popups/err_lib_save_memory_full/err_lib_save_memory_full.dart';
import 'global_popups/err_lower_thread_decreased/err_lower_thread_decreased.dart';
import 'global_popups/err_machine_name_enable_wlan/err_machine_name_enable_wlan.dart';
import 'global_popups/err_main_board_power_off/err_main_board_power_off.dart';
import 'global_popups/err_main_board_power_off_nmi/err_main_board_power_off_nmi.dart';
import 'global_popups/err_maintenance/err_maintenance.dart';
import 'global_popups/err_maintenance_1000h/err_maintenance_1000h.dart';
import 'global_popups/err_man_mem_all_delete_ok/err_man_mem_all_delete_ok.dart';
import 'global_popups/err_man_mem_delete_ok/err_man_mem_delete_ok.dart';
import 'global_popups/err_manual_memory_correction/err_manual_memory_correction.dart';
import 'global_popups/err_mark_delete_ok_for_nuwanai/err_mark_delete_ok_for_nuwanai.dart';
import 'global_popups/err_mark_reset_mark_pat_cnct/err_mark_reset_mark_pat_cnct.dart';
import 'global_popups/err_mcd_cancel_b/err_mcd_cancel_b.dart';
import 'global_popups/err_mcd_cancel_t/err_mcd_cancel_t.dart';
import 'global_popups/err_mcd_finish_check_b/err_mcd_finish_check_b.dart';
import 'global_popups/err_mcd_finish_check_t/err_mcd_finish_check_t.dart';
import 'global_popups/err_mcd_home_b/err_mcd_home_b.dart';
import 'global_popups/err_mcd_home_t/err_mcd_home_t.dart';
import 'global_popups/err_mcd_new/err_mcd_new.dart';
import 'global_popups/err_mcd_not_exchange_area_over/err_mcd_not_exchange_area_over.dart';
import 'global_popups/err_mcd_scan_img_trimming/err_mcd_scan_img_trimming.dart';
import 'global_popups/err_mdc_background_delete/err_mdc_background_delete.dart';
import 'global_popups/err_mdc_bg_file_delete/err_mdc_bg_file_delete.dart';
import 'global_popups/err_mdc_cancel_b/err_mdc_cancel_b.dart';
import 'global_popups/err_mdc_cancel_t/err_mdc_cancel_t.dart';
import 'global_popups/err_mdc_cmn_media_cannot_delete_by_protected/err_mdc_cmn_media_cannot_delete_by_protected.dart';
import 'global_popups/err_mdc_create_new/err_mdc_create_new.dart';
import 'global_popups/err_mdc_finish_check_b/err_mdc_finish_check_b.dart';
import 'global_popups/err_mdc_finish_check_t/err_mdc_finish_check_t.dart';
import 'global_popups/err_mdc_home_b/err_mdc_home_b.dart';
import 'global_popups/err_mdc_home_clear_all_editing_data_and_move/err_mdc_home_clear_all_editing_data_and_move.dart';
import 'global_popups/err_mdc_home_t/err_mdc_home_t.dart';
import 'global_popups/err_mdc_import_custom_pattern_all_delete_ok/err_mdc_import_custom_pattern_all_delete_ok.dart';
import 'global_popups/err_mdc_import_custom_pattern_change_after_saving/err_mdc_import_custom_pattern_change_after_saving.dart';
import 'global_popups/err_mdc_import_custom_pattern_choose_replace_data/err_mdc_import_custom_pattern_choose_replace_data.dart';
import 'global_popups/err_mdc_import_custom_pattern_delete_failure/err_mdc_import_custom_pattern_delete_failure.dart';
import 'global_popups/err_mdc_import_custom_pattern_emb_data_saved/err_mdc_import_custom_pattern_emb_data_saved.dart';
import 'global_popups/err_mdc_import_custom_pattern_not_saved_ext_memory/err_mdc_import_custom_pattern_not_saved_ext_memory.dart';
import 'global_popups/err_mdc_import_custom_pattern_replace_ok/err_mdc_import_custom_pattern_replace_ok.dart';
import 'global_popups/err_mdc_not_change_setting_memory_over/err_mdc_not_change_setting_memory_over.dart';
import 'global_popups/err_mdc_not_exchange_area_over/err_mdc_not_exchange_area_over.dart';
import 'global_popups/err_mdc_resume_ok_b/err_mdc_resume_ok_b.dart';
import 'global_popups/err_mdc_resume_ok_t/err_mdc_resume_ok_t.dart';
import 'global_popups/err_mdc_sd_media_cannot_delete_by_protected/err_mdc_sd_media_cannot_delete_by_protected.dart';
import 'global_popups/err_mdc_too_alloc_failure/err_mdc_too_alloc_failure.dart';
import 'global_popups/err_mdc_too_much_selected_go_menu/err_mdc_too_much_selected_go_menu.dart';
import 'global_popups/err_mdc_usb_media_cannot_delete_by_protected/err_mdc_usb_media_cannot_delete_by_protected.dart';
import 'global_popups/err_miss_mark_before_mark_pat_cnct/err_miss_mark_before_mark_pat_cnct.dart';
import 'global_popups/err_move_mask/err_move_mask.dart';
import 'global_popups/err_myi_cannot_input/err_myi_cannot_input.dart';
import 'global_popups/err_myi_deco_cancel_ptn_ok/err_myi_deco_cancel_ptn_ok.dart';
import 'global_popups/err_myi_deco_selptn_area_over/err_myi_deco_selptn_area_over.dart';
import 'global_popups/err_myi_delete_ok_for_delete/err_myi_delete_ok_for_delete.dart';
import 'global_popups/err_myi_delete_ok_for_normal/err_myi_delete_ok_for_normal.dart';
import 'global_popups/err_myi_delete_ok_for_savedfile_select/err_myi_delete_ok_for_savedfile_select.dart';
import 'global_popups/err_needle_plate2_emb_carry_initial/err_needle_plate2_emb_carry_initial.dart';
import 'global_popups/err_needle_plate_emb_carry_initial/err_needle_plate_emb_carry_initial.dart';
import 'global_popups/err_needle_up/err_needle_up.dart';
import 'global_popups/err_needle_up_emb_noclose/err_needle_up_emb_noclose.dart';
import 'global_popups/err_needle_up_frame_move/err_needle_up_frame_move.dart';
import 'global_popups/err_needle_up_threader_noclose/err_needle_up_threader_noclose.dart';
import 'global_popups/err_net_accesstoken_wrong/err_net_accesstoken_wrong.dart';
import 'global_popups/err_net_disable_wlan/err_net_disable_wlan.dart';
import 'global_popups/err_net_maintenance/err_net_maintenance.dart';
import 'global_popups/err_net_not_connect/err_net_not_connect.dart';
import 'global_popups/err_net_proxy_wrong/err_net_proxy_wrong.dart';
import 'global_popups/err_net_server_failure/err_net_server_failure.dart';
import 'global_popups/err_net_timeout/err_net_timeout.dart';
import 'global_popups/err_next_pattern_sel_mark_pat_cnct/err_next_pattern_sel_mark_pat_cnct.dart';
import 'global_popups/err_no_chg_font/err_no_chg_font.dart';
import 'global_popups/err_no_mark_after_mark_pat_cnct/err_no_mark_after_mark_pat_cnct.dart';
import 'global_popups/err_no_mark_before_mark_pat_cnct/err_no_mark_before_mark_pat_cnct.dart';
import 'global_popups/err_no_mark_reset_old_mark_pat_cnct/err_no_mark_reset_old_mark_pat_cnct.dart';
import 'global_popups/err_no_memory_card/err_no_memory_card.dart';
import 'global_popups/err_no_more_select/err_no_more_select.dart';
import 'global_popups/err_no_more_select_for_emb/err_no_more_select_for_emb.dart';
import 'global_popups/err_no_position_mark/err_no_position_mark.dart';
import 'global_popups/err_no_save_pattern/err_no_save_pattern.dart';
import 'global_popups/err_no_use_function_cnct/err_no_use_function_cnct.dart';
import 'global_popups/err_not_connect_to_internet/err_not_connect_to_internet.dart';
import 'global_popups/err_not_enough_available_memory_to_save/err_not_enough_available_memory_to_save.dart';
import 'global_popups/err_not_ss_botton_by_foot_controler/err_not_ss_botton_by_foot_controler.dart';
import 'global_popups/err_now_certificating/err_now_certificating.dart';
import 'global_popups/err_now_data_loading/err_now_data_loading.dart';
import 'global_popups/err_now_deleting/err_now_deleting.dart';
import 'global_popups/err_now_momorying_flash/err_now_momorying_flash.dart';
import 'global_popups/err_ok_move_and_start_capture/err_ok_move_and_start_capture.dart';
import 'global_popups/err_osae_down/err_osae_down.dart';
import 'global_popups/err_osae_down_emb/err_osae_down_emb.dart';
import 'global_popups/err_osae_lever_down/err_osae_lever_down.dart';
import 'global_popups/err_osae_lever_up/err_osae_lever_up.dart';
import 'global_popups/err_osae_lever_up_noclose/err_osae_lever_up_noclose.dart';
import 'global_popups/err_osae_up/err_osae_up.dart';
import 'global_popups/err_osae_up_emb/err_osae_up_emb.dart';
import 'global_popups/err_outside_of_emb_frm_noadd/err_outside_of_emb_frm_noadd.dart';
import 'global_popups/err_outside_of_emb_frm_nouse/err_outside_of_emb_frm_nouse.dart';
import 'global_popups/err_pattern_cancel_ok_by_delete_keys/err_pattern_cancel_ok_by_delete_keys.dart';
import 'global_popups/err_pattern_cannot_save/err_pattern_cannot_save.dart';
import 'global_popups/err_pattern_delete_ok_fd_for_delete_screen/err_pattern_delete_ok_fd_for_delete_screen.dart';
import 'global_popups/err_pattern_delete_ok_fd_for_select_screen/err_pattern_delete_ok_fd_for_select_screen.dart';
import 'global_popups/err_pattern_delete_ok_flash_for_delete_screen/err_pattern_delete_ok_flash_for_delete_screen.dart';
import 'global_popups/err_pattern_delete_ok_flash_for_select_screen/err_pattern_delete_ok_flash_for_select_screen.dart';
import 'global_popups/err_pattern_delete_ok_host_for_delete_screen/err_pattern_delete_ok_host_for_delete_screen.dart';
import 'global_popups/err_pattern_delete_ok_host_for_delete_screen_mcd/err_pattern_delete_ok_host_for_delete_screen_mcd.dart';
import 'global_popups/err_pattern_delete_ok_host_for_select_screen/err_pattern_delete_ok_host_for_select_screen.dart';
import 'global_popups/err_pattern_delete_ok_host_for_select_screen_mcd/err_pattern_delete_ok_host_for_select_screen_mcd.dart';
import 'global_popups/err_pf1_font_change_ng/err_pf1_font_change_ng.dart';
import 'global_popups/err_pf1_font_change_ok/err_pf1_font_change_ok.dart';
import 'global_popups/err_pft_move/err_pft_move.dart';
import 'global_popups/err_pft_move_frame_move/err_pft_move_frame_move.dart';
import 'global_popups/err_photo_stitch_exit/err_photo_stitch_exit.dart';
import 'global_popups/err_please_wait/err_please_wait.dart';
import 'global_popups/err_please_wait_netdiag/err_please_wait_netdiag.dart';
import 'global_popups/err_pocket_full/err_pocket_full.dart';
import 'global_popups/err_positioning_area_over/err_positioning_area_over.dart';
import 'global_popups/err_positioning_area_over_mark_pat_cnct/err_positioning_area_over_mark_pat_cnct.dart';
import 'global_popups/err_press_certification_key/err_press_certification_key.dart';
import 'global_popups/err_processing_complete/err_processing_complete.dart';
import 'global_popups/err_processing_failed/err_processing_failed.dart';
import 'global_popups/err_processing_interruptions/err_processing_interruptions.dart';
import 'global_popups/err_read_pm9_old_version/err_read_pm9_old_version.dart';
import 'global_popups/err_recognizing/err_recognizing.dart';
import 'global_popups/err_recognizing_mark_pat_cnct/err_recognizing_mark_pat_cnct.dart';
import 'global_popups/err_recognizing_scan/err_recognizing_scan.dart';
import 'global_popups/err_recognizing_small/err_recognizing_small.dart';
import 'global_popups/err_recomend_tomenui/err_recomend_tomenui.dart';
import 'global_popups/err_recommend_update/err_recommend_update.dart';
import 'global_popups/err_red_square_over/err_red_square_over.dart';
import 'global_popups/err_regist_machine_name_failure/err_regist_machine_name_failure.dart';
import 'global_popups/err_regist_pincode_failure/err_regist_pincode_failure.dart';
import 'global_popups/err_regist_pincode_wrong/err_regist_pincode_wrong.dart';
import 'global_popups/err_remove_mark_pat_cnct/err_remove_mark_pat_cnct.dart';
import 'global_popups/err_remove_mark_pat_cnct_pattern_mag/err_remove_mark_pat_cnct_pattern_mag.dart';
import 'global_popups/err_remove_mark_pat_cnct_reset/err_remove_mark_pat_cnct_reset.dart';
import 'global_popups/err_remove_position_mark/err_remove_position_mark.dart';
import 'global_popups/err_remove_sr/err_remove_sr.dart';
import 'global_popups/err_remove_sr_noclose/err_remove_sr_noclose.dart';
import 'global_popups/err_removwe_comp_frame_scan_org_img_data/err_removwe_comp_frame_scan_org_img_data.dart';
import 'global_popups/err_reset_emb_size_and_position/err_reset_emb_size_and_position.dart';
import 'global_popups/err_reset_emb_size_position/err_reset_emb_size_position.dart';
import 'global_popups/err_reset_new_1st_mark_pat_cnct/err_reset_new_1st_mark_pat_cnct.dart';
import 'global_popups/err_reset_new_2nd_mark_pat_cnct/err_reset_new_2nd_mark_pat_cnct.dart';
import 'global_popups/err_resume_ok/err_resume_ok.dart';
import 'global_popups/err_right_bobbin_holder/err_right_bobbin_holder.dart';
import 'global_popups/err_scan_frame_not_use/err_scan_frame_not_use.dart';
import 'global_popups/err_scan_shading_update/err_scan_shading_update.dart';
import 'global_popups/err_scan_shading_update_scan/err_scan_shading_update_scan.dart';
import 'global_popups/err_sd_media_broken/err_sd_media_broken.dart';
import 'global_popups/err_sd_media_cannot_delete_by_protected/err_sd_media_cannot_delete_by_protected.dart';
import 'global_popups/err_sd_media_cannot_read/err_sd_media_cannot_read.dart';
import 'global_popups/err_sd_media_cannot_write_by_protected/err_sd_media_cannot_write_by_protected.dart';
import 'global_popups/err_sd_media_changed/err_sd_media_changed.dart';
import 'global_popups/err_sd_media_error/err_sd_media_error.dart';
import 'global_popups/err_sd_media_memory_full_memory_change/err_sd_media_memory_full_memory_change.dart';
import 'global_popups/err_sd_media_not_use/err_sd_media_not_use.dart';
import 'global_popups/err_sd_media_set/err_sd_media_set.dart';
import 'global_popups/err_sd_now_communicating/err_sd_now_communicating.dart';
import 'global_popups/err_select_pattern/err_select_pattern.dart';
import 'global_popups/err_select_stitch/err_select_stitch.dart';
import 'global_popups/err_selected_data_delete_ok_popup/err_selected_data_delete_ok_popup.dart';
import 'global_popups/err_selected_pattern_cancel_ok/err_selected_pattern_cancel_ok.dart';
import 'global_popups/err_selected_pattern_delete_ok_popup/err_selected_pattern_delete_ok_popup.dart';
import 'global_popups/err_selected_stitch_cancel_ok/err_selected_stitch_cancel_ok.dart';
import 'global_popups/err_selected_stitch_cancel_ok_for_advice/err_selected_stitch_cancel_ok_for_advice.dart';
import 'global_popups/err_set_before_1st_mark_pat_cnct/err_set_before_1st_mark_pat_cnct.dart';
import 'global_popups/err_set_before_2nd_mark_pat_cnct/err_set_before_2nd_mark_pat_cnct.dart';
import 'global_popups/err_set_rtc/err_set_rtc.dart';
import 'global_popups/err_set_settings_default/err_set_settings_default.dart';
import 'global_popups/err_sew_next_parts_ok/err_sew_next_parts_ok.dart';
import 'global_popups/err_sewing_over/err_sewing_over.dart';
import 'global_popups/err_sewing_over_internal_largeconnect/err_sewing_over_internal_largeconnect.dart';
import 'global_popups/err_sewing_over_internal_largeconnect_sound/err_sewing_over_internal_largeconnect_sound.dart';
import 'global_popups/err_sewing_over_mark_pat_cnct/err_sewing_over_mark_pat_cnct.dart';
import 'global_popups/err_sewing_over_mark_pat_cnct_resume/err_sewing_over_mark_pat_cnct_resume.dart';
import 'global_popups/err_snowman_confirm/err_snowman_confirm.dart';
import 'global_popups/err_snowman_delete_border_ok/err_snowman_delete_border_ok.dart';
import 'global_popups/err_sr_connected/err_sr_connected.dart';
import 'global_popups/err_sr_disconnected/err_sr_disconnected.dart';
import 'global_popups/err_sr_ready_timeout/err_sr_ready_timeout.dart';
import 'global_popups/err_sr_select_mode/err_sr_select_mode.dart';
import 'global_popups/err_sr_twin_needle/err_sr_twin_needle.dart';
import 'global_popups/err_success_before_mark_pat_cnct/err_success_before_mark_pat_cnct.dart';
import 'global_popups/err_success_reset_new_mark_pat_cnct/err_success_reset_new_mark_pat_cnct.dart';
import 'global_popups/err_tapering_current_finish/err_tapering_current_finish.dart';
import 'global_popups/err_tapering_current_finish_button/err_tapering_current_finish_button.dart';
import 'global_popups/err_tapering_current_finish_cuetop/err_tapering_current_finish_cuetop.dart';
import 'global_popups/err_tapering_current_finish_cyclenum/err_tapering_current_finish_cyclenum.dart';
import 'global_popups/err_tapering_current_finish_decreasenum/err_tapering_current_finish_decreasenum.dart';
import 'global_popups/err_tapering_current_finish_endangle/err_tapering_current_finish_endangle.dart';
import 'global_popups/err_tapering_current_finish_endpoint/err_tapering_current_finish_endpoint.dart';
import 'global_popups/err_tapering_current_finish_flip_off/err_tapering_current_finish_flip_off.dart';
import 'global_popups/err_tapering_current_finish_flip_on/err_tapering_current_finish_flip_on.dart';
import 'global_popups/err_tapering_current_finish_increasenum/err_tapering_current_finish_increasenum.dart';
import 'global_popups/err_tapering_current_finish_retrieve/err_tapering_current_finish_retrieve.dart';
import 'global_popups/err_tapering_current_finish_startangle/err_tapering_current_finish_startangle.dart';
import 'global_popups/err_tapering_disable_key/err_tapering_disable_key.dart';
import 'global_popups/err_tapering_finish/err_tapering_finish.dart';
import 'global_popups/err_tapering_finish_projector/err_tapering_finish_projector.dart';
import 'global_popups/err_tapering_mark_too_close/err_tapering_mark_too_close.dart';
import 'global_popups/err_this_cmn_media_not_use/err_this_cmn_media_not_use.dart';
import 'global_popups/err_this_flame_not_use/err_this_flame_not_use.dart';
import 'global_popups/err_this_frame_not_use_bw/err_this_frame_not_use_bw.dart';
import 'global_popups/err_this_key_cannot_used/err_this_key_cannot_used.dart';
import 'global_popups/err_this_pattarn_not_this_frame/err_this_pattarn_not_this_frame.dart';
import 'global_popups/err_this_pattern_cannot_converted/err_this_pattern_cannot_converted.dart';
import 'global_popups/err_this_pattern_memory_over/err_this_pattern_memory_over.dart';
import 'global_popups/err_this_pattern_too_complex/err_this_pattern_too_complex.dart';
import 'global_popups/err_this_pattern_too_complex_for_illust_scan/err_this_pattern_too_complex_for_illust_scan.dart';
import 'global_popups/err_this_picture_delete_ok/err_this_picture_delete_ok.dart';
import 'global_popups/err_this_sd_media_not_use/err_this_sd_media_not_use.dart';
import 'global_popups/err_this_usb_media_not_use/err_this_usb_media_not_use.dart';
import 'global_popups/err_thread_anzzensouti/err_thread_anzzensouti.dart';
import 'global_popups/err_thread_holder_left/err_thread_holder_left.dart';
import 'global_popups/err_thread_reset/err_thread_reset.dart';
import 'global_popups/err_thread_through_failed/err_thread_through_failed.dart';
import 'global_popups/err_thunit_hit_frame/err_thunit_hit_frame.dart';
import 'global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off.dart';
import 'global_popups/err_trouble_occored_power_off2/err_trouble_occored_power_off2.dart';
import 'global_popups/err_twin_needle_auto_thread_not_used/err_twin_needle_auto_thread_not_used.dart';
import 'global_popups/err_twin_needle_not_select_pattern/err_twin_needle_not_select_pattern.dart';
import 'global_popups/err_twin_needle_not_straight_needle_plate/err_twin_needle_not_straight_needle_plate.dart';
import 'global_popups/err_unregist_pincode/err_unregist_pincode.dart';
import 'global_popups/err_unregist_pincode_failure/err_unregist_pincode_failure.dart';
import 'global_popups/err_update_farm_first/err_update_farm_first.dart';
import 'global_popups/err_update_wlan_off/err_update_wlan_off.dart';
import 'global_popups/err_upgrade_finish_b/err_upgrade_finish_b.dart';
import 'global_popups/err_upload_confirm/err_upload_confirm.dart';
import 'global_popups/err_upload_disable_pincode/err_upload_disable_pincode.dart';
import 'global_popups/err_upload_failure/err_upload_failure.dart';
import 'global_popups/err_upload_override_confirm/err_upload_override_confirm.dart';
import 'global_popups/err_upper_thread/err_upper_thread.dart';
import 'global_popups/err_usb_device_cannot_used/err_usb_device_cannot_used.dart';
import 'global_popups/err_usb_function_now_writing/err_usb_function_now_writing.dart';
import 'global_popups/err_usb_host_now_writing/err_usb_host_now_writing.dart';
import 'global_popups/err_usb_line_broken/err_usb_line_broken.dart';
import 'global_popups/err_usb_media_broken/err_usb_media_broken.dart';
import 'global_popups/err_usb_media_cannot_delete_by_protected/err_usb_media_cannot_delete_by_protected.dart';
import 'global_popups/err_usb_media_cannot_read/err_usb_media_cannot_read.dart';
import 'global_popups/err_usb_media_cannot_write_by_protected/err_usb_media_cannot_write_by_protected.dart';
import 'global_popups/err_usb_media_changed/err_usb_media_changed.dart';
import 'global_popups/err_usb_media_error/err_usb_media_error.dart';
import 'global_popups/err_usb_media_memory_full_memory_change/err_usb_media_memory_full_memory_change.dart';
import 'global_popups/err_usb_media_not_use/err_usb_media_not_use.dart';
import 'global_popups/err_usb_media_now_formatting/err_usb_media_now_formatting.dart';
import 'global_popups/err_usb_media_set/err_usb_media_set.dart';
import 'global_popups/err_usb_upgrade_file_is_error/err_usb_upgrade_file_is_error.dart';
import 'global_popups/err_usb_upgrade_finish/err_usb_upgrade_finish.dart';
import 'global_popups/err_usb_upgrade_start/err_usb_upgrade_start.dart';
import 'global_popups/err_use_foot_controler_when_width/err_use_foot_controler_when_width.dart';
import 'global_popups/err_utl_delete_ok_for_delete/err_utl_delete_ok_for_delete.dart';
import 'global_popups/err_utl_delete_ok_for_normal/err_utl_delete_ok_for_normal.dart';
import 'global_popups/err_utl_edit_end_yet/err_utl_edit_end_yet.dart';
import 'global_popups/err_utl_memory_ok/err_utl_memory_ok.dart';
import 'global_popups/err_utl_remove_fabric_from_pf/err_utl_remove_fabric_from_pf.dart';
import 'global_popups/err_utl_this_pattern_not_sew_when_use_df/err_utl_this_pattern_not_sew_when_use_df.dart';
import 'global_popups/err_verup_for_install_movie/err_verup_for_install_movie.dart';
import 'global_popups/err_video_not_used/err_video_not_used.dart';
import 'global_popups/err_wait_complete_cgi/err_wait_complete_cgi.dart';
import 'global_popups/err_warn_dont_use_open_toe_foot_sr/err_warn_dont_use_open_toe_foot_sr.dart';
import 'global_popups/err_wlan_setup_confirmation/err_wlan_setup_confirmation.dart';
import 'global_popups/err_xy_zphase/err_xy_zphase.dart';
import 'global_popups/layout_camera/layout_camera.dart';
import 'global_popups/picture_play_please_wait/picture_play_please_wait.dart';
import 'global_popups/picture_play_please_wait_with_cancel/picture_play_please_wait_with_cancel.dart';
import 'global_popups/screen_shot_save_popup/screen_shot_save_popup.dart';
import 'global_popups/stitch_camera/stitch_camera_popup.dart';
import 'global_popups/unused_message_231/unused_message_231.dart';
import 'global_popups/unused_message_232/unused_message_232.dart';
import 'global_popups/unused_message_303/unused_message_303.dart';
import 'global_popups/unused_message_304/unused_message_304.dart';
import 'global_popups/unused_message_310/unused_message_310.dart';

///
/// 各画面の名前
///
enum GlobalPopupRouteEnum {
  ///	0	ダミー エラー番号０は、通常画面
  ERR_DUMMY,

  ///	1	安全装置が働きました。糸が絡んでいませんか？針が曲がっていませんか？
  ERR_ANZENSOUTI(
      systemSound: SystemSoundEnum.error,
      messageSound: [MessageSoundEnum.t_err01],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	2	上糸が切れていないか確かめてください
  ERR_UPPER_THREAD(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err02],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	3	押えレバーを上げてください
  ERR_OSAE_LEVER_UP(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	4	押えレバーを下げてください
  ERR_OSAE_LEVER_DOWN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	5	カードが入っていませんカードをいれてください
  ERR_NO_MEMORY_CARD(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	6	この刺しゅうカードは使用できません
  ERR_CANNOT_USE_CARD(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	7	これ以上組み合わせできません
  ERR_NO_MORE_SELECT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	8	刺しゅう機がついていない時はこの釦は使えません
  ERR_EMBUNIT_NOT_ATT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	9	刺しゅう機がついている時はこの釦は使えません
  ERR_EMBUNIT_IS_ATT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	10	刺しゅう機がついている時はこの釦は使えません 電源を入れなおしてください
  @Deprecated("DMY_ERR_010")
  ERR_EMBUNIT_IS_ATT2(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	11	刺しゅう機がついている時はフットコントローラーは使えませんのではずしてください
  ERR_FOOT_CONTROLER_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	12	データ容量の制限を越えました、選べません(刺繍メモリフル)
  ERR_EMB_TOO_MUCH_SELECTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	13	針を上にあげてください
  ERR_NEEDLE_UP(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	14	Border その他エラー ポップアップない
  ERR_BORDER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	15	フットコントローラーが付いているときはスタートストップボタンは使えません
  ERR_NOT_SS_BOTTON_BY_FOOT_CONTROLER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	16	編集模様を最後まで入力し、編集終了を押してください　刺繍編
  ERR_EMB_EDIT_END_YET(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	17	編集模様を最後まで入力し、編集終了を押してください　実用編
  ERR_UTL_EDIT_END_YET(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	18	模様を選んでください　実用
  ERR_SELECT_STITCH(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	19	模様が枠からはみだしました。前の配列に変更する（文字の配列ができません）
  ERR_EMB_PATTERN_EXCEEDED_FOR_SELECT_ARRAY(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	20	模様が枠からはみだしました。選択した模様を削除する
  ERR_EMB_PATTERN_EXCEEDED_FOR_SELECT_PATTERN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	21	選択した模様を９０°回転し、模様選択を続行する
  ERR_EMB_SELECT_PATTERN_ROTATE90(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	22	模様を選んでください　刺繍
  ERR_SELECT_PATTERN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	23	 記憶中 (内蔵メモリ)
  ERR_NOW_MOMORYING_FLASH(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	24	記憶中 (SDカード)
  @Deprecated("DMY_ERR_024")
  ERR_SD_NOW_WRITING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	25	記憶中 (USB(マウス無し))
  ERR_USB_HOST_NOW_WRITING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	26	記憶中 (USB(マウスあり))
  @Deprecated("DMY_ERR_026")
  ERR_USB_HOST2_NOW_WRITING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	27	記憶中 (PC)
  ERR_USB_FUNCTION_NOW_WRITING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	28	下糸が少なくなってきました
  ERR_LOWER_THREAD_DECREASED(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err24],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	29	刺繍キャリッジが動きます、手や物を離してください
  ERR_EMB_CARRY_MOVING(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err25],
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	30	消去します（記憶データ）
  @Deprecated("DMY_ERR_030")
  ERR_EMB_KIOKU_MEMORY_DELETE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	31	模様の選択をキャンセルします 実用
  ERR_SELECTED_STITCH_CANCEL_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	32	模様の呼び出し中です、しばらくお待ちください
  ERR_NOW_DATA_LOADING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	33	模様の選択をキャンセルします
  ERR_SELECTED_PATTERN_CANCEL_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	34	設定条件を記憶します。よろしいですか? 《キャンセル》 《OK》
  @Deprecated("DMY_ERR_034")
  ERR_UTL_MEMORY_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	35	大枠に変更ください 《OK》
  ERR_CHANGE_L_FRAME(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	36	大枠または、中枠2に変更ください 《OK》
  ERR_CHANGE_L_OR_LM_FRAME(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	37	枠をはずしてください 《OK》
  ERR_EMB_FRAME_OFF(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	38	枠を付けてください 《OK》
  ERR_EMB_FRAME_SET(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	39	WIDTH CONTLOR モードでは、スタート/ストップキーは使えません。フットコントローラを使用ください
  ERR_USE_FOOT_CONTROLER_WHEN_WIDTH(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	40	糸巻きの安全装置が働きました。糸がからんでいませんか？
  ERR_THREAD_ANZZENSOUTI(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err37],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	41	2本針モードでは、この模様は選べません。2本針モードを解除して、選んでください。
  ERR_TWIN_NEEDLE_NOT_SELECT_PATTERN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	42	容量不足で模様が記憶できません。模様を消去するか、FDを交換してください 《キャンセル》 《消去実行》
  ERR_DATA_MOMORY_FULL(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	43	FDエラー 《OK》
  @Deprecated("DMY_ERR_043")
  ERR_FD_ERROR(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	44	糸巻きホルダーを左にしてください
  @Deprecated("DMY_ERR_044")
  ERR_THREAD_HOLDER_LEFT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	45	押えレバーを上げてください（Ｃｌｏｓｅなし）
  ERR_OSAE_LEVER_UP_NOCLOSE(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	46	2本針モードでは、直線針板を使用できません。2本針を取り外し、2本針モードを解除してください。
  ERR_TWIN_NEEDLE_NOT_STRAIGHT_NEEDLE_PLATE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	47	針上下スイッチで針を上にあげてください（Ｃｌｏｓｅなし）
  ERR_NEEDLE_UP_EMB_NOCLOSE(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	48	選んだ模様が消えます。よろしいですか?（ＦＤ選択画面用） 《キャンセル》 《OK》
  @Deprecated("DMY_ERR_048")
  ERR_PATTERN_DELETE_OK_FD_FOR_SELECT_SCREEN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	49	選んだ模様が消えます。よろしいですか?（ＦＤ消去画面用） 《キャンセル》 《OK》
  @Deprecated("DMY_ERR_049")
  ERR_PATTERN_DELETE_OK_FD_FOR_DELETE_SCREEN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	50	選んだ模様が消えます。よろしいですか?（ＦＬＡＳＨ選択画面用） 《キャンセル》 《OK》
  @Deprecated("DMY_ERR_050")
  ERR_PATTERN_DELETE_OK_FLASH_FOR_SELECT_SCREEN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	51	選んだ模様が消えます。よろしいですか?（ＦＬＡＳＨ消去画面用） 《キャンセル》 《OK》
  @Deprecated("DMY_ERR_051")
  ERR_PATTERN_DELETE_OK_FLASH_FOR_DELETE_SCREEN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	52	フラッシュ用。容量不足で模様が記憶できません。模様を消去して下さい 《キャンセル》 《OK》
  ERR_FLASH_MEMORY_FULL(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	53	「文字・模様」選んだ模様が消えます。よろしいですか?（ＦＬＡＳＨ選択画面用） 《キャンセル》 《OK》
  @Deprecated("DMY_ERR_053")
  ERR_UTL_DELETE_OK_FOR_NORMAL(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	54	「文字・模様」選んだ模様が消えます。よろしいですか?（ＦＬＡＳＨ選択画面用） 《キャンセル》 《OK》
  @Deprecated("DMY_ERR_054")
  ERR_UTL_DELETE_OK_FOR_DELETE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	55	スキャン通常終了
  ERR_SCAN_NORMAL_END(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	56	消去中
  ERR_NOW_DELETING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	57	赤いラインがはみ出しているときは操作できません。
  ERR_RED_SQUARE_OVER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	58	記憶できない模様が含まれています。ミシンの内臓メモリに記憶くださ。
  ERR_NO_SAVE_PATTERN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	59	「マイイラスト」選んだ模様が消えます。よろしいですか?（ＦＬＡＳＨ選択画面用） 《キャンセル》 《OK》
  ERR_MYI_DELETE_OK_FOR_NORMAL(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	60	「マイイラスト」選んだ模様が消えます。よろしいですか?（ＦＬＡＳＨ選択画面用） 《キャンセル》 《OK》
  @Deprecated("DMY_ERR_060")
  ERR_MYI_DELETE_OK_FOR_DELETE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	61	識別できるフォーマットではありません。《OK》
  ERR_FD_WRONG_FORMAT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	62	模様が枠からはみだしました。
  ERR_EMB_PATTERN_EXCEEDED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	63	データ容量の制限を越えました、選べません(刺繍メモリフル)
  ERR_EMB_TOO_MUCH_SELECTED_GO_MENU(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	64	枠をはずしてください（Ｃｌｏｓｅなし）
  ERR_EMB_FRAME_OFF_NOCLOSE(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	65	これ以上組み合わせできません（刺繍用）
  ERR_NO_MORE_SELECT_FOR_EMB(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	66	選択した模様を９０°回転し、模様選択を続行する キャンセルしても模様は消去しない
  @Deprecated("DMY_ERR_066")
  ERR_EMB_SELECT_PATTERN_ROTATE90_NOT_DELETE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	67	大枠または、中枠2にまたは中枠に変更ください 《OK》
  ERR_CHANGE_L_OR_LM_OR_M_FRAME(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	68	この模様は専用枠に変えてください 《OK》
  @Deprecated("DMY_ERR_068")
  ERR_THIS_PATTARN_NOT_THIS_FRAME(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	69	この模様はつかえません
  ERR_EMB_THIS_PATTERN_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	70	カスタムスレッドを書き換えます。よろしいですか？
  @Deprecated("DMY_ERR_070")
  ERR_CUSTOM_THREAD_CHANGE_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	71	カスタムスレッドが全部消えます。よろしいですか？
  @Deprecated("DMY_ERR_071")
  ERR_CUSTOM_THREAD_ALL_DELETE_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	72	色の変更が元に戻ります。よろしいですか？
  ERR_COLOR_CHANGE_BACK_OK(
      systemSound: SystemSoundEnum.warning,
      isClickableInScreenLock: true,
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	73	データを書き換えます。よろしいですか？ＥＥＰＲＯＭデータの書き換え
  @Deprecated("DMY_ERR_073")
  ERR_EEPROM_DATA_CHANGE_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	74	レジューム記憶を呼び出しますか？
  ERR_RESUME_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	75	SR 待機中のタイムアウト
  ERR_SR_READY_TIMEOUT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	76	SR画面に遷移しますか？
  ERR_SR_CONNECTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	77	SR画面を抜けますか？
  ERR_SR_DISCONNECTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	78	刺しゅう機がついていないので縫えません。電源スイッチを切ってから刺しゅう機を取り付けてください。
  ERR_EMBUNIT_SET(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	79	ＵＳＢメディアが入っていません。ＵＳＢメディアを入れてください。
  ERR_USB_MEDIA_SET(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	80	SDカードが入っていません。SDカードを入れてください。
  @Deprecated("DMY_ERR_080")
  ERR_SD_MEDIA_SET(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	81	大枠に変更ください 《OK》
  ERR_CHANGE_EXCEPT_S_FRAME(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	82	このＵＳＢメディアは使用できません。
  ERR_USB_MEDIA_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	83	このSDカードは使用できません。
  @Deprecated("DMY_ERR_083")
  ERR_SD_MEDIA_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	84	SR SRでは2本針は使用できません。
  ERR_TWIN_NEEDLE_GOTO_SR(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	85	SR しつけ縫いではオープントゥフットを使用しないでください。
  ERR_WARN_DONT_USE_OPEN_TOE_FOOT_SR(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	86	SR SR押さえを外してください。
  ERR_REMOVE_SR(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	87	ＵＳＢメディアのフォーマットができません。ＵＳＢメディアの種類が違うか、壊れている可能性があります。ＵＳＢメディアを交換してください。
  @Deprecated("DMY_ERR_087")
  ERR_USB_MEDIA_BROKEN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	88	SDカードが壊れている可能性があります。SDカードを交換して、もう一度記憶してください。
  @Deprecated("DMY_ERR_088")
  ERR_SD_MEDIA_BROKEN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	89	モード選択をしてから縫製開始してください
  ERR_SR_SELECT_MODE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	90	空き
  DMY_ERR_090,

  ///	91	空き
  DMY_ERR_091,

  ///	92	空き
  DMY_ERR_092,

  ///	93	ＵＳＢメディアが交換されました。読み込んでいる最中に交換しないでください。
  @Deprecated("DMY_ERR_093")
  ERR_USB_MEDIA_CHANGED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	94	SDカードが交換されました。読み込んでいる最中に交換しないでください。
  @Deprecated("DMY_ERR_094")
  ERR_SD_MEDIA_CHANGED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	95	CMN メディアが交換されました。読み込んでいる最中に交換しないでください。
  @Deprecated("DMY_ERR_095")
  ERR_CMN_MEDIA_CHANGED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	96	ＵＳＢメディアがライトプロテクトされていて記憶できません。ライトプロテクトを解除して記憶してください。
  ERR_USB_MEDIA_CANNOT_WRITE_BY_PROTECTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	97	SDカードがライトプロテクトされていて記憶できません。ライトプロテクトを解除して記憶してください。
  @Deprecated("DMY_ERR_097")
  ERR_SD_MEDIA_CANNOT_WRITE_BY_PROTECTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	98	CMN メディアがライトプロテクトされていて記憶できません。ライトプロテクトを解除して記憶してください。
  ERR_CMN_MEDIA_CANNOT_WRITE_BY_PROTECTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	99	ＵＳＢメディアがライトプロテクトされていて消去できません。ライトプロテクトを解除して消去してください。
  ERR_USB_MEDIA_CANNOT_DELETE_BY_PROTECTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	100	SDカードがライトプロテクトされていて消去できません。ライトプロテクトを解除して消去してください。
  @Deprecated("DMY_ERR_100")
  ERR_SD_MEDIA_CANNOT_DELETE_BY_PROTECTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	101	CMN メディアがライトプロテクトされていて消去できません。ライトプロテクトを解除して消去してください。
  @Deprecated("DMY_ERR_101")
  ERR_CMN_MEDIA_CANNOT_DELETE_BY_PROTECTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	102	ＵＳＢメディアエラー
  @Deprecated("DMY_ERR_102")
  ERR_USB_MEDIA_ERROR(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	103	SDカードエラー
  @Deprecated("DMY_ERR_103")
  ERR_SD_MEDIA_ERROR(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	104	CMN メディアエラー
  @Deprecated("DMY_ERR_104")
  ERR_CMN_MEDIA_ERROR(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	105	ＵＳＢメディアが読めません。ＵＳＢメディアが壊れている可能性があります。
  @Deprecated("DMY_ERR_105")
  ERR_USB_MEDIA_CANNOT_READ(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	106	SDカードが読めません。SDカードが壊れている可能性があります。
  @Deprecated("DMY_ERR_106")
  ERR_SD_MEDIA_CANNOT_READ(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	107	CMN メディアが読めません。メディアが壊れている可能性があります。
  @Deprecated("DMY_ERR_107")
  ERR_CMN_MEDIA_CANNOT_READ(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	108	ＵＳＢメディアのフォーマット中です。
  @Deprecated("DMY_ERR_108")
  ERR_USB_MEDIA_NOW_FORMATTING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	109	ＵＳＢ通信中
  ERR_USB_NOW_COMMUNICATING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	110	SDカード通信中
  @Deprecated("DMY_ERR_110")
  ERR_SD_NOW_COMMUNICATING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	111	CMN メディア通信中
  @Deprecated("DMY_ERR_111")
  ERR_CMN_NOW_COMMUNICATING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	112	しばらくお待ち下さい。
  ERR_PLEASE_WAIT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	113	青い枠がはみ出しているときは操作できません。
  @Deprecated("DMY_ERR_113")
  ERR_BLUE_LINE_IS_OVER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	114	次のパーツを縫いますか？
  ERR_SEW_NEXT_PARTS_OK(
      systemSound: SystemSoundEnum.warning,
      isClickableInScreenLock: true,
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	115	縫い終わりました。
  ERR_SEWING_OVER(
      systemSound: SystemSoundEnum.sewOvr2,
      messageSound: [MessageSoundEnum.t_err107],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	116	ポケットがいっぱいです。模様を消去してください。
  ERR_POCKET_FULL(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	117	押えスイッチで押えを上げください。
  ERR_OSAE_UP(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	118	押えスイッチで押えを下げてください。
  ERR_OSAE_DOWN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	119	糸掛けがうまくできません。もう一度糸掛けスイッチを押してください。
  @Deprecated("DMY_ERR_119")
  ERR_THREAD_THROUGH_FAILED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	120	プログラムのアップグレードを行います。ＵＳＢでプログラムをミシンに入れてください。
  @Deprecated("DMY_ERR_120")
  ERR_USB_UPGRADE_START(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	121	アップグレードが終わりました。電源を切ってください。
  @Deprecated("DMY_ERR_121")
  ERR_USB_UPGRADE_FINISH(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	122	正しいアップグレードファイルではありません。
  @Deprecated("DMY_ERR_122")
  ERR_USB_UPGRADE_FILE_IS_ERROR(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	123	ＤＡＴＡエラー
  @Deprecated("DMY_ERR_123")
  ERR_DATA_ERROR(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	124	ＦＬＡＳＨ ＲＯＭエラー
  @Deprecated("DMY_ERR_124")
  ERR_FLASH_ROM_ERROR(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	125	不具合が生じました。電源をいったんＯＦＦしてから再度やり直してください。
  ERR_TROUBLE_OCCORED_POWER_OFF(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	126	針板を交換してください。
  ERR_CHANGE_NEEDLE_BOARD(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	127	刺しゅう枠を元の位置に戻します。よろしいですか？
  ERR_EMB_FLAME_RETURN_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	128	ボーダー模様の組み合わせが解除されます。よろしいですか？  ※全てのボーダー模様 解除用
  ERR_ALL_DELETE_BORDER_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	129	このＵＳＢメディアには対応していません。
  @Deprecated("DMY_ERR_129")
  ERR_THIS_USB_MEDIA_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	130	このSDカードには対応していません。
  @Deprecated("DMY_ERR_130")
  ERR_THIS_SD_MEDIA_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	131	このメディアには対応していません。
  @Deprecated("DMY_ERR_131")
  ERR_THIS_CMN_MEDIA_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	132	ＵＳＢメディア/コネクターが引き抜かれました。
  @Deprecated("DMY_ERR_132")
  ERR_USB_LINE_BROKEN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	133	不具合が生じました。電源をいったんＯＦＦしてから再度やり直してください。
  @Deprecated("DMY_ERR_133")
  ERR_TROUBLE_OCCORED_POWER_OFF2(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	134	選んだ模様が消えます。よろしいですか?（ＨＯＳＴ選択画面用） 《キャンセル》 《OK》
  @Deprecated("DMY_ERR_134")
  ERR_PATTERN_DELETE_OK_HOST_FOR_SELECT_SCREEN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	135	選んだ模様が消えます。よろしいですか?（ＨＯＳＴ消去画面用） 《キャンセル》 《OK》
  @Deprecated("DMY_ERR_135")
  ERR_PATTERN_DELETE_OK_HOST_FOR_DELETE_SCREEN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	136	ロックキー
  ERR_ALL_LOCK_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	137	選んだ模様が消えます。よろしいですか?
  ERR_PATTERN_CANCEL_OK_BY_DELETE_KEYS(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	138	ボビン押えを右に戻してください。
  ERR_RIGHT_BOBBIN_HOLDER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	139	押えが上下します。手を押えに近づけないでください。
  ERR_PFT_MOVE(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	140	針板を交換してください。
  ERR_CHANGE_NEEDLE_BOARD_NOCLOSE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	141	ボーダー模様の組み合わせが解除されます。よろしいですか？  ※カレントボーダー模様 解除用(カレント以外は解除しない)
  ERR_CUR_DELETE_BORDER_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	142	不具合が生じました。電源をいったんＯＦＦしてから再度やり直してください。
  ERR_FAIL_PM_INITIAL(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	143	刺繍キャリッジが動きます、手や物を離してください
  @Deprecated("DMY_ERR_143")
  ERR_EMB_CARRY_MOVING_NOT_OVER_WRITE(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err25],
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	144	刺繍キャリッジが動きます、手や物を離してください
  ERR_EMB_CARRY_MOVING_FRAME_MOVE(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err25],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	145	針を上にあげてください
  ERR_NEEDLE_UP_FRAME_MOVE(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	146	ボタン穴かがりレバーを上げてください
  ERR_BOTTON_ANAKAGARI_LEVER_UP_FRAME_MOVE(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	147	押えが上下します。手を押えに近づけないでください。
  ERR_PFT_MOVE_FRAME_MOVE(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	148	枠をはずしてください 《OK》
  ERR_EMB_FRAME_OFF_FRAME_MOVE(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	149	刺繍枠固定レバーを下げてください。
  ERR_EMB_FRAME_HOLD_LEVER_DOWN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	150	下糸が少なくなってきました。（刺繍）
  ERR_EMB_LOWER_THREAD_DECREASED(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err575],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	151	模様が枠からはみだしました。
  ERR_EMB_PATTERN_EXCEEDED_FOR_SELECT_PATTERN_NOT_DELETE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	152	選択した模様を９０°回転し、模様選択を続行する
  @Deprecated("DMY_ERR_152")
  ERR_EMB_SELECT_PATTERN_ROTATE90_WITH_ALL_DELETE_CLOSE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	153	フェイルセーフ
  ERR_FAIL_SAFE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	154	選んだ模様が消えます。よろしいですか? 《キャンセル》 《OK》
  @Deprecated("DMY_ERR_154")
  ERR_SELECTED_STITCH_CANCEL_OK_FOR_ADVICE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	155	2本針モードでは、自動糸通しは使えません
  ERR_TWIN_NEEDLE_AUTO_THREAD_NOT_USED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	156	糸を掛けなおしてください
  ERR_THREAD_RESET(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	157	空き
  DMY_ERR_157,

  ///	158	移動や回転が元に戻りますがよろしいですか？
  @Deprecated("DMY_ERR_158")
  ERR_EMB_POSITIONING_RESET(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	159	ボーダー模様の組み合わせが解除されます。よろしいですか？
  @Deprecated("DMY_ERR_159")
  ERR_EMB_POSITIONING_BORDER_RESET(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	160	刺繍キャリッジが動きます、手や物を離してください
  @Deprecated("DMY_ERR_160")
  ERR_EMB_POSITIONING_SCAN_CANCEL_FRAME_MOVE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	161	この模様はつかえません
  ERR_ANGOU_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	162	刺しゅうをするときは、枠固定レバーが倒れているのを確認してからスタート／ストップスイッチを押してください。
  ERR_EMB_FRAME_HOLD_LEVER_DOWN_POWER_ON(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	163	この刺しゅう枠は使えません
  ERR_THIS_FLAME_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	164	この模様は容量オーバーです
  ERR_THIS_PATTERN_MEMORY_OVER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	165	メンテナンス時期になりましたのでミシンのメンテナンスをおすすめします
  ERR_MAINTENANCE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	166	針上下スイッチで針を上にあげてください（糸通し時、Ｃｌｏｓｅなし） THPM03-00-52
  ERR_NEEDLE_UP_THREADER_NOCLOSE(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	167	プロジェクタ投影を開始します。刺しゅうキャリッジが動きます。刺しゅうキャリッジの近くから物を離し、手を近づけないようにしてください。T_ERR_Proj_Emb_012
  ERR_EMB_PRJ_START_CARRY_MOVING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	168	刺しゅう枠が小さいため、プロジェクターによる値変更機能には対応していません。刺しゅう模様の投影のみおこないます。刺繍キャリッジが動きます、手や物を離してください T_ERR_Proj_Emb_001
  ERR_EMB_PRJ_START_NO_CAMERAUI_CARRY_MOVING(
      systemSound: SystemSoundEnum.warning,
      messageSound: [],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	169	刺しゅう枠が小さいため、プロジェクターによる値変更機能には対応していません。刺しゅう模様の投影のみおこないます。T_ERR_Proj_Emb_002
  ERR_EMB_PRJ_START_NO_CAMERAUI(
      systemSound: SystemSoundEnum.warning,
      messageSound: [],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	170	刺繍キャリッジが動きます、手や物を離してください
  ERR_EMB_POSITIONING_WARNING(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err25],
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	171	認識中
  ERR_RECOGNIZING(
      systemSound: SystemSoundEnum.warning,
      isClickableInScreenLock: true,
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	172	位置あわせマークを認識する事が出来ませんでした
  ERR_NO_POSITION_MARK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	173	位置あわせマークを取って下さい
  ERR_REMOVE_POSITION_MARK(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err215],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	174	針板２を装着してください PLT204-01
  ERR_CHANGE_NEEDLE_BOARD2_NOCLOSE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	175	この画像を消してもいいですか？
  ERR_THIS_PICTURE_DELETE_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	176	ファイルサイズが大きすぎます
  ERR_IMG_FILE_SIZE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	177	対応したフォーマットではありません
  ERR_IMG_FILE_FORMAT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	178	レジューム記憶を呼び出しますか？(ＨＯＭＥ画面用)
  ERR_HOME_RESUME_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	179	メンテナンス時期になりましたのでミシンのメンテナンスをおすすめします
  ERR_MAINTENANCE_1000H(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	180	模様がはみ出します（刺繍位置あわせ用）
  ERR_POSITIONING_AREA_OVER(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err246],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	181	認識中 ちび
  @Deprecated("DMY_ERR_181")
  ERR_RECOGNIZING_SMALL(
      systemSound: SystemSoundEnum.warning,
      isClickableInScreenLock: true,
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	182	消してもいいですか？（マニュアルメモリ削除確認）
  ERR_MAN_MEM_DELETE_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	183	消してもいいですか？（マニュアルメモリ全削除確認）
  ERR_MAN_MEM_ALL_DELETE_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	184	選択した模様を９０°回転し、模様選択を続行する キャンセル時にファイル選択をリセット
  ERR_EMB_SELECT_PATTERN_ROTATE90_F(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	185	このキーは今は使えません
  ERR_THIS_KEY_CANNOT_USED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	186	刺繍キャリッジが動きます (糸通しが刺繍枠にぶつかるとき) THPM07-00-04
  ERR_THUNIT_HIT_FRAME(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err25],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  /// 187	IIVO用　アプリにエラー状態通知するためのエラーコード追加 刺繍模様投影強制終了待ち
  @Deprecated("DMY_ERR_187")
  ERR_EMB_PROJ_FORCE_QUIT_FRAMEOFF_WAITING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	188	PC接続中 * /X1V1.00M-866
  @Deprecated("DMY_ERR_188")
  CONNECTING_TO_PC_NOW(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	189	針板を装着してください(刺繍機イニシャル用) エラーの優先順位を上げため追加 X1V1.00M-1220
  ERR_NEEDLE_PLATE_EMB_CARRY_INITIAL(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	190	針板２を装着してください(刺繍機イニシャル用) エラーの優先順位を上げため追加 X1V1.00M-1220
  ERR_NEEDLE_PLATE2_EMB_CARRY_INITIAL(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	191	ボーダー模様の組み合わせが解除されます。よろしいですか？（刺繍位置合わせ用） X1V1.10M-84
  ERR_ALL_DELETE_BORDER_OK_FOR_POSITIONING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	192	データ容量の制限を越えました、選べません(刺繍メモリフル)(ＵＳＢホストデータ展開時) X1V1.10M-131
  @Deprecated("DMY_ERR_192")
  ERR_EMB_TOO_MUCH_DATA_EXPAND_USB_HOST(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	193	認識中 スキャン
  ERR_RECOGNIZING_SCAN(
      systemSound: SystemSoundEnum.warning,
      isClickableInScreenLock: true,
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	194	この模様を変換する事は出来ません
  @Deprecated("DMY_ERR_194")
  ERR_THIS_PATTERN_CANNOT_CONVERTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	195	模様の大きさと位置がリセットされます。良いですか
  ERR_RESET_EMB_SIZE_AND_POSITION(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	196	模様の大きさがリセットされます。良いですか
  @Deprecated("DMY_ERR_196")
  ERR_RESET_EMB_SIZE_POSITION(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	197	布厚を検出します。赤い線の中に位置あわせマークを貼ってください X1V200 SCAN-02-02 toukurma
  ERR_DETECT_FABRIC_THICKNESS(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err253],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	198	布厚検出に成功しました。位置あわせマークを剥がして下さい X1V200 SCAN-02-05 toukurma
  ERR_DETECTION_SUCCESS(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err254],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	199	OKキーを押すと枠が動きスキャン動作を開始します X1V200 SCAN-01-02 toukurma
  ERR_OK_MOVE_AND_START_CAPTURE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	200	布厚検出に失敗しました。もう一度やり直しますか？ X1V200 SCAN-02-06 toukurma
  ERR_DETECTION_FAILURE_AGAIN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	201	認証に成功しました。ミシンを再起動して下さい
  @Deprecated("DMY_ERR_201")
  ERR_CERTIFICATION_SUCCESS(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	202	キットを購入された方で認証する場合は認証キーを押して下さい
  @Deprecated("DMY_ERR_202")
  ERR_PRESS_CERTIFICATION_KEY(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	203	キットの認証を行います。認証後、ミシンの再起動が必要です
  @Deprecated("DMY_ERR_203")
  ERR_CERTIFICATE_UPGRADE_KIT_START(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	204	認証キーを入力してからSETキーを押して下さい
  @Deprecated("DMY_ERR_204")
  ERR_ENTER_CERTIFICATION_KEY_AND_PRESS_SET(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	205	認証中
  @Deprecated("DMY_ERR_205")
  ERR_NOW_CERTIFICATING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	206	認証キーが正しくありません。確認して再度入力してください
  @Deprecated("DMY_ERR_206")
  ERR_CERTIFICATION_KEY_INCORRECT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	207	NANDに記憶した合成画像ファイルの削除
  ERR_REMOVWE_COMP_FRAME_SCAN_ORG_IMG_DATA(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	208	 Failed to save file X1V200 CAP-01-13/14 Naka
  ERR_FAILED_TO_SAVE_FILE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	209	縫製終了。OK後ミシンが動きます。模様つなぎ用 X2V300 PTCN-2-1 toukurma
  ERR_SEWING_OVER_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.sewOvr2,
      messageSound: [MessageSoundEnum.t_err303],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	210	マークを赤い線の中に貼ってください。模様つなぎ布張替え前の１つ目のマーク用 X2V300 PTCN-2-3 toukurma
  ERR_SET_BEFORE_1ST_MARK_PAT_CNCT(
      messageSound: [MessageSoundEnum.t_err270],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	211	認識中 模様つなぎ用* * X2V300 PTCN-2-5 toukurma
  ERR_RECOGNIZING_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning,
      isClickableInScreenLock: true,
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	212	マークを見つけることが出来ませんでした。模様つなぎの布張替え前用* * X2V300 PTCN-2-6 toukurma
  ERR_NO_MARK_BEFORE_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	213	マークの貼り間違い。模様つなぎの布張替え前用* * X2V300 PTCN-2-7 toukurma
  ERR_MISS_MARK_BEFORE_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err273],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	214	マークを赤い線の中に貼ってください。模様つなぎ布張替え前の2つ目のマーク用 * * X2V300 PTCN-2-8 toukurma
  ERR_SET_BEFORE_2ND_MARK_PAT_CNCT(
      messageSound: [MessageSoundEnum.t_err271],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	215	マークを認識しました。模様つなぎの布張替え前のマーク認識成功* * X2V300 PTCN-2-13 toukurma
  ERR_SUCCESS_BEFORE_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err307],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	216	ボーダー模様の組み合わせが解除されます。よろしいですか？ 模様つなぎ用* * X2V300 PTCN-3-2 toukurma
  ERR_ALL_DELETE_BORDER_OK_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	217	マークを見つけることが出来ませんでした。模様つなぎの布張替え後用* * X2V300 PTCN-3-12 toukurma
  ERR_NO_MARK_AFTER_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err309],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	218	模様がはみ出します 模様つなぎ用* * X2V300 PTCN-3-13 toukurma
  ERR_POSITIONING_AREA_OVER_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err308],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	219	次の模様を選択してください。模様つなぎ用 X2V310 PTCN2-2-5 toukurma
  ERR_NEXT_PATTERN_SEL_MARK_PAT_CNCT(
      messageSound: [MessageSoundEnum.t_err304],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	220	位置あわせマークを取って下さい 模様つなぎ用* * X2V300 PTCN-3-15 toukurma
  ERR_REMOVE_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err311],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	221	マークの貼り直し処理に進みます。* * X2V310 PTCN2-5-1 toukurma
  ERR_MARK_RESET_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err312],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	222	位置あわせマークを取って下さい 模様つなぎ貼り直し用* * X2V300 PTCN toukurma
  ERR_REMOVE_MARK_PAT_CNCT_RESET(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err276],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	223	刺繍キャリッジが動きます、手や物を離してください 模様つなぎ用X2V310 PTCN2-2-16 toukurma
  ERR_EMB_FRAME_MOVE_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err25],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	224	マークを赤い線の中に貼ってください。模様つなぎ貼り直し１つ目のマーク用 * * X2V310 PTCN2 toukurma
  ERR_RESET_NEW_1ST_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err270],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	225	マークを赤い線の中に貼ってください。模様つなぎ貼り直し２つ目のマーク用 * * X2V310 PTCN2 toukurma
  ERR_RESET_NEW_2ND_MARK_PAT_CNCT(
      messageSound: [MessageSoundEnum.t_err271],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	226	つなぎキャンセル確認
  ERR_CANCEL_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err277],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	227	刺繍位置合わせマークの位置が変更されました。* * X2V310 PTCN2 toukurma
  ERR_SUCCESS_RESET_NEW_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err310],
      isClickableInScreenLock: true,
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	228	このデータは複雑すぎて変換できません X2V300 MCD toukurma
  ERR_THIS_PATTERN_TOO_COMPLEX(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	229	MCD CANCEL ブラザー XVV100 MCD toukurma
  @Deprecated("DMY_ERR_229")
  ERR_MCD_CANCEL_B(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	230	MCD HOME ブラザー XVV100 MCD toukurma
  @Deprecated("DMY_ERR_230")
  ERR_MCD_HOME_B(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	231	空き
  DMY_ERR_231,

  ///	232	空き
  DMY_ERR_232,

  ///	【未使用】233	選んだ模様が消えます。よろしいですか?（ＨＯＳＴ選択画面用） X2V300 MCD toukurma
  @Deprecated("DMY_ERR_233")
  ERR_PATTERN_DELETE_OK_HOST_FOR_SELECT_SCREEN_MCD(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	【未使用】234	選んだ模様が消えます。よろしいですか?（ＨＯＳＴ消去画面用） X2V300 MCD toukurma
  @Deprecated("DMY_ERR_234")
  ERR_PATTERN_DELETE_OK_HOST_FOR_DELETE_SCREEN_MCD(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	235	MCD NEW XVV100 MCD toukurma
  @Deprecated("DMY_ERR_235")
  ERR_MCD_NEW(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	236	不要　　カスタムパレットテーブル情報が消えます。よろしいですか？ X2V300 SHUFFLE CUSTOM-1-2 makiry
  @Deprecated("DMY_ERR_236")
  ERR_EMB_ALL_DELETE_CUSTOM_PALETTE_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	237	選択されたモード用に利用できる色がカスタムスレッドテーブルにありません X2V300 SHUFFLE SHUFFLE-2-9 makiry
  @Deprecated("DMY_ERR_237")
  ERR_EMB_NO_AVAILABLE_COLOR_FOR_SHUF_IN_CUSTOM(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	238	選択されたモード用に利用できる色がカスタムスレッドテーブルにありません X2V300 SHUFFLE SHUFFLE-2-9 makiry
  ERR_EMB_NO_AVAILABLE_COLOR_FOR_SHUF_IN_ORG(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	239	容量がたりません。ＵＳＢメディアを交換してください。 X2V300 toukurma
  @Deprecated("DMY_ERR_239")
  ERR_USB_MEDIA_MEMORY_FULL_MEMORY_CHANGE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	240	SD容量がたりません。SDカードを交換してください。
  @Deprecated("DMY_ERR_240")
  ERR_SD_MEDIA_MEMORY_FULL_MEMORY_CHANGE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	241	CMN 容量がたりません。メディアを交換してください。
  @Deprecated("DMY_ERR_241")
  ERR_CMN_MEDIA_MEMORY_FULL_MEMORY_CHANGE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	242	マークを見つけることが出来ませんでした。貼り直し前のマーク探索用 X2V310 PTCN2-5-3 toukurma
  ERR_NO_MARK_RESET_OLD_MARK_PAT_CNCT(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err313],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	243	針交換時はカメラ針位置セットを行うことを推奨します
  @Deprecated("DMY_ERR_243")
  ERR_CAMERA_SET_WHEN_NEEDLE_CHANGE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	244	縫製終了。OK後ミシンが動きます。模様つなぎレジューム用 X2V310 M-25 hirata
  ERR_SEWING_OVER_MARK_PAT_CNCT_RESUME(
      systemSound: SystemSoundEnum.sewOvr2,
      messageSound: [MessageSoundEnum.t_err303],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	245	模様のデータが読めません。データが壊れている可能性があります。電源を入れ直して下さい。VcomboL V100 M-4
  @Deprecated("DMY_ERR_245")
  ERR_EMBDATA(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	246	ビデオ機能無効
  @Deprecated("DMY_ERR_246")
  ERR_VIDEO_NOT_USED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	247	容量が不足しているので、保存できません。
  ERR_NOT_ENOUGH_AVAILABLE_MEMORY_TO_SAVE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	248	(MDCカスタム模様の) 削除に失敗しました。
  ERR_MDC_IMPORT_CUSTOM_PATTERN_DELETE_FAILURE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	249	カスタム模様をすべて削除すると、編集に使われているその模様が別の模様に変わります。すべてのカスタム模様を削除してよろしいですか？ ※カスタム模様・・デコフィル模様またはモチーフ模様
  ERR_MDC_IMPORT_CUSTOM_PATTERN_ALL_DELETE_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	250	データ保存後にインポートしたカスタム模様の削除・変更を行うと、次回読み込み時にデータがオリジナルから変化します。
  ERR_MDC_IMPORT_CUSTOM_PATTERN_CHANGE_AFTER_SAVING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	251	インポートしたカスタム模様を含むデータは外部メモリに保存できません。 ※データ・・・お絵描きデータ
  ERR_MDC_IMPORT_CUSTOM_PATTERN_NOT_SAVED_EXT_MEMORY(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	252	刺繍データのみ保存します。カスタム模様を含む編集データは外部メモリに保存できません。内部メモリに保存してください。
  ERR_MDC_IMPORT_CUSTOM_PATTERN_EMB_DATA_SAVED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	253	データ記憶領域がいっぱいです。カスタム模様を１つ選択して、新しい模様に入れ換えてください。
  ERR_MDC_IMPORT_CUSTOM_PATTERN_CHOOSE_REPLACE_DATA(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	254	カスタム模様を入れ換えると、入れ換え前の模様を使用したデータでも連動して模様が変化します。本当に入れ替えますか？
  ERR_MDC_IMPORT_CUSTOM_PATTERN_REPLACE_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	255	プロジェクタ起動中です。しばらくお待ちください。T_ERR_Proj_Emb_10
  ERR_EMB_PRJ_START_PLEASE_WAIT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	256	プロジェクタ終了中です。しばらくお待ちください。T_ERR_Proj_Emb_11
  ERR_EMB_PRJ_CLOSE_PLEASE_WAIT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	257	プロジェクタ投影を終了します。* OKキーを押すと刺しゅうキャリッジが動きます。T_ERR_Proj_Emb_006
  ERR_EMB_PRJ_CLOSE_CARRY_MOVING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	258	プロジェクタ投影を開始します。T_ERR_Proj_Emb_013
  ERR_EMB_PRJ_START_NO_CARRY_MOVING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	259	刺繍枠が変更されました 超音波刺繍位置合わせ Vpro_SENSOR EPOS toukurma
  @Deprecated("DMY_ERR_259")
  ERR_EMB_ULTR_SNIC_POS_CHANGE_FRAME(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	260	の模様はデュアルフィードでは縫えません VMain VER100 DFPM-2-5 2012.04.13 HAL
  ERR_UTL_THIS_PATTERN_NOT_SEW_WHEN_USE_DF(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	261	布を取り除いてから、糸通ししてください。
  ERR_UTL_REMOVE_FABRIC_FROM_PF(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	262	プロジェクタ投影を終了します。T_ERR_Proj_Emb_003
  ERR_EMB_PRJ_CLOSE_NO_CARRY_MOVING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	263	時刻を設定してください(仮)
  ERR_SET_RTC(
      systemSound: SystemSoundEnum.accept, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	264	IIC通信に不具合が生じました。電源をいったんＯＦＦしてから再度やり直してください。VMain VER100 IIC-4-7 HAL
  ERR_IIC_COMMUNICATION(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	265	刺しゅう枠が外されたので、プロジェクタ投影を終了します。T_ERR_Proj_Emb_005
  ERR_EMB_PROJECTOR_CLOSE_NOFRAME(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	266	刺しゅう枠が外されたので、プロジェクタ投影を終了します。* OKキーを押すと刺しゅうキャリッジが動きます。T_ERR_Proj_Emb_004
  ERR_EMB_PROJECTOR_CLOSE_NOFRAME_EMB_CARRY_MOVING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	267	空き
  DMY_ERR_267,

  ///	268	LedPointerを外してください。(実用縫製時は使用不可) VMain VER100 LED_PT_REMOVE_UTL-1-1 2012/06/14 HAL
  ERR_LEDPT_CONNECT_WHEN_UTL(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	269	DFを外してください。 VMain VER100 DF_REMOVE_EMB-1-1 2012/06/14 HAL
  ERR_DF_CONNECT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	270	MyDesignCenter レジュームするかの確認(ブラザー)
  ERR_MDC_RESUME_OK_B(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	271	MyDesignCenter レジュームするかの確認(タコニー)
  ERR_MDC_RESUME_OK_T(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	272	押えスイッチで押えを上げください。 EMB用
  ERR_OSAE_UP_EMB(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	273	押えスイッチで押えを下げてください。 EMB用
  ERR_OSAE_DOWN_EMB(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	274	刺繍枠固定レバーを下げてください。刺繍位置合わせ中にレバーを上げられた場合 ver1.04 M14 Makir
  ERR_EMB_FRAME_HOLD_LEVER_DOWN_ULTR_SNIC(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	275	空き
  DMY_ERR_275,

  ///	276	設定項目をデフォルトにする時のエラー XV Setting_Default-1-1 2014/05/08 Nagai
  @Deprecated("DMY_ERR_276")
  ERR_SET_SETTINGS_DEFAULT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	277	この刺繍機は使えません XV EmbFrame_Panel-3-3 2014/06/17 Nagai
  ERR_EMB_UNIT_NOT_USE(
      systemSound: SystemSoundEnum.error, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	278	MCD 終了確認 ブラザー
  @Deprecated("DMY_ERR_278")
  ERR_MCD_FINISH_CHECK_B(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	279	模様の選択をキャンセルします。よろしいですか？
  @Deprecated("DMY_ERR_279")
  ERR_MYI_DECO_CANCEL_PTN_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	280	エリアをはみ出すのでその模様は読み込めません。
  ERR_MYI_DECO_SELPTN_AREA_OVER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	281	スキャン枠は使えません
  ERR_SCAN_FRAME_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	282	画素数が多すぎます
  @Deprecated("DMY_ERR_282")
  ERR_IMG_PIXEL_OVER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	283	これ以上入力できません。
  ERR_MYI_CANNOT_INPUT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	284	読み込む画像をセットした枠をミシンに取り付けてください。
  ERR_ATTATCH_SCAN_FRAME(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	285	表示されているエリアが変換できます
  @Deprecated("DMY_ERR_285")
  ERR_MCD_SCAN_IMG_TRIMMING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	286	つなぎモード中は、この機能は使用できません
  @Deprecated("DMY_ERR_286")
  ERR_NO_USE_FUNCTION_CNCT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	287	最大縫製エリアからはみ出るため、変換できませんでした。
  ERR_MCD_NOT_EXCHANGE_AREA_OVER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	288	メイン基板電源OFF XV V100 M-477 Nagai 2014/07/17
  ERR_MAIN_BOARD_POWER_OFF(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	289	メイン基板電源OFF(NMI) XV V100 M-477 Nagai 2014/07/17
  ERR_MAIN_BOARD_POWER_OFF_NMI(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	290	選んだ模様が消えます。よろしいですか? 《キャンセル》 《OK》※No.29「ERR_SELECTED_PATTERN_DELETE_OK」と同じメッセージ。return時のMessageにERR_POPUP_RETURN_CANCELまたはERR_POPUP_RETURN_OKが返る 20140722 Aoki Add
  ERR_SELECTED_PATTERN_DELETE_OK_POPUP(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	291	枠をはみ出します。これ以上は入力できません。
  ERR_OUTSIDE_OF_EMB_FRM_NOADD(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	292	枠をはみ出します。この機能は使用できません。
  ERR_OUTSIDE_OF_EMB_FRM_NOUSE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	293	選択した書体にない文字があるため変更できません。
  ERR_NO_CHG_FONT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	294	MCD CANCEL タコニー
  @Deprecated("DMY_ERR_294")
  ERR_MCD_CANCEL_T(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	295	MCD HOME タコニー
  @Deprecated("DMY_ERR_295")
  ERR_MCD_HOME_T(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	296	MCD 終了確認 タコニー
  @Deprecated("DMY_ERR_296")
  ERR_MCD_FINISH_CHECK_T(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	297	内側アウトラインと外側のアウトラインのステッチが重なります。ステッチ幅を小さくしてください。
  ERR_APPLIQUE_NG_EX_IN_OVERLAP(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	298	MyDesignCenter [All Clear] 描画を消しますか？ 旧MDCのID：ERR_MCD_NEW
  ERR_MDC_CREATE_NEW(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	299	MyDesignCenter [CANCEL] 刺繍選択に戻りますか？(ブラザー) 旧MDCのID：ERR_MCD_CANCEL_B
  ERR_MDC_CANCEL_B(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	300	MyDesignCenter [CANCEL] 刺繍選択に戻りますか？(タコニー) 旧MDCのID：ERR_MCD_CANCEL_T
  ERR_MDC_CANCEL_T(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	301	MyDesignCenter [HOME] HOMEに戻りますか？(ブラザー) 旧MDCのID：ERR_MCD_HOME_B
  ERR_MDC_HOME_B(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	302	MyDesignCenter [HOME] HOMEに戻りますか？(タコニー) 旧MDCのID：ERR_MCD_HOME_T
  ERR_MDC_HOME_T(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	303	2018.3.19 Aoki 無効化、XPでは不要なメッセージ
  @Deprecated("DMY_ERR_303")
  UNUSED_MESSAGE_303,

  ///	304	2018.3.19 Aoki 無効化、XPでは不要なメッセージ
  @Deprecated("DMY_ERR_304")
  UNUSED_MESSAGE_304,

  ///	305	MyDesignCenter [終了確認] Setしますか？(ブラザー) 旧MDCのID：ERR_MCD_FINISH_CHECK_B
  ERR_MDC_FINISH_CHECK_B(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	306	MyDesignCenter [終了確認] Setしますか？(タコニー) 旧MDCのID：ERR_MCD_FINISH_CHECK_T
  ERR_MDC_FINISH_CHECK_T(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	307	MyDesignCenter [刺繍変換]結果 最大縫製エリアからはみ出るため、変換できませんでした。 旧MDCのID：ERR_MCD_NOT_EXCHANGE_AREA_OVER
  ERR_MDC_NOT_EXCHANGE_AREA_OVER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	308	MyDesignCenter (MDCメモリエラー用)データ容量の制限を越えました、選べません(刺繍メモリフル) 旧MDCのID：ERR_EMB_TOO_MUCH_SELECTED_GO_MENU
  ERR_MDC_TOO_MUCH_SELECTED_GO_MENU(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	309	MyDesignCenter (MDCメモリエラー用)データ容量の制限を越えました、選べません(刺繍メモリフル)
  ERR_MDC_TOO_ALLOC_FAILURE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	310	2018.3.19 Aoki 無効化、XPでは不要なメッセージ
  @Deprecated("DMY_ERR_310")
  UNUSED_MESSAGE_310,

  ///	311	カラーソート不可
  ERR_COL_SORT_NG(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	312	BW終了しました。
  ERR_EMB_BW_SEWING_FINISH(
      systemSound: SystemSoundEnum.sewOvr1, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	313	刺繍キャリッジが動きます、手や物を離してください
  ERR_EMB_CARRY_MOVING_FRAME_MOVE_BW(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err25],
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///	314	刺しゅう枠を元の位置に戻します。よろしいですか？
  ERR_EMB_FLAME_RETURN_OK_BW(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err484],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	315	枠を付けてください
  ERR_EMB_FRAME_SET_BW(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	316	この刺しゅう枠は使えません
  ERR_THIS_FRAME_NOT_USE_BW(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	317	BW終了しました。
  ERR_EMB_BW_SEWING_OVER(
      systemSound: SystemSoundEnum.sewOvr2, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	318	他のカテゴリーの模様と組み合わせることができません
  @Deprecated("DMY_ERR_318")
  ERR_EMB_BW_THIS_PATTERN_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	319	模様が離れすぎている
  ERR_APPLIQUE_NG_DISTANCE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	320	枠からはみ出す場合
  @Deprecated("DMY_ERR_320")
  ERR_APPLIQUE_NG_SIZE_OVER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	321	複雑な形状または適さない形状のため、アップリケラインを生成できませんでした。アップリケ設定を変更するか、別の模様を選択してください。
  ERR_APPLIQUE_NG_COMPLEX(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	322	複雑な形状ではないが容量オーバー
  ERR_APPLIQUE_NG_MEM_OVER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	323	選んだデータが消えます。よろしいですか? 《キャンセル》 《OK》
  ERR_SELECTED_DATA_DELETE_OK_POPUP(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	324	糸印が消えますがよろしいですか？グループ化時用
  ERR_DELETE_BORDER_MARK_OK_GROUP(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	325	糸印が消えますがよろしいですか？アングループ時用
  ERR_DELETE_BORDER_MARK_OK_UNGROUP(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	326	糸印が消えますがよろしいですか？アップリケ遷移時用
  ERR_DELETE_BORDER_MARK_OK_WAPPEN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	327	MyDesignCenter [HOME] すべての模様を消去し、ホーム画面に移りますが、よろしいですか? 《キャンセル》 《OK》
  ERR_MDC_HOME_CLEAR_ALL_EDITING_DATA_AND_MOVE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	328	OK to delete the background image?
  ERR_MDC_BACKGROUND_DELETE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	329	糸印が消えますがよろしいですか？簡単ステップリング遷移時用
  ERR_DELETE_BORDER_MARK_OK_EASYSTTIPLE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	330	設定が変更できませんでした。縫い方設定の記憶領域が不足しています。
  ERR_MDC_NOT_CHANGE_SETTING_MEMORY_OVER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	331	データが複雑なため、変換できませんでした。 イラストスキャン メモリエラー修正 PR1000II Ver110 M-59 imaizuka
  @Deprecated("DMY_ERR_331")
  ERR_THIS_PATTERN_TOO_COMPLEX_FOR_ILLUST_SCAN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	332	製品同梱のA＋押えを取り付けてください。
  ERR_CAMERA_BH_NG(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err55],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	333	My Design Center"のスタンプ模様リストから呼び出せます。
  ERR_EMB_STAMP_MAKE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	334	IQ Designerのスタンプ模様リストから呼び出せます。
  ERR_EMB_STAMP_MAKE_TC(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	335 刺しゅうキャリッジの近くから物を離し、手を近づけないようにしてください。/*** XP VER100　XYInitialMsg-1-2　2018/04/13　HAL ***/
  ERR_XY_ZPHASE(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///  336 本体メモリーにデータを保存しました。続けて縫製しますか
  ERR_EMB_QUILT_SASHES_CONTINUE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	337 CGI終わるまで待機
  @Deprecated("DMY_ERR_337")
  ERR_WAIT_COMPLETE_CGI(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	338	「マイイラスト」選んだ模様が消えます。よろしいですか?（保存ファイル選択時用） 《キャンセル》 《OK》	/* XPV110M0544 */
  @Deprecated("DMY_ERR_338")
  ERR_MYI_DELETE_OK_FOR_SAVEDFILE_SELECT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	339	他のカテゴリーの模様と組み合わせることができません
  ERR_EMB_QUILT_THIS_PATTERN_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	340	 キルトサッシ関連：最初の模様の位置を促す。
  ERR_EMB_QUILT_SASHES_1ST_SETTING(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err581],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	341	 キルトサッシ関連：コーナーの模様の位置を促す。
  ERR_EMB_QUILT_SASHES_CORNER_SETTING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	342	 キルトサッシ関連：最初のコーナー配置ガイダンス。Moveへ促す。①
  ERR_EMB_QUILT_SASHES_1ST_CORNER_OF_MOVE(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err583],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	343	 キルトサッシ関連：最初のコーナー方向決めガイダンス。Rotateへ促す。④
  ERR_EMB_QUILT_SASHES_1ST_CORNER_OF_ROTATE(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err585],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	344	 キルトサッシ関連：端部品の配置ガイダンス。Moveへ促す。②
  ERR_EMB_QUILT_SASHES_STRAIGHT_OF_MOVE(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err584],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	345	 キルトサッシ関連：Move設定後のRotateへ促す。⑤
  ERR_EMB_QUILT_SASHES_MOVE_TO_ROTATE(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err585],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	346	 キルトサッシ関連：コーナー前方向決めガイダンス。Rotateへ促す。⑥
  ERR_EMB_QUILT_SASHES_BEFORE_CORNER_OF_ROTATE(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err585, MessageSoundEnum.t_err586],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	347	 キルトサッシ関連：コーナー配置ガイダンス。Moveへ促す。③
  ERR_EMB_QUILT_SASHES_CORNER_OF_MOVE(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err584],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	348	 キルトサッシ関連：コーナー方向決めガイダンス。Rotateへ促す。⑧
  ERR_EMB_QUILT_SASHES_CORNER_OF_ROTATE(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err585],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	349	 キルトサッシ関連：最後の部品の配置ガイダンス。Rotateへ促す。⑦
  ERR_EMB_QUILT_SASHES_LAST_OF_ROTATE(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err587],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	350	 スキャン枠を使って背景スキャンをしてください。白と黒の帯を読み取ることで、補正値が更新され、スキャン画像の精度が向上します。（OK）
  ERR_SCAN_SHADING_UPDATE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	351	 キルトサッシ関連：模様の張替えを促す。
  ERR_EMB_QUILT_SASHES_REPOSITION_SETTING(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err588],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	352	 キルトサッシ関連：最初の模様の位置を促す。
  @Deprecated("DMY_ERR_352")
  ERR_EMB_QUILT_SASHES_1ST_SETTING2(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err581_b],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	353	 カウチング関連：LEDポインタを外してください。
  ERR_LEDPT_CONNECT_WHEN_COUTING(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  354	USBケーブルを使ってComputerとの接続はできません
  @Deprecated("DMY_ERR_354")
  ERR_USB_DEVICE_CANNOT_USED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	355	 他のカテゴリーの模様と組み合わせることができません
  ERR_EMB_COUTING_THIS_PATTERN_NOT_USE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	356	この刺しゅうデータは糸色情報が不足しているため、糸色を近似して表示します。正しい糸色情報を表示するには、糸色変更画面で、糸色番号を入力してください。
  ERR_BAD_EMBROIDERY_DATA(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	357	 補正値読取スキャン
  ERR_SCAN_SHADING_UPDATE_SCAN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	358
  ERR_UPGRADE_FINISH_B(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	359  下糸を交換してください。刺しゅう枠を元の位置に戻します。よろしいですか？
  ERR_EMB_FLAME_RETURN_OK_BOBIN_CHANGE(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err120_2],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	360 ご利用前にミシンに記載されているEULAを読んでください。
  ERR_EULA_CONFIRMATION(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  361 しばらくお待ちください（CANCELボタン付き）
  @Deprecated("DMY_ERR_361")
  ERR_PLEASE_WAIT_NETDIAG(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  362 しばらくお待ちください(マニュアルメモリ消去)
  @Deprecated("DMY_ERR_362")
  ERR_MANUAL_MEMORY_CORRECTION(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  363 終点設定を終わりますよろしいですか？
  ERR_EPS_FINISH(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  364 シールが近すぎて終点設定が行えません
  @Deprecated("DMY_ERR_364")
  ERR_EPS_MARK_TOO_CLOSE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  365 設定により一時停止しました
  ERR_EPS_STOP_MSG(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  366 終点設定を終わります（規定よりも送り補正値がオーバーした場合）			/*** XPUGK2　EPS-7-12　2020/01/09　HAL ***/
  ERR_EPS_CORRECT_OVER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  367 終点設定を終わりますよろしいですか？ OKの場合プロジェクター表示へ移動 	/*** XPUGK2　EPS-1-15　2020/01/30　HAL ***/
  ERR_EPS_FINISH_PROJECTOR(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  368 指定された距離では、模様長さ調整ができません。					/*** XPUGK2　EPS-7-13　2020/01/31　HAL ***/
  ERR_EPS_CORRECT_OVER_SSKEY(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	369	刺繍枠固定レバーを下げてください。	モニタリングエラー用：Matrixからくる枠レバーエラー
  ERR_EMB_FRAME_HOLD_LEVER_DOWN_MATRIX(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  370 データ構造上、テクスチャを表示できません。
  ERR_APPLIQUE_SOME_PARTS_NOT_TEXTURE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	371 刺しゅう順が変更されます。
  ERR_EMB_CHANGE_ORDER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	372 選択したデータファイルを削除します。よろしいですか？
  @Deprecated("DMY_ERR_372")
  ERR_MDC_BG_FILE_DELETE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	373	ＵＳＢメディアがライトプロテクトされていて消去できません。ライトプロテクトを解除して消去してください。
  @Deprecated("DMY_ERR_373")
  ERR_MDC_USB_MEDIA_CANNOT_DELETE_BY_PROTECTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	374	SDカードがライトプロテクトされていて消去できません。ライトプロテクトを解除して消去してください。
  @Deprecated("DMY_ERR_374")
  ERR_MDC_SD_MEDIA_CANNOT_DELETE_BY_PROTECTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	375	CMN メディアがライトプロテクトされていて消去できません。ライトプロテクトを解除して消去してください。
  @Deprecated("DMY_ERR_375")
  ERR_MDC_CMN_MEDIA_CANNOT_DELETE_BY_PROTECTED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  376 2本針モードが解除されました。2本針が取り外されているか確認してください。  /*** XPver2.05 M-8　-1-2　2020/03/24　makiry ***/
  ERR_CHECK_TWIN_NEEDLE_REMOVED(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  377 刺しゅう枠は使用できません。スキャン専用枠をご使用ください。
  ERR_EMB_CHANGE_TO_SCAN_FRAME(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  378 終点距離まで短すぎます。縫製開始位置をもっと終点から遠くするか直前停止をOFFにしてください
  ERR_EPS_LENGTH_SSKEY(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  379 終点設定中は、このキーは使えません							/*** XPUGK2　EPS-1-26　2020/07/07　HAL ***/
  ERR_EPS_DISABLE_KEY(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  380 ネットワークに接続されていません。無線LANの設定画面を表示しますか？
  ERR_NET_DISABLE_WLAN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  381 メンテナンスのためCanvasWorkspaceサービスが停止しており、本機能はご利用いただけません。復旧まで今しばらくお待ちください。
  ERR_NET_MAINTENANCE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  382 CanvasWorkspaceにマシン登録できませんでした。<BR>最初から操作を行ってください
  ERR_REGIST_PINCODE_FAILURE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  383 PINコードが不正です。もう一度入力してください。
  ERR_REGIST_PINCODE_WRONG(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  384 マシンがインターネットに接続されていません。
  ERR_NET_NOT_CONNECT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  385 サーバーへの接続に失敗しました。<BR>プロキシ等の設定を確認してください。"
  ERR_NET_PROXY_WRONG(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  386 サーバーとの通信がタイムアウトしました。<BR>時間をおいて再試行してください。
  ERR_NET_TIMEOUT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  387 マシン登録が解除された可能性があります。CanvasWorkspaceで確認してください。
  ERR_NET_ACCESSTOKEN_WRONG(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  388 "データのダウンロードに失敗しました。<BR>最初から操作を行ってください。"
  ERR_UPLOAD_FAILURE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  389 サーバー上のテンポラリデータポケットにデータを送信しますが、よろしいですか？<BR>＊テンポラリデータポケットのデータは、一定期間後自動で削除されます。
  ERR_UPLOAD_CONFIRM(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  390 テンポラリデータポケットには既にデータがありますが、新しいデータに置き換えてよろしいですか？<BR>＊テンポラリデータポケットのデータは、一定期間後自動で削除されます。
  ERR_UPLOAD_OVERRIDE_CONFIRM(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  391 ダウンロード中にエラーが発生しましたもう一度 最初から操作を行ってください
  ERR_DOWNLOAD_FAILURE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  392 データを取り込み可能な大きさに縮小しました
  ERR_DOWNLOAD_SIZE_CHANGE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  393 テンポラリーデータポケットには読み込み可能なデータがありません。
  ERR_DOWNLOAD_UNREADABLE_DATA(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  394 CanvasWorkspace登録を解除してよろしいですか？
  @Deprecated("DMY_ERR_394")
  ERR_UNREGIST_PINCODE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  395 CanvasWorkspaceからマシン登録を削除できませんでした。ウェブサイトから手動で登録解除してください。
  ERR_UNREGIST_PINCODE_FAILURE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  396 カットデータを受信するには、CanvasWorkspaceサーバーに、ScanNCut及びこのミシンのマシン登録が必要です。（PINコード登録）。<BR>登録用の設定画面を表示しますか？
  ERR_DOWNLOAD_DISABLE_PINCODE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  397 データを送信するには、CanvasWorkspaceサーバーに、このミシンの登録が必要です。登録用の設定画面を表示しますか？
  ERR_UPLOAD_DISABLE_PINCODE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  398 マシン登録が解除された可能性があります。CanvasWorkspaceで確認してください。
  ERR_NET_SERVER_FAILURE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  399 この機能は選択されている特別な模様では使用できません。
  ERR_CANNOT_USE_SPECIAL_PATTERN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  400 縫わない設定により糸印の設定が無効になります。よろしいですか？
  ERR_MARK_DELETE_OK_FOR_NUWANAI(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  401 ステッチの長さが長い模様です。縫いはじめに糸を抜けにくくするため、止め縫いキーで止め縫いしてから、縫製を開始してください。
  ERR_RECOMEND_TOMENUI(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err785],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	402 無線LAN接続を設定してください。
  ERR_WLAN_SETUP_CONFIRMATION(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  403 終了してもいいですか？（内蔵大型つなぎの自動つなぎ終了確認）
  ERR_CONFIRM_CANCEL_AUTOCNCT(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err278],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  404 続きを縫製する予定があれば、次の模様を呼び出してください。その後電源を切っても再開できます。
  ERR_SEWING_OVER_INTERNAL_LARGECONNECT(
      systemSound: SystemSoundEnum.sewOvr2,
      messageSound: [MessageSoundEnum.t_err279],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///  405 403	続きを縫製する予定があれば、次の模様を呼び出してください。その後電源を切っても再開できます。の音が違うバージョン
  ERR_SEWING_OVER_INTERNAL_LARGECONNECT_SOUND(
      systemSound: SystemSoundEnum.accept,
      messageSound: [MessageSoundEnum.t_err279],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	406	バージョンアップしてください
  @Deprecated("DMY_ERR_406")
  ERR_RECOMMEND_UPDATE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	407 動画をインストールするために再度アップデートをしてください
  @Deprecated("DMY_ERR_407")
  ERR_VERUP_FOR_INSTALL_MOVIE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	408	EdgeToEdgeキルトガイダンス1画面 先頭模様専用のガイダンス 左上に枠を設置
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_1(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err581_b],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	409	EdgeToEdgeキルトガイダンス2画面 先頭模様専用のガイダンス 位置合わせ指示 [マスク左上角]の位置をチャコ印を目安に移動キーで配置
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_2(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err583_e],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	410 EdgeToEdgeキルトガイダンス3画面 先頭模様専用のガイダンス 回転指示 [マスク右上角]の位置をチャコ印を目安に角度キーで配置　[マスク左上角]を基準に回転する
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_3(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err585],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	411	EdgeToEdgeキルトガイダンス4画面 1行目の2列目以降用のガイダンス 位置合わせ指示 前模様の[終点]に[始点]が合うように移動キーで配置
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_4(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err584],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	412	EdgeToEdgeキルトガイダンス5画面 1行目の2列目以降最終列以外用のガイダンス 回転指示 [マスク右上角]の位置をチャコ印を目安に角度キーで配置
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_5(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err585],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	413	EdgeToEdgeキルトガイダンス6画面 1行目の最終列模様のガイダンス 回転と拡縮指示
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_6(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err586_e],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	414	EdgeToEdgeキルトガイダンス7画面 2行目以降1列目模様のガイダンス 位置合わせ指示 上模様の[マスク左下角]位置に[マスク左上角]が合うように移動キーで配置
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_7(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err584_e],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	415	EdgeToEdgeキルトガイダンス8画面 2行目以降かつ最終列以外の1列目模様のガイダンス 回転指示と拡縮指示
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_8(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err586_e],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	416	EdgeToEdgeキルトガイダンス10画面 2行目以降2列目以降模様のガイダンス 位置合わせ指示 前模様の[終点]に[始点]が合うように移動キーで配置
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_10(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err584],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	417	EdgeToEdgeキルトガイダンス11画面 2行目以降かつ最終行以外かつ2列目以降最終列未満の模様のガイダンス 回転＆拡縮指示
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_11(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err586_e],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	418	EdgeToEdgeキルトガイダンス12画面 2行目以降かつ最終行以外かつ最終列の模様のガイダンス 回転＆拡縮指示
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_12(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err586_e],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	419	EdgeToEdgeキルトガイダンス14画面 1行目の2列目以降の模様用の布張替ガイダンス
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_14(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err588_e],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	420	EdgeToEdgeキルトガイダンス15画面 2行目以降の先頭の模様用の布張替ガイダンス
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_15(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err582_e],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	421	EdgeToEdgeキルトガイダンス16画面 2行目以降の2列目以降の模様の布張替ガイダンス
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_16(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err588_e_2],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	422	位置あわせマークを取って下さい 模様つなぎ用 模様を拡縮
  ERR_REMOVE_MARK_PAT_CNCT_PATTERN_MAG(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err311_size],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	423	マーク間距離が布張替え前後で違った場合のエラー
  ERR_AFTER_MARK_PAT_CNCT_MARK_DIV(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err311_rehoop],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///  424 テーパリング設定を終わりますよろしいですか？ OKの場合プロジェクター表示へ移動
  ERR_TAPERING_FINISH_PROJECTOR(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  425 テーパリング設定を終わりますよろしいですか？
  ERR_TAPERING_FINISH(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  426 シールが近すぎて終点設定が行えません
  @Deprecated("DMY_ERR_426")
  ERR_TAPERING_MARK_TOO_CLOSE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  427 現在のテーパリング縫製を終了しますよろしいですか？(頭出しキー押下)
  ERR_TAPERING_CURRENT_FINISH_CUETOP(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  428 現在のテーパリング縫製を終了しますよろしいですか？(反転キー押下)
  ERR_TAPERING_CURRENT_FINISH_FLIP_ON(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  429 現在のテーパリング縫製を終了しますよろしいですか？(反転キー押下)
  ERR_TAPERING_CURRENT_FINISH_FLIP_OFF(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  430 現在のテーパリング縫製を終了しますよろしいですか？(リトリーブキー押下)
  ERR_TAPERING_CURRENT_FINISH_RETRIEVE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  431 現在のテーパリング縫製を終了しますよろしいですか？(終了方法：ボタンキー押下)
  @Deprecated("DMY_ERR_431")
  ERR_TAPERING_CURRENT_FINISH_BUTTON(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  432 現在のテーパリング縫製を終了しますよろしいですか？(終了方法：回数指定キー押下)
  @Deprecated("DMY_ERR_432")
  ERR_TAPERING_CURRENT_FINISH_CYCLENUM(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  433 現在のテーパリング縫製を終了しますよろしいですか？(終了方法：終点設定キー押下)
  @Deprecated("DMY_ERR_433")
  ERR_TAPERING_CURRENT_FINISH_ENDPOINT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  434 現在のテーパリング縫製を終了しますよろしいですか？(終了角度キー押下)
  ERR_TAPERING_CURRENT_FINISH_STARTANGLE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  435 現在のテーパリング縫製を終了しますよろしいですか？(終了角度キー押下)
  ERR_TAPERING_CURRENT_FINISH_ENDANGLE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  436 現在のテーパリング縫製を終了しますよろしいですか？(終了角度キー押下)
  ERR_TAPERING_CURRENT_FINISH_INCREASENUM(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  437 現在のテーパリング縫製を終了しますよろしいですか？(終了角度キー押下)
  ERR_TAPERING_CURRENT_FINISH_DECREASENUM(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  438 現在のテーパリング縫製を終了しますよろしいですか？
  ERR_TAPERING_CURRENT_FINISH(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  439 テーパリング縫製中は、このキーは使えません
  ERR_TAPERING_DISABLE_KEY(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	440	EdgeToEdgeキルトガイダンス9画面 最終行かつ1列目の模様用のガイダンス 回転、拡縮指示
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_9(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err586_e],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	441	EdgeToEdgeキルトガイダンス13画面 最終行かつ2列目以降最終列未満の模様のガイダンス 回転、拡縮指示
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_13(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err586_e],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	442	EdgeToEdgeキルトガイダンス17画面 最終行かつ最終列の模様のガイダンス 回転、拡縮指示
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_17(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err586_e],
      errAct: ErrAct_t.ERROR_ACT_NON),

  ///	443 内蔵ポケットにしか保存できない時の容量上限による保存エラー
  @Deprecated("DMY_ERR_443")
  ERR_DATA_MOMORY_FULL_DELETE_OTHER_DATA(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  444 マシン名を変更する場合は無線LANをONしてください。
  ERR_MACHINENAME_ENABLE_WLAN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  445 マシン名の変更ができませんでした。
  ERR_REGIST_MACHINE_NAME_FAILURE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  446 記憶できない模様が含まれています。
  ERR_PATTERN_CANNOT_SAVE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  447 このデータを使用するためには、ミシンのソフトウェアを最新のバージョンにアップデートする必要があります。
  ERR_DOWNLOAD_PAIDCONT_UPDATE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	448 WLAN電波設定が規程の値以外の時に表示するエラー	PHFIRMXP-2204 WLAN電波設定異常チェック makiry
  ERR_WLAN_SETTING_IMPROPER(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  449 新しいフォントデータ形式に変換しますか？					PF1_1103
  @Deprecated("DMY_ERR_449")
  ERR_FONT_CNG_PF1_OK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  450 古い文字データ形式なので編集に制限がかかります            PF1_1103
  @Deprecated("DMY_ERR_450")
  ERR_FONT_CNG_NG_FORMAT(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  451 アプリ側でエラー発生状態　操作ロック用
  ERR_APP(systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  452 1色分縫製完了
  ERR_SEWING_PATTERN_OVER(
      systemSound: SystemSoundEnum.sewOvr1, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  453 DFを外してください。 ( Closeなし )
  ERR_DF_CONNECT_NOCLOSE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///  454 SR押さえを外してください。 ( Closeなし )
  ERR_REMOVE_SR_NOCLOSE(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///	455	刺しゅうキャリッジが動きます。刺しゅうキャリッジの近くから物を離し、手を近づけないようにしてください。
  ERR_EMB_CARRY_MOVING_FRAME_REMOVE(
      systemSound: SystemSoundEnum.warning,
      messageSound: [MessageSoundEnum.t_err25],
      errAct: ErrAct_t.ERROR_ACT_KEY),

  ///	456 枠を元に戻すにはOKキーを押してください。
  ERR_EMB_FRAME_REMOVE_RERUEN(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  ///  457 アプリ側でエラー発生状態　操作ロック用
  ERR_APP_ACT_NON(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_NON),

  ///  458 アプリ側でエラー発生状態　操作ロック用
  ERR_APP_ACT_NON_WITH_PUSH(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_NON_WITH_PUSH),

  ///  459 アプリ側でエラー発生状態　上書き、メカ操作すべてLOCK
  ERR_APP_ALL_LOCK(
      systemSound: SystemSoundEnum.warning,
      errAct: ErrAct_t.ERROR_ACT_ALL_LOCK),

  ///
  ERR_CODE_MAX(systemSound: SystemSoundEnum.warning),

  ///
  /// 下記はアプリ追加
  ///

  cameraPopup(errAct: ErrAct_t.ERROR_ACT_KEY), // PanelPopupRoute

  screenShotSavePopup(errAct: ErrAct_t.ERROR_ACT_KEY), // PanelPopupRoute

  layoutCamera(errAct: ErrAct_t.ERROR_ACT_KEY), // PanelPopupRoute

  stitch_camera(errAct: ErrAct_t.ERROR_ACT_KEY), // PanelPopupRoute

  errCheckIsNeedDownloadSound(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errNotConnectToInternet(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errProcessingComplete(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errProcessingInterruptions(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errProcessingFailed(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errDownloadMessageSoundPopup(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errUpdateFirmFirst(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errSnowmanDeleteBorderOk(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errSrUnableToCommunicate(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errSnowmanConfirm(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errConvertEmbMoveToEdit(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errMoveMask(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errDeleteArtspira(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errHomeVoiceGuidance(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errImgPixelAndSpaceOver(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errPhotoStitchExit(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errFilterCleared(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errPF1FontChangeOK(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errPF1FontChangeNG(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errUpdateWlanOff(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errorReadPM9oldVersion(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errorImageTooLarge(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  errorLibExceptionMemoryFull(
      systemSound: SystemSoundEnum.warning, errAct: ErrAct_t.ERROR_ACT_KEY),

  picturePlayPleaseWait(errAct: ErrAct_t.ERROR_ACT_KEY),

  picturePlayPleaseWaitWithCancel(errAct: ErrAct_t.ERROR_ACT_KEY);

  const GlobalPopupRouteEnum({
    this.systemSound,
    this.messageSound,
    this.errAct,
    this.isClickableInScreenLock = false,
  });

  ///
  /// null 読み上げない
  ///
  final SystemSoundEnum? systemSound;
  final List<MessageSoundEnum>? messageSound;

  final ErrAct_t? errAct;

  ///
  /// デフォルトでは、繰り返すボタンが表示されます
  ///
  final bool? isDisplayReplayButton = true;

  ///
  /// ロック画面時にポップアップのクリックが透過可能
  ///
  final bool? isClickableInScreenLock;

  @override
  String toString() => super.toString().split(".").last;

  ///
  /// 列挙値を取得する
  /// 例：
  /// GlobalPopupRouteEnum.errDummy == GlobalPopupRouteEnum.praseByName("errDummy")
  ///
  static GlobalPopupRouteEnum? praseByName(String routeName) {
    for (var value in GlobalPopupRouteEnum.values) {
      if (value.name == routeName) {
        return value;
      }
    }
    return null;
  }
}

///
/// それぞれの画面のルーティング
///
final Map<String, GlobalPopupBuilder> iivoGlobalPopupRoutes = {
  GlobalPopupRouteEnum.ERR_DUMMY.name: GlobalPopupBuilder(
    builder: (context) => const ErrDummy(),
  ),
  GlobalPopupRouteEnum.ERR_ANZENSOUTI.name: GlobalPopupBuilder(
    builder: (context) => const ErrAnzensouti(),
  ),
  GlobalPopupRouteEnum.ERR_UPPER_THREAD.name: GlobalPopupBuilder(
    builder: (context) => const ErrUpperThread(),
  ),
  GlobalPopupRouteEnum.ERR_OSAE_LEVER_UP.name: GlobalPopupBuilder(
    builder: (context) => const ErrOsaeLeverUp(),
  ),
  GlobalPopupRouteEnum.ERR_OSAE_LEVER_DOWN.name: GlobalPopupBuilder(
    builder: (context) => const ErrOsaeLeverDown(),
  ),
  GlobalPopupRouteEnum.ERR_NO_MEMORY_CARD.name: GlobalPopupBuilder(
    builder: (context) => const ErrNoMemoryCard(),
  ),
  GlobalPopupRouteEnum.ERR_CANNOT_USE_CARD.name: GlobalPopupBuilder(
    builder: (context) => const ErrCannotUseCard(),
  ),
  GlobalPopupRouteEnum.ERR_NO_MORE_SELECT.name: GlobalPopupBuilder(
    builder: (context) => const ErrNoMoreSelect(),
  ),
  GlobalPopupRouteEnum.ERR_EMBUNIT_NOT_ATT.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbunitNotAtt(),
  ),
  GlobalPopupRouteEnum.ERR_EMBUNIT_IS_ATT.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbunitIsAtt(),
  ),
  GlobalPopupRouteEnum.ERR_EMBUNIT_IS_ATT2.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbunitIsAtt2(),
  ),
  GlobalPopupRouteEnum.ERR_FOOT_CONTROLER_NOT_USE.name: GlobalPopupBuilder(
    builder: (context) => const ErrFootControlerNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbTooMuchSelected(),
  ),
  GlobalPopupRouteEnum.ERR_NEEDLE_UP.name: GlobalPopupBuilder(
    builder: (context) => const ErrNeedleUp(),
  ),
  GlobalPopupRouteEnum.ERR_BORDER.name: GlobalPopupBuilder(
    builder: (context) => const DmyErr014(),
  ),
  GlobalPopupRouteEnum.ERR_NOT_SS_BOTTON_BY_FOOT_CONTROLER.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrNotSsBottonByFootControler(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDIT_END_YET.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbEditEndYet(),
  ),
  GlobalPopupRouteEnum.ERR_UTL_EDIT_END_YET.name: GlobalPopupBuilder(
    builder: (context) => const ErrUtlEditEndYet(),
  ),
  GlobalPopupRouteEnum.ERR_SELECT_STITCH.name: GlobalPopupBuilder(
    builder: (context) => const ErrSelectStitch(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PATTERN_EXCEEDED_FOR_SELECT_ARRAY.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbPatternExceededForSelectArray(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PATTERN_EXCEEDED_FOR_SELECT_PATTERN.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbPatternExceededForSelectPattern(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_SELECT_PATTERN_ROTATE90.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbSelectPatternRotate90(),
  ),
  GlobalPopupRouteEnum.ERR_SELECT_PATTERN.name: GlobalPopupBuilder(
    builder: (context) => const ErrSelectPattern(),
  ),
  GlobalPopupRouteEnum.ERR_NOW_MOMORYING_FLASH.name: GlobalPopupBuilder(
    builder: (context) => const ErrNowMomoryingFlash(),
  ),
  GlobalPopupRouteEnum.ERR_SD_NOW_WRITING.name: GlobalPopupBuilder(
    builder: (context) => const DmyErr024(),
  ),
  GlobalPopupRouteEnum.ERR_USB_HOST_NOW_WRITING.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbHostNowWriting(),
  ),
  GlobalPopupRouteEnum.ERR_USB_HOST2_NOW_WRITING.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbHostNowWriting(),
  ),
  GlobalPopupRouteEnum.ERR_USB_FUNCTION_NOW_WRITING.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbFunctionNowWriting(),
  ),
  GlobalPopupRouteEnum.ERR_LOWER_THREAD_DECREASED.name: GlobalPopupBuilder(
    builder: (context) => const ErrLowerThreadDecreased(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_CARRY_MOVING.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbCarryMoving(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_KIOKU_MEMORY_DELETE.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbKiokuMemoryDelete(),
  ),
  GlobalPopupRouteEnum.ERR_SELECTED_STITCH_CANCEL_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrSelectedStitchCancelOk(),
  ),
  GlobalPopupRouteEnum.ERR_NOW_DATA_LOADING.name: GlobalPopupBuilder(
    builder: (context) => const ErrNowDataLoading(),
  ),
  GlobalPopupRouteEnum.ERR_SELECTED_PATTERN_CANCEL_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrSelectedPatternCancelOk(),
  ),
  GlobalPopupRouteEnum.ERR_UTL_MEMORY_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrUtlMemoryOk(),
  ),
  GlobalPopupRouteEnum.ERR_CHANGE_L_FRAME.name: GlobalPopupBuilder(
    builder: (context) => const ErrChangeLFrame(),
  ),
  GlobalPopupRouteEnum.ERR_CHANGE_L_OR_LM_FRAME.name: GlobalPopupBuilder(
    builder: (context) => const ErrChangeLOrLmFrame(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FRAME_OFF.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbFrameOff(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FRAME_SET.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbFrameSet(),
  ),
  GlobalPopupRouteEnum.ERR_USE_FOOT_CONTROLER_WHEN_WIDTH.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrUseFootControlerWhenWidth(),
  ),
  GlobalPopupRouteEnum.ERR_THREAD_ANZZENSOUTI.name: GlobalPopupBuilder(
    builder: (context) => const ErrThreadAnzzensouti(),
  ),
  GlobalPopupRouteEnum.ERR_TWIN_NEEDLE_NOT_SELECT_PATTERN.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTwinNeedleNotSelectPattern(),
  ),
  GlobalPopupRouteEnum.ERR_DATA_MOMORY_FULL.name: GlobalPopupBuilder(
    builder: (context) => const ErrDataMomoryFull(),
  ),
  GlobalPopupRouteEnum.ERR_FD_ERROR.name: GlobalPopupBuilder(
    builder: (context) => const ErrFdError(),
  ),
  GlobalPopupRouteEnum.ERR_THREAD_HOLDER_LEFT.name: GlobalPopupBuilder(
    builder: (context) => const ErrThreadHolderLeft(),
  ),
  GlobalPopupRouteEnum.ERR_OSAE_LEVER_UP_NOCLOSE.name: GlobalPopupBuilder(
    builder: (context) => const ErrOsaeLeverUpNoclose(),
  ),
  GlobalPopupRouteEnum.ERR_TWIN_NEEDLE_NOT_STRAIGHT_NEEDLE_PLATE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTwinNeedleNotStraightNeedlePlate(),
  ),
  GlobalPopupRouteEnum.ERR_NEEDLE_UP_EMB_NOCLOSE.name: GlobalPopupBuilder(
    builder: (context) => const ErrNeedleUpEmbNoclose(),
  ),
  GlobalPopupRouteEnum.ERR_PATTERN_DELETE_OK_FD_FOR_SELECT_SCREEN.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrPatternDeleteOkFdForSelectScreen(),
  ),
  GlobalPopupRouteEnum.ERR_PATTERN_DELETE_OK_FD_FOR_DELETE_SCREEN.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrPatternDeleteOkFdForDeleteScreen(),
  ),
  GlobalPopupRouteEnum.ERR_PATTERN_DELETE_OK_FLASH_FOR_SELECT_SCREEN.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrPatternDeleteOkFlashForSelectScreen(),
  ),
  GlobalPopupRouteEnum.ERR_PATTERN_DELETE_OK_FLASH_FOR_DELETE_SCREEN.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrPatternDeleteOkFlashForDeleteScreen(),
  ),
  GlobalPopupRouteEnum.ERR_FLASH_MEMORY_FULL.name: GlobalPopupBuilder(
    builder: (context) => const ErrFlashMemoryFull(),
  ),
  GlobalPopupRouteEnum.ERR_UTL_DELETE_OK_FOR_NORMAL.name: GlobalPopupBuilder(
    builder: (context) => const ErrUtlDeleteOkForNormal(),
  ),
  GlobalPopupRouteEnum.ERR_UTL_DELETE_OK_FOR_DELETE.name: GlobalPopupBuilder(
    builder: (context) => const ErrUtlDeleteOkForDelete(),
  ),
  GlobalPopupRouteEnum.ERR_SCAN_NORMAL_END.name: GlobalPopupBuilder(
    builder: (context) => const DmyErr055(),
  ),
  GlobalPopupRouteEnum.ERR_NOW_DELETING.name: GlobalPopupBuilder(
    builder: (context) => const ErrNowDeleting(),
  ),
  GlobalPopupRouteEnum.ERR_RED_SQUARE_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrRedSquareOver(),
  ),
  GlobalPopupRouteEnum.ERR_NO_SAVE_PATTERN.name: GlobalPopupBuilder(
    builder: (context) => const ErrNoSavePattern(),
  ),
  GlobalPopupRouteEnum.ERR_MYI_DELETE_OK_FOR_NORMAL.name: GlobalPopupBuilder(
    builder: (context) => const ErrMyiDeleteOkForNormal(),
  ),
  GlobalPopupRouteEnum.ERR_MYI_DELETE_OK_FOR_DELETE.name: GlobalPopupBuilder(
    builder: (context) => const ErrMyiDeleteOkForDelete(),
  ),
  GlobalPopupRouteEnum.ERR_FD_WRONG_FORMAT.name: GlobalPopupBuilder(
    builder: (context) => const ErrFdWrongFormat(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PATTERN_EXCEEDED.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbPatternExceeded(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbTooMuchSelectedGoMenu(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FRAME_OFF_NOCLOSE.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbFrameOffNoclose(),
  ),
  GlobalPopupRouteEnum.ERR_NO_MORE_SELECT_FOR_EMB.name: GlobalPopupBuilder(
    builder: (context) => const ErrNoMoreSelectForEmb(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_SELECT_PATTERN_ROTATE90_NOT_DELETE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbSelectPatternRotate90NotDelete(),
  ),
  GlobalPopupRouteEnum.ERR_CHANGE_L_OR_LM_OR_M_FRAME.name: GlobalPopupBuilder(
    builder: (context) => const ErrChangeLOrLmOrMFrame(),
  ),
  GlobalPopupRouteEnum.ERR_THIS_PATTARN_NOT_THIS_FRAME.name: GlobalPopupBuilder(
    builder: (context) => const ErrThisPattarnNotThisFrame(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_THIS_PATTERN_NOT_USE.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbThisPatternNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_CUSTOM_THREAD_CHANGE_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrCustomThreadChangeOk(),
  ),
  GlobalPopupRouteEnum.ERR_CUSTOM_THREAD_ALL_DELETE_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrCustomThreadAllDeleteOk(),
  ),
  GlobalPopupRouteEnum.ERR_COLOR_CHANGE_BACK_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrColorChangeBackOk(),
    barrier: false,
  ),
  GlobalPopupRouteEnum.ERR_EEPROM_DATA_CHANGE_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrEepromDataChangeOk(),
  ),
  GlobalPopupRouteEnum.ERR_RESUME_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrResumeOk(),
  ),
  GlobalPopupRouteEnum.ERR_SR_READY_TIMEOUT.name: GlobalPopupBuilder(
    builder: (context) => const ErrSrReadyTimeout(),
  ),
  GlobalPopupRouteEnum.ERR_SR_CONNECTED.name: GlobalPopupBuilder(
    builder: (context) => const ErrSrConnected(),
  ),
  GlobalPopupRouteEnum.ERR_SR_DISCONNECTED.name: GlobalPopupBuilder(
    builder: (context) => const ErrSrDisconnected(),
  ),
  GlobalPopupRouteEnum.ERR_EMBUNIT_SET.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbunitSet(),
  ),
  GlobalPopupRouteEnum.ERR_USB_MEDIA_SET.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbMediaSet(),
  ),
  GlobalPopupRouteEnum.ERR_SD_MEDIA_SET.name: GlobalPopupBuilder(
    builder: (context) => const ErrSdMediaSet(),
  ),
  GlobalPopupRouteEnum.ERR_CHANGE_EXCEPT_S_FRAME.name: GlobalPopupBuilder(
    builder: (context) => const ErrChangeExceptSFrame(),
  ),
  GlobalPopupRouteEnum.ERR_USB_MEDIA_NOT_USE.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbMediaNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_SD_MEDIA_NOT_USE.name: GlobalPopupBuilder(
    builder: (context) => const ErrSdMediaNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_TWIN_NEEDLE_GOTO_SR.name: GlobalPopupBuilder(
    builder: (context) => const ErrSrTwinNeedle(),
  ),
  GlobalPopupRouteEnum.ERR_WARN_DONT_USE_OPEN_TOE_FOOT_SR.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrWarnDontUseOpenToeFootSr(),
  ),
  GlobalPopupRouteEnum.ERR_REMOVE_SR.name: GlobalPopupBuilder(
    builder: (context) => const ErrRemoveSr(),
  ),
  GlobalPopupRouteEnum.ERR_USB_MEDIA_BROKEN.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbMediaBroken(),
  ),
  GlobalPopupRouteEnum.ERR_SD_MEDIA_BROKEN.name: GlobalPopupBuilder(
    builder: (context) => const ErrSdMediaBroken(),
  ),
  GlobalPopupRouteEnum.ERR_SR_SELECT_MODE.name: GlobalPopupBuilder(
    builder: (context) => const ErrSRSelectMode(),
  ),
  GlobalPopupRouteEnum.DMY_ERR_090.name: GlobalPopupBuilder(
    builder: (context) => const DmyErr090(),
  ),
  GlobalPopupRouteEnum.DMY_ERR_091.name: GlobalPopupBuilder(
    builder: (context) => const DmyErr091(),
  ),
  GlobalPopupRouteEnum.DMY_ERR_092.name: GlobalPopupBuilder(
    builder: (context) => const DmyErr092(),
  ),
  GlobalPopupRouteEnum.ERR_USB_MEDIA_CHANGED.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbMediaChanged(),
  ),
  GlobalPopupRouteEnum.ERR_SD_MEDIA_CHANGED.name: GlobalPopupBuilder(
    builder: (context) => const ErrSdMediaChanged(),
  ),
  GlobalPopupRouteEnum.ERR_CMN_MEDIA_CHANGED.name: GlobalPopupBuilder(
    builder: (context) => const ErrCmnMediaChanged(),
  ),
  GlobalPopupRouteEnum.ERR_USB_MEDIA_CANNOT_WRITE_BY_PROTECTED.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrUsbMediaCannotWriteByProtected(),
  ),
  GlobalPopupRouteEnum.ERR_SD_MEDIA_CANNOT_WRITE_BY_PROTECTED.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSdMediaCannotWriteByProtected(),
  ),
  GlobalPopupRouteEnum.ERR_CMN_MEDIA_CANNOT_WRITE_BY_PROTECTED.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrCmnMediaCannotWriteByProtected(),
  ),
  GlobalPopupRouteEnum.ERR_USB_MEDIA_CANNOT_DELETE_BY_PROTECTED.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrUsbMediaCannotDeleteByProtected(),
  ),
  GlobalPopupRouteEnum.ERR_SD_MEDIA_CANNOT_DELETE_BY_PROTECTED.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSdMediaCannotDeleteByProtected(),
  ),
  GlobalPopupRouteEnum.ERR_CMN_MEDIA_CANNOT_DELETE_BY_PROTECTED.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrCmnMediaCannotDeleteByProtected(),
  ),
  GlobalPopupRouteEnum.ERR_USB_MEDIA_ERROR.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbMediaError(),
  ),
  GlobalPopupRouteEnum.ERR_SD_MEDIA_ERROR.name: GlobalPopupBuilder(
    builder: (context) => const ErrSdMediaError(),
  ),
  GlobalPopupRouteEnum.ERR_CMN_MEDIA_ERROR.name: GlobalPopupBuilder(
    builder: (context) => const ErrCmnMediaError(),
  ),
  GlobalPopupRouteEnum.ERR_USB_MEDIA_CANNOT_READ.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbMediaCannotRead(),
  ),
  GlobalPopupRouteEnum.ERR_SD_MEDIA_CANNOT_READ.name: GlobalPopupBuilder(
    builder: (context) => const ErrSdMediaCannotRead(),
  ),
  GlobalPopupRouteEnum.ERR_CMN_MEDIA_CANNOT_READ.name: GlobalPopupBuilder(
    builder: (context) => const ErrCmnMediaCannotRead(),
  ),
  GlobalPopupRouteEnum.ERR_USB_MEDIA_NOW_FORMATTING.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbMediaNowFormatting(),
  ),
  GlobalPopupRouteEnum.ERR_USB_NOW_COMMUNICATING.name: GlobalPopupBuilder(
    builder: (context) => const DmyErr109(),
  ),
  GlobalPopupRouteEnum.ERR_SD_NOW_COMMUNICATING.name: GlobalPopupBuilder(
    builder: (context) => const ErrSdNowCommunicating(),
  ),
  GlobalPopupRouteEnum.ERR_CMN_NOW_COMMUNICATING.name: GlobalPopupBuilder(
    builder: (context) => const ErrCmnNowCommunicating(),
  ),
  GlobalPopupRouteEnum.ERR_PLEASE_WAIT.name: GlobalPopupBuilder(
    builder: (context) => const ErrPleaseWait(),
  ),
  GlobalPopupRouteEnum.ERR_BLUE_LINE_IS_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrBlueLineIsOver(),
  ),
  GlobalPopupRouteEnum.ERR_SEW_NEXT_PARTS_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrSewNextPartsOk(),
    barrier: false,
  ),
  GlobalPopupRouteEnum.ERR_SEWING_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrSewingOver(),
  ),
  GlobalPopupRouteEnum.ERR_POCKET_FULL.name: GlobalPopupBuilder(
    builder: (context) => const ErrPocketFull(),
  ),
  GlobalPopupRouteEnum.ERR_OSAE_UP.name: GlobalPopupBuilder(
    builder: (context) => const ErrOsaeUp(),
  ),
  GlobalPopupRouteEnum.ERR_OSAE_DOWN.name: GlobalPopupBuilder(
    builder: (context) => const ErrOsaeDown(),
  ),
  GlobalPopupRouteEnum.ERR_THREAD_THROUGH_FAILED.name: GlobalPopupBuilder(
    builder: (context) => const ErrThreadThroughFailed(),
  ),
  GlobalPopupRouteEnum.ERR_USB_UPGRADE_START.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbUpgradeStart(),
  ),
  GlobalPopupRouteEnum.ERR_USB_UPGRADE_FINISH.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbUpgradeFinish(),
  ),
  GlobalPopupRouteEnum.ERR_USB_UPGRADE_FILE_IS_ERROR.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbUpgradeFileIsError(),
  ),
  GlobalPopupRouteEnum.ERR_DATA_ERROR.name: GlobalPopupBuilder(
    builder: (context) => const ErrDataError(),
  ),
  GlobalPopupRouteEnum.ERR_FLASH_ROM_ERROR.name: GlobalPopupBuilder(
    builder: (context) => const ErrFlashRomError(),
  ),
  GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF.name: GlobalPopupBuilder(
    builder: (context) => const ErrTroubleOccoredPowerOff(),
  ),
  GlobalPopupRouteEnum.ERR_CHANGE_NEEDLE_BOARD.name: GlobalPopupBuilder(
    builder: (context) => const ErrChangeNeedleBoard(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FLAME_RETURN_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbFlameReturnOk(),
  ),
  GlobalPopupRouteEnum.ERR_ALL_DELETE_BORDER_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrAllDeleteBorderOk(),
  ),
  GlobalPopupRouteEnum.ERR_THIS_USB_MEDIA_NOT_USE.name: GlobalPopupBuilder(
    builder: (context) => const ErrThisUsbMediaNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_THIS_SD_MEDIA_NOT_USE.name: GlobalPopupBuilder(
    builder: (context) => const ErrThisSdMediaNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_THIS_CMN_MEDIA_NOT_USE.name: GlobalPopupBuilder(
    builder: (context) => const ErrThisCmnMediaNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_USB_LINE_BROKEN.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbLineBroken(),
  ),
  GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF2.name: GlobalPopupBuilder(
    builder: (context) => const ErrTroubleOccoredPowerOff2(),
  ),
  GlobalPopupRouteEnum.ERR_PATTERN_DELETE_OK_HOST_FOR_SELECT_SCREEN.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrPatternDeleteOkHostForSelectScreen(),
  ),
  GlobalPopupRouteEnum.ERR_PATTERN_DELETE_OK_HOST_FOR_DELETE_SCREEN.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrPatternDeleteOkHostForDeleteScreen(),
  ),
  GlobalPopupRouteEnum.ERR_ALL_LOCK_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrAllLockOk(),
  ),
  GlobalPopupRouteEnum.ERR_PATTERN_CANCEL_OK_BY_DELETE_KEYS.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrPatternCancelOkByDeleteKeys(),
  ),
  GlobalPopupRouteEnum.ERR_RIGHT_BOBBIN_HOLDER.name: GlobalPopupBuilder(
    builder: (context) => const ErrRightBobbinHolder(),
  ),
  GlobalPopupRouteEnum.ERR_PFT_MOVE.name: GlobalPopupBuilder(
    builder: (context) => const ErrPftMove(),
  ),
  GlobalPopupRouteEnum.ERR_CHANGE_NEEDLE_BOARD_NOCLOSE.name: GlobalPopupBuilder(
    builder: (context) => const ErrChangeNeedleBoardNoclose(),
  ),
  GlobalPopupRouteEnum.ERR_CUR_DELETE_BORDER_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrCurDeleteBorderOk(),
  ),
  GlobalPopupRouteEnum.ERR_FAIL_PM_INITIAL.name: GlobalPopupBuilder(
    builder: (context) => const ErrFailPmInitial(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_CARRY_MOVING_NOT_OVER_WRITE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbCarryMovingNotOverWrite(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_CARRY_MOVING_FRAME_MOVE.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbCarryMovingFrameMove(),
  ),
  GlobalPopupRouteEnum.ERR_NEEDLE_UP_FRAME_MOVE.name: GlobalPopupBuilder(
    builder: (context) => const ErrNeedleUpFrameMove(),
  ),
  GlobalPopupRouteEnum.ERR_BOTTON_ANAKAGARI_LEVER_UP_FRAME_MOVE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrBottonAnakagariLeverUpFrameMove(),
  ),
  GlobalPopupRouteEnum.ERR_PFT_MOVE_FRAME_MOVE.name: GlobalPopupBuilder(
    builder: (context) => const ErrPftMoveFrameMove(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FRAME_OFF_FRAME_MOVE.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbFrameOffFrameMove(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FRAME_HOLD_LEVER_DOWN.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbFrameHoldLeverDown(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_LOWER_THREAD_DECREASED.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbLowerThreadDecreased(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PATTERN_EXCEEDED_FOR_SELECT_PATTERN_NOT_DELETE
      .name: GlobalPopupBuilder(
    builder: (context) =>
        const ErrEmbPatternExceededForSelectPatternNotDelete(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_SELECT_PATTERN_ROTATE90_WITH_ALL_DELETE_CLOSE
      .name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbSelectPatternRotate90WithAllDeleteClose(),
  ),
  GlobalPopupRouteEnum.ERR_FAIL_SAFE.name: GlobalPopupBuilder(
    builder: (context) => const ErrFailSafe(),
  ),
  GlobalPopupRouteEnum.ERR_SELECTED_STITCH_CANCEL_OK_FOR_ADVICE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSelectedStitchCancelOkForAdvice(),
  ),
  GlobalPopupRouteEnum.ERR_TWIN_NEEDLE_AUTO_THREAD_NOT_USED.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTwinNeedleAutoThreadNotUsed(),
  ),
  GlobalPopupRouteEnum.ERR_THREAD_RESET.name: GlobalPopupBuilder(
    builder: (context) => const ErrThreadReset(),
  ),
  GlobalPopupRouteEnum.DMY_ERR_157.name: GlobalPopupBuilder(
    builder: (context) => const DmyErr157(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_POSITIONING_RESET.name: GlobalPopupBuilder(
    builder: (context) => const DmyErr158(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_POSITIONING_BORDER_RESET.name:
      GlobalPopupBuilder(
    builder: (context) => const DmyErr159(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_POSITIONING_SCAN_CANCEL_FRAME_MOVE.name:
      GlobalPopupBuilder(
    builder: (context) => const DmyErr160(),
  ),
  GlobalPopupRouteEnum.ERR_ANGOU_NOT_USE.name: GlobalPopupBuilder(
    builder: (context) => const ErrAngouNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FRAME_HOLD_LEVER_DOWN_POWER_ON.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbFrameHoldLeverDownPowerOn(),
  ),
  GlobalPopupRouteEnum.ERR_THIS_FLAME_NOT_USE.name: GlobalPopupBuilder(
    builder: (context) => const ErrThisFlameNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_THIS_PATTERN_MEMORY_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrThisPatternMemoryOver(),
  ),
  GlobalPopupRouteEnum.ERR_MAINTENANCE.name: GlobalPopupBuilder(
    builder: (context) => const ErrMaintenance(),
  ),
  GlobalPopupRouteEnum.ERR_NEEDLE_UP_THREADER_NOCLOSE.name: GlobalPopupBuilder(
    builder: (context) => const ErrNeedleUpThreaderNoclose(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_POSITIONING_WARNING.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbPositioningWarning(),
  ),
  GlobalPopupRouteEnum.ERR_RECOGNIZING.name: GlobalPopupBuilder(
    builder: (context) => const ErrRecognizing(),
    barrier: false,
  ),
  GlobalPopupRouteEnum.ERR_NO_POSITION_MARK.name: GlobalPopupBuilder(
    builder: (context) => const ErrNoPositionMark(),
  ),
  GlobalPopupRouteEnum.ERR_REMOVE_POSITION_MARK.name: GlobalPopupBuilder(
    builder: (context) => const ErrRemovePositionMark(),
  ),
  GlobalPopupRouteEnum.ERR_CHANGE_NEEDLE_BOARD2_NOCLOSE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrChangeNeedleBoard2Noclose(),
  ),
  GlobalPopupRouteEnum.ERR_THIS_PICTURE_DELETE_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrThisPictureDeleteOk(),
  ),
  GlobalPopupRouteEnum.ERR_IMG_FILE_SIZE.name: GlobalPopupBuilder(
    builder: (context) => const ErrImgFileSize(),
  ),
  GlobalPopupRouteEnum.ERR_IMG_FILE_FORMAT.name: GlobalPopupBuilder(
    builder: (context) => const ErrImgFileFormat(),
  ),
  GlobalPopupRouteEnum.ERR_HOME_RESUME_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrHomeResumeOk(),
  ),
  GlobalPopupRouteEnum.ERR_MAINTENANCE_1000H.name: GlobalPopupBuilder(
    builder: (context) => const ErrMaintenance1000h(),
  ),
  GlobalPopupRouteEnum.ERR_POSITIONING_AREA_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrPositioningAreaOver(),
  ),
  GlobalPopupRouteEnum.ERR_RECOGNIZING_SMALL.name: GlobalPopupBuilder(
    builder: (context) => const ErrRecognizingSmall(),
    barrier: false,
  ),
  GlobalPopupRouteEnum.ERR_MAN_MEM_DELETE_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrManMemDeleteOk(),
  ),
  GlobalPopupRouteEnum.ERR_MAN_MEM_ALL_DELETE_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrManMemAllDeleteOk(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_SELECT_PATTERN_ROTATE90_F.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbSelectPatternRotate90F(),
  ),
  GlobalPopupRouteEnum.ERR_THIS_KEY_CANNOT_USED.name: GlobalPopupBuilder(
    builder: (context) => const ErrThisKeyCannotUsed(),
  ),
  GlobalPopupRouteEnum.ERR_THUNIT_HIT_FRAME.name: GlobalPopupBuilder(
    builder: (context) => const ErrThunitHitFrame(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PROJ_FORCE_QUIT_FRAMEOFF_WAITING.name:
      GlobalPopupBuilder(
    builder: (context) => const DmyErr187(),
  ),
  GlobalPopupRouteEnum.CONNECTING_TO_PC_NOW.name: GlobalPopupBuilder(
    builder: (context) => const ConnectingToPcNow(),
  ),
  GlobalPopupRouteEnum.ERR_NEEDLE_PLATE_EMB_CARRY_INITIAL.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrNeedlePlateEmbCarryInitial(),
  ),
  GlobalPopupRouteEnum.ERR_NEEDLE_PLATE2_EMB_CARRY_INITIAL.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrNeedlePlate2EmbCarryInitial(),
  ),
  GlobalPopupRouteEnum.ERR_ALL_DELETE_BORDER_OK_FOR_POSITIONING.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrAllDeleteBorderOkForPositioning(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_DATA_EXPAND_USB_HOST.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbTooMuchDataExpandUsbHost(),
  ),
  GlobalPopupRouteEnum.ERR_RECOGNIZING_SCAN.name: GlobalPopupBuilder(
    builder: (context) => const ErrRecognizingScan(),
    barrier: false,
  ),
  GlobalPopupRouteEnum.ERR_THIS_PATTERN_CANNOT_CONVERTED.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrThisPatternCannotConverted(),
  ),
  GlobalPopupRouteEnum.ERR_RESET_EMB_SIZE_AND_POSITION.name: GlobalPopupBuilder(
    builder: (context) => const ErrResetEmbSizeAndPosition(),
  ),
  GlobalPopupRouteEnum.ERR_RESET_EMB_SIZE_POSITION.name: GlobalPopupBuilder(
    builder: (context) => const ErrResetEmbSizePosition(),
  ),
  GlobalPopupRouteEnum.ERR_DETECT_FABRIC_THICKNESS.name: GlobalPopupBuilder(
    builder: (context) => const ErrDetectFabricThickness(),
  ),
  GlobalPopupRouteEnum.ERR_DETECTION_SUCCESS.name: GlobalPopupBuilder(
    builder: (context) => const ErrDetectionSuccess(),
  ),
  GlobalPopupRouteEnum.ERR_OK_MOVE_AND_START_CAPTURE.name: GlobalPopupBuilder(
    builder: (context) => const ErrOkMoveAndStartCapture(),
  ),
  GlobalPopupRouteEnum.ERR_DETECTION_FAILURE_AGAIN.name: GlobalPopupBuilder(
    builder: (context) => const ErrDetectionFailureAgain(),
  ),
  GlobalPopupRouteEnum.ERR_CERTIFICATION_SUCCESS.name: GlobalPopupBuilder(
    builder: (context) => const ErrCertificationSuccess(),
  ),
  GlobalPopupRouteEnum.ERR_PRESS_CERTIFICATION_KEY.name: GlobalPopupBuilder(
    builder: (context) => const ErrPressCertificationKey(),
  ),
  GlobalPopupRouteEnum.ERR_CERTIFICATE_UPGRADE_KIT_START.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrCertificateUpgradeKitStart(),
  ),
  GlobalPopupRouteEnum.ERR_ENTER_CERTIFICATION_KEY_AND_PRESS_SET.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEnterCertificationKeyAndPressSet(),
  ),
  GlobalPopupRouteEnum.ERR_NOW_CERTIFICATING.name: GlobalPopupBuilder(
    builder: (context) => const ErrNowCertificating(),
  ),
  GlobalPopupRouteEnum.ERR_CERTIFICATION_KEY_INCORRECT.name: GlobalPopupBuilder(
    builder: (context) => const ErrCertificationKeyIncorrect(),
  ),
  GlobalPopupRouteEnum.ERR_REMOVWE_COMP_FRAME_SCAN_ORG_IMG_DATA.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrRemovweCompFrameScanOrgImgData(),
  ),
  GlobalPopupRouteEnum.ERR_FAILED_TO_SAVE_FILE.name: GlobalPopupBuilder(
    builder: (context) => const ErrFailedToSaveFile(),
  ),
  GlobalPopupRouteEnum.ERR_SEWING_OVER_MARK_PAT_CNCT.name: GlobalPopupBuilder(
    builder: (context) => const ErrSewingOverMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_SET_BEFORE_1ST_MARK_PAT_CNCT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSetBefore1stMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_RECOGNIZING_MARK_PAT_CNCT.name: GlobalPopupBuilder(
    builder: (context) => const ErrRecognizingMarkPatCnct(),
    barrier: false,
  ),
  GlobalPopupRouteEnum.ERR_NO_MARK_BEFORE_MARK_PAT_CNCT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrNoMarkBeforeMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_MISS_MARK_BEFORE_MARK_PAT_CNCT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMissMarkBeforeMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_SET_BEFORE_2ND_MARK_PAT_CNCT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSetBefore2ndMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_SUCCESS_BEFORE_MARK_PAT_CNCT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSuccessBeforeMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_ALL_DELETE_BORDER_OK_MARK_PAT_CNCT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrAllDeleteBorderOkMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_NO_MARK_AFTER_MARK_PAT_CNCT.name: GlobalPopupBuilder(
    builder: (context) => const ErrNoMarkAfterMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_POSITIONING_AREA_OVER_MARK_PAT_CNCT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrPositioningAreaOverMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_NEXT_PATTERN_SEL_MARK_PAT_CNCT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrNextPatternSelMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_REMOVE_MARK_PAT_CNCT.name: GlobalPopupBuilder(
    builder: (context) => const ErrRemoveMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_MARK_RESET_MARK_PAT_CNCT.name: GlobalPopupBuilder(
    builder: (context) => const ErrMarkResetMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_REMOVE_MARK_PAT_CNCT_RESET.name: GlobalPopupBuilder(
    builder: (context) => const ErrRemoveMarkPatCnctReset(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FRAME_MOVE_MARK_PAT_CNCT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbFrameMoveMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_RESET_NEW_1ST_MARK_PAT_CNCT.name: GlobalPopupBuilder(
    builder: (context) => const ErrResetNew1stMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_RESET_NEW_2ND_MARK_PAT_CNCT.name: GlobalPopupBuilder(
    builder: (context) => const ErrResetNew2ndMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_CANCEL_MARK_PAT_CNCT.name: GlobalPopupBuilder(
    builder: (context) => const ErrCancelMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_SUCCESS_RESET_NEW_MARK_PAT_CNCT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSuccessResetNewMarkPatCnct(),
    barrier: false,
  ),
  GlobalPopupRouteEnum.ERR_THIS_PATTERN_TOO_COMPLEX.name: GlobalPopupBuilder(
    builder: (context) => const ErrThisPatternTooComplex(),
  ),
  GlobalPopupRouteEnum.ERR_MCD_CANCEL_B.name: GlobalPopupBuilder(
    builder: (context) => const ErrMcdCancelB(),
  ),
  GlobalPopupRouteEnum.ERR_MCD_HOME_B.name: GlobalPopupBuilder(
    builder: (context) => const ErrMcdHomeB(),
  ),
  GlobalPopupRouteEnum.DMY_ERR_231.name: GlobalPopupBuilder(
    builder: (context) => const UnusedMessage231(),
  ),
  GlobalPopupRouteEnum.DMY_ERR_232.name: GlobalPopupBuilder(
    builder: (context) => const UnusedMessage232(),
  ),
  GlobalPopupRouteEnum.ERR_PATTERN_DELETE_OK_HOST_FOR_SELECT_SCREEN_MCD.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrPatternDeleteOkHostForSelectScreenMcd(),
  ),
  GlobalPopupRouteEnum.ERR_PATTERN_DELETE_OK_HOST_FOR_DELETE_SCREEN_MCD.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrPatternDeleteOkHostForDeleteScreenMcd(),
  ),
  GlobalPopupRouteEnum.ERR_MCD_NEW.name: GlobalPopupBuilder(
    builder: (context) => const ErrMcdNew(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_ALL_DELETE_CUSTOM_PALETTE_OK.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbAllDeleteCustomPaletteOk(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_NO_AVAILABLE_COLOR_FOR_SHUF_IN_CUSTOM.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbNoAvailableColorForShufInCustom(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_NO_AVAILABLE_COLOR_FOR_SHUF_IN_ORG.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbNoAvailableColorForShufInOrg(),
  ),
  GlobalPopupRouteEnum.ERR_USB_MEDIA_MEMORY_FULL_MEMORY_CHANGE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrUsbMediaMemoryFullMemoryChange(),
  ),
  GlobalPopupRouteEnum.ERR_SD_MEDIA_MEMORY_FULL_MEMORY_CHANGE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSdMediaMemoryFullMemoryChange(),
  ),
  GlobalPopupRouteEnum.ERR_CMN_MEDIA_MEMORY_FULL_MEMORY_CHANGE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrCmnMediaMemoryFullMemoryChange(),
  ),
  GlobalPopupRouteEnum.ERR_NO_MARK_RESET_OLD_MARK_PAT_CNCT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrNoMarkResetOldMarkPatCnct(),
  ),
  GlobalPopupRouteEnum.ERR_CAMERA_SET_WHEN_NEEDLE_CHANGE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrCameraSetWhenNeedleChange(),
  ),
  GlobalPopupRouteEnum.ERR_SEWING_OVER_MARK_PAT_CNCT_RESUME.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSewingOverMarkPatCnctResume(),
  ),
  GlobalPopupRouteEnum.ERR_EMBDATA.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbdata(),
  ),
  GlobalPopupRouteEnum.ERR_VIDEO_NOT_USED.name: GlobalPopupBuilder(
    builder: (context) => const ErrVideoNotUsed(),
  ),
  GlobalPopupRouteEnum.ERR_NOT_ENOUGH_AVAILABLE_MEMORY_TO_SAVE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrNotEnoughAvailableMemoryToSave(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_IMPORT_CUSTOM_PATTERN_DELETE_FAILURE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcImportCustomPatternDeleteFailure(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_IMPORT_CUSTOM_PATTERN_ALL_DELETE_OK.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcImportCustomPatternAllDeleteOk(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_IMPORT_CUSTOM_PATTERN_CHANGE_AFTER_SAVING.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcImportCustomPatternChangeAfterSaving(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_IMPORT_CUSTOM_PATTERN_NOT_SAVED_EXT_MEMORY.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcImportCustomPatternNotSavedExtMemory(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_IMPORT_CUSTOM_PATTERN_EMB_DATA_SAVED.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcImportCustomPatternEmbDataSaved(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_IMPORT_CUSTOM_PATTERN_CHOOSE_REPLACE_DATA.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcImportCustomPatternChooseReplaceData(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_IMPORT_CUSTOM_PATTERN_REPLACE_OK.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcImportCustomPatternReplaceOk(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PRJ_CLOSE_CARRY_MOVING.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbPrjCloseCarryMoving(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PRJ_START_NO_CARRY_MOVING.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbPrjStartNoCarryMoving(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_ULTR_SNIC_POS_CHANGE_FRAME.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbUltrSnicPosChangeFrame(),
  ),
  GlobalPopupRouteEnum.ERR_UTL_THIS_PATTERN_NOT_SEW_WHEN_USE_DF.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrUtlThisPatternNotSewWhenUseDf(),
  ),
  GlobalPopupRouteEnum.ERR_UTL_REMOVE_FABRIC_FROM_PF.name: GlobalPopupBuilder(
    builder: (context) => const ErrUtlRemoveFabricFromPf(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PRJ_CLOSE_NO_CARRY_MOVING.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbPrjCloseNoCarryMoving(),
  ),
  GlobalPopupRouteEnum.ERR_SET_RTC.name: GlobalPopupBuilder(
    builder: (context) => const ErrSetRtc(),
  ),
  GlobalPopupRouteEnum.ERR_IIC_COMMUNICATION.name: GlobalPopupBuilder(
    builder: (context) => const ErrIicCommunication(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PROJECTOR_CLOSE_NOFRAME.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbProjectorCloseNoFrame(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PROJECTOR_CLOSE_NOFRAME_EMB_CARRY_MOVING.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbProjectorCloseNoFrameEmbCarryMoving(),
  ),
  GlobalPopupRouteEnum.DMY_ERR_267.name: GlobalPopupBuilder(
    builder: (context) => const DmyErr267(),
  ),
  GlobalPopupRouteEnum.ERR_LEDPT_CONNECT_WHEN_UTL.name: GlobalPopupBuilder(
    builder: (context) => const ErrLedptConnectWhenUtl(),
  ),
  GlobalPopupRouteEnum.ERR_DF_CONNECT.name: GlobalPopupBuilder(
    builder: (context) => const ErrDfConnect(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_RESUME_OK_B.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcResumeOkB(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_RESUME_OK_T.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcResumeOkT(),
  ),
  GlobalPopupRouteEnum.ERR_OSAE_UP_EMB.name: GlobalPopupBuilder(
    builder: (context) => const ErrOsaeUpEmb(),
  ),
  GlobalPopupRouteEnum.ERR_OSAE_DOWN_EMB.name: GlobalPopupBuilder(
    builder: (context) => const ErrOsaeDownEmb(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FRAME_HOLD_LEVER_DOWN_ULTR_SNIC.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbFrameHoldLeverDownUltrSnic(),
  ),
  GlobalPopupRouteEnum.DMY_ERR_275.name: GlobalPopupBuilder(
    builder: (context) => const DmyErr275(),
  ),
  GlobalPopupRouteEnum.ERR_SET_SETTINGS_DEFAULT.name: GlobalPopupBuilder(
    builder: (context) => const ErrSetSettingsDefault(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_UNIT_NOT_USE.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbUnitNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_MCD_FINISH_CHECK_B.name: GlobalPopupBuilder(
    builder: (context) => const ErrMcdFinishCheckB(),
  ),
  GlobalPopupRouteEnum.ERR_MYI_DECO_CANCEL_PTN_OK.name: GlobalPopupBuilder(
    builder: (context) => const ErrMyiDecoCancelPtnOk(),
  ),
  GlobalPopupRouteEnum.ERR_MYI_DECO_SELPTN_AREA_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrMyiDecoSelptnAreaOver(),
  ),
  GlobalPopupRouteEnum.ERR_SCAN_FRAME_NOT_USE.name: GlobalPopupBuilder(
    builder: (context) => const ErrScanFrameNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_IMG_PIXEL_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrImgPixelOver(),
  ),
  GlobalPopupRouteEnum.ERR_MYI_CANNOT_INPUT.name: GlobalPopupBuilder(
    builder: (context) => const ErrMyiCannotInput(),
  ),
  GlobalPopupRouteEnum.ERR_ATTATCH_SCAN_FRAME.name: GlobalPopupBuilder(
    builder: (context) => const ErrAttatchScanFrame(),
  ),
  GlobalPopupRouteEnum.ERR_MCD_SCAN_IMG_TRIMMING.name: GlobalPopupBuilder(
    builder: (context) => const ErrMcdScanImgTrimming(),
  ),
  GlobalPopupRouteEnum.ERR_NO_USE_FUNCTION_CNCT.name: GlobalPopupBuilder(
    builder: (context) => const ErrNoUseFunctionCnct(),
  ),
  GlobalPopupRouteEnum.ERR_MCD_NOT_EXCHANGE_AREA_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrMcdNotExchangeAreaOver(),
  ),
  GlobalPopupRouteEnum.ERR_MAIN_BOARD_POWER_OFF.name: GlobalPopupBuilder(
    builder: (context) => const ErrMainBoardPowerOff(),
  ),
  GlobalPopupRouteEnum.ERR_MAIN_BOARD_POWER_OFF_NMI.name: GlobalPopupBuilder(
    builder: (context) => const ErrMainBoardPowerOffNmi(),
  ),
  GlobalPopupRouteEnum.ERR_SELECTED_PATTERN_DELETE_OK_POPUP.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSelectedPatternDeleteOkPopup(),
  ),
  GlobalPopupRouteEnum.ERR_OUTSIDE_OF_EMB_FRM_NOADD.name: GlobalPopupBuilder(
    builder: (context) => const ErrOutsideOfEmbFrmNoadd(),
  ),
  GlobalPopupRouteEnum.ERR_OUTSIDE_OF_EMB_FRM_NOUSE.name: GlobalPopupBuilder(
    builder: (context) => const ErrOutsideOfEmbFrmNouse(),
  ),
  GlobalPopupRouteEnum.ERR_NO_CHG_FONT.name: GlobalPopupBuilder(
    builder: (context) => const ErrNoChgFont(),
  ),
  GlobalPopupRouteEnum.ERR_MCD_CANCEL_T.name: GlobalPopupBuilder(
    builder: (context) => const ErrMcdCancelT(),
  ),
  GlobalPopupRouteEnum.ERR_MCD_HOME_T.name: GlobalPopupBuilder(
    builder: (context) => const ErrMcdHomeT(),
  ),
  GlobalPopupRouteEnum.ERR_MCD_FINISH_CHECK_T.name: GlobalPopupBuilder(
    builder: (context) => const ErrMcdFinishCheckT(),
  ),
  GlobalPopupRouteEnum.ERR_APPLIQUE_NG_EX_IN_OVERLAP.name: GlobalPopupBuilder(
    builder: (context) => const ErrAppliqueNgExInOverlap(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_CREATE_NEW.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcCreateNew(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_CANCEL_B.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcCancelB(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_CANCEL_T.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcCancelT(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_HOME_B.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcHomeB(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_HOME_T.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcHomeT(),
  ),
  GlobalPopupRouteEnum.UNUSED_MESSAGE_303.name: GlobalPopupBuilder(
    builder: (context) => const UnusedMessage303(),
  ),
  GlobalPopupRouteEnum.UNUSED_MESSAGE_304.name: GlobalPopupBuilder(
    builder: (context) => const UnusedMessage304(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_FINISH_CHECK_B.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcFinishCheckB(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_FINISH_CHECK_T.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcFinishCheckT(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_NOT_EXCHANGE_AREA_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcNotExchangeAreaOver(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_TOO_MUCH_SELECTED_GO_MENU.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcTooMuchSelectedGoMenu(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_TOO_ALLOC_FAILURE.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcTooAllocFailure(),
  ),
  GlobalPopupRouteEnum.UNUSED_MESSAGE_310.name: GlobalPopupBuilder(
    builder: (context) => const UnusedMessage310(),
  ),
  GlobalPopupRouteEnum.ERR_COL_SORT_NG.name: GlobalPopupBuilder(
    builder: (context) => const ErrColSortNg(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_BW_SEWING_FINISH.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbBwSewingFinish(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_CARRY_MOVING_FRAME_MOVE_BW.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbCarryMovingFrameMoveBw(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FLAME_RETURN_OK_BW.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbFlameReturnOkBw(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FRAME_SET_BW.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbFrameSetBw(),
  ),
  GlobalPopupRouteEnum.ERR_THIS_FRAME_NOT_USE_BW.name: GlobalPopupBuilder(
    builder: (context) => const ErrThisFrameNotUseBw(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_BW_SEWING_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbBwSewingOver(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_BW_THIS_PATTERN_NOT_USE.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbBwThisPatternNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_APPLIQUE_NG_DISTANCE.name: GlobalPopupBuilder(
    builder: (context) => const ErrAppliqueNgDistance(),
  ),
  GlobalPopupRouteEnum.ERR_APPLIQUE_NG_SIZE_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrAppliqueNgSizeOver(),
  ),
  GlobalPopupRouteEnum.ERR_APPLIQUE_NG_COMPLEX.name: GlobalPopupBuilder(
    builder: (context) => const ErrAppliqueNgComplex(),
  ),
  GlobalPopupRouteEnum.ERR_APPLIQUE_NG_MEM_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrAppliqueNgMemOver(),
  ),
  GlobalPopupRouteEnum.ERR_SELECTED_DATA_DELETE_OK_POPUP.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSelectedDataDeleteOkPopup(),
  ),
  GlobalPopupRouteEnum.ERR_DELETE_BORDER_MARK_OK_GROUP.name: GlobalPopupBuilder(
    builder: (context) => const ErrDeleteBorderMarkOkGroup(),
  ),
  GlobalPopupRouteEnum.ERR_DELETE_BORDER_MARK_OK_UNGROUP.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrDeleteBorderMarkOkUnGroup(),
  ),
  GlobalPopupRouteEnum.ERR_DELETE_BORDER_MARK_OK_WAPPEN.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrDeleteBorderMarkOkWappen(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_HOME_CLEAR_ALL_EDITING_DATA_AND_MOVE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcHomeClearAllEditingDataAndMove(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_BACKGROUND_DELETE.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcBackgroundDelete(),
  ),
  GlobalPopupRouteEnum.ERR_DELETE_BORDER_MARK_OK_EASYSTTIPLE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrDeleteBorderMarkOkEasysttiple(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_NOT_CHANGE_SETTING_MEMORY_OVER.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcNotChangeSettingMemoryOver(),
  ),
  GlobalPopupRouteEnum.ERR_THIS_PATTERN_TOO_COMPLEX_FOR_ILLUST_SCAN.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrThisPatternTooComplexForIllustScan(),
  ),
  GlobalPopupRouteEnum.ERR_CAMERA_BH_NG.name: GlobalPopupBuilder(
    builder: (context) => const ErrCameraBhNg(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_STAMP_MAKE.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbStampMake(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_STAMP_MAKE_TC.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbStampMakeTc(),
  ),
  GlobalPopupRouteEnum.ERR_XY_ZPHASE.name: GlobalPopupBuilder(
    builder: (context) => const ErrXyZphase(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_CONTINUE.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashesContinue(),
  ),
  GlobalPopupRouteEnum.ERR_WAIT_COMPLETE_CGI.name: GlobalPopupBuilder(
    builder: (context) => const ErrWaitCompleteCgi(),
  ),
  GlobalPopupRouteEnum.ERR_MYI_DELETE_OK_FOR_SAVEDFILE_SELECT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMyiDeleteOkForSavedfileSelect(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_THIS_PATTERN_NOT_USE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltThisPatternNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_1ST_SETTING.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashes1stSetting(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_CORNER_SETTING.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashesCornerSetting(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_1ST_CORNER_OF_MOVE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashes1stCornerOfMove(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_1ST_CORNER_OF_ROTATE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashes1stCornerOfRotate(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_STRAIGHT_OF_MOVE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashesStraightOfMove(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_MOVE_TO_ROTATE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashesMoveToRotate(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_BEFORE_CORNER_OF_ROTATE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashesBeforeCornerOfRotate(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_CORNER_OF_MOVE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashesCornerOfMove(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_CORNER_OF_ROTATE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashesCornerOfRotate(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_LAST_OF_ROTATE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashesLastOfRotate(),
  ),
  GlobalPopupRouteEnum.ERR_SCAN_SHADING_UPDATE.name: GlobalPopupBuilder(
    builder: (context) => const ErrScanShadingUpdate(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_REPOSITION_SETTING.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashesRepositionSetting(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_QUILT_SASHES_1ST_SETTING2.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbQuiltSashes1stSetting2(),
  ),
  GlobalPopupRouteEnum.ERR_LEDPT_CONNECT_WHEN_COUTING.name: GlobalPopupBuilder(
    builder: (context) => const ErrLedptConnectWhenCouting(),
  ),
  GlobalPopupRouteEnum.ERR_USB_DEVICE_CANNOT_USED.name: GlobalPopupBuilder(
    builder: (context) => const ErrUsbDeviceCannotUsed(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_COUTING_THIS_PATTERN_NOT_USE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbCoutingThisPatternNotUse(),
  ),
  GlobalPopupRouteEnum.ERR_BAD_EMBROIDERY_DATA.name: GlobalPopupBuilder(
    builder: (context) => const ErrBadEmbroideryData(),
  ),
  GlobalPopupRouteEnum.ERR_SCAN_SHADING_UPDATE_SCAN.name: GlobalPopupBuilder(
    builder: (context) => const ErrScanShadingUpdateScan(),
  ),
  GlobalPopupRouteEnum.ERR_UPGRADE_FINISH_B.name: GlobalPopupBuilder(
    builder: (context) => const ErrUpgradeFinishB(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FLAME_RETURN_OK_BOBIN_CHANGE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbFlameReturnOkBobinChange(),
  ),
  GlobalPopupRouteEnum.ERR_EULA_CONFIRMATION.name: GlobalPopupBuilder(
    builder: (context) => const ErrEulaConfirmation(),
  ),
  GlobalPopupRouteEnum.ERR_PLEASE_WAIT_NETDIAG.name: GlobalPopupBuilder(
    builder: (context) => const ErrPleaseWaitNetdiag(),
  ),
  GlobalPopupRouteEnum.ERR_MANUAL_MEMORY_CORRECTION.name: GlobalPopupBuilder(
    builder: (context) => const ErrManualMemoryCorrection(),
  ),
  GlobalPopupRouteEnum.ERR_EPS_FINISH.name: GlobalPopupBuilder(
    builder: (context) => const ErrEpsFinish(),
  ),
  GlobalPopupRouteEnum.ERR_EPS_MARK_TOO_CLOSE.name: GlobalPopupBuilder(
    builder: (context) => const ErrEpsMarkTooClose(),
  ),
  GlobalPopupRouteEnum.ERR_EPS_STOP_MSG.name: GlobalPopupBuilder(
    builder: (context) => const ErrEpsStopMsg(),
  ),
  GlobalPopupRouteEnum.ERR_EPS_CORRECT_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrEpsCorrectOver(),
  ),
  GlobalPopupRouteEnum.ERR_EPS_FINISH_PROJECTOR.name: GlobalPopupBuilder(
    builder: (context) => const ErrEpsFinishProjector(),
  ),
  GlobalPopupRouteEnum.ERR_EPS_CORRECT_OVER_SSKEY.name: GlobalPopupBuilder(
    builder: (context) => const ErrEpsCorrectOverSskey(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FRAME_HOLD_LEVER_DOWN_MATRIX.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbFrameHoldLeverDownMatrix(),
  ),
  GlobalPopupRouteEnum.ERR_APPLIQUE_SOME_PARTS_NOT_TEXTURE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrAppliqueSomePartsNotTexture(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_CHANGE_ORDER.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbChangeOrder(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_BG_FILE_DELETE.name: GlobalPopupBuilder(
    builder: (context) => const ErrMdcBgFileDelete(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_USB_MEDIA_CANNOT_DELETE_BY_PROTECTED.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcUsbMediaCannotDeleteByProtected(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_SD_MEDIA_CANNOT_DELETE_BY_PROTECTED.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcSdMediaCannotDeleteByProtected(),
  ),
  GlobalPopupRouteEnum.ERR_MDC_CMN_MEDIA_CANNOT_DELETE_BY_PROTECTED.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrMdcCmnMediaCannotDeleteByProtected(),
  ),
  GlobalPopupRouteEnum.ERR_CHECK_TWIN_NEEDLE_REMOVED.name: GlobalPopupBuilder(
    builder: (context) => const ErrCheckTwinNeedleRemoved(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_CHANGE_TO_SCAN_FRAME.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbChangeToScanFrame(),
  ),
  GlobalPopupRouteEnum.ERR_EPS_LENGTH_SSKEY.name: GlobalPopupBuilder(
    builder: (context) => const ErrEpsLengthSskey(),
  ),
  GlobalPopupRouteEnum.ERR_EPS_DISABLE_KEY.name: GlobalPopupBuilder(
    builder: (context) => const ErrEpsDisableKey(),
  ),
  GlobalPopupRouteEnum.ERR_NET_DISABLE_WLAN.name: GlobalPopupBuilder(
    builder: (context) => const ErrNetDisableWlan(),
  ),
  GlobalPopupRouteEnum.ERR_NET_MAINTENANCE.name: GlobalPopupBuilder(
    builder: (context) => const ErrNetMaintenance(),
  ),
  GlobalPopupRouteEnum.ERR_REGIST_PINCODE_FAILURE.name: GlobalPopupBuilder(
    builder: (context) => const ErrRegistPincodeFailure(),
  ),
  GlobalPopupRouteEnum.ERR_REGIST_PINCODE_WRONG.name: GlobalPopupBuilder(
    builder: (context) => const ErrRegistPincodeWrong(),
  ),
  GlobalPopupRouteEnum.ERR_NET_NOT_CONNECT.name: GlobalPopupBuilder(
    builder: (context) => const ErrNetNotConnect(),
  ),
  GlobalPopupRouteEnum.ERR_NET_PROXY_WRONG.name: GlobalPopupBuilder(
    builder: (context) => const ErrNetProxyWrong(),
  ),
  GlobalPopupRouteEnum.ERR_NET_TIMEOUT.name: GlobalPopupBuilder(
    builder: (context) => const ErrNetTimeout(),
  ),
  GlobalPopupRouteEnum.ERR_NET_ACCESSTOKEN_WRONG.name: GlobalPopupBuilder(
    builder: (context) => const ErrNetAccesstokenWrong(),
  ),
  GlobalPopupRouteEnum.ERR_UPLOAD_FAILURE.name: GlobalPopupBuilder(
    builder: (context) => const ErrUploadFailure(),
  ),
  GlobalPopupRouteEnum.ERR_UPLOAD_CONFIRM.name: GlobalPopupBuilder(
    builder: (context) => const ErrUploadConfirm(),
  ),
  GlobalPopupRouteEnum.ERR_UPLOAD_OVERRIDE_CONFIRM.name: GlobalPopupBuilder(
    builder: (context) => const ErrUploadOverrideConfirm(),
  ),
  GlobalPopupRouteEnum.ERR_DOWNLOAD_FAILURE.name: GlobalPopupBuilder(
    builder: (context) => const ErrDownloadFailure(),
  ),
  GlobalPopupRouteEnum.ERR_DOWNLOAD_SIZE_CHANGE.name: GlobalPopupBuilder(
    builder: (context) => const ErrDownloadSizeChange(),
  ),
  GlobalPopupRouteEnum.ERR_DOWNLOAD_UNREADABLE_DATA.name: GlobalPopupBuilder(
    builder: (context) => const ErrDownloadUnreadableData(),
  ),
  GlobalPopupRouteEnum.ERR_UNREGIST_PINCODE.name: GlobalPopupBuilder(
    builder: (context) => const ErrUnregistPincode(),
  ),
  GlobalPopupRouteEnum.ERR_UNREGIST_PINCODE_FAILURE.name: GlobalPopupBuilder(
    builder: (context) => const ErrUnregistPincodeFailure(),
  ),
  GlobalPopupRouteEnum.ERR_DOWNLOAD_DISABLE_PINCODE.name: GlobalPopupBuilder(
    builder: (context) => const ErrDownloadDisablePincode(),
  ),
  GlobalPopupRouteEnum.ERR_UPLOAD_DISABLE_PINCODE.name: GlobalPopupBuilder(
    builder: (context) => const ErrUploadDisablePincode(),
  ),
  GlobalPopupRouteEnum.ERR_NET_SERVER_FAILURE.name: GlobalPopupBuilder(
    builder: (context) => const ErrNetServerFailure(),
  ),
  GlobalPopupRouteEnum.ERR_CANNOT_USE_SPECIAL_PATTERN.name: GlobalPopupBuilder(
    builder: (context) => const ErrCannotUseSpecialPattern(),
  ),
  GlobalPopupRouteEnum.ERR_MARK_DELETE_OK_FOR_NUWANAI.name: GlobalPopupBuilder(
    builder: (context) => const ErrMarkDeleteOkForNuwanai(),
  ),
  GlobalPopupRouteEnum.ERR_RECOMEND_TOMENUI.name: GlobalPopupBuilder(
    builder: (context) => const ErrRecomendTomenui(),
  ),
  GlobalPopupRouteEnum.ERR_WLAN_SETUP_CONFIRMATION.name: GlobalPopupBuilder(
    builder: (context) => const ErrWlanSetupConfirmation(),
  ),
  GlobalPopupRouteEnum.ERR_CONFIRM_CANCEL_AUTOCNCT.name: GlobalPopupBuilder(
    builder: (context) => const ErrConfirmCancelAutocnct(),
  ),
  GlobalPopupRouteEnum.ERR_SEWING_OVER_INTERNAL_LARGECONNECT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSewingOverInternalLargeconnect(),
  ),
  GlobalPopupRouteEnum.ERR_SEWING_OVER_INTERNAL_LARGECONNECT_SOUND.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrSewingOverInternalLargeconnectSound(),
  ),
  GlobalPopupRouteEnum.ERR_RECOMMEND_UPDATE.name: GlobalPopupBuilder(
    builder: (context) => const ErrRecommendUpdate(),
  ),
  GlobalPopupRouteEnum.ERR_VERUP_FOR_INSTALL_MOVIE.name: GlobalPopupBuilder(
    builder: (context) => const ErrVerupForInstallMovie(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_1.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide1(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_2.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide2(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_3.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide3(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_4.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide4(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_5.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide5(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_6.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide6(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_7.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide7(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_8.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide8(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_10.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide10(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_11.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide11(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_12.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide12(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_14.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide14(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_15.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide15(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_16.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide16(),
  ),
  GlobalPopupRouteEnum.ERR_REMOVE_MARK_PAT_CNCT_PATTERN_MAG.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrRemoveMarkPatCnctPatternMag(),
  ),
  GlobalPopupRouteEnum.ERR_AFTER_MARK_PAT_CNCT_MARK_DIV.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrAfterMarkPatCnctMarkDiv(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_FINISH_PROJECTOR.name: GlobalPopupBuilder(
    builder: (context) => const ErrTaperingFinishProjector(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_FINISH.name: GlobalPopupBuilder(
    builder: (context) => const ErrTaperingFinish(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_MARK_TOO_CLOSE.name: GlobalPopupBuilder(
    builder: (context) => const ErrTaperingMarkTooClose(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH_CUETOP.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTaperingCurrentFinishCuetop(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH_FLIP_ON.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTaperingCurrentFinishFlipOn(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH_FLIP_OFF.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTaperingCurrentFinishFlipOff(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH_RETRIEVE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTaperingCurrentFinishRetrieve(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH_BUTTON.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTaperingCurrentFinishButton(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH_CYCLENUM.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTaperingCurrentFinishCyclenum(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH_ENDPOINT.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTaperingCurrentFinishEndpoint(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH_STARTANGLE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTaperingCurrentFinishStartangle(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH_ENDANGLE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTaperingCurrentFinishEndangle(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH_INCREASENUM.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTaperingCurrentFinishIncreasenum(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH_DECREASENUM.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrTaperingCurrentFinishDecreasenum(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH.name: GlobalPopupBuilder(
    builder: (context) => const ErrTaperingCurrentFinish(),
  ),
  GlobalPopupRouteEnum.ERR_TAPERING_DISABLE_KEY.name: GlobalPopupBuilder(
    builder: (context) => const ErrTaperingDisableKey(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_9.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide9(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_13.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide13(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_EDGE_QUILT_SASHES_GUIDE_17.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbEdgeQuiltSashesGuide17(),
  ),
  GlobalPopupRouteEnum.ERR_DATA_MOMORY_FULL_DELETE_OTHER_DATA.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrDataMomoryFullDeleteOtherData(),
  ),
  GlobalPopupRouteEnum.ERR_MACHINENAME_ENABLE_WLAN.name: GlobalPopupBuilder(
    builder: (context) => const ErrMachineNameEnableWlan(),
  ),
  GlobalPopupRouteEnum.ERR_REGIST_MACHINE_NAME_FAILURE.name: GlobalPopupBuilder(
    builder: (context) => const ErrRegistMachineNameFailure(),
  ),
  GlobalPopupRouteEnum.ERR_PATTERN_CANNOT_SAVE.name: GlobalPopupBuilder(
    builder: (context) => const ErrPatternCannotSave(),
  ),
  GlobalPopupRouteEnum.ERR_DOWNLOAD_PAIDCONT_UPDATE.name: GlobalPopupBuilder(
    builder: (context) => const ErrDownloadPaidContUpdate(),
  ),
  GlobalPopupRouteEnum.cameraPopup.name: GlobalPopupBuilder(
    builder: (context) => const CameraPopup(),
    barrier: false,
  ),
  GlobalPopupRouteEnum.screenShotSavePopup.name: GlobalPopupBuilder(
    builder: (context) => const ScreenShotSavePopup(),
    barrier: false,
  ),
  GlobalPopupRouteEnum.layoutCamera.name: GlobalPopupBuilder(
    builder: (context) => const LayoutCamera(),
    barrier: false,
  ),
  GlobalPopupRouteEnum.stitch_camera.name: GlobalPopupBuilder(
    builder: (context) => const StitchCameraPopup(),
    barrier: false,
  ),
  GlobalPopupRouteEnum.ERR_FONT_CNG_NG_FORMAT.name: GlobalPopupBuilder(
    builder: (context) => const ErrDummy(),
  ),
  GlobalPopupRouteEnum.errCheckIsNeedDownloadSound.name: GlobalPopupBuilder(
    builder: (context) => const ErrCheckIsNeedDownloadSound(),
  ),
  GlobalPopupRouteEnum.errNotConnectToInternet.name: GlobalPopupBuilder(
    builder: (context) => const ErrNotConnectToInternet(),
  ),
  GlobalPopupRouteEnum.errProcessingComplete.name: GlobalPopupBuilder(
    builder: (context) => const ErrProcessingComplete(),
  ),
  GlobalPopupRouteEnum.errProcessingInterruptions.name: GlobalPopupBuilder(
    builder: (context) => const ErrProcessingInterruptions(),
  ),
  GlobalPopupRouteEnum.errProcessingFailed.name: GlobalPopupBuilder(
    builder: (context) => const ErrProcessingFailed(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PRJ_START_NO_CAMERAUI_CARRY_MOVING.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbPrjStartNoCameraUiCarryMoving(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PRJ_START_NO_CAMERAUI.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbPrjStartNoCameraUi(),
  ),
  GlobalPopupRouteEnum.errDownloadMessageSoundPopup.name: GlobalPopupBuilder(
    builder: (context) => const ErrDownloadMessageSoundPopup(),
  ),
  GlobalPopupRouteEnum.errUpdateFirmFirst.name: GlobalPopupBuilder(
    builder: (context) => const ErrErrUpdateFarmFirst(),
  ),
  GlobalPopupRouteEnum.ERR_SEWING_PATTERN_OVER.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbSewingThreadChange(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PRJ_START_CARRY_MOVING.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbPrjStartCarryMoving(),
  ),
  GlobalPopupRouteEnum.errSnowmanDeleteBorderOk.name: GlobalPopupBuilder(
    builder: (context) => const ErrSnowmanDeleteBorderOk(),
  ),
  GlobalPopupRouteEnum.errSnowmanConfirm.name: GlobalPopupBuilder(
    builder: (context) => const ErrSnowmanConfirm(),
  ),
  GlobalPopupRouteEnum.errConvertEmbMoveToEdit.name: GlobalPopupBuilder(
    builder: (context) => const ErrConvertEmbMoveToEdit(),
  ),
  GlobalPopupRouteEnum.errMoveMask.name: GlobalPopupBuilder(
    builder: (context) => const ErrMoveMask(),
  ),
  GlobalPopupRouteEnum.errDeleteArtspira.name: GlobalPopupBuilder(
    builder: (context) => const ErrDeleteArtspira(),
  ),
  GlobalPopupRouteEnum.errHomeVoiceGuidance.name: GlobalPopupBuilder(
    builder: (context) => const ErrHomeVoiceGuidance(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PRJ_START_PLEASE_WAIT.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbPrjStartPleaseWait(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_PRJ_CLOSE_PLEASE_WAIT.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbPrjClosePleaseWait(),
  ),
  GlobalPopupRouteEnum.errImgPixelAndSpaceOver.name: GlobalPopupBuilder(
    builder: (context) => const ErrImgPixelAndSpaceOver(),
  ),
  GlobalPopupRouteEnum.errPhotoStitchExit.name: GlobalPopupBuilder(
    builder: (context) => const ErrPhotoStitchExit(),
  ),
  GlobalPopupRouteEnum.errFilterCleared.name: GlobalPopupBuilder(
    builder: (context) => const ErrFilterCleared(),
  ),
  GlobalPopupRouteEnum.errPF1FontChangeOK.name: GlobalPopupBuilder(
    builder: (context) => const ErrPF1FontChangeOK(),
  ),
  GlobalPopupRouteEnum.errPF1FontChangeNG.name: GlobalPopupBuilder(
    builder: (context) => const ErrPF1FontChangeNG(),
  ),
  GlobalPopupRouteEnum.errUpdateWlanOff.name: GlobalPopupBuilder(
    builder: (context) => const ErrUpdateWlanOff(),
  ),
  GlobalPopupRouteEnum.errorReadPM9oldVersion.toString(): GlobalPopupBuilder(
    builder: (context) => const ErrReadPM9oldVersion(),
  ),
  GlobalPopupRouteEnum.ERR_DF_CONNECT_NOCLOSE.name: GlobalPopupBuilder(
    builder: (context) => const ErrDFConnectNoClose(),
  ),
  GlobalPopupRouteEnum.ERR_REMOVE_SR_NOCLOSE.toString(): GlobalPopupBuilder(
    builder: (context) => const ErrRemoveSrNoClose(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_CARRY_MOVING_FRAME_REMOVE.name:
      GlobalPopupBuilder(
    builder: (context) => const ErrEmbCarryMovingFrameRemove(),
  ),
  GlobalPopupRouteEnum.ERR_EMB_FRAME_REMOVE_RERUEN.name: GlobalPopupBuilder(
    builder: (context) => const ErrEmbFrameRemoveReruen(),
  ),
  GlobalPopupRouteEnum.errorImageTooLarge.name: GlobalPopupBuilder(
    builder: (context) => const ErrImageTooLarge(),
  ),
  GlobalPopupRouteEnum.errorLibExceptionMemoryFull.name: GlobalPopupBuilder(
    builder: (context) => const ErrLibSaveMemoryFull(),
  ),
  GlobalPopupRouteEnum.picturePlayPleaseWait.name: GlobalPopupBuilder(
    builder: (context) => const PicturePlayPleaseWait(),
  ),
  GlobalPopupRouteEnum.picturePlayPleaseWaitWithCancel.name: GlobalPopupBuilder(
    builder: (context) => const PicturePlayPleaseWaitWithCancel(),
  ),
};
