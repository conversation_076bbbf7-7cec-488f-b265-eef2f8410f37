import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import '../../../library_manage_meta/library_manage_meta.dart';
import '../../../library_manage_meta/tpd_library_result.dart';
import 'type_define.dart';

abstract class TpdLibraryAPIInterface with TpdLibraryAPIExtension {
  ///
  /// bpIF初期化する
  ///
  /// 全てのFFI API呼び出す前に、一度[bpIFInit]関数を呼び出す必要があります。
  ///
  /// AllData初期化とEEPRom初期化が実行する
  ///
  /// 戻り値   :
  /// - [BpIfInitError] : BpIf初期化状態
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.doUiImp,
    "返されたエラーに対応する必要があります。",
  )
  BpIfInitError bpIFInit();

  ///
  /// bpIF初期化する
  ///
  /// 戻り値   :
  /// - [BpIfInitError] : BpIf初期化状態
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.libMaintenance,
    "",
  )
  BpIfInitError bpIFInit1st();

  ///
  /// bpIF初期化する
  ///
  /// 戻り値   :
  /// - [BpIfInitError] : BpIf初期化状態
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.libMaintenance,
    "",
  )
  BpIfInitError bpIFInit2nd();

  ///
  /// bpIFに起動モードを設定
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.libMaintenance,
    "",
  )
  void bpIFSetTestMode(int mode);

  ///
  /// デバイスエラーを取得する
  ///
  /// コールバックは今では実現できないので　暫定はこの関数をポーリングしてください
  ///
  @TpdApiResult(TpdApiFunction.bpIFGetError, ApiStatus.ok, "")
  BPIFError bpIFGetError();

  ///
  /// 様々な事件をLIBに 通知する
  ///
  /// 例えば エラー解消、テストモードの画面遷移、OUTKEY、刺繡機移動　etc
  /// 詳細は [BPIFSendKey]に参考してください
  ///
  @TpdApiResult(
    TpdApiFunction.bpIFSendDisplayData,
    ApiStatus.doUiImp,
    "BPIFSendKey変更が多いです、UI側必要なら、対してお願いいたします",
  )
  int bpIFSendDisplayData(BPIFSendKey messageId);

  ///
  /// 様々な事件をLIBに 通知する
  ///
  /// 例えば エラー解消、テストモードの画面遷移、OUTKEY、刺繡機移動　etc
  /// 詳細は [BPIFSendKey]に参考してください
  ///
  @TpdApiResult(
    TpdApiFunction.bpIFSendDisplayDataSync,
    ApiStatus.doUiImp,
    "BPIFSendKey変更が多いです、UI側必要なら、対してお願いいたします",
  )
  int bpIFSendDisplayDataSync(BPIFSendKey messageId);

  ///
  /// SR機能 Beep音提示
  /// 布の移動速度が速すぎて検出できない場合は、警告音を再生する必要です。
  /// その時に どの音を再生するか　この関数で通知します
  ///  - 戻る
  /// 詳細は [BeepRequest]に参考してください
  ///
  @TpdApiResult(TpdApiFunction.bpIFGetBeep, ApiStatus.ok, "")
  BeepRequest bpIFGetBeep();

  ///
  ///
  ///
  @TpdApiResult(TpdApiFunction.appDisplayData, ApiStatus.doUiImp, "")
  BPIFUtlAppDisplayInfoPublicDef bpIFGetAppDisplayUtl();

  ///
  /// 表示用の状態を取得します
  /// - brother_panelの状態(元々ＸＰのbasemode)
  /// - 刺繍機接続状態
  /// - 糸巻状態
  /// - 刺繍機停止中
  /// - etc
  ///
  ///
  @TpdApiResult(
    TpdApiFunction.appDisplayData,
    ApiStatus.doUiImp,
    "パラメータBPIFGlobalAppInfoに変更がある、API は問題ない、UI側必要なら、対してお願いいたします",
  )
  BPIFGlobalAppInfo bpIFGetAppDisplayGlobal();

  ///
  /// 「EMB」ページに関する情報の取得
  ///
  @TpdApiResult(
    TpdApiFunction.appDisplayData,
    ApiStatus.ngLib,
    """
    最初の呼び出しでは、縫製時間と総ステッチ数は最初の糸の色の値のみを示し、2番目の呼び出しは正しいです
    """,
  )
  BPIFEmbAppDisplayInfoPublicDef bPIFGetAppDisplayEmb();

  ///
  /// 起動状態を取得する
  ///
  /// 引数[in]:
  /// - ない
  ///
  /// 戻り値   :
  /// - [InitState] : 起動状態
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.ok,
    "",
  )
  InitState getInitState();

  ///
  /// 共通キー
  ///

  ///
  /// ホーム画面から遷移する際に確認する
  ///
  /// 戻る値
  /// - [DirErrorCode.dirInvalidError]
  /// 無効音
  /// - [DirErrorCode.dirInvalidPanelError]
  ///   - ERR_CHANGE_NEEDLE_BOARD2_NOCLOSE
  ///   - ERR_CHANGE_NEEDLE_BOARD
  ///   - EmbCarriageMovingErrorCheck
  ///   - ERR_LEDPT_CONNECT_WHEN_UTL
  ///   - ERR_DF_CONNECT
  ///   - ERR_REMOVE_SR
  ///   - ERR_NEEDLE_UP
  ///   - FrameErrorCodeGet
  ///   - ERR_TWIN_NEEDLE_NOT_SELECT_PATTERN
  /// - [DirErrorCode.dirRequiresConfirmation]
  ///   - ERR_MDC_HOME_CLEAR_ALL_EDITING_DATA_AND_MOVE
  ///   - ERR_MDC_HOME_B
  ///   - ERR_MDC_HOME_T
  ///   - ERR_SELECTED_PATTERN_CANCEL_OK
  ///   - ERR_SELECTED_STITCH_CANCEL_OK
  /// - [DirErrorCode.dirTransitionOK]
  ///
  @TpdApiResult(
    TpdApiFunction.homeKey,
    ApiStatus.ok,
    "",
  )
  DirErrorCode checkGotoHome();

  ///
  /// Home画面へ遷移する
  ///
  /// 戻り値   :
  /// - [DeviceErrorCode] : エラーコード
  ///
  DeviceErrorCode gotoHome();

  ///
  /// マイイラストに遷移できるか？
  ///
  @TpdApiResult(
    TpdApiFunction.myIlKey,
    ApiStatus.doLibImpUnit,
    "",
  )
  DirErrorCode checkGotoMyIl();

  ///
  /// MDCの状態取得
  ///
  @TpdApiResult(
    TpdApiFunction.bpIFGetAppDisplay,
    ApiStatus.libMaintenance,
    "",
  )
  BPIFMDCAppDisplayInfoPublicDef_t bPIFGetAppDisplayMdc();

  ///
  /// teaching画面から遷移する
  ///
  @TpdApiResult(
    TpdApiFunction.outKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode gotoHowToUse();

  ///
  /// teaching画面から戻る
  ///
  @TpdApiResult(
    TpdApiFunction.outKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode returnHowToUse();

  ///
  /// Setting画面から遷移する
  ///
  /// 戻る値
  /// - [DeviceErrorCode.devInvalidError ]
  /// - [DeviceErrorCode.devInvalidPanelError]
  ///   - ERR_EPS_FINISH (auto stopのときに)
  ///   - ERR_TAPERING_FINISH (Tapering onのときに)
  /// - [DeviceErrorCode.devNoError]
  ///
  @TpdApiResult(
    TpdApiFunction.outKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode gotoSettings();

  ///
  /// Home画面から遷移する
  ///
  @TpdApiResult(
    TpdApiFunction.outKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode gotoWlanSetting();

  ///
  /// Home画面から遷移する
  ///
  /// 戻る値
  /// - [DeviceErrorCode.devInvalidError ]
  /// - [DeviceErrorCode.devInvalidPanelError]
  ///   - ERR_EPS_FINISH (auto stopのときに)
  ///   - ERR_TAPERING_FINISH (Tapering onのときに)
  ///
  @TpdApiResult(
    TpdApiFunction.outKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode gotoClockSetting();

  @TpdApiResult(
    TpdApiFunction.outKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode returnClockSetting();

  ///
  /// DeepSleep
  ///
  @TpdApiResult(
    TpdApiFunction.outKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode startSleep();

  ///
  /// DeepSleep
  ///
  @TpdApiResult(
    TpdApiFunction.ecoSleep,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode stopSleep();

  ///
  /// Screren Server
  ///
  @TpdApiResult(
    TpdApiFunction.ecoSleep,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode startScreenSaver();

  ///
  /// Screren Server
  ///
  @TpdApiResult(
    TpdApiFunction.ecoSleep,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode stopScreenSaver();

  ///
  /// Ecomode
  ///
  @TpdApiResult(
    TpdApiFunction.ecoSleep,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode startEco();

  ///
  /// Ecomode
  ///
  @TpdApiResult(
    TpdApiFunction.ecoSleep,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode stopEco();

  ///
  /// SRに移動
  ///
  /// - [DeviceErrorCode] : エラーコード
  ///
  @TpdApiResult(
    TpdApiFunction.sr,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode gotoSR();

  ///
  /// SRを終了
  ///
  /// - [DeviceErrorCode] : エラーコード
  ///
  @TpdApiResult(
    TpdApiFunction.sr,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode exitSR();

  ///
  /// SRBastingWarning popup表示したい
  ///
  ///
  ///
  /// このAPIを呼ぶとbrother_panelでエラーMSGが発行されます。ErrorIDは85です。
  /// このエラーは[OK]ボタンだけあります。
  ///
  /// 戻り値:
  /// - [DeviceErrorCode] : エラーコード
  ///
  @TpdApiResult(
    TpdApiFunction.sr,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode gotoSRBastingWarning();

  ///
  /// SRBastingWarning
  ///
  /// SRBastingWarning popup表示閉じる
  ///
  /// 戻り値:
  /// - [DeviceErrorCode] : エラーコード
  ///
  @TpdApiResult(
    TpdApiFunction.sr,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode exitSRBastingWarning();

  ///
  /// Home画面から遷移する
  ///
  /// 引数[in]:
  /// - ない
  ///
  /// 戻り値   :
  /// - [InitState] : 起動状態
  ///
  @TpdApiResult(
    TpdApiFunction.homeKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode gotoUtlFromHome();

  ///
  /// Home画面から遷移する
  ///
  @TpdApiResult(
    TpdApiFunction.homeKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode gotoEmbFromHome();

  ///
  /// Home画面から遷移する
  ///
  @TpdApiResult(
    TpdApiFunction.homeKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode gotoDisneyFromHome();

  ///
  /// Home画面から遷移する
  ///
  @TpdApiResult(
    TpdApiFunction.homeKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode gotoMDCFromHome();

  ///
  /// ヘッダーのカメラボタンをクリックして
  /// それぞれのページでカメラを開くときに使用されます
  ///
  /// 戻る：
  /// [DeviceErrorCode.devNoError]
  /// [DeviceErrorCode.devInvalidError]
  /// [DeviceErrorCode.devInvalidPanelError]
  ///
  DeviceErrorCode showChangeView();

  ///
  /// ヘッダーのカメラボタンをクリックして
  /// それぞれのページでカメラを閉じたときに使用されます
  ///
  @TpdApiResult(
    TpdApiFunction.cameraClose,
    ApiStatus.libMaintenance,
    "",
  )
  DeviceErrorCode hideChangeView();

  ///
  /// カメラインターフェースを閉じる
  ///
  /// 戻る：
  /// [DeviceErrorCode.devNoError]
  /// [DeviceErrorCode.devInvalidError]
  ///
  @TpdApiResult(
    TpdApiFunction.cameraClose,
    ApiStatus.libMaintenance,
    """
    """,
  )
  DeviceErrorCode closeChangeView();

  ///
  /// gotoHomeFromOpening
  ///
  @TpdApiResult(
    TpdApiFunction.homeKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode gotoHomeFromOpening();

  ///
  /// DemoMode モードに入る
  ///
  /// 戻る値は
  /// -[DeviceErrorCode] : エラーコード
  ///
  @TpdApiResult(
    TpdApiFunction.demoMode,
    ApiStatus.libMaintenance,
    "",
  )
  DeviceErrorCode startDemoMode();

  ///
  /// 設定画面から戻る
  ///
  DeviceErrorCode returnSettings();

  ///
  /// Embroidery Unit 接続ステータスを取得する
  ///
  /// 戻る値は
  /// - true :Embroidery Unit装着する
  /// - false :Embroidery Unit装着しない
  ///
  bool isEmbUnitConnect();

  ///
  /// MyIlEdit からUtlプレビューへ移動
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.libMaintenance,
    "",
  )
  DeviceErrorCode gotoUtlPreviewFromMyIlEdit();

  ///
  /// 移動Utlプレビュー
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.libMaintenance,
    "",
  )
  DeviceErrorCode gotoUtlPreview();

  ///
  /// カメラ画像に針落ち点を表示する
  ///
  /// 戻る：
  /// [DeviceErrorCode.devNoError]
  /// [DeviceErrorCode.devInvalidError]
  /// [DeviceErrorCode.devInvalidPanelError]
  ///
  DeviceErrorCode setChangeViewNeedlePosition(bool isOn);

  ///
  /// カメラ画像にグリッドを表示する
  ///
  /// 戻る：
  /// [DeviceErrorCode.devNoError]
  /// [DeviceErrorCode.devInvalidError]
  ///
  DeviceErrorCode seChangeViewGrid(bool isOn);

  ///
  /// カメラ画像をZoom In/Outする
  ///
  /// 戻る：
  /// [DeviceErrorCode.devNoError]
  /// [DeviceErrorCode.devInvalidError]
  ///
  DeviceErrorCode seChangeViewZoom(bool isOn);

  ///
  /// デバイスをロックする
  ///
  @TpdApiResult(
    TpdApiFunction.outKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode lockAll();

  ///
  /// デバイスをアンロックする
  ///
  @TpdApiResult(
    TpdApiFunction.outKey,
    ApiStatus.ok,
    "",
  )
  DeviceErrorCode clearLockAll();

  ///
  /// ActionMatrix_Task 周期処理を一時停止/再開 (スタイル変換を動かす前にsuspend、終わったらresumeで再開すること)
  ///
  @TpdApiResult(
    TpdApiFunction.matrixTask,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  DeviceErrorCode suspendActionMatrixTask();

  ///
  /// ActionMatrix_Task 周期処理を一時停止/再開 (スタイル変換を動かす前にsuspend、終わったらresumeで再開すること)
  ///
  @TpdApiResult(
    TpdApiFunction.matrixTask,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  DeviceErrorCode resumeActionMatrixTask();

  ///
  /// アプリがエラー状態か
  ///
  @TpdApiResult(
    TpdApiFunction.appDisplayData,
    ApiStatus.ok,
    """
    """,
  )
  DeviceErrorCode setErrorState(int error);

  ///
  /// setAppErrorState
  ///
  @TpdApiResult(
    TpdApiFunction.appDisplayData,
    ApiStatus.ok,
    """
    """,
  )
  DeviceErrorCode setAppErrorState(ErrAct_t errAct);

  ///
  /// clearAppErrorState
  ///
  @TpdApiResult(
    TpdApiFunction.appDisplayData,
    ApiStatus.ok,
    """
    """,
  )
  DeviceErrorCode clearAppErrorState();

  ///
  /// タッチイベントを通知する
  ///
  DeviceErrorCode displayTouchEvent();

  ///
  /// utilityからDecoに入る
  ///
  /// 戻り値   :
  /// - [DeviceErrorCode] : エラーコード
  ///
  DeviceErrorCode gotoDecoFromUtl();

  ///
  /// Decoからutilityに入る
  ///
  /// 戻り値   :
  /// - [DeviceErrorCode] : エラーコード
  ///
  DeviceErrorCode gotoUtlModeFromDeco();

  ///
  /// DecoからmyIllustに入る
  ///
  /// 戻り値   :
  /// - [DeviceErrorCode] : エラーコード
  ///
  @TpdApiResult(
    TpdApiFunction.sewingKeyFunction,
    ApiStatus.ok,
    """
    """,
  )
  DeviceErrorCode gotoMyIlFromDeco();

  ///
  /// myIllustからDecoに入る
  ///
  /// 戻り値   :
  /// - [DeviceErrorCode] : エラーコード
  ///
  DeviceErrorCode gotoDecoFromMyIl();

  ///
  /// 編集からテストに入る
  ///
  /// 戻り値   :
  /// - [DeviceErrorCode] : エラーコード
  ///
  DeviceErrorCode gotoMyIlTestFromMyIlEdit();

  ///
  /// テストから編集に入る
  ///
  /// 戻り値   :
  /// - [DeviceErrorCode] : エラーコード
  ///
  @Deprecated("エラーコード2： devInvalidErrorが返される")
  DeviceErrorCode gotoMyIlEditFromMyIlTest();

  ///
  /// bpIFInitMinimum
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.libMaintenance,
    """
    """,
  )
  int bpIFInitMinimum(String workFolderPath);

  ///
  /// メンテナンスモード等のpopup表示を行うか？
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.libMaintenance,
    """
    """,
  )
  PopupState isDisplayPopup();

  ///
  /// 初回ガイダンスのチェック開始
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.libMaintenance,
    "",
  )
  DeviceErrorCode initSettingGuidanceStart();

  ///
  /// 初回ガイダンスのチェック終了
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.libMaintenance,
    "",
  )
  DeviceErrorCode initSettingGuidanceComplete();

  ///
  /// setMatrixEnableList
  /// 詳細は [MachineKeyState]に参考してください
  ///
  /// 戻り値   :
  /// - [DirErrorCode.dirMotorError] : メガキー動作中(無効音)
  /// - [DirErrorCode.dirInvalidError] : setMatrixEnableListNotOverwrited既に設定済み
  /// - [DirErrorCode.dirNoError] : 正常に運行している
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.ok,
    "",
  )
  DirErrorCode setMatrixEnableList(int activatePattern);

  ///
  /// アプリのBPIF_MAX値を通知する
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.libMaintenance,
    "",
  )
  int appBpifMaxNotification(int idMax);

  ///
  /// エラーなどで上書きしない状態でメカキーロックを設定する
  /// 詳細は [MachineKeyState]に参考してください
  ///
  @TpdApiResult(
    TpdApiFunction.libInit,
    ApiStatus.libMaintenance,
    "",
  )
  bool setMatrixEnableListNotOverwrited(int activatePattern);

  ///
  /// setMatrixEnableListNotOverwritedとセットで使用する
  /// 上書き禁止状態を解除する
  /// 詳細は [MachineKeyState]に参考してください
  ///
  void clearMatrixEnableListNotOverwrited(int activatePattern);

  ///
  /// カメラ表示を停止するための関数
  /// 呼べば、isAspectViewやisChangeViewは0になります。
  ///
  /// 戻り値   :
  /// - [DeviceErrorCode] : エラーコード
  ///
  DeviceErrorCode stopCameraCapture();
}

mixin TpdLibraryAPIExtension {
  ///
  /// [SSLightingColor] : SSキーの色
  ///
  @TpdApiResult(
    TpdApiFunction.appDisplayData,
    ApiStatus.ok,
    "",
  )
  SSLightingColor getSSLedColor();

  ///
  /// Ecoを閉じる、Libからの頼み
  ///
  /// 例えば ミシンの実体のボタン押す(SSkey etc)
  ///
  bool requestExitEcoModel();

  ///
  /// ScreenSaverを閉じる、Libからの頼み
  ///
  /// 例えば ミシンの実体のボタン押す(SSkey etc)
  ///
  bool requestExitScreenSaver();

  ///
  /// 前回のAPI呼び出しから今回の呼び出しまでの間に、機械の物理キーが押されたかどうかを確認します。
  ///
  /// より即時的なキー押下のフィードバックを得るには、このAPIを頻繁にポーリングし、ポーリング開始前に
  /// 一度このAPIを呼び出して前回の結果をクリアするか、最初の戻り値を無視してください。
  ///
  /// APIの実装方法は、前回の押下状態を保存し、押下状態をクリアしてから、保存した結果を返します。
  ///
  @TpdApiResult(
    TpdApiFunction.machineKey,
    ApiStatus.ok,
    "",
  )
  bool hasMechaKeySousaBetweenLastAndCurrentCheck();

  ///
  /// shutdown flag
  ///
  /// アプリ再開の時に LibはアプリがShutOff画面に入る欲しい
  ///
  bool isInShutOffModel();

  ///
  /// BwdState状態取得
  ///
  BwdPanelState getBwdState();

  ///
  /// BaseModeを取得します
  ///
  BaseMode_t getBaseMode();

  ///
  /// XPコード「FMDTStateGet() != FMDT_Not」を参照してください。
  /// TODO:http://apngoap306.ap.brothergroup.net:8080/jira/browse/PHBSH-2394
  ///
  bool isNotFrameMoveDownThreadState();

  ///
  /// パルスモータ回転中判断
  /// XPコード「PulseMotor_Driving(InMemoryPF_PulseMotorState())」を参照してください。
  /// TODO:http://apngoap306.ap.brothergroup.net:8080/jira/browse/PHBSH-2394
  ///
  bool isPulseMotorDriving();

  ///
  /// headerのSVGはUnit8Listへ変換
  ///
  Future<void> changHeaderSvgToByte() async {}

  ///
  /// headerのSVGはUnit8Listを読みます
  ///
  List<HeaderFooterSvgInfo> getHeaderSvgInfo();

  ///
  /// footerのSVGはUnit8Listへ変換
  ///
  Future<void> changFooterSvgToByte() async {}

  ///
  /// footerのSVGはUnit8Listを読みます
  ///
  List<HeaderFooterSvgInfo> getFooterSvgInfo();

  ///
  /// MDC画面 Cancel キー処理。刺繍選択画面に戻る。
  ///  ・MCDに描画されているデータをクリアする。
  ///  ・MDC内部データの解放。
  ///  ・刺繍グループの再設定。
  DeviceErrorCode keyMDCMakeCancel();

  ///
  /// 自動テストモードに入るかどうか
  ///
  bool shouldEnterAutoTestMode();
}
