import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_tatami_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'pull_compensation_setting_popup_view_interface.dart';

final pullCompensationSettingPopupViewModelProvider = StateNotifierProvider
    .autoDispose<PullCompensationStateViewInterface, PullCompensationState>(
        (ref) => PullCompensationSettingPopupViewModel(ref));

class PullCompensationSettingPopupViewModel
    extends PullCompensationStateViewInterface {
  PullCompensationSettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const PullCompensationState(), ref) {
    _pullCompensationState = SurfaceTatamiModel().getPullCompensation() !=
            SurfaceTatamiModel.pullCompensationNotUpdating
        ? TatamiSettingState.settingCompleted
        : TatamiSettingState.unknown;
    update();
  }

  ///
  /// 最大幅値
  final int _maxPullCompensationValue = 20;

  ///
  /// 最小幅値
  ///
  final int _minPullCompensationValue = 0;

  ///
  /// ステップ量
  ///
  final int _stepValue = 1;

  ///
  /// 縫い縮み設定(ステッチの長さ補正)の状態
  ///
  TatamiSettingState _pullCompensationState =
      TatamiSettingState.settingCompleted;

  ///
  /// 縫い縮み設定(ステッチの長さ補正)
  ///
  int _pullCompensationValue = SurfaceTatamiModel().getPullCompensation();

  @override
  Unit currentSelectedUnit = DeviceInfoModel().displayUnitType;

  @override
  void update() {
    state = state.copyWith(
      pullCompensationValue: _getPullCompensationValue(),
      isMainValue:
          _pullCompensationValue == _minPullCompensationValue ? true : false,
      isMaxValue:
          _pullCompensationValue == _maxPullCompensationValue ? true : false,
    );
  }

  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_pullCompensationState == TatamiSettingState.unknown) {
      _pullCompensationValue =
          SurfaceTatamiModel().pullCompensationDefaultValue;
    } else {
      _pullCompensationValue = _pullCompensationValue - _stepValue;
      if (_pullCompensationValue < _minPullCompensationValue) {
        _pullCompensationValue = _minPullCompensationValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }

    ///  Model 更新

    _pullCompensationState = TatamiSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    state = state.copyWith(
      pullCompensationValue: _getPullCompensationValue(),
      isMaxValue: false,
      isMainValue:
          _pullCompensationValue == _minPullCompensationValue ? true : false,
    );
    return true;
  }

  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_pullCompensationState == TatamiSettingState.unknown) {
      _pullCompensationValue =
          SurfaceTatamiModel().pullCompensationDefaultValue;
    } else {
      _pullCompensationValue = _pullCompensationValue + _stepValue;
      if (_pullCompensationValue > _maxPullCompensationValue) {
        _pullCompensationValue = _maxPullCompensationValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }

    ///  Model 更新
    _pullCompensationState = TatamiSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    state = state.copyWith(
      pullCompensationValue: _getPullCompensationValue(),
      isMaxValue:
          _pullCompensationValue == _maxPullCompensationValue ? true : false,
      isMainValue: false,
    );
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref.read(stitchPageViewModelProvider.notifier).maybeRemovePopupRoute(
        PopupEnum.surfaceTatamiPullCompensation.toString());
    if (_pullCompensationState == TatamiSettingState.unknown) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    /// PullCompensationの初期値
    int oldPullCompensationValue = SurfaceTatamiModel().getPullCompensation();

    /// Model更新
    SurfaceTatamiModel().setPullCompensation(_pullCompensationValue);

    if (SurfaceTatamiModel().setTatamiSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (_pullCompensationValue != oldPullCompensationValue) {
      ResumeHistoryModel().backSnapshot();
    } else {
      /// do nothing
    }

    _pullCompensationState = TatamiSettingState.settingCompleted;
    CreationModel().changeStitchCreation();
  }

  ///
  /// 縫い縮み設定(ステッチの長さ補正)値を取得する
  ///
  String _getPullCompensationValue() {
    /// cmからmmへ
    double value = _pullCompensationValue / 10.0;

    if (_pullCompensationState == TatamiSettingState.unknown) {
      if (currentSelectedUnit == Unit.mm) {
        return SurfaceTatamiModel.differentMmPullCompensationValue;
      } else {
        return SurfaceTatamiModel.differentInchPullCompensationValue;
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return value.toStringAsFixed(1);
    }

    return ToolbarModel.getDisplayInchShowValue(value);
  }

  @override
  bool getPullCompensationTextStyle() {
    if (_pullCompensationState == TatamiSettingState.unknown) {
      return true;
    }

    if (_pullCompensationValue ==
        SurfaceTatamiModel().pullCompensationDefaultValue) {
      return true;
    } else {
      return false;
    }
  }
}
