// ignore_for_file: unused_field
import 'dart:core';
import 'dart:ffi' as dart_ffi;
import 'dart:ffi';
import 'dart:typed_data';

import 'package:audio_player/audio_player_interface.dart';
import 'package:ffi/ffi.dart' as ffi;
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ph_eel_plugin/devedit_bindings.dart' show getDevEditBindings;
import 'package:ph_eel_plugin/devedit_bindings_generated.dart' as dev_gen;
import 'package:ph_eel_plugin/warpchk_bindings.dart' show getWarpChkBindings;
import 'package:ph_eel_plugin/warpchk_bindings_generated.dart' as warp_gen;
import 'package:system_config/system_config.dart';

import '../../../../../panel_library.dart';
import '../../device_library_log.dart';
import '../../interface/api_interface.dart';
import '../../interface/test_api_interface.dart';
import 'mock_connection_server_data.dart';
import 'mock_setting_data.dart';

final dev_gen.DevEditBindings _bindings = getDevEditBindings();
final warp_gen.WarpChkBindings? _warpChkBindings = getWarpChkBindings();
// TODO 刺しゅうユニットのチェックがないので、いったん追加する
FrameSize embFrameType = FrameSizeType.FRAME_NOTHING;

class DeviceLibraryMockImplementAndroid implements DeviceLibraryAPIInterface {
  DeviceLibraryMockImplementAndroid();

  void init(WidgetRef ref) {}

//////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ１         /////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////
  ///
  /// スピードコントロールレバーを使用してWIDTH調節を行う。
  ///
  /// [value] : true(ON)/false(OFF)
  ///
  @override
  DeviceErrorCode setWidthControl(bool value) {
    DeviceLibraryLog.hello("setWidthControl,$value");
    final errIndex = _bindings.setWidthControl(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setWidthControl");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, bool value}) getWidthControl() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getWidthControl");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getWidthControl(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye(" getWidthControl,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, bool value}) getWidthControlDefault() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getWidthControlDefault");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getWidthControlDefault(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye(" getWidthControlDefault,$value");
        return (errorCode: errCode, value: value);
      });

  ///
  /// 送り調整（模様調整－縦方向）
  ///
  /// 文字・模様縫いの模様を調節する
  ///
  /// [value] : -9 ~ 9( 初期値:0,単位：１ )
  ///
  @override
  DeviceErrorCode setFineAdjustVerti(int value) {
    DeviceLibraryLog.hello("setFineAdjustVerti");
    final errIndex = _bindings.setFineAdjustVerti(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" setFineAdjustVerti");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getFineAdjustVerti() {
    DeviceLibraryLog.hello("getFineAdjustVerti");
    final pointer = ffi.calloc<dart_ffi.Int16>();
    final errIndex = _bindings.getFineAdjustVerti(pointer);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    final int value = pointer.value;
    ffi.calloc.free(pointer);
    DeviceLibraryLog.byeBye(" getFineAdjustVerti,$value");
    return (errorCode: errCode, value: value);
  }

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getFineAdjustVertiValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getFineAdjustVertiValueList");
            final pointer = arena.call<dev_gen.UserSettingItemValue>();
            final int errIndex = _bindings.getFineAdjustVertiValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValue valueList =
                UserSettingItemValue.fromPointer(pointer);
            DeviceLibraryLog.byeBye("getFineAdjustVertiValueList");
            return (errorCode: errCode, valueList: valueList);
          });

  ///
  /// 横送り調整（模様調整－横方向）
  ///
  /// 文字・模様縫いの模様を調節する
  ///
  /// [value] : -9 ~ 9( 初期値:0,単位：１ )
  ///
  @override
  DeviceErrorCode setFineAdjustHoriz(int value) {
    DeviceLibraryLog.hello("setFineAdjustHoriz,$value");
    final errIndex = _bindings.setFineAdjustHoriz(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" setFineAdjustHoriz");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getFineAdjustHoriz() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getFineAdjustHoriz");
        final pointer = arena.call<dart_ffi.Int16>();
        final errIndex = _bindings.getFineAdjustHoriz(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = pointer.value;
        DeviceLibraryLog.byeBye("getFineAdjustHoriz,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValue value})
      getFineAdjustHorizValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getFineAdjustHorizValueList");
            final pointer = arena.call<dev_gen.UserSettingItemValue>();
            final int errIndex = _bindings.getFineAdjustHorizValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValue valueList =
                UserSettingItemValue.fromPointer(pointer);
            DeviceLibraryLog.byeBye("getFineAdjustHorizValueList");
            return (errorCode: errCode, value: valueList);
          });

  ///
  /// 押え高さ調整
  ///
  /// 押えが上がった状態の押えの高さを調節する。
  ///
  /// [value] : 0~3
  /// 3.2 ~ 10.0( 初期値:7.5,選択肢　2.0mm、3.2mm、7.5mm、10.0mmから選択 )
  ///
  @override
  DeviceErrorCode setPresserFootHeight(int value) {
    DeviceLibraryLog.hello("setPresserFootHeight,$value");
    final errIndex = _bindings.setPresserFootHeight(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setPresserFootHeight");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getPresserFootHeight() {
    DeviceLibraryLog.hello("getPresserFootHeight");
    final pointer = ffi.calloc<dart_ffi.Int16>();
    final errIndex = _bindings.getPresserFootHeight(pointer);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    final int value = _changeSrPresserFootHeightToInt(pointer.value);
    ffi.calloc.free(pointer);
    DeviceLibraryLog.byeBye(
        "getPresserFootHeight,errCode:$errCode,value:$value");
    return (errorCode: errCode, value: value);
  }

  @override
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<int> valueList
  }) getPresserFootHeightValueList() => ffi.using((arena) {
        DeviceLibraryLog.hello("getPresserFootHeightValueList");
        final pointer = arena.call<dev_gen.UserSettingItemValueList>();
        final int errIndex = _bindings.getPresserFootHeightValueList(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

        final UserSettingItemValueList<int> valueList =
            UserSettingItemValueList.fromPointer(
                pointer, _changeSrPresserFootHeightToInt);
        DeviceLibraryLog.byeBye("getPresserFootHeightValueList, $valueList");
        // Cpp側で確保したメモリを解放 //
        _bindings.UserSettingItemValueListFree(pointer);
        return (errorCode: errCode, valueList: valueList);
      });

  @override
  DeviceErrorCode setSrPresserFootHeight(int height) {
    DeviceLibraryLog.hello("setSrPresserFootHeight,$height");
    var tempJsonData = getMockData();
    tempJsonData["stitchRegulatorFootHeight"] = height;
    setMockData(tempJsonData);
    DeviceLibraryLog.byeBye("setSrPresserFootHeight");
    return DeviceErrorCode.devNoError;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getSrPresserFootHeight() {
    DeviceLibraryLog.hello("getSrPresserFootHeight");

    int value = getMockData()["stitchRegulatorFootHeight"] ?? 15;
    DeviceLibraryLog.byeBye("getSrPresserFootHeight,$value");
    return (errorCode: DeviceErrorCode.devNoError, value: value);
  }

  ///
  /// int に変換します
  ///
  int _changeSrPresserFootHeightToInt(int value) => value;

  ///
  /// 押え圧力
  ///
  /// 押えの圧力を調節する。 選択肢1,2,3,4（数字が大きいほど押え圧は強くなる。）
  ///
  /// ＊DFモジュールが接続され、かつローラーが下の場合は、押え圧力の設定は2で固定となる。
  /// バージョンアップにて、＋―キーがないので、固定だとわかる手段を準備する予定。。
  ///
  /// [value] : 1 ~ 4( 初期値:3 )
  ///
  @override
  DeviceErrorCode setPresserFootPressure(int value) {
    DeviceLibraryLog.hello("setPresserFootPressure,$value");
    final errIndex = _bindings.setPresserFootPressure(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setPresserFootPressure");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getPresserFootPressure() {
    DeviceLibraryLog.hello("getPresserFootPressure");
    final pointer = ffi.calloc<dart_ffi.Int16>();
    final errIndex = _bindings.getPresserFootPressure(pointer);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    final int value = _changePresserFootPressureToInt(pointer.value);
    ffi.calloc.free(pointer);
    DeviceLibraryLog.byeBye("getPresserFootPressure,$value");
    return (errorCode: errCode, value: value);
  }

  @override
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<int> valueList
  }) getPresserFootPressureValueList() => ffi.using((arena) {
        DeviceLibraryLog.hello("getPresserFootPressureValueList");
        final pointer = arena.call<dev_gen.UserSettingItemValueList>();
        final int errIndex = _bindings.getPresserFootPressureValueList(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

        final UserSettingItemValueList<int> valueList =
            UserSettingItemValueList.fromPointer(
                pointer, _changePresserFootPressureToInt);
        DeviceLibraryLog.byeBye("getPresserFootPressureValueList");
        // Cpp側で確保したメモリを解放 //
        _bindings.UserSettingItemValueListFree(pointer);
        return (errorCode: errCode, valueList: valueList);
      });

  ///
  /// int に変換します
  ///
  int _changePresserFootPressureToInt(int value) => value;

  ///
  /// センサーシステム（自動押え圧補整）
  ///
  /// ONにすると、センサーが布地の厚さを自動的に読み取って、スムーズに布送りする。
  ///
  /// [value] : bool( 初期値:false )
  ///
  ///
  @override
  DeviceErrorCode setAutomaticFabricSensorSystem(bool value) {
    DeviceLibraryLog.hello("setAutomaticFabricSensorSystem,$value");
    final errIndex = _bindings.setAutomaticFabricSensorSystem(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setAutomaticFabricSensorSystem");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, bool value}) getAutomaticFabricSensorSystem() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getAutomaticFabricSensorSystem");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getAutomaticFabricSensorSystem(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;

        DeviceLibraryLog.byeBye("getAutomaticFabricSensorSystem,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({
    DeviceErrorCode errorCode,
    bool value
  }) getAutomaticFabricSensorSystemDefault() => ffi.using((arena) {
        DeviceLibraryLog.hello("getAutomaticFabricSensorSystemDefault");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex =
            _bindings.getAutomaticFabricSensorSystemDefault(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;

        DeviceLibraryLog.byeBye("getAutomaticFabricSensorSystemDefault,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  DeviceErrorCode deleteCompFrameScanBackImgView() {
    DeviceLibraryLog.hello("deleteCompFrameScanBackImgView,");
    final errIndex = _bindings.deleteCompFrameScanBackImgView();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("deleteCompFrameScanBackImgView");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, bool isOn}) getCompFrameScanBackImgView() =>
      ffi.using((arena) {
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getCompFrameScanBackImgView(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye(" getCompFrameScanBackImgView,$value");
        return (errorCode: errCode, isOn: value);
      });

//////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ2         /////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////

  ///
  /// 初期針位置（基線切り替え）
  ///
  /// 「1-01直線（左基線）」か「1-03直線（中基線）」かを選択する。
  ///
  /// ブラザー：「1-01直線（左基線）」、タコニー：「1-03直線（中基線）」
  ///
  /// 詳細は [BaseLineType] を参考してください
  ///
  ///
  @override
  DeviceErrorCode setBaseLine(BaseLineType value) {
    DeviceLibraryLog.hello("setBaseLine");

    final bool valueBool = BaseLineType.getValueByType(value);

    final errIndex = _bindings.setBaseLine(valueBool);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(
        "setBaseLine,errCode: $errCode,BaseLineType:$value,valueBool:$valueBool");

    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, BaseLineType value}) getBaseLine() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getBaseLine");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getBaseLine(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye(
            "getBaseLine,$value, ${BaseLineType.getValuesByBool(value)}");
        return (
          errorCode: errCode,
          value: BaseLineType.getValuesByBool(value),
        );
      });

  @override
  ({DeviceErrorCode errorCode, BaseLineType value}) getBaseLineDefault() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getBaseLineDefault");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getBaseLineDefault(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye(
            "getBaseLine,$value, ${BaseLineType.getValuesByBool(value)}");
        return (
          errorCode: errCode,
          value: BaseLineType.getValuesByBool(value),
        );
      });

  ///
  /// ピボット押え高さ
  ///
  /// ピボット機能を有効にした時の、縫製停止時の押えの高さを選択する。
  ///
  /// [value] : 0 ~ 3( 初期値:1)
  ///  2.0 ~ 7.5( 初期値:3.2 選択肢 2.0mm, 3.2mm, 5.0mm, 7.5mm)
  ///
  @override
  DeviceErrorCode setPivotingHeight(int value) {
    DeviceLibraryLog.hello("setPivotingHeight");
    final errIndex = _bindings.setPivotingHeight(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setPivotingHeight,$value");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getPivotingHeight() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getPivotingHeight");
        final pointer = arena.call<dart_ffi.Int16>();
        final errIndex = _bindings.getPivotingHeight(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = _changePivotingHeightToInt(pointer.value);
        DeviceLibraryLog.byeBye("getPivotingHeight,$value");
        return (errorCode: errCode, value: value);
      });
  @override
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getPivotingHeightValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getPivotingHeightValueList");
            final pointer = arena.call<dev_gen.UserSettingItemValueList>();
            final int errIndex = _bindings.getPivotingHeightValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValueList<int> valueList =
                UserSettingItemValueList.fromPointer(
                    pointer, _changePivotingHeightToInt);
            DeviceLibraryLog.byeBye("getPivotingHeightValueList, $valueList ");
            //Cpp側で確保したメモリを解放 //
            _bindings.UserSettingItemValueListFree(pointer);
            return (errorCode: errCode, valueList: valueList);
          });

  ///
  /// int に変換します
  ///
  int _changePivotingHeightToInt(int value) => value;

  ///
  /// フリーモーション押え高さ
  ///
  /// フリーモーションに設定した時の、押えの高さを変更する
  ///
  /// [value] : 0 ~7( 初期値:1)
  /// **(初期値:1.0 選択肢　0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0)**
  ///
  @override
  DeviceErrorCode setFreeMotionFootHeight(int value) {
    DeviceLibraryLog.hello("setFreeMotionFootHeight");
    final errIndex = _bindings.setFreeMotionFootHeight(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setFreeMotionFootHeight,$value");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getFreeMotionFootHeight() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getFreeMotionFootHeight");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getFreeMotionFootHeight(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        int value = _changeFreeMotionFootHeightToInt(pointer.value);

        DeviceLibraryLog.byeBye("getFreeMotionFootHeight,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getFreeMotionFootHeightValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getFreeMotionFootHeightValueList");
            final pointer = arena.call<dev_gen.UserSettingItemValueList>();
            final int errIndex =
                _bindings.getFreeMotionFootHeightValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValueList<int> valueList =
                UserSettingItemValueList.fromPointer(
                    pointer, _changeFreeMotionFootHeightToInt);
            DeviceLibraryLog.byeBye("getFreeMotionFootHeightValueList");
            // Cpp側で確保したメモリを解放 //
            _bindings.UserSettingItemValueListFree(pointer);
            return (errorCode: errCode, valueList: valueList);
          });

  ///
  int _changeFreeMotionFootHeightToInt(int value) => value;

  ///
  /// デュアルフィード送り調整
  ///
  /// デュアルフィードの送り量を調整する
  ///
  /// [value] : -10 ~ 10( 初期値:0，単位：１％)
  ///
  @override
  DeviceErrorCode setDualFeedFeedAdjustment(int value) {
    DeviceLibraryLog.hello("setDualFeedFeedAdjustment,$value");
    final errIndex = _bindings.setDualFeedFeedAdjustment(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setDualFeedFeedAdjustment");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getDualFeedFeedAdjustment() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getDualFeedFeedAdjustment");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getDualFeedFeedAdjustment(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = pointer.value;
        DeviceLibraryLog.byeBye("getDualFeedFeedAdjustment,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValue value})
      getDualFeedFeedAdjustmentValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getDualFeedFeedAdjustmentValueList");
            final pointer = arena.call<dev_gen.UserSettingItemValue>();
            final int errIndex =
                _bindings.getDualFeedFeedAdjustmentValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValue value =
                UserSettingItemValue.fromPointer(pointer);
            DeviceLibraryLog.byeBye("getDualFeedFeedAdjustmentValueList");
            return (errorCode: errCode, value: value);
          });

  ///
  /// 自動下げの値を設定する
  ///
  /// [value] : true(ON)/false(OFF)
  ///
  @override
  DeviceErrorCode setUtlAutoDown(bool value) {
    DeviceLibraryLog.hello("setUtlAutoDown,$value");
    final errIndex = _bindings.setUtlAutoDown(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setUtlAutoDown");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, bool value}) getUtlAutoDown() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getUtlAutoDown");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getUtlAutoDown(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;

        DeviceLibraryLog.byeBye("getUtlAutoDown,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, bool value}) getUtlAutoDownDefault() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getUtlAutoDownDefault");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getUtlAutoDownDefault(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye("getUtlAutoDownDefault,$value");
        return (
          errorCode: errCode,
          value: value,
        );
      });

  ///
  /// 自動押え上下
  ///
  /// - 自動下げ
  /// - 糸切り時
  /// - 縫製開始時の自動押え下げ
  /// - 糸切り前後の押え下げ上げ
  ///
  /// **停止時の自動押え上げは、実用のメイン画面に出す。**
  ///
  /// [value] : true(ON)/false(OFF)
  ///
  /// 従来のOFF状態を初期値とする（安全面を考慮するためQMからの指示）
  ///
  @override
  DeviceErrorCode setPressToTrim(value) {
    DeviceLibraryLog.hello("setPressToTrim");
    final errIndex = _bindings.setPressToTrim(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setPressToTrim,$value");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, bool value}) getPressToTrim() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getPressToTrim");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getPressToTrim(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye("getPressToTrim,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, bool value}) getPressToTrimDefault() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getPressToTrimDefault");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getPressToTrimDefault(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye("getPressToTrimDefault,$value");
        return (
          errorCode: errCode,
          value: value,
        );
      });

//////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ3         /////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////

  ///
  /// 実用の起動画面
  ///
  /// 実用のデフォルトカテゴリーを実用カテゴリーにするかキルトカテゴリーにするか設定する。
  ///
  /// [value] : true(キルトＯＦＦ)/false(キルトＯＮ)
  ///
  @override
  DeviceErrorCode setInitialStitchPage(bool value) {
    DeviceLibraryLog.hello("setInitialStitchPage, $value");
    final errIndex = _bindings.setInitialStitchPage(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setInitialStitchPage,errCode:$errCode");
    return errCode;
  }

  @override
  @override
  ({DeviceErrorCode errorCode, bool value}) getInitialStitchPage() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getInitialStitchPage");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getInitialStitchPage(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye("getInitialStitchPage,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, bool value}) getInitialStitchPageDefault() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getInitialStitchPageDefault");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getInitialStitchPageDefault(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye("getInitialStitchPageDefault,$value");
        return (
          errorCode: errCode,
          value: value,
        );
      });

  ///
  ///
  /// 止め縫い優先
  ///
  /// 止め縫い模様は、止め縫いキーでなく返し縫キーをおしても止め縫いする。
  ///
  /// [value] : true(ON)/false(OFF)
  ///
  @override
  @override
  DeviceErrorCode setReinforcementPriority(bool value) {
    DeviceLibraryLog.hello("setReinforcementPriority");
    final errIndex = _bindings.setReinforcementPriority(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setReinforcementPriority,$value");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, bool value}) getReinforcementPriority() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getReinforcementPriority");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getReinforcementPriority(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye("getReinforcementPriority,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, bool value}) getReinforcementPriorityDefault() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getReinforcementPriorityDefault");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getReinforcementPriorityDefault(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye("getReinforcementPriorityDefault,$value");
        return (
          errorCode: errCode,
          value: value,
        );
      });

  ///
  /// マルチファンクションフットコントローラー
  ///
  /// メインフットコントローラーのヒールスイッチ機能を設定する
  ///
  /// [value] : 詳細は [HeelSwitch] を参考してください
  /// **(初期値:Needle Position)**
  ///
  @override
  ({DeviceErrorCode errorCode, HeelSwitch value}) getHeelSwitch() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getHeelSwitch");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getHeelSwitch(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final HeelSwitch value = _changeIntToHeelSwitch(pointer.value);
        DeviceLibraryLog.byeBye("getHeelSwitch,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  DeviceErrorCode setHeelSwitch(HeelSwitch value) {
    DeviceLibraryLog.hello("setHeelSwitch");
    final errIndex = _bindings.setHeelSwitch(value.index);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setHeelSwitch,$value");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValueList<HeelSwitch> valueList})
      getHeelSwitchValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getHeelSwitchValueList");
            final pointer = arena.call<dev_gen.UserSettingItemValueList>();
            final int errIndex = _bindings.getHeelSwitchValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValueList<HeelSwitch> valueList =
                UserSettingItemValueList.fromPointer(
                    pointer, _changeIntToHeelSwitch);

            DeviceLibraryLog.byeBye("getHeelSwitchValueList");
            // Cpp側で確保したメモリを解放 //
            _bindings.UserSettingItemValueListFree(pointer);
            return (errorCode: errCode, valueList: valueList);
          });

  ///
  /// int を HeelSwitch に変換します
  ///
  HeelSwitch _changeIntToHeelSwitch(int value) {
    if (value < 0 || value >= HeelSwitch.values.length) {
      DeviceLibraryLog.e("invalid levelIndex($value), change to default");
      return HeelSwitch.FHK_FUNC_NP;
    } else {
      return HeelSwitch.values[value];
    }
  }

  ///
  /// マルチファンクションフットコントローラー
  ///
  /// メインフットコントローラーのサイドペダル機能を設定する
  ///
  /// [value] : 詳細は [SidePedal] を参考してください
  /// **(初期値:Thread Cutting)**
  ///
  /// ※Heel switchと同一設定が可能。排他は行わない
  ///
  @override
  DeviceErrorCode setSidePedal(SidePedal value) {
    DeviceLibraryLog.hello("setSidePedal");
    final errIndex = _bindings.setSidePedal(value.index);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setSidePedal,$value");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, SidePedal value}) getSidePedal() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getSidePedal");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getSidePedal(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final SidePedal value = _changeIntToSidePedal(pointer.value);
        DeviceLibraryLog.byeBye("getSidePedal,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValueList<SidePedal> valueList})
      getSidePedalValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getSidePedalValueList");
            final pointer = arena.call<dev_gen.UserSettingItemValueList>();
            final int errIndex = _bindings.getSidePedalValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValueList<SidePedal> valueList =
                UserSettingItemValueList.fromPointer(
                    pointer, _changeIntToSidePedal);
            DeviceLibraryLog.byeBye("getSidePedalValueList");
            // Cpp側で確保したメモリを解放 //
            _bindings.UserSettingItemValueListFree(pointer);
            return (errorCode: errCode, valueList: valueList);
          });

  ///
  /// int を SidePedal に変換します
  ///
  SidePedal _changeIntToSidePedal(int value) {
    if (value < 0 || value >= SidePedal.values.length) {
      DeviceLibraryLog.e("invalid SidePedal($value), change to default");
      return SidePedal.FSSW_FUNC_TC;
    } else {
      return SidePedal.values[value];
    }
  }

  ///
  /// 終点設定直前停止
  ///
  /// 停止位置の真上に、終点シールを貼っている場合、縫製終了の直前で一旦停止をして、
  /// 剥がしてもらうための設定。
  ///
  /// [value] : true(ON)/false(OFF)
  /// **(初期値:OFF)**
  ///
  /// ONのときに、一旦停止する
  ///
  @override
  DeviceErrorCode setEndPointSettingTemporaryStop(bool value) {
    DeviceLibraryLog.hello("setEndPointSettingTemporaryStop");
    final errIndex = _bindings.setEndPointSettingTemporaryStop(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setEndPointSettingTemporaryStop,$value");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, bool value}) getEndPointSettingTemporaryStop() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getEndPointSettingTemporaryStop");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getEndPointSettingTemporaryStop(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye("getEndPointSettingTemporaryStop,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, bool value})
      getEndPointSettingTemporaryStopDefault() => ffi.using((arena) {
            DeviceLibraryLog.hello("getEndPointSettingTemporaryStopDefault");
            final pointer = arena.call<dart_ffi.Bool>();
            final errIndex =
                _bindings.getEndPointSettingTemporaryStopDefault(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
            final bool value = pointer.value;
            DeviceLibraryLog.byeBye(
                "getEndPointSettingTemporaryStopDefault,$value");
            return (errorCode: errCode, value: value);
          });

//////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ4         /////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////

  /// 言语設定のキャッシュ値
  Language? _languageValue;

  @override
  DeviceErrorCode saveLanguage(Language language) {
    DeviceLibraryLog.hello("saveLanguage,$language");
    final errIndex = _bindings.saveLanguage(language.index);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    _languageValue = errCode != DeviceErrorCode.devNoError ? null : language;
    DeviceLibraryLog.byeBye("saveLanguage");
    return errCode;
  }

  @override
  ({
    DeviceErrorCode errorCode,
    Language value
  }) getLanguage() => _languageValue == null
      ? ffi.using((arena) {
          DeviceLibraryLog.hello("getLanguage");
          final pointer = arena.call<dart_ffi.Int8>();
          final errIndex = _bindings.getLanguage(pointer);
          final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
          Language value = Language.LANG_ENGLISH;

          if (pointer.value < 0 || pointer.value >= Language.values.length) {
            DeviceLibraryLog.e("invalid Language($value), change to default");
            value = Language.LANG_ENGLISH;
          } else {
            value = Language.values[pointer.value];
          }

          _languageValue = errCode != DeviceErrorCode.devNoError ? null : value;

          DeviceLibraryLog.byeBye("getLanguage,$value");
          return (errorCode: errCode, value: value);
        })
      : (errorCode: DeviceErrorCode.devNoError, value: _languageValue!);

  @override
  ({DeviceErrorCode errorCode, MessageSoundLanguage value})
      getMessageSoundLanguage() {
    DeviceLibraryLog.hello("getMessageSoundLanguage");

    try {
      int value = getMockData()["readAloudLanguage"] ?? 0;
      DeviceLibraryLog.byeBye("getMessageSoundLanguage,$value");
      return (
        errorCode: DeviceErrorCode.devNoError,
        value: MessageSoundLanguage.values[value]
      );
    } catch (e) {
      DeviceLibraryLog.byeBye(
          "getMessageSoundLanguage,error occur default return");
      return (
        errorCode: DeviceErrorCode.devNoError,
        value: MessageSoundLanguage.englishA
      );
    }
  }

  @override
  ({DeviceErrorCode errorCode, bool value}) getVoiceGuidanceOnOff() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getVoiceGuidanceOnOff");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getVoiceGuidanceOnOff(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye("getVoiceGuidanceOnOff,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  DeviceErrorCode saveVoiceGuidanceOnOff(bool value) {
    DeviceLibraryLog.hello("saveVoiceGuidanceOnOff,$value");
    final errIndex = _bindings.saveVoiceGuidanceOnOff(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("saveVoiceGuidanceOnOff");
    return errCode;
  }

  @override
  DeviceErrorCode setMessageSoundLanguage(MessageSoundLanguage language) {
    DeviceLibraryLog.hello("setMessageSoundLanguage,$language");
    var tempJsonData = getMockData();
    tempJsonData["readAloudLanguage"] = language.index;
    setMockData(tempJsonData);
    DeviceLibraryLog.byeBye("setMessageSoundLanguage");
    return DeviceErrorCode.devNoError;
  }

  ///
  /// 読み上げ音ボリューム
  ///
  /// 読み上げ音のスピーカーの音量を調整する。
  ///
  /// [value] : 0 ~ 5
  /// **(初期値:3、単位：1)**
  ///
  /// ※ バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @override
  DeviceErrorCode saveVoiceVolume(int level) {
    DeviceLibraryLog.hello("saveVoiceVolume,$level");
    MessageSoundPlayer().setVolume(level / maxVolume);
    final errIndex = _bindings.saveVoiceVolume(level);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("saveVoiceVolume");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getVoiceVolume() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getVoiceVolume");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getVoiceVolume(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        const int messageVolumeDefault = 3;
        int value = messageVolumeDefault;
        if (pointer.value < 0) {
          value = messageVolumeDefault;
        } else {
          value = pointer.value;
        }
        DeviceLibraryLog.byeBye("getVoiceVolume,$value");
        return (errorCode: errCode, value: value);
      });

  ///
  /// SRブザー音ボリューム設定
  /// 追従不可のときに鳴らす
  ///
  /// [value] : 0 ~ 5(0:off)
  /// **(初期値:3、単位：1)**
  ///
  /// ※ 設定画面　他のボリュームと別項目
  ///
  @override
  DeviceErrorCode saveSRVolume(int level) {
    DeviceLibraryLog.hello("saveSRVolume,$level");
    final errIndex = _bindings.saveSRVolume(level);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("saveSRVolume");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getSRVolume() => ffi.using((arena) {
        if (getPollingLogFlagForDebug() == true) {
          DeviceLibraryLog.hello("getSRVolume");
        }

        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getSRVolume(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        const int srVolumeDefault = 3;
        int value = srVolumeDefault;

        /// SRのブザーをOFFできないようにする https://brothergroup.atlassian.net/browse/PHFIRMIIVO-7023
        if (pointer.value <= 0) {
          value = srVolumeDefault;
        } else {
          value = pointer.value;
        }

        if (getPollingLogFlagForDebug() == true) {
          DeviceLibraryLog.byeBye("getSRVolume,$value");
        }

        return (errorCode: errCode, value: value);
      });

  ///
  ///ミシン効果音のスピーカーの音量を調整する
  ///
  @override
  DeviceErrorCode saveMachineSpeakerVolume(int level) {
    SystemSoundPlayer().setVolume(level / maxVolume);

    DeviceLibraryLog.hello("saveMachineSpeakerVolume, $level ");
    final errIndex = _bindings.saveMachineSpeakerVolume(level);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("saveMachineSpeakerVolume");

    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getMachineSpeakerVolume() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getMachineSpeakerVolume");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getMachineSpeakerVolume(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        const int messageVolumeDefault = 3;
        int value = messageVolumeDefault;
        if (pointer.value < 0) {
          value = messageVolumeDefault;
        } else {
          value = pointer.value;
        }
        DeviceLibraryLog.byeBye("getMachineSpeakerVolume:$value  ");
        return (errorCode: errCode, value: value);
      });

  ///
  ///ライトの明るさ調整
  ///
  @override
  DeviceErrorCode setLight(int level) {
    DeviceLibraryLog.hello("setLight");
    final errIndex = _bindings.setLight(level);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setLight");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getLight() => ffi.using((arena) {
        DeviceLibraryLog.hello("getLight");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getLight(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = _changeLightToInt(pointer.value);
        DeviceLibraryLog.byeBye("getLight,$value  ");
        return (errorCode: errCode, value: value);
      });

  @override
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<int> valueList
  }) getLightValueList() => ffi.using((arena) {
        DeviceLibraryLog.hello("getLightValueList");
        final pointer = arena.call<dev_gen.UserSettingItemValueList>();
        final int errIndex = _bindings.getLightValueList(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

        final UserSettingItemValueList<int> valueList =
            UserSettingItemValueList.fromPointer(pointer, _changeLightToInt);
        DeviceLibraryLog.byeBye("getLightValueList");
        // Cpp側で確保したメモリを解放 //
        _bindings.UserSettingItemValueListFree(pointer);
        return (errorCode: errCode, valueList: valueList);
      });

  ///
  /// int に変換します
  ///
  int _changeLightToInt(int value) => value;

  ///
  ///液晶画面の明るさを調整する
  ///
  @override
  DeviceErrorCode setScreenDisplayBrightness(ScreenBrightnessLevel level) {
    DeviceLibraryLog.hello(
        "setScreenDisplayBrightness,$level,${ScreenBrightnessLevel.toFFIApiIndex(level)}");
    final errIndex = _bindings
        .setScreenDisplayBrightness(ScreenBrightnessLevel.toFFIApiIndex(level));
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setScreenDisplayBrightness,error: $errIndex");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, ScreenBrightnessLevel value})
      getScreenDisplayBrightness() => ffi.using((arena) {
            DeviceLibraryLog.hello("getScreenDisplayBrightness");
            final pointer = arena.call<dart_ffi.Int8>();
            final errIndex = _bindings.getScreenDisplayBrightness(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
            ScreenBrightnessLevel value =
                ScreenBrightnessLevel.formFFIApiIndex(pointer.value);
            DeviceLibraryLog.byeBye(
                "getScreenDisplayBrightness,error: $errIndex,originalValue:${pointer.value},$value");
            return (errorCode: errCode, value: value);
          });

  @override
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<ScreenBrightnessLevel> valueList
  }) getScreenDisplayBrightnessValueList() => ffi.using((arena) {
        DeviceLibraryLog.hello("getScreenDisplayBrightnessValueList");
        final pointer = arena.call<dev_gen.UserSettingItemValueList>();
        final int errIndex =
            _bindings.getScreenDisplayBrightnessValueList(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

        final UserSettingItemValueList<ScreenBrightnessLevel> valueList =
            UserSettingItemValueList.fromPointer(
                pointer, ScreenBrightnessLevel.formFFIApiIndex);
        DeviceLibraryLog.byeBye(
            "getScreenDisplayBrightnessValueList,$valueList");
        // Cpp側で確保したメモリを解放 //
        _bindings.UserSettingItemValueListFree(pointer);
        return (errorCode: errCode, valueList: valueList);
      });

//////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ5         /////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////
  ///
  /// Initial Screen（起動画面）
  ///
  /// 起動時の表示画面を選択する。
  ///
  /// [value] :選択肢は３つ 0-Opening Screen、1-Home Page、2-Sewing/Embroidery
  ///
  /// **(初期値:0)** 詳細は　[InitialScreenEnum]に参考ください
  /// ※ バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @override
  DeviceErrorCode setInitialScreen(int initialScreenIndex) {
    DeviceLibraryLog.hello("setInitialScreen,$initialScreenIndex");
    final errIndex = _bindings.setInitialScreen(initialScreenIndex);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setInitialScreen");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getInitialScreen() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getInitialScreen");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getInitialScreen(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = pointer.value;
        DeviceLibraryLog.byeBye(" getInitialScreen,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getInitialScreenValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getInitialScreenValueList");
            final pointer = arena.call<dev_gen.UserSettingItemValueList>();
            final int errIndex = _bindings.getInitialScreenValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValueList<int> valueList =
                UserSettingItemValueList.fromPointer(
                    pointer, _changeInitialScreenValueToInt);
            DeviceLibraryLog.byeBye(
                " getInitialScreenValueList ${valueList.valueList.toString()}");
            return (errorCode: errCode, valueList: valueList);
          });

  int _changeInitialScreenValueToInt(int value) => value;

  ///
  /// スリープ機能
  ///
  /// Eco Modeと
  ///
  /// [value] : 0(OFF) , 10 ~ 120(ON)
  /// **(初期値:OFF、単位：10min)**
  ///
  @override
  DeviceErrorCode saveEcoMode(int value) {
    DeviceLibraryLog.hello("saveEcoMode,$value");
    final errIndex = _bindings.saveEcoMode(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("saveEcoMode");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getEcoMode() => ffi.using((arena) {
        DeviceLibraryLog.hello("getEcoMode");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getEcoMode(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = pointer.value;
        DeviceLibraryLog.byeBye(" getEcoMode,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getEcoModeValueList() => (
            errorCode: DeviceErrorCode.devNoError,
            valueList: const UserSettingItemValueList(
              0,
              13,
              [
                DisplayValue(0, "OFF"),
                DisplayValue(10, "10 min"),
                DisplayValue(20, "20 min"),
                DisplayValue(30, "30 min"),
                DisplayValue(40, "40 min"),
                DisplayValue(50, "50 min"),
                DisplayValue(60, "60 min"),
                DisplayValue(70, "70 min"),
                DisplayValue(80, "80 min"),
                DisplayValue(90, "90 min"),
                DisplayValue(100, "100 min"),
                DisplayValue(110, "110 min"),
                DisplayValue(120, "120 min"),
              ],
            )
          );

  ///
  /// スリープ機能
  ///
  /// Shutoff Support Modeに入る時間を設定する。
  ///
  /// [value] : 0(OFF) , 1~12(ON)
  /// **(初期値:OFF、単位：1hour)**
  ///
  @override
  DeviceErrorCode saveShutoffSupportMode(int value) {
    DeviceLibraryLog.hello("saveShutoffSupportMode,$value");
    final errIndex = _bindings.saveShutoffSupportMode(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("saveShutoffSupportMode");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getShutoffSupportMode() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getShutoffSupportMode");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getShutoffSupportMode(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = pointer.value;
        DeviceLibraryLog.byeBye(" getShutoffSupportMode,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValueList<int> valueList})
      getShutoffSupportModeValueList() => (
            errorCode: DeviceErrorCode.devNoError,
            valueList: const UserSettingItemValueList(
              0,
              13,
              [
                DisplayValue(0, "OFF"),
                DisplayValue(1, "1 h"),
                DisplayValue(2, "2 h"),
                DisplayValue(3, "3 h"),
                DisplayValue(4, "4 h"),
                DisplayValue(5, "5 h"),
                DisplayValue(6, "6 h"),
                DisplayValue(7, "7 h"),
                DisplayValue(8, "8 h"),
                DisplayValue(9, "9 h"),
                DisplayValue(10, "10 h"),
                DisplayValue(11, "11 h"),
                DisplayValue(12, "12 h"),
              ],
            )
          );

  ///
  /// スクリーンセーバー
  ///
  /// スクリーンセーバー画面を起動する時間と、表示する画像を設定する。
  ///
  /// [value] : 0(OFF) , 1~60(ON)
  /// **(初期値:5、単位：1min)**
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @override
  DeviceErrorCode saveScreenSaverTime(int value) {
    DeviceLibraryLog.hello("saveScreenSaverTime,$value");
    final errIndex = _bindings.saveScreenSaverTime(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("saveScreenSaverTime");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getScreenSaverTime() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getScreenSaverTime");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getScreenSaverTime(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = pointer.value;
        DeviceLibraryLog.byeBye(
            " getScreenSaverTime,error:$errCode,value:$value");
        return (errorCode: errCode, value: value);
      });

  ///
  /// スクリーンセーバー画面を起動起動後どの位置の画像を使用するか
  ///
  /// [value] : 0(デフォルト) , 0xFF(カスタム)
  /// **(初期値:0)**
  ///
  ///
  @override
  DeviceErrorCode saveSettingsScreenSaverType(ScreenSaverType value) {
    DeviceLibraryLog.hello("saveSettingsScreenSaverType,$value");
    final settingData = getMockData();
    settingData["screenSaverType"] = value.index;

    DeviceLibraryLog.byeBye("saveSettingsScreenSaverType");
    return DeviceErrorCode.devNoError;
  }

  @override
  ({DeviceErrorCode errorCode, ScreenSaverType value})
      getSettingsScreenSaverType() {
    DeviceLibraryLog.hello("getSettingsScreenSaverType");
    final value = ScreenSaverType
        .values[(getMockData()["screenSaverType"] ?? 0).clamp(0, 1)];
    DeviceLibraryLog.byeBye(" getSettingsScreenSaverType,$value");
    return (errorCode: DeviceErrorCode.devNoError, value: value);
  }

  ///
  /// マウスポインター
  ///
  /// USBマウスが接続された時の表示形状を３種類から選択できる。
  ///
  /// [value] : 0-2
  /// **(初期値:0、単位：1min)**
  ///
  /// ※バックアップ用に値を保存する。機能の実行はアプリで行う
  ///
  @override
  DeviceErrorCode saveMousePointer(int type) {
    DeviceLibraryLog.hello("saveMousePointer,$type");
    final errIndex = _bindings.saveMousePointer(type);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("saveMousePointer");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getMousePointer() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getMousePointer");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getMousePointer(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        int value = pointer.value;

        if (value < 0 || value >= MouseCursorType.values.length) {
          DeviceLibraryLog.e(
              "invalid MouseCursorType($value), change to default");
          value = MouseCursorType.mouseType1.index;
        }
        DeviceLibraryLog.byeBye(" getMousePointer,$value");
        return (errorCode: errCode, value: value);
      });

  ///
  ///Needle Position関数を取得する
  ///
  @override
  DeviceErrorCode setNeedlePositionStitchPlacement(bool value) {
    DeviceLibraryLog.hello("setNeedlePositionStitchPlacement");
    final errIndex = _bindings.setNeedlePositionStitchPlacement(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setNeedlePositionStitchPlacement");
    return errCode;
  }

  @override
  ({
    DeviceErrorCode errorCode,
    bool value
  }) getNeedlePositionStitchPlacement() => ffi.using((arena) {
        DeviceLibraryLog.hello("getNeedlePositionStitchPlacement");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getNeedlePositionStitchPlacement(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye("getNeedlePositionStitchPlacement,$value");
        return (errorCode: errCode, value: value);
      });
  @override
  ({DeviceErrorCode errorCode, bool value})
      getNeedlePositionStitchPlacementDefault() => ffi.using((arena) {
            DeviceLibraryLog.hello("getNeedlePositionStitchPlacementDefault");
            final pointer = arena.call<dart_ffi.Bool>();
            final errIndex =
                _bindings.getNeedlePositionStitchPlacementDefault(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
            final bool value = pointer.value;
            DeviceLibraryLog.byeBye(
                "getNeedlePositionStitchPlacementDefault,$value");
            return (errorCode: errCode, value: value);
          });

  ///
  ///糸通しをするとONに戻る
  /// isDisabled == true : キャンセルしない
  /// isDisabled == false : キャンセルする
  ///
  @override
  DeviceErrorCode setUpperAndBobbinThreadSensor(bool isDisabled) {
    DeviceLibraryLog.hello("setUpperAndBobbinThreadSensor $isDisabled");
    final errIndex = _bindings.setUpperAndBobbinThreadSensor(!isDisabled);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setUpperAndBobbinThreadSensor");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, bool isDisabled})
      getUpperAndBobbinThreadSensor() => ffi.using((arena) {
            DeviceLibraryLog.hello("getUpperAndBobbinThreadSensor");
            final pointer = arena.call<dart_ffi.Bool>();
            final errIndex = _bindings.getUpperAndBobbinThreadSensor(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
            final bool value = pointer.value;
            DeviceLibraryLog.byeBye("getUpperAndBobbinThreadSensor,$value");
            return (errorCode: errCode, isDisabled: !value);
          });
  @override
  ({
    DeviceErrorCode errorCode,
    bool isDisabled
  }) getUpperAndBobbinThreadSensorDefault() => ffi.using((arena) {
        DeviceLibraryLog.hello("getUpperAndBobbinThreadSensorDefault");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex =
            _bindings.getUpperAndBobbinThreadSensorDefault(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye("getUpperAndBobbinThreadSensorDefault,$value");
        return (errorCode: errCode, isDisabled: value);
      });
//////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ6         /////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////
  @override
  DeviceErrorCode setProjectorBrightnessMinus() {
    DeviceLibraryLog.hello("setProjectorBrightnessMinus");
    final errIndex = _bindings.setProjectorBrightnessMinus();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setProjectorBrightnessMinus");
    return errCode;
  }

  @override
  DeviceErrorCode setProjectorBrightnessPlus() {
    DeviceLibraryLog.hello("setProjectorBrightnessPlus");
    final errIndex = _bindings.setProjectorBrightnessPlus();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setProjectorBrightnessPlus");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getProjectorBrightness() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getProjectorBrightness");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getProjectorBrightness(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = _changeProjectorBrightnessToInt(pointer.value);

        DeviceLibraryLog.byeBye("getProjectorBrightness,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValue valueList
  }) getProjectorBrightnessValueList() => ffi.using((arena) {
        DeviceLibraryLog.hello("getProjectorBrightnessValueList");
        final pointer = arena.call<dev_gen.UserSettingItemValue>();
        final int errIndex = _bindings.getProjectorBrightnessValueList(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

        final UserSettingItemValue valueList =
            UserSettingItemValue.fromPointer(pointer);
        DeviceLibraryLog.byeBye("getProjectorBrightnessValueList");
        return (errorCode: errCode, valueList: valueList);
      });

  ///
  /// int に変換します
  ///
  int _changeProjectorBrightnessToInt(int value) => value;

  @override
  DeviceErrorCode setProjectorBackbroundColor(DeviceProjectorColor color) {
    DeviceLibraryLog.hello("setProjectorBackbroundColor,$color");
    final int index = DeviceProjectorColor.toFfiApiIntValue(color);
    final errIndex = _bindings.setProjectorBackbroundColor(index);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setProjectorBackbroundColor");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, DeviceProjectorColor projectorColor})
      getProjectorBackbroundColor() => ffi.using((arena) {
            DeviceLibraryLog.hello("getProjectorBackbroundColor");
            final value = arena.call<dart_ffi.Int8>();
            int errIndex = _bindings.getProjectorBackbroundColor(value);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
            final DeviceProjectorColor color =
                DeviceProjectorColor.fromFfiApiIntValue(value.value);
            DeviceLibraryLog.byeBye(" getProjectorBackbroundColor | $color");
            return (errorCode: errCode, projectorColor: color);
          });

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getProjectorBackbroundColorValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getProjectorBackbroundColorValueList");
            final pointer = arena.call<dev_gen.UserSettingItemValue>();
            final int errIndex =
                _bindings.getProjectorBackbroundColorValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValue valueList =
                UserSettingItemValue.fromPointer(pointer);
            DeviceLibraryLog.byeBye("getProjectorBackbroundColorValueList");
            return (errorCode: errCode, valueList: valueList);
          });

  ///
  /// プロジェクターのアウトライン設定する
  ///
  /// 引数 :
  /// - [ProjectorPatternOutlineState] outline: プロジェクターのアウトライン設定
  /// - [DeviceError] : エラーコード
  ///
  @override
  DeviceErrorCode setProjectorPatternOutline(
      ProjectorPatternOutlineState outline) {
    DeviceLibraryLog.hello("setProjectorPatternOutline");
    final bool isOn = ProjectorPatternOutlineState.toFfiApiBoolValue(outline);
    final errIndex = _bindings.setProjectorPatternOutline(isOn);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setProjectorPatternOutline");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, ProjectorPatternOutlineState outlineState})
      getProjectorPatternOutline() => ffi.using((arena) {
            DeviceLibraryLog.hello("getProjectorPatternOutline");
            final isOn = arena.call<dart_ffi.Bool>();
            final errIndex = _bindings.getProjectorPatternOutline(isOn);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
            final ProjectorPatternOutlineState value =
                ProjectorPatternOutlineState.fromFfiApiBoolValue(isOn.value);
            DeviceLibraryLog.byeBye(" getProjectorPatternOutline,$value");
            return (errorCode: errCode, outlineState: value);
          });

  @override
  ({
    DeviceErrorCode errorCode,
    ProjectorPatternOutlineState outlineState
  }) getProjectorPatternOutlineDefault() => ffi.using((arena) {
        DeviceLibraryLog.hello("getProjectorPatternOutlineDefault");
        final isOn = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getProjectorPatternOutlineDefault(isOn);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final ProjectorPatternOutlineState value =
            ProjectorPatternOutlineState.fromFfiApiBoolValue(isOn.value);
        DeviceLibraryLog.byeBye(" getProjectorPatternOutlineDefault,$value");
        return (errorCode: errCode, outlineState: value);
      });

  @override
  DeviceErrorCode setPointerColor(DeviceProjectorColor color) {
    DeviceLibraryLog.hello("setPointerColor");
    final int index = DeviceProjectorColor.toFfiApiIntValue(color);
    final errIndex = _bindings.setPointerColor(index);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setPointerColor");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, DeviceProjectorColor projectorColor})
      getPointerColor() => ffi.using((arena) {
            DeviceLibraryLog.hello("getPointerColor");
            final value = arena.call<dart_ffi.Int8>();
            int errIndex = _bindings.getPointerColor(value);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
            final deviceProjectorColor =
                DeviceProjectorColor.fromFfiApiIntValue(value.value);
            DeviceLibraryLog.byeBye(" getPointerColor,$deviceProjectorColor");
            return (errorCode: errCode, projectorColor: deviceProjectorColor);
          });

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getPointerColorValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getPointerColorValueList");
            final pointer = arena.call<dev_gen.UserSettingItemValue>();
            final int errIndex = _bindings.getPointerColorValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValue valueList =
                UserSettingItemValue.fromPointer(pointer);
            DeviceLibraryLog.byeBye("getPointerColorValueList");
            return (errorCode: errCode, valueList: valueList);
          });

  @override
  DeviceErrorCode setPointerSharpe(ProjectorPointerShapeType value) {
    DeviceLibraryLog.hello("setPointerSharpe");
    final ffiApiIntValue = ProjectorPointerShapeType.toFfiApiIntValue(value);
    final int errIndex = _bindings.setPointerSharpe(ffiApiIntValue);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setPointerSharpe");
    return errCode;
  }

  @override
  ({
    DeviceErrorCode errorCode,
    ProjectorPointerShapeType pointerShapeType
  }) getPointerSharpe() => ffi.using((arena) {
        DeviceLibraryLog.hello("getPointerSharpe");
        final valuePointer = arena.call<dart_ffi.Int8>();
        final int errIndex = _bindings.getPointerSharpe(valuePointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

        final ProjectorPointerShapeType shape =
            ProjectorPointerShapeType.fromFfiApiIntValue(valuePointer.value);
        DeviceLibraryLog.byeBye(" getPointerSharpe,$shape");
        return (errorCode: errCode, pointerShapeType: shape);
      });

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getPointerSharpeValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getPointerSharpeValueList");

            final pointer = arena.call<dev_gen.UserSettingItemValue>();
            final int errIndex = _bindings.getPointerSharpeValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValue valueList =
                UserSettingItemValue.fromPointer(pointer);

            DeviceLibraryLog.byeBye("getPointerSharpeValueList");
            return (errorCode: errCode, valueList: valueList);
          });

  @override
  DeviceErrorCode doCalibrationAdjustment() {
    DeviceLibraryLog.hello("doCalibrationAdjustment");
    int errIndex = _bindings.doCalibrationAdjustment();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("doCalibrationAdjustment");
    return errCode;
  }

  @override
  DeviceErrorCode doneCalibrationAdjustment() {
    DeviceLibraryLog.hello("doneCalibrationAdjustment");
    int errIndex = _bindings.doneCalibrationAdjustment();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("doneCalibrationAdjustment");
    return errCode;
  }

  @override
  CameraNDPMessage getCameraNDPMessage() {
    DeviceLibraryLog.hello("getCameraNDPMessage");
    final int message = _bindings.getCameraNDPMessage();
    final CameraNDPMessage cameraNDPMessage;
    if (message < 0 || message >= CameraNDPMessage.values.length) {
      DeviceLibraryLog.e(
          "invalid CameraNDPMessage($message), change to default");
      cameraNDPMessage = CameraNDPMessage.NO_CNDP_EM;
    } else {
      cameraNDPMessage = CameraNDPMessage.values[message];
    }

    DeviceLibraryLog.byeBye("getCameraNDPMessage,$cameraNDPMessage");
    return cameraNDPMessage;
  }

  @override
  DeviceErrorCode startCalibrationProjector() {
    DeviceLibraryLog.hello("startCalibrationProjector");
    int errIndex = _bindings.startCalibrationProjector();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("startCalibrationProjector");
    return errCode;
  }

  @override
  DeviceErrorCode retryCalibrationAdjustment() {
    DeviceLibraryLog.hello("retryCalibrationAdjustment");
    int errIndex = _bindings.retryCalibrationAdjustment();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("retryCalibrationAdjustment");
    return errCode;
  }

  @override
  DeviceErrorCode startCalibrationAdjustment() {
    DeviceLibraryLog.hello("startCalibrationAdjustment");
    int errIndex = _bindings.startCalibrationAdjustment();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("startCalibrationAdjustment");
    return errCode;
  }

  @override
  DeviceErrorCode cancelCalibrationAdjustment() {
    DeviceLibraryLog.hello("cancelCalibrationAdjustment");
    int errIndex = _bindings.cancelCalibrationAdjustment();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("cancelCalibrationAdjustment");
    return errCode;
  }

//////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ7         /////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////

  ///
  /// PANEL_API DeviceErrorCode_t getSncState(); →　起動時に一度渡せばよいのでDeviceSettingInfoで取得する
  ///
  @override
  DeviceErrorCode setKitActivationCode(int kitID, int certifyKey) {
    DeviceLibraryLog.hello(
        "setKitActivationCode,kitID:$kitID,certifyKey:$certifyKey");
    int errIndex = _bindings.setKitActivationCode(kitID, certifyKey);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setKitActivationCode");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, DeviceSettingInfo settingInfo})
      getDeviceSettingInfo() => ffi.using((arena) {
            DeviceLibraryLog.hello("getDeviceSettingInfo");
            const int kitNumber = 10;
            final pointer = arena.call<dev_gen.DeviceSettingInfo>();
            int errIndex = _bindings.getDeviceSettingInfo(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
            String productNo = "";
            productNo = pointer.ref.productIdToString();

            List<bool> isKit = [];
            for (var i = 0; i < kitNumber; i++) {
              isKit.add(pointer.ref.isKit[i]);
            }

            String serialNumber = "";
            final stringList = <int>[];
            for (var i = 0; i < 9; i++) {
              stringList.add(pointer.ref.serialNumber[i]);
            }
            serialNumber = String.fromCharCodes(stringList);

            final modeCodeList = <int>[];
            for (var i = 0; i < 11; i++) {
              modeCodeList.add(pointer.ref.modeCode[i]);
            }
            String modeCode = String.fromCharCodes(modeCodeList);

            /// TESTAPIで強制変更
            final int spec;
            if (DeviceLibraryTestApi().modeSpec != null) {
              spec = DeviceLibraryTestApi().modeSpec!;
            } else {
              spec = pointer.ref.Spec;
            }

            final DeviceSettingInfo deviceSettingInfo = DeviceSettingInfo(
              serviceCount: pointer.ref.serviceCount,
              totalCount: pointer.ref.totalCount,
              productNo: productNo,
              panelMajorVer: pointer.ref.panelMajorVer,
              isKit: isKit,
              serialNumber: serialNumber,
              isReplacedSerialNumber: pointer.ref.isReplacedSerialNumber,
              panelMinorVer: pointer.ref.panelMinorVer,
              panelPatchVer: pointer.ref.panelPatchVer,
              spec: spec,
              ModelNo: pointer.ref.ModelNo,
              libPanelMajorVer: pointer.ref.libPanelMajorVer,
              libPanelMinorVer: pointer.ref.libPanelMinorVer,
              libPanelPatchVer: pointer.ref.libPanelPatchVer,
              modeCode: modeCode,
              isProductsJapan: pointer.ref.isProductsJapan,
            );
            _specValue ??= deviceSettingInfo.spec;
            _isProductsJapan ??= deviceSettingInfo.isProductsJapan;
            DeviceLibraryLog.byeBye(" getDeviceSettingInfo,$deviceSettingInfo");
            return (errorCode: errCode, settingInfo: deviceSettingInfo);
          });

//////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ8         /////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////
  ///
  ///枠のサイズ値を保存する
  ///
  @override
  DeviceErrorCode setEmbroideryFrameDisplay(EmbFrameDispType frameSizeIndex) {
    DeviceLibraryLog.hello("setEmbroideryFrameDisplay");
    final errIndex = _bindings.setEmbroideryFrameDisplay(frameSizeIndex.index);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setEmbroideryFrameDisplay");
    return errCode;
  }

  ///
  /// 枠のサイズ値が取得されます
  ///
  @override
  ({
    DeviceErrorCode errorCode,
    EmbFrameDispType frameDispType,
  }) getEmbroideryFrameDisplay() => ffi.using((arena) {
        DeviceLibraryLog.hello("getEmbroideryFrameDisplay");
        final pointer = arena.call<dart_ffi.Int32>();
        final errIndex = _bindings.getEmbroideryFrameDisplay(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        EmbFrameDispType value = _changeIntToEmbroideryFrame(pointer.value);
        DeviceLibraryLog.byeBye(" getEmbroideryFrameDisplay,$value");
        return (
          errorCode: errCode,
          frameDispType: value,
        );
      });

  @override
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<EmbFrameDispType> valueList
  }) getEmbroideryFrameDisplayValueLis() => ffi.using((arena) {
        DeviceLibraryLog.hello("getEmbroideryFrameDisplayValueLis");
        final pointer = arena.call<dev_gen.UserSettingItemValueList>();
        final int errIndex =
            _bindings.getEmbroideryFrameDisplayValueLis(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

        final UserSettingItemValueList<EmbFrameDispType> valueList =
            UserSettingItemValueList.fromPointer(
                pointer, _changeIntToEmbroideryFrame);
        DeviceLibraryLog.byeBye("getEmbroideryFrameDisplayValueLis");
        // Cpp側で確保したメモリを解放 //
        _bindings.UserSettingItemValueListFree(pointer);
        return (errorCode: errCode, valueList: valueList);
      });

  ///
  /// int に変換します
  ///
  EmbFrameDispType _changeIntToEmbroideryFrame(int value) {
    if (value < 0 || value >= EmbFrameDispType.values.length) {
      DeviceLibraryLog.e("invalid EmbFrameSizeType($value), change to default");
      return EmbFrameDispType.embFrameSize_465_297;
    } else {
      return EmbFrameDispType.values[value];
    }
  }

  ///
  /// グリッド値が取得されます
  ///
  @override
  ({DeviceErrorCode errorCode, EmbGridType gridType}) getGrid() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getGrid");
        final pointer = arena.call<dart_ffi.Int32>();
        final errIndex = _bindings.getGrid(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        int gridIndex = pointer.value;
        EmbGridType value = EmbGridType.embGridNot;
        if (gridIndex < 0 || gridIndex >= EmbGridType.values.length) {
          DeviceLibraryLog.e(
              "invalid EmbFrameSizeType($value), change to default");
        } else {
          value = EmbGridType.values[gridIndex];
        }

        DeviceLibraryLog.byeBye(" getGrid,$value");
        return (errorCode: errCode, gridType: value);
      });

  ///
  /// グリッド値が取得されます
  ///
  @override
  DeviceErrorCode saveGrid(EmbGridType embGridDisplayType) {
    DeviceLibraryLog.hello("saveGrid");
    final errIndex = _bindings.saveGrid(embGridDisplayType.index);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("saveGrid");
    return errCode;
  }

  @override
  DeviceErrorCode setMaxEmbroiderySpeed(int maxEmbroiderySpeed) {
    DeviceLibraryLog.hello("setMaxEmbroiderySpeed,$maxEmbroiderySpeed");
    final errIndex = _bindings.setMaxEmbroiderySpeed(maxEmbroiderySpeed);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setMaxEmbroiderySpeed");
    return errCode;
  }

  ///
  /// 刺繍最高回転数設定値が取得されます
  ///
  @override
  ({DeviceErrorCode errorCode, int value}) getMaxEmbroiderySpeed() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getMaxEmbroiderySpeed");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getMaxEmbroiderySpeed(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        int value = _changeMaxEmbroiderySpeedToInt(pointer.value);
        DeviceLibraryLog.byeBye("getMaxEmbroiderySpeed,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<int> valueList
  }) getMaxEmbroiderySpeedValueList() => ffi.using((arena) {
        DeviceLibraryLog.hello("getMaxEmbroiderySpeedValueList");
        final pointer = arena.call<dev_gen.UserSettingItemValueList>();
        final int errIndex = _bindings.getMaxEmbroiderySpeedValueList(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

        final UserSettingItemValueList<int> valueList =
            UserSettingItemValueList.fromPointer(
                pointer, _changeMaxEmbroiderySpeedToInt);
        DeviceLibraryLog.byeBye(
            "getMaxEmbroiderySpeedValueList,value:$valueList");
        // Cpp側で確保したメモリを解放 //
        _bindings.UserSettingItemValueListFree(pointer);
        return (errorCode: errCode, valueList: valueList);
      });

  @override
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<int> valueList
  }) getMaxEmbroiderySpeedValueAllList() => ffi.using(
        (arena) {
          DeviceLibraryLog.hello("getMaxEmbroiderySpeedValueAllList");
          final pointer = arena.call<dev_gen.UserSettingItemValueList>();
          final errIndex = _bindings.getMaxEmbroiderySpeedValueAllList(pointer);
          final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
          final UserSettingItemValueList<int> valueList =
              UserSettingItemValueList.fromPointer(
                  pointer, _changeMaxEmbroiderySpeedToInt);
          DeviceLibraryLog.byeBye(
              "getMaxEmbroiderySpeedValueAllList,$valueList");
           // Cpp側で確保したメモリを解放 //
           _bindings.UserSettingItemValueListFree(pointer);

          return (errorCode: errCode, valueList: valueList);
        },
      );

  int _changeMaxEmbroiderySpeedToInt(int value) => value;

  ///
  /// 刺繍糸調子値が設定する
  ///
  @override
  DeviceErrorCode setEmbroideryTension(int embroideryTension) {
    DeviceLibraryLog.hello("setEmbroideryTension,$embroideryTension");
    final errIndex = _bindings.setEmbroideryTension(embroideryTension);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setEmbroideryTension");
    return errCode;
  }

  ///
  /// 刺繍糸調子設定値が取得されます
  ///
  @override
  ({DeviceErrorCode errorCode, int value}) getEmbroideryTension() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getEmbroideryTension");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getEmbroideryTension(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        int value = _changeEmbroideryTensionToInt(pointer.value);
        DeviceLibraryLog.byeBye(" getEmbroideryTension,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<int> valueList
  }) getEmbroideryTensionValueList() => ffi.using((arena) {
        DeviceLibraryLog.hello("getEmbroideryTensionValueList");
        final pointer = arena.call<dev_gen.UserSettingItemValueList>();
        final int errIndex = _bindings.getEmbroideryTensionValueList(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final UserSettingItemValueList<int> valueList =
            UserSettingItemValueList.fromPointer(
                pointer, _changeEmbroideryTensionToInt);
        DeviceLibraryLog.byeBye(
            "getEmbroideryTensionValueList,value:$valueList");
        // Cpp側で確保したメモリを解放 //
        _bindings.UserSettingItemValueListFree(pointer);
        return (errorCode: errCode, valueList: valueList);
      });

  ///
  /// int に変換します
  ///
  int _changeEmbroideryTensionToInt(int value) => value;

  ///
  /// 刺繍の押え高さが設定する
  ///
  @override
  DeviceErrorCode setEmbroideryFootHeight(int embroideryFootHeight) {
    DeviceLibraryLog.hello("setEmbroideryFootHeight,$embroideryFootHeight");
    final errIndex = _bindings.setEmbroideryFootHeight(embroideryFootHeight);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setEmbroideryFootHeight");
    return errCode;
  }

  ///
  /// 刺繍の押え高さが取得されます
  ///
  @override
  ({DeviceErrorCode errorCode, int value}) getEmbroideryFootHeight() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getEmbroideryFootHeight");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getEmbroideryFootHeight(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = pointer.value;
        DeviceLibraryLog.byeBye(" getEmbroideryFootHeight,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getEmbroideryFootHeightValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getEmbroideryFootHeightValueList");
            final pointer = arena.call<dev_gen.UserSettingItemValue>();
            final int errIndex =
                _bindings.getEmbroideryFootHeightValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValue valueList =
                UserSettingItemValue.fromPointer(pointer);
            DeviceLibraryLog.byeBye("getEmbroideryFootHeightValueList");
            return (errorCode: errCode, valueList: valueList);
          });

  ///
  /// 刺繍時針停止位置（上下）が設定する
  ///
  @override
  DeviceErrorCode setEmbroideryNeedleStopPosition(
      bool embroideryNeedleStopPosition) {
    DeviceLibraryLog.hello(
        "setEmbroideryNeedleStopPosition,$embroideryNeedleStopPosition");
    final errIndex =
        _bindings.setEmbroideryNeedleStopPosition(embroideryNeedleStopPosition);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setEmbroideryNeedleStopPosition");
    return errCode;
  }

  ///
  /// 刺繍時針停止位置（上下）が取得されます
  ///
  @override
  ({DeviceErrorCode errorCode, bool value}) getEmbroideryNeedleStopPosition() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getEmbroideryNeedleStopPosition");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getEmbroideryNeedleStopPosition(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye(" getEmbroideryNeedleStopPosition,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, bool value})
      getEmbroideryNeedleStopPositionDefault() => ffi.using((arena) {
            DeviceLibraryLog.hello("getEmbroideryNeedleStopPositionDefault");
            final pointer = arena.call<dart_ffi.Bool>();
            final errIndex =
                _bindings.getEmbroideryNeedleStopPositionDefault(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
            final bool value = pointer.value;
            DeviceLibraryLog.byeBye(
                " getEmbroideryNeedleStopPositionDefault,$value");
            return (errorCode: errCode, value: value);
          });

  ///
  /// 刺繍の自動押え下げが設定する
  ///
  @override
  DeviceErrorCode setEmbAutoDown(bool embroideryFootAutoDown) {
    DeviceLibraryLog.hello("setEmbAutoDown,$embroideryFootAutoDown");
    final errIndex = _bindings.setEmbAutoDown(embroideryFootAutoDown);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setEmbAutoDown");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, bool value}) getEmbAutoDown() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getEmbAutoDown");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getEmbAutoDown(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye(" getEmbAutoDown,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, bool value}) getEmbAutoDownDefault() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getEmbAutoDownDefault");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getEmbAutoDownDefault(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye(" getEmbAutoDownDefault,$value");
        return (errorCode: errCode, value: value);
      });
//////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ9         /////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////

  /// 表示単位のキャッシュ値
  DisplayUnit? _displayUnit;

  ///
  /// 表示単位が取得されます
  ///
  @override
  ({DeviceErrorCode errorCode, DisplayUnit displayUnit}) getDisplayUnit() =>
      _displayUnit == null
          ? ffi.using((arena) {
              DeviceLibraryLog.hello("getDisplayUnit");
              final pointer = arena.call<dart_ffi.Int8>();
              final errIndex = _bindings.getDisplayUnit(pointer);
              final errCode =
                  DeviceErrorCodeExtension.getValuesByIndex(errIndex);
              final int value = pointer.value;

              if (errCode != DeviceErrorCode.devNoError) {
                _displayUnit = null;
              } else {
                _displayUnit =
                    value == ENUM_LIB.TRUE ? DisplayUnit.mm : DisplayUnit.inch;
              }

              DeviceLibraryLog.byeBye(" getDisplayUnit,$value");
              return (
                errorCode: errCode,
                displayUnit:
                    value == ENUM_LIB.TRUE ? DisplayUnit.mm : DisplayUnit.inch
              );
            })
          : (errorCode: DeviceErrorCode.devNoError, displayUnit: _displayUnit!);

  @override
  DeviceErrorCode saveDisplayUnit(DisplayUnit value) {
    DeviceLibraryLog.hello("saveDisplayUnit, value:$value");
    int errIndex = 0;
    if (value == DisplayUnit.mm) {
      errIndex = _bindings.saveDisplayUnit(true);
    } else {
      errIndex = _bindings.saveDisplayUnit(false);
    }
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

    _displayUnit = errCode != DeviceErrorCode.devNoError ? null : value;
    DeviceLibraryLog.byeBye("saveDisplayUnit");
    return errCode;
  }

  /// 糸色表示設定のキャッシュ値
  bool? _threadColorSetting;

  ///
  /// 糸色表示設定
  ///
  @override
  ({
    DeviceErrorCode errorCode,
    bool value
  }) getThreadColor() => _threadColorSetting == null
      ? ffi.using((arena) {
          DeviceLibraryLog.hello("getThreadColor");
          final pointer = arena.call<dart_ffi.Int8>();
          final errIndex = _bindings.getThreadColor(pointer);
          final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
          int value = pointer.value;

          if (errCode != DeviceErrorCode.devNoError) {
            _threadColorSetting = null;
          } else {
            _threadColorSetting = value != 0 ? true : false;
          }

          DeviceLibraryLog.byeBye(" getThreadColor,$value");
          return (errorCode: errCode, value: value != 0 ? true : false);
        })
      : (errorCode: DeviceErrorCode.devNoError, value: _threadColorSetting!);

  @override
  DeviceErrorCode saveThreadColor(bool value) {
    DeviceLibraryLog.hello("saveThreadColor $value");
    int errIndex = _bindings.saveThreadColor(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    _threadColorSetting = errCode != DeviceErrorCode.devNoError ? null : value;
    DeviceLibraryLog.byeBye(" saveThreadColor");
    return errCode;
  }

  ///
  /// 糸ブランド
  ///
  @override
  ({DeviceErrorCode errorCode, int value}) getThreadBrand() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getThreadBrand");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getThreadBrand(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = _changeThreadBrandToInt(pointer.value);
        DeviceLibraryLog.byeBye(" getThreadBrand,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  DeviceErrorCode setThreadBrand(value) {
    DeviceLibraryLog.hello("setThreadBrand");
    int errIndex = _bindings.setThreadBrand(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" setThreadBrand");
    return errCode;
  }

  @override
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<int> valueList
  }) getThreadBrandValueList() => ffi.using((arena) {
        DeviceLibraryLog.hello("getThreadBrandValueList");
        final pointer = arena.call<dev_gen.UserSettingItemValueList>();
        final int errIndex = _bindings.getThreadBrandValueList(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

        final UserSettingItemValueList<int> valueList =
            UserSettingItemValueList.fromPointer(
                pointer, _changeThreadBrandToInt);
        DeviceLibraryLog.byeBye("getThreadBrandValueList,valueList:$valueList");
        // Cpp側で確保したメモリを解放 //
        _bindings.UserSettingItemValueListFree(pointer);
        return (errorCode: errCode, valueList: valueList);
      });

  ///
  /// int に変換します
  ///
  int _changeThreadBrandToInt(int value) => value;

  ///
  /// 背景色
  ///
  @override
  ({DeviceErrorCode errorCode, int value}) getEmbroideryBackgroundColor() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getEmbroideryBackgroundColor");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getEmbroideryBackgroundColor(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = pointer.value;
        DeviceLibraryLog.byeBye(" getEmbroideryBackgroundColor,>>>$value<<<");
        return (errorCode: errCode, value: value);
      });

  @override
  DeviceErrorCode saveEmbroideryBackgroundColor(int value) {
    DeviceLibraryLog.hello("saveEmbroideryBackgroundColor, >>>> $value <<<  ");
    int errIndex = _bindings.saveEmbroideryBackgroundColor(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" saveEmbroideryBackgroundColor");
    return errCode;
  }

  ///
  /// サムネイル色
  ///
  @override
  ({DeviceErrorCode errorCode, int value}) getThumbnailBackgroundColor() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getThumbnailBackgroundColor");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getThumbnailBackgroundColor(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value;
        if (pointer.value >= settingColorsTableMax || pointer.value < 0) {
          value = 0;
          DeviceLibraryLog.e("getThumbnailBackgroundColor Error[0]");
        } else {
          value = pointer.value;
        }

        DeviceLibraryLog.byeBye(
            " getThumbnailBackgroundColor,>>> $value <<<   ");
        return (errorCode: errCode, value: value);
      });

  @override
  DeviceErrorCode saveThumbnailBackgroundColor(int value) {
    DeviceLibraryLog.hello("saveThumbnailBackgroundColor,>>> $value <<< ");
    int errIndex = _bindings.saveThumbnailBackgroundColor(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" saveThumbnailBackgroundColor");
    return errCode;
  }

  ///
  /// サムネイルサイズ設定
  ///
  @override
  ({DeviceErrorCode errorCode, ThumbnailSizeType value}) getThumbnailSize() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getThumbnailSize");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getThumbnailSize(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final ThumbnailSizeType value =
            ThumbnailSizeType.formFFIApiIndex(pointer.value);
        DeviceLibraryLog.byeBye(
            " getThumbnailSize,>>> $value ${pointer.value}<<< ");
        return (errorCode: errCode, value: value);
      });

  @override
  DeviceErrorCode saveThumbnailSize(ThumbnailSizeType value) {
    DeviceLibraryLog.hello("saveThumbnailSize,>>> $value <<< ");
    int errIndex = _bindings.saveThumbnailSize(value.index);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" saveThumbnailSize");
    return errCode;
  }

  ///
  /// サムネイルサイズ設定
  ///
  /// 新規追加
  ///
  @override
  ({
    DeviceErrorCode errorCode,
    UserSettingItemValueList<ThumbnailSizeType> valueList
  }) getThumbnailSizeLis() => (
        errorCode: DeviceErrorCode.devNoError,
        valueList: const UserSettingItemValueList(
          ThumbnailSizeType.M,
          3,
          [
            DisplayValue(ThumbnailSizeType.L, "Large"),
            DisplayValue(ThumbnailSizeType.M, "Mid"),
            DisplayValue(ThumbnailSizeType.S, "Small"),
          ],
        )
      );

  @override
  ({DeviceErrorCode errorCode, int value}) getEmbroideryBastingDistance() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getEmbroideryBastingDistance");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getEmbroideryBastingDistance(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = pointer.value;
        DeviceLibraryLog.byeBye(" getEmbroideryBastingDistance,>>> $value <<<");
        return (errorCode: errCode, value: value);
      });

  @override
  DeviceErrorCode setEmbroideryBastingDistance(int value) {
    DeviceLibraryLog.hello("setEmbroideryBastingDistance,>>> $value <<<");
    int errIndex = _bindings.setEmbroideryBastingDistance(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" setEmbroideryBastingDistance");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, UserSettingItemValue valueList})
      getEmbroideryBastingDistanceValueList() => ffi.using((arena) {
            DeviceLibraryLog.hello("getEmbroideryBastingDistanceValueList");
            final pointer = arena.call<dev_gen.UserSettingItemValue>();
            final int errIndex =
                _bindings.getEmbroideryBastingDistanceValueList(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

            final UserSettingItemValue valueList =
                UserSettingItemValue.fromPointer(pointer);
            DeviceLibraryLog.byeBye(
                "getEmbroideryBastingDistanceValueList,>>> $valueList <<<");
            return (errorCode: errCode, valueList: valueList);
          });

/////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ10         /////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////
  @override
  ({DeviceErrorCode errorCode, ScanQualityType value}) getScanQuality() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getScanQuality");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getScanQuality(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final int value = pointer.value;
        DeviceLibraryLog.byeBye(" getScanQuality,>>> $value <<<");
        return (
          errorCode: errCode,
          value: value == 1 ? ScanQualityType.fine : ScanQualityType.standard
        );
      });

  @override
  DeviceErrorCode setScanQuality(ScanQualityType value) {
    DeviceLibraryLog.hello("setScanQuality,>>> $value <<<");
    int errIndex =
        _bindings.setScanQuality(value == ScanQualityType.fine ? true : false);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" setScanQuality");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, ScanQualityType value})
      getScanQualityDefault() => ffi.using((arena) {
            DeviceLibraryLog.hello("getScanQualityDefault");
            final pointer = arena.call<dart_ffi.Bool>();
            final errIndex = _bindings.getScanQualityDefault(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
            final bool value = pointer.value;
            DeviceLibraryLog.byeBye(" getScanQualityDefault,,>>> $value <<<");
            return (
              errorCode: errCode,
              value: value ? ScanQualityType.fine : ScanQualityType.standard
            );
          });

  @override
  ({DeviceErrorCode errorCode, bool value}) getFabricThicknessSensor() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getFabricThicknessSensor");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getFabricThicknessSensor(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye(" getFabricThicknessSensor,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  DeviceErrorCode setFabricThicknessSensor(value) {
    DeviceLibraryLog.hello("setFabricThicknessSensor");
    int errIndex = _bindings.setFabricThicknessSensor(value);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" setFabricThicknessSensor");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, bool value}) getFabricThicknessSensorDefault() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getFabricThicknessSensorDefault");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getFabricThicknessSensorDefault(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye(" getFabricThicknessSensorDefault,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  DeviceErrorCode startLedPtSetting() {
    DeviceLibraryLog.hello("startLedPtSetting");
    int errIndex = _bindings.startLedPtSetting();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" startLedPtSetting");
    return errCode;
  }

  @override
  DeviceErrorCode closeLedPtSetting() {
    DeviceLibraryLog.hello("closeLedPtSetting");
    int errIndex = _bindings.closeLedPtSetting();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" closeLedPtSetting");
    return errCode;
  }

  @override
  DeviceErrorCode setLedPtHeightMinus() {
    DeviceLibraryLog.hello("setLedPtHeightMinus");
    int errIndex = _bindings.setLedPtHeightMinus();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" setLedPtHeightMinus");
    return errCode;
  }

  @override
  DeviceErrorCode setLedPtHeightPlus() {
    DeviceLibraryLog.hello("setLedPtHeightPlus");
    int errIndex = _bindings.setLedPtHeightPlus();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" setLedPtHeightPlus");
    return errCode;
  }

  @override
  DeviceErrorCode setLedBrightnessMinus() {
    DeviceLibraryLog.hello("setLedBrightnessMinus");
    int errIndex = _bindings.setLedBrightnessMinus();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" setLedBrightnessMinus");
    return errCode;
  }

  @override
  DeviceErrorCode setLedPtBrightnessPlus() {
    DeviceLibraryLog.hello("setLedPtBrightnessPlus");
    int errIndex = _bindings.setLedPtBrightnessPlus();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye(" setLedPtBrightnessPlus");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getLedPtBrightnessPlus() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getLedPtBrightnessPlus");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getLedPtBrightnessPlus(pointer);
        final value = pointer.value;
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        DeviceLibraryLog.byeBye(" getLedPtBrightnessPlus $value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, int value}) getLedPtBrightnessPlusDefault() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getLedPtBrightnessPlusDefault");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getLedPtBrightnessPlusDefault(pointer);
        final value = pointer.value;
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        DeviceLibraryLog.byeBye(" getLedPtBrightnessPlusDefault $value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, int value}) getLedPtHeightPlus() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getLedPtHeightPlus");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getLedPtHeightPlus(pointer);
        final value = pointer.value;
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        DeviceLibraryLog.byeBye(" getLedPtHeightPlus $value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, int value}) getLedPtHeightPlusDefault() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getLedPtHeightPlusDefault");
        final pointer = arena.call<dart_ffi.Int8>();
        final errIndex = _bindings.getLedPtHeightPlusDefault(pointer);
        final value = pointer.value;
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        DeviceLibraryLog.byeBye(" getLedPtHeightPlusDefault $value");
        return (errorCode: errCode, value: value);
      });

  @override
  ({DeviceErrorCode errorCode, bool value}) getWPlusPFRegistrationStatus() =>
      (errorCode: DeviceErrorCode.devNoError, value: false);

  @override
  DeviceErrorCode clearWPlusPFRegistrationStatus() =>
      DeviceErrorCode.devNoError;

/////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ11         /////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////
  @override
  DeviceErrorCode setMachineName(String value) {
    DeviceLibraryLog.hello("setMachineName");
    final data = getMockData();
    data["machineName"] = value;
    // final machineName = value.toNativeUtf8();
    // final errIndex = _bindings.setMachineName(
    //   machineName.cast<dart_ffi.Char>(),
    // );
    // final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    // ffi.calloc.free(machineName);
    DeviceLibraryLog.byeBye("setMachineName");
    return DeviceErrorCode.devNoError;
  }

  @override
  ({DeviceErrorCode errorCode, String machineName}) getMachineName() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getMachineName");

        final data = getMockData();
        final machineNameString = data["machineName"];

        // String machineNameString = "";
        // final pointer = machineNameString.toNativeUtf8();
        // final errIndex =
        //     _bindings.getMachineName(pointer.cast<dart_ffi.Char>());
        // final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        // machineNameString = pointer.toString();
        DeviceLibraryLog.byeBye(" getMachineName");
        return (
          errorCode: DeviceErrorCode.devNoError,
          machineName: machineNameString,
        );
      });

  @override
  Future<({DeviceErrorCode errorCode, String version})> getApkVersion() async {
    DeviceLibraryLog.hello("getApkVersion");

    final currentAppPackageName = await SystemConfig.getCurrentAppPackageName();
    if (currentAppPackageName == null) {
      return (errorCode: DeviceErrorCode.devNoError, version: "unknownApp");
    }
    return (
      errorCode: DeviceErrorCode.devNoError,
      version:
          await SystemConfig.getApplicationVersionName(currentAppPackageName) ??
              "unknownVersion"
    );
  }

  @override
  ({DeviceErrorCode errorCode, String value}) getUpgradeVersion() {
    DeviceLibraryLog.hello("getUpgradeVersion");

    String value = getMockData()["upgradeVersion"] ?? "1.00";
    DeviceLibraryLog.byeBye("getUpgradeVersion,$value");
    return (errorCode: DeviceErrorCode.devNoError, value: value);
  }

  @override
  DeviceErrorCode setUpgradeVersion(String upgradeVersion) {
    var tempJsonData = getMockData();
    tempJsonData["upgradeVersion"] = upgradeVersion;
    setMockData(tempJsonData);
    return DeviceErrorCode.devNoError;
  }

/////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ12         /////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////          ページ13         /////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////アプリケーション側が管理する開発またはデバッグ支援機能////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////
  ///
  /// デバッグ情報表示有無
  ///
  /// デバッグが有効になっている場合に、ログ表示を有効にする。
  ///
  /// 戻る値は
  /// - true  :有効
  /// - false :無効
  ///
  @override
  ({DeviceErrorCode errorCode, bool value}) isDebugInfoEnabled() {
    DeviceLibraryLog.d("isDebugInfoEnabled");
    try {
      bool value = getMockData()['isDebugInfoEnabled'] ?? false;
      return (errorCode: DeviceErrorCode.devNoError, value: value);
    } catch (e) {
      DeviceLibraryLog.byeBye("isDebugInfoEnabled,error occur");
      return (errorCode: DeviceErrorCode.devNoError, value: false);
    }
  }

  @override
  ({DeviceErrorCode errorCode, int value}) getSettingScreenShot() {
    DeviceLibraryLog.hello("getSettingScreenShot");

    try {
      int value = getMockData()['settingScreenShot'] ?? 0;
      DeviceLibraryLog.byeBye("getSettingScreenShot,$value");
      return (errorCode: DeviceErrorCode.devNoError, value: value);
    } catch (e) {
      DeviceLibraryLog.byeBye(
          "getSettingScreenShot,error occur default return");
      return (errorCode: DeviceErrorCode.devNoError, value: 0);
    }
  }

  @override
  DeviceErrorCode setSettingScreenShot(int level) {
    DeviceLibraryLog.hello("setSettingScreenShot");
    var tempJsonData = getMockData();
    tempJsonData["settingScreenShot"] = level;
    setMockData(tempJsonData);
    DeviceLibraryLog.byeBye("setSettingScreenShot,$level");
    return DeviceErrorCode.devNoError;
  }

  @override
  ({DeviceErrorCode errorCode, bool value}) isAndroidNeedSSSS() {
    DeviceLibraryLog.hello("isAndroidNeedSSSS");
    bool value = getMockData()['isAndroidNeedSSSS'] ?? false;
    DeviceLibraryLog.byeBye("isAndroidNeedSSSS,$value");
    return (errorCode: DeviceErrorCode.devNoError, value: value);
  }

////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////アプリケーション側が管理する開発またはデバッグ支援機能//////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////
  @override
  ({
    DeviceErrorCode errorCode,
    CommonUserSettingInfo settingInfo
  }) getCommonUserSettingInfo() => ffi.using((arena) {
        DeviceLibraryLog.hello("getCommonUserSettingInfo");
        final value = arena.call<dev_gen.CommonUserSettingInfo>();
        final errIndex = _bindings.getCommonUserSettingInfo(value);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final CommonUserSettingInfo commonUserSettingInfo =
            CommonUserSettingInfo(
          ecoMode: value.ref.ecoMode,
          light: value.ref.light,
          screenDisplayBrightness: value.ref.screenDisplayBrightness,
          isUpperAndBobbinThreadSensor: value.ref.isUpperAndBobbinThreadSensor,
          machineSpeakerVolume: value.ref.machineSpeakerVolume,
          isNeedlePositionStitchPlacement:
              value.ref.isNeedlePositionStitchPlacement,
          initialScreen: value.ref.initialScreen,
          shutoffSupportMode: value.ref.shutoffSupportMode,
          screenSaverTime: value.ref.screenSaverTime,
          screenSaverType: value.ref.screenSaverType,
          mousePointer: value.ref.mousePointer,
          projectorBrightness: value.ref.projectorBrightness,
          projectorBackgroundColor: value.ref.projectorBackbroundColor,
          isProjectorPatternOutline: value.ref.isProjectorPatternOutline,
          pointerColor: value.ref.pointerColor,
          pointerSharpe: value.ref.pointerColor,
          language: value.ref.language,
        );
        DeviceLibraryLog.byeBye(" getCommonUserSettingInfo");
        return (errorCode: errCode, settingInfo: commonUserSettingInfo);
      });

/////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////

  ///
  /// TODO
  ///
  @override
  ModelStatusInfo getModelStatusInfo() => const ModelStatusInfo(
        embServiceStitchCount: 0,
        embTotalStitchCount: 0,
        utlServiceStitchCount: 0,
        utlTotalStitchCount: 0,
        serviceStitchCount: 0,
        totalStitchCount: 0,
        totalThreadCount: 0,
      );

  @override
  List<ErrorListInfo> getUserLogErrorList() => const [
        ErrorListInfo(errorCode: 0, errorType: 0),
        ErrorListInfo(errorCode: 1, errorType: 1)
      ];

  @override
  void resetCommonUserSetting() {
    DeviceLibraryLog.hello("resetCommonUserSetting");
    _bindings.resetCommonUserSetting();
    DeviceLibraryLog.byeBye("resetCommonUserSetting");
  }

  @override
  void resetEmbUserSetting() {
    DeviceLibraryLog.hello("resetEmbUserSetting");
    _bindings.resetEmbUserSetting();
    DeviceLibraryLog.byeBye("resetEmbUserSetting");
  }

  @override
  void resetUtlUserSetting() {
    DeviceLibraryLog.hello("resetUtlUserSetting");
    _bindings.resetUtlUserSetting();
    DeviceLibraryLog.byeBye("resetUtlUserSetting");
  }

  @override
  void resetAllUserSetting() {
    DeviceLibraryLog.hello("resetAllUserSetting");
    _bindings.resetAllUserSetting();
    DeviceLibraryLog.byeBye("resetAllUserSetting");
  }

  @override
  DeviceErrorCode startAllUserSetting() {
    DeviceLibraryLog.hello("startAllUserSetting");
    final errIndex = _bindings.startAllUserSetting();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("startAllUserSetting");
    return errCode;
  }

  @override
  DeviceErrorCode finishAllUserSetting() {
    DeviceLibraryLog.hello("finishAllUserSetting");
    final errIndex = _bindings.finishAllUserSetting();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("finishAllUserSetting");
    return errCode;
  }
  ///////////////////////////////////////////////////////////////////////////////////////////////////
  ///////////////////////////////////////////////////////////////////////////////////////////////////
  ///////////////////////////////////////////////////////////////////////////////////////////////////

  // TODO 刺しゅうユニットのチェックがないので、いったん追加する
  @override
  FrameSize getEmbFrameType() =>
      TpdLibrary().apiBinding.bPIFGetAppDisplayEmb().embSetting.ref.frameSize;

  @override
  bool isFrameFixLeverLock() =>
      TpdLibrary().apiBinding.bpIFGetAppDisplayGlobal().isFrameHoldLeverDown;

  ///////////////////////////////////////////////////////////////////////////////////////////////////
  ///////////////////////////////////////////////////////////////////////////////////////////////////
  ///////////////////////////////////////////////////////////////////////////////////////////////////

  @override
  ({
    DeviceErrorCode errorCode,
    EmbUserSettingInfo settingInfo
  }) getEmbUserSettingInfo() => ffi.using((arena) {
        DeviceLibraryLog.hello("getCommonUserSettingInfo");
        final value = arena.call<dev_gen.EmbUserSettingInfo>();
        final errIndex = _bindings.getEmbUserSettingInfo(value);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final EmbUserSettingInfo userSettingInfo = EmbUserSettingInfo(
          embroideryFrameDisplay: value.ref.embroideryFrameDisplay,
          grid: value.ref.grid,
          maxEmbroiderySpeed: value.ref.maxEmbroiderySpeed,
          embroideryTension: value.ref.embroideryTension,
          embroideryFootHeight: value.ref.embroideryFootHeight,
          embroideryNeedleStopPosition: value.ref.embroideryNeedleStopPosition,
          isAutoDown: value.ref.isAutoDown,
          displayUnit: value.ref.displayUnit,
          threadColor: value.ref.threadColor,
          threadBrand: value.ref.threadBrand,
          embroideryBackgroundColor: value.ref.embroideryBackgroundColor,
          thumbnailBackgroundColor: value.ref.thumbnailBackgroundColor,
          thumbnailSize: value.ref.thumbnailSize,
          embroideryBastingDistance: value.ref.embroideryBastingDistance,
          scanQuality: value.ref.scanQuality,
          fabricThicknessSensor: value.ref.fabricThicknessSensor,
          isScanBackImgView: value.ref.isScanBackImgView,
        );
        DeviceLibraryLog.byeBye("getEmbUserSettingInfo");
        return (errorCode: errCode, settingInfo: userSettingInfo);
      });

  ///
  /// TODO: LIB未実装
  ///
  @override
  ModelSoftVersionInfo getModelSoftVersionInfo() => ModelSoftVersionInfo(
        /// serial number ※9桁 (バッファに余裕を持たせている)
        serialNumber: ["s", "e", "r", "i", "a", "l", "_", "0", "0"],

        /// serial number置き換えの有無 : 0=なし, 1=あり(おかしなコードがあり'?'に置き換えた)
        isReplacedSerialNumber: 0,

        // PRODUCT ID ※10桁 (バッファに余裕を持たせている)
        productId: Uint8List.fromList([0, 1, 2, 3, 4, 5, 6, 7, 8, 9]),

        /// パネル プログラム Ver.
        panelVersion: 123,

        /// パネル プログラム Sub Ver.の有無 : 0=なし, 1=あり
        isPanelSubVersion: 0,

        /// パネル プログラム Sub Ver.
        mainVersion: 0,

        /// メイン プログラム Ver.
        panelSubVersion: 123,

        /// BOOT プログラム Ver.
        bootVersion: 123,

        /// マスター Ver.
        masterVersion: 123,

        /// FPGA Ver.
        fpgaVersion: 123,

        /// XY_CPU Ver.
        xyCpuVersion: 123,

        /// BOARD TYPE : 0=TYPE-A, 1=TYPE-B, 2=TYPE-C, 3=TYPE-D ※brother_panel側 SOC_TYPE_Eを参照
        boardType: 0,
      );

  @override
  Uint8List embMdcConvertFCM2PCE(Uint8List fcmData, String contentType) =>
      Uint8List(0);

  @override
  bool embMdcStampSelectSaveSNCStamp(Uint8List pceData, String contentType) =>
      true;

  @override
  Uint8List embMakePhxFile() => Uint8List(0);

  ///
  /// カメラペンUI ON/OFF設定取得するAPI
  ///
  /// 戻り値   :
  /// - [DeviceErrorCode ] errorCode:エラーコード
  /// - [CameraUISetting] cameraUISetting: カメラペンUI設定値
  ///
  @override
  DeviceErrorCode saveEmbCameraUIProjectionSetting(CameraUISetting value) {
    DeviceLibraryLog.hello("saveEmbCameraUIProjectionSetting,$value");
    final errIndex = _bindings.saveEmbCameraUIProjectionSetting(value.index);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("saveEmbCameraUIProjectionSetting");
    return errCode;
  }

  @override
  ({
    DeviceErrorCode errorCode,
    CameraUiArrangeState cameraUiArrangeState
  }) getCameraPenUIArrangeState() => ffi.using((arena) {
        DeviceLibraryLog.hello("getCameraPenUIArrangeState");
        final pointer = arena.call<dart_ffi.Int>();
        final errIndex = _bindings.getCameraPenUIArrangeState(pointer);
        final value = CameraUiArrangeState.fromFfiApiIntValue(pointer.value);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        DeviceLibraryLog.byeBye("getCameraPenUIArrangeState,$value");
        return (errorCode: errCode, cameraUiArrangeState: value);
      });

  @override
  DeviceErrorCode saveEmbCameraUIArrangeStateSetting(
      CameraUiArrangeState value) {
    DeviceLibraryLog.hello("saveEmbCameraUIArrangeStateSetting,$value");
    final errIndex = _bindings.saveEmbCameraUIArrangeStateSetting(
        CameraUiArrangeState.toFfiApiIntValue(value));
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("saveEmbCameraUIArrangeStateSetting");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, CameraUISetting value})
      getEmbCameraUIProjectionSetting() => ffi.using((arena) {
            DeviceLibraryLog.hello("getEmbCameraUIProjectionSetting");
            final pointer = arena.call<dart_ffi.Int8>();
            final errIndex = _bindings.getEmbCameraUIProjectionSetting(pointer);
            final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
            final CameraUISetting value = CameraUISetting.values[pointer.value];
            DeviceLibraryLog.byeBye("getEmbCameraUIProjectionSetting,$value");
            return (errorCode: errCode, value: value);
          });

  @override
  ({DeviceErrorCode errorCode, CameraPenAlignType cameraPenAlignType})
      getCameraPenUIAlignSetting() {
    final ret = getCameraPenUIArrangeState();
    if (ret.errorCode != DeviceErrorCode.devNoError) {
      return (
        errorCode: ret.errorCode,
        cameraPenAlignType: CameraPenAlignType.bottom,
      );
    }

    return (
      errorCode: ret.errorCode,
      cameraPenAlignType: ret.cameraUiArrangeState.toCameraPenAlignType()
    );
  }

  @override
  DeviceErrorCode saveEmbCameraUIAlignSetting(CameraPenAlignType value) {
    final ret = getCameraPenUIMinimumSetting();
    if (ret.errorCode != DeviceErrorCode.devNoError) {
      return ret.errorCode;
    }
    final cameraUiArrangeState =
        CameraUiArrangeState.toCameraUiArrangeState(value, ret.isMinimum);
    final deviceErrorCode =
        saveEmbCameraUIArrangeStateSetting(cameraUiArrangeState);
    return deviceErrorCode;
  }

  @override
  ({DeviceErrorCode errorCode, bool isMinimum}) getCameraPenUIMinimumSetting() {
    final ret = getCameraPenUIArrangeState();
    if (ret.errorCode != DeviceErrorCode.devNoError) {
      return (
        errorCode: ret.errorCode,
        isMinimum: false,
      );
    }

    return (
      errorCode: ret.errorCode,
      isMinimum: ret.cameraUiArrangeState.toMinimum(),
    );
  }

  @override
  DeviceErrorCode saveEmbCameraUIMinimumSetting(bool isMinimum) {
    final ret = getCameraPenUIAlignSetting();
    if (ret.errorCode != DeviceErrorCode.devNoError) {
      return ret.errorCode;
    }
    final cameraUiArrangeState = CameraUiArrangeState.toCameraUiArrangeState(
        ret.cameraPenAlignType, isMinimum);
    final deviceErrorCode =
        saveEmbCameraUIArrangeStateSetting(cameraUiArrangeState);
    return deviceErrorCode;
  }

  @override
  UserSettingEnabledInfo getUserSettingEnableInfo() => ffi.using((arena) {
        final value = arena.call<dev_gen.UserSettingEnabledInfo>();
        _bindings.getUserSettingEnableInfo(value);
        final UserSettingEnabledInfo userSettingEnabledInfo =
            UserSettingEnabledInfo(
          heelSwitch: value.ref.heelSwitch,
          sidePedal: value.ref.sidePedal,
          presserFootHeight: value.ref.presserFootHeight,
          presserFootPressure: value.ref.presserFootPressure,
          baseLine: value.ref.baseLine,
          ledStart: value.ref.ledStart,
        );
        return userSettingEnabledInfo;
      });

  @override
  ({
    DeviceErrorCode errorCode,
    UtlUserSettingInfo settingInfo
  }) getUtlUserSettingInfo() => ffi.using((arena) {
        DeviceLibraryLog.hello("getUtlUserSettingInfo");
        final value = arena.call<dev_gen.UtlUserSettingInfo>();
        final errIndex = _bindings.getUtlUserSettingInfo(value);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final UtlUserSettingInfo userSettingInfo = UtlUserSettingInfo(
          isWidthControl: value.ref.isWidthControl,
          fineAdjustHorizValue: value.ref.fineAdjustHorizValue,
          presserFootHeightValue: value.ref.presserFootHeightValue,
          presserFootPressureValue: value.ref.presserFootHeightValue,
          isAutomaticFabricSensorSystem:
              value.ref.isAutomaticFabricSensorSystem,
          isBaseLine: value.ref.isBaseLine,
          pivotingHeightValue: value.ref.pivotingHeightValue,
          freeMotionFootHeighValue: value.ref.freeMotionFootHeighValue,
          dualFeedFeedAdjustmentValue: value.ref.dualFeedFeedAdjustmentValue,
          isAutoDown: value.ref.isAutoDown,
          isPressToTrim: value.ref.isPressToTrim,
          isInitialStitchPage: value.ref.isInitialStitchPage,
          isReinforcementPriority: value.ref.isReinforcementPriority,
          heelSwitchValue: value.ref.heelSwitchValue,
          sidePedalValue: value.ref.sidePedalVlue,
          isEndPointSettingTemporaryStop:
              value.ref.isEndPointSettingTemporaryStop,
        );
        DeviceLibraryLog.byeBye(" getUtlUserSettingInfo, $userSettingInfo  ");
        return (errorCode: errCode, settingInfo: userSettingInfo);
      });

  @override
  bool getClockDisplayStatus() {
    DeviceLibraryLog.hello("getClockDisplayStatus");

    bool value = getMockData()["clockDisplayStatus"] ?? false;
    DeviceLibraryLog.byeBye("getClockDisplayStatus,$value");
    return value;
  }

  @override
  void setClockDisplayStatus(bool value) {
    DeviceLibraryLog.hello("setClockDisplayStatus,$value");
    var tempJsonData = getMockData();
    tempJsonData["clockDisplayStatus"] = value;
    setMockData(tempJsonData);
    DeviceLibraryLog.byeBye("setClockDisplayStatus");
  }

  @override
  int getShowTimeTypeIndex() {
    DeviceLibraryLog.hello("getShowTimeTypeIndex");
    int value = getMockData()["showTimeTypeIndex"] ?? ShowTimeType.hour.index;
    if (value > ShowTimeType.hour.index || value < ShowTimeType.am.index) {
      value = ShowTimeType.hour.index;
    } else {
      /// Do Nothing
    }
    DeviceLibraryLog.byeBye("getShowTimeTypeIndex,$value");
    return value;
  }

  @override
  void setShowTimeTypeIndex(int value) {
    DeviceLibraryLog.hello("setShowTimeTypeIndex,$value");
    var tempJsonData = getMockData();
    tempJsonData["showTimeTypeIndex"] = value;
    setMockData(tempJsonData);
    DeviceLibraryLog.byeBye("setShowTimeTypeIndex");
  }

  @override
  EULAInitStatusEnum getEulaInitStatus() {
    DeviceLibraryLog.hello("getEulaInitStatus");

    int value = getMockData()["eulaInitStatus"] ?? 0;
    DeviceLibraryLog.byeBye("getEulaInitStatus,$value");
    return EULAInitStatusEnum.formFFIApiIndex(value);
  }

  @override
  void setEulaInitStatus(EULAInitStatusEnum value) {
    DeviceLibraryLog.hello("setEulaInitStatus,$value");
    var tempJsonData = getMockData();
    tempJsonData["eulaInitStatus"] = value.index;
    setMockData(tempJsonData);
    DeviceLibraryLog.byeBye("setEulaInitStatus");
  }

  @override
  WlanGuideSettingStatusEnum getWLanGuideSettingStatus() {
    DeviceLibraryLog.hello("getWLanGuideSettingStatus");

    int value = getMockData()["wLanGuideSettingStatus"] ?? 0;
    DeviceLibraryLog.byeBye("getWLanGuideSettingStatus,$value");
    return WlanGuideSettingStatusEnum.formFFIApiIndex(value);
  }

  @override
  void setWLanGuideSettingStatus(WlanGuideSettingStatusEnum value) {
    DeviceLibraryLog.hello("setWLanGuideSettingStatus,$value");
    var tempJsonData = getMockData();
    tempJsonData["wLanGuideSettingStatus"] = value.index;
    setMockData(tempJsonData);
    DeviceLibraryLog.byeBye("setWLanGuideSettingStatus");
  }

  @override
  DeviceErrorCode setSetupLangFlag() {
    DeviceLibraryLog.hello("setSetupLangFlag");
    final errIndex = _bindings.setSetupLangFlag();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setSetupLangFlag");
    return errCode;
  }

  @override
  ({DeviceErrorCode errorCode, bool value}) getSetupLangFlag() =>
      ffi.using((arena) {
        DeviceLibraryLog.hello("getSetupLangFlag");
        final pointer = arena.call<dart_ffi.Bool>();
        final errIndex = _bindings.getSetupLangFlag(pointer);
        final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
        final bool value = pointer.value;
        DeviceLibraryLog.byeBye("getSetupLangFlag,$value");
        return (errorCode: errCode, value: value);
      });

  @override
  DeviceErrorCode resetSetupLang() {
    DeviceLibraryLog.hello("resetSetupLang");
    final errIndex = _bindings.resetSetupLang();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("resetSetupLang");
    return errCode;
  }

  @override
  DeviceErrorCode setSetupVoiceGuideFlag() {
    DeviceLibraryLog.hello("setSetupVoiceGuideFlag");
    final errIndex = _bindings.setSetupVoiceGuideFlag();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("setSetupVoiceGuideFlag");
    return errCode;
  }

  @override
  ({DeviceErrorCode errCode, bool isSet}) getSetupVoiceGuideFlag() => ffi.using(
        (arena) {
          DeviceLibraryLog.hello("getSetupVoiceGuideFlag");
          final pointer = arena.call<dart_ffi.Bool>();
          final errIndex = _bindings.getSetupVoiceGuideFlag(pointer);
          final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
          final bool isSet = pointer.value;
          DeviceLibraryLog.byeBye("getSetupVoiceGuideFlag,$isSet");
          return (errCode: errCode, isSet: isSet);
        },
      );

  @override
  DeviceErrorCode resetSetupVoiceGuideFlag() {
    DeviceLibraryLog.hello("resetSetupVoiceGuideFlag");
    final errIndex = _bindings.resetSetupVoiceGuideFlag();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceLibraryLog.byeBye("resetSetupVoiceGuideFlag");
    return errCode;
  }

  ///
  /// カメラペンのキャリブレーション関連インターフェース
  /// ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
  ///

  @override
  Offset getCameraPenOffset() {
    var tempJsonData = getMockData();
    double offsetWidth = tempJsonData["cameraViewOffsetWidth"];
    double offsetHeight = tempJsonData["cameraViewOffsetHeight"];
    return Offset(offsetWidth, offsetHeight);
  }

  @override
  void setCameraPenOffset(Offset offset) {
    var tempJsonData = getMockData();
    tempJsonData["cameraViewOffsetWidth"] = offset.dx;
    tempJsonData["cameraViewOffsetHeight"] = offset.dy;
  }

  @override
  Offset getCameraPenConversionRatio() {
    var tempJsonData = getMockData();
    double ratioWidth = tempJsonData["cameraViewRatioWidth"];
    double ratioHeight = tempJsonData["cameraViewRatioHeight"];
    return Offset(ratioWidth, ratioHeight);
  }

  @override
  void setCameraPenConversionRatio(Offset offset) {
    var tempJsonData = getMockData();
    tempJsonData["cameraViewRatioWidth"] = offset.dx;
    tempJsonData["cameraViewRatioHeight"] = offset.dy;
  }

  /// 値を格納するために使用されるプライベートフィールド
  int? _specValue;

  @override
  int getSpec() {
    // DeviceLibraryLog.hello("getSpec");

    /// spec
    int specValue;
    if (_specValue == null) {
      _specValue = getDeviceSettingInfo().settingInfo.spec;
      specValue = _specValue!;
    } else {
      specValue = _specValue!;
    }

    /// TESTAPIで強制変更
    if (DeviceLibraryTestApi().modeSpec != null) {
      specValue = DeviceLibraryTestApi().modeSpec!;
    } else {
      /// Do nothing
    }
    // DeviceLibraryLog.byeBye("getSpec,$specValue");
    return specValue;
  }

  /// 日本仕向けか
  bool? _isProductsJapan;

  @override
  bool getProductsJapan() {
    DeviceLibraryLog.hello("getProductsJapan");

    /// spec
    bool isProductsJapan;
    if (_isProductsJapan == null) {
      _isProductsJapan = getDeviceSettingInfo().settingInfo.isProductsJapan;
      isProductsJapan = _isProductsJapan!;
    } else {
      isProductsJapan = _isProductsJapan!;
    }

    DeviceLibraryLog.byeBye("getProductsJapan,$isProductsJapan");
    return isProductsJapan;
  }

  @override
  ({DeviceErrorCode errorCode, UserLogProductInfo? userLogProductInfo})
      getUserLogProductInfo() {
    DeviceLibraryLog.hello("getUserLogProductInfo");

    var pointer = dev_gen.UserLogProductInfo_t.allocate();
    final int errIndex = _bindings.getUserLogProductInfo(pointer);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    if (errCode == DeviceErrorCode.devNoError) {
      final info = UserLogProductInfo.fromPointer(pointer);

      DeviceLibraryLog.byeBye("getUserLogProductInfo");
      dev_gen.UserLogProductInfo_t.deallocate(pointer);
      return (errorCode: errCode, userLogProductInfo: info);
    } else {
      DeviceLibraryLog.byeBye("getUserLogProductInfo");
      dev_gen.UserLogProductInfo_t.deallocate(pointer);
      return (errorCode: errCode, userLogProductInfo: null);
    }
  }

  ///
  /// intが(0から2^16-1まで)の範囲内にあるか確認する。
  ///
  void _assertUint16Range(int num) {
    assert(num >= 0 && num <= 65535);
  }

  @override
  ({Uint16List buffer, DeviceErrorCode errorCode}) bpifEepRead(
    int adr,
    int size,
  ) =>
      ffi.using((arena) {
        _assertUint16Range(adr);
        _assertUint16Range(size);
        DeviceLibraryLog.hello("bpifEepRead-> adr: $adr, size: $size");
        final Pointer<Uint16> ptr = arena.allocate(size);
        final int errorIndex = _bindings.BpifEepRead(adr, size, ptr);
        DeviceLibraryLog.byeBye("bpifEepRead");
        return (
          buffer: ptr.asTypedList(size),
          errorCode: DeviceErrorCodeExtension.getValuesByIndex(errorIndex)
        );
      });

  @override
  DeviceErrorCode bpifEepWrite(
    int adr,
    int size,
    int data,
    BoardType boardType,
  ) {
    _assertUint16Range(adr);
    _assertUint16Range(size);
    _assertUint16Range(data);
    DeviceLibraryLog.hello(
      "bpifEepWrite-> adr: $adr, size: $size, data: $data, boardType: $boardType",
    );
    final int errorIndex =
        _bindings.BpifEepWrite(adr, size, data, boardType.index);
    DeviceLibraryLog.byeBye("bpifEepWrite");
    return DeviceErrorCodeExtension.getValuesByIndex(errorIndex);
  }

  @override
  ({DeviceErrorCode errorCode, bool value}) isPhotoStitchAiTestEnabled() {
    DeviceLibraryLog.hello("isPhotoStitchAiTestEnabled");

    try {
      bool value = getMockData()['isPhotoStitchAiTestEnabled'] ?? false;
      DeviceLibraryLog.byeBye("isPhotoStitchAiTestEnabled,$value");
      return (errorCode: DeviceErrorCode.devNoError, value: value);
    } catch (e) {
      DeviceLibraryLog.byeBye("isPhotoStitchAiTestEnabled,error occur");
      return (errorCode: DeviceErrorCode.devNoError, value: false);
    }
  }

  @override
  int getWlanOffStartUpCount() {
    DeviceLibraryLog.hello("getWlanOffStartUpCount");
    var tempJsonData = getMockData();
    final count = tempJsonData["wlanOffStartUpCount"] ?? 1;
    DeviceLibraryLog.byeBye("getWlanOffStartUpCount, $count");
    return count;
  }

  @override
  void addWlanOffStartUpCount() {
    DeviceLibraryLog.hello("addWlanOffStartUpCount");
    var tempJsonData = getMockData();
    final count = tempJsonData["wlanOffStartUpCount"] ?? 0;
    tempJsonData["wlanOffStartUpCount"] = count + 1;
    DeviceLibraryLog.byeBye("addWlanOffStartUpCount ");
  }

  @override
  void resetWlanOffStartUpCount() {
    DeviceLibraryLog.hello("resetWlanOffStartUpCount");
    var tempJsonData = getMockData();
    tempJsonData["wlanOffStartUpCount"] = 0;
    DeviceLibraryLog.byeBye("resetWlanOffStartUpCount");
  }

  ///
  /// artspira関連インターフェース
  /// ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
  ///
  @override
  void setArtspiraRegisterInfo(bool isRegister, String name,
      {String? accessToken, String? refreshToken}) {
    DeviceLibraryLog.hello("setArtspiraRegisterInfo");
    var tempJsonData = getMockData();
    tempJsonData["isPinCodeRegister"] = isRegister;
    tempJsonData["artspiraUserName"] = name;
    if (accessToken != null) {
      tempJsonData["artspiraAccessToken"] = accessToken;
    }

    if (refreshToken != null) {
      tempJsonData["artspiraRefreshToken"] = refreshToken;
    }

    DeviceLibraryLog.byeBye("setArtspiraRegister");
  }

  @override
  (bool isRegister, String name, String accessToken, String refreshToken)
      getArtspiraRegisterInfo() {
    DeviceLibraryLog.hello("getArtspiraRegisterInfo");
    var tempJsonData = getMockData();
    final isRegister = tempJsonData["isPinCodeRegister"] ?? false;
    final name = tempJsonData["artspiraUserName"] ?? "";
    final accessToken = tempJsonData["artspiraAccessToken"] ?? "";
    final refreshToken = tempJsonData["artspiraRefreshToken"] ?? "";
    DeviceLibraryLog.byeBye("getArtspiraRegisterInfo");
    return (isRegister, name, accessToken, refreshToken);
  }

  @override
  void setArtspiraAgreedToTelemetry(int level) {
    DeviceLibraryLog.hello("setArtspiraAgreedToTelemetry");
    var tempJsonData = getMockData();

    /// 収集レベル
    tempJsonData["agreedToTelemetry"] = level;
    DeviceLibraryLog.byeBye("setArtspiraAgreedToTelemetry");
  }

  @override
  int getArtspiraAgreedToTelemetry() {
    DeviceLibraryLog.hello("getArtspiraAgreedToTelemetry");
    var tempJsonData = getMockData();

    /// 収集レベルはnullの場合は「未取得（-1）」を返す
    final level = tempJsonData["agreedToTelemetry"] ?? -1;
    DeviceLibraryLog.byeBye("getArtspiraAgreedToTelemetry");
    return level;
  }

  @override
  bool getMdcResumeState() {
    DeviceLibraryLog.hello("getMdcResumeState");
    var tempJsonData = getMockData();

    final level = tempJsonData["mdcResume"] ?? false;
    DeviceLibraryLog.byeBye("getMdcResumeState");
    return level;
  }

  @override
  void setMdcResumeState(bool state) {
    DeviceLibraryLog.hello("setMdcResumeState");
    var tempJsonData = getMockData();

    tempJsonData["mdcResume"] = state;
    DeviceLibraryLog.byeBye("setMdcResumeState");
  }

  @override
  int getMdcSnapPoint() {
    DeviceLibraryLog.hello("getMdcSnapPoint");
    var tempJsonData = getMockData();

    final level = tempJsonData["mdcSnapPoint"] ?? -1;
    DeviceLibraryLog.byeBye("getMdcSnapPoint");
    return level;
  }

  @override
  void setMdcSnapPoint(int snapPoint) {
    DeviceLibraryLog.hello("setMdcSnapPoint");
    var tempJsonData = getMockData();

    tempJsonData["mdcSnapPoint"] = snapPoint;
    DeviceLibraryLog.byeBye("setMdcSnapPoint");
  }

  @override
  int getMdcSnapSize() {
    DeviceLibraryLog.hello("getMdcSnapSize");
    var tempJsonData = getMockData();

    final level = tempJsonData["mdcSnapSize"] ?? 0;
    DeviceLibraryLog.byeBye("getMdcSnapSize");
    return level;
  }

  @override
  void setMdcSnapSize(int snapSize) {
    DeviceLibraryLog.hello("setMdcSnapSize");
    var tempJsonData = getMockData();

    tempJsonData["mdcSnapSize"] = snapSize;
    DeviceLibraryLog.byeBye("setMdcSnapSize");
  }

  @override
  int getMdcUndoSize() {
    DeviceLibraryLog.hello("getMdcUndoSize");
    var tempJsonData = getMockData();

    final level = tempJsonData["mdcUndoSize"] ?? 0;
    DeviceLibraryLog.byeBye("getMdcUndoSize");
    return level;
  }

  @override
  void setMdcUndoSize(int undoSize) {
    DeviceLibraryLog.hello("setMdcUndoSize");
    var tempJsonData = getMockData();

    tempJsonData["mdcUndoSize"] = undoSize;
    DeviceLibraryLog.byeBye("setMdcUndoSize");
  }

  @override
  bool getMdcBackgroundState() {
    DeviceLibraryLog.hello("getMdcBackgroundState");
    var tempJsonData = getMockData();

    final state = tempJsonData["mdcBackgroundState"] ?? false;
    DeviceLibraryLog.byeBye("getMdcBackgroundState");
    return state;
  }

  @override
  void setMdcBackgroundState(bool state) {
    DeviceLibraryLog.hello("setMdcBackgroundState");
    var tempJsonData = getMockData();

    tempJsonData["mdcBackgroundState"] = state;
    DeviceLibraryLog.byeBye("setMdcBackgroundState");
  }

  @override
  int getAppBadgeSenju() {
    DeviceLibraryLog.hello("getAppBadgeSenju");
    var tempJsonData = getMockConnectionServerData();

    final value = tempJsonData["appBadgeSenju"] ?? 1;
    DeviceLibraryLog.byeBye("getAppBadgeSenju");
    return value;
  }

  @override
  int getCanvasServer() {
    DeviceLibraryLog.hello("getCanvasServer");
    var tempJsonData = getMockConnectionServerData();

    final value = tempJsonData["canvasServer"] ?? 1;
    DeviceLibraryLog.byeBye("getCanvasServer");
    return value;
  }

  @override
  bool getIsSetTime() {
    DeviceLibraryLog.hello("getIsSetTime");
    var tempJsonData = getMockConnectionServerData();

    final value = tempJsonData["isSetTime"];
    DeviceLibraryLog.byeBye("getIsSetTime");
    return value;
  }

  @override
  int getKitServer() {
    DeviceLibraryLog.hello("getKitServer");
    var tempJsonData = getMockConnectionServerData();

    final value = tempJsonData["kitServer"] ?? 0;
    DeviceLibraryLog.byeBye("getKitServer");
    return value;
  }

  @override
  int getSncKitServer() {
    DeviceLibraryLog.hello("getSncKitServer");
    var tempJsonData = getMockConnectionServerData();

    final value = tempJsonData["sncKitServer"] ?? 0;
    DeviceLibraryLog.byeBye("getSncKitServer");
    return value;
  }

  @override
  String getWlanMachineName() {
    DeviceLibraryLog.hello("getWlanMachineName");
    var tempJsonData = getMockConnectionServerData();

    final String value = tempJsonData["wlanMachineName"];
    DeviceLibraryLog.byeBye("getWlanMachineName");
    return value.isEmpty ? getDefaultMachineName() : value;
  }

  @override
  String getDefaultMachineName() {
    String productNo = getProductNo();
    return "SewingMachine${productNo.substring(productNo.length - 3, productNo.length)}";
  }

  @override
  void setAppBadgeSenju(int value) {
    DeviceLibraryLog.hello("setAppBadgeSenju");
    var tempJsonData = getMockConnectionServerData();

    tempJsonData["appBadgeSenju"] = value;
    DeviceLibraryLog.byeBye("setAppBadgeSenju");
  }

  @override
  void setCanvasServer(int value) {
    DeviceLibraryLog.hello("setCanvasServer");
    var tempJsonData = getMockConnectionServerData();

    tempJsonData["canvasServer"] = value;
    DeviceLibraryLog.byeBye("setCanvasServer");
  }

  @override
  void setFirmDownloadTimestamp(String value) {
    DeviceLibraryLog.hello("setFirmDownloadTimestamp");
    var tempJsonData = getMockConnectionServerData();

    tempJsonData["firmDownloadTimestamp"] = value;
    DeviceLibraryLog.byeBye("setFirmDownloadTimestamp");
  }

  @override
  void setIsSetTime(bool value) {
    DeviceLibraryLog.hello("setIsSetTime");
    var tempJsonData = getMockConnectionServerData();

    tempJsonData["isSetTime"] = value;
    DeviceLibraryLog.byeBye("setIsSetTime");
  }

  @override
  void setKitServer(int value) {
    DeviceLibraryLog.hello("setKitServer");
    var tempJsonData = getMockConnectionServerData();

    tempJsonData["kitServer"] = value;
    DeviceLibraryLog.byeBye("setKitServer");
  }

  @override
  void setSncKitServer(int value) {
    DeviceLibraryLog.hello("setSncKitServer");
    var tempJsonData = getMockConnectionServerData();

    tempJsonData["sncKitServer"] = value;
    DeviceLibraryLog.byeBye("setSncKitServer");
  }

  @override
  void setWlanMachineName(String value) {
    DeviceLibraryLog.hello("setWlanMachineName");
    var tempJsonData = getMockConnectionServerData();

    tempJsonData["wlanMachineName"] = value;
    DeviceLibraryLog.byeBye("setWlanMachineName");
  }

  /// 値を格納するために使用されるプライベートフィールド
  String? _productNoValue;

  @override
  String getProductNo() {
    DeviceLibraryLog.hello("getProductNo");

    /// プロダクトNo
    String productValue;
    if (_productNoValue == null) {
      _productNoValue = getDeviceSettingInfo().settingInfo.productNo;
      productValue = _productNoValue!;
    } else {
      productValue = _productNoValue!;
    }

    DeviceLibraryLog.byeBye("getProductNo,$productValue");
    return productValue;
  }

  @override
  bool getMdcClipboardState() {
    DeviceLibraryLog.hello("getMdcClipboardState");
    var tempJsonData = getMockData();

    final state = tempJsonData["mdcClipboardState"] ?? false;
    DeviceLibraryLog.byeBye("getMdcClipboardState");
    return state;
  }

  @override
  void setMdcClipboardState(bool state) {
    DeviceLibraryLog.hello("mdcClipboardState");
    var tempJsonData = getMockData();

    tempJsonData["mdcClipboardState"] = state;
    DeviceLibraryLog.byeBye("setMdcClipboardState");
  }

  @override
  RTCReadStatus getRTCReadErrorStatus() {
    DeviceLibraryLog.hello("getRTCReadErrorStatus");
    int rtcReadErrorStatus = RTCReadStatus.rtcReadSuccess.index;
    if (_warpChkBindings != null) {
      try {
        rtcReadErrorStatus = _warpChkBindings!.getRTCReadErrorStatus();
      } catch (e) {
        DeviceLibraryLog.e(
            "not found getRTCReadErrorStatus, please check \"libwarpchk.brother.so\" has getRTCReadErrorStatus");
        rtcReadErrorStatus = RTCReadStatus.rtcReadSuccess.index;
      }
    } else {
      DeviceLibraryLog.e(
          "getRTCReadErrorStatus, \"libwarpchk.brother.so\" not found");
    }
    DeviceLibraryLog.byeBye("getRTCReadErrorStatus, $rtcReadErrorStatus ");
    return RTCReadStatus.formFFIApiIndex(rtcReadErrorStatus);
  }

  @override
  int resetRTCReadErrorStatus() {
    DeviceLibraryLog.hello("resetRTCReadErrorStatus");
    int resetStatus = -1;
    if (_warpChkBindings != null) {
      try {
        resetStatus = _warpChkBindings!.resetRTCReadErrorStatus();
      } catch (e) {
        DeviceLibraryLog.e(
            "not found resetRTCReadErrorStatus, please check \"libwarpchk.brother.so\" has resetRTCReadErrorStatus");
      }
    } else {
      DeviceLibraryLog.e(
          "resetRTCReadErrorStatus, \"libwarpchk.brother.so\" not found");
    }
    DeviceLibraryLog.byeBye("resetRTCReadErrorStatus, $resetStatus ");
    return resetStatus;
  }

  @override
  void writeDebugLogToFile() {
    DeviceLibraryLog.hello("writeDebugLogToFile");

    if (_warpChkBindings != null) {
      try {
        _warpChkBindings!.writeDebugLogToFile();
      } catch (e) {
        DeviceLibraryLog.e(
            "not found writeDebugLogToFile, please check \"libwarpchk.brother.so\" has writeDebugLogToFile");
      }
    } else {
      DeviceLibraryLog.e(
          "writeDebugLogToFile, \"libwarpchk.brother.so\" not found");
    }
    DeviceLibraryLog.byeBye("writeDebugLogToFile ");
  }

  @override
  void UserSettingItemValueListFree() {
    DeviceLibraryLog.hello("UserSettingItemValueListFree");
    DeviceLibraryLog.byeBye("UserSettingItemValueListFree");
  }
}
