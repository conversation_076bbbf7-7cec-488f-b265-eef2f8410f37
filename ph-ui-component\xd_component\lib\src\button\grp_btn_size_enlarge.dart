// ignore_for_file: camel_case_types

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';

import '../../xd_component.dart';

class grp_btn_size_enlarge extends StatelessWidget {
  const grp_btn_size_enlarge({
    super.key,
    this.state = ButtonState.normal,
    this.onTap,
    this.onLongPress,
    this.onTapUp,
    this.onTapCancel,
    this.feedBackControl = const FeedBackControl(),
    this.longPressTriggerTick = 3,
  });

  final bool Function()? onTap;
  final bool Function()? onLongPress;
  final void Function()? onTapUp;
  final void Function()? onTapCancel;
  final int longPressTriggerTick;
  final ButtonState state;
  final FeedBackControl? feedBackControl;
  @override
  Widget build(BuildContext context) => LongPressButtonNew(
        longPressTriggerTick: longPressTriggerTick,
        feedBackControl: feedBackControl,
        onTap: state == ButtonState.disable ? null : onTap,
        onLongPress: onLongPress,
        onTapUp: onTapUp,
        state: state,
        style: ThemeButton.btn_n_size98x70_theme1,
        child: const Center(
          child: ico_size_enlarge(),
        ),
      );
}
