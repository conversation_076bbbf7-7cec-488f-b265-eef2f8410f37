import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'line_candle_view_model.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'line_candle_view_interface.freezed.dart';

@freezed
class LineCandleState with _$LineCandleState {
  const factory LineCandleState({
    @Default(false) bool isSizePopupOn,
    @Default(false) bool isSpacePopupOn,
    required String sizeDisplayValue,
    required bool sizeDisplayDefault,
    required String spaceDisplayValue,
    required bool spaceDisplayDefault,
  }) = _LineCandleState;
}

abstract class LineCandleViewInterface extends ViewModel<LineCandleState> {
  LineCandleViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// サイズ設定ポップアップウィンドウを開きます
  ///
  void openSizeSettingPopup(context);

  ///
  /// 間隔設定ポップアップウィンドウを開く
  ///
  void openSpaceSettingPopup(context);

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
