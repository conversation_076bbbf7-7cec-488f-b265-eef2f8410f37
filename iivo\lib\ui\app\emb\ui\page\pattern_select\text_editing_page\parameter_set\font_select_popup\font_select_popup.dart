import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import 'font_select_popup_view_interface.dart';
import 'font_select_popup_view_model.dart';

class FontSelectPopup extends ConsumerStatefulWidget {
  const FontSelectPopup({super.key});

  @override
  ConsumerState<FontSelectPopup> createState() => _FontSelectPopupState();
}

class _FontSelectPopupState extends ConsumerState<FontSelectPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;

    final viewModel = ref.read(fontSelectPopupViewModelProvider.notifier);
    final state = ref.watch(fontSelectPopupViewModelProvider);
    return Column(children: [
      const Spacer(flex: 778),
      Expanded(
        flex: 433,
        child: Container(
          margin: const EdgeInsets.fromLTRB(6, 0, 6, 0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.0),
            border: Border.all(color: Colors.grey, width: 2),
          ),
          child: Column(
            children: [
              Expanded(
                flex: 329,
                child: Row(
                  verticalDirection: VerticalDirection.down,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Spacer(flex: 20),
                    Expanded(
                      flex: 717,
                      child: ListView.builder(
                        controller: viewModel.scrollController,
                        itemCount: 1,
                        itemBuilder: (context, index) => Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(
                              height: 7,
                            ),

                            state.hasExclusiveScriptFont
                                ? Column(
                                    children: [
                                      SizedBox(
                                        height: 101,
                                        width: 717,
                                        child: btn_exclusive_script(
                                          feedBackControl:
                                              FeedBackControl.onlyDisable,
                                          onTap: () =>
                                              viewModel.onItemButtonClicked(
                                                  exclusiveScriptFontIndex),
                                          exclusiveIcon:
                                              viewModel.getCharacterFontImage(
                                                  exclusiveScriptFontIndex),
                                        ),
                                      ),
                                      const SizedBox(height: 5)
                                    ],
                                  )
                                : Container(),

                            /// TODO :http://apngoap306.ap.brothergroup.net:8080/jira/browse/PHBSH-2245
                            GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: viewModel.getItemCount(),
                              cacheExtent: 524,
                              gridDelegate:
                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 4,
                                crossAxisSpacing: 7,
                                mainAxisSpacing: 7,
                                mainAxisExtent: 101,
                              ),
                              itemBuilder: (context, index) {
                                if (viewModel.getCharacterFontName(index) ==
                                    'Space') {
                                  return Container();
                                }
                                return grp_btn_thumbnail(
                                  onTap: () =>
                                      viewModel.onItemButtonClicked(index),
                                  child: Stack(
                                    children: [
                                      Center(
                                        child: viewModel
                                            .getCharacterFontImage(index),
                                      ),
                                      Column(
                                        children: [
                                          const Spacer(flex: 4),
                                          Expanded(
                                            flex: 22,
                                            child: Row(
                                              children: [
                                                const Spacer(flex: 4),
                                                Expanded(
                                                  flex: 85,
                                                  child: Text(
                                                    viewModel
                                                        .getCharacterFontName(
                                                            index),
                                                    style: const TextStyle(
                                                      color: Colors.black,
                                                      fontFamily: "Roboto",
                                                      fontSize: 24,
                                                      height: 1,
                                                      decoration:
                                                          TextDecoration.none,
                                                    ),
                                                    textAlign: TextAlign.left,
                                                  ),
                                                ),
                                                const Spacer(flex: 85),
                                              ],
                                            ),
                                          ),
                                          const Spacer(flex: 75),
                                        ],
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    const Spacer(flex: 31),
                    Expanded(
                      flex: 8,
                      child: Container(
                        padding: const EdgeInsets.fromLTRB(0, 7, 0, 0),
                        child: CustomScrollbar(
                          controller: viewModel.scrollController,
                          visibilityWhenScrollFull: false,
                        ),
                      ),
                    ),
                    const Spacer(flex: 12),
                  ],
                ),
              ),
              const Spacer(flex: 5),
              Expanded(
                flex: 80,
                child: Row(
                  children: [
                    const Spacer(flex: 624),
                    Expanded(
                      flex: 152,
                      child: grp_btn_ok(
                        buttonText: l10n.icon_ok,
                        onTap: () => viewModel.onOkButtonClicked(context),
                      ),
                    ),
                    const Spacer(flex: 12),
                  ],
                ),
              ),
              const Spacer(flex: 12),
            ],
          ),
        ),
      ),
      const Spacer(flex: 69),
    ]);
  }
}
