// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'magnification_popup_view_interface.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MagnificationPopupState {
  int get magnificationIndex => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MagnificationPopupStateCopyWith<MagnificationPopupState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MagnificationPopupStateCopyWith<$Res> {
  factory $MagnificationPopupStateCopyWith(MagnificationPopupState value,
          $Res Function(MagnificationPopupState) then) =
      _$MagnificationPopupStateCopyWithImpl<$Res, MagnificationPopupState>;
  @useResult
  $Res call({int magnificationIndex});
}

/// @nodoc
class _$MagnificationPopupStateCopyWithImpl<$Res,
        $Val extends MagnificationPopupState>
    implements $MagnificationPopupStateCopyWith<$Res> {
  _$MagnificationPopupStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? magnificationIndex = null,
  }) {
    return _then(_value.copyWith(
      magnificationIndex: null == magnificationIndex
          ? _value.magnificationIndex
          : magnificationIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MagnificationPopupStateImplCopyWith<$Res>
    implements $MagnificationPopupStateCopyWith<$Res> {
  factory _$$MagnificationPopupStateImplCopyWith(
          _$MagnificationPopupStateImpl value,
          $Res Function(_$MagnificationPopupStateImpl) then) =
      __$$MagnificationPopupStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int magnificationIndex});
}

/// @nodoc
class __$$MagnificationPopupStateImplCopyWithImpl<$Res>
    extends _$MagnificationPopupStateCopyWithImpl<$Res,
        _$MagnificationPopupStateImpl>
    implements _$$MagnificationPopupStateImplCopyWith<$Res> {
  __$$MagnificationPopupStateImplCopyWithImpl(
      _$MagnificationPopupStateImpl _value,
      $Res Function(_$MagnificationPopupStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? magnificationIndex = null,
  }) {
    return _then(_$MagnificationPopupStateImpl(
      magnificationIndex: null == magnificationIndex
          ? _value.magnificationIndex
          : magnificationIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$MagnificationPopupStateImpl implements _MagnificationPopupState {
  const _$MagnificationPopupStateImpl({this.magnificationIndex = 0});

  @override
  @JsonKey()
  final int magnificationIndex;

  @override
  String toString() {
    return 'MagnificationPopupState(magnificationIndex: $magnificationIndex)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MagnificationPopupStateImpl &&
            (identical(other.magnificationIndex, magnificationIndex) ||
                other.magnificationIndex == magnificationIndex));
  }

  @override
  int get hashCode => Object.hash(runtimeType, magnificationIndex);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MagnificationPopupStateImplCopyWith<_$MagnificationPopupStateImpl>
      get copyWith => __$$MagnificationPopupStateImplCopyWithImpl<
          _$MagnificationPopupStateImpl>(this, _$identity);
}

abstract class _MagnificationPopupState implements MagnificationPopupState {
  const factory _MagnificationPopupState({final int magnificationIndex}) =
      _$MagnificationPopupStateImpl;

  @override
  int get magnificationIndex;
  @override
  @JsonKey(ignore: true)
  _$$MagnificationPopupStateImplCopyWith<_$MagnificationPopupStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
