import 'dart:io';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:path/path.dart' show join;
import 'package:usb_manager/usb_manager.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../../../memory/memory.dart';
import '../../../../../../../../../model/const_def_model.dart';
import '../../../../../../../../../model/device_memory_model.dart';
import '../../../../../../../../../model/handel_model.dart';
import '../../../../../../../../../model/thread_color_model.dart';
import '../../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../../../../../global_popup/panel_popup_route.dart';
import '../../../../../../model/edit_model.dart';
import '../../../../../../model/memory_model.dart';
import '../../../../../../model/pattern_model.dart';
import '../../../../../../model/redo_undo_model.dart';
import '../../../../../../model/select_information_model.dart';
import '../../../../../../model/select_model.dart';
import '../../../../../../model/thread_model.dart';
import '../../../../../page_route.dart';
import 'component/pdf_save_popup.dart';
import 'select_information_popup_view_interface.dart';

///
/// USBディスプレイの最大数
///
const int maxUsbDisplayNumber = 2;

/// 1列の最大個数
const int eachColumnMaxItemNumber = 5;

/// 黒い背景の定義
Color blackBackgroundColor = const Color.fromARGB(255, 48, 50, 50);

final selectInformationPopupViewModelProvider =
    StateNotifierProvider.autoDispose<SelectInformationPopupViewModelInterface,
        SelectInformationPopupState>((_) => SelectInformationPopupViewModel());

class SelectInformationPopupViewModel
    extends SelectInformationPopupViewModelInterface {
  SelectInformationPopupViewModel()
      : super(SelectInformationPopupState(
          isInch: false,
          isPrintAndStitchPattern: false,
          isImageDisplayOn: false,
          isBackGroundColorOn: false,
          isUsb1ClickedOn: false,
          heightValue: '----',
          widthValue: '----',
          totalNeedleCountValue: '----',
          totalThreadValue: '----',
          totalTimeValue: '----',
          threadInfoDisplayList: [],
          popupType: InformationPopupId.none,
          scrollController: ScrollController(),
        )) {
    PrintAndStitchType printAndStitchType = _getIsPrintAndStitchPatternType();
    bool isPrintAndStitchPattern =
        printAndStitchType != PrintAndStitchType.normal;

    bool isInch = PatternModel().isUnitMm == false;

    PatternDispInfo patternDispInfo = _getTemporaryGroupInfo();

    _widthMMValue = patternDispInfo.right - patternDispInfo.left;
    _heightMMValue = patternDispInfo.bottom - patternDispInfo.top;

    final String heightValue =
        PatternModel().changeValueToDisplay(_heightMMValue);
    final String widthValue =
        PatternModel().changeValueToDisplay(_widthMMValue);
    String totalNeedleCountValue = patternDispInfo.stitchNum.toString();
    String totalThreadValue = patternDispInfo.threadNum.toString();
    String totalTimeValue = patternDispInfo.sewingTime
        .clamp(sewingTimeDisplayLimitMin, sewingTimeDisplayLimitMax)
        .toString();
    List<List<ThreadAutoSelectDispInfo>> threadInfoDisplayList =
        _listConversion(_getThreadInfoDisplayList());

    Image image = EmbLibrary()
        .apiBinding
        .getInfoImage(
          SelectInformationModel().isImageDisplayOn,
          Colors.white.value,
        )
        .image;

    Image? backGroundImage;
    if (printAndStitchType != PrintAndStitchType.normal) {
      try {
        String rootPath = join(getRootPath(), 'alldata', 'jpg');
        String whaleBackGroundImage = join(rootPath, 'UpgBrotherEx11.jpg');
        String bambiBackGroundImage = join(rootPath, 'EJpgDis2.jpg');

        FileEntity newImageFile = FileEntity(
            printAndStitchType == PrintAndStitchType.whale
                ? whaleBackGroundImage
                : bambiBackGroundImage);
        if (newImageFile.existsSync()) {
          final byteBuffer = newImageFile.readAsBytesSync();
          backGroundImage = Image.memory(byteBuffer, fit: BoxFit.fill);
          decodeImageFromList(byteBuffer).then(
              (value) => SelectInformationModel().printBackgroundImage = value);
        } else {
          /// Do nothing
        }
      } on IOException catch (e) {
        Log.errorTrace('画像データを読み込エラー!!!. message is ${e.toString()}');
      } on Exception catch (e) {
        Log.errorTrace('画像解析エラー!!!. message is ${e.toString()}');
      }
    } else {
      /// DO nothing
    }

    /// Model更新
    SelectInformationModel().printAndStitchType = printAndStitchType;

    /// View更新
    state = state.copyWith(
      isInch: isInch,
      widthValue: widthValue,
      heightValue: heightValue,
      totalTimeValue: totalTimeValue,
      totalThreadValue: totalThreadValue,
      totalNeedleCountValue: totalNeedleCountValue,
      isPrintAndStitchPattern: isPrintAndStitchPattern,
      isBackGroundColorOn: SelectInformationModel().isBackGroundColorOn,
      isImageDisplayOn: SelectInformationModel().isImageDisplayOn,
      threadInfoDisplayList: threadInfoDisplayList,
      image: image,
      backGroundImage: backGroundImage,
    );
  }

  ///
  /// 模様サイズ（0.1mm）
  ///
  late final int _widthMMValue;
  late final int _heightMMValue;

  ///
  /// 画面更新
  ///
  @override
  void update() {
    final List<List<ThreadAutoSelectDispInfo>> threadInfoDisplayList =
        _listConversion(_getThreadInfoDisplayList());
    final String heightValue =
        PatternModel().changeValueToDisplay(_heightMMValue);
    final String widthValue =
        PatternModel().changeValueToDisplay(_widthMMValue);

    /// View更新
    state = state.copyWith(
      isInch: PatternModel().isUnitMm == false,
      threadInfoDisplayList: threadInfoDisplayList,
      widthValue: widthValue,
      heightValue: heightValue,
    );
  }

  ///
  /// Real Previewのクリック関数
  ///
  @override
  void onRealPreviewClick() {
    if (state.isImageDisplayOn == false) {
      return;
    }

    /// model更新
    PatternModel().realPreviewDisplayType = RealPreviewDisplayType.information;

    /// view更新
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.embRealPreview);
  }

  ///
  /// Image Displayのクリック関数
  ///
  @override
  void onImageDisplayClick() {
    bool isImageDisplayOn = !state.isImageDisplayOn;

    /// Model更新
    SelectInformationModel().isImageDisplayOn = isImageDisplayOn;

    /// lib更新
    Image image = EmbLibrary()
        .apiBinding
        .getInfoImage(
          isImageDisplayOn,
          Colors.white.value,
        )
        .image;

    /// view更新
    state = state.copyWith(
      isImageDisplayOn: isImageDisplayOn,
      image: image,
    );
  }

  ///
  /// Image Saveのクリック関数
  ///
  @override
  void onImageSaveClick(BuildContext context) {
    /// 画面遷移
    _openImageSavePopup(context);
  }

  ///
  /// Backgroundのクリック関数
  ///
  @override
  void onBackgroundClick() {
    bool isBackGroundColorOn = !state.isBackGroundColorOn;

    /// Model更新
    SelectInformationModel().isBackGroundColorOn = isBackGroundColorOn;

    /// view更新
    state = state.copyWith(isBackGroundColorOn: isBackGroundColorOn);
  }

  ///
  /// Setのクリック関数
  ///
  @override
  Future<void> onSetButtonClick(BuildContext context) async {
    final firstFileInfo = MemoryModel().loadFileInfoList.firstOrNull;
    if (firstFileInfo != null) {
      final kind = firstFileInfo.kind;

      /// 1方向キルト / 6角形キルト / Edge模様
      if (kind == ETLKind.etlKindExtQuilt ||
          kind == ETLKind.etlKindPolygonQuilt ||
          kind == ETLKind.etlKindEdge) {
        EmbLibraryError error = EmbLibrary().apiBinding.embGotoEdit();
        if (error != EmbLibraryError.EMB_GOTO_RESUME &&
            error != EmbLibraryError.EMB_NO_ERR) {
          return;
        }
        EmbLibrary().apiBinding.prepareSewing();
        error = EmbLibrary().apiBinding.embGotoSewing();
        if (error == EmbLibraryError.EMB_GOTO_RESUME) {
          PanelPopupRoute().openPleaseWaitPopup();
          await Future.delayed(pleaseWaitTime);
          final err = EmbLibrary()
              .apiBinding
              .saveEmbResumeData(EditModel().resumeSavePath);
          if (err != EmbLibraryError.EMB_NO_ERR) {
            PanelPopupRoute().closePleaseWaitPopup();
            GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
              arguments: TroubleOccoredPowerOffArgument(
                onOKButtonClicked: GlobalPopupRoute().resetErrorState,
              ),
            );
            return;
          }

          /// Resumeの時、特別な操作
          error = EmbLibrary().apiBinding.embGotoResumeOk();
          PanelPopupRoute().closePleaseWaitPopup();
        }

        if (error != EmbLibraryError.EMB_GOTO_SEWING) {
          return;
        }

        /// Model更新
        PatternModel()
          ..reloadAllPattern()
          ..clearTemporaryPatternList();

        PagesRoute().pushNamedAndRemoveUntil(
            nextRoute: PageRouteEnum.sewing, untilRoute: PageRouteEnum.home);
      } else {
        /// 大型分割模様 / 4分割キルト
        EmbLibraryError error = EmbLibrary().apiBinding.embGotoEdit();
        if (error != EmbLibraryError.EMB_NO_ERR) {
          return;
        }

        final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
        if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
          return;
        }

        /// Model更新
        PatternModel()
          ..reloadAllPattern()
          ..clearTemporaryPatternList();

        PagesRoute().pushNamedAndRemoveUntil(
            nextRoute: PageRouteEnum.patternEdit,
            untilRoute: PageRouteEnum.home);
      }
    } else {
      final tepmCategory = SelectInformationModel().temporaryCategory;
      if (tepmCategory == CategoryType.unknownPattern) {
        Log.assertTrace("The currently selected appearance is invalid!");
        return;
      }

      /// 大型分割模様
      if (tepmCategory == CategoryType.largeConnect) {
        EmbLibraryError error = EmbLibrary().apiBinding.embGotoEdit();
        if (error != EmbLibraryError.EMB_NO_ERR) {
          return;
        }

        final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
        if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
          return;
        }

        /// Model更新
        PatternModel()
          ..reloadAllPattern()
          ..clearTemporaryPatternList();

        PagesRoute().pushNamedAndRemoveUntil(
            nextRoute: PageRouteEnum.patternEdit,
            untilRoute: PageRouteEnum.home);
        return;
      }

      /// 通常の模様
      EmbLibraryError error = EmbLibrary().apiBinding.embGotoEdit();
      if (error != EmbLibraryError.EMB_NO_ERR) {
        return;
      }
      final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
      if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
        return;
      }

      /// Model更新
      PatternModel()
        ..reloadAllPattern()
        ..clearTemporaryPatternList();
      SelectModel()
        ..selectedCategoryType = null
        ..selectedPatternIndex = null;
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }
      SelectInformationModel()
          .setTemporaryPatternAutoKind(CategoryType.unknownPattern);

      PagesRoute().pushNamedAndRemoveUntil(
          nextRoute: PageRouteEnum.patternEdit, untilRoute: PageRouteEnum.home);
    }
  }

  ///
  /// closeのクリック関数
  /// 自分を閉じる、前の画面に戻る
  ///
  @override
  void onCloseButtonClick(BuildContext context) =>
      PopupNavigator.pop(context: context);

  @override
  void dispose() {
    SelectInformationModel().printBackgroundImage?.dispose();
    SelectInformationModel().printBackgroundImage = null;
    super.dispose();
  }

  ///
  /// 現在登録されているパターンの縫製情報を取得する
  ///
  /// [isAllPattern] : 要約された情報を取得するかどうか
  ///                : true(登録されている模様の縫製情報)
  ///                : false(現在選択されているグループの縫製情報)
  ///
  /// [drawType] :  糸色情報取得画面
  ///
  PatternDispInfo _getTemporaryGroupInfo() {
    int totalStitchNum = 0;
    int totalThreadNum = 0;
    int totalSewingTime = 0;
    RectanArea area = RectanArea.maxArea;
    int height = 0;
    int wide = 0;
    for (var temporaryGroup in PatternModel().temporaryGroupList) {
      /// 統計データ
      List<ThreadInfo> threadInfoList = temporaryGroup.threadInfo;

      /// 累加針数、糸数、裁縫は時々
      for (var threadInfo in threadInfoList) {
        totalThreadNum++;
        totalStitchNum += threadInfo.stitchNumber;
        totalSewingTime += threadInfo.sewingTime;
      }

      /// まとめ縫いに必要な最大スペース
      area = getEmbGrpInfoMaxSewingArea(
        embGrp: temporaryGroup.embGroupInfo.embGrp,
        embPatternInfo: temporaryGroup.embGroupInfo.embPatternInfo,
        embInfo: temporaryGroup.embGroupInfo.embInfo,
        beforeArea: area,
      );
      height = temporaryGroup.embGroupInfo.embInfo.height;
      wide = temporaryGroup.embGroupInfo.embInfo.wide;
    }

    return PatternDispInfo(
      right: area.right,
      left: area.left,
      bottom: area.bottom,
      top: area.top,
      allPatternHeight: height,
      allPatternWidth: wide,
      stitchNum: totalStitchNum,
      threadNum: totalThreadNum,
      sewingTime:
          PatternModel().changeLibSewingTimeToUiSewingTime(totalSewingTime),
    );
  }

  ///
  /// 表示用の線色情報の取得
  ///
  List<ThreadAutoSelectDispInfo> _getThreadInfoDisplayList() {
    List<ThreadAutoSelectDispInfo> displayThreadInfoList = [];
    List<ThreadInfo> threadInfoList =
        SelectInformationModel.getTemporaryGroupThreadInfo();
    bool isThreadColorDefault = ThreadModel.getThreadColor();

    for (int index = 0; index < threadInfoList.length; index++) {
      displayThreadInfoList.add(ThreadAutoSelectDispInfo(
        isEnable: true,
        isSelected: false,
        isThreadNotSewing: threadInfoList[index].notSewing,
        treadColor: threadInfoList[index].colorRGB,
        threadCode: ThreadModel.getThreadCode(threadInfoList[index].threadCode,
            threadInfoList[index].threadCodeDigit, isThreadColorDefault),
        threadBrandName: _getThreadBrandName(threadInfoList[index].brandCode,
            threadInfoList[index].threadCode, isThreadColorDefault),
        threadColorName: _getThreadColorName(threadInfoList[index].index300,
            threadInfoList[index].threadCode, isThreadColorDefault),
        threadSewingTime: PatternModel()
            .changeLibSewingTimeToUiSewingTime(threadInfoList[index].sewingTime)
            .toString(),
        appliqueIcon: ThreadModel.getAppliqueIcon(
          threadInfoList[index].threadCode,
          false,
          isThreadColorDefault,
        ),
      ));
    }

    return displayThreadInfoList;
  }

  ///
  /// データを5つずつグループ化します
  ///
  List<List<ThreadAutoSelectDispInfo>> _listConversion(
      List<ThreadAutoSelectDispInfo> inputList) {
    List<List<ThreadAutoSelectDispInfo>> outList = [];

    /// すべてのデータを順次入力
    for (var info in inputList) {
      /// 5つのデータごとに分割する
      if (outList.isEmpty || outList.last.length >= eachColumnMaxItemNumber) {
        outList.add([]);
      } else {
        /// Do noting
      }

      /// 最後にデータを追加
      outList.last.add(info);
    }

    return outList;
  }

  ///
  /// ブランド名の取得
  ///
  String _getThreadBrandName(
      int brandCode, int threadCode, bool isThreadColorDefault) {
    if (isThreadColorDefault == false ||
        ThreadModel.isAppliqueThreadCode(threadCode)) {
      return '';
    }

    ThreadBrandName threadBrandName =
        ThreadBrandName.getValuesByNumber(brandCode);

    return ThreadColorModel().getThreadBrandName(threadBrandName);
  }

  ///
  /// ImageSaveポープアープを開ける
  ///
  void _openImageSavePopup(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (_) => ImageSavePopup(
          onCancelClick: () => _closeImageSavePopup(context),
          onUSB1Click: (usbInfo) => _onMemoryPopupUSB1Click(context, usbInfo),
          onUSB2Click: (usbInfo) => _onMemoryPopupUSB2Click(context, usbInfo),
        ),
      ),
    );
  }

  ///
  /// カラー名の取得
  ///
  String _getThreadColorName(
      int index300, int threadCode, bool isThreadColorDefault) {
    if (isThreadColorDefault == true) {
      return '';
    }

    return ThreadColorModel().getThreadColorNameWithIndex300(index300);
  }

  ///
  /// ImageSaveポープアープを閉じる
  ///
  void _closeImageSavePopup(BuildContext context) =>
      PopupNavigator.pop(context: context);

  ///
  /// SaveポップアップのUSB1ボタンのクリック関数
  ///

  void _onMemoryPopupUSB1Click(BuildContext context, USBInfo? usbInfo) {
    if (usbInfo == null) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    assert(PatternModel().getAllPattern().isNotEmpty, "ログインしていない模様");
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// Saving中ポップアップ　開ける
    GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_USB_HOST_NOW_WRITING);

    /// model更新
    SelectInformationModel()
      ..selectedUSBPath = usbInfo.usbPath
      ..copyPDFFileToDevice().then((accessError) {
        /// メモリーアクセスエラー発生
        if (accessError != AccessError.none) {
          return HandelModel.handleMemoryAccessError(accessError);
        }

        /// Saving中ポップアップ　閉じる
        GlobalPopupRoute().resetErrorState();

        /// 画面遷移
        /// ImageSaveポープアープを閉じる
        _closeImageSavePopup(context);
      });
  }

  ///
  /// SaveポップアップのUSB2ボタンのクリック関数
  ///

  void _onMemoryPopupUSB2Click(BuildContext context, USBInfo? usbInfo) {
    if (usbInfo == null) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    assert(PatternModel().getAllPattern().isNotEmpty, "ログインしていない模様");
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// Saving中ポップアップ　開ける
    GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_USB_HOST_NOW_WRITING);

    /// model更新
    SelectInformationModel()
      ..selectedUSBPath = usbInfo.usbPath
      ..copyPDFFileToDevice().then((accessError) {
        /// メモリーアクセスエラー発生
        if (accessError != AccessError.none) {
          return HandelModel.handleMemoryAccessError(accessError);
        }

        /// Saving中ポップアップ　閉じる
        GlobalPopupRoute().resetErrorState();

        /// 画面遷移
        /// ImageSaveポープアープを閉じる
        _closeImageSavePopup(context);
      });
  }

  ///
  /// 特殊なパターンの種類を取得する
  ///
  PrintAndStitchType _getIsPrintAndStitchPatternType() {
    /// Disneyカテゴリーのタイプ
    int disneyCategoryType = 47;

    /// OnePointカテゴリーのタイプ
    int onePointCategoryType = 20;

    /// カーター・ゴットゴック選帝王とパトネンド選帝之が両方とも選ばれているかどうかを確認します。
    if (SelectModel().selectedPatternIndex != null &&
        SelectModel().selectedCategoryType != null) {
      /// カテゴリの種類が [ディズニー] の場合
      if (SelectModel().selectedCategoryType == disneyCategoryType) {
        if (SelectInformationModel()
            .getDisneyPatternAllCategoryImagesInfo()
            .firstWhere((element) => element.categoryType == disneyCategoryType)
            .isPrint[SelectModel().selectedPatternIndex!]) {
          return PrintAndStitchType.bambi;
        }
      }

      /// カテゴリの種類が OnePoint の場合
      if (SelectModel().selectedCategoryType == onePointCategoryType) {
        if (SelectInformationModel()
            .getOnePointPatternAllCategoryImagesInfo()
            .firstWhere(
                (element) => element.categoryType == onePointCategoryType)
            .isPrint[SelectModel().selectedPatternIndex!]) {
          return PrintAndStitchType.whale;
        }
      }
    }

    /// 上記の条件のいずれも満たされない場合は、通常の型が返されます
    return PrintAndStitchType.normal;
  }
}
