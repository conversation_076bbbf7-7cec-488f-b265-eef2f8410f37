import 'dart:ffi' as dart_ffi;
import 'dart:io';
import 'dart:math';

import 'package:ffi/ffi.dart' as ffi;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image/image.dart' as img;
import 'package:ph_eel_plugin/embedit_bindings.dart' show getEmbEditBindings;
import 'package:ph_eel_plugin/embedit_bindings_generated.dart'
    as embedit_bindings_generated;
import 'package:system_config/system_config.dart';

import '../../../../../panel_library.dart';
import '../../emb_library_log.dart';
import '../../interface/api_interface.dart';

final embedit_bindings_generated.EmbEditBindings _bindings =
    getEmbEditBindings();

class EmbLibraryMockImplementAndroid implements EmbLibraryAPIInterface {
  void init(WidgetRef ref) {}

  /// メモリー　 calloc
  final _embImageInfo = embedit_bindings_generated.embImageInfo_t.allocate();
  final _embImage = embedit_bindings_generated.embImg_t.allocate();
  final _realImageInfo = embedit_bindings_generated.realImageInfo_t.allocate();
  final _threadInfo = embedit_bindings_generated.threadInfo_t.allocate_p();
  dart_ffi.Pointer<dart_ffi.Uint8> _resultImgPtr = dart_ffi.nullptr;

  void _delSelectedGroupImage() {
    EmbLibraryLog.d("delSelectedGroupImage  start");
    _bindings.delSelectedGroupImage(_embImageInfo);
    EmbLibraryLog.d("delSelectedGroupImage  end");
  }

  /// libの線の色情報を削除する
  void _deleteThreadInfo() {
    EmbLibraryLog.d("deleteThreadInfo  start");
    _bindings.deleteThreadInfo(_threadInfo);
    EmbLibraryLog.d("deleteThreadInfo  end");
  }

  bool _openEmb = false;

  @override
  EmbLibraryError initEmb() {
    EmbLibraryLog.hello("initEmb");
    final errorIndex = _bindings.initEmb();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errorIndex);
    if (errCode == EmbLibraryError.EMB_NO_ERR) {
      _openEmb = true;
    }
    EmbLibraryLog.byeBye("initEmb");
    return errCode;
  }

  @override
  void closeEmbMode() {
    EmbLibraryLog.d("closeEmbMode  start");
    _bindings.closeEmbMode();
    _openEmb = false;
    EmbLibraryLog.d("closeEmbMode  end");
  }

  @override
  bool isEmbModeOpened() {
    return _openEmb;
  }

  @override
  void closeEmbModeMdc() {
    _openEmb = false;
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) selectEmb(
      int embIndex, int category, EmbBHSizeKind bhSize) {
    EmbLibraryLog.d("selectEmb  start");
    final emb = ffi.calloc<embedit_bindings_generated.MemHandle_t>();
    int error = _bindings.selectEmb(embIndex, category, bhSize.index, emb);
    MemHandle handle = emb.value.address;
    ffi.calloc.free(emb);
    EmbLibraryLog.d("selectEmb  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      handle: handle
    );
  }

  ///
  ///  6.	キルト選択
  ///
  @override
  ({EmbLibraryError errorCode, Image image, List<ThreadInfo> threadInfoList})
      getQuiltThumbnailImage(int embQuiltIndex, int quiltCategory) {
    EmbLibraryLog.hello("getQuiltThumbnailImage");
    List<ThreadInfo> threadInfoList = [];
    final threadInfoNum = ffi.calloc<dart_ffi.Int32>();
    int error = _bindings.getQuiltThumbnailImage(embQuiltIndex, quiltCategory,
        _embImageInfo, _threadInfo, threadInfoNum);

    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    Image image = Image.memory(
      Uint8List.fromList(
        _embImageInfo == dart_ffi.nullptr ||
                errorCode != EmbLibraryError.EMB_NO_ERR
            ? []
            : _embImageInfo.ref.embImage.ref.imageData
                .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
      ),
    );

    for (int index = 0; index < threadInfoNum.value; index++) {
      final libThreadInfo = (_threadInfo.value + index).ref;

      threadInfoList.add(
        ThreadInfo(
          brandCode: libThreadInfo.brandCode,
          index300: libThreadInfo.index300,
          colorRGB: Color(libThreadInfo.colorRGB).withOpacity(1),
          notSewing: libThreadInfo.notSewing,
          sewingTime: libThreadInfo.sewingTime,
          stdCode: libThreadInfo.stdCode,
          stitchNumber: libThreadInfo.stitchNumber,
          threadCode: libThreadInfo.threadCode,
          threadCodeDigit: libThreadInfo.threadCodeDigit,
          groupH: libThreadInfo.groupH.address,
          patternH: libThreadInfo.patternH.address,
          colIdx: libThreadInfo.colIdx,
        ),
      );
    }

    ffi.calloc.free(threadInfoNum);
    EmbLibraryLog.byeBye("getQuiltThumbnailImage");
    _delSelectedGroupImage();
    _deleteThreadInfo();
    return (errorCode: errorCode, image: image, threadInfoList: threadInfoList);
  }

  @override
  ({EmbColorGr colorInfo, EmbLibraryError errorCode}) getQuiltColorInfo(
      int colorNum) {
    EmbLibraryLog.d("getQuiltColorInfo  start");
    final colorInfoPointer =
        ffi.calloc<embedit_bindings_generated.EmbColorGr_t>();

    int error = _bindings.getQuiltColorInfo(colorNum, colorInfoPointer);
    EmbColorGr colorInfo = EmbColorGr(
      stdThreadCode: colorInfoPointer.ref.StdThreadCode,
      paletteBrand: colorInfoPointer.ref.paletteBrand,
      paletteCode: colorInfoPointer.ref.paletteCode,
      colorRGB: colorInfoPointer.ref.ColorRGB,
    );

    ffi.calloc.free(colorInfoPointer);
    EmbLibraryLog.d(
        "getQuiltColorInfo  end,colorNum:$colorNum,colorInfo:$colorInfo");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      colorInfo: colorInfo
    );
  }

  @override
  EmbLibraryError cancelStipple() {
    EmbLibraryLog.d("cancelStipple  start");
    int error = _bindings.cancelStipple();
    EmbLibraryLog.d("cancelStipple  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError completeStipple() {
    EmbLibraryLog.d("completeStipple  start");
    int error = _bindings.completeStipple();
    EmbLibraryLog.d("completeStipple  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError checkBeforeBorderProc() {
    EmbLibraryLog.d("checkBeforeBorderProc  start");
    int error = _bindings.checkBeforeBorderProc();
    EmbLibraryLog.d("checkBeforeBorderProc  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError checkBeforeEasyStippleMakeProc() =>
      EmbLibraryError.EMB_NO_ERR;

  @override
  EmbLibraryError checkBorderMarkStipple() {
    EmbLibraryLog.d("checkBorderMarkStipple  start");
    int error = _bindings.checkBorderMarkStipple();
    EmbLibraryLog.d("checkBorderMarkStipple  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError isEnabledSevedToExternalMemory() {
    EmbLibraryLog.d("isEnabledSevedToExternalMemory  start");
    int error = _bindings.isEnabledSevedToExternalMemory();
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("isEnabledSevedToExternalMemory  end,errorCode:$errorCode");

    return errorCode;
  }

  @override
  EmbLibraryError isEnabledSevedToInternalMemory() {
    EmbLibraryLog.d("isEnabledSevedToInternalMemory  start");
    int error = _bindings.isEnabledSevedToInternalMemory();
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("isEnabledSevedToInternalMemory  end,errorCode:$errorCode");

    return errorCode;
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) addBasting() {
    EmbLibraryLog.d("addBasting  start");
    final groupH = ffi.calloc<embedit_bindings_generated.MemHandle_t>();

    int error = _bindings.addBasting(groupH);
    MemHandle handle = groupH.value.address;
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    ffi.calloc.free(groupH);
    EmbLibraryLog.d("addBasting  end,errorCode:$errorCode");
    return (errorCode: errorCode, handle: handle);
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) addBorder(
      BorderPosition borderPos) {
    EmbLibraryLog.d("addBorder  start");
    final borderH = embedit_bindings_generated.EmbBorder_t.allocate_p();
    borderH.value = embedit_bindings_generated.EmbBorder_t.allocate();

    int error = _bindings.addBorder(borderPos.index, borderH);
    MemHandle handle = borderH.value.address;

    ffi.calloc.free(borderH);
    EmbLibraryLog.d("addBorder  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      handle: handle
    );
  }

  @override
  ({EmbLibraryError errorCode, int num}) addShufflePattern() {
    EmbLibraryLog.d("addShufflePattern  start");
    final thumbnail = ffi.calloc<dart_ffi.Int32>();

    int error = _bindings.addShufflePattern(thumbnail);
    int num = thumbnail.value;

    ffi.calloc.free(thumbnail);
    EmbLibraryLog.d("addShufflePattern  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      num: num
    );
  }

  @override
  EmbLibraryError alignEmb(int alignment) {
    EmbLibraryLog.d("alignEmb  start");
    int error = _bindings.alignEmb(alignment);
    EmbLibraryLog.d("alignEmb  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError changeColor(int r, int g, int b) {
    EmbLibraryLog.d("changeColor  start");
    int error = _bindings.changeColor(r, g, b);
    EmbLibraryLog.d("changeColor  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError changeColorByPalette(ThreadBrandName brandCode, int index) {
    EmbLibraryLog.d("changeColorByPalette  start");
    int error = _bindings.changeColorByPalette(brandCode.number, index);
    EmbLibraryLog.d("changeColorByPalette  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError changeOrderFirst() {
    EmbLibraryLog.d("changeOrderFirst  start");
    int error = _bindings.changeOrderFirst();
    EmbLibraryLog.d("changeOrderFirst  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError changeOrderLast() {
    EmbLibraryLog.d("changeOrderLast  start");
    int error = _bindings.changeOrderLast();
    EmbLibraryLog.d("changeOrderLast  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError changeOrderNext() {
    EmbLibraryLog.d("changeOrderNext  start");
    int error = _bindings.changeOrderNext();
    EmbLibraryLog.d("changeOrderNext  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError changeOrderPrevious() {
    EmbLibraryLog.d("changeOrderPrevious  start");
    int error = _bindings.changeOrderPrevious();
    EmbLibraryLog.d("changeOrderPrevious  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError changeSettingsSewing(
      MemHandle? groupH, int index, bool notSewing) {
    EmbLibraryLog.d("changeSettingsSewing  start");
    int error = _bindings.changeSettingsSweing(
      groupH == null ? dart_ffi.nullptr : dart_ffi.Pointer.fromAddress(groupH),
      0,
      index,
      notSewing,
    );
    EmbLibraryLog.d("changeSettingsSewing  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError changeSizeEmbGroup(MagType type, int step) {
    EmbLibraryLog.d("changeSizeEmbGroup  start");
    int error = _bindings.changeSizeEmbGroup(type.index, step);
    EmbLibraryLog.d("changeSizeEmbGroup  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError changeSizeEmbGroupSTB(MagType type, int step) {
    EmbLibraryLog.d("changeSizeEmbGroupSTB  start");
    int error = _bindings.changeSizeEmbGroupSTB(type.index, step);
    EmbLibraryLog.d("changeSizeEmbGroupSTB  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError resetStbMode() {
    EmbLibraryLog.d("resetStbMode  start");
    int error = _bindings.resetStbMode();
    EmbLibraryLog.d("resetStbMode  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError setStbMode() {
    EmbLibraryLog.d("setStbMode  start");
    int error = _bindings.setStbMode();
    EmbLibraryLog.d("setStbMode  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError changeSizeQuiltEmb(MagType type, int step) {
    EmbLibraryLog.d("changeSizeQuiltEmb  start   type:$type; step:$step");
    int error = _bindings.changeSizeQuiltEmb(type.index, step);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("changeSizeQuiltEmb  end  $errorCode");
    return errorCode;
  }

  @override
  EmbLibraryError saveUndoRedoFile(String fileName) {
    EmbLibraryLog.d("saveUndoRedoFile  start");
    final filePathNative = fileName.toNativeUtf8();
    int error =
        _bindings.saveUndoRedoFile(filePathNative.cast<dart_ffi.Char>());
    ffi.calloc.free(filePathNative);
    EmbLibraryLog.d("saveUndoRedoFile  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError undoRedo(String fileName) {
    EmbLibraryLog.d("undoRedo  start");
    final filePathNative = fileName.toNativeUtf8();
    int error = _bindings.undoRedo(filePathNative.cast<dart_ffi.Char>());
    ffi.calloc.free(filePathNative);
    EmbLibraryLog.d("undoRedo  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError delBorder(BorderPosition borderPos) {
    EmbLibraryLog.d("delBorder  start");
    int error = _bindings.delBorder(borderPos.index);
    EmbLibraryLog.d("delBorder  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError deleteBasting() {
    EmbLibraryLog.d("deleteBasting  start");
    int error = _bindings.deleteBasting();
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("deleteBasting  end,errorCode:$errorCode");
    return errorCode;
  }

  @override
  EmbLibraryError deleteEmb() {
    EmbLibraryLog.d("deleteEmb  start");
    int error = _bindings.deleteEmb();
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("deleteEmb  end,errorCode:$errorCode");
    return errorCode;
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) divideBorder(
      DivBorder borderPos, int index) {
    EmbLibraryLog.d("divideBorder  start");
    int error = _bindings.divideBorder(borderPos.index, index);
    EmbLibraryLog.d("divideBorder  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      handle: 0
    );
  }

  @override
  ({EmbLibraryError errorCode, List<MemHandle> handleList}) duplicateEmb() {
    EmbLibraryLog.d("duplicateEmb  start");
    List<MemHandle> handleList = [];
    final libHandelList = ffi.calloc<embedit_bindings_generated.MemHandle_t>();
    final count = ffi.calloc<dart_ffi.Int32>();

    int error = _bindings.duplicateEmb(libHandelList, count);

    var handleValueList = libHandelList.value
        as dart_ffi.Pointer<embedit_bindings_generated.MemHandle_t>;

    for (int index = 0; index < count.value; index++) {
      // print(
      //     ' -----------------duplicateEmb index:$index  NewHandel: ${libHandelList.elementAt(index).address}----------------------');
      handleList.add(handleValueList[index].address);
    }

    // NSS No.576 メモリリーク修正対応 //
    _bindings.freeMemHandle(libHandelList);

    ffi.calloc.free(libHandelList);
    ffi.calloc.free(count);
    EmbLibraryLog.d("duplicateEmb  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      handleList: handleList
    );
  }

  @override
  EmbLibraryError editSelectedApplique() {
    EmbLibraryLog.d("editSelectedApplique  start");
    int error = _bindings.editSelectedApplique();
    EmbLibraryLog.d("editSelectedApplique  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  bool embEditPositionNG() {
    EmbLibraryLog.d("embEditPositionNG  start");
    bool result = _bindings.embEditPositionNG();
    EmbLibraryLog.d("embEditPositionNG  end");
    return result;
  }

  @override
  EmbLibraryError setShuffledNewColor(int thumbNum) {
    EmbLibraryLog.d("setShuffledNewColor  start");
    int error = _bindings.setShuffledNewColor(thumbNum);
    EmbLibraryLog.d("setShuffledNewColor  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError checkColorShuffle() {
    EmbLibraryLog.d("checkColorShuffle  start");
    int error = _bindings.checkColorShuffle();
    EmbLibraryLog.d("checkColorShuffle  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  void setEmbSewNonStopSewing(bool isOneColor) {
    EmbLibraryLog.d("setEmbSewNonStopSewing  start,isOneColor:$isOneColor");
    _bindings.setEmbSewNonStopSewing(isOneColor);
    EmbLibraryLog.d("setEmbSewNonStopSewing  end");
  }

  @override
  void enterMultiSelectMode() {
    EmbLibraryLog.d("enterMultiSelectMode  start");
    _bindings.enterMultiSelectMode();
    EmbLibraryLog.d("enterMultiSelectMode  end");
  }

  @override
  void exitMultiSelectMode() {
    EmbLibraryLog.d("exitMultiSelectMode  start");
    _bindings.exitMultiSelectMode();
    EmbLibraryLog.d("exitMultiSelectMode  end");
  }

  @override
  EmbLibraryError flipEmbHorizontal() {
    EmbLibraryLog.d("flipEmbHorizontal  start");
    int error = _bindings.flipEmbHorizontal();
    EmbLibraryLog.d("flipEmbHorizontal  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  ({EmbLibraryError errorCode, BorderInfo borderInfo}) getBorderInfo(
      MemHandle borderHandle) {
    EmbLibraryLog.d("getBorderInfo  start,borderHandle:$borderHandle");
    final libBorderInfo = embedit_bindings_generated.borderInfo_t.allocate();

    int error = _bindings.getBorderInfo(
        dart_ffi.Pointer.fromAddress(borderHandle), libBorderInfo);

    BorderInfo borderInfo = BorderInfo(
      style: SSPoint(
        X: libBorderInfo.ref.style.X,
        Y: libBorderInfo.ref.style.Y,
      ),
      space: SSPoint(
        X: libBorderInfo.ref.space.X,
        Y: libBorderInfo.ref.space.Y,
      ),
      position: SSPoint(
        X: libBorderInfo.ref.position.X,
        Y: libBorderInfo.ref.position.Y,
      ),
      parentGroup: libBorderInfo.ref.parentGroup.address,
      angle: libBorderInfo.ref.angle,
      groupNum: libBorderInfo.ref.groupNum,
      mask: RectanArea(
        left: libBorderInfo.ref.mask.Left,
        right: libBorderInfo.ref.mask.Right,
        top: libBorderInfo.ref.mask.Top,
        bottom: libBorderInfo.ref.mask.Bottom,
      ),
    );

    ffi.calloc.free(libBorderInfo);

    EmbLibraryLog.d(
        "getBorderInfo  end,borderInfo: space X:${borderInfo.space.X}, Y:${borderInfo.space.Y}");

    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      borderInfo: borderInfo
    );
  }

  @override
  int getCurrentStitchNumber() {
    int result = _bindings.getCurrentStitchNumber();
    return result;
  }

  @override
  ({EmbLibraryError errorCode, int division, Image image})
      getQuiltPreviewEdgeToEdgeImage(bool frameBorder) {
    EmbLibraryLog.hello("getQuiltPreviewEdgeToEdgeImage");
    final libDivision = ffi.calloc<dart_ffi.Int32>();

    int error = _bindings.getQuiltPreviewEdgeToEdgeImage(
        frameBorder, libDivision, _embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    int division = libDivision.value;
    Image image = Image.memory(
      Uint8List.fromList(
        _embImageInfo == dart_ffi.nullptr ||
                errorCode != EmbLibraryError.EMB_NO_ERR
            ? []
            : _embImageInfo.ref.embImage.ref.imageData
                .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
      ),
    );

    ffi.calloc.free(libDivision);
    EmbLibraryLog.byeBye("getQuiltPreviewEdgeToEdgeImage");
    _delSelectedGroupImage();
    return (errorCode: errorCode, division: division, image: image);
  }

  @override
  ({
    int row,
    int piece,
    EmbroiderySize embroiderySize,
    EmbLibraryError errorCode
  }) getSewingInfoEdgeToEdge() {
    EmbLibraryLog.d("getSewingInfoEdgeToEdge  start");
    int row = 0;
    int piece = 0;
    int height = 0;
    int width = 0;

    final edgeToEdgeInfo =
        embedit_bindings_generated.edgeToEdgeInfo_t.allocate();

    int error = _bindings.getSewingInfoEdgeToEdge(edgeToEdgeInfo);

    row = edgeToEdgeInfo.ref.row;
    piece = edgeToEdgeInfo.ref.piece;
    height = edgeToEdgeInfo.ref.heightMM;
    width = edgeToEdgeInfo.ref.widthMM;

    ffi.calloc.free(edgeToEdgeInfo);
    EmbLibraryLog.d("getSewingInfoEdgeToEdge  end");
    return (
      row: row,
      piece: piece,
      embroiderySize: EmbroiderySize(height: height, width: width),
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error)
    );
  }

  @override
  ({EmbLibraryError errorCode, List<BrandColorInfo> brandColorInfoList})
      getThreadColorTable(ThreadBrandName brandCode) {
    EmbLibraryLog.d("getThreadColorTable  start");
    List<BrandColorInfo> list = [];
    final tcTable = ffi
        .calloc<dart_ffi.Pointer<embedit_bindings_generated.BrandColorTbl>>();
    final recordNum = ffi.calloc<dart_ffi.Uint32>();
    int error =
        _bindings.getThreadColorTable(brandCode.number, tcTable, recordNum);

    for (int index = 0; index < recordNum.value; index++) {
      final brandColor = tcTable.value + index;
      list.add(BrandColorInfo(
        r: brandColor.ref.r,
        g: brandColor.ref.g,
        b: brandColor.ref.b,
        threadCode: brandColor.ref.code,
        index300: brandColor.ref.index300,
        stdCode: brandColor.ref.stdCode,
      ));
    }
    ffi.calloc.free(tcTable);
    ffi.calloc.free(recordNum);
    EmbLibraryLog.d("getThreadColorTable  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      brandColorInfoList: list
    );
  }

  @override
  ({EmbLibraryError errorCode, List<BrandColorInfo> brandColorInfoList})
      getThreadColor61Table(ThreadBrandName brandCode) {
    EmbLibraryLog.d("getThreadColor61Table  start");
    List<BrandColorInfo> list = [];
    final tcTable = ffi
        .calloc<dart_ffi.Pointer<embedit_bindings_generated.BrandColorTbl>>();
    int error = _bindings.getThreadColor61Table(brandCode.number, tcTable);

    for (int index = 0; index < 61; index++) {
      final brandColor = tcTable.value + index;
      list.add(BrandColorInfo(
        r: brandColor.ref.r,
        g: brandColor.ref.g,
        b: brandColor.ref.b,
        threadCode: brandColor.ref.code,
        index300: brandColor.ref.index300,
        stdCode: brandColor.ref.stdCode,
      ));
    }
    ffi.calloc.free(tcTable);
    EmbLibraryLog.d("getThreadColor61Table  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      brandColorInfoList: list
    );
  }

  @override
  ({EmbLibraryError errorCode, int density}) getEmbDensity() {
    EmbLibraryLog.d("getEmbDensity  start");
    final pDensity = ffi.calloc<dart_ffi.Uint8>();
    int error = _bindings.getEmbDensity(pDensity);
    int density = pDensity.value;

    ffi.calloc.free(pDensity);
    EmbLibraryLog.d("getEmbDensity  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      density: density
    );
  }

  @override
  bool getEmbMarkPatCntAutoCustom() {
    EmbLibraryLog.d("getEmbMarkPatCntAutoCustom  start");
    bool result = _bindings.getEmbMarkPatCntAutoCustom();
    EmbLibraryLog.d("getEmbMarkPatCntAutoCustom  end");
    return result;
  }

  @override
  ({EmbLibraryError errorCode, EmbSewingInfo embSewingInfo}) getEmbSewInfo() {
    EmbLibraryLog.d("getEmbSewInfo  start");
    final sewingInfo = ffi.calloc<embedit_bindings_generated.embSewingInfo_t>();

    int error = _bindings.getEmbSewInfo(sewingInfo);
    EmbSewingInfo embSewingInfo = EmbSewingInfo(
      width: sewingInfo.ref.width,
      height: sewingInfo.ref.height,
      needleCurrent: sewingInfo.ref.needleCurrent,
      needleAll: sewingInfo.ref.needleAll,
      threadCurrent: sewingInfo.ref.threadCurrent,
      threadAll: sewingInfo.ref.threadAll,
      sewTimeCurrent: sewingInfo.ref.sewTimeCurrent,
      sewTimeAll: sewingInfo.ref.sewTimeAll,
      positionX: sewingInfo.ref.positionX,
      positionY: sewingInfo.ref.positionY,
      angle: sewingInfo.ref.angle,
    );

    ffi.calloc.free(sewingInfo);
    EmbLibraryLog.d("getEmbSewInfo  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      embSewingInfo: embSewingInfo
    );
  }

  @override
  ({EmbLibraryError errorCode, List<ThreadInfo> threadInfoList})
      getEmbEditThreadInfo(MemHandle groupH) {
    EmbLibraryLog.d("getEmbEditThreadInfo  start");
    List<ThreadInfo> threadInfoList = [];

    final threadInfoNum = ffi.calloc<dart_ffi.Int32>();

    int error = _bindings.getEmbEditThreadInfo(
      dart_ffi.Pointer.fromAddress(groupH),
      _threadInfo,
      threadInfoNum,
    );

    for (int index = 0; index < threadInfoNum.value; index++) {
      final libThreadInfo = (_threadInfo.value + index).ref;

      threadInfoList.add(
        ThreadInfo(
          brandCode: libThreadInfo.brandCode,
          index300: libThreadInfo.index300,
          colorRGB: Color(libThreadInfo.colorRGB).withOpacity(1),
          notSewing: libThreadInfo.notSewing,
          sewingTime: libThreadInfo.sewingTime,
          stdCode: libThreadInfo.stdCode,
          stitchNumber: libThreadInfo.stitchNumber,
          threadCode: libThreadInfo.threadCode,
          threadCodeDigit: libThreadInfo.threadCodeDigit,
          groupH: libThreadInfo.groupH.address,
          patternH: libThreadInfo.patternH.address,
          colIdx: libThreadInfo.colIdx,
        ),
      );
    }

    ffi.calloc.free(threadInfoNum);
    EmbLibraryLog.d("getEmbEditThreadInfo  end");

    _deleteThreadInfo();
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      threadInfoList: threadInfoList
    );
  }

  @override
  ({
    EmbLibraryError errorCode,
    int threadInfoCurrentIndex,
    List<ThreadInfo> threadInfoList,
    int threadInfoNum
  }) getEmbSewingThreadInfoAll() {
    List<ThreadInfo> threadInfoList = [];

    final libThreadInfoNum = ffi.calloc<dart_ffi.Int32>();
    final libThreadInfoCurIdx = ffi.calloc<dart_ffi.Int32>();

    int errorIndex = _bindings.getEmbSewingThreadInfoAll(
      _threadInfo,
      libThreadInfoNum,
      libThreadInfoCurIdx,
    );
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(errorIndex);
    if (errorCode != EmbLibraryError.EMB_NO_ERR) {
      EmbLibraryLog.e(
          "getEmbSewingThreadInfoAll \nerrorCode:$errorCode \nnum:${libThreadInfoNum.value}\nlibThreadInfoCurIdx:${libThreadInfoCurIdx.value}");

      return (
        errorCode: errorCode,
        threadInfoList: [],
        threadInfoNum: 0,
        threadInfoCurrentIndex: 0,
      );
    }

    for (int index = 0; index < libThreadInfoNum.value; index++) {
      final libThreadInfo = (_threadInfo.value + index).ref;
      threadInfoList.add(
        ThreadInfo(
          brandCode: libThreadInfo.brandCode,
          index300: libThreadInfo.index300,
          colorRGB: Color(libThreadInfo.colorRGB).withOpacity(1),
          notSewing: libThreadInfo.notSewing,
          sewingTime: libThreadInfo.sewingTime,
          stdCode: libThreadInfo.stdCode,
          stitchNumber: libThreadInfo.stitchNumber,
          threadCode: libThreadInfo.threadCode,
          threadCodeDigit: libThreadInfo.threadCodeDigit,
          groupH: libThreadInfo.groupH.address,
          patternH: libThreadInfo.patternH.address,
          colIdx: libThreadInfo.colIdx,
        ),
      );
    }

    int threadInfoNum = libThreadInfoNum.value;
    int threadInfoCurrentIndex = libThreadInfoCurIdx.value;

    ffi.calloc.free(libThreadInfoNum);
    ffi.calloc.free(libThreadInfoCurIdx);

    _deleteThreadInfo();
    return (
      errorCode: errorCode,
      threadInfoList: threadInfoList,
      threadInfoNum: threadInfoNum,
      threadInfoCurrentIndex: threadInfoCurrentIndex,
    );
  }

  @override
  ({EmbLibraryError errorCode, List<MemHandle> handleList})
      getGroupHandleAll() {
    EmbLibraryLog.d("getGroupHandleAll  start");
    List<MemHandle> handleList = [];
    final libHandelList = ffi.calloc<embedit_bindings_generated.MemHandle_t>();
    final count = ffi.calloc<dart_ffi.Int32>();

    int error = _bindings.getGroupHandleAll(libHandelList, count);

    var handleValueList = libHandelList.value
        as dart_ffi.Pointer<embedit_bindings_generated.MemHandle_t>;

    for (int index = 0; index < count.value; index++) {
      handleList.add(handleValueList[index].address);
    }

    // NSS No.576 メモリリーク修正対応 //
    _bindings.freeMemHandle(libHandelList);
    ffi.calloc.free(libHandelList);
    ffi.calloc.free(count);
    EmbLibraryLog.d("getGroupHandleAll  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      handleList: handleList
    );
  }

  @override
  ({EmbLibraryError errorCode, List<MemHandle> handleList})
      getGroupHandleInBorder(MemHandle borderHandle) {
    EmbLibraryLog.d("getGroupHandleInBorder  start");
    final grpHArray = ffi.calloc<embedit_bindings_generated.MemHandle_t>();
    final handleNum = ffi.calloc<dart_ffi.Int32>();

    EmbLibraryError error = EmbLibraryErrorExtension.getValuesByIndex(
        _bindings.getGroupHandleInBorder(
            dart_ffi.Pointer.fromAddress(borderHandle), grpHArray, handleNum));

    List<MemHandle> groupHandleList = [];

    var handleValueList = grpHArray.value
        as dart_ffi.Pointer<embedit_bindings_generated.MemHandle_t>;

    for (int index = 0; index < handleNum.value; index++) {
      groupHandleList.add(handleValueList[index].address);
    }

    // NSS No.576 メモリリーク修正対応 //
    _bindings.freeMemHandle(grpHArray);

    ffi.calloc.free(grpHArray);
    ffi.calloc.free(handleNum);
    EmbLibraryLog.d("getGroupHandleInBorder  end");
    return (errorCode: error, handleList: groupHandleList);
  }

  @override
  ({EmbLibraryError errorCode, Image image}) getInfoImage(
      bool backDisplay, int backColor) {
    EmbLibraryLog.d("getInfoImage  start");

    int error = _bindings.getInfoImage(backDisplay, backColor, _embImage);
    Image image = Image.memory(
      Uint8List.fromList(
        _embImage.ref.imageData.asTypedList(_embImage.ref.imageSize),
      ),
    );

    _bindings.delInfoImage(_embImage.ref);
    EmbLibraryLog.d("getInfoImage  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      image: image
    );
  }

  @override
  ({EmbLibraryError errorCode, int num}) getMaxNumColorShuffle(
      ThreadBrandName brandCode) {
    EmbLibraryLog.d("getMaxNumColorShuffle  start");
    final colors = ffi.calloc<dart_ffi.Int16>();

    int error = _bindings.getMaxNumColorShuffle(brandCode.index, colors);
    int num = colors.value;

    ffi.calloc.free(colors);
    EmbLibraryLog.d("getMaxNumColorShuffle  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      num: num
    );
  }

  @override
  ({EmbLibraryError errorCode, SSPoint position}) getNeedlePosition(
      int stitch) {
    EmbLibraryLog.d("getNeedlePosition  start");
    final libPosition = embedit_bindings_generated.SSPoint_t.allocate();

    int error = _bindings.getNeedlePosition(stitch, libPosition);

    SSPoint position = SSPoint(X: libPosition.ref.X, Y: libPosition.ref.Y);

    ffi.calloc.free(libPosition);
    EmbLibraryLog.d("getNeedlePosition  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      position: position
    );
  }

  @override
  ({EmbLibraryError errorCode, SSPoint position}) getNeedleStartPosition(
      int position) {
    EmbLibraryLog.d("getNeedleStartPosition  start");
    final libPosition = embedit_bindings_generated.SSPoint_t.allocate();

    int error = _bindings.getNeedleStartPosition(position, libPosition);

    SSPoint resultPosition =
        SSPoint(X: libPosition.ref.X, Y: libPosition.ref.Y);

    ffi.calloc.free(libPosition);
    EmbLibraryLog.d("getNeedleStartPosition  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      position: resultPosition
    );
  }

  @override
  SSPoint? getCurrentNeedlePosition() {
    EmbLibraryLog.hello("getCurrentNeedlePosition");
    final libPosition = embedit_bindings_generated.SSPoint_t.allocate();
    final error = _bindings.getCurrentNeedlePosition(libPosition);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    SSPoint? position = (errorCode == EmbLibraryError.EMB_NO_ERR)
        ? SSPoint(X: libPosition.ref.X, Y: libPosition.ref.Y)
        : null;

    ffi.calloc.free(libPosition);
    EmbLibraryLog.byeBye("getCurrentNeedlePosition");
    return position;
  }

  @override
  int getOutColorDepth() {
    EmbLibraryLog.d("getOutColorDepth  start");
    int outColorDepth = _bindings.getOutColorDepth();
    EmbLibraryLog.d("getOutColorDepth  end");
    return outColorDepth;
  }

  @override
  ({EmbLibraryError errorCode, int num}) getPartsNumSavedFile(String file) {
    EmbLibraryLog.d("getPartsNumSavedFile  start");

    final thumbnail = ffi.calloc<dart_ffi.Int32>();
    final filePathNative = file.toNativeUtf8();
    int error = _bindings.getPartsNumSavedFile(
        filePathNative.cast<dart_ffi.Char>(), thumbnail);
    int value = thumbnail.value;
    ffi.calloc.free(filePathNative);
    ffi.calloc.free(thumbnail);
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      num: value
    );
  }

  @override
  ({EmbLibraryError errorCode, Image image}) getQuiltImage(
      int embQuiltIndex, int quiltCategory, int color, int flip) {
    EmbLibraryLog.hello("getQuiltImage");

    int error = _bindings.getQuiltImage(
        embQuiltIndex, quiltCategory, color, flip, _embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    Image image = Image.memory(
      Uint8List.fromList(
        _embImageInfo == dart_ffi.nullptr ||
                errorCode != EmbLibraryError.EMB_NO_ERR
            ? []
            : _embImageInfo.ref.embImage.ref.imageData
                .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
      ),
    );

    EmbLibraryLog.byeBye("getQuiltImage");
    _delSelectedGroupImage();
    return (errorCode: errorCode, image: image);
  }

  @override
  ({EmbLibraryError errorCode, int division, Image image})
      getQuiltPreviewExtensionImage(bool frameBorder) {
    EmbLibraryLog.hello("getQuiltPreviewExtensionImage");
    final libDivision = ffi.calloc<dart_ffi.Int32>();

    int error = _bindings.getQuiltPreviewExtensionImage(
        frameBorder, libDivision, _embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    int division = libDivision.value;
    Image image = Image.memory(
      Uint8List.fromList(
        _embImageInfo == dart_ffi.nullptr ||
                errorCode != EmbLibraryError.EMB_NO_ERR
            ? []
            : _embImageInfo.ref.embImage.ref.imageData
                .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
      ),
    );

    ffi.calloc.free(libDivision);
    EmbLibraryLog.byeBye("getQuiltPreviewExtensionImage");
    _delSelectedGroupImage();
    return (errorCode: errorCode, division: division, image: image);
  }

  @override
  ({EmbLibraryError errorCode, int division, Image image}) getQuiltPreviewImage(
      bool dividingLine) {
    EmbLibraryLog.hello("getQuiltPreviewImage");
    final libDivision = ffi.calloc<dart_ffi.Int32>();

    int error = _bindings.getQuiltPreviewImage(
        dividingLine, libDivision, _embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    int division = libDivision.value;
    Image image = Image.memory(
      Uint8List.fromList(
        _embImageInfo == dart_ffi.nullptr ||
                errorCode != EmbLibraryError.EMB_NO_ERR
            ? []
            : _embImageInfo.ref.embImage.ref.imageData
                .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
      ),
    );

    ffi.calloc.free(libDivision);
    EmbLibraryLog.byeBye("getQuiltPreviewImage");
    _delSelectedGroupImage();
    return (errorCode: errorCode, division: division, image: image);
  }

  @override
  ({EmbLibraryError errorCode, QuiltSashesLimit limit}) getQuiltSizeLimit(
      QuiltSplitType quiltType, EmbQuiltSashesFrameType frameType) {
    EmbLibraryLog.d("getQuiltSizeLimit  start,frameType:$frameType");
    final sizeLimit = embedit_bindings_generated.quiltSashesLimit_t.allocate();

    int error = _bindings.getQuiltSizeLimit(
        quiltType.index, frameType.index, sizeLimit);

    QuiltSashesLimit limit = QuiltSashesLimit(
      widthMax: sizeLimit.ref.widthMax,
      widthMin: sizeLimit.ref.widthMin,
      heightMax: sizeLimit.ref.heightMax,
      heightMin: sizeLimit.ref.heightMin,
      bandMax: sizeLimit.ref.bandMax,
      bandMin: sizeLimit.ref.bandMin,
    );

    ffi.calloc.free(sizeLimit);
    EmbLibraryLog.d("getQuiltSizeLimit  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      limit: limit
    );
  }

  @override
  ({EmbLibraryError errorCode, Image image, List<RealImg> realImgList})
      getRealImage(bool isMDC, bool displayPosition, int scale) {
    EmbLibraryLog.d("getRealImage  start");
    List<RealImg> realImgList = [];

    int error = _bindings.getRealImage(
        isMDC == false, false, displayPosition, scale, _realImageInfo);

    for (int index = 0; index < _realImageInfo.ref.pixNum; index++) {
      var pixInfo = _realImageInfo.ref.pix + index;
      realImgList.add(RealImg(
        X: pixInfo.ref.x,
        Y: pixInfo.ref.y,
        rgb: pixInfo.ref.pixRGB,
      ));
    }

    Image image = Image.memory(
      Uint8List.fromList(
        _realImageInfo.ref.realImage.imageData
            .asTypedList(_realImageInfo.ref.realImage.imageSize),
      ),
    );

    _bindings.delRealImage(_realImageInfo.ref);
    EmbLibraryLog.d("getRealImage  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      image: image,
      realImgList: realImgList
    );
  }

  @override
  ({EmbLibraryError errorCode, List<EmbStitchPos> needlePos, int pointNum})
      getRealImageLight(bool allPattern) {
    EmbLibraryLog.hello("getRealImageLight");
    List<EmbStitchPos> realImgList = [];
    final needlePos = embedit_bindings_generated.embstitchPos_t.allocate_p();
    final pointNum = ffi.calloc<dart_ffi.Int32>();
    final errIndex =
        _bindings.getRealImageLight(allPattern, needlePos, pointNum);
    final num = pointNum.value;
    final needlePosPtr = needlePos.value;
    for (int index = 0; index < num; index++) {
      final pixInfo = needlePosPtr + index;
      final pos = pixInfo.ref.pos;
      int x = pos.X;
      int y = pos.Y;
      int code = pixInfo.ref.code;
      realImgList.add(
        EmbStitchPos(
          pos: SSPoint(X: x, Y: y),
          code: code,
        ),
      );
    }
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    ffi.calloc.free(pointNum);
    EmbLibraryLog.byeBye("getRealImageLight $errCode");

    EmbLibraryLog.hello("delRealImageLight");
    _bindings.delRealImageLight(needlePos);
    EmbLibraryLog.byeBye("delRealImageLight :  $errCode");
    return (errorCode: errCode, needlePos: realImgList, pointNum: num);
  }

  @override
  ({EmbLibraryError errorCode, int imageNum, List<Uint8List> imageDataList})
      getSelectedGroupARGBImage(
    MemHandle memHandle,
    int centerType,
    int scale,
    bool imageType,
  ) {
    EmbLibraryLog.hello(
        "getSelectedGroupARGBImage centerType:$centerType ; scale:$scale ; imageType:$imageType");

    int error = _bindings.getSelectedGroupARGBImage(
      dart_ffi.Pointer.fromAddress(memHandle),
      centerType,
      scale,
      imageType,
      _embImageInfo,
    );
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    List<Uint8List> imageDataList = [];
    int imageNum = _embImageInfo.ref.imageNum;
    if (_embImageInfo == dart_ffi.nullptr ||
        errorCode != EmbLibraryError.EMB_NO_ERR) {
      imageNum = 0;
      imageDataList = [];
    } else {
      try {
        for (var index = 0; index < imageNum; index++) {
          imageDataList.add(
            Uint8List.fromList(
              (_embImageInfo.ref.embImage + index).ref.imageData.asTypedList(
                  (_embImageInfo.ref.embImage + index).ref.imageSize),
            ),
          );
        }
      } catch (e, m) {
        imageDataList = [];
        EmbLibraryLog.exception(e, m);
      }
    }
    EmbLibraryLog.byeBye("getSelectedGroupARGBImage errorCode:$errorCode");
    _delSelectedGroupImage();
    return (
      errorCode: errorCode,
      imageNum: imageNum,
      imageDataList: imageDataList
    );
  }

  @override
  ({EmbLibraryError errorCode, Image image}) getShuffledImage(
      int thumbNum, int type) {
    EmbLibraryLog.hello("getShuffledImage");

    int error = _bindings.getShuffledImage(thumbNum, type, _embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    Image image = Image.memory(
      Uint8List.fromList(
        _embImageInfo == dart_ffi.nullptr ||
                errorCode != EmbLibraryError.EMB_NO_ERR
            ? []
            : _embImageInfo.ref.embImage.ref.imageData
                .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
      ),
    );

    EmbLibraryLog.byeBye("getShuffledImage");
    _delSelectedGroupImage();
    return (errorCode: errorCode, image: image);
  }

  @override
  ({EmbLibraryError errorCode, String str}) getThreadColorName(
      int brandCode, int threadCode, String pRGBCode) {
    EmbLibraryLog.d("getThreadColorName  start");
    final libPRGBCode = ffi.calloc<dart_ffi.UnsignedChar>();
    final libColorName = ffi.calloc<dart_ffi.Pointer<dart_ffi.UnsignedShort>>();

    libPRGBCode.value = 0;
    int error = _bindings.getThreadColorName(
        brandCode, threadCode, libPRGBCode, libColorName);

    ffi.calloc.free(libColorName);
    ffi.calloc.free(libPRGBCode);
    EmbLibraryLog.d("getThreadColorName  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      str: ""
    );
  }

  @override
  ({EmbLibraryError errorCode, SavedFileInfo? fileInfo, Image? image})
      getThumbnailPartsSavedFile(
          String file, ThumbnailSize size, int index, bool transparent) {
    EmbLibraryLog.hello("getThumbnailPartsSavedFile");
    final param = embedit_bindings_generated.savedFileInfo_t.allocate();
    final filePathNative = file.toNativeUtf8();

    int error = _bindings.getThumbnailPartsSavedFile(
      filePathNative.cast<dart_ffi.Char>(),
      size.index,
      index,
      transparent,
      param,
      _embImageInfo,
    );
    ffi.calloc.free(filePathNative);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    if (errorCode != EmbLibraryError.EMB_NO_ERR ||
        _embImageInfo == dart_ffi.nullptr) {
      ffi.calloc.free(param);
      EmbLibraryLog.byeBye("getThumbnailPartsSavedFile");
      _delSelectedGroupImage();
      return (errorCode: errorCode, fileInfo: null, image: null);
    } else {
      SavedFileInfo outParam = SavedFileInfo(
        allParts: param.ref.allParts,
        partsX: param.ref.partsX,
        partsY: param.ref.partsY,
        kind: ETLKind.values[param.ref.kind],
        maskSize: Length(
          height: param.ref.maskSize.x,
          weight: param.ref.maskSize.y,
        ),
        partsNum: param.ref.partsNum,
        partsPosition: Position(
          x: param.ref.partsPosition.x,
          y: param.ref.partsPosition.y,
        ),
        thumbnailSize: Length(
          height: param.ref.thumbnailSize.x,
          weight: param.ref.thumbnailSize.y,
        ),
      );

      Image image = Image.memory(
        Uint8List.fromList(
          _embImageInfo.ref.embImage.ref.imageData
              .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
        ),
      );

      ffi.calloc.free(param);
      EmbLibraryLog.byeBye("getThumbnailPartsSavedFile");
      _delSelectedGroupImage();
      return (errorCode: errorCode, fileInfo: outParam, image: image);
    }
  }

  @override
  ({EmbLibraryError errorCode, SavedFileInfo? fileInfo, Image? image})
      getThumbnailSavedFile(String file, ThumbnailSize size, bool transparent) {
    EmbLibraryLog.hello("getThumbnailSavedFile file:$file");
    final param = embedit_bindings_generated.savedFileInfo_t.allocate();
    final filePathNative = file.toNativeUtf8();

    int error = _bindings.getThumbnailSavedFile(
      filePathNative.cast<dart_ffi.Char>(),
      size.index,
      transparent,
      param,
      _embImageInfo,
    );
    ffi.calloc.free(filePathNative);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    if (errorCode != EmbLibraryError.EMB_NO_ERR) {
      ffi.calloc.free(param);
      EmbLibraryLog.byeBye("getThumbnailSavedFile errorCode:$errorCode");
      _delSelectedGroupImage();
      return (errorCode: errorCode, fileInfo: null, image: null);
    } else {
      SavedFileInfo outParam = SavedFileInfo(
        allParts: param.ref.allParts,
        partsX: param.ref.partsX,
        partsY: param.ref.partsY,
        kind: ETLKind.values[param.ref.kind],
        maskSize: Length(
          height: param.ref.maskSize.y,
          weight: param.ref.maskSize.x,
        ),
        partsNum: param.ref.partsNum,
        partsPosition: Position(
          x: param.ref.partsPosition.x,
          y: param.ref.partsPosition.y,
        ),
        thumbnailSize: Length(
          height: param.ref.thumbnailSize.x,
          weight: param.ref.thumbnailSize.y,
        ),
      );
      Image image = Image.memory(
        Uint8List.fromList(
          errorCode != EmbLibraryError.EMB_NO_ERR ||
                  _embImageInfo == dart_ffi.nullptr
              ? []
              : _embImageInfo.ref.embImage.ref.imageData
                  .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
        ),
      );

      ffi.calloc.free(param);
      EmbLibraryLog.byeBye("getThumbnailSavedFile");
      _delSelectedGroupImage();
      return (errorCode: errorCode, fileInfo: outParam, image: image);
    }
  }

  @override
  EmbLibraryError goAppliquePartsSelection() {
    EmbLibraryLog.d("goAppliquePartsSelection  start");
    int error = _bindings.goAppliquePartsSelection();
    EmbLibraryLog.d("goAppliquePartsSelection  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  ({EmbLibraryError errorCode, MemHandle borderHandle})
      groupingMultiplePatterns() {
    EmbLibraryLog.d("groupingMultiplePatterns  start");

    final borderH = embedit_bindings_generated.EmbBorder_t.allocate_p();
    borderH.value = embedit_bindings_generated.EmbBorder_t.allocate();

    EmbLibraryError error = EmbLibraryErrorExtension.getValuesByIndex(
        _bindings.groupingMultiplePatterns(borderH));
    MemHandle borderHandle = borderH.value.address;

    ffi.calloc.free(borderH);

    EmbLibraryLog.d("groupingMultiplePatterns  end");
    return (errorCode: error, borderHandle: borderHandle);
  }

  @override
  bool isEmbCustomDesign() {
    EmbLibraryLog.d("isEmbCustomDesign  start");
    final pResult = ffi.calloc<dart_ffi.Bool>();
    _bindings.isEmbCustomDesign(pResult);
    bool result = pResult.value;
    ffi.calloc.free(pResult);
    EmbLibraryLog.d("isEmbCustomDesign  end,$result");
    return result;
  }

  @override
  bool isEmbSewOneColorNG() {
    EmbLibraryLog.d("isEmbSewOneColorNG  start");
    bool isEmbSewOneColorNG = _bindings.isEmbSewOneColorNG();
    EmbLibraryLog.d("isEmbSewOneColorNG  end,$isEmbSewOneColorNG");
    return isEmbSewOneColorNG;
  }

  @override
  bool isEmbSewOneColorON() {
    EmbLibraryLog.d("isEmbSewOneColorON  start");
    bool isEmbSewOneColorON = _bindings.isEmbSewOneColorON();
    EmbLibraryLog.d("isEmbSewOneColorON  end,$isEmbSewOneColorON");
    return isEmbSewOneColorON;
  }

  @override
  int setEmbSewPatternConnectReserve() {
    EmbLibraryLog.d("setEmbSewPatternConnectReserve  start");
    final result = _bindings.setEmbSewPatternConnectReserve();
    EmbLibraryLog.d("setEmbSewPatternConnectReserve  end  result : $result");
    return result;
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) makeApplique() {
    EmbLibraryLog.d("makeApplique  start");

    final memHandle = ffi.calloc<embedit_bindings_generated.MemHandle_t>();

    int error = _bindings.makeApplique(memHandle);

    MemHandle handle = memHandle.value.address;
    ffi.calloc.free(memHandle);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("makeApplique  end,errorCode:$errorCode");
    return (errorCode: errorCode, handle: handle);
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle, Image image})
      makeAppliqueSelectedParts(AppliquePartsParam param) {
    EmbLibraryLog.hello("makeAppliqueSelectedParts");
    final memHandle = ffi.calloc<embedit_bindings_generated.MemHandle_t>();
    final libParam = embedit_bindings_generated.appliquePartsParam_t.allocate();

    libParam.ref.outlineType = param.outlineType.index;
    libParam.ref.satinType = param.satinType.index;
    libParam.ref.runType = param.runType.index;
    libParam.ref.stitchWidth = param.stitchWidth.index;
    libParam.ref.stitchDensity = param.stitchDensity.index;
    libParam.ref.distance = param.distance;
    libParam.ref.texture = param.texture;

    int error =
        _bindings.makeAppliqueSelectedParts(libParam, memHandle, _embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    MemHandle handle = memHandle.value.address;
    Image image = Image.memory(
      Uint8List.fromList(
        _embImageInfo == dart_ffi.nullptr ||
                errorCode != EmbLibraryError.EMB_NO_ERR
            ? []
            : _embImageInfo.ref.embImage.ref.imageData
                .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
      ),
    );

    ffi.calloc.free(memHandle);
    ffi.calloc.free(libParam);
    EmbLibraryLog.byeBye("makeAppliqueSelectedParts");
    _delSelectedGroupImage();
    return (errorCode: errorCode, handle: handle, image: image);
  }

  @override
  ({EmbLibraryError errorCode, Uint8List imageDate}) makeOutline(
      int distance, bool inside, bool back) {
    EmbLibraryLog.hello("makeOutline");

    int error = _bindings.makeOutline(distance, inside, back, _embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    Uint8List image = Uint8List.fromList(
      _embImageInfo == dart_ffi.nullptr ||
              errorCode != EmbLibraryError.EMB_NO_ERR
          ? []
          : _embImageInfo.ref.embImage.ref.imageData
              .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
    );

    EmbLibraryLog.byeBye("makeOutline");
    _delSelectedGroupImage();
    return (errorCode: errorCode, imageDate: image);
  }

  @override
  EmbLibraryError initMakingEasyStipple() {
    EmbLibraryLog.hello("initMakingEasyStipple");
    final errIndex = _bindings.initMakingEasyStipple();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("initMakingEasyStipple");
    return errCode;
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) makeStipple(
      StippleType type) {
    EmbLibraryLog.d("makeStipple  start");
    final grpH = ffi.calloc<embedit_bindings_generated.MemHandle_t>();
    int error = _bindings.makeStipple(type.index, grpH);
    MemHandle handle = grpH.value.address;

    ffi.calloc.free(grpH);
    EmbLibraryLog.d("makeStipple  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      handle: handle
    );
  }

  @override
  EmbLibraryError moveEmb(int dirX, int dirY) {
    EmbLibraryLog.d("moveEmb  start");
    int error = _bindings.moveEmb(dirX, dirY);
    EmbLibraryLog.d("moveEmb  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError moveEmbCenter() {
    EmbLibraryLog.d("moveEmbCenter  start");
    int error = _bindings.moveEmbCenter();
    EmbLibraryLog.d("moveEmbCenter  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError moveQuiltEmb(int dirX, int dirY, bool flgLongPress) {
    EmbLibraryLog.d(
        "moveQuiltEmb  start dirX:$dirX; dirY:$dirY flgLongPress:$flgLongPress");
    int error = _bindings.moveQuiltEmb(dirX, dirY, flgLongPress);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("moveQuiltEmb  end  $errorCode");
    return errorCode;
  }

  @override
  EmbLibraryError prepareAddEmb() {
    EmbLibraryLog.d("prepareAddEmb  start");
    int error = _bindings.prepareAddEmb();
    EmbLibraryLog.d("prepareAddEmb  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError rotateEmb(int angle) {
    EmbLibraryLog.d("rotateEmb  start");
    int error = _bindings.rotateEmb(angle);
    EmbLibraryLog.d("rotateEmb  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError rotateQuiltEmb(int angle) {
    EmbLibraryLog.d("rotateQuiltEmb  start  angle:$angle");
    int error = _bindings.rotateQuiltEmb(angle);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("rotateQuiltEmb  end  $errorCode");
    return errorCode;
  }

  @override
  EmbLibraryError setEmbDensity(MemHandle groupH, int density) {
    EmbLibraryLog.d("setEmbDensity  start");
    int error =
        _bindings.setEmbDensity(dart_ffi.Pointer.fromAddress(groupH), density);
    EmbLibraryLog.d("setEmbDensity  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError saveEmbData(String fileName) {
    EmbLibraryLog.hello("saveEmbData");
    final filePathNative = fileName.toNativeUtf8();
    final error = _bindings.saveEmbData(filePathNative.cast<dart_ffi.Char>());
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    ffi.calloc.free(filePathNative);
    EmbLibraryLog.byeBye("saveEmbData");
    return errorCode;
  }

  @override
  EmbLibraryError saveEmbResumeData(String fileName) {
    EmbLibraryLog.hello("saveEmbResumeData");
    final filePathNative = fileName.toNativeUtf8();
    final error =
        _bindings.saveEmbResumeData(filePathNative.cast<dart_ffi.Char>());
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    ffi.calloc.free(filePathNative);
    EmbLibraryLog.byeBye("saveEmbResumeData");
    return errorCode;
  }

  @override
  EmbLibraryError saveOutline(String file) {
    EmbLibraryLog.d("saveOutline  start  file:$file");

    final filePathNative = file.toNativeUtf8();
    int error = _bindings.saveOutline(filePathNative.cast<dart_ffi.Char>());
    ffi.calloc.free(filePathNative);

    EmbLibraryLog.d("saveOutline  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError saveQuiltData(
      QuiltSplitType type, int sewType, String fileName) {
    EmbLibraryLog.d("saveQuiltData  start");
    final filePathNative = fileName.toNativeUtf8();
    // print(
    //     ' ------------------------------  saveQuiltData $fileName----------------------');
    int error = _bindings.saveQuiltData(
        type.index, sewType, filePathNative.cast<dart_ffi.Char>());
    // print(
    //     ' ------------------------------  saveQuiltData ${EmbLibraryErrorExtension.getValuesByIndex(error)}-----------------------');
    ffi.calloc.free(filePathNative);
    EmbLibraryLog.d("saveQuiltData  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  ({EmbLibraryError errorCode, Image image}) selectAppliqueParts(
      MemHandle groupH, int index, bool selection) {
    EmbLibraryLog.hello("selectAppliqueParts");

    // print(
    //     '------------------  selectAppliqueParts  index $index  selection $selection ------------------');
    int error = _bindings.selectAppliqueParts(
        dart_ffi.Pointer.fromAddress(groupH), index, selection, _embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    // print('------------------  selectAppliqueParts  End ------------------');
    Image image = Image.memory(
      Uint8List.fromList(
        _embImageInfo == dart_ffi.nullptr ||
                errorCode != EmbLibraryError.EMB_NO_ERR
            ? []
            : _embImageInfo.ref.embImage.ref.imageData
                .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
      ),
    );

    EmbLibraryLog.byeBye("selectAppliqueParts");
    _delSelectedGroupImage();
    return (errorCode: errorCode, image: image);
  }

  @override
  ({EmbLibraryError errorCode, List<MemHandle> handles}) selectEditFile(
      String file, int dataSource, int restriction, int partsNo) {
    EmbLibraryLog.d("selectEditFile  start");

    final filePathNative = file.toNativeUtf8();
    final embH = ffi.calloc<embedit_bindings_generated.MemHandle_t>();
    final handleNum = ffi.calloc<dart_ffi.Int32>();

    final errorIndex = _bindings.selectEditFile(
      filePathNative.cast<dart_ffi.Char>(),
      dataSource,
      restriction,
      partsNo,
      embH,
      handleNum,
    );

    final List<MemHandle> handles = [];
    var handleValueList =
        embH.value as dart_ffi.Pointer<embedit_bindings_generated.MemHandle_t>;

    for (int index = 0; index < handleNum.value; index++) {
      handles.add(handleValueList[index].address);
    }

    _bindings.selectEditFileEmbHFree(embH);
    ffi.calloc.free(embH);
    ffi.calloc.free(filePathNative);
    ffi.calloc.free(handleNum);
    EmbLibraryLog.d("selectEditFile  end");


    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(errorIndex),
      handles: handles,
    );
  }

  @override
  EmbLibraryError selectEmbToEdit(MemHandle groupH) {
    EmbLibraryLog.d("selectEmbToEdit  start");
    int error = _bindings.selectEmbToEdit(dart_ffi.Pointer.fromAddress(groupH));
    EmbLibraryLog.d("selectEmbToEdit  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError selectMultiEmb(
      MemHandle srcGroup, GroupSelectCondition selectCondition) {
    EmbLibraryLog.d("selectMultiEmb  start");
    int error = _bindings.selectMultiEmb(
        dart_ffi.Pointer.fromAddress(srcGroup), selectCondition.index);
    EmbLibraryLog.d("selectMultiEmb  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError selectPartsInGroupColorChange(MemHandle gHandle, int index) {
    EmbLibraryLog.d("selectPartsInGroupColorChange  start");
    int error = _bindings.selectPartsInGroupColorChange(
        dart_ffi.Pointer.fromAddress(gHandle), index);
    EmbLibraryLog.d("selectPartsInGroupColorChange  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError setBorderSpace(DivBorder direction, int space) {
    EmbLibraryLog.d("setBorderSpace");
    int error =
        _bindings.setBorderSpace(DivBorder.toFfiApiIntValue(direction), space);

    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("setBorderSpace  end,errorCode:$errorCode");
    return errorCode;
  }

  @override
  EmbLibraryError setColorSort(bool colorSort) {
    EmbLibraryLog.d("setColorSort:$colorSort");
    int error = _bindings.setColorSort(colorSort);

    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("setColorSort  end,errorCode:$errorCode");
    return errorCode;
  }

  @override
  EmbLibraryError setCurrentStitchNumber(int needleCount) {
    EmbLibraryLog.d("setCurrentStitchNumber  start");
    // print(
    //     '---------------------setCurrentStitchNumber  $needleCount-------------------------------');
    int error = _bindings.setCurrentStitchNumber(needleCount);
    // print('---------------------setCurrentStitchNumber  End   $error-------------------------------');
    EmbLibraryLog.d("setCurrentStitchNumber  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) setDecoFillStipple(
    bool decorativeType,
    int decorativeNo,
    FrameSize frSize,
    int distance,
    int fillSize,
  ) {
    EmbLibraryLog.d("setDecoFillStipple  start");
    final grpH = ffi.calloc<embedit_bindings_generated.MemHandle_t>();

    int error = _bindings.setDecoFillStipple(
      decorativeType,
      decorativeNo,
      frSize,
      distance,
      fillSize,
      grpH,
    );

    MemHandle handle = grpH.value.address;

    ffi.calloc.free(grpH);
    EmbLibraryLog.d("setDecoFillStipple  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      handle: handle
    );
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) setDistanceApplique(
      int distance) {
    EmbLibraryLog.d("setDistanceApplique  start");
    final grpH = ffi.calloc<embedit_bindings_generated.MemHandle_t>();
    int error = _bindings.setDistanceApplique(distance, grpH);

    MemHandle handle = grpH.value.address;
    ffi.calloc.free(grpH);
    EmbLibraryLog.d("setDistanceApplique  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      handle: handle
    );
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) setEchoStipple(
      FrameSize frSize, int distance, int space) {
    EmbLibraryLog.d("setEchoStipple  start");

    final grpH = ffi.calloc<embedit_bindings_generated.MemHandle_t>();
    int error = _bindings.setEchoStipple(frSize, distance, space, grpH);
    MemHandle handle = grpH.value.address;
    ffi.calloc.free(grpH);
    EmbLibraryLog.d("setEchoStipple  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      handle: handle
    );
  }

  @override
  void setEmbSewingMaskTrace() {
    EmbLibraryLog.d("setEmbSewingMaskTrace  start");

    /// 1秒の遅延
    var duration = const Duration(seconds: 7);
    sleep(duration);

    EmbLibraryLog.d("setEmbSewingMaskTrace  end");
  }

  @override
  void setQuiltNeedlePosition(int quiltNeedlePositionValue) {
    EmbLibraryLog.d("setQuiltNeedlePosition");
  }

  @override
  void setOutColorDepth(int colorDepth) {
    EmbLibraryLog.d("setOutColorDepth  start");
    _bindings.setOutColorDepth(colorDepth);
    EmbLibraryLog.d("setOutColorDepth  end");
  }

  @override
  ({EmbLibraryError errorCode, QuiltSashesLimit limit}) setQuiltBand(
      int bValue) {
    EmbLibraryLog.d("setQuiltBand  start");
    final sizeLimit = embedit_bindings_generated.quiltSashesLimit_t.allocate();

    int error = _bindings.setQuiltBand(bValue, sizeLimit);

    QuiltSashesLimit limit = QuiltSashesLimit(
      widthMax: sizeLimit.ref.widthMax,
      widthMin: sizeLimit.ref.widthMin,
      heightMax: sizeLimit.ref.heightMax,
      heightMin: sizeLimit.ref.heightMin,
      bandMax: sizeLimit.ref.bandMax,
      bandMin: sizeLimit.ref.bandMin,
    );

    ffi.calloc.free(sizeLimit);
    EmbLibraryLog.d("setQuiltBand  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      limit: limit
    );
  }

  @override
  ({EmbLibraryError errorCode, QuiltSashesLimit limit}) setQuiltHeight(
      int heightValue) {
    EmbLibraryLog.d("setQuiltHeight  start");
    final sizeLimit = embedit_bindings_generated.quiltSashesLimit_t.allocate();

    int error = _bindings.setQuiltHeight(heightValue, sizeLimit);

    QuiltSashesLimit limit = QuiltSashesLimit(
      widthMax: sizeLimit.ref.widthMax,
      widthMin: sizeLimit.ref.widthMin,
      heightMax: sizeLimit.ref.heightMax,
      heightMin: sizeLimit.ref.heightMin,
      bandMax: sizeLimit.ref.bandMax,
      bandMin: sizeLimit.ref.bandMin,
    );

    ffi.calloc.free(sizeLimit);
    EmbLibraryLog.d("setQuiltHeight  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      limit: limit
    );
  }

  @override
  ({EmbLibraryError errorCode, QuiltSashesLimit limit}) setQuiltWidth(
      int widthValue) {
    EmbLibraryLog.d("setQuiltWidth  start");
    final sizeLimit = embedit_bindings_generated.quiltSashesLimit_t.allocate();

    int error = _bindings.setQuiltWidth(widthValue, sizeLimit);

    QuiltSashesLimit limit = QuiltSashesLimit(
      widthMax: sizeLimit.ref.widthMax,
      widthMin: sizeLimit.ref.widthMin,
      heightMax: sizeLimit.ref.heightMax,
      heightMin: sizeLimit.ref.heightMin,
      bandMax: sizeLimit.ref.bandMax,
      bandMin: sizeLimit.ref.bandMin,
    );

    ffi.calloc.free(sizeLimit);
    EmbLibraryLog.d("setQuiltWidth  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      limit: limit
    );
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) setStipple(
      FrameSize frSize, int distance, int space) {
    EmbLibraryLog.d("setStipple  start,frSize:$frSize");

    final grpH = ffi.calloc<embedit_bindings_generated.MemHandle_t>();
    int error = _bindings.setStipple(frSize, distance, space, grpH);
    MemHandle handle = grpH.value.address;
    ffi.calloc.free(grpH);
    EmbLibraryLog.d("setStipple  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      handle: handle
    );
  }

  @override
  ({EmbLibraryError errorCode, int num}) shuffleGradation(
      int shuffleColorNum, ShuffleBaseColor shuffle) {
    EmbLibraryLog.d("shuffleGradation  start");
    final thumbnail = ffi.calloc<dart_ffi.Int32>();
    final libShuffle = embedit_bindings_generated.shuffleBaseColor_t.allocate();

    libShuffle.ref.mode = shuffle.mode;
    for (int index = 0; index < shuffle.color.length; index++) {
      libShuffle.ref.color[index].brandCode = shuffle.color[index].brandCode;
      libShuffle.ref.color[index].tableIdx = shuffle.color[index].tableIndex;
      libShuffle.ref.color[index].valid = shuffle.color[index].valid;
    }

    int error =
        _bindings.shuffleGradation(shuffleColorNum, libShuffle, thumbnail);
    int num = thumbnail.value;

    ffi.calloc.free(thumbnail);
    ffi.calloc.free(libShuffle);
    EmbLibraryLog.d("shuffleGradation  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      num: num
    );
  }

  @override
  ({EmbLibraryError errorCode, int num}) shuffleRandom(
      int shuffleColorNum, ShuffleBaseColor shuffle) {
    EmbLibraryLog.d("shuffleRandom  start");
    final thumbnail = ffi.calloc<dart_ffi.Int32>();
    final libShuffle = embedit_bindings_generated.shuffleBaseColor_t.allocate();

    libShuffle.ref.mode = shuffle.mode;
    for (int index = 0; index < shuffle.color.length; index++) {
      libShuffle.ref.color[index].brandCode = shuffle.color[index].brandCode;
      libShuffle.ref.color[index].tableIdx = shuffle.color[index].tableIndex;
      libShuffle.ref.color[index].valid = shuffle.color[index].valid;
    }

    int error = _bindings.shuffleRandom(shuffleColorNum, libShuffle, thumbnail);

    int num = thumbnail.value;

    ffi.calloc.free(thumbnail);
    ffi.calloc.free(libShuffle);
    EmbLibraryLog.d("shuffleRandom  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      num: num
    );
  }

  @override
  ({EmbLibraryError errorCode, int num}) shuffleSoft(int shuffleColorNum) {
    EmbLibraryLog.d("shuffleSoft  start");
    final thumbnail = ffi.calloc<dart_ffi.Int32>();

    int error = _bindings.shuffleSoft(shuffleColorNum, thumbnail);
    int num = thumbnail.value;

    ffi.calloc.free(thumbnail);
    EmbLibraryLog.d("shuffleSoft  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      num: num
    );
  }

  @override
  ({EmbLibraryError errorCode, int num}) shuffleVivid(int shuffleColorNum) {
    EmbLibraryLog.d("shuffleVivid  start");
    final thumbnail = ffi.calloc<dart_ffi.Int32>();

    int error = _bindings.shuffleVivid(shuffleColorNum, thumbnail);
    int num = thumbnail.value;

    ffi.calloc.free(thumbnail);
    EmbLibraryLog.d("shuffleVivid  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      num: num
    );
  }

  @override
  EmbLibraryError startEmbBorder() {
    EmbLibraryLog.d("startEmbBorder  start");
    int error = _bindings.startEmbBorder();
    EmbLibraryLog.d("startEmbBorder  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError unGroupingGroupedPatterns(MemHandle borderHandle) {
    EmbLibraryLog.d("unGroupingGroupedPatterns  start");
    int error = _bindings
        .ungroupingGroupedPatterns(dart_ffi.Pointer.fromAddress(borderHandle));
    EmbLibraryLog.d("unGroupingGroupedPatterns  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  Uint8List covertPlfToBmp(dynamic bmpWidth, dynamic bmpHeight, Uint8List plf) {
    EmbLibraryLog.d("covertPlfToBmp");

    return plf;
  }

  @override
  ({EmbLibraryError errorCode, int division, Image image})
      getQuiltPreviewHexagonImage(bool frameBorder) {
    EmbLibraryLog.hello("getQuiltPreviewHexagonImage");
    final libDivision = ffi.calloc<dart_ffi.Int32>();
    int error = _bindings.getQuiltPreviewHexagonImage(
        frameBorder, libDivision, _embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    int division = libDivision.value;
    Image image = Image.memory(
      Uint8List.fromList(
        _embImageInfo == dart_ffi.nullptr ||
                errorCode != EmbLibraryError.EMB_NO_ERR
            ? []
            : _embImageInfo.ref.embImage.ref.imageData
                .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
      ),
    );

    ffi.calloc.free(libDivision);
    EmbLibraryLog.byeBye("getQuiltPreviewHexagonImage");
    _delSelectedGroupImage();
    return (errorCode: errorCode, division: division, image: image);
  }

  @override
  ({EmbLibraryError errorCode, Image image}) getFlipPart(
      int embQuiltIndex, int flip, bool frame) {
    EmbLibraryLog.hello("getFlipPart");

    int error =
        _bindings.getFlipPart(embQuiltIndex, flip, frame, _embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    Image image = Image.memory(
      Uint8List.fromList(
        _embImageInfo == dart_ffi.nullptr ||
                errorCode != EmbLibraryError.EMB_NO_ERR
            ? []
            : _embImageInfo.ref.embImage.ref.imageData
                .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
      ),
    );

    EmbLibraryLog.byeBye("getFlipPart");
    _delSelectedGroupImage();
    return (errorCode: errorCode, image: image);
  }

  @override
  void changeQuiltColor(int colorNum, int r, int g, int b) {
    EmbLibraryLog.d("changeQuiltColor  start");
    _bindings.changeQuiltColor(colorNum, r, g, b);
    EmbLibraryLog.d("changeQuiltColor  end");
  }

  @override
  EmbLibraryError changeQuiltColorByPalette(
      int colorNum, int Brand, int index) {
    EmbLibraryLog.d("changeQuiltColorByPalette start");
    final errIndex =
        _bindings.changeQuiltColorByPalette(colorNum, Brand, index);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("changeQuiltColorByPalette end");
    return errCode;
  }

  @override
  EmbLibraryError finishApplique() {
    EmbLibraryLog.d("finishApplique  start");
    int error = _bindings.finishApplique();
    EmbLibraryLog.d("finishApplique  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  ({EmbLibraryError errorCode, Image image}) selectAppliquePartsAll(
      MemHandle memHandle, bool selection) {
    EmbLibraryLog.hello("selectAppliquePartsAll");

    int error = _bindings.selectAppliquePartsAll(
        dart_ffi.Pointer.fromAddress(memHandle), selection, _embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    Image image = Image.memory(
      Uint8List.fromList(
        _embImageInfo == dart_ffi.nullptr ||
                errorCode != EmbLibraryError.EMB_NO_ERR
            ? []
            : _embImageInfo.ref.embImage.ref.imageData
                .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
      ),
    );

    EmbLibraryLog.byeBye("selectAppliquePartsAll");
    _delSelectedGroupImage();
    return (errorCode: errorCode, image: image);
  }

  @override
  EmbLibraryError isAppliqueTextureNotAllDraw() {
    EmbLibraryLog.d("isAppliqueTextureNotAllDraw");

    return EmbLibraryError.EMB_NO_ERR;
  }

  @override
  ({EmbLibraryError errorCode, int time}) getPhotoStitchEmbTotalTime() {
    EmbLibraryLog.d("getPhotoStitchEmbTotalTime  start");

    int time = Random().nextInt(888);

    EmbLibraryLog.d("getPhotoStitchEmbTotalTime  end");
    return (errorCode: EmbLibraryError.EMB_NO_ERR, time: time);
  }

  @override
  EmbLibraryError connectSewingScan(ConnectSewingScanType scanType,
      void Function(Image, ConnectSewingScanStatus) callBack) {
    EmbLibraryLog.d("connectSewingScan");

    return EmbLibraryError.EMB_NO_ERR;
  }

  @override
  EmbLibraryError moveEmbAll(int dirX, int dirY, bool flgLongPress) {
    EmbLibraryLog.hello(
        "moveEmbAll | dirX:$dirX | dirY:$dirY | flgLongPress:$flgLongPress");
    int errorIndex = _bindings.moveEmbAll(dirX, dirY, flgLongPress);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errorIndex);
    EmbLibraryLog.byeBye("moveEmbAll");
    return errCode;
  }

  @override
  EmbLibraryError rotateEmbAll(int angle) {
    EmbLibraryLog.d("rotateEmbAll  start angle:$angle");
    int error = _bindings.rotateEmbAll(angle);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("rotateEmbAll  end $errCode");
    return errCode;
  }

  @override
  EmbLibraryError scalingQuiltEmb(int scale) {
    EmbLibraryLog.d("scalingQuiltEmb start  scale:$scale");
    int error = _bindings.scalingQuiltEmb(scale);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("scalingQuiltEmb end $errCode");
    return errCode;
  }

  @override
  EmbLibraryError startPointSetQuiltEmb(int point) {
    EmbLibraryLog.d("startPointSetQuiltEmb start  point: $point");
    int error = _bindings.startPointSetQuiltEmb(point);
    EmbLibraryLog.d("startPointSetQuiltEmb end    error: $error");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError selectPartsQuitEmb(int select) {
    EmbLibraryLog.d("selectPartsQuitEmb start");
    int error = _bindings.selectPartsQuitEmb(select);
    EmbLibraryLog.d("selectPartsQuitEmb end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError moveEmbAllCenter() {
    EmbLibraryLog.d("moveEmbAllCenter  start");
    int error = _bindings.moveEmbAllCenter();
    EmbLibraryLog.d("moveEmbAllCenter  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) getCurrentGroupHandle() {
    EmbLibraryLog.d("getCurrentGroupHandle  start");
    final currentG = ffi.calloc<embedit_bindings_generated.MemHandle_t>();

    int error = _bindings.getCurrentGroupHandle(currentG);
    MemHandle currentHandle = currentG.value.address;

    ffi.calloc.free(currentG);
    EmbLibraryLog.d("getCurrentGroupHandle  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      handle: currentHandle
    );
  }

  @override
  EmbLibraryError scanFabric(void Function(Image, FabricScanStatus) callBack) {
    EmbLibraryLog.d("scanFabric");

    return EmbLibraryError.EMB_NO_ERR;
  }

  @override
  Uint8List? getMonitoringEmbImage() {
    EmbLibraryLog.d("getMonitoringEmbImage");

    return null;
  }

  @override
  Uint8List? getMonitoringEmbProgImage() {
    EmbLibraryLog.d("getMonitoringEmbProgImage");

    return null;
  }

  @override
  EmbLibraryError errorDisplayNeedleUpWhenFrameMove() {
    EmbLibraryLog.d("errorDisplayNeedleUpWhenFrameMove");

    return EmbLibraryError.EMB_NO_ERR;
  }

  @override
  EmbLibraryError errorDisplayPresserFootDownWhenFrameMove() {
    EmbLibraryLog.d("errorDisplayPresserFootDownWhenFrameMove");

    return EmbLibraryError.EMB_NO_ERR;
  }

  @override
  bool isEmbroideryUnitLeverDown() {
    EmbLibraryLog.d("isEmbroideryUnitLeverDown");

    return true;
  }

  @override
  EmbLibraryError endEmbBorder() {
    EmbLibraryLog.d("endEmbBorder  start");
    int error = _bindings.endEmbBorder();
    EmbLibraryLog.d("endEmbBorder  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) getBorderHandleIncludeGroup(
      MemHandle group) {
    EmbLibraryLog.d("getBorderHandleIncludeGroup  start");
    final borderH = embedit_bindings_generated.EmbBorder_t.allocate_p();
    borderH.value = embedit_bindings_generated.EmbBorder_t.allocate();

    int error = _bindings.getBorderHandleIncludeGroup(
        dart_ffi.Pointer.fromAddress(group), borderH);

    MemHandle borderHandle = borderH.value.address;

    ffi.calloc.free(borderH);

    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    if (errorCode != EmbLibraryError.EMB_NO_ERR) {
      EmbLibraryLog.d("getBorderHandleIncludeGroup  $errorCode");
    } else {
      /// do nothing
    }
    EmbLibraryLog.d("getBorderHandleIncludeGroup  end");
    return (errorCode: errorCode, handle: borderHandle);
  }

  @override
  ({EmbLibraryError errorCode, bool result}) checkEmbPatternType(
      MemHandle groupH, EmbPatternType embType) {
    EmbLibraryLog.d("checkEmbPatternType  start");
    final libResult = ffi.calloc<dart_ffi.Bool>();

    int error = _bindings.checkEmbPatternType(
        dart_ffi.Pointer.fromAddress(groupH), embType.number, libResult);
    bool result = libResult.value;

    ffi.calloc.free(libResult);
    EmbLibraryLog.d("checkEmbPatternType  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      result: result
    );
  }

  @override
  EmbLibraryError prepareSewing() {
    EmbLibraryLog.d("prepareSewing start");
    int error = _bindings.prepareSewing();
    EmbLibraryLog.d("prepareSewing end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  ({
    EmbLibraryError errorCode,
    EmbInfo embInfo,
    EmbGrp embGrp,
    EmbPtrnInfo embPatternInfo
  }) getEmbGrpInfo(MemHandle groupH) {
    EmbLibraryLog.hello("getEmbGrpInfo");
    final embInfoPointer = ffi.calloc<embedit_bindings_generated.embInfo_t>();
    final grpInfoPointer = ffi.calloc<embedit_bindings_generated.embGrp_t>();
    final ptrnInfoPointer =
        ffi.calloc<embedit_bindings_generated.embPtrnInfo_t>();

    final error = _bindings.getEmbGrpInfo(
      dart_ffi.Pointer.fromAddress(groupH),
      embInfoPointer,
      grpInfoPointer,
      ptrnInfoPointer,
    );

    EmbGrp embGrp = EmbGrp(
      position: SSPoint(
        X: grpInfoPointer.ref.position.X,
        Y: grpInfoPointer.ref.position.Y,
      ),
      angle: grpInfoPointer.ref.angle,
      selected: grpInfoPointer.ref.selected == 1,
      patternLayOutMethod: PatternLayOut.getValuesByNumber(
          grpInfoPointer.ref.patternLayOutMethod),
      grpRotateOrigine: SSPoint(
        X: grpInfoPointer.ref.grpRotateOrigine.X,
        Y: grpInfoPointer.ref.grpRotateOrigine.Y,
      ),
    );

    List<EmbPtrn> embPtrnList = [];
    for (int index = 0; index < ptrnInfoPointer.ref.patternNum; index++) {
      embedit_bindings_generated.embPtrn_t embPtrn =
          (ptrnInfoPointer.ref.embPattern + index).ref;

      List<SSPoint> quiltRectanglePoint = [];
      for (int i = 0; i < embPtrn.quiltRectanglePointNum; i++) {
        embedit_bindings_generated.SSPoint_t value =
            embPtrn.quiltRectanglePoint[i];
        quiltRectanglePoint.add(SSPoint(X: value.X, Y: value.Y));
      }

      embPtrnList.add(
        EmbPtrn(
          inGrpOffset: SSPoint(
            X: embPtrn.inGrpOffset.X,
            Y: embPtrn.inGrpOffset.Y,
          ),
          size: RectanArea(
            left: embPtrn.size.Left,
            right: embPtrn.size.Right,
            top: embPtrn.size.Top,
            bottom: embPtrn.size.Bottom,
          ),
          rotatedMask: Rectangle(
            positionTopLeft: SSPoint(
              X: embPtrn.rotatedMask.Ptl.X,
              Y: embPtrn.rotatedMask.Ptl.Y,
            ),
            positionTopRight: SSPoint(
              X: embPtrn.rotatedMask.Ptr.X,
              Y: embPtrn.rotatedMask.Ptr.Y,
            ),
            positionBottomLeft: SSPoint(
              X: embPtrn.rotatedMask.Pbl.X,
              Y: embPtrn.rotatedMask.Pbl.Y,
            ),
            positionBottomRight: SSPoint(
              X: embPtrn.rotatedMask.Pbr.X,
              Y: embPtrn.rotatedMask.Pbr.Y,
            ),
          ),
          packedMask: RectanArea(
            left: embPtrn.packedMask.Left,
            right: embPtrn.packedMask.Right,
            top: embPtrn.packedMask.Top,
            bottom: embPtrn.packedMask.Bottom,
          ),
          mirror: embPtrn.mirror,
          doSTB: embPtrn.doSTB,
          magStep: Magnitude(
            X: embPtrn.magStep.X,
            Y: embPtrn.magStep.Y,
          ),
          fontNumber: embPtrn.fontNumber,
          selected: embPtrn.selected,
          unicode: embPtrn.unicode,
          dataSource: embPtrn.dataSource,
          embExtension: embPtrn.embExtension,
          quiltRectanglePoint: quiltRectanglePoint,
          quiltRectanglePointNum: embPtrn.quiltRectanglePointNum,
          magMax: embPtrn.MagMax,
          magDefault: embPtrn.MagDefault,
          magMin: embPtrn.MagMin,
        ),
      );

      print(embPtrnList.last.toString());
    }

    EmbPtrnInfo embPtrnInfo = EmbPtrnInfo(
      embPatterns: embPtrnList,
      patternNum: ptrnInfoPointer.ref.patternNum,
    );
    EmbInfo embInfo = EmbInfo(
      allAngle: embInfoPointer.ref.allAngle,
      frameOffset: SSPoint(
          X: embInfoPointer.ref.frameOffset.X,
          Y: embInfoPointer.ref.frameOffset.Y),
      embRotateOrigine: SSPoint(
          X: embInfoPointer.ref.embRotateOrigine.X,
          Y: embInfoPointer.ref.embRotateOrigine.Y),
      height: embInfoPointer.ref.height,
      wide: embInfoPointer.ref.wide,
    );

    // メモリリーク修正対応 //
    _bindings.deleteEmbGrpPatternInfo(ptrnInfoPointer);

    ffi.calloc.free(embInfoPointer);
    ffi.calloc.free(grpInfoPointer);
    ffi.calloc.free(ptrnInfoPointer);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.byeBye("getEmbGrpInfo");
    return (
      errorCode: errorCode,
      embInfo: embInfo,
      embGrp: embGrp,
      embPatternInfo: embPtrnInfo,
    );
  }

  @override
  ({EmbLibraryError errorCode, EmbPtrnInfo embPtrnInfo}) getEmbGrpPatternInfo(
      MemHandle groupH) {
    EmbLibraryLog.d("getEmbGrpPatternInfo  start");
    EmbPtrnInfo embPtrnInfo = EmbPtrnInfo(embPatterns: [], patternNum: 0);
    EmbLibraryLog.d("getEmbGrpPatternInfo  end");
    return (errorCode: EmbLibraryError.EMB_NO_ERR, embPtrnInfo: embPtrnInfo);
  }

  @override
  ({
    EmbLibraryError errorCode,
    RectanArea area,
    ThreadMarkState threadMarkState
  }) getBorderCompInfo(MemHandle memHandle, int index) {
    EmbLibraryLog.d("getBorderCompInfo  start");

    final elementPos = ffi.calloc<embedit_bindings_generated.RectanArea_t>();
    final mark = ffi.calloc<embedit_bindings_generated.threadMarkState_t>();

    int error = _bindings.getBorderCompInfo(
      dart_ffi.Pointer.fromAddress(memHandle),
      index,
      elementPos,
      mark,
    );

    RectanArea area = RectanArea(
      left: elementPos.ref.Left,
      right: elementPos.ref.Right,
      top: elementPos.ref.Top,
      bottom: elementPos.ref.Bottom,
    );
    ThreadMarkState threadMarkState = ThreadMarkState(
      top: mark.ref.top == 1 ? true : false,
      topLeft: mark.ref.topLeft == 1 ? true : false,
      topRight: mark.ref.topRight == 1 ? true : false,
      centerLeft: mark.ref.left == 1 ? true : false,
      centerRight: mark.ref.right == 1 ? true : false,
      bottomLeft: mark.ref.bottomLeft == 1 ? true : false,
      bottomRight: mark.ref.bottomRight == 1 ? true : false,
      bottom: mark.ref.bottom == 1 ? true : false,
    );

    ffi.calloc.free(elementPos);
    ffi.calloc.free(mark);

    EmbLibraryLog.d("getBorderCompInfo  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      area: area,
      threadMarkState: threadMarkState
    );
  }

  @override
  EmbLibraryError signThreadMark(
      int index, bool setReset, ThreadPosition position) {
    EmbLibraryLog.d("signThreadMark start");
    int error = _bindings.asignThreadMark(
      index,
      setReset
          ? embedit_bindings_generated.threadMark_t.SET_MARK
          : embedit_bindings_generated.threadMark_t.RESET_MARK,
      position.number,
    );

    EmbLibraryLog.d("signThreadMark end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  ({EmbLibraryError errorCode, List<MemHandle> handleList})
      getBorderHandleAll() {
    EmbLibraryLog.d("getBorderHandleAll  start");
    List<MemHandle> handleList = [];
    final borderHArray = ffi.calloc<embedit_bindings_generated.MemHandle_t>();
    final handleNum = ffi.calloc<dart_ffi.Int32>();

    int error = _bindings.getBorderHandleAll(borderHArray, handleNum);

    var handleValueList = borderHArray.value
        as dart_ffi.Pointer<embedit_bindings_generated.MemHandle_t>;

    for (int index = 0; index < handleNum.value; index++) {
      handleList.add(handleValueList[index].address);
    }

    ffi.calloc.free(borderHArray);
    ffi.calloc.free(handleNum);
    EmbLibraryLog.d("getBorderHandleAll  end");
    return (
      errorCode: EmbLibraryErrorExtension.getValuesByIndex(error),
      handleList: handleList
    );
  }

  @override
  EmbLibraryError embGotoEdit() {
    EmbLibraryLog.d("embGotoEdit  start");
    int error = _bindings.embGotoEdit();
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("embGotoEdit  end, errorCode:$errorCode");
    if (isMachineApp()) {
      return errorCode;
    }
    return EmbLibraryError.EMB_NO_ERR;
  }

  @override
  EmbLibraryError embGotoSewing() {
    EmbLibraryLog.d("embGotoSewing  start");
    int error = _bindings.embGotoSewing();
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("embGotoSewing  end, errorCode:$errorCode");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError embEditReturnSelect() {
    EmbLibraryLog.d("embEditReturnSelect  start");
    int error = _bindings.embEditReturnSelect();
    EmbLibraryLog.d("embEditReturnSelect  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError embSewingReturnEdit() {
    EmbLibraryLog.d("embSewingReturnEdit  start");
    int error = _bindings.embSewingReturnEdit();
    EmbLibraryLog.d("embSewingReturnEdit  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError embGotoMDCPaint() {
    EmbLibraryLog.d("embGotoMDCPaint  start");
    int error = _bindings.embGotoMDCPaint();
    EmbLibraryLog.d("embGotoMDCPaint  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError embSewingSelectBackgroundScan() {
    EmbLibraryLog.d("embSewingSelectBackgroundScan  start");
    int error = _bindings.embSewingSelectBackgroundScan();
    EmbLibraryLog.d("embSewingSelectBackgroundScan  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError embSewingBackgroundScanStart() {
    EmbLibraryLog.d("embSewingBackgroundScanStart  start");
    int error = _bindings.embSewingBackgroundScanStart();
    EmbLibraryLog.d("embSewingBackgroundScanStart  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError embSewingBackgroundScanCancel() {
    EmbLibraryLog.d("embSewingBackgroundScanCancel  start");
    int error = _bindings.embSewingBackgroundScanCancel();
    EmbLibraryLog.d("embSewingBackgroundScanCancel  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError embSewingBackgroundScanEnd() {
    EmbLibraryLog.d("embSewingBackgroundScanEnd  start");
    int error = _bindings.embSewingBackgroundScanEnd();
    EmbLibraryLog.d("embSewingBackgroundScanEnd  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError mdcSelectBackgroundScan() {
    EmbLibraryLog.d("mdcSelectBackgroundScan  start");
    int error = _bindings.mdcSelectBackgroundScan();
    EmbLibraryLog.d("mdcSelectBackgroundScan  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  ///********************************************************** */
  ///                                                           */
  /// スノーマン関連API一覧                                       */
  ///                                                           */
  ///********************************************************** */
  @override
  EmbLibraryError embSewingSelectSnowman() {
    EmbLibraryLog.d("embSewingSelectSnowman  start");

    int error = _bindings.embSewingSelectSnowman();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("embSewingSelectSnowman  end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError embSewingSnowmanDirSelect(EmbDirSelect dir) {
    EmbLibraryLog.d("embSewingSnowmanDirSelect  start");
    int error = _bindings.embSewingSnowmanDirSelect(dir.index);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("embSewingSnowmanDirSelect  end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError embSewingSnowmanEmbMachineRemove() {
    EmbLibraryLog.d("embSewingSnowmanEmbMachineRemove  start");

    int error = _bindings.embSewingSnowmanEmbMachineRemove();

    final errCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d(
        "embSewingSnowmanEmbMachineRemove  end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError embSewingSnowmanEnd() {
    EmbLibraryLog.d("embSewingSnowmanEnd  start");

    int error = _bindings.embSewingSnowmanEnd();

    final errCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("embSewingSnowmanEnd  end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError embSewingSnowmanFrameMoveCancel() {
    EmbLibraryLog.d("embSewingSnowmanFrameMoveCancel  start");

    int error = _bindings.embSewingSnowmanFrameMoveCancel();

    final errCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d(
        "embSewingSnowmanFrameMoveCancel  end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError embSewingSnowmanFrameMoveOk() {
    EmbLibraryLog.d("embSewingSnowmanFrameMoveOk  start");

    int error = _bindings.embSewingSnowmanFrameMoveOk();

    final errCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("embSewingSnowmanFrameMoveOk  end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError embSewingSnowmanScanCancel() {
    EmbLibraryLog.d("embSewingSnowmanScanCancel  start");

    int error = _bindings.embSewingSnowmanScanCancel();

    final errCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("embSewingSnowmanScanCancel  end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError embSewingSnowmanScanOk() {
    EmbLibraryLog.d("embSewingSnowmanScanOk  start");
    int errIndex = _bindings.embSewingSnowmanScanOk();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingSnowmanScanOk  end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError embSewingSnowmanStartCancel() {
    EmbLibraryLog.d("embSewingSnowmanStartCancel  start");
    int errIndex = _bindings.embSewingSnowmanStartCancel();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingSnowmanStartCancel  end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError embSewingSnowmanStartOk() {
    EmbLibraryLog.d("embSewingSnowmanStartOk  start");
    int errIndex = _bindings.embSewingSnowmanStartOk();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingSnowmanStartOk  end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError removeEmbUnit() {
    EmbLibraryLog.d("removeEmbUnit  start");
    int errIndex = _bindings.removeEmbUnit();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("removeEmbUnit  end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError setEmbTensionMinus(bool isLongPress) {
    EmbLibraryLog.d("setEmbTensionMinus start");
    const int longPressStep = 2;
    const int shortPressStep = 1;
    final errIndex = _bindings
        .setEmbTensionMinus(isLongPress ? longPressStep : shortPressStep);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("setEmbTensionMinus end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError setEmbTensionPlus(bool isLongPress) {
    EmbLibraryLog.d("setEmbTensionPlus start");
    const int longPressStep = 2;
    const int shortPressStep = 1;
    final errIndex = _bindings
        .setEmbTensionPlus(isLongPress ? longPressStep : shortPressStep);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("setEmbTensionPlus end, errorCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError setEndColorTrimOff() {
    EmbLibraryLog.d("setEndColorTrimOff start");
    final errIndex = _bindings.setEndColorTrimOff();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("setEndColorTrimOff end");
    return errCode;
  }

  @override
  EmbLibraryError setEndColorTrimOn() {
    EmbLibraryLog.d("setEndColorTrimOn start");
    final errIndex = _bindings.setEndColorTrimOn();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("setEndColorTrimOn end");
    return errCode;
  }

  @override
  EmbLibraryError setJumpStitchLengthMinus() {
    EmbLibraryLog.d("setJumpStitchLengthMinus start");
    final errIndex = _bindings.setJumpStitchLengthMinus();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("setJumpStitchLengthMinus end");
    return errCode;
  }

  @override
  EmbLibraryError setJumpStitchLengthPlus() {
    EmbLibraryLog.d("setJumpStitchLengthPlus start");
    final errIndex = _bindings.setJumpStitchLengthPlus();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("setJumpStitchLengthPlus end");
    return errCode;
  }

  @override
  EmbLibraryError setJumpStitchTrimOff() {
    EmbLibraryLog.d("setJumpStitchTrimOff start");
    final errIndex = _bindings.setJumpStitchTrimOff();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("setJumpStitchTrimOff end");
    return errCode;
  }

  @override
  EmbLibraryError setJumpStitchTrimOn() {
    EmbLibraryLog.d("setJumpStitchTrimOn start");
    final errIndex = _bindings.setJumpStitchTrimOn();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("setJumpStitchTrimOn end");
    return errCode;
  }

  @override
  EmbLibraryError setLedPointerON() {
    EmbLibraryLog.d("setLedPointerON start");
    final errIndex = _bindings.setLedPointerON();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("setLedPointerON end");
    return errCode;
  }

  @override
  ({EmbLibraryError error, CameraScanInfoAndData infoAndData})
      getCameraScanInfoAndData(CameraScanDataKind kind) {
    EmbLibraryLog.d("getCameraScanInfoAndData start");
    final tcTable =
        embedit_bindings_generated.CameraScanInfoAndData_t.allocate();
    int error = _bindings.getCameraScanInfoAndData(kind.number, tcTable);

    Uint8List data =
        Uint8List.fromList(tcTable.ref.data.asTypedList(tcTable.ref.dataSize));
    Uint8List info =
        Uint8List.fromList(tcTable.ref.info.asTypedList(tcTable.ref.infoSize));

    CameraScanInfoAndData scanInfoAndData = CameraScanInfoAndData(
      data: data,
      dataSize: tcTable.ref.dataSize,
      info: info,
      infoSize: tcTable.ref.infoSize,
    );

    ffi.calloc.free(tcTable);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("getCameraScanInfoAndData  end, $errCode");
    return (
      error: errCode,
      infoAndData: scanInfoAndData,
    );
  }

  @override
  void setMdcSelectPictureForScanInfoAndData(String filePath) {}

  @override
  ({EmbLibraryError error, Uint8List cameraScanImage}) getCameraScanImage(
    CameraScanImageScale scale,
    String cameraScanInfoPath,
    String cameraScanDataPath,
  ) {
    EmbLibraryLog.d("getCameraScanImage start");
    final tcTable = embedit_bindings_generated.embImg_t.allocate();
    final infoPath = cameraScanInfoPath.toNativeUtf8();
    final dataPath = cameraScanDataPath.toNativeUtf8();

    int error = _bindings.getCameraScanImage(
      infoPath.cast<dart_ffi.Char>(),
      dataPath.cast<dart_ffi.Char>(),
      scale.number,
      tcTable,
    );

    Uint8List image = Uint8List.fromList(
      tcTable.ref.imageData.asTypedList(tcTable.ref.imageSize),
    );
    ffi.calloc.free(infoPath);
    ffi.calloc.free(dataPath);
    ffi.calloc.free(tcTable);
    EmbLibraryLog.d("getCameraScanImage  end");
    return (
      error: EmbLibraryErrorExtension.getValuesByIndex(error),
      cameraScanImage: image,
    );
  }

  @override
  ({EmbLibraryError error, Uint8List cameraScanImage})
      getCameraScanTestModeImage(int kind) {
    EmbLibraryLog.d("getCameraScanTestModeImage start");

    final tcTable = embedit_bindings_generated.embImg_t.allocate();
    int error = _bindings.getCameraScanTestModeImage(kind, tcTable);

    Uint8List image = Uint8List.fromList(
      tcTable.ref.imageData.asTypedList(tcTable.ref.imageSize),
    );

    ffi.calloc.free(tcTable);
    EmbLibraryLog.d("getCameraScanTestModeImage  end");
    return (
      error: EmbLibraryErrorExtension.getValuesByIndex(error),
      cameraScanImage: image,
    );
  }

  @override
  ({EmbLibraryError errorCode, MemHandle handle}) cancelBorderBeforeWappen() {
    EmbLibraryLog.d("cancelBorderBeforeWappen start");
    final borderH = embedit_bindings_generated.EmbBorder_t.allocate_p();
    borderH.value = embedit_bindings_generated.EmbBorder_t.allocate();
    int errIndex = _bindings.cancelBorderBeforeWappen(borderH);
    MemHandle handle = borderH.value.address;
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    ffi.calloc.free(borderH);
    EmbLibraryLog.d("cancelBorderBeforeWappen end");
    return (errorCode: errCode, handle: handle);
  }

  @override
  EmbLibraryError checkTextureDrawing() {
    EmbLibraryLog.d("checkTextureDrawing start");
    final errIndex = _bindings.checkTextureDrawing();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("checkTextureDrawing end");
    return errCode;
  }

  @override
  EmbLibraryError completeSelectedApplique() {
    EmbLibraryLog.d("completeSelectedApplique start");
    final errIndex = _bindings.completeSelectedApplique();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("completeSelectedApplique end");
    return errCode;
  }

  @override
  EmbLibraryError deleteMarkBeforeWappen() {
    EmbLibraryLog.d("deleteMarkBeforeWappen start");
    final errIndex = _bindings.deleteMarkBeforeWappen();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("deleteMarkBeforeWappen end");
    return errCode;
  }

  @override
  ({EmbLibraryError errCode, Uint8List image}) getWappenPreviewTexture() {
    EmbLibraryLog.hello("getWappenPreviewTexture");
    final error = _bindings.getWappenPreviewTexture(_embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    Uint8List image = Uint8List.fromList(
      _embImageInfo == dart_ffi.nullptr ||
              errorCode != EmbLibraryError.EMB_NO_ERR
          ? []
          : _embImageInfo.ref.embImage.ref.imageData
              .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
    );

    EmbLibraryLog.byeBye("getWappenPreviewTexture");
    _delSelectedGroupImage();
    return (errCode: errorCode, image: image);
  }

  @override
  EmbLibraryError initAppliqueSelect() {
    EmbLibraryLog.d("initAppliqueSelect start");
    final errIndex = _bindings.initAppliqueSelect();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("initAppliqueSelect end");
    return errCode;
  }

  @override
  ({EmbLibraryError errCode, Uint8List image}) setTexture(bool texture) {
    EmbLibraryLog.hello("setTexture");
    final error = _bindings.setTexture(texture, _embImageInfo);
    final errorCode = EmbLibraryErrorExtension.getValuesByIndex(error);

    Uint8List image = Uint8List.fromList(
      _embImageInfo == dart_ffi.nullptr ||
              errorCode != EmbLibraryError.EMB_NO_ERR
          ? []
          : _embImageInfo.ref.embImage.ref.imageData
              .asTypedList(_embImageInfo.ref.embImage.ref.imageSize),
    );
    EmbLibraryLog.byeBye("setTexture");
    _delSelectedGroupImage();
    return (errCode: errorCode, image: image);
  }

  @override
  EmbLibraryError embGotoResumeOk() {
    EmbLibraryLog.d("embGotoResumeOk start");
    final errIndex = _bindings.embGotoResumeOk();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embGotoResumeOk end");
    return errCode;
  }

  @override
  EmbLibraryError embSewingSelectFb() {
    EmbLibraryLog.d("embSewingSelectFb  start");
    int error = _bindings.embSewingSelectFb();
    EmbLibraryLog.d("embSewingSelectFb  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError embSewingFbStitchMove(EmbSewingFbStitchMove moveType) {
    EmbLibraryLog.d(
        "embSewingFbStitchMove  start,moveType:$moveType,index:${moveType.index}");
    int error = _bindings.embSewingFbStitchMove(moveType.index);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(error);
    EmbLibraryLog.d("embSewingFbStitchMove  end,errCode: $errCode");
    return errCode;
  }

  @override
  EmbLibraryError embSewingFbCameraView() {
    EmbLibraryLog.d("embSewingFbCameraView  start");
    int error = _bindings.embSewingFbCameraView();
    EmbLibraryLog.d("embSewingFbCameraView  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError embSewingFbEnd() {
    EmbLibraryLog.d("embSewingFbEnd  start");
    int error = _bindings.embSewingFbEnd();
    EmbLibraryLog.d("embSewingFbEnd  end");
    return EmbLibraryErrorExtension.getValuesByIndex(error);
  }

  @override
  EmbLibraryError embSewingStartCameraFb() {
    EmbLibraryLog.d("embSewingStartCameraFb start");
    final errIndex = _bindings.embSewingStartCameraFb();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingStartCameraFb end");
    return errCode;
  }

  @override
  EmbLibraryError embSewingCloseCameraFb() {
    EmbLibraryLog.d("embSewingCloseCameraFb start");
    final errIndex = _bindings.embSewingCloseCameraFb();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingCloseCameraFb end");
    return errCode;
  }

  @override
  EmbLibraryError embSewingChangeNeedleViewCameraFb() {
    EmbLibraryLog.d("embSewingChangeNeedleViewCameraFb start");
    final errIndex = _bindings.embSewingChangeNeedleViewCameraFb();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingChangeNeedleViewCameraFb end");
    return errCode;
  }

  @override
  EmbLibraryError embSewingZoomCameraFb() {
    EmbLibraryLog.d("embSewingZoomCameraFb start");
    final errIndex = _bindings.embSewingZoomCameraFb();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingZoomCameraFb end");
    return errCode;
  }

  @override
  void embSewOverMarkPatCnct(bool flg) {
    EmbLibraryLog.d("embSewOverMarkPatCnct start");
    _bindings.embSewOverMarkPatCnct(flg);
    EmbLibraryLog.d("embSewOverMarkPatCnct end");
  }

  @override
  void embNextPatternSelMarkPatCnct() {
    EmbLibraryLog.d("embNextPatternSelMarkPatCnct start");
    _bindings.embNextPatternSelMarkPatCnct();
    EmbLibraryLog.d("embNextPatternSelMarkPatCnct end");
  }

  @override
  void embMarkPatCnctMove(int dir) {
    EmbLibraryLog.d("embMarkPatCnctMove start");
    _bindings.embMarkPatCnctMove(dir);
    EmbLibraryLog.d("embMarkPatCnctMove end");
  }

  @override
  EmbLibraryError embMarkPatCnctRotateLeft() {
    EmbLibraryLog.d("embMarkPatCnctRotateLeft start");
    final errIndex = _bindings.embMarkPatCnctRotateLeft();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embMarkPatCnctRotateLeft end");
    return errCode;
  }

  @override
  EmbLibraryError embMarkPatCnctRotateRight() {
    EmbLibraryLog.d("embMarkPatCnctRotateRight start");
    final errIndex = _bindings.embMarkPatCnctRotateRight();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embMarkPatCnctRotateRight end");
    return errCode;
  }

  @override
  EmbLibraryError embMarkPatCnctRotateRight90() {
    EmbLibraryLog.d("embMarkPatCnctRotateRight90 start");
    final errIndex = _bindings.embMarkPatCnctRotateRight90();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embMarkPatCnctRotateRight90 end");
    return errCode;
  }

  @override
  void embSetMarkPatCnctStdCancelBefore() {
    EmbLibraryLog.d("embSetMarkPatCnctStdCancelBefore start");
    _bindings.embSetMarkPatCnctStdCancelBefore();
    EmbLibraryLog.d("embSetMarkPatCnctStdCancelBefore end");
  }

  @override
  void embSetBefore1stMarkPatCnctOK() {
    EmbLibraryLog.d("embSetBefore1stMarkPatCnctOK start");
    _bindings.embSetBefore1stMarkPatCnctOK();
    EmbLibraryLog.d("embSetBefore1stMarkPatCnctOK end");
  }

  @override
  void embSetBefore2ndMarkPatCnctOK() {
    EmbLibraryLog.d("embSetBefore2ndMarkPatCnctOK start");
    _bindings.embSetBefore2ndMarkPatCnctOK();
    EmbLibraryLog.d("embSetBefore2ndMarkPatCnctOK end");
  }

  @override
  EmbLibraryError embSewingSelectMaskTrace() {
    EmbLibraryLog.d("embSewingSelectMaskTrace start");
    final errIndex = _bindings.embSewingSelectMaskTrace();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingSelectMaskTrace end");
    return errCode;
  }

  @override
  EmbLibraryError embSewingMaskTraceExe() {
    EmbLibraryLog.d("embSewingMaskTraceExe start");
    final errIndex = _bindings.embSewingMaskTraceExe();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingMaskTraceExe end");
    return errCode;
  }

  @override
  EmbLibraryError embSewingMaskTraceEnd() {
    EmbLibraryLog.d("embSewingMaskTraceEnd start");
    final errIndex = _bindings.embSewingMaskTraceEnd();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingMaskTraceEnd end");
    return errCode;
  }

  @override
  EmbLibraryError embSewingMaskTracePosSet(int pos) {
    EmbLibraryLog.d("embSewingMaskTracePosSet start");
    final errIndex = _bindings.embSewingMaskTracePosSet(pos);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingMaskTracePosSet end");
    return errCode;
  }

  @override
  EmbLibraryError embSewingSelectStartPosition() {
    EmbLibraryLog.d("embSewingSelectStartPosition start");
    final errIndex = _bindings.embSewingSelectStartPosition();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingSelectStartPosition end");
    return errCode;
  }

  @override
  EmbLibraryError embSewingStartPositionSet() {
    EmbLibraryLog.d("embSewingStartPositionSet start");
    final errIndex = _bindings.embSewingStartPositionSet();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingStartPositionSet end");
    return errCode;
  }

  @override
  EmbLibraryError embSewingStartPositionClear() => EmbLibraryError.EMB_NO_ERR;

  @override
  EmbLibraryError embSewingStartPositionEnd() {
    EmbLibraryLog.d("embSewingStartPositionEnd start");
    final errIndex = _bindings.embSewingStartPositionEnd();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.d("embSewingStartPositionEnd end");
    return errCode;
  }

  @override
  ({EmbLibraryError errCode, bool attr}) checkPatternNoMirrorNoCharacter(
      MemHandle patternH) {
    EmbLibraryLog.d("checkPatternNoMirrorNoCharacter  start");
    final libResult = ffi.calloc<dart_ffi.Bool>();

    final errIndex = _bindings.checkPatternNoMirrorNoCharacter(
        dart_ffi.Pointer.fromAddress(patternH), libResult);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    bool attr = libResult.value;

    ffi.calloc.free(libResult);
    EmbLibraryLog.d("checkPatternNoMirrorNoCharacter  end");
    return (errCode: errCode, attr: attr);
  }

  @override
  EmbLibraryError curGroupCharArrayBack() {
    EmbLibraryLog.hello("curGroupCharArrayBack");
    final errIndex = _bindings.curGroupCharArrayBack();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("curGroupCharArrayBack");
    return errCode;
  }

  @override
  EmbPatAutoKind getEmbMarkPatCntAutoKind() {
    EmbLibraryLog.hello("getEmbMarkPatCntAutoKind");
    final kindIndex = _bindings.getEmbMarkPatCntAutoKind();
    EmbLibraryLog.byeBye("getEmbMarkPatCntAutoKind");
    return EmbPatAutoKind.values[kindIndex];
  }

  @override
  void embMarkPatCnctEditParamSet(
      SSPoint pos, MarkPatCnctSetting cnctSetting, int angle) {
    EmbLibraryLog.hello("embMarkPatCnctEditParamSet");

    final libPos = embedit_bindings_generated.SSPoint_t.allocate();
    libPos.ref.X = pos.X;
    libPos.ref.Y = pos.Y;

    _bindings.embMarkPatCnctEditParamSet(libPos, cnctSetting.index, angle);

    ffi.calloc.free(libPos);
    EmbLibraryLog.byeBye("embMarkPatCnctEditParamSet");
  }

  @override
  EmbLibraryError embResumeDataRead(String resumeFilePath) {
    EmbLibraryLog.hello("embResumeDataRead");
    final filePathNative = resumeFilePath.toNativeUtf8();
    final errIndex =
        _bindings.embResumeDataRead(filePathNative.cast<dart_ffi.Char>());
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    ffi.calloc.free(filePathNative);
    EmbLibraryLog.byeBye("embResumeDataRead");
    return errCode;
  }

  @override
  EmbLibraryError checkMemoryForEmbSewing() {
    EmbLibraryLog.hello("checkMemoryForEmbSewing");
    final errIndex = _bindings.checkMemoryForEmbSewing();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("checkMemoryForEmbSewing");
    return errCode;
  }

  @override
  EmbLibraryError checkMemoryForEmbEdit() {
    EmbLibraryLog.hello("checkMemoryForEmbEdit");
    final errIndex = _bindings.checkMemoryForEmbEdit();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("checkMemoryForEmbEdit");
    return errCode;
  }

  @override
  EmbLibraryError checkEmbLargeCategorySelection(EmbLargeCategory cate) {
    EmbLibraryLog.hello("checkEmbLargeCategorySelection");
    final errIndex = _bindings.checkEmbLargeCategorySelection(cate.index);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("checkEmbLargeCategorySelection");
    return errCode;
  }

  @override
  double embMarkPatCnctDistanceGet() {
    EmbLibraryLog.hello("embMarkPatCnctDistanceGet");
    final markDis = ffi.calloc<dart_ffi.Double>();
    _bindings.embMarkPatCnctDistanceGet(markDis);
    double distance = markDis.value;
    ffi.calloc.free(markDis);
    EmbLibraryLog.byeBye("embMarkPatCnctDistanceGet");
    return distance;
  }

  @override
  EmbLibraryError emb1stAutoMarkPatCnctOK() {
    EmbLibraryLog.hello("emb1stAutoMarkPatCnctOK");
    final errIndex = _bindings.emb1stAutoMarkPatCnctOK();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("emb1stAutoMarkPatCnctOK");
    return errCode;
  }

  @override
  EmbLibraryError emb1stAutoMarkPatCnctCancel() {
    EmbLibraryLog.hello("emb1stAutoMarkPatCnctCancel");
    final errIndex = _bindings.emb1stAutoMarkPatCnctCancel();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("emb1stAutoMarkPatCnctCancel");
    return errCode;
  }

  @override
  EmbLibraryError emb1stAutoConfirmMarkPatCnctOk() {
    EmbLibraryLog.hello("emb1stAutoConfirmMarkPatCnctOk");
    final errIndex = _bindings.emb1stAutoConfirmMarkPatCnctOk();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("emb1stAutoConfirmMarkPatCnctOk");
    return errCode;
  }

  @override
  EmbLibraryError emb1stAutoConfirmMarkPatCnctCancel() {
    EmbLibraryLog.hello("emb1stAutoConfirmMarkPatCnctCancel");
    final errIndex = _bindings.emb1stAutoConfirmMarkPatCnctCancel();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("emb1stAutoConfirmMarkPatCnctCancel");
    return errCode;
  }

  @override
  EmbMarkPatImgDrawParam embMarkPatCnctImgDrawPrmGet(int markNo) {
    EmbLibraryLog.d("embMarkPatCnctImgDrawPrmGet  start");
    final libParam =
        embedit_bindings_generated.EmbMarkPatImgDrawParam_t.allocate();
    _bindings.embMarkPatCnctImgDrawPrmGet(markNo, libParam);

    EmbMarkPatImgDrawParam param = EmbMarkPatImgDrawParam(
      angle: libParam.ref.angle,
      x1: libParam.ref.x1,
      y1: libParam.ref.y1,
      x2: libParam.ref.x2,
      y2: libParam.ref.y2,
      offsetX: libParam.ref.offsetX,
      offsetY: libParam.ref.offsetY,
      scale: libParam.ref.scale,
      bPale: libParam.ref.bPale,
      draw: MarkPatCnctImgType.values[libParam.ref.draw],
      rect: RectanArea(
        left: libParam.ref.rect.Left,
        right: libParam.ref.rect.Right,
        top: libParam.ref.rect.Top,
        bottom: libParam.ref.rect.Bottom,
      ),
    );

    ffi.calloc.free(libParam);
    EmbLibraryLog.d("embMarkPatCnctImgDrawPrmGet  end");
    return param;
  }

  @override
  EmbMarkPatSnowmanDrawParam embMarkPatCnctSnowmanDrawPrmGet(int markNo) {
    EmbLibraryLog.d("embMarkPatCnctSnowmanDrawPrmGet  start");
    final libParam =
        embedit_bindings_generated.EmbMarkPatSnowmanDrawParam_t.allocate();
    _bindings.embMarkPatCnctSnowmanDrawPrmGet(markNo, libParam);

    EmbMarkPatSnowmanDrawParam param = EmbMarkPatSnowmanDrawParam(
      angleMark: libParam.ref.angleMark,
      x: libParam.ref.x,
      y: libParam.ref.y,
      offsetX: libParam.ref.offsetX,
      offsetY: libParam.ref.offsetY,
      scale: libParam.ref.scale,
    );

    ffi.calloc.free(libParam);
    EmbLibraryLog.d("embMarkPatCnctSnowmanDrawPrmGet  end");
    return param;
  }

  @override
  EmbLibraryError embSewingGotoMove() {
    EmbLibraryLog.hello("embSewingGotoMove");
    final errIndex = _bindings.embSewingGotoMove();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("embSewingGotoMove");
    return errCode;
  }

  @override
  EmbLibraryError embSewingGotoMoveQuilt() {
    EmbLibraryLog.hello("embSewingGotoMoveQuilt");
    final errIndex = _bindings.embSewingGotoMoveQuilt();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("embSewingGotoMoveQuilt");
    return errCode;
  }

  @override
  EmbLibraryError embSewingGotoRotate() {
    EmbLibraryLog.hello("embSewingGotoRotate");
    final errIndex = _bindings.embSewingGotoRotate();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("embSewingGotoRotate");
    return errCode;
  }

  @override
  EmbLibraryError embSewingGotoRotateQuilt() {
    EmbLibraryLog.hello("embSewingGotoRotateQuilt");
    final errIndex = _bindings.embSewingGotoRotateQuilt();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("embSewingGotoRotateQuilt");
    return errCode;
  }

  @override
  EmbLibraryError embSewingReturnMoveOrRotate() {
    EmbLibraryLog.hello("embSewingReturnMoveOrRotate");
    final errIndex = _bindings.embSewingReturnMoveOrRotate();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("embSewingReturnMoveOrRotate");
    return errCode;
  }

  @override
  EmbLibraryError cancelApplique() {
    EmbLibraryLog.hello("cancelApplique");
    final errIndex = _bindings.cancelApplique();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("cancelApplique");
    return errCode;
  }

  @override
  EmbLibraryError cancelPreviewApplique() {
    EmbLibraryLog.hello("cancelPreviewApplique");
    final errIndex = _bindings.cancelPreviewApplique();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("cancelPreviewApplique");
    return errCode;
  }

  @override
  EmbLibraryError checkUsableAppliqueParts() {
    EmbLibraryLog.hello("checkUsableAppliqueParts");
    final errIndex = _bindings.checkUsableAppliqueParts();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("checkUsableAppliqueParts");
    return errCode;
  }

  @override
  void embMarkPatCnctSetScanResultImage(
      {required Uint8List lastImgData,
      required int lastImgWidth,
      required int lastImgHeight,
      required Uint8List nowImgData,
      required int nowImgWidth,
      required int nowImgHeight,
      required int r,
      required int g,
      required int b}) {
    EmbLibraryLog.hello("embMarkPatCnctSetScanResultImage");

    final lastImgPtr = ffi.calloc<dart_ffi.Uint8>(lastImgData.length);
    final lastImgDataByte = lastImgPtr.asTypedList(lastImgData.length);
    lastImgDataByte.setAll(0, lastImgData);

    final nowImgPtr = ffi.calloc<dart_ffi.Uint8>(nowImgData.length);
    final nowImgDataByte = nowImgPtr.asTypedList(nowImgData.length);
    nowImgDataByte.setAll(0, nowImgData);

    _bindings.embMarkPatCnctSetScanResultImage(
      lastImgPtr,
      lastImgWidth,
      lastImgHeight,
      nowImgPtr,
      nowImgWidth,
      nowImgHeight,
      r,
      g,
      b,
    );
    ffi.calloc.free(lastImgPtr);
    ffi.calloc.free(nowImgPtr);
    EmbLibraryLog.byeBye("embMarkPatCnctSetScanResultImage");
  }

  @override
  Uint8List? embMarkPatCnctGetScanResultImage() {
    EmbLibraryLog.hello("embMarkPatCnctGetScanResultImage");
    if (_resultImgPtr == dart_ffi.nullptr) {
      _resultImgPtr =
          ffi.calloc<dart_ffi.Uint8>(_resultImgWidth * _resultImgHeight * 3);
    } else {
      /// Do nothing
    }

    bool result = _bindings.embMarkPatCnctGetScanResultImage(_resultImgPtr);
    EmbLibraryLog.d("embMarkPatCnctGetScanResultImage result:$result");

    Uint8List? image;
    if (result == false) {
      image = null;
    } else {
      /// RGBデータから画像データを生成する
      img.Image imageData =
          img.Image(width: _resultImgWidth, height: _resultImgHeight);

      for (int y = 0; y < _resultImgHeight; y++) {
        for (int x = 0; x < _resultImgWidth; x++) {
          int index = (y * _resultImgWidth + x) * 3;
          int r = _resultImgPtr[index]; // R
          int g = _resultImgPtr[index + 1]; // G
          int b = _resultImgPtr[index + 2]; // B

          imageData.setPixelRgb(x, y, r, g, b);
        }
      }
      image = Uint8List.fromList(img.encodePng(imageData));
    }

    EmbLibraryLog.byeBye("embMarkPatCnctGetScanResultImage");
    return image;
  }

  @override
  void embMarkPatCnctDelScanResultImage() {
    EmbLibraryLog.hello("embMarkPatCnctDelScanResultImage");
    ffi.calloc.free(_resultImgPtr);
    _resultImgPtr = dart_ffi.nullptr;
    _bindings.embMarkPatCnctDelScanResultImage();
    EmbLibraryLog.byeBye("embMarkPatCnctDelScanResultImage");
  }

  @override
  MarkPatCnctSetting embMarkPatCnctSettingGet() {
    EmbLibraryLog.hello("embMarkPatCnctSettingGet");
    final libResult = _bindings.embMarkPatCnctSettingGet();
    final result = MarkPatCnctSetting.values[libResult];
    EmbLibraryLog.byeBye("embMarkPatCnctSettingGet");
    return result;
  }

  @override
  void embMarkPatCnctScrSetting(int posX, int posY) {
    EmbLibraryLog.hello("embMarkPatCnctScrSetting");
    _bindings.embMarkPatCnctScrSetting(posX, posY);
    EmbLibraryLog.byeBye("embMarkPatCnctScrSetting");
  }

  @override
  EmbLibraryError embSewingEscUnit() {
    EmbLibraryLog.hello("embSewingEscUnit");
    final errIndex = _bindings.embSewingEscUnit();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("embSewingEscUnit");
    return errCode;
  }

  @override
  EmbLibraryError embSewingMoveEscUnit() {
    EmbLibraryLog.hello("embSewingMoveEscUnit");
    final errIndex = _bindings.embSewingMoveEscUnit();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("embSewingMoveEscUnit");
    return errCode;
  }

  @override
  EmbLibraryError embSewingReturnEscUnit() {
    EmbLibraryLog.hello("embSewingReturnEscUnit");
    final errIndex = _bindings.embSewingReturnEscUnit();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("embSewingReturnEscUnit");
    return errCode;
  }

  @override
  EmbLibraryError embSewingSnowmanScanCancelFrameMove() {
    EmbLibraryLog.hello("embSewingSnowmanScanCancelFrameMove");
    final errIndex = _bindings.embSewingSnowmanScanCancelFrameMove();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("embSewingSnowmanScanCancelFrameMove");
    return errCode;
  }

  @override
  EmbLibraryError exchangeErrCode(int errorCode) {
    EmbLibraryLog.hello("exchangeErrCode");
    final errIndex = _bindings.exchangeErrCode(errorCode);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("exchangeErrCode");
    return errCode;
  }

  @override
  int getEmbSewPatternConnect() {
    EmbLibraryLog.hello("getEmbSewPatternConnect");
    final result = _bindings.getEmbSewPatternConnect();
    EmbLibraryLog.byeBye("getEmbSewPatternConnect");
    return result;
  }

  @override
  bool isEmbSewingMoveEscUnit() {
    EmbLibraryLog.hello("isEmbSewingMoveEscUnit");
    final libResult = _bindings.isEmbSewingMoveEscUnit();
    EmbLibraryLog.byeBye("isEmbSewingMoveEscUnit");
    return libResult;
  }

  @override
  EmbLibraryError embEditReturnSelectGray() {
    EmbLibraryLog.hello("embEditReturnSelectGray");
    final errIndex = _bindings.embEditReturnSelectGray();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("embEditReturnSelectGray");
    return errCode;
  }

  @override
  bool isRestrictionEmb() {
    EmbLibraryLog.hello("isRestrictionEmb");
    final libResult = _bindings.isRestrictionEmb();
    EmbLibraryLog.byeBye("isRestrictionEmb");
    return libResult;
  }

  @override
  bool isVolatileEmb() {
    EmbLibraryLog.hello("isVolatileEmb");
    final libResult = _bindings.isVolatileEmb();
    EmbLibraryLog.byeBye("isVolatileEmb");
    return libResult;
  }

  @override
  void setRestrictionCurEmbGroup(int restriction) {
    EmbLibraryLog.hello("setRestrictionCurEmbGroup");
    _bindings.setRestrictionCurEmbGroup(restriction);
    EmbLibraryLog.byeBye("setRestrictionCurEmbGroup");
  }

  @override
  void setVolatileCurEmbGroup(bool isVolatile) {
    EmbLibraryLog.hello("setVolatileCurEmbGroup");
    _bindings.setVolatileCurEmbGroup(isVolatile);
    EmbLibraryLog.byeBye("setVolatileCurEmbGroup");
  }

  @override
  EmbLibraryError embSelectReturnEdit() {
    EmbLibraryLog.hello("embSelectReturnEdit");
    final errIndex = _bindings.embSelectReturnEdit();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("embSelectReturnEdit");
    return errCode;
  }

  @override
  EmbLibraryError selectEmbGroupNext() {
    EmbLibraryLog.hello("selectEmbGroupNext");
    final errIndex = _bindings.selectEmbGroupNext();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("selectEmbGroupNext");
    return errCode;
  }

  @override
  EmbLibraryError selectEmbGroupPrev() {
    EmbLibraryLog.hello("selectEmbGroupPrev");
    final errIndex = _bindings.selectEmbGroupPrev();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("selectEmbGroupPrev");
    return errCode;
  }

  @override
  EmbLibraryError gotoEmbFromOpening() {
    EmbLibraryLog.hello("gotoEmbFromOpening");
    final errIndex = _bindings.gotoEmbFromOpening();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    if (errCode == EmbLibraryError.EMB_NO_ERR) {
      _openEmb = true;
    }
    EmbLibraryLog.byeBye("gotoEmbFromOpening");
    return errCode;
  }

  @override
  EmbLibraryError finishOutline() {
    EmbLibraryLog.hello("finishOutline");
    final errIndex = _bindings.finishOutline();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("finishOutline");
    return errCode;
  }

  @override
  EmbLibraryError notPatternAreaSelectWork() {
    EmbLibraryLog.hello("notPatternAreaSelectWork");
    final errIndex = _bindings.notPatternAreaSelectWork();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("notPatternAreaSelectWork");
    return errCode;
  }

  @override
  SSPoint embMarkPatCnctSettingOffsetGet() {
    EmbLibraryLog.hello("embMarkPatCnctSettingOffsetGet");
    final libPos = embedit_bindings_generated.SSPoint_t.allocate();
    _bindings.embMarkPatCnctSettingOffsetGet(libPos);
    final offset = SSPoint(X: libPos.ref.X, Y: libPos.ref.Y);
    EmbLibraryLog.byeBye("embMarkPatCnctSettingOffsetGet offset : $offset");
    return offset;
  }

  @override
  EmbLibraryError setBorderCurIdx(int index) {
    EmbLibraryLog.hello("setBorderCurIdx index:$index start");
    final errIndex = _bindings.setBorderCurIdx(index);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("setBorderCurIdx end");
    return errCode;
  }

  @override
  EmbLibraryError failureCancelStipple() {
    EmbLibraryLog.hello("failureCancelStipple");
    final errIndex = _bindings.failureCancelStipple();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("failureCancelStipple end");
    return errCode;
  }

  @override
  EmbLibraryError resetEmbRotate() {
    EmbLibraryLog.hello("resetEmbRotate");
    final errIndex = _bindings.resetEmbRotate();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("resetEmbRotate end");
    return errCode;
  }

  @override
  EmbLibraryError resetEmbAllRotate() {
    EmbLibraryLog.hello("resetEmbAllRotate");
    final errIndex = _bindings.resetEmbAllRotate();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("resetEmbAllRotate end");
    return errCode;
  }

  @override
  EmbLibraryError deleteBorderMarkForStippleError() {
    EmbLibraryLog.hello("deleteBorderMarkForStippleError");
    final errIndex = _bindings.deleteBorderMarkForStippleError();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("deleteBorderMarkForStippleError end");
    return errCode;
  }

  @override
  EmbLibraryError cancelSelectedApplique() {
    EmbLibraryLog.hello("cancelSelectedApplique start");
    final errIndex = _bindings.cancelSelectedApplique();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("cancelSelectedApplique end");
    return errCode;
  }

  @override
  ({EmbLibraryError errorCode, RectanArea area}) getEmbArea() {
    EmbLibraryLog.hello("getEmbArea start");
    final libArea = ffi.calloc<embedit_bindings_generated.RectanArea_t>();
    final errIndex = _bindings.getEmbArea(libArea);
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    final area = RectanArea(
      left: libArea.ref.Left,
      right: libArea.ref.Right,
      top: libArea.ref.Top,
      bottom: libArea.ref.Bottom,
    );
    ffi.calloc.free(libArea);
    EmbLibraryLog.byeBye("getEmbArea end");
    return (errorCode: errCode, area: area);
  }

  @override
  EmbLibraryError goAppliqueParameterSetting() {
    EmbLibraryLog.hello("goAppliqueParameterSetting start");
    final errIndex = _bindings.goAppliqueParameterSetting();
    final errCode = EmbLibraryErrorExtension.getValuesByIndex(errIndex);
    EmbLibraryLog.byeBye("goAppliqueParameterSetting end");
    return errCode;
  }

  @override
  void deleteEmbGrpPatternInfo() {
    EmbLibraryLog.hello("deleteEmbGrpPatternInfo start");
    EmbLibraryLog.byeBye("deleteEmbGrpPatternInfo end");
  }
}

/// 布張替え時等の画面の表示画像
/// 画像のサイズは固定です (x=740 y=580  RGB)
const int _resultImgWidth = 740;
const int _resultImgHeight = 580;
