package com.example.customscreensaver

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import java.io.File

/**
 * Bitmapを格納・読み込みするリポジトリです。不要になった際に、
 * [BitmapRepository.clearCache]を呼び出してメモリ使用量を解放してください。
 */
object BitmapRepository {

  private var cache: MutableMap<String, Bitmap>? = null

  /**
   * ファイルからBitmapをロードします。このファイルが既にロードされている場合、
   * キャッシュから取得します。
   */
  fun loadBitmap(imgFile: File): Bitmap? {
    if (cache == null) {
      cache = mutableMapOf()
    }
    return try {
      cache?.getOrPut(imgFile.absolutePath) {
        BitmapFactory.decodeFile(imgFile.absolutePath)
      }
    } catch (e: Throwable) {
      e.printStackTrace()
      null
    }
  }

  /**
   * キャッシュをクリアします。呼び出し後、
   * キャッシュ済みの全ての[Bitmap]が使用するメモリが回収されます。
   */
  fun clearCache() {
    cache?.values?.forEach {
      if (!it.isRecycled) {
        it.recycle()
      }
    }
    cache = null
  }
}
