import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:path/path.dart';

import '../../../../memory/memory.dart';
import 'decorative_popup_model.dart';
import 'motif_popup_model.dart';
import 'paint/clip_board_model.dart';
import 'paint/draw_canvas_model.dart';
import 'paint/drawing_type_model.dart';
import 'paint/edit_object_model.dart';
import 'paint/line_property_popup_model.dart';
import 'paint/pen_model.dart';
import 'paint/scan_model.dart';
import 'paint/select_model.dart';
import 'paint/surface_property_popup_model.dart';
import 'paint/toolbar_model.dart';
import 'paint/top_bar_model.dart';
import 'stitch/creation_isolate.dart';
import 'stitch/draw_region_model.dart';

///
/// 現在どのインターフェイスにありますか
///
enum ResumePageType {
  paint,
  stitch,
}

class ResumeHistoryModel {
  ResumeHistoryModel._internal();

  factory ResumeHistoryModel() => _instance;
  static final ResumeHistoryModel _instance = ResumeHistoryModel._internal();

  ///
  /// スナップショット保存数
  ///
  int mdcSnapSize = 0;

  ///
  /// Undo可能な回数
  ///
  int mdcUndoSize = 0;

  ///
  /// 次にスナップショットを保存するIndex
  ///
  int mdcSnapPoint = 0;

  ///
  /// 現在存在するインタフェースタイプです
  ///
  ResumePageType currentPageType = ResumePageType.paint;

  ///
  /// 最大スナップショットファイル数
  ///
  static const int maxSnapSize = 51;

  ///
  /// Undo操作開始位置
  ///
  static const int undoStartIndex = 1;

  ///
  /// スナップショットファイルの開始位置
  ///
  static const int snapStartIndex = 0;

  ///
  /// Userの保存情報
  ///
  List<MdcHistory> _historyList = [];

  ///
  /// 保存必要かどうか
  ///
  bool _enableShot = false;
  void setEnableShort(bool value) => _enableShot = value;
  bool getEnableShort() => _enableShot;

  bool _isResumeStitchPage = false;
  bool get isResumeStitchPage => _isResumeStitchPage;

  ///
  /// 初期化
  ///
  void initBeforeUseMdc() {
    mdcSnapSize = DeviceLibrary().apiBinding.getMdcSnapSize();
    mdcUndoSize = DeviceLibrary().apiBinding.getMdcUndoSize();
    mdcSnapPoint = DeviceLibrary().apiBinding.getMdcSnapPoint();
    _historyList = [];

    /// 初期化時に背景画像が更新された状態です
    _maybeDisplayBackGroundImage();

    /// クリップボード初期化
    _maybeHasClipBoard();

    /// 履歴ファイル情報を読み込み,画面に戻します
    _restoreSnapshot(isResume: true);
  }

  ///
  /// 1つ前の作業内容に戻る
  ///
  void undo() {
    if (mdcUndoSize <= undoStartIndex) {
      return;
    }

    mdcSnapPoint--;
    if (mdcSnapPoint < snapStartIndex) {
      mdcSnapPoint = maxSnapSize - undoStartIndex;
    }
    mdcUndoSize--;

    DeviceLibrary().apiBinding.setMdcUndoSize(mdcUndoSize);
    DeviceLibrary().apiBinding.setMdcSnapPoint(mdcSnapPoint);

    _restoreSnapshot(isResume: false);

    if (currentPageType == ResumePageType.stitch) {
      CreationModel().changeStitchCreation();
    } else {
      /// Do Nothing
    }
  }

  ///
  /// 1つ先の作業内容に進む
  ///
  void redo() {
    if (mdcUndoSize >= mdcSnapSize) {
      return;
    }

    mdcSnapPoint++;
    if (mdcSnapPoint >= maxSnapSize) {
      mdcSnapPoint = snapStartIndex;
    }
    mdcUndoSize++;

    DeviceLibrary().apiBinding.setMdcUndoSize(mdcUndoSize);
    DeviceLibrary().apiBinding.setMdcSnapPoint(mdcSnapPoint);

    _restoreSnapshot(isResume: false);

    if (currentPageType == ResumePageType.stitch) {
      CreationModel().changeStitchCreation();
    } else {
      /// Do Nothing
    }
  }

  ///
  /// 保存しているUndo/Redo用のデータ数を取得する
  ///
  int historySize() => mdcSnapSize;

  ///
  /// Undo可能かチェックする
  ///
  bool existUndoData() {
    if (mdcUndoSize <= undoStartIndex) {
      return false;
    }
    return true;
  }

  ///
  /// Redo可能かチェックする
  ///
  bool existRedoData() {
    if (mdcUndoSize >= mdcSnapSize) {
      return false;
    }
    return true;
  }

  ///
  /// resume歴史処理を拒絶為にLockフラグを追加する
  ///
  bool _lock = false;
  bool isResumeProcessLocked() => _lock;
  void lockResumeProcess() => _lock = true;
  void unlockResumeProcess() => _lock = false;

  ///
  /// resume状態があるかどうかです
  ///
  bool isEntryResume() => DeviceLibrary().apiBinding.getMdcResumeState();

  ///
  /// resumeから離脱します
  ///
  void exitResume() {
    DeviceLibrary().apiBinding.setMdcResumeState(false);
    DeviceLibrary().apiBinding.setMdcBackgroundState(false);
  }

  ///
  /// resume状態になります。
  ///
  void entryResume() => DeviceLibrary().apiBinding.setMdcResumeState(true);

  ///
  /// 表示領域を記憶保存する
  ///
  void saveMdcScreen() {
    switch (DrawingTypeModel.mdcDrawingType) {
      case MdcDrawingType.drawtype_pencil:
      case MdcDrawingType.drawtype_line_fill:
      case MdcDrawingType.drawtype_fixed_shape:
      case MdcDrawingType.drawtype_area_select:
        backSnapshot();
        break;
      case MdcDrawingType.drawtype_blush:
      case MdcDrawingType.drawtype_surface_fill:
        final isNoSew = SurfacePropertyPopupModel().sewKindsSurface ==
            MdcSewKindsSurface.noSew;
        if (isNoSew && mdcSnapPoint == 0) {
          return;
        }
        backSnapshot();
        break;
      case MdcDrawingType.drawtype_eraser:
        if (mdcSnapPoint > 0) {
          backSnapshot();
          break;
        }
      default:
        break;
    }
  }

  ///
  /// 現在の作業内容を保存する
  ///
  void backSnapshot() {
    /// undo/redoのIndex更新
    mdcSnapPoint++;
    if (mdcSnapPoint >= maxSnapSize) {
      mdcSnapPoint = 0;
    }
    if (mdcUndoSize < maxSnapSize) {
      mdcUndoSize++;
    }
    mdcSnapSize = mdcUndoSize;

    DeviceLibrary().apiBinding.setMdcSnapSize(mdcSnapSize);
    DeviceLibrary().apiBinding.setMdcUndoSize(mdcUndoSize);
    DeviceLibrary().apiBinding.setMdcSnapPoint(mdcSnapPoint);
    if (mdcSnapPoint > 0) {
      entryResume();
    } else {
      /// Do Nothing
    }

    /// 作業内容を保存する
    final String rootDirectory = memorySector.mdcUdo.absolutePath;
    final String fileCount = mdcSnapPoint.toString().padLeft(2, "0");
    final String fileName = join(rootDirectory, "MdcDraw$fileCount.his");
    final String userFileName =
        join(rootDirectory, "UserMdcDraw$fileCount.his");

    /// 操作を保存する（レジューム、Undo/Redo操作）
    MdcLibrary().apiBinding.registMdcEditHistory(fileName);

    /// User 操作を保存
    _registerUserHistory(userFileName);
  }

  ///
  /// 今回の変更履歴で前回の変更履歴を上書き
  ///
  void coverSnapshot() {
    String rootDirectory = memorySector.mdcUdo.absolutePath;

    /// Lib履歴働作値を取ります
    String fileName = join(
        rootDirectory, "MdcDraw${mdcSnapPoint.toString().padLeft(2, "0")}.his");

    /// 操作を保存する（レジューム、Undo/Redo操作）
    MdcLibrary().apiBinding.registMdcEditHistory(fileName);
  }

  ///
  /// 履歴を消去します
  ///
  void clearAllHistory() {
    mdcSnapSize = mdcSnapDefaultSize;
    mdcUndoSize = mdcUndoDefaultSize;
    mdcSnapPoint = mdcSnapDefaultPoint;

    DeviceLibrary().apiBinding.setMdcSnapSize(mdcSnapSize);
    DeviceLibrary().apiBinding.setMdcUndoSize(mdcUndoSize);
    DeviceLibrary().apiBinding.setMdcSnapPoint(mdcSnapPoint);

    _historyList = [];
  }

  ///
  /// リスト追加
  ///
  void addUserHistoryList(CanvasPenState penState,
      {required bool isLastPoint}) {
    if (LinePropertyPopupModel().isLineCloseType == false) {
      _historyList = [];
      return;
    }

    final MdcHistory history;
    if (isLastPoint) {
      history = const MdcHistory(
        penPositionList: [],
        penFirstPosition: CanvasLinePoint.invalidPosition,
        penSecondPosition: CanvasLinePoint.invalidPosition,
        penPrePosition: CanvasLinePoint.invalidPosition,
        penLastPosition: CanvasLinePoint.invalidPosition,
        clipPositionList: [],
        clipFirstPosition: CanvasLinePoint.invalidPosition,
        clipSecondPosition: CanvasLinePoint.invalidPosition,
        clipPrePosition: CanvasLinePoint.invalidPosition,
        clipLastPosition: CanvasLinePoint.invalidPosition,
      );
    } else {
      history = MdcHistory(
        penPositionList: LinePropertyPopupModel().isLineCloseType
            ? penState.lineStatus.positionList
            : [],
        penFirstPosition: LinePropertyPopupModel().isLineCloseType
            ? penState.position.firstPosition
            : CanvasLinePoint.invalidPosition,
        penSecondPosition: LinePropertyPopupModel().isLineCloseType
            ? penState.position.secondPosition
            : CanvasLinePoint.invalidPosition,
        penPrePosition: LinePropertyPopupModel().isLineCloseType
            ? penState.position.prePosition
            : CanvasLinePoint.invalidPosition,
        penLastPosition: LinePropertyPopupModel().isLineCloseType
            ? penState.position.lastPosition
            : CanvasLinePoint.invalidPosition,
        clipPositionList: [],
        clipFirstPosition: CanvasLinePoint.invalidPosition,
        clipSecondPosition: CanvasLinePoint.invalidPosition,
        clipPrePosition: CanvasLinePoint.invalidPosition,
        clipLastPosition: CanvasLinePoint.invalidPosition,
      );
    }

    _historyList.add(history);
  }

  ///
  /// user hisファイル保存する
  ///
  void _registerUserHistory(String path) {
    final MdcHistory history;
    if (_historyList.isEmpty) {
      history = const MdcHistory(
        penPositionList: [],
        penFirstPosition: CanvasLinePoint.invalidPosition,
        penSecondPosition: CanvasLinePoint.invalidPosition,
        penPrePosition: CanvasLinePoint.invalidPosition,
        penLastPosition: CanvasLinePoint.invalidPosition,
        clipPositionList: [],
        clipFirstPosition: CanvasLinePoint.invalidPosition,
        clipSecondPosition: CanvasLinePoint.invalidPosition,
        clipPrePosition: CanvasLinePoint.invalidPosition,
        clipLastPosition: CanvasLinePoint.invalidPosition,
      );
    } else {
      history = _historyList.removeAt(0);
    }

    try {
      final File file = File(path);
      List<Map<String, double>> clipPositionListMap = [];
      for (var element in history.clipPositionList) {
        clipPositionListMap.add(
          {
            'dx': element.dx,
            'dy': element.dy,
          },
        );
      }
      List<Map<String, int>> penPositionListMap = [];
      for (var element in history.penPositionList) {
        penPositionListMap.add(
          {
            'dx': element.X,
            'dy': element.Y,
          },
        );
      }

      final String data = jsonEncode({
        /// pen
        'penPositionList': penPositionListMap,
        'penFirstPosition': {
          'dx': history.penFirstPosition.dx,
          'dy': history.penFirstPosition.dy,
        },
        'penSecondPosition': {
          'dx': history.penSecondPosition.dx,
          'dy': history.penSecondPosition.dy
        },
        'penPrePosition': {
          'dx': history.penPrePosition.dx,
          'dy': history.penPrePosition.dy,
        },
        'penLastPosition': {
          'dx': history.penLastPosition.dx,
          'dy': history.penLastPosition.dy,
        },

        /// clip
        'clipPositionList': clipPositionListMap,
        'clipFirstPosition': {
          'dx': history.clipFirstPosition.dx,
          'dy': history.clipFirstPosition.dy,
        },
        'clipSecondPosition': {
          'dx': history.clipSecondPosition.dx,
          'dy': history.clipSecondPosition.dy,
        },
        'clipPrePosition': {
          'dx': history.clipPrePosition.dx,
          'dy': history.clipPrePosition.dy,
        },
        'clipLastPosition': {
          'dx': history.clipLastPosition.dx,
          'dy': history.clipLastPosition.dy,
        },
      });

      file.writeAsStringSync(data, flush: true);
    } catch (e) {
      Log.e(tag: "MDC RESUME", description: "save user info error $e");
    }
  }

  ///
  /// user his読み取ります
  ///
  MdcHistory? _loadMdcHistoryFromFile(String path) {
    try {
      final File file = File(path);
      final String data = file.readAsStringSync();
      final jsonData = jsonDecode(data);

      /// Clip
      List<Offset> clipOffsetList = [];
      final jsonClipList = jsonData['clipPositionList'];
      if (jsonClipList.isNotEmpty) {
        jsonClipList.forEach((element) {
          final x = element['dx'];
          final y = element['dy'];
          if (x == null || y == null) {
            clipOffsetList.clear();
            return;
          }
          Offset point = Offset(x, y);
          clipOffsetList.add(point);
        });
      }

      /// Pen
      List<SSPoint> penOffsetList = [];
      final jsonPenList = jsonData['penPositionList'];
      if (jsonPenList.isNotEmpty) {
        jsonPenList.forEach((element) {
          final x = element['dx'];
          final y = element['dy'];
          if (x == null || y == null) {
            penOffsetList.clear();
            return;
          }
          penOffsetList.add(SSPoint(X: x, Y: y));
        });
      }

      /// pen
      final MdcHistory history;
      if (penOffsetList.isEmpty) {
        history = const MdcHistory(
          penPositionList: [],
          penFirstPosition: CanvasLinePoint.invalidPosition,
          penSecondPosition: CanvasLinePoint.invalidPosition,
          penLastPosition: CanvasLinePoint.invalidPosition,
          penPrePosition: CanvasLinePoint.invalidPosition,

          /// clip
          clipPositionList: [],
          clipFirstPosition: ClipBoardState.invalidPosition,
          clipSecondPosition: ClipBoardState.invalidPosition,
          clipPrePosition: ClipBoardState.invalidPosition,
          clipLastPosition: ClipBoardState.invalidPosition,
        );
      } else {
        history = MdcHistory(
          penPositionList: penOffsetList,
          penFirstPosition: Offset(
            jsonData['penFirstPosition']['dx'] ?? CanvasLinePoint.invalidValue,
            jsonData['penFirstPosition']['dy'] ?? CanvasLinePoint.invalidValue,
          ),
          penSecondPosition: Offset(
            jsonData['penSecondPosition']['dx'] ?? CanvasLinePoint.invalidValue,
            jsonData['penSecondPosition']['dy'] ?? CanvasLinePoint.invalidValue,
          ),
          penLastPosition: Offset(
            jsonData['penLastPosition']['dx'] ?? CanvasLinePoint.invalidValue,
            jsonData['penLastPosition']['dy'] ?? CanvasLinePoint.invalidValue,
          ),
          penPrePosition: Offset(
            jsonData['penPrePosition']['dx'] ?? CanvasLinePoint.invalidValue,
            jsonData['penPrePosition']['dy'] ?? CanvasLinePoint.invalidValue,
          ),

          /// clip
          clipPositionList: [],
          clipFirstPosition: ClipBoardState.invalidPosition,
          clipSecondPosition: ClipBoardState.invalidPosition,
          clipPrePosition: ClipBoardState.invalidPosition,
          clipLastPosition: ClipBoardState.invalidPosition,
        );
      }

      return history;
    } catch (e) {
      return null;
    }
  }

  ///
  /// 初期化時に背景画像が更新された状態です。
  ///
  void _maybeDisplayBackGroundImage() {
    /// 背景画像存在するのでしょうか
    EditObjectModel().existBackImage =
        DeviceLibrary().apiBinding.getMdcBackgroundState();
    if (EditObjectModel().existBackImage == true) {
      /// 背景ファイルのチェック
      final FileEntity infoFile = FileEntity(ScanModel().selectInfoSavePath);
      final FileEntity dataFile = FileEntity(ScanModel().selectDataSavePath);
      final bool isFileExist = infoFile.existsSync() && dataFile.existsSync();

      if (isFileExist) {
        /// お絵描き画像の描画濃度をセットする (デフォルト値)
        const densityLevel = DensityLevel.mdc75BackGround25;
        TopBarModel().densityLevelIndex = densityLevel.index;
        MdcLibrary().apiBinding.setMdcImageDrawingDensity(densityLevel.number);
        ScanModel().loadBackgroundImage();
      } else {
        /// お絵描き画像の描画濃度をセットする (不透過)
        const densityLevel = DensityLevel.mdc100BackGround0;
        TopBarModel().densityLevelIndex = densityLevel.index;
        MdcLibrary().apiBinding.setMdcImageDrawingDensity(densityLevel.number);

        EditObjectModel().existBackImage = false;
        DeviceLibrary().apiBinding.setMdcBackgroundState(false);
      }
    } else {
      ScanModel().clearBackgroundImage();
    }
  }

  ///
  /// レシューム起動の場合、クリップボード初期化
  ///
  void _maybeHasClipBoard() {
    final bool existClipboard =
        DeviceLibrary().apiBinding.getMdcClipboardState();

    /// 保存したのClipboard値は「false」
    if (!existClipboard) {
      EditObjectModel().existClipboard = false;
      return;
    }

    /// 保存したのClipboard値は「true」
    final String clipFile =
        join(memorySector.mdcUdo.absolutePath, ToolBarModel.cutFileName);
    final bool isFileExist = FileEntity(clipFile).existsSync();

    /// Clipファイル存在しないの場合
    if (isFileExist == false) {
      EditObjectModel().existClipboard = false;
      DeviceLibrary().apiBinding.setMdcClipboardState(false);
    } else {
      EditObjectModel().existClipboard = true;
    }
  }

  ///
  /// 履歴ファイル情報を読み込み,画面に戻します
  ///
  void _restoreSnapshot({required bool isResume}) {
    String rootDirectory = memorySector.mdcUdo.absolutePath;

    /// Lib履歴働作値を取ります
    String fileName = join(
        rootDirectory, "MdcDraw${mdcSnapPoint.toString().padLeft(2, "0")}.his");

    /// ユーザ働作履歴値です
    String userFileName = join(rootDirectory,
        "UserMdcDraw${mdcSnapPoint.toString().padLeft(2, "0")}.his");

    /// 線プロパティ画面でのAPIセット内容更新
    MotifPopupModel().setMdcImportFileList();

    /// 面プロパティ画面でのAPIセット内容更新
    DecorativePopupModel().setMdcImportFileList();

    /// 保存したデータを読み込む
    final ({
      MdcLibraryError errCode,
      bool existBackImage,
      bool existClipboard,
      MdcImageInfo imgInfoBackGround,
      MdcImageInfo imgInfoParts,
      MdcUnsettledObjectInfo objInfo,
      MdcReqProc reqProc,
      MdcHistSubParam subParam
    }) mdcHistory;
    if (isResume) {
      mdcHistory = MdcLibrary().apiBinding.loadMdcEditHistoryResume(fileName);
    } else {
      mdcHistory = MdcLibrary().apiBinding.loadMdcEditHistory(fileName);
    }

    final _MdcHistory history = _MdcHistory(
      imgInfoBackGround: mdcHistory.imgInfoBackGround,
      imgInfoParts: mdcHistory.imgInfoParts,
      objInfo: mdcHistory.objInfo,
      reqProc: mdcHistory.reqProc,
      subParam: mdcHistory.subParam,
    );

    /// 模様編集画面復帰
    final historyType = history.subParam.historyType;
    final isInStitchPage =
        historyType == MdcHistoryType.historyType_region_multi ||
            historyType == MdcHistoryType.historyType_region_single;

    DrawingTypeModel.mdcDrawingType = mdcHistory.subParam.drawingType;
    MdcLibrary().apiBinding.setMdcDrawingType(DrawingTypeModel.mdcDrawingType);

    /// 縫製設定画面復帰
    if (isInStitchPage) {
      _isResumeStitchPage = true;
      DrawRegionModel().setOperation(
        historyType == MdcHistoryType.historyType_region_single
            ? Operation.single
            : Operation.multiLink,
      );

      /// お絵描きイメージを取得する
      DrawRegionModel().updateCanvasImage();
      return;
    }

    /// お絵描き画面復帰
    _restoreModelValues(history);
    _restoreEditObject(history);
    _restoreDrawingInfo(history, userFileName, isResume: isResume);
  }

  ///
  /// ペン先の更新
  ///
  void restoreCanvasNib() {
    final drawingType = DrawingTypeModel.mdcDrawingType;
    switch (drawingType) {
      case MdcDrawingType.drawtype_pencil:
        DrawCanvasModel().updateOutLineNib();
        break;
      case MdcDrawingType.drawtype_blush:
        DrawCanvasModel().updateBrushNib();
        break;
      case MdcDrawingType.drawtype_eraser:
        DrawCanvasModel().updateEraserNib();
        break;
      default:
        break;
    }
  }

  ///
  /// お絵描き画面内容復帰
  /// mdcDrawingType : 描画種別復帰
  /// reqProc : ﾌﾟﾛｾｽ情報更新
  /// imgInfoParts : 部分模様
  /// imgInfoBackGround : 背景模様
  /// objInfo : 編集指示情報
  ///
  void _restoreEditObject(_MdcHistory mdcHistory) {
    int historyType = mdcHistory.subParam.historyType;
    if (historyType <= MdcHistoryType.historyType_invalid ||
        historyType >= MdcHistoryType.historyType_end) {
      Log.errorTrace("ライブラリエラー");
    }

    EditObjectModel().reqProc = mdcHistory.reqProc;
    EditObjectModel().objectInfo = mdcHistory.objInfo;

    if (mdcHistory.subParam.historyType ==
        MdcHistoryType.historyType_draw_part) {
      if (mdcHistory.reqProc.status != InstructionProgressStatus.during) {
        Log.errorTrace(
            "loadMdcEditHistory実行後reqProc.status不正です。 実際値：${mdcHistory.reqProc.status}  予期値：InstructionProgressStatus.during ");
      }
      DrawCanvasModel().penState = CanvasPenState.init();
      EditObjectModel().updateMdcSize();
      EditObjectModel().setMdcBothImageInfo(
          updateParts: mdcHistory.imgInfoParts,
          updateBackground: mdcHistory.imgInfoBackGround,
          needUpdateUI: true);
    } else {
      EditObjectModel()
          .setMdcPartsImageInfo(MdcImageInfo.empty(), needUpdateUI: false);
      EditObjectModel().updateCanvasImageWithoutParts();
    }
  }

  ///
  /// 各Model値復帰
  ///
  void _restoreModelValues(_MdcHistory mdcHistory) {
    /// ToolBar
    LinePropertyPopupModel().lineToolType =
        MdcLineToolTypes.values[mdcHistory.subParam.toolType[0]];

    /// Bottom
    /// 範囲選択タイプ
    if (mdcHistory.subParam.drawingType ==
        MdcDrawingType.drawtype_area_select) {
      SelectModel.selectTypes = MdcClippingTypes.getValuesByNumber(
          mdcHistory.subParam.areaSelectType);
    } else {
      SelectModel.selectTypes = SelectModel.defaultType;
    }
  }

  ///
  /// 描画情報復帰
  ///
  void _restoreDrawingInfo(_MdcHistory mdcHistory, String userFileName,
      {required bool isResume}) {
    /// ユーザーが保存した履歴です
    final MdcHistory? userMdcHistory = _loadMdcHistoryFromFile(userFileName);

    /// Clip初期化
    ClipBoardModel().resetPositionList();
    ClipBoardModel().clipBoardProperty = ClipBoardProperty();

    /// Pen初期化
    if (isResume) {
      DrawCanvasModel().penState = CanvasPenState.init();
    } else {
      PenModel().resetDrawingState();
    }

    /// ペン先の更新
    restoreCanvasNib();

    if (mdcHistory.subParam.toolType[0] == MdcLineToolTypes.lineClose.number &&
        userMdcHistory != null) {
      DrawCanvasModel().penState.lineStatus.positionList =
          userMdcHistory.penPositionList;
      DrawCanvasModel().penState.position = CanvasLinePoint(
        firstPosition: userMdcHistory.penFirstPosition,
        secondPosition: userMdcHistory.penSecondPosition,
        lastPosition: userMdcHistory.penLastPosition,
        prePosition: userMdcHistory.penPrePosition,
      );
    }
  }
}

///
/// 履歴読み込み
///
class _MdcHistory {
  const _MdcHistory({
    required this.imgInfoBackGround,
    required this.imgInfoParts,
    required this.objInfo,
    required this.reqProc,
    required this.subParam,
  });
  final MdcImageInfo imgInfoBackGround;
  final MdcImageInfo imgInfoParts;
  final MdcUnsettledObjectInfo objInfo;
  final MdcReqProc reqProc;
  final MdcHistSubParam subParam;
}
