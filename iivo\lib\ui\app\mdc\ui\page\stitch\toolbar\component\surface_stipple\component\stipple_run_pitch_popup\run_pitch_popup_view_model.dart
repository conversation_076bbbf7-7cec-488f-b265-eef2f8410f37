import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_stipple_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'run_pitch_popup_view_interface.dart';

final runPitchViewModelProvider = StateNotifierProvider.autoDispose<
    RunPitchPopupStateViewInterface,
    RunPitchPopupState>((ref) => RunPitchViewModel(ref));

class RunPitchViewModel extends RunPitchPopupStateViewInterface {
  RunPitchViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const RunPitchPopupState(
              isRunPitchMinusToLimit: false,
              isRunPitchPlusToLimit: false,
              runPitchInputValue: "",
              runPitchDisplayTextStyle: false,
            ),
            ref) {
    update();
  }

  @override
  void update() {
    state = state.copyWith(
      isRunPitchMinusToLimit: _isMinusToLimit(),
      isRunPitchPlusToLimit: _isPlusToLimit(),
      runPitchInputValue: _getRunPitchDisplayValue(),
      runPitchDisplayTextStyle: _getRunPitchDisplayTextStyle(),
    );
  }

  ///
  /// ステップ量
  ///
  final int _stepValue = 10;

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  @override
  int defaultValue = SurfaceStippleModel().runPitchDefaultValue;

  ///
  /// RunPitch値ディスプレイスター
  ///
  bool _isRunPitchValueDisplayStar = SurfaceStippleModel().getRunPitch() !=
          SurfaceStippleModel.runPitchNotUpdating
      ? false
      : true;

  ///
  /// RunPitch値
  ///
  int _runPitchValue = SurfaceStippleModel().getRunPitch();

  @override
  bool plusRunPitch(bool isLongPress) {
    if (_isRunPitchValueDisplayStar) {
      _isRunPitchValueDisplayStar = false;

      ///  Model 更新
      _runPitchValue = defaultValue;

      /// View更新
      update();
      return false;
    }
    if (_runPitchValue >= SurfaceStippleModel.maxiRunPitchValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    if (isLongPress == true) {
      _runPitchValue -= _runPitchValue % _stepValue;
      if (_runPitchValue != SurfaceStippleModel.maxiRunPitchValue) {
        _runPitchValue += _stepValue;
      }
    } else {
      _runPitchValue++;
    }
    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// View 更新
    update();
    return true;
  }

  @override
  bool miniRunPitch(bool isLongPress) {
    if (_isRunPitchValueDisplayStar) {
      _isRunPitchValueDisplayStar = false;

      ///  Model 更新
      _runPitchValue = defaultValue;

      /// View更新
      update();
      return false;
    }

    if (_runPitchValue <= SurfaceStippleModel.miniRunPitchValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    if (isLongPress == true) {
      _runPitchValue -= _runPitchValue % _stepValue;
      if (_runPitchValue != SurfaceStippleModel.miniRunPitchValue) {
        _runPitchValue -= _stepValue;
      }
    } else {
      _runPitchValue--;
    }
    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// View 更新
    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.surfaceStippleRunPitch.toString());
    if (_isRunPitchValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int runPitchValue = SurfaceStippleModel().getRunPitch();

    /// Model更新
    SurfaceStippleModel().setRunPitch(_runPitchValue);
    if (SurfaceStippleModel().setStippleSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (runPitchValue != _runPitchValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// RunPitchの表示値を取得する
  ///
  String _getRunPitchDisplayValue() {
    /// cmからmmへ
    double surfaceStippleRunPitchValue = _runPitchValue / _stepValue;
    if (_isRunPitchValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return surfaceStippleRunPitchValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(surfaceStippleRunPitchValue);
  }

  ///
  /// 縮小ボタンの状態の取得
  ///
  bool _isMinusToLimit() {
    if (_runPitchValue <= SurfaceStippleModel.miniRunPitchValue) {
      return true;
    }
    return false;
  }

  ///
  /// 増大ボタン状態の取得
  ///
  bool _isPlusToLimit() {
    if (_runPitchValue >= SurfaceStippleModel.maxiRunPitchValue) {
      return true;
    }
    return false;
  }

  ///
  /// テキストスタイルの取得
  ///
  bool _getRunPitchDisplayTextStyle() {
    if (_isRunPitchValueDisplayStar) {
      return true;
    }

    if (_runPitchValue == defaultValue) {
      return true;
    }

    return false;
  }
}
