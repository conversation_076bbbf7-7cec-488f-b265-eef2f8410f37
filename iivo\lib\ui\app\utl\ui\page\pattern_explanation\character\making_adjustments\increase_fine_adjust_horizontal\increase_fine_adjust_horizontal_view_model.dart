import 'dart:async';
import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../model/pattern_explanation_model.dart';
import '../../../../../page_route.dart';
import 'increase_fine_adjust_horizontal_view_interface.dart';

final increaseFineAdjustHorizontalViewModelViewModelProvider =
    StateNotifierProvider.autoDispose<IncreaseFineAdjustHorizontalViewModel,
            IncreaseFineAdjustHorizontalState>(
        (ref) => IncreaseFineAdjustHorizontalViewModel(ref));

class IncreaseFineAdjustHorizontalViewModel
    extends ViewModel<IncreaseFineAdjustHorizontalState> {
  IncreaseFineAdjustHorizontalViewModel(this._ref)
      : super(const IncreaseFineAdjustHorizontalState()) {
    _ref.listen(
      appDisplayUtlStateProvider
          .select((value) => value.utlFuncSetting.ref.isUtlSewing),
      (previous, nextState) {
        update();
      },
    );
    update();
  }

  final AutoDisposeStateNotifierProviderRef _ref;

  ///
  /// 「+/-」ボタンを長押しするときのタイマー
  ///
  Timer? _timer;

  ///
  /// ViewModel更新
  ///
  @override
  void update() {
    state = state.copyWith(
      fineAdjustHorizontalDisplayValue: PatternExplanationModel()
          .changeValueToDisplay(
              DeviceLibrary().apiBinding.getFineAdjustHoriz().value),
      isStopingMotor: TpdLibrary()
              .apiBinding
              .bpIFGetAppDisplayUtl()
              .utlFuncSetting
              .ref
              .isUtlSewing ==
          false,
    );
  }

  ///
  /// タイマーを消します
  ///
  void timerCancel() {
    _timer?.cancel();
  }

  ///
  /// Horizontal値が減少するクリック関数
  ///
  bool onHorizontalDecreaseButtonClicked(bool isLongPress) {
    /// Horizontalの値
    int fineAdjustHorizontal =
        DeviceLibrary().apiBinding.getFineAdjustHoriz().value;
    if (fineAdjustHorizontal <=
        PatternExplanationModel.fineAdjustMinHorizontal) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    } else {
      if (!isLongPress) {
        SystemSoundPlayer().play(SystemSoundEnum.accept);
      }
      fineAdjustHorizontal--;

      /// Libに通知
      DeviceLibrary().apiBinding.setFineAdjustHoriz(fineAdjustHorizontal);

      /// View更新
      update();

      return true;
    }
  }

  ///
  /// Horizontal値が増加したクリック関数
  ///
  bool onHorizontalIncreaseButtonClick(bool isLongPress) {
    int fineAdjustHorizontal =
        DeviceLibrary().apiBinding.getFineAdjustHoriz().value;
    if (fineAdjustHorizontal >=
        PatternExplanationModel.fineAdjustMaxHorizontal) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    } else {
      if (!isLongPress) {
        SystemSoundPlayer().play(SystemSoundEnum.accept);
      }
      fineAdjustHorizontal++;

      /// Libに通知
      DeviceLibrary().apiBinding.setFineAdjustHoriz(fineAdjustHorizontal);

      /// View更新
      update();

      return true;
    }
  }

  ///
  /// Returnボタンをクリック
  ///
  void onReturnButtonClick() {
    PagesRoute().pop();
  }
}
