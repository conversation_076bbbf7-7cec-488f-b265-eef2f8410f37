import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/machine_config_model.dart';
import '../../../../../../../../model/projector_model.dart';
import '../../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../../../model/provider/task/device_error_polling_task.dart';
import '../../../../../../../global_popup/global_popup_export.dart';
import '../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../global_popup/global_popups/err_applique_ng_complex/err_applique_ng_complex_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_applique_ng_distance/err_applique_ng_distance_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_applique_ng_ex_in_overlap/err_applique_ng_ex_in_overlap_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_applique_ng_mem_over/err_applique_ng_mem_over_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_cur_delete_border_ok/err_cur_delete_border_ok_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_delete_border_mark_ok_easysttiple/err_delete_border_mark_ok_easysttiple_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_delete_border_mark_ok_group/err_delete_border_mark_ok_group_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_delete_border_mark_ok_un_group/err_delete_border_mark_ok_un_group_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_delete_border_mark_ok_wappen/err_delete_border_mark_ok_wappen_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_emb_pattern_exceeded/err_emb_pattern_exceeded_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_emb_too_much_selected_go_menu/err_emb_too_much_selected_go_menu_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_mcd_not_exchange_area_over/err_mcd_not_exchange_area_over_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_outside_of_emb_frm_nouse/err_outside_of_emb_frm_nouse_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_red_square_over/err_red_square_over_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_this_pattern_too_complex/err_this_pattern_too_complex_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../../model/applique_model.dart';
import '../../../../../model/border_model.dart';
import '../../../../../model/color_change_model.dart';
import '../../../../../model/edit_model.dart';
import '../../../../../model/key_board_font_model.dart';
import '../../../../../model/pattern_model.dart';
import '../../../../../model/redo_undo_model.dart';
import '../../../../../model/scan_model.dart';
import '../../../../../model/stipple_model.dart';
import '../../../../../model/thread_color_list_model.dart';
import '../../../../page_route.dart';
import '../../../common_component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../../pattern_edit_view_model.dart';
import 'toolbar_view_interface.dart';

final toolbarViewModelProvider =
    StateNotifierProvider.autoDispose<ToolbarInterface, ToolbarState>(
        (ref) => ToolbarViewModel(ref));

class ToolbarViewModel extends ToolbarInterface {
  ToolbarViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const ToolbarState(), ref) {
    update();
    _listenStatus();
  }

  @override
  void update() {
    final EmbEditAttribParam attribute =
        TpdLibrary().apiBinding.bPIFGetAppDisplayEmb().embEditAttrib.ref;

    state = state.copyWith(
      isEnglish: PatternModel().isEnglish,
      isToolPopupOn: EditModel().toolbarPopupId != ToolbarPopupId.edit,
      isScanPopupOpen: ScanModel().isEditPageScanPopupOpen,
      isProjectorON: ProjectorModel().embProjector.isEmbProjectorViewOpen,
      isSizeButtonEnable: _isSizeButtonEnable(attribute.sizeState),
      isMoveButtonEnable: _isMoveButtonEnable(attribute.moveState),
      groupButtonState:
          _getGroupButtonState(attribute.groupState, attribute.group),
      isRotateButtonEnable: _isRotateButtonEnable(attribute.rotateState),
      flipButtonState:
          _getFlipButtonState(attribute.editFlipState, attribute.editFlip),
      isCopyButtonEnable: _isCopyButtonEnable(attribute.copyState),
      isDensityButtonEnable: _isDensityButtonEnable(attribute.densityState),
      isBorderButtonEnable: _isBorderButtonEnable(attribute.borderState),
      isAppliqueButtonEnable: _isAppliqueButtonEnable(attribute.appliqueState),
      isColorChangeButtonEnable:
          _isColorChangeButtonEnable(attribute.changeColorState),
      isTextEditButtonEnable: _isTextEditButtonEnable(attribute.textEditState),
      isAlignmentButtonEnable:
          _isAlignmentButtonEnable(attribute.alignmentState),
      isStipplingButtonEnable:
          _isStipplingButtonEnable(attribute.stipplingState),
      isOutlineButtonEnable: _isOutlineButtonEnable(attribute.outlineState),
      isOrderButtonEnable: _isOrderButtonEnable(attribute.orderState),
      isNotSewingEnable: _isNotSewingEnable(attribute.notSewingState),
    );
  }

  @override
  void updateByScanPopupChange() {
    state =
        state.copyWith(isScanPopupOpen: ScanModel().isEditPageScanPopupOpen);
  }

  @override
  void onSizeButtonClicked(BuildContext context) {
    if (state.isSizeButtonEnable == false) {
      return;
    }

    /// Model更新
    EditModel().toolbarPopupId = ToolbarPopupId.sizeAdjustment;
    PopupNavigator.pushNamed(
            context: context, nextRouteName: PopupEnum.sizeAdjustment)
        .then((value) {
      /// Model更新
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      /// View更新
      update();

      /// 他の画面を更新する
      _updateOtherWidget();
    });

    /// View更新
    update();

    /// 他の画面を更新する
    _updateOtherWidget();
  }

  @override
  void onMoveButtonClicked(BuildContext context) {
    if (state.isMoveButtonEnable == false) {
      return;
    }

    PopupNavigator.pushNamed(context: context, nextRouteName: PopupEnum.move)
        .then((value) {
      /// Model更新
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      /// View更新
      update();

      /// 他の画面を更新する
      _updateOtherWidget();
    });

    /// Model更新
    EditModel().toolbarPopupId = ToolbarPopupId.move;

    /// View更新
    update();

    /// 他の画面を更新する
    _updateOtherWidget();
  }

  @override
  void onGroupButtonClick() {
    if (state.groupButtonState == ButtonState.disable) {
      return;
    }

    /// 組み合わせの場合
    if (state.groupButtonState == ButtonState.normal) {
      /// 組み合わせられたPatternに糸印が存在する
      if (BorderModel().isAllSelectedPatternHasBorderMark() == true) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_DELETE_BORDER_MARK_OK_GROUP,
          arguments: ErrDeleteBorderMarkOkGroupArgument(
            onOKButtonClicked: () {
              GlobalPopupRoute().resetErrorState();

              /// Lib更新
              final EmbLibraryError error = PatternModel().groupPatterns();
              if (error == EmbLibraryError.EMB_REDSQUAREOVER_ERR) {
                GlobalPopupRoute().updateErrorState(
                  nextRoute: GlobalPopupRouteEnum.ERR_RED_SQUARE_OVER,
                  arguments: ErrRedSquareOverArgument(
                    onOKButtonClicked: GlobalPopupRoute().resetErrorState,
                  ),
                );
                return;
              } else if (error == EmbLibraryError.EMB_GRPHANDLE_ERR) {
                SystemSoundPlayer().play(SystemSoundEnum.invalid);
                return;
              } else if (error == EmbLibraryError.EMB_NOSELECT_ERR) {
                SystemSoundPlayer().play(SystemSoundEnum.invalid);
                return;
              } else if (error != EmbLibraryError.EMB_NO_ERR) {
                return;
              }
              final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
              if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
                return;
              }

              /// Model更新
              if (RedoUndoModel().saveUndoRedoFile() !=
                  EmbLibraryError.EMB_NO_ERR) {
                GlobalPopupRoute().updateErrorState(
                  nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
                  arguments: TroubleOccoredPowerOffArgument(
                    onOKButtonClicked: GlobalPopupRoute().resetErrorState,
                  ),
                );
                return;
              }

              /// View更新
              update();

              /// 他の画面を更新する
              _updateOtherWidget();

              /// プロジェクト投影画面を更新する
              final embProjectorFunction =
                  ref.read(embProjectorFunctionProvider.notifier);
              embProjectorFunction.maybeUpdateProjectorScreen(
                  redrawEmbPattern: true);
            },
            onCancelButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      /// Lib更新
      final EmbLibraryError error = PatternModel().groupPatterns();
      if (error == EmbLibraryError.EMB_REDSQUAREOVER_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_RED_SQUARE_OVER,
          arguments: ErrRedSquareOverArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      } else if (error == EmbLibraryError.EMB_GRPHANDLE_ERR) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      } else if (error == EmbLibraryError.EMB_NOSELECT_ERR) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      } else if (error != EmbLibraryError.EMB_NO_ERR) {
        return;
      }

      final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
      if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
        return;
      }
    }

    /// 組み合わせ解除の場合
    else {
      /// 選択されたパターンには糸印があります
      if (BorderModel().isCurrentPatternHasBorderMark() == true) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_DELETE_BORDER_MARK_OK_UNGROUP,
          arguments: ErrDeleteBorderMarkOkUnGroupArgument(
            onOKButtonClicked: () {
              /// Lib更新
              PatternModel().unGroupPatterns();
              if (RedoUndoModel().saveUndoRedoFile() !=
                  EmbLibraryError.EMB_NO_ERR) {
                GlobalPopupRoute().updateErrorState(
                  nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
                  arguments: TroubleOccoredPowerOffArgument(
                    onOKButtonClicked: GlobalPopupRoute().resetErrorState,
                  ),
                );
                return;
              }

              /// View更新
              update();

              /// 他の画面を更新する
              _updateOtherWidget();

              /// プロジェクト投影画面を更新する
              final embProjectorFunction =
                  ref.read(embProjectorFunctionProvider.notifier);
              embProjectorFunction.maybeUpdateProjectorScreen(
                  redrawEmbPattern: true);

              GlobalPopupRoute().resetErrorState();
            },
            onCancelButtonClicked: () => GlobalPopupRoute().resetErrorState(),
          ),
        );
        return;
      } else {
        /// model更新
        PatternModel().unGroupPatterns();
      }
    }

    /// Model更新
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }

    /// view 更新
    update();

    /// 他の画面を更新する
    _updateOtherWidget();

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onRotateButtonClicked(BuildContext context) {
    if (state.isRotateButtonEnable == false) {
      return;
    }

    PopupNavigator.pushNamed(context: context, nextRouteName: PopupEnum.rotate)
        .then((value) {
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      /// View更新
      update();

      /// 他の画面を更新する
      _updateOtherWidget();
    });

    /// Model更新
    EditModel().toolbarPopupId = ToolbarPopupId.rotate;

    /// View更新
    update();

    /// 他の画面を更新する
    _updateOtherWidget();
  }

  @override
  void onFlipButtonClicked(BuildContext context) {
    if (state.flipButtonState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final error = EditModel().flipEmbHorizontal();

    if (error == EmbLibraryError.EMB_REDSQUAREOVER_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_RED_SQUARE_OVER,
        arguments: ErrRedSquareOverArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    } else if (error == EmbLibraryError.EMB_PATTERN_EXCEEDED) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_EMB_PATTERN_EXCEEDED,
        arguments: EmbPatternExceededArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    } else if (error == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    /// Model更新
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }

    /// View更新
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 他の画面を更新する
    _updateOtherWidget();

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onCopyButtonClick() {
    if (state.isCopyButtonEnable == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    EmbLibraryError error = EditModel().copyPattern();
    if (error == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    /// Model更新
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }

    /// View更新
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 他の画面を更新する
    _updateOtherWidget();

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onDensityButtonClicked(BuildContext context) {
    if (state.isDensityButtonEnable == false) {
      return;
    }

    /// Model更新
    EditModel().toolbarPopupId = ToolbarPopupId.density;

    PopupNavigator.pushNamed(context: context, nextRouteName: PopupEnum.density)
        .then((value) {
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      /// View更新
      update();

      /// 他の画面を更新する
      _updateOtherWidget();
    });

    /// View更新
    update();

    /// 他の画面を更新する
    _updateOtherWidget();
  }

  @override
  void onBorderButtonClicked(BuildContext context) {
    if (state.isBorderButtonEnable == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final borderError = EmbLibrary().apiBinding.checkBeforeBorderProc();
    if (borderError != EmbLibraryError.EMB_NO_ERR) {
      if (borderError == EmbLibraryError.EMB_REDSQUAREOVER_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_RED_SQUARE_OVER,
          arguments: ErrRedSquareOverArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }
      if (borderError == EmbLibraryError.EMB_CHANGE_ORDER_WARN) {
        errEmbChangeOrderOkFunc = () {
          GlobalPopupRoute().resetErrorState();
          _changeGroupUp(context);
        };
        errEmbChangeOrderCancelFunc =
            () => GlobalPopupRoute().resetErrorState();
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_EMB_CHANGE_ORDER,
        );
        return;
      }
      return;
    }

    final error = BorderModel().startEmbBorder();
    if (error == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// Model更新
    EditModel().toolbarPopupId = ToolbarPopupId.border;

    /// View更新
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    update();

    /// 他の画面を更新する
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);
    PopupNavigator.pushNamed(context: context, nextRouteName: PopupEnum.border)
        .then((value) {
      /// Model更新
      BorderModel().endEmbBorder();
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      /// View更新
      update();

      /// 他の画面を更新する
      _updateOtherWidget();
    });
  }

  @override
  void onAppliqueButtonClick(BuildContext context) {
    if (state.isAppliqueButtonEnable == false) {
      return;
    }

    EmbLibraryError embErrorCode = AppliqueModel().initAppliqueSelect();

    /// Borderの場合
    if (embErrorCode == EmbLibraryError.EMB_CURDELETEBORDER_WARN) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_CUR_DELETE_BORDER_OK,
        arguments: ErrCurDeleteBorderOkArgument(
          onCancelButtonClicked: GlobalPopupRoute().resetErrorState,
          onOKButtonClicked: () {
            /// Model更新
            AppliqueModel().cancelBorderBeforeWappen();
            if (RedoUndoModel().saveUndoRedoFile() !=
                EmbLibraryError.EMB_NO_ERR) {
              GlobalPopupRoute().updateErrorState(
                nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
                arguments: TroubleOccoredPowerOffArgument(
                  onOKButtonClicked: GlobalPopupRoute().resetErrorState,
                ),
              );
              return;
            }
            GlobalPopupRoute().resetErrorState();

            /// 他の画面を更新する
            _updateOtherWidget();

            _goToAppliquePage(context);
          },
        ),
      );
      return;
    } else if (embErrorCode == EmbLibraryError.EMB_DELBORDERMARK_WARN) {
      /// Border糸印の場合
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_DELETE_BORDER_MARK_OK_WAPPEN,
        arguments: ErrDeleteBorderMarkOkWappenArgument(
          onCancelButtonClicked: GlobalPopupRoute().resetErrorState,
          onOKButtonClicked: () {
            /// Model更新
            AppliqueModel().deleteMarkBeforeWappen();
            if (RedoUndoModel().saveUndoRedoFile() !=
                EmbLibraryError.EMB_NO_ERR) {
              GlobalPopupRoute().updateErrorState(
                nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
                arguments: TroubleOccoredPowerOffArgument(
                  onOKButtonClicked: GlobalPopupRoute().resetErrorState,
                ),
              );
              return;
            }
            GlobalPopupRoute().resetErrorState();

            /// 他の画面を更新する
            _updateOtherWidget();

            _goToAppliquePage(context);
          },
        ),
      );
      return;
    }

    /// エラーがないの場合
    _goToAppliquePage(context);
  }

  @override
  void onColorChangeButtonClicked() {
    if (state.isColorChangeButtonEnable == false) {
      return;
    }

    /// Modelの更新
    ThreadColorListModel().selectedGroupIndex = 0;
    ColorChangeModel().selectPatternIndex = 0;
    ColorChangeModel().selectColorIndex = 0;
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }
    PatternModel()
      ..selectedZoomScaleInEditPage = zoomList.first
      ..clearAllPatternImageCache();

    /// ページをジャンプ
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.colorChange).then((value) {
      /// 他の画面を更新する
      _updateOtherWidget();
    });
  }

  @override
  void onTextEditButtonClick(BuildContext context) {
    if (state.isTextEditButtonEnable == false) {
      return;
    }

    /// Model更新
    KeyBoardFontModel().startEmbCharEdit(true);
    KeyBoardFontModel().getReEditData();

    /// 他の画面を更新する
    _updateOtherWidget();

    PagesRoute()
        .pushNamed(nextRoute: PageRouteEnum.characterFontSelect)
        .then((value) {
      /// 他の画面を更新する
      _updateOtherWidget();
    });
  }

  @override
  void onAlignmentButtonClicked(BuildContext context) {
    if (state.isAlignmentButtonEnable == false) {
      return;
    }

    /// Model更新
    if (RedoUndoModel().changeCurrentUndoRedoFile() !=
        EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }
    EditModel().toolbarPopupId = ToolbarPopupId.alignment;
    EmbLibrary().apiBinding.enterMultiSelectMode();
    PatternModel().getCurrentGroup().selectMultiEmb(true);

    PopupNavigator.pushNamed(
            context: context, nextRouteName: PopupEnum.alignment)
        .then((value) {
      /// Model更新
      EmbLibrary().apiBinding.exitMultiSelectMode();
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      /// View更新
      update();

      /// 他の画面を更新する
      _updateOtherWidget();
    });

    /// View更新
    update();

    /// 他の画面を更新する
    _updateOtherWidget();

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onStipplingButtonClicked(BuildContext context) {
    if (state.isStipplingButtonEnable == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 糸印有り無しを確認する
    final borderMarkError = EmbLibrary().apiBinding.checkBorderMarkStipple();
    if (borderMarkError == EmbLibraryError.EMB_DELETE_BORDERMARK_WARN) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_DELETE_BORDER_MARK_OK_EASYSTTIPLE,
        arguments: ErrDeleteBorderMarkOkEasysttipleArgument(
          onCancelButtonClicked: (_) => GlobalPopupRoute().resetErrorState(),
          onOKButtonClicked: (_) {
            /// Model 更新
            EmbLibrary().apiBinding.deleteBorderMarkForStippleError();

            /// BorderからGroupに変わることがある　および　選択の模様が更新されたため、
            /// loadNewPattern関数を使用して選択の模様を再生成します。
            PatternModel().loadNewPattern(markToReloadPatterns: []);

            /// UNDOファイルを保存します。
            if (RedoUndoModel().saveUndoRedoFile() !=
                EmbLibraryError.EMB_NO_ERR) {
              GlobalPopupRoute().updateErrorState(
                nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
                arguments: TroubleOccoredPowerOffArgument(
                  onOKButtonClicked: GlobalPopupRoute().resetErrorState,
                ),
              );
              return;
            }

            /// 他の画面を更新する
            _updateOtherWidget();

            /// Lib更新
            final EmbLibraryError initError =
                EmbLibrary().apiBinding.initMakingEasyStipple();
            if (initError == EmbLibraryError.EMB_INVALID_ERR) {
              SystemSoundPlayer().play(SystemSoundEnum.invalid);
              return;
            } else if (initError != EmbLibraryError.EMB_NO_ERR) {
              _showInitMakingEasyStippleError(initError);
              return;
            }

            final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
            if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index &&
                isAppError(bPIFErrorPointer.errorCode) == false) {
              SystemSoundPlayer().play(SystemSoundEnum.invalid);
              return;
            }

            /// ERR_DELETE_BORDER_MARK_OK_EASYSTTIPLEを閉じる
            GlobalPopupRoute().resetErrorState();
            SystemSoundPlayer().play(SystemSoundEnum.accept);

            /// Stipple画面に遷移する
            _goToStipple(context);
          },
        ),
      );
      return;
    } else if (borderMarkError != EmbLibraryError.EMB_NO_ERR) {
      return;
    }

    /// Lib更新
    final EmbLibraryError error =
        EmbLibrary().apiBinding.initMakingEasyStipple();
    if (error == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else if (error != EmbLibraryError.EMB_NO_ERR) {
      _showInitMakingEasyStippleError(error);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// Stipple画面に遷移する
    _goToStipple(context);
  }

  @override
  void onOutlineButtonClicked(BuildContext context) {
    if (state.isOutlineButtonEnable == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// Lib更新
    final error =
        EditModel().makeOutline(outlineDistanceDefaultValue, false, true);
    if (error == EmbLibraryError.EMB_OUTSIDE_OF_EMB_FRM_NOUSE) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_OUTSIDE_OF_EMB_FRM_NOUSE,
          arguments: ErrOutsideOfEmbFrmNouseArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
      return;
    } else if (error == EmbLibraryError.EMB_TOOCOMPLEX_ERR ||
        error == EmbLibraryError.EMB_MAKEOUTLINE_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_THIS_PATTERN_TOO_COMPLEX,
          arguments: ErrThisPatternTooComplexArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
      return;
    } else if (error == EmbLibraryError.EMB_DISTANCE_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_APPLIQUE_NG_DISTANCE,
          arguments: ErrAppliqueNgDistanceArgument(
            onOKButtonClicked: (_) => GlobalPopupRoute().resetErrorState(),
          ));
      return;
    } else if (error == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else if (error != EmbLibraryError.EMB_NO_ERR) {
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    if (!mounted) {
      return;
    }

    /// view更新
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// outlineに入る
    PopupNavigator.pushNamed(
        context: context, nextRouteName: PopupEnum.outline);
  }

  @override
  void onOrderChangeButtonClick(BuildContext context) {
    if (state.isOrderButtonEnable == false) {
      return;
    }

    /// Model更新
    EditModel().toolbarPopupId = ToolbarPopupId.order;

    PopupNavigator.pushNamed(context: context, nextRouteName: PopupEnum.order)
        .then((value) {
      /// Model更新
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }
      EditModel().toolbarPopupId = ToolbarPopupId.edit;

      /// View更新
      update();

      /// 他の画面を更新する
      _updateOtherWidget();
    });

    /// 他の画面を更新する
    _updateOtherWidget();
  }

  @override
  void onNotSewingButtonClick(BuildContext context) {
    if (state.isNotSewingEnable == false) {
      return;
    }
    PopupNavigator.pushNamed(
        context: context, nextRouteName: PopupEnum.notSewing);

    /// Model更新
    EditModel().toolbarPopupId = ToolbarPopupId.notSewing;
  }

  @override
  void dispose() {
    super.dispose();

    /// 状態リッスンの停止
    _listener?.close();
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// 内部で使われる関数群
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// Applique作成ページのエラー処理判定を入力します
  ///
  void _goToAppliquePage(BuildContext context) {
    /// KIT未認証時、Tacony仕様は、従来通りのフロー・画面とする
    /// KIT認証後は、タイプ選択画面（従来アップリケ・色パーツを布に入れ替えるアップリケ）以降が追加される。
    if (MachineConfigModel().isKitSNCValid == true) {
      PopupNavigator.pushNamed(
          context: context, nextRouteName: PopupEnum.applique);
      return;
    }

    EmbLibraryError embErrorCode = AppliqueModel().makeApplique();
    switch (embErrorCode) {
      case EmbLibraryError.EMB_MCD_NOT_EXCHANGE_AREA_OVER:
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_MCD_NOT_EXCHANGE_AREA_OVER,
          arguments: McdNotExchangeAreaOverArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        break;
      case EmbLibraryError.EMB_DISTANCE_ERR:
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_APPLIQUE_NG_DISTANCE,
          arguments: ErrAppliqueNgDistanceArgument(
            onOKButtonClicked: (_) => GlobalPopupRoute().resetErrorState(),
          ),
        );
        break;
      case EmbLibraryError.EMB_APPLIQUE_NG_MEM_OVER:
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_APPLIQUE_NG_MEM_OVER,
          arguments: AppliqueNgMemOverArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        break;
      case EmbLibraryError.EMB_OVERLAP_ERR:
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_APPLIQUE_NG_EX_IN_OVERLAP,
          arguments: AppliqueNgExInOverlapArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        break;
      case EmbLibraryError.EMB_APPLIQUE_NG_COMPLEX:
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_APPLIQUE_NG_COMPLEX,
          arguments: AppliqueNgComplexlapArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        break;
      case EmbLibraryError.EMB_NO_ERR:
        if (TpdLibrary().apiBinding.bpIFGetError().errorCode !=
            ErrCode_t.ERR_DUMMY.index) {
          GlobalPopupRoute().resetErrorState();
        } else {
          /// DoNothing
        }
        AppliqueModel().setDistanceApplique(
          AppliqueModel().getAppliqueDistanceValue() ??
              AppliqueModel().outLineDistanceDefaultValue,
        );
        PopupNavigator.pushNamed(
            context: context, nextRouteName: PopupEnum.appliqueOutline);
        break;
      default:
        break;
    }
  }

  ///
  /// errEmbChangeOrderのokボタン
  /// 並べ替えを変更する
  ///
  void _changeGroupUp(BuildContext context) {
    final error = BorderModel().startEmbBorder();
    if (error == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// Model更新
    PatternModel().reloadAllPattern();

    EditModel().toolbarPopupId = ToolbarPopupId.border;

    /// View更新
    update();

    /// 他の画面を更新する
    _updateOtherWidget();

    PopupNavigator.pushNamed(context: context, nextRouteName: PopupEnum.border)
        .then((_) {
      /// Model更新
      BorderModel().endEmbBorder();
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      /// View更新
      update();

      /// 他の画面を更新する
      _updateOtherWidget();
    });
  }

  ///
  /// initMakingEasyStippleのエラーを処理する
  ///
  void _showInitMakingEasyStippleError(EmbLibraryError error) {
    if (error == EmbLibraryError.EMB_OUTSIDE_OF_EMB_FRM_NOUSE) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_OUTSIDE_OF_EMB_FRM_NOUSE,
          arguments: ErrOutsideOfEmbFrmNouseArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
      return;
    } else if (error == EmbLibraryError.EMB_DATAOVER_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
          arguments: ErrEmbTooMuchSelectedGoMenuArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
    } else if (error == EmbLibraryError.EMB_TOOCOMPLEX_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_THIS_PATTERN_TOO_COMPLEX,
          arguments: ErrThisPatternTooComplexArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
      return;
    }

    Log.errorTrace('initMakingEasyStipple Unknown Error!!!');
  }

  ///
  /// Stipple画面に遷移する
  ///
  void _goToStipple(BuildContext context) {
    /// Appliqueは現在選択されている模様を変更するため、今の模様のバックアップを作成します
    StippleModel().selectedPatternsBackUp =
        PatternModel().getAllSelectedPattern();

    /// Stippleに入る
    PopupNavigator.pushNamed(
        context: context, nextRouteName: PopupEnum.stippling);
  }

  ///
  /// 他の画面を更新する
  ///
  void _updateOtherWidget() {
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);
  }

  ///
  /// データ更新ページをリッスンする
  ///
  ProviderSubscription? _listener;

  ///
  /// 状態をリッスンします
  ///
  void _listenStatus() {
    _listener = ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.sizeState),
      (previous, nextState) {
        state = state.copyWith(
          isSizeButtonEnable: _isSizeButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.moveState),
      (previous, nextState) {
        state = state.copyWith(
          isMoveButtonEnable: _isMoveButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.groupState),
      (previous, nextState) {
        update();
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.group),
      (previous, nextState) {
        update();
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.rotateState),
      (previous, nextState) {
        state = state.copyWith(
          isRotateButtonEnable: _isRotateButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.editFlipState),
      (previous, nextState) {
        update();
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.editFlip),
      (previous, nextState) {
        update();
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.copyState),
      (previous, nextState) {
        state = state.copyWith(
          isCopyButtonEnable: _isCopyButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.densityState),
      (previous, nextState) {
        state = state.copyWith(
          isDensityButtonEnable: _isDensityButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.borderState),
      (previous, nextState) {
        state = state.copyWith(
          isBorderButtonEnable: _isBorderButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.appliqueState),
      (previous, nextState) {
        state = state.copyWith(
          isAppliqueButtonEnable: _isAppliqueButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.changeColorState),
      (previous, nextState) {
        state = state.copyWith(
          isColorChangeButtonEnable: _isColorChangeButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.textEditState),
      (previous, nextState) {
        state = state.copyWith(
          isTextEditButtonEnable: _isTextEditButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.alignmentState),
      (previous, nextState) {
        state = state.copyWith(
          isAlignmentButtonEnable: _isAlignmentButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.stipplingState),
      (previous, nextState) {
        state = state.copyWith(
          isStipplingButtonEnable: _isStipplingButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.outlineState),
      (previous, nextState) {
        state = state.copyWith(
          isOutlineButtonEnable: _isOutlineButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.orderState),
      (previous, nextState) {
        state = state.copyWith(
          isOrderButtonEnable: _isOrderButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.notSewingState),
      (previous, nextState) {
        state = state.copyWith(
          isNotSewingEnable: _isNotSewingEnable(nextState),
        );
      },
    );
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// ボタン表示用の判定関数群
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// サイズボタンの状態を取得する
  ///
  bool _isSizeButtonEnable(int sizeState) {
    if (AppInfoFuncState.getValueByNumber(sizeState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 移動ボタンの状態を取得する
  ///
  bool _isMoveButtonEnable(int moveState) {
    if (AppInfoFuncState.getValueByNumber(moveState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// グループボタンの状態を取得する
  ///
  ButtonState _getGroupButtonState(int groupState, bool isGroup) {
    if (AppInfoFuncState.getValueByNumber(groupState) ==
        AppInfoFuncState.disable) {
      return ButtonState.disable;
    }
    if (isGroup) {
      return ButtonState.select;
    } else {
      return ButtonState.normal;
    }
  }

  ///
  /// 回転ボタンの状態を取得する
  ///
  bool _isRotateButtonEnable(int rotateState) {
    if (AppInfoFuncState.getValueByNumber(rotateState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 反転ボタンの状態を取得する
  ///
  ButtonState _getFlipButtonState(int editFlipState, bool isFlip) {
    if (AppInfoFuncState.getValueByNumber(editFlipState) ==
        AppInfoFuncState.disable) {
      return ButtonState.disable;
    }
    if (isFlip) {
      return ButtonState.select;
    } else {
      return ButtonState.normal;
    }
  }

  ///
  /// コピーボタンの状態を取得する
  ///
  bool _isCopyButtonEnable(int copyState) {
    if (AppInfoFuncState.getValueByNumber(copyState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 密度ボタンの状態を取得する
  ///
  bool _isDensityButtonEnable(int densityState) {
    if (AppInfoFuncState.getValueByNumber(densityState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// ボーダーボタンの状態を取得する
  ///
  bool _isBorderButtonEnable(int borderState) {
    if (AppInfoFuncState.getValueByNumber(borderState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// アプリッケボタンの状態を取得する
  ///
  bool _isAppliqueButtonEnable(int appliqueState) {
    if (AppInfoFuncState.getValueByNumber(appliqueState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 色変えボタンの状態を取得する
  ///
  bool _isColorChangeButtonEnable(int changeColorState) {
    if (AppInfoFuncState.getValueByNumber(changeColorState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// テキスト編集ボタンの状態を取得する
  ///
  bool _isTextEditButtonEnable(int textEditState) {
    if (AppInfoFuncState.getValueByNumber(textEditState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// アライメントボタンの状態を取得する
  ///
  bool _isAlignmentButtonEnable(int alignmentState) {
    if (AppInfoFuncState.getValueByNumber(alignmentState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 簡単ステップリングボタンの状態を取得する
  ///
  bool _isStipplingButtonEnable(int stipplingState) {
    if (AppInfoFuncState.getValueByNumber(stipplingState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// アウトラインボタンの状態を取得する
  ///
  bool _isOutlineButtonEnable(int outlineState) {
    if (AppInfoFuncState.getValueByNumber(outlineState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 入れ替えボタンの状態を取得する
  ///
  bool _isOrderButtonEnable(int orderState) {
    if (AppInfoFuncState.getValueByNumber(orderState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 縫わないボタンの状態を取得する
  ///
  bool _isNotSewingEnable(int notSewingState) {
    if (AppInfoFuncState.getValueByNumber(notSewingState) ==
        AppInfoFuncState.disable) {
      return false;
    } else {
      return true;
    }
  }
}
