import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../model/const_def_model.dart';
import '../../../../model/setting_model.dart';
import '../../../../network/server/http/route/my_stitch_monitoring/common.dart';
import 'key_board_font_model.dart' show ArrayTypes;
import 'sewing_model.dart';
import 'thread_color_list_model.dart';

/// 読み出したSizeデータの変換倍率   '読み出したデータの単位(0.1mm) / 10 = mm'
const int conversionRate = 10;

/// 検索異常
const int notFound = -1;

/// 表示倍率値リスト(Zoomの使用)
const List<int> zoomList = [100, 125, 150, 200, 400];

/// 無効な糸色コード
const int invalidThreadCode = 0xFFFF;

const int color61IndexMax = 61;

const int colorCodeNoDefine = 65;

///
/// パターン選択画面表示情報
///
class PatternModel {
  PatternModel._internal();
  factory PatternModel() {
    if (_instance._isInitialized == false) {
      _instance._isInitialized = true;
    }

    return _instance;
  }

  Color getThumbnailBackgroundColor() => settingColorsTable[
      DeviceLibrary().apiBinding.getThumbnailBackgroundColor().value];

  bool _isInitialized = false;
  static final PatternModel _instance = PatternModel._internal();

  /// 現在模様編集画面（及び編集画面中の画面）の表示倍率
  /// 100〜400(%)
  int selectedZoomScaleInEditPage = zoomList.first;

  /// 現在模様選択画面（及び選択画面中の画面）の表示倍率
  /// 100〜400(%)
  int selectedZoomScaleInSelectPage = zoomList.first;

  /// Real Previewの表示モード
  RealPreviewDisplayType realPreviewDisplayType =
      RealPreviewDisplayType.patternSelect;

  ///
  /// 刺繍しつけ距離のmm/inch単位変換
  ///
  String changeValueToDisplay(int value) => isUnitMm
      ? (value / conversionRate).toStringAsFixed(1).padLeft(5, ' ')
      : (value * SettingModel().inchRate / conversionRate)
          .toStringAsFixed(2)
          .padLeft(5, ' ');

  ///
  /// 大型つなぎ模様のmm/inch単位変換
  ///
  String largeConnectChangeValueToDisplay(int value) => isUnitMm
      ? value.toStringAsFixed(0)
      : (value * SettingModel().inchRate).toStringAsFixed(2);

  ///
  /// Libが取得した縫製時間をUiが使用する時間に変換する
  ///
  int changeLibSewingTimeToUiSewingTime(int libSewingTime) {
    /// ミリ秒から秒への倍率
    const msToSRate = 1000;

    /// 秒から分への倍率
    const sToMRate = 60;

    libSewingTime = libSewingTime ~/ msToSRate;
    if (libSewingTime < sToMRate) {
      libSewingTime = sewingTimeDisplayLimitMin;
    } else {
      libSewingTime = (libSewingTime / sToMRate).round();
    }

    return libSewingTime.clamp(
        sewingTimeDisplayLimitMin, sewingTimeDisplayLimitMax);
  }

  int sewingTimeRound(int msec) {
    /// ミリ秒から秒への倍率
    const msToSRate = 1000;

    /// 秒から分への倍率
    const sToMRate = 60;

    int min = msec ~/ (msToSRate * sToMRate);
    int sec = msec % (msToSRate * sToMRate);

    if (msec == 0) {
      return 0;
    }
    if (min == 0) {
      /*	０分はダメ			*/
      return sewingTimeDisplayLimitMin;
    } else if (min >= sewingTimeDisplayLimitMax) {
      /*	分 ３桁までに制限	*/
      return sewingTimeDisplayLimitMax;
    } else {
      if (sec < 30 * 1000) {
        /*	３０秒から切り上げ	*/
        return min;
      } else {
        return min + 1;
      }
    }
  }

  /////////////////////////////////////////////////////////////////////////////
  ///
  /// _patternListの使用
  ///
  /////////////////////////////////////////////////////////////////////////////

  ///
  /// UIストレージのpatternメンバー
  ///
  /// BorderもGroupもPatternに属しています
  /// Borderには複数のGroupがあります
  ///
  ///   -pattern(Group)
  ///   -pattern(Border)
  ///             -|pattern(Group)
  ///             -|pattern(Group)
  ///   -pattern(Group)
  ///
  final List<Pattern> _patternList = [];

  ///
  /// しつけ糸追加
  ///
  EmbLibraryError addBasting() {
    var (errorCode: error, handle: _) = EmbLibrary().apiBinding.addBasting();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    reloadAllPattern();

    /// MyStitchMonitor App接続の場合
    /// 返信情報更新する
    updateMonitoringEmbInfoWithColor();

    return error;
  }

  ///
  /// patternを削除
  ///
  EmbLibraryError deleteEmb() {
    EmbLibraryError error = EmbLibrary().apiBinding.deleteEmb();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    /// ここではファサードの再読み込みはなく、既存のデータを並べ替えるだけです
    loadNewPattern(markToReloadPatterns: []);
    return error;
  }

  ///
  /// しつけ糸削除
  ///
  EmbLibraryError deleteBasting() {
    EmbLibraryError error = EmbLibrary().apiBinding.deleteBasting();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    reloadAllPattern();

    /// MyStitchMonitor App接続の場合
    /// 返信情報更新する
    updateMonitoringEmbInfoWithColor();

    return error;
  }

  ///
  /// すべてのpatternを削除
  ///
  void deleteEmbAll() {
    EmbLibrary().apiBinding.enterMultiSelectMode();
    changeAllSelectStateInMultiplePage(true);
    EmbLibrary().apiBinding.exitMultiSelectMode();
    deleteEmb();
  }

  ///
  /// 新しいパターン情報を読み取る
  /// markToReloadPatterns : 強制的に再ロードする必要がある模様。
  /// 　　　　              元の模様に基づいて新しい模様を取得する場合は、元の模様を指定して強制的に更新します。更新された模様が更新されないの問題を守る。
  /// 　　　　              例えば：Applique、新しいハンドルは生成されず、新しいデータはロードされませんの問題があります。
  ///
  void loadNewPattern({required List<Pattern> markToReloadPatterns}) {
    /// データのクリーンアップ
    ThreadColorListModel().selectedGroupIndex = null;

    /// 縫製画面における全模様の糸色情報をクリア
    SewingModel().clearEmbSewingThreadList();

    var (errorCode: _, handleList: groupHandles) =
        EmbLibrary().apiBinding.getGroupHandleAll();

    /// groupHandleは存在しない、直接終了します
    if (groupHandles.isEmpty) {
      _patternList.clear();
      return;
    }

    final List<Pattern> newPatternList = [];

    /// 未編集の模様
    Map<MemHandle, Pattern> notEditPatterns = {};

    /// 今回完成したグループハンドル
    List<MemHandle> addedHandles = [];

    /// 変更が必要なハンドル
    final Map<MemHandle, Pattern> markToReloadPatternsMap = markToReloadPatterns
        .asMap()
        .map((key, value) => MapEntry<MemHandle, Pattern>(value.handle, value));

    final MemHandle currentGroupHandle = getCurrentGroupHandle();

    /// 未編集のパターンを整理します。
    /// 編集中の模様は、その後　必ず再生成する。
    for (var pattern in _patternList) {
      /// パターンが選択状態の場合、未編集パターンとは見なしません。
      if (pattern is EmbBorder &&
          (pattern.groupList.first.embGroupInfo.embGrp.selected == true ||
              pattern.isContainCurrentPattern(currentGroupHandle))) {
        continue;
      } else if (pattern is EmbGroup &&
          (pattern.embGroupInfo.embGrp.selected == true ||
              pattern.isCurrentGroup(currentGroupHandle))) {
        continue;
      } else if (markToReloadPatternsMap.containsKey(pattern.handle)) {
        /// 変更必要の模様、必ず変更します、未編集のパターンではない
        continue;
      } else {
        /// 現在編集されていないパターンを追加します。
        final planets = <MemHandle, Pattern>{pattern.handle: pattern};
        notEditPatterns.addEntries(planets.entries);
      }
    }

    for (int index = 0; index < groupHandles.length; index++) {
      int groupHandle = groupHandles[index];
      if (groupHandle == currentGroupHandle) {
        ThreadColorListModel().selectedGroupIndex = index;
      } else {
        /// Do nothing
      }

      /// 既に生成されている場合は、この handle をスキップします。
      if (addedHandles.contains(groupHandle)) {
        continue;
      }

      /// Borderかどうか判断
      final (errorCode: error, handle: borderHandle) =
          EmbLibrary().apiBinding.getBorderHandleIncludeGroup(groupHandle);

      /// Borderを追加
      if (error == EmbLibraryError.EMB_NO_ERR) {
        final EmbBorder embBorder;

        /// 既存のデータを直接追加
        if (notEditPatterns.containsKey(borderHandle) == true) {
          final Pattern existingPattern = notEditPatterns[borderHandle]!;
          embBorder = existingPattern is EmbBorder
              ? existingPattern
              : EmbBorder(borderHandle);

          /// 新しいデータを追加
        } else {
          embBorder = EmbBorder(borderHandle);
        }

        /// ハンドルを記録します
        newPatternList.add(embBorder);
        for (var group in embBorder.groupList) {
          addedHandles.add(group.handle);
        }

        /// Groupを追加
      } else {
        final EmbGroup embGroup;

        /// 既存のデータを直接追加
        if (notEditPatterns.containsKey(groupHandle) == true) {
          final Pattern existingPattern = notEditPatterns[groupHandle]!;
          embGroup = existingPattern is EmbGroup
              ? existingPattern
              : EmbGroup(groupHandle);

          /// 新しいデータを追加
        } else {
          embGroup = EmbGroup(groupHandle);
        }

        /// ハンドルを記録します
        newPatternList.add(embGroup);
        addedHandles.add(groupHandle);
      }
    }

    /// 古いデータをクリアし、新しいデータを追加する
    _patternList
      ..clear()
      ..addAll(newPatternList);
  }

  ///
  /// すべての模様を再読み込みします
  ///
  void reloadAllPattern() {
    /// データのクリーンアップ
    ThreadColorListModel().selectedGroupIndex = null;
    if (_patternList.isNotEmpty) {
      _patternList.clear();
    } else {
      /// Do nothing
    }

    /// 縫製画面における全模様の糸色情報をクリア
    SewingModel().clearEmbSewingThreadList();

    var (errorCode: _, handleList: groupHandles) =
        EmbLibrary().apiBinding.getGroupHandleAll();

    /// groupHandleは存在しない、直接終了します
    if (groupHandles.isEmpty) {
      return;
    }

    /// 今回完成したグループハンドル
    List<MemHandle> addedHandles = [];

    int currentGroupHandle = getCurrentGroupHandle();
    for (int index = 0; index < groupHandles.length; index++) {
      int groupHandle = groupHandles[index];
      if (groupHandle == currentGroupHandle) {
        ThreadColorListModel().selectedGroupIndex = index;
      } else {
        /// Do nothing
      }

      /// 既に生成されている場合は、この handle をスキップします。
      if (addedHandles.contains(groupHandle)) {
        continue;
      }

      /// Borderかどうか判断
      final (errorCode: error, handle: borderHandle) =
          EmbLibrary().apiBinding.getBorderHandleIncludeGroup(groupHandle);

      /// Borderを追加
      if (error == EmbLibraryError.EMB_NO_ERR) {
        EmbBorder embBorder = EmbBorder(borderHandle);
        _patternList.add(embBorder);

        /// Border 内のすべてのグループハンドルを記録します
        for (var group in embBorder.groupList) {
          addedHandles.add(group.handle);
        }

        /// Groupを追加
      } else {
        _patternList.add(EmbGroup(groupHandle));

        /// グループハンドルを記録します
        addedHandles.add(groupHandle);
      }
    }
  }

  ///
  /// 例えば：ColorList機能
  /// 現在選択されているGroupを、入力されたGroupIndexに従って変更します
  /// 現在選択されているGroupが変更されると、現在選択されているPatternも変更されます
  ///
  /// [groupPatternIndex] : 選択したいgroupのIndex
  ///
  void selectPatternWithGroupIndex(int groupPatternIndex) {
    List<EmbGroup> groupList = getAllGroup();
    groupList[groupPatternIndex].selectEmbToEdit();
  }

  ///
  /// 例えば：Order機能
  /// 現在選択されているGroupPatternを、入力されたPatternIndexに従って変更します
  ///
  void selectPatternWithSewingIndex(int patternIndex) {
    Pattern pattern = _patternList[patternIndex];

    if (pattern is EmbBorder) {
      pattern.groupList.first.selectEmbToEdit();
      return;
    }

    if (pattern is EmbGroup) {
      pattern.selectEmbToEdit();
      return;
    }
  }

  ///
  /// Edit-> 模様の複数選択機能の使用
  /// 指定したpatternを選択状態に設定する
  /// 現在のGroupを変更しない
  ///
  void selectPatternInMultiplePage(int patternIndex) {
    Pattern pattern = _patternList[patternIndex];

    if (pattern is EmbBorder) {
      bool isSelect = true;
      if (pattern.groupList.first.embGroupInfo.embGrp.selected) {
        isSelect = false;
      } else {
        isSelect = true;
      }
      pattern.selectMultiEmb(isSelect);
      return;
    }

    if (pattern is EmbGroup) {
      bool isSelect = true;
      if (pattern.embGroupInfo.embGrp.selected) {
        isSelect = false;
      } else {
        isSelect = true;
      }
      pattern.selectMultiEmb(isSelect);
      return;
    }
  }

  ///
  /// Edit-> 模様の複数選択機能の使用
  /// 全てのpatternを選択状態に設定する
  /// 現在のGroupを変更しない
  ///
  void changeAllSelectStateInMultiplePage(bool isSelect) {
    for (var pattern in _patternList) {
      if (pattern is EmbBorder) {
        pattern.selectMultiEmb(isSelect);
      } else if (pattern is EmbGroup) {
        pattern.selectMultiEmb(isSelect);
      } else {
        /// DO Nothing
      }
    }
  }

  ///
  /// 現在のグループを選択状態に設定
  /// すべてのPatternの選択状態を解除し、現在のグループを選択状態に設定します
  ///
  void notPatternAreaSelectWork() {
    EmbLibrary().apiBinding.notPatternAreaSelectWork();
    clearAllSelectedGroupInfoCache();
  }

  ///
  /// すべてのPatternを取得
  ///
  List<Pattern> getAllPattern() => _patternList;

  ///
  /// 選択した状態のすべてのPatternを取得
  ///
  List<Pattern> getAllSelectedPattern({bool onlyCheckGroupSelected = false}) {
    List<Pattern> patternList = [];

    for (var pattern in _patternList) {
      if (pattern is EmbGroup && pattern.embGroupInfo.embGrp.selected) {
        patternList.add(pattern);
      } else if (pattern is EmbBorder &&
          pattern.groupList.first.embGroupInfo.embGrp.selected) {
        patternList.add(pattern);
      } else {
        /// DO nothing
      }
    }

    /// 複数選択状態でない場合は、現在選択されている模様を戻ります
    if (onlyCheckGroupSelected == false && patternList.isEmpty) {
      patternList.add(getCurrentPattern());
    } else {
      /// Do nothing
    }

    return patternList;
  }

  ///
  /// すべてのグループを取得
  /// Border内部に存在するGroupを含む
  ///
  List<EmbGroup> getAllGroup() {
    List<EmbGroup> groupList = [];

    for (var pattern in _patternList) {
      if (pattern is EmbBorder) {
        groupList.addAll(pattern.groupList);
      } else if (pattern is EmbGroup) {
        groupList.add(pattern);
      } else {
        /// Do nothing
      }
    }

    return groupList;
  }

  ///
  /// すべてのBorderを取得
  ///
  List<EmbBorder> getAllBorder() {
    List<EmbBorder> borderList = [];

    for (var pattern in _patternList) {
      if (pattern is EmbBorder) {
        borderList.add(pattern);
      } else {
        /// Do nothing
      }
    }

    return borderList;
  }

  ///
  /// カレントグループのハンドルを返す
  ///
  MemHandle getCurrentGroupHandle() {
    var (errorCode: error, handle: groupHandle) =
        EmbLibrary().apiBinding.getCurrentGroupHandle();

    assert(
        error == EmbLibraryError.EMB_NO_ERR, 'current Handleの取得中にエラーが発生しました');

    return groupHandle;
  }

  ///
  /// 現在選択されているグループを取得
  ///
  EmbGroup getCurrentGroup({MemHandle? curGroupHandle}) {
    final MemHandle currentGroupHandle =
        curGroupHandle ?? getCurrentGroupHandle();

    for (int index = 0; index < _patternList.length; index++) {
      Pattern pattern = _patternList[index];

      if (pattern is EmbBorder) {
        for (var group in pattern.groupList) {
          if (group.handle == currentGroupHandle) {
            return group;
          }
        }
      } else if (pattern is EmbGroup) {
        if (pattern.handle == currentGroupHandle) {
          return pattern;
        }
      } else {
        /// Do Nothing
      }
    }

    Log.errorTrace('_patternListの中にCurrentGroupがない');
    return EmbGroup(currentGroupHandle);
  }

  ///
  /// 現在選択されているPatternを取得
  ///
  Pattern getCurrentPattern({MemHandle? curGroupHandle}) {
    return _patternList[
        getCurrentGroupSewingIndex(curGroupHandle: curGroupHandle)];
  }

  ///
  /// 現在選択されているグループのリストを取得する
  ///
  /// currentGroupを探し、
  /// currentGroupがGroupに対応する場合はGroupを返し、
  /// currentGroupがBorderに対応する場合はBorder内のすべてのGroupを返す
  ///
  List<EmbGroup> getAllGroupInCurrentPattern() {
    Pattern currentPattern = getCurrentPattern();

    if (currentPattern is EmbBorder) {
      return currentPattern.groupList;
    }

    if (currentPattern is EmbGroup) {
      return [currentPattern];
    }
    return [];
  }

  ///
  /// _patternListにおける現在のグループのIndexを取得する
  ///
  int getCurrentGroupSewingIndex({MemHandle? curGroupHandle}) {
    final MemHandle currentGroupHandle =
        curGroupHandle ?? getCurrentGroupHandle();

    for (int index = 0; index < _patternList.length; index++) {
      Pattern pattern = _patternList[index];

      if (pattern is EmbBorder) {
        for (var group in pattern.groupList) {
          if (group.handle == currentGroupHandle) {
            return index;
          }
        }
      } else if (pattern is EmbGroup) {
        if (pattern.handle == currentGroupHandle) {
          return index;
        }
      } else {
        /// Do Nothing
      }
    }

    Log.errorTrace('_patternListの中にCurrentGroupがない');
    return 0;
  }

  ///
  /// 現在登録されているパターンの縫製情報を取得する
  ///
  /// [isAllPattern] : 要約された情報を取得するかどうか </br>
  ///                : true(登録されている模様の縫製情報) </br>
  ///                : false(現在選択されているグループの縫製情報)
  ///
  /// [isNeedTemporaryGroup]  : 一時的パターンデータが必要かどうか </br>
  ///                         : true(データの計算時に TemporaryGroup データを含める) </br>
  ///                         : false(データの計算時に TemporaryGroup データは含まれません)
  ///
  /// [isNeedAllPatternSize]  : パターン全体のサイズが必要ですか </br>
  ///                         : true(PatternDispInfoのallPatternWidthとallPatternHeightの値を取得して返します) </br>
  ///                         : false(PatternDispInfoのallPatternWidthとallPatternHeightの値は0です)
  ///
  PatternDispInfo getPatternInfo({
    required bool isAllPattern,
    bool isNeedTemporaryGroup = false,
    bool isNeedAllPatternSize = false,
  }) {
    int totalStitchNum = 0;
    int totalThreadNum = 0;
    int totalSewingTime = 0;

    List<EmbGroup> groupList = [];
    List<ThreadInfo> colorList = [];
    EmbGroup? currentGroup;

    /// 統計データ用のグループ・リストの構築
    if (isAllPattern == true) {
      groupList = getAllGroup();
    } else {
      if (_patternList.isNotEmpty) {
        currentGroup = getCurrentGroup();
        groupList = [currentGroup];
      } else {
        // do nothing
      }
    }

    if (isNeedTemporaryGroup == true && temporaryGroupList.isNotEmpty) {
      groupList.addAll(temporaryGroupList);
    } else {
      /// Do noting
    }

    if (groupList.isEmpty) {
      return const PatternDispInfo(
        right: 0,
        left: 0,
        bottom: 0,
        top: 0,
        stitchNum: 0,
        threadNum: 0,
        sewingTime: 0,
        colorNum: 0,
        allPatternWidth: 0,
        allPatternHeight: 0,
      );
    }
    RectanArea area = RectanArea.maxArea;

    /// パターン全体のサイズを取得する場合、currentGroupをnullにすることはできません。
    if (isNeedAllPatternSize == true && currentGroup == null) {
      currentGroup = getCurrentGroup();
    } else {
      /// do nothing
    }

    /// 全体サイズ幅と高さ
    int allPatternWidth = 0;
    int allPatternHeight = 0;

    /// 統計データ
    for (var group in groupList) {
      List<ThreadInfo> threadInfoList = group.threadInfo;

      /// 累加針数、糸数、裁縫は時々
      for (var threadInfo in threadInfoList) {
        if (threadInfo.notSewing == true) {
          continue;
        }
        totalThreadNum++;
        totalStitchNum += threadInfo.stitchNumber;
        totalSewingTime += threadInfo.sewingTime;

        /// 同じ色が存在するかどうかを確認します
        bool hasColor = colorList.any((element) =>
            element.colorRGB == threadInfo.colorRGB &&
            element.brandCode == threadInfo.brandCode);
        if (hasColor == false) {
          colorList.add(threadInfo);
        } else {
          /// do nothing
        }
      }

      /// まとめ縫いに必要な最大スペース
      area = getEmbGrpInfoMaxSewingArea(
        embGrp: group.embGroupInfo.embGrp,
        embPatternInfo: group.embGroupInfo.embPatternInfo,
        embInfo: group.embGroupInfo.embInfo,
        beforeArea: area,
      );

      /// 全体サイズ幅と高さを取得
      if (isNeedAllPatternSize == true &&
          currentGroup != null &&
          group.isCurrentGroup(currentGroup.handle)) {
        allPatternWidth = group.embGroupInfo.embInfo.wide;
        allPatternHeight = group.embGroupInfo.embInfo.height;
      } else {
        /// do nothing
      }
    }

    return PatternDispInfo(
      allPatternWidth: allPatternWidth,
      allPatternHeight: allPatternHeight,
      right: area.right,
      left: area.left,
      bottom: area.bottom,
      top: area.top,
      stitchNum: totalStitchNum,
      threadNum: totalThreadNum,
      sewingTime: changeLibSewingTimeToUiSewingTime(totalSewingTime),
      colorNum: colorList.length,
    );
  }

  ///
  /// 現在の選択状態の取得
  ///
  /// out [SelectMode] : 現在の選択状態 | 単一選択状態
  ///                                  | 単一選択組み合わせ状態
  ///                                  | 複数選択状態
  ///                                  | 複数選択状態(組合せを含む)
  ///
  SelectMode getSelectMode() {
    SelectMode selectMode = SelectMode.group;
    EmbGroup currentGroup = getCurrentGroup();
    int? borderHandle = currentGroup.getBorderHandleIncludeGroup();
    if (borderHandle != null) {
      selectMode = SelectMode.border;
    } else {
      selectMode = SelectMode.group;
    }

    /// 複数選択状態の判断
    if (getAllSelectedPattern().length > 1) {
      if (selectMode == SelectMode.border) {
        selectMode = SelectMode.multipleContainBorder;
      } else {
        selectMode = SelectMode.multiple;
      }
    } else {
      /// Do Nothing
    }

    return selectMode;
  }

  ///
  /// 複数のembが選択されているかどうかを判断する
  ///
  bool get isSelectedMultiEmb => [
        SelectMode.multiple,
        SelectMode.multipleContainBorder,
      ].contains(getSelectMode());

  ///
  /// 現在選択されているBorderを含むかどうか
  ///
  bool get isSelectedBorder => [
        SelectMode.border,
        SelectMode.multipleContainBorder,
      ].contains(getSelectMode());

  ///
  /// カウチング模様 ログインしているかどうか
  ///
  bool isLoginCouching() {
    if (_patternList.isEmpty) {
      return false;
    }

    if (getCurrentGroup().checkEmbPatternType(EmbPatternType.couting)) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 内蔵ロングスティッチ模様模様 ログインしているかどうか
  ///
  bool isLoginLongStitch() {
    if (_patternList.isEmpty) {
      return false;
    }

    if (getCurrentGroup().checkEmbPatternType(EmbPatternType.longStitch)) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// しつけがログインしているかどうかを判断する
  ///
  bool isLoginBasting() {
    bool isBastingLogin = false;

    List<Pattern> patternList = getAllPattern();
    for (var pattern in patternList) {
      if (pattern is EmbGroup) {
        if (pattern.checkEmbPatternType(EmbPatternType.basting)) {
          isBastingLogin = true;
          break;
        } else {
          /// Do Noting
        }
      } else {
        /// Do Noting
      }
    }
    return isBastingLogin;
  }

  /////////////////////////////////////////////////////////////////////////////
  ///
  /// APIの使用
  ///
  /////////////////////////////////////////////////////////////////////////////

  ///
  /// ミリかどうか
  ///
  bool get isUnitMm =>
      DeviceLibrary().apiBinding.getDisplayUnit().displayUnit == DisplayUnit.mm;

  ///
  /// 英語かどうか
  ///
  bool get isEnglish =>
      DeviceLibrary().apiBinding.getLanguage().value == Language.LANG_ENGLISH;

  ///
  /// 設定アプリの設定に基づいてサムネイルのサイズを取得します
  ///
  ThumbnailSizeType get thumbnailSize =>
      DeviceLibrary().apiBinding.getThumbnailSize().value;

  ///
  /// 設定アプリ中のサムネイルのサイズを設定します
  ///
  void saveThumbnailSize(ThumbnailSizeType value) =>
      DeviceLibrary().apiBinding.saveThumbnailSize(value);

  ///
  /// 一時記憶な模様
  ///
  List<Pattern> _temporaryPatternList = [];
  List<Pattern> get temporaryPatternList => _temporaryPatternList;
  List<EmbGroup> get temporaryGroupList {
    List<EmbGroup> groupList = [];

    for (var temporaryPattern in _temporaryPatternList) {
      if (temporaryPattern is EmbBorder) {
        groupList.addAll(temporaryPattern.groupList);
      } else if (temporaryPattern is EmbGroup) {
        groupList.add(temporaryPattern);
      } else {
        /// Do nothing
      }
    }

    return groupList;
  }

  ///
  /// 一時的に追加されたデータのクリーンアップ
  ///
  void clearTemporaryPatternList() {
    _temporaryPatternList.clear();
    _temporaryPatternList = [];
  }

  ///
  /// 一時的なデータを追加する
  ///
  void addTemporaryGroupList(List<MemHandle> groupHandles) {
    /// 一時データのクリーンアップ
    if (_temporaryPatternList.isNotEmpty) {
      clearTemporaryPatternList();
    } else {
      /// Do nothing
    }

    /// 今回完成したグループハンドル
    List<MemHandle> addedHandles = [];

    for (var groupHandle in groupHandles) {
      /// 既に生成されている場合は、この handle をスキップします。
      if (addedHandles.contains(groupHandle)) {
        continue;
      }

      EmbGroup embGroup = EmbGroup(groupHandle);
      MemHandle? borderHandle = embGroup.getBorderHandleIncludeGroup();

      if (borderHandle != null) {
        EmbBorder embBorder = EmbBorder(borderHandle);
        _temporaryPatternList.add(embBorder);

        /// Border 内のすべてのグループハンドルを記録します
        for (var group in embBorder.groupList) {
          addedHandles.add(group.handle);
        }
      } else {
        _temporaryPatternList.add(embGroup);

        /// グループハンドルを記録します
        addedHandles.add(groupHandle);
      }
    }
  }

  ///
  /// 一時データのクリーンアップ
  ///
  void deleteTemporaryPatternList() {
    if (_temporaryPatternList.isEmpty) {
      return;
    }

    /// すべてのログインのスタイルを削除します。
    for (var temporaryPattern in _temporaryPatternList) {
      if (temporaryPattern is EmbBorder) {
        temporaryPattern.selectEmbToEdit();
        EmbLibrary().apiBinding.deleteEmb();
      } else if (temporaryPattern is EmbGroup) {
        temporaryPattern.selectEmbToEdit();
        EmbLibrary().apiBinding.deleteEmb();
      } else {
        /// Do noting
      }
    }

    clearTemporaryPatternList();
  }

  ///
  /// 模様選択
  ///
  EmbLibraryError selectEmb(int embIndex, int category,
      {EmbBHSizeKind bhSize = EmbBHSizeKind.bhSizeXs}) {
    /// 前の選択の模様を削除します
    deleteTemporaryPatternList();

    /// 新しい模様を追加する
    var (errorCode: error, handle: groupHandle) =
        EmbLibrary().apiBinding.selectEmb(embIndex, category, bhSize);

    if (error == EmbLibraryError.EMB_NO_ERR) {
      addTemporaryGroupList([groupHandle]);
    } else {
      clearTemporaryPatternList();
    }

    return error;
  }

  ///
  /// 複数模様のグループ化
  ///
  EmbLibraryError groupPatterns() {
    var (errorCode: error, borderHandle: _) =
        EmbLibrary().apiBinding.groupingMultiplePatterns();

    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    loadNewPattern(markToReloadPatterns: []);
    return error;
  }

  ///
  /// グループ化された模様のグループ化解除
  ///
  EmbLibraryError unGroupPatterns() {
    EmbLibraryError error = EmbLibrary()
        .apiBinding
        .unGroupingGroupedPatterns(getCurrentPattern().handle);
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    loadNewPattern(markToReloadPatterns: []);
    return error;
  }

  ///
  /// EmbBorderのEmbGroupの数が更新されます
  /// リピート機能
  ///
  void updateCurrentGroup() {
    Pattern pattern = getCurrentPattern();
    if (pattern is EmbBorder) {
      pattern
        ..updateGroupList()
        ..clearBorderCompInfoCache()
        ..clearBorderInfoCache()
        ..clearGroupInfoCache();
      return;
    } else {
      /// Do noting
    }
  }

  ///
  /// 模様の移動
  ///
  int moveTotalLibX = 0;
  int moveTotalLibY = 0;

  ///
  /// 模様移動(増量移動)
  ///
  /// - [dirX] dirX: 横方向移動量(dirX = 0.5mm * dirX)
  /// - [dirY] dirY: 縦方向移動量(dirY = 0.5mm * dirY)
  ///
  EmbLibraryError moveEmb(int dirX, int dirY) {
    EmbLibraryError error = EmbLibrary().apiBinding.moveEmb(dirX, dirY);
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    /// GRPInfoを変更し、キャッシュされたデータを消去し、再取得します
    getAllSelectedPattern().forEach((pattern) {
      if (pattern is EmbBorder) {
        pattern
          ..clearGroupInfoCache()
          ..clearBorderCompInfoCache()
          ..clearBorderInfoCache();
      } else if (pattern is EmbGroup) {
        pattern.clearGroupInfoCache();
      } else {
        /// Do nothing
      }
    });
    return error;
  }

  ///
  /// 模様中央に移動させる
  ///
  EmbLibraryError moveEmbCenter() {
    EmbLibraryError error = EmbLibrary().apiBinding.moveEmbCenter();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    /// GRPInfoを変更し、キャッシュされたデータを消去し、再取得します
    getAllSelectedPattern().forEach((pattern) {
      if (pattern is EmbBorder) {
        pattern
          ..clearGroupInfoCache()
          ..clearBorderCompInfoCache()
          ..clearBorderInfoCache();
      } else if (pattern is EmbGroup) {
        pattern.clearGroupInfoCache();
      } else {
        /// Do nothing
      }
    });
    return error;
  }

  ///
  /// 模様を回転させる
  ///
  EmbLibraryError rotateEmb(int degree) {
    EmbLibraryError error = EmbLibrary().apiBinding.rotateEmb(degree);
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    /// GRPInfoを変更し、キャッシュされたデータを消去し、再取得します
    Pattern currentPattern = getCurrentPattern();
    if (currentPattern is EmbBorder) {
      currentPattern
        ..clearGroupInfoCache()
        ..clearBorderCompInfoCache()
        ..clearBorderInfoCache()
        ..clearGroupImageCache();
    } else if (currentPattern is EmbGroup) {
      currentPattern
        ..clearGroupInfoCache()
        ..clearMainImageCache();
    } else {
      /// Do nothing
    }

    return error;
  }

  ///
  /// 模様を回転させる
  ///
  EmbLibraryError resetRotateEmb() {
    EmbLibraryError error = EmbLibrary().apiBinding.resetEmbRotate();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    /// GRPInfoを変更し、キャッシュされたデータを消去し、再取得します
    Pattern currentPattern = getCurrentPattern();
    if (currentPattern is EmbBorder) {
      currentPattern
        ..clearGroupInfoCache()
        ..clearBorderCompInfoCache()
        ..clearBorderInfoCache()
        ..clearGroupImageCache();
    } else if (currentPattern is EmbGroup) {
      currentPattern
        ..clearGroupInfoCache()
        ..clearMainImageCache();
    } else {
      /// Do nothing
    }

    return EmbLibraryError.EMB_NO_ERR;
  }

  ///
  /// 自動模様つなぎ取得
  ///
  static bool getEmbMarkPatCntAutoCustom() =>
      EmbLibrary().apiBinding.getEmbMarkPatCntAutoCustom();

  ///
  /// 2本指による拡縮時に更新するの関数
  ///
  void scaleUpdate(ScaleUpdateDetails details, double scale) {
    if (details.scale > 1) {
      saveThumbnailSize(ThumbnailSizeType.values[thumbnailSize.index + 1]);
    } else if (details.scale < 1) {
      saveThumbnailSize(ThumbnailSizeType.values[thumbnailSize.index - 1]);
    }
  }

  ///
  /// 画像キャッシュをクリアする
  ///
  void clearCurrentPatternImageCache({Pattern? pattern}) {
    if (pattern != null) {
      if (pattern is EmbBorder) {
        pattern.clearGroupImageCache();
      } else if (pattern is EmbGroup) {
        pattern.clearMainImageCache();
      }
    } else {
      Pattern currentPattern = getCurrentPattern();
      if (currentPattern is EmbBorder) {
        currentPattern.clearGroupImageCache();
      } else if (currentPattern is EmbGroup) {
        currentPattern.clearMainImageCache();
      }
    }
  }

  ///
  /// 現在のグループのBorder情報キャッシュをクリア
  ///
  void clearCurrentBorderInfoCache({Pattern? pattern}) {
    if (pattern != null) {
      if (pattern is EmbBorder) {
        pattern
          ..clearBorderInfoCache()
          ..clearBorderCompInfoCache();
      } else {
        /// do nothing
      }
    } else {
      Pattern currentPattern = getCurrentPattern();
      if (currentPattern is EmbBorder) {
        currentPattern
          ..clearBorderInfoCache()
          ..clearBorderCompInfoCache();
      } else {
        /// do nothing
      }
    }
  }

  ///
  /// 現在のグループ情報キャッシュをクリア
  ///
  void clearCurrentGroupInfoCache({Pattern? pattern}) {
    if (pattern != null) {
      if (pattern is EmbBorder) {
        pattern.clearGroupInfoCache();
      } else if (pattern is EmbGroup) {
        pattern.clearGroupInfoCache();
      }
    } else {
      Pattern currentPattern = getCurrentPattern();
      if (currentPattern is EmbBorder) {
        currentPattern.clearGroupInfoCache();
      } else if (currentPattern is EmbGroup) {
        currentPattern.clearGroupInfoCache();
      }
    }
  }

  ///
  /// 現在のグループスレッド情報キャッシュをクリアする
  ///
  void clearCurrentThreadInfoCache({Pattern? pattern}) {
    if (pattern != null) {
      if (pattern is EmbBorder) {
        pattern.clearThreadInfoCache();
      } else if (pattern is EmbGroup) {
        pattern.clearThreadInfoCache();
      }
    } else {
      Pattern currentPattern = getCurrentPattern();
      if (currentPattern is EmbBorder) {
        currentPattern.clearThreadInfoCache();
      } else if (currentPattern is EmbGroup) {
        currentPattern.clearThreadInfoCache();
      }
    }
  }

  ///
  /// 選択したすべてのパターンのBorder情報をクリアします
  ///
  void clearAllSelectedBorderInfoCache({List<Pattern>? patternList}) {
    if (patternList != null && patternList.isNotEmpty) {
      for (var pattern in patternList) {
        if (pattern is EmbBorder) {
          pattern
            ..clearBorderInfoCache()
            ..clearBorderCompInfoCache();
        } else {
          ///do nothing
        }
      }
    } else {
      List<Pattern> selectedPatternList = getAllSelectedPattern();
      for (var pattern in selectedPatternList) {
        if (pattern is EmbBorder) {
          pattern
            ..clearBorderInfoCache()
            ..clearBorderCompInfoCache();
        } else {
          ///do nothing
        }
      }
    }
  }

  ///
  /// 選択したすべてのパターンの情報をクリアします
  ///
  void clearAllSelectedGroupInfoCache({List<Pattern>? patternList}) {
    if (patternList != null && patternList.isNotEmpty) {
      for (var pattern in patternList) {
        if (pattern is EmbBorder) {
          pattern.clearGroupInfoCache();
        } else if (pattern is EmbGroup) {
          pattern.clearGroupInfoCache();
        }
      }
    } else {
      List<Pattern> selectedPatternList = getAllSelectedPattern();
      for (var pattern in selectedPatternList) {
        if (pattern is EmbBorder) {
          pattern.clearGroupInfoCache();
        } else if (pattern is EmbGroup) {
          pattern.clearGroupInfoCache();
        }
      }
    }
  }

  ///
  /// すべてのパターンの画像キャッシュをクリアする
  ///
  void clearAllPatternImageCache() {
    for (var pattern in _patternList) {
      if (pattern is EmbBorder) {
        pattern.clearGroupImageCache();
      } else if (pattern is EmbGroup) {
        pattern.clearMainImageCache();
      }
    }
    for (var pattern in _temporaryPatternList) {
      if (pattern is EmbBorder) {
        pattern.clearGroupImageCache();
      } else if (pattern is EmbGroup) {
        pattern.clearMainImageCache();
      }
    }
  }

  ///
  /// すべてのパターンの線色情報キャッシュをクリアする
  ///
  void clearAllPatternThreadInfoCache() {
    for (var pattern in _patternList) {
      if (pattern is EmbBorder) {
        pattern.clearThreadInfoCache();
      } else if (pattern is EmbGroup) {
        pattern.clearThreadInfoCache();
      }
    }
    for (var pattern in _temporaryPatternList) {
      if (pattern is EmbBorder) {
        pattern.clearThreadInfoCache();
      } else if (pattern is EmbGroup) {
        pattern.clearThreadInfoCache();
      }
    }
  }

  ///
  /// このModelのすべてのデータを初期化する
  ///
  void reset() {
    _patternList.clear();
    _temporaryPatternList.clear();
    selectedZoomScaleInSelectPage = zoomList.first;
    selectedZoomScaleInEditPage = zoomList.first;
    realPreviewDisplayType = RealPreviewDisplayType.patternSelect;
  }
}

///
/// UI用のPattern定義
///
abstract class Pattern {
  Pattern(this.handle);

  /// Lib用のハンドル
  MemHandle handle;
}

///
/// Group模様の定義
///
class EmbGroup extends Pattern {
  EmbGroup(
    super.handle, [
    EmbPatternType? embPatternType,
    bool? isFont,
  ]) {
    /// 模様タイプの初期化
    if (embPatternType == null) {
      for (var element in EmbPatternType.values) {
        if (EmbLibrary()
            .apiBinding
            .checkEmbPatternType(handle, element)
            .result) {
          _embType = element;
          break;
        }
      }
    } else {
      _embType = embPatternType;
    }

    /// テキストであるかどうかを判断する
    if (isFont == null) {
      if (_embType == EmbPatternType.char ||
          _embType == EmbPatternType.extChar) {
        _isFont = true;
      } else {
        _isFont = false;
      }
    } else {
      _isFont = isFont;
    }
  }

  ///
  /// 現在のEmbGroupをコピーする
  ///
  EmbGroup copyWithNewHandle(MemHandle newHandle) {
    EmbGroup copy = EmbGroup(
      newHandle,
      _embType,
      _isFont,
    );

    /// 現在のEmbGroupのキャッシュデータをコピーしてください
    copy.setCacheData(
      embGrp: _embGrp,
      embInfo: _embInfo,
      embPatternInfo: _embPatternInfo,
      normalMainImage: _normalMainImage,
      scaledMainImage: _scaledMainImage,
      imageScale: _imageScale,
      centerType: _centerType,
      arcImageCache: _arcImageCache,
      threadInfo: _threadInfo,
    );

    return copy;
  }

  ///
  /// EmbGroupのキャッシュデータを設定します
  ///
  void setCacheData({
    required EmbInfo? embInfo,
    required EmbGrp? embGrp,
    required EmbPtrnInfo? embPatternInfo,
    required List<Uint8List> normalMainImage,
    required List<Uint8List> scaledMainImage,
    required int imageScale,
    required int centerType,
    required Uint8List? arcImageCache,
    required List<ThreadInfo> threadInfo,
  }) {
    /// GroupInfoデータ
    _embGrp = embGrp;
    _embInfo = embInfo;
    _embPatternInfo = embPatternInfo;

    /// 画像データ
    _normalMainImage = normalMainImage;
    _scaledMainImage = scaledMainImage;
    _imageScale = imageScale;
    _centerType = centerType;
    _arcImageCache = arcImageCache;

    /// 線色情報
    _threadInfo = threadInfo;
  }

  ///
  /// 模様タイプ確認
  ///
  bool checkEmbPatternType(EmbPatternType embType) => _embType == embType;
  EmbPatternType _embType = EmbPatternType.extChar;

  ///
  /// 模様タイプを取得します
  ///
  EmbPatternType getEmbPatternType() => _embType;

  ///
  /// パターンが文字パターンであるかどうか
  ///
  bool get isFont => _isFont;
  bool _isFont = false;

  ///
  /// 現在選択されているGroup状態を取得します
  ///
  bool isCurrentGroup(MemHandle currentGroupHandle) =>
      handle == currentGroupHandle;

  EmbGrp? _embGrp;
  EmbInfo? _embInfo;
  EmbPtrnInfo? _embPatternInfo;

  ///
  /// GroupInfoのキャッシュ データをクリアします
  ///
  /// **!!!!! この関数は ViewModel では使用しないでください !!!!!** </br>
  /// **!!!!! Don't use this function in any ViewModel !!!!!**
  ///
  void clearGroupInfoCache() {
    _embGrp = null;
    _embInfo = null;
    _embPatternInfo = null;

    if (isFont) {
      _arcImageCache = null;
    }
  }

  ///
  /// 模様情報を取得
  /// キャッシュされたデータが存在する場合は、キャッシュされたデータが使用されます
  ///
  /// 文字模様:embInfoの数は入力された文字の数です
  /// その他:ひとつだけ
  ///
  ({
    EmbInfo embInfo,
    EmbGrp embGrp,
    EmbPtrnInfo embPatternInfo,
  }) get embGroupInfo {
    if (_embInfo == null || _embGrp == null || _embPatternInfo == null) {
      var (
        errorCode: _,
        embGrp: embGrp,
        embInfo: embInfo,
        embPatternInfo: embPatternInfo,
      ) = EmbLibrary().apiBinding.getEmbGrpInfo(handle);

      _embGrp = embGrp;
      _embInfo = embInfo;
      _embPatternInfo = embPatternInfo;
    } else {
      /// Do noting
    }

    return (
      embGrp: _embGrp!,
      embInfo: _embInfo!,
      embPatternInfo: _embPatternInfo!,
    );
  }

  ///
  /// 完全に表示された画像を取得する
  ///
  List<Uint8List> _normalMainImage = [];
  List<Uint8List> _scaledMainImage = [];
  int _imageScale = 100;
  int _centerType = ScrollCenterType.IMAGE_EDITING;
  List<Uint8List> mainImage(int centerType, int scale) {
    if (scale == 100) {
      if (_normalMainImage.isEmpty || _centerType != centerType) {
        _centerType = centerType;
        var result = EmbLibrary()
            .apiBinding
            .getSelectedGroupARGBImage(handle, centerType, scale, true);
        _normalMainImage.clear();
        _normalMainImage.addAll(result.imageDataList);
      }
      return _normalMainImage;
    } else {
      if (_scaledMainImage.isEmpty ||
          scale != _imageScale ||
          _centerType != centerType) {
        _imageScale = scale;
        _centerType = centerType;
        var result = EmbLibrary()
            .apiBinding
            .getSelectedGroupARGBImage(handle, centerType, scale, true);
        _scaledMainImage.clear();
        _scaledMainImage.addAll(result.imageDataList);
      }
      return _scaledMainImage;
    }
  }

  ///
  /// 完全に表示された画像を取得する
  ///
  /// **!!!!! この関数は ViewModel では使用しないでください !!!!!** </br>
  /// **!!!!! Don't use this function in any ViewModel !!!!!**
  ///
  void clearMainImageCache() {
    _normalMainImage = [];
    _scaledMainImage = [];
    _imageScale = 100;
  }

  ///
  /// 完全なピクチャを構成するメンバーピクチャを取得します
  ///
  Uint8List subImage(int centerType, int scale) {
    var result = EmbLibrary()
        .apiBinding
        .getSelectedGroupARGBImage(handle, centerType, scale, false);

    return result.imageDataList.first;
  }

  List<ThreadInfo> _threadInfo = [];

  ///
  /// 線色情報のキャッシュ データをクリアします
  ///
  /// **!!!!! この関数は ViewModel では使用しないでください !!!!!** </br>
  /// **!!!!! Don't use this function in any ViewModel !!!!!**
  ///
  void clearThreadInfoCache() {
    _threadInfo.clear();
    _threadInfo = [];
  }

  ///
  /// 線色情報
  /// キャッシュされたデータが存在する場合は、キャッシュされたデータが使用されます
  ///
  List<ThreadInfo> get threadInfo {
    if (_threadInfo.isEmpty) {
      _threadInfo =
          EmbLibrary().apiBinding.getEmbEditThreadInfo(handle).threadInfoList;
    } else {
      /// Do nothing
    }

    return _threadInfo;
  }

  ///
  /// 編集対象とする模様を選択する
  /// 現在の模様を切り替えます
  ///
  void selectEmbToEdit() {
    EmbLibrary().apiBinding.selectEmbToEdit(handle);
  }

  ///
  /// 複数選択状態は、選択/非選択状態にする
  ///
  void selectMultiEmb(bool isSelect) {
    EmbLibrary().apiBinding.selectMultiEmb(
        handle,
        isSelect
            ? GroupSelectCondition.groupSelected
            : GroupSelectCondition.groupUnselected);
    clearGroupInfoCache();
  }

  ///
  /// 模様サイズを変更する
  ///
  EmbLibraryError changeSizeEmbGroup(
      {required MagType type, required int step}) {
    EmbLibraryError embErrorCode = EmbLibraryError.EMB_NO_ERR;

    if (embGroupInfo.embPatternInfo.embPatterns.first.doSTB) {
      embErrorCode = EmbLibrary().apiBinding.changeSizeEmbGroupSTB(type, step);
    } else {
      embErrorCode = EmbLibrary().apiBinding.changeSizeEmbGroup(type, step);
    }

    return embErrorCode;
  }

  ///
  /// 模様サイズをリストアする
  ///
  EmbLibraryError resetSizeEmbGroup({required bool isChangeDoSTB}) {
    EmbLibraryError embErrorCode = EmbLibraryError.EMB_NO_ERR;
    EmbPtrn embPattern = embGroupInfo.embPatternInfo.embPatterns.first;

    /// 模様サイズを変更する
    if (embPattern.doSTB) {
      embErrorCode =
          EmbLibrary().apiBinding.changeSizeEmbGroupSTB(MagType.xyReset, 0);
    } else {
      embErrorCode =
          EmbLibrary().apiBinding.changeSizeEmbGroup(MagType.xyReset, 0);
    }

    return embErrorCode;
  }

  ///
  /// 指定したグループハンドルを含むボーダーハンドルを取得する
  ///
  MemHandle? getBorderHandleIncludeGroup() {
    final (errorCode: error, handle: borderHandle) =
        EmbLibrary().apiBinding.getBorderHandleIncludeGroup(handle);
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return null;
    } else {
      return borderHandle;
    }
  }

  ///
  /// EmbCharPatternを取る
  ///
  List<MemHandle> getEmbCharPatternHandleList() {
    List<MemHandle> embCharHandleList = [];

    final int patternNum =
        EmbCharLibrary().apiBinding.getPatternNum(handle).patternNum;
    for (var i = 0; i < patternNum; i++) {
      MemHandle setHandle =
          EmbCharLibrary().apiBinding.getPatternHandle(handle, i).setHandle;
      embCharHandleList.add(setHandle);
    }

    return embCharHandleList;
  }

  ///
  /// 円弧下線イメージのキャッシュ
  ///
  Uint8List? _arcImageCache;

  ///
  /// 円弧下線イメージの取得
  ///
  Uint8List getArcImage(int centerType) {
    if (isFont == false) {
      return Uint8List.fromList([]);
    }

    if (_arcImageCache == null) {
      var image =
          EmbCharLibrary().apiBinding.getArcImage(handle, centerType).image;
      _arcImageCache = image;
    }

    return _arcImageCache!;
  }

  /// 文字列
  List<String> get charList {
    List<String> unicodeList = [];
    for (var element in embGroupInfo.embPatternInfo.embPatterns) {
      unicodeList.add(String.fromCharCode(element.unicode));
    }

    return unicodeList;
  }

  /// 文字の配列
  ArrayTypes get charArray {
    PatternLayOut patternLayOut = embGroupInfo.embGrp.patternLayOutMethod;

    switch (patternLayOut) {
      case PatternLayOut.flat:
        return ArrayTypes.flat;
      case PatternLayOut.looseOutSide:
        return ArrayTypes.looseOutSide;
      case PatternLayOut.steepInSide:
        return ArrayTypes.steepInSide;
      case PatternLayOut.steepOutSide:
        return ArrayTypes.steepOutSide;
      case PatternLayOut.looseInSide:
        return ArrayTypes.looseInSide;
      case PatternLayOut.slant:
        return ArrayTypes.slant;
      default:
        return ArrayTypes.flat;
    }
  }
}

///
/// Border模様の定義
///
class EmbBorder extends Pattern {
  EmbBorder(super.handle) {
    updateGroupList();
  }

  ///
  /// Border内のGroup情報
  ///
  final List<EmbGroup> _groupList = [];

  ///
  /// Border内のGroup情報の取得
  ///
  List<EmbGroup> get groupList => _groupList;

  ///
  /// 現在選択されているGroupを含むかどうか
  ///
  bool isContainCurrentPattern(MemHandle currentPatternHandle) {
    for (var group in groupList) {
      if (group.isCurrentGroup(currentPatternHandle)) {
        return true;
      }
    }
    return false;
  }

  ///
  /// GroupInfoのキャッシュ データをクリアします
  ///
  /// **!!!!! この関数は ViewModel では使用しないでください !!!!!** </br>
  /// **!!!!! Don't use this function in any ViewModel !!!!!**
  ///
  void clearGroupInfoCache() {
    for (var group in _groupList) {
      group.clearGroupInfoCache();
    }
  }

  ///
  /// 画像キャッシュデータのクリア
  ///
  /// **!!!!! この関数は ViewModel では使用しないでください !!!!!** </br>
  /// **!!!!! Don't use this function in any ViewModel !!!!!**
  ///
  void clearGroupImageCache() {
    for (var group in _groupList) {
      group.clearMainImageCache();
    }
  }

  ///
  /// 線色情報のキャッシュ データをクリアします
  ///
  /// **!!!!! この関数は ViewModel では使用しないでください !!!!!** </br>
  /// **!!!!! Don't use this function in any ViewModel !!!!!**
  ///
  void clearThreadInfoCache() {
    for (var group in _groupList) {
      group.clearThreadInfoCache();
    }
  }

  ///
  /// GroupList の更新
  ///
  void updateGroupList() {
    var (
      errorCode: _,
      handleList: handleList,
    ) = EmbLibrary().apiBinding.getGroupHandleInBorder(handle);

    if (handleList.isEmpty) {
      Log.assertTrace("getGroupHandleInBorderが取得したhandleリストは空データです!!");
      return;
    }

    _borderInfo = null;

    if (handleList.length != borderInfo.repeatTimes * borderInfo.groupNum) {
      Log.assertTrace(
          "getGroupHandleInBorderで取得したhandleListの数と、getBorderInfoで得られた(style.X * style.Y * groupNum)の結果が一致していません!!");
      return;
    }

    /// 基本データ
    final List<EmbGroup> subBorderEmbGroup = [];

    if (_groupList.isEmpty) {
      subBorderEmbGroup.addAll(handleList
          .getRange(0, borderInfo.groupNum)
          .map((e) => EmbGroup(e))
          .toList());
    } else {
      subBorderEmbGroup
          .addAll(_groupList.getRange(0, borderInfo.groupNum).toList());
    }

    _groupList.clear();

    final Iterator<MemHandle> handleIterator = handleList.iterator;

    /// 重複回数
    for (int times = 0; times < borderInfo.repeatTimes; times++) {
      /// subBorderEmbGroupを走査し
      for (EmbGroup embGroup in subBorderEmbGroup) {
        /// データを追加する
        handleIterator.moveNext();
        _groupList.add(embGroup.copyWithNewHandle(handleIterator.current));
      }
    }
  }

  ///
  /// ボーダー情報取得
  ///
  BorderInfo get borderInfo {
    if (_borderInfo == null) {
      var (
        errorCode: _,
        borderInfo: borderInfo,
      ) = EmbLibrary().apiBinding.getBorderInfo(handle);
      _borderInfo = borderInfo;
    }

    return _borderInfo!;
  }

  ///
  /// ボーダー情報のキャッシュ
  ///
  BorderInfo? _borderInfo;

  ///
  /// ボーダー情報のキャッシュをクリアする
  ///
  /// **!!!!! この関数は ViewModel では使用しないでください !!!!!** </br>
  /// **!!!!! Don't use this function in any ViewModel !!!!!**
  ///
  void clearBorderInfoCache() {
    _borderInfo = null;
  }

  ///
  /// Repeat Border模様かどうか
  ///
  bool get isRepeatBorder => borderInfo.isRepeatBorder;

  ///
  /// 糸印ありなし状態のキャッシュ
  ///
  Map<int, ThreadMarkState>? _threadMarkState;

  ///
  /// 指定したグループハンドルを含むボーダーハンドルを取得する
  ///
  ThreadMarkState getBorderCompInfo(int index) {
    /// データキャッシュなし
    if (_threadMarkState == null) {
      var (
        errorCode: _,
        area: _,
        threadMarkState: threadMarkState,
      ) = EmbLibrary().apiBinding.getBorderCompInfo(handle, index);

      _threadMarkState = {
        index: threadMarkState,
      };
    }

    /// データキャッシュはありますが、indexのデータはありません
    else if (_threadMarkState!.containsKey(index) == false) {
      var (
        errorCode: _,
        area: _,
        threadMarkState: threadMarkState,
      ) = EmbLibrary().apiBinding.getBorderCompInfo(handle, index);

      _threadMarkState![index] = threadMarkState;
    } else {
      /// do nothing
    }

    return _threadMarkState![index]!;
  }

  ///
  /// BorderCompInfoのキャッシュをクリアする
  ///
  /// **!!!!! この関数は ViewModel では使用しないでください !!!!!** </br>
  /// **!!!!! Don't use this function in any ViewModel !!!!!**
  ///
  void clearBorderCompInfoCache() {
    _threadMarkState = null;
  }

  ///
  /// 線色情報
  ///
  List<ThreadInfo> threadInfo() {
    List<ThreadInfo> threadInfoList = [];
    for (var group in _groupList) {
      threadInfoList.addAll(group.threadInfo);
    }

    return threadInfoList;
  }

  ///
  /// 編集対象とする模様を選択する
  /// 現在の模様を切り替えます
  ///
  void selectEmbToEdit() =>
      EmbLibrary().apiBinding.selectEmbToEdit(groupList.first.handle);

  ///
  /// 複数選択状態は、選択/非選択状態にする
  ///
  void selectMultiEmb(bool isSelect) {
    EmbLibrary().apiBinding.selectMultiEmb(
        groupList.first.handle,
        isSelect
            ? GroupSelectCondition.groupSelected
            : GroupSelectCondition.groupUnselected);
    clearGroupInfoCache();
    clearBorderCompInfoCache();
    clearBorderInfoCache();
  }
}

///
/// 現在の選択モード
///
enum SelectMode {
  /// 単一選択状態
  group,

  /// 単一選択組み合わせ状態
  border,

  /// 複数選択状態
  multiple,

  /// 複数選択状態(組合せを含む)
  multipleContainBorder,
}

///
/// 現在のログイン模様の表示用まとめ情報
///
class PatternDispInfo {
  const PatternDispInfo({
    required this.left,
    required this.right,
    required this.top,
    required this.bottom,
    required this.allPatternWidth,
    required this.allPatternHeight,
    required this.stitchNum,
    required this.threadNum,
    required this.sewingTime,
    this.colorNum = 0,
  });

  final int left;
  final int right;
  final int top;
  final int bottom;
  final int stitchNum;
  final int threadNum;
  final int sewingTime;
  final int colorNum;

  /// 全体サイズ幅
  final int allPatternWidth;

  /// 全体サイズ高さ
  final int allPatternHeight;
}

///
/// Real Previewの表示モード
///
enum RealPreviewDisplayType {
  /// 模様選択画面
  patternSelect,

  /// 編集画面
  patternEdit,

  /// 縫製画面
  sewing,

  /// インフォメーション画面
  information,
}
