import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'motif_flip_popup_view_model.dart';

class MotifFlipPopup extends ConsumerStatefulWidget {
  const MotifFlipPopup({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState<MotifFlipPopup> createState() => _MotifFlipState();
}

class _MotifFlipState extends ConsumerState<MotifFlipPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(motifFlipViewModelProvider);
    final viewModel = ref.read(motifFlipViewModelProvider.notifier);

    return Row(
      children: [
        const Spacer(
          flex: 571,
        ),
        Expanded(
          flex: 229,
          child: Column(
            children: [
              const Spacer(
                flex: 159,
              ),
              Expanded(
                flex: 1052,
                child: Scaffold(
                  backgroundColor: Colors.transparent,
                  body: pre_edit_toolbar_mdc(
                    child: Column(
                      children: [
                        const Spacer(
                          flex: 129,
                        ),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_str_flip(
                                  text: l10n.icon_00567,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 89,
                        ),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 98,
                                child: grp_btn_mdc_flipInside(
                                  onTap: () => viewModel.onInsideButtonClick(),
                                  state: state.isSelectInside
                                      ? ButtonState.select
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 9,
                              ),
                              Expanded(
                                flex: 98,
                                child: grp_btn_mdc_flipoutside(
                                  onTap: () => viewModel.onOutsideButtonClick(),
                                  state: state.isSelectOutside
                                      ? ButtonState.select
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 613,
                        ),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_btn_positive_mdc(
                                  onTap: viewModel.onOkButtonClicked,
                                  text: l10n.icon_ok,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 12,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const Spacer(
                flex: 69,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
