// ignore_for_file: constant_identifier_names

import 'library_manage_meta.dart';

class EmbApiResult {
  const EmbApiResult(
    this.apiFunction,
    this.testResult,
    this.comment,
  );

  ///
  /// API関連の機能
  ///
  final EmbApiFunction apiFunction;

  ///
  /// API開発状態
  ///
  final ApiStatus testResult;

  ///
  /// 説明
  ///
  final String comment;
}

enum EmbApiFunction {
  cameraScan,
  initial,
  applique,
  backgroundScan,
  quiltSashe,
  quiltHexagon,
  quiltEdgetoEdge,
  emb_output_setting,
  embSelect_Category_Select,
  embSelect_Login,
  embSelect_UintMove,
  embSelect_PatternMove,
  embSelect_FileRead,

  embEdit_ChangeColor,
  embChar_keyBoard,
  embSewing,
  embSewingTension,
  embSewingColorTrim,
  embSewingStitchTrim,
  embSewingFeedback,
  embConnectSewing,
  embSewing_startPoint,
  embSewing_maskTrace,
  embSewing_PatternMove,
  embSewing_toolbar_layoutmenu_rotate_size_return,
  embSewingSnowman,
  embCheckPattern,
  border,
  screen_transfer,
  resume,
  realPreview,
  stipple,
}
