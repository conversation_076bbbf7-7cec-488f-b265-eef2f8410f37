import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_osae_down_emb_view_interface.dart';

final errOsaeDownEmbViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrOsaeDownEmbViewInterface, ErrOsaeDownEmbState,
        BuildContext>((ref, context) => ErrOsaeDownEmbViewModel(ref, context));

class ErrOsaeDownEmbViewModel extends ErrOsaeDownEmbViewInterface {
  ErrOsaeDownEmbViewModel(Ref ref, BuildContext context)
      : super(const ErrOsaeDownEmbState(), ref, context);

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    final int errCode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERROSAEDOWN);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }
}
