import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'chain_thickness_view_interface.freezed.dart';

@freezed
class ChainThicknessState with _$ChainThicknessState {
  const factory ChainThicknessState({
    required String thicknessInputValue,
    required bool isDefaultStyle,
    required bool plusButtonValid,
    required bool minusButtonValid,
  }) = _ChainThicknessState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class ChainThicknessStateViewInterface
    extends ViewModel<ChainThicknessState> {
  ChainThicknessStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// チェーンステッチのデフォルト値
  ///
  int get defaultValue;

  ///
  /// チェーンステッチのThickness増大
  ///
  bool plusLineChainSize(bool isLongPress);

  ///
  /// チェーンステッチのThickness縮小
  ///
  bool miniLineChainSize(bool isLongPress);
}
