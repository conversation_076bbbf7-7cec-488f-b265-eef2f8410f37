import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_thread_reset_view_interface.dart';

final errThreadResetViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrThreadResetViewInterface, ErrThreadResetState,
        BuildContext>((ref, context) => ErrThreadResetViewModel(ref, context));

class ErrThreadResetViewModel extends ErrThreadResetViewInterface {
  ErrThreadResetViewModel(Ref ref, BuildContext context)
      : super(const ErrThreadResetState(), ref, context);

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    final int errCode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERRORTHREADRESET);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }
}
