import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_not_ss_botton_by_foot_controler_view_interface.dart';

final errNotSsBottonByFootControlerViewModelProvider =
    StateNotifierProvider.family.autoDispose<
            ErrNotSsBottonByFootControlerViewInterface,
            ErrNotSsBottonByFootControlerState,
            BuildContext>(
        (ref, context) => ErrNotSsBottonByFootControlerViewModel(ref, context));

class ErrNotSsBottonByFootControlerViewModel
    extends ErrNotSsBottonByFootControlerViewInterface {
  ErrNotSsBottonByFootControlerViewModel(Ref ref, BuildContext context)
      : super(const ErrNotSsBottonByFootControlerState(), ref, context);

  @override
  void onOKButtonClicked() {
    int errcode;
    errcode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERRORSSWHENFOOTON);
    if (errcode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }
  }
}
