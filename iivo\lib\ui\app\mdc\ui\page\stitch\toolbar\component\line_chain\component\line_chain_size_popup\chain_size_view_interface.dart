import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'chain_size_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class ChainSizeState with _$ChainSizeState {
  const factory ChainSizeState({
    required String sizeInputValue,
    required bool isDefaultStyle,
    required bool plusButtonValid,
    required bool minusButtonValid,
  }) = _ChainSizeState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class ChainSizeStateSizeStateViewInterface
    extends ViewModel<ChainSizeState> {
  ChainSizeStateSizeStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool miniLineChainSize(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool plusLineChainSize(bool isLongPress);

  ///
  /// チェーンステッチのデフォルト値
  ///
  int get defaultValue;

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
