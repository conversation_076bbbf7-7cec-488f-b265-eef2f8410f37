import 'dart:ffi';
import 'dart:math';
import 'dart:ui';

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../model/frame_model.dart';
import '../../../../../../../model/preview_area_size_model.dart';
import '../../../../../../../model/projector_model.dart';
import '../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../model/border_model.dart';
import '../../../../model/edit_model.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/preview_model.dart';
import '../../../../model/redo_undo_model.dart';
import '../../../../model/scan_model.dart';
import '../../common_component/function_provider/move_function_provider/move_function_interface.dart';
import '../../common_component/function_provider/move_function_provider/move_function_provider.dart';
import '../../common_component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../pattern_edit_view_model.dart';
import 'preview_view_interface.dart';

typedef PatternViewDisplayInfo = PatternDisplayInfo;
typedef EmbBorderViewDisplayInfo = EmbBorderDisplayInfo;
typedef EmbGroupViewDisplayInfo = EmbGroupDisplayInfo;
typedef PatternViewRedPointInfo = RedPointInfo;
typedef ThreadMarkDisplayInfo = ThreadMarkInfo;
typedef EmbPatternViewDisplayInfo = EmbPatternDisplayInfo;

typedef PopupType = ToolbarPopupId;

/// 移動量　0.5mm単位　1で0.5mm移動
const double _oneMoveStepInMm = 0.5;

/// 10mm定義
const int _gridLine10MmValue = 10;

/// 25mm定義
const int _gridLine25MmValue = 25;

/// フレームの破線の一段の長さ
const double _dashedLineLength = 2.0;

/// フレームの破線の間隔
const double _dashedLineSpace = 2.0;

/// 黒点行列の間隔
const double _blackPointsSpace = 10.0;

/// Maskの表示部を超えるCutLineの長さ
double _cutLineOutMaskSize = 10 * _pixelOfOneMm;

/// 1本指
const int _oneFinger = 1;

/// 1画素に対応するmm
double _mmOfOnePixel = frame297x465MmSize.dx / embPreviewSizeDot.dx;

/// 1mmに対応する画素数
double _pixelOfOneMm = embPreviewSizeDot.dx / frame297x465MmSize.dx;

/// 角度の定義
const int _angle360 = 360;
const int _angle180 = 180;
const int _angle90 = 90;

/// プレビュー中心点
final Offset _originalCenter = embPreviewSizeDot / 2;

/// UIでのプレembPreviewSizeDot
final Rect _zoomPreviewRect = Rect.fromCenter(
  center: _originalCenter,
  width: embPreviewSizeDot.dx,
  height: embPreviewSizeDot.dy,
);

final previewViewModelProvider =
    StateNotifierProvider.autoDispose<PreviewViewModelInterface, PreviewState>(
  (ref) => PreviewViewModel(ref),
);

class PreviewViewModel extends PreviewViewModelInterface {
  PreviewViewModel(Ref ref)
      : super(
          PreviewState(
            patternDisplayInfoList: [],
            backgroundColor: Colors.transparent,
            maskColor: const Color.fromARGB(255, 235, 0, 0),
            popupType: ToolbarPopupId.none,
            gridTypeIndex: 0,
            gridColor: Colors.transparent,
            frameDrawPath: Path(),
            frameColor: Colors.transparent,
            blackPoints: [],
            isRectangularMarqueeEnable: false,
            isDragPreviewEnable: false,
            zoomOutPoint: const Offset(0, 0),
            isRedPointOverPreview: false,
            cutLinePath: Path(),
            threadMarkInfoList: [],
            isProjectorON: false,
            backgroundImage: null,
            knifeDisplayWidget: null,
            borderMarkMaskRect: null,
            rectangularMarqueeArea: null,
            dragAngle: null,
            redPointInfo: null,
            isPreviewClickEnable: true,
          ),
          ref,
        ) {
    _prevZoomScale = PatternModel().selectedZoomScaleInEditPage;
    _openListen();
    update();
  }

  ///
  /// UI上の現在選択されているpatternの中心座標
  ///
  Offset _rotatePointCenter = const Offset(0, 0);

  ///
  /// Rotateを使用した場合の5°ごとの記録
  /// 今回の回転が5°に達したかどうかを計算するために使用します。「_angleDegrees」の更新が必要かどうか
  ///
  double _startAngle = 0;

  ///
  /// 前回の更新時に、画面上で指がドラッグした座標を記録する。
  /// 更新されるたびに、この座標は最新のドラッグ位置に更新される。
  ///
  /// 例えば: Rotate Point Drag、Move Drag
  ///
  Offset _lastDragPosition = const Offset(0, 0);

  ///
  /// Size赤点を使用したドラッグ量
  ///
  Offset _sizeRedPointDragCount = const Offset(0, 0);

  ///
  /// ドットの使用
  ///
  bool _isUsingRedPoint = false;

  ///
  /// 矩形選択ツールドラッグの開始座標と終了座標
  ///
  Offset _rectStartDargPoint = const Offset(0, 0);
  Offset _rectEndDargPoint = const Offset(0, 0);

  /// 今回の矩形選択の設定する状態
  /// 矩形選択で最初にヒットした模様が選択状態だった場合は、矩形範囲内の模様はすべて非選択状態になる。
  /// 矩形選択で最初にヒットした模様が非選択状態だった場合は、矩形範囲内の模様はすべて選択状態になる。
  bool? _rectTouchState;

  /// 今回ブロックされたPatternのIndex
  final List<int> _boxedPatternIndexList = [];

  ///
  /// 現在の表示倍率のバックアップ
  ///
  int _prevZoomScale = PatternModel().selectedZoomScaleInEditPage;

  ///
  /// Previewドラッグの開始点
  ///
  Offset _previewStartDargPoint = const Offset(0, 0);

  ///
  /// 拡大された現在のPreviewの中心点
  /// この点に疑問がある場合は、この変数をViewに描画することができます(zoomOutPointとの結合理解)
  ///
  Offset _viewCenter = _originalCenter;

  ///
  /// 2本指の拡大中、前回の拡大倍数
  ///
  int _prevDoubleFingerSizeStep = 0;

  ///
  /// 2本指でZoom倍率を拡大する前のZoom値バックアップ
  ///
  int _selectedZoomScaleBack = PatternModel().selectedZoomScaleInEditPage;

  @override
  void update() {
    final patternDisplayInfoList = _getPatternDisplayInfoList();

    /// 赤いドットは最後のパターンにのみ表示されます
    final Offset patternTopLeft = Offset(
      patternDisplayInfoList.last.left,
      patternDisplayInfoList.last.top,
    );
    final Offset patternBottomRight = Offset(
      patternDisplayInfoList.last.left + patternDisplayInfoList.last.width,
      patternDisplayInfoList.last.top + patternDisplayInfoList.last.height,
    );

    /// 分割線 と ナイフ
    final (cutLinePath, knifeDisplayWidget) =
        _getCutLineAndKnife(patternDisplayInfoList.last);

    /// view更新
    state = state.copyWith(
      patternDisplayInfoList: patternDisplayInfoList,
      backgroundColor: PreviewModel().getEmbroideryBackgroundColor(),
      gridTypeIndex: DeviceLibrary().apiBinding.getGrid().gridType.index,
      frameDrawPath: _getFrameDrawPath(),
      gridColor: PreviewModel().getEmbroideryGridColor(),
      frameColor: PreviewModel().getEmbroideryFrameColor(),
      maskColor: _isUsingRedPoint
          ? const Color.fromARGB(255, 48, 48, 160)
          : const Color.fromARGB(255, 235, 0, 0),
      popupType: EditModel().toolbarPopupId,
      blackPoints: _getBlackPoints(),
      isRectangularMarqueeEnable: _isRectangularMarqueeEnable(),
      isDragPreviewEnable: PreviewModel().isInDragPreviewMode,
      rectangularMarqueeArea:
          Rect.fromPoints(_rectStartDargPoint, _rectEndDargPoint),
      zoomOutPoint: _getZoomOutCenterPoint(),
      isRedPointOverPreview: _isRedPointOverPreview(
        viewCenter: _viewCenter,
        topLeft: patternTopLeft,
        bottomRight: patternBottomRight,
      ),
      backgroundImage: _getScanBackGroundImage(),
      cutLinePath: cutLinePath,
      knifeDisplayWidget: knifeDisplayWidget,
      borderMarkMaskRect: _getThreadMarkMaskRect(patternDisplayInfoList.last),
      threadMarkInfoList: PreviewModel.getThreadMarkInfoList(
        centerPoint: _originalCenter,
        pixelOfOneMm: _pixelOfOneMm,
        patternDisplayInfoList: patternDisplayInfoList,
      ),
      isProjectorON: ProjectorModel().embProjector.isEmbProjectorViewOpen,
      dragAngle: null,
      redPointInfo: _getRedPointInfo(patternInfo: patternDisplayInfoList.last),
    );
  }

  @override
  Offset get getPreviewSize => embPreviewSizeDot;

  @override
  double get getMagnification =>
      PatternModel().selectedZoomScaleInEditPage / zoomList.first;

  @override
  double get getMaskOutArea => maskOutArea;

  @override
  void onPreviewSpaceAreaClicked() {
    /// 複数のページを選択したり、ページを並べたりするときに、空白の領域をクリックすることはありません
    if ([
      ToolbarPopupId.multipleSelection,
      ToolbarPopupId.alignment,
    ].contains(EditModel().toolbarPopupId)) {
      return;
    }

    /// Patternをクリックのとき、空白の領域をクリックすることはありません
    if (_patternOpacityClickedMap.isNotEmpty ||
        _patternAllAreaClickedMap.isNotEmpty) {
      return;
    }

    /// Model更新
    PatternModel().notPatternAreaSelectWork();
    BorderModel()
      ..resetMarkSelectIndex()
      ..resetDivideSelectedIndex();

    /// View更新
    update();

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.preview);
  }

  @override
  void onPreviewDisableClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.invalid);
  }

  ///
  /// ドラッグモードになっているかどうか
  ///
  bool _isDaring = false;

  //////////////////////////////////////////////////////////////////
  ///
  /// ↓ ↓ ↓ ↓ ↓ ↓ ↓ パターンの移動に関連するデータと関数  ↓ ↓ ↓ ↓ ↓ ↓ ↓
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// 移動開始時のバックアップの模様の表示情報
  ///
  List<PatternDisplayInfo> _patternMoveDispInfoListBackup = [];

  ///
  /// 移動開始時のバックアップされた模様の最大範囲
  ///
  Rect _maxRectSizeBackup = Rect.largest;

  ///
  /// 移動開始時のバックアップされた赤い点の表示情報
  ///
  RedPointDisplayInfo? _redPointInfoBackup;

  ///
  /// ドラッグが開始される位置
  ///
  Offset _startMovePoint = const Offset(0, 0);

  ///
  /// 選択したすべての模様のX座標とY座標のリスト
  ///
  final List<double> _xList = [];
  final List<double> _yList = [];

  @override
  void backupPatternDisplayInfoInMove() {
    _patternMoveDispInfoListBackup.clear();
    _patternMoveDispInfoListBackup = List.from(state.patternDisplayInfoList);
    _maxRectSizeBackup = _getMaxRectSize();
    _redPointInfoBackup = state.redPointInfo;
  }

  @override
  void dargPatternStart(ScaleStartDetails details) {
    if (_patternDragEnable() == false) {
      return;
    }

    /// Model更新
    BorderModel()
      ..resetMarkSelectIndex()
      ..resetDivideSelectedIndex();

    _startMovePoint = details.localFocalPoint;
    _lastDragPosition = details.localFocalPoint;

    onPatternAllAreaTapUp(isInDargPatternStart: true);

    /// パターンの開始情報をバックアップする
    backupPatternDisplayInfoInMove();

    _isDaring = true;
  }

  @override
  void dargPattern(ScaleUpdateDetails details) {
    if (_patternDragEnable() == false) {
      return;
    }

    if (details.pointerCount > _oneFinger) {
      int step = _getDoubleFingerStep(details.scale);

      /// 変更されていない場合は更新しない
      if (_prevDoubleFingerSizeStep == step) {
        return;
      }

      /// 模様サイズを変更する
      final EmbGroup currentGroup = PatternModel().getCurrentGroup();
      currentGroup.changeSizeEmbGroup(
          type: MagType.xyAll, step: step - _prevDoubleFingerSizeStep);
      _prevDoubleFingerSizeStep = step;

      final Pattern currentPattern =
          PatternModel().getCurrentPattern(curGroupHandle: currentGroup.handle);
      if (currentPattern is EmbBorder) {
        currentPattern
          ..clearGroupInfoCache()
          ..clearBorderCompInfoCache()
          ..clearBorderInfoCache()
          ..clearGroupImageCache();
      } else if (currentPattern is EmbGroup) {
        currentPattern
          ..clearGroupInfoCache()
          ..clearMainImageCache();
      } else {
        /// Do nothing
      }

      /// View更新
      update();

      /// 他のページへの更新の通知
      ref
          .read(patternEditViewModelProvider.notifier)
          .updateEditPageByChild(ModuleType.preview);
    } else {
      final Offset currentPoint = details.localFocalPoint;

      /// 10ドット以下の変化は無視する
      final Offset diffWithDrag = _lastDragPosition - currentPoint;
      if (diffWithDrag.dx.abs() <= (dragDebounceThreshold / getMagnification) &&
          diffWithDrag.dy.abs() <= (dragDebounceThreshold / getMagnification)) {
        return;
      }

      /// 今回は移動の合計量を計算
      Offset moveOffset = currentPoint - _startMovePoint;

      /// プレビュー領域内のパターンの動きを制限します
      final double newLeft = (_maxRectSizeBackup.left + moveOffset.dx).clamp(
        _zoomPreviewRect.left,
        _zoomPreviewRect.right - _maxRectSizeBackup.width,
      );
      final double newTop = (_maxRectSizeBackup.top + moveOffset.dy).clamp(
        _zoomPreviewRect.top,
        _zoomPreviewRect.bottom - _maxRectSizeBackup.height,
      );

      /// パターンの実際の移動量を計算します
      moveOffset = Offset(
          newLeft - _maxRectSizeBackup.left, newTop - _maxRectSizeBackup.top);

      /// APIに必要な水平方向と垂直方向の移動量は、
      /// 実際の移動量に基づいて計算され、移動は0.5mm未満です.5mm舍去
      final int moveTotalLibX =
          (moveOffset.dx * _mmOfOnePixel) ~/ _oneMoveStepInMm;
      final int moveTotalLibY =
          (moveOffset.dy * _mmOfOnePixel) ~/ _oneMoveStepInMm;

      /// UIの実際の移動は、APIに必要な水平方向と垂直方向の移動量によって計算されます
      final double moveXOffset =
          moveTotalLibX * _oneMoveStepInMm * _pixelOfOneMm;
      final double moveYOffset =
          moveTotalLibY * _oneMoveStepInMm * _pixelOfOneMm;
      moveOffset = Offset(moveXOffset, moveYOffset);

      /// Model 更新
      PatternModel()
        ..moveTotalLibX = moveTotalLibX
        ..moveTotalLibY = moveTotalLibY;
      _lastDragPosition = currentPoint;

      /// view更新
      _updateMovedPatternDisplayInfo(moveOffset);

      /// 他のページへの更新の通知
      ref
          .read(patternEditViewModelProvider.notifier)
          .updateEditPageByChild(ModuleType.moveWithDrag);
    }
  }

  @override
  void dargPatternEnd() {
    if (_patternDragEnable() == false) {
      return;
    }

    /// データの初期化
    _prevDoubleFingerSizeStep = 0;
    _startMovePoint = const Offset(0, 0);
    _isDaring = false;

    /// Model更新
    final moveFunction = ref.read(moveFunctionProvider.notifier);
    moveFunction.movePatternByXY(
      PatternModel().moveTotalLibX,
      PatternModel().moveTotalLibY,
      apiType: MoveAPIType.moveEmb,
    );
    PatternModel()
      ..moveTotalLibX = 0
      ..moveTotalLibY = 0;
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.preview);

    /// 投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  bool moveByArrowKey() {
    bool isOverFrame = false;

    /// modelの使用回数を減らすために必要な値を定義します
    int moveTotalLibX = PatternModel().moveTotalLibX;
    int moveTotalLibY = PatternModel().moveTotalLibY;

    /// 今回は移動の合計量を計算
    double moveXOffset = moveTotalLibX * _oneMoveStepInMm * _pixelOfOneMm;
    double moveYOffset = moveTotalLibY * _oneMoveStepInMm * _pixelOfOneMm;

    /// 新しい頂点座標は、移動量から計算されます
    double newLeft = _maxRectSizeBackup.left + moveXOffset;
    double newTop = _maxRectSizeBackup.top + moveYOffset;
    double newRight = newLeft + _maxRectSizeBackup.width;
    double newBottom = newTop + _maxRectSizeBackup.height;

    /// 右の境界か左の境界を越える
    if (newRight > _zoomPreviewRect.right || newLeft < _zoomPreviewRect.left) {
      isOverFrame = true;

      newLeft = newLeft.clamp(_zoomPreviewRect.left,
          _zoomPreviewRect.right - _maxRectSizeBackup.width);

      double newMoveXOffset = newLeft - _maxRectSizeBackup.left;
      moveTotalLibX = (newMoveXOffset * _mmOfOnePixel) ~/ _oneMoveStepInMm;

      /// 左上、左下、右上、右下に移動すると、X方向とY方向は同じ量だけ移動します
      if (moveTotalLibY != 0) {
        moveTotalLibY =
            moveTotalLibY > 0 ? moveTotalLibX.abs() : -moveTotalLibX.abs();
      } else {
        /// Do nothing
      }
    } else if (newBottom > _zoomPreviewRect.bottom ||
        newTop < _zoomPreviewRect.top) {
      isOverFrame = true;

      /// 上の境界か下の境界を越える
      newTop = newTop.clamp(_zoomPreviewRect.top,
          _zoomPreviewRect.bottom - _maxRectSizeBackup.height);

      double newMoveYOffset = newTop - _maxRectSizeBackup.top;
      moveTotalLibY = (newMoveYOffset * _mmOfOnePixel) ~/ _oneMoveStepInMm;

      /// 左上、左下、右上、右下に移動すると、X方向とY方向は同じ量だけ移動します
      if (moveTotalLibX != 0) {
        moveTotalLibX =
            moveTotalLibX > 0 ? moveTotalLibY.abs() : -moveTotalLibY.abs();
      } else {
        /// Do nothing
      }
    }

    moveXOffset = moveTotalLibX * _oneMoveStepInMm * _pixelOfOneMm;
    moveYOffset = moveTotalLibY * _oneMoveStepInMm * _pixelOfOneMm;
    Offset moveOffset = Offset(moveXOffset, moveYOffset);

    /// Model更新
    PatternModel()
      ..moveTotalLibX = moveTotalLibX
      ..moveTotalLibY = moveTotalLibY;

    /// view更新
    _updateMovedPatternDisplayInfo(moveOffset);

    return isOverFrame;
  }

  ///
  /// 選択したすべての模様の最大四角形範囲を取得します
  ///
  Rect _getMaxRectSize() {
    _xList.clear();
    _yList.clear();

    List<PatternDisplayInfo> patternList =
        List.from(_patternMoveDispInfoListBackup);
    List<PatternDisplayInfo> selectedPatternInfoList = [];

    /// 現在のパターンを現在の画面内で移動できるかどうか
    final bool isCanMoveCurrentPattern = [
          ToolbarPopupId.multipleSelection,
          ToolbarPopupId.alignment,
        ].contains(EditModel().toolbarPopupId) ==
        false;

    for (var element in patternList) {
      if (element.isSelected ||
          (isCanMoveCurrentPattern && element.isCurrentPattern)) {
        selectedPatternInfoList.add(element);
      }
    }

    /// 模様が選択されている場合
    if (selectedPatternInfoList.length == 1) {
      PatternDisplayInfo patternInfo = selectedPatternInfoList.first;
      EmbBorderDisplayInfo borderInfo = patternInfo.borderDisplayInfoList.first;
      EmbGroupDisplayInfo groupInfo = borderInfo.groupDisplayInfoList.first;
      EmbPatternDisplayInfo embPatternInfo =
          groupInfo.embPatternDisplayInfoList.first;

      if (groupInfo.isFont) {
        /// 文字模様
        Offset topLeft = patternInfo.maskDisplayInfo.topLeft;
        Offset bottomRight = patternInfo.maskDisplayInfo.bottomRight;

        _xList.add(topLeft.dx);
        _yList.add(topLeft.dy);
        _xList.add(bottomRight.dx);
        _yList.add(bottomRight.dy);
      } else {
        if (patternInfo.isBorder) {
          /// 組み合わせ模様
          Offset topLeft = patternInfo.maskDisplayInfo.topLeft;
          Offset bottomRight = patternInfo.maskDisplayInfo.bottomRight;

          _xList.add(topLeft.dx);
          _yList.add(topLeft.dy);
          _xList.add(bottomRight.dx);
          _yList.add(bottomRight.dy);
        } else {
          double top = groupInfo.top + borderInfo.top + patternInfo.top;
          double left = groupInfo.left + borderInfo.left + patternInfo.left;

          double imageTop = embPatternInfo.imageTop + top;
          double imageLeft = embPatternInfo.imageLeft + left;
          double imageBottom = imageTop + embPatternInfo.imageHeight;
          double imageRight = imageLeft + embPatternInfo.imageWidth;

          _xList.add(imageLeft);
          _yList.add(imageTop);
          _xList.add(imageRight);
          _yList.add(imageBottom);
        }
      }
    } else if (selectedPatternInfoList.length > 1) {
      /// 複数の模様を選択した場合

      List<double> patternItemXList = [];
      List<double> patternItemYList = [];

      for (var pattern in selectedPatternInfoList) {
        if (pattern
            .borderDisplayInfoList.first.groupDisplayInfoList.first.isFont) {
          /// 文字模様
          Offset topLeft = pattern.maskDisplayInfo.topLeft;
          Offset bottomRight = pattern.maskDisplayInfo.bottomRight;

          _xList.add(topLeft.dx);
          _yList.add(topLeft.dy);
          _xList.add(bottomRight.dx);
          _yList.add(bottomRight.dy);
        } else {
          /// 文字以外の模様
          Offset patternTopLeft = pattern.topLeft;
          for (var border in pattern.borderDisplayInfoList) {
            Offset borderTopLeft = border.topLeft;
            for (var group in border.groupDisplayInfoList) {
              Offset groupTopLeft = group.topLeft;
              for (var embPattern in group.embPatternDisplayInfoList) {
                /// ページ上の相対位置オフセットの回復します
                Offset topLeft = groupTopLeft + borderTopLeft + patternTopLeft;
                double imageTop = embPattern.imageTop + topLeft.dy;
                double imageLeft = embPattern.imageLeft + topLeft.dx;
                double imageBottom = imageTop + embPattern.imageHeight;
                double imageRight = imageLeft + embPattern.imageWidth;

                patternItemXList.add(imageLeft);
                patternItemXList.add(imageRight);
                patternItemYList.add(imageTop);
                patternItemYList.add(imageBottom);
              }
            }
          }

          final (
            topMin: top,
            leftMin: left,
            bottomMax: bottom,
            rightMax: right
          ) = PreviewModel.getMaxOuterWithXYList(
            xList: patternItemXList,
            yList: patternItemYList,
          );

          _xList.add(left);
          _yList.add(top);
          _xList.add(right);
          _yList.add(bottom);
        }
      }
    } else {
      /// Error
    }

    final (
      topMin: topMin,
      leftMin: leftMin,
      bottomMax: bottomMax,
      rightMax: rightMax
    ) = PreviewModel.getMaxOuterWithXYList(xList: _xList, yList: _yList);

    return Rect.fromLTRB(leftMin, topMin, rightMax, bottomMax);
  }

  ///
  /// 移動後に表示データを更新する
  ///
  void _updateMovedPatternDisplayInfo(Offset moveOffset) {
    /// 選択したすべての模様の位置情報を更新します
    List<PatternDisplayInfo> updatedPatternInfoList = [];

    /// 現在のパターンを現在の画面内で移動できるかどうか
    final bool isCanMoveCurrentPattern = [
          ToolbarPopupId.multipleSelection,
          ToolbarPopupId.alignment,
        ].contains(EditModel().toolbarPopupId) ==
        false;

    for (var pattern in _patternMoveDispInfoListBackup) {
      if (pattern.isSelected ||
          (isCanMoveCurrentPattern && pattern.isCurrentPattern)) {
        List<EmbBorderDisplayInfo> borderDisplayInfoList = [];
        for (var border in pattern.borderDisplayInfoList) {
          List<EmbGroupDisplayInfo> groupDisplayInfoList = [];
          for (var group in border.groupDisplayInfoList) {
            groupDisplayInfoList
                .add(group.copyWith(arcImageOffset: moveOffset));
          }
          borderDisplayInfoList.add(
            border.copyWith(
              maskDisplayInfo: Mask(
                topLeft: border.maskDisplayInfo.topLeft + moveOffset,
                topRight: border.maskDisplayInfo.topRight + moveOffset,
                bottomLeft: border.maskDisplayInfo.bottomLeft + moveOffset,
                bottomRight: border.maskDisplayInfo.bottomRight + moveOffset,
              ),
              baseMaskInfo: Mask(
                topLeft: border.baseMaskInfo.topLeft + moveOffset,
                topRight: border.baseMaskInfo.topRight + moveOffset,
                bottomLeft: border.baseMaskInfo.bottomLeft + moveOffset,
                bottomRight: border.baseMaskInfo.bottomRight + moveOffset,
              ),
              groupDisplayInfoList: groupDisplayInfoList,
            ),
          );
        }

        updatedPatternInfoList.add(
          pattern.copyWith(
            left: pattern.left + moveOffset.dx,
            top: pattern.top + moveOffset.dy,
            maskDisplayInfo: Mask(
              topLeft: pattern.maskDisplayInfo.topLeft + moveOffset,
              topRight: pattern.maskDisplayInfo.topRight + moveOffset,
              bottomLeft: pattern.maskDisplayInfo.bottomLeft + moveOffset,
              bottomRight: pattern.maskDisplayInfo.bottomRight + moveOffset,
            ),
            borderDisplayInfoList: borderDisplayInfoList,
          ),
        );
      } else {
        updatedPatternInfoList.add(pattern);
      }
    }

    /// 赤いドットは最後のパターンにのみ表示されます
    final Offset patternTopLeft = Offset(
      updatedPatternInfoList.last.left,
      updatedPatternInfoList.last.top,
    );
    final Offset patternBottomRight = Offset(
      updatedPatternInfoList.last.left + updatedPatternInfoList.last.width,
      updatedPatternInfoList.last.top + updatedPatternInfoList.last.height,
    );

    /// 分割線 と ナイフ
    final (cutLinePath, knifeDisplayWidget) =
        _getCutLineAndKnife(updatedPatternInfoList.last);

    /// 赤い点の表示情報を更新します
    RedPointDisplayInfo? redPointInfo;
    if (_redPointInfoBackup != null) {
      redPointInfo = _redPointInfoBackup!.copyWith(
        topLeft: _redPointInfoBackup!.topLeft + moveOffset,
      );
    } else {
      /// Don nothing
    }

    /// view更新
    state = state.copyWith(
      patternDisplayInfoList: updatedPatternInfoList,
      isRedPointOverPreview: _isRedPointOverPreview(
        viewCenter: _viewCenter,
        topLeft: patternTopLeft,
        bottomRight: patternBottomRight,
      ),
      threadMarkInfoList: PreviewModel.getThreadMarkInfoList(
          centerPoint: _originalCenter,
          pixelOfOneMm: _pixelOfOneMm,
          patternDisplayInfoList: updatedPatternInfoList),
      cutLinePath: cutLinePath,
      knifeDisplayWidget: knifeDisplayWidget,
      redPointInfo: redPointInfo,
      borderMarkMaskRect: _getThreadMarkMaskRect(updatedPatternInfoList.last),
    );
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// ↑ ↑ ↑ ↑ ↑ ↑ ↑ パターンの移動に関連するデータと関数  ↑ ↑ ↑ ↑ ↑ ↑ ↑
  ///
  //////////////////////////////////////////////////////////////////

  /// マスクの新しい位置を計算するために使用される回転の中心
  Offset _maskRotateCenter = const Offset(0, 0);

  /// 回転前のバックアップデータ。
  List<PatternDisplayInfo> _patternDisplayInfoListBack = [];

  @override
  void backupPatternDisplayInfoInRotate() {
    /// 回転前のすべての模様の情報をバックアップします。
    _patternDisplayInfoListBack.clear();
    _patternDisplayInfoListBack = [];
    _patternDisplayInfoListBack = List.from(state.patternDisplayInfoList);

    /// 現在の回転模様を見つけます「回転の模様は現在の模様であり、現在の模様は最も表層に表示されるため、最後のものを使用すればよい。」
    PatternDisplayInfo patternDisplayInfo =
        _patternDisplayInfoListBack.last.copyWith();

    /// 先頭にあるBaseBorderのMask情報を取り出します。
    EmbBorderDisplayInfo baseBorderDisplayInfo =
        patternDisplayInfo.borderDisplayInfoList.first.copyWith();

    /// PatternとBorderの左上隅
    Offset patternTopLeft = patternDisplayInfo.topLeft;
    Offset borderTopLeft = baseBorderDisplayInfo.topLeft;
    List<Offset> embPatternMaskPointList = [];

    /// すべてのembPatternのMask位置が取得します
    for (var groupDisplayInfo in baseBorderDisplayInfo.groupDisplayInfoList) {
      /// Groupの左上隅
      Offset groupTopLeft = groupDisplayInfo.topLeft;
      for (var embPatternDisplayInfo
          in groupDisplayInfo.embPatternDisplayInfoList) {
        /// ページ上の相対位置オフセットの回復します
        Offset topLeft = embPatternDisplayInfo.topLeft +
            groupTopLeft +
            borderTopLeft +
            patternTopLeft;
        embPatternMaskPointList.addAll([
          embPatternDisplayInfo.mask.topLeft + topLeft,
          embPatternDisplayInfo.mask.topRight + topLeft,
          embPatternDisplayInfo.mask.bottomLeft + topLeft,
          embPatternDisplayInfo.mask.bottomRight + topLeft
        ]);
      }
    }

    /// BaseBorderの回転中心を計算します。
    _maskRotateCenter = _calculateCenter(embPatternMaskPointList);
  }

  @override
  void dargRotatePointStart(Offset center, double angle, Offset startPoint) {
    _rotatePointCenter = center;
    _startAngle = angle;
    _isUsingRedPoint = true;
    _isDaring = true;
    _lastDragPosition = startPoint;

    /// View更新
    state = state.copyWith(
      maskColor: const Color.fromARGB(255, 48, 48, 160),
    );
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// Rotateの前に、Rotate中にUIで使用する必要があるデータをバックアップします
    backupPatternDisplayInfoInRotate();
  }

  @override
  void dargRotatePoint(Offset currentPoint) {
    /// 10ドット以下の変化は無視する
    final Offset diffWithDrag = _lastDragPosition - currentPoint;
    if (diffWithDrag.dx.abs() <= (dragDebounceThreshold / getMagnification) &&
        diffWithDrag.dy.abs() <= (dragDebounceThreshold / getMagnification)) {
      return;
    }

    final Offset diffWithCenter = currentPoint - _rotatePointCenter;

    /// 第1象限を0~90°に、正のX軸を0°に、正のY軸を90°に設定します
    final double baseAngle =
        atan2(diffWithCenter.dy, diffWithCenter.dx) * _angle180 / pi + _angle90;

    /// 回転が始まる角度に0°をリダイレクトし、角度の値が0~360の間であることを確認します
    final double currentAngle = (baseAngle - _startAngle) % _angle360;

    /// 1°旋转
    if (state.dragAngle == currentAngle.toInt()) {
      return;
    } else {
      _lastDragPosition = currentPoint;
      PreviewModel().uiRotateAngle = currentAngle.ceilToDouble();
      updateByRotateLongPress();
      return;
    }
  }

  @override
  void dargRotatePointEnd() {
    /// 角度の値を Lib 使用量の値に変換します。
    int degree = (state.dragAngle ?? 0).toInt() * conversionRate;

    /// リセット
    _startAngle = 0;
    _isDaring = false;
    _isUsingRedPoint = false;
    _rotatePointCenter = const Offset(0, 0);
    PreviewModel().uiRotateAngle = 0;

    /// Lib通知
    if (PatternModel().rotateEmb(degree) == EmbLibraryError.EMB_NO_ERR) {
      /// Model更新
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      /// View更新
      update();

      /// 他のページへの更新の通知
      ref
          .read(patternEditViewModelProvider.notifier)
          .updateEditPageByChild(ModuleType.preview);

      /// プロジェクト投影画面を更新する
      final embProjectorFunction =
          ref.read(embProjectorFunctionProvider.notifier);
      embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
    } else {
      /// Do nothing
    }
  }

  @override
  void dargSizePointStart(Offset centerPoint) {
    _rotatePointCenter = centerPoint;
    _isUsingRedPoint = true;
    _isDaring = true;

    /// View更新
    state = state.copyWith(
      maskColor: const Color.fromARGB(255, 48, 48, 160),
    );
    SystemSoundPlayer().play(SystemSoundEnum.accept);
  }

  @override
  void dargSizePoint(Offset offset, PointPositionType type) {
    /// 累積移動量
    _sizeRedPointDragCount += offset;

    int step = _getSizeStep(type, _sizeRedPointDragCount);
    MagType magType = _changePointTypeToMagType(type);

    /// 移動量リセット
    if (step != 0) {
      _sizeRedPointDragCount = const Offset(0, 0);
    } else {
      return;
    }

    /// 模様サイズを変更する
    final EmbGroup currentGroup = PatternModel().getCurrentGroup();
    currentGroup.changeSizeEmbGroup(type: magType, step: step);

    final Pattern currentPattern =
        PatternModel().getCurrentPattern(curGroupHandle: currentGroup.handle);
    if (currentPattern is EmbBorder) {
      currentPattern
        ..clearGroupInfoCache()
        ..clearBorderCompInfoCache()
        ..clearBorderInfoCache()
        ..clearGroupImageCache();
    } else if (currentPattern is EmbGroup) {
      currentPattern
        ..clearGroupInfoCache()
        ..clearMainImageCache();
    } else {
      /// Do nothing
    }

    /// View更新
    update();

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.preview);
  }

  @override
  void dargSizePointEnd() {
    _sizeRedPointDragCount = const Offset(0, 0);
    _rotatePointCenter = const Offset(0, 0);
    _isUsingRedPoint = false;
    _isDaring = false;

    /// Model更新
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }

    PatternModel().clearCurrentPatternImageCache();

    /// View更新
    update();

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.preview);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
    SystemSoundPlayer().play(SystemSoundEnum.accept);
  }

  @override
  List<double> getGridVerticalList() {
    List<double> verticalList = [];
    double xOffset = _originalCenter.dx;
    int mmValue = state.gridTypeIndex == EmbGridType.embGridGridLine10.index
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    /// 中心点の左端の線の位置を計算するには
    xOffset -= _pixelOfOneMm * mmValue;
    while (xOffset >= 0) {
      verticalList.add(xOffset);
      xOffset -= _pixelOfOneMm * mmValue;
    }

    xOffset = _originalCenter.dx;

    /// 中心点の右端の線の位置を計算するには
    xOffset += _pixelOfOneMm * mmValue;
    while (xOffset <= embPreviewSizeDot.dx) {
      verticalList.add(xOffset);
      xOffset += _pixelOfOneMm * mmValue;
    }

    return verticalList;
  }

  @override
  List<double> getGridHorizontalList() {
    List<double> horizontalList = [];
    double yOffset = _originalCenter.dy;
    int mmValue = state.gridTypeIndex == EmbGridType.embGridGridLine10.index
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    /// 中心点上端の線の位置を計算するには
    yOffset -= _pixelOfOneMm * mmValue;
    while (yOffset >= 0) {
      horizontalList.add(yOffset);
      yOffset -= _pixelOfOneMm * mmValue;
    }

    yOffset = _originalCenter.dy;

    /// 中心点の下端の線の位置を計算するには
    yOffset += _pixelOfOneMm * mmValue;
    while (yOffset <= embPreviewSizeDot.dy) {
      horizontalList.add(yOffset);
      yOffset += _pixelOfOneMm * mmValue;
    }

    return horizontalList;
  }

  @override
  void useRectangularMarqueeStart(Offset startPoint) {
    _rectStartDargPoint = startPoint;
    _rectEndDargPoint = startPoint;
    _isDaring = true;
    _rectTouchState = null;
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// Model更新
    _changePatternSelectedStateForRect();

    /// View更新
    _updateInUseRectangularMarquee();

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.multipleSelectionPreview);
  }

  @override
  void useRectangularMarquee(Offset point) {
    _rectEndDargPoint = Offset(
      point.dx.clamp(0, embPreviewSizeDot.dx),
      point.dy.clamp(0, embPreviewSizeDot.dy),
    );

    /// Model更新
    _changePatternSelectedStateForRect();

    /// View更新
    _updateInUseRectangularMarquee();

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.multipleSelectionPreview);
  }

  @override
  void useRectangularMarqueeEnd() {
    _rectStartDargPoint = const Offset(0, 0);
    _rectEndDargPoint = const Offset(0, 0);
    _rectTouchState = null;
    _isDaring = false;
    _boxedPatternIndexList.clear();

    /// View更新
    state = state.copyWith(rectangularMarqueeArea: null);
  }

  ///
  /// 複数選択画面とAlignment画面のプレビューをドラッグアンドドロップしてパターンを選択するとき、専用のページ更新関数。
  ///
  void _updateInUseRectangularMarquee() {
    final patternDisplayInfoList = _getPatternDisplayInfoList();

    /// view更新
    state = state.copyWith(
      patternDisplayInfoList: patternDisplayInfoList,
      rectangularMarqueeArea:
          Rect.fromPoints(_rectStartDargPoint, _rectEndDargPoint),
    );
  }

  @override
  void dargPreviewStart(ScaleStartDetails details) {
    _previewStartDargPoint = details.localFocalPoint;
    _selectedZoomScaleBack = PatternModel().selectedZoomScaleInEditPage;
  }

  @override
  void dargPreview(ScaleUpdateDetails details) {
    if (details.pointerCount > _oneFinger) {
      if (PreviewModel().isInDragPreviewMode == false) {
        return;
      }

      int index = zoomList.indexOf(_selectedZoomScaleBack);
      int step = _getDoubleFingerStep(details.scale);
      index = (index + step).clamp(0, zoomList.length - 1);

      /// 必要でない場合は更新しない
      if (PatternModel().selectedZoomScaleInEditPage != zoomList[index]) {
        /// Model更新
        PatternModel().selectedZoomScaleInEditPage = zoomList[index];

        /// View更新
        update();

        /// 他のページへの更新の通知
        ref
            .read(patternEditViewModelProvider.notifier)
            .updateEditPageByChild(ModuleType.preview);
      } else {
        /// Do noting
      }
    } else {
      Offset offset = details.localFocalPoint - _previewStartDargPoint;
      Offset zoomOutPoint = state.zoomOutPoint - offset;

      /// 新しい移動開始点の設定
      _previewStartDargPoint = details.localFocalPoint;

      /// 範囲外の制御
      zoomOutPoint = Offset(
          zoomOutPoint.dx
              .clamp(-embPreviewSizeDot.dx / 2, embPreviewSizeDot.dx / 2),
          zoomOutPoint.dy
              .clamp(-embPreviewSizeDot.dy / 2, embPreviewSizeDot.dy / 2));

      /// 拡大された現在のPreviewの中心点をオフセット
      offset = -offset;
      _viewCenter += offset - (offset / getMagnification);

      /// 分割線 と ナイフ
      final (cutLinePath, knifeDisplayWidget) =
          _getCutLineAndKnife(state.patternDisplayInfoList.last);

      /// View更新
      state = state.copyWith(
        zoomOutPoint: zoomOutPoint,
        cutLinePath: cutLinePath,
        knifeDisplayWidget: knifeDisplayWidget,
      );
    }
  }

  @override
  void updateByRotateLongPress() {
    double currentAngle = PreviewModel().uiRotateAngle;
    late PatternDisplayInfo newPatternDisplayInfo;

    for (int index = 0; index < _angle360; index++) {
      /// 「回転の模様は現在の模様であり、現在の模様は最も表層に表示されるため、最後のものを使用すればよい。」
      newPatternDisplayInfo = PreviewModel().getNewPatternDisplayInfoInRotate(
          angle: currentAngle,
          patternDisplayInfo: _patternDisplayInfoListBack.last.copyWith(),
          pixelOfOneMm: _pixelOfOneMm,
          rotateCenter: _maskRotateCenter);
      if (_zoomPreviewRect
                  .contains(newPatternDisplayInfo.maskDisplayInfo.topLeft) ==
              false ||
          _zoomPreviewRect.contains(
                  newPatternDisplayInfo.maskDisplayInfo.bottomRight) ==
              false) {
        /// 使用する赤い点が回転し続けると、対応する角度に回転しても更新されません。
        if (_isUsingRedPoint == true) {
          return;
        } else {
          /// それ以外の場合は、ボタンを押し続けて次の移動角度を計算する必要があります。
          /// 回転方向を確認します。
          currentAngle = PreviewModel().isRotatingForward
              ? (currentAngle + 1) % _angle360
              : (currentAngle - 1) % _angle360;
          continue;
        }
      } else {
        break;
      }
    }

    /// Model更新
    PreviewModel().uiRotateAngle = currentAngle;

    /// 新しいPatternをリストに書き込み、ここでページを更新します
    List<PatternDisplayInfo> patternDisplayInfoList =
        List.from(_patternDisplayInfoListBack);
    patternDisplayInfoList.removeLast();
    patternDisplayInfoList.add(newPatternDisplayInfo);

    /// View更新
    state = state.copyWith(
      dragAngle: currentAngle,
      patternDisplayInfoList: patternDisplayInfoList,
      threadMarkInfoList: PreviewModel.getThreadMarkInfoList(
          centerPoint: _originalCenter,
          pixelOfOneMm: _pixelOfOneMm,
          patternDisplayInfoList: patternDisplayInfoList),
    );

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.rotateWithRedPoint);
  }

  ///
  /// クリックされた模様のリスト(透明領域と非透明領域を含む)
  ///
  final Map<int, int> _patternAllAreaClickedMap = {};

  ///
  /// クリックされた模様のリスト(非透明領域)
  ///
  final Map<int, int> _patternOpacityClickedMap = {};

  @override
  void onPatternNoOpacityAreaClicked({
    required int displayIndex,
    required int sewingIndex,
  }) {
    final planet = <int, int>{displayIndex: sewingIndex};
    _patternOpacityClickedMap.addEntries(planet.entries);
  }

  @override
  void onPatternAllAreaTapDown({
    required int displayIndex,
    required int sewingIndex,
  }) {
    final planet = <int, int>{displayIndex: sewingIndex};
    _patternAllAreaClickedMap.addEntries(planet.entries);
  }

  @override
  void onPatternAllAreaTapUp({bool isInDargPatternStart = false}) {
    if (_isDaring == true) {
      _patternAllAreaClickedMap.clear();
      _patternOpacityClickedMap.clear();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 不透明な領域にヒットした場合は、不透明なリストで見た目を探す
    if (_patternOpacityClickedMap.isNotEmpty) {
      /// クリックされた一番上の模様を探す
      int maxDisplayIndex = -1;
      _patternOpacityClickedMap.forEach((displayIndex, sewingIndex) {
        if (maxDisplayIndex < displayIndex) {
          maxDisplayIndex = displayIndex;
        }
      });

      /// 指定する模様を選択
      _onPatternClicked(
        displayIndex: maxDisplayIndex,
        sewingIndex: _patternOpacityClickedMap[maxDisplayIndex]!,
        isInDargPatternStart: isInDargPatternStart,
      );
    } else if (_patternAllAreaClickedMap.isNotEmpty) {
      /// クリックされた一番上の模様を探す
      int maxDisplayIndex = -1;
      _patternAllAreaClickedMap.forEach((displayIndex, sewingIndex) {
        if (maxDisplayIndex < displayIndex) {
          maxDisplayIndex = displayIndex;
        }
      });

      /// 指定する模様を選択
      _onPatternClicked(
        displayIndex: maxDisplayIndex,
        sewingIndex: _patternAllAreaClickedMap[maxDisplayIndex]!,
        isInDargPatternStart: isInDargPatternStart,
      );
    }

    /// クリックされたスタイルのリストをクリーンアップします
    _patternAllAreaClickedMap.clear();
    _patternOpacityClickedMap.clear();
  }

  ///
  /// 指定する模様を選択
  ///
  void _onPatternClicked(
      {required int sewingIndex,
      required int displayIndex,
      bool isInDargPatternStart = false}) {
    if (PreviewModel().isInDragPreviewMode == true) {
      return;
    }

    /// Model更新
    BorderModel()
      ..resetMarkSelectIndex()
      ..resetDivideSelectedIndex();

    /// 模様を選ぶ
    if ([
      ToolbarPopupId.multipleSelection,
      ToolbarPopupId.alignment,
    ].contains(EditModel().toolbarPopupId)) {
      /// 複数選択画面のクリック
      PatternModel().selectPatternInMultiplePage(sewingIndex);

      /// プロジェクト投影画面を更新する
      final embProjectorFunction =
          ref.read(embProjectorFunctionProvider.notifier);
      embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
    } else {
      if (state.patternDisplayInfoList[displayIndex].borderDisplayInfoList.first
              .isSelected ==
          true) {
        /// 他のページへの更新の通知
        if (isInDargPatternStart) {
          /// Do nothing
        } else {
          ref
              .read(patternEditViewModelProvider.notifier)
              .updateEditPageByChild(ModuleType.preview);
        }

        return;
      } else {
        PatternModel().selectPatternWithSewingIndex(sewingIndex);

        /// Borderページでは、模様を切り替えてBorderInfoを更新します
        if (ToolbarPopupId.border == EditModel().toolbarPopupId) {
          BorderModel().getBorderInfo();
        } else {
          /// Do noting
        }

        /// プロジェクト投影画面を更新する
        final embProjectorFunction =
            ref.read(embProjectorFunctionProvider.notifier);
        embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
      }
    }

    /// View更新
    update();

    /// 他のページへの更新の通知
    if (isInDargPatternStart) {
      /// Do nothing
    } else {
      ref
          .read(patternEditViewModelProvider.notifier)
          .updateEditPageByChild(ModuleType.preview);
    }
  }

  ///
  /// 選択した枠のプレビュー表示データを取得します
  ///
  Path _getFrameDrawPath() {
    List<FrameSizeAndArea>? frameSizeAndAreaList = getFrameDisplaySizeAndArea(
        DeviceLibrary().apiBinding.getEmbroideryFrameDisplay().frameDispType);
    assert(frameSizeAndAreaList != null, "対応するサイズのボックスが見つかりません");

    Path drawPath = Path();
    Path basePath = Path();
    double magnification = getMagnification;

    if (frameSizeAndAreaList!.length > 1) {
      basePath =
          _get60x20FrameBasePath(frameSizeAndAreaList: frameSizeAndAreaList);
    } else {
      double width = PreviewModel.convertMmToPixels(
          value: frameSizeAndAreaList.first.width, pixelOfOneMm: _pixelOfOneMm);
      double height = PreviewModel.convertMmToPixels(
          value: frameSizeAndAreaList.first.height,
          pixelOfOneMm: _pixelOfOneMm);
      double startX = PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList.first.left,
              pixelOfOneMm: _pixelOfOneMm) +
          _originalCenter.dx;
      double startY = PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList.first.top,
              pixelOfOneMm: _pixelOfOneMm) +
          _originalCenter.dy;

      Rect rect = Rect.fromLTWH(startX, startY, width, height);
      basePath.moveTo(rect.left, rect.top);
      basePath.lineTo(rect.left, rect.bottom);
      basePath.lineTo(rect.right, rect.bottom);
      basePath.lineTo(rect.right, rect.top);
      basePath.lineTo(rect.left, rect.top);
    }

    /// フレームの破線の一段の長さ
    double dashWidth = _dashedLineLength / magnification;
    double dashSpace = _dashedLineSpace / magnification;

    /// 描画Pathを計算する
    double distance = 0.0;
    for (PathMetric pathMetric in basePath.computeMetrics()) {
      while (distance < pathMetric.length) {
        drawPath.addPath(
          pathMetric.extractPath(distance, distance + dashWidth),
          Offset.zero,
        );
        distance += dashWidth;
        distance += dashSpace;
      }
    }
    return drawPath;
  }

  ///
  /// 60*20枠のプレビューにはデータが表示されま
  ///
  Path _get60x20FrameBasePath(
      {required List<FrameSizeAndArea> frameSizeAndAreaList}) {
    Path basePath = Path();

    /// List順：60*20 mm、50*30 mm、30*40 mm
    List<Rect> rectList = List.generate(
      frameSizeAndAreaList.length,
      (index) => Rect.fromLTWH(
          PreviewModel.convertMmToPixels(
                  value: frameSizeAndAreaList[index].left,
                  pixelOfOneMm: _pixelOfOneMm) +
              _originalCenter.dx,
          PreviewModel.convertMmToPixels(
                  value: frameSizeAndAreaList[index].top,
                  pixelOfOneMm: _pixelOfOneMm) +
              _originalCenter.dy,
          PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList[index].width,
              pixelOfOneMm: _pixelOfOneMm),
          PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList[index].height,
              pixelOfOneMm: _pixelOfOneMm)),
    );

    /// 1番目と2番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*40 mm
    rectList.insert(
      1,
      Rect.fromPoints(
        Offset(rectList[1].left, rectList.first.top),
        Offset(rectList[1].right, rectList.first.bottom),
      ),
    );

    /// 2番目と3番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*30 mm、30*40 mm
    rectList.insert(
      3,
      Rect.fromPoints(
        Offset(rectList.last.left, rectList[2].top),
        Offset(rectList.last.right, rectList[2].bottom),
      ),
    );

    /// 60*20 mmの左上点から描画
    basePath.moveTo(rectList.first.left, rectList.first.top);

    /// Rectの左上点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].left, rectList[index].top);
    }

    /// Rectの右上点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].right, rectList[index].top);
    }

    /// Rectの右下点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].right, rectList[index].bottom);
    }

    /// Rectの左下点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].left, rectList[index].bottom);
    }

    /// 接続開始点
    basePath.lineTo(rectList.first.left, rectList.first.top);

    return basePath;
  }

  ///
  /// 黒点行列の表示データを取得します
  ///
  List<Offset> _getBlackPoints() {
    double space = _blackPointsSpace / getMagnification;
    List<Offset> points = [];

    /// 黒点行列は以下の場合には表示しない
    if (PatternModel().selectedZoomScaleInEditPage == zoomList.first ||
        [
          EmbGridType.embGridCenterLine,
          EmbGridType.embGridGridLine10,
          EmbGridType.embGridGridLine_25
        ].contains(DeviceLibrary().apiBinding.getGrid())) {
      return points;
    } else {
      for (double y = space; y < embPreviewSizeDot.dy; y += space) {
        for (double x = space; x < embPreviewSizeDot.dx; x += space) {
          points.add(Offset(x, y));
        }
      }
    }

    return points;
  }

  ///
  /// Viewは最初にPattern情報変換を使用する
  ///
  List<PatternDisplayInfo> _getPatternDisplayInfoList() {
    List<PatternDisplayInfo> patternDisplayInfoList = [];
    const int invalidIndex = -1;
    int surfaceDisplayIndex = invalidIndex;

    List<Pattern> patternList = PatternModel().getAllPattern();
    ToolbarPopupId toolbarPopupId = EditModel().toolbarPopupId;

    /// カレントグループのハンドル
    final currentGroupHandle = PatternModel().getCurrentGroupHandle();

    for (int sewingIndex = 0; sewingIndex < patternList.length; sewingIndex++) {
      Pattern pattern = patternList[sewingIndex];
      if (pattern is EmbGroup) {
        patternDisplayInfoList.add(PreviewModel().getGroupPatternDisplayInfo(
          scrollType: ScrollCenterType.IMAGE_EDITING,
          group: pattern,
          centerPoint: _originalCenter,
          pixelOfOneMm: _pixelOfOneMm,
          sewingIndex: sewingIndex,
          currentGroupHandle: currentGroupHandle,
          zoomScale: PatternModel().selectedZoomScaleInEditPage,
        ));
      } else if (pattern is EmbBorder) {
        patternDisplayInfoList.add(PreviewModel().getBorderPatternDisplayInfo(
          scrollType: ScrollCenterType.IMAGE_EDITING,
          border: pattern,
          centerPoint: _originalCenter,
          pixelOfOneMm: _pixelOfOneMm,
          sewingIndex: sewingIndex,
          currentGroupHandle: currentGroupHandle,
          zoomScale: PatternModel().selectedZoomScaleInEditPage,
        ));
      } else {
        /// Do noting
      }

      /// サーフェスに配置する必要があるインデックスを格納します
      if (patternDisplayInfoList
                  .last.borderDisplayInfoList.first.isCurrentPattern ==
              true &&
          toolbarPopupId != ToolbarPopupId.multipleSelection &&
          toolbarPopupId != ToolbarPopupId.alignment &&
          toolbarPopupId != ToolbarPopupId.order) {
        surfaceDisplayIndex = sewingIndex;
      } else {
        /// Do noting
      }
    }

    /// 選択したパターンをピン留めする
    if (surfaceDisplayIndex != invalidIndex) {
      PatternDisplayInfo surfaceDisplayInfo =
          patternDisplayInfoList[surfaceDisplayIndex];
      patternDisplayInfoList.removeAt(surfaceDisplayIndex);
      patternDisplayInfoList.add(surfaceDisplayInfo);
    } else {
      /// Do noting
    }

    return patternDisplayInfoList;
  }

  ///
  /// 拡大タイプは、赤点の位置によって得られる
  ///
  MagType _changePointTypeToMagType(PointPositionType pointType) {
    MagType magType = MagType.xyAll;
    switch (pointType) {
      case PointPositionType.topCenter:
        magType = MagType.yOnly;
        break;
      case PointPositionType.bottomCenter:
        magType = MagType.yOnly;
        break;
      case PointPositionType.left:
        magType = MagType.xOnly;
        break;
      case PointPositionType.right:
        magType = MagType.xOnly;
        break;
      case PointPositionType.topLeft:
      case PointPositionType.topRight:
      case PointPositionType.bottomLeft:
      case PointPositionType.bottomRight:
      default:
        magType = MagType.xyAll;
        break;
    }

    return magType;
  }

  ///
  /// pixelNumが0.5 mmより大きいかどうかを計算し、0.5 mmの数倍になる
  ///
  int _calculateMultiple(double pixel) {
    int step = 0;
    if (pixel.abs() >= (_mmOfOnePixel * 5)) {
      step = pixel ~/ (_mmOfOnePixel * 5);
    } else {
      /// Do noting
    }
    return step;
  }

  ///
  /// 拡大タイプは、赤点の位置によって得られる
  ///
  int _getSizeStep(PointPositionType pointType, Offset offset) {
    int step = 0;

    switch (pointType) {
      case PointPositionType.topCenter:
        step = -_calculateMultiple(offset.dy);
        break;
      case PointPositionType.bottomCenter:
        step = _calculateMultiple(offset.dy);
        break;
      case PointPositionType.left:
        step = -_calculateMultiple(offset.dx);
        break;
      case PointPositionType.right:
        step = _calculateMultiple(offset.dx);
        break;
      case PointPositionType.topLeft:
        int stepX = -_calculateMultiple(offset.dx);
        int stepY = -_calculateMultiple(offset.dy);
        if (stepX <= 0 && stepY <= 0) {
          step = min(stepX, stepY);
        } else {
          step = max(stepX, stepY);
        }
        break;
      case PointPositionType.topRight:
        int stepX = _calculateMultiple(offset.dx);
        int stepY = -_calculateMultiple(offset.dy);
        if (stepX <= 0 && stepY <= 0) {
          step = min(stepX, stepY);
        } else {
          step = max(stepX, stepY);
        }
        break;
      case PointPositionType.bottomLeft:
        int stepX = -_calculateMultiple(offset.dx);
        int stepY = _calculateMultiple(offset.dy);
        if (stepX <= 0 && stepY <= 0) {
          step = min(stepX, stepY);
        } else {
          step = max(stepX, stepY);
        }
        break;
      case PointPositionType.bottomRight:
      default:
        int stepX = _calculateMultiple(offset.dx);
        int stepY = _calculateMultiple(offset.dy);
        if (stepX <= 0 && stepY <= 0) {
          step = min(stepX, stepY);
        } else {
          step = max(stepX, stepY);
        }
        break;
    }
    return step;
  }

  ///
  /// Patternをドラッグできますか
  ///
  bool _patternDragEnable() {
    if ([
          ToolbarPopupId.multipleSelection,
          ToolbarPopupId.alignment,
          ToolbarPopupId.order,
        ].contains(EditModel().toolbarPopupId) ||
        PreviewModel().isInDragPreviewMode == true ||
        state.isPreviewClickEnable == false) {
      return false;
    }

    return true;
  }

  ///
  /// 矩形選択ツールを使用できるかどうか
  ///
  bool _isRectangularMarqueeEnable() {
    if ([
          ToolbarPopupId.multipleSelection,
          ToolbarPopupId.alignment,
        ].contains(EditModel().toolbarPopupId) &&
        PreviewModel().isInDragPreviewMode == false) {
      return true;
    }

    return false;
  }

  ///
  /// PatternとtargetRectに重複部分があるかどうかを判断する
  ///
  bool _isOverLap(PatternDisplayInfo patternInfo, Rect rect1) {
    /// Patternの範囲の計算
    Offset leftTop = Offset(
      patternInfo.left + maskOutArea,
      patternInfo.top + maskOutArea,
    );
    Offset bottomRight = Offset(
      patternInfo.left + patternInfo.width - maskOutArea,
      patternInfo.top + patternInfo.height - maskOutArea,
    );

    /// Patternを生成する矩形範囲
    Rect rect2 = Rect.fromPoints(leftTop, bottomRight);

    /// PatternとtargetRectに重複部分があるかどうかを判断する
    if ((rect1.left + rect1.width) > rect2.left &&
        (rect2.left + rect2.width) > rect1.left &&
        (rect1.top + rect1.height) > rect2.top &&
        (rect2.top + rect2.height) > rect1.top) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// ボックス選択中にPatternの状態を判断して変更する
  ///
  void _changePatternSelectedStateForRect() {
    Rect rect = Rect.fromPoints(_rectStartDargPoint, _rectEndDargPoint);
    for (int displayIndex = 0;
        displayIndex < state.patternDisplayInfoList.length;
        displayIndex++) {
      PatternDisplayInfo info = state.patternDisplayInfoList[displayIndex];

      /// ボックスがPatternを選択したかどうかを判断する
      if (_isOverLap(info, rect)) {
        /// 最初のボックスをPatternに選択
        if (_rectTouchState == null) {
          _rectTouchState = !info.isSelected;

          /// 最初のボックスで選択されたPattern状態を変更する
          PatternModel().selectPatternInMultiplePage(
              info.borderDisplayInfoList.first.sewingIndex);

          if (_boxedPatternIndexList.contains(displayIndex)) {
            /// Do noting
          } else {
            _boxedPatternIndexList.add(displayIndex);
          }
        } else {
          /// 今回のボックス選択状態を使用してPatternに書き込み
          if (_rectTouchState == info.isSelected) {
            /// Patternの状態は今回のボックス選択の目的の状態と同じであり、patternの状態は変更されない
            /// Do nothing
          } else {
            PatternModel().selectPatternInMultiplePage(
                info.borderDisplayInfoList.first.sewingIndex);
            if (_boxedPatternIndexList.contains(displayIndex)) {
              /// Do noting
            } else {
              _boxedPatternIndexList.add(displayIndex);
            }
          }
        }
      } else {
        /// 矩形選択を解除した場合、矩形選択されたかどうかを判断する
        if (_boxedPatternIndexList.contains(displayIndex)) {
          PatternModel().selectPatternInMultiplePage(
              info.borderDisplayInfoList.first.sewingIndex);
          _boxedPatternIndexList.remove(displayIndex);
        } else {
          /// Do noting
        }
      }
    }
  }

  ///
  /// 赤い点の表示情報を取得します。
  ///
  RedPointDisplayInfo? _getRedPointInfo({
    required PatternDisplayInfo patternInfo,
  }) {
    ToolbarPopupId toolbarPopupId = EditModel().toolbarPopupId;

    /// これらのページ以外に赤い点を表示する必要はありません。
    if ([
          ToolbarPopupId.none,
          ToolbarPopupId.edit,
          ToolbarPopupId.rotate,
          ToolbarPopupId.sizeAdjustment,
        ].contains(toolbarPopupId) ==
        false) {
      return null;
    }

    if (patternInfo.isCurrentPattern == false) {
      return null;
    }

    EmbBorderDisplayInfo borderInfo = patternInfo.borderDisplayInfoList.first;
    EmbGroupDisplayInfo groupInfo = borderInfo.groupDisplayInfoList.first;
    Offset offset =
        patternInfo.topLeft + borderInfo.topLeft + groupInfo.topLeft;
    Offset topLeft = groupInfo.embPatternDisplayInfoList.first.topLeft +
        groupInfo.embPatternDisplayInfoList.first.mask.topLeft;
    Offset topRight = groupInfo.embPatternDisplayInfoList.last.topLeft +
        groupInfo.embPatternDisplayInfoList.last.mask.topRight;
    Offset bottomLeft = groupInfo.embPatternDisplayInfoList.first.topLeft +
        groupInfo.embPatternDisplayInfoList.first.mask.bottomLeft;
    Offset bottomRight = groupInfo.embPatternDisplayInfoList.last.topLeft +
        groupInfo.embPatternDisplayInfoList.last.mask.bottomRight;

    List<RedPointInfo> redPointInfoList = [];

    /// Rotate赤色ドットの描画情報を取得する
    redPointInfoList.addAll(PreviewModel().getRotateRedPointInfo(
      topLeft: topLeft,
      topRight: topRight,
      bottomLeft: bottomLeft,
      bottomRight: bottomRight,
      toolbarPopupId: toolbarPopupId,
      magnification: getMagnification,
      isBorder: patternInfo.isBorder,
    ));

    /// Size赤色ドットの描画情報を取得する
    redPointInfoList.addAll(PreviewModel().getSizeRedPointInfo(
      topLeft: topLeft,
      topRight: topRight,
      bottomLeft: bottomLeft,
      bottomRight: bottomRight,
      toolbarPopupId: toolbarPopupId,
      magnification: getMagnification,
      isBorder: patternInfo.isBorder,
    ));

    /// 赤いドットの追加タッチ領域サイズ
    final double redPointTouchOutAreaInView =
        PreviewModel().getRedPointTouchOutArea(
      topLeft: topLeft,
      topRight: topRight,
      bottomLeft: bottomLeft,
      magnification: getMagnification,
    );

    return RedPointDisplayInfo(
      topLeft: offset -
          Offset(redPointTouchOutAreaInView, redPointTouchOutAreaInView),
      width: groupInfo.width + redPointTouchOutAreaInView * 2,
      height: groupInfo.height + redPointTouchOutAreaInView * 2,
      angle: groupInfo.angle,
      redPointImageSize: redPointImageSize / getMagnification,
      redPointTouchOutArea: redPointTouchOutAreaInView,
      redPointInfos: redPointInfoList,
    );
  }

  ///
  /// 拡大率が変化すると、ZoomOutの拡大点変換
  ///
  Offset _getZoomOutCenterPoint() {
    Offset zoomOutPoint = state.zoomOutPoint;
    int currentZoomScale = PatternModel().selectedZoomScaleInEditPage;
    double magnification = getMagnification;

    /// 拡大率は変化していない
    if (_prevZoomScale == currentZoomScale) {
      /// Do noting
    } else if (currentZoomScale == zoomList.first) {
      /// 拡大率が100%に戻ると、拡大点がリセットされます。
      zoomOutPoint = const Offset(0, 0);
    } else if (currentZoomScale > _prevZoomScale) {
      /// 拡大時、画面の現在の拡大点を探す
      /// 現在位置から（0、0）までの長さ と 新しい拡大点までの長さには比例があります。
      /// 例:400%の場合、拡大点から（0、0）まで、1/4箇所が新しい拡大点です
      zoomOutPoint = zoomOutPoint - (zoomOutPoint / magnification);
    } else {
      /// 縮小時、拡大点は変わらない
      /// Do noting
    }

    /// 拡張されたOffset点をPreviewの座標点に変換する
    /// Offset  左上点（-570/2，-855/2）、右下点（570/2,855/2）
    /// Preview 左上点（  0   ，  0   ）、右下点（570  ,855  ）
    Offset zoomOutPointInPreview = zoomOutPoint + _originalCenter;

    /// 拡大点と初期中心点の距離を計算するには
    Offset distance = _originalCenter - zoomOutPointInPreview;

    /// 新しいView中心点の計算
    /// 拡大点から元の中心点までの距離 と View中心点から元の中心点までの距離 には比例があります。
    /// 上で計算された拡大点の割合と同じです。
    _viewCenter = _originalCenter - (distance - distance / magnification);

    /// バックアップ拡大率
    _prevZoomScale = PatternModel().selectedZoomScaleInEditPage;

    return zoomOutPoint;
  }

  ///
  /// maskボックスがPreview表示領域を超えていないか
  /// 超えた場合、赤いドットは表示されません
  ///
  bool _isRedPointOverPreview({
    required Offset viewCenter,
    required Offset topLeft,
    required Offset bottomRight,
  }) {
    bool isMaskOutPreviewArea = false;
    double magnification = getMagnification;

    /// Maskボックス位置修正（データに赤いドットの範囲が追加されているので、ここで削除）
    topLeft = Offset(
      topLeft.dx + maskOutArea,
      topLeft.dy + maskOutArea,
    );
    bottomRight = Offset(
      bottomRight.dx - maskOutArea,
      bottomRight.dy - maskOutArea,
    );

    /// 現在拡大したPreviewの幅と高さ
    double zoomWidth = embPreviewSizeDot.dx / magnification;
    double zoomHeight = embPreviewSizeDot.dy / magnification;

    /// 現在拡大したPreviewの表示範囲
    Rect zoomPreviewRect = Rect.fromCenter(
      center: viewCenter,
      width: zoomWidth,
      height: zoomHeight,
    );

    /// 範囲判断
    if (topLeft.dx <= zoomPreviewRect.left ||
        topLeft.dy <= zoomPreviewRect.top ||
        bottomRight.dx >= zoomPreviewRect.right ||
        bottomRight.dy >= zoomPreviewRect.bottom) {
      isMaskOutPreviewArea = true;
    } else {
      isMaskOutPreviewArea = false;
    }

    return isMaskOutPreviewArea;
  }

  ///
  /// 二重指拡張時のScale値変換
  /// 拡大時、scale範囲は1より大きく、scale値が大きいほど拡大する(1精度)
  /// 縮小時、scale範囲0 ~ 1、scale値が小さいほど縮小されます(0.1精度)
  ///
  int _getDoubleFingerStep(double scale) {
    int step = 0;
    if (scale > 1) {
      /// 例：Scale=1.2   ===>   Step = 1；
      /// 例：Scale=3.2   ===>   Step = 3；
      step = scale.toInt();
    } else if (scale >= 0 && scale < 1) {
      /// 例：Scale=0.8   ===>   Step = -2；
      /// 例：Scale=0.5   ===>   Step = -5；
      step = -((1 - scale) * 10).toInt();
    } else {
      step = 0;
    }

    return step;
  }

  ///
  /// スキャンした背景画像を取得する
  ///
  Widget? _getScanBackGroundImage() {
    if (ScanModel().hasBackgroundImage == false ||
        ScanModel().getBackgroundShowStatus() == false) {
      return null;
    }

    /// 画像データを解析し、画像を取得する
    final scanBackGroundImage = ScanModel.createSketchesImage();

    if (scanBackGroundImage == null) {
      return null;
    }

    return Opacity(
      opacity: ScanModel().backgroundDensityValue,
      child: Image.memory(scanBackGroundImage),
    );
  }

  ///
  /// 分割線 と ナイフの表示コンポーネントを取得します
  ///
  (Path, Widget?) _getCutLineAndKnife(PatternDisplayInfo patternDisplayInfo) {
    Path cutLinePath = Path();
    Widget? knifeDisplayWidget;

    /// Borderページにない場合はNULL値を返します
    if (ToolbarPopupId.border != EditModel().toolbarPopupId) {
      return (cutLinePath, knifeDisplayWidget);
    }

    if (BorderModel().cutLineDisplay != CutLineDisplay.none) {
      Mask baseMaskInfo =
          patternDisplayInfo.borderDisplayInfoList.first.baseMaskInfo;

      double startPointX = baseMaskInfo.topLeft.dx;
      double startPointY = baseMaskInfo.topLeft.dy;
      double endPointX = startPointX;
      double endPointY = startPointY;

      SSPoint style = patternDisplayInfo.style;
      double spaceX = PreviewModel.convertMmToPixels(
          value: patternDisplayInfo.space.X, pixelOfOneMm: _pixelOfOneMm);
      double spaceY = PreviewModel.convertMmToPixels(
          value: patternDisplayInfo.space.Y, pixelOfOneMm: _pixelOfOneMm);
      double baseMaskHeight =
          baseMaskInfo.bottomLeft.dy - baseMaskInfo.topLeft.dy;
      double baseMaskWidth = baseMaskInfo.topRight.dx - baseMaskInfo.topLeft.dx;

      if (BorderModel().cutLineDisplay == CutLineDisplay.vertical) {
        /// 横方向の切断線
        startPointY = endPointY = startPointY +
            (spaceY + baseMaskHeight) * BorderModel().getDivideSelectedIndex;
        startPointX = startPointX - _cutLineOutMaskSize;
        endPointX = endPointX +
            ((baseMaskWidth + spaceX) * style.X - spaceX) +
            _cutLineOutMaskSize;
      } else {
        /// 縦方向の切断線
        startPointX = endPointX = startPointX +
            (spaceX + baseMaskWidth) * BorderModel().getDivideSelectedIndex;
        startPointY = startPointY - _cutLineOutMaskSize;
        endPointY = endPointY +
            ((baseMaskHeight + spaceY) * style.Y - spaceY) +
            _cutLineOutMaskSize;
      }

      Line cutLine = Line(
        startPoint: Offset(startPointX, startPointY),
        endPoint: Offset(endPointX, endPointY),
      );

      cutLinePath.moveTo(cutLine.startPoint.dx, cutLine.startPoint.dy);
      cutLinePath.lineTo(cutLine.endPoint.dx, cutLine.endPoint.dy);
      knifeDisplayWidget = _getKnifeDisplayWidget(
        startPoint: cutLine.startPoint,
        endPoint: cutLine.endPoint,
        viewCenter: _viewCenter,
        isVertical: BorderModel().cutLineDisplay == CutLineDisplay.vertical,
      );
    } else {
      /// Do noting
    }

    return (cutLinePath, knifeDisplayWidget);
  }

  ///
  /// ナイフの表示コンポーネントを取得します
  ///
  Widget _getKnifeDisplayWidget({
    required Offset startPoint,
    required Offset endPoint,
    required bool isVertical,
    required Offset viewCenter,
  }) {
    double magnification = getMagnification;

    Offset p0 = startPoint;
    Offset p1 = endPoint;
    KnifeDisplayInfo knifeInfo;
    Rect knifeOverArea = Rect.fromLTWH(
      knifeOffsetThresholdHorizontal.toDouble(),
      knifeOffsetThresholdVertical.toDouble(),
      getPreviewSize.dx - knifeOffsetThresholdHorizontal * 2,
      getPreviewSize.dy - knifeOffsetThresholdVertical * 2,
    );

    /// 現在拡大したPreviewの幅と高さ
    double zoomWidth = knifeOverArea.width / magnification;
    double zoomHeight = knifeOverArea.height / magnification;

    /// 現在拡大したPreviewの表示範囲
    knifeOverArea = Rect.fromCenter(
      center: viewCenter,
      width: zoomWidth,
      height: zoomHeight,
    );

    /// 次の判定を判断するために、縦断断時に2点の情報をやり取りします
    if (isVertical == true) {
      p0 = startPoint;
      p1 = endPoint;
    } else {
      p0 = endPoint;
      p1 = startPoint;
    }

    /// ナイフの表示情報の確認
    if (p1.dy < knifeOverArea.top || p1.dx > knifeOverArea.right) {
      if (p0.dx < knifeOverArea.left) {
        knifeInfo = knifeDisplayInfoList[CutKnifeKind.lowerRight.index];
        knifeInfo =
            knifeInfo.copyWith(offset: p0 + knifeInfo.offset / magnification);
      } else if (p0.dy > knifeOverArea.bottom) {
        knifeInfo = knifeDisplayInfoList[CutKnifeKind.upperRight.index];
        knifeInfo =
            knifeInfo.copyWith(offset: p0 + knifeInfo.offset / magnification);
      } else {
        knifeInfo = knifeDisplayInfoList[CutKnifeKind.lowerLeft.index];
        knifeInfo =
            knifeInfo.copyWith(offset: p0 + knifeInfo.offset / magnification);
      }
    } else {
      knifeInfo = knifeDisplayInfoList[CutKnifeKind.upperRight.index];
      knifeInfo =
          knifeInfo.copyWith(offset: p1 + knifeInfo.offset / magnification);
    }

    return Positioned(
      top: knifeInfo.offset.dy,
      left: knifeInfo.offset.dx,
      width: knifeIconWidth / magnification,
      height: knifeIconHeight / magnification,
      child: IgnorePointer(child: knifeInfo.icon),
    );
  }

  ///
  /// Borderのmarkページでは、 maskを取得します
  ///
  Rect? _getThreadMarkMaskRect(PatternDisplayInfo patternDisplayInfo) {
    if (EditModel().toolbarPopupId == ToolbarPopupId.border &&
        BorderModel().borderActionState == BorderActionState.borderMark) {
      int markSelectIndex = BorderModel().getMarkSelectIndex;
      if (markSelectIndex >= patternDisplayInfo.borderDisplayInfoList.length ||
          markSelectIndex < 0) {
        return null;
      }

      Mask mask = patternDisplayInfo
          .borderDisplayInfoList[markSelectIndex].maskDisplayInfo;
      return Rect.fromPoints(
        mask.topLeft,
        mask.bottomRight,
      );
    } else {
      return null;
    }
  }

  ///
  /// すべてのポイントの中心点を計算します
  ///
  Offset _calculateCenter(List<Offset> points) {
    double maxX = double.minPositive;
    double maxY = double.minPositive;
    double minX = double.maxFinite;
    double minY = double.maxFinite;

    for (Offset point in points) {
      maxX = max(point.dx, maxX);
      maxY = max(point.dy, maxY);
      minX = min(point.dx, minX);
      minY = min(point.dy, minY);
    }

    double centerX = (maxX + minX) / 2;
    double centerY = (maxY + minY) / 2;

    return Offset(centerX, centerY);
  }

  ///
  /// データ更新ページをリッスンする
  ///
  ProviderSubscription? _listener;
  void _openListen() {
    _listener = ref.listen(
      fireImmediately: true,
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.moveState),
      (_, nextState) {
        state = state.copyWith(
          isPreviewClickEnable: AppInfoFuncState.getValueByNumber(nextState) ==
              AppInfoFuncState.enable,
        );
      },
    );
  }

  @override
  void dispose() {
    super.dispose();

    /// 状態リッスンの停止
    _listener?.close();
  }
}
