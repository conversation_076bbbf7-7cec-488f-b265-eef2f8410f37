import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'stitch_page_view_model.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'stitch_page_view_interface.freezed.dart';

@freezed
class StitchPageState with _$StitchPageState {
  const factory StitchPageState({
    @Default(true) bool isDisplayBottom,
    @Default(true) bool isDisplayToolBar,
  }) = _StitchPageState;
}

abstract class StitchPageViewInterface extends ViewModel<StitchPageState> {
  StitchPageViewInterface(super.state, this.ref, this.context);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// コンテキスト
  ///
  final BuildContext context;

  ///
  /// 子画面のView更新
  ///
  void updateStitchPageByChild();

  /// 名前付きルートの登録
  ///
  Map<String, PopupRouteBuilder> registerNamedPopup();

  ///
  /// 子画面のView更新
  ///
  void updatePageByChild(ComponentType vm);

  ///
  /// パスを閉じるポップアップ
  ///
  void maybeRemovePopupRoute(String route);

  ///
  /// 指定された名前のポップアップを開く
  ///
  void openPopupRoute(String route, Function update);

  ///
  /// 前のポップアップ画面に戻りできますが
  ///
  bool get hasPop;

  ///
  /// ポップアップが閉じた後にページを更新する
  ///
  void updateRelatedInStitchPage(Function update);
}
