import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'position_offset_setting_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class PositionOffsetSettingPopupState with _$PositionOffsetSettingPopupState {
  const factory PositionOffsetSettingPopupState({
    @Default("") String offsetXValue,
    @Default("") String offsetYValue,
    @Default(false) bool isMinYOffsetValue,
    @Default(false) bool isMaxYOffsetValue,
    @Default(false) bool isMinXOffsetValue,
    @Default(false) bool isMaxXOffsetValue,
  }) = _PositionOffsetSettingPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class PositionOffsetSettingPopupStateViewInterface
    extends ViewModel<PositionOffsetSettingPopupState> {
  PositionOffsetSettingPopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// 「Up」ボタンをクリックする
  ///
  bool onUpButtonClicked(bool isLongPress);

  ///
  /// 「Down」ボタンをクリックする
  ///
  bool onDownButtonClicked(bool isLongPress);

  ///
  /// 「Left」ボタンをクリックする
  ///
  bool onLeftButtonClicked(bool isLongPress);

  ///
  /// 「Right」ボタンをクリックする
  ///
  bool onRightButtonClicked(bool isLongPress);

  ///
  /// 縦方向表示テキストスタイルを取得します
  ///
  bool getOffsetYDefault();

  ///
  /// 横方向表示テキストスタイルを取得します
  ///
  bool getOffsetXDefault();

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
