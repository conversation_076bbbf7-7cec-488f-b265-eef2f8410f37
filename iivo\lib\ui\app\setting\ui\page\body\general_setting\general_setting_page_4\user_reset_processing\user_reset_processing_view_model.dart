import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../../model/general_setting_page4_model.dart';
import '../../../../../page_route.dart';
import 'user_reset_processing_view_interface.dart';

final userResetProcessingViewModelProvider = StateNotifierProvider.autoDispose<
    UserResetProcessingViewModel,
    UserResetProcessingInterface>((ref) => UserResetProcessingViewModel());

class UserResetProcessingViewModel
    extends ViewModel<UserResetProcessingInterface> {
  UserResetProcessingViewModel() : super(const UserResetProcessingInterface()) {
    GeneralSettingPage4Model().userReset().then((_) {
      PagesRoute().pushNamedAndRemoveUntil(
          nextRoute: PageRouteEnum.userResetComplete,
          untilRoute: PageRouteEnum.home);
    });
  }
}
