import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:ui';

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../model/frame_model.dart';
import '../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../model/color_table.dart';
import '../../../../model/device_info_model.dart';
import '../../../../model/paint/clip_board_model.dart';
import '../../../../model/paint/draw_canvas_model.dart';
import '../../../../model/paint/drawing_type_model.dart';
import '../../../../model/paint/edit_object_model.dart';
import '../../../../model/paint/library_isolate.dart';
import '../../../../model/paint/magnification_model.dart';
import '../../../../model/paint/paint_model.dart';
import '../../../../model/paint/pen_model.dart';
import '../../../../model/paint/scan_model.dart';
import '../../../../model/paint/select_model.dart';
import '../../../../model/paint/top_bar_model.dart';
import '../../../../model/resume_history_model.dart';
import '../../../../model/scroll_control_model.dart';
import '../paint_page_view_model.dart';
import 'mdc_canvas_view_interface.dart';

///
/// 操作モード
///
enum Operation {
  none,
  editPen,
  clipPen,
}

enum GestureType {
  penDraw,
  scale,
}

final mdcCanvasViewModelProvider =
    StateNotifierProvider.autoDispose<MDCCanvasViewInterface, MDCCanvasState>(
        (ref) => MDCCanvasViewModel(ref));

class MDCCanvasViewModel extends MDCCanvasViewInterface {
  MDCCanvasViewModel(this._ref) : super(const MDCCanvasState());

  @override
  void build() {
    super.build();

    _isMakingImage = false;
    DrawCanvasModel().initUiDrawingPen();
    DrawCanvasModel().updateFrameAndGridCallback = _updateFrameAndGrid;
    DrawCanvasModel().updateUiImageInfoCallBack = _updateUiImageInfo;

    MagnificationModel().zoomEdit = EditZoom.reset();
    MagnificationModel().zoomInfoSet(MagnificationModel.magnification_100);

    update();

    /// 履歴動作があるときに更新します
    Future(() {
      if (ResumeHistoryModel().isEntryResume()) {
        /// 更新canvas画像
        DrawCanvasModel().updateUiImageInfoCallBack?.call();
      }
    });

    _ref.listen(appDisplaySettingFrameDispTypeProvider.select((value) => value),
        fireImmediately: true, (_, next) {
      DeviceInfoModel().frameType = next;
    });
  }

  ///
  /// タッチ数
  ///
  int _numOfFingers = 0;
  static const _oneFingerTouch = 1;
  static const _noFingerTouch = 0;

  /// 読み出したSizeデータの変換倍率   '読み出したデータの単位(0.1mm) / 10 = mm'
  static const int _conversionRate = 10;

  /// フレームの破線の一段の長さ
  static const double _dashedLineLength = 1.0;

  /// フレームの破線の間隔
  static const double _dashedLineSpace = 1.0;

  /// 単位変換率（mm/dot）
  static const double _ratio = ClipBoardModel.mmToDotRate;

  /// 10mm定義
  static const int _gridLine10MmValue = 10;

  /// 25mm定義
  static const int _gridLine25MmValue = 25;

  ///
  /// 前回ハンドル状態
  ///
  bool _isPreHandleOn = false;

  ///
  /// 部分模様移動状態
  ///
  final _PartMoveInfo _partMoveInfo = _PartMoveInfo(
    isMoving: false,
    startPoint: Offset.zero,
    totalOffset: Offset.zero,
  );

  ///
  /// 部分模様画像ハッシュ値
  ///
  int _previousSketchesHash = -1;

  ///
  /// LibイメージUint8Listからui.Imageに変更中のフラグ
  ///
  bool _isMakingImage = false;

  ///
  /// キャンバス幅
  ///
  @override
  double get canvasWidth => PenModel.canvasWidth;

  ///
  /// キャンバスの高さ
  ///
  @override
  double get canvasHeight => PenModel.canvasHeight;

  ///
  /// Y方向のスクロールコントローラの幅
  ///
  final double _scrollControllerYWidth = 8;
  @override
  double get scrollControllerYWidth => _scrollControllerYWidth;

  ///
  /// X方向のスクロールコントローラの高さ
  ///
  final double _scrollControllerXHeight = 8;
  @override
  double get scrollControllerXHeight => _scrollControllerXHeight;

  @override
  MdcUnsettledObjectInfo get objectInfo => EditObjectModel().objectInfo;

  ///
  /// Objectの初期データ
  ///
  @override
  MdcUnsettledObjectInfo get originObjectInfo =>
      EditObjectModel().originObjectInfo;

  ///
  /// 回転ページを開くかどうか
  ///
  @override
  bool get isRotate => PaintModel().getPopupType() == PopupType.rotate;

  ///
  /// 長押ししているかどうか
  ///
  @override
  bool get isLongPress => EditObjectModel().isLongPress;

  @override
  set canvasController(CanvasController controller) =>
      DrawCanvasModel().canvasController = controller;

  ///
  /// 倍率のリスト
  ///
  final List<double> _scaleList = [0.5, 1, 2, 4, 8];

  ///
  /// providerのref
  ///
  final Ref _ref;

  ///
  /// リリースオフセット
  ///
  Offset releaseOffset = const Offset(0, 0);

  GestureType _gestureType = GestureType.penDraw;

  @override
  ClipBoardProperty get clipBoardProperty => ClipBoardModel().clipBoardProperty;

  @override
  ClipBoardState get clipPenState => ClipBoardModel().clipBoardState;

  ///
  /// 画面描画座標リスト
  ///
  @override
  List<Offset> get clipPenPositionList => ClipBoardModel().clipPositionList;

  ///
  /// 横方向初期化
  ///
  bool _scrollXInit = false;

  ///
  /// 縦方向初期化
  ///
  bool _scrollYInit = false;

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    double preScale = state.scale;
    double currentScale = _scaleList[MagnificationModel().valueToIndex()];

    _updateBackgroundState();
    _updatePenState(preScale, currentScale);
    _adjustScrollController(preScale);
    _updateFrameAndGrid();
    _updateSketchesImage();

    /// view更新
    state = state.copyWith(
      scale: currentScale,
      isStartAnimation: ClipBoardModel().clipPositionList.isNotEmpty,
      rotate: EditObjectModel().objectInfo.rotate,
    );

    /// サイズ変更
    if (EditObjectModel().isLongPress) {
      state = state.copyWith(partsFrameRect: _updatePartsRect());
    }
  }

  ///
  /// ドラッグ移動
  ///
  void _onDragMove(Offset offset) {
    /// 画像は2倍固定
    const double fixScale = 2.0;

    /// 1倍の画面エリア
    const double imageAreaX = PenModel.baseImageWidth / fixScale;
    const double imageAreaY = PenModel.baseImageHeight / fixScale;

    if (scrollControllerX.hasClients) {
      final maxExtent =
          imageAreaX * state.scale * fixScale - PenModel.canvasWidth;
      double offsetX = scrollControllerX.offset - offset.dx;
      offsetX = offsetX.clamp(0, maxExtent);
      scrollControllerX.jumpTo(offsetX);
    }

    if (scrollControllerY.hasClients) {
      final maxExtent =
          imageAreaY * state.scale * fixScale - PenModel.canvasHeight;
      double offsetY = scrollControllerY.offset - offset.dy;
      offsetY = offsetY.clamp(0, maxExtent);
      scrollControllerY.jumpTo(offsetY);
    }
  }

  ///
  /// 2本指でZoom倍率を拡大する前のZoom Indexバックアップ
  ///
  int _selectedZoomIndexBack = MagnificationModel.magnificationInvalid;

  @override
  CanvasPenState get currentPenState => DrawCanvasModel().penState;

  ///
  /// オンスケールスタート
  ///
  @override
  void onScaleStart(ScaleStartDetails details) {
    /// LibイメージUint8Listからui.Imageに変更中、押されていない状態
    if (_isMakingImage) {
      /// ペンツールの場合、何もしない
      final Operation operation = _getCurrentOperation();
      final bool isDrawing = PenModel().isDrawingTool();
      if (operation != Operation.clipPen && isDrawing) {
        return;
      }

      /// 背景イメージ または 部分模様イメージは作成中の場合、操作操作必要しない
      final bool skipOperation =
          SelectModel.selectTypes == MdcClippingTypes.dropperSelect ||
              SelectModel.selectTypes == MdcClippingTypes.allSelect;
      if (skipOperation) {
        return;
      }
    }

    /// 1本指：お絵描き開始
    if (TopBarModel().getDragMoveFlg() == false &&
        _gestureType == GestureType.penDraw) {
      _onPenDown(details);
      return;
    }
  }

  ///
  /// オンスケール更新
  ///
  @override
  void onScaleUpdateAndPointerMove(ScaleUpdateDetails details) {
    /// 画像作成中、拡大縮小/移動操作はできない
    if (_isMakingImage &&
        (_gestureType == GestureType.scale || _partMoveInfo.isMoving)) {
      return;
    }

    final bool isExceeded100 = MagnificationModel.magnificationLevel >
        MagnificationModel.magnification_100;
    final canDrag = TopBarModel().getDragMoveFlg() == true && isExceeded100;

    /// 2本指操作中
    if (_selectedZoomIndexBack != MagnificationModel.magnificationInvalid) {
      if (details.pointerCount > _oneFingerTouch &&
          _gestureType == GestureType.scale) {
        _scaleUpdate(details.scale);
        _onDragMove(details.focalPointDelta);
      }
    }

    /// 2本指操作開始
    else if (details.pointerCount > _oneFingerTouch) {
      _selectedZoomIndexBack = MagnificationModel().valueToIndex();
      _partMoveInfo
        ..isMoving = false
        ..startPoint = Offset.zero
        ..totalOffset = Offset.zero;
      _gestureType = GestureType.scale;
      ClipBoardModel().resetPositionList();
    } else {
      /// Do Nothing
    }

    if (details.pointerCount == _oneFingerTouch) {
      /// 移動操作
      if (canDrag) {
        _onDragMove(details.focalPointDelta);
        return;
      }

      /// お絵描き
      if (_gestureType == GestureType.penDraw) {
        _onPanMove(details);
      }
    }
  }

  ///
  /// 刺しゅう枠のイメージを作成する
  ///
  @override
  void drawEmbFrameAndGrid(Canvas canvas, MDCFrameMetrics metrics) {
    final Path framePath =
        _getEmbFrameDrawPath(metrics.frameType, metrics.scale);

    _drawEmbFrame(canvas, framePath);

    final List<double> horizontalList =
        _getGridHorizontalList(metrics.gridType, metrics.scale);
    final List<double> verticalList =
        _getGridVerticalList(metrics.gridType, metrics.scale);

    final double scaledWidth = PenModel.baseImageWidth * metrics.scale;
    final double scaledHeight = PenModel.baseImageHeight * metrics.scale;

    _drawEmbGrid(canvas, horizontalList, verticalList, metrics.gridType,
        Size(scaledWidth, scaledHeight));
  }

  ///
  /// 選択した枠のプレビュー表示データを取得します
  ///
  Path _getEmbFrameDrawPath(EmbFrameDispType frameType, double scale) {
    List<FrameSizeAndArea>? frameSizeAndAreaList =
        getFrameDisplaySizeAndArea(frameType);
    assert(frameSizeAndAreaList != null, "対応するサイズのボックスが見つかりません");

    Path drawPath = Path();
    Path basePath = Path();

    if (frameSizeAndAreaList!.length > 1) {
      basePath = _get60x20FrameBasePath(scale, frameSizeAndAreaList);
    } else {
      MdcSize frameSize = MdcSize(
        width: frameSizeAndAreaList.first.width / _conversionRate,
        height: frameSizeAndAreaList.first.height / _conversionRate,
      );

      double width = frameSize.width / _ratio * scale;
      double height = frameSize.height / _ratio * scale;

      Rect rect = Rect.fromCenter(
        center: Offset(
          PenModel.baseImageWidth * scale / 2,
          PenModel.baseImageHeight * scale / 2,
        ),
        width: width,
        height: height,
      );

      basePath.moveTo(rect.left.roundToDouble(), rect.top.roundToDouble());
      basePath.lineTo(rect.left.roundToDouble(), rect.bottom.roundToDouble());
      basePath.lineTo(rect.right.roundToDouble(), rect.bottom.roundToDouble());
      basePath.lineTo(rect.right.roundToDouble(), rect.top.roundToDouble());
      basePath.lineTo(rect.left.roundToDouble(), rect.top.roundToDouble());
    }

    /// フレームの破線の一段の長さ
    double dashWidth = _dashedLineLength;
    double dashSpace = _dashedLineSpace;

    /// 描画Pathを計算する
    double distance = 0.0;
    for (ui.PathMetric pathMetric in basePath.computeMetrics()) {
      while (distance < pathMetric.length) {
        drawPath.addPath(
          pathMetric.extractPath(distance, distance + dashWidth),
          Offset.zero,
        );
        distance += dashWidth;
        distance += dashSpace;
      }
    }
    return drawPath;
  }

  ///
  /// 60*20枠のプレビューにはデータが表示されま
  ///
  Path _get60x20FrameBasePath(
      double scale, List<FrameSizeAndArea> frameSizeAndAreaList) {
    Path basePath = Path();

    /// List順：60*20 mm、50*30 mm、30*40 mm
    List<Rect> rectList = List.generate(frameSizeAndAreaList.length, (index) {
      MdcSize frameSize = MdcSize(
        width: frameSizeAndAreaList[index].width / _conversionRate,
        height: frameSizeAndAreaList[index].height / _conversionRate,
      );
      double width = (frameSize.width / _ratio * scale).roundToDouble();
      double height = (frameSize.height / _ratio * scale).roundToDouble();

      Rect rect = Rect.fromCenter(
        center: Offset(PenModel.baseImageWidth * scale / 2,
            PenModel.baseImageHeight * scale / 2),
        width: width,
        height: height,
      );
      return Rect.fromLTWH(
          rect.left.roundToDouble(), rect.top.roundToDouble(), width, height);
    });

    /// 1番目と2番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*40 mm
    rectList.insert(
      1,
      Rect.fromPoints(
        Offset(rectList[1].left, rectList.first.top),
        Offset(rectList[1].right, rectList.first.bottom),
      ),
    );

    /// 2番目と3番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*30 mm、30*40 mm
    rectList.insert(
      3,
      Rect.fromPoints(
        Offset(rectList.last.left, rectList[2].top),
        Offset(rectList.last.right, rectList[2].bottom),
      ),
    );

    /// 60*20 mmの左上点から描画
    basePath.moveTo(rectList.first.left, rectList.first.top);

    /// Rectの左上点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].left, rectList[index].top);
    }

    /// Rectの右上点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].right, rectList[index].top);
    }

    /// Rectの右下点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].right, rectList[index].bottom);
    }

    /// Rectの左下点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].left, rectList[index].bottom);
    }

    /// 接続開始点
    basePath.lineTo(rectList.first.left, rectList.first.top);

    return basePath;
  }

  ///
  /// グリッドの垂直線のリストを取得します
  ///
  List<double> _getGridVerticalList(EmbGridType gridType, double scale) {
    double offsetXMax = PenModel.baseImageWidth * scale;
    List<double> verticalList = [];
    double xCenter = offsetXMax / 2;
    double xOffset = xCenter;
    int mmValue = gridType == EmbGridType.embGridGridLine10
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    int mm2Px = (mmValue / _ratio * scale).round();

    /// 中心点の左端の線の位置を計算するには
    xOffset -= mm2Px;
    while (xOffset >= 0) {
      verticalList.add(xOffset);
      xOffset -= mm2Px;
    }

    /// 中心点の右端の線の位置を計算するには
    xOffset = xCenter;
    xOffset += mm2Px;
    while (xOffset <= offsetXMax) {
      verticalList.add(xOffset);
      xOffset += mm2Px;
    }

    verticalList.sort();
    return verticalList;
  }

  ///
  /// グリッドの水平線のリストを取得します
  ///
  List<double> _getGridHorizontalList(EmbGridType gridType, double scale) {
    double offsetYMax = PenModel.baseImageHeight * scale;

    List<double> horizontalList = [];
    double yCenter = offsetYMax / 2;
    double yOffset = yCenter;
    int mmValue = gridType == EmbGridType.embGridGridLine10
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    int mm2Px = (mmValue / _ratio * scale).round();

    /// 中心点上端の線の位置を計算するには
    yOffset -= mm2Px;
    while (yOffset >= 0) {
      horizontalList.add(yOffset);
      yOffset -= mm2Px;
    }

    /// 中心点の下端の線の位置を計算するには
    yOffset = yCenter;
    yOffset += mm2Px;
    while (yOffset <= offsetYMax) {
      horizontalList.add(yOffset);
      yOffset += mm2Px;
    }

    horizontalList.sort();
    return horizontalList;
  }

  ///
  /// 刺しゅう枠を描画する
  ///
  void _drawEmbFrame(Canvas canvas, Path path) {
    /// ペンキ塗り
    Paint paint = Paint()
      ..isAntiAlias = true
      ..color = Colors.black
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke
      ..strokeJoin = StrokeJoin.miter;
    canvas.drawPath(path, paint);
  }

  ///
  /// グリッドを描画する
  ///
  void _drawEmbGrid(Canvas canvas, List<double> horizontalList,
      List<double> verticalList, EmbGridType gridType, Size size) {
    const double strokeWidth = 1;

    ///
    /// センターカーソル
    ///
    void gridCenterLine(Canvas canvas, Size size) {
      final paint = Paint()
        ..isAntiAlias = true
        ..color = getMdcGridColor()
        ..strokeCap = StrokeCap.butt
        ..strokeWidth = strokeWidth * 2
        ..style = PaintingStyle.stroke;

      canvas.drawPoints(
        PointMode.lines,
        [
          Offset(1, size.centerLeft(Offset.zero).dy),
          Offset(size.width - 1, size.centerLeft(Offset.zero).dy)
        ],
        paint,
      );

      canvas.drawPoints(
        PointMode.lines,
        [
          Offset(size.topCenter(Offset.zero).dx, 1),
          Offset(size.topCenter(Offset.zero).dx, size.height - 1)
        ],
        paint,
      );
    }

    ///
    /// 10mm格子 と 25mm格子
    ///
    void gridLine(Canvas canvas, Size size) {
      gridCenterLine(canvas, size);

      Paint paint = Paint()
        ..isAntiAlias = true
        ..color = getMdcGridColor()
        ..strokeCap = StrokeCap.butt
        ..strokeWidth = strokeWidth
        ..style = PaintingStyle.stroke;

      for (var element in verticalList) {
        canvas.drawLine(
          Offset(element, 1),
          Offset(element, size.height - 1),
          paint,
        );
      }
      for (var element in horizontalList) {
        canvas.drawLine(
          Offset(1, element),
          Offset(size.width - 1, element),
          paint,
        );
      }
    }

    ///
    /// 中央赤十字
    ///
    void centerPointer(Canvas canvas, Size size) {
      Paint paint = Paint()
        ..isAntiAlias = false
        ..color = Colors.white
        ..strokeCap = StrokeCap.butt
        ..strokeWidth = strokeWidth
        ..style = PaintingStyle.fill;

      canvas.drawRect(
        Rect.fromCenter(
          center: size.center(Offset.zero),
          width: 22,
          height: 6,
        ),
        paint,
      );
      canvas.drawRect(
        Rect.fromCenter(
          center: size.center(Offset.zero),
          width: 6,
          height: 22,
        ),
        paint,
      );

      paint.color = const Color.fromARGB(255, 235, 0, 0);
      canvas.drawRect(
        Rect.fromCenter(
          center: size.center(Offset.zero),
          width: 20,
          height: 4,
        ),
        paint,
      );
      canvas.drawRect(
        Rect.fromCenter(
          center: size.center(Offset.zero),
          width: 4,
          height: 20,
        ),
        paint,
      );
    }

    switch (gridType) {
      case EmbGridType.embGridGridLine10:
      case EmbGridType.embGridGridLine_25:
        gridLine(canvas, size);
        break;
      case EmbGridType.embGridMark:
        centerPointer(canvas, size);
        break;
      case EmbGridType.embGridCenterLine:
        gridCenterLine(canvas, size);
        break;
      case EmbGridType.embGridNot:
      default:
        break;
    }
  }

  ///
  /// 調整座標
  ///
  Offset _adjustCoordinates(Offset offset) {
    Offset keyPose = _keyPosToView(offset);
    int x = keyPose.dx.toInt();
    int y = keyPose.dy.toInt();

    if (state.scale == _scaleList[0]) {
      /* 拡大 */
      double magRatio = PenModel.baseImageWidth / canvasWidth;
      double magRatioHalf = magRatio * state.scale;
      double valueX = offset.dx * magRatio + magRatioHalf;
      double valueY = offset.dy * magRatio + magRatioHalf;
      x = (valueX + (valueX >= 0 ? 0.5 : -0.5)).toInt();
      y = (valueY + (valueX >= 0 ? 0.5 : -0.5)).toInt();
    } else if (state.scale == _scaleList[1]) {
      x += scrollControllerX.offset.toInt();
      y += scrollControllerY.offset.toInt();
    } else {
      int scale = state.scale.toInt();
      int xx = x.toInt();
      int yy = y.toInt();
      xx -= scale >> 1;
      yy -= scale >> 1;
      xx <<= 4;
      yy <<= 4;
      xx = xx ~/ scale + 8;
      yy = yy ~/ scale + 8;
      xx >>= 4;
      yy >>= 4;

      xx += scrollControllerX.offset ~/ state.scale;
      yy += scrollControllerY.offset ~/ state.scale;
      x = xx;
      y = yy;
    }
    return Offset(x.toDouble(), y.toDouble());
  }

  ///
  /// 移動座標調整
  ///
  Offset _adjustMoveCoordinates(Offset offset) {
    double offsetX = offset.dx;
    double offsetY = offset.dy;

    /// 2倍の座標に変換する
    if (state.scale == _scaleList[0]) {
      offsetX = offsetX * 2;
      offsetY = offsetY * 2;
    } else if (state.scale == _scaleList[1]) {
      offsetX = offsetX;
      offsetY = offsetY;
    } else {
      offsetX = offsetX / state.scale;
      offsetY = offsetY / state.scale;
    }

    final tempTotalOffsetX = _partMoveInfo.totalOffset.dx + offsetX;
    final tempTotalOffsetY = _partMoveInfo.totalOffset.dy + offsetY;

    if (_partMoveInfo.startPoint.dx + tempTotalOffsetX >
            PenModel.baseImageWidth ||
        _partMoveInfo.startPoint.dx + tempTotalOffsetX < 0) {
      offsetX = 0;
    } else {
      _partMoveInfo.totalOffset =
          Offset(tempTotalOffsetX, _partMoveInfo.totalOffset.dy);
    }

    if (_partMoveInfo.startPoint.dy + tempTotalOffsetY >
            PenModel.baseImageHeight ||
        _partMoveInfo.startPoint.dy + tempTotalOffsetY < 0) {
      offsetY = 0;
    } else {
      _partMoveInfo.totalOffset =
          Offset(_partMoveInfo.totalOffset.dx, tempTotalOffsetY);
    }

    return Offset(offsetX, offsetY);
  }

  ///
  /// KEY関数座標からViewRect上の座標値に変換する
  ///
  Offset _keyPosToView(Offset keyPose) {
    /// KeyPostToView
    double keyPosX = keyPose.dx;
    double keyPosY = keyPose.dy;
    double viewWidth = PenModel.canvasWidth;
    double viewHeight = PenModel.canvasHeight;

    if (MagnificationModel.magnificationLevel >
        MagnificationModel.magnification_100) {
      viewWidth -= _scrollControllerYWidth;
      viewHeight -= _scrollControllerXHeight;
    }

    keyPosX -= 1;
    keyPosX = max(0, min(keyPosX, viewWidth - 1));
    keyPosY = max(0, min(keyPosY, viewHeight - 1));

    return Offset(keyPosX, keyPosY);
  }

  ///
  /// 二重指拡張時のScale値変換
  /// 拡大時、scale範囲は1より大きく、scale値が大きいほど拡大する(1精度)
  /// 縮小時、scale範囲0 ~ 1、scale値が小さいほど縮小されます(0.1精度)
  ///
  int _getDoubleFingerStep(double scale) {
    final double oldScale = _scaleList[_selectedZoomIndexBack];
    final double newScale = oldScale * scale;
    int index = 0;

    /// scaleは0～1の場合
    if (newScale < _scaleList[1]) {
      final String formattedNumber = newScale.toStringAsFixed(1);
      final double roundedNumber = double.parse(formattedNumber);
      index = _scaleList.indexWhere((element) => roundedNumber <= element);
    }

    /// scaleは1～の場合
    else {
      final intScale = newScale.ceil();
      index = _scaleList.lastIndexWhere((element) => intScale >= element);
    }

    /// 無効値
    if (index == MagnificationModel.magnificationInvalid) {
      return MagnificationModel.magnificationInvalid;
    }

    return index;
  }

  ///
  /// 操作モードの判断
  ///
  Operation _getCurrentOperation() {
    switch (DrawingTypeModel.mdcDrawingType) {
      case MdcDrawingType.drawtype_pencil:
      case MdcDrawingType.drawtype_line_fill:
      case MdcDrawingType.drawtype_line_color:
      case MdcDrawingType.drawtype_blush:
      case MdcDrawingType.drawtype_surface_fill:
      case MdcDrawingType.drawtype_surface_color:
      case MdcDrawingType.drawtype_eraser:
        return Operation.editPen;
      case MdcDrawingType.drawtype_area_select:
        return Operation.clipPen;
      default:
        return Operation.none;
    }
  }

  ///
  /// 刺しゅう枠とグリッドのメトリクス更新
  ///
  void _updateFrameAndGrid() {
    final MDCFrameMetrics currentFrameMetrics = MDCFrameMetrics(
        frameType: DeviceInfoModel().frameType,
        gridType: DeviceInfoModel().gridType,
        scale: _scaleList[MagnificationModel().valueToIndex()]);

    state = state.copyWith(frameMetrics: currentFrameMetrics);
  }

  ///
  /// 部分模様/背景模様 更新
  ///
  Future<void> _updateUiImageInfo() async {
    _isMakingImage = true;
    final result = await _updateImageInfo(
        EditObjectModel().getMdcPartsImageInfo(),
        EditObjectModel().getMdcBackGroundImageInfo());
    state = state.copyWith(
      imgInfoParts: result.partImageInfo,
      imgInfoBackGround: result.backgroundImageInfo,
      partsImage: result.partsImage,
      backgroundImage: result.backgroundImage,
      partsFrameRect: result.partRect,
      partsFramePath: _getFrameRectPath(),
    );
    _isMakingImage = false;
  }

  Future<
          ({
            PartsImage? partsImage,
            ui.Image? backgroundImage,
            MdcImageInfo partImageInfo,
            MdcImageInfo backgroundImageInfo,
            Rect partRect,
          })>
      _updateImageInfo(
          MdcImageInfo partImageInfo, MdcImageInfo backgroundImageInfo) async {
    PartsImage? partsImage = state.partsImage;
    ui.Image? backgroundImage = state.backgroundImage;
    Rect partRect;

    /// 部分模様のui.imageを作成する
    if (partImageInfo.imageData.isEmpty) {
      partsImage = null;
    } else if (partImageInfo.imageData.isNotEmpty &&
        partImageInfo.imageData.hashCode !=
            state.imgInfoParts?.imageData.hashCode) {
      final ui.Image image = await _decodeImage(partImageInfo.imageData);
      partsImage = PartsImage(partImageInfo.hashCode, image);
      EditObjectModel().unlockPartsProcess();
    } else {
      /// Do Nothing
    }

    /// 背景イメージのui.imageを作成する
    if (backgroundImageInfo.imageData.isEmpty) {
      backgroundImage = null;
    } else if (backgroundImageInfo.imageData.isNotEmpty &&
        (backgroundImageInfo.hashCode != state.imgInfoBackGround.hashCode)) {
      backgroundImage = await _decodeImage(backgroundImageInfo.imageData);
      DrawCanvasModel().canvasController.reset();
      DrawCanvasModel().canvasController.image = backgroundImage;
      DrawCanvasModel().canvasController.setBackUpImage(backgroundImage);
    } else {
      /// Do Nothing
    }

    /// お絵描き画像Rect更新
    if (partImageInfo.imageData.isNotEmpty) {
      final centerPose = Offset(
        objectInfo.centerPosX * state.scale,
        objectInfo.centerPosY * state.scale,
      );
      partRect = Rect.fromCenter(
        center: centerPose,
        width: objectInfo.width * state.scale,
        height: objectInfo.height * state.scale,
      );
    } else {
      partRect = Rect.zero;
    }

    return (
      partsImage: partsImage,
      backgroundImage: backgroundImage,
      partImageInfo: partImageInfo,
      backgroundImageInfo: backgroundImageInfo,
      partRect: partRect
    );
  }

  ///
  /// お絵描き画像更新
  ///
  Future<void> _updateSketchesImage() async {
    final imgDate = ScanModel().sketchesImageData;
    final currentImageHash = imgDate.hashCode;
    if (currentImageHash != _previousSketchesHash) {
      _previousSketchesHash = currentImageHash;
      if (imgDate.isNotEmpty) {
        _isMakingImage = true;
        final ui.Image image = await _decodeImage(imgDate);
        _isMakingImage = false;
        state = state.copyWith(sketchesImage: image);
      } else {
        state = state.copyWith(sketchesImage: null);
      }
    } else {
      // ハッシュ値が同じ場合は画像を更新しない
    }
  }

  ///
  /// お絵描き画像Rect更新
  ///
  Rect _updatePartsRect() {
    if (EditObjectModel().hasPartsImageInfo()) {
      final centerPose = Offset(
        objectInfo.centerPosX * state.scale,
        objectInfo.centerPosY * state.scale,
      );
      return Rect.fromCenter(
        center: centerPose,
        width: objectInfo.width * state.scale,
        height: objectInfo.height * state.scale,
      );
    }
    return Rect.zero;
  }

  ///
  /// 選択枠表示用Path作成
  /// ([0]左上、[1]左下、[2]右下、[3]右上座標)
  /// ※部分模様の左上からのオフセット座標
  ///
  Path? _getFrameRectPath() {
    final imageInfo = EditObjectModel().getMdcPartsImageInfo();
    if (imageInfo.imageData.isEmpty) {
      return null;
    }

    Path path = Path();
    path.moveTo(
        (imageInfo.startPointX + objectInfo.rectanglePntX[0]).toDouble(),
        (imageInfo.startPointY + objectInfo.rectanglePntY[0]).toDouble());
    for (int i = 1; i < objectInfo.rectanglePntX.length; i++) {
      path.lineTo(
          (imageInfo.startPointX + objectInfo.rectanglePntX[i]).toDouble(),
          (imageInfo.startPointY + objectInfo.rectanglePntY[i]).toDouble());
    }
    path.close();
    return path;
  }

  ///
  /// ScrollBar調整
  ///
  void _adjustScrollController(double preScale) {
    if (MagnificationModel.magnificationLevel ==
        MagnificationModel.magnification_100) {
      if (scrollControllerX.hasClients) {
        scrollControllerX.jumpTo(0);
        _scrollXInit = false;
      }
      if (scrollControllerY.hasClients) {
        scrollControllerY.jumpTo(0);
        _scrollYInit = false;
      }
      MagnificationModel().zoomEdit.zoomNowF = false;
    } else {
      if (MagnificationModel().zoomEdit.zoomNowF) {
        /// 1倍の画面エリア
        const double imageAreaX = PenModel.baseImageWidth / 2;
        const double imageAreaY = PenModel.baseImageHeight / 2;
        const double fixScale = 2;
        final realScale = preScale * fixScale;

        /// 移動情報設定
        if (scrollControllerX.hasClients) {
          /// 横方向の最大移動量
          final maxExtent = imageAreaX * realScale - PenModel.canvasWidth;
          final double centerPos = maxExtent / 2;
          final double offset = scrollControllerX.offset.ceilToDouble();

          MagnificationModel().zoomEdit
            ..preOffsetX = offset
            ..preCenterX = centerPos
            ..scrollOrgX = (offset - centerPos) / realScale;
        }

        if (scrollControllerY.hasClients) {
          /// 縦方向の最大移動量
          final maxExtent = imageAreaY * realScale - PenModel.canvasHeight;
          final double centerPos = maxExtent / 2;
          final double offset = scrollControllerY.offset.ceilToDouble();

          MagnificationModel().zoomEdit
            ..preOffsetY = offset
            ..preCenterY = centerPos
            ..scrollOrgY = (offset - centerPos) / realScale;
        }

        /// 画面表示座標計算
        final EditZoom zoom = MagnificationModel().zoomEdit;

        /// 横方向移動
        if (scrollControllerX.hasClients) {
          /// ScrollBar移動位置
          double offsetX = 0;

          /// 横方向の最大移動量
          final maxExtent =
              imageAreaX * state.scale * fixScale - PenModel.canvasWidth;

          /// 初期化
          if (_scrollXInit == false) {
            offsetX = maxExtent / 2;
            scrollControllerX.jumpTo(offsetX);
            _scrollXInit = true;
          } else {
            /// 表示エリアの半分
            const double halfScreen = PenModel.canvasWidth / 2;

            /// 移動量の計算
            final centerPose = zoom.detailCTPos.dx;
            double offset = MagnificationModel().divideAbyBWithRoundUp(
                zoom.scrollOrgX * zoom.magnification.shi,
                zoom.magnification.bo);
            offsetX = centerPose + offset - halfScreen;

            //描画エリアの末端はどこなのか
            if (offsetX < 0) {
              offset = 0;
            }
            if (offsetX > maxExtent) {
              offset = maxExtent;
            }

            /// scrollBar移動位置設定
            scrollControllerX.jumpTo(offsetX);
          }
        }

        /// 縦方向移動
        if (scrollControllerY.hasClients) {
          /// 縦方向の最大移動量
          final maxExtent =
              imageAreaY * state.scale * fixScale - PenModel.canvasHeight;

          /// ScrollBar移動位置
          double offsetY = 0;

          /// 初期化
          if (_scrollYInit == false) {
            offsetY = maxExtent / 2;
            scrollControllerY.jumpTo(offsetY);
            _scrollYInit = true;
          } else {
            /// 表示エリアの半分
            const double halfScreen = PenModel.canvasHeight / 2;

            /// 移動量の計算
            final centerPose = MagnificationModel().zoomEdit.detailCTPos.dy;
            double offset = MagnificationModel().divideAbyBWithRoundUp(
                zoom.scrollOrgY * zoom.magnification.shi,
                zoom.magnification.bo);
            offsetY = centerPose + offset - halfScreen;

            /// 描画エリアの末端はどこなのか
            if (offsetY < 0) {
              offset = 0;
            }
            if (offsetY > maxExtent) {
              offset = maxExtent;
            }

            /// scrollBar移動位置設定
            scrollControllerY.jumpTo(offsetY);
          }
        }

        MagnificationModel().zoomEdit.zoomNowF = false;
      }
    }
  }

  ///
  /// 下絵イメージ更新
  ///
  void _updateBackgroundState() {
    int index = TopBarModel().densityLevelIndex;

    /// view更新
    state = state.copyWith(
      sketchesDensityLevel: ScanModel().backgroundDensityLevel[index],
      mdcDensityLevel: ScanModel().mdcDensityLevel[index],
    );
  }

  ///
  /// ペン処理
  ///
  void _updatePenState(double preScale, double currentScale) {
    DrawCanvasModel().penState.nearArea =
        DrawCanvasModel().getDisplayNearArea();
    if (preScale != currentScale) {
      /// view更新
      state = state.copyWith(
        scale: currentScale,
        scrollBarWidth: MagnificationModel.magnificationLevel ==
                MagnificationModel.magnification_100
            ? canvasWidth
            : canvasWidth - _scrollControllerYWidth,
        scrollBarHeight: MagnificationModel.magnificationLevel ==
                MagnificationModel.magnification_100
            ? canvasHeight
            : canvasHeight - _scrollControllerXHeight,
        scaleImageWidth: PenModel.baseImageWidth * currentScale,
        scaleImageHeight: PenModel.baseImageHeight * currentScale,
      );
    }
  }

  ///
  /// Parts模様範囲の判断
  ///
  bool _isPressInPartArea(Offset position) {
    if (EditObjectModel().getMdcPartsImageInfo().imageData.isNotEmpty) {
      MdcUnsettledObjectInfo objectInfo = EditObjectModel().objectInfo;
      MdcImageInfo imageInfo = EditObjectModel().getMdcPartsImageInfo();

      Rect rect = Rect.fromPoints(
        Offset(
          (imageInfo.startPointX + objectInfo.rectanglePntX[0]).toDouble(),
          (imageInfo.startPointY + objectInfo.rectanglePntY[0]).toDouble(),
        ),
        Offset(
          (imageInfo.startPointX + objectInfo.rectanglePntX[2]).toDouble(),
          (imageInfo.startPointY + objectInfo.rectanglePntY[2]).toDouble(),
        ),
      );
      if (rect.contains(position)) {
        return true;
      }
    }
    return false;
  }

  @override
  void onPointerDown(PointerDownEvent event) {
    /// ポップアップCloseと画面更新
    if (PaintModel().isMagnificationPopupOpened()) {
      PaintModel().clearPopupType();
      updateMagnificationByChild?.call();
    }

    /// お絵描きエリアに触れている指の数を統計する
    /// _numOfFingers == _oneFingerTouch 画面描画 または 移動
    /// _numOfFingers >= _towFingerTouch 拡大縮小 または 移動
    _numOfFingers++;

    /// LibイメージUint8Listからui.Imageに変更中、押されていない状態
    if (_isMakingImage) {
      /// 範囲選択中の場合、
      /// release操作必要：
      ///                矩形選択
      ///                自由曲線(最後は閉じる)
      ///                直線(連続)の開始点
      /// release操作なし：
      ///                自動選択
      ///                全選択
      final Operation operation = _getCurrentOperation();
      final bool skipRelease = operation == Operation.editPen ||
          SelectModel.selectTypes == MdcClippingTypes.dropperSelect ||
          SelectModel.selectTypes == MdcClippingTypes.allSelect ||
          SelectModel.selectTypes == MdcClippingTypes.lineMulti ||
          _partMoveInfo.isMoving;
      final bool needRelease =
          SelectModel.selectTypes == MdcClippingTypes.rectangle ||
              SelectModel.selectTypes == MdcClippingTypes.freeClose;
      if (!skipRelease && needRelease) {
        /// 前回の範囲選択完了処理
        EditObjectModel().release(SelectModel.selectTypes);
        ClipBoardModel().release(ClipBoardModel().clipBoardState.lastPosition);
      } else {
        /// Do Nothing
      }
    }

    /// 1本指：お絵描き または 移動開始
    if (_numOfFingers == _oneFingerTouch) {
      _gestureType = GestureType.penDraw;
      return;
    }

    final bool isHandleOff = TopBarModel().getDragMoveFlg() == false;

    /// 2本指：他の操作
    if (_gestureType != GestureType.scale &&
        _numOfFingers > _oneFingerTouch &&
        isHandleOff == true) {
      /// HandleOn
      _handleButtonOn();

      /// 保存した内容戻る
      _removePointer();
      return;
    }
  }

  @override
  void onPointerUp(PointerUpEvent event) {
    _numOfFingers--;

    /// 指の数が負の数
    if (_numOfFingers < _noFingerTouch) {
      _numOfFingers = _oneFingerTouch;
      return;
    }

    /// お絵描き
    if (_gestureType == GestureType.penDraw &&
        TopBarModel().getDragMoveFlg() == false) {
      _onPenUp(event);
      return;
    }

    /// 2本指操作中
    if (_gestureType == GestureType.scale) {
      /// 無効値(2本指操作終了)
      if (_selectedZoomIndexBack != MagnificationModel.magnificationInvalid) {
        _selectedZoomIndexBack = MagnificationModel.magnificationInvalid;
        return;
      }

      /// 1本指->0本指
      if (_numOfFingers == _noFingerTouch) {
        _handleButtonOff();
        return;
      }
    }
  }

  ///
  /// HandleボタンOn状態になります
  ///
  void _handleButtonOn() {
    _isPreHandleOn = TopBarModel().getDragMoveFlg();
    TopBarModel().setDragMoveFlg(true);

    /// 他の画面を更新する
    _ref
        .read(paintPageProvider.notifier)
        .updatePaintPageByChild(ModuleType.canvas);
  }

  ///
  /// HandleボタンOff状態になります
  ///
  void _handleButtonOff() {
    if (TopBarModel().getDragMoveFlg()) {
      if (MagnificationModel.magnificationLevel ==
          MagnificationModel.magnification_100) {
        TopBarModel().setDragMoveFlg(false);
      } else {
        TopBarModel().setDragMoveFlg(_isPreHandleOn);
      }

      /// 他の画面を更新する
      _ref
          .read(paintPageProvider.notifier)
          .updatePaintPageByChild(ModuleType.canvas);
    }
  }

  ///
  /// 描画した内容クリア
  ///
  void _removePointer() {
    /// 範囲選択の場合
    Operation operation = _getCurrentOperation();
    if (operation == Operation.clipPen) {
      ClipBoardModel().clipBoardState = ClipBoardState.init();
      ClipBoardModel().clipPositionList.clear();
      return;
    }

    /// お絵描きの場合
    if (operation == Operation.editPen) {
      /// 描画した点を削除する
      DrawCanvasModel().penState.position = CanvasLinePoint.init();
      DrawCanvasModel().penState.lineStatus = PointListStatus.init();

      /// 保存したの情報戻る
      DrawCanvasModel().canvasController.clearImageInfo();
      DrawCanvasModel().penState.restoreBackUp();

      /// 画面更新停止
      DrawCanvasModel().canvasController.stop();
      return;
    }
  }

  ///
  /// 拡大縮小中
  ///
  void _scaleUpdate(double scale) {
    int index = _getDoubleFingerStep(scale);
    if (index == MagnificationModel.magnificationInvalid) {
      return;
    }

    /// 必要でない場合は更新しない
    final newLevel = MagnificationModel().indexToValue(index);
    if (MagnificationModel.magnificationLevel != newLevel) {
      /// Model更新
      MagnificationModel.magnificationLevel = newLevel;

      /// 拡大中
      MagnificationModel().zoomEdit.zoomNowF = true;
      MagnificationModel().zoomInfoSet(MagnificationModel.magnificationLevel);

      /// View更新
      update();

      /// 他の画面を更新する
      _ref
          .read(paintPageProvider.notifier)
          .updatePaintPageByChild(ModuleType.canvas);
    }
  }

  ///
  /// ペンDown
  ///
  void _onPenDown(ScaleStartDetails event) {
    Offset position = _adjustCoordinates(event.localFocalPoint);
    Operation operation = _getCurrentOperation();

    switch (operation) {
      /// 範囲選択操作
      case Operation.clipPen:

        /// Parts模様範囲中
        if (_isPressInPartArea(position)) {
          SystemSoundPlayer().play(SystemSoundEnum.accept);
          _partMoveInfo
            ..isMoving = true
            ..startPoint = position
            ..totalOffset = Offset.zero;
        } else {
          /// Parts模様範囲外
          EditObjectModel().press(SelectModel.selectTypes, position);
          ClipBoardModel().press(position);

          /// view更新
          if (SelectModel.selectTypes != MdcClippingTypes.dropperSelect &&
              SelectModel.selectTypes != MdcClippingTypes.allSelect &&
              ClipBoardModel().clipPositionList.isNotEmpty) {
            state = state.copyWith(isStartAnimation: true);
          }

          /// 他の画面を更新する
          _ref
              .read(paintPageProvider.notifier)
              .updatePaintPageByChild(ModuleType.canvas);
        }

        break;

      /// ペン描画開始
      case Operation.editPen:
      default:
        PenModel().press(position);

        /// 画面更新
        /// 線または面は流し込みツールの場合
        if (PenModel().isFillColorTool()) {
          update();
          _ref
              .read(paintPageProvider.notifier)
              .updatePaintPageByChild(ModuleType.canvas);
        }

        /// 線または面はスポイトツールの場合
        else if (PenModel().isColorEyedropperTool()) {
          _ref
              .read(paintPageProvider.notifier)
              .updatePaintPageByChild(ModuleType.canvas);
        }

        /// お絵描きの場合: 鉛筆/ブラシ/消しゴム
        else {
          /// Do Nothing
        }
    }
  }

  ///
  /// ペンDown
  ///
  void _onPanMove(ScaleUpdateDetails event) {
    /// 範囲外の処理
    Offset penOffset = Offset(
      event.localFocalPoint.dx > PenModel.canvasWidth
          ? PenModel.canvasWidth
          : event.localFocalPoint.dx,
      event.localFocalPoint.dy > PenModel.canvasHeight
          ? PenModel.canvasHeight
          : event.localFocalPoint.dy,
    );

    /// ペン描画
    Offset position = _adjustCoordinates(penOffset);
    Operation operation = _getCurrentOperation();
    switch (operation) {
      /// 範囲選択操作
      case Operation.clipPen:

        /// Parts模様移動
        if (_partMoveInfo.isMoving) {
          final moveOffset = _adjustMoveCoordinates(event.focalPointDelta);
          EditObjectModel().updateMdcPartsImageInfoPosition(moveOffset);
          _updateUiImageInfo();
        } else {
          /// 範囲選択移動
          ClipBoardModel().move(position);
        }
        break;

      /// ペン描画移動
      case Operation.editPen:
      default:
        PenModel().move(position);
        break;
    }
  }

  ///
  /// ペンUp
  ///
  void _onPenUp(PointerUpEvent event) {
    Offset penOffset = Offset(
      event.localPosition.dx > PenModel.canvasWidth
          ? PenModel.canvasWidth
          : event.localPosition.dx,
      event.localPosition.dy > PenModel.canvasHeight
          ? PenModel.canvasHeight
          : event.localPosition.dy,
    );
    Offset position = _adjustCoordinates(penOffset);
    Operation operation = _getCurrentOperation();

    switch (operation) {
      /// 範囲選択操作
      case Operation.clipPen:

        /// Parts模様移動完了
        if (_partMoveInfo.isMoving) {
          final Offset totalOffset = _partMoveInfo.totalOffset;
          _partMoveInfo
            ..isMoving = false
            ..startPoint = Offset.zero
            ..totalOffset = Offset.zero;

          if (totalOffset.dx.abs() < 1 || totalOffset.dy.abs() < 1) {
            return;
          }

          final errorCode = EditObjectModel().moveUnsettledObject(
            EditObjectModel().objectInfo.centerPosX,
            EditObjectModel().objectInfo.centerPosY,
          );

          /// 現在の作業内容を保存する
          if (errorCode == MdcLibraryError.mdcNoError) {
            ResumeHistoryModel().backSnapshot();
          } else {
            /// Do Nothing
          }
        } else {
          /// 範囲選択完了
          EditObjectModel().release(SelectModel.selectTypes);
          ClipBoardModel()
              .release(ClipBoardModel().clipBoardState.lastPosition);

          /// view更新
          if (ClipBoardModel().clipPositionList.isEmpty) {
            state = state.copyWith(isStartAnimation: false);
          }

          /// 他の画面を更新する
          _ref
              .read(paintPageProvider.notifier)
              .updatePaintPageByChild(ModuleType.canvas);
        }
        break;

      /// ペン描画完了
      case Operation.editPen:
      default:
        PenModel().release(position);
        break;
    }
  }

  ///
  /// イメージをデコードする
  ///
  static Future<ui.Image> _decodeImage(Uint8List imageData) {
    final Completer<ui.Image> completer = Completer<ui.Image>();
    ui.decodeImageFromList(imageData, completer.complete);
    return completer.future;
  }

  @override
  void dispose() {
    super.dispose();
    scrollControllerX.dispose();
    scrollControllerY.dispose();
    DrawCanvasModel().penState = CanvasPenState.init();
    DrawCanvasModel().updateUiImageInfoCallBack = null;
    DrawCanvasModel().updateFrameAndGridCallback = null;
    DrawCanvasModel().updateBottomCallback = null;
    MagnificationModel().zoomEdit = EditZoom.reset();
    EditObjectModel().unlockPartsProcess();
  }

  @override
  Future<bool> lockEditProcWhenProcessing(
      void Function() afterProcessingFinish) async {
    if (!mounted) {
      return false;
    }

    if (state.isNeedProcessing) {
      return false;
    }

    /// LIB処理失敗後、画面がずっと回転の动画状態に留まる。
    if (LibraryIsolate().isMdcProcessing()) {
      state = state.copyWith(isNeedProcessing: true);

      bool isProcessCancel = false;
      await Future.doWhile(() async {
        if (!mounted) {
          isProcessCancel = false;
          return false;
        }
        if (LibraryIsolate().isMdcProcessing() == false) {
          state = state.copyWith(isNeedProcessing: false);
          afterProcessingFinish.call();
          isProcessCancel = false;
          return false;
        }

        /// 処理が速すぎると引っかかるので、画面が一週するのと同じ時間です
        await Future.delayed(processingWaitTime);
        isProcessCancel = true;
        return true;
      });

      return isProcessCancel;
    } else {
      afterProcessingFinish.call();
      return true;
    }
  }
}

///
/// 部分模様移動状態
///
class _PartMoveInfo {
  _PartMoveInfo(
      {required this.isMoving,
      required this.startPoint,
      required this.totalOffset});

  bool isMoving;
  Offset startPoint;
  Offset totalOffset;
}
