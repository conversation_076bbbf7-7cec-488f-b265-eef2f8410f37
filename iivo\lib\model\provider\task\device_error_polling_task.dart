import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:audio_player/audio_player_interface.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../iivo.dart';
import '../../../ui/app/emb/ui/page/sewing/sewing_view_model.dart';
import '../../../ui/app/emb/ui/page/sewing/tool_bar/component/layout_menu_popup/snowman/snowman_view_model.dart';
import '../../../ui/global_popup/global_popup_route.dart';
import '../../../ui/global_popup/global_popup_route_enum.dart';
import '../../../ui/global_popup/global_popups/err_positioning_area_over/err_positioning_area_over_view_model.dart';

///
/// エラーポーリング停止（エラー画面遷移停止）要求
///
void requestLockErrorPollingTask() {
  _lock = true;
}

///
/// エラーポーリング起動（もしロックしているなら）
///
void unLockErrorPollingTask() {
  _lock = false;
}

///
/// APPエラーがとうか
///
bool isAppError(int error) {
  return error == ErrCode_t.ERR_APP.index ||
      error == ErrCode_t.ERR_APP_ACT_NON.index ||
      error == ErrCode_t.ERR_APP_ACT_NON_WITH_PUSH.index ||
      error == ErrCode_t.ERR_APP_ALL_LOCK.index;
}

GlobalPopupRouteEnum? nextRoute;
Object? arguments;

///
/// アプリのError状態のerror表示を設定する
///
void setError(GlobalPopupRouteEnum next, Object? argument) {
  if (next == GlobalPopupRouteEnum.ERR_DUMMY) {
    Log.debugTrace("nextRoute is $next");
    return;
  }
  if (next.errAct == null) {
    for (int i = 0; i < 4; i++) {
      sleep(const Duration(milliseconds: 500));
      Log.e(tag: "ErrorState", description: "setAppErrorState errAct Null");
    }
    return;
  }
  final DeviceErrorCode error =
      TpdLibrary().apiBinding.setAppErrorState(next.errAct!);

  /// エラー場合 何もしない
  switch (error) {
    case DeviceErrorCode.devInvalidError:
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    case DeviceErrorCode.devParamError:
    case DeviceErrorCode.devInternalError:
      for (int i = 0; i < 4; i++) {
        sleep(const Duration(milliseconds: 500));
        Log.e(tag: "ErrorState", description: "setAppErrorState error:$error");
      }
      return;
    default:
      break;
  }

  nextRoute = next;
  arguments = argument;
  devicesErrorEvent();
}

///
/// アプリのError状態をクリアする
///
DeviceErrorCode clearError() {
  DeviceErrorCode deviceError = TpdLibrary().apiBinding.clearAppErrorState();
  if (deviceError == DeviceErrorCode.devNoError) {
    devicesErrorEvent();
  } else {
    SystemSoundPlayer().play(SystemSoundEnum.invalid);
  }
  return deviceError;
}

FutureOr<void> devicesErrorPollingTask(Duration pollingDuration) async {
  Timer.periodic(pollingDuration, (timer) {
    devicesErrorEvent();
  });
}

///
/// errorの状態に基づいてerrorの表示をコントロールする
///
void devicesErrorEvent() {
  Timeline.startSync("devicesErrorPollingTask");

  /// Lockしましたら、何もしない
  if (_lock == true) {
    Timeline.finishSync();
    return;
  }

  final errorStateTempValue = TpdLibrary().apiBinding.bpIFGetError();

  /// currentState更新
  final currentState = errorStateTempValue;

  // 同じなら、何もしない
  // しかしもしエラーは[ErrCode_t.ERR_APP]、[nextRoute==null]の時に
  // アプリで連続エラー表示を更新していますので、エラー更新が必要です。
  if (_previousState.errorCode == currentState.errorCode &&
      ((currentState.errorCode != ErrCode_t.ERR_APP.index &&
              currentState.errorCode != ErrCode_t.ERR_APP_ACT_NON.index &&
              currentState.errorCode !=
                  ErrCode_t.ERR_APP_ACT_NON_WITH_PUSH.index &&
              currentState.errorCode != ErrCode_t.ERR_APP_ALL_LOCK.index) ||
          nextRoute == null)) {
    Timeline.finishSync();
    return;
  }

  /// エラーサーズ確保
  if (_previousState.errorCode > GlobalPopupRouteEnum.values.length ||
      _previousState.errorCode < 0 ||
      currentState.errorCode > GlobalPopupRouteEnum.values.length ||
      currentState.errorCode < 0) {
    Log.e(
        tag: "ErrorEvent",
        description:
            "unknown ErrorEvent prevError:${_previousState.errorCode},nextError:${_previousState.errorCode}");
    Timeline.finishSync();
    return;
  }

  final currentError = currentState.errorCode;
  final prevError = _previousState.errorCode;

  Log.d(
      tag: "ErrorEvent",
      description:
          "err toggle\nprevious:${_previousState.errorCode},next:${currentState.errorCode}\nnext GlobalPopupName:${GlobalPopupRouteEnum.values[currentState.errorCode].name}");

  /// エラー消して欲しいの場合
  if (prevError != ErrCode_t.ERR_DUMMY.index &&
      currentError == ErrCode_t.ERR_DUMMY.index) {
    GlobalPopupRoute().maybePop();
    _previousState = currentState;
    Timeline.finishSync();
    return;
  }

  /// エラーがある場合
  /// 遷移方選んで
  final Future Function({
    required GlobalPopupRouteEnum nextRoute,
    Object? arguments,
  }) popupNavigatorTypeFunc;
  if (currentError != ErrCode_t.ERR_DUMMY.index &&
      prevError != ErrCode_t.ERR_DUMMY.index) {
    if (GlobalPopupRoute().canPop()) {
      popupNavigatorTypeFunc = GlobalPopupRoute().pushReplacement;
    } else {
      Log.e(
          tag: "ErrorEvent",
          description:
              "!!! pre view is page but try to pushReplacement error popup");
      popupNavigatorTypeFunc = GlobalPopupRoute().pushNamed;
    }
  } else {
    popupNavigatorTypeFunc = GlobalPopupRoute().pushNamed;
  }

  /// エラー処理
  /// ここのelse中に通常処理がある

  /// アプリでコントロール
  if (currentError == ErrCode_t.ERR_APP.index ||
      currentError == ErrCode_t.ERR_APP_ACT_NON.index ||
      currentError == ErrCode_t.ERR_APP_ACT_NON_WITH_PUSH.index ||
      currentError == ErrCode_t.ERR_APP_ALL_LOCK.index) {
    if (nextRoute != null) {
      popupNavigatorTypeFunc(
        nextRoute: nextRoute!,
        arguments: arguments,
      );
      nextRoute = null;
      arguments = null;
    } else {
      Log.debugTrace("nextRoute is null");
    }
  } else if (currentError == ErrCode_t.ERR_FAIL_PM_INITIAL.index) {
    if (currentState.pmError == XYCPU_X_Z_ENCODER_CHK_COMP ||
        currentState.pmError == XYCPU_Y_Z_ENCODER_CHK_COMP ||
        currentState.pmError == XYCPU_EEPROM_WRITE_FINISHED ||
        currentState.pmError == NO_ERROR) {
      /// do nothing
      Log.i(tag: "ErrorEvent", description: "pmError:${currentState.pmError}");
    } else {
      popupNavigatorTypeFunc(
        nextRoute: GlobalPopupRouteEnum.ERR_FAIL_PM_INITIAL,
      );
    }
  } else if (currentError == ErrCode_t.ERR_REMOVE_POSITION_MARK.index) {
    if (globalViewModelRef.exists(snowmanViewModelProvider) == true) {
      globalViewModelRef.read(sewingProvider.notifier).popSnowManPopup();
    } else {
      Log.w(
          tag: "ErrorEvent",
          description: "The footage outside of Snowman has come in!!!");
    }
    popupNavigatorTypeFunc(
      nextRoute: GlobalPopupRouteEnum.ERR_REMOVE_POSITION_MARK,
    );
  } else if (currentError == ErrCode_t.ERR_POSITIONING_AREA_OVER.index) {
    if (globalViewModelRef.exists(snowmanViewModelProvider) == true) {
      globalViewModelRef.read(sewingProvider.notifier).popSnowManPopup();
      popupNavigatorTypeFunc(
        nextRoute: GlobalPopupRouteEnum.ERR_POSITIONING_AREA_OVER,
        arguments: ErrPositioningAreaOverArgument(
          onOKButtonClicked: globalViewModelRef
              .read(sewingProvider.notifier)
              .positionAreaOverErrorRePushSnowManPopup,
        ),
      );
    } else {
      popupNavigatorTypeFunc(
        nextRoute: GlobalPopupRouteEnum.ERR_POSITIONING_AREA_OVER,
      );
    }
  } else if (currentError == ErrCode_t.ERR_SCAN_NORMAL_END.index) {
    EmbLibrary().apiBinding.embSewingBackgroundScanEnd();
    _saveBackgroundScanImage();
  } else {
    popupNavigatorTypeFunc(
        nextRoute: GlobalPopupRouteEnum.values[currentError]);
  }

  /// previous状態更新
  _previousState = currentState;
  Timeline.finishSync();
}

/// 前のエラー状態を保存
BPIFError _previousState = BPIFError.defaultBPIFError;

///
/// エラーポーリングロックフラグ
///
bool _lock = true;

void Function()? saveMDCScanInfoAndData;
void Function()? saveEMBScanInfoAndData;

///
/// スキャンした背景画像を保存
///
void _saveBackgroundScanImage() {
  if (saveEMBScanInfoAndData != null) {
    saveEMBScanInfoAndData?.call();
    saveEMBScanInfoAndData = null;
  } else if (saveMDCScanInfoAndData != null) {
    saveMDCScanInfoAndData?.call();
    saveMDCScanInfoAndData = null;
  } else {
    /// Do Nothing
  }
}
