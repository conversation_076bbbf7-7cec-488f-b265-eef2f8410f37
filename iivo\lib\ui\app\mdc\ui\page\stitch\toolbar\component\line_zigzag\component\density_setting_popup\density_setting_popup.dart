import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'density_setting_popup_view_model.dart';

class DensitySettingPopup extends ConsumerStatefulWidget {
  const DensitySettingPopup({super.key});

  @override
  ConsumerState<DensitySettingPopup> createState() =>
      _DensitySettingPopupState();
}

class _DensitySettingPopupState extends ConsumerState<DensitySettingPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(densitySettingPopupViewModelProvider);
    final viewModel = ref.read(densitySettingPopupViewModelProvider.notifier);
    return Row(children: [
      const Spacer(flex: 571),
      Expanded(
        flex: 229,
        child: Column(
          children: [
            const Spacer(flex: 159),
            Expanded(
                flex: 1052,
                child: Material(
                  color: Colors.transparent,
                  child: pre_edit_toolbar_mdc(
                    child: Column(
                      children: [
                        const Spacer(flex: 37),
                        const Expanded(
                          flex: 68,
                          child: Row(
                            children: [
                              Spacer(flex: 52),
                              Expanded(
                                flex: 126,
                                child: ico_stitch_zigzag_density(),
                              ),
                              Spacer(flex: 51),
                            ],
                          ),
                        ),
                        const Spacer(flex: 24),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                  flex: 205,
                                  child: grp_str_density_mdc_01(
                                    text: l10n.icon_00538,
                                  )),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 8),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                  flex: 205,
                                  child: grp_str_parameter(
                                      text: state.densityDisplayValue,
                                      isDefault: state.isDefaultValue,
                                      displayText: "%")),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 12),
                        Expanded(
                          flex: 63,
                          child: Row(
                            children: [
                              const Spacer(flex: 48),
                              Expanded(
                                flex: 63,
                                child: grp_btn_minus_01(
                                  state: state.minusButtonValid
                                      ? ButtonState.normal
                                      : ButtonState.disable,
                                  onTap: () =>
                                      viewModel.onMinusButtonClicked(false),
                                  onLongPress: () =>
                                      viewModel.onMinusButtonClicked(true),
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 10),
                              Expanded(
                                flex: 63,
                                child: grp_btn_plus_01(
                                  state: state.plusButtonValid
                                      ? ButtonState.normal
                                      : ButtonState.disable,
                                  onTap: () =>
                                      viewModel.onPluButtonClicked(false),
                                  onLongPress: () =>
                                      viewModel.onPluButtonClicked(true),
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 47),
                            ],
                          ),
                        ),
                        const Spacer(flex: 620),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 205,
                                child: grp_btn_positive_mdc(
                                  onTap: viewModel.onOkButtonClicked,
                                  text: l10n.icon_ok,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 12),
                      ],
                    ),
                  ),
                )),
            const Spacer(flex: 69),
          ],
        ),
      )
    ]);
  }
}
