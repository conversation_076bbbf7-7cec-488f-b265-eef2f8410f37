import 'dart:typed_data';
import 'dart:ui';
import '../../../../panel_library.dart';
import '../../../library_manage_meta/library_manage_meta.dart';
import '../../../library_manage_meta/mdc_library_result.dart';

abstract class MdcLibraryAPIInterface with _ExtensionFunction {
  ///
  /// MDC Mode の起動、初期化を行う
  ///
  /// My Design Centerを選択、開始する際に実行する
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcInit,
    ApiStatus.ngLib,
    """
openMdcModeを呼び出しと、アプリがフリズしました
2024-06-14 09:19:21.424  2093-3536  flutter                 com.brother.ph.iivo.brother          I  [38;5;255m┌──────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2024-06-14 09:19:21.424  2093-3536  flutter                 com.brother.ph.iivo.brother          I  [38;5;255m│ [DEBUG] | 0:19:21 423ms | Mdc Library | openMdcMode[0m
2024-06-14 09:19:21.424  2093-3536  flutter                 com.brother.ph.iivo.brother          I  [38;5;255m│ [0m
2024-06-14 09:19:21.424  2093-3536  flutter                 com.brother.ph.iivo.brother          I  [38;5;255m└──────────────────────────────────────────────────────────────────────────────────────────────────────────────[0m
2024-06-14 09:19:21.453  2093-3536  brother_app             com.brother.ph.iivo.brother          D  [2024/06/14 00:19:21.453109][DEBUG  ][3536][openEmbMode:260] start
2024-06-14 09:19:21.453  2093-3536  brother_app             com.brother.ph.iivo.brother          D  [2024/06/14 00:19:21.453255][DEBUG  ][3536][KeyFunRequestLock:3898] Matrix_Disable_Set    [0]
2024-06-14 09:19:21.486  2093-3536  brother_app             com.brother.ph.iivo.brother          D  [2024/06/14 00:19:21.486398][DEBUG  ][3536][KeyFunRequestLock:3901] lock ...
2024-06-14 09:19:21.486  2093-3536  brother_app             com.brother.ph.iivo.brother          D  [2024/06/14 00:19:21.486517][DEBUG  ][3536][KeyFunRequestLock:3909] apppppp!!!!

""",
  )
  MdcLibraryError openMdcMode();

  ///
  /// MDCをオフにする必要があるか判断します
  ///
  bool isMdcModeOpened();

  ///
  /// MDCのモード状態を設定します
  ///
  void setMdcModeOpened(bool isMdcModeOpened);

  ///
  /// MDC Mode の終了を行う
  ///
  /// My Design Center画面を終了する際に実行する、Emb画面への移行時にも実行する
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError closeMdcMode();

  ///
  /// 描画の種類を設定する
  ///
  /// 引数[in]:
  /// - [int] drawingType :
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError setMdcDrawingType(int drawingType);

  ///
  /// 線プロパティーの各パラメータを設定する
  ///
  /// 引数[in]:
  /// - [MdcEditLineProperty] lineProp :現在設定されている線プロパティーのパラメータ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError setMdcEditLineProperty(MdcEditLineProperty lineProp);

  ///
  /// 面プロパティーの各パラメータを設定する
  ///
  /// 引数[in]:
  /// - [MdcEditLineProperty] lineProp :現在設定されている線プロパティーのパラメータ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError setMdcEditSurfaceProperty(
      MdcEditSurfaceProperty mdcEditSurfaceProperty);

  ///
  /// 消しゴムプロパティーの各パラメータを一括設定する
  ///
  /// 引数[in]:
  /// - [MdcEditEraserProperty] eraseProp : 現在設定されている消しゴムプロパティーのパラメータ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError setMdcEditEraserProperty(
      MdcEditEraserProperty mdcEditEraserProperty);

  ///
  /// スタンプ図形(定型図形)の種類と形式を設定する
  ///
  /// 引数[in]:
  /// - [MdcStampProperty] stampProp : スタンプ図形(定型図形)の種類
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError setMdcStampProperty(MdcStampProperty stampProp);

  ///
  /// 描画指示関数
  ///
  /// 引数[in]:
  /// - [MdcPenTouchInfo] pInfo : ペンタッチ情報
  /// - [MdcReqProc]     reqproc : リクエストプロセス情報
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcReqProc]   :  リクエストプロセス情報
  /// - [MdcImageInfo] : 生成イメージデータ格納
  ///
  ({MdcLibraryError errorCode, MdcReqProc reqProc, MdcImageInfo imageInfo})
      editMdcDraw(MdcPenTouchInfo pInfo, MdcReqProc reqproc);

  ///
  /// セット前お絵描きイメージ編集関数
  ///
  /// 引数[in]:
  /// - [MdcUnsettledObjectInfo] unsettledObjectInfo : メンバinstはformInst_paste、clip_filenameにクリップボードファイルパスを指定、他メンバは任意
  /// - [MdcReqProc]     reqProc : メンバstatusはprogStat_beginningで指定する ※指定されない場合は内部でリカバー
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcUnsettledObjectInfo] unsettledObjectInfo : 出力する部分模様のサイズ、角度、反転状態を出力
  /// - [MdcReqProc]   :  メンバproc_idに新たな要求IDを割り振り、stausをprogStat_duringで出力
  /// - [MdcImageInfo] imgInfoParts : 選択した部分模様描画イメージ	※αブレンドRGB
  /// - [MdcImageInfo] imgInfoBackGround : 全体描画イメージ				※RGB
  ///
  ({
    MdcLibraryError errorCode,
    MdcUnsettledObjectInfo unsettledObjectInfo,
    MdcReqProc reqProc,
    MdcImageInfo imgInfoParts,
    MdcImageInfo imgInfoBackGround,
  }) editMdcUnsettledObject(MdcUnsettledObjectInfo objInfo, MdcReqProc reqProc);

  ///
  /// お絵描きイメージ貼り付け関数
  ///
  /// ※未選択状態(選択解除後、切り取り後)からの貼り付け用
  /// 貼り付け位置、サイズ、角度、反転状態は、最後にクリップボードファイルに保存した状態となる
  ///
  /// 引数[in]:
  /// - [MdcUnsettledObjectInfo] objInfo : メンバinstはformInst_paste、clip_filenameにクリップボードファイルパスを指定、他メンバは任意
  /// - [MdcReqProc]     reqProc : メンバstatusはprogStat_beginningで指定する ※指定されない場合は内部でリカバー
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcUnsettledObjectInfo] :  出力する部分模様のサイズ、角度、反転状態を出力
  /// - [MdcReqProc]   :  メンバproc_idに新たな要求IDを割り振り、stausをprogStat_duringで出力
  /// - [MdcImageInfo] imgInfoParts : 貼り付けた部分模様描画イメージ	※αブレンドRGB
  /// - [MdcImageInfo] imgInfoBackGround : 全体描画イメージ				※RGB
  ///
  ({
    MdcLibraryError errorCode,
    MdcUnsettledObjectInfo unsettledObjectInfo,
    MdcReqProc reqProc,
    MdcImageInfo imgInfoParts,
    MdcImageInfo imgInfoBackGround
  }) editMdcUnsettledObjectNewPaste(
      MdcUnsettledObjectInfo objInfo, MdcReqProc reqProc);

  ///
  /// 範囲選択指示関数
  ///
  /// 引数[in]:
  /// - [MdcPenTouchInfo] pInfo : 選択範囲情報
  /// - [MdcReqProc]     reqProc : リクエストプロセス情報
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcReqProc]   :  リクエストプロセス情報
  /// - [MdcUnsettledObjectInfo] objInfo : 範囲選択した部分模様情報   ※描画イメージと同時に出力する
  /// - [MdcImageInfo] imgInfoParts : 選択した部分模様描画イメージ(描画位置座標、縦横サイズ、BMPデータサイズ、BMPデータ) ※アルファブレンドRGB(模様なし部分が透過)
  /// - [MdcImageInfo] imgInfoBackGround : 部分模様なし全体描画イメージ(描画位置座標、縦横サイズ、BMPデータサイズ、BMPデータ) ※RGB
  ///
  ({
    MdcLibraryError errorCode,
    MdcReqProc reqProc,
    MdcUnsettledObjectInfo objectInfo,
    MdcImageInfo imgInfoParts,
    MdcImageInfo imgInfoBackGround
  }) editMdcAreaSelect(MdcPenTouchInfo pInfo, MdcReqProc reqProc, int pointNum,
      List<SSPoint> pointList);

  ///
  /// 指定されたファイルのデータを読み出して描画データを返す
  ///
  /// 記憶データのサムネイル読み出し
  ///
  /// 引数[in]:
  /// - [String] file : 読み出すデータのファイル名(フルパス) PM9形式
  /// - [int] width : 幅
  /// - [int] height : 高さ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcImageInfo]    : 生成イメージデータ格納
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.doUiImp,
    "Android Simでテストは終了し、問題がないです。 今の時点で実機テストできないので、UI実装時に確認お願い",
  )
  ({MdcLibraryError errorCode, MdcThumbImageInfo imageInfo})
      getMdcImageThumbnail(String file, int width, int height);

  ///
  /// 指定されたファイルのデータを読み出して描画データを返す
  ///
  /// 記憶データの本体読み出し
  ///
  /// 引数[in]:
  /// - [String] file : 読み出すデータのファイル名(フルパス) PM9形式
  /// - [MdcUnsettledObjectInfo] info : -
  /// - [MdcImageInfo] imgInfo : -
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcReqProc]   :  リクエストプロセス情報
  /// - [MdcUnsettledObjectInfo] objInfo : 範囲選択した部分模様情報   ※描画イメージと同時に出力する
  /// - [MdcImageInfo]    : 生成イメージデータ格納
  ///
  ({
    MdcLibraryError errorCode,
    MdcReqProc reqProc,
    MdcUnsettledObjectInfo objectInfo,
    MdcImageInfo imgInfoParts,
    MdcImageInfo backGroundImageInfo,
  }) getMdcImageSubstance(
      String file, MdcUnsettledObjectInfo info, MdcReqProc reqProc);

  ///
  /// 全画面クリア
  ///
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcImageInfo]    : 生成イメージデータ格納
  ///
  ({MdcLibraryError errorCode, MdcImageInfo imgInfo}) clearMdcAll();

  ///
  /// 領域選択形式を設定する
  ///
  /// 引数[in]:
  /// - [MdcClippingTypes] type : clip種別選択
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError setMdcAreaSelectType(MdcClippingTypes type);

  ///
  /// 表示領域を記憶保存する（PM9ファイル）
  ///
  /// 引数[in]:
  /// - [String] filename : 保存模様のフルパス
  ///
  /// 戻り値  :
  /// - [MdcLibraryError]   : エラーコード
  ///
  MdcLibraryError saveMdcPM9File(String filename);

  ///
  /// 縫製設定画面へ移行する
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///   - mdcErrorNoRegionExist:Region 対象なし
  ///   - mdcErrorInvalid:メインモータ動作中（無効音　遷移不可）
  ///   - mdcNoError:エラーなし
  /// - [Int]:リージョンの個数
  ///
  ({MdcLibraryError errorCode, int regionNum}) changeMdcPhaseDetail();

  ///
  /// 現在設定されている線プロパティーのパラメータを取得する
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcEditLineProperty] : 現在設定されている線プロパティーのパラメータ
  ///
  ({MdcLibraryError errorCode, MdcEditLineProperty editLineProperty})
      getMdcEditLineProperty();

  ///
  /// 現在設定されている面プロパティーのパラメータを取得する
  ///
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcEditSurfaceProperty] : 現在設定されている面プロパティーのパラメータ
  ///
  ({MdcLibraryError errorCode, MdcEditSurfaceProperty editSurfaceProperty})
      getMdcEditSurfaceProperty();

  ///
  /// 現在設定されている消しゴムプロパティーのパラメータを取得する
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcEditSurfaceProperty] : 現在設定されている消しゴムプロパティーのパラメータ
  ///
  ({MdcLibraryError errorCode, MdcEditEraserProperty editEraserProperty})
      getMdcEditEraserProperty();

  ///
  /// 現在設定されているスタンプ図形(定型図形)のパラメータを取得する
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcStampProperty] : スタンプ図形(定型図形)の種類
  ///
  ({MdcLibraryError errorCode, MdcStampProperty stampProperty})
      getMdcStampProperty();

  ///
  /// 現在設定されているスタンプ図形(定型図形)のパラメータを取得する
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcStampProperty] : スタンプ図形(定型図形)の種類
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    """
    """,
  )
  MdcLibraryError setMdcSelectPictureForScanInfoAndData(String filePath);

  ///
  /// ジグザグ縫いパラメータの設定を行い、選択状態のオブジェクトに取得する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [bool] targetAll:  同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewZigzagParam] prm: MDCステッチジグザグパラメータ
  /// - [ColorCode] color: 色
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({MdcLibraryError errCode, MdcSewZigzagParam value, ColorCode color})
      getMdcZigzagLineParamInfo(int regionNo, bool targetAll);

  ///
  /// ランニングステッチパラメータの設定を行い、選択状態のオブジェクトに取得する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [bool] targetAll:  同一縫い種一括指定
  ///
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewRunningParam] prm: MDCステッチ運転パラメータ
  /// - [ColorCode] color: 色
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({MdcLibraryError errCode, MdcSewRunningParam prm, ColorCode color})
      getMdcRunningStitchLineParamInfo(int regionNo, bool targetAll);

  ///
  /// 線プロパティーのトリプルステッチパラメータの設定を行い、選択状態のオブジェクトに取得する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [bool] targetAll:  同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewTripleParam] prm: Mdcスリーステッチアトリビュートのパラメータ
  /// - [ColorCode] color: 色
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({MdcLibraryError errCode, MdcSewTripleParam prm, ColorCode color})
      getMdcTripleStitchLineParamInfo(int regionNo, bool targetAll);

  ///
  /// キャンドルウィックパラメータの設定を行い、選択状態のオブジェクトに取得する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [bool] targetAll:  同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewCandleParam] prm: Mdcスリーステッチアトリビュートのパラメータ
  /// - [ColorCode] color: 色
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({MdcLibraryError errCode, MdcSewCandleParam prm, ColorCode color})
      getMdcCandlwickingLineParamInfo(int regionNo, bool targetAll);

  ///
  /// チェーンステッチパラメータの設定を行い、選択状態のオブジェクトに取得する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [bool] targetAll:  同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewChainParam] prm: Mdcステッチチェーンパラメータ
  /// - [ColorCode] color: 色
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({MdcLibraryError errCode, MdcSewChainParam prm, ColorCode color})
      getMdcChainStitchLineParamInfo(int regionNo, bool targetAll);

  ///
  /// Eステッチパラメータの設定を行い、選択状態のオブジェクトに反映する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [bool] targetAll:  同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewEParam] prm: 「Eステッチ」の各パラメータ
  /// - [ColorCode] color: 色
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({MdcLibraryError errCode, MdcSewEParam prm, ColorCode color})
      getMdcEStitchLineParamInfo(int regionNo, bool targetAll);

  ///
  /// Vステッチパラメータの設定を行い、選択状態のオブジェクトに反映する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [MdcSewVParam] prm: 「Eステッチ」の各パラメータ
  /// - [bool] targetAll:  同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError setMdcVStitchLineParam(
      int regionNo, bool targetAll, MdcSewVParam prm);

  ///
  /// Vステッチパラメータの設定を行い、選択状態のオブジェクトに反映する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [bool] targetAll:  同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewVParam] prm: 「Eステッチ」の各パラメータ
  /// - [ColorCode] color: 色
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({MdcLibraryError errCode, MdcSewVParam prm, ColorCode color})
      getMdcVStitchLineParamInfo(int regionNo, bool targetAll);

  ///
  /// モチーフパラメータの設定を行い、選択状態のオブジェクトに反映する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [MdcSewMotifParam] prm: モチーフの各パラメータ
  /// - [bool] targetAll:  同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError setMdcMotifLineParam(
      int regionNo, bool targetAll, MdcSewMotifParam prm);

  ///
  /// モチーフパラメータの設定を行い、選択状態のオブジェクトに反映する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [bool] targetAll:  同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewMotifParam] prm: モチーフの各パラメータ
  /// - [ColorCode] color: 色
  /// - [bool] motifType: 線モチーフタイプ
  /// - [int] motifNo: 線モチーフ番号
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({
    MdcLibraryError errCode,
    MdcSewMotifParam prm,
    ColorCode color,
    bool motifType,
    int motifNo
  }) getMdcMotifLineParamInfo(int regionNo, bool targetAll);

  ///
  /// 「タタミ縫い」の各パラメータの設定を行い、選択状態のオブジェクトに反映する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [MdcSewTatamiParam] prm: 「タタミ縫い」の各パラメータ
  /// - [bool] targetAll            ：同一縫い種一括指定
  ///
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError setMdcTatamiSurfaceParam(
      int regionNo, bool targetAll, MdcSewTatamiParam prm);

  ///
  /// 「タタミ縫い」の各パラメータの設定を行い、選択状態のオブジェクトに反映する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [bool] targetAll            ：同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [ColorCode] color: 色
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewTatamiParam] prm: 「タタミ縫い」の各パラメータ
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({ColorCode color, MdcLibraryError errorCode, MdcSewTatamiParam prm})
      getMdcTatamiSurfaceParamInfo(int regionNo, bool targetAll);

  ///
  /// 「スティップリング」の各パラメータの設定を行い、選択状態のオブジェクトに反映する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [MdcSewStipParam] prm: スティップリング縫いの各パラメータ
  /// - [bool] targetAll            ：同一縫い種一括指定
  ///
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError setMdcStipplingSurfaceParam(
      int regionNo, bool targetAll, MdcSewStipParam prm);

  ///
  /// 「スティップリング」の各パラメータの設定を行い、選択状態のオブジェクトに反映する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [bool] targetAll            ：同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [ColorCode] color: 色
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewStipParam] prm: スティップリング縫いの各パラメータ
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({ColorCode color, MdcLibraryError errorCode, MdcSewStipParam prm})
      getMdcStipplingSurfaceParamInfo(int regionNo, bool targetAll);

  ///
  /// 「Decorative Fill」の各パラメータの設定を行い、選択状態のオブジェクトに反映する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [bool] targetAll            ：同一縫い種一括指定
  /// - [MdcSewDecoParam] prm: 「Decorative Fill」の各パラメータ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError setMdcDecorativeFillSurfaceParam(
      int regionNo, bool targetAll, MdcSewDecoParam prm);

  ///
  /// 「Decorative Fill」の各パラメータの設定を行い、選択状態のオブジェクトに反映する
  ///
  /// 引数[in]:
  /// - [int] regionNo: リージョン番号
  /// - [bool] targetAll            ：同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewDecoParam] prm: 「Decorative Fill」の各パラメータ
  /// - [ColorCode] codeColor: 色
  /// - [bool] decorativeTypeValue: デコラティブフィルタイプ
  /// - [int] decorativeNoValue: デコラティブフィル番号
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({
    MdcLibraryError errCode,
    MdcSewDecoParam prm,
    ColorCode codeColor,
    bool decorativeTypeValue,
    int decorativeNoValue
  }) getMdcDecorativeFillSurfaceParamInfo(int regionNo, bool targetAll);

  ///
  /// インフォメーションイメージデータの取得
  ///
  /// 768(W)×525(H)pixel。イメージは中央配置される。
  ///
  /// 引数[in]:
  /// - [bool] backDisplay : 背景表示　true 表示する, false 表示しない
  /// - [int] backColor    : 背景色　RGB値
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [EmbImg]          : イメージデータ情報
  ///
  ({MdcLibraryError errorCode, EmbImg embImg}) getInfoImage(
      bool backDisplay, int backColor);

  ///
  /// 押下座標のリストを記憶する
  ///
  /// 引数[in]:
  /// - [int] pointNum  : 登録する点の数(下記配列の要素数)
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({MdcLibraryError errCode, MdcReqProc reqProc}) reserveMdcLineList(
      int pointNum, List<SSPoint> pointList, MdcReqProc reqproc);

  ///
  /// 「4.3.5 描画データ通知関数」で記録された線データを描画したイメージを取得する
  ///
  /// 引数[in]:
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  /// - [MdcImageInfo  imgInfo] : 全体描画イメージ「2.2 お絵描きイメージ取得構造体(out用引数)」参照※αブレンドRGB画像を出力
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({MdcLibraryError errCode, MdcReqProc reqProc, MdcImageInfo imageInfo})
      getMdcReservedLineDraw(MdcReqProc reqProc);

  ///
  /// 一括線描画まとめ関数
  /// 直線描画を描く際にコールする関数
  ///
  /// 引数[in]:
  /// - [int] pointNum  : 登録する点の数(下記配列の要素数)
  /// - [SSPoint] pointList : 点座標のリスト
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  /// - [MdcImageInfo  imgInfo] : 全体描画イメージ「2.2 お絵描きイメージ取得構造体(out用引数)」参照※αブレンドRGB画像を出力
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.doLibImpUnit,
    """
    """,
  )
  ({MdcLibraryError errCode, MdcReqProc reqProc, MdcImageInfo imageInfo})
      editMdcReservedLine(
          int pointNum, List<SSPoint> pointList, MdcReqProc reqProc);

  ///
  /// 引数[in]:
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.libMaintenance,
    """
    """,
  )
  ({MdcLibraryError errCode, MdcReqProc reqProc}) noticeMdcDraw(
      MdcReqProc reqProc);

  ///
  /// MDC→EmbSelect遷移中エラー状態チェック
  ///
  /// 引数[in]:
  /// - [bool] undoRedoFlg      : undoRedoかどうか
  /// - [bool] scanImageFlg     : 下絵データ存在かどうか
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.libMaintenance,
    """
    """,
  )
  MdcLibraryError checkMDCPaintToEmbSelect(
    bool undoRedoFlg,
    bool scanImageFlg,
  );

  ///
  /// MDC [Canel]キー押下による刺繍カテゴリ選択画面への遷移
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.libMaintenance,
    """
    """,
  )
  MdcLibraryError gotoMDCPaintToEmbSelect();

  ///
  /// MDC Paint→MDC 縫製設定へ遷移
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///   - mdcNoError:エラーなし
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    """
    """,
  )
  MdcLibraryError gotoMDCPaintToMDCDetailSet();

  ///
  /// MDC 縫製設定→MDC Paintに戻る
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///   - mdcErrorInvalid:メインモータ動作中（無効音　遷移不可）
  ///   - mdcErrorNotInMode:MdcEditMode になっていない
  ///   - mdcNoError:エラーなし
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.ok,
    """
    """,
  )
  MdcLibraryError gotoMDCDetailSetToMDCPaint();

  ///
  /// MDC→EmbEdit遷移中エラー状態チェック
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    """
    """,
  )
  MdcLibraryError checkMDCDetailSetToEmbEdit();

  ///
  /// MDC 縫製設定→Emb Editへ遷移
  ///
  /// 戻り値  :
  /// - [EmbLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    """
    """,
  )
  MdcLibraryError gotoMDCDetailSetToEmbEdit();

  ///
  /// スキャンモードに移行する
  ///
  /// 引数[in]:
  /// - [String] fileName : ファイル名フルパス
  /// - [int] mode : モード(MDC_SCANMODE_LINE, MDC_SCANMODE_ILLUST)
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [CameraScanInfoAndData] : infoAndData スキャン後に取得したポートレート
  /// - [int]   mmsize_width : スキャンイメージの横幅 [x0.1mm]
  /// - [int]   mmsize_height: スキャンイメージの縦幅 [x0.1mm]
  /// - [MdcImageInfo] : imgInfo 画像情報
  ///
  ({MdcLibraryError errorCode, int width, int height, MdcImageInfo imgInfo})
      initScanMode(CameraScanInfoAndData infoAndData, int mode);

  ///
  /// トリミングサイズを変更する
  ///
  /// 引数[in]:
  /// - [MdcPoint] upperLeft : トリミング範囲左上座標
  /// - [MdcPoint] bottomRight : トリミング範囲右下座標
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [int] width : スキャンイメージの横幅 [x0.1mm]
  /// - [int] height: スキャンイメージの縦幅 [x0.1mm]
  ///
  ({MdcLibraryError errorCode, int widthTrimmingSize, int heightTrimmingSize})
      changeTrimmingSize(MdcPoint upperLeft, MdcPoint bottomRight);

  ///
  /// アウトライン画像を作成する
  ///
  /// 引数[in]:
  /// - [MdcScanOutlineDetectLevel] grayLevel : グレー検出レベル
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  ({MdcLibraryError errorCode, MdcImageInfo imgInfo}) convertToOutlineImage(
      int grayLevel);

  ///
  /// アウトライン画像を確定する
  ///
  /// 引数[in]:
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  /// - [MdcImageInfo] imgInfo : 生成イメージデータ格納
  ///
  ({
    MdcLibraryError errorCode,
    MdcReqProc reqProc,
    MdcUnsettledObjectInfo mdcUnsettledObjectInfo,
    MdcImageInfo imgInfoParts,
    MdcImageInfo backGroundImageInfo,
  }) confirmOutlineImage(MdcReqProc reqproc);

  ///
  /// イラスト画像を作成する
  ///
  /// 引数[in]:
  /// - [int] maxColor : 最大色数
  /// - [bool] drawLine : 線識別の有無
  /// - [int] lineWidth : 線識別単位[0.1mm]
  /// - [int] lineColor : 0x00RRGGBB
  /// - [bool] removeBack : 背景色除去
  /// - [int] transColor : 透過色(0x00RRGGBB) 全色指定はALLCOLOR_TRANS(0xFFFFFFFF)
  /// - [int] trans : 不透過率(0～100)　100以上は100とする 0は完全透過, 100が不透過
  /// - [MdcReqProc] reqproc : リクエストプロセス情報(単発リクエスト)
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcReqProc] reqproc : リクエストプロセス情報(単発リクエスト)
  /// - [MdcImageInfo] imgInfo : 生成イメージデータ格納
  ///
  ({MdcLibraryError errorCode, MdcImageInfo imgInfo}) convertToIllusrationImage(
    int maxColor,
    bool drawLine,
    int lineWidth,
    int lineColor,
    bool removeBack,
  );

  ///
  /// イラスト画像を確定する
  ///
  /// 引数[in]:
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  /// - [MdcImageInfo] imgInfoParts : 生成イメージデータ格納
  /// - [MdcImageInfo] imgInfoParts : 生成イメージデータ格納
  ///
  ({
    MdcLibraryError errorCode,
    MdcReqProc reqProc,
    MdcUnsettledObjectInfo mdcUnsettledObjectInfo,
    MdcImageInfo imgInfoParts,
    MdcImageInfo backGroundImageInfo,
  }) confirmIllusrationImage(MdcReqProc reqproc);

  ///
  /// イメージ取得後のデータ領域開放
  ///
  MdcLibraryError mdcReleaseImageInfo();

  ///
  /// インポートしたユーザー作成模様ファイルのファイル名リスト、件数を保存する関数
  ///
  /// 引数[in]:
  /// - [MdcImportFileList] importList : 線面ユーザー作成模様ファイルのファイル名リスト
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError setMdcImportFileList(MdcImportFileList importList);

  ///
  /// インポートしたユーザー作成模様ファイルのファイル名リスト、件数を保存する関数
  ///
  MdcLibraryError setMdcImageDrawingDensity(int drawingDensity);

  ///
  /// お絵描き画像の描画濃度を取得
  ///
  ({MdcLibraryError errorCode, int drawingDensity}) getMdcImageDrawingDensity();

  ///
  /// 取得データの確位置定関数
  ///
  ({MdcLibraryError errorCode, MdcReqProc reqProc, MdcImageInfo imageInfo})
      mdcSetImagePosition(MdcReqProc reqProc);

  ///
  /// 指定グループのイメージデータの抽出
  ///
  /// UI側でイメージを使わなっくなったら廃棄する必要がある。
  ///
  /// 引数[in]:
  /// - [MemHandle] memHandle         : イメージ取得したいグループのハンドル
  /// - [int] centerType : センタータイプ
  /// - [int] scale                   : 拡縮率
  /// - [bool] imageType              : イメージ種別
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [EmbImageInfo]       : イメージ出力先ファイル
  ///
  ({MdcLibraryError errorCode, EmbImageInfo imageInfo})
      getSelectedGroupARGBImage(
          MemHandle groupH, int centerType, int scale, bool imageType);

  ///
  /// 操作を保存する（レジューム、Undo/Redo操作）
  ///
  /// 引数[in]:
  /// - [String] fullPathFileName         : ファイル名
  ///
  /// 戻り値  :
  /// - [MdcLibraryError]   : エラーコード
  ///
  MdcLibraryError registMdcEditHistory(String fullPathFileName);

  ///
  /// 操作を元に戻す、操作を進める際の履歴データを読み込む
  ///
  /// 引数[in]:
  /// - [String] fullPathFileName : 履歴保存ファイル(フルパスファイル名)
  ///
  /// 引数[out]:
  /// - [MdcReqProc] reqProc : リクエストプロセス情報
  /// - [bool] existBackImage : 背景画像あり= true
  /// - [bool] existClipboard : クリップボードデータあり= true
  /// - [MdcUnsettledObjectInfo] objInfo : 編集操作情報
  /// - [MdcImageInfo] imgInfoParts : 部分模様描画イメージ
  /// - [MdcImageInfo] imgInfoBackGround : 全体描画イメージ
  /// - [MdcHistSubParam] subParam : 履歴データから抽出するパラメータ群
  /// - [MdcLibraryError]   : エラーコード
  ///
  ({
    MdcLibraryError errCode,
    MdcReqProc reqProc,
    bool existBackImage,
    bool existClipboard,
    MdcUnsettledObjectInfo objInfo,
    MdcImageInfo imgInfoParts,
    MdcImageInfo imgInfoBackGround,
    MdcHistSubParam subParam,
  }) loadMdcEditHistory(String fullPathFileName);

  ///
  /// イラスト画像を確定する
  ///
  /// 引数[in]:
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcReqProc] reqproc : リクエストプロセス情報
  /// - [MdcImageInfo] backGroundImageInfo : 生成イメージデータ格納
  ///
  ({
    MdcLibraryError errorCode,
    MdcReqProc reqProc,
    MdcImageInfo backGroundImageInfo,
  }) getMdcCurrentDrawingImage(MdcReqProc reqProc);

  ///
  /// 指定したスティップリング、デコラティブフィルの全体画像(お絵描きイメージ全体サイズの画像)を取得する
  ///
  /// 引数[in]:
  /// - [int] targetKind : リクエストプロセス情報
  /// - [int] targetNumber : 要求プロセスの数
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcImageInfo] imgInfo : 画像情報
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    "",
  )
  ({
    MdcLibraryError errorCode,
    MdcImageInfo imageInfo,
  }) getMdcStipplingDecofillMaxDrawingImage(int targetKind, int targetNumber);

  ///
  /// お絵描き模様の有無を確認する
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [bool]: result
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.doLibImpUnit,
    "androidSimの検証が完了し、マシンが検証を待っています",
  )
  ({
    MdcLibraryError errorCode,
    bool result,
  }) isMdcDrawingPatternPresence();

  ///
  /// お絵描きデータから刺繍データを作成するステッチ展開処理を開始する
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MemHandle]: ステッチ展開で生成した刺繍グループハンドル
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    "",
  )
  ({
    MdcLibraryError errorCode,
    MemHandle grpH,
  }) startMdcStitchCreation();
  Future<
      ({
        MdcLibraryError errorCode,
        MemHandle grpH,
      })> startMdcStitchCreationAsync();

  ///
  /// 実行中のステッチ展開処理をキャンセルさせる
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.doLibImpUnit,
    "",
  )
  MdcLibraryError cancelMdcStitchCreation();

  ///
  /// リージョン番号を指定して線の縫い種を変更する
  ///
  /// 引数[in]:
  /// - [int] regionNo                  : リージョン番号	※縫い種を変更するリージョン番号を指定する
  /// - [int] targetAll                 :
  /// - [MdcSewKinds_line] kind         : 変更先の縫い種
  /// - [int] motifType                 : 線モチーフタイプ	※kindが線モチーフの場合に指定される
  ///                                     (false:内蔵模様, true:ユーザー作成模様)
  /// - [int] motifNo                   : 線モチーフ番号	※kindが線モチーフの場合に指定される
  ///                                     (motifTypeがfalseの場合:1～内蔵模様最大数, 　motifTyepがtrueの場合:1～インポート数(最大12))
  /// - [int] color                     :
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    "",
  )
  MdcLibraryError updateMdcLineStitchTypeForRegion(
    int regionNo,
    bool targetAll,
    MdcSewKinds_line kind,
    bool motifType,
    int motifNo,
    ColorCode color,
  );

  ///
  /// リージョン番号を指定して面の縫い種を変更する
  ///
  /// 引数[in]:
  /// - [int] regionNo                    ：リージョン番号	※縫い種を変更するリージョン番号を指定する
  /// - [int] targetAll                 :
  /// - [MdcSewKinds_surface] kind        ：変更先の縫い種
  /// - [int] decorativeType              ：デコラティブフィルタイプ	※kindがデコラティブフィルの場合に指定される
  ///                                       (false:内蔵模様, true:ユーザー作成模様)
  /// - [int] motifNo                     ：デコラティブフィル番号	※kindがデコラティブフィルの場合に指定される
  ///                                       (decorativeTypeがfalseの場合:1～内蔵模様最大数, 　decorativeTypeがtrueの場合:1～インポート数(最大12))
  /// - [int] color                     :
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    "",
  )
  MdcLibraryError updateMdcSurfaceStitchTypeForRegion(
    int regionNo,
    bool targetAll,
    MdcSewKinds_surface kind,
    bool decorativeType,
    int decorativeNo,
    ColorCode color,
  );

  ///
  /// ジグザグ(サテン)ステッチ縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  ///
  /// 引数[in]:
  /// - [int] regionNo              ：リージョン番号
  /// - [bool] targetAll            ：同一縫い種一括指定
  /// - [MdcSewZigzagParam] prm     ：モチーフの各パラメータ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    "",
  )
  MdcLibraryError setMdcZigzagLineParam(
    int regionNo,
    bool targetAll,
    MdcSewZigzagParam prm,
  );

  ///
  /// ランニングステッチ縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  ///
  /// 引数[in]:
  /// - [int] regionNo              ：リージョン番号
  /// - [bool] targetAll            ：同一縫い種一括指定
  /// - [MdcSewRunningParam] prm    ：モチーフの各パラメータ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    "",
  )
  MdcLibraryError setMdcRunningStitchLineParam(
    int regionNo,
    bool targetAll,
    MdcSewRunningParam prm,
  );

  ///
  /// トリプルステッチ縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  ///
  /// 引数[in]:
  /// - [int] regionNo              ：リージョン番号
  /// - [bool] targetAll            ：同一縫い種一括指定
  /// - [MdcSewRunningParam] prm    ：モチーフの各パラメータ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    "",
  )
  MdcLibraryError setMdcTripleStitchLineParam(
    int regionNo,
    bool targetAll,
    MdcSewTripleParam prm,
  );

  ///
  /// キャンドルウィック縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  ///
  /// 引数[in]:
  /// - [int] regionNo              ：リージョン番号
  /// - [bool] targetAll            ：同一縫い種一括指定
  /// - [MdcSewCandleParam] prm     ：モチーフの各パラメータ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    "",
  )
  MdcLibraryError setMdcCandlwickingLineParam(
    int regionNo,
    bool targetAll,
    MdcSewCandleParam prm,
  );

  ///
  /// チェーンステッチ縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  ///
  /// 引数[in]:
  /// - [int] regionNo              ：リージョン番号
  /// - [bool] targetAll            ：同一縫い種一括指定
  /// - [MdcSewChainParam] prm      ：モチーフの各パラメータ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    "",
  )
  MdcLibraryError setMdcChainStitchLineParam(
    int regionNo,
    bool targetAll,
    MdcSewChainParam prm,
  );

  ///
  /// Eステッチ縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  ///
  /// 引数[in]:
  /// - [int] regionNo              ：リージョン番号
  /// - [bool] targetAll            ：同一縫い種一括指定
  /// - [MdcSewEParam] prm          ：モチーフの各パラメータ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    "",
  )
  MdcLibraryError setMdcEStitchLineParam(
    int regionNo,
    bool targetAll,
    MdcSewEParam prm,
  );

  ///
  /// 粗いジグザグ縫い縫製パラメータを設定して、選択状態のオブジェクトに反映する関数
  ///
  /// 引数[in]:
  /// - [int] regionNo                    ：リージョン番号
  /// - [bool] targetAll                  ：同一縫い種一括指定
  /// - [MdcSewRoughZigzagParam] prm      ：モチーフの各パラメータ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    "",
  )
  MdcLibraryError setMdcRoughZigzagLineParam(
    int regionNo,
    bool targetAll,
    MdcSewRoughZigzagParam prm,
  );

  ///
  /// 粗いジグザグ縫い縫製パラメータや色情報の取得
  ///
  /// 引数[in]:
  /// - [int] regionNo                    ：リージョン番号
  /// - [bool] targetAll                  ：同一縫い種一括指定
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewRoughZigzagParam] :
  /// - [int] :
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    "",
  )
  ({
    MdcLibraryError errorCode,
    MdcSewRoughZigzagParam prm,
    ColorCode color,
  }) getMdcRoughZigzagLineParamInfo(
    int regionNo,
    bool targetAll,
  );

  ///
  /// 全縫製パラメータのデフォルト値を取得する
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [MdcSewLineAllParam] :  線の縫い種の縫製パラメータデフォルト値
  /// - [MdcSewSurfaceAllParam] : 面の縫い種の縫製パラメータデフォルト値
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    "",
  )
  ({
    MdcLibraryError errorCode,
    MdcSewLineAllParam lineParam,
    MdcSewSurfaceAllParam surfParam,
  }) getMdcSewParamDefaultData();
}

///
/// 簡単修正為に
/// BSH追加したのAPI mixinでMdcLibFunctionInterfaceに追加する。
///
///
mixin _ExtensionFunction {
  ///
  /// 内蔵 デコラティブフィルの総数を取得する
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [int]             : デコラティブフィルの総数
  ///
  ({MdcLibraryError errorCode, int totalNum})
      getMdcBuiltInDecorativeFillTotalNum();

  ///
  /// 内蔵 デコラティブフィルのサムネイルを取得する
  ///
  /// 引数[in]:
  /// - [int] number      : 選択した模様番号
  /// - [int] width       : 表示サムネイルの幅
  /// - [int] height      : 表示サムネイルの長さ
  /// - [Color] lineColor : 表示サムネイル線の色
  ///
  /// 戻り値  :
  /// - [MdcLibraryError]   : エラーコード
  /// - [MdcThumbImageInfo] : イメージデータ情報
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.doUiImp,
    "Android Simでテストは終了し、問題がないです。 今の時点で実機入れないのでテストできないので、UI実装時に確認お願い",
  )
  ({MdcLibraryError errorCode, MdcThumbImageInfo imageInfo})
      getMdcBuiltInDecorativeFillThumbnail(
          int number, int width, int height, Color lineColor);

  ///
  /// 内蔵 線モチーフの総数を取得する
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [int]             : 線モチーフの総数
  ///
  ({MdcLibraryError errorCode, int totalNum}) getMdcBuiltInLineMotifTotalNum();

  ///
  /// 内蔵 線モチーフのサムネイルを取得する
  ///
  /// 引数[in]:
  /// - [int] number      : 選択した模様番号
  /// - [int] width       : 表示サムネイルの幅
  /// - [int] height      : 表示サムネイルの長さ
  /// - [Color] lineColor : 表示サムネイル線の色
  ///
  /// 戻り値  :
  /// - [MdcLibraryError]   : エラーコード
  /// - [MdcThumbImageInfo] : イメージデータ情報
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.doUiImp,
    "Android Simでテストは終了し、問題がないです。 今の時点で実機入れないのでテストできないので、UI実装時に確認お願い",
  )
  ({MdcLibraryError errorCode, MdcThumbImageInfo imageInfo})
      getMdcBuiltInLineMotifThumbnail(
          int number, int width, int height, Color lineColor);

  ///
  /// カスタム デコラティブフィルのサムネイルを取得する
  ///
  /// 引数[in]:
  /// - [String] filename : 選択した模様のフルパス
  /// - [int] width       : 表示サムネイルの幅
  /// - [int] height      : 表示サムネイルの長さ
  /// - [Color] lineColor : 表示サムネイル線の色
  ///
  /// 戻り値  :
  /// - [MdcLibraryError]   : エラーコード
  /// - [MdcThumbImageInfo] : イメージデータ情報
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.doUiImp,
    "Android Simでテストは終了し、問題がないです。 今の時点で実機入れないのでテストできないので、UI実装時に確認お願い",
  )
  ({MdcLibraryError errorCode, MdcThumbImageInfo imageInfo})
      getMdcCustomDecorativeFillThumbnail(
          String filename, int width, int height, Color lineColor);

  ///
  /// カスタム 線モチーフのサムネイルを取得する
  ///
  /// 引数[in]:
  /// - [String] filename : 選択した模様のフルパス
  /// - [int] width       : 表示サムネイルの幅
  /// - [int] height      : 表示サムネイルの長さ
  /// - [Color] lineColor : 表示サムネイル線の色
  ///
  /// 戻り値  :
  /// - [MdcLibraryError]   : エラーコード
  /// - [MdcThumbImageInfo] : イメージデータ情報
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.doUiImp,
    "Android Simでテストは終了し、問題がないです。 今の時点で実機入れないのでテストできないので、UI実装時に確認お願い",
  )
  ({MdcLibraryError errorCode, MdcThumbImageInfo imageInfo})
      getMdcCustomLineMotifThumbnail(
          String filename, int width, int height, Color lineColor);

  ///
  /// MDCで取得したサムネイルイメージデータを削除する
  /// ※ライブラリ内で確保したBMPイメージのメモリ解放
  ///
  MdcLibraryError delMdcThumbImage();

  ///
  /// 内蔵 指定スタンプの総数を取得する
  ///
  /// ※基本30スタンプだが変更された場合に分かるよう追加
  ///
  /// 引数[in]:
  /// - [MdcStampTypes] kind : スタンプ図形のタブ選択種別
  ///                         ・通常スタンプ(線)
  ///                         ・通常スタンプ(塗りつぶし)
  ///                         ・通常スタンプ(線＋塗りつぶし)
  ///                         ・閉じた線スタンプ
  ///                         ・開いた線スタンプ
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [int]             : 指定スタンプの総数
  ///
  ({MdcLibraryError errorCode, int totalNum}) getMdcBuiltInSpecifyStampTotalNum(
      MdcStampTypes kind);

  ///
  /// 内蔵 指定スタンプのサムネイルを取得する
  ///
  /// 引数[in]:
  /// - [MdcStampTypes] kind : スタンプ図形のタブ選択種別
  ///                         ・通常スタンプ(線)
  ///                         ・通常スタンプ(塗りつぶし)
  ///                         ・通常スタンプ(線＋塗りつぶし)
  ///                         ・閉じた線スタンプ
  ///                         ・開いた線スタンプ
  /// - [int] number : 選択した模様番号
  /// - [int] width  : 表示サムネイルの幅
  /// - [int] height : 表示サムネイルの長さ
  /// - [Color] lineColor    : lineColor
  /// - [Color] surfaceColor : surfaceColor
  ///
  /// 戻り値  :
  /// - [MdcLibraryError]   : エラーコード
  /// - [MdcThumbImageInfo] : イメージデータ情報
  ///
  ({MdcLibraryError errorCode, MdcThumbImageInfo imageInfo})
      getMdcBuiltInSpecifyStampThumbnail(
    MdcStampTypes kind,
    int number,
    int width,
    int height,
    Color lineColor,
    Color surfaceColor,
  );

  ///
  /// アウトライン 指定スタンプのサムネイルを取得する
  ///
  /// 引数[in]:
  /// - [String] filename : 選択した模様のフルパス
  /// - [int] width       : 表示サムネイルの幅
  /// - [int] height      : 表示サムネイルの長さ
  /// - [Color] lineColor : lineColor
  ///
  /// 戻り値  :
  /// - [MdcLibraryError]   : エラーコード
  /// - [MdcThumbImageInfo] : イメージデータ情報
  ///
  ({MdcLibraryError errorCode, MdcThumbImageInfo imageInfo})
      getMdcOutlineStampThumbnail(
    String filename,
    int width,
    int height,
    Color lineColor,
  );

  ///
  /// SNC連携 指定スタンプのサムネイルを取得する
  ///
  /// 引数[in]:
  /// - [String] filename : 選択した模様のフルパス
  /// - [int] width       : 表示サムネイルの幅
  /// - [int] height      : 表示サムネイルの長さ
  /// - [Color] lineColor : lineColor
  ///
  /// 戻り値  :
  /// - [MdcLibraryError]   : エラーコード
  /// - [MdcThumbImageInfo] : イメージデータ情報
  ///
  ({MdcLibraryError errorCode, MdcThumbImageInfo imageInfo})
      getMdcCuttingStampThumbnail(
    String filename,
    int width,
    int height,
    Color lineColor,
  );

  ///
  /// お絵描きで使用中のユーザー作成模様番号を確認する
  ///
  /// 引数[bool]: allChkFlg
  ///
  /// 戻り値  :
  /// - [MdcLibraryError]   : エラーコード
  /// - [MdcImportDataUsingNumber] usingNumBuff : 使用中のユーザー作成模様番号出力バッファ
  ///
  ({MdcLibraryError errorCode, MdcImportDataUsingNumber usingNumBuff})
      isMdcUsingImportDataNumber(bool allChkFlg);

  ///
  /// 表示領域を記憶保存する（PHXファイル）
  ///
  /// 引数[in]:
  /// - [String] filename : 保存模様のフルパス
  ///
  /// 戻り値  :
  /// - [MdcLibraryError]   : エラーコード
  ///
  MdcLibraryError saveMdcPHXFile(String filename);

  ///
  /// FCM を PCE に変換してファイル保存する
  ///
  /// 引数[in]:
  /// - [Uint8List] fcmData : Httpから取得したのFCMデータ
  /// - [int] fcmDataSize : FCMデータサイズ
  /// - [String] filenamePce : PCEファイルパス ※フルパス指定
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  ///
  MdcLibraryError saveFcmDataToPce(
      Uint8List fcmData, int fcmDataSize, String filenamePce);

  ///
  /// PMFファイル有効かどうか
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [bool]: result
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.libMaintenance,
    "",
  )
  ({
    MdcLibraryError errorCode,
    bool result,
  }) isMdcPmfFileValid(String fileName);

  ///
  /// PLFファイル有効かどうか
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [bool]: result
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.libMaintenance,
    "",
  )
  ({
    MdcLibraryError errorCode,
    bool result,
  }) isMdcPlfFileValid(String fileName);

  ///
  /// 操Resume時に復帰する履歴データを読み込む
  ///
  /// 引数[in]:
  /// - [String] fullPathFileName : 履歴保存ファイル(フルパスファイル名)
  ///
  /// 引数[out]:
  /// - [MdcReqProc] reqProc : リクエストプロセス情報
  /// - [bool] existBackImage : 背景画像あり= true
  /// - [bool] existClipboard : クリップボードデータあり= true
  /// - [MdcUnsettledObjectInfo] objInfo : 編集操作情報
  /// - [MdcImageInfo] imgInfoParts : 部分模様描画イメージ
  /// - [MdcImageInfo] imgInfoBackGround : 全体描画イメージ
  /// - [MdcHistSubParam] subParam : 履歴データから抽出するパラメータ群
  /// - [MdcLibraryError]   : エラーコード
  ///                         - mdcNoError : エラーなし
  ///                         - mdcNoHistoryRegist : 履歴なし
  ///                         - mdcErrorWrongParam : パラメータエラー
  ///                         - mdcErrorDataAcquisitionFailure : データ取得失敗　※履歴データ読み込み失敗
  ///                         - mdcErrorDefault : 内部エラー
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.ok,
    "",
  )
  ({
    MdcLibraryError errCode,
    MdcReqProc reqProc,
    bool existBackImage,
    bool existClipboard,
    MdcUnsettledObjectInfo objInfo,
    MdcImageInfo imgInfoParts,
    MdcImageInfo imgInfoBackGround,
    MdcHistSubParam subParam,
  }) loadMdcEditHistoryResume(String fullPathFileName);

  ///
  /// 全てのリージョン情報（リージョン番号、縫い種、領域情報）を取得する
  ///
  /// 引数[in]:
  /// - なし
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [List<RegionInfoAndNumber>] regionInfoBuf : リージョン情報バッファ
  /// - [int] bufNum : リージョン情報バッファ
  /// - [int] curRegionNo : ライブラリ内で選択状態となっているリージョン番号
  ///                       ※縫製パラメータ、色、縫い種などの変更で、同じリージョンなのに変更前後で番号が変わってしまう場合があるため、どれが選択中なのかを通知するために出力
  ///
  @MdcApiResult(
    MdcApiFunction.mdcStitch,
    ApiStatus.libMaintenance,
    "",
  )
  ({
    MdcLibraryError errorCode,
    List<RegionInfoAndNumber> regionInfoBuf,
    int bufNum,
    int curRegionNo,
  }) getMdcEditRegionInfoAllData(int inputBufNum);

  ///
  /// お絵描き模様の有無を確認する関数（Next）
  ///
  /// 戻り値  :
  /// - [MdcLibraryError] : エラーコード
  /// - [bool]: result
  ///
  @MdcApiResult(
    MdcApiFunction.mdcPaint,
    ApiStatus.doLibImpUnit,
    "",
  )
  ({
    MdcLibraryError errorCode,
    bool result,
  }) isMdcDrawingPatternPresenceForNext(MdcReqProc reqProc);
}
