import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'top_bar_view_model.dart';

class TopBar extends ConsumerStatefulWidget {
  const TopBar({Key? key}) : super(key: key);

  @override
  ConsumerState<TopBar> createState() => _TopBarState();
}

class _TopBarState extends ConsumerState<TopBar> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;

    final state = ref.watch(topBarViewModelProvider);
    final viewModel = ref.read(topBarViewModelProvider.notifier);

    return Row(
      children: [
        const Spacer(flex: 12),
        Expanded(
          flex: 133,
          child: grp_btn_zoom(
            feedBackControl: FeedBackControl.onlyDisable,
            onTap: () => viewModel.onMagnificationButtonClicked(context),
            child: viewModel.getMagnificationLevel,
          ),
        ),
        const Spacer(flex: 8),
        Expanded(
          flex: 63,
          child: CustomTooltip(
            message: l10n.tt_mdc_pantool,
            child: grp_btn_pan_tool(
              feedBackControl: FeedBackControl.onlyDisable,
              onTap: () => viewModel.onDragMoveButtonClicked(context),
              state: state.dragMoveButtonState,
            ),
          ),
        ),

        /// 背景画像濃淡調整
        state.isDensityShow == false
            ? Expanded(
                flex: 344,
                child: Container(),
              )
            : Expanded(
                flex: 344,
                child: Row(children: [
                  const Spacer(flex: 24),
                  Expanded(
                    flex: 63,
                    child: grp_btn_background_thin(
                      onTap: () =>
                          viewModel.onDensityReduceButtonClicked(context),
                      feedBackControl: FeedBackControl.onlyDisable,
                    ),
                  ),
                  const Spacer(flex: 8),
                  Expanded(
                      flex: 178,
                      child: Stack(
                        children: [
                          Column(
                            children: [
                              const Spacer(
                                flex: 22,
                              ),
                              Expanded(
                                  flex: 15,
                                  child: Row(
                                    children: [
                                      Spacer(
                                          flex: [
                                        1,
                                        31,
                                        61,
                                        91,
                                        121,
                                        151,
                                      ][state.densityLevelIndex]),
                                      const Expanded(
                                        flex: 22,
                                        child: pic_ingecator_memory_01(),
                                      ),
                                      Spacer(
                                          flex: [
                                        151,
                                        121,
                                        91,
                                        61,
                                        31,
                                        1,
                                      ][state.densityLevelIndex]),
                                    ],
                                  )),
                              const Spacer(
                                flex: 8,
                              ),
                              const Expanded(
                                flex: 19,
                                child: Row(
                                  children: [
                                    Spacer(
                                      flex: 12,
                                    ),
                                    Expanded(
                                      flex: 154,
                                      child: pre_indicator6_pale(),
                                    ),
                                    Spacer(
                                      flex: 12,
                                    ),
                                  ],
                                ),
                              ),
                              const Spacer(
                                flex: 24,
                              ),
                            ],
                          ),
                        ],
                      )),
                  const Spacer(flex: 8),
                  Expanded(
                    flex: 63,
                    child: grp_btn_background_dark(
                      onTap: () => viewModel.onDensityAddButtonClicked(context),
                      feedBackControl: FeedBackControl.onlyDisable,
                    ),
                  ),
                ]),
              ),

        const Spacer(flex: 94),
        Expanded(
          flex: 63,
          child: CustomTooltip(
            message: l10n.tt_mdc_scanmenu,
            child: grp_btn_mdcImage(
              feedBackControl: FeedBackControl.onlyDisable,
              onTap: () => viewModel.onMdcImageButtonClicked(context),
            ),
          ),
        ),

        const Spacer(flex: 8),
        Expanded(
          flex: 63,
          child: CustomTooltip(
            message: l10n.tt_mdc_datacall,
            child: grp_btn_dcdatacall(
              feedBackControl: FeedBackControl.onlyDisable,
              onTap: () => viewModel.onMemoryImportButtonClicked(context),
            ),
          ),
        ),

        const Spacer(flex: 13),
      ],
    );
  }
}
