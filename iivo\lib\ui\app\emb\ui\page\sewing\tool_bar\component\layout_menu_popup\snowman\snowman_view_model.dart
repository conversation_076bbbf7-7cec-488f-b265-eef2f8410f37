import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../../../model/projector_model.dart';
import '../../../../../../../../../global_popup/global_popup_export.dart';
import '../../../../../../../model/snowman_model.dart';
import 'component/snowman_controller.dart';
import 'snowman_interface.dart';

///
/// 縫製パターンの最大表示領域
/// WidthMax = 462; HeightMax = 462
///
const double _patternMaxDisplayArea = 462;

/// 取得の画像
Image? scanPreviewImage;

final snowmanViewModelProvider = StateNotifierProvider.autoDispose<
    SnowmanViewModelInterface,
    SnowmanInterface>((ref) => SnowmanViewModel(ref));

class SnowmanViewModel extends SnowmanViewModelInterface {
  SnowmanViewModel(Ref ref) : super(const SnowmanInterface(), ref) {
    var result = EmbLibrary().apiBinding.getRealImageLight(true);
    snowmanController = SnowmanController(stitchData: result.needlePos);
    var patternSize = snowmanController.getPatternSize();

    SnowmanMarkDirectionEnum snowmanMarkDirection =
        SnowmanModel.getSnowmanMarkDirection();

    MarkDirection markDirection =
        _changeSnowmanMarkDirectionToMarkDirection(snowmanMarkDirection);

    double? patternWidth = patternSize.patternWidth;
    double? patternHeight = patternSize.patternHeight;

    /// 最大領域に応じてパターンを拡大または縮小する
    if (patternWidth > patternHeight) {
      patternHeight *= _patternMaxDisplayArea / patternWidth;
      patternWidth = _patternMaxDisplayArea;
    } else {
      patternWidth *= _patternMaxDisplayArea / patternHeight;
      patternHeight = _patternMaxDisplayArea;
    }

    /// View更新
    state = state.copyWith(
        patternHeight: patternHeight,
        patternWidth: patternWidth,
        markDirection: markDirection);
  }

  ///
  /// RecognizePreview更新関数
  ///
  Image? sendImageToRecognize() => scanPreviewImage;

  ///
  /// タグ位置キーのクリック関数
  ///
  @override
  void onMarkPositionButtonClicked(MarkDirection markDirection) {
    /// 選択したときに無効なクリック
    if (state.markDirection == markDirection) {
      return;
    }

    /// Model更新
    SnowmanModel.setSnowManMarkPosition(
        _changeMarkDirectionToSnowmanMarkDirection(markDirection));
    SnowmanModel.setSnowmanMarkDirection(
        _changeMarkDirectionToSnowmanMarkDirection(markDirection));

    /// View更新
    state = state.copyWith(markDirection: markDirection);
  }

  ///
  /// Cancelキーのクリック関数
  ///
  @override
  void onCancelButtonClicked(BuildContext context) {
    EmbLibrary().apiBinding.embSewingSnowmanScanCancel();
    PopupNavigator.pop(context: context);
  }

  @override
  void onScanButtonClicked() {
    EmbLibraryError error = EmbLibrary().apiBinding.embSewingSnowmanScanOk();

    if (error == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else if (error == EmbLibraryError.EMB_INVALID_ERR_PANEL) {
      final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
      if (bPIFErrorPointer.errorCode ==
          ErrCode_t.ERR_EMB_POSITIONING_WARNING.index) {
        /// [170	刺繍キャリッジが動きます、手や物を離してください] このポップアップは、scanボタンをクリックした後に表示されます。
        /// ポップアップのOKボタンをクリックするとスキャンが始まるため、OKをクリックした際にはまずポップアップを閉じます。
        errEmbPositioningWarningOkButtonFunc = () {
          /// 投影が閉じられ、理論上ここではW押えの投影のみが開くはずです
          if (ProjectorModel().embProjector.isEmbProjectorOpened() == true) {
            ProjectorModel().embProjector.closeEmbProjector();
          } else {
            /// Do Nothing
          }
        };

        SystemSoundPlayer().play(SystemSoundEnum.accept);
        return;
      } else {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
    } else if (error != EmbLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }
  }

  ///
  /// ViewのタイプをModelのタイプに変換する(タグ位置)
  ///
  SnowmanMarkDirectionEnum _changeMarkDirectionToSnowmanMarkDirection(
      MarkDirection markDirection) {
    switch (markDirection) {
      case MarkDirection.leftUp:
        return SnowmanMarkDirectionEnum.leftUp;
      case MarkDirection.up:
        return SnowmanMarkDirectionEnum.up;
      case MarkDirection.rightUp:
        return SnowmanMarkDirectionEnum.rightUp;
      case MarkDirection.left:
        return SnowmanMarkDirectionEnum.left;
      case MarkDirection.right:
        return SnowmanMarkDirectionEnum.right;
      case MarkDirection.leftDown:
        return SnowmanMarkDirectionEnum.leftDown;
      case MarkDirection.down:
        return SnowmanMarkDirectionEnum.down;
      case MarkDirection.rightDown:
        return SnowmanMarkDirectionEnum.rightDown;
      case MarkDirection.center:
      default:
        return SnowmanMarkDirectionEnum.center;
    }
  }

  ///
  /// ModelのタイプをViewのタイプに変換する(タグ位置)
  ///
  MarkDirection _changeSnowmanMarkDirectionToMarkDirection(
      SnowmanMarkDirectionEnum snowmanMarkDirection) {
    switch (snowmanMarkDirection) {
      case SnowmanMarkDirectionEnum.leftUp:
        return MarkDirection.leftUp;
      case SnowmanMarkDirectionEnum.up:
        return MarkDirection.up;
      case SnowmanMarkDirectionEnum.rightUp:
        return MarkDirection.rightUp;
      case SnowmanMarkDirectionEnum.left:
        return MarkDirection.left;
      case SnowmanMarkDirectionEnum.right:
        return MarkDirection.right;
      case SnowmanMarkDirectionEnum.leftDown:
        return MarkDirection.leftDown;
      case SnowmanMarkDirectionEnum.down:
        return MarkDirection.down;
      case SnowmanMarkDirectionEnum.rightDown:
        return MarkDirection.rightDown;
      case SnowmanMarkDirectionEnum.center:
      default:
        return MarkDirection.center;
    }
  }
}
