// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'filter_result_view_interface.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FilterResultState {
  int get frameIndex => throw _privateConstructorUsedError;
  bool get isSelectedPattern => throw _privateConstructorUsedError;
  bool get showZoomList => throw _privateConstructorUsedError;
  int get selectedZoomScale => throw _privateConstructorUsedError;
  String get heightValue => throw _privateConstructorUsedError;
  String get widthValue => throw _privateConstructorUsedError;
  String get totalTimeValue => throw _privateConstructorUsedError;
  String get numberOfColorsValue => throw _privateConstructorUsedError;
  String get colorChangesValue => throw _privateConstructorUsedError;
  bool get showFilterButton => throw _privateConstructorUsedError;
  List<Image> get imageGroup => throw _privateConstructorUsedError;
  bool get isFilterApplied => throw _privateConstructorUsedError;
  int get thumbnailSize => throw _privateConstructorUsedError;
  bool get setButtonEnable => throw _privateConstructorUsedError;
  bool get isSelectSortLeft => throw _privateConstructorUsedError;
  ButtonState get formationButtonState => throw _privateConstructorUsedError;
  ButtonState get realPreviewButtonState => throw _privateConstructorUsedError;

  /// Pattern表示情報
  List<PatternDisplayInfo> get patternDisplayInfoList =>
      throw _privateConstructorUsedError;
  List<PatternDisplayInfo> get temporaryGroupDisplayInfoList =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $FilterResultStateCopyWith<FilterResultState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FilterResultStateCopyWith<$Res> {
  factory $FilterResultStateCopyWith(
          FilterResultState value, $Res Function(FilterResultState) then) =
      _$FilterResultStateCopyWithImpl<$Res, FilterResultState>;
  @useResult
  $Res call(
      {int frameIndex,
      bool isSelectedPattern,
      bool showZoomList,
      int selectedZoomScale,
      String heightValue,
      String widthValue,
      String totalTimeValue,
      String numberOfColorsValue,
      String colorChangesValue,
      bool showFilterButton,
      List<Image> imageGroup,
      bool isFilterApplied,
      int thumbnailSize,
      bool setButtonEnable,
      bool isSelectSortLeft,
      ButtonState formationButtonState,
      ButtonState realPreviewButtonState,
      List<PatternDisplayInfo> patternDisplayInfoList,
      List<PatternDisplayInfo> temporaryGroupDisplayInfoList});
}

/// @nodoc
class _$FilterResultStateCopyWithImpl<$Res, $Val extends FilterResultState>
    implements $FilterResultStateCopyWith<$Res> {
  _$FilterResultStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? frameIndex = null,
    Object? isSelectedPattern = null,
    Object? showZoomList = null,
    Object? selectedZoomScale = null,
    Object? heightValue = null,
    Object? widthValue = null,
    Object? totalTimeValue = null,
    Object? numberOfColorsValue = null,
    Object? colorChangesValue = null,
    Object? showFilterButton = null,
    Object? imageGroup = null,
    Object? isFilterApplied = null,
    Object? thumbnailSize = null,
    Object? setButtonEnable = null,
    Object? isSelectSortLeft = null,
    Object? formationButtonState = null,
    Object? realPreviewButtonState = null,
    Object? patternDisplayInfoList = null,
    Object? temporaryGroupDisplayInfoList = null,
  }) {
    return _then(_value.copyWith(
      frameIndex: null == frameIndex
          ? _value.frameIndex
          : frameIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isSelectedPattern: null == isSelectedPattern
          ? _value.isSelectedPattern
          : isSelectedPattern // ignore: cast_nullable_to_non_nullable
              as bool,
      showZoomList: null == showZoomList
          ? _value.showZoomList
          : showZoomList // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedZoomScale: null == selectedZoomScale
          ? _value.selectedZoomScale
          : selectedZoomScale // ignore: cast_nullable_to_non_nullable
              as int,
      heightValue: null == heightValue
          ? _value.heightValue
          : heightValue // ignore: cast_nullable_to_non_nullable
              as String,
      widthValue: null == widthValue
          ? _value.widthValue
          : widthValue // ignore: cast_nullable_to_non_nullable
              as String,
      totalTimeValue: null == totalTimeValue
          ? _value.totalTimeValue
          : totalTimeValue // ignore: cast_nullable_to_non_nullable
              as String,
      numberOfColorsValue: null == numberOfColorsValue
          ? _value.numberOfColorsValue
          : numberOfColorsValue // ignore: cast_nullable_to_non_nullable
              as String,
      colorChangesValue: null == colorChangesValue
          ? _value.colorChangesValue
          : colorChangesValue // ignore: cast_nullable_to_non_nullable
              as String,
      showFilterButton: null == showFilterButton
          ? _value.showFilterButton
          : showFilterButton // ignore: cast_nullable_to_non_nullable
              as bool,
      imageGroup: null == imageGroup
          ? _value.imageGroup
          : imageGroup // ignore: cast_nullable_to_non_nullable
              as List<Image>,
      isFilterApplied: null == isFilterApplied
          ? _value.isFilterApplied
          : isFilterApplied // ignore: cast_nullable_to_non_nullable
              as bool,
      thumbnailSize: null == thumbnailSize
          ? _value.thumbnailSize
          : thumbnailSize // ignore: cast_nullable_to_non_nullable
              as int,
      setButtonEnable: null == setButtonEnable
          ? _value.setButtonEnable
          : setButtonEnable // ignore: cast_nullable_to_non_nullable
              as bool,
      isSelectSortLeft: null == isSelectSortLeft
          ? _value.isSelectSortLeft
          : isSelectSortLeft // ignore: cast_nullable_to_non_nullable
              as bool,
      formationButtonState: null == formationButtonState
          ? _value.formationButtonState
          : formationButtonState // ignore: cast_nullable_to_non_nullable
              as ButtonState,
      realPreviewButtonState: null == realPreviewButtonState
          ? _value.realPreviewButtonState
          : realPreviewButtonState // ignore: cast_nullable_to_non_nullable
              as ButtonState,
      patternDisplayInfoList: null == patternDisplayInfoList
          ? _value.patternDisplayInfoList
          : patternDisplayInfoList // ignore: cast_nullable_to_non_nullable
              as List<PatternDisplayInfo>,
      temporaryGroupDisplayInfoList: null == temporaryGroupDisplayInfoList
          ? _value.temporaryGroupDisplayInfoList
          : temporaryGroupDisplayInfoList // ignore: cast_nullable_to_non_nullable
              as List<PatternDisplayInfo>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FilterResultStateImplCopyWith<$Res>
    implements $FilterResultStateCopyWith<$Res> {
  factory _$$FilterResultStateImplCopyWith(_$FilterResultStateImpl value,
          $Res Function(_$FilterResultStateImpl) then) =
      __$$FilterResultStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int frameIndex,
      bool isSelectedPattern,
      bool showZoomList,
      int selectedZoomScale,
      String heightValue,
      String widthValue,
      String totalTimeValue,
      String numberOfColorsValue,
      String colorChangesValue,
      bool showFilterButton,
      List<Image> imageGroup,
      bool isFilterApplied,
      int thumbnailSize,
      bool setButtonEnable,
      bool isSelectSortLeft,
      ButtonState formationButtonState,
      ButtonState realPreviewButtonState,
      List<PatternDisplayInfo> patternDisplayInfoList,
      List<PatternDisplayInfo> temporaryGroupDisplayInfoList});
}

/// @nodoc
class __$$FilterResultStateImplCopyWithImpl<$Res>
    extends _$FilterResultStateCopyWithImpl<$Res, _$FilterResultStateImpl>
    implements _$$FilterResultStateImplCopyWith<$Res> {
  __$$FilterResultStateImplCopyWithImpl(_$FilterResultStateImpl _value,
      $Res Function(_$FilterResultStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? frameIndex = null,
    Object? isSelectedPattern = null,
    Object? showZoomList = null,
    Object? selectedZoomScale = null,
    Object? heightValue = null,
    Object? widthValue = null,
    Object? totalTimeValue = null,
    Object? numberOfColorsValue = null,
    Object? colorChangesValue = null,
    Object? showFilterButton = null,
    Object? imageGroup = null,
    Object? isFilterApplied = null,
    Object? thumbnailSize = null,
    Object? setButtonEnable = null,
    Object? isSelectSortLeft = null,
    Object? formationButtonState = null,
    Object? realPreviewButtonState = null,
    Object? patternDisplayInfoList = null,
    Object? temporaryGroupDisplayInfoList = null,
  }) {
    return _then(_$FilterResultStateImpl(
      frameIndex: null == frameIndex
          ? _value.frameIndex
          : frameIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isSelectedPattern: null == isSelectedPattern
          ? _value.isSelectedPattern
          : isSelectedPattern // ignore: cast_nullable_to_non_nullable
              as bool,
      showZoomList: null == showZoomList
          ? _value.showZoomList
          : showZoomList // ignore: cast_nullable_to_non_nullable
              as bool,
      selectedZoomScale: null == selectedZoomScale
          ? _value.selectedZoomScale
          : selectedZoomScale // ignore: cast_nullable_to_non_nullable
              as int,
      heightValue: null == heightValue
          ? _value.heightValue
          : heightValue // ignore: cast_nullable_to_non_nullable
              as String,
      widthValue: null == widthValue
          ? _value.widthValue
          : widthValue // ignore: cast_nullable_to_non_nullable
              as String,
      totalTimeValue: null == totalTimeValue
          ? _value.totalTimeValue
          : totalTimeValue // ignore: cast_nullable_to_non_nullable
              as String,
      numberOfColorsValue: null == numberOfColorsValue
          ? _value.numberOfColorsValue
          : numberOfColorsValue // ignore: cast_nullable_to_non_nullable
              as String,
      colorChangesValue: null == colorChangesValue
          ? _value.colorChangesValue
          : colorChangesValue // ignore: cast_nullable_to_non_nullable
              as String,
      showFilterButton: null == showFilterButton
          ? _value.showFilterButton
          : showFilterButton // ignore: cast_nullable_to_non_nullable
              as bool,
      imageGroup: null == imageGroup
          ? _value._imageGroup
          : imageGroup // ignore: cast_nullable_to_non_nullable
              as List<Image>,
      isFilterApplied: null == isFilterApplied
          ? _value.isFilterApplied
          : isFilterApplied // ignore: cast_nullable_to_non_nullable
              as bool,
      thumbnailSize: null == thumbnailSize
          ? _value.thumbnailSize
          : thumbnailSize // ignore: cast_nullable_to_non_nullable
              as int,
      setButtonEnable: null == setButtonEnable
          ? _value.setButtonEnable
          : setButtonEnable // ignore: cast_nullable_to_non_nullable
              as bool,
      isSelectSortLeft: null == isSelectSortLeft
          ? _value.isSelectSortLeft
          : isSelectSortLeft // ignore: cast_nullable_to_non_nullable
              as bool,
      formationButtonState: null == formationButtonState
          ? _value.formationButtonState
          : formationButtonState // ignore: cast_nullable_to_non_nullable
              as ButtonState,
      realPreviewButtonState: null == realPreviewButtonState
          ? _value.realPreviewButtonState
          : realPreviewButtonState // ignore: cast_nullable_to_non_nullable
              as ButtonState,
      patternDisplayInfoList: null == patternDisplayInfoList
          ? _value._patternDisplayInfoList
          : patternDisplayInfoList // ignore: cast_nullable_to_non_nullable
              as List<PatternDisplayInfo>,
      temporaryGroupDisplayInfoList: null == temporaryGroupDisplayInfoList
          ? _value._temporaryGroupDisplayInfoList
          : temporaryGroupDisplayInfoList // ignore: cast_nullable_to_non_nullable
              as List<PatternDisplayInfo>,
    ));
  }
}

/// @nodoc

class _$FilterResultStateImpl implements _FilterResultState {
  const _$FilterResultStateImpl(
      {this.frameIndex = 0,
      this.isSelectedPattern = false,
      this.showZoomList = false,
      this.selectedZoomScale = 100,
      this.heightValue = '----',
      this.widthValue = '----',
      this.totalTimeValue = '----',
      this.numberOfColorsValue = '----',
      this.colorChangesValue = '----',
      this.showFilterButton = false,
      final List<Image> imageGroup = const [],
      this.isFilterApplied = false,
      this.thumbnailSize = 0,
      this.setButtonEnable = false,
      this.isSelectSortLeft = false,
      this.formationButtonState = ButtonState.disable,
      this.realPreviewButtonState = ButtonState.disable,
      final List<PatternDisplayInfo> patternDisplayInfoList = const [],
      final List<PatternDisplayInfo> temporaryGroupDisplayInfoList = const []})
      : _imageGroup = imageGroup,
        _patternDisplayInfoList = patternDisplayInfoList,
        _temporaryGroupDisplayInfoList = temporaryGroupDisplayInfoList;

  @override
  @JsonKey()
  final int frameIndex;
  @override
  @JsonKey()
  final bool isSelectedPattern;
  @override
  @JsonKey()
  final bool showZoomList;
  @override
  @JsonKey()
  final int selectedZoomScale;
  @override
  @JsonKey()
  final String heightValue;
  @override
  @JsonKey()
  final String widthValue;
  @override
  @JsonKey()
  final String totalTimeValue;
  @override
  @JsonKey()
  final String numberOfColorsValue;
  @override
  @JsonKey()
  final String colorChangesValue;
  @override
  @JsonKey()
  final bool showFilterButton;
  final List<Image> _imageGroup;
  @override
  @JsonKey()
  List<Image> get imageGroup {
    if (_imageGroup is EqualUnmodifiableListView) return _imageGroup;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_imageGroup);
  }

  @override
  @JsonKey()
  final bool isFilterApplied;
  @override
  @JsonKey()
  final int thumbnailSize;
  @override
  @JsonKey()
  final bool setButtonEnable;
  @override
  @JsonKey()
  final bool isSelectSortLeft;
  @override
  @JsonKey()
  final ButtonState formationButtonState;
  @override
  @JsonKey()
  final ButtonState realPreviewButtonState;

  /// Pattern表示情報
  final List<PatternDisplayInfo> _patternDisplayInfoList;

  /// Pattern表示情報
  @override
  @JsonKey()
  List<PatternDisplayInfo> get patternDisplayInfoList {
    if (_patternDisplayInfoList is EqualUnmodifiableListView)
      return _patternDisplayInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_patternDisplayInfoList);
  }

  final List<PatternDisplayInfo> _temporaryGroupDisplayInfoList;
  @override
  @JsonKey()
  List<PatternDisplayInfo> get temporaryGroupDisplayInfoList {
    if (_temporaryGroupDisplayInfoList is EqualUnmodifiableListView)
      return _temporaryGroupDisplayInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_temporaryGroupDisplayInfoList);
  }

  @override
  String toString() {
    return 'FilterResultState(frameIndex: $frameIndex, isSelectedPattern: $isSelectedPattern, showZoomList: $showZoomList, selectedZoomScale: $selectedZoomScale, heightValue: $heightValue, widthValue: $widthValue, totalTimeValue: $totalTimeValue, numberOfColorsValue: $numberOfColorsValue, colorChangesValue: $colorChangesValue, showFilterButton: $showFilterButton, imageGroup: $imageGroup, isFilterApplied: $isFilterApplied, thumbnailSize: $thumbnailSize, setButtonEnable: $setButtonEnable, isSelectSortLeft: $isSelectSortLeft, formationButtonState: $formationButtonState, realPreviewButtonState: $realPreviewButtonState, patternDisplayInfoList: $patternDisplayInfoList, temporaryGroupDisplayInfoList: $temporaryGroupDisplayInfoList)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterResultStateImpl &&
            (identical(other.frameIndex, frameIndex) ||
                other.frameIndex == frameIndex) &&
            (identical(other.isSelectedPattern, isSelectedPattern) ||
                other.isSelectedPattern == isSelectedPattern) &&
            (identical(other.showZoomList, showZoomList) ||
                other.showZoomList == showZoomList) &&
            (identical(other.selectedZoomScale, selectedZoomScale) ||
                other.selectedZoomScale == selectedZoomScale) &&
            (identical(other.heightValue, heightValue) ||
                other.heightValue == heightValue) &&
            (identical(other.widthValue, widthValue) ||
                other.widthValue == widthValue) &&
            (identical(other.totalTimeValue, totalTimeValue) ||
                other.totalTimeValue == totalTimeValue) &&
            (identical(other.numberOfColorsValue, numberOfColorsValue) ||
                other.numberOfColorsValue == numberOfColorsValue) &&
            (identical(other.colorChangesValue, colorChangesValue) ||
                other.colorChangesValue == colorChangesValue) &&
            (identical(other.showFilterButton, showFilterButton) ||
                other.showFilterButton == showFilterButton) &&
            const DeepCollectionEquality()
                .equals(other._imageGroup, _imageGroup) &&
            (identical(other.isFilterApplied, isFilterApplied) ||
                other.isFilterApplied == isFilterApplied) &&
            (identical(other.thumbnailSize, thumbnailSize) ||
                other.thumbnailSize == thumbnailSize) &&
            (identical(other.setButtonEnable, setButtonEnable) ||
                other.setButtonEnable == setButtonEnable) &&
            (identical(other.isSelectSortLeft, isSelectSortLeft) ||
                other.isSelectSortLeft == isSelectSortLeft) &&
            (identical(other.formationButtonState, formationButtonState) ||
                other.formationButtonState == formationButtonState) &&
            (identical(other.realPreviewButtonState, realPreviewButtonState) ||
                other.realPreviewButtonState == realPreviewButtonState) &&
            const DeepCollectionEquality().equals(
                other._patternDisplayInfoList, _patternDisplayInfoList) &&
            const DeepCollectionEquality().equals(
                other._temporaryGroupDisplayInfoList,
                _temporaryGroupDisplayInfoList));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        frameIndex,
        isSelectedPattern,
        showZoomList,
        selectedZoomScale,
        heightValue,
        widthValue,
        totalTimeValue,
        numberOfColorsValue,
        colorChangesValue,
        showFilterButton,
        const DeepCollectionEquality().hash(_imageGroup),
        isFilterApplied,
        thumbnailSize,
        setButtonEnable,
        isSelectSortLeft,
        formationButtonState,
        realPreviewButtonState,
        const DeepCollectionEquality().hash(_patternDisplayInfoList),
        const DeepCollectionEquality().hash(_temporaryGroupDisplayInfoList)
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterResultStateImplCopyWith<_$FilterResultStateImpl> get copyWith =>
      __$$FilterResultStateImplCopyWithImpl<_$FilterResultStateImpl>(
          this, _$identity);
}

abstract class _FilterResultState implements FilterResultState {
  const factory _FilterResultState(
          {final int frameIndex,
          final bool isSelectedPattern,
          final bool showZoomList,
          final int selectedZoomScale,
          final String heightValue,
          final String widthValue,
          final String totalTimeValue,
          final String numberOfColorsValue,
          final String colorChangesValue,
          final bool showFilterButton,
          final List<Image> imageGroup,
          final bool isFilterApplied,
          final int thumbnailSize,
          final bool setButtonEnable,
          final bool isSelectSortLeft,
          final ButtonState formationButtonState,
          final ButtonState realPreviewButtonState,
          final List<PatternDisplayInfo> patternDisplayInfoList,
          final List<PatternDisplayInfo> temporaryGroupDisplayInfoList}) =
      _$FilterResultStateImpl;

  @override
  int get frameIndex;
  @override
  bool get isSelectedPattern;
  @override
  bool get showZoomList;
  @override
  int get selectedZoomScale;
  @override
  String get heightValue;
  @override
  String get widthValue;
  @override
  String get totalTimeValue;
  @override
  String get numberOfColorsValue;
  @override
  String get colorChangesValue;
  @override
  bool get showFilterButton;
  @override
  List<Image> get imageGroup;
  @override
  bool get isFilterApplied;
  @override
  int get thumbnailSize;
  @override
  bool get setButtonEnable;
  @override
  bool get isSelectSortLeft;
  @override
  ButtonState get formationButtonState;
  @override
  ButtonState get realPreviewButtonState;
  @override

  /// Pattern表示情報
  List<PatternDisplayInfo> get patternDisplayInfoList;
  @override
  List<PatternDisplayInfo> get temporaryGroupDisplayInfoList;
  @override
  @JsonKey(ignore: true)
  _$$FilterResultStateImplCopyWith<_$FilterResultStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
