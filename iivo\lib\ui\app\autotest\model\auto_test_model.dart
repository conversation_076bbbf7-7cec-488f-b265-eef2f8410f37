import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:log/log.dart';
import 'package:path/path.dart';

///
/// 自動テストの[AutoTestAction]実行器インターフェース。
///
abstract interface class AutoTestActionRunner {
  ///
  /// アクションを実行し、例外がスローされれば失敗を意味し、そうでなければ成功を意味する
  ///
  /// - return: 変更が実行されたかどうか
  ///
  FutureOr<bool> doAction(AutoTestAction action);
}

///
/// 自動テストのアクションユニットの情報を保持するクラス。
/// 例えば、「ユーザー設定のリセット」は一つのアクションと見なされる。
///
class AutoTestAction implements Comparable<AutoTestAction> {
  AutoTestAction({
    required this.rawData,
    required this.name,
    required this.ordinal,
    required this.desc,
    required Map<String, dynamic>? param,
  }) : _param = param;

  final Map<String, dynamic> rawData;
  final String name;
  final String? desc;
  final int ordinal;
  final Map<String, dynamic>? _param;

  dynamic getParam(String paramName) => _param?[paramName];

  @override
  int compareTo(AutoTestAction other) {
    return ordinal.compareTo(other.ordinal);
  }

  @override
  String toString() {
    return "AutoTestAction(name=$name, ordinal=$ordinal, desc=$desc, param=$_param)";
  }
}

///
/// 自動テストのモデル
///
class AutoTestModel {
  factory AutoTestModel() => _instance;
  AutoTestModel._internal();
  static final AutoTestModel _instance = AutoTestModel._internal();

  ///
  /// すべての自動テストアクションを実行する。
  ///
  /// - param:
  ///   - [runner] アクションランナー。各アクションの実行はこのクラスによって代理される。
  ///
  /// - return: 実行ログ情報
  ///
  Stream<String> performAutoTestActions(AutoTestActionRunner runner) async* {
    Map<String, dynamic>? jsonData;
    try {
      yield "Clear residual files";
      await _clearResidualFiles();

      yield "Parse json file";
      jsonData = await _parseAutoTestSetupJson();

      yield "Parse action list";
      final List<AutoTestAction> actions =
          _parseActionList(jsonData["actions"]);

      for (final AutoTestAction action in actions) {
        yield "Running action: ${action.desc ?? action.name}";
        try {
          action.rawData["isChanged"] = await runner.doAction(action);
          action.rawData["isSuccessful"] = true;
        } catch (e) {
          action.rawData["isSuccessful"] = false;
          throw Exception("Failed to run action `${action.name}`: $e");
        }
      }

      yield "Succeeded, this app will be stopped automatically.";
      _yieldAutoTestResult(isSuccessful: true, contentData: jsonData);
    } catch (e) {
      yield "Failed: $e";
      if (jsonData == null) {
        jsonData = {_errorMessageKeyName: e.toString()};
      } else {
        jsonData[_errorMessageKeyName] = e.toString();
      }
      _yieldAutoTestResult(isSuccessful: false, contentData: jsonData);
    }
  }

  ///
  /// 自動テストアクションのリストを解析する
  ///
  List<AutoTestAction> _parseActionList(Map<String, dynamic> rawActionsData) {
    final List<AutoTestAction> actions = [];
    for (final MapEntry<String, dynamic> entry in rawActionsData.entries) {
      final Map<String, dynamic> rawAction = entry.value;
      if (rawAction["enabled"] != true) {
        continue;
      }
      final AutoTestAction action = AutoTestAction(
        rawData: rawAction,
        name: entry.key,
        ordinal: rawAction["ordinal"],
        desc: rawAction["comment"],
        param: rawAction["param"],
      );
      actions.add(action);
    }
    actions.sort();
    return actions;
  }

  ///
  /// すべての残留ファイルを削除する
  ///
  Future<void> _clearResidualFiles() async {
    final List<String> residualFilePaths = [
      join(_autoTestWorkspaceDirPath, _autoTestSetupOKFilename),
      join(_autoTestWorkspaceDirPath, _autoTestSetupNGFilename)
    ];
    for (String filepath in residualFilePaths) {
      final File file = File(filepath);
      if (await file.exists() == false) {
        continue;
      }
      await file.delete();
    }
  }

  ///
  /// 自動テスト結果を生成します。
  ///
  /// [isSuccessful] は、すべての [AutoTestAction] が成功裏に実行されたかを示す。
  /// [contentData] は、エラー情報のような、結果の詳細情報です。
  ///
  /// このメソッドはエラーを発生させてはなりません。エラーが発生することは、
  /// 我々が自動テストが必要とする結果を正しく提供できないことを意味します。
  /// その場合、現在のプロセスを強制終了させます。自動テストプログラムは、
  /// 現在のプロセスが存在しないことを、失敗の判断基準の一つとして使用すべきです。
  ///
  Future<void> _yieldAutoTestResult(
      {required bool isSuccessful,
      required Map<String, dynamic> contentData}) async {
    try {
      final String filename;
      if (isSuccessful) {
        filename = _autoTestSetupOKFilename;
      } else {
        filename = _autoTestSetupNGFilename;
      }
      final File targetFile = File(join(_autoTestWorkspaceDirPath, filename));
      await targetFile.writeAsString(jsonEncode(contentData));
    } catch (e) {
      _logE("Fatal error occurred, force stop panel app: $e");
      // Suicide
      exit(1);
    }
  }

  ///
  /// `auto_test_setup.json` を解析します。
  ///
  Future<Map<String, dynamic>> _parseAutoTestSetupJson() async {
    final File flagFile = File(
      join(_autoTestWorkspaceDirPath, _autoTestSetupJsonFilename),
    );
    if (await flagFile.exists()) {
      try {
        final String json = await flagFile.readAsString();
        final Map<String, dynamic> data = jsonDecode(json);
        return data;
      } finally {
        await flagFile.delete();
      }
    } else {
      throw Exception("$flagFile does not exist");
    }
  }

  void _logE(String desc) => Log.e(tag: _logTag, description: desc);

  static const String _logTag = "AutoTestModel";
  static const String _errorMessageKeyName = "errorMsg";

  static const String _autoTestWorkspaceDirPath = "/data/brother_dir/auto_test";

  static const String _autoTestSetupJsonFilename = "auto_test_setup.json";
  static const String _autoTestSetupNGFilename = "AUTO_TEST_SETUP_NG";
  static const String _autoTestSetupOKFilename = "AUTO_TEST_SETUP_OK";
}
