import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:panel_library/panel_library.dart';

import 'draw_canvas_model.dart';
import 'edit_object_model.dart';
import 'pen_model.dart';
import 'scan_model.dart';
import 'select_model.dart';
import 'size_adjustment_model.dart';
import 'stamp_model.dart';

///
/// 補助線描画状態
///
class ClipBoardState {
  ClipBoardState.init()
      : firstPosition = invalidPosition,
        secondPosition = invalidPosition,
        prePosition = invalidPosition,
        lastPosition = invalidPosition;

  ClipBoardState({
    this.firstPosition = invalidPosition,
    this.secondPosition = invalidPosition,
    this.lastPosition = invalidPosition,
    this.prePosition = invalidPosition,
  });

  Offset firstPosition;
  Offset secondPosition;
  Offset lastPosition;
  Offset prePosition;

  final Color whiteFrameColor = const Color.fromARGB(255, 237, 237, 237);
  final Color redFrameColor = const Color.fromARGB(255, 255, 121, 126);
  final double whiteFrameWidth = 2.0;
  final double redFrameWidth = 1.0;

  ///
  /// 座標情報
  ///
  static const Offset invalidPosition = Offset(invalidValue, invalidValue);

  ///
  /// 無効座標
  ///
  static const double invalidValue = -1.0;
}

///
/// 描画プロパティー
///
class ClipBoardProperty {
  ClipBoardProperty({
    this.nearArea = nearAreaDefault,
    this.clippingType = MdcClippingTypes.rectangle,
    this.nibWidth = 1,
  });

  double nearArea;
  MdcClippingTypes clippingType;
  double nibWidth;
  static const double nearAreaDefault = 50;
}

class ClipBoardModel {
  ClipBoardModel._internal();

  factory ClipBoardModel() => _instance;
  static final ClipBoardModel _instance = ClipBoardModel._internal();

  ///
  /// リクエスト状態
  ///
  InstructionProgressStatus instructionProgressStatus =
      InstructionProgressStatus.single;

  ///
  /// ペン先に設定に応じたサイズ
  ///
  static const int shapeDotThickness = 1;

  ///
  /// 開始点表示範囲枠の幅
  ///
  static const double nearAreaDefault = 50;
  static const double nibWidthDefault = 1.0;

  ///
  /// 操作Positionのリスト
  ///
  List<Offset> clipPositionList = [];

  ///
  /// 半
  ///
  static const int half = 2;

  ///
  /// 単位変換率（mm/dot）
  ///
  static const double mmToDotRate = PenModel.ratio;

  ///
  /// Clipポップアップ表示中
  ///
  bool hasClipBoardPopup = false;

  ///
  /// 描画状態
  ///
  ClipBoardState clipBoardState = ClipBoardState.init();

  ///
  /// 領域選択Press
  ///
  void press(ui.Offset offset) {
    clipBoardProperty.clippingType = SelectModel.selectTypes;
    switch (SelectModel.selectTypes) {
      case MdcClippingTypes.rectangle:
        resetPositionList();
        clipPositionList.add(offset);
        clipBoardState = clipBoardState
          ..firstPosition = offset
          ..secondPosition = ClipBoardState.invalidPosition
          ..lastPosition = offset
          ..prePosition = offset;
        break;

      case MdcClippingTypes.freeClose:
        resetPositionList();
        clipPositionList.add(offset);
        clipBoardState = clipBoardState
          ..firstPosition = offset
          ..secondPosition = ClipBoardState.invalidPosition
          ..prePosition = offset
          ..lastPosition = offset;
        break;

      case MdcClippingTypes.lineMulti:

        /// 描画データなしの場合
        if (!MdcLibrary().apiBinding.isMdcDrawingPatternPresence().result) {
          resetPositionList();
          clipPositionList.add(offset);
          clipBoardState = clipBoardState
            ..firstPosition = offset
            ..secondPosition = ClipBoardState.invalidPosition
            ..lastPosition = offset
            ..prePosition = offset;
          clipBoardProperty.nearArea = DrawCanvasModel().getDisplayNearArea();
          return;
        }

        /// 開始点追加
        if (clipPositionList.isEmpty) {
          clipPositionList.add(offset);
          clipBoardState = clipBoardState
            ..firstPosition = offset
            ..secondPosition = ClipBoardState.invalidPosition
            ..lastPosition = offset
            ..prePosition = offset;
          clipBoardProperty.nearArea = DrawCanvasModel().getDisplayNearArea();
          return;
        }

        /// 途中点追加 または エラー状態の処理
        if (clipPositionList.isNotEmpty) {
          /// 前回点はLIBに登録成功
          /// UI側リスト追加する
          if (EditObjectModel().clipErrorCode ==
              MdcLibraryError.mdcNoErrorNoImage) {
            clipPositionList.add(offset);
            clipBoardState.prePosition = offset;
            return;
          }

          /// 前回点はLIBに登録失敗。
          /// UI側保存の点をクリア
          resetPositionList();
        }
        break;

      case MdcClippingTypes.dropperSelect:
      case MdcClippingTypes.allSelect:
      default:
        break;
    }
  }

  ///
  /// 領域選択Move
  ///
  void move(ui.Offset offset) {
    switch (SelectModel.selectTypes) {
      case MdcClippingTypes.rectangle:
        if (clipBoardState.secondPosition == ClipBoardState.invalidPosition) {
          clipPositionList.add(offset);
          clipBoardState.secondPosition = clipBoardState.prePosition;
        } else {
          clipPositionList.removeLast();
          clipPositionList.add(offset);
          clipBoardState.prePosition = offset;
        }
        break;
      case MdcClippingTypes.lineMulti:
        if (clipBoardState.secondPosition == ClipBoardState.invalidPosition &&
            clipBoardState.firstPosition != ClipBoardState.invalidPosition) {
          clipPositionList.add(offset);
          clipBoardState.secondPosition = clipBoardState.prePosition;
        } else {
          clipPositionList.removeLast();
          clipPositionList.add(offset);
          clipBoardState.prePosition = offset;
        }
        break;
      case MdcClippingTypes.freeClose:
        clipPositionList.add(offset);
        clipBoardState.lastPosition = offset;
        break;

      case MdcClippingTypes.dropperSelect:
      case MdcClippingTypes.allSelect:
      default:
        // 初期選択時はPress()で位置情報を覚えるのでここでは更新不要(今後必要な場合を考慮してcaseだけ用意)
        break;
    }
  }

  ///
  /// 領域選択Move
  ///
  void release(ui.Offset offset) {
    switch (SelectModel.selectTypes) {
      case MdcClippingTypes.rectangle:
      case MdcClippingTypes.freeClose:
      case MdcClippingTypes.dropperSelect:
      case MdcClippingTypes.allSelect:
        resetPositionList();
        break;

      case MdcClippingTypes.lineMulti:

        /// 描画範囲に描画データがないの場合
        if (!MdcLibrary().apiBinding.isMdcDrawingPatternPresence().result) {
          resetPositionList();
          return;
        }

        /// 今回点はLIBに登録失敗の場合
        if (EditObjectModel().clipErrorCode !=
                MdcLibraryError.mdcNoErrorNoImage &&
            EditObjectModel().clipErrorCode != MdcLibraryError.mdcNoError) {
          resetPositionList();
          break;
        }

        /// 今回点はLIBに登録成功
        if (clipPositionList.isNotEmpty) {
          /// 今回点は途中点の場合、UI側最終点の位置を更新する
          if (EditObjectModel().clipErrorCode ==
              MdcLibraryError.mdcNoErrorNoImage) {
            clipBoardState.lastPosition = clipBoardState.prePosition;
            return;
          }

          /// 今回点は最終点の場合、UI側保存の点をクリア
          if (EditObjectModel().clipErrorCode == MdcLibraryError.mdcNoError) {
            resetPositionList();
            return;
          }
        }
        break;

      default:
        // 初期選択時はPress()で位置情報を覚えるのでここでは更新不要(今後必要な場合を考慮してcaseだけ用意)
        break;
    }
  }

  ///
  /// 描画プロパティー
  ///
  ClipBoardProperty clipBoardProperty = ClipBoardProperty(
    nearArea: nearAreaDefault,
    clippingType: MdcClippingTypes.rectangle,
    nibWidth: nibWidthDefault,
  );

  ///
  /// リセット
  ///
  void reset() {
    clipBoardProperty = clipBoardProperty
      ..nearArea = nearAreaDefault
      ..clippingType = MdcClippingTypes.rectangle
      ..nibWidth = nibWidthDefault;

    clipBoardState = clipBoardState
      ..firstPosition = ClipBoardState.invalidPosition
      ..lastPosition = ClipBoardState.invalidPosition;

    clipPositionList = [];
  }

  ///
  /// 全体部分模様確定
  ///
  void confirmUnsettledObject({required bool needUpdateUI}) {
    if (!EditObjectModel().hasPartsImageInfo()) {
      return;
    }

    if (EditObjectModel().isStampParts) {
      StampModel().confirmUnsettledObject(needUpdateUI: needUpdateUI);
    } else {
      EditObjectModel().confirmUnsettledObject(needUpdateUI: needUpdateUI);
    }

    resetPositionList();

    if (ScanModel().isLoadScanImage) {
      ScanModel().scanImageType = ScanImageType.imageScanInserted;
    } else {
      ScanModel().scanImageType = ScanImageType.none;
    }

    SizeAdjustmentModel().sizeInput = const MdcSize(width: 0, height: 0);
  }

  ///
  /// リセット座標情報
  /// UI側保存の点をクリア
  /// clipErrorCodeをmdcNoErrorにします
  /// clipBoardState初期化
  ///
  void resetPositionList() {
    EditObjectModel().clipErrorCode = MdcLibraryError.mdcNoError;
    clipBoardState = ClipBoardState.init();
    clipPositionList = [];
  }
}
