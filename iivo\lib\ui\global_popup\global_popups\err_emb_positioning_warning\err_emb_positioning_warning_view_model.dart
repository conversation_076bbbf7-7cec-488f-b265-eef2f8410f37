import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import 'err_emb_positioning_warning_view_interface.dart';

void Function()? errEmbPositioningWarningOkButtonFunc;

final errEmbPositioningWarningViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrEmbPositioningWarningViewInterface,
            ErrEmbPositioningWarningState, BuildContext>(
        (ref, context) => ErrEmbPositioningWarningViewModel(ref, context));

class ErrEmbPositioningWarningViewModel
    extends ErrEmbPositioningWarningViewInterface {
  ErrEmbPositioningWarningViewModel(Ref ref, BuildContext context)
      : super(const ErrEmbPositioningWarningState(), ref, context);

  ///
  /// cancelボタンクリク関数
  ///
  @override
  void onCancelButtonClicked() =>
      EmbLibrary().apiBinding.embSewingSnowmanFrameMoveCancel();

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    EmbLibraryError error =
        EmbLibrary().apiBinding.embSewingSnowmanFrameMoveOk();
    if (error == EmbLibraryError.EMB_NO_ERR) {
      errEmbPositioningWarningOkButtonFunc?.call();
      errEmbPositioningWarningOkButtonFunc = null;
    } else {
      /// do nothing
    }
  }

  @override
  void dispose() {
    super.dispose();
    errEmbPositioningWarningOkButtonFunc = null;
  }
}
