import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/device_info_model.dart';
import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/line_motif_model.dart';
import '../../../../../../model/stitch/toolbar_model.dart';
import '../../../stitch_page_view_model.dart';
import 'line_motif_view_interface.dart';

typedef Unit = DisplayUnit;

typedef LineMotifViewModelProvider
    = AutoDisposeStateNotifierProvider<LineMotifViewInterface, LineMotifState>;

class LineMotifViewModel extends LineMotifViewInterface {
  LineMotifViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineMotifState(
              lineMotifSizePopup: false,
              lineMotifSpacingPopup: false,
              lineMotifFlipPopup: false,
              flipSide: FlipSide.flip_inside,
              sizeDisplayValue: "",
              spacingDisplayValue: "",
              flipDisplayValue: "",
              isSizeDefaultValue: false,
              isSpacingDefaultValue: false,
              isFlipDefaultValue: false,
            ),
            ref) {
    /// View更新
    update();
  }

  ///
  /// 単位取得する
  ///
  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  ///
  /// ステップ量
  ///
  final int _stepValue = 10;

  ///
  /// モチーフの既定値
  ///

  final FlipSide defaultSideValue = LineMotifModel().lineFlipTypes;
  final defaultSizeValue = LineMotifModel().defaultSizeValue;
  final defaultSpacingValue = LineMotifModel().defaultSpacingValue;

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    state = state.copyWith(
      sizeDisplayValue: _getSizeDisplayValue(),
      spacingDisplayValue: _getSpacingDisplayValue(),
      flipDisplayValue: _getFlipDisplayValue(),
      isSizeDefaultValue: _getSizeDisplayTextStyle(),
      isSpacingDefaultValue: _getSpacingDisplayTextStyle(),
      isFlipDefaultValue: _getFlipDisplayTextStyle(),
    );
  }

  ///
  /// Sizeダンパボタン
  ///
  @override
  void onCandleSizeClick(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineMotifSize.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// Spacingダンパボタン
  ///
  @override
  void onCandleSpacingClick(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineMotifSpacing.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// Flipダンパボタン
  ///
  @override
  void onCandleFlipClick(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineMotifFlip.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// Flipタイプの取得
  ///
  @override
  bool getMdcFlipLineType() {
    if (LineMotifModel().getLineFlip() == defaultSideValue) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// Sizeの表示値を取得する
  ///
  String _getSizeDisplayValue() {
    int size = LineMotifModel().getSize();
    if (size == LineMotifModel.sizeNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    /// cmからmmへ
    double motifSizeValue = size / _stepValue;
    if (currentSelectedUnit == Unit.mm) {
      return motifSizeValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(motifSizeValue);
  }

  ///
  /// Spacingの表示値を取得する
  ///
  String _getSpacingDisplayValue() {
    int space = LineMotifModel().getSpacing();
    if (space == LineMotifModel.spacingNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    /// cmからmmへ
    double motifSpacingValue = space / _stepValue;
    if (currentSelectedUnit == Unit.mm) {
      return motifSpacingValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(motifSpacingValue);
  }

  ///
  /// Flipの表示値を取得する
  ///
  String _getFlipDisplayValue() {
    if (LineMotifModel().getLineFlip() == LineMotifModel.sideNotUpdating) {
      return "**";
    } else {
      return "";
    }
  }

  ///
  /// Sizeを表示するテキストスタイルを取得するには
  ///
  bool _getSizeDisplayTextStyle() {
    int size = LineMotifModel().getSize();
    if (size == defaultSizeValue || size == LineMotifModel.sizeNotUpdating) {
      return true;
    }

    return false;
  }

  ///
  /// Spacingを表示するテキストスタイルを取得するには
  ///
  bool _getSpacingDisplayTextStyle() {
    int space = LineMotifModel().getSpacing();
    if (space == defaultSpacingValue ||
        space == LineMotifModel.spacingNotUpdating) {
      return true;
    }

    return false;
  }

  ///
  /// Flipを表示するテキストスタイルを取得するには
  ///
  bool _getFlipDisplayTextStyle() =>
      LineMotifModel().getLineFlip() == LineMotifModel.sideNotUpdating
          ? true
          : false;
}
