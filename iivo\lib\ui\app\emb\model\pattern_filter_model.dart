import 'package:flutter/material.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../model/app_locale.dart';
import 'pattern_data_reader/pattern_data_base.dart';

///
/// [FrameSize]フィルター項目
///
enum FrameSize {
  frame_297_465(2970, 4650),
  frame_272_408(2720, 4080),
  frame_272_272(2720, 2720),
  frame_254_254(2540, 2540),
  frame_240_360(2400, 3600),
  frame_240_240(2400, 2400),
  frame_200_300(2000, 3000),
  frame_200_200(2000, 2000),
  frame_180_360(1800, 3600),
  frame_180_300(1800, 3000),
  frame_160_260(1600, 2600),
  frame_150_150(1500, 1500),
  frame_130_180(1300, 1800),
  frame_100_300(1000, 3000),
  frame_100_180(1000, 1800),
  frame_100_100(1000, 1000),
  frame_0_0(0, 0);

  const FrameSize(this.width, this.height);
  final int width;
  final int height;
}

///
/// [NumberOfColor]フィルター項目
///
enum NumberOfColor {
  one(1, 1, 1),
  between2to5(2, 2, 5),
  between6to10(3, 6, 10),
  between11to15(4, 11, 15),
  between16to20(5, 16, 20),
  over21(6, 21, double.infinity);

  const NumberOfColor(this.number, this.min, this.max);
  final int number;
  final double min;
  final double max;
}

///
/// フィルター
///
enum FilterType {
  hide,
  available,
  filtered,
}

class PatternFilter {
  PatternFilter._internal();
  factory PatternFilter() => _instance;
  static final PatternFilter _instance = PatternFilter._internal();

  ///
  /// フィルターボタンを表示するかどうか
  ///
  FilterType _filterStatus = FilterType.hide;
  FilterType get filterStatus => _filterStatus;

  ///
  /// フィルタリングされたパターンを逆の順序で配置する必要があるかどうか
  ///
  bool isFilterReverse = true;

  ///
  /// パターンの最小サイズのデフォルト値
  ///
  static const FrameSize frameSizeLowerLimitDefault = FrameSize.frame_0_0;

  ///
  /// パターンの最小サイズの値
  ///
  FrameSize frameSizeLowerLimit = frameSizeLowerLimitDefault;
  bool isGreaterThanLowerLimit(int frameSizeIndex) =>
      frameSizeIndex <= frameSizeLowerLimit.index;

  ///
  /// パターンの最大サイズのデフォルト値
  ///
  static const FrameSize frameSizeUpperLimitDefault = FrameSize.frame_297_465;

  ///
  /// パターンの最大サイズの値
  ///
  FrameSize frameSizeUpperLimit = frameSizeUpperLimitDefault;
  bool isLessThanUpperLimit(int frameSizeIndex) =>
      frameSizeIndex >= frameSizeUpperLimit.index;

  ///
  /// パターンのサイズが設定範囲内にあるかどうか
  ///
  bool isContainFrameSize(int width, int height) {
    if (frameSizeUpperLimit.height >= frameSizeLowerLimit.height) {
      if (width >= frameSizeLowerLimit.width &&
          width <= frameSizeUpperLimit.width &&
          height >= frameSizeLowerLimit.height &&
          height <= frameSizeUpperLimit.height) {
        return true;
      }
    } else {
      /// 下限の高さが上限の高さを超えている場合、下限の高さの判定を行いません。
      if (width >= frameSizeLowerLimit.width &&
          width <= frameSizeUpperLimit.width &&
          height <= frameSizeUpperLimit.height) {
        return true;
      }
    }

    return false;
  }

  ///
  /// フィルターの状態を隠すに設定する
  ///
  void setFilterHide() {
    _filterStatus = FilterType.hide;
  }

  ///
  /// フィルターの状態を使用可能に設定する
  ///
  void setFilterAvailable() {
    if (filterStatus == FilterType.hide) {
      _filterStatus = FilterType.available;
    } else {
      /// do nothing
    }
  }

  ///
  /// フィルターの状態を適用済みに設定する
  ///
  void setFilterApplied() {
    if (filterStatus == FilterType.available) {
      _filterStatus = FilterType.filtered;
    } else {
      /// do nothing
    }
  }

  ///
  /// 色数の設定値
  ///
  List<NumberOfColor?> numberOfColorValueList =
      List.from(PatternFilter.colorNumberInfo.keys.toList());

  ///
  /// パターンの色数が設定範囲内にあるかどうか
  ///
  bool isContainColorNumber(int colorNumber) {
    bool isContain = false;

    if (numberOfColorValueList.every((element) => element == null)) {
      isContain = true;
    } else {
      for (var element in numberOfColorValueList) {
        if (element == null) {
          continue;
        } else {
          if (colorNumber >= element.min && colorNumber <= element.max) {
            isContain = true;
            break;
          } else {
            isContain = false;
          }
        }
      }
    }

    return isContain;
  }

  ///
  /// フィルタリングされたパターンの情報
  ///
  List<PatternFilterGroup> filterPatternInfo = [];

  ///
  /// 表示に使用するパターンのアイコン
  ///
  List<Image> iconList = [];

  ///
  /// パターンを刺繍フレームのサイズに基づいて並べ替える
  /// 1.　選択肢のFrameSizeでソート
  /// 2.　色数の指定もあれば　①のなかで色数の降順昇順
  /// 3.　①② が同じ模様であればカテゴリNo,模様No,の順を考慮
  ///
  void _sortPattern() {
    /// 刺繍フレーム範囲に対応するパターンリスト
    Map<FrameSize, List<PatternFilterGroup>> frameSizeRanges = {
      FrameSize.frame_100_100: [],
      FrameSize.frame_100_180: [],
      FrameSize.frame_100_300: [],
      FrameSize.frame_130_180: [],
      FrameSize.frame_150_150: [],
      FrameSize.frame_160_260: [],
      FrameSize.frame_180_360: [],
      FrameSize.frame_180_300: [],
      FrameSize.frame_200_200: [],
      FrameSize.frame_200_300: [],
      FrameSize.frame_240_240: [],
      FrameSize.frame_240_360: [],
      FrameSize.frame_254_254: [],
      FrameSize.frame_272_272: [],
      FrameSize.frame_272_408: [],
      FrameSize.frame_297_465: [],
    };

    /// パターンの刺繍フレームのサイズは、パターンの幅と高さによって得られます
    FrameSize getFrameSize(int width, int height) {
      for (var element in frameSizeRanges.keys.toList()) {
        if (width <= element.width && height <= element.height) {
          return element;
        }
      }
      return FrameSize.frame_297_465;
    }

    /// パターンのサイズに基づいてパターンをグループ化する
    /// 1.選択肢のFrameSizeでソート
    for (PatternFilterGroup item in filterPatternInfo) {
      FrameSize frameSize = getFrameSize(item.width, item.height);
      if (frameSizeRanges[frameSize] == null) {
      } else {
        frameSizeRanges[frameSize]!.add(item);
      }
    }

    bool isNumberOfColorNotSet =
        numberOfColorValueList.every((element) => element == null);

    for (var rangeItems in frameSizeRanges.values) {
      rangeItems.sort((a, b) {
        /// 2.色数の指定もあれば　①のなかで色数の降順昇順
        if (isNumberOfColorNotSet == false) {
          int colorComparison = isFilterReverse
              ? b.colorNumber.compareTo(a.colorNumber)
              : a.colorNumber.compareTo(b.colorNumber);

          if (colorComparison != 0) {
            return colorComparison;
          }
        } else {
          /// DoNothing
        }

        /// 3.①② が同じ模様であればカテゴリNo,模様No,の順を考慮
        int groupComparison = isFilterReverse
            ? b.groupNo.compareTo(a.groupNo)
            : a.groupNo.compareTo(b.groupNo);
        if (groupComparison != 0) {
          return groupComparison;
        }
        return isFilterReverse
            ? b.patternIndex.compareTo(a.patternIndex)
            : a.patternIndex.compareTo(b.patternIndex);
      });
    }

    /// ソートされたパターンのリスト
    List<PatternFilterGroup> sortedFilterPattern = [];
    List<FrameSize> sortedFrameSize = frameSizeRanges.keys.toList();

    /// 逆順が必要な場合
    if (isFilterReverse == true) {
      sortedFrameSize = sortedFrameSize.reversed.toList();
    }

    for (FrameSize range in sortedFrameSize) {
      if (frameSizeRanges[range] == null) {
        sortedFilterPattern.addAll([]);
      } else {
        sortedFilterPattern.addAll(frameSizeRanges[range]!);
      }
    }

    filterPatternInfo.clear();
    filterPatternInfo.addAll(sortedFilterPattern);
  }

  ///
  /// フィルタリングされたパターンの情報を取得する
  ///
  Future<void> updateFilterPatternInfo() async {
    if (patternData == null || patternData!.isEmpty) {
      return;
    }

    filterPatternInfo.clear();
    iconList.clear();

    if (patternData is List<PatternInfoGroupImplement> &&
        patternData is List<PatternIconGroupImplement>) {
      for (var element
          in PatternFilter().patternData! as List<PatternInfoGroupImplement>) {
        for (int i = 0; i < element.patternFilterInfo.length; i++) {
          final width = element.patternFilterInfo[i].width;
          final height = element.patternFilterInfo[i].height;
          final colorNumber = element.patternFilterInfo[i].colorNumber;

          if (isContainFrameSize(width, height) &&
              isContainColorNumber(colorNumber)) {
            filterPatternInfo.add(element.patternFilterInfo[i]);
          }
        }
      }

      _sortPattern();

      for (var element in filterPatternInfo) {
        iconList.add(element.icon);
      }
    }
  }

  ///
  /// パターンサイズのディスプレイのメッセージを取得する(mm)
  ///
  List<String> getFrameSizeListMM() {
    AppLocalizations l10n =
        lookupAppLocalizations(AppLocale().getCurrentLocale());

    List<String> frameSizeListMM = [
      l10n.icon_frame_297_465_mm,
      l10n.icon_frame_272_408_mm,
      l10n.icon_frame_272_272_mm,
      l10n.icon_frame_254_254_mm,
      l10n.icon_frame_240_360_mm,
      l10n.icon_frame_240_240_mm,
      l10n.icon_frame_200_300_mm,
      l10n.icon_frame_200_200_mm,
      l10n.icon_frame_180_360_mm,
      l10n.icon_frame_180_300_mm,
      l10n.icon_frame_160_260_mm,
      l10n.icon_frame_150_150_mm,
      l10n.icon_frame_130_180_mm,
      l10n.icon_frame_100_300_mm,
      l10n.icon_frame_100_180_mm,
      l10n.icon_frame_100_100_mm,
      "0 × 0 mm",
    ];

    return frameSizeListMM;
  }

  ///
  /// パターンサイズのディスプレイのメッセージを取得する(inch)
  ///
  List<String> getFrameSizeListInch() {
    AppLocalizations l10n =
        lookupAppLocalizations(AppLocale().getCurrentLocale());

    List<String> frameSizeListMM = [
      l10n.icon_frame_297_465_inch,
      l10n.icon_frame_272_408_inch,
      l10n.icon_frame_272_272_inch,
      l10n.icon_frame_254_254_inch,
      l10n.icon_frame_240_360_inch,
      l10n.icon_frame_240_240_inch,
      l10n.icon_frame_200_300_inch,
      l10n.icon_frame_200_200_inch,
      l10n.icon_frame_180_360_inch,
      l10n.icon_frame_180_300_inch,
      l10n.icon_frame_160_260_inch,
      l10n.icon_frame_150_150_inch,
      l10n.icon_frame_130_180_inch,
      l10n.icon_frame_100_300_inch,
      l10n.icon_frame_100_180_inch,
      l10n.icon_frame_100_100_inch,
      "0\" × 0\"",
    ];

    return frameSizeListMM;
  }

  ///
  /// フィルタColorNumber画面の表示情報
  ///
  static const Map<NumberOfColor, String> colorNumberInfo = {
    NumberOfColor.one: "1",
    NumberOfColor.between2to5: "2-5",
    NumberOfColor.between6to10: "6-10",
    NumberOfColor.between11to15: "11-15",
    NumberOfColor.between16to20: "16-20",
    NumberOfColor.over21: "21-",
  };

  ///
  /// フィルタ用のjsonファイルから取得したのpatternData
  /// 各カテゴリ入る前に　この変数[patternData]を設定する
  ///
  List<PatternDataBase>? patternData;

  ///
  /// Filter設定をリセットする
  ///
  void resetFilterSetting() {
    setFilterHide();
    frameSizeLowerLimit = frameSizeLowerLimitDefault;
    frameSizeUpperLimit = frameSizeUpperLimitDefault;

    for (var i = 0; i < PatternFilter().numberOfColorValueList.length; i++) {
      PatternFilter().numberOfColorValueList[i] =
          PatternFilter.colorNumberInfo.keys.toList()[i];
    }
  }

  ///
  /// デフォルトクリア処理
  ///
  void reset() {
    patternData = null;
    filterPatternInfo.clear();
    iconList.clear();
    frameSizeLowerLimit = frameSizeLowerLimitDefault;
    frameSizeUpperLimit = frameSizeUpperLimitDefault;
  }
}
