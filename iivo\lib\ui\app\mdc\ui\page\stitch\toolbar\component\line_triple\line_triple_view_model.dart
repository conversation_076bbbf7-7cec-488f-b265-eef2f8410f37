import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/device_info_model.dart';
import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/line_triple_model.dart';
import '../../../../../../model/stitch/toolbar_model.dart';
import '../../../stitch_page_view_model.dart';
import 'line_triple_view_interface.dart';

typedef LineTripleViewModelProvider = AutoDisposeStateNotifierProvider<
    LineTripleStateViewInterface, LineTripleState>;

class LineTripleViewModel extends LineTripleStateViewInterface {
  LineTripleViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineTripleState(
              runPitchDisplayValue: "",
              isrunPitchDefaultVlue: false,
              isUnitMm: true,
              isUnderSewingValeSame: true,
              underSewingState: MDCIsOnOff.mdcIs_on,
              isUnderSewingDefaultVlue: false,
            ),
            ref) {
    update();
  }

  ///
  /// view更新
  ///
  @override
  void update() {
    state = state.copyWith(
      runPitchDisplayValue: _getRunPitchDisplayValue(),
      isrunPitchDefaultVlue: _getRunPitchDefault(),
      isUnitMm:
          DeviceInfoModel().displayUnitType == DisplayUnit.mm ? true : false,
      isUnderSewingValeSame: LineTripleModel().getUnderSewing() !=
              LineTripleModel.underSewingNotUpdating
          ? true
          : false,
      underSewingState: LineTripleModel().getUnderSewing(),
      isUnderSewingDefaultVlue: _getUnderSewingDefault(),
    );
  }

  ///
  /// ランピッチボタンをクリック
  ///
  @override
  void onRunPitchButtonClicked(BuildContext context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineTripleRunPitch.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// ランピッチの表示値を取得する
  ///
  String _getRunPitchDisplayValue() {
    int runPitch = LineTripleModel().getRunPitch();
    double runPitchValue = runPitch / LineTripleModel.conversionRate;

    if (runPitch == LineTripleModel.pitchNotUpdating) {
      if (DeviceInfoModel().displayUnitType == DisplayUnit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }
    if (DeviceInfoModel().displayUnitType == DisplayUnit.mm) {
      return runPitchValue.toStringAsFixed(1);
    } else {
      return ToolbarModel.getDisplayInchShowValue(runPitchValue);
    }
  }

  ///
  ///  ランピッチ表示テキストスタイルを取得します
  ///
  bool _getRunPitchDefault() {
    int runPitch = LineTripleModel().getRunPitch();
    if (runPitch == LineTripleModel().runPitchDefaultValue ||
        runPitch == LineTripleModel.pitchNotUpdating) {
      return true;
    }
    return false;
  }

  ///
  ///  下縫い表示テキストスタイルを取得します
  ///
  bool _getUnderSewingDefault() {
    MDCIsOnOff underSewing = LineTripleModel().getUnderSewing();
    if (underSewing == LineTripleModel().underSewingDefaultValue ||
        underSewing == LineTripleModel.underSewingNotUpdating) {
      return true;
    }

    return false;
  }

  ///
  /// 下縫いチボタンをクリック
  /// ToDo: 表示方法仕様確認
  /// QA jira:PHBSH-2670
  ///
  @override
  void openUnderSewingSettingPopup(BuildContext context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineTripleUnderSewing.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }
}
