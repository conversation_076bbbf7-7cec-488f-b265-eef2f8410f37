import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'line_v_stitch_view_model.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'line_v_stitch_view_interface.freezed.dart';

@freezed
class LineVStitchState with _$LineVStitchState {
  const factory LineVStitchState({
    @Default(false) bool isWidthPopupOn,
    @Default(false) bool isSpacePopupOn,
    @Default(false) bool isThicknessPopupOn,
    @Default(false) bool isFlipPopupOn,
    @Default(true) bool isFlipSelect,
    required String widthDisplayValue,
    required bool isWidthDefaultValue,
    required String spaceDisplayValue,
    required bool isSpaceDefaultValue,
    required String thicknessDisplayValue,
    required bool isThicknessDefaultValue,
    required String flipDisplayValue,
    required bool isFlipDefaultValue,
  }) = _LineVStitchState;
}

abstract class LineVStitchViewInterface extends ViewModel<LineVStitchState> {
  LineVStitchViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// 幅設定ポップアップウィンドウを開きます
  ///
  void openWidthSettingPopup(context);

  ///
  /// 間隔設定ポップアップウィンドウを開く
  ///
  void openSpaceSettingPopup(context);

  ///
  /// 厚さ設定ポップアップウィンドウを開く
  ///
  void openThicknessSettingPopup(context);

  ///
  /// 方向設定ポップアップウィンドウを開きます
  ///
  void openFlipSettingPopup(context);

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;

  ///
  /// テキスト信号
  ///
  String get textSignal;
}
