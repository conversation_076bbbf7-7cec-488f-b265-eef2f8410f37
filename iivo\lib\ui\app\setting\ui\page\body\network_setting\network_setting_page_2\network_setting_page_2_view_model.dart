import 'dart:io';

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:network_wifi/network_wifi.dart';
// ignore: implementation_imports
import 'package:panel_library/src/device_library/src/implement/mock/mock_setting_data.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../../network/upgrade/upgrade.dart';
import '../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../page_route/page_route.dart';
import '../../../../../../teaching/model/pdf_model.dart';
import '../../../../../model/network_setting_page2_model.dart';
import '../../../setting_page_view_model.dart';
import 'network_setting_page_2_interface.dart';
import 'network_wait_page/network_wait_popup.dart';

final networkPage2ViewModelProvider = StateNotifierProvider.family.autoDispose<
    NetworkSettingPage2ViewModel,
    NetworkSettingPage2State,
    BuildContext>((ref, context) => NetworkSettingPage2ViewModel(ref, context));

class NetworkSettingPage2ViewModel extends NetworkSettingPage2Interface {
  NetworkSettingPage2ViewModel(Ref ref, context)
      : super(
            const NetworkSettingPage2State(
                isStartEnable: true,
                latestVersion: '',
                subText: '',
                text: '',
                isResume: false,
                progressValue: 0),
            ref,
            context) {
    // [wlanUpdateStateProvider] を監視して、UI がリアルタイムで更新されることを確認します。
    final cancellation =
        ref.read(wlanUpdateStateProvider.notifier).addListener((hasUpdate) {
      if (_isIdle()) {
        if (hasUpdate == true) {
          NetworkSettingPage2Model().downloadState =
              WlanUpgradeDownloadState.idleHasUpdate;
        } else if (hasUpdate == false) {
          NetworkSettingPage2Model().downloadState =
              WlanUpgradeDownloadState.idleNoUpdate;
        } else {
          NetworkSettingPage2Model().downloadState =
              WlanUpgradeDownloadState.unknown;
        }
        update();
      }
    }, fireImmediately: true);
    ref.onDispose(cancellation);

    /// Androidプラットフォームの場合、Wi-Fiイベントのサブスクライバーを追加します。
    if (Platform.isAndroid) {
      final Future<void> Function() unsubscribe =
          WifiManager().wifiEventBroadcast.addSubscriber(
        (event) async {
          /// Wi-Fiイベントが発生したらアップデート処理を呼び出します。
          update();
        },
        hot: false,
      );
      ref.onDispose(unsubscribe);
    }

    /// Android以外のプラットフォームでは、直接アップデート処理を呼び出します。
    update();
  }

  ///
  /// 現在のダウンロードはアイドル状態です
  ///
  bool _isIdle() {
    return NetworkSettingPage2Model().downloadState ==
            WlanUpgradeDownloadState.idleNoUpdate ||
        NetworkSettingPage2Model().downloadState ==
            WlanUpgradeDownloadState.idleHasUpdate ||
        NetworkSettingPage2Model().downloadState ==
            WlanUpgradeDownloadState.unknown;
  }

  @override
  void update() {
    /// 初期化値の設定
    state = state.copyWith(
      text: _getDownloadStateText(),
      isResume: NetworkSettingPage2Model().downloadState ==
          WlanUpgradeDownloadState.paused,
      isStartEnable: _isStartButtonEnabled(),
      latestVersion: _getLatestVersionName(),
      progressValue: _getProgressValue(),
    );
  }

  ///
  /// 最新のバージョン名を取得します
  ///
  String _getLatestVersionName() {
    if (NetworkSettingPage2Model().downloadState ==
        WlanUpgradeDownloadState.unknown) {
      return Upgrade.defaultVersionName;
    } else {
      final String latestVersion = Upgrade().getLatestVersionName();
      if (latestVersion == Upgrade.defaultVersionName) {
        return Upgrade().getInstalledVersionName();
      } else {
        return latestVersion;
      }
    }
  }

  int _getProgressValue() {
    if (NetworkSettingPage2Model().downloadState !=
        WlanUpgradeDownloadState.paused) {
      return 0;
    } else {
      return NetworkSettingPage2Model().downloadingProgress;
    }
  }

  String _getDownloadStateText() {
    final l10n = AppLocalizations.of(context)!;
    switch (NetworkSettingPage2Model().downloadState) {
      case WlanUpgradeDownloadState.unknown:
        return l10n.icon_status_a1;
      case WlanUpgradeDownloadState.idleHasUpdate:
        return "${l10n.icon_status_a4}\n${l10n.t_err_dl_updateprogram}";
      case WlanUpgradeDownloadState.paused:
        return l10n.icon_pause_downloading2;
      case WlanUpgradeDownloadState.completed:
        return l10n.t_err591;
      case WlanUpgradeDownloadState.failed:
        return l10n.t_err_networkconnectionerr;
      case WlanUpgradeDownloadState.failedMemoryFull:
        return l10n.t_err_dl_fail;
      case WlanUpgradeDownloadState.idleNoUpdate:
        return l10n.icon_status_c2;
      case WlanUpgradeDownloadState.downloading:
        return l10n.icon_status_b2;
    }
  }

  ///
  ///  スタート ボタンが使用可能かどうかの状態を取得します
  ///
  bool _isStartButtonEnabled() => WifiManager().isEnabled();

  @override
  void onStartClickButton(BuildContext context) {
    if (state.isStartEnable == false) return;
    if (NetworkSettingPage2Model().downloadState ==
        WlanUpgradeDownloadState.completed) return;
    if (WifiManager().isConnected() == false ||
        WifiManager().isEnabled() == false) return;

    if (state.isResume == false) {
      GlobalPopupRoute().showPleaseWaitPopup();
      Upgrade().hasUpdateInServer().then((hasUpdate) {
        GlobalPopupRoute().resetPleaseWaitPopup();

        if (hasUpdate == null) return;

        if (hasUpdate) {
          if (Upgrade().hasNewEulaVersion()) {
            _enterDownloadPage();
          } else {
            _showEulaPdf();
          }
          Upgrade().updateWlanUpdateState(true);
        } else {
          Upgrade().updateWlanUpdateState(false);
        }
      });
    } else {
      _enterDownloadPage();
    }
  }

  ///
  /// EULA PDF をユーザーに表示する
  ///
  void _showEulaPdf() {
    PdfModel().selectSettingPdfRoute = SettingPdfRouteEnum.endUser;
    PdfModel().isSettingFrom = false;
    PdfModel().isTeachingFrom = false;
    PagesRoute()
        .pushNamed(nextRoute: PageRouteEnum.settingPdfPreview)
        .then((_) => _enterDownloadPage());
  }

  ///
  /// ダウンロードページに入る
  ///
  void _enterDownloadPage() {
    // Header と Footer をグレー表示にする
    Upgrade().isWlanDownloading = true;
    ref
        .read(settingPageViewModelProvider.notifier)
        .updateSettingPageByChild(ModuleType.downLoading);

    PopupNavigator.push(
            context: context,
            popup: PopupRouteBuilder(
                builder: (context) => const NetworkWaitPopup(), barrier: true))
        .then((value) {
      // Header と Footerのグレーアウトを解除
      Upgrade().isWlanDownloading = false;
      ref
          .read(settingPageViewModelProvider.notifier)
          .updateSettingPageByChild(ModuleType.downLoading);
      update();
    });
  }

  @override
  void launchAndroidSettings() {
    if (getMockData()["androidSettingsEntry"] == true) {
      const MethodChannel("main").invokeMapMethod("launchSettings");
    }
  }
}
