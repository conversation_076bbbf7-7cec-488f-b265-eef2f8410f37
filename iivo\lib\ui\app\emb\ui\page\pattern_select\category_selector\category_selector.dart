import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../component/emb_footer/emb_footer.dart';
import '../../../component/emb_header/emb_header.dart';
import '../../common_component/zoom_selector_popup.dart';
import 'category_selector_view_interface.dart';
import 'category_selector_view_model.dart';
import 'component/preview/preview.dart';

///
/// 刺しゅうパターン選択画面
///
class CategorySelector extends StatefulPage {
  const CategorySelector({
    super.key,
    super.needMultipleClick = true,
  });
  @override
  PageState<CategorySelector> createState() => _CategorySelectorState();
}

class _CategorySelectorState extends PageState<CategorySelector> {
  @override
  void initPageState(BuildContext context, WidgetRef ref) {
    super.initPageState(context, ref);
    ref.read(categorySelectorViewInfoProvider.notifier).context = context;
  }

  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(categorySelectorViewModelProvider);
    final viewModel = ref.read(categorySelectorViewModelProvider.notifier);

    return Scaffold(
      body: Stack(
        children: [
          state.isEnterPattern
              ? Stack(
                  children: [
                    MultiTouchBlocker(
                        child: Column(
                      children: [
                        const Expanded(
                          flex: 71,
                          child: EmbHeader(),
                        ),
                        Expanded(
                          flex: 1148,
                          child: pre_base_embroidery(
                            child: Column(
                              children: [
                                const Spacer(flex: 13),
                                Expanded(
                                  flex: 63,
                                  child: Align(
                                    alignment: Alignment.center,
                                    child: pre_category_header(
                                      zoomIcon: viewModel.getSelectedZoomIcon(),
                                      showZoomButton: state.showZoomButton,
                                      zoomButtonState: ButtonState.normal,
                                      onZoomButtonClicked: () =>
                                          viewModel.onZoomButtonClicked(),
                                      showFilterButton: state.showFilterButton,
                                      isFilterApplied: state.isFilterApplied,
                                      onFilterButtonClicked: () => viewModel
                                          .onFilterButtonClicked(context),
                                      onFilterResetButtonClicked:
                                          viewModel.onFilterCloseButtonClicked,
                                      filterButtonInfo: state.isFilterApplied
                                          ? l10n.icon_filterapplied
                                          : l10n.icon_filter,
                                      showInformationButton:
                                          state.showInformationButton,
                                      informationButtonState:
                                          state.formationButtonState,
                                      onInformationButtonClicked: () =>
                                          viewModel.onInformationButtonClicked(
                                              context),
                                      realPreviewButtonState:
                                          state.realPreviewButtonState,
                                      onRealPreviewButtonClicked:
                                          viewModel.onRealPreviewButtonClicked,
                                    ),
                                  ),
                                ),
                                const Spacer(flex: 12),
                                const Expanded(
                                  flex: 958,
                                  child: pre_base_white(),
                                ),
                                const Spacer(
                                  flex: 102,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const Expanded(
                          flex: 61,
                          child: EmbFooter(),
                        ),
                      ],
                    )),
                    Column(
                      children: [
                        const Spacer(flex: 171),
                        Expanded(
                          flex: 438,
                          child: Row(
                            children: [
                              const Spacer(flex: 18),
                              Expanded(
                                flex: 699,
                                child: pre_category_preview(
                                  isEnterPattern: state.isEnterPattern,
                                  patternWidth: state.widthValue,
                                  patternHeight: state.heightValue,
                                  flameThumbnail: FlameThumbnailType
                                      .values[state.frameIndex],
                                  colorNumber: state.numberOfColorsValue,
                                  colorChange: state.colorChangesValue,
                                  stitchTime: state.totalTimeValue,
                                  preview: Preview(
                                    patternDisplayInfoList:
                                        state.patternDisplayInfoList,
                                    temporaryGroupDisplayInfoList:
                                        state.temporaryGroupDisplayInfoList,
                                    quiltImage: state.quiltImage,
                                    isQuiltPattern: state.isQuiltPattern,
                                  ),
                                ),
                              ),
                              const Spacer(flex: 83),
                            ],
                          ),
                        ),
                        const Spacer(flex: 671),
                      ],
                    ),
                  ],
                )
              : Stack(
                  children: [
                    MultiTouchBlocker(
                      child: Column(
                        children: [
                          const Expanded(
                            flex: 71,
                            child: EmbHeader(),
                          ),
                          Expanded(
                              flex: 1148,
                              child: Stack(
                                children: [
                                  pre_base_embroidery(
                                    child: Column(
                                      children: [
                                        const Spacer(flex: 13),
                                        Expanded(
                                          flex: 63,
                                          child: Align(
                                            alignment: Alignment.center,
                                            child: pre_category_header(
                                              zoomIcon: viewModel
                                                  .getSelectedZoomIcon(),
                                              showZoomButton:
                                                  state.showZoomButton,
                                              zoomButtonState:
                                                  ButtonState.normal,
                                              onZoomButtonClicked: () =>
                                                  viewModel
                                                      .onZoomButtonClicked(),
                                              showFilterButton:
                                                  state.showFilterButton,
                                              isFilterApplied:
                                                  state.isFilterApplied,
                                              onFilterButtonClicked: () =>
                                                  viewModel
                                                      .onFilterButtonClicked(
                                                          context),
                                              onFilterResetButtonClicked:
                                                  viewModel
                                                      .onFilterCloseButtonClicked,
                                              filterButtonInfo:
                                                  state.isFilterApplied
                                                      ? l10n.icon_filterapplied
                                                      : l10n.icon_filter,
                                              showInformationButton:
                                                  state.showInformationButton,
                                              informationButtonState:
                                                  state.formationButtonState,
                                              onInformationButtonClicked: () =>
                                                  viewModel
                                                      .onInformationButtonClicked(
                                                          context),
                                              realPreviewButtonState:
                                                  state.realPreviewButtonState,
                                              onRealPreviewButtonClicked:
                                                  viewModel
                                                      .onRealPreviewButtonClicked,
                                            ),
                                          ),
                                        ),
                                        const Spacer(flex: 12),
                                        Expanded(
                                          flex: 958,
                                          child: Stack(
                                            children: [
                                              pre_base_white(
                                                child: Column(
                                                  children: [
                                                    const Spacer(flex: 6),
                                                    const Expanded(
                                                      flex: 437,
                                                      child: Row(
                                                        children: [
                                                          Spacer(flex: 800),
                                                        ],
                                                      ),
                                                    ),
                                                    const Spacer(flex: 24),
                                                    Expanded(
                                                      flex: 384,
                                                      child: Row(children: [
                                                        const Spacer(flex: 18),
                                                        Expanded(
                                                          flex: 764,
                                                          child:
                                                              GridView.builder(
                                                            shrinkWrap: true,
                                                            itemCount: viewModel
                                                                .getAllCategoryImagesInfo()
                                                                .length,
                                                            controller: viewModel
                                                                .scrollController,
                                                            gridDelegate:
                                                                const SliverGridDelegateWithFixedCrossAxisCount(
                                                              crossAxisCount: 4,
                                                              crossAxisSpacing:
                                                                  12,
                                                              mainAxisSpacing:
                                                                  12,
                                                              mainAxisExtent:
                                                                  117,
                                                              childAspectRatio:
                                                                  0.7,
                                                            ),
                                                            itemBuilder: (context,
                                                                    index) =>
                                                                CustomTooltip(
                                                              message: viewModel
                                                                  .getCategoryTips(
                                                                      index),
                                                              child:
                                                                  grp_grid_thumbnail(
                                                                key: Key(
                                                                    "EmbCategoryListView$index"),
                                                                feedbackControl:
                                                                    null,
                                                                onTap: () => viewModel
                                                                    .onCategoryButtonClicked(
                                                                        index),
                                                                text: viewModel
                                                                    .getSubstringBeforeSpace(
                                                                        index),
                                                                isDisney: index ==
                                                                    disneyIndex,
                                                                child: viewModel
                                                                    .getAllCategoryImagesInfo()[
                                                                        index]
                                                                    .image,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                        const Spacer(flex: 18),
                                                      ]),
                                                    ),
                                                    const Spacer(flex: 19),
                                                    Expanded(
                                                      flex: 80,
                                                      child: Row(
                                                        children: [
                                                          const Spacer(
                                                              flex: 18),

                                                          /// grp_btn_memory
                                                          Expanded(
                                                            flex: 182,
                                                            child:
                                                                CustomTooltip(
                                                              message: l10n
                                                                  .tt_embcate_memory,
                                                              child:
                                                                  grp_btn_memory_category(
                                                                onTap: viewModel
                                                                    .onMemoryCategoryButtonClicked,
                                                              ),
                                                            ),
                                                          ),

                                                          const Spacer(
                                                              flex: 206),

                                                          /// grp_btn_photostitch
                                                          Expanded(
                                                            flex: 182,
                                                            child:
                                                                grp_btn_photostitch(
                                                              text: l10n
                                                                  .icon_embcate_photostitch,
                                                              onTap: viewModel
                                                                  .onPicturePlayButtonClicked,
                                                              feedBackControl:
                                                                  null,
                                                            ),
                                                          ),

                                                          const Spacer(
                                                              flex: 12),

                                                          /// grp_btn_mdc/grp_btn_IQ
                                                          viewModel.isBrother()
                                                              ? Expanded(
                                                                  flex: 182,
                                                                  child:
                                                                      grp_btn_mdc(
                                                                    text: l10n
                                                                        .icon_00500_2,
                                                                    onTap: viewModel
                                                                        .onMDCButtonClicked,
                                                                  ),
                                                                )
                                                              : Expanded(
                                                                  flex: 182,
                                                                  child:
                                                                      grp_btn_IQ(
                                                                    onTap: viewModel
                                                                        .onMDCButtonClicked,
                                                                    child:
                                                                        const ico_IQDesigner_logo(),
                                                                  ),
                                                                ),

                                                          const Spacer(
                                                              flex: 18),
                                                        ],
                                                      ),
                                                    ),
                                                    const Spacer(flex: 8),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Expanded(
                                          flex: 102,
                                          child: Stack(
                                            children: [
                                              Column(
                                                children: [
                                                  const Spacer(flex: 12),
                                                  Expanded(
                                                    flex: 80,
                                                    child: Row(
                                                      children: [
                                                        const Spacer(flex: 12),
                                                        viewModel.isAddModel()
                                                            ? Expanded(
                                                                flex: 152,
                                                                child:
                                                                    grp_btn_return(
                                                                  buttonText: l10n
                                                                      .icon_return,
                                                                  onTap: viewModel
                                                                      .onReturnButtonClicked,
                                                                ),
                                                              )
                                                            : const Spacer(
                                                                flex: 152),
                                                        const Spacer(flex: 48),
                                                        Expanded(
                                                            flex: 84,
                                                            child:
                                                                grp_btn_embthreadcutting_category(
                                                              onTap: () => viewModel
                                                                  .onEmbThreadCuttingButtonClicked(),
                                                            )),
                                                        const Spacer(
                                                          flex: 504,
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                  const Spacer(flex: 10),
                                                ],
                                              ),
                                              Column(
                                                children: [
                                                  const Spacer(flex: 4),
                                                  Expanded(
                                                    flex: 96,
                                                    child: Row(
                                                      children: [
                                                        const Spacer(
                                                          flex: 304,
                                                        ),
                                                        Expanded(
                                                            flex: 320,
                                                            child: grp_str_text1(
                                                                text: l10n
                                                                    .icon_00042,
                                                                align: Alignment
                                                                    .centerLeft,
                                                                fontSize: 24,
                                                                color: const Color(
                                                                    0xffffffff))),
                                                        const Spacer(
                                                          flex: 176,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  const Spacer(flex: 2)
                                                ],
                                              )
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              )),
                          const Expanded(
                            flex: 61,
                            child: EmbFooter(),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      children: [
                        const Spacer(flex: 171),
                        Expanded(
                          flex: 438,
                          child: Row(
                            children: [
                              const Spacer(flex: 18),
                              Expanded(
                                flex: 699,
                                child: pre_category_preview(
                                  isEnterPattern: state.isEnterPattern,
                                  patternWidth: state.widthValue,
                                  patternHeight: state.heightValue,
                                  flameThumbnail: FlameThumbnailType
                                      .values[state.frameIndex],
                                  colorNumber: state.numberOfColorsValue,
                                  colorChange: state.colorChangesValue,
                                  stitchTime: state.totalTimeValue,
                                  preview: Preview(
                                    patternDisplayInfoList:
                                        state.patternDisplayInfoList,
                                    temporaryGroupDisplayInfoList:
                                        state.temporaryGroupDisplayInfoList,
                                    quiltImage: state.quiltImage,
                                    isQuiltPattern: state.isQuiltPattern,
                                  ),
                                ),
                              ),
                              const Spacer(flex: 83),
                            ],
                          ),
                        ),
                        const Spacer(flex: 671),
                      ],
                    ),
                  ],
                ),
          state.showZoomList
              ? MultiTouchBlocker(
                  child: Column(
                  children: [
                    const Spacer(flex: 78),
                    Expanded(
                      flex: 434,
                      child: Row(
                        children: [
                          const Spacer(flex: 6),
                          Expanded(
                            flex: 145,
                            child: ZoomSelectorPopup(
                              zoomList: viewModel.zoomDisplayList,
                              selectZoomValue: state.selectedZoomScale,
                              closePopup: viewModel.closeZoomPopup,
                              onZoomButtonClick: viewModel.onZoomPopupListClick,
                            ),
                          ),
                          const Spacer(flex: 649),
                        ],
                      ),
                    ),
                    const Spacer(flex: 768),
                  ],
                ))
              : Container(),
        ],
      ),
    );
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup(WidgetRef ref) =>
      ref.read(categorySelectorViewModelProvider.notifier).registerNamedPopup();
}
