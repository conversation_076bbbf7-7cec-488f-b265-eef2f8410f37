import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_candle_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'line_candle_size_popup_view_interface.dart';

/// 読み出したSizeデータの変換倍率 '読み出したデータの単位/10=mm'
const double _conversionRate = 10.0;

final lineCandleSizeViewModelProvider = StateNotifierProvider.autoDispose<
    LineCandleSizeStateViewInterface,
    LineCandleSizeState>((ref) => LineCandleSizeViewModel(ref));

class LineCandleSizeViewModel extends LineCandleSizeStateViewInterface {
  LineCandleSizeViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineCandleSizeState(
                sizeDisplayValue: "",
                isDefaultStyle: false,
                plusButtonValid: false,
                minusButtonValid: false),
            ref) {
    update();
  }

  ///
  /// ステップ量
  ///
  final int _stepLongPressValue = 10;
  final int _stepClickValue = 1;

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  /// サイズ表示星
  ///
  bool _isSizeValueDisplayStar =
      LineCandleModel().getSize() != LineCandleModel.sizeNotUpdating
          ? false
          : true;

  ///
  /// サイズ値
  ///
  int _sizeValue = LineCandleModel().getSize();

  @override
  void update() {
    state = state.copyWith(
      sizeDisplayValue: _getSizeDisplayValue(),
      isDefaultStyle: _isDefaultStyle(),
      plusButtonValid: _getPlusButtonState(),
      minusButtonValid: _getMinusButtonState(),
    );
  }

  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isSizeValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSizeValueDisplayStar = false;

      ///  Model 更新
      _sizeValue = LineCandleModel().sizeDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_sizeValue <= LineCandleModel.miniSizeValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress == true) {
      _sizeValue -= _sizeValue % _stepLongPressValue;
      if (_sizeValue > LineCandleModel.miniSizeValue) {
        ///  Model 更新
        _sizeValue -= _stepLongPressValue;
      }
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      _sizeValue -= _stepClickValue;
    }

    update();
    return true;
  }

  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_isSizeValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSizeValueDisplayStar = false;

      ///  Model 更新
      _sizeValue = LineCandleModel().sizeDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_sizeValue == LineCandleModel.maxiSizeValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress == true) {
      _sizeValue -= _sizeValue % _stepLongPressValue;
      if (_sizeValue < LineCandleModel.maxiSizeValue) {
        ///  Model 更新
        _sizeValue += _stepLongPressValue;
      }
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      _sizeValue += _stepClickValue;
    }

    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineCandleSize.toString());
    if (_isSizeValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int sizeValue = LineCandleModel().getSize();

    /// Model 更新
    LineCandleModel().setSize(_sizeValue);
    if (LineCandleModel().setMdcCandleWickingSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (sizeValue != _sizeValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// 大きさの表示値を取得します
  ///
  String _getSizeDisplayValue() {
    /// cmからmmへ
    double sizeValue = _sizeValue / _conversionRate;

    if (_isSizeValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      }

      return "*.***";
    }

    if (currentSelectedUnit == Unit.mm) {
      return sizeValue.toStringAsFixed(1);
    }

    return ToolbarModel.getDisplayInchShowValue(sizeValue);
  }

  ///
  /// 大きさ表示テキスト スタイルを取得します
  ///
  bool _isDefaultStyle() {
    if (_isSizeValueDisplayStar) {
      return true;
    }

    if (_sizeValue == LineCandleModel().sizeDefaultValue) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isSizeValueDisplayStar) {
      return true;
    }

    if (_sizeValue <= LineCandleModel.miniSizeValue) {
      return false;
    }

    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isSizeValueDisplayStar) {
      return true;
    }

    if (_sizeValue >= LineCandleModel.maxiSizeValue) {
      return false;
    }

    return true;
  }
}
