import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'run_pitch_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class RunPitchPopupState with _$RunPitchPopupState {
  const factory RunPitchPopupState({
    required bool isRunPitchMinusToLimit,
    required bool isRunPitchPlusToLimit,
    required String runPitchInputValue,
    required bool runPitchDisplayTextStyle,
  }) = _RunPitchPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class RunPitchPopupStateViewInterface
    extends ViewModel<RunPitchPopupState> {
  RunPitchPopupStateViewInterface(super.state, this.ref);

  ///
  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool miniRunPitch(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool plusRunPitch(bool isLongPress);

  ///
  /// チェーンステッチのデフォルト値
  ///
  int get defaultValue;

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
