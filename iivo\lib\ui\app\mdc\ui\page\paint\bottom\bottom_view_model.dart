import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../memory/memory.dart';
import '../../../../../../../model/device_memory_model.dart';
import '../../../../../../../model/handel_model.dart';
import '../../../../../../../model/machine_config_model.dart';
import '../../../../../../global_popup/global_popup_export.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../global_popup/global_popups/err_data_momory_full/err_data_momory_full_view_model.dart';
import '../../../../../../global_popup/global_popups/err_mdc_import_custom_pattern_change_after_saving/err_mdc_import_custom_pattern_change_after_saving_view_model.dart';
import '../../../../../../global_popup/global_popups/err_mdc_import_custom_pattern_not_saved_ext_memory/err_mdc_import_custom_pattern_not_saved_ext_memory_view_model.dart';
import '../../../../model/device_info_model.dart';
import '../../../../model/memory_model.dart';
import '../../../../model/paint/clip_board_model.dart';
import '../../../../model/paint/draw_canvas_model.dart';
import '../../../../model/paint/drawing_type_model.dart';
import '../../../../model/paint/edit_object_model.dart';
import '../../../../model/paint/library_isolate.dart';
import '../../../../model/paint/line_property_popup_model.dart';
import '../../../../model/paint/magnification_model.dart';
import '../../../../model/paint/paint_model.dart';
import '../../../../model/paint/pen_model.dart';
import '../../../../model/paint/scan_model.dart';
import '../../../../model/paint/select_model.dart';
import '../../../../model/paint/surface_property_popup_model.dart';
import '../../../../model/paint/top_bar_model.dart';
import '../../../../model/resume_history_model.dart';
import '../../../../model/stitch/draw_region_model.dart';
import '../../../page_route.dart';
import '../paint_page_view_model.dart';
import 'bottom_view_interface.dart';
import 'component/save_popup.dart';

final bottomProvider =
    StateNotifierProvider.autoDispose<BottomViewInterface, BottomState>(
        (ref) => BottomViewModel(ref));

class BottomViewModel extends BottomViewInterface {
  BottomViewModel(this._ref) : super(const BottomState()) {
    DrawCanvasModel().updateBottomCallback = update;
  }

  @override
  void build() {
    super.build();
    update();
  }

  ///
  /// providerのref
  ///
  final Ref _ref;

  ///
  /// ViewModel更新
  ///
  @override
  void update() {
    state = state.copyWith(
      selectButtonState: _getSelectButtonState(),
      redoButtonState: _getRedoState(),
      undoButtonState: _getUndoState(),
      isEnglish: DeviceInfoModel().currentLanguage == Language.LANG_ENGLISH,
    );
  }

  @override
  void dispose() {
    super.dispose();
    DrawCanvasModel().updateBottomCallback = null;
  }

  ///
  /// clipツール、クリップ部分選択します。
  ///
  @override
  void onSelectButtonClick(BuildContext context) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      final int preDrawingType = DrawingTypeModel.mdcDrawingType;
      _mayBeUpdateDrawingType(MdcDrawingType.drawtype_area_select);
      if (_needChangeAreaSelectCanvasImage(preDrawingType)) {
        EditObjectModel().updateCanvasImageWithoutParts();
      }
      MdcLibrary().apiBinding.setMdcAreaSelectType(SelectModel.selectTypes);

      /// Model更新
      TopBarModel().setDragMoveFlg(false);
      DrawCanvasModel().penState.finishLineDraw();

      _openSelectPopup(context);

      /// view更新
      state = state.copyWith(
        selectButtonState: DrawingTypeModel.mdcDrawingType ==
                MdcDrawingType.drawtype_area_select
            ? ButtonState.select
            : ButtonState.normal,
      );
    });
  }

  ///
  /// CancelButtonクリック
  ///
  @override
  void onCancelButtonClick(BuildContext context) {
    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      PaintModel().closeAllPopup(context);
      updateMagnificationByChild?.call();

      bool undoRedoFlg = false;
      bool scanImageFlg = false;

      if (EditObjectModel().checkCanvas() ||
          ResumeHistoryModel().existUndoData() ||
          ResumeHistoryModel().existRedoData()) {
        undoRedoFlg = true;
      }

      if (ScanModel().hasBgImage()) {
        scanImageFlg = true;
      }

      var errorCode = MdcLibrary()
          .apiBinding
          .checkMDCPaintToEmbSelect(undoRedoFlg, scanImageFlg);

      if (errorCode == MdcLibraryError.mdcErrorInvalidPanel) {
        GlobalPopupRoute()
            .updateErrorState(nextRoute: GlobalPopupRouteEnum.ERR_NEEDLE_UP);
        errNeedleUpFunc = _clearAppErrorState;
      }

      if (errorCode == MdcLibraryError.mdcErrorRequiresConfirmation) {
        if (MachineConfigModel().isBrother) {
          GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum.ERR_MDC_CANCEL_B);
          errMdcCancelBOKFunc = _errMdcCancelOKBFunc;
          errMdcCancelBCancelFunc = _clearAppErrorState;
        } else {
          GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum.ERR_MDC_CANCEL_T);
          errMdcCancelTOKFunc = _errMdcCancelOKBFunc;
          errMdcCancelTCancelFunc = _clearAppErrorState;
        }
      }

      if (errorCode == MdcLibraryError.mdcErrorTransitionOK) {
        SystemSoundPlayer().play(SystemSoundEnum.accept);
        errorCode = MdcLibrary().apiBinding.gotoMDCPaintToEmbSelect();
        if (errorCode == MdcLibraryError.mdcNoError) {
          _onCancelClick();
        }
      }
    });
  }

  ///
  /// AllCleanButtonクリック
  ///
  @override
  void onAllCleanButtonClick(BuildContext context) {
    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      if (ResumeHistoryModel().historySize() > 1 ||
          EditObjectModel().checkCanvas()) {
        PaintModel().closeAllPopup(context);
        updateMagnificationByChild?.call();

        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_MDC_CREATE_NEW);
        errMdcCreateNewOKFunc = _onAllCleanForOkButtonClick;
        errMdcCreateNewCancelFunc = _clearAppErrorState;
      } else {
        if (ScanModel().hasBgImage()) {
          PaintModel().closeAllPopup(context);
          updateMagnificationByChild?.call();

          GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum.ERR_MDC_BACKGROUND_DELETE);
          errMdcBackgroundDeleteOKFunc = _onAllCleanForOkButtonClick;
          errMdcBackgroundDeleteCancelFunc = _clearAppErrorState;
        } else {
          SystemSoundPlayer().play(SystemSoundEnum.invalid);
          EditObjectModel().allClear();
          ResumeHistoryModel().exitResume();
        }
      }
    });
  }

  ///
  /// キャンバスを空にしてページをジャンプ
  ///
  void _onCancelForOkButtonClick() {
    PenModel().clearAllData();

    DrawingTypeModel().reset();
    EditObjectModel().reset();
    MemoryModel().reset();

    LinePropertyPopupModel().toDefault();
    SurfacePropertyPopupModel().toDefault();
    ResumeHistoryModel().exitResume();
    ResumeHistoryModel().clearAllHistory();
    SelectModel.selectTypes = SelectModel.defaultType;

    MdcLibrary()
        .apiBinding
        .setMdcImageDrawingDensity(DensityLevel.mdc100BackGround0.number);
    MdcLibrary().apiBinding.gotoMDCPaintToEmbSelect();

    LibraryIsolate().killIsolate();

    EmbLibrary().apiBinding.initEmb();

    PagesRoute().pushNamedAndRemoveUntil(
      nextRoute: PageRouteEnum.patternSelect,
      untilRoute: PageRouteEnum.home,
    );
  }

  ///
  /// キャンバスを空にしてページをジャンプ
  ///
  void _onCancelClick() {
    LibraryIsolate().killIsolate();
    EmbLibrary().apiBinding.initEmb();
    PagesRoute().pushNamedAndRemoveUntil(
      nextRoute: PageRouteEnum.patternSelect,
      untilRoute: PageRouteEnum.home,
    );
  }

  ///
  /// UndoButtonクリック
  ///
  @override
  void onUndoButtonClick(BuildContext context) {
    if (ResumeHistoryModel().mdcUndoSize <= ResumeHistoryModel.undoStartIndex) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      PaintModel().closeAllPopup(context);
      updateMagnificationByChild?.call();
      ResumeHistoryModel().undo();

      /// view更新
      update();

      /// 他の画面を更新する
      _ref
          .read(paintPageProvider.notifier)
          .updatePaintPageByChild(ModuleType.bottom);
    });
  }

  ///
  /// RedoButtonクリック
  ///
  @override
  void onRedoButtonClick(BuildContext context) {
    if (ResumeHistoryModel().mdcUndoSize >= ResumeHistoryModel().mdcSnapSize) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      PaintModel().closeAllPopup(context);
      updateMagnificationByChild?.call();
      ResumeHistoryModel().redo();

      /// view更新
      update();

      /// 他の画面を更新する
      _ref
          .read(paintPageProvider.notifier)
          .updatePaintPageByChild(ModuleType.bottom);
    });
  }

  ///
  /// キャンバスを空にする
  ///
  void _onAllCleanForOkButtonClick() {
    DeviceErrorCode deviceError = GlobalPopupRoute().resetErrorState();
    if (deviceError == DeviceErrorCode.devNoError) {
      var (errorCode: _, imgInfo: _) = MdcLibrary().apiBinding.clearMdcAll();

      EditObjectModel().allClear();
      DrawCanvasModel().penState.finishLineDraw();
      DrawCanvasModel().canvasController.reset();

      ResumeHistoryModel().exitResume();
      ResumeHistoryModel().clearAllHistory();
      ResumeHistoryModel().backSnapshot();

      ScanModel().reset();
      ScanModel().clearSketchesImageData();
      ScanModel().isLoadScanImage = false;
      ScanModel().scanImageType = ScanImageType.none;
      SelectModel.selectTypes = SelectModel.defaultType;
      MdcLibrary()
          .apiBinding
          .setMdcImageDrawingDensity(DensityLevel.mdc100BackGround0.number);

      TopBarModel().reset();
      ClipBoardModel().reset();
      MagnificationModel().reset();

      /// view更新
      update();

      ///
      /// 他の画面を更新する
      ///
      _ref
          .read(paintPageProvider.notifier)
          .updatePaintPageByChild(ModuleType.bottom);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }
  }

  ///
  /// Memoryボタンのクリック関数
  ///
  @override
  void onMemoryButtonClick(BuildContext context) {
    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      /// 部分模様あるの場合、ライブラリは模様が表示範囲外に移動したかどうかを判断できないため、模様を確認する必要があります。
      if (EditObjectModel().hasPartsImageInfo()) {
        PaintModel().closeAllPopup(context);
        updateMagnificationByChild?.call();
        _mayBeConfirmUnsettledObjectAndBackSnapshot();

        /// View更新
        _ref
            .read(paintPageProvider.notifier)
            .updatePaintPageByChild(ModuleType.bottom);
      }

      if (MdcLibrary().apiBinding.isMdcDrawingPatternPresence().result ==
          false) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }

      SystemSoundPlayer().play(SystemSoundEnum.accept);
      if (_needChangeAreaSelectCanvasImage(DrawingTypeModel.mdcDrawingType)) {
        EditObjectModel().updateCanvasImageWithoutParts();
      }

      PaintModel().closeAllPopup(context);
      updateMagnificationByChild?.call();

      /// Model更新
      PenModel().resetDrawingState();

      /// View更新
      /// 他の画面を更新する
      _ref
          .read(paintPageProvider.notifier)
          .updatePaintPageByChild(ModuleType.bottom);

      /// 画面遷移
      ///  Memoryポップアップを開く
      _openSaveDeviceSelectPopup(context);
    });
  }

  ///
  /// nextボタンクリク
  ///
  @override
  void onNextButtonClicked(BuildContext context) {
    final bool isButtonEnable = TpdLibrary()
        .apiBinding
        .setMatrixEnableListNotOverwrited(
            MachineKeyState.machineKeyEnableAllNG);
    if (isButtonEnable == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      Log.debugTrace("onNext");
      if (MdcLibrary()
              .apiBinding
              .isMdcDrawingPatternPresenceForNext(EditObjectModel().reqProc)
              .result ==
          false) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        TpdLibrary().apiBinding.clearMatrixEnableListNotOverwrited(
            MachineKeyState.machineKeyEnableAll);
        return;
      }

      /// 有効音を鳴らす
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      GlobalPopupRoute().showPleaseWaitPopup(
          arguments: {GlobalPopupRoute.isStopSystemSound: true});
      _mayBeConfirmUnsettledObjectAndBackSnapshot();

      final (
        errorCode: outErrorCode,
        regionNum: outRegionNum,
      ) = MdcLibrary().apiBinding.changeMdcPhaseDetail();
      if (outErrorCode != MdcLibraryError.mdcNoError) {
        GlobalPopupRoute().resetPleaseWaitPopup();
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        TpdLibrary().apiBinding.clearMatrixEnableListNotOverwrited(
            MachineKeyState.machineKeyEnableAll);
        return;
      }

      PaintModel().closeAllPopup(context);
      LibraryIsolate().lockMdcProcess();
      Future.delayed(pleaseWaitTime).then((_) async {
        /// 初期化
        DrawRegionModel().reset();

        /// 面と線を全部Focusしたの状態でstitch画面に遷移することが必要です
        /// ここの修正は全部Focusであり
        /// Focusしたの状態のままでお絵描きイメージを取得する
        DrawRegionModel().updateCanvasImage();

        /// [changeMdcPhaseDetail]から取得したの「stitch画面模様数」を保存します(性能UPの為に)
        DrawRegionModel().regionNum = outRegionNum;

        /// プロセスLock停止
        LibraryIsolate().unlockMdcProcess();
        Log.debugTrace("try to kill isolate");
        await LibraryIsolate().killIsolate();
        PenModel().resetDrawingState();
        GlobalPopupRoute().resetPleaseWaitPopup();

        /// 次の遷移
        updateMagnificationByChild?.call();

        /// brother_panel遷移
        final errorCode = MdcLibrary().apiBinding.gotoMDCPaintToMDCDetailSet();
        if (errorCode != MdcLibraryError.mdcNoError) {
          Log.errorTrace("goto MdcStitch error");
          TpdLibrary().apiBinding.clearMatrixEnableListNotOverwrited(
              MachineKeyState.machineKeyEnableAll);
          return;
        }

        /// MDC縫製設置画面の初回履歴を記憶
        ResumeHistoryModel().clearAllHistory();
        ResumeHistoryModel().backSnapshot();

        Log.debugTrace("goto MdcStitch");
        PagesRoute().pushReplacement(nextRoute: PageRouteEnum.stitch);
        TpdLibrary().apiBinding.clearMatrixEnableListNotOverwrited(
            MachineKeyState.machineKeyEnableAll);
      });
    }).then((value) {
      if (value == false) {
        TpdLibrary().apiBinding.clearMatrixEnableListNotOverwrited(
            MachineKeyState.machineKeyEnableAll);
      } else {
        /// do nothing
      }
    });
  }

  ///
  /// Undoの状態取得する
  ///
  ButtonState _getUndoState() => ResumeHistoryModel().mdcUndoSize <= 1
      ? ButtonState.disable
      : ButtonState.normal;

  ///
  /// Redoの状態取得する
  ///
  ButtonState _getRedoState() =>
      ResumeHistoryModel().mdcUndoSize >= ResumeHistoryModel().mdcSnapSize
          ? ButtonState.disable
          : ButtonState.normal;

  ///
  /// 範囲選択ボタン状態を取得する
  ///
  ButtonState _getSelectButtonState() {
    return TopBarModel().getDragMoveFlg() ||
            DrawingTypeModel.mdcDrawingType !=
                MdcDrawingType.drawtype_area_select
        ? ButtonState.normal
        : ButtonState.select;
  }

  ///
  /// 選択範囲を開く
  ///
  void _openSelectPopup(BuildContext context) {
    PaintModel().setPopupType(PopupType.areaSelect);

    /// 他の画面を更新する
    _ref
        .read(paintPageProvider.notifier)
        .updatePaintPageByChild(ModuleType.bottom);
    updateMagnificationByChild?.call();

    /// 画面遷移
    PopupNavigator.pushNamedAndRemoveAll(
      context: context,
      nextRouteName: PopupEnum.bottomAreaSelect,
    ).then((value) {
      PaintModel().clearPopupType();
    });
  }

  ///
  /// デーバス選択ポップアップを開ける
  ///
  void _openSaveDeviceSelectPopup(BuildContext context) {
    MdcImportDataUsingNumber checkResult =
        MdcLibrary().apiBinding.isMdcUsingImportDataNumber(true).usingNumBuff;
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => SavePopup(
            onCancelClick: () => _closeSaveDeviceSelectPopup(context),
            onUSB1Click: (usbInfo) {
              if (usbInfo == null) {
                SystemSoundPlayer().play(SystemSoundEnum.invalid);
                return;
              }
              if (checkResult.decoSetNum != 0 || checkResult.motifSetNum != 0) {
                GlobalPopupRoute().updateErrorState(
                  nextRoute: GlobalPopupRouteEnum
                      .ERR_MDC_IMPORT_CUSTOM_PATTERN_NOT_SAVED_EXT_MEMORY,
                  arguments: ErrMdcImportCustomPatternNotSavedExtMemoryArgument(
                    onOKButtonClicked: GlobalPopupRoute().resetErrorState,
                  ),
                );
                return;
              }

              SystemSoundPlayer().play(SystemSoundEnum.accept);
              _saveMemory(context, DeviceKind.usb, usbPath: usbInfo.usbPath);
            },
            onUSB2Click: (usbInfo) {
              if (usbInfo == null) {
                SystemSoundPlayer().play(SystemSoundEnum.invalid);
                return;
              }
              if (checkResult.decoSetNum != 0 || checkResult.motifSetNum != 0) {
                GlobalPopupRoute().updateErrorState(
                  nextRoute: GlobalPopupRouteEnum
                      .ERR_MDC_IMPORT_CUSTOM_PATTERN_NOT_SAVED_EXT_MEMORY,
                  arguments: ErrMdcImportCustomPatternNotSavedExtMemoryArgument(
                    onOKButtonClicked: GlobalPopupRoute().resetErrorState,
                  ),
                );
                return;
              }

              SystemSoundPlayer().play(SystemSoundEnum.accept);
              _saveMemory(context, DeviceKind.usb, usbPath: usbInfo.usbPath);
            },
            onInternalStorageClick: () {
              if (checkResult.decoSetNum != 0 || checkResult.motifSetNum != 0) {
                if (MemoryModel.importPatternSavingNotifyFlag) {
                  MemoryModel.importPatternSavingNotifyFlag = false;
                  GlobalPopupRoute().updateErrorState(
                    nextRoute: GlobalPopupRouteEnum
                        .ERR_MDC_IMPORT_CUSTOM_PATTERN_CHANGE_AFTER_SAVING,
                    arguments:
                        ErrMdcImportCustomPatternChangeAfterSavingArgument(
                            onOKButtonClicked: (_) {
                      _saveMemory(
                        context,
                        DeviceKind.internalStorage,
                      );
                    }),
                  );
                  return;
                }
              }

              SystemSoundPlayer().play(SystemSoundEnum.accept);
              _saveMemory(context, DeviceKind.internalStorage);
            }),
        barrier: false,
      ),
    );
  }

  ///
  /// デーバス選択ポップアップを閉じる
  ///
  void _closeSaveDeviceSelectPopup(BuildContext context) {
    PopupNavigator.pop(context: context);
  }

  ///
  /// Saveメモリー
  ///
  /// [deviceKind] : デーバス種類(usb、internalStorageのみ)
  /// [usbPath] : USBの読み取りパス
  ///
  void _saveMemory(BuildContext context, DeviceKind deviceKind,
      {String usbPath = ""}) {
    /// Saving中ポップアップ　開ける
    _openSavingPopup(deviceKind);

    /// ストレージファイル
    PaintMemoryModel.savePm9File(deviceKind, usingUsbPath: usbPath).then(
      (value) {
        /// エラー確認
        if (value.accessError != AccessError.none) {
          if (value.mdcLibraryError == MdcLibraryError.mdcErrorFileSaveFailed) {
            GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum.ERR_DATA_MOMORY_FULL,
              arguments: DataMomoryFullArgument(
                onOKButtonClicked: GlobalPopupRoute().resetErrorState,
              ),
            );
            return;
          } else {
            return HandelModel.handleMemoryAccessError(value.accessError);
          }
        }

        /// Saving中ポップアップ　閉じる
        _closeSavingPopup();

        /// 画面遷移
        /// デーバス選択ポップアップを閉じる
        _closeSaveDeviceSelectPopup(context);
      },
    );
  }

  /// Saving中ポップアップ　開ける
  ///
  /// デバイス種類より、ポップアップのアイコンが変わる
  void _openSavingPopup(DeviceKind deviceKind) {
    switch (deviceKind) {
      case DeviceKind.usb:
        GlobalPopupRoute().openUsbSavingPopup();
        break;
      case DeviceKind.internalStorage:
        GlobalPopupRoute().openPcSavingPopup();
        break;
      default:
        Log.e(
            tag: memoryAccessErrorLogTag,
            description: "Unexpected deviceKind:$deviceKind");
        break;
    }
  }

  /// Saving中ポップアップ　閉じる
  void _closeSavingPopup() => GlobalPopupRoute().resetErrorState();

  ///
  /// アプリのエラー状態をクリアする
  ///
  void _clearAppErrorState() => GlobalPopupRoute().resetErrorState();

  void _errMdcCancelOKBFunc() {
    DeviceErrorCode deviceError = GlobalPopupRoute().resetErrorState();
    if (deviceError == DeviceErrorCode.devNoError) {
      _onCancelForOkButtonClick();
    } else {
      /// DoNothing
    }
  }

  ///
  /// 部分模様確定と履歴登録
  ///
  void _mayBeConfirmUnsettledObjectAndBackSnapshot() {
    if (EditObjectModel().hasPartsImageInfo() == false) {
      return;
    }

    ClipBoardModel().confirmUnsettledObject(needUpdateUI: true);
    ResumeHistoryModel().backSnapshot();
  }

  ///
  /// ライブラリ側描画の種類更新
  ///
  void _mayBeUpdateDrawingType(int targetType) {
    if (DrawingTypeModel.mdcDrawingType == targetType) {
      return;
    }

    /// Model更新
    DrawingTypeModel.mdcDrawingType = targetType;

    /// Lib更新
    MdcLibrary().apiBinding.setMdcDrawingType(targetType);
  }

  ///
  /// 範囲選択または消しゴム種類のイメージデータを更新必要かどうか
  ///
  bool _needChangeAreaSelectCanvasImage(int drawingType) =>
      drawingType != MdcDrawingType.drawtype_eraser &&
      drawingType != MdcDrawingType.drawtype_area_select;
}
