import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/magnification_model.dart';
import '../../../../../../model/stitch/view_area_model.dart';
import '../../../stitch_page_view_model.dart';
import 'magnification_popup_view_interface.dart';

final magnificationPopupViewModelProvider = StateNotifierProvider.autoDispose<
    MagnificationPopupViewInterface,
    MagnificationPopupState>((ref) => MagnificationPopupViewModel(ref));

class MagnificationPopupViewModel extends MagnificationPopupViewInterface {
  MagnificationPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const MagnificationPopupState(), ref) {
    update();
  }

  ///
  /// view更新
  ///
  @override
  void update() {
    state = state.copyWith(
        magnificationIndex: MagnificationModel()
            .valueToIndex(MagnificationModel.magnificationLevel));
  }

  ///
  /// クリックしてレベルを拡大
  ///
  @override
  void onMagnificationLevelClicked(int index) {
    int magnificationLevel;
    if (index >= MagnificationModel().magnificationLevelNumber()) {
      magnificationLevel = MagnificationModel.magnification_100;
    } else {
      magnificationLevel = MagnificationModel().indexToValue(index);
    }
    if (magnificationLevel == MagnificationModel.magnification_100) {
      ViewAreaModel().isDragMove = false;
    }

    /// 拡大中
    MagnificationModel.magnificationLevel = magnificationLevel;
    MagnificationModel().zoomEdit.zoomNowF = true;
    MagnificationModel().zoomInfoSet(MagnificationModel.magnificationLevel);

    /// view更新
    state = state.copyWith(
        magnificationIndex: MagnificationModel()
            .valueToIndex(MagnificationModel.magnificationLevel));

    DrawRegionModel().updateDisplayImageInfo(DrawRegionModel().handle).then(
        (value) =>

            /// 他の画面を更新する
            ref
                .read(stitchPageViewModelProvider.notifier)
                .updatePageByChild(ComponentType.topBar));
  }

  ///
  /// 拡大表示ポップアップを開く
  ///
  @override
  void maybeCloseMagnificationPopup() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
  }
}
