import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_change_l_frame_view_interface.dart';

final errChangeLFrameViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrChangeLFrameViewInterface, ErrChangeLFrameState,
        BuildContext>((ref, context) => ErrChangeLFrameViewModel(ref, context));

class ErrChangeLFrameViewModel extends ErrChangeLFrameViewInterface {
  ErrChangeLFrameViewModel(Ref ref, BuildContext context)
      : super(const ErrChangeLFrameState(), ref, context);

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    int errcode;
    errcode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERRFRAMEOFF);
    if (errcode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }
  }
}
