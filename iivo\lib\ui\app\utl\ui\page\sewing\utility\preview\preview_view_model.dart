import 'dart:async';
import 'dart:ffi';
import 'dart:typed_data';

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/camera_model.dart';
import '../../../../../../../../model/projector/camera_pen/camera_pen_type.dart';
import '../../../../../../../../model/projector_model.dart';
import '../../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../global_popup/global_popups/camera_popup/camera_popup_view_model.dart';
import '../../../../../../../global_popup/panel_popup_route.dart';
import '../../../../../model/camera_image_model.dart';
import '../../../../../model/pattern_model.dart';
import '../../../../../model/pattern_status_model.dart';
import '../../../../../model/preview_model.dart';
import '../../common/camera_pen/camera_pen_view_model.dart';
import '../utility_view_model.dart';
import 'preview_view_interface.dart';

final utilityPreviewViewModeProvider = StateNotifierProvider.autoDispose<
    UtilityPreviewViewMode,
    UtilityPreviewState>((ref) => UtilityPreviewViewMode(ref));

class UtilityPreviewViewMode extends UtilityPreviewViewModelInterface {
  UtilityPreviewViewMode(AutoDisposeStateNotifierProviderRef ref)
      : super(
            UtilityPreviewState(
                threadColor: defaultThreadColor,
                scaleRatio: '',
                previewImg: null),
            ref);

  @override
  void build() {
    super.build();

    /// プレビューイマジンと表示糸色更新
    state = state.copyWith(
      previewImg: _previewData.getPreviewImage(),
      threadColor: threadColorList[_previewData.getThreadColorsIndex()],
    );

    PatternStatusModel().listenTaperingState(ref, () {
      final imageInfo =
          UtlLibrary().apiBinding.getCurrentPatternImage().imageInfo;
      _previewData.updatePreviewImage(imageInfo);
      state = state.copyWith(
        previewImg: _previewData.getPreviewImage(),
        scaleRatio: _previewData.getScaleRatio(),
        threadColor: threadColorList[_previewData.getThreadColorsIndex()],
        isCameraPreviewDisplay: CameraModel().isCameraOpen == true &&
            CameraModel().isUtlCameraPopupOpen == false,
      );
    });

    PatternStatusModel().listenParameterState(ref, () {
      if (PreviewDataModel().isInRealPreview) {
        return;
      }
      final imageInfo =
          UtlLibrary().apiBinding.getCurrentPatternImage().imageInfo;
      _previewData.updatePreviewImage(imageInfo);

      state = state.copyWith(
        previewImg: _previewData.getPreviewImage(),
        scaleRatio: _previewData.getScaleRatio(),
      );
    });

    ref.listen(
      appDisplayUtlStateProvider.select(
          (value) => value.utlFuncSetting.ref.isBHSlitLengthRecognition),
      (previous, nextState) {
        Log.i(
            tag: "BH project BHSlitLength",
            description: "isBHSlitLengthRecognition:$nextState");
        startOrStopUpdateBHProject(nextState);
      },
    );

    ref.listen(
      fireImmediately: true,
      appDisplayUtlStateProvider
          .select((value) => value.utlFuncSetting.ref.isAspectUtlView),
      (previous, nextState) {
        if (nextState == true) {
          if (state.isCameraPreviewDisplay == false) {
            state = state.copyWith(
              isCameraPreviewDisplay: true,
              cameraImage: [],
            );
          } else {
            /// Do Nothing
          }

          CameraImageModel.cameraImagePollingControl
              .registerCameraImageCallBack(
            _refreshCameraImageUi,
          );
        } else {
          if (state.isCameraPreviewDisplay == true) {
            state = state.copyWith(
              isCameraPreviewDisplay: false,
              cameraImage: [],
            );
          } else {
            /// Do Nothing
          }

          CameraImageModel.cameraImagePollingControl
              .maybeUnregisterCameraImageCallBack(_refreshCameraImageUi);
        }
      },
    );
  }

  /// Modelデータ設定と読み込み
  PreviewDataModel get _previewData => PreviewDataModel();

  Timer? _projectTimer;

  final int _projectTimer_100ms = 100;

  @override
  void update() {
    // プレビューイマジンと表示糸色更新
    state = state.copyWith(
      previewImg: _previewData.getPreviewImage(),
      scaleRatio: _previewData.getScaleRatio(),
      threadColor: threadColorList[_previewData.getThreadColorsIndex()],
    );

    if (ref.exists(utilityViewModelProvider)) {
      ref.read(utilityViewModelProvider.notifier).updateCameraPenUI();
    }
  }

  @override
  void onThreadColorButtonClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    var threadColorIndex = _previewData.getThreadColorsIndex();

    threadColorIndex++;
    if (threadColorIndex >= threadColorList.length) {
      threadColorIndex = 0;
    }

    /// Modelデータ更新
    _previewData.setThreadColorsIndex(threadColorIndex);

    /// View更新
    state = state.copyWith(
      threadColor: threadColorList[threadColorIndex],
      previewImg: _previewData.getPreviewImage(),
    );

    /// Projector更新
    ProjectorModel().refreshUtlProjector();
    if (ref.exists(utilityViewModelProvider)) {
      ref.read(utilityViewModelProvider.notifier).updateCameraPenUI();
    }
  }

  @override
  Future<void> onCameraPreviewClick(BuildContext context) async {
    final errCode = UtlLibrary().apiBinding.startUtlCameraView();
    if (errCode == UtlLibraryError.utlErrorInvalid) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
        errCode == UtlLibraryError.utlErrorInvalidPanel) {
      return;
    }

    if (errCode != UtlLibraryError.utlNoError) {
      return;
    }

    /// 音再生
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 状態更新
    CameraModel().isUtlCameraPopupOpen = true;

    PanelPopupRoute().pushNamed(
        nextRoute: GlobalPopupRouteEnum.cameraPopup,
        arguments: const CameraArgument(isUtlOpen: true));
  }

  ///
  /// 実用画面でBH模様のプロジェクター投影を更新します
  ///
  /// isStart : BH長さ認識中
  ///
  @override
  Future<void> startOrStopUpdateBHProject(bool isStart) async {
    if (PatternDataModel().checkBHPatten() != true) {
      _projectTimer?.cancel();
      _projectTimer = null;

      /// プロジェクト戻に戻る更新します
      if (ProjectorModel()
              .isUtlProjectorOpened(UtlProjectorType.sewingPattern) ==
          true) {
        await ref
            .read(cameraPenViewModelProvider.notifier)
            .openBHCameraPenAndShowCameraPenUI();
        return;
      }

      return;
    }
    _projectTimer?.cancel();
    _projectTimer = null;
    if (isStart == false) {
      await ref
          .read(cameraPenViewModelProvider.notifier)
          .openBHCameraPenAndShowCameraPenUI();

      return;
    }

    /// needUpdateProjectorUI = falseの原因
    /// プロジェクトの更新が遅い、LCDPreviewImageの更新がブロックされました
    final cameraPenFunction = ref.read(cameraPenViewModelProvider.notifier);
    await cameraPenFunction.closeBHCameraPenAndCameraPenUI();

    _projectTimer =
        Timer.periodic(Duration(milliseconds: _projectTimer_100ms), (_) {
      /// BH模様じゃないなら、pollingの必要がない
      if (PatternDataModel().checkBHPatten() != true) {
        _projectTimer?.cancel();
        _projectTimer = null;
        return;
      }

      /// Polling Log
      if (getPollingLogFlagForDebug() == true) {
        Log.i(
            tag: "BH project",
            description: "isBHSlitLengthRecognition:refresh UI");
      }

      /// 必要なら、プロジェクト模様更新します
      if (ProjectorModel()
                  .isUtlProjectorOpened(UtlProjectorType.sewingPattern) ==
              true ||
          ProjectorModel().isUtlProjectorOpened(UtlProjectorType.guideline) ==
              true) {
        ProjectorModel().refreshUtlProjector(autoBacklight: false);
        return;
      }
    });
  }

  ///
  /// カメラ画像更新
  ///
  void _refreshCameraImageUi(Uint8List? image) {
    if (mounted == false) {
      Log.hello("mounted error");
      return;
    }

    if (image == null || image.isEmpty) {
      return;
    }

    state = state.copyWith(cameraImage: image);
  }

  @override
  void dispose() {
    super.dispose();

    _projectTimer?.cancel();
    _projectTimer = null;
  }
}
