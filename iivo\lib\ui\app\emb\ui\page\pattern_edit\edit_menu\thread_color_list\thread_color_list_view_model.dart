import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../global_popup/global_popups/err_this_key_cannot_used/err_this_key_cannot_used_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../../model/color_change_model.dart';
import '../../../../../model/pattern_model.dart';
import '../../../../../model/redo_undo_model.dart';
import '../../../../../model/thread_color_list_model.dart';
import '../../../../page_route.dart';
import '../../pattern_edit_view_model.dart';
import 'thread_color_list_view_interface.dart';

final threadColorListViewModelProvider = StateNotifierProvider.autoDispose<
    ThreadColorListViewModelInterface, ThreadColorListState>(
  (ref) => ThreadColorListViewModel(ref),
);

class ThreadColorListViewModel extends ThreadColorListViewModelInterface {
  ThreadColorListViewModel(Ref ref)
      : super(
            ThreadColorListState(
              scrollController: ScrollController(),
              displayList: [],
            ),
            ref) {
    /// View更新
    update();
  }

  @override
  void update() {
    /// View更新
    state = state.copyWith(
      displayList: ThreadColorListModel().getDisplayList(
        selectedPatternIndex: ThreadColorListModel().selectedGroupIndex,
        zoomScale: PatternModel().selectedZoomScaleInEditPage,
      ),
    );
  }

  @override
  void onPatternItemClicked(int patternIndex) {
    /// Model更新
    if (ThreadColorListModel().selectedGroupIndex == null ||
        ThreadColorListModel().selectedGroupIndex != patternIndex) {
      ThreadColorListModel().selectedGroupIndex = patternIndex;
    } else {
      ThreadColorListModel().selectedGroupIndex = null;
    }
    PatternModel().selectPatternWithGroupIndex(patternIndex);

    /// View更新
    update();

    if (ThreadColorListModel().selectedGroupIndex != null) {
      state.scrollController.jumpTo(_getJumpPositioned(patternIndex));
    } else {
      /// Do Noting
    }

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.colorList);
  }

  @override
  void onColorItemClicked(int patternIndex, int colorIndex) {
    if (PatternModel().isSelectedMultiEmb) {
      /// 複数選択時のヒントポップウィンドウ
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_THIS_KEY_CANNOT_USED,
          arguments: ErrThisKeyCannotUsedArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
    } else {
      /// Model更新
      ColorChangeModel().selectColorIndex = colorIndex;
      ColorChangeModel().selectPatternIndex = patternIndex;
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }
      PatternModel()
        ..selectedZoomScaleInEditPage = zoomList.first
        ..clearAllPatternImageCache();

      /// ページをジャンプ
      PagesRoute()
          .pushNamed(nextRoute: PageRouteEnum.colorChange)
          .then((value) {
        update();
        ref
            .read(patternEditViewModelProvider.notifier)
            .updateEditPageByChild(ModuleType.colorList);
      });
    }
  }

  ///
  /// リスト内でジャンプ可能な座標の計算
  ///
  double _getJumpPositioned(int patternIndex) {
    /// 線色リストのItem高さ
    double listAreaHeight = 777.0;

    /// Itemの間隔
    const double itemSpace = 8;

    /// 線色リストのItemの高さ
    double patternItemHeight = 134.0 + itemSpace;

    /// カラーリストのItemItemの高さ
    double threadItemHeight = 63.0 + itemSpace;

    /// リストの最大高さ
    double maxListHeight = 0.0;

    /// ジャンプ可能な最大座標
    double maxJumpPositioned = 0.0;

    double positioned = patternIndex * patternItemHeight;

    /// 線色リストを展開するかどうか
    if (ThreadColorListModel().selectedGroupIndex != null) {
      /// 展開時にカラーリストの高さを加算
      maxListHeight = patternItemHeight * state.displayList.length +
          threadItemHeight *
              state.displayList[ThreadColorListModel().selectedGroupIndex!]
                  .threadInfoDisplayList.length;
    } else {
      maxListHeight = patternItemHeight * state.displayList.length;
    }

    if (maxListHeight < listAreaHeight) {
      /// リストの全長が表示領域を超えていない場合
      maxJumpPositioned = 0;
    } else {
      /// リストの全長が表示領域を超えた場合
      maxJumpPositioned = maxListHeight - listAreaHeight;
    }

    return positioned.clamp(0, maxJumpPositioned);
  }
}
