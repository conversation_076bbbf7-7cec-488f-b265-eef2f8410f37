import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../../../../global_popup/global_popups/err_applique_ng_complex/err_applique_ng_complex_view_model.dart';
import '../../../../../../../../../../global_popup/global_popups/err_applique_ng_distance/err_applique_ng_distance_view_model.dart';
import '../../../../../../../../../../global_popup/global_popups/err_applique_ng_ex_in_overlap/err_applique_ng_ex_in_overlap_view_model.dart';
import '../../../../../../../../../../global_popup/global_popups/err_applique_ng_mem_over/err_applique_ng_mem_over_view_model.dart';
import '../../../../../../../../../../global_popup/global_popups/err_applique_some_parts_not_texture/err_applique_some_parts_not_texture_view_model.dart';
import '../../../../../../../../../../global_popup/global_popups/err_mcd_not_exchange_area_over/err_mcd_not_exchange_area_over_view_model.dart';
import '../../../../../../../../model/applique_model.dart';
import '../../../../../../../../model/edit_model.dart';
import '../../../../../../../../model/pattern_model.dart';
import '../../../../../pattern_edit_view_model.dart';
import 'applique_preview_interface.dart';

///
/// ステップ量
///
const _densityStepValue = 100;

///
/// ステップ量
///
const _widthStepValue = 10;

///
/// 黒い背景に白いテキスト
///
const TextStyle _blackTextWhiteBackground =
    TextStyle(color: Colors.white, backgroundColor: Colors.black);

///
/// 白い背景に黒いテキスト
///
const TextStyle _whiteTextBlackBackground = TextStyle(
    color: Color.fromARGB(255, 51, 51, 51), backgroundColor: Colors.white);

///
/// 白い背景と緑のテキスト
///
const TextStyle _greenTextBlackBackground = TextStyle(
    color: Color.fromARGB(255, 0, 255, 0), backgroundColor: Colors.white);

final appliquePreviewViewModelProvider = StateNotifierProvider.autoDispose<
    AppliquePreviewViewModelInterface, AppliquePreviewInterface>(
  (ref) => AppliquePreviewViewModel(ref),
);

class AppliquePreviewViewModel extends AppliquePreviewViewModelInterface {
  AppliquePreviewViewModel(this._ref)
      : super(
            const AppliquePreviewInterface(
              isUnitMm: true,
              textureOnState: true,
              textureSwitchState: false,
              nextState: false,
              internalDistanceTextStyle: _blackTextWhiteBackground,
              internalDistanceValue: '',
              satinDensityTextStyle: _blackTextWhiteBackground,
              satinDensityValue: '',
              zigzagDensityTextStyle: _blackTextWhiteBackground,
              zigzagDensityValue: '',
              zigzagDistanceTextStyle: _blackTextWhiteBackground,
              zigzagDistanceValue: '',
              zigzagWidthTextStyle: _blackTextWhiteBackground,
              zigzagWidthValue: '',
              satinWidthTextStyle: _blackTextWhiteBackground,
              satinWidthValue: '',
              isOpenPopup: false,
              appliquePreviewImage: null,
              isAppOutlineModeEx: false,
              isAppOutlineModeExIn: false,
              isAppOutlineModeIn: false,
              runningState: ButtonState.normal,
              btnOffState: ButtonState.normal,
              isDefaultStitch3: false,
              isAppSatinKindTacDown: false,
              isAppSatinKindOff: false,
              isWidthDefault: false,
              isAppSatinKindCoveringStitch: false,
              isDensityDefault: false,
              isDistanceDefault: false,
            ),
            _ref) {
    AppliqueModel()
      ..partsPreviewImage = null
      ..partsPreviewImage = AppliqueModel().partsImage;

    /// View更新
    update();

    /// スイッチの状態監視
    _openTextureSwitchStateListen();
  }

  /// providerのref
  final AutoDisposeStateNotifierProviderRef _ref;

  @override
  void update() {
    bool isUnitMm = PatternModel().isUnitMm;

    state = state.copyWith(
      appliquePreviewImage: AppliqueModel().partsPreviewImage,
      isUnitMm: isUnitMm,
      internalDistanceValue: _getDisplayValue(
          AppliqueModel.internalDistanceInputValue, _widthStepValue, isUnitMm),
      internalDistanceTextStyle: AppliqueModel.internalDistanceInputValue ==
              AppliqueModel.internalDistanceDefaultValue
          ? _blackTextWhiteBackground
          : AppliqueModel.internalDistanceInputValue < 0
              ? _greenTextBlackBackground
              : _whiteTextBlackBackground,
      zigzagWidthValue: _getWidthDisplayValue(
          AppliqueModel.widthMmValue[AppliqueModel.zigzagWidthIndex], isUnitMm),
      zigzagWidthTextStyle:
          AppliqueModel.widthMmValue[AppliqueModel.zigzagWidthIndex] ==
                  AppliqueModel.widthDefaultValue
              ? _blackTextWhiteBackground
              : _whiteTextBlackBackground,
      zigzagDensityValue: _getZigzagDensityValue(isUnitMm),
      zigzagDensityTextStyle: AppliqueModel
                  .stitchDensityMmValue[AppliqueModel.zigzagDensityIndex] ==
              AppliqueModel.zigzagDensityDefaultValue
          ? _blackTextWhiteBackground
          : _whiteTextBlackBackground,
      zigzagDistanceValue: _getDisplayValue(
          AppliqueModel.distanceInputValue, _widthStepValue, isUnitMm),
      zigzagDistanceTextStyle:
          AppliqueModel.distanceInputValue == AppliqueModel.distanceDefaultValue
              ? _blackTextWhiteBackground
              : AppliqueModel.distanceInputValue < 0
                  ? _greenTextBlackBackground
                  : _whiteTextBlackBackground,
      satinDensityValue: _getSatinDensityValue(isUnitMm),
      satinDensityTextStyle:
          AppliqueModel.stitchDensityMmValue[AppliqueModel.satinDensityIndex] ==
                  AppliqueModel.satinDensityDefaultValue
              ? _blackTextWhiteBackground
              : _whiteTextBlackBackground,
      satinWidthValue: _getWidthDisplayValue(
          AppliqueModel.widthMmValue[AppliqueModel.satinWidthIndex], isUnitMm),
      satinWidthTextStyle:
          AppliqueModel.widthMmValue[AppliqueModel.satinWidthIndex] ==
                  AppliqueModel.widthDefaultValue
              ? _blackTextWhiteBackground
              : _whiteTextBlackBackground,
      isOpenPopup: false,
      nextState: AppliqueModel.isNextState,
      isAppOutlineModeEx: _isAppOutlineModeEx(),
      isAppOutlineModeExIn: _isAppOutlineModeExIn(),
      isAppOutlineModeIn: _isAppOutlineModeIn(),
      runningState: _runningState(),
      btnOffState: _btnOffState(),
      isDefaultStitch3: _isDefaultStitch3(),
      isAppSatinKindTacDown: _isAppSatinKindTacDown(),
      isAppSatinKindCoveringStitch: _isAppSatinKindCoveringStitch(),
      isAppSatinKindOff: _isAppSatinKindOff(),
      isWidthDefault: _isWidthDefault(),
      isDensityDefault: _isDensityDefault(),
      isDistanceDefault: _isDistanceDefault(),
    );
  }

  @override
  void onExternalButtonClick() {
    if (state.isOpenPopup ||
        AppliqueModel.appliquePreviewType ==
            AppliqueOutlineMode.appOutlineModeEx) {
      return;
    }

    /// Model 更新
    AppliqueModel.appliquePreviewType = AppliqueOutlineMode.appOutlineModeEx;
    AppliqueModel.isNextState = false;

    /// view更新
    update();
  }

  @override
  void onContoursButtonClick() {
    if (state.isOpenPopup ||
        AppliqueModel.appliquePreviewType ==
            AppliqueOutlineMode.appOutlineModeExIn) {
      return;
    }

    /// Model 更新
    AppliqueModel.appliquePreviewType = AppliqueOutlineMode.appOutlineModeExIn;
    AppliqueModel.isNextState = false;

    /// view更新
    update();
  }

  @override
  void onInternalButtonClick() {
    if (state.isOpenPopup ||
        AppliqueModel.appliquePreviewType ==
            AppliqueOutlineMode.appOutlineModeIn) {
      return;
    }

    /// Model 更新
    AppliqueModel.appliquePreviewType = AppliqueOutlineMode.appOutlineModeIn;
    AppliqueModel.isNextState = false;

    /// view更新
    update();
  }

  @override
  void onExternalZigzagButtonClick() {
    if (AppliqueModel.appliqueExternalStitchType ==
        AppliqueSatinKind.appSatinKindTacDown) {
      return;
    }

    /// Model 更新
    AppliqueModel.appliqueExternalStitchType =
        AppliqueSatinKind.appSatinKindTacDown;
    AppliqueModel.isNextState = false;

    /// view更新
    update();
  }

  @override
  void onExternalSatinButtonClick() {
    if (AppliqueModel.appliqueExternalStitchType ==
        AppliqueSatinKind.appSatinKindCoveringStitch) {
      return;
    }

    /// Model 更新
    AppliqueModel.appliqueExternalStitchType =
        AppliqueSatinKind.appSatinKindCoveringStitch;
    AppliqueModel.isNextState = false;

    /// view更新
    update();
  }

  @override
  void onExternalOFFButtonClick() {
    if (AppliqueModel.appliqueExternalStitchType ==
        AppliqueSatinKind.appSatinKindOff) {
      return;
    }

    /// Model 更新
    AppliqueModel.appliqueExternalStitchType =
        AppliqueSatinKind.appSatinKindOff;
    AppliqueModel.isNextState = false;

    /// view更新
    update();
  }

  @override
  void onInternalRunningButtonClick() {
    if (AppliqueModel.appliqueInternalStitchType ==
        AppliqueRunKind.appRunKindRun) {
      return;
    }

    /// Model 更新
    AppliqueModel.appliqueInternalStitchType = AppliqueRunKind.appRunKindRun;
    AppliqueModel.isNextState = false;

    /// view更新
    update();
  }

  @override
  void onInternalOFFButtonClick() {
    if (AppliqueModel.appliqueInternalStitchType ==
        AppliqueRunKind.appRunKindOff) {
      return;
    }

    /// Model 更新
    AppliqueModel.appliqueInternalStitchType = AppliqueRunKind.appRunKindOff;
    AppliqueModel.isNextState = false;

    /// view更新
    update();
  }

  @override
  void onInternalDistanceButtonClick(BuildContext context) {
    state = state.copyWith(isOpenPopup: true);
    PopupNavigator.pushNamed(
            context: context,
            nextRouteName: PopupEnum.appliquePreviewInternalDistance)
        .then((value) => update());
  }

  @override
  void onExternalContoursDistanceButtonClick(BuildContext context) {
    state = state.copyWith(isOpenPopup: true);
    PopupNavigator.pushNamed(
            context: context,
            nextRouteName: PopupEnum.appliquePreviewExternalContoursDistance)
        .then((value) => update());
  }

  @override
  void onExternalContoursWidthButtonClick(BuildContext context) {
    state = state.copyWith(isOpenPopup: true);
    PopupNavigator.pushNamed(
            context: context,
            nextRouteName: PopupEnum.appliquePreviewExternalContoursWidth)
        .then((value) => update());
  }

  @override
  void onExternalContoursDensityButtonClick(BuildContext context) {
    state = state.copyWith(isOpenPopup: true);
    PopupNavigator.pushNamed(
            context: context,
            nextRouteName: PopupEnum.appliquePreviewExternalContoursDensity)
        .then((value) => update());
  }

  @override
  void onTextureSwitchButtonClick(bool value) {
    if (state.isOpenPopup) {
      return;
    }

    /// Lib更新
    AppliqueModel().setTexture(value);

    /// View更新
    state = state.copyWith(
      textureOnState: value,
      appliquePreviewImage: AppliqueModel().partsPreviewImage,
    );
  }

  @override
  void onPreviewButtonClick(BuildContext context) {
    int widthIndex = 0, densityIndex = 0, distanceValue = 0;

    /// 幅の下付き と　密度スケール
    if (AppliqueModel.appliqueExternalStitchType ==
        AppliqueSatinKind.appSatinKindTacDown) {
      widthIndex = AppliqueModel.zigzagWidthIndex;
      densityIndex = AppliqueModel.zigzagDensityIndex;
    } else {
      widthIndex = AppliqueModel.satinWidthIndex;
      densityIndex = AppliqueModel.satinDensityIndex;
    }

    /// 距離
    if (AppliqueModel.appliquePreviewType ==
        AppliqueOutlineMode.appOutlineModeIn) {
      distanceValue = AppliqueModel.internalDistanceInputValue;
    } else {
      distanceValue = AppliqueModel.distanceInputValue;
    }

    /// Lib更新
    EmbLibraryError error = AppliqueModel().makeAppliqueSelectedParts(
      AppliqueModel.appliquePreviewType,
      AppliqueModel.appliqueExternalStitchType,
      AppliqueModel.appliqueInternalStitchType,
      widthIndex,
      densityIndex,
      distanceValue,
      state.textureOnState,
    );

    switch (error) {
      case EmbLibraryError.EMB_APPLIQUE_NG_MEM_OVER:
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_APPLIQUE_NG_MEM_OVER,
          arguments: AppliqueNgMemOverArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      case EmbLibraryError.EMB_MCD_NOT_EXCHANGE_AREA_OVER:
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_MCD_NOT_EXCHANGE_AREA_OVER,
          arguments: McdNotExchangeAreaOverArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      case EmbLibraryError.EMB_OVERLAP_ERR:
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_APPLIQUE_NG_EX_IN_OVERLAP,
          arguments: AppliqueNgExInOverlapArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      case EmbLibraryError.EMB_DISTANCE_ERR:
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_APPLIQUE_NG_DISTANCE,
          arguments: ErrAppliqueNgDistanceArgument(
            onOKButtonClicked: (_) => GlobalPopupRoute().resetErrorState(),
          ),
        );
        return;
      case EmbLibraryError.EMB_APPLIQUE_NG_COMPLEX:
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_APPLIQUE_NG_COMPLEX,
          arguments: AppliqueNgComplexlapArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      case EmbLibraryError.EMB_RGBCONVERT_ERR:
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      case EmbLibraryError.EMB_NO_ERR:
        AppliqueModel.isNextState = true;
        update();
        return;
      default:
        Log.debugTrace('makeAppliqueSelectedParts Unknown Error! ! !');
        return;
    }
  }

  @override
  void onReturnButtonClick(BuildContext context) {
    if (state.isOpenPopup) {
      return;
    }
    EmbLibrary().apiBinding.cancelPreviewApplique();
    PatternModel().clearTemporaryPatternList();

    AppliqueModel.isNextState = false;
    PopupNavigator.pop(context: context);
  }

  @override
  void onNextButtonClick(BuildContext context) {
    /// 色選択Applique編集モードへ移行
    /// 編集中模様のnotSewingの状態を変更する
    EmbLibraryError embLibraryError = AppliqueModel().editSelectedApplique();
    if (embLibraryError != EmbLibraryError.EMB_NO_ERR) {
      return;
    }
    PopupNavigator.pushNamed(
        context: context, nextRouteName: PopupEnum.appliqueNotSewing);

    /// テクスチャの描画が可能かどうか確認する
    embLibraryError = AppliqueModel().checkTextureDrawing();
    if (embLibraryError == EmbLibraryError.EMB_APPLIQUE_TEXTURE_WARN) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_APPLIQUE_SOME_PARTS_NOT_TEXTURE,
        arguments: AppliqueSomePartsNotTextureArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }
  }

  @override
  void dispose() {
    super.dispose();

    /// データの停止
    _listener.close();
  }

  String _getDisplayValue(int inputValue, int stepValue, bool isUnitMm) {
    double distanceValue = inputValue / stepValue;

    if (isUnitMm) {
      if (stepValue == _densityStepValue) {
        return distanceValue.toStringAsFixed(2);
      }
      return distanceValue.toStringAsFixed(1);
    }
    return EditModel.getDisplayInchShowValue(distanceValue);
  }

  String _getWidthDisplayValue(double inputValue, bool isUnitMm) {
    if (isUnitMm) {
      return inputValue.toStringAsFixed(1);
    }
    return EditModel.getDisplayInchShowValue(inputValue);
  }

  String _getZigzagDensityValue(bool isUnitMm) {
    if (isUnitMm) {
      return AppliqueModel
          .stitchDensityMmValue[AppliqueModel.zigzagDensityIndex]
          .toStringAsFixed(2);
    }
    return AppliqueModel
        .stitchDensityInchValue[AppliqueModel.zigzagDensityIndex]
        .toStringAsFixed(1);
  }

  String _getSatinDensityValue(bool isUnitMm) {
    if (isUnitMm) {
      return AppliqueModel.stitchDensityMmValue[AppliqueModel.satinDensityIndex]
          .toStringAsFixed(2);
    }
    return AppliqueModel.stitchDensityInchValue[AppliqueModel.satinDensityIndex]
        .toStringAsFixed(1);
  }

  bool _isAppOutlineModeEx() {
    if (AppliqueModel.appliquePreviewType ==
        AppliqueOutlineMode.appOutlineModeEx) {
      return true;
    } else {
      return false;
    }
  }

  bool _isAppOutlineModeExIn() {
    if (AppliqueModel.appliquePreviewType ==
        AppliqueOutlineMode.appOutlineModeExIn) {
      return true;
    } else {
      return false;
    }
  }

  bool _isAppOutlineModeIn() {
    if (AppliqueModel.appliquePreviewType ==
        AppliqueOutlineMode.appOutlineModeIn) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// スイッチの状態監視
  ///
  void _openTextureSwitchStateListen() {
    _listener = _ref.stateListen(
        appDisplayEmbStateProvider.select(
            (value) => value.embEditAttrib.ref.subNewWappenTextureState),
        (_, nextState) {
      final textureSwitchState = AppInfoFuncState.getValueByNumber(nextState) ==
          AppInfoFuncState.enable;
      state = state.copyWith(textureSwitchState: textureSwitchState);
    });
  }

  ButtonState _runningState() {
    if (AppliqueModel.appliqueInternalStitchType ==
        AppliqueRunKind.appRunKindRun) {
      return ButtonState.select;
    } else {
      return ButtonState.normal;
    }
  }

  ButtonState _btnOffState() {
    if (AppliqueModel.appliqueInternalStitchType ==
        AppliqueRunKind.appRunKindOff) {
      return ButtonState.select;
    } else {
      return ButtonState.normal;
    }
  }

  bool _isDefaultStitch3() {
    if (AppliqueModel.internalDistanceInputValue ==
        AppliqueModel.internalDistanceDefaultValue) {
      return true;
    } else {
      return false;
    }
  }

  bool _isAppSatinKindTacDown() {
    if (AppliqueModel.appliqueExternalStitchType ==
        AppliqueSatinKind.appSatinKindTacDown) {
      return true;
    } else {
      return false;
    }
  }

  bool _isAppSatinKindCoveringStitch() {
    if (AppliqueModel.appliqueExternalStitchType ==
        AppliqueSatinKind.appSatinKindCoveringStitch) {
      return true;
    } else {
      return false;
    }
  }

  bool _isAppSatinKindOff() {
    if (AppliqueModel.appliqueExternalStitchType ==
        AppliqueSatinKind.appSatinKindOff) {
      return true;
    } else {
      return false;
    }
  }

  bool _isWidthDefault() {
    if (AppliqueModel.appliqueExternalStitchType ==
        AppliqueSatinKind.appSatinKindTacDown) {
      if (AppliqueModel.widthMmValue[AppliqueModel.zigzagWidthIndex] ==
          AppliqueModel.widthDefaultValue) {
        return true;
      } else {
        return false;
      }
    } else if (AppliqueModel.widthMmValue[AppliqueModel.satinWidthIndex] ==
        AppliqueModel.widthDefaultValue) {
      return true;
    } else {
      return false;
    }
  }

  bool _isDensityDefault() {
    if (AppliqueModel.appliqueExternalStitchType ==
        AppliqueSatinKind.appSatinKindTacDown) {
      if (AppliqueModel
              .stitchDensityMmValue[AppliqueModel.zigzagDensityIndex] ==
          AppliqueModel.zigzagDensityDefaultValue) {
        return true;
      } else {
        return false;
      }
    } else if (AppliqueModel
            .stitchDensityMmValue[AppliqueModel.satinDensityIndex] ==
        AppliqueModel.satinDensityDefaultValue) {
      return true;
    } else {
      return false;
    }
  }

  bool _isDistanceDefault() {
    if (AppliqueModel.distanceInputValue ==
        AppliqueModel.distanceDefaultValue) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// データ更新ページをリッスンする
  ///
  late final ProviderSubscription _listener;
}
