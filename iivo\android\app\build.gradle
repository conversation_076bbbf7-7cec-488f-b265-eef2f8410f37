plugins {
  id "com.android.application"
  id "kotlin-android"
  id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
  localPropertiesFile.withReader('UTF-8') { reader ->
    localProperties.load(reader)
  }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
  flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
  flutterVersionName = '1.0'
}

android {

  /// 「panel.apk」と「home.apk」のユーザーに表示されるバージョン情報です
  /// バージョンを調整する場合は、対応する定数を変更してください。
  /// 重要事項：Release時は(dev)タグを削除してください。TODO：自動化したい
  def panelVersionName = "1.8.7#2 (dev_after_bex25)"  /// 「panel.apk」
  def homeVersionName = "1.8.1 (dev)"  /// 「home.apk」

  namespace "com.brother.ph.iivo.iivo"
  compileSdkVersion flutter.compileSdkVersion
  ndkVersion flutter.ndkVersion

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_1_8
    targetCompatibility JavaVersion.VERSION_1_8
  }

  kotlinOptions {
    jvmTarget = '1.8'
  }

  sourceSets {
    main.java.srcDirs += 'src/main/kotlin'
  }

  signingConfigs {
    android_no_google_key {
      storeFile file("key/android_sim_no_google.jks")
      keyAlias "key0"
      keyPassword "123456"
      storePassword "123456"
    }
    machine_key {
      storeFile file("key/platform.jks")
      keyAlias "systemkey"
      keyPassword "123456"
      storePassword "123456"
    }
  }

  buildTypes {
    release {
      signingConfig null
    }

    debug {
      signingConfig null
    }

    profile  {
      signingConfig null
    }
  }

  defaultConfig {

    // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
    applicationId "com.brother.ph.iivo.iivo"
    // You can update the following values to match your application needs.
    // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
    minSdkVersion 29
    //noinspection ExpiredTargetSdkVersion
    targetSdkVersion 29
    versionCode flutterVersionCode.toInteger()
    versionName flutterVersionName

    flavorDimensions "model", "product"
    ndk {
        //noinspection ChromeOsAbiSupport
        // abiFilters を指定すると `flutter build apk --split-per-abi` できなくなるため、abiFilters は指定しないこと
    }
    productFlavors {
      // model
      home {
        dimension = "model"
        applicationId "com.brother.ph.iivo.home"
        manifestPlaceholders = [
          applicationLabel: "home",
          // [home] をランチャーアプリとして登録
          intentCategory1: "android.intent.category.HOME",
          intentCategory2: "android.intent.category.LAUNCHER_APP"
          ]
        versionName homeVersionName
      }

      panel {
        isDefault = true
        dimension = "model"
        applicationId "com.brother.ph.iivo.panel"
        manifestPlaceholders = [
          applicationLabel: "panel",
          // デスクトップに [panel] を表示させます
          // 複数の同一のカテゴリは、Android システムによって 1 つだけとみなされます
          intentCategory1: "android.intent.category.DEFAULT",
          intentCategory2: "android.intent.category.DEFAULT"
          ]
        versionName panelVersionName
      }

      // product
      androidSim {
        dimension = "product"
        manifestPlaceholders = [applicationSharedUserId: ""]
        signingConfig signingConfigs.machine_key
      }
      machine {
        isDefault = true
        dimension = "product"
        manifestPlaceholders = [applicationSharedUserId: "android.uid.system"]

        signingConfig signingConfigs.machine_key
      }
      androidPanelBoard {
        dimension = "product"
        manifestPlaceholders = [applicationSharedUserId: "android.uid.system"]

        signingConfig signingConfigs.machine_key
      }
      androidNoGoogleSim {
        dimension = "product"
        manifestPlaceholders = [applicationSharedUserId: "android.uid.system"]

        signingConfig signingConfigs.android_no_google_key
      }
      /// その後は削除されます
      brother {
        dimension = "model"
        applicationId "com.brother.ph.iivo.brother"
        manifestPlaceholders = [
          applicationLabel: "brother",
          // デスクトップに [panel] を表示させます
          // 複数の同一のカテゴリは、Android システムによって 1 つだけとみなされます
          intentCategory1: "android.intent.category.DEFAULT",
          intentCategory2: "android.intent.category.DEFAULT"
          ]
        versionName panelVersionName
      }

      tacony {
        dimension = "model"
        applicationId "com.brother.ph.iivo.tacony"
        manifestPlaceholders = [
          applicationLabel: "tacony",
          // デスクトップに [panel] を表示させます
          // 複数の同一のカテゴリは、Android システムによって 1 つだけとみなされます
          intentCategory1: "android.intent.category.DEFAULT",
          intentCategory2: "android.intent.category.DEFAULT"
          ]
        versionName panelVersionName
      }

    }
  }
}

flutter {
  source '../..'
}

dependencies {
  compileOnly(project(':app:hidden-api'))
  implementation(project(':app:hidden-api-bridge'))
}

afterEvaluate {
  // apk作成直前にlibflutter.soをカスタマイズ版に置き換える
  // 課題No.525 暗号スイートの無効化にて対応
  // merge***NativeLibsではオリジナルのlibflutter.soが取得されるが、それをカスタム版に置き換える
  // 現在のカスタム版のバージョンは3.19.0をベースにboringsslに不要な暗号スイートを削除した物である
  // libflutter.soのベースバージョンを変える場合などは、変更後ベースバージョンで同様の対応を入れたものを用意する必要がある
  tasks.named("mergePanelMachineReleaseNativeLibs").configure {
    doLast {
      def srcLibFlutter = file("../custom_flutter_engine/prebuild/release/armeabi-v7a/libflutter.so")
      def dstLibFlutter = file("../../build/app/intermediates/merged_native_libs/panelMachineRelease/out/lib/armeabi-v7a/")
      if(srcLibFlutter.exists())
      {
        copy {
          from(srcLibFlutter)
          into(dstLibFlutter)
        }
      }else{
        throw new GradleException("MIssing custom Flutter engine file!")
      }
    }
  }

  tasks.named("mergePanelMachineDebugNativeLibs").configure {
    doLast {
      def srcLibFlutter = file("../custom_flutter_engine/prebuild/debug/armeabi-v7a/libflutter.so")
      def dstLibFlutter = file("../../build/app/intermediates/merged_native_libs/panelMachineDebug/out/lib/armeabi-v7a/")
      if(srcLibFlutter.exists())
      {
        copy {
          from(srcLibFlutter)
          into(dstLibFlutter)
        }
      }else{
        throw new GradleException("MIssing custom Flutter engine file!")
      }
    }
  }

  tasks.named("mergePanelMachineProfileNativeLibs").configure {
    doLast {
      def srcLibFlutter = file("../custom_flutter_engine/prebuild/profile/armeabi-v7a/libflutter.so")
      def dstLibFlutter = file("../../build/app/intermediates/merged_native_libs/panelMachineProfile/out/lib/armeabi-v7a/")
      if(srcLibFlutter.exists())
      {
        copy {
          from(srcLibFlutter)
          into(dstLibFlutter)
        }
      }else{
        throw new GradleException("MIssing custom Flutter engine file!")
      }
    }
  }

}
