import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_emb_frame_set_view_interface.dart';

final errEmbFrameSetViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrEmbFrameSetViewInterface, ErrEmbFrameSetState,
        BuildContext>((ref, context) => ErrEmbFrameSetViewModel(ref, context));

class ErrEmbFrameSetViewModel extends ErrEmbFrameSetViewInterface {
  ErrEmbFrameSetViewModel(Ref ref, BuildContext context)
      : super(const ErrEmbFrameSetState(), ref, context);

  @override
  void onOKButtonClicked() {
    int errcode;
    errcode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERRFRAMESET);
    if (errcode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }
  }
}
