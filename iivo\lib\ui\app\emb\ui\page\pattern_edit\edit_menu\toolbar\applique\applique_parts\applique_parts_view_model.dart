import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../../../model/preview_area_size_model.dart';
import '../../../../../../../model/applique_model.dart';
import '../../../../../../../model/pattern_model.dart';
import '../../../../../../../model/preview_model.dart';
import '../../../../../../../model/redo_undo_model.dart';
import '../../../../pattern_edit_view_model.dart';
import 'applique_parts_interface.dart';

typedef EmbBorderViewDisplayInfo = EmbBorderDisplayInfo;
typedef EmbGroupViewDisplayInfo = EmbGroupDisplayInfo;
typedef EmbPatternViewDisplayInfo = EmbPatternDisplayInfo;

final appliquePartsViewModelProvider = StateNotifierProvider.autoDispose<
    AppliquePartsViewModelInterface, AppliquePartsState>(
  (ref) => AppliquePartsViewModel(ref),
);

class AppliquePartsViewModel extends AppliquePartsViewModelInterface {
  AppliquePartsViewModel(Ref ref)
      : super(
            AppliquePartsState(
              isEnglish: true,
              isNext: false,
              scrollController: ScrollController(),
              displayList: [],
              partsImage: null,
            ),
            ref) {
    /// Model更新
    AppliqueModel().isThreadSelectedList =
        AppliqueModel().getAllColorItemState(false);

    /// ViewModel更新
    _selectedPatternIndex = 0;

    /// View更新
    state = state.copyWith(currentPatternInfo: _getAppliquePartsImage());
    update();
  }

  /// 現在選択されている模様の下付き文字
  int? _selectedPatternIndex = 0;

  @override
  void update() {
    AppliqueModel appliqueModel = AppliqueModel();

    state = state.copyWith(
      partsImage: appliqueModel.partsImage,
      isEnglish: PatternModel().isEnglish,
      isNext: appliqueModel.isColorItemEnable(),
      displayList: appliqueModel.getDisplayList(
        selectedPatternIndex: _selectedPatternIndex,
        zoomScale: zoomList.first,
      ),
    );
  }

  @override
  void onColorItemClicked(int patternIndex, int colorIndex) {
    bool selection =
        !AppliqueModel().isThreadSelectedList[patternIndex][colorIndex];

    /// Lib更新
    var result = AppliqueModel()
        .selectAppliqueParts(patternIndex, colorIndex, selection);

    if (result.errorCode != EmbLibraryError.EMB_NO_ERR) {
      return;
    }

    /// Model更新
    AppliqueModel()
      ..isThreadSelectedList[patternIndex][colorIndex] = selection
      ..partsImage = result.image;

    /// View更新
    update();
  }

  @override
  void onPatternItemClicked(int patternIndex) {
    /// Model更新
    if (_selectedPatternIndex == null ||
        _selectedPatternIndex != patternIndex) {
      _selectedPatternIndex = patternIndex;
    } else {
      _selectedPatternIndex = null;
    }

    /// View更新
    update();

    if (_selectedPatternIndex != null) {
      state.scrollController.jumpTo(_getJumpPositioned(patternIndex));
    } else {
      /// Do Noting
    }
  }

  @override
  void onNextButtonClick(BuildContext context) {
    if (state.isNext == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final EmbLibraryError error =
        EmbLibrary().apiBinding.goAppliqueParameterSetting();
    if (error == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else if (error != EmbLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      Log.errorTrace("goAppliqueParameterSetting : Unknown Error!!!");
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    PopupNavigator.pushNamed(
        context: context, nextRouteName: PopupEnum.appliquePreview);
  }

  @override
  void onSelectAllButtonClick() {
    var result = AppliqueModel().selectAppliquePartsAll(true);
    if (result.errorCode != EmbLibraryError.EMB_NO_ERR) {
      return;
    }
    AppliqueModel().isThreadSelectedList =
        AppliqueModel().getAllColorItemState(true);

    /// View更新
    AppliqueModel().partsImage = result.image;
    update();
  }

  @override
  void onSelectNoneButtonClick() {
    var result = AppliqueModel().selectAppliquePartsAll(false);
    if (result.errorCode != EmbLibraryError.EMB_NO_ERR) {
      return;
    }

    AppliqueModel().isThreadSelectedList =
        AppliqueModel().getAllColorItemState(false);

    /// View更新
    AppliqueModel().partsImage = result.image;
    update();
  }

  @override
  void onReturnButtonClick(BuildContext context) {
    /// Lib通知
    RedoUndoModel().undoRedo();
    PatternModel().reloadAllPattern();

    EmbLibrary().apiBinding.cancelApplique();
    PopupNavigator.pop(context: context);
  }

  ///
  /// 選択パターンの表示情報の取得
  ///
  PatternDisplayInfo? _getAppliquePartsImage() {
    PatternDisplayInfo? patternDisplayInfo;

    /// プレビュー中心点
    final Offset originalCenter = embPreviewSizeDot / 2;

    /// 1mmに対応する画素数
    double pixelOfOneMm = embPreviewSizeDot.dx / frame297x465MmSize.dx;

    /// カレントグループのハンドル
    final currentGroupHandle = PatternModel().getCurrentGroupHandle();

    final Pattern currentPattern = AppliqueModel().getEditPattern.editPattern;

    /// 選択パターンの表示情報の取得
    if (currentPattern is EmbGroup) {
      patternDisplayInfo = PreviewModel().getGroupPatternDisplayInfo(
        scrollType: ScrollCenterType.IMAGE_EDITING,
        group: currentPattern,
        centerPoint: originalCenter,
        pixelOfOneMm: pixelOfOneMm,
        sewingIndex: 0,
        currentGroupHandle: currentGroupHandle,
        zoomScale: zoomList.first,
      );
    } else if (currentPattern is EmbBorder) {
      patternDisplayInfo = PreviewModel().getBorderPatternDisplayInfo(
        scrollType: ScrollCenterType.IMAGE_EDITING,
        border: currentPattern,
        centerPoint: originalCenter,
        pixelOfOneMm: pixelOfOneMm,
        sewingIndex: 0,
        currentGroupHandle: currentGroupHandle,
        zoomScale: zoomList.first,
      );
    } else {
      /// Do noting
    }

    return patternDisplayInfo;
  }

  ///
  /// リスト内でジャンプ可能な座標の計算
  ///
  double _getJumpPositioned(int patternIndex) {
    /// 線色リストのItem高さ
    double listAreaHeight = 880.0;

    /// Itemの間隔
    const double itemSpace = 8;

    /// 線色リストのItemの高さ
    double patternItemHeight = 134.0 + itemSpace;

    /// カラーリストのItemItemの高さ
    double threadItemHeight = 63.0 + itemSpace;

    /// リストの最大高さ
    double maxListHeight = 0.0;

    /// ジャンプ可能な最大座標
    double maxJumpPositioned = 0.0;

    double positioned = patternIndex * patternItemHeight;

    /// 線色リストを展開するかどうか
    if (_selectedPatternIndex != null) {
      /// 展開時にカラーリストの高さを加算
      maxListHeight = patternItemHeight * state.displayList.length +
          threadItemHeight *
              state.displayList[_selectedPatternIndex!].threadInfoDisplayList
                  .length;
    } else {
      maxListHeight = patternItemHeight * state.displayList.length;
    }

    if (maxListHeight < listAreaHeight) {
      /// リストの全長が表示領域を超えていない場合
      maxJumpPositioned = 0;
    } else {
      /// リストの全長が表示領域を超えた場合
      maxJumpPositioned = maxListHeight - listAreaHeight;
    }

    return positioned.clamp(0, maxJumpPositioned);
  }
}
