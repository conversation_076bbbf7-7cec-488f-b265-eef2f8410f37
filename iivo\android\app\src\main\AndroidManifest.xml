<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:sharedUserId="${applicationSharedUserId}"
  android:sharedUserMaxSdkVersion="32"
  tools:targetApi="tiramisu">


  <uses-feature android:name="android.hardware.wifi" />

  <uses-permission
    android:name="android.permission.SET_TIME"
    tools:ignore="ProtectedPermissions" />
  <uses-permission
    android:name="android.permission.SET_TIME_ZONE"
    tools:ignore="ProtectedPermissions" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
  <uses-permission
    android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
    tools:ignore="ProtectedPermissions" />
  <uses-permission
    android:name="android.permission.MOUNT_FORMAT_FILESYSTEMS"
    tools:ignore="ProtectedPermissions" />
  <uses-permission
    android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
    tools:ignore="ScopedStorage" />
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
  <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
  <uses-permission
    android:name="android.permission.CONFIGURE_WIFI_DISPLAY"
    tools:ignore="ProtectedPermissions" />
  <uses-permission
    android:name="android.permission.WRITE_SETTINGS"
    tools:ignore="ProtectedPermissions" />
  <uses-permission
    android:name="android.permission.WRITE_SECURE_SETTINGS"
    tools:ignore="ProtectedPermissions" />
  <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
  <uses-permission
    android:name="android.permission.INTERACT_ACROSS_USERS"
    tools:ignore="ProtectedPermissions" />
  <uses-permission
    android:name="android.permission.BIND_ACCESSIBILITY_SERVICE"
    tools:ignore="ProtectedPermissions" />
  <uses-permission android:name="android.permission.SET_WALLPAPER" />
  <uses-permission android:name="android.permission.WAKE_LOCK" />
  <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
  <uses-permission android:name="android.permission.MONITOR_INPUT" />
  <uses-permission android:name="android.permission.LOCAL_MAC_ADDRESS" />


  <application
    android:name="${applicationName}"
    android:icon="@mipmap/ic_launcher"
    android:label="${applicationLabel}"
    android:requestLegacyExternalStorage="true">

    <activity
      android:name=".MainActivity"
      android:configChanges="colorMode|density|fontScale|fontWeightAdjustment|grammaticalGender|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode"
      android:exported="true"
      android:hardwareAccelerated="true"
      android:launchMode="singleTop"
      android:theme="@style/LaunchTheme"
      android:windowSoftInputMode="adjustPan">

      <intent-filter>
        <action android:name="android.intent.action.MAIN" />

        <category android:name="android.intent.category.LAUNCHER" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="${intentCategory1}" />
        <category android:name="${intentCategory2}" />
      </intent-filter>
      <!-- ADD THIS INTENT FILTER -->
      <intent-filter>
        <action
          android:name="com.android_package_installer.content.SESSION_API_PACKAGE_INSTALLED"
          android:exported="false" />
      </intent-filter>
    </activity>

    <activity
      android:name=".IIVOFlutterActivity"
      android:configChanges="colorMode|density|fontScale|fontWeightAdjustment|grammaticalGender|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode"
      android:exported="true"
      android:hardwareAccelerated="true"
      android:launchMode="singleTop"
      android:theme="@style/LaunchTheme"
      android:windowSoftInputMode="adjustPan">
      <!-- Specifies an Android theme to apply to this Activity as soon as
           the Android process has started. This theme is visible to the user
           while the Flutter UI initializes. After that, this theme continues
           to determine the Window background behind the Flutter UI. -->
      <meta-data
        android:name="io.flutter.embedding.android.NormalTheme"
        android:resource="@style/NormalTheme" />
    </activity>

    <!-- ADD THIS PROVIDER -->
    <provider
      android:name="androidx.core.content.FileProvider"
      android:authorities="${applicationId}"
      android:grantUriPermissions="true">
      <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_paths" />
    </provider>
    <!-- Don't delete the meta-data below.
         This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
    <meta-data
      android:name="flutterEmbedding"
      android:value="2" />
  </application>
</manifest>
