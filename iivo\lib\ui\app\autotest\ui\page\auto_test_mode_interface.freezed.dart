// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auto_test_mode_interface.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AutoTestModeState {
  bool get isPerformingActions => throw _privateConstructorUsedError;
  String? get currentActionDesc => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $AutoTestModeStateCopyWith<AutoTestModeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AutoTestModeStateCopyWith<$Res> {
  factory $AutoTestModeStateCopyWith(
          AutoTestModeState value, $Res Function(AutoTestModeState) then) =
      _$AutoTestModeStateCopyWithImpl<$Res, AutoTestModeState>;
  @useResult
  $Res call({bool isPerformingActions, String? currentActionDesc});
}

/// @nodoc
class _$AutoTestModeStateCopyWithImpl<$Res, $Val extends AutoTestModeState>
    implements $AutoTestModeStateCopyWith<$Res> {
  _$AutoTestModeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isPerformingActions = null,
    Object? currentActionDesc = freezed,
  }) {
    return _then(_value.copyWith(
      isPerformingActions: null == isPerformingActions
          ? _value.isPerformingActions
          : isPerformingActions // ignore: cast_nullable_to_non_nullable
              as bool,
      currentActionDesc: freezed == currentActionDesc
          ? _value.currentActionDesc
          : currentActionDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AutoTestModeStateImplCopyWith<$Res>
    implements $AutoTestModeStateCopyWith<$Res> {
  factory _$$AutoTestModeStateImplCopyWith(_$AutoTestModeStateImpl value,
          $Res Function(_$AutoTestModeStateImpl) then) =
      __$$AutoTestModeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isPerformingActions, String? currentActionDesc});
}

/// @nodoc
class __$$AutoTestModeStateImplCopyWithImpl<$Res>
    extends _$AutoTestModeStateCopyWithImpl<$Res, _$AutoTestModeStateImpl>
    implements _$$AutoTestModeStateImplCopyWith<$Res> {
  __$$AutoTestModeStateImplCopyWithImpl(_$AutoTestModeStateImpl _value,
      $Res Function(_$AutoTestModeStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isPerformingActions = null,
    Object? currentActionDesc = freezed,
  }) {
    return _then(_$AutoTestModeStateImpl(
      isPerformingActions: null == isPerformingActions
          ? _value.isPerformingActions
          : isPerformingActions // ignore: cast_nullable_to_non_nullable
              as bool,
      currentActionDesc: freezed == currentActionDesc
          ? _value.currentActionDesc
          : currentActionDesc // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AutoTestModeStateImpl implements _AutoTestModeState {
  const _$AutoTestModeStateImpl(
      {this.isPerformingActions = false, this.currentActionDesc = null});

  @override
  @JsonKey()
  final bool isPerformingActions;
  @override
  @JsonKey()
  final String? currentActionDesc;

  @override
  String toString() {
    return 'AutoTestModeState(isPerformingActions: $isPerformingActions, currentActionDesc: $currentActionDesc)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AutoTestModeStateImpl &&
            (identical(other.isPerformingActions, isPerformingActions) ||
                other.isPerformingActions == isPerformingActions) &&
            (identical(other.currentActionDesc, currentActionDesc) ||
                other.currentActionDesc == currentActionDesc));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isPerformingActions, currentActionDesc);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AutoTestModeStateImplCopyWith<_$AutoTestModeStateImpl> get copyWith =>
      __$$AutoTestModeStateImplCopyWithImpl<_$AutoTestModeStateImpl>(
          this, _$identity);
}

abstract class _AutoTestModeState implements AutoTestModeState {
  const factory _AutoTestModeState(
      {final bool isPerformingActions,
      final String? currentActionDesc}) = _$AutoTestModeStateImpl;

  @override
  bool get isPerformingActions;
  @override
  String? get currentActionDesc;
  @override
  @JsonKey(ignore: true)
  _$$AutoTestModeStateImplCopyWith<_$AutoTestModeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
