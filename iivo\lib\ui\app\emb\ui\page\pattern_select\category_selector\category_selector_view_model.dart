import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../memory/memory.dart';
import '../../../../../../../model/const_def_model.dart';
import '../../../../../../../model/handel_model.dart';
import '../../../../../../../model/machine_config_model.dart';
import '../../../../../../../model/preview_area_size_model.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../global_popup/global_popups/err_emb_too_much_selected/err_emb_too_much_selected_view_model.dart';
import '../../../../../../global_popup/global_popups/err_filter_cleared/err_filter_cleared_view_model.dart';
import '../../../../../../global_popup/global_popups/err_mdc_resume_ok_b/err_mdc_resume_ok_b_view_model.dart';
import '../../../../../../global_popup/global_popups/err_mdc_resume_ok_t/err_mdc_resume_ok_t_view_model.dart';
import '../../../../../mdc/model/device_info_model.dart';
import '../../../../../mdc/model/paint/library_isolate.dart';
import '../../../../../mdc/model/paint/paint_model.dart';
import '../../../../../mdc/model/resume_history_model.dart';
import '../../../../../mdc/model/stitch/draw_region_model.dart';
import '../../../../model/area_model.dart';
import '../../../../model/large_connected_model.dart';
import '../../../../model/memory_model.dart';
import '../../../../model/pattern_data_reader/category_image_reader.dart';
import '../../../../model/pattern_data_reader/character_pattern_reader.dart';
import '../../../../model/pattern_data_reader/disney_pattern_reader.dart';
import '../../../../model/pattern_data_reader/exclusives_pattern_reader.dart';
import '../../../../model/pattern_data_reader/one_point_pattern_reader.dart';
import '../../../../model/pattern_filter_model.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/photo_stitch/photo_stitch_model.dart';
import '../../../../model/preview_model.dart';
import '../../../../model/quilt_model.dart';
import '../../../../model/select_information_model.dart';
import '../../../../model/select_model.dart';
import '../../../../model/thread_color_list_model.dart';
import '../../../component/emb_header/emb_header_view_model.dart';
import '../../../page_route.dart';
import '../bh_pattern/bh_pattern_selector.dart';
import '../character_pattern/character_pattern_selector.dart';
import '../couching_image_select/couching_image.dart';
import '../disney_pattern/disney_pattern.dart';
import '../exclusives_pattern/exclusives_pattern.dart';
import '../frame_select/frame_select.dart';
import '../large_connect_pattern/large_connect_pattern.dart';
import '../long_stitch_pattern/long_stitch_pattern.dart';
import '../memory_pattern/memory_pattern.dart';
import '../one_point_pattern/one_point_pattern.dart';
import '../quilt_pattern/quilt_pattern.dart';
import 'category_selector_view_interface.dart';
import 'component/filter/filter_navigator.dart';
import 'component/preview/preview_view_model.dart';
import 'component/select_information_popup/select_information_popup.dart';

const _debugLogTag = "CategorySelectorViewModel";

typedef PatternViewDisplayInfo = EmbGroupDisplayInfo;

final categorySelectorViewInfoProvider =
    AutoDisposeNotifierProvider<_CategorySelectorViewInfo, void>(
        () => _CategorySelectorViewInfo());

class _CategorySelectorViewInfo extends AutoDisposeNotifier<void> {
  @override
  void build() {}

  BuildContext? _context;
  BuildContext get context => _context!;
  set context(value) => _context = value;
}

final categorySelectorViewModelProvider = StateNotifierProvider.autoDispose<
    CategorySelectorViewInterface, CategorySelectorState>((ref) {
  final context = ref.read(categorySelectorViewInfoProvider.notifier).context;
  return CategorySelectorViewModel(context, ref);
});

class CategorySelectorViewModel extends CategorySelectorViewInterface {
  CategorySelectorViewModel(BuildContext context, Ref ref)
      : _context = context,
        super(const CategorySelectorState(), ref);

  @override
  void build() {
    super.build();
    Future(() async {
      var categoryType = PagesRoute().getArgument(context: _context);
      if (categoryType != null && categoryType is int) {
        final int categoryIndex =
            CategoryImageReader().getCategoryIndexByCategoryType(categoryType);

        switch (categoryType) {
          case CategoryImageReader.disney:
            onCategoryButtonClicked(categoryIndex);
            break;
          case CategoryImageReader.largeConnect:
            onCategoryButtonClicked(categoryIndex);
            LargeConnectedModel().loadLastSelectedLargeData();
            break;
          case CategoryImageReader.memory:

            /// memoryCategoryに入る
            await _goToMemoryLargeCategory();
            break;
          default:
            break;
        }
      } else {
        /// Do nothing
      }
    });

    update();
  }

  final BuildContext _context;

  /// 表示倍率値リスト
  @override
  List<int> get zoomDisplayList => zoomList;

  ///
  /// ViewModel更新
  ///
  @override
  void update() {
    // TODO:画像展開中に数値表示がないのを待つ
    // http://apngoap306.ap.brothergroup.net:8080/jira/browse/PHBSH-1228
    final AppLocalizations l10n = AppLocalizations.of(_context)!;
    PatternDispInfo patternDispInfo = PatternModel().getPatternInfo(
      isAllPattern: true,
      isNeedTemporaryGroup: true,
      isNeedAllPatternSize: true,
    );

    int frameIndex = AreaModel().getEmbTopBarFrameIndex(patternDispInfo);

    String heightValue = '----';
    String widthValue = '----';
    String totalTimeValue = '----';
    String numberOfColorsValue = '----';
    String colorChangesValue = '----';
    state = state.copyWith(isUnitMm: PatternModel().isUnitMm);

    if ((PatternModel().getAllPattern().isNotEmpty &&
            ThreadColorListModel().isNoThreadToSewing() == false) ||
        PatternModel().temporaryPatternList.isNotEmpty) {
      heightValue = state.isUnitMm
          ? PatternModel()
                  .changeValueToDisplay(patternDispInfo.allPatternHeight) +
              l10n.icon_00225
          : PatternModel()
                  .changeValueToDisplay(patternDispInfo.allPatternHeight) +
              l10n.icon_00226;
      widthValue = state.isUnitMm
          ? PatternModel()
                  .changeValueToDisplay(patternDispInfo.allPatternWidth) +
              l10n.icon_00225
          : PatternModel()
                  .changeValueToDisplay(patternDispInfo.allPatternWidth) +
              l10n.icon_00226;

      totalTimeValue = patternDispInfo.sewingTime
              .clamp(sewingTimeDisplayLimitMin, sewingTimeDisplayLimitMax)
              .toString() +
          l10n.icon_00039;
      numberOfColorsValue = patternDispInfo.colorNum.toString();
      colorChangesValue = patternDispInfo.threadNum.toString();
    } else {
      /// Do noting
    }

    scrollController.dispose();
    scrollController = ScrollController();

    ButtonState realPreviewButtonState =
        PatternModel().getAllPattern().isNotEmpty ||
                PatternModel().temporaryPatternList.isNotEmpty
            ? ButtonState.normal
            : ButtonState.disable;
    ButtonState formationButtonState =
        PatternModel().temporaryPatternList.isNotEmpty
            ? ButtonState.normal
            : ButtonState.disable;

    /// View更新
    state = state.copyWith(
      frameIndex: frameIndex,
      heightValue: heightValue,
      widthValue: widthValue,
      totalTimeValue: totalTimeValue,
      numberOfColorsValue: numberOfColorsValue,
      colorChangesValue: colorChangesValue,
      patternDisplayInfoList: _getPatternDisplayInfoList(),
      temporaryGroupDisplayInfoList: _getTemporaryGroupDisplayInfoList(),
      selectedZoomScale: PatternModel().selectedZoomScaleInSelectPage,
      showFilterButton: PatternFilter().filterStatus != FilterType.hide,
      isFilterApplied: PatternFilter().filterStatus == FilterType.filtered,
      formationButtonState: formationButtonState,
      realPreviewButtonState: realPreviewButtonState,
      isUnitMm: PatternModel().isUnitMm,
      quiltImage: PreviewModel().quiltImage ?? Container(),
    );
  }

  ///
  /// 虫眼鏡による拡大画像iconList
  ///
  final List<Widget> _zoomIconList = [
    const ico_zoom100(),
    const ico_zoom125(),
    const ico_zoom150(),
    const ico_zoom200(),
    const ico_zoom400(),
  ];

  ///
  /// TopPage画面のView更新
  ///
  @override
  void updateTopPageByChild(CategorySelectorModuleType dt) {
    switch (dt) {
      case CategorySelectorModuleType.memorySelectorSet:
        ref.read(embHeaderViewModelProvider.notifier).update();
        break;
      case CategorySelectorModuleType.patternSelector:
        update();
        ref.read(previewViewModelProvider.notifier).update();
        break;
      case CategorySelectorModuleType.filter:
        update();
        break;
      default:
        update();
        break;
    }
  }

  ///
  /// 全てのカテゴリのイコンデータを取得する
  ///
  /// ##@return
  /// - List<CategoryImageGroup>: 順番保存されているの模様イコンデータ
  ///
  @override
  List<CategoryImageGroup> getAllCategoryImagesInfo() =>
      CategoryImageReader().getAllCategoryImagesInfo();

  ///
  /// 倍率選択ポップアップを開くためのクリック関数
  ///
  @override
  void onZoomButtonClicked() {
    state = state.copyWith(showZoomList: true);
  }

  ///
  /// 詳細ポップアップを開くためのクリック関数
  ///
  @override
  void onInformationButtonClicked(BuildContext context) {
    PopupNavigator.pushNamed(
        context: context,
        nextRouteName: CategorySelectorPageSubPopupEnum.information);
  }

  ///
  /// リアルプレビューポップアップを開くためのクリック関数
  ///
  @override
  void onRealPreviewButtonClicked() {
    /// Model更新
    PatternModel().realPreviewDisplayType =
        RealPreviewDisplayType.patternSelect;

    /// View更新
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.embRealPreview);
  }

  ///
  /// カテゴリメモリクリック機能
  ///
  @override
  void onMemoryCategoryButtonClicked() {
    /// カテゴリがログインできるか確認してください
    final error = EmbLibrary()
        .apiBinding
        .checkEmbLargeCategorySelection(EmbLargeCategory.EMB_LARGE_CAT_MEMORY);
    if (error == EmbLibraryError.EMB_INVALID_ERR_PANEL ||
        error != EmbLibraryError.EMB_NO_ERR) {
      return;
    }

    SelectModel().isLargeConnectSet = false;
    PatternFilter().setFilterHide();
    _updateFilterStatus();

    /// 待機ポップアップウィンドウを開く
    GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_PLEASE_WAIT,
        arguments: {GlobalPopupRoute.isStopSystemSound: true});

    MemoryModel().loadFileInMachineList().then((accessError) {
      if (accessError != AccessError.none) {
        HandelModel.handleMemoryAccessError(accessError);
        return;
      }

      /// 待機中のポップアップウィンドウを閉じます
      GlobalPopupRoute().resetErrorState();

      /// memoryポープアープを開ける
      _pushSubSelectPopup(
        nextRouteName: CategorySelectorPageSubPopupEnum.memory,
        onSubSelectPopupClose: (value) {
          state = state.copyWith(
            showInformationButton: false,
            isEnterPattern: false,
            showZoomList: false,
          );
          update();
        },
      );

      /// View更新
      state = state.copyWith(
        showInformationButton: true,
        showZoomButton: true,
        showZoomList: false,
        selectedCategoryName: SelectModel.memoryPatternIndexGroupName,
      );
    });
  }

  ///
  /// 大型分割の通常パターンを縫製完了後、memoryにジャンプする
  ///
  Future<void> _goToMemoryLargeCategory() async {
    /// 待機ポップアップウィンドウを開く
    GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_PLEASE_WAIT,
        arguments: {GlobalPopupRoute.isStopSystemSound: true});

    PatternFilter().setFilterHide();
    _updateFilterStatus();

    /// View更新
    state = state.copyWith(
      showInformationButton: true,
      showZoomButton: true,
      showZoomList: false,
      selectedCategoryName: SelectModel.memoryPatternIndexGroupName,
    );

    await MemoryModel().loadLastSelectedLargeData();

    /// memoryポープアープを開ける
    _pushSubSelectPopup(
      nextRouteName: CategorySelectorPageSubPopupEnum.memory,
      onSubSelectPopupClose: (value) {
        state = state.copyWith(
          showInformationButton: false,
          isEnterPattern: false,
          showZoomList: false,
        );
        update();
      },
    );

    GlobalPopupRoute().resetErrorState();
  }

  ///
  /// 大カテゴリのクリック関数
  ///
  @override
  void onCategoryButtonClicked(int categoryIndex) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    final int categoryType = CategoryImageReader()
        .getCategoryPatternTypeByCategoryIndex(categoryIndex);
    final String categoryName =
        getAllCategoryImagesInfo()[categoryIndex].groupName;

    if (PatternFilter().filterStatus == FilterType.filtered &&
        _isNotApplicableCategoryType(categoryType)) {
      _openFilterClearedPopup(categoryName, categoryType);
    } else {
      _subCategoryEntry(categoryName, categoryType);
    }
  }

  ///
  /// 選択したの模様categoryを各処理を進め
  ///
  void _subCategoryEntry(String categoryName, int categoryType) {
    state = state.copyWith(isQuiltPattern: false);
    scrollController = ScrollController();

    /// カテゴリー
    EmbLargeCategory embLargeCategory = EmbLargeCategory.EMB_LARGE_CAT_OTHER;
    switch (categoryType) {
      case CategoryImageReader.quilt:
        embLargeCategory = EmbLargeCategory.EMB_LARGE_CAT_CREATE_QUILT;
        break;
      case CategoryImageReader.couching:
        embLargeCategory = EmbLargeCategory.EMB_LARGE_CAT_COUTING;
        break;
      case CategoryImageReader.longStitch:
        embLargeCategory = EmbLargeCategory.EMB_LARGE_CAT_LONGSTITCH;
        break;
      case CategoryImageReader.largeConnect:
        embLargeCategory = EmbLargeCategory.EMB_LARGE_CAT_CUSTOMDESIGN;
        break;
      default:
        embLargeCategory = EmbLargeCategory.EMB_LARGE_CAT_OTHER;
        break;
    }

    /// カテゴリがログインできるか確認してください
    final error = EmbLibrary()
        .apiBinding
        .checkEmbLargeCategorySelection(embLargeCategory);
    if (error == EmbLibraryError.EMB_INVALID_ERR_PANEL ||
        error != EmbLibraryError.EMB_NO_ERR) {
      return;
    }

    SelectModel().isLargeConnectSet = false;

    /// 文字模様
    if (categoryType == CategoryImageReader.character) {
      PatternFilter().setFilterAvailable();

      _updateFilterStatus();
      PatternFilter().patternData =
          CharacterPatternImageReader().getAllCharacterPatternsInfo();

      /// model更新
      SelectInformationModel()
          .setTemporaryPatternAutoKind(CategoryType.character);
      if (PatternFilter().filterStatus == FilterType.filtered) {
        PagesRoute().pushReplacement(
            nextRoute: PageRouteEnum.patternSelectFilterResult);
      } else {
        _pushSubSelectPopup(
            nextRouteName: CategorySelectorPageSubPopupEnum.characterPattern,
            onSubSelectPopupClose: (value) {
              state = state.copyWith(
                showInformationButton: false,
                showZoomButton: true,
                isEnterPattern: false,
                showZoomList: false,
              );
              update();
            });
      }
    }

    /// 文字フォント
    else if (categoryType == CategoryImageReader.characterFont) {
      PagesRoute()
          .pushNamed(nextRoute: PageRouteEnum.embCharacterFontCategoryPage)
          .then((_) {
        state = state.copyWith(
          showInformationButton: false,
          showZoomButton: true,
          isEnterPattern: false,
          showZoomList: false,
        );
        update();
      });
    }

    /// 枠模様
    else if (categoryType == CategoryImageReader.frame) {
      PatternFilter().setFilterHide();
      _updateFilterStatus();

      /// model更新
      SelectInformationModel().setTemporaryPatternAutoKind(CategoryType.frame);
      _pushSubSelectPopup(
          nextRouteName: CategorySelectorPageSubPopupEnum.frame,
          onSubSelectPopupClose: (value) {
            state = state.copyWith(
              showInformationButton: false,
              isEnterPattern: false,
              showZoomList: false,
            );
            update();
          });
    }

    /// BH
    else if (categoryType == CategoryImageReader.bh) {
      PatternFilter().setFilterHide();
      _updateFilterStatus();

      /// model更新
      SelectInformationModel().setTemporaryPatternAutoKind(CategoryType.bh);
      _pushSubSelectPopup(
          nextRouteName: CategorySelectorPageSubPopupEnum.bhPattern,
          onSubSelectPopupClose: (value) {
            state = state.copyWith(
              showInformationButton: false,
              isEnterPattern: false,
              showZoomList: false,
            );
            update();
          });
    }

    /// one point模様
    else if (categoryType == CategoryImageReader.onePoint) {
      PatternFilter().setFilterAvailable();
      _updateFilterStatus();
      PatternFilter().patternData =
          OnePointPatternReader().getAllOnePointPatternInfo();

      /// model更新
      SelectInformationModel()
          .setTemporaryPatternAutoKind(CategoryType.onePoint);
      if (PatternFilter().filterStatus == FilterType.filtered) {
        PagesRoute().pushReplacement(
            nextRoute: PageRouteEnum.patternSelectFilterResult);
      } else {
        _pushSubSelectPopup(
            nextRouteName: CategorySelectorPageSubPopupEnum.onePointPattern,
            onSubSelectPopupClose: (_) {
              state = state.copyWith(
                showInformationButton: false,
                showZoomButton: true,
                isEnterPattern: false,
                showZoomList: false,
              );
              update();
            });
      }
    }

    /// Disney模様
    else if (categoryType == CategoryImageReader.disney) {
      PatternFilter().setFilterAvailable();

      _updateFilterStatus();
      PatternFilter().patternData =
          DisneyPatternReader().getAllDisneyPatternInfo();

      /// model更新
      SelectInformationModel().setTemporaryPatternAutoKind(CategoryType.disney);

      if (PatternFilter().filterStatus == FilterType.filtered) {
        PagesRoute().pushReplacement(
            nextRoute: PageRouteEnum.patternSelectFilterResult);
      } else {
        _pushSubSelectPopup(
            nextRouteName: CategorySelectorPageSubPopupEnum.disneyPattern,
            onSubSelectPopupClose: (value) {
              state = state.copyWith(
                showInformationButton: false,
                showZoomButton: true,
                isEnterPattern: false,
                showZoomList: false,
              );
              update();
            });
      }
    }

    /// Quilt
    else if (categoryType == CategoryImageReader.quilt) {
      state = state.copyWith(isQuiltPattern: true);
      PatternFilter().setFilterHide();
      _updateFilterStatus();
      _pushSubSelectPopup(
          nextRouteName: CategorySelectorPageSubPopupEnum.quiltPattern,
          onSubSelectPopupClose: (value) {
            state = state.copyWith(
              showInformationButton: false,
              showZoomButton: true,
              isEnterPattern: false,
              showZoomList: false,
            );

            if (QuiltImageModel().isQuiltPushToMemory == true) {
              state = state.copyWith(showZoomList: false);
              _memoryCategoryButtonClicked();
              QuiltImageModel().isQuiltPushToMemory = false;
            }
            state = state.copyWith(isQuiltPattern: false);
            update();
          });
    }

    /// taconyのExclusives模様
    else if (categoryType == CategoryImageReader.exclusives) {
      PatternFilter().setFilterAvailable();
      _updateFilterStatus();

      PatternFilter().patternData =
          ExclusivesPatternReader().getAllExclusivesPatternsInfo();

      /// model更新
      SelectInformationModel()
          .setTemporaryPatternAutoKind(CategoryType.exclusives);
      if (PatternFilter().filterStatus == FilterType.filtered) {
        PagesRoute().pushReplacement(
            nextRoute: PageRouteEnum.patternSelectFilterResult);
      } else {
        _pushSubSelectPopup(
            nextRouteName: CategorySelectorPageSubPopupEnum.exclusives,
            onSubSelectPopupClose: (value) {
              state = state.copyWith(
                showInformationButton: false,
                isEnterPattern: false,
                showZoomList: false,
              );
              update();
            });
      }
    }

    /// カウチング模様
    else if (categoryType == CategoryImageReader.couching) {
      PatternFilter().setFilterHide();
      _updateFilterStatus();

      /// model更新
      SelectInformationModel()
          .setTemporaryPatternAutoKind(CategoryType.couching);
      _pushSubSelectPopup(
          nextRouteName: CategorySelectorPageSubPopupEnum.couching,
          onSubSelectPopupClose: (value) {
            state = state.copyWith(
              showInformationButton: false,
              isEnterPattern: false,
              showZoomList: false,
            );
            update();
          });
    }

    /// ロングステッチ模様
    else if (categoryType == CategoryImageReader.longStitch) {
      PatternFilter().setFilterHide();
      _updateFilterStatus();

      /// model更新
      SelectInformationModel()
          .setTemporaryPatternAutoKind(CategoryType.longStitch);
      _pushSubSelectPopup(
          nextRouteName: CategorySelectorPageSubPopupEnum.longStitch,
          onSubSelectPopupClose: (value) {
            state = state.copyWith(
              showInformationButton: false,
              isEnterPattern: false,
              showZoomList: false,
            );
            update();
          });
    }

    /// largeConnect
    else if (categoryType == CategoryImageReader.largeConnect) {
      PatternFilter().setFilterHide();
      _updateFilterStatus();

      /// 待機ポップアップウィンドウを開く
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_PLEASE_WAIT,
          arguments: {GlobalPopupRoute.isStopSystemSound: true});

      LargeConnectedModel().loadFile().then((accessError) {
        if (accessError != AccessError.none) {
          HandelModel.handleMemoryAccessError(accessError);
          return;
        }

        /// 待機中のポップアップウィンドウを閉じます
        GlobalPopupRoute().resetErrorState();

        /// model更新
        SelectInformationModel()
            .setTemporaryPatternAutoKind(CategoryType.largeConnect);

        _pushSubSelectPopup(
            nextRouteName: CategorySelectorPageSubPopupEnum.largeConnect,
            onSubSelectPopupClose: (value) {
              state = state.copyWith(
                showInformationButton: false,
                isEnterPattern: false,
                showZoomList: false,
              );
              update();
            });
      });
    }

    /// memory
    else {
      Log.e(tag: _debugLogTag, description: "unknown category");
    }

    /// View更新
    state = state.copyWith(
      showInformationButton:
          categoryType == CategoryImageReader.quilt ? false : true,
      showZoomButton: categoryType == CategoryImageReader.quilt ? false : true,
      selectedCategoryName: categoryName,
      showZoomList: false,
    );
  }

  ///
  /// sub選択popup画面遷移
  ///
  /// Libの要求：sub選択popup画面遷移前にMSG_Keyを送信します。
  ///
  void _pushSubSelectPopup<T>({
    required CategorySelectorPageSubPopupEnum nextRouteName,
    void Function(Object? value)? onSubSelectPopupClose,
  }) {
    if (!_context.mounted) {
      Log.e(
          tag: _debugLogTag,
          description: "pushSubSelectPopup | page is unmounted");
      return;
    }

    PopupNavigator.pushNamed(context: _context, nextRouteName: nextRouteName)
        .then((value) {
      if (!_context.mounted) {
        Log.e(
            tag: _debugLogTag,
            description: "pushSubSelectPopup | page is unmounted");
        return;
      }

      onSubSelectPopupClose?.call(value);
      return;
    });
  }

  ///
  /// 対象外カテゴリーキーを押したときは
  /// brother対象カテゴリ : [1,3,D => onePoint,character,disney]
  /// tacony対象カテゴリ : [1,2,4 => exclusives,onePoint,character]
  ///
  bool _isNotApplicableCategoryType(int categoryType) {
    if (categoryType == CategoryImageReader.onePoint ||
        categoryType == CategoryImageReader.character ||
        categoryType == CategoryImageReader.disney ||
        categoryType == CategoryImageReader.exclusives) {
      return false;
    } else {
      return true;
    }
  }

  ///
  /// カテゴリのフィルタリングを解除するかどうかのエラープロンプトをオンにします
  ///
  void _openFilterClearedPopup(String categoryName, int categoryType) {
    GlobalPopupRoute().updateErrorState(
      nextRoute: GlobalPopupRouteEnum.errFilterCleared,
      arguments: FilterClearedArgument(
        onOKButtonClicked: (_) {
          PatternFilter().setFilterHide();
          GlobalPopupRoute().resetErrorState();
          update();
          _subCategoryEntry(categoryName, categoryType);
        },
        onCancelButtonClicked: (_) => GlobalPopupRoute().resetErrorState(),
      ),
    );
  }

  ///
  /// 名前付きルートの登録
  ///
  @override
  Map<String, PopupRouteBuilder> registerNamedPopup() {
    state = state.copyWith(
      isEnterPattern: true,
    );
    return {
      CategorySelectorPageSubPopupEnum.exclusives.toString(): PopupRouteBuilder(
        builder: (_) => ExclusivesPatternSelector(
          categoryName: state.selectedCategoryName,
        ),
        barrier: false,
        needMultipleClick: true,
      ),
      CategorySelectorPageSubPopupEnum.characterPattern.toString():
          PopupRouteBuilder(
        builder: (_) => CharacterPatternSelector(
          categoryName: state.selectedCategoryName,
        ),
        barrier: false,
        needMultipleClick: true,
      ),
      CategorySelectorPageSubPopupEnum.bhPattern.toString(): PopupRouteBuilder(
        builder: (_) => BhPatternSelector(
          categoryName: state.selectedCategoryName,
        ),
        barrier: false,
        needMultipleClick: true,
      ),
      CategorySelectorPageSubPopupEnum.onePointPattern.toString():
          PopupRouteBuilder(
        builder: (_) => OnePointPatternSelector(
          categoryName: state.selectedCategoryName,
        ),
        barrier: false,
        needMultipleClick: true,
      ),
      CategorySelectorPageSubPopupEnum.disneyPattern.toString():
          PopupRouteBuilder(
        builder: (_) => DisneyPatternSelector(
          categoryName: state.selectedCategoryName,
        ),
        barrier: false,
        needMultipleClick: true,
      ),
      CategorySelectorPageSubPopupEnum.longStitch.toString(): PopupRouteBuilder(
        builder: (_) => LongStitchPatternSelector(
          categoryName: state.selectedCategoryName,
        ),
        barrier: false,
        needMultipleClick: true,
      ),
      CategorySelectorPageSubPopupEnum.information.toString():
          PopupRouteBuilder(
        builder: (_) => const SelectInformationPopup(),
        barrier: false,
      ),
      CategorySelectorPageSubPopupEnum.quiltPattern.toString():
          PopupRouteBuilder(
        builder: (_) => QuiltPattern(
          categoryName: state.selectedCategoryName,
        ),
        barrier: false,
      ),
      CategorySelectorPageSubPopupEnum.couching.toString(): PopupRouteBuilder(
        builder: (_) => CouchingImage(
          categoryName: state.selectedCategoryName,
        ),
        barrier: false,
        needMultipleClick: true,
      ),
      CategorySelectorPageSubPopupEnum.largeConnect.toString():
          PopupRouteBuilder(
        builder: (_) => LargeConnectPatternSelector(
          categoryName: state.selectedCategoryName,
        ),
        barrier: false,
      ),
      CategorySelectorPageSubPopupEnum.memory.toString(): PopupRouteBuilder(
        builder: (_) => const MemoryPattern(),
        barrier: false,
        needMultipleClick: true,
      ),
      CategorySelectorPageSubPopupEnum.frame.toString(): PopupRouteBuilder(
        builder: (_) => FrameSelect(
          categoryName: state.selectedCategoryName,
        ),
        barrier: false,
      ),
    };
  }

  @override
  void onFilterButtonClicked(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        barrier: false,
        builder: (_) => const FilterNavigator(),
      ),
    ).then((value) {
      if (!mounted) {
        return;
      }
      update();
    });
  }

  @override
  void onFilterCloseButtonClicked() {
    PatternFilter().resetFilterSetting();
    update();
  }

  ///
  /// Viewは最初にPattern情報変換を使用する
  ///
  List<PatternDisplayInfo> _getPatternDisplayInfoList() {
    List<PatternDisplayInfo> patternDisplayInfoList = [];

    /// プレビュー中心点
    final Offset originalCenter = selectPreviewSize / 2;

    /// 1mmに対応する画素数
    double pixelOfOneMm = selectPreviewSize.dx / frame297x465MmSize.dx;

    if (PatternModel().getAllPattern().isNotEmpty) {
      /// カレントグループのハンドル
      final currentGroupHandle = PatternModel().getCurrentGroupHandle();

      PatternModel().getAllPattern().asMap().forEach((sewingIndex, pattern) {
        if (pattern is EmbGroup) {
          patternDisplayInfoList.add(
            PreviewModel().getGroupPatternDisplayInfo(
              scrollType: ScrollCenterType.IMAGE_SELECT,
              group: pattern,
              centerPoint: originalCenter,
              pixelOfOneMm: pixelOfOneMm,
              sewingIndex: sewingIndex,
              currentGroupHandle: currentGroupHandle,
              zoomScale: PatternModel().selectedZoomScaleInSelectPage,
            ),
          );
        } else if (pattern is EmbBorder) {
          patternDisplayInfoList.add(
            PreviewModel().getBorderPatternDisplayInfo(
              scrollType: ScrollCenterType.IMAGE_SELECT,
              border: pattern,
              centerPoint: originalCenter,
              pixelOfOneMm: pixelOfOneMm,
              sewingIndex: sewingIndex,
              currentGroupHandle: currentGroupHandle,
              zoomScale: PatternModel().selectedZoomScaleInSelectPage,
            ),
          );
        } else {
          /// Do noting
        }
      });
    }

    return patternDisplayInfoList;
  }

  ///
  /// Viewは最初にPattern情報変換を使用する
  ///
  List<PatternDisplayInfo> _getTemporaryGroupDisplayInfoList() {
    if (PatternModel().temporaryPatternList.isEmpty) {
      return [];
    }

    List<PatternDisplayInfo> temporaryGroupDisplayInfoList = [];

    /// プレビュー中心点
    final Offset originalCenter = selectPreviewSize / 2;

    /// 1mmに対応する画素数
    double pixelOfOneMm = selectPreviewSize.dx / frame297x465MmSize.dx;

    /// カレントグループのハンドル
    final currentGroupHandle = PatternModel().getCurrentGroupHandle();

    PatternModel().temporaryPatternList.asMap().forEach((sewingIndex, pattern) {
      if (pattern is EmbGroup) {
        temporaryGroupDisplayInfoList.add(
          PreviewModel().getGroupPatternDisplayInfo(
            scrollType: ScrollCenterType.IMAGE_SELECT,
            group: pattern,
            centerPoint: originalCenter,
            pixelOfOneMm: pixelOfOneMm,
            sewingIndex: 0,
            currentGroupHandle: currentGroupHandle,
            zoomScale: PatternModel().selectedZoomScaleInSelectPage,
          ),
        );
      } else if (pattern is EmbBorder) {
        temporaryGroupDisplayInfoList.add(
          PreviewModel().getBorderPatternDisplayInfo(
            scrollType: ScrollCenterType.IMAGE_SELECT,
            border: pattern,
            centerPoint: originalCenter,
            pixelOfOneMm: pixelOfOneMm,
            sewingIndex: 0,
            currentGroupHandle: currentGroupHandle,
            zoomScale: PatternModel().selectedZoomScaleInSelectPage,
          ),
        );
      } else {
        /// Do noting
      }
    });

    return temporaryGroupDisplayInfoList;
  }

  @override
  void onPicturePlayButtonClicked() {
    final error = EmbLibrary()
        .apiBinding
        .checkEmbLargeCategorySelection(EmbLargeCategory.EMB_LARGE_CAT_PHOTO);

    if (error == EmbLibraryError.EMB_INVALID_ERR_PANEL ||
        error != EmbLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    state = state.copyWith(showZoomList: false);
    bool isSuccess = PhotoStitchModel().prepareForPhotoStitch();
    if (isSuccess) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      PagesRoute().pushNamed(nextRoute: PageRouteEnum.photoStitchPopup);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }
  }

  ///
  /// MDCに移動
  ///
  @override
  void onMDCButtonClicked() {
    /// Emb Select→MDC Paintへの遷移
    var errCode = EmbLibrary().apiBinding.embGotoMDCPaint();
    if (errCode != EmbLibraryError.EMB_NO_ERR) {
      return;
    }

    /// Libにエラー状態のチェック
    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    /// アプリ側画面遷移
    var errorCode = MdcLibrary().apiBinding.openMdcMode();
    if (errorCode == MdcLibraryError.mdcEditActiveOK) {
      EmbLibrary().apiBinding.closeEmbModeMdc();
      MachineConfigModel().machineBaseModel = MachineBaseModeEnum.embroidery;
      state = state.copyWith(showZoomList: false);

      /// 設定の情報初期化
      DeviceInfoModel().updateMdcCommonDeviceInfo();

      PaintModel().toDefaultProperty();

      /// Isolate開始
      LibraryIsolate().initLibIsolate();

      if (ResumeHistoryModel().isEntryResume()) {
        _openMdcResumePopup();
      } else {
        _openPaintPage();
      }
    } else {
      /// データ容量の制限を越えました、選べません(刺繍メモリフル)
      _openTooMuchSelectedPopup();
    }
  }

  ///
  /// 倍率値リストを閉じる
  ///
  @override
  void closeZoomPopup() {
    state = state.copyWith(
        showZoomList: false,
        selectedZoomScale: PatternModel().selectedZoomScaleInSelectPage);
  }

  ///
  /// 倍率値変更
  ///
  @override
  void onZoomPopupListClick(int zoomValue) {
    /// Model更新
    PatternModel().selectedZoomScaleInSelectPage = zoomValue;

    /// view更新
    state = state.copyWith(
        showZoomList: false,
        selectedZoomScale: PatternModel().selectedZoomScaleInSelectPage);

    /// 他のページへの更新の通知
    updateTopPageByChild(CategorySelectorModuleType.patternSelector);
  }

  @override
  void onReturnButtonClicked() {
    final error = EmbLibrary().apiBinding.embSelectReturnEdit();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return;
    }
    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    PagesRoute().pushNamedAndRemoveUntil(
        nextRoute: PageRouteEnum.patternEdit, untilRoute: PageRouteEnum.home);
  }

  @override
  bool isAddModel() => SelectModel().isAddModel;

  ///
  /// ミリかどうか
  ///
  bool get isUnitMm =>
      DeviceLibrary().apiBinding.getDisplayUnit().displayUnit == DisplayUnit.mm;

  ///
  /// EmbUnit移除
  ///
  @override
  void onEmbThreadCuttingButtonClicked() {
    var errorCode = EmbLibrary().apiBinding.removeEmbUnit();
    if (errorCode != EmbLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }
  }

  @override
  void onFavoriteButtonClicked() {
    // TODO: implement onFavoriteButtonClicked
  }

  @override
  String getCategoryTips(int index) {
    // TODO: https://brothergroup.atlassian.net/browse/PHBSH-3258 tooltips在json实现
    final AppLocalizations l10n = AppLocalizations.of(_context)!;
    return MachineConfigModel().isBrother
        ? index == 0
            ? l10n.tt_embcate_embpatterns
            : index == 1
                ? l10n.tt_embcate_character
                : index == 2
                    ? l10n.tt_embcate_decoalphabet
                    : index == 3
                        ? l10n.tt_embcate_frame
                        : index == 4
                            ? l10n.tt_embcate_utility
                            : index == 5
                                ? l10n.tt_embcate_split
                                : index == 6
                                    ? l10n.tt_embcate_long_stitch
                                    : index == 7
                                        ? l10n.tt_embcate_quilt
                                        : index == 8
                                            ? l10n.tt_embcate_b_disney
                                            : index == 9
                                                ? l10n.tt_embcate_couching
                                                : index == 10
                                                    ? l10n
                                                        .tt_embcate_t_exclusives
                                                    : ""
        : index == 0
            ? l10n.tt_embcate_t_exclusives
            : index == 1
                ? l10n.tt_embcate_embpatterns
                : index == 2
                    ? l10n.tt_embcate_character
                    : index == 3
                        ? l10n.tt_embcate_decoalphabet
                        : index == 4
                            ? l10n.tt_embcate_frame
                            : index == 5
                                ? l10n.tt_embcate_utility
                                : index == 6
                                    ? l10n.tt_embcate_split
                                    : index == 7
                                        ? l10n.tt_embcate_long_stitch
                                        : index == 8
                                            ? l10n.tt_embcate_quilt
                                            : index == 9
                                                ? l10n.tt_embcate_couching
                                                : "";
  }

  ///
  /// 選択されているものを取得します index
  ///
  @override
  Widget getSelectedZoomIcon() {
    for (int i = 0; i < zoomList.length; i++) {
      if (state.selectedZoomScale == zoomList[i]) {
        return _zoomIconList[i];
      }
    }
    return Container();
  }

  ///
  ///BrotherまたはTaconyのデバイスを入手する
  ///
  @override
  bool isBrother() => MachineConfigModel().isBrother;

  ///
  /// 空格を含む文字列から、最初の空格の前の文字列を取得する。
  ///
  /// - Parameter input: 入力文字列
  /// - Returns: 最初の空格前の文字列。空格が存在しない場合は入力文字列全体。
  @override
  String getSubstringBeforeSpace(int index) {
    String input = getAllCategoryImagesInfo()[index].groupName;
    int spaceIndex = getAllCategoryImagesInfo()[index].groupName.indexOf(' ');

    /// 空格のインデックスを検索

    if (spaceIndex != -1) {
      /// 空格が見つかった場合
      return input.substring(0, spaceIndex);

      /// 空格前の文字列を返却
    } else {
      return input;

      /// 空格がない場合は入力文字列全体を返却
    }
  }

  ///
  /// カテゴリメモリクリック機能
  ///
  void _memoryCategoryButtonClicked() {
    /// memoryポープアープを開ける
    _pushSubSelectPopup(
      nextRouteName: CategorySelectorPageSubPopupEnum.memory,
      onSubSelectPopupClose: (value) {
        state = state.copyWith(
          showInformationButton: false,
          isEnterPattern: false,
          showZoomList: false,
        );
        update();
      },
    );

    /// View更新
    state = state.copyWith(
      showInformationButton: true,
      showZoomButton: true,
      showZoomList: false,
      selectedCategoryName: SelectModel.memoryPatternIndexGroupName,
    );
  }

  ///
  /// フィルターの状態を更新する
  ///
  void _updateFilterStatus() {
    state = state.copyWith(
      showFilterButton: PatternFilter().filterStatus != FilterType.hide,
      isFilterApplied: PatternFilter().filterStatus == FilterType.filtered,
    );
  }

  ///
  /// データ容量の制限を越えました、選べません(刺繍メモリフル)
  ///
  void _openTooMuchSelectedPopup() {
    GlobalPopupRoute().updateErrorState(
      nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED,
      arguments: EmbTooMuchSelectedArgument(
        onOKButtonClicked: () {
          GlobalPopupRoute().resetErrorState();
          MdcLibrary().apiBinding.closeMdcMode();
        },
      ),
    );
  }

  ///
  /// 初期化ペイント画面です
  ///
  void _openPaintPage() {
    /// お絵描き画像の描画濃度をセットする (不透過度=デフォルト値)
    MdcLibrary()
        .apiBinding
        .setMdcImageDrawingDensity(DensityLevel.mdc100BackGround0.number);
    ResumeHistoryModel().clearAllHistory();
    ResumeHistoryModel().backSnapshot();

    /// paint画面 へ
    PagesRoute().pushReplacement(nextRoute: PageRouteEnum.paint);
  }

  ///
  /// resume画面を開きます。
  ///
  void _openMdcResumePopup() {
    if (MachineConfigModel().isBrother) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_MDC_RESUME_OK_B,
        arguments: ErrMdcResumeOkBArgument(
          onCancelButtonClicked: _resumePopupCancelButtonClicked,
          onOKButtonClicked: _resumePopupOkButtonClicked,
        ),
      );
    } else {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_MDC_RESUME_OK_T,
        arguments: ErrMdcResumeOkTArgument(
          onCancelButtonClicked: _resumePopupCancelButtonClicked,
          onOKButtonClicked: _resumePopupOkButtonClicked,
        ),
      );
    }
  }

  ///
  /// resumeウィンドウのCancelボタンです
  ///
  void _resumePopupCancelButtonClicked() {
    /// 履歴を消去します
    ResumeHistoryModel().exitResume();
    ResumeHistoryModel().clearAllHistory();
    ResumeHistoryModel().backSnapshot();

    /// paint画面 へ
    PagesRoute().pushNamedAndRemoveUntil(
      nextRoute: PageRouteEnum.paint,
      untilRoute: PageRouteEnum.home,
    );
    GlobalPopupRoute().resetErrorState();
  }

  ///
  /// resumeウィンドウのOKボタンです
  ///
  void _resumePopupOkButtonClicked() {
    /// paint画面のデータを更新します
    ResumeHistoryModel().initBeforeUseMdc();

    /// Libエラー解消
    GlobalPopupRoute().resetErrorState();

    /// 電源が切れる前の画像のタイプを取得
    if (ResumeHistoryModel().isResumeStitchPage == false) {
      PagesRoute().pushNamedAndRemoveUntil(
        nextRoute: PageRouteEnum.paint,
        untilRoute: PageRouteEnum.home,
      );
    } else {
      /// paint画面 => stitch画面 lib側に移動します
      final detail = MdcLibrary().apiBinding.changeMdcPhaseDetail();

      DrawRegionModel().regionNum = detail.regionNum;

      /// brother_panel遷移
      final errorCode = MdcLibrary().apiBinding.gotoMDCPaintToMDCDetailSet();
      if (errorCode != MdcLibraryError.mdcNoError) {
        Log.errorTrace("goto MdcStitch error");
        return;
      }

      Log.debugTrace("goto MdcStitch");
      PagesRoute().pushNamedAndRemoveUntil(
        nextRoute: PageRouteEnum.stitch,
        untilRoute: PageRouteEnum.home,
      );
    }
  }
}
