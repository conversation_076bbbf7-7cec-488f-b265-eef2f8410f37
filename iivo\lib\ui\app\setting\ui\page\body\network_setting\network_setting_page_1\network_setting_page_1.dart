import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import 'network_setting_page_1_view_model.dart';

class NetworkSettingPage1 extends ConsumerStatefulWidget {
  const NetworkSettingPage1({super.key});

  @override
  ConsumerState<NetworkSettingPage1> createState() =>
      _NetworkSettingPage1State();
}

class _NetworkSettingPage1State extends ConsumerState<NetworkSettingPage1> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(networkPage1ViewModelProvider);
    final viewModel = ref.read(networkPage1ViewModelProvider.notifier);
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          Row(
            children: [
              const Spacer(flex: 12),
              Expanded(
                flex: 776,
                child: Stack(
                  children: [
                    Column(
                      children: [
                        Expanded(
                          flex: 230,
                          child: grp_pre_wlan_enable(
                            topText: l10n.icon_00600,
                            bottomText: viewModel.getSSID(l10n),
                            switchValue: state.wirelessLanEnable,
                            onSwitchChanged: (_) {
                              if (state.wirelessLanEnable == false) {
                                viewModel
                                    .onWirelessLanEnableButtonClicked(true);
                              } else {
                                viewModel
                                    .onWirelessLanEnableButtonClicked(false);
                              }
                            },
                          ),
                        ),
                        const Spacer(flex: 8),
                        Expanded(
                          flex: 111,
                          child: grp_pre_wlan_setup_wizard_title(
                            voiceControlFunction:
                                viewModel.onWirelessLanSetupSoundButton,
                            centerLText: l10n.icon_wlan_connection,
                            onTap: () => viewModel
                                .onWirelessLanSetupButtonClicked(context),
                            feedBackControl: null,
                          ),
                        ),
                        const Spacer(flex: 8),
                        Expanded(
                          flex: 230,
                          child: grp_pre_machine_name_title(
                            buttonText: l10n.icon_00187,
                            leftText: l10n.icon_00603,
                            rightText: state.machineName,
                            onTap: () => viewModel
                                .onMachineNameChangeButtonClicked(context),
                          ),
                        ),
                        const Spacer(flex: 8),
                        Expanded(
                          flex: 111,
                          child: grp_pre_wlan_others_title(
                              centerLText: l10n.icon_00399,
                              onTap: () =>
                                  viewModel.onOthersButtonClicked(context)
                          ),
                        ),
                        const Spacer(flex: 8),
                        Expanded(
                          flex: 111,
                          child: grp_pre_network_reset_title(
                            text: l10n.icon_wlan_networkreset,
                            buttonText: l10n.icon_reset,
                            onTap: () => viewModel.openResetPopup(context),
                          ),
                        ),
                        const Spacer(flex: 119),
                      ],
                    ),
                    Column(
                      children: [
                        const Spacer(flex: 128),
                        Expanded(
                          flex: 95,
                          child: Row(
                            children: [
                              const Spacer(flex: 20),
                              Expanded(
                                flex: 469,
                                child: state.isShowLimitedConnectMessage
                                    ? grp_str_massege(
                                        fontSize: 24,
                                        text: l10n.icon_wlan_limitedconnect,
                                      )
                                    : Container(),
                              ),
                              const Spacer(flex: 287),
                            ],
                          ),
                        ),
                        const Spacer(flex: 721),
                      ],
                    ),
                  ],
                ),
              ),
              const Spacer(flex: 12),
            ],
          ),
        ],
      ),
    );
  }
}
