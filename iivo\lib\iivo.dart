import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import 'model/app_locale.dart';
import 'ui/app/testmode/page/page_route.dart';
import 'ui/global_popup/global_popup_route_enum.dart';
import 'ui/page_route/page_route.dart';

GlobalKey<NavigatorState> pageNavigatorKey =
    GlobalKey(debugLabel: "iivo MaterialApp NavigatorState");
GlobalKey<NavigatorState> globalPopupNavigatorKey =
    GlobalKey(debugLabel: "iivo globalPopup NavigatorState");
GlobalKey<NavigatorState> panelPopupNavigatorKey =
    GlobalKey(debugLabel: "iivo panelPopup NavigatorState");
GlobalKey<NavigatorState> overlayEntryKey =
    GlobalKey(debugLabel: "iivo OverlayEntry NavigatorState");
GlobalKey<AppState> appKey = GlobalKey(debugLabel: "iivo");

SubRoutePopupNavigatorState? _subRoutePopupNavigatorState;
SubRoutePopupNavigatorState get subRoutePopupNavigatorState =>
    _subRoutePopupNavigatorState!;

SubRoutePopupNavigatorState? _globalRoutePopupNavigatorState;
SubRoutePopupNavigatorState get globalRoutePopupNavigatorState =>
    _globalRoutePopupNavigatorState!;

AutoDisposeStateProviderRef<void> get globalViewModelRef => _globalRef!;
AutoDisposeStateProviderRef<void>? _globalRef;

///
final _globalProvider = StateProvider.autoDispose<void>((ref) {
  _globalRef = ref;
});

///
/// UI画面を初期化して配置します
///
class IIVO extends ConsumerStatefulWidget {
  const IIVO({super.key});

  @override
  ConsumerState<IIVO> createState() => IIVOState();
}

class IIVOState extends ConsumerState<IIVO> {
  Map<String, PageBuilder> _iivoAllPageRoutes = {};

  @override
  void initState() {
    super.initState();
    _iivoAllPageRoutes = {
      ...iivoPageRoutes,
      ...testModePageRoutes,
    };
  }

  ///
  /// 日誌情報を保存するコールバック関数
  ///
  static void setSaveLogFunction(VoidCallback logFunction) {
    _logFunction = logFunction;
  }

  ///
  /// USBに日誌情報を保存する
  ///
  static VoidCallback? _logFunction;

  @override
  Widget build(BuildContext context) {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
    final langueNotifier = ref.watch(appLocale);

    return _EagerInitialization(
      child: FittedBox(
        alignment: Alignment.topLeft,
        clipBehavior: Clip.antiAlias,
        fit: BoxFit.scaleDown,
        child: SizedBox(
          height: 1280,
          width: 800,
          child: Listener(
            onPointerDown: (event) {
              const Rect rect = Rect.fromLTWH(0, 1219, 130, 61);
              final Offset position = event.localPosition;
              if (rect.contains(position)) {
                _logFunction?.call();
              }
            },
            child: App(
              key: appKey,
              title: 'IIVO',
              localizationsDelegates: AppLocalizations.localizationsDelegates,
              supportedLocales: AppLocalizations.supportedLocales,
              locale: langueNotifier.value,
              theme: ThemeData(
                fontFamily: "Roboto",
                iconTheme: const IconThemeData(color: Colors.transparent),
              ),
              overlayEntryKey: overlayEntryKey,
              pageNavigatorKey: pageNavigatorKey,
              panelPopupNavigatorKey: panelPopupNavigatorKey,
              globalPopupNavigatorKey: globalPopupNavigatorKey,
              initialPageRoute: initialPageRoute,
              registerNamedPage: _iivoAllPageRoutes,
              registerNamedPopup: iivoGlobalPopupRoutes,
              panelPopupNavigatorProvider: (subRoutePopupNavigatorState) {
                _subRoutePopupNavigatorState = subRoutePopupNavigatorState;
              },
              globalPopupNavigatorProvider: (subRoutePopupNavigatorState) {
                _globalRoutePopupNavigatorState = subRoutePopupNavigatorState;
              },
            ),
          ),
        ),
      ),
    );
  }
}

///
/// _globalProvider初期化する
///
/// FAQ
/// Won't this rebuild our entire application when the provider changes?
/// No, this is not the case. In the sample given above, the consumer responsible for eagerly initializing is a separate widget, which does nothing but return a child.
/// The key part is that it returns a child, rather than instantiating MaterialApp itself. This means that if _EagerInitialization ever rebuilds, the child variable will not have changed. And when a widget doesn't change, Flutter doesn't rebuild it.
/// As such, only _EagerInitialization will rebuild, unless another widget is also listening to that provider.
///
class _EagerInitialization extends ConsumerWidget {
  const _EagerInitialization({required this.child});
  final Widget child;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(_globalProvider);
    return child;
  }
}
