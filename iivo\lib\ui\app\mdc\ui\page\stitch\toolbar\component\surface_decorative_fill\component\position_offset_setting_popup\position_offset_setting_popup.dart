import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'position_offset_setting_popup_view_model.dart';

class PositionOffsetSettingPopup extends ConsumerStatefulWidget {
  const PositionOffsetSettingPopup({super.key});

  @override
  ConsumerState<PositionOffsetSettingPopup> createState() =>
      _PositionOffsetSettingPopupState();
}

class _PositionOffsetSettingPopupState
    extends ConsumerState<PositionOffsetSettingPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(positionOffsetSettingPopupViewModelProvider);
    final viewModel =
        ref.read(positionOffsetSettingPopupViewModelProvider.notifier);

    return Row(
      children: [
        const Spacer(flex: 571),
        Expanded(
          flex: 229,
          child: Column(
            children: [
              const Spacer(flex: 159),
              Expanded(
                flex: 1052,
                child: Scaffold(
                  backgroundColor: Colors.transparent,
                  body: pre_edit_toolbar_mdc(
                    child: Column(
                      children: [
                        const Spacer(flex: 37),
                        const Expanded(
                          flex: 68,
                          child: Row(
                            children: [
                              Spacer(flex: 52),
                              Expanded(
                                flex: 126,
                                child: ico_positionoffset(),
                              ),
                              Spacer(flex: 51),
                            ],
                          ),
                        ),
                        const Spacer(flex: 24),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 205,
                                child: grp_str_positionoffset(
                                  text: l10n.icon_00566,
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 36),
                        Expanded(
                          flex: 37,
                          child: Row(
                            children: [
                              const Spacer(flex: 32),
                              const Expanded(
                                flex: 32,
                                child: Column(
                                  children: [
                                    Expanded(
                                      flex: 32,
                                      child: ico_move_leftright(),
                                    ),
                                    Spacer(flex: 5),
                                  ],
                                ),
                              ),
                              Expanded(
                                flex: 90,
                                child: grp_str_number_number1(
                                  text: state.offsetYValue,
                                  isDefaultValue: viewModel.getOffsetYDefault(),
                                ),
                              ),
                              const Spacer(flex: 3),
                              Expanded(
                                flex: 60,
                                child: Column(
                                  children: [
                                    const Spacer(flex: 8),
                                    Expanded(
                                      flex: 26,
                                      child: grp_str_unit_mm1(
                                        text: viewModel.currentSelectedUnit ==
                                                Unit.mm
                                            ? l10n.icon_00225
                                            : l10n.icon_00226,
                                      ),
                                    ),
                                    const Spacer(flex: 3),
                                  ],
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 9),
                        Expanded(
                          flex: 63,
                          child: Row(
                            children: [
                              const Spacer(flex: 48),
                              Expanded(
                                flex: 63,
                                child: grp_btn_minus_01(
                                  onTap: () =>
                                      viewModel.onUpButtonClicked(false),
                                  onLongPress: () =>
                                      viewModel.onUpButtonClicked(true),
                                  state: state.isMinYOffsetValue
                                      ? ButtonState.normal
                                      : ButtonState.disable,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 8),
                              Expanded(
                                flex: 63,
                                child: grp_btn_plus_01(
                                  onTap: () =>
                                      viewModel.onDownButtonClicked(false),
                                  onLongPress: () =>
                                      viewModel.onDownButtonClicked(true),
                                  state: state.isMaxYOffsetValue
                                      ? ButtonState.normal
                                      : ButtonState.disable,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 47),
                            ],
                          ),
                        ),
                        const Spacer(flex: 47),
                        Expanded(
                          flex: 37,
                          child: Row(
                            children: [
                              const Spacer(flex: 32),
                              const Expanded(
                                flex: 32,
                                child: Column(
                                  children: [
                                    Expanded(
                                      flex: 32,
                                      child: ico_move_updown(),
                                    ),
                                    Spacer(flex: 5),
                                  ],
                                ),
                              ),
                              Expanded(
                                flex: 90,
                                child: grp_str_number_number2(
                                  text: state.offsetXValue,
                                  isDefaultValue: viewModel.getOffsetXDefault(),
                                ),
                              ),
                              const Spacer(flex: 3),
                              Expanded(
                                flex: 60,
                                child: Column(
                                  children: [
                                    const Spacer(flex: 8),
                                    Expanded(
                                      flex: 26,
                                      child: grp_str_unit_mm2(
                                        text: viewModel.currentSelectedUnit ==
                                                Unit.mm
                                            ? l10n.icon_00225
                                            : l10n.icon_00226,
                                      ),
                                    ),
                                    const Spacer(flex: 3),
                                  ],
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 9),
                        Expanded(
                          flex: 63,
                          child: Row(
                            children: [
                              const Spacer(flex: 48),
                              Expanded(
                                flex: 63,
                                child: grp_btn_minus_01(
                                  onTap: () =>
                                      viewModel.onLeftButtonClicked(false),
                                  onLongPress: () =>
                                      viewModel.onLeftButtonClicked(true),
                                  state: state.isMinXOffsetValue
                                      ? ButtonState.normal
                                      : ButtonState.disable,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 8),
                              Expanded(
                                flex: 63,
                                child: grp_btn_plus_01(
                                  onTap: () =>
                                      viewModel.onRightButtonClicked(false),
                                  onLongPress: () =>
                                      viewModel.onRightButtonClicked(true),
                                  state: state.isMaxXOffsetValue
                                      ? ButtonState.normal
                                      : ButtonState.disable,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 47),
                            ],
                          ),
                        ),
                        const Spacer(flex: 471),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 205,
                                child: grp_btn_positive_mdc(
                                  onTap: viewModel.onOkButtonClicked,
                                  text: l10n.icon_ok,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(flex: 12),
                            ],
                          ),
                        ),
                        const Spacer(flex: 12),
                      ],
                    ),
                  ),
                ),
              ),
              const Spacer(flex: 69),
            ],
          ),
        ),
      ],
    );
  }
}
