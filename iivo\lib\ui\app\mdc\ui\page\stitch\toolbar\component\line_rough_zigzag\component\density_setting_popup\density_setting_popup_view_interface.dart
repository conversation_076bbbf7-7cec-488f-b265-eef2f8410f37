import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

part 'density_setting_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class DensitySettingPopupState with _$DensitySettingPopupState {
  const factory DensitySettingPopupState({
    required String densityDisplayValue,
    required bool isDefaultStyle,
    required bool plusButtonValid,
    required bool minusButtonValid,
  }) = _DensitySettingPopupState;
}

abstract class DensitySettingPopupViewInterface
    extends ViewModel<DensitySettingPopupState> {
  DensitySettingPopupViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress);

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked(BuildContext context);

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
