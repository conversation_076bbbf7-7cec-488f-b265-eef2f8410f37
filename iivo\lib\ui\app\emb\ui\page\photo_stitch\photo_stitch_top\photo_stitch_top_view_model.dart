import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../../../memory/memory.dart';
import '../../../../../../../model/device_memory_model.dart';
import '../../../../../../../model/handel_model.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../global_popup/global_popups/err_usb_media_set/err_usb_media_set_view_model.dart';
import '../../../../model/photo_stitch/photo_stitch_model.dart';
import '../../../page_route.dart';
import 'photo_stitch_top_view_interface.dart';

///
/// USBディスプレイの最大数
///
const int maxUsbDisplayNumber = 2;

final photoStitchTopViewModelProvider = StateNotifierProvider.autoDispose<
    PhotoStitchTopViewInterface,
    PhotoStitchTopState>((ref) => PhotoStitchTopViewModel(ref));

class PhotoStitchTopViewModel extends PhotoStitchTopViewInterface {
  PhotoStitchTopViewModel(Ref ref) : super(const PhotoStitchTopState()) {
    DeviceMemoryModel().registerAutoDisposeUsbChangeListener(ref, (value) {
      state = state.copyWith(usbInfoList: value);
    });
  }

  @override
  int get getUsbButton1 => usbButton1;

  @override
  int get getUsbButton2 => usbButton2;

  ///
  /// Returnボタンは関数をクリックします
  ///
  @override
  void onReturnButtonClicked() {
    PagesRoute().pop();
    PhotoStitchModel().actionAfterExitingPhotoStitch();
  }

  ///
  /// 内蔵メモリーのクリック関数
  ///
  @override
  void onMachineButtonClicked(context) {
    _showWaitPopup();

    PhotoStitchModel.getFileList(deviceKind: DeviceKind.internalStorage)
        .then((response) {
      /// メモリーアクセスエラー発生
      if (response.error != AccessError.none) {
        return HandelModel.handleMemoryAccessError(response.error);
      }

      /// waitポープアープを閉じる
      _resetWaitPopup();

      /// Model更新
      PhotoStitchModel().photoFileList = response.data;
      PhotoStitchModel().selectedDevice = DeviceKind.internalStorage;

      /// 画面遷移
      /// 自分を閉じる、「fileSelector」へ画面遷移する
      PagesRoute().pushNamed(nextRoute: PageRouteEnum.fileSelector);
    });
  }

  ///
  /// USB1ボタンは関数をクリックします
  ///
  @override
  void onUsb1ButtonClicked(BuildContext context) {
    /// USB 接続確認
    if (state.usbInfoList.isEmpty) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_USB_MEDIA_SET,
        arguments: ErrUsbMediaSetArgument(
          onOkButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// waitポープアープを開ける
    _showWaitPopup();

    /// Model更新
    PhotoStitchModel.getFileList(
            deviceKind: DeviceKind.usb,
            usingUsbPath: state.usbInfoList[usbButton1].usbPath)
        .then((response) {
      /// メモリーアクセスエラー発生
      if (response.error != AccessError.none) {
        return HandelModel.handleMemoryAccessError(response.error);
      }

      /// waitポープアープを閉じる
      _resetWaitPopup();

      /// Model更新
      PhotoStitchModel().photoFileList = response.data;
      PhotoStitchModel().selectedDevice = DeviceKind.usb;
      PhotoStitchModel().selectedUsbPath =
          state.usbInfoList[usbButton1].usbPath;

      /// 画面遷移
      /// 自分を閉じる、「fileSelector」へ画面遷移する
      PagesRoute().pushNamed(nextRoute: PageRouteEnum.fileSelector);
    });
  }

  ///
  /// USB2ボタンは関数をクリックします
  ///
  @override
  void onUsb2ButtonClicked(BuildContext context) {
    if (state.usbInfoList.length < maxUsbDisplayNumber) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// waitポープアープを開ける
    _showWaitPopup();

    PhotoStitchModel.getFileList(
            deviceKind: DeviceKind.usb,
            usingUsbPath: state.usbInfoList[usbButton2].usbPath)
        .then((response) {
      /// メモリーアクセスエラー発生
      if (response.error != AccessError.none) {
        return HandelModel.handleMemoryAccessError(response.error);
      }

      /// waitポープアープを閉じる
      _resetWaitPopup();

      /// Model更新
      PhotoStitchModel().photoFileList = response.data;
      PhotoStitchModel().selectedDevice = DeviceKind.usb;
      PhotoStitchModel().selectedUsbPath =
          state.usbInfoList[usbButton2].usbPath;

      /// 画面遷移
      /// 自分を閉じる、「fileSelector」へ画面遷移する
      PagesRoute().pushNamed(nextRoute: PageRouteEnum.fileSelector);
    });
  }

  ///
  /// WIFIボタンは関数をクリックします
  ///
  @override
  void onWifiButtonClicked(BuildContext context) {
    /// waitポープアープを開ける
    _showWaitPopup();

    PhotoStitchModel.getFileList(deviceKind: DeviceKind.wLan).then((response) {
      /// メモリーアクセスエラー発生
      if (response.error != AccessError.none) {
        return HandelModel.handleMemoryAccessError(response.error);
      }

      /// waitポープアープを閉じる
      _resetWaitPopup();

      /// Model更新
      PhotoStitchModel().photoFileList = response.data;
      PhotoStitchModel().selectedDevice = DeviceKind.wLan;

      /// 画面遷移
      /// 自分を閉じる、「fileSelector」へ画面遷移する
      PagesRoute().pushNamed(nextRoute: PageRouteEnum.fileSelector);
    });
  }

  ///
  /// waitポープアープを開ける
  ///
  void _showWaitPopup() => GlobalPopupRoute().updateErrorState(
      nextRoute: GlobalPopupRouteEnum.ERR_PLEASE_WAIT,
      arguments: {GlobalPopupRoute.isStopSystemSound: true});

  ///
  /// waitポープアープを閉じる
  ///
  void _resetWaitPopup() => GlobalPopupRoute().resetErrorState();
}
