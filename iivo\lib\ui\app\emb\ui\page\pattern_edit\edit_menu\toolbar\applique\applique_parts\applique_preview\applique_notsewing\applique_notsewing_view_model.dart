import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../../../../../../model/preview_area_size_model.dart';
import '../../../../../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../../../../../../model/applique_model.dart';
import '../../../../../../../../../model/pattern_model.dart';
import '../../../../../../../../../model/preview_model.dart';
import '../../../../../../../../../model/redo_undo_model.dart';
import '../../../../../../pattern_edit_view_model.dart';
import 'applique_notsewing_interface.dart';

typedef PatternViewDisplayInfo = PatternDisplayInfo;
typedef EmbBorderViewDisplayInfo = EmbBorderDisplayInfo;
typedef EmbGroupViewDisplayInfo = EmbGroupDisplayInfo;
typedef EmbPatternViewDisplayInfo = EmbPatternDisplayInfo;

final notSewingViewInfoProvider =
    AutoDisposeNotifierProvider<_NotSewingViewInfo, void>(
        () => _NotSewingViewInfo());

class _NotSewingViewInfo extends AutoDisposeNotifier<void> {
  @override
  void build() {}

  BuildContext? _context;
  BuildContext get context => _context!;
  set context(value) => _context = value;
}

final appliqueNotSewingViewModelProvider = StateNotifierProvider.autoDispose<
    AppliqueNotSewingViewModelInterface, AppliqueNotSewingInterface>(
  (ref) => AppliqueNotSewingViewModel(ref),
);

class AppliqueNotSewingViewModel extends AppliqueNotSewingViewModelInterface {
  AppliqueNotSewingViewModel(
    Ref ref,
  ) : super(
            AppliqueNotSewingInterface(
              isNotSewingEnable: false,
              displayList: [],
              scrollController: ScrollController(),
              patternDisplayInfoList: [],
            ),
            ref) {
    _selectedPatternIndex = 0;
    _selectedColorItemIndex = 0;

    /// model更新
    AppliqueModel().selectColor(
      selectedPatternIndex: _selectedPatternIndex,
      selectedColorItemIndex: _selectedColorItemIndex,
    );

    /// View更新
    update();
  }

  @override
  void update() {
    List<ColorListDisplayInfo> displayList =
        AppliqueModel().getDisplayListInNotSewingPage(
      selectedPatternIndex: _selectedPatternIndex,
      selectedColorItemIndex: _selectedColorItemIndex,
      zoomScale: zoomList.first,
    );

    state = state.copyWith(
      partsImage: AppliqueModel().getWappenPreviewTexture(),
      patternDisplayInfoList: _getPatternDisplayInfoList(),
      displayList: displayList,
      thumbnailImage: AppliqueModel().getThumbnailImage(
        selectedPatternIndex: _selectedPatternIndex,
      ),
      isNotSewingEnable: displayList[_selectedPatternIndex]
          .threadInfoDisplayList[_selectedColorItemIndex]
          .isThreadNotSewing,
    );
  }

  ///
  /// 選択したpatternのインデックス
  ///
  int _selectedPatternIndex = 0;

  ///
  /// 選択した線色インデックス
  ///
  int _selectedColorItemIndex = 0;

  @override
  void onColorItemClicked(int patternIndex, int itemIndex) {
    /// model更新
    _selectedPatternIndex = patternIndex;
    _selectedColorItemIndex = itemIndex;

    /// model更新
    AppliqueModel().selectColor(
      selectedPatternIndex: _selectedPatternIndex,
      selectedColorItemIndex: _selectedColorItemIndex,
    );

    /// View更新
    update();
  }

  @override
  void onNotSewingButtonClick() {
    EmbLibraryError embLibraryError = AppliqueModel().changeSettingsSewing(
      patternIndex: _selectedPatternIndex,
      colorIndex: _selectedColorItemIndex,
      notSewing: !state.isNotSewingEnable,
    );
    if (embLibraryError != EmbLibraryError.EMB_NO_ERR) {
      return;
    }

    /// view更新
    update();
  }

  @override
  void onOKButtonClick(BuildContext context) {
    AppliqueModel().completeSelectedApplique();
    EmbLibrary().apiBinding.finishApplique();
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }

    /// 他の画面を更新する
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);
    PopupNavigator.popUntil(context: context, nextRouteName: PopupEnum.toolBar);
  }

  @override
  void onReturnButtonClick(BuildContext context) {
    final EmbLibraryError error =
        EmbLibrary().apiBinding.cancelSelectedApplique();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      Log.errorTrace('cancelSelectedApplique : Unknown Error!!!');
      return;
    }

    /// ThreadInfoを変更し、キャッシュされたデータを消去し、再取得します
    PatternModel()
      ..clearCurrentThreadInfoCache(
          pattern: AppliqueModel().getEditPattern.editPattern)
      ..clearCurrentPatternImageCache(
          pattern: AppliqueModel().getEditPattern.editPattern);

    PopupNavigator.pop(context: context);
  }

  ///
  /// 選択パターンの表示情報の取得
  ///
  List<PatternDisplayInfo> _getPatternDisplayInfoList() {
    List<PatternDisplayInfo> patternDisplayInfoList = [];

    /// プレビュー中心点
    final Offset originalCenter = embPreviewSizeDot / 2;

    /// 1mmに対応する画素数
    final double pixelOfOneMm = embPreviewSizeDot.dx / frame297x465MmSize.dx;

    /// カレントグループのハンドル
    final currentGroupHandle = PatternModel().getCurrentGroupHandle();

    final Pattern currentPattern = AppliqueModel().getEditPattern.editPattern;

    /// 選択パターンの表示情報の取得
    if (currentPattern is EmbGroup) {
      patternDisplayInfoList.add(PreviewModel().getGroupPatternDisplayInfo(
        scrollType: ScrollCenterType.IMAGE_EDITING,
        group: currentPattern,
        centerPoint: originalCenter,
        pixelOfOneMm: pixelOfOneMm,
        sewingIndex: 0,
        currentGroupHandle: currentGroupHandle,
        zoomScale: zoomList.first,
      ));
    } else if (currentPattern is EmbBorder) {
      patternDisplayInfoList.add(PreviewModel().getBorderPatternDisplayInfo(
        scrollType: ScrollCenterType.IMAGE_EDITING,
        border: currentPattern,
        centerPoint: originalCenter,
        pixelOfOneMm: pixelOfOneMm,
        sewingIndex: 0,
        currentGroupHandle: currentGroupHandle,
        zoomScale: zoomList.first,
      ));
    } else {
      /// Do noting
    }

    /// 追加された新規追加の模様の表示情報
    if (PatternModel().temporaryGroupList.firstOrNull != null) {
      patternDisplayInfoList.add(PreviewModel().getGroupPatternDisplayInfo(
        scrollType: ScrollCenterType.IMAGE_EDITING,
        group: PatternModel().temporaryGroupList.first,
        centerPoint: originalCenter,
        pixelOfOneMm: pixelOfOneMm,
        sewingIndex: 0,
        currentGroupHandle: currentGroupHandle,
        zoomScale: zoomList.first,
      ));
    } else {
      /// Do noting
    }

    return patternDisplayInfoList;
  }
}
