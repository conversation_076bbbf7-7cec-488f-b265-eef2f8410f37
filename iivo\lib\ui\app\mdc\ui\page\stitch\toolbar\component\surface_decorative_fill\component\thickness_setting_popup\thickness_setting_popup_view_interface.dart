import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'thickness_setting_popup_view_interface.freezed.dart';

@freezed
class ThicknessSettingPopupState with _$ThicknessSettingPopupState {
  const factory ThicknessSettingPopupState({
    @Default(false) bool isThickButtonSelected,
    @Default(false) bool isNarrowButtonSelected,
  }) = _ThicknessSettingPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class ThicknessSettingPopupStateViewInterface
    extends ViewModel<ThicknessSettingPopupState> {
  ThicknessSettingPopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// 1~2重ボタンがクリックされました
  ///
  void onNarrowButtonClicked();

  ///
  /// 2～3重ボタンがクリックされました
  ///
  void onThickButtonClicked();
}
