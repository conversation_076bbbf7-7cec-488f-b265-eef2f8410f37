// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'parameter_set_interface.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ParameterSetState {
  bool get isMultiCharSelectMode => throw _privateConstructorUsedError;
  Offset? get redLinePosition => throw _privateConstructorUsedError;
  Rect? get dashedBoxRect => throw _privateConstructorUsedError;
  ButtonState get leftMoveButtonState => throw _privateConstructorUsedError;
  ButtonState get rightMoveButtonState => throw _privateConstructorUsedError;
  ButtonState get realPreviewButtonState => throw _privateConstructorUsedError;
  List<ShowDataList> get disPlayText => throw _privateConstructorUsedError;
  String get fontType => throw _privateConstructorUsedError;
  Widget? get fontTypeImage => throw _privateConstructorUsedError;
  String get fontSize => throw _privateConstructorUsedError;
  int get frameIndex => throw _privateConstructorUsedError;
  String get patternWidth => throw _privateConstructorUsedError;
  String get patternHight => throw _privateConstructorUsedError;
  int get zoomScaleIndex => throw _privateConstructorUsedError;
  bool get isZoomPopupOn => throw _privateConstructorUsedError;
  bool get isHandleSelected => throw _privateConstructorUsedError;
  bool get isInch => throw _privateConstructorUsedError;
  bool get isExclusiveType => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ParameterSetStateCopyWith<ParameterSetState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ParameterSetStateCopyWith<$Res> {
  factory $ParameterSetStateCopyWith(
          ParameterSetState value, $Res Function(ParameterSetState) then) =
      _$ParameterSetStateCopyWithImpl<$Res, ParameterSetState>;
  @useResult
  $Res call(
      {bool isMultiCharSelectMode,
      Offset? redLinePosition,
      Rect? dashedBoxRect,
      ButtonState leftMoveButtonState,
      ButtonState rightMoveButtonState,
      ButtonState realPreviewButtonState,
      List<ShowDataList> disPlayText,
      String fontType,
      Widget? fontTypeImage,
      String fontSize,
      int frameIndex,
      String patternWidth,
      String patternHight,
      int zoomScaleIndex,
      bool isZoomPopupOn,
      bool isHandleSelected,
      bool isInch,
      bool isExclusiveType});
}

/// @nodoc
class _$ParameterSetStateCopyWithImpl<$Res, $Val extends ParameterSetState>
    implements $ParameterSetStateCopyWith<$Res> {
  _$ParameterSetStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isMultiCharSelectMode = null,
    Object? redLinePosition = freezed,
    Object? dashedBoxRect = freezed,
    Object? leftMoveButtonState = freezed,
    Object? rightMoveButtonState = freezed,
    Object? realPreviewButtonState = freezed,
    Object? disPlayText = null,
    Object? fontType = null,
    Object? fontTypeImage = freezed,
    Object? fontSize = null,
    Object? frameIndex = null,
    Object? patternWidth = null,
    Object? patternHight = null,
    Object? zoomScaleIndex = null,
    Object? isZoomPopupOn = null,
    Object? isHandleSelected = null,
    Object? isInch = null,
    Object? isExclusiveType = null,
  }) {
    return _then(_value.copyWith(
      isMultiCharSelectMode: null == isMultiCharSelectMode
          ? _value.isMultiCharSelectMode
          : isMultiCharSelectMode // ignore: cast_nullable_to_non_nullable
              as bool,
      redLinePosition: freezed == redLinePosition
          ? _value.redLinePosition
          : redLinePosition // ignore: cast_nullable_to_non_nullable
              as Offset?,
      dashedBoxRect: freezed == dashedBoxRect
          ? _value.dashedBoxRect
          : dashedBoxRect // ignore: cast_nullable_to_non_nullable
              as Rect?,
      leftMoveButtonState: freezed == leftMoveButtonState
          ? _value.leftMoveButtonState
          : leftMoveButtonState // ignore: cast_nullable_to_non_nullable
              as ButtonState,
      rightMoveButtonState: freezed == rightMoveButtonState
          ? _value.rightMoveButtonState
          : rightMoveButtonState // ignore: cast_nullable_to_non_nullable
              as ButtonState,
      realPreviewButtonState: freezed == realPreviewButtonState
          ? _value.realPreviewButtonState
          : realPreviewButtonState // ignore: cast_nullable_to_non_nullable
              as ButtonState,
      disPlayText: null == disPlayText
          ? _value.disPlayText
          : disPlayText // ignore: cast_nullable_to_non_nullable
              as List<ShowDataList>,
      fontType: null == fontType
          ? _value.fontType
          : fontType // ignore: cast_nullable_to_non_nullable
              as String,
      fontTypeImage: freezed == fontTypeImage
          ? _value.fontTypeImage
          : fontTypeImage // ignore: cast_nullable_to_non_nullable
              as Widget?,
      fontSize: null == fontSize
          ? _value.fontSize
          : fontSize // ignore: cast_nullable_to_non_nullable
              as String,
      frameIndex: null == frameIndex
          ? _value.frameIndex
          : frameIndex // ignore: cast_nullable_to_non_nullable
              as int,
      patternWidth: null == patternWidth
          ? _value.patternWidth
          : patternWidth // ignore: cast_nullable_to_non_nullable
              as String,
      patternHight: null == patternHight
          ? _value.patternHight
          : patternHight // ignore: cast_nullable_to_non_nullable
              as String,
      zoomScaleIndex: null == zoomScaleIndex
          ? _value.zoomScaleIndex
          : zoomScaleIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isZoomPopupOn: null == isZoomPopupOn
          ? _value.isZoomPopupOn
          : isZoomPopupOn // ignore: cast_nullable_to_non_nullable
              as bool,
      isHandleSelected: null == isHandleSelected
          ? _value.isHandleSelected
          : isHandleSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isInch: null == isInch
          ? _value.isInch
          : isInch // ignore: cast_nullable_to_non_nullable
              as bool,
      isExclusiveType: null == isExclusiveType
          ? _value.isExclusiveType
          : isExclusiveType // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ParameterSetStateCopyWith<$Res>
    implements $ParameterSetStateCopyWith<$Res> {
  factory _$$_ParameterSetStateCopyWith(_$_ParameterSetState value,
          $Res Function(_$_ParameterSetState) then) =
      __$$_ParameterSetStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isMultiCharSelectMode,
      Offset? redLinePosition,
      Rect? dashedBoxRect,
      ButtonState leftMoveButtonState,
      ButtonState rightMoveButtonState,
      ButtonState realPreviewButtonState,
      List<ShowDataList> disPlayText,
      String fontType,
      Widget? fontTypeImage,
      String fontSize,
      int frameIndex,
      String patternWidth,
      String patternHight,
      int zoomScaleIndex,
      bool isZoomPopupOn,
      bool isHandleSelected,
      bool isInch,
      bool isExclusiveType});
}

/// @nodoc
class __$$_ParameterSetStateCopyWithImpl<$Res>
    extends _$ParameterSetStateCopyWithImpl<$Res, _$_ParameterSetState>
    implements _$$_ParameterSetStateCopyWith<$Res> {
  __$$_ParameterSetStateCopyWithImpl(
      _$_ParameterSetState _value, $Res Function(_$_ParameterSetState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isMultiCharSelectMode = null,
    Object? redLinePosition = freezed,
    Object? dashedBoxRect = freezed,
    Object? leftMoveButtonState = freezed,
    Object? rightMoveButtonState = freezed,
    Object? realPreviewButtonState = freezed,
    Object? disPlayText = null,
    Object? fontType = null,
    Object? fontTypeImage = freezed,
    Object? fontSize = null,
    Object? frameIndex = null,
    Object? patternWidth = null,
    Object? patternHight = null,
    Object? zoomScaleIndex = null,
    Object? isZoomPopupOn = null,
    Object? isHandleSelected = null,
    Object? isInch = null,
    Object? isExclusiveType = null,
  }) {
    return _then(_$_ParameterSetState(
      isMultiCharSelectMode: null == isMultiCharSelectMode
          ? _value.isMultiCharSelectMode
          : isMultiCharSelectMode // ignore: cast_nullable_to_non_nullable
              as bool,
      redLinePosition: freezed == redLinePosition
          ? _value.redLinePosition
          : redLinePosition // ignore: cast_nullable_to_non_nullable
              as Offset?,
      dashedBoxRect: freezed == dashedBoxRect
          ? _value.dashedBoxRect
          : dashedBoxRect // ignore: cast_nullable_to_non_nullable
              as Rect?,
      leftMoveButtonState: freezed == leftMoveButtonState
          ? _value.leftMoveButtonState
          : leftMoveButtonState // ignore: cast_nullable_to_non_nullable
              as ButtonState,
      rightMoveButtonState: freezed == rightMoveButtonState
          ? _value.rightMoveButtonState
          : rightMoveButtonState // ignore: cast_nullable_to_non_nullable
              as ButtonState,
      realPreviewButtonState: freezed == realPreviewButtonState
          ? _value.realPreviewButtonState
          : realPreviewButtonState // ignore: cast_nullable_to_non_nullable
              as ButtonState,
      disPlayText: null == disPlayText
          ? _value._disPlayText
          : disPlayText // ignore: cast_nullable_to_non_nullable
              as List<ShowDataList>,
      fontType: null == fontType
          ? _value.fontType
          : fontType // ignore: cast_nullable_to_non_nullable
              as String,
      fontTypeImage: freezed == fontTypeImage
          ? _value.fontTypeImage
          : fontTypeImage // ignore: cast_nullable_to_non_nullable
              as Widget?,
      fontSize: null == fontSize
          ? _value.fontSize
          : fontSize // ignore: cast_nullable_to_non_nullable
              as String,
      frameIndex: null == frameIndex
          ? _value.frameIndex
          : frameIndex // ignore: cast_nullable_to_non_nullable
              as int,
      patternWidth: null == patternWidth
          ? _value.patternWidth
          : patternWidth // ignore: cast_nullable_to_non_nullable
              as String,
      patternHight: null == patternHight
          ? _value.patternHight
          : patternHight // ignore: cast_nullable_to_non_nullable
              as String,
      zoomScaleIndex: null == zoomScaleIndex
          ? _value.zoomScaleIndex
          : zoomScaleIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isZoomPopupOn: null == isZoomPopupOn
          ? _value.isZoomPopupOn
          : isZoomPopupOn // ignore: cast_nullable_to_non_nullable
              as bool,
      isHandleSelected: null == isHandleSelected
          ? _value.isHandleSelected
          : isHandleSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isInch: null == isInch
          ? _value.isInch
          : isInch // ignore: cast_nullable_to_non_nullable
              as bool,
      isExclusiveType: null == isExclusiveType
          ? _value.isExclusiveType
          : isExclusiveType // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_ParameterSetState implements _ParameterSetState {
  _$_ParameterSetState(
      {this.isMultiCharSelectMode = true,
      this.redLinePosition = null,
      this.dashedBoxRect = null,
      this.leftMoveButtonState = ButtonState.normal,
      this.rightMoveButtonState = ButtonState.normal,
      this.realPreviewButtonState = ButtonState.normal,
      final List<ShowDataList> disPlayText = const [],
      required this.fontType,
      this.fontTypeImage = null,
      this.fontSize = "",
      this.frameIndex = 0,
      this.patternWidth = "----",
      this.patternHight = "----",
      this.zoomScaleIndex = 0,
      this.isZoomPopupOn = false,
      this.isHandleSelected = false,
      this.isInch = false,
      this.isExclusiveType = false})
      : _disPlayText = disPlayText;

  @override
  @JsonKey()
  final bool isMultiCharSelectMode;
  @override
  @JsonKey()
  final Offset? redLinePosition;
  @override
  @JsonKey()
  final Rect? dashedBoxRect;
  @override
  @JsonKey()
  final ButtonState leftMoveButtonState;
  @override
  @JsonKey()
  final ButtonState rightMoveButtonState;
  @override
  @JsonKey()
  final ButtonState realPreviewButtonState;
  final List<ShowDataList> _disPlayText;
  @override
  @JsonKey()
  List<ShowDataList> get disPlayText {
    if (_disPlayText is EqualUnmodifiableListView) return _disPlayText;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_disPlayText);
  }

  @override
  final String fontType;
  @override
  @JsonKey()
  final Widget? fontTypeImage;
  @override
  @JsonKey()
  final String fontSize;
  @override
  @JsonKey()
  final int frameIndex;
  @override
  @JsonKey()
  final String patternWidth;
  @override
  @JsonKey()
  final String patternHight;
  @override
  @JsonKey()
  final int zoomScaleIndex;
  @override
  @JsonKey()
  final bool isZoomPopupOn;
  @override
  @JsonKey()
  final bool isHandleSelected;
  @override
  @JsonKey()
  final bool isInch;
  @override
  @JsonKey()
  final bool isExclusiveType;

  @override
  String toString() {
    return 'ParameterSetState(isMultiCharSelectMode: $isMultiCharSelectMode, redLinePosition: $redLinePosition, dashedBoxRect: $dashedBoxRect, leftMoveButtonState: $leftMoveButtonState, rightMoveButtonState: $rightMoveButtonState, realPreviewButtonState: $realPreviewButtonState, disPlayText: $disPlayText, fontType: $fontType, fontTypeImage: $fontTypeImage, fontSize: $fontSize, frameIndex: $frameIndex, patternWidth: $patternWidth, patternHight: $patternHight, zoomScaleIndex: $zoomScaleIndex, isZoomPopupOn: $isZoomPopupOn, isHandleSelected: $isHandleSelected, isInch: $isInch, isExclusiveType: $isExclusiveType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ParameterSetState &&
            (identical(other.isMultiCharSelectMode, isMultiCharSelectMode) ||
                other.isMultiCharSelectMode == isMultiCharSelectMode) &&
            (identical(other.redLinePosition, redLinePosition) ||
                other.redLinePosition == redLinePosition) &&
            (identical(other.dashedBoxRect, dashedBoxRect) ||
                other.dashedBoxRect == dashedBoxRect) &&
            const DeepCollectionEquality()
                .equals(other.leftMoveButtonState, leftMoveButtonState) &&
            const DeepCollectionEquality()
                .equals(other.rightMoveButtonState, rightMoveButtonState) &&
            const DeepCollectionEquality()
                .equals(other.realPreviewButtonState, realPreviewButtonState) &&
            const DeepCollectionEquality()
                .equals(other._disPlayText, _disPlayText) &&
            (identical(other.fontType, fontType) ||
                other.fontType == fontType) &&
            (identical(other.fontTypeImage, fontTypeImage) ||
                other.fontTypeImage == fontTypeImage) &&
            (identical(other.fontSize, fontSize) ||
                other.fontSize == fontSize) &&
            (identical(other.frameIndex, frameIndex) ||
                other.frameIndex == frameIndex) &&
            (identical(other.patternWidth, patternWidth) ||
                other.patternWidth == patternWidth) &&
            (identical(other.patternHight, patternHight) ||
                other.patternHight == patternHight) &&
            (identical(other.zoomScaleIndex, zoomScaleIndex) ||
                other.zoomScaleIndex == zoomScaleIndex) &&
            (identical(other.isZoomPopupOn, isZoomPopupOn) ||
                other.isZoomPopupOn == isZoomPopupOn) &&
            (identical(other.isHandleSelected, isHandleSelected) ||
                other.isHandleSelected == isHandleSelected) &&
            (identical(other.isInch, isInch) || other.isInch == isInch) &&
            (identical(other.isExclusiveType, isExclusiveType) ||
                other.isExclusiveType == isExclusiveType));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isMultiCharSelectMode,
      redLinePosition,
      dashedBoxRect,
      const DeepCollectionEquality().hash(leftMoveButtonState),
      const DeepCollectionEquality().hash(rightMoveButtonState),
      const DeepCollectionEquality().hash(realPreviewButtonState),
      const DeepCollectionEquality().hash(_disPlayText),
      fontType,
      fontTypeImage,
      fontSize,
      frameIndex,
      patternWidth,
      patternHight,
      zoomScaleIndex,
      isZoomPopupOn,
      isHandleSelected,
      isInch,
      isExclusiveType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ParameterSetStateCopyWith<_$_ParameterSetState> get copyWith =>
      __$$_ParameterSetStateCopyWithImpl<_$_ParameterSetState>(
          this, _$identity);
}

abstract class _ParameterSetState implements ParameterSetState {
  factory _ParameterSetState(
      {final bool isMultiCharSelectMode,
      final Offset? redLinePosition,
      final Rect? dashedBoxRect,
      final ButtonState leftMoveButtonState,
      final ButtonState rightMoveButtonState,
      final ButtonState realPreviewButtonState,
      final List<ShowDataList> disPlayText,
      required final String fontType,
      final Widget? fontTypeImage,
      final String fontSize,
      final int frameIndex,
      final String patternWidth,
      final String patternHight,
      final int zoomScaleIndex,
      final bool isZoomPopupOn,
      final bool isHandleSelected,
      final bool isInch,
      final bool isExclusiveType}) = _$_ParameterSetState;

  @override
  bool get isMultiCharSelectMode;
  @override
  Offset? get redLinePosition;
  @override
  Rect? get dashedBoxRect;
  @override
  ButtonState get leftMoveButtonState;
  @override
  ButtonState get rightMoveButtonState;
  @override
  ButtonState get realPreviewButtonState;
  @override
  List<ShowDataList> get disPlayText;
  @override
  String get fontType;
  @override
  Widget? get fontTypeImage;
  @override
  String get fontSize;
  @override
  int get frameIndex;
  @override
  String get patternWidth;
  @override
  String get patternHight;
  @override
  int get zoomScaleIndex;
  @override
  bool get isZoomPopupOn;
  @override
  bool get isHandleSelected;
  @override
  bool get isInch;
  @override
  bool get isExclusiveType;
  @override
  @JsonKey(ignore: true)
  _$$_ParameterSetStateCopyWith<_$_ParameterSetState> get copyWith =>
      throw _privateConstructorUsedError;
}
