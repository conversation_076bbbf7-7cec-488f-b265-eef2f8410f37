import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/device_info_model.dart';
import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/surface_tatami_model.dart';
import '../../../../../../model/stitch/toolbar_model.dart';
import '../../../stitch_page_view_model.dart';
import 'surface_tatami_view_interface.dart';

typedef SurfaceTatamiViewModelProvider = AutoDisposeStateNotifierProvider<
    SurfaceTatamStateViewInterface, SurfaceTatamiState>;

class SurfaceTatamiViewModel extends SurfaceTatamStateViewInterface {
  SurfaceTatamiViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const SurfaceTatamiState(), ref) {
    update();
  }

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  ///
  /// 「タタミ縫い」の下打ち有無は異なるの場合は"*"を表示する
  ///
  static const differentUnderSewingValue = "**";

  @override
  void update() {
    state = state.copyWith(
      directionValue: _getDirectionValue(),
      densityValue: _getDensityValue(),
      pullCompensationValue: _getPullCompensationValueValue(),
      isUnderSewingValeSame: SurfaceTatamiModel().getUnderSewing() !=
              SurfaceTatamiModel.underSewingNotUpdating
          ? true
          : false,
      underSewingState: SurfaceTatamiModel().getUnderSewing(),
    );
  }

  @override
  void onDirectionButtonClicked(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceTatamiDirection.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void onDensityButtonClicked(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceTatamiDensity.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void onPullCompensationButtonClicked(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceTatamiPullCompensation.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void openUnderSewingSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceTatamiUnderSewing.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  bool isDirectionAutoState() {
    int direction = SurfaceTatamiModel().getDirection();
    MDCTatamiDirKind dirKind = SurfaceTatamiModel().getDirKind();
    if (direction != SurfaceTatamiModel.directionNotUpdating) {
      if (dirKind == MDCTatamiDirKind.mdc_tatami_dir_kind_auto) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  @override
  bool getDirectionDefault() {
    if (SurfaceTatamiModel().getDirKind() ==
        MDCTatamiDirKind.mdc_tatami_dir_kind_invalid) {
      return true;
    }
    return SurfaceTatamiModel().getDisplayDefault(
        SurfaceTatamiModel().getDirection() !=
                SurfaceTatamiModel.directionNotUpdating
            ? TatamiSettingState.settingCompleted
            : TatamiSettingState.unknown,
        SurfaceTatamiModel().getDirection(),
        SurfaceTatamiModel().directionDefaultValue);
  }

  @override
  bool getDensityDefault() => SurfaceTatamiModel().getDisplayDefault(
      SurfaceTatamiModel().getDensityIndex() !=
              SurfaceTatamiModel.densityNotUpdating.index
          ? TatamiSettingState.settingCompleted
          : TatamiSettingState.unknown,
      SurfaceTatamiModel().getDensityIndex(),
      SurfaceTatamiModel().densityDefaultValue.index);

  @override
  bool getPullCompensationDefault() => SurfaceTatamiModel().getDisplayDefault(
      SurfaceTatamiModel().getPullCompensation() !=
              SurfaceTatamiModel.pullCompensationNotUpdating
          ? TatamiSettingState.settingCompleted
          : TatamiSettingState.unknown,
      SurfaceTatamiModel().getPullCompensation(),
      SurfaceTatamiModel().pullCompensationDefaultValue);

  ///
  /// タタミ縫いの方向の値を取得する
  ///
  String _getDirectionValue() {
    if (SurfaceTatamiModel().getDirKind() ==
        MDCTatamiDirKind.mdc_tatami_dir_kind_invalid) {
      return SurfaceTatamiModel.differentDirectionValue;
    }
    if (SurfaceTatamiModel().getDirection() !=
        SurfaceTatamiModel.directionNotUpdating) {
      return SurfaceTatamiModel().getDirection().toString();
    } else {
      return SurfaceTatamiModel.differentDirectionValue;
    }
  }

  ///
  /// 糸密度の値を取得する
  ///
  String _getDensityValue() {
    if (SurfaceTatamiModel().getDensityIndex() !=
        SurfaceTatamiModel.densityNotUpdating.index) {
      int index = SurfaceTatamiModel().getDensityIndex();
      return SurfaceTatamiModel.densityList[index].toString();
    } else {
      return SurfaceTatamiModel.differentDensityValue;
    }
  }

  ///
  /// 縫い縮み設定(ステッチの長さ補正)の値を取得する
  ///
  String _getPullCompensationValueValue() {
    int pullCompensation = SurfaceTatamiModel().getPullCompensation();

    /// cmからmmへ
    double value = pullCompensation / 10.0;

    if (pullCompensation != SurfaceTatamiModel.pullCompensationNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return value.toStringAsFixed(1);
      } else {
        return ToolbarModel.getDisplayInchShowValue(value);
      }
    } else {
      if (currentSelectedUnit == Unit.mm) {
        return SurfaceTatamiModel.differentMmPullCompensationValue;
      } else {
        return SurfaceTatamiModel.differentInchPullCompensationValue;
      }
    }
  }
}
