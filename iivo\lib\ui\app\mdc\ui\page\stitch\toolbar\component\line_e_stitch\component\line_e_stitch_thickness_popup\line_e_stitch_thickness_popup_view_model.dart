import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_e_stitch_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'line_e_stitch_thickness_popup_view_interface.dart';

final lineEStitchThicknessViewModelProvider = StateNotifierProvider.autoDispose<
    LineEStitchThicknessStateViewInterface,
    LineEStitchThicknessState>((ref) => LineEStitchThicknessViewModel(ref));

class LineEStitchThicknessViewModel
    extends LineEStitchThicknessStateViewInterface {
  LineEStitchThicknessViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineEStitchThicknessState(
                thicknessDisplayValue: "",
                isDefaultStyle: false,
                plusButtonValid: false,
                minusButtonValid: false),
            ref) {
    update();
  }

  @override
  int get maxThicknessValue => LineEStitchModel.maxiThicknessValue;

  @override
  int get minThicknessValue => LineEStitchModel.miniThicknessValue;

  ///
  /// ステップ量
  ///
  final int _stepValue = 2;

  ///
  /// 厚さの値ディスプレイスター
  ///
  bool _isThicknessValueDisplayStar =
      LineEStitchModel().getThickness() != LineEStitchModel.thickNessNotUpdating
          ? false
          : true;

  ///
  /// 厚さの値
  ///
  int _thicknessValue = LineEStitchModel().getThickness();

  @override
  void update() {
    state = state.copyWith(
        thicknessDisplayValue: _getThicknessDisplayValue(),
        isDefaultStyle: _isDefaultStyle(),
        plusButtonValid: _getPlusButtonState(),
        minusButtonValid: _getMinusButtonState());
  }

  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isThicknessValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isThicknessValueDisplayStar = false;

      ///  Model 更新
      _thicknessValue = LineEStitchModel().thicknessDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_thicknessValue <= LineEStitchModel.miniThicknessValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _thicknessValue =
        _thicknessValue - _stepValue < LineEStitchModel.miniThicknessValue
            ? LineEStitchModel.miniThicknessValue
            : _thicknessValue - _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_isThicknessValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isThicknessValueDisplayStar = false;

      ///  Model 更新
      _thicknessValue = LineEStitchModel().thicknessDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_thicknessValue >= LineEStitchModel.maxiThicknessValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _thicknessValue =
        _thicknessValue + _stepValue > LineEStitchModel.maxiThicknessValue
            ? LineEStitchModel.maxiThicknessValue
            : _thicknessValue + _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineEStitchThickness.toString());
    if (_isThicknessValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int thicknessValue = LineEStitchModel().getThickness();

    /// Model 更新
    LineEStitchModel().setThickness(_thicknessValue);
    if (LineEStitchModel().setMdcEStitchSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (thicknessValue != _thicknessValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// 厚さの表示値を取得します
  ///
  String _getThicknessDisplayValue() {
    if (_isThicknessValueDisplayStar) {
      return "*";
    }

    return _thicknessValue.toString();
  }

  ///
  /// 太さ表示のテキスト スタイルを取得します
  ///
  bool _isDefaultStyle() {
    if (_isThicknessValueDisplayStar) {
      return true;
    }

    if (_thicknessValue == LineEStitchModel().thicknessDefaultValue) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isThicknessValueDisplayStar) {
      return true;
    }

    if (_thicknessValue <= LineEStitchModel.miniThicknessValue) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isThicknessValueDisplayStar) {
      return true;
    }

    if (_thicknessValue >= LineEStitchModel.maxiThicknessValue) {
      return false;
    }
    return true;
  }
}
