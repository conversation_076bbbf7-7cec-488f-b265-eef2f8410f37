import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'auto_test_mode_interface.freezed.dart';

@freezed
class AutoTestModeState with _$AutoTestModeState {
  const factory AutoTestModeState(
      {@Default(false) bool isPerformingActions,
      @Default(null) String? currentActionDesc}) = _AutoTestModeState;
}

abstract class AutoTestModeViewModelInterface
    extends ViewModel<AutoTestModeState> {
  AutoTestModeViewModelInterface(super.state, this.ref);

  Ref ref;

  Future<void> performAutoTestSetup();
}
