import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:system_config/system_config.dart';

import '../../../../../../model/machine_config_model.dart';
import '../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../model/setting_model.dart';
import '../../../../../component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../../../../../page_route/page_route.dart';
import '../../../../home/<USER>/home_model.dart';
import '../../../model/clock_setting_model.dart';
import '../../component/setting_footer/setting_footer_view_model.dart';
import '../../component/stop_sewing/stop_sewing_view_model.dart';
import 'clock_setting_interface.dart';

/// 時間変化の量
const int _timeStep = 1;

/// 最大年値
const int _yearValueMax = 2037;

/// 最小年値
const int _yearValueMin = 2024;

/// 最大月値
const int _monthValueMax = 12;

/// 最小月値
const int _monthValueMin = 1;

/// 12時間の最大値
const int _hour12ValueMax = 12;

/// 12時間の最小値
const int _hour12ValueMin = 1;

/// 24時間の最大値
const int _hour24ValueMax = 23;

/// 24時間の最小値
const int _hour24ValueMin = 0;

/// 最大分値
const int _minuteValueMax = 59;

/// 最小分値
const int _minuteValueMin = 0;
final clockSettingViewModelProvider = StateNotifierProvider.autoDispose<
    ClockSettingViewModelInterface, ClockSettingInterface>(
  (ref) => ClockSettingViewModel(ref),
);

class ClockSettingViewModel extends ClockSettingViewModelInterface {
  ClockSettingViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const ClockSettingInterface(
              dayDisplayValue: '',
              hourDisplayValue: '',
              isSelectClockDisplayON: false,
              minuteDisplayValue: '',
              monthDisplayValue: '',
              showTimeType: ShowTimeType.pm,
              yearDisplayValue: '',
              isSwitchButtonEnabled: true,
              isOkButtonEnabled: false,
            ),
            ref) {
    /// model更新
    ClockSettingModel().initCurrentTime();
    SettingModel().isClockPopup = true;
    MachineConfigModel().currentMode = SettingBaseMode.setting;

    /// View更新
    update();
    ref.listen(
      appDisplayGlobalStateProvider.select((value) => value.isStopingMotor),
      (previous, nextState) {
        if (previous == true && nextState == false) {
          /// 縫製が始まったら、ストップマスクを開けます
          ref
              .read(stopSewingPopupViewModelProvider.notifier)
              .openStopSewingPopup();
        } else if (previous == false && nextState == true) {
          /// 縫製停止、クローズ停止マスキング
          ref
              .read(stopSewingPopupViewModelProvider.notifier)
              .closeStopSewingPopup();
        }
      },
    );

    /// 画面を閉じる際、StopSewingが確実に閉じるようにする
    ref.onDispose(() => ref
        .read(stopSewingPopupViewModelProvider.notifier)
        .closeStopSewingPopup());
  }

  @override
  void update() {
    /// showTimeType 必ず分けて書いてください。そうしないと機能に影響が出る可能性があります。
    state = state.copyWith(
      showTimeType: ClockSettingModel.showTimeInputType,
    );

    /// 初めて機械を起動したとき
    if (!DeviceLibrary().apiBinding.getIsSetTime()) {
      state = state.copyWith(
        isSelectClockDisplayON: false,
        isSwitchButtonEnabled: false,
        isOkButtonEnabled: false,
        yearDisplayValue: "- - - -",
        monthDisplayValue: "- -",
        dayDisplayValue: "- -",
        hourDisplayValue: "- -",
        minuteDisplayValue: "- -",
      );
      return;
    }

    state = state.copyWith(
      isSelectClockDisplayON:
          DeviceLibrary().apiBinding.getClockDisplayStatus(),
      isSwitchButtonEnabled: true,
      isOkButtonEnabled: true,
      yearDisplayValue: ClockSettingModel.yearInputValue.toString(),
      monthDisplayValue:
          ClockSettingModel.monthInputValue.toString().padLeft(2, "0"),
      dayDisplayValue:
          ClockSettingModel.dayInputValue.toString().padLeft(2, "0"),
      hourDisplayValue: ClockSettingModel.showTimeInputType == ShowTimeType.hour
          ? ClockSettingModel.hour24InputValue.toString().padLeft(2, "0")
          : ClockSettingModel.hour12InputValue.toString().padLeft(2, "0"),
      minuteDisplayValue:
          ClockSettingModel.minuteInputValue.toString().padLeft(2, "0"),
    );
  }

  ///
  /// カスタム時間を設定する
  /// showTimeInputType.pm 時間はPMの形をしています
  /// showTimeInputType.am 時間はAMの形をしています
  /// showTimeInputType.hour 時間はhourの形をしています
  ///
  void _setTheCurrentTime() => SystemConfig.setTheCurrentTime(
        int.parse(state.yearDisplayValue),
        int.parse(state.monthDisplayValue),
        int.parse(state.dayDisplayValue),
        ClockSettingModel.change12HourTo24(int.parse(state.hourDisplayValue)),
        int.parse(state.minuteDisplayValue),
      );

  @override
  void onOKButtonClick() {
    if (DeviceLibrary().apiBinding.getIsSetTime()) {
      /// カスタム時間を設定する
      _setTheCurrentTime();
    }

    /// RTC状態が異常の場合、時計を設定し、異常状態のTAGファイルを削除する
    if (DeviceLibrary().apiBinding.getRTCReadErrorStatus() ==
        RTCReadStatus.rtcReadError) {
      DeviceLibrary().apiBinding.resetRTCReadErrorStatus();
    } else {
      /// do nothing
    }

    if (HomeModel().isHomeInitCompleted == false) {
      PagesRoute().pop();
    } else {
      final error = TpdLibrary().apiBinding.returnClockSetting();
      if (error == DeviceErrorCode.devInvalidError) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }

      final bPIFError = TpdLibrary().apiBinding.bpIFGetError();
      if (error == DeviceErrorCode.devInvalidPanelError ||
          bPIFError.errorCode != ErrCode_t.ERR_DUMMY.index) {
        return;
      }

      if (error != DeviceErrorCode.devNoError) {
        Log.e(tag: "error", description: "An unexpected error occurred");
        return;
      }

      SettingModel().isClockPopup = false;

      if (MachineConfigModel().baseMode == SettingBaseMode.utl) {
        MachineConfigModel().nextMode = SettingBaseMode.utl;
        MachineConfigModel().currentMode = SettingBaseMode.utl;
      }

      updateAllViewMode();

      /// プロジェクト投影画面を開ける
      final commonProjectorFunction =
          ref.read(commonProjectorFunctionProvider.notifier);
      commonProjectorFunction.reopenProjector().then((_) => PagesRoute().pop());
    }
  }

  ///
  /// 時計を表示するかどうか
  ///
  @override
  void onClockSwitchButtonClick() {
    state =
        state.copyWith(isSelectClockDisplayON: !state.isSelectClockDisplayON);
    DeviceLibrary()
        .apiBinding
        .setClockDisplayStatus(state.isSelectClockDisplayON);

    /// その他の画面更新
    _refreshAllFooterState();
  }

  @override
  void onYearMinusButtonClick() {
    if (!DeviceLibrary().apiBinding.getIsSetTime()) {
      _setTimeAndRefresh();
      return;
    }

    if ((ClockSettingModel.yearInputValue - _timeStep) < _yearValueMin) {
      ClockSettingModel.yearInputValue = _yearValueMax;
    } else {
      ClockSettingModel.yearInputValue--;
    }
    ClockSettingModel().changeDays(monthOrYearChange: true);
    update();
    ClockSettingModel.isLongPressValue = true;

    /// その他の画面更新
    _refreshAllFooterState();
  }

  @override
  void onYearPlusButtonClick() {
    if (!DeviceLibrary().apiBinding.getIsSetTime()) {
      _setTimeAndRefresh();
      return;
    }

    if ((ClockSettingModel.yearInputValue + _timeStep) > _yearValueMax) {
      ClockSettingModel.yearInputValue = _yearValueMin;
    } else {
      ClockSettingModel.yearInputValue++;
    }
    ClockSettingModel().changeDays(monthOrYearChange: true);
    update();
    ClockSettingModel.isLongPressValue = true;

    /// その他の画面更新
    _refreshAllFooterState();
  }

  @override
  void onMonthMinusButtonClick() {
    if (!DeviceLibrary().apiBinding.getIsSetTime()) {
      _setTimeAndRefresh();
      return;
    }

    if ((ClockSettingModel.monthInputValue - _timeStep) < _monthValueMin) {
      ClockSettingModel.monthInputValue = _monthValueMax;
    } else {
      ClockSettingModel.monthInputValue--;
    }
    ClockSettingModel().changeDays(monthOrYearChange: true);
    update();
    ClockSettingModel.isLongPressValue = true;

    /// その他の画面更新
    _refreshAllFooterState();
  }

  @override
  void onMonthPlusButtonClick() {
    if (!DeviceLibrary().apiBinding.getIsSetTime()) {
      _setTimeAndRefresh();
      return;
    }

    if ((ClockSettingModel.monthInputValue + _timeStep) > _monthValueMax) {
      ClockSettingModel.monthInputValue = _monthValueMin;
    } else {
      ClockSettingModel.monthInputValue++;
    }
    ClockSettingModel().changeDays(monthOrYearChange: true);
    update();
    ClockSettingModel.isLongPressValue = true;

    /// その他の画面更新
    _refreshAllFooterState();
  }

  @override
  void onDayMinusButtonClick() {
    if (!DeviceLibrary().apiBinding.getIsSetTime()) {
      _setTimeAndRefresh();
      return;
    }

    ClockSettingModel.dayInputValue -= _timeStep;
    ClockSettingModel().changeDays();
    update();
    ClockSettingModel.isLongPressValue = true;

    /// その他の画面更新
    _refreshAllFooterState();
  }

  @override
  void onDayPlusButtonClick() {
    if (!DeviceLibrary().apiBinding.getIsSetTime()) {
      _setTimeAndRefresh();
      return;
    }

    ClockSettingModel.dayInputValue += _timeStep;
    ClockSettingModel().changeDays();
    update();
    ClockSettingModel.isLongPressValue = true;

    /// その他の画面更新
    _refreshAllFooterState();
  }

  @override
  void onHourMinusButtonClick() {
    if (!DeviceLibrary().apiBinding.getIsSetTime()) {
      _setTimeAndRefresh();
      return;
    }

    if (state.showTimeType == ShowTimeType.hour) {
      if ((ClockSettingModel.hour24InputValue - _timeStep) < _hour24ValueMin) {
        ClockSettingModel.hour24InputValue = _hour24ValueMax;
      } else {
        ClockSettingModel.hour24InputValue--;
      }
    } else {
      if ((ClockSettingModel.hour12InputValue - _timeStep) < _hour12ValueMin) {
        ClockSettingModel.hour12InputValue = _hour12ValueMax;
      } else {
        ClockSettingModel.hour12InputValue--;
      }
    }

    update();
    ClockSettingModel.isLongPressValue = true;

    /// その他の画面更新
    _refreshAllFooterState();
  }

  @override
  void onHourPlusButtonClick() {
    if (!DeviceLibrary().apiBinding.getIsSetTime()) {
      _setTimeAndRefresh();
      return;
    }

    if (state.showTimeType == ShowTimeType.hour) {
      if ((ClockSettingModel.hour24InputValue + _timeStep) > _hour24ValueMax) {
        ClockSettingModel.hour24InputValue = _hour24ValueMin;
      } else {
        ClockSettingModel.hour24InputValue++;
      }
    } else {
      if ((ClockSettingModel.hour12InputValue + _timeStep) > _hour12ValueMax) {
        ClockSettingModel.hour12InputValue = _hour12ValueMin;
      } else {
        ClockSettingModel.hour12InputValue++;
      }
    }
    update();
    ClockSettingModel.isLongPressValue = true;

    /// その他の画面更新
    _refreshAllFooterState();
  }

  @override
  void onMinuteMinusButtonClick() {
    if (!DeviceLibrary().apiBinding.getIsSetTime()) {
      _setTimeAndRefresh();
      return;
    }

    if ((ClockSettingModel.minuteInputValue - _timeStep) < _minuteValueMin) {
      ClockSettingModel.minuteInputValue = _minuteValueMax;
    } else {
      ClockSettingModel.minuteInputValue--;
    }
    update();
    ClockSettingModel.isLongPressValue = true;

    /// その他の画面更新
    _refreshAllFooterState();
  }

  @override
  void onMinutePlusButtonClick() {
    if (!DeviceLibrary().apiBinding.getIsSetTime()) {
      _setTimeAndRefresh();
      return;
    }

    if ((ClockSettingModel.minuteInputValue + _timeStep) > _minuteValueMax) {
      ClockSettingModel.minuteInputValue = _minuteValueMin;
    } else {
      ClockSettingModel.minuteInputValue++;
    }
    update();
    ClockSettingModel.isLongPressValue = true;

    /// その他の画面更新
    _refreshAllFooterState();
  }

  @override
  void on24hButtonClick() {
    if (ClockSettingModel.showTimeInputType == ShowTimeType.hour) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 現在のフォーマットを24時間制に変換
    final hour12InputValue = ClockSettingModel.hour12InputValue;
    ClockSettingModel.hour24InputValue =
        ClockSettingModel.change12HourTo24(hour12InputValue);

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// lib更新
    DeviceLibrary().apiBinding.setShowTimeTypeIndex(ShowTimeType.hour.index);

    /// model更新
    ClockSettingModel.showTimeInputType = ShowTimeType.hour;
    update();

    /// その他の画面更新,システム時刻の更新
    onTapUp();
  }

  @override
  void onAMButtonClick() {
    if (ClockSettingModel.showTimeInputType == ShowTimeType.am) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 現在のフォーマットを12時間制に変換
    if (state.showTimeType == ShowTimeType.hour) {
      final hour24InputValue = ClockSettingModel.hour24InputValue;
      ClockSettingModel.hour12InputValue =
          ClockSettingModel.change24HourTo12(hour24InputValue);
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// lib更新
    DeviceLibrary().apiBinding.setShowTimeTypeIndex(ShowTimeType.am.index);

    /// model更新
    ClockSettingModel.showTimeInputType = ShowTimeType.am;
    update();

    /// その他の画面更新,システム時刻の更新
    onTapUp();
  }

  @override
  void onPMButtonClick() {
    if (ClockSettingModel.showTimeInputType == ShowTimeType.pm) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 現在のフォーマットを12時間制に変換
    if (state.showTimeType == ShowTimeType.hour) {
      final hour24InputValue = ClockSettingModel.hour24InputValue;
      ClockSettingModel.hour12InputValue =
          ClockSettingModel.change24HourTo12(hour24InputValue);
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// lib更新
    DeviceLibrary().apiBinding.setShowTimeTypeIndex(ShowTimeType.pm.index);

    /// model更新
    ClockSettingModel.showTimeInputType = ShowTimeType.pm;
    update();

    /// その他の画面更新,システム時刻の更新
    onTapUp();
  }

  ///
  /// すべての長押しボタンのリリース操作がトリガされる時
  ///
  @override
  void onTapUp() {
    if (!DeviceLibrary().apiBinding.getIsSetTime()) {
      return;
    }

    /// 長押しの早期解除を防止し、リスナーの早期更新を回避する。
    /// 「_setTheCurrentTime」は「ClockSettingModel.isLongPressValue」が変更される前に呼び出す必要があります。
    _setTheCurrentTime();
    ClockSettingModel.isLongPressValue = false;

    /// その他の画面更新
    _refreshAllFooterState();
  }

  ///
  /// その他の画面更新
  ///
  void _refreshAllFooterState() {
    ref
        .read(settingFooterViewModelProvider.notifier)
        .updateCommonFooterPageByChild();
  }

  ///
  /// 初期時刻を設定して更新する
  ///
  void _setTimeAndRefresh() {
    DeviceLibrary().apiBinding.setIsSetTime(true);
    update();
  }
}
