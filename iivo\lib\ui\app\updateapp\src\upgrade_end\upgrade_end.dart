import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import 'upgrade_end_view_model.dart';

class UpgradeEnd extends StatefulPage {
  const UpgradeEnd({super.key});
  @override
  PageState<UpgradeEnd> createState() => _UpgradeEndState();
}

class _UpgradeEndState extends PageState<UpgradeEnd> {
  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final state = ref.watch(upgradeUpdateEndViewModeProvider(context));
    ref.read(upgradeUpdateEndViewModeProvider(context).notifier);
    final AppLocalizations l10n = AppLocalizations.of(context)!;

    return Scaffold(
      body: Stack(
        children: [
          const pre_base_gray(),
          Column(
            children: [
              state.isLineUpdate
                  ? const Expanded(
                      flex: 30,
                      child: Row(
                        children: [
                          Spacer(flex: 100),
                          Expanded(
                            flex: 200,
                            child: Center(
                              child: grp_str_number1(
                                text: "PRODUCT LINE MODE",
                                alignment: Alignment.center,
                                textStyle: TextStyle(
                                  fontFamily: "Roboto",
                                  fontSize: 30,
                                  height: 1,
                                  color: Colors.white,
                                  backgroundColor: Colors.red,
                                ),
                              ),
                            ),
                          ),
                          Spacer(flex: 100),
                        ],
                      ),
                    )
                  : const Spacer(flex: 30),
              const Spacer(
                flex: 137,
              ),
              const Expanded(
                flex: 156,
                child: Center(
                  child: ico_mb_upgrade_l(),
                ),
              ),
              const Spacer(flex: 56),
              Expanded(
                flex: 213,
                child: Row(
                  children: [
                    const Spacer(flex: 40),
                    Expanded(
                      flex: 720,
                      child: grp_str_one(
                        text: state.isErrorTitleEmpty
                            ? l10n.upg_09
                            : state.endErrorTitle,
                      ),
                    ),
                    const Spacer(flex: 40)
                  ],
                ),
              ),
              const Spacer(flex: 84),
              state.isErrorTitleEmpty == true
                  ? Expanded(
                      flex: 167,
                      child: Row(
                        children: [
                          const Spacer(flex: 40),
                          Expanded(
                            flex: 720,
                            child: grp_str_two(
                              text: l10n.upg_19,
                            ),
                          ),
                          const Spacer(flex: 40),
                        ],
                      ))
                  : const Spacer(flex: 167),
              const Spacer(flex: 304),
              Expanded(
                flex: 45,
                child: Row(
                  children: [
                    const Spacer(flex: 420),
                    Expanded(
                        flex: 340,
                        child: grp_str_five(
                          text: "UPG VERSION : ${state.upgVersion}",
                          align: Alignment.topRight,
                        )),
                    const Spacer(flex: 40),
                  ],
                ),
              ),
              const Spacer(flex: 88),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup(WidgetRef ref) => {};
}
