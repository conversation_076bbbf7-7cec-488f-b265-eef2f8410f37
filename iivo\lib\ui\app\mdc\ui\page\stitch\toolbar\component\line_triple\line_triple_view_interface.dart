import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

part 'line_triple_view_interface.freezed.dart';

///
/// 単位
///
typedef Unit = DisplayUnit;

///
/// 白い背景に黒いテキスト
///
const TextStyle blackTextWhiteBackground =
    TextStyle(color: Colors.white, backgroundColor: Colors.black);

///
/// 黒い背景に白いテキスト
///
const TextStyle whiteTextBlackBackground =
    TextStyle(color: Colors.black, backgroundColor: Colors.white);

@freezed
class LineTripleState with _$LineTripleState {
  const factory LineTripleState({
    required bool isUnitMm,
    required String runPitchDisplayValue,
    required bool isrunPitchDefaultVlue,
    required bool isUnderSewingValeSame,
    required MDCIsOnOff underSewingState,
    required bool isUnderSewingDefaultVlue,
  }) = _LineTripleState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class LineTripleStateViewInterface extends ViewModel<LineTripleState> {
  LineTripleStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// 「下縫い」の下打ち有無は異なるの場合は"*"を表示する
  ///
  final differentUnderSewingValue = "**";

  ///
  /// ランピッチボタンをクリック
  ///
  void onRunPitchButtonClicked(BuildContext context);

  ///
  /// 下縫い設定チボタンをクリック
  ///
  void openUnderSewingSettingPopup(BuildContext context);
}
