import 'dart:async';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../global_popup/component/index.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../global_popup/global_popups/err_move_mask/err_move_mask_view_model.dart';
import '../../../../model/photo_stitch/photo_stitch_history.dart';
import '../../../../model/photo_stitch/photo_stitch_model.dart';
import '../../../../model/photo_stitch/photo_stitch_size_adjustment_model.dart';
import '../../../../model/photo_stitch/style_change_model.dart';
import '../../../page_route.dart';
import 'component/photo_frame_fit/photo_frame_popup_fit.dart';
import 'component/photo_remove_background_popup/photo_remove_background_popup.dart';
import 'component/trimming/trimming.dart';
import 'photo_size_adjustment_view_interface.dart';

final photoSizeAdjustmentViewModelProvider = StateNotifierProvider.autoDispose<
    PhotoSizeAdjustmentViewInterface,
    PhotoSizeAdjustmentState>((ref) => PhotoSizeAdjustmentViewModel());

enum PopupEnum {
  backgroundRemove,
  mask,
  frame,
}

///
/// 縦、横、縦横の指定
///
enum _MagType {
  xOnly,
  yOnly,
  xyAll,
}

class PhotoSizeAdjustmentViewModel extends PhotoSizeAdjustmentViewInterface {
  PhotoSizeAdjustmentViewModel() : super(const PhotoSizeAdjustmentState()) {
    _initial();
  }

  ///
  /// 回転角度90°
  ///
  static const int _degree90 = 90;

  ///
  /// 回転角度180°
  ///
  static const int _degree180 = 180;

  ///
  /// 移動の最大オフセット
  ///
  static const double _maxStep = 10;

  ///
  ///拡張ステップのデフォルト値は0.1です
  ///
  double _enlargeStep = 0.1;

  ///
  ///削減ステップのデフォルト値は-0.1です
  ///
  double _reduceStep = -0.1;

  ///
  ///単位歩幅
  ///
  static const double _unitStep = 0.1;

  ///
  /// 272-10x272-10枠相当で表示 (mm)
  ///
  static const int _maxImageSizeMM = 262;

  ///
  /// 10x10枠相当で表示 (mm)
  ///
  static const int _minImageSizeMM = 10;

  ///
  /// 表示用イメージエリア
  /// UI 表示領域のサイズ * イメージの最大サイズ(272mm-10mm) / 最大フレームサイズ(272mm)
  ///
  static const _uiImageAreaSizePixel = 509;

  ///
  /// 画像表示領域の幅
  ///
  late double _uiImageWidthPixel;

  ///
  /// 画像表示領域の高さ
  ///
  late double _uiImageHeightPixel;

  ///
  /// 元画像のデータ
  ///
  PhotoStitchImage get _originalImageData =>
      PhotoStitchSizeAdjustmentModel().photoStitchImage;

  ///
  /// マスク種類
  ///
  PhotoTrimmingType trimmingType = PhotoTrimmingType.square;

  ///
  /// コンテンツ画像の表示用情報
  ///
  Rect _contentImageRect = Rect.zero;

  /// 更新したのサイズ
  double _newRealWidthMM =
      PhotoStitchSizeAdjustmentModel().photoStitchImage.realWidthMM;
  double _newRealHeightMM =
      PhotoStitchSizeAdjustmentModel().photoStitchImage.realHeightMM;

  /// デフォルト値です
  final double _defaultWidthMM =
      PhotoStitchModel().photoStitchImage.realWidthMM;
  final double _defaultHeightMM =
      PhotoStitchModel().photoStitchImage.realHeightMM;

  @override
  void onBackgroundRemoveButtonClicked(BuildContext context) {
    final SizeEditType type = PhotoStitchModel().imageEditType;
    if (type == SizeEditType.mask) {
      _openMoveMaskPopup(context, SizeEditType.background);
    } else if (type == SizeEditType.background) {
      _openMoveBackgroundPopup(context, SizeEditType.background);
    } else {
      _goToRemoveBackground(context);
    }
  }

  @override
  void onFrameFitButtonClicked(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => const PhotoFrameFitPopup(),
        barrier: false,
      ),
    ).then((value) => _initial());
  }

  @override
  void onMaskButtonClicked(BuildContext context) {
    SizeEditType type = PhotoStitchModel().imageEditType;
    if (type == SizeEditType.background) {
      _openMoveBackgroundPopup(context, SizeEditType.mask);
    } else if (type == SizeEditType.mask) {
      _gotoSizeAdjustToFraming(context);
    } else {
      _goToMask(context);
    }
  }

  @override
  void update() {
    if (_uiImageWidthPixel.toInt() > _uiImageAreaSizePixel ||
        _uiImageHeightPixel.toInt() > _uiImageAreaSizePixel) {
      return;
    }

    _contentImageRect =
        Rect.fromLTWH(0, 0, _uiImageWidthPixel, _uiImageHeightPixel);

    state = state.copyWith(
      isInch: PhotoStitchSizeAdjustmentModel().isUnitMm == false,
      imageWidth: _uiImageWidthPixel,
      imageHeight: _uiImageHeightPixel,
      widthValue: PhotoStitchSizeAdjustmentModel()
          .changeValueToDisplay(_newRealWidthMM),
      heightValue: PhotoStitchSizeAdjustmentModel()
          .changeValueToDisplay(_newRealHeightMM),
      contentImage: Image.memory(
        _originalImageData.imageData,
        fit: BoxFit.contain,
        width: _originalImageData.pixelWidth,
        height: _originalImageData.pixelWidth,
        filterQuality: FilterQuality.high,
        gaplessPlayback: true,
      ),
    );
  }

  @override
  void onResetButtonClicked() {
    if (_newRealWidthMM == _defaultWidthMM &&
        _newRealHeightMM == _defaultHeightMM) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    PhotoStitchSizeAdjustmentModel().resetOriginalScalingSize();

    /// Model更新
    PhotoStitchSizeAdjustmentModel().ratioWidth = _uiImageAreaSizePixel /
        (_maxImageSizeMM * _originalImageData.dpMMWidth);
    PhotoStitchSizeAdjustmentModel().ratioHeight = _uiImageAreaSizePixel /
        (_maxImageSizeMM * _originalImageData.dpMMHeight);

    _uiImageWidthPixel = _originalImageData.pixelWidth *
        PhotoStitchSizeAdjustmentModel().ratioWidth;
    _uiImageHeightPixel = _originalImageData.pixelHeight *
        PhotoStitchSizeAdjustmentModel().ratioHeight;

    PhotoStitchModel().imageEditType = SizeEditType.none;
    _newRealWidthMM = _originalImageData.realWidthMM;
    _newRealHeightMM = _originalImageData.realHeightMM;
    PhotoStitchSizeAdjustmentModel()
        .changeTrimmingBackgroundImage(needDecodeImage: false);
    update();
  }

  @override
  bool onXEnlargeButtonClicked(bool isLongPress) {
    if (isLongPress) {
      if (_enlargeStep < _maxStep) {
        _enlargeStep = _enlargeStep + _unitStep;
      }
      if (_changeContentImageSize(_MagType.xOnly, _enlargeStep) == false) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
      return true;
    }
    _enlargeStep = _unitStep;
    if (_changeContentImageSize(_MagType.xOnly, _enlargeStep) == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    return true;
  }

  @override
  bool onXReduceButtonClicked(bool isLongPress) {
    if (isLongPress) {
      if (_reduceStep > -_maxStep) {
        _reduceStep = _reduceStep - _unitStep;
      }
      if (_changeContentImageSize(_MagType.xOnly, _reduceStep) == false) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
      return true;
    }
    _reduceStep = -_unitStep;
    if (_changeContentImageSize(_MagType.xOnly, _reduceStep) == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    return true;
  }

  @override
  bool onXYEnlargeButtonClicked(bool isLongPress) {
    if (isLongPress) {
      if (_enlargeStep < _maxStep) {
        _enlargeStep = _enlargeStep + _unitStep;
      }
      if (_changeContentImageSize(_MagType.xyAll, _enlargeStep) == false) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
      return true;
    }
    _enlargeStep = _unitStep;
    if (_changeContentImageSize(_MagType.xyAll, _enlargeStep) == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    return true;
  }

  @override
  bool onXYReduceButtonClicked(bool isLongPress) {
    if (isLongPress) {
      if (_reduceStep > -_maxStep) {
        _reduceStep = _reduceStep - _unitStep;
      }
      if (_changeContentImageSize(_MagType.xyAll, _reduceStep) == false) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
      return true;
    }
    _reduceStep = -_unitStep;
    if (_changeContentImageSize(_MagType.xyAll, _reduceStep) == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    return true;
  }

  @override
  bool onYEnlargeButtonClicked(bool isLongPress) {
    if (isLongPress) {
      if (_enlargeStep < _maxStep) {
        _enlargeStep = _enlargeStep + _unitStep;
      }
      if (_changeContentImageSize(_MagType.yOnly, _enlargeStep) == false) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
      return true;
    }
    _enlargeStep = _unitStep;
    if (_changeContentImageSize(_MagType.yOnly, _enlargeStep) == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    return true;
  }

  @override
  bool onYReduceButtonClicked(bool isLongPress) {
    if (isLongPress) {
      if (_reduceStep > -_maxStep) {
        _reduceStep = _reduceStep - _unitStep;
      }
      if (_changeContentImageSize(_MagType.yOnly, _reduceStep) == false) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
      return true;
    }
    _reduceStep = -_unitStep;
    if (_changeContentImageSize(_MagType.yOnly, _reduceStep) == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    return true;
  }

  ///
  ///時計回りに90度回転
  ///
  @override
  Future<void> onRotateRight90() async {
    PhotoStitchImage stitchImage;
    TrimmingImage trimmingImage =
        PhotoStitchSizeAdjustmentModel().trimmingImage;
    int degree = trimmingImage.clipDegree;

    if (_uiImageWidthPixel > _uiImageAreaSizePixel ||
        _uiImageHeightPixel > _uiImageAreaSizePixel) {
      degree += _degree180;
      stitchImage = await PhotoStitchSizeAdjustmentModel().rotateImage(
        _degree180,
        PhotoStitchSizeAdjustmentModel().photoStitchImage,
      );
    } else {
      degree += _degree90;
      stitchImage = await PhotoStitchSizeAdjustmentModel().rotateImage(
        _degree90,
        PhotoStitchSizeAdjustmentModel().photoStitchImage,
      );

      PhotoStitchSizeAdjustmentModel().photoStitchImage
        ..imageData = stitchImage.imageData
        ..pixelWidth = stitchImage.pixelWidth
        ..pixelHeight = stitchImage.pixelHeight
        ..dpiWidth = stitchImage.dpiWidth
        ..dpiHeight = stitchImage.dpiHeight;

      double oldUiImageWidthPixel = _uiImageWidthPixel;
      double oldUiImageHeightPixel = _uiImageHeightPixel;

      _uiImageWidthPixel = oldUiImageHeightPixel;
      _uiImageHeightPixel = oldUiImageWidthPixel;
      _newRealWidthMM =
          PhotoStitchSizeAdjustmentModel().photoStitchImage.realWidthMM;
      _newRealHeightMM =
          PhotoStitchSizeAdjustmentModel().photoStitchImage.realHeightMM;
    }
    if (degree >= 360) {
      degree = 0;
    }
    trimmingImage = trimmingImage.copyWith(clipDegree: degree);
    PhotoStitchSizeAdjustmentModel().trimmingImage = trimmingImage;
    await PhotoStitchSizeAdjustmentModel()
        .changeTrimmingBackgroundImage(needDecodeImage: true);

    update();
  }

  ///
  ///初期化する
  ///
  void _initial() {
    /// Model更新
    PhotoStitchSizeAdjustmentModel().ratioWidth = _uiImageAreaSizePixel /
        (_maxImageSizeMM * _originalImageData.dpMMWidth);
    PhotoStitchSizeAdjustmentModel().ratioHeight = _uiImageAreaSizePixel /
        (_maxImageSizeMM * _originalImageData.dpMMHeight);

    _uiImageWidthPixel = _originalImageData.pixelWidth *
        PhotoStitchSizeAdjustmentModel().ratioWidth;
    _uiImageHeightPixel = _originalImageData.pixelHeight *
        PhotoStitchSizeAdjustmentModel().ratioHeight;

    /// 更新したのサイズ
    _newRealWidthMM =
        PhotoStitchSizeAdjustmentModel().photoStitchImage.realWidthMM;
    _newRealHeightMM =
        PhotoStitchSizeAdjustmentModel().photoStitchImage.realHeightMM;

    PhotoStitchSizeAdjustmentModel()
        .changeTrimmingBackgroundImage(needDecodeImage: false);
    update();
  }

  ///
  /// 模様サイズを変更する
  ///
  bool _changeContentImageSize(_MagType type, double step) {
    /// 変更されたイメージの実際のサイズ
    double newRealWidthMM = _newRealWidthMM;
    double newRealHeightMM = _newRealHeightMM;

    /// 音響を有効にする
    bool isAcceptSound = true;
    switch (type) {
      case _MagType.xyAll:
        if (newRealWidthMM > newRealHeightMM) {
          /// 幅の最小値です
          final minWidthSize =
              _minImageSizeMM * (newRealWidthMM / newRealHeightMM);

          newRealWidthMM = _newRealWidthMM + step;

          /// 制約幅は最大値または最小値を超えません
          if (newRealWidthMM < minWidthSize) {
            isAcceptSound = false;
            newRealWidthMM = minWidthSize;
          } else if (newRealWidthMM > _maxImageSizeMM) {
            isAcceptSound = false;
            newRealWidthMM = _maxImageSizeMM.toDouble();
          } else {
            isAcceptSound = true;
          }

          /// 幅から計算して等比例の高い値です
          newRealHeightMM =
              (newRealWidthMM / _newRealWidthMM) * _newRealHeightMM;
        } else {
          /// 高い最小値です
          final minHeightSize =
              _minImageSizeMM * (newRealHeightMM / newRealWidthMM);

          newRealHeightMM = _newRealHeightMM + step;

          /// 制約は最大または最小値を超えません
          if (newRealHeightMM < minHeightSize) {
            isAcceptSound = false;
            newRealHeightMM = minHeightSize;
          } else if (newRealHeightMM > _maxImageSizeMM) {
            isAcceptSound = false;
            newRealHeightMM = _maxImageSizeMM.toDouble();
          } else {
            isAcceptSound = true;
          }

          /// 高さに比例して広い値を計算します
          newRealWidthMM =
              (newRealHeightMM / _newRealHeightMM) * _newRealWidthMM;
        }
        break;
      case _MagType.xOnly:
        newRealWidthMM = _newRealWidthMM + step;
        if (newRealWidthMM > _maxImageSizeMM) {
          newRealWidthMM = _maxImageSizeMM.toDouble();
          isAcceptSound = false;
        } else if (newRealWidthMM < _minImageSizeMM) {
          newRealWidthMM = _minImageSizeMM.toDouble();
          isAcceptSound = false;
        } else {
          isAcceptSound = true;
        }
        break;
      case _MagType.yOnly:
        newRealHeightMM = newRealHeightMM + step;
        if (newRealHeightMM > _maxImageSizeMM) {
          newRealHeightMM = _maxImageSizeMM.toDouble();
          isAcceptSound = false;
        } else if (newRealHeightMM < _minImageSizeMM) {
          newRealHeightMM = _minImageSizeMM.toDouble();
          isAcceptSound = false;
        } else {
          isAcceptSound = true;
        }
        break;
    }

    _newRealWidthMM = newRealWidthMM;
    _newRealHeightMM = newRealHeightMM;

    PhotoStitchSizeAdjustmentModel()
        .updateDpi(_newRealWidthMM, _newRealHeightMM);

    /// Model更新
    PhotoStitchSizeAdjustmentModel().ratioWidth = _uiImageAreaSizePixel /
        (_maxImageSizeMM * _originalImageData.dpMMWidth);
    PhotoStitchSizeAdjustmentModel().ratioHeight = _uiImageAreaSizePixel /
        (_maxImageSizeMM * _originalImageData.dpMMHeight);

    _uiImageWidthPixel = (_uiImageAreaSizePixel / _maxImageSizeMM) *
        double.parse(_newRealWidthMM.toStringAsFixed(1));
    _uiImageHeightPixel = (_uiImageAreaSizePixel / _maxImageSizeMM) *
        double.parse(_newRealHeightMM.toStringAsFixed(1));

    /// View更新
    update();

    if (isAcceptSound == false) {
      return false;
    } else {
      return true;
    }
  }

  ///
  /// 背景除去設定を解除しますのポップアップ
  ///
  void _openMoveBackgroundPopup(BuildContext context, SizeEditType gotoMode) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => PopupS(
          popupMessage: AppLocalizations.of(context)!.t_err_photos_03,
          buttonLeftText: AppLocalizations.of(context)!.icon_cancel,
          buttonRightText: AppLocalizations.of(context)!.icon_ok,
          rightButtonClick: () {
            PopupNavigator.pop(context: context);
            if (PhotoStitchSizeAdjustmentModel()
                .isBackgroundImageOverMaxSize()) {
              _openResetScalingPopup(context, gotoMode);
            } else {
              PhotoStitchSizeAdjustmentModel().onlyRemoveProcessResult();
              PhotoStitchModel().imageEditType = SizeEditType.none;

              if (gotoMode == SizeEditType.background) {
                _goToRemoveBackground(context);
              } else {
                _goToMask(context);
              }
            }
          },
          leftButtonClick: () => PopupNavigator.pop(context: context),
        ),
        barrier: true,
      ),
    );
  }

  ///
  /// マスクを解除しますのポップアップ
  ///
  void _openMoveMaskPopup(BuildContext context, SizeEditType gotoMode) {
    GlobalPopupRoute().updateErrorState(
      nextRoute: GlobalPopupRouteEnum.errMoveMask,
      arguments: MoveMaskArgument(
        onRightButtonClicked: (_) {
          GlobalPopupRoute().resetErrorState();
          if (PhotoStitchSizeAdjustmentModel().isFlamingImageOverMaxSize()) {
            _openResetScalingPopup(context, gotoMode);
          } else {
            PhotoStitchSizeAdjustmentModel().onlyRemoveProcessResult();
            PhotoStitchModel().imageEditType = SizeEditType.none;

            if (gotoMode == SizeEditType.background) {
              _goToRemoveBackground(context);
            }
          }
        },
      ),
    );
  }

  ///
  /// 写真刺繍背景除去Auto画面に遷移
  ///
  void _goToRemoveBackground(BuildContext context) {
    PhotoStitchHistoryModel().contentImage = Image.memory(
      PhotoStitchSizeAdjustmentModel().photoStitchImage.imageData,
      fit: BoxFit.contain,
      width: PhotoStitchSizeAdjustmentModel().photoStitchImage.pixelWidth,
      height: PhotoStitchSizeAdjustmentModel().photoStitchImage.pixelHeight,
      filterQuality: FilterQuality.high,
      gaplessPlayback: true,
    );

    PopupNavigator.pushNamed(
      context: context,
      nextRouteName: PopupEnum.backgroundRemove,
    ).then((value) => _initial());
  }

  ///
  /// 写真刺繍背景除去Auto画面に遷移
  ///
  void _goToMask(BuildContext context) {
    PopupNavigator.pushNamed(
      context: context,
      nextRouteName: PopupEnum.mask,
    ).then((value) => _initial());
  }

  ///
  /// 拡縮をリセットのポップアップ
  ///
  void _openResetScalingPopup(BuildContext context, SizeEditType gotoMode) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => PopupS(
          popupMessage: AppLocalizations.of(context)!.t_err_photos_02,
          buttonRightText: AppLocalizations.of(context)!.icon_ok,
          rightButtonClick: () {
            PopupNavigator.pop(context: context);
            PhotoStitchSizeAdjustmentModel().onlyRemoveProcessResult();
            PhotoStitchSizeAdjustmentModel().resetOriginalScalingSize();
            PhotoStitchModel().imageEditType = SizeEditType.none;

            if (gotoMode == SizeEditType.background) {
              _goToRemoveBackground(context);
            } else {
              _goToMask(context);
            }
          },
        ),
        barrier: true,
      ),
    );
  }

  ///
  /// 名前付きルートの登録
  ///
  @override
  Map<String, PopupRouteBuilder> registerNamedPopup() => {
        PopupEnum.backgroundRemove.toString(): PopupRouteBuilder(
          builder: (context) => const PhotoRemoveBackgroundPopup(),
          barrier: false,
        ),
        PopupEnum.mask.toString(): PopupRouteBuilder(
          builder: (context) => const Trimming(),
          barrier: false,
        ),
        PopupEnum.frame.toString(): PopupRouteBuilder(
          builder: (context) => const PhotoFrameFitPopup(),
          barrier: false,
        ),
      };

  @override
  Rect getContentImageRect() => _contentImageRect;

  @override
  double getContentImageRadius() {
    double radius =
        PhotoStitchSizeAdjustmentModel().trimmingImage.contentImageRadius;
    return radius;
  }

  @override
  void onNextButtonClicked() {
    StyleChangeModel().photoStitchImage
      ..imageData = _originalImageData.imageData
      ..pixelWidth = _originalImageData.pixelWidth
      ..pixelHeight = _originalImageData.pixelHeight
      ..dpiWidth = _originalImageData.dpiWidth
      ..dpiHeight = _originalImageData.dpiHeight;

    StyleChangeModel().builtInContentImageData = _originalImageData.imageData;
    StyleChangeModel().customContentImageData = _originalImageData.imageData;
    StyleChangeModel().originContentImageData = _originalImageData.imageData;
    StyleChangeModel().ratioWidth = PhotoStitchSizeAdjustmentModel().ratioWidth;
    StyleChangeModel().ratioHeight =
        PhotoStitchSizeAdjustmentModel().ratioHeight;
    StyleChangeModel().initColorUse();
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.photoStitchStyle);
  }

  @override
  void onReturnButtonClicked() {
    PhotoStitchModel().imageEditType = SizeEditType.none;
    PhotoStitchModel().isInPhotoStitchEdit = false;
    PagesRoute().pop();
  }

  ///
  /// サイズ更新
  ///
  @override
  void onTapeCancel() {
    /// 画像の実際のサイズ(inch)
    double newRealWidthInch = _newRealWidthMM / PhotoStitchModel.inchToMM;
    double newRealHeightInch = _newRealHeightMM / PhotoStitchModel.inchToMM;

    /// 画像の解像度(inch)
    double dpiWidth = _originalImageData.pixelWidth / newRealWidthInch;
    double dpiHeight = _originalImageData.pixelHeight / newRealHeightInch;

    /// Model更新
    PhotoStitchSizeAdjustmentModel()
        .changeTrimmingBackgroundImage(needDecodeImage: false);
    PhotoStitchSizeAdjustmentModel().changeImageData(
      dpiWidth: dpiWidth,
      dpiHeight: dpiHeight,
    );
  }

  ///
  /// Flaming画面へ遷移
  ///
  void _gotoSizeAdjustToFraming(BuildContext context) {
    if (PhotoStitchSizeAdjustmentModel().isFlamingImageOverMaxSize()) {
      _openResetScalingPopup(context, SizeEditType.mask);
    } else {
      _goToMask(context);
    }
  }

  @override
  bool get isCircle =>
      PhotoStitchSizeAdjustmentModel().trimmingImage.contentImageRadius ==
      PhotoStitchSizeAdjustmentModel().circleRadius;
}
