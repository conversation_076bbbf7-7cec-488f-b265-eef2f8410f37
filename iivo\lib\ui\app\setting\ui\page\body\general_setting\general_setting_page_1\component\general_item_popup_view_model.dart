import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../../model/machine_config_model.dart';
import '../../../../../../../emb/model/pattern_data_reader/category_image_reader.dart';
import '../../../../../../../emb/model/pattern_data_reader/character_font_image_reader.dart';
import '../../../../../../../emb/model/pattern_data_reader/exclusives_pattern_reader.dart';
import '../../../../../../model/general_setting_page1_model.dart';
import 'general_item_popup_view_interface.dart';

final generalItemPopupViewModelProvider = StateNotifierProvider.family
    .autoDispose<GeneralItemPopupViewInterface, GeneralItemPopupState, int>(
        (ref, clickedButtonIndex) =>
            GeneralItemPopupViewModel(clickedButtonIndex));

class GeneralItemPopupViewModel extends GeneralItemPopupViewInterface {
  GeneralItemPopupViewModel(this.clickedButtonIndex)
      : super(const GeneralItemPopupState()) {
    state = state.copyWith(
      selectIndex: _getSelectIndex(),
      buttonsState: _getButtonStateList(),
      isDefaultValue: _getIsDefaultValue(),
    );
  }
  final int clickedButtonIndex;

  ///
  /// 選択ボタンの判断する
  ///
  @override
  void onButtonClick(int index, BuildContext context) {
    if (clickedButtonIndex == ClickButtonType.language.index) {
      final language =
          GeneralSettingPage1Model.systemLanguageDisplayList()[index].value;
      GeneralSettingPage1Model().setLanguage(language);

      /// Category画像情報キャッシュを更新
      CategoryImageReader().updateCategoryImagesInfoCache();
      CharacterFontImageReader().updateCharacterFontImagesInfoCache();
      ExclusivesPatternReader().updateExclusivesImagesInfoCache();
    } else if (clickedButtonIndex == ClickButtonType.readAloudVolume.index) {
      DeviceLibrary().apiBinding.saveVoiceVolume(
          GeneralSettingPage1Model.volumeMessageList[index].value);
    } else if (clickedButtonIndex == ClickButtonType.srAloudVolume.index) {
      final level = GeneralSettingPage1Model.srVolumeMessageList[index].value;
      DeviceLibrary().apiBinding.saveSRVolume(level);
      SrSoundPlayer().setVolume(level / maxVolume);
    } else if (clickedButtonIndex == ClickButtonType.light.index) {
      DeviceLibrary().apiBinding.setLight(
          GeneralSettingPage1Model().lightMessageList.valueList[index].value);
    } else if (clickedButtonIndex ==
        ClickButtonType.screenDisplayBrightness.index) {
      MachineConfigModel().setBrightness(
          GeneralSettingPage1Model.screenDisplayBrightnessList[index].value);
    } else {
      DeviceLibrary().apiBinding.saveMachineSpeakerVolume(
          GeneralSettingPage1Model.volumeMessageList[index].value);
    }

    PopupNavigator.pop(context: context);

    /// view更新
    state = state.copyWith(selectIndex: index);
  }

  ///
  /// 自分を閉じる
  ///
  @override
  void closeSelf(BuildContext context) {
    PopupNavigator.pop(context: context);
    SystemSoundPlayer().play(SystemSoundEnum.accept);
  }

  ///
  /// 選択したの項目インデクス取得する
  ///
  int _getSelectIndex() {
    List<dynamic> dataList = [];
    dynamic saveValue;

    if (clickedButtonIndex == ClickButtonType.language.index) {
      dataList = GeneralSettingPage1Model.systemLanguageDisplayList();
      saveValue = DeviceLibrary().apiBinding.getLanguage().value;
    } else if (clickedButtonIndex == ClickButtonType.readAloudVolume.index) {
      dataList = GeneralSettingPage1Model.volumeMessageList;
      saveValue = DeviceLibrary().apiBinding.getVoiceVolume().value;
    } else if (clickedButtonIndex == ClickButtonType.srAloudVolume.index) {
      dataList = GeneralSettingPage1Model.srVolumeMessageList;
      saveValue = DeviceLibrary().apiBinding.getSRVolume().value;
    } else if (clickedButtonIndex == ClickButtonType.light.index) {
      return GeneralSettingPage1Model().getLightValueIndex();
    } else if (clickedButtonIndex ==
        ClickButtonType.screenDisplayBrightness.index) {
      dataList = GeneralSettingPage1Model.screenDisplayBrightnessList;
      saveValue = DeviceLibrary().apiBinding.getScreenDisplayBrightness().value;
    } else {
      dataList = GeneralSettingPage1Model.volumeMessageList;
      saveValue = DeviceLibrary().apiBinding.getMachineSpeakerVolume().value;
    }
    int index = 0;
    if (dataList is List<DisplayValue>) {
      index = dataList.indexWhere((element) => element.value == saveValue);
    } else {
      index = dataList.indexWhere((element) => element == saveValue);
    }
    if (index < 0) {
      index = 0;
      Log.e(
          tag: 'setting',
          description:
              'APIで異常値を取得しました,currentValue:$saveValue,dataList:$dataList ');
    }
    return index;
  }

  ///
  /// ボタンの種類の一覧を取得する
  ///
  List<ButtonState> _getButtonStateList() {
    int displayLength = 0;
    if (clickedButtonIndex == ClickButtonType.language.index) {
      displayLength =
          GeneralSettingPage1Model.systemLanguageDisplayList().length;
    } else if (clickedButtonIndex == ClickButtonType.readAloudVolume.index ||
        clickedButtonIndex == ClickButtonType.machineSpeakerVolume.index) {
      displayLength = GeneralSettingPage1Model.volumeMessageList.length;
    } else if (clickedButtonIndex == ClickButtonType.srAloudVolume.index) {
      displayLength = GeneralSettingPage1Model.srVolumeMessageList.length;
    } else if (clickedButtonIndex == ClickButtonType.light.index) {
      displayLength = GeneralSettingPage1Model().lightMessageList.listSize;
    } else if (clickedButtonIndex ==
        ClickButtonType.screenDisplayBrightness.index) {
      displayLength =
          GeneralSettingPage1Model.screenDisplayBrightnessList.length;
    } else {
      displayLength = GeneralSettingPage1Model.volumeMessageList.length;
    }

    /// [すべてのボタンの種類] を [未選択] に設定します
    List<ButtonState> buttonStateList =
        List.filled(displayLength, ButtonState.normal);

    buttonStateList[_getSelectIndex()] = ButtonState.select;

    return buttonStateList;
  }

  ///
  /// デフォルト値がないかどうかを判断します
  ///
  List<bool> _getIsDefaultValue() {
    dynamic defaultValue;
    List<dynamic> dataList = [];

    List<bool> isDefaultValue = List.filled(dataList.length, false);

    if (clickedButtonIndex == ClickButtonType.language.index) {
      dataList = GeneralSettingPage1Model.systemLanguageDisplayList();
      List<bool> isDefaultValue = List.filled(dataList.length, false);
      return isDefaultValue;
    } else if (clickedButtonIndex == ClickButtonType.readAloudVolume.index) {
      defaultValue = GeneralSettingPage1Model.messageVolumeDefault;
      dataList = GeneralSettingPage1Model.volumeMessageList;
    } else if (clickedButtonIndex ==
        ClickButtonType.machineSpeakerVolume.index) {
      defaultValue = GeneralSettingPage1Model.machineSpeakerVolumeDefault;
      dataList = GeneralSettingPage1Model.volumeMessageList;
    } else if (clickedButtonIndex == ClickButtonType.srAloudVolume.index) {
      defaultValue = GeneralSettingPage1Model.srVolumeDefault;
      dataList = GeneralSettingPage1Model.srVolumeMessageList;
    } else if (clickedButtonIndex == ClickButtonType.light.index) {
      defaultValue = GeneralSettingPage1Model().lightMessageList.defaultValue;
      for (var element
          in GeneralSettingPage1Model().lightMessageList.valueList) {
        dataList.add(element.value);
      }
    } else if (clickedButtonIndex ==
        ClickButtonType.screenDisplayBrightness.index) {
      defaultValue = GeneralSettingPage1Model.screenDisplayBrightnessDefault;
      dataList = GeneralSettingPage1Model.screenDisplayBrightnessList;
    } else {
      defaultValue = GeneralSettingPage1Model.machineSpeakerVolumeDefault;
      dataList = GeneralSettingPage1Model.volumeMessageList;
    }

    /// [すべてのボタンの種類] を [非デフォルト値です] に設定します
    isDefaultValue = List.filled(dataList.length, false);
    if (dataList is List<DisplayValue>) {
      isDefaultValue[dataList
          .indexWhere((element) => element.value == defaultValue)] = true;
    } else {
      isDefaultValue[
          dataList.indexWhere((element) => element == defaultValue)] = true;
    }

    return isDefaultValue;
  }
}
