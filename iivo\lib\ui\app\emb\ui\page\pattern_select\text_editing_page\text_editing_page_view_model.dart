import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../model/key_board_font_model.dart';
import 'bottom_bar/bottom_bar_view_model.dart';
import 'bottom_bar/component/font_array/font_array_popup_view_model.dart';
import 'bottom_bar/component/font_spacing/font_spacing_popup_view_model.dart';
import 'character_type/character_type_view_model.dart';
import 'parameter_set/parameter_set_view_model.dart';
import 'preview_area/background/background_view_model.dart';
import 'preview_area/preview_area_view_model.dart';
import 'text_editing_page_interface.dart';

enum TextEditingType {
  parameterSet,
  bottomBar,
  characterType,
  preview,
  zoomPopup,
  sizePopup,
  arrayPopup,
  spacingPopup,
  fontPopup,
}

enum TextEditingMoveType {
  beforeMove,
  moveWithKey,
  moveEnd,
}

final textEditingPageViewModelProvider = StateNotifierProvider.autoDispose<
    TextEditingPageViewInterface, TextEditingPageState>(
  (ref) => TextEditingPageViewModel(ref),
);

class TextEditingPageViewModel extends TextEditingPageViewInterface {
  TextEditingPageViewModel(Ref ref) : super(const TextEditingPageState(), ref);

  ///
  /// 子画面のView更新
  ///
  @override
  void updateTextEditingPageByChild(TextEditingType vm) {
    switch (vm) {
      case TextEditingType.parameterSet:
        if (KeyBoardFontModel().bottomPopupOpenState ==
            BottomPopupOpenState.spacing) {
          ref.read(fontSpacingPopupViewModelProvider.notifier).update();
        } else {
          /// Do noting
        }
        if (KeyBoardFontModel().bottomPopupOpenState ==
            BottomPopupOpenState.array) {
          ref.read(fontArrayPopupViewModelProvider.notifier).update();
        } else {
          /// Do noting
        }
        ref.read(characterTypeViewModelProvider.notifier).update();
        ref.read(bottomBarViewModelProvider.notifier).update();
        ref.read(previewAreaViewModelProvider.notifier).update();
        ref.read(previewBackgroundViewModelProvider.notifier).update();
        break;
      case TextEditingType.zoomPopup:
        ref.read(previewAreaViewModelProvider.notifier).update();
        ref.read(previewBackgroundViewModelProvider.notifier).update();
        break;
      case TextEditingType.bottomBar:
        ref.read(parameterSetViewModelProvider.notifier).update();
        ref.read(previewAreaViewModelProvider.notifier).update();
        break;
      case TextEditingType.characterType:
        ref.read(parameterSetViewModelProvider.notifier).update();
        ref.read(bottomBarViewModelProvider.notifier).update();
        ref.read(previewAreaViewModelProvider.notifier).update();
        break;
      case TextEditingType.preview:
        ref.read(parameterSetViewModelProvider.notifier).update();
        ref.read(characterTypeViewModelProvider.notifier).update();
        break;
      case TextEditingType.sizePopup:
        ref.read(parameterSetViewModelProvider.notifier).update();
        ref.read(previewAreaViewModelProvider.notifier).update();
        break;
      case TextEditingType.arrayPopup:
        ref.read(previewAreaViewModelProvider.notifier).update();
        break;
      case TextEditingType.spacingPopup:
        ref.read(parameterSetViewModelProvider.notifier).update();
        ref.read(previewAreaViewModelProvider.notifier).update();
        break;
      case TextEditingType.fontPopup:
        ref.read(parameterSetViewModelProvider.notifier).update();
        ref.read(characterTypeViewModelProvider.notifier).update();
        ref.read(previewAreaViewModelProvider.notifier).update();
        break;
      default:
        break;
    }
  }

  ///
  /// 移動ボタンを使用して、テキスト編集ページを更新します
  ///
  /// [bool] : 移動時にエッジに到達しているかどうか
  ///          true:エッジに移動されました
  ///          false:まだエッジに移行していません
  ///
  @override
  bool updateTextEditingPageByMoveButton(TextEditingMoveType vm) {
    switch (vm) {
      case TextEditingMoveType.beforeMove:
        ref
            .read(previewAreaViewModelProvider.notifier)
            .backupPatternDisplayInfoInMove();
        return false;
      case TextEditingMoveType.moveWithKey:
        return ref.read(previewAreaViewModelProvider.notifier).moveByArrowKey();
      case TextEditingMoveType.moveEnd:
        ref.read(previewAreaViewModelProvider.notifier).update();
        return false;
    }
  }
}
