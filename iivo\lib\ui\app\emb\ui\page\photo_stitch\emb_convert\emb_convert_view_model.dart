import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../model/thread_color_model.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../global_popup/global_popups/err_angou_not_use/err_angou_not_use_view_model.dart';
import '../../../../../../global_popup/global_popups/err_convert_emb_move_to_edit/err_convert_emb_move_to_edit_view_model.dart';
import '../../../../../../global_popup/global_popups/err_emb_too_much_selected/err_emb_too_much_selected_view_model.dart';
import '../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/photo_stitch/photo_stitch_emb_convert_model.dart';
import '../../../../model/photo_stitch/photo_stitch_model.dart';
import '../../../../model/photo_stitch/photo_stitch_size_adjustment_model.dart';
import '../../../../model/photo_stitch/style_change_model.dart';
import '../../../page_route.dart';
import '../../common_component/memory/memory.dart';
import 'color_add_popup/color_add_popup.dart';
import 'emb_convert_view_interface.dart';

void Function()? finishPhotoStitchEditFunc;

final embConvertViewModelProvider =
    StateNotifierProvider.autoDispose<EmbConvertViewInterface, EmbConvertState>(
        (ref) => EmbConvertViewModel());

class EmbConvertViewModel extends EmbConvertViewInterface {
  EmbConvertViewModel() : super(const EmbConvertState()) {
    finishPhotoStitchEditFunc = _finishPhotoStitchEditFunc;

    _init();
    Future(() {
      _showWaitingNoCancelPopup();
      EmbConvertModel().getContentImageAndInitThreadBrand().then((errorCode) {
        _convertErrorCode = errorCode;
        update();

        /// 刺繡変換失敗の場合
        if (_convertFailed) {
          if (errorCode == EmbLibraryError.EMB_MEMOVER_OK_ERR) {
            GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED,
              arguments: EmbTooMuchSelectedArgument(
                onOKButtonClicked: GlobalPopupRoute().resetErrorState,
              ),
            );
          } else {
            GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum.ERR_ANGOU_NOT_USE,
              arguments: AngouNotUseArgument(
                onOKButtonClicked: GlobalPopupRoute().resetErrorState,
              ),
            );
          }
        } else {
          _closeWaitingNoCancelPopup();
        }
      });
    });
  }

  @override
  ScrollController get scrollControllerY => _scrollControllerY;

  @override
  ScrollController get scrollControllerX => _scrollControllerX;

  @override
  void update() {
    final bool needShowPreview = EmbConvertModel().isShowPreviewButton;
    final ButtonState handState =
        _isShowPanTool ? ButtonState.select : ButtonState.normal;
    state = state.copyWith(
      numberOfColors: EmbConvertModel().numberOfColors,
      density: EmbConvertModel().density,
      colorListData: EmbConvertModel().getThreadColorList(),
      isAllPined: EmbConvertModel().isAllPined(),
      selectItemIndex: EmbConvertModel().selectIndex,
      isEnglish: DeviceLibrary().apiBinding.getLanguage().value ==
          Language.LANG_ENGLISH,
      previewImage: EmbConvertModel().previewImage,
      pinCount: EmbConvertModel().pinColorCount,
      totalEmbTime:
          EmbConvertModel().getEmbTotalTime() == EmbConvertModel.noTargetFound
              ? "----"
              : EmbConvertModel().getEmbTotalTime().toString(),
      selectedZoomScale: EmbConvertModel().selectedZoomScale,
      isZoomPopup: _zoomPopup,
      isShowColorName:
          DeviceLibrary().apiBinding.getThreadColor().value == false,
      scale: _scale,
      isShowPreview: needShowPreview,
      zoomButtonState:
          _convertFailed ? ButtonState.disable : ButtonState.normal,
      handButtonState: _convertFailed ? ButtonState.disable : handState,
      realPreviewButtonState:
          _convertFailed ? ButtonState.disable : ButtonState.normal,
      isOriginalImageVisible: _convertFailed ? true : false,
    );
  }

  @override
  void onZoomButtonClicked() {
    _zoomPopup = true;

    update();
  }

  @override
  void onNumberLeftButtonClicked() {
    _zoomPopup = false;
    if (EmbConvertModel().numberOfColors > minNumberOfColors) {
      EmbConvertModel().numberOfColors--;
      if (EmbConvertModel().isPreviewButtonShow()) {
        EmbConvertModel().updateState = CovertInfoState.change;
        EmbConvertModel().isShowPreviewButton = true;
      } else {
        EmbConvertModel().isShowPreviewButton = false;
      }
    }

    update();
  }

  @override
  void onNumberRightButtonClicked() {
    _zoomPopup = false;
    if (EmbConvertModel().numberOfColors < maxNumberOfColors) {
      EmbConvertModel().numberOfColors++;
      if (EmbConvertModel().isPreviewButtonShow()) {
        EmbConvertModel().updateState = CovertInfoState.change;
        EmbConvertModel().isShowPreviewButton = true;
      } else {
        EmbConvertModel().isShowPreviewButton = false;
      }
    }
    update();
  }

  ///
  /// NumberButtonのボタン状態を取得 Left
  ///
  @override
  ButtonState getNumberLeftButtonState() =>
      state.numberOfColors <= minNumberOfColors
          ? ButtonState.disable
          : ButtonState.normal;

  ///
  /// NumberButtonのボタン状態を取得 Right
  ///
  @override
  ButtonState getNumberRightButtonState() =>
      state.numberOfColors >= maxNumberOfColors
          ? ButtonState.disable
          : ButtonState.normal;

  @override
  String getThreadBrandName() =>
      ThreadColorModel().getThreadBrandName(EmbConvertModel().threadBrand);

  @override
  void onBrandLeftButtonClicked() {
    _zoomPopup = false;
    int index = EmbConvertModel()
        .threadBrandNameList
        .indexOf(EmbConvertModel().threadBrand);

    /// 現在のブランドが最初で、クリックすると最後のブランドに切り替わります
    if (index == 0) {
      index = EmbConvertModel().threadBrandNameList.length - 1;
    } else {
      index = index - 1;
    }

    EmbConvertModel().threadBrand =
        EmbConvertModel().threadBrandNameList[index];
    if (EmbConvertModel().isPreviewButtonShow()) {
      EmbConvertModel().updateState = CovertInfoState.change;
      EmbConvertModel().isShowPreviewButton = true;
    } else {
      EmbConvertModel().isShowPreviewButton = false;
    }

    update();
  }

  @override
  void onBrandRightButtonClicked() {
    _zoomPopup = false;
    int index = EmbConvertModel()
        .threadBrandNameList
        .indexOf(EmbConvertModel().threadBrand);

    /// 現在のブランドが最後で、クリックして最初のブランドを切り替えます
    if (index == EmbConvertModel().threadBrandNameList.length - 1) {
      index = 0;
    } else {
      index = index + 1;
    }

    EmbConvertModel().threadBrand =
        EmbConvertModel().threadBrandNameList[index];
    if (EmbConvertModel().isPreviewButtonShow()) {
      EmbConvertModel().updateState = CovertInfoState.change;
      EmbConvertModel().isShowPreviewButton = true;
    } else {
      EmbConvertModel().isShowPreviewButton = false;
    }

    update();
  }

  @override
  void onDensityLeftButtonClicked() {
    _zoomPopup = false;
    int density = EmbConvertModel().density - 1;
    if (density >= minDensity) {
      EmbConvertModel().density = density;
      if (EmbConvertModel().isPreviewButtonShow()) {
        EmbConvertModel().updateState = CovertInfoState.change;
        EmbConvertModel().isShowPreviewButton = true;
      } else {
        EmbConvertModel().isShowPreviewButton = false;
      }
    }
    update();
  }

  @override
  void onDensityRightButtonClicked() {
    _zoomPopup = false;
    int density = EmbConvertModel().density + 1;
    if (density <= maxDensity) {
      EmbConvertModel().density = density;
      if (EmbConvertModel().isPreviewButtonShow()) {
        EmbConvertModel().updateState = CovertInfoState.change;
        EmbConvertModel().isShowPreviewButton = true;
      } else {
        EmbConvertModel().isShowPreviewButton = false;
      }
    }
    update();
  }

  ///
  /// DensityButtonのボタン状態を取得 Left
  ///
  @override
  ButtonState getDensityLeftButtonState() =>
      state.density <= minDensity ? ButtonState.disable : ButtonState.normal;

  ///
  /// DensityButtonのボタン状態を取得 Right
  ///
  @override
  ButtonState getDensityRightButtonState() =>
      state.density >= maxDensity ? ButtonState.disable : ButtonState.normal;

  @override
  void onItemClicked(int index) {
    _zoomPopup = false;
    EmbConvertModel().selectIndex = index;
    update();
  }

  @override
  void onItemPinButtonClicked(int index) {
    _zoomPopup = false;
    EmbConvertModel().switchItemPin(index);
    EmbConvertModel().updateState = CovertInfoState.change;
    EmbConvertModel().isShowPreviewButton = true;

    update();
  }

  @override
  void onAddPinButtonClicked(BuildContext context) {
    _zoomPopup = false;
    state = state.copyWith(isZoomPopup: _zoomPopup);
    if (EmbConvertModel().getThreadColorList().length < maxNumberOfColors &&
        EmbConvertModel().pinColorCount < EmbConvertModel().numberOfColors) {
      PopupNavigator.push(
        context: context,
        popup: PopupRouteBuilder(
          builder: (c) => const ColorAddPopup(),
        ),
      ).then((value) {
        if (value != null) {
          EmbConvertModel().addItem();
          if (EmbConvertModel().isPreviewButtonShow()) {
            EmbConvertModel().updateState = CovertInfoState.change;
            EmbConvertModel().isShowPreviewButton = true;
          } else {
            EmbConvertModel().isShowPreviewButton = false;
          }
          update();
        }
        return;
      });
    }
  }

  @override
  void onAllPinButtonClicked() {
    _zoomPopup = false;
    EmbConvertModel().switchAllItemPin();
    EmbConvertModel().updateState = CovertInfoState.change;
    EmbConvertModel().isShowPreviewButton = true;
    update();
  }

  @override
  void onRemovePinButtonClicked() {
    _zoomPopup = false;
    if (EmbConvertModel().getThreadColorList().length <= 1) {
      return;
    }
    EmbConvertModel().removeItem();
    if (EmbConvertModel().isPreviewButtonShow()) {
      EmbConvertModel().updateState = CovertInfoState.change;
      EmbConvertModel().isShowPreviewButton = true;
    } else {
      EmbConvertModel().isShowPreviewButton = false;
    }

    update();
  }

  @override
  void onReturnButtonClicked() {
    _zoomPopup = false;
    EmbConvertModel().reset();
    _finishPhotoStitchEditFunc();
    PagesRoute().pop();
  }

  @override
  void onMemoryButtonClicked(BuildContext context) {
    if (_convertFailed) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    _zoomPopup = false;
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => Memory(
          onCancelClick: () => PopupNavigator.pop(context: context),
        ),
      ),
    );
  }

  @override
  void onOKButtonClicked() {
    if (_convertFailed) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    _zoomPopup = false;
    state = state.copyWith(isZoomPopup: _zoomPopup);

    GlobalPopupRoute().updateErrorState(
      nextRoute: GlobalPopupRouteEnum.errConvertEmbMoveToEdit,
      arguments: ConvertEmbMoveToEditArgument(
        onLeftButtonClicked: GlobalPopupRoute().resetErrorState,
        onRightButtonClicked: () {
          GlobalPopupRoute().resetErrorState();
          EmbPhotoStitchLibrary().apiBinding.finishPhotoStitchEdit(false);

          EmbLibraryError error = EmbLibrary().apiBinding.embGotoEdit();
          if (error != EmbLibraryError.EMB_NO_ERR) {
            return;
          }
          final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
          if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
            return;
          }
          final undoRedoError = EmbConvertModel().setHandleToEmb();
          if (undoRedoError != EmbLibraryError.EMB_NO_ERR) {
            GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
              arguments: TroubleOccoredPowerOffArgument(
                onOKButtonClicked: GlobalPopupRoute().resetErrorState,
              ),
            );
            return;
          }

          PhotoStitchModel().reset();
          PhotoStitchSizeAdjustmentModel().reset();
          StyleChangeModel().reset();
          EmbConvertModel().reset();

          PagesRoute().pushNamedAndRemoveUntil(
            nextRoute: PageRouteEnum.patternEdit,
            untilRoute: PageRouteEnum.home,
          );

          PhotoStitchModel().actionAfterExitingPhotoStitch();
        },
      ),
    );
  }

  @override
  void onPreviewButtonClicked(BuildContext context) {
    _zoomPopup = false;
    state = state.copyWith(isZoomPopup: _zoomPopup);

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    _showWaitingNoCancelPopup();

    EmbConvertModel().setEmbConvert().then((errorCode) {
      if (!mounted) {
        return;
      }
      _closeWaitingNoCancelPopup();

      _convertErrorCode = errorCode;
      if (errorCode == EmbLibraryError.EMB_NO_ERR) {
        EmbConvertModel().isShowPreviewButton = false;

        ///view更新
        update();
        return;
      }

      _isShowPanTool = false;
      _scale = _magnificationRatio_100;
      _moveToCenter(state.scale, _scale);
      EmbConvertModel().isShowPreviewButton = true;

      if (_scrollControllerX.hasClients) {
        _scrollControllerX.jumpTo(0);
      }
      if (_scrollControllerY.hasClients) {
        _scrollControllerY.jumpTo(0);
      }

      ///view更新
      update();

      ///
      /// エラー表示
      /// ERR_EMB_TOO_MUCH_SELECTED：
      /// -- EMB_MEMOVER_OK_ERR：メモリオーバーエラー
      /// ERR_ANGOU_NOT_USE：
      /// -- EMB_PATTERNHANDLE_ERR：パターンハンドルエラー
      /// -- EMB_NEWPATTERN_ERR：新パターン作成エラー
      /// -- EMB_INVALIDVALUE_ERR：入力値エラー
      /// -- EMB_THREADBRAND_ERR：糸ブランドエラー
      ///
      if (errorCode == EmbLibraryError.EMB_MEMOVER_OK_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED,
          arguments: EmbTooMuchSelectedArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
      } else {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_ANGOU_NOT_USE,
          arguments: AngouNotUseArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    EmbConvertModel().reset();
    finishPhotoStitchEditFunc = null;
    _scrollControllerX.dispose();
    _scrollControllerY.dispose();
  }

  @override
  void onZoomPopupCloseButtonClicked() {
    _zoomPopup = false;
    update();
  }

  @override
  void onZoomPopupScaleButtonClicked(int scale) {
    _zoomPopup = false;

    /// Model更新
    EmbConvertModel().selectedZoomScale = scale;

    double preScale = state.scale;
    switch (scale) {
      case _magnification_100:
        _scale = _magnificationRatio_100;
        break;
      case _magnification_125:
        _scale = _magnificationRatio_125;
        break;
      case _magnification_150:
        _scale = _magnificationRatio_150;
        break;
      case _magnification_200:
        _scale = _magnificationRatio_200;
        break;
      case _magnification_400:
        _scale = _magnificationRatio_400;
        break;
      default:
        _scale = _magnificationRatio_100;
    }

    _moveToCenter(preScale, _scale);
    update();
  }

  @override
  bool get isDisablePanTool => _scale == _magnificationRatio_100 ? true : false;
  @override
  void onPanToolClicked() {
    _zoomPopup = false;
    _isShowPanTool = !_isShowPanTool;
    update();
  }

  ///
  /// オンスケール更新
  ///
  @override
  void onScaleUpdate(ScaleUpdateDetails details) {
    if (state.handButtonState != ButtonState.select) {
      return;
    }

    double offsetXMax = scrollControllerX.position.maxScrollExtent;
    double offsetYMax = scrollControllerY.position.maxScrollExtent;

    double offsetX = scrollControllerX.offset - details.focalPointDelta.dx;
    double offsetY = scrollControllerY.offset - details.focalPointDelta.dy;
    offsetX = offsetX.clamp(0, offsetXMax);
    offsetY = offsetY.clamp(0, offsetYMax);

    _scrollControllerX.jumpTo(offsetX);
    _scrollControllerY.jumpTo(offsetY);

    if (details.pointerCount > 1) {
      _towFingerMagnification(details);
    }
  }

  ///
  ///2 本指による拡大
  ///
  void _towFingerMagnification(ScaleUpdateDetails details) {
    if (details.scale > 1) {
      //拡大
      _scale = _scale + _enlargeSpeed;
    } else if (details.scale < 1) {
      //縮む
      _scale = _scale - _enlargeSpeed;
    }

    if (_scale <= _magnificationRatio_100) {
      _scale = _magnificationRatio_100;
      EmbConvertModel().selectedZoomScale = _magnification_100;
    } else if (_scale > _magnificationRatio_100 &&
        _scale < _magnificationRatio_125) {
      EmbConvertModel().selectedZoomScale = _magnification_100;
    } else if (_scale >= _magnificationRatio_125 &&
        _scale < _magnificationRatio_150) {
      EmbConvertModel().selectedZoomScale = _magnification_125;
    } else if (_scale >= _magnificationRatio_150 &&
        _scale < _magnificationRatio_200) {
      EmbConvertModel().selectedZoomScale = _magnification_150;
    } else if (_scale >= _magnificationRatio_200 &&
        _scale < _magnificationRatio_400) {
      EmbConvertModel().selectedZoomScale = _magnification_200;
    } else if (_scale >= _magnificationRatio_400) {
      _scale = _magnificationRatio_400;
      EmbConvertModel().selectedZoomScale = _magnification_400;
    }

    update();
  }

  @override
  bool isAddButtonAvailable() =>
      EmbConvertModel().getThreadColorList().length < maxNumberOfColors &&
      EmbConvertModel().pinColorCount < EmbConvertModel().numberOfColors;

  @override
  void onRealPreviewButtonClicked() {
    _zoomPopup = false;

    /// Model更新
    EmbConvertModel().embPhotoStitchExit();
    PatternModel().realPreviewDisplayType = RealPreviewDisplayType.patternEdit;

    /// View更新
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.embRealPreview);
  }

  ///
  /// 虫眼鏡による拡大画像iconList
  ///
  final List<Widget> _zoomIconList = [
    const ico_zoom100(),
    const ico_zoom125(),
    const ico_zoom150(),
    const ico_zoom200(),
    const ico_zoom400(),
  ];

  ///
  /// 選択されているものを取得します index
  ///
  @override
  Widget getSelectedZoomIcon() {
    for (int i = 0; i < zoomValueList.length; i++) {
      if (state.selectedZoomScale == zoomValueList[i]) {
        return _zoomIconList[i];
      }
    }
    return Container();
  }

  @override
  double get imageWidth => _getDisplayImageSize().dx;

  @override
  double get imageHeight => _getDisplayImageSize().dy;

  ///
  /// 画像をプレビューエリアの中間に表示する
  ///
  void _moveToCenter(double preScale, double currentScale) {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (EmbConvertModel().selectedZoomScale == _magnification_100) {
        _scrollControllerX.jumpTo(0);
        _scrollControllerY.jumpTo(0);
      } else {
        if (preScale != currentScale) {
          double offsetX = _scrollControllerX.position.maxScrollExtent / 2;
          double offsetY = _scrollControllerY.position.maxScrollExtent / 2;
          _scrollControllerX.jumpTo(offsetX);
          _scrollControllerY.jumpTo(offsetY);
        }
      }
    });
  }

  ///
  /// 待機中のポップアップ
  ///
  void _showWaitingNoCancelPopup() => GlobalPopupRoute()
      .updateErrorState(nextRoute: GlobalPopupRouteEnum.picturePlayPleaseWait);

  void _closeWaitingNoCancelPopup() => GlobalPopupRoute().resetErrorState();

  void _finishPhotoStitchEditFunc() {
    EmbPhotoStitchLibrary().apiBinding.finishPhotoStitchEdit(true);
  }

  ///
  /// 画面初期化
  ///
  void _init() {
    EmbConvertModel().isShowPreviewButton = false;
    state = state.copyWith(
      scrollController: ScrollController(),
      numberOfColors: EmbConvertModel.defaultNumberOfColors,
      density: EmbConvertModel.defaultDensity,
      colorListData: [],
      isAllPined: false,
      selectItemIndex: EmbConvertModel.noTargetFound,
      isEnglish: DeviceLibrary().apiBinding.getLanguage().value ==
          Language.LANG_ENGLISH,
      previewImage: EmbConvertModel().photoStitchImage.imageData,
      pinCount: 0,
      totalEmbTime: "----",
      selectedZoomScale: EmbConvertModel.defaultZoomScale,
      isZoomPopup: false,
      isShowColorName:
          DeviceLibrary().apiBinding.getThreadColor().value == false,
      scale: _magnificationRatio_100,
      isShowPreview: false,
      zoomButtonState: ButtonState.normal,
      handButtonState: ButtonState.normal,
      realPreviewButtonState: ButtonState.disable,
    );
  }

  ///
  /// 刺绣画像変換ページの最大UI表示区域を取得
  ///
  Offset _getDisplayImageSize() {
    final PhotoStitchImage photoStitchImage =
        EmbConvertModel().photoStitchImage;
    Offset size = Offset.zero;
    final double ratio =
        photoStitchImage.realWidthMM / photoStitchImage.realHeightMM;
    if (photoStitchImage.realWidthMM > photoStitchImage.realHeightMM) {
      size = Offset(
        maxDisplayImagePixelWidth,
        maxDisplayImagePixelWidth / ratio,
      );
    } else {
      size = Offset(
        maxDisplayImagePixelHeight * ratio,
        maxDisplayImagePixelHeight,
      );
    }

    return size;
  }

  /// ポップアップウィンドウを表示するかどうかズームするかどうか
  bool _zoomPopup = false;

  /// ひれい
  double _scale = 1;

  ///ハンドボタンを表示するかどうか
  bool _isShowPanTool = false;

  /// 100%
  static const int _magnification_100 = 100;

  /// 125%
  static const int _magnification_125 = 125;

  /// 150%
  static const int _magnification_150 = 150;

  /// 200%
  static const int _magnification_200 = 200;

  /// 400%
  static const int _magnification_400 = 400;

  /// 100%图像は倍率を示しています
  static const double _magnificationRatio_100 = 1;

  /// 125%图像は倍率を示しています
  static const double _magnificationRatio_125 = _magnificationRatio_100 * 1.25;

  /// 150%图像は倍率を示しています
  static const double _magnificationRatio_150 = _magnificationRatio_100 * 1.5;

  /// 200%图像は倍率を示しています
  static const double _magnificationRatio_200 = _magnificationRatio_100 * 2;

  /// 400%图像は倍率を示しています
  static const double _magnificationRatio_400 = _magnificationRatio_100 * 4;

  ///
  ///増幅率
  ///
  static const double _enlargeSpeed = 0.005;

  ///
  /// Y方向のスクロールコントローラ
  ///
  final ScrollController _scrollControllerY = ScrollController();

  ///
  /// X方向のスクロールコントローラ
  ///
  final ScrollController _scrollControllerX = ScrollController();

  ///
  /// 刺繍データ変換エラー
  ///
  EmbLibraryError _convertErrorCode = EmbLibraryError.EMB_NO_ERR;
  bool get _convertFailed => _convertErrorCode != EmbLibraryError.EMB_NO_ERR;
}
