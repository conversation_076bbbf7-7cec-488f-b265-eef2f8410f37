import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'line_e_stitch_thickness_popup_view_interface.freezed.dart';

@freezed
class LineEStitchThicknessState with _$LineEStitchThicknessState {
  const factory LineEStitchThicknessState({
    required String thicknessDisplayValue,
    required bool isDefaultStyle,
    required bool plusButtonValid,
    required bool minusButtonValid,
  }) = _LineEStitchThicknessState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class LineEStitchThicknessStateViewInterface
    extends ViewModel<LineEStitchThicknessState> {
  LineEStitchThicknessStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress);

  ///
  /// 最大幅値
  ///
  int get maxThicknessValue;

  ///
  /// 最小幅値
  ///
  int get minThicknessValue;
}
