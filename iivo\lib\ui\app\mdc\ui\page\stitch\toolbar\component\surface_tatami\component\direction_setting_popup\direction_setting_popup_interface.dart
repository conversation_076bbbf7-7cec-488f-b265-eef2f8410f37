import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

part 'direction_setting_popup_interface.freezed.dart';

@freezed
class DirectionSettingPopupState with _$DirectionSettingPopupState {
  const factory DirectionSettingPopupState({
    @Default("45") String directionValue,
    @Default(MDCTatamiDirKind.mdc_tatami_dir_kind_auto)
    MDCTatamiDirKind autoButtonState,
    @Default(false) bool isMaxValue,
    @Default(false) bool isMainValue,
  }) = _DirectionSettingPopupState;
}

abstract class DirectionSettingPopupViewInterface
    extends ViewModel<DirectionSettingPopupState> {
  DirectionSettingPopupViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// 自動ボタンをクリックする
  ///
  void onAutoButtonClicked();

  ///
  /// マニュアル設定ボタンをクリックする
  ///
  void onManualButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress);

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// 画面表示Style Colorを取得する
  ///
  bool getDirectionTextStyle();
}
