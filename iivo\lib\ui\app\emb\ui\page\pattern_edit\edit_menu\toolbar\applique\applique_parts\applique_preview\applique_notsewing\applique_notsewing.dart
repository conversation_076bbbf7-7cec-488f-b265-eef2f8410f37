import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../common_component/preview_custom_painter.dart'
    show MaskPainter;
import 'applique_notsewing_view_model.dart';

class AppliqueNotSewingPopup extends ConsumerStatefulWidget {
  const AppliqueNotSewingPopup({super.key});

  @override
  ConsumerState<AppliqueNotSewingPopup> createState() =>
      _AppliqueNotSewingPopupState();
}

class _AppliqueNotSewingPopupState
    extends ConsumerState<AppliqueNotSewingPopup> {
  @override
  void initState() {
    super.initState();

    ref.read(notSewingViewInfoProvider.notifier).context = context;
  }

  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    ref.watch(notSewingViewInfoProvider);
    final viewModel = ref.read(appliqueNotSewingViewModelProvider.notifier);
    final state = ref.watch(appliqueNotSewingViewModelProvider);
    return Column(
      children: [
        const Spacer(flex: 71),
        Expanded(
          flex: 1148,
          child: Material(
            color: Colors.transparent,
            child: Column(
              children: [
                const Spacer(flex: 8),
                Expanded(
                  flex: 1132,
                  child: Row(
                    children: [
                      const Spacer(flex: 6),
                      Expanded(
                        flex: 788,
                        child: Stack(
                          children: [
                            const pic_popup_size788x1132(),
                            Column(
                              children: [
                                const Spacer(flex: 80),
                                Expanded(
                                  flex: 880,
                                  child: Row(
                                    children: [
                                      Expanded(
                                        flex: 564,
                                        child: pic_embroidery_mainpreview(
                                          child: Row(
                                            children: [
                                              const Spacer(flex: 1),
                                              Expanded(
                                                flex: 562,
                                                child: Stack(
                                                  children: [
                                                    /// 画像
                                                    state.partsImage ??
                                                        Container(),

                                                    /// すべてのpatternを生成
                                                    ...() {
                                                      List<Widget>
                                                          patternWidget = [];

                                                      state
                                                          .patternDisplayInfoList
                                                          .asMap()
                                                          .entries
                                                          .forEach((pattern) {
                                                        PatternViewDisplayInfo
                                                            patternInfo =
                                                            pattern.value;
                                                        patternWidget.add(
                                                          Positioned(
                                                            top:
                                                                patternInfo.top,
                                                            left: patternInfo
                                                                .left,
                                                            width: patternInfo
                                                                .width,
                                                            height: patternInfo
                                                                .height,
                                                            child: Stack(
                                                              children: [
                                                                /// すべてのborderを生成
                                                                ...() {
                                                                  List<Widget>
                                                                      borderWidget =
                                                                      [];
                                                                  patternInfo
                                                                      .borderDisplayInfoList
                                                                      .asMap()
                                                                      .entries
                                                                      .forEach(
                                                                          (border) {
                                                                    EmbBorderViewDisplayInfo
                                                                        borderInfo =
                                                                        border
                                                                            .value;
                                                                    borderWidget
                                                                        .add(
                                                                      Positioned(
                                                                        top: borderInfo
                                                                            .top,
                                                                        left: borderInfo
                                                                            .left,
                                                                        width: borderInfo
                                                                            .width,
                                                                        height:
                                                                            borderInfo.height,
                                                                        child:
                                                                            Stack(
                                                                          alignment:
                                                                              Alignment.center,
                                                                          children: [
                                                                            /// すべてのgroupを生成
                                                                            ...() {
                                                                              List<Widget> groupWidget = [];
                                                                              borderInfo.groupDisplayInfoList.asMap().entries.forEach((group) {
                                                                                EmbGroupViewDisplayInfo groupInfo = group.value;

                                                                                groupWidget.add(
                                                                                  /// patternの表示領域(Maskと赤いドットを含む)
                                                                                  Positioned(
                                                                                    top: groupInfo.top,
                                                                                    left: groupInfo.left,
                                                                                    width: groupInfo.width,
                                                                                    height: groupInfo.height,

                                                                                    /// group
                                                                                    child: Stack(
                                                                                      children: [
                                                                                        /// すべてのEmbPatternを生成
                                                                                        ...() {
                                                                                          List<Widget> widget = [];
                                                                                          groupInfo.embPatternDisplayInfoList.asMap().entries.forEach((embPattern) {
                                                                                            EmbPatternViewDisplayInfo embPatternInfo = embPattern.value;

                                                                                            widget.add(
                                                                                              Positioned(
                                                                                                top: embPatternInfo.top,
                                                                                                left: embPatternInfo.left,
                                                                                                width: embPatternInfo.width,
                                                                                                height: embPatternInfo.height,
                                                                                                child:

                                                                                                    /// 縫ってないの赤い点線のボックス
                                                                                                    groupInfo.isAllNotSewing == false
                                                                                                        ? Container()
                                                                                                        : IgnorePointer(
                                                                                                            child: CustomPaint(
                                                                                                              painter: MaskPainter(
                                                                                                                isDashedLine: true,
                                                                                                                maskColor: const Color.fromARGB(255, 235, 0, 0),
                                                                                                                strokeWidth: 1,
                                                                                                                maskTopLeft: embPatternInfo.mask.topLeft,
                                                                                                                maskTopRight: embPatternInfo.mask.topRight,
                                                                                                                maskBottomLeft: embPatternInfo.mask.bottomLeft,
                                                                                                                maskBottomRight: embPatternInfo.mask.bottomRight,
                                                                                                              ),
                                                                                                            ),
                                                                                                          ),
                                                                                              ),
                                                                                            );
                                                                                          });
                                                                                          return widget;
                                                                                        }(),
                                                                                      ],
                                                                                    ),
                                                                                  ),
                                                                                );
                                                                              });
                                                                              return groupWidget;
                                                                            }(),
                                                                          ],
                                                                        ),
                                                                      ),
                                                                    );
                                                                  });

                                                                  return borderWidget;
                                                                }(),
                                                              ],
                                                            ),
                                                          ),
                                                        );
                                                      });

                                                      /// 文字の円弧下線イメージ
                                                      state
                                                          .patternDisplayInfoList
                                                          .asMap()
                                                          .entries
                                                          .forEach((pattern) {
                                                        PatternViewDisplayInfo
                                                            patternInfo =
                                                            pattern.value;

                                                        patternWidget.add(
                                                          IgnorePointer(
                                                            ignoring: true,
                                                            child: Stack(
                                                              children: [
                                                                /// すべてのBorderを生成
                                                                ...() {
                                                                  List<Widget>
                                                                      borderWidget =
                                                                      [];
                                                                  patternInfo
                                                                      .borderDisplayInfoList
                                                                      .asMap()
                                                                      .entries
                                                                      .forEach(
                                                                    (border) {
                                                                      EmbBorderViewDisplayInfo
                                                                          borderInfo =
                                                                          border
                                                                              .value;
                                                                      borderWidget
                                                                          .add(
                                                                        Stack(
                                                                            alignment:
                                                                                Alignment.center,
                                                                            children: [
                                                                              /// すべてのgroupを生成
                                                                              ...() {
                                                                                List<Widget> groupWidget = [];
                                                                                borderInfo.groupDisplayInfoList.asMap().entries.forEach((group) {
                                                                                  EmbGroupViewDisplayInfo groupInfo = group.value;

                                                                                  /// 文字の円弧下線イメージ
                                                                                  if (groupInfo.arcImage != null) {
                                                                                    groupWidget.add(
                                                                                      Image.memory(
                                                                                        groupInfo.arcImage!,
                                                                                        fit: BoxFit.cover,
                                                                                        alignment: Alignment.center,
                                                                                        gaplessPlayback: false,
                                                                                      ),
                                                                                    );
                                                                                  } else {
                                                                                    /// do nothing
                                                                                  }
                                                                                });

                                                                                return groupWidget;
                                                                              }(),
                                                                            ]),
                                                                      );
                                                                    },
                                                                  );

                                                                  return borderWidget;
                                                                }(),
                                                              ],
                                                            ),
                                                          ),
                                                        );
                                                      });

                                                      return patternWidget;
                                                    }(),
                                                  ],
                                                ),
                                              ),
                                              const Spacer(flex: 1)
                                            ],
                                          ),
                                        ),
                                      ),
                                      const Spacer(flex: 12),
                                      Expanded(
                                        flex: 212,
                                        child: Column(
                                          children: [
                                            Expanded(
                                              flex: 186,
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    flex: 205,
                                                    child: Stack(
                                                      children: [
                                                        const pic_emb_thread_contents_preview(),
                                                        Column(
                                                          children: [
                                                            const Spacer(
                                                                flex: 1),
                                                            Expanded(
                                                              flex: 184,
                                                              child: Center(
                                                                child: state
                                                                        .thumbnailImage ??
                                                                    Container(),
                                                              ),
                                                            ),
                                                            const Spacer(
                                                                flex: 1),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  const Spacer(flex: 7),
                                                ],
                                              ),
                                            ),
                                            const Spacer(flex: 8),
                                            Expanded(
                                              flex: 686,
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                      flex: 194,
                                                      child:
                                                          grp_lst_colorthread(
                                                        controller: state
                                                            .scrollController,
                                                        displayList:
                                                            state.displayList,
                                                        onColorInfoClick: viewModel
                                                            .onColorItemClicked,
                                                      )),
                                                  const Spacer(flex: 5),
                                                  Expanded(
                                                    flex: 8,
                                                    child: CustomScrollbar(
                                                      key: const Key(
                                                          "Applique_NotSewing_CustomScrollbar"),
                                                      controller: state
                                                          .scrollController,
                                                      visibilityWhenScrollFull:
                                                          false,
                                                    ),
                                                  ),
                                                  const Spacer(flex: 5),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const Spacer(flex: 80),
                                Expanded(
                                  flex: 80,
                                  child: Row(
                                    children: [
                                      const Spacer(flex: 12),
                                      Expanded(
                                        flex: 152,
                                        child: grp_btn_negative(
                                          text: l10n.icon_return,
                                          onTap: () => viewModel
                                              .onReturnButtonClick(context),
                                        ),
                                      ),
                                      const Spacer(flex: 188),
                                      Expanded(
                                        flex: 84,
                                        child: grp_btn_applique_notsewing(
                                          state: state.isNotSewingEnable
                                              ? ButtonState.select
                                              : ButtonState.normal,
                                          onTap:
                                              viewModel.onNotSewingButtonClick,
                                        ),
                                      ),
                                      const Spacer(flex: 188),
                                      Expanded(
                                        flex: 152,
                                        child: grp_btn_positive(
                                          text: l10n.icon_ok,
                                          onTap: () => viewModel
                                              .onOKButtonClick(context),
                                        ),
                                      ),
                                      const Spacer(flex: 12),
                                    ],
                                  ),
                                ),
                                const Spacer(flex: 12),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const Spacer(flex: 6),
                    ],
                  ),
                ),
                const Spacer(flex: 8),
              ],
            ),
          ),
        ),
        const Spacer(flex: 61),
      ],
    );
  }
}
