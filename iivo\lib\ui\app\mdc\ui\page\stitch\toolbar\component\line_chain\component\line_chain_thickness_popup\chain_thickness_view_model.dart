import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_chain_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'chain_thickness_view_interface.dart';

final chainThicknessViewModelProvider = StateNotifierProvider.autoDispose<
    ChainThicknessStateViewInterface,
    ChainThicknessState>((ref) => ChainThicknessViewModel(ref));

class ChainThicknessViewModel extends ChainThicknessStateViewInterface {
  ChainThicknessViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const ChainThicknessState(
              isDefaultStyle: false,
              thicknessInputValue: "",
              minusButtonValid: false,
              plusButtonValid: false,
            ),
            ref) {
    update();
  }

  ///
  /// 厚さ表示星
  ///
  bool _isThicknessValueDisplayStar =
      LineChainModel().getThickness() != LineChainModel.thicknessNotUpdating
          ? false
          : true;

  ///
  /// 厚さ値
  ///
  int _thicknessValue = LineChainModel().getThickness();

  @override
  void update() {
    state = state.copyWith(
      thicknessInputValue: _getThicknessDisplayValue(),
      isDefaultStyle: _isDefaultStyle(),
      minusButtonValid: _getMinusButtonState(),
      plusButtonValid: _getPlusButtonState(),
    );
  }

  @override
  int get defaultValue => LineChainModel().thicknessDefaultValue;

  @override
  bool plusLineChainSize(bool isLongPress) {
    if (_isThicknessValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isThicknessValueDisplayStar = false;

      ///  Model 更新
      _thicknessValue = LineChainModel().thicknessDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_thicknessValue >= LineChainModel.maxiThicknessValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    _thicknessValue++;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  bool miniLineChainSize(bool isLongPress) {
    if (_isThicknessValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isThicknessValueDisplayStar = false;

      ///  Model 更新
      _thicknessValue = LineChainModel().thicknessDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_thicknessValue <= LineChainModel.miniThicknessValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    _thicknessValue--;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineChainThickness.toString());
    if (_isThicknessValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int thicknessValue = LineChainModel().getThickness();

    /// Model 更新
    LineChainModel().setThickness(_thicknessValue);
    if (LineChainModel().setMdcChainStitchSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (thicknessValue != _thicknessValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// 厚度表示値を取得します
  ///
  String _getThicknessDisplayValue() {
    if (_isThicknessValueDisplayStar) {
      return "*";
    }
    return _thicknessValue.toString();
  }

  ///
  /// 表示厚さのテキストスタイルを取得するには
  ///
  bool _isDefaultStyle() {
    if (_isThicknessValueDisplayStar) {
      return true;
    }

    if (_thicknessValue == defaultValue) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isThicknessValueDisplayStar) {
      return true;
    }

    if (_thicknessValue <= LineChainModel.miniThicknessValue) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isThicknessValueDisplayStar) {
      return true;
    }

    if (_thicknessValue >= LineChainModel.maxiThicknessValue) {
      return false;
    }
    return true;
  }
}
