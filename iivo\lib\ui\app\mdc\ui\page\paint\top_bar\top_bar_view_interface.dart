import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'top_bar_view_interface.freezed.dart';

@freezed
class TopBarState with _$TopBarState {
  const factory TopBarState({
    @Default(false) bool isDensityShow,
    @Default(1) int densityLevelIndex,
    @Default(ButtonState.disable) ButtonState dragMoveButtonState,
  }) = _TopBarState;
}

abstract class TopBarViewInterface extends ViewModel<TopBarState> {
  TopBarViewInterface(super.state);

  ///
  /// 拡大レベルの取得
  ///
  Widget get getMagnificationLevel;

  ///
  /// View更新
  ///
  @override
  void update();

  ///
  /// 拡大倍率ボタンのクリック関数
  ///
  void onMagnificationButtonClicked(BuildContext context);

  ///
  /// ドラッグ移動ボタンがクリックされました
  ///
  void onDragMoveButtonClicked(BuildContext context);

  ///
  /// スキャン
  /// 下絵スキャン、ラインスキャン、イラストスキャンの3種類を選択する。
  ///
  void onMdcImageButtonClicked(BuildContext context);

  ///
  /// 下絵　濃いのクリック関数
  ///
  void onDensityAddButtonClicked(BuildContext context);

  ///
  /// 下絵　薄いのクリック関数
  ///
  void onDensityReduceButtonClicked(BuildContext context);

  ///
  /// データインポートボタンをクリックする
  ///
  void onMemoryImportButtonClicked(BuildContext context);
}
