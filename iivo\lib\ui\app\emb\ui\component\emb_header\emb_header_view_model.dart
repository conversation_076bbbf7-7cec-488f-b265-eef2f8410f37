import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../model/app_locale.dart';
import '../../../../../../model/camera_model.dart';
import '../../../../../../model/header_model.dart';
import '../../../../../../model/machine_config_model.dart';
import '../../../../../../model/projector_model.dart';
import '../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../model/sewing_global_model.dart';
import '../../../../../../network/upgrade/upgrade.dart';
import '../../../../../component/common_header/common_header_view_interface.dart';
import '../../../../../component/common_header/common_header_view_model.dart';
import '../../../../../component/real_preview/component/real_preview_controller.dart';
import '../../../../../component/real_preview/real_preview_view_interface.dart';
import '../../../../../component/real_preview/real_preview_view_model.dart';
import '../../../../../global_popup/global_popup_route.dart';
import '../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../global_popup/global_popups/camera_popup/camera_popup_view_model.dart';
import '../../../../../global_popup/global_popups/err_photo_stitch_exit/err_photo_stitch_exit_view_model.dart';
import '../../../../../global_popup/global_popups/err_selected_pattern_cancel_ok/err_selected_pattern_cancel_ok_view_model.dart';
import '../../../../../global_popup/panel_popup_route.dart';
import '../../../../../page_route/page_route.dart';
import '../../../../home/<USER>/home_model.dart';
import '../../../../home/<USER>/component/home_header/bwd_popup/bwd_popup_view_model.dart';
import '../../../../setting/model/setting_model.dart';
import '../../../../utl/model/camera_image_model.dart';
import '../../../model/color_shuffling_model.dart';
import '../../../model/emb_reset_model.dart';
import '../../../model/photo_stitch/photo_stitch_model.dart';
import '../../../model/sewing_model.dart';
import '../../../model/snowman_model.dart';
import '../../page/common_component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../../page/common_component/sew_connectsew/connect_sewing_view_model.dart'
    show connectSewingViewModelProvider;
import '../../page/pattern_edit/edit_menu/toolbar/stippling/stippling_view_model.dart';
import '../../page/pattern_edit/pattern_edit_view_model.dart' as pattern_edit;
import '../../page/photo_stitch/emb_convert/emb_convert_view_model.dart';
import '../../page/sewing/sewing_view_interface.dart' as pattern_sewing;
import '../../page/sewing/sewing_view_model.dart' as pattern_sewing;

/// view _modelに必要な構造
final embHeaderViewModelProvider =
    AutoDisposeStateNotifierProvider<CommonHeaderViewModel, CommonHeaderState>(
        (ref) => EmbHeaderViewModel(ref));

class EmbHeaderViewModel extends CommonHeaderViewModel {
  EmbHeaderViewModel(
    ref,
  ) : super(ref);

  @override
  void build() {
    super.build();

    /// Model更新
    MachineConfigModel().baseMode = SettingBaseMode.emb;
    MachineConfigModel().currentMode = SettingBaseMode.emb;

    /// bwd接続、カメラ機能閉じる
    ref.listen(
      appDisplayGlobalStateProvider
          .select((value) => value.bwdPanelState.ref.IsOn),
      (previous, nextState) {
        if (nextState != ENUM_LIB.FALSE) {
          /// FBCamera閉じる
          /// Cameraのクロースは「stitch_camera」に実現のでここは何もしない
          if (CameraModel().isForwardBackwardPageCameraOpen == true) {
            CameraModel().isForwardBackwardPageCameraOpen = false;
            PanelPopupRoute()
                .maybeRemoveRoute(route: GlobalPopupRouteEnum.stitch_camera);

            ///「ステッチF/B」ページも閉じます
            PagesRoute().popUntil(nextRoute: PageRouteEnum.sewing);
          }

          /// カメラ機能閉じる
          final cameraRet = maybeCloseCamera();
          if (cameraRet == true) {
            state = state.copyWith(cameraButtonState: ButtonState.normal);
          }
        } else {
          /// Do Nothing
        }
      },
    );

    /// 縫製中、カメラ機能閉じる
    ref.listen(
        appDisplayEmbStateProvider
            .select((value) => value.embSetting.ref.isEmbSewing), (_, next) {
      if (next == true) {
        /// カメラ機能閉じる
        final cameraRet = maybeCloseCamera();
        if (cameraRet == true) {
          state = state.copyWith(cameraButtonState: ButtonState.normal);
        }
      } else {
        /// Do Nothing
      }
    });
  }

  @override
  void update() {
    ButtonState cameraButtonState = ButtonState.normal;

    /// 特殊模様の場合
    /// カメラボタン無効
    if (SewingModel().isAutoOneDirection() ||
        ProjectorModel().embProjector.isEmbProjectorViewOpen == true) {
      cameraButtonState = ButtonState.disable;
    } else if (CameraModel().isCameraOpen == true) {
      cameraButtonState = ButtonState.select;
    } else {
      cameraButtonState = ButtonState.normal;
    }

    Locale locale = AppLocale().getCurrentLocale();

    /// View更新
    state = state.copyWith(
      cameraButtonState: cameraButtonState,
      isReadyFirmUpdate: Upgrade().isReadyUpgrade,
      cameraTipMessageString: lookupAppLocalizations(locale).tt_head_camera,
      homeTipMessageString: lookupAppLocalizations(locale).tt_head_home,
      lockTipMessageString: lookupAppLocalizations(locale).tt_head_lock,
      osaeTipMessageString: lookupAppLocalizations(locale).tt_head_osae,
      settingTipMessageString: lookupAppLocalizations(locale).tt_head_setting,
      teachingTipMessageString: lookupAppLocalizations(locale).tt_head_teaching,
      wifiTipMessageString: lookupAppLocalizations(locale).tt_head_wifi,
      isAppBadgeSenju: HeaderModel().getAppBadgeSenju(),
    );
  }

  @override
  void onCameraButtonClicked(BuildContext context) {
    /// 刺繍写真に入った後、クリック不可
    if (PhotoStitchModel().isPhotoStitchEntered) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final provider = realPreviewViewModelProvider(RealPreviewType.emb);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// 色を変える時、クリック不可
    if (ColorShufflingModel().isInColorShuffling == true) {
      return;
    }

    /// 「つなぎ设定界面」をクリックした場合、クリック不可
    if (ref.exists(connectSewingViewModelProvider)) {
      return;

      /// 「スノーマンマーク界面」をクリックした場合、無効になります
    }
    if (SnowmanModel().isSnowManPopupOpen == true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 「下糸巻き」ページをクリックした場合、無効になります
    if (ref.exists(bwdPopupViewModelProvider) &&
        ref.read(bwdPopupViewModelProvider).bobbinPopupState == true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// プロジェクト起動時　カメラボタン無効
    if (ProjectorModel().embProjector.isEmbProjectorViewOpen == true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// カメラは開いており、閉じています
    if (state.cameraButtonState == ButtonState.select ||
        CameraModel().isForwardBackwardPageCameraOpen == true) {
      final DeviceErrorCode errCode = TpdLibrary().apiBinding.hideChangeView();
      if (errCode == DeviceErrorCode.devInvalidError) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }

      final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
      if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
          errCode == DeviceErrorCode.devInvalidPanelError) {
        return;
      }

      if (errCode != DeviceErrorCode.devNoError) {
        return;
      }

      /// FBCamera閉じる
      /// Cameraのクロースは「stitch_camera」に実現のでここは何もしない
      if (CameraModel().isForwardBackwardPageCameraOpen == true) {
        SystemSoundPlayer().play(SystemSoundEnum.accept);
        CameraModel().isForwardBackwardPageCameraOpen = false;
        PanelPopupRoute()
            .maybeRemoveRoute(route: GlobalPopupRouteEnum.stitch_camera);

        ///「ステッチF/B」ページも閉じます
        PagesRoute().popUntil(nextRoute: PageRouteEnum.sewing);
        return;
      }

      /// カメラ機能閉じる
      final cameraRet = maybeCloseCamera();
      if (cameraRet == true) {
        state = state.copyWith(cameraButtonState: ButtonState.normal);
      }
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      return;
    }

    /// カメラが開いていない場合は、カメラを開きます
    _openCamera(context);
  }

  @override
  void onHomeButtonClicked(BuildContext context) {
    final provider = realPreviewViewModelProvider(RealPreviewType.emb);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        /// Real Previewボタン状態戻る
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    } else {
      /// Do Nothing
    }

    /// 簡単スティップリング/エコーキルト/デコラティブフィルの変換中である場合、クリック不可です。
    if (ref.exists(stipplingViewModelProvider) &&
        ref.read(stipplingViewModelProvider).isNewPatternLoading == true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// プロジェクタ開始時に先にプロジェクトを閉じる
    final projectorRet = _maybeCloseProjector();
    if (projectorRet == true) {
      return;
    }

    /// Home を確認すると lib にエラーが表示される
    final deviceError = TpdLibrary().apiBinding.checkGotoHome();
    if (deviceError == DirErrorCode.dirInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
        deviceError == DirErrorCode.dirInvalidPanelError) {
      return;
    }

    if (deviceError == DirErrorCode.dirRequiresConfirmation) {
      /// UIにエラーが表示されるかどうかを確認します

      errSelectedPatternOkFunc = () {
        final DeviceErrorCode deviceError =
            GlobalPopupRoute().resetErrorState();
        if (deviceError != DeviceErrorCode.devNoError) {
          return;
        }

        /// カメラ機能閉じる
        final cameraRet = maybeCloseCamera();
        if (cameraRet == true) {
          state = state.copyWith(cameraButtonState: ButtonState.normal);
        }

        _errSelectedPatternGotoHome();
      };
      errSelectedPatternCancelFunc = () => GlobalPopupRoute().resetErrorState();

      /// エラー画面遷移
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_SELECTED_PATTERN_CANCEL_OK);

      return;
    }

    if (deviceError == DirErrorCode.dirTransitionOK) {
      /// photoStitch画面がHome画面に戻ります
      if (PhotoStitchModel().isInPhotoStitchEdit) {
        /// UIにエラーが表示されるかどうかを確認します
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.errPhotoStitchExit,
          arguments: ErrPhotoStitchExitArgument(
            onOKButtonClicked: (_) {
              DeviceErrorCode deviceError =
                  GlobalPopupRoute().resetErrorState();
              if (deviceError != DeviceErrorCode.devNoError) {
                return;
              }

              /// カメラ機能閉じる
              final cameraRet = maybeCloseCamera();
              if (cameraRet == true) {
                state = state.copyWith(cameraButtonState: ButtonState.normal);
              }

              finishPhotoStitchEditFunc?.call();

              embGoToHome();
              HomeModel.geToHome();

              PhotoStitchModel().isInPhotoStitchEdit = false;
              MachineConfigModel().baseMode = SettingBaseMode.home;
              PhotoStitchModel().actionAfterExitingPhotoStitch();
              PagesRoute().popUntil(nextRoute: PageRouteEnum.home);
            },
            onCancelButtonClicked: (_) => GlobalPopupRoute().resetErrorState(),
          ),
        );
        return;
      }

      /// カメラ機能閉じる
      final cameraRet = maybeCloseCamera();
      if (cameraRet == true) {
        state = state.copyWith(cameraButtonState: ButtonState.normal);
      }

      /// ホームに移行する前の状態のクリーンアップ
      embGoToHome();

      /// Homeへ
      MachineConfigModel().baseMode = SettingBaseMode.home;

      /// Homeへ
      HomeModel.geToHome();

      PhotoStitchModel().actionAfterExitingPhotoStitch();

      super.onHomeButtonClicked(context);
    }
  }

  @override
  void onOsaeButtonClicked(BuildContext context) {
    /// 刺繍写真に入った後、クリック不可
    if (PhotoStitchModel().isPhotoStitchEntered) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final provider = realPreviewViewModelProvider(RealPreviewType.emb);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// 色を変える時、クリック不可
    else if (ColorShufflingModel().isInColorShuffling == true) {
      return;
    }

    /// プロジェクタ開始時に先にプロジェクトを閉じる
    final projectorRet = _maybeCloseProjector();
    if (projectorRet == true) {
      return;
    }

    /// CameraPopUpは開いており、閉じています
    if (TpdLibrary()
            .apiBinding
            .bpIFGetAppDisplayGlobal()
            .changeViewState
            .ref
            .isChangeView ==
        true) {
      /// FBCamera閉じる
      /// Cameraのクロースは「stitch_camera」に実現のでここは何もしない
      if (CameraModel().isForwardBackwardPageCameraOpen == true) {
        CameraModel().isForwardBackwardPageCameraOpen = false;
        PanelPopupRoute()
            .maybeRemoveRoute(route: GlobalPopupRouteEnum.stitch_camera);

        ///「ステッチF/B」ページも閉じます
        PagesRoute().popUntil(nextRoute: PageRouteEnum.sewing);
      }

      /// カメラ機能閉じる
      final cameraRet = maybeCloseCamera();
      if (cameraRet == true) {
        state = state.copyWith(cameraButtonState: ButtonState.normal);
      }
    }

    super.onOsaeButtonClicked(context);
  }

  @override
  void onLockButtonClicked(BuildContext context) {
    /// 刺繍写真に入った後、クリック不可
    if (PhotoStitchModel().isPhotoStitchEntered) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final provider = realPreviewViewModelProvider(RealPreviewType.emb);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// 色を変える時、クリック不可
    if (ColorShufflingModel().isInColorShuffling == true) {
      return;
    }

    /// 「つなぎ设定界面」ページをクリックした場合、無効になります
    if (ref.exists(connectSewingViewModelProvider)) {
      return;
    }

    super.onLockButtonClicked(context);
  }

  @override
  void onSettingButtonClicked(BuildContext context) {
    /// 刺繍写真に入った後、クリック不可
    if (PhotoStitchModel().isPhotoStitchEntered) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final provider = realPreviewViewModelProvider(RealPreviewType.emb);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// 色を変える時、クリック不可
    if (ColorShufflingModel().isInColorShuffling == true) {
      return;
    }

    /// 「つなぎ设定界面」ページをクリックした場合、無効になります
    if (ref.exists(connectSewingViewModelProvider)) {
      return;
    }

    /// プロジェクタ開始時に先にプロジェクトを閉じる
    final projectorRet = _maybeCloseProjector();
    if (projectorRet == true) {
      return;
    }

    /// つなぎのプロジェクトを閉じる
    if (SewingGlobalModel().isInSewingPage == true &&
        SewingModel().isAutoOneDirection()) {
      ProjectorModel().embProjector.closeEmbProjector();
    } else {
      /// do nothing
    }

    /// Settingボタンをクリックすると、Deviceデバイスの状態が更新されます
    final errCode = TpdLibrary().apiBinding.gotoSettings();

    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    final bPIFError = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    if (bPIFError != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      return;
    }

    _maybeCloseWFoot();

    /// カメラ機能閉じる
    final cameraRet = maybeCloseCamera();
    if (cameraRet == true) {
      state = state.copyWith(cameraButtonState: ButtonState.normal);
    }

    /// EMBはEMBの関連設定ページにジャンプする
    SettingModel().setSettingPageIndexWithOtherPage(SettingMode.embroidery);
    super.onSettingButtonClicked(context);
  }

  @override
  void onTeachingButtonClicked(BuildContext context) {
    final provider = realPreviewViewModelProvider(RealPreviewType.emb);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// 色を変える時、クリック不可
    if (ColorShufflingModel().isInColorShuffling == true) {
      return;
    }

    /// 「つなぎ设定界面」ページをクリックした場合、無効になります
    if (ref.exists(connectSewingViewModelProvider)) {
      return;
    }

    /// プロジェクタ開始時に先にプロジェクトを閉じる
    final projectorRet = _maybeCloseProjector();
    if (projectorRet == true) {
      return;
    }

    /// つなぎのプロジェクトを閉じる
    if (SewingGlobalModel().isInSewingPage == true &&
        SewingModel().isAutoOneDirection()) {
      ProjectorModel().embProjector.closeEmbProjector();
    } else {
      /// do nothing
    }

    /// teaching画面遷移
    final errCode = TpdLibrary().apiBinding.gotoHowToUse();
    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFError = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    if (bPIFError != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      return;
    }

    if (errCode != DeviceErrorCode.devNoError) {
      return;
    }

    _maybeCloseWFoot();

    /// カメラ機能閉じる
    final cameraRet = maybeCloseCamera();
    if (cameraRet == true) {
      state = state.copyWith(cameraButtonState: ButtonState.normal);
    }

    super.onTeachingButtonClicked(context);
  }

  @override
  void onWifiButtonClicked(BuildContext context) {
    /// 刺繍写真に入った後、クリック不可
    if (PhotoStitchModel().isPhotoStitchEntered) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 色を変える時、クリック不可
    if (ColorShufflingModel().isInColorShuffling == true) {
      return;
    }

    /// 「つなぎ设定界面」ページをクリックした場合、無効になります
    if (ref.exists(connectSewingViewModelProvider)) {
      return;
    }

    final provider = realPreviewViewModelProvider(RealPreviewType.emb);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// プロジェクタ開始時に先にプロジェクトを閉じる
    final projectorRet = _maybeCloseProjector();
    if (projectorRet == true) {
      return;
    }

    /// つなぎのプロジェクトを閉じる
    if (SewingGlobalModel().isInSewingPage == true &&
        SewingModel().isAutoOneDirection()) {
      ProjectorModel().embProjector.closeEmbProjector();
    } else {
      /// do nothing
    }

    final errCode = TpdLibrary().apiBinding.gotoWlanSetting();
    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFError = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    if (bPIFError != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      return;
    }

    if (errCode != DeviceErrorCode.devNoError) {
      return;
    }

    _maybeCloseWFoot();

    /// カメラ機能閉じる
    final cameraRet = maybeCloseCamera();
    if (cameraRet == true) {
      state = state.copyWith(cameraButtonState: ButtonState.normal);
    }
    super.onWifiButtonClicked(context);
  }

  ///
  /// カメラを閉じる
  ///
  @override
  bool maybeCloseCamera() {
    /// カメラは開いており、閉じています
    if (CameraModel().isCameraOpen ||
        CameraModel().isForwardBackwardPageCameraOpen) {
      /// カメラ閉じる
      CameraModel().isCameraOpen = false;
      CameraModel().isForwardBackwardPageCameraOpen = false;

      /// カメラHard閉じる
      CameraImageModel.cameraImagePollingControl
          .stopCameraImagePollingAndRefreshUI();

      /// カメラPopup画面閉じる
      /// stitch_camera
      /// layoutCamera
      closeCameraPopup();

      return true;
    }

    return false;
  }

  ///
  /// カメラの画面を閉じる
  /// stitch_camera
  /// layoutCamera
  ///
  @override
  void closeCameraPopup() {
    /// save Popupの場合は、カメラPopupに戻る
    PanelPopupRoute()
        .maybeRemoveRoute(route: GlobalPopupRouteEnum.screenShotSavePopup);

    /// カメラPopupを閉じる
    PanelPopupRoute().maybeRemoveRoute(route: GlobalPopupRouteEnum.cameraPopup);

    PanelPopupRoute()
        .maybeRemoveRoute(route: GlobalPopupRouteEnum.stitch_camera);

    PanelPopupRoute()
        .maybeRemoveRoute(route: GlobalPopupRouteEnum.layoutCamera);
  }

  ///
  /// プロジェクト起動時、プロジェクト閉じる流れ処理
  /// 戻る値: true(閉じる処理が必要ですので、普通処理をしない)
  ///
  bool _maybeCloseProjector() {
    /// プロジェクションがオンになっている場合の処理
    if (ProjectorModel().embProjector.isEmbProjectorViewOpen == true) {
      final embProjectorFunction =
          ref.read(embProjectorFunctionProvider.notifier);

      /// プロジェクト起動・停止前にエラーCheckをする
      /// エラーがあれば ポープアープを表示する
      final hasError = embProjectorFunction
          .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
      if (hasError) {
        return true;
      }

      embProjectorFunction.closeEmbProjectorView(
        afterClosedHandleCallback: () {},
        closingHandleCallback: () {
          ProjectorModel().embProjector.isEmbProjectorViewOpen = false;

          if (SewingModel().isInSewingPage) {
            ref
                .read(pattern_sewing.sewingProvider.notifier)
                .updateSewingByChild(pattern_sewing.ModuleType.projector);
          } else {
            ref
                .read(pattern_edit.patternEditViewModelProvider.notifier)
                .updateEditPageByChild(pattern_edit.ModuleType.projector);
          }
          update();
        },
      );
      return true;
    }

    return false;
  }

  ///
  /// W押えのプロジェクションがオンになっている場合は、プロジェクションをオフにします
  ///
  void _maybeCloseWFoot() {
    if (ProjectorModel().embProjector.isEmbWFooterProjectorOpen() == true) {
      ProjectorModel().embProjector.closeEmbProjector();
    } else {
      /// Do Nothing
    }

    /// LEDPointerを閉じます
    DeviceLibrary().apiBinding.closeLedPtSetting();
  }

  ///
  /// カメラを開きます
  ///
  void _openCamera(BuildContext context) {
    /// showChangeView
    final errCode = TpdLibrary().apiBinding.showChangeView();
    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      return;
    }

    if (errCode != DeviceErrorCode.devNoError) {
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// Model更新
    CameraModel().isCameraOpen = true;

    /// カメラ起動
    CameraImageModel.cameraImagePollingControl
        .startCameraImagePollingAndRefreshUI();

    /// View更新
    state = state.copyWith(cameraButtonState: ButtonState.select);

    /// カメラPopup表示
    /// カメラPopup閉じる callback追加
    final Future future;
    if (SewingModel().isInSewingPage == false ||
        SewingModel().isForwardBackwardPageOpen == true) {
      future = PanelPopupRoute().pushNamed(
          nextRoute: GlobalPopupRouteEnum.cameraPopup,
          arguments: const CameraArgument(isUtlOpen: false));
    } else {
      future = PanelPopupRoute().pushNamed(
          nextRoute: GlobalPopupRouteEnum.layoutCamera,
          arguments: const CameraArgument(isUtlOpen: false));
    }
    future.then((_) {
      /// カメラ機能閉じる
      final cameraRet = maybeCloseCamera();
      if (cameraRet == true) {
        state = state.copyWith(cameraButtonState: ButtonState.normal);
      }
    });
  }

  ///
  /// エラーが表示されるかどうかを確認します
  ///
  void _errSelectedPatternGotoHome() {
    if (SewingGlobalModel().isInSewingPage == true &&
        SewingModel().isAutoOneDirection()) {
      ProjectorModel().embProjector.closeEmbProjector();
    } else {
      /// do nothing
    }

    _maybeCloseWFoot();

    /// カメラ機能閉じる
    final cameraRet = maybeCloseCamera();
    if (cameraRet == true) {
      state = state.copyWith(cameraButtonState: ButtonState.normal);
    }

    embGoToHome();

    MachineConfigModel().baseMode = SettingBaseMode.home;

    /// Homeへ
    HomeModel.geToHome();

    PhotoStitchModel().actionAfterExitingPhotoStitch();

    PagesRoute().popUntil(nextRoute: PageRouteEnum.home);
  }
}
