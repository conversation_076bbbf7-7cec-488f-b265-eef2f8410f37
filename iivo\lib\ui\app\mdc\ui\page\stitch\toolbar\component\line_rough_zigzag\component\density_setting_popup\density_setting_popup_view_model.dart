import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_rough_zigzag_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'density_setting_popup_view_interface.dart';

typedef Unit = DisplayUnit;

final densitySettingPopupViewModelProvider = StateNotifierProvider.autoDispose<
    DensitySettingPopupViewInterface,
    DensitySettingPopupState>((ref) => DensitySettingPopupViewModel(ref));

class DensitySettingPopupViewModel extends DensitySettingPopupViewInterface {
  DensitySettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const DensitySettingPopupState(
              densityDisplayValue: "",
              isDefaultStyle: false,
              minusButtonValid: false,
              plusButtonValid: false,
            ),
            ref) {
    update();
  }

  ///
  /// 最大密度値
  ///
  // final int _maxDensityValue = 200;
  final int maxDensityIndex =
      MdcRoughZigzagDensity.mdc_rough_zigzag_density_200.index;

  ///
  /// 最小密度値
  ///
  // final int _minDensityValue = 25;
  final int minDensityIndex =
      MdcRoughZigzagDensity.mdc_rough_zigzag_density_025.index;

  ///
  /// デフォルトの密度値
  ///

  final int _defaultDensityIndex =
      LineRoughZigzagModel().defaultDensityValue.index;

  ///
  /// ステップ量
  ///
  final int _stepValue = 1;

  ///
  /// 単位取得する
  ///
  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  ///
  /// ディスプレイスター
  ///
  bool _isDensityValueDisplayStar = LineRoughZigzagModel().getDensityIndex() !=
          LineRoughZigzagModel.densityNotUpdating.index
      ? false
      : true;

  ///
  /// 密度値
  ///
  int _densityIndex = LineRoughZigzagModel().getDensityIndex();

  ///
  /// 自分を閉じる
  ///
  void _closeSelf(BuildContext context) {
    PopupNavigator.pop(context: context);
  }

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    state = state.copyWith(
      densityDisplayValue: _getDensityDisplayValue(),
      isDefaultStyle: _isDefaultStyle(),
      minusButtonValid: _getMinusButtonState(),
      plusButtonValid: _getPlusButtonState(),
    );
  }

  ///
  /// マイナスボタンをクリックする
  ///
  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isDensityValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isDensityValueDisplayStar = false;

      ///  Model 更新
      _densityIndex = _defaultDensityIndex;

      /// View更新
      update();

      return false;
    }
    if (_densityIndex == minDensityIndex) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _densityIndex = _densityIndex - _stepValue < minDensityIndex
        ? maxDensityIndex
        : _densityIndex - _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  ///
  /// プラスボタンをクリックする
  ///
  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_isDensityValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isDensityValueDisplayStar = false;

      ///  Model 更新
      _densityIndex = _defaultDensityIndex;

      /// View更新
      update();

      return false;
    }
    if (_densityIndex == maxDensityIndex) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _densityIndex = _densityIndex + _stepValue > maxDensityIndex
        ? maxDensityIndex
        : _densityIndex + _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  ///
  /// OK ボタンがクリックされました
  ///
  @override
  void onOkButtonClicked(BuildContext context) {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineRoughZigzagDensity.toString());
    if (_isDensityValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int density = LineRoughZigzagModel().getDensityIndex();

    ///  Model 更新
    LineRoughZigzagModel().setDensityIndex(_densityIndex);
    if (LineRoughZigzagModel().setMdcRoughZigzagSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (density != _densityIndex) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// 密度表示値を取得します
  ///
  String _getDensityDisplayValue() {
    if (_isDensityValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.**";
      }
      return "*.***";
    }
    // mdc_rough_zigzag_density_invalidのプレースホルダーを減算します
    final int densityValueIndex = MdcRoughZigzagDensity.values.length - 1;
    if (_densityIndex < 0 ||
        _densityIndex > densityValueIndex ||
        LineRoughZigzagModel.stitchDensityMmValue.length != densityValueIndex) {
      Log.assertTrace("MdcRoughZigzagDensity 配列が範囲外です");
    }
    // mdc_rough_zigzag_density_invalidのプレースホルダーを減算します
    final int densityIndexValue = _densityIndex - 1;
    if (currentSelectedUnit == Unit.mm) {
      return LineRoughZigzagModel.stitchDensityMmValue[densityIndexValue];
    }

    return LineRoughZigzagModel.stitchDensityInchValue[densityIndexValue];
  }

  ///
  /// 密度表示テキストスタイルを取得します
  ///
  bool _isDefaultStyle() {
    if (_isDensityValueDisplayStar) {
      return true;
    }

    if (_densityIndex == _defaultDensityIndex) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isDensityValueDisplayStar) {
      return true;
    }

    if (_densityIndex <= minDensityIndex) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isDensityValueDisplayStar) {
      return true;
    }

    if (_densityIndex >= maxDensityIndex) {
      return false;
    }
    return true;
  }
}
