import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'line_chain_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

///
/// 白い背景に黒いテキスト
///
const TextStyle blackTextWhiteBackground =
    TextStyle(color: Colors.white, backgroundColor: Colors.black);

///
/// 黒い背景に白いテキスト
///
const TextStyle whiteTextBlackBackground =
    TextStyle(color: Colors.black, backgroundColor: Colors.transparent);

@freezed
class LineChainState with _$LineChainState {
  const factory LineChainState({
    required bool isSizePopupOn,
    required bool isThicknessPopupOn,
    required String candleSizeValue,
    required String candleThicknessValue,
    required bool isThicknessDefaultValue,
    required bool isSizeDefaultValue,
  }) = _LineChainState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class LineChainStateViewInterface extends ViewModel<LineChainState> {
  LineChainStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// チェーンステッチのSizeダンパボタン
  ///
  void onCandleSizeClick(context);

  ///
  /// チェーンステッチのThicknessダンパボタン
  ///
  void onCandleThicknessClick(context);

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;

  ///
  /// ラインジグザグプロパティの既定値
  ///
  int get defaultSizeValue;
  int get defaultThicknessValue;
  String get textSignal;
}
