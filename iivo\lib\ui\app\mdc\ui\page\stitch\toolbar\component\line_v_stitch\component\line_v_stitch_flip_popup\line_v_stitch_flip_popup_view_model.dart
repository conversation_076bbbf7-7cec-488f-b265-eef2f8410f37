import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_v_stitch_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'line_v_stitch_flip_popup_view_interface.dart';

final lineVStitchFlipViewModelProvider = StateNotifierProvider.autoDispose<
    LineVStitchFlipStateViewInterface,
    LineVStitchFlipState>((ref) => LineVStitchFlipViewModel(ref));

class LineVStitchFlipViewModel extends LineVStitchFlipStateViewInterface {
  LineVStitchFlipViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            LineVStitchFlipState(
                flipInsideState: LineVStitchModel().getFlip() !=
                        LineVStitchModel.sideNotUpdating
                    ? LineVStitchModel().getFlip() == FlipSide.flip_inside
                        ? true
                        : false
                    : false,
                flipOutsideState: LineVStitchModel().getFlip() !=
                        LineVStitchModel.sideNotUpdating
                    ? LineVStitchModel().getFlip() == FlipSide.flip_outside
                        ? true
                        : false
                    : false),
            ref);

  @override
  void onInsideClicked() {
    if (state.flipInsideState) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    state = state.copyWith(flipInsideState: true, flipOutsideState: false);
  }

  @override
  void onOutsideClicked() {
    if (state.flipOutsideState) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    state = state.copyWith(flipInsideState: false, flipOutsideState: true);
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineVStitchFlip.toString());
    if (state.flipInsideState == false && state.flipOutsideState == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    FlipSide getFlip = LineVStitchModel().getFlip();

    /// Model 更新
    if (state.flipInsideState) {
      LineVStitchModel().setFlip(FlipSide.flip_inside);
    }

    if (state.flipOutsideState) {
      LineVStitchModel().setFlip(FlipSide.flip_outside);
    }

    if (LineVStitchModel().setMdcVStitchLineSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    FlipSide flipInsideState = LineVStitchModel().getFlip();
    if (getFlip != flipInsideState) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }
}
