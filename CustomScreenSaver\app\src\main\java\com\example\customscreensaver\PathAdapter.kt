import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.brother.ph.iivo.screensaver.R
import com.example.customscreensaver.BitmapRepository
import java.io.File

class PathAdapter(
  private val paths: List<String>,
  private val listener: OnPathClickListener,
) : RecyclerView.Adapter<PathAdapter.PathViewHolder>() {

  private var currentIndex = 0

  interface OnPathClickListener {
    fun onPathClick(position: Int)
  }

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PathViewHolder {
    val view = LayoutInflater.from(parent.context).inflate(R.layout.item_path, parent, false)
    return PathViewHolder(view)
  }

  override fun onBindViewHolder(holder: PathViewHolder, position: Int) {
    holder.bind(paths[currentIndex])
  }

  override fun getItemCount(): Int = 1

  inner class PathViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
    private val imageView: ImageView = itemView.findViewById(R.id.pathImageView)

    init {
      imageView.setOnClickListener {
        listener.onPathClick(adapterPosition)
      }
    }

    fun bind(path: String) {
      imageView.setImageBitmap(BitmapRepository.loadBitmap(File(path)))
    }
  }

  private val handler = Handler(Looper.getMainLooper())

  /**
   * 画像スライドショーを開始
   */
  fun startImageLoop() {
    /// ハンドラーを使用して、スケジュールされた時間にイメージを切り替える
    val runnable = object : Runnable {
      override fun run() {
        if (currentIndex < paths.size - 1) {
          currentIndex++
        } else {
          currentIndex = 0
        }
        notifyItemRangeChanged(0, itemCount)
        /// 10秒後に再度実行してください
        handler.postDelayed(this, 10000)
      }
    }
    handler.postDelayed(runnable, 10000) /// 実行を開始する
  }

  /**
   * 資源を破棄し解放する
   */
  fun dispose() {
    // Activity終了時に、今後の全てのHandler要求を破棄する事で、メモリリークを防止する
    handler.removeCallbacksAndMessages(null)
    BitmapRepository.clearCache()
  }
}
