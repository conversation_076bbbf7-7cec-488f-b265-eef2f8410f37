import 'app_localizations.dart';

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get color_001 => 'ピンク';

  @override
  String get color_002 => 'ピンク';

  @override
  String get color_003 => 'ピンク';

  @override
  String get color_004 => 'モモイロ';

  @override
  String get color_005 => 'ピンク';

  @override
  String get color_006 => 'モモイロ';

  @override
  String get color_007 => 'ピンク';

  @override
  String get color_008 => 'ピンク';

  @override
  String get color_009 => 'コイピンク';

  @override
  String get color_010 => 'コイピンク';

  @override
  String get color_011 => 'アカムラサキ';

  @override
  String get color_012 => 'ピンク';

  @override
  String get color_013 => 'サーモンピンク';

  @override
  String get color_014 => 'コイピンク';

  @override
  String get color_015 => 'ピンク';

  @override
  String get color_016 => 'サーモンピンク';

  @override
  String get color_017 => 'コイピンク';

  @override
  String get color_018 => 'シュイロ';

  @override
  String get color_019 => 'アカ';

  @override
  String get color_020 => 'アカ';

  @override
  String get color_021 => 'アカ';

  @override
  String get color_022 => 'クチバイロ';

  @override
  String get color_023 => 'アカ';

  @override
  String get color_024 => 'クチバイロ';

  @override
  String get color_025 => 'ベニカバイロ';

  @override
  String get color_026 => 'ベニカバイロ';

  @override
  String get color_027 => 'マゼンダ';

  @override
  String get color_028 => 'ベニイロ';

  @override
  String get color_029 => 'コイピンク';

  @override
  String get color_030 => 'コイピンク';

  @override
  String get color_031 => 'コイピンク';

  @override
  String get color_032 => 'マゼンダ';

  @override
  String get color_033 => 'マゼンダ';

  @override
  String get color_034 => 'マゼンダ';

  @override
  String get color_035 => 'マゼンダ';

  @override
  String get color_036 => 'アカ';

  @override
  String get color_037 => 'アカ';

  @override
  String get color_038 => 'マゼンダ';

  @override
  String get color_039 => 'アカ';

  @override
  String get color_040 => 'アカ';

  @override
  String get color_041 => 'アカ';

  @override
  String get color_042 => 'アカ';

  @override
  String get color_043 => 'シュイロ';

  @override
  String get color_044 => 'シュイロ';

  @override
  String get color_045 => 'シュイロ';

  @override
  String get color_046 => 'アカ';

  @override
  String get color_047 => 'アカ';

  @override
  String get color_048 => 'アカ';

  @override
  String get color_049 => 'モモイロ';

  @override
  String get color_050 => 'ピンク';

  @override
  String get color_051 => 'サーモンピンク';

  @override
  String get color_052 => 'サーモンピンク';

  @override
  String get color_053 => 'ピンク';

  @override
  String get color_054 => 'マンダリン';

  @override
  String get color_055 => 'チャイロ';

  @override
  String get color_056 => 'ミズイロ';

  @override
  String get color_057 => 'ミズイロ';

  @override
  String get color_058 => 'ミズイロ';

  @override
  String get color_059 => 'ソライロ';

  @override
  String get color_060 => 'アオムラサキ';

  @override
  String get color_061 => 'ソライロ';

  @override
  String get color_062 => 'ソライロ';

  @override
  String get color_063 => 'ソライロ';

  @override
  String get color_064 => 'ミズイロ';

  @override
  String get color_065 => 'ソライロ';

  @override
  String get color_066 => 'ミズイロ';

  @override
  String get color_067 => 'ソライロ';

  @override
  String get color_068 => 'ミズイロ';

  @override
  String get color_069 => 'アオムラサキ';

  @override
  String get color_070 => 'ミズイロ';

  @override
  String get color_071 => 'アオ';

  @override
  String get color_072 => 'アオムラサキ';

  @override
  String get color_073 => 'グンジョウイロ';

  @override
  String get color_074 => 'ナンドイロ';

  @override
  String get color_075 => 'アオムラサキ';

  @override
  String get color_076 => 'ミズイロ';

  @override
  String get color_077 => 'ライトブルー';

  @override
  String get color_078 => 'ライトブルー';

  @override
  String get color_079 => 'アイイロ';

  @override
  String get color_080 => 'ライトブルー';

  @override
  String get color_081 => 'アイイロ';

  @override
  String get color_082 => 'アオ';

  @override
  String get color_083 => 'アイイロ';

  @override
  String get color_084 => 'ナンドイロ';

  @override
  String get color_085 => 'フカミドリ';

  @override
  String get color_086 => 'フカミドリ';

  @override
  String get color_087 => 'グンジョウイロ';

  @override
  String get color_088 => 'グンジョウイロ';

  @override
  String get color_089 => 'グンジョウイロ';

  @override
  String get color_090 => 'アイイロ';

  @override
  String get color_091 => 'アイイロ';

  @override
  String get color_092 => 'グンジョウイロ';

  @override
  String get color_093 => 'アイイロ';

  @override
  String get color_094 => 'アイイロ';

  @override
  String get color_095 => 'アイイロ';

  @override
  String get color_096 => 'グンジョウイロ';

  @override
  String get color_097 => 'グンジョウイロ';

  @override
  String get color_098 => 'ミズイロ';

  @override
  String get color_099 => 'アオムラサキ';

  @override
  String get color_100 => 'フジイロ';

  @override
  String get color_101 => 'フジイロ';

  @override
  String get color_102 => 'フジイロ';

  @override
  String get color_103 => 'フジムラサキ';

  @override
  String get color_104 => 'グンジョウイロ';

  @override
  String get color_105 => 'アオ';

  @override
  String get color_106 => 'グンジョウイロ';

  @override
  String get color_107 => 'グンジョウイロ';

  @override
  String get color_108 => 'グンジョウイロ';

  @override
  String get color_109 => 'コイアカムラサキ';

  @override
  String get color_110 => 'グンジョウイロ';

  @override
  String get color_111 => 'コイアカムラサキ';

  @override
  String get color_112 => 'スミレイロ';

  @override
  String get color_113 => 'ムラサキ';

  @override
  String get color_114 => 'スミレイロ';

  @override
  String get color_115 => 'フジイロ';

  @override
  String get color_116 => 'アカムラサキ';

  @override
  String get color_117 => 'ウスアカムラサキ';

  @override
  String get color_118 => 'ウスムラサキ';

  @override
  String get color_119 => 'モモイロ';

  @override
  String get color_120 => 'ピンク';

  @override
  String get color_121 => 'ピンク';

  @override
  String get color_122 => 'ピンク';

  @override
  String get color_123 => 'ピンク';

  @override
  String get color_124 => 'コイピンク';

  @override
  String get color_125 => 'ベニカバイロ';

  @override
  String get color_126 => 'スミレイロ';

  @override
  String get color_127 => 'スミレイロ';

  @override
  String get color_128 => 'ミズイロ';

  @override
  String get color_129 => 'ライトブルー';

  @override
  String get color_130 => 'ライトブルー';

  @override
  String get color_131 => 'アオ';

  @override
  String get color_132 => 'ライトブルー';

  @override
  String get color_133 => 'ナンドイロ';

  @override
  String get color_134 => 'ナンドイロ';

  @override
  String get color_135 => 'アオミドリ';

  @override
  String get color_136 => 'フカミドリ';

  @override
  String get color_137 => 'ナンドイロ';

  @override
  String get color_138 => 'グンジョウイロ';

  @override
  String get color_139 => 'ギン';

  @override
  String get color_140 => 'ギン';

  @override
  String get color_141 => 'エメラルドグリーン';

  @override
  String get color_142 => 'モスグリーン';

  @override
  String get color_143 => 'アオミドリ';

  @override
  String get color_144 => 'ライトブルー';

  @override
  String get color_145 => 'モスグリーン';

  @override
  String get color_146 => 'モスグリーン';

  @override
  String get color_147 => 'ライトブルー';

  @override
  String get color_148 => 'モスグリーン';

  @override
  String get color_149 => 'ナンドイロ';

  @override
  String get color_150 => 'フカミドリ';

  @override
  String get color_151 => 'アオミドリ';

  @override
  String get color_152 => 'アオミドリ';

  @override
  String get color_153 => 'セイジイロ';

  @override
  String get color_154 => 'マツバイロ';

  @override
  String get color_155 => 'マツバイロ';

  @override
  String get color_156 => 'フカミドリ';

  @override
  String get color_157 => 'フカミドリ';

  @override
  String get color_158 => 'ハイミドリ';

  @override
  String get color_159 => 'コイコゲチャ';

  @override
  String get color_160 => 'ハイミドリ';

  @override
  String get color_161 => 'フカミドリ';

  @override
  String get color_162 => 'コイコゲチャ';

  @override
  String get color_163 => 'フカミドリ';

  @override
  String get color_164 => 'ソライロ';

  @override
  String get color_165 => 'エメラルドグリーン';

  @override
  String get color_166 => 'ハイミドリ';

  @override
  String get color_167 => 'ハイミドリ';

  @override
  String get color_168 => 'モスグリーン';

  @override
  String get color_169 => 'アオミドリ';

  @override
  String get color_170 => 'モスグリーン';

  @override
  String get color_171 => 'アオミドリ';

  @override
  String get color_172 => 'フカミドリ';

  @override
  String get color_173 => 'フカミドリ';

  @override
  String get color_174 => 'フカミドリ';

  @override
  String get color_175 => 'キミドリ';

  @override
  String get color_176 => 'アオミドリ';

  @override
  String get color_177 => 'ミドリ';

  @override
  String get color_178 => 'アオミドリ';

  @override
  String get color_179 => 'モスグリーン';

  @override
  String get color_180 => 'ミドリ';

  @override
  String get color_181 => 'キミドリ';

  @override
  String get color_182 => 'トキワイロ';

  @override
  String get color_183 => 'ワカバイロ';

  @override
  String get color_184 => 'ワカバイロ';

  @override
  String get color_185 => 'エメラルドグリーン';

  @override
  String get color_186 => 'ワカバイロ';

  @override
  String get color_187 => 'キミドリ';

  @override
  String get color_188 => 'ワカバイロ';

  @override
  String get color_189 => 'キミドリ';

  @override
  String get color_190 => 'トキワイロ';

  @override
  String get color_191 => 'キミドリ';

  @override
  String get color_192 => 'キミドリ';

  @override
  String get color_193 => 'トキワイロ';

  @override
  String get color_194 => 'キミドリ';

  @override
  String get color_195 => 'キイロ';

  @override
  String get color_196 => 'ツチイロ';

  @override
  String get color_197 => 'チャイロ';

  @override
  String get color_198 => 'キミドリ';

  @override
  String get color_199 => 'ハイミドリ';

  @override
  String get color_200 => 'ハイミドリ';

  @override
  String get color_201 => 'ハイミドリ';

  @override
  String get color_202 => 'ハイミドリ';

  @override
  String get color_203 => 'クリームイロ';

  @override
  String get color_204 => 'レモンイロ';

  @override
  String get color_205 => 'キイロ';

  @override
  String get color_206 => 'キイロ';

  @override
  String get color_207 => 'キイロ';

  @override
  String get color_208 => 'オウドイロ';

  @override
  String get color_209 => 'キイロ';

  @override
  String get color_210 => 'オレンジ';

  @override
  String get color_211 => 'ツチイロ';

  @override
  String get color_212 => 'オウドイロ';

  @override
  String get color_213 => 'オレンジ';

  @override
  String get color_214 => 'オレンジ';

  @override
  String get color_215 => 'キンチャ';

  @override
  String get color_216 => 'オレンジ';

  @override
  String get color_217 => 'オウドイロ';

  @override
  String get color_218 => 'ヤマブキイロ';

  @override
  String get color_219 => 'オウドイロ';

  @override
  String get color_220 => 'オウドイロ';

  @override
  String get color_221 => 'ツチイロ';

  @override
  String get color_222 => 'ダイダイイロ';

  @override
  String get color_223 => 'シュイロ';

  @override
  String get color_224 => 'シュイロ';

  @override
  String get color_225 => 'シュイロ';

  @override
  String get color_226 => 'シュイロ';

  @override
  String get color_227 => 'アカ';

  @override
  String get color_228 => 'アカ';

  @override
  String get color_229 => 'シュイロ';

  @override
  String get color_230 => 'サーモンピンク';

  @override
  String get color_231 => 'サーモンピンク';

  @override
  String get color_232 => 'キンチャ';

  @override
  String get color_233 => 'マンダリン';

  @override
  String get color_234 => 'ハダイロ';

  @override
  String get color_235 => 'マンダリン';

  @override
  String get color_236 => 'ダイダイイロ';

  @override
  String get color_237 => 'ダイダイイロ';

  @override
  String get color_238 => 'オレンジ';

  @override
  String get color_239 => 'オレンジ';

  @override
  String get color_240 => 'オウドイロ';

  @override
  String get color_241 => 'アカチャイロ';

  @override
  String get color_242 => 'チャイロ';

  @override
  String get color_243 => 'クチバイロ';

  @override
  String get color_244 => 'クリームイロ';

  @override
  String get color_245 => 'タマゴイロ';

  @override
  String get color_246 => 'タマゴイロ';

  @override
  String get color_247 => 'ワカバイロ';

  @override
  String get color_248 => 'ベージュ';

  @override
  String get color_249 => 'ツチイロ';

  @override
  String get color_250 => 'オウドイロ';

  @override
  String get color_251 => 'ツチイロ';

  @override
  String get color_252 => 'キンチャ';

  @override
  String get color_253 => 'ハダイロ';

  @override
  String get color_254 => 'ベージュ';

  @override
  String get color_255 => 'ベージュ';

  @override
  String get color_256 => 'ツチイロ';

  @override
  String get color_257 => 'クチバイロ';

  @override
  String get color_258 => 'ハイミドリ';

  @override
  String get color_259 => 'ハダイロ';

  @override
  String get color_260 => 'サーモンピンク';

  @override
  String get color_261 => 'ハダイロ';

  @override
  String get color_262 => 'サーモンピンク';

  @override
  String get color_263 => 'チャイロ';

  @override
  String get color_264 => 'オレンジ';

  @override
  String get color_265 => 'コゲチャ';

  @override
  String get color_266 => 'ツチイロ';

  @override
  String get color_267 => 'クチバイロ';

  @override
  String get color_268 => 'クチバイロ';

  @override
  String get color_269 => 'クチバイロ';

  @override
  String get color_270 => 'コイコゲチャ';

  @override
  String get color_271 => 'クチバイロ';

  @override
  String get color_272 => 'ツチイロ';

  @override
  String get color_273 => 'ベージュ';

  @override
  String get color_274 => 'ハイイロ';

  @override
  String get color_275 => 'コイグレー';

  @override
  String get color_276 => 'コイグレー';

  @override
  String get color_277 => 'コイコゲチャ';

  @override
  String get color_278 => 'コイグレー';

  @override
  String get color_279 => 'グレー';

  @override
  String get color_280 => 'グレー';

  @override
  String get color_281 => 'グレー';

  @override
  String get color_282 => 'グレー';

  @override
  String get color_283 => 'コイグレー';

  @override
  String get color_284 => 'ネズミイロ';

  @override
  String get color_285 => 'ハイイロ';

  @override
  String get color_286 => 'シロ';

  @override
  String get color_287 => 'ツチイロ';

  @override
  String get color_288 => 'ギン';

  @override
  String get color_289 => 'クロ';

  @override
  String get color_290 => 'シロ';

  @override
  String get color_291 => 'シロ';

  @override
  String get color_292 => 'コイピンク';

  @override
  String get color_293 => 'コイピンク';

  @override
  String get color_294 => 'コイピンク';

  @override
  String get color_295 => 'サーモンピンク';

  @override
  String get color_296 => 'サーモンピンク';

  @override
  String get color_297 => 'サーモンピンク';

  @override
  String get color_298 => 'クリームイロ';

  @override
  String get color_299 => 'クチバイロ';

  @override
  String get color_300 => 'グンジョウイロ';

  @override
  String get color_301 => 'アップリケピース';

  @override
  String get color_302 => 'アップリケノイチ';

  @override
  String get color_303 => 'アップリケ';

  @override
  String get id_icon_test00001 => '\$\$\$\$\$';

  @override
  String get icon_00002 => '実用・文字模様';

  @override
  String get icon_00003_1 => '刺しゅう';

  @override
  String get icon_00006_3 => '実用模様';

  @override
  String get icon_00007_3 => '文字\n模様ぬい';

  @override
  String get icon_stitch => 'ぬい目';

  @override
  String get icon_close_1 => '閉じる';

  @override
  String get icon_cancel => 'キャンセル';

  @override
  String get icon_ok => 'OK';

  @override
  String get icon_00011_zz => '%%%icon%%%';

  @override
  String get icon_00011_zz_s => '%%%icon%%%';

  @override
  String get icon_00011 => '削除';

  @override
  String get icon_00012_zz => '%%%icon%%%';

  @override
  String get icon_00012_zz_s => '%%%icon%%%';

  @override
  String get icon_reset_zz => '%%%icon%%%';

  @override
  String get icon_reset_zz_s => '%%%icon%%%';

  @override
  String get icon_reset => 'リセット';

  @override
  String get icon_reset_v => 'リセット';

  @override
  String get icon_00014_zz => '%%%icon%%%';

  @override
  String get icon_00014_zz_s => '%%%icon%%%';

  @override
  String get icon_00014 => 'メモリー';

  @override
  String get icon_save => 'データ保存';

  @override
  String get icon_00015_zz => '%%%icon%%%';

  @override
  String get icon_00015_zz_s => '%%%icon%%%';

  @override
  String get icon_util_mem_retrieve => '呼び出し';

  @override
  String get icon_util_mem_memory => '記憶';

  @override
  String get icon_util_mem_reset => 'リセット';

  @override
  String get icon_util_mem_delete => '削除';

  @override
  String get icon_util_mem_alldelete => '全削除';

  @override
  String get icon_00017_zz => '振り幅';

  @override
  String get icon_00017_zz_s => '振り幅';

  @override
  String get icon_00018_zz => 'ぬい目の長さ';

  @override
  String get icon_00018_zz_s => 'ぬい目の長さ';

  @override
  String get icon_00019_zz => '糸調子';

  @override
  String get icon_00019_zz_s => '糸調子';

  @override
  String get icon_00020_zz => 'L/Rシフト';

  @override
  String get icon_00020_zz_s => 'L/Rシフト';

  @override
  String get icon_util_width => '振り幅';

  @override
  String get icon_util_length => '長さ';

  @override
  String get icon_util_lrshift => 'L/Rシフト';

  @override
  String get icon_util_tension => '糸調子';

  @override
  String get icon_util_slitlength => 'スリット';

  @override
  String get icon_00021_zz => '%%%icon%%%';

  @override
  String get icon_00021_zz_s => '%%%icon%%%';

  @override
  String get icon_00022_zz => '%%%icon%%%';

  @override
  String get icon_00022_zz_s => '%%%icon%%%';

  @override
  String get icon_00027_zz => '%%%icon%%%';

  @override
  String get icon_00027_zz_s => '%%%icon%%%';

  @override
  String get icon_00028_zz => '%%%icon%%%';

  @override
  String get icon_00028_zz_s => '%%%icon%%%';

  @override
  String get icon_00029_zz => '%%%icon%%%';

  @override
  String get icon_00029_zz_s => '%%%icon%%%';

  @override
  String get icon_00038_zz => '%%%icon%%%';

  @override
  String get icon_00038_zz_s => '%%%icon%%%';

  @override
  String get icon_00030_1 => '試しぬい';

  @override
  String get icon_guidel_guideline => 'ガイドライン';

  @override
  String get icon_guidel_main => 'メイン';

  @override
  String get icon_guidel_sub => 'サブ';

  @override
  String get icon_guidel_mainline => 'メインライン';

  @override
  String get icon_guidel_subline => 'サブライン';

  @override
  String get icon_guidel_linelength => '線の長さ';

  @override
  String get icon_guidel_line_l => 'L';

  @override
  String get icon_guidel_line_m => 'M';

  @override
  String get icon_guidel_line_s => 'S';

  @override
  String get icon_guidel_color => '色';

  @override
  String get icon_guidel_position => '位置';

  @override
  String get icon_guidel_main_pos => 'メインライン位置';

  @override
  String get icon_guidel_sub_pos => 'サブライン位置';

  @override
  String get icon__guidel_sub_frommain => 'メインラインからの距離';

  @override
  String get icon_guidel_gridsize => '格子の間隔';

  @override
  String get icon_guidel_angle => '角度';

  @override
  String get icon_guidel_seamallowance => '縫い代幅';

  @override
  String get icon_guidel_spacing => '間隔';

  @override
  String get icon_guidel_lengthl_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengthl_zz_s => '%%%icon%%%';

  @override
  String get icon_guidel_lengthm_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengthm_zz_s => '%%%icon%%%';

  @override
  String get icon_guidel_lengths_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengths_zz_s => '%%%icon%%%';

  @override
  String get icon_position => '位置';

  @override
  String get icon_00031_2 => '編集';

  @override
  String get icon_00033_1 => '追加';

  @override
  String get icon_00035 => '縫製';

  @override
  String get icon_return => '戻る';

  @override
  String get icon_00038_1 => 'セット';

  @override
  String get icon_00038_2 => 'セット';

  @override
  String get icon_00039 => 'min';

  @override
  String get icon_00041_1 => '選択';

  @override
  String get icon_select => '選択';

  @override
  String get icon_select_2 => '選択';

  @override
  String get icon_00041_2 => '模様選択';

  @override
  String get icon_00042 => '刺しゅう機を\n取り外す前に\n必ず押してください。';

  @override
  String get icon_00046_zz => '%%%icon%%%';

  @override
  String get icon_00046_zz_s => '%%%icon%%%';

  @override
  String get icon_00048 => '数字\n入力';

  @override
  String get icon_00049 => 'リスト選択';

  @override
  String get icon_00050 => '取り込み';

  @override
  String get icon_00051_zz => '%%%icon%%%';

  @override
  String get icon_00051_zz_s => '%%%icon%%%';

  @override
  String get icon_00052_zz => '%%%icon%%%';

  @override
  String get icon_00052_zz_s => '%%%icon%%%';

  @override
  String get icon_00053_b1 => '%%%none%%%';

  @override
  String get icon_00053_b2 => '%%%none%%%';

  @override
  String get icon_00053_t1 => 'Color Visualizer';

  @override
  String get icon_00053_t2 => 'Color\nVisualizer';

  @override
  String get icon_00055_1 => 'ランダム';

  @override
  String get icon_00055_2 => 'ランダム';

  @override
  String get icon_00056_1 => 'グラデーション';

  @override
  String get icon_00056_2 => 'グラデーション';

  @override
  String get icon_00057 => 'ビビッド';

  @override
  String get icon_00054 => 'ソフト';

  @override
  String get icon_00058_1 => '色数';

  @override
  String get icon_00059 => 'リフレッシュ';

  @override
  String get icon_00060 => '指定なし';

  @override
  String get icon_emb_tension => '糸調子';

  @override
  String get icon_emb_threadcutting => '糸切り';

  @override
  String get icon_00063_a => '自動糸切り';

  @override
  String get icon_00064_a => '渡り糸切り';

  @override
  String get icon_00065 => '糸密度';

  @override
  String get icon_00066 => '大きさ';

  @override
  String get icon_00067_zz => '%%%icon%%%';

  @override
  String get icon_00067_zz_s => '%%%icon%%%';

  @override
  String get icon_00068_zz => '%%%icon%%%';

  @override
  String get icon_00068_zz_s => '%%%icon%%%';

  @override
  String get icon_00070_zz => '%%%icon%%%';

  @override
  String get icon_00070_zz_s => '%%%icon%%%';

  @override
  String get icon_00071_zz => '確認';

  @override
  String get icon_00071_zz_s => '確認';

  @override
  String get icon_00072 => 'レイアウト';

  @override
  String get icon_00075_zz => '%%%icon%%%';

  @override
  String get icon_00075_zz_s => '%%%icon%%%';

  @override
  String get icon_00076_zz => '%%%icon%%%';

  @override
  String get icon_00076_zz_s => '%%%icon%%%';

  @override
  String get icon_00077_zz => '%%%icon%%%';

  @override
  String get icon_00077_zz_s => '%%%icon%%%';

  @override
  String get icon_00079 => '針落ち点';

  @override
  String get icon_00080 => '次へ';

  @override
  String get icon_prev => '前へ';

  @override
  String get icon_segment => 'パーツ';

  @override
  String get icon_00083 => 'ぬい終わり位置設定';

  @override
  String get icon_00084 => '模様長さ調整';

  @override
  String get icon_00085 => 'ぬい終わり位置設定\n一時停止';

  @override
  String get icon_00088 => 'スキャン';

  @override
  String get icon_00089 => '動画';

  @override
  String get icon_00090 => '繰り返し';

  @override
  String get icon_00091_1 => '複数選択';

  @override
  String get icon_00091_2 => '複数選択';

  @override
  String get icon_00093_zz => '%%%icon%%%';

  @override
  String get icon_00093_zz_s => '%%%icon%%%';

  @override
  String get icon_00094_zz => '%%%icon%%%';

  @override
  String get icon_00094_zz_s => '%%%icon%%%';

  @override
  String get icon_00095 => '終了';

  @override
  String get icon_00096 => 'キルトボーダー／コーナー分割';

  @override
  String get icon_resettodef => '初期設定にリセット';

  @override
  String get icon_resettodefall => '初期状態にリセット';

  @override
  String get icon_resettodefall_2 => '初期状態にリセット';

  @override
  String get icon_00100 => '言語';

  @override
  String get icon_00101_a => '日本語';

  @override
  String get icon_00101_b => 'Japanese';

  @override
  String get icon_00102 => '明るさ';

  @override
  String get icon_00103 => '画面の明るさ';

  @override
  String get icon_00104 => 'スクリーンセーバー';

  @override
  String get icon_00105 => '標準';

  @override
  String get icon_00106 => 'ユーザー設定';

  @override
  String get icon_00107 => 'エコモード';

  @override
  String get icon_00108 => '電源オフサポートモード';

  @override
  String get icon_00109 => '照明';

  @override
  String get icon_00112 => 'ミシンスピーカーボリューム';

  @override
  String get icon_00114 => 'ボリューム';

  @override
  String get icon_00115 => 'マウスポインター';

  @override
  String get icon_00116 => 'mm / \"(inch)';

  @override
  String get icon_00118 => 'ホーム';

  @override
  String get icon_00119 => '実用 / 刺しゅう';

  @override
  String get icon_00192 => '起動画面';

  @override
  String get icon_00121 => 'オープニング';

  @override
  String get icon_00122 => '初期ステッチ画面';

  @override
  String get icon_00123 => '';

  @override
  String get icon_00124 => '振り幅コントロール';

  @override
  String get icon_00125_1 => '模様調整たて';

  @override
  String get icon_00126_1 => '模様調整よこ';

  @override
  String get icon_00127_1 => '押え高さ';

  @override
  String get icon_00128_1 => '押え圧';

  @override
  String get icon_00129 => '基線切り替え';

  @override
  String get icon_00130_1 => 'ピボット押え高さ';

  @override
  String get icon_00131_1 => 'フリーモーション押え高さ';

  @override
  String get icon_00134 => '自動押え圧補正';

  @override
  String get icon_00135 => '布厚センサー';

  @override
  String get icon_00136_2 => '針位置 - 上/下';

  @override
  String get icon_00137 => '針位置 - ぬい位置';

  @override
  String get icon_00138 => '上糸・下糸センサー';

  @override
  String get icon_00140 => 'デュアルフィード送り量調整';

  @override
  String get icon_00141 => 'マルチ機能フットコントローラー';

  @override
  String get icon_00142 => 'ヒールキック';

  @override
  String get icon_00143 => 'サイドペダル';

  @override
  String get icon_00144_a => '針上下';

  @override
  String get icon_00145 => '糸切り';

  @override
  String get icon_00146 => '1針ぬい';

  @override
  String get icon_00147 => '返しぬい';

  @override
  String get icon_00243 => '押え上下';

  @override
  String get icon_00244 => '指定なし';

  @override
  String get icon_00249 => '止めぬい';

  @override
  String get icon_00148 => '穴溝長さ';

  @override
  String get icon_00148_zz => '%%%icon%%%';

  @override
  String get icon_00148_zz_s => '%%%icon%%%';

  @override
  String get icon_00150 => '止めぬい優先設定';

  @override
  String get icon_00152_1 => '刺しゅう枠表示';

  @override
  String get icon_00155_1 => '模様キーの大きさ';

  @override
  String get icon_00157 => '縫製エリア背景色';

  @override
  String get icon_00159 => '模様キー背景色';

  @override
  String get icon_00163_a => '背景画像表示';

  @override
  String get icon_00163 => '背景画像';

  @override
  String get icon_00164 => 'スキャン画像';

  @override
  String get icon_00165 => '標準';

  @override
  String get icon_00166 => 'きれい';

  @override
  String get icon_00167 => 'スキャン画質';

  @override
  String get icon_00168 => '糸立てLED';

  @override
  String get icon_00178 => '刺しゅうしつけ距離';

  @override
  String get icon_00180 => 'アップリケ距離';

  @override
  String get icon_00182_1 => '刺しゅうの最高速度';

  @override
  String get icon_00183_1 => '刺しゅうの糸調子調整';

  @override
  String get icon_00184_1 => '刺しゅうの押え高さ';

  @override
  String get icon_00185 => '枠サイズ';

  @override
  String get icon_00186 => 'グリッド';

  @override
  String get icon_00187 => '変更';

  @override
  String get icon_00188 => '削除';

  @override
  String get icon_00191 => '色';

  @override
  String get icon_00193 => '時計表示';

  @override
  String get icon_00194 => 'AM';

  @override
  String get icon_00195 => 'PM';

  @override
  String get icon_00196 => '24h';

  @override
  String get icon_clock_msg1 => 'ネットワーク接続のため、正しい日付を設定してください。';

  @override
  String get icon_00197 => '超音波センサー\nキャリブレーション';

  @override
  String get icon_00199 => 'ガイドラインマーカー調整';

  @override
  String get icon_00200 => 'ガイドラインマーカーの明るさ';

  @override
  String get icon_00201_1 => 'LEDポインター付き\n刺しゅう押え調整';

  @override
  String get icon_00202_p => '明るさ';

  @override
  String get icon_00206_1 => '認証';

  @override
  String get icon_00207_a => 'Kit';

  @override
  String get icon_00208 => 'スタート';

  @override
  String get icon_00209 => 'ストップ';

  @override
  String get icon_00211 => 'サービスカウント';

  @override
  String get icon_00212 => 'SCS';

  @override
  String get icon_00214 => 'トータル針数';

  @override
  String get icon_00218 => 'No.';

  @override
  String get icon_00220 => 'バージョン';

  @override
  String get icon_00222 => 'YYYY';

  @override
  String get icon_00223 => 'MM';

  @override
  String get icon_00224 => 'DD';

  @override
  String get icon_00225 => 'mm';

  @override
  String get icon_00226 => '\"';

  @override
  String get icon_on => 'ON';

  @override
  String get icon_off => 'OFF';

  @override
  String get icon_00229 => 'KB';

  @override
  String get icon_00230 => 'bPocket';

  @override
  String get icon_00231 => '1';

  @override
  String get icon_00232 => '2';

  @override
  String get icon_00233 => '3';

  @override
  String get icon_00234 => '4';

  @override
  String get icon_00235 => '5';

  @override
  String get icon_00236 => '6';

  @override
  String get icon_00237 => '7';

  @override
  String get icon_00238 => '8';

  @override
  String get icon_00239 => '9';

  @override
  String get icon_00240 => '0';

  @override
  String get icon_00241 => 'C';

  @override
  String get icon_00242 => '%';

  @override
  String get icon_00245 => '自動押え上下';

  @override
  String get icon_00246 => '自動下げ';

  @override
  String get icon_00247 => '自動上げ';

  @override
  String get icon_00248_zz => '%%%icon%%%';

  @override
  String get icon_00248_zz_s => '%%%icon%%%';

  @override
  String get icon_00248 => '糸切り時';

  @override
  String get icon_00251 => 'プロジェクター';

  @override
  String get icon_00253 => '背景色';

  @override
  String get icon_00254 => '実用：\n模様のアウトライン';

  @override
  String get icon_00255 => '刺しゅう：\nポインターの色';

  @override
  String get icon_pointershape => 'ポインターの形状';

  @override
  String get icon_00256 => 'カメラ';

  @override
  String get icon_00257 => 'カメラ/プロジェクターの針位置調整';

  @override
  String get icon_recog_ok => 'OK';

  @override
  String get icon_recog_ng => 'NG';

  @override
  String get icon_00258 => '刺しゅうの針停止位置';

  @override
  String get icon_00259 => '単位';

  @override
  String get icon_00260 => '糸色';

  @override
  String get icon_00261 => '糸ブランド';

  @override
  String get icon_00264 => '糸色名';

  @override
  String get icon_00265 => '# 123';

  @override
  String get icon_00266 => '時間';

  @override
  String get icon_00268 => 'オリジナル';

  @override
  String get icon_00269 => 'ブラザー';

  @override
  String get icon_00269_t => 'Embroidery';

  @override
  String get icon_00270 => 'カントリー';

  @override
  String get icon_00270_t => 'Country';

  @override
  String get icon_00271 => 'Madeira\nPoly';

  @override
  String get icon_00272 => 'Madeira\nRayon';

  @override
  String get icon_00273 => 'Sulky';

  @override
  String get icon_00274 => 'Robison-Anton';

  @override
  String get icon_00275 => 'Robison-Anton\nPoly';

  @override
  String get icon_00276 => 'Robison-Anton\nRayon';

  @override
  String get icon_00277 => 'Isacord';

  @override
  String get icon_00278 => 'Gütermann';

  @override
  String get icon_00279 => 'Simplicity Pro';

  @override
  String get icon_00279_p => 'Pacesetter Pro';

  @override
  String get icon_00280 => 'Floriani';

  @override
  String get icon_00281 => 'Iris';

  @override
  String get icon_00282 => 'Aurifil';

  @override
  String get icon_00283 => 'WonderFil ';

  @override
  String get icon_00284 => 'Polyfast';

  @override
  String get icon_00290 => 'ご購入されたアップグレードキットの機能を有効にさせたい場合は[認証]キーを押して下さい。';

  @override
  String get icon_00291 => 'KIT I';

  @override
  String get icon_00292 => 'KIT II';

  @override
  String get icon_00293 => 'KIT III';

  @override
  String get icon_00294 => 'KIT IV';

  @override
  String get icon_00295 => 'KIT V';

  @override
  String get icon_00296 => 'KIT VI';

  @override
  String get icon_00297 => 'KIT VII';

  @override
  String get icon_00298 => 'KIT VIII';

  @override
  String get icon_00299 => 'KIT IX';

  @override
  String get icon_00300 => 'KIT X';

  @override
  String get icon_00643_s => 'なし';

  @override
  String get icon_00301 => '使い方';

  @override
  String get icon_00302 => 'ぬい方';

  @override
  String get icon_00303 => '模様説明';

  @override
  String get icon_manuals => '説明書';

  @override
  String get icon_operariong_b => '取扱説明書';

  @override
  String get icon_operariong_t => '取扱説明書 (English)';

  @override
  String get icon_pdf => 'PDFマニュアル';

  @override
  String get icon_supportsite => 'サポートサイト';

  @override
  String get icon_pdf_eula => 'エンドユーザー使用許諾契約';

  @override
  String get icon_pdf_sewing => '実用編';

  @override
  String get icon_pdf_emb => '刺しゅう編';

  @override
  String get icon_pdf_sewing_ef => '実用編 (English)';

  @override
  String get icon_pdf_emb_ef => '刺しゅう編 (English)';

  @override
  String get icon_pdf_sewing_t => '実用編 (English)';

  @override
  String get icon_pdf_emb_t => '刺しゅう編 (English)';

  @override
  String get icon_f_omadendum => '別冊';

  @override
  String get icon_f_omadendum_ef => '別冊';

  @override
  String get icon_f_omadendum_l => '取扱説明書・別冊';

  @override
  String get icon_f_om_kit1 => 'KIT I';

  @override
  String get icon_f_om_kit2 => 'KIT II';

  @override
  String get icon_f_om_kit3 => 'KIT III';

  @override
  String get icon_f_om_kit1_l => '取扱説明書\nKIT I';

  @override
  String get icon_f_omadendum_t => '別冊';

  @override
  String get icon_f_om_kit1_t => 'KIT I';

  @override
  String get icon_f_om_kit2_t => 'KIT II';

  @override
  String get icon_f_om_kit3_t => 'KIT III';

  @override
  String get icon_t_pdf_iivo_url_b => 'モバイル端末やパソコンで説明書を閲覧したいときは、\nhttps://s.brother/fmraaにアクセスしてください。';

  @override
  String get icon_t_pdf_iivo_url_t => 'モバイル端末やパソコンで説明書を閲覧したいときは、\nhttps://babylock.com/radiance-instruction-and-reference-guideにアクセスしてください。';

  @override
  String get icon_t_video_iivo_url_b => 'https://s.brother/fvraa\nにアクセスしてください。\nこのモデルのチュートリアルビデオをご覧いただけます。';

  @override
  String get icon_t_video_iivo_url_t => 'https://babylock.com/radiance-training\nにアクセスしてください。\nこのモデルのチュートリアルビデオをご覧いただけます。';

  @override
  String get icon_pdf_url_qr_t => 'www.babylock.com';

  @override
  String get icon_nettool => 'ネットワーク診断ツール';

  @override
  String get icon_iagree => '同意します';

  @override
  String get icon_terms_cancel => 'キャンセル';

  @override
  String get icon_confirm => '確認';

  @override
  String get icon_00304 => '各部の名称とはたらき';

  @override
  String get icon_00305 => 'おもなボタン';

  @override
  String get icon_00306 => '基本操作';

  @override
  String get icon_00307 => '刺しゅうの基本操作';

  @override
  String get icon_00308 => 'トラブルチェック';

  @override
  String get icon_00309 => 'お手入れ';

  @override
  String get icon_00310 => '糸が布の裏側でからまっている';

  @override
  String get icon_00311 => '糸通しができない';

  @override
  String get icon_00312 => '自動糸通しができない';

  @override
  String get icon_00313 => '糸調子が合わない';

  @override
  String get icon_00314 => '上糸が切れる';

  @override
  String get icon_00315 => '下糸が切れる';

  @override
  String get icon_00316 => 'ぬい目が飛ぶ';

  @override
  String get icon_00317 => '針が折れる';

  @override
  String get icon_00318 => 'ミシンが動かない';

  @override
  String get icon_00320 => '文字模様がくずれる';

  @override
  String get icon_00321 => '布地を送らない';

  @override
  String get icon_00322 => '布地にしわがよる';

  @override
  String get icon_00323 => 'ぬい音が高い\n(ガタガタと音がする)';

  @override
  String get icon_00325 => '刺しゅう模様がくずれる';

  @override
  String get icon_00326 => '刺しゅう機が動かない';

  @override
  String get icon_00331 => 'かんどめ';

  @override
  String get icon_00332 => 'まつりぬい';

  @override
  String get icon_00333 => 'ボタン穴かがり';

  @override
  String get icon_00334 => 'ボタンつけ';

  @override
  String get icon_00335 => 'ダーツ';

  @override
  String get icon_00336 => '折りふせぬい';

  @override
  String get icon_00337 => 'ギャザー';

  @override
  String get icon_00338 => 'たちめかがり';

  @override
  String get icon_00339 => 'ピンタック';

  @override
  String get icon_00340 => 'スカラップ';

  @override
  String get icon_00341 => '地ぬい';

  @override
  String get icon_00342 => 'ファスナーつけ';

  @override
  String get icon_00343 => 'ピーシング';

  @override
  String get icon_00344 => 'フリーモーションキルト';

  @override
  String get icon_00345 => 'キルト';

  @override
  String get icon_00346 => 'エコーキルト';

  @override
  String get icon_00347 => 'アップリケ 1';

  @override
  String get icon_00348 => 'アップリケ 2';

  @override
  String get icon_search => '検索';

  @override
  String get icon_00353 => '上糸通し';

  @override
  String get icon_00354 => '下糸巻き';

  @override
  String get icon_00355 => '針の交換';

  @override
  String get icon_00356 => '押えの交換';

  @override
  String get icon_00357 => '下糸をセットする';

  @override
  String get icon_00358 => '実用ぬい機能';

  @override
  String get icon_00359 => '糸切りの設定の仕方';

  @override
  String get icon_00360 => '特別ネジ回しの使い方';

  @override
  String get icon_00361 => 'ピボット機能の使い方';

  @override
  String get icon_00362 => '実用ぬいの振り幅とぬい目の長さの合わせ方';

  @override
  String get icon_00363 => '多機能ドライバーの使い方';

  @override
  String get icon_00364 => '自動押え圧補正の説明';

  @override
  String get icon_00365 => 'マイイラストの使い方';

  @override
  String get icon_00366 => '端縫い機能の使い方';

  @override
  String get icon_00367 => 'ボビンワークでぬう(実用)';

  @override
  String get icon_00368 => 'ボビンワークでぬう(刺しゅう)';

  @override
  String get icon_00369 => 'ボビンワークの準備';

  @override
  String get icon_00370 => '反転BWの本体のガイドの付け方';

  @override
  String get icon_00371 => '反転BWの使い方';

  @override
  String get icon_00372 => '実用画面でカメラ画像を表示する';

  @override
  String get icon_00373 => 'ガイドラインマーカーの位置を調整する';

  @override
  String get icon_00374 => 'ガイドラインマーカーの明るさを調整する';

  @override
  String get icon_00375 => '糸調子を調節する';

  @override
  String get icon_00376 => '布地にアイロンで接着芯を貼る';

  @override
  String get icon_00377 => '刺しゅう枠に布地を張る';

  @override
  String get icon_00378 => '刺しゅう枠のセットの仕方';

  @override
  String get icon_00379 => '刺しゅう機交換(補助テーブル→刺しゅう機) ';

  @override
  String get icon_00380 => '刺しゅう押さえ交換(実用押さえ→刺しゅう押さえ)';

  @override
  String get icon_00381 => '刺しゅう機能';

  @override
  String get icon_00382 => 'プリント＆ステッチのやり方';

  @override
  String get icon_00383 => 'カラーシャッフルのやり方';

  @override
  String get icon_00384 => 'マイデザインセンターの使い方';

  @override
  String get icon_00385 => 'ラインスキャンのやり方';

  @override
  String get icon_00386 => 'イラストスキャンのやり方';

  @override
  String get icon_00387 => 'スキャン枠への紙のセットの仕方';

  @override
  String get icon_00388 => 'カメラで布地をスキャンして画面に表示する(背景スキャン)';

  @override
  String get icon_00389 => '位置合わせマークによる刺しゅう位置合わせ';

  @override
  String get icon_00390 => 'カメラを使って模様をつなぐ';

  @override
  String get icon_00391 => 'カメラを使って刺しゅう模様を配置する';

  @override
  String get icon_00392 => 'グリッド線を使った位置あわせ';

  @override
  String get icon_00393 => '設定';

  @override
  String get icon_00394 => 'カメラの針落ち点調整';

  @override
  String get icon_00395 => 'アップグレードのやり方';

  @override
  String get icon_00396 => '設定画面でのガイドラインマーカーの位置・明るさ調整';

  @override
  String get icon_00397 => '時計の合わせ方';

  @override
  String get icon_00398 => '自動止め縫いの方法';

  @override
  String get icon_00399 => 'その他';

  @override
  String get icon_00400 => '動画の見方・保存の仕方';

  @override
  String get icon_00401 => 'センサーペン';

  @override
  String get icon_00402 => 'センサーペンの本体へのセットの仕方';

  @override
  String get icon_00403 => 'センサーペンのキャリブレーション';

  @override
  String get icon_00404 => 'センサーペンのガイドラインマーカーの位置設定';

  @override
  String get icon_00405 => 'センサーペンの針落ち位置設定';

  @override
  String get icon_00406 => 'センサーペンのふり幅縫製位置の設定';

  @override
  String get icon_00407 => 'センサーペンの終点設定';

  @override
  String get icon_00408 => 'センサーペンの刺しゅう位置設定';

  @override
  String get icon_00409 => 'アクセサリー';

  @override
  String get icon_00410 => 'ニーリフターの調整・付け方';

  @override
  String get icon_00411 => '多機能ドライバーの使い方';

  @override
  String get icon_00412 => '多機能ドライバーの使い方';

  @override
  String get icon_00416 => '多機能フットコントローラーのセットの仕方';

  @override
  String get icon_00417 => '多機能フットコントローラーの機能割り当て方法';

  @override
  String get icon_00418 => 'LEDポインターの付け方';

  @override
  String get icon_00419 => 'LEDポインターの調整';

  @override
  String get icon_00420 => 'カメラを使ってスティップリング刺しゅうをする';

  @override
  String get icon_00421 => '高さ調整用アダプタを使って押えを装着する';

  @override
  String get icon_00422 => 'アクセサリーケースの使い方';

  @override
  String get icon_00423 => '手入れ(カマ掃除)';

  @override
  String get icon_00500 => 'My Design Center';

  @override
  String get icon_00500_2 => 'My Design\nCenter';

  @override
  String get icon_iqdesigner => 'IQ Designer';

  @override
  String get icon_00501 => 'Line Scan';

  @override
  String get icon_00503_zz => '%%%icon%%%';

  @override
  String get icon_00503_zz_s => '%%%icon%%%';

  @override
  String get icon_00505 => 'Illustration Scan';

  @override
  String get icon_imagescan => '画像スキャン';

  @override
  String get icon_linedesign => 'ラインデザイン';

  @override
  String get icon_illustrationdesign => 'イラストデザイン';

  @override
  String get icon_00509_zz => '%%%icon%%%';

  @override
  String get icon_00510 => '認識';

  @override
  String get icon_00511_1 => 'プレビュー';

  @override
  String get icon_00511_2 => 'プレビュー';

  @override
  String get icon_showpreview => 'プレビューを表示する';

  @override
  String get icon_00512 => '再試行';

  @override
  String get icon_00514 => '認識最小サイズ';

  @override
  String get icon_00516 => '２値化レベル(２階調化)';

  @override
  String get icon_00503 => '線';

  @override
  String get icon_00518 => '消しゴム';

  @override
  String get icon_00520 => '原画表示';

  @override
  String get icon_00521 => '結果表示';

  @override
  String get icon_00522 => '結果表示';

  @override
  String get icon_00523 => '最大色数';

  @override
  String get icon_00525 => '背景を取り除く';

  @override
  String get icon_00526 => '認識';

  @override
  String get icon_00528 => '縫い種設定';

  @override
  String get icon_00529 => '線プロパティ';

  @override
  String get icon_00530 => '面プロパティ';

  @override
  String get icon_00533 => 'サイズ';

  @override
  String get icon_00537 => 'ジグザグ幅';

  @override
  String get icon_00538 => '糸密度';

  @override
  String get icon_00539 => '走りピッチ';

  @override
  String get icon_00540 => 'タタミ縫い';

  @override
  String get icon_00541 => '縫い角度';

  @override
  String get icon_00544 => '縮み補正';

  @override
  String get icon_00545 => '下打ち';

  @override
  String get icon_00547 => '間隔';

  @override
  String get icon_00548_1 => '手動';

  @override
  String get icon_00548_2 => '手動';

  @override
  String get icon_00549_1 => '自動';

  @override
  String get icon_00549_2 => '自動';

  @override
  String get icon_00550 => 'ステッチ変換';

  @override
  String get icon_00551 => '範囲指定';

  @override
  String get icon_00552 => '色指定';

  @override
  String get icon_00553 => '次へ';

  @override
  String get icon_00554 => '距離';

  @override
  String get icon_00555 => 'アウトラインの保存';

  @override
  String get icon_00556 => '閉じた図形';

  @override
  String get icon_00557 => '開いた図形';

  @override
  String get icon_00558 => '保存したアウトライン';

  @override
  String get icon_00559 => '枠サイズ';

  @override
  String get icon_00562 => 'アウトライン';

  @override
  String get icon_00564 => '重なり';

  @override
  String get icon_00565 => 'ゆらぎ';

  @override
  String get icon_00566 => '位置のオフセット';

  @override
  String get icon_inside => '内側';

  @override
  String get icon_outside => '外側';

  @override
  String get icon_00567 => '反転';

  @override
  String get icon_00568 => 'ステッチ幅';

  @override
  String get icon_00569 => '現在の画像';

  @override
  String get icon_00570 => '新規画像';

  @override
  String get icon_frame_297_465_mm => '297 × 465 mm';

  @override
  String get icon_frame_297_465_inch => '11-5/8\"× 18-1/4\"';

  @override
  String get icon_frame_272_408_mm => '272 × 408 mm';

  @override
  String get icon_frame_272_408_inch => '10-5/8\"× 16\"';

  @override
  String get icon_frame_254_254_mm => '254 × 254 mm';

  @override
  String get icon_frame_254_254_inch => '10\"× 10\"';

  @override
  String get icon_frame_240_360_mm => '240 × 360 mm';

  @override
  String get icon_frame_240_360_inch => '9-1/2\"× 14\"';

  @override
  String get icon_frame_180_360_mm => '180 × 360 mm';

  @override
  String get icon_frame_180_360_inch => ' 7\" × 14\"';

  @override
  String get icon_frame_180_300_mm => '180 × 300 mm';

  @override
  String get icon_frame_180_300_inch => ' 7\" × 12\"';

  @override
  String get icon_frame_200_300_mm => '200 × 300 mm';

  @override
  String get icon_frame_200_300_inch => '8\"×12\"';

  @override
  String get icon_frame_100_300_mm => '100 × 300 mm';

  @override
  String get icon_frame_100_300_inch => '4\"× 12\"';

  @override
  String get icon_frame_160_260_mm => '160 × 260 mm';

  @override
  String get icon_frame_160_260_inch => '6-1/4\"× 10-1/4\"';

  @override
  String get icon_frame_240_240_mm => '240 × 240 mm';

  @override
  String get icon_frame_240_240_inch => '9-1/2\"× 9-1/2\"';

  @override
  String get icon_frame_200_200_mm => '200 × 200 mm';

  @override
  String get icon_frame_200_200_inch => '8\"× 8\"';

  @override
  String get icon_frame_130_180_mm => '130 × 180 mm';

  @override
  String get icon_frame_130_180_inch => '5\"× 7\"';

  @override
  String get icon_frame_100_180_mm => '100 × 180 mm';

  @override
  String get icon_frame_100_180_inch => '4\"× 7\"';

  @override
  String get icon_frame_150_150_mm => '150 × 150 mm';

  @override
  String get icon_frame_150_150_inch => '6\"× 6\"';

  @override
  String get icon_frame_100_100_mm => '100 × 100 mm';

  @override
  String get icon_frame_100_100_inch => '4\"× 4\"';

  @override
  String get icon_frame_60_20_mm => '60 × 20 mm';

  @override
  String get icon_frame_60_20_inch => '2-3/8\"× 3/4\"';

  @override
  String get icon_zoom_50 => '50';

  @override
  String get icon_zoom_100 => '100';

  @override
  String get icon_zoom_125 => '125';

  @override
  String get icon_zoom_150 => '150';

  @override
  String get icon_zoom_200 => '200';

  @override
  String get icon_zoom_400 => '400';

  @override
  String get icon_zoom_800 => '800';

  @override
  String get icon_zoom_120 => '120';

  @override
  String get icon_zoom_240 => '240';

  @override
  String get icon_zoom_480 => '480';

  @override
  String get icon_zoom_960 => '960';

  @override
  String get icon_00600 => '無線LAN有効';

  @override
  String get icon_00600_1 => '無線LAN有効';

  @override
  String get icon_00601 => 'SSID';

  @override
  String get icon_00602 => 'SSID 選択...';

  @override
  String get icon_00603 => 'マシン名';

  @override
  String get icon_00604 => 'WPS (プッシュ)';

  @override
  String get icon_00605 => 'WPS (Pin)';

  @override
  String get icon_00606 => 'その他';

  @override
  String get icon_00608 => '無線状態';

  @override
  String get icon_00608_1 => '無線状態';

  @override
  String get icon_00609 => '保存SSID';

  @override
  String get icon_00609_1 => '保存SSID';

  @override
  String get icon_00610 => '新SSID';

  @override
  String get icon_wlan_title => '無線LAN';

  @override
  String get icon_wlan_connection => '無線LAN接続';

  @override
  String get icon_wlan_networks => '無線LANネットワーク';

  @override
  String get icon_wlan_enable => '無線LAN有効';

  @override
  String get icon_wlan_setinfo_01 => '利用可能なネットワークを確認するには、無線 LAN有効をオンにします。';

  @override
  String get icon_wlan_setinfo_02 => '無線LANネットワークを検索中…';

  @override
  String get icon_wlan_setinfo_03 => '無線LANに接続中…';

  @override
  String get icon_wlan_setinfo_05 => '無線LANをオンにしています…';

  @override
  String get icon_wlan_setinfo_06 => '無線LANがオンされました';

  @override
  String get icon_wlan_setinfo_04 => '無線LANを切断中…';

  @override
  String get icon_wlan_setinfo_07 => '以下を含む、すべてのネットワーク設定がリセットされます。·無線LAN';

  @override
  String get icon_wlan_networkreset => 'ネットワーク設定リセット';

  @override
  String get icon_wlan_limitedconnect => 'ネットワークに接続できません。時計の設定を確認してください。';

  @override
  String get icon_00630 => 'ネットワーク';

  @override
  String get icon_00631 => '無線接続ウィザード';

  @override
  String get icon_00631_1 => '無線接続ウィザード';

  @override
  String get icon_00632 => '詳細';

  @override
  String get icon_00633 => '接続状態';

  @override
  String get icon_00634 => '電波状態';

  @override
  String get icon_00635 => '通信モード';

  @override
  String get icon_00636 => 'アクティブ(11b)';

  @override
  String get icon_00637 => 'アクティブ(11g)';

  @override
  String get icon_00638 => 'アクティブ(11n)';

  @override
  String get icon_00639 => '接続に失敗しました';

  @override
  String get icon_00640 => '強い';

  @override
  String get icon_00641 => '普通';

  @override
  String get icon_00642 => '弱い';

  @override
  String get icon_00643 => 'なし';

  @override
  String get icon_00644 => 'アドホック';

  @override
  String get icon_00645 => 'インフラストラクチャ';

  @override
  String get icon_00646 => 'TCP/IP';

  @override
  String get icon_00647 => 'MACアドレス';

  @override
  String get icon_00648 => 'プロキシ設定';

  @override
  String get icon_00649 => 'IP取得方法';

  @override
  String get icon_00650 => 'IP アドレス';

  @override
  String get icon_00651 => 'サブネット マスク';

  @override
  String get icon_00652 => 'ゲートウェイ';

  @override
  String get icon_00653 => 'ノード名';

  @override
  String get icon_00654 => 'WINS設定';

  @override
  String get icon_00655 => 'WINS サーバー';

  @override
  String get icon_00656 => 'DNS サーバー';

  @override
  String get icon_00656_p => 'DNS サーバー プライマリ';

  @override
  String get icon_00656_s => 'DNS サーバー セカンダリ';

  @override
  String get icon_00657 => 'APIPA';

  @override
  String get icon_00658 => 'プロキシ経由接続';

  @override
  String get icon_00659 => 'アドレス';

  @override
  String get icon_00660 => 'ポート';

  @override
  String get icon_00661 => 'ユーザー名';

  @override
  String get icon_00662 => 'パスワード入力';

  @override
  String get icon_00663 => 'プライマリ';

  @override
  String get icon_00664 => 'セカンダリ';

  @override
  String get icon_00665 => 'SSID検索中...';

  @override
  String get icon_00666 => 'アクセスポイントのSSID';

  @override
  String get icon_00667 => 'ネットワークキー';

  @override
  String get icon_00668 => 'はい';

  @override
  String get icon_00669 => 'いいえ';

  @override
  String get icon_00670 => '認証方法';

  @override
  String get icon_00671 => 'オープンシステム認証';

  @override
  String get icon_00672 => '共有キー認証';

  @override
  String get icon_00673 => 'WPA/WPA2-PSK';

  @override
  String get icon_00674 => '暗号化方法';

  @override
  String get icon_00674_a => '暗号化方法(オープンシステム認証)';

  @override
  String get icon_00674_c => '暗号化方法(共有キー認証)';

  @override
  String get icon_00675 => 'WEP';

  @override
  String get icon_00676 => 'AES';

  @override
  String get icon_00677 => 'TKIP';

  @override
  String get icon_00678 => '停止中';

  @override
  String get icon_00679 => 'Static';

  @override
  String get icon_00680 => 'Auto';

  @override
  String get icon_00681 => 'WPA';

  @override
  String get icon_00682 => '日付';

  @override
  String get icon_cert_key => '通常認証';

  @override
  String get icon_cert_web => 'オンラインミシン認証';

  @override
  String get icon_status_t => 'ステータス';

  @override
  String get icon_status_a1 => '未確認';

  @override
  String get icon_status_a2 => 'チェック中';

  @override
  String get icon_status_a3 => 'チェック済：すでに最新の状態です。';

  @override
  String get icon_status_a4 => 'サーバーに新しいプログラムがあります。';

  @override
  String get icon_status_b1 => 'ダウンロード未実施';

  @override
  String get icon_status_b2 => 'ダウンロード中';

  @override
  String get icon_status_b3 => 'ダウンロード完了';

  @override
  String get icon_cancel_downloading => 'ダウンロード中断処理中';

  @override
  String get icon_pause_downloading2 => 'ダウンロード一時停止中\nダウンロードを続行する場合は、再開ボタンを押してください';

  @override
  String get icon_status_c1 => '最新のプログラムが、まだインストールされていません。';

  @override
  String get icon_status_c2 => '最新のプログラムがインストールされています。';

  @override
  String get icon_app_dl_moniter => 'モニタリングアプリのダウンロード';

  @override
  String get icon_shape => '形状';

  @override
  String get icon_favorite => 'お気に入り';

  @override
  String get icon_sash_4section => '4分割';

  @override
  String get icon_sash_1direction => '１方向';

  @override
  String get icon_sash_1dtotal => '全パーツ数';

  @override
  String get icon_offset => 'オフセット';

  @override
  String get icon_startpoint => '始点';

  @override
  String get icon_endpoint => '終点';

  @override
  String get icon_embfootdwn => '刺しゅう押え自動下げ';

  @override
  String get icon_frame_272_272_mm => '272 × 272 mm';

  @override
  String get icon_frame_272_272_inch => '10-5/8\"× 10-5/8\"';

  @override
  String get icon_appguide_w => 'アプリガイド';

  @override
  String get icon_appguide => 'アプリガイド';

  @override
  String get icon_mobileapp => 'モバイルアプリ';

  @override
  String get icon_app => 'アプリ';

  @override
  String get icon_emb1 => '刺しゅう 1';

  @override
  String get icon_emb2 => '刺しゅう 2';

  @override
  String get icon_00185_2 => '枠サイズ';

  @override
  String get icon_type => 'タイプ';

  @override
  String get icon_typea => 'タイプA';

  @override
  String get icon_typeb => 'タイプB';

  @override
  String get icon_typec => 'タイプC';

  @override
  String get icon_sash_typesplit => '分割タイプ';

  @override
  String get icon_mystitchmonitor => 'My Stitch Monitor';

  @override
  String get icon_mydesignsnap => 'My Design Snap';

  @override
  String get icon_mystitchmonitor_t => 'IQ Intuition Monitoring';

  @override
  String get icon_mydesignsnap_t => 'IQ Intuition Positioning';

  @override
  String get icon_actcode => 'アクティベーションコード';

  @override
  String get icon_machineno => 'マシン番号 (No.)';

  @override
  String get icon_autodl => '自動ダウンロード';

  @override
  String get icon_updatemanu => '手動アップデート';

  @override
  String get icon_dl_updateprogram => '更新プログラムダウンロード';

  @override
  String get icon_dl_updateprogram_2 => '更新プログラムダウンロード';

  @override
  String get icon_chk_update => '更新プログラムのチェック';

  @override
  String get icon_pause => '中断';

  @override
  String get icon_resume => '再開';

  @override
  String get icon_cert_method => '認証方法';

  @override
  String get icon_latestver => '最新バージョン';

  @override
  String get icon_latestveravail => '利用可能な最新バージョン';

  @override
  String get icon_device_ios => 'iOS 機器向け';

  @override
  String get icon_device_android => 'Android™ 機器向け';

  @override
  String get icon_f_ios => 'iOS向け';

  @override
  String get icon_f_android => 'Android™ 向け';

  @override
  String get icon_cws_myconnection => 'CanvasWorkspace\n (マイコネクション)';

  @override
  String get icon_step1 => 'ステップ１：';

  @override
  String get icon_step2 => 'ステップ２：';

  @override
  String get icon_step3 => 'ステップ３：';

  @override
  String get icon_step4 => 'ステップ４：';

  @override
  String get icon_step5 => 'ステップ５：';

  @override
  String get icon_register => '登録';

  @override
  String get icon_loginid => 'ログインID：';

  @override
  String get icon_id => 'ID：';

  @override
  String get icon_appq1 => 'アップリケパッチ\n(ワッペン)';

  @override
  String get icon_appq2 => 'アップリケパッチ\n(指定色)';

  @override
  String get icon_original_img => 'オリジナル画像';

  @override
  String get icon_appq_stitch_1 => 'ジグザグステッチ';

  @override
  String get icon_appq_stitch_2 => 'サテンステッチ';

  @override
  String get icon_appq_stitch_3 => '走り縫い';

  @override
  String get icon_stamp_web => 'カット用アウトライン';

  @override
  String get icon_cws_rgs_title => 'マシン登録をするためにPINコードを取得してください。';

  @override
  String get icon_cws_rgs_s1 => 'CanvasWorkspaceにログインしてください。\nhttp://CanvasWorkspace.Brother.com';

  @override
  String get icon_cws_rgs_s2 => '[アカウント設定]をタップしてください。';

  @override
  String get icon_pincode => 'PINコード';

  @override
  String get icon_kitsnc => 'ScanNCut (マイコネクション)';

  @override
  String get icon_snc1 => 'ScanNCut';

  @override
  String get icon_f_om_kitsnc => 'ScanNCut (マイコネクション)';

  @override
  String get icon_density_mm => '本/mm';

  @override
  String get icon_density_inch => '本/inch';

  @override
  String get icon_machineregist => 'マシン登録';

  @override
  String get icon_snj_myconnection => 'Artspira / マイコネクション';

  @override
  String get icon_snj_rgs_title => 'マシン登録をするためにPINコードを取得してください。';

  @override
  String get icon_snj_rgs_s1_iivo1 => 'Artspiraアプリをインストールして、ログインしてください。\nhttps://s.brother/snjumq4211';

  @override
  String get icon_snj_rgs_s2 => '[機器設定]をタップ、ミシンの[登録]をタップし、その後 [無線LANモデル]を選択してください。';

  @override
  String get icon_snj_rgs_s3 => 'Artspiraアプリで下記の番号を入力し、PINコードを取得してください。';

  @override
  String get icon_snj_rgs_pin => '次の画面でPINコードを入力してください。';

  @override
  String get icon_cws_rgs_s3 => '[マシン登録]をタップし、 [ミシンを追加する]を選択してください。';

  @override
  String get icon_cws_rgs_s4 => 'WEB画面で下記の番号を入力し、PINコードを取得してください。';

  @override
  String get icon_cws_rgs_pin => '次の画面でPINコードを入力してください。';

  @override
  String get icon_transfer => '転送';

  @override
  String get icon_app_selectcolor => 'アップリケ布にする色を選択してください。';

  @override
  String get icon_texture => 'テクスチャ';

  @override
  String get icon_userthread => 'ユーザー定義';

  @override
  String get icon_senju => 'Artspira';

  @override
  String get icon_notnow => '今はしない';

  @override
  String get icon_builtin => '内蔵';

  @override
  String get icon_user => 'カスタム';

  @override
  String get icon_clearall => '全て削除';

  @override
  String get icon_taperingtitle => 'テーパリング';

  @override
  String get icon_tapering01 => '開始';

  @override
  String get icon_tapering02 => '終了';

  @override
  String get icon_tapering03 => '終了スタイル';

  @override
  String get icon_tapering03_2 => '終了スタイル';

  @override
  String get icon_tapering04 => '開始角度';

  @override
  String get icon_tapering05 => '終了角度';

  @override
  String get icon_tapering06 => '模様繰り返し';

  @override
  String get icon_tapering06_s => '繰り返し';

  @override
  String get icon_times => '回';

  @override
  String get icon_approx_s => '約';

  @override
  String get icon_e2etitle => 'Edge-To-Edge Quilt';

  @override
  String get icon_e2e01 => '反転オプション';

  @override
  String get icon_e2e01_2 => '反転オプション';

  @override
  String get icon_e2e02 => '行';

  @override
  String get icon_e2e03 => '個';

  @override
  String get icon_sr_title => 'ステッチレギュレーター';

  @override
  String get icon_sr_mode_title => 'モード';

  @override
  String get icon_sr_mode_00exp => 'ステップ1 - モードを選択。\nステップ2 - 模様を選択。\n   *モード３ではしつけ縫い模様が自動で選択されます。\nステップ3 - 縫い始めてください。';

  @override
  String get icon_sr_mode01exp => '間欠モード\n\n布の動きが無い時は、針は上で停止し、布の動きを待ちます。手を針下に入れないように注意してください。';

  @override
  String get icon_sr_mode02exp => '連続モード\n\n布の動きが無い場合は、同じ位置でゆっくり縫製します。模様の角など指定ステッチ長以下で針を落としたいときや、止め縫いにも使えます。';

  @override
  String get icon_sr_mode03exp => 'しつけモード\n\n長めの間隔で針落ちします。手を針下に入れないように注意してください。';

  @override
  String get icon_sr_mode04exp => 'フリーモーションモード\n\n一定の速度で針落ちします。布を動かす速度によって、ステッチの長さが変わります。';

  @override
  String get icon_sr_mem_mode01 => '間欠';

  @override
  String get icon_sr_mem_mode02 => '連続';

  @override
  String get icon_sr_modemem_03 => 'しつけ';

  @override
  String get icon_sr_mem_mode04 => 'フリーモーション';

  @override
  String get icon_sr_sensingline => 'センシングライン';

  @override
  String get icon_sr_footheight => 'SR高さ';

  @override
  String get icon_unselect => '無選択';

  @override
  String get icon_filter => 'フィルター';

  @override
  String get icon_filterapplied => 'フィルター適用中';

  @override
  String get icon_apply => '適用';

  @override
  String get icon_upperlimit => '上限';

  @override
  String get icon_lowerlimit => '下限';

  @override
  String get icon_all => '全て';

  @override
  String get icon_bh_guide01 => 'ボタンホールガイド';

  @override
  String get icon_bh_guide02 => '配列';

  @override
  String get icon_bh_guide03 => '間隔';

  @override
  String get icon_bh_guide04 => '布端ガイド';

  @override
  String get icon_bh_guide05 => '端からの距離';

  @override
  String get icon_colorchanges => '色替え数';

  @override
  String get icon_voiceguidance_title => 'ボイスガイダンス';

  @override
  String get icon_voicevolume => 'ボイスボリューム';

  @override
  String get icon_voice_01eng_a => 'English-A';

  @override
  String get icon_voice_01eng_b => 'English-B';

  @override
  String get icon_voice_02deu_a => 'Deutsch-A';

  @override
  String get icon_voice_02deu_b => 'Deutsch-B';

  @override
  String get icon_voice_03fra_a => 'Français-A';

  @override
  String get icon_voice_03fra_b => 'Français-B';

  @override
  String get icon_voice_04ita_a => 'Italiano-A';

  @override
  String get icon_voice_04ita_b => 'Italiano-B';

  @override
  String get icon_voice_05nld_a => 'Nederlands-A';

  @override
  String get icon_voice_05nld_b => 'Nederlands-B';

  @override
  String get icon_voice_06esp_a => 'Español-A';

  @override
  String get icon_voice_06esp_b => 'Español-B';

  @override
  String get icon_voice_07jpn_a => '日本語-A';

  @override
  String get icon_voice_07jpn_b => '日本語-B';

  @override
  String get icon_embcate_photostitch => 'ピクチャープレイ刺しゅう';

  @override
  String get icon_photos_title => 'ピクチャープレイ刺しゅう機能';

  @override
  String get icon_photos_01 => '画像ファイルを選択してください (JPG, BMP, PNG)。';

  @override
  String get icon_photos_02 => 'サイズ調整';

  @override
  String get icon_photos_03 => '背景除去';

  @override
  String get icon_photos_04 => '範囲指定';

  @override
  String get icon_photos_05 => 'フレームにフィット';

  @override
  String get icon_photos_06 => '自動 (AI)';

  @override
  String get icon_photos_07 => '手動';

  @override
  String get icon_photos_08 => 'AI変換のスタイルを選択してください。';

  @override
  String get icon_photos_09 => '色調整';

  @override
  String get icon_photos_10 => '輪郭強調';

  @override
  String get icon_photos_11 => '明るさ';

  @override
  String get icon_photos_12 => 'コントラスト';

  @override
  String get icon_photos_13 => '彩度';

  @override
  String get icon_photos_14 => '画像ファイルのインポート (JPG, BMP, PNG)';

  @override
  String get icon_photos_15 => '刺しゅう設定';

  @override
  String get icon_style0 => 'オリジナル';

  @override
  String get icon_style1 => 'スタイル１';

  @override
  String get icon_style2 => 'スタイル２';

  @override
  String get icon_style3 => 'スタイル３';

  @override
  String get icon_style4 => 'スタイル４';

  @override
  String get icon_style5 => 'スタイル５';

  @override
  String get icon_style6 => 'スタイル６';

  @override
  String get icon_style7 => 'スタイル７';

  @override
  String get icon_style8 => 'スタイル８';

  @override
  String get icon_style9 => 'スタイル９';

  @override
  String get icon_style10 => 'スタイル１０';

  @override
  String get icon_style1_name => 'ポップアート';

  @override
  String get icon_style2_name => 'アールデコ';

  @override
  String get icon_style3_name => 'ペンシルスケッチ';

  @override
  String get icon_style4_name => 'オイルペイント';

  @override
  String get icon_style5_name => 'ネオンサイン';

  @override
  String get icon_style6_name => 'セピアアート';

  @override
  String get icon_style7_name => '浮世絵';

  @override
  String get icon_style8_name => 'ステンドグラス';

  @override
  String get icon_style9_name => 'モザイク';

  @override
  String get icon_style10_name => 'ファンシー';

  @override
  String get icon_stylusedit => 'スタイラスによるプロジェクター編集';

  @override
  String get icon_projectorsettings => 'プロジェクター設定';

  @override
  String get icon_setting_srvolume => 'SRブザーボリューム';

  @override
  String get icon_embcate_bt_01 => 'キルト';

  @override
  String get icon_embcate_bt_02 => 'アップリケ';

  @override
  String get icon_embcate_bt_03 => '植物';

  @override
  String get icon_embcate_bt_04 => '動物';

  @override
  String get icon_embcate_bt_05 => 'レタリング';

  @override
  String get icon_embcate_bt_06 => 'デコレーション';

  @override
  String get icon_embcate_bt_07 => '季節';

  @override
  String get icon_embcate_bt_08 => '3Dレース';

  @override
  String get icon_embcate_bt_09 => 'クロッシェレース';

  @override
  String get icon_embcate_bt_10 => 'インザフープ';

  @override
  String get icon_embcate_b_01 => 'キルト 2';

  @override
  String get icon_embcate_b_02 => 'ステンドグラスキルト';

  @override
  String get icon_embcate_b_03 => 'アップリケ 2';

  @override
  String get icon_embcate_b_04 => '植物 2';

  @override
  String get icon_embcate_b_05 => '薔薇(ピエール＝ジョゼフ・ルドゥーテ)';

  @override
  String get icon_embcate_b_06 => 'スイス刺しゅう(Zündt Designs)';

  @override
  String get icon_embcate_b_07 => 'ゼンタングル';

  @override
  String get icon_embcate_b_08 => '動物 2';

  @override
  String get icon_embcate_b_09 => 'レタリング 2';

  @override
  String get icon_embcate_b_10 => 'スポーツ';

  @override
  String get icon_embcate_b_11 => 'マリン';

  @override
  String get icon_embcate_b_12 => '食べもの';

  @override
  String get icon_embcate_b_13 => 'ベビー/キッズ';

  @override
  String get icon_embcate_b_14 => 'デコレーション 2';

  @override
  String get icon_embcate_b_15 => '3Dレース 2';

  @override
  String get icon_legalinfo => '法律に基づく情報';

  @override
  String get icon_legal_opensource => 'Open Source Licensing Remarks\n(オープンソースライセンス公開)';

  @override
  String get icon_legal_thirdpartysoft => 'Third-Party Software\n(サードパーティソフトウェア)';

  @override
  String get icon_nousb => '－－－－－－';

  @override
  String get icon_randomfill => 'ランダムフィル';

  @override
  String get icon_selarea => 'エリア選択';

  @override
  String get icon_maxnumber_patt => '最小距離';

  @override
  String get t_err01 => '安全装置が働きました。\n糸がからんでいませんか？\n針が曲がっていませんか？';

  @override
  String get t_err02 => '上糸を確かめ、もう一度かけ直してください。';

  @override
  String get t_err02_emb => '上糸を確かめ、もう一度かけ直してください。\n\n* 刺しゅう枠を中央に移動するには縫製画面の枠移動キーを押してください。';

  @override
  String get t_err03 => '押えレバーを上げてください。';

  @override
  String get t_err04 => '押えレバーを下げてください。';

  @override
  String get t_err05 => '刺しゅうカードが入っていません。\nカードを入れてください。';

  @override
  String get t_err06 => 'この刺しゅうカードは使用できません。';

  @override
  String get t_err07 => 'これ以上模様の組み合わせができません。';

  @override
  String get t_err07_u => 'これ以上ステッチの組み合わせができません。';

  @override
  String get t_err08 => '刺しゅう機が付いていないときに、このキーは使用できません。\n電源スイッチを切ってから、刺しゅう機を取り付けてください。';

  @override
  String get t_err09 => '刺しゅう機が付いているときに、このキーは使用できません。';

  @override
  String get t_err10 => '刺しゅう機が付いているときに、このキーは使用できません。\n電源スイッチを切ってから、刺しゅう機をはずしてください。';

  @override
  String get t_err11 => '刺しゅう機が付いているときに、フットコントローラーは使用できません。\nフットコントローラーをはずしてください。';

  @override
  String get t_err_corrupteddataturnoff => 'データが認識できません。データが壊れている可能性があります。\n\n電源を入れ直して下さい。';

  @override
  String get t_err12 => 'この模様はデータ容量の制限を超えています。';

  @override
  String get t_err13 => '針が下がっているときに、このキーは使用できません。\n針を上げてから再度キーを押してください。';

  @override
  String get t_err15 => 'フットコントローラーが付いているときは、スタート／ストップスイッチは使用できません。';

  @override
  String get t_err16 => '模様の編集を完了してください。';

  @override
  String get t_err16_e => '刺繍模様の編集を完了してください。';

  @override
  String get t_err16_u => 'ステッチ模様の編集を完了してください。';

  @override
  String get t_err17 => 'ボタン穴かがりレバーを上げてください。';

  @override
  String get t_err18 => 'ボタン穴かがりレバーを下げてください。';

  @override
  String get t_err19 => '文字の配列ができません。';

  @override
  String get t_err22 => '模様を選んでください。';

  @override
  String get t_err22_u => 'ステッチを選んでください。';

  @override
  String get t_err23 => '記憶中…';

  @override
  String get t_err24 => '下糸が少なくなりました。';

  @override
  String get t_err25 => '刺しゅうキャリッジが動きます。\n刺しゅうキャリッジの近くから物を離し、手を近づけないようにしてください。';

  @override
  String get t_err26 => '消去中…';

  @override
  String get t_err27 => '容量が不足しています。\n模様を消去してください。';

  @override
  String get t_err27_d => '容量が不足しているため保存できません。\n他のデータを消去してください。';

  @override
  String get t_err61 => '容量が不足しています。';

  @override
  String get t_err61_d => '容量が不足しているためデータを保存できません。\n他の模様を消去するか、メディアを交換してください。';

  @override
  String get t_err61_dd => '容量が不足しているためデータを保存できません。\n他のデータを消去するか、他のメディアをお使いください。';

  @override
  String get t_err28 => '模様の呼び出し中です。\nしばらくお待ち下さい。';

  @override
  String get t_err28_d => 'データの呼び出し中です。\nしばらくお待ち下さい。';

  @override
  String get t_err29 => '選んだ模様を消去します。\nよろしいですか？';

  @override
  String get t_err65 => '選んだ模様を消去します。\nよろしいですか？';

  @override
  String get t_err29_d => '選択したデータファイルを削除します。\nよろしいですか？';

  @override
  String get t_err30 => '設定条件を記憶します。\nよろしいですか？';

  @override
  String get t_err33 => '刺しゅう枠をはずしてください。';

  @override
  String get t_err34 => '刺しゅう枠をセットしてください。';

  @override
  String get t_err36 => 'スピードコントロールレバーで振り幅を調節するときは、スタート／ストップスイッチは使用できません。フットコントローラーを使用してください。';

  @override
  String get t_err37 => '下糸巻きの安全装置が働きました。\n糸がからんでいませんか？';

  @override
  String get t_err38 => '２本針の設定のときには、この機能を選ぶことはできません。\n１本針の設定にしてから、選んでください。';

  @override
  String get t_err_twinn_10 => '2本針モードでは、直線針板を使用できません。\n２本針を取り外し、2本針モードを解除してください。';

  @override
  String get t_err_twinn_11 => '2本針モードがキャンセルされました。\n2本針を取り外してください。';

  @override
  String get t_err_twinn_12 => '2本針が取り外されていることを確認してください。';

  @override
  String get t_err42 => 'よろしければOKボタンを押してください。';

  @override
  String get t_err48 => '模様のデータが読めません。\nデータが壊れている可能性があります。';

  @override
  String get t_err50 => 'ボビン押えを左に押してください。';

  @override
  String get t_err53 => '針上下スイッチを押して針を上げてください。';

  @override
  String get t_err55 => 'ボタンホール押え”A＋”を取り付けてください。\n内蔵のカメラで、「A＋」マークと３つのドットを認識することで、「A＋」押えと判断しています。';

  @override
  String get t_err56 => 'ボタンの長さが認識できませんでした。\n３つのドットがはっきり見えるように確認してください。または、ボタン穴長さの値を入力して、やり直してください。';

  @override
  String get t_err63 => '赤い枠がはみ出しているときは操作できません。位置を動かしてから操作してください。';

  @override
  String get t_err64 => '特別な模様が含まれているため、外部メモリーには保存できません。\n本体メモリーに保存してください。';

  @override
  String get t_err69 => '記憶できない模様が含まれています。';

  @override
  String get t_err76 => 'この刺しゅう枠でぬうことはできません。\n大きい枠に取り替えて下さい。';

  @override
  String get t_err77 => 'この刺しゅう枠でぬうことはできません。\n専用枠に取り替えてください。';

  @override
  String get t_err82 => '色の変更が元に戻ります。\nよろしいですか？';

  @override
  String get t_err83 => 'データを書き換えます。\nよろしいですか？';

  @override
  String get t_err84 => 'レジューム記憶を呼び出しますか？';

  @override
  String get t_err88 => '刺しゅう機がついていないのでぬえません。\n電源スイッチを切ってから刺しゅう機を取り付けてください。';

  @override
  String get t_err89 => 'ＵＳＢメディアが入っていません。\nＵＳＢメディアを入れてください。';

  @override
  String get t_err90 => 'このＵＳＢメディアは使用できません。';

  @override
  String get t_err_usb_notcompati => '接続されたUSBメディアは本機と互換性がありません。\n別のUSBメディアを使用してください。';

  @override
  String get t_err93 => 'ＵＳＢメディアが壊れている可能性があります。\nＵＳＢメディアを交換して、\nもう一度記憶してください。';

  @override
  String get t_err94 => '容量が不足しています。\n模様を消去するか、ＵＳＢメディアを交換してください。';

  @override
  String get t_err94_cmn => '容量が不足しています。\n模様を消去するか、メディアを交換してください。';

  @override
  String get t_err95 => 'ＵＳＢメディアが交換されました。\n読み込んでいる最中に交換しないでください。';

  @override
  String get t_err95_cmn => 'メディアが交換されました。\n読み込んでいる最中に交換しないでください。';

  @override
  String get t_err96 => 'ＵＳＢメディアがライトプロテクトされていて記憶できません。\nライトプロテクトを解除して記憶してください。';

  @override
  String get t_err96_cmn => 'メディアがライトプロテクトされていて記憶できません。\nライトプロテクトを解除して記憶してください。';

  @override
  String get t_err97 => 'ＵＳＢメディアがライトプロテクトされていて消去できません。\nライトプロテクトを解除して消去してください。';

  @override
  String get t_err97_cmn => 'メディアがライトプロテクトされていて消去できません。\nライトプロテクトを解除して消去してください。';

  @override
  String get t_err98 => 'ＵＳＢメディアエラー';

  @override
  String get t_err98_cmn => 'メディアエラー';

  @override
  String get t_err99 => 'ＵＳＢメディアが読めません。\nＵＳＢメディアが壊れている可能性があります。';

  @override
  String get t_err100 => 'ＵＳＢメディアのフォーマット中です。';

  @override
  String get t_err101 => 'ＵＳＢ通信中';

  @override
  String get t_err101_cmn => 'メディア通信中';

  @override
  String get t_err103 => 'しばらくお待ち下さい。';

  @override
  String get t_err104 => '模様が青い枠からはみ出しているときは操作できません。';

  @override
  String get t_err106 => '次のパーツを縫製しますか？';

  @override
  String get t_err107 => 'ぬい終わりました。';

  @override
  String get t_err108 => 'ポケットがいっぱいです。';

  @override
  String get t_err109 => '押え上下スイッチで押えを上げてください。';

  @override
  String get t_err110 => '押え上下スイッチで押えを下げてください。';

  @override
  String get t_err111 => '糸かけがうまくできません。\nもう一度糸かけスイッチを押してください。';

  @override
  String get t_err113 => 'プログラムのアップグレードを行います。\nＵＳＢでプログラムをミシンに入れてください。';

  @override
  String get t_err116 => 'ＤＡＴＡエラー';

  @override
  String get t_err117 => 'ＦＬＡＳＨ ＲＯＭエラー';

  @override
  String get t_err118 => '不具合が生じました。\n電源をいったんＯＦＦしてから再度やり直してください。';

  @override
  String get t_err119 => '針板を着脱するときは、電源を切ってください。';

  @override
  String get t_err120 => '刺しゅう枠を前の位置に戻します。\nよろしいですか？';

  @override
  String get t_err120_2 => '刺しゅう枠を外して、下糸を交換してください。\n枠をつけたあとOKキーを押すと、前の位置に戻ります。';

  @override
  String get t_err121 => 'ボーダー模様の組み合わせが解除されます。\nよろしいですか？';

  @override
  String get t_err122 => 'このＵＳＢメディアには対応していません。';

  @override
  String get t_err122_cmn => 'このメディアには対応していません。';

  @override
  String get t_err123 => 'ＵＳＢメディア／コネクタが\n引き抜かれました。';

  @override
  String get t_err124 => '不具合が生じました。\n電源をいったんＯＦＦしてから再度やり直して下さい。';

  @override
  String get t_err125 => '糸がうまくかかっていない可能性があります。\n始めから糸をかけ直して下さい。';

  @override
  String get t_err126 => '押えが自動で下がります。\nよろしいですか？';

  @override
  String get t_err127 => '２本針の設定のときには、上糸通しスイッチは使えません。';

  @override
  String get t_err128 => '刺しゅう枠を奥までスライドさせてください。\n枠固定レバーを倒してください。';

  @override
  String get t_err129 => 'ボビン押えを右に押してください。';

  @override
  String get t_err130 => '押えが上下します。\n手を近づけないようにしてください。';

  @override
  String get t_err131 => 'この模様は使えません。';

  @override
  String get t_err132 => '刺しゅうをするときは、枠固定レバーが倒れているのを確認してからスタート／ストップスイッチを押してください。';

  @override
  String get t_err133 => 'この刺しゅう枠は使えません。';

  @override
  String get t_err134 => 'この模様はデータ容量の制限を\n超えているため使えません。';

  @override
  String get t_err136 => '定期点検の時期になりました。';

  @override
  String get t_err137 => '定期点検を行ってください。1000時間を超えています。';

  @override
  String get t_err208 => '計算中…';

  @override
  String get t_err209 => '刺しゅう機が移動します。';

  @override
  String get t_err210 => '認識中…';

  @override
  String get t_err213 => '位置合わせマークを認識する事ができませんでした。';

  @override
  String get t_err215 => '位置合わせマークをはがして下さい。';

  @override
  String get t_err224 => '白い布または紙にゴミ、汚れはありませんか。\nもしある場合は、取り替えて下さい。';

  @override
  String get t_err227 => '正しく設定または認識する事ができませんでした。';

  @override
  String get t_err228 => 'ファイルサイズが大きすぎます。\n規定サイズ以内のファイルを使用して下さい。';

  @override
  String get t_err229 => 'このファイルは使用できません。';

  @override
  String get t_err229_b => 'このファイルバージョンのデータは読み込めません。';

  @override
  String get t_err239 => 'ＰＣと通信中です。\nＵＳＢケーブルを抜かないで下さい。';

  @override
  String get t_err241 => 'ファイルが読み込めません。';

  @override
  String get t_err242 => '書き込みに失敗しました。';

  @override
  String get t_err244 => '選択した画像を削除しますが、よろしいですか？';

  @override
  String get t_err245 => 'このキーは使えません。';

  @override
  String get t_err246 => '縫製エリアから模様がはみ出します。\n位置を変更してスキャンし直して下さい。';

  @override
  String get t_err247 => '容量が不足しているので、保存できません。';

  @override
  String get t_err248 => '設定が消えます。\nよろしいですか？';

  @override
  String get t_err249 => '布端を検知する事ができませんでした。';

  @override
  String get t_err250 => 'この模様は変換できません。';

  @override
  String get t_err251 => '模様の大きさと位置がリセットされます。\nよろしいですか？';

  @override
  String get t_err252 => '模様の大きさがリセットされますが、よろしいですか？';

  @override
  String get t_err253 => '布厚を検出します。\n赤い線の中に位置合わせマークを貼って下さい。';

  @override
  String get t_err254 => '布厚検出に成功しました。\n位置合わせマークをはがして下さい。\nＯＫキーを押すとスキャン動作を開始します。';

  @override
  String get t_err255 => 'ＯＫキーを押すと枠が動きスキャン動作を開始します。';

  @override
  String get t_err256 => '布厚検出に失敗しました。\nもう一度やり直しますか？';

  @override
  String get t_err257 => '認証に成功しました。\nミシンを再起動させて下さい。';

  @override
  String get t_err257_1 => '認証に成功しました。\nミシンを再起動させて下さい。';

  @override
  String get t_err257_2 => '認証に成功しました。\nマシンを再起動させて下さい。';

  @override
  String get t_err259 => 'アップグレードキットを認証します。\n\n認証キーを入力して下さい。';

  @override
  String get t_err260 => '認証キーを入力したら、[セット]キーを入力して下さい。';

  @override
  String get t_err261 => '認証中…';

  @override
  String get t_err262 => '認証キーが間違っています。\n確認して、もう一度入力して下さい。';

  @override
  String get t_err263 => 'KIT';

  @override
  String get t_err264 => '背景画像を消去しますか？';

  @override
  String get t_err265 => '模様の大きさと位置、角度がリセットされます。\nよろしいですか？';

  @override
  String get t_err343 => '移動や回転が元に戻りますがよろしいですか？';

  @override
  String get t_err266 => 'アップグレードキットを認証しました。';

  @override
  String get t_err270 => '１個目の位置合わせマークを赤い枠線の中に収まるようにしっかりと貼って下さい。\nスキャンキーを押すと刺しゅう機が動きます。';

  @override
  String get t_err271 => '２個目の位置合わせマークを赤い枠線の中に収まるようにしっかりと貼って下さい。\nスキャンキーを押すと刺しゅう機が動きます。';

  @override
  String get t_err273 => '位置合わせマークが指示通りに貼られていませんでした。\nマークを貼り直して下さい。';

  @override
  String get t_err274 => '位置合わせマークを認識しました。\nマークをはがさず、２つのマークの中心が縫製範囲内に\n収まるように布を張り直して次の模様を選択して下さい。';

  @override
  String get t_err276 => '位置合わせマークを認識しました。\nマークをはがして下さい。';

  @override
  String get t_err277 => '模様つなぎ機能を解除しますが、よろしいですか？';

  @override
  String get t_err278 => '終了すると、次のパーツの縫製ができなくなります。模様つなぎ縫製を本当に終了していいですか？';

  @override
  String get t_err279 => '縫製が終了しました。\n次の模様をつなげますか？\n\n* 布を枠から外さないでください。\n* 続きを縫製する予定があれば、次の模様を呼び出してください。その後電源を切っても再開できます。';

  @override
  String get t_err282 => 'これ以上入力できません。';

  @override
  String get t_err283 => 'アプリケーションを終了します。';

  @override
  String get t_err284 => 'データが複雑なため、変換できませんでした。';

  @override
  String get t_err286 => '線の編集を終了しますか？';

  @override
  String get t_err288 => '位置合わせマークを認識しました。\nマークを２つとも新しい位置に貼り直して下さい。';

  @override
  String get t_err290 => '位置合わせマークを認識できません。\n２つのマークの中心が縫製範囲内に収まるように\nマークを貼り直して下さい。';

  @override
  String get t_err291 => '空き容量が不足しています。\n違うＵＳＢメディアを使用してください。';

  @override
  String get t_err291_cmn => '空き容量が不足しています。\n違うメディアを使用してください。';

  @override
  String get t_err292 => '選択されたモードを利用するための十分な色数が標準パレットにありません。';

  @override
  String get t_err292_s => '選択されたモードを利用するための十分な色数が指定されたパレットにありません。';

  @override
  String get t_err297 => '手順３から４を繰り返してください。';

  @override
  String get t_err298 => 'シールが汚れています。';

  @override
  String get t_err_cameracalib_ng_msg => '正しく認識する事ができませんでした。\n新しい白いシールを使用してください。';

  @override
  String get t_err_cameracalib_ok_msg => 'OKキーを押すと、針落ち点を記憶します。';

  @override
  String get t_err299 => '数回試しても成功しない場合は、お近くの販売店にご相談ください。';

  @override
  String get t_err300 => '必要に応じて、取扱説明書を参照し、推奨の針の種類をご確認ください。';

  @override
  String get t_err_cameracalib_title => 'カメラ/プロジェクターの針位置調整';

  @override
  String get t_err_cameracalib_1_4 => '１．針上下キーを押して、針上にして下さい。\n\n２、針と押えをはずした後、針落ち点に白いシールを貼ってください。\n\n３．使用する針(75/11または90/14の標準サイズ)を装着してください。\n\n４．スタートキーを押してください。キャリブレーションを開始します。\n安全のため、スタートキーを押す前に針の周りを片づけてください。\n\n＊事故を防ぐために、針の付近に手や物を近づけないように注意してください。';

  @override
  String get t_err303 => '縫製が終了しました。\n次の模様をつなげますか？';

  @override
  String get t_err304 => '布を枠から外さないでください。\n次に縫製する模様を選択して下さい。';

  @override
  String get t_err307 => '位置合わせマークをはがさないで下さい。\nマークの中心と縫製する模様が縫製範囲内に収まるように布を張りなおして下さい。';

  @override
  String get t_err308 => '模様が縫製範囲からはみ出しました。\n位置合わせマークの中心と縫製する模様が縫製範囲内に収まるように布を張りなおして下さい。';

  @override
  String get t_err309 => '位置合わせマークが見つかりませんでした。\nマークの中心と縫製する模様が縫製範囲内に収まるように布を張りなおして下さい。';

  @override
  String get t_err310 => '位置合わせマークの位置が変更されました。\nマークの中心と縫製する模様が縫製範囲内に収まるように\n布を張りなおして下さい。';

  @override
  String get t_err311 => '位置合わせマークを認識しました。\nマークをはがして、縫製して下さい。';

  @override
  String get t_err311_size => '位置合わせマークを認識しました。\nマークをはがして、縫製して下さい。\n\n* マーク間の距離が布張替えにより変化したため、模様のサイズを自動調整しました。';

  @override
  String get t_err311_rehoop => 'マーク間の距離が布張替えにより大きく変化しています。\nマーク間距離が下記の長さになるように布を張り直してください。';

  @override
  String get t_err312 => '位置合わせマークをはがさないでください。\nマークの貼り直し処理に進みます。\n布を張りなおして下さい。';

  @override
  String get t_err313 => '位置合わせマークが見つかりませんでした。\n布を張りなおしてください。';

  @override
  String get t_err314 => '位置合わせマークを認識しました。\nマークをはがして下さい。';

  @override
  String get t_err354 => 'シャットオフサポートモードが働きました。\nミシンの電源を切ってください。';

  @override
  String get t_err356 => 'この模様はデュアルフィードモードでぬうことが出来ません。';

  @override
  String get t_err359 => 'デュアルフィードモジュールを外してください。';

  @override
  String get t_err360 => '時刻を設定してください。';

  @override
  String get t_err361 => 'ご利用の言語を選択してください。';

  @override
  String get t_err362 => 'ＬＥＤポインター付き刺しゅう押えを外してください。';

  @override
  String get t_err364 => 'モジュールエラー';

  @override
  String get t_err368 => '移動や回転、ボーダー設定が元に戻りますがよろしいですか？';

  @override
  String get t_err373 => '刺繍枠が変更されました。\n元の刺繍枠に戻してください。';

  @override
  String get t_err380 => '布を取り除いてから、糸通ししてください。';

  @override
  String get t_err381 => 'ぬい終わり位置設定を解除しますが、よろしいですか？';

  @override
  String get t_err382 => '選択している模様では、ぬい終わり位置設定の長さ調整モードをご利用になれません。\n他の模様を選択するか、模様の長さを変更してください。';

  @override
  String get t_err383 => 'ぬい終わり設定シールをはがして、縫製を続けてください。';

  @override
  String get t_err384 => 'この状態ではぬい終わり位置設定の長さ調整モードをご使用になれません。\nぬい終わり位置設定を解除します。';

  @override
  String get t_err385 => '距離が短いため、ぬい終わり位置設定を使用できません。\n距離を長くするか、「一時停止」設定をOFFにすることでご使用が可能となります。';

  @override
  String get t_err386 => 'ぬい終わり位置設定がONのときは、この機能は使用できません。';

  @override
  String get t_err390 => '編集中のすべてのデータを消去し、ホーム画面に移りますが、よろしいですか？';

  @override
  String get t_err390_old => 'すべての模様を削除し、ホーム画面に移りますが、よろしいですか？';

  @override
  String get t_err391 => '模様の選択をキャンセルします。\nよろしいですか？';

  @override
  String get t_err391_u => '模様の選択をキャンセルします。\nよろしいですか？';

  @override
  String get t_err392 => '刺しゅうカードの情報を読み込んでいます。';

  @override
  String get t_err393 => 'この模様は組み合わせることができません。';

  @override
  String get t_err394 => '容量が不足しているため、保存できません。他の模様を削除しますか？';

  @override
  String get t_err395 => 'エリアをはみ出すのでその模様は読み込めません。';

  @override
  String get t_err396 => 'エリアからはみ出さないように、カーソルを使って移動してください。';

  @override
  String get t_err397 => '編集模様を削除します。よろしいですか？';

  @override
  String get t_err398 => '別ファイルで保存中…';

  @override
  String get t_err400 => '枠をはみ出します。\nこれ以上組み合わせるときは、\n模様を回転して、追加入力してください。';

  @override
  String get t_err401 => '枠をはみ出します。';

  @override
  String get t_err402 => '枠をはみ出します。\nこれ以上は入力できません。';

  @override
  String get t_err403 => '枠をはみ出します。\nこの機能は使用できません。';

  @override
  String get t_err404 => '選択した書体にない文字があるため変更できません。';

  @override
  String get t_err405 => '枠をはみ出します。';

  @override
  String get t_err406 => 'この枠では模様がはみ出します。 \n模様の選択を解除してもよろしいですか？';

  @override
  String get t_err408 => '模様が重なっています。\nこの機能は使用できません。';

  @override
  String get t_err410 => '位置合わせマークの中心や角に合わせて刺しゅうすることができます。刺しゅうする位置を決め、位置合わせマークを貼ってください。';

  @override
  String get t_err411 => '準備ができたら[Scan]キーを押してください。';

  @override
  String get t_err412 => '位置合わせマークが、配置可能な範囲に見つかりませんでした。';

  @override
  String get t_err413 => '位置合わせマークを使って、模様をきれいにつなげていきます。';

  @override
  String get t_err414 => '次の模様をつなぐ位置を指定してください。';

  @override
  String get t_err415 => 'データが読み込めません。';

  @override
  String get t_err416 => 'データを保存しました。\nファイル名：';

  @override
  String get t_err417 => 'データの読み込み中です。\nしばらくお待ちください。';

  @override
  String get t_err418 => 'このファイル形式は使えません。';

  @override
  String get t_err419 => 'ファイルサイズが大きすぎるため、このファイルは使用できません。';

  @override
  String get t_err420 => '画像のトレースに失敗しました。';

  @override
  String get t_err421 => 'ここで指定した色以下に減色した画像にしてから輪郭を抽出します。';

  @override
  String get t_err422 => 'イラストまたは線が描かれた紙をスキャン専用枠にセットし、マグネットで固定してください。';

  @override
  String get t_err423 => '刺しゅう枠は使用できません。スキャン専用枠をご使用ください。';

  @override
  String get t_err424 => '認識処理に数分かかることがあります。';

  @override
  String get t_err425 => '\"My Design Center\" の画面に移りますが、よろしいですか？';

  @override
  String get t_err426 => '\"IQ Designer\" の画面に移りますが、よろしいですか？';

  @override
  String get t_err428 => '\"My Design Center\" で作成した画像データを記憶していませんが、よろしいですか?';

  @override
  String get t_err429 => '\"IQ Designer\" で作成した画像データを記憶していませんが、よろしいですか?';

  @override
  String get t_err430 => 'スキャン枠では、ぬうことができません。\n刺しゅう枠に取り換えてください。';

  @override
  String get t_err432 => '読み込む画像をセットした枠をミシンに取り付けてください。';

  @override
  String get t_err433 => 'ラインスキャンまたはイラストスキャンする場合は、適切な色情報を取得するために、[スキャン枠]を使用してください。';

  @override
  String get t_err440 => '画像ファイルを選択してください。';

  @override
  String get t_err445 => 'この画像ファイルは使用できません。';

  @override
  String get t_err446 => 'スキャン中…';

  @override
  String get t_err447 => '認識中…';

  @override
  String get t_err448 => '処理中…';

  @override
  String get t_err451 => '編集中の画像データがすべて削除されますが、よろしいですか？';

  @override
  String get t_err452 => '使用できるファイルは、5MBまたは120万画素以内のJPG, PNG, BMP形式の画像ファイルです。';

  @override
  String get t_err453 => 'このページの設定を元に戻しますが、よろしいですか？';

  @override
  String get t_err454 => '模様が指定枠からはみ出します。\nこれ以上組み合わせるときは、模様を回転して、追加入力してください。';

  @override
  String get t_err455 => '模様が指定枠からはみ出します。';

  @override
  String get t_err457 => '枠指定表示をOFFしてください。';

  @override
  String get t_err458 => '画像ファイルのインポート。';

  @override
  String get t_err459 => 'この刺しゅう機は使用できません。';

  @override
  String get t_err460 => '表示されているエリアが変換できます。';

  @override
  String get t_err461 => '枠が動いて、スキャンを開始します。';

  @override
  String get t_err462_pp => 'このファイルは使用できません。\n6MB未満かつ1600万画素以内の画像ファイル(JPG, PNG, BMP) をお使いください。';

  @override
  String get t_err463 => '刺しゅう枠またはスキャン枠をはずしてください。';

  @override
  String get t_err464 => '最大縫製エリアからはみ出るため、変換できませんでした。';

  @override
  String get t_err465 => '刺しゅうデータを確定し、\"My Design Center\"を終了します。\n刺しゅう編集画面に移行してもよろしいですか？';

  @override
  String get t_err466 => '\"My Design Center\"を終了しますが、よろしいですか？';

  @override
  String get t_err467 => 'つなぎモード中は、この機能は使用できません。';

  @override
  String get t_err468 => 'メイン基板電源ＯＦＦ';

  @override
  String get t_err469 => '刺しゅうデータを確定し、\"IQ Designer\"を終了します。\n刺しゅう編集画面に移行してもよろしいですか？';

  @override
  String get t_err470 => '\"IQ Designer\"を終了しますが、よろしいですか？';

  @override
  String get t_err471 => '選択したデータを削除しますが、よろしいですか？';

  @override
  String get t_err472 => '複数の模様を選択してください。';

  @override
  String get t_err473 => '配置した模様を画像として保存しました。';

  @override
  String get t_err474 => '配置した模様のアウトラインを保存しました。';

  @override
  String get t_err475 => '模様の周りのオフセット距離を指定してください。';

  @override
  String get t_err478 => '\"My Design Center\"のスタンプ模様リストから呼び出せます。';

  @override
  String get t_err478_tc => '\"IQ Designer\"のスタンプ模様リストから呼び出せます。';

  @override
  String get t_err479 => 'ボビンワーク模様は、他のカテゴリーの模様を組み合わせることができません。';

  @override
  String get t_err480 => '１針目の位置に移動します。';

  @override
  String get t_err481 => 'ボビンワーク模様１つの縫製が終了しました。';

  @override
  String get t_err482 => 'すべてのボビンワーク模様の縫製が終了しました。';

  @override
  String get t_err483 => '糸を切ってください。';

  @override
  String get t_err484_old => '次の模様を縫製するために、下糸の量と種類を確認してください。';

  @override
  String get t_err484 => '下糸を交換して、刺しゅう枠をセットしてください。\nＯＫキーを押すと刺しゅうキャリッジが動きます。';

  @override
  String get t_err485 => '次のボビンワーク模様を縫製します。';

  @override
  String get t_err486 => 'プーリーを回して、布に１針落とし、下糸を引き抜いてください。';

  @override
  String get t_err489 => 'ステッチに変換するデータがありません。';

  @override
  String get t_err496 => 'すべてのエリアに対して、この設定値を適用しますが、よろしいですか？';

  @override
  String get t_err497 => 'アウトライン';

  @override
  String get t_err501 => '設定が変更できませんでした。縫い方設定の記憶領域が不足しています。';

  @override
  String get t_err502 => '模様同士が離れているため１つに生成することができません。';

  @override
  String get t_err503 => '模様の形状が複雑なためアップリケラインを作成できません。';

  @override
  String get t_err503_new => '複雑な形状または適さない形状のため、アップリケラインを生成できませんでした。\nアップリケ設定を変更するか、別の模様を選択してください。\n* 位置や角度によって結果が異なる場合があります。';

  @override
  String get t_err504 => '枠をはみ出すためアップリケラインを作成できません。';

  @override
  String get t_err509 => 'データ構造上、テクスチャを表示できない部分があります。';

  @override
  String get t_err505 => 'カラーソーティングがONの場合は無効です。';

  @override
  String get t_err506 => '糸印が消えます。よろしいですか？';

  @override
  String get t_err508 => '縫わない設定により糸印の設定が無効になります。よろしいですか？';

  @override
  String get t_err507 => '自動';

  @override
  String get t_err510 => '現在の画像を使いますか？';

  @override
  String get t_err511 => '本体メモリーにデータを保存しました。続けて縫製しますか？';

  @override
  String get t_err515 => '温度が上昇したため、プロジェクター機能はしばらく使用できません。';

  @override
  String get t_err516 => 'この模様は、この針板では縫えません。';

  @override
  String get t_err517 => 'この模様を縫うには、針板を変更してください。';

  @override
  String get t_err518 => '刺しゅう枠が変更されました。\n刺しゅうキャリッジが動きます。\n';

  @override
  String get t_err519 => 'プロジェクター投影を終了します。';

  @override
  String get t_err520 => '刺しゅうキャリッジの近くから物を離し、手を近づけないようにしてください。';

  @override
  String get t_err_521 => '使い方動画をダウンロードできます。';

  @override
  String get t_err574 => '不具合が生じました。\nEEPROMにアクセスできません。';

  @override
  String get t_err575 => '下糸が少なくなりました。\n\n* “止めぬいスイッチ”を押すと、止め縫いできます。\n* 枠移動キーを押すと、糸切りして、枠を脱着用の位置に移動します。のちほど、前の位置に自動で戻ります。';

  @override
  String get t_err577 => 'カウチング模様は、他のカテゴリーの模様を組み合わせることができません。';

  @override
  String get t_err578 => 'これ以上の色の組み合わせは、お気に入り選択できません。';

  @override
  String get t_err581 => '布の右上の角から縫い始めます。\n刺繍枠を縫い始め位置にセットしてください。';

  @override
  String get t_err581_b => '布の左上の角から縫い始めます。\n刺繍枠を縫い始め位置にセットしてください。';

  @override
  String get t_err582 => '1辺を縫い終わりました。布を反時計回りに90度回転させて、刺繍枠を張り直してください。';

  @override
  String get t_err582_n => '1辺を縫い終わりました。布を反時計回りに回転させて、次の角に刺繍枠を張り直してください。';

  @override
  String get t_err582_e => '1段を縫い終わりました。次の段の左端に刺繍枠を張り直してください。その際、上側の模様の糸印を含むように配置してください。';

  @override
  String get t_err583 => '移動キーを使って、模様の内側の角の位置を合わせてください。';

  @override
  String get t_err583_e => '移動キーを使って、模様エリアの左上角を布の縫製エリアの左上角(しるし)に合わせてください。';

  @override
  String get t_err584 => '移動キーを使って、始点を前の模様の終点に合わせてください。';

  @override
  String get t_err584_e => '移動キーを使って、模様の縫製エリアの左上角を上側の模様の左下糸印に合わせてください。';

  @override
  String get t_err585 => '模様周囲のポイントを見ながら、回転キーを使って、角度を調節してください。';

  @override
  String get t_err586 => '左下のポイントが、次の模様の内側角になるようにサイズを調節してください。';

  @override
  String get t_err586_b => '右下のポイントが、次の模様の内側角になるようにサイズを調節してください。';

  @override
  String get t_err586_e => '模様の端を見ながら、回転キーとサイズ変更キーを使って、角度とサイズを調整してください。';

  @override
  String get t_err587 => '回転キー、サイズ変更キーを使って、終点を最初の模様の始点に合わせてください。';

  @override
  String get t_err588 => '布を張りなおして下さい。';

  @override
  String get t_err588_e => '右側に布を張りなおして下さい。その際、枠の左側に、前の模様の右端を含むように配置してください。';

  @override
  String get t_err588_e_2 => '右側に布を張りなおして下さい。その際、前の模様の右端と、上側の模様の糸印を含むように配置してください。';

  @override
  String get t_err590 => '「取り込み」キーを押して、アップデートファイルをインストールしてください。';

  @override
  String get t_err591 => '新しいアップデート用のプログラムがあります。アップデートする場合は、電源を切って、糸通しキーを押しながら電源を入れなおしてください。';

  @override
  String get t_err_dl_updateprogram2 => '新しいアップデート用のプログラムが準備できました。\nアップデートする場合は、電源を切って、糸通しキーを押しながら電源を入れなおしてください。';

  @override
  String get t_err592 => 'LOADキーを押して、アップデートファイルを保存してください。';

  @override
  String get t_err593 => 'アップデートファイルを保存するデバイスを選択してください。';

  @override
  String get t_err594 => 'アップデートファイルが保存されているデバイスを選択してください。';

  @override
  String get t_err_dl_updateprogram => 'スタートキーを押して、アップデートファイルをダウンロードしてください。';

  @override
  String get t_err_dl_fail => 'ダウンロードに失敗しました: 内部ストレージがいっぱいです。';

  @override
  String get t_err_networkconnectionerr => '通信が切断しました。無線接続を確認してください。';

  @override
  String get t_err_not_turnoff => '電源を切らないでください。';

  @override
  String get t_err_pressresume_continuedl => 'ダウンロードを続行する場合は、再開ボタンを押してください。';

  @override
  String get t_err_updateformovie => '動画をインストールするために再度アップデートをしてください。';

  @override
  String get t_err595 => '16ケタのアクティベーションコードを入力してください。';

  @override
  String get t_err596 => '16ケタのアクティベーションコードを入力して、Setキーを押してください。';

  @override
  String get t_err597 => 'ミシン番号とアクティベーションコードをサーバーに送ります。';

  @override
  String get t_err598 => 'ミシンが無線LANにつながっているかたは、「オンラインミシン認証」方法を推奨します。';

  @override
  String get t_err599 => 'アクティベーションコードが間違っています。\n確認して、もう一度入力して下さい。';

  @override
  String get t_err599_used => '入力されたコードは、別のマシンですでに登録されています。';

  @override
  String get t_err601 => '無線LANをオンにしますか?';

  @override
  String get t_err602 => 'SSID検索中…';

  @override
  String get t_err603 => '設定を適用しますか?';

  @override
  String get t_err604 => '無線LANに接続しました。';

  @override
  String get t_err605 => '無線LAN接続中。';

  @override
  String get t_err606 => 'ネットワークに接続されていません。無線LANの設定を確認してください。';

  @override
  String get t_err607 => 'ネットワークに接続されていません。ネットワークの接続状況を確認してください。';

  @override
  String get t_err608 => 'サーバへの接続に失敗しました。プロキシ等の設定を確認してください。';

  @override
  String get t_err609 => 'ネットワークにエラーが発生しました。';

  @override
  String get t_err611 => '無効';

  @override
  String get t_err612 => 'ネットワーク機能に異常が発生しました。';

  @override
  String get t_err613 => 'データを取り込めませんでした。もう一度やり直してください。';

  @override
  String get t_err614 => '保存されているアクセスポイントの情報があります。\nこの情報を使用して接続しますか？';

  @override
  String get t_err615 => 'ネットワーク接続エラー01';

  @override
  String get t_err616 => 'ネットワークキーが違います。';

  @override
  String get t_err617 => 'ネットワークキーが違います。';

  @override
  String get t_err618 => 'ネットワーク設定をリセットしますか?';

  @override
  String get t_err620 => '電源をいったんOFFしてから再度ONしてください。';

  @override
  String get t_err621 => 'プロキシサーバーに対する認証に失敗しました。\nプロキシサーバーの設定を確認してください。';

  @override
  String get t_err622 => '認証に失敗しました。ユーザー名とパスワードを確認してください。';

  @override
  String get t_err623 => 'キャンセル中';

  @override
  String get t_err624 => '通信中にエラーが発生しました。';

  @override
  String get t_err625 => '完了しました。';

  @override
  String get t_err626 => '無線LAN設定確認';

  @override
  String get t_err627 => '接続 失敗';

  @override
  String get t_err628 => 'サーバーへの接続に失敗しました。\nネットワーク設定を確認してください。';

  @override
  String get t_err629 => 'サーバーへの接続に失敗しました。\n時間をおいて再試行してください。';

  @override
  String get t_err630 => 'ダウンロード中です。しばらくお待ちください。';

  @override
  String get t_err631 => 'ダウンロード中にエラーが発生しました。もう一度 最初から操作を行ってください。';

  @override
  String get t_err632 => 'アクセスポイントが見つかりません。';

  @override
  String get t_err633 => 'データがありません！';

  @override
  String get t_err634 => 'ユーザーズガイドの<エラーメッセージ>をご覧ください。';

  @override
  String get t_err636 => 'サーバが見つかりません。';

  @override
  String get t_err637 => 'サーバーとの通信がタイムアウトしました。時間をおいて再試行してください。';

  @override
  String get t_err638 => '転送中…';

  @override
  String get t_err697 => 'USBケーブルによるデータ転送はできません。';

  @override
  String get t_err84_mdc => 'レジューム記憶を呼び出しますか？\n(My Design Center)';

  @override
  String get t_err84_iqd => 'レジューム記憶を呼び出しますか？\n(IQ Designer)';

  @override
  String get t_err703_b => '\"My Stitch Monitor\"をインストールして、刺繍縫製のモニタリングをしましょう！';

  @override
  String get t_err703_t => '\"IQ Intuition Monitoring\"をインストールして、刺繍縫製のモニタリングをしましょう！';

  @override
  String get t_err704_b => 'お手持ちの携帯端末にモニタリングアプリ\n「My Stitch Monitor」をインストールしましょう。\n\n刺しゅうの進行状況や糸色情報を端末で確認できるようになります。\nまた縫製完了やエラーの通知を受け取ることもできます。';

  @override
  String get t_err704_t => 'お手持ちの携帯端末にモニタリングアプリ\n「IQ Intuition Monitoring」をインストールしましょう。\n\n刺しゅうの進行状況や糸色情報を端末で確認できるようになります。\nまた縫製完了やエラーの通知を受け取ることもできます。';

  @override
  String get t_err705_b => 'お手持ちの携帯端末に画像転送アプリ「My Design Snap」をインストールしましょう。\n\nMy Design Centerでは、転送された画像から簡単に刺しゅうデータが作れます。';

  @override
  String get t_err705_t => 'お手持ちの携帯端末に画像転送アプリ「IQ Intuition Positioning」をインストールしましょう。\n\n\"IQ Designer\" では、転送された画像から簡単に刺しゅうデータが作れます。';

  @override
  String get t_err708 => 'このアプリは、KIT1を購入すると使用できます。';

  @override
  String get t_err709 => 'このアプリは、KIT2を購入すると使用できます。';

  @override
  String get t_err711 => 'この刺しゅうデータは糸色情報が不足しているため、糸色を近似して表示します。\n正しい糸色情報を表示するには、糸色変更画面で、糸色番号を入力してください。';

  @override
  String get t_err713 => 'ご利用前に、エンドユーザー使用許諾契約(EULA)をお読みください。';

  @override
  String get t_err715 => '使用許諾契約書の条項に同意します。';

  @override
  String get t_err01_heated => '主軸モータの温度が上昇したため、安全装置が働きました。糸がからんでいませんか？';

  @override
  String get t_err01_motor => '主軸モータがロックしたため、安全装置が働きました。糸がからんでいませんか？';

  @override
  String get t_err01_npsensor => '針位置センサの異常を検出したため、安全装置が働きました。';

  @override
  String get t_err734 => 'iOS向け';

  @override
  String get t_err735 => 'Android™ 向け';

  @override
  String get t_err738 => '刺しゅう順が変更されます。';

  @override
  String get t_err739 => '特別な模様なので、この機能は使用できません。';

  @override
  String get t_err740 => '内側アウトラインと外側のアウトラインのステッチが重なります。ステッチ幅を小さくするか、オフセット距離を大きくしてください。';

  @override
  String get t_err741 => 'データを取り込み可能な大きさに縮小しました';

  @override
  String get t_err742 => 'ネットワークに接続されていません。無線LANの設定画面を表示しますか？';

  @override
  String get t_err743 => 'メンテナンスのためCanvasWorkspaceサービスが停止しており、本機能はご利用いただけません。復旧まで今しばらくお待ちください。';

  @override
  String get t_err743_s => 'メンテナンスのためArtspiraサービスが停止しており、本機能はご利用いただけません。復旧まで今しばらくお待ちください。';

  @override
  String get t_err744 => 'CanvasWorkspaceにマシン登録できませんでした。\n最初から操作を行ってください。';

  @override
  String get t_err744_s => 'Artspiraサーバーにマシン登録できませんでした。\n最初から操作を行ってください。';

  @override
  String get t_err745 => 'PINコードが不正です。もう一度入力してください。';

  @override
  String get t_err746 => 'マシンがインターネットに接続されていません。';

  @override
  String get t_err747 => 'サーバーへの接続に失敗しました。\nプロキシ等の設定を確認してください。';

  @override
  String get t_err748 => 'サーバーとの通信がタイムアウトしました。\n時間をおいて再試行してください。';

  @override
  String get t_err749 => 'CanvasWorkspace登録を解除してよろしいですか？';

  @override
  String get t_err749_s => 'Artspiraサーバー登録を解除してよろしいですか？';

  @override
  String get t_err750 => 'CanvasWorkspaceからマシン登録を削除できませんでした。ウェブサイトから手動で登録解除してください。';

  @override
  String get t_err750_s => 'Artspiraサーバーからマシン登録を削除できませんでした。\nアプリから手動で登録解除してください。';

  @override
  String get t_err751 => 'データを送信するには、CanvasWorkspaceサーバーに、このミシンの登録が必要です。登録用の設定画面を表示しますか？';

  @override
  String get t_err751_s => 'データを送信するには、Artspiraサーバーに、このミシンの登録が必要です。登録用の設定画面を表示しますか？';

  @override
  String get t_err752 => 'マシン登録が解除された可能性があります。CanvasWorkspaceで確認してください。';

  @override
  String get t_err752_s => 'マシン登録が解除された可能性があります。Artspira で確認してください。';

  @override
  String get t_err753 => 'データのアップロードに失敗しました。\n最初から操作を行ってください。';

  @override
  String get t_err754 => 'データのダウンロードに失敗しました。\n最初から操作を行ってください。';

  @override
  String get t_err755 => '認証に成功しました。\nミシンを再起動させて下さい。\n\nScanNCutとデータを送受信するためには、マシンを再起動後、設定13ページから、CanvasWorkspaceサーバーでマシン登録を行ってください。';

  @override
  String get t_err755_s => '認証に成功しました。\nミシンを再起動させて下さい。';

  @override
  String get t_err756 => 'テンポラリデータポケットには既にデータがありますが、新しいデータに置き換えてよろしいですか？\n＊テンポラリデータポケットのデータは、一定期間後自動で削除されます。';

  @override
  String get t_err757 => 'サーバー上のテンポラリデータポケットにデータを送信しますが、よろしいですか？\n＊テンポラリデータポケットのデータは、一定期間後自動で削除されます。';

  @override
  String get t_err761 => 'テンポラリデータポケットにデータがありません。';

  @override
  String get t_err762 => 'テンポラリーデータポケットには読み込み可能なデータがありません。';

  @override
  String get t_err763 => 'カットデータを受信するには、CanvasWorkspaceサーバーに、ScanNCut及びこのミシンのマシン登録が必要です。(PINコード登録)。\n登録用の設定画面を表示しますか？';

  @override
  String get t_err763_s => 'Artspiraを使ってデータを受け取るために、あなたのミシンを登録してください。(PINコード登録)登録画面に行きますか？';

  @override
  String get t_err764 => 'お手持ちの携帯端末に「Artspira」をインストールしましょう。\nアカウントを作成することで、あなたの刺しゅうの世界が広がります。\n詳細はQRコードからアクセスしてください。';

  @override
  String get t_err765 => 'マシン名の変更ができませんでした。';

  @override
  String get t_err766 => 'マシン名を変更する場合は無線LANをONしてください。';

  @override
  String get t_err770 => '削除に失敗しました。';

  @override
  String get t_err771 => 'カスタム模様をすべて削除すると、編集に使われているその模様が別の模様に変わります。\nすべてのカスタム模様を削除して、よろしいですか？';

  @override
  String get t_err772 => 'データ保存後にインポートしたカスタム模様の削除・変更を行うと、次回読み込み時にデータがオリジナルから変化します。';

  @override
  String get t_err773 => 'インポートしたカスタム模様を含むデータは外部メモリに保存できません。';

  @override
  String get t_err774 => '刺繍データのみ保存します。カスタム模様を含む編集データは外部メモリに保存できません。内部メモリに保存してください。';

  @override
  String get t_err775 => 'データ記憶領域がいっぱいです。\nカスタム模様を１つ選択して、新しい模様に入れ換えてください。';

  @override
  String get t_err776 => 'カスタム模様を入れ換えると、入れ換え前の模様を使用したデータでも連動して模様が変化します。本当に入れ替えますか？';

  @override
  String get t_err_taper01 => 'テーパリングを設定できません。距離を長くするか、角度を大きくしてください。';

  @override
  String get t_err_taper02 => '縫製前に設定を完了してください。';

  @override
  String get t_err_taper03 => 'テーパリング設定を解除しますが、よろしいですか？';

  @override
  String get t_err_taper04 => '現在のテーパリング縫製を終了しますがよろしいですか？';

  @override
  String get t_err_taper05 => 'テーパリング縫製中は、この機能は使用できません。';

  @override
  String get t_err_tapering07 => '返し縫いキー押下で、終了テーパリングを開始します。';

  @override
  String get t_err_tapering08 => '指定回数でテーパリングを終了します。';

  @override
  String get t_err_tapering09 => 'ぬい終わり設定シールを貼った位置でテーパリングを終了します。';

  @override
  String get t_err785 => '刺しゅうを始める前に、上糸と下糸の量が十分にあることを確認してください。途中で糸がなくなると、模様がきれいに仕上がらないおそれがあります。';

  @override
  String get t_err790 => '保存した全てのデータ・設定、ネットワーク情報をリセットし、初期状態に戻しますが、よろしいですか？';

  @override
  String get t_err791 => '削除中…\n電源を切らないでください。';

  @override
  String get t_err792 => 'リセットが完了しました。\nミシンの電源をOFFにしてください。';

  @override
  String get t_err_paidcont_update => 'このデータを使用するためには、ミシンのソフトウェアを最新のバージョンにアップデートする必要があります。';

  @override
  String get t_err_embcarriageevacuate => '枠を元に戻すにはOKキーを押してください。';

  @override
  String get t_err_sr_01 => 'ステッチレギュレーターモジュールを外してください。';

  @override
  String get t_err_sr_02 => '数秒間、布送り動作が無かったので、縫製を終了しました。';

  @override
  String get t_err_sr_03 => 'ステッチレギュレーターによるフリーモーションキルティング・しつけ縫いを開始できます。\n\n布の引きすぎによる針折れに注意してください。';

  @override
  String get t_err_sr_04 => 'モード選択をしてから縫製開始してください';

  @override
  String get t_err_sr_05 => '針が動きます。針元から手を離してください。';

  @override
  String get t_err_sr_06 => 'ステッチレギュレーターモジュールが抜かれました。ステッチレギュレーター専用画面を終了します。 \n再度専用画面に入るには、モジュールを挿してください。';

  @override
  String get t_err_sr_08 => '糸調子を強くする場合は、布の引きすぎによる針折れに注意してください。';

  @override
  String get t_err_sr_09 => '2本針モードでは、この機能を使用できません。\nステッチレギュレーターモジュールのコネクタを取り外し、2本針モードを解除してから、再度お試しください。';

  @override
  String get t_err_sr_10 => 'しつけ縫いではオープントゥ押さえを使用しないでください。\n針が折れてけがをする恐れがあります。';

  @override
  String get t_err_manual_01_b => 'モバイル端末やパソコンで説明書を閲覧したいときは、XXXにアクセスしてください。';

  @override
  String get t_err_manual_02_t => 'モバイル端末やパソコンで説明書を閲覧したいときは、XXXにアクセスしてください。';

  @override
  String get t_err_proj_emb_001 => '刺しゅう枠が小さいため、「スタイラスによるプロジェクター編集」には対応していません。刺しゅう模様の投影のみおこないます。\n\n*OKキーを押すと刺しゅうキャリッジが動きます。';

  @override
  String get t_err_proj_emb_002 => '刺しゅう枠が小さいため、「スタイラスによるプロジェクター編集」には対応していません。刺しゅう模様の投影のみおこないます。';

  @override
  String get t_err_proj_emb_003 => 'プロジェクタ投影を終了します。';

  @override
  String get t_err_proj_emb_004 => '刺しゅう枠が外されたので、プロジェクタ投影を終了します。\n\n* OKキーを押すと刺しゅうキャリッジが動きます。';

  @override
  String get t_err_proj_emb_005 => '刺しゅう枠が外されたので、プロジェクタ投影を終了します。';

  @override
  String get t_err_proj_emb_006 => 'プロジェクタ投影を終了します。\n\n* OKキーを押すと刺しゅうキャリッジが動きます。';

  @override
  String get t_err_proj_emb_007 => '模様の選択をキャンセルしますが、よろしいですか？\n\n* OKキーを押すと刺しゅうキャリッジが動きます。';

  @override
  String get t_err_proj_emb_008 => '刺しゅうデータを確定し、\"My Design Center\"を終了します。\n刺しゅう編集画面に移行してもよろしいですか？\n\n*OKキーを押すと、プロジェクタ投影を終了し、刺しゅうキャリッジが動きます。';

  @override
  String get t_err_proj_emb_009 => '刺しゅうデータを確定し、\"IQ Designer\"を終了します。\n刺しゅう編集画面に移行してもよろしいですか？\n\n*OKキーを押すと、プロジェクタ投影を終了し、刺しゅうキャリッジが動きます。';

  @override
  String get t_err_proj_emb_010 => '処理中…';

  @override
  String get t_err_proj_emb_011 => '終了中';

  @override
  String get t_err_proj_emb_012 => 'プロジェクタ投影を開始します。\n\n* OKキーを押すと刺しゅうキャリッジが動きます。';

  @override
  String get t_err_proj_emb_013 => 'プロジェクタ投影を開始します。';

  @override
  String get t_err_proj_emb_014 => 'プロジェクター投影中は、この機能は使用できません。';

  @override
  String get t_err_proj_smallframe => '枠が小さいので使用できません';

  @override
  String get t_err_mdc_import_01 => 'このデータは読み込むときにサイズが変わるので、必要に応じて調整してください。';

  @override
  String get t_err_voiceg_01 => 'ボイスガイダンスのデータを確認中・・・';

  @override
  String get t_err_voiceg_02 => 'ボイスガイダンスが準備できました。設定をONにします。';

  @override
  String get t_err_photos_01 => '画像へのマスクを解除します';

  @override
  String get t_err_photos_02 => '画像のサイズ変更をリセットしますがよろしいですか？';

  @override
  String get t_err_photos_03 => '背景除去を解除しますがよろしいですか？';

  @override
  String get t_err_photos_04 => '刺しゅうに変換します';

  @override
  String get t_err_photos_05 => '刺しゅうデータを確定し、ピクチャープレイ刺しゅう機能を終了します。\n刺しゅう編集画面に移行してもよろしいですか？';

  @override
  String get t_err_photos_06 => 'しばらくお待ち下さい。\n変換中は無線 LAN 接続が一時的にオフになります。';

  @override
  String get t_err_photos_exit => '\"ピクチャープレイ刺しゅう機能\"を終了しますが、よろしいですか？';

  @override
  String get t_err_font_old_new => 'データ形式が古いので新しいデータ形式に変換しますか？';

  @override
  String get t_err_font_old_lomited => 'データ形式が古いので編集機能が制限されます。';

  @override
  String get t_err_firstset_wlan => '無線LANを設定してください。\n無線ネットワークの接続設定に移行しますか？';

  @override
  String get t_err_firstset_voiceguidance => 'ボイスガイダンス機能を設定してください。\nボイスガイダンスの設定に移行しますか？';

  @override
  String get t_err_wlan_function_01 => 'この機能を使用するためにはミシン本体のWLAN設定をONにしてネットワーク接続する必要があります。\n無線LANの設定画面を表示しますか？';

  @override
  String get t_err_teachingimage => 'イラストは説明用のためであり、実際の機械外観はモデルによって異なります。';

  @override
  String get t_err_photo_disclaimers => '本機能を使用する場合、お客様はコンテンツに以下を含まないことに同意するものとします。\n• すべての適用法令に反する目的(特に人種差別、差別、憎悪、ポルノまたは児童ポルノのコンテンツもしくは公序良俗に反する発言)に使用すること\n• 他の人のプライバシーやパブリシティの権利を侵害すること\n• 第三者が所有する著作権、商標権、その他の知的財産権を侵害すること\n• 悪意のあるサイトに閲覧者を誘導するようなURLやキーワードを含めること\nお客様は、使用するコンテンツについて、一切の責任を負うことを承諾し、同意するものとします。\n詳細については利用規約をご参照ください。\n\nコンテンツを使用することにより、利用規約とガイドラインを読み、完全に理解したことを保証するものとします。';

  @override
  String get t_err_framemovekey => '* 刺しゅう枠を中央に移動するには縫製画面の枠移動キーを押してください。';

  @override
  String get speech_colorchangeinfo => '縫製が終了しました。\n次の糸色をセットしてください。';

  @override
  String get t_err_updateinfo_01 => '重要なアップデートがあります。\n無線LAN設定画面からアップデートしてください。';

  @override
  String get t_err_updateinfo_02 => '最新ソフトウェアのお知らせを受け取るために、無線LANを設定してください。\nもしくは、ブラザーサポートWEBサイトにアクセスして最新ソフトウェアのアップデート情報を確認してください。';

  @override
  String get t_err_removecarriage => '刺しゅう機を着脱するときは、電源を切ってください。';

  @override
  String get t_err_filter_removed => 'このカテゴリーは対象外のため、フィルターが解除されました。';

  @override
  String get t_err_filter_cleared => 'このカテゴリではフィルター機能は対象外のため、解除します。よろしいですか？';

  @override
  String get t_principal07 => '［押えレバー］';

  @override
  String get t_principal07_01 => '\n押えレバーを上げると押えが上がり、\n押えレバーを下げると押えが下がります。\n\n※押えレバーが下がっていないと、ミシンは動きません。';

  @override
  String get t_principal07_02 => '(ａ)押え\n(ｂ)押えレバー';

  @override
  String get t_principal03 => '［スピードコントロールレバー］';

  @override
  String get t_principal03_01 => '\n実用ぬいの模様は、\nスピードコントロールレバーをスライドすることによって、ぬう速さの調節ができます。\n左にスライドさせるとゆっくりに、\n右にスライドさせるとはやくなります。';

  @override
  String get t_principal03_02 => '(ａ)スピードコントロールレバー\n(ｂ)ゆっくり\n(ｃ)はやく';

  @override
  String get t_principal12 => '［プーリー］';

  @override
  String get t_principal12_01 => 'プーリーを回すと、針が上下します。\n\n※プーリーは手前側に回してください。';

  @override
  String get t_principal08 => '［補助テーブル］';

  @override
  String get t_principal08_01 => '補助テーブル内のアクセサリーケースに\n押えやボビンなどが保管できます。\nカフス付け、ズボンの裾上げなど\n筒状になったものをぬうときには、\n補助テーブルを取り外してください。';

  @override
  String get t_principal10 => '［ニーリフター］';

  @override
  String get t_principal10_00 => '(ａ)ニーリフター';

  @override
  String get t_principal10_01 => '\nニーリフターは手を使わずに\n押えを上げたり、下げたりできるので、\nキルトなどをぬうときに使うと便利です。\n\n１．取り付けは、差し込み口の切り込みに\nニーリフターのツメを合わせて、奥まで差し込みます。';

  @override
  String get t_principal10_03_00 => '(ａ)押え';

  @override
  String get t_principal10_03 => '\n２．ひざを使ってニーリフターを右に押すと\n押えが上がり、離すと押えが下がります。';

  @override
  String get t_principal11 => '［フットコントローラー］';

  @override
  String get t_principal11_00 => 'フットコントローラーを用いて、\n実用ぬいのスタート／ストップを行うことができます。\n\n１．フットコントローラーのプラグを\nジャックに差し込みます。';

  @override
  String get t_principal11_02 => '２．ぬい始めるには、\nフットコントローラーをゆっくりと踏み込んでください。\n足を離すと、ミシンは停止します。\n\n※本体のスピードコントロールレバーで\nフットコントローラーの最高速度を\n調節することができます。';

  @override
  String get t_xv_principal11_01 => 'マルチ機能フットコントローラーを使用すると、縫製のスタート/ストップの他に糸切や返し縫など、さまざまな機能を設定してミシンを操作することができます。';

  @override
  String get t_xv_principal11_02 => '１．メインフットコントローラー底面のミゾに固定プレートの幅広い面を合わせ、ネジ止めします。';

  @override
  String get t_xv_principal11_03 => '２．固定プレートのもう片方を、サイドペダル側側面にある切り込み口に通し、ネジ止めします。';

  @override
  String get t_xv_principal11_04 => '３．サイドペダルの接続プラグを、メインフットコントローラー背面のジャックへ差し込みます。';

  @override
  String get t_xv_principal11_05 => '４．メインフットコントローラーの接続プラグを、ミシン本体の右側面にあるフットコントローラージャックに差し込みます。';

  @override
  String get t_xv_principal11_06 => '５．ぬい始めるには、\nフットコントローラーをゆっくりと踏み込んでください。\n足を離すと、ミシンは停止します。\n\n※本体のスピードコントロールレバーで\nフットコントローラーの最高速度を\n調節することができます。';

  @override
  String get t_principal11_01_02 => '(ａ)フットコントローラー\n(ｂ)フットコントローラージャック\n';

  @override
  String get t_principal09 => '［ドロップレバー］';

  @override
  String get t_principal09_01 => '\nボタンつけなどをするときに使います。\nドロップレバーは補助テーブルを外し、\n押えを上げてから操作します。';

  @override
  String get t_principal09_02 => '(ａ)ドロップレバー';

  @override
  String get t_principal_buttons_01 => '［針上下スイッチ］';

  @override
  String get t_principal_buttons_01_01 => 'ぬい方向を変えるときや細かいところを\nぬうときに使います。\n押すと針の位置が上下に切り換わります。\n繰り返し押すと１針ぬいができます。';

  @override
  String get t_principal_buttons_02 => '［糸切りスイッチ］';

  @override
  String get t_principal_buttons_02_01 => 'ぬい終わったあとに押すと\n上糸、下糸を自動的に切ります。';

  @override
  String get t_principal_buttons_06 => '［押え上下スイッチ］';

  @override
  String get t_principal_buttons_06_01 => 'このボタンを押すと押えが下りて布を押えます。\nもう一度押すと押えが上がります。\nこのボタンを使用すると、\n押えを最高位まで上げられます。';

  @override
  String get t_principal_buttons_05 => '［自動糸通しスイッチ］';

  @override
  String get t_principal_buttons_05_01 => 'このボタンを押すと、\n自動的に針に上糸が通ります。';

  @override
  String get t_principal_buttons_04 => '［スタート／ストップスイッチ］';

  @override
  String get t_principal_buttons_04_01 => '押すと数針ゆっくりとぬってから、\nスピードコントロールレバーで\n設定されている速度でぬい始めます。\nもう一度押すとストップします。\nスタート／ストップスイッチを押している間は\n低速でぬいます。\nスイッチはミシンの状態によって色が変わります。\n\n緑色：スタート可能な状態のときおよび縫製中\n赤色：スタート不可能な状態のとき';

  @override
  String get t_principal_buttons_03 => '［返しぬいスイッチ］';

  @override
  String get t_principal_buttons_03_01 => 'ぬい始めやぬい終わりの返しぬいをするときに使います。\n直線、ジグザグ模様を選んだ場合は、押している間だけ低速で返しぬいをします。(逆方向にぬいます)。\nただし、直線、ジグザグ模様以外を選んだ場合は、押すと止めぬいをします。(同じ場所で３～５針ぬって自動停止します。)';

  @override
  String get t_principal_buttons_07 => '［止めぬいスイッチ］';

  @override
  String get t_principal_buttons_07_01 => 'ぬい始めやぬい終わりの止めぬいを\nするときに使います。実用模様を選んだ場合は、\n押すと止めぬいをします。\n(同じ場所で３～５針ぬって自動停止します。)文字・飾り模様を選んだ場合は、\n押すとぬっている模様の最後までぬってから\n止めぬいをします。';

  @override
  String get t_basic13 => '［上糸通し］';

  @override
  String get t_basic13_01_02 => '(ａ)押え上下スイッチ';

  @override
  String get t_basic13_01 => '動画キーを押すと上糸通しの操作手順を\n動画で見ることができます。\n\n１．押え上下スイッチを押して押えを上げます。';

  @override
  String get t_basic13_02_00 => '(ａ)針上下スイッチ';

  @override
  String get t_basic13_02 => '\n２．針上下スイッチを押して針を上げます。';

  @override
  String get t_basic13_03_02 => '(ａ)糸立て\n(ｂ)糸こま押え\n(ｃ)糸こま';

  @override
  String get t_basic13_03 => '\n３．糸立て棒をななめ手前に軽くおこし、\n糸こまを差し込みます。\nこのとき、糸は糸こまの手前から出してください。\n糸こま押えを奥までしっかり差し込み、\n糸立て棒を元に戻します。';

  @override
  String get t_basic13_11_02 => '(ａ)糸こま押え(小)\n(ｂ)チーズ巻き\n(ｃ)すきま';

  @override
  String get t_basic13_11 => '\n※チーズ巻きの細い糸こまを使用するときは\n糸こま押え(小)を使い、\n糸こまから少し離してセットしてください。';

  @override
  String get t_basic13_04_02 => '(ａ)糸案内板 ';

  @override
  String get t_basic13_04 => '\n４．糸こまからの糸を両手で持ち、\n糸案内板の下から上へ糸をかけます。 ';

  @override
  String get t_basic13_05 => '５．糸こまからの糸を右手で保持しながら、\n左手で糸端を持ってミゾに沿って\n下→上→下の順に糸を通します。 ';

  @override
  String get t_basic13_06_02 => '(ａ)針棒糸かけ \n';

  @override
  String get t_basic13_06 => '６．針棒糸かけに糸をかけます。\n ';

  @override
  String get t_basic13_07 => '７．押え上下スイッチを押して押えを下げます。';

  @override
  String get t_basic13_08_02 => '(ａ)糸案内皿 \n';

  @override
  String get t_basic13_08 => '８．糸案内皿に糸をかけます。\n糸ガイドのミゾに糸が通っていることを確認してください。\n糸は、糸案内皿の奥まで確実に入れてください。\n針穴に糸が通らないことがあります。\n ';

  @override
  String get t_basic13_09_02 => '(ｂ)糸切り \n';

  @override
  String get t_basic13_09 => '９．糸を糸切りにかけて、糸を切ります。 ';

  @override
  String get t_basic13_10_02 => '(ａ)糸通しスイッチ';

  @override
  String get t_basic13_10 => '\n１０．糸通しスイッチを押します。 ';

  @override
  String get t_basic14 => '［下糸巻き］';

  @override
  String get t_basic14_01_02 => '(ａ)ミゾ\n(ｂ)下糸巻き軸のバネ';

  @override
  String get t_basic14_00 => '\n動画キーを押すと下糸巻きの操作手順を\n動画で見ることができます。\n手順は以下の説明を読んだ後、操作してください。\n\n１．ボビンのミゾと下糸巻き軸のバネを合わせて、\nボビンを軸にセットします。';

  @override
  String get t_basic14_02 => '\n２．補助糸立て棒を上におこします。';

  @override
  String get t_basic14_02_02 => '(ａ)補助糸立て棒';

  @override
  String get t_basic14_03 => '\n３．糸こまを糸が手前から出るように\n補助糸立て棒に差し込み、\n糸こま押えを奥までしっかり差し込みます。';

  @override
  String get t_basic14_03_02 => '(ａ)糸こま押え\n(ｂ)補助糸立て棒\n(ｃ)糸こま';

  @override
  String get t_basic14_04 => '\n４．糸こまからの糸を右手で保持しながら、\n左手で糸端を持って糸案内に糸をかけます。';

  @override
  String get t_basic14_04_02 => '(ａ)糸案内';

  @override
  String get t_basic14_05 => '\n５．皿に糸をかけます。\n糸は皿の下に確実に入れてください。 ';

  @override
  String get t_basic14_05_02 => '(ａ)皿';

  @override
  String get t_basic14_06 => '６．糸をボビンに時計まわりに\n５～６回巻きつけます。';

  @override
  String get t_basic14_07 => '(ａ)ガイドミゾ(カッター内蔵)\n(ｂ)ボビン受け座\n\n７．糸端をボビンの受け座のガイドミゾに引っかけて、\n右へ糸を引き、カッターで糸を切ります。 ';

  @override
  String get t_basic14_08_02 => '\n８．ボビンホルダーを左へカチッと音がするまで押します。\n糸巻き画面が表示されます。';

  @override
  String get t_basic14_08_03 => '(ａ)ボビンホルダー(スイッチ)';

  @override
  String get t_basic14_08_04 => '\n* 下糸巻きスライダーを使って、糸巻き量を5段階に調整することができます。';

  @override
  String get t_basic14_08_05 => '(ａ)下糸巻きスライダー\n(ｂ)多い\n(ｃ)少ない';

  @override
  String get t_basic14_09 => '９．スタートボタンで下糸巻きが始まります。\n満タンになる前に止めたいときは\nストップボタンを押してください。\n';

  @override
  String get t_basic14_10 => '１０．下糸巻きの速さは、\n糸巻き画面の－/＋を押して調整できます。\nまた、「OK」を押すと、\n糸巻き画面を最小化することができます。';

  @override
  String get t_basic14_101 => '１１．糸を巻き終わると\n自動的にボビンの回転が止まり、\nボビンホルダーが元の位置に戻ります。';

  @override
  String get t_basic14_102 => '１２．糸を切ってボビンを取り外します。 ';

  @override
  String get t_basic14_11 => '\n［糸立て棒で下糸を巻く場合］\n\n糸立て棒を使用して下糸巻きを行う事も出来ます。\n\n１．ボビンのミゾと下糸巻き軸のバネを合わせて、\nボビンを軸にセットします。';

  @override
  String get t_basic14_11_02 => '(ａ)ミゾ\n(ｂ)下糸巻き軸のバネ';

  @override
  String get t_basic14_12 => '\n２．糸立て棒をななめ手前に軽くおこし、\n糸こまを差し込みます。\n糸は糸こまの手前から出してください。\n糸こま押えを奥までしっかり差し込み、\n糸立て棒を元に戻します。';

  @override
  String get t_basic14_12_02 => '(ａ)糸立て棒\n(ｂ)糸こま押え\n(ｃ)糸こま';

  @override
  String get t_basic14_13 => '\n３．糸こまからの糸を両手で持ち、\n糸案内板の下から上へ糸をかけます。\n糸案内に糸をかけます。';

  @override
  String get t_basic14_13_02 => '(ａ)糸案内板\n(ｂ)糸案内';

  @override
  String get t_basic14_14 => '\n４．皿に糸をかけます。\n糸は皿の下に確実に入れてください。 ';

  @override
  String get t_basic14_15_02 => '(ａ)皿';

  @override
  String get t_basic14_16 => '５．糸をボビンに時計まわりに\n５～６回巻きつけます。';

  @override
  String get t_basic14_17 => '(ａ)ガイドミゾ(カッター内蔵)\n(ｂ)ボビン受け座\n\n６．糸端をボビンの受け座のガイドミゾに引っかけて、\n右へ糸を引き、カッターで糸を切ります。';

  @override
  String get t_basic14_18 => '\n７．ボビンホルダーを左へカチッと音がするまで押します。\n糸巻き画面が表示されます。';

  @override
  String get t_basic14_18_02 => '(ａ)ボビンホルダー(スイッチ)';

  @override
  String get t_basic14_20 => '８．スタートボタンで下糸巻きが始まります。\n';

  @override
  String get t_basic14_201 => '９．下糸巻きの速さは、\n糸巻き画面の－/＋を押して調整できます。\nまた、「OK」を押すと、\n糸巻き画面を最小化することができます。';

  @override
  String get t_basic14_202 => '１０．糸を巻き終わると自動的にボビンの回転が止まり、ボビンホルダーが元の位置に戻ります。';

  @override
  String get t_basic14_203 => '１１．糸を切ってボビンを取り外します。 ';

  @override
  String get t_basic14_21_02 => '\n※チーズ巻きの細い糸こまを使用するときは、\n糸こま押え(小)を使い、\n糸こまとの間に少しすきまを作ってください。 ';

  @override
  String get t_basic14_21_03 => '(ａ)糸こま押え(小)\n(ｂ)チーズ巻き\n(ｃ)すきま';

  @override
  String get t_basic15 => '［針の交換］';

  @override
  String get t_basic15_00 => '\n＊正しい針\n平らな物(針板、ガラス板など)に\n針の平らな面を合わせ、\nすきまが平行である針を使用してください。\n針先が曲がったり、\nつぶれているものは使用しないでください。';

  @override
  String get t_basic15_00_01 => '(ａ)平行なすきま\n(ｂ)平らなもの';

  @override
  String get t_basic15_01 => '１．針上下スイッチを押して針を上げます。';

  @override
  String get t_basic15_02 => '２．交換キーを押します。';

  @override
  String get t_basic15_03 => '３．付属のドライバーで\n針の止めネジを手前に回してゆるめ、針を抜きます。';

  @override
  String get t_basic15_04 => '\n４．新しい針の平らな面を後ろ側に向けて、\nストッパーに当たるまで差し込み、\n止めネジをしっかりとしめます。';

  @override
  String get t_basic15_04_02 => '(ａ)ストッパー\n(ｂ)針設置穴\n(ｃ)平らな面';

  @override
  String get t_basic15_05 => '５．交換キーを押して、すべてのキーとスイッチのロックを解除します。';

  @override
  String get t_basic16 => '［押えの交換］';

  @override
  String get t_basic16_01 => '＊押えの外し方\n\n１．針上下スイッチを押して針を上げます。';

  @override
  String get t_basic16_02 => '２．交換キーを押します。';

  @override
  String get t_basic16_03 => '\n３．押えレバーを上げます。';

  @override
  String get t_basic16_03_02 => '(ａ)押え\n(ｂ)押えレバー';

  @override
  String get t_basic16_04 => '\n４．押えホルダーの黒いボタンを押して押えを取ります。';

  @override
  String get t_basic16_04_02 => '(ａ)黒いボタン\n(ｂ)押えホルダー';

  @override
  String get t_basic16_05 => '\n＊押えの取り付け\n\n１．押えホルダーのミゾと押えのピンが合うように\n押えを置き、押えレバーを下げます。';

  @override
  String get t_basic16_05_02 => '(ａ)ノッチ\n(ｂ)ピン';

  @override
  String get t_basic16_06 => '２．交換キーを押して、すべてのキーとスイッチのロックを解除します。';

  @override
  String get t_basic16_07 => '(ａ)押え\n(ｂ)押えレバー\n\n３．押えレバーを上げます。';

  @override
  String get t_basic17 => '［下糸セット］';

  @override
  String get t_basic17_01 => '動画キーを押すと下糸セットの操作手順を\n動画で見ることができます。\n\n１．押え上下スイッチを押して押えを上げます。';

  @override
  String get t_basic17_02 => '(ａ)針板ふた\n(ｂ)つまみ\n\n２．針板ふたの横のつまみを右に押して\n針板ふたを外します。';

  @override
  String get t_basic17_03 => '３．右手でボビンを持ち、左手で糸端を持ちます。';

  @override
  String get t_basic17_04 => '４．右手でボビンを押えながら、\n矢印の向きから糸が出るように、\n下糸を巻いたボビンをセットします。';

  @override
  String get t_basic17_05 => '５．右手でボビンを押えながら、左手で糸端を持ち、\n少し引きながら糸をミゾにそって通し、\n終わりの部分で手前に引きます。\nカッターで糸が切れます。';

  @override
  String get t_basic17_05_02 => '(ａ)カッター\n';

  @override
  String get t_basic17_06 => '６．針板ふたを閉めます。\n針板ふたの左側の凸部を差し込み、\n右側を上からカチッと音が出るまで押します。';

  @override
  String get t_embbasic17 => '［布地に芯を貼るには］';

  @override
  String get t_embbasic17_00 => '\n刺しゅうをきれいに仕上げるために\n刺しゅう用接着芯はかかせません。\nぬい縮みや模様くずれをふせぐために、\n以下の項目を必ず守ってください。';

  @override
  String get t_embbasic17_01 => '\n１．接着芯を使うときは、\n使用する刺しゅう枠よりも大きくしてください。';

  @override
  String get t_embbasic17_01_02 => '(ａ)刺しゅう枠の大きさ\n(ｂ)接着芯';

  @override
  String get t_embbasic17_02 => '\n２．アイロンがけを行って、裏面に接着芯を貼ります。\n\n＊アイロンがけができない、もしくは困難な布の場合、\n接着せずに芯の上に布を置いて、\n一緒に枠に挟んでください。';

  @override
  String get t_embbasic17_02_02 => '(ａ)接着面\n(ｂ)布';

  @override
  String get t_embbasic17_03 => '\n＊オーガンジーやローンといった薄手の布地、タオルやコーデュロイのような毛羽立った布地で\n刺しゅうを行う場合は、\n別売りの水溶性シートをご利用ください。';

  @override
  String get t_embbasic18 => '［刺しゅう枠に布地を張るには］';

  @override
  String get t_embbasic18_01 => '１．刺しゅう枠の外枠の調節ネジをゆるめ、\n内枠を外します。';

  @override
  String get t_embbasic18_02 => '\n２．布地を外枠の上にのせ、内枠ではさみます。\n外枠の△と内枠の△を合わせて、再び差し込みます。';

  @override
  String get t_embbasic18_02_02 => '(ａ)内枠\n(ｂ)外枠\n(ｃ)調節ネジ';

  @override
  String get t_embbasic18_03 => '３．調節ネジをしっかりとしめ、\n角を引っ張って、布にたるみのない状態にします。\n調節ネジが緩まないようにしてください。';

  @override
  String get t_embbasic18_04 => '\n４．やさしく布をひっぱって、\n布にたるみのない状態にします。\n布を張った後、緩まないように調節ネジをしめてください。\n\n※布を張った後、たるんでいないかどうか\nしっかり確認してください。\n\n※外枠と内枠の高さが同じになるように、\nしっかりはめてください。\n\n※注意\n布は４隅から引っ張ってください。\n布を張っている間は、\n調節ネジをきつくしめておいてください。';

  @override
  String get t_embbasic18_04_02 => '(ａ)外枠\n(ｂ)内枠\n(ｃ)布地の表';

  @override
  String get t_embbasic18_04_11 => '＊レバーつきの刺しゅう枠の場合\n';

  @override
  String get t_embbasic18_04_12 => '\n１．レバーを下げます。';

  @override
  String get t_embbasic18_04_13 => '２．刺しゅう枠の調節ネジを手でゆるめ、内枠を外します。';

  @override
  String get t_embbasic18_04_14 => '\n３．布をセットします。';

  @override
  String get t_embbasic18_04_15 => '４．刺しゅう枠の調節ネジを手でしっかりとしめます。';

  @override
  String get t_embbasic18_04_16 => '５．レバーを元の位置に戻してください。';

  @override
  String get t_embbasic18_05 => '\n［刺しゅうシートの使い方］\n';

  @override
  String get t_embbasic18_05_01 => '刺しゅうしたい位置が決まっているときは、\n刺しゅうシートを使って枠にはめます。\n\n１．布地にチャコペンで、\n刺しゅうしたい位置に印を付けます。';

  @override
  String get t_embbasic18_05_02 => '(ａ)模様\n(ｂ)基準線';

  @override
  String get t_embbasic18_06 => '\n２．内枠の上に刺しゅうシートをのせ、\nシートの基準線と布地の印を合わせます。';

  @override
  String get t_embbasic18_06_02 => '(ａ)内枠\n(ｂ)基準線';

  @override
  String get t_embbasic18_07 => '\n３．刺しゅうする布地にたるみがないように、\nしっかりと張ります。';

  @override
  String get t_embbasic18_07_02 => '(ａ)内枠\n(ｂ)外枠';

  @override
  String get t_embbasic18_08 => '４．刺しゅうシートを取り出します。';

  @override
  String get t_embbasic19 => '［刺しゅう枠をセットするには］';

  @override
  String get t_embbasic19_01 => '＊注意\n枠をつける前に新しいボビンを入れてください。\n\n１．押え上下スイッチを押します。';

  @override
  String get t_embbasic19_02 => '\n２．枠固定レバーを上げます。';

  @override
  String get t_embbasic19_03 => '\n３．刺しゅう枠ホルダーを左側に押しながら、\n刺しゅう枠をスライドします。';

  @override
  String get t_embbasic19_03_02 => '(ａ)刺しゅう枠ホルダー\n(ｂ)刺しゅう枠のガイド';

  @override
  String get t_embbasic19_04 => '４．刺しゅう枠ホルダーの△と\n刺しゅう枠の△を合わせて、\n刺しゅう枠ホルダーを元に戻します。';

  @override
  String get t_embbasic19_05 => '\n５．枠固定レバーを下げます。\n\n＊枠固定レバーを下げなかった場合、\n模様が正しくぬえなかったり、\n押えが当たって故障の原因になります。';

  @override
  String get t_embbasic19_05_02 => '(ａ)枠固定レバー';

  @override
  String get t_embbasic19_06 => '\n［刺しゅう枠を取り外すときは］\n\n１．枠固定レバーを上げます。';

  @override
  String get t_embbasic19_07 => '２．刺しゅう枠を手前に引いて取り外します。';

  @override
  String get t_embbasic20 => '［刺しゅう機の取り付け］';

  @override
  String get t_embbasic20_01 => '手順は以下の説明を読んだ後、操作してください。\n\n１．電源を切り、(お持ちであれば)補助テーブルをはずしてください。';

  @override
  String get t_embbasic20_03 => '\n２．刺しゅう機のコネクタを\n刺しゅう機差し込み口に合わせ、\nカチッと音がするまでしっかりと差し込みます。';

  @override
  String get t_embbasic20_03_02 => '(ａ)コネクタ\n(ｂ)差し込み口';

  @override
  String get t_embbasic20_04 => '(ａ)ＯＦＦ\n(ｂ)ＯＮ\n\n３．電源スイッチを入れると、メッセージが表示されます。';

  @override
  String get t_embbasic20_05 => '４．ＯＫを押します。\nキャリッジが初期設定の位置に移動します。';

  @override
  String get t_embbasic20_06 => '［刺しゅう機の取り外し方］';

  @override
  String get t_embbasic20_06_02 => '\n(ａ)ＯＦＦ\n(ｂ)ＯＮ\n\n手順は以下の説明を読んだ後、操作してください。\n\n１．電源を切ります。';

  @override
  String get t_embbasic20_07 => '(ａ)取り外しボタン(刺しゅう機の下部にあります)\n\n２．取り外しボタンを押しながら取り外します。';

  @override
  String get t_xp_embbasic21 => '［刺しゅう押え＜Ｗ＞の取りつけ］';

  @override
  String get t_xp_embbasic21_01 => '１．針上下スイッチを押して針を上げます。';

  @override
  String get t_xp_embbasic21_02 => '２．交換キーを押します。';

  @override
  String get t_xp_embbasic21_03 => '\n３．押えを上げます。';

  @override
  String get t_xp_embbasic21_04 => '\n４．黒いボタンを押して押えを取り外します。';

  @override
  String get t_xp_embbasic21_04_02 => '(ａ)黒いボタン\n(ｂ)押えホルダー';

  @override
  String get t_xp_embbasic21_05 => '\n５．付属のドライバーで押えホルダーのネジをゆるめて、押えホルダーを外します。';

  @override
  String get t_xp_embbasic21_05_02 => '(ａ)ドライバー\n(ｂ)押えホルダー\n(ｃ)押えホルダーのネジ';

  @override
  String get t_xp_embbasic21_06 => '(ａ)押えレバー\n\n６．押えレバーを下げます。';

  @override
  String get t_xp_embbasic21_07_01 => '(ａ) 押え棒\n ';

  @override
  String get t_xp_embbasic21_07_02 => '７．刺しゅう押え＜Ｗ＞を押え棒の後ろから取り付けます。';

  @override
  String get t_xp_embbasic21_08_01 => '(ａ)押えホルダーのネジ';

  @override
  String get t_xp_embbasic21_08_02 => '８．刺しゅう押えを右手で押えながら、付属のネジ回しでネジを締めて取り付けます。';

  @override
  String get t_xp_embbasic21_09 => '９．交換キーを押して、すべてのキーとスイッチのロックを解除します。';

  @override
  String get t_embbasic21 => '［刺しゅう押え＜Ｗ＞の取りつけ］';

  @override
  String get t_embbasic21_01 => '１．針上下スイッチを押して針を上げます。';

  @override
  String get t_embbasic21_02 => '２．交換キーを押します。';

  @override
  String get t_embbasic21_03 => '\n３．押えを上げます。';

  @override
  String get t_embbasic21_04 => '\n４．黒いボタンを押して押えを取り外します。';

  @override
  String get t_embbasic21_04_02 => '(ａ)黒いボタン\n(ｂ)押えホルダー';

  @override
  String get t_embbasic21_05 => '\n５．付属のドライバーで押えホルダーのネジをゆるめて、\n押えホルダーを外します。';

  @override
  String get t_embbasic21_05_02 => '(ａ)ドライバー\n(ｂ)押えホルダー\n(ｃ)押えホルダーのネジ';

  @override
  String get t_embbasic21_06 => '(ａ)押えレバー\n\n６．押えレバーを下げます。';

  @override
  String get t_embbasic21_07 => '(ａ)腕\n(ｂ)針止めネジ\n(ｃ)押えホルダーのネジ\n(ｄ)ワイパー\n\n７．＜Ｗ＞押えの腕が針抱きの後ろにくるように、\n＜Ｗ＞押えを押え棒に取り付けます。';

  @override
  String get t_embbasic21_08 => '８．付属のドライバーでネジを確実にしめます。';

  @override
  String get t_embbasic21_09 => '９．交換キーを押して、すべてのキーとスイッチのロックを解除します。';

  @override
  String get t_embbasic21_emb_07 => '(ａ)腕\n(ｂ)針止めネジ\n(ｃ)押えホルダーのネジ\n(ｄ)ワイパー\n\n３．＜Ｗ＞押えの腕が針抱きの後ろにくるように、\n＜Ｗ＞押えを押え棒に取り付けます。';

  @override
  String get t_embbasic21_emb_08 => '４．付属のドライバーでネジを確実にしめます。';

  @override
  String get t_embbasic21_emb_09 => '５．交換キーを押して、すべてのキーとスイッチのロックを解除します。';

  @override
  String get t_xv_embbasic21 => '［刺しゅう押え＜Ｗ＋＞の取りつけ］';

  @override
  String get t_xv_embbasic21_05 => '\n５．付属のドライバーで押えホルダーのネジをゆるめて、\n押えホルダーを外します。';

  @override
  String get t_xv_embbasic21_07_01 => '(ａ)押え棒\n ';

  @override
  String get t_xv_embbasic21_07_02 => '７．刺しゅう押え＜Ｗ＋＞を押え棒の後ろから取り付けます。';

  @override
  String get t_xv_embbasic21_08_01 => '(ａ)押えホルダーのネジ\n';

  @override
  String get t_xv_embbasic21_08_02 => '８．刺しゅう押えを右手で押えながら、付属のネジ回しでネジを締めて取り付けます。';

  @override
  String get t_xv_embbasic21_09 => '９．LEDポインター付き刺しゅう押え＜W+＞のプラグをミシン背面のコネクタに差し込みます。';

  @override
  String get t_xv_embbasic21_10 => '１０．交換キーを押して、すべてのキーとスイッチのロックを解除します。';

  @override
  String get t_embbasic22 => '［適した接着芯］';

  @override
  String get t_embbasic22_00_01 => '１．アイロンがかけられる布';

  @override
  String get t_embbasic22_00_02 => '２．アイロンがかけられない布';

  @override
  String get t_embbasic22_00_03 => '３．薄い布地';

  @override
  String get t_embbasic22_00_04 => '４．起毛した布地';

  @override
  String get t_embbasic22_00_05 => '\nきれいな仕上がりにするために、\n接着芯を必ず使ってください。\n以上の説明にしたがって接着芯を選んでください。';

  @override
  String get t_embbasic22_01 => '\n［１．アイロンがかけられる布］\n';

  @override
  String get t_embbasic22_01_02 => '布の裏にアイロンで貼ります。\n枠のサイズよりも大きな接着芯を使ってください。';

  @override
  String get t_embbasic22_01_03 => '(ａ)枠サイズ\n(ｂ)接着芯';

  @override
  String get t_embbasic22_02 => '［２．アイロンがかけられない布］';

  @override
  String get t_embbasic22_02_02 => '\nアイロンで接着せず、\n芯の上にのせて枠にはめて刺しゅうしてください。';

  @override
  String get t_embbasic22_03 => '［３．薄い布地］';

  @override
  String get t_embbasic22_03_02 => '\n接着芯を使用すると布が固くなってしまうので、\n水溶性シートを使用してください。';

  @override
  String get t_embbasic22_04 => '［４．起毛した布地］';

  @override
  String get t_embbasic22_04_02 => '\nアイロンで接着すると毛が抜けてしまうので、\n水溶性シートを使うか\n接着芯の上に布をのせて刺しゅうしてください。';

  @override
  String get t_embbasic23 => '［糸調子の調節］';

  @override
  String get t_embbasic23_01 => '刺しゅうのときは、上糸が布地の裏側に少し見えるように、糸調子を調節します。';

  @override
  String get t_embbasic23_01_01 => '１．よい状態';

  @override
  String get t_embbasic23_01_02 => '２．上糸が強い状態';

  @override
  String get t_embbasic23_01_03 => '３．上糸が弱い状態';

  @override
  String get t_embbasic23_02 => '［１．よい状態]';

  @override
  String get t_embbasic23_02_02 => '\n布地の裏側に、少し上糸が見えるくらいに調節します。\n糸調子が正しく調節されていないと、ぬい目がきれいになりません。\n布地にしわがよったり、糸が切れたりします。';

  @override
  String get t_embbasic23_03 => '［２．上糸が強い]';

  @override
  String get t_embbasic23_03_02 => '\n上糸が強すぎると、下糸が見えてしまいます。';

  @override
  String get t_embbasic23_03_03 => '\n－を押して糸調子をゆるくしてください。';

  @override
  String get t_embbasic23_04 => '［３．上糸が弱すぎる]';

  @override
  String get t_embbasic23_04_02 => '\n上糸が弱すぎると、ゆるんだ仕上がりになったり\n上糸がループ状になったりします。';

  @override
  String get t_embbasic23_04_03 => '\n＋を押して糸調子を強くしてください。';

  @override
  String get t_trouble22 => '［上糸が切れる]';

  @override
  String get t_trouble22_01 => '＊原因１\n上糸の通し方がまちがっている\n(糸こまが正しくセットされていない、\n糸こま押えの大きさが合っていない、\n針棒糸かけから糸がはずれているなど)。\n\n＊対処\n糸を正しく通します。';

  @override
  String get t_trouble22_02 => '＊原因２\n糸にこぶや結び目がある。\n\n＊対処\n糸のこぶや結び目を取り除きます。';

  @override
  String get t_trouble22_03 => '＊原因３\n針に比べて糸が太すぎる。\n\n＊対処\n布地と糸と針の組み合わせ表を見て、\n適切な組み合わせにします。';

  @override
  String get t_trouble22_04 => '＊原因４\n上糸調子が強すぎる。\n\n＊対処\n糸調子を調節します。';

  @override
  String get t_trouble22_05 => '＊原因５\n糸がからまっている。\n\n＊対処\nかまなどにからんだ糸をはさみなどで\n切って取り除きます。';

  @override
  String get t_trouble22_06 => '＊原因６\n針が曲がっていたり、針先がつぶれている。\n\n＊対処\n新しい針に交換します。';

  @override
  String get t_trouble22_07 => '＊原因７\n針の取り付け方がまちがっている。\n\n＊対処\n針を正しく取りつけます。';

  @override
  String get t_trouble22_08 => '＊原因８\n針板の穴の周辺にキズがある。\n\n＊対処\n針板を交換するか、\nお買い上げの販売店にご相談ください。';

  @override
  String get t_trouble22_09 => '＊原因９\n押えの穴の周辺にキズがある。\n\n＊対処\n押えを交換するか、\nお買い上げの販売店にご相談ください。';

  @override
  String get t_trouble22_10 => '＊原因１０\n内かまにキズがある。\n\n＊対処\n内かまを交換するか、\nお買い上げの販売店にご相談ください。';

  @override
  String get t_trouble23 => '［下糸が切れる]';

  @override
  String get t_trouble23_01 => '＊原因１\n下糸セットのしかたがまちがっている。\n\n＊対処\n下糸を正しくセットします。';

  @override
  String get t_trouble23_02 => '＊原因２\nボビンに傷があり、回転がなめらかでない。\n\n＊対処\nボビンを交換します。';

  @override
  String get t_trouble23_03 => '＊原因３\n糸がからまっている。\n\n＊対処\nかまなどにからんだ糸をはさみなどで\n切って取り除きます。';

  @override
  String get t_trouble24 => '［ぬい目が飛ぶ]';

  @override
  String get t_trouble24_01 => '＊原因１\n上糸の通し方がまちがっている。\n\n＊対処\n糸を通す順序を調べて通し直します。';

  @override
  String get t_trouble24_02 => '＊原因２\n布地に合った糸・針を使っていない。\n\n＊対処\n布地と糸と針の組み合わせ表を見て、\n適正な組み合わせにします。';

  @override
  String get t_trouble24_03 => '＊原因３\n針が曲がっていたり、針先がつぶれている。\n\n＊対処\n新しい針に交換します。';

  @override
  String get t_trouble24_04 => '＊原因４\n針の取り付け方がまちがっている。\n\n＊対処\n針を正しく取り付けます。';

  @override
  String get t_trouble24_05 => '＊原因５\n品質の悪い針を使っている。\n\n＊対処\n正しい針に交換します。';

  @override
  String get t_trouble24_06 => '＊原因６\n針板の下にゴミがたまっている。\n\n＊対処\nブラシなどでゴミを取り除きます。';

  @override
  String get t_trouble25 => '［布地にしわがよる］';

  @override
  String get t_trouble25_01 => '＊原因１\n上糸の通し方、\nまたは下糸のセットのしかたがまちがっている。\n\n＊対処\n上糸を通す順番を調べて通し直します。\nまたは、下糸を正しくセットします。';

  @override
  String get t_trouble25_02 => '＊原因２\n糸こまが正しく付いていない。\n\n＊対処\n糸こまの付け方を調べて付け直します。';

  @override
  String get t_trouble25_03 => '＊原因３\n布地に合った糸・針を使っていない。\n\n＊対処\n布地と糸と針の組み合わせ表を見て、\n適正な組み合わせにします。';

  @override
  String get t_trouble25_04 => '＊原因４\n針が曲がっていたり、針先がつぶれている。\n\n＊対処\n新しい針に交換します。';

  @override
  String get t_trouble25_05 => '＊原因５\n薄地に対してぬい目があらすぎる。\n\n＊対処\nぬい目を細かくします。';

  @override
  String get t_trouble25_06 => '＊原因６\n糸調子が合っていない。\n\n＊対処\n糸調子を調節します。';

  @override
  String get t_trouble25_07 => '＊原因７\n模様に合った押えを使用していない。\n\n＊対処\n指定の押えを取り付けます。';

  @override
  String get t_trouble26 => '［ぬい音が高い］';

  @override
  String get t_trouble26_01 => '＊原因１\n送り歯にゴミがたまっている。\n\n＊対処\nゴミを取り除きます。';

  @override
  String get t_trouble26_02 => '＊原因２\nかまの部分に糸くずが巻きこまれている。\n\n＊対処\nかまの掃除をします。';

  @override
  String get t_trouble26_03 => '＊原因３\n上糸の通し方がまちがっている。\n\n＊対処\n糸を通す順序を調べて通し直します。';

  @override
  String get t_trouble26_04 => '＊原因４\n内かまにキズがある。\n\n＊対処\n内かまを交換するか、\nお買い上げの販売店にご相談ください。';

  @override
  String get t_trouble27 => '［自動糸通しができない]';

  @override
  String get t_trouble27_01 => '＊原因１\n針が正しい位置にない。\n\n＊対処\n針上下スイッチで針を上げます。';

  @override
  String get t_trouble27_02 => '＊原因２\n自動糸通し装置のフックが針穴に入らない。\n\n＊対処\n針上下スイッチで針を上げます。';

  @override
  String get t_trouble27_03 => '＊原因３\n針の取り付け方がまちがっている。\n\n＊対処\n針を正しく取り付けます。';

  @override
  String get t_trouble28 => '［糸調子が合わない］';

  @override
  String get t_trouble28_01 => '＊原因１\n上糸の通し方がまちがっている。\n\n＊対処\n糸を通す順序を調べて通し直します。';

  @override
  String get t_trouble28_02 => '＊原因２\n下糸セットのしかたがまちがっている。\n\n＊対処\n下糸を正しくセットします。';

  @override
  String get t_trouble28_03 => '＊原因３\n布地に合った糸・針を使っていない。\n\n＊対処\n布地と糸と針の組み合わせ表を見て、\n適正な組み合わせにします。';

  @override
  String get t_trouble28_04 => '＊原因４\n押えホルダーが正しく取りつけられていない。\n\n＊対処\n押えホルダーを正しく取りつけます。';

  @override
  String get t_trouble28_05 => '＊原因５\n糸調子が合っていない。\n\n＊対処\n糸調子を調節します。';

  @override
  String get t_trouble29 => '［文字模様がくずれる］';

  @override
  String get t_trouble29_01 => '＊原因１\n模様に合った押えを使用していない。\n\n＊対処\n指定の押えを取り付けます。';

  @override
  String get t_trouble29_02 => '＊原因２\n模様が正しく調整されていない。\n\n＊対処\n模様の調整をし直します。';

  @override
  String get t_trouble29_03 => '＊原因３\n薄い布地や伸びる布地なのに芯を貼っていない。\n\n＊対処\n布地に接着芯を貼ります。';

  @override
  String get t_trouble29_04 => '＊原因４\n糸調子が合っていない。\n\n＊対処\n糸調子を調節します。';

  @override
  String get t_trouble30 => '［刺しゅう模様がくずれる]';

  @override
  String get t_trouble30_01 => '＊原因１\n糸がからまっている。\n\n＊対処\nかまなどにからんだ糸をはさみなどで\n切って取り除きます。';

  @override
  String get t_trouble30_02 => '＊原因２\n刺しゅう枠に布地がきちんと張られていない。\n(布地の張り方がゆるいなど)\n\n＊対処\n布地の張り方がゆるいと、\n模様くずれやぬい縮みの原因となります。\n刺しゅう枠に布地を正しく張ります。';

  @override
  String get t_trouble30_03 => '＊原因３\n接着芯を貼っていない。\n\n＊対処\n特に伸びる布地、薄い布地、目の粗い布地、\nぬい縮みしやすい布地には、\n必ず刺しゅう用接着芯を貼ります。';

  @override
  String get t_trouble30_04 => '＊原因４\nキャリッジや刺しゅう枠が\n周辺に置いてある物に当たっている。\n\n＊対処\nキャリッジや刺しゅう枠が物にぶつかると\n模様くずれの原因となります。\n刺しゅう枠が動く範囲には物を置かないようにします。';

  @override
  String get t_trouble30_05 => '＊原因５\n刺しゅう枠からはみでた布地を\nアーム側に置いている。\n(刺しゅう枠がつかえて動かない)\n\n＊対処\n刺しゅう枠からはみ出た布地が\nアームの反対側になるようにして枠を張り替え、\n模様を１８０度回転して刺しゅうをします。';

  @override
  String get t_trouble30_06 => '＊原因６\n重い衣類に刺しゅうして、\n刺しゅう機の動きが悪くなった。\n\n＊対処\nアームベットと水平になるように\n電話帳などを置いたり、\n重い側を軽く持ち上げるようにしてぬいます。';

  @override
  String get t_trouble30_07 => '＊原因７\nテーブルから刺しゅうする布地が\n垂れ下がっている。\n\n＊対処\n布地が垂れ下がった状態で刺しゅうをすると、\n刺しゅう機の動きが悪くなります。\nテーブルから布地が垂れ下がらないようにします。';

  @override
  String get t_trouble30_08 => '＊原因８\n布地がひっかかっている、\nまたははさみ込まれている。\n\n＊対処\nミシンを止めて布地を正しい位置に直します。';

  @override
  String get t_trouble30_09 => '＊原因９\n下糸交換などでぬっている途中に\n刺しゅう枠を外したり、\nセットするときに枠を刺しゅう押えにあてたり、\n押したりした。\nまたは刺しゅう機を動かした。\n\n＊対処\nぬっている途中で刺しゅう押えに物がぶつかったり、\n刺しゅう機が動いたりすると\n模様くずれの原因となります。\nぬっている途中で刺しゅう枠を外したり、\nセットしたりするときには注意してください。';

  @override
  String get t_trouble31 => '［針が折れる］';

  @override
  String get t_trouble31_01 => '＊原因１\n針の取り付け方がまちがっている。\n\n＊対処\n針を正しく取り付けます。';

  @override
  String get t_trouble31_02 => '＊原因２\n針の止めネジがゆるんでいる。\n\n＊対処\n止めネジをしっかりしめます。';

  @override
  String get t_trouble31_03 => '＊原因３\n針が曲がっていたり、針先がつぶれている。\n\n＊対処\n新しい針に交換します。';

  @override
  String get t_trouble31_04 => '＊原因４\n布地に合った糸・針を使っていない。\n\n＊対処\n布地と糸と針の組み合わせ表を見て、\n適正な組み合わせにします。';

  @override
  String get t_trouble31_05 => '＊原因５\n模様に合った押えを使用していない。\n\n＊対処\n指定の押えに交換します。';

  @override
  String get t_trouble31_06 => '＊原因６\n上糸調子が特に強すぎる。\n\n＊対処\n糸調子を調節します。';

  @override
  String get t_trouble31_07 => '＊原因７\n布地を不当に引っ張っている。\n\n＊対処\n布地を引っ張らないようにします。';

  @override
  String get t_trouble31_08 => '＊原因８\n糸こまが正しくついていない。\n\n＊対処\n糸こまのつけ方を調べて付け直します。';

  @override
  String get t_trouble31_09 => '＊原因９\n針板の穴の周辺にキズがある。\n\n＊対処\n針板を交換するか、\nお買い上げの販売店にご相談ください。';

  @override
  String get t_trouble31_10 => '＊原因１０\n押えの穴の周辺にキズがある。\n\n＊対処\n押えを交換するか、\nお買い上げの販売店にご相談ください。';

  @override
  String get t_trouble31_11 => '＊原因１１\n内かまにキズがある。\n\n＊対処\n内かまを交換するか、\nお買い上げの販売店にご相談ください。';

  @override
  String get t_trouble31_12 => '＊原因１２\n品質の悪い針を使っている。\n\n＊対処\n正しい針に交換します。';

  @override
  String get t_trouble32 => '［布地を送らない］';

  @override
  String get t_trouble32_01 => '＊原因１\n送り歯が下がっている。\n\n＊対処\nドロップレバーで送り歯を上げます。';

  @override
  String get t_trouble32_02 => '＊原因２\nぬい目が細かすぎる。\n\n＊対処\nぬい目の長さを長くします。';

  @override
  String get t_trouble32_03 => '＊原因３\n模様に合った押えを使用していない。\n\n＊対処\n指定の押えに交換します。';

  @override
  String get t_trouble32_04 => '＊原因４\n針が曲がっていたり、針先がつぶれている。\n\n＊対処\n新しい針に交換します。';

  @override
  String get t_trouble32_05 => '＊原因５\n糸がからまっている。\n\n＊対処\nかまなどにからんだ糸をはさみなどで\n切って取り除きます。';

  @override
  String get t_trouble33 => '［ミシンが動かない］';

  @override
  String get t_trouble33_01 => '＊原因１\n模様が選ばれていない。\n\n＊対処\n模様を選びます。';

  @override
  String get t_trouble33_02 => '＊原因２\nスタート／ストップスイッチを押していない。\n\n＊対処\nスタート／ストップスイッチを押します。';

  @override
  String get t_trouble33_03 => '＊原因３\n電源スイッチが入っていない。\n\n＊対処\n電源スイッチを入れます。';

  @override
  String get t_trouble33_04 => '＊原因４\n押えが下がっていない。\n\n＊対処\n押えを下げます。';

  @override
  String get t_trouble33_05 => '＊原因５\nフットコントローラーを接続したまま\nスタート／ストップスイッチを押している。\n\n＊対処\nフットコントローラーを外すか、\nフットコントローラーで操作します。';

  @override
  String get t_trouble33_06 => '＊原因６\nスピードコントロールレバーで\n振り幅の調節をしている。\n\n＊対処\nスタート／ストップスイッチではなく\nフットコントローラーで操作します。';

  @override
  String get t_trouble34 => '［刺しゅう機が動かない］';

  @override
  String get t_trouble34_01 => '＊原因１\n模様が選ばれていない。\n\n＊対処\n模様を選びます。';

  @override
  String get t_trouble34_02 => '＊原因２\n電源スイッチが入っていない。\n\n＊対処\n電源スイッチを入れます。';

  @override
  String get t_trouble34_03 => '＊原因３\n刺しゅう機が正しくセットされていない。\n\n＊対処\n刺しゅう機を正しくセットします。';

  @override
  String get t_trouble34_04 => '＊原因４\n刺しゅう枠をつけた状態で初期設定を行った。\n\n＊対処\n正しく初期設定を行います。';

  @override
  String get t_trouble35 => ' ［糸が布の裏側でからまっている]';

  @override
  String get t_trouble35_01 => ' ＊原因１\n液晶のコントラストが合っていない。\n\n＊対処\n画面の調整をします。';

  @override
  String get t_maintenance36 => '［かまの掃除］';

  @override
  String get t_maintenance36_00 => '手順は以下の説明を読んだ後、操作してください。\n\nほこりがたまると縫製不良になったり、\n下糸検出が効かない場合があります。\nいつもきれいにしておきましょう。';

  @override
  String get t_maintenance36_01 => '\n１．針上下スイッチで針を上げます。';

  @override
  String get t_maintenance36_02 => '２．押えを下げます。';

  @override
  String get t_maintenance36_03 => '(ａ)ＯＦＦ\n(ｂ)ＯＮ\n\n３．電源スイッチを切ります。';

  @override
  String get t_maintenance36_04 => '４．針と押えホルダーを外します。';

  @override
  String get t_maintenance36_05_11 => '５．補助テーブルまたは刺しゅう機がついている場合は取り外してください。\n\n針板レバーを手前にスライドさせると、針板が開きます。';

  @override
  String get t_maintenance36_05_12 => '(ａ)手前にスライドさせる。';

  @override
  String get t_maintenance36_05_13 => '６．右手で針板を引き出し、取り外します。';

  @override
  String get t_maintenance36_05_14 => '(ａ)針板カバー';

  @override
  String get t_maintenance36_05_15 => '７．内かまを持ち、取り出します。';

  @override
  String get t_maintenance36_07_02 => '(ａ)内かま';

  @override
  String get t_maintenance36_08 => '\n８．付属のブラシや掃除機で、\n外かまとその周辺の糸くずを取り除いてください。\n\n＊外かまには油をささないで下さい。';

  @override
  String get t_maintenance36_08_02 => '(ａ)ブラシ\n(ｂ)外かま';

  @override
  String get t_embbasic18_04_21 => '\n９．内かまの▲印がミシンの●印に合うように、内かまを戻します。';

  @override
  String get t_embbasic18_04_22 => '(ａ)内かまの▲印(ｂ)ミシンの●印';

  @override
  String get t_embbasic18_04_23 => '\n１０．針板のツメをミシンの切り欠きに、はめます。';

  @override
  String get t_embbasic18_04_24 => '(ａ)ツメ\n(ｂ)切り欠き';

  @override
  String get t_embbasic18_04_25 => '１１．針板の右側を押して、しっかりはめ込んでください。';

  @override
  String get t_sewing01_00 => 'ぬい方選択';

  @override
  String get t_sewing01_00_01 => '１－０１：通常の時\n１－０５：ぬい目を丈夫にしたい時\n１－０６：伸びる布地をぬう時';

  @override
  String get t_sewing01_00_01_s_normal => '通常の時';

  @override
  String get t_sewing01_00_01_s_reinforced => 'ぬい目を丈夫にしたい時';

  @override
  String get t_sewing01_00_01_s_stretch => '伸びる布地をぬう時';

  @override
  String get t_sewing01 => '［直線ぬい］';

  @override
  String get t_sewing01_01 => '\n１．＜Ｊ＞押えを取り付けます。\n左手で糸と布地を押さえて、\n右手でプーリーを手前に回し、\nぬい始めの位置に針をさします。';

  @override
  String get t_sewing01_01_02 => '(ａ)ぬい始めの位置';

  @override
  String get t_sewing01_02 => '\n２．押えを下げます。\n返しぬいスイッチを押しながら\n返しぬい(止めぬい)をした後、\nスタート／ストップスイッチを押します。\nゆっくりぬい始めます。';

  @override
  String get t_sewing01_03 => '(ａ)返しぬい';

  @override
  String get t_sewing01_04 => '３．ぬい終わりは\n返しぬいスイッチを押しながら、\n返しぬい(止めぬい)をします。';

  @override
  String get t_sewing01_05 => '４．縫製後、糸切りスイッチを押して、糸を切ります。\n\n＊画面上で自動糸切りと自動返しぬいキーが選択されている場合、\nぬい始めにスタート／ストップスイッチが押されたとき、自動的に返しぬい(止めぬい)されます。\nぬい終わりに返しぬいスイッチを押すと、\n返しぬい(止めぬい)し、自動的に糸切りします。';

  @override
  String get t_sewing01_06 => '\n［針位置の変え方］\n左基線と中基線の直線ぬいは、L/Rシフトの＋－を押すと、\n針位置を左右に移動させることができます。\n押えの右端から針までの距離を\nぬいしろ幅に合わせておくと、\nぬうときに押えの右端に布端を合わせて\nぬえばよいので便利です。';

  @override
  String get t_sewing01_07 => '(ａ)ぬいしろ幅';

  @override
  String get t_sewing02 => '［たちめかがり］';

  @override
  String get t_sewing02_00 => 'ぬい方選択';

  @override
  String get t_sewing02_00_01 => '１－１６：薄地、普通地\n１－１７：厚地\n１－１８：普通地、厚地とほつれやすい布地\n１－１９：伸びる布地\n１－２０：厚地と普通地の伸びる布地\n１－２１：伸びる布地';

  @override
  String get t_sewing02_00_01_f_lightandmedium => '薄地、普通地';

  @override
  String get t_sewing02_00_01_f_heavy => '厚地';

  @override
  String get t_sewing02_00_01_f_mediumstretch => '普通地、厚地とほつれやすい布地';

  @override
  String get t_sewing02_00_01_f_stretch1 => '伸びる布地';

  @override
  String get t_sewing02_00_01_f_thickandmediumstretch => '厚地と普通地の伸びる布地';

  @override
  String get t_sewing02_00_01_f_stretch2 => '伸びる布地';

  @override
  String get t_sewing02_01 => '１．＜Ｇ＞押えを取り付け、\n押えのガイドに布端を当てて押えを下げます。';

  @override
  String get t_sewing02_02 => '\n２．布端をガイドに当てながら、\nガイドにそってぬいます。';

  @override
  String get t_sewing02_02_02 => '(ａ)ガイド';

  @override
  String get t_sewing02_03 => '\n１．＜Ｊ＞押えを取り付け、\n針を布端より\nわずかに外側に落ちるようにぬいます。';

  @override
  String get t_sewing02_04 => '(ａ)針落ち位置';

  @override
  String get t_sewing02_05 => '\n振り幅を調節したら、\nぬう前にプーリーを前向きに回して\n針が押えに当たらないか確認してください。\n針が押えに触れた場合、\n針が折れてけがをする恐れがあります。';

  @override
  String get t_sewing02_05_02 => '(ａ)針に触れないこと';

  @override
  String get t_sewing04 => '［スカラップ］';

  @override
  String get t_sewing04_01 => 'スカラップは、半月状の丸いカーブの\n連続した波型の模様をいいます。\nブラウスの衿やハンカチなどのふち飾りに使ったり、\n裾のアクセントに使います。\n軽い布には接着スプレーが必要です。\n実際にぬう前にためしぬいをしてください。';

  @override
  String get t_sewing04_02 => '１．＜Ｎ+＞押えを取り付けます。\n模様が布端にかからないように、\n端を残してぬってください。';

  @override
  String get t_sewing04_03 => '２．ぬい目にそって布地を切ります。\n糸を切らないように注意してください。';

  @override
  String get t_sewing05_00 => 'ぬい方選択';

  @override
  String get t_sewing05_00_01 => '４－０１：薄地、普通地(横穴用)\n４－０７：薄地、普通地\n４－１０：目のあらい伸びる布地\n４－１１：伸びる布地\n４－１３：スーツ、オーバー\n４－１４：ジーンズ、ズボン\n４－１５：厚手のコート';

  @override
  String get t_sewing05_00_01_f_lighttomediumhorizhole => '薄地、普通地(横穴用)';

  @override
  String get t_sewing05_00_01_f_lighttomedium => '薄地、普通地';

  @override
  String get t_sewing05_00_01_f_stretchweaves => '目のあらい伸びる布地';

  @override
  String get t_sewing05_00_01_f_stretch => '伸びる布地';

  @override
  String get t_sewing05_00_01_f_suitsandovercoat => 'スーツ、オーバー';

  @override
  String get t_sewing05_00_01_f_jeansandtrousers => 'ジーンズ、ズボン';

  @override
  String get t_sewing05_00_01_f_thickcoats => '厚手のコート';

  @override
  String get t_sewing05 => '［ボタン穴かがり］';

  @override
  String get t_sewing05_02 => '１．ボタン穴かがりをしたい位置にしるしをつけます。';

  @override
  String get t_sewing05_03 => '\n２．＜Ａ＋＞押えを取り付けます。\n押えの台皿を引き出し、ボタンホールに通すボタンを\n乗せてはさんでから、台皿をしめます。\n\n＊ボタンホールのサイズは、\nボタンの台皿のサイズによって決まります。';

  @override
  String get t_sewing05_04 => '(ａ)台皿';

  @override
  String get t_sewing05_05 => '\n３．指で押えを押しながら、\n布地のしるしに押えのしるしを合わせて、\n押えを下げます。';

  @override
  String get t_sewing05_06 => '(ａ)布地のしるし\n(ｂ)押えのしるし';

  @override
  String get t_sewing05_07 => '\n４．ボタン穴かがりレバーを下げます。';

  @override
  String get t_sewing05_08 => '(ａ)金具';

  @override
  String get t_sewing05_09 => '４．上糸を軽く持ってぬい始めます。\nぬい終わると自動的に止めぬいをして止まります。';

  @override
  String get t_sewing05_10 => '\n５．かんどめの内側にまち針をさして、\nボタン穴を切り開きます。\nリッパーでぬい糸を切らないように注意してください。';

  @override
  String get t_sewing05_11 => '(ａ)まち針\n(ｂ)リッパー';

  @override
  String get t_sewing05_12 => '［鍵穴ボタンホールの場合］\nボタンホールの丸い穴を作る時は\nハトメ穴パンチを使用します。\nかんどめの内側にまち針をさして、\nハトメ穴パンチで作った穴から\nリッパーでまち針の方に切り開いてください。';

  @override
  String get t_sewing05_13 => '(ａ)ハトメ穴パンチ\n(ｂ)まち針';

  @override
  String get t_sewing05_14 => '［伸びる布地をぬうときは］\n\n伸びる布地にボタン穴かがりをするときは、\n４－１０または４－１１を選び、\nボタン穴かがりに芯ひもを入れてください。';

  @override
  String get t_sewing05_16 => '\n１．＜Ａ＋＞押えの後部のつのに芯ひもをひっかけて、\n押えの下を平行に手前に張ります。\n芯ひもの端は押えの前部のみぞにはさんで、\n仮結びします。';

  @override
  String get t_sewing05_17 => '(ａ)上糸';

  @override
  String get t_sewing05_18 => '２．押えを下げてぬい始めます。';

  @override
  String get t_sewing05_19 => '３．ぬい上がったら芯ひもを引いてたるみをなくし、\n針穴の大きい手ぬい針を使って\n布地の裏側に引き出します。\n芯ひもを結び、余分な部分を切ります。';

  @override
  String get t_sewing05_20 => '\n［ボタンが台皿にのらないときは］\n押えスケールの目盛りを使って、\nボタン穴かがりの大きさを設定します。\n押えスケールの目盛りは１目盛り０．５ｃｍです。\n押えスケールの目盛りをボタンの直径＋厚みの\n寸法に合わせてください。';

  @override
  String get t_sewing05_21 => '(ａ)押えスケール\n(ｂ)台皿\n(ｃ)でき上がり寸法(直径＋厚み)\n(ｄ)０．５ｃｍ';

  @override
  String get t_sewing05_22 => '\n例：直径１．５ｃｍ厚み１ｃｍのボタンの場合、\nスケールを２．５ｃｍに合わせます。';

  @override
  String get t_sewing05_23 => '(ａ)厚み１ｃｍ\n(ｂ)直径１．５ｃｍ';

  @override
  String get t_sewing06 => '［ボタンつけ］';

  @override
  String get t_sewing06_01 => 'ボタン付けをするときは、\n自動糸切機能を使用しないでください。\n\n１．＜Ｍ＞押えを取り付け、ボタンを押えにはさみ、\n押えを下げます。';

  @override
  String get t_sewing06_01_02 => '(ａ)ボタン\n(ｂ)プレート';

  @override
  String get t_sewing06_02 => '２．プーリーを手前に回して、\n針がボタンに当たらないで\nボタン穴に交互に入るか確かめてから、\nぬい始めてください。\nぬい終わったら自動で停止します。';

  @override
  String get t_sewing06_03 => '３．ぬい始めの上糸と下糸をはさみで切り取り、\nぬい終わりの上糸を布地の裏に引き出して、\n２本を結びます。';

  @override
  String get t_sewing06_04 => '［４つ穴ボタンをつけるときは］';

  @override
  String get t_sewing06_05 => '手前の２つの穴からぬっていきます。\nぬい終わったら押えを上げ、\n後ろ側のボタン穴に針を合わせてぬってください。';

  @override
  String get t_sewing06_06 => '[ボタンに足を取り付ける]\n\n１．足レバーを手前に押します。\n';

  @override
  String get t_sewing06_07 => '(ａ)足レバー';

  @override
  String get t_sewing06_08 => '２．ぬい終わったら、長めに切った上糸の両端を\nボタンと布地の間に引き出します。\n上糸をボタン足に巻きつけて結びます。';

  @override
  String get t_sewing07 => '［かんどめ］';

  @override
  String get t_sewing07_01 => 'かんどめは止めぬいの一種で\nポケット口やスリットのあき止まりなど、\n力の加わる部分を補強するときに使います。';

  @override
  String get t_sewing07_02 => '\n１．＜Ａ＞押えの目盛りでぬう長さを設定して、\n押えを取り付けます。';

  @override
  String get t_sewing07_03 => '(ａ)押えスケール\n(ｂ)でき上がり寸法\n(ｃ)０．５ｃｍ';

  @override
  String get t_sewing07_04 => '２．ポケット口が手前にくるように布地を置きます。';

  @override
  String get t_sewing07_05 => '\n３．指で押えを押しながら、\n最初の針落ち位置を確認して押えを下げます。';

  @override
  String get t_sewing07_06 => '(ａ)０．２ｃｍ';

  @override
  String get t_sewing07_09 => '４．上糸を軽く持ってぬい始めます。\nぬい終わると自動的に止めぬいをして止まります。';

  @override
  String get t_sewing07_10 => '\n［厚地の場合］\n押えが水平になるように、\n厚地または厚紙を置いてぬってください。';

  @override
  String get t_sewing07_11 => '(ａ)押え\n(ｂ)厚紙\n(ｃ)布';

  @override
  String get t_sewing08 => '［ファスナーつけ］';

  @override
  String get t_sewing08_00 => '\n１．＜Ｊ＞押えを取り付けます。\nあき止まりまでは直線でぬい、\nあきの部分はしつけをします。';

  @override
  String get t_sewing08_02 => '(ａ)しつけ\n(ｂ)返しぬい\n(ｃ)あき止まり\n(ｄ)裏';

  @override
  String get t_sewing08_03 => '\n２．ぬいしろを割り、ぬい目にファスナーの中心を\n合わせてしつけをします。';

  @override
  String get t_sewing08_04 => '(ａ)しつけ\n(ｂ)ムシ\n(ｃ)裏';

  @override
  String get t_sewing08_05 => '\n３．＜Ｊ＞押えを取り外し、\n＜Ｉ＞押えの右側のピンを押えホルダーに\n合わせて取り付けます。';

  @override
  String get t_sewing08_06 => '(ａ)右\n(ｂ)左\n(ｃ)針落ち位置';

  @override
  String get t_sewing08_07 => '４．表面を上にしてぬいます。\nぬい終わったら、しつけを取ります。';

  @override
  String get t_sewing08_08 => '\n［脇あき］\nスカートやワンピースの脇あきに使います。\n\n１．＜Ｊ＞押えを取り付けます。\n布地を中表に合わせてあき止まりまでは\n直線でぬい、あきの部分にはしつけをします。';

  @override
  String get t_sewing08_11 => '(ａ)返しぬい\n(ｂ)裏\n(ｃ)しつけ\n(ｄ)あき止まり';

  @override
  String get t_sewing08_12 => '\n２．ぬいしろを割り、後ろスカートのぬいしろを０．３ｃｍ\n出して折り、山をファスナーのムシのきわに合わせます。';

  @override
  String get t_sewing08_13 => '(ａ)スライダー\n(ｂ)裏\n(ｃ)ムシ\n(ｄ)あき止まり\n(ｅ)０．３ｃｍ';

  @override
  String get t_sewing08_14 => '\n３．＜Ｊ＞押えを取り外し、\n＜Ｉ＞押えの右側のピンを押えホルダーに合わせて\n取り付けます。';

  @override
  String get t_sewing08_15 => '(ａ)右\n(ｂ)左\n(ｃ)針落ち位置';

  @override
  String get t_sewing08_16 => '\n４．０．３ｃｍの幅の真ん中を\nあき止まりからぬってください。\n５ｃｍくらい手前までぬったらいったんミシンを止めて、\nスライダーを下げて端までぬいます。';

  @override
  String get t_sewing08_17 => '(ａ)５ｃｍ\n(ｂ)０．３ｃｍ';

  @override
  String get t_sewing08_18 => '\n５．ファスナーを閉じて表に返し、しつけをします。';

  @override
  String get t_sewing08_19 => '(ａ)前スカート(前)\n(ｂ)しつけ\n(ｃ)前スカート(前)\n(ｄ)前スカート(裏)';

  @override
  String get t_sewing08_20 => '\n６．押えの取り付け位置を\nピンの左側に付け替えます。';

  @override
  String get t_sewing08_21 => '(ａ)右\n(ｂ)左\n(ｃ)針落ち位置';

  @override
  String get t_sewing08_22 => '７．あき止まりまでは返しぬいをして、\n押えの左端をムシのきわに当てながらぬいます。\n５ｃｍくらい手前で針を落としたまましつけをほどき、\nスライダーを下げて残りの部分をぬいます。';

  @override
  String get t_sewing08_23 => '(ａ)しつけ\n(ｂ)０．７～１ｃｍ\n(ｃ)返しぬい\n(ｄ)５ｃｍ';

  @override
  String get t_sewing09_00 => 'ぬい方選択';

  @override
  String get t_sewing09_00_01 => 'ドレスやブラウス、ズボンやスカートの\n裾や袖口をぬうためにこれらのぬい模様を選択します。';

  @override
  String get t_sewing09_00_02 => '２－０１：普通の布地\n２－０２：伸縮する布地';

  @override
  String get t_sewing09_00_02_f_other => '普通の布地';

  @override
  String get t_sewing09_00_02_f_stretch => '伸縮する布地';

  @override
  String get t_sewing09 => '［まつりぬい］';

  @override
  String get t_sewing09_01 => '\n１．裏側を上にして布地を下のように折ってください。';

  @override
  String get t_sewing09_02 => '(ａ)０．５ｃｍ\n(ｂ)しつけ\n(ｃ)裏';

  @override
  String get t_sewing09_03 => '\n２．＜Ｒ＞押えを取り付け、\n押えのガイドに布地の折り山を当てて押えを下げます。';

  @override
  String get t_sewing09_04 => '(ａ)ガイド\n(ｂ)折り山';

  @override
  String get t_sewing09_05 => '\n３．布地の折り山をガイドに当てながら、ぬいます。';

  @override
  String get t_sewing09_06 => '(ａ)針位置';

  @override
  String get t_sewing09_07 => '\n４．しつけをほどいて、表に返します。';

  @override
  String get t_sewing09_08 => '(ａ)裏\n(ｂ)表';

  @override
  String get t_sewing10 => '［アップリケ］';

  @override
  String get t_sewing10_01 => '\n１．アップリケ布がずれない程度に\nのり付けするか、しつけをして固定します。';

  @override
  String get t_sewing10_02 => '(ａ)アップリケ布\n(ｂ)のり';

  @override
  String get t_sewing10_03 => '\n２．＜Ｊ＞押えを取り付け、針がアップリケ布の端より\nわずかに外側に落ちるようにぬいます。';

  @override
  String get t_sewing10_04 => '(ａ)アップリケ布\n(ｂ)針落ち位置';

  @override
  String get t_sewing10_06 => '［急な角度をぬうときは］';

  @override
  String get t_sewing10_07 => '\nアップリケ布の外側に針をさしたまま、\n押えを上げて少しずつ方向をかえながらぬうと、\nきれいなぬい目に仕上がります。';

  @override
  String get t_sewing11 => '［ピンタック］';

  @override
  String get t_sewing11_01 => '\n１．布に折り目のしるしをつけます。';

  @override
  String get t_sewing11_01_02 => '(ａ)裏';

  @override
  String get t_sewing11_02 => '\n２．布を折ってアイロンをかけます。';

  @override
  String get t_sewing11_02_02 => '(ａ)表';

  @override
  String get t_sewing11_03 => '３．＜Ｉ＞押えを取り付けます。\n折り目に沿ってぬいます。';

  @override
  String get t_sewing11_04_02 => '(ａ)タック幅\n(ｂ)裏\n(ｃ)表\n';

  @override
  String get t_sewing11_05 => '４．アイロンで同じ方向に倒します。';

  @override
  String get t_sewing12 => '［ギャザー］';

  @override
  String get t_sewing12_00 => 'スカートや服の袖などに使用します。';

  @override
  String get t_sewing12_01 => '１．上糸、下糸とも５０ｍｍくらい糸を引き出します。';

  @override
  String get t_sewing12_01_02 => '(ａ)上糸\n(ｂ)下糸\n(ｃ)５０ｍｍ\n';

  @override
  String get t_sewing12_02 => '\n２．平行に２本ぬいます。\n糸端は５０ｍｍくらい残してください。';

  @override
  String get t_sewing12_02_02 => '(ａ)ぬい目\n(ｂ)１０－１５ｍｍ\n(ｃ)５０ｍｍ';

  @override
  String get t_sewing12_03 => '３．下糸を引いてギャザーをよせ、糸端を結びます。';

  @override
  String get t_sewing12_04 => '４．アイロンで押えてギャザーを落ち着かせます。';

  @override
  String get t_sewing12_05 => '５．２本のぬい目の間をぬった後、\n糸を取り去ります。';

  @override
  String get t_sewing13 => '［ダーツ］';

  @override
  String get t_sewing13_01 => '\n１．ぬい始めに返しぬいをして、しつけの横をぬいます。\n布を伸ばさないようにぬって下さい。';

  @override
  String get t_sewing13_01_02 => '(ａ)しつけ\n(ｂ)表\n(ｃ)裏';

  @override
  String get t_sewing13_02 => '２．糸端を５０ｍｍ程度残して切り、糸端を結びます。\n\n＊糸切りスイッチは使用しないで下さい。';

  @override
  String get t_sewing13_03 => '３．針で糸端をダーツの中に入れます。';

  @override
  String get t_sewing13_04 => '４．アイロンで倒します。';

  @override
  String get t_sewing14 => '［折りふせぬい］';

  @override
  String get t_sewing14_00 => 'ぬいしろを補強し、\nしっかりと端を仕上げるために使います。\n\n１．できあがり線でぬい、\n折りふせぬいが倒れる側のぬいしろを\n半分にカットします。';

  @override
  String get t_sewing14_01_02 => '(ａ)裏\n(ｂ)１２ｍｍ';

  @override
  String get t_sewing14_02 => '\n２．ぬいしろを割ります。';

  @override
  String get t_sewing14_02_02 => '(ａ)裏\n(ｂ)できあがり線';

  @override
  String get t_sewing14_03 => '\n３．ぬいしろを短い方のぬいしろ側に倒し、\nアイロンをかけます。';

  @override
  String get t_sewing14_03_02 => '(ａ)裏';

  @override
  String get t_sewing14_04 => '\n４．長いぬいしろで短いぬいしろをくるみ、\n端をぬいます。';

  @override
  String get t_sewing14_04_01 => '(ａ)裏';

  @override
  String get t_sewing15_00 => 'ぬい方選択';

  @override
  String get t_sewing15_00_01 => 'Ｑ－０１：ピーシング直線(中基線)\nＱ－０２：ピーシング直線(右基線)\nＱ－０３：ピーシング直線(左基線)';

  @override
  String get t_sewing15_00_01_s_piecingmiddle => 'ピーシング直線(中基線)';

  @override
  String get t_sewing15_00_01_s_piecingright => 'ピーシング直線(右基線)';

  @override
  String get t_sewing15_00_01_s_piecingleft => 'ピーシング直線(左基線)';

  @override
  String get t_sewing15 => '［ピーシング］';

  @override
  String get t_sewing15_01 => '(ａ)ぬいしろ：６．５ｍｍ\n      (Ｑ－０２を選んだとき)\n(ｂ)押えの右端に合わせる\n\n１．＜Ｊ＞押えを取り付けます。\n押えの端に布端を合わせてぬいます。';

  @override
  String get t_sewing15_012 => '(ａ)ぬいしろ：７ｍｍ\n      (Ｑ－０２を選んだとき)\n(ｂ)押えの右端に合わせる\n\n１．＜Ｊ＞押えを取り付けます。\n押えの端に布端を合わせてぬいます。';

  @override
  String get t_sewing15_01_02 => '(ａ)ぬいしろ：６．５ｍｍ\n      (Ｑ－０３を選んだとき)\n(ｂ)押えの左端に合わせる\n\n１．＜Ｊ＞押えを取り付けます。\n押えの端に布端を合わせてぬいます。';

  @override
  String get t_sewing15_01_022 => '(ａ)ぬいしろ：７ｍｍ\n      (Ｑ－０３を選んだとき)\n(ｂ)押えの左端に合わせる\n\n１．＜Ｊ＞押えを取り付けます。\n押えの端に布端を合わせてぬいます。';

  @override
  String get t_sewing15_02 => '(ａ)ガイド\n\nガイド付きピーシング押えを使うと、\n正確に１／４インチ(６．４ｍｍ)または\n１／８インチ(３．２ｍｍ)のぬいしろで\nぬうことができます。\nキルトのピーシングやつき合わせに使います。\n\n１．Ｑ－０１を選び、\nガイド付きピーシング押えを取り付けます。';

  @override
  String get t_sewing15_03 => '(ａ)ガイド\n(ｂ)１／４インチ(６．４ｍｍ)\n\n２．押えのガイドとしるしを使って、\n一定幅のぬいしろでぬいます。\n\n「ぬいしろを１／４インチ(６．４ｍｍ)にするとき」\nガイドに布端を合わせてぬいます。';

  @override
  String get t_sewing15_04 => '(ａ)このしるしを布端に合わせてぬい始める\n(ｂ)ぬい始め\n(ｃ)ぬい終わり\n(ｄ)反対側の布端でぬい終わる、\nまたはぬい方向を変える\n(ｅ)１／４インチ(６．４ｍｍ)\n\n「一定幅のぬいしろを残してぬうとき」\nぬい始めとぬい終わり、\nまたはぬい方向を変えるときに、\n布端から１／４インチ(６．４ｍｍ)の幅になるように\n押えのしるしを使います。';

  @override
  String get t_sewing15_05 => '(ａ)布地の表\n(ｂ)ぬい目の位置\n(ｃ)１／８インチ(３．２ｍｍ)\n\n「キルトステッチに使うとき(１／８インチ(３．２ｍｍ)幅)」\n押えの先端の左端を目安にしてぬいます。';

  @override
  String get t_sewing16 => '［キルト］';

  @override
  String get t_sewing16_01 => '１．押えを取り外し、押えホルダーのネジをゆるめて\n押えホルダーを取り外します。\n\n押え棒の平らな面とアダプターの平らな面を合わせて差し込みます。アダプターが止まるまで差し込んで、ネジ回しでネジを締めます。';

  @override
  String get t_sewing16_02 => '(ａ)レバー\n(ｂ)針の止めネジ\n(ｃ)ふたまた部\n(ｄ)押え棒\n\n２．針の止めネジがウォーキングフットのレバーの\nふたまた部にはさまるようにして、\nウォーキングフットを押え棒に合わせます。';

  @override
  String get t_sewing16_03 => '３．押えレバーを下げます。\nドライバーでネジを確実にしめます。';

  @override
  String get t_sewing16_04 => '４．押えの両側に手を置き、\n布地をしっかりと張ってぬいます。';

  @override
  String get t_sewing16_05 => '＊設定画面で「自動押え圧補正機能」をＯＮに設定すると、\nスムーズに布地を送ることができ、\nきれいに仕上がります。';

  @override
  String get t_sewing17 => '［フリーモーションキルト］';

  @override
  String get t_sewing17_00 => '(ａ)フリーモーションキルト押え＜Ｃ＞\n(ｂ)フリーモーションキルト押え＜Ｏ＞\n\nフリーモーションキルトでは、\n選択する模様に応じて、\nフリーモーションキルト押え＜Ｃ＞\nまたはフリーモーションキルト押え＜Ｏ＞を使います。';

  @override
  String get t_sewing17_01 => '１．送り歯ドロップキーを押して、\nフリーモーションモードに設定します。';

  @override
  String get t_sewing17_02_01 => '(ａ)押えホルダーのネジ\n(ｂ)切り込み\n\n２．押えホルダーのネジを、\n手前からフリーモーションキルト押え＜Ｃ＞の\n切り込みに合わせます。\n押えホルダーのネジをしめます。';

  @override
  String get t_sewing17_02_02 => '(ａ)ピン\n(ｂ)針のとめネジ\n(ｃ)押え棒\n\nフリーモーションキルト押え＜Ｏ＞のピンが\n針の止めネジの上にくるようにして、\n押えと押え棒の左下を合わせます。\n押えホルダーのネジをしめます。';

  @override
  String get t_sewing17_03 => '(ａ)模様\n\n３．両手で布地をしっかりと張り、\nぬい目の長さが約２．０～２．５ｍｍで一定になるように、\n普通の速さで布地を動かします。\n\n＊フットコントローラーを取り付けて、\n一定の速さでぬうことをおすすめします。';

  @override
  String get t_sewing18 => '［エコーキルト］';

  @override
  String get t_sewing18_00 => '(ａ)６．４ｍｍ\n(ｂ)９．５ｍｍ\n\nフリーモーションエコーキルト押え＜Ｅ＞';

  @override
  String get t_sewing18_01 => '２．押えを取り外し、\n押えホルダーのネジをゆるめて、\nネジと押えホルダーを取り外します。\n\n押え棒の平らな面とアダプターの平らな面を合わせて差し込みます。アダプターが止まるまで差し込んで、ネジ回しでネジを締めます。';

  @override
  String get t_sewing18_02 => '３．フリーモーションエコーキルト押え＜Ｅ＞と\n押え棒の穴の位置がそろうように、\n押えを押え棒に左側から合わせます。\n\n付属のネジ(小)を２～３回手で回して、押え棒に仮止めします。';

  @override
  String get t_sewing18_03 => '４．ネジをしめます。';

  @override
  String get t_sewing18_04 => '(ａ)６．４ｍｍ\n\n５．押えの目盛りをガイドにして、\nモチーフの外側をぬっていきます。';

  @override
  String get t_sewing18_05 => '出来上がりのイメージ';

  @override
  String get t_sewing19 => '［アップリケ］';

  @override
  String get t_sewing19_01 => '(ａ)ぬいしろ(３～５ｍｍ)\n\n１．アップリケ布に模様を写し、\nぬいしろを付けて切り取ります。';

  @override
  String get t_sewing19_02 => '２．アップリケのデザインに合わせて切り取った厚紙\nまたは接着芯をアップリケ布の裏側にあてて、\nアイロンでぬいしろを内側に折ります。';

  @override
  String get t_sewing19_03 => '３．アップリケ布を表に返し、\n厚紙または接着芯を取ります。\nまち針またはしつけで、\nアップリケ布を土台布に固定します。';

  @override
  String get t_sewing19_04 => '(ａ)針落ち位置\n\n４．＜Ｊ＞押えを取り付けます。\n針が落ちる位置を確認し、\n針がアップリケ布の端より\nわずかに外側に落ちるようにまわりをぬいます。';

  @override
  String get t_explain_use => '［模様の用途］';

  @override
  String get t_explain01_01 => '地ぬい、ギャザー、ピンタックなど\n洋裁に広く使用します。\n返しぬいスイッチを押すと返しぬいをします。';

  @override
  String get t_explain01_01_01 => '\n［針位置の変え方］\n左基線と中基線の直線ぬいは、L/Rシフトの＋－を押すと、\n針位置を左右に移動させることができます。\n押えの右端から針までの距離を\nぬいしろ幅に合わせておくと、\nぬうときに押えの右端に布端を合わせて\nぬえばよいので便利です。';

  @override
  String get t_explain01_02 => '地ぬい、ギャザー、ピンタックなど\n洋裁に広く使用します。\n返しぬいスイッチを押すと止めぬいをします。';

  @override
  String get t_explain01_03 => 'ファスナーつけ、地ぬい、ギャザー、ピンタックなど\n洋裁に広く使用します。\n返しぬいスイッチを押すと返しぬいをします。';

  @override
  String get t_explain01_04 => '地ぬい、ギャザー、ピンタックなど\n洋裁に広く使用します。\n返しぬいスイッチを押すと止めぬいをします。';

  @override
  String get t_explain01_05 => '丈夫なぬい目なので、\n袖つけや股下をぬうのに便利です。\nまた、伸びる布地をぬうときや\n飾りぬいとしても使用します。';

  @override
  String get t_explain01_06 => '伸縮性のあるぬい目なので、\n伸びる布地をぬうときに使用します。\nまた、飾りぬいとしても使用します。';

  @override
  String get t_explain01_07 => '飾りぬいに使用します。';

  @override
  String get t_explain01_08 => '仮り止めに使用します。';

  @override
  String get t_explain01_09 => 'たちめかがりやアップリケなどに使用します。\n返しぬいスイッチを押すと返しぬいをします。\n振り幅を調節すると\n中心を基線にして振り幅が変わります。';

  @override
  String get t_explain01_10 => 'たちめかがりやアップリケなどに使用します。\n返しぬいスイッチを押すと止めぬいをします。\n振り幅を調節すると\n中心を基線にして振り幅が変わります。';

  @override
  String get t_explain01_11 => '右の針位置から始まります。\n振り幅を調節すると\n右を基線にして振り幅が変わります。';

  @override
  String get t_explain01_12 => '左の針位置から始まります。\n振り幅を調節すると\n左を基線にして振り幅が変わります。';

  @override
  String get t_explain01_13 => '普通地、伸びる布地のたちめかがりやゴムひも付け、\nつくろいぬいなど幅広い用途に使用します。';

  @override
  String get t_explain01_14 => '普通地、厚地、伸びる布地の\nたちめかがりやゴムひも付け、\nつくろいぬいなど幅広い用途に使用します。';

  @override
  String get t_explain01_14a => '普通地、厚地、伸びる布地の\nたちめかがりやゴムひも付け、\nつくろいぬいなど幅広い用途に使用します。';

  @override
  String get t_explain01_15 => '薄地、普通地のたちめかがりに使用します。';

  @override
  String get t_explain01_16 => '厚地のたちめかがりに使用します。';

  @override
  String get t_explain01_17 => '普通地や厚地、破れやすい布地のたち目かがりや飾りぬいに使用します。';

  @override
  String get t_explain01_18 => '伸びる布地のたちめかがりに使用します。';

  @override
  String get t_explain01_19 => '厚地、伸びる布地のたちめかがりに使用します。';

  @override
  String get t_explain01_20 => '伸びる布地のたちめかがりや\n飾りぬいに使用します。';

  @override
  String get t_explain01_21 => '伸びる布地のたちめかがりに使用します。';

  @override
  String get t_explain01_22 => '伸びる布地のたちめかがりに使用します。';

  @override
  String get t_explain01_23 => '伸びる布地のたちめかがりに使用します。';

  @override
  String get t_explain01_24 => 'サイドカッター押えを取り付けてぬいます。\n布地を切りながら、直線をぬいます。';

  @override
  String get t_explain01_25 => 'サイドカッター押えを取り付けてぬいます。\n布地を切りながら、ジグザグステッチをぬいます。';

  @override
  String get t_explain01_26 => 'サイドカッター押えを取り付けてぬいます。\n薄地、普通地のたちめかがりに使用します。';

  @override
  String get t_explain01_27 => 'サイドカッター押えを取り付けてぬいます。\n厚地のたちめかがりに使用します。';

  @override
  String get t_explain01_28 => 'サイドカッター押えを取り付けてぬいます。\n普通地、厚地のたちめかがりに使用します。';

  @override
  String get t_explain01_29 => 'ピーシング用の直線です。\n押えの右端に布地を合わせてぬうと、\nぬいしろの幅が６．５ｍｍでぬえるように\n針位置が設定されています。';

  @override
  String get t_explain01_292 => 'ピーシング用の直線です。\n押えの右端に布地を合わせてぬうと、\nぬいしろの幅が７ｍｍでぬえるように\n針位置が設定されています。';

  @override
  String get t_explain01_29a => 'ピーシング用の直線です。';

  @override
  String get t_explain01_30 => 'ピーシング用の直線です。\n押えの左端に布地を合わせてぬうと、\nぬいしろの幅が６．５ｍｍでぬえるように\n針位置が設定されています。';

  @override
  String get t_explain01_302 => 'ピーシング用の直線です。\n押えの左端に布地を合わせてぬうと、\nぬいしろの幅が７ｍｍでぬえるように\n針位置が設定されています。';

  @override
  String get t_explain01_31 => '手ぬい風のキルト直線がぬえます。\nぬうときは上糸の調子を強くしてぬいます。';

  @override
  String get t_explain01_32 => 'アップリケキルトやフリーモーションキルト、\nサテンぬいなどに使用します。';

  @override
  String get t_explain01_33 => 'アップリケやバインディングをするときに\n使用します。';

  @override
  String get t_explain01_34 => 'キルトで背景をうめるときに使用します。';

  @override
  String get t_explain02_01 => '普通地のまつりぬいに使用します。';

  @override
  String get t_explain02_02 => '伸びる布地のまつりぬいに使用します。';

  @override
  String get t_explain02_03 => 'アップリケに使用します。';

  @override
  String get t_explain02_04 => 'シェルタックは貝殻を直線状にならべたような\n円弧状のひだを作るもので、\nふち取りなどに用いるほか、\nブラウス、ワンピースなどの胸元や袖に\n飾りぬいとして使用します。\nぬう時は、糸調子を強めにして下さい。';

  @override
  String get t_explain02_05 => 'スカラップは半月状の丸いカーブの\n連続した波形の模様をいいます。\nブラウスやハンカチなどのふち飾りに使用します。';

  @override
  String get t_explain02_06 => 'スカラップは半月状の丸いカーブの\n連続した波形の模様をいいます。\nブラウスやハンカチなどのふち飾りに使用します。';

  @override
  String get t_explain02_07 => 'パッチワークや飾りぬいに使用します。';

  @override
  String get t_explain02_08 => 'パッチワークや飾りぬいに使用します。\nトリコット地などたち目かがり兼用の\n地ぬいにも使用できます。';

  @override
  String get t_explain02_09 => 'パッチワークや飾りぬいに使用します。';

  @override
  String get t_explain02_10 => 'スモッキングや飾りぬいに使用します。';

  @override
  String get t_explain02_11 => 'ファゴティングは布地と布地の間を離して\n糸でかがる方法をいい、\nブラウスや子供服などに使用します。\nまた、飾りぬいとしても使用します。';

  @override
  String get t_explain02_12 => 'ファゴティングは布地と布地の間を離して\n糸でかがる方法をいい、\nブラウスや子供服などに使用します。\nまた、飾りぬいとしても使用します。';

  @override
  String get t_explain02_13 => '伸びる布地にゴムひもをつけるときに使用します。';

  @override
  String get t_explain02_14 => '飾りぬいに使用します。';

  @override
  String get t_explain02_15 => '飾りぬいに使用します。';

  @override
  String get t_explain02_15a => '飾りぬいに使用します。';

  @override
  String get t_explain02_16 => '飾りぬいやゴムひも付けに使用します。';

  @override
  String get t_explain02_17 => '飾りぬいやゴムひも付けに使用します。';

  @override
  String get t_explain02_18 => '飾りぬいやアップリケに使用します。';

  @override
  String get t_explain02_19 => '飾りぬいに使用します。';

  @override
  String get t_explain03_01 => '左基線の３重ぬいで、すそ飾りなどに使用します。';

  @override
  String get t_explain03_02 => '中基線の３重ぬいで、すそ飾りなどに使用します。';

  @override
  String get t_explain03_03 => 'ふち飾りなどに使用します。';

  @override
  String get t_explain03_04 => 'レース付けやふち飾りなどに使用します。';

  @override
  String get t_explain03_05 => 'ふち飾りなどに使用します。';

  @override
  String get t_explain03_06 => 'ふち飾りなどに使用します。';

  @override
  String get t_explain03_07 => 'ふち飾りなどに使用します。\n薄地、普通地の平織りの布に適しています。';

  @override
  String get t_explain03_08 => 'ふち飾りなどに使用します。\n薄地、普通地の平織りの布に適しています。';

  @override
  String get t_explain03_09 => 'ふち飾りなどに使用します。\n薄地、普通地の平織りの布に適しています。';

  @override
  String get t_explain03_10 => 'ふち飾りなどに使用します。\n薄地、普通地の平織りの布に適しています。';

  @override
  String get t_explain03_11 => 'ふち飾りなどに使用します。\n薄地、普通地の平織りの布に適しています。';

  @override
  String get t_explain03_12 => 'ふち飾りなどに使用します。\n薄地、普通地の平織りの布に適しています。';

  @override
  String get t_explain03_13 => 'ふち飾りなどに使用します。\n薄地、普通地の平織りの布に適しています。';

  @override
  String get t_explain03_14 => 'ふち飾りなどに使用します。\n薄地、普通地の平織りの布に適しています。';

  @override
  String get t_explain03_15 => 'ふち飾りなどに使用します。\n薄地、普通地の平織りの布に適しています。';

  @override
  String get t_explain03_16 => 'ふち飾りなどに使用します。\n薄地、普通地の平織りの布に適しています。';

  @override
  String get t_explain03_17 => 'ふち飾りなどに使用します。\n薄地、普通地の平織りの布に適しています。';

  @override
  String get t_explain03_18 => 'ふち飾りなどに使用します。\n薄地、普通地の平織りの布に適しています。';

  @override
  String get t_explain03_19 => 'つき合わせや飾りぬいに使用します。';

  @override
  String get t_explain03_20 => 'ふち飾りやぬった後にリボンを通して飾りとして使用します。';

  @override
  String get t_explain03_21 => '飾りぬいやスモッキングに使用します。';

  @override
  String get t_explain03_22 => '飾りぬいやスモッキングに使用します。';

  @override
  String get t_explain03_23 => '飾りぬいやスモッキングに使用します。';

  @override
  String get t_explain03_24 => '飾りぬいやスモッキングに使用します。';

  @override
  String get t_explain03_25 => '飾りぬいに使用します。';

  @override
  String get t_explain04_01 => 'ねむり穴、横穴用。\n薄地から普通地のボタン穴かがりに使用します。\nブラウス、シャツなどに使用します。';

  @override
  String get t_explain04_02 => '横穴用。\n厚地の布や厚みのあるボタンを使うときに\n使用します。';

  @override
  String get t_explain04_03 => '横穴用。\nウエストベルトなど力がかかるところに\n使用します。';

  @override
  String get t_explain04_04 => '横穴用。\n厚地のボタン穴かがりに使用します。';

  @override
  String get t_explain04_05 => 'ねむり穴。';

  @override
  String get t_explain04_06 => 'ねむり穴。\n普通地から厚地のボタン穴かがりに使用します。\nジャケットやブラウスに使用します。';

  @override
  String get t_explain04_07 => '両止めボタン穴かがり。\n麻、木綿など、ある程度腰のある素材に\n適しています。\nホームウエアやブラウスなど手軽にぬえるものに\n多く利用されます。';

  @override
  String get t_explain04_08 => '両止めボタン穴かがり。\n厚地の布や厚みのあるボタンを使うときに\n使用します。';

  @override
  String get t_explain04_09 => '横穴用。\n厚地のボタン穴かがりに使用します。';

  @override
  String get t_explain04_10 => '伸びる布地や編み地のボタン穴かがりに\n使用します。\n芯ひもを入れてぬいます。';

  @override
  String get t_explain04_11 => '伸びる布地のボタン穴かがりに使用します。\n芯ひもを入れてぬいます。';

  @override
  String get t_explain04_12 => '玉縁穴かがり(バウンドボタンホール)を作るときに\n使用します。';

  @override
  String get t_explain04_13 => 'はとめ穴。厚地や毛の長いものの\nボタン穴かがりに使用します。\nブレザー、スーツ、コートなどに使用します。';

  @override
  String get t_explain04_14 => 'はとめ穴。\n普通地から厚地のボタン穴かがりに使用します。\nジーンズ、ズボンなどに使用します。';

  @override
  String get t_explain04_15 => 'はとめ穴。\n厚地や毛の長いものの横穴ボタン穴かがりに\n使用します。\n厚手のコートなどに使用します。';

  @override
  String get t_explain04_15a => '４ステップボタンホールの左側です。';

  @override
  String get t_explain04_15b => '４ステップボタンホールの下のかん部分です。';

  @override
  String get t_explain04_15c => '４ステップボタンホールの右側です。';

  @override
  String get t_explain04_15d => '４ステップボタンホールの上のかん部分です。';

  @override
  String get t_explain04_16 => '普通地のつくろいぬいに使用します。';

  @override
  String get t_explain04_17 => '厚地のつくろいぬいに使用します。';

  @override
  String get t_explain04_18 => 'ぬい目がほどけやすい箇所やポケット口などの\nあき止まり部分の力の加わる部分を\n補強したい場合に用います。';

  @override
  String get t_explain04_19 => 'ボタンつけに使用します。';

  @override
  String get t_explain04_20 => 'ベルトの穴かがりなどに使用します。';

  @override
  String get t_explain04_21 => 'ベルトの穴かがりなどに使用します。';

  @override
  String get t_explain05_01 => '出来上がった服の袖やズボンのすそに\nワッペンをつけるときに使用します。';

  @override
  String get t_explain05_02 => '出来上がった服の袖やズボンのすそに\nワッペンをつけるときに使用します。';

  @override
  String get t_explain05_03 => '出来上がった服の袖やズボンのすそに\nワッペンをつけるときに使用します。';

  @override
  String get t_explain05_04 => '出来上がった服の袖やズボンのすそに\nワッペンをつけるときに使用します。';

  @override
  String get t_explain05_05 => '出来上がった服の袖やズボンのすそに\nワッペンをつけるときに使用します。';

  @override
  String get t_explain05_06 => '出来上がった服の袖やズボンのすそに\nワッペンをつけるときに使用します。';

  @override
  String get t_explain05_07 => '出来上がった服の袖やズボンのすそに\nワッペンをつけるときに使用します。';

  @override
  String get t_explain05_08 => '出来上がった服の袖やズボンのすそに\nワッペンをつけるときに使用します。';

  @override
  String get t_explain05_09 => '出来上がった服の袖やズボンのすそに\nワッペンをつけるときに使用します。';

  @override
  String get t_explain05_10 => '出来上がった服の袖やズボンのすそに\nワッペンをつけるときに使用します。';

  @override
  String get t_explain05_11 => '出来上がった服の袖やズボンのすそに\nワッペンをつけるときに使用します。';

  @override
  String get t_explain05_12 => '出来上がった服の袖やズボンのすそに\nワッペンをつけるときに使用します。';

  @override
  String get t_explain06_01 => 'フリーモーションで、毛糸などをぬい付けて\n飾りぬいすることができます。';

  @override
  String get t_explain06_02 => 'フリーモーションしつけ送り歯が下がり、布を自由に動かしながら、しつけぬいをすることができます。';

  @override
  String get t_explain06_03a => 'この模様は、微小ステッチで構成されています。\n上糸にナイロン透明糸または、布に近い色の細糸を使うと、手ぬい風の縫い目になります。下糸を布と異なる色を使うと、縫い目が際立ちます。';

  @override
  String get t_explain07_01 => 'アップリケや飾りぬいに使用します。';

  @override
  String get t_explain07_02 => '縫い始めまたは縫い終わりで、模様の先端をとがらせることができます。';

  @override
  String get t_explaindeco00_01 => 'きれいにぬうために';

  @override
  String get t_explaindeco00_02 => '模様の調整';

  @override
  String get t_explaindeco01_00 => '［きれいにぬうために］';

  @override
  String get t_explaindeco01_01 => '伸びる布地、薄地、織りのあらい布地には、\n裏側に別売の接着芯を貼ってください。\n接着芯を貼りたくないときには、\nトレーシングペーパーなどの薄い紙を\n布地の下に敷いてぬってください。';

  @override
  String get t_explaindeco01_02 => '(ａ)布地\n(ｂ)接着芯\n(ｃ)薄い紙\n\n＊布の種類\n伸びる布地、薄地、織りのあらい布地は、\n裏面に別売の接着芯を貼ってください。\n接着芯を貼りたくないときは、\nトレーシングペーパーなどの薄い紙を\n布地の下に敷いてぬってください。\n\n＊糸の種類\n５０番から６０番\n\n＊針の種類\n薄地、普通地、伸びる布地：伸縮地専用針(金色)\n厚地：家庭用ミシン針９０／１４\n\n＊押え\nＮ+押えを使用してください。\n他の押えだと\n仕上がりが悪くなるおそれがあります。 ';

  @override
  String get t_explaindeco02_00 => '［模様の調整］';

  @override
  String get t_explaindeco02_01 => '布地の種類や、\n接着芯や縫製スピードなどによって、\n模様がくずれることがあります。\n模様がくずれた場合は模様の調整を行ってください。\n実際にぬう時と同じ条件で\n６－１２０模様をぬって、\n各々の場合にしたがって調整を行ってください。';

  @override
  String get t_explaindeco02_02 => '１．６－１２０を選択してください。\nＮ+押えを取り付け、模様をぬいます。';

  @override
  String get t_explaindeco02_03_00 => '\n２．ぬい上がりを比較してください。';

  @override
  String get t_explaindeco02_04_00 => '［１．模様がつまっている］';

  @override
  String get t_explaindeco02_04_01 => '＋を押してください。\nもう一度ぬってぬい上がりを比較します。\n正しいぬい上がりになるまで調節してください。';

  @override
  String get t_explaindeco02_05_00 => '［２．模様が伸びている］';

  @override
  String get t_explaindeco02_05_01 => '－を押してください。\nもう一度ぬってぬい上がりを比較します。\n正しいぬい上がりになるまで調節してください。';

  @override
  String get t_explaindeco02_06_00 => '［３．模様が左にずれている］';

  @override
  String get t_explaindeco02_06_01 => '＋を押してください。\nもう一度ぬってぬい上がりを比較します。\n正しいぬい上がりになるまで調節してください。';

  @override
  String get t_explaindeco02_07_00 => '［４．模様が右にずれている］';

  @override
  String get t_explaindeco02_07_01 => '－を押してください。\nもう一度ぬってぬい上がりを比較します。\n正しいぬい上がりになるまで調節してください。';

  @override
  String get t_terms_read => '下記の規約をよくお読みください。';

  @override
  String get t_terms_cert_read => '下記の規約をよくお読みください。';

  @override
  String get t_terms_cert_01_00 => 'アップグレードキットの認証';

  @override
  String get t_terms_cert_01_01 => 'アップグレードキットの認証に関する規約\n';

  @override
  String get t_terms_cert_01_02 => 'このソフトウェアのオプション機能(有償ライセンス、マニュアル、ドキュメント、およびこれらに関連するアップデートを含みますが、これに限られません。以下、総称して「本オプション機能」といいます)を有効化する際、特定のライセンスコード、製品番号、シリアル番号、その他の関連する情報(以下、「お客様データ」といいます)の提供を直接または間接的に求められる場合があります。\n';

  @override
  String get t_terms_cert_01_03 => 'お客様データの中に含まれる一部の情報は、ブラザー工業株式会社(以下、「ブラザー」といいます)またはその関係会社が提供する製品登録用ウェブサイトに登録されたお客様の情報と関連づけることができる場合があります。ただし、ブラザーはお客様データを本オプション機能を有効化する目的(以下、「本目的」といいます)のためにのみ利用するものとし、お客様データによりお客様個人を特定せず、かつ本目的以外に利用しません。お客様データは、ブラザー、またはマイクロソフトもしくはアマゾンなどのクラウドサービスプロバイダ―が管理するサーバーで保存される場合があります。これらのサーバーは、お客様の居住する国と比較して、個人情報保護のレベルが十分でない国に所在する可能性があります。但し、ブラザーは、お客様データを適切なセキュリティ対策を使用して不正な利用または開示を防止し、適用法令にしたがってお客様データを保護するものとします。';

  @override
  String get t_terms_nettool_read => '下記の規約をよくお読みください。';

  @override
  String get t_terms_nettool_01_00 => 'ネットワーク診断ツール';

  @override
  String get t_terms_nettool_01_01 => 'ネットワーク診断ツールに関する規約\n';

  @override
  String get t_terms_nettool_01_02 => 'ネットワーク接続に問題がある場合、このソフトウェア上のネットワーク診断ツールをご利用ください。ネットワーク診断ツールを利用する場合、お客様のミシンまたはクラフト製品(以下、「ブラザー製品」といいます)およびブラザー製品に接続されたデバイスの情報(IPアドレス、MACアドレス、プロキシ接続情報、サブネットマスク、ゲートウェイ、DNSサーバー、その他の接続情報等を含みますが、これに限られません。以下、「ネットワーク関連情報」)がスクリーン上に表示されます。';

  @override
  String get t_terms_nettool_01_03 => 'ブラザー製品のネットワーク接続に問題があり、テクニカルサポートを申し込まれる場合、ブラザー工業株式会社(以下、「ブラザー」)もしくはその関係会社、またはお客様がお住まいの国もしくは地域のディーラーもしくは販売会社に、ネットワーク関連情報を提供することを求められる場合があります。お客様が提供したネットワーク情報は、お客様にテクニカルサポートを提供するためにブラザーまたはその関係会社に共有される場合があります。この場合、お客様から収集したネットワーク関連情報は、適用法令に従い厳密に管理されます。ブラザーはお客様の事前同意なく、上記の目的以外のためにネットワーク関連情報を利用しません。';

  @override
  String get t_terms_cert_read_t => '下記の規約をよくお読みください。';

  @override
  String get t_terms_cert_01_01_t => 'アップグレードキットの認証';

  @override
  String get t_terms_cert_01_02_t => 'このソフトウェアのオプション機能(有償ライセンス、マニュアル、ドキュメント、およびこれらに関連するアップデートを含みますが、これに限られません。以下、総称して「本オプション機能」といいます)を有効化する際、特定のライセンスコード、製品番号、シリアル番号、その他の関連する情報(以下、「お客様データ」といいます)の提供を直接または間接的に求められる場合があります。\n';

  @override
  String get t_terms_cert_01_03_t => 'お客様データの中に含まれる一部の情報は、ベビーロックの商号を使用しているタコニーコーポレーション(以下、「弊社」といいます)が提供する製品登録用ウェブサイトに登録されたお客様の情報と関連づけることができる場合があります。ただし、弊社はお客様データを本オプション機能を有効化する目的(以下、「本目的」といいます)のためにのみ利用するものとし、お客様データによりお客様個人を特定せず、かつ本目的以外に利用しません。お客様データは、弊社、またはマイクロソフトもしくはAWSなどのクラウドサービスプロバイダ―が管理するサーバーで保存される場合があります。これらのサーバーは、お客様の居住する国と比較して、個人情報保護のレベルが十分でない国に所在する可能性があります。但し、弊社は、お客様データを適切なセキュリティ対策を使用して不正な利用または開示を防止し、適用法令にしたがってお客様データを保護するものとします。';

  @override
  String get t_terms_nettool_read_t => '下記の規約をよくお読みください。';

  @override
  String get t_terms_nettool_01_01_t => 'ネットワーク診断ツールに関する規約\n';

  @override
  String get t_terms_nettool_01_02_t => 'ネットワーク接続に問題がある場合、このソフトウェア上のネットワーク診断ツールをご利用ください。ネットワーク診断ツールを利用する場合、お客様のミシン製品(以下、「弊社製品」といいます)および弊社製品に接続されたデバイスの情報(IPアドレス、MACアドレス、プロキシ接続情報、サブネットマスク、ゲートウェイ、DNSサーバー、その他の接続情報等を含みますが、これに限られません。以下、「ネットワーク関連情報」)がスクリーン上に表示されます。';

  @override
  String get t_terms_nettool_01_03_t => '弊社製品のネットワーク接続に問題があり、テクニカルサポートを申し込まれる場合、ベビーロックの商号を使用しているタコニーコーポレーション(以下、「弊社」といいます)もしくは、お客様がお住まいの国もしくは地域の販売会社に、ネットワーク関連情報を提供することを求められる場合があります。お客様が提供したネットワーク情報は、お客様にテクニカルサポートを提供するために弊社に共有される場合があります。\nこの場合、お客様から収集したネットワーク関連情報は、適用法令に従い厳密に管理されます。弊社はお客様の事前同意なく、上記の目的以外のためにネットワーク関連情報を利用しません。';

  @override
  String get t_terms_mnmpinmac_01_b => 'OKボタンを押すと、ご使用のミシンと、ScanNCutや他のミシンとの対応関係を作るために、PINコード、MACアドレス、マシンナンバーがブラザーサーバーに送付されます。\nこの情報は、上記以外の用途では使用しません。';

  @override
  String get t_terms_snj_pair_01 => 'OKボタンを押すと、他のブラザー製品(サービス)との連携のために、マシンナンバー、マシン名、PINコード、MACアドレスがブラザーサーバーに送付されます。\nこの情報は、上記以外の用途では使用しません。';

  @override
  String get upg_01 => 'ＵＳＢメディアを接続してください。';

  @override
  String get upg_02 => 'ファイルが読み込めません。';

  @override
  String get upg_03 => '正しいファイルが見つかりません。';

  @override
  String get upg_04 => 'チェックサムエラー';

  @override
  String get upg_05 => '書き込みに失敗しました。';

  @override
  String get upg_06 => '書き込みアドレスが正しくありません。';

  @override
  String get upg_07 => 'ＰＣと通信中です。\nＵＳＢケーブルを抜かないで下さい。';

  @override
  String get upg_08 => 'アップデートファイルを書き込んでいます。\n電源を切らないで下さい。';

  @override
  String get update_08 => 'アップデートファイルを書き込んでいます。\n電源を切らないで下さい。';

  @override
  String get upg_09 => 'アップデートが完了しました。';

  @override
  String get update_09 => 'アップデートが完了しました。';

  @override
  String get upg_10 => 'アップデートファイルが保存された\nＵＳＢメディアを接続してから\n取り込みキーを押してください。';

  @override
  String get update_10 => 'アップデートファイルが保存された\nＵＳＢメディアを接続してから\n取り込みキーを押してください。';

  @override
  String get upg_12 => '取り込みキーを押して、アップデートファイルを\nインストールしてください。';

  @override
  String get update_13 => '現在のバージョンから直接新しいバージョンにアップデートすることはできません。';

  @override
  String get update_14 => 'ソフトウェアを下記バージョンにアップデートしてください。\nその際一旦マシンの電源を切ってから、アップデートファイルが保存されたUSBメディアを使用してアップデートをしてください。';

  @override
  String get update_15 => '一旦マシンの電源を切ってから、アップデートファイルが保存されたUSBメディアを使用してアップデートをしてください。';

  @override
  String get icon_00037 => '戻る';

  @override
  String get icon_00008_u => '閉じる';

  @override
  String get icon_00009_u => 'キャンセル';

  @override
  String get icon_00010_u => 'ＯＫ';

  @override
  String get icon_00050_u => '取り込み';

  @override
  String get upg_16 => 'アップデートに失敗しました。\nプログラムを再度インストールしてください。\n* それでも問題がある場合は、アップデートプログラムをダウンロードし直し、インストールしてください。';

  @override
  String get upg_17 => 'アップデートに失敗しました。\nアップデートプログラムを再度ダウンロードして、インストールしてください。';

  @override
  String get upg_18 => 'ERR_UPEND';

  @override
  String get upg_19 => '電源を入れなおして下さい。\n最初はミシンの起動に時間がかかります。途中画面が暗くなります。';

  @override
  String get upg_20 => '画面が暗くなっても、電源を切らないでください。';

  @override
  String get upg_21 => 'ファイルシステム破損の検出に失敗しました。\n電源を入れ直して下さい。';

  @override
  String get upg_22 => '破損したシステムファイルの修復に失敗しました。\n電源を入れ直して下さい。';

  @override
  String get upg_23 => 'アップデートに失敗しました。\nミシンを通常起動したあと、電源を切って、再度アップデートプログラムをインストールしてください。';

  @override
  String get t_name_01_01 => '直線(左基線)';

  @override
  String get t_name_01_02 => '直線(左基線)';

  @override
  String get t_name_01_03 => '直線(中基線)';

  @override
  String get t_name_01_04 => '直線(中基線)';

  @override
  String get t_name_01_05 => '３重ぬい';

  @override
  String get t_name_01_06 => '伸縮ぬい';

  @override
  String get t_name_01_07 => '飾りぬい';

  @override
  String get t_name_01_08 => 'しつけぬい';

  @override
  String get t_name_01_09 => 'ジグザグ(中基線)';

  @override
  String get t_name_01_10 => 'ジグザグ(中基線)';

  @override
  String get t_name_01_11 => 'ジグザグ(右基線)';

  @override
  String get t_name_01_12 => 'ジグザグ(左基線)';

  @override
  String get t_name_01_13 => '２点ジグザグ';

  @override
  String get t_name_01_14 => '２点ジグザグ';

  @override
  String get t_name_01_14a => '３点ジグザグ';

  @override
  String get t_name_01_15 => 'たちめかがり';

  @override
  String get t_name_01_16 => 'たちめかがり';

  @override
  String get t_name_01_17 => 'たちめかがり';

  @override
  String get t_name_01_18 => 'たちめかがり';

  @override
  String get t_name_01_19 => 'たちめかがり';

  @override
  String get t_name_01_20 => 'たちめかがり';

  @override
  String get t_name_01_21 => 'たちめかがり';

  @override
  String get t_name_01_22 => 'たちめかがり';

  @override
  String get t_name_01_23 => 'たちめかがり';

  @override
  String get t_name_01_24 => 'サイドカッター';

  @override
  String get t_name_01_25 => 'サイドカッター';

  @override
  String get t_name_01_26 => 'サイドカッター';

  @override
  String get t_name_01_27 => 'サイドカッター';

  @override
  String get t_name_01_28 => 'サイドカッター';

  @override
  String get t_name_01_29 => 'ピーシング直線';

  @override
  String get t_name_01_29a => 'ピーシング直線';

  @override
  String get t_name_01_30 => 'ピーシング直線';

  @override
  String get t_name_01_31 => '手ぬい風直線';

  @override
  String get t_name_01_32 => 'ジグザグ';

  @override
  String get t_name_01_33 => 'アップリケ';

  @override
  String get t_name_01_34 => 'キルティング模様';

  @override
  String get t_name_02_01 => 'まつりぬい';

  @override
  String get t_name_02_02 => 'まつりぬい';

  @override
  String get t_name_02_03 => 'アップリケ';

  @override
  String get t_name_02_03a => 'アップリケ';

  @override
  String get t_name_02_04 => 'シェルタック';

  @override
  String get t_name_02_05 => 'サテンスカラップ';

  @override
  String get t_name_02_06 => 'スカラップ';

  @override
  String get t_name_02_07 => 'つきあわせ';

  @override
  String get t_name_02_08 => 'つきあわせ';

  @override
  String get t_name_02_09 => 'つきあわせ';

  @override
  String get t_name_02_10 => 'スモッキング';

  @override
  String get t_name_02_11 => 'ファゴティング';

  @override
  String get t_name_02_12 => 'ファゴティング';

  @override
  String get t_name_02_13 => 'ゴムひもつけ';

  @override
  String get t_name_02_14 => '飾りぬい';

  @override
  String get t_name_02_15 => '飾りぬい';

  @override
  String get t_name_02_15a => '飾りぬい';

  @override
  String get t_name_02_16 => '飾りぬい';

  @override
  String get t_name_02_17 => '飾りぬい';

  @override
  String get t_name_02_18 => '飾りぬい';

  @override
  String get t_name_02_19 => '飾りぬい';

  @override
  String get t_name_03_01 => '飾りぬい';

  @override
  String get t_name_03_02 => '飾りぬい';

  @override
  String get t_name_03_03 => '飾りぬい';

  @override
  String get t_name_03_04 => '飾りぬい';

  @override
  String get t_name_03_05 => '飾りぬい';

  @override
  String get t_name_03_06 => '飾りぬい';

  @override
  String get t_name_03_07 => '飾りぬい';

  @override
  String get t_name_03_08 => '飾りぬい';

  @override
  String get t_name_03_09 => '飾りぬい';

  @override
  String get t_name_03_10 => '飾りぬい';

  @override
  String get t_name_03_11 => '飾りぬい';

  @override
  String get t_name_03_12 => '飾りぬい';

  @override
  String get t_name_03_13 => '飾りぬい';

  @override
  String get t_name_03_14 => '飾りぬい';

  @override
  String get t_name_03_15 => '飾りぬい';

  @override
  String get t_name_03_16 => '飾りぬい';

  @override
  String get t_name_03_17 => '飾りぬい';

  @override
  String get t_name_03_18 => '飾りぬい';

  @override
  String get t_name_03_19 => '飾りぬい';

  @override
  String get t_name_03_20 => '飾りぬい';

  @override
  String get t_name_03_21 => '飾りぬい';

  @override
  String get t_name_03_22 => '飾りぬい';

  @override
  String get t_name_03_23 => '飾りぬい';

  @override
  String get t_name_03_24 => '飾りぬい';

  @override
  String get t_name_03_25 => '飾りぬい';

  @override
  String get t_name_04_01 => 'ボタン穴かがり';

  @override
  String get t_name_04_02 => 'ボタン穴かがり';

  @override
  String get t_name_04_03 => 'ボタン穴かがり';

  @override
  String get t_name_04_04 => 'ボタン穴かがり';

  @override
  String get t_name_04_05 => 'ボタン穴かがり';

  @override
  String get t_name_04_06 => 'ボタン穴かがり';

  @override
  String get t_name_04_07 => 'ボタン穴かがり';

  @override
  String get t_name_04_08 => 'ボタン穴かがり';

  @override
  String get t_name_04_09 => 'ボタン穴かがり';

  @override
  String get t_name_04_10 => 'ボタン穴かがり';

  @override
  String get t_name_04_11 => 'ボタン穴かがり';

  @override
  String get t_name_04_12 => 'ボタン穴かがり';

  @override
  String get t_name_04_13 => 'ボタン穴かがり';

  @override
  String get t_name_04_14 => 'ボタン穴かがり';

  @override
  String get t_name_04_15 => 'ボタン穴かがり';

  @override
  String get t_name_04_15a => '４ステップボタンホール  １';

  @override
  String get t_name_04_15b => '４ステップボタンホール  ２';

  @override
  String get t_name_04_15c => '４ステップボタンホール  ３';

  @override
  String get t_name_04_15d => '４ステップボタンホール  ４';

  @override
  String get t_name_04_16 => 'ダーニング';

  @override
  String get t_name_04_17 => 'ダーニング';

  @override
  String get t_name_04_18 => 'かんどめ';

  @override
  String get t_name_04_19 => 'ボタンつけ';

  @override
  String get t_name_04_20 => 'アイレット';

  @override
  String get t_name_04_21 => 'アイレット';

  @override
  String get t_name_05_01 => '横送り直線';

  @override
  String get t_name_05_02 => '横送り直線';

  @override
  String get t_name_05_03 => '横送り直線';

  @override
  String get t_name_05_04 => '横送り直線';

  @override
  String get t_name_05_05 => '横送り直線';

  @override
  String get t_name_05_06 => '横送り直線';

  @override
  String get t_name_05_07 => '横送り直線';

  @override
  String get t_name_05_08 => '横送り直線';

  @override
  String get t_name_05_09 => '横送りジグザグ';

  @override
  String get t_name_05_10 => '横送りジグザグ';

  @override
  String get t_name_05_11 => '横送りジグザグ';

  @override
  String get t_name_05_12 => '横送りジグザグ';

  @override
  String get t_name_06_01 => 'フリーモーションカウチング';

  @override
  String get t_name_06_02 => 'フリーモーションしつけぬい';

  @override
  String get t_name_06_03 => '手ぬい風直線';

  @override
  String get t_name_06_04 => '手ぬい風直線';

  @override
  String get t_name_06_05 => '手ぬい風直線';

  @override
  String get t_name_06_06 => 'ニードルフェルティング模様';

  @override
  String get t_name_07_01 => 'アップリケ';

  @override
  String get t_name_07_02 => 'テーパリング';

  @override
  String get t_name_sr_01 => '直線(中基線)';

  @override
  String get t_name_sr_02 => 'ジグザグ(中基線)';

  @override
  String get t_name_sr_03 => 'フリーモーションしつけぬい';

  @override
  String get tt_head_wifi => '無線LAN設定';

  @override
  String get tt_head_camera => 'カメラビュー';

  @override
  String get tt_head_setting => '設定';

  @override
  String get tt_head_teaching => 'ミシンの使い方';

  @override
  String get tt_head_osae => '針・押え交換';

  @override
  String get tt_head_lock => '画面ロック';

  @override
  String get tt_head_home => 'ホーム';

  @override
  String get tt_foot_clock => '日時設定';

  @override
  String get tt_tch_og_principal_parts1 => '［押えレバー］';

  @override
  String get tt_tch_og_principal_parts2 => '［スピードコントロールレバー］';

  @override
  String get tt_tch_og_principal_parts3 => '［プーリー］';

  @override
  String get tt_tch_og_principal_parts4 => '［補助テーブル］';

  @override
  String get tt_tch_og_mb_knee_lifter => '［ニーリフター］';

  @override
  String get tt_tch_og_principal_parts6 => '［フットコントローラー］';

  @override
  String get tt_tch_og_principalbuttons1 => '［針上下スイッチ］';

  @override
  String get tt_tch_og_principalbuttons2 => '［糸切りスイッチ］';

  @override
  String get tt_tch_og_principalbuttons3 => '［押え上下スイッチ］';

  @override
  String get tt_tch_og_principalbuttons4 => '［自動糸通しスイッチ］';

  @override
  String get tt_tch_og_principalbuttons5 => '［スタート／ストップスイッチ］';

  @override
  String get tt_tch_og_principalbuttons6 => '［返しぬいスイッチ］';

  @override
  String get tt_tch_og_principalbuttons7 => '［止めぬいスイッチ］';

  @override
  String get tt_tch_og_basic_operation1 => '［上糸通し］';

  @override
  String get tt_tch_og_basic_operation2 => '［下糸巻き］';

  @override
  String get tt_tch_og_basic_operation3 => '［針の交換］';

  @override
  String get tt_tch_og_basic_operation4 => '［押えの交換］';

  @override
  String get tt_tch_og_basic_operation5 => '［下糸セット］';

  @override
  String get tt_tch_og_emb_basic_operation1 => '［糸調子の調節］';

  @override
  String get tt_tch_og_emb_basic_operation2 => '［布地に芯を貼るには］';

  @override
  String get tt_tch_og_emb_basic_operation3 => '［刺しゅう枠に布地を張るには］';

  @override
  String get tt_tch_og_emb_basic_operation4 => '［刺しゅう枠をセットするには］';

  @override
  String get tt_tch_og_emb_basic_operation5 => '［刺しゅう機の取り付け］';

  @override
  String get tt_tch_og_emb_basic_operation6 => '［刺しゅう押え＜Ｗ＞の取りつけ］';

  @override
  String get tt_tch_og_emb_basic_operation7 => '［適した接着芯］';

  @override
  String get tt_tch_maintenance1 => '［かまの掃除］';

  @override
  String get tt_utl_category01 => '直線／たち目かがり';

  @override
  String get tt_utl_category02 => '飾り';

  @override
  String get tt_utl_category03 => 'ヘアルーム';

  @override
  String get tt_utl_category04 => 'ボタン穴かがり／かんどめ';

  @override
  String get tt_utl_category05 => '横送り';

  @override
  String get tt_utl_category_q => 'キルト';

  @override
  String get tt_utl_category_s => 'その他';

  @override
  String get tt_utl_category_t => 'テーパリング';

  @override
  String get tt_utl_stitchpreview => 'プレビュー';

  @override
  String get tt_utl_projecter => 'プロジェクター機能';

  @override
  String get tt_utl_guideline => 'ガイドラインマーカー';

  @override
  String get tt_utl_editmenu => '編集メニュー';

  @override
  String get tt_utl_freemotion => 'フリーモーションモード';

  @override
  String get tt_utl_repeat_stitch_atamadashi => '模様の頭出し';

  @override
  String get tt_utl_alone_repeat => '単独／連続縫い';

  @override
  String get tt_utl_utilityflipvertical => '反転';

  @override
  String get tt_utl_twinneedle => '1 本針/ 2 本針';

  @override
  String get tt_utl_buttonholemanual => 'ボタン穴長さ';

  @override
  String get tt_utl_endpointsetting => '縫い終わり位置設定';

  @override
  String get tt_utl_tapering => 'テーパリング';

  @override
  String get tt_utl_autoreverse => '自動止めぬい';

  @override
  String get tt_utl_scissor => '自動糸切り';

  @override
  String get tt_utl_needlestopposition => '針停止位置設定';

  @override
  String get tt_utl_pivot => 'ピボット／自動上げ';

  @override
  String get tt_utl_threadcolor => '糸色変更';

  @override
  String get tt_utl_category06 => '大型飾り';

  @override
  String get tt_utl_category07 => '大型植物';

  @override
  String get tt_utl_category08 => '大型モチーフとメッセージ';

  @override
  String get tt_utl_category09 => '小型飾り';

  @override
  String get tt_utl_category10 => '小型植物';

  @override
  String get tt_utl_category11 => 'キャンドルウィック';

  @override
  String get tt_utl_category12 => '大型サテンステッチ';

  @override
  String get tt_utl_category13 => '7 mm サテンステッチ';

  @override
  String get tt_utl_category14 => 'クロスステッチ';

  @override
  String get tt_utl_category15 => '実用飾り';

  @override
  String get tt_utl_category16 => 'ディズニー';

  @override
  String get tt_utl_category17 => 'ゴシック体';

  @override
  String get tt_utl_category18 => '筆記体';

  @override
  String get tt_utl_category19 => 'アウトライン文字';

  @override
  String get tt_utl_category20 => 'キリル文字';

  @override
  String get tt_deco_category_pocket => 'ポケット(内蔵/外部メモリー)';

  @override
  String get tt_deco_mycustomsititch => 'マイイラスト機能';

  @override
  String get tt_deco_stitchpreview => 'プレビュー';

  @override
  String get tt_deco_projecter => 'プロジェクター機能';

  @override
  String get tt_deco_guidline => 'ガイドラインマーカー';

  @override
  String get tt_deco_editmenu => '編集メニュー';

  @override
  String get tt_deco_memory => '模様のデータ保存';

  @override
  String get tt_deco_threadcolor => '糸色変更';

  @override
  String get tt_deco_stitchplus => '模様追加';

  @override
  String get tt_deco_stitchselectall => '全選択オン／オフ';

  @override
  String get tt_deco_pivot => 'ピボット／自動上げ';

  @override
  String get tt_deco_needlestopposition => '針停止位置設定';

  @override
  String get tt_deco_scissor => '自動糸切り';

  @override
  String get tt_deco_autoreverse => '自動止めぬい';

  @override
  String get tt_deco_stitchstep1 => 'ステップ効果';

  @override
  String get tt_deco_stitchstep2 => 'ステップ効果';

  @override
  String get tt_deco_filemanager => 'ファイルマネージャー';

  @override
  String get tt_deco_filemanager_selectall => '全選択';

  @override
  String get tt_deco_filemanager_selectnone => '選択解除';

  @override
  String get tt_deco_filemanager_delete => '削除';

  @override
  String get tt_deco_filemanager_memory => '選択した実用模様を内蔵のメモリーに保存';

  @override
  String get tt_deco_freemotion => 'フリーモーションモード';

  @override
  String get tt_deco_repeat_stitch_atamadashi => '模様の頭出し';

  @override
  String get tt_deco_alone_repeat => '単独／連続縫い';

  @override
  String get tt_deco_utilityfliphorizon => '左右反転';

  @override
  String get tt_deco_utilityflipvertical => '上下反転';

  @override
  String get tt_deco_alone_single => '1 本針/ 2 本針';

  @override
  String get tt_deco_delete => '削除';

  @override
  String get tt_deco_density => '糸密度';

  @override
  String get tt_deco_elongator => '模様の長さ';

  @override
  String get tt_deco_spacing => '文字間';

  @override
  String get tt_deco_stitchsizelink => '縦横比維持';

  @override
  String get tt_deco_endpointsetting => '縫い終わり位置設定';

  @override
  String get tt_mcs_triplesewing => '1 重縫い／ 3 重縫い';

  @override
  String get tt_mcs_pointdelete => '点削除';

  @override
  String get tt_mcs_blockmove => 'ブロック移動';

  @override
  String get tt_mcs_insert => '挿入';

  @override
  String get tt_utl_mcspointset => 'セット';

  @override
  String get tt_mcs_contents => '模様インポート';

  @override
  String get tt_mcs_memory => '模様のデータ保存';

  @override
  String get tt_utl_sr_guideline => 'ガイドラインマーカー';

  @override
  String get tt_utl_sr_sensingline => 'センシングライン';

  @override
  String get tt_utl_sr_srstatus => 'ステッチレギュレーターステータス';

  @override
  String get tt_embcate_embpatterns => '刺しゅう模様';

  @override
  String get tt_embcate_character => '文字模様';

  @override
  String get tt_embcate_decoalphabet => '飾りアルファベット模様';

  @override
  String get tt_embcate_frame => '枠模様';

  @override
  String get tt_embcate_utility => 'ボタンホール模様/ 実用刺しゅう模様';

  @override
  String get tt_embcate_split => '分割刺しゅう模様';

  @override
  String get tt_embcate_long_stitch => 'ロングステッチ刺しゅう模様';

  @override
  String get tt_embcate_quilt => 'キルトサッシ&エッジトゥエッジキルト模様';

  @override
  String get tt_embcate_b_disney => 'ディズニー模様';

  @override
  String get tt_embcate_couching => 'カウチング模様';

  @override
  String get tt_embcate_t_exclusives => 'Exclusives';

  @override
  String get tt_embcate_memory => 'ミシンに記憶した模様や USB メディアなどに保存された模様の呼び出し';

  @override
  String get tt_emb_pantool => '手のひらツール';

  @override
  String get tt_emb_backgroundscan => '布地スキャン';

  @override
  String get tt_emb_realpreview => 'プレビュー';

  @override
  String get tt_emb_memory => '保存';

  @override
  String get tt_emb_redo => 'やり直す';

  @override
  String get tt_emb_undo => '元に戻す';

  @override
  String get tt_emb_delete => '削除';

  @override
  String get tt_emb_select => '選択';

  @override
  String get tt_emb_multipleselect => '複数選択';

  @override
  String get tt_emb_editsize => 'サイズ';

  @override
  String get tt_emb_editmove => '移動';

  @override
  String get tt_emb_editgroup => 'グループ化';

  @override
  String get tt_emb_editrotate => '回転';

  @override
  String get tt_emb_editflip => '左右反転';

  @override
  String get tt_emb_editduplicate => '複製';

  @override
  String get tt_emb_editdensity => '糸密度';

  @override
  String get tt_emb_editborder => 'ボーダー機能(繰り返し模様作成)';

  @override
  String get tt_emb_editapplique => 'アップリケピース';

  @override
  String get tt_emb_editchangecolor => '糸色パレット';

  @override
  String get tt_emb_edittextedit => '文字編集';

  @override
  String get tt_emb_editalign => '整列';

  @override
  String get tt_emb_editstippling => 'スティップリング';

  @override
  String get tt_emb_editoutline => 'アウトライン抽出';

  @override
  String get tt_emb_editorder => '縫い順';

  @override
  String get tt_emb_editnotsew => '縫製スキップ設定';

  @override
  String get tt_emb_textsize => 'サイズ';

  @override
  String get tt_emb_textarray => '配列';

  @override
  String get tt_emb_textspacing => '文字間';

  @override
  String get tt_emb_textalign => '整列';

  @override
  String get tt_emb_embfootw => '針落ち位置確認';

  @override
  String get tt_emb_emb_projectorsetting => 'プロジェクター設定';

  @override
  String get tt_emb_embprojector => 'プロジェクター';

  @override
  String get tt_emb_embmove => '移動';

  @override
  String get tt_emb_embrotate => '回転';

  @override
  String get tt_emb_embbasting => 'しつけ';

  @override
  String get tt_emb_embsnowman => '刺しゅう位置決め';

  @override
  String get tt_emb_embonecolorsew => '一色縫い';

  @override
  String get tt_emb_embcolorsorting => 'カラーソート';

  @override
  String get tt_emb_embconnectsew => '模様つなぎ';

  @override
  String get tt_emb_embframemove => '枠移動: 一時的に枠を中央に移動します。';

  @override
  String get tt_emb_embmemory => '保存';

  @override
  String get tt_emb_embmasktrace => '刺しゅう範囲トレース';

  @override
  String get tt_emb_embstartposition => '縫い始め位置';

  @override
  String get tt_emb_embneedlenumber => '進む／戻る';

  @override
  String get tt_emb_embfbcamera => 'カメラビュー';

  @override
  String get tt_emb_embthreadcutting => '糸切り／糸調子';

  @override
  String get tt_emb_embcolorbar => ' プログレスバー1 色／全色';

  @override
  String get tt_emb_patterninfo => '模様の情報';

  @override
  String get tt_emb_previewsim => 'ステッチシミュレーター';

  @override
  String get tt_emb_sewtrim_endcolor => '１色縫い終わりの糸切り';

  @override
  String get tt_emb_sewtrim_jumpstitch => '渡り糸の糸切り';

  @override
  String get tt_emb_previewframe => '刺しゅう枠のプレビュー';

  @override
  String get tt_emb_size_normalstb => '模様の大きさを変える針数を保って／糸密度を保って';

  @override
  String get tt_emb_edit_border_vert => '模様を上下方向に繰り返す／削除する';

  @override
  String get tt_emb_edit_border_horiz => '模様を左右方向に繰り返す／削除する';

  @override
  String get tt_emb_edit_border_dividervert => '模様を横方向に切る';

  @override
  String get tt_emb_edit_border_dividehoriz => '模様を縦方向に切る';

  @override
  String get tt_emb_edit_border_threadmark => '糸印';

  @override
  String get tt_emb_edit_border_reset => 'リセット';

  @override
  String get tt_emb_emb_rotate_reset => 'リセット';

  @override
  String get tt_emb_edit_rotate_reset => 'リセット';

  @override
  String get tt_emb_camera_rotate_reset => 'リセット';

  @override
  String get tt_emb_edit_font_spacing_reset => 'リセット';

  @override
  String get tt_emb_edit_align_reset => 'リセット';

  @override
  String get tt_emb_edit_size_reset => 'リセット';

  @override
  String get tt_emb_edit_order_reset => 'リセット';

  @override
  String get tt_emb_quiltborder_color_reset => 'リセット';

  @override
  String get tt_emb_edit_color_reset => 'リセット';

  @override
  String get tt_emb_photositich_size_change_reset => 'リセット';

  @override
  String get tt_emb_edit_projlcd_switch_fb_reset => 'リセット';

  @override
  String get tt_emb_edit_projlcd_align_reset => 'リセット';

  @override
  String get tt_emb_edit_projlcd_border_reset => 'リセット';

  @override
  String get tt_emb_edit_projlcd_rotate_reset => 'リセット';

  @override
  String get tt_emb_edit_projlcd_size_reset => 'リセット';

  @override
  String get tt_mdc_paint_rotate_reset => 'リセット';

  @override
  String get tt_mdc_paint_size_input_reset => 'リセット';

  @override
  String get tt_mdc_paint_size_reset => 'リセット';

  @override
  String get tt_emb_newapplique_color_selectall => '全選択';

  @override
  String get tt_emb_newapplique_color_selectnone => '選択解除';

  @override
  String get tt_emb_color_selectall => '単選択/全選択';

  @override
  String get tt_emb_colorcolorshuffling => 'Color Shuffling(カラーシャッフル)';

  @override
  String get tt_emb_colorvisualizer => 'Color Visualizer';

  @override
  String get tt_emb_editselectall => '全選択';

  @override
  String get tt_emb_editdeselectall => '選択解除';

  @override
  String get tt_emb_infoprintimage => '背景を組み合わせたイメージ';

  @override
  String get tt_emb_infooutputfiles => 'PDFファイル3種類(印刷できる布地用／アイロン転写用／位置合わせ用)がUSBメディアにコピーされます。';

  @override
  String get tt_emb_filemanager => 'ファイルマネージャー';

  @override
  String get tt_emb_filemanager_selectall => '全選択';

  @override
  String get tt_emb_filemanager_selectnone => '選択解除';

  @override
  String get tt_emb_filemanager_delete => '削除';

  @override
  String get tt_emb_filemanager_memory => '選択した模様を内蔵のメモリーに保存';

  @override
  String get tt_emb_easystippling_stippling => 'スティップリング模様';

  @override
  String get tt_emb_easystippling_echo => 'エコーキルト模様';

  @override
  String get tt_emb_easystippling_decorativefill => 'デコラティブフィル模様';

  @override
  String get tt_emb_quitlsash_startpoint => '開始位置の投影';

  @override
  String get tt_emb_quitlsash_endtpoint => '終点位置の投影';

  @override
  String get tt_emb_connect_migimawari => '2個目の模様位置を時計回りに移動します。';

  @override
  String get tt_emb_connect_hidarimawari => '2個目の模様位置を反時計回りに移動します。';

  @override
  String get tt_emb_connect_rotate => '回転';

  @override
  String get tt_emb_quiltborder_save => '保存';

  @override
  String get tt_mdc_pantool => '手のひらツール';

  @override
  String get tt_mdc_scanmenu => 'スキャンした画像や画像データファイルを使用して、自由に模様を作成できます。';

  @override
  String get tt_mdc_datacall => '描画データ(.pm9)の呼び出し';

  @override
  String get tt_mdc_linetool => '線ツール';

  @override
  String get tt_mdc_lineproperty => '線プロパティ';

  @override
  String get tt_mdc_linespoit => '線のスポイトツール';

  @override
  String get tt_mdc_linepouring => '線のバケツツール';

  @override
  String get tt_mdc_brushtool => 'ブラシツール';

  @override
  String get tt_mdc_brushproperty => '面プロパティ';

  @override
  String get tt_mdc_brushspoit => '面のスポイトツール';

  @override
  String get tt_mdc_brushpouring => '面のバケツツール';

  @override
  String get tt_mdc_painteraser => '消しゴム';

  @override
  String get tt_mdc_paintstamp => 'スタンプ図形';

  @override
  String get tt_mdc_paintsize => 'サイズ';

  @override
  String get tt_mdc_paintrotate => '回転';

  @override
  String get tt_mdc_paintflip => '反転';

  @override
  String get tt_mdc_paintduplicate => '複製';

  @override
  String get tt_mdc_paintcut => '切り取り';

  @override
  String get tt_mdc_paintpaste => '貼りつけ';

  @override
  String get tt_mdc_memory => '描画データ(.pm9)を保存';

  @override
  String get tt_mdc_select => '選択';

  @override
  String get tt_mdc_redo => 'やり直しキー';

  @override
  String get tt_mdc_undo => '元に戻す';

  @override
  String get tt_mdc_allclear => '全削除';

  @override
  String get tt_mdc_lineopen => '終点が開いた手書き線';

  @override
  String get tt_mdc_lineclose => '自動的に終点が閉じる手描き線';

  @override
  String get tt_mdc_lineline => '直線';

  @override
  String get tt_mdc_linepolygonal => '多角形';

  @override
  String get tt_mdc_stitchzigzag => 'ジグザグステッチ';

  @override
  String get tt_mdc_stitchrunning => '走り縫い';

  @override
  String get tt_mdc_stitchtriple => '3 重縫い';

  @override
  String get tt_mdc_stitchcandle => 'キャンドルウィックステッチ';

  @override
  String get tt_mdc_stitchchain => 'チェーンステッチ';

  @override
  String get tt_mdc_stitchestitch => 'E ステッチ';

  @override
  String get tt_mdc_stitchvsitich => 'V ステッチ';

  @override
  String get tt_mdc_stitchmotif => 'モチーフステッチ';

  @override
  String get tt_mdc_stitchnnotsew => '縫わない線';

  @override
  String get tt_mdc_stitchzigzaglowdensity => 'アップリケジグザグステッチ';

  @override
  String get tt_mdc_regiontatami => 'タタミ縫い';

  @override
  String get tt_mdc_regionstippling => 'スティップリング';

  @override
  String get tt_mdc_regiondecorativefill => 'デコラティブフィル模様';

  @override
  String get tt_mdc_regionnotsew => '縫わない面';

  @override
  String get tt_mdc_stamp1 => '基本図形';

  @override
  String get tt_mdc_stamp2 => '閉じた図形';

  @override
  String get tt_mdc_stamp3 => '開いた図形';

  @override
  String get tt_mdc_stamp4 => '保存したアウトライン';

  @override
  String get tt_mdc_stamp5 => '刺しゅう枠エリア';

  @override
  String get tt_mdc_stamp6 => 'カット用アウトライン';

  @override
  String get tt_mdc_select_rectangle => '四角選択';

  @override
  String get tt_mdc_select_continuousrectangle => '多角形選択';

  @override
  String get tt_mdc_select_free => '自由曲線選択';

  @override
  String get tt_mdc_select_auto => '自動選択';

  @override
  String get tt_mdc_select_all => '全選択';

  @override
  String get tt_mdc_memory_drawemb => '描画データ(.pm9)と刺しゅうデータ(.phx)の保存';

  @override
  String get tt_mdc_embset_pantool => '手のひらツール';

  @override
  String get tt_mdc_embset_patterninfo => '模様の情報';

  @override
  String get tt_mdc_embset_realpreview => 'プレビュー';

  @override
  String get tt_mdc_embset_projector => 'プロジェクター';

  @override
  String get tt_mdc_embset_projectorsetting => 'プロジェクター設定';

  @override
  String get tt_mdc_zigzagwidth => 'ジグザグ幅';

  @override
  String get tt_mdc_zigzagdensity => '糸密度';

  @override
  String get tt_mdc_runpitch => '走りピッチ';

  @override
  String get tt_mdc_running_undersew => '下打ち';

  @override
  String get tt_mdc_candlewicksize => 'サイ ズ';

  @override
  String get tt_mdc_candlewickspacing => '間隔';

  @override
  String get tt_mdc_chainsize => 'サイ ズ';

  @override
  String get tt_mdc_chainthickness => '重なり';

  @override
  String get tt_mdc_estitchwidth => 'ステッチ幅';

  @override
  String get tt_mdc_estitchspacing => '間隔';

  @override
  String get tt_mdc_estitchthickness => '重なり';

  @override
  String get tt_mdc_estitchflip => '反転';

  @override
  String get tt_mdc_vstitchwidth => 'ステッチ幅';

  @override
  String get tt_mdc_vstitchspacing => '間隔';

  @override
  String get tt_mdc_vstitchthickness => '重なり';

  @override
  String get tt_mdc_vstitchflip => '反転';

  @override
  String get tt_mdc_motifstitchsize => 'サイ ズ';

  @override
  String get tt_mdc_motifstitchspacing => '間隔';

  @override
  String get tt_mdc_motifstitchflip => '反転';

  @override
  String get tt_mdc_zigzagwidth_2 => 'ジグザグ幅';

  @override
  String get tt_mdc_zigzagdensity_2 => '糸密度';

  @override
  String get tt_mdc_tatamiderection => '縫い角度';

  @override
  String get tt_mdc_tatamidensity => '糸密度';

  @override
  String get tt_mdc_tatamipullconpen => '縮み補正';

  @override
  String get tt_mdc_tatamiundersewing => '下打ち';

  @override
  String get tt_mdc_stiprunpitch => '走りピッチ';

  @override
  String get tt_mdc_stipspacing => '間隔';

  @override
  String get tt_mdc_stipdistance => '距離';

  @override
  String get tt_mdc_stipsingletriple => '縫い目';

  @override
  String get tt_mdc_decofillsize => 'サイズ';

  @override
  String get tt_mdc_decofilldirection => '縫い角度';

  @override
  String get tt_mdc_decofilloutline => '糸切り削減用アウトライン';

  @override
  String get tt_mdc_decofillrandomshift => 'ゆらぎ';

  @override
  String get tt_mdc_decofillpositionoffset => '位置のオフセット';

  @override
  String get tt_mdc_decofillthickness1 => '重なり';

  @override
  String get tt_mdc_decofillthickness3 => '重なり';

  @override
  String get tt_mdc_decofillthickness1_2 => '１～２重';

  @override
  String get tt_mdc_decofillthickness2_3 => '２～３重';

  @override
  String get tt_mdc_stitchlink => '同じ縫い方設定の模様を一括選択';

  @override
  String get tt_mdc_fill_linereading => '線状の領域や、領域の周囲をアウトラインに変換できます。アウトラインに変換する線の太さを選択してください';

  @override
  String get tt_mdc_fill_linecolor => '指定した色で抽出されたアウトラインが、線属性に変換されます。';

  @override
  String get tt_emb_photostitch_backremoval => '背景除去';

  @override
  String get tt_emb_photostitch_framing => '範囲指定';

  @override
  String get tt_emb_photostitch_fittoframe => 'フレームにフィット';

  @override
  String get tt_emb_photostitch_backremoval_scopeplus => '領域追加：切り抜きたい領域を線でマークします';

  @override
  String get tt_emb_photostitch_backremoval_scopeminus => '領域解除：切り抜きたくない領域を線でマークします';

  @override
  String get tt_emb_photostitch_backremoval_erase => '指定した描画線を削除します';

  @override
  String get tt_emb_photostitch_backremoval_trash => '描画線を全削除します';

  @override
  String get tt_emb_photostitch_backremoval_blind => 'ペンで描いた線の表示/非表示';

  @override
  String get tt_emb_photostitch_styleusecolor => 'ON: スタイル画像の色を使う /OFF: オリジナル写真の色を使う';

  @override
  String get tt_emb_photostitch_colortune => '色調整';

  @override
  String get tt_emb_photostitch_colorlis_allselect => '色リストの糸色をすべて固定する／クリアする';

  @override
  String get tt_emb_photostitch_colorlis_add => '色リストに色を追加する';

  @override
  String get tt_emb_photostitch_colorlis_remove => '選択した色を色リストから削除する';

  @override
  String get tt_emb_photostitch_pantool => '手のひらツール';

  @override
  String get tt_emb_photostitch_memory => '保存';

  @override
  String get tt_emb_edit_projectorsetting => 'プロジェクター設定';

  @override
  String get tt_emb_edit_projector => 'プロジェクター';

  @override
  String get tt_settings_reset_3type => '設定のリセット(実用縫い／本体一般／刺しゅう)';

  @override
  String get tt_settings_screenimage_usb => '設定画面を画像として USB メディアに保存する';

  @override
  String get tt_camera_emb_screenshot => 'カメラ画像をUSBメディアに保存';

  @override
  String get tt_camera_emb_grid => 'グリッド表示/非表示';

  @override
  String get tt_camera_emb_needlepoint => '針落ち点の表示/非表示';

  @override
  String get tt_camera_util_screenshot => 'カメラ画像をUSBメディアに保存';

  @override
  String get tt_camera_util_grid => 'グリッド表示/非表示';

  @override
  String get tt_camera_util_needlepoint => '針落ち点の表示/非表示';
}
