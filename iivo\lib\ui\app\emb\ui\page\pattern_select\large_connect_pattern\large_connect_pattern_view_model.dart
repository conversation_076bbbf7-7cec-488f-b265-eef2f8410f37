import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../memory/memory.dart';
import '../../../../../../../model/app_locale.dart';
import '../../../../../../../model/handel_model.dart';
import '../../../../../../../model/setting_model.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../page_route/page_route.dart';
import '../../../../model/large_connected_model.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/select_information_model.dart';
import '../../../../model/select_model.dart';
import '../category_selector/category_selector_view_interface.dart';
import '../category_selector/category_selector_view_model.dart';
import 'large_connect_pattern_view_interface.dart';

final largeConnectPatternViewModelProvider = StateNotifierProvider.autoDispose<
    LargeConnectPatternViewModelInterface,
    LargeConnectPatternState>((ref) => LargeConnectPatternViewModel(ref));

class LargeConnectPatternViewModel
    extends LargeConnectPatternViewModelInterface {
  LargeConnectPatternViewModel(Ref ref)
      : super(const LargeConnectPatternState(), ref) {
    update();
  }

  @override
  void update() {
    Color itemColor = PatternModel().getThumbnailBackgroundColor();

    state = state.copyWith(
      fileList:
          _getFileDisplayList(LargeConnectedModel().loadFileList, itemColor),
      partsList: LargeConnectedModel().selectedFile == null
          ? null
          : _getPartsDisplayList(
              LargeConnectedModel().selectedFile!,
              LargeConnectedModel().loadPartsList,
              itemColor,
            ),
    );
  }

  @override
  void onLargePatternClick(int index) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// 待機ポップアップウィンドウを開く
    GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_PLEASE_WAIT,
        arguments: {GlobalPopupRoute.isStopSystemSound: true});

    LargeConnectedModel().loadParts(index).then((accessError) {
      if (accessError != AccessError.none) {
        HandelModel.handleMemoryAccessError(accessError);
        return;
      }

      /// 待機中のポップアップウィンドウを閉じます
      GlobalPopupRoute().resetErrorState();

      update();
    });
  }

  @override
  void onPartsClick(int index) {
    if (LargeConnectedModel().selectedFile == null) {
      return;
    }

    /// パターンログイン
    EmbLibraryError error = LargeConnectedModel().selectEditFile(
      LargeConnectedModel().selectedFile!.fileName,
      DataSource.USERLOG_DATA_SRC_INTERNAL,
      0,
      index,
    );

    ///selectEditFile->embGotoEdit->selectEditFile
    if (error == EmbLibraryError.EMB_INVALID_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// View更新
    state = state.copyWith(isSetButtonEnable: true);
    ref
        .read(categorySelectorViewModelProvider.notifier)
        .updateTopPageByChild(CategorySelectorModuleType.patternSelector);
  }

  @override
  void onSelectFolderUpButtonClicked() {
    /// 読み込まれたデータをクリーンアップします。
    LargeConnectedModel().unLoadPartsList();
    state = state.copyWith(partsList: null);
  }

  @override
  void onReturnButtonClicked(BuildContext context) {
    /// ファイルを開く場合
    if (LargeConnectedModel().selectedFile == null) {
      /// Model更新
      PatternModel().deleteTemporaryPatternList();
      SelectInformationModel()
          .setTemporaryPatternAutoKind(CategoryType.unknownPattern);
      SelectModel()
        ..selectedCategoryType = null
        ..selectedPatternIndex = null;

      _closeSelf(context);
    } else {
      /// ファイルが開かれていない場合
      LargeConnectedModel().unLoadPartsList();
      state = state.copyWith(partsList: null);
    }
  }

  @override
  void onSetButtonClick(BuildContext context) {
    EmbLibraryError error = EmbLibrary().apiBinding.embGotoEdit();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    SelectModel().isLargeConnectSet = true;

    /// Model更新
    PatternModel()
      ..reloadAllPattern()
      ..clearTemporaryPatternList();
    SelectInformationModel()
        .setTemporaryPatternAutoKind(CategoryType.unknownPattern);
    SelectModel()
      ..selectedCategoryType = null
      ..selectedPatternIndex = null;

    PagesRoute().pushNamedAndRemoveUntil(
        nextRoute: PageRouteEnum.patternEdit, untilRoute: PageRouteEnum.home);
  }

  ///
  /// ファイルの表示データを取得します
  ///
  List<ThumbnailDisplayInfo> _getFileDisplayList(
      List<LargePatternFileInfo> fileList, Color itemColor) {
    List<ThumbnailDisplayInfo> displayFileList = [];
    bool isUnitMm = PatternModel().isUnitMm;

    for (var file in fileList) {
      displayFileList.add(
        ThumbnailDisplayInfo(
          fileName: isUnitMm
              ? file.fileName.split('.').first
              : _decodeFileNameToInch(file.fileName),
          image: file.image,
          partsNumber: file.isFileNotOpen ? null : file.partsCount.toString(),
          splitIconType: file.isFileNotOpen ? null : SplitIconType.multiData,
          itemColor: itemColor,
          isFileNotOpen: file.isFileNotOpen,
        ),
      );
    }
    return displayFileList;
  }

  ///
  /// partsの表示データを取得します
  ///
  List<ThumbnailDisplayInfo> _getPartsDisplayList(LargePatternFileInfo file,
      List<FilePartsInfo> partsList, Color itemColor) {
    List<ThumbnailDisplayInfo> displayPartsList = [];

    displayPartsList.add(
      ThumbnailDisplayInfo(
        fileName: PatternModel().isUnitMm
            ? file.fileName.split('.').first
            : _decodeFileNameToInch(file.fileName),
        image: file.image,
        partsNumber: file.isFileNotOpen ? null : file.partsCount.toString(),
        splitIconType: file.isFileNotOpen ? null : SplitIconType.multiData,
        isShowConnectIcon: true,
        itemColor: itemColor,
        isFileNotOpen: file.isFileNotOpen,
      ),
    );

    for (var file in partsList) {
      displayPartsList.add(
        ThumbnailDisplayInfo(
          fileName: file.partsNumber,
          image: file.image,
          bottomLeftString: file.partsName,
          itemColor: itemColor,
        ),
      );
    }
    return displayPartsList;
  }

  ///
  /// インチ、ファイル名を変換します。
  ///
  String _decodeFileNameToInch(String fileName) {
    int spaceIndex = fileName.indexOf(' ');
    int xIndex = fileName.indexOf('x');
    int mmIndex = fileName.indexOf('mm');

    /// 不規則なファイル名を処理します「指定された文字が見つかりませんでした」
    if ([spaceIndex, xIndex, mmIndex].contains(-1)) {
      return fileName;
    }

    /// 不規則なファイル名を処理します「指定された文字が順番に並んでいません」
    if (spaceIndex >= xIndex || xIndex >= mmIndex) {
      return fileName;
    }

    int? width = int.tryParse(fileName.substring(spaceIndex + 1, xIndex));
    int? height = int.tryParse(fileName.substring(xIndex + 1, mmIndex));

    /// 不規則なファイル名を処理します「指定された文字が順番に並んでいません」
    if (width == null || height == null) {
      return fileName;
    }

    AppLocalizations l10n =
        lookupAppLocalizations(AppLocale().getCurrentLocale());

    String newFileName = fileName.substring(0, spaceIndex + 1) +
        (width * SettingModel().inchRate).toStringAsFixed(2) +
        fileName.substring(xIndex, xIndex + 1) +
        (height * SettingModel().inchRate).toStringAsFixed(2) +
        l10n.icon_00226;

    return newFileName;
  }

  ///
  /// 自分を閉じる
  ///
  void _closeSelf(BuildContext context) {
    if (PopupNavigator.canPop(context: context)) {
      PopupNavigator.pop(context: context);
    }
  }
}
