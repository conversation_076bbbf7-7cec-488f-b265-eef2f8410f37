import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'line_e_stitch_thickness_popup_view_model.dart';

class ThicknessPopup extends ConsumerStatefulWidget {
  const ThicknessPopup({super.key});

  @override
  ConsumerState<ThicknessPopup> createState() => _ThicknessSettingPopupState();
}

class _ThicknessSettingPopupState extends ConsumerState<ThicknessPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(lineEStitchThicknessViewModelProvider);
    final viewModel = ref.read(lineEStitchThicknessViewModelProvider.notifier);

    return Row(
      children: [
        const Spacer(
          flex: 571,
        ),
        Expanded(
          flex: 229,
          child: Column(
            children: [
              const Spacer(
                flex: 159,
              ),
              Expanded(
                flex: 1052,
                child: Scaffold(
                  backgroundColor: Colors.transparent,
                  body: pre_edit_toolbar_mdc(
                    child: Column(
                      children: [
                        const Spacer(
                          flex: 37,
                        ),
                        const Expanded(
                          flex: 68,
                          child: Row(
                            children: [
                              Spacer(
                                flex: 52,
                              ),
                              Expanded(
                                flex: 126,
                                child: ico_mdcstitch_chain_repetitio(),
                              ),
                              Spacer(
                                flex: 51,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 24,
                        ),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_str_thickness(
                                  text: l10n.icon_00564,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 8,
                        ),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_str_parameter(
                                  signText: String.fromCharCode(0x00d7),
                                  text: state.thicknessDisplayValue,
                                  isDefault: state.isDefaultStyle,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 12,
                        ),
                        Expanded(
                          flex: 63,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 48,
                              ),
                              Expanded(
                                flex: 63,
                                child: grp_btn_minus_01(
                                  onTap: () =>
                                      viewModel.onMinusButtonClicked(false),
                                  onLongPress: () =>
                                      viewModel.onMinusButtonClicked(true),
                                  state: state.minusButtonValid
                                      ? ButtonState.normal
                                      : ButtonState.disable,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 10,
                              ),
                              Expanded(
                                flex: 63,
                                child: grp_btn_plus_01(
                                  onTap: () =>
                                      viewModel.onPlusButtonClicked(false),
                                  onLongPress: () =>
                                      viewModel.onPlusButtonClicked(true),
                                  state: state.plusButtonValid
                                      ? ButtonState.normal
                                      : ButtonState.disable,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 47,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 620,
                        ),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_btn_positive_mdc(
                                  onTap: viewModel.onOkButtonClicked,
                                  text: l10n.icon_ok,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 12,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const Spacer(
                flex: 69,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
