import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../../../model/preview_model.dart' show PatternDisplayInfo;

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'filter_result_view_interface.freezed.dart';

@freezed
class FilterResultState with _$FilterResultState {
  const factory FilterResultState({
    @Default(0) int frameIndex,
    @Default(false) bool isSelectedPattern,
    @Default(false) bool showZoomList,
    @Default(100) int selectedZoomScale,
    @Default('----') String heightValue,
    @Default('----') String widthValue,
    @Default('----') String totalTimeValue,
    @Default('----') String numberOfColorsValue,
    @Default('----') String colorChangesValue,
    @Default(false) bool showFilterButton,
    @Default([]) List<Image> imageGroup,
    @Default(false) bool isFilterApplied,
    @Default(0) int thumbnailSize,
    @Default(false) bool setButtonEnable,
    @Default(false) bool isSelectSortLeft,
    @Default(ButtonState.disable) ButtonState formationButtonState,
    @Default(ButtonState.disable) ButtonState realPreviewButtonState,

    /// Pattern表示情報
    @Default([]) List<PatternDisplayInfo> patternDisplayInfoList,
    @Default([]) List<PatternDisplayInfo> temporaryGroupDisplayInfoList,
  }) = _FilterResultState;
}

abstract class FilterResultViewInterface extends ViewModel<FilterResultState> {
  FilterResultViewInterface(super.state, this.ref);

  /// providerのref
  ///
  final Ref ref;

  ///
  /// 倍率選択ポップアップを開くためのクリック関数
  ///
  void onZoomButtonClicked();

  ///
  /// 選択されているものを取得します icon
  ///
  Widget getSelectedZoomIcon();

  ///
  /// 倍率値リストを閉じる
  ///
  void closeZoomPopup();

  ///
  /// 倍率値変更
  ///
  void onZoomPopupListClick(int zoomValue);

  ///
  /// 詳細ポップアップを開くためのクリック関数
  ///
  void onInformationButtonClicked(BuildContext context);

  ///
  /// リアルプレビューポップアップを開くためのクリック関数
  ///
  void onRealPreviewButtonClicked();

  ///
  /// ボタンのクリック関数
  ///
  void onFilterButtonClicked(BuildContext context);

  ///
  /// フィルターバーの×ボタンのクリック関数
  ///
  void onFilterCloseButtonClicked();

  ///
  /// フィルターの降順ボタンのクリック関数
  ///
  void onFilterReverseButtonClick();

  ///
  /// フィルターの昇順ボタンのクリック関数
  ///
  void onFilterOrderButtonClick();

  ///
  /// 模様を選択します
  /// @Param
  /// index:模様のcategoryType
  ///
  void onPatternClick(int patternIndex);

  ///
  /// 名前付きルートの登録
  ///
  void onReturnButtonClicked();

  ///
  /// Setのクリック関数
  ///
  void onSetButtonClicked();
}
