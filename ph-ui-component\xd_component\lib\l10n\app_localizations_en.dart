import 'app_localizations.dart';

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get color_001 => 'PINK';

  @override
  String get color_002 => 'DUSTY ROSE';

  @override
  String get color_003 => 'PETAL PINK';

  @override
  String get color_004 => 'LIGHT PINK';

  @override
  String get color_005 => 'LIGHT CORAL';

  @override
  String get color_006 => 'GINGER JAR';

  @override
  String get color_007 => 'HEATHER MIST';

  @override
  String get color_008 => 'CHAMPAGNE';

  @override
  String get color_009 => 'DARK MAUVE';

  @override
  String get color_010 => 'HEATHER';

  @override
  String get color_011 => 'COMFORT PINK';

  @override
  String get color_012 => 'MOUNTAIN\nROSE';

  @override
  String get color_013 => 'CHERRY PINK';

  @override
  String get color_014 => 'CARNATION';

  @override
  String get color_015 => 'SALMON';

  @override
  String get color_016 => 'SHRIMP';

  @override
  String get color_017 => 'DARK CORAL';

  @override
  String get color_018 => 'BITTERROOT';

  @override
  String get color_019 => 'BURGUNDY';

  @override
  String get color_020 => 'WARM WINE';

  @override
  String get color_021 => 'RUSSET';

  @override
  String get color_022 => 'PLUM';

  @override
  String get color_023 => 'MAROON';

  @override
  String get color_024 => 'ROYAL CREST';

  @override
  String get color_025 => 'HOT PINK';

  @override
  String get color_026 => 'RUBY';

  @override
  String get color_027 => 'DARK FUCHSIA';

  @override
  String get color_028 => 'CARMINE';

  @override
  String get color_029 => 'DEEP ROSE';

  @override
  String get color_030 => 'BEGONIA';

  @override
  String get color_031 => 'AZALEA';

  @override
  String get color_032 => 'RUBINE RED';

  @override
  String get color_033 => 'STRAWBERRY';

  @override
  String get color_034 => 'DEVIL RED';

  @override
  String get color_035 => 'CANDY APPLE\nRED';

  @override
  String get color_036 => 'HOLLYHOCK\nRED';

  @override
  String get color_037 => 'TOASTY RED';

  @override
  String get color_038 => 'WILD FIRE';

  @override
  String get color_039 => 'RED';

  @override
  String get color_040 => 'JOCKEY RED';

  @override
  String get color_041 => 'RADIANT RED';

  @override
  String get color_042 => 'RED BERRY';

  @override
  String get color_043 => 'FOXY RED';

  @override
  String get color_044 => 'LIPSTICK';

  @override
  String get color_045 => 'CHRISTMAS\nRED';

  @override
  String get color_046 => 'SCARLET';

  @override
  String get color_047 => 'DEEP SCARLET';

  @override
  String get color_048 => 'CRANBERRY';

  @override
  String get color_049 => 'FLESH PINK';

  @override
  String get color_050 => 'BISQUE';

  @override
  String get color_051 => 'FLAMINGO';

  @override
  String get color_052 => 'MELON';

  @override
  String get color_053 => 'PEACH MELBA';

  @override
  String get color_054 => 'HONEYSUCKLE';

  @override
  String get color_055 => 'DARK ORANGE';

  @override
  String get color_056 => 'CLEAR BLUE';

  @override
  String get color_057 => 'BLUE JOY';

  @override
  String get color_058 => 'ICE BLUE';

  @override
  String get color_059 => 'MEDIUM BLUE';

  @override
  String get color_060 => 'ROCKPORT\nBLUE';

  @override
  String get color_061 => 'PASTEL BLUE';

  @override
  String get color_062 => 'BABY BLUE';

  @override
  String get color_063 => 'SKY BLUE';

  @override
  String get color_064 => 'LIGHT BLUE';

  @override
  String get color_065 => 'LAKE BLUE';

  @override
  String get color_066 => 'HAPPY BLUE';

  @override
  String get color_067 => 'ULTRA BLUE';

  @override
  String get color_068 => 'TROPIC BLUE';

  @override
  String get color_069 => 'CORNFLOWER\nBLUE';

  @override
  String get color_070 => 'BLUE MOON';

  @override
  String get color_071 => 'SAPPHIRE';

  @override
  String get color_072 => 'SLATE BLUE';

  @override
  String get color_073 => 'DARK BLUE';

  @override
  String get color_074 => 'DEEP BLUE';

  @override
  String get color_075 => 'WONDER BLUE';

  @override
  String get color_076 => 'BALTIC BLUE';

  @override
  String get color_077 => 'CALIFORNIA\nBLUE';

  @override
  String get color_078 => 'CERULEAN';

  @override
  String get color_079 => 'SOLAR BLUE';

  @override
  String get color_080 => 'ELECTRIC\nBLUE';

  @override
  String get color_081 => 'PACIFIC BLUE';

  @override
  String get color_082 => 'BLUE';

  @override
  String get color_083 => 'IMPERIAL BLUE';

  @override
  String get color_084 => 'FLEET BLUE';

  @override
  String get color_085 => 'BLUE RIBBON';

  @override
  String get color_086 => 'LIGHT NAVY';

  @override
  String get color_087 => 'MEDIUM NAVY';

  @override
  String get color_088 => 'EMPIRE BLUE';

  @override
  String get color_089 => 'MIDNIGHT\nNAVY';

  @override
  String get color_090 => 'DARK\nSAPPHIRE';

  @override
  String get color_091 => 'BLUE SUEDE';

  @override
  String get color_092 => 'ULTRA MARINE';

  @override
  String get color_093 => 'ROYAL BLUE';

  @override
  String get color_094 => 'COBALT BLUE';

  @override
  String get color_095 => 'PRUSSIAN\nBLUE';

  @override
  String get color_096 => 'NASSAU BLUE';

  @override
  String get color_097 => 'CHOW BLUE';

  @override
  String get color_098 => 'PARIS BLUE';

  @override
  String get color_099 => 'BLUE\nLAVENDER';

  @override
  String get color_100 => 'MEDIUM\nLAVENDER';

  @override
  String get color_101 => 'LAVENDER';

  @override
  String get color_102 => 'TULIP\nLAVENDER';

  @override
  String get color_103 => 'WISTERIA\nVIOLET';

  @override
  String get color_104 => 'QUEEN PURPLE';

  @override
  String get color_105 => 'LIVID\nLAVENDER';

  @override
  String get color_106 => 'PURPLE\nCHARIOT';

  @override
  String get color_107 => 'PURPLE MAZE';

  @override
  String get color_108 => 'DEEP PURPLE';

  @override
  String get color_109 => 'PURPLE\nACCENT';

  @override
  String get color_110 => 'KING PURPLE';

  @override
  String get color_111 => 'ROYAL PURPLE';

  @override
  String get color_112 => 'MOD PURPLE';

  @override
  String get color_113 => 'PURPLE';

  @override
  String get color_114 => 'VIOLET';

  @override
  String get color_115 => 'PALE PURPLE';

  @override
  String get color_116 => 'MAGENTA';

  @override
  String get color_117 => 'LIGHT LILAC';

  @override
  String get color_118 => 'LILAC';

  @override
  String get color_119 => 'SIBERIAN IRIS';

  @override
  String get color_120 => 'FANTASIA PINK';

  @override
  String get color_121 => 'DREAM PINK';

  @override
  String get color_122 => 'EXCLUSIVE\nPINK';

  @override
  String get color_123 => 'PINK\nSPLENDOR';

  @override
  String get color_124 => 'WILD PINK';

  @override
  String get color_125 => 'AMBER RED';

  @override
  String get color_126 => 'RASPBERRY\nICE';

  @override
  String get color_127 => 'AUBERGINE';

  @override
  String get color_128 => 'BAMBINO BLUE';

  @override
  String get color_129 => 'BLUE FRINGE';

  @override
  String get color_130 => 'MARINE AQUA';

  @override
  String get color_131 => 'BLUE WISTERIA';

  @override
  String get color_132 => 'ANGEL BLUE';

  @override
  String get color_133 => 'MALLARD BLUE';

  @override
  String get color_134 => 'PEACOCK BLUE';

  @override
  String get color_135 => 'DARK JADE';

  @override
  String get color_136 => 'PARADISE\nGREEN';

  @override
  String get color_137 => 'PERPETUAL\nTEAL';

  @override
  String get color_138 => 'VENUS BLUE';

  @override
  String get color_139 => 'FEATHER BLUE';

  @override
  String get color_140 => 'MINT JULEP';

  @override
  String get color_141 => 'SEA GLASS';

  @override
  String get color_142 => 'TURQUOISE';

  @override
  String get color_143 => 'MYSTIC TEAL';

  @override
  String get color_144 => 'AQUA';

  @override
  String get color_145 => 'OCEANIC\nGREEN';

  @override
  String get color_146 => 'TEMPEST TURQ';

  @override
  String get color_147 => 'MD GREEN';

  @override
  String get color_148 => 'DARK TEAL';

  @override
  String get color_149 => 'OCEAN AQUA';

  @override
  String get color_150 => 'BLUE SPRUCE';

  @override
  String get color_151 => 'NEWPORT';

  @override
  String get color_152 => 'GREEN BAY';

  @override
  String get color_153 => 'SEACREST';

  @override
  String get color_154 => 'KIWI GREEN';

  @override
  String get color_155 => 'OLIVE GREEN';

  @override
  String get color_156 => 'HARBOR\nGREEN';

  @override
  String get color_157 => 'SPECIAL\nGREEN';

  @override
  String get color_158 => 'DARK ARMY\nGREEN';

  @override
  String get color_159 => 'HEAVY GREEN';

  @override
  String get color_160 => 'ALPINE GREEN';

  @override
  String get color_161 => 'FIELD GREEN';

  @override
  String get color_162 => 'GREEN SAIL';

  @override
  String get color_163 => 'MIDNIGHT TEAL';

  @override
  String get color_164 => 'SEA MIST';

  @override
  String get color_165 => 'WILLOW GREEN';

  @override
  String get color_166 => 'TEALEAF';

  @override
  String get color_167 => 'ISLE GREEN';

  @override
  String get color_168 => 'FOREST PINE';

  @override
  String get color_169 => 'JADE';

  @override
  String get color_170 => 'PEPPERMINT';

  @override
  String get color_171 => 'TEAL GREEN';

  @override
  String get color_172 => 'DEEP GREEN';

  @override
  String get color_173 => 'CLASSIC\nGREEN';

  @override
  String get color_174 => 'DARK PINE\nGREEN';

  @override
  String get color_175 => 'GREEN';

  @override
  String get color_176 => 'IRISH GREEN';

  @override
  String get color_177 => 'EMERALD\nGREEN';

  @override
  String get color_178 => 'SHAMROCK';

  @override
  String get color_179 => 'MOSS GREEN';

  @override
  String get color_180 => 'LIGHT KELLY';

  @override
  String get color_181 => 'KELLY';

  @override
  String get color_182 => 'LEAF GREEN';

  @override
  String get color_183 => 'SLIGHTLY\nGREEN';

  @override
  String get color_184 => 'GREEN OAK';

  @override
  String get color_185 => 'MINT GREEN';

  @override
  String get color_186 => 'FRESH GREEN';

  @override
  String get color_187 => 'PEAPOD';

  @override
  String get color_188 => 'PASTORAL\nGREEN';

  @override
  String get color_189 => 'LIGHT\nAVOCADO';

  @override
  String get color_190 => 'HARVEST\nGREEN';

  @override
  String get color_191 => 'GREEN DUST';

  @override
  String get color_192 => 'LIME GREEN';

  @override
  String get color_193 => 'ERIN GREEN';

  @override
  String get color_194 => 'FOLIAGE\nGREEN';

  @override
  String get color_195 => 'SUNFLOWER';

  @override
  String get color_196 => 'GOLD';

  @override
  String get color_197 => 'AUTUMN\nGREEN';

  @override
  String get color_198 => 'OLIVE DRAB';

  @override
  String get color_199 => 'MEADOW';

  @override
  String get color_200 => 'SAGE';

  @override
  String get color_201 => 'DARK OLIVE';

  @override
  String get color_202 => 'ARMY GREEN';

  @override
  String get color_203 => 'CORONATION\nGOLD';

  @override
  String get color_204 => 'LEMON\nYELLOW';

  @override
  String get color_205 => 'BRIGHT\nYELLOW';

  @override
  String get color_206 => 'YELLOW';

  @override
  String get color_207 => 'OMBRE GOLD';

  @override
  String get color_208 => 'MANILA';

  @override
  String get color_209 => 'GOLDENROD';

  @override
  String get color_210 => 'WARM\nSUNSHINE';

  @override
  String get color_211 => 'POLLEN GOLD';

  @override
  String get color_212 => 'DAY LILLY';

  @override
  String get color_213 => 'STAR GOLD';

  @override
  String get color_214 => 'GOLDEN\nSUNSHINE';

  @override
  String get color_215 => 'BRASS';

  @override
  String get color_216 => 'ORANGE';

  @override
  String get color_217 => 'DEEP GOLD';

  @override
  String get color_218 => 'HARVEST GOLD';

  @override
  String get color_219 => 'YELLOW MIST';

  @override
  String get color_220 => 'MUSTARD';

  @override
  String get color_221 => 'COPPER';

  @override
  String get color_222 => 'GRILLED\nORANGE';

  @override
  String get color_223 => 'ORANGEADE';

  @override
  String get color_224 => 'PAPRIKA';

  @override
  String get color_225 => 'VERMILLION';

  @override
  String get color_226 => 'SAFFRON';

  @override
  String get color_227 => 'AUBURN';

  @override
  String get color_228 => 'TERRA COTTA';

  @override
  String get color_229 => 'DARK RUST';

  @override
  String get color_230 => 'DARK MELON';

  @override
  String get color_231 => 'TAWNY';

  @override
  String get color_232 => 'SOFT PEACH';

  @override
  String get color_233 => 'RUST';

  @override
  String get color_234 => 'DARK APRICOT';

  @override
  String get color_235 => 'TANGERINE';

  @override
  String get color_236 => 'PUMPKIN';

  @override
  String get color_237 => 'SUNBURST';

  @override
  String get color_238 => 'GOLD SPICE';

  @override
  String get color_239 => 'HONEY';

  @override
  String get color_240 => 'ALMOND';

  @override
  String get color_241 => 'REDDISH\nBROWN';

  @override
  String get color_242 => 'CLAY BROWN';

  @override
  String get color_243 => 'RUSSET\nBROWN';

  @override
  String get color_244 => 'CREAM BROWN';

  @override
  String get color_245 => 'CREAM\nYELLOW';

  @override
  String get color_246 => 'GLOW';

  @override
  String get color_247 => 'PISTACHIO';

  @override
  String get color_248 => 'GOLDEN TAN';

  @override
  String get color_249 => 'GINGER';

  @override
  String get color_250 => 'TEMPLE GOLD';

  @override
  String get color_251 => 'TAN';

  @override
  String get color_252 => 'SUNTAN';

  @override
  String get color_253 => 'CANDY TAN';

  @override
  String get color_254 => 'BEIGE';

  @override
  String get color_255 => 'RATTAN';

  @override
  String get color_256 => 'DARK BEIGE';

  @override
  String get color_257 => 'BRONZE';

  @override
  String get color_258 => 'COFFEE';

  @override
  String get color_259 => 'LINEN';

  @override
  String get color_260 => 'SEASHELL';

  @override
  String get color_261 => 'ECRU';

  @override
  String get color_262 => 'SALMON PINK';

  @override
  String get color_263 => 'LIGHT COCOA';

  @override
  String get color_264 => 'DUSTY PEACH';

  @override
  String get color_265 => 'LIGHT BROWN';

  @override
  String get color_266 => 'KHAKI';

  @override
  String get color_267 => 'COFFEE BEAN';

  @override
  String get color_268 => 'BROWNSTONE';

  @override
  String get color_269 => 'DEEP BROWN';

  @override
  String get color_270 => 'DARK BROWN';

  @override
  String get color_271 => 'BROWN';

  @override
  String get color_272 => 'HAPPY TRAIL';

  @override
  String get color_273 => 'DARK TAUPE';

  @override
  String get color_274 => 'WARM GRAY';

  @override
  String get color_275 => 'DEEP GRAY';

  @override
  String get color_276 => 'METAL';

  @override
  String get color_277 => 'BLACK\nCHROME';

  @override
  String get color_278 => 'CHARCOAL';

  @override
  String get color_279 => 'MEDIUM GRAY';

  @override
  String get color_280 => 'COOL GRAY';

  @override
  String get color_281 => 'SMOKE GRAY';

  @override
  String get color_282 => 'PEWTER';

  @override
  String get color_283 => 'DARK GRAY';

  @override
  String get color_284 => 'GRAY';

  @override
  String get color_285 => 'LIGHT GRAY';

  @override
  String get color_286 => 'CHROME';

  @override
  String get color_287 => 'ANTIQUE GOLD';

  @override
  String get color_288 => 'SILVER';

  @override
  String get color_289 => 'BLACK';

  @override
  String get color_290 => 'NATURAL\nWHITE';

  @override
  String get color_291 => 'WHITE';

  @override
  String get color_292 => 'NEON PINK';

  @override
  String get color_293 => 'DARK PINK';

  @override
  String get color_294 => 'WATERMELON';

  @override
  String get color_295 => 'SOFT PINK';

  @override
  String get color_296 => 'LIGHT\nCANTALOUPE';

  @override
  String get color_297 => 'PEACH MIST';

  @override
  String get color_298 => 'HAVANA\nYELLOW';

  @override
  String get color_299 => 'DARK CHERRY';

  @override
  String get color_300 => 'BLUE INK';

  @override
  String get color_301 => 'APPLIQUE\nMATERIAL';

  @override
  String get color_302 => 'APPLIQUE\nPOSITION';

  @override
  String get color_303 => 'APPLIQUE';

  @override
  String get id_icon_test00001 => '\$\$\$\$\$';

  @override
  String get icon_00002 => 'Sewing';

  @override
  String get icon_00003_1 => 'Embroidery';

  @override
  String get icon_00006_3 => 'Utility\nStitch';

  @override
  String get icon_00007_3 => 'Character\nDecorative\nStitch';

  @override
  String get icon_stitch => 'Stitch';

  @override
  String get icon_close_1 => 'Close';

  @override
  String get icon_cancel => 'Cancel';

  @override
  String get icon_ok => 'OK';

  @override
  String get icon_00011_zz => 'Delete';

  @override
  String get icon_00011_zz_s => 'Delete';

  @override
  String get icon_00011 => 'Delete';

  @override
  String get icon_00012_zz => 'All Delete';

  @override
  String get icon_00012_zz_s => 'All Delete';

  @override
  String get icon_reset_zz => 'Reset';

  @override
  String get icon_reset_zz_s => 'Reset';

  @override
  String get icon_reset => 'Reset';

  @override
  String get icon_reset_v => 'Reset';

  @override
  String get icon_00014_zz => 'Memory';

  @override
  String get icon_00014_zz_s => 'Memory';

  @override
  String get icon_00014 => 'Memory';

  @override
  String get icon_save => 'Save';

  @override
  String get icon_00015_zz => 'Retrieve';

  @override
  String get icon_00015_zz_s => 'Retrieve';

  @override
  String get icon_util_mem_retrieve => 'Retrieve';

  @override
  String get icon_util_mem_memory => 'Memory';

  @override
  String get icon_util_mem_reset => 'Reset';

  @override
  String get icon_util_mem_delete => 'Delete';

  @override
  String get icon_util_mem_alldelete => 'All Delete';

  @override
  String get icon_00017_zz => 'Width';

  @override
  String get icon_00017_zz_s => 'Width';

  @override
  String get icon_00018_zz => 'Length';

  @override
  String get icon_00018_zz_s => 'Length';

  @override
  String get icon_00019_zz => 'Tension';

  @override
  String get icon_00019_zz_s => 'Tension';

  @override
  String get icon_00020_zz => 'L/R Shift';

  @override
  String get icon_00020_zz_s => 'L/R Shift';

  @override
  String get icon_util_width => 'Width';

  @override
  String get icon_util_length => 'Length';

  @override
  String get icon_util_lrshift => 'L/R Shift';

  @override
  String get icon_util_tension => 'Tension';

  @override
  String get icon_util_slitlength => 'Slit Length';

  @override
  String get icon_00021_zz => 'Twin Needle';

  @override
  String get icon_00021_zz_s => 'Twin Needle';

  @override
  String get icon_00022_zz => 'Elongation';

  @override
  String get icon_00022_zz_s => 'Elongation';

  @override
  String get icon_00027_zz => 'Point\nDelete';

  @override
  String get icon_00027_zz_s => 'Point\nDelete';

  @override
  String get icon_00028_zz => 'Block\nMove';

  @override
  String get icon_00028_zz_s => 'Block\nMove';

  @override
  String get icon_00029_zz => 'Insert';

  @override
  String get icon_00029_zz_s => 'Insert';

  @override
  String get icon_00038_zz => 'Set';

  @override
  String get icon_00038_zz_s => 'Set';

  @override
  String get icon_00030_1 => 'Test';

  @override
  String get icon_guidel_guideline => 'Guideline';

  @override
  String get icon_guidel_main => 'Main';

  @override
  String get icon_guidel_sub => 'Sub';

  @override
  String get icon_guidel_mainline => 'Main Line';

  @override
  String get icon_guidel_subline => 'Sub Line';

  @override
  String get icon_guidel_linelength => 'Line Length';

  @override
  String get icon_guidel_line_l => 'L';

  @override
  String get icon_guidel_line_m => 'M';

  @override
  String get icon_guidel_line_s => 'S';

  @override
  String get icon_guidel_color => 'Color';

  @override
  String get icon_guidel_position => 'Position';

  @override
  String get icon_guidel_main_pos => 'Main Line Position';

  @override
  String get icon_guidel_sub_pos => 'Sub Line Position';

  @override
  String get icon__guidel_sub_frommain => 'Distance from main line';

  @override
  String get icon_guidel_gridsize => 'Grid Size';

  @override
  String get icon_guidel_angle => 'Angle';

  @override
  String get icon_guidel_seamallowance => 'Seam Allowance';

  @override
  String get icon_guidel_spacing => 'Spacing';

  @override
  String get icon_guidel_lengthl_zz => 'L';

  @override
  String get icon_guidel_lengthl_zz_s => 'L';

  @override
  String get icon_guidel_lengthm_zz => 'M';

  @override
  String get icon_guidel_lengthm_zz_s => 'M';

  @override
  String get icon_guidel_lengths_zz => 'S';

  @override
  String get icon_guidel_lengths_zz_s => 'S';

  @override
  String get icon_position => 'Position';

  @override
  String get icon_00031_2 => 'Edit';

  @override
  String get icon_00033_1 => 'Add';

  @override
  String get icon_00035 => 'Embroidery';

  @override
  String get icon_return => 'Return';

  @override
  String get icon_00038_1 => 'Set';

  @override
  String get icon_00038_2 => 'Set';

  @override
  String get icon_00039 => 'min';

  @override
  String get icon_00041_1 => 'Select';

  @override
  String get icon_select => 'Select';

  @override
  String get icon_select_2 => 'Select';

  @override
  String get icon_00041_2 => 'Select';

  @override
  String get icon_00042 => 'Always press when\nremoving embroidery unit.';

  @override
  String get icon_00046_zz => '%%%icon%%%';

  @override
  String get icon_00046_zz_s => 'Duplicate';

  @override
  String get icon_00048 => 'Number';

  @override
  String get icon_00049 => 'List';

  @override
  String get icon_00050 => 'Load';

  @override
  String get icon_00051_zz => 'Clear';

  @override
  String get icon_00051_zz_s => 'Clear';

  @override
  String get icon_00052_zz => 'All\nClear';

  @override
  String get icon_00052_zz_s => 'All\nClear';

  @override
  String get icon_00053_b1 => 'Color Shuffling ';

  @override
  String get icon_00053_b2 => 'Color\nShuffling ';

  @override
  String get icon_00053_t1 => 'Color Visualizer';

  @override
  String get icon_00053_t2 => 'Color\nVisualizer';

  @override
  String get icon_00055_1 => 'Random';

  @override
  String get icon_00055_2 => 'Random';

  @override
  String get icon_00056_1 => 'Gradient';

  @override
  String get icon_00056_2 => 'Gradient';

  @override
  String get icon_00057 => 'Vivid';

  @override
  String get icon_00054 => 'Soft';

  @override
  String get icon_00058_1 => 'Number of Colors';

  @override
  String get icon_00059 => 'Refresh';

  @override
  String get icon_00060 => 'None Specified';

  @override
  String get icon_emb_tension => 'Tension';

  @override
  String get icon_emb_threadcutting => 'Thread cutting';

  @override
  String get icon_00063_a => 'End Color Trim';

  @override
  String get icon_00064_a => 'Jump Stitch Trim';

  @override
  String get icon_00065 => 'Density';

  @override
  String get icon_00066 => 'Size';

  @override
  String get icon_00067_zz => 'Rotate';

  @override
  String get icon_00067_zz_s => 'Rotate';

  @override
  String get icon_00068_zz => 'Move';

  @override
  String get icon_00068_zz_s => 'Move';

  @override
  String get icon_00070_zz => 'Multi\nColor';

  @override
  String get icon_00070_zz_s => 'Multi\nColor';

  @override
  String get icon_00071_zz => 'Check';

  @override
  String get icon_00071_zz_s => 'Check';

  @override
  String get icon_00072 => 'Layout';

  @override
  String get icon_00075_zz => 'Array';

  @override
  String get icon_00075_zz_s => 'Array';

  @override
  String get icon_00076_zz => 'Spacing';

  @override
  String get icon_00076_zz_s => 'Spacing';

  @override
  String get icon_00077_zz => 'Font\nEdit';

  @override
  String get icon_00077_zz_s => 'Font\nEdit';

  @override
  String get icon_00079 => 'Needle Drop\n Position';

  @override
  String get icon_00080 => 'Next';

  @override
  String get icon_prev => 'Previous';

  @override
  String get icon_segment => 'Segment';

  @override
  String get icon_00083 => 'End Point Setting';

  @override
  String get icon_00084 => 'Length tuning';

  @override
  String get icon_00085 => 'End Point Setting\nTemporary Stop';

  @override
  String get icon_00088 => 'Scan';

  @override
  String get icon_00089 => 'Video';

  @override
  String get icon_00090 => 'Repeat';

  @override
  String get icon_00091_1 => 'Multiple Selection';

  @override
  String get icon_00091_2 => 'Multiple\nSelection';

  @override
  String get icon_00093_zz => 'Select\nall';

  @override
  String get icon_00093_zz_s => 'Select\nall';

  @override
  String get icon_00094_zz => 'Select\nnone';

  @override
  String get icon_00094_zz_s => 'Select\nnone';

  @override
  String get icon_00095 => 'Exit';

  @override
  String get icon_00096 => 'Auto split of Quilt Sashes';

  @override
  String get icon_resettodef => 'Reset to default settings ';

  @override
  String get icon_resettodefall => 'Reset to Default';

  @override
  String get icon_resettodefall_2 => 'Reset to Default';

  @override
  String get icon_00100 => 'Language';

  @override
  String get icon_00101_a => 'English';

  @override
  String get icon_00101_b => 'English';

  @override
  String get icon_00102 => 'Brightness';

  @override
  String get icon_00103 => 'Screen Display\nBrightness';

  @override
  String get icon_00104 => 'Screen Saver';

  @override
  String get icon_00105 => 'Default';

  @override
  String get icon_00106 => 'Customize';

  @override
  String get icon_00107 => 'Eco Mode';

  @override
  String get icon_00108 => 'Shutoff Support Mode';

  @override
  String get icon_00109 => 'Light';

  @override
  String get icon_00112 => 'Machine Speaker Volume';

  @override
  String get icon_00114 => 'Volume';

  @override
  String get icon_00115 => 'Mouse Pointer';

  @override
  String get icon_00116 => 'mm / \"(inch)';

  @override
  String get icon_00118 => 'Home Page';

  @override
  String get icon_00119 => 'Sewing/\nEmbroidery\nScreen';

  @override
  String get icon_00192 => 'Initial Screen';

  @override
  String get icon_00121 => 'Opening Screen';

  @override
  String get icon_00122 => 'Initial Stitch Page';

  @override
  String get icon_00123 => 'Spool Stand LED';

  @override
  String get icon_00124 => 'Width Control';

  @override
  String get icon_00125_1 => 'Fine Adjust Verti.';

  @override
  String get icon_00126_1 => 'Fine Adjust Horiz.';

  @override
  String get icon_00127_1 => 'Presser Foot Height';

  @override
  String get icon_00128_1 => 'Presser Foot Pressure';

  @override
  String get icon_00129 => 'Initial Position';

  @override
  String get icon_00130_1 => 'Pivoting Height';

  @override
  String get icon_00131_1 => 'Free Motion\nFoot Height';

  @override
  String get icon_00134 => 'Automatic Fabric\nSensor System';

  @override
  String get icon_00135 => 'Fabric Thickness Sensor';

  @override
  String get icon_00136_2 => 'Needle Position - \nUp/Down';

  @override
  String get icon_00137 => 'Needle Position -\nStitch Placement';

  @override
  String get icon_00138 => 'Upper and Bobbin\nThread Sensor';

  @override
  String get icon_00140 => 'Dual Feed\nFeed Adjustment';

  @override
  String get icon_00141 => 'Multi Function\nFoot Controller ';

  @override
  String get icon_00142 => 'Heel Kick ';

  @override
  String get icon_00143 => 'Side Pedal';

  @override
  String get icon_00144_a => 'Needle Position\nUp/Down';

  @override
  String get icon_00145 => 'Thread Cutting';

  @override
  String get icon_00146 => 'Single Stitch ';

  @override
  String get icon_00147 => 'Reverse Stitch ';

  @override
  String get icon_00243 => 'Presser Foot\nUp/Down';

  @override
  String get icon_00244 => 'No setting';

  @override
  String get icon_00249 => 'Reinforcement Stitch';

  @override
  String get icon_00148 => 'Slit Length';

  @override
  String get icon_00148_zz => 'Slit Length';

  @override
  String get icon_00148_zz_s => 'Slit Length';

  @override
  String get icon_00150 => 'Reinforcement\nPriority';

  @override
  String get icon_00152_1 => 'Embroidery Frame Display';

  @override
  String get icon_00155_1 => 'Thumbnail Size';

  @override
  String get icon_00157 => 'Embroidery\nBackground Color';

  @override
  String get icon_00159 => 'Thumbnail\nBackground Color';

  @override
  String get icon_00163_a => 'Background Image\nDisplay';

  @override
  String get icon_00163 => 'Background\nImage';

  @override
  String get icon_00164 => 'Scan Image';

  @override
  String get icon_00165 => 'Standard';

  @override
  String get icon_00166 => 'Fine';

  @override
  String get icon_00167 => 'Scan Quality';

  @override
  String get icon_00168 => 'Spool Stand LED';

  @override
  String get icon_00178 => 'Embroidery Basting\nDistance';

  @override
  String get icon_00180 => 'Embroidery Appliqué\nDistance';

  @override
  String get icon_00182_1 => 'Max Embroidery Speed';

  @override
  String get icon_00183_1 => 'Embroidery Tension';

  @override
  String get icon_00184_1 => 'Embroidery Foot Height';

  @override
  String get icon_00185 => 'Frame size';

  @override
  String get icon_00186 => 'Grid';

  @override
  String get icon_00187 => 'Change';

  @override
  String get icon_00188 => 'Delete';

  @override
  String get icon_00191 => 'Color';

  @override
  String get icon_00193 => 'Clock Display';

  @override
  String get icon_00194 => 'AM';

  @override
  String get icon_00195 => 'PM';

  @override
  String get icon_00196 => '24h';

  @override
  String get icon_clock_msg1 => 'Set correct date for network connection.';

  @override
  String get icon_00197 => 'Sensor Function\nCalibration';

  @override
  String get icon_00199 => 'Guideline Marker\nAdjustment';

  @override
  String get icon_00200 => 'Guideline Marker\n Brightness';

  @override
  String get icon_00201_1 => 'Embroidery Foot with\nLED Pointer Adjustment';

  @override
  String get icon_00202_p => 'Brightness';

  @override
  String get icon_00206_1 => 'Certification';

  @override
  String get icon_00207_a => 'Kit';

  @override
  String get icon_00208 => 'Start';

  @override
  String get icon_00209 => 'Stop';

  @override
  String get icon_00211 => 'Service Count';

  @override
  String get icon_00212 => 'SCS';

  @override
  String get icon_00214 => 'Total Count';

  @override
  String get icon_00218 => 'No.';

  @override
  String get icon_00220 => 'Version';

  @override
  String get icon_00222 => 'YYYY';

  @override
  String get icon_00223 => 'MM';

  @override
  String get icon_00224 => 'DD';

  @override
  String get icon_00225 => 'mm';

  @override
  String get icon_00226 => '\"';

  @override
  String get icon_on => 'ON';

  @override
  String get icon_off => 'OFF';

  @override
  String get icon_00229 => 'KB';

  @override
  String get icon_00230 => 'bPocket';

  @override
  String get icon_00231 => '1';

  @override
  String get icon_00232 => '2';

  @override
  String get icon_00233 => '3';

  @override
  String get icon_00234 => '4';

  @override
  String get icon_00235 => '5';

  @override
  String get icon_00236 => '6';

  @override
  String get icon_00237 => '7';

  @override
  String get icon_00238 => '8';

  @override
  String get icon_00239 => '9';

  @override
  String get icon_00240 => '0';

  @override
  String get icon_00241 => 'C';

  @override
  String get icon_00242 => '%';

  @override
  String get icon_00245 => 'Automatic Presser Foot\nLift';

  @override
  String get icon_00246 => 'Auto Down';

  @override
  String get icon_00247 => 'Auto Up';

  @override
  String get icon_00248_zz => '          Press to Trim';

  @override
  String get icon_00248_zz_s => '          Press to Trim';

  @override
  String get icon_00248 => 'Press to Trim';

  @override
  String get icon_00251 => 'Projector';

  @override
  String get icon_00253 => 'Background Color';

  @override
  String get icon_00254 => 'Sewing:\nPattern Outline';

  @override
  String get icon_00255 => 'Embroidery:\nPointer Color';

  @override
  String get icon_pointershape => 'Pointer Shape';

  @override
  String get icon_00256 => 'Camera';

  @override
  String get icon_00257 => 'Needle Calibration for\nCamera/Projector';

  @override
  String get icon_recog_ok => 'OK';

  @override
  String get icon_recog_ng => 'NG';

  @override
  String get icon_00258 => 'Embroidery\nNeedle Stop Position';

  @override
  String get icon_00259 => 'Unit';

  @override
  String get icon_00260 => 'Thread color ';

  @override
  String get icon_00261 => 'Thread brand';

  @override
  String get icon_00264 => 'Name of Color';

  @override
  String get icon_00265 => '# 123';

  @override
  String get icon_00266 => 'Time';

  @override
  String get icon_00268 => 'Original';

  @override
  String get icon_00269 => 'Embroidery';

  @override
  String get icon_00269_t => 'Embroidery';

  @override
  String get icon_00270 => 'Country';

  @override
  String get icon_00270_t => 'Country';

  @override
  String get icon_00271 => 'Madeira\nPoly';

  @override
  String get icon_00272 => 'Madeira\nRayon';

  @override
  String get icon_00273 => 'Sulky';

  @override
  String get icon_00274 => 'Robison-Anton';

  @override
  String get icon_00275 => 'Robison-Anton\nPoly';

  @override
  String get icon_00276 => 'Robison-Anton\nRayon';

  @override
  String get icon_00277 => 'Isacord';

  @override
  String get icon_00278 => 'Gütermann';

  @override
  String get icon_00279 => 'Simplicity Pro';

  @override
  String get icon_00279_p => 'Pacesetter Pro';

  @override
  String get icon_00280 => 'Floriani';

  @override
  String get icon_00281 => 'Iris';

  @override
  String get icon_00282 => 'Aurifil';

  @override
  String get icon_00283 => 'WonderFil ';

  @override
  String get icon_00284 => 'Polyfast';

  @override
  String get icon_00290 => 'If you have purchased an upgrade kit\nand want to certify your sewing machine, \npress [CERTIFICATION] key.';

  @override
  String get icon_00291 => 'KIT I';

  @override
  String get icon_00292 => 'KIT II';

  @override
  String get icon_00293 => 'KIT III';

  @override
  String get icon_00294 => 'KIT IV';

  @override
  String get icon_00295 => 'KIT V';

  @override
  String get icon_00296 => 'KIT VI';

  @override
  String get icon_00297 => 'KIT VII';

  @override
  String get icon_00298 => 'KIT VIII';

  @override
  String get icon_00299 => 'KIT IX';

  @override
  String get icon_00300 => 'KIT X';

  @override
  String get icon_00643_s => 'none';

  @override
  String get icon_00301 => 'Operation Guide';

  @override
  String get icon_00302 => 'Sewing Guide';

  @override
  String get icon_00303 => 'Pattern Explanation';

  @override
  String get icon_manuals => 'Manuals';

  @override
  String get icon_operariong_b => 'Operation Manual';

  @override
  String get icon_operariong_t => 'Instruction and Reference Guide';

  @override
  String get icon_pdf => 'PDF Manual';

  @override
  String get icon_supportsite => 'Support Site';

  @override
  String get icon_pdf_eula => 'End-user license agreements\n(EULA)';

  @override
  String get icon_pdf_sewing => 'Sewing';

  @override
  String get icon_pdf_emb => 'Embroidery';

  @override
  String get icon_pdf_sewing_ef => 'Sewing';

  @override
  String get icon_pdf_emb_ef => 'Embroidery';

  @override
  String get icon_pdf_sewing_t => 'Sewing';

  @override
  String get icon_pdf_emb_t => 'Embroidery';

  @override
  String get icon_f_omadendum => 'Addendum';

  @override
  String get icon_f_omadendum_ef => 'Addendum';

  @override
  String get icon_f_omadendum_l => 'Operation Manual\nAddendum';

  @override
  String get icon_f_om_kit1 => 'KIT I';

  @override
  String get icon_f_om_kit2 => 'KIT II';

  @override
  String get icon_f_om_kit3 => 'KIT III';

  @override
  String get icon_f_om_kit1_l => 'Operation Manual\nKIT I';

  @override
  String get icon_f_omadendum_t => 'Addendum';

  @override
  String get icon_f_om_kit1_t => 'KIT I';

  @override
  String get icon_f_om_kit2_t => 'KIT II';

  @override
  String get icon_f_om_kit3_t => 'KIT III';

  @override
  String get icon_t_pdf_iivo_url_b => 'If you want to view the manuals on your mobile device or PC, please access\nhttps://s.brother/fmraa.';

  @override
  String get icon_t_pdf_iivo_url_t => 'If you want to view the manual on your mobile device or PC, please access\nhttps://babylock.com/radiance-instruction-and-reference-guide.';

  @override
  String get icon_t_video_iivo_url_b => 'Please visit us at\n https://s.brother/fvraa\nso that you can view the tutorial videos on this model.';

  @override
  String get icon_t_video_iivo_url_t => 'Please visit us at\n https://babylock.com/radiance-training\nso that you can view the tutorial videos on this model.';

  @override
  String get icon_pdf_url_qr_t => 'www.babylock.com';

  @override
  String get icon_nettool => 'Network Diagnosis Tool';

  @override
  String get icon_iagree => 'I Agree';

  @override
  String get icon_terms_cancel => 'Cancel';

  @override
  String get icon_confirm => 'Confirm';

  @override
  String get icon_00304 => 'Principal Parts';

  @override
  String get icon_00305 => 'Principal Buttons';

  @override
  String get icon_00306 => 'Basic Operation';

  @override
  String get icon_00307 => 'Embroidery\nBasic Operation';

  @override
  String get icon_00308 => 'Troubleshooting';

  @override
  String get icon_00309 => 'Maintenance';

  @override
  String get icon_00310 => 'The thread is tangled on\nthe wrong side of the fabric';

  @override
  String get icon_00311 => 'Cannot thread the needle';

  @override
  String get icon_00312 => 'Cannot use\nthe needle threader';

  @override
  String get icon_00313 => 'Thread tension is incorrect';

  @override
  String get icon_00314 => 'Upper thread breaks';

  @override
  String get icon_00315 => 'Bobbin thread breaks';

  @override
  String get icon_00316 => 'Skipped stitches';

  @override
  String get icon_00317 => 'Needle breaks';

  @override
  String get icon_00318 => 'Machine does not operate';

  @override
  String get icon_00320 => 'Character pattern\ndoes not sew out correctly';

  @override
  String get icon_00321 => 'Fabric does not feed\nthrough the machine';

  @override
  String get icon_00322 => 'Fabric puckers';

  @override
  String get icon_00323 => 'Machine is noisy';

  @override
  String get icon_00325 => 'Embroidery pattern\ndoes not sew out correctly';

  @override
  String get icon_00326 => 'Embroidery unit\ndoes not operate';

  @override
  String get icon_00331 => 'Bar Tack';

  @override
  String get icon_00332 => 'Blind Hem Stitch';

  @override
  String get icon_00333 => 'Buttonhole';

  @override
  String get icon_00334 => 'Buttons';

  @override
  String get icon_00335 => 'Dart Seam';

  @override
  String get icon_00336 => 'Flat Fell Seam';

  @override
  String get icon_00337 => 'Gathering';

  @override
  String get icon_00338 => 'Overcasting';

  @override
  String get icon_00339 => 'Pintuck';

  @override
  String get icon_00340 => 'Scallop';

  @override
  String get icon_00341 => 'Straight Stitch';

  @override
  String get icon_00342 => 'Zipper Insertion';

  @override
  String get icon_00343 => 'Piecing';

  @override
  String get icon_00344 => 'Free-Motion\nQuilting';

  @override
  String get icon_00345 => 'Quilting';

  @override
  String get icon_00346 => 'Echo Quilting';

  @override
  String get icon_00347 => 'Applique 1';

  @override
  String get icon_00348 => 'Applique 2';

  @override
  String get icon_search => 'Search';

  @override
  String get icon_00353 => 'Upper threading the machine';

  @override
  String get icon_00354 => 'Winding the bobbin';

  @override
  String get icon_00355 => 'Changing the needle';

  @override
  String get icon_00356 => 'Changing the presser foot';

  @override
  String get icon_00357 => 'Setting the bobbin';

  @override
  String get icon_00358 => 'Sewing Function';

  @override
  String get icon_00359 => 'Using the thread trimming function';

  @override
  String get icon_00360 => 'Using special screwdriver';

  @override
  String get icon_00361 => 'Using the pivot function';

  @override
  String get icon_00362 => 'Setting the stitch width and length';

  @override
  String get icon_00363 => 'Using multipurpose screwdriver';

  @override
  String get icon_00364 => 'Using automatic fabric sensor system (Automatic presser foot pressure)';

  @override
  String get icon_00365 => 'Using my custom stitch';

  @override
  String get icon_00366 => 'Using the edge sewing function';

  @override
  String get icon_00367 => 'Creating bobbin work (sewing)';

  @override
  String get icon_00368 => 'Creating bobbin work (embroidery)';

  @override
  String get icon_00369 => 'Preparing for bobbin work';

  @override
  String get icon_00370 => 'Preparing for reverse bobbin work';

  @override
  String get icon_00371 => 'Creating the reverse bobbin work (sewing)';

  @override
  String get icon_00372 => 'Using the built-in camera on the sewing mode';

  @override
  String get icon_00373 => 'Adjusting the needle drop position with the guideline marker on the setting screen';

  @override
  String get icon_00374 => 'Adjusting the brightness of the guideline marker on the settings screen';

  @override
  String get icon_00375 => 'Setting the thread tension';

  @override
  String get icon_00376 => 'Attaching Iron-on Stabilizers';

  @override
  String get icon_00377 => 'Hooping fabric in the embroidery frame';

  @override
  String get icon_00378 => 'Attaching/Removing the embroidery frame';

  @override
  String get icon_00379 => 'Attaching/Removing the embroidery unit/flatbed attachment';

  @override
  String get icon_00380 => 'Attaching/Removing the presser foot holder';

  @override
  String get icon_00381 => 'Embroidery Function';

  @override
  String get icon_00382 => 'Using  print and stitch function';

  @override
  String get icon_00383 => 'Using color shuffling function';

  @override
  String get icon_00384 => 'Using my design center';

  @override
  String get icon_00385 => 'Scanning line drawings';

  @override
  String get icon_00386 => 'Scanning illustrations';

  @override
  String get icon_00387 => 'Using the scanning frame';

  @override
  String get icon_00388 => 'Displaying the fabric on the LCD (Scanning with the built-in camera)';

  @override
  String get icon_00389 => 'Aligning the embroidering position with the positioning sticker';

  @override
  String get icon_00390 => 'Connecting patterns using the built-in camera';

  @override
  String get icon_00391 => 'Aligning the embroidering position using the built-in camera';

  @override
  String get icon_00392 => '';

  @override
  String get icon_00393 => 'Settings';

  @override
  String get icon_00394 => 'Camera needle position setting';

  @override
  String get icon_00395 => 'Upgrading the machine’s software';

  @override
  String get icon_00396 => 'Adjusting the guideline marker on the settings screen';

  @override
  String get icon_00397 => 'Setting the time/date';

  @override
  String get icon_00398 => 'Using automatic reinforcement stitching';

  @override
  String get icon_00399 => 'Others';

  @override
  String get icon_00400 => 'Viewing/saving videos';

  @override
  String get icon_00401 => 'Sensor Pen';

  @override
  String get icon_00402 => 'Connecting the sensor pen';

  @override
  String get icon_00403 => 'Calibrating the sensor pen';

  @override
  String get icon_00404 => 'Specifying the guideline marker position with the sensor pen';

  @override
  String get icon_00405 => 'Specifying the needle drop position with the sensor pen';

  @override
  String get icon_00406 => 'Specifying the stitch width/position with the sensor pen';

  @override
  String get icon_00407 => 'Specifying the sewing end point with the sensor pen';

  @override
  String get icon_00408 => 'Specifying the embroidering position with the sensor pen';

  @override
  String get icon_00409 => 'Accessory';

  @override
  String get icon_00410 => 'Using the knee lifter';

  @override
  String get icon_00411 => 'Using the multi-screwdriver';

  @override
  String get icon_00412 => 'Using the Multipurpose screwdriver';

  @override
  String get icon_00416 => 'Installing the multi function foot controller';

  @override
  String get icon_00417 => 'Assigning functions to the multi function foot controller';

  @override
  String get icon_00418 => 'Attaching/Removing the embroidery foot with LED pointer';

  @override
  String get icon_00419 => 'Adjusting the embroidery foot with LED pointer';

  @override
  String get icon_00420 => 'Creating stippling embroidery patterns using the built-in camera';

  @override
  String get icon_00421 => 'Attaching the presser foot with the included adapter';

  @override
  String get icon_00422 => 'Using the accessory case';

  @override
  String get icon_00423 => 'Maintenance (cleaning the race)';

  @override
  String get icon_00500 => 'My Design Center';

  @override
  String get icon_00500_2 => 'My Design\nCenter';

  @override
  String get icon_iqdesigner => 'IQ Designer';

  @override
  String get icon_00501 => 'Line Scan';

  @override
  String get icon_00503_zz => 'Line';

  @override
  String get icon_00503_zz_s => 'Line';

  @override
  String get icon_00505 => 'Illustration Scan';

  @override
  String get icon_imagescan => 'Image scan';

  @override
  String get icon_linedesign => 'Line design';

  @override
  String get icon_illustrationdesign => 'Illustration design';

  @override
  String get icon_00509_zz => 'Illust.';

  @override
  String get icon_00510 => 'Recognize';

  @override
  String get icon_00511_1 => 'Preview';

  @override
  String get icon_00511_2 => 'Preview';

  @override
  String get icon_showpreview => 'Show Preview';

  @override
  String get icon_00512 => 'Retry';

  @override
  String get icon_00514 => 'Ignore Object Size';

  @override
  String get icon_00516 => 'Gray-Scale Detection level';

  @override
  String get icon_00503 => 'Line';

  @override
  String get icon_00518 => 'Eraser';

  @override
  String get icon_00520 => 'Original View';

  @override
  String get icon_00521 => 'Result View';

  @override
  String get icon_00522 => 'Result View';

  @override
  String get icon_00523 => 'Max. Number of\nColors';

  @override
  String get icon_00525 => 'Remove\nBackground';

  @override
  String get icon_00526 => 'Recognize';

  @override
  String get icon_00528 => 'Embroidery Settings';

  @override
  String get icon_00529 => 'Line Property';

  @override
  String get icon_00530 => 'Region Property';

  @override
  String get icon_00533 => 'Size';

  @override
  String get icon_00537 => 'Zigzag width';

  @override
  String get icon_00538 => 'Density';

  @override
  String get icon_00539 => 'Run Pitch';

  @override
  String get icon_00540 => 'Fill Stitch';

  @override
  String get icon_00541 => 'Direction';

  @override
  String get icon_00544 => 'Pull\ncompensation';

  @override
  String get icon_00545 => 'Under sewing';

  @override
  String get icon_00547 => 'Spacing';

  @override
  String get icon_00548_1 => 'Manual';

  @override
  String get icon_00548_2 => 'Manual';

  @override
  String get icon_00549_1 => 'Auto';

  @override
  String get icon_00549_2 => 'Auto';

  @override
  String get icon_00550 => 'To stitch';

  @override
  String get icon_00551 => 'Framing the image';

  @override
  String get icon_00552 => 'Color specification';

  @override
  String get icon_00553 => 'Next';

  @override
  String get icon_00554 => 'Distance';

  @override
  String get icon_00555 => 'Saving outlines';

  @override
  String get icon_00556 => 'Closed shapes';

  @override
  String get icon_00557 => 'Open shapes';

  @override
  String get icon_00558 => 'Saved outlines';

  @override
  String get icon_00559 => 'Frame embroidering areas';

  @override
  String get icon_00562 => 'Outline';

  @override
  String get icon_00564 => 'Thickness ';

  @override
  String get icon_00565 => 'Random shift';

  @override
  String get icon_00566 => 'Position Offset';

  @override
  String get icon_inside => 'Inside';

  @override
  String get icon_outside => 'Outside';

  @override
  String get icon_00567 => 'Flip';

  @override
  String get icon_00568 => 'Stitch Width';

  @override
  String get icon_00569 => 'Current';

  @override
  String get icon_00570 => 'New';

  @override
  String get icon_frame_297_465_mm => '297 × 465 mm';

  @override
  String get icon_frame_297_465_inch => '11-5/8\"× 18-1/4\"';

  @override
  String get icon_frame_272_408_mm => '272 × 408 mm';

  @override
  String get icon_frame_272_408_inch => '10-5/8\"× 16\"';

  @override
  String get icon_frame_254_254_mm => '254 × 254 mm';

  @override
  String get icon_frame_254_254_inch => '10\"× 10\"';

  @override
  String get icon_frame_240_360_mm => '240 × 360 mm';

  @override
  String get icon_frame_240_360_inch => '9-1/2\"× 14\"';

  @override
  String get icon_frame_180_360_mm => '180 × 360 mm';

  @override
  String get icon_frame_180_360_inch => ' 7\" × 14\"';

  @override
  String get icon_frame_180_300_mm => '180 × 300 mm';

  @override
  String get icon_frame_180_300_inch => ' 7\" × 12\"';

  @override
  String get icon_frame_200_300_mm => '200 × 300 mm';

  @override
  String get icon_frame_200_300_inch => '8\"×12\"';

  @override
  String get icon_frame_100_300_mm => '100 × 300 mm';

  @override
  String get icon_frame_100_300_inch => '4\"× 12\"';

  @override
  String get icon_frame_160_260_mm => '160 × 260 mm';

  @override
  String get icon_frame_160_260_inch => '6-1/4\"× 10-1/4\"';

  @override
  String get icon_frame_240_240_mm => '240 × 240 mm';

  @override
  String get icon_frame_240_240_inch => '9-1/2\"× 9-1/2\"';

  @override
  String get icon_frame_200_200_mm => '200 × 200 mm';

  @override
  String get icon_frame_200_200_inch => '8\"× 8\"';

  @override
  String get icon_frame_130_180_mm => '130 × 180 mm';

  @override
  String get icon_frame_130_180_inch => '5\"× 7\"';

  @override
  String get icon_frame_100_180_mm => '100 × 180 mm';

  @override
  String get icon_frame_100_180_inch => '4\"× 7\"';

  @override
  String get icon_frame_150_150_mm => '150 × 150 mm';

  @override
  String get icon_frame_150_150_inch => '6\"× 6\"';

  @override
  String get icon_frame_100_100_mm => '100 × 100 mm';

  @override
  String get icon_frame_100_100_inch => '4\"× 4\"';

  @override
  String get icon_frame_60_20_mm => '60 × 20 mm';

  @override
  String get icon_frame_60_20_inch => '2-3/8\"× 3/4\"';

  @override
  String get icon_zoom_50 => '50';

  @override
  String get icon_zoom_100 => '100';

  @override
  String get icon_zoom_125 => '125';

  @override
  String get icon_zoom_150 => '150';

  @override
  String get icon_zoom_200 => '200';

  @override
  String get icon_zoom_400 => '400';

  @override
  String get icon_zoom_800 => '800';

  @override
  String get icon_zoom_120 => '120';

  @override
  String get icon_zoom_240 => '240';

  @override
  String get icon_zoom_480 => '480';

  @override
  String get icon_zoom_960 => '960';

  @override
  String get icon_00600 => 'Wireless LAN Enable';

  @override
  String get icon_00600_1 => 'WLAN Enable';

  @override
  String get icon_00601 => 'SSID';

  @override
  String get icon_00602 => 'Select SSID...';

  @override
  String get icon_00603 => 'Machine name';

  @override
  String get icon_00604 => 'WPS (Push)';

  @override
  String get icon_00605 => 'WPS (Pin)';

  @override
  String get icon_00606 => 'Others';

  @override
  String get icon_00608 => 'Wireless LAN Status';

  @override
  String get icon_00608_1 => 'WLAN Status';

  @override
  String get icon_00609 => 'Saved SSID';

  @override
  String get icon_00609_1 => 'Saved SSID';

  @override
  String get icon_00610 => 'New SSID';

  @override
  String get icon_wlan_title => 'Wireless LAN';

  @override
  String get icon_wlan_connection => 'Wireless LAN Connection';

  @override
  String get icon_wlan_networks => 'Wireless LAN Networks';

  @override
  String get icon_wlan_enable => 'Wireless LAN Enable';

  @override
  String get icon_wlan_setinfo_01 => 'To see available networks, turn on Wireless LAN Enable.';

  @override
  String get icon_wlan_setinfo_02 => 'Searching for Wireless LAN networks…';

  @override
  String get icon_wlan_setinfo_03 => 'Connecting for Wireless LAN…';

  @override
  String get icon_wlan_setinfo_05 => 'Turning on Wireless LAN…';

  @override
  String get icon_wlan_setinfo_06 => 'Wireless LAN turned on';

  @override
  String get icon_wlan_setinfo_04 => 'Turning off Wireless LAN…';

  @override
  String get icon_wlan_setinfo_07 => 'This will reset all network settings, including:·Wireless LAN';

  @override
  String get icon_wlan_networkreset => 'Network Reset';

  @override
  String get icon_wlan_limitedconnect => 'Cannot connect to the network. Please check the clock settings.';

  @override
  String get icon_00630 => 'Network';

  @override
  String get icon_00631 => 'Wireless LAN Setup Wizard';

  @override
  String get icon_00631_1 => 'Setup Wizard';

  @override
  String get icon_00632 => 'Detail';

  @override
  String get icon_00633 => 'Status';

  @override
  String get icon_00634 => 'Signal';

  @override
  String get icon_00635 => 'Comm. Mode';

  @override
  String get icon_00636 => 'Active(11b)';

  @override
  String get icon_00637 => 'Active(11g)';

  @override
  String get icon_00638 => 'Active(11n)';

  @override
  String get icon_00639 => 'Connection Fail';

  @override
  String get icon_00640 => 'Strong';

  @override
  String get icon_00641 => 'Medium';

  @override
  String get icon_00642 => 'Weak';

  @override
  String get icon_00643 => 'None';

  @override
  String get icon_00644 => 'Ad-hoc';

  @override
  String get icon_00645 => 'Infrastructure';

  @override
  String get icon_00646 => 'TCP/IP';

  @override
  String get icon_00647 => 'MAC Address';

  @override
  String get icon_00648 => 'Proxy Settings';

  @override
  String get icon_00649 => 'BOOT Method';

  @override
  String get icon_00650 => 'IP Address';

  @override
  String get icon_00651 => 'Subnet Mask';

  @override
  String get icon_00652 => 'Gateway';

  @override
  String get icon_00653 => 'Node Name';

  @override
  String get icon_00654 => 'WINS Config';

  @override
  String get icon_00655 => 'WINS Server';

  @override
  String get icon_00656 => 'DNS Server';

  @override
  String get icon_00656_p => 'DNS Server Primary';

  @override
  String get icon_00656_s => 'DNS Server Secondary';

  @override
  String get icon_00657 => 'APIPA';

  @override
  String get icon_00658 => 'Proxy Connection';

  @override
  String get icon_00659 => 'Address';

  @override
  String get icon_00660 => 'Port';

  @override
  String get icon_00661 => 'User Name';

  @override
  String get icon_00662 => 'Password';

  @override
  String get icon_00663 => 'Primary';

  @override
  String get icon_00664 => 'Secondary';

  @override
  String get icon_00665 => 'Searching SSID...';

  @override
  String get icon_00666 => 'SSID of access point';

  @override
  String get icon_00667 => 'Network Key/Password';

  @override
  String get icon_00668 => 'Yes';

  @override
  String get icon_00669 => 'No';

  @override
  String get icon_00670 => 'Auth. Select';

  @override
  String get icon_00671 => 'Open System';

  @override
  String get icon_00672 => 'Shared Key';

  @override
  String get icon_00673 => 'WPA/WPA2-PSK';

  @override
  String get icon_00674 => 'Encryption Type';

  @override
  String get icon_00674_a => 'Encryption Type (Open System)';

  @override
  String get icon_00674_c => 'Encryption Type (WPA/WPA2-PSK)';

  @override
  String get icon_00675 => 'WEP';

  @override
  String get icon_00676 => 'AES';

  @override
  String get icon_00677 => 'TKIP';

  @override
  String get icon_00678 => 'Disabled';

  @override
  String get icon_00679 => 'Static';

  @override
  String get icon_00680 => 'Auto';

  @override
  String get icon_00681 => 'WPA';

  @override
  String get icon_00682 => 'Date';

  @override
  String get icon_cert_key => 'Normal Certification';

  @override
  String get icon_cert_web => 'Online\nMachine Certification';

  @override
  String get icon_status_t => 'Status';

  @override
  String get icon_status_a1 => 'Not checked';

  @override
  String get icon_status_a2 => 'Checking';

  @override
  String get icon_status_a3 => 'Checked: Already updated';

  @override
  String get icon_status_a4 => 'New update on server';

  @override
  String get icon_status_b1 => 'Not downloaded';

  @override
  String get icon_status_b2 => 'Downloading';

  @override
  String get icon_status_b3 => 'Downloaded';

  @override
  String get icon_cancel_downloading => 'Cancel the downloading';

  @override
  String get icon_pause_downloading2 => 'Pause the downloading\nPress the Resume key to continue the download';

  @override
  String get icon_status_c1 => 'The new update is not installed yet.';

  @override
  String get icon_status_c2 => 'The new update is installed.';

  @override
  String get icon_app_dl_moniter => 'Download Monitoring App';

  @override
  String get icon_shape => 'Shape';

  @override
  String get icon_favorite => 'Favorites';

  @override
  String get icon_sash_4section => '4 sections (2×2)';

  @override
  String get icon_sash_1direction => 'One direction';

  @override
  String get icon_sash_1dtotal => 'Total pieces';

  @override
  String get icon_offset => 'Offset';

  @override
  String get icon_startpoint => 'Start Point';

  @override
  String get icon_endpoint => 'End Point';

  @override
  String get icon_embfootdwn => 'Embroidery Foot\nAuto Down';

  @override
  String get icon_frame_272_272_mm => '272 × 272 mm';

  @override
  String get icon_frame_272_272_inch => '10-5/8\"× 10-5/8\"';

  @override
  String get icon_appguide_w => 'App Guide';

  @override
  String get icon_appguide => 'App Guide';

  @override
  String get icon_mobileapp => 'Mobile App';

  @override
  String get icon_app => 'App';

  @override
  String get icon_emb1 => 'Embroidery 1';

  @override
  String get icon_emb2 => 'Embroidery 2';

  @override
  String get icon_00185_2 => 'Frame size';

  @override
  String get icon_type => 'Type';

  @override
  String get icon_typea => 'Type A';

  @override
  String get icon_typeb => 'Type B';

  @override
  String get icon_typec => 'Type C';

  @override
  String get icon_sash_typesplit => 'Type of Split';

  @override
  String get icon_mystitchmonitor => 'My Stitch Monitor';

  @override
  String get icon_mydesignsnap => 'My Design Snap';

  @override
  String get icon_mystitchmonitor_t => 'IQ Intuition Monitoring';

  @override
  String get icon_mydesignsnap_t => 'IQ Intuition Positioning';

  @override
  String get icon_actcode => 'Activation Code';

  @override
  String get icon_machineno => 'Machine Number (No.)';

  @override
  String get icon_autodl => 'Auto-Download ';

  @override
  String get icon_updatemanu => 'Update manually';

  @override
  String get icon_dl_updateprogram => 'Download the update program';

  @override
  String get icon_dl_updateprogram_2 => 'Download the update program';

  @override
  String get icon_chk_update => 'Check for updates';

  @override
  String get icon_pause => 'Pause';

  @override
  String get icon_resume => 'Resume';

  @override
  String get icon_cert_method => 'Certification method';

  @override
  String get icon_latestver => 'Latest Version';

  @override
  String get icon_latestveravail => 'Latest Version Available';

  @override
  String get icon_device_ios => 'For iOS Devices';

  @override
  String get icon_device_android => 'For Android™\nDevices';

  @override
  String get icon_f_ios => 'For iOS';

  @override
  String get icon_f_android => 'For Android™';

  @override
  String get icon_cws_myconnection => 'CanvasWorkspace\n (My Connection)';

  @override
  String get icon_step1 => 'STEP1:';

  @override
  String get icon_step2 => 'STEP2:';

  @override
  String get icon_step3 => 'STEP3:';

  @override
  String get icon_step4 => 'STEP4:';

  @override
  String get icon_step5 => 'STEP5:';

  @override
  String get icon_register => 'Register';

  @override
  String get icon_loginid => 'Login ID:';

  @override
  String get icon_id => 'ID:';

  @override
  String get icon_appq1 => 'Appliqué patch\n(Normal)';

  @override
  String get icon_appq2 => 'Appliqué patch\nfor selected colors';

  @override
  String get icon_original_img => 'Original image';

  @override
  String get icon_appq_stitch_1 => 'Zigzag stitch';

  @override
  String get icon_appq_stitch_2 => 'Satin stitch';

  @override
  String get icon_appq_stitch_3 => 'Running stitch';

  @override
  String get icon_stamp_web => 'Cutting outlines';

  @override
  String get icon_cws_rgs_title => 'Get PIN code to register your machine.';

  @override
  String get icon_cws_rgs_s1 => 'Log in to CanvasWorkspace.\nhttp://CanvasWorkspace.Brother.com';

  @override
  String get icon_cws_rgs_s2 => 'Tap [Account Settings].';

  @override
  String get icon_pincode => 'PIN code';

  @override
  String get icon_kitsnc => 'ScanNCut (My Connection)';

  @override
  String get icon_snc1 => 'ScanNCut';

  @override
  String get icon_f_om_kitsnc => 'ScanNCut (My Connection)';

  @override
  String get icon_density_mm => 'line/mm';

  @override
  String get icon_density_inch => 'line/inch';

  @override
  String get icon_machineregist => 'Machine Registration';

  @override
  String get icon_snj_myconnection => 'Artspira / My Connection';

  @override
  String get icon_snj_rgs_title => 'Get PIN code to register your machine.';

  @override
  String get icon_snj_rgs_s1_iivo1 => 'Log in to Artspira.\nhttps://s.brother/snjumq4211';

  @override
  String get icon_snj_rgs_s2 => 'Tap [Machine Settings] and tap [Register] of Embroidery Machine, then select [Wireless LAN model].';

  @override
  String get icon_snj_rgs_s3 => 'Type the following number on the Artspira, and get Pin code.';

  @override
  String get icon_snj_rgs_pin => 'Type the PIN Code on the next screen.';

  @override
  String get icon_cws_rgs_s3 => 'Tap [Machine(s) Registration] and select [Register a New Sewing Machine].';

  @override
  String get icon_cws_rgs_s4 => 'Type the following number in the web screen, and get Pin code.';

  @override
  String get icon_cws_rgs_pin => 'Type the PIN Code on the next screen.';

  @override
  String get icon_transfer => 'Transfer';

  @override
  String get icon_app_selectcolor => 'Select colors for appliqué patch';

  @override
  String get icon_texture => 'Texture';

  @override
  String get icon_userthread => 'User Thread';

  @override
  String get icon_senju => 'Artspira';

  @override
  String get icon_notnow => 'Not now';

  @override
  String get icon_builtin => 'Built-in';

  @override
  String get icon_user => 'Custom';

  @override
  String get icon_clearall => 'Clear all';

  @override
  String get icon_taperingtitle => 'Tapering ';

  @override
  String get icon_tapering01 => 'Start';

  @override
  String get icon_tapering02 => 'End';

  @override
  String get icon_tapering03 => 'Ending style';

  @override
  String get icon_tapering03_2 => 'Ending style';

  @override
  String get icon_tapering04 => 'Start Angle';

  @override
  String get icon_tapering05 => 'End Angle';

  @override
  String get icon_tapering06 => 'Pattern repetition';

  @override
  String get icon_tapering06_s => 'Repetition ';

  @override
  String get icon_times => 'times';

  @override
  String get icon_approx_s => 'Approx.';

  @override
  String get icon_e2etitle => 'Edge-To-Edge Quilt';

  @override
  String get icon_e2e01 => 'Flip option';

  @override
  String get icon_e2e01_2 => 'Flip option';

  @override
  String get icon_e2e02 => 'row(s)';

  @override
  String get icon_e2e03 => 'piece(s)';

  @override
  String get icon_sr_title => 'Stitch regulator';

  @override
  String get icon_sr_mode_title => 'Mode';

  @override
  String get icon_sr_mode_00exp => 'Step 1 - Select a mode.\nStep 2 - Select a stitch.\n  *Basting stitch is automatically selected in mode 3.\nStep 3 - Please start sewing.';

  @override
  String get icon_sr_mode01exp => 'Intermittent mode\n\nWhen there is no movement of the cloth, the needle stops at the top, and the needle drops after moving the specified length. Be careful not to put your hand under the needle.';

  @override
  String get icon_sr_mode02exp => 'Continuous mode\n\nWhen there is no movement of the cloth, the needle drops slowly in the same position for lock stitch or for shorter stitch length than specified, such as at the corners of a pattern.';

  @override
  String get icon_sr_mode03exp => 'Basting mode\n\nThe needle drops at longer intervals for basting. Be careful not to put your hand under the needle.';

  @override
  String get icon_sr_mode04exp => 'Free motion mode\n\nNeedle drops at a constant speed. The stitch length changes depending on the fabric feed speed. ';

  @override
  String get icon_sr_mem_mode01 => 'Intermittent';

  @override
  String get icon_sr_mem_mode02 => 'Continuous';

  @override
  String get icon_sr_modemem_03 => 'Basting';

  @override
  String get icon_sr_mem_mode04 => 'Free Motion';

  @override
  String get icon_sr_sensingline => 'Sensing line';

  @override
  String get icon_sr_footheight => 'SR Height';

  @override
  String get icon_unselect => 'Unselect';

  @override
  String get icon_filter => 'Filter';

  @override
  String get icon_filterapplied => 'Filter applied';

  @override
  String get icon_apply => 'Apply';

  @override
  String get icon_upperlimit => 'Upper limit';

  @override
  String get icon_lowerlimit => 'Lower limit';

  @override
  String get icon_all => 'All';

  @override
  String get icon_bh_guide01 => 'Buttonhole guide';

  @override
  String get icon_bh_guide02 => 'Array';

  @override
  String get icon_bh_guide03 => 'Spacing';

  @override
  String get icon_bh_guide04 => 'Cloth edge guide';

  @override
  String get icon_bh_guide05 => 'Distance from edge';

  @override
  String get icon_colorchanges => 'Color Changes';

  @override
  String get icon_voiceguidance_title => 'Voice Guidance';

  @override
  String get icon_voicevolume => 'Voice Volume';

  @override
  String get icon_voice_01eng_a => 'English-A';

  @override
  String get icon_voice_01eng_b => 'English-B';

  @override
  String get icon_voice_02deu_a => 'Deutsch-A';

  @override
  String get icon_voice_02deu_b => 'Deutsch-B';

  @override
  String get icon_voice_03fra_a => 'Français-A';

  @override
  String get icon_voice_03fra_b => 'Français-B';

  @override
  String get icon_voice_04ita_a => 'Italiano-A';

  @override
  String get icon_voice_04ita_b => 'Italiano-B';

  @override
  String get icon_voice_05nld_a => 'Nederlands-A';

  @override
  String get icon_voice_05nld_b => 'Nederlands-B';

  @override
  String get icon_voice_06esp_a => 'Español-A';

  @override
  String get icon_voice_06esp_b => 'Español-B';

  @override
  String get icon_voice_07jpn_a => '日本語-A';

  @override
  String get icon_voice_07jpn_b => '日本語-B';

  @override
  String get icon_embcate_photostitch => 'Picture Play\nEmbroidery';

  @override
  String get icon_photos_title => 'Picture Play Embroidery Function';

  @override
  String get icon_photos_01 => 'Select an image file (JPG, BMP, PNG).';

  @override
  String get icon_photos_02 => 'Size adjustment';

  @override
  String get icon_photos_03 => 'Background removal';

  @override
  String get icon_photos_04 => 'Framing the image';

  @override
  String get icon_photos_05 => 'Fit to frame';

  @override
  String get icon_photos_06 => 'Auto (AI)';

  @override
  String get icon_photos_07 => 'Manual';

  @override
  String get icon_photos_08 => 'Select a style to convert by AI.';

  @override
  String get icon_photos_09 => 'Color adjustment';

  @override
  String get icon_photos_10 => 'Accented Edges';

  @override
  String get icon_photos_11 => 'Brightness';

  @override
  String get icon_photos_12 => 'Contrast';

  @override
  String get icon_photos_13 => 'Saturation';

  @override
  String get icon_photos_14 => 'Importing an image file (JPG, BMP, PNG)';

  @override
  String get icon_photos_15 => 'Embroidery settings';

  @override
  String get icon_style0 => 'Original';

  @override
  String get icon_style1 => 'Style 1';

  @override
  String get icon_style2 => 'Style 2';

  @override
  String get icon_style3 => 'Style 3';

  @override
  String get icon_style4 => 'Style 4';

  @override
  String get icon_style5 => 'Style 5';

  @override
  String get icon_style6 => 'Style 6';

  @override
  String get icon_style7 => 'Style 7';

  @override
  String get icon_style8 => 'Style 8';

  @override
  String get icon_style9 => 'Style 9';

  @override
  String get icon_style10 => 'Style 10';

  @override
  String get icon_style1_name => 'Icon Art';

  @override
  String get icon_style2_name => 'Art Deco';

  @override
  String get icon_style3_name => 'Pencil Sketch';

  @override
  String get icon_style4_name => 'Oil Pastels';

  @override
  String get icon_style5_name => 'Neon Sign ';

  @override
  String get icon_style6_name => 'Art Nouveau';

  @override
  String get icon_style7_name => 'Bold & Vivid ';

  @override
  String get icon_style8_name => 'Stained Glass ';

  @override
  String get icon_style9_name => 'Geo Art';

  @override
  String get icon_style10_name => 'Cartoon Graphic';

  @override
  String get icon_stylusedit => 'Projector Edit with Stylus';

  @override
  String get icon_projectorsettings => 'Projector settings';

  @override
  String get icon_setting_srvolume => 'SR Buzzer Volume';

  @override
  String get icon_embcate_bt_01 => 'Quilt';

  @override
  String get icon_embcate_bt_02 => 'Applique';

  @override
  String get icon_embcate_bt_03 => 'Botanical';

  @override
  String get icon_embcate_bt_04 => 'Animals';

  @override
  String get icon_embcate_bt_05 => 'Letter';

  @override
  String get icon_embcate_bt_06 => 'Decoration';

  @override
  String get icon_embcate_bt_07 => 'Seasons';

  @override
  String get icon_embcate_bt_08 => '3D Lace';

  @override
  String get icon_embcate_bt_09 => 'Crochet Lace';

  @override
  String get icon_embcate_bt_10 => 'In the Hoop';

  @override
  String get icon_embcate_b_01 => 'Quilt 2';

  @override
  String get icon_embcate_b_02 => 'Anna Aldmon Quilt Designs';

  @override
  String get icon_embcate_b_03 => 'Applique 2';

  @override
  String get icon_embcate_b_04 => 'Botanical 2';

  @override
  String get icon_embcate_b_05 => 'Pierre-Joseph Redouté’s Roses';

  @override
  String get icon_embcate_b_06 => 'Zündt Designs';

  @override
  String get icon_embcate_b_07 => 'Zentangle';

  @override
  String get icon_embcate_b_08 => 'Animals 2';

  @override
  String get icon_embcate_b_09 => 'Letter 2';

  @override
  String get icon_embcate_b_10 => 'Sports';

  @override
  String get icon_embcate_b_11 => 'Marine';

  @override
  String get icon_embcate_b_12 => 'Food';

  @override
  String get icon_embcate_b_13 => 'Children';

  @override
  String get icon_embcate_b_14 => 'Decoration 2';

  @override
  String get icon_embcate_b_15 => '3D Lace 2';

  @override
  String get icon_legalinfo => 'Legal Information';

  @override
  String get icon_legal_opensource => 'Open Source Licensing Remarks';

  @override
  String get icon_legal_thirdpartysoft => 'Third-Party Software';

  @override
  String get icon_nousb => '－－－－－－';

  @override
  String get icon_randomfill => 'Random fill';

  @override
  String get icon_selarea => 'Select area';

  @override
  String get icon_maxnumber_patt => 'Min. Distance';

  @override
  String get t_err01 => 'The safety device has been activated.\nIs the thread tangled?\nIs the needle bent?';

  @override
  String get t_err02 => 'Check and rethread the upper thread.';

  @override
  String get t_err02_emb => 'Check and rethread the upper thread.\n\n* Touch the frame move key on the embroidery screen to move frame to the center.';

  @override
  String get t_err03 => 'Raise the presser foot lever.';

  @override
  String get t_err04 => 'Lower the presser foot lever.';

  @override
  String get t_err05 => 'There is no embroidery card in card slot.\nInsert an embroidery card.';

  @override
  String get t_err06 => 'This embroidery card cannot be used.\nUnusable cards include, cards licensed for sale in other countries, no embroidery pattern, etc.';

  @override
  String get t_err07 => 'No more patterns can be added to this combination.';

  @override
  String get t_err07_u => 'No more stitches can be combined.';

  @override
  String get t_err08 => 'This button does not operate when the embroidery unit is not attached. \nTurn off the power and attach the embroidery unit.';

  @override
  String get t_err09 => 'This button does not operate when the embroidery unit is attached.';

  @override
  String get t_err10 => 'This button does not operate when the embroidery unit is attached.\nTurn off the power and remove the embroidery unit.';

  @override
  String get t_err11 => 'The foot controller cannot be used when the embroidery unit is attached.\nRemove the foot controller.';

  @override
  String get t_err_corrupteddataturnoff => 'Cannot recognize the data  The data may be corrupted.\n\nPlease turn off the power and turn on again.';

  @override
  String get t_err12 => 'Data volume is too large for this pattern.';

  @override
  String get t_err13 => 'This key does not operate when the needle is down.\nRaise the needle and press the key again.';

  @override
  String get t_err15 => 'The \"Start/Stop\" button does not operate with the foot controller attached.\nRemove the foot controller.';

  @override
  String get t_err16 => 'Finish editing the pattern before sewing the pattern.';

  @override
  String get t_err16_e => 'Finish editing the patterns before embroidering.';

  @override
  String get t_err16_u => 'Finish editing the stitch data before sewing.';

  @override
  String get t_err17 => 'Raise the buttonhole lever.';

  @override
  String get t_err18 => 'Lower the buttonhole lever.';

  @override
  String get t_err19 => 'Cannot change the configuration of the characters.';

  @override
  String get t_err22 => 'Select a pattern.';

  @override
  String get t_err22_u => 'Select a stitch.';

  @override
  String get t_err23 => 'Saving…';

  @override
  String get t_err24 => 'The bobbin thread is almost empty.';

  @override
  String get t_err25 => 'The carriage of the embroidery unit will move.\nKeep your hands etc. away from the carriage.';

  @override
  String get t_err26 => 'Deleting…';

  @override
  String get t_err27 => 'Not enough available memory to save the pattern.\nDelete another pattern?';

  @override
  String get t_err27_d => 'Not enough available memory to save the data.\nDelete another data?';

  @override
  String get t_err61 => 'Not enough available memory to save the pattern.';

  @override
  String get t_err61_d => 'Not enough available memory to save the data.\nDelete some patterns or use a different media.';

  @override
  String get t_err61_dd => 'Not enough available memory to save the data.\nDelete some data or use a different media.';

  @override
  String get t_err28 => 'Retrieving the pattern.\nWait a moment.';

  @override
  String get t_err28_d => 'Retrieving the data.\nWait a moment.';

  @override
  String get t_err29 => 'OK to delete the selected pattern?';

  @override
  String get t_err65 => 'OK to delete the selected pattern?';

  @override
  String get t_err29_d => 'OK to delete the selected data files?';

  @override
  String get t_err30 => 'OK to save the current settings?';

  @override
  String get t_err33 => 'Remove the embroidery frame.';

  @override
  String get t_err34 => 'Attach the embroidery frame.';

  @override
  String get t_err36 => 'When the speed controller is set to control the zigzag stitch width, the \"Start/Stop\" button does not operate.';

  @override
  String get t_err37 => 'The bobbin winder safety device has activated.\nIs the thread tangled?';

  @override
  String get t_err38 => 'This function cannot be used while the machine is in twin needle mode.\nCancel twin needle mode and choose the function again.';

  @override
  String get t_err_twinn_10 => 'The straight needle plate cannot be used in twin needle mode.\nRemove the twin needle and cancel twin needle mode.';

  @override
  String get t_err_twinn_11 => 'Twin needle mode was canceled.\nPlease remove the twin needle.';

  @override
  String get t_err_twinn_12 => 'Please confirm the twin needle is removed.';

  @override
  String get t_err42 => 'Check the result and then press OK.';

  @override
  String get t_err48 => 'Cannot recognize the data for the selected pattern. \nThe data may be corrupted.';

  @override
  String get t_err50 => 'Set the bobbin holder to the left.';

  @override
  String get t_err53 => 'The needle is down.\nPress the needle position button to raise the needle.';

  @override
  String get t_err55 => 'Attach buttonhole foot \"A＋\".\nThe built-in camera detects buttonhole foot \"A＋\" by the mark \"A＋\" and the three dots.';

  @override
  String get t_err56 => 'Unable to recognize the button size.\nCheck that the three dots are easily visible, or input the values for the slit length and try again.';

  @override
  String get t_err63 => 'This editing function cannot be used when the pattern is out of the red outline.\nUse this function after moving the pattern.';

  @override
  String get t_err64 => 'Includes a special pattern that cannot be saved to external memory.\nSave the pattern in the machine\'s memory.';

  @override
  String get t_err69 => 'There is a pattern that cannot be saved included.';

  @override
  String get t_err76 => 'Change to a larger embroidery frame.';

  @override
  String get t_err77 => 'This pattern cannot be used with this embroidery frame.\nReplace with the appropriate frame.';

  @override
  String get t_err82 => 'OK to revert to previous color changes?';

  @override
  String get t_err83 => 'OK to overwrite the data?';

  @override
  String get t_err84 => 'OK to recall and resume previous memory?';

  @override
  String get t_err88 => 'Cannot embroider since the embroidery unit is not attached.\nTurn off the machine, and then attach the embroidery unit.';

  @override
  String get t_err89 => 'USB media is not loaded.\nLoad USB media.';

  @override
  String get t_err90 => 'This USB media cannot be used.';

  @override
  String get t_err_usb_notcompati => 'The connected USB media is not compatible with the machine.\nPlease use a different USB media.';

  @override
  String get t_err93 => 'The USB media may be damaged.\nUse a different USB media, and then try saving again.';

  @override
  String get t_err94 => 'Not enough space.\nDelete some patterns or use a different USB media.';

  @override
  String get t_err94_cmn => 'Not enough space.\nDelete some patterns or use a different media.';

  @override
  String get t_err95 => 'The USB media was changed.\nDo not change the USB media while it is being read.';

  @override
  String get t_err95_cmn => 'The media was changed.\nDo not change the media while it is being read.';

  @override
  String get t_err96 => 'The USB media is write-protected so the data cannot be saved.\nCancel the write-protection before trying to save the data.';

  @override
  String get t_err96_cmn => 'The media is write-protected, so the data cannot be saved.\nCancel the write protection before trying to save the data.';

  @override
  String get t_err97 => 'The USB media is write-protected so the data cannot be deleted.\nCancel the write-protection before trying to delete the data.';

  @override
  String get t_err97_cmn => 'The media is write-protected, so the data cannot be deleted.\nCancel the write protection before trying to delete the data.';

  @override
  String get t_err98 => 'USB media error';

  @override
  String get t_err98_cmn => 'Media error';

  @override
  String get t_err99 => 'The USB media cannot be read.\nThe USB media may be damaged.';

  @override
  String get t_err100 => 'Formatting the USB media';

  @override
  String get t_err101 => 'Transmitting by USB';

  @override
  String get t_err101_cmn => 'Accessing media';

  @override
  String get t_err103 => 'Please wait a while.';

  @override
  String get t_err104 => 'Cannot operate when patterns extend from the blue frame.';

  @override
  String get t_err106 => 'Embroider the next segment?';

  @override
  String get t_err107 => 'Finished embroidering.';

  @override
  String get t_err108 => 'The pockets are full.';

  @override
  String get t_err109 => 'Use the presser foot lifter button to raise the presser foot.';

  @override
  String get t_err110 => 'Use the presser foot lifter button to lower the presser foot.';

  @override
  String get t_err111 => 'Not correctly threaded.\nPress the automatic threading button again.';

  @override
  String get t_err113 => 'The program will be upgraded.\nLoad the program onto the machine by USB.';

  @override
  String get t_err116 => 'Data error';

  @override
  String get t_err117 => 'Flash ROM error';

  @override
  String get t_err118 => 'A malfunction occurred.\nTurn the machine off, then on again.';

  @override
  String get t_err119 => 'Turn off the machine before attaching or removing the needle plate.';

  @override
  String get t_err120 => 'OK to move the embroidery carriage to its previous position?';

  @override
  String get t_err120_2 => 'Remove the embroidery frame and replace the bobbin.\nNext, attach the frame and touch OK to move it to the previous position.';

  @override
  String get t_err121 => 'OK to separate the combined border pattern?';

  @override
  String get t_err122 => 'This USB media is incompatible.';

  @override
  String get t_err122_cmn => 'This media is incompatible.';

  @override
  String get t_err123 => 'The USB media is removed or the connector is unplugged.';

  @override
  String get t_err124 => 'A malfunction occurred.\nTurn the machine off, then on again.';

  @override
  String get t_err125 => 'The upper thread may not be threaded correctly.\nThread the upper thread from the beginning.';

  @override
  String get t_err126 => 'OK to automatically lower the presser foot?';

  @override
  String get t_err127 => 'In twin needle mode, the automatic needle threading button cannot be used.';

  @override
  String get t_err128 => 'Make sure embroidery frame is moved as far back as possible.\nLOCK DOWN THE FRAME-SECURING LEVER.';

  @override
  String get t_err129 => 'Set the bobbin holder to the right.';

  @override
  String get t_err130 => 'The presser foot will move up and down.\nKeep your hands, etc. away from the presser foot.';

  @override
  String get t_err131 => 'This pattern cannot be used.';

  @override
  String get t_err132 => 'Before embroidering, make sure embroidery frame is moved as far back as possible and the frame-securing lever is locked down.\nThen, press \"Start/Stop\" button to embroider.';

  @override
  String get t_err133 => 'This embroidery frame cannot be used.';

  @override
  String get t_err134 => 'This pattern cannot be used since it exceeds the data capacity.';

  @override
  String get t_err136 => 'Preventive maintenance is recommended.';

  @override
  String get t_err137 => 'Preventive maintenance is recommended.\n1000 hours exceeded.';

  @override
  String get t_err208 => 'Calculating… ';

  @override
  String get t_err209 => 'The carriage of the embroidery unit will move.';

  @override
  String get t_err210 => 'Recognizing… ';

  @override
  String get t_err213 => 'Cannot recognize the embroidery positioning mark.';

  @override
  String get t_err215 => 'Remove the embroidery positioning mark.';

  @override
  String get t_err224 => 'There is dust or stain on the white paper or fabric.\nReplace with a clean white paper or fabric.';

  @override
  String get t_err227 => 'Cannot set or recognize correctly.';

  @override
  String get t_err228 => 'This file exceeds the data capacity and cannot be used.\nUse a suitable size file.';

  @override
  String get t_err229 => 'This file cannot be used.';

  @override
  String get t_err229_b => 'This file version cannot be read.';

  @override
  String get t_err239 => 'Connected to PC.\nDo not disconnect the USB cable.';

  @override
  String get t_err241 => 'Cannot read file.';

  @override
  String get t_err242 => 'Failed to save file.';

  @override
  String get t_err244 => 'OK to delete the selected picture?';

  @override
  String get t_err245 => 'This key cannot be used at this time.';

  @override
  String get t_err246 => 'The pattern extends out of the pattern area.\nChange position of the pattern and scan the new area.';

  @override
  String get t_err247 => 'Not enough available memory to save.';

  @override
  String get t_err248 => 'OK to delete the setting?';

  @override
  String get t_err249 => 'Cannot recognize the fabric edge.';

  @override
  String get t_err250 => 'This pattern cannot be converted.';

  @override
  String get t_err251 => 'OK to reset embroidery size and position?';

  @override
  String get t_err252 => 'OK to reset embroidery size?';

  @override
  String get t_err253 => 'Detect fabric thickness.\nAffix positioning sticker in red line.';

  @override
  String get t_err254 => 'Detection success.\nRemove the embroidery positioning mark.\nPress OK key to start background capture.';

  @override
  String get t_err255 => 'Press OK key, then the embroidery frame will move and start background capture.';

  @override
  String get t_err256 => 'Detection failure.\nDo you do it again?';

  @override
  String get t_err257 => 'Certification was successful.\nPlease restart the sewing machine.';

  @override
  String get t_err257_1 => 'Certification was successful.\nPlease restart the sewing machine.';

  @override
  String get t_err257_2 => 'Certification was successful.\nPlease restart the machine.';

  @override
  String get t_err259 => 'Certifying upgrade kit.\n\nPress kit number to certify.';

  @override
  String get t_err260 => 'Enter the certification key number and then press [SET].';

  @override
  String get t_err261 => 'Certificating…';

  @override
  String get t_err262 => 'The certification key is incorrect.\nCheck the key, and then type it in again.';

  @override
  String get t_err263 => 'KIT';

  @override
  String get t_err264 => 'OK to delete the background image?';

  @override
  String get t_err265 => 'OK to reset the pattern to its original size, angle and position?';

  @override
  String get t_err343 => 'OK to revert to the original position and/or angle?';

  @override
  String get t_err266 => 'Certificate upgrade kit.';

  @override
  String get t_err270 => 'Affix the first embroidery positioning mark on the material securely, so that the mark is inside of the red frame.\nThe carriage of embroidery unit will move after pressing the Scan key.';

  @override
  String get t_err271 => 'Affix the second embroidery positioning mark on the material securely, so that the mark is inside of the red frame.\nThe carriage of embroidery unit will move after pressing the Scan key.';

  @override
  String get t_err273 => 'The embroidery positioning mark is not affixed properly.\nPlease remove and reaffix the embroidery positioning mark.';

  @override
  String get t_err274 => 'The positioning marks are recognized.\nLeave the embroidery positioning marks attached and rehoop the material.\nThe centers of the positioning marks should be in the embroidery area and then select a pattern.';

  @override
  String get t_err276 => 'The embroidery positioning marks are recognized.\nPlease remove the embroidery positioning marks.';

  @override
  String get t_err277 => 'Are you sure you want  to “Cancel\" pattern connection?';

  @override
  String get t_err278 => 'Next section pattern cannot be embroidered after exiting. Are you sure you want to end pattern connection?';

  @override
  String get t_err279 => 'Embroidery is finished.\nOK to connect next pattern?\n\n* Do not remove the material from the frame.\n* If you wish to continue later, select and set the next section of the pattern. It can be resumed if the machine has read it. ';

  @override
  String get t_err282 => 'No more can be entered.';

  @override
  String get t_err283 => 'This application will close.';

  @override
  String get t_err284 => 'This data is too complicated and cannot be converted.';

  @override
  String get t_err286 => 'OK to finish editing the line?';

  @override
  String get t_err288 => 'The embroidery positioning marks are recognized.\nPlease remove and reaffix the embroidery positioning marks to new positions.';

  @override
  String get t_err290 => 'Cannot recognize the embroidery positioning marks.\nPlease remove and reaffix the embroidery positioning marks.\nThe centers of the embroidery positioning marks should be in the embroidery area.';

  @override
  String get t_err291 => 'Not enough space.\nUse a different USB media.';

  @override
  String get t_err291_cmn => 'Not enough space.\nUse a different media.';

  @override
  String get t_err292 => 'There are not enough colors in the thread table, for the selected mode.';

  @override
  String get t_err292_s => 'There are not enough colors in the current palette for the selected mode.';

  @override
  String get t_err297 => 'Repeat process from step 3 through 4.';

  @override
  String get t_err298 => 'The sticker is dirty.';

  @override
  String get t_err_cameracalib_ng_msg => 'Cannot recognize correctly.\nAffix a new white sticker.';

  @override
  String get t_err_cameracalib_ok_msg => 'Press OK key to memorize the needle drop position.';

  @override
  String get t_err299 => 'If after multiple trials setting is not successful, please contact your nearest retailer.';

  @override
  String get t_err300 => 'If necessary, please refer to instruction manual and reference guide for recommended needle.';

  @override
  String get t_err_cameracalib_title => 'Needle Calibration for Camera/Projector';

  @override
  String get t_err_cameracalib_1_4 => '1. Press the needle position button to raise the needle.\n\n2. After removing the needle and the presser foot, affix white sticker on needle dropping point area.\n\n3. Insert the needle (standard size 75/11 or 90/14).\n\n4. Press START key to initiate the calibration process.\nFor safety please make sure area around needle is clear prior to pressing \"START\" key.\n\n* Be sure to keep your hands and other items away\n from the needle, otherwise injuries may occur.';

  @override
  String get t_err303 => 'Embroidery is finished.\nOK to connect next pattern?';

  @override
  String get t_err304 => 'Do not remove the material from the frame.\nPress OK key to select the next pattern.';

  @override
  String get t_err307 => 'Do not remove the embroidery positioning marks.\nPlease rehoop the material so that the next pattern and the centers of the two marks are in the embroidery area.';

  @override
  String get t_err308 => 'The next pattern is out of the embroidery area.\nPlease rehoop the material, so that the next pattern and the centers of the two marks are in the embroidery area.';

  @override
  String get t_err309 => 'Cannot recognize the embroidery positioning marks.\nPlease rehoop the material so that the next pattern and the centers of the two marks are in the embroidery area. ';

  @override
  String get t_err310 => 'The position of the embroidery positioning marks was changed.\nPlease rehoop the material so the next pattern and the centers of the two marks are in the embroidery area.';

  @override
  String get t_err311 => 'The embroidery positioning marks are recognized.\nRemove the embroidery positioning marks, and embroider the pattern. ';

  @override
  String get t_err311_size => 'The embroidery positioning marks are recognized.\nRemove the embroidery positioning marks, and embroider the pattern. \n\n* The size of the next pattern has been automatically fine-tuned, since the distance between the marks changed during rehooping.';

  @override
  String get t_err311_rehoop => 'The distance between the marks are out of position due to the rehoop.\nPlease rehoop so that the distance between the marks is of the following length.';

  @override
  String get t_err312 => 'Do not remove the embroidery positioning marks.\nNeed to reaffix the embroidery positioning marks.\nPlease rehoop the material. ';

  @override
  String get t_err313 => 'Cannot recognize the embroidery positioning marks.\nPlease rehoop the material.';

  @override
  String get t_err314 => 'The embroidery positioning marks are recognized.\nRemove the embroidery positioning marks.';

  @override
  String get t_err354 => 'The shutoff support mode has been activated.\nTurn off the machine.';

  @override
  String get t_err356 => 'This stitch is not \"Dual Feed Mode\" compatible.';

  @override
  String get t_err359 => 'Remove Dual Feed module from the machine.';

  @override
  String get t_err360 => 'Set the clock.';

  @override
  String get t_err361 => 'Select your language.';

  @override
  String get t_err362 => 'Remove Embroidery foot with LED pointer from the machine.';

  @override
  String get t_err364 => 'Module error';

  @override
  String get t_err368 => 'OK to reset the pattern\'s border setting, position and/or angle?';

  @override
  String get t_err373 => 'The embroidery frame has been changed, replace it with the original frame.';

  @override
  String get t_err380 => 'To thread the needle, please remove the fabric from under the presser foot.';

  @override
  String get t_err381 => 'OK to cancel the end point setting?';

  @override
  String get t_err382 => 'The adjustment mode of the sewing end point setting is not available with the selected stitch.\nSelect a different stitch, or change the length of the stitch.';

  @override
  String get t_err383 => 'After removing the end point sticker, continue sewing.';

  @override
  String get t_err384 => 'The adjustment mode of the stitch end setting cannot be used at this time.\nThe end point setting will be canceled.';

  @override
  String get t_err385 => 'This distance is too short to use the end point setting.\nYou can use it if the distance is longer or “Temporary Stop” setting is set to OFF.';

  @override
  String get t_err386 => 'This function cannot be used while the End Point Setting is ON.';

  @override
  String get t_err390 => 'OK to clear all editing data and move to home screen?';

  @override
  String get t_err390_old => 'OK to delete all patterns and move to home screen?';

  @override
  String get t_err391 => 'OK to cancel the current pattern selection?';

  @override
  String get t_err391_u => 'OK to cancel the current stitch selection?';

  @override
  String get t_err392 => 'Transmitting by embroidery card.';

  @override
  String get t_err393 => 'This pattern cannot be combined.';

  @override
  String get t_err394 => 'Not enough available memory to save the pattern. \nDelete a saved pattern?';

  @override
  String get t_err395 => 'This pattern cannot be loaded as the pattern extends out of the editable area.';

  @override
  String get t_err396 => 'This pattern cannot be loaded as the pattern extends out of the editable area. \nUse cursors to move the point to fit into the editable area. ';

  @override
  String get t_err397 => 'OK to delete the current stitch?';

  @override
  String get t_err398 => 'Save as a new file…';

  @override
  String get t_err400 => 'Pattern extends to the outside of embroidery frame.\nIf you plan to add more patterns, rotate the pattern combination.';

  @override
  String get t_err401 => 'Pattern extends to the outside of embroidery frame.';

  @override
  String get t_err402 => 'Pattern extends to the outside of embroidery frame.\nAdd no additional characters.';

  @override
  String get t_err403 => 'Pattern extends to the outside of embroidery frame.\nThis function cannot be used at this time.';

  @override
  String get t_err404 => 'Cannot change the font since some letters are not included to the selected font.';

  @override
  String get t_err405 => 'Selected pattern field extends to the outside of the embroidery frame.';

  @override
  String get t_err406 => 'Pattern extends to the outside of the selected embroidery frame. \nOK to cancel the selected pattern?';

  @override
  String get t_err408 => 'This function cannot be used when patterns are overlapping.';

  @override
  String get t_err410 => 'The pattern can be embroidered with a center or corner aligned with the embroidery positioning mark. Affix the embroidery positioning mark at the desired position.';

  @override
  String get t_err411 => 'After completing the necessary preparations, press [Scan] key.';

  @override
  String get t_err412 => 'The embroidery positioning mark could not be found in the detection area.';

  @override
  String get t_err413 => 'Use the embroidery positioning mark to connect patterns.';

  @override
  String get t_err414 => 'Select the position where the next pattern\nwill be connected.';

  @override
  String get t_err415 => 'The data cannot be read.';

  @override
  String get t_err416 => 'The data was saved.\nFile name: ';

  @override
  String get t_err417 => 'The data is being read.\nPlease wait.';

  @override
  String get t_err418 => 'This file type cannot be used.';

  @override
  String get t_err419 => 'Since this file is too large, it cannot be used.';

  @override
  String get t_err420 => 'Failed to trace the image.';

  @override
  String get t_err421 => 'The number of colors in an image will be reduced to the number specified here, and then the outline will be extracted.';

  @override
  String get t_err422 => 'Place the paper with the illustration or line drawing into the scanning frame securing it with the magnets.';

  @override
  String get t_err423 => 'An embroidery frame cannot be used. Be sure to use the scanning frame.';

  @override
  String get t_err424 => 'It may take a few minutes to finish detecting.';

  @override
  String get t_err425 => 'OK to continue to \"My Design Center\" screen?';

  @override
  String get t_err426 => 'OK to continue to \"IQ Designer\" screen?';

  @override
  String get t_err428 => 'The \"My Design Center\" data will not be saved. OK to continue?';

  @override
  String get t_err429 => 'The \"IQ Designer\" data will not be saved. OK to continue?';

  @override
  String get t_err430 => 'The scanning frame cannot be used for embroidering.\nReplace it with the embroidery frame.';

  @override
  String get t_err432 => 'Attach the frame containing the image to be scanned to the machine.';

  @override
  String get t_err433 => 'To convert an image to a line or fill image, \nscan the design with the Scanning Frame\nso that your machine can retrieve appropriate\ncolor information.';

  @override
  String get t_err440 => 'Select the image file.';

  @override
  String get t_err445 => 'This image file cannot be used.';

  @override
  String get t_err446 => 'Scanning…';

  @override
  String get t_err447 => 'Recognizing…';

  @override
  String get t_err448 => 'Processing…';

  @override
  String get t_err451 => 'OK to erase all the edited image data?';

  @override
  String get t_err452 => 'The image files that can be used are JPG, PNG or BMP files of less than 5MB, 1.2 million pixels.';

  @override
  String get t_err453 => 'OK to restore the default settings on this page?';

  @override
  String get t_err454 => 'The pattern combination is too large for the identified embroidery frame. If you plan to add more patterns, rotate the pattern combination.';

  @override
  String get t_err455 => 'The pattern combination is too large for the identified embroidery frame.';

  @override
  String get t_err457 => 'Set the Embroidery frame identification view to OFF.';

  @override
  String get t_err458 => 'Importing an image file.';

  @override
  String get t_err459 => 'This embroidery unit cannot be used.';

  @override
  String get t_err460 => 'The patterns displayed in the image area can be converted.';

  @override
  String get t_err461 => 'The frame will move to be scanned with the built-in camera.';

  @override
  String get t_err462_pp => 'This image file cannot be used.\nThe image files that can be used are JPG, PNG or BMP files of less than 6MB and within 16 million pixels.';

  @override
  String get t_err463 => 'Remove the embroidery frame or scanning frame.';

  @override
  String get t_err464 => 'Pattern extends to the outside of embroidery area, and cannot be converted.';

  @override
  String get t_err465 => 'Converted to the embroidery pattern, and My Design Center will be exited.\nOK to continue to embroidery edit screen?';

  @override
  String get t_err466 => 'OK to exit My Design Center?';

  @override
  String get t_err467 => 'This function cannot be used with the connect patterns mode.';

  @override
  String get t_err468 => 'Machine PCB power off';

  @override
  String get t_err469 => 'Converted to the embroidery pattern, and IQ Designer will be exited.\nOK to continue to embroidery edit screen?';

  @override
  String get t_err470 => 'OK to exit IQ Designer?';

  @override
  String get t_err471 => 'OK to delete the selected data?';

  @override
  String get t_err472 => 'Select multiple patterns.';

  @override
  String get t_err473 => 'Saved the arranged pattern(s) as image(s).';

  @override
  String get t_err474 => 'Saved the outline of the arranged pattern(s).';

  @override
  String get t_err475 => 'Specify offset distance around pattern.';

  @override
  String get t_err478 => 'Recall from My Design Center stamp pattern list.';

  @override
  String get t_err478_tc => 'Recall from IQ Designer stamp pattern list.';

  @override
  String get t_err479 => 'A bobbin work pattern cannot be combined with a pattern from a different category.';

  @override
  String get t_err480 => 'Moves to the first needle position.';

  @override
  String get t_err481 => 'Finished the sewing of one bobbin work pattern.';

  @override
  String get t_err482 => 'Finished the sewing of all bobbin work patterns.';

  @override
  String get t_err483 => 'Cut the threads.';

  @override
  String get t_err484_old => 'Before embroidering the following patterns, check the amount and type of bobbin thread installed.';

  @override
  String get t_err484 => 'Replace the bobbin thread, and then attach the embroidery frame.\nThe carriage of the embroidery unit will move after pressing OK.';

  @override
  String get t_err485 => 'The following bobbin work pattern will be embroidered.';

  @override
  String get t_err486 => 'Turn the handwheel to lower the needle into the fabric, and then pull up the bobbin thread.';

  @override
  String get t_err489 => 'No data to be converted.';

  @override
  String get t_err496 => 'OK to apply the setting to all the areas?';

  @override
  String get t_err497 => 'Outline';

  @override
  String get t_err501 => 'Settings cannot be applied.\nNot enough available memory to save the attributes.';

  @override
  String get t_err502 => 'Too long distance between selected patterns.';

  @override
  String get t_err503 => 'Too complex shape for applique line.';

  @override
  String get t_err503_new => 'Too complex or unsuitable shape for appliqué line.\nPlease try to change the appliqué settings, or select a different pattern.\n* The result may differ depending on the position and angle.';

  @override
  String get t_err504 => 'Too big size to add applique line.';

  @override
  String get t_err509 => 'A part of the texture cannot be displayed due to data structure.';

  @override
  String get t_err505 => 'This function cannot be used when color sorting is performed.';

  @override
  String get t_err506 => 'OK to delete the thread mark?';

  @override
  String get t_err508 => 'Thread mark function cannot be used when deselecting the last color region.\nOk to delete the thread mark?';

  @override
  String get t_err507 => 'Auto';

  @override
  String get t_err510 => 'Use the current image?';

  @override
  String get t_err511 => 'The data was saved in the machine\'s memory.\nEmbroider the data?';

  @override
  String get t_err515 => 'Turn off the machine to cool the built-in projecter.';

  @override
  String get t_err516 => 'This stitch can not be used with the current needle plate.';

  @override
  String get t_err517 => 'Change the needle plate to sew this stitch.';

  @override
  String get t_err518 => 'The embroidery frame has been changed.\nThe carriage of the embroidery unit will move.';

  @override
  String get t_err519 => 'The projector will be turned off.';

  @override
  String get t_err520 => 'Keep your hands etc. away from the carriage.';

  @override
  String get t_err_521 => 'Tutorial videos can be downloaded.';

  @override
  String get t_err574 => 'A malfunction occurred.\nEEPROM cannot be accessed.';

  @override
  String get t_err575 => 'The bobbin thread is almost empty.\n\n* Use the “Reinforcement Stitch” button to sew a single stitch repeatedly and tie off the stitching.\n* Use the move frame key to move the embroidery carriage so the embroidery frame can be removed or installed. Afterward, the carriage moves back to its previous position.';

  @override
  String get t_err577 => 'A couching pattern cannot be combined with a pattern from a different category.';

  @override
  String get t_err578 => 'No more color scheme can be selected as favorites.';

  @override
  String get t_err581 => 'Begin sewing from the upper-right corner of the fabric.\nAttach the embroidery frame in the initial sewing position.';

  @override
  String get t_err581_b => 'Begin sewing from the upper-left corner of the fabric.\nAttach the embroidery frame in the initial sewing position.';

  @override
  String get t_err582 => 'One side was sewn. Turn the fabric 90° counterclockwise, and then rehoop it in the embroidery frame.';

  @override
  String get t_err582_n => 'One side was sewn.  Turn the fabric counterclockwise and rehoop the fabric for the next corner.';

  @override
  String get t_err582_e => 'One row is finished. To start with the next row, rehoop the fabric to the left edge of the next row, while including the thread mark of the upper pattern in the hoop.';

  @override
  String get t_err583 => 'Use the move pattern keys to adjust the inside corner of the pattern.';

  @override
  String get t_err583_e => 'Use the move pattern keys to align the upper left corner of the pattern area with the upper left corner (marking) of the area to be embroidered.';

  @override
  String get t_err584 => 'Use the move pattern keys to align the start point with the end point of the previous pattern.';

  @override
  String get t_err584_e => 'Use the move pattern keys to align the upper left corner of the pattern area with the lower left thread mark of the upper pattern.';

  @override
  String get t_err585 => 'Use the rotate keys to adjust the angle of the pattern while keeping an eye on the points around the pattern.';

  @override
  String get t_err586 => 'Adjust the size of the pattern so that the lower-left point will be the inside corner of the next pattern.';

  @override
  String get t_err586_b => 'Adjust the size of the pattern so that the lower-right point will be the inside corner of the next pattern.';

  @override
  String get t_err586_e => 'Use the rotate keys and the size keys to adjust the angle and the size of the pattern while keeping an eye on the edges of the pattern.';

  @override
  String get t_err587 => 'Use the rotate keys and size keys to align the end point with the start point of the first pattern.';

  @override
  String get t_err588 => 'Please rehoop the material. ';

  @override
  String get t_err588_e => 'Please rehoop the fabric to the right side, while including the right edge of the pattern on the left in the hoop.';

  @override
  String get t_err588_e_2 => 'Rehoop towards the right, while including the right edge of the pattern on the right and the thread mark of the pattern above. ';

  @override
  String get t_err590 => 'Press LOAD to install the update file.';

  @override
  String get t_err591 => 'There is a new update available. To install the update, turn off the machine, and then hold down the “Automatic Threading” button and turn the machine on again.';

  @override
  String get t_err_dl_updateprogram2 => 'The new update program is ready.\nTo install the update, turn off the machine, and then hold down the “Automatic Threading” button and turn the machine on again.';

  @override
  String get t_err592 => 'Press LOAD to save the update file.';

  @override
  String get t_err593 => 'Select the device where the update file will be saved.';

  @override
  String get t_err594 => 'Select the device where the update file is saved.';

  @override
  String get t_err_dl_updateprogram => 'Press the Start key to download the update program.';

  @override
  String get t_err_dl_fail => 'Download failed: Internal storage is full.';

  @override
  String get t_err_networkconnectionerr => 'Lost the network connection.\nPlease make sure the machine are connected to a wireless network.';

  @override
  String get t_err_not_turnoff => 'Do not turn off the machine.';

  @override
  String get t_err_pressresume_continuedl => 'Press the Resume key to continue the download.';

  @override
  String get t_err_updateformovie => 'Please update again to install the movies.';

  @override
  String get t_err595 => 'Type in the 16-digit activation code.';

  @override
  String get t_err596 => 'Type in the 16-digit activation code, and then press [Set] key.';

  @override
  String get t_err597 => 'The machine number and activation code will be sent to the server.';

  @override
  String get t_err598 => '\"Online Machine Certification\"  is recommended for the machine connected to wireless LAN.';

  @override
  String get t_err599 => 'The activation code is incorrect.\nCheck the key, and then type it in again.';

  @override
  String get t_err599_used => 'The entered code has already been registered with a different machine.';

  @override
  String get t_err601 => 'Enable wireless LAN?';

  @override
  String get t_err602 => 'Searching SSID…';

  @override
  String get t_err603 => 'Apply Settings?';

  @override
  String get t_err604 => 'Connected to wireless LAN.';

  @override
  String get t_err605 => 'Connecting to wireless LAN.';

  @override
  String get t_err606 => 'Unconnected to the network.\nCheck the wireless LAN setting.';

  @override
  String get t_err607 => 'Unconnected to the network.\nCheck the network connection status.';

  @override
  String get t_err608 => 'Unconnected to the internet server.\nCheck the proxy setting.';

  @override
  String get t_err609 => 'A network error occurred.';

  @override
  String get t_err611 => 'Disabled';

  @override
  String get t_err612 => 'Errors occurred in network function.';

  @override
  String get t_err613 => 'The data could not be imported.\nStart over again.\n';

  @override
  String get t_err614 => 'There is a saved Access Point information.\nOK to connect with this information?';

  @override
  String get t_err615 => 'Connection Error 01';

  @override
  String get t_err616 => 'Wrong network key/password.';

  @override
  String get t_err617 => 'Wrong Network Key.';

  @override
  String get t_err618 => 'Reset Network?';

  @override
  String get t_err620 => 'Turn the machine off, then on again.';

  @override
  String get t_err621 => 'Authentication failed on connection to server.\nCheck proxy server settings.';

  @override
  String get t_err622 => 'Authentication failed.\nConfirm the Username or Password.';

  @override
  String get t_err623 => 'Cancelling';

  @override
  String get t_err624 => 'Communication Error';

  @override
  String get t_err625 => 'Completed';

  @override
  String get t_err626 => 'Confirm I/F';

  @override
  String get t_err627 => 'Connect error';

  @override
  String get t_err628 => 'Connection failed to server.\nCheck network settings.';

  @override
  String get t_err629 => 'Connection failed to server.\nTry again later.';

  @override
  String get t_err630 => 'Downloading.\nPlease wait.';

  @override
  String get t_err631 => 'Error was occurred while downloading.\nStart over again.';

  @override
  String get t_err632 => 'No Access Point.';

  @override
  String get t_err633 => 'No Data !';

  @override
  String get t_err634 => 'See Troubleshooting chapter in User\'s Guide.';

  @override
  String get t_err636 => 'Server cannot be found, please check the name or address or enter another LDAP server.';

  @override
  String get t_err637 => 'Server timed out.\nTry again later.';

  @override
  String get t_err638 => 'Transferring…';

  @override
  String get t_err697 => 'No data transfer available via USB cable.';

  @override
  String get t_err84_mdc => 'OK to recall and resume previous memory?\n(My Design Center).';

  @override
  String get t_err84_iqd => 'OK to recall and resume previous memory?\n(IQ Designer).';

  @override
  String get t_err703_b => 'Install \"My Stitch Monitor\" to monitor embroidering.';

  @override
  String get t_err703_t => 'Install \"IQ Intuition Monitoring\" to monitor embroidering.';

  @override
  String get t_err704_b => 'Install \"My Stitch Monitor\" app to monitor embroidering on your smart device.\n\nYou can monitor the progress status of your embroidery on your smart device.\nYou can also check all thread color information used for embroidering.';

  @override
  String get t_err704_t => 'Install \"IQ Intuition Monitoring\" app to monitor embroidering on your smart device.\n\nYou can monitor the progress status of your embroidery on your smart device.\nYou can also check all thread color information used for embroidering.';

  @override
  String get t_err705_b => 'Install \"My Design Snap\" app, to send images from your smart device to your machine. \n\nYou can easily create embroidery patterns from the images, in My Design Center.  ';

  @override
  String get t_err705_t => 'Install \"IQ Intuition Positioning\" app, to send images from your smart device to your machine. \n\nYou can easily create embroidery patterns from the images, in \"IQ Designer\".  ';

  @override
  String get t_err708 => 'KIT1 is required to use this app.';

  @override
  String get t_err709 => 'KIT2 is required to use this app.';

  @override
  String get t_err711 => 'This embroidery data does not include enough thread information.\nMachine will display approximate thread information.\nTo display correct thread information, please enter the thread color number on the thread color changing screen.';

  @override
  String get t_err713 => 'Please read the end-user license agreements (EULA)  before use.';

  @override
  String get t_err715 => 'I accept the terms in the license agreement.';

  @override
  String get t_err01_heated => 'The safety device was activated since the main shaft motor was heated. Is the thread tangled?';

  @override
  String get t_err01_motor => 'The safety device was activated since the main shaft motor seized. Is the thread tangled?';

  @override
  String get t_err01_npsensor => 'The safety device was activated since the needle position sensor malfunctioned.';

  @override
  String get t_err734 => 'For iOS';

  @override
  String get t_err735 => 'For Android™';

  @override
  String get t_err738 => 'The embroidering order will be changed.';

  @override
  String get t_err739 => 'This function cannot be used when selecting a special pattern.';

  @override
  String get t_err740 => 'The stitch will overlap with the stitches on the other side. Please decrease the stitch width or increase the offset distance.';

  @override
  String get t_err741 => 'The data has been shrunk to a compatible size. ';

  @override
  String get t_err742 => 'Do you wish to go to settings to connect your machine to a Wireless network?';

  @override
  String get t_err743 => 'Cannot operate this function as the CanvasWorkspace services are currently down for maintenance. Please wait until the restoration is complete.';

  @override
  String get t_err743_s => 'Cannot operate this function as the Artspira services are currently down for maintenance. Please wait until the restoration is complete.';

  @override
  String get t_err744 => 'Your machine could not be registered on CanvasWorkspace.\nStart over again.';

  @override
  String get t_err744_s => 'Your machine could not be registered on Artspira.\nStart over again.';

  @override
  String get t_err745 => 'The Pincode is not correct. Please re-enter the Pincode.';

  @override
  String get t_err746 => 'Your machine is not connected to the internet.';

  @override
  String get t_err747 => 'Unconnected to the internet server.\nCheck the proxy setting.';

  @override
  String get t_err748 => 'Server timed out.\nTry again later.';

  @override
  String get t_err749 => 'Do you wish to disconnect from CanvasWorkspace?';

  @override
  String get t_err749_s => 'Do you wish to disconnect from Artspira?';

  @override
  String get t_err750 => 'Your machine could not be disconnected from CanvasWorkspace. Please disconnect manually from the webpage.';

  @override
  String get t_err750_s => 'Your machine could not be disconnected from Artspira.\nPlease disconnect manually from the app.';

  @override
  String get t_err751 => 'Your sewing machine needs to be registered on CanvasWorkspace to send data (PIN registration).\nDo you wish to go to the settings screen?';

  @override
  String get t_err751_s => 'Your sewing machine needs to be registered on Artspira to send data (PIN registration).\nDo you wish to go to the settings screen?';

  @override
  String get t_err752 => 'Your machine may not be registered. Please check on CanvasWorkspace.';

  @override
  String get t_err752_s => 'Your machine may not be registered. Please check on Artspira.';

  @override
  String get t_err753 => 'The data could not be uploaded.\nStart over again.';

  @override
  String get t_err754 => 'The data could not be downloaded.\nStart over again.';

  @override
  String get t_err755 => 'Certification was successful.\nPlease restart the sewing machine.\n\nTo send data to your ScanNCut machine, restart your machine and register your machine on CanvasWorkspace server from pg.13 of Settings.';

  @override
  String get t_err755_s => 'Certification was successful.\nPlease restart the sewing machine.';

  @override
  String get t_err756 => 'Do you wish to replace the existing data with new data?\n* The data in the Temporary Data Pocket will be automatically deleted after a certain period of time.';

  @override
  String get t_err757 => 'Do you wish to send data to the Temporary data pocket on the server?\n* The data in the Temporary Data Pocket will be automatically deleted after a certain period of time.';

  @override
  String get t_err761 => 'No data in the temporary data pocket.';

  @override
  String get t_err762 => 'There was no readable data in the Temporary data pocket.';

  @override
  String get t_err763 => 'Your ScanNCut and sewing machine needs to be registered on CanvasWorkspace to receive cutting data (PIN registration).\nDo you wish to go to the settings screen?';

  @override
  String get t_err763_s => 'Please register your sewing machine to receive data using Artspira．(PIN code registration) \nDo you wish to go to the settings screen to register?';

  @override
  String get t_err764 => 'Install \"Artspira\" on your smart device. \nYour world of embroidery will expand by creating your Artspira account. \nScan the QR code for details. ';

  @override
  String get t_err765 => 'The machine name could not be changed.';

  @override
  String get t_err766 => 'Enable the wireless LAN to change the machine name.';

  @override
  String get t_err770 => 'Failed to delete.';

  @override
  String get t_err771 => 'If you delete all custom patterns, the patterns being used for editing, will be replaced by another pattern.\nOK to delete all custom patterns?';

  @override
  String get t_err772 => 'If you delete or change the imported custom pattern after saving, the data may change from the original.';

  @override
  String get t_err773 => 'Data which includes imported custom patterns cannot be saved in an external memory drive.';

  @override
  String get t_err774 => 'Only the embroidery stitch data will be saved. Editing data, which includes custom patterns cannot be saved in an external memory drive. Please save it in the internal memory drive.';

  @override
  String get t_err775 => 'The data storage is full.\nChoose a custom pattern to replace it with the new pattern.';

  @override
  String get t_err776 => 'If you replace the custom pattern, it may change while using the previous patten. OK to continue? ';

  @override
  String get t_err_taper01 => 'Could not set the tapering stitch. Please increase the distance or the angle.';

  @override
  String get t_err_taper02 => 'Finish setting before sewing.';

  @override
  String get t_err_taper03 => 'OK to cancel the tapering stitch setting?';

  @override
  String get t_err_taper04 => 'OK to cancel the current status of the tapering stitch?';

  @override
  String get t_err_taper05 => 'This function cannot be used while the tapering stitch.';

  @override
  String get t_err_tapering07 => 'Please press the reverse stitch button to start stitching the tapering end.';

  @override
  String get t_err_tapering08 => 'Tapering will be stopped after the specified number of repetitions.';

  @override
  String get t_err_tapering09 => 'Tapering will end at the location of the end point sticker. ';

  @override
  String get t_err785 => 'Ensure there is enough upper and bobbin thread to completely stitch the design, as it is impossible to get satisfactory results if either thread runs out.';

  @override
  String get t_err790 => 'All saved data, settings and network information will be reset to default. \nDo you wish to continue?';

  @override
  String get t_err791 => 'Deleting…\nDo not turn off the machine.';

  @override
  String get t_err792 => 'Reset is complete.\nPlease turn off the machine.';

  @override
  String get t_err_paidcont_update => 'In order to use this data, it is necessary to update the software of this machine to the latest version.';

  @override
  String get t_err_embcarriageevacuate => 'Touch OK to move the carriage of embroidery unit to original position.';

  @override
  String get t_err_sr_01 => 'Remove stitch regulator module from the machine.';

  @override
  String get t_err_sr_02 => 'Sewing status was canceled because there was no cloth feeding operation for several seconds.';

  @override
  String get t_err_sr_03 => 'You can start free-motion quilting/basting with the stitch regulator.\n\nBe careful not to pull the fabric tightly, causing it to break the needle.';

  @override
  String get t_err_sr_04 => 'Please start sewing after selecting the mode.';

  @override
  String get t_err_sr_05 => 'Needle moves. Take your hand off the needle.';

  @override
  String get t_err_sr_06 => 'The stitch regulator module has been unplugged. The stitch regulator dedicated screen will be closed.  \nTo re-enter the screen, plug in it.';

  @override
  String get t_err_sr_08 => 'Be careful for needle breakage due to pulling the fabric forcefully in case of making the thread tension setting tighter.';

  @override
  String get t_err_sr_09 => 'Twin needle mode cannot be used with this function.\nUnplug the connector of the stitch regulator module and then turn off the twin needle mode to try again.';

  @override
  String get t_err_sr_10 => 'When using basting stitching, do not use the stitch regulator open toe quilting foot. Otherwise, the needle may break, causing injuries.';

  @override
  String get t_err_manual_01_b => 'If you want to view the manuals on your mobile device or PC, please access XXX(URL).';

  @override
  String get t_err_manual_02_t => 'If you want to view the manual on your mobile device or PC, please access XXX(URL).';

  @override
  String get t_err_proj_emb_001 => 'The projector functionality is limited due to the small embroidery frame. \"Projector Edit with Stylus\" is not supported, but embroidery patterns are projected.\n\n*The carriage of the embroidery unit will move after pressing OK.';

  @override
  String get t_err_proj_emb_002 => 'The projector functionality is limited due to the small embroidery frame. \"Projector Edit with Stylus\" is not supported, but embroidery patterns are projected.';

  @override
  String get t_err_proj_emb_003 => 'Projector is turning off.';

  @override
  String get t_err_proj_emb_004 => 'Projector is turning off due to the embroidery frame was removed.\n\n* The carriage of the embroidery unit will move after pressing OK.';

  @override
  String get t_err_proj_emb_005 => 'Projector is turning off due to the embroidery frame was removed.';

  @override
  String get t_err_proj_emb_006 => 'Projector is turning off.\n\n* The carriage of the embroidery unit will move after pressing OK.';

  @override
  String get t_err_proj_emb_007 => 'OK to cancel the current pattern selection?\n\n* The carriage of the embroidery unit will move after pressing OK.';

  @override
  String get t_err_proj_emb_008 => 'Converted to the embroidery pattern, and My Design Center will be exited.\nOK to continue to embroidery edit screen?\n\n* Projector is turning off and the carriage of the embroidery unit will move after pressing OK.';

  @override
  String get t_err_proj_emb_009 => 'Converted to the embroidery pattern, and IQ Designer will be exited.\nOK to continue to embroidery edit screen?\n\n* Projector is turning off and the carriage of the embroidery unit will move after pressing OK.';

  @override
  String get t_err_proj_emb_010 => 'Processing…';

  @override
  String get t_err_proj_emb_011 => 'Closing… ';

  @override
  String get t_err_proj_emb_012 => 'Projector is turning on.\n\n* The carriage of the embroidery unit will move after pressing OK.';

  @override
  String get t_err_proj_emb_013 => 'Projector is turning on.';

  @override
  String get t_err_proj_emb_014 => 'This function cannot be used while the projector is in operation.';

  @override
  String get t_err_proj_smallframe => 'Unavailable due to the small embroidery frame.';

  @override
  String get t_err_mdc_import_01 => 'Be sure to adjust the data as its size changes when loading.';

  @override
  String get t_err_voiceg_01 => 'Checking for the voice guidance data...';

  @override
  String get t_err_voiceg_02 => 'Voice guidance is ready and the setting is turned on.';

  @override
  String get t_err_photos_01 => 'Release the mask from the picture.';

  @override
  String get t_err_photos_02 => 'OK to reset the image size adjustment?';

  @override
  String get t_err_photos_03 => 'OK to cancel the background removal?';

  @override
  String get t_err_photos_04 => 'Convert to embroidery.';

  @override
  String get t_err_photos_05 => 'Converted to the embroidery pattern, and Picture Play Embroidery Function will be exited.\nOK to continue to embroidery edit screen?';

  @override
  String get t_err_photos_06 => 'Please wait a while.\nThe wireless LAN connection is temporarily turned off while the conversion is in progress.';

  @override
  String get t_err_photos_exit => 'OK to exit Picture Play Embroidery Function?';

  @override
  String get t_err_font_old_new => 'OK to convert the file to the newer data format as the old format is used?';

  @override
  String get t_err_font_old_lomited => 'The editing function is limited due to the old data format.';

  @override
  String get t_err_firstset_wlan => 'Set a Wireless LAN.\nDo you wish to go to settings to connect your machine to the Wireless network?';

  @override
  String get t_err_firstset_voiceguidance => 'Set the Voice Guidance function.\nDo you wish to go to settings for the Voice Guidance?';

  @override
  String get t_err_wlan_function_01 => 'In order to use the function, the wireless LAN setting on the machine must be turned on, and the machine must be connected to a wireless network.\nDo you wish to go to settings to connect your machine to a Wireless network?';

  @override
  String get t_err_teachingimage => 'Images for illustrative purposes only, some image may vary by model.';

  @override
  String get t_err_photo_disclaimers => 'By using this feature, you agree that no part of the content will be used:\n• for purposes that are contrary to all applicable laws and regulations (including, in particular, racist, discriminatory, hateful, pornographic or child pornographic content and/or statements that offend public order or public decency);\n• to violate any person\'s right to privacy or publicity;\n• to infringe on any copyright, trademark or other intellectual property right owned by a third-party;\n• to include any URL or keyword that direct viewers to malicious sites;\nThe user accepts and acknowledges being solely responsible for the content used. \nPlease refer to Terms of Use for details.\n\nBy using the content, I certify that I have read and fully understand Terms of Service and Guidelines.';

  @override
  String get t_err_framemovekey => '* Touch the frame move key on the embroidery screen to move frame to the center.';

  @override
  String get speech_colorchangeinfo => 'Finished stitching. \nPlease thread the machine with the next thread color.';

  @override
  String get t_err_updateinfo_01 => 'Important Update Available.\nUpdate your machine by downloading the update file from the [Download the update program] in the \"Machine settings\".';

  @override
  String get t_err_updateinfo_02 => 'Please connect your machine to a wireless network to receive notifications about the latest software. \nOr please visit Brother support website for the latest software updates.';

  @override
  String get t_err_removecarriage => 'Turn off the machine before attaching or removing the embroidery unit.';

  @override
  String get t_err_filter_removed => 'The filter has been cleared because the category is not supported.';

  @override
  String get t_err_filter_cleared => 'OK to clear the filter because the filter function in not applicable in this category.';

  @override
  String get t_principal07 => '[Presser Foot Lever]';

  @override
  String get t_principal07_01 => '\nRaise and lower the presser foot lever to raise and lower the presser foot.';

  @override
  String get t_principal07_02 => '(a) Presser foot\n(b) Presser foot lever\n';

  @override
  String get t_principal03 => '[Sewing Speed Controller]';

  @override
  String get t_principal03_01 => '\nUse this controller to adjust the sewing speed.\nMove the slide to the left to sew at slower speeds.\nMove the slide to the right to sew at higher speeds.';

  @override
  String get t_principal03_02 => '(a) Lever\n(b) Slow\n(c) Fast';

  @override
  String get t_principal12 => '[Handwheel]';

  @override
  String get t_principal12_01 => 'Rotate the handwheel toward you to raise and lower the needle.\nThe wheel should be turned toward the front of the machine.';

  @override
  String get t_principal08 => '[Flat Bed Attachment With Accessory Compartment]';

  @override
  String get t_principal08_01 => 'Store presser feet and bobbins in the accessory compartment of the flat bed attachment.\nWhen sewing cylindrical pieces, remove the flat bed attachment.';

  @override
  String get t_principal10 => '[Knee Lifter]';

  @override
  String get t_principal10_00 => '(a) Knee Lifter';

  @override
  String get t_principal10_01 => '\nUsing the knee lifter, you can raise and lower the presser foot with your knee, leaving both hands free to handle the fabric.\n\n1. Align the tabs on the knee lifter with the notches in the jack, and then insert the knee lifter as far as possible.';

  @override
  String get t_principal10_03_00 => '(a) Presser foot';

  @override
  String get t_principal10_03 => '\n2. Use your knee to move the knee lifter to the right in order to raise the presser foot.\nRelease the knee lifter to lower the presser foot.';

  @override
  String get t_principal11 => '[Foot Controller]';

  @override
  String get t_principal11_00 => '\nYou can also use the foot controller to start and stop sewing.\n\n1. Insert the foot controller plug into its jack on the machine.';

  @override
  String get t_principal11_02 => '2. Slowly depress the foot controller to start sewing.\nRelease the foot controller to stop the machine.\n\n*The speed set using the sewing speed controller is the foot controller\'s maximum sewing speed.';

  @override
  String get t_xv_principal11_01 => 'With the multi-function foot controller, various sewing machine operations in addition to starting/stopping sewing,  such as thread cutting and reverse stitching, can be specified to be performed. ';

  @override
  String get t_xv_principal11_02 => '1. Align the wide side of the mounting plate with the notch in the bottom of the main foot controller,  and then secure them together with a screw. ';

  @override
  String get t_xv_principal11_03 => '2. Align the other side of the mounting plate with the notch on the bottom of the side pedal, and then secure them together with a screw. ';

  @override
  String get t_xv_principal11_04 => '3. Insert the plug for the side pedal into the jack at the back of the main foot controller. ';

  @override
  String get t_xv_principal11_05 => '4. Insert the round-shaped plug for the main foot controller into the foot controller jack on the right side of the machine. ';

  @override
  String get t_xv_principal11_06 => '5. Slowly depress the foot controller to start sewing. Release the foot controller to stop the machine.\n \n *The speed set using the sewing speed controller is the foot controller\'s maximum sewing speed.  ';

  @override
  String get t_principal11_01_02 => '(a) Foot controller\n(b) Foot controller jack';

  @override
  String get t_principal09 => '[Feed Dog Position Switch]';

  @override
  String get t_principal09_01 => '\nUse the feed dog position switch to lower the feed dogs.';

  @override
  String get t_principal09_02 => '(a) Feed dog position switch';

  @override
  String get t_principal_buttons_01 => '[\"Needle Position\" Button]';

  @override
  String get t_principal_buttons_01_01 => 'Use this button when changing sewing direction or for detailed sewing in small areas.\nPress this button to raise or lower the needle position.\nPress the button twice to sew a single stitch.';

  @override
  String get t_principal_buttons_02 => '[\"Thread Cutter\" Button]';

  @override
  String get t_principal_buttons_02_01 => 'Press this button after sewing to automatically trim the excess thread.';

  @override
  String get t_principal_buttons_06 => '[\"Presser Foot Lifter\" button]';

  @override
  String get t_principal_buttons_06_01 => 'Press this button to lower the presser foot and apply pressure to the fabric.\nPress this button again to raise the presser foot.\nYou can raise the presser foot to the highest level by holding the presser foot button down.';

  @override
  String get t_principal_buttons_05 => '[\"Automatic Threading\" button]';

  @override
  String get t_principal_buttons_05_01 => 'Use this button to automatically thread the needle.';

  @override
  String get t_principal_buttons_04 => '[\"Start/Stop\" Button]';

  @override
  String get t_principal_buttons_04_01 => 'Press this button and the machine will sew a few stitches at a slow speed and then begin sewing at the speed set by the sewing speed controller.\nPress the button again to stop the machine.\nHold the button in to sew at the machine\'s slowest speed.\nThe button changes color according to the machine\'s operation mode.\n\nGreen: the machine is ready to sew or is sewing.\nRed: the machine cannot sew.';

  @override
  String get t_principal_buttons_03 => '[\"Reverse\" Stitch Button]';

  @override
  String get t_principal_buttons_03_01 => 'Use this button to sew reverse stitches at the beginning and end of sewing. \nFor straight and zigzag stitch patterns, the machine will sew reverse stitches at low speed, while pressing and holding the \"Reverse\" stitch button. (Stitches are sewn in the opposite direction).\nFor the other stitch patterns, the machine will sew reinforcement stitches. Press this button, and the machine sews 3 to 5 stitches in the same spot and stops automatically.';

  @override
  String get t_principal_buttons_07 => '[\"Reinforcement/Tie-off\" Stitch button]';

  @override
  String get t_principal_buttons_07_01 => 'Use this button to sew reinforcement stitches at the beginning and end of sewing.\nFor Utility stitch patterns, press this button and the machine sews 3 to 5 stitches in the same spot and stops automatically.\nFor Character/Decorative stitch patterns, the machine will sew reinforcement stitches after sewing single unit of the pattern.';

  @override
  String get t_basic13 => '[Upper Threading]';

  @override
  String get t_basic13_01_02 => '(a) \"Presser Foot Lifter\" button\n';

  @override
  String get t_basic13_01 => 'Press the motion picture key to see a video of the displayed instructions.\n\n1. Press the \"Presser Foot Lifter\" button to raise the presser foot. ';

  @override
  String get t_basic13_02_00 => '(a) \"Needle Position\" button';

  @override
  String get t_basic13_02 => '\n2. Press the \"Needle Position\" button to raise the needle. ';

  @override
  String get t_basic13_03_02 => '(a) Spool pin\n(b) Spool cap\n(c) Thread spool ';

  @override
  String get t_basic13_03 => '\n3. Pivot the spool pin so that it angles upward.\nSet the thread spool on the spool pin so that the thread unwinds from the front of the spool.\nPush the spool cap onto the spool pin as far in as possible, then return the spool pin to its original position. ';

  @override
  String get t_basic13_11_02 => '(a) Spool cap (small) \n(b) Thread spool (cross-wound thread) \n(c) Space ';

  @override
  String get t_basic13_11 => '\nWhen sewing with fine, cross-wound thread, use the small spool cap, and leave a small space between the cap and the thread spool. ';

  @override
  String get t_basic13_04_02 => '(a) Thread guide plate ';

  @override
  String get t_basic13_04 => '\n4. While holding the thread with both hands, pass the thread under and up the left side of the thread guide plate.';

  @override
  String get t_basic13_05 => '5. While holding the spool of thread in your right hand, follow the thread path with the end of the thread in your left hand. Pass the thread down, up, and then down through the grooves.';

  @override
  String get t_basic13_06_02 => '(a) Needle bar thread guide';

  @override
  String get t_basic13_06 => '\n6. Pass the thread behind the needle bar thread guide. \n';

  @override
  String get t_basic13_07 => '7. Press the \"Presser Foot Lifter\" button to lower the presser foot. ';

  @override
  String get t_basic13_08_02 => '(a) Thread guide discs ';

  @override
  String get t_basic13_08 => '\n8. Pass the thread through the thread guide discs. The thread should firmly enter the thread guide discs or the needle may not be threaded correctly. \n';

  @override
  String get t_basic13_09_02 => '(b) Thread cutter';

  @override
  String get t_basic13_09 => '\n9. Pull the thread across the thread cutter to cut the thread. ';

  @override
  String get t_basic13_10_02 => '(a) \"Automatic Threading\" button';

  @override
  String get t_basic13_10 => '\n10. Press the \"Automatic Threading\" button to have the machine automatically thread the needle. ';

  @override
  String get t_basic14 => '[Bobbin Winding]';

  @override
  String get t_basic14_01_02 => '(a) Groove in the bobbin\n(b) Spring on the shaft ';

  @override
  String get t_basic14_00 => '\nPress the motion picture key to see a video of the displayed instructions.\n\n1. Align the groove in the bobbin with the spring on the bobbin winder shaft, and set the bobbin on the shaft. ';

  @override
  String get t_basic14_02 => '\n2. Set the supplemental spool pin in the \"up\" position. ';

  @override
  String get t_basic14_02_02 => '(a) Supplemental spool pin ';

  @override
  String get t_basic14_03 => '\n3. Place the spool of thread on the supplemental spool pin, so that thread unwinds from the front of the spool. Then push the spool cap onto the spool pin as far in as possible to secure the thread spool. ';

  @override
  String get t_basic14_03_02 => '(a) Spool cap\n(b) Spool pin\n(c) Thread spool';

  @override
  String get t_basic14_04 => '\n4. With your right hand, hold the thread near the thread spool. With your left hand, hold the end of the thread, and use both hands to pass the thread into the thread guide. ';

  @override
  String get t_basic14_04_02 => '(a) Thread guide';

  @override
  String get t_basic14_05 => '\n5. Pass the thread around the pre-tension disk making sure that the thread is under the pre-tension disk. ';

  @override
  String get t_basic14_05_02 => '(a)  Pre-tension disk';

  @override
  String get t_basic14_06 => '6. Wind the thread clockwise around the bobbin 5-6 times. ';

  @override
  String get t_basic14_07 => '(a) Guide slit(with built-in cutter)\n(b) Bobbin winder seat\n\n7. Pass the end of the thread through the guide slit on the bobbin winder seat, and pull the thread to the right to cut the thread with the cutter. ';

  @override
  String get t_basic14_08_02 => '\n8. Push the bobbin winding switch to the left, until it clicks into place.  The bobbin winding window will be displayed on the LCD screen.';

  @override
  String get t_basic14_08_03 => '(a) Bobbin winding switch';

  @override
  String get t_basic14_08_04 => '\n* Use the bobbin winding slider to adjust the amount of thread wound onto the bobbin to one of five levels.';

  @override
  String get t_basic14_08_05 => '(a) Bobbin winding slider\n(b) More\n(c) Less';

  @override
  String get t_basic14_09 => '9. Press the bobbin winding \"START\" key. The bobbin starts winding automatically. Touch \"STOP\" if you want to stop before the bobbin is full.\n';

  @override
  String get t_basic14_10 => '10. You can change the winding speed by pressing - or + on the bobbin winding window. Press \"OK\" to minimize the bobbin winding window.';

  @override
  String get t_basic14_101 => '11. The bobbin stops rotating when it is done winding. The bobbin winding switch will return to the original position.';

  @override
  String get t_basic14_102 => '12. Cut the thread with the cutter and remove the bobbin. ';

  @override
  String get t_basic14_11 => '\n*Using the Spool Pin\nYou can use the main spool pin to wind the bobbin before sewing. \n\nNote: Do not use this spool pin to wind the bobbin while the machine is sewing.\n\n1. Align the groove in the bobbin with the spring on the bobbin winder shaft, and set the bobbin on the shaft. ';

  @override
  String get t_basic14_11_02 => '(a) Groove in the bobbin\n(b) Spring on the shaft ';

  @override
  String get t_basic14_12 => '\n2. Pivot the spool pin so that it angles upward.\nSet the thread spool on the spool pin so that the thread unwinds from the front of the spool.\nPush the spool cap onto the spool pin as far as possible, then return the spool pin to its original position. ';

  @override
  String get t_basic14_12_02 => '(a) Spool pin\n(b) Spool cap\n(c) Thread spool ';

  @override
  String get t_basic14_13 => '\n3. Holding the thread with your hands, slide the thread into the grooves on the thread guide plate.\nPass the thread around the thread guide. ';

  @override
  String get t_basic14_13_02 => '(a) Thread guide plate\n(b) Thread guide ';

  @override
  String get t_basic14_14 => '\n4. Pass the thread around the pre-tension disk making sure that the thread is under the pre-tension disk. ';

  @override
  String get t_basic14_15_02 => '(a) Pre-tension disk ';

  @override
  String get t_basic14_16 => '5. Wind the thread clockwise around the bobbin 5-6 times. ';

  @override
  String get t_basic14_17 => '(a) Guide slit(with built-in cutter)\n(b) Bobbin winder seat\n\n6. Pass the end of the thread through the guide slit in the bobbin winder seat, and pull the thread to the right to cut the thread with the cutter. ';

  @override
  String get t_basic14_18 => '\n7. Push the bobbin winding switch to the left, until it clicks into place.  The bobbin winding window will be displayed on the LCD screen.';

  @override
  String get t_basic14_18_02 => '(a) Bobbin winding switch  ';

  @override
  String get t_basic14_20 => '8. Press the \"Bobbin Winding Start/Stop\" key. The bobbin starts winding automatically.\n';

  @override
  String get t_basic14_201 => '9. You can change the winding speed by pressing - or + on the bobbin winding window. Press \"OK\" to minimize the bobbin winding window.';

  @override
  String get t_basic14_202 => '10. The bobbin stops rotating when it is done winding. The bobbin winding switch will return to the original position.';

  @override
  String get t_basic14_203 => '11. Cut the thread with the cutter and remove the bobbin. ';

  @override
  String get t_basic14_21_02 => '\nWhen sewing with fine, cross-wound thread, use the small spool cap, and leave a small space between the cap and the thread spool. ';

  @override
  String get t_basic14_21_03 => '(a) Spool cap (small) \n(b) Thread spool (cross-wound thread) \n(c) Space';

  @override
  String get t_basic15 => '[Changing the Needle]';

  @override
  String get t_basic15_00 => '\nTo Check the Needle Correctly:\nTo check the needle, place the flat side of the needle on a flat surface.\nCheck the needle from the top and the sides.\nThrow away any bent needles.';

  @override
  String get t_basic15_00_01 => '(a) Parallel space\n(b) Level surface (bobbin cover, glass, etc.) ';

  @override
  String get t_basic15_01 => '1. Press the \"Needle Position\" button to raise the needle.';

  @override
  String get t_basic15_02 => '2. Press Presser foot/needle exchange key.';

  @override
  String get t_basic15_03 => '3. Use the screwdriver to turn the screw toward the front of the machine and loosen the screw, then remove the needle.';

  @override
  String get t_basic15_04 => '\n4. With the flat side of the needle facing the back, insert the new needle all the way to the top of the needle stopper(viewing window) in the needle clamp.\nUse a screwdriver to securely tighten the needle clamp screw.';

  @override
  String get t_basic15_04_02 => '(a) Needle stopper\n(b) Hole for setting needle\n(c) Flat side of needle';

  @override
  String get t_basic15_05 => '5. Press Presser foot/needle exchange key to unlock all operation buttons.';

  @override
  String get t_basic16 => '[Changing the Presser Foot]';

  @override
  String get t_basic16_01 => '*Removing the Presser Foot\n\n1. Press the \"Needle Position\" button to raise the needle.';

  @override
  String get t_basic16_02 => '2. Press Presser foot/needle exchange key.';

  @override
  String get t_basic16_03 => '\n3. Raise the presser foot lever.';

  @override
  String get t_basic16_03_02 => '(a) Presser foot\n(b) Presser foot lever';

  @override
  String get t_basic16_04 => '\n4. Press the black button at the back of the presser foot holder and remove the presser foot.';

  @override
  String get t_basic16_04_02 => '(a) Black button\n(b) Presser foot holder';

  @override
  String get t_basic16_05 => '\n*Attaching the Presser Foot\n\n1. Place the new presser foot under the holder, aligning the foot pin with the notch in the holder.\nLower the presser foot lever so that the presser foot pin snaps into the notch in the holder.';

  @override
  String get t_basic16_05_02 => '(a) Notch\n(b) Pin';

  @override
  String get t_basic16_06 => '2. Press Presser foot/needle exchange key to unlock all keys and buttons.';

  @override
  String get t_basic16_07 => '(a) Presser foot\n(b) Presser foot lever\n\n3. Raise the presser foot lever.';

  @override
  String get t_basic17 => '[Setting the Bobbin]';

  @override
  String get t_basic17_01 => 'Press the motion picture key to see a video of the displayed instructions.\n\n1. Press the \"Presser Foot Lifter\" button to raise the presser foot.';

  @override
  String get t_basic17_02 => '(a) Bobbin cover\n(b) Latch\n\n2. Slide the bobbin cover latch to the right. The bobbin cover opens.\nRemove the bobbin cover.';

  @override
  String get t_basic17_03 => '3. Hold the bobbin with your right hand and hold the end of the thread with your left hand.';

  @override
  String get t_basic17_04 => '4. Insert the bobbin into the shuttle so that the thread unrolls to the left.\nLightly hold down the bobbin with your right hand, and then guide the thread as shown with your left hand.';

  @override
  String get t_basic17_05 => '\n5. Pass the thread through the guide, and then pull the thread out towards the front.\nThe cutter cuts the thread.';

  @override
  String get t_basic17_05_02 => '(a) Cutter';

  @override
  String get t_basic17_06 => '6. Insert the tab in the lower-left corner of the bobbin cover, and then lightly press down on the right side to close the cover.';

  @override
  String get t_embbasic17 => '[Attaching Iron-on Stabilizers (backing) to the Fabric]';

  @override
  String get t_embbasic17_00 => '\nFor best results in your embroidery projects, always use stabilizer material for embroidery.\nFollow the package instructions for the stabilizer being used.';

  @override
  String get t_embbasic17_01 => '\n1. Use a piece of stabilizer, which is larger than the embroidery frame being used.';

  @override
  String get t_embbasic17_01_02 => '(a) Size of the embroidery frame\n(b) Iron-on stabilizer (backing) ';

  @override
  String get t_embbasic17_02 => '\n2. Iron the iron-on stabilizer material to the wrong side of the fabric.\n\n*When using fabrics that cannot be ironed (such as towel or fabrics that have loops which expand when ironed) or in places where ironing is difficult, position the stabilizer material under the fabric without fusing it, then position the fabric and stabilizer in the embroidery frame, or check with your authorized dealer for the correct stabilizer to use.';

  @override
  String get t_embbasic17_02_02 => '(a) Fusible side of stabilizer\n(b) Fabric (wrong side) ';

  @override
  String get t_embbasic17_03 => '\n*When embroidering on thin fabrics such as organdy or lawn, or on napped fabrics such as towel or corduroy, use water soluble stabilizer (sold separately) for best results.\nThe water soluble stabilizer material will dissolve completely in water, giving the project a more attractive finish.';

  @override
  String get t_embbasic18 => '[Inserting the Fabric]';

  @override
  String get t_embbasic18_01 => '1. Lift-up and loosen the outer embroidery frame\'s adjustment screw and remove the inner frame.';

  @override
  String get t_embbasic18_02 => '\n2. Lay the fabric right side up on top of the outer frame.\nRe-insert the inner frame making sure to align the inner frame\'s Δ with the outer frame\'s Δ.';

  @override
  String get t_embbasic18_02_02 => '(a) Inner frame\n(b) Outer frame\n(c) Adjustment screw';

  @override
  String get t_embbasic18_03 => '3. Slightly tighten the frame adjustment screw, and then remove the slack in the fabric by pulling on the edges and corners.\nDo not loosen the screw.';

  @override
  String get t_embbasic18_04 => '\n4. Gently stretch the fabric taut, and tighten the frame adjustment screw to keep the fabric from loosening after stretching.\n\n* After stretching the fabric, make sure the fabric is taut.\n\n* Make sure the inside and outside frames are even before you start embroidering.\n\n*Note\nStretch the fabric by all four corners and all four edges.\nWhile stretching the fabric, tighten the frame adjustment screw.';

  @override
  String get t_embbasic18_04_02 => '(a) Outer frame\n(b) Inner frame\n(c) Fabric';

  @override
  String get t_embbasic18_04_11 => '*When using an Embroidery Frame with a lever attached.\n';

  @override
  String get t_embbasic18_04_12 => '\n1. Lower the lever.';

  @override
  String get t_embbasic18_04_13 => '2. Loosen the frame adjustment screw by hand, and then remove the inner frame.';

  @override
  String get t_embbasic18_04_14 => '\n3. Set the cloth in place.';

  @override
  String get t_embbasic18_04_15 => '4．Tighten the frame  adjustment screw by hand.';

  @override
  String get t_embbasic18_04_16 => '5. Return the lever to its original position.';

  @override
  String get t_embbasic18_05 => '\n[Using the Embroidery Sheet]';

  @override
  String get t_embbasic18_05_01 => 'When you want to embroider the pattern in a particular place, use the embroidery sheet with the frame.\n\n1. With a chalk pen, mark the area of the fabric you want to embroider.';

  @override
  String get t_embbasic18_05_02 => '(a) Embroidery pattern\n(b) Mark';

  @override
  String get t_embbasic18_06 => '\n2. Place the embroidery sheet on the inner frame.\nAlign the guide lines on the embroidery sheet with the mark you made on the fabric.';

  @override
  String get t_embbasic18_06_02 => '(a) Inner frame\n(b) Guide line';

  @override
  String get t_embbasic18_07 => '\n3. Gently stretch the fabric so there are no folds or wrinkles, and insert the inner frame into the outer frame.';

  @override
  String get t_embbasic18_07_02 => '(a) Inner frame\n(b) Outer frame';

  @override
  String get t_embbasic18_08 => '4. Remove the embroidery sheet.';

  @override
  String get t_embbasic19 => '[Attaching & Removing the Embroidery Frame]';

  @override
  String get t_embbasic19_01 => '*Wind and insert the bobbin before attaching the embroidery frame.\n\n1. Press the \"Presser Foot Lifter\" button to raise the presser foot.';

  @override
  String get t_embbasic19_02 => '\n2. Raise the frame-securing lever.';

  @override
  String get t_embbasic19_03 => '\n3. Align the embroidery frame guide with the right edge of the embroidery frame holder.';

  @override
  String get t_embbasic19_03_02 => '(a) Embroidery frame holder\n(b) Embroidery frame guide';

  @override
  String get t_embbasic19_04 => '4. Slide the embroidery frame into the holder, making sure to align the embroidery frame\'s Δ with the holder\'s Δ.';

  @override
  String get t_embbasic19_05 => '\n5. Lower the frame-securing lever to secure the embroidery frame in the embroidery frame holder.\n\n*If the frame-securing lever is not lowered, the embroidery pattern may not sew out correctly or the presser foot may strike the embroidery frame and cause injury.';

  @override
  String get t_embbasic19_05_02 => '(a) Frame-securing lever';

  @override
  String get t_embbasic19_06 => '\n[Removing the Embroidery Frame]\n\n1. Raise the frame-securing lever.';

  @override
  String get t_embbasic19_07 => '2. Pull the embroidery frame toward you.';

  @override
  String get t_embbasic20 => '[Attaching the Embroidery Unit]';

  @override
  String get t_embbasic20_01 => 'Before turning off the power, read through the steps explained below.\n\n1. Turn the main power to OFF, and then remove the flat bed attachment (if your machine has one).';

  @override
  String get t_embbasic20_03 => '\n2. Insert the embroidery unit connection into the machine connection port.\nPush lightly until the unit clicks into place.';

  @override
  String get t_embbasic20_03_02 => '(a) Embroidery unit connection\n(b) Machine connection port';

  @override
  String get t_embbasic20_04 => '(a) OFF\n(b) ON\n\n3. Turn the main power to ON.';

  @override
  String get t_embbasic20_05 => '4. Press the OK key.\nThe carriage will move to the initialization position.';

  @override
  String get t_embbasic20_06 => '[Removing the Embroidery Unit]';

  @override
  String get t_embbasic20_06_02 => '\n(a) OFF\n(b) ON\n\nBefore turning off the power, read through the steps explained below.\n\n1. Turn the main power to OFF.';

  @override
  String get t_embbasic20_07 => '(a) Release button (located under the embroidery unit)\n\n2. Hold down the release button, and pull the embroidery unit away from the machine.';

  @override
  String get t_xp_embbasic21 => '[Attaching Embroidery Foot \"W\"]';

  @override
  String get t_xp_embbasic21_01 => '1. Press the \"Needle Position\" button to raise the needle.';

  @override
  String get t_xp_embbasic21_02 => '2. Press Presser foot/needle exchange key.';

  @override
  String get t_xp_embbasic21_03 => '\n3. Raise the presser foot lever.';

  @override
  String get t_xp_embbasic21_04 => '\n4. Push the black button on the presser foot holder and remove the presser foot.';

  @override
  String get t_xp_embbasic21_04_02 => '(a) Black button\n(b) Presser foot holder';

  @override
  String get t_xp_embbasic21_05 => '\n5. Use the included screwdriver to loosen the screw of the presser foot holder, then remove the presser foot holder.';

  @override
  String get t_xp_embbasic21_05_02 => '(a) Screwdriver\n(b) Presser foot holder\n(c) Presser foot holder screw';

  @override
  String get t_xp_embbasic21_06 => '(a) Presser foot lever\n\n6. Lower the presser foot lever.';

  @override
  String get t_xp_embbasic21_07_01 => ' (a) Presser foot bar\n ';

  @override
  String get t_xp_embbasic21_07_02 => ' 7. Position the embroidery foot \"W\" on the presser foot bar from behind. ';

  @override
  String get t_xp_embbasic21_08_01 => ' (a) Presser foot holder screw\n ';

  @override
  String get t_xp_embbasic21_08_02 => ' 8. Hold the embroidery foot in place with your right hand, and then use the included screwdriver to securely tighten the presser foot holder screw.';

  @override
  String get t_xp_embbasic21_09 => '9. Press Presser foot/needle exchange key to unlock all keys and buttons.';

  @override
  String get t_embbasic21 => '[Attaching Embroidery Foot \"W\"]';

  @override
  String get t_embbasic21_01 => '1. Press the \"Needle Position\" button to raise the needle.';

  @override
  String get t_embbasic21_02 => '2. Press Presser foot/needle exchange key.';

  @override
  String get t_embbasic21_03 => '\n3. Raise the presser foot lever.';

  @override
  String get t_embbasic21_04 => '\n4. Push the black button on the presser foot holder and remove the presser foot.';

  @override
  String get t_embbasic21_04_02 => '(a) Black button\n(b) Presser foot holder';

  @override
  String get t_embbasic21_05 => '\n5. Use the included screwdriver to loosen the screw of the presser foot holder, then remove the presser foot holder.';

  @override
  String get t_embbasic21_05_02 => '(a) Screwdriver\n(b) Presser foot holder\n(c) Presser foot holder screw';

  @override
  String get t_embbasic21_06 => '(a) Presser foot lever\n\n6. Lower the presser foot lever.';

  @override
  String get t_embbasic21_07 => '(a) Arm\n(b) Needle clamp screw\n(c) Presser foot holder screw\n(d) Wiper\n\n7. Position embroidery foot \"W\" on the presser foot bar so that the arm of embroidery foot \"W\" is in contact with the back of the needle holder.';

  @override
  String get t_embbasic21_08 => '8. Use the included screwdriver to firmly tighten the screw of the presser foot holder.';

  @override
  String get t_embbasic21_09 => '9. Press Presser foot/needle exchange key to unlock all keys and buttons.';

  @override
  String get t_embbasic21_emb_07 => '(a) Arm\n(b) Needle clamp screw\n(c) Presser foot holder screw\n(d) Wiper\n\n3. Position embroidery foot \"W\" on the presser foot bar so that the arm of embroidery foot \"W\" is in contact with the back of the needle holder.';

  @override
  String get t_embbasic21_emb_08 => '4. Use the included screwdriver to firmly tighten the screw of the presser foot holder.';

  @override
  String get t_embbasic21_emb_09 => '5. Press Presser foot/needle exchange key to unlock all keys and buttons.';

  @override
  String get t_xv_embbasic21 => ' [Attaching Embroidery Foot \"W+\"]';

  @override
  String get t_xv_embbasic21_05 => '\n 5. Use the included screwdriver to remove the screw of the presser foot holder, then remove the presser foot holder. ';

  @override
  String get t_xv_embbasic21_07_01 => ' (a) Presser foot bar\n ';

  @override
  String get t_xv_embbasic21_07_02 => ' 7. Position the embroidery foot \"W+\" on the presser foot bar from behind. ';

  @override
  String get t_xv_embbasic21_08_01 => ' (a) Presser foot holder screw\n ';

  @override
  String get t_xv_embbasic21_08_02 => ' 8. Hold the embroidery foot in place with your right hand, and then use the included screwdriver to securely tighten the presser foot holder screw.';

  @override
  String get t_xv_embbasic21_09 => ' 9. Plug the connector of the embroidery foot \"W+\" with LED pointer into the jack on the left of your machine. ';

  @override
  String get t_xv_embbasic21_10 => ' 10. Press Presser foot/needle exchange key to unlock all keys and buttons.';

  @override
  String get t_embbasic22 => '[Correct stabilizer to use]';

  @override
  String get t_embbasic22_00_01 => '1. Fabrics that can be ironed';

  @override
  String get t_embbasic22_00_02 => '2. Fabrics that cannot be ironed';

  @override
  String get t_embbasic22_00_03 => '3. Thin fabrics';

  @override
  String get t_embbasic22_00_04 => '4. Napped fabrics';

  @override
  String get t_embbasic22_00_05 => '\nFor best results in your embroidery projects, always use stabilizer material for embroidery.\nFollow the package instructions for the stabilizer being used.';

  @override
  String get t_embbasic22_01 => '\n[1. Fabrics that can be ironed]';

  @override
  String get t_embbasic22_01_02 => '\nIron the iron-on stabilizer material to the wrong side of the fabric.\nUse a piece of stabilizer which is larger than the embroidery frame being used.';

  @override
  String get t_embbasic22_01_03 => '(a) Size of the embroidery frame\n(b) Iron-on stabilizer (backing) ';

  @override
  String get t_embbasic22_02 => '[2. Fabrics that cannot be ironed]';

  @override
  String get t_embbasic22_02_02 => '\nPlace the stabilizer material under the fabric without attaching it, then position the fabric and stabilizer in the embroidery frame.';

  @override
  String get t_embbasic22_03 => '[3. Thin fabrics]';

  @override
  String get t_embbasic22_03_02 => '\nUse water soluble stabilizer material (sold separately) for best results.\nThe water soluble stabilizer material will dissolve completely in water, giving the project a more attractive finish.';

  @override
  String get t_embbasic22_04 => '[4. Napped fabrics]';

  @override
  String get t_embbasic22_04_02 => '\nWhen using fabrics that cannot be ironed (such as towel or fabrics that have loops which expand when ironed) , place the stabilizer material under the fabric without attaching it, then position the fabric and stabilizer in the embroidery frame, or use water soluble stabilizer material (sold separately) .';

  @override
  String get t_embbasic23 => '[Adjusting Thread Tension]';

  @override
  String get t_embbasic23_01 => 'When embroidering, the thread tension should be set so that the upper thread can slightly be seen on the wrong side of the fabric.';

  @override
  String get t_embbasic23_01_01 => '1. Correct Thread Tension';

  @override
  String get t_embbasic23_01_02 => '2. Upper thread is too taut';

  @override
  String get t_embbasic23_01_03 => '3. Upper thread is too loose';

  @override
  String get t_embbasic23_02 => '[1. Correct Thread Tension]';

  @override
  String get t_embbasic23_02_02 => '\nThe pattern can be seen from the wrong side of the fabric.\nIf the thread tension is not set correctly, the pattern will not finish well.\nThe fabric may pucker or the thread may break.';

  @override
  String get t_embbasic23_03 => '[2. Upper thread is too taut]';

  @override
  String get t_embbasic23_03_02 => '\nThe tension of the upper thread is too taut, resulting in the bobbin thread being visible from the right side of the fabric.';

  @override
  String get t_embbasic23_03_03 => '\nPress - to weaken the upper thread tension.';

  @override
  String get t_embbasic23_04 => '[3. Upper thread is too loose]';

  @override
  String get t_embbasic23_04_02 => '\nThe tension of the upper thread is too loose, resulting in a loose upper thread, loose thread locks or loops appearing on the right side of the fabric.';

  @override
  String get t_embbasic23_04_03 => '\nPress + to increase the thread tension.';

  @override
  String get t_trouble22 => '[Upper thread breaks]';

  @override
  String get t_trouble22_01 => '*Cause 1\nMachine is not threaded correctly (used the wrong spool cap, spool cap is loose, the thread did not catch the needle bar threaded, etc.) .\n\n*Solution\nRethread the machine correctly.';

  @override
  String get t_trouble22_02 => '*Cause 2\nThread is knotted or tangled.\n\n*Solution\nRe-thread upper and lower thread.';

  @override
  String get t_trouble22_03 => '*Cause 3\nThread is too thick for the needle.\n\n*Solution\nCheck needle and thread combinations.';

  @override
  String get t_trouble22_04 => '*Cause 4\nUpper thread tension is too tight.\n\n*Solution\nAdjust the thread tension.';

  @override
  String get t_trouble22_05 => '*Cause 5\nThread is twisted.\n\n*Solution\nUse scissors, etc. to cut the twisted thread and remove it from the race, etc.';

  @override
  String get t_trouble22_06 => '*Cause 6\nNeedle is turned, bent or the point is dull.\n\n*Solution\nReplace the needle.';

  @override
  String get t_trouble22_07 => '*Cause 7\nNeedle is installed incorrectly.\n\n*Solution\nReinstall the needle correctly.';

  @override
  String get t_trouble22_08 => '*Cause 8\nThere are scratches near the hole of the needle plate.\n\n*Solution\nReplace the needle plate, or consult your nearest dealer.';

  @override
  String get t_trouble22_09 => '*Cause 9\nThere are scratches near the hole in the presser foot.\n\n*Solution\nReplace the presser foot, or consult your nearest dealer.';

  @override
  String get t_trouble22_10 => '*Cause 10\nThere are scratches on the race.\n\n*Solution\nReplace the race, or consult your nearest dealer.';

  @override
  String get t_trouble23 => '[Bobbin thread breaks]';

  @override
  String get t_trouble23_01 => '*Cause 1\nBobbin is set incorrectly.\n\n*Solution\nReset the bobbin thread correctly.';

  @override
  String get t_trouble23_02 => '*Cause 2\nThere are scratches on the bobbin or it doesn\'t rotate properly.\n\n*Solution\nReplace the bobbin.';

  @override
  String get t_trouble23_03 => '*Cause 3\nThread is twisted.\n\n*Solution\nUse scissors, etc. to cut the twisted thread and remove it from the race, etc.';

  @override
  String get t_trouble24 => '[Skipped stitches]';

  @override
  String get t_trouble24_01 => '*Cause 1\nMachine is threaded incorrectly.\n\n*Solution\nCheck the steps for threading the machine and rethread it correctly.';

  @override
  String get t_trouble24_02 => '*Cause 2\nUsing improper needle or thread for the selected fabric.\n\n*Solution\nCheck the \"Fabric/Thread/Needle Combinations\" chart.';

  @override
  String get t_trouble24_03 => '*Cause 3\nNeedle is turned, bent or the point is dull.\n\n*Solution\nReplace the needle.';

  @override
  String get t_trouble24_04 => '*Cause 4\nNeedle is installed incorrectly.\n\n*Solution\nReinstall the needle correctly.';

  @override
  String get t_trouble24_05 => '*Cause 5\nNeedle is defective.\n\n*Solution\nReplace the needle.';

  @override
  String get t_trouble24_06 => '*Cause 6\nDust or lint is collected under the needle plate.\n\n*Solution\nRemove the dust or lint with the brush.';

  @override
  String get t_trouble25 => '[Fabric puckers]';

  @override
  String get t_trouble25_01 => '*Cause 1\nThere is a mistake in the upper or bobbin threading.\n\n*Solution\nCheck the steps for threading the machine and rethread it correctly.';

  @override
  String get t_trouble25_02 => '*Cause 2\nSpool cap is set incorrectly.\n\n*Solution\nCheck the method for attaching the spool cap, then reattach the spool cap.';

  @override
  String get t_trouble25_03 => '*Cause 3\nUsing improper needle or thread for the selected fabric.\n\n*Solution\nCheck the \"Fabric/Thread/Needle Combinations\" chart.';

  @override
  String get t_trouble25_04 => '*Cause 4\nNeedle is turned, bent or the point is dull.\n\n*Solution\nReplace the needle.';

  @override
  String get t_trouble25_05 => '*Cause 5\nStitches are too long when sewing thin fabrics.\n\n*Solution\nShorten the stitch length.';

  @override
  String get t_trouble25_06 => '*Cause 6\nThread tension is set incorrectly.\n\n*Solution\nAdjust the thread tension.';

  @override
  String get t_trouble25_07 => '*Cause 7\nWrong presser foot.\n\n*Solution\nUse correct presser foot.';

  @override
  String get t_trouble26 => '[Machine is noisy]';

  @override
  String get t_trouble26_01 => '*Cause 1\nDust or lint is caught in the feed dogs.\n\n*Solution\nRemove the dust or lint.';

  @override
  String get t_trouble26_02 => '*Cause 2\nPieces of thread are caught in the race.\n\n*Solution\nClean the race.';

  @override
  String get t_trouble26_03 => '*Cause 3\nUpper threading is incorrect.\n\n*Solution\nCheck the steps for threading the machine and rethread the machine.';

  @override
  String get t_trouble26_04 => '*Cause 4\nThere are scratches on the race.\n\n*Solution\nReplace the race, or consult your nearest dealer.';

  @override
  String get t_trouble27 => '[Cannot use the needle threader]';

  @override
  String get t_trouble27_01 => '*Cause 1\nNeedle is not in the correct position.\n\n*Solution\nPress the \"Needle Position\" button to raise the needle.';

  @override
  String get t_trouble27_02 => '*Cause 2\nThreading hook does not pass through the eye of the needle.\n\n*Solution\nPress the \"Needle Position\" button to raise the needle.';

  @override
  String get t_trouble27_03 => '*Cause 3\nNeedle is installed incorrectly.\n\n*Solution\nReinstall the needle correctly.';

  @override
  String get t_trouble28 => '[Thread tension is incorrect]';

  @override
  String get t_trouble28_01 => '*Cause 1\nUpper threading is incorrect.\n\n*Solution\nCheck the steps for threading the machine and rethread the machine.';

  @override
  String get t_trouble28_02 => '*Cause 2\nBobbin is set incorrectly.\n\n*Solution\nReset the bobbin.';

  @override
  String get t_trouble28_03 => '*Cause 3\nUsing improper needle or thread for the selected fabric.\n\n*Solution\nCheck the \"Fabric/Thread/Needle Combinations\" chart.';

  @override
  String get t_trouble28_04 => '*Cause 4\nPresser foot holder is not attached correctly.\n\n*Solution\nReattach the presser foot holder correctly.';

  @override
  String get t_trouble28_05 => '*Cause 5\nThread tension is set incorrectly.\n\n*Solution\nAdjust the thread tension.';

  @override
  String get t_trouble29 => '[Character pattern does not sew out correctly]';

  @override
  String get t_trouble29_01 => '*Cause 1\nWrong presser foot was used.\n\n*Solution\nAttach the correct presser foot.';

  @override
  String get t_trouble29_02 => '*Cause 2\nPattern adjustment settings were set incorrectly.\n\n*Solution\nRevise the pattern adjustment settings.';

  @override
  String get t_trouble29_03 => '*Cause 3\nDid not use a stabilizer material on thin fabrics or stretch fabrics.\n\n*Solution\nAttach a stabilizer material.';

  @override
  String get t_trouble29_04 => '*Cause 4\nThread tension is set incorrectly.\n\n*Solution\nAdjust the thread tension.';

  @override
  String get t_trouble30 => '[Embroidery pattern does not sew out correctly]';

  @override
  String get t_trouble30_01 => '*Cause 1\nThread is twisted.\n\n*Solution\nUse scissors, etc. to cut the twisted thread and remove it from the race, etc.';

  @override
  String get t_trouble30_02 => '*Cause 2\nFabric was not inserted into the frame correctly (fabric was loose, etc.) .\n\n*Solution\nIf the fabric is not stretched tight in the frame, the pattern may turn out poorly or there may be shrinkage of the pattern.\nSet the fabric into the frame correctly.';

  @override
  String get t_trouble30_03 => '*Cause 3\nStabilizing material was not attached.\n\n*Solution\nAlways use stabilizing material, especially with stretch fabrics, lightweight fabrics, fabrics with a coarse weave, or fabrics that often cause pattern shrinkage. See your nearest dealer for the proper stabilizer.';

  @override
  String get t_trouble30_04 => '*Cause 4\nThere was an object placed near the machine, and the carriage or embroidery frame hit the object during sewing.\n\n*Solution\nIf the frame bumps something during sewing, the pattern will turn out poorly.\nDo not place anything in the area where the frame may bump it during sewing.';

  @override
  String get t_trouble30_05 => '*Cause 5\nFabric outside the frame edges interferes with the sewing arm, so the embroidery unit cannot move.\n\n*Solution\nReinsert the fabric in the embroidery frame so that the excess fabric is away from the sewing arm, and rotate the pattern 180 degrees.';

  @override
  String get t_trouble30_06 => '*Cause 6\nFabric is too heavy, so the embroidery unit cannot move freely.\n\n*Solution\nPlace a large thick book or similar object under the arm head to lightly lift the heavy side and keep it level.';

  @override
  String get t_trouble30_07 => '*Cause 7\nFabric is hanging off the table.\n\n*Solution\nIf the fabric is hanging off the table during embroidery, the embroidery unit will not move freely.\nPlace the fabric so that it does not hang off the table (or, hold fabric to keep from dragging).';

  @override
  String get t_trouble30_08 => '*Cause 8\nFabric is snagged or caught on something.\n\n*Solution\nStop the machine and place the fabric so that it does not get caught or snagged.';

  @override
  String get t_trouble30_09 => '*Cause 9\nEmbroidery frame was removed during sewing (for example, to reset the bobbin) .\nThe presser foot was bumped or moved while removing or attaching the embroidery frame, or the embroidery unit was moved.\n\n*Solution\nIf the presser foot is bumped or the embroidery unit is moved during sewing, the pattern will not turn out.\nBe careful when removing or reattaching the embroidery frame during sewing.';

  @override
  String get t_trouble31 => '[Needle breaks]';

  @override
  String get t_trouble31_01 => '*Cause 1\nNeedle is installed incorrectly.\n\n*Solution\nReinstall the needle correctly.';

  @override
  String get t_trouble31_02 => '*Cause 2\nNeedle clamp screw is not tightened.\n\n*Solution\nTighten the needle clamp screw.';

  @override
  String get t_trouble31_03 => '*Cause 3\nNeedle is turned or bent.\n\n*Solution\nReplace the needle.';

  @override
  String get t_trouble31_04 => '*Cause 4\nUsing improper needle or thread for the selected fabric.\n\n*Solution\nCheck the \"Fabric/Thread/Needle Combinations\" chart.';

  @override
  String get t_trouble31_05 => '*Cause 5\nWrong presser foot was used.\n\n*Solution\nUse the recommended presser foot.';

  @override
  String get t_trouble31_06 => '*Cause 6\nUpper thread tension is too tight.\n\n*Solution\nAdjust the thread tension setting.';

  @override
  String get t_trouble31_07 => '*Cause 7\nFabric is pulled during sewing.\n\n*Solution\nDo not pull the fabric during sewing.';

  @override
  String get t_trouble31_08 => '*Cause 8\nSpool cap is set incorrectly.\n\n*Solution\nCheck the method for attaching the spool cap, then reattach the spool cap.';

  @override
  String get t_trouble31_09 => '*Cause 9\nThere are scratches around the holes in the needle plate.\n\n*Solution\nReplace the needle plate, or consult your nearest dealer.';

  @override
  String get t_trouble31_10 => '*Cause 10\nThere are scratches around the hole(s) in the presser foot.\n\n*Solution\nReplace the presser foot, or consult your nearest dealer.';

  @override
  String get t_trouble31_11 => '*Cause 11\nThere are scratches on the race.\n\n*Solution\nReplace the race, or consult your nearest dealer.';

  @override
  String get t_trouble31_12 => '*Cause 12\nNeedle is defective.\n\n*Solution\nReplace the needle.';

  @override
  String get t_trouble32 => '[Fabric does not feed through the machine]';

  @override
  String get t_trouble32_01 => '*Cause 1\nFeed dogs are set in the down position.\n\n*Solution\nPress the free-motion mode key, and then rotate the handwheel to raise the feed dogs.';

  @override
  String get t_trouble32_02 => '*Cause 2\nStitches are too close together.\n\n*Solution\nLengthen the stitch length setting.';

  @override
  String get t_trouble32_03 => '*Cause 3\nWrong presser foot was used.\n\n*Solution\nUse the correct presser foot.';

  @override
  String get t_trouble32_04 => '*Cause 4\nNeedle is turned, bent or the point is dull.\n\n*Solution\nReplace the needle.';

  @override
  String get t_trouble32_05 => '*Cause 5\nThread is entangled.\n\n*Solution\nCut the entangled thread and remove it from the race.';

  @override
  String get t_trouble33 => '[The Machine does not operate]';

  @override
  String get t_trouble33_01 => '*Cause 1\nThere is no pattern selected.\n\n*Solution\nSelect a pattern.';

  @override
  String get t_trouble33_02 => '*Cause 2\n\"Start/Stop\" button was not pressed.\n\n*Solution\nPress the \"Start/Stop\" button.';

  @override
  String get t_trouble33_03 => '*Cause 3\nMain power switch is not turned on.\n\n*Solution\nTurn on the main power switch.';

  @override
  String get t_trouble33_04 => '*Cause 4\nPresser foot is not lowered.\n\n*Solution\nLower the presser foot.';

  @override
  String get t_trouble33_05 => '*Cause 5\n\"Start/Stop\" button was pressed with the foot controller attached.\n\n*Solution\nRemove the foot controller, or use the foot controller to operate the machine.';

  @override
  String get t_trouble33_06 => '*Cause 6\nSpeed control lever is set to control the zigzag stitch width.\n\n*Solution\nUse the foot controller instead of the \"Start/Stop\" button to operate the machine.';

  @override
  String get t_trouble34 => '[Embroidery unit does not operate]';

  @override
  String get t_trouble34_01 => '*Cause 1\nThere is no pattern selected.\n\n*Solution\nSelect a pattern.';

  @override
  String get t_trouble34_02 => '*Cause 2\nMain power switch is not turned on.\n\n*Solution\nTurn on the main power switch.';

  @override
  String get t_trouble34_03 => '*Cause 3\nEmbroidery unit is not attached correctly.\n\n*Solution\nReattach the embroidery unit correctly.';

  @override
  String get t_trouble34_04 => '*Cause 4\nEmbroidery frame was attached before the unit was initialized.\n\n*Solution\nRemove the embroidery unit, perform the initialization process correctly.';

  @override
  String get t_trouble35 => ' [The thread is tangled on the wrong side of the fabric]';

  @override
  String get t_trouble35_01 => ' *Cause 1\nLCD contrast is not properly adjusted.\n\n*Solution\nAdjust the LCD contrast.';

  @override
  String get t_maintenance36 => '[Cleaning the Race and Shuttle]';

  @override
  String get t_maintenance36_00 => 'If dust or bits of dirt collect in the race or bobbin case, the machine will not run well, and the bobbin thread detection function may not operate.\nKeep the machine clean for best results.\nBefore turning off the power, read through the steps explained below.';

  @override
  String get t_maintenance36_01 => '\n1. Press the \"Needle Position\" button to raise the needle.';

  @override
  String get t_maintenance36_02 => '2. Lower the presser foot.';

  @override
  String get t_maintenance36_03 => '(a) OFF\n(b) ON\n\n3. Turn the main power to OFF.';

  @override
  String get t_maintenance36_04 => '4. Remove the needle and the presser foot holder.';

  @override
  String get t_maintenance36_05_11 => '5. Remove the flat bed attachment or the embroidery unit if either are attached.\n\nSlide the needle plate lever toward you.\nThe needle plate opens.';

  @override
  String get t_maintenance36_05_12 => '(a)Slide toward you.\n';

  @override
  String get t_maintenance36_05_13 => '6. Slide out the needle plate with your right hand to remove it.';

  @override
  String get t_maintenance36_05_14 => '(a) Needle plate cover\n';

  @override
  String get t_maintenance36_05_15 => '\n7. Grasp the bobbin case, and then gently lift out.';

  @override
  String get t_maintenance36_07_02 => '(a) Bobbin case';

  @override
  String get t_maintenance36_08 => '\n8. Use the cleaning brush or a vacuum cleaner to remove any lint and dust from the race and its surrounding area.\n\n* Do not apply oil to the bobbin case.';

  @override
  String get t_maintenance36_08_02 => '(a) Cleaning brush\n(b) Race';

  @override
  String get t_embbasic18_04_21 => '\n9. Insert the bobbin case so that the ▲ mark on the bobbin case aligns with the ● mark on the machine.';

  @override
  String get t_embbasic18_04_22 => '(a)  ▲ mark on the bobbin case\n(b)  ● mark on the machine';

  @override
  String get t_embbasic18_04_23 => '\n10. Insert the tabs on the needle plate into the notches in the machine.';

  @override
  String get t_embbasic18_04_24 => '(a) Tabs\n(b) Notches';

  @override
  String get t_embbasic18_04_25 => '11. Press down on the right side of the needle plate to secure it.';

  @override
  String get t_sewing01_00 => 'Sewing select type';

  @override
  String get t_sewing01_00_01 => '1-01:Normal sewing\n1-05:Reinforced sewing\n1-06:Stretch fabric sewing';

  @override
  String get t_sewing01_00_01_s_normal => 'Normal sewing';

  @override
  String get t_sewing01_00_01_s_reinforced => 'Reinforced sewing';

  @override
  String get t_sewing01_00_01_s_stretch => 'Stretch fabric sewing';

  @override
  String get t_sewing01 => '[Straight Stitches]';

  @override
  String get t_sewing01_01 => '\n1. Attach presser foot \"J\".\nHold the thread tails and fabric with your left hand, and rotate the Handwheel with your right hand to insert the needle into the fabric.';

  @override
  String get t_sewing01_01_02 => '(a) Sewing start position';

  @override
  String get t_sewing01_02 => '\n2. Lower the presser foot, and hold the \"Reverse/Reinforcement Stitch\" button to sew 3-4 stitches. The machine sews reverse stitches (or reinforcement stitches) .\nPress the \"Start/Stop\" button to sew forward.\nThe machine will begin sewing slowly.';

  @override
  String get t_sewing01_03 => '(a) Reverse stitches';

  @override
  String get t_sewing01_04 => '3. When sewing is completed, hold the \"Reverse/Reinforcement Stitch\" button to sew 3-4 reverse stitches (or reinforcement stitches) at the end of seam.';

  @override
  String get t_sewing01_05 => '4. After sewing, press the \"Thread Cutter\" button to trim the threads.\n\n* When the automatic thread cutting and automatic reinforcement stitch keys on the screen are selected, reverse stitches (or reinforcement stitches) will be sewn automatically at the beginning of sewing when the \"Start/Stop\" button is pressed.\nPress the \"Reverse/Reinforcement Stitch\" button to sew reverse stitches (or reinforcement stitches) and trim the thread automatically at the end of sewing.';

  @override
  String get t_sewing01_06 => '\n* Changing the Needle Position \nWhen you select left or middle needle position stitches, you can use \"+\" and \"-\" keys in the L/R shift display to change the position of the needle.\nMatch the distance from the right edge of the presser foot to the needle with the seam allowance, then align the edge of the presser foot with the edge of the fabric during sewing for an attractive finish.';

  @override
  String get t_sewing01_07 => '(a) Seam allowance';

  @override
  String get t_sewing02 => '[Overcasting]';

  @override
  String get t_sewing02_00 => 'Sewing select type';

  @override
  String get t_sewing02_00_01 => '1-16:Light and medium fabrics\n1-17:Heavy fabric\n1-18:Medium, heavy and easily friable fabrics\n1-19:Stretch fabric\n1-20:Thick and medium stretch fabric\n1-21:Stretch fabric';

  @override
  String get t_sewing02_00_01_f_lightandmedium => 'Light and medium fabrics';

  @override
  String get t_sewing02_00_01_f_heavy => 'Heavy fabric';

  @override
  String get t_sewing02_00_01_f_mediumstretch => 'Medium, heavy and easily friable fabrics';

  @override
  String get t_sewing02_00_01_f_stretch1 => 'Stretch fabric';

  @override
  String get t_sewing02_00_01_f_thickandmediumstretch => 'Thick and medium stretch fabric';

  @override
  String get t_sewing02_00_01_f_stretch2 => 'Stretch fabric';

  @override
  String get t_sewing02_01 => '1. Attach presser foot \"G\". Lower the presser foot so that the presser foot guide is set flush against the edge of the fabric.';

  @override
  String get t_sewing02_02 => '\n2. Sew along the presser foot guide.';

  @override
  String get t_sewing02_02_02 => '(a) Guide';

  @override
  String get t_sewing02_03 => '\n1. Attach presser foot \"J\". Sew with the needle dropping slightly off the edge of the fabric.';

  @override
  String get t_sewing02_04 => '(a) Needle drop position';

  @override
  String get t_sewing02_05 => '\n*After the stitch width is adjusted, rotate the handwheel toward you by hand and check that the needle does not touch the presser foot.\nIf the needle hits the presser foot the needle may break and cause injury.';

  @override
  String get t_sewing02_05_02 => '(a) The needle should not touch';

  @override
  String get t_sewing04 => '[Scallop Stitches]';

  @override
  String get t_sewing04_01 => 'This wave-shaped satin stitch is called the scallop stitch. Use this stitch to decorate the edges of blouse collars and handkerchiefs or use it as a hem accent.\nA temporary spray adhesive may be necessary for lightweight fabrics. Test sew the fabric before sewing a project.';

  @override
  String get t_sewing04_02 => '1. Attach presser foot \"N+\". Sew scallop stitches along the edge of the fabric.\nDo not sew directly on the edge of the fabric.';

  @override
  String get t_sewing04_03 => '2. Trim along the seam, making sure not to cut the stitches.';

  @override
  String get t_sewing05_00 => 'Sewing select type';

  @override
  String get t_sewing05_00_01 => '4-01:Light to medium weight fabric (for horizontal holes)  \n4-07:Light to medium weight fabric\n4-10:Stretch fabric with coarse weaves\n4-11:Stretch fabric\n4-13:Suits, overcoat\n4-14:Jeans, trousers\n4-15:Thick coats';

  @override
  String get t_sewing05_00_01_f_lighttomediumhorizhole => 'Light to medium weight fabric (for horizontal holes) ';

  @override
  String get t_sewing05_00_01_f_lighttomedium => 'Light to medium weight fabric';

  @override
  String get t_sewing05_00_01_f_stretchweaves => 'Stretch fabric with coarse weaves';

  @override
  String get t_sewing05_00_01_f_stretch => 'Stretch fabric';

  @override
  String get t_sewing05_00_01_f_suitsandovercoat => 'Suits, overcoat';

  @override
  String get t_sewing05_00_01_f_jeansandtrousers => 'Jeans, trousers';

  @override
  String get t_sewing05_00_01_f_thickcoats => 'Thick coats';

  @override
  String get t_sewing05 => '[Buttonholes]';

  @override
  String get t_sewing05_02 => '1. Mark the position and length of the buttonhole on the fabric.';

  @override
  String get t_sewing05_03 => '\n2. Attach buttonhole foot \"A+\". Pull out the button holder plate on the presser foot, and insert the button that will be put through the buttonhole.\nThen tighten the button holder plate around the button.\n* The size of the buttonhole is determined by the size of the button in the button holder plate.';

  @override
  String get t_sewing05_04 => '(a) Button holder plate';

  @override
  String get t_sewing05_05 => '\n3. Align the presser foot with the mark on the fabric, and lower the presser foot.';

  @override
  String get t_sewing05_06 => '(a) Mark on the fabric\n(b) Marks on the presser foot';

  @override
  String get t_sewing05_07 => '\n4. Lower the buttonhole lever so that it is positioned behind the metal bracket on the buttonhole foot.';

  @override
  String get t_sewing05_08 => '(a) Metal bracket';

  @override
  String get t_sewing05_09 => '4. Gently hold the end of the upper thread, and then start sewing. Feed the fabric carefully by hand while the buttonhole is sewn.\nOnce sewing is completed, the machine automatically sews reinforcement stitches, and then stops.';

  @override
  String get t_sewing05_10 => '\n5. Insert a pin along the inside of one of the bar tacks, and then insert the seam ripper into the center of the buttonhole and cut towards the pin.';

  @override
  String get t_sewing05_11 => '(a) Basting pin\n(b) Seam ripper';

  @override
  String get t_sewing05_12 => '\n[For Keyhole Buttonholes]\nUse the eyelet punch to make a hole in the rounded end of the buttonhole. Then insert a pin along the inside of one of the bar tacks, insert a seam ripper into the hole made with the eyelet punch, and cut towards the pin.';

  @override
  String get t_sewing05_13 => '(a) Eyelet punch\n(b) Basting pin';

  @override
  String get t_sewing05_14 => '*Sewing Stretch Fabrics\nWhen sewing on stretch fabric with 4-10 or 4-11, sew the buttonhole stitches over a gimp thread.';

  @override
  String get t_sewing05_16 => '\n1. Hook the gimp thread onto the back of presser foot \"A+\".\nInsert the ends into the grooves at the front of the presser foot, and then temporarily tie them there.';

  @override
  String get t_sewing05_17 => '(a) Upper thread';

  @override
  String get t_sewing05_18 => '2. Lower the presser foot and start sewing.';

  @override
  String get t_sewing05_19 => '3. Once sewing is completed, gently pull the gimp thread to remove any slack, and trim off any excess.';

  @override
  String get t_sewing05_20 => '\n*Odd Shaped Buttons/Buttons that do not fit into the Button Holder Plate\nUse the markings on the presser foot scale to set the size of the buttonhole.\nOne mark on the presser foot scale equals 5mm (approx.3/16 inch) .\nAdd the button diameter and thickness together, and then set the plate at the calculated value.';

  @override
  String get t_sewing05_21 => '(a) Presser foot scale\n(b) Button holder plate\n(c) Completed measurement of diameter + thickness\n(d) 5 mm (approx.3/16 inch) ';

  @override
  String get t_sewing05_22 => '\nExample:\nFor a button with a diameter of 15 mm (approx.9/16 inch) , and a thickness of 10 mm (approx.3/8 inch) , the scale should be set at 25 mm (approx.1 inch) .';

  @override
  String get t_sewing05_23 => '(a) 10 mm (approx.3/8 inch) \n(b) 15 mm (approx.9/16 inch) ';

  @override
  String get t_sewing06 => '[Button Sewing]';

  @override
  String get t_sewing06_01 => 'Do not use the automatic thread cutting function when sewing buttons. Otherwise, you will lose the thread ends.\n\n1. Attach button sewing foot \"M\", slide the button along the metal plate and into the presser foot, and lower the presser foot.';

  @override
  String get t_sewing06_01_02 => '(a) Button\n(b) Metal plate\n';

  @override
  String get t_sewing06_02 => '2. Rotate the handwheel to check that the needle goes into each hole correctly. Then, gently hold the end of the upper thread and start sewing. The machine stops automatically when sewing is finished.';

  @override
  String get t_sewing06_03 => '3. From the wrong side of the fabric, pull the end of the bobbin thread to pull the upper thread through to the wrong side of the fabric. Tie the two thread ends together and cut the threads.';

  @override
  String get t_sewing06_04 => '*Attaching 4 Hole Buttons';

  @override
  String get t_sewing06_05 => 'Sew the two holes closest to you. Then raise the presser foot and move the fabric so that the needle goes into the next two holes, and sew them in the same way.';

  @override
  String get t_sewing06_06 => '*Attaching a Shank to the Button\n\n1. Pull the shank lever toward you before sewing.';

  @override
  String get t_sewing06_07 => '(a) Shank lever\n';

  @override
  String get t_sewing06_08 => '2. Pull the two ends of the upper thread between the button and the fabric, wind them around the shank, and then tie them firmly together.\nTie the ends of the bobbin thread from the beginning and end of sewing together on the wrong side of the fabric.';

  @override
  String get t_sewing07 => '[Bar Tacks]';

  @override
  String get t_sewing07_01 => 'Use bar tacks to reinforce areas that will be subject to strain, such as pocket corners.';

  @override
  String get t_sewing07_02 => '\n1. Attach buttonhole foot \"A+\" and set the scale to the length of the bar tack you wish to sew.';

  @override
  String get t_sewing07_03 => '(a) Presser foot scale\n(b) Completed length measurement\n(c) 5 mm (approx.3/16 inch) ';

  @override
  String get t_sewing07_04 => '2. Set the fabric so that the pocket moves towards you during sewing.';

  @override
  String get t_sewing07_05 => '\n3. Check the first needle drop point and lower the presser foot.';

  @override
  String get t_sewing07_06 => '(a) 2 mm (approx.1/16 inch) ';

  @override
  String get t_sewing07_09 => '4. Gently hold the end of the upper thread and begin sewing.\nWhen sewing is completed, the machine will sew reinforcement stitches and stop automatically.';

  @override
  String get t_sewing07_10 => '\n*Bar Tacks on Thick Fabrics\nPlace a piece of folded fabric or cardboard beside the fabric being sewn to level the buttonhole foot and allow for easier feeding.';

  @override
  String get t_sewing07_11 => '(a) Presser foot\n(b) Thick paper\n(c) Fabric';

  @override
  String get t_sewing08 => '[Zipper Insertion]';

  @override
  String get t_sewing08_00 => '\n*Centered Zipper\nUse for bags and other such applications.\n\n1. Attach presser foot \"J\" and sew straight stitches up to the zipper opening.\nChange to a basting stitch and sew to the top of the fabric.';

  @override
  String get t_sewing08_02 => '(a) Basting stitches\n(b) Reverse stitches\n(c) End of zipper opening\n(d) Wrong side';

  @override
  String get t_sewing08_03 => '\n2. Press the seam allowance open and attach the zipper with a basting stitch in the middle of each side of the zipper tape.';

  @override
  String get t_sewing08_04 => '(a) Basting stitches\n(b) Zipper\n(c) Wrong side';

  @override
  String get t_sewing08_05 => '\n3. Remove presser foot \"J\". Align the right side of the pin in zipper foot \"I\" with the presser foot holder, and attach the zipper foot.';

  @override
  String get t_sewing08_06 => '(a) Right\n(b) Left\n(c) Needle drop position';

  @override
  String get t_sewing08_07 => '4. Topstitch 7 to 10 mm (approx.1/4 to 3/8 inch) from the seamed edge of the fabric, then remove the basting.';

  @override
  String get t_sewing08_08 => '\n*Inserting a Side Zipper\nUse for side zippers in skirts or dresses.\n\n1. Attach presser foot \"J\" and sew straight stitches up to the zipper opening.\nChange to a basting stitch and sew to the top of the fabric.';

  @override
  String get t_sewing08_11 => '(a) Reverse stitches\n(b) Wrong side of fabric\n(c) Basting stitches\n(d) End of zipper opening';

  @override
  String get t_sewing08_12 => '\n2. Press the seam allowance open and align the folded hem along the teeth of the zipper, while maintaining 3 mm (approx.1/8 inch) of sewing space.';

  @override
  String get t_sewing08_13 => '(a) Zipper pull tab\n(b) Wrong side of fabric\n(c) Zipper teeth\n(d) End of zipper opening\n(e) 3 mm (1/8 inch) ';

  @override
  String get t_sewing08_14 => '\n3. Remove presser foot \"J\".\nAlign the right side of the pin in zipper foot \"I\" with the presser foot holder, and attach the presser foot.';

  @override
  String get t_sewing08_15 => '(a) Right\n(b) Left\n(c) Needle drop position';

  @override
  String get t_sewing08_16 => '\n4. Set the presser foot in the 3 mm (1/8 inch) margin.\nStarting from the end of the zipper opening, sew to a point about 50 mm (approx.2 inches) from the edge of the fabric, then stop the machine.\nPull down the zipper slider, then continue sewing to the edge of the fabric.';

  @override
  String get t_sewing08_17 => '(a) 50 mm (approx.2 inches) \n(b) 3 mm (approx.1/8 inch) ';

  @override
  String get t_sewing08_18 => '\n5. Close the zipper, turn the fabric over, and sew a basting stitch.';

  @override
  String get t_sewing08_19 => '(a) Front of the skirt (wrong side of fabric) \n(b) Basting stitches\n(c) Front of the skirt (right side of fabric) \n(d) Back of the skirt (right side of fabric) ';

  @override
  String get t_sewing08_20 => '\n6. Remove the presser foot, and reattach it so that the left side of the pin is attached to the presser foot holder.\n\n* When sewing the left side of the zipper, the needle should drop on the right side of the presser foot.\nWhen sewing the right side of the zipper, the needle should drop on the left side of the presser foot.';

  @override
  String get t_sewing08_21 => '(a) Right\n(b) Left\n(c) Needle drop position';

  @override
  String get t_sewing08_22 => '\n7. Set the fabric so that the left edge of the presser foot touches the edge of the zipper teeth.\nSew reverse stitches at the top of the zipper, then continue sewing.\nStop sewing about 50 mm (approx.2 inches) from the edge of the fabric, leave the needle in the fabric, and remove the basting stitches.\nOpen the zipper and sew the rest of the seam.';

  @override
  String get t_sewing08_23 => '(a) Basting stitches\n(b) 7 to 10 mm (approx.1/4 inch to 3/8 inch) \n(c) Reverse stitches\n(d) 50 mm (approx.2 inches) ';

  @override
  String get t_sewing09_00 => 'Sewing select type';

  @override
  String get t_sewing09_00_01 => 'Select from these stitches to sew the hems or cuffs of dresses, blouses, pants, or skirts.';

  @override
  String get t_sewing09_00_02 => '2-01:Other Fabric\n2-02:Stretch Fabric';

  @override
  String get t_sewing09_00_02_f_other => 'Other Fabric';

  @override
  String get t_sewing09_00_02_f_stretch => 'Stretch Fabric';

  @override
  String get t_sewing09 => '[Blind Hem Stitches]';

  @override
  String get t_sewing09_01 => '\n1. Place the fabric wrong side up, and fold and baste the fabric.';

  @override
  String get t_sewing09_02 => '(a) 5 mm (approx.3/16 inch) \n(b) Basting stitches\n(c) Wrong side of the fabric';

  @override
  String get t_sewing09_03 => '\n2. Attach blind hem stitch foot \"R\", and lower the presser foot.\nPosition the fabric so the folded edge touches the guide of the presser foot.';

  @override
  String get t_sewing09_04 => '(a) Guide\n(b) Fold';

  @override
  String get t_sewing09_05 => '\n3. Sew the fabric, keeping the folded edge in contact with the presser foot.';

  @override
  String get t_sewing09_06 => '(a) Needle position';

  @override
  String get t_sewing09_07 => '\n4. Remove the basting stitches and reverse the fabric.';

  @override
  String get t_sewing09_08 => '(a) Wrong side of fabric\n(b) Right side of fabric';

  @override
  String get t_sewing10 => '[Appliqué]';

  @override
  String get t_sewing10_01 => '\n1. Use a temporary spray adhesive, fabric glue or a basting stitch to attach the appliqué to the fabric.\nThis will keep the fabric from moving during sewing.';

  @override
  String get t_sewing10_02 => '(a) Appliqué\n(b) Fabric glue';

  @override
  String get t_sewing10_03 => '\n2. Attach presser foot \"J\". Check that the needle drops slightly off the edge of the appliqué, then begin sewing.';

  @override
  String get t_sewing10_04 => '(a) Appliqué material\n(b) Needle drop position';

  @override
  String get t_sewing10_06 => '*Sewing Sharp Curves\n';

  @override
  String get t_sewing10_07 => 'Stop the machine with the needle in the fabric outside the appliqué.\nRaise the presser foot and turn the fabric a little bit at a time while sewing for an attractive finish to the seam.';

  @override
  String get t_sewing11 => '[Pintuck]';

  @override
  String get t_sewing11_01 => '\n1. Mark along the folds on the wrong side of the fabric.';

  @override
  String get t_sewing11_01_02 => '(a) Wrong side';

  @override
  String get t_sewing11_02 => '\n2. Turn the fabric and iron the folded parts only.';

  @override
  String get t_sewing11_02_02 => '(a) Surface';

  @override
  String get t_sewing11_03 => '\n3. Attach presser foot \"I\".\nSew a straight stitch along the fold.';

  @override
  String get t_sewing11_04_02 => '(a) Width for pintuck\n(b) Wrong side\n(c) Surface';

  @override
  String get t_sewing11_05 => '4. Iron the folds in the same direction.';

  @override
  String get t_sewing12 => '[Gathering]';

  @override
  String get t_sewing12_00 => '\nUse on waists of skirts, sleeves of shirts, etc.';

  @override
  String get t_sewing12_01 => '\n1. Pull the bobbin and upper threads out by 50 mm (approx.1-15/16 inches) .';

  @override
  String get t_sewing12_01_02 => '(a) Upper thread\n(b) Bobbin thread\n(c) About 50 mm (approx.1-15/16 inches) ';

  @override
  String get t_sewing12_02 => '\n2. Sew two rows of straight stitches parallel to the seam line, then trim excess thread leaving 50 mm (approx.1-15/16 inches) .';

  @override
  String get t_sewing12_02_02 => '(a) Seam line\n(b) 10 to 15 mm (approx.3/8 inch to 9/16 inch) \n(c) About 50 mm (approx.1-15/16 inches) ';

  @override
  String get t_sewing12_03 => '3. Pull the bobbin threads to obtain the desired amount of gather, then tie the threads.';

  @override
  String get t_sewing12_04 => '4. Smooth the gathers by ironing them.\n';

  @override
  String get t_sewing12_05 => '5. Sew on the seam line and remove the basting stitch.';

  @override
  String get t_sewing13 => '[Dart Seam]';

  @override
  String get t_sewing13_01 => '\n1. Sew a reverse stitch at the beginning of the dart and then sew from the wide end to the other end without stretching the fabric.';

  @override
  String get t_sewing13_01_02 => '(a) Basting\n(b) Surface \n(c) Wrong side ';

  @override
  String get t_sewing13_02 => '2. Cut the thread at the end leaving 50 mm (approx.1-15/16 inches) , and then tie both ends together.\n\n* Do not sew a reverse stitch at the end.';

  @override
  String get t_sewing13_03 => '3. Insert the ends of the thread into the dart with a hand sewing needle.';

  @override
  String get t_sewing13_04 => '4. Iron the dart to one side so that it is flat.';

  @override
  String get t_sewing14 => '[Flat Fell Seam]';

  @override
  String get t_sewing14_00 => '\nUse for reinforcing seams and finishing edges neatly.\n\n1. Sew the finish line, then cut half of the seam allowance from the side that the flat fell seam will lie.';

  @override
  String get t_sewing14_01_02 => '(a) Wrong side\n(b) About 12 mm (approx.1/2 inch) ';

  @override
  String get t_sewing14_02 => '\n2. Spread the fabric out along the finish line.';

  @override
  String get t_sewing14_02_02 => '(a) Wrong side\n(b) Finish line';

  @override
  String get t_sewing14_03 => '\n3. Lay both seam allowances on the side of the shorter seam (cut seam) and iron them.';

  @override
  String get t_sewing14_03_02 => '(a) Wrong side';

  @override
  String get t_sewing14_04 => '\n4. Fold the longer seam allowance around the shorter one, and sew the edge of the fold.';

  @override
  String get t_sewing14_04_01 => '(a) Wrong side';

  @override
  String get t_sewing15_00 => 'Sewing select type';

  @override
  String get t_sewing15_00_01 => 'Q-01:Piecing stitch(Middle)\nQ-02:Piecing stitch(Right)\nQ-03:Piecing stitch(Left)';

  @override
  String get t_sewing15_00_01_s_piecingmiddle => 'Piecing stitch(Middle)';

  @override
  String get t_sewing15_00_01_s_piecingright => 'Piecing stitch(Right)';

  @override
  String get t_sewing15_00_01_s_piecingleft => 'Piecing stitch(Left)';

  @override
  String get t_sewing15 => '[Piecing]';

  @override
  String get t_sewing15_01 => '(a) Seam allowance: 6.5mm(approx.1/4 inch)\n       (when Q-02 is selected)\n(b) Align with right side of presser foot.\n\n1. Attach presser foot \"J\".\nSew with the edge of the fabric aligned with the side of the presser foot.';

  @override
  String get t_sewing15_012 => '(a) Seam allowance: 7mm\n       (when Q-02 is selected)\n(b) Align with right side of presser foot.\n\n1. Attach presser foot \"J\".\nSew with the edge of the fabric aligned with the side of the presser foot.';

  @override
  String get t_sewing15_01_02 => '(a) Seam allowance: 6.5mm(approx.1/4 inch)\n       (when Q-03 is selected)\n(b) Align with left side of presser foot.\n\n1. Attach presser foot \"J\".\nSew with the edge of the fabric aligned with the side of the presser foot.';

  @override
  String get t_sewing15_01_022 => '(a) Seam allowance: 7mm\n       (when Q-03 is selected)\n(b) Align with left side of presser foot.\n\n1. Attach presser foot \"J\".\nSew with the edge of the fabric aligned with the side of the presser foot.';

  @override
  String get t_sewing15_02 => '(a) Guide\n\nThis 1/4\" quilting foot with guide can sew an accurate 1/4 inch or 1/8 inch seam allowance.\nIt can be used for piecing together a quilt or for topstitching.\n\n1. Select Q-01, and then attach the 1/4\" quilting foot with guide.';

  @override
  String get t_sewing15_03 => '(a) Guide\n(b) 1/4 inch\n\n2. Use the guide on the presser foot and the marks to sew accurate seam allowances.\n\n\"Piecing a 1/4 inch seam allowance\"\nSew keeping the edge of the fabrics against the guide.';

  @override
  String get t_sewing15_04 => '(a) Align this mark with edge of fabric to begin\n(b) Beginning of stitching\n(c) End of stitching\n(d) Opposite edge of fabric to end or pivot\n(e) 1/4 inch\n\n\"Creating an accurate seam allowance\"\nUse the mark on the foot to begin, end, or pivot 1/4 inch from edge of fabric.';

  @override
  String get t_sewing15_05 => '(a) Surface of fabric\n(b) Seam\n(c) 1/8 inch\n\n\"Topstitching quilting, 1/8 inch\"\nSew with the edge of the fabric aligned with the left side of the presser foot end.';

  @override
  String get t_sewing16 => '[Quilting]';

  @override
  String get t_sewing16_01 => '1. Remove the presser foot, loosen the screw of the presser foot holder to remove the presser foot holder.\n\nSet the adapter on the presser foot bar, aligning the flat side of the adapter opening with the flat side of the presser bar. Push it up as far as possible, and then tighten the screw securely with the screwdriver.';

  @override
  String get t_sewing16_02 => '(a) Operation lever\n(b) Needle clamp screw\n(c) Fork\n(d) Presser foot bar\n\n2. Set the operation lever of the walking foot so that the needle clamp screw is set between the fork, and position the walking foot on the presser foot bar.';

  @override
  String get t_sewing16_03 => '3. Lower the presser foot lever. Tighten the screw securely with the screwdriver.';

  @override
  String get t_sewing16_04 => '4. Place one hand on each side of the presser foot to hold the fabric secure while sewing.';

  @override
  String get t_sewing16_05 => '* If \"AUTOMATIC FABRIC SENSOR SYSTEM\" in the machine settings screen is set to \"ON\", the fabric can be fed smoothly for best sewing results.';

  @override
  String get t_sewing17 => '[Free-motion quilting]';

  @override
  String get t_sewing17_00 => '(a) Free-motion quilting foot \"C\"\n(b) Free-motion open toe quilting foot \"O\"\n\nFor free-motion quilting, use free-motion quilting foot \"C\" and free-motion open toe quilting foot \"O\" depending on the stitch pattern that is selected.';

  @override
  String get t_sewing17_01 => '1. Press the free-motion mode key to set the machine to free-motion sewing mode.';

  @override
  String get t_sewing17_02_01 => '(a) Presser foot holder screw\n(b) Notch\n\n2. Attach free-motion quilting foot \"C\" at the front with the presser foot holder screw aligned with the notch in the foot.\nThen tighten the presser foot holder screw.';

  @override
  String get t_sewing17_02_02 => '(a) Pin\n(b) Needle clamp screw\n(c) Presser foot bar\n\nAttach free-motion open toe quilting foot \"O\" by positioning the pin of the foot above the needle clamp screw and aligning the lower-left of the foot and the presser bar.\nThen tighten the presser foot holder screw.';

  @override
  String get t_sewing17_03 => '(a) Stitch\n\n3. Use both hands to stretch the fabric taut, and then move the fabric at a consistent pace in order to sew uniform stitches roughly 2.0-2.5 mm (approx. 1/16 - 3/32 inch) in length.\n\n* We recommend attaching the foot controller and sewing at a consistent speed.';

  @override
  String get t_sewing18 => '[Echo quilting]';

  @override
  String get t_sewing18_00 => '(a) 6.4mm(approx.1/4 inch)\n(b) 9.5mm(approx.3/8 inch)\n\nFree-motion echo quilting foot \"E\".';

  @override
  String get t_sewing18_01 => '2. Remove the presser foot, loosen the presser foot holder screw, and then remove the screw and the presser foot holder.\n\nSet the adapter on the presser foot bar, aligning the flat side of the adapter opening with the flat side of the presser bar. Push it up as far as possible, and then tighten the screw securely with the screwdriver.';

  @override
  String get t_sewing18_02 => '3. Position the free-motion echo quilting foot \"E\" on the left side of the presser bar with the holes in the echo foot and presser bar aligned.\n\nTurn a supplied small screw 2 or 3 times with your hand.';

  @override
  String get t_sewing18_03 => '4. Tighten the screw.';

  @override
  String get t_sewing18_04 => '(a) 6.4mm(approx.1/4 inch)\n\n5. Using the measurement on the echo foot as a guide, sew around the motif.';

  @override
  String get t_sewing18_05 => 'Finished project';

  @override
  String get t_sewing19 => '[Appliqué]';

  @override
  String get t_sewing19_01 => '(a) Seam allowance:3 to 5mm\n\n1. Trace the pattern onto the appliqué fabric, and then cut around it.';

  @override
  String get t_sewing19_02 => '2. Cut a piece of thick paper or stabilizer to the finished size of the appliqué design, place it on the back of the appliqué, and then fold over the seam allowance using an iron.';

  @override
  String get t_sewing19_03 => '3. Turn the appliqué over and remove the stabilizer or paper.\nPin or baste the appliqué on to the main fabric.';

  @override
  String get t_sewing19_04 => '(a) Needle drop point\n\n4. Attach presser foot \"J\".\nCheck the needle drop point, and then sew along the edge of the appliqué while making sure that the needle drops slightly off the edge of the fabric.';

  @override
  String get t_explain_use => '[Use]';

  @override
  String get t_explain01_01 => 'General sewing, gather, pintuck etc. Reverse stitch is sewn while pressing \"Reverse/Reinforcement Stitch\" button.';

  @override
  String get t_explain01_01_01 => '\n* Changing the Needle Position \nWhen you select left or middle needle position stitches, you can use \"+\" and \"-\" keys in the L/R shift display to change the position of the needle.\nMatch the distance from the right edge of the presser foot to the needle with the seam allowance, then align the edge of the presser foot with the edge of the fabric during sewing for an attractive finish.';

  @override
  String get t_explain01_02 => 'General sewing, gather, pintuck, etc. Reinforcement stitch is sewn while pressing \"Reverse/Reinforcement Stitch\" button.';

  @override
  String get t_explain01_03 => 'General sewing, gather, pintuck, etc. Reverse stitch is sewn while pressing \"Reverse/Reinforcement Stitch\" button.';

  @override
  String get t_explain01_04 => 'General sewing, gather, pintuck, etc. Reinforcement stitch is sewn while pressing \"Reverse/Reinforcement Stitch\" button.';

  @override
  String get t_explain01_05 => 'General sewing for reinforcement and decorative topstitching.';

  @override
  String get t_explain01_06 => 'Reinforced stitching, sewing and decorative applications.';

  @override
  String get t_explain01_07 => 'Decorative stitching, top stitching.';

  @override
  String get t_explain01_08 => 'Basting.';

  @override
  String get t_explain01_09 => 'For overcasting, mending. Reverse stitch is sewn while pressing \"Reverse/Reinforcement Stitch\" button.';

  @override
  String get t_explain01_10 => 'For overcasting, mending. Reinforcement stitch is sewn while pressing \"Reverse/Reinforcement Stitch\" button.';

  @override
  String get t_explain01_11 => 'Start from right needle position, zigzag sew at left.';

  @override
  String get t_explain01_12 => 'Start from left needle position, zigzag sew at right.';

  @override
  String get t_explain01_13 => 'Overcasting (medium weight and stretch fabrics) , tape and elastic.';

  @override
  String get t_explain01_14 => 'Overcasting (medium, heavyweight and stretch fabrics), tape and elastic.';

  @override
  String get t_explain01_14a => 'Overcasting (medium, heavyweight and stretch fabrics), tape and elastic.';

  @override
  String get t_explain01_15 => 'Reinforcing of light and medium weight fabrics.';

  @override
  String get t_explain01_16 => 'Reinforcing of heavyweight fabric.';

  @override
  String get t_explain01_17 => 'Reinforcing of medium, heavyweight and easily friable fabrics or decorative stitching.';

  @override
  String get t_explain01_18 => 'Reinforced seaming of stretch fabric.';

  @override
  String get t_explain01_19 => 'Reinforcing of medium stretch fabric and heavyweight fabric, decorative stitching.';

  @override
  String get t_explain01_20 => 'Reinforcement of stretch fabric or decorative stitching.';

  @override
  String get t_explain01_21 => 'Stretch knit seam.';

  @override
  String get t_explain01_22 => 'Reinforcement and seaming stretch fabric.';

  @override
  String get t_explain01_23 => 'Reinforcement of stretch fabric.';

  @override
  String get t_explain01_24 => 'Straight stitch while cutting fabrics.';

  @override
  String get t_explain01_25 => 'Zigzag stitch while cutting fabrics.';

  @override
  String get t_explain01_26 => 'Overcasting stitch while cutting fabrics.';

  @override
  String get t_explain01_27 => 'Overcasting stitch while cutting fabrics.';

  @override
  String get t_explain01_28 => 'Overcasting stitch while cutting fabrics.';

  @override
  String get t_explain01_29 => 'Piecework/patchwork  6.5mm (1/4 inch) right seam allowance.';

  @override
  String get t_explain01_292 => 'Piecework/patchwork  7mm right seam allowance.';

  @override
  String get t_explain01_29a => 'Piecework/patchwork';

  @override
  String get t_explain01_30 => 'Piecework/patchwork 6.5mm (1/4 inch) left seam allowance.';

  @override
  String get t_explain01_302 => 'Piecework/patchwork 7mm left seam allowance.';

  @override
  String get t_explain01_31 => 'Quilting stitch made to look like hand quilting stitch.';

  @override
  String get t_explain01_32 => 'Zigzag stitch for quilting and sewing on appliqué quilt pieces.';

  @override
  String get t_explain01_33 => 'Quilting stitch for invisible appliqué or attaching binding.';

  @override
  String get t_explain01_34 => 'Background quilting.';

  @override
  String get t_explain02_01 => 'Hemming woven fabrics.';

  @override
  String get t_explain02_02 => 'Hemming stretch fabric.';

  @override
  String get t_explain02_03 => 'Appliqués, decorative blanket stitch.';

  @override
  String get t_explain02_04 => 'Shell tuck edge finish on fabrics.\nIncrease the upper thread tension for an attractive scallop finish to the shelltuck stitches.';

  @override
  String get t_explain02_05 => 'Decorating collar of blouse, edge of handkerchief.';

  @override
  String get t_explain02_06 => 'Decorating collar of blouse, edge of handkerchief.';

  @override
  String get t_explain02_07 => 'Patchwork stitches, decorative stitching.';

  @override
  String get t_explain02_08 => 'Patchwork stitches, decorative stitching.';

  @override
  String get t_explain02_09 => 'Decorative stitching, attaching cord and couching.';

  @override
  String get t_explain02_10 => 'Smocking, decorative stitching.';

  @override
  String get t_explain02_11 => 'Fagoting, decorative stitching.';

  @override
  String get t_explain02_12 => 'Fagoting, bridging and decorative stitching.';

  @override
  String get t_explain02_13 => 'Attaching tape to seam in stretch fabric.';

  @override
  String get t_explain02_14 => 'Decorative stitching.';

  @override
  String get t_explain02_15 => 'Decorative top stitching.';

  @override
  String get t_explain02_15a => 'Decorative stitching.';

  @override
  String get t_explain02_16 => 'Decorative stitching.';

  @override
  String get t_explain02_17 => 'Decorative stitching and attaching elastic.';

  @override
  String get t_explain02_18 => 'Decorative stitching and appliqué.';

  @override
  String get t_explain02_19 => 'Decorative stitching.';

  @override
  String get t_explain03_01 => 'Decorative hems, triple straight at left.';

  @override
  String get t_explain03_02 => 'Decorative hems, triple straight at center.';

  @override
  String get t_explain03_03 => 'Decorative hems, top stitching.';

  @override
  String get t_explain03_04 => 'Decorative hems, lace attaching pin stitch.';

  @override
  String get t_explain03_05 => 'Decorative hems.';

  @override
  String get t_explain03_06 => 'Decorative hems daisy stitch.';

  @override
  String get t_explain03_07 => 'Heirloom, decorative hems.';

  @override
  String get t_explain03_08 => 'Heirloom, decorative hems.';

  @override
  String get t_explain03_09 => 'Heirloom, decorative hems.';

  @override
  String get t_explain03_10 => 'Heirloom, decorative hems.';

  @override
  String get t_explain03_11 => 'Heirloom, decorative hems.';

  @override
  String get t_explain03_12 => 'Heirloom, decorative hems.';

  @override
  String get t_explain03_13 => 'Heirloom, decorative hems.';

  @override
  String get t_explain03_14 => 'Heirloom, decorative hems.';

  @override
  String get t_explain03_15 => 'Heirloom, decorative hems.';

  @override
  String get t_explain03_16 => 'Heirloom, decorative hems.';

  @override
  String get t_explain03_17 => 'Heirloom, decorative hems.';

  @override
  String get t_explain03_18 => 'Heirloom, decorative hems.';

  @override
  String get t_explain03_19 => 'Decorative hems and bridging stitch.';

  @override
  String get t_explain03_20 => 'Decorative hems. Fagoting, attaching ribbon.';

  @override
  String get t_explain03_21 => 'Decorative hems, smocking.';

  @override
  String get t_explain03_22 => 'Decorative hems, smocking.';

  @override
  String get t_explain03_23 => 'Decorative hems, smocking.';

  @override
  String get t_explain03_24 => 'Decorative hems.';

  @override
  String get t_explain03_25 => 'Decorative stitching.';

  @override
  String get t_explain04_01 => 'Buttonhole on light to medium weight fabrics.';

  @override
  String get t_explain04_02 => 'Buttonholes with extra space for larger buttons.';

  @override
  String get t_explain04_03 => 'Reinforced waist tapered buttonholes.';

  @override
  String get t_explain04_04 => 'Buttonholes with vertical bar tack in heavyweight fabrics.';

  @override
  String get t_explain04_05 => 'Buttonholes with bar tack.';

  @override
  String get t_explain04_06 => 'Buttonholes for fine, medium to heavy weight fabrics.';

  @override
  String get t_explain04_07 => 'Buttonholes for light to medium weight fabrics.';

  @override
  String get t_explain04_08 => 'Buttonholes with extra space for larger decorative buttons.';

  @override
  String get t_explain04_09 => 'Heavy-duty buttonholes with vertical bar tacks.';

  @override
  String get t_explain04_10 => 'Buttonholes for stretch or woven fabrics.';

  @override
  String get t_explain04_11 => 'Buttonholes for heirloom and stretch fabrics.';

  @override
  String get t_explain04_12 => 'The first step in making bound buttonholes.';

  @override
  String get t_explain04_13 => 'Buttonholes in heavyweight or thick fabrics for larger flat buttons.';

  @override
  String get t_explain04_14 => 'Buttonholes in medium to heavy weight fabrics for larger flat buttons.';

  @override
  String get t_explain04_15 => 'Buttonholes with vertical bar tack for reinforcement in heavyweight or thick fabrics.';

  @override
  String get t_explain04_15a => 'Left side of 4 step buttonhole.';

  @override
  String get t_explain04_15b => 'Bar tack of 4 step buttonhole.';

  @override
  String get t_explain04_15c => 'Right side of 4 step buttonhole.';

  @override
  String get t_explain04_15d => 'Bar tack of 4 step buttonhole.';

  @override
  String get t_explain04_16 => 'Darning of medium weight fabric.';

  @override
  String get t_explain04_17 => 'Darning of heavyweight fabric.';

  @override
  String get t_explain04_18 => 'Reinforcement at opening of pocket, etc.';

  @override
  String get t_explain04_19 => 'Attaching buttons.';

  @override
  String get t_explain04_20 => 'For making eyelet, holes on belt, etc.\nIf your sewing does not turn out well, adjust the stitch pattern.';

  @override
  String get t_explain04_21 => 'For making star-shaped eyelets or holes.\nIf your sewing does not turn out well, adjust the stitch pattern.';

  @override
  String get t_explain05_01 => 'For attaching appliqué on tubular pieces of fabric and mitering corners.';

  @override
  String get t_explain05_02 => 'For attaching appliqué on tubular pieces of fabric and mitering corners.';

  @override
  String get t_explain05_03 => 'For attaching appliqué on tubular pieces of fabric and mitering corners.';

  @override
  String get t_explain05_04 => 'For attaching appliqué on tubular pieces of fabric.';

  @override
  String get t_explain05_05 => 'For attaching appliqué on tubular pieces of fabric.';

  @override
  String get t_explain05_06 => 'For attaching appliqué on tubular pieces of fabric and mitering corners.';

  @override
  String get t_explain05_07 => 'For attaching appliqué on tubular pieces of fabric and mitering corners.';

  @override
  String get t_explain05_08 => 'For attaching appliqué on tubular pieces of fabric and mitering corners.';

  @override
  String get t_explain05_09 => 'For attaching appliqué on tubular pieces of fabric.';

  @override
  String get t_explain05_10 => 'For attaching appliqué on tubular pieces of fabric.';

  @override
  String get t_explain05_11 => 'For attaching appliqué on tubular pieces of fabric and mitering corners.';

  @override
  String get t_explain05_12 => 'For attaching appliqué on tubular pieces of fabric and mitering corners.';

  @override
  String get t_explain06_01 => 'For appliquéing with yarn, etc., to create decorative yarn embellishment with sewing free-motion.';

  @override
  String get t_explain06_02 => 'Free-Motion Basting\nWhen the feed dogs are lowered, basting can be sewn while freely moving the fabric.';

  @override
  String get t_explain06_03a => 'This stitch pattern is made up of several short stitches.\nSew this stitch pattern using transparent nylon thread or lightweight thread with a color matching the fabric for the upper thread to give your project the look of hand sewing. If the bobbin thread has a color different from the fabric, the stitch pattern will stand out.';

  @override
  String get t_explain07_01 => 'Appliqués, decorative stitch.';

  @override
  String get t_explain07_02 => 'Stitch pattern can be tapered at the beginning or at the end of sewing.';

  @override
  String get t_explaindeco00_01 => 'Sewing Attractive Finishes';

  @override
  String get t_explaindeco00_02 => 'Making Adjustments';

  @override
  String get t_explaindeco01_00 => '[Sewing Attractive Finishes]';

  @override
  String get t_explaindeco01_01 => 'To sew attractive finishes to your character/decorative stitches, check the fabric/needle/thread combinations. Other factors, such as fabric thickness, stabilizer material, etc., also have an effect on the stitch, so you should always sew a few trial stitches before beginning on your project.';

  @override
  String get t_explaindeco01_02 => '(a) Fabric\n(b) Stabilizer\n(c) Thin paper\n\n*Fabric\nWhen sewing on stretch fabrics, lightweight fabrics, or fabrics with coarse weaves, attach stabilizer material as an option. If you do not wish to do so, place the fabric on a thin paper such as tracing paper.\n\n*Thread\n#50 - #60\n\n*Needle\nWith lightweight, regular, or stretch fabrics: the Ball point needle (golden colored) \nWith heavyweight fabrics: home sewing machine needle 90/14\n\n*Presser foot\nMonogramming foot \"N+\". Using another presser foot may give inferior results.';

  @override
  String get t_explaindeco02_00 => '[Making Adjustments]';

  @override
  String get t_explaindeco02_01 => 'Your stitch pattern may sometimes turn out poorly, depending on the type or thickness of fabric, the stabilizer material used, sewing speed, etc.\nIf your sewing does not turn out well, sew trial stitches using the same conditions as the real sewing, and adjust the stitch pattern as explained below.\nIf the pattern does not turn out well even after making adjustments based on 6-120 pattern, make adjustments for each pattern individually.';

  @override
  String get t_explaindeco02_02 => '1. Select 6-120. Attach monogramming foot \"N+\" and sew the pattern.';

  @override
  String get t_explaindeco02_03_00 => '\n2. Compare the finished pattern to the illustration of the pattern below.';

  @override
  String get t_explaindeco02_04_00 => '[1. If the pattern is bunched]';

  @override
  String get t_explaindeco02_04_01 => 'Press + in the FINE ADJUST VERTI.\nSew the stitch pattern again.\nIf the stitch pattern still comes out poorly, make adjustments again. Adjust until the stitch pattern comes out correctly.';

  @override
  String get t_explaindeco02_05_00 => '[2. If the pattern has gaps]';

  @override
  String get t_explaindeco02_05_01 => 'Press - in the FINE ADJUST VERTI.\nSew the stitch pattern again.\nIf the stitch pattern still comes out poorly, make adjustments again. Adjust until the stitch pattern comes out correctly.';

  @override
  String get t_explaindeco02_06_00 => '[3. If the pattern is skewed to the left]';

  @override
  String get t_explaindeco02_06_01 => 'Press + in the FINE ADJUST HORIZ.\nIf the stitch pattern still comes out poorly, make adjustments again. Adjust until the stitch pattern comes out correctly.';

  @override
  String get t_explaindeco02_07_00 => '[4. If the pattern is skewed to the right]';

  @override
  String get t_explaindeco02_07_01 => 'Press - in the FINE ADJUST HORIZ.\nIf the stitch pattern still comes out poorly, make adjustments again. Adjust until the stitch pattern comes out correctly.';

  @override
  String get t_terms_read => 'Please read the following terms carefully.';

  @override
  String get t_terms_cert_read => 'Please read the following terms carefully.';

  @override
  String get t_terms_cert_01_00 => 'Certification of Upgrade KIT';

  @override
  String get t_terms_cert_01_01 => 'Terms on Certification of Upgrade KIT\n';

  @override
  String get t_terms_cert_01_02 => '  When you activate any optional functions in this software (“Software”), including, but not limited to, any paid licenses, manuals, documents, and other materials, and any of their updates (collectively, the “Tools”), you may be required, directly or indirectly, to provide certain license codes, product number, serial number, and other related information (“User Data”) in order to use the Tools.\n';

  @override
  String get t_terms_cert_01_03 => '  Some of the information included in the User Data can be associated with the data which you might register to Brother Industries, Ltd. (“Company”) or its subsidiary’s product registration website.  However, the Company will not use the User Data to identify you or for any other purpose other than to activate the Tools. The User Data may be transmitted to the Company’s administrative server or servers of cloud service providers such as Microsoft and Amazon, which may be located in countries without an adequate level of protection for personal data compared to that in your country. However, the Company will protect your User Data in accordance with the applicable law by using appropriate security measures to prevent unauthorized use or disclosure.';

  @override
  String get t_terms_nettool_read => 'Please read the following terms carefully.';

  @override
  String get t_terms_nettool_01_00 => 'Network Diagnosis Tool';

  @override
  String get t_terms_nettool_01_01 => 'Terms on Network Diagnosis Tool\n';

  @override
  String get t_terms_nettool_01_02 => '  When you have a problem with your network connection, you may choose to run the network diagnosis tool in this software (“Software”).  When you perform a network diagnosis, information from sewing or craft products (“Company Product”) and any devices connected to the Company Product, including, but not limited to, Internet Protocol (IP) or Media Access Control (MAC) address, proxy connection information, subnet mask, gateway, DNS server, and other related information (“Network-Related Information”) will be displayed on the screen.  ';

  @override
  String get t_terms_nettool_01_03 => '  When you have trouble with the internet connection of the Company Product and wish to obtain technical support, you may be asked to communicate your Network-Related Information to your local dealers or vendors, or Brother Industries, Ltd. (“Company”) and/or its subsidiaries in person, by telephone, by mail, by fax, or over the internet.  If you choose to provide your Network-Related Information, you agree and acknowledge that the Network-Related Information may be transferred to the Company or its subsidiaries solely for the purpose of analyzing or fixing your network problem, and that your information will be protected in accordance with the applicable law.\n  Other than as set forth herein, your Network-Related Information shall not be collected or stored by the Company or its subsidiaries, unless the Company and/or its subsidiaries obtains your prior approval separately.';

  @override
  String get t_terms_cert_read_t => 'Please read the following terms carefully.';

  @override
  String get t_terms_cert_01_01_t => 'Terms on Certification of Upgrade KIT\n';

  @override
  String get t_terms_cert_01_02_t => '  When you activate any optional functions in this software (“Software”), including, but not limited to, any paid licenses, manuals, documents, and other materials, and any of their updates (collectively, the “Tools”), you may be required, directly or indirectly, to provide certain license codes, product number, and serial number (“User Data”) in order to use the Tools.\n';

  @override
  String get t_terms_cert_01_03_t => '  Some of the information included in the User Data can be associated with the data which you might register to Tacony Corporation d/b/a Baby Lock (“Company”) product registration website.  However, the Company will not use the User Data to identify you or for any other purpose other than to activate the Tools. The User Data may be transmitted to the Company’s administrative server or servers of cloud service providers such as Microsoft and AWS, which may be located in countries without an adequate level of protection for personal data compared to that in your country.  However, the Company will protect your User Data in accordance with the applicable law by using appropriate security measures to prevent unauthorized use or disclosure.';

  @override
  String get t_terms_nettool_read_t => 'Please read the following terms carefully.';

  @override
  String get t_terms_nettool_01_01_t => 'Terms on Network Diagnosis Tool\n';

  @override
  String get t_terms_nettool_01_02_t => '  When you have a problem with your network connection, you may choose to run the network diagnosis tool in this software (“Software”).  When you perform a network diagnosis, information from sewing products (“Company Product”) and any devices connected to the Company Product, including, but not limited to, Internet Protocol (IP) or Media Access Control (MAC) address, proxy connection information, subnet mask, gateway, DNS server, and other related information (“Network-Related Information”) will be displayed on the screen.';

  @override
  String get t_terms_nettool_01_03_t => '  When you have trouble with the internet connection of the Company Product and wish to obtain technical support, you may be asked to communicate your Network-Related Information to your local retailer or Tacony Corporation d/b/a Baby Lock (“Company”) in person, by telephone, by mail, by fax, or over the internet.  If you choose to provide your Network-Related Information, you agree and acknowledge that the Network-Related Information may be transferred to the Company solely for the purpose of analyzing or fixing your network problem, and that your information will be protected in accordance with the applicable law.\n  Other than as set forth herein, your Network-Related Information shall not be collected or stored by the Company unless the Company obtains your prior approval separately.';

  @override
  String get t_terms_mnmpinmac_01_b => 'By clicking \"OK\", your PIN Code, MAC address and machine number will be sent to Brother\'s server to pair your sewing machine with your ScanNCut and your other sewing machines.\nThe information provided will not be used other than for the purposes stated above.';

  @override
  String get t_terms_snj_pair_01 => 'By clicking \"OK\", your PIN Code, MAC address, machine name and machine number will be sent to Brother\'s server to pair your sewing machine with your Brother devices/services. \nThe information provided will not be used other than for the purposes stated above.';

  @override
  String get upg_01 => 'Connect the USB media.';

  @override
  String get upg_02 => 'Cannot read file.';

  @override
  String get upg_03 => 'Cannot find correct file.';

  @override
  String get upg_04 => 'Check sum error';

  @override
  String get upg_05 => 'Failed to save file.';

  @override
  String get upg_06 => 'File address is incorrect.';

  @override
  String get upg_07 => 'Connected to PC. Do not disconnect the USB cable.';

  @override
  String get upg_08 => 'Saving the update file. \nDo not turn main power to OFF.';

  @override
  String get update_08 => 'Saving the update file. \nDo not turn main power to OFF.';

  @override
  String get upg_09 => 'Update complete.';

  @override
  String get update_09 => 'Update complete.';

  @override
  String get upg_10 => 'Press LOAD after connecting the USB media containing the update file.';

  @override
  String get update_10 => 'Press LOAD after connecting the USB media containing the update file.';

  @override
  String get upg_12 => 'Press LOAD to install the update file.';

  @override
  String get update_13 => 'Cannot update to the new version from the current version directly.';

  @override
  String get update_14 => 'Update the software to the version below by turning off the machine and then update with the USB media containing the update file.';

  @override
  String get update_15 => 'Turn off the machine and then update with the USB media containing the update file.';

  @override
  String get icon_00037 => 'Return';

  @override
  String get icon_00008_u => 'Close';

  @override
  String get icon_00009_u => 'Cancel';

  @override
  String get icon_00010_u => 'ＯＫ';

  @override
  String get icon_00050_u => 'Load';

  @override
  String get upg_16 => 'Update failed. \nPlease try again installing the update program.\n* If the problem persists, please redownload and install the program.';

  @override
  String get upg_17 => 'Update failed.\nPlease download and install the update program again.';

  @override
  String get upg_18 => 'ERR_UPEND';

  @override
  String get upg_19 => 'Please restart the machine.\nIt may take some time to start up the machine initially. The screen may go dark temporarily.';

  @override
  String get upg_20 => 'Do not turn off the machine even if the screen goes dark.';

  @override
  String get upg_21 => 'Failed to detect file system corruption.\nTurn the machine off, then on again.';

  @override
  String get upg_22 => 'Failed to repair corrupted system files.\nTurn the machine off, then on again.';

  @override
  String get upg_23 => 'Update failed.\nAfter starting the machine normally, turn off the machine and then try to install the update program again.';

  @override
  String get t_name_01_01 => 'Straight stitch (Left)';

  @override
  String get t_name_01_02 => 'Straight stitch (Left)';

  @override
  String get t_name_01_03 => 'Straight stitch (Middle)';

  @override
  String get t_name_01_04 => 'Straight stitch (Middle)';

  @override
  String get t_name_01_05 => 'Triple stretch stitch';

  @override
  String get t_name_01_06 => 'Stem stitch';

  @override
  String get t_name_01_07 => 'Decorative stitch';

  @override
  String get t_name_01_08 => 'Basting stitch';

  @override
  String get t_name_01_09 => 'Zigzag stitch';

  @override
  String get t_name_01_10 => 'Zigzag stitch';

  @override
  String get t_name_01_11 => 'Zigzag stitch (Right)';

  @override
  String get t_name_01_12 => 'Zigzag stitch (Left)';

  @override
  String get t_name_01_13 => '2 steps elastic zigzag ';

  @override
  String get t_name_01_14 => '2 steps elastic zigzag';

  @override
  String get t_name_01_14a => '3 steps elastic zigzag';

  @override
  String get t_name_01_15 => 'Overcasting stitch';

  @override
  String get t_name_01_16 => 'Overcasting stitch';

  @override
  String get t_name_01_17 => 'Overcasting stitch';

  @override
  String get t_name_01_18 => 'Overcasting stitch';

  @override
  String get t_name_01_19 => 'Overcasting stitch';

  @override
  String get t_name_01_20 => 'Overcasting stitch';

  @override
  String get t_name_01_21 => 'Overcasting stitch';

  @override
  String get t_name_01_22 => 'Single diamond overcast';

  @override
  String get t_name_01_23 => 'Single diamond overcast';

  @override
  String get t_name_01_24 => 'With side cutter';

  @override
  String get t_name_01_25 => 'With side cutter';

  @override
  String get t_name_01_26 => 'With side cutter';

  @override
  String get t_name_01_27 => 'With side cutter';

  @override
  String get t_name_01_28 => 'With side cutter';

  @override
  String get t_name_01_29 => 'Piecing stitch (Right)';

  @override
  String get t_name_01_29a => 'Piecing stitch (Middle)';

  @override
  String get t_name_01_30 => 'Piecing stitch (Left)';

  @override
  String get t_name_01_31 => 'Hand-look quilting';

  @override
  String get t_name_01_32 => 'Quilting appliqué zigzag stitch';

  @override
  String get t_name_01_33 => 'Quilting appliqué stitch';

  @override
  String get t_name_01_34 => 'Quilting stippling';

  @override
  String get t_name_02_01 => 'Blind hem stitch';

  @override
  String get t_name_02_02 => 'Blind hem stitch stretch';

  @override
  String get t_name_02_03 => 'Blanket stitch';

  @override
  String get t_name_02_03a => 'Blanket stitch';

  @override
  String get t_name_02_04 => 'Shell tuck edge';

  @override
  String get t_name_02_05 => 'Satin scallop stitch';

  @override
  String get t_name_02_06 => 'Scallop stitch';

  @override
  String get t_name_02_07 => 'Patchwork join stitch';

  @override
  String get t_name_02_08 => 'Patchwork double overlock stitch';

  @override
  String get t_name_02_09 => 'Couching stitch';

  @override
  String get t_name_02_10 => 'Smocking stitch';

  @override
  String get t_name_02_11 => 'Feather stitch';

  @override
  String get t_name_02_12 => 'Fagoting cross stitch';

  @override
  String get t_name_02_13 => 'Tape attaching';

  @override
  String get t_name_02_14 => 'Ladder stitch';

  @override
  String get t_name_02_15 => 'Rick-rack stitch';

  @override
  String get t_name_02_15a => 'Decorative stitch';

  @override
  String get t_name_02_16 => 'Decorative stitch';

  @override
  String get t_name_02_17 => 'Serpentine stitch';

  @override
  String get t_name_02_18 => 'Decorative stitch';

  @override
  String get t_name_02_19 => 'Decorative stippling stitch';

  @override
  String get t_name_03_01 => 'Hemstitching';

  @override
  String get t_name_03_02 => 'Hemstitching';

  @override
  String get t_name_03_03 => 'Hemstitching zigzag';

  @override
  String get t_name_03_04 => 'Hemstitching';

  @override
  String get t_name_03_05 => 'Hemstitching';

  @override
  String get t_name_03_06 => 'Hemstitching';

  @override
  String get t_name_03_07 => 'Hemstitching';

  @override
  String get t_name_03_08 => 'Hemstitching';

  @override
  String get t_name_03_09 => 'Hemstitching';

  @override
  String get t_name_03_10 => 'Hemstitching';

  @override
  String get t_name_03_11 => 'Hemstitching';

  @override
  String get t_name_03_12 => 'Honeycomb stitch';

  @override
  String get t_name_03_13 => 'Honeycomb stitch';

  @override
  String get t_name_03_14 => 'Hemstitching';

  @override
  String get t_name_03_15 => 'Hemstitching';

  @override
  String get t_name_03_16 => 'Hemstitching';

  @override
  String get t_name_03_17 => 'Hemstitching';

  @override
  String get t_name_03_18 => 'Hemstitching';

  @override
  String get t_name_03_19 => 'Hemstitching';

  @override
  String get t_name_03_20 => 'Hemstitching';

  @override
  String get t_name_03_21 => 'Hemstitching';

  @override
  String get t_name_03_22 => 'Hemstitching';

  @override
  String get t_name_03_23 => 'Hemstitching';

  @override
  String get t_name_03_24 => 'Hemstitching';

  @override
  String get t_name_03_25 => 'Ladder stitch';

  @override
  String get t_name_04_01 => 'Narrow rounded buttonhole';

  @override
  String get t_name_04_02 => 'Wide round ended buttonhole';

  @override
  String get t_name_04_03 => 'Tapered round ended buttonhole';

  @override
  String get t_name_04_04 => 'Round ended buttonhole';

  @override
  String get t_name_04_05 => 'Round ended buttonhole';

  @override
  String get t_name_04_06 => 'Round double ended buttonhole';

  @override
  String get t_name_04_07 => 'Narrow squared buttonhole';

  @override
  String get t_name_04_08 => 'Wide squared buttonhole';

  @override
  String get t_name_04_09 => 'Squared buttonhole';

  @override
  String get t_name_04_10 => 'Stretch buttonhole';

  @override
  String get t_name_04_11 => 'Heirloom buttonhole';

  @override
  String get t_name_04_12 => 'Bound buttonhole';

  @override
  String get t_name_04_13 => 'Keyhole buttonhole';

  @override
  String get t_name_04_14 => 'Tapered keyhole buttonhole';

  @override
  String get t_name_04_15 => 'Keyhole buttonhole';

  @override
  String get t_name_04_15a => '4 step buttonhole 1';

  @override
  String get t_name_04_15b => '4 step buttonhole 2';

  @override
  String get t_name_04_15c => '4 step buttonhole 3';

  @override
  String get t_name_04_15d => '4 step buttonhole 4';

  @override
  String get t_name_04_16 => 'Darning';

  @override
  String get t_name_04_17 => 'Darning';

  @override
  String get t_name_04_18 => 'Bar tack';

  @override
  String get t_name_04_19 => 'Button sewing';

  @override
  String get t_name_04_20 => 'Eyelet';

  @override
  String get t_name_04_21 => 'Star eyelet';

  @override
  String get t_name_05_01 => 'Diagonally left up (Straight)';

  @override
  String get t_name_05_02 => 'Reverse (Straight)';

  @override
  String get t_name_05_03 => 'Diagonally right up (Straight)';

  @override
  String get t_name_05_04 => 'Sideways to left (Straight)';

  @override
  String get t_name_05_05 => 'Sideways to right (Straight)';

  @override
  String get t_name_05_06 => 'Diagonally left down (Straight)';

  @override
  String get t_name_05_07 => 'Forward (Straight)';

  @override
  String get t_name_05_08 => 'Diagonally right down (Straight)';

  @override
  String get t_name_05_09 => 'Sideways to left (Zigzag)';

  @override
  String get t_name_05_10 => 'Sideways to right (Zigzag)';

  @override
  String get t_name_05_11 => 'Forward (Zigzag)';

  @override
  String get t_name_05_12 => 'Reverse (Zigzag)';

  @override
  String get t_name_06_01 => 'Free motion couching stitch';

  @override
  String get t_name_06_02 => 'Free-motion basting';

  @override
  String get t_name_06_03 => 'Hand-look quilting';

  @override
  String get t_name_06_04 => 'Hand-look quilting';

  @override
  String get t_name_06_05 => 'Hand-look quilting';

  @override
  String get t_name_06_06 => 'Needle felting stitch';

  @override
  String get t_name_07_01 => 'Appliqué stitch';

  @override
  String get t_name_07_02 => 'Tapering stitch';

  @override
  String get t_name_sr_01 => 'Straight stitch (Middle)';

  @override
  String get t_name_sr_02 => 'Zigzag stitch';

  @override
  String get t_name_sr_03 => 'Free-motion basting';

  @override
  String get tt_head_wifi => 'Wireless LAN settings';

  @override
  String get tt_head_camera => 'Camera view';

  @override
  String get tt_head_setting => 'Machine settings';

  @override
  String get tt_head_teaching => 'Machine help';

  @override
  String get tt_head_osae => 'Presser foot/Needle exchange';

  @override
  String get tt_head_lock => 'Screen lock';

  @override
  String get tt_head_home => 'Home Page';

  @override
  String get tt_foot_clock => 'Time/Date settings';

  @override
  String get tt_tch_og_principal_parts1 => '[Presser Foot Lever]';

  @override
  String get tt_tch_og_principal_parts2 => '[Sewing Speed Controller]';

  @override
  String get tt_tch_og_principal_parts3 => '[Handwheel]';

  @override
  String get tt_tch_og_principal_parts4 => '[Flat Bed Attachment With Accessory Compartment]';

  @override
  String get tt_tch_og_mb_knee_lifter => '[Knee Lifter]';

  @override
  String get tt_tch_og_principal_parts6 => '[Foot Controller]';

  @override
  String get tt_tch_og_principalbuttons1 => '[\"Needle Position\" Button]';

  @override
  String get tt_tch_og_principalbuttons2 => '[\"Thread Cutter\" Button]';

  @override
  String get tt_tch_og_principalbuttons3 => '[\"Presser Foot Lifter\" button]';

  @override
  String get tt_tch_og_principalbuttons4 => '[\"Automatic Threading\" button]';

  @override
  String get tt_tch_og_principalbuttons5 => '[\"Start/Stop\" Button]';

  @override
  String get tt_tch_og_principalbuttons6 => '[\"Reverse\" Stitch Button]';

  @override
  String get tt_tch_og_principalbuttons7 => '[\"Reinforcement/Tie-off\" Stitch button]';

  @override
  String get tt_tch_og_basic_operation1 => '[Upper Threading]';

  @override
  String get tt_tch_og_basic_operation2 => '[Bobbin Winding]';

  @override
  String get tt_tch_og_basic_operation3 => '[Changing the Needle]';

  @override
  String get tt_tch_og_basic_operation4 => '[Changing the Presser Foot]';

  @override
  String get tt_tch_og_basic_operation5 => '[Setting the Bobbin]';

  @override
  String get tt_tch_og_emb_basic_operation1 => '[Adjusting Thread Tension]';

  @override
  String get tt_tch_og_emb_basic_operation2 => '[Attaching Iron-on Stabilizers (backing) to the Fabric]';

  @override
  String get tt_tch_og_emb_basic_operation3 => '[Inserting the Fabric]';

  @override
  String get tt_tch_og_emb_basic_operation4 => '[Attaching & Removing the Embroidery Frame]';

  @override
  String get tt_tch_og_emb_basic_operation5 => '[Attaching the Embroidery Unit]';

  @override
  String get tt_tch_og_emb_basic_operation6 => '[Attaching Embroidery Foot \"W\"]';

  @override
  String get tt_tch_og_emb_basic_operation7 => '[Correct stabilizer to use]';

  @override
  String get tt_tch_maintenance1 => '[Cleaning the Race and Shuttle]';

  @override
  String get tt_utl_category01 => 'Straight/Overcasting';

  @override
  String get tt_utl_category02 => 'Decorative Stitches';

  @override
  String get tt_utl_category03 => 'Heirloom Stitches';

  @override
  String get tt_utl_category04 => 'Buttonholes/Bar tacks';

  @override
  String get tt_utl_category05 => 'Multi-directional Sewing';

  @override
  String get tt_utl_category_q => 'Quilting Stitches';

  @override
  String get tt_utl_category_s => 'Other Stitches';

  @override
  String get tt_utl_category_t => 'Stitch Tapering';

  @override
  String get tt_utl_stitchpreview => 'Preview';

  @override
  String get tt_utl_projecter => 'Projector functions';

  @override
  String get tt_utl_guideline => 'Guideline marker';

  @override
  String get tt_utl_editmenu => 'Edit menu';

  @override
  String get tt_utl_freemotion => 'Free motion mode';

  @override
  String get tt_utl_repeat_stitch_atamadashi => 'Back to beginning ';

  @override
  String get tt_utl_alone_repeat => 'Single/Repeat sewing';

  @override
  String get tt_utl_utilityflipvertical => 'Mirror image';

  @override
  String get tt_utl_twinneedle => 'Single needle/ twin needle';

  @override
  String get tt_utl_buttonholemanual => 'Slit length of buttonhole';

  @override
  String get tt_utl_endpointsetting => 'End point setting';

  @override
  String get tt_utl_tapering => 'Stitch tapering';

  @override
  String get tt_utl_autoreverse => 'Automatic reinforcement stitch';

  @override
  String get tt_utl_scissor => 'Automatic thread cutting';

  @override
  String get tt_utl_needlestopposition => 'Needle stop position setting';

  @override
  String get tt_utl_pivot => 'Pivot/Auto Up';

  @override
  String get tt_utl_threadcolor => 'Thread color change';

  @override
  String get tt_utl_category06 => 'Wide and Various';

  @override
  String get tt_utl_category07 => 'Wide Botanical';

  @override
  String get tt_utl_category08 => 'Wide Motifs and Messages';

  @override
  String get tt_utl_category09 => 'Narrow and Various';

  @override
  String get tt_utl_category10 => 'Narrow Botanical';

  @override
  String get tt_utl_category11 => 'Candlewicking';

  @override
  String get tt_utl_category12 => 'Large Satin';

  @override
  String get tt_utl_category13 => 'Satin';

  @override
  String get tt_utl_category14 => 'Cross Stitches';

  @override
  String get tt_utl_category15 => 'Combinable Utility';

  @override
  String get tt_utl_category16 => 'Disney';

  @override
  String get tt_utl_category17 => 'Gothic Font';

  @override
  String get tt_utl_category18 => 'Handwriting Font';

  @override
  String get tt_utl_category19 => 'Outline Font';

  @override
  String get tt_utl_category20 => 'Cyrillic font';

  @override
  String get tt_deco_category_pocket => 'Pocket(Machine\'s and external  memory)';

  @override
  String get tt_deco_mycustomsititch => ' “MY CUSTOM STITCH” function';

  @override
  String get tt_deco_stitchpreview => 'Preview';

  @override
  String get tt_deco_projecter => 'Projector functions';

  @override
  String get tt_deco_guidline => 'Guideline marker';

  @override
  String get tt_deco_editmenu => 'Edit menu';

  @override
  String get tt_deco_memory => 'Save stitch pattern data';

  @override
  String get tt_deco_threadcolor => 'Thread color change';

  @override
  String get tt_deco_stitchplus => 'Add stitch pattern';

  @override
  String get tt_deco_stitchselectall => 'Select all on/off';

  @override
  String get tt_deco_pivot => 'Pivot/Auto Up';

  @override
  String get tt_deco_needlestopposition => 'Needle stop position setting';

  @override
  String get tt_deco_scissor => 'Automatic thread cutting';

  @override
  String get tt_deco_autoreverse => 'Automatic reinforcement stitch';

  @override
  String get tt_deco_stitchstep1 => 'Step effect';

  @override
  String get tt_deco_stitchstep2 => 'Step effect';

  @override
  String get tt_deco_filemanager => 'File manager';

  @override
  String get tt_deco_filemanager_selectall => 'Select all';

  @override
  String get tt_deco_filemanager_selectnone => 'Deselect all';

  @override
  String get tt_deco_filemanager_delete => 'Delete';

  @override
  String get tt_deco_filemanager_memory => 'Save the selected stitch patterns in the machine\'s memory.';

  @override
  String get tt_deco_freemotion => 'Free motion mode';

  @override
  String get tt_deco_repeat_stitch_atamadashi => 'Back to beginning ';

  @override
  String get tt_deco_alone_repeat => 'Single/Repeat sewing';

  @override
  String get tt_deco_utilityfliphorizon => 'Horizontal mirror image';

  @override
  String get tt_deco_utilityflipvertical => 'Vertical mirror image';

  @override
  String get tt_deco_alone_single => 'Single needle/ twin needle';

  @override
  String get tt_deco_delete => 'Delete';

  @override
  String get tt_deco_density => 'Thread density';

  @override
  String get tt_deco_elongator => 'Elongation';

  @override
  String get tt_deco_spacing => 'Character spacing';

  @override
  String get tt_deco_stitchsizelink => 'Maintain aspect ratio';

  @override
  String get tt_deco_endpointsetting => 'End point setting';

  @override
  String get tt_mcs_triplesewing => 'Single/triple stitching';

  @override
  String get tt_mcs_pointdelete => 'Point delete';

  @override
  String get tt_mcs_blockmove => 'Block move';

  @override
  String get tt_mcs_insert => 'Insert';

  @override
  String get tt_utl_mcspointset => 'Set';

  @override
  String get tt_mcs_contents => 'Import stitch patterns';

  @override
  String get tt_mcs_memory => 'Save stitch pattern data';

  @override
  String get tt_utl_sr_guideline => 'Guideline marker';

  @override
  String get tt_utl_sr_sensingline => 'Sensing line';

  @override
  String get tt_utl_sr_srstatus => 'Stitch Regulator Status';

  @override
  String get tt_embcate_embpatterns => 'Embroidery patterns';

  @override
  String get tt_embcate_character => 'Character patterns';

  @override
  String get tt_embcate_decoalphabet => 'Decorative alphabet patterns';

  @override
  String get tt_embcate_frame => 'Frame patterns';

  @override
  String get tt_embcate_utility => 'Button hole patterns/ Utility embroidery patterns';

  @override
  String get tt_embcate_split => 'Split embroidery patterns';

  @override
  String get tt_embcate_long_stitch => 'Long-Stitch embroidery patterns';

  @override
  String get tt_embcate_quilt => 'Quilt sashes and edge-to-edge quilt patterns';

  @override
  String get tt_embcate_b_disney => 'Disney patterns';

  @override
  String get tt_embcate_couching => 'Couching patterns';

  @override
  String get tt_embcate_t_exclusives => 'Exclusives';

  @override
  String get tt_embcate_memory => 'Patterns saved in the machine’s memory, USB media, etc. ';

  @override
  String get tt_emb_pantool => 'Hand tool';

  @override
  String get tt_emb_backgroundscan => 'Fabric scan';

  @override
  String get tt_emb_realpreview => 'Preview';

  @override
  String get tt_emb_memory => 'Memory';

  @override
  String get tt_emb_redo => 'Redo';

  @override
  String get tt_emb_undo => 'Undo';

  @override
  String get tt_emb_delete => 'Delete';

  @override
  String get tt_emb_select => 'Select';

  @override
  String get tt_emb_multipleselect => 'Multiple selection';

  @override
  String get tt_emb_editsize => 'Size';

  @override
  String get tt_emb_editmove => 'Move';

  @override
  String get tt_emb_editgroup => 'Group/ungroup';

  @override
  String get tt_emb_editrotate => 'Rotate';

  @override
  String get tt_emb_editflip => 'Flip Horizontal';

  @override
  String get tt_emb_editduplicate => 'Duplicate';

  @override
  String get tt_emb_editdensity => 'Density';

  @override
  String get tt_emb_editborder => 'Border function (Designing Repeated Patterns)';

  @override
  String get tt_emb_editapplique => 'Appliqué piece';

  @override
  String get tt_emb_editchangecolor => 'Thread palette';

  @override
  String get tt_emb_edittextedit => 'Edit character patterns';

  @override
  String get tt_emb_editalign => 'Alignment';

  @override
  String get tt_emb_editstippling => 'Stippling';

  @override
  String get tt_emb_editoutline => 'Outline extraction';

  @override
  String get tt_emb_editorder => 'Embroidering order';

  @override
  String get tt_emb_editnotsew => 'No sew setting';

  @override
  String get tt_emb_textsize => 'Size';

  @override
  String get tt_emb_textarray => 'Array';

  @override
  String get tt_emb_textspacing => 'Character spacing';

  @override
  String get tt_emb_textalign => 'Alignment';

  @override
  String get tt_emb_embfootw => 'Needle point check';

  @override
  String get tt_emb_emb_projectorsetting => 'Projector settings';

  @override
  String get tt_emb_embprojector => 'Projector';

  @override
  String get tt_emb_embmove => 'Move';

  @override
  String get tt_emb_embrotate => 'Rotate';

  @override
  String get tt_emb_embbasting => 'Basting';

  @override
  String get tt_emb_embsnowman => 'Embroidery positioning';

  @override
  String get tt_emb_embonecolorsew => 'Uninterrupted embroidery';

  @override
  String get tt_emb_embcolorsorting => 'Color sorting';

  @override
  String get tt_emb_embconnectsew => 'Pattern connect';

  @override
  String get tt_emb_embframemove => 'Frame move: The frame will temporarily move to the center.';

  @override
  String get tt_emb_embmemory => 'Memory';

  @override
  String get tt_emb_embmasktrace => 'Trace area';

  @override
  String get tt_emb_embstartposition => 'Starting point';

  @override
  String get tt_emb_embneedlenumber => 'Forward/Back';

  @override
  String get tt_emb_embfbcamera => 'Camera view';

  @override
  String get tt_emb_embthreadcutting => 'Cut/Tension';

  @override
  String get tt_emb_embcolorbar => 'One color/ all colors for progress bar';

  @override
  String get tt_emb_patterninfo => 'Pattern information';

  @override
  String get tt_emb_previewsim => 'Stitch simulator';

  @override
  String get tt_emb_sewtrim_endcolor => 'End Color Trim';

  @override
  String get tt_emb_sewtrim_jumpstitch => 'Jump Stitch Trim';

  @override
  String get tt_emb_previewframe => 'Preview embroidery frame ';

  @override
  String get tt_emb_size_normalstb => 'Change the pattern size while maintaining the number of stitches/ the thread density';

  @override
  String get tt_emb_edit_border_vert => 'Pattern repeat/ delete vertically';

  @override
  String get tt_emb_edit_border_horiz => 'Pattern repeat/ delete horizontally';

  @override
  String get tt_emb_edit_border_dividervert => 'Pattern cut vertically';

  @override
  String get tt_emb_edit_border_dividehoriz => 'Pattern cut horizontally';

  @override
  String get tt_emb_edit_border_threadmark => 'Thread marks';

  @override
  String get tt_emb_edit_border_reset => 'Reset';

  @override
  String get tt_emb_emb_rotate_reset => 'Reset';

  @override
  String get tt_emb_edit_rotate_reset => 'Reset';

  @override
  String get tt_emb_camera_rotate_reset => 'Reset';

  @override
  String get tt_emb_edit_font_spacing_reset => 'Reset';

  @override
  String get tt_emb_edit_align_reset => 'Reset';

  @override
  String get tt_emb_edit_size_reset => 'Reset';

  @override
  String get tt_emb_edit_order_reset => 'Reset';

  @override
  String get tt_emb_quiltborder_color_reset => 'Reset';

  @override
  String get tt_emb_edit_color_reset => 'Reset';

  @override
  String get tt_emb_photositich_size_change_reset => 'Reset';

  @override
  String get tt_emb_edit_projlcd_switch_fb_reset => 'Reset';

  @override
  String get tt_emb_edit_projlcd_align_reset => 'Reset';

  @override
  String get tt_emb_edit_projlcd_border_reset => 'Reset';

  @override
  String get tt_emb_edit_projlcd_rotate_reset => 'Reset';

  @override
  String get tt_emb_edit_projlcd_size_reset => 'Reset';

  @override
  String get tt_mdc_paint_rotate_reset => 'Reset';

  @override
  String get tt_mdc_paint_size_input_reset => 'Reset';

  @override
  String get tt_mdc_paint_size_reset => 'Reset';

  @override
  String get tt_emb_newapplique_color_selectall => 'Select all';

  @override
  String get tt_emb_newapplique_color_selectnone => 'Deselect all';

  @override
  String get tt_emb_color_selectall => 'Select single color/ all colors';

  @override
  String get tt_emb_colorcolorshuffling => 'Color Shuffling';

  @override
  String get tt_emb_colorvisualizer => 'Color Visualizer';

  @override
  String get tt_emb_editselectall => 'Select all';

  @override
  String get tt_emb_editdeselectall => 'Deselect all';

  @override
  String get tt_emb_infoprintimage => 'Combined with the print image';

  @override
  String get tt_emb_infooutputfiles => '3 PDF files (for printable fabric/ an ironon transfer/ positioning) are copied onto the USB media.';

  @override
  String get tt_emb_filemanager => 'File manager';

  @override
  String get tt_emb_filemanager_selectall => 'Select all';

  @override
  String get tt_emb_filemanager_selectnone => 'Deselect all';

  @override
  String get tt_emb_filemanager_delete => 'Delete';

  @override
  String get tt_emb_filemanager_memory => 'Save the selected patterns in the machine\'s memory.';

  @override
  String get tt_emb_easystippling_stippling => 'Stippling pattern';

  @override
  String get tt_emb_easystippling_echo => 'Echo quilting pattern';

  @override
  String get tt_emb_easystippling_decorativefill => 'Decorative fill pattern';

  @override
  String get tt_emb_quitlsash_startpoint => 'Project start point';

  @override
  String get tt_emb_quitlsash_endtpoint => 'Project end point';

  @override
  String get tt_emb_connect_migimawari => 'The second pattern position moves in a clockwise';

  @override
  String get tt_emb_connect_hidarimawari => 'The second pattern position moves in a counterclockwise';

  @override
  String get tt_emb_connect_rotate => 'Rotate';

  @override
  String get tt_emb_quiltborder_save => 'Memory';

  @override
  String get tt_mdc_pantool => 'Hand tool';

  @override
  String get tt_mdc_scanmenu => 'Custom patterns can be created using scanned images or image data files.';

  @override
  String get tt_mdc_datacall => 'Recall pattern drawing data (.pm9)';

  @override
  String get tt_mdc_linetool => 'Line tool';

  @override
  String get tt_mdc_lineproperty => 'Line Property';

  @override
  String get tt_mdc_linespoit => 'Dropper tool for line';

  @override
  String get tt_mdc_linepouring => 'Bucket tool for line';

  @override
  String get tt_mdc_brushtool => 'Brush tool';

  @override
  String get tt_mdc_brushproperty => 'Region Property';

  @override
  String get tt_mdc_brushspoit => 'Dropper tool for region';

  @override
  String get tt_mdc_brushpouring => 'Bucket tool for region';

  @override
  String get tt_mdc_painteraser => 'Eraser';

  @override
  String get tt_mdc_paintstamp => 'Stamp shapes';

  @override
  String get tt_mdc_paintsize => 'Size';

  @override
  String get tt_mdc_paintrotate => 'Rotate';

  @override
  String get tt_mdc_paintflip => 'Mirror image';

  @override
  String get tt_mdc_paintduplicate => 'Duplicate';

  @override
  String get tt_mdc_paintcut => 'Cut';

  @override
  String get tt_mdc_paintpaste => 'Paste';

  @override
  String get tt_mdc_memory => 'Save the pattern drawing data (.pm9) ';

  @override
  String get tt_mdc_select => 'Select';

  @override
  String get tt_mdc_redo => 'Redo';

  @override
  String get tt_mdc_undo => 'Undo';

  @override
  String get tt_mdc_allclear => 'All Clear';

  @override
  String get tt_mdc_lineopen => 'Freehand line with the end open';

  @override
  String get tt_mdc_lineclose => 'Freehand line closing the end automatically';

  @override
  String get tt_mdc_lineline => 'Straight line with one stroke';

  @override
  String get tt_mdc_linepolygonal => 'Polygonal shape';

  @override
  String get tt_mdc_stitchzigzag => 'Zigzag stitch';

  @override
  String get tt_mdc_stitchrunning => 'Running stitch';

  @override
  String get tt_mdc_stitchtriple => 'Triple stitch';

  @override
  String get tt_mdc_stitchcandle => 'Candlewicking stitch';

  @override
  String get tt_mdc_stitchchain => 'Chain stitch';

  @override
  String get tt_mdc_stitchestitch => 'E stitch';

  @override
  String get tt_mdc_stitchvsitich => 'V stitch';

  @override
  String get tt_mdc_stitchmotif => 'Motif stitches';

  @override
  String get tt_mdc_stitchnnotsew => 'Line without stitch';

  @override
  String get tt_mdc_stitchzigzaglowdensity => 'Appliqué zigzag stitch';

  @override
  String get tt_mdc_regiontatami => 'Fill stitch pattern';

  @override
  String get tt_mdc_regionstippling => 'Stippling pattern';

  @override
  String get tt_mdc_regiondecorativefill => 'Decorative fill patterns';

  @override
  String get tt_mdc_regionnotsew => 'No stitches';

  @override
  String get tt_mdc_stamp1 => 'Basic shapes';

  @override
  String get tt_mdc_stamp2 => 'Closed shapes';

  @override
  String get tt_mdc_stamp3 => 'Open shapes';

  @override
  String get tt_mdc_stamp4 => 'Saved outlines';

  @override
  String get tt_mdc_stamp5 => 'Frame embroidering areas';

  @override
  String get tt_mdc_stamp6 => 'Cutting outlines';

  @override
  String get tt_mdc_select_rectangle => 'Box selection';

  @override
  String get tt_mdc_select_continuousrectangle => 'Polygonal shape selection';

  @override
  String get tt_mdc_select_free => 'Freestyle curve selection';

  @override
  String get tt_mdc_select_auto => 'Automatic selection';

  @override
  String get tt_mdc_select_all => 'Select all';

  @override
  String get tt_mdc_memory_drawemb => 'Save the pattern drawing data (.pm9) and embroidery data (.phx).';

  @override
  String get tt_mdc_embset_pantool => 'Hand tool';

  @override
  String get tt_mdc_embset_patterninfo => 'Pattern information';

  @override
  String get tt_mdc_embset_realpreview => 'Preview';

  @override
  String get tt_mdc_embset_projector => 'Projector';

  @override
  String get tt_mdc_embset_projectorsetting => 'Projector settings';

  @override
  String get tt_mdc_zigzagwidth => 'Zigzag width';

  @override
  String get tt_mdc_zigzagdensity => 'Density';

  @override
  String get tt_mdc_runpitch => 'Run pitch';

  @override
  String get tt_mdc_running_undersew => 'Under sewing';

  @override
  String get tt_mdc_candlewicksize => 'Size';

  @override
  String get tt_mdc_candlewickspacing => 'Spacing';

  @override
  String get tt_mdc_chainsize => 'Size';

  @override
  String get tt_mdc_chainthickness => 'Thickness';

  @override
  String get tt_mdc_estitchwidth => 'Stitch width';

  @override
  String get tt_mdc_estitchspacing => 'Spacing';

  @override
  String get tt_mdc_estitchthickness => 'Thickness';

  @override
  String get tt_mdc_estitchflip => 'Flip';

  @override
  String get tt_mdc_vstitchwidth => 'Stitch width';

  @override
  String get tt_mdc_vstitchspacing => 'Spacing';

  @override
  String get tt_mdc_vstitchthickness => 'Thickness';

  @override
  String get tt_mdc_vstitchflip => 'Flip';

  @override
  String get tt_mdc_motifstitchsize => 'Size';

  @override
  String get tt_mdc_motifstitchspacing => 'Spacing';

  @override
  String get tt_mdc_motifstitchflip => 'Flip';

  @override
  String get tt_mdc_zigzagwidth_2 => 'Zigzag width';

  @override
  String get tt_mdc_zigzagdensity_2 => 'Density';

  @override
  String get tt_mdc_tatamiderection => 'Direction';

  @override
  String get tt_mdc_tatamidensity => 'Density';

  @override
  String get tt_mdc_tatamipullconpen => 'Pull compensation';

  @override
  String get tt_mdc_tatamiundersewing => 'Under sewing';

  @override
  String get tt_mdc_stiprunpitch => 'Run pitch';

  @override
  String get tt_mdc_stipspacing => 'Spacing';

  @override
  String get tt_mdc_stipdistance => 'Distance';

  @override
  String get tt_mdc_stipsingletriple => 'Single/Triple stitch';

  @override
  String get tt_mdc_decofillsize => 'Size';

  @override
  String get tt_mdc_decofilldirection => 'Direction';

  @override
  String get tt_mdc_decofilloutline => 'Outline for reducing thread cut';

  @override
  String get tt_mdc_decofillrandomshift => 'Random shift';

  @override
  String get tt_mdc_decofillpositionoffset => 'Position offset';

  @override
  String get tt_mdc_decofillthickness1 => 'Thickness';

  @override
  String get tt_mdc_decofillthickness3 => 'Thickness';

  @override
  String get tt_mdc_decofillthickness1_2 => 'Single-double';

  @override
  String get tt_mdc_decofillthickness2_3 => 'Double-triple';

  @override
  String get tt_mdc_stitchlink => 'Select objects with the same stitch settings at once';

  @override
  String get tt_mdc_fill_linereading => 'Linear areas and perimeters are converted to outlines. Choose outline thickness.';

  @override
  String get tt_mdc_fill_linecolor => 'The extracted outlines with the specified color are converted to line attributes.';

  @override
  String get tt_emb_photostitch_backremoval => 'Background removal';

  @override
  String get tt_emb_photostitch_framing => 'Framing the image';

  @override
  String get tt_emb_photostitch_fittoframe => 'Fit to frame';

  @override
  String get tt_emb_photostitch_backremoval_scopeplus => 'Add a new cropping area:Mark the area you want to crop with a line.';

  @override
  String get tt_emb_photostitch_backremoval_scopeminus => 'Clear the cropping area:Mark the area you do not crop with a line.';

  @override
  String get tt_emb_photostitch_backremoval_erase => 'Delete the specified drawing line.';

  @override
  String get tt_emb_photostitch_backremoval_trash => 'Delete all the drawing lines.';

  @override
  String get tt_emb_photostitch_backremoval_blind => 'Show/ Hide all lines drawn with pens.';

  @override
  String get tt_emb_photostitch_styleusecolor => 'ON: Use colors of style image/ OFF: Use colors of original photo';

  @override
  String get tt_emb_photostitch_colortune => 'Color adjustment';

  @override
  String get tt_emb_photostitch_colorlis_allselect => 'Keep/ Clear all thread colors in the color list';

  @override
  String get tt_emb_photostitch_colorlis_add => 'Add a color to the color list';

  @override
  String get tt_emb_photostitch_colorlis_remove => 'Remove the selected color from the color list';

  @override
  String get tt_emb_photostitch_pantool => 'Hand tool';

  @override
  String get tt_emb_photostitch_memory => 'Memory';

  @override
  String get tt_emb_edit_projectorsetting => 'Projector settings';

  @override
  String get tt_emb_edit_projector => 'Projector';

  @override
  String get tt_settings_reset_3type => 'Reset the settings (Sewing/ General /Embroidery)';

  @override
  String get tt_settings_screenimage_usb => 'Save a Settings Screen Image to USB Media';

  @override
  String get tt_camera_emb_screenshot => 'Save a camera image to the USB media.';

  @override
  String get tt_camera_emb_grid => 'Show/ Hide Grid';

  @override
  String get tt_camera_emb_needlepoint => 'Show/ Hide the needle drop point';

  @override
  String get tt_camera_util_screenshot => 'Save a camera image to the USB media.';

  @override
  String get tt_camera_util_grid => 'Show/ Hide Grid';

  @override
  String get tt_camera_util_needlepoint => 'Show/ Hide the needle drop point';
}
