﻿import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';

import 'theme_button.dart';

// ignore: camel_case_types
class grp_btn_photostitch extends StatelessWidget {
  const grp_btn_photostitch({
    super.key,
    this.text = '',
    this.onTap,
    this.state = ButtonState.normal,
    this.feedBackControl,
  });

  final void Function()? onTap;
  final ButtonState state;
  final String text;
  final FeedBackControl? feedBackControl;
  @override
  Widget build(BuildContext context) => FeedBackButton(
        onTap: state == ButtonState.disable ? null : () => onTap?.call(),
        state: state,
        feedBackControl: feedBackControl,
        style: ThemeButton.btn_n_size182x80_theme1,
        child: Column(
          children: [
            const Spacer(flex: 4),
            Expanded(
              flex: 72,
              child: Row(
                children: [
                  const Spacer(flex: 5),
                  Expanded(
                    flex: 172,
                    child: Text(
                      textAlign: TextAlign.center,
                      text,
                      style: const TextStyle(
                        fontSize: 28,
                        color: Colors.black,
                        height: 37 / 28,
                      ),
                    ),
                  ),
                  const Spacer(flex: 5),
                ],
              ),
            ),
            const Spacer(flex: 4),
          ],
        ),
      );
}
