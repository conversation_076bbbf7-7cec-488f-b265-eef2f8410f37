import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'bottom_view_model.dart';

class Bottom extends ConsumerStatefulWidget {
  const Bottom({super.key});
  @override
  ConsumerState<Bottom> createState() => _BottomState();
}

class _BottomState extends ConsumerState<Bottom> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(bottomProvider);
    final viewModel = ref.read(bottomProvider.notifier);

    return Column(
      children: [
        const Spacer(flex: 89),
        Expanded(
          flex: 80,
          child: Row(
            children: [
              const Spacer(flex: 12),
              Expanded(
                flex: 152,
                child: grp_btn_return_mdc(
                  onTap: viewModel.onReturnButtonClicked,
                  text: l10n.icon_return,
                  feedBackControl: FeedBackControl.onlyDisable,
                ),
              ),
              const Spacer(flex: 102),
              Expanded(
                flex: 84,
                child: Visibility(
                  visible: state.isShowUndoRedoButton,
                  child: grp_btn_undo(
                    onTap: viewModel.onUndoButtonClick,
                    state: state.undoButtonState,
                  ),
                ),
              ),
              const Spacer(flex: 8),
              Expanded(
                flex: 84,
                child: Visibility(
                  visible: state.isShowUndoRedoButton,
                  child: grp_btn_redo(
                    onTap: viewModel.onRedoButtonClick,
                    state: state.redoButtonState,
                  ),
                ),
              ),
              const Spacer(flex: 100),
              Expanded(
                flex: 84,
                child: CustomTooltip(
                  message: l10n.tt_mdc_memory_drawemb,
                  child: grp_btn_memory(
                    state: viewModel.isProcessImage
                        ? ButtonState.disable
                        : ButtonState.normal,
                    onTap: () => viewModel.onMemoryButtonClick(context),
                    feedBackControl: const FeedBackControl(
                        normalSound: SystemSoundEnum.none),
                  ),
                ),
              ),
              const Spacer(flex: 10),
              Expanded(
                flex: 152,
                child: grp_btn_set_mdc(
                  feedBackControl: null,
                  onTap: () => viewModel.onSetButtonClick(context),
                  text: l10n.icon_00038_1,
                ),
              ),
              const Spacer(flex: 12),
            ],
          ),
        ),
        const Spacer(flex: 10),
      ],
    );
  }
}
