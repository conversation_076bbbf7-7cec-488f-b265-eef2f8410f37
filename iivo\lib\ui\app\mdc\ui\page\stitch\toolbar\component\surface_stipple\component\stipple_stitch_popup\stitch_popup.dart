import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'stitch_popup_view_model.dart';

class StitchPopup extends ConsumerStatefulWidget {
  const StitchPopup({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState<StitchPopup> createState() => _StitchState();
}

class _StitchState extends ConsumerState<StitchPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(stitchViewModelProvider);
    final viewModel = ref.read(stitchViewModelProvider.notifier);

    return Row(
      children: [
        const Spacer(
          flex: 571,
        ),
        Expanded(
          flex: 229,
          child: Column(
            children: [
              const Spacer(
                flex: 159,
              ),
              Expanded(
                flex: 1052,
                child: Scaffold(
                  backgroundColor: Colors.transparent,
                  body: pre_edit_toolbar_mdc(
                    child: Column(
                      children: [
                        const Spacer(
                          flex: 129,
                        ),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_str_stitch_mdc(
                                  text: l10n.icon_stitch,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 89,
                        ),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 98,
                                child: grp_btn_mdc_stip_single(
                                  onTap: () => viewModel.onSingleButtonClick(),
                                  state: state.isSelectSingle
                                      ? ButtonState.select
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 9,
                              ),
                              Expanded(
                                flex: 98,
                                child: grp_btn_mdc_stip_triple(
                                  onTap: () => viewModel.onTripleButtonClick(),
                                  state: state.isSelectTriple
                                      ? ButtonState.select
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              )
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 613,
                        ),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_btn_positive_mdc(
                                  onTap: viewModel.onOkButtonClicked,
                                  text: l10n.icon_ok,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 12,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const Spacer(
                flex: 69,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
