## 1.0.37

Library更新
- soファイル更新

変更時刻 : 2025-7-22

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/222
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/901
## 1.0.36

Library更新
- soファイル更新

変更時刻 : 2025-7-14

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/221
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/898
## 1.0.35

Library更新
- soファイル更新

変更時刻 : 2025-7-14

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/220
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/897

## 1.0.34

Library更新
- soファイル更新

変更時刻 : 2025-7-9

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/217
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/893

## 1.0.33

Library更新
- soファイル更新

変更時刻 : 2025-7-8

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/216
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/892

## 1.0.32

Library更新
- TpdLibrary
  - 関数：setMatrixEnableList戻り値を追加されます
  - 構造体：DirErrorCode_t
      メンバー「dirMotorError」が追加されます
      メンバー「dirNoError」が追加されます
- soファイル更新

変更時刻 : 2025-7-3

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/215
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/888

## 1.0.31

Library更新
- EmbLibrary
  - 関数：deleteEmbGrpPatternInfo
      パラメータ変更
  - 関数：selectEditFileEmbHFree
      新規追加
  - 関数：freeMemHandle
      新規追加
- TpdLibrary
  - 構造体：BPIFSendKey
      メンバー「KEYCLEARAPPERRORSTATE」が追加されます   
- soファイル更新

変更時刻 : 2025-6-30

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/213
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/884


## 1.0.30

Library更新
- DeviceLibrary
  - 関数：UserSettingItemValueListFree
      新規追加
- EmbLibrary
  - 関数：deleteEmbGrpPatternInfo
      新規追加
  - 関数：goAppliqueParameterSetting
      新規追加

- soファイル更新

変更時刻 : 2025-6-25

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/212
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/878
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/879

## 1.0.29

Library更新
- EmbLibrary
  - 関数：cancelSelectedApplique
      新規追加
  - 構造体：embErrorCode_t
    メンバー「EMB_ALL_GROUP_MOVE_TO_LIMIT」が追加されます 

- soファイル更新

変更時刻 : 2025-6-23

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/211
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/875

## 1.0.28

Library更新
- EmbLibrary
  - 構造体：embErrorCode_t
      メンバー「EMB_MDC_TOO_ALLOC_FAILURE」が追加されます 
      メンバー「EMB_MDC_CANCEL_B」が追加されます 

- soファイル更新

変更時刻 : 2025-6-19

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/210
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/873

## 1.0.27

Library更新
- EmbLibrary
  - 関数：deleteBorderMarkForStippleError新規追加 
  - 構造体：embErrorCode_t
      メンバー「EMB_QUILT_THIS_PATTERN_NOT_USE」が追加されます 

- soファイル更新

変更時刻 : 2025-6-17

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/208
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/870

## 1.0.26

Library更新
- soファイル更新

変更時刻 : 2025-6-16

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/207
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/868

## 1.0.25

Library更新
- ProjectorLibrary
  - 関数：resetProjectorEmbPatternProjectionArea
      コメントアウトされている
- TpdLibrary
  - 構造体：BPIFSendKey
      内容が変更されました  

- soファイル更新

変更時刻 : 2025-6-13

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/206
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/865

## 1.0.24

Library更新
- EmbPhotoStitchLibrary
  - 構造体：PhotoConvertEmb_t
      widthDpi と heightDpi の型の変更
 
- soファイル更新

変更時刻 : 2025-6-11

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/203
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/860

## 1.0.23

Library更新
- TpdLibrary
  - 関数：setMatrixEnableListNotOverwrited戻り値の変更
 
- soファイル更新

変更時刻 : 2025-6-09

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/202
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/858


## 1.0.22

Library更新
- TpdLibrary
  - 関数：setMatrixEnableListNotOverwrited新規追加
  - 関数：clearMatrixEnableListNotOverwrited新規追加
  - 構造体：ErrCode_t
      メンバー「ERR_APP_ALL_LOCK」が追加されます   
- soファイル更新

変更時刻 : 2025-6-05

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/201

## 1.0.21

Library更新
- soファイル更新

変更時刻 : 2025-6-03

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/200

## 1.0.20

Library更新
- soファイル更新

変更時刻 : 2025-5-27

変更詳細は　以下一つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/199


## 1.0.19

Library更新
- soファイル更新

変更時刻 : 2025-5-23

変更詳細は　以下3つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/850
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/851
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/198


## 1.0.18

Library更新
- EmbLibrary
  - 関数：resetEmbRotate新規追加 
- soファイル更新

変更時刻 : 2025-5-20

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/847

## 1.0.17

Library更新
- TpdLibrary
  - 構造体：DeviceErrorCode_t 
      メンバー「devEEPWriteError」が追加されます   
- soファイル更新

変更時刻 : 2025-5-15

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/197
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/846

## 1.0.16

Library更新

- EmbLibrary
  - 関数：failureCancelStipple新規追加 
- TpdLibrary
  - 関数：hasMechaKeySousaBetweenLastAndCurrentCheck
       新規追加 
  - 構造体：EcoSleepPanelState_t 
      メンバー「isMechaKeySousa」が追加されます   
- soファイル更新

変更時刻 : 2025-5-12

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/196
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/840

## 1.0.15

Library更新
- DeviceLibrary
  - 関数：bpifEepWrite新規追加
  - 関数：bpifEepRead新規追加
  - 構造体：BOARD_TYPE新規追加
- EmbLibrary
  - 構造体：embErrorCode_t
      メンバー「EMB_EEP_WRITE_ERROR」が削除された
- OS更新

変更時刻 : 2025-4-28

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/195


## 1.0.14

Library更新
- EmbLibrary
  - 構造体：ScrollCenterType_t
      メンバー「IMAGE_EDITING_PROJECTOR」が削除された
      メンバー「IMAGE_MDC_STITCH_PROJECTOR」が削除された
- MdcLibrary
  - 関数：editMdcReservedLine新規追加

変更時刻 : 2025-4-24

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/194
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/833

## 1.0.13

Library更新
- EmbLibrary
  - 構造体：MagType_t
      メンバー「XY_RESET」が追加されます 
  - 構造体：embErrorCode_t
      メンバー「EMB_FD_WRONG_FORMAT」が追加されます 

変更時刻 : 2025-4-22

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/193
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/825

## 1.0.12

Library更新

- UTLLibrary
  - 関数：setMyIllustImageAddInformation新規追加
  - 構造体：MyIllustDetailInfo_t新規追加
  - 構造体：UtlManualAdjustParam_tメンバー「TaperingCycleNum」が修改されます

変更時刻 : 2025-4-17

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/187
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/819

## 1.0.11
Library更新
- MDCLibrary
  - 構造体：MdcHistSubParam_t
      メンバー「pointNum」が追加されます   
      メンバー「pointList」が追加されます   

変更時刻 : 2025-4-7

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/187
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/808

## 1.0.10
OS更新

変更時刻 : 2025-3-26

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/commit/551137a2589f110acc8ee23312d83f46628119f7
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/797


## 1.0.9
Library更新
- CameraProjectLibrary
  - 関数：CameraResetImageUpdateState
       新規追加 
- EmbLibrary
  - 関数：deleteThreadInfo
       新規追加 
  - 構造体：embErrorCode_t
       メンバー「EMB_MEMOVER_OK_ERR」が追加されます
- TpdLibrary
  - 関数：BPIFSetTestMode
       新規追加 
- MDCLibrary
  - 関数：isMdcDrawingPatternPresenceForNext
       新規追加 

変更時刻 : 2025-3-13

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/184
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/778


## 1.0.8
Library更新
なし

変更時刻 : 2025-3-5

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/182
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/770

## 1.0.7
Library更新
- TpdLibrary
  - 構造体：BPIFSendKey
      メンバー「KEYEMBINDICATOR」が追加されます
      メンバー「BPIF_TEACHING_SETMATRIXENABLELIST」が追加されます

変更時刻 : 2025-2-28

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/180
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/762

## 1.0.6
Library更新
- MDCLibrary
  - loadMdcEditHistoryResume
         新規追加 

変更時刻 : 2025-2-19

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/179
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/752

## 1.0.5
各soファイル更新
Library更新

- TpdLibrary
  - 構造体：BPIFTestMode62Param_t
       メンバー「xyEepData」が追加されます   

変更時刻 : 2025-02-10

変更詳細は　以下のLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/177
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/commit/2b0308e455dd7e6165a460cc4e3fc29fe342c3c6
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/commit/c6b70bc7d23834196d1101dd48fa9fb1c18a6dcb

## 1.0.4
各soファイル更新
Library更新
- EmbCharLibrary
 - 関数：cancelEmbCharEdit
       新規追加 

- EmbLibrary
  - 構造体：embErrorCode_t
       メンバー「EMB_TROUBLE_OCCORED_POWER_OFF」が追加されます   

- TpdLibrary
 - 構造体：InitState_t
       メンバー「INITSTATE_TEST_MODE_SERVICE_MODE_XY_MOTOR_ADJUST」が追加されます

 - UtlLibrary
 - 構造体：UtlErrorCode_t 
       メンバー「utlErrorMemoryFull」が追加されます
       メンバー「utlErrorPowerOff」が追加されます


変更時刻 : 2025-02-06

変更詳細は　以下のLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/175
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/commit/b825abbecc311a202afeed3c535530a738cc1a42



## 1.0.3
Library更新
- EmbLibrary
 - 関数：checkColorShuffle
       新規追加 

- MdcLibrary
 - 構造体：MdcEditFormInstruct
       メンバー「formInst_density_change」が追加されます

変更時刻 : 2025-01-23

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/commit/3bb425cc32697bffcf1ab8fc68e6d8fcd7b9aef5
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/172

## 1.0.2
Library更新
- TpdLibrary
  - 関数：setAppErrorState
         メンバー「errAct」が追加されます

変更時刻 : 2025-01-07

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/169
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/711

## 1.0.1
Library更新
- ProjectorLibrary
  - 構造体：ProjectorErrorCode_t
         メンバー「projectorErrorDataNotSet」が追加されます
- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「BPIF_UTL_KEYPROJECTORVIEWWAITLINEMAKER」が追加されます
- UtlLibrary
  - 関数：calibUtlLineMaker
        新規追加 

変更時刻 : 2024-12-24

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/168
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/699

## 1.0.0
Library更新
- TpdLibrary
  - 関数：AppBpifMaxNotification
        新規追加 
- UtlLibrary
  - 構造体：UtlStateInfo_t
         メンバー「patternNum」が追加されます

変更時刻 : 2024-12-16

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/167
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/685

## 0.9.9
Library更新
- TpdLibrary
  - 関数：setMatrixEnableList
        新規追加 

変更時刻 : 2024-12-11

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/165
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/681

## 0.9.8
Library更新
- TpdLibrary
  - 構造体：BPIFTestMode01Param_t
         メンバー「isEnabledADB」が追加されます
- MdcLibrary
  - 関数：getMdcEditHistory
  - 関数：setMdcZigzagLineProperty
  - 関数：setMdcRunningStitchLineProperty
  - 関数：setMdcTripleStitchLineProperty
  - 関数：setMdcCandlwickingLineProperty
  - 関数：setMdcChainStitchLineProperty
  - 関数：setMdcEStitchLineProperty
  - 関数：setMdcVStitchLineProperty
  - 関数：setMdcMotifLineProperty
  - 関数：setMdcRoughZigzagLineProperty
  - 関数：getMdcZigzagLineProperty
  - 関数：getMdcRunningStitchLineProperty
  - 関数：getMdcTripleStitchLineProperty
  - 関数：getMdcCandlwickingLineProperty
  - 関数：getMdcChainStitchLineProperty
  - 関数：getMdcEStitchLineProperty
  - 関数：getMdcVStitchLineProperty
  - 関数：getMdcMotifLineProperty
  - 関数：getMdcRoughZigzagLineProperty
  - 関数：setMdcTatamiSurfaceProperty
  - 関数：setMdcStipplingSurfaceProperty
  - 関数：setMdcDecorativeFillSurfaceProperty
  - 関数：getMdcTatamiSurfaceProperty
  - 関数：getMdcStipplingSurfaceProperty
  - 関数：getMdcDecorativeFillSurfaceProperty
  - 関数：getMdcEditRegionProperty
         削除された
  - 構造体：RegionInfo_t
         「color,tpIdx,mtf,detail」を削除された
  - 構造体：MdcSewZigzagParam_t
  - 構造体：MdcSewRunningParam_t
  - 構造体：MdcSewTripleParam_t
  - 構造体：MdcSewCandleParam_t
  - 構造体：MdcSewChainParam_t
  - 構造体：MdcSewEParam_t
  - 構造体：MdcSewVParam_t
  - 構造体：MdcSewRoughZigzagParam_t
  - 構造体：MdcSewStippleParam_t
         「color」を削除された
  - 構造体：MdcSewMotifParam_t
         「color,motifType,motifNo」を削除された
  - 構造体：MdcSewTatamiParam_t
         「color,dirAutoFlg」を削除された
  - 構造体：MDC_DecoFill_Rand_t
          削除された
  - 構造体：MdcSewDecoParam_t
         「color,decorativeType,decorativeNo」を削除された

変更時刻 : 2024-12-05

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/162
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/669

## 0.9.7
Library更新
- EmbLibrary
  - 関数：moveQuiltEmb
         メンバー「flgLongPress」が追加されます

変更時刻 : 2024-12-04

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/161
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/666

## 0.9.6
Library更新
- ProjectorLibrary
  - 関数：calibEmbQuiltProjector
        新規追加 
- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「BPIF_EMB_KEYPRJSTARTEMBMEASUREPRESSERFOOT」が追加されます
  - 構造体：ErrCode_t
         メンバー「ERR_APP_ACT_NON」が追加されます
         メンバー「ERR_APP_ACT_NON_WITH_PUSH」が追加されます
  - 構造体：ErrAct_t
        新規追加 

変更時刻 : 2024-12-02

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/160
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/660

## 0.9.5
Library更新
- TpdLibrary
  - 構造体：BPIFTestMode37Param_t
         「HeightNum」を追加された
         「HeightNumZero」を削除された
- EmbLibrary
  - 関数：setEmbTensionPlus
  - 関数：setEmbTensionMinus
         「stepSize」を追加された

変更時刻 : 2024-11-28

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/157
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/654

## 0.9.4
Library更新
- EmbLibrary
  - 関数：embMarkPatCnctSettingOffsetGet
         新規追加 

変更時刻 : 2024-11-26

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/155
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/651

## 0.9.3
Library更新
なし

変更時刻 : 2024-11-23

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/154
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/648

## 0.9.2
Library更新
- EmbLibrary
  - 構造体：moveEmbAll
         メンバー「flgLongPress」が追加されます

変更時刻 : 2024-11-20

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/152
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/636

## 0.9.1
Library更新
- EmbLibrary
  - 構造体：notPatternAreaSelectWork
        新規追加 

変更時刻 : 2024-11-19

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/151
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/632

## 0.9.0
Library更新
なし

変更時刻 : 2024-11-19

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/150
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/626

## 0.8.9
Library更新
- EmbLibrary
  - 構造体：finishOutline
        新規追加 
  - 構造体：EmbSewingAttribParam_t
  - 構造体：EmbEditAttribParam_t
         メンバー「projectorState」が追加されます

変更時刻 : 2024-11-18

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/149
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/623

## 0.8.8
Library更新
- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「BPIF_UTL_KEYGOTOIMAGE_JITU」が追加されます

変更時刻 : 2024-11-18

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/148
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/621

## 0.8.7
Library更新
- TpdLibrary
  - 構造体：initSettingGuidanceStart
  - 構造体：initSettingGuidanceComplete
        新規追加 

変更時刻 : 2024-11-16

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/147
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/620

## 0.8.6
Library更新
- MdcLibrary
  - 構造体：MdcHistoryTypes
        新規追加 
- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「BPIF_SEND_KEYLEDPOINTEROFF」が追加されます

変更時刻 : 2024-11-15

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/146
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/618

## 0.8.5
Library更新
- EmbLibrary
  - 構造体：embGrp_t
         メンバー「grpRotateOrigine」が追加されます
  - 構造体：embInfo_t
         メンバー「angle」が削除されます
         メンバー「position」が削除されます
         メンバー「allAngle」が追加されます
         メンバー「embRotateOrigine」が追加されます

変更時刻 : 2024-11-15

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/145
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/617

## 0.8.4
Library更新
- ProjectorLibrary
  - 関数：moveProjectorEmbPatternProjectionArea
  - 関数：startFrameMoveEmbProjector
        削除されます

変更時刻 : 2024-11-15

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/144
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/616

## 0.8.3
Library更新
なし

変更時刻 : 2024-11-14

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/143
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/612

## 0.8.2
Library更新
- MdcLibrary
  - 関数：editMdcAreaSelect
         メンバー「pointNum」が追加されます
         メンバー「pointList」が追加されます
  - 構造体：MdcDrawingTypes 
         メンバー「drawtype_history」が追加されます
         メンバー「drawtype_history_part」が追加されます
  - 構造体：MdcHistSubParam_t 
         メンバー「historyType」が追加されます

変更時刻 : 2024-11-14

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/142
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/610

## 0.8.1
Library更新
なし

変更時刻 : 2024-11-13

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/141
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/608

## 0.8.0
Library更新
なし

変更時刻 : 2024-11-13

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/139
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/604

## 0.7.9
Library更新
なし

変更時刻 : 2024-11-13

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/138
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/603

## 0.7.8
Library更新
なし

変更時刻 : 2024-11-12

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/137
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/599

## 0.7.7
Library更新
なし

変更時刻 : 2024-11-11

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/135
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/593

## 0.7.6
Library更新
なし

変更時刻 : 2024-11-08

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/134
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/591

## 0.7.5
Library更新
- DeviceLibrary
  - 構造体：DeviceSettingInfo
         メンバー「modeCode」が追加されます

変更時刻 : 2024-11-08

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/133
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/590

## 0.7.4
Library更新
- EmbLibrary
  - 関数：gotoEmbFromOpening
        新規追加 
- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「GOTOEMBFROMOPENING」が追加されます

変更時刻 : 2024-11-07

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/132
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/588

## 0.7.3
Library更新
- EmbLibrary
  - 関数：selectEmbGroupNext
  - 関数：selectEmbGroupPrev
        新規追加 
- ProjectorLibrary
  - 関数：checkErrorBeforeEmbProjectorCloseNoFrame
        新規追加 
- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「BPIF_EMB_KEYCHECKERRBEFOREPROJECTIONCLOSENOFRAME」が追加されます
  - 構造体：BPIFSendKey
         メンバー「BPIF_EMB_QUILT_FRAME_POSITION_REACQUISITION」が追加されます

変更時刻 : 2024-11-07

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/131
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/587

## 0.7.2
Library更新
- TpdLibrary
  - 関数：getCurrentNeedlePosition
        新規追加 
  - 構造体：embPtrn_t
         メンバー「KEYFRAMEREMOVEOKSUB」が追加されます
         メンバー「KEYFRAMEREMOVERETURNOKSUB」が追加されます
         メンバー「BPIF_SEND_EMBSEW_KEYFRAMEREMOVEOKSUB」が削除されます
         メンバー「BPIF_SEND_EMBSEW_KEYFRAMEREMOVERETURNOKSUB」が削除されます
  - 構造体：ErrCode_t
         メンバー「ERR_EMB_CARRY_MOVING_FRAME_REMOVE」が追加されます
         メンバー「ERR_EMB_FRAME_REMOVE_RERUEN」が追加されます
  - 構造体：TESTMODE44_PRM_DEF_CHECK_NUM
         メンバー「EN_PRM_DEF_CHECK_NUM_HEELKICK_REGIST」が追加されます
         メンバー「EN_PRM_DEF_CHECK_NUM_SIDESW_REGIST」が追加されます
         メンバー「EN_PRM_DEF_CHECK_NUM_UTL_AUTOPF_LIFT_TRIM」が追加されます
         メンバー「EN_PRM_DEF_CHECK_NUM_UTL_AUTOPF_LIFT_DOWN」が追加されます
         メンバー「EN_PRM_DEF_CHECK_NUM_UTL_AUTOPF_LIFT_UP」が追加されます
         メンバー「EN_PRM_DEF_CHECK_NUM_UTL_PIVOT_SAVE」が追加されます
         メンバー「EN_PRM_DEF_CHECK_NUM_SS_CUR_TYPE」が追加されます
         メンバー「EN_PRM_DEF_CHECK_NUM_EMB_GRID_DISPLAY」が追加されます
         メンバー「EN_PRM_DEF_CHECK_NUM_JUMP_STITCH_LENGTH」が追加されます
         メンバー「EN_PRM_DEF_CHECK_NUM_JUMP_STITCH_TRIM」が追加されます
         メンバー「EN_PRM_DEF_CHECK_NUM_EMB_WAPPEN_OFFSET」が追加されます

変更時刻 : 2024-11-05

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/130
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/585


## 0.7.1
Library更新
なし

変更時刻 : 2024-11-06

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/127
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/580

## 0.7.0
Library更新
- EmbLibrary
  - 関数：getCurrentNeedlePosition
        新規追加 
  - 構造体：embPtrn_t
         メンバー「subCutVerticalState」が追加されます
         メンバー「subCutHorizontalState」が追加されます
         メンバー「subCutState」が削除されます
- TpdLibrary
  - 構造体：ErrCode_t
         メンバー「ERR_DF_CONNECT_NOCLOSE」が追加されます
         メンバー「ERR_REMOVE_SR_NOCLOSE」が追加されます
  - 構造体：ErrCode_t
         メンバー「INITSTATE_PROGRAM_SPEC_DIFFERENCE」が追加されます
         メンバー「INITSTATE_TEST_MODE_EEPROM_COPY_FINISH」が追加されます

変更時刻 : 2024-11-05

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/126
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/574

## 0.6.9
Library更新
- EmbLibrary
  - 関数：getCurrentNeedlePosition
        新規追加 
  - 構造体：embPtrn_t
         メンバー「MagMax」が追加されます
         メンバー「MagDefault」が追加されます
         メンバー「MagMin」が追加されます
- UtlLibrary
  - 構造体：UtlManualDspMode_t
         メンバー「UTL_MANUAL_DSP_MODE_2ND_DECIMAL」が追加されます

変更時刻 : 2024-11-01

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/125
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/573

## 0.6.8
Library更新
なし

変更時刻 : 2024-11-01

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/124
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/571

## 0.6.7
Library更新
- EmbLibrary
  - 関数：saveEmbResumeData
        新規追加 

変更時刻 : 2024-10-31

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/123
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/565


## 0.6.6
Library更新
なし

変更時刻 : 2024-10-31

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/122
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/562

## 0.6.5
Library更新
- EmbLibrary
  - 構造体：EmbAttribParam_t
         メンバー「projectorState」が削除されます
         メンバー「nonStopSewState」が削除されます
         メンバー「nonStopSew」が削除されます
         メンバー「colorSortState」が削除されます
         メンバー「colorSort」が削除されます
         メンバー「threadCutState」が削除されます
         メンバー「jumpStitchTrimCutState」が削除されます
         メンバー「embTension」が削除されます
         メンバー「threadCut」が削除されます
         メンバー「jumpStitchTrim」が削除されます
         メンバー「jumpStitchLength」が削除されます
         メンバー「embSewingQultStartPosition」が削除されます
         メンバー「embSewingStartPosition」が削除されます
         メンバー「isEmbSewing」が追加されます
         メンバー「mode」が追加されます

変更時刻 : 2024-10-30

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/121
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/560

## 0.6.4
Library更新
- EmbLibrary
  - 関数：embMarkPatCnctEditParamSet
         メンバー「offset」が削除されます

変更時刻 : 2024-10-30

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/120
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/557


## 0.6.3
Library更新
- UtlLibrary
  - 関数：openPreviewScreen
         メンバー「offset」が追加されます
  - 関数：operatePreviewImage
         メンバー「offset」が追加されます
  - 構造体：SLPoint_t
        新規追加 

変更時刻 : 2024-10-29

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/119
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/556


## 0.6.2
Library更新
- MdcLibrary
  - 構造体：MdcHistSubParam_t
         メンバー「region_id」が追加されます

変更時刻 : 2024-10-28

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/118
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/555

## 0.6.1
Library更新
なし

変更時刻 : 2024-10-28

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/117
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/552

## 0.6.0
Library更新
- EmbLibrary
  - 構造体：EmbSewingAttribParam_t
         メンバー「quirEditMode」が削除されます
  - 構造体：EmbSewingScr_t
        新規追加 

変更時刻 : 2024-10-25

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/115
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/547


## 0.5.9
Library更新
- EmbLibrary
  - 構造体：EmbEditAttribParam_t
         メンバー「subOrderState」が削除されます
         メンバー「subBackOrderState」が追加されます
         メンバー「subFrontOrderState」が追加されます

変更時刻 : 2024-10-25

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/114
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/540

## 0.5.8
Library更新
なし

変更時刻 : 2024-10-24

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/113
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/537

## 0.5.7
Library更新
- EmbLibrary
  - 関数：embMarkPatCnctGetScanResultImage
         返却値のパラメータの型が変更されました。

- TpdLibrary
  - 関数：getProjectorEmbGuidelineOnlyImage
        新規追加 

変更時刻 : 2024-10-23

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/112
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/535

## 0.5.6
Library更新
- EmbLibrary
  - 構造体：embInfo_t
         メンバー「height」が追加されます
         メンバー「wide」が追加されます
  - 関数：embMarkPatCnctSettingGet
        新規追加 

変更時刻 : 2024-10-21

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/110
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/531


## 0.5.5
Library更新
なし

変更時刻 : 2024-10-21

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/109
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/530

## 0.5.4
Library更新
- EmbLibrary
  - 関数：selectEditFile
         メンバー「dataSource」が追加されます
         メンバー「restriction」が追加されます
  - 関数：embSelectReturnEdit
        新規追加 

- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「KEYGOTOEDITEDITINGFROMSELECTRETURN」が追加されます

変更時刻 : 2024-10-21

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/108
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/529

## 0.5.3
Library更新
- EmbLibrary
  - 関数：delRealImageLight
        パラメータの型が変更されました。
  - 関数：embMarkPatCnctSetScanResultImage
         メンバー「resultImgData」が削除されます
         メンバー「resultImgWidht」が削除されます
         メンバー「resultImgHeight」が削除されます
  - 関数：embMarkPatCnctGetScanResultImage
         返却値が削除されます
         メンバー「resultImgHeight」が追加されます
  - 関数：embMarkPatCnctDelScanResultImage
        新規追加 
  - 構造体：embErrorCode_t
         メンバー「EMB_SELECT_PATTERN_ROTATE90_WITH_ALL_DELETE_CLOSE」が削除されます
         メンバー「EMB_BAD_EMBROIDERY_DATA」が削除されます
         メンバー「EMB_SELECT_PATTERN_ROTATE90」が削除されます
         メンバー「EMB_COUTING_THIS_PATTERN_NOT_USE」が削除されます
         メンバー「EMB_SELECT_PATTERN_ROTATE90_F」が削除されます
         メンバー「EMB_THIS_PATTERN_NOT_USE」が削除されます
         メンバー「EMB_ANGOU_NOT_USE」が削除されます

変更時刻 : 2024-10-18

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/107
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/524

## 0.5.2
Library更新
- EmbLibrary
  - 関数：isVolatileEmb
  - 関数：setVolatileCurEmbGroup
  - 関数：isRestrictionEmb
  - 関数：setRestrictionCurEmbGroup
        新規追加 
  - 構造体：baseColor_t
        パラメータの型が変更されました。
  - 構造体：embLargeCategory_t
         メンバー「EMB_LARGE_CAT_PHOTO」が追加されます
- MdcLibrary
  - 関数：getMdcEditRegionInfoAll
         返却値のパラメータの型が変更されました。
         
変更時刻 : 2024-10-17

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/106
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/523


## 0.5.1
Library更新
- TpdLibrary
  - 構造体：BPIFChangeView_t
         メンバー「isNeedlePosition」が追加されます

変更時刻 : 2024-10-17

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/105
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/521

## 0.5.0
Library更新
- EmbLibrary
  - 関数：embEditReturnSelectGray
        新規追加 
  - 関数：embMarkPatCnctSetScanResultImage
         メンバー「resultImgData」が追加されます
         メンバー「resultImgWidht」が追加されます
         メンバー「resultImgHeight」が追加されます
  - 関数：embMarkPatCnctSetScanResultImage
         返却値のパラメータの型が変更されました。
  - 構造体：EmbAttribParam_t
        メンバー「isModeEmbSewingPopupSewingHelp」が追加されます

- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「KEYEDITADDGRAY」が追加されます

変更時刻 : 2024-10-16

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/104
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/518

## 0.4.9
Library更新
- TpdLibrary
  - 関数：isDisplayPopup
        新規追加 

  - 構造体：PopupState_t
         新規追加 

変更時刻 : 2024-10-15

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/103
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/513

## 0.4.8
Library更新
- DeviceLibrary
  - 構造体：CameraNDPEMessage_t
         構造体から列挙型に変更されました。

- EmbLibrary
  - 関数：embMarkPatCnctSetScanResultImage
         メンバー「lastImgWidth」が追加されます
         メンバー「lastImgHeight」が追加されます
         メンバー「nowImgWidht」が追加されます
         メンバー「nowImgHeight」が追加されます

- TpdLibrary
  - 構造体：EmbAttribParam_t
         メンバー「embCameraUiProjectionOnOffState」が追加されます
  - 構造体：BPIFSendKey
         メンバー「BPIF_SEND_EMBSEW_KEYFRAMEREMOVESUB」が追加されます
         メンバー「BPIF_SEND_EMBSEW_KEYFRAMEREMOVEOKSUB」が追加されます
         メンバー「BPIF_SEND_EMBSEW_KEYFRAMEREMOVERETURNOKSUB」が追加されます

- MdcLibrary
  - 構造体：MDCAttribParam_t
         メンバー「embCameraUiProjectionOnOffState」が追加されます

変更時刻 : 2024-10-14

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/102
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/510


## 0.4.7
Library更新
- MdcLibrary
  - 関数：getMdcEditRegionInfoAll
         返却値のパラメータの型が変更されました。

変更時刻 : 2024-10-12

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/101
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/509

## 0.4.6
Library更新
- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「BPIF_EMB_KEYPRJEMBAREAPARAMRESET」が追加されます

変更時刻 : 2024-10-11

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/100
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/507

## 0.4.5
Library更新
なし

変更時刻 : 2024-10-11

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/99
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/506

## 0.4.4
Library更新
- DeviceLibrary
  - 構造体：CameraNDPEMessage_t
         列挙型から構造体に変更されました。

- EmbLibrary
  - 関数：getRealImageLight
  - 関数：embMarkPatCnctSetScanResultImage
  - 関数：embMarkPatCnctGetScanResultImage
         返却値のパラメータの型が変更されました。

- MdcLibrary
  - 関数：loadMdcEditHistory
        新規追加 

変更時刻 : 2024-10-10

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/98
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/504

## 0.4.3
Library更新
- EmbLibrary
  - 関数：completeStipple
  - 関数：embMarkPatCnctSetScanResultImage
  - 関数：embMarkPatCnctGetScanResultImage
         新規追加 

変更時刻 : 2024-10-10

変更詳細は 以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/97
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/501

## 0.4.2
Library更新
- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「KEYGOTOEMBFBFORCHAGEVIEW」が追加されます
  - 構造体：ActionMode_t
         新規追加

- UtlLibrary
  - 構造体：UtlStateInfo_t
         メンバー「isUtlSewing」が追加されます
         メンバー「isProjectorPatternSelectValid」が追加されます

変更時刻 : 2024-10-09

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/96
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/500


## 0.4.1
Library更新
- EmbCharLibrary
  - 関数：selectEmbPf1
  - 関数：EmbPatternPF1ReCalc
         削除された

- DeviceLibrary
  - 構造体：UserLogProductInfo_t
        メンバー「
         softVer、
         setting、
         status、」が削除された
        メンバー「
         backupState、
         panelMajorVer、
         panelMinorVer、
         panelPatchVer、
         libMajorVer、
         libMinorVer、
         libPatchVer、
         main_version、
         boot_version、
         master_version、
         fpga_version、
         xy_cpu_version、
         boardType、
         embSpeed、
         embServiceStitchCnt、
         embTotalStitchCnt、
         utlServiceStitchCnt、
         utlTotalStitchCnt、
         serviceStitchCnt、
         totalStitchCnt、
         totalThreadCnt、
         errorListCode、
         errorListType、」が追加されます

  - 定数：BRNETGATEWAY_MONITORING_STATUS_BEFOREINITIAL
  - 定数：BRNETGATEWAY_MONITORING_STATUS_NOERRSTOP_GREEN
  - 定数：BRNETGATEWAY_MONITORING_STATUS_NOERRSTOP_RED
  - 定数：BRNETGATEWAY_MONITORING_STATUS_UTLSEWING
  - 定数：BRNETGATEWAY_MONITORING_STATUS_EMBSEWING
  - 定数：BRNETGATEWAY_MONITORING_STATUS_EMBCHANGETHERAD
  - 定数：BRNETGATEWAY_MONITORING_STATUS_SEWINGCOMPLETE
  - 定数：BRNETGATEWAY_MONITORING_STATUS_UPPERTHREAD_ERR
  - 定数：BRNETGATEWAY_MONITORING_STATUS_LOWERTHREAD_ERR
  - 定数：BRNETGATEWAY_MONITORING_STATUS_WIPER_ERR
  - 定数：BRNETGATEWAY_MONITORING_STATUS_NON_UPDATE
  - 定数：BRNETGATEWAY_MONITORING_STATUS_OTHER_ERR
  - 定数：BRNETGATEWAY_MONITORING_STATUS_EMB_ATTACHED
  - 定数：BRNETGATEWAY_MONITORING_STATUS_EMB_NOTON
  - 定数：BRNETGATEWAY_MONITORING_STATUS_INVALID
         新規追加

- EmbLibrary
  - 関数：selectEmbPf1
  - 関数：getSelectedGroupImagePtr
  - 関数：setSnowManMarkPosition
  - 関数：embMarkPatCnctMoveLeft
  - 関数：embMarkPatCnctMoveRight
  - 関数：completeStipple
         削除された
  - 関数：getEmbSewPatternConnect
  - 関数：exchangeErrCode
  - 関数：embSewingSnowmanScanCancelFrameMove
  - 関数：embMarkPatCnctRotateLeft
  - 関数：embMarkPatCnctRotateRight
  - 関数：embMarkPatCnctScrSetting
  - 関数：embSewingEscUnit
  - 関数：embSewingMoveEscUnit
  - 関数：embSewingReturnEscUnit
  - 関数：isEmbSewingMoveEscUnit
         新規追加 
  - 関数：embMarkPatCnctRotateRight90
         返り値を追加しました
  - 関数：setEmbBorderMark
          メンバー「orderOnOff」が追加されます
  - 構造体：UserLogProductInfo_t
        メンバー「
         IMAGE_EDITING_PROJECTOR、
         IMAGE_MDC_STITCH_PROJECTOR、」が追加されます
  - 構造体：MDC_ExpandType3_Fill_t
  - 構造体：SnowManMarkPos_t
  - 構造体：setPosition_t
        削除された
  - 構造体：EmbDirSelect_t
        新規追加 

- MdcLibrary
  - 関数：getMdcSewParamDefaultData
        新規追加 
  - 構造体：MdcSewLineAllParam_t
  - 構造体：MdcSewSurfaceAllParam_t
        新規追加 
  - 構造体：MdcSewKinds_line
  - 構造体：MdcSewKinds_surface
         内容が変更されました

- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「TTK_TM27_PROJ_BRIGHTNESS_DOWN」が追加されます
         メンバー「TTK_TM27_PROJ_BRIGHTNESS_UP」が追加されます
  - 構造体：BPIFTestMode27Param_t
         メンバー「brightnessValue」が追加されます
  - 構造体：BPIFGlobalAppInfo_t
         メンバー「actionMode」が追加されます

- UtlLibrary
  - 関数：getDisableUtlDataList
         メンバー「disableDataList」が変更されました

変更時刻 : 2024-10-08

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/95
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/497

## 0.4.0
Library更新
- EmbLibrary
  - 関数：checkUsableAppliqueParts
  - 関数：completeStipple
  - 関数：embSewingStartPositionClear
  - 関数：checkBeforeEasyStippleMakeProc
  - 関数：checkMemoryForEmbSewing
  - 関数：checkMemoryForEmbEdit
         新規追加
  - 構造体：EmbSewingAttribParam_t
         メンバー「connectSewMark」が追加されます
         メンバー「subRotateState」が追加されます
         メンバー「subRotateResetState」が追加されます
         メンバー「subSmallRotateState」が追加されます
         メンバー「subMoveState」が追加されます
         メンバー「startSewingQuiltState」が追加されます
         メンバー「startQuiltSewing」が追加されます
         メンバー「startPositionMovetQuiltHexagon」が追加されます
         メンバー「startPositionMovetQuiltConer」が追加されます
         メンバー「QuiltExtCurrentNum」が追加されます
         メンバー「QuiltExtTotalNum」が追加されます
         メンバー「quitShashesPartsLeftState」が追加されます
         メンバー「quitShashesPartsRightState」が追加されます
  - 構造体：EmbEditAttribParam_t
         メンバー「subAlignmentState」が追加されます
         メンバー「subOldWappenState」が追加されます
         メンバー「subDensityState」が追加されます
         メンバー「subCutState」が追加されます
         メンバー「subBorderSelectedGroupState」が追加されます
         メンバー「subSmallRotateState」が追加されます
         メンバー「subMagnificationState」が追加されます
         メンバー「subBlockState」が追加されます
         メンバー「subSTBState」が追加されます
         メンバー「subOrderState」が追加されます
         メンバー「previewState」が追加されます
         メンバー「subEditGroupSelectNext」が追加されます
  - 構造体：embErrorCode_t
         メンバー「EMB_PATTERN_EXCEEDED」が追加されます
         メンバー「EMB_TOO_MUCH_SELECTED」が追加されます
         メンバー「EMB_MCD_NOT_EXCHANGE_AREA_OVER」が追加されます
         メンバー「EMB_APPLIQUE_NG_MEM_OVER」が追加されます
         メンバー「EMB_APPLIQUE_NG_COMPLEX」が追加されます
         メンバー「EMB_OUTSIDE_OF_EMB_FRM_NOUSE」が追加されます
  - 構造体：StartPositionQuilt_t
         新規追加

- MdcLibrary
  - 関数：getMdcTripleStitchLineParamInfo
         関数名を修正しました
  - 関数：setMdcStipplingSurfaceProperty
  - 関数：setMdcStipplingSurfaceParam
  - 関数：getMdcStipplingSurfaceProperty
         内容が変更されました
  - 構造体：MdcSewStippleParam_t
         内容が変更されました

- ProjectorLibrary
  - 関数：getProjectorUserCalibrationImage
         新規追加

- TpdLibrary
  - 関数：BPIFGetAppDisplay_mdc
         新規追加
  - 構造体：BPIFSendKey
         メンバー「KEYEMBNEEDLEPOSITIONTOCENTER」が追加されます
         メンバー「BPIF_SEND_QUIL_KEYEMBFRAMEMOVEQUILT」が追加されます
         メンバー「BPIF_EMB_QUILT_SCALING_MAGNIFICATION」が追加されます
         メンバー「BPIF_SEND_EMBSEW_KEYEMBALLROTATE」が追加されます
         メンバー「BPIF_SETTING_KEYDFCORRECTPULSE」が追加されます
  - 構造体：InitState_t
         メンバー「INITSTATE_TEST_MODE_NORMAL_MODE_OK」が追加されます
         メンバー「INITSTATE_UPGRADE_MODE」が追加されます
         メンバー「BPIF_SETTING_KEYDFCORRECTPULSE」が追加されます
  - 構造体：MDCAttribParam_t
  - 構造体：FaleSafeKind_t
  - 構造体：IicErrorUnit
         新規追加
  - 定義：IICエラー，PMエラー
         定義追加

- UtlLibrary
  - 構造体：UtlAttribParam_t
         メンバー「cursor」が追加されます

変更時刻 : 2024-09-29

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/92
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/489


## 0.3.9
Library更新
- EmbLibrary
  - 関数：cancelApplique
  - 関数：cancelPreviewApplique
         新規追加

変更時刻 : 2024-09-27

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/91
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/487


## 0.3.8
Library更新
変更はありません

変更時刻 : 2024-09-25

変更詳細は　以下二つのLinkを参考してください

https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/90
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/483

## 0.3.7
Library更新
- MdcLibrary
  - 関数：drawMdcSewingPen
         削除された

変更時刻 : 2024-09-25

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/89
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/481

## 0.3.6
Library更新
- MdcLibrary

  IF仕様書、ffiの以下構造体メンバ型を int16_t⇒uint16_t に変更しました。
    - MdcSewZigzagParam       構造体		width
    - MdcSewRunningParam      構造体		pitch
    - MdcSewTripleParam 　    構造体		pitch
    - MdcSewCandleParam       構造体	  size、spacing、
    - MdcSewChainParam        構造体		size、thickness
    - MdcSewEParam        　  構造体		width、spacing、thickness
    - MdcSewVParam      　    構造体		width、spacing、thickness
    - MdcSewMotifParam        構造体	  size
    - MdcSewRoughZigzagParam  構造体    width
    - MdcSewTatamiParam       構造体		direction、PullCompensation
    - MdcSewStipParam   　    構造体		runPitch、spacing、distance
    - MdcSewDecoParam         構造体		size、direction、randomShift

変更時刻 : 2024-09-23

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/88
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/476

## 0.3.5
Library更新
- EmbLibrary
  - 関数：embSewingGotoRotate
  - 関数：embSewingGotoRotateQuilt
  - 関数：embSewingGotoMove
  - 関数：embSewingGotoMoveQuilt
  - 関数：embMarkPatCnctImgDrawPrmGet
  - 関数：embMarkPatCnctSnowmanDrawPrmGet
         新規追加
  - 構造体：EmbMarkPatImgDrawParam_t
  - 構造体：EmbMarkPatSnowmanDrawParam_t
  - 構造体：MarkPatCnctImgType_t
         新規追加
  - 構造体：EmbSewingAttribParam_t
           メンバー「isQuiltExt」が追加されます

- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「BPIF_SEND_EMB_KEYGOTOEMBMOVEQUILT」が追加されます
         メンバー「BPIF_SEND_EMB_KEYGOTOALLROTATEQUILT」が追加されます

変更時刻 : 2024-09-23

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/87
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/472

## 0.3.4
Library更新
- EmbLibrary
  - 構造体：EmbSewingAttribParam_t
         メンバー「rotAngle,disVertical,disHorizon」が追加されます
  - 構造体：EmbEditAttribParam_t
         メンバー「rotAngle,sizeWidth,sizeHeight,sizeRunCalc,disVertical,disHorizon」が追加されます
  - 構造体：embPatAutoKind_t
         メンバー「EMB_PATAUTOKIND_QUILTEXTENSION_POLYGON」が追加されます

- TpdLibrary
  - 関数：BPIFInit1st
  - 関数：BPIFInit2nd
         新規追加
  - 構造体：InitState_t
           メンバー「INITSTATE_UPGRADE_MODE」が追加されます

- MdcLibrary
  - 関数：setMdcZigzagLineParam
  - 関数：setMdcRunningStitchLineParam
  - 関数：setMdcTripleStitchLineParam
  - 関数：setMdcCandlwickingLineParam
  - 関数：setMdcChainStitchLineParam
  - 関数：setMdcEStitchLineParam
  - 関数：setMdcVStitchLineParam
  - 関数：setMdcMotifLineParam
  - 関数：setMdcRoughZigzagLineParam
  - 関数：getMdcZigzagLineParamInfo
  - 関数：getMdcRunningStitchLineParamInfo
  - 関数：getMdcTripleStitchLinePramInfo
  - 関数：getMdcCandlwickingLineParamInfo
  - 関数：getMdcChainStitchLineParamInfo
  - 関数：getMdcEStitchLineParamInfo
  - 関数：getMdcVStitchLineParamInfo
  - 関数：getMdcMotifLineParamInfo
  - 関数：getMdcRoughZigzagLineParamInfo
  - 関数：setMdcTatamiSurfaceParam
  - 関数：setMdcStipplingSurfaceParam
  - 関数：setMdcDecorativeFillSurfaceParam
  - 関数：getMdcTatamiSurfaceParamInfo
  - 関数：getMdcStipplingSurfaceParamInfo
  - 関数：getMdcDecorativeFillSurfaceParamInfo
  - 関数：getMdcEditRegionInfo
  - 関数：getMdcEditRegionInfoAll
         新規追加
  - 関数：updateMdcLineStitchTypeForRegion
  - 関数：updateMdcSurfaceStitchTypeForRegion
           メンバー「targetAll,color」が追加されます

  - 構造体：RegionInfoAndNumber_t
  - 構造体：MDC_Tatami_Dir_Kind_t
         新規追加
  - 構造体：RegionInfo_t
         メンバー「type,number」が追加されます
  - 構造体：MdcSewTatamiParam_t
         メンバー「dirKind」が追加されます
  - 構造体：MDC_DecoFill_RandType_t
         メンバー「mdc_decofill_randtype_invalid」が追加されます
  - 構造体：MDC_DecoFill_Thickness_t
         メンバー「mdc_decofill_thickness_invalid」が追加されます
  - 構造体：MdcIsOnOff
  - 構造体：MdcStichLine
  - 構造体：MdcFlipside
         enumに変わる
  - 構造体：MDC_Rough_Zigzag_Density_t
  - 構造体：MDC_Zigzag_Tatami_Density_t
         メンバーの名前が修正される
         
変更時刻 : 2024-09-21

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/86
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/470

## 0.3.3
Library更新
- EmbLibrary
  - 関数：initMakingEasyStipple
         新規追加
  - 関数：makeStipple
           メンバー「type」が追加されます

- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「KEYEMBROIDERYMOVEQUILTRETURN」が追加されます
         メンバー「KEYEMBROIDERYROTATEQUILTRETURN」が追加されます
         メンバー「KEYCORNERSETTINGRETURNBYPOP」が追加されます

変更時刻 : 2024-09-19

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/85
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/466

## 0.3.2
Library更新
- EmbLibrary
  - 関数：embMarkPatCnctDistanceGet
  - 関数：emb1stAutoMarkPatCnctOK
  - 関数：emb1stAutoMarkPatCnctCancel
  - 関数：emb1stAutoConfirmMarkPatCnctCancel
  - 関数：emb1stAutoConfirmMarkPatCnctOk
         新規追加

- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「KEYSEWOVERAUTOMARKPATCNCTOK」が追加されます
         メンバー「KEYSEWOVERAUTOMARKPATCNCTCANCEL」が追加されます

変更時刻 : 2024-09-18

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/84
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/461

## 0.3.1
Library更新
- EmbLibrary
  - 関数：delRealImageLight
  - 関数：getRealImageLight
  - 関数：checkEmbLargeCategorySelection
  - 構造体：embstitchPos_t
  - 構造体：embLargeCategory_t
         新規追加

変更時刻 : 2024-09-18

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/83
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/460

## 0.3.0
Library更新
なし

変更時刻 : 2024-09-14

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/82
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/459

## 0.2.9
Library更新
- MdcLibrary
  - 関数：getMdcEditHistory
         メンバー「subParam」が新規追加
  - 関数：updateMdcLineStitchTypeForRegion
  - 関数：updateMdcSurfaceStitchTypeForRegion
  - 構造体：MdcHistSubParam_t
         新規追加

- TpdLibrary
  - 構造体：BPIFSendKey
         メンバー「BPIF_UTL_KEYBHSLITLENGTHAUTOUPDATE」が追加されます

- UtlLibrary
  - 関数：startBHSlitLengthAutoUpdate
         新規追加

変更時刻 : 2024-09-14

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/81
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/454



## 0.2.8
Library更新
- EmbLibrary
  - 構造体：embPtrn_t
           メンバー「dataSource,embExtension」が追加されます

変更時刻 : 2024-09-12

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/80
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/451



## 0.2.7
Library更新
なし

変更時刻 : 2024-09-12

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/79
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/448



## 0.2.6
Library更新
- MdcLibrary
  - 関数：setMdcTatamiSurfaceProperty
  - 関数：setMdcDecorativeFillSurfaceProperty
  - 関数：getMdcTatamiSurfaceProperty
  - 関数：getMdcDecorativeFillSurfaceProperty
           関数名を修正しました

  - 構造体：MdcSewTatamiParam_t
           メンバー「dirAutoFlg」が新規追加
           
  - 構造体：MDC_DecoFill_Rand_t
  - 構造体：MDC_DecoFill_RandType_t
  - 構造体：MDC_DecoFill_Thickness_t
           新規追加

  - 構造体：MdcSewDecoParam_t
           メンバー「runPitch」が削除されました

  - 構造体：MdcFrameStampTypes
           メンバーを追加しました


変更時刻 : 2024-09-11

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/77
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/78
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/447
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/443

## 0.2.5
Library更新
なし

変更時刻 : 2024-09-10

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/76
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/442


## 0.2.4
Library更新
なし

変更時刻 : 2024-09-09

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/75
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/441

## 0.2.3
Library更新
- TpdLibrary
  - 関数：bpIFInitMinimum
           新規追加

変更時刻 : 2024-09-09

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/74
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/435

## 0.2.2
Library更新
- EmbLibrary
  - 構造体：embPtrn_t
           メンバー「quiltRectanglePointNum,quiltRectanglePoint」が追加されます

- MdcLibrary
  - 構造体：MdcErrorCode_t
           メンバー「mdcErrorNotExchangeAreaOver,mdcErrorThisPatternTooComplex,mdcErrorTooAllocFailure,mdcErrorCancel」が追加されます

- TpdLibrary
  - 構造体：BPIFSendKey
           メンバーは変更されます


変更時刻 : 2024-09-07

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/73
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/432

## 0.2.1
Library更新
- EmbLibrary
  - 関数：embResumeDataRead
         新規追加

変更時刻 : 2024-09-07

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/72
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/430

## 0.2.0
Library更新
- EmbLibrary
  - 関数：embMarkPatCnctEditParamSet
         新規追加
  - 構造体：MarkPatCnctSetting_t
           新規追加

変更時刻 : 2024-09-05

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/71
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/427


## 0.1.9
Library更新
変更はありません

変更時刻 : 2024-09-04

変更詳細は　以下二つのLinkを参考してください

https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/70
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/422

## 0.1.8
Library更新
- EmbLibrary
  - 関数：getEmbMarkPatCntAutoKind
         新規追加
  - 構造体：EmbEditAttribParam_t
           メンバー「isEmbroideryConnect」が追加されます
  - 構造体：startPointQuilt_t
           メンバー「startPointQuilt_Base」が追加されます
  - 構造体：embPatAutoKind_t
           新規追加

- MdcLibrary
  - 関数：changeMdcPhaseDetail
        パラメータ「emb」が削除されます
  - 関数：startMdcStitchCreation
  - 関数：cancelMdcStitchCreation
  - 関数：getMdcRoughZigzagLineProperty
         パラメータ「prm」のタイプが変更されます
  - 構造体：MdcSewZigzagParam_t
  - 構造体：MdcSewRoughZigzagParam_t
           パラメータ「color,density」のタイプが変更されます
  - 構造体：MdcSewRunningParam_t
  - 構造体：MdcSewTripleParam_t
  - 構造体：MdcSewCandleParam_t
  - 構造体：MdcSewChainParam_t
  - 構造体：MdcSewEParam_t
  - 構造体：MdcSewVParam_t
  - 構造体：MdcSewMotifParam_t
  - 構造体：MdcSewTatamiParam_t
  - 構造体：MdcSewStipParam_t
  - 構造体：MdcSewDecoParam_t
           パラメータ「color」のタイプが変更されます

- ProjectorLibrary
  - 関数：setProjectorEmbGridDrawKind
  - 関数：getProjectorEmbGridDrawKind
  - 関数：setProjectorEmbFrameDrawKind
  - 関数：getProjectorEmbFrameDrawKind
  - 関数：setProjectorEmbFrameOutOfRangeDrawKind
  - 関数：getProjectorEmbFrameOutOfRangeDrawKind
  - 関数：setProjectorSRStatusDisplay
  - 関数：getProjectorSRStatusDisplay
  - 関数：setProjectorSRSensingLineDisplay
  - 関数：getProjectorSRSensingLineDisplay
         新規追加
  - 構造体：ProjectorMode_t
           メンバー「projectorMode_Emb_Expanding,projectorMode_Emb_Quilt,projectorMode_Emb_Quilt_Expanding,projectorMode_Mdc_Expanding」が追加されます
  - 構造体：ProjectorEmbGridDrawKind_t
  - 構造体：ProjectorEmbFrameDrawKind_t
  - 構造体：ProjectorEmbFrameOutOfRangeDrawKind_t
           新規追加

- TpdLibrary
  - 構造体：BPIFSendError_t
           構造体名を修正しました

変更時刻 : 2024-09-03

変更詳細は　以下二つのLinkを参考してください
https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/69
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/419

## 0.1.7
Library更新
- CameraPprojectLibrary
  - 関数：CameraReleaseImage
    新規追加

- MdcLibrary
  - 構造体：MDC_Zigzag_Tatami_Density_t
    新規追加
  - 構造体：MDC_Rough_Zigzag_Density_t
    新規追加
  - 構造体：MdcFrameStampTypes
    内容が変更されました
  - 構造体：MdcSewMotifParam_t
    内容が変更されました

- TpdLibrary
  - 構造体：BPIFSendKey
    内容が変更されました

変更時刻 : 2024-08-29

変更詳細は　以下二つのLinkを参考してください

https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/68
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/411


## 0.1.6
Library更新
- DeviceLibrary
  - 関数：startCalibrationProjector
    新規追加

- ProjectorLibrary
  - 関数：getEmbProjectorAreaCenterPositionDefaultOffsetMM
    新規追加

- TpdLibrary
  - 関数：checkGotoMyIl
    新規追加

- UtlLibrary
  - 関数：setSRPfPos
    新規追加
  - 関数：setSRPfPosDefault
    新規追加

変更時刻 : 2024-08-28

変更詳細は　以下二つのLinkを参考してください

https://gitlab.phc.brother.co.jp/bsh/ph-eel-plugin/-/merge_requests/67
https://gitlab.phc.brother.co.jp/bsh/ph-app-common/-/merge_requests/406


## 0.1.5
Library更新
- EmbLibrary
  - 関数：mdcSelectBackgroundScan
    新規追加
  - 構造体：embGrp_t
    内容が変更されました
  - 構造体：embPtrn_t
    内容が変更されました

- MdcLibrary
  - 構造体：MdcErrorCode_t
    内容が変更されました
  - 構造体：MdcSewMotifParam_t
    内容が変更されました
  - 構造体：MdcSewDecoParam_t
    内容が変更されました

- TpdLibrary
  - 構造体：BPIFSendKey
    内容が変更されました

変更時刻 : 2024-08-22

変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/66
http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/396


## 0.1.3
Library更新
- EmbLibrary
  - 関数：moveEmbAllFrameNoMove
    新規追加
  - 関数：moveEmbAllCenterFrameNoMove
    新規追加
  - 関数：isEnabledSevedToExternalMemory
    新規追加
  - 関数：isEnabledSevedToInternalMemory
    新規追加
  - 構造体：EmbAttribParam_t
    内容が変更されました
  - 構造体：EmbSewingAttribParam_t
    新規追加
  - 構造体：EmbEditAttribParam_t
    新規追加
  - 構造体：embErrorCode_t
    新規追加

- ProjectorLibrary
  - 関数：startEmbProjectorPleaseWait
    新規追加
  - 関数：closeEmbProjectorPleaseWait
    新規追加

- TpdLibrary
  - 構造体：BPIFSendKey
    内容が変更されました
  - 構造体：ErrCode_t
    内容が変更されました
  - 構造体：BPIFEmbAppDisplayInfoPublicDef_t
    内容が変更されました

- UtlLibrary
  - 構造体：UtlAttribParam_t
    内容が変更されました

変更時刻 : 2024-08-21

変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/65
http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/391


## 0.1.2
Library更新
- EmbLibrary
  - 関数：scalingQuiltEmb
    新規追加
  - 関数：startPointSetQuiltEmb
    新規追加
  - 関数：selectPartsQuitEmb
    新規追加
  - 関数：embEditReturnSelect
    新規追加
  - 関数：embSewingReturnEdit
    新規追加
  - 関数：embGotoMDCPaint
    新規追加
  - 構造体：scalingQuilt_t
    新規追加
  - 構造体：startPointQuilt_t
    新規追加
  - 構造体：selectPartsQuilt_t
    新規追加

- MdcLibrary
  - 関数：isMdcPmfFileValid
    新規追加
  - 関数：isMdcPlfFileValid
    新規追加
  - 関数：checkMDCPaintToEmbSelect
    新規追加
  - 関数：gotoMDCPaintToEmbSelect
    新規追加
  - 関数：gotoMDCPaintToMDCDetailSet
    新規追加
  - 関数：gotoMDCDetailSetToMDCPaint
    新規追加
  - 関数：checkMDCDetailSetToEmbEdit
    新規追加
  - 関数：gotoMDCDetailSetToEmbEdit
    新規追加
  - 構造体：MdcErrorCode_t
    内容が変更されました

- ProjectorLibrary
  - 関数：checkErrorBeforeEmbProjectorStartClose
    新規追加
  - 関数：endProjectorEmbPatternProjection
    内容が変更されました 
  - 構造体：ProjectorErrorCode_t
    内容が変更されました

- TpdLibrary
  - 関数：suspendActionMatrixTask
    新規追加  
  - 関数：resumeActionMatrixTask
    新規追加  
  - 構造体：BPIFSendKey
    内容が変更されました

変更時刻 : 2024-08-17

変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/64
http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/376


## 0.1.1
Library更新
- DeviceLibrary
  - 関数：getCameraPenUIArrangeState
    新規追加
  - 関数：saveEmbCameraUIArrangeStateSetting
    新規追加
  - 関数：getUserLogProductInfo
    新規追加
  - 構造体：UserLogModelStatus_t
    新規追加
  - 構造体：UserLogUserSetting_t
    新規追加
  - 構造体：UserLogSoftVersion_t 
    新規追加
  - 構造体：UserLogProductInfo_t
    新規追加

- MdcLibrary 
  - 関数：isMdcUsingImportDataNumber
    内容が変更されました    
  - 構造体：MdcMaxDrawingImageTarget
    内容が変更されました

- EmbLibrary
  - 関数：checkPatternNoMirrorNoCharacter
    内容が変更されました  
  - 構造体：EmbTrialStartPosition_t
    内容が変更されました 
    
変更時刻 : 2024-08-09   

変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/60
http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/362


## 0.1.0
Library更新
- DeviceLibrary
  - 関数：getCameraPenUIArrangeState
    コンテンツが削除されました
  - 関数：saveEmbCameraUIArrangeStateSetting
    コンテンツが削除されました

- MdcLibrary
  - 関数：reserveMdcLineList
    内容が変更されました  
  - 関数：getMdcReservedLineDraw
    内容が変更されました  
  - 関数：noticeMdcDraw
    新規追加       

- TpdLibrary
  - 関数：BPIFSendDisplayDataSync
    新規追加  
  - 構造体：BPIFSendErrore_t
    新規追加
    
変更時刻 : 2024-08-03    

変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/56
http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/351


## 0.0.9
Library更新
- DeviceLibrary
  - 関数：setSetupVoiceGuideFlag
    新規追加
  - 関数：getSetupVoiceGuideFlag
    新規追加
  - 関数：resetSetupVoiceGuideFlag
    新規追加  

- EmbLibrary
  - 関数：curGroupCharArrayBack
    新規追加
  - 関数：setDecoFillStipple
    内容が変更されました  
  - 関数：cancelStipple
    新規追加  
  - 構造体：borderInfo_t
    内容が変更されました
  - 関数：checkBeforeBorderProc
    新規追加
  - 関数：checkBorderMarkStipple
    新規追加

- MdcLibrary
  - 構造体：MdcErrorCode_t
    内容が変更されました  
  - 構造体：MdcErrorCode_t_tmp
    新規追加   
  - 関数：saveFcmFileToPce
    コンテンツが削除されました
  - 関数：saveFcmDataToPce
    新規追加     

- TpdLibrary
  - 関数：gotoSR
    新規追加  
  - 関数：exitSR
    新規追加
  - 関数：gotoSRBastingWarning
    新規追加
  - 関数：exitSRBastingWarning
    新規追加  
  - 関数：checkGotoHome
    新規追加  
  - 構造体：BPIFGlobalAppInfo_t
    内容が変更されました     
  - 構造体：DirErrorCode_t
    コンテンツが削除されました 
  - 構造体：ErrCode_t
    内容が変更されました   
  - 構造体：BPIFSendKey
    内容が変更されました

- UtlLibrary
  - 関数：gotoSR
    コンテンツが削除されました  
  - 関数：exitSR
    コンテンツが削除されました
  - 関数：gotoSRBastingWarning
    コンテンツが削除されました
  - 関数：exitSRBastingWarning
    コンテンツが削除されました   
  - 構造体：SR_Mode_t
    内容が変更されました  
  - 構造体：SR_Status_t
    内容が変更されました    
  - 構造体：UtlAttribParam_t
    内容が変更されました   
  - 構造体：UtlStateInfo_t
    内容が変更されました          
    
変更時刻 : 2024-07-25    

変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/48
http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/327


## 0.0.8
Library更新
- DeviceLibrary
  - 関数：resetAllUserSetting
    新規追加
  - 関数：startAllUserSetting
    新規追加
  - 関数：finishAllUserSetting
    新規追加  
  - 構造体：ModeSpec_t 
    内容が変更されました

- EmbLibrary
  - 構造体：EmbAttribParam_t 
    内容が変更されました 
  - 構造体：embErrorCode_t 
    内容が変更されました
  - 構造体：EmbQuiltSashesFrameType_t 
    内容が変更されました  
  - 関数：checkPatternNoMirrorNoCharacter
    新規追加  

- MdcLibrary
  - 関数：setMdcSelectPictureForScanInfoAndData
    新規追加 
  - 関数：initScanMode
    内容が変更されました  
  - 関数：convertToOutlineImage
    内容が変更されました
  - 関数：convertToIllusrationImage
    内容が変更されました  

- NetworkLibrary    
  - 関数：initNtdCrypto
    新規追加
  - 関数：getNtdEncryptoInfo
    新規追加  
  - 関数：decryptoNtdEmbData
    新規追加 
  - 関数：getNtdEncryptoSign
    新規追加   

- TpdLibrary
  - 関数：setErrorState
    新規追加
  - 関数：setAppErrorState 
    新規追加  
  - 関数：clearAppErrorState 
    新規追加   
  - 構造体：BPIFSendKey
    内容が変更されました   
  - 構造体：InitState_t
    内容が変更されました    
  - 関数：BPIFSendDisplayDataSync 
    コンテンツが削除されました     
  - 構造体：BPIFSendKey
    内容が変更されました

- UtlLibrary
  - 関数：setTaperingStartAlignAndAngleStart
    新規追加
  - 関数：setTaperingEndAlignAndAngleStart
    新規追加  
  - 関数：setTaperingStartEndAlignAndAngleComfirm
    新規追加  
  - 関数：closeTapering
    内容が変更されました
  - 関数：openPreviewScreen
    内容が変更されました 
  - 関数：operatePreviewImage
    内容が変更されました   
  - 関数：setAspectFix
    新規追加  
  - 関数：setTaperingStartEndAlignAndAngleComfirm
    内容が変更されました  
  - 関数：UtlChangeDisable
    新規追加  
  - 構造体：UtlAttribParam_t
    内容が変更されました     
  - 関数：gotoSR
    新規追加  
  - 関数：exitSR
    新規追加
  - 関数：gotoSRBastingWarning
    新規追加
  - 関数：exitSRBastingWarning
    新規追加     
    
変更時刻 : 2024-07-18    

変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/46
http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/320


## 0.0.7
Library更新
- EmbLibrary
  - 関数：embGotoResumeOk 
    新規追加 
  - 関数：embSewingSelectFb 
    新規追加  
  - 関数：embSewingFbStitchMove 
    新規追加
  - 関数：embSewingFbCameraView 
    新規追加
  - 関数：embSewingFbEnd 
    新規追加
  - 関数：embSewingStartCameraFb
    新規追加
  - 関数：embSewingCloseCameraFb
    新規追加
  - 関数：embSewingChangeNeedleViewCameraFb
    新規追加
  - 関数：embSewingZoomCameraFb
    新規追加
  - 関数：embSewOverMarkPatCnct
    新規追加
  - 関数：embNextPatternSelMarkPatCnct
    新規追加  
  - 関数：embMarkPatCnctMove
    新規追加
  - 関数：embMarkPatCnctMoveLeft
    新規追加
  - 関数：embMarkPatCnctMoveRight
    新規追加  
  - 関数：embMarkPatCnctRotateRight90
    新規追加
  - 関数：embSetMarkPatCnctStdCancelBefore
    新規追加
  - 関数：embSetBefore1stMarkPatCnctOK
    新規追加
  - 関数：embSetBefore2ndMarkPatCnctOK
    新規追加  
  - 関数：embSewingSelectMaskTrace
    新規追加
  - 関数：embSewingMaskTraceExe
    新規追加
  - 関数：embSewingMaskTraceEnd
    新規追加
  - 関数：embSewingMaskTracePosSet
    新規追加
  - 関数：embSewingSelectStartPosition
    新規追加
  - 関数：embSewingStartPositionSet
    新規追加
  - 関数：embSewingStartPositionEnd
    新規追加
  - 構造体：EmbAttribParam_t 
    内容が変更されました

- ProjectorLibrary
  - 関数：startProjectorEmbPatternProjection
    内容が変更されました  
  - 関数：startFrameMoveEmbProjector
    新規追加 
  - 関数：notifyEmbProjectorStartComplete
    新規追加 
  - 関数：notifyEmbProjectorCloseComplete
    新規追加 

- TpdLibrary
  - 関数：BPIFRegisterStateCallback
    API は削除されました
  - 関数：BPIFRegisterErrorCallback 
    API は削除されました
  - 関数：BPIFRegisterBeepCallback 
    API は削除されました
  - 関数：BPIFUnregisterStateCallback 
    API は削除されました 
  - 関数：BPIFUnregisterErrorCallback 
    API は削除されました  
  - 関数：BPIFUnregisterBeepCallback 
    API は削除されました
  - 関数：BPIFGetState 
    API は削除されました
  - 構造体：BPIFSendKey 
    内容が変更されました 
  - 構造体：DeviceErrorCode_t 
    内容が変更されました
    
変更時刻 : 2024-07-10    

変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/41
http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/291


## 0.0.6
Library更新
- DeviceLibrary
  - 関数：setPresserFootHeight
    内容が変更されました
  - 関数：getPresserFootHeight
    内容が変更されました 
  - 関数：getPresserFootHeightValueList
    内容が変更されました   
  - 関数：getPresserFootPressure
    内容が変更されました 
  - 関数：setPivotingHeight
    内容が変更されました  
  - 関数：getPivotingHeight
    内容が変更されました  
  - 構造体：DeviceSettingInfo
    内容が変更されました  
  - 構造体：UtlUserSettingInfo 
    内容が変更されました 

- EmbLibrary
  - 構造体：embGrp_t 
    内容が変更されました 
  - 構造体：embInfo_t 
    内容が変更されました  

- MdcLibrary
  - 関数：reserveMdcLineList
    新規追加
  - 関数：getMdcReservedLineDraw
    新規追加 
  - 関数：getMdcStipplingDecofillMaxDrawingImage
    新規追加    
  - 構造体：MdcMaxDrawingImageTarget
    新規追加                            

- UtlLibrary
  - 関数：calibUtlProjector
    新規追加
  - 関数：closePreviewScreen
    内容が変更されました  
  - 構造体：UtlAttribParam_t
    内容が変更されました

- ProjectorLibrary
  - 関数：getProjectorTestModeImage
    内容が変更されました    

- TpdLibrary
  - 関数：closeChangeView
    新規追加
  - 構造体：BPIFSendKey 
    内容が変更されました
  - 構造体：BPIFTestMode27Param_t 
    内容が変更されました 
  - 構造体：AcView_t 
    構成体が削除されます  
  - 構造体：AcViewNdp_t 
    構成体が削除されます  
  - 構造体：AcViewLattice_t 
    構成体が削除されます
  - 構造体：ChangeView_t 
    内容が変更されました,名称変更ChangeView_t => BPIFChangeView_t 
    
変更時刻 : 2024-07-04    

変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/40
http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/272


## 0.0.5
Library更新
- DeviceLibrary
  - 関数：saveVoiceGuidanceOnOff
    新規追加
  - 関数：getVoiceGuidanceOnOff
    新規追加  
  - 関数：getMaxEmbroiderySpeedValueAllList
    lib实现
  - 関数：getSetupLangFlag
    新規追加
  - 関数：setSetupLangFlag
    新規追加
  - 関数：resetSetupLang
    新規追加
  - 構造体：CommonUserSettingInfo
    内容が変更されました  
  - 構造体：UserSettingEnabledInfo
    内容が変更されました
  - 構造体：FotHeelKick_FuncNum_t
    内容が変更されました

- EmbLibrary
  - 関数：embSewingBackgroundScanEnd
    新規追加
  - 関数：initAppliqueSelect
    新規追加
  - 関数：deleteMarkBeforeWappen
    新規追加
  - 関数：cancelBorderBeforeWappen
    新規追加
  - 関数：setTexture
    新規追加  
  - 関数：checkTextureDrawing
    新規追加
  - 関数：getWappenPreviewTexture
    新規追加  
  - 関数：completeSelectedApplique
    新規追加
  - 構造体：embErrorCode_t
    内容が変更されました  

- MdcLibrary
  - 関数：getMdcZigzagLineProperty
    新規追加
  - 関数：getMdcRunningStitchLineProperty
    新規追加 
  - 関数：getMdcTripleStitchLineProperty
    新規追加    
  - 関数：getMdcCandlwickingLineProperty
    新規追加 
  - 関数：getMdcChainStitchLineProperty
    新規追加   
  - 関数：getMdcEStitchLineProperty
    新規追加
  - 関数：getMdcVStitchLineProperty
    新規追加  
  - 関数：getMdcMotifLineProperty
    新規追加
  - 関数：getMdcRoughZigzagLineProperty
    新規追加  
  - 関数：getMdcTatamiSurfacePropety
    新規追加  
  - 関数：getMdcStipplingSurfaceProperty
    新規追加  
  - 関数：getMdcDecorativeFillSurfacePropety
    新規追加  

- TpdLibrary
  - 関数：gotoHomeFromOpening
    新規追加
  - 関数：startDemoMode
    新規追加
  - 構造体：bpIFSendDisplayData
    BPIFSendKey定義中にkey関数追加
    

- UtlLibrary
  - 構造体：SR_Mode_t
    新規追加
  - 構造体：SR_Status_t
    新規追加
  - 構造体：UtlAttribParam_t
    内容が変更されました  

変更時刻 : 2024-06-28    

変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/39
http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/249


## 0.0.4
Library更新
- DeviceLibrary
  - 関数：saveVoiceVolume   
    関数の名前が変更されます,lib実現,saveMessageSoundVolume => saveVoiceVolume
  - 関数：getVoiceVolume
    関数の名前が変更されます,lib実現,getMessageSoundVolume => getVoiceVolume
  - 関数：saveSRVolume
    関数の名前が変更されます,lib実現,saveSrSoundVolume => saveSRVolume
  - 関数：getSRVolume
    関数の名前が変更されます,lib実現,getSrSoundVolume => getSRVolume
  - 関数：saveInitialScreen
    関数の名前が変更されます：saveInitialScreen => setInitialScreen
  - 関数：getInitialScreenValueList
    新規追加
  - 関数：saveInitialScreen
    新規追加  
  - 関数：setThumbnailSize
    関数の名前が変更されます：setThumbnailSize => saveThumbnailSize
  - 関数：setThumbnailSize
    新規追加
  - 関数：getLedPtHeightPlus
    新規追加
  - 関数：getLedPtHeightPlusDefault
    新規追加
  - 関数：getLedPtBrightnessPlus
    新規追加
  - 関数：getLedPtBrightnessPlusDefault
    新規追加  
  - 構造体：CommonUserSettingInfo
    パラメータ調整
    
- EmbLibrary
  - 関数：setLedPointerON
    新規追加
  - 関数：setEmbTensionMinus
    新規追加
  - 関数：setEmbTensionPlus
    新規追加
  - 関数：setEndColorTrimOn
    新規追加
  - 関数：setEndColorTrimOff
    新規追加
  - 関数：setJumpStitchTrimOn
    新規追加
  - 関数：setJumpStitchTrimOff
    新規追加
  - 関数：setJumpStitchLengthMinus
    新規追加  
  - 関数：setJumpStitchLengthPlus
    新規追加  
  - 関数：embSewingSelectBackgroundScan
    新規追加
  - 関数：embSewingBackgroundScanStart
    新規追加
  - 関数：embSewingBackgroundScanCancel
    新規追加
  - 構造体：EmbAttribParam_t
    新規追加
  - 構造体：PresserFoot_t
    新規追加
  - 構造体：FrameSize_t
    新規追加 
  - 構造体：Emb_Frame_Type_t
    新規追加   

- MdcLibrary
  - 構造体：MdcErrorCode_t
    パラメータ調整

- TpdLibrary
  - 関数：BPIFGetAppDisplay_emb
    新規追加
  - 関数：gotoUtlPreviewFromMyIlEdit
    新規追加  
  - 関数：gotoUtlPreview
    新規追加
  - 構造体：BwdPanelState_t
    パラメータ調整  
  - 構造体：EcoSleepPanelState_t
    パラメータ調整
  - 構造体：BPIFGlobalAppInfo_t
    パラメータ調整
  - 構造体：BPIFUtlAppDisplayInfoPublicDef_t
    パラメータ調整 
  - 構造体：BPIFEmbAppDisplayInfoPublicDef_t
    新規追加   


変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/37
http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/230


## 0.0.3
Library更新
- EmbLibrary
  - 関数：getThumbnailSavedFile
    パラメーター「transparent」を追加する
  - 関数：getThumbnailPartsSavedFile
    パラメーター「transparent」を追加する
  - 関数：getCameraScanInfoAndData
    新規追加
  - 関数：getCameraScanImage
    新規追加
  - 関数：getCameraScanTestModeImage
    新規追加
  - 構造体：Link_t
    内容が変更されました

- MdcLibrary
  - 関数：isMdcDrawingPatternPresence
    新規追加

- TpdLibrary
  - 関数：getSSLedColor
    新規追加
  - 関数：BPIFGetAppDisplay_global
    BPIFGlobalAppInfo_t定義中パラメータ調整
  - 関数：BPIFGetTestModeData_tm64
    BPIFTestMode64Param_t定義中パラメータ調整

- UtlLibrary
  - 構造体：threadColor_t
    新規追加

- ProjectorLibrary
  - 構造体：ProjectorTestModeImage_t
    パラメータ調整

変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/35
http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/216


## 0.0.2
Library更新
- DeviceLibrary
  - 関数：saveScreenDisplayBrightness   
    名前変更、ScreenBrightnessは アプリ実現　-> libで実現。（今はinterface用意だけ）
  - 関数：removeEmbUnit
    DeviceLibraryからEmbLibraryに移動します

- EmbCharLibrary
  - 関数：getPf1CharExistList
    関数実現修正
  - 関数：cngSelectedPatternFontNumber
    関数実現修正

- EmbLibrary
  - 関数：shuffleRandom
    関数実現修正
  - 関数：shuffleGradation
    関数実現修正
  - 関数：removeEmbUnit
    DeviceLibraryからEmbLibraryに移動します

- MdcLibrary
  - 関数：getMdcImageThumbnail
    関数実現修正、パラメータ変更
  - 関数：getMdcBuiltInDecorativeFillThumbnail
    関数実現修正、パラメータ変更
  - 関数：getMdcBuiltInLineMotifThumbnail
    関数実現修正、パラメータ変更
  - 関数：getMdcCustomDecorativeFillThumbnail
    関数実現修正、パラメータ変更
  - 関数：getMdcCustomLineMotifThumbnail
    関数実現修正、パラメータ変更
  - 関数：getMdcCustomLineMotifThumbnail
    関数実現修正、パラメータ変更
- TpdLibrary
  - 関数：bpIFInit
    関数実現修正、戻る定義変更
  - 関数：bpIFGetError
    関数実現修正、戻る定義変更
  - 関数：bpIFSendDisplayData
    BPIFSendKey定義中にkey関数追加
  - 関数: bpIFGetAppDisplayGlobal
    BPIFGlobalAppInfo定義中パラメータ調整
  - 関数: bpIFGetAppDisplayUtl
    BPIFUtlAppDisplayInfoPublicDef 定義中パラメータ調整
  - 関数: getInitState
    新規追加

変更詳細は　以下二つのLinkを参考してください

http://apbil2030891:10088/bsh/ph-eel-plugin/-/merge_requests/33

http://apbil2030891:10088/bsh/ph-app-common/-/merge_requests/205

## 0.0.1
初版作成









