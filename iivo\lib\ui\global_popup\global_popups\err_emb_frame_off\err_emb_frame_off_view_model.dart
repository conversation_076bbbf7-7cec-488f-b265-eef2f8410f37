import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_emb_frame_off_view_interface.dart';

final errEmbFrameOffViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrEmbFrameOffViewInterface, ErrEmbFrameOffState,
        BuildContext>((ref, context) => ErrEmbFrameOffViewModel(ref, context));

class ErrEmbFrameOffViewModel extends ErrEmbFrameOffViewInterface
    with DeviceLibraryEventObserver {
  ErrEmbFrameOffViewModel(Ref ref, BuildContext context)
      : super(const ErrEmbFrameOffState(), ref, context);

  @override
  void onOKButtonClicked() {
    int errcode;
    errcode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERRFRAMEOFF);
    if (errcode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }
  }
}
