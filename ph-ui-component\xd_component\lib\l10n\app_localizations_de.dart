import 'app_localizations.dart';

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get color_001 => 'ROSA';

  @override
  String get color_002 => 'STAUBROSE';

  @override
  String get color_003 => 'BLÜTENBLATT-\nROSA';

  @override
  String get color_004 => 'HELLROSA';

  @override
  String get color_005 => 'HELLKORALLE';

  @override
  String get color_006 => 'INGWERROT';

  @override
  String get color_007 => 'ERIKAROT';

  @override
  String get color_008 => 'CHAMPAGNER';

  @override
  String get color_009 => 'DUNKELLILA';

  @override
  String get color_010 => 'DUNKEL\nERIKAROT';

  @override
  String get color_011 => 'KOMFORTROSA';

  @override
  String get color_012 => 'BERGROSE';

  @override
  String get color_013 => 'KIRSCHROSA';

  @override
  String get color_014 => 'NELKE';

  @override
  String get color_015 => 'LACHS';

  @override
  String get color_016 => 'GARNELE';

  @override
  String get color_017 => 'DUNKEL\nKORALL';

  @override
  String get color_018 => 'BITTERWURZEL';

  @override
  String get color_019 => 'BURGUND';

  @override
  String get color_020 => 'WARMES\nWEINROT';

  @override
  String get color_021 => 'ROTBRAUN';

  @override
  String get color_022 => 'PFLAUME';

  @override
  String get color_023 => 'KASTANI-\nENBRAUN';

  @override
  String get color_024 => 'KÖNIGSROT';

  @override
  String get color_025 => 'KNALLROSA';

  @override
  String get color_026 => 'RUBIN';

  @override
  String get color_027 => 'DUNKEL\nVIOLETT';

  @override
  String get color_028 => 'KARMINROT';

  @override
  String get color_029 => 'TIEFROT';

  @override
  String get color_030 => 'BEGONIE';

  @override
  String get color_031 => 'AZALEE';

  @override
  String get color_032 => 'RUBINROT';

  @override
  String get color_033 => 'ERDBEERE';

  @override
  String get color_034 => 'TEUFELSROT';

  @override
  String get color_035 => 'ZUCKER-\nAPFELROT';

  @override
  String get color_036 => 'STOCK-\nROSENROT';

  @override
  String get color_037 => 'ROSTROT';

  @override
  String get color_038 => 'WILDES FEUER';

  @override
  String get color_039 => 'ROT';

  @override
  String get color_040 => 'JOCKEYROT';

  @override
  String get color_041 => 'STRAHLENDES\nROT';

  @override
  String get color_042 => 'ROTE BEERE';

  @override
  String get color_043 => 'FUCHSROT';

  @override
  String get color_044 => 'LIPPENSTIFT';

  @override
  String get color_045 => 'WEIHNACHTS-\nROT';

  @override
  String get color_046 => 'SCHARLACH-\nROT';

  @override
  String get color_047 => 'TIEFES\nSCHARL.ROT';

  @override
  String get color_048 => 'PREISELBEERE';

  @override
  String get color_049 => 'FLEISCH\nFARBEN';

  @override
  String get color_050 => 'PORZELLAN';

  @override
  String get color_051 => 'FLAMINGO';

  @override
  String get color_052 => 'MELONE';

  @override
  String get color_053 => 'PFIRSICHMELBA';

  @override
  String get color_054 => 'GEIßBLATT';

  @override
  String get color_055 => 'DUNKEL-\nORANGE';

  @override
  String get color_056 => 'KLARES BLAU';

  @override
  String get color_057 => 'BLAUE FREUDE';

  @override
  String get color_058 => 'EISBLAU';

  @override
  String get color_059 => 'MITTELBLAU';

  @override
  String get color_060 => 'ROCKPORT-\nBLAU';

  @override
  String get color_061 => 'PASTELLBLAU';

  @override
  String get color_062 => 'BABYBLAU';

  @override
  String get color_063 => 'HIMMELBLAU';

  @override
  String get color_064 => 'HELLBLAU';

  @override
  String get color_065 => 'SEEBLAU';

  @override
  String get color_066 => 'GLÜCKSBLAU';

  @override
  String get color_067 => 'ULTRABLAU';

  @override
  String get color_068 => 'TROPENBLAU';

  @override
  String get color_069 => 'KORNBLU\nMENBLAU';

  @override
  String get color_070 => 'BLAUER MOND';

  @override
  String get color_071 => 'SAPHIR';

  @override
  String get color_072 => 'SCHIEFERBLAU';

  @override
  String get color_073 => 'DUNKELBLAU';

  @override
  String get color_074 => 'TIEFBLAU';

  @override
  String get color_075 => 'BLAUES\nWUNDER';

  @override
  String get color_076 => 'BALTISCHBLAU';

  @override
  String get color_077 => 'KALIFOR-\nNIABLAU';

  @override
  String get color_078 => 'COELINBLAU';

  @override
  String get color_079 => 'SONNENBLAU';

  @override
  String get color_080 => 'ELEKTRISCH-\nBLAU';

  @override
  String get color_081 => 'PAZIFISCHBLAU';

  @override
  String get color_082 => 'BLAU';

  @override
  String get color_083 => 'KAISERBLAU';

  @override
  String get color_084 => 'FLOTTENBLAU';

  @override
  String get color_085 => 'BLAUES BAND';

  @override
  String get color_086 => 'MARINEBLAU\nHELL';

  @override
  String get color_087 => 'MARINEBLAU\nMITTEL';

  @override
  String get color_088 => 'REICHSBLAU';

  @override
  String get color_089 => 'MITTER-\nNACHTSBLAU';

  @override
  String get color_090 => 'DUNKLER\nSAPHIR';

  @override
  String get color_091 => 'WILDLEDER-\nBLAU';

  @override
  String get color_092 => 'ULTRA MARIN';

  @override
  String get color_093 => 'KÖNIGSBLAU';

  @override
  String get color_094 => 'KOBALTBLAU';

  @override
  String get color_095 => 'BERLINER BLAU';

  @override
  String get color_096 => 'NASSAUBLAU';

  @override
  String get color_097 => 'DOHLENBLAU';

  @override
  String get color_098 => 'PARISBLAU';

  @override
  String get color_099 => 'BLAUER\nLAVENDEL';

  @override
  String get color_100 => 'LAVENDEL\nMITTEL';

  @override
  String get color_101 => 'LAVENDEL';

  @override
  String get color_102 => 'TULPEN-\nLAVENDEL';

  @override
  String get color_103 => 'WISTARIA\nVIOLETT';

  @override
  String get color_104 => 'KÖNIGINPURPUR';

  @override
  String get color_105 => 'BLEIGRAUER\nLAVENDEL';

  @override
  String get color_106 => 'PURPURWAGEN';

  @override
  String get color_107 => 'PURPUR-\nLABYRINTH';

  @override
  String get color_108 => 'TIEFES PURPUR';

  @override
  String get color_109 => 'PURPURAKZENT';

  @override
  String get color_110 => 'KÖNIGSPURPUR';

  @override
  String get color_111 => 'PURPUR\nROYALE';

  @override
  String get color_112 => 'MODERN-\nPURPUR';

  @override
  String get color_113 => 'PURPUR';

  @override
  String get color_114 => 'VIOLETT';

  @override
  String get color_115 => 'BLASSPURPUR';

  @override
  String get color_116 => 'MAGENTA';

  @override
  String get color_117 => 'HELLLILA';

  @override
  String get color_118 => 'LILA';

  @override
  String get color_119 => 'SIBIRISCH\nBLAUE LILIE';

  @override
  String get color_120 => 'PHANTASIE-\nROSA';

  @override
  String get color_121 => 'TRAUM ROSA';

  @override
  String get color_122 => 'EXKLUSIV\nROSA';

  @override
  String get color_123 => 'ROSA PRACHT';

  @override
  String get color_124 => 'WILDES ROSA';

  @override
  String get color_125 => 'BERNSTEINROT';

  @override
  String get color_126 => 'HIMBEEREIS';

  @override
  String get color_127 => 'AUBERGINE';

  @override
  String get color_128 => 'BAMBINOBLAU';

  @override
  String get color_129 => 'BLAUE\nFRANSEN';

  @override
  String get color_130 => 'MEERWASSER';

  @override
  String get color_131 => 'BLAUE\nGLYZINIE';

  @override
  String get color_132 => 'ENGELSBLAU';

  @override
  String get color_133 => 'ENTENBLAU';

  @override
  String get color_134 => 'PFAUENBLAU';

  @override
  String get color_135 => 'DUNKEL JADE';

  @override
  String get color_136 => 'PARADIESGRÜN';

  @override
  String get color_137 => 'KRICKENTEN';

  @override
  String get color_138 => 'VENUSBLAU';

  @override
  String get color_139 => 'FEDERBLAU';

  @override
  String get color_140 => 'MINZWHISKEY';

  @override
  String get color_141 => 'MEERKRISTALL';

  @override
  String get color_142 => 'TÜRKIS';

  @override
  String get color_143 => 'ZAUBER-\nKRICKENTE';

  @override
  String get color_144 => 'WASSER';

  @override
  String get color_145 => 'OZEAN GRÜN';

  @override
  String get color_146 => 'STURMTÜRKIS';

  @override
  String get color_147 => 'MD-GRÜN';

  @override
  String get color_148 => 'DUNKEL\nKRICKENTE';

  @override
  String get color_149 => 'OZEANWASSER';

  @override
  String get color_150 => 'BLAUE KIEFER';

  @override
  String get color_151 => 'NEWPORT';

  @override
  String get color_152 => 'GRÜNE BUCHT';

  @override
  String get color_153 => 'BLASSGRÜN';

  @override
  String get color_154 => 'KIWIGRÜN';

  @override
  String get color_155 => 'OLIVGRÜN';

  @override
  String get color_156 => 'HAFENGRÜN';

  @override
  String get color_157 => 'SPEZIALGRÜN';

  @override
  String get color_158 => 'DUNKLES\nARMEEGRÜN';

  @override
  String get color_159 => 'SCHWERES\nGRÜN';

  @override
  String get color_160 => 'ALPENGRÜN';

  @override
  String get color_161 => 'FELDGRÜN';

  @override
  String get color_162 => 'GRÜNES SEGEL';

  @override
  String get color_163 => 'SCHWARZE\nKRICKENTE';

  @override
  String get color_164 => 'MEERESNEBEL';

  @override
  String get color_165 => 'WEIDEGRÜN';

  @override
  String get color_166 => 'TEEBLATT';

  @override
  String get color_167 => 'INSELGRÜN';

  @override
  String get color_168 => 'WALDKIEFER';

  @override
  String get color_169 => 'JADE';

  @override
  String get color_170 => 'PFEFFERMINZ';

  @override
  String get color_171 => 'BLAUGRÜN';

  @override
  String get color_172 => 'DUNKELGRÜN';

  @override
  String get color_173 => 'KLASSISCHES\nGRÜN';

  @override
  String get color_174 => 'DUNKLES\nKIEFERGRÜN';

  @override
  String get color_175 => 'GRÜN';

  @override
  String get color_176 => 'IRISCHGRÜN';

  @override
  String get color_177 => 'EMERALDGRÜN';

  @override
  String get color_178 => 'FELDKLEE';

  @override
  String get color_179 => 'MOOSGRÜN';

  @override
  String get color_180 => 'HELLES KELLY';

  @override
  String get color_181 => 'KELLY';

  @override
  String get color_182 => 'BLATTGRÜN';

  @override
  String get color_183 => 'LEICHTES GRÜN';

  @override
  String get color_184 => 'GRÜNE EICHE';

  @override
  String get color_185 => 'PFEFFER\nMINZGRÜN';

  @override
  String get color_186 => 'FRISCH GRÜN';

  @override
  String get color_187 => 'BOHNENHÜLSE';

  @override
  String get color_188 => 'WEIDENGRÜN';

  @override
  String get color_189 => 'HELLE\nAVOCADO';

  @override
  String get color_190 => 'ERNTEGRÜN';

  @override
  String get color_191 => 'GRÜNER STAUB';

  @override
  String get color_192 => 'LIMONEN GRÜN';

  @override
  String get color_193 => 'IRLANDGRÜN';

  @override
  String get color_194 => 'LAUBGRÜN';

  @override
  String get color_195 => 'SONNENBLUME';

  @override
  String get color_196 => 'GOLD';

  @override
  String get color_197 => 'HERBSTGRÜN';

  @override
  String get color_198 => 'BRAUNE OLIVE';

  @override
  String get color_199 => 'WIESE';

  @override
  String get color_200 => 'SALBEI';

  @override
  String get color_201 => 'DUNKEL-\nOLIVGRÜN';

  @override
  String get color_202 => 'ARMEEGRÜN';

  @override
  String get color_203 => 'KRÖNUNGS-\nGOLD';

  @override
  String get color_204 => 'ZITRONEN GELB';

  @override
  String get color_205 => 'HELLGELB';

  @override
  String get color_206 => 'GELB';

  @override
  String get color_207 => 'UMBRA';

  @override
  String get color_208 => 'MANILA';

  @override
  String get color_209 => 'GOLDRUTE';

  @override
  String get color_210 => 'WARMER\nSONNENSCHEIN';

  @override
  String get color_211 => 'POLLENGOLD';

  @override
  String get color_212 => 'TAGESLILIE';

  @override
  String get color_213 => 'STERNENGOLD';

  @override
  String get color_214 => 'GOLDENER\nSONNENSCHEIN';

  @override
  String get color_215 => 'MESSING\nFARBEN';

  @override
  String get color_216 => 'ORANGE';

  @override
  String get color_217 => 'TIEFGOLD';

  @override
  String get color_218 => 'ERNTEGOLD';

  @override
  String get color_219 => 'GELBER NEBEL';

  @override
  String get color_220 => 'SENF';

  @override
  String get color_221 => 'KUPFER';

  @override
  String get color_222 => 'GRILLORANGE';

  @override
  String get color_223 => 'LIMONADE';

  @override
  String get color_224 => 'PAPRIKA';

  @override
  String get color_225 => 'ZINNOBER ROT';

  @override
  String get color_226 => 'SAFRANGELB';

  @override
  String get color_227 => 'ROSTROT';

  @override
  String get color_228 => 'TERRAKOTTA';

  @override
  String get color_229 => 'DUNKLER ROST';

  @override
  String get color_230 => 'DUNKELMELONE';

  @override
  String get color_231 => 'GELBBRAUN';

  @override
  String get color_232 => 'WEICHER\nPFIRSICH';

  @override
  String get color_233 => 'ROST';

  @override
  String get color_234 => 'DUNKEL-\nAPRIKOSE';

  @override
  String get color_235 => 'MANDARI\nNENORANGE';

  @override
  String get color_236 => 'KÜRBISGELB';

  @override
  String get color_237 => 'SONNEN-\nFLAMME';

  @override
  String get color_238 => 'GOLDENES\nGEWÜRZ';

  @override
  String get color_239 => 'HONIG';

  @override
  String get color_240 => 'MANDELN';

  @override
  String get color_241 => 'ROTBRAUN';

  @override
  String get color_242 => 'LEHMBRAUN';

  @override
  String get color_243 => 'ROTBRAUN';

  @override
  String get color_244 => 'BRAUNWEISS';

  @override
  String get color_245 => 'GELBLICH\nWEISS';

  @override
  String get color_246 => 'GLANZ';

  @override
  String get color_247 => 'PISTAZIE';

  @override
  String get color_248 => 'GOLDENE\nBRÄUNE';

  @override
  String get color_249 => 'INGWER';

  @override
  String get color_250 => 'TEMPELGOLD';

  @override
  String get color_251 => 'BRÄUNE';

  @override
  String get color_252 => 'SONNENBRÄUNE';

  @override
  String get color_253 => 'KANDISZUCKER';

  @override
  String get color_254 => 'BEIGE';

  @override
  String get color_255 => 'RATTAN';

  @override
  String get color_256 => 'DUNKELBEIGE';

  @override
  String get color_257 => 'BRONZE';

  @override
  String get color_258 => 'KAFFEE';

  @override
  String get color_259 => 'LEINENFARBIG';

  @override
  String get color_260 => 'MEERES-\nMUSCHEL';

  @override
  String get color_261 => 'NATURFARBEN';

  @override
  String get color_262 => 'LACHSROSA';

  @override
  String get color_263 => 'HELLER KAKAO';

  @override
  String get color_264 => 'STAUBIGER\nPFIRSICH';

  @override
  String get color_265 => 'HELLBRAUN';

  @override
  String get color_266 => 'KHAKIBRAUN';

  @override
  String get color_267 => 'KAFFEEBOHNE';

  @override
  String get color_268 => 'BRAUNER\nSANDSTEIN';

  @override
  String get color_269 => 'TIEFES BRAUN';

  @override
  String get color_270 => 'DUNKELBRAUN';

  @override
  String get color_271 => 'BRAUN';

  @override
  String get color_272 => 'GLÜCKSPFAD';

  @override
  String get color_273 => 'DUNKLER\nMAULWURF';

  @override
  String get color_274 => 'WARMGRAU';

  @override
  String get color_275 => 'TIEFES GRAU';

  @override
  String get color_276 => 'METALL';

  @override
  String get color_277 => 'CHROM-\nSCHWARZ';

  @override
  String get color_278 => 'HOLZKOHLE';

  @override
  String get color_279 => 'MITTELGRAU';

  @override
  String get color_280 => 'KÜHLES GRAU';

  @override
  String get color_281 => 'RAUCHGRAU';

  @override
  String get color_282 => 'ZINNGRAU';

  @override
  String get color_283 => 'DUNKEL GRAU';

  @override
  String get color_284 => 'GRAU';

  @override
  String get color_285 => 'HELLGRAU';

  @override
  String get color_286 => 'CHROM';

  @override
  String get color_287 => 'ANTIKGOLD';

  @override
  String get color_288 => 'SILBER';

  @override
  String get color_289 => 'SCHWARZ';

  @override
  String get color_290 => 'NATURWEIß';

  @override
  String get color_291 => 'WEISS';

  @override
  String get color_292 => 'NEONROSA';

  @override
  String get color_293 => 'DUNKELROSA';

  @override
  String get color_294 => 'WASSER-\nMELONE';

  @override
  String get color_295 => 'WEICHES ROSA';

  @override
  String get color_296 => 'HELLE\nZUCKERMELONE';

  @override
  String get color_297 => 'PFIRSICHNEBEL';

  @override
  String get color_298 => 'HAVANNAGELB';

  @override
  String get color_299 => 'DUNKEL\nKIRSCHE';

  @override
  String get color_300 => 'TINTENBLAU';

  @override
  String get color_301 => 'MATERIAL';

  @override
  String get color_302 => 'POSITION DER\nAPPLIKATION';

  @override
  String get color_303 => 'APPLIKATION';

  @override
  String get id_icon_test00001 => '\$\$\$\$\$';

  @override
  String get icon_00002 => 'Nähen';

  @override
  String get icon_00003_1 => 'Sticken';

  @override
  String get icon_00006_3 => 'Nutzstich';

  @override
  String get icon_00007_3 => 'Buchstaben-/\nDekorstich';

  @override
  String get icon_stitch => 'Stich';

  @override
  String get icon_close_1 => 'Schliessen';

  @override
  String get icon_cancel => 'Abbrechen';

  @override
  String get icon_ok => 'OK';

  @override
  String get icon_00011_zz => '%%%icon%%%';

  @override
  String get icon_00011_zz_s => '%%%icon%%%';

  @override
  String get icon_00011 => 'Lösch';

  @override
  String get icon_00012_zz => '%%%icon%%%';

  @override
  String get icon_00012_zz_s => '%%%icon%%%';

  @override
  String get icon_reset_zz => '%%%icon%%%';

  @override
  String get icon_reset_zz_s => '%%%icon%%%';

  @override
  String get icon_reset => 'Zurücks.';

  @override
  String get icon_reset_v => 'Zurücksetzen';

  @override
  String get icon_00014_zz => '%%%icon%%%';

  @override
  String get icon_00014_zz_s => '%%%icon%%%';

  @override
  String get icon_00014 => 'Speicher';

  @override
  String get icon_save => 'Speichern';

  @override
  String get icon_00015_zz => '%%%icon%%%';

  @override
  String get icon_00015_zz_s => '%%%icon%%%';

  @override
  String get icon_util_mem_retrieve => 'Abrufen';

  @override
  String get icon_util_mem_memory => 'Speicher';

  @override
  String get icon_util_mem_reset => 'Zurück-\nsetzen';

  @override
  String get icon_util_mem_delete => 'Löschen';

  @override
  String get icon_util_mem_alldelete => 'Alles löschen';

  @override
  String get icon_00017_zz => '%%%icon%%%';

  @override
  String get icon_00017_zz_s => '%%%icon%%%';

  @override
  String get icon_00018_zz => '%%%icon%%%';

  @override
  String get icon_00018_zz_s => '%%%icon%%%';

  @override
  String get icon_00019_zz => '%%%icon%%%';

  @override
  String get icon_00019_zz_s => '%%%icon%%%';

  @override
  String get icon_00020_zz => '%%%icon%%%';

  @override
  String get icon_00020_zz_s => '%%%icon%%%';

  @override
  String get icon_util_width => 'Breite';

  @override
  String get icon_util_length => 'Länge';

  @override
  String get icon_util_lrshift => 'L/R Shift';

  @override
  String get icon_util_tension => 'Spannung';

  @override
  String get icon_util_slitlength => 'Schlitz';

  @override
  String get icon_00021_zz => '%%%icon%%%';

  @override
  String get icon_00021_zz_s => '%%%icon%%%';

  @override
  String get icon_00022_zz => '%%%icon%%%';

  @override
  String get icon_00022_zz_s => '%%%icon%%%';

  @override
  String get icon_00027_zz => '%%%icon%%%';

  @override
  String get icon_00027_zz_s => '%%%icon%%%';

  @override
  String get icon_00028_zz => '%%%icon%%%';

  @override
  String get icon_00028_zz_s => '%%%icon%%%';

  @override
  String get icon_00029_zz => '%%%icon%%%';

  @override
  String get icon_00029_zz_s => '%%%icon%%%';

  @override
  String get icon_00038_zz => '%%%icon%%%';

  @override
  String get icon_00038_zz_s => '%%%icon%%%';

  @override
  String get icon_00030_1 => 'Test';

  @override
  String get icon_guidel_guideline => 'Führungslinie';

  @override
  String get icon_guidel_main => 'Haupt-';

  @override
  String get icon_guidel_sub => 'Hilfs-';

  @override
  String get icon_guidel_mainline => 'Hauptlinie';

  @override
  String get icon_guidel_subline => 'Hilfslinie';

  @override
  String get icon_guidel_linelength => 'Linienlänge';

  @override
  String get icon_guidel_line_l => 'L';

  @override
  String get icon_guidel_line_m => 'M';

  @override
  String get icon_guidel_line_s => 'S';

  @override
  String get icon_guidel_color => 'Farbe';

  @override
  String get icon_guidel_position => 'Position';

  @override
  String get icon_guidel_main_pos => 'Hauptlinienposition';

  @override
  String get icon_guidel_sub_pos => 'Hilfslinienposition';

  @override
  String get icon__guidel_sub_frommain => 'Entfernung von der Hauptlinie';

  @override
  String get icon_guidel_gridsize => 'Rastergröße';

  @override
  String get icon_guidel_angle => 'Winkel';

  @override
  String get icon_guidel_seamallowance => 'Nahtzugabe';

  @override
  String get icon_guidel_spacing => 'Abstand';

  @override
  String get icon_guidel_lengthl_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengthl_zz_s => '%%%icon%%%';

  @override
  String get icon_guidel_lengthm_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengthm_zz_s => '%%%icon%%%';

  @override
  String get icon_guidel_lengths_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengths_zz_s => '%%%icon%%%';

  @override
  String get icon_position => 'Position';

  @override
  String get icon_00031_2 => 'Editieren';

  @override
  String get icon_00033_1 => 'Hinzufügen';

  @override
  String get icon_00035 => 'Sticken';

  @override
  String get icon_return => 'Zum\nAnfang';

  @override
  String get icon_00038_1 => 'Einstellen';

  @override
  String get icon_00038_2 => 'Ein-\nstellen';

  @override
  String get icon_00039 => 'min';

  @override
  String get icon_00041_1 => 'Wählen';

  @override
  String get icon_select => 'Wählen';

  @override
  String get icon_select_2 => 'Wählen';

  @override
  String get icon_00041_2 => 'Wählen';

  @override
  String get icon_00042 => 'Den Knopf immer\nbeim Abnehmen des\nSticktisches drücken.';

  @override
  String get icon_00046_zz => '%%%icon%%%';

  @override
  String get icon_00046_zz_s => '%%%icon%%%';

  @override
  String get icon_00048 => 'Nummer';

  @override
  String get icon_00049 => 'Liste';

  @override
  String get icon_00050 => 'Laden';

  @override
  String get icon_00051_zz => '%%%icon%%%';

  @override
  String get icon_00051_zz_s => '%%%icon%%%';

  @override
  String get icon_00052_zz => '%%%icon%%%';

  @override
  String get icon_00052_zz_s => '%%%icon%%%';

  @override
  String get icon_00053_b1 => '%%%none%%%';

  @override
  String get icon_00053_b2 => '%%%none%%%';

  @override
  String get icon_00053_t1 => 'Color Visualizer';

  @override
  String get icon_00053_t2 => 'Color\nVisualizer';

  @override
  String get icon_00055_1 => 'Beliebig';

  @override
  String get icon_00055_2 => 'Beliebig';

  @override
  String get icon_00056_1 => 'Abstufung';

  @override
  String get icon_00056_2 => 'Abstufung';

  @override
  String get icon_00057 => 'Intensiv';

  @override
  String get icon_00054 => 'Weich';

  @override
  String get icon_00058_1 => 'Farbanzahl';

  @override
  String get icon_00059 => 'Aktuali-\nsieren';

  @override
  String get icon_00060 => 'Ohne Angabe';

  @override
  String get icon_emb_tension => 'Spannung';

  @override
  String get icon_emb_threadcutting => 'Faden-\nabschneiden';

  @override
  String get icon_00063_a => 'Farbenende abschneiden';

  @override
  String get icon_00064_a => 'Sprungstiche abschneiden';

  @override
  String get icon_00065 => 'Dichte';

  @override
  String get icon_00066 => 'Größe';

  @override
  String get icon_00067_zz => '%%%icon%%%';

  @override
  String get icon_00067_zz_s => '%%%icon%%%';

  @override
  String get icon_00068_zz => '%%%icon%%%';

  @override
  String get icon_00068_zz_s => '%%%icon%%%';

  @override
  String get icon_00070_zz => '%%%icon%%%';

  @override
  String get icon_00070_zz_s => '%%%icon%%%';

  @override
  String get icon_00071_zz => '%%%icon%%%';

  @override
  String get icon_00071_zz_s => '%%%icon%%%';

  @override
  String get icon_00072 => 'Layout';

  @override
  String get icon_00075_zz => '%%%icon%%%';

  @override
  String get icon_00075_zz_s => '%%%icon%%%';

  @override
  String get icon_00076_zz => '%%%icon%%%';

  @override
  String get icon_00076_zz_s => '%%%icon%%%';

  @override
  String get icon_00077_zz => '%%%icon%%%';

  @override
  String get icon_00077_zz_s => '%%%icon%%%';

  @override
  String get icon_00079 => 'Nadeleinstich-Position';

  @override
  String get icon_00080 => 'Weiter';

  @override
  String get icon_prev => 'Vorherige';

  @override
  String get icon_segment => 'Segment';

  @override
  String get icon_00083 => 'Endpunkteinstellung';

  @override
  String get icon_00084 => 'Längenanpassung';

  @override
  String get icon_00085 => 'Endpunkteinstellung\nVorübergehend anhalten';

  @override
  String get icon_00088 => 'Scannen';

  @override
  String get icon_00089 => 'Video';

  @override
  String get icon_00090 => 'Video wiederholen';

  @override
  String get icon_00091_1 => 'Mehrfachauswahl';

  @override
  String get icon_00091_2 => 'Mehrfach-\nauswahl';

  @override
  String get icon_00093_zz => '%%%icon%%%';

  @override
  String get icon_00093_zz_s => '%%%icon%%%';

  @override
  String get icon_00094_zz => '%%%icon%%%';

  @override
  String get icon_00094_zz_s => '%%%icon%%%';

  @override
  String get icon_00095 => 'Beenden';

  @override
  String get icon_00096 => 'Quiltstreifen automatisch teilen';

  @override
  String get icon_resettodef => 'Auf Standardeinstellungen zurücksetzen';

  @override
  String get icon_resettodefall => 'Auf Standardeinstellungen zurücksetzen';

  @override
  String get icon_resettodefall_2 => 'Auf Standardeinstellungen\nzurücksetzen';

  @override
  String get icon_00100 => 'Sprache';

  @override
  String get icon_00101_a => 'Deutsch';

  @override
  String get icon_00101_b => 'German';

  @override
  String get icon_00102 => 'Helligkeit';

  @override
  String get icon_00103 => 'Helligkeit der\nBildschirmanzeige';

  @override
  String get icon_00104 => 'Bildschirmschoner';

  @override
  String get icon_00105 => 'Vorgabe';

  @override
  String get icon_00106 => 'Anpassen';

  @override
  String get icon_00107 => 'Eco-Modus';

  @override
  String get icon_00108 => 'Abschalt-\nSupportmodus';

  @override
  String get icon_00109 => 'Licht';

  @override
  String get icon_00112 => 'Lautstärke des Maschinen-\nlautsprechers';

  @override
  String get icon_00114 => 'Lautstärke';

  @override
  String get icon_00115 => 'Mauszeiger';

  @override
  String get icon_00116 => 'mm / \"(inch)';

  @override
  String get icon_00118 => 'Startseite';

  @override
  String get icon_00119 => 'Näh-/\nStickbildschirm';

  @override
  String get icon_00192 => 'Startbildschirm';

  @override
  String get icon_00121 => 'Startbildschirm';

  @override
  String get icon_00122 => 'Erste Stichseite';

  @override
  String get icon_00123 => 'Garnrollenständer-LED';

  @override
  String get icon_00124 => 'Stichbreitensteuerung';

  @override
  String get icon_00125_1 => 'Feineinstellung\nVertikal';

  @override
  String get icon_00126_1 => 'Feineinstellung\nHorizontal';

  @override
  String get icon_00127_1 => 'Nähfußhöhe';

  @override
  String get icon_00128_1 => 'Nähfußdruck';

  @override
  String get icon_00129 => 'Anfangsposition';

  @override
  String get icon_00130_1 => 'Drehhöhe';

  @override
  String get icon_00131_1 => 'Freihandfußhöhe';

  @override
  String get icon_00134 => 'Automatisches\nStoffsensorsystem';

  @override
  String get icon_00135 => 'Stoffdickesensor';

  @override
  String get icon_00136_2 => 'Nadelposition -\nOben/Unten';

  @override
  String get icon_00137 => 'Nadelposition -\nStichposition';

  @override
  String get icon_00138 => 'Ober- und Unter-\nfadensensor';

  @override
  String get icon_00140 => 'Doppeltransport\nTransporteinstellung';

  @override
  String get icon_00141 => 'Multifunktionsfußpedal';

  @override
  String get icon_00142 => 'Fersenbetätigung';

  @override
  String get icon_00143 => 'Seitenpedal';

  @override
  String get icon_00144_a => 'Nadelposition\nOben/Unten';

  @override
  String get icon_00145 => 'Fadenab-\nschneiden';

  @override
  String get icon_00146 => 'Einzelstich';

  @override
  String get icon_00147 => 'Rückwärtsstich';

  @override
  String get icon_00243 => 'Nähfuß\nAuf/Ab';

  @override
  String get icon_00244 => 'Keine Einstellung';

  @override
  String get icon_00249 => 'Verstärkungsstich';

  @override
  String get icon_00148 => 'Schlitzlänge';

  @override
  String get icon_00148_zz => '%%%icon%%%';

  @override
  String get icon_00148_zz_s => '%%%icon%%%';

  @override
  String get icon_00150 => 'Priorität des Vernähens';

  @override
  String get icon_00152_1 => 'Stickrahmenanzeige';

  @override
  String get icon_00155_1 => 'Miniaturbildgröße';

  @override
  String get icon_00157 => 'Stickerei\nHintergrundfarbe';

  @override
  String get icon_00159 => 'Miniaturansicht\nHintergrundfarbe';

  @override
  String get icon_00163_a => 'Hintergrundbildanzeige';

  @override
  String get icon_00163 => 'Hintergrund-\nbild';

  @override
  String get icon_00164 => 'Scan-Bild';

  @override
  String get icon_00165 => 'Standard';

  @override
  String get icon_00166 => 'Fein';

  @override
  String get icon_00167 => 'Scan-Qualität';

  @override
  String get icon_00168 => 'Garnrollenständer-LED';

  @override
  String get icon_00178 => 'Abstand\nStickerei/Heftnaht';

  @override
  String get icon_00180 => 'Abstand\nStickerei/Applikation';

  @override
  String get icon_00182_1 => 'Max.\nStickgeschwindigkeit';

  @override
  String get icon_00183_1 => 'Stickfadenspannung';

  @override
  String get icon_00184_1 => 'Stickfußhöhe';

  @override
  String get icon_00185 => 'Rahmengröße';

  @override
  String get icon_00186 => 'Raster';

  @override
  String get icon_00187 => 'Ändern';

  @override
  String get icon_00188 => 'Löschen';

  @override
  String get icon_00191 => 'Farbe';

  @override
  String get icon_00193 => 'Uhranzeige';

  @override
  String get icon_00194 => 'AM';

  @override
  String get icon_00195 => 'PM';

  @override
  String get icon_00196 => '24h';

  @override
  String get icon_clock_msg1 => 'Stellen Sie das richtige Datum für die Netzwerkverbindung ein.';

  @override
  String get icon_00197 => 'Kalibrierung der\nSensorfunktion';

  @override
  String get icon_00199 => 'Einstellung der\nFührungslinie';

  @override
  String get icon_00200 => 'Helligkeit der Laser-Führungslinie';

  @override
  String get icon_00201_1 => 'Einstellung des\nStickfußes mit LED-Zeiger';

  @override
  String get icon_00202_p => 'Helligkeit';

  @override
  String get icon_00206_1 => 'Zertifizierung';

  @override
  String get icon_00207_a => 'Kit';

  @override
  String get icon_00208 => 'Start';

  @override
  String get icon_00209 => 'Stopp';

  @override
  String get icon_00211 => 'Servicezähler';

  @override
  String get icon_00212 => 'SCS';

  @override
  String get icon_00214 => 'Gesamtzähler';

  @override
  String get icon_00218 => 'No.';

  @override
  String get icon_00220 => 'Version';

  @override
  String get icon_00222 => 'YYYY';

  @override
  String get icon_00223 => 'MM';

  @override
  String get icon_00224 => 'DD';

  @override
  String get icon_00225 => 'mm';

  @override
  String get icon_00226 => '\"';

  @override
  String get icon_on => 'ON';

  @override
  String get icon_off => 'OFF';

  @override
  String get icon_00229 => 'KB';

  @override
  String get icon_00230 => 'bPocket';

  @override
  String get icon_00231 => '1';

  @override
  String get icon_00232 => '2';

  @override
  String get icon_00233 => '3';

  @override
  String get icon_00234 => '4';

  @override
  String get icon_00235 => '5';

  @override
  String get icon_00236 => '6';

  @override
  String get icon_00237 => '7';

  @override
  String get icon_00238 => '8';

  @override
  String get icon_00239 => '9';

  @override
  String get icon_00240 => '0';

  @override
  String get icon_00241 => 'C';

  @override
  String get icon_00242 => '%';

  @override
  String get icon_00245 => 'Automatisches Anheben/\nAbsenken des Nähfußes';

  @override
  String get icon_00246 => 'Absenken';

  @override
  String get icon_00247 => 'Anheben';

  @override
  String get icon_00248_zz => '%%%icon%%%';

  @override
  String get icon_00248_zz_s => '%%%icon%%%';

  @override
  String get icon_00248 => 'Drücken zum\nAbschneiden';

  @override
  String get icon_00251 => 'Projektor';

  @override
  String get icon_00253 => 'Hintergrundfarbe';

  @override
  String get icon_00254 => 'Nähen:\nMusterumriss';

  @override
  String get icon_00255 => 'Sticken:\nZeigerfarbe';

  @override
  String get icon_pointershape => 'Zeigerform';

  @override
  String get icon_00256 => 'Kamera';

  @override
  String get icon_00257 => 'Nadelkalibrierung für\nKamera/Projektor';

  @override
  String get icon_recog_ok => 'OK';

  @override
  String get icon_recog_ng => 'NG';

  @override
  String get icon_00258 => 'Nadelstopp-Position für\nSticken';

  @override
  String get icon_00259 => 'Einheit';

  @override
  String get icon_00260 => 'Garnfarbe';

  @override
  String get icon_00261 => 'Garnmarke';

  @override
  String get icon_00264 => 'Farbname';

  @override
  String get icon_00265 => '# 123';

  @override
  String get icon_00266 => 'Zeit';

  @override
  String get icon_00268 => 'Original';

  @override
  String get icon_00269 => 'Embroidery';

  @override
  String get icon_00269_t => 'Embroidery';

  @override
  String get icon_00270 => 'Country';

  @override
  String get icon_00270_t => 'Country';

  @override
  String get icon_00271 => 'Madeira\nPoly';

  @override
  String get icon_00272 => 'Madeira\nRayon';

  @override
  String get icon_00273 => 'Sulky';

  @override
  String get icon_00274 => 'Robison-Anton';

  @override
  String get icon_00275 => 'Robison-Anton\nPoly';

  @override
  String get icon_00276 => 'Robison-Anton\nRayon';

  @override
  String get icon_00277 => 'Isacord';

  @override
  String get icon_00278 => 'Gütermann';

  @override
  String get icon_00279 => 'Simplicity Pro';

  @override
  String get icon_00279_p => 'Pacesetter Pro';

  @override
  String get icon_00280 => 'Floriani';

  @override
  String get icon_00281 => 'Iris';

  @override
  String get icon_00282 => 'Aurifil';

  @override
  String get icon_00283 => 'WonderFil ';

  @override
  String get icon_00284 => 'Polyfast';

  @override
  String get icon_00290 => 'Wenn Sie das Upgrade-Kit gekauft haben\nund Ihre Nähmaschine zertifizieren möchten,\ndrücken Sie [ZERTIFIZIERUNG].';

  @override
  String get icon_00291 => 'KIT I';

  @override
  String get icon_00292 => 'KIT II';

  @override
  String get icon_00293 => 'KIT III';

  @override
  String get icon_00294 => 'KIT IV';

  @override
  String get icon_00295 => 'KIT V';

  @override
  String get icon_00296 => 'KIT VI';

  @override
  String get icon_00297 => 'KIT VII';

  @override
  String get icon_00298 => 'KIT VIII';

  @override
  String get icon_00299 => 'KIT IX';

  @override
  String get icon_00300 => 'KIT X';

  @override
  String get icon_00643_s => 'Keines';

  @override
  String get icon_00301 => 'Betriebsanleitung';

  @override
  String get icon_00302 => 'Nähanleitung';

  @override
  String get icon_00303 => 'Mustererklärung';

  @override
  String get icon_manuals => 'Bedienungsanleitungen';

  @override
  String get icon_operariong_b => 'Bedienungsanleitung';

  @override
  String get icon_operariong_t => 'Gebrauchsanweisung mit Kurzanleitung (English)';

  @override
  String get icon_pdf => 'PDF-Handbuch';

  @override
  String get icon_supportsite => 'Support-Seite';

  @override
  String get icon_pdf_eula => 'Endbenutzer-Lizenzvereinbarung\n(EULA)';

  @override
  String get icon_pdf_sewing => 'Nähen';

  @override
  String get icon_pdf_emb => 'Sticken';

  @override
  String get icon_pdf_sewing_ef => 'Nähen (English)';

  @override
  String get icon_pdf_emb_ef => 'Sticken (English)';

  @override
  String get icon_pdf_sewing_t => 'Nähen (English)';

  @override
  String get icon_pdf_emb_t => 'Sticken (English)';

  @override
  String get icon_f_omadendum => 'Ergänzung';

  @override
  String get icon_f_omadendum_ef => 'Ergänzung (English)';

  @override
  String get icon_f_omadendum_l => 'Ergänzung\nzur Bedienungsanleitung';

  @override
  String get icon_f_om_kit1 => 'KIT I';

  @override
  String get icon_f_om_kit2 => 'KIT II';

  @override
  String get icon_f_om_kit3 => 'KIT III';

  @override
  String get icon_f_om_kit1_l => 'Bedienungsanleitung\nKIT I';

  @override
  String get icon_f_omadendum_t => 'Ergänzung (English)';

  @override
  String get icon_f_om_kit1_t => 'KIT I (English)';

  @override
  String get icon_f_om_kit2_t => 'KIT II (English)';

  @override
  String get icon_f_om_kit3_t => 'KIT III (English)';

  @override
  String get icon_t_pdf_iivo_url_b => 'Bedienungsanleitungen, die Sie auf Ihrem Mobilgerät oder PC lesen können, finden Sie unter\nhttps://s.brother/fmraa.';

  @override
  String get icon_t_pdf_iivo_url_t => 'Eine Bedienungsanleitung, die Sie auf Ihrem Mobilgerät oder PC lesen können, finden Sie unter\nhttps://babylock.com/radiance-instruction-and-reference-guide.';

  @override
  String get icon_t_video_iivo_url_b => 'Besuchen Sie uns auf\n https://s.brother/fvraa\ndamit Sie sich die Lernvideos zu diesem Modell ansehen können.';

  @override
  String get icon_t_video_iivo_url_t => 'Besuchen Sie uns auf\n https://babylock.com/radiance-training\ndamit Sie sich die Lernvideos zu diesem Modell ansehen können.';

  @override
  String get icon_pdf_url_qr_t => 'www.babylock.com';

  @override
  String get icon_nettool => 'Netzwerk-Diagnosetool';

  @override
  String get icon_iagree => 'Ich stimme zu';

  @override
  String get icon_terms_cancel => 'Abbrechen';

  @override
  String get icon_confirm => 'Bestätigen';

  @override
  String get icon_00304 => 'Hauptmaschinenteile';

  @override
  String get icon_00305 => 'Haupttasten';

  @override
  String get icon_00306 => 'Grundbetrieb';

  @override
  String get icon_00307 => 'Sticken Grundbetrieb';

  @override
  String get icon_00308 => 'Fehlersuche';

  @override
  String get icon_00309 => 'Wartung';

  @override
  String get icon_00310 => 'Der Faden hat sich auf der\nStoffunterseite verwickelt';

  @override
  String get icon_00311 => 'Nadeleinfädeln nicht\nmöglich';

  @override
  String get icon_00312 => 'Nadeleinfädler kann nicht\nverwendet werden';

  @override
  String get icon_00313 => 'Falsche Fadenspannung';

  @override
  String get icon_00314 => 'Oberfaden reißt';

  @override
  String get icon_00315 => 'Unterfaden reißt';

  @override
  String get icon_00316 => 'Übersprungene Stiche';

  @override
  String get icon_00317 => 'Nadel bricht ab';

  @override
  String get icon_00318 => 'Maschine läuft nicht an';

  @override
  String get icon_00320 => 'Buchstabenmuster\ngelingen nicht';

  @override
  String get icon_00321 => 'Stoff wird nicht\nkorrekt transportiert';

  @override
  String get icon_00322 => 'Stoff wirft Falten';

  @override
  String get icon_00323 => 'Die Maschine ist laut';

  @override
  String get icon_00325 => 'Das Stickmuster gelingt nicht';

  @override
  String get icon_00326 => 'Stickeinheit arbeitet\nnicht';

  @override
  String get icon_00331 => 'Verriegeln';

  @override
  String get icon_00332 => 'Blindstich';

  @override
  String get icon_00333 => 'Knopfloch';

  @override
  String get icon_00334 => 'Knopfannähen';

  @override
  String get icon_00335 => 'Abnäher';

  @override
  String get icon_00336 => 'Kappnaht';

  @override
  String get icon_00337 => 'Kräuseln';

  @override
  String get icon_00338 => 'Überwendlings-stich';

  @override
  String get icon_00339 => 'Biesennähen';

  @override
  String get icon_00340 => 'Bogennaht';

  @override
  String get icon_00341 => 'Geradstich';

  @override
  String get icon_00342 => 'Reißverschluss einsetzen';

  @override
  String get icon_00343 => 'Zusammenfügen';

  @override
  String get icon_00344 => 'Freihand-Quilten';

  @override
  String get icon_00345 => 'Quilten';

  @override
  String get icon_00346 => 'Echo-Quilten';

  @override
  String get icon_00347 => 'Applizieren 1';

  @override
  String get icon_00348 => 'Applizieren 2';

  @override
  String get icon_search => 'Suche';

  @override
  String get icon_00353 => 'Einfädeln des Oberfadens';

  @override
  String get icon_00354 => 'Aufspulen des Unterfadens';

  @override
  String get icon_00355 => 'Auswechseln der Nadel';

  @override
  String get icon_00356 => 'Auswechseln des Nähfußes';

  @override
  String get icon_00357 => 'Einsetzen der Spule';

  @override
  String get icon_00358 => 'Nähfunktion';

  @override
  String get icon_00359 => 'Automatisches Fadentrimmen';

  @override
  String get icon_00360 => 'Verwendung des Spezialschraubendrehers';

  @override
  String get icon_00361 => 'Verwendung der Drehfunktion';

  @override
  String get icon_00362 => 'Einstellen der Stichbreite und -länge';

  @override
  String get icon_00363 => 'Verwendung des Mehrzweck-Schraubendrehers';

  @override
  String get icon_00364 => 'Automatische Farbensortierung bei der Randfunktion (Automatischer Nähfußdruck)';

  @override
  String get icon_00365 => 'Verwendung von Mein Stichdesign';

  @override
  String get icon_00366 => 'Verwendung der Kantennähfunktion';

  @override
  String get icon_00367 => 'Kreieren von Bobbin Work (Nähen)';

  @override
  String get icon_00368 => 'Kreieren von Bobbin Work (Sticken)';

  @override
  String get icon_00369 => 'Vorbereitungen für Bobbin Work';

  @override
  String get icon_00370 => 'Vorbereitungen für umgekehrte Bobbin Work';

  @override
  String get icon_00371 => 'Kreieren von umgekehrter Bobbin Work (Nähen)';

  @override
  String get icon_00372 => 'Verwendung der eingebauten Kamera im Nähmodus';

  @override
  String get icon_00373 => 'Einstellen des Nadeleinstichpunktes mit der Hilfslinienmarkierung im Einstellungsbildschirm';

  @override
  String get icon_00374 => 'Einstellen der Helligkeit der Hilfslinienmarkierung im Einstellungsbildschirm';

  @override
  String get icon_00375 => 'Einstellung der Fadenspannung';

  @override
  String get icon_00376 => 'Anbringen von Aufbügelvlies';

  @override
  String get icon_00377 => 'Einspannen des Stoffes in den Stickrahmen';

  @override
  String get icon_00378 => 'Anbringen/Abnehmen des Stickrahmens';

  @override
  String get icon_00379 => 'Anbringen/Abnehmen der Stickeinheit/des Zubehörfachs';

  @override
  String get icon_00380 => 'Anbringen/Abnehmen des Nähfußhalters';

  @override
  String get icon_00381 => 'Stickfunktion';

  @override
  String get icon_00382 => 'Verwendung der Funktion „Drucken und Sticken“';

  @override
  String get icon_00383 => 'Verwendung der Funktion „Color Shuffling (Zufällige Farbkombinationen)“';

  @override
  String get icon_00384 => 'Verwendung von „Mein Design Center“';

  @override
  String get icon_00385 => 'Scannen von Strichzeichnungen';

  @override
  String get icon_00386 => 'Scannen von Abbildungen';

  @override
  String get icon_00387 => 'Verwendung des Scanrahmens';

  @override
  String get icon_00388 => 'Anzeige des Stoffes im LCD (Scannen mit der eingebauten Kamera)';

  @override
  String get icon_00389 => 'Ausrichten der Stickposition mit dem Stickpositions-Aufkleber';

  @override
  String get icon_00390 => 'Verbinden von Mustern mit Hilfe der eingebauten Kamera';

  @override
  String get icon_00391 => 'Ausrichten der Stickposition mit Hilfe der eingebauten Kamera';

  @override
  String get icon_00392 => '';

  @override
  String get icon_00393 => 'Einstellungen';

  @override
  String get icon_00394 => 'Einstellung der Kamera-Nadelposition';

  @override
  String get icon_00395 => 'Aktualisieren der Maschinensoftware';

  @override
  String get icon_00396 => 'Einstellen der Hilfslinienmarkierung im Einstellungsbildschirm';

  @override
  String get icon_00397 => 'Einstellen von Uhrzeit/Datum';

  @override
  String get icon_00398 => 'Automatische Verstärkungsstiche';

  @override
  String get icon_00399 => 'Weitere';

  @override
  String get icon_00400 => 'Anzeigen/Speichern von Videos';

  @override
  String get icon_00401 => 'Sensorstift';

  @override
  String get icon_00402 => 'Anschließen des Sensorstiftes';

  @override
  String get icon_00403 => 'Kalibrieren des Sensorstiftes';

  @override
  String get icon_00404 => 'Festlegen der Hilfslinienmarkierungsposition mit dem Sensorstift';

  @override
  String get icon_00405 => 'Festlegen des Nadeleinstichpunktes mit dem Sensorstift';

  @override
  String get icon_00406 => 'Festlegen der Stichbreite/-position mit dem Sensorstift';

  @override
  String get icon_00407 => 'Festlegen des Nähendpunktes mit dem Sensorstift';

  @override
  String get icon_00408 => 'Festlegen der Stickposition mit dem Sensorstift';

  @override
  String get icon_00409 => 'Zubehör';

  @override
  String get icon_00410 => 'Verwenden des Kniehebels';

  @override
  String get icon_00411 => 'Verwendung des Multifunktions-Schraubendrehers';

  @override
  String get icon_00412 => 'Verwendung des Mehrzweck-Schraubendrehers';

  @override
  String get icon_00416 => 'Installieren des Multifunktionsfußpedals';

  @override
  String get icon_00417 => 'Zuordnen von Funktionen für das Multifunktionsfußpedal';

  @override
  String get icon_00418 => 'Anbringen/Abnehmen des Stickfußes mit LED-Zeiger';

  @override
  String get icon_00419 => 'Einstellen des Stickfußes mit LED-Zeiger';

  @override
  String get icon_00420 => 'Kreieren von punktierten Stickmustern mit Hilfe der eingebauten Kamera';

  @override
  String get icon_00421 => 'Anbringen von Nähfüßen mit dem mitgelieferten Adapter';

  @override
  String get icon_00422 => 'Verwendung des Zubehörkoffers';

  @override
  String get icon_00423 => 'Wartung (Reinigen der Greiferbahn)';

  @override
  String get icon_00500 => 'Mein Design Center';

  @override
  String get icon_00500_2 => 'Mein Design\nCenter';

  @override
  String get icon_iqdesigner => 'IQ Designer';

  @override
  String get icon_00501 => 'Linien-Scan';

  @override
  String get icon_00503_zz => '%%%icon%%%';

  @override
  String get icon_00503_zz_s => '%%%icon%%%';

  @override
  String get icon_00505 => 'Abbildungs-Scan';

  @override
  String get icon_imagescan => 'Bildscan';

  @override
  String get icon_linedesign => 'Liniendesign';

  @override
  String get icon_illustrationdesign => 'Illustrationsdesign';

  @override
  String get icon_00509_zz => '%%%icon%%%';

  @override
  String get icon_00510 => 'Erkennen';

  @override
  String get icon_00511_1 => 'Vorschau';

  @override
  String get icon_00511_2 => 'Vorschau';

  @override
  String get icon_showpreview => 'Vorschau anzeigen';

  @override
  String get icon_00512 => 'Wieder-\nholung';

  @override
  String get icon_00514 => 'Objektgröße ignorieren';

  @override
  String get icon_00516 => 'Grauton-Erkennungsempfindlichkeit';

  @override
  String get icon_00503 => 'Linie';

  @override
  String get icon_00518 => 'Radierer';

  @override
  String get icon_00520 => 'Originalansicht';

  @override
  String get icon_00521 => 'Ergebnisansicht';

  @override
  String get icon_00522 => 'Ergebnisansicht';

  @override
  String get icon_00523 => 'Max. Anzahl\nFarben';

  @override
  String get icon_00525 => 'Hintergrund\nentfernen';

  @override
  String get icon_00526 => 'Erkennen';

  @override
  String get icon_00528 => 'Stickeinstellungen';

  @override
  String get icon_00529 => 'Linieneigenschaften';

  @override
  String get icon_00530 => 'Bereichseigenschaften';

  @override
  String get icon_00533 => 'Größe';

  @override
  String get icon_00537 => 'Zickzack-Breite';

  @override
  String get icon_00538 => 'Dichte';

  @override
  String get icon_00539 => 'Laufweite';

  @override
  String get icon_00540 => 'Füllstich';

  @override
  String get icon_00541 => 'Richtung';

  @override
  String get icon_00544 => 'Zug-\nKompensation';

  @override
  String get icon_00545 => 'Unternähen';

  @override
  String get icon_00547 => 'Abstand';

  @override
  String get icon_00548_1 => 'Manuell';

  @override
  String get icon_00548_2 => 'Manuell';

  @override
  String get icon_00549_1 => 'Auto';

  @override
  String get icon_00549_2 => 'Auto';

  @override
  String get icon_00550 => 'Zu Stich';

  @override
  String get icon_00551 => 'Rahmen des Bildes';

  @override
  String get icon_00552 => 'Farben mischen';

  @override
  String get icon_00553 => 'Weiter';

  @override
  String get icon_00554 => 'Abstand';

  @override
  String get icon_00555 => 'Umrisse werden gespeichert';

  @override
  String get icon_00556 => 'Geschlossene Formen';

  @override
  String get icon_00557 => 'Offene Formen';

  @override
  String get icon_00558 => 'Gespeicherte Umrisse';

  @override
  String get icon_00559 => 'Rahmenstickbereiche';

  @override
  String get icon_00562 => 'Umriss';

  @override
  String get icon_00564 => 'Dicke';

  @override
  String get icon_00565 => 'Zufällige\nVerzerrung';

  @override
  String get icon_00566 => 'Positionsversatz';

  @override
  String get icon_inside => 'Innen';

  @override
  String get icon_outside => 'Außen';

  @override
  String get icon_00567 => 'Spiegeln';

  @override
  String get icon_00568 => 'Stichbreite';

  @override
  String get icon_00569 => 'Aktuell';

  @override
  String get icon_00570 => 'Neu';

  @override
  String get icon_frame_297_465_mm => '297 × 465 mm';

  @override
  String get icon_frame_297_465_inch => '11-5/8\"× 18-1/4\"';

  @override
  String get icon_frame_272_408_mm => '272 × 408 mm';

  @override
  String get icon_frame_272_408_inch => '10-5/8\"× 16\"';

  @override
  String get icon_frame_254_254_mm => '254 × 254 mm';

  @override
  String get icon_frame_254_254_inch => '10\"× 10\"';

  @override
  String get icon_frame_240_360_mm => '240 × 360 mm';

  @override
  String get icon_frame_240_360_inch => '9-1/2\"× 14\"';

  @override
  String get icon_frame_180_360_mm => '180 × 360 mm';

  @override
  String get icon_frame_180_360_inch => ' 7\" × 14\"';

  @override
  String get icon_frame_180_300_mm => '180 × 300 mm';

  @override
  String get icon_frame_180_300_inch => ' 7\" × 12\"';

  @override
  String get icon_frame_200_300_mm => '200 × 300 mm';

  @override
  String get icon_frame_200_300_inch => '8\"×12\"';

  @override
  String get icon_frame_100_300_mm => '100 × 300 mm';

  @override
  String get icon_frame_100_300_inch => '4\"× 12\"';

  @override
  String get icon_frame_160_260_mm => '160 × 260 mm';

  @override
  String get icon_frame_160_260_inch => '6-1/4\"× 10-1/4\"';

  @override
  String get icon_frame_240_240_mm => '240 × 240 mm';

  @override
  String get icon_frame_240_240_inch => '9-1/2\"× 9-1/2\"';

  @override
  String get icon_frame_200_200_mm => '200 × 200 mm';

  @override
  String get icon_frame_200_200_inch => '8\"× 8\"';

  @override
  String get icon_frame_130_180_mm => '130 × 180 mm';

  @override
  String get icon_frame_130_180_inch => '5\"× 7\"';

  @override
  String get icon_frame_100_180_mm => '100 × 180 mm';

  @override
  String get icon_frame_100_180_inch => '4\"× 7\"';

  @override
  String get icon_frame_150_150_mm => '150 × 150 mm';

  @override
  String get icon_frame_150_150_inch => '6\"× 6\"';

  @override
  String get icon_frame_100_100_mm => '100 × 100 mm';

  @override
  String get icon_frame_100_100_inch => '4\"× 4\"';

  @override
  String get icon_frame_60_20_mm => '60 × 20 mm';

  @override
  String get icon_frame_60_20_inch => '2-3/8\"× 3/4\"';

  @override
  String get icon_zoom_50 => '50';

  @override
  String get icon_zoom_100 => '100';

  @override
  String get icon_zoom_125 => '125';

  @override
  String get icon_zoom_150 => '150';

  @override
  String get icon_zoom_200 => '200';

  @override
  String get icon_zoom_400 => '400';

  @override
  String get icon_zoom_800 => '800';

  @override
  String get icon_zoom_120 => '120';

  @override
  String get icon_zoom_240 => '240';

  @override
  String get icon_zoom_480 => '480';

  @override
  String get icon_zoom_960 => '960';

  @override
  String get icon_00600 => 'Wireless LAN aktivieren';

  @override
  String get icon_00600_1 => 'WLAN aktiv';

  @override
  String get icon_00601 => 'SSID';

  @override
  String get icon_00602 => 'SSID auswählen...';

  @override
  String get icon_00603 => 'Maschinenbezeichnung';

  @override
  String get icon_00604 => 'WPS (Push-Verfahren)';

  @override
  String get icon_00605 => 'WPS (Pin-Verfahren)';

  @override
  String get icon_00606 => 'Sonstiges';

  @override
  String get icon_00608 => 'Wireless LAN-Status';

  @override
  String get icon_00608_1 => 'WLAN-Status';

  @override
  String get icon_00609 => 'Gespei-\ncherte SSID';

  @override
  String get icon_00609_1 => 'Gespeicherte SSID';

  @override
  String get icon_00610 => 'Neue SSID';

  @override
  String get icon_wlan_title => 'Wireless-LAN';

  @override
  String get icon_wlan_connection => 'Wireless-LAN-Verbindung';

  @override
  String get icon_wlan_networks => 'Wireless-LAN-Netzwerke';

  @override
  String get icon_wlan_enable => 'Wireless LAN aktivieren';

  @override
  String get icon_wlan_setinfo_01 => 'Um verfügbare Netzwerke anzuzeigen, aktivieren Sie Wireless LAN aktivieren.';

  @override
  String get icon_wlan_setinfo_02 => 'Suche nach Wireless-LAN-Netzwerken…';

  @override
  String get icon_wlan_setinfo_03 => 'Verbindung mit Wireless-LAN wird hergestellt...';

  @override
  String get icon_wlan_setinfo_05 => 'Wireless-LAN wird eingeschaltet...';

  @override
  String get icon_wlan_setinfo_06 => 'Wireless-LAN eingeschaltet';

  @override
  String get icon_wlan_setinfo_04 => 'Wireless-LAN wird ausgeschaltet...';

  @override
  String get icon_wlan_setinfo_07 => 'Hierdurch werden sämtliche Netzwerkeinstellungen zurückgesetzt, einschließlich:·Wireless LAN';

  @override
  String get icon_wlan_networkreset => 'Netzwerk-Reset';

  @override
  String get icon_wlan_limitedconnect => 'Es kann keine Verbindung zum Netzwerk hergestellt werden. Bitte überprüfen Sie die Uhreinstellungen.';

  @override
  String get icon_00630 => 'Netzwerk';

  @override
  String get icon_00631 => 'Wireless LAN-Setup-Assistent';

  @override
  String get icon_00631_1 => 'Setup-Assist.';

  @override
  String get icon_00632 => 'Detail';

  @override
  String get icon_00633 => 'Status';

  @override
  String get icon_00634 => 'Signal';

  @override
  String get icon_00635 => 'Komm.-Modus';

  @override
  String get icon_00636 => 'Aktiv (11b)';

  @override
  String get icon_00637 => 'Aktiv (11g)';

  @override
  String get icon_00638 => 'Aktiv (11n)';

  @override
  String get icon_00639 => 'Keine Verbindung';

  @override
  String get icon_00640 => 'Stark';

  @override
  String get icon_00641 => 'Mittel';

  @override
  String get icon_00642 => 'Schwach';

  @override
  String get icon_00643 => 'Keines';

  @override
  String get icon_00644 => 'Ad-hoc';

  @override
  String get icon_00645 => 'Infrastruktur';

  @override
  String get icon_00646 => 'TCP/IP';

  @override
  String get icon_00647 => 'MAC-Adresse';

  @override
  String get icon_00648 => 'Proxy-Einstell.';

  @override
  String get icon_00649 => 'Boot-Methode';

  @override
  String get icon_00650 => 'IP-Adresse';

  @override
  String get icon_00651 => 'Subnet-Mask';

  @override
  String get icon_00652 => 'Gateway';

  @override
  String get icon_00653 => 'Knotenname';

  @override
  String get icon_00654 => 'WINS-Konfig.';

  @override
  String get icon_00655 => 'WINS-Server';

  @override
  String get icon_00656 => 'DNS-Server';

  @override
  String get icon_00656_p => 'DNS-Server Primär';

  @override
  String get icon_00656_s => 'DNS-Server Sekundär';

  @override
  String get icon_00657 => 'APIPA';

  @override
  String get icon_00658 => 'Proxy-Verbindung';

  @override
  String get icon_00659 => 'Adresse';

  @override
  String get icon_00660 => 'Port';

  @override
  String get icon_00661 => 'Benutzername';

  @override
  String get icon_00662 => 'Kennwort';

  @override
  String get icon_00663 => 'Primär';

  @override
  String get icon_00664 => 'Sekundär';

  @override
  String get icon_00665 => 'Suche SSID...';

  @override
  String get icon_00666 => 'SSID des Access Points';

  @override
  String get icon_00667 => 'Netzwerkschlüssel';

  @override
  String get icon_00668 => 'Ja';

  @override
  String get icon_00669 => 'Nein';

  @override
  String get icon_00670 => 'Authent. wählen';

  @override
  String get icon_00671 => 'Open System';

  @override
  String get icon_00672 => 'Shared Key';

  @override
  String get icon_00673 => 'WPA/WPA2-PSK';

  @override
  String get icon_00674 => 'Verschlüsselung';

  @override
  String get icon_00674_a => 'Verschlüsselung (Open System)';

  @override
  String get icon_00674_c => 'Verschlüsselung (WPA/WPA2-PSK)';

  @override
  String get icon_00675 => 'WEP';

  @override
  String get icon_00676 => 'AES';

  @override
  String get icon_00677 => 'TKIP';

  @override
  String get icon_00678 => 'Deaktiviert';

  @override
  String get icon_00679 => 'Fest';

  @override
  String get icon_00680 => 'Auto';

  @override
  String get icon_00681 => 'WPA';

  @override
  String get icon_00682 => 'Datum';

  @override
  String get icon_cert_key => 'Normale Zertifizierung';

  @override
  String get icon_cert_web => 'Online-\nMaschinenzertifizierung';

  @override
  String get icon_status_t => 'Status';

  @override
  String get icon_status_a1 => 'Nicht überprüft';

  @override
  String get icon_status_a2 => 'Überprüfen';

  @override
  String get icon_status_a3 => 'Überprüft: Bereits aktualisiert';

  @override
  String get icon_status_a4 => 'Neues Update auf Server';

  @override
  String get icon_status_b1 => 'Nicht heruntergeladen';

  @override
  String get icon_status_b2 => 'Herunterladen';

  @override
  String get icon_status_b3 => 'Heruntergeladen';

  @override
  String get icon_cancel_downloading => 'Download abbrechen';

  @override
  String get icon_pause_downloading2 => 'Download anhalten\nDrücken Sie die Taste „Fortsetzen“, um mit dem Download fortzufahren';

  @override
  String get icon_status_c1 => 'Das neue Update wurde noch nicht installiert.';

  @override
  String get icon_status_c2 => 'Das neue Update wurde installiert.';

  @override
  String get icon_app_dl_moniter => 'Überwachungs-App herunterladen';

  @override
  String get icon_shape => 'Form';

  @override
  String get icon_favorite => 'Favoriten';

  @override
  String get icon_sash_4section => '4 Abschnitte (2 x 2)';

  @override
  String get icon_sash_1direction => 'Eine Richtung';

  @override
  String get icon_sash_1dtotal => 'Teile insgesamt';

  @override
  String get icon_offset => 'Versatz';

  @override
  String get icon_startpoint => 'Anfangspunkt';

  @override
  String get icon_endpoint => 'Endpunkt';

  @override
  String get icon_embfootdwn => 'Stickfuß -\nAuto nach unten';

  @override
  String get icon_frame_272_272_mm => '272 × 272 mm';

  @override
  String get icon_frame_272_272_inch => '10-5/8\"× 10-5/8\"';

  @override
  String get icon_appguide_w => 'App-Anleitung';

  @override
  String get icon_appguide => 'App-\nAnleitung';

  @override
  String get icon_mobileapp => 'Mobile App';

  @override
  String get icon_app => 'App';

  @override
  String get icon_emb1 => 'Sticken 1';

  @override
  String get icon_emb2 => 'Sticken 2';

  @override
  String get icon_00185_2 => 'Rahmengröße';

  @override
  String get icon_type => 'Art';

  @override
  String get icon_typea => 'Typ A';

  @override
  String get icon_typeb => 'Typ B';

  @override
  String get icon_typec => 'Typ C';

  @override
  String get icon_sash_typesplit => 'Art der Teilung';

  @override
  String get icon_mystitchmonitor => 'My Stitch Monitor';

  @override
  String get icon_mydesignsnap => 'My Design Snap';

  @override
  String get icon_mystitchmonitor_t => 'IQ Intuition Monitoring';

  @override
  String get icon_mydesignsnap_t => 'IQ Intuition Positioning';

  @override
  String get icon_actcode => 'Aktivierungscode';

  @override
  String get icon_machineno => 'Maschinennummer (No.)';

  @override
  String get icon_autodl => 'Automatisch herunterladen';

  @override
  String get icon_updatemanu => 'Manuell aktualisieren';

  @override
  String get icon_dl_updateprogram => 'Aktualisierungsprogramm herunterladen';

  @override
  String get icon_dl_updateprogram_2 => 'Aktualisierungsprogramm\nherunterladen';

  @override
  String get icon_chk_update => 'Auf Aktualisierungen überprüfen';

  @override
  String get icon_pause => 'Pause';

  @override
  String get icon_resume => 'Fortsetzen';

  @override
  String get icon_cert_method => 'Zertifizierungsmethode';

  @override
  String get icon_latestver => 'Neueste Version';

  @override
  String get icon_latestveravail => 'Neueste Version verfügbar';

  @override
  String get icon_device_ios => 'Für iOS-Geräte';

  @override
  String get icon_device_android => 'Für Android™-\nGeräte';

  @override
  String get icon_f_ios => 'Für iOS';

  @override
  String get icon_f_android => 'Für Android™';

  @override
  String get icon_cws_myconnection => 'CanvasWorkspace\n (Meine Verbindung)';

  @override
  String get icon_step1 => 'SCHRITT1:';

  @override
  String get icon_step2 => 'SCHRITT2:';

  @override
  String get icon_step3 => 'SCHRITT3:';

  @override
  String get icon_step4 => 'SCHRITT4:';

  @override
  String get icon_step5 => 'SCHRITT5:';

  @override
  String get icon_register => 'Registrieren';

  @override
  String get icon_loginid => 'Login-ID:';

  @override
  String get icon_id => 'ID:';

  @override
  String get icon_appq1 => 'Aufnäher\n (Normal)';

  @override
  String get icon_appq2 => 'Aufnäher für \nausgewählte Farben';

  @override
  String get icon_original_img => 'Originalbild';

  @override
  String get icon_appq_stitch_1 => 'Zickzackstich';

  @override
  String get icon_appq_stitch_2 => 'Satinstich';

  @override
  String get icon_appq_stitch_3 => 'Geradstich';

  @override
  String get icon_stamp_web => 'Schnittumrisse';

  @override
  String get icon_cws_rgs_title => 'Rufen Sie den PIN-Code ab, um Ihre Maschine zu registrieren.';

  @override
  String get icon_cws_rgs_s1 => 'Melden Sie sich bei CanvasWorkspace an.\nhttp://CanvasWorkspace.Brother.com';

  @override
  String get icon_cws_rgs_s2 => 'Tippen Sie auf [Kontoeinstellungen].';

  @override
  String get icon_pincode => 'PIN-Code';

  @override
  String get icon_kitsnc => 'ScanNCut (Meine Verbindung)';

  @override
  String get icon_snc1 => 'ScanNCut';

  @override
  String get icon_f_om_kitsnc => 'ScanNCut (Meine Verbindung)';

  @override
  String get icon_density_mm => 'Linie/mm';

  @override
  String get icon_density_inch => 'Linie/inch';

  @override
  String get icon_machineregist => 'Maschinenregistrierung';

  @override
  String get icon_snj_myconnection => 'Artspira / Meine Verbindung';

  @override
  String get icon_snj_rgs_title => 'Rufen Sie den PIN-Code ab, um Ihre Maschine zu registrieren.';

  @override
  String get icon_snj_rgs_s1_iivo1 => 'Melden Sie sich bei Artspira an.\nhttps://s.brother/snjumq4211';

  @override
  String get icon_snj_rgs_s2 => 'Tippen Sie auf der Stickmaschine auf [Maschineneinstellungen] und auf [Registrieren] und wählen Sie dann [Wireless-LAN-Modell] aus.';

  @override
  String get icon_snj_rgs_s3 => 'Geben Sie in Artspira die folgende Nummer ein und rufen Sie den PIN-Code ab.';

  @override
  String get icon_snj_rgs_pin => 'Geben Sie den PIN-Code auf dem nächsten Bildschirm ein.';

  @override
  String get icon_cws_rgs_s3 => 'Tippen Sie auf [Maschinenregistrierung] und wählen Sie [Eine neue Nähmaschine registrieren] aus.';

  @override
  String get icon_cws_rgs_s4 => 'Geben Sie auf dem Bildschirm die folgende Nummer ein und rufen Sie den PIN-Code ab.';

  @override
  String get icon_cws_rgs_pin => 'Geben Sie den PIN-Code auf dem nächsten Bildschirm ein.';

  @override
  String get icon_transfer => 'Übertragung';

  @override
  String get icon_app_selectcolor => 'Farben für Aufnäher auswählen';

  @override
  String get icon_texture => 'Gewebestruktur';

  @override
  String get icon_userthread => 'Anwendergarn';

  @override
  String get icon_senju => 'Artspira';

  @override
  String get icon_notnow => 'Später';

  @override
  String get icon_builtin => 'Integriertes\nMuster';

  @override
  String get icon_user => 'Eigenes';

  @override
  String get icon_clearall => 'Alles\nlöschen';

  @override
  String get icon_taperingtitle => 'Anschrägen';

  @override
  String get icon_tapering01 => 'Start';

  @override
  String get icon_tapering02 => 'Ende';

  @override
  String get icon_tapering03 => 'End-Stil';

  @override
  String get icon_tapering03_2 => 'End-Stil';

  @override
  String get icon_tapering04 => 'Startwinkel';

  @override
  String get icon_tapering05 => 'Endwinkel';

  @override
  String get icon_tapering06 => 'Muster wiederholen';

  @override
  String get icon_tapering06_s => 'Wiederholung';

  @override
  String get icon_times => 'Mal';

  @override
  String get icon_approx_s => 'ca.';

  @override
  String get icon_e2etitle => 'Edge-To-Edge Quilt';

  @override
  String get icon_e2e01 => 'Spiegeln';

  @override
  String get icon_e2e01_2 => 'Spiegeln';

  @override
  String get icon_e2e02 => 'Reihe(n)';

  @override
  String get icon_e2e03 => 'Teil(e)';

  @override
  String get icon_sr_title => 'Stichregler';

  @override
  String get icon_sr_mode_title => 'Modus';

  @override
  String get icon_sr_mode_00exp => 'Schritt 1 - Wählen Sie einen Modus aus.\nSchritt 2 - Wählen Sie einen Stich aus. \n  *Heftnaht wird automatisch in Modus 3 ausgewählt.\nSchritt 3 - Bitte beginnen Sie mit dem Nähen.';

  @override
  String get icon_sr_mode01exp => 'Diskontinuierlicher Modus\n\nWenn der Stoff nicht transportiert wird, wird die Nadel in der obersten Stellung angehalten. Nach einer Bewegung in der angegebenen Länge erfolgt der Nadeleinstich. Achten Sie darauf, Ihre Hand nicht unter die Nadel zu halten.';

  @override
  String get icon_sr_mode02exp => 'Kontinuierlicher Modus\n\nWenn der Stoff nicht transportiert wird, erfolgt der Nadeleinstich langsam an derselben Stelle, um einen Steppstich zu nähen, oder mit einer kürzeren Stichlänge als eingestellt, um z. B. die Ecken eines Musters zu nähen.';

  @override
  String get icon_sr_mode03exp => 'Heftstichmodus\n\nBei Heftstichen erfolgt der Nadeleinstich in größeren Abständen. Achten Sie darauf, Ihre Hand nicht unter die Nadel zu halten.';

  @override
  String get icon_sr_mode04exp => 'Freihandnähmodus\n\nMit der eingestellten Geschwindigkeit nähen';

  @override
  String get icon_sr_mem_mode01 => 'Diskontinuierlich';

  @override
  String get icon_sr_mem_mode02 => 'Kontinuierlich';

  @override
  String get icon_sr_modemem_03 => 'Heften';

  @override
  String get icon_sr_mem_mode04 => 'Freihand';

  @override
  String get icon_sr_sensingline => 'Linienerkennung';

  @override
  String get icon_sr_footheight => 'SR-Höhe';

  @override
  String get icon_unselect => 'Nicht auswählen';

  @override
  String get icon_filter => 'Filter';

  @override
  String get icon_filterapplied => 'Angewendeter Filter';

  @override
  String get icon_apply => 'Anwenden';

  @override
  String get icon_upperlimit => 'Höchstgrenze';

  @override
  String get icon_lowerlimit => 'Untere Grenze';

  @override
  String get icon_all => 'Alle';

  @override
  String get icon_bh_guide01 => 'Knopflochführung';

  @override
  String get icon_bh_guide02 => 'Anordnung';

  @override
  String get icon_bh_guide03 => 'Abstand';

  @override
  String get icon_bh_guide04 => 'Stoffkanten-\nführung';

  @override
  String get icon_bh_guide05 => 'Abstand von der Kante';

  @override
  String get icon_colorchanges => 'Farbänderungen';

  @override
  String get icon_voiceguidance_title => 'Sprachführung';

  @override
  String get icon_voicevolume => 'Lautstärke der Stimme';

  @override
  String get icon_voice_01eng_a => 'English-A';

  @override
  String get icon_voice_01eng_b => 'English-B';

  @override
  String get icon_voice_02deu_a => 'Deutsch-A';

  @override
  String get icon_voice_02deu_b => 'Deutsch-B';

  @override
  String get icon_voice_03fra_a => 'Français-A';

  @override
  String get icon_voice_03fra_b => 'Français-B';

  @override
  String get icon_voice_04ita_a => 'Italiano-A';

  @override
  String get icon_voice_04ita_b => 'Italiano-B';

  @override
  String get icon_voice_05nld_a => 'Nederlands-A';

  @override
  String get icon_voice_05nld_b => 'Nederlands-B';

  @override
  String get icon_voice_06esp_a => 'Español-A';

  @override
  String get icon_voice_06esp_b => 'Español-B';

  @override
  String get icon_voice_07jpn_a => '日本語-A';

  @override
  String get icon_voice_07jpn_b => '日本語-B';

  @override
  String get icon_embcate_photostitch => 'Picture Play\nStickerei ';

  @override
  String get icon_photos_title => 'Picture Play Stickfunktion';

  @override
  String get icon_photos_01 => 'Wählen Sie eine Bilddatei aus (JPG, BMP, PNG).';

  @override
  String get icon_photos_02 => 'Größenanpassung';

  @override
  String get icon_photos_03 => 'Hintergrund entfernen';

  @override
  String get icon_photos_04 => 'Rahmen des Bildes';

  @override
  String get icon_photos_05 => 'An Rahmen anpassen';

  @override
  String get icon_photos_06 => 'Auto (AI)';

  @override
  String get icon_photos_07 => 'Manuell';

  @override
  String get icon_photos_08 => 'Stil auswählen, der von der KI umgewandelt werden soll.';

  @override
  String get icon_photos_09 => 'Farbanpassung';

  @override
  String get icon_photos_10 => 'Kanteneffekte';

  @override
  String get icon_photos_11 => 'Helligkeit';

  @override
  String get icon_photos_12 => 'Kontrast';

  @override
  String get icon_photos_13 => 'Sättigung';

  @override
  String get icon_photos_14 => 'Eine Bilddatei importieren (JPG, BMP, PNG)';

  @override
  String get icon_photos_15 => 'Stickeinstellungen';

  @override
  String get icon_style0 => 'Original';

  @override
  String get icon_style1 => 'Stil 1';

  @override
  String get icon_style2 => 'Stil 2';

  @override
  String get icon_style3 => 'Stil 3';

  @override
  String get icon_style4 => 'Stil 4';

  @override
  String get icon_style5 => 'Stil 5';

  @override
  String get icon_style6 => 'Stil 6';

  @override
  String get icon_style7 => 'Stil 7';

  @override
  String get icon_style8 => 'Stil 8';

  @override
  String get icon_style9 => 'Stil 9';

  @override
  String get icon_style10 => 'Stil 10';

  @override
  String get icon_style1_name => 'Icons';

  @override
  String get icon_style2_name => 'Art déco';

  @override
  String get icon_style3_name => 'Bleistiftskizze';

  @override
  String get icon_style4_name => 'Ölpastell-Gemälde';

  @override
  String get icon_style5_name => 'Neonschild';

  @override
  String get icon_style6_name => 'Art Nouveau';

  @override
  String get icon_style7_name => 'Leuchtend und lebendig';

  @override
  String get icon_style8_name => 'Glasmalerei';

  @override
  String get icon_style9_name => 'Geo-Kunst';

  @override
  String get icon_style10_name => 'Cartoon-Grafik';

  @override
  String get icon_stylusedit => 'Projektorbearbeitung mit Stift';

  @override
  String get icon_projectorsettings => 'Projektoreinstellungen';

  @override
  String get icon_setting_srvolume => 'SR-Summerlautstärke';

  @override
  String get icon_embcate_bt_01 => 'Quilt';

  @override
  String get icon_embcate_bt_02 => 'Applikation';

  @override
  String get icon_embcate_bt_03 => 'Pflanzen';

  @override
  String get icon_embcate_bt_04 => 'Tiere';

  @override
  String get icon_embcate_bt_05 => 'Buchstabe';

  @override
  String get icon_embcate_bt_06 => 'Dekoration';

  @override
  String get icon_embcate_bt_07 => 'Jahreszeiten';

  @override
  String get icon_embcate_bt_08 => '3D-Spitze';

  @override
  String get icon_embcate_bt_09 => 'Häkelspitze';

  @override
  String get icon_embcate_bt_10 => 'ITH-Stickerei';

  @override
  String get icon_embcate_b_01 => 'Quilt 2';

  @override
  String get icon_embcate_b_02 => 'Quiltdesigns Anna Aldmon';

  @override
  String get icon_embcate_b_03 => 'Applikation 2';

  @override
  String get icon_embcate_b_04 => 'Pflanzen 2';

  @override
  String get icon_embcate_b_05 => 'Design „Rosen von Pierre-Joseph Redoute“';

  @override
  String get icon_embcate_b_06 => 'Zündt-Design';

  @override
  String get icon_embcate_b_07 => 'Zentangle';

  @override
  String get icon_embcate_b_08 => 'Tiere 2';

  @override
  String get icon_embcate_b_09 => 'Buchstabe 2';

  @override
  String get icon_embcate_b_10 => 'Sport';

  @override
  String get icon_embcate_b_11 => 'Nautik';

  @override
  String get icon_embcate_b_12 => 'Nahrung';

  @override
  String get icon_embcate_b_13 => 'Kinder';

  @override
  String get icon_embcate_b_14 => 'Dekoration 2';

  @override
  String get icon_embcate_b_15 => '3D-Spitze 2';

  @override
  String get icon_legalinfo => 'Rechtsinformation';

  @override
  String get icon_legal_opensource => 'Open Source Licensing Remarks\n(Hinweise zur Open-Source-Lizenzierung)';

  @override
  String get icon_legal_thirdpartysoft => 'Third-Party Software\n(Software von Drittanbietern)';

  @override
  String get icon_nousb => '－－－－－－';

  @override
  String get icon_randomfill => 'Zufällige Füllung';

  @override
  String get icon_selarea => 'Bereich auswählen';

  @override
  String get icon_maxnumber_patt => 'Mindestabstand';

  @override
  String get t_err01 => 'Die Sicherheits-Vorrichtung wurde aktiviert.\nIst der Faden verwickelt?\nIst die Nadel verbogen?';

  @override
  String get t_err02 => 'Oberfaden prüfen und neu einfädeln.';

  @override
  String get t_err02_emb => 'Oberfaden prüfen und neu einfädeln.\n\n* Berühren Sie im Stickbildschirm die Taste zum Verschieben des Rahmens, um den Rahmen in die mittlere Position zu bringen.';

  @override
  String get t_err03 => 'Heben Sie den Nähfußhebel an.';

  @override
  String get t_err04 => 'Senken Sie den Nähfußhebel ab.';

  @override
  String get t_err05 => 'Im Diskettenlaufwerk befindet sich keine Stickkarte.\nStecken Sie eine Stickkarte ein.';

  @override
  String get t_err06 => 'Diese Stickkarte ist nicht geeignet.\nNicht geeignete Karten sind Karten, die im Ausland gekauft wurden, Karten ohne Stickmuster etc.';

  @override
  String get t_err07 => 'Dieser Kombination können keine Muster mehr hinzugefügt werden.';

  @override
  String get t_err07_u => 'Mehr Stiche können nicht kombiniert werden.';

  @override
  String get t_err08 => 'Diese Taste ist nicht funktionsfähig, wenn die Stickeinheit nicht angeschlossen ist.\nSchalten Sie die Maschine aus und bringen Sie die Stickeinheit an.';

  @override
  String get t_err09 => 'Diese Taste ist nicht funktionsfähig, wenn die Stickeinheit angeschlossen ist.';

  @override
  String get t_err10 => 'Diese Taste ist nicht funktionsfähig, wenn die Stickeinheit angeschlossen ist.\nSchalten Sie die Maschine aus und nehmen Sie die Stickeinheit ab.';

  @override
  String get t_err11 => 'Das Fußpedal kann nicht zusammen mit der Stickeinheit benutzt werden.\nEntfernen Sie das Fußpedal.';

  @override
  String get t_err_corrupteddataturnoff => 'Die Daten können nicht erkannt werden. Die Daten sind möglicherweise fehlerhaft.\n\nSchalten Sie die Maschine aus und wieder ein.';

  @override
  String get t_err12 => 'Datenmenge ist zu groß für dieses Muster.';

  @override
  String get t_err13 => 'Diese Taste hat bei abgesenkter Nadel keine Funktion.\nHeben Sie die Nadel an und drücken Sie erneut die Taste.';

  @override
  String get t_err15 => 'Die Taste \"Start/Stopp\" hat bei angeschlossenem Fußpedal keine Funktion.\nEntfernen Sie das Fußpedal.';

  @override
  String get t_err16 => 'Das Muster muss vor dem Nähen erst editiert werden.';

  @override
  String get t_err16_e => 'Schließen Sie zunächst die Musterbearbeitung ab, bevor Sie mit dem Sticken beginnen.';

  @override
  String get t_err16_u => 'Schließen Sie zunächst die Stichdatenbearbeitung ab, bevor Sie mit dem Nähen beginnen.';

  @override
  String get t_err17 => 'Heben Sie den Knopflochhebel an.';

  @override
  String get t_err18 => 'Ziehen Sie den Knopflochhebel nach unten.';

  @override
  String get t_err19 => 'Die Konfiguration der Zeichen kann nicht geändert werden.';

  @override
  String get t_err22 => 'Wählen Sie ein Muster.';

  @override
  String get t_err22_u => 'Wählen Sie einen Stich aus.';

  @override
  String get t_err23 => 'Muster wird gespeichert…';

  @override
  String get t_err24 => 'Die Unterfadenspule ist fast leer.';

  @override
  String get t_err25 => 'Der Stickarm der Stickeinheit setzt sich gleich in Bewegung.\nHalten Sie Hände, Fremdkörper etc. vom Stickarm fern.';

  @override
  String get t_err26 => 'Muster wird gelöscht…';

  @override
  String get t_err27 => 'Es ist nicht genug Speicherplatz vorhanden, um dieses Muster zu speichern.\nSie müssen zuerst ein anderes Muster löschen.';

  @override
  String get t_err27_d => 'Es ist nicht genug Speicherplatz vorhanden, um dieses Muster zu speichern.\nAndere Muster löschen?';

  @override
  String get t_err61 => 'Es ist nicht genug Speicherplatz vorhanden, um dieses Muster zu speichern.';

  @override
  String get t_err61_d => 'Es ist nicht genug Speicherplatz vorhanden, um dieses Muster zu speichern.\nLöschen Sie einige Muster oder verwenden Sie ein anderes Medium.';

  @override
  String get t_err61_dd => 'Es ist nicht genug Speicherplatz vorhanden, um dieses Muster zu speichern.\nLöschen Sie einige Daten oder verwenden Sie ein anderes Medium.';

  @override
  String get t_err28 => 'Muster wird abgerufen.\nBitte warten Sie einen Moment.';

  @override
  String get t_err28_d => 'Muster wird abgerufen.\nBitte warten Sie einen Moment.';

  @override
  String get t_err29 => 'Soll das Muster gelöscht werden?';

  @override
  String get t_err65 => 'Soll das Muster gelöscht werden?';

  @override
  String get t_err29_d => 'Ausgewählte Daten löschen?';

  @override
  String get t_err30 => 'Sollen die aktuellen Einstellungen gespeichert werden?';

  @override
  String get t_err33 => 'Nehmen Sie den Stickrahmen ab.';

  @override
  String get t_err34 => 'Setzen Sie den Stickrahmen ein.';

  @override
  String get t_err36 => 'Wenn die Zickzackstichbreite durch den Geschwindigkeitsregler eingestellt wird, kann die Taste \"Start/Stopp\" nicht benutzt werden.';

  @override
  String get t_err37 => 'Die Sicherheitseinrichtung der Aufspulvorrichtung wurde aktiviert.\nIst der Faden verwickelt?';

  @override
  String get t_err38 => 'Diese Funktion kann im Zwillingsnadelmodus nicht benutzt werden.\nHeben Sie den Zwillingsnadelmodus auf und wählen Sie erneut diese Funktion.';

  @override
  String get t_err_twinn_10 => 'Die Geradstich-Stichplatte kann nicht im Zwillingsnadel-Modus verwendet werden.\nEntfernen Sie die Zwillingsnadel und brechen Sie den Zwillingsnadel-Modus ab.';

  @override
  String get t_err_twinn_11 => 'Der Zwillingsnadel-Modus wurde abgebrochen.\nEntfernen Sie bitte die Zwillingsnadel.';

  @override
  String get t_err_twinn_12 => 'Überprüfen Sie, ob die Zwillingsnadel entfernt wurde.';

  @override
  String get t_err42 => 'Prüfen Sie das Ergebnis und drücken Sie dann OK.';

  @override
  String get t_err48 => 'Die Daten für das ausgewählte Muster werden nicht erkannt. Die Daten sind möglicherweise fehlerhaft.';

  @override
  String get t_err50 => 'Den Spulenhalter nach links drücken.';

  @override
  String get t_err53 => 'Die Nadel ist nicht in der oberen Position.\nDie Taste \"Nadelposition\" zum Anheben der Nadel drücken.';

  @override
  String get t_err55 => 'Bringen Sie den Knopflochfuß „A＋“ an.\nDie eingebaute Kamera erkennt den Knopflochfuß „A＋“ anhand der Markierung „A＋“ und den drei Punkten.';

  @override
  String get t_err56 => 'Die Knopfgröße wird nicht erkannt.\nÜberprüfen Sie, ob die drei Punkte problemlos sichtbar sind oder geben Sie die Werte für die Lochlänge ein und versuchen Sie es erneut.';

  @override
  String get t_err63 => 'Diese Editierfunktion kann nicht benutzt werden, wenn das Muster über den roten Umriss hinausragt. Benutzen Sie diese Funktion nach dem Verschieben des Musters.';

  @override
  String get t_err64 => 'Enthält ein spezielles Muster, das nicht auf externen Speicher gespeichert werden kann.\nSpeichern Sie das Muster im Speicher der Maschine.';

  @override
  String get t_err69 => 'Es ist ein Muster enthalten, das nicht gespeichert werden kann.';

  @override
  String get t_err76 => 'Verwenden Sie einen größeren Stickrahmen.';

  @override
  String get t_err77 => 'Dieses Muster kann nicht mit diesem Stickrahmen verwendet werden.\nErsetzen Sie ihn mit dem für diese Betriebsart vorgesehenen Stickrahmen.';

  @override
  String get t_err82 => 'Sollen wieder die vorherigen Farbwechsel verwendet werden?';

  @override
  String get t_err83 => 'Sollen die Daten überschrieben werden?';

  @override
  String get t_err84 => 'Soll der vorherige Speicher aufgerufen und weiter verwendet werden?';

  @override
  String get t_err88 => 'Sticken nicht möglich, da die Stickeinheit nicht angebracht ist.\nSchalten Sie die Maschine aus und bringen Sie die Stickeinheit an.';

  @override
  String get t_err89 => 'Das USB-Medium ist nicht geladen.\nLaden Sie das USB-Medium.';

  @override
  String get t_err90 => 'Das USB-Medium kann nicht verwendet werden.';

  @override
  String get t_err_usb_notcompati => 'Das angeschlossene USB-Medium ist nicht mit dem Gerät kompatibel.\nBitte verwenden Sie ein anderes USB-Medium.';

  @override
  String get t_err93 => 'Das USB-Medium ist möglicherweise beschädigt.\nVerwenden Sie ein anderes USB-Medium und speichern Sie erneut.';

  @override
  String get t_err94 => 'Nicht genug Speicherplatz.\nLöschen Sie einige Muster oder verwenden Sie ein anderes USB-Medium.';

  @override
  String get t_err94_cmn => 'Nicht genügend Speicherplatz.\nLöschen Sie einige Muster oder verwenden Sie ein anderes Medium.';

  @override
  String get t_err95 => 'Das USB-Medium wurde gewechselt.\nWechseln Sie das USB-Medium nicht, während es gelesen wird.';

  @override
  String get t_err95_cmn => 'Das Medium wurde gewechselt.\nDas Medium nicht wechseln, solange es gelesen wird.';

  @override
  String get t_err96 => 'Das USB-Medium ist schreibgeschützt, so dass die Daten nicht gespeichert werden können. Deaktivieren Sie den Schreibschutz, bevor Sie die Daten speichern.';

  @override
  String get t_err96_cmn => 'Die Daten können nicht gespeichert werden, da das Medium schreibgeschützt ist.\nDeaktivieren Sie den Schreibschutz und versuchen Sie dann die Speicherung der Daten erneut.';

  @override
  String get t_err97 => 'Das USB-Medium ist schreibgeschützt, so dass die Daten nicht gelöscht werden können.\nDeaktivieren Sie den Schreibschutz, bevor Sie die Daten löschen.';

  @override
  String get t_err97_cmn => 'Die Daten können nicht gelöscht werden, da das Medium schreibgeschützt ist.\nDeaktivieren Sie den Schreibschutz und versuchen Sie dann die Löschung der Daten erneut.';

  @override
  String get t_err98 => 'Fehler im USB-Medium';

  @override
  String get t_err98_cmn => 'Mediumfehler';

  @override
  String get t_err99 => 'Das USB-Medium kann nicht gelesen werden.\nDas USB-Medium ist möglicherweise beschädigt.';

  @override
  String get t_err100 => 'Das USB-Medium wird formatiert';

  @override
  String get t_err101 => 'Übertragung über USB';

  @override
  String get t_err101_cmn => 'Medium wird gelesen';

  @override
  String get t_err103 => 'Bitte warten Sie einen Augenblick.';

  @override
  String get t_err104 => 'Betrieb nicht möglich, wenn Muster über den blauen Rahmen hinausragen.';

  @override
  String get t_err106 => 'Nächstes Segment sticken?';

  @override
  String get t_err107 => 'Sticken abgeschlossen.';

  @override
  String get t_err108 => 'Die Speicher sind voll.';

  @override
  String get t_err109 => 'Heben Sie den Nähfuß mit der Hebetaste an.';

  @override
  String get t_err110 => 'Senken Sie den Nähfuß mit der Hebetaste ab.';

  @override
  String get t_err111 => 'Nicht richtig eingefädelt.\nDrücken Sie die automatische Einfädeltaste erneut.';

  @override
  String get t_err113 => 'Das Programm wird aktualisiert.\nLaden Sie das Programm über USB in die Maschine.';

  @override
  String get t_err116 => 'Datenfehler';

  @override
  String get t_err117 => 'Flash-ROM-Fehler';

  @override
  String get t_err118 => 'Es ist eine Störung aufgetreten.\nSchalten Sie die Maschine aus und wieder ein.';

  @override
  String get t_err119 => 'Schalten Sie die Maschine aus, bevor Sie eine Stichplatte entfernen oder einsetzen.';

  @override
  String get t_err120 => 'Soll der Stickwagen an seine vorherige Position gefahren werden?';

  @override
  String get t_err120_2 => 'Stickrahmen abnehmen und Spule austauschen. Dann Rahmen anbringen und auf „OK“ tippen, um ihn an die vorherige Position zu bewegen.';

  @override
  String get t_err121 => 'Soll die kombinierte Umrandung getrennt werden?';

  @override
  String get t_err122 => 'Dieses USB-Medium ist nicht kompatibel.';

  @override
  String get t_err122_cmn => 'Dieses Medium ist nicht kompatibel.';

  @override
  String get t_err123 => 'Das USB-Medium wurde entfernt oder der Stecker ausgesteckt.';

  @override
  String get t_err124 => 'Es ist eine Störung aufgetreten.\nSchalten Sie die Maschine aus und wieder ein.';

  @override
  String get t_err125 => 'Der Oberfaden wurde möglicherweise nicht richtig eingefädelt.\nFädeln Sie den Oberfaden von Anfang an ein.';

  @override
  String get t_err126 => 'Soll der Nähfuß automatisch abgesenkt werden?';

  @override
  String get t_err127 => 'Im Zwillingsnadelmodus kann die automatische Nadeleinfädlertaste nicht verwendet werden.';

  @override
  String get t_err128 => 'Achten Sie darauf, dass der Stickrahmen so weit wie möglich nach hinten geschoben ist.\nKLAPPEN SIE DEN RAHMENSICHERUNGSHEBEL HERUNTER UM DEN RAHMEN EINZURASTEN.';

  @override
  String get t_err129 => 'Den Spulenhalter nach rechts drücken.';

  @override
  String get t_err130 => 'Der Nähfuß bewegt auf und ab.\nHalten Sie Ihre Hände usw. außer Reichweite des Nähfußes.';

  @override
  String get t_err131 => 'Dieses Muster kann nicht verwendet werden.';

  @override
  String get t_err132 => 'Vor dem Sticken sicherstellen, dass der Stickr. ganz nach hinten geschoben und der Sicherungshebel heruntergeklappt ist. \"Start/Stopp\" drücken.';

  @override
  String get t_err133 => 'Dieser Stickrahmen kann nicht verwendet werden.';

  @override
  String get t_err134 => 'Dieses Muster kann nicht verwendet werden, weil die Datenkapazitätsgrenze überschritten ist.';

  @override
  String get t_err136 => 'Vorbeugende Wartung wird empfohlen.';

  @override
  String get t_err137 => 'Vorbeugende Wartung wird empfohlen.\n1000 Stunden sind überschritten.';

  @override
  String get t_err208 => 'Wird berechnet…';

  @override
  String get t_err209 => 'Der Stickarm der Stickeinheit wird sich bewegen.';

  @override
  String get t_err210 => 'Wird erkannt…';

  @override
  String get t_err213 => 'Kann Stickmuster-Positionsmarkierung nicht erkennen.';

  @override
  String get t_err215 => 'Stickmuster-Positionsmarkierung entfernen.';

  @override
  String get t_err224 => 'Auf dem weißen Papier oder Stoff sind Staub oder Flecken.\nSauberes weißes Papier oder Stoff einlegen.';

  @override
  String get t_err227 => 'Korrekte Einstellung oder Erkennung nicht möglich.';

  @override
  String get t_err228 => 'Datei ist zu groß und kann nicht verwendet werden.\nKleinere Datei verwenden.';

  @override
  String get t_err229 => 'Diese Datei kann nicht verwendet werden.';

  @override
  String get t_err229_b => 'Diese Dateiversion kann nicht gelesen werden.';

  @override
  String get t_err239 => 'Mit PC verbinden.\nUSB-Kabel nicht trennen.';

  @override
  String get t_err241 => 'Datei kann nicht gelesen werden.';

  @override
  String get t_err242 => 'Speichern der Datei fehlgeschlagen.';

  @override
  String get t_err244 => 'Gewähltes Bild löschen?';

  @override
  String get t_err245 => 'Diese Taste kann im Moment nicht benutzt werden.';

  @override
  String get t_err246 => 'Das Muster ragt über den Rand des Musterbereichs hinaus.\nPassen Sie die Position des Musters an und scannen Sie den neuen Bereich.';

  @override
  String get t_err247 => 'Nicht genügend Speicherplatz vorhanden.';

  @override
  String get t_err248 => 'Möchten Sie die Einstellung löschen?';

  @override
  String get t_err249 => 'Kann Stoffkante nicht erkennen.';

  @override
  String get t_err250 => 'Dieses Muster kann nicht konvertiert werden.';

  @override
  String get t_err251 => 'Größe und Position der Stickerei zurücksetzen?';

  @override
  String get t_err252 => 'Stickereigröße zurücksetzen?';

  @override
  String get t_err253 => 'Stoffdicke erfassen.\nPositionsaufkleber auf roter Linie anbringen.';

  @override
  String get t_err254 => 'Erfassung erfolgreich.\nStickpositionsmarkierung entfernen.\nOK-Taste drücken, um Hintergrundspeicherung zu starten.';

  @override
  String get t_err255 => 'OK-Taste drücken.\nDer Stickrahmen bewegt sich und die Hintergrundspeicherung beginnt.';

  @override
  String get t_err256 => 'Erfassungsfehler.\nWiederholen?';

  @override
  String get t_err257 => 'Zertifizierung war erfolgreich.\nBitte die Nähmaschine neu starten.';

  @override
  String get t_err257_1 => 'Zertifizierung war erfolgreich.\nBitte die Nähmaschine neu starten.';

  @override
  String get t_err257_2 => 'Zertifizierung war erfolgreich.\nStarten Sie die Maschine neu.';

  @override
  String get t_err259 => 'Upgrade-Kit zertifizieren.\n\nDrücken Sie zur Zertifizierung die Kit-Nummer.';

  @override
  String get t_err260 => 'Geben Sie den Zertifizierungsschlüssel ein und drücken Sie dann [EINSTELLEN].';

  @override
  String get t_err261 => 'Zertifizieren…';

  @override
  String get t_err262 => 'Der Zertifizierungsschlüssel ist nicht richtig.\nPrüfen Sie den Schlüssel und geben Sie ihn erneut ein.';

  @override
  String get t_err263 => 'KIT';

  @override
  String get t_err264 => 'Hintergrundbild wirklich löschen?';

  @override
  String get t_err265 => 'Muster wirklich auf Ausgangsgröße, -winkel und -position zurücksetzen?';

  @override
  String get t_err343 => 'Ursprüngliche Winkelstellung und/oder Position wirklich wiederherstellen?';

  @override
  String get t_err266 => 'Upgrade-Kit zertifizieren.';

  @override
  String get t_err270 => 'Befestigen Sie die erste Markierung für die Stickpositionierung fest auf dem Material, so dass sie sich innerhalb des roten Rahmens befindet. Der Wagen der Stickeinheit bewegt sich, nachdem die Scan-Taste gedrückt wurde.';

  @override
  String get t_err271 => 'Befestigen Sie die zweite Markierung für die Stickpositionierung fest auf dem Material, so dass sie sich innerhalb des roten Rahmens befindet. Der Wagen der Stickeinheit bewegt sich, nachdem die Scan-Taste gedrückt wurde.';

  @override
  String get t_err273 => 'Die Stickpositioniermarkierung ist nicht richtig befestigt.\nBitte die Stickpositioniermarkierung entfernen und neu befestigen.';

  @override
  String get t_err274 => 'Die Positioniermarkierungen werden erkannt.\nDas Material mit den befestigten Positioniermarkierungen neu einspannen.\nWenn die Mittelpunkte der Positioniermarken im Stickbereich liegen, ein Stickmuster auswählen.';

  @override
  String get t_err276 => 'Die Stickpositioniermarkierungen werden erkannt.\nBitte die Stickpositioniermarkierungen entfernen.';

  @override
  String get t_err277 => 'Möchten Sie die Musterverbindung wirklich „Abbrechen“?';

  @override
  String get t_err278 => 'Wenn Sie den Vorgang beenden, kann das Muster des nächsten Abschnitts nicht mehr gestickt werden. Möchten Sie die Musterverbindung wirklich beenden?';

  @override
  String get t_err279 => 'Stickerei abgeschlossen.\nNächstes Muster verbinden?\n\n* Nehmen Sie den Stoff nicht aus dem Rahmen.\n* Wenn Sie später fortfahren möchten, wählen Sie den nächsten Musterabschnitt aus und bestätigen Sie. Sie können mit dem Vorgang fortfahren, wenn die Maschine diese Daten gespeichert hat.';

  @override
  String get t_err282 => 'Mehr kann nicht eingegeben werden.';

  @override
  String get t_err283 => 'Diese Anwendung wird geschlossen.';

  @override
  String get t_err284 => 'Diese Daten sind zu kompliziert und können nicht konvertiert werden.';

  @override
  String get t_err286 => 'Bearbeiten der Zeile beenden?';

  @override
  String get t_err288 => 'Die Stickpositioniermarkierungen werden erkannt.\nBitte die Stickpositioniermarkierungen entfernen und an anderen Positionen neu befestigen.';

  @override
  String get t_err290 => 'Stickpositioniermarkierungen nicht erkannt.\nBitte die Stickpositioniermarkierungen entfernen und neu befestigen.\nDie Mittelpunkte der Stickpositioniermarkierungen sollten im Stickbereich liegen.';

  @override
  String get t_err291 => 'Nicht genug Speicherplatz.\nVerwenden Sie ein anderes USB-Medium.';

  @override
  String get t_err291_cmn => 'Nicht genügend Speicherplatz.\nVerwenden Sie ein anderes Medium.';

  @override
  String get t_err292 => 'Für den gewählten Modus gibt es in der Garnfarbentabelle nicht genügend Farben.';

  @override
  String get t_err292_s => 'Die aktuelle Palette umfasst nicht genügend Farben für den ausgewählten Modus.';

  @override
  String get t_err297 => 'Wiederholen Sie den Vorgang von Schritt 3 bis 4.';

  @override
  String get t_err298 => 'Der Aufkleber ist schmutzig.';

  @override
  String get t_err_cameracalib_ng_msg => 'Korrekte Erkennung nicht möglich.\nBefestigen Sie einen neuen weißen Sticker.';

  @override
  String get t_err_cameracalib_ok_msg => 'Drücken Sie die OK-Taste, um die Nadeleinstichposition zu speichern.';

  @override
  String get t_err299 => 'Wenn die Einstellung nach mehreren Versuchen nicht gelingt, wenden Sie sich bitte an Ihren nächsten Händler.';

  @override
  String get t_err300 => 'Informationen zur empfohlenen Nadel finden Sie bei Bedarf in der Bedienungsanleitung und Kurzanleitung.';

  @override
  String get t_err_cameracalib_title => 'Nadelkalibrierung für Kamera/Projektor';

  @override
  String get t_err_cameracalib_1_4 => '1. Die Taste \"Nadelposition\" drücken, um die Nadel\n anzuheben.\n2. Nachdem Sie die Nadel und den Nähfuß entfernt haben,\n befestigen Sie den weißen Sticker im Bereich der\n Nadeleinstichposition.\n3. Setzen Sie die Nadel ein\n (Standardgröße 75/11 oder 90/14).\n4. Starten Sie den Kalibriervorgang durch Drücken der\n Taste \"START\".\nAchten Sie aus Sicherheitsgründen vor dem Drücken der\n Taste \"START\" darauf, dass der Nadelbereich frei ist.\n\n* Berühren Sie daher die Nadel nicht mit den Händen\n oder anderen Gegenständen, um Verletzungen zu\n vermeiden.';

  @override
  String get t_err303 => 'Stickerei abgeschlossen.\nNächstes Muster verbinden?';

  @override
  String get t_err304 => 'Nehmen Sie den Stoff nicht aus dem Rahmen.\nDrücken Sie OK zur Auswahl des nächsten Musters.';

  @override
  String get t_err307 => 'Die Stickpositioniermarkierungen nicht entfernen.\nSpannen Sie den Stoff wieder so ein, dass sich das nächste Muster und die Mittelpunkte der beiden Stickpositioniermarkierungen im Stickbereich befinden.';

  @override
  String get t_err308 => 'Das nächste Muster befindet sich außerhalb des Stickbereichs.\nSpannen Sie den Stoff wieder so ein, dass sich das nächste Muster und die Mittelpunkte der beiden Stickpositioniermarkierungen im Stickbereich befinden.';

  @override
  String get t_err309 => 'Stickpositioniermarkierung nicht erkannt.\nSpannen Sie den Stoff wieder so ein, dass sich das nächste Muster und die Mittelpunkte der beiden Stickpositioniermarkierungen im Stickbereich befinden.';

  @override
  String get t_err310 => 'Die Position der Stickpositioniermarkierungen wurde verändert.\nSpannen Sie den Stoff wieder so ein, dass sich das nächste Muster und die Mittelpunkte der beiden Stickpositioniermarkierungen im Stickbereich befinden.';

  @override
  String get t_err311 => 'Die Stickpositioniermarkierungen werden erkannt.\nEntfernen Sie die Stickpositioniermarkierungen und sticken Sie das Muster.';

  @override
  String get t_err311_size => 'Die Stickpositioniermarkierungen werden erkannt.\nEntfernen Sie die Stickpositioniermarkierungen und sticken Sie das Muster.\n\n* Die Größe des nächsten Musters wurde automatisch angepasst, da sich der Abstand zwischen den Markierungen beim Neueinspannen geändert hat.';

  @override
  String get t_err311_rehoop => 'Der Abstand zwischen den Markierungen ist nach dem Neueinspannen nicht mehr korrekt.\nSpannen Sie den Stoff um, sodass der Abstand zwischen den Markierungen die folgende Länge aufweist.';

  @override
  String get t_err312 => 'Die Stickpositioniermarkierungen nicht entfernen.\nDie Stickpositioniermarkierungen müssen erneut befestigt werden.\nBitte das Material neu einspannen.';

  @override
  String get t_err313 => 'Stickpositioniermarkierung nicht erkannt.\nBitte das Material neu einspannen.';

  @override
  String get t_err314 => 'Die Stickpositioniermarkierungen werden erkannt.\nEntfernen Sie die Stickpositioniermarkierungen.';

  @override
  String get t_err354 => 'Der Abschalt-Supportmodus wurde aktiviert.\nSchalten Sie die Maschine aus.';

  @override
  String get t_err356 => 'Dieser Stich ist nicht mit dem Obertransportmodus kompatibel.';

  @override
  String get t_err359 => 'Entfernen Sie das Obertransportmodul von der Maschine.';

  @override
  String get t_err360 => 'Stellen Sie die Uhr ein.';

  @override
  String get t_err361 => 'Wählen Sie Ihre Sprache.';

  @override
  String get t_err362 => 'Entfernen Sie den Stickfuß mit LED-Zeiger von der Maschine.';

  @override
  String get t_err364 => 'Modulfehler';

  @override
  String get t_err368 => 'Einstellungen für Rand, Position und/oder Winkel des Musters wirklich zurücksetzen?';

  @override
  String get t_err373 => 'Der Stickrahmen wurde verändert, mit dem ursprünglichen Rahmen ersetzen.';

  @override
  String get t_err380 => 'Zum Einfädeln nehmen Sie den Stoff unter dem Nähfuß heraus.';

  @override
  String get t_err381 => 'Möchten Sie die Endpunkteinstellung abbrechen?';

  @override
  String get t_err382 => 'Der Einstellungsmodus der Nähendpunkteinstellung ist mit dem ausgewählten Stich nicht verfügbar. \nWählen Sie einen anderen Stich aus, oder ändern Sie die Stichlänge.';

  @override
  String get t_err383 => 'Wenn Sie den Endpunkt-Aufkleber entfernt haben, setzen Sie den Nähvorgang fort.';

  @override
  String get t_err384 => 'Der Einstellungsmodus der Stichendeinstellung kann derzeit nicht verwendet werden. \nDie Endpunkteinstellung wird gelöscht.';

  @override
  String get t_err385 => 'Dieser Abstand ist zu kurz, um die Endpunkteinstellung zu verwenden.\nSie können es verwenden, wenn der Abstand länger ist oder die Einstellung „Vorübergehend anhalten“ auf AUS gesetzt ist.';

  @override
  String get t_err386 => 'DieDiese Funktion kann bei Endpunkteinstellung ON nicht verwendet werden.';

  @override
  String get t_err390 => 'OK zum Löschen aller Bearbeitungsdaten und weiter zum Home-Bildschirm?';

  @override
  String get t_err390_old => 'Sollen alle Muster gelöscht werden und möchten Sie zur Startseite zurückkehren?';

  @override
  String get t_err391 => 'Aktuelle Musterauswahl abbrechen?';

  @override
  String get t_err391_u => 'Soll die aktuelle Stichauswahl gelöscht werden?';

  @override
  String get t_err392 => 'Übertragung per Stickkarte.';

  @override
  String get t_err393 => 'Dieses Muster kann nicht kombiniert werden.';

  @override
  String get t_err394 => 'Es ist nicht genug Speicherplatz vorhanden, um dieses Muster zu speichern. \nEin gespeichertes Muster löschen?';

  @override
  String get t_err395 => 'Dieses Muster kann nicht geladen werden, weil es über den editierbaren Bereich hinausragt.';

  @override
  String get t_err396 => 'Dieses Muster kann nicht geladen werden, weil es über den editierbaren Bereich hinausragt. \nBewegen Sie den Punkt mit den Pfeiltasten in den editierbaren Bereich.';

  @override
  String get t_err397 => 'Aktuellen Stich wirklich löschen?';

  @override
  String get t_err398 => 'Als neue Datei speichern…';

  @override
  String get t_err400 => 'Muster ragt über den Stickrahmen hinaus.\nUm mehr Muster hinzuzufügen, muss dieses Muster gedreht werden.';

  @override
  String get t_err401 => 'Muster ragt über den Stickrahmen hinaus.';

  @override
  String get t_err402 => 'Muster ragt über den Stickrahmen hinaus.\nKeine weiteren Zeichen mehr hinzufügen.';

  @override
  String get t_err403 => 'Muster ragt über den Stickrahmen hinaus.\nDiese Funktion kann im Moment nicht verwendet werden.';

  @override
  String get t_err404 => 'Schriftart kann nicht geändert werden, da einige Buchstaben nicht in der ausgewählten Schriftart enthalten sind.';

  @override
  String get t_err405 => 'Der ausgewählte Musterbereich ragt über den Stickrahmen hinaus.';

  @override
  String get t_err406 => 'Muster ragt über den ausgewählten Stickrahmen hinaus.\nAktuelle Musterauswahl abbrechen?';

  @override
  String get t_err408 => 'Diese Funktion kann bei überlappenden Mustern nicht verwendet werden.';

  @override
  String get t_err410 => 'Das Muster kann so gestickt werden, dass die Mitte oder eine Ecke mit der Stickpositionsmarkierung ausgerichtet ist. Befestigen Sie die Stickpositionsmarkierung an der gewünschten Position.';

  @override
  String get t_err411 => 'Drücken Sie nach Abschluss der erforderlichen Vorbereitungen die Taste [Scan].';

  @override
  String get t_err412 => 'Die Stickpositionsmarkierung wurde im Erfassungsbereich nicht gefunden.';

  @override
  String get t_err413 => 'Verwenden Sie zum Verbinden von Mustern die Stickpositionsmarkierung.';

  @override
  String get t_err414 => 'Wählen Sie die Position für den Anschluss \ndes nächsten Musters.';

  @override
  String get t_err415 => 'Daten können nicht gelesen werden.';

  @override
  String get t_err416 => 'Daten gespeichert.\nDateiname:';

  @override
  String get t_err417 => 'Die Daten werden gelesen.\nBitte warten.';

  @override
  String get t_err418 => 'Dieser Dateityp kann nicht verwendet werden.';

  @override
  String get t_err419 => 'Diese Datei kann nicht verwendet werden, da sie zu groß ist.';

  @override
  String get t_err420 => 'Verfolgung des Bildes fehlgeschlagen. ';

  @override
  String get t_err421 => 'Die Anzahl von Farben in einem Bild wird auf die hier angegebene Zahl reduziert und anschließend wird der Umriss extrahiert.';

  @override
  String get t_err422 => 'Legen Sie das Papier mit der Abbildung oder Strichzeichnung in den Scanrahmen.';

  @override
  String get t_err423 => 'Ein Stickrahmen kann nicht verwendet werden. Achten Sie darauf, dass Sie den Scanrahmen verwenden.';

  @override
  String get t_err424 => 'Es kann einige Minuten dauern, bis die Erkennung abgeschlossen ist.';

  @override
  String get t_err425 => 'Weiter zum Bildschirm \"Mein Design Center\"?';

  @override
  String get t_err426 => 'Weiter zum Bildschirm \"IQ Designer\"?';

  @override
  String get t_err428 => 'Die in \"Mein Design Center\" erstellten Bilddaten werden nicht gespeichert. Fortfahren?';

  @override
  String get t_err429 => 'Die Daten von \"IQ Designer\" werden nicht gespeichert. Fortfahren?';

  @override
  String get t_err430 => 'Der Scanrahmen kann nicht zum Sticken verwendet werden.\nErsetzen Sie ihn durch einen Stickrahmen.';

  @override
  String get t_err432 => 'Bringen Sie den Rahmen, der das einzuscannende Bild enthält, an der Maschine an.';

  @override
  String get t_err433 => 'Verwenden Sie beim Konvertieren eines Bildes\nin Linienbilder oder Füllbilder Scanrahmen,\num entsprechende Garnfarbeninformationen zu laden.';

  @override
  String get t_err440 => 'Wählen Sie die Bilddatei.';

  @override
  String get t_err445 => 'Diese Bilddatei kann nicht verwendet werden.';

  @override
  String get t_err446 => 'Scannen…';

  @override
  String get t_err447 => 'Wird erkannt…';

  @override
  String get t_err448 => 'Verarbeitung läuft…';

  @override
  String get t_err451 => 'Alle bearbeiteten Bilddaten löschen?';

  @override
  String get t_err452 => 'Es können Bilddateien in den Formaten JPG, PNG oder BMP bis zu einer Größe von 5MB, 1,2 Mio. Pixel verwendet werden.';

  @override
  String get t_err453 => 'OK die Originaleinstellungen dieser Seite wieder herzustellen?';

  @override
  String get t_err454 => 'Die Musterkombination ist für den erkannten Stickrahmen zu groß. Drehen Sie die Musterkombination, wenn Sie mehr Muster hinzufügen möchten.';

  @override
  String get t_err455 => 'Die Musterkombination ist für den erkannten Stickrahmen zu groß.';

  @override
  String get t_err457 => 'Stellen Sie die Ansicht zur Stickrahmenerkennung auf AUS.';

  @override
  String get t_err458 => 'Bilddatei wird importiert.';

  @override
  String get t_err459 => 'Diese Stickeinheit kann nicht verwendet werden.';

  @override
  String get t_err460 => 'Die im Bildbereich angezeigten Muster können konvertiert werden.';

  @override
  String get t_err461 => 'Der Rahmen wird zum Scannen mit der eingebauten Kamera bewegt.';

  @override
  String get t_err462_pp => 'Diese Bilddatei kann nicht verwendet werden.\nAls Bilddateien können JPG-, PNG- oder BMP-Dateien mit einer Größe von weniger als 6 MB und maximal 16 Millionen Pixeln verwendet werden.';

  @override
  String get t_err463 => 'Entfernen Sie den Stickrahmen oder Scanrahmen.';

  @override
  String get t_err464 => 'Das Muster ragt über den Stickbereich hinaus und kann nicht konvertiert werden.';

  @override
  String get t_err465 => 'Konvertiert in das Stickmuster, Mein Design Center wird beendet. \nWeiter zum Stickeditierbildschirm?';

  @override
  String get t_err466 => 'Mein Design Center beenden?';

  @override
  String get t_err467 => 'Diese Funktion kann mit dem Musterverbindungsmodus nicht verwendet werden.';

  @override
  String get t_err468 => 'Maschinen-PCB ausgeschaltet';

  @override
  String get t_err469 => 'Konvertiert in das Stickmuster, IQ Designer wird beendet. Weiter zum Stickeditierbildschirm?';

  @override
  String get t_err470 => 'IQ Designer beenden?';

  @override
  String get t_err471 => 'Ausgewählte Daten löschen?';

  @override
  String get t_err472 => 'Mehrere Muster wählen.';

  @override
  String get t_err473 => 'Angeordnete(s) Muster als Bild(er) gespeichert.';

  @override
  String get t_err474 => 'Umriss der angeordneten Muster gespeichert.';

  @override
  String get t_err475 => 'Versatzabstand um das Muster festlegen.';

  @override
  String get t_err478 => 'Aus der Stempelmusterliste in \"Mein Design Center\" abrufen.';

  @override
  String get t_err478_tc => 'Aus der Stempelmusterliste in \"IQ Designer\" abrufen.';

  @override
  String get t_err479 => 'Ein Bobbin-Work-Muster kann nicht mit einem Muster einer anderen Kategorie kombiniert werden.';

  @override
  String get t_err480 => 'Bewegt die Nadel zur ersten Nadelposition.';

  @override
  String get t_err481 => 'Nähen eines Bobbin-Work-Musters beendet.';

  @override
  String get t_err482 => 'Nähen aller Bobbin-Work-Muster beendet.';

  @override
  String get t_err483 => 'Fäden abschneiden.';

  @override
  String get t_err484_old => 'Vor dem Sticken der folgenden Muster die Menge und den Typ des eingesetzten Unterfadens prüfen.';

  @override
  String get t_err484 => 'Ersetzen Sie den Unterfaden und setzen Sie dann den Stickrahmen ein.\nNach dem Drücken auf OK bewegt sich der Stickarm der Stickeinheit.';

  @override
  String get t_err485 => 'Das folgende Bobbin-Work-Muster wird gestickt.';

  @override
  String get t_err486 => 'Das Handrad zum Absenken der Nadel in den Stoff drehen und dann den Unterfaden heraufziehen.';

  @override
  String get t_err489 => 'Keine Daten zum Konvertieren vorhanden.';

  @override
  String get t_err496 => 'Die Einstellung auf alle Bereiche anwenden?';

  @override
  String get t_err497 => 'Umriss';

  @override
  String get t_err501 => 'Einstellungen können nicht angewandt werden. Es ist nicht genug Speicherplatz vorhanden, um die Attribute zu speichern.';

  @override
  String get t_err502 => 'Der Abstand zwischen den ausgewählten Mustern ist zu groß.';

  @override
  String get t_err503 => 'Die Form ist für eine Applikationslinie zu kompliziert.';

  @override
  String get t_err503_new => 'Die Form ist für die Applikationslinie zu komplex oder ungeeignet.\nÄndern Sie die Applikationseinstellungen oder wählen Sie ein anderes Muster aus.\n* Das Ergebnis kann je nach Position und Winkel abweichen.';

  @override
  String get t_err504 => 'Zu groß zum Hinzufügen einer Applikationslinie.';

  @override
  String get t_err509 => 'Ein Teil der Textur kann aufgrund der Datenstruktur nicht angezeigt werden.';

  @override
  String get t_err505 => 'Diese Funktion kann nicht verwendet werden, wenn Farbensortierung ausgeführt wird.';

  @override
  String get t_err506 => 'Löschen der Garnmarkierung OK?';

  @override
  String get t_err508 => 'Garnmarkierungsfunktion kann nicht verwendet werden, wenn die Auswahl des letzten Farbbereichs aufgehoben wird.\nLöschen der Garnmarkierung OK?';

  @override
  String get t_err507 => 'Auto';

  @override
  String get t_err510 => 'Aktuelles Bild verwenden?';

  @override
  String get t_err511 => 'Die Daten wurden im Maschinenspeicher gespeichert.\nAnhand dieser Daten sticken?';

  @override
  String get t_err515 => 'Schalten Sie die Maschine aus, damit der interne Projektor abkühlen kann.';

  @override
  String get t_err516 => 'Dieser Stich kann mit der aktuell angebrachten Stichplatte nicht verwendet werden.';

  @override
  String get t_err517 => 'Wechseln Sie die Stichplatte, um diesen Stich zu verwenden.';

  @override
  String get t_err518 => 'Der Stickrahmen wurde geändert. Der Wagen der Stickeinheit wird bewegt.\n';

  @override
  String get t_err519 => 'Der Projektor wird ausgeschaltet.';

  @override
  String get t_err520 => 'Halten Sie Hände, Fremdkörper etc. vom Stickarm fern.';

  @override
  String get t_err_521 => 'Video-Lernprogramme können heruntergeladen werden.';

  @override
  String get t_err574 => 'Es ist eine Fehlfunktion aufgetreten.\n Ein Zugriff auf EEPROM ist nicht möglich.';

  @override
  String get t_err575 => 'Die Unterfadenspule ist fast leer.\n\n* Verwenden Sie die Taste „Verstärkungsstich“ zum Vernähen durch wiederholtes Nähen eines Einzelstiches.\n* Verwenden Sie die Taste „Rahmen bewegen“, um den Stickwagen so zu bewegen, dass der Stickrahmen entfernt oder eingesetzt werden kann. Danach bewegt sich der Wagen zurück in die vorherige Position.';

  @override
  String get t_err577 => 'Ein Plattstichmuster kann nicht mit einem Muster einer anderen Kategorie kombiniert werden.';

  @override
  String get t_err578 => 'Sie können keine weiteren Farbschemen als Favoriten auswählen.';

  @override
  String get t_err581 => 'Beginnen Sie mit dem Sticken an der oberen rechten Ecke des Stoffes.\nBefestigen Sie den Stickrahmen an der ursprünglichen Stickposition.';

  @override
  String get t_err581_b => 'Beginnen Sie mit dem Sticken an der linken oberen  Ecke des Stoffes.\nBefestigen Sie den Stickrahmen an der ursprünglichen Stickposition.';

  @override
  String get t_err582 => 'Eine Seite wurde gestickt. Drehen Sie den Stoff um 90 Grad gegen den Uhrzeigersinn, und befestigen Sie ihn dann erneut im Stickrahmen.';

  @override
  String get t_err582_n => 'Eine Seite wurde genäht. Drehen Sie den Stoff entgegen dem Uhrzeigersinn und spannen Sie ihn für die nächste Ecke wieder im Stickrahmen ein.';

  @override
  String get t_err582_e => 'Eine Reihe ist fertig. Um mit der nächsten Reihe zu beginnen, spannen Sie den Stoff neu am linken Rand der nächsten Reihe ein. Die Garnmarkierung des oberen Musters muss dabei im Stickrahmen liegen.';

  @override
  String get t_err583 => 'Passen Sie die innere Ecke des Musters mit den Tasten „Muster bewegen“ an.';

  @override
  String get t_err583_e => 'Verwenden Sie die Tasten zum Verschieben des Musters, um die linke obere Ecke des Musterbereichs an der linken oberen Ecke (Markierung) des Stickbereichs auszurichten.';

  @override
  String get t_err584 => 'Richten Sie mit den Tasten „Muster bewegen“ den Anfangspunkt am Endpunkt des vorherigen Musters aus.';

  @override
  String get t_err584_e => 'Verwenden Sie die Tasten zum Verschieben des Musters, um die obere linke Ecke des Musterbereichs an der unteren linken Garnmarkierung des oberen Musters auszurichten.';

  @override
  String get t_err585 => 'Passen Sie mit den Tasten „Drehen“ den Winkel des Musters an, und achten Sie dabei auf die Punkte um das Muster herum.';

  @override
  String get t_err586 => 'Passen Sie die Größe des Musters an, sodass der Punkt unten links die innere Ecke des nächsten Musters wird.';

  @override
  String get t_err586_b => 'Passen Sie die Größe des Musters an, sodass der Punkt unten rechts die innere Ecke des nächsten Musters wird.';

  @override
  String get t_err586_e => 'Verwenden Sie die Tasten zum Drehen und für die Größenauswahl, um den Winkel und die Größe des Musters einzustellen. Achten Sie dabei auf die Ränder des Musters.';

  @override
  String get t_err587 => 'Richten Sie mit den Tasten „Drehen“ und „Größe“ den Endpunkt am Anfangspunkt des ersten Musters aus.';

  @override
  String get t_err588 => 'Bitte das Material neu einspannen.';

  @override
  String get t_err588_e => 'Bitte spannen Sie den Stoff auf rechts um. Dabei sollte die rechte Kante des Musters auf der linken Seite des Stickrahmens eingespannt werden.';

  @override
  String get t_err588_e_2 => 'Spannen Sie den Stoff weiter rechts neu ein. Der rechte Rand des Musters liegt dabei rechts und die Garnmarkierung des Musters darüber liegt innerhalb des Stickrahmens.';

  @override
  String get t_err590 => 'Drücken Sie auf LADEN, um die Update-Datei zu installieren.';

  @override
  String get t_err591 => 'Es steht ein neues Update zur Verfügung. Schalten Sie zum Installieren des Updates die Maschine aus, und halten Sie dann die Taste „Automatisches Einfädeln“ gedrückt, und schalten Sie die Maschine wieder ein.';

  @override
  String get t_err_dl_updateprogram2 => 'Das neue Aktualisierungsprogramm ist bereit.\nSchalten Sie zum Installieren des Updates die Maschine aus, und halten Sie dann die Taste „Automatisches Einfädeln“ gedrückt, und schalten Sie die Maschine wieder ein.';

  @override
  String get t_err592 => 'Drücken Sie auf LADEN, um die Update-Datei zu speichern.';

  @override
  String get t_err593 => 'Wählen Sie das Gerät aus, auf dem die Update-Datei gespeichert wird.';

  @override
  String get t_err594 => 'Wählen Sie das Gerät aus, auf dem die Update-Datei gespeichert ist.';

  @override
  String get t_err_dl_updateprogram => 'Drücken Sie die Taste „Start“, um das Aktualisierungsprogramm herunterzuladen.';

  @override
  String get t_err_dl_fail => 'Download fehlgeschlagen: Der interne Speicher ist voll.';

  @override
  String get t_err_networkconnectionerr => 'Netzwerkverbindung ist unterbrochen.\nVergewissern Sie sich, dass die Maschine mit einem WLAN-Netzwerk verbunden ist.';

  @override
  String get t_err_not_turnoff => 'Schalten Sie die Maschine nicht aus.';

  @override
  String get t_err_pressresume_continuedl => 'Drücken Sie die Taste „Fortsetzen“, um mit dem Download fortzufahren.';

  @override
  String get t_err_updateformovie => 'Führen Sie die Aktualisierung erneut durch, um die Filme zu installieren.';

  @override
  String get t_err595 => 'Geben Sie den 16-stelligen Aktivierungscode ein.';

  @override
  String get t_err596 => 'Geben Sie den 16-stelligen Aktivierungscode ein, und drücken Sie dann die Taste [Einstellen].';

  @override
  String get t_err597 => 'Die Maschinennummer und der Aktivierungscode werden an den Server gesendet.';

  @override
  String get t_err598 => '„Online-Maschinenzertifizierung“ wird für die mit dem Wireless-LAN verbundene Maschine empfohlen.';

  @override
  String get t_err599 => 'Der Aktivierungscode ist falsch.\nÜberprüfen Sie den Schlüssel und geben Sie ihn dann erneut ein.';

  @override
  String get t_err599_used => 'Der eingegebene Code wurde bereits mit einer anderen Maschine registriert.';

  @override
  String get t_err601 => 'WLAN aktivieren?';

  @override
  String get t_err602 => 'Suche SSID…';

  @override
  String get t_err603 => 'Übernehmen?';

  @override
  String get t_err604 => 'WLAN-Verbindung hergestellt.';

  @override
  String get t_err605 => 'WLAN-Verbindung herstellen.';

  @override
  String get t_err606 => 'Netzwerk getrennt.\nPrüfen Sie die WLAN-Einstellung.';

  @override
  String get t_err607 => 'Verbindung mit Server fehlgeschlagen.\nNetzwerkeinst. überprüfen.';

  @override
  String get t_err608 => 'Authentifizierung fehlg. bei Verbindung mit Server.\nProxy-Servereinst. überpr.';

  @override
  String get t_err609 => 'Es ist ein Netzwerkfehler aufgetreten.';

  @override
  String get t_err611 => 'Deaktiviert';

  @override
  String get t_err612 => 'In der Netzwerkfunktion sind Fehler aufgetreten.';

  @override
  String get t_err613 => 'Die Daten konnten nicht importiert werden.\nBeginnen Sie noch einmal.';

  @override
  String get t_err614 => 'Es sind gespeicherte Access-Point-Daten vorhanden.\nSoll eine Verbindung mit diesen Daten aufgebaut werden?';

  @override
  String get t_err615 => 'Verbindungsfehler 01';

  @override
  String get t_err616 => 'Netzwerkschlüssel falsch.';

  @override
  String get t_err617 => 'Netzwerkschlüssel falsch.';

  @override
  String get t_err618 => 'Netzwerk-Reset?';

  @override
  String get t_err620 => 'Es ist eine Störung aufgetreten. Schalten Sie die Maschine aus und wieder ein. ';

  @override
  String get t_err621 => 'Authentifizierung fehlg. bei Verbindung mit Server.\nProxy-Servereinst. überpr.';

  @override
  String get t_err622 => 'Authentifizierung fehlgeschlagen.\nBenutzername und Kennwort prüfen.';

  @override
  String get t_err623 => 'Abbruch';

  @override
  String get t_err624 => 'Übertragungsfehler';

  @override
  String get t_err625 => 'Abgeschlossen';

  @override
  String get t_err626 => 'I/F prüfen';

  @override
  String get t_err627 => 'Verbindungsfehl';

  @override
  String get t_err628 => 'Verbindung mit Server fehlgeschlagen.\nNetzwerkeinst. überprüfen.';

  @override
  String get t_err629 => 'Verbindung mit Server fehlgeschlagen.\nSpäter erneut versuchen.';

  @override
  String get t_err630 => 'Download.\nBitte warten.';

  @override
  String get t_err631 => 'Fehler beim Download.\nVorgang wiederholen.';

  @override
  String get t_err632 => 'Kein Access Point.';

  @override
  String get t_err633 => 'Keine Daten!';

  @override
  String get t_err634 => 'Siehe Fehlersuche im Benutzerhandbuch.';

  @override
  String get t_err636 => 'Server kann nicht gefunden werden. Name und Adresse prüfen oder anderen LDAP-Server wählen.';

  @override
  String get t_err637 => 'Server Timeout.\nSpäter erneut versuchen.';

  @override
  String get t_err638 => 'Übertragen…';

  @override
  String get t_err697 => 'Keine Datenübertragung per USB-Kabel verfügbar.';

  @override
  String get t_err84_mdc => 'Soll der vorherige Speicher aufgerufen und weiter verwendet werden?\n(Mein Design Center).';

  @override
  String get t_err84_iqd => 'Soll der vorherige Speicher aufgerufen und weiter verwendet werden?\n(IQ Designer).';

  @override
  String get t_err703_b => 'Installieren Sie \"My Stitch Monitor\", um das Sticken zu überwachen.';

  @override
  String get t_err703_t => 'Installieren Sie \"IQ Intuition Monitoring\", um das Sticken zu überwachen.';

  @override
  String get t_err704_b => 'Installieren Sie die App „My Stitch Monitor“, um den Stickvorgang auf Ihrem Smartgerät zu überwachen. \n\nSie können den Fortschritt des Stickvorgangs auf Ihrem Smartgerät überwachen. \nAußerdem können Sie sämtliche Informationen zur für den Stickvorgang verwendeten Garnfarbe überprüfen.';

  @override
  String get t_err704_t => 'Installieren Sie die App „IQ Intuition Monitoring“, um den Stickvorgang auf Ihrem Smartgerät zu überwachen. \n\nSie können den Fortschritt des Stickvorgangs auf Ihrem Smartgerät überwachen. \nAußerdem können Sie sämtliche Informationen zur für den Stickvorgang verwendeten Garnfarbe überprüfen.';

  @override
  String get t_err705_b => 'Installieren Sie die App „My Design Snap“, um Bilder von Ihrem Smartgerät an Ihre Maschine zu senden. \n\nIn „Mein Design Center“ können Sie aus den Bildern einfach Stickmuster erstellen.';

  @override
  String get t_err705_t => 'Installieren Sie die App „IQ Intuition Positioning“, um Bilder von Ihrem Smartgerät an Ihre Maschine zu senden. \n\nIn „IQ Designer“ können Sie aus den Bildern einfach Stickmuster erstellen.';

  @override
  String get t_err708 => 'Zur Verwendung dieser App ist KIT1 erforderlich.';

  @override
  String get t_err709 => 'Zur Verwendung dieser App ist KIT2 erforderlich.';

  @override
  String get t_err711 => 'Diese Stickdaten enthalten nicht ausreichend Garninformationen.\nGeben Sie auf dem Garnfarbwechsel-Bildschirm bitte die Garnfarbnummer ein, um die korrekten Garninformationen anzuzeigen.';

  @override
  String get t_err713 => 'Bitte lesen Sie vor der Verwendung die Endbenutzerlizenzvereinbarungen (EULA).';

  @override
  String get t_err715 => 'Ich akzeptiere die Bedingungen der Lizenzvereinbarung.';

  @override
  String get t_err01_heated => 'Die Sicherheitsvorrichtung wurde aktiviert, da sich der Motor der Hauptwelle erhitzt hat. Hat sich der Faden verheddert?';

  @override
  String get t_err01_motor => 'Die Sicherheitsvorrichtung wurde aktiviert, da sich der Hauptwellenmotor festgelaufen hat. Hat sich der Faden verwickelt?';

  @override
  String get t_err01_npsensor => 'Die Sicherheitsvorrichtung wurde aufgrund einer Fehlfunktion des Nadelpositionssensors aktiviert.';

  @override
  String get t_err734 => 'Für iOS';

  @override
  String get t_err735 => 'Für Android™';

  @override
  String get t_err738 => 'Die Stickreihenfolge wird geändert.';

  @override
  String get t_err739 => 'Diese Funktion kann nicht verwendet werden, wenn ein Sondermuster ausgewählt ist.';

  @override
  String get t_err740 => 'Der Stich überlappt sich mit den Stichen auf der anderen Seite. Verringern Sie die Stichbreite oder erhöhen Sie den Versatzabstand.';

  @override
  String get t_err741 => 'Die Daten wurden auf eine geeignete Größe verringert.';

  @override
  String get t_err742 => 'Möchten Sie zu den Einstellungen wechseln, um Ihre Maschine mit dem WLAN-Netzwerk zu verbinden?';

  @override
  String get t_err743 => 'Diese Funktion kann nicht ausgeführt werden, da die CanvasWorkspace-Services im Moment für Wartungszwecke abgeschaltet sind. Bitte warten Sie, bis die Wiederherstellung abgeschlossen ist.';

  @override
  String get t_err743_s => 'Diese Funktion kann nicht ausgeführt werden, da die Artspira-Services im Moment für Wartungszwecke abgeschaltet sind. Bitte warten Sie, bis die Wiederherstellung abgeschlossen ist.';

  @override
  String get t_err744 => 'Ihre Maschine konnte nicht bei CanvasWorkspace registriert werden.\nVorgang wiederholen.';

  @override
  String get t_err744_s => 'Ihre Maschine konnte nicht bei Artspira registriert werden.\nVorgang wiederholen.';

  @override
  String get t_err745 => 'Der PIN-Code ist nicht korrekt. Geben Sie den PIN-Code erneut ein.';

  @override
  String get t_err746 => 'Die Maschine ist nicht mit dem Internet verbunden.';

  @override
  String get t_err747 => 'Keine Verbindung zum Internet-Server.\nÜberprüfen Sie die Proxy-Einstellung.';

  @override
  String get t_err748 => 'Servertimeout.\nVersuchen Sie es später erneut.';

  @override
  String get t_err749 => 'Möchten Sie die Verbindung zu CanvasWorkspace trennen?';

  @override
  String get t_err749_s => 'Möchten Sie die Verbindung zu Artspira trennen?';

  @override
  String get t_err750 => 'Die Verbindung Ihrer Maschine zu CanvasWorkspace konnte nicht getrennt werden. Trennen Sie die Verbindung manuell über die Webseite.';

  @override
  String get t_err750_s => 'Die Verbindung Ihrer Maschine zu Artspira konnte nicht getrennt werden.\nTrennen Sie die Verbindung manuell über die App.';

  @override
  String get t_err751 => 'Ihre Nähmaschine muss bei CanvasWorkspace registriert werden, um Daten zu senden (PIN-Registrierung).\nMöchten Sie zum Einstellungsbildschirm wechseln?';

  @override
  String get t_err751_s => 'Ihre Nähmaschine muss bei Artspira registriert werden, um Daten zu senden (PIN-Registrierung).\nMöchten Sie zum Einstellungsbildschirm wechseln?';

  @override
  String get t_err752 => 'Ihre Maschine ist möglicherweise nicht registriert. Bitte prüfen Sie dies auf CanvasWorkspace.';

  @override
  String get t_err752_s => 'Ihre Maschine ist möglicherweise nicht registriert. Bitte prüfen Sie dies auf Artspira.';

  @override
  String get t_err753 => 'Die Daten konnten nicht hochgeladen werden.\nVorgang wiederholen.';

  @override
  String get t_err754 => 'Die Daten konnten nicht heruntergeladen werden.\nVorgang wiederholen.';

  @override
  String get t_err755 => 'Zertifizierung war erfolgreich.\nBitte die Nähmaschine neu starten.\n\nUm Daten an Ihre ScanNCut-Maschine zu senden, starten Sie die Maschine neu und registrieren Sie auf Seite 13 der Einstellungen die Maschine beim CanvasWorkspace-Server.';

  @override
  String get t_err755_s => 'Zertifizierung war erfolgreich.\nBitte die Nähmaschine neu starten.';

  @override
  String get t_err756 => 'Möchten Sie die vorhandenen Daten durch die neuen Daten ersetzen?\n* Die Daten in der temporären Datenablage werden nach einem gewissen Zeitraum automatisch gelöscht.';

  @override
  String get t_err757 => 'Möchten Sie Daten an die temporäre Datenablage auf dem Server senden?\n* Die Daten in der temporären Datenablage werden nach einem gewissen Zeitraum automatisch gelöscht.';

  @override
  String get t_err761 => 'Keine Daten in der temporären Datenablage.';

  @override
  String get t_err762 => 'In der temporären Datenablage sind keine lesbaren Daten vorhanden.';

  @override
  String get t_err763 => 'Ihre ScanNCut- und Nähmaschine muss bei CanvasWorkspace registriert werden, um die Schnittdaten zu erhalten (PIN-Registrierung).\nMöchten Sie zum Einstellungsbildschirm wechseln?';

  @override
  String get t_err763_s => 'Bitte registrieren Sie Ihre Nähmaschine, um Daten von Artspira zu erhalten. (PIN-Code-Registrierung) \nMöchten Sie zum Einstellungsbildschirm wechseln, um Ihr Gerät zu registrieren?';

  @override
  String get t_err764 => 'Installieren Sie „Artspira“ auf Ihrem Smart-Gerät. \nDurch die Erstellung eines Artspira-Kontos eröffnen sich Ihnen ganz neue Möglichkeiten des Stickens. \nScannen Sie für weitere Informationen den QR-Code.';

  @override
  String get t_err765 => 'Die Maschinenbezeichnung konnte nicht geändert werden.';

  @override
  String get t_err766 => 'Aktivieren Sie das Wireless-LAN, um die Maschinenbezeichnung zu ändern.';

  @override
  String get t_err770 => 'Konnte nicht gelöscht werden.';

  @override
  String get t_err771 => 'Wenn Sie alle eigenen Stickmuster löschen, werden die verwendeten Muster durch andere Muster ersetzt.\nMöchten Sie alle eigenen Stickmuster löschen?';

  @override
  String get t_err772 => 'Wenn Sie das importierte eigene Stickmuster nach dem Speichern löschen oder ändern, können die Daten vom Original abweichen.';

  @override
  String get t_err773 => 'Daten mit importierten benutzerdefinierten Mustern können nicht auf einem externen Speicher gespeichert werden.';

  @override
  String get t_err774 => 'Es werden nur die Stickdaten gespeichert. Bearbeitungsdaten, zu denen auch benutzerdefinierte Muster gehören, können nicht auf einem externen Speicher gespeichert werden. Bitte speichern Sie diese im internen Speicher.';

  @override
  String get t_err775 => 'Der Datenspeicher ist voll.\nWählen Sie ein eigenes Stickmuster aus, dass durch das neue Muster ersetzt werden soll.';

  @override
  String get t_err776 => 'Wenn Sie das eigene Stickmuster ersetzen, könnte es sich verändern, wenn Sie das vorhergehende verwenden. Weiter?';

  @override
  String get t_err_taper01 => 'Der Anschrägstich konnte nicht eingestellt werden. Bitte vergrößern Sie den Abstand oder den Winkel.';

  @override
  String get t_err_taper02 => 'Stellen Sie den Stich fertig ein, bevor Sie sticken.';

  @override
  String get t_err_taper03 => 'Anschrägsticheinstellung abbrechen?';

  @override
  String get t_err_taper04 => 'Aktuellen Anschrägstichstatus abbrechen?';

  @override
  String get t_err_taper05 => 'Diese Funktion kann mit dem Anschrägstich nicht verwendet werden.';

  @override
  String get t_err_tapering07 => 'Drücken Sie die Taste „Rückwärtsstich“, um mit dem Nähen angeschrägten Endes zu beginnen.';

  @override
  String get t_err_tapering08 => 'Das Anschrägen endet nach der angegebenen Wiederholungsanzahl.';

  @override
  String get t_err_tapering09 => 'Das Anschrägen endet am Endpunktaufkleber.';

  @override
  String get t_err785 => 'Achten Sie darauf, dass Ober- und Unterfaden lang genug sind, sodass das Motiv vollständig gestickt werden kann. Ist einer der beiden Fäden zu kurz, wirkt sich das negativ auf das Ergebnis aus.';

  @override
  String get t_err790 => 'Alle gespeicherten Daten, Einstellungen und Netzwerkinformationen werden auf die Standardeinstellungen zurückgesetzt. \nMöchten Sie fortfahren?';

  @override
  String get t_err791 => 'Wird gelöscht…\nSchalten Sie die Maschine nicht aus.';

  @override
  String get t_err792 => 'Die Einstellungen wurden zurückgesetzt.\nSchalten Sie die Maschine aus.';

  @override
  String get t_err_paidcont_update => 'Zur Verwendung dieser Daten müssen Sie\ndie Software der Maschine auf die\nneueste Version aktualisieren.';

  @override
  String get t_err_embcarriageevacuate => 'Tippen Sie auf OK, um den Wagen der Stickeinheit in die ursprüngliche Position zu bringen.';

  @override
  String get t_err_sr_01 => 'Stichregelmodul von der Maschine trennen.';

  @override
  String get t_err_sr_02 => 'Nähmodus wurde abgebrochen, da mehrere Sekunden lang kein Stofftransport erfolgte.';

  @override
  String get t_err_sr_03 => 'Sie können mit dem Freihandquilten/Heften unter Verwendung des Stichreglers beginnen.\n\nAchten Sie darauf, nicht zu stark am Stoff zu ziehen, da sonst die Nadel abbrechen kann.';

  @override
  String get t_err_sr_04 => 'Beginnen Sie erst mit dem Nähen, nachdem Sie einen Modus ausgewählt haben.';

  @override
  String get t_err_sr_05 => 'Die Nadel bewegt sich. Halten Sie Ihre Hand von der Nadel fern.';

  @override
  String get t_err_sr_06 => 'Das Stichreglermodul ist nicht eingesteckt. Der Bildschirm für den Stichregler wird geschlossen. \nSchließen Sie das Modul erneut an, um den Bildschirm wieder zu öffnen.';

  @override
  String get t_err_sr_08 => 'Achten Sie beim Erhöhen der Fadenspannung darauf, die Nadel nicht durch starkes Ziehen am Stoff abzubrechen.';

  @override
  String get t_err_sr_09 => 'Der Zwillingsnadelmodus kann mit dieser Funktion nicht verwendet werden.\nZiehen Sie den Stecker des Stichreglermoduls ab, schalten Sie dann den Zwillingsnadelmodus aus und versuchen Sie es erneut.';

  @override
  String get t_err_sr_10 => 'Verwenden Sie bei Heftstichen nicht den offenen Quiltnähfuß für den Stichregler. Die Nadel könnte sonst abbrechen und Sie könnten sich verletzen.';

  @override
  String get t_err_manual_01_b => 'Bedienungsanleitungen, die Sie auf Ihrem Mobilgerät oder PC lesen können, finden Sie unter XXX (URL).';

  @override
  String get t_err_manual_02_t => 'Eine Bedienungsanleitung, die Sie auf Ihrem Mobilgerät oder PC lesen können, finden Sie unter XXX (URL).';

  @override
  String get t_err_proj_emb_001 => 'Die Projektorfunktion ist aufgrund des kleinen Stickrahmens eingeschränkt. „Projektorbearbeitung mit Stift“ wird nicht unterstützt, Stickmuster werden jedoch projiziert.\n\n*Nach dem Drücken auf OK bewegt sich der Stickarm der Stickeinheit.';

  @override
  String get t_err_proj_emb_002 => 'Die Projektorfunktion ist aufgrund des kleinen Stickrahmens eingeschränkt. „Projektorbearbeitung mit Stift“ wird nicht unterstützt, Stickmuster werden jedoch projiziert.';

  @override
  String get t_err_proj_emb_003 => 'Projektor wird ausgeschaltet.';

  @override
  String get t_err_proj_emb_004 => 'Der Projektor wird ausgeschaltet, da der Stickrahmen abgenommen wurde.\n\n* Nach dem Drücken auf OK bewegt sich der Stickarm der Stickeinheit.';

  @override
  String get t_err_proj_emb_005 => 'Der Projektor wird ausgeschaltet, da der Stickrahmen abgenommen wurde.';

  @override
  String get t_err_proj_emb_006 => 'Projektor wird ausgeschaltet.\n\n* Nach dem Drücken auf OK bewegt sich der Stickarm der Stickeinheit.';

  @override
  String get t_err_proj_emb_007 => 'Aktuelle Musterauswahl abbrechen?\n\n* Nach dem Drücken auf OK bewegt sich der Stickarm der Stickeinheit.';

  @override
  String get t_err_proj_emb_008 => 'Konvertiert in das Stickmuster, Mein Design Center wird beendet. \nWeiter zum Stickeditierbildschirm?\n\n* Der Projektor wird ausgeschaltet. Der Wagen der Stickeinheit setzt sich nach dem Drücken von „OK“ in Bewegung.';

  @override
  String get t_err_proj_emb_009 => 'Konvertiert in das Stickmuster, IQ Designer wird beendet. \nWeiter zum Stickeditierbildschirm?\n\n* Der Projektor wird ausgeschaltet. Der Wagen der Stickeinheit setzt sich nach dem Drücken von „OK“ in Bewegung.';

  @override
  String get t_err_proj_emb_010 => 'Verarbeitung läuft…';

  @override
  String get t_err_proj_emb_011 => 'Wird geschlossen...';

  @override
  String get t_err_proj_emb_012 => 'Projektor wird eingeschaltet.\n\n* Nach dem Drücken auf OK bewegt sich der Stickarm der Stickeinheit.';

  @override
  String get t_err_proj_emb_013 => 'Projektor wird eingeschaltet.';

  @override
  String get t_err_proj_emb_014 => 'Diese Funktion kann nicht verwendet werden, während der Projektor in Betrieb ist.';

  @override
  String get t_err_proj_smallframe => 'Aufgrund des kleinen Stickrahmens nicht verfügbar.';

  @override
  String get t_err_mdc_import_01 => 'Achten Sie darauf, die Daten anzupassen, da sich ihre Größe beim Laden ändert.';

  @override
  String get t_err_voiceg_01 => 'Es wird auf Daten für die Sprachführung geprüft ...';

  @override
  String get t_err_voiceg_02 => 'Die Sprachführung ist bereit und die Einstellung wurde aktiviert.';

  @override
  String get t_err_photos_01 => 'Entfernen Sie die Maske vom Bild.';

  @override
  String get t_err_photos_02 => 'Bildgrößeneinstellung zurücksetzen?';

  @override
  String get t_err_photos_03 => 'Entfernung des Hintergrunds abbrechen?';

  @override
  String get t_err_photos_04 => 'Wandeln Sie es in eine Stickerei um.';

  @override
  String get t_err_photos_05 => 'Konvertiert in das Stickmuster, und Picture Play Stickfunktion wird beendet. \nWeiter zum Stickeditierbildschirm?';

  @override
  String get t_err_photos_06 => 'Bitte warten Sie einen Augenblick.\nWährend der Konvertierung wird die Wireless-LAN-Verbindung vorübergehend deaktiviert.';

  @override
  String get t_err_photos_exit => 'Picture Play Stickfunktion beenden?';

  @override
  String get t_err_font_old_new => 'Datei in das neue Datenformat konvertieren, obwohl das alte Format verwendet wird?';

  @override
  String get t_err_font_old_lomited => 'Die Bearbeitungsfunktion ist aufgrund des alten Datenformats eingeschränkt.';

  @override
  String get t_err_firstset_wlan => 'Richten Sie ein Wireless-LAN ein.\nMöchten Sie zu den Einstellungen wechseln, um Ihre Maschine mit dem Wireless-LAN-Netzwerk zu verbinden?';

  @override
  String get t_err_firstset_voiceguidance => 'Richten Sie die Sprachsteuerung ein.\nMöchten Sie zu den Einstellungen für die Sprachsteuerung wechseln?';

  @override
  String get t_err_wlan_function_01 => 'Um die Funktion zu verwenden, muss die Wireless-LAN-Einstellung der Maschine aktiviert sein und die Maschine mit einem Wireless-Netzwerk verbunden sein.\nMöchten Sie zu den Einstellungen wechseln, um Ihre Maschine mit einem Wireless-LAN-Netzwerk zu verbinden?';

  @override
  String get t_err_teachingimage => 'Die Abbildungen dienen lediglich Illustrationszwecken; einige Bilder können je nach Modell variieren.';

  @override
  String get t_err_photo_disclaimers => 'Mit der Nutzung dieser Funktion erklären Sie sich damit einverstanden, dass kein Teil des Inhalts verwendet wird:\n• für Zwecke, die gegen alle geltenden Gesetze und Vorschriften verstoßen (insbesondere rassistische, diskriminierende, hasserfüllte, pornografische oder kinderpornografische Inhalte und/oder Äußerungen, die gegen die öffentliche Ordnung oder die guten Sitten verstoßen);\n• um die Rechte einer Person auf Privatsphäre oder Öffentlichkeit zu verletzen;\n• um Urheberrechte, Marken oder andere geistige Eigentumsrechte Dritter zu verletzen;\n• um URLs oder Schlüsselwörter zu enthalten, die den Betrachter zu bösartigen Seiten führen.\nDer/die Nutzer/in akzeptiert und erkennt an, dass er/sie die alleinige Verantwortung für die verwendeten Inhalte trägt.\nEinzelheiten entnehmen Sie bitte den Nutzungshinweise.\n\nDurch die Nutzung der Inhalte bestätige ich, dass ich die Nutzungsbedingungen und Hilfslinien gelesen und vollständig verstanden habe.';

  @override
  String get t_err_framemovekey => '* Berühren Sie im Stickbildschirm die Taste zum Verschieben des Rahmens, um den Rahmen in die mittlere Position zu bringen.';

  @override
  String get speech_colorchangeinfo => 'Sticken abgeschlossen.\nLegen Sie die nächste Garnfarbe fest.';

  @override
  String get t_err_updateinfo_01 => 'Wichtige Aktualisierung verfügbar.\nAktualisieren Sie Ihre Maschine, indem Sie die Aktualisierungsdatei über [Aktualisierungsprogramm herunterladen] unter „Maschineneinstellungen“ herunterladen.';

  @override
  String get t_err_updateinfo_02 => 'Verbinden Sie Ihre Maschine mit einem Wireless-Netzwerk, um Benachrichtigungen zur aktuellen Software zu erhalten. \nDie aktuellen Softwareaktualisierungen finden Sie auch auf der Brother Support Website.';

  @override
  String get t_err_removecarriage => 'Schalten Sie die Maschine aus, bevor Sie die Stickeinheit einsetzen oder abnehmen.';

  @override
  String get t_err_filter_removed => 'Der Filter wurde gelöscht, da die Kategorie nicht unterstützt wird.';

  @override
  String get t_err_filter_cleared => 'Filter kann gelöscht werden, da die Filterfunktion in dieser Kategorie nicht anwendbar ist.';

  @override
  String get t_principal07 => '[Nähfußhebel]';

  @override
  String get t_principal07_01 => '\nStellen Sie den Nähfußhebel nach oben oder unten, um den Nähfuß anzuheben bzw. abzusenken.';

  @override
  String get t_principal07_02 => '(a) Nähfuß \n(b) Nähfußhebel';

  @override
  String get t_principal03 => '[Nähgeschwindigkeitsregler]';

  @override
  String get t_principal03_01 => '\nBenutzen Sie diesen Regler zur Einstellung der Nähgeschwindigkeit.\nSchieben Sie den Hebel nach links, wenn Sie langsamer nähen möchten.\nSchieben Sie ihn nach rechts, wenn Sie schneller nähen möchten.';

  @override
  String get t_principal03_02 => '(a) Hebel\n(b) Langsam\n(c) Schnell';

  @override
  String get t_principal12 => '[Handrad]';

  @override
  String get t_principal12_01 => 'Drehen Sie das Handrad zum Heben und Senken der Nadel in Ihre Richtung.\nDrehen Sie das Handrad immer in Ihre Richtung.';

  @override
  String get t_principal08 => '[Zubehörfach]';

  @override
  String get t_principal08_01 => 'Bewahren Sie Nähfüße und Spulen im Zubehörfach auf.\nEntfernen Sie das Zubehörfach zum Nähen zylindrischer Stoffbahnen.';

  @override
  String get t_principal10 => '[Kniehebel]';

  @override
  String get t_principal10_00 => '(a) Kniehebel';

  @override
  String get t_principal10_01 => '\nMit dem Kniehebel können Sie den Nähfuß anheben und absenken und dabei Ihre Hände am Stoff lassen.\n\n1. Richten Sie die Zungen am Kniehebel mit den Kerben in der Öffnung aus und schieben Sie den Kniehebel so weit wie möglich hinein.';

  @override
  String get t_principal10_03_00 => '(a) Nähfuß ';

  @override
  String get t_principal10_03 => '\n2. Schieben Sie den Kniehebel mit dem Knie nach rechts, um den Nähfuß anzuheben.\nLassen Sie den Kniehebel wieder los, um den Nähfuß abzusenken.';

  @override
  String get t_principal11 => '[Fußpedal]';

  @override
  String get t_principal11_00 => '\nSie können das Nähen auch mit dem Fußpedal starten und stoppen.\n\n1. Stecken Sie den Stecker des Fußpedals in die Buchse an der Nähmaschine ein.';

  @override
  String get t_principal11_02 => '2. Treten Sie langsam auf das Fußpedal, um mit dem Nähen zu beginnen.\nLassen Sie das Fußpedal los, wenn Sie mit dem Nähen aufhören wollen.\n\n*Die Einstellung des Nähgeschwindigkeitsreglers bestimmt die maximale Geschwindigkeit, die mit dem Fußpedal erreicht werden kann.';

  @override
  String get t_xv_principal11_01 => 'Für das Multifunktionsfußpedal können Sie zusätzlich zum Starten/Stoppen des Nähvorgangs noch weitere Maschinenfunktionen festlegen, wie z. B. Fadenabschneiden und Rückwärtsstiche.';

  @override
  String get t_xv_principal11_02 => '1. Richten Sie die breite Seite der Montageplatte mit der Kerbe unten am Hauptfußpedal aus und sichern Sie beides mit einer Schraube.';

  @override
  String get t_xv_principal11_03 => '2. Richten Sie die andere Seite der Montageplatte mit der Kerbe unten am Hilfspedal aus und sichern Sie beides mit einer Schraube.';

  @override
  String get t_xv_principal11_04 => '3. Stecken Sie den Stecker des Hilfspedals in die Buchse hinten am Hauptfußpedal ein.';

  @override
  String get t_xv_principal11_05 => '4. Stecken Sie den runden Stecker des Hauptfußpedals in die Fußpedalbuchse an der rechten Seite der Maschine ein.';

  @override
  String get t_xv_principal11_06 => '5. Treten Sie langsam auf das Fußpedal, um mit dem Nähen zu beginnen. Lassen Sie das Fußpedal los, wenn Sie mit dem Nähen aufhören wollen.\n\n*Die Einstellung des Nähgeschwindigkeitsreglers bestimmt die maximale Geschwindigkeit, die mit dem Fußpedal erreicht werden kann.';

  @override
  String get t_principal11_01_02 => '(a) Fußpedal\n(b) Fußpedal-Anschlussbuchse';

  @override
  String get t_principal09 => '[Transporteur-Einstellhebel]';

  @override
  String get t_principal09_01 => '\nMit dem Transporteur-Einstellhebel können Sie den unteren Transporteur absenken.';

  @override
  String get t_principal09_02 => '(a) Transporteur-Einstellhebel';

  @override
  String get t_principal_buttons_01 => '[Taste \"Nadelposition\"]';

  @override
  String get t_principal_buttons_01_01 => 'Diese Taste dient der Richtungsänderung während des Nähens und bei detaillierten Nähvorgängen in kleinen Bereichen.\nDrücken Sie diese Taste, um die Nadelposition zu heben oder zu senken.\nBei zweimaligem Drücken der Taste näht die Maschine einen einzelnen Stich.';

  @override
  String get t_principal_buttons_02 => '[Taste \"Fadenabschneider\"]';

  @override
  String get t_principal_buttons_02_01 => 'Beim Drücken dieser Taste wird nach dem Nähvorgang der überschüssige Faden abgeschnitten.';

  @override
  String get t_principal_buttons_06 => '[Taste \"Nähfußhebel\"]';

  @override
  String get t_principal_buttons_06_01 => 'Drücken Sie diese Taste, um den Nähfuß zu senken und den Stoff festzuhalten.\nDrücken Sie diese Taste erneut, um den Nähfuß wieder anzuheben.\nDurch Halten der Taste \"Nähfußhebel\" können Sie den Nähfuß an seine höchste Position heben.';

  @override
  String get t_principal_buttons_05 => '[Taste \"Automatisches Einfädeln\"]';

  @override
  String get t_principal_buttons_05_01 => 'Verwenden Sie diese Taste zum automatischen Einfädeln der Nadel.';

  @override
  String get t_principal_buttons_04 => '[Taste \"Start/Stopp\"]';

  @override
  String get t_principal_buttons_04_01 => 'Wenn Sie diese Taste drücken, näht die Maschine zuerst einige Stiche langsam und dann mit der durch den Nähgeschwindigkeitsregler eingestellten Geschwindigkeit.\nDrücken Sie diese Taste nochmals, um die Maschine zu stoppen.\nHalten Sie die Taste gedrückt, wenn Sie mit der langsamsten Geschwindigkeit der Maschine nähen wollen.\nJe nach Betriebsmodus, nimmt die Taste eine andere Farbe an.\n\nGrün: die Maschine ist betriebsbereit oder näht bereits.\nRot: die Maschine ist nicht betriebsbereit.';

  @override
  String get t_principal_buttons_03 => '[Taste \"Rückwärts\"]';

  @override
  String get t_principal_buttons_03_01 => 'Diese Taste dient dazu, Rückwärtsstiche am Anfang und Ende des Nähvorgangs zu nähen.\nBei einigen Gerad- und Zickzackstichen können langsame Rückwärtsstiche genäht werden, wenn die Taste \"Rückwärts\" heruntergedrückt gehalten wird. (Die Stiche werden in entgegengesetzter Richtung genäht.) \nBei anderen Stichen näht die Maschine Verstärkungsstiche. Wenn Sie diese Taste drücken, näht die Maschine 3 bis 5 Stiche aufeinander und hält dann automatisch an.';

  @override
  String get t_principal_buttons_07 => '[Taste \"Verstärkung/Vernähen\"]';

  @override
  String get t_principal_buttons_07_01 => 'Diese Taste dient dazu, am Anfang und Ende des Nähvorgangs Verstärkungsstiche zu nähen.\nDrücken Sie diese Taste bei Nutzstichen, dann näht die Maschine 3 bis 5 Stiche aufeinander und hält dann automatisch an.\nBei Buchstaben-/Dekorstichen näht die Maschine Verstärkungsstiche erst nach einer weiteren Stichmustereinheit.';

  @override
  String get t_basic13 => '[Einfädeln des Oberfadens]';

  @override
  String get t_basic13_01_02 => '(a) Taste \"Nähfußhebel\"\n';

  @override
  String get t_basic13_01 => 'Drücken Sie die Filmtaste, um ein Video der angezeigten Anweisungen zu sehen.\n\n1. Drücken Sie die Taste \"Nähfußhebel\", um den Nähfuß anzuheben.';

  @override
  String get t_basic13_02_00 => '(a) Taste \"Nadelposition\"';

  @override
  String get t_basic13_02 => '\n2. Drücken Sie die Taste \"Nadelposition\", um die Nadel anzuheben. ';

  @override
  String get t_basic13_03_02 => '(a) Garnrollenstift\n(b) Garnrollenkappe\n(c) Garnrolle';

  @override
  String get t_basic13_03 => '\n3. Drehen Sie den Garnrollenstift nach oben.\nSetzen Sie die Garnrolle so auf den Garnrollenstift, dass der Faden nach vorn abgewickelt wird.\nDrücken Sie die Garnrollenkappe so weit wie möglich auf den Garnrollenstift und bringen Sie den Garnrollenstift wieder in seine Ausgangsposition.';

  @override
  String get t_basic13_11_02 => '(a) Garnrollenkappe (klein)  \n(b) Garnrolle (kreuzweise aufgespulter Faden) \n(c) Abstand';

  @override
  String get t_basic13_11 => '\nWenn Sie einen sehr feinen, kreuzweise aufgespulten Faden verwenden, lassen Sie etwas Abstand zwischen der Garnrollenkappe und der Garnrolle.';

  @override
  String get t_basic13_04_02 => '(a) Fadenführungsplatte ';

  @override
  String get t_basic13_04 => '\n4. Halten Sie den Faden der Garnrolle mit beiden Händen fest und führen Sie ihn unterhalb der Fadenführungsplatte hindurch und dann an deren linken Seite nach oben.';

  @override
  String get t_basic13_05 => '5. Halten Sie die Garnrolle mit der rechten Hand fest, führen Sie das Fadenende mit der linken Hand nach unten, nach oben und dann durch die Kerben.';

  @override
  String get t_basic13_06_02 => '(a) Fadenführung der Nadelstange';

  @override
  String get t_basic13_06 => '\n6. Führen Sie den Faden hinter die Fadenführung der Nadelstange. \n';

  @override
  String get t_basic13_07 => '7. Drücken Sie die Taste \"Nähfußhebel\", um den Nähfuß abzusenken.';

  @override
  String get t_basic13_08_02 => '(a) Fadenführungsscheiben';

  @override
  String get t_basic13_08 => '\n8. Führen Sie den Faden durch die Fadenführungsscheiben. Stellen Sie sicher, dass der Faden durch die Nut in der Fadenführung führt. Der Faden sollte fest zwischen den Fadenführungsscheiben sitzen. Die Nadel kann möglicherweise nicht eingefädelt werden. \n';

  @override
  String get t_basic13_09_02 => '(b) Fadenabschneider';

  @override
  String get t_basic13_09 => '\n9. Ziehen Sie den Faden unter dem Fadenabschneider hoch, um den Faden abzuschneiden. ';

  @override
  String get t_basic13_10_02 => '(a) Taste \"Automatisches Einfädeln\"';

  @override
  String get t_basic13_10 => '\n10. Drücken Sie die Taste \"Automatisches Einfädeln\", um die Nadel automatisch von der Maschine einfädeln zu lassen. ';

  @override
  String get t_basic14 => '[Aufspulen des Unterfadens]';

  @override
  String get t_basic14_01_02 => '(a) Kerbe in der Spule\n(b) Feder an der Welle';

  @override
  String get t_basic14_00 => '\nDrücken Sie die Filmtaste, um ein Video der angezeigten Anweisungen zu sehen.\n\n1. Die Kerbe in der Spule auf die Feder an der Spulerwelle ausrichten und die Spule auf die Spulerwelle aufsetzen. ';

  @override
  String get t_basic14_02 => '\n2. Stellen Sie den zusätzlichen Garnrollenstift in die Position \"Oben\". ';

  @override
  String get t_basic14_02_02 => '(a) Zusätzlicher Garnrollenstift ';

  @override
  String get t_basic14_03 => '\n3. Die Garnrolle so auf den zusätzlichen Garnrollenstift aufsetzen, dass der Faden von der Garnrollen-Vorderseite abgewickelt wird. Danach die Garnrollenkappe so weit wie möglich auf den Garnrollenstift drücken, um die Garnrolle zu sichern. ';

  @override
  String get t_basic14_03_02 => '(a) Garnrollenkappe\n(b) Garnrollenstift\n(c) Garnrolle ';

  @override
  String get t_basic14_04 => '\n4. Den Faden um die Spannscheibe herum führen und sicherstellen, dass der Faden sich unter der Spannscheibe befindet.';

  @override
  String get t_basic14_04_02 => '(a) Fadenführung';

  @override
  String get t_basic14_05 => '\n5. Den Faden um die Spannscheibe herum führen und sicherstellen, dass der Faden sich unter der Spannscheibe befindet.';

  @override
  String get t_basic14_05_02 => '(a) Spannscheibe';

  @override
  String get t_basic14_06 => '6. Den Faden im Uhrzeigersinn 5 bis 6 Mal um die Spule wickeln.';

  @override
  String get t_basic14_07 => '(a) Führungsschlitz (mit eingebautem Messer)\n(b) Spulenträgerplatte\n\n7. Führen Sie das Fadenende durch den Schlitz in der Spulenträgerplatte, ziehen den Faden nach rechts und schneiden ihn mit dem Fadenschneider ab. ';

  @override
  String get t_basic14_08_02 => '\n8. Den Spulerschalter nach links drücken, bis er einrastet.  Auf dem LCD-Schirm wird die Anzeige zum Aufspulen angezeigt.';

  @override
  String get t_basic14_08_03 => '(a) Spulerschalter';

  @override
  String get t_basic14_08_04 => '\n* Mit dem Aufspulschieberegler können Sie die auf die Spule aufgewickelte Fadenmenge auf eine von fünf Stufen einstellen.';

  @override
  String get t_basic14_08_05 => '(a) Aufspulschieberegler\n(b) Mehr\n(c) Weniger';

  @override
  String get t_basic14_09 => '9. Die Taste \"Spulen Start/Stopp\" drücken. Der Unterfaden wird automatisch aufgespult.\n';

  @override
  String get t_basic14_10 => '10. Sie können die Aufspulgeschwindigkeit ändern, indem Sie in der Anzeige Spulen auf - oder + drücken. Drücken Sie auf \"OK\", um das Fenster der Aufspulfunktion zu minimieren.';

  @override
  String get t_basic14_101 => '11. Die Spule hört nach dem Aufspulen auf, sich zu drehen. Der Spulerschalter kehrt in seine ursprüngliche Position zurück.';

  @override
  String get t_basic14_102 => '12. Schneiden Sie den Faden mit dem Fadenschneider ab und entfernen Sie die Spule. ';

  @override
  String get t_basic14_11 => '\n*Benutzung des Garnrollenstifts \nSie können vor dem Nähen den Hauptgarnrollenstift zum Aufspulen des Unterfadens benutzen. \n\nHinweis: Dieser Stift darf während des Nähens nicht zum Aufspulen des Unterfadens benutzt werden.\n\n1. Die Kerbe in der Spule auf die Feder an der Spulerwelle ausrichten und die Spule auf die Spulerwelle aufsetzen. ';

  @override
  String get t_basic14_11_02 => '(a) Kerbe in der Spule\n(b) Feder an der Welle';

  @override
  String get t_basic14_12 => '\n2. Drehen Sie den Garnrollenstift nach oben.\nSetzen Sie die Garnrolle so auf den Garnrollenstift, dass der Faden nach vorn abgewickelt wird.\nDrücken Sie die Garnrollenkappe so weit wie möglich auf den Garnrollenstift und bringen Sie den Garnrollenstift wieder in seine Ausgangsposition. ';

  @override
  String get t_basic14_12_02 => '(a) Garnrollenstift\n(b) Garnrollenkappe\n(c) Garnrolle ';

  @override
  String get t_basic14_13 => '\n3. Halten Sie den Faden mit der Hand fest und ziehen Sie ihn in die Nuten an der Fadenführungsplatte.\nFühren Sie den Faden um die Fadenführung herum. ';

  @override
  String get t_basic14_13_02 => '(a) Fadenführungsplattel\n(b) Fadenführung ';

  @override
  String get t_basic14_14 => '\n4. Den Faden um die Vorspannung herum führen und sicherstellen, dass der Faden sich unter der Spannungsscheibe befindet.';

  @override
  String get t_basic14_15_02 => '(a) Spannscheibe';

  @override
  String get t_basic14_16 => '5. Den Faden im Uhrzeigersinn 5 bis 6 Mal um die Spule wickeln.';

  @override
  String get t_basic14_17 => '(a) Führungsschlitz (mit eingebautem Messer)\n(b) Spulenträgerplatte\n\n6. Führen Sie das Fadenende durch den Schlitz in der Spulenträgerplatte, ziehen den Faden nach rechts und schneiden ihn mit dem Fadenschneider ab. ';

  @override
  String get t_basic14_18 => '\n7. Den Spulerschalter nach links drücken, bis er einrastet.  Auf dem LCD-Schirm wird die Anzeige zum Aufspulen angezeigt.';

  @override
  String get t_basic14_18_02 => '(a) Spulerschalter';

  @override
  String get t_basic14_20 => '8. Die Taste \"Spulen Start/Stopp\" drücken. Der Unterfaden wird automatisch aufgespult.\n';

  @override
  String get t_basic14_201 => '9. Sie können die Aufspulgeschwindigkeit ändern, indem Sie in der Anzeige Spulen auf - oder + drücken. Drücken Sie auf \"OK\", um das Fenster der Aufspulfunktion zu minimieren.';

  @override
  String get t_basic14_202 => '10. Die Spule hört nach dem Aufspulen auf, sich zu drehen. Der Spulerschalter kehrt in seine ursprüngliche Position zurück.';

  @override
  String get t_basic14_203 => '11. Schneiden Sie den Faden mit dem Fadenschneider ab und entfernen Sie die Spule. ';

  @override
  String get t_basic14_21_02 => '\nWenn Sie einen sehr feinen, kreuzweise aufgespulten Faden verwenden, lassen Sie etwas Abstand zwischen der Garnrollenkappe und der Garnrolle. ';

  @override
  String get t_basic14_21_03 => '(a) Garnrollenkappe (klein)\n(b) Garnrolle (kreuzweise aufgespulter Faden) \n(c) Abstand ';

  @override
  String get t_basic15 => '[Auswechseln der Nadel]';

  @override
  String get t_basic15_00 => '\nRichtiges Überprüfen der Nadel:\nUm die Nadel zu überprüfen, legen Sie sie mit der abgeflachten Seite auf eine plane Oberfläche.\nÜberprüfen Sie die Nadel von oben und an allen Seiten.\nVerbogene Nadeln entsorgen und keinesfalls verwenden.';

  @override
  String get t_basic15_00_01 => '(a) Parallelabstand\n(b) Plane Oberfläche (Spulenfachabdeckung, Glas, usw.)';

  @override
  String get t_basic15_01 => '1. Drücken Sie die Taste \"Nadelposition\", um die Nadel anzuheben.';

  @override
  String get t_basic15_02 => '2. Drücken Sie die Taste \"Nähfuß-/Nadelwechsel\".';

  @override
  String get t_basic15_03 => '3. Lösen Sie die Schraube an der Vorderseite der Maschine mit Hilfe eines Schraubendrehers und ziehen Sie die Nadel nach unten heraus.';

  @override
  String get t_basic15_04 => '\n4. Stecken Sie die Nadel mit der abgeflachten Seite nach hinten bis zum Nadelanschlag (Guckfenster) in die Nadelklemme hinein.\nZiehen Sie die Schraube mit Hilfe eines Schraubendrehers fest an.';

  @override
  String get t_basic15_04_02 => '(a) Nadelanschlag\n(b) Nadeleinführloch\n(c) Abgeflachte Seite der Nadel';

  @override
  String get t_basic15_05 => '5. Drücken Sie die Taste \"Nähfuß-/Nadelwechsel\", um alle Betriebstasten zu entriegeln.';

  @override
  String get t_basic16 => '[Auswechseln des Nähfußes]';

  @override
  String get t_basic16_01 => '*Abnehmen des Nähfußes\n\n1. Drücken Sie die Taste \"Nadelposition\", um die Nadel anzuheben.';

  @override
  String get t_basic16_02 => '2. Drücken Sie die Taste \"Nähfuß-/Nadelwechsel\".';

  @override
  String get t_basic16_03 => '\n3. Heben Sie den Nähfußhebel an.';

  @override
  String get t_basic16_03_02 => '(a) Nähfuß \n(b) Nähfußhebel';

  @override
  String get t_basic16_04 => '\n4. Drücken Sie zum Lösen des Nähfußes den schwarzen Knopf an der Rückseite des Nähfußhalters.';

  @override
  String get t_basic16_04_02 => '(a) Schwarzer Knopf\n(b) Nähfußhalter';

  @override
  String get t_basic16_05 => '\n*Anbringen des Nähfußes\n\n1. Setzen Sie den neuen Nähfuß so unter den Halter, dass der Nähfußstift mit dem Halterschlitz ausgerichtet ist.\nStellen Sie den Nähfußhebel nach unten, damit der Nähfußstift in die Kerbe im Nähfußhalter einrasten kann.';

  @override
  String get t_basic16_05_02 => '(a) Kerbe\n(b) Stift';

  @override
  String get t_basic16_06 => '2. Drücken Sie die Taste \"Nähfuß-/Nadelwechsel\", um alle Betriebstasten zu entriegeln.';

  @override
  String get t_basic16_07 => '(a) Nähfuß \n(b) Nähfußhebel\n\n3. Heben Sie den Nähfußhebel an.';

  @override
  String get t_basic17 => '[Einsetzen der Spule]';

  @override
  String get t_basic17_01 => 'Drücken Sie die Filmtaste, um ein Video der angezeigten Anweisungen zu sehen.\n\n1. Drücken Sie die Taste \"Nähfußhebel\", um den Nähfuß anzuheben.';

  @override
  String get t_basic17_02 => '(a) Spulenabdeckung\n(b) Verriegelung\n\n2. Schieben Sie die Verriegelung der Spulenfachabdeckung nach rechts. Das Spulenfach wird geöffnet.\nNehmen Sie die Spulenfachabdeckung ab.';

  @override
  String get t_basic17_03 => '3. Halten Sie die Spule mit der rechten Hand und das Ende des Fadens mit der linken.';

  @override
  String get t_basic17_04 => '4. Setzen Sie die Spule in die Kapsel ein, so dass sich der Faden nach links abwickelt.\nDrücken Sie die Spule mit der rechten Hand leicht nach unten und führen Sie mit der linken den Faden wie gezeigt.';

  @override
  String get t_basic17_05 => '\n5. Führen Sie den Faden durch die Führung und ziehen Sie ihn dann nach vorne heraus.\nDer Faden wird durch den Fadenabschneider abgeschnitten.';

  @override
  String get t_basic17_05_02 => '(a) Fadenabschneider';

  @override
  String get t_basic17_06 => '6. Setzen Sie die Lasche in die linke untere Ecke der Spulenfachabdeckung ein und drücken Sie dann leicht auf die rechte Seite, um die Abdeckung zu schließen.';

  @override
  String get t_embbasic17 => '[Anbringen von Aufbügelvlies (Unterlegmaterial) am Stoff]';

  @override
  String get t_embbasic17_00 => 'Um ein optimales Ergebnis zu erzielen, benutzen Sie zum Sticken immer ein Stickunterlegvlies.\nFolgen Sie den Anweisungen der Packungsbeilage des Unterlegmaterials.';

  @override
  String get t_embbasic17_01 => '\n1. Verwenden Sie ein Stück Unterlegmaterial, das größer ist als der Stickrahmen.';

  @override
  String get t_embbasic17_01_02 => '(a) Größe des Stickrahmens\n(b) Aufbügelvlies (Unterlegmaterial) ';

  @override
  String get t_embbasic17_02 => '\n2. Bügeln Sie das Stickunterlegvlies auf die linke Stoffseite.\n\n*Bei Stoffen, die nicht gebügelt werden können (wie Frottee oder Stoffe mit Schlingen, die sich beim Bügeln vergrößern) oder an Orten ohne Bügelmöglichkeit, legen Sie das Stickunterlegvlies unter den Stoff, ohne es zu befestigen, und spannen beides in den Stickrahmen. Andererseits können Sie sich jedoch auch in Ihrem Nähmaschinen-Fachgeschäft über die richtige Auswahl des Unterlegmaterials informieren.';

  @override
  String get t_embbasic17_02_02 => '(a) Verbindungsseite des Unterlegmaterials\n(b) Stoff (linke Seite) ';

  @override
  String get t_embbasic17_03 => '\n*Benutzen Sie zum Besticken von dünnen Stoffen wie Organdy und Batist und von florigen Stoffen wie Frottee und Kord ein wasserlösliches Unterlegmaterial (getrennt erhältlich) , um ein optimales Ergebnis zu erzielen.\nDas wasserlösliche Stickunterlegvlies löst sich in Wasser vollständig auf und gibt Ihrer Stickerei so ein schöneres Aussehen.';

  @override
  String get t_embbasic18 => '[Einspannen des Stoffes]';

  @override
  String get t_embbasic18_01 => '1. Heben und lösen Sie die Einstellschraube des äußeren Stickrahmens und nehmen Sie den inneren Rahmen heraus.';

  @override
  String get t_embbasic18_02 => '\n2. Legen Sie den Stoff mit der rechten Seite nach oben auf den Außenrahmen.\nSetzen Sie den inneren Rahmen wieder ein und achten Sie dabei darauf, dass die Markierung Δ des inneren Rahmens mit der Markierung Δ des Außenrahmens ausgerichtet ist.';

  @override
  String get t_embbasic18_02_02 => '(a) Innenrahmen\n(b) Außenrahmen\n(c) Einstellschraube';

  @override
  String get t_embbasic18_03 => '3. Ziehen Sie die Einstellschraube leicht an und achten Sie darauf, dass der Stoff stramm eingespannt ist, indem Sie ihn an den Ecken und Kanten fest in den Rahmen ziehen.\nDie Schraube nicht wieder lösen.';

  @override
  String get t_embbasic18_04 => '\n4. Ziehen Sie den Stoff vorsichtig stramm und drehen Sie die Rahmeneinstellschraube fest, damit sich der Stoff nach dem Dehnen nicht wieder lockern kann.\n\n* Stellen Sie sicher, dass der Stoff nach dem Dehnen fest eingespannt ist.\n\n* Achten Sie darauf, dass sich der innere und der äußere Rahmen auf gleicher Höhe befinden, bevor Sie mit dem Sticken beginnen.\n\n*Hinweis\nStraffen Sie den Stoff an allen vier Ecken und allen vier Kanten. Während der Stoff gestrafft wird, die Rahmeneinstellschraube anziehen.';

  @override
  String get t_embbasic18_04_02 => '(a) Außenrahmen\n(b) Innenrahmen\n(c) Stoff';

  @override
  String get t_embbasic18_04_11 => '*Bei Verwendung eines Stickrahmens mit angebrachtem Hebel.\n';

  @override
  String get t_embbasic18_04_12 => '\n1. Bewegen Sie den Hebel nach unten.';

  @override
  String get t_embbasic18_04_13 => '2. Lösen Sie die Feststellschraube des Rahmens per Hand und entfernen Sie dann den inneren Rahmen.';

  @override
  String get t_embbasic18_04_14 => '\n3. Bringen Sie den Stoff in Position.';

  @override
  String get t_embbasic18_04_15 => '4. Ziehen Sie die Feststellschraube des Rahmens per Hand an.';

  @override
  String get t_embbasic18_04_16 => '5. Bringen Sie den Hebel wieder in seine ursprüngliche Position.';

  @override
  String get t_embbasic18_05 => '\n[Benutzung der Stickschablone]';

  @override
  String get t_embbasic18_05_01 => 'Wenn Sie das Muster an einer bestimmten Stelle sticken möchten, benutzen Sie mit dem Stickrahmen die Stickschablone.\n\n1. Markieren Sie mit Kreide die Mitte des Stickmusters auf dem Stoff.';

  @override
  String get t_embbasic18_05_02 => '(a) Stickmuster\n(b) Markierung';

  @override
  String get t_embbasic18_06 => '\n2. Legen Sie die Stickschablone auf den inneren Rahmen. Richten Sie die Linien auf der Stickschablone mit den Stoffmarkierungen aus.';

  @override
  String get t_embbasic18_06_02 => '(a) Innenrahmen\n(b) Linie';

  @override
  String get t_embbasic18_07 => '\n3. Straffen Sie den Stoff vorsichtig, bis er keine Falten mehr wirft, und setzen Sie den Innenrahmen in den Außenrahmen hinein.';

  @override
  String get t_embbasic18_07_02 => '(a) Innenrahmen\n(b) Außenrahmen';

  @override
  String get t_embbasic18_08 => '4. Entfernen Sie die Stickschablone.';

  @override
  String get t_embbasic19 => '[Einsetzen des Stickrahmens]';

  @override
  String get t_embbasic19_01 => '*Spulen Sie den Unterfaden auf die Spule und setzen Sie diese ein, bevor Sie den Stickrahmen einsetzen.\n\n1. Drücken Sie die Taste \"Nähfußhebel\", um den Nähfuß anzuheben.';

  @override
  String get t_embbasic19_02 => '\n2. Heben Sie den Rahmensicherungshebel an.';

  @override
  String get t_embbasic19_03 => '\n3. Richten Sie die Stickrahmenführung mit der rechten Kante des Stickrahmenhalters aus.';

  @override
  String get t_embbasic19_03_02 => '(a) Stickrahmenhalter\n(b) Stickrahmenführung';

  @override
  String get t_embbasic19_04 => '4. Schieben Sie den Stickrahmen in den Halter. Achten Sie dabei darauf, die Markierung Δ des Stickrahmens mit der Markierung Δ des Halters auszurichten.';

  @override
  String get t_embbasic19_05 => '\n5. Senken Sie den Rahmensicherungshebel, um den Stickrahmen im Stickrahmenhalter zu sichern.\n\n* Bleibt der Rahmensicherungshebel oben, wird das Stickmuster möglicherweise nicht einwandfrei genäht oder der Nähfuß trifft auf den Stickrahmen und verursacht Verletzungen.';

  @override
  String get t_embbasic19_05_02 => '(a) Rahmensicherungshebel';

  @override
  String get t_embbasic19_06 => '\n[Abnehmen des Stickrahmens]\n\n1. Heben Sie den Rahmensicherungshebel an.';

  @override
  String get t_embbasic19_07 => '2. Ziehen Sie den Stickrahmen zu sich heran.';

  @override
  String get t_embbasic20 => '[Anbringen der Stickeinheit]';

  @override
  String get t_embbasic20_01 => 'Bevor Sie die Nähmaschine ausschalten, lesen Sie die nachfolgend beschriebenen Schritte.\n\n1. Schalten Sie den Netzschalter AUS und entfernen Sie das Zubehörfach (falls eingesetzt).';

  @override
  String get t_embbasic20_03 => '\n2. Stecken Sie den Stecker der Stickeinheit in die Anschlussbuchse der Maschine. Drücken Sie das Gerät leicht an, bis es einrastet.';

  @override
  String get t_embbasic20_03_02 => '(a) Anschluss für Stickeinheit\n(b) Anschlussbuchse der Maschine';

  @override
  String get t_embbasic20_04 => '(a) AUS\n(b) EIN\n\n3. Schalten Sie den Netzschalter EIN.';

  @override
  String get t_embbasic20_05 => '4. Drücken Sie die Taste \"OK\". Der Wagen bewegt sich in die Initialisierungsposition.';

  @override
  String get t_embbasic20_06 => '[Abnehmen der Stickeinheit]';

  @override
  String get t_embbasic20_06_02 => '\n(a) AUS\n(b) EIN\n\nBevor Sie die Nähmaschine ausschalten, lesen Sie die nachfolgend beschriebenen Schritte.\n\n1. Schalten Sie den Netzschalter AUS.';

  @override
  String get t_embbasic20_07 => '(a) Freigabeknopf (unter der Stickeinheit)\n\n2. Drücken Sie den Freigabeknopf und ziehen Sie die Stickeinheit von der Nähmaschine ab.';

  @override
  String get t_xp_embbasic21 => '[Anbringen des Stickfußes \"W\"]';

  @override
  String get t_xp_embbasic21_01 => '1. Drücken Sie die Taste \"Nadelposition\", um die Nadel anzuheben.';

  @override
  String get t_xp_embbasic21_02 => '2. Drücken Sie die Taste \"Nähfuß-/Nadelwechsel\".';

  @override
  String get t_xp_embbasic21_03 => '\n3. Heben Sie den Nähfußhebel an.';

  @override
  String get t_xp_embbasic21_04 => '\n4. Drücken Sie zum Lösen des Nähfußes den schwarzen Knopf, der sich an der Rückseite des Nähfußhalters befindet.';

  @override
  String get t_xp_embbasic21_04_02 => '(a) Schwarzer Knopf\n(b) Nähfußhalter';

  @override
  String get t_xp_embbasic21_05 => '\n5. Lösen Sie mit dem mitgelieferten Schraubendreher die Schraube des Nähfußhalters und nehmen Sie den Nähfußhalter ab.';

  @override
  String get t_xp_embbasic21_05_02 => '(a) Schraubendreher\n(b) Nähfußhalter\n(c) Nähfußhalterschraube';

  @override
  String get t_xp_embbasic21_06 => '(a) Nähfußhebel\n\n6. Stellen Sie den Nähfußhebel nach unten.';

  @override
  String get t_xp_embbasic21_07_01 => '(a) Nähfußstange\n';

  @override
  String get t_xp_embbasic21_07_02 => '7. Setzen Sie den Stickfuß \"W\" von hinten an die Nähfußstange.';

  @override
  String get t_xp_embbasic21_08_01 => '(a) Nähfußhalterschraube\n';

  @override
  String get t_xp_embbasic21_08_02 => '8. Halten Sie den Stickfuß mit der rechten Hand fest und ziehen Sie mit der linken Hand die Nähfußhalterschraube mit dem beiliegenden Schraubendreher fest.';

  @override
  String get t_xp_embbasic21_09 => '9. Drücken Sie die Taste \"Nähfuß-/Nadelwechsel\", um alle Betriebstasten zu entriegeln.';

  @override
  String get t_embbasic21 => '[Anbringen des Stickfußes \"W\"]';

  @override
  String get t_embbasic21_01 => '1. Drücken Sie die Taste \"Nadelposition\", um die Nadel anzuheben.';

  @override
  String get t_embbasic21_02 => '2. Drücken Sie die Taste \"Nähfuß-/Nadelwechsel\".';

  @override
  String get t_embbasic21_03 => '\n3. Heben Sie den Nähfußhebel an.';

  @override
  String get t_embbasic21_04 => '\n4. Drücken Sie zum Lösen des Nähfußes den schwarzen Knopf, der sich an der Rückseite des Nähfußhalters befindet.';

  @override
  String get t_embbasic21_04_02 => '(a) Schwarzer Knopf\n(b) Nähfußhalter';

  @override
  String get t_embbasic21_05 => '\n5. Lösen Sie mit dem mitgelieferten Schraubendreher die Schraube des Nähfußhalters und nehmen Sie den Nähfußhalter ab.';

  @override
  String get t_embbasic21_05_02 => '(a) Schraubendreher\n(b) Nähfußhalter\n(c) Nähfußhalterschraube';

  @override
  String get t_embbasic21_06 => '(a) Nähfußhebel\n\n6. Stellen Sie den Nähfußhebel nach unten.';

  @override
  String get t_embbasic21_07 => '(a) Arm\n(b) Nadelklemmschraube\n(c) Nähfußhalterschraube\n(d) Wischer\n\n7. Setzen Sie den Stickfuß \"W\" so an die Nähfußstange, dass der Arm des Stickfußes \"W\" die Rückseite des Nadelhalters berührt.';

  @override
  String get t_embbasic21_08 => '8. Schrauben Sie mit dem mitgelieferten Schraubendreher die Schraube des Nähfußhalters fest.';

  @override
  String get t_embbasic21_09 => '9. Drücken Sie die Taste \"Nähfuß-/Nadelwechsel\", um alle Betriebstasten zu entriegeln.';

  @override
  String get t_embbasic21_emb_07 => '(a) Arm\n(b) Nadelklemmschraube\n(c) Nähfußhalterschraube\n(d) Wischer\n\n3. Setzen Sie den Stickfuß \"W\" so an die Nähfußstange, dass der Arm des Stickfußes \"W\" die Rückseite des Nadelhalters berührt.';

  @override
  String get t_embbasic21_emb_08 => '4. Schrauben Sie mit dem mitgelieferten Schraubendreher die Schraube des Nähfußhalters fest.';

  @override
  String get t_embbasic21_emb_09 => '5. Drücken Sie die Taste \"Nähfuß-/Nadelwechsel\", um alle Betriebstasten zu entriegeln.';

  @override
  String get t_xv_embbasic21 => '[Anbringen des Stickfußes \"W+\"]';

  @override
  String get t_xv_embbasic21_05 => '\n5. Lösen Sie mit dem mitgelieferten Schraubendreher die Schraube des Nähfußhalters und nehmen Sie den Nähfußhalter ab.';

  @override
  String get t_xv_embbasic21_07_01 => '(a) Nähfußstange\n';

  @override
  String get t_xv_embbasic21_07_02 => '7. Setzen Sie den Stickfuß \"W+\" von hinten an die Nähfußstange.';

  @override
  String get t_xv_embbasic21_08_01 => '(a) Nähfußhalterschraube\n';

  @override
  String get t_xv_embbasic21_08_02 => '8. Halten Sie den Stickfuß mit der rechten Hand fest und ziehen Sie mit der linken Hand die Nähfußhalterschraube mit dem beiliegenden Schraubendreher fest.';

  @override
  String get t_xv_embbasic21_09 => '9. Stecken Sie den Stecker des Stickfußes \"W+\" mit LED-Zeiger in die Buchse links an der Maschine ein.';

  @override
  String get t_xv_embbasic21_10 => '10. Drücken Sie die Taste \"Nähfuß-/Nadelwechsel\", um alle Betriebstasten zu entriegeln.';

  @override
  String get t_embbasic22 => '[Richtiger Einsatz von Unterlegmaterial]';

  @override
  String get t_embbasic22_00_01 => '1. Stoffe, die gebügelt werden können';

  @override
  String get t_embbasic22_00_02 => '2. Stoffe, die nicht gebügelt werden können';

  @override
  String get t_embbasic22_00_03 => '3. Dünne Stoffe';

  @override
  String get t_embbasic22_00_04 => '4. Florige Stoffe';

  @override
  String get t_embbasic22_00_05 => '\nUm ein optimales Ergebnis zu erzielen, benutzen Sie zum Sticken immer ein Stickunterlegvlies. Folgen Sie den Anweisungen der Packungsbeilage des Unterlegmaterials.';

  @override
  String get t_embbasic22_01 => '\n[1. Stoffe, die gebügelt werden können]';

  @override
  String get t_embbasic22_01_02 => '\nBügeln Sie das Stickunterlegvlies auf die linke Stoffseite. Verwenden Sie ein Stück Unterlegmaterial, das größer ist als der Stickrahmen.';

  @override
  String get t_embbasic22_01_03 => '(a) Größe des Stickrahmens\n(b) Aufbügelvlies (Unterlegmaterial) ';

  @override
  String get t_embbasic22_02 => '[2. Stoffe, die nicht gebügelt werden können]';

  @override
  String get t_embbasic22_02_02 => '\nLegen Sie das Stickunterlegvlies unter den Stoff ohne es zu befestigen und positionieren Sie dann Stoff und Unterlegvlies zusammen im Stickrahmen.';

  @override
  String get t_embbasic22_03 => '[3. Dünne Stoffe]';

  @override
  String get t_embbasic22_03_02 => '\nVerwenden Sie ein wasserlösliches Stickunterlegvlies (nicht im Lieferumfang enthalten) für beste Ergebnisse. Das wasserlösliche Stickunterlegvlies löst sich in Wasser vollständig auf und gibt Ihrer Stickerei so ein schöneres Aussehen.';

  @override
  String get t_embbasic22_04 => '[4. Florige Stoffe]';

  @override
  String get t_embbasic22_04_02 => '\nBei Stoffen, die nicht gebügelt werden können (wie Frottee oder Stoffe mit Schlingen, die sich beim Bügeln vergrößern) , legen Sie das Stickunterlegvlies unter den Stoff, ohne es zu befestigen, und spannen beides in den Stickrahmen, oder verwenden Sie ein wasserlösliches Stickunterlegvlies (nicht im Lieferumfang enthalten) .';

  @override
  String get t_embbasic23 => '[Einstellung der Fadenspannung]';

  @override
  String get t_embbasic23_01 => 'Beim Sticken sollte die Oberfadenspannung so eingestellt werden, dass der Oberfaden an der Unterseite des Stoffes leicht sichtbar ist.';

  @override
  String get t_embbasic23_01_01 => '1. Richtige Fadenspannung';

  @override
  String get t_embbasic23_01_02 => '2. Oberfaden ist zu stramm';

  @override
  String get t_embbasic23_01_03 => '3. Oberfaden ist zu locker';

  @override
  String get t_embbasic23_02 => '[1. Richtige Fadenspannung]';

  @override
  String get t_embbasic23_02_02 => '\nDas Muster ist auf der Rükseite des Stoffes sichtbar. Wenn die Oberfadenspannung nicht richtig eingestellt ist, wird das Muster nicht sauber gestickt. Der Stoff wirft unter Umständen Falten oder der Faden reißt.';

  @override
  String get t_embbasic23_03 => '[2. Oberfaden ist zu stramm]';

  @override
  String get t_embbasic23_03_02 => '\nDie Spannung des Oberfadens ist zu stramm und bewirkt, dass der Unterfaden durch die rechte Seite des Stoffes sichtbar ist.';

  @override
  String get t_embbasic23_03_03 => 'Drücken Sie  - , um die Fadenspannung zu verringern.';

  @override
  String get t_embbasic23_04 => '[3. Oberfaden ist zu locker]';

  @override
  String get t_embbasic23_04_02 => '\nDie Spannung des Oberfadens ist zu gering und verursacht einen zu lockeren Unterfaden. Auf der rechten Stoffseite sind lose Fäden oder Schlaufen.';

  @override
  String get t_embbasic23_04_03 => 'Drücken Sie + , um die Fadenspannung zu erhöhen.';

  @override
  String get t_trouble22 => '[Oberfaden reißt]';

  @override
  String get t_trouble22_01 => '*Ursache 1\nMaschine ist nicht richtig eingefädelt (falsche Garnrollenkappe, Garnrollenkappe ist lose, der Nadeleinfädler hat den Faden nicht gegriffen, usw.) .\n\n*Lösung\nFädeln Sie die Maschine richtig ein.';

  @override
  String get t_trouble22_02 => '*Ursache 2\nFaden hat sich verknotet oder verhaspelt.\n\n*Lösung\nFädeln Sie Ober- und Unterfaden erneut ein.';

  @override
  String get t_trouble22_03 => '*Ursache 3\nFaden ist zu dick für die Nadel.\n\n*Lösung\nÜberprüfen Sie Nadel- und Fadenkombinationen.';

  @override
  String get t_trouble22_04 => '*Ursache 4\nOberfadenspannung ist zu stark.\n\n*Lösung\nRegulieren Sie die Fadenspannung.';

  @override
  String get t_trouble22_05 => '*Ursache 5\nFaden ist verwickelt.\n\n*Lösung\nSchneiden Sie den verwickelten Faden mit einer Schere o. Ä. durch und ziehen Sie ihn aus dem Greifer.';

  @override
  String get t_trouble22_06 => '*Ursache 6\nNadel ist verbogen oder die Spitze ist abgenutzt.\n\n*Lösung\nSetzen Sie eine neue Nadel ein.';

  @override
  String get t_trouble22_07 => '*Ursache 7\nNadel ist nicht richtig eingesetzt.\n\n*Lösung\nSetzen Sie die Nadel richtig ein.';

  @override
  String get t_trouble22_08 => '*Ursache 8\nDie Stichplatte ist in der Nähe des Loches verkratzt.\n\n*Lösung\nErsetzen Sie die Stichplatte oder wenden Sie sich an den nächsten Fachhändler.';

  @override
  String get t_trouble22_09 => '*Ursache 9\nDer Nähfuß ist in der Nähe des Loches verkratzt.\n\n*Lösung\nErsetzen Sie den Nähfuß oder wenden Sie sich an den nächsten Fachhändler.';

  @override
  String get t_trouble22_10 => '*Ursache 10\nDer Greifer ist verkratzt.\n\n*Lösung\nErsetzen Sie den Greifer oder wenden Sie sich an den nächsten Fachhändler.';

  @override
  String get t_trouble23 => '[Unterfaden reißt]';

  @override
  String get t_trouble23_01 => '*Ursache 1\nSpule ist falsch eingesetzt.\n\n*Lösung\nFädeln Sie den Unterfaden richtig ein.';

  @override
  String get t_trouble23_02 => '*Ursache 2\nDie Spule ist verkratzt oder sie dreht sich nicht richtig.\n\n*Lösung\nErsetzen Sie die Spule.';

  @override
  String get t_trouble23_03 => '*Ursache 3\nFaden ist verwickelt.\n\n*Lösung\nSchneiden Sie den verwickelten Faden mit einer Schere o.Ä. durch und ziehen Sie ihn aus dem Greifer.';

  @override
  String get t_trouble24 => '[Übersprungene Stiche]';

  @override
  String get t_trouble24_01 => '*Ursache 1\nDie Maschine ist nicht richtig eingefädelt.\n\n*Lösung\nFädeln Sie die Maschine unter Beachtung der einzelnen Schritte erneut ein.';

  @override
  String get t_trouble24_02 => '*Ursache 2\nNadel und Faden entsprechen nicht dem ausgewählten Stoff.\n\n*Lösung\nÜberprüfen Sie die Tabelle \"Stoff-/Faden-/ Nadelkombinationen\".';

  @override
  String get t_trouble24_03 => '*Ursache 3\nNadel ist verbogen oder die Spitze ist abgenutzt.\n\n*Lösung\nSetzen Sie eine neue Nadel ein.';

  @override
  String get t_trouble24_04 => '*Ursache 4\nNadel ist nicht richtig eingesetzt.\n\n*Lösung\nSetzen Sie die Nadel richtig ein.';

  @override
  String get t_trouble24_05 => '*Ursache 5\nNadel ist defekt.\n\n*Lösung\nSetzen Sie eine neue Nadel ein.';

  @override
  String get t_trouble24_06 => '*Ursache 6\nStaub und Fusseln haben sich unter der Stichplatte angesammelt.\n\n*Lösung\nEntfernen Sie Staub und Fusseln mit einem Pinsel.';

  @override
  String get t_trouble25 => '[Stoff wirft Falten]';

  @override
  String get t_trouble25_01 => '*Ursache 1\nOber- oder Unterfaden wurden falsch eingefädelt.\n\n*Lösung\nFädeln Sie die Maschine unter Beachtung der einzelnen Schritte erneut ein.';

  @override
  String get t_trouble25_02 => '*Ursache 2\nGarnrollenkappe wurde nicht richtig aufgesetzt.\n\n*Lösung\nÜberprüfen Sie, wie die Garnrollenkappe richtig angebracht wird, und bringen Sie sie dann richtig wieder an.';

  @override
  String get t_trouble25_03 => '*Ursache 3\nNadel und Faden entsprechen nicht dem ausgewählten Stoff.\n\n*Lösung\nÜberprüfen Sie die Tabelle \"Stoff-/Faden-/ Nadelkombinationen\".';

  @override
  String get t_trouble25_04 => '*Ursache 4\nNadel ist verbogen oder die Spitze ist abgenutzt.\n\n*Lösung\nSetzen Sie eine neue Nadel ein.';

  @override
  String get t_trouble25_05 => '*Ursache 5\nStiche sind beim Nähen auf dünnen Stoffen zu lang.\n\n*Lösung\nVerwenden Sie eine kürzere Stichlänge.';

  @override
  String get t_trouble25_06 => '*Ursache 6\nFadenspannung ist falsch eingestellt.\n\n*Lösung\nRegulieren Sie die Fadenspannung.';

  @override
  String get t_trouble25_07 => '*Ursache 7\nFalscher Nähfuß.\n\n*Lösung\nSetzen Sie den richtigen Nähfuß ein.';

  @override
  String get t_trouble26 => '[Die Maschine ist laut]';

  @override
  String get t_trouble26_01 => '*Ursache 1\nStaub und Fusseln haben sich im Transporteur festgesetzt.\n\n*Lösung\nEntfernen Sie Staub und Fusseln.';

  @override
  String get t_trouble26_02 => '*Ursache 2\nFäden haben sich im Greifer verfangen.\n\n*Lösung\nReinigen Sie den Greifer.';

  @override
  String get t_trouble26_03 => '*Ursache 3\nOberfaden ist nicht richtig eingefädelt.\n\n*Lösung\nFädeln Sie die Maschine unter Beachtung der einzelnen Schritte erneut ein.';

  @override
  String get t_trouble26_04 => '*Ursache 4\nDer Greifer ist verkratzt.\n\n*Lösung\nErsetzen Sie den Greifer oder wenden Sie sich an den nächsten Fachhändler.';

  @override
  String get t_trouble27 => '[Nadeleinfädler kann nicht verwendet werden]';

  @override
  String get t_trouble27_01 => '*Ursache 1\nNadel ist nicht in der richtigen Position.\n\n*Lösung\nDrücken Sie die Taste \"Nadelposition\", um die Nadel anzuheben.';

  @override
  String get t_trouble27_02 => '*Ursache 2\nEinfädlerhaken greift nicht durch das Nadelöhr.\n\n*Lösung\nDrücken Sie die Taste \"Nadelposition\", um die Nadel anzuheben.';

  @override
  String get t_trouble27_03 => '*Ursache 3\nNadel ist nicht richtig eingesetzt.\n\n*Lösung\nSetzen Sie die Nadel richtig ein.';

  @override
  String get t_trouble28 => '[Falsche Fadenspannung]';

  @override
  String get t_trouble28_01 => '*Ursache 1\nOberfaden ist nicht richtig eingefädelt.\n\n*Lösung\nFädeln Sie die Maschine unter Beachtung der einzelnen Schritte erneut ein.';

  @override
  String get t_trouble28_02 => '*Ursache 2\nSpule ist falsch eingesetzt.\n\n*Lösung\nSetzen Sie die Spule erneut ein.';

  @override
  String get t_trouble28_03 => '*Ursache 3\nNadel und Faden entsprechen nicht dem ausgewählten Stoff.\n\n*Lösung\nÜberprüfen Sie die Tabelle \"Stoff-/Faden-/ Nadelkombinationen\".';

  @override
  String get t_trouble28_04 => '*Ursache 4\nNähfußhalter ist nicht richtig befestigt.\n\n*Lösung\nBringen Sie den Nähfußhalter wieder richtig an.';

  @override
  String get t_trouble28_05 => '*Ursache 5\nFadenspannung ist falsch eingestellt.\n\n*Lösung\nRegulieren Sie die Fadenspannung.';

  @override
  String get t_trouble29 => '[Buchstabenmuster gelingen nicht]';

  @override
  String get t_trouble29_01 => '*Ursache 1\nFalscher Nähfuß.\n\n*Lösung\nSetzen Sie den richtigen Nähfuß ein.';

  @override
  String get t_trouble29_02 => '*Ursache 2\nMuster war falsch eingestellt.\n\n*Lösung\nÜberprüfen Sie die Mustereinstellungen.';

  @override
  String get t_trouble29_03 => '*Ursache 3\nDünnes Material oder Stretchgewebe wurde ohne Stickunterlegvlies verarbeitet.\n\n*Lösung\nBefestigen Sie ein Stickunterlegvlies.';

  @override
  String get t_trouble29_04 => '*Ursache 4\nFadenspannung ist falsch eingestellt.\n\n*Lösung\nRegulieren Sie die Fadenspannung.';

  @override
  String get t_trouble30 => '[Das Stickmuster gelingt nicht]';

  @override
  String get t_trouble30_01 => '*Ursache 1\nFaden ist verwickelt.\n\n*Lösung\nSchneiden Sie den verwickelten Faden mit einer Schere o. Ä. durch und ziehen Sie ihn aus dem Greifer.';

  @override
  String get t_trouble30_02 => '*Ursache 2\nStoff war nicht richtig in den Rahmen eingespannt (Stoff war zu lose usw.) .\n\n*Lösung\nWenn der Stoff nicht richtig in den Rahmen eingespannt ist, kann es sein, dass das Stickmuster nicht gelingt oder das Muster schrumpft. Spannen Sie den Stoff richtig in den Stickrahmen ein.';

  @override
  String get t_trouble30_03 => '*Ursache 3\nStickunterlegvlies war nicht richtig befestigt.\n\n*Lösung\nVerwenden Sie zum Sticken von Stretchgeweben, dünnen und grob gewebten Stoffen und solchen, die sich leicht zusammenziehen, immer ein Stickunterlegvlies. Ihr Fachhändler hält das richtige Unterlegmaterial für Sie bereit.';

  @override
  String get t_trouble30_04 => '*Ursache 4\nDer Wagen oder Stickrahmen hat während des Stickens einen im Weg liegenden Gegenstand berührt.\n\n*Lösung\nWenn der Rahmen während des Stickens an etwas stößt, wird das Stickmuster misslingen. Legen Sie daher nichts in den Bereich, in dem es der Rahmen während des Nähens berühren könnte.';

  @override
  String get t_trouble30_05 => '*Ursache 5\nDer außerhalb des Rahmens befindliche Stoff behindert den Stickarm, so dass das Stickgerät sich nicht richtig bewegen kann.\n\n*Lösung\nSpannen Sie den Stoff erneut so in den Rahmen, dass der überschüssige Stoff nicht mit dem Stickarm in Berührung kommt, und drehen Sie das Muster um 180 Grad.';

  @override
  String get t_trouble30_06 => '*Ursache 6\nDer Stoff ist zu schwer und bewirkt, dass das Stickgerät sich nicht richtig bewegen kann.\n\n*Lösung\nLegen Sie ein großes dickes Buch unter den Arm, um die schwere Seite etwas anzuheben und auf gleiche Höhe zu bringen.';

  @override
  String get t_trouble30_07 => '*Ursache 7\nStoff hängt über die Tischkante hinaus.\n\n*Lösung\nWenn der Stoff während des Stickens von der Tischkante herunterhängt, kann das Stickgerät sich nicht frei bewegen. Legen Sie den Stoff so, dass er nicht vom Tisch herunterhängt (oder halten Sie ihn fest, damit er nicht nach unten zieht) .';

  @override
  String get t_trouble30_08 => '*Ursache 8\nDer Stoff hat sich verhakt oder ist an etwas hängen geblieben.\n\n*Lösung\nStoppen Sie die Maschine und legen Sie den Stoff so, dass er sich nicht verhaken kann.';

  @override
  String get t_trouble30_09 => '*Ursache 9\nDer Stickrahmen war während des Stickens entfernt worden (z. B. um die Spule richtig einzusetzen) . Der Nähfuß wurde beim Anbringen des Stickrahmens versehentlich verschoben oder das Stickgerät wurde verschoben.\n\n*Lösung\nWenn der Nähfuß während des Nähens angestoßen oder das Stickgerät verschoben wurde, hat dies ein Mißlingen des Stickmusters zur Folge. Beim Entfernen oder erneuten Anbringen des Stickrahmens während des Nähens sollten Sie daher äußerst vorsichtig sein.';

  @override
  String get t_trouble31 => '[Nadel bricht ab]';

  @override
  String get t_trouble31_01 => '*Ursache 1\nNadel ist nicht richtig eingesetzt.\n\n*Lösung\nSetzen Sie die Nadel richtig ein.';

  @override
  String get t_trouble31_02 => '*Ursache 2\nNadelklemmschraube ist nicht fest genug angezogen.\n\n*Lösung\nZiehen Sie die Nadelklemmschraube an.';

  @override
  String get t_trouble31_03 => '*Ursache 3\nNadel ist verbogen.\n\n*Lösung\nSetzen Sie eine neue Nadel ein.';

  @override
  String get t_trouble31_04 => '*Ursache 4\nNadel und Faden entsprechen nicht dem ausgewählten Stoff.\n\n*Lösung\nÜberprüfen Sie die Tabelle \"Stoff-/Faden-/ Nadelkombinationen\".';

  @override
  String get t_trouble31_05 => '*Ursache 5\nFalscher Nähfuß.\n\n*Lösung\nSetzen Sie den richtigen Nähfuß ein.';

  @override
  String get t_trouble31_06 => '*Ursache 6\nOberfadenspannung ist zu stark.\n\n*Lösung\nRegulieren Sie die Fadenspannung.';

  @override
  String get t_trouble31_07 => '*Ursache 7\nStoff wurde während des Nähens gezogen.\n\n*Lösung\nZiehen Sie den Stoff nicht während des Nähens.';

  @override
  String get t_trouble31_08 => '*Ursache 8\nGarnrollenkappe wurde nicht richtig aufgesetzt.\n\n*Lösung\nÜberprüfen Sie, wie die Garnrollenkappe richtig angebracht wird, und bringen Sie sie dann richtig wieder an.';

  @override
  String get t_trouble31_09 => '*Ursache 9\nDie Stichplatte ist in der Nähe des Loches verkratzt.\n\n*Lösung\nErsetzen Sie die Stichplatte oder wenden Sie sich an den nächsten Fachhändler.';

  @override
  String get t_trouble31_10 => '*Ursache 10\nDer Nähfuß ist in der Nähe des Loches verkratzt.\n\n*Lösung\nErsetzen Sie den Nähfuß oder wenden Sie sich an den nächsten Fachhändler.';

  @override
  String get t_trouble31_11 => '*Ursache 11\nDer Greifer ist verkratzt.\n\n*Lösung\nErsetzen Sie den Greifer oder wenden Sie sich an den nächsten Fachhändler.';

  @override
  String get t_trouble31_12 => '*Ursache 12\nNadel ist defekt.\n\n*Lösung\nSetzen Sie eine neue Nadel ein.';

  @override
  String get t_trouble32 => '[Stoff wird nicht korrekt transportiert]';

  @override
  String get t_trouble32_01 => '*Ursache 1\nDer Transporteur ist abgesenkt.\n\n*Lösung\nDrücken Sie die Taste für den Freihandmodus und drehen Sie das Handrad um den Transporteur zu heben.';

  @override
  String get t_trouble32_02 => '*Ursache 2\nStiche sind zu dicht zusammen.\n\n*Lösung\nVerwenden Sie eine längere Stichlänge.';

  @override
  String get t_trouble32_03 => '*Ursache 3\nFalscher Nähfuß.\n\n*Lösung\nSetzen Sie den richtigen Nähfuß ein.';

  @override
  String get t_trouble32_04 => '*Ursache 4\nNadel ist verbogen oder die Spitze ist abgenutzt.\n\n*Lösung\nSetzen Sie eine neue Nadel ein.';

  @override
  String get t_trouble32_05 => '*Ursache 5\nFaden ist verwickelt.\n\n*Lösung\nSchneiden Sie den verwickelten Faden durch und ziehen Sie ihn aus dem Greifer.';

  @override
  String get t_trouble33 => '[Maschine läuft nicht an]';

  @override
  String get t_trouble33_01 => '*Ursache 1\nEs wurde kein Muster ausgewählt.\n\n*Lösung\nWählen Sie ein Stichmuster.';

  @override
  String get t_trouble33_02 => '*Ursache 2\nTaste \"Start/Stopp\" wurde nicht gedrückt.\n\n*Lösung\nDrücken Sie die Taste \"Start/Stopp\".';

  @override
  String get t_trouble33_03 => '*Ursache 3\nDer Netzschalter ist nicht eingeschaltet.\n\n*Lösung\nSchalten Sie den Netzschalter ein.';

  @override
  String get t_trouble33_04 => '*Ursache 4\nNähfuß ist nicht abgesenkt.\n\n*Lösung\nSenken Sie den Nähfuß ab.';

  @override
  String get t_trouble33_05 => '*Ursache 5\nTaste \"Start/Stopp\" wurde bei angeschlossenem Fußpedal betätigt.\n\n*Lösung\nNehmen Sie das Fußpedal ab oder benutzen Sie das Fußpedal, um die Maschine in Betrieb zu setzen.';

  @override
  String get t_trouble33_06 => '*Ursache 6\nGeschwindigkeitsregler wurde zur Einstellung der Zickzackstichbreite benutzt.\n\n*Lösung\nBenutzen Sie das Fußpedal anstelle der Taste \"Start/Stopp\", um die Maschine in Betrieb zu setzen.';

  @override
  String get t_trouble34 => '[Stickeinheit arbeitet nicht]';

  @override
  String get t_trouble34_01 => '*Ursache 1\nEs wurde kein Muster ausgewählt.\n\n*Lösung\nWählen Sie ein Stichmuster.';

  @override
  String get t_trouble34_02 => '*Ursache 2\nDer Netzschalter ist nicht eingeschaltet.\n\n*Lösung\nSchalten Sie den Netzschalter ein.';

  @override
  String get t_trouble34_03 => '*Ursache 3\nStickeinheit ist nicht richtig angeschlossen.\n\n*Lösung\nSchließen Sie die Stickeinheit wieder richtig an.';

  @override
  String get t_trouble34_04 => '*Ursache 4\nStickrahmen wurde vor Initialisierung des Gerätes eingesetzt.\n\n*Lösung\nFühren Sie den Initialisierungsvorgang richtig durch.';

  @override
  String get t_trouble35 => ' [Der Faden hat sich auf der Stoffunterseite verwickelt]';

  @override
  String get t_trouble35_01 => ' *Ursache 1\nHelligkeit des Displays ist nicht richtig eingestellt.\n\n*Lösung\nRegulieren Sie die Helligkeit des Displays.';

  @override
  String get t_maintenance36 => '[Reinigen von Greifergehäuse und Spulenkapsel]';

  @override
  String get t_maintenance36_00 => 'Falls sich im Greifergehäuse und in der Spulenkapsel Staub ansammelt, vermindert sich die Nähleistung und die Abtastung des Unterfadens wird unzuverlässig. Halten Sie daher den Greifer immer sauber.\nBevor Sie die Nähmaschine ausschalten, lesen Sie die nachfolgend beschriebenen Schritte.';

  @override
  String get t_maintenance36_01 => '\n1. Drücken Sie die Taste \"Nadelposition\", um die Nadel anzuheben.';

  @override
  String get t_maintenance36_02 => '2. Senken Sie den Nähfuß ab.';

  @override
  String get t_maintenance36_03 => '(a) AUS\n(b) EIN\n\n3. Schalten Sie den Netzschalter AUS.';

  @override
  String get t_maintenance36_04 => '4. Entfernen Sie die Nadel und den Nähfußhalter.';

  @override
  String get t_maintenance36_05_11 => '5. Entfernen Sie das Zubehörfach oder die Stickeinheit, falls eines davon installiert ist.\nBewegen Sie den Stichplattenhebel in Ihre Richtung.\nDaraufhin wird die Stichplatte geöffnet.';

  @override
  String get t_maintenance36_05_12 => '(a)Bewegung in Ihre Richtung.\n';

  @override
  String get t_maintenance36_05_13 => '6. Ziehen Sie die Stichplatte per Hand heraus, um sie zu entfernen.';

  @override
  String get t_maintenance36_05_14 => '(a) Stichplattenabdeckung\n';

  @override
  String get t_maintenance36_05_15 => '\n7. Nehmen Sie die Spulenkapsel und ziehen Sie sie vorsichtig heraus.';

  @override
  String get t_maintenance36_07_02 => '(a) Spulenkapsel';

  @override
  String get t_maintenance36_08 => '\n8. Entfernen Sie Staub und Fusseln, die sich im Greifergehäuse und drum herum angesammelt haben, mit dem Reinigungspinsel oder einem Staubsauger.\n\n* Die Spulenkapsel darf nicht geölt werden.';

  @override
  String get t_maintenance36_08_02 => '(a) Reinigungspinsel\n(b) Greifergehäuse';

  @override
  String get t_embbasic18_04_21 => '\n9. Setzen Sie die Spulenkapsel so ein, dass die Markierung ▲ an der Spulenkapsel mit der Markierung ● an der Maschine ausgerichtet ist.';

  @override
  String get t_embbasic18_04_22 => '(a)  ▲ Markierung an der Spulenkapsel\n(b)  ● Markierung an der Maschine';

  @override
  String get t_embbasic18_04_23 => '\n10. Stecken Sie die Laschen an der Stichplatte in die Kerben an der Maschine.';

  @override
  String get t_embbasic18_04_24 => '(a) Laschen\n(b) Kerben';

  @override
  String get t_embbasic18_04_25 => '11. Drücken Sie die rechte Seite der Stichplatte herunter, damit sie einrastet.';

  @override
  String get t_sewing01_00 => 'Auswahl der Nähart';

  @override
  String get t_sewing01_00_01 => '1-01:Normal\n1-05:Verstärkung\n1-06:Stretchstoffe';

  @override
  String get t_sewing01_00_01_s_normal => 'Normal';

  @override
  String get t_sewing01_00_01_s_reinforced => 'Verstärkung';

  @override
  String get t_sewing01_00_01_s_stretch => 'Stretchstoffe';

  @override
  String get t_sewing01 => '[Geradstich]';

  @override
  String get t_sewing01_01 => '\n1. Setzen Sie den Nähfuß \"J\" ein.\nHalten Sie Fadenenden und Stoff mit der linken Hand und drehen Sie das Handrad mit der rechten Hand, um die Nadel in den Stoff einzustechen.';

  @override
  String get t_sewing01_01_02 => '(a) Nähanfangsposition';

  @override
  String get t_sewing01_02 => '\n2. Senken Sie den Nähfuß und drücken Sie die Taste \"Rückwärts/Vernähen\", um 3-4 Stiche zu nähen. Die Maschine näht rückwärts oder vernäht.\nDrücken Sie die Taste \"Start/Stopp\", um vorwärts zu nähen. Die Maschine beginnt jetzt langsam zu nähen.';

  @override
  String get t_sewing01_03 => '(a) Rückwärtsstiche';

  @override
  String get t_sewing01_04 => '3. Wenn Sie mit dem Nähen fertig sind, drücken Sie die Taste \"Rückwärts/Vernähen\", um 3-4 Rückwärts- oder Verstärkungsstiche zu nähen.';

  @override
  String get t_sewing01_05 => '4. Wenn Sie mit dem Nähen fertig sind, drücken Sie die Taste \"Fadenabschneider\", um die Fäden abzuschneiden.\n\n* Wenn automatisches Fadenabschneiden und automatische Verstärkungsstiche auf der Anzeige gewählt sind, werden bei Betätigung der Taste \"Start/ Stopp\" am Anfang automatisch Rückwärts- oder Verstärkungsstiche genäht.\nWird die Taste \"Rückwärts/Vernähen\" am Nahtende gedrückt, werden automatisch Rückwärts- oder Verstärkungsstiche genäht und die Fäden abgeschnitten.';

  @override
  String get t_sewing01_06 => '\n * Ändern der Nadelposition \n Bei Stick- und Nähmustern mit linker oder mittlerer Nadelposition können Sie die Nadelposition mit Hilfe der Tasten „+“ und „-“ in der Ansicht „L/R Shift“ verändern. Einen besonders schönen Abschluss erzielen Sie, wenn Sie den Abstand vom rechten Rand des Nähfußes zur Nadel auf die Nahtzugabe abstimmen und während des Nähens darauf achten, dass der Nähfuß immer parallel zum Stoffrand ist.';

  @override
  String get t_sewing01_07 => '(a) Nahtzugabe';

  @override
  String get t_sewing02 => '[Überwendlingstich]';

  @override
  String get t_sewing02_00 => 'Auswahl der Nähart';

  @override
  String get t_sewing02_00_01 => '1-16:Dünne und mittelschwere Stoffe\n1-17:Dicke Stoffe\n1-18:Mittlere, dicke und leicht brüchige Stoffe\n1-19:Stretchstoffe\n1-20:Dicke und mittelschwere Stretchstoffe\n1-21:Stretchstoffe';

  @override
  String get t_sewing02_00_01_f_lightandmedium => 'Dünne und mittelschwere Stoffe';

  @override
  String get t_sewing02_00_01_f_heavy => 'Dicke Stoffe';

  @override
  String get t_sewing02_00_01_f_mediumstretch => 'Mittlere, dicke und leicht brüchige Stoffe';

  @override
  String get t_sewing02_00_01_f_stretch1 => 'Stretchstoffe';

  @override
  String get t_sewing02_00_01_f_thickandmediumstretch => 'Dicke und mittelschwere Stretchstoffe';

  @override
  String get t_sewing02_00_01_f_stretch2 => 'Stretchstoffe';

  @override
  String get t_sewing02_01 => '1. Setzen Sie den Nähfuß \"G\" ein. Senken Sie den Nähfuß so ab, dass die Nähfußführung genau mit dem Stoffrand abschließt.';

  @override
  String get t_sewing02_02 => '\n2. Nähen Sie an der Nähfußführung entlang.';

  @override
  String get t_sewing02_02_02 => '(a) Führung';

  @override
  String get t_sewing02_03 => '\n1. Setzen Sie den Nähfuß \"J\" ein. Nähen Sie so, dass die Nadel knapp neben dem Stoffrand einsticht.';

  @override
  String get t_sewing02_04 => '(a) Nadeleinstichposition';

  @override
  String get t_sewing02_05 => '\n* Drehen Sie nach dem Einstellen der Stichbreite das Handrad in Ihre Richtung, um zu kontrollieren, ob die Nadel den Nähfuß nicht berührt. Wenn die Nadel den Nähfuß berührt, kann sie brechen und Verletzungen verursachen.';

  @override
  String get t_sewing02_05_02 => '(a) Die Nadel darf den Nähfuß nicht berühren';

  @override
  String get t_sewing04 => '[Bogennähte]';

  @override
  String get t_sewing04_01 => 'Die Bogennaht ist ein wellenförmiges Satinstichmuster. Verwenden Sie dieses Stichmuster zur Verzierung von Blusenkragen, Taschentüchern oder um einem Saum eine besondere Note zu geben.\nUnter Umständen ist bei sehr dünnen Stoffen ein Kurzzeit-Sprühkleber notwendig. Nähen Sie eine Probenaht, bevor Sie Ihr Nähprojekt beginnen.';

  @override
  String get t_sewing04_02 => '1. Setzen Sie den Nähfuß \"N+\" ein. Nähen Sie die Bogennaht entlang der Stoffkante. Nähen Sie so, dass die Nadel etwas entfernt von der Stoffkante einsticht.';

  @override
  String get t_sewing04_03 => '2. Schneiden Sie den Stoff entlang der Bogennaht vorsichtig ab, ohne dabei die Fäden zu zerschneiden.';

  @override
  String get t_sewing05_00 => 'Auswahl der Nähart';

  @override
  String get t_sewing05_00_01 => '4-01:Dünne und mittelschwere Stoffe (für waagrechte Löcher)\n4-07:Dünne oder mittelschwere Stoffe\n4-10:Stretchstoffe, grob gewebt\n4-11:Stretchstoffe\n4-13:Anzüge, Mäntel\n4-14:Jeans, Hosen\n4-15:Dicke Mäntel';

  @override
  String get t_sewing05_00_01_f_lighttomediumhorizhole => 'Dünne und mittelschwere Stoffe (für waagrechte Löcher)';

  @override
  String get t_sewing05_00_01_f_lighttomedium => 'Dünne oder mittelschwere Stoffe';

  @override
  String get t_sewing05_00_01_f_stretchweaves => 'Stretchstoffe, grob gewebt';

  @override
  String get t_sewing05_00_01_f_stretch => 'Stretchstoffe';

  @override
  String get t_sewing05_00_01_f_suitsandovercoat => 'Anzüge, Mäntel';

  @override
  String get t_sewing05_00_01_f_jeansandtrousers => 'Jeans, Hosen';

  @override
  String get t_sewing05_00_01_f_thickcoats => 'Dicke Mäntel';

  @override
  String get t_sewing05 => '[Knopflöcher]';

  @override
  String get t_sewing05_02 => '1. Markieren Sie die Position und Länge des Knopflochs auf dem Stoff.';

  @override
  String get t_sewing05_03 => '\n2. Bringen Sie den Knopflochfuß \"A+\" an. Ziehen Sie die Knopfhalterplatte des Knopflochfußes \"A+\" heraus und führen Sie den Knopf ein, der durch das Knopfloch gesteckt wird. Befestigen Sie dann die Knopfhalterplatte um den Knopf.\n\n* Die Größe des Knopflochs wird durch die Größe des Knopfes in der Knopfhalterplatte bestimmt..';

  @override
  String get t_sewing05_04 => '(a) Knopfhalterplatte';

  @override
  String get t_sewing05_05 => '\n3. Richten Sie den Nähfuß mit der Markierung auf dem Stoff aus und senken Sie den Nähfußhebel.';

  @override
  String get t_sewing05_06 => '(a) Stoffmarkierung\n(b) Nähfußmarkierung';

  @override
  String get t_sewing05_07 => '\n4. Senken Sie den Knopflochhebel so, dass er sich hinter dem Metallhaken am Knopflochfuß befindet.';

  @override
  String get t_sewing05_08 => '(a) Metallhaken';

  @override
  String get t_sewing05_09 => '4. Halten Sie das Ende des Oberfadens fest und beginnen Sie mit dem Nähen. Transportieren Sie den Stoff vorsichtig mit der Hand, während Sie das Knopfloch nähen.\nAm Ende des Nähvorgangs näht die Maschine automatisch Verstärkungsstiche und hält dann an.';

  @override
  String get t_sewing05_10 => '\n5. Stecken Sie eine Stecknadel an der Innenkante des vorderen oder hinteren Riegels quer in das Knopfloch, stechen Sie mit dem Trennmesser in die Mitte des Knopflochs ein und schneiden Sie das Knopfloch dann in die Richtung zur Stecknadel hin auf.';

  @override
  String get t_sewing05_11 => '(a) Stecknadel\n(b) Pfeiltrenner';

  @override
  String get t_sewing05_12 => '\n[Für Augenknopflöcher]\nDurchstechen Sie das Knopflochauge mit einer Ahle. Sichern Sie die Innenseite des Riegels mit einer Stecknadel, stecken Sie einen Pfeiltrenner in das Knopflochauge und schneiden Sie das Knopfloch in Richtung Stecknadel auf.';

  @override
  String get t_sewing05_13 => '(a) Ahle\n(b) Stecknadel';

  @override
  String get t_sewing05_14 => '*Nähen von Stretchstoffen\nLegen Sie beim Knopflochnähen von Stretchstoffen eine Kordel in den Knopflochsaum ein (nur für 4-10- oder 4-11- Knopflöcher) .';

  @override
  String get t_sewing05_16 => '\n1. Haken Sie die Kordel über die Rückseite des Nähfußes \"A+\". Legen Sie die Fadenenden in die Nut an der Nähfußvorderseite und verknüpfen Sie die Kordel provisorisch.';

  @override
  String get t_sewing05_17 => '(a) Oberfaden';

  @override
  String get t_sewing05_18 => '2. Senken Sie den Nähfuß und beginnen Sie mit dem Nähen.';

  @override
  String get t_sewing05_19 => '3. Ziehen nach dem Nähen leicht an der Kordel, um den Faden zu spannen und schneiden Sie die überschüssige Kordel ab.';

  @override
  String get t_sewing05_20 => '\n*Falls die Knöpfe nicht in den Knopflochfuß eingelegt werden können\n\nOrientieren Sie sich bei der Einstellung der Knopflochgröße nach den Markierungen auf der Nähfußskala. Eine Markierung auf der Nähfußskala entspricht 5 mm (ca. 3/16 Zoll) . Messen Sie den Knopfdurchmesser und die Knopfdicke und addieren Sie die beiden Werte, um die Platte entsprechend einzustellen.';

  @override
  String get t_sewing05_21 => '(a) Nähfußskala\n(b) Knopfhalterplatte\n(c) Messergebnis bestehend aus Durchmesser und Dicke\n(d) 5 mm (ca. 3/16 Zoll) ';

  @override
  String get t_sewing05_22 => '\nBeispiel:\nFür einen Knopf mit einem Durchmesser von 15 mm (ca. 9/16 Zoll) und einer Dicke von 10 mm (ca. 3/8 Zoll) , muss die Skala auf 25 mm (ca. 1 Zoll) eingestellt werden.';

  @override
  String get t_sewing05_23 => '(a) 10 mm (ca. 3/8 Zoll) \n(b) 15 mm (ca. 9/16 Zoll) ';

  @override
  String get t_sewing06 => '[Annähen von Knöpfen]';

  @override
  String get t_sewing06_01 => 'Beim Annähen von Knöpfen darf die automatische Fadenabschneidefunktion nicht eingeschaltet sein. Sonst verlieren Sie die Fadenenden. \n\n1. Setzen Sie den Knopfannähfuß \"M\" ein, schieben den Knopf entlang der Metallplatte und in den Nähfuß hinein und senken Sie den Nähfuß ab.';

  @override
  String get t_sewing06_01_02 => '(a) Knopf\n(b) Metallplatte\n';

  @override
  String get t_sewing06_02 => '2. Drehen Sie das Handrad, um zu kontrollieren, ob die Nadel richtig in die Löcher einsticht. Halten Sie das Ende des Oberfadens fest und beginnen Sie mit dem Nähen. Nach dem Annähen des Knopfes hält die Maschine automatisch an.';

  @override
  String get t_sewing06_03 => '3. Ziehen Sie am Ende das Spulenfadenende nach unten und ziehen Sie das Oberfadenende auf die Stoffrückseite. Verknoten Sie die beiden Fadenenden und schneiden Sie die Fadenenden ab.';

  @override
  String get t_sewing06_04 => '*Annähen von Knöpfen mit vier Löchern';

  @override
  String get t_sewing06_05 => 'Nähen Sie zuerst die beiden vorderen Knopflöcher an. Heben Sie danach den Nähfuß an, schieben den Stoff so weiter, dass die Nadel in die nächsten beiden Löcher passt, und nähen Sie den Knopf an den hinteren Löchern auf die gleiche Weise an.';

  @override
  String get t_sewing06_06 => '*Annähen von Ösenknöpfen\n\n1. Ziehen Sie den Ösenknopfhebel vor dem Nähen nach vorne.';

  @override
  String get t_sewing06_07 => '(a) Ösenknopfhebel\n';

  @override
  String get t_sewing06_08 => '2. Halten Sie die beiden Oberfadenenden vom Anfang und Ende zwischen dem Knopf und dem Stoff fest, wickeln Sie die Fäden um den Hals und verknüpfen Sie die beiden Fäden miteinander.\nVerknüpfen Sie die Unterfadenenden auf der Stoffrückseite.';

  @override
  String get t_sewing07 => '[Verriegeln]';

  @override
  String get t_sewing07_01 => 'Mit Riegelnähten können Sie Bereiche verstärken, die starker Abnutzung ausgesetzt sind, wie z. B. die Ecken von Taschen.';

  @override
  String get t_sewing07_02 => '\n1. Setzen Sie den Knopflochfuß \"A+\" ein und stellen Sie die Länge der Riegelnaht auf der Skala ein.';

  @override
  String get t_sewing07_03 => '(a) Nähfußskala\n(b) Längenmessergebnis\n(c) 5 mm (ca. 3/16 Zoll) ';

  @override
  String get t_sewing07_04 => '2. Legen Sie den Stoff so, dass sich die Tasche während des Nähens in Ihre Richtung bewegt.';

  @override
  String get t_sewing07_05 => '\n3. Prüfen Sie den ersten Einstich und senken Sie den Nähfuß ab.';

  @override
  String get t_sewing07_06 => '(a) 2 mm (ca. 1/16 Zoll) ';

  @override
  String get t_sewing07_09 => '4. Halten Sie das Ende des Oberfadens fest und beginnen Sie mit dem Nähen. Am Ende des Nähvorgangs näht die Maschine automatisch Verstärkungsstiche und hält dann an.';

  @override
  String get t_sewing07_10 => '\n*Verriegeln von dicken Stoffen\nLegen Sie einen gefalteten Stoffrest oder Pappe von hinten unter den Nähfuß. Dadurch wird der Nähfuß angehoben und der Stofftransport erleichtert.';

  @override
  String get t_sewing07_11 => '(a) Nähfuß \n(b) Dickes Papier \n(c) Stoff';

  @override
  String get t_sewing08 => '[Reißverschlusseinsetzen]';

  @override
  String get t_sewing08_00 => '\n*Mittig eingenähter Reißverschluss\nFür Taschen u.Ä.\n\n1. Setzen Sie den Nähfuß \"J\" ein, und nähen Sie Geradstiche bis zur Reißverschlussgabelung. Heften Sie dann bis zur Reißverschlussöffnung.';

  @override
  String get t_sewing08_02 => '(a) Heftnaht\n(b) Rückwärtsstiche\n(c) Ende der Reißverschlussöffnung.\n(d) Linke Seite';

  @override
  String get t_sewing08_03 => '\n2. Drücken Sie die Nahtzugabe auseinander und bringen Sie den Reißverschluss mit einer Heftnaht in der Mitte jeder Seite des Reißverschlussbandes an.';

  @override
  String get t_sewing08_04 => '(a) Heftnaht\n(b) Reißverschluss\n(c) Linke Seite';

  @override
  String get t_sewing08_05 => '\n3. Entfernen Sie den Nähfuß \"J\". Richten Sie das rechte Stiftende des Reißverschlussfußes \"I\" mit dem Nähfußhalter aus und befestigen Sie den Reißverschlussfuß.';

  @override
  String get t_sewing08_06 => '(a) Rechts\n(b) Links\n(c) Nadeleinstichposition';

  @override
  String get t_sewing08_07 => '4. Nähen Sie auf der Stoffoberseite 7 bis 10 mm (ca. 1/4 bis 3/8 Zoll) von der Saumkante entfernt, und entfernen Sie die Heftstiche.';

  @override
  String get t_sewing08_08 => '\n*Reißverschluss seitlich einnähen\nFür seitliche Reißverschlüsse in Röcken und Kleidern.\n\n1. Setzen Sie den Nähfuß \"J\" ein, und nähen Sie Geradstiche bis zur Reißverschlussgabelung. Heften Sie dann bis zur Reißverschlussöffnung.';

  @override
  String get t_sewing08_11 => '(a) Rückwärtsstiche\n(b) Stoffrückseite\n(c) Heftnaht\n(d) Ende der Reißverschlussöffnung.';

  @override
  String get t_sewing08_12 => '\n2. Falten Sie die Nahtzugabe auseinander und legen Sie die Rückseite des Stoffes so auf den Reißverschluss, dass die Stoffkante richtig auf die Reißverschlusszähne ausgerichtet ist und ca. 3 mm (ca. 1/8 Zoll) für die Naht vorhanden sind.';

  @override
  String get t_sewing08_13 => '(a) Reißverschlussläufer\n(b) Stoffrückseite\n(c) Reißverschlusszähne\n(d) Ende der Reißverschlussöffnung\n(e) 3 mm (1/8 Zoll) ';

  @override
  String get t_sewing08_14 => '\n3. Entfernen Sie den Nähfuß \"J\". Richten Sie das rechte Stiftende des Reißverschlussfußes \"I\" mit dem Nähfußhalter aus und befestigen Sie den Nähfuß.';

  @override
  String get t_sewing08_15 => '(a) Rechts\n(b) Links\n(c) Nadeleinstichposition';

  @override
  String get t_sewing08_16 => '\n4. Stellen Sie den Nähfuß auf einen Abstand von 3 mm (1/8 Zoll) ein. Nähen Sie von der Reißverschlussöffnung aus bis ca. 50 mm (ca. 2 Zoll) vor der Stoffkante und stoppen Sie die Maschine. Ziehen Sie den Reißverschlussläufer nach unten und nähen Sie weiter bis zur Stoffkante.';

  @override
  String get t_sewing08_17 => '(a) 50 mm (ca. 2 Zoll) \n(b) 3 mm (ca. 1/8 Zoll) ';

  @override
  String get t_sewing08_18 => '\n5. Schließen Sie den Reißverschluss, drehen Sie den Stoff auf die andere Seite und nähen Sie eine Heftnaht.';

  @override
  String get t_sewing08_19 => '(a) Vorderseite des Rockes (Stoffrückseite) \n(b) Heftnaht\n(c) Vorderseite des Rockes (Stoffvorderseite) \n(d) Rückseite des Rockes (Stoffvorderseite) ';

  @override
  String get t_sewing08_20 => '\n6. Nehmen Sie den Nähfuß ab und bringen Sie ihn so wieder an, dass das linke Ende des Stiftes am Nähfußhalter befestigt ist.\n\n* Beim Nähen der linken Reißverschlussseite muss die Nadel rechts neben dem Nähfuß einstechen. Beim Nähen der rechten Reißverschlussseite muss die Nadel links neben dem Nähfuß einstechen.';

  @override
  String get t_sewing08_21 => '(a) Rechts\n(b) Links\n(c) Nadeleinstichposition';

  @override
  String get t_sewing08_22 => '\n7. Legen Sie den Stoff so, dass die linke Kante des Nähfußes den Rand der Reißverschlusszähne berührt. Nähen Sie am oberen Teil des Reißverschlusses zuerst Rückwärtsstiche und nähen Sie dann weiter. Nähen Sie bis ca. 50 mm (2 Zoll) vor der Stoffkante, lassen die Nadel im Stoff und entfernen Sie die Heftstiche. Öffnen Sie den Reißverschluss und nähen Sie den Rest der Naht.';

  @override
  String get t_sewing08_23 => '(a) Heftnähte\n(b) 7 bis 10 mm (ca. 1/4 bis 3/8 Zoll) \n(c) Rückwärtsstiche\n(d) 50 mm (ca. 2 Zoll) ';

  @override
  String get t_sewing09_00 => 'Auswahl der Nähart';

  @override
  String get t_sewing09_00_01 => 'Diese Stiche eignen sich zum Säumen von Manschetten an Kleidern und Blusen sowie für Hosen und Röcke.';

  @override
  String get t_sewing09_00_02 => '2-01:Andere Stoffe\n2-02:Stretchstoffe';

  @override
  String get t_sewing09_00_02_f_other => 'Andere Stoffe';

  @override
  String get t_sewing09_00_02_f_stretch => 'Stretchstoffe';

  @override
  String get t_sewing09 => '[Blindstiche]';

  @override
  String get t_sewing09_01 => '\n1. Legen Sie den Stoff mit der linken Seite nach oben, falten und heften Sie den Stoff.';

  @override
  String get t_sewing09_02 => '(a) 5 mm (ca. 3/16 Zoll) \n(b) Heftnähte\n(c) Stoffrückseite';

  @override
  String get t_sewing09_03 => '\n2. Setzen Sie den Blindstichfuß \"R\" ein und senken Sie den Nähfuß. Legen Sie den Stoff so, dass die gefaltete Kante die Nähfußführung berührt.';

  @override
  String get t_sewing09_04 => '(a) Führung\n(b) Falte';

  @override
  String get t_sewing09_05 => '\n3. Nähen Sie den Stoff und achten Sie dabei darauf, dass die gefaltete Kante immer in Kontakt mit der Nähfußführung bleibt.';

  @override
  String get t_sewing09_06 => '(a) Nadelposition';

  @override
  String get t_sewing09_07 => '\n4. Entfernen Sie den Heftfaden und wenden Sie den Stoff.';

  @override
  String get t_sewing09_08 => '(a) Stoffrückseite\n(b) Stoffvorderseite';

  @override
  String get t_sewing10 => '[Applikationen]';

  @override
  String get t_sewing10_01 => '\n1. Befestigen Sie die Applikation mit einem Kurzzeit-Sprühkleber, Stoffkleber oder einer Heftnaht auf dem Stoff. Dadurch kann beim Nähen nichts verrutschen.';

  @override
  String get t_sewing10_02 => '(a) Applikation\n(b) Stoffkleber';

  @override
  String get t_sewing10_03 => '\n2. Setzen Sie den Nähfuß \"J\" ein. Nähen Sie so, dass die Nadel knapp neben der Applikation einsticht und beginnen Sie zu nähen.';

  @override
  String get t_sewing10_04 => '(a) Applikationsmaterial\n(b) Nadeleinstichposition';

  @override
  String get t_sewing10_06 => '*Nähen scharfer Kurven\n';

  @override
  String get t_sewing10_07 => 'Halten Sie die Maschine an und positionieren Sie die Nadel knapp außerhalb der Applikation. Heben Sie den Nähfuß leicht an und drehen Sie den Stoff entsprechend, um die richtige Nadelposition einzuhalten.';

  @override
  String get t_sewing11 => '[Biesen]';

  @override
  String get t_sewing11_01 => '\n1. Markieren Sie die Falten auf der linken Stoffseite.';

  @override
  String get t_sewing11_01_02 => '(a) Linke Seite';

  @override
  String get t_sewing11_02 => '\n2. Drehen Sie den Stoff um und bügeln Sie nur die gefalteten Teile.';

  @override
  String get t_sewing11_02_02 => '(a) Oberfläche';

  @override
  String get t_sewing11_03 => '\n3.  Setzen Sie den Nähfuß \"I\" ein.\nNähen Sie einen Geradstich entlang der Falte.';

  @override
  String get t_sewing11_04_02 => '(a) Breite für Biesen\n(b) Linke Seite\n(c) Oberfläche';

  @override
  String get t_sewing11_05 => '4. Bügeln Sie alle Falten in derselben Richtung.';

  @override
  String get t_sewing12 => '[Raffen]';

  @override
  String get t_sewing12_00 => '\nFür Rocktaillen, Hemdärmel usw.';

  @override
  String get t_sewing12_01 => '\n1. Ziehen Sie Unter- und Oberfaden ca. 50 mm (ca.1-15/16 Zoll) heraus.';

  @override
  String get t_sewing12_01_02 => '(a) Oberfaden\n(b) Unterfaden\n(c) ca. 50 mm (ca. 1-15/16 Zoll) ';

  @override
  String get t_sewing12_02 => '\n2. Nähen Sie zwei Reihen Geradstiche parallel zur Saumlinie und schneiden Sie anschließend den überschüssigen Faden auf eine Länge von ca. 50 mm (ca. 1-15/16 Zoll) ab.';

  @override
  String get t_sewing12_02_02 => '(a) Saumlinie\n(b) 10 bis 15 mm (ca. 3/8 bis 9/16 Zoll) \n(c) ca. 50 mm (ca. 1-15/16 Zoll) ';

  @override
  String get t_sewing12_03 => '3. Ziehen Sie die Unterfäden bis zur gewünschten Raffung und binden Sie die Fäden dann zusammen.';

  @override
  String get t_sewing12_04 => '4. Glätten Sie die Falten mit einem Bügeleisen.\n';

  @override
  String get t_sewing12_05 => '5. Nähen Sie auf der Saumlinie und entfernen Sie die Heftnaht.';

  @override
  String get t_sewing13 => '[Abnäher]';

  @override
  String get t_sewing13_01 => '\n1. Nähen Sie am Anfang des Abnähers einen Rückwärtsstich und dann vom breiten Ende zum anderen Ende ohne den Stoff dabei zu dehnen.';

  @override
  String get t_sewing13_01_02 => '(a) Heften\n(b) Oberfläche\n(c) Linke Seite';

  @override
  String get t_sewing13_02 => '2. Schneiden Sie den Faden auf eine Länge von 50 mm (ca. 1-15/16 Zoll) ab und binden Sie dann beide Enden zusammen.\n\n* Nähen Sie am Ende keinen Rückwärtsstich.';

  @override
  String get t_sewing13_03 => '3. Führen Sie die Fadenenden mit einer Handnähnadel in den Abnäher ein.';

  @override
  String get t_sewing13_04 => '4. Bügeln Sie den Abnäher auf einer Seite flach.';

  @override
  String get t_sewing14 => '[Kappnaht]';

  @override
  String get t_sewing14_00 => '\nZur Verstärkung von Säumen und zum sauberen Abschließen von Kanten.\n\n1. Nähen Sie die Saumnaht und schneiden Sie dann die Hälfte der Saumzugabe von der Seite ab, an der die Kappnaht liegen soll.';

  @override
  String get t_sewing14_01_02 => '(a) Linke Seite\n(b) ca. 12 mm (ca. 1/2 Zoll) ';

  @override
  String get t_sewing14_02 => '\n2. Breiten Sie den Stoff entlang der Saumnaht aus.';

  @override
  String get t_sewing14_02_02 => '(a) Linke Seite\n(b) Saumnaht';

  @override
  String get t_sewing14_03 => '\n3. Legen Sie beide Saumzugaben auf die kürzere Saumseite (Schnittsaum) und bügeln Sie sie.';

  @override
  String get t_sewing14_03_02 => '(a) Linke Seite';

  @override
  String get t_sewing14_04 => '\n4. Falten Sie die längere Saumzugabe um die kürzere und nähen Sie die Faltenkante.';

  @override
  String get t_sewing14_04_01 => '(a) Linke Seite';

  @override
  String get t_sewing15_00 => 'Auswahl der Nähart';

  @override
  String get t_sewing15_00_01 => 'Q-01: Patchwork-Stich (Mitte)\nQ-02: Patchwork-Stich (rechts)\nQ-03: Patchwork-Stich (links)';

  @override
  String get t_sewing15_00_01_s_piecingmiddle => 'Patchwork-Stich (Mitte)';

  @override
  String get t_sewing15_00_01_s_piecingright => 'Patchwork-Stich (rechts)';

  @override
  String get t_sewing15_00_01_s_piecingleft => 'Patchwork-Stich (links)';

  @override
  String get t_sewing15 => '[Zusammenfügen]';

  @override
  String get t_sewing15_01 => '(a)  Nahtzugabe: 6,5 mm (ca. 1/4 Zoll)\n       (bei Auswahl von Q-02)\n(b) Mit der rechten Nähfußseite ausrichten.\n\n1. Setzen Sie den Nähfuß \"J\" ein.\nNähen Sie so, dass der Stoffrand auf die Seite des Nähfußes ausgerichtet ist.';

  @override
  String get t_sewing15_012 => '(a) Nahtzugabe: 7 mm\n       (bei Auswahl von Q-02)\n(b) Mit der rechten Nähfußseite ausrichten.\n\n1. Setzen Sie den Nähfuß \"J\" ein.\nNähen Sie so, dass der Stoffrand auf die Seite des Nähfußes ausgerichtet ist.';

  @override
  String get t_sewing15_01_02 => '(a) Nahtzugabe: 6,5 mm (ca. 1/4 Zoll)\n       (bei Auswahl von Q-03)\n(b) Mit der linken Nähfußseite ausrichten.\n\n1. Setzen Sie den Nähfuß \"J\" ein.\nNähen Sie so, dass der Stoffrand auf die Seite des Nähfußes ausgerichtet ist.';

  @override
  String get t_sewing15_01_022 => '(a) Nahtzugabe: 7 mm\n       (bei Auswahl von Q-03\n(b) Mit der linken Nähfußseite ausrichten.\n\n1. Setzen Sie den Nähfuß \"J\" ein.\nNähen Sie so, dass der Stoffrand auf die Seite des Nähfußes ausgerichtet ist.';

  @override
  String get t_sewing15_02 => '(a) Führung\n\nMit diesem 1/4-Zoll-Quiltfuß mit Führung kann eine genaue 1/4-Zoll- oder 1/8-Zoll-Nahtzugabe genäht werden. Er eignet sich zum Zusammensetzen eines Quilts oder zum Nähen auf der Stoffoberseite.\n\n1. Wählen Sie Q-01 und setzen Sie den 1/4-Zoll-Quiltfuß mit Führung ein.';

  @override
  String get t_sewing15_03 => '(a) Führung\n(b) 1/4 Zoll\n\n2. Verwenden Sie die Führung am Nähfuß und die Markierungen, um genaue Nahtzugaben zu nähen.\n\n\"Zusammenfügen einer 1/4-Zoll-Nahtzugabe\"\nNähen Sie so, dass die Führung genau mit dem Stoffrand abschließt.';

  @override
  String get t_sewing15_04 => '(a) Richten Sie am Anfang diese Markierung mit der Stoffkante aus\n(b) Beginn der Naht\n(c) Ende der Naht\n(d) Gegenüberliegende Stoffkante zum Beenden oder Drehen\n(e) 1/4 Zoll\n\n\"Erstellen einer genauen Nahtzugabe\"\nVerwenden Sie die Markierung am Nähfuß, um 1/4 Zoll von der Stoffkante mit dem Nähen zu beginnen, das Nähen zu beenden oder den Stoff zu drehen.';

  @override
  String get t_sewing15_05 => '(a) Stoffoberseite\n(b) Saum\n(c) 1/8 Zoll\n\n\"Quilten auf der Stoffoberseite, 1/8 Zoll\"\nNähen Sie so, dass der Stoffrand auf die linke Seite des Nähfußendes ausgerichtet ist.';

  @override
  String get t_sewing16 => '[Quilten]';

  @override
  String get t_sewing16_01 => '1. Entfernen Sie den Nähfuß und lösen Sie die Schraube des Nähfußhalters, um den Nähfußhalter abzunehmen.\n\nSetzen Sie den Adapter mit der flachen Seite der Adapteröffnung zur flachen Seite der Nähfußstange auf die Nähfußstange. Schieben Sie ihn so weit wie möglich nach oben und ziehen Sie dann die Schraube mit dem Schraubendreher fest.';

  @override
  String get t_sewing16_02 => '(a) Betätigungshebel\n(b) Nadelklemmschraube\n(c) Gabel\n(d) Nähfußstange\n\n2. Setzen Sie den Betätigungshebel des Stoffobertransportes so ein, dass die Nadelklemmschraube zwischen der Gabel sitzt und positionieren Sie den Stoffobertransport auf die Nähfußstange.';

  @override
  String get t_sewing16_03 => '3. Stellen Sie den Nähfußhebel nach unten. Die Schraube anschließend fest mit dem Schraubendreher anziehen.';

  @override
  String get t_sewing16_04 => '4. Halten Sie beim Nähen den Stoff auf beiden Seiten des Nähfußes mit beiden Händen fest.';

  @override
  String get t_sewing16_05 => '* Wenn \"AUTOMATISCHES STOFFSENSORSYSTEM\" im Einstellungsbildschirm auf \"ON\" eingestellt ist, kann der Stoff für die besten Nähergebnisse weich transportiert werden.';

  @override
  String get t_sewing17 => '[Freihand-Quilten]';

  @override
  String get t_sewing17_00 => '(a) Quiltfuß \"C\" zum Freihand-Quilten\n(b) Klarsicht-Quiltfuß \"O\" zum Freihand-Quilten\n\nVerwenden Sie zum Freihand-Quilten je nach ausgewähltem Stichmuster den Quiltfuß \"C\" oder den Klarsicht-Quiltfuß \"O\".';

  @override
  String get t_sewing17_01 => '1. Drücken Sie die Untertransport-Taste, um die Maschine für den Modus zum Freihand-Quilten einzustellen.';

  @override
  String get t_sewing17_02_01 => '(a) Nähfußhalterschraube\n(b) Kerbe\n\n2. Setzen Sie den Quiltfuß \"C\" zum Freihand-Quilten vorne ein und richten Sie dabei die Nähfußhalterschraube mit der Kerbe im Fuß aus.\nZiehen Sie dann die Nähfußhalterschraube an.';

  @override
  String get t_sewing17_02_02 => '(a) Stift\n(b) Nadelklemmschraube\n(c) Nähfußstange\n\nSetzen Sie den Klarsicht-Quiltfuß \"O\" so ein: Positionieren Sie den Quiltfußstift über der Nadelklemmschraube und richten Sie den Quiltfuß unten links mit der Nähfußstange aus.\nZiehen Sie dann die Nähfußhalterschraube an.';

  @override
  String get t_sewing17_03 => '(a) Stich\n\n3. Ziehen Sie den Stoff mit beiden Händen straff und führen Sie ihn mit gleichbleibender Geschwindigkeit zum Nähen von gleichmäßigen Stichen mit einer Länge von ca. 2,0 - 2,5 mm (ca. 1/16 - 3/32 Zoll).\n\n* Wir empfehlen, das Fußpedal anzuschließen und mit konstanter Geschwindigkeit zu nähen.';

  @override
  String get t_sewing18 => '[Echo-Quilten]';

  @override
  String get t_sewing18_00 => '(a) 6,4 mm (ca. 1/4 Zoll)\n(b) 9,5mm (ca. 3/8 Zoll)\n\nQuiltfuß \"E\" zum Freihand-Quilten.';

  @override
  String get t_sewing18_01 => '2. Entfernen Sie den Nähfuß, lösen Sie die Schraube des Nähfußhalters, und nehmen Sie dann die Schraube und den Nähfußhalter ab.\n\nSetzen Sie den Adapter mit der flachen Seite der Adapteröffnung zur flachen Seite der Nähfußstange auf die Nähfußstange. Schieben Sie ihn so weit wie möglich nach oben und ziehen Sie dann die Schraube mit dem Schraubendreher fest.';

  @override
  String get t_sewing18_02 => '3. Positionieren Sie den Quiltfuß \"E\" zum Freihand-Quilten auf der linken Seite der Nähfußstange und richten Sie dabei die Löcher im Quiltfuß mit der Nähfußstange aus.\n\nDrehen Sie die mitgelieferte kleine Schraube 2 bis 3 mal mit der Hand.';

  @override
  String get t_sewing18_03 => '4. Ziehen Sie die Schraube an.';

  @override
  String get t_sewing18_04 => '(a) 6,4 mm (ca. 1/4 Zoll)\n\n5. Verwenden Sie die Maße am Quiltfuß als Orientierung und nähen Sie um das Motiv herum.';

  @override
  String get t_sewing18_05 => 'Fertige Stickerei';

  @override
  String get t_sewing19 => '[Applikationen]';

  @override
  String get t_sewing19_01 => '(a) Nahtzugabe: 3 bis 5 mm\n\n1. Übertragen Sie die Konturen der Applikation auf den Applikationsstoff und schneiden Sie das Muster aus.';

  @override
  String get t_sewing19_02 => '2. Legen Sie ein Stück dickes Papier oder Applikationsvlies, das auf die Endgröße ausgeschnitten ist, auf die Rückseite der Applikation und falten Sie die Nahtzugabe mit einem Bügeleisen um.';

  @override
  String get t_sewing19_03 => '3. Wenden Sie die Applikation und entfernen Sie das Applikationsvlies oder Papier. Heften oder stecken Sie die Applikation auf den Stoff.';

  @override
  String get t_sewing19_04 => '(a) Nadeleinstichpunkt\n\n4. Setzen Sie den Nähfuß \"J\" ein.\nPrüfen Sie den Nadeleinstichpunkt und nähen Sie dann an der Applikationskante entlang. Achten Sie dabei darauf, dass die Nadel knapp neben dem Applikationsrand einsticht.';

  @override
  String get t_explain_use => '[Verwendung]';

  @override
  String get t_explain01_01 => 'Allgemeines Nähen, Raffen, Biesen usw. Rückwärtsstich wird genäht, wenn Taste \"Rückwärts/Vernähen\" gedrückt wird.';

  @override
  String get t_explain01_01_01 => '\n * Ändern der Nadelposition \n Bei Stick- und Nähmustern mit linker oder mittlerer Nadelposition können Sie die Nadelposition mit Hilfe der Tasten „+“ und „-“ in der Ansicht „L/R Shift“ verändern. Einen besonders schönen Abschluss erzielen Sie, wenn Sie den Abstand vom rechten Rand des Nähfußes zur Nadel auf die Nahtzugabe abstimmen und während des Nähens darauf achten, dass der Nähfuß immer parallel zum Stoffrand ist.';

  @override
  String get t_explain01_02 => 'Allgemeines Nähen, Raffen, Biesen usw. Rückwärtsstich wird genäht, wenn Taste \"Rückwärts/Vernähen\" gedrückt wird.';

  @override
  String get t_explain01_03 => 'Allgemeines Nähen, Raffen, Biesen usw. Rückwärtsstich wird genäht, wenn Taste \"Rückwärts/Vernähen\" gedrückt wird.';

  @override
  String get t_explain01_04 => 'Allgemeines Nähen, Raffen, Biesen usw. Rückwärtsstich wird genäht, wenn Taste \"Rückwärts/Vernähen\" gedrückt wird.';

  @override
  String get t_explain01_05 => 'Normales Nähen und als Zierstich auf der Stoffoberseite.';

  @override
  String get t_explain01_06 => 'Verstärkte Stiche, Nähen und Zierapplikationen.';

  @override
  String get t_explain01_07 => 'Zierstiche, Nähen auf der Stoffoberseite.';

  @override
  String get t_explain01_08 => 'Heften.';

  @override
  String get t_explain01_09 => 'Als Überwendlingstich, zum Stopfen. Rückwärtsstich wird genäht, wenn die Taste \"Rückwärts/Vernähen\" gedrückt wird.';

  @override
  String get t_explain01_10 => 'Als Überwendlingstich, zum Stopfen. Verstärkungsstich wird genäht, wenn die Taste \"Rückwärts/Vernähen\" gedrückt wird.';

  @override
  String get t_explain01_11 => 'Beginnend mit der rechten Nadelposition, wird der Zickzackstich nach links genäht.';

  @override
  String get t_explain01_12 => 'Beginnend mit der linken Nadelposition, wird der Zickzackstich nach rechts genäht.';

  @override
  String get t_explain01_13 => 'Überwendlingstich (Stoffe mittlerer Stärke und Stretchgewebe) , Bänder und Gummis.';

  @override
  String get t_explain01_14 => 'Überwendlingstich (Stoffe mittlerer Stärke, dicke Stoffe und Stretchgewebe) , Bänder und Gummis.';

  @override
  String get t_explain01_14a => 'Überwendlingstich (Stoffe mittlerer Stärke, dicke Stoffe und Stretchgewebe) , Bänder und Gummis.';

  @override
  String get t_explain01_15 => 'Verstärken von dünnen und Stoffen mittlerer Stärke.';

  @override
  String get t_explain01_16 => 'Verstärken von dicken Stoffen';

  @override
  String get t_explain01_17 => 'Verstärken von mittleren und dicken Stoffen und solchen, die leicht ausfransen; auch als Dekorstich zu benutzen.';

  @override
  String get t_explain01_18 => 'Verstärken der Nähte bei Stretchstoffen.';

  @override
  String get t_explain01_19 => 'Verstärken von mittleren und schweren Stoffen; auch als Dekorstich zu benutzen.';

  @override
  String get t_explain01_20 => 'Verstärken der Nähte bei Stretchstoffen oder als Dekorstich.';

  @override
  String get t_explain01_21 => 'Elastischer Stricksaum.';

  @override
  String get t_explain01_22 => 'Verstärken und Säumen von Stretchstoffen.';

  @override
  String get t_explain01_23 => 'Verstärken von Stretchstoffen.';

  @override
  String get t_explain01_24 => 'Geradstich, wobei der Stoff abgeschnitten wird.';

  @override
  String get t_explain01_25 => 'Zickzackstich, wobei der Stoff abgeschnitten wird.';

  @override
  String get t_explain01_26 => 'Überwendlingstich, wobei der Stoff abgeschnitten wird.';

  @override
  String get t_explain01_27 => 'Überwendlingstich, wobei der Stoff abgeschnitten wird.';

  @override
  String get t_explain01_28 => 'Überwendlingstich, wobei der Stoff abgeschnitten wird.';

  @override
  String get t_explain01_29 => 'Patchwork-Stich 6,5 mm (1/4 Zoll) Nahtzugabe rechts.';

  @override
  String get t_explain01_292 => 'Patchwork-Stich 7 mm Nahtzugabe rechts.';

  @override
  String get t_explain01_29a => 'Zusammenfügen/Patchwork';

  @override
  String get t_explain01_30 => 'Patchwork-Stich 6,5 mm (1/4 Zoll) Nahtzugabe links.';

  @override
  String get t_explain01_302 => 'Patchwork-Stich 7 mm Nahtzugabe links.';

  @override
  String get t_explain01_31 => 'Quiltstich (wie handgemacht) .';

  @override
  String get t_explain01_32 => 'Zickzackstich zum Nähen und Applizieren von Steppstoff.';

  @override
  String get t_explain01_33 => 'Quiltstich für unsichtbares Applizieren und Befestigen von Borten';

  @override
  String get t_explain01_34 => 'Hintergrund-Quilten.';

  @override
  String get t_explain02_01 => 'Säumen gewebter Stoffe.';

  @override
  String get t_explain02_02 => 'Säumen von Stretchstoffen.';

  @override
  String get t_explain02_03 => 'Applikationen, dekorative Knopflochstiche.';

  @override
  String get t_explain02_04 => 'Muschelsaum-Dekorstich beim Säumen von Stoffen. Wählen Sie eine größere Oberfadenspannung, um dem Muschelsaum eine attraktive Bogenform zu verleihen.';

  @override
  String get t_explain02_05 => 'Verzierung von Blusenkragen, Taschentüchern.';

  @override
  String get t_explain02_06 => 'Verzierung von Blusenkragen, Taschentüchern.';

  @override
  String get t_explain02_07 => 'Patchwork-Stich, Dekorstiche.';

  @override
  String get t_explain02_08 => 'Patchwork-Stich, Dekorstiche.';

  @override
  String get t_explain02_09 => 'Dekorstich zur Befestigung von Kordeln und Plattstickerei.';

  @override
  String get t_explain02_10 => 'Wabenstich, Dekorstich.';

  @override
  String get t_explain02_11 => 'Hexenstich, Dekorstich.';

  @override
  String get t_explain02_12 => 'Hexenstich, Überbrückung und Dekorstich.';

  @override
  String get t_explain02_13 => 'Annähen von Bändern in Stretchstoffen.';

  @override
  String get t_explain02_14 => 'Dekorstich.';

  @override
  String get t_explain02_15 => 'Dekorstich auf der Stoffoberseite.';

  @override
  String get t_explain02_15a => 'Dekorstich.';

  @override
  String get t_explain02_16 => 'Dekorstich.';

  @override
  String get t_explain02_17 => 'Dekorstich und Befestigen von Gummiband.';

  @override
  String get t_explain02_18 => 'Dekorstich und Applikationen.';

  @override
  String get t_explain02_19 => 'Dekorstich.';

  @override
  String get t_explain03_01 => 'Dekorsäume, dreifach gerade links.';

  @override
  String get t_explain03_02 => 'Dekorsäume, dreifach gerade in der Mitte.';

  @override
  String get t_explain03_03 => 'Dekorsäume, auf der Stoffoberseite.';

  @override
  String get t_explain03_04 => 'Dekorsäume, Annähen von Spitze.';

  @override
  String get t_explain03_05 => 'Dekorsäume.';

  @override
  String get t_explain03_06 => 'Dekorsäume, Sternstich.';

  @override
  String get t_explain03_07 => 'Hohlsäume, Dekorsäume.';

  @override
  String get t_explain03_08 => 'Hohlsäume, Dekorsäume.';

  @override
  String get t_explain03_09 => 'Hohlsäume, Dekorsäume.';

  @override
  String get t_explain03_10 => 'Hohlsäume, Dekorsäume.';

  @override
  String get t_explain03_11 => 'Hohlsäume, Dekorsäume.';

  @override
  String get t_explain03_12 => 'Hohlsäume, Dekorsäume.';

  @override
  String get t_explain03_13 => 'Hohlsäume, Dekorsäume.';

  @override
  String get t_explain03_14 => 'Hohlsäume, Dekorsäume.';

  @override
  String get t_explain03_15 => 'Hohlsäume, Dekorsäume.';

  @override
  String get t_explain03_16 => 'Hohlsäume, Dekorsäume.';

  @override
  String get t_explain03_17 => 'Hohlsäume, Dekorsäume.';

  @override
  String get t_explain03_18 => 'Hohlsäume, Dekorsäume.';

  @override
  String get t_explain03_19 => 'Dekorsäume und Überbrückungsstich.';

  @override
  String get t_explain03_20 => 'Dekorsäume. Hexenstich, Annähen von Bändern.';

  @override
  String get t_explain03_21 => 'Dekorsäume, Wabenstich.';

  @override
  String get t_explain03_22 => 'Dekorsäume, Wabenstich.';

  @override
  String get t_explain03_23 => 'Dekorsäume, Wabenstich.';

  @override
  String get t_explain03_24 => 'Dekorsäume.';

  @override
  String get t_explain03_25 => 'Dekorstich.';

  @override
  String get t_explain04_01 => 'Knopflöcher auf Stoffen leichter bis mittlerer Stärke.';

  @override
  String get t_explain04_02 => 'Knopflöcher mit zusätzlichem Platz für größere Knöpfe.';

  @override
  String get t_explain04_03 => 'Verstärkte Knopflöcher, in der Mitte spitz zulaufend.';

  @override
  String get t_explain04_04 => 'Knopflöcher mit vertikaler Verriegelung auf dickem Stoff.';

  @override
  String get t_explain04_05 => 'Knopflöcher mit Verriegelung.';

  @override
  String get t_explain04_06 => 'Knopflöcher für feine, mittlere bis dicke Stoffe';

  @override
  String get t_explain04_07 => 'Knopflöcher für Stoffe leichter bis mittlerer Stärke.';

  @override
  String get t_explain04_08 => 'Knopflöcher mit zusätzlichem Platz für größere Zierknöpfe.';

  @override
  String get t_explain04_09 => 'Knopflöcher für starke Beanspruchung mit vertikaler Verriegelung.';

  @override
  String get t_explain04_10 => 'Knopflöcher für Stretch- oder Webstoffe.';

  @override
  String get t_explain04_11 => 'Knopflöcher für Hohlsaum und Stretchstoffe.';

  @override
  String get t_explain04_12 => 'Der erste Schritt beim Erstellen von Paspel-Knopflöchern.';

  @override
  String get t_explain04_13 => 'Knopflöcher für schwere oder dicke Stoffe und große flache Knöpfe.';

  @override
  String get t_explain04_14 => 'Knopflöcher für mittlere bis schwere Stoffe und große flache Knöpfe.';

  @override
  String get t_explain04_15 => 'Knopflöcher für starke Beanspruchung mit vertikaler Verriegelung für schwere oder dicke Stoffe.';

  @override
  String get t_explain04_15a => 'Linke Seite eines 4-Stufen-Knopflochs.';

  @override
  String get t_explain04_15b => 'Verriegelung eines 4-Stufen-Knopflochs.';

  @override
  String get t_explain04_15c => 'Rechte Seite eines 4-Stufen-Knopflochs.';

  @override
  String get t_explain04_15d => 'Verriegelung eines 4-Stufen-Knopflochs.';

  @override
  String get t_explain04_16 => 'Stopfen von Stoffen mittlerer Stärke.';

  @override
  String get t_explain04_17 => 'Stopfen von dicken Stoffen.';

  @override
  String get t_explain04_18 => 'Verstärkung an Taschenöffnungen u. Ä.';

  @override
  String get t_explain04_19 => 'Annähen von Knöpfen.';

  @override
  String get t_explain04_20 => 'Zum Nähen von Ösen in Gürteln u. Ä. Passen Sie bei schlechten Ergebnissen das Stichmuster an.';

  @override
  String get t_explain04_21 => 'Zum Nähen von sternförmigen Ösen. Passen Sie bei schlechten Ergebnissen das Stichmuster an.';

  @override
  String get t_explain05_01 => 'Zur Befestigung von Applikationen auf röhrenförmigen Stoffteilen und Gehrungsecken.';

  @override
  String get t_explain05_02 => 'Zur Befestigung von Applikationen auf röhrenförmigen Stoffteilen und Gehrungsecken.';

  @override
  String get t_explain05_03 => 'Zur Befestigung von Applikationen auf röhrenförmigen Stoffteilen und Gehrungsecken.';

  @override
  String get t_explain05_04 => 'Zur Befestigung von Applikationen auf röhrenförmigen Stoffteilen.';

  @override
  String get t_explain05_05 => 'Zur Befestigung von Applikationen auf röhrenförmigen Stoffteilen.';

  @override
  String get t_explain05_06 => 'Zur Befestigung von Applikationen auf röhrenförmigen Stoffteilen und Gehrungsecken.';

  @override
  String get t_explain05_07 => 'Zur Befestigung von Applikationen auf röhrenförmigen Stoffteilen und Gehrungsecken.';

  @override
  String get t_explain05_08 => 'Zur Befestigung von Applikationen auf röhrenförmigen Stoffteilen und Gehrungsecken.';

  @override
  String get t_explain05_09 => 'Zur Befestigung von Applikationen auf röhrenförmigen Stoffteilen.';

  @override
  String get t_explain05_10 => 'Zur Befestigung von Applikationen auf röhrenförmigen Stoffteilen.';

  @override
  String get t_explain05_11 => 'Zur Befestigung von Applikationen auf röhrenförmigen Stoffteilen und Gehrungsecken.';

  @override
  String get t_explain05_12 => 'Zur Befestigung von Applikationen auf röhrenförmigen Stoffteilen und Gehrungsecken.';

  @override
  String get t_explain06_01 => 'Für Applikationsarbeiten mit Garn usw. zur Herstellung dekorativer, frei genähter Garnverzierungen.';

  @override
  String get t_explain06_02 => 'Freihand-Heftnähte\nWenn der Transporteur gesenkt ist, können Heftnähte genäht werden, während Sie den Stoff frei bewegen.';

  @override
  String get t_explain06_03a => 'Dieses Stichmuster besteht aus mehreren Stichen mit kurzer Stichlänge.\nNähen Sie dieses Stichmuster mit transparentem Nylongarn oder mit einem dünnen Garn als Oberfaden, der der Farbe des Stoffes entspricht, damit Ihr Projekt wie von Hand genäht aussieht. Wenn die Farbe des Unterfadens nicht die gleiche Farbe wie der Stoff hat, ist das Stichmuster besser erkennbar.';

  @override
  String get t_explain07_01 => 'Applikationen, Dekorstich.';

  @override
  String get t_explain07_02 => 'Das Stichmuster kann am Anfang oder am Ende des Nähens angeschrägt werden.';

  @override
  String get t_explaindeco00_01 => 'Sticken eines schönen Abschlusses';

  @override
  String get t_explaindeco00_02 => 'Änderung von Einstellungen';

  @override
  String get t_explaindeco01_00 => '[Sticken eines schönen Abschlusses]';

  @override
  String get t_explaindeco01_01 => 'Zum Nähen attraktiver Abschlüsse von Buchstaben-/Dekorstichen stehen viele Stoff/Faden/Nadel-Kombinationen zur Verfügung. Andere Faktoren, wie die Dicke des Stoffes, Stickunterlegvlies usw. beeinflussen den Stich ebenfalls. Nähen Sie daher immer zuerst eine Probenaht.';

  @override
  String get t_explaindeco01_02 => '(a) Stoff\n(b) Unterlegmaterial\n(c) Dünnes Papier\n\n*Stoff:\nBeim Nähen von Stretchstoffen, dünnen oder grob gewebten Stoffen können Sie optional ein Stickunterlegvlies verwenden. Wenn Sie kein Unterlegmaterial verwenden möchten, können Sie dünnes Papier, wie z. B. Pauspapier, unter den Stoff legen.\n\n*Faden\n#50 - #60\n\n*Nadel\nBei dünnen, normalen oder Stretchstoffen: Kugelkopfnadel (goldfarben) \nBei dicken Stoffen: Haushalts-Nähmaschinennadel Stärke 90/14\n\n*Nähfuß\nMonogramm-Nähfuß \"N+\" Die Verwendung eines anderen Nähfußes kann dazu führen, dass kein optimales Ergebnis erzielt wird.';

  @override
  String get t_explaindeco02_00 => '[Änderung von Einstellungen]';

  @override
  String get t_explaindeco02_01 => '\nJe nach Art und Dicke des verwendeten Stoffes, des Unterlegmaterials oder der Nähgeschwindigkeit usw. kann es vorkommen, dass Ihr Stich nicht so gelingt, wie Sie es wünschen. Aus diesem Grund ist es ratsam, vor dem Nähen einige Probestiche mit den gleichen Materialien wie dem eigentlichen Nähprojekt zu nähen, und den Stich ggf. wie unten erklärt zu regulieren. Wenn das Muster auch nach dem Ändern der Grundmuster 6-120 nicht einwandfrei herauskommt, stellen Sie jedes Muster individuell ein.';

  @override
  String get t_explaindeco02_02 => '1. Wählen Sie 6-120. Bringen Sie den Nähfuß \"N+\" an und nähen Sie das Muster.';

  @override
  String get t_explaindeco02_03_00 => '\n2. Vergleichen Sie das genähte Muster mit dem Bild unten.';

  @override
  String get t_explaindeco02_04_00 => '[1. Wenn das Muster sich zusammengezogen hat]';

  @override
  String get t_explaindeco02_04_01 => 'Drücken Sie + in FEINEINSTELLUNG VERTI. Nähen Sie das Stichmuster noch einmal. Ist das Stichmuster immer noch schlecht, justieren Sie erneut. Stellen Sie das Muster so lange ein, bis es einwandfrei genäht wird.';

  @override
  String get t_explaindeco02_05_00 => '[2. Wenn das Muster Lücken hat]';

  @override
  String get t_explaindeco02_05_01 => 'Drücken Sie - in FEINEINSTELLUNG VERTI. Nähen Sie das Stichmuster noch einmal. Ist das Stichmuster immer noch schlecht, justieren Sie erneut. Stellen Sie das Muster so lange ein, bis es einwandfrei genäht wird.';

  @override
  String get t_explaindeco02_06_00 => '[3. Wenn das Muster nach links verdreht ist]';

  @override
  String get t_explaindeco02_06_01 => 'Drücken Sie + in FEINEINSTELLUNG HORIZ. Ist das Stichmuster immer noch schlecht, justieren Sie erneut. Stellen Sie das Muster so lange ein, bis es einwandfrei genäht wird.';

  @override
  String get t_explaindeco02_07_00 => '[4. Wenn das Muster nach rechts verdreht ist]';

  @override
  String get t_explaindeco02_07_01 => 'Drücken Sie - in FEINEINSTELLUNG HORIZ. Ist das Stichmuster immer noch schlecht, justieren Sie erneut. Stellen Sie das Muster so lange ein, bis es einwandfrei genäht wird.';

  @override
  String get t_terms_read => 'Lesen Sie die folgenden Bestimmungen aufmerksam durch.';

  @override
  String get t_terms_cert_read => 'Lesen Sie die folgenden Bestimmungen aufmerksam durch.';

  @override
  String get t_terms_cert_01_00 => 'Zertifizierung für Upgrade KIT';

  @override
  String get t_terms_cert_01_01 => 'Bedingungen der Zertifizierung für Upgrade KIT\n';

  @override
  String get t_terms_cert_01_02 => '  Wenn Sie in dieser Software („Software“) optionale Funktionen wie gebührenpflichtige Lizenzen, Bedienungsanleitungen, Dokumente oder andere Materialien und zugehörige Aktualisierungen (kollektiv die „Tools“) aktivieren, ist unter Umständen direkt oder indirekt die Angabe bestimmter Lizenzcodes, der Produktnummer, der Seriennummer oder anderer zugehöriger Informationen („Benutzerdaten“) erforderlich, um diese Tools verwenden zu können.\n';

  @override
  String get t_terms_cert_01_03 => '  Manche der in den Benutzerdaten enthaltenen Informationen können mit den Daten in Verbindung gebracht werden, die Sie auf der Website zur Produktregistrierung von Brother Industries, Ltd. („Unternehmen“) oder einer Tochtergesellschaft angeben.  Das Unternehmen verwendet die Benutzerdaten jedoch zu keinem anderen Zweck, als Sie für die Aktivierung der Tools zu identifizieren. Die Benutzerdaten können auf den Verwaltungsserver des Unternehmens oder Server solcher Anbieter von Cloud-Diensten wie Microsoft und Amazon übermittelt werden, die sich in Ländern befinden, die für persönliche Daten verglichen mit Ihrem Land kein angemessenes Maß an Schutz bieten. Das Unternehmen wird Ihre Benutzerdaten jedoch in Übereinstimmung mit den geltenden Gesetzen unter Anwendung angemessener Sicherheitsmaßnahmen schützen, um einer unbefugten Verwendung oder Offenlegung vorzubeugen.';

  @override
  String get t_terms_nettool_read => 'Lesen Sie die folgenden Bestimmungen aufmerksam durch.';

  @override
  String get t_terms_nettool_01_00 => 'Netzwerk-Diagnosetool';

  @override
  String get t_terms_nettool_01_01 => 'Bedingungen des Netzwerk-Diagnosetools\n';

  @override
  String get t_terms_nettool_01_02 => '  Wenn Sie ein Problem mit Ihrer Netzwerkverbindung haben, können Sie das Netzwerk-Diagnosetool in dieser Software („Software“) ausführen.  Wenn Sie eine Netzwerk-Diagnose durchführen, werden auf dem Bildschirm Informationen von Näh- oder Bastelprodukten („Unternehmensprodukt“) und aller mit dem Unternehmensprodukt verbundenen Geräte, wie unter anderem die Internet Protocol (IP) oder die Media Access Control (MAC) Adresse, Informationen zur Proxy-Verbindung, Subnetzmaske, Gateway, DNS-Server, und andere zugehörige Informationen („netzwerkbezogene Informationen“) angezeigt.  ';

  @override
  String get t_terms_nettool_01_03 => '  Wenn Sie Probleme mit der Internetverbindung des Unternehmensprodukts haben und technische Unterstützung wünschen, werden Sie unter Umständen gebeten, Ihrem Händler vor Ort oder Brother Industries, Ltd. („Unternehmen“) und/oder dessen Tochtergesellschaften Ihre netzwerkbezogenen Informationen persönlich, telefonisch, per Post, per Fax oder über das Internet mitzuteilen.  Wenn Sie Ihre netzwerkbezogenen Informationen bereitstellen, erklären Sie sich damit einverstanden, dass die netzwerkbezogenen Informationen an das Unternehmen oder die Tochtergesellschaften allein zu dem Zweck übermittelt werden, Ihr Netzwerkproblem zu analysieren oder zu beheben, und dass Ihre Informationen in Übereinstimmung mit den geltenden Gesetzen geschützt werden.\n  Ihre netzwerkbezogenen Informationen werden vom Unternehmen oder dessen Tochtergesellschaften ausschließlich zu den hier aufgeführten Zwecken gesammelt oder gespeichert, sofern das Unternehmen und/oder dessen Tochtergesellschaften keine gesonderte vorherige Genehmigung von Ihnen einholen.';

  @override
  String get t_terms_cert_read_t => 'Lesen Sie die folgenden Bestimmungen aufmerksam durch.';

  @override
  String get t_terms_cert_01_01_t => 'Bedingungen der Zertifizierung für Upgrade KIT\n';

  @override
  String get t_terms_cert_01_02_t => '  Wenn Sie optionale Funktionen in dieser Software („Software“) aktivieren, darunter zahlungspflichtige Lizenzen, Anleitungen, Dokumente und weitere Materialien sowie entsprechende Aktualisierungen (zusammen die „Tools“), müssen Sie möglicherweise direkt oder indirekt bestimmte Lizenzcodes, Produktnummern und Seriennummern („Benutzerdaten“) angeben, um die Tools verwenden zu können.\n';

  @override
  String get t_terms_cert_01_03_t => '  Einige der in den Benutzerdaten enthaltenen Informationen können mit den Daten in Zusammenhang gebracht werden, die Sie möglicherweise auf der Website zur Produktregistrierung von Tacony Corporation d/b/a Baby Lock („Unternehmen“) angeben.  Das Unternehmen verwendet die Benutzerdaten jedoch nicht, um Sie persönlich zu identifizieren, oder für andere Zwecke außer zur Aktivierung der Tools. Die Benutzerdaten können an den Verwaltungsserver des Unternehmens oder an Server von Cloud-Anbietern wie Microsoft und AWS übermittelt werden, die sich möglicherweise in Ländern befinden, in denen im Vergleich zum Land Ihres Wohnsitzes ein niedrigeres Datenschutzniveau herrscht.  Das Unternehmen schützt Ihre Benutzerdaten jedoch in Einklang mit den geltenden Gesetzen unter Anwendung geeigneter Sicherheitsmaßnahmen, um die unbefugte Nutzung oder Offenlegung zu verhindern.';

  @override
  String get t_terms_nettool_read_t => 'Lesen Sie die folgenden Bestimmungen aufmerksam durch.';

  @override
  String get t_terms_nettool_01_01_t => 'Bestimmungen zum Tool zur Netzwerkdiagnose\n';

  @override
  String get t_terms_nettool_01_02_t => '  Wenn Probleme mit Ihrer Netzwerkverbindung vorliegen, können Sie in dieser Software („Software“) das Tool zur Netzwerkdiagnose ausführen.  Wenn Sie eine Netzwerkdiagnose durchführen, werden Daten von Nähprodukten („Unternehmensprodukt“) und Geräten, die mit dem Unternehmensprodukt verbunden sind, darunter IP- (Internet Protocol) oder MAC- (Media Access Control)-Adresse, Proxy-Verbindungsinformationen, Subnetzmaske, Gateway, DNS-Server und sonstige zugehörige Informationen („Netzwerkinformationen“) auf dem Bildschirm angezeigt.  ';

  @override
  String get t_terms_nettool_01_03_t => '  Wenn Probleme mit der Internetverbindung des Unternehmensprodukts vorliegen und Sie technischen Support in Anspruch nehmen möchten, werden Sie möglicherweise gebeten, Ihre Netzwerkinformationen persönlich, per Telefon, E-Mail oder Fax oder über das Internet an Ihren Einzelhändler vor Ort oder an Tacony Corporation d/b/a Baby Lock („Unternehmen“) zu übermitteln.  Durch die Bereitstellung Ihrer Netzwerkinformationen stimmen Sie zu und erkennen an, dass die Netzwerkinformationen nur zum Zweck der Analyse oder Behebung Ihres Netzwerkproblems an das Unternehmen übermittelt werden dürfen und dass Ihre Daten in Einklang mit geltenden Gesetzen geschützt werden.\n  Abgesehen von den hierin dargelegten Bestimmungen werden Ihre Netzwerkinformationen vom Unternehmen nicht erfasst oder gespeichert, sofern das Unternehmen nicht zuvor separat Ihre Zustimmung eingeholt hat.';

  @override
  String get t_terms_mnmpinmac_01_b => 'Wenn Sie auf „OK“ klicken, werden der PIN-Code, die MAC-Adresse und die Maschinennummer an den Server von Brother gesendet, damit Ihre Maschine mit ScanNCut und Ihren weiteren Nähmaschinen verknüpft werden kann.\nDie bereitgestellten Informationen werden ausschließlich zu den oben genannten Zwecken genutzt.';

  @override
  String get t_terms_snj_pair_01 => 'Wenn Sie auf „OK“ klicken, werden der PIN-Code, die MAC-Adresse, die Maschinenbezeichnung und die Maschinennummer an den Server von Brother gesendet, damit Ihre Nähmaschine mit Ihren weiteren Brother-Geräten und -Diensten verknüpft werden kann.\nDie bereitgestellten Informationen werden ausschließlich zu den oben genannten Zwecken genutzt.';

  @override
  String get upg_01 => 'Das USB-Medium anschließn.';

  @override
  String get upg_02 => 'Datei nicht lesbar.';

  @override
  String get upg_03 => 'Richtige Datei nicht gefunden.';

  @override
  String get upg_04 => 'Prüfsummenfehler';

  @override
  String get upg_05 => 'Dateispeichern nicht abgeschlossen.';

  @override
  String get upg_06 => 'Falsche Dateiadresse.';

  @override
  String get upg_07 => 'Aktive PC-Verbindung. Das USB-Kabel nicht trennen.';

  @override
  String get upg_08 => 'Aktualisierungsdatei wird gespeichert. \nNetzschalter nicht ausschalten (OFF).';

  @override
  String get update_08 => 'Aktualisierungsdatei wird gespeichert. \nNetzschalter nicht ausschalten (OFF).';

  @override
  String get upg_09 => 'Aktualisierung abgeschlossen.';

  @override
  String get update_09 => 'Aktualisierung abgeschlossen.';

  @override
  String get upg_10 => 'Nach dem Anschließen des USB-Mediums mit der Aktualisierungsdatei die Taste LADEN drücken.';

  @override
  String get update_10 => 'Nach dem Anschließen des USB-Mediums mit der Aktualisierungsdatei die Taste LADEN drücken.';

  @override
  String get upg_12 => 'Drücken Sie auf LADEN, um die Update-Datei zu installieren.';

  @override
  String get update_13 => 'Eine direkte Aktualisierung von der aktuellen Version auf die neue Version ist nicht möglich.';

  @override
  String get update_14 => 'Die Software aktualisieren Sie auf die unten genannte Version, indem Sie die Maschine ausschalten und die Aktualisierung mit dem USB-Medium und der darauf enthaltenen Aktualisierungsdatei durchführen.';

  @override
  String get update_15 => 'Schalten Sie die Maschine aus und führen Sie die Aktualisierung mit dem USB-Medium und der darauf enthaltenen Aktualisierungsdatei durch.';

  @override
  String get icon_00037 => 'Zum\nAnfang';

  @override
  String get icon_00008_u => 'Schliessen';

  @override
  String get icon_00009_u => 'Abbrechen';

  @override
  String get icon_00010_u => 'ＯＫ';

  @override
  String get icon_00050_u => 'Laden';

  @override
  String get upg_16 => 'Aktualisierung fehlgeschlagen. \nWiederholen Sie bitte die Installation des Aktualisierungsprogramms. \n* Wenn das Problem weiterhin besteht, laden Sie das Programm erneut herunter und installieren Sie es neu.';

  @override
  String get upg_17 => 'Aktualisierung fehlgeschlagen. \nLaden Sie das Aktualisierungsprogramm erneut herunter und installieren Sie es neu.';

  @override
  String get upg_18 => 'ERR_UPEND';

  @override
  String get upg_19 => 'Bitte starten Sie die Maschine neu.\nDer erste Start der Maschine kann einige Zeit dauern. Der Bildschirm kann vorübergehend dunkel werden.';

  @override
  String get upg_20 => 'Schalten Sie die Maschine nicht aus, auch wenn der Bildschirm schwarz wird.';

  @override
  String get upg_21 => 'Die Erkennung einer Dateisystembeschädigung ist fehlgeschlagen. \nSchalten Sie die Maschine aus und wieder ein.';

  @override
  String get upg_22 => 'Beschädigte Systemdateien konnten nicht repariert werden.\nSchalten Sie die Maschine aus und wieder ein.';

  @override
  String get upg_23 => 'Aktualisierung fehlgeschlagen. \nNachdem Sie die Maschine normal gestartet haben, schalten Sie sie aus und versuchen Sie dann erneut, das Aktualisierungsprogramm zu installieren.';

  @override
  String get t_name_01_01 => 'Geradstich (Links)';

  @override
  String get t_name_01_02 => 'Geradstich (Links)';

  @override
  String get t_name_01_03 => 'Geradstich (Mitte)';

  @override
  String get t_name_01_04 => 'Geradstich (Mitte)';

  @override
  String get t_name_01_05 => 'Dreifacher Stretchstich';

  @override
  String get t_name_01_06 => 'Stilstich';

  @override
  String get t_name_01_07 => 'Dekorstich';

  @override
  String get t_name_01_08 => 'Heftnaht';

  @override
  String get t_name_01_09 => 'Zickzackstich';

  @override
  String get t_name_01_10 => 'Zickzackstich';

  @override
  String get t_name_01_11 => 'Zickzackstich (Rechts)';

  @override
  String get t_name_01_12 => 'Zickzackstich (Links)';

  @override
  String get t_name_01_13 => '2-st. genähter elast. Zickzackstich';

  @override
  String get t_name_01_14 => '2-st. genähter elast. Zickzackstich';

  @override
  String get t_name_01_14a => '3-st. genähter elast. Zickzackstich';

  @override
  String get t_name_01_15 => 'Überwendlingstich';

  @override
  String get t_name_01_16 => 'Überwendlingstich';

  @override
  String get t_name_01_17 => 'Überwendlingstich';

  @override
  String get t_name_01_18 => 'Überwendlingstich';

  @override
  String get t_name_01_19 => 'Überwendlingstich';

  @override
  String get t_name_01_20 => 'Überwendlingstich';

  @override
  String get t_name_01_21 => 'Überwendlingstich';

  @override
  String get t_name_01_22 => 'Trikotstich';

  @override
  String get t_name_01_23 => 'Trikotstich';

  @override
  String get t_name_01_24 => 'Mit Kantenabschneider';

  @override
  String get t_name_01_25 => 'Mit Kantenabschneider';

  @override
  String get t_name_01_26 => 'Mit Kantenabschneider';

  @override
  String get t_name_01_27 => 'Mit Kantenabschneider';

  @override
  String get t_name_01_28 => 'Mit Kantenabschneider';

  @override
  String get t_name_01_29 => 'Patchwork-Stich (Rechts)';

  @override
  String get t_name_01_29a => 'Patchwork-Stich (Mitte)';

  @override
  String get t_name_01_30 => 'Patchwork-Stich (Links)';

  @override
  String get t_name_01_31 => 'Quiltstich';

  @override
  String get t_name_01_32 => 'Zickzackstich zum Quilten';

  @override
  String get t_name_01_33 => 'Quilt-Applikationsstich';

  @override
  String get t_name_01_34 => 'Quilt-Punktierstich';

  @override
  String get t_name_02_01 => 'Blindstich';

  @override
  String get t_name_02_02 => 'Stretch-Blindstich';

  @override
  String get t_name_02_03 => 'Knopflochstich';

  @override
  String get t_name_02_03a => 'Knopflochstich';

  @override
  String get t_name_02_04 => 'Muschelsaum';

  @override
  String get t_name_02_05 => 'Satin-Bogennaht';

  @override
  String get t_name_02_06 => 'Bogennaht';

  @override
  String get t_name_02_07 => 'Patchwork-Verbindungsstich';

  @override
  String get t_name_02_08 => 'Patchwork-Überwendlingstich';

  @override
  String get t_name_02_09 => 'Plattstich';

  @override
  String get t_name_02_10 => 'Wabenstich';

  @override
  String get t_name_02_11 => 'Hexenstich';

  @override
  String get t_name_02_12 => 'Kreuzweiser Hexenstich';

  @override
  String get t_name_02_13 => 'Annähen von Bändern';

  @override
  String get t_name_02_14 => 'Maschenstich';

  @override
  String get t_name_02_15 => 'Zickzackstich';

  @override
  String get t_name_02_15a => 'Dekorstich';

  @override
  String get t_name_02_16 => 'Dekorstich';

  @override
  String get t_name_02_17 => 'Schlangenstich';

  @override
  String get t_name_02_18 => 'Dekorstich';

  @override
  String get t_name_02_19 => 'Dekorativer Punktierstich';

  @override
  String get t_name_03_01 => 'Saumnähte';

  @override
  String get t_name_03_02 => 'Saumnähte';

  @override
  String get t_name_03_03 => 'Zickzack-Saumnähte';

  @override
  String get t_name_03_04 => 'Saumnähte';

  @override
  String get t_name_03_05 => 'Saumnähte';

  @override
  String get t_name_03_06 => 'Saumnähte';

  @override
  String get t_name_03_07 => 'Saumnähte';

  @override
  String get t_name_03_08 => 'Saumnähte';

  @override
  String get t_name_03_09 => 'Saumnähte';

  @override
  String get t_name_03_10 => 'Saumnähte';

  @override
  String get t_name_03_11 => 'Saumnähte';

  @override
  String get t_name_03_12 => 'Wabenmuster';

  @override
  String get t_name_03_13 => 'Wabenmuster';

  @override
  String get t_name_03_14 => 'Saumnähte';

  @override
  String get t_name_03_15 => 'Saumnähte';

  @override
  String get t_name_03_16 => 'Saumnähte';

  @override
  String get t_name_03_17 => 'Saumnähte';

  @override
  String get t_name_03_18 => 'Saumnähte';

  @override
  String get t_name_03_19 => 'Saumnähte';

  @override
  String get t_name_03_20 => 'Saumnähte';

  @override
  String get t_name_03_21 => 'Saumnähte';

  @override
  String get t_name_03_22 => 'Saumnähte';

  @override
  String get t_name_03_23 => 'Saumnähte';

  @override
  String get t_name_03_24 => 'Saumnähte';

  @override
  String get t_name_03_25 => 'Maschenstich';

  @override
  String get t_name_04_01 => 'Schmales Rundknopfloch';

  @override
  String get t_name_04_02 => 'Breites Rundknopfloch';

  @override
  String get t_name_04_03 => 'Rundknopfloch mit Keilriegel';

  @override
  String get t_name_04_04 => 'Rundknopfloch mit Längsriegel';

  @override
  String get t_name_04_05 => 'Rundknopfloch mit Längsriegel';

  @override
  String get t_name_04_06 => 'Beidseitig abgerund. Knopfloch';

  @override
  String get t_name_04_07 => 'Schmales Standardknopfloch';

  @override
  String get t_name_04_08 => 'Breites Standardknopfloch';

  @override
  String get t_name_04_09 => 'Standardknopfloch';

  @override
  String get t_name_04_10 => 'Elastikknopfloch';

  @override
  String get t_name_04_11 => 'Hohlsaum-Knopfloch';

  @override
  String get t_name_04_12 => 'Paspel-Knopfloch';

  @override
  String get t_name_04_13 => 'Augenknopfloch';

  @override
  String get t_name_04_14 => 'Augenknopfloch mit Keilriegel';

  @override
  String get t_name_04_15 => 'Augenknopfloch';

  @override
  String get t_name_04_15a => '4-Stufen-Knopfloch 1';

  @override
  String get t_name_04_15b => '4-Stufen-Knopfloch 2';

  @override
  String get t_name_04_15c => '4-Stufen-Knopfloch 3';

  @override
  String get t_name_04_15d => '4-Stufen-Knopfloch 4';

  @override
  String get t_name_04_16 => 'Stopfen';

  @override
  String get t_name_04_17 => 'Stopfen';

  @override
  String get t_name_04_18 => 'Verriegeln';

  @override
  String get t_name_04_19 => 'Annähen von Knöpfen';

  @override
  String get t_name_04_20 => 'Ösen';

  @override
  String get t_name_04_21 => 'Sternförmige Ösen';

  @override
  String get t_name_05_01 => 'Diagonal nach links oben (Geradstich)';

  @override
  String get t_name_05_02 => 'Rückwärts (Geradstich)';

  @override
  String get t_name_05_03 => 'Diagonal nach rechts oben (Geradstich)';

  @override
  String get t_name_05_04 => 'Seitwärts nach links (Geradstich)';

  @override
  String get t_name_05_05 => 'Seitwärts nach rechts (Geradstich)';

  @override
  String get t_name_05_06 => 'Diagonal nach links unten (Geradstich)';

  @override
  String get t_name_05_07 => 'Vorwärts(Geradstich)';

  @override
  String get t_name_05_08 => 'Diagonal nach rechts unten (Geradstich)';

  @override
  String get t_name_05_09 => 'Seitwärts nach links (Zickzackstich)';

  @override
  String get t_name_05_10 => 'Seitwärts nach rechts (Zickzackstich)';

  @override
  String get t_name_05_11 => 'Vorwärts (Zickzackstich)';

  @override
  String get t_name_05_12 => 'Rückwärts (Zickzackstich)';

  @override
  String get t_name_06_01 => 'Freihand-Kordelaufnähfuß';

  @override
  String get t_name_06_02 => 'Freihand-Heftnähte';

  @override
  String get t_name_06_03 => 'Quiltstich';

  @override
  String get t_name_06_04 => 'Quiltstich';

  @override
  String get t_name_06_05 => 'Quiltstich';

  @override
  String get t_name_06_06 => 'Nadelfilzstich';

  @override
  String get t_name_07_01 => 'Applikationsstich';

  @override
  String get t_name_07_02 => 'Anschrägstich';

  @override
  String get t_name_sr_01 => 'Geradstich (Mitte)';

  @override
  String get t_name_sr_02 => 'Zickzackstich';

  @override
  String get t_name_sr_03 => 'Freihand-Heftnähte';

  @override
  String get tt_head_wifi => 'Wlan Einstellungen';

  @override
  String get tt_head_camera => 'Kameraansicht';

  @override
  String get tt_head_setting => 'Maschineneinstellungen';

  @override
  String get tt_head_teaching => 'Hilfe zur Maschine';

  @override
  String get tt_head_osae => 'Nähfuß-/Nadelwechsel';

  @override
  String get tt_head_lock => 'Bildschirmsperre';

  @override
  String get tt_head_home => 'Startseite';

  @override
  String get tt_foot_clock => 'Uhrzeit-/Datumseinstellungen';

  @override
  String get tt_tch_og_principal_parts1 => '[Nähfußhebel]';

  @override
  String get tt_tch_og_principal_parts2 => '[Nähgeschwindigkeitsregler]';

  @override
  String get tt_tch_og_principal_parts3 => '[Handrad]';

  @override
  String get tt_tch_og_principal_parts4 => '[Zubehörfach]';

  @override
  String get tt_tch_og_mb_knee_lifter => '[Kniehebel]';

  @override
  String get tt_tch_og_principal_parts6 => '[Fußpedal]';

  @override
  String get tt_tch_og_principalbuttons1 => '[Taste \"Nadelposition\"]';

  @override
  String get tt_tch_og_principalbuttons2 => '[Taste \"Fadenabschneider\"]';

  @override
  String get tt_tch_og_principalbuttons3 => '[Taste \"Nähfußhebel\"]';

  @override
  String get tt_tch_og_principalbuttons4 => '[Taste \"Automatisches Einfädeln\"]';

  @override
  String get tt_tch_og_principalbuttons5 => '[Taste \"Start/Stopp\"]';

  @override
  String get tt_tch_og_principalbuttons6 => '[Taste \"Rückwärts\"]';

  @override
  String get tt_tch_og_principalbuttons7 => '[Taste \"Verstärkung/Vernähen\"]';

  @override
  String get tt_tch_og_basic_operation1 => '[Einfädeln des Oberfadens]';

  @override
  String get tt_tch_og_basic_operation2 => '[Aufspulen des Unterfadens]';

  @override
  String get tt_tch_og_basic_operation3 => '[Auswechseln der Nadel]';

  @override
  String get tt_tch_og_basic_operation4 => '[Auswechseln des Nähfußes]';

  @override
  String get tt_tch_og_basic_operation5 => '[Einsetzen der Spule]';

  @override
  String get tt_tch_og_emb_basic_operation1 => '[Einstellung der Fadenspannung]';

  @override
  String get tt_tch_og_emb_basic_operation2 => '[Aufbügeln von Bügelvlies (Unterlegmaterial) am Stoff]';

  @override
  String get tt_tch_og_emb_basic_operation3 => '[Einspannen des Stoffes]';

  @override
  String get tt_tch_og_emb_basic_operation4 => '[Einsetzen des Stickrahmens]';

  @override
  String get tt_tch_og_emb_basic_operation5 => '[Anbringen der Stickeinheit]';

  @override
  String get tt_tch_og_emb_basic_operation6 => '[Anbringen des Stickfußes \"W\"]';

  @override
  String get tt_tch_og_emb_basic_operation7 => '[Richtiger Einsatz von Unterlegmaterial]';

  @override
  String get tt_tch_maintenance1 => '[Reinigen von Greifergehäuse und Spulenkapsel]';

  @override
  String get tt_utl_category01 => 'Geradstich/Überwendlingstich';

  @override
  String get tt_utl_category02 => 'Dekorstiche';

  @override
  String get tt_utl_category03 => 'Hohlsaumstiche';

  @override
  String get tt_utl_category04 => 'Knopflöcher/Verriegeln';

  @override
  String get tt_utl_category05 => 'Nähen in verschiedene Richtungen';

  @override
  String get tt_utl_category_q => 'Quiltstiche';

  @override
  String get tt_utl_category_s => 'Weitere Stiche';

  @override
  String get tt_utl_category_t => 'Tapering-Stiche';

  @override
  String get tt_utl_stitchpreview => 'Vorschau';

  @override
  String get tt_utl_projecter => 'Projektorfunktionen';

  @override
  String get tt_utl_guideline => 'Führungslinien';

  @override
  String get tt_utl_editmenu => 'Bearbeitungsmenü';

  @override
  String get tt_utl_freemotion => 'Freihandnähmodus';

  @override
  String get tt_utl_repeat_stitch_atamadashi => 'Zurück zum Anfang';

  @override
  String get tt_utl_alone_repeat => 'Einzel-/Aneinandergefügte Stiche';

  @override
  String get tt_utl_utilityflipvertical => 'Bild spiegeln';

  @override
  String get tt_utl_twinneedle => 'Einzelnadel/Zwillingsnadel';

  @override
  String get tt_utl_buttonholemanual => 'Schlitzlänge für Knopfloch';

  @override
  String get tt_utl_endpointsetting => 'Endpunkteinstellung';

  @override
  String get tt_utl_tapering => 'Tapering-Stiche';

  @override
  String get tt_utl_autoreverse => 'Automatisches Vernähen';

  @override
  String get tt_utl_scissor => 'Automatisches Fadenabschneiden';

  @override
  String get tt_utl_needlestopposition => 'Einstellung der Nadel-Stopp-Position';

  @override
  String get tt_utl_pivot => 'Drehen/Auto nach oben';

  @override
  String get tt_utl_threadcolor => 'Garnfarbenwechsel';

  @override
  String get tt_utl_category06 => 'Verschiedene breite Muster';

  @override
  String get tt_utl_category07 => 'Breite Pflanzenmotive';

  @override
  String get tt_utl_category08 => 'Breite Motive und Texte';

  @override
  String get tt_utl_category09 => 'Verschiedene schmale Muster';

  @override
  String get tt_utl_category10 => 'Schmale Pflanzenmotive';

  @override
  String get tt_utl_category11 => 'Weißstickerei';

  @override
  String get tt_utl_category12 => 'Große Satinstiche';

  @override
  String get tt_utl_category13 => 'Satinstiche';

  @override
  String get tt_utl_category14 => 'Kreuzstiche';

  @override
  String get tt_utl_category15 => 'Dekorative Nutzstiche';

  @override
  String get tt_utl_category16 => 'Disney';

  @override
  String get tt_utl_category17 => 'Schriftart Gotisch';

  @override
  String get tt_utl_category18 => 'Schriftart Handschrift';

  @override
  String get tt_utl_category19 => 'Outlineschriftart';

  @override
  String get tt_utl_category20 => 'Kyrillische Schriftart';

  @override
  String get tt_deco_category_pocket => 'Tasche(Maschinen-/externer Speicher)';

  @override
  String get tt_deco_mycustomsititch => 'Funktion „MY CUSTOM STITCH (MEIN STICHDESIGN)“';

  @override
  String get tt_deco_stitchpreview => 'Vorschau';

  @override
  String get tt_deco_projecter => 'Projektorfunktionen';

  @override
  String get tt_deco_guidline => 'Führungslinien';

  @override
  String get tt_deco_editmenu => 'Bearbeitungsmenü';

  @override
  String get tt_deco_memory => 'Stichmusterdaten speichern';

  @override
  String get tt_deco_threadcolor => 'Garnfarbenwechsel';

  @override
  String get tt_deco_stitchplus => 'Stichmuster hinzufügen';

  @override
  String get tt_deco_stitchselectall => '„Alle auswählen“ ein/aus';

  @override
  String get tt_deco_pivot => 'Drehen/Auto nach oben';

  @override
  String get tt_deco_needlestopposition => 'Einstellung der Nadel-Stopp-Position';

  @override
  String get tt_deco_scissor => 'Automatisches Fadenabschneiden';

  @override
  String get tt_deco_autoreverse => 'Automatisches Vernähen';

  @override
  String get tt_deco_stitchstep1 => 'Stufeneffekt';

  @override
  String get tt_deco_stitchstep2 => 'Stufeneffekt';

  @override
  String get tt_deco_filemanager => 'Dateimanager';

  @override
  String get tt_deco_filemanager_selectall => 'Alle auswählen';

  @override
  String get tt_deco_filemanager_selectnone => 'Auswahl aufheben';

  @override
  String get tt_deco_filemanager_delete => 'Löschen';

  @override
  String get tt_deco_filemanager_memory => 'Ausgewählte Stichmuster in der Maschine speichern.';

  @override
  String get tt_deco_freemotion => 'Freihandnähmodus';

  @override
  String get tt_deco_repeat_stitch_atamadashi => 'Zurück zum Anfang';

  @override
  String get tt_deco_alone_repeat => 'Einzel-/Aneinandergefügte Stiche';

  @override
  String get tt_deco_utilityfliphorizon => 'Bild horizontal spiegeln';

  @override
  String get tt_deco_utilityflipvertical => 'Bild vertikal spiegeln';

  @override
  String get tt_deco_alone_single => 'Einzelnadel/Zwillingsnadel';

  @override
  String get tt_deco_delete => 'Löschen';

  @override
  String get tt_deco_density => 'Fadenstärke';

  @override
  String get tt_deco_elongator => 'Verlängerung';

  @override
  String get tt_deco_spacing => 'Buchstabenabstand';

  @override
  String get tt_deco_stitchsizelink => 'Seitenverhältnis beibehalten';

  @override
  String get tt_deco_endpointsetting => 'Endpunkteinstellung';

  @override
  String get tt_mcs_triplesewing => 'Einfach-/Dreifachstich';

  @override
  String get tt_mcs_pointdelete => 'Punkt löschen';

  @override
  String get tt_mcs_blockmove => 'Block verschieben';

  @override
  String get tt_mcs_insert => 'Einfügen';

  @override
  String get tt_utl_mcspointset => 'Einstellen';

  @override
  String get tt_mcs_contents => 'Stichmuster importieren';

  @override
  String get tt_mcs_memory => 'Stichmusterdaten speichern';

  @override
  String get tt_utl_sr_guideline => 'Führungslinien';

  @override
  String get tt_utl_sr_sensingline => 'Linienerkennung';

  @override
  String get tt_utl_sr_srstatus => 'Stichreglerstatus';

  @override
  String get tt_embcate_embpatterns => 'Stickmuster';

  @override
  String get tt_embcate_character => 'Buchstabenmuster';

  @override
  String get tt_embcate_decoalphabet => 'Dekor-Buchstabenmuster';

  @override
  String get tt_embcate_frame => 'Umrandungen';

  @override
  String get tt_embcate_utility => 'Knopflochmuster/ Nutzstich-Stickmuster';

  @override
  String get tt_embcate_split => 'Geteilte Stickmuster';

  @override
  String get tt_embcate_long_stitch => 'Langstich-Stickmuster';

  @override
  String get tt_embcate_quilt => 'Quilt-Streifen und Edge-to-Edge-Quiltmuster';

  @override
  String get tt_embcate_b_disney => 'Disney-Muster';

  @override
  String get tt_embcate_couching => 'Couching-Stichmuster';

  @override
  String get tt_embcate_t_exclusives => 'Exclusives';

  @override
  String get tt_embcate_memory => 'Im Speicher der Maschine, auf USB-Stick usw. gespeicherte Muster';

  @override
  String get tt_emb_pantool => 'Hand-Werkzeug';

  @override
  String get tt_emb_backgroundscan => 'Stoff-Scan';

  @override
  String get tt_emb_realpreview => 'Vorschau';

  @override
  String get tt_emb_memory => 'Speicher';

  @override
  String get tt_emb_redo => 'Wiederholen';

  @override
  String get tt_emb_undo => 'Rückgängig';

  @override
  String get tt_emb_delete => 'Löschen';

  @override
  String get tt_emb_select => 'Wählen';

  @override
  String get tt_emb_multipleselect => 'Mehrfachauswahl';

  @override
  String get tt_emb_editsize => 'Größe';

  @override
  String get tt_emb_editmove => 'Verschieben';

  @override
  String get tt_emb_editgroup => 'Gruppieren/Gruppierung aufheben';

  @override
  String get tt_emb_editrotate => 'Drehen';

  @override
  String get tt_emb_editflip => 'Horizontal spiegeln';

  @override
  String get tt_emb_editduplicate => 'Duplizieren';

  @override
  String get tt_emb_editdensity => 'Stichdichte';

  @override
  String get tt_emb_editborder => 'Randfunktion (Entwerfen von wiederholten Mustern)';

  @override
  String get tt_emb_editapplique => 'Applikation';

  @override
  String get tt_emb_editchangecolor => 'Farbenpalette';

  @override
  String get tt_emb_edittextedit => 'Buchstabenmuster bearbeiten';

  @override
  String get tt_emb_editalign => 'Ausrichtung';

  @override
  String get tt_emb_editstippling => 'Punktieren';

  @override
  String get tt_emb_editoutline => 'Stempelfunktion';

  @override
  String get tt_emb_editorder => 'Stickreihenfolge';

  @override
  String get tt_emb_editnotsew => 'Nicht sticken';

  @override
  String get tt_emb_textsize => 'Größe';

  @override
  String get tt_emb_textarray => 'Anordnung';

  @override
  String get tt_emb_textspacing => 'Buchstabenabstand';

  @override
  String get tt_emb_textalign => 'Ausrichtung';

  @override
  String get tt_emb_embfootw => 'Nadeleinstichpunkt prüfen';

  @override
  String get tt_emb_emb_projectorsetting => 'Projektoreinstellungen';

  @override
  String get tt_emb_embprojector => 'Projektor';

  @override
  String get tt_emb_embmove => 'Verschieben';

  @override
  String get tt_emb_embrotate => 'Drehen';

  @override
  String get tt_emb_embbasting => 'Heftstich';

  @override
  String get tt_emb_embsnowman => 'Stickposition';

  @override
  String get tt_emb_embonecolorsew => 'Ununterbrochenes Sticken';

  @override
  String get tt_emb_embcolorsorting => 'Farbsortierung';

  @override
  String get tt_emb_embconnectsew => 'Muster verbinden';

  @override
  String get tt_emb_embframemove => 'Verschieben des Rahmens: Der Rahmen wird vorübergehend in die Mitte verschoben.';

  @override
  String get tt_emb_embmemory => 'Speicher';

  @override
  String get tt_emb_embmasktrace => 'Stickmuster umfahren';

  @override
  String get tt_emb_embstartposition => 'Anfangspunkt';

  @override
  String get tt_emb_embneedlenumber => 'Vorwärts/Zurück';

  @override
  String get tt_emb_embfbcamera => 'Kameraansicht';

  @override
  String get tt_emb_embthreadcutting => 'Schneiden/Fadenspannung';

  @override
  String get tt_emb_embcolorbar => 'Anzeige von einer Farbe/allen Farben auf Fortschrittsbalken';

  @override
  String get tt_emb_patterninfo => 'Musterinformationen';

  @override
  String get tt_emb_previewsim => 'Stichsimulation';

  @override
  String get tt_emb_sewtrim_endcolor => 'Farbenende abschneiden';

  @override
  String get tt_emb_sewtrim_jumpstitch => 'Sprungstiche abschneiden';

  @override
  String get tt_emb_previewframe => 'Vorschau des Stickrahmens';

  @override
  String get tt_emb_size_normalstb => 'Mustergröße unter Beibehaltung der Stichanzahl/Fadendichte ändern';

  @override
  String get tt_emb_edit_border_vert => 'Muster vertikal wiederholen/löschen';

  @override
  String get tt_emb_edit_border_horiz => 'Muster horizontal wiederholen/löschen';

  @override
  String get tt_emb_edit_border_dividervert => 'Muster vertikal schneiden';

  @override
  String get tt_emb_edit_border_dividehoriz => 'Muster horizontal schneiden';

  @override
  String get tt_emb_edit_border_threadmark => 'Garnmarkierungen';

  @override
  String get tt_emb_edit_border_reset => 'Zurücksetzen';

  @override
  String get tt_emb_emb_rotate_reset => 'Zurücksetzen';

  @override
  String get tt_emb_edit_rotate_reset => 'Zurücksetzen';

  @override
  String get tt_emb_camera_rotate_reset => 'Zurücksetzen';

  @override
  String get tt_emb_edit_font_spacing_reset => 'Zurücksetzen';

  @override
  String get tt_emb_edit_align_reset => 'Zurücksetzen';

  @override
  String get tt_emb_edit_size_reset => 'Zurücksetzen';

  @override
  String get tt_emb_edit_order_reset => 'Zurücksetzen';

  @override
  String get tt_emb_quiltborder_color_reset => 'Zurücksetzen';

  @override
  String get tt_emb_edit_color_reset => 'Zurücksetzen';

  @override
  String get tt_emb_photositich_size_change_reset => 'Zurücksetzen';

  @override
  String get tt_emb_edit_projlcd_switch_fb_reset => 'Zurücksetzen';

  @override
  String get tt_emb_edit_projlcd_align_reset => 'Zurücksetzen';

  @override
  String get tt_emb_edit_projlcd_border_reset => 'Zurücksetzen';

  @override
  String get tt_emb_edit_projlcd_rotate_reset => 'Zurücksetzen';

  @override
  String get tt_emb_edit_projlcd_size_reset => 'Zurücksetzen';

  @override
  String get tt_mdc_paint_rotate_reset => 'Zurücksetzen';

  @override
  String get tt_mdc_paint_size_input_reset => 'Zurücksetzen';

  @override
  String get tt_mdc_paint_size_reset => 'Zurücksetzen';

  @override
  String get tt_emb_newapplique_color_selectall => 'Alle auswählen';

  @override
  String get tt_emb_newapplique_color_selectnone => 'Auswahl aufheben';

  @override
  String get tt_emb_color_selectall => 'Einzelne Farbe/alle Farben auswählen';

  @override
  String get tt_emb_colorcolorshuffling => 'Color Shuffling(Zufällige Farbkombinationen)';

  @override
  String get tt_emb_colorvisualizer => 'Color Visualizer';

  @override
  String get tt_emb_editselectall => 'Alle auswählen';

  @override
  String get tt_emb_editdeselectall => 'Auswahl aufheben';

  @override
  String get tt_emb_infoprintimage => 'Mit Druckbild kombiniert';

  @override
  String get tt_emb_infooutputfiles => '3 PDF-Dateien (für bedruckbaren Stoff/zum Aufbügeln per Transferpapier/für Positionierung) werden auf das USB-Medium kopiert.';

  @override
  String get tt_emb_filemanager => 'Dateimanager';

  @override
  String get tt_emb_filemanager_selectall => 'Alle auswählen';

  @override
  String get tt_emb_filemanager_selectnone => 'Auswahl aufheben';

  @override
  String get tt_emb_filemanager_delete => 'Löschen';

  @override
  String get tt_emb_filemanager_memory => 'Ausgewählte Muster in der Maschine speichern.';

  @override
  String get tt_emb_easystippling_stippling => 'Punktierstichmuster';

  @override
  String get tt_emb_easystippling_echo => 'Echoquiltmuster';

  @override
  String get tt_emb_easystippling_decorativefill => 'Dekorfüllungsmuster';

  @override
  String get tt_emb_quitlsash_startpoint => 'Startpunkt projizieren';

  @override
  String get tt_emb_quitlsash_endtpoint => 'Endpunkt projizieren';

  @override
  String get tt_emb_connect_migimawari => 'Die zweite Musterposition wird im Uhrzeigersinn gedreht';

  @override
  String get tt_emb_connect_hidarimawari => 'Die zweite Musterposition wird gegen den Uhrzeigersinn gedreht';

  @override
  String get tt_emb_connect_rotate => 'Drehen';

  @override
  String get tt_emb_quiltborder_save => 'Speicher';

  @override
  String get tt_mdc_pantool => 'Hand-Werkzeug';

  @override
  String get tt_mdc_scanmenu => 'Eigene Musterdesigns können aus eingescannten Bildern oder Bild-Datendateien erstellt werden.';

  @override
  String get tt_mdc_datacall => 'Musterzeichnungsdaten (.pm9) abrufen';

  @override
  String get tt_mdc_linetool => 'Linienwerkzeug';

  @override
  String get tt_mdc_lineproperty => 'Linieneigenschaften';

  @override
  String get tt_mdc_linespoit => 'Pipettenwerkzeug für Linie';

  @override
  String get tt_mdc_linepouring => 'Füllwerkzeug für Linie';

  @override
  String get tt_mdc_brushtool => 'Pinselwerkzeug';

  @override
  String get tt_mdc_brushproperty => 'Bereichseigenschaften';

  @override
  String get tt_mdc_brushspoit => 'Pipettenwerkzeug für Region';

  @override
  String get tt_mdc_brushpouring => 'Füllwerkzeug für Region';

  @override
  String get tt_mdc_painteraser => 'Radierer';

  @override
  String get tt_mdc_paintstamp => 'Umrandungsformen';

  @override
  String get tt_mdc_paintsize => 'Größe';

  @override
  String get tt_mdc_paintrotate => 'Drehen';

  @override
  String get tt_mdc_paintflip => 'Bild spiegeln';

  @override
  String get tt_mdc_paintduplicate => 'Kopieren';

  @override
  String get tt_mdc_paintcut => 'Ausschneiden';

  @override
  String get tt_mdc_paintpaste => 'Einfügen';

  @override
  String get tt_mdc_memory => 'Musterzeichnungsdaten (.pm9) speichern';

  @override
  String get tt_mdc_select => 'Wählen';

  @override
  String get tt_mdc_redo => 'Wiederholen';

  @override
  String get tt_mdc_undo => 'Rückgängig';

  @override
  String get tt_mdc_allclear => 'Alles Löschen';

  @override
  String get tt_mdc_lineopen => 'Freihandlinie mit offenem Ende';

  @override
  String get tt_mdc_lineclose => 'Freihandlinie, die das Ende automatisch schließt';

  @override
  String get tt_mdc_lineline => 'Gerade Linie mit einem Strich';

  @override
  String get tt_mdc_linepolygonal => 'Polygonform';

  @override
  String get tt_mdc_stitchzigzag => 'Zickzackstich';

  @override
  String get tt_mdc_stitchrunning => 'Geradstich';

  @override
  String get tt_mdc_stitchtriple => 'Dreifachstich';

  @override
  String get tt_mdc_stitchcandle => 'Candlewicking-Stich';

  @override
  String get tt_mdc_stitchchain => 'Kettenstich';

  @override
  String get tt_mdc_stitchestitch => 'E-Stich';

  @override
  String get tt_mdc_stitchvsitich => 'V-Stich';

  @override
  String get tt_mdc_stitchmotif => 'Motivstiche';

  @override
  String get tt_mdc_stitchnnotsew => 'Linie ohne Stich';

  @override
  String get tt_mdc_stitchzigzaglowdensity => 'Applikationszickzackstich';

  @override
  String get tt_mdc_regiontatami => 'Füllstichmuster';

  @override
  String get tt_mdc_regionstippling => 'Punktierstichmuster';

  @override
  String get tt_mdc_regiondecorativefill => 'Dekorfüllungsmuster';

  @override
  String get tt_mdc_regionnotsew => 'Keine Stiche';

  @override
  String get tt_mdc_stamp1 => 'Grundformen';

  @override
  String get tt_mdc_stamp2 => 'Geschlossene Formen';

  @override
  String get tt_mdc_stamp3 => 'Offene Formen';

  @override
  String get tt_mdc_stamp4 => 'Gespeicherte Umrisse';

  @override
  String get tt_mdc_stamp5 => 'Rahmenstickbereiche';

  @override
  String get tt_mdc_stamp6 => 'Schnittumrisse';

  @override
  String get tt_mdc_select_rectangle => 'Fensterauswahl';

  @override
  String get tt_mdc_select_continuousrectangle => 'Auswahl Polygonform';

  @override
  String get tt_mdc_select_free => 'Freihand-Kurvenauswahl';

  @override
  String get tt_mdc_select_auto => 'Automatische Auswahl';

  @override
  String get tt_mdc_select_all => 'Alle auswählen';

  @override
  String get tt_mdc_memory_drawemb => 'Musterzeichnungsdaten (.pm9) und Stickdaten (.phx) speichern.';

  @override
  String get tt_mdc_embset_pantool => 'Hand-Werkzeug';

  @override
  String get tt_mdc_embset_patterninfo => 'Musterinformationen';

  @override
  String get tt_mdc_embset_realpreview => 'Vorschau';

  @override
  String get tt_mdc_embset_projector => 'Projektor';

  @override
  String get tt_mdc_embset_projectorsetting => 'Projektoreinstellungen';

  @override
  String get tt_mdc_zigzagwidth => 'Zickzack-Breite';

  @override
  String get tt_mdc_zigzagdensity => 'Dichte';

  @override
  String get tt_mdc_runpitch => 'Laufweite';

  @override
  String get tt_mdc_running_undersew => 'Unternähen';

  @override
  String get tt_mdc_candlewicksize => 'Größe';

  @override
  String get tt_mdc_candlewickspacing => 'Abstand';

  @override
  String get tt_mdc_chainsize => 'Größe';

  @override
  String get tt_mdc_chainthickness => 'Dicke';

  @override
  String get tt_mdc_estitchwidth => 'Stichbreite';

  @override
  String get tt_mdc_estitchspacing => 'Abstand';

  @override
  String get tt_mdc_estitchthickness => 'Dicke';

  @override
  String get tt_mdc_estitchflip => 'Spiegeln';

  @override
  String get tt_mdc_vstitchwidth => 'Stichbreite';

  @override
  String get tt_mdc_vstitchspacing => 'Abstand';

  @override
  String get tt_mdc_vstitchthickness => 'Dicke';

  @override
  String get tt_mdc_vstitchflip => 'Spiegeln';

  @override
  String get tt_mdc_motifstitchsize => 'Größe';

  @override
  String get tt_mdc_motifstitchspacing => 'Abstand';

  @override
  String get tt_mdc_motifstitchflip => 'Spiegeln';

  @override
  String get tt_mdc_zigzagwidth_2 => 'Zickzack-Breite';

  @override
  String get tt_mdc_zigzagdensity_2 => 'Dichte';

  @override
  String get tt_mdc_tatamiderection => 'Richtung';

  @override
  String get tt_mdc_tatamidensity => 'Dichte';

  @override
  String get tt_mdc_tatamipullconpen => 'Zug-Kompensation';

  @override
  String get tt_mdc_tatamiundersewing => 'Unternähen';

  @override
  String get tt_mdc_stiprunpitch => 'Laufweite';

  @override
  String get tt_mdc_stipspacing => 'Abstand';

  @override
  String get tt_mdc_stipdistance => 'Abstand';

  @override
  String get tt_mdc_stipsingletriple => 'Einzel-/Dreifachstich';

  @override
  String get tt_mdc_decofillsize => 'Größe';

  @override
  String get tt_mdc_decofilldirection => 'Richtung';

  @override
  String get tt_mdc_decofilloutline => 'Umrisslinien für selteneres Fadenabschneiden';

  @override
  String get tt_mdc_decofillrandomshift => 'Zufällige Verzerrung';

  @override
  String get tt_mdc_decofillpositionoffset => 'Positionsversatz';

  @override
  String get tt_mdc_decofillthickness1 => 'Dicke';

  @override
  String get tt_mdc_decofillthickness3 => 'Dicke';

  @override
  String get tt_mdc_decofillthickness1_2 => 'Einfach-doppelt';

  @override
  String get tt_mdc_decofillthickness2_3 => 'Doppelt-dreifach';

  @override
  String get tt_mdc_stitchlink => 'Objekte mit denselben Sticheinstellungen gleichzeitig auswählen';

  @override
  String get tt_mdc_fill_linereading => 'Linienförmige Flächen und Umrandungen werden in Umrisslinien konvertiert. Umrissstärke festlegen.';

  @override
  String get tt_mdc_fill_linecolor => 'Die extrahierten Umrisslinien mit der angegebenen Farbe werden in Linienattribute konvertiert.';

  @override
  String get tt_emb_photostitch_backremoval => 'Hintergrund entfernen';

  @override
  String get tt_emb_photostitch_framing => 'Rahmen des Bildes';

  @override
  String get tt_emb_photostitch_fittoframe => 'An Rahmen anpassen';

  @override
  String get tt_emb_photostitch_backremoval_scopeplus => 'Fügen Sie einen neuen Bereich zum Beschneiden hinzu:Markieren Sie den Bereich, den Sie beschneiden möchten, mit einer Linie.';

  @override
  String get tt_emb_photostitch_backremoval_scopeminus => 'Löschen Sie den Bereich zum Beschneiden:Markieren Sie den Bereich, den Sie nicht beschneiden möchten, mit einer Linie.';

  @override
  String get tt_emb_photostitch_backremoval_erase => 'Löschen Sie die angegebene Zeichnungslinie.';

  @override
  String get tt_emb_photostitch_backremoval_trash => 'Löschen Sie alle Zeichnungslinien.';

  @override
  String get tt_emb_photostitch_backremoval_blind => 'Alle per Stift gezeichneten Linien ein-/ausblenden.';

  @override
  String get tt_emb_photostitch_styleusecolor => 'EIN: Farben des Stilbildes verwenden/AUS: Farben des Originalfotos verwenden';

  @override
  String get tt_emb_photostitch_colortune => 'Farbanpassung';

  @override
  String get tt_emb_photostitch_colorlis_allselect => 'Alle Garnfarben in der Farbenliste beibehalten/löschen';

  @override
  String get tt_emb_photostitch_colorlis_add => 'Farbe zur Farbenliste hinzufügen';

  @override
  String get tt_emb_photostitch_colorlis_remove => 'Die gewählte Farbe aus der Farbenliste entfernen';

  @override
  String get tt_emb_photostitch_pantool => 'Hand-Werkzeug';

  @override
  String get tt_emb_photostitch_memory => 'Speicher';

  @override
  String get tt_emb_edit_projectorsetting => 'Projektoreinstellungen';

  @override
  String get tt_emb_edit_projector => 'Projektor';

  @override
  String get tt_settings_reset_3type => 'Einstellungen (Nähen/Allgemein/Sticken) zurücksetzen';

  @override
  String get tt_settings_screenimage_usb => 'Einstellungsbildschirm auf ein USB-Medium speichern';

  @override
  String get tt_camera_emb_screenshot => 'Ein Kamerabild auf dem USB-Medium speichern.';

  @override
  String get tt_camera_emb_grid => 'Raster ein-/ausblenden';

  @override
  String get tt_camera_emb_needlepoint => 'Nadeleinstichpunkt ein-/ausblenden';

  @override
  String get tt_camera_util_screenshot => 'Ein Kamerabild auf dem USB-Medium speichern.';

  @override
  String get tt_camera_util_grid => 'Raster ein-/ausblenden';

  @override
  String get tt_camera_util_needlepoint => 'Nadeleinstichpunkt ein-/ausblenden';
}
