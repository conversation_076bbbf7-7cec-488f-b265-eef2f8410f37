import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'size_adjustment_view_interface.freezed.dart';

@freezed
class SizeAdjustmentState with _$SizeAdjustmentState {
  const factory SizeAdjustmentState({
    @Default(false) bool isInch,
    @Default(false) bool isEnglish,
    @Default(false) bool isTabDisable,
    @Default(false) bool isPatternDoSTB,
    @Default('----') String widthValue,
    @Default('----') String heightValue,
    @Default(false) bool isScanPopupOpen,
  }) = _SizeAdjustmentState;
}

abstract class SizeAdjustmentViewModelInterface
    extends ViewModel<SizeAdjustmentState> {
  SizeAdjustmentViewModelInterface(
    super.state,
    this.ref,
  );

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// ステータスが変更されたときの更新
  ///
  void updateByScanPopupChange();

  /// 終わり
  void sizeAdjustmentCancel();

  ///
  /// 拡縮の種類選択のクリック関数
  ///
  void onSTBChangeButtonClicked();

  ///
  /// 元に戻すキーのクリック関数
  ///
  void onResetButtonClicked();

  ///
  /// OKボタンのクリック関数
  ///
  void onOKButtonClick(BuildContext context);

  ///
  /// 横方向縮小拡大ボタンが関数をクリックします。
  ///
  bool onSizeTabWidthEnlargeButton(bool isLongPress);
  bool onSizeTabWidthReduceButton(bool isLongPress);

  ///
  /// 縦方向縮小拡大ボタンが関数をクリックします。
  ///
  bool onSizeTabHeightEnlargeButton(bool isLongPress);
  bool onSizeTabHeightReduceButton(bool isLongPress);

  ///
  /// 横縦方向縮小拡大ボタンが関数をクリックします。
  ///
  bool onSizeTabEnlargeButton(bool isLongPress);
  bool onSizeTabReduceButton(bool isLongPress);
}
