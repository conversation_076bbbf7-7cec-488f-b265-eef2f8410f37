import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import '../../global_popup_route.dart';
import 'err_change_l_or_lm_frame_view_interface.dart';

class ErrChangeLOrLmFrameArgument {
  const ErrChangeLOrLmFrameArgument({
    this.onOKButtonClicked,
  });

  ///
  /// okボタンクリク関数
  ///
  final void Function(BuildContext context)? onOKButtonClicked;
}

final errChangeLOrLmFrameViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrChangeLOrLmFrameViewInterface, ErrChangeLOrLmFrameState,
            BuildContext>(
        (ref, context) => ErrChangeLOrLmFrameViewModel(ref, context));

class ErrChangeLOrLmFrameViewModel extends ErrChangeLOrLmFrameViewInterface {
  ErrChangeLOrLmFrameViewModel(Ref ref, this._context)
      : _argument = GlobalPopupRoute().getArgument(context: _context),
        super(const ErrChangeLOrLmFrameState(), ref, _context);

  final ErrChangeLOrLmFrameArgument? _argument;
  final BuildContext _context;

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    int errcode;
    errcode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERRFRAMEOFF);
    if (errcode == BPIFSendError_t.bpifNoError.index) {
      _argument?.onOKButtonClicked?.call(_context);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }
  }
}
