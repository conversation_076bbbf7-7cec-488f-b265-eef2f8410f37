import 'dart:ui';

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../common_component/zoom_selector_popup.dart';
import 'parameter_set_view_model.dart';

/// オートリピート時間
const int keyRepeat500 = 500;

/// 長押しトリガーティック
const int keyTrigger2 = 2;

class ParameterSet extends ConsumerStatefulWidget {
  const ParameterSet({super.key});
  @override
  ConsumerState<ParameterSet> createState() => _ParameterSetState();
}

class _ParameterSetState extends ConsumerState<ParameterSet> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(parameterSetViewModelProvider);
    final viewModel = ref.read(parameterSetViewModelProvider.notifier);
    return Stack(
      children: [
        Column(
          children: [
            const Spacer(
              flex: 40,
            ),
            Expanded(
              flex: 630,
              child: Row(
                children: [
                  const Spacer(
                    flex: 11,
                  ),
                  Expanded(
                    flex: 133,
                    child: grp_btn_zoom(
                      onTap: viewModel.onZoomButtonClicked,
                      child: [
                        const ico_zoom100(),
                        const ico_zoom125(),
                        const ico_zoom150(),
                        const ico_zoom200(),
                        const ico_zoom400(),
                      ][state.zoomScaleIndex],
                    ),
                  ),
                  const Spacer(
                    flex: 8,
                  ),
                  Expanded(
                    flex: 63,
                    child: grp_btn_pan_tool(
                      onTap: viewModel.onHandleButtonClicked,
                      state: state.isHandleSelected
                          ? ButtonState.select
                          : ButtonState.normal,
                    ),
                  ),
                  const Spacer(
                    flex: 42,
                  ),
                  Expanded(
                    flex: 63,
                    child: CustomTooltip(
                      message: l10n.tt_emb_realpreview,
                      child: grp_btn_emb_realpreview_01(
                        state: state.realPreviewButtonState,
                        onTap: () => viewModel.onRealPreviewClicked(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(
              flex: 180,
            ),
            Expanded(
              flex: 830,
              child: Row(
                children: [
                  const Spacer(
                    flex: 11,
                  ),
                  Expanded(
                    flex: 119,
                    child: Column(
                      children: [
                        Expanded(
                          flex: 63,
                          child: [
                            const ico_thumnail_size00(),
                            const ico_thumnail_size01(),
                            const ico_thumnail_size02(),
                            const ico_thumnail_size03(),
                          ][state.frameIndex],
                        ),
                        const Spacer(
                          flex: 20,
                        ),
                      ],
                    ),
                  ),
                  const Spacer(
                    flex: 37,
                  ),
                  const Expanded(
                    flex: 28,
                    child: Column(
                      children: [
                        Spacer(
                          flex: 23,
                        ),
                        Expanded(
                          flex: 28,
                          child: ico_onesizeicon(),
                        ),
                        Spacer(
                          flex: 32,
                        ),
                      ],
                    ),
                  ),
                  const Spacer(
                    flex: 11,
                  ),
                  Expanded(
                    flex: 80,
                    child: Column(
                      children: [
                        const Spacer(
                          flex: 4,
                        ),
                        Expanded(
                          flex: 39,
                          child: grp_str_number1_01(
                            text: state.patternHight,
                          ),
                        ),
                        Expanded(
                          flex: 39,
                          child: grp_str_number2_01(
                            text: state.patternWidth,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(
                    flex: 1,
                  ),
                  state.isInch
                      ? Expanded(
                          flex: 36,
                          child: Column(
                            children: [
                              const Spacer(
                                flex: 11,
                              ),
                              Expanded(
                                flex: 30,
                                child: grp_str_mm1_01(
                                    text: l10n.icon_00226,
                                    alignment: Alignment.topLeft),
                              ),
                              const Spacer(
                                flex: 10,
                              ),
                              Expanded(
                                flex: 30,
                                child: grp_str_mm2_01(
                                    text: l10n.icon_00226,
                                    alignment: Alignment.topLeft),
                              ),
                            ],
                          ),
                        )
                      : Expanded(
                          flex: 36,
                          child: Column(
                            children: [
                              const Spacer(
                                flex: 15,
                              ),
                              Expanded(
                                flex: 26,
                                child: grp_str_mm1_01(
                                  text: l10n.icon_00225,
                                ),
                              ),
                              const Spacer(
                                flex: 14,
                              ),
                              Expanded(
                                flex: 26,
                                child: grp_str_mm2_01(
                                  text: l10n.icon_00225,
                                ),
                              ),
                              const Spacer(
                                flex: 2,
                              ),
                            ],
                          ),
                        ),
                ],
              ),
            ),
            const Spacer(
              flex: 340,
            ),
            Expanded(
              flex: 700,
              child: Row(
                children: [
                  const Spacer(
                    flex: 11,
                  ),
                  Expanded(
                    flex: 297,
                    child: GestureDetector(
                      onTap: () => viewModel.onOpenFontPopupClicked(context),
                      child: grp_ddb_std_fonttype(
                        text: state.isExclusiveType ? null : state.fontType,
                        image: state.fontTypeImage,
                      ),
                    ),
                  ),
                  const Spacer(
                    flex: 15,
                  ),
                ],
              ),
            ),
            const Spacer(
              flex: 360,
            ),
            Expanded(
              flex: 630,
              child: Row(
                children: [
                  const Spacer(
                    flex: 11,
                  ),
                  Expanded(
                    flex: 133,
                    child: grp_ddb_std_fontsize(
                      text: state.fontSize,
                    ),
                  ),
                  const Spacer(
                    flex: 30,
                  ),
                  Expanded(
                    flex: 63,
                    child: grp_btn_minus_01(
                      onTap: () => viewModel.onMinusButtonOnClicked(false),
                      onLongPress: () => viewModel.onMinusButtonOnClicked(true),
                      feedBackControl: null,
                      repeatDurationMS: keyRepeat500,
                      longPressTriggerTick: keyTrigger2,
                    ),
                  ),
                  const Spacer(
                    flex: 8,
                  ),
                  Expanded(
                    flex: 63,
                    child: grp_btn_plus_01(
                      onTap: () => viewModel.onPlusButtonOnClicked(false),
                      onLongPress: () => viewModel.onPlusButtonOnClicked(true),
                      feedBackControl: null,
                      repeatDurationMS: keyRepeat500,
                      longPressTriggerTick: keyTrigger2,
                    ),
                  ),
                  const Spacer(
                    flex: 15,
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 2400,
              child: Row(
                children: [
                  const Spacer(flex: 115),
                  Expanded(
                    flex: 2940,
                    child: Container(
                      width: 294,
                      height: 218.5,
                      alignment: Alignment.topLeft,
                      child: Column(
                        children: [
                          const Spacer(flex: 440),
                          Expanded(
                            flex: 1575,
                            child: LayoutBuilder(
                              builder: (context, constraints) => Stack(
                                children: [
                                  SizedBox(
                                    width: constraints.maxWidth,
                                    height: constraints.maxHeight,
                                  ),

                                  /// テキスト ボックスの描画
                                  ...() {
                                    List<Widget> widget = [];
                                    for (ShowDataList list
                                        in state.disPlayText) {
                                      widget.add(
                                        Positioned(
                                          left: list.disPlayCharMarginLeft,
                                          width: list.disPlayCharWidth,
                                          height: 35,
                                          top: list.disPlayCharMarginTop,
                                          child: Text(
                                            list.disPlayCharUniCode,
                                            style: const TextStyle(
                                              fontFamily: "Roboto",
                                              fontSize: 35,
                                              fontWeight: FontWeight.bold,
                                              height: 0,
                                              letterSpacing: 0,
                                            ),
                                          ),
                                        ),
                                      );
                                    }

                                    return widget;
                                  }(),

                                  /// 点線のボックスが描画されます
                                  CustomPaint(
                                    foregroundPainter: FramePainter(
                                      pathColor: Colors.black,
                                      rect: state.dashedBoxRect,
                                    ),
                                  ),

                                  /// 点線のボックスの右側にある赤い線  その後、変更が必要です(削除しない)
                                  Positioned(
                                    left: state.redLinePosition?.dx,
                                    top: state.redLinePosition?.dy,
                                    child: state.disPlayText.isEmpty
                                        ? Container()
                                        : Container(
                                            height: 35,
                                            width: 2,
                                            color: Colors.red,
                                          ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const Spacer(flex: 15),
                        ],
                      ),
                    ),
                  ),
                  const Spacer(flex: 180),
                ],
              ),
            ),
            Expanded(
              flex: 630,
              child: Row(
                children: [
                  const Expanded(
                    flex: 34,
                    child: Column(
                      children: [
                        Spacer(
                          flex: 7,
                        ),
                        Expanded(
                          flex: 50,
                          child: ico_font_cursor(),
                        ),
                        Spacer(
                          flex: 7,
                        ),
                      ],
                    ),
                  ),
                  const Spacer(
                    flex: 9,
                  ),
                  Expanded(
                    flex: 63,
                    child: grp_btn_move_center_left_01(
                      state: state.leftMoveButtonState,
                      onTap: viewModel.onLeftButtonOnClicked,
                    ),
                  ),
                  const Spacer(
                    flex: 7,
                  ),
                  Expanded(
                    flex: 63,
                    child: grp_btn_move_center_right_01(
                      state: state.rightMoveButtonState,
                      onTap: viewModel.onRightButtonOnClicked,
                    ),
                  ),
                  const Spacer(
                    flex: 11,
                  ),
                  Expanded(
                    flex: 147,
                    child: grp_stbtn_abc_a_select(
                        onLeftTap: viewModel.onSelectAllButtonClicked,
                        onRightTap: viewModel.onOneLetterButtonClicked,
                        isSelect: state.isMultiCharSelectMode),
                  ),
                ],
              ),
            ),
            const Spacer(
              flex: 220,
            ),
          ],
        ),
        LayoutBuilder(
          builder: (context, constraints) => Stack(
            fit: StackFit.expand,
            children: [
              Positioned(
                left: constraints.maxWidth * (11 / 323),
                top: constraints.maxHeight * (400 / 691),
                child: const pic_line1(),
              ),
              Positioned(
                left: constraints.maxWidth * (11 / 323),
                top: constraints.maxHeight * (461 / 691),
                child: const pic_line2(),
              ),
              Positioned(
                left: constraints.maxWidth * (11 / 323),
                top: constraints.maxHeight * (522 / 691),
                child: const pic_line3(),
              ),
              Positioned(
                left: constraints.maxWidth * (11 / 323),
                top: constraints.maxHeight * (583 / 691),
                child: const pic_line3(),
              ),
            ],
          ),
        ),

        /// 倍率選択用のポップアップウィンドウ
        state.isZoomPopupOn
            ? Column(
                children: [
                  Expanded(
                    flex: 434,
                    child: Row(
                      children: [
                        const Spacer(flex: 5),
                        Expanded(
                          flex: 145,
                          child: ZoomSelectorPopup(
                            zoomList: viewModel.zoomDisplayList,
                            selectZoomValue:
                                viewModel.zoomDisplayList[state.zoomScaleIndex],
                            closePopup: viewModel.closeZoomPopup,
                            onZoomButtonClick:
                                viewModel.onZoomPopupButtonClicked,
                          ),
                        ),
                        const Spacer(flex: 170),
                      ],
                    ),
                  ),
                  const Spacer(flex: 264),
                ],
              )
            : Container(),
      ],
    );
  }
}

class FramePainter extends CustomPainter {
  FramePainter({
    required this.rect,
    required this.pathColor,
  });
  final Rect? rect;
  final Color pathColor;

  @override
  void paint(Canvas canvas, Size size) {
    if (rect == null) {
      return;
    }

    /// ペンキ塗り
    Paint paint = Paint()
      ..isAntiAlias = false
      ..color = pathColor
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    canvas.drawPath(_getDashedPath(rect!), paint);
  }

  ///
  /// 点線のボックスのパスを描画します
  ///
  Path _getDashedPath(Rect rectA) {
    Path drawPath = Path();
    Path basePath = Path();

    /// 点線のボックスを描画する
    Rect rect = rectA;
    basePath.moveTo(rect.left, rect.top);
    basePath.lineTo(rect.left, rect.bottom);
    basePath.lineTo(rect.right, rect.bottom);
    basePath.lineTo(rect.right, rect.top);
    basePath.lineTo(rect.left, rect.top);

    /// フレームの破線の一段の長さ
    double dashWidth = 1.0;
    double dashSpace = 1.0;

    /// 描画Pathを計算する
    double distance = 0.0;
    for (PathMetric pathMetric in basePath.computeMetrics()) {
      while (distance < pathMetric.length) {
        drawPath.addPath(
          pathMetric.extractPath(distance, distance + dashWidth),
          Offset.zero,
        );
        distance += dashWidth;
        distance += dashSpace;
      }
    }
    return drawPath;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
