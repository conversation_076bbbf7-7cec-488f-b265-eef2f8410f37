import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_e_stitch_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'line_e_stitch_width_popup_view_interface.dart';

/// 読み出したSizeデータの変換倍率 '読み出したデータの単位/10=mm'
const double _conversionRate = 10.0;

final lineEStitchWidthViewModelProvider = StateNotifierProvider.autoDispose<
    LineEStitchWidthStateViewInterface,
    LineEStitchWidthState>((ref) => LineEStitchWidthViewModel(ref));

class LineEStitchWidthViewModel extends LineEStitchWidthStateViewInterface {
  LineEStitchWidthViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineEStitchWidthState(
                widthDisplayValue: "",
                isDefaultStyle: false,
                plusButtonValid: false,
                minusButtonValid: false),
            ref) {
    update();
  }

  @override
  int get maxWidthValue => LineEStitchModel.maxiWidthValue;

  @override
  int get minWidthValue => LineEStitchModel.miniWidthValue;

  ///
  /// ステップ量
  ///
  final int _stepValue = 5;

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  ///
  /// 幅の値ディスプレイスター
  ///
  bool _isWidthValueDisplayStar =
      LineEStitchModel().getWidth() != LineEStitchModel.widthNotUpdating
          ? false
          : true;

  ///
  /// 幅の値
  ///
  int _widthValue = LineEStitchModel().getWidth();

  @override
  void update() {
    state = state.copyWith(
        widthDisplayValue: _getWidthDisplayValue(),
        isDefaultStyle: _isDefaultStyle(),
        plusButtonValid: _getPlusButtonState(),
        minusButtonValid: _getMinusButtonState());
  }

  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isWidthValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isWidthValueDisplayStar = false;

      ///  Model 更新
      _widthValue = LineEStitchModel().widthDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_widthValue <= LineEStitchModel.miniWidthValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _widthValue = _widthValue - _stepValue < LineEStitchModel.miniWidthValue
        ? LineEStitchModel.miniWidthValue
        : _widthValue - _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_isWidthValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isWidthValueDisplayStar = false;

      ///  Model 更新
      _widthValue = LineEStitchModel().widthDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_widthValue >= LineEStitchModel.maxiWidthValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _widthValue = _widthValue + _stepValue > LineEStitchModel.maxiWidthValue
        ? LineEStitchModel.maxiWidthValue
        : _widthValue + _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineEStitchWidth.toString());
    if (_isWidthValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int widthValue = LineEStitchModel().getWidth();

    /// Model 更新
    LineEStitchModel().setWidth(_widthValue);
    if (LineEStitchModel().setMdcEStitchSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (widthValue != _widthValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// 幅の表示値を取得する
  ///
  String _getWidthDisplayValue() {
    /// cmからmmへ
    double lineEStitchWidthValue = _widthValue / _conversionRate;

    if (_isWidthValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }
    if (currentSelectedUnit == Unit.mm) {
      return lineEStitchWidthValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(lineEStitchWidthValue);
  }

  ///
  /// 幅表示テキストスタイルを取得します
  ///
  bool _isDefaultStyle() {
    if (_isWidthValueDisplayStar) {
      return true;
    } else {
      if (_widthValue == LineEStitchModel().widthDefaultValue) {
        return true;
      } else {
        return false;
      }
    }
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isWidthValueDisplayStar) {
      return true;
    }

    if (_widthValue <= LineEStitchModel.miniWidthValue) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isWidthValueDisplayStar) {
      return true;
    }

    if (_widthValue >= LineEStitchModel.maxiWidthValue) {
      return false;
    }
    return true;
  }
}
