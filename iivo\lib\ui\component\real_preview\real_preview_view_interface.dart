import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

import 'component/real_preview_controller.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'real_preview_view_interface.freezed.dart';

///
/// Real Previewの種類
///
enum RealPreviewType {
  emb,
  mdc,
}

/// Tableの状態
///
/// - unselected：未選択状態
/// - selected：選択状態
/// - hide：表示されない
enum RealPreviewTableType {
  unselected,
  selected,
  hide,
}

///
/// 枠内表示種類
///
enum RealPreviewFrameType {
  frame297x465,
  frame272x272,
  frame130x180,
  frame100x100
}

class FlexNum {
  FlexNum({
    required this.left,
    required this.centerX,
    required this.right,
    required this.top,
    required this.centerY,
    required this.bottom,
  });

  final int left;
  final int centerX;
  final int right;
  final int top;
  final int centerY;
  final int bottom;
}

@freezed
class RealPreviewState with _$RealPreviewState {
  const factory RealPreviewState({
    @Default(false) bool isZoomOut,
    @Default(false) bool isPressFootDisplay,
    @Default(RealPreviewAction.stop) RealPreviewAction animationPlayState,
    @Default(RealPreviewTableType.selected)
    RealPreviewTableType frameTableState,
    @Default(RealPreviewTableType.unselected)
    RealPreviewTableType stitchSimulatorTableState,
    @Default(RealPreviewFrameType.frame297x465)
    RealPreviewFrameType selectedFrame,
    @Default(PresserFoot.EMB_PRESSER_FOOT_W) PresserFoot displayPressFootType,
    @Default([
      ButtonState.select,
      ButtonState.normal,
      ButtonState.normal,
      ButtonState.normal
    ])
    List<ButtonState> frameButtonStateList,
    @Default([
      ButtonState.select,
      ButtonState.normal,
      ButtonState.normal,
    ])
    List<ButtonState> speedButtonStateList,

    /// 背景スキャンの画像
    @Default(null) Widget? backgroundImage,

    /// プリント＆ステッチ模様で表示される背景画像
    @Default(null) Image? printBackGroundImage,
    @Default(null) Widget? realPreviewImage,
    @Default(true) bool isBusyDrawing,
  }) = _RealPreviewState;
}

abstract class RealPreviewViewModelInterface
    extends ViewModel<RealPreviewState> {
  RealPreviewViewModelInterface(
    super.state,
    this.ref,
    this.type,
  );

  ///
  /// providerのref
  ///
  Ref ref;

  ///
  /// Real Previewの種類
  ///
  RealPreviewType type;

  ///
  /// アニメーションコントローラ
  ///
  late final RealPreviewController realPreviewController;

  ///
  /// 拡大ボタンのクリック関数
  ///
  void onZoomOutButtonClick();

  ///
  /// 縮小ボタンのクリック関数
  ///
  void onZoomInButtonClick();

  ///
  /// Frame Tableボタンのクリック関数
  ///
  void onFrameTableButtonClick();

  ///
  /// ステッチシミュレーター Tableボタンのクリック関数
  ///
  void onRrePlayButtonClick();

  ///
  /// Frameボタンのクリック関数
  ///
  void onFrameButtonClick(RealPreviewFrameType getEmbFrameType);

  ///
  /// stitch simulatorボタンのクリック関数
  ///
  void onStitchSimulatorTableButtonClick();

  ///
  /// Speedボタンのクリック関数
  ///
  void onSpeedButtonClick(RealPreviewSpeed speedType);

  ///
  /// 再生キー / 一時停止キーボタンのクリック関数
  ///
  void onPlayButtonClick();

  ///
  /// プリント＆ステッチ模様で表示される背景画像の表示範囲
  ///
  FlexNum get printBackGroundFlex;

  ///
  /// 閉じるボタンのクリック関数
  ///
  void onCloseButtonClick();
}
