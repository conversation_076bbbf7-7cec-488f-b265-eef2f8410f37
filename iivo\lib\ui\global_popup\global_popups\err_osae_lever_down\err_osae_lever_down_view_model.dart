import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_osae_lever_down_view_interface.dart';

final errOsaeLeverDownViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrOsaeLeverDownViewInterface, ErrOsaeLeverDownState,
            BuildContext>(
        (ref, context) => ErrOsaeLeverDownViewModel(ref, context));

class ErrOsaeLeverDownViewModel extends ErrOsaeLeverDownViewInterface {
  ErrOsaeLeverDownViewModel(Ref ref, BuildContext context)
      : super(const ErrOsaeLeverDownState(), ref, context);

  @override
  void onOKButtonClicked() {
    int errcode;
    errcode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERROSAEDOWN);
    if (errcode == BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }
  }
}
