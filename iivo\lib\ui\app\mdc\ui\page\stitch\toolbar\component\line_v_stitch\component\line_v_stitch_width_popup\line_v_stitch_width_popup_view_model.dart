import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_v_stitch_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'line_v_stitch_width_popup_state.dart';

typedef Unit = DisplayUnit;

///
/// 黒い背景に白いテキスト
///
const TextStyle blackTextWhiteBackground = TextStyle(
    color: Colors.white, backgroundColor: Color.fromARGB(255, 51, 51, 51));

///
/// 白い背景に黒いテキスト
///
const TextStyle whiteTextBlackBackground = TextStyle(
    color: Color.fromARGB(255, 51, 51, 51), backgroundColor: Colors.white);

/// 読み出したSizeデータの変換倍率 '読み出したデータの単位/10=mm'
const double _conversionRate = 10.0;

final lineVStitchWidthViewModelProvider = StateNotifierProvider.autoDispose<
    LineVStitchWidthViewModel,
    LineVStitchWidthState>((ref) => LineVStitchWidthViewModel(ref));

class LineVStitchWidthViewModel extends ViewModel<LineVStitchWidthState> {
  LineVStitchWidthViewModel(this.ref)
      : super(
          const LineVStitchWidthState(
              widthDisplayValue: "",
              isDefaultValue: false,
              plusButtonValid: false,
              minusButtonValid: false),
        ) {
    update();
  }
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// 最大幅値
  final int maxWidthValue = LineVStitchModel.maxiWidthValue;

  ///
  /// 最小幅値
  ///
  final int minWidthValue = LineVStitchModel.miniWidthValue;

  ///
  /// ステップ量
  ///
  final int _stepValue = 5;

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  ///
  /// 幅値表示星
  ///
  bool _isWidthValueDisplayStar =
      LineVStitchModel().getWidth() != LineVStitchModel.widthNotUpdating
          ? false
          : true;

  ///
  /// 幅値
  ///
  int _widthValue = LineVStitchModel().getWidth();

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    state = state.copyWith(
        widthDisplayValue: _getWidthDisplayValue(),
        isDefaultValue: _isDefaultValue(),
        plusButtonValid: _getPlusButtonState(),
        minusButtonValid: _getMinusButtonState());
  }

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isWidthValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isWidthValueDisplayStar = false;

      ///  Model 更新
      _widthValue = LineVStitchModel().widthDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_widthValue <= LineVStitchModel.miniWidthValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _widthValue = _widthValue - _stepValue < LineVStitchModel.miniWidthValue
        ? LineVStitchModel.miniWidthValue
        : _widthValue - _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress) {
    if (_isWidthValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isWidthValueDisplayStar = false;

      ///  Model 更新
      _widthValue = LineVStitchModel().widthDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_widthValue >= LineVStitchModel.maxiWidthValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _widthValue = _widthValue + _stepValue > LineVStitchModel.maxiWidthValue
        ? LineVStitchModel.maxiWidthValue
        : _widthValue + _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineVStitchWidth.toString());
    if (_isWidthValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int widthValue = LineVStitchModel().getWidth();

    /// Model 更新
    LineVStitchModel().setWidth(_widthValue);
    if (LineVStitchModel().setMdcVStitchLineSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (widthValue != _widthValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// 幅の表示値を取得する
  ///
  String _getWidthDisplayValue() {
    /// cmからmmへ
    double lineVStitchWidthValue = _widthValue / _conversionRate;

    if (_isWidthValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }
    if (currentSelectedUnit == Unit.mm) {
      return lineVStitchWidthValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(lineVStitchWidthValue);
  }

  ///
  /// 幅表示テキストスタイルを取得します
  ///
  bool _isDefaultValue() => _isWidthValueDisplayStar
      ? true
      : _widthValue == LineVStitchModel().widthDefaultValue
          ? true
          : false;

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isWidthValueDisplayStar) {
      return true;
    }

    if (_widthValue <= LineVStitchModel.miniWidthValue) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isWidthValueDisplayStar) {
      return true;
    }

    if (_widthValue >= LineVStitchModel.maxiWidthValue) {
      return false;
    }
    return true;
  }
}
