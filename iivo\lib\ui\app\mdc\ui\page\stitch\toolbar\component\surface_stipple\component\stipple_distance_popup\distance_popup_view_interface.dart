import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'distance_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class DistancePopupState with _$DistancePopupState {
  const factory DistancePopupState({
    required bool isDistanceMinusToLimit,
    required bool isDistancePlusToLimit,
    required String distanceInputValue,
    required bool distanceDisplayTextStyle,
  }) = _DistancePopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class DistancePopupStateViewInterface
    extends ViewModel<DistancePopupState> {
  DistancePopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool miniDistance(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool plusDistance(bool isLongPress);

  ///
  /// チェーンステッチのデフォルト値
  ///
  int get defaultValue;

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
