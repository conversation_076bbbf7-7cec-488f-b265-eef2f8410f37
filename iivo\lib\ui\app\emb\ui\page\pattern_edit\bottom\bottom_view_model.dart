import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../model/projector_model.dart';
import '../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../global_popup/global_popup_export.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../global_popup/global_popups/err_emb_pattern_exceeded/err_emb_pattern_exceeded_view_model.dart';
import '../../../../../../global_popup/global_popups/err_pattern_cancel_ok_by_delete_keys/err_pattern_cancel_ok_by_delete_keys_view_model.dart';
import '../../../../../../global_popup/global_popups/err_pattern_cannot_save/err_pattern_cannot_save_view_model.dart';
import '../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../model/border_model.dart';
import '../../../../model/connect_sewing_model.dart';
import '../../../../model/edit_model.dart';
import '../../../../model/pattern_filter_model.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/redo_undo_model.dart';
import '../../../../model/select_model.dart';
import '../../../component/emb_header/emb_header_view_model.dart';
import '../../../page_route.dart';
import '../../common_component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../../common_component/memory/memory.dart';
import '../pattern_edit_view_model.dart';
import 'bottom_view_interface.dart';

final bottomViewModelProvider =
    StateNotifierProvider.autoDispose<BottomViewModel, BottomState>(
        (ref) => BottomViewModel(ref));

class BottomViewModel extends BottomViewModelInterface {
  BottomViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const BottomState(), ref) {
    update();
    _listenStatus();
  }

  @override
  void update() {
    final EmbEditAttribParam attribute =
        TpdLibrary().apiBinding.bPIFGetAppDisplayEmb().embEditAttrib.ref;

    state = state.copyWith(
      bottomDisplayType: _getBottomDisplayType(),
      isAddButtonDisplay: _isAddButtonDisplay(attribute.addState),
      isAddButtonEnable: _isAddButtonEnable(attribute.addState),
      isDeleteButtonDisplay: _isDeleteButtonDisplay(attribute.deleteState),
      isRedoEnable: _isRedoEnable(),
      isUndoEnable: _isUndoEnable(),
      isMemoryEnable: _isMemoryButtonEnable(attribute.memoryState),
      isEmbroideryEnable: _isEmbroideryButtonEnable(attribute.embroideryState),
      isMultipleSelectDisplay:
          _isMultipleSelectDisplay(attribute.multipleSelectionState),
      isEnglish: PatternModel().isEnglish,
      isConnectSew: attribute.isEmbroideryConnect,
    );
  }

  @override
  void onAddButtonClicked() {
    if (state.isAddButtonEnable == false) {
      final error = EmbLibrary().apiBinding.embEditReturnSelectGray();
      if (error == EmbLibraryError.EMB_INVALID_ERR) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
      } else {
        /// Do nothing
      }
      return;
    }

    if (ProjectorModel().embProjector.isEmbProjectorViewOpen == true) {
      final embProjectorFunction =
          ref.read(embProjectorFunctionProvider.notifier);

      /// プロジェクト起動・停止前にエラーCheckをする
      /// エラーがあれば ポープアープを表示する
      final hasError = embProjectorFunction
          .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
      if (hasError) {
        return;
      }

      embProjectorFunction.closeEmbProjectorView(
        closingHandleCallback: () {
          ProjectorModel().embProjector.isEmbProjectorViewOpen = false;
          ref
              .read(patternEditViewModelProvider.notifier)
              .updateEditPageByChild(ModuleType.projector);
          ref.read(embHeaderViewModelProvider.notifier).update();
        },
        afterClosedHandleCallback: () {},
      );

      return;
    }

    /// 模様追加前処理
    final error = EmbLibrary().apiBinding.embEditReturnSelect();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return;
    }
    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }
    EmbLibrary().apiBinding.prepareAddEmb();
    PatternModel().clearAllSelectedGroupInfoCache();

    SystemSoundPlayer().play(SystemSoundEnum.change);

    SelectModel().isAddModel = true;
    PatternModel()
      ..selectedZoomScaleInEditPage = zoomList.first
      ..selectedZoomScaleInSelectPage = zoomList.first;
    PagesRoute().pushNamedAndRemoveUntil(
      nextRoute: PatternFilter().filterStatus == FilterType.filtered
          ? PageRouteEnum.patternSelectFilterResult
          : PageRouteEnum.patternSelect,
      untilRoute: PageRouteEnum.home,
    );
  }

  @override
  void onEmbroideryButtonClicked() {
    if (state.isEmbroideryEnable == false && state.isConnectSew == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    if (ProjectorModel().embProjector.isEmbProjectorViewOpen == true) {
      final embProjectorFunction =
          ref.read(embProjectorFunctionProvider.notifier);

      /// プロジェクト起動・停止前にエラーCheckをする
      /// エラーがあれば ポープアープを表示する
      final hasError = embProjectorFunction
          .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
      if (hasError) {
        return;
      }

      embProjectorFunction.closeEmbProjectorView(
          closingHandleCallback: () {
            ProjectorModel().embProjector.isEmbProjectorViewOpen = false;
            ref
                .read(patternEditViewModelProvider.notifier)
                .updateEditPageByChild(ModuleType.projector);
            ref.read(embHeaderViewModelProvider.notifier).update();
          },
          afterClosedHandleCallback: () {});
      return;
    }

    final embErrorCode = EmbLibrary().apiBinding.prepareSewing();
    if (embErrorCode != EmbLibraryError.EMB_NO_ERR) {
      return;
    }

    EmbLibraryError error = EmbLibrary().apiBinding.embGotoSewing();
    if (error == EmbLibraryError.EMB_GOTO_RESUME) {
      SystemSoundPlayer().play(SystemSoundEnum.change);
      GlobalPopupRoute().openPleaseWaitPopup();
      final err =
          EmbLibrary().apiBinding.saveEmbResumeData(EditModel().resumeSavePath);
      if (err != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().closePleaseWaitPopup();
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      /// Resumeの時、特別な操作
      error = EmbLibrary().apiBinding.embGotoResumeOk();
      GlobalPopupRoute().closePleaseWaitPopup();
    }

    switch (error) {
      case EmbLibraryError.EMB_GOTO_SEWING:
        SystemSoundPlayer().play(SystemSoundEnum.change);
        ConnectSewingModel().saveNewCnctImage();
        _gotoSewing();
        return;

      /// つなぎ画面(EmbEditSewingSetMarkPatCnctSetting)
      /// 指定したパスにつなぎの画像を保存します。
      case EmbLibraryError.EMB_GOTO_SEWING_PTN_CONNECT:
        SystemSoundPlayer().play(SystemSoundEnum.change);
        GlobalPopupRoute().openPleaseWaitPopup();
        ConnectSewingModel().saveNewCnctImage().then((_) {
          GlobalPopupRoute().closePleaseWaitPopup();
          PagesRoute().pushNamed(nextRoute: PageRouteEnum.connectSew);
          PatternModel()
            ..selectedZoomScaleInEditPage = zoomList.first
            ..clearAllPatternImageCache();
        });
        return;
      case EmbLibraryError.EMB_INVALID_ERR_PANEL:
        final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
        if (bPIFErrorPointer.errorCode ==
            ErrCode_t.ERR_RECOMEND_TOMENUI.index) {
          errRecomendTomenuiPopFunc = onEmbroideryButtonClicked;
        }
        return;
      case EmbLibraryError.EMB_INVALID_ERR:
      default:
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
    }
  }

  @override
  void onSelectLeftButtonClicked() {
    final error = EmbLibrary().apiBinding.selectEmbGroupPrev();
    if (error == EmbLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      PatternModel().clearAllSelectedGroupInfoCache();
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }

    BorderModel()
      ..resetMarkSelectIndex()
      ..resetDivideSelectedIndex();

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.bottom);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onSelectRightButtonClicked() {
    final error = EmbLibrary().apiBinding.selectEmbGroupNext();
    if (error == EmbLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      PatternModel().clearAllSelectedGroupInfoCache();
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }

    BorderModel()
      ..resetMarkSelectIndex()
      ..resetDivideSelectedIndex();

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.bottom);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onMultipleSelectionButtonClicked(BuildContext context) {
    /// model更新
    EditModel().multipleBackupToolbarPopupId = EditModel().toolbarPopupId;
    EditModel().toolbarPopupId = ToolbarPopupId.multipleSelection;
    EmbLibrary().apiBinding.enterMultiSelectMode();

    /// view更新
    update();
    PopupNavigator.pushNamed(
      context: context,
      nextRouteName: PopupEnum.multipleSelection,
    ).then((value) {
      EmbLibrary().apiBinding.exitMultiSelectMode();
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      /// プロジェクションがオンの場合、保存状態をEditに設定します
      if (ProjectorModel().embProjector.isEmbProjectorViewOpen) {
        EditModel().multipleBackupToolbarPopupId = ToolbarPopupId.edit;
      } else {
        /// do nothing
      }

      /// Model更新
      EditModel().toolbarPopupId = EditModel().multipleBackupToolbarPopupId;

      /// 他のページへの更新の通知
      ref
          .read(patternEditViewModelProvider.notifier)
          .updateEditPageByChild(ModuleType.multipleSelection);

      /// プロジェクト投影画面を更新する
      final embProjectorFunction =
          ref.read(embProjectorFunctionProvider.notifier);
      embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
    });

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.bottom);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void onUndoButtonClicked() {
    if (state.isUndoEnable == false) {
      return;
    }

    /// リスト内のUNDOの位置を更新します
    (RedoUndoModel().undoIndex--).clamp(0, RedoUndoModel().maxUndoListLength);

    /// Lib通知
    RedoUndoModel().undoRedo();
    PatternModel().reloadAllPattern();

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);

    /// View更新
    update();

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.bottom);
  }

  @override
  void onRedoButtonClicked() {
    if (state.isRedoEnable == false) {
      return;
    }

    /// リスト内のUNDOの位置を更新します
    (RedoUndoModel().undoIndex++).clamp(0, RedoUndoModel().maxUndoListLength);

    /// Lib通知
    RedoUndoModel().undoRedo();
    PatternModel().reloadAllPattern();

    /// 投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);

    /// View更新
    update();

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.bottom);
  }

  @override
  void onDeleteButtonClicked() {
    final allPatternNum = PatternModel().getAllPattern().length;
    final selectedPatternNum = PatternModel().getAllSelectedPattern().length;

    /// プロジェクション中にすべての模様が削除された場合
    if (allPatternNum == selectedPatternNum &&
        ProjectorModel().embProjector.isEmbProjectorViewOpen == true) {
      final embProjectorFunction =
          ref.read(embProjectorFunctionProvider.notifier);

      /// プロジェクト起動・停止前にエラーCheckをする
      /// エラーがあれば ポープアープを表示する
      final hasError = embProjectorFunction
          .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
      if (hasError) {
        return;
      }

      embProjectorFunction.closeEmbProjectorView(
        closingHandleCallback: () {
          ProjectorModel().embProjector.isEmbProjectorViewOpen = false;
          ref
              .read(patternEditViewModelProvider.notifier)
              .updateEditPageByChild(ModuleType.projector);
          ref.read(embHeaderViewModelProvider.notifier).update();
        },
        afterClosedHandleCallback: () {},
      );
      return;
    } else {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_PATTERN_CANCEL_OK_BY_DELETE_KEYS,
        arguments: ErrPatternCancelOkByDeleteKeyArgument(
          onCancelButtonClicked: (_) => GlobalPopupRoute().resetErrorState(),
          onOKButtonClicked: (_) {
            /// 選択したすべてのパターンを削除する
            PatternModel().deleteEmb();

            /// 自分を閉じる
            GlobalPopupRoute().resetErrorState();

            if (PatternModel().getAllPattern().isEmpty) {
              /// パターンがない場合はページを閉じる
              SelectModel().isAddModel = false;
              EmbLibrary().apiBinding.embEditReturnSelect();
              EmbLibrary().apiBinding.prepareAddEmb();
              PatternModel()
                ..selectedZoomScaleInEditPage = zoomList.first
                ..selectedZoomScaleInSelectPage = zoomList.first;
              PagesRoute().pushNamedAndRemoveUntil(
                nextRoute: PatternFilter().filterStatus == FilterType.filtered
                    ? PageRouteEnum.patternSelectFilterResult
                    : PageRouteEnum.patternSelect,
                untilRoute: PageRouteEnum.home,
              );
            } else {
              /// PHX ファイルを保存する(全て模様が削除の時に、UNDO／REDOフェールを保存しない)
              if (RedoUndoModel().saveUndoRedoFile() !=
                  EmbLibraryError.EMB_NO_ERR) {
                GlobalPopupRoute().updateErrorState(
                  nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
                  arguments: TroubleOccoredPowerOffArgument(
                    onOKButtonClicked: GlobalPopupRoute().resetErrorState,
                  ),
                );
                return;
              }

              /// 投影画面を更新する
              final embProjectorFunction =
                  ref.read(embProjectorFunctionProvider.notifier);
              embProjectorFunction.maybeUpdateProjectorScreen(
                  redrawEmbPattern: true);

              /// 他のページへの更新の通知
              ref
                  .read(patternEditViewModelProvider.notifier)
                  .updateEditPageByChild(ModuleType.bottom);
            }
          },
        ),
      );
    }
  }

  @override
  void onMemoryButtonClicked(BuildContext context) {
    if (state.isMemoryEnable == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 編集画面でMemoryボタンが押されたときに実行される確認処理を行う
    final checkError = EmbLibrary().apiBinding.checkMemoryForEmbEdit();

    if (checkError == EmbLibraryError.EMB_PATTERN_CANNOT_SAVE) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_PATTERN_CANNOT_SAVE,
        arguments: PatternCannotSaveArgument(
          onOkButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    } else if (checkError == EmbLibraryError.EMB_PATTERN_EXCEEDED) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_EMB_PATTERN_EXCEEDED,
        arguments: EmbPatternExceededArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    } else {
      /// do nothing
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => Memory(
          onCancelClick: () => PopupNavigator.pop(context: context),
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();

    /// 状態リッスンの停止
    _listener?.close();
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// 内部で使われる関数群
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// 刺しゅう 模様選択状態への遷移
  ///
  void _gotoSewing() {
    PatternModel()
      ..selectedZoomScaleInEditPage = zoomList.first
      ..clearAllPatternImageCache();

    PagesRoute().pushNamedAndRemoveUntil(
      nextRoute: PageRouteEnum.sewing,
      untilRoute: PageRouteEnum.home,
    );
  }

  ///
  /// データ更新ページをリッスンする
  ///
  ProviderSubscription? _listener;

  ///
  /// 状態をリッスンします
  ///
  void _listenStatus() {
    _listener = ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.addState),
      (previous, nextState) {
        state = state.copyWith(
          isAddButtonDisplay: _isAddButtonDisplay(nextState),
          isAddButtonEnable: _isAddButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.deleteState),
      (previous, nextState) {
        state = state.copyWith(
          isDeleteButtonDisplay: _isDeleteButtonDisplay(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.memoryState),
      (previous, nextState) {
        state = state.copyWith(
          isMemoryEnable: _isMemoryButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.embroideryState),
      (previous, nextState) {
        state = state.copyWith(
          isEmbroideryEnable: _isEmbroideryButtonEnable(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.multipleSelectionState),
      (previous, nextState) {
        state = state.copyWith(
          isMultipleSelectDisplay: _isMultipleSelectDisplay(nextState),
        );
      },
    );

    ref.listen(
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.isEmbroideryConnect),
      (previous, nextState) {
        state = state.copyWith(
          isConnectSew: nextState,
        );
      },
    );
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// ボタン表示用の判定関数群
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// Addボタンを表示するかどうか
  ///
  bool _isAddButtonDisplay(int addState) {
    if (AppInfoFuncState.getValueByNumber(addState) == AppInfoFuncState.non) {
      return false;
    } else {
      return true;
    }
  }

  ///
  /// Addボタンが有効かどうか
  ///
  bool _isAddButtonEnable(int addState) {
    if (AppInfoFuncState.getValueByNumber(addState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// Deleteボタンを表示するかどうか
  ///
  bool _isDeleteButtonDisplay(int deleteState) {
    if (AppInfoFuncState.getValueByNumber(deleteState) ==
        AppInfoFuncState.non) {
      return false;
    } else {
      return true;
    }
  }

  ///
  /// Memoryボタンが有効かどうか
  ///
  bool _isMemoryButtonEnable(int memoryState) {
    if (AppInfoFuncState.getValueByNumber(memoryState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// Embroideryボタンが有効かどうか
  ///
  bool _isEmbroideryButtonEnable(int embroideryState) {
    if (AppInfoFuncState.getValueByNumber(embroideryState) ==
        AppInfoFuncState.enable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 選択メニューを表示するかどうか
  ///
  bool _isMultipleSelectDisplay(int selectState) {
    if (AppInfoFuncState.getValueByNumber(selectState) ==
        AppInfoFuncState.non) {
      return false;
    } else {
      return true;
    }
  }

  ///
  /// REDOキーが有効かどうか
  ///
  bool _isRedoEnable() =>
      RedoUndoModel().undoIndex <
          RedoUndoModel().embUndoFileNameList.length - 1 &&
      PatternModel.getEmbMarkPatCntAutoCustom() == false;

  ///
  /// UNDOキーが有効かどうか
  ///
  bool _isUndoEnable() =>
      RedoUndoModel().undoIndex > 0 &&
      PatternModel.getEmbMarkPatCntAutoCustom() == false;

  ///
  /// 下部領域の表示状態を取得します
  ///
  BottomDisplayType _getBottomDisplayType() {
    BottomDisplayType bottomDisplayType = BottomDisplayType.bottom;

    if (EditModel().toolbarPopupId != ToolbarPopupId.none &&
        EditModel().toolbarPopupId != ToolbarPopupId.edit) {
      bottomDisplayType = BottomDisplayType.onlySelect;
    }

    if ([
      ToolbarPopupId.order,
      ToolbarPopupId.alignment,
      ToolbarPopupId.multipleSelection,
    ].contains(EditModel().toolbarPopupId)) {
      return BottomDisplayType.none;
    }

    return bottomDisplayType;
  }
}
