// ignore_for_file: camel_case_types, depend_on_referenced_packages

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';

import '../../xd_component.dart';
import 'theme_button.dart';

class grp_btn_return_mdc extends StatelessWidget {
  const grp_btn_return_mdc({
    super.key,
    this.onTap,
    this.text = '',
    this.state = ButtonState.normal,
    this.feedBackControl = const FeedBackControl(),
  });

  final String text;
  final ButtonState state;
  final void Function()? onTap;
  final FeedBackControl? feedBackControl;

  @override
  Widget build(BuildContext context) => FeedBackButton(
        onTap: state == ButtonState.disable ? null : () => onTap?.call(),
        state: state,
        style: ThemeButton.btn_n_size152x80_theme1,
        feedBackControl: feedBackControl,
        child: Row(
          children: [
            const Spacer(flex: 2),
            Expanded(
              flex: 148,
              child: Column(
                children: [
                  const Spacer(flex: 4),
                  Expanded(
                    flex: 72,
                    child: Center(
                      child: Text(
                        text,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  const Spacer(flex: 4),
                ],
              ),
            ),
            const Spacer(flex: 2),
          ],
        ),
      );
}
