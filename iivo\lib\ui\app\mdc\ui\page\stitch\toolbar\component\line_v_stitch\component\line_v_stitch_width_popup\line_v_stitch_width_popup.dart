import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'line_v_stitch_width_popup_view_model.dart';

class WidthPopup extends ConsumerStatefulWidget {
  const WidthPopup({super.key});

  @override
  ConsumerState<WidthPopup> createState() => _WidthSettingPopupState();
}

class _WidthSettingPopupState extends ConsumerState<WidthPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(lineVStitchWidthViewModelProvider);
    final viewModel = ref.read(lineVStitchWidthViewModelProvider.notifier);

    return Column(
      children: [
        const Spacer(flex: 159),
        Expanded(
          flex: 1052,
          child: Material(
            color: Colors.transparent,
            child: Row(
              children: [
                const Spacer(flex: 571),
                Expanded(
                  flex: 229,
                  child: Stack(
                    children: [
                      const pre_edit_toolbar(),
                      Column(
                        children: [
                          const Spacer(flex: 37),
                          const Expanded(
                            flex: 68,
                            child: Row(
                              children: [
                                Spacer(flex: 52),
                                Expanded(
                                  flex: 126,
                                  child: ico_mdcstitch_vsitich_width(),
                                ),
                                Spacer(flex: 51),
                              ],
                            ),
                          ),
                          const Spacer(flex: 24),
                          Expanded(
                            flex: 69,
                            child: Row(
                              children: [
                                const Spacer(flex: 12),
                                Expanded(
                                  flex: 205,
                                  child: grp_str_stitchwidth_01(
                                    text: l10n.icon_00568,
                                  ),
                                ),
                                const Spacer(flex: 12),
                              ],
                            ),
                          ),
                          const Spacer(flex: 8),
                          Expanded(
                            flex: 69,
                            child: Row(
                              children: [
                                const Spacer(flex: 12),
                                Expanded(
                                  flex: 205,
                                  child: grp_str_parameter_01(
                                      text: state.widthDisplayValue,
                                      unit: viewModel.currentSelectedUnit ==
                                              Unit.mm
                                          ? l10n.icon_00225
                                          : l10n.icon_00226,
                                      isDefaultValue: state.isDefaultValue),
                                ),
                                const Spacer(flex: 12),
                              ],
                            ),
                          ),
                          const Spacer(flex: 12),
                          Expanded(
                            flex: 63,
                            child: Row(
                              children: [
                                const Spacer(flex: 48),

                                /// 「-」ボタン
                                Expanded(
                                  flex: 63,
                                  child: grp_btn_minus_01(
                                    onTap: () =>
                                        viewModel.onMinusButtonClicked(false),
                                    onLongPress: () =>
                                        viewModel.onMinusButtonClicked(true),
                                    state: state.minusButtonValid
                                        ? ButtonState.normal
                                        : ButtonState.disable,
                                    feedBackControl: null,
                                  ),
                                ),
                                const Spacer(flex: 8),

                                /// 「+」ボタン
                                Expanded(
                                  flex: 63,
                                  child: grp_btn_plus_01(
                                    onTap: () =>
                                        viewModel.onPlusButtonClicked(false),
                                    onLongPress: () =>
                                        viewModel.onPlusButtonClicked(true),
                                    state: state.plusButtonValid
                                        ? ButtonState.normal
                                        : ButtonState.disable,
                                    feedBackControl: null,
                                  ),
                                ),
                                const Spacer(flex: 47),
                              ],
                            ),
                          ),
                          const Spacer(flex: 620),
                          Expanded(
                            flex: 70,
                            child: Row(
                              children: [
                                const Spacer(flex: 12),
                                Expanded(
                                  flex: 205,
                                  child: grp_btn_positive(
                                    style: ThemeButton.btn_n_size205x70_theme1,
                                    onTap: viewModel.onOkButtonClicked,
                                    text: l10n.icon_ok,
                                    feedBackControl: null,
                                  ),
                                ),
                                const Spacer(flex: 12),
                              ],
                            ),
                          ),
                          const Spacer(flex: 12),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        const Spacer(flex: 69),
      ],
    );
  }
}
