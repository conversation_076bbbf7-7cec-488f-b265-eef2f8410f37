import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'size_setting_popup_view_interface.freezed.dart';

@freezed
class SizeSettingPopupState with _$SizeSettingPopupState {
  const factory SizeSettingPopupState({
    @Default("100") String settingValue,
    @Default(false) bool isMaxValue,
    @Default(false) bool isMainValue,
  }) = _SizeSettingPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class SizeSettingPopupStateViewInterface
    extends ViewModel<SizeSettingPopupState> {
  SizeSettingPopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress);

  ///
  /// 倍率/サイズ表示テキストスタイルを取得します
  ///
  bool getSizeTextStyle();
}
