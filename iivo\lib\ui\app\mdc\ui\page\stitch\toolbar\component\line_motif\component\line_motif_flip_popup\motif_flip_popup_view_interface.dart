import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'motif_flip_popup_view_interface.freezed.dart';

@freezed
class MotifFlipPopupState with _$MotifFlipPopupState {
  const factory MotifFlipPopupState({
    required bool isSelectInside,
    required bool isSelectOutside,
  }) = _MotifFlipPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class MotifFlipPopupStateViewInterface
    extends ViewModel<MotifFlipPopupState> {
  MotifFlipPopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// デフォルト値
  ///
  FlipSide get defaultValue;

  ///
  /// ボタンをクリックしてリスニング
  ///
  void onInsideButtonClick();

  ///
  ///  ボタンをクリックしてリスニング
  ///
  void onOutsideButtonClick();
}
