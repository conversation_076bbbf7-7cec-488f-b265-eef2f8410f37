import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings.dart' as tpd;
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import '../../../../model/write_debug_log_task_queue.dart.dart';
import '../page/page_route.dart';

/// テストモード画面番号 通知 Provider
final testModePageNoProvider =
    StateNotifierProvider<TestModePageNoNotifier, TestModePageNo>(
        (ref) => TestModePageNoNotifier());

class TestModePageNoNotifier extends StateNotifier<TestModePageNo> {
  TestModePageNoNotifier() : super(const TestModePageNo(pageNo: 0));

  void set(int pageNo) {
    debugPrint("debug: set testModeId = $pageNo");
    state = TestModePageNo(pageNo: pageNo);
  }

  int get() {
    debugPrint("debug: get testModeId = ${state.pageNo}");
    return state.pageNo;
  }
}

@immutable
class TestModePageNo {
  const TestModePageNo({required this.pageNo});

  final int pageNo;
}

// ignore: camel_case_types
enum testModeButtonType {
  normal,
  select,
  gray,
  graySelect,
}

// ダミー関数
void nullFnc() {}

//テストモードタイトル描画
//引数1 - Function function : テストモード番号の□押下時に呼び出される関数
//引数2 - String num : テストモード番号
//引数3 - String title : テストモードタイトル
Widget testModeTitleWidget(Function function, String num, String title) =>
    Row(children: [
      InkWell(
        onTap: () async {
          function();
          SystemSoundPlayer().play(SystemSoundEnum.accept);
          DeviceLibrary().apiBinding.writeDebugLogToFile();
          await Future.delayed(const Duration(seconds: 1));
          WriteDebugLogTaskQueue.getInstance().saveDebugLogToUSB();
        },
        child: Container(
          height: 80,
          width: 80,
          alignment: Alignment.center,
          decoration:
              BoxDecoration(border: Border.all(width: 2), color: Colors.white),
          child: Text(num,
              style: const TextStyle(fontSize: 36, color: Colors.black)),
        ),
      ),
      Padding(
          padding: const EdgeInsets.all(20.0),
          child: Text(title,
              style:
                  const TextStyle(fontSize: 36, fontWeight: FontWeight.bold))),
    ]);

//テストモードページ描画
//引数1 - Function pageNext : ページ進むキーの押下時に呼び出される関数
//引数2 - Function pagePrev : ページ戻るキーの押下時に呼び出される関数
//引数3 - int currentPage : 現在のページ(表示用なので最初のページなら1)
//引数4 - int totalPage : 総ページ数
Widget testModePageWidget(
        Function pageNext, Function pagePrev, int currentPage, int totalPage) =>
    Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        testModeIconButtonWidget(pagePrev, Icons.keyboard_arrow_left, 50),
        const SizedBox(width: 30),
        Container(
          height: 40,
          width: 30,
          alignment: Alignment.center,
          child: Text(currentPage.toString(),
              style: const TextStyle(fontSize: 36, color: Colors.black)),
        ),
        Container(
          height: 40,
          width: 30,
          alignment: Alignment.center,
          child: const Text('/',
              style: TextStyle(fontSize: 36, color: Colors.black)),
        ),
        Container(
          height: 40,
          width: 30,
          alignment: Alignment.center,
          child: Text(totalPage.toString(),
              style: const TextStyle(fontSize: 36, color: Colors.black)),
        ),
        const SizedBox(width: 30),
        testModeIconButtonWidget(pageNext, Icons.keyboard_arrow_right, 50),
      ],
    );

//これはそのうち無くしたい
Widget setKeyButton(Function function, String text) => OutlinedButton(
      onPressed: () {
        function();
      },
      style: ElevatedButton.styleFrom(
        minimumSize: const Size(100, 100),
      ),
      child:
          Text(text, style: const TextStyle(fontSize: 36, color: Colors.black)),
    );

//テストモード用ボタン描画(ボタンサイズ自動)
//引数1 - Function function : ボタン押下時に呼び出される関数
//引数2 - String text : ボタンの上に描画する文字
//引数3 - double inputFontSize : フォントサイズ(指定なしの場合は36)
//引数4 - testModeButtonType inputButtonType : ボタンの状態(指定なしの場合はnormal)
//引数5 - double styleFromWidth : ボタンの幅(指定なしの場合は160)
//引数6 - double styleFromHeight : ボタンの高さ(指定なしの場合は80)
//引数7 - bool isDisable : ボタン無効化=true, ボタン有効化=false
Widget testModeButtonWidget(Function function, String text,
    {double inputFontSize = 36,
    testModeButtonType inputButtonType = testModeButtonType.normal,
    double styleFromWidth = 160,
    double styleFromHeight = 80,
    bool isDisable = false,
    Function fncOnLongPress = nullFnc}) {
  Color buttonBorderColor; //ボタンの枠色
  Color buttonFillColor; //ボタンの色
  Color buttonFillColorSel; //ボタンの選択色
  Color buttonLabelColor; //ボタンのウワモノの色
  if (inputButtonType == testModeButtonType.select) {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.blue.shade200;
    buttonFillColorSel = Colors.blue.shade400;
    buttonLabelColor = Colors.grey.shade900;
  } else if (inputButtonType == testModeButtonType.gray) {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.grey.shade200;
    buttonFillColorSel = Colors.grey.shade400;
    buttonLabelColor = Colors.grey.shade500;
  } else if (inputButtonType == testModeButtonType.graySelect) {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.blue.shade100;
    buttonFillColorSel = Colors.blue.shade300;
    buttonLabelColor = Colors.grey.shade500;
  } else {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.white;
    buttonFillColorSel = Colors.blue.shade300;
    buttonLabelColor = Colors.black;
  }
  return ElevatedButton(
      onPressed: isDisable
          ? null
          : () {
              function();
            },
      onLongPress: () {
        fncOnLongPress();
      },
      style: ElevatedButton.styleFrom(
        minimumSize: Size(styleFromWidth, styleFromHeight),
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
        side: BorderSide(color: buttonBorderColor, width: 2),
        backgroundColor: buttonFillColor,
        foregroundColor: buttonFillColorSel,
      ),
      child: Text(text,
          style: TextStyle(fontSize: inputFontSize, color: buttonLabelColor),
          textAlign: TextAlign.center));
}

//テストモード用ボタン描画(ボタンサイズ指定)
//引数1 - Function function : ボタン押下時に呼び出される関数
//引数2 - String text : ボタンの上に描画する文字
//引数3 - double width : ボタンの横サイズ
//引数4 - double height : ボタンの横サイズ
//引数5 - double inputFontSize : フォントサイズ(指定なしの場合は36)
//引数6 - testModeButtonType inputButtonType : ボタンの状態(指定なしの場合はnormal)
Widget testModeButtonSizeFixWidget(
    Function function, String text, double width, double height,
    {double inputFontSize = 36,
    testModeButtonType inputButtonType = testModeButtonType.normal}) {
  Color buttonBorderColor; //ボタンの枠色
  Color buttonFillColor; //ボタンの色
  Color buttonFillColorSel; //ボタンの選択色
  Color buttonLabelColor; //ボタンのウワモノの色
  if (inputButtonType == testModeButtonType.select) {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.blue.shade200;
    buttonFillColorSel = Colors.blue.shade400;
    buttonLabelColor = Colors.grey.shade900;
  } else if (inputButtonType == testModeButtonType.gray) {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.grey.shade200;
    buttonFillColorSel = Colors.grey.shade400;
    buttonLabelColor = Colors.grey.shade500;
  } else if (inputButtonType == testModeButtonType.graySelect) {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.blue.shade100;
    buttonFillColorSel = Colors.blue.shade300;
    buttonLabelColor = Colors.grey.shade500;
  } else {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.white;
    buttonFillColorSel = Colors.blue.shade300;
    buttonLabelColor = Colors.black;
  }
  return ElevatedButton(
      onPressed: () {
        function();
      },
      style: ElevatedButton.styleFrom(
        fixedSize: Size(width, height),
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
        side: BorderSide(color: buttonBorderColor, width: 2),
        backgroundColor: buttonFillColor,
        foregroundColor: buttonFillColorSel,
      ),
      child: Text(text,
          style: TextStyle(fontSize: inputFontSize, color: buttonLabelColor),
          textAlign: TextAlign.center));
}

//テストモード用アイコン描画
//引数1 - Function function : アイコン押下時に呼び出される関数
//引数2 - IconData id : アイコンの上に描画する絵のID
//引数3 - double iconSize : アイコンの上に描画する絵の大きさ
//引数4 - double inputWidth : アイコンの横サイズ(指定なしの場合は80)
//引数5 - double inputHeight : アイコンの縦サイズ(指定なしの場合は80)
//引数6 - testModeButtonType inputButtonType : ボタンの状態(指定なしの場合はnormal)
//引数7 - bool isDisable : ボタン無効化=true, ボタン有効化=false
Widget testModeIconButtonWidget(Function function, IconData id, double iconSize,
    {double inputWidth = 80,
    double inputHeight = 80,
    testModeButtonType inputButtonType = testModeButtonType.normal,
    bool isDisable = false}) {
  Color buttonBorderColor; //ボタンの枠色
  Color buttonFillColor; //ボタンの色
  Color buttonFillColorSel; //ボタンの選択色
  Color buttonLabelColor; //ボタンのウワモノの色
  if (inputButtonType == testModeButtonType.select) {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.blue.shade200;
    buttonFillColorSel = Colors.blue.shade400;
    buttonLabelColor = Colors.blue.shade900;
  } else if (inputButtonType == testModeButtonType.gray) {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.grey.shade200;
    buttonFillColorSel = Colors.grey.shade400;
    buttonLabelColor = Colors.grey.shade500;
  } else if (inputButtonType == testModeButtonType.graySelect) {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.blue.shade100;
    buttonFillColorSel = Colors.blue.shade300;
    buttonLabelColor = Colors.grey.shade500;
  } else {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.white;
    buttonFillColorSel = Colors.blue.shade300;
    buttonLabelColor = Colors.indigo.shade700;
  }
  return ElevatedButton(
    onPressed: isDisable
        ? null
        : () {
            function();
          },
    style: ElevatedButton.styleFrom(
      fixedSize: Size(inputWidth, inputHeight),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      side: BorderSide(color: buttonBorderColor, width: 2),
      backgroundColor: buttonFillColor,
      foregroundColor: buttonFillColorSel,
      padding: EdgeInsets.zero,
    ),
    child: Align(
      alignment: Alignment.center,
      child: Icon(
        id,
        size: iconSize,
        color: buttonLabelColor,
        shadows: [
          Shadow(color: buttonLabelColor, blurRadius: 10),
        ],
      ),
    ),
  );
}

//テストモード用カスタムアイコン描画(※設定内容によってはアイコンの横サイズが引数よりも大きくなることがある)
//引数1 - Function function : アイコン押下時に呼び出される関数
//引数2 - String iconImagePath : アイコンの上に描画する画像のパス
//引数4 - double inputWidth : アイコンの横サイズ(指定なしの場合は80)
//引数5 - double inputHeight : アイコンの縦サイズ(指定なしの場合は80)
//引数6 - testModeButtonType inputButtonType : ボタンの状態(指定なしの場合はnormal)
Widget testModeCustomIconButtonWidget(Function function, String iconImagePath,
    {double inputWidth = 80,
    double inputHeight = 80,
    testModeButtonType inputButtonType = testModeButtonType.normal}) {
  Color buttonBorderColor; //ボタンの枠色
  Color buttonFillColor; //ボタンの色
  Color buttonFillColorSel; //ボタンの選択色
  if (inputButtonType == testModeButtonType.select) {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.blue.shade200;
    buttonFillColorSel = Colors.blue.shade300;
  } else if (inputButtonType == testModeButtonType.gray) {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.grey.shade200;
    buttonFillColorSel = Colors.grey.shade300;
  } else if (inputButtonType == testModeButtonType.graySelect) {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.blue.shade100;
    buttonFillColorSel = Colors.blue.shade200;
  } else {
    buttonBorderColor = Colors.grey;
    buttonFillColor = Colors.white;
    buttonFillColorSel = Colors.blue.shade100;
  }
  return MaterialButton(
    height: inputHeight,
    minWidth: inputWidth,
    color: buttonFillColor,
    highlightColor: buttonFillColorSel,
    shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
        side: BorderSide(color: buttonBorderColor, width: 2)),
    child: Container(
      height: inputHeight - 20,
      width: inputWidth - 20,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(iconImagePath),
          fit: BoxFit.contain,
        ),
      ),
    ),
    onPressed: () {
      function();
    },
  );
}

//文字描画
//引数1 - String text　： 文字
//引数2 - double inputFontSize : フォントサイズ(指定なしの場合は36)
//引数3 - FontWeight inputFontWeight : フォント太さ(指定なしの場合はノーマル)
//引数4 - Color foreColor : フォント色(指定なしの場合は黒)
Widget testModeTextWidget(String text,
        {double inputFontSize = 36,
        FontWeight inputFontWeight = FontWeight.normal,
        Color foreColor = Colors.black}) =>
    Text(text,
        style: TextStyle(
            fontSize: inputFontSize,
            color: foreColor,
            fontWeight: inputFontWeight));

//色指定付き文字描画
//引数1 - String text　： 文字
//引数2 - Color textColor : 文字色
//引数3 - double inputFontSize : フォントサイズ(指定なしの場合は36)
//引数4 - FontWeight inputFontWeight : フォント太さ(指定なしの場合はノーマル)
Widget testModeTextColorWidget(String text, Color textColor,
        {double inputFontSize = 36,
        FontWeight inputFontWeight = FontWeight.normal}) =>
    Text(text,
        style: TextStyle(
            fontSize: inputFontSize,
            color: textColor,
            fontWeight: inputFontWeight));

//白黒反転指定付き文字描画
//引数1 - String text　： 文字
//引数2 - bool reversal : 白黒反転指定(trueの場合は反転)
//引数3 - double inputFontSize : フォントサイズ(指定なしの場合は36)
//引数4 - FontWeight inputFontWeight : フォント太さ(指定なしの場合はノーマル)
Widget testModeTextReversalWidget(String text, bool reversal,
    {double inputFontSize = 36,
    FontWeight inputFontWeight = FontWeight.normal}) {
  Widget result;
  if (reversal == true) {
    result = Text(text,
        style: TextStyle(
            fontSize: inputFontSize,
            color: Colors.white,
            backgroundColor: Colors.black,
            fontWeight: inputFontWeight));
  } else {
    result = Text(text,
        style: TextStyle(
            fontSize: inputFontSize,
            color: Colors.black,
            fontWeight: inputFontWeight));
  }
  return result;
}

//領域指定数値描画(領域内で右寄せ)
//引数1 - int num　： 描画したい数値
//引数2 - bool reversal : 白黒反転指定(trueの場合は反転)
//引数3 - double width : 領域の横サイズ
//引数4 - double height : 領域の縦サイズ+
//引数5 - double inputFontSize : フォントサイズ(指定なしの場合は36)
Widget containerNumberWidget(
    int num, bool reversal, double width, double height,
    {double inputFontSize = 36,
    Color foregroundColor = Colors.black,
    Color backgroundColor = Colors.white,
    bool plusSignOn = false}) {
  Widget result;
  String number = num.abs().toString();
  String symbol;
  if (num > 0) {
    if (plusSignOn == true) {
      symbol = '+';
    } else {
      symbol = '';
    }
  } else if (num < 0) {
    symbol = '-';
  } else {
    symbol = '';
  }
  if (reversal == true) {
    result = Container(
      height: height,
      width: width,
      alignment: Alignment.centerRight,
      child: Text(symbol + number,
          style: TextStyle(
              fontSize: inputFontSize,
              color: backgroundColor,
              backgroundColor: foregroundColor)),
    );
  } else {
    result = Container(
      height: height,
      width: width,
      alignment: Alignment.centerRight,
      child: Text(symbol + number,
          style: TextStyle(fontSize: inputFontSize, color: foregroundColor)),
    );
  }
  return result;
}

//領域指定数値描画(領域内で右寄せ)
//引数1 - int num　： 描画したい数値
//引数2 - bool reversal : 白黒反転指定(trueの場合は反転)
//引数3 - double width : 領域の横サイズ
//引数4 - double height : 領域の縦サイズ
//引数5 - double inputFontSize : フォントサイズ(指定なしの場合は36)
Widget containerNumberAddSymbolWidget(
    int num, bool reversal, double width, double height,
    {double inputFontSize = 36,
    Color foregroundColor = Colors.black,
    Color backgroundColor = Colors.white}) {
  Widget result;
  String number = num.abs().toString();
  String symbol;
  if (num > 0) {
    symbol = '+';
  } else if (num < 0) {
    symbol = '-';
  } else {
    symbol = '';
  }
  if (reversal == true) {
    result = Container(
      height: height,
      width: width,
      alignment: Alignment.centerRight,
      child: Text(symbol + number,
          style: TextStyle(
              fontSize: inputFontSize,
              color: backgroundColor,
              backgroundColor: foregroundColor)),
    );
  } else {
    result = Container(
      height: height,
      width: width,
      alignment: Alignment.centerRight,
      child: Text(symbol + number,
          style: TextStyle(fontSize: inputFontSize, color: foregroundColor)),
    );
  }
  return result;
}

//符号付き桁数指定領域指定数値描画(領域内で右寄せ)
//引数1 - int num　： 描画したい数値
//引数2 - int digit : 符号を除いた桁数
//引数3 - bool reversal : 白黒反転指定(trueの場合は反転)
//引数4 - double width : 領域の横サイズ
//引数5 - double height : 領域の縦サイズ
//引数6 - double inputFontSize : フォントサイズ(指定なしの場合は36)
Widget containerNumberDigitWidget(
    int num, int digit, bool reversal, double width, double height,
    {double inputFontSize = 36,
    Color foregroundColor = Colors.black,
    Color backgroundColor = Colors.white}) {
  Widget result;
  String number = num.abs().toString().padLeft(digit, '0');
  String symbol;
  if (num > 0) {
    symbol = '';
  } else if (num < 0) {
    symbol = '-';
  } else {
    symbol = '';
  }
  if (reversal == true) {
    result = Container(
      height: height,
      width: width,
      alignment: Alignment.centerRight,
      child: Text(symbol + number,
          style: TextStyle(
              fontSize: inputFontSize,
              color: backgroundColor,
              backgroundColor: foregroundColor)),
    );
  } else {
    result = Container(
      height: height,
      width: width,
      alignment: Alignment.centerRight,
      child: Text(symbol + number,
          style: TextStyle(fontSize: inputFontSize, color: foregroundColor)),
    );
  }
  return result;
}

//符号付き桁数指定領域指定数値描画(領域内で右寄せ)
//引数1 - int num　： 描画したい数値
//引数2 - int digit : 符号を除いた桁数
//引数3 - bool reversal : 白黒反転指定(trueの場合は反転)
//引数4 - double width : 領域の横サイズ
//引数5 - double height : 領域の縦サイズ
//引数6 - double inputFontSize : フォントサイズ(指定なしの場合は36)
Widget containerNumberDigitAddSymbolWidget(
    int num, int digit, bool reversal, double width, double height,
    {double inputFontSize = 36,
    Color foregroundColor = Colors.black,
    Color backgroundColor = Colors.white}) {
  Widget result;
  String number = num.abs().toString().padLeft(digit, '0');
  String symbol;
  if (num > 0) {
    symbol = '+';
  } else if (num < 0) {
    symbol = '-';
  } else {
    symbol = '';
  }
  if (reversal == true) {
    result = Container(
      height: height,
      width: width,
      alignment: Alignment.centerRight,
      child: Text(symbol + number,
          style: TextStyle(
              fontSize: inputFontSize,
              color: backgroundColor,
              backgroundColor: foregroundColor)),
    );
  } else {
    result = Container(
      height: height,
      width: width,
      alignment: Alignment.centerRight,
      child: Text(symbol + number,
          style: TextStyle(fontSize: inputFontSize, color: foregroundColor)),
    );
  }
  return result;
}

//トグルボタン描画
//引数1 - List<bool> listIsSelected　： 選択状態を示すリスト
//引数2 - List<Widget> listChildren　： 表示Widgetリスト
//引数3 - Function fncOnPressed　： ボタン選択時の処理関数（int 引数付き）
// ex.
//  List<bool> listIsSelected = [true, false];
//  static const List<Widget> listChildren = <Widget>[Text('ON'), Text('OFF')];
//  void fncOnPressed(int index) { listIsSelected[index] = true; ...}
Widget testModeToggleButtonsWidget(
    List<bool> listIsSelected, List<Widget> listChildren, Function fncOnPressed,
    {double boxHeight = 80.0, double boxWidth = 120.0}) {
  Widget result;
  result = ToggleButtons(
    // direction: vertical ? Axis.vertical : Axis.horizontal,
    borderRadius: const BorderRadius.all(Radius.circular(8)),
    borderWidth: 2,
    selectedBorderColor: Colors.blue[700],
    selectedColor: Colors.white,
    fillColor: Colors.blue[200],
    color: Colors.blue[400],
    constraints: BoxConstraints(
      minHeight: boxHeight,
      minWidth: boxWidth,
    ),
    isSelected: listIsSelected,
    children: listChildren,
    onPressed: (index) => fncOnPressed(index),
  );
  return result;
}

// topメニューボタン描画
//引数1 - BuildContext context　  ： 親
//引数2 - String childPageNo　    ： メニュー項目番号
//引数3 - String childPageName　  ： メニュー項目名
//引数4 - Widget? fncOnPressed　  ： ボタン選択時の処理関数（Null許可）
//引数5 - int pageNo              ： メニュー番号
//引数6 - BPIFSendKey? ttkPushNo  ： キー関数ID
//引数7 - int menuLockStates      ： メニューロック状態(0: ロックなし, 1: ロックあり)
Widget testModeChildPageTransitionButtonWidget(
        BuildContext context,
        String childPageNo,
        String childPageName,
        Widget? fncOnPressed,
        WidgetRef? ref,
        int pageNo,
        BPIFSendKey? ttkPushNo,
        {int menuLockStates = 0,
        Function? menuKeyJudge}) =>
    ElevatedButton(
      onPressed: (fncOnPressed == null || menuLockStates == 1)
          ? null
          : () {
              bool judge = true;
              int errcode = BPIFSendError_t.bpifNoError.index;
              if (menuKeyJudge != null) {
                // ボタン有効判定（メカキー押し）
                judge = menuKeyJudge(pageNo);
                debugPrint("debug: judge=${judge ? 'true' : 'false'}");
              }

              if (judge == true) {
                tpd.funcCallMainApiPanelInit();
                // テストモード用の共有メモリ TestModeSharedData を各テストモードで独自に使っているため、ページ遷移する前に0クリアしておく
                tpd.clearTestModeData();

                if (ref != null && ttkPushNo != null) {
                  debugPrint("debug: pressed pageNo=$pageNo");
                  final testModePageNoNotifier =
                      ref.watch(testModePageNoProvider.notifier);
                  testModePageNoNotifier.set(pageNo);

                  // テストモード用の共有メモリ準備
                  errcode = tpd.sendDisplayDataSync(ttkPushNo);
                }

                if (errcode == BPIFSendError_t.bpifNoError.index) {
                  TestModePagesRoute().push(
                      nextPageWidget: fncOnPressed, arguments: fncOnPressed);
                } else {
                  SystemSoundPlayer().play(SystemSoundEnum.invalid);
                }
              }
            },
      style: ElevatedButton.styleFrom(
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
        side: const BorderSide(color: Colors.white60, width: 1),
        backgroundColor: Colors.white,
        foregroundColor: Colors.blue,
      ),
      child: SizedBox(
        width: 300,
        height: 80,
        child: Row(children: <Widget>[
          Text(
            childPageNo,
            style: const TextStyle(fontSize: 24, color: Colors.black),
          ),
          Expanded(
            child: Text((menuLockStates == 0 ? childPageName : ''),
                style: const TextStyle(fontSize: 24, color: Colors.black),
                maxLines: 3),
          ),
        ]),
      ),
    );
