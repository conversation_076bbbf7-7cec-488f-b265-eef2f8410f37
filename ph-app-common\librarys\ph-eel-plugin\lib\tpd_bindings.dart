import 'dart:ffi';

import 'package:ffi/ffi.dart' as ffi;
import 'package:flutter/foundation.dart';

import 'lib_base.dart';
import 'tpd_bindings_generated.dart';

final TpDgbBindings _bindings = TpDgbBindings(panellib);
TpDgbBindings getTpDgbBindings() => _bindings;

// ライブラリ用Workフォルダパス
const String strFolderPath =
    "/sdcard/Android/data/com.brother.ph.eel.plugin_sample/files/Documents/work/";
// int registStateCallback(Pointer<NativeFunction<StateCallback>> callback) =>
//     _bindings.BPIFRegisterStateCallback(callback);
// int registErrorCallback(Pointer<NativeFunction<ErrorCallback>> callback) =>
//     _bindings.BPIFRegisterErrorCallback(callback);
// int registBeepCallback(Pointer<NativeFunction<BeepCallback>> callback) =>
//     _bindings.BPIFRegisterBeepCallback(callback);
//
// void unregistStateCallback() => _bindings.BPIFUnregisterStateCallback();
// void unregisterErrorCallback() => _bindings.BPIFUnregisterErrorCallback();
// void unregisterBeepCallback() => _bindings.BPIFUnregisterBeepCallback();

//int funcCallMainApiPanelInit() => _bindings.BPIFInit();
int funcCallMainApiPanelInit() {
  Pointer<Char> charPath = ffi.calloc<Char>(255);

  final strCode = strFolderPath.runes.toList();

  int i = 0;
  for (i = 0; i < strCode.length; i++) {
    charPath[i] = strCode[i];
  }
  charPath[i] = 0;

  int ret = _bindings.BPIFInit(charPath); // ライブラリ用Workフォルダパスもセットする

  ffi.calloc.free(charPath);

  return ret;
}

// Pointer<Uint8> getStat() {
//   _bindings.BPIFGetState(stateData as Pointer<Void>);
//   return stateData;
// }

Pointer<ErrorInfo_t> getError() {
  _bindings.BPIFGetError(tmpError);
  return tmpError;
}

int getBeepType() {
  return _bindings.BPIFGetBeep();
}

int sendDisplayData(BPIFSendKey send_msg_id) {
  debugPrint('BPIFSendDisplayData: ${send_msg_id.name}(${send_msg_id.index})');
  return _bindings.BPIFSendDisplayData(send_msg_id.index);
}

int sendDisplayDataSync(BPIFSendKey send_msg_id) {
  debugPrint(
      'BPIFSendDisplayDataSync: ${send_msg_id.name}(${send_msg_id.index})');
  return _bindings.BPIFSendDisplayDataSync(send_msg_id.index);
}

Pointer<ErrorInfo_t> tmpError = ErrorInfo_t.allocate();
Pointer<Uint8> stateData = ffi.calloc<Uint8>(256);

/// テストモードのグローバル共有メモリ領域を 0 初期化
void clearTestModeData() {
  _bindings.BPIFClearTestModeData();
}

/// テストモードdummyのグローバル共有メモリ取得
Pointer<BPIFTestModeDummy_t> tmDummyData = ffi.calloc<BPIFTestModeDummy_t>();
Pointer<BPIFTestModeDummy_t> getTestModeDataTmDummy() {
  _bindings.BPIFGetTestModeData_tmDummy(tmDummyData);
  return tmDummyData;
}

/// テストモードdummyのグローバル共有メモリ設定
void setTestModeDataTmDummy(Pointer<BPIFTestModeDummy_t> tmpData) {
  _bindings.BPIFSetTestModeData_tmDummy(tmpData);
}

void setTestModeDataTmDummyFile(String tmpData) {
  _bindings.BPIFSetTestModeData_tmDummy_file(tmpData);
}

/// テストモードメニューの共有メモリ取得
Pointer<BPIFTestMode00Param_t> tm00Data = ffi.calloc<BPIFTestMode00Param_t>();
Pointer<BPIFTestMode00Param_t> getTestModeDataTm00() {
  _bindings.BPIFGetTestModeData_tm00(tm00Data);
  return tm00Data;
}

/// テストモード01の共有メモリ取得
Pointer<BPIFTestMode01Param_t> tm01Data = ffi.calloc<BPIFTestMode01Param_t>();
Pointer<BPIFTestMode01Param_t> getTestModeDataTm01() {
  _bindings.BPIFGetTestModeData_tm01(tm01Data);
  return tm01Data;
}

/// テストモード02の共有メモリ取得
Pointer<BPIFTestMode02Param_t> tm02Data = ffi.calloc<BPIFTestMode02Param_t>();
Pointer<BPIFTestMode02Param_t> getTestModeDataTm02() {
  _bindings.BPIFGetTestModeData_tm02(tm02Data);
  return tm02Data;
}

void setTestModeDataTm02(Pointer<BPIFTestMode02Param_t> tmpData) {
  _bindings.BPIFSetTestModeData_tm02(tmpData);
}

/// テストモード03の共有メモリ取得
Pointer<BPIFTestMode03Param_t> tm03Data = ffi.calloc<BPIFTestMode03Param_t>();
Pointer<BPIFTestMode03Param_t> getTestModeDataTm03() {
  _bindings.BPIFGetTestModeData_tm03(tm03Data);
  return tm03Data;
}

void setTestModeDataTm03(Pointer<BPIFTestMode03Param_t> tmpData) {
  _bindings.BPIFSetTestModeData_tm03(tmpData);
}

/// テストモード06の共有メモリ取得
Pointer<BPIFTestMode06Param_t> tm06Data = ffi.calloc<BPIFTestMode06Param_t>();
Pointer<BPIFTestMode06Param_t> getTestModeDataTm06() {
  _bindings.BPIFGetTestModeData_tm06(tm06Data);
  return tm06Data;
}

/// テストモード07の共有メモリ取得
Pointer<BPIFTestMode07Param_t> tm07Data = ffi.calloc<BPIFTestMode07Param_t>();
Pointer<BPIFTestMode07Param_t> getTestModeDataTm07() {
  _bindings.BPIFGetTestModeData_tm07(tm07Data);
  return tm07Data;
}

/// テストモード10の共有メモリ取得
Pointer<BPIFTestMode10Param_t> tm10Data = ffi.calloc<BPIFTestMode10Param_t>();
Pointer<BPIFTestMode10Param_t> getTestModeDataTm10() {
  _bindings.BPIFGetTestModeData_tm10(tm10Data);
  return tm10Data;
}

/// テストモード11の共有メモリ取得
Pointer<BPIFTestMode11Param_t> tm11Data = ffi.calloc<BPIFTestMode11Param_t>();
Pointer<BPIFTestMode11Param_t> getTestModeDataTm11() {
  _bindings.BPIFGetTestModeData_tm11(tm11Data);
  return tm11Data;
}

/// テストモード13の共有メモリ取得
Pointer<BPIFTestMode13Param_t> tm13Data = ffi.calloc<BPIFTestMode13Param_t>();
Pointer<BPIFTestMode13Param_t> getTestModeDataTm13() {
  _bindings.BPIFGetTestModeData_tm13(tm13Data);
  return tm13Data;
}

/// テストモード14の共有メモリ取得
Pointer<BPIFTestMode14Param_t> tm14Data = ffi.calloc<BPIFTestMode14Param_t>();
Pointer<BPIFTestMode14Param_t> getTestModeDataTm14() {
  _bindings.BPIFGetTestModeData_tm14(tm14Data);
  return tm14Data;
}

/// テストモード19の共有メモリ取得
Pointer<BPIFTestMode19Param_t> tm19Data = ffi.calloc<BPIFTestMode19Param_t>();
Pointer<BPIFTestMode19Param_t> getTestModeDataTm19() {
  _bindings.BPIFGetTestModeData_tm19(tm19Data);
  return tm19Data;
}

/// テストモード22の共有メモリ取得
Pointer<BPIFTestMode22Param_t> tm22Data = ffi.calloc<BPIFTestMode22Param_t>();
Pointer<BPIFTestMode22Param_t> getTestModeDataTm22() {
  _bindings.BPIFGetTestModeData_tm22(tm22Data);
  return tm22Data;
}

/// テストモード23の共有メモリ取得
Pointer<BPIFTestMode23Param_t> tm23Data = ffi.calloc<BPIFTestMode23Param_t>();
Pointer<BPIFTestMode23Param_t> getTestModeDataTm23() {
  _bindings.BPIFGetTestModeData_tm23(tm23Data);
  return tm23Data;
}

/// テストモード25の共有メモリ取得
Pointer<BPIFTestMode25Param_t> tm25Data = ffi.calloc<BPIFTestMode25Param_t>();
Pointer<BPIFTestMode25Param_t> getTestModeDataTm25() {
  _bindings.BPIFGetTestModeData_tm25(tm25Data);
  return tm25Data;
}

void setTestModeDataTm25(Pointer<BPIFTestMode25Param_t> tmpData) {
  _bindings.BPIFSetTestModeData_tm25(tmpData);
}

/// テストモード27の共有メモリ取得
Pointer<BPIFTestMode27Param_t> tm27Data = ffi.calloc<BPIFTestMode27Param_t>();
Pointer<BPIFTestMode27Param_t> getTestModeDataTm27() {
  _bindings.BPIFGetTestModeData_tm27(tm27Data);
  return tm27Data;
}

/// テストモード28の共有メモリ取得
Pointer<BPIFTestMode28Param_t> tm28Data = ffi.calloc<BPIFTestMode28Param_t>();
Pointer<BPIFTestMode28Param_t> getTestModeDataTm28() {
  _bindings.BPIFGetTestModeData_tm28(tm28Data);
  return tm28Data;
}

/// テストモード29の共有メモリ取得
Pointer<BPIFTestMode29Param_t> tm29Data = ffi.calloc<BPIFTestMode29Param_t>();
Pointer<BPIFTestMode29Param_t> getTestModeDataTm29() {
  _bindings.BPIFGetTestModeData_tm29(tm29Data);
  return tm29Data;
}

/// テストモード17の共有メモリ取得
Pointer<BPIFTestMode17Param_t> tm17Data = ffi.calloc<BPIFTestMode17Param_t>();
Pointer<BPIFTestMode17Param_t> getTestModeDataTm17() {
  _bindings.BPIFGetTestModeData_tm17(tm17Data);
  return tm17Data;
}

/// テストモード17の共有メモリ設定
void setTestModeDataTm17(Pointer<BPIFTestMode17Param_t> tmpData) {
  _bindings.BPIFSetTestModeData_tm17(tmpData);
}

void setTestModeDataTm17Mode(int tmpData) {
  // debugPrint("debug1: set mode $tmpData");
  // Future<Pointer<Uint8>> buffer = _bindings.BPIFGetTestModeData_buffer();
  // debugPrint("debug2: $buffer");
  // Pointer<BPIFTestMode17Param_t> tm17 = buffer as Pointer<BPIFTestMode17Param_t>;
  // debugPrint("debug3: $tm17");

  _bindings.BPIFSetTestModeData_tm17_mode(tmpData);
}

void setTestModeDataTm17State(int tmpData) {
  _bindings.BPIFSetTestModeData_tm17_state(tmpData);
}

void setTestModeDataTm17PanelAddress(int tmpData) {
  _bindings.BPIFSetTestModeData_tm17_panel_address(tmpData);
}

void setTestModeDataTm17PanelWriteValue(int tmpData) {
  _bindings.BPIFSetTestModeData_tm17_panel_writeValue(tmpData);
}

void setTestModeDataTm17MachineAddress(int tmpData) {
  _bindings.BPIFSetTestModeData_tm17_machine_address(tmpData);
}

void setTestModeDataTm17MachineWriteValue(int tmpData) {
  _bindings.BPIFSetTestModeData_tm17_machine_writeValue(tmpData);
}

/// テストモード26の共有メモリ取得
Pointer<BPIFTestMode26Param_t> tm26Data = ffi.calloc<BPIFTestMode26Param_t>();
Pointer<BPIFTestMode26Param_t> getTestModeDataTm26() {
  _bindings.BPIFGetTestModeData_tm26(tm26Data);
  return tm26Data;
}

/// テストモード30の共有メモリ取得
Pointer<BPIFTestMode30Param_t> tm30Data = ffi.calloc<BPIFTestMode30Param_t>();
Pointer<BPIFTestMode30Param_t> getTestModeDataTm30() {
  _bindings.BPIFGetTestModeData_tm30(tm30Data);
  return tm30Data;
}

/// テストモード30の共有メモリ設定
void setTestModeDataTm30(Pointer<BPIFTestMode30Param_t> tmpData) {
  _bindings.BPIFSetTestModeData_tm30(tmpData);
}

void setTestModeDataTm30EepFilePath(String tmpData) {
  _bindings.BPIFSetTestModeData_tm30_eepFilePath(tmpData);
}

/// テストモード31の共有メモリ取得
Pointer<BPIFTestMode31Param_t> tm31Data = ffi.calloc<BPIFTestMode31Param_t>();
Pointer<BPIFTestMode31Param_t> getTestModeDataTm31() {
  _bindings.BPIFGetTestModeData_tm31(tm31Data);
  return tm31Data;
}

/// テストモード32の共有メモリ取得
Pointer<BPIFTestMode32Param_t> tm32Data = ffi.calloc<BPIFTestMode32Param_t>();
Pointer<BPIFTestMode32Param_t> getTestModeDataTm32() {
  _bindings.BPIFGetTestModeData_tm32(tm32Data);
  return tm32Data;
}

/// テストモード34の共有メモリ取得
Pointer<BPIFTestMode34Param_t> tm34Data = ffi.calloc<BPIFTestMode34Param_t>();
Pointer<BPIFTestMode34Param_t> getTestModeDataTm34() {
  _bindings.BPIFGetTestModeData_tm34(tm34Data);
  return tm34Data;
}

/// テストモード35の共有メモリ取得
Pointer<BPIFTestMode35Param_t> tm35Data = ffi.calloc<BPIFTestMode35Param_t>();
Pointer<BPIFTestMode35Param_t> getTestModeDataTm35() {
  _bindings.BPIFGetTestModeData_tm35(tm35Data);
  return tm35Data;
}

/// テストモード36の共有メモリ取得
Pointer<BPIFTestMode36Param_t> tm36Data = ffi.calloc<BPIFTestMode36Param_t>();
Pointer<BPIFTestMode36Param_t> getTestModeDataTm36() {
  _bindings.BPIFGetTestModeData_tm36(tm36Data);
  return tm36Data;
}

void setTestModeDataTm36(Pointer<BPIFTestMode36Param_t> tmpData) {
  _bindings.BPIFSetTestModeData_tm36(tmpData);
}

/// テストモード37の共有メモリ取得
Pointer<BPIFTestMode37Param_t> tm37Data = ffi.calloc<BPIFTestMode37Param_t>();
Pointer<BPIFTestMode37Param_t> getTestModeDataTm37() {
  _bindings.BPIFGetTestModeData_tm37(tm37Data);
  return tm37Data;
}

void setTestModeDataTm37(Pointer<BPIFTestMode37Param_t> tmpData) {
  _bindings.BPIFSetTestModeData_tm37(tmpData);
}

/// テストモード39の共有メモリ取得
Pointer<BPIFTestMode39Param_t> tm39Data = ffi.calloc<BPIFTestMode39Param_t>();
Pointer<BPIFTestMode39Param_t> getTestModeDataTm39() {
  _bindings.BPIFGetTestModeData_tm39(tm39Data);
  return tm39Data;
}

void setTestModeDataTm39(Pointer<BPIFTestMode39Param_t> tmpData) {
  _bindings.BPIFSetTestModeData_tm39(tmpData);
}

/// テストモード42の共有メモリ取得
Pointer<BPIFTestMode42Param_t> tm42Data = ffi.calloc<BPIFTestMode42Param_t>();
Pointer<BPIFTestMode42Param_t> getTestModeDataTm42() {
  _bindings.BPIFGetTestModeData_tm42(tm42Data);
  return tm42Data;
}

void setTestModeDataTm42(Pointer<BPIFTestMode42Param_t> tmpData) {
  _bindings.BPIFSetTestModeData_tm42(tmpData);
}

/// テストモード44の共有メモリ取得
Pointer<BPIFTestMode44Param_t> tm44Data = ffi.calloc<BPIFTestMode44Param_t>();
Pointer<BPIFTestMode44Param_t> getTestModeDataTm44() {
  _bindings.BPIFGetTestModeData_tm44(tm44Data);
  return tm44Data;
}

/// テストモード47の共有メモリ取得
Pointer<BPIFTestMode47Param_t> tm47Data = ffi.calloc<BPIFTestMode47Param_t>();
Pointer<BPIFTestMode47Param_t> getTestModeDataTm47() {
  _bindings.BPIFGetTestModeData_tm47(tm47Data);
  return tm47Data;
}

/// テストモード48の共有メモリ取得
Pointer<BPIFTestMode48Param_t> tm48Data = ffi.calloc<BPIFTestMode48Param_t>();
Pointer<BPIFTestMode48Param_t> getTestModeDataTm48() {
  _bindings.BPIFGetTestModeData_tm48(tm48Data);
  return tm48Data;
}

/// テストモード49の共有メモリ取得
Pointer<BPIFTestMode49Param_t> tm49Data = ffi.calloc<BPIFTestMode49Param_t>();
Pointer<BPIFTestMode49Param_t> getTestModeDataTm49() {
  _bindings.BPIFGetTestModeData_tm49(tm49Data);
  return tm49Data;
}

/// テストモード50の共有メモリ取得
Pointer<BPIFTestMode50Param_t> tm50Data = ffi.calloc<BPIFTestMode50Param_t>();
Pointer<BPIFTestMode50Param_t> getTestModeDataTm50() {
  _bindings.BPIFGetTestModeData_tm50(tm50Data);
  return tm50Data;
}

/// テストモード51の共有メモリ取得
Pointer<BPIFTestMode51Param_t> tm51Data = ffi.calloc<BPIFTestMode51Param_t>();
Pointer<BPIFTestMode51Param_t> getTestModeDataTm51() {
  _bindings.BPIFGetTestModeData_tm51(tm51Data);
  return tm51Data;
}

/// テストモード52の共有メモリ取得
Pointer<BPIFTestMode52Param_t> tm52Data = ffi.calloc<BPIFTestMode52Param_t>();
Pointer<BPIFTestMode52Param_t> getTestModeDataTm52() {
  _bindings.BPIFGetTestModeData_tm52(tm52Data);
  return tm52Data;
}

/// テストモード53の共有メモリ取得
Pointer<BPIFTestMode53Param_t> tm53Data = ffi.calloc<BPIFTestMode53Param_t>();
Pointer<BPIFTestMode53Param_t> getTestModeDataTm53() {
  _bindings.BPIFGetTestModeData_tm53(tm53Data);
  return tm53Data;
}

/// テストモード60の共有メモリ取得
Pointer<BPIFTestMode60Param_t> tm60Data = ffi.calloc<BPIFTestMode60Param_t>();
Pointer<BPIFTestMode60Param_t> getTestModeDataTm60() {
  _bindings.BPIFGetTestModeData_tm60(tm60Data);
  return tm60Data;
}

/// テストモード60の共有メモリ設定
void setTestModeDataTm60(Pointer<BPIFTestMode60Param_t> tmpData) {
  _bindings.BPIFSetTestModeData_tm60(tmpData);
}

void setTestModeDataTm60EepFilePanelPath(String tmpData) {
  _bindings.BPIFSetTestModeData_tm60_eepToFilePanelPath(tmpData);
}

void setTestModeDataTm60EepFileMachinePath(String tmpData) {
  _bindings.BPIFSetTestModeData_tm60_eepToFileMachinePath(tmpData);
}

void setTestModeDataTm60EepFilePath(String tmpData) {
  _bindings.BPIFSetTestModeData_tm60_eepFilePath(tmpData);
}

/// テストモード62の共有メモリ取得
Pointer<BPIFTestMode62Param_t> tm62Data = ffi.calloc<BPIFTestMode62Param_t>();
Pointer<BPIFTestMode62Param_t> getTestModeDataTm62() {
  _bindings.BPIFGetTestModeData_tm62(tm62Data);
  return tm62Data;
}

/// テストモード64の共有メモリ取得
Pointer<BPIFTestMode64Param_t> tm64Data = ffi.calloc<BPIFTestMode64Param_t>();
Pointer<BPIFTestMode64Param_t> getTestModeDataTm64() {
  _bindings.BPIFGetTestModeData_tm64(tm64Data);
  return tm64Data;
}

/// テストモード65の共有メモリ取得
Pointer<BPIFTestMode65Param_t> tm65Data = ffi.calloc<BPIFTestMode65Param_t>();
Pointer<BPIFTestMode65Param_t> getTestModeDataTm65() {
  _bindings.BPIFGetTestModeData_tm65(tm65Data);
  return tm65Data;
}

/// テストモード69の共有メモリ取得
Pointer<BPIFTestMode69Param_t> tm69Data = ffi.calloc<BPIFTestMode69Param_t>();
Pointer<BPIFTestMode69Param_t> getTestModeDataTm69() {
  _bindings.BPIFGetTestModeData_tm69(tm69Data);
  return tm69Data;
}

/// 共通キー
int gotoHome() {
  return _bindings.gotoHome();
}

int gotoHowToUse() {
  return _bindings.gotoHowToUse();
}

int returnHowToUse() {
  return _bindings.returnHowToUse();
}

int gotoSettings() {
  return _bindings.gotoSettings();
}

int returnSettings() {
  return _bindings.returnSettings();
}

int gotoWlanSetting() {
  return _bindings.gotoWlanSetting();
}

int returnWlanSetting() {
  return _bindings.returnWlanSetting();
}

int showChangeView() {
  return _bindings.showChangeView();
}

int hideChangeView() {
  return _bindings.hideChangeView();
}

int gotoClockSetting() {
  return _bindings.gotoClockSetting();
}

int returnClockSetting() {
  return _bindings.returnClockSetting();
}

/// /デバイスの状態
/// タッチパネル操作をロックする
int lockTouch() {
  return _bindings.lockTouch();
}

int clearLockTouch() {
  return _bindings.clearLockTouch();
}

/// メカキーも含めてロックする
int lockAll() {
  return _bindings.lockAll();
}

int xlearLockAll() {
  return _bindings.clearLockAll();
}

/// ActionMatrix_Task 周期処理を一時停止/再開 (スタイル変換を動かす前にsuspend、終わったらresumeで再開すること)
int suspendActionMatrixTask() {
  return _bindings.suspendActionMatrixTask();
}

int resumeActionMatrixTask() {
  return _bindings.resumeActionMatrixTask();
}

/// PHFIRMIIVO-6983 アプリと起動タイミングをあわせる
/// 初回ガイダンスのチェック開始
int initSettingGuidanceStart() {
  return _bindings.initSettingGuidanceStart();
}
/// 初回ガイダンスのチェック完了
int initSettingGuidanceComplete() {
  return _bindings.initSettingGuidanceComplete();
}

// アプリのBPIF_MAX値を通知する
int AppBpifMaxNotification(int idMax) {
  return _bindings.AppBpifMaxNotification(idMax);
}

// /// アプリで動画再生状態になっているかどうか
// int playMovie() {
//   return _bindings.playMovie();
// }
//
// int stopMovie() {
//   return _bindings.stopMovie();
// }

// /// アプリがエラー状態か
// int setErrorState(ErrCode_t error) {
//   return _bindings.setErrorState(error.index);
//
// }
//
// int clearErrorState() {
//   return _bindings.clearErrorState();
// }

/// DeepSleep
int startSleep() {
  return _bindings.startSleep();
}

int stopSleep() {
  return _bindings.stopSleep();
}

/// Screren Server
int startScreenSaver() {
  return _bindings.startScreenSaver();
}

int stopScreenSaver() {
  return _bindings.stopScreenSaver();
}

/// Ecomode LCDオフ
int startEco() {
  return _bindings.startEco();
}

int stopEco() {
  return _bindings.stopEco();
}

/// Home画面から遷移する
int gotoUtlFromHome() {
  return _bindings.gotoUtlFromHome();
}

int gotoEmbFromHome() {
  return _bindings.gotoEmbFromHome();
}

int gotoDisneyFromHome() {
  return _bindings.gotoDisneyFromHome();
}

int gotoMDCFromHome() {
  return _bindings.gotoMDCFromHome();
}

/// SSLedの色
int getSSLedColor() {
  return _bindings.getSSLedColor(ssLedColor);
}

Pointer<Uint8> ssLedColor = ffi.calloc<Uint8>();

// void setMatrixEnableList(int activatePattern){
//   return _bindings.setMatrixEnableList(activatePattern);
// }
