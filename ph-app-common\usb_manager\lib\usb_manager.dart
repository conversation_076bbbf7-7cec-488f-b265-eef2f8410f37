import 'dart:async';
import 'dart:convert';
import 'dart:io' show Directory, Platform;

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:log/log.dart';
import 'package:memory/memory_interface.dart';
import 'package:path/path.dart';

import 'src/mouse_connection_manager.dart';
import 'usb_manager_platform_interface.dart';

class USBInfo {
  USBInfo({
    /// Usb 名前
    required this.usbName,

    /// Usb 残容量
    required this.usbSpace,

    /// Usb パス
    required this.usbPath,
  });
  final String usbName;
  final String usbSpace;
  final String usbPath;
}

class UsbManager {
  UsbManager._internal();

  factory UsbManager() => _instance;
  static final UsbManager _instance = UsbManager._internal();

  /// Usbの最大表示数
  static int maxUsbNumber = 2;

  //
  /// メッセージの受信
  ///
  Future<void> receiveMessage(Function refresh) =>
      UsbManagerPlatform.instance.receiveMessage(refresh);

  ///
  /// USB情報の取得
  ///
  Future<List<USBInfo>> getUsbInfoList() async {
    if (!Platform.isAndroid) {
      return Future(
        () => [
          USBInfo(
              usbName: "usbName1",
              usbSpace: "0MB",
              usbPath: memorySector.usb1.absolutePath),
          USBInfo(
              usbName: "usbName2",
              usbSpace: "0MB",
              usbPath: memorySector.usb2.absolutePath),
        ],
      );
    }
    if (isHiddenApiSupported) {
      return _usbInfoList;
    } else {
      try {
        List<USBInfo> usbInfoList = [];
        for (var file
            in Directory(memorySector.external_usb.absolutePath).listSync()) {
          await _wrapUsbInfoFromPath(file.path, usbInfoList);
        }
        return usbInfoList;
      } catch (e) {
        return [];
      }
    }
  }

  /// 現在の USB 情報を同期的に取得します
  List<USBInfo> getUsbInfoListSync() => _usbInfoList;

  final _usbMonitorChannel = const MethodChannel("usb_monitor");

  /// 現在のシステムが USB 関連の隠し API をサポートしているかどうか
  bool isHiddenApiSupported = true;

  ///
  /// 初期化。 APP起動時に呼び出してください。
  ///
  void init() {
    _workaroundFlutterMouseRemoveIssue();
    _usbMonitorChannel.setMethodCallHandler((call) async {
      if (call.method == "onUsbChanged") {
        onUsbDeviceChanged();
      } else {
        _logcat("Unexpected method call: ${call.method}");
      }
    });
    UsbManagerPlatform.instance.usbManagerInit().then((value) {
      if (value == false) {
        _logcat("Unsupported OS for UsbManager");
        isHiddenApiSupported = false;
      } else if (value == true) {
        // no op
      } else {
        onUsbDeviceChanged();
      }
    });
  }

  ///
  /// 指定したUSBデバイスをマウントする
  ///
  Future<bool?> mountUsbDevice(String usbPath) async {
    if (!Platform.isAndroid) return true;
    if (isHiddenApiSupported == false) return true;
    try {
      return _usbMonitorChannel
          .invokeMethod("mountUsbDevice", {"usbPath": usbPath});
    } catch (e) {
      _logcat("Failed to invoke method `mountUsbDevice`: $e");
      return null;
    }
  }

  ///
  /// 指定されたUSBデバイスをアンインストールします
  ///
  Future<bool?> unmountUsbDevice(String usbPath) async {
    if (!Platform.isAndroid) return true;
    if (isHiddenApiSupported == false) return true;

    try {
      return _usbMonitorChannel
          .invokeMethod("unmountUsbDevice", {"usbPath": usbPath});
    } catch (e) {
      _logcat("Failed to invoke method `unmountUsbDevice`: $e");
      return null;
    }
  }

  ///
  /// すべての USB デバイスをアンマウントする
  ///
  /// TestModeでのみ使用できます。この方法には副作用があるため、参照してください：
  /// https://brothergroup.atlassian.net/browse/PHFIRMIIVO-7788
  ///
  /// [DeviceMemoryModel.beginUsbTransaction] を使用してください。
  ///
  Future<bool?> unmountAllUsbDevices() async {
    if (!Platform.isAndroid) return true;
    if (isHiddenApiSupported == false) return true;

    try {
      return _usbMonitorChannel.invokeMethod("unmountAllUsbDevices");
    } catch (e) {
      _logcat("Failed to invoke method `unmountAllUsbDevices`: $e");
      return false;
    }
  }

  ///
  /// すべての USB デバイスをマウントする
  ///
  /// TestModeでのみ使用できます。この方法には副作用があるため、参照してください：
  /// https://brothergroup.atlassian.net/browse/PHFIRMIIVO-7788
  ///
  /// [DeviceMemoryModel.beginUsbTransaction] を使用してください。
  ///
  Future<bool?> mountAllUsbDevices() async {
    if (!Platform.isAndroid) return true;
    if (isHiddenApiSupported == false) return true;
    try {
      return _usbMonitorChannel.invokeMethod("mountAllUsbDevices");
    } catch (e) {
      _logcat("Failed to invoke method `mountAllUsbDevices`: $e");
      return null;
    }
  }

  ///
  /// 圧縮ファイルから特定のファイルの内容を読み取る
  /// isSuccess:読み取りが成功したかどうか
  /// json:ファイルの内容、JSON形式
  ///
  Future<({bool isSuccess, Map<String, dynamic> json})> readJsonFromZip(
      String zipFilePath, String fileName,
      {String? password}) async {
    Completer<({bool isSuccess, Map<String, dynamic> json})> completer =
        Completer<({bool isSuccess, Map<String, dynamic> json})>();
    Map<String, dynamic> upfHeaderJson = {};

    try {
      UsbManagerPlatform.instance
          .readJsonFromZip(zipFilePath, fileName, password: password)
          .then((value) {
        if (value == null || value.isEmpty) {
          completer.complete((isSuccess: false, json: upfHeaderJson));
          return;
        }

        String jsonString = value;

        upfHeaderJson = jsonDecode(jsonString);
        _logcat("upfHeaderJson:$jsonString");
        completer.complete((isSuccess: true, json: upfHeaderJson));
      });
    } catch (e) {
      _logcat("readJsonFromZip error:$e");
      completer.complete((isSuccess: false, json: upfHeaderJson));
    }

    return completer.future;
  }

  Future<bool> unZip(String zipFilePath, String dirPath,
      {String? password}) async {
    final bool? version = await UsbManagerPlatform.instance
        .unZip(zipFilePath, dirPath, password: password);
    if (version == null) {
      return false;
    } else {
      return version;
    }
  }

  Future<String> calculateSHA256(String filePath) async {
    final String? fileHash =
        await UsbManagerPlatform.instance.calculateSHA256(filePath);

    return fileHash ?? "";
  }

  /// 無効な値
  final int _invalidValue = -1;

  ///
  /// 現在マウントされている USB リストの監視を開始します。
  ///
  /// モニタリングが開始（Sticky）されるとき、または USB が抜き差しされるとき、
  /// [listener] をコールバックし、現在の [USBInfo] リストを渡します。
  ///
  /// 戻り値: [Timer] [Timer.cancel]を呼び出してモニタリングをキャンセルします。
  ///
  Timer _registerUsbChangeListenerLegacy(UsbInfoChangeListener listener) {
    /// USB リスニングポーリング間隔 (ミリ秒)
    const int usbInfoPollingInterval = 100;

    List<USBInfo>? prevUsbInfo;

    return Timer.periodic(const Duration(milliseconds: usbInfoPollingInterval),
        (timer) async {
      try {
        final usbPaths = Directory(memorySector.external_usb.absolutePath)
            .listSync()
            .map((e) => e.path)
            .toList();

        List<USBInfo> curUsbInfo = [];
        bool isChanged = false;

        for (var usbPath in usbPaths) {
          int index = prevUsbInfo?.indexWhere((e) => e.usbPath == usbPath) ??
              _invalidValue;

          if (index != _invalidValue) {
            curUsbInfo.add(prevUsbInfo![index]);
          } else if (await _wrapUsbInfoFromPath(usbPath, curUsbInfo)) {
            isChanged = true;
          }
        }

        if (curUsbInfo.length != prevUsbInfo?.length) {
          isChanged = true;
        }
        // USB リストが変更された場合にのみ実行されます
        if (isChanged) {
          prevUsbInfo = curUsbInfo;
          listener(curUsbInfo);
        }
      } catch (e) {
        // Do nothing
      }
    });
  }

  ///
  /// パスを [USBInfo] としてラップする
  ///
  /// 戻り値: 成功したかどうかを表します
  ///
  Future<bool> _wrapUsbInfoFromPath(
      String path, List<USBInfo> usbInfoList) async {
    int freeSpace = await UsbManagerPlatform.instance.getUsbFreeSpace(path);
    if (freeSpace > _invalidValue) {
      String usbName = basename(path);

      /// USB 名の長さ
      const int usbNameDisplayLength = 9;
      if (usbName.length > usbNameDisplayLength) {
        usbName = usbName.substring(0, usbNameDisplayLength);
      }
      usbInfoList.add(
          USBInfo(usbName: usbName, usbSpace: "${freeSpace}MB", usbPath: path));
      return true;
    }
    return false;
  }

  /// 現在のUSB情報リスト
  final List<USBInfo> _usbInfoList = [];

  /// 登録されたUSB情報リスナー
  final List<UsbInfoChangeListener> _usbInfoChangedListeners = [];

  ///
  /// このメソッドは、USB デバイスの接続または取り外し時に現在の USB 情報を更新するため。
  /// このメソッドは Android 層によって呼び出されます。
  ///
  Future<void> onUsbDeviceChanged() async {
    final usbInfoList = await _requestCurrentUsbInfo();
    if (usbInfoList != null) {
      _usbInfoList.clear();
      _usbInfoList.addAll(usbInfoList);
      _notifyListeners(usbInfoList);
    } else {
      // no-op
    }
  }

  ///
  /// 現在の USB 情報が変更されたことをすべての [UsbInfoChangeListener] に通知します。
  ///
  void _notifyListeners(List<USBInfo> usbInfoList) {
    for (var element in _usbInfoChangedListeners) {
      element.call(usbInfoList);
    }
  }

  ///
  /// 現在の USB 情報を取得します。 Android レイヤーに結果を返すようにリクエストします。
  /// 戻り値が null の場合は、取得が失敗したことを意味します。
  ///
  Future<List<USBInfo>?> _requestCurrentUsbInfo() async {
    try {
      final list =
          await _usbMonitorChannel.invokeListMethod<dynamic>("getUsbInfoList");
      if (list == null) return null;

      final List<USBInfo> usbInfoList = [];
      for (var element in list) {
        final map = element as Map<dynamic, dynamic>;
        usbInfoList.add(USBInfo(
            usbName: map["usbLabel"],
            usbSpace: "${map["usbFreeSpace"]}MB",
            usbPath: map["usbPath"]));
      }
      return usbInfoList;
    } catch (e) {
      _logcat("Failed to request current usb info: $e");
      return null;
    }
  }

  ///
  /// USB情報変更リスナーの登録，登録時に [listener] が1回呼び出されます。
  ///
  /// - return: USB情報監視解除機能
  ///
  Function registerUsbChangeListener(UsbInfoChangeListener listener) {
    if (isHiddenApiSupported) {
      _usbInfoChangedListeners.add(listener);
      listener.call(_usbInfoList);
      return () => _usbInfoChangedListeners.remove(listener);
    } else {
      return _registerUsbChangeListenerLegacy(listener).cancel;
    }
  }

  ///
  /// 現在のフレームワークはマウスの切断を自動的に検出できないため、マウスの切断イベントを
  /// 積極的に監視し、[PointerRemovedEvent]を[MouseTracker]に手動で送信して、
  /// マウスの切断を通知します。
  ///
  /// これにより、https://brothergroup.atlassian.net/browse/PHFIRMIIVO-7946
  /// で指摘された問題が修正されます。
  ///
  /// これはFlutter(v3.29.2)フレームワークの既知の未解決の問題です:
  /// https://github.com/flutter/flutter/issues/111834
  ///
  /// @see [MouseConnectionManager]
  ///
  void _workaroundFlutterMouseRemoveIssue() {
    MouseConnectionManager().registerConnectionChangeListener((connected) {
      if (connected == false) {
        WidgetsFlutterBinding.ensureInitialized();
        RendererBinding.instance.mouseTracker.updateWithEvent(
            const PointerRemovedEvent(kind: PointerDeviceKind.mouse), null);
      } else {
        // マウスが接続されたとき、何もしません
      }
    });
  }

  ///
  /// UPFアップデート時にUPFファイルの署名を検証する
  ///
  Future<bool> verifyUPFIntegrity(String zipFilePath, String password,
      Uint8List publicKeyPemDataList) async {
    try {
      _logcat("verifyUPFIntegrity");
      return UsbManagerPlatform.instance
          .verifyUPFIntegrity(zipFilePath, password, publicKeyPemDataList);
    } catch (e) {
      return false;
    }
  }

  ///
  /// 指定のフォルダリストを圧縮し、任意のディレクトリにZIPファイルとして保存する。
  /// 圧縮時にAES256位暗号化を利用し、パスワードを付与する。
  /// @param sourceFolderPathList 圧縮対象のフォルダパスリスト
  /// @param outputZipFilePath    出力先のZIPファイルパス（任意のディレクトリ）
  /// @param password             圧縮ファイルのパスワード
  /// @return                     圧縮に成功したか否か
  ///
  Future<bool> compressFolder(List<String> sourceFolderPathList,
      String outputZipFilePath, String password) async {
    try {
      return UsbManagerPlatform.instance
          .compressFolder(sourceFolderPathList, outputZipFilePath, password);
    } catch (e) {
      return false;
    }
  }
}

typedef UsbInfoChangeListener = void Function(List<USBInfo> usbInfoList);

///
/// 印刷情報レベルログ
///
void _logcat(String msg) {
  Log.i(tag: "UsbManagerDart", description: msg);
}
