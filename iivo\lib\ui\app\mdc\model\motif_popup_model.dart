import 'dart:io' show IOException;

import 'package:flutter/services.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:path/path.dart';

import '../../../../../memory/memory.dart';
import 'memory_model.dart';

enum MotifPatternType { buildIn, custom }

class MotifPopupModel {
  MotifPopupModel._internal();

  factory MotifPopupModel() => _instance;
  static final MotifPopupModel _instance = MotifPopupModel._internal();

  ///
  /// モチーフのサムネイル幅/高
  ///
  static const double motifThumbnailWidth = 152;
  static const double motifThumbnailHeight = 52;

  ///
  ///  モチーフビルドインファイル数
  ///
  static int get motifBuildFileNum =>
      MdcLibrary().apiBinding.getMdcBuiltInLineMotifTotalNum().totalNum;

  ///
  /// 開始ビルドインモチーフ番号
  ///
  static const int startMotifBuildInNo = 1;
  static const int invalidMotifNo = 0;

  ///
  /// 開始モチーフカスタム番号
  ///
  static int get startMotifCustomNo => motifBuildFileNum + 1;

  ///
  /// 選択したのモチーフカスタム番号
  /// 値は１～
  ///
  int currentSelectedMotifNo = startMotifBuildInNo;

  ///
  /// 使用中のユーザー作成模様番号情報
  ///
  int usingMotifNum = 0;
  List<int> usingMotifList = [];

  ///
  /// 選択されたモチーフタイプ
  ///
  static MotifPatternType getSelectedMotifPatternType(
      int currentSelectedMotifNo) {
    if (currentSelectedMotifNo > motifBuildFileNum) {
      return MotifPatternType.custom;
    }
    return MotifPatternType.buildIn;
  }

  ///
  /// モチーフカスタムパターンフルチェック
  ///
  static bool checkMotifCustomPatternFull() {
    try {
      for (int i = 0; i < _CustomFileInfoList.maxFileCount; i++) {
        final path = _CustomFileInfoList().getFilePathByIndex(i);
        final FileEntity motifImportFile = FileEntity(path);
        if (!motifImportFile.existsSync()) {
          return false;
        }
      }
    } on Exception {
      Log.e(
          tag: memoryAccessErrorLogTag,
          description: "check motif custom pattern full failed");
      return false;
    }

    return true;
  }

  ///
  /// モチーフビルドインPMFデータリスト
  ///
  final List<Uint8List> motifBuildInThumbnailDataList = [];

  ///
  /// モチーフカスタムパターンを追加
  ///
  static Future<AccessError> addMotifCustomPattern(FileEntity pmfFile) async {
    AccessError accessError = AccessError.none;
    try {
      if (MdcLibrary().apiBinding.isMdcPmfFileValid(pmfFile.path).result ==
          false) {
        return AccessError.other;
      }

      for (int i = 0; i < _CustomFileInfoList.maxFileCount; i++) {
        final path = _CustomFileInfoList().getFilePathByIndex(i);
        final FileEntity motifImportFile = FileEntity(path);
        if (motifImportFile.existsSync()) {
          continue;
        } else {
          pmfFile.copySync(motifImportFile.path);
          break;
        }
      }
    } on IOException catch (e) {
      Log.e(
          tag: memoryAccessErrorLogTag,
          description: "add motif custom pattern failed");
      accessError = memoryExceptionToAccessError(e);
    }

    return accessError;
  }

  ///
  /// モチーフカスタムパターン合計数を取得
  ///
  static int getMotifCustomPatternTotalNum() {
    int totalNum = 0;
    try {
      for (int i = 0; i < _CustomFileInfoList.maxFileCount; i++) {
        final path = _CustomFileInfoList().getFilePathByIndex(i);
        final FileEntity motifImportFile = FileEntity(path);
        if (motifImportFile.existsSync()) {
          totalNum++;
        } else {
          break;
        }
      }
    } on Exception {
      Log.e(
          tag: memoryAccessErrorLogTag,
          description: "get motif custom pattern total num failed");
    }
    return totalNum;
  }

  ///
  /// カスタムパターンの使用チェック
  ///
  bool checkCustomPatternUse(int motifNo) {
    for (int i = 0; i < usingMotifNum; i++) {
      if (usingMotifList[i] == motifNo) {
        return true;
      }
    }
    return false;
  }

  ///
  /// モチーフカスタムサムネイルリストを読み込む
  ///
  List<Uint8List> loadMotifCustomThumbnailDataList() {
    setMdcImportFileList();

    List<Uint8List> motifCustomThumbnailDataList = [];
    for (int i = 0; i < _CustomFileInfoList.maxFileCount; i++) {
      final path = _CustomFileInfoList().getFilePathByIndex(i);
      final FileEntity motifImportFile = FileEntity(path);

      if (motifImportFile.existsSync()) {
        Uint8List imageData = MdcLibrary()
            .apiBinding
            .getMdcCustomLineMotifThumbnail(
              motifImportFile.path,
              motifThumbnailWidth.toInt(),
              motifThumbnailHeight.toInt(),
              const Color(_lineColor),
            )
            .imageInfo
            .imageData;
        motifCustomThumbnailDataList.add(imageData);
        MdcLibrary().apiBinding.delMdcThumbImage();
      } else {
        break;
      }
    }

    return motifCustomThumbnailDataList;
  }

  ///
  /// モチーフビルドインサムネイルリストを読み込む
  ///
  List<Uint8List> loadMotifBuildInThumbnailDataList() {
    List<Uint8List> motifBuildInThumbnailDataList = [];
    for (int i = 1; i <= motifBuildFileNum; i++) {
      Uint8List imageData = MdcLibrary()
          .apiBinding
          .getMdcBuiltInLineMotifThumbnail(
            i,
            motifThumbnailWidth.toInt(),
            motifThumbnailHeight.toInt(),
            const Color(_lineColor),
          )
          .imageInfo
          .imageData;
      motifBuildInThumbnailDataList.add(imageData);
    }

    return motifBuildInThumbnailDataList;
  }

  ///
  /// 選択したサムネイルを取得する
  ///
  static Uint8List getSelectedPatternThumbnailData(
      bool isCustom, int selectedMotifNo) {
    if (isCustom == false) {
      var imageData = MdcLibrary()
          .apiBinding
          .getMdcBuiltInLineMotifThumbnail(
            selectedMotifNo,
            motifThumbnailWidth.toInt(),
            motifThumbnailHeight.toInt(),
            const Color(_lineColor),
          )
          .imageInfo
          .imageData;
      return imageData;
    } else {
      final int index = selectedMotifNo - 1;
      final String path = _CustomFileInfoList().getFilePathByIndex(index);

      var imageData = MdcLibrary()
          .apiBinding
          .getMdcCustomLineMotifThumbnail(
            path,
            motifThumbnailWidth.toInt(),
            motifThumbnailHeight.toInt(),
            const Color(_lineColor),
          )
          .imageInfo
          .imageData;
      MdcLibrary().apiBinding.delMdcThumbImage();
      return imageData;
    }
  }

  ///
  /// 使用中のユーザー作成模様番号情報を取得する
  ///
  void getUsingDataInfo() {
    var info =
        MdcLibrary().apiBinding.isMdcUsingImportDataNumber(true).usingNumBuff;
    usingMotifNum = info.motifSetNum;
    usingMotifList = info.motifUsingData;
  }

  ///
  /// すべてのモチーフカスタムパターンを削除
  ///
  static bool deleteAllMotifCustomPattern() {
    try {
      for (int i = 0; i < _CustomFileInfoList.maxFileCount; i++) {
        final String path = _CustomFileInfoList().getFilePathByIndex(i);
        final FileEntity motifImportFile = FileEntity(path);
        if (motifImportFile.existsSync()) {
          motifImportFile.deleteSync();
        } else {
          break;
        }
      }
    } on Exception {
      Log.e(
          tag: memoryAccessErrorLogTag,
          description: "delete all motif custom pattern failed");
      return false;
    }

    return true;
  }

  ///
  /// モチーフ画像データを読み取る
  ///
  static Uint8List readSelectedMotifThumbnailData(int selectedMotifNo) {
    Uint8List selectedMotifThumbnailData = Uint8List.fromList([]);

    if (selectedMotifNo > motifBuildFileNum) {
      final index = selectedMotifNo - startMotifBuildInNo - motifBuildFileNum;
      final String path = _CustomFileInfoList().getFilePathByIndex(index);

      var thumbnailData = MdcLibrary()
          .apiBinding
          .getMdcCustomLineMotifThumbnail(
            path,
            motifThumbnailWidth.toInt(),
            motifThumbnailHeight.toInt(),
            const Color(_lineColor),
          )
          .imageInfo
          .imageData;
      selectedMotifThumbnailData = thumbnailData;
      MdcLibrary().apiBinding.delMdcThumbImage();
    } else {
      var thumbnailData = MdcLibrary()
          .apiBinding
          .getMdcBuiltInLineMotifThumbnail(
            selectedMotifNo,
            motifThumbnailWidth.toInt(),
            motifThumbnailHeight.toInt(),
            const Color(_lineColor),
          )
          .imageInfo
          .imageData;
      selectedMotifThumbnailData = thumbnailData;
    }
    return selectedMotifThumbnailData;
  }

  ///
  /// モチーフカスタムパターンの置換
  /// - [customPatternIndex] : 切り替え番号
  /// - [newMotifFile] : 切り替えMotifFile
  ///
  static AccessError replaceMotifCustomPattern(
    int customPatternIndex,
    FileEntity newMotifFile,
  ) {
    AccessError accessError = AccessError.none;

    try {
      if (MdcLibrary().apiBinding.isMdcPmfFileValid(newMotifFile.path).result ==
          false) {
        return AccessError.other;
      }
      newMotifFile.copySync(join(
        memorySector.mdcImp.absolutePath,
        _CustomFileInfoList().getFileNameByIndex(customPatternIndex),
      ));
    } on IOException catch (e) {
      accessError = memoryExceptionToAccessError(e);
    }

    return accessError;
  }

  ///
  /// インポートしたユーザー作成模様(線モチーフ)の格納場所、件数をセットする
  ///
  void setMdcImportFileList() {
    MdcImportFileList fileListInfo = MemoryModel().fileListInfo;
    List<String> fileList = [];

    for (int i = 0; i < _CustomFileInfoList.maxFileCount; i++) {
      final String path = _CustomFileInfoList().getFilePathByIndex(i);
      final FileEntity motifImportFile = FileEntity(path);
      if (motifImportFile.existsSync()) {
        fileList.add(motifImportFile.path);
      }
    }

    fileListInfo = fileListInfo.copyWith(
      motifImportNum: fileList.length,
      pmfFileList: fileList,
    );

    /// model更新
    MemoryModel().fileListInfo = fileListInfo;

    MdcLibrary().apiBinding.setMdcImportFileList(fileListInfo);
  }

  ///
  /// 指定IndexのフラグをTrueになります
  ///
  void markFileChangedByIndex(int index) =>
      _CustomFileInfoList().markFileChangedByIndex(index);

  ///
  /// 使用中のカスタム模様ファイルは差し替えられましたか
  ///
  bool hasAnyFileChanged() => _CustomFileInfoList().hasAnyFileChanged();

  ///
  /// 全部フラリセット
  ///
  void clearFileChangedMark() => _CustomFileInfoList().clearFileChangedMark();

  ///
  /// 線の色
  ///
  static const int _lineColor = 0x00102070;
}

///
/// ユーザー作成ファイル情報
///
class _CustomFileInfoList {
  _CustomFileInfoList._internal();

  factory _CustomFileInfoList() => _instance;
  static final _CustomFileInfoList _instance = _CustomFileInfoList._internal();

  ///
  ///  モチーフカスタムインポートファイル名リスト
  ///
  static final List<Map<String, String>> _fileInfoList = [
    {'fileName': 'Import_Motif_001.pmf', 'isChanged': 'false'},
    {'fileName': 'Import_Motif_002.pmf', 'isChanged': 'false'},
    {'fileName': 'Import_Motif_003.pmf', 'isChanged': 'false'},
    {'fileName': 'Import_Motif_004.pmf', 'isChanged': 'false'},
    {'fileName': 'Import_Motif_005.pmf', 'isChanged': 'false'},
    {'fileName': 'Import_Motif_006.pmf', 'isChanged': 'false'},
    {'fileName': 'Import_Motif_007.pmf', 'isChanged': 'false'},
    {'fileName': 'Import_Motif_008.pmf', 'isChanged': 'false'},
    {'fileName': 'Import_Motif_009.pmf', 'isChanged': 'false'},
    {'fileName': 'Import_Motif_010.pmf', 'isChanged': 'false'},
    {'fileName': 'Import_Motif_011.pmf', 'isChanged': 'false'},
    {'fileName': 'Import_Motif_012.pmf', 'isChanged': 'false'},
  ];

  ///
  /// 最大12個
  ///
  static const int maxFileCount = 12;

  ///
  /// 差し替えられる模様は使用中かどうか
  ///
  bool hasAnyFileChanged() {
    for (int i = 0; i < maxFileCount; i++) {
      final String changedString = _fileInfoList[i]['isChanged'] ?? 'false';
      final isChanged = bool.tryParse(changedString) ?? false;
      if (isChanged == false) {
        continue;
      }

      final int decoNo = i + 1;
      final bool patternUsing = MotifPopupModel().checkCustomPatternUse(decoNo);
      if (patternUsing) {
        return true;
      }
    }

    return false;
  }

  ///
  /// 指定IndexのフラグをTrueになります
  ///
  void markFileChangedByIndex(int index) {
    if (index < 0 || index > maxFileCount) {
      Log.assertTrace('Unknown index');
      return;
    }
    _fileInfoList[index]['isChanged'] = 'true';
  }

  ///
  /// 全部フラグをfalseになります
  ///
  void clearFileChangedMark() {
    for (int i = 0; i < maxFileCount; i++) {
      _fileInfoList[i]['isChanged'] = 'false';
    }
  }

  ///
  /// 指定Indexのファイルのパスを取得する
  ///
  String getFilePathByIndex(int index) {
    if (index < 0 || index > maxFileCount) {
      Log.assertTrace('Unknown file');
      return '';
    }

    final String? fileName = _fileInfoList[index]['fileName'];
    if (fileName == null) {
      Log.assertTrace('Unknown file');
      return '';
    }

    return join(memorySector.mdcImp.absolutePath, fileName);
  }

  ///
  /// 指定Indexのファイル名を取得する
  ///
  String getFileNameByIndex(int index) {
    if (index < 0 || index > maxFileCount) {
      Log.assertTrace('Unknown file');
      return '';
    }

    final String? fileName = _fileInfoList[index]['fileName'];
    if (fileName == null) {
      Log.assertTrace('Unknown file');
      return '';
    }

    return fileName;
  }
}
