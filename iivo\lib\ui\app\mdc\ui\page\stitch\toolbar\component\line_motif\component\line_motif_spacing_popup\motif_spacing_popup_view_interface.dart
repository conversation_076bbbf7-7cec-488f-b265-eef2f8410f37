import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'motif_spacing_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class MotifSpacingPopupState with _$MotifSpacingPopupState {
  const factory MotifSpacingPopupState({
    required bool isSpacingMinusToLimit,
    required bool isSpacingPlusToLimit,
    required String spacingInputValue,
    required bool isDefaultStyle,
  }) = _MotifSpacingPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class MotifSpacingPopupStateViewInterface
    extends ViewModel<MotifSpacingPopupState> {
  MotifSpacingPopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// チェーンステッチのデフォルト値
  ///
  int get defaultValue;

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;

  ///
  /// Spacing増大操作
  ///
  bool plusSpacing(bool isLongPress);

  ///
  /// Spacing縮小操作
  ///
  bool miniSpacing(bool isLongPress);
}
