import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../model/key_board_font_model.dart';
import '../../../../model/pattern_data_reader/character_font_image_reader.dart';
import '../../../../model/pattern_data_reader/character_font_select_reader.dart';
import '../../../../model/select_model.dart';
import '../../../page_route.dart';
import 'character_font_popup/character_popup.dart';
import 'character_font_view_interface.dart';

final characterFontSelectorViewModelProvider = StateNotifierProvider
    .autoDispose<CharacterFontViewModelInterface, CharacterFontSelectorState>(
        (ref) => CharacterFontSelectorViewModel(ref));

class CharacterFontSelectorViewModel extends CharacterFontViewModelInterface {
  CharacterFontSelectorViewModel(Ref ref)
      : super(const CharacterFontSelectorState(), ref);

  ///
  /// 全てのカテゴリのイコンデータを取得する
  ///
  /// ##@return
  /// - List<CategoryImageGroup>: 順番保存されているの模様イコンデータ
  ///
  @override
  List<CharacterFontImageGroup> getAllCharacterFontImagesInfo() =>
      CharacterFontImageReader().getAllCharacterFontImagesInfo();

  ///
  /// ExclusiveScriptフォントの判断
  ///
  @override
  bool isExclusiveScriptType() =>
      CharacterFontImageReader()
          .getAllCharacterFontImagesInfo()
          .first
          .fontNumber ==
      KeyBoardFontModel.exclusiveScriptFontNumber;

  ///
  /// フォントの数を取得します
  ///
  @override
  int characterFontImagesLength() =>
      CharacterFontImageReader().getAllCharacterFontImagesInfo().length;

  ///
  /// フォントの名前を取得します
  ///
  @override
  String getCharacterFontName(int index) {
    if (isExclusiveScriptType()) {
      return getAllCharacterFontImagesInfo()[index + 1].name;
    } else {
      return getAllCharacterFontImagesInfo()[index].name;
    }
  }

  ///
  /// フォントの画像を取得します
  ///
  @override
  Widget getCharacterFontImage(int index) {
    if (isExclusiveScriptType()) {
      return getAllCharacterFontImagesInfo()[index + 1].image;
    }
    if (index == getExclusiveScriptFontIndex) {
      return Container();
    }
    return getAllCharacterFontImagesInfo()[index].image;
  }

  ///
  /// Returnのクリック関数
  ///
  @override
  void onReturnButtonClicked(BuildContext context) {
    scrollController = ScrollController();

    /// 自分を閉じる
    PopupNavigator.pop(context: context);

    /// Model更新
    SelectModel()
      ..selectedCategoryType = null
      ..selectedPatternIndex = null;
  }

  ///
  /// タイプ画像クリックイベント
  ///
  @override
  void onItemClicked(BuildContext context, int index, bool checkboxSelected) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    int outIndex = index;
    if (isExclusiveScriptType()) {
      outIndex = index + 1;
    }
    KeyBoardFontModel().reset();
    if (checkboxSelected) {
      PopupNavigator.push(
          context: context,
          popup: PopupRouteBuilder(
            builder: (context) => CharacterPopup(
              outIndex: outIndex,
              categoryName: getCharacterFontName(index),
            ),
            barrier: false,
          ));
    } else {
      String groupName = getCharacterFontName(index);
      CharacterFontSelectReader().updateCharacterFontSelectGroup(groupName);
      KeyBoardFontModel().fontName = groupName;
      KeyBoardFontModel().startEmbCharEdit(false);
      PagesRoute().pushNamed(nextRoute: PageRouteEnum.characterFontSelect);

      if (isExclusiveScriptType()) {
        KeyBoardFontModel().fontNumber =
            getAllCharacterFontImagesInfo()[index + 1].fontNumber;
        KeyBoardFontModel().selectedFontType =
            getAllCharacterFontImagesInfo()[index + 1].type;

        if (getAllCharacterFontImagesInfo()[index + 1].type ==
            KeyBoardFontModel.emcTypeAlp) {
          KeyBoardFontModel().displayFontSize =
              KeyBoardFontModel.defaultFontSizeAlp;
        } else if (getAllCharacterFontImagesInfo()[index + 1].type ==
            KeyBoardFontModel.emcTypeSmall) {
          KeyBoardFontModel().displayFontSize =
              KeyBoardFontModel.defaultFontSizeSmall;
        }
      } else {
        KeyBoardFontModel().fontNumber =
            getAllCharacterFontImagesInfo()[index].fontNumber;
        KeyBoardFontModel().selectedFontType =
            getAllCharacterFontImagesInfo()[index].type;

        if (getAllCharacterFontImagesInfo()[index].type ==
            KeyBoardFontModel.emcTypeAlp) {
          KeyBoardFontModel().displayFontSize =
              KeyBoardFontModel.defaultFontSizeAlp;
        } else if (getAllCharacterFontImagesInfo()[index].type ==
            KeyBoardFontModel.emcTypeSmall) {
          KeyBoardFontModel().displayFontSize =
              KeyBoardFontModel.defaultFontSizeSmall;
        }
      }
    }
  }

  final int _exclusiveScriptIndex = 0;

  @override
  void onScriptButtonClicked(BuildContext context, bool checkboxSelected) {
    KeyBoardFontModel().reset();
    if (checkboxSelected) {
      PopupNavigator.push(
          context: context,
          popup: PopupRouteBuilder(
            builder: (context) => CharacterPopup(
              outIndex: _exclusiveScriptIndex,
              categoryName:
                  getAllCharacterFontImagesInfo()[_exclusiveScriptIndex].name,
            ),
            barrier: false,
          ));
    } else {
      String groupName =
          getAllCharacterFontImagesInfo()[_exclusiveScriptIndex].name;
      CharacterFontSelectReader().updateCharacterFontSelectGroup(groupName);
      KeyBoardFontModel().fontName = groupName;
      KeyBoardFontModel().startEmbCharEdit(false);
      PagesRoute().pushNamed(nextRoute: PageRouteEnum.characterFontSelect);

      KeyBoardFontModel().fontNumber =
          getAllCharacterFontImagesInfo()[0].fontNumber;
      KeyBoardFontModel().selectedFontType =
          getAllCharacterFontImagesInfo()[0].type;
      KeyBoardFontModel().displayFontSize =
          KeyBoardFontModel.defaultFontSizeAlp;
    }
  }

  @override
  bool isCharacterFontName(index) => getCharacterFontName(index) == 'Space';

  @override
  int get getExclusiveScriptFontIndex =>
      KeyBoardFontModel.exclusiveScriptFontIndex;
}
