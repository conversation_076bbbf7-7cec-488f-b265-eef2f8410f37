import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/l10n/app_localizations.dart';

import '../network/server/http/route/artspira/common.dart';

final appLocale = ChangeNotifierProvider<AppLocale>(
  (ref) => AppLocale(),
);

class AppLocale with ChangeNotifier {
  factory AppLocale() => _instance;
  AppLocale._internal();
  static final AppLocale _instance = AppLocale._internal();
  Locale _locale = const Locale('en');
  bool get isEnglish => _locale == const Locale('en');
  bool get isFrench => _locale == const Locale('fr');

  static const Locale _english = Locale('en');
  static const Locale _german = Locale('de');
  static const Locale _french = Locale('fr');
  static const Locale _italian = Locale('it');
  static const Locale _dutch = Locale('nl');
  static const Locale _spanish = Locale('es');
  static const Locale _japanese = Locale('ja');

  Locale get value => _locale;
  Locale getCurrentLocale() => _locale;

  AppLocalizations? get l10n => _l10n;
  AppLocalizations? _l10n;

  void initLocale(Language value) {
    _locale = getLocalFromLangue(value);
    _l10n = lookupAppLocalizations(_locale);
    setLocale(_locale);
  }

  void setLocale(Locale currentLocale) {
    _locale = currentLocale;
    _l10n = lookupAppLocalizations(_locale);
    notifyListeners();

    /// artspira header "Accept-Language"　設定
    ArtspiraRequestCommonHeader()
        .setReqHeaderAcceptLanguage(getPdfLanguageToolkit());
  }

  ///
  /// 現地の言語の一覧を取得する
  ///
  Locale getLocalFromLangue(Language value) => switch (value) {
        Language.LANG_ENGLISH => _english,
        Language.LANG_GERMAN => _german,
        Language.LANG_FRENCH => _french,
        Language.LANG_ITALIAN => _italian,
        Language.LANG_DUTCH => _dutch,
        Language.LANG_SPANISH => _spanish,
        Language.LANG_JAPAN => _japanese,
      };

  ///
  /// この関数は、PDF ファイルの言語関連の機能をサポートするための専用ツールキットを取得します
  ///
  String getPdfLanguageToolkit() => switch (_locale.languageCode) {
        'en' => "en-US",
        'de' => "de",
        'fr' => "fr",
        'it' => "it",
        'nl' => "nl",
        'es' => "es-ES",
        'ja' => "ja",
        _ => "en-US",
      };

  ///
  /// 言語選択リスト
  ///
  List<Language> getSystemLanguageList = [
    Language.LANG_ENGLISH,
    Language.LANG_GERMAN,
    Language.LANG_FRENCH,
    Language.LANG_ITALIAN,
    Language.LANG_DUTCH,
    Language.LANG_SPANISH,
    Language.LANG_JAPAN,
  ];
}
