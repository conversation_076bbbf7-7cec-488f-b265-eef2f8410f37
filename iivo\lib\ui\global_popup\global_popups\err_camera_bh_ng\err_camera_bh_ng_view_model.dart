import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_camera_bh_ng_view_interface.dart';

final errCameraBhNgViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrCameraBhNgViewInterface, ErrCameraBhNgState, BuildContext>(
        (ref, context) => ErrCameraBhNgViewModel(ref, context));

class ErrCameraBhNgViewModel extends ErrCameraBhNgViewInterface {
  ErrCameraBhNgViewModel(Ref ref, BuildContext context)
      : super(const ErrCameraBhNgState(), ref, context);

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    final int errCode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERRBHRECG);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }
}
