import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../model/pattern_data_reader/character_font_image_reader.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'character_font_view_interface.freezed.dart';

@freezed
class CharacterFontSelectorState with _$CharacterFontSelectorState {
  const factory CharacterFontSelectorState() = _CharacterFontSelectorState;
}

abstract class CharacterFontViewModelInterface
    extends ViewModel<CharacterFontSelectorState> {
  CharacterFontViewModelInterface(super.state, this.ref);

  Ref ref;

  ScrollController scrollController = ScrollController();

  ///
  /// フォント画像を取得する
  ///
  Widget getCharacterFontImage(int index);

  ///
  /// 戻るボタンがクリックされた
  ///
  void onReturnButtonClicked(BuildContext context);

  ///
  /// Script画像クリックイベント
  ///
  void onScriptButtonClicked(BuildContext context, bool checkboxSelected);

  ///
  /// 全てのカテゴリのイコンデータを取得する
  ///
  /// ##@return
  /// - List<CategoryImageGroup>: 順番保存されているの模様イコンデータ
  ///
  List<CharacterFontImageGroup> getAllCharacterFontImagesInfo();

  ///
  /// タイプ画像クリックイベント
  ///
  void onItemClicked(BuildContext context, int index, bool checkboxSelected);

  ///
  /// ExclusiveScriptフォントの判断
  ///
  bool isExclusiveScriptType();

  ///
  /// フォントの数を取得します
  ///
  int characterFontImagesLength();

  ///
  /// フォントの名前を取得します
  ///
  String getCharacterFontName(int index);

  ///
  /// 正しいフォント名が取得されているかどうか
  ///
  bool isCharacterFontName(index);

  ///
  /// Exclusive Script フォントインデックス
  ///
  int get getExclusiveScriptFontIndex;
}
