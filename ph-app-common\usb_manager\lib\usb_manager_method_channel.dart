import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'usb_manager_platform_interface.dart';

/// An implementation of [UsbManagerPlatform] that uses method channels.
class MethodChannelUsbManager extends UsbManagerPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('usb_manager');

  @override
  Future<bool?> usbManagerInit() async =>
      await methodChannel.invokeMethod("usbManagerInit");

  @override
  Future<String?> getDeviceList() async {
    final version = await methodChannel.invokeMethod<String>('getDeviceList');
    return version;
  }

  @override
  Future<void> receiveMessage(Function refresh) async {
    methodChannel.setMethodCallHandler((call) async {
      if (call.method == 'refresh') {
        refresh;
      }
    });
  }

  @override
  Future<String?> readJsonFromZip(String zipFilePath, String fileName,
      {String? password}) async {
    final version = await methodChannel.invokeMethod<String>(
        'readJsonFromZip', {
      "zipFilePath": zipFilePath,
      "fileName": fileName,
      "password": password
    });
    return version;
  }

  @override
  Future<bool?> unZip(String zipFilePath, String dirPath,
      {String? password}) async {
    final version = await methodChannel.invokeMethod<bool>('unZip',
        {"zipFilePath": zipFilePath, "dirPath": dirPath, "password": password});
    return version;
  }

  @override
  Future<int> getUsbFreeSpace(String path) async =>
      await methodChannel.invokeMethod("getUsbFreeSpace", {"path": path});

  @override
  Future<bool?> extractDirFromUpf(
          {required String upfFilepath,
          required String outputDirPath,
          required String dirName,
          String? password}) async =>
      await methodChannel.invokeMethod("extractDirFromUpf", {
        "outputDirPath": outputDirPath,
        "upfFilepath": upfFilepath,
        "dirName": dirName,
        "password": password
      });

  @override
  Future<bool?> extractUpfHeaderFromUpf(
          {required String upfFilepath,
          required String outputDirPath,
          String? password}) async =>
      await methodChannel.invokeMethod("extractUpfHeaderFromUpf", {
        "outputDirPath": outputDirPath,
        "upfFilepath": upfFilepath,
        "password": password
      });

  @override
  Future<String?> calculateSHA256(String filePath) async {
    final version = await methodChannel
        .invokeMethod<String>('calculateSHA256', {"filePath": filePath});
    return version;
  }

  @override
  Future<bool> verifyUPFIntegrity(String zipFilePath, String password,
      Uint8List publicKeyPemDataList) async {
    final bool? isVerified =
        await methodChannel.invokeMethod<bool>("verifyUPFIntegrity", {
      "zipFilePath": zipFilePath,
      "password": password,
      "publicKeyPemDataList": publicKeyPemDataList
    });
    return isVerified ?? false;
  }

  @override
  Future<bool> compressFolder(List<String> sourceFolderPathList,
      String outputZipFilePath, String password) async {
    final bool? isSuccess =
        await methodChannel.invokeMethod<bool>("compressFolder", {
      "sourceFolderPathList": sourceFolderPathList,
      "outputZipFilePath": outputZipFilePath,
      "password": password
    });
    return isSuccess ?? false;
  }
}
