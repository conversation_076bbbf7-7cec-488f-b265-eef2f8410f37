import 'dart:math';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../model/preview_area_size_model.dart';
import '../../../../model/thread_color_model.dart';
import 'pattern_model.dart';
import 'preview_model.dart';
import 'thread_model.dart';

///
/// 線色リスト の item、画像表示領域の高さと幅
///
const int threadItemImageAreaWidth = 186;
const int threadItemImageAreaHeight = 126;

///
/// パターンのオリジナルサイズ比率
///
const double originalSizeRatio = 1;

///
/// 刺しゅうパターン編集画面に関する情報
///
class ThreadColorListModel {
  ThreadColorListModel._internal();
  factory ThreadColorListModel() {
    if (_instance._isInitialized == false) {
      _instance._isInitialized = true;
    }
    return _instance;
  }

  bool _isInitialized = false;
  static final ThreadColorListModel _instance =
      ThreadColorListModel._internal();

  ///
  /// 現在の線色リストで選択されている画像
  ///
  int? selectedGroupIndex;

  ///
  /// 縫製の線色がないか判断する
  /// true: 縫う必要のない線色  false:縫製が必要な線色があります
  ///
  bool isNoThreadToSewing() {
    List<EmbGroup> groupList = PatternModel().getAllGroup();

    for (var group in groupList) {
      bool hasThreadToSewing =
          group.threadInfo.any((element) => element.notSewing == false);

      /// 糸の色は縫う必要があります
      if (hasThreadToSewing == true) {
        return false;
      }
    }

    /// パターンなしで縫えばfalseに戻る
    return true;
  }

  ///
  /// 色変更ページの線色情報取得
  /// libの情報をmodel用の線色情報に変換する
  ///
  List<ColorListDisplayInfo> getDisplayList({
    required int? selectedPatternIndex,
    required int zoomScale,
  }) {
    List<EmbGroup> groupList = PatternModel().getAllGroup();
    List<ColorListDisplayInfo> displayList = [];

    for (int index = 0; index < groupList.length; index++) {
      bool isPatternSelected =
          selectedPatternIndex != null && selectedPatternIndex == index
              ? true
              : false;

      List<ThreadColorDispInfo> displayThreadInfoList =
          _getThreadInfoDisplayList(
        threadInfoList: groupList[index].threadInfo,
      );

      /// 線の色リストのサムネイル画像
      final Widget patternImage = getEmbGroupThreadImageList(
        groupList[index],
        ScrollCenterType.IMAGE_EDITING,
        zoomScale: zoomScale,
      );

      displayList.add(ColorListDisplayInfo(
          isSelectedPattern: isPatternSelected,
          patternImage: patternImage,
          threadInfoDisplayList: displayThreadInfoList));
    }

    return displayList;
  }

  ///
  /// 表示用の線色情報の取得
  ///
  List<ThreadColorDispInfo> _getThreadInfoDisplayList({
    required List<ThreadInfo> threadInfoList,
  }) {
    List<ThreadColorDispInfo> displayThreadInfoList = [];
    bool isThreadColorDefault = ThreadModel.getThreadColor();

    for (int index = 0; index < threadInfoList.length; index++) {
      final baseDisplayInfo = getThreadBaseDisplayInfo(threadInfoList[index]);

      displayThreadInfoList.add(ThreadColorDispInfo(
        isSelected: false,
        isThreadNotSewing: threadInfoList[index].notSewing,
        treadColor: baseDisplayInfo.treadColor,
        threadCode: baseDisplayInfo.threadCode,
        threadBrandName: baseDisplayInfo.threadBrandName,
        threadColorName: baseDisplayInfo.threadColorName,
        threadSewingTime: PatternModel()
            .changeLibSewingTimeToUiSewingTime(threadInfoList[index].sewingTime)
            .toString(),
        appliqueIcon: ThreadModel.getAppliqueIcon(
          threadInfoList[index].threadCode,
          false,
          isThreadColorDefault,
        ),
      ));
    }

    return displayThreadInfoList;
  }

  ({
    Color treadColor,
    String threadCode,
    String threadBrandName,
    String threadColorName,
  }) getThreadBaseDisplayInfo(ThreadInfo threadInfo) {
    bool isThreadColorDefault = ThreadModel.getThreadColor();
    return (
      treadColor: threadInfo.colorRGB,
      threadCode: ThreadModel.getThreadCode(threadInfo.threadCode,
          threadInfo.threadCodeDigit, isThreadColorDefault),
      threadBrandName: _getThreadBrandName(
          threadInfo.brandCode, isThreadColorDefault, threadInfo.threadCode),
      threadColorName: getThreadColorName(threadInfo.index300,
          threadInfo.threadCodeDigit, isThreadColorDefault),
    );
  }

  ///
  /// ブランド名の取得
  ///
  String _getThreadBrandName(
      int brandCode, bool isThreadColorDefault, int threadCode) {
    if (isThreadColorDefault == false ||
        ThreadModel.isAppliqueThreadCode(threadCode)) {
      return '';
    }

    ThreadBrandName threadBrandName =
        ThreadBrandName.getValuesByNumber(brandCode);

    return ThreadColorModel().getThreadBrandName(threadBrandName);
  }

  ///
  /// カラー名の取得
  ///
  String getThreadColorName(
      int index300, int threadCodeDigit, bool isThreadColorDefault) {
    if (isThreadColorDefault == true || threadCodeDigit == 0) {
      return '';
    }

    return ThreadColorModel().getThreadColorNameWithIndex300(index300);
  }

  ///
  /// EmbGroupの線の色リストの画像を取得する
  ///
  Widget getEmbGroupThreadImageList(
    EmbGroup pattern,
    int scrollType, {
    required int zoomScale,
    int? imageAreaWidth,
    int? imageAreaHeight,
  }) {
    if (imageAreaWidth == null || imageAreaHeight == null) {
      imageAreaWidth = threadItemImageAreaWidth;
      imageAreaHeight = threadItemImageAreaHeight;
    } else {
      /// do nothing
    }

    List<Uint8List> groupImageList = pattern.mainImage(scrollType, zoomScale);

    if (groupImageList.length > 1) {
      List<Offset> sizeList = [];
      List<Offset> startPointList = [];
      List<Uint8List> imageDataList = [];

      /// プレビュー領域に描画された画像の開始点とサイズを取得します
      var list = PreviewModel().getImageTopLeftAndSize(
        group: pattern,
        centerPoint: embPreviewSizeDot / 2,
        pixelOfOneMm: embPreviewSizeDot.dx / frame297x465MmSize.dx,
      );

      for (int i = 0; i < list.length; i++) {
        sizeList.add(list[i].size);
        startPointList.add(list[i].topLeft);
        imageDataList.add(groupImageList[i]);
      }

      return createItemImageFromPreviewData(
        imageAreaWidth: imageAreaWidth,
        imageAreaHeight: imageAreaHeight,
        imageDataList: imageDataList,
        sizeList: sizeList,
        startPointList: startPointList,
      );
    } else {
      return Image.memory(
        pattern.mainImage(scrollType, zoomScale).first,
      );
    }
  }

  ///
  /// 線の色リスト の item に表示される内容は、 Preview上に描画された情報によって得られます。
  ///
  Widget createItemImageFromPreviewData({
    required int imageAreaWidth,
    required int imageAreaHeight,
    required List<Uint8List> imageDataList,
    required List<Offset> sizeList,
    required List<Offset> startPointList,
  }) {
    double maskWidth = 0;
    double maskHeight = 0;
    Offset? minStartPoint;

    /// 最小開始点を探す。
    for (int index = 0; index < imageDataList.length; index++) {
      if (minStartPoint != null) {
        minStartPoint = Offset(
          min(minStartPoint.dx, startPointList[index].dx),
          min(minStartPoint.dy, startPointList[index].dy),
        );
      } else {
        minStartPoint =
            Offset(startPointList[index].dx, startPointList[index].dy);
      }

      maskWidth = max(maskWidth, startPointList[index].dx + sizeList[index].dx);
      maskHeight =
          max(maskHeight, startPointList[index].dy + sizeList[index].dy);
    }

    /// Preview でのスタイルに必要な描画領域を計算します
    maskWidth -= minStartPoint!.dx;
    maskHeight -= minStartPoint.dy;

    /// Order に描画領域を配置する item の拡大/縮小率を計算します。
    double widthRatio, heightRatio;
    if (maskWidth < imageAreaWidth && maskHeight < imageAreaHeight) {
      /// 表示領域よりも小さい場合、パターンのオリジナルサイズで表示します
      widthRatio = originalSizeRatio;
      heightRatio = originalSizeRatio;
    } else {
      widthRatio = imageAreaWidth / maskWidth;
      heightRatio = imageAreaHeight / maskHeight;
    }

    /// すべての模様を順番に縮小していきます。
    /// 画像の描画起点をオフセットして、画面上で中央に表示します。
    if (widthRatio < heightRatio) {
      maskHeight *= widthRatio;
      maskWidth *= widthRatio;
      final Offset offset = Offset(
        (imageAreaWidth - maskWidth) / 2,
        (imageAreaHeight - maskHeight) / 2,
      );

      for (int index = 0; index < imageDataList.length; index++) {
        startPointList[index] =
            (startPointList[index] - minStartPoint) * widthRatio + offset;
        sizeList[index] = sizeList[index] * widthRatio;
      }
    } else {
      maskWidth *= heightRatio;
      maskHeight *= heightRatio;
      final Offset offset = Offset(
        (imageAreaWidth - maskWidth) / 2,
        (imageAreaHeight - maskHeight) / 2,
      );

      for (int index = 0; index < imageDataList.length; index++) {
        startPointList[index] =
            (startPointList[index] - minStartPoint) * heightRatio + offset;
        sizeList[index] = sizeList[index] * heightRatio;
      }
    }

    /// レイアウトされたすべての画像を返します。
    return Stack(
      children: [
        ...() {
          List<Widget> widget = [];
          for (int index = 0; index < imageDataList.length; index++) {
            widget.add(
              Positioned(
                left: startPointList[index].dx,
                top: startPointList[index].dy,
                width: sizeList[index].dx,
                height: sizeList[index].dy,
                child: Image.memory(
                  imageDataList[index],
                  fit: BoxFit.fill,
                ),
              ),
            );
          }
          return widget;
        }(),
      ],
    );
  }

  ///
  /// modelのreset関数
  ///
  void reset() {
    selectedGroupIndex = null;
  }
}
