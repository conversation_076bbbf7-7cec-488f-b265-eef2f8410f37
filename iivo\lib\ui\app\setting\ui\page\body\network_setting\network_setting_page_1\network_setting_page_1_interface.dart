import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:xd_component/l10n/app_localizations.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'network_setting_page_1_interface.freezed.dart';

@freezed
class NetworkSettingPage1State with _$NetworkSettingPage1State {
  const factory NetworkSettingPage1State({
    @Default(false) bool wirelessLanEnable,
    @Default('SewingMachine') String machineName,
    @Default('') String connectedSSID,
    @Default(false) bool isShowLimitedConnectMessage,
    @Default(false) bool wirelessIsConnected,
  }) = _NetworkSettingPage1State;
}

abstract class NetworkSettingPage1Interface
    extends ViewModel<NetworkSettingPage1State> {
  NetworkSettingPage1Interface(super.state, this.ref);

  ///
  /// providerのref
  ///
  Ref ref;

  ///
  ///無線LAN有効ボタンをクリック
  ///
  void onWirelessLanEnableButtonClicked(bool value);

  ///
  /// 無線LAN接続ウィザードボタンをクリック
  ///
  void onWirelessLanSetupButtonClicked(BuildContext context);

  ///
  /// マシン名ボタンクリック
  ///
  void onMachineNameChangeButtonClicked(BuildContext context);

  void onWirelessLanSetupSoundButton();

  ///
  /// 無線LAN接続状態ボタンをクリック
  ///
  void onWirelessLanStatusButtonClicked(BuildContext context);

  ///
  /// ネットワーク設置リセットポップアップを開く/閉じる
  ///
  void openResetPopup(BuildContext context);

  ///
  ///【Others】ボタンのクリックイベント
  ///
  void onOthersButtonClicked(BuildContext context);

  ///
  /// SSIDを取得する
  ///
  String getSSID(AppLocalizations l10n);
}
