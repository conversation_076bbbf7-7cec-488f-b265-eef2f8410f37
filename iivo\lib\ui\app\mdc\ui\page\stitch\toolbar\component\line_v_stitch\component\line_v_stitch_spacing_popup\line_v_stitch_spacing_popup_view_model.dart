import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_v_stitch_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'line_v_stitch_spacing_popup_view_interface.dart';

/// 読み出したSizeデータの変換倍率 '読み出したデータの単位/10=mm'
const double _conversionRate = 10.0;

final lineVStitchSpaceViewModelProvider = StateNotifierProvider.autoDispose<
    LineVStitchSpaceStateViewInterface,
    LineVStitchSpaceState>((ref) => LineVStitchSpaceViewModel(ref));

class LineVStitchSpaceViewModel extends LineVStitchSpaceStateViewInterface {
  LineVStitchSpaceViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineVStitchSpaceState(
                spaceDisplayValue: "",
                isDefaultValue: false,
                plusButtonValid: false,
                minusButtonValid: false),
            ref) {
    update();
  }

  ///
  /// ステップ量
  ///
  final int _stepValue = 5;

  @override
  int get maxSpaceValue => LineVStitchModel.maxiSpaceValue;

  @override
  int get minSpaceValue => LineVStitchModel.miniSpaceValue;

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  ///
  /// スペース値表示星
  ///
  bool _isSpaceValueDisplayStar =
      LineVStitchModel().getSpace() != LineVStitchModel.spacingNotUpdating
          ? false
          : true;

  ///
  /// スペース値
  ///
  int _spaceValue = LineVStitchModel().getSpace();

  @override
  void update() {
    state = state.copyWith(
        spaceDisplayValue: _getSpaceDisplayValue(),
        isDefaultValue: _isDefaultValue(),
        plusButtonValid: _getPlusButtonState(),
        minusButtonValid: _getMinusButtonState());
  }

  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isSpaceValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSpaceValueDisplayStar = false;

      ///  Model 更新
      _spaceValue = LineVStitchModel().spaceDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_spaceValue <= LineVStitchModel.miniSpaceValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _spaceValue = _spaceValue - _stepValue < LineVStitchModel.miniSpaceValue
        ? LineVStitchModel.miniSpaceValue
        : _spaceValue - _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_isSpaceValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSpaceValueDisplayStar = false;

      ///  Model 更新
      _spaceValue = LineVStitchModel().spaceDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_spaceValue >= LineVStitchModel.maxiSpaceValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _spaceValue = _spaceValue + _stepValue > LineVStitchModel.maxiSpaceValue
        ? LineVStitchModel.maxiSpaceValue
        : _spaceValue + _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineVStitchSpace.toString());
    if (_isSpaceValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int spaceValue = LineVStitchModel().getSpace();

    /// Model 更新
    LineVStitchModel().setSpace(_spaceValue);

    if (LineVStitchModel().setMdcVStitchLineSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (spaceValue != _spaceValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// 間隔の表示値を取得します
  ///
  String _getSpaceDisplayValue() {
    /// cmからmmへ
    double lineVStitchSpaceValue = _spaceValue / _conversionRate;

    if (_isSpaceValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return lineVStitchSpaceValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(lineVStitchSpaceValue);
  }

  ///
  /// 間隔表示テキスト スタイルを取得します
  ///
  bool _isDefaultValue() {
    if (_isSpaceValueDisplayStar) {
      return true;
    }

    if (_spaceValue == LineVStitchModel().spaceDefaultValue) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isSpaceValueDisplayStar) {
      return true;
    }

    if (_spaceValue <= LineVStitchModel.miniSpaceValue) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isSpaceValueDisplayStar) {
      return true;
    }

    if (_spaceValue >= LineVStitchModel.maxiSpaceValue) {
      return false;
    }
    return true;
  }
}
