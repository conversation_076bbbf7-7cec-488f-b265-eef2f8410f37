import 'dart:async';
import 'dart:ffi';
import 'dart:io' show IOException;
import 'dart:ui' as ui;

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:log/log.dart';
import 'package:ml_handling/ml_handling.dart';
import 'package:panel_library/panel_library.dart';
import 'package:path/path.dart';

import '../../../../../memory/memory.dart';
import '../../../../../model/device_memory_model.dart';
import '../../../home/<USER>/bwd_model.dart';
import '../../../home/<USER>/component/home_header/bwd_popup/bwd_popup_view_model.dart';
import 'photo_stitch_history.dart';
import 'style_change_model.dart';

///
/// コンテンツ画像編集種類
///
enum SizeEditType {
  background,
  mask,
  frame,
  none,
}

///
/// png画像の解像度単位
///
enum PngUnit {
  unknown,
  meter,
  inch,
}

///
/// Jpeg画像の解像度単位
///
enum JpegUnit {
  unknown(1),
  inch(2),
  centimeter(3);

  const JpegUnit(this.number);
  final int number;
  static JpegUnit getValuesByNumber(int number) => JpegUnit.values.firstWhere(
        (element) => element.number == number,
      );
}

///
/// ファイルサイズ：6MBまでのサイズ
///
const _imageMaxSize = 1024 * 1024 * 6;

///
/// 最大1600万画素
///
const _imageMaxPixel = 16000000;

///
/// 画像読取時のエラーメッセージ
///
enum PhotoStitchImageError {
  /// メモリーFull
  memoryFull,

  /// USB未接続
  pathNotFoundException,

  /// ファイルが異常に書き込まれました
  pathAccessWriteException,

  /// ファイルの削除が異常です
  pathAccessDeleteException,

  /// ファイル破損異常です
  fileCorruptionException,

  /// ファイル保存エラー
  fileSaveInvalidException,

  /// Internalエラー
  internalException,

  /// ライブラリ側のメモリフル
  libSaveFileMemoryFull,

  /// その他
  other,

  overSize,
  overFrame,

  /// ファイルが大きすぎる
  overTooLarge,
  none;

  const PhotoStitchImageError();

  static PhotoStitchImageError formAccessError(AccessError accessError) =>
      switch (accessError) {
        AccessError.memoryFull => PhotoStitchImageError.memoryFull,
        AccessError.other => PhotoStitchImageError.other,
        AccessError.pathNotFoundException =>
          PhotoStitchImageError.pathNotFoundException,
        AccessError.none => PhotoStitchImageError.none,
        AccessError.pathAccessWriteException =>
          PhotoStitchImageError.pathAccessWriteException,
        AccessError.pathAccessDeleteException =>
          PhotoStitchImageError.pathAccessDeleteException,
        AccessError.fileCorruptionException =>
          PhotoStitchImageError.fileCorruptionException,
        AccessError.fileSaveInvalidException =>
          PhotoStitchImageError.fileSaveInvalidException,
        AccessError.internalException =>
          PhotoStitchImageError.internalException,
        AccessError.libSaveFileMemoryFull =>
          PhotoStitchImageError.libSaveFileMemoryFull,
      };

  static AccessError toAccessError(PhotoStitchImageError screenSaverError) =>
      switch (screenSaverError) {
        PhotoStitchImageError.memoryFull => AccessError.memoryFull,
        PhotoStitchImageError.pathNotFoundException =>
          AccessError.pathNotFoundException,
        PhotoStitchImageError.none => AccessError.none,
        PhotoStitchImageError.pathAccessWriteException =>
          AccessError.pathAccessWriteException,
        PhotoStitchImageError.pathAccessDeleteException =>
          AccessError.pathAccessDeleteException,
        PhotoStitchImageError.fileCorruptionException =>
          AccessError.fileCorruptionException,
        PhotoStitchImageError.fileSaveInvalidException =>
          AccessError.fileSaveInvalidException,
        PhotoStitchImageError.internalException =>
          AccessError.internalException,
        PhotoStitchImageError.libSaveFileMemoryFull =>
          AccessError.libSaveFileMemoryFull,
        _ => AccessError.other,
      };
}

///
/// 画像の構造体を読み込みます
///
class ImageResponse<T> {
  const ImageResponse({
    required this.data,
    required this.errorId,
  });
  final T data;
  final PhotoStitchImageError errorId;
}

///
/// ページ表示用のファイル情報構造体
///
class PhotoStitchFolderType {
  const PhotoStitchFolderType({
    required this.fileName,
    required this.path,
    required this.isDirectory,
  });
  final String fileName;
  final String path;
  final bool isDirectory;
}

///
/// 写真データ
///
class PhotoStitchImage {
  PhotoStitchImage.init()
      : imageData = Uint8List.fromList([]),
        dpiWidth = 0.0,
        dpiHeight = 0.0,
        pixelWidth = 0,
        pixelHeight = 0;

  PhotoStitchImage({
    Uint8List? imageData,
    this.pixelWidth = 0,
    this.pixelHeight = 0,
    this.dpiWidth = 0.0,
    this.dpiHeight = 0.0,
  }) : imageData = imageData ?? Uint8List.fromList([]);

  /// 画像データ(png)
  Uint8List imageData;

  /// 横向きの画像のDPI(inch)
  double dpiWidth;

  /// 縦向きの画像のDPI(inch)
  double dpiHeight;

  /// 横向きの画像のDPMM(mm)
  double get dpMMWidth => dpiWidth / PhotoStitchModel.inchToMM;

  /// 縦向きの画像のDPMM(mm)
  double get dpMMHeight => dpiHeight / PhotoStitchModel.inchToMM;

  /// 横向きの画像のピクセル長(pixel)
  double pixelWidth;

  /// 縦向きの画像のピクセル長(pixel)
  double pixelHeight;

  /// 画像の実際の幅(inch)
  double get realWidthInch => pixelWidth / dpiWidth;

  /// 画像の実際の高さ(inch)
  double get realHeightInch => pixelHeight / dpiHeight;

  /// 画像の実際の幅(mm)
  double get realWidthMM => pixelWidth / dpMMWidth;

  /// 画像の実際の高さ(mm)
  double get realHeightMM => pixelHeight / dpMMHeight;
}

class PhotoStitchModel {
  PhotoStitchModel._internal();
  factory PhotoStitchModel() => _instance;
  static final PhotoStitchModel _instance = PhotoStitchModel._internal();

  /// ページに切り替わる際に遷移するためのList
  List<PhotoStitchFolderType> photoFileList = [];

  /// 読み取りたいデバイス
  DeviceKind selectedDevice = DeviceKind.usb;

  /// USB パス
  String selectedUsbPath = "";

  ///
  /// コンテンツ画像編集されだかどうかの判断
  ///
  SizeEditType imageEditType = SizeEditType.none;

  ///
  /// 写真刺繍スタイル画像ファイル
  ///
  FileEntity? customSelectedFile;

  ///
  /// 最大イメージサイズ
  ///
  static const int maxPhotoFrame = 262;

  ///
  /// 画像ファイルのパス
  ///
  String _fileName = "";

  ///
  /// 1inch(=25.4mm)
  ///
  static const double inchToMM = 25.4;

  ///
  /// 画像データ
  ///
  final PhotoStitchImage photoStitchImage = PhotoStitchImage(
    imageData: Uint8List(0),
    dpiWidth: 0.0,
    dpiHeight: 0.0,
    pixelWidth: 0,
    pixelHeight: 0,
  );

  ///
  /// 写真刺繍の編集画面に入るかどうか
  ///
  bool isInPhotoStitchEdit = false;

  ///
  /// 刺繍写真のページにはいりましたか
  ///
  bool isPhotoStitchEntered = false;

  /// UI 上の画像の最大幅
  final double maxDisplayImagePixelWidth = 539;

  /// UI 上の画像の最大高さ
  final double maxDisplayImagePixelHeight = 529;

  /// サービス処理操作
  OperationState _operationState = OperationState.disableCancel;
  void initOperationState() => _operationState = OperationState.disableCancel;
  void setOperationState(OperationState state) => _operationState = state;
  OperationState getOperationState() => _operationState;

  ///
  /// 選択された画像情報を取得する
  ///
  Future<ImageResponse<Map<String, dynamic>?>> getImageData(
      String filePath) async {
    try {
      if (FileEntity(filePath).existsSync() == false) {
        return const ImageResponse(
          data: null,
          errorId: PhotoStitchImageError.pathNotFoundException,
        );
      }

      /// メソッドを呼び出し、結果を待機します
      final Map<dynamic, dynamic>? result =
          await MlHandling().getImageData(filePath);

      _fileName = basename(filePath);

      /// 戻り値が Map 型であるか確認します
      if (result != null) {
        final Map<String, dynamic> imageData =
            Map<String, dynamic>.from(result);
        return ImageResponse(
          data: imageData,
          errorId: PhotoStitchImageError.none,
        );
      } else {
        return const ImageResponse(
          data: null,
          errorId: PhotoStitchImageError.other,
        );
      }
    } on PlatformException catch (e) {
      /// エラーコードに基づいて異なるエラーを設定します
      switch (e.code) {
        case 'invalidFormat':
          return const ImageResponse(
            data: null,
            errorId: PhotoStitchImageError.overFrame,
          );
        case 'overSize':
          return const ImageResponse(
            data: null,
            errorId: PhotoStitchImageError.overSize,
          );
        default:
          return const ImageResponse(
            data: null,
            errorId: PhotoStitchImageError.other,
          );
      }
    } catch (error) {
      /// その他のエラーを処理します
      return const ImageResponse(
        data: null,
        errorId: PhotoStitchImageError.other,
      );
    }
  }

  ///
  /// ファイルリストの取得
  ///
  Offset getMaxDisplaySize(PhotoStitchImage data) {
    Offset size = Offset.zero;
    double ratio = data.realWidthMM / data.realHeightMM;
    if (data.realWidthMM > data.realHeightMM) {
      size = Offset(
        maxDisplayImagePixelWidth,
        maxDisplayImagePixelWidth / ratio,
      );
    } else {
      size = Offset(
        maxDisplayImagePixelHeight * ratio,
        maxDisplayImagePixelHeight,
      );
    }

    return size;
  }

  ///
  /// ファイルリストの取得
  ///
  static Future<MemoryAccessResponse<List<PhotoStitchFolderType>>> getFileList(
          {required DeviceKind deviceKind,
          String subPath = '',
          String usingUsbPath = ""}) =>
      DeviceMemoryModel().beginUsbTransactionIfNecessary(
          deviceKind == DeviceKind.usb,
          usingUsbPath,
          () => _getFileList(
              deviceKind: deviceKind,
              subPath: subPath,
              usingUsbPath: usingUsbPath));

  ///
  /// ファイルリストの取得
  ///
  static Future<MemoryAccessResponse<List<PhotoStitchFolderType>>> _getFileList(
      {required DeviceKind deviceKind,
      String subPath = '',
      String usingUsbPath = ""}) async {
    await Future.delayed(const Duration(milliseconds: 100));

    List<PhotoStitchFolderType> fileList = [];

    AccessError accessError = AccessError.none;
    try {
      String directoryPath = switch (deviceKind) {
        DeviceKind.usb => usingUsbPath,
        DeviceKind.wLan => memorySector.net_app.absolutePath,
        DeviceKind.internalStorage => memorySector.embMem.absolutePath,
        _ => () {
            Log.errorTrace("unException deviceKind:$deviceKind");
            return '';
          }(),
      };

      final WalkFiler walkFiler;
      final SortOrder sortOrder;
      if (deviceKind == DeviceKind.usb) {
        sortOrder = SortOrder.ascend;
        walkFiler = WalkFiler.fileNameRegExpWithAnyFolder(
            FileExtensionRegExp.jpg_png_bmp);
      } else {
        sortOrder = SortOrder.descend;
        walkFiler = WalkFiler.fileOnlyByName(FileExtensionRegExp.jpg_png_bmp);
      }

      final List<MemoryEntity> loadedList = DeviceMemoryModel()
          .directoryWalk(
            DirectoryEntity(join(directoryPath, subPath)),
            walkFiler,
            sortOrder: sortOrder,
          )
          .where(DeviceMemoryModel().isNotLostDir)
          .toList();

      fileList = List.generate(
        loadedList.length,
        (index) => PhotoStitchFolderType(
          fileName: basename(loadedList[index].path),
          path: loadedList[index].path,
          isDirectory: loadedList[index] is DirectoryEntity,
        ),
      );
    } on IOException catch (e) {
      fileList = [];
      accessError = memoryExceptionToAccessError(e);
    }

    return MemoryAccessResponse(error: accessError, data: fileList);
  }

  ///
  /// スクリーンショットを読み込む関数
  ///
  static Future<ImageResponse<FileEntity?>> readImage(
          String path, String fileName) =>
      DeviceMemoryModel()
          .beginUsbTransaction(path, () => _readImage(path, fileName));

  ///
  /// スクリーンショットを読み込む関数
  ///
  static Future<ImageResponse<FileEntity?>> _readImage(
      String path, String fileName) async {
    await Future.delayed(const Duration(milliseconds: 100));

    /// 画像をロードできる最小サイズです
    const int minWidth = 32;
    const int minHeight = 32;
    FileEntity file;
    try {
      /// 指定した画像を読み込む
      file = FileEntity(path);

      /// ファイルサイズ6MB未満のチェック
      if (file.lengthSync() > _imageMaxSize) {
        return ImageResponse(
            data: file, errorId: PhotoStitchImageError.overTooLarge);
      }

      /// 1600万画素以内のチェック
      var decodedImage = await decodeImageFromList(file.readAsBytesSync());
      if ((decodedImage.height * decodedImage.width) > _imageMaxPixel) {
        return ImageResponse(
            data: file, errorId: PhotoStitchImageError.overSize);
      }

      /// 画像が限定サイズより小さいかどうかを判定します
      if (decodedImage.width < minWidth || decodedImage.height < minHeight) {
        return const ImageResponse(
            data: null, errorId: PhotoStitchImageError.overFrame);
      }
    } on Exception catch (error) {
      if (error is IOException) {
        final photoStitchImageError = PhotoStitchImageError.formAccessError(
            memoryExceptionToAccessError(error));
        return ImageResponse(data: null, errorId: photoStitchImageError);
      }
      Log.debugTrace('画像解析エラー!!!. message is ${error.toString()}');
      return const ImageResponse(
          data: null, errorId: PhotoStitchImageError.overFrame);
    }

    return ImageResponse(data: file, errorId: PhotoStitchImageError.none);
  }

  ///
  /// Flutter画像をui.Imageに変換する
  ///
  Future<ui.Image> flutterImageToUiImage(Image? imageProvider) async {
    Completer<ui.Image> completer = Completer<ui.Image>();
    imageProvider?.image.resolve(const ImageConfiguration()).addListener(
      ImageStreamListener((info, _) {
        ui.Image image = info.image;
        completer.complete(image);
      }),
    );
    return completer.future;
  }

  bool get isUnitMm =>
      DeviceLibrary().apiBinding.getDisplayUnit().displayUnit == DisplayUnit.mm;

  ///
  /// 画面デフォルトクリア処理
  ///
  void reset() {
    photoFileList = [];
    selectedDevice = DeviceKind.usb;
    isInPhotoStitchEdit = false;
    imageEditType = SizeEditType.none;
    _operationState = OperationState.disableCancel;

    PhotoStitchHistoryModel().reset();
  }

  ///
  /// 元画像のwidgetからui.Imageに変更する
  ///
  Future<Uint8List> convertImageToRgbaData(Image imageProvider) async {
    ui.Image image = await flutterImageToUiImage(imageProvider);
    ByteData? byteData =
        await image.toByteData(format: ui.ImageByteFormat.rawRgba);

    if (byteData == null) {
      return Uint8List.fromList([]);
    } else {
      return byteData.buffer.asUint8List();
    }
  }

  ///
  /// serverを呼び出して、処理が必要なイメージを追加します
  ///
  Future<int?> mlAddContent(Uint8List imageData, double width, double height) {
    Completer<int> completer = Completer<int>();

    MlHandling()
        .mlAddContent(
      imageData,
      _fileName,
      width,
      height,
    )
        .then((value) {
      completer.complete(value);
    });

    return completer.future;
  }

  ///
  /// serverを呼び出してスタイル イメージを追加する
  ///
  Future<void> addCustomStyleImage(FileEntity file, Uint8List contentData,
          double width, double height) =>
      DeviceMemoryModel().beginUsbTransaction(file.path,
          () => _addCustomStyleImage(file, contentData, width, height));

  ///
  /// serverを呼び出してスタイル イメージを追加する
  ///
  Future<void> _addCustomStyleImage(FileEntity file, Uint8List contentData,
      double width, double height) async {
    final img.Image decodedImage = img.decodeImage(file.readAsBytesSync())!;
    final Uint8List imageData = img.encodeBmp(decodedImage);
    await mlAddContent(contentData, width, height);
    final isSuccessful = await MlHandling().mlAddStyle(
      imageData,
      basename(file.path),
      StyleChangeModel().getCustomColorUse(),
      decodedImage.width.toDouble(),
      decodedImage.height.toDouble(),
    );
    if (isSuccessful == null || isSuccessful == 0) {
      Log.debugTrace('ML側mlAddStyle処理エラー！！！');
    }
  }

  ///
  /// serverを呼び出して、処理が必要なイメージを追加します
  ///
  Future<int?> addBuiltInStyleImage(Image? previewImage, String fileName,
      Uint8List contentImage, double width, double height) async {
    Completer<int> completer = Completer<int>();

    await mlAddContent(contentImage, width, height);
    flutterImageToUiImage(previewImage).then((uiImage) {
      uiImage.toByteData(format: ui.ImageByteFormat.png).then((byteData) {
        MlHandling()
            .mlAddStyle(
          byteData!.buffer.asUint8List(),
          fileName,
          StyleChangeModel().getBuiltInColorUse(),
          uiImage.width.toDouble(),
          uiImage.height.toDouble(),
        )
            .then((value) {
          completer.complete(value);
        });
      });
    });

    return completer.future;
  }

  ///
  /// serverを呼び出してイメージのスタイルを変更する
  ///
  Future<void> getMlProcessState() async {
    const int successful = 1;

    Completer completer = Completer();
    MlHandling().mlHandlingStart().then((startStatus) {
      /// ML起動失敗の場合
      if (startStatus != successful) {
        setOperationState(OperationState.error);
        Log.debugTrace('ML側 mlHandlingStart実行失敗！！！');
        return;
      }

      /// ML起動成功の場合
      /// キャンセル操作できる
      Log.debugTrace('ML側 mlHandlingStart実行成功！！！');

      bool isScriptStart = false;
      // 100ミリ秒ごとに指定した機能を実行するタイマーを開始します
      LoopTimer.periodic(const Duration(milliseconds: 100), (timer) async {
        final state = await MlHandling().mlGetStatus();

        if (state == MLState.mlOk.number) {
          Log.debugTrace('ML側画像処理成功！！！');

          /// キャンセル操作中：finishCancel
          /// 通常操作中：successful
          if (getOperationState() == OperationState.startCancel) {
            setOperationState(OperationState.finishCancel);
          } else {
            setOperationState(OperationState.successful);
          }

          timer.cancel();
          completer.complete();
        } else if (state == MLState.mlInt.number) {
          Log.debugTrace('ML側キャンセル操作完了！！！');
          setOperationState(OperationState.finishCancel);

          timer.cancel();
          completer.complete();
        } else if (state == MLState.mlErr.number ||
            state == MLState.mlAbrt.number) {
          Log.debugTrace('ML側スタイル変更エラー！！！. state is $state');
          setOperationState(OperationState.error);

          timer.cancel();
          completer.complete();
        } else {
          if (state == MLState.mlBusy.number) {
            Log.debugTrace('ML側スクリプト起動中！！！');
            isScriptStart = true;
            return;
          }

          if (isScriptStart) {
            Log.debugTrace('ML側スクリプト起動完了！！！');
            setOperationState(OperationState.canBeCancel);
            isScriptStart = false;
            return;
          }

          Log.debugTrace('ML側処理中！！！. state is $state');
        }
      });
    });

    return completer.future;
  }

  ///
  /// Androidコードを使用して、画像の透明チャネルを処理します
  /// - inImage:処理が必要な画像;
  /// - isClip:画像の透明な領域を切り抜くかどうか、falseの場合、画像の透明チャネルは白に変更されます
  ///
  Future<({Uint8List image, int width, int height})?> removeTransparentColor(
      Uint8List inImage, bool isClip) async {
    Completer<({Uint8List image, int width, int height})> completer =
        Completer<({Uint8List image, int width, int height})>();
    MlHandling().removeTransparentColor(inImage, isClip).then((value) {
      if (value != null) {
        Uint8List resultImage = value["image"];
        int imageWidth = value["width"];
        int imageHeight = value["height"];

        completer.complete(
            (image: resultImage, width: imageWidth, height: imageHeight));
      }
    });

    return completer.future;
  }

  ///
  /// RGBAデータからImageデータに変換する
  ///
  Future<ui.Image> rgbaDataToDisplayImage(
      Uint8List rgbaData, int width, int height) async {
    final Completer<ui.Image> completer = Completer();
    ui.decodeImageFromPixels(
      rgbaData,
      width,
      height,
      ui.PixelFormat.rgba8888,
      (img) {
        completer.complete(img);
      },
    );
    return completer.future;
  }

  ///
  /// 透明チャンネルの画像をブレンドする
  ///
  /// 画像transparentの透明チャンネルを画像sourceに移行する
  ///
  Future<Uint8List> blendTransparentImage(
      Uint8List transparent, Uint8List source, int width, int height) async {
    /// 透明なチャンネルを持つ画像
    ui.Image transparentImage;
    final codecTransparent = await ui.instantiateImageCodec(transparent);
    transparentImage = (await codecTransparent.getNextFrame()).image;

    /// 透明なチャンネルにブレンドする必要がある画像
    ui.Image srcImage;
    final codecSrc = await ui.instantiateImageCodec(source);
    srcImage = (await codecSrc.getNextFrame()).image;

    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    /// 最初に元の画像を描く
    final paint = Paint()..blendMode = BlendMode.src;
    canvas.drawImage(srcImage, ui.Offset.zero, paint);

    /// 透明なチャネルを描画する
    paint.blendMode = BlendMode.dstIn;
    canvas.drawImage(transparentImage, ui.Offset.zero, paint);

    final picture = recorder.endRecording();
    final image = await picture.toImage(width, height);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    return byteData!.buffer.asUint8List();
  }

  ///
  /// 刺繍写真に入る前の準備
  ///
  bool prepareForPhotoStitch() {
    isPhotoStitchEntered = true;
    final bool isSuccess = TpdLibrary()
        .apiBinding
        .setMatrixEnableListNotOverwrited(
            MachineKeyState.machineKeyEnableAllNG);
    if (isSuccess == false) {
      return false;
    }

    closeBwdPopup();
    if (BwdModel.isBwdStart) {
      BwdModel.stopBwd();
    }

    return true;
  }

  ///
  /// 刺繍写真から退出後の動作
  ///
  void actionAfterExitingPhotoStitch() {
    if (isPhotoStitchEntered) {
      TpdLibrary().apiBinding.clearMatrixEnableListNotOverwrited(
          MachineKeyState.machineKeyEnableAll);

      int bwdIsOn = TpdLibrary()
          .apiBinding
          .bpIFGetAppDisplayGlobal()
          .bwdPanelState
          .ref
          .IsOn;

      if (bwdIsOn != ENUM_LIB.FALSE) {
        BwdModel.handleHeaderBobbinButton();
        openBwdPopup();
      }
    }

    isPhotoStitchEntered = false;
  }

  ///
  /// 指定した画像を削除します
  ///
  Future<bool> deleteImage(String filePath) => DeviceMemoryModel()
      .beginUsbTransaction(filePath, () => _deleteImage(filePath));

  ///
  /// 指定した画像を削除します
  ///
  bool _deleteImage(String filePath) {
    try {
      /// 指定したディレクトリの下のファイルリストを読み込みます
      final FileEntity file = FileEntity(filePath);

      /// フォルダのオープンに失敗しました
      if (file.existsSync()) {
        file.deleteSync();
        return true;
      } else {
        return false;
      }
    } on Exception catch (e) {
      Log.errorTrace('画像データを読み込エラー!!!. message is ${e.toString()}');
      return false;
    }
  }
}

/// MLの状態
enum MLState {
  /// 時間内に更新されなかった
  mlNoUpdata(-1),

  /// recvエラー発生
  mlRecvErr(-2),

  /// 応答受信用ソケットなし
  mlNoRespSock(-3),

  /// 初期状態
  mlBlank(0),

  /// 正常終了した
  mlOk(1),

  /// 異常終了した
  mlErr(2),

  /// 割込み終了した
  mlInt(3),

  /// アブートした
  mlAbrt(4),

  /// 動作中
  mlBusy(5),

  /// 開始できなかった
  mlDidNotStart(6),

  /// 起動待ち
  mlStartWait(7),

  /// 未定義
  mlUnknownErr(8);

  const MLState(this.number);
  final int number;

  static MLState getValuesByNumber(int number) => MLState.values
          .firstWhere((element) => element.number == number, orElse: () {
        Log.errorTrace('can not find MLState');
        return MLState.mlErr;
      });
}

///
/// キャンセル操作状態
///
enum OperationState {
  /// エラー状態
  error,

  /// 変換成功
  successful,

  /// キャンセル操作できない
  disableCancel,

  /// キャンセル操作できる
  canBeCancel,

  /// キャンセル操作開始
  startCancel,

  /// キャンセル操作完了
  finishCancel,
}
