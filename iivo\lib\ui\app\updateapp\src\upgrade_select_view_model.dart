import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// ignore: implementation_imports
import 'package:panel_library/src/device_library/src/implement/mock/mock_setting_data.dart';
import 'package:path/path.dart';
import 'package:usb_manager/usb_manager.dart';
import 'package:xd_component/l10n/app_localizations.dart';

import '../iivo_export.dart';
import '../model/upgrade_model.dart';
import '../page/page_route.dart';
import '../update_app_log.dart';
import 'upgrade_select_view_interface.dart';

/// view _modelに必要な構造
final upgradePageViewModeProvider = StateNotifierProvider.autoDispose
    .family<UpgradeSelectViewInterface, UpgradeSelectState, BuildContext>(
        (ref, context) => UpgradeSelectViewMode(ref, context));

class UpgradeSelectViewMode extends UpgradeSelectViewInterface {
  UpgradeSelectViewMode(
    ref,
    context,
  ) : super(const UpgradeSelectState(), ref, context) {
    _cleanMainData();
    _cleanLineUpdateData();

    state = state.copyWith(
      upgVersion: UpgradeModel().upgVersion,
      isWifiConnect: _isWifiCanClick(),
      isUsbUpgrade: _isUsbUpgrade(),
    );

    _observeUsbInfoChanges();
  }

  /// USB情報監視解除機能
  Function? _usbListenerUnregister;

  final _l10n = lookupAppLocalizations(AppLocale().getCurrentLocale());

  final String _mainUpdateProgress = "IIVO_PROCESS_";
  final String _mainUpdateOk = "IIVO_RESULT_OK";
  final String _mainUpdateNg = "IIVO_RESULT_NG";
  final String _lineUpdateCheck = "PROUPCHK.IIVO";
  final String _lineUpdateEnd = "PROUPEND.IIVO";
  final String _lineUpdateDge = "PROUPJUDGE.IIVO";

  ///
  /// USB情報の監視を開始します
  ///
  void _observeUsbInfoChanges() {
    _usbListenerUnregister = UsbManager().registerUsbChangeListener(
        (usbInfoList) => state = state.copyWith(usbInfoList: usbInfoList));
  }

  ///
  /// [UpdateAppPageRouteEnum.upgradeLoad]ページにジャンプします。パフォーマンスを向上させるには、
  /// ジャンプする前に USB 情報の監視をキャンセルし、現在のページに戻った後に監視を再開します。
  ///
  void _gotoUpgradeLoadPage() {
    _usbListenerUnregister?.call();
    UpdateAppPagesRoute()
        .pushNamed(nextRoute: UpdateAppPageRouteEnum.upgradeLoad)
        .then((_) => _observeUsbInfoChanges());
  }

  @override
  Future<void> onUSB1ButtonClick() async {
    if (state.usbInfoList.isEmpty) {
      state = state.copyWith(errorValue: _l10n.t_err89);
      return;
    }

    UpgradeModel().usbPath = state.usbInfoList[usbButton1].usbPath;
    UpgradeModel().deviceKind = DeviceKind.usb;
    _gotoUpgradeLoadPage();
  }

  @override
  void onUSB2ButtonClick() {
    UpgradeModel().usbPath = state.usbInfoList[usbButton2].usbPath;
    UpgradeModel().deviceKind = DeviceKind.usb;
    _gotoUpgradeLoadPage();
  }

  @override
  void onWifiPocketButtonClick() {
    UpgradeModel().usbPath =
        join(memorySector.brother_dir.absolutePath, "upgrade");
    UpgradeModel().deviceKind = DeviceKind.wLan;
    _gotoUpgradeLoadPage();
  }

  ///
  /// Wi-Fiボタンをクリックできるかどうかを確認します
  ///
  bool _isWifiCanClick() {
    List<FolderType> folders = [];
    try {
      folders = _getFileUPFList(
          join(memorySector.brother_dir.absolutePath, "upgrade"));
    } catch (e) {
      return false;
    }

    return folders.isNotEmpty;
  }

  ///
  /// USBアップデートが使えるかどうか判断します
  /// インストール開始後,USBアップデート不可,キーはグレーアウト。押すと拒否音。
  ///
  bool _isUsbUpgrade() {
    try {
      final FileEntity txtFile = FileEntity(UpgradeModel().txtSavePath);
      if (txtFile.existsSync()) {
        return false;
      } else {
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  ///
  /// UPFファイルを読み取る
  ///
  List<FolderType> _getFileUPFList(String path) {
    List<FolderType> fileList = [];

    try {
      final directoryPath = path;

      final List<MemoryEntity> loadedList = DeviceMemoryModel().directoryWalk(
        DirectoryEntity(directoryPath),
        WalkFiler.fileNameRegExpWithAnyFolder(
            RegExp(r'\.upf$', caseSensitive: false)),
      );

      for (var element in loadedList) {
        if (element is DirectoryEntity) {
          // do nothing
        } else {
          fileList.add(
            FolderType(
              fileName: basename(element.path),
              path: element.path,
              isDirectory: false,
            ),
          );
        }
      }
    } catch (e) {
      fileList = [];
    }

    return fileList;
  }

  @override
  void launchAndroidSettings() {
    if (getMockData()["androidSettingsEntry"] == true) {
      const MethodChannel("main").invokeMapMethod("launchSettings");
    }
  }

  @override
  bool get isLineUpdate => FileEntity(
          join(memorySector.brother_dir.absolutePath, "IIVO_PRODUCTMODE"))
      .existsSync();

  ///
  /// メインプログラム更新時のデータをクリアする
  ///
  void _cleanMainData() {
    UpdateAppLog.d("Delete residual data after main update start");
    try {
      final String brotherDirPath = memorySector.brother_dir.absolutePath;

      Directory(brotherDirPath).list().forEach((entity) {
        if (entity is File) {
          String fileName = basename(entity.path);
          if (fileName.startsWith(_mainUpdateProgress) ||
              fileName == _mainUpdateOk ||
              fileName == _mainUpdateNg ||
              fileName.contains(".zip") ||
              fileName.contains(".upf")) {
            FileEntity(join(brotherDirPath, fileName)).deleteSync();
          }
        }
      });

      final String updateAppPath =
          join(memorySector.brother_dir.absolutePath, "updateApp");
      final Directory directory = Directory(updateAppPath);

      if (directory.existsSync()) {
        directory.deleteSync(recursive: true);
      }
    } catch (e) {
      state = state.copyWith(
          errorValue:
              "${_l10n.upg_16}\n${UpgradeError.errorCode1.errorCodeMessage}");
      UpdateAppLog.e("Delete residual data after main update error:$e");
    }
    UpdateAppLog.d("Delete residual data after main update end");
  }

  ///
  /// LineUpdateの残存データをクリアする
  ///
  void _cleanLineUpdateData() {
    UpdateAppLog.d("Delete residual data after line update start");
    try {
      FileEntity checkFile = FileEntity(
          join(memorySector.line_update.absolutePath, _lineUpdateCheck));
      FileEntity endFile = FileEntity(
          join(memorySector.line_update.absolutePath, _lineUpdateEnd));
      FileEntity dgeFile = FileEntity(
          join(memorySector.line_update.absolutePath, _lineUpdateDge));
      FileEntity homeFile =
          FileEntity(join(memorySector.brother_dir.absolutePath, "home.apk"));
      if (homeFile.existsSync()) {
        homeFile.deleteSync();
      }

      if (checkFile.existsSync()) {
        checkFile.deleteSync();
      }

      if (endFile.existsSync()) {
        endFile.deleteSync();
      }

      if (dgeFile.existsSync()) {
        dgeFile.deleteSync();
      }
    } catch (e) {
      state = state.copyWith(
          errorValue:
              "${_l10n.upg_16}\n${UpgradeError.errorCode2.errorCodeMessage}");
      UpdateAppLog.e("Delete residual data after line update error:$e");
    }
    UpdateAppLog.d("Delete residual data after line update end");
  }

  @override
  void dispose() {
    super.dispose();
    _usbListenerUnregister?.call();
  }
}
