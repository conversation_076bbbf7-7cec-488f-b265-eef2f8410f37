// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'preview_view_interface.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PreviewState {
  /// Pattern表示情報
  List<PatternDisplayInfo> get patternDisplayInfoList =>
      throw _privateConstructorUsedError;

  /// maskボックスがPreview表示領域を超えていないか
  /// 超えた場合、赤いドットは表示されません
  bool get isRedPointOverPreview => throw _privateConstructorUsedError;

  /// セッティング画面　刺しゅう２　（ページ：9）
  /// 背景色
  Color get backgroundColor => throw _privateConstructorUsedError;

  /// 赤枠の色
  Color get maskColor => throw _privateConstructorUsedError;

  /// セッティング画面　刺しゅう１　（ページ：8）
  /// グリッド
  int get gridTypeIndex => throw _privateConstructorUsedError;

  /// グリッドグリッド
  Color get gridColor => throw _privateConstructorUsedError;

  /// 枠サイズ
  Path get frameDrawPath => throw _privateConstructorUsedError;

  /// 枠サイズ色
  Color get frameColor => throw _privateConstructorUsedError;

  /// 針数から針位置
  Offset? get needlePointerCenterOffset => throw _privateConstructorUsedError;

  /// 背景スキャンの画像
  Widget? get backgroundImage => throw _privateConstructorUsedError;

  /// ページ上の糸印の表示情報
  List<ThreadMarkInfo> get threadMarkInfoList =>
      throw _privateConstructorUsedError;

  /// 投影がオンになっているかどうか
  bool get isProjectorON => throw _privateConstructorUsedError;

  /// Mask ボックスの表示情報
  Mask? get maskDisplayInfo => throw _privateConstructorUsedError;

  /// Rotateドットの表示位置情報
  RedPointDisplayInfo? get rotateRedPoint => throw _privateConstructorUsedError;

  /// 回転の中心点
  Offset get rotateCenter => throw _privateConstructorUsedError;

  /// ドラッグまたは長押しで設定された角度
  double? get dragAngle => throw _privateConstructorUsedError;

  /// キルトマスク
  Path? get quiltMask => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PreviewStateCopyWith<PreviewState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PreviewStateCopyWith<$Res> {
  factory $PreviewStateCopyWith(
          PreviewState value, $Res Function(PreviewState) then) =
      _$PreviewStateCopyWithImpl<$Res, PreviewState>;
  @useResult
  $Res call(
      {List<PatternDisplayInfo> patternDisplayInfoList,
      bool isRedPointOverPreview,
      Color backgroundColor,
      Color maskColor,
      int gridTypeIndex,
      Color gridColor,
      Path frameDrawPath,
      Color frameColor,
      Offset? needlePointerCenterOffset,
      Widget? backgroundImage,
      List<ThreadMarkInfo> threadMarkInfoList,
      bool isProjectorON,
      Mask? maskDisplayInfo,
      RedPointDisplayInfo? rotateRedPoint,
      Offset rotateCenter,
      double? dragAngle,
      Path? quiltMask});
}

/// @nodoc
class _$PreviewStateCopyWithImpl<$Res, $Val extends PreviewState>
    implements $PreviewStateCopyWith<$Res> {
  _$PreviewStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? patternDisplayInfoList = null,
    Object? isRedPointOverPreview = null,
    Object? backgroundColor = null,
    Object? maskColor = null,
    Object? gridTypeIndex = null,
    Object? gridColor = null,
    Object? frameDrawPath = null,
    Object? frameColor = null,
    Object? needlePointerCenterOffset = freezed,
    Object? backgroundImage = freezed,
    Object? threadMarkInfoList = null,
    Object? isProjectorON = null,
    Object? maskDisplayInfo = freezed,
    Object? rotateRedPoint = freezed,
    Object? rotateCenter = null,
    Object? dragAngle = freezed,
    Object? quiltMask = freezed,
  }) {
    return _then(_value.copyWith(
      patternDisplayInfoList: null == patternDisplayInfoList
          ? _value.patternDisplayInfoList
          : patternDisplayInfoList // ignore: cast_nullable_to_non_nullable
              as List<PatternDisplayInfo>,
      isRedPointOverPreview: null == isRedPointOverPreview
          ? _value.isRedPointOverPreview
          : isRedPointOverPreview // ignore: cast_nullable_to_non_nullable
              as bool,
      backgroundColor: null == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as Color,
      maskColor: null == maskColor
          ? _value.maskColor
          : maskColor // ignore: cast_nullable_to_non_nullable
              as Color,
      gridTypeIndex: null == gridTypeIndex
          ? _value.gridTypeIndex
          : gridTypeIndex // ignore: cast_nullable_to_non_nullable
              as int,
      gridColor: null == gridColor
          ? _value.gridColor
          : gridColor // ignore: cast_nullable_to_non_nullable
              as Color,
      frameDrawPath: null == frameDrawPath
          ? _value.frameDrawPath
          : frameDrawPath // ignore: cast_nullable_to_non_nullable
              as Path,
      frameColor: null == frameColor
          ? _value.frameColor
          : frameColor // ignore: cast_nullable_to_non_nullable
              as Color,
      needlePointerCenterOffset: freezed == needlePointerCenterOffset
          ? _value.needlePointerCenterOffset
          : needlePointerCenterOffset // ignore: cast_nullable_to_non_nullable
              as Offset?,
      backgroundImage: freezed == backgroundImage
          ? _value.backgroundImage
          : backgroundImage // ignore: cast_nullable_to_non_nullable
              as Widget?,
      threadMarkInfoList: null == threadMarkInfoList
          ? _value.threadMarkInfoList
          : threadMarkInfoList // ignore: cast_nullable_to_non_nullable
              as List<ThreadMarkInfo>,
      isProjectorON: null == isProjectorON
          ? _value.isProjectorON
          : isProjectorON // ignore: cast_nullable_to_non_nullable
              as bool,
      maskDisplayInfo: freezed == maskDisplayInfo
          ? _value.maskDisplayInfo
          : maskDisplayInfo // ignore: cast_nullable_to_non_nullable
              as Mask?,
      rotateRedPoint: freezed == rotateRedPoint
          ? _value.rotateRedPoint
          : rotateRedPoint // ignore: cast_nullable_to_non_nullable
              as RedPointDisplayInfo?,
      rotateCenter: null == rotateCenter
          ? _value.rotateCenter
          : rotateCenter // ignore: cast_nullable_to_non_nullable
              as Offset,
      dragAngle: freezed == dragAngle
          ? _value.dragAngle
          : dragAngle // ignore: cast_nullable_to_non_nullable
              as double?,
      quiltMask: freezed == quiltMask
          ? _value.quiltMask
          : quiltMask // ignore: cast_nullable_to_non_nullable
              as Path?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PreviewStateImplCopyWith<$Res>
    implements $PreviewStateCopyWith<$Res> {
  factory _$$PreviewStateImplCopyWith(
          _$PreviewStateImpl value, $Res Function(_$PreviewStateImpl) then) =
      __$$PreviewStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<PatternDisplayInfo> patternDisplayInfoList,
      bool isRedPointOverPreview,
      Color backgroundColor,
      Color maskColor,
      int gridTypeIndex,
      Color gridColor,
      Path frameDrawPath,
      Color frameColor,
      Offset? needlePointerCenterOffset,
      Widget? backgroundImage,
      List<ThreadMarkInfo> threadMarkInfoList,
      bool isProjectorON,
      Mask? maskDisplayInfo,
      RedPointDisplayInfo? rotateRedPoint,
      Offset rotateCenter,
      double? dragAngle,
      Path? quiltMask});
}

/// @nodoc
class __$$PreviewStateImplCopyWithImpl<$Res>
    extends _$PreviewStateCopyWithImpl<$Res, _$PreviewStateImpl>
    implements _$$PreviewStateImplCopyWith<$Res> {
  __$$PreviewStateImplCopyWithImpl(
      _$PreviewStateImpl _value, $Res Function(_$PreviewStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? patternDisplayInfoList = null,
    Object? isRedPointOverPreview = null,
    Object? backgroundColor = null,
    Object? maskColor = null,
    Object? gridTypeIndex = null,
    Object? gridColor = null,
    Object? frameDrawPath = null,
    Object? frameColor = null,
    Object? needlePointerCenterOffset = freezed,
    Object? backgroundImage = freezed,
    Object? threadMarkInfoList = null,
    Object? isProjectorON = null,
    Object? maskDisplayInfo = freezed,
    Object? rotateRedPoint = freezed,
    Object? rotateCenter = null,
    Object? dragAngle = freezed,
    Object? quiltMask = freezed,
  }) {
    return _then(_$PreviewStateImpl(
      patternDisplayInfoList: null == patternDisplayInfoList
          ? _value._patternDisplayInfoList
          : patternDisplayInfoList // ignore: cast_nullable_to_non_nullable
              as List<PatternDisplayInfo>,
      isRedPointOverPreview: null == isRedPointOverPreview
          ? _value.isRedPointOverPreview
          : isRedPointOverPreview // ignore: cast_nullable_to_non_nullable
              as bool,
      backgroundColor: null == backgroundColor
          ? _value.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as Color,
      maskColor: null == maskColor
          ? _value.maskColor
          : maskColor // ignore: cast_nullable_to_non_nullable
              as Color,
      gridTypeIndex: null == gridTypeIndex
          ? _value.gridTypeIndex
          : gridTypeIndex // ignore: cast_nullable_to_non_nullable
              as int,
      gridColor: null == gridColor
          ? _value.gridColor
          : gridColor // ignore: cast_nullable_to_non_nullable
              as Color,
      frameDrawPath: null == frameDrawPath
          ? _value.frameDrawPath
          : frameDrawPath // ignore: cast_nullable_to_non_nullable
              as Path,
      frameColor: null == frameColor
          ? _value.frameColor
          : frameColor // ignore: cast_nullable_to_non_nullable
              as Color,
      needlePointerCenterOffset: freezed == needlePointerCenterOffset
          ? _value.needlePointerCenterOffset
          : needlePointerCenterOffset // ignore: cast_nullable_to_non_nullable
              as Offset?,
      backgroundImage: freezed == backgroundImage
          ? _value.backgroundImage
          : backgroundImage // ignore: cast_nullable_to_non_nullable
              as Widget?,
      threadMarkInfoList: null == threadMarkInfoList
          ? _value._threadMarkInfoList
          : threadMarkInfoList // ignore: cast_nullable_to_non_nullable
              as List<ThreadMarkInfo>,
      isProjectorON: null == isProjectorON
          ? _value.isProjectorON
          : isProjectorON // ignore: cast_nullable_to_non_nullable
              as bool,
      maskDisplayInfo: freezed == maskDisplayInfo
          ? _value.maskDisplayInfo
          : maskDisplayInfo // ignore: cast_nullable_to_non_nullable
              as Mask?,
      rotateRedPoint: freezed == rotateRedPoint
          ? _value.rotateRedPoint
          : rotateRedPoint // ignore: cast_nullable_to_non_nullable
              as RedPointDisplayInfo?,
      rotateCenter: null == rotateCenter
          ? _value.rotateCenter
          : rotateCenter // ignore: cast_nullable_to_non_nullable
              as Offset,
      dragAngle: freezed == dragAngle
          ? _value.dragAngle
          : dragAngle // ignore: cast_nullable_to_non_nullable
              as double?,
      quiltMask: freezed == quiltMask
          ? _value.quiltMask
          : quiltMask // ignore: cast_nullable_to_non_nullable
              as Path?,
    ));
  }
}

/// @nodoc

class _$PreviewStateImpl implements _PreviewState {
  const _$PreviewStateImpl(
      {required final List<PatternDisplayInfo> patternDisplayInfoList,
      required this.isRedPointOverPreview,
      required this.backgroundColor,
      required this.maskColor,
      required this.gridTypeIndex,
      required this.gridColor,
      required this.frameDrawPath,
      required this.frameColor,
      required this.needlePointerCenterOffset,
      required this.backgroundImage,
      required final List<ThreadMarkInfo> threadMarkInfoList,
      required this.isProjectorON,
      this.maskDisplayInfo,
      this.rotateRedPoint,
      required this.rotateCenter,
      this.dragAngle,
      this.quiltMask})
      : _patternDisplayInfoList = patternDisplayInfoList,
        _threadMarkInfoList = threadMarkInfoList;

  /// Pattern表示情報
  final List<PatternDisplayInfo> _patternDisplayInfoList;

  /// Pattern表示情報
  @override
  List<PatternDisplayInfo> get patternDisplayInfoList {
    if (_patternDisplayInfoList is EqualUnmodifiableListView)
      return _patternDisplayInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_patternDisplayInfoList);
  }

  /// maskボックスがPreview表示領域を超えていないか
  /// 超えた場合、赤いドットは表示されません
  @override
  final bool isRedPointOverPreview;

  /// セッティング画面　刺しゅう２　（ページ：9）
  /// 背景色
  @override
  final Color backgroundColor;

  /// 赤枠の色
  @override
  final Color maskColor;

  /// セッティング画面　刺しゅう１　（ページ：8）
  /// グリッド
  @override
  final int gridTypeIndex;

  /// グリッドグリッド
  @override
  final Color gridColor;

  /// 枠サイズ
  @override
  final Path frameDrawPath;

  /// 枠サイズ色
  @override
  final Color frameColor;

  /// 針数から針位置
  @override
  final Offset? needlePointerCenterOffset;

  /// 背景スキャンの画像
  @override
  final Widget? backgroundImage;

  /// ページ上の糸印の表示情報
  final List<ThreadMarkInfo> _threadMarkInfoList;

  /// ページ上の糸印の表示情報
  @override
  List<ThreadMarkInfo> get threadMarkInfoList {
    if (_threadMarkInfoList is EqualUnmodifiableListView)
      return _threadMarkInfoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_threadMarkInfoList);
  }

  /// 投影がオンになっているかどうか
  @override
  final bool isProjectorON;

  /// Mask ボックスの表示情報
  @override
  final Mask? maskDisplayInfo;

  /// Rotateドットの表示位置情報
  @override
  final RedPointDisplayInfo? rotateRedPoint;

  /// 回転の中心点
  @override
  final Offset rotateCenter;

  /// ドラッグまたは長押しで設定された角度
  @override
  final double? dragAngle;

  /// キルトマスク
  @override
  final Path? quiltMask;

  @override
  String toString() {
    return 'PreviewState(patternDisplayInfoList: $patternDisplayInfoList, isRedPointOverPreview: $isRedPointOverPreview, backgroundColor: $backgroundColor, maskColor: $maskColor, gridTypeIndex: $gridTypeIndex, gridColor: $gridColor, frameDrawPath: $frameDrawPath, frameColor: $frameColor, needlePointerCenterOffset: $needlePointerCenterOffset, backgroundImage: $backgroundImage, threadMarkInfoList: $threadMarkInfoList, isProjectorON: $isProjectorON, maskDisplayInfo: $maskDisplayInfo, rotateRedPoint: $rotateRedPoint, rotateCenter: $rotateCenter, dragAngle: $dragAngle, quiltMask: $quiltMask)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PreviewStateImpl &&
            const DeepCollectionEquality().equals(
                other._patternDisplayInfoList, _patternDisplayInfoList) &&
            (identical(other.isRedPointOverPreview, isRedPointOverPreview) ||
                other.isRedPointOverPreview == isRedPointOverPreview) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.maskColor, maskColor) ||
                other.maskColor == maskColor) &&
            (identical(other.gridTypeIndex, gridTypeIndex) ||
                other.gridTypeIndex == gridTypeIndex) &&
            (identical(other.gridColor, gridColor) ||
                other.gridColor == gridColor) &&
            (identical(other.frameDrawPath, frameDrawPath) ||
                other.frameDrawPath == frameDrawPath) &&
            (identical(other.frameColor, frameColor) ||
                other.frameColor == frameColor) &&
            (identical(other.needlePointerCenterOffset,
                    needlePointerCenterOffset) ||
                other.needlePointerCenterOffset == needlePointerCenterOffset) &&
            (identical(other.backgroundImage, backgroundImage) ||
                other.backgroundImage == backgroundImage) &&
            const DeepCollectionEquality()
                .equals(other._threadMarkInfoList, _threadMarkInfoList) &&
            (identical(other.isProjectorON, isProjectorON) ||
                other.isProjectorON == isProjectorON) &&
            const DeepCollectionEquality()
                .equals(other.maskDisplayInfo, maskDisplayInfo) &&
            const DeepCollectionEquality()
                .equals(other.rotateRedPoint, rotateRedPoint) &&
            (identical(other.rotateCenter, rotateCenter) ||
                other.rotateCenter == rotateCenter) &&
            (identical(other.dragAngle, dragAngle) ||
                other.dragAngle == dragAngle) &&
            (identical(other.quiltMask, quiltMask) ||
                other.quiltMask == quiltMask));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_patternDisplayInfoList),
      isRedPointOverPreview,
      backgroundColor,
      maskColor,
      gridTypeIndex,
      gridColor,
      frameDrawPath,
      frameColor,
      needlePointerCenterOffset,
      backgroundImage,
      const DeepCollectionEquality().hash(_threadMarkInfoList),
      isProjectorON,
      const DeepCollectionEquality().hash(maskDisplayInfo),
      const DeepCollectionEquality().hash(rotateRedPoint),
      rotateCenter,
      dragAngle,
      quiltMask);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PreviewStateImplCopyWith<_$PreviewStateImpl> get copyWith =>
      __$$PreviewStateImplCopyWithImpl<_$PreviewStateImpl>(this, _$identity);
}

abstract class _PreviewState implements PreviewState {
  const factory _PreviewState(
      {required final List<PatternDisplayInfo> patternDisplayInfoList,
      required final bool isRedPointOverPreview,
      required final Color backgroundColor,
      required final Color maskColor,
      required final int gridTypeIndex,
      required final Color gridColor,
      required final Path frameDrawPath,
      required final Color frameColor,
      required final Offset? needlePointerCenterOffset,
      required final Widget? backgroundImage,
      required final List<ThreadMarkInfo> threadMarkInfoList,
      required final bool isProjectorON,
      final Mask? maskDisplayInfo,
      final RedPointDisplayInfo? rotateRedPoint,
      required final Offset rotateCenter,
      final double? dragAngle,
      final Path? quiltMask}) = _$PreviewStateImpl;

  @override

  /// Pattern表示情報
  List<PatternDisplayInfo> get patternDisplayInfoList;
  @override

  /// maskボックスがPreview表示領域を超えていないか
  /// 超えた場合、赤いドットは表示されません
  bool get isRedPointOverPreview;
  @override

  /// セッティング画面　刺しゅう２　（ページ：9）
  /// 背景色
  Color get backgroundColor;
  @override

  /// 赤枠の色
  Color get maskColor;
  @override

  /// セッティング画面　刺しゅう１　（ページ：8）
  /// グリッド
  int get gridTypeIndex;
  @override

  /// グリッドグリッド
  Color get gridColor;
  @override

  /// 枠サイズ
  Path get frameDrawPath;
  @override

  /// 枠サイズ色
  Color get frameColor;
  @override

  /// 針数から針位置
  Offset? get needlePointerCenterOffset;
  @override

  /// 背景スキャンの画像
  Widget? get backgroundImage;
  @override

  /// ページ上の糸印の表示情報
  List<ThreadMarkInfo> get threadMarkInfoList;
  @override

  /// 投影がオンになっているかどうか
  bool get isProjectorON;
  @override

  /// Mask ボックスの表示情報
  Mask? get maskDisplayInfo;
  @override

  /// Rotateドットの表示位置情報
  RedPointDisplayInfo? get rotateRedPoint;
  @override

  /// 回転の中心点
  Offset get rotateCenter;
  @override

  /// ドラッグまたは長押しで設定された角度
  double? get dragAngle;
  @override

  /// キルトマスク
  Path? get quiltMask;
  @override
  @JsonKey(ignore: true)
  _$$PreviewStateImplCopyWith<_$PreviewStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
