import 'dart:async';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../model/pattern_image_capture_model.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../global_popup/global_popups/err_emb_too_much_selected_go_menu/err_emb_too_much_selected_go_menu_view_model.dart';
import '../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../model/pattern_data_reader/frame_image_reader.dart';
import '../../../../model/pattern_data_reader/pattern_data_base.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/redo_undo_model.dart';
import '../../../../model/select_information_model.dart';
import '../../../../model/select_model.dart';
import '../../../../model/thread_color_list_model.dart';
import '../../../page_route.dart';
import '../category_selector/category_selector_view_interface.dart';
import '../category_selector/category_selector_view_model.dart';
import 'frame_select_interface.dart';

final frameSelectViewModelProvider = StateNotifierProvider.autoDispose<
    FrameSelectViewModelInterface, FrameSelectInterface>(
  (ref) => FrameSelectViewModel(ref),
);

class FrameSelectViewModel extends FrameSelectViewModelInterface {
  FrameSelectViewModel(
    Ref ref,
  ) : super(
            FrameSelectInterface(
              selectCategoryIndex: 0,
              fourSFunctionOn: false,
              setButton: false,
              gridColor: PatternModel().getThumbnailBackgroundColor(),
            ),
            ref) {
    update();
  }

  static const invalidNum = -1;

  @override
  List<FrameImageGroup> getAllCategoryImagesInfo() =>
      FrameImageGroupReader().getAllFrameInfo();

  @override
  void onCategoryClick(int index) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// view更新
    state = state.copyWith(selectCategoryIndex: index);
  }

  @override
  void onPatternClick(int patternIndex, int categoryType) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// Model更新
    SelectModel().selectedPatternIndex =
        getAllCategoryImagesInfo()[state.selectCategoryIndex]
            .patternNumGroup[patternIndex];
    SelectModel().selectedPatternIndex =
        SelectModel().selectedPatternIndex == invalidNum
            ? patternIndex
            : SelectModel().selectedPatternIndex;
    SelectModel().selectedCategoryType = categoryType;

    assert(SelectModel().selectedCategoryType != null, '割り当てに失敗しました');
    assert(SelectModel().selectedPatternIndex != null, '割り当てに失敗しました');
    EmbLibraryError error = PatternModel().selectEmb(
      SelectModel().selectedPatternIndex!,
      SelectModel().selectedCategoryType!,
    );

    /// メモリーフル時の対応
    if (error == EmbLibraryError.EMB_DATAOVER_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
          arguments: ErrEmbTooMuchSelectedGoMenuArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
      return;
    }

    /// view更新
    state = state.copyWith(setButton: true);

    ref
        .read(categorySelectorViewModelProvider.notifier)
        .updateTopPageByChild(CategorySelectorModuleType.patternSelector);
  }

  @override
  void onReturnButtonClicked(BuildContext context) {
    /// Model更新
    PatternModel().deleteTemporaryPatternList();
    SelectInformationModel()
        .setTemporaryPatternAutoKind(CategoryType.unknownPattern);
    SelectModel()
      ..selectedCategoryType = null
      ..selectedPatternIndex = null;

    _closeSelf(context);
  }

  @override
  void onSetButtonClick(BuildContext context) {
    EmbLibraryError error = EmbLibrary().apiBinding.embGotoEdit();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    /// Model更新
    PatternModel()
      ..reloadAllPattern()
      ..clearTemporaryPatternList();
    SelectModel()
      ..selectedCategoryType = null
      ..selectedPatternIndex = null;
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }
    SelectInformationModel()
        .setTemporaryPatternAutoKind(CategoryType.unknownPattern);

    PagesRoute().pushNamedAndRemoveUntil(
        nextRoute: PageRouteEnum.patternEdit, untilRoute: PageRouteEnum.home);
  }

  ///
  /// ViewModel更新
  ///
  @override
  void update() {
    state = state.copyWith(
      gridColor: PatternModel().getThumbnailBackgroundColor(),
    );
  }

  @override
  void onEmbroideryButtonClicked(BuildContext context) {
    if (ThreadColorListModel().isNoThreadToSewing()) {
      return;
    }
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.sewing);
  }

  ///
  /// 自分を閉じる
  ///
  void _closeSelf(BuildContext context) {
    if (PopupNavigator.canPop(context: context)) {
      PopupNavigator.pop(context: context);
    }
  }

  ///
  /// プレビューのKey定義
  ///
  final GlobalKey<State<StatefulWidget>> _patternViewKey = GlobalKey();
  @override
  GlobalKey<State<StatefulWidget>> get patternViewKey => _patternViewKey;

  static const double patternListHeightDefault = 274;

  ///
  /// ファイルハンドリング関数
  ///
  @override
  Future<void> handleKeyPress(KeyEvent event) async {
    /// パターンイメージキャプチャモデルのインスタンス作成
    final model = PatternImageCaptureModel();

    /// キーイベントを処理
    model.handleKeyPress(event, () async {
      /// カテゴリイメージデータを取得
      final categoryImageData =
          getAllCategoryImagesInfo()[state.selectCategoryIndex];
      state = state.copyWith(
        fourSFunctionOn: true,
      );

      /// 1秒待つ
      await Future.delayed(const Duration(seconds: 1));

      /// パターンリストを画像として保存
      await model.savePatternListToImage(_patternViewKey);

      List<PatternFilterGroup>? patternFilterInfo;
      if (categoryImageData is PatternInfoGroupImplement) {
        patternFilterInfo =
            (categoryImageData as PatternInfoGroupImplement).patternFilterInfo;
      } else {
        patternFilterInfo = null;
      }

      /// スレッド情報をHTMLとして保存
      await model.saveThreadInfoToHtml(
        state.selectCategoryIndex,
        categoryImageData.categoryType,
        categoryImageData.iconsGroup,
        patternFilterInfo,
      );

      state = state.copyWith(
        fourSFunctionOn: false,
      );
    });
  }
}
