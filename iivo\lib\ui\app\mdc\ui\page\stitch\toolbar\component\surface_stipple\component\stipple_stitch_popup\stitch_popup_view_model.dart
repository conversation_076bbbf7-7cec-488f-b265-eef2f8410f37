import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_stipple_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'stitch_popup_view_interface.dart';

final stitchViewModelProvider = StateNotifierProvider.autoDispose<
    StitchPopupStateViewInterface,
    StitchPopupState>((ref) => StitchViewModel(ref));

class StitchViewModel extends StitchPopupStateViewInterface {
  StitchViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const StitchPopupState(
              isSelectSingle: false,
              isSelectTriple: false,
            ),
            ref) {
    update();
  }

  @override
  void update() {
    state = state.copyWith(
      isSelectSingle: _getSingleState(),
      isSelectTriple: _getTripleState(),
    );
  }

  @override
  StichLine get defaultValue => SurfaceStippleModel.stichLineTypes;

  ///
  /// スティッヒ線種を選択している
  ///
  bool _hasNoStichLineTypeSelected = SurfaceStippleModel().getStitchLine() !=
          SurfaceStippleModel.stitchNotUpdating
      ? false
      : true;

  ///
  /// stichLineType値
  ///
  StichLine _stichLineType = SurfaceStippleModel().getStitchLine();

  @override
  void onSingleButtonClick() {
    if (state.isSelectSingle) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    _hasNoStichLineTypeSelected = false;
    _stichLineType = StichLine.mdc_stichline_single;

    /// view更新
    state = state.copyWith(
      isSelectSingle: true,
      isSelectTriple: false,
    );
  }

  @override
  void onTripleButtonClick() {
    if (state.isSelectTriple) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    _hasNoStichLineTypeSelected = false;
    _stichLineType = StichLine.mdc_stichline_triple;

    /// view更新
    state = state.copyWith(
      isSelectSingle: false,
      isSelectTriple: true,
    );
  }

  ///
  /// ボタンの選択状態を取得
  ///
  bool _getSingleState() {
    if (_hasNoStichLineTypeSelected) {
      return false;
    }
    if (_stichLineType == defaultValue) {
      return true;
    } else {
      return false;
    }
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.surfaceStippleStitch.toString());
    if (_hasNoStichLineTypeSelected) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    StichLine stichLineType = SurfaceStippleModel().getStitchLine();

    /// Model更新
    SurfaceStippleModel().setStitchLine(_stichLineType);
    if (SurfaceStippleModel().setStippleSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    if (stichLineType != _stichLineType) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// ボタンの選択状態を取得
  ///
  bool _getTripleState() {
    if (_hasNoStichLineTypeSelected) {
      return false;
    }
    if (_stichLineType == defaultValue) {
      return false;
    } else {
      return true;
    }
  }
}
