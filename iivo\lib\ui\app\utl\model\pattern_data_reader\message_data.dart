import 'package:xd_component/xd_component.dart';

import '../../../../../model/app_locale.dart';

class AllMessage {
  Map<String, String> getAllMessage() {
    AppLocalizations l10n =
        lookupAppLocalizations(AppLocale().getCurrentLocale());

    Map<String, String> messageMap = {
      "color_001": l10n.color_001,
      "color_002": l10n.color_002,
      "color_003": l10n.color_003,
      "color_004": l10n.color_004,
      "color_005": l10n.color_005,
      "color_006": l10n.color_006,
      "color_007": l10n.color_007,
      "color_008": l10n.color_008,
      "color_009": l10n.color_009,
      "color_010": l10n.color_010,
      "color_011": l10n.color_011,
      "color_012": l10n.color_012,
      "color_013": l10n.color_013,
      "color_014": l10n.color_014,
      "color_015": l10n.color_015,
      "color_016": l10n.color_016,
      "color_017": l10n.color_017,
      "color_018": l10n.color_018,
      "color_019": l10n.color_019,
      "color_020": l10n.color_020,
      "color_021": l10n.color_021,
      "color_022": l10n.color_022,
      "color_023": l10n.color_023,
      "color_024": l10n.color_024,
      "color_025": l10n.color_025,
      "color_026": l10n.color_026,
      "color_027": l10n.color_027,
      "color_028": l10n.color_028,
      "color_029": l10n.color_029,
      "color_030": l10n.color_030,
      "color_031": l10n.color_031,
      "color_032": l10n.color_032,
      "color_033": l10n.color_033,
      "color_034": l10n.color_034,
      "color_035": l10n.color_035,
      "color_036": l10n.color_036,
      "color_037": l10n.color_037,
      "color_038": l10n.color_038,
      "color_039": l10n.color_039,
      "color_040": l10n.color_040,
      "color_041": l10n.color_041,
      "color_042": l10n.color_042,
      "color_043": l10n.color_043,
      "color_044": l10n.color_044,
      "color_045": l10n.color_045,
      "color_046": l10n.color_046,
      "color_047": l10n.color_047,
      "color_048": l10n.color_048,
      "color_049": l10n.color_049,
      "color_050": l10n.color_050,
      "color_051": l10n.color_051,
      "color_052": l10n.color_052,
      "color_053": l10n.color_053,
      "color_054": l10n.color_054,
      "color_055": l10n.color_055,
      "color_056": l10n.color_056,
      "color_057": l10n.color_057,
      "color_058": l10n.color_058,
      "color_059": l10n.color_059,
      "color_060": l10n.color_060,
      "color_061": l10n.color_061,
      "color_062": l10n.color_062,
      "color_063": l10n.color_063,
      "color_064": l10n.color_064,
      "color_065": l10n.color_065,
      "color_066": l10n.color_066,
      "color_067": l10n.color_067,
      "color_068": l10n.color_068,
      "color_069": l10n.color_069,
      "color_070": l10n.color_070,
      "color_071": l10n.color_071,
      "color_072": l10n.color_072,
      "color_073": l10n.color_073,
      "color_074": l10n.color_074,
      "color_075": l10n.color_075,
      "color_076": l10n.color_076,
      "color_077": l10n.color_077,
      "color_078": l10n.color_078,
      "color_079": l10n.color_079,
      "color_080": l10n.color_080,
      "color_081": l10n.color_081,
      "color_082": l10n.color_082,
      "color_083": l10n.color_083,
      "color_084": l10n.color_084,
      "color_085": l10n.color_085,
      "color_086": l10n.color_086,
      "color_087": l10n.color_087,
      "color_088": l10n.color_088,
      "color_089": l10n.color_089,
      "color_090": l10n.color_090,
      "color_091": l10n.color_091,
      "color_092": l10n.color_092,
      "color_093": l10n.color_093,
      "color_094": l10n.color_094,
      "color_095": l10n.color_095,
      "color_096": l10n.color_096,
      "color_097": l10n.color_097,
      "color_098": l10n.color_098,
      "color_099": l10n.color_099,
      "color_100": l10n.color_100,
      "color_101": l10n.color_101,
      "color_102": l10n.color_102,
      "color_103": l10n.color_103,
      "color_104": l10n.color_104,
      "color_105": l10n.color_105,
      "color_106": l10n.color_106,
      "color_107": l10n.color_107,
      "color_108": l10n.color_108,
      "color_109": l10n.color_109,
      "color_110": l10n.color_110,
      "color_111": l10n.color_111,
      "color_112": l10n.color_112,
      "color_113": l10n.color_113,
      "color_114": l10n.color_114,
      "color_115": l10n.color_115,
      "color_116": l10n.color_116,
      "color_117": l10n.color_117,
      "color_118": l10n.color_118,
      "color_119": l10n.color_119,
      "color_120": l10n.color_120,
      "color_121": l10n.color_121,
      "color_122": l10n.color_122,
      "color_123": l10n.color_123,
      "color_124": l10n.color_124,
      "color_125": l10n.color_125,
      "color_126": l10n.color_126,
      "color_127": l10n.color_127,
      "color_128": l10n.color_128,
      "color_129": l10n.color_129,
      "color_130": l10n.color_130,
      "color_131": l10n.color_131,
      "color_132": l10n.color_132,
      "color_133": l10n.color_133,
      "color_134": l10n.color_134,
      "color_135": l10n.color_135,
      "color_136": l10n.color_136,
      "color_137": l10n.color_137,
      "color_138": l10n.color_138,
      "color_139": l10n.color_139,
      "color_140": l10n.color_140,
      "color_141": l10n.color_141,
      "color_142": l10n.color_142,
      "color_143": l10n.color_143,
      "color_144": l10n.color_144,
      "color_145": l10n.color_145,
      "color_146": l10n.color_146,
      "color_147": l10n.color_147,
      "color_148": l10n.color_148,
      "color_149": l10n.color_149,
      "color_150": l10n.color_150,
      "color_151": l10n.color_151,
      "color_152": l10n.color_152,
      "color_153": l10n.color_153,
      "color_154": l10n.color_154,
      "color_155": l10n.color_155,
      "color_156": l10n.color_156,
      "color_157": l10n.color_157,
      "color_158": l10n.color_158,
      "color_159": l10n.color_159,
      "color_160": l10n.color_160,
      "color_161": l10n.color_161,
      "color_162": l10n.color_162,
      "color_163": l10n.color_163,
      "color_164": l10n.color_164,
      "color_165": l10n.color_165,
      "color_166": l10n.color_166,
      "color_167": l10n.color_167,
      "color_168": l10n.color_168,
      "color_169": l10n.color_169,
      "color_170": l10n.color_170,
      "color_171": l10n.color_171,
      "color_172": l10n.color_172,
      "color_173": l10n.color_173,
      "color_174": l10n.color_174,
      "color_175": l10n.color_175,
      "color_176": l10n.color_176,
      "color_177": l10n.color_177,
      "color_178": l10n.color_178,
      "color_179": l10n.color_179,
      "color_180": l10n.color_180,
      "color_181": l10n.color_181,
      "color_182": l10n.color_182,
      "color_183": l10n.color_183,
      "color_184": l10n.color_184,
      "color_185": l10n.color_185,
      "color_186": l10n.color_186,
      "color_187": l10n.color_187,
      "color_188": l10n.color_188,
      "color_189": l10n.color_189,
      "color_190": l10n.color_190,
      "color_191": l10n.color_191,
      "color_192": l10n.color_192,
      "color_193": l10n.color_193,
      "color_194": l10n.color_194,
      "color_195": l10n.color_195,
      "color_196": l10n.color_196,
      "color_197": l10n.color_197,
      "color_198": l10n.color_198,
      "color_199": l10n.color_199,
      "color_200": l10n.color_200,
      "color_201": l10n.color_201,
      "color_202": l10n.color_202,
      "color_203": l10n.color_203,
      "color_204": l10n.color_204,
      "color_205": l10n.color_205,
      "color_206": l10n.color_206,
      "color_207": l10n.color_207,
      "color_208": l10n.color_208,
      "color_209": l10n.color_209,
      "color_210": l10n.color_210,
      "color_211": l10n.color_211,
      "color_212": l10n.color_212,
      "color_213": l10n.color_213,
      "color_214": l10n.color_214,
      "color_215": l10n.color_215,
      "color_216": l10n.color_216,
      "color_217": l10n.color_217,
      "color_218": l10n.color_218,
      "color_219": l10n.color_219,
      "color_220": l10n.color_220,
      "color_221": l10n.color_221,
      "color_222": l10n.color_222,
      "color_223": l10n.color_223,
      "color_224": l10n.color_224,
      "color_225": l10n.color_225,
      "color_226": l10n.color_226,
      "color_227": l10n.color_227,
      "color_228": l10n.color_228,
      "color_229": l10n.color_229,
      "color_230": l10n.color_230,
      "color_231": l10n.color_231,
      "color_232": l10n.color_232,
      "color_233": l10n.color_233,
      "color_234": l10n.color_234,
      "color_235": l10n.color_235,
      "color_236": l10n.color_236,
      "color_237": l10n.color_237,
      "color_238": l10n.color_238,
      "color_239": l10n.color_239,
      "color_240": l10n.color_240,
      "color_241": l10n.color_241,
      "color_242": l10n.color_242,
      "color_243": l10n.color_243,
      "color_244": l10n.color_244,
      "color_245": l10n.color_245,
      "color_246": l10n.color_246,
      "color_247": l10n.color_247,
      "color_248": l10n.color_248,
      "color_249": l10n.color_249,
      "color_250": l10n.color_250,
      "color_251": l10n.color_251,
      "color_252": l10n.color_252,
      "color_253": l10n.color_253,
      "color_254": l10n.color_254,
      "color_255": l10n.color_255,
      "color_256": l10n.color_256,
      "color_257": l10n.color_257,
      "color_258": l10n.color_258,
      "color_259": l10n.color_259,
      "color_260": l10n.color_260,
      "color_261": l10n.color_261,
      "color_262": l10n.color_262,
      "color_263": l10n.color_263,
      "color_264": l10n.color_264,
      "color_265": l10n.color_265,
      "color_266": l10n.color_266,
      "color_267": l10n.color_267,
      "color_268": l10n.color_268,
      "color_269": l10n.color_269,
      "color_270": l10n.color_270,
      "color_271": l10n.color_271,
      "color_272": l10n.color_272,
      "color_273": l10n.color_273,
      "color_274": l10n.color_274,
      "color_275": l10n.color_275,
      "color_276": l10n.color_276,
      "color_277": l10n.color_277,
      "color_278": l10n.color_278,
      "color_279": l10n.color_279,
      "color_280": l10n.color_280,
      "color_281": l10n.color_281,
      "color_282": l10n.color_282,
      "color_283": l10n.color_283,
      "color_284": l10n.color_284,
      "color_285": l10n.color_285,
      "color_286": l10n.color_286,
      "color_287": l10n.color_287,
      "color_288": l10n.color_288,
      "color_289": l10n.color_289,
      "color_290": l10n.color_290,
      "color_291": l10n.color_291,
      "color_292": l10n.color_292,
      "color_293": l10n.color_293,
      "color_294": l10n.color_294,
      "color_295": l10n.color_295,
      "color_296": l10n.color_296,
      "color_297": l10n.color_297,
      "color_298": l10n.color_298,
      "color_299": l10n.color_299,
      "color_300": l10n.color_300,
      "color_301": l10n.color_301,
      "color_302": l10n.color_302,
      "color_303": l10n.color_303,
      "id_icon_test00001": l10n.id_icon_test00001,
      "icon_00002": l10n.icon_00002,
      "icon_00003_1": l10n.icon_00003_1,
      "icon_00006_3": l10n.icon_00006_3,
      "icon_00007_3": l10n.icon_00007_3,
      "icon_stitch": l10n.icon_stitch,
      "icon_close_1": l10n.icon_close_1,
      "icon_cancel": l10n.icon_cancel,
      "icon_ok": l10n.icon_ok,
      "icon_00011_zz": l10n.icon_00011_zz,
      "icon_00011": l10n.icon_00011,
      "icon_00012_zz": l10n.icon_00012_zz,
      "icon_reset_zz": l10n.icon_reset_zz,
      "icon_reset": l10n.icon_reset,
      "icon_00014_zz": l10n.icon_00014_zz,
      "icon_00014": l10n.icon_00014,
      "icon_save": l10n.icon_save,
      "icon_00015_zz": l10n.icon_00015_zz,
      "icon_00017_zz": l10n.icon_00017_zz,
      "icon_00018_zz": l10n.icon_00018_zz,
      "icon_00019_zz": l10n.icon_00019_zz,
      "icon_00020_zz": l10n.icon_00020_zz,
      "icon_00021_zz": l10n.icon_00021_zz,
      "icon_00022_zz": l10n.icon_00022_zz,
      "icon_00027_zz": l10n.icon_00027_zz,
      "icon_00028_zz": l10n.icon_00028_zz,
      "icon_00029_zz": l10n.icon_00029_zz,
      "icon_00038_zz": l10n.icon_00038_zz,
      "icon_00030_1": l10n.icon_00030_1,
      "icon_guidel_guideline": l10n.icon_guidel_guideline,
      "icon_guidel_main": l10n.icon_guidel_main,
      "icon_guidel_sub": l10n.icon_guidel_sub,
      "icon_guidel_mainline": l10n.icon_guidel_mainline,
      "icon_guidel_subline": l10n.icon_guidel_subline,
      "icon_guidel_linelength": l10n.icon_guidel_linelength,
      "icon_guidel_line_l": l10n.icon_guidel_line_l,
      "icon_guidel_line_m": l10n.icon_guidel_line_m,
      "icon_guidel_line_s": l10n.icon_guidel_line_s,
      "icon_guidel_color": l10n.icon_guidel_color,
      "icon_guidel_position": l10n.icon_guidel_position,
      "icon_guidel_main_pos": l10n.icon_guidel_main_pos,
      "icon_guidel_sub_pos": l10n.icon_guidel_sub_pos,
      "icon_guidel_gridsize": l10n.icon_guidel_gridsize,
      "icon_guidel_angle": l10n.icon_guidel_angle,
      "icon_guidel_seamallowance": l10n.icon_guidel_seamallowance,
      "icon_guidel_lengthl_zz": l10n.icon_guidel_lengthl_zz,
      "icon_guidel_lengthm_zz": l10n.icon_guidel_lengthm_zz,
      "icon_guidel_lengths_zz": l10n.icon_guidel_lengths_zz,
      "icon_position": l10n.icon_position,
      "icon_00031_2": l10n.icon_00031_2,
      "icon_00033_1": l10n.icon_00033_1,
      "icon_00035": l10n.icon_00035,
      "icon_return": l10n.icon_return,
      "icon_00038_1": l10n.icon_00038_1,
      "icon_00038_2": l10n.icon_00038_2,
      "icon_00039": l10n.icon_00039,
      "icon_00041_1": l10n.icon_00041_1,
      "icon_select": l10n.icon_select,
      "icon_select_2": l10n.icon_select_2,
      "icon_00041_2": l10n.icon_00041_2,
      "icon_00042": l10n.icon_00042,
      "icon_00046_zz": l10n.icon_00046_zz,
      "icon_00048": l10n.icon_00048,
      "icon_00049": l10n.icon_00049,
      "icon_00050": l10n.icon_00050,
      "icon_00051_zz": l10n.icon_00051_zz,
      "icon_00052_zz": l10n.icon_00052_zz,
      "icon_00053_b1": l10n.icon_00053_b1,
      "icon_00053_b2": l10n.icon_00053_b2,
      "icon_00053_t1": l10n.icon_00053_t1,
      "icon_00053_t2": l10n.icon_00053_t2,
      "icon_00055_1": l10n.icon_00055_1,
      "icon_00055_2": l10n.icon_00055_2,
      "icon_00056_1": l10n.icon_00056_1,
      "icon_00056_2": l10n.icon_00056_2,
      "icon_00057": l10n.icon_00057,
      "icon_00054": l10n.icon_00054,
      "icon_00058_1": l10n.icon_00058_1,
      "icon_00059": l10n.icon_00059,
      "icon_00060": l10n.icon_00060,
      "icon_00065": l10n.icon_00065,
      "icon_00066": l10n.icon_00066,
      "icon_00067_zz": l10n.icon_00067_zz,
      "icon_00068_zz": l10n.icon_00068_zz,
      "icon_00070_zz": l10n.icon_00070_zz,
      "icon_00071_zz": l10n.icon_00071_zz,
      "icon_00072": l10n.icon_00072,
      "icon_00075_zz": l10n.icon_00075_zz,
      "icon_00076_zz": l10n.icon_00076_zz,
      "icon_00077_zz": l10n.icon_00077_zz,
      "icon_00079": l10n.icon_00079,
      "icon_00080": l10n.icon_00080,
      "icon_prev": l10n.icon_prev,
      "icon_segment": l10n.icon_segment,
      "icon_00083": l10n.icon_00083,
      "icon_00084": l10n.icon_00084,
      "icon_00085": l10n.icon_00085,
      "icon_00088": l10n.icon_00088,
      "icon_00089": l10n.icon_00089,
      "icon_00090": l10n.icon_00090,
      "icon_00091_1": l10n.icon_00091_1,
      "icon_00091_2": l10n.icon_00091_2,
      "icon_00093_zz": l10n.icon_00093_zz,
      "icon_00094_zz": l10n.icon_00094_zz,
      "icon_00095": l10n.icon_00095,
      "icon_00096": l10n.icon_00096,
      "icon_resettodef": l10n.icon_resettodef,
      "icon_resettodefall": l10n.icon_resettodefall,
      "icon_resettodefall_2": l10n.icon_resettodefall_2,
      "icon_00100": l10n.icon_00100,
      "icon_00101_a": l10n.icon_00101_a,
      "icon_00101_b": l10n.icon_00101_b,
      "icon_00102": l10n.icon_00102,
      "icon_00103": l10n.icon_00103,
      "icon_00104": l10n.icon_00104,
      "icon_00105": l10n.icon_00105,
      "icon_00106": l10n.icon_00106,
      "icon_00107": l10n.icon_00107,
      "icon_00108": l10n.icon_00108,
      "icon_00109": l10n.icon_00109,
      "icon_00112": l10n.icon_00112,
      "icon_00114": l10n.icon_00114,
      "icon_00115": l10n.icon_00115,
      "icon_00116": l10n.icon_00116,
      "icon_00118": l10n.icon_00118,
      "icon_00119": l10n.icon_00119,
      "icon_00192": l10n.icon_00192,
      "icon_00121": l10n.icon_00121,
      "icon_00122": l10n.icon_00122,
      "icon_00123": l10n.icon_00123,
      "icon_00124": l10n.icon_00124,
      "icon_00125_1": l10n.icon_00125_1,
      "icon_00126_1": l10n.icon_00126_1,
      "icon_00127_1": l10n.icon_00127_1,
      "icon_00128_1": l10n.icon_00128_1,
      "icon_00129": l10n.icon_00129,
      "icon_00130_1": l10n.icon_00130_1,
      "icon_00131_1": l10n.icon_00131_1,
      "icon_00134": l10n.icon_00134,
      "icon_00135": l10n.icon_00135,
      "icon_00136_2": l10n.icon_00136_2,
      "icon_00137": l10n.icon_00137,
      "icon_00138": l10n.icon_00138,
      "icon_00140": l10n.icon_00140,
      "icon_00141": l10n.icon_00141,
      "icon_00142": l10n.icon_00142,
      "icon_00143": l10n.icon_00143,
      "icon_00144_a": l10n.icon_00144_a,
      "icon_00145": l10n.icon_00145,
      "icon_00146": l10n.icon_00146,
      "icon_00147": l10n.icon_00147,
      "icon_00243": l10n.icon_00243,
      "icon_00244": l10n.icon_00244,
      "icon_00148": l10n.icon_00148,
      "icon_00148_zz": l10n.icon_00148_zz,
      "icon_00150": l10n.icon_00150,
      "icon_00152_1": l10n.icon_00152_1,
      "icon_00155_1": l10n.icon_00155_1,
      "icon_00157": l10n.icon_00157,
      "icon_00159": l10n.icon_00159,
      "icon_00163_a": l10n.icon_00163_a,
      "icon_00163": l10n.icon_00163,
      "icon_00164": l10n.icon_00164,
      "icon_00165": l10n.icon_00165,
      "icon_00166": l10n.icon_00166,
      "icon_00167": l10n.icon_00167,
      "icon_00168": l10n.icon_00168,
      "icon_00178": l10n.icon_00178,
      "icon_00180": l10n.icon_00180,
      "icon_00182_1": l10n.icon_00182_1,
      "icon_00183_1": l10n.icon_00183_1,
      "icon_00184_1": l10n.icon_00184_1,
      "icon_00185": l10n.icon_00185,
      "icon_00186": l10n.icon_00186,
      "icon_00187": l10n.icon_00187,
      "icon_00191": l10n.icon_00191,
      "icon_00193": l10n.icon_00193,
      "icon_00194": l10n.icon_00194,
      "icon_00195": l10n.icon_00195,
      "icon_00196": l10n.icon_00196,
      "icon_00197": l10n.icon_00197,
      "icon_00199": l10n.icon_00199,
      "icon_00200": l10n.icon_00200,
      "icon_00201_1": l10n.icon_00201_1,
      "icon_00206_1": l10n.icon_00206_1,
      "icon_00207_a": l10n.icon_00207_a,
      "icon_00208": l10n.icon_00208,
      "icon_00209": l10n.icon_00209,
      "icon_00211": l10n.icon_00211,
      "icon_00212": l10n.icon_00212,
      "icon_00214": l10n.icon_00214,
      "icon_00218": l10n.icon_00218,
      "icon_00220": l10n.icon_00220,
      "icon_00222": l10n.icon_00222,
      "icon_00223": l10n.icon_00223,
      "icon_00224": l10n.icon_00224,
      "icon_00225": l10n.icon_00225,
      "icon_00226": l10n.icon_00226,
      "icon_on": l10n.icon_on,
      "icon_off": l10n.icon_off,
      "icon_00229": l10n.icon_00229,
      "icon_00230": l10n.icon_00230,
      "icon_00231": l10n.icon_00231,
      "icon_00232": l10n.icon_00232,
      "icon_00233": l10n.icon_00233,
      "icon_00234": l10n.icon_00234,
      "icon_00235": l10n.icon_00235,
      "icon_00236": l10n.icon_00236,
      "icon_00237": l10n.icon_00237,
      "icon_00238": l10n.icon_00238,
      "icon_00239": l10n.icon_00239,
      "icon_00240": l10n.icon_00240,
      "icon_00241": l10n.icon_00241,
      "icon_00242": l10n.icon_00242,
      "icon_00245": l10n.icon_00245,
      "icon_00246": l10n.icon_00246,
      "icon_00247": l10n.icon_00247,
      "icon_00248_zz": l10n.icon_00248_zz,
      "icon_00248": l10n.icon_00248,
      "icon_00251": l10n.icon_00251,
      "icon_00253": l10n.icon_00253,
      "icon_00254": l10n.icon_00254,
      "icon_00255": l10n.icon_00255,
      "icon_pointershape": l10n.icon_pointershape,
      "icon_00256": l10n.icon_00256,
      "icon_00257": l10n.icon_00257,
      "icon_recog_ok": l10n.icon_recog_ok,
      "icon_recog_ng": l10n.icon_recog_ng,
      "icon_00258": l10n.icon_00258,
      "icon_00259": l10n.icon_00259,
      "icon_00260": l10n.icon_00260,
      "icon_00261": l10n.icon_00261,
      "icon_00264": l10n.icon_00264,
      "icon_00265": l10n.icon_00265,
      "icon_00268": l10n.icon_00268,
      "icon_00269": l10n.icon_00269,
      "icon_00270": l10n.icon_00270,
      "icon_00271": l10n.icon_00271,
      "icon_00272": l10n.icon_00272,
      "icon_00273": l10n.icon_00273,
      "icon_00274": l10n.icon_00274,
      "icon_00275": l10n.icon_00275,
      "icon_00276": l10n.icon_00276,
      "icon_00277": l10n.icon_00277,
      "icon_00278": l10n.icon_00278,
      "icon_00279": l10n.icon_00279,
      "icon_00279_p": l10n.icon_00279_p,
      "icon_00280": l10n.icon_00280,
      "icon_00281": l10n.icon_00281,
      "icon_00282": l10n.icon_00282,
      "icon_00283": l10n.icon_00283,
      "icon_00284": l10n.icon_00284,
      "icon_00290": l10n.icon_00290,
      "icon_00291": l10n.icon_00291,
      "icon_00292": l10n.icon_00292,
      "icon_00293": l10n.icon_00293,
      "icon_00294": l10n.icon_00294,
      "icon_00295": l10n.icon_00295,
      "icon_00296": l10n.icon_00296,
      "icon_00297": l10n.icon_00297,
      "icon_00298": l10n.icon_00298,
      "icon_00299": l10n.icon_00299,
      "icon_00300": l10n.icon_00300,
      "icon_00301": l10n.icon_00301,
      "icon_00302": l10n.icon_00302,
      "icon_00303": l10n.icon_00303,
      "icon_pdf": l10n.icon_pdf,
      "icon_pdf_eula": l10n.icon_pdf_eula,
      "icon_pdf_sewing": l10n.icon_pdf_sewing,
      "icon_pdf_emb": l10n.icon_pdf_emb,
      "icon_pdf_sewing_ef": l10n.icon_pdf_sewing_ef,
      "icon_pdf_emb_ef": l10n.icon_pdf_emb_ef,
      "icon_pdf_sewing_t": l10n.icon_pdf_sewing_t,
      "icon_pdf_emb_t": l10n.icon_pdf_emb_t,
      "icon_f_omadendum": l10n.icon_f_omadendum,
      "icon_f_omadendum_ef": l10n.icon_f_omadendum_ef,
      "icon_f_omadendum_l": l10n.icon_f_omadendum_l,
      "icon_f_om_kit1": l10n.icon_f_om_kit1,
      "icon_f_om_kit2": l10n.icon_f_om_kit2,
      "icon_f_om_kit3": l10n.icon_f_om_kit3,
      "icon_f_om_kit1_l": l10n.icon_f_om_kit1_l,
      "icon_f_omadendum_t": l10n.icon_f_omadendum_t,
      "icon_f_om_kit1_t": l10n.icon_f_om_kit1_t,
      "icon_f_om_kit2_t": l10n.icon_f_om_kit2_t,
      "icon_f_om_kit3_t": l10n.icon_f_om_kit3_t,
      "icon_pdf_url_qr_t": l10n.icon_pdf_url_qr_t,
      "icon_nettool": l10n.icon_nettool,
      "icon_iagree": l10n.icon_iagree,
      "icon_terms_cancel": l10n.icon_terms_cancel,
      "icon_confirm": l10n.icon_confirm,
      "icon_00304": l10n.icon_00304,
      "icon_00305": l10n.icon_00305,
      "icon_00306": l10n.icon_00306,
      "icon_00307": l10n.icon_00307,
      "icon_00308": l10n.icon_00308,
      "icon_00309": l10n.icon_00309,
      "icon_00310": l10n.icon_00310,
      "icon_00311": l10n.icon_00311,
      "icon_00312": l10n.icon_00312,
      "icon_00313": l10n.icon_00313,
      "icon_00314": l10n.icon_00314,
      "icon_00315": l10n.icon_00315,
      "icon_00316": l10n.icon_00316,
      "icon_00317": l10n.icon_00317,
      "icon_00318": l10n.icon_00318,
      "icon_00320": l10n.icon_00320,
      "icon_00321": l10n.icon_00321,
      "icon_00322": l10n.icon_00322,
      "icon_00323": l10n.icon_00323,
      "icon_00325": l10n.icon_00325,
      "icon_00326": l10n.icon_00326,
      "icon_00331": l10n.icon_00331,
      "icon_00332": l10n.icon_00332,
      "icon_00333": l10n.icon_00333,
      "icon_00334": l10n.icon_00334,
      "icon_00335": l10n.icon_00335,
      "icon_00336": l10n.icon_00336,
      "icon_00337": l10n.icon_00337,
      "icon_00338": l10n.icon_00338,
      "icon_00339": l10n.icon_00339,
      "icon_00340": l10n.icon_00340,
      "icon_00341": l10n.icon_00341,
      "icon_00342": l10n.icon_00342,
      "icon_00343": l10n.icon_00343,
      "icon_00344": l10n.icon_00344,
      "icon_00345": l10n.icon_00345,
      "icon_00346": l10n.icon_00346,
      "icon_00347": l10n.icon_00347,
      "icon_00348": l10n.icon_00348,
      "icon_search": l10n.icon_search,
      "icon_00353": l10n.icon_00353,
      "icon_00354": l10n.icon_00354,
      "icon_00355": l10n.icon_00355,
      "icon_00356": l10n.icon_00356,
      "icon_00357": l10n.icon_00357,
      "icon_00358": l10n.icon_00358,
      "icon_00359": l10n.icon_00359,
      "icon_00360": l10n.icon_00360,
      "icon_00361": l10n.icon_00361,
      "icon_00362": l10n.icon_00362,
      "icon_00363": l10n.icon_00363,
      "icon_00364": l10n.icon_00364,
      "icon_00365": l10n.icon_00365,
      "icon_00366": l10n.icon_00366,
      "icon_00367": l10n.icon_00367,
      "icon_00368": l10n.icon_00368,
      "icon_00369": l10n.icon_00369,
      "icon_00370": l10n.icon_00370,
      "icon_00371": l10n.icon_00371,
      "icon_00372": l10n.icon_00372,
      "icon_00373": l10n.icon_00373,
      "icon_00374": l10n.icon_00374,
      "icon_00375": l10n.icon_00375,
      "icon_00376": l10n.icon_00376,
      "icon_00377": l10n.icon_00377,
      "icon_00378": l10n.icon_00378,
      "icon_00379": l10n.icon_00379,
      "icon_00380": l10n.icon_00380,
      "icon_00381": l10n.icon_00381,
      "icon_00382": l10n.icon_00382,
      "icon_00383": l10n.icon_00383,
      "icon_00384": l10n.icon_00384,
      "icon_00385": l10n.icon_00385,
      "icon_00386": l10n.icon_00386,
      "icon_00387": l10n.icon_00387,
      "icon_00388": l10n.icon_00388,
      "icon_00389": l10n.icon_00389,
      "icon_00390": l10n.icon_00390,
      "icon_00391": l10n.icon_00391,
      "icon_00392": l10n.icon_00392,
      "icon_00393": l10n.icon_00393,
      "icon_00394": l10n.icon_00394,
      "icon_00395": l10n.icon_00395,
      "icon_00396": l10n.icon_00396,
      "icon_00397": l10n.icon_00397,
      "icon_00398": l10n.icon_00398,
      "icon_00399": l10n.icon_00399,
      "icon_00400": l10n.icon_00400,
      "icon_00401": l10n.icon_00401,
      "icon_00402": l10n.icon_00402,
      "icon_00403": l10n.icon_00403,
      "icon_00404": l10n.icon_00404,
      "icon_00405": l10n.icon_00405,
      "icon_00406": l10n.icon_00406,
      "icon_00407": l10n.icon_00407,
      "icon_00408": l10n.icon_00408,
      "icon_00409": l10n.icon_00409,
      "icon_00410": l10n.icon_00410,
      "icon_00411": l10n.icon_00411,
      "icon_00412": l10n.icon_00412,
      "icon_00416": l10n.icon_00416,
      "icon_00417": l10n.icon_00417,
      "icon_00418": l10n.icon_00418,
      "icon_00419": l10n.icon_00419,
      "icon_00420": l10n.icon_00420,
      "icon_00421": l10n.icon_00421,
      "icon_00422": l10n.icon_00422,
      "icon_00423": l10n.icon_00423,
      "icon_00500": l10n.icon_00500,
      "icon_00500_2": l10n.icon_00500_2,
      "icon_iqdesigner": l10n.icon_iqdesigner,
      "icon_00501": l10n.icon_00501,
      "icon_00503_zz": l10n.icon_00503_zz,
      "icon_00505": l10n.icon_00505,
      "icon_imagescan": l10n.icon_imagescan,
      "icon_linedesign": l10n.icon_linedesign,
      "icon_illustrationdesign": l10n.icon_illustrationdesign,
      "icon_00509_zz": l10n.icon_00509_zz,
      "icon_00510": l10n.icon_00510,
      "icon_00511_1": l10n.icon_00511_1,
      "icon_00511_2": l10n.icon_00511_2,
      "icon_00512": l10n.icon_00512,
      "icon_00514": l10n.icon_00514,
      "icon_00516": l10n.icon_00516,
      "icon_00503": l10n.icon_00503,
      "icon_00518": l10n.icon_00518,
      "icon_00520": l10n.icon_00520,
      "icon_00521": l10n.icon_00521,
      "icon_00522": l10n.icon_00522,
      "icon_00523": l10n.icon_00523,
      "icon_00525": l10n.icon_00525,
      "icon_00526": l10n.icon_00526,
      "icon_00528": l10n.icon_00528,
      "icon_00529": l10n.icon_00529,
      "icon_00530": l10n.icon_00530,
      "icon_00533": l10n.icon_00533,
      "icon_00537": l10n.icon_00537,
      "icon_00538": l10n.icon_00538,
      "icon_00539": l10n.icon_00539,
      "icon_00540": l10n.icon_00540,
      "icon_00541": l10n.icon_00541,
      "icon_00544": l10n.icon_00544,
      "icon_00545": l10n.icon_00545,
      "icon_00547": l10n.icon_00547,
      "icon_00548_1": l10n.icon_00548_1,
      "icon_00548_2": l10n.icon_00548_2,
      "icon_00549_1": l10n.icon_00549_1,
      "icon_00549_2": l10n.icon_00549_2,
      "icon_00550": l10n.icon_00550,
      "icon_00551": l10n.icon_00551,
      "icon_00552": l10n.icon_00552,
      "icon_00553": l10n.icon_00553,
      "icon_00554": l10n.icon_00554,
      "icon_00555": l10n.icon_00555,
      "icon_00556": l10n.icon_00556,
      "icon_00557": l10n.icon_00557,
      "icon_00558": l10n.icon_00558,
      "icon_00559": l10n.icon_00559,
      "icon_00562": l10n.icon_00562,
      "icon_00564": l10n.icon_00564,
      "icon_00565": l10n.icon_00565,
      "icon_00566": l10n.icon_00566,
      "icon_inside": l10n.icon_inside,
      "icon_outside": l10n.icon_outside,
      "icon_00567": l10n.icon_00567,
      "icon_00568": l10n.icon_00568,
      "icon_00569": l10n.icon_00569,
      "icon_00570": l10n.icon_00570,
      "icon_frame_272_408_mm": l10n.icon_frame_272_408_mm,
      "icon_frame_272_408_inch": l10n.icon_frame_272_408_inch,
      "icon_frame_254_254_mm": l10n.icon_frame_254_254_mm,
      "icon_frame_254_254_inch": l10n.icon_frame_254_254_inch,
      "icon_frame_240_360_mm": l10n.icon_frame_240_360_mm,
      "icon_frame_240_360_inch": l10n.icon_frame_240_360_inch,
      "icon_frame_180_360_mm": l10n.icon_frame_180_360_mm,
      "icon_frame_180_360_inch": l10n.icon_frame_180_360_inch,
      "icon_frame_200_300_mm": l10n.icon_frame_200_300_mm,
      "icon_frame_200_300_inch": l10n.icon_frame_200_300_inch,
      "icon_frame_100_300_mm": l10n.icon_frame_100_300_mm,
      "icon_frame_100_300_inch": l10n.icon_frame_100_300_inch,
      "icon_frame_160_260_mm": l10n.icon_frame_160_260_mm,
      "icon_frame_160_260_inch": l10n.icon_frame_160_260_inch,
      "icon_frame_240_240_mm": l10n.icon_frame_240_240_mm,
      "icon_frame_240_240_inch": l10n.icon_frame_240_240_inch,
      "icon_frame_200_200_mm": l10n.icon_frame_200_200_mm,
      "icon_frame_200_200_inch": l10n.icon_frame_200_200_inch,
      "icon_frame_130_180_mm": l10n.icon_frame_130_180_mm,
      "icon_frame_130_180_inch": l10n.icon_frame_130_180_inch,
      "icon_frame_100_180_mm": l10n.icon_frame_100_180_mm,
      "icon_frame_100_180_inch": l10n.icon_frame_100_180_inch,
      "icon_frame_150_150_mm": l10n.icon_frame_150_150_mm,
      "icon_frame_150_150_inch": l10n.icon_frame_150_150_inch,
      "icon_frame_100_100_mm": l10n.icon_frame_100_100_mm,
      "icon_frame_100_100_inch": l10n.icon_frame_100_100_inch,
      "icon_frame_60_20_mm": l10n.icon_frame_60_20_mm,
      "icon_frame_60_20_inch": l10n.icon_frame_60_20_inch,
      "icon_zoom_50": l10n.icon_zoom_50,
      "icon_zoom_100": l10n.icon_zoom_100,
      "icon_zoom_125": l10n.icon_zoom_125,
      "icon_zoom_150": l10n.icon_zoom_150,
      "icon_zoom_200": l10n.icon_zoom_200,
      "icon_zoom_400": l10n.icon_zoom_400,
      "icon_zoom_800": l10n.icon_zoom_800,
      "icon_zoom_120": l10n.icon_zoom_120,
      "icon_zoom_240": l10n.icon_zoom_240,
      "icon_zoom_480": l10n.icon_zoom_480,
      "icon_zoom_960": l10n.icon_zoom_960,
      "icon_00600": l10n.icon_00600,
      "icon_00600_1": l10n.icon_00600_1,
      "icon_00601": l10n.icon_00601,
      "icon_00602": l10n.icon_00602,
      "icon_00603": l10n.icon_00603,
      "icon_00604": l10n.icon_00604,
      "icon_00605": l10n.icon_00605,
      "icon_00606": l10n.icon_00606,
      "icon_00608": l10n.icon_00608,
      "icon_00608_1": l10n.icon_00608_1,
      "icon_00609": l10n.icon_00609,
      "icon_00609_1": l10n.icon_00609_1,
      "icon_00610": l10n.icon_00610,
      "icon_00630": l10n.icon_00630,
      "icon_00631": l10n.icon_00631,
      "icon_00631_1": l10n.icon_00631_1,
      "icon_00632": l10n.icon_00632,
      "icon_00633": l10n.icon_00633,
      "icon_00634": l10n.icon_00634,
      "icon_00635": l10n.icon_00635,
      "icon_00636": l10n.icon_00636,
      "icon_00637": l10n.icon_00637,
      "icon_00638": l10n.icon_00638,
      "icon_00639": l10n.icon_00639,
      "icon_00640": l10n.icon_00640,
      "icon_00641": l10n.icon_00641,
      "icon_00642": l10n.icon_00642,
      "icon_00643": l10n.icon_00643,
      "icon_00643_s": l10n.icon_00643_s,
      "icon_00644": l10n.icon_00644,
      "icon_00645": l10n.icon_00645,
      "icon_00646": l10n.icon_00646,
      "icon_00647": l10n.icon_00647,
      "icon_00648": l10n.icon_00648,
      "icon_00649": l10n.icon_00649,
      "icon_00650": l10n.icon_00650,
      "icon_00651": l10n.icon_00651,
      "icon_00652": l10n.icon_00652,
      "icon_00653": l10n.icon_00653,
      "icon_00654": l10n.icon_00654,
      "icon_00655": l10n.icon_00655,
      "icon_00656": l10n.icon_00656,
      "icon_00656_p": l10n.icon_00656_p,
      "icon_00656_s": l10n.icon_00656_s,
      "icon_00657": l10n.icon_00657,
      "icon_00658": l10n.icon_00658,
      "icon_00659": l10n.icon_00659,
      "icon_00660": l10n.icon_00660,
      "icon_00661": l10n.icon_00661,
      "icon_00662": l10n.icon_00662,
      "icon_00663": l10n.icon_00663,
      "icon_00664": l10n.icon_00664,
      "icon_00665": l10n.icon_00665,
      "icon_00666": l10n.icon_00666,
      "icon_00667": l10n.icon_00667,
      "icon_00668": l10n.icon_00668,
      "icon_00669": l10n.icon_00669,
      "icon_00670": l10n.icon_00670,
      "icon_00671": l10n.icon_00671,
      "icon_00672": l10n.icon_00672,
      "icon_00673": l10n.icon_00673,
      "icon_00674": l10n.icon_00674,
      "icon_00674_a": l10n.icon_00674_a,
      "icon_00674_c": l10n.icon_00674_c,
      "icon_00675": l10n.icon_00675,
      "icon_00676": l10n.icon_00676,
      "icon_00677": l10n.icon_00677,
      "icon_00678": l10n.icon_00678,
      "icon_00679": l10n.icon_00679,
      "icon_00680": l10n.icon_00680,
      "icon_00681": l10n.icon_00681,
      "icon_00682": l10n.icon_00682,
      "icon_cert_key": l10n.icon_cert_key,
      "icon_cert_web": l10n.icon_cert_web,
      "icon_status_t": l10n.icon_status_t,
      "icon_status_a1": l10n.icon_status_a1,
      "icon_status_a2": l10n.icon_status_a2,
      "icon_status_a3": l10n.icon_status_a3,
      "icon_status_a4": l10n.icon_status_a4,
      "icon_status_b1": l10n.icon_status_b1,
      "icon_status_b2": l10n.icon_status_b2,
      "icon_status_b3": l10n.icon_status_b3,
      "icon_cancel_downloading": l10n.icon_cancel_downloading,
      "icon_pause_downloading2": l10n.icon_pause_downloading2,
      "icon_status_c1": l10n.icon_status_c1,
      "icon_status_c2": l10n.icon_status_c2,
      "icon_app_dl_moniter": l10n.icon_app_dl_moniter,
      "icon_shape": l10n.icon_shape,
      "icon_favorite": l10n.icon_favorite,
      "icon_sash_4section": l10n.icon_sash_4section,
      "icon_sash_1direction": l10n.icon_sash_1direction,
      "icon_sash_1dtotal": l10n.icon_sash_1dtotal,
      "icon_offset": l10n.icon_offset,
      "icon_startpoint": l10n.icon_startpoint,
      "icon_endpoint": l10n.icon_endpoint,
      "icon_embfootdwn": l10n.icon_embfootdwn,
      "icon_frame_272_272_mm": l10n.icon_frame_272_272_mm,
      "icon_frame_272_272_inch": l10n.icon_frame_272_272_inch,
      "icon_appguide_w": l10n.icon_appguide_w,
      "icon_appguide": l10n.icon_appguide,
      "icon_mobileapp": l10n.icon_mobileapp,
      "icon_app": l10n.icon_app,
      "icon_emb1": l10n.icon_emb1,
      "icon_emb2": l10n.icon_emb2,
      "icon_00185_2": l10n.icon_00185_2,
      "icon_typea": l10n.icon_typea,
      "icon_typeb": l10n.icon_typeb,
      "icon_typec": l10n.icon_typec,
      "icon_sash_typesplit": l10n.icon_sash_typesplit,
      "icon_mystitchmonitor": l10n.icon_mystitchmonitor,
      "icon_mydesignsnap": l10n.icon_mydesignsnap,
      "icon_mystitchmonitor_t": l10n.icon_mystitchmonitor_t,
      "icon_mydesignsnap_t": l10n.icon_mydesignsnap_t,
      "icon_actcode": l10n.icon_actcode,
      "icon_machineno": l10n.icon_machineno,
      "icon_autodl": l10n.icon_autodl,
      "icon_updatemanu": l10n.icon_updatemanu,
      "icon_dl_updateprogram": l10n.icon_dl_updateprogram,
      "icon_dl_updateprogram_2": l10n.icon_dl_updateprogram_2,
      "icon_pause": l10n.icon_pause,
      "icon_resume": l10n.icon_resume,
      "icon_cert_method": l10n.icon_cert_method,
      "icon_latestver": l10n.icon_latestver,
      "icon_device_ios": l10n.icon_device_ios,
      "icon_device_android": l10n.icon_device_android,
      "icon_f_ios": l10n.icon_f_ios,
      "icon_f_android": l10n.icon_f_android,
      "icon_cws_myconnection": l10n.icon_cws_myconnection,
      "icon_step1": l10n.icon_step1,
      "icon_step2": l10n.icon_step2,
      "icon_step3": l10n.icon_step3,
      "icon_step4": l10n.icon_step4,
      "icon_step5": l10n.icon_step5,
      "icon_register": l10n.icon_register,
      "icon_loginid": l10n.icon_loginid,
      "icon_id": l10n.icon_id,
      "icon_appq1": l10n.icon_appq1,
      "icon_appq2": l10n.icon_appq2,
      "icon_original_img": l10n.icon_original_img,
      "icon_appq_stitch_1": l10n.icon_appq_stitch_1,
      "icon_appq_stitch_2": l10n.icon_appq_stitch_2,
      "icon_appq_stitch_3": l10n.icon_appq_stitch_3,
      "icon_stamp_web": l10n.icon_stamp_web,
      "icon_cws_rgs_title": l10n.icon_cws_rgs_title,
      "icon_cws_rgs_s1": l10n.icon_cws_rgs_s1,
      "icon_cws_rgs_s2": l10n.icon_cws_rgs_s2,
      "icon_cws_rgs_s3": l10n.icon_cws_rgs_s3,
      "icon_cws_rgs_s4": l10n.icon_cws_rgs_s4,
      "icon_cws_rgs_pin": l10n.icon_cws_rgs_pin,
      "icon_pincode": l10n.icon_pincode,
      "icon_kitsnc": l10n.icon_kitsnc,
      "icon_snc1": l10n.icon_snc1,
      "icon_f_om_kitsnc": l10n.icon_f_om_kitsnc,
      "icon_density_mm": l10n.icon_density_mm,
      "icon_density_inch": l10n.icon_density_inch,
      "icon_machineregist": l10n.icon_machineregist,
      "icon_snj_myconnection": l10n.icon_snj_myconnection,
      "icon_snj_rgs_title": l10n.icon_snj_rgs_title,
      "icon_snj_rgs_s2": l10n.icon_snj_rgs_s2,
      "icon_snj_rgs_s3": l10n.icon_snj_rgs_s3,
      "icon_snj_rgs_pin": l10n.icon_snj_rgs_pin,
      "icon_transfer": l10n.icon_transfer,
      "icon_app_selectcolor": l10n.icon_app_selectcolor,
      "icon_texture": l10n.icon_texture,
      "icon_userthread": l10n.icon_userthread,
      "icon_senju": l10n.icon_senju,
      "icon_notnow": l10n.icon_notnow,
      "icon_builtin": l10n.icon_builtin,
      "icon_user": l10n.icon_user,
      "icon_clearall": l10n.icon_clearall,
      "icon_taperingtitle": l10n.icon_taperingtitle,
      "icon_tapering01": l10n.icon_tapering01,
      "icon_tapering02": l10n.icon_tapering02,
      "icon_tapering03": l10n.icon_tapering03,
      "icon_tapering04": l10n.icon_tapering04,
      "icon_tapering05": l10n.icon_tapering05,
      "icon_tapering06": l10n.icon_tapering06,
      "icon_tapering06_s": l10n.icon_tapering06_s,
      "icon_times": l10n.icon_times,
      "icon_approx_s": l10n.icon_approx_s,
      "icon_e2etitle": l10n.icon_e2etitle,
      "icon_e2e01": l10n.icon_e2e01,
      "icon_e2e01_2": l10n.icon_e2e01_2,
      "icon_e2e02": l10n.icon_e2e02,
      "icon_e2e03": l10n.icon_e2e03,
      "t_err01": l10n.t_err01,
      "t_err02": l10n.t_err02,
      "t_err02_emb": l10n.t_err02_emb,
      "t_err03": l10n.t_err03,
      "t_err04": l10n.t_err04,
      "t_err05": l10n.t_err05,
      "t_err06": l10n.t_err06,
      "t_err07": l10n.t_err07,
      "t_err07_u": l10n.t_err07_u,
      "t_err08": l10n.t_err08,
      "t_err09": l10n.t_err09,
      "t_err10": l10n.t_err10,
      "t_err11": l10n.t_err11,
      "t_err12": l10n.t_err12,
      "t_err13": l10n.t_err13,
      "t_err15": l10n.t_err15,
      "t_err16": l10n.t_err16,
      "t_err16_e": l10n.t_err16_e,
      "t_err16_u": l10n.t_err16_u,
      "t_err17": l10n.t_err17,
      "t_err18": l10n.t_err18,
      "t_err19": l10n.t_err19,
      "t_err22": l10n.t_err22,
      "t_err22_u": l10n.t_err22_u,
      "t_err23": l10n.t_err23,
      "t_err24": l10n.t_err24,
      "t_err25": l10n.t_err25,
      "t_err26": l10n.t_err26,
      "t_err27": l10n.t_err27,
      "t_err27_d": l10n.t_err27_d,
      "t_err61": l10n.t_err61,
      "t_err61_d": l10n.t_err61_d,
      "t_err61_dd": l10n.t_err61_dd,
      "t_err28": l10n.t_err28,
      "t_err28_d": l10n.t_err28_d,
      "t_err29": l10n.t_err29,
      "t_err65": l10n.t_err65,
      "t_err29_d": l10n.t_err29_d,
      "t_err30": l10n.t_err30,
      "t_err33": l10n.t_err33,
      "t_err34": l10n.t_err34,
      "t_err36": l10n.t_err36,
      "t_err37": l10n.t_err37,
      "t_err38": l10n.t_err38,
      "t_err_twinn_10": l10n.t_err_twinn_10,
      "t_err_twinn_11": l10n.t_err_twinn_11,
      "t_err_twinn_12": l10n.t_err_twinn_12,
      "t_err42": l10n.t_err42,
      "t_err48": l10n.t_err48,
      "t_err50": l10n.t_err50,
      "t_err53": l10n.t_err53,
      "t_err55": l10n.t_err55,
      "t_err56": l10n.t_err56,
      "t_err63": l10n.t_err63,
      "t_err64": l10n.t_err64,
      "t_err69": l10n.t_err69,
      "t_err76": l10n.t_err76,
      "t_err77": l10n.t_err77,
      "t_err82": l10n.t_err82,
      "t_err83": l10n.t_err83,
      "t_err84": l10n.t_err84,
      "t_err88": l10n.t_err88,
      "t_err89": l10n.t_err89,
      "t_err90": l10n.t_err90,
      "t_err93": l10n.t_err93,
      "t_err94": l10n.t_err94,
      "t_err94_cmn": l10n.t_err94_cmn,
      "t_err95": l10n.t_err95,
      "t_err95_cmn": l10n.t_err95_cmn,
      "t_err96": l10n.t_err96,
      "t_err96_cmn": l10n.t_err96_cmn,
      "t_err97": l10n.t_err97,
      "t_err97_cmn": l10n.t_err97_cmn,
      "t_err98": l10n.t_err98,
      "t_err98_cmn": l10n.t_err98_cmn,
      "t_err99": l10n.t_err99,
      "t_err100": l10n.t_err100,
      "t_err101": l10n.t_err101,
      "t_err101_cmn": l10n.t_err101_cmn,
      "t_err103": l10n.t_err103,
      "t_err104": l10n.t_err104,
      "t_err106": l10n.t_err106,
      "t_err107": l10n.t_err107,
      "t_err108": l10n.t_err108,
      "t_err109": l10n.t_err109,
      "t_err110": l10n.t_err110,
      "t_err111": l10n.t_err111,
      "t_err113": l10n.t_err113,
      "t_err116": l10n.t_err116,
      "t_err117": l10n.t_err117,
      "t_err118": l10n.t_err118,
      "t_err119": l10n.t_err119,
      "t_err120": l10n.t_err120,
      "t_err120_2": l10n.t_err120_2,
      "t_err121": l10n.t_err121,
      "t_err122": l10n.t_err122,
      "t_err122_cmn": l10n.t_err122_cmn,
      "t_err123": l10n.t_err123,
      "t_err124": l10n.t_err124,
      "t_err125": l10n.t_err125,
      "t_err126": l10n.t_err126,
      "t_err127": l10n.t_err127,
      "t_err128": l10n.t_err128,
      "t_err129": l10n.t_err129,
      "t_err130": l10n.t_err130,
      "t_err131": l10n.t_err131,
      "t_err132": l10n.t_err132,
      "t_err133": l10n.t_err133,
      "t_err134": l10n.t_err134,
      "t_err136": l10n.t_err136,
      "t_err137": l10n.t_err137,
      "t_err208": l10n.t_err208,
      "t_err209": l10n.t_err209,
      "t_err210": l10n.t_err210,
      "t_err213": l10n.t_err213,
      "t_err215": l10n.t_err215,
      "t_err224": l10n.t_err224,
      "t_err227": l10n.t_err227,
      "t_err228": l10n.t_err228,
      "t_err229": l10n.t_err229,
      "t_err229_b": l10n.t_err229_b,
      "t_err239": l10n.t_err239,
      "t_err241": l10n.t_err241,
      "t_err242": l10n.t_err242,
      "t_err244": l10n.t_err244,
      "t_err245": l10n.t_err245,
      "t_err246": l10n.t_err246,
      "t_err247": l10n.t_err247,
      "t_err248": l10n.t_err248,
      "t_err249": l10n.t_err249,
      "t_err250": l10n.t_err250,
      "t_err251": l10n.t_err251,
      "t_err252": l10n.t_err252,
      "t_err253": l10n.t_err253,
      "t_err254": l10n.t_err254,
      "t_err255": l10n.t_err255,
      "t_err256": l10n.t_err256,
      "t_err257": l10n.t_err257,
      "t_err257_1": l10n.t_err257_1,
      "t_err257_2": l10n.t_err257_2,
      "t_err259": l10n.t_err259,
      "t_err260": l10n.t_err260,
      "t_err261": l10n.t_err261,
      "t_err262": l10n.t_err262,
      "t_err263": l10n.t_err263,
      "t_err264": l10n.t_err264,
      "t_err265": l10n.t_err265,
      "t_err343": l10n.t_err343,
      "t_err266": l10n.t_err266,
      "t_err270": l10n.t_err270,
      "t_err271": l10n.t_err271,
      "t_err273": l10n.t_err273,
      "t_err274": l10n.t_err274,
      "t_err276": l10n.t_err276,
      "t_err277": l10n.t_err277,
      "t_err278": l10n.t_err278,
      "t_err279": l10n.t_err279,
      "t_err282": l10n.t_err282,
      "t_err283": l10n.t_err283,
      "t_err284": l10n.t_err284,
      "t_err286": l10n.t_err286,
      "t_err288": l10n.t_err288,
      "t_err290": l10n.t_err290,
      "t_err291": l10n.t_err291,
      "t_err291_cmn": l10n.t_err291_cmn,
      "t_err292": l10n.t_err292,
      "t_err292_s": l10n.t_err292_s,
      "t_err297": l10n.t_err297,
      "t_err298": l10n.t_err298,
      "t_err_cameracalib_ng_msg": l10n.t_err_cameracalib_ng_msg,
      "t_err_cameracalib_ok_msg": l10n.t_err_cameracalib_ok_msg,
      "t_err299": l10n.t_err299,
      "t_err300": l10n.t_err300,
      "t_err_cameracalib_title": l10n.t_err_cameracalib_title,
      "t_err_cameracalib_1_4": l10n.t_err_cameracalib_1_4,
      "t_err303": l10n.t_err303,
      "t_err304": l10n.t_err304,
      "t_err307": l10n.t_err307,
      "t_err308": l10n.t_err308,
      "t_err309": l10n.t_err309,
      "t_err310": l10n.t_err310,
      "t_err311": l10n.t_err311,
      "t_err311_size": l10n.t_err311_size,
      "t_err311_rehoop": l10n.t_err311_rehoop,
      "t_err312": l10n.t_err312,
      "t_err313": l10n.t_err313,
      "t_err314": l10n.t_err314,
      "t_err354": l10n.t_err354,
      "t_err356": l10n.t_err356,
      "t_err359": l10n.t_err359,
      "t_err360": l10n.t_err360,
      "t_err361": l10n.t_err361,
      "t_err362": l10n.t_err362,
      "t_err364": l10n.t_err364,
      "t_err373": l10n.t_err373,
      "t_err380": l10n.t_err380,
      "t_err381": l10n.t_err381,
      "t_err382": l10n.t_err382,
      "t_err383": l10n.t_err383,
      "t_err384": l10n.t_err384,
      "t_err385": l10n.t_err385,
      "t_err386": l10n.t_err386,
      "t_err390": l10n.t_err390,
      "t_err390_old": l10n.t_err390_old,
      "t_err391": l10n.t_err391,
      "t_err391_u": l10n.t_err391_u,
      "t_err392": l10n.t_err392,
      "t_err393": l10n.t_err393,
      "t_err394": l10n.t_err394,
      "t_err395": l10n.t_err395,
      "t_err396": l10n.t_err396,
      "t_err397": l10n.t_err397,
      "t_err398": l10n.t_err398,
      "t_err400": l10n.t_err400,
      "t_err401": l10n.t_err401,
      "t_err402": l10n.t_err402,
      "t_err403": l10n.t_err403,
      "t_err404": l10n.t_err404,
      "t_err405": l10n.t_err405,
      "t_err406": l10n.t_err406,
      "t_err408": l10n.t_err408,
      "t_err410": l10n.t_err410,
      "t_err411": l10n.t_err411,
      "t_err412": l10n.t_err412,
      "t_err413": l10n.t_err413,
      "t_err414": l10n.t_err414,
      "t_err415": l10n.t_err415,
      "t_err416": l10n.t_err416,
      "t_err417": l10n.t_err417,
      "t_err418": l10n.t_err418,
      "t_err419": l10n.t_err419,
      "t_err420": l10n.t_err420,
      "t_err421": l10n.t_err421,
      "t_err422": l10n.t_err422,
      "t_err423": l10n.t_err423,
      "t_err424": l10n.t_err424,
      "t_err425": l10n.t_err425,
      "t_err426": l10n.t_err426,
      "t_err428": l10n.t_err428,
      "t_err429": l10n.t_err429,
      "t_err430": l10n.t_err430,
      "t_err433": l10n.t_err433,
      "t_err440": l10n.t_err440,
      "t_err445": l10n.t_err445,
      "t_err446": l10n.t_err446,
      "t_err447": l10n.t_err447,
      "t_err448": l10n.t_err448,
      "t_err451": l10n.t_err451,
      "t_err452": l10n.t_err452,
      "t_err453": l10n.t_err453,
      "t_err454": l10n.t_err454,
      "t_err455": l10n.t_err455,
      "t_err457": l10n.t_err457,
      "t_err458": l10n.t_err458,
      "t_err459": l10n.t_err459,
      "t_err460": l10n.t_err460,
      "t_err461": l10n.t_err461,
      "t_err463": l10n.t_err463,
      "t_err464": l10n.t_err464,
      "t_err465": l10n.t_err465,
      "t_err466": l10n.t_err466,
      "t_err467": l10n.t_err467,
      "t_err468": l10n.t_err468,
      "t_err469": l10n.t_err469,
      "t_err470": l10n.t_err470,
      "t_err471": l10n.t_err471,
      "t_err472": l10n.t_err472,
      "t_err473": l10n.t_err473,
      "t_err474": l10n.t_err474,
      "t_err475": l10n.t_err475,
      "t_err478": l10n.t_err478,
      "t_err478_tc": l10n.t_err478_tc,
      "t_err479": l10n.t_err479,
      "t_err480": l10n.t_err480,
      "t_err481": l10n.t_err481,
      "t_err482": l10n.t_err482,
      "t_err483": l10n.t_err483,
      "t_err484_old": l10n.t_err484_old,
      "t_err484": l10n.t_err484,
      "t_err485": l10n.t_err485,
      "t_err486": l10n.t_err486,
      "t_err489": l10n.t_err489,
      "t_err496": l10n.t_err496,
      "t_err497": l10n.t_err497,
      "t_err501": l10n.t_err501,
      "t_err502": l10n.t_err502,
      "t_err503": l10n.t_err503,
      "t_err503_new": l10n.t_err503_new,
      "t_err504": l10n.t_err504,
      "t_err509": l10n.t_err509,
      "t_err505": l10n.t_err505,
      "t_err506": l10n.t_err506,
      "t_err508": l10n.t_err508,
      "t_err507": l10n.t_err507,
      "t_err510": l10n.t_err510,
      "t_err511": l10n.t_err511,
      "t_err515": l10n.t_err515,
      "t_err516": l10n.t_err516,
      "t_err517": l10n.t_err517,
      "t_err518": l10n.t_err518,
      "t_err519": l10n.t_err519,
      "t_err520": l10n.t_err520,
      "t_err_521": l10n.t_err_521,
      "t_err574": l10n.t_err574,
      "t_err575": l10n.t_err575,
      "t_err577": l10n.t_err577,
      "t_err578": l10n.t_err578,
      "t_err581": l10n.t_err581,
      "t_err581_b": l10n.t_err581_b,
      "t_err582": l10n.t_err582,
      "t_err582_n": l10n.t_err582_n,
      "t_err582_e": l10n.t_err582_e,
      "t_err583": l10n.t_err583,
      "t_err583_e": l10n.t_err583_e,
      "t_err584": l10n.t_err584,
      "t_err584_e": l10n.t_err584_e,
      "t_err585": l10n.t_err585,
      "t_err586": l10n.t_err586,
      "t_err586_b": l10n.t_err586_b,
      "t_err586_e": l10n.t_err586_e,
      "t_err587": l10n.t_err587,
      "t_err588": l10n.t_err588,
      "t_err588_e": l10n.t_err588_e,
      "t_err588_e_2": l10n.t_err588_e_2,
      "t_err590": l10n.t_err590,
      "t_err591": l10n.t_err591,
      "t_err_dl_updateprogram2": l10n.t_err_dl_updateprogram2,
      "t_err592": l10n.t_err592,
      "t_err593": l10n.t_err593,
      "t_err594": l10n.t_err594,
      "t_err_dl_updateprogram": l10n.t_err_dl_updateprogram,
      "t_err_networkconnectionerr": l10n.t_err_networkconnectionerr,
      "t_err_not_turnoff": l10n.t_err_not_turnoff,
      "t_err_pressresume_continuedl": l10n.t_err_pressresume_continuedl,
      "t_err_updateformovie": l10n.t_err_updateformovie,
      "t_err595": l10n.t_err595,
      "t_err596": l10n.t_err596,
      "t_err597": l10n.t_err597,
      "t_err598": l10n.t_err598,
      "t_err599": l10n.t_err599,
      "t_err599_used": l10n.t_err599_used,
      "t_err601": l10n.t_err601,
      "t_err602": l10n.t_err602,
      "t_err603": l10n.t_err603,
      "t_err604": l10n.t_err604,
      "t_err605": l10n.t_err605,
      "t_err606": l10n.t_err606,
      "t_err607": l10n.t_err607,
      "t_err608": l10n.t_err608,
      "t_err609": l10n.t_err609,
      "t_err611": l10n.t_err611,
      "t_err612": l10n.t_err612,
      "t_err613": l10n.t_err613,
      "t_err614": l10n.t_err614,
      "t_err615": l10n.t_err615,
      "t_err616": l10n.t_err616,
      "t_err617": l10n.t_err617,
      "t_err618": l10n.t_err618,
      "t_err620": l10n.t_err620,
      "t_err621": l10n.t_err621,
      "t_err622": l10n.t_err622,
      "t_err623": l10n.t_err623,
      "t_err624": l10n.t_err624,
      "t_err625": l10n.t_err625,
      "t_err626": l10n.t_err626,
      "t_err627": l10n.t_err627,
      "t_err628": l10n.t_err628,
      "t_err629": l10n.t_err629,
      "t_err630": l10n.t_err630,
      "t_err631": l10n.t_err631,
      "t_err632": l10n.t_err632,
      "t_err633": l10n.t_err633,
      "t_err634": l10n.t_err634,
      "t_err636": l10n.t_err636,
      "t_err637": l10n.t_err637,
      "t_err638": l10n.t_err638,
      "t_err84_mdc": l10n.t_err84_mdc,
      "t_err84_iqd": l10n.t_err84_iqd,
      "t_err703_b": l10n.t_err703_b,
      "t_err703_t": l10n.t_err703_t,
      "t_err704_b": l10n.t_err704_b,
      "t_err704_t": l10n.t_err704_t,
      "t_err705_b": l10n.t_err705_b,
      "t_err705_t": l10n.t_err705_t,
      "t_err708": l10n.t_err708,
      "t_err709": l10n.t_err709,
      "t_err711": l10n.t_err711,
      "t_err713": l10n.t_err713,
      "t_err715": l10n.t_err715,
      "t_err01_heated": l10n.t_err01_heated,
      "t_err01_motor": l10n.t_err01_motor,
      "t_err01_npsensor": l10n.t_err01_npsensor,
      "t_err734": l10n.t_err734,
      "t_err735": l10n.t_err735,
      "t_err738": l10n.t_err738,
      "t_err739": l10n.t_err739,
      "t_err740": l10n.t_err740,
      "t_err741": l10n.t_err741,
      "t_err742": l10n.t_err742,
      "t_err743": l10n.t_err743,
      "t_err743_s": l10n.t_err743_s,
      "t_err744": l10n.t_err744,
      "t_err744_s": l10n.t_err744_s,
      "t_err745": l10n.t_err745,
      "t_err746": l10n.t_err746,
      "t_err747": l10n.t_err747,
      "t_err748": l10n.t_err748,
      "t_err749": l10n.t_err749,
      "t_err749_s": l10n.t_err749_s,
      "t_err750": l10n.t_err750,
      "t_err750_s": l10n.t_err750_s,
      "t_err751": l10n.t_err751,
      "t_err752": l10n.t_err752,
      "t_err752_s": l10n.t_err752_s,
      "t_err753": l10n.t_err753,
      "t_err754": l10n.t_err754,
      "t_err755": l10n.t_err755,
      "t_err756": l10n.t_err756,
      "t_err757": l10n.t_err757,
      "t_err761": l10n.t_err761,
      "t_err762": l10n.t_err762,
      "t_err763": l10n.t_err763,
      "t_err763_s": l10n.t_err763_s,
      "t_err764": l10n.t_err764,
      "t_err765": l10n.t_err765,
      "t_err766": l10n.t_err766,
      "t_err770": l10n.t_err770,
      "t_err771": l10n.t_err771,
      "t_err772": l10n.t_err772,
      "t_err773": l10n.t_err773,
      "t_err774": l10n.t_err774,
      "t_err775": l10n.t_err775,
      "t_err776": l10n.t_err776,
      "t_err_taper01": l10n.t_err_taper01,
      "t_err_taper02": l10n.t_err_taper02,
      "t_err_taper03": l10n.t_err_taper03,
      "t_err_taper04": l10n.t_err_taper04,
      "t_err_taper05": l10n.t_err_taper05,
      "t_err_tapering07": l10n.t_err_tapering07,
      "t_err_tapering08": l10n.t_err_tapering08,
      "t_err_tapering09": l10n.t_err_tapering09,
      "t_err785": l10n.t_err785,
      "t_err790": l10n.t_err790,
      "t_err791": l10n.t_err791,
      "t_err792": l10n.t_err792,
      "t_err_paidcont_update": l10n.t_err_paidcont_update,
      "t_principal07": l10n.t_principal07,
      "t_principal07_01": l10n.t_principal07_01,
      "t_principal07_02": l10n.t_principal07_02,
      "t_principal03": l10n.t_principal03,
      "t_principal03_01": l10n.t_principal03_01,
      "t_principal03_02": l10n.t_principal03_02,
      "t_principal12": l10n.t_principal12,
      "t_principal12_01": l10n.t_principal12_01,
      "t_principal08": l10n.t_principal08,
      "t_principal08_01": l10n.t_principal08_01,
      "t_principal10": l10n.t_principal10,
      "t_principal10_00": l10n.t_principal10_00,
      "t_principal10_01": l10n.t_principal10_01,
      "t_principal10_03_00": l10n.t_principal10_03_00,
      "t_principal10_03": l10n.t_principal10_03,
      "t_principal11": l10n.t_principal11,
      "t_principal11_00": l10n.t_principal11_00,
      "t_principal11_02": l10n.t_principal11_02,
      "t_xv_principal11_01": l10n.t_xv_principal11_01,
      "t_xv_principal11_02": l10n.t_xv_principal11_02,
      "t_xv_principal11_03": l10n.t_xv_principal11_03,
      "t_xv_principal11_04": l10n.t_xv_principal11_04,
      "t_xv_principal11_05": l10n.t_xv_principal11_05,
      "t_xv_principal11_06": l10n.t_xv_principal11_06,
      "t_principal11_01_02": l10n.t_principal11_01_02,
      "t_principal09": l10n.t_principal09,
      "t_principal09_01": l10n.t_principal09_01,
      "t_principal09_02": l10n.t_principal09_02,
      "t_principal_buttons_01": l10n.t_principal_buttons_01,
      "t_principal_buttons_01_01": l10n.t_principal_buttons_01_01,
      "t_principal_buttons_02": l10n.t_principal_buttons_02,
      "t_principal_buttons_02_01": l10n.t_principal_buttons_02_01,
      "t_principal_buttons_06": l10n.t_principal_buttons_06,
      "t_principal_buttons_06_01": l10n.t_principal_buttons_06_01,
      "t_principal_buttons_05": l10n.t_principal_buttons_05,
      "t_principal_buttons_05_01": l10n.t_principal_buttons_05_01,
      "t_principal_buttons_04": l10n.t_principal_buttons_04,
      "t_principal_buttons_04_01": l10n.t_principal_buttons_04_01,
      "t_principal_buttons_03": l10n.t_principal_buttons_03,
      "t_principal_buttons_03_01": l10n.t_principal_buttons_03_01,
      "t_principal_buttons_07": l10n.t_principal_buttons_07,
      "t_principal_buttons_07_01": l10n.t_principal_buttons_07_01,
      "t_basic13": l10n.t_basic13,
      "t_basic13_01_02": l10n.t_basic13_01_02,
      "t_basic13_01": l10n.t_basic13_01,
      "t_basic13_02_00": l10n.t_basic13_02_00,
      "t_basic13_02": l10n.t_basic13_02,
      "t_basic13_03_02": l10n.t_basic13_03_02,
      "t_basic13_03": l10n.t_basic13_03,
      "t_basic13_11_02": l10n.t_basic13_11_02,
      "t_basic13_11": l10n.t_basic13_11,
      "t_basic13_04_02": l10n.t_basic13_04_02,
      "t_basic13_04": l10n.t_basic13_04,
      "t_basic13_05": l10n.t_basic13_05,
      "t_basic13_06_02": l10n.t_basic13_06_02,
      "t_basic13_06": l10n.t_basic13_06,
      "t_basic13_07": l10n.t_basic13_07,
      "t_basic13_08_02": l10n.t_basic13_08_02,
      "t_basic13_08": l10n.t_basic13_08,
      "t_basic13_09_02": l10n.t_basic13_09_02,
      "t_basic13_09": l10n.t_basic13_09,
      "t_basic13_10_02": l10n.t_basic13_10_02,
      "t_basic13_10": l10n.t_basic13_10,
      "t_basic14": l10n.t_basic14,
      "t_basic14_01_02": l10n.t_basic14_01_02,
      "t_basic14_00": l10n.t_basic14_00,
      "t_basic14_02": l10n.t_basic14_02,
      "t_basic14_02_02": l10n.t_basic14_02_02,
      "t_basic14_03": l10n.t_basic14_03,
      "t_basic14_03_02": l10n.t_basic14_03_02,
      "t_basic14_04": l10n.t_basic14_04,
      "t_basic14_04_02": l10n.t_basic14_04_02,
      "t_basic14_05": l10n.t_basic14_05,
      "t_basic14_05_02": l10n.t_basic14_05_02,
      "t_basic14_06": l10n.t_basic14_06,
      "t_basic14_07": l10n.t_basic14_07,
      "t_basic14_08_02": l10n.t_basic14_08_02,
      "t_basic14_08_03": l10n.t_basic14_08_03,
      "t_basic14_08_04": l10n.t_basic14_08_04,
      "t_basic14_08_05": l10n.t_basic14_08_05,
      "t_basic14_09": l10n.t_basic14_09,
      "t_basic14_10": l10n.t_basic14_10,
      "t_basic14_101": l10n.t_basic14_101,
      "t_basic14_102": l10n.t_basic14_102,
      "t_basic14_11": l10n.t_basic14_11,
      "t_basic14_11_02": l10n.t_basic14_11_02,
      "t_basic14_12": l10n.t_basic14_12,
      "t_basic14_12_02": l10n.t_basic14_12_02,
      "t_basic14_13": l10n.t_basic14_13,
      "t_basic14_13_02": l10n.t_basic14_13_02,
      "t_basic14_14": l10n.t_basic14_14,
      "t_basic14_15_02": l10n.t_basic14_15_02,
      "t_basic14_16": l10n.t_basic14_16,
      "t_basic14_17": l10n.t_basic14_17,
      "t_basic14_18": l10n.t_basic14_18,
      "t_basic14_18_02": l10n.t_basic14_18_02,
      "t_basic14_20": l10n.t_basic14_20,
      "t_basic14_201": l10n.t_basic14_201,
      "t_basic14_202": l10n.t_basic14_202,
      "t_basic14_203": l10n.t_basic14_203,
      "t_basic14_21_02": l10n.t_basic14_21_02,
      "t_basic14_21_03": l10n.t_basic14_21_03,
      "t_basic15": l10n.t_basic15,
      "t_basic15_00": l10n.t_basic15_00,
      "t_basic15_00_01": l10n.t_basic15_00_01,
      "t_basic15_01": l10n.t_basic15_01,
      "t_basic15_02": l10n.t_basic15_02,
      "t_basic15_03": l10n.t_basic15_03,
      "t_basic15_04": l10n.t_basic15_04,
      "t_basic15_04_02": l10n.t_basic15_04_02,
      "t_basic15_05": l10n.t_basic15_05,
      "t_basic16": l10n.t_basic16,
      "t_basic16_01": l10n.t_basic16_01,
      "t_basic16_02": l10n.t_basic16_02,
      "t_basic16_03": l10n.t_basic16_03,
      "t_basic16_03_02": l10n.t_basic16_03_02,
      "t_basic16_04": l10n.t_basic16_04,
      "t_basic16_04_02": l10n.t_basic16_04_02,
      "t_basic16_05": l10n.t_basic16_05,
      "t_basic16_05_02": l10n.t_basic16_05_02,
      "t_basic16_06": l10n.t_basic16_06,
      "t_basic16_07": l10n.t_basic16_07,
      "t_basic17": l10n.t_basic17,
      "t_basic17_01": l10n.t_basic17_01,
      "t_basic17_02": l10n.t_basic17_02,
      "t_basic17_03": l10n.t_basic17_03,
      "t_basic17_04": l10n.t_basic17_04,
      "t_basic17_05": l10n.t_basic17_05,
      "t_basic17_05_02": l10n.t_basic17_05_02,
      "t_basic17_06": l10n.t_basic17_06,
      "t_embbasic17": l10n.t_embbasic17,
      "t_embbasic17_00": l10n.t_embbasic17_00,
      "t_embbasic17_01": l10n.t_embbasic17_01,
      "t_embbasic17_01_02": l10n.t_embbasic17_01_02,
      "t_embbasic17_02": l10n.t_embbasic17_02,
      "t_embbasic17_02_02": l10n.t_embbasic17_02_02,
      "t_embbasic17_03": l10n.t_embbasic17_03,
      "t_embbasic18": l10n.t_embbasic18,
      "t_embbasic18_01": l10n.t_embbasic18_01,
      "t_embbasic18_02": l10n.t_embbasic18_02,
      "t_embbasic18_02_02": l10n.t_embbasic18_02_02,
      "t_embbasic18_03": l10n.t_embbasic18_03,
      "t_embbasic18_04": l10n.t_embbasic18_04,
      "t_embbasic18_04_02": l10n.t_embbasic18_04_02,
      "t_embbasic18_04_11": l10n.t_embbasic18_04_11,
      "t_embbasic18_04_12": l10n.t_embbasic18_04_12,
      "t_embbasic18_04_13": l10n.t_embbasic18_04_13,
      "t_embbasic18_04_14": l10n.t_embbasic18_04_14,
      "t_embbasic18_04_15": l10n.t_embbasic18_04_15,
      "t_embbasic18_04_16": l10n.t_embbasic18_04_16,
      "t_embbasic18_05": l10n.t_embbasic18_05,
      "t_embbasic18_05_01": l10n.t_embbasic18_05_01,
      "t_embbasic18_05_02": l10n.t_embbasic18_05_02,
      "t_embbasic18_06": l10n.t_embbasic18_06,
      "t_embbasic18_06_02": l10n.t_embbasic18_06_02,
      "t_embbasic18_07": l10n.t_embbasic18_07,
      "t_embbasic18_07_02": l10n.t_embbasic18_07_02,
      "t_embbasic18_08": l10n.t_embbasic18_08,
      "t_embbasic19": l10n.t_embbasic19,
      "t_embbasic19_01": l10n.t_embbasic19_01,
      "t_embbasic19_02": l10n.t_embbasic19_02,
      "t_embbasic19_03": l10n.t_embbasic19_03,
      "t_embbasic19_03_02": l10n.t_embbasic19_03_02,
      "t_embbasic19_04": l10n.t_embbasic19_04,
      "t_embbasic19_05": l10n.t_embbasic19_05,
      "t_embbasic19_05_02": l10n.t_embbasic19_05_02,
      "t_embbasic19_06": l10n.t_embbasic19_06,
      "t_embbasic19_07": l10n.t_embbasic19_07,
      "t_embbasic20": l10n.t_embbasic20,
      "t_embbasic20_01": l10n.t_embbasic20_01,
      "t_embbasic20_03": l10n.t_embbasic20_03,
      "t_embbasic20_03_02": l10n.t_embbasic20_03_02,
      "t_embbasic20_04": l10n.t_embbasic20_04,
      "t_embbasic20_05": l10n.t_embbasic20_05,
      "t_embbasic20_06": l10n.t_embbasic20_06,
      "t_embbasic20_06_02": l10n.t_embbasic20_06_02,
      "t_embbasic20_07": l10n.t_embbasic20_07,
      "t_xp_embbasic21": l10n.t_xp_embbasic21,
      "t_xp_embbasic21_01": l10n.t_xp_embbasic21_01,
      "t_xp_embbasic21_02": l10n.t_xp_embbasic21_02,
      "t_xp_embbasic21_03": l10n.t_xp_embbasic21_03,
      "t_xp_embbasic21_04": l10n.t_xp_embbasic21_04,
      "t_xp_embbasic21_04_02": l10n.t_xp_embbasic21_04_02,
      "t_xp_embbasic21_05": l10n.t_xp_embbasic21_05,
      "t_xp_embbasic21_05_02": l10n.t_xp_embbasic21_05_02,
      "t_xp_embbasic21_06": l10n.t_xp_embbasic21_06,
      "t_xp_embbasic21_07_01": l10n.t_xp_embbasic21_07_01,
      "t_xp_embbasic21_07_02": l10n.t_xp_embbasic21_07_02,
      "t_xp_embbasic21_08_01": l10n.t_xp_embbasic21_08_01,
      "t_xp_embbasic21_08_02": l10n.t_xp_embbasic21_08_02,
      "t_xp_embbasic21_09": l10n.t_xp_embbasic21_09,
      "t_embbasic21": l10n.t_embbasic21,
      "t_embbasic21_01": l10n.t_embbasic21_01,
      "t_embbasic21_02": l10n.t_embbasic21_02,
      "t_embbasic21_03": l10n.t_embbasic21_03,
      "t_embbasic21_04": l10n.t_embbasic21_04,
      "t_embbasic21_04_02": l10n.t_embbasic21_04_02,
      "t_embbasic21_05": l10n.t_embbasic21_05,
      "t_embbasic21_05_02": l10n.t_embbasic21_05_02,
      "t_embbasic21_06": l10n.t_embbasic21_06,
      "t_embbasic21_07": l10n.t_embbasic21_07,
      "t_embbasic21_08": l10n.t_embbasic21_08,
      "t_embbasic21_09": l10n.t_embbasic21_09,
      "t_embbasic21_emb_07": l10n.t_embbasic21_emb_07,
      "t_embbasic21_emb_08": l10n.t_embbasic21_emb_08,
      "t_embbasic21_emb_09": l10n.t_embbasic21_emb_09,
      "t_xv_embbasic21": l10n.t_xv_embbasic21,
      "t_xv_embbasic21_05": l10n.t_xv_embbasic21_05,
      "t_xv_embbasic21_07_01": l10n.t_xv_embbasic21_07_01,
      "t_xv_embbasic21_07_02": l10n.t_xv_embbasic21_07_02,
      "t_xv_embbasic21_08_01": l10n.t_xv_embbasic21_08_01,
      "t_xv_embbasic21_08_02": l10n.t_xv_embbasic21_08_02,
      "t_xv_embbasic21_09": l10n.t_xv_embbasic21_09,
      "t_xv_embbasic21_10": l10n.t_xv_embbasic21_10,
      "t_embbasic22": l10n.t_embbasic22,
      "t_embbasic22_00_01": l10n.t_embbasic22_00_01,
      "t_embbasic22_00_02": l10n.t_embbasic22_00_02,
      "t_embbasic22_00_03": l10n.t_embbasic22_00_03,
      "t_embbasic22_00_04": l10n.t_embbasic22_00_04,
      "t_embbasic22_00_05": l10n.t_embbasic22_00_05,
      "t_embbasic22_01": l10n.t_embbasic22_01,
      "t_embbasic22_01_02": l10n.t_embbasic22_01_02,
      "t_embbasic22_01_03": l10n.t_embbasic22_01_03,
      "t_embbasic22_02": l10n.t_embbasic22_02,
      "t_embbasic22_02_02": l10n.t_embbasic22_02_02,
      "t_embbasic22_03": l10n.t_embbasic22_03,
      "t_embbasic22_03_02": l10n.t_embbasic22_03_02,
      "t_embbasic22_04": l10n.t_embbasic22_04,
      "t_embbasic22_04_02": l10n.t_embbasic22_04_02,
      "t_embbasic23": l10n.t_embbasic23,
      "t_embbasic23_01": l10n.t_embbasic23_01,
      "t_embbasic23_01_01": l10n.t_embbasic23_01_01,
      "t_embbasic23_01_02": l10n.t_embbasic23_01_02,
      "t_embbasic23_01_03": l10n.t_embbasic23_01_03,
      "t_embbasic23_02": l10n.t_embbasic23_02,
      "t_embbasic23_02_02": l10n.t_embbasic23_02_02,
      "t_embbasic23_03": l10n.t_embbasic23_03,
      "t_embbasic23_03_02": l10n.t_embbasic23_03_02,
      "t_embbasic23_03_03": l10n.t_embbasic23_03_03,
      "t_embbasic23_04": l10n.t_embbasic23_04,
      "t_embbasic23_04_02": l10n.t_embbasic23_04_02,
      "t_embbasic23_04_03": l10n.t_embbasic23_04_03,
      "t_trouble22": l10n.t_trouble22,
      "t_trouble22_01": l10n.t_trouble22_01,
      "t_trouble22_02": l10n.t_trouble22_02,
      "t_trouble22_03": l10n.t_trouble22_03,
      "t_trouble22_04": l10n.t_trouble22_04,
      "t_trouble22_05": l10n.t_trouble22_05,
      "t_trouble22_06": l10n.t_trouble22_06,
      "t_trouble22_07": l10n.t_trouble22_07,
      "t_trouble22_08": l10n.t_trouble22_08,
      "t_trouble22_09": l10n.t_trouble22_09,
      "t_trouble22_10": l10n.t_trouble22_10,
      "t_trouble23": l10n.t_trouble23,
      "t_trouble23_01": l10n.t_trouble23_01,
      "t_trouble23_02": l10n.t_trouble23_02,
      "t_trouble23_03": l10n.t_trouble23_03,
      "t_trouble24": l10n.t_trouble24,
      "t_trouble24_01": l10n.t_trouble24_01,
      "t_trouble24_02": l10n.t_trouble24_02,
      "t_trouble24_03": l10n.t_trouble24_03,
      "t_trouble24_04": l10n.t_trouble24_04,
      "t_trouble24_05": l10n.t_trouble24_05,
      "t_trouble24_06": l10n.t_trouble24_06,
      "t_trouble25": l10n.t_trouble25,
      "t_trouble25_01": l10n.t_trouble25_01,
      "t_trouble25_02": l10n.t_trouble25_02,
      "t_trouble25_03": l10n.t_trouble25_03,
      "t_trouble25_04": l10n.t_trouble25_04,
      "t_trouble25_05": l10n.t_trouble25_05,
      "t_trouble25_06": l10n.t_trouble25_06,
      "t_trouble25_07": l10n.t_trouble25_07,
      "t_trouble26": l10n.t_trouble26,
      "t_trouble26_01": l10n.t_trouble26_01,
      "t_trouble26_02": l10n.t_trouble26_02,
      "t_trouble26_03": l10n.t_trouble26_03,
      "t_trouble26_04": l10n.t_trouble26_04,
      "t_trouble27": l10n.t_trouble27,
      "t_trouble27_01": l10n.t_trouble27_01,
      "t_trouble27_02": l10n.t_trouble27_02,
      "t_trouble27_03": l10n.t_trouble27_03,
      "t_trouble28": l10n.t_trouble28,
      "t_trouble28_01": l10n.t_trouble28_01,
      "t_trouble28_02": l10n.t_trouble28_02,
      "t_trouble28_03": l10n.t_trouble28_03,
      "t_trouble28_04": l10n.t_trouble28_04,
      "t_trouble28_05": l10n.t_trouble28_05,
      "t_trouble29": l10n.t_trouble29,
      "t_trouble29_01": l10n.t_trouble29_01,
      "t_trouble29_02": l10n.t_trouble29_02,
      "t_trouble29_03": l10n.t_trouble29_03,
      "t_trouble29_04": l10n.t_trouble29_04,
      "t_trouble30": l10n.t_trouble30,
      "t_trouble30_01": l10n.t_trouble30_01,
      "t_trouble30_02": l10n.t_trouble30_02,
      "t_trouble30_03": l10n.t_trouble30_03,
      "t_trouble30_04": l10n.t_trouble30_04,
      "t_trouble30_05": l10n.t_trouble30_05,
      "t_trouble30_06": l10n.t_trouble30_06,
      "t_trouble30_07": l10n.t_trouble30_07,
      "t_trouble30_08": l10n.t_trouble30_08,
      "t_trouble30_09": l10n.t_trouble30_09,
      "t_trouble31": l10n.t_trouble31,
      "t_trouble31_01": l10n.t_trouble31_01,
      "t_trouble31_02": l10n.t_trouble31_02,
      "t_trouble31_03": l10n.t_trouble31_03,
      "t_trouble31_04": l10n.t_trouble31_04,
      "t_trouble31_05": l10n.t_trouble31_05,
      "t_trouble31_06": l10n.t_trouble31_06,
      "t_trouble31_07": l10n.t_trouble31_07,
      "t_trouble31_08": l10n.t_trouble31_08,
      "t_trouble31_09": l10n.t_trouble31_09,
      "t_trouble31_10": l10n.t_trouble31_10,
      "t_trouble31_11": l10n.t_trouble31_11,
      "t_trouble31_12": l10n.t_trouble31_12,
      "t_trouble32": l10n.t_trouble32,
      "t_trouble32_01": l10n.t_trouble32_01,
      "t_trouble32_02": l10n.t_trouble32_02,
      "t_trouble32_03": l10n.t_trouble32_03,
      "t_trouble32_04": l10n.t_trouble32_04,
      "t_trouble32_05": l10n.t_trouble32_05,
      "t_trouble33": l10n.t_trouble33,
      "t_trouble33_01": l10n.t_trouble33_01,
      "t_trouble33_02": l10n.t_trouble33_02,
      "t_trouble33_03": l10n.t_trouble33_03,
      "t_trouble33_04": l10n.t_trouble33_04,
      "t_trouble33_05": l10n.t_trouble33_05,
      "t_trouble33_06": l10n.t_trouble33_06,
      "t_trouble34": l10n.t_trouble34,
      "t_trouble34_01": l10n.t_trouble34_01,
      "t_trouble34_02": l10n.t_trouble34_02,
      "t_trouble34_03": l10n.t_trouble34_03,
      "t_trouble34_04": l10n.t_trouble34_04,
      "t_trouble35": l10n.t_trouble35,
      "t_trouble35_01": l10n.t_trouble35_01,
      "t_maintenance36": l10n.t_maintenance36,
      "t_maintenance36_00": l10n.t_maintenance36_00,
      "t_maintenance36_01": l10n.t_maintenance36_01,
      "t_maintenance36_02": l10n.t_maintenance36_02,
      "t_maintenance36_03": l10n.t_maintenance36_03,
      "t_maintenance36_04": l10n.t_maintenance36_04,
      "t_maintenance36_05_11": l10n.t_maintenance36_05_11,
      "t_maintenance36_05_12": l10n.t_maintenance36_05_12,
      "t_maintenance36_05_13": l10n.t_maintenance36_05_13,
      "t_maintenance36_05_14": l10n.t_maintenance36_05_14,
      "t_maintenance36_05_15": l10n.t_maintenance36_05_15,
      "t_maintenance36_07_02": l10n.t_maintenance36_07_02,
      "t_maintenance36_08": l10n.t_maintenance36_08,
      "t_maintenance36_08_02": l10n.t_maintenance36_08_02,
      "t_embbasic18_04_21": l10n.t_embbasic18_04_21,
      "t_embbasic18_04_22": l10n.t_embbasic18_04_22,
      "t_embbasic18_04_23": l10n.t_embbasic18_04_23,
      "t_embbasic18_04_24": l10n.t_embbasic18_04_24,
      "t_embbasic18_04_25": l10n.t_embbasic18_04_25,
      "t_sewing01_00": l10n.t_sewing01_00,
      "t_sewing01_00_01": l10n.t_sewing01_00_01,
      "t_sewing01": l10n.t_sewing01,
      "t_sewing01_01": l10n.t_sewing01_01,
      "t_sewing01_01_02": l10n.t_sewing01_01_02,
      "t_sewing01_02": l10n.t_sewing01_02,
      "t_sewing01_03": l10n.t_sewing01_03,
      "t_sewing01_04": l10n.t_sewing01_04,
      "t_sewing01_05": l10n.t_sewing01_05,
      "t_sewing01_06": l10n.t_sewing01_06,
      "t_sewing01_07": l10n.t_sewing01_07,
      "t_sewing02": l10n.t_sewing02,
      "t_sewing02_00": l10n.t_sewing02_00,
      "t_sewing02_00_01": l10n.t_sewing02_00_01,
      "t_sewing02_01": l10n.t_sewing02_01,
      "t_sewing02_02": l10n.t_sewing02_02,
      "t_sewing02_02_02": l10n.t_sewing02_02_02,
      "t_sewing02_03": l10n.t_sewing02_03,
      "t_sewing02_04": l10n.t_sewing02_04,
      "t_sewing02_05": l10n.t_sewing02_05,
      "t_sewing02_05_02": l10n.t_sewing02_05_02,
      "t_sewing04": l10n.t_sewing04,
      "t_sewing04_01": l10n.t_sewing04_01,
      "t_sewing04_02": l10n.t_sewing04_02,
      "t_sewing04_03": l10n.t_sewing04_03,
      "t_sewing05_00": l10n.t_sewing05_00,
      "t_sewing05_00_01": l10n.t_sewing05_00_01,
      "t_sewing05": l10n.t_sewing05,
      "t_sewing05_02": l10n.t_sewing05_02,
      "t_sewing05_03": l10n.t_sewing05_03,
      "t_sewing05_04": l10n.t_sewing05_04,
      "t_sewing05_05": l10n.t_sewing05_05,
      "t_sewing05_06": l10n.t_sewing05_06,
      "t_sewing05_07": l10n.t_sewing05_07,
      "t_sewing05_08": l10n.t_sewing05_08,
      "t_sewing05_09": l10n.t_sewing05_09,
      "t_sewing05_10": l10n.t_sewing05_10,
      "t_sewing05_11": l10n.t_sewing05_11,
      "t_sewing05_12": l10n.t_sewing05_12,
      "t_sewing05_13": l10n.t_sewing05_13,
      "t_sewing05_14": l10n.t_sewing05_14,
      "t_sewing05_16": l10n.t_sewing05_16,
      "t_sewing05_17": l10n.t_sewing05_17,
      "t_sewing05_18": l10n.t_sewing05_18,
      "t_sewing05_19": l10n.t_sewing05_19,
      "t_sewing05_20": l10n.t_sewing05_20,
      "t_sewing05_21": l10n.t_sewing05_21,
      "t_sewing05_22": l10n.t_sewing05_22,
      "t_sewing05_23": l10n.t_sewing05_23,
      "t_sewing06": l10n.t_sewing06,
      "t_sewing06_01": l10n.t_sewing06_01,
      "t_sewing06_01_02": l10n.t_sewing06_01_02,
      "t_sewing06_02": l10n.t_sewing06_02,
      "t_sewing06_03": l10n.t_sewing06_03,
      "t_sewing06_04": l10n.t_sewing06_04,
      "t_sewing06_05": l10n.t_sewing06_05,
      "t_sewing06_06": l10n.t_sewing06_06,
      "t_sewing06_07": l10n.t_sewing06_07,
      "t_sewing06_08": l10n.t_sewing06_08,
      "t_sewing07": l10n.t_sewing07,
      "t_sewing07_01": l10n.t_sewing07_01,
      "t_sewing07_02": l10n.t_sewing07_02,
      "t_sewing07_03": l10n.t_sewing07_03,
      "t_sewing07_04": l10n.t_sewing07_04,
      "t_sewing07_05": l10n.t_sewing07_05,
      "t_sewing07_06": l10n.t_sewing07_06,
      "t_sewing07_09": l10n.t_sewing07_09,
      "t_sewing07_10": l10n.t_sewing07_10,
      "t_sewing07_11": l10n.t_sewing07_11,
      "t_sewing08": l10n.t_sewing08,
      "t_sewing08_00": l10n.t_sewing08_00,
      "t_sewing08_02": l10n.t_sewing08_02,
      "t_sewing08_03": l10n.t_sewing08_03,
      "t_sewing08_04": l10n.t_sewing08_04,
      "t_sewing08_05": l10n.t_sewing08_05,
      "t_sewing08_06": l10n.t_sewing08_06,
      "t_sewing08_07": l10n.t_sewing08_07,
      "t_sewing08_08": l10n.t_sewing08_08,
      "t_sewing08_11": l10n.t_sewing08_11,
      "t_sewing08_12": l10n.t_sewing08_12,
      "t_sewing08_13": l10n.t_sewing08_13,
      "t_sewing08_14": l10n.t_sewing08_14,
      "t_sewing08_15": l10n.t_sewing08_15,
      "t_sewing08_16": l10n.t_sewing08_16,
      "t_sewing08_17": l10n.t_sewing08_17,
      "t_sewing08_18": l10n.t_sewing08_18,
      "t_sewing08_19": l10n.t_sewing08_19,
      "t_sewing08_20": l10n.t_sewing08_20,
      "t_sewing08_21": l10n.t_sewing08_21,
      "t_sewing08_22": l10n.t_sewing08_22,
      "t_sewing08_23": l10n.t_sewing08_23,
      "t_sewing09_00": l10n.t_sewing09_00,
      "t_sewing09_00_01": l10n.t_sewing09_00_01,
      "t_sewing09_00_02": l10n.t_sewing09_00_02,
      "t_sewing09": l10n.t_sewing09,
      "t_sewing09_01": l10n.t_sewing09_01,
      "t_sewing09_02": l10n.t_sewing09_02,
      "t_sewing09_03": l10n.t_sewing09_03,
      "t_sewing09_04": l10n.t_sewing09_04,
      "t_sewing09_05": l10n.t_sewing09_05,
      "t_sewing09_06": l10n.t_sewing09_06,
      "t_sewing09_07": l10n.t_sewing09_07,
      "t_sewing09_08": l10n.t_sewing09_08,
      "t_sewing10": l10n.t_sewing10,
      "t_sewing10_01": l10n.t_sewing10_01,
      "t_sewing10_02": l10n.t_sewing10_02,
      "t_sewing10_03": l10n.t_sewing10_03,
      "t_sewing10_04": l10n.t_sewing10_04,
      "t_sewing10_06": l10n.t_sewing10_06,
      "t_sewing10_07": l10n.t_sewing10_07,
      "t_sewing11": l10n.t_sewing11,
      "t_sewing11_01": l10n.t_sewing11_01,
      "t_sewing11_01_02": l10n.t_sewing11_01_02,
      "t_sewing11_02": l10n.t_sewing11_02,
      "t_sewing11_02_02": l10n.t_sewing11_02_02,
      "t_sewing11_03": l10n.t_sewing11_03,
      "t_sewing11_04_02": l10n.t_sewing11_04_02,
      "t_sewing11_05": l10n.t_sewing11_05,
      "t_sewing12": l10n.t_sewing12,
      "t_sewing12_00": l10n.t_sewing12_00,
      "t_sewing12_01": l10n.t_sewing12_01,
      "t_sewing12_01_02": l10n.t_sewing12_01_02,
      "t_sewing12_02": l10n.t_sewing12_02,
      "t_sewing12_02_02": l10n.t_sewing12_02_02,
      "t_sewing12_03": l10n.t_sewing12_03,
      "t_sewing12_04": l10n.t_sewing12_04,
      "t_sewing12_05": l10n.t_sewing12_05,
      "t_sewing13": l10n.t_sewing13,
      "t_sewing13_01": l10n.t_sewing13_01,
      "t_sewing13_01_02": l10n.t_sewing13_01_02,
      "t_sewing13_02": l10n.t_sewing13_02,
      "t_sewing13_03": l10n.t_sewing13_03,
      "t_sewing13_04": l10n.t_sewing13_04,
      "t_sewing14": l10n.t_sewing14,
      "t_sewing14_00": l10n.t_sewing14_00,
      "t_sewing14_01_02": l10n.t_sewing14_01_02,
      "t_sewing14_02": l10n.t_sewing14_02,
      "t_sewing14_02_02": l10n.t_sewing14_02_02,
      "t_sewing14_03": l10n.t_sewing14_03,
      "t_sewing14_03_02": l10n.t_sewing14_03_02,
      "t_sewing14_04": l10n.t_sewing14_04,
      "t_sewing14_04_01": l10n.t_sewing14_04_01,
      "t_sewing15_00": l10n.t_sewing15_00,
      "t_sewing15_00_01": l10n.t_sewing15_00_01,
      "t_sewing15": l10n.t_sewing15,
      "t_sewing15_01": l10n.t_sewing15_01,
      "t_sewing15_012": l10n.t_sewing15_012,
      "t_sewing15_01_02": l10n.t_sewing15_01_02,
      "t_sewing15_01_022": l10n.t_sewing15_01_022,
      "t_sewing15_02": l10n.t_sewing15_02,
      "t_sewing15_03": l10n.t_sewing15_03,
      "t_sewing15_04": l10n.t_sewing15_04,
      "t_sewing15_05": l10n.t_sewing15_05,
      "t_sewing16": l10n.t_sewing16,
      "t_sewing16_01": l10n.t_sewing16_01,
      "t_sewing16_02": l10n.t_sewing16_02,
      "t_sewing16_03": l10n.t_sewing16_03,
      "t_sewing16_04": l10n.t_sewing16_04,
      "t_sewing16_05": l10n.t_sewing16_05,
      "t_sewing17": l10n.t_sewing17,
      "t_sewing17_00": l10n.t_sewing17_00,
      "t_sewing17_01": l10n.t_sewing17_01,
      "t_sewing17_02_01": l10n.t_sewing17_02_01,
      "t_sewing17_02_02": l10n.t_sewing17_02_02,
      "t_sewing17_03": l10n.t_sewing17_03,
      "t_sewing18": l10n.t_sewing18,
      "t_sewing18_00": l10n.t_sewing18_00,
      "t_sewing18_01": l10n.t_sewing18_01,
      "t_sewing18_02": l10n.t_sewing18_02,
      "t_sewing18_03": l10n.t_sewing18_03,
      "t_sewing18_04": l10n.t_sewing18_04,
      "t_sewing18_05": l10n.t_sewing18_05,
      "t_sewing19": l10n.t_sewing19,
      "t_sewing19_01": l10n.t_sewing19_01,
      "t_sewing19_02": l10n.t_sewing19_02,
      "t_sewing19_03": l10n.t_sewing19_03,
      "t_sewing19_04": l10n.t_sewing19_04,
      "t_explain_use": l10n.t_explain_use,
      "t_explain01_01": l10n.t_explain01_01,
      "t_explain01_01_01": l10n.t_explain01_01_01,
      "t_explain01_02": l10n.t_explain01_02,
      "t_explain01_03": l10n.t_explain01_03,
      "t_explain01_04": l10n.t_explain01_04,
      "t_explain01_05": l10n.t_explain01_05,
      "t_explain01_06": l10n.t_explain01_06,
      "t_explain01_07": l10n.t_explain01_07,
      "t_explain01_08": l10n.t_explain01_08,
      "t_explain01_09": l10n.t_explain01_09,
      "t_explain01_10": l10n.t_explain01_10,
      "t_explain01_11": l10n.t_explain01_11,
      "t_explain01_12": l10n.t_explain01_12,
      "t_explain01_13": l10n.t_explain01_13,
      "t_explain01_14": l10n.t_explain01_14,
      "t_explain01_14a": l10n.t_explain01_14a,
      "t_explain01_15": l10n.t_explain01_15,
      "t_explain01_16": l10n.t_explain01_16,
      "t_explain01_17": l10n.t_explain01_17,
      "t_explain01_18": l10n.t_explain01_18,
      "t_explain01_19": l10n.t_explain01_19,
      "t_explain01_20": l10n.t_explain01_20,
      "t_explain01_21": l10n.t_explain01_21,
      "t_explain01_22": l10n.t_explain01_22,
      "t_explain01_23": l10n.t_explain01_23,
      "t_explain01_24": l10n.t_explain01_24,
      "t_explain01_25": l10n.t_explain01_25,
      "t_explain01_26": l10n.t_explain01_26,
      "t_explain01_27": l10n.t_explain01_27,
      "t_explain01_28": l10n.t_explain01_28,
      "t_explain01_29": l10n.t_explain01_29,
      "t_explain01_292": l10n.t_explain01_292,
      "t_explain01_29a": l10n.t_explain01_29a,
      "t_explain01_30": l10n.t_explain01_30,
      "t_explain01_302": l10n.t_explain01_302,
      "t_explain01_31": l10n.t_explain01_31,
      "t_explain01_32": l10n.t_explain01_32,
      "t_explain01_33": l10n.t_explain01_33,
      "t_explain01_34": l10n.t_explain01_34,
      "t_explain02_01": l10n.t_explain02_01,
      "t_explain02_02": l10n.t_explain02_02,
      "t_explain02_03": l10n.t_explain02_03,
      "t_explain02_04": l10n.t_explain02_04,
      "t_explain02_05": l10n.t_explain02_05,
      "t_explain02_06": l10n.t_explain02_06,
      "t_explain02_07": l10n.t_explain02_07,
      "t_explain02_08": l10n.t_explain02_08,
      "t_explain02_09": l10n.t_explain02_09,
      "t_explain02_10": l10n.t_explain02_10,
      "t_explain02_11": l10n.t_explain02_11,
      "t_explain02_12": l10n.t_explain02_12,
      "t_explain02_13": l10n.t_explain02_13,
      "t_explain02_14": l10n.t_explain02_14,
      "t_explain02_15": l10n.t_explain02_15,
      "t_explain02_15a": l10n.t_explain02_15a,
      "t_explain02_16": l10n.t_explain02_16,
      "t_explain02_17": l10n.t_explain02_17,
      "t_explain02_18": l10n.t_explain02_18,
      "t_explain02_19": l10n.t_explain02_19,
      "t_explain03_01": l10n.t_explain03_01,
      "t_explain03_02": l10n.t_explain03_02,
      "t_explain03_03": l10n.t_explain03_03,
      "t_explain03_04": l10n.t_explain03_04,
      "t_explain03_05": l10n.t_explain03_05,
      "t_explain03_06": l10n.t_explain03_06,
      "t_explain03_07": l10n.t_explain03_07,
      "t_explain03_08": l10n.t_explain03_08,
      "t_explain03_09": l10n.t_explain03_09,
      "t_explain03_10": l10n.t_explain03_10,
      "t_explain03_11": l10n.t_explain03_11,
      "t_explain03_12": l10n.t_explain03_12,
      "t_explain03_13": l10n.t_explain03_13,
      "t_explain03_14": l10n.t_explain03_14,
      "t_explain03_15": l10n.t_explain03_15,
      "t_explain03_16": l10n.t_explain03_16,
      "t_explain03_17": l10n.t_explain03_17,
      "t_explain03_18": l10n.t_explain03_18,
      "t_explain03_19": l10n.t_explain03_19,
      "t_explain03_20": l10n.t_explain03_20,
      "t_explain03_21": l10n.t_explain03_21,
      "t_explain03_22": l10n.t_explain03_22,
      "t_explain03_23": l10n.t_explain03_23,
      "t_explain03_24": l10n.t_explain03_24,
      "t_explain03_25": l10n.t_explain03_25,
      "t_explain04_01": l10n.t_explain04_01,
      "t_explain04_02": l10n.t_explain04_02,
      "t_explain04_03": l10n.t_explain04_03,
      "t_explain04_04": l10n.t_explain04_04,
      "t_explain04_05": l10n.t_explain04_05,
      "t_explain04_06": l10n.t_explain04_06,
      "t_explain04_07": l10n.t_explain04_07,
      "t_explain04_08": l10n.t_explain04_08,
      "t_explain04_09": l10n.t_explain04_09,
      "t_explain04_10": l10n.t_explain04_10,
      "t_explain04_11": l10n.t_explain04_11,
      "t_explain04_12": l10n.t_explain04_12,
      "t_explain04_13": l10n.t_explain04_13,
      "t_explain04_14": l10n.t_explain04_14,
      "t_explain04_15": l10n.t_explain04_15,
      "t_explain04_15a": l10n.t_explain04_15a,
      "t_explain04_15b": l10n.t_explain04_15b,
      "t_explain04_15c": l10n.t_explain04_15c,
      "t_explain04_15d": l10n.t_explain04_15d,
      "t_explain04_16": l10n.t_explain04_16,
      "t_explain04_17": l10n.t_explain04_17,
      "t_explain04_18": l10n.t_explain04_18,
      "t_explain04_19": l10n.t_explain04_19,
      "t_explain04_20": l10n.t_explain04_20,
      "t_explain04_21": l10n.t_explain04_21,
      "t_explain05_01": l10n.t_explain05_01,
      "t_explain05_02": l10n.t_explain05_02,
      "t_explain05_03": l10n.t_explain05_03,
      "t_explain05_04": l10n.t_explain05_04,
      "t_explain05_05": l10n.t_explain05_05,
      "t_explain05_06": l10n.t_explain05_06,
      "t_explain05_07": l10n.t_explain05_07,
      "t_explain05_08": l10n.t_explain05_08,
      "t_explain05_09": l10n.t_explain05_09,
      "t_explain05_10": l10n.t_explain05_10,
      "t_explain05_11": l10n.t_explain05_11,
      "t_explain05_12": l10n.t_explain05_12,
      "t_explain06_01": l10n.t_explain06_01,
      "t_explain06_02": l10n.t_explain06_02,
      "t_explain06_03a": l10n.t_explain06_03a,
      "t_explain07_01": l10n.t_explain07_01,
      "t_explain07_02": l10n.t_explain07_02,
      "t_explaindeco00_01": l10n.t_explaindeco00_01,
      "t_explaindeco00_02": l10n.t_explaindeco00_02,
      "t_explaindeco01_00": l10n.t_explaindeco01_00,
      "t_explaindeco01_01": l10n.t_explaindeco01_01,
      "t_explaindeco01_02": l10n.t_explaindeco01_02,
      "t_explaindeco02_00": l10n.t_explaindeco02_00,
      "t_explaindeco02_01": l10n.t_explaindeco02_01,
      "t_explaindeco02_02": l10n.t_explaindeco02_02,
      "t_explaindeco02_03_00": l10n.t_explaindeco02_03_00,
      "t_explaindeco02_04_00": l10n.t_explaindeco02_04_00,
      "t_explaindeco02_04_01": l10n.t_explaindeco02_04_01,
      "t_explaindeco02_05_00": l10n.t_explaindeco02_05_00,
      "t_explaindeco02_05_01": l10n.t_explaindeco02_05_01,
      "t_explaindeco02_06_00": l10n.t_explaindeco02_06_00,
      "t_explaindeco02_06_01": l10n.t_explaindeco02_06_01,
      "t_explaindeco02_07_00": l10n.t_explaindeco02_07_00,
      "t_explaindeco02_07_01": l10n.t_explaindeco02_07_01,
      "t_terms_read": l10n.t_terms_read,
      "t_terms_cert_read": l10n.t_terms_cert_read,
      "t_terms_cert_01_00": l10n.t_terms_cert_01_00,
      "t_terms_cert_01_01": l10n.t_terms_cert_01_01,
      "t_terms_cert_01_02": l10n.t_terms_cert_01_02,
      "t_terms_cert_01_03": l10n.t_terms_cert_01_03,
      "t_terms_nettool_read": l10n.t_terms_nettool_read,
      "t_terms_nettool_01_00": l10n.t_terms_nettool_01_00,
      "t_terms_nettool_01_01": l10n.t_terms_nettool_01_01,
      "t_terms_nettool_01_02": l10n.t_terms_nettool_01_02,
      "t_terms_nettool_01_03": l10n.t_terms_nettool_01_03,
      "t_terms_cert_read_t": l10n.t_terms_cert_read_t,
      "t_terms_cert_01_01_t": l10n.t_terms_cert_01_01_t,
      "t_terms_cert_01_02_t": l10n.t_terms_cert_01_02_t,
      "t_terms_cert_01_03_t": l10n.t_terms_cert_01_03_t,
      "t_terms_nettool_read_t": l10n.t_terms_nettool_read_t,
      "t_terms_nettool_01_01_t": l10n.t_terms_nettool_01_01_t,
      "t_terms_nettool_01_02_t": l10n.t_terms_nettool_01_02_t,
      "t_terms_nettool_01_03_t": l10n.t_terms_nettool_01_03_t,
      "t_terms_mnmpinmac_01_b": l10n.t_terms_mnmpinmac_01_b,
      "t_terms_snj_pair_01": l10n.t_terms_snj_pair_01,
      "upg_01": l10n.upg_01,
      "upg_02": l10n.upg_02,
      "upg_03": l10n.upg_03,
      "upg_04": l10n.upg_04,
      "upg_05": l10n.upg_05,
      "upg_06": l10n.upg_06,
      "upg_07": l10n.upg_07,
      "upg_08": l10n.upg_08,
      "upg_09": l10n.upg_09,
      "upg_10": l10n.upg_10,
      "upg_11": l10n.update_10,
      "upg_12": l10n.upg_12,
      "upg_13": l10n.update_13,
      "upg_14": l10n.update_14,
      "upg_15": l10n.update_15,
      "icon_00037": l10n.icon_00037,
      "icon_00008_u": l10n.icon_00008_u,
      "icon_00009_u": l10n.icon_00009_u,
      "icon_00010_u": l10n.icon_00010_u,
      "icon_00050_u": l10n.icon_00050_u,
      "t_name_01_01": l10n.t_name_01_01,
      "t_name_01_02": l10n.t_name_01_02,
      "t_name_01_03": l10n.t_name_01_03,
      "t_name_01_04": l10n.t_name_01_04,
      "t_name_01_05": l10n.t_name_01_05,
      "t_name_01_06": l10n.t_name_01_06,
      "t_name_01_07": l10n.t_name_01_07,
      "t_name_01_08": l10n.t_name_01_08,
      "t_name_01_09": l10n.t_name_01_09,
      "t_name_01_10": l10n.t_name_01_10,
      "t_name_01_11": l10n.t_name_01_11,
      "t_name_01_12": l10n.t_name_01_12,
      "t_name_01_13": l10n.t_name_01_13,
      "t_name_01_14": l10n.t_name_01_14,
      "t_name_01_14a": l10n.t_name_01_14a,
      "t_name_01_15": l10n.t_name_01_15,
      "t_name_01_16": l10n.t_name_01_16,
      "t_name_01_17": l10n.t_name_01_17,
      "t_name_01_18": l10n.t_name_01_18,
      "t_name_01_19": l10n.t_name_01_19,
      "t_name_01_20": l10n.t_name_01_20,
      "t_name_01_21": l10n.t_name_01_21,
      "t_name_01_22": l10n.t_name_01_22,
      "t_name_01_23": l10n.t_name_01_23,
      "t_name_01_24": l10n.t_name_01_24,
      "t_name_01_25": l10n.t_name_01_25,
      "t_name_01_26": l10n.t_name_01_26,
      "t_name_01_27": l10n.t_name_01_27,
      "t_name_01_28": l10n.t_name_01_28,
      "t_name_01_29": l10n.t_name_01_29,
      "t_name_01_29a": l10n.t_name_01_29a,
      "t_name_01_30": l10n.t_name_01_30,
      "t_name_01_31": l10n.t_name_01_31,
      "t_name_01_32": l10n.t_name_01_32,
      "t_name_01_33": l10n.t_name_01_33,
      "t_name_01_34": l10n.t_name_01_34,
      "t_name_02_01": l10n.t_name_02_01,
      "t_name_02_02": l10n.t_name_02_02,
      "t_name_02_03": l10n.t_name_02_03,
      "t_name_02_03a": l10n.t_name_02_03a,
      "t_name_02_04": l10n.t_name_02_04,
      "t_name_02_05": l10n.t_name_02_05,
      "t_name_02_06": l10n.t_name_02_06,
      "t_name_02_07": l10n.t_name_02_07,
      "t_name_02_08": l10n.t_name_02_08,
      "t_name_02_09": l10n.t_name_02_09,
      "t_name_02_10": l10n.t_name_02_10,
      "t_name_02_11": l10n.t_name_02_11,
      "t_name_02_12": l10n.t_name_02_12,
      "t_name_02_13": l10n.t_name_02_13,
      "t_name_02_14": l10n.t_name_02_14,
      "t_name_02_15": l10n.t_name_02_15,
      "t_name_02_15a": l10n.t_name_02_15a,
      "t_name_02_16": l10n.t_name_02_16,
      "t_name_02_17": l10n.t_name_02_17,
      "t_name_02_18": l10n.t_name_02_18,
      "t_name_02_19": l10n.t_name_02_19,
      "t_name_03_01": l10n.t_name_03_01,
      "t_name_03_02": l10n.t_name_03_02,
      "t_name_03_03": l10n.t_name_03_03,
      "t_name_03_04": l10n.t_name_03_04,
      "t_name_03_05": l10n.t_name_03_05,
      "t_name_03_06": l10n.t_name_03_06,
      "t_name_03_07": l10n.t_name_03_07,
      "t_name_03_08": l10n.t_name_03_08,
      "t_name_03_09": l10n.t_name_03_09,
      "t_name_03_10": l10n.t_name_03_10,
      "t_name_03_11": l10n.t_name_03_11,
      "t_name_03_12": l10n.t_name_03_12,
      "t_name_03_13": l10n.t_name_03_13,
      "t_name_03_14": l10n.t_name_03_14,
      "t_name_03_15": l10n.t_name_03_15,
      "t_name_03_16": l10n.t_name_03_16,
      "t_name_03_17": l10n.t_name_03_17,
      "t_name_03_18": l10n.t_name_03_18,
      "t_name_03_19": l10n.t_name_03_19,
      "t_name_03_20": l10n.t_name_03_20,
      "t_name_03_21": l10n.t_name_03_21,
      "t_name_03_22": l10n.t_name_03_22,
      "t_name_03_23": l10n.t_name_03_23,
      "t_name_03_24": l10n.t_name_03_24,
      "t_name_03_25": l10n.t_name_03_25,
      "t_name_04_01": l10n.t_name_04_01,
      "t_name_04_02": l10n.t_name_04_02,
      "t_name_04_03": l10n.t_name_04_03,
      "t_name_04_04": l10n.t_name_04_04,
      "t_name_04_05": l10n.t_name_04_05,
      "t_name_04_06": l10n.t_name_04_06,
      "t_name_04_07": l10n.t_name_04_07,
      "t_name_04_08": l10n.t_name_04_08,
      "t_name_04_09": l10n.t_name_04_09,
      "t_name_04_10": l10n.t_name_04_10,
      "t_name_04_11": l10n.t_name_04_11,
      "t_name_04_12": l10n.t_name_04_12,
      "t_name_04_13": l10n.t_name_04_13,
      "t_name_04_14": l10n.t_name_04_14,
      "t_name_04_15": l10n.t_name_04_15,
      "t_name_04_15a": l10n.t_name_04_15a,
      "t_name_04_15b": l10n.t_name_04_15b,
      "t_name_04_15c": l10n.t_name_04_15c,
      "t_name_04_15d": l10n.t_name_04_15d,
      "t_name_04_16": l10n.t_name_04_16,
      "t_name_04_17": l10n.t_name_04_17,
      "t_name_04_18": l10n.t_name_04_18,
      "t_name_04_19": l10n.t_name_04_19,
      "t_name_04_20": l10n.t_name_04_20,
      "t_name_04_21": l10n.t_name_04_21,
      "t_name_05_01": l10n.t_name_05_01,
      "t_name_05_02": l10n.t_name_05_02,
      "t_name_05_03": l10n.t_name_05_03,
      "t_name_05_04": l10n.t_name_05_04,
      "t_name_05_05": l10n.t_name_05_05,
      "t_name_05_06": l10n.t_name_05_06,
      "t_name_05_07": l10n.t_name_05_07,
      "t_name_05_08": l10n.t_name_05_08,
      "t_name_05_09": l10n.t_name_05_09,
      "t_name_05_10": l10n.t_name_05_10,
      "t_name_05_11": l10n.t_name_05_11,
      "t_name_05_12": l10n.t_name_05_12,
      "t_name_06_01": l10n.t_name_06_01,
      "t_name_06_02": l10n.t_name_06_02,
      "t_name_06_03": l10n.t_name_06_03,
      "t_name_06_04": l10n.t_name_06_04,
      "t_name_06_05": l10n.t_name_06_05,
      "t_name_06_06": l10n.t_name_06_06,
      "t_name_07_01": l10n.t_name_07_01,
      "t_name_07_02": l10n.t_name_07_02,
      "tt_mdc_pantool": l10n.tt_mdc_pantool,
      "tt_emb_backgroundscan": l10n.tt_emb_backgroundscan,
      "tt_emb_memory": l10n.tt_emb_memory,
      "tt_emb_redo": l10n.tt_emb_redo,
      "tt_emb_undo": l10n.tt_emb_undo,
      "tt_emb_delete": l10n.tt_emb_delete,
      "tt_emb_multipleselect": l10n.tt_emb_multipleselect,
      "tt_emb_editsize": l10n.tt_emb_editsize,
      "tt_emb_editmove": l10n.tt_emb_editmove,
      "tt_emb_editgroup": l10n.tt_emb_editgroup,
      "tt_emb_editrotate": l10n.tt_emb_editrotate,
      "tt_emb_editflip": l10n.tt_emb_editflip,
      "tt_emb_editduplicate": l10n.tt_emb_editduplicate,
      "tt_emb_editdensity": l10n.tt_emb_editdensity,
      "tt_emb_editborder": l10n.tt_emb_editborder,
      "tt_emb_editapplique": l10n.tt_emb_editapplique,
      "tt_emb_editchangecolor": l10n.tt_emb_editchangecolor,
      "tt_emb_edittextedit": l10n.tt_emb_edittextedit,
      "tt_emb_editalign": l10n.tt_emb_editalign,
      "tt_emb_editstippling": l10n.tt_emb_editstippling,
      "tt_emb_editoutline": l10n.tt_emb_editoutline,
      "tt_emb_embprojector": l10n.tt_emb_embprojector,
      "tt_emb_embbasting": l10n.tt_emb_embbasting,
      "tt_emb_embsnowman": l10n.tt_emb_embsnowman,
      "tt_emb_embonecolorsew": l10n.tt_emb_embonecolorsew,
      "tt_emb_embcolorsorting": l10n.tt_emb_embcolorsorting,
      "tt_emb_embconnectsew": l10n.tt_emb_embconnectsew,
      "tt_emb_embmasktrace": l10n.tt_emb_embmasktrace,
      "tt_emb_embstartposition": l10n.tt_emb_embstartposition,
      "tt_emb_embneedlenumber": l10n.tt_emb_embneedlenumber,
      "tt_emb_embthreadcutting": l10n.tt_emb_embthreadcutting,
      "tt_emb_embcolorbar": l10n.tt_emb_embcolorbar,
      "tt_emb_editselectall": l10n.tt_emb_editselectall,
      "tt_emb_editdeselectall": l10n.tt_emb_editdeselectall,
      "tt_mdc_scanmenu": l10n.tt_mdc_scanmenu,
      "tt_mdc_datacall": l10n.tt_mdc_datacall,
      "tt_mdc_linetool": l10n.tt_mdc_linetool,
      "tt_mdc_linespoit": l10n.tt_mdc_linespoit,
      "tt_mdc_linepouring": l10n.tt_mdc_linepouring,
      "tt_mdc_brushtool": l10n.tt_mdc_brushtool,
      "tt_mdc_brushproperty": l10n.tt_mdc_brushproperty,
      "tt_mdc_brushspoit": l10n.tt_mdc_brushspoit,
      "tt_mdc_brushpouring": l10n.tt_mdc_brushpouring,
      "tt_mdc_painteraser": l10n.tt_mdc_painteraser,
      "tt_mdc_paintstamp": l10n.tt_mdc_paintstamp,
      "tt_mdc_paintsize": l10n.tt_mdc_paintsize,
      "tt_mdc_paintrotate": l10n.tt_mdc_paintrotate,
      "tt_mdc_paintflip": l10n.tt_mdc_paintflip,
      "tt_mdc_paintduplicate": l10n.tt_mdc_paintduplicate,
      "tt_mdc_paintcut": l10n.tt_mdc_paintcut,
      "tt_mdc_paintpaste": l10n.tt_mdc_paintpaste,
      "tt_mdc_select": l10n.tt_mdc_select,
      "tt_mdc_redo": l10n.tt_mdc_redo,
      "tt_mdc_undo": l10n.tt_mdc_undo,
      "tt_mdc_allclear": l10n.tt_mdc_allclear,
      "tt_mdc_lineopen": l10n.tt_mdc_lineopen,
      "tt_mdc_lineclose": l10n.tt_mdc_lineclose,
      "tt_mdc_lineline": l10n.tt_mdc_lineline,
      "tt_mdc_linepolygonal": l10n.tt_mdc_linepolygonal,
      "tt_mdc_stitchzigzag": l10n.tt_mdc_stitchzigzag,
      "tt_mdc_stitchrunning": l10n.tt_mdc_stitchrunning,
      "tt_mdc_stitchtriple": l10n.tt_mdc_stitchtriple,
      "tt_mdc_stitchcandle": l10n.tt_mdc_stitchcandle,
      "tt_mdc_stitchchain": l10n.tt_mdc_stitchchain,
      "tt_mdc_stitchestitch": l10n.tt_mdc_stitchestitch,
      "tt_mdc_stitchvsitich": l10n.tt_mdc_stitchvsitich,
      "tt_mdc_stitchmotif": l10n.tt_mdc_stitchmotif,
      "tt_mdc_stitchnnotsew": l10n.tt_mdc_stitchnnotsew,
      "tt_mdc_regiontatami": l10n.tt_mdc_regiontatami,
      "tt_mdc_regionstippling": l10n.tt_mdc_regionstippling,
      "tt_mdc_regiondecorativefill": l10n.tt_mdc_regiondecorativefill,
      "tt_mdc_regionnotsew": l10n.tt_mdc_regionnotsew,
      "tt_mdc_stamp1": l10n.tt_mdc_stamp1,
      "tt_mdc_stamp2": l10n.tt_mdc_stamp2,
      "tt_mdc_stamp3": l10n.tt_mdc_stamp3,
      "tt_mdc_stamp4": l10n.tt_mdc_stamp4,
      "tt_mdc_stamp5": l10n.tt_mdc_stamp5,
      "tt_mdc_stamp6": l10n.tt_mdc_stamp6,
      "tt_mdc_zigzagwidth": l10n.tt_mdc_zigzagwidth,
      "tt_mdc_zigzagdensity": l10n.tt_mdc_zigzagdensity,
      "tt_mdc_runpitch": l10n.tt_mdc_runpitch,
      "tt_mdc_candlewicksize": l10n.tt_mdc_candlewicksize,
      "tt_mdc_candlewickspacing": l10n.tt_mdc_candlewickspacing,
      "tt_mdc_chainsize": l10n.tt_mdc_chainsize,
      "tt_mdc_chainthickness": l10n.tt_mdc_chainthickness,
      "tt_mdc_estitchwidth": l10n.tt_mdc_estitchwidth,
      "tt_mdc_estitchspacing": l10n.tt_mdc_estitchspacing,
      "tt_mdc_estitchthickness": l10n.tt_mdc_estitchthickness,
      "tt_mdc_estitchflip": l10n.tt_mdc_estitchflip,
      "tt_mdc_vstitchwidth": l10n.tt_mdc_vstitchwidth,
      "tt_mdc_vstitchspacing": l10n.tt_mdc_vstitchspacing,
      "tt_mdc_vstitchthickness": l10n.tt_mdc_vstitchthickness,
      "tt_mdc_vstitchflip": l10n.tt_mdc_vstitchflip,
      "tt_mdc_motifstitchsize": l10n.tt_mdc_motifstitchsize,
      "tt_mdc_motifstitchspacing": l10n.tt_mdc_motifstitchspacing,
      "tt_mdc_motifstitchflip": l10n.tt_mdc_motifstitchflip,
      "tt_mdc_tatamiderection": l10n.tt_mdc_tatamiderection,
      "tt_mdc_tatamidensity": l10n.tt_mdc_tatamidensity,
      "tt_mdc_tatamipullconpen": l10n.tt_mdc_tatamipullconpen,
      "tt_mdc_tatamiundersewing": l10n.tt_mdc_tatamiundersewing,
      "tt_mdc_stiprunpitch": l10n.tt_mdc_stiprunpitch,
      "tt_mdc_stipspacing": l10n.tt_mdc_stipspacing,
      "tt_mdc_stipdistance": l10n.tt_mdc_stipdistance,
      "tt_mdc_stipsingletriple": l10n.tt_mdc_stipsingletriple,
      "tt_mdc_decofillsize": l10n.tt_mdc_decofillsize,
      "tt_mdc_decofilldirection": l10n.tt_mdc_decofilldirection,
      "tt_mdc_decofilloutline": l10n.tt_mdc_decofilloutline,
      "tt_mdc_decofillrandomshift": l10n.tt_mdc_decofillrandomshift,
      "tt_mdc_decofillpositionoffset": l10n.tt_mdc_decofillpositionoffset,
      "tt_mdc_decofillthickness1_2": l10n.tt_mdc_decofillthickness1_2,
      "tt_mdc_decofillthickness2_3": l10n.tt_mdc_decofillthickness2_3,
      "tt_mdc_stitchlink": l10n.tt_mdc_stitchlink,
      "tt_mdc_fill_linereading": l10n.tt_mdc_fill_linereading,
      "tt_mdc_fill_linecolor": l10n.tt_mdc_fill_linecolor,
      "tt_utl_category01": l10n.tt_utl_category01,
      "tt_utl_category02": l10n.tt_utl_category02,
      "tt_utl_category03": l10n.tt_utl_category03,
      "tt_utl_category04": l10n.tt_utl_category04,
      "tt_utl_category05": l10n.tt_utl_category05,
      "tt_utl_category_q": l10n.tt_utl_category_q,
      "tt_utl_category_s": l10n.tt_utl_category_s,
      "tt_utl_stitchpreview": l10n.tt_utl_stitchpreview,
      "tt_utl_projecter": l10n.tt_utl_projecter,
      "tt_utl_guideline": l10n.tt_utl_guideline,
      "tt_utl_freemotion": l10n.tt_utl_freemotion,
      "tt_utl_repeat_stitch_atamadashi": l10n.tt_utl_repeat_stitch_atamadashi,
      "tt_utl_alone_repeat": l10n.tt_utl_alone_repeat,
      "tt_utl_utilityflipvetical": l10n.tt_utl_utilityflipvertical,
      "tt_utl_buttonholemanual": l10n.tt_utl_buttonholemanual,
      "tt_utl_endpointsetting": l10n.tt_utl_endpointsetting,
      "tt_utl_scissor": l10n.tt_utl_scissor,
      "tt_deco_pivot": l10n.tt_deco_pivot,
      "tt_utl_threadcolor": l10n.tt_utl_threadcolor,
      "tt_utl_category06": l10n.tt_utl_category06,
      "tt_utl_category07": l10n.tt_utl_category07,
      "tt_utl_category08": l10n.tt_utl_category08,
      "tt_utl_category09": l10n.tt_utl_category09,
      "tt_utl_category10": l10n.tt_utl_category10,
      "tt_utl_category11": l10n.tt_utl_category11,
      "tt_utl_category12": l10n.tt_utl_category12,
      "tt_utl_category13": l10n.tt_utl_category13,
      "tt_utl_category14": l10n.tt_utl_category14,
      "tt_utl_category15": l10n.tt_utl_category15,
      "tt_utl_category16": l10n.tt_utl_category16,
      "tt_utl_category17": l10n.tt_utl_category17,
      "tt_utl_category18": l10n.tt_utl_category18,
      "tt_utl_category19": l10n.tt_utl_category19,
      "tt_utl_category20": l10n.tt_utl_category20,
      "tt_deco_utilityfliphorizon": l10n.tt_deco_utilityfliphorizon,
      "tt_deco_utilityflipvertical": l10n.tt_deco_utilityflipvertical,
      "tt_deco_delete": l10n.tt_deco_delete,
      "tt_deco_spacing": l10n.tt_deco_spacing,
      "tt_mcs_triplesewing": l10n.tt_mcs_triplesewing,
      "tt_mcs_pointdelete": l10n.tt_mcs_pointdelete,
      "tt_mcs_blockmove": l10n.tt_mcs_blockmove,
      "tt_mcs_insert": l10n.tt_mcs_insert,
      "tt_utl_mcspointset": l10n.tt_utl_mcspointset,
      "tt_mcs_contents": l10n.tt_mcs_contents,
      "t_sewing01_00_01_s_normal": l10n.t_sewing01_00_01_s_normal,
      "t_sewing01_00_01_s_reinforced": l10n.t_sewing01_00_01_s_reinforced,
      "t_sewing01_00_01_s_stretch": l10n.t_sewing01_00_01_s_stretch,
      "t_sewing02_00_01_f_lightandmedium":
          l10n.t_sewing02_00_01_f_lightandmedium,
      "t_sewing02_00_01_f_heavy": l10n.t_sewing02_00_01_f_heavy,
      "t_sewing02_00_01_f_mediumstretch": l10n.t_sewing02_00_01_f_mediumstretch,
      "t_sewing02_00_01_f_stretch1": l10n.t_sewing02_00_01_f_stretch1,
      "t_sewing02_00_01_f_thickandmediumstretch":
          l10n.t_sewing02_00_01_f_thickandmediumstretch,
      "t_sewing09_00_02_f_other": l10n.t_sewing09_00_02_f_other,
      "t_sewing05_00_01_f_lighttomediumhorizhole":
          l10n.t_sewing05_00_01_f_lighttomediumhorizhole,
      "t_sewing05_00_01_f_lighttomedium": l10n.t_sewing05_00_01_f_lighttomedium,
      "t_sewing05_00_01_f_stretchweaves": l10n.t_sewing05_00_01_f_stretchweaves,
      "t_sewing05_00_01_f_suitsandovercoat":
          l10n.t_sewing05_00_01_f_suitsandovercoat,
      "t_sewing05_00_01_f_jeansandtrousers":
          l10n.t_sewing05_00_01_f_jeansandtrousers,
      "t_sewing05_00_01_f_thickcoats": l10n.t_sewing05_00_01_f_thickcoats,
      "t_sewing15_00_01_s_piecingmiddle": l10n.t_sewing15_00_01_s_piecingmiddle,
      "t_sewing15_00_01_s_piecingright": l10n.t_sewing15_00_01_s_piecingright,
      "t_sewing15_00_01_s_piecingleft": l10n.t_sewing15_00_01_s_piecingleft,
      "t_name_sr_01": l10n.t_name_sr_01,
      "t_name_sr_02": l10n.t_name_sr_02,
      "t_name_sr_03": l10n.t_name_sr_03,
      "icon_embcate_bt_01": l10n.icon_embcate_bt_01,
      "icon_embcate_bt_02": l10n.icon_embcate_bt_02,
      "icon_embcate_bt_03": l10n.icon_embcate_bt_03,
      "icon_embcate_bt_04": l10n.icon_embcate_bt_04,
      "icon_embcate_bt_05": l10n.icon_embcate_bt_05,
      "icon_embcate_bt_06": l10n.icon_embcate_bt_06,
      "icon_embcate_bt_07": l10n.icon_embcate_bt_07,
      "icon_embcate_bt_08": l10n.icon_embcate_bt_08,
      "icon_embcate_bt_09": l10n.icon_embcate_bt_09,
      "icon_embcate_bt_10": l10n.icon_embcate_bt_10,
      "icon_embcate_b_01": l10n.icon_embcate_b_01,
      "icon_embcate_b_02": l10n.icon_embcate_b_02,
      "icon_embcate_b_03": l10n.icon_embcate_b_03,
      "icon_embcate_b_04": l10n.icon_embcate_b_04,
      "icon_embcate_b_05": l10n.icon_embcate_b_05,
      "icon_embcate_b_06": l10n.icon_embcate_b_06,
      "icon_embcate_b_07": l10n.icon_embcate_b_07,
      "icon_embcate_b_08": l10n.icon_embcate_b_08,
      "icon_embcate_b_09": l10n.icon_embcate_b_09,
      "icon_embcate_b_10": l10n.icon_embcate_b_10,
      "icon_embcate_b_11": l10n.icon_embcate_b_11,
      "icon_embcate_b_12": l10n.icon_embcate_b_12,
      "icon_embcate_b_13": l10n.icon_embcate_b_13,
      "icon_embcate_b_14": l10n.icon_embcate_b_14,
      "icon_embcate_b_15": l10n.icon_embcate_b_15,
      "icon_style1": l10n.icon_style1,
      "icon_style2": l10n.icon_style2,
      "icon_style3": l10n.icon_style3,
      "icon_style4": l10n.icon_style4,
      "icon_style5": l10n.icon_style5,
      "icon_style6": l10n.icon_style6,
      "icon_style7": l10n.icon_style7,
      "icon_style8": l10n.icon_style8,
      "icon_style9": l10n.icon_style9,
      "icon_style10": l10n.icon_style10,
      "icon_style1_name": l10n.icon_style1_name,
      "icon_style2_name": l10n.icon_style2_name,
      "icon_style3_name": l10n.icon_style3_name,
      "icon_style4_name": l10n.icon_style4_name,
      "icon_style5_name": l10n.icon_style5_name,
      "icon_style6_name": l10n.icon_style6_name,
      "icon_style7_name": l10n.icon_style7_name,
      "icon_style8_name": l10n.icon_style8_name,
      "icon_style9_name": l10n.icon_style9_name,
      "icon_style10_name": l10n.icon_style10_name,
    };

    return messageMap;
  }
}
