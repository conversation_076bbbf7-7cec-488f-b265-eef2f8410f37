import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'bottom_view_interface.freezed.dart';

@freezed
class BottomState with _$BottomState {
  const factory BottomState(
      {@Default(false) bool isEnglish,
      @Default(false) redoButtonState,
      @Default(false) undoButtonState,
      @Default(false) bool isShowUndoRedoButton}) = _BottomState;
}

abstract class BottomViewInterface extends ViewModel<BottomState> {
  BottomViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// ステッチ展開処理かどうか
  ///
  bool get isProcessImage;

  ///
  /// Memoryボタンのクリック関数
  ///
  void onMemoryButtonClick(BuildContext context);

  ///
  /// Returnボタンをクリックする
  ///
  void onReturnButtonClicked();

  ///
  /// UndoButtonクリック
  ///
  void onUndoButtonClick();

  ///
  /// RedoButtonクリック
  ///
  void onRedoButtonClick();

  ///
  /// SetButtonクリック
  ///
  void onSetButtonClick(BuildContext context);
}
