import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../model/app_locale.dart';
import '../../../../../../model/camera_model.dart';
import '../../../../../../model/header_model.dart';
import '../../../../../../model/machine_config_model.dart';
import '../../../../../../model/projector_model.dart';
import '../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../component/common_header/common_header_view_interface.dart';
import '../../../../../component/common_header/common_header_view_model.dart';
import '../../../../../component/real_preview/component/real_preview_controller.dart';
import '../../../../../component/real_preview/real_preview_view_interface.dart';
import '../../../../../component/real_preview/real_preview_view_model.dart';
import '../../../../../global_popup/global_popup_route.dart';
import '../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../global_popup/global_popups/camera_popup/camera_popup_view_model.dart';
import '../../../../../global_popup/global_popups/err_mdc_home_b/err_mdc_home_b_view_model.dart';
import '../../../../../global_popup/global_popups/err_mdc_home_clear_all_editing_data_and_move/err_mdc_home_clear_all_editing_data_and_move_view_model.dart';
import '../../../../../global_popup/global_popups/err_mdc_home_t/err_mdc_home_t_view_model.dart';
import '../../../../../global_popup/panel_popup_route.dart';
import '../../../../../page_route/page_route.dart';
import '../../../../emb/model/emb_reset_model.dart';
import '../../../../home/<USER>/home_model.dart';
import '../../../../home/<USER>/component/home_header/bwd_popup/bwd_popup_view_model.dart';
import '../../../../setting/model/setting_model.dart';
import '../../../../utl/model/camera_image_model.dart';
import '../../../model/mdc_reset_model.dart';
import '../../../model/paint/library_isolate.dart';
import '../../../model/stitch/draw_region_model.dart';
import '../../page/paint/paint_page_view_model.dart' show paintPageProvider;
import '../../page/stitch/stitch_page_view_model.dart';
import '../function_provider/projector_function_provider/projector_function_provider.dart';

/// view _modelに必要な構造
final mdcHeaderViewModelProvider =
    AutoDisposeStateNotifierProvider<CommonHeaderViewModel, CommonHeaderState>(
        (ref) => MdcHeaderViewModel(ref));

class MdcHeaderViewModel extends CommonHeaderViewModel {
  MdcHeaderViewModel(
    ref,
  ) : super(ref);

  @override
  void build() {
    super.build();

    /// Model更新
    MachineConfigModel().baseMode = SettingBaseMode.mdc;
    MachineConfigModel().currentMode = SettingBaseMode.mdc;

    /// bwd接続、カメラ機能閉じる
    ref.listen(
      appDisplayGlobalStateProvider
          .select((value) => value.bwdPanelState.ref.IsOn),
      (previous, nextState) {
        if (nextState != ENUM_LIB.FALSE) {
          /// カメラ機能閉じる
          final cameraRet = maybeCloseCamera();
          if (cameraRet == true) {
            state = state.copyWith(cameraButtonState: ButtonState.normal);
          }
        } else {
          /// Do Nothing
        }
      },
    );
  }

  @override
  void update() {
    ButtonState cameraButtonState = ButtonState.normal;

    /// 特殊模様の場合
    /// カメラボタン無効
    if (ProjectorModel().mdcProjector.isMdcProjectorViewOpen == true) {
      cameraButtonState = ButtonState.disable;
    } else if (CameraModel().isCameraOpen == true) {
      cameraButtonState = ButtonState.select;
    } else {
      cameraButtonState = ButtonState.normal;
    }

    Locale locale = AppLocale().getCurrentLocale();

    /// View更新
    state = state.copyWith(
      cameraButtonState: cameraButtonState,
      cameraTipMessageString: lookupAppLocalizations(locale).tt_head_camera,
      homeTipMessageString: lookupAppLocalizations(locale).tt_head_home,
      lockTipMessageString: lookupAppLocalizations(locale).tt_head_lock,
      osaeTipMessageString: lookupAppLocalizations(locale).tt_head_osae,
      settingTipMessageString: lookupAppLocalizations(locale).tt_head_setting,
      teachingTipMessageString: lookupAppLocalizations(locale).tt_head_teaching,
      wifiTipMessageString: lookupAppLocalizations(locale).tt_head_wifi,
      isAppBadgeSenju: HeaderModel().getAppBadgeSenju(),
    );
  }

  @override
  void onCameraButtonClicked(BuildContext context) {
    final provider = realPreviewViewModelProvider(RealPreviewType.mdc);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// 「下糸巻き」ページをクリックした場合、無効になります
    if (ref.exists(bwdPopupViewModelProvider) &&
        ref.read(bwdPopupViewModelProvider).bobbinPopupState == true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// プロジェクト起動時　カメラボタン無効
    if (ProjectorModel().mdcProjector.isMdcProjectorViewOpen == true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// カメラは開いており、閉じています
    if (state.cameraButtonState == ButtonState.select) {
      /// hideChangeView
      final errCode = TpdLibrary().apiBinding.hideChangeView();
      if (errCode == DeviceErrorCode.devInvalidError) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }

      final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
      if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
          errCode == DeviceErrorCode.devInvalidPanelError) {
        return;
      }

      if (errCode != DeviceErrorCode.devNoError) {
        return;
      }

      /// カメラ機能閉じる
      final cameraRet = maybeCloseCamera();
      if (cameraRet == true) {
        state = state.copyWith(cameraButtonState: ButtonState.normal);
      }

      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    /// カメラが開いていない場合は、カメラを開きます
    _openMdcCamera();
  }

  @override
  void onOsaeButtonClicked(BuildContext context) {
    final provider = realPreviewViewModelProvider(RealPreviewType.mdc);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// CameraPopUpは開いており、閉じています
    if (TpdLibrary()
            .apiBinding
            .bpIFGetAppDisplayGlobal()
            .changeViewState
            .ref
            .isChangeView ==
        true) {
      /// カメラ機能閉じる
      final cameraRet = maybeCloseCamera();
      if (cameraRet == true) {
        state = state.copyWith(cameraButtonState: ButtonState.normal);
      }
    }

    /// プロジェクト確認
    if (maybeCloseMdcProjector() == true) {
      return;
    }

    super.onOsaeButtonClicked(context);
  }

  @override
  void onLockButtonClicked(BuildContext context) {
    final provider = realPreviewViewModelProvider(RealPreviewType.mdc);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    super.onLockButtonClicked(context);
  }

  @override
  void onHomeButtonClicked(BuildContext context) {
    void buttonClick() {
      if (DrawRegionModel().isProcessImage) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }

      final provider = realPreviewViewModelProvider(RealPreviewType.mdc);

      /// Real Preview再生中の場合、クリック不可
      if (ref.exists(provider)) {
        if (ref.read(provider).isBusyDrawing) {
          SystemSoundPlayer().play(SystemSoundEnum.invalid);
          return;
        }
        if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
          return;
        } else {
          ref.read(provider.notifier).onFrameTableButtonClick();
        }
      }

      /// プロジェクト確認
      if (maybeCloseMdcProjector() == true) {
        return;
      }

      /// Home を確認すると lib にエラーが表示される
      final errorCode = TpdLibrary().apiBinding.checkGotoHome();
      if (errorCode == DirErrorCode.dirInvalidError) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }

      final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
      if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
          errorCode == DirErrorCode.dirInvalidPanelError) {
        return;
      }

      if (errorCode == DirErrorCode.dirRequiresConfirmation) {
        /// UIにエラーが表示されるかどうかを確認します
        if (embHasData() == true) {
          _handleEmbHasDataError();
          return;
        }

        _handleMdcHasDataError();
        return;
      }

      if (errorCode == DirErrorCode.dirTransitionOK) {
        if (mdcHasData() == true) {
          _handleMdcHasDataError();
          return;
        }

        /// カメラ機能閉じる
        final cameraRet = maybeCloseCamera();
        if (cameraRet == true) {
          state = state.copyWith(cameraButtonState: ButtonState.normal);
        }

        _gotoHomePrecess();

        /// 普通流れへ
        super.onHomeButtonClicked(context);
      }

      return;
    }

    if (ref.exists(paintPageProvider)) {
      ref
          .read(paintPageProvider.notifier)
          .lockEditProcWhenProcessing(buttonClick);
    } else {
      buttonClick.call();
    }
  }

  @override
  void onSettingButtonClicked(BuildContext context) {
    final provider = realPreviewViewModelProvider(RealPreviewType.mdc);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// プロジェクト確認
    if (maybeCloseMdcProjector() == true) {
      return;
    }

    /// setting画面へ
    final errCode = TpdLibrary().apiBinding.gotoSettings();

    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    final bPIFError = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    if (bPIFError != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      return;
    }

    if (errCode != DeviceErrorCode.devNoError) {
      return;
    }

    /// カメラ機能閉じる
    final cameraRet = maybeCloseCamera();
    if (cameraRet == true) {
      state = state.copyWith(cameraButtonState: ButtonState.normal);
    }

    /// MDCはEMBの関連設定ページにジャンプする
    SettingModel().setSettingPageIndexWithOtherPage(SettingMode.embroidery);
    super.onSettingButtonClicked(context);
  }

  @override
  void onTeachingButtonClicked(BuildContext context) {
    final deviceError = TpdLibrary().apiBinding.gotoHowToUse();
    if (deviceError != DeviceErrorCode.devNoError) {
      return;
    }
    final provider = realPreviewViewModelProvider(RealPreviewType.mdc);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// プロジェクト確認
    if (maybeCloseMdcProjector() == true) {
      return;
    }

    /// カメラ機能閉じる
    final cameraRet = maybeCloseCamera();
    if (cameraRet == true) {
      state = state.copyWith(cameraButtonState: ButtonState.normal);
    }

    super.onTeachingButtonClicked(context);
  }

  @override
  void onWifiButtonClicked(BuildContext context) {
    final deviceError = TpdLibrary().apiBinding.gotoWlanSetting();
    if (deviceError != DeviceErrorCode.devNoError) {
      return;
    }

    final provider = realPreviewViewModelProvider(RealPreviewType.mdc);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// プロジェクト確認
    if (maybeCloseMdcProjector() == true) {
      return;
    }

    /// カメラ機能閉じる
    final cameraRet = maybeCloseCamera();
    if (cameraRet == true) {
      state = state.copyWith(cameraButtonState: ButtonState.normal);
    }

    super.onWifiButtonClicked(context);
  }

  @override
  void dispose() {
    LibraryIsolate().killIsolate();
    super.dispose();
  }

  ///
  /// プロジェクト起動時、プロジェクト閉じる流れ処理
  /// 戻る値: true(閉じる処理が必要ですので、普通処理をしない)
  ///
  bool maybeCloseMdcProjector() {
    /// プロジェクションがオンになっている場合の処理
    if (ProjectorModel().mdcProjector.isMdcProjectorViewOpen == true) {
      final mdcProjectorFunction =
          ref.read(mdcProjectorFunctionProvider.notifier);

      /// プロジェクト起動・停止前にエラーCheckをする
      /// エラーがあれば ポープアープを表示する
      final hasError = mdcProjectorFunction
          .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
      if (hasError) {
        return true;
      }

      mdcProjectorFunction.closeMdcProjectorView(
        closingHandleCallback: () {
          ProjectorModel().mdcProjector.isMdcProjectorViewOpen = false;

          /// 他のページへの更新の通知
          ref
              .read(stitchPageViewModelProvider.notifier)
              .updatePageByChild(ComponentType.projector);
          update();
        },
        afterClosedHandleCallback: () {},
      );

      return true;
    }

    return false;
  }

  ///
  /// MDCがホームに戻る前の回復です
  ///
  void _gotoHomePrecess() {
    /// ホームに移行する前の状態のクリーンアップ
    mdcGoToHome();

    /// Homeへ
    HomeModel.geToHome();

    MachineConfigModel().baseMode = SettingBaseMode.home;
  }

  ///
  /// カメラを開きます
  ///
  void _openMdcCamera() {
    /// showChangeView
    final errCode = TpdLibrary().apiBinding.showChangeView();
    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      return;
    }

    if (errCode != DeviceErrorCode.devNoError) {
      return;
    }

    /// カメラ起動
    CameraModel().isCameraOpen = true;
    CameraImageModel.cameraImagePollingControl
        .startCameraImagePollingAndRefreshUI();

    /// View更新
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    state = state.copyWith(cameraButtonState: ButtonState.select);

    PanelPopupRoute()
        .pushNamed(
            nextRoute: GlobalPopupRouteEnum.cameraPopup,
            arguments: const CameraArgument(isUtlOpen: false))
        .then((_) {
      /// カメラ機能閉じる
      final cameraRet = maybeCloseCamera();
      if (cameraRet == true) {
        state = state.copyWith(cameraButtonState: ButtonState.normal);
      }
    });
  }

  ///
  /// EmbHasDataError 処理
  ///
  void _handleEmbHasDataError() {
    GlobalPopupRoute().updateErrorState(
      nextRoute:
          GlobalPopupRouteEnum.ERR_MDC_HOME_CLEAR_ALL_EDITING_DATA_AND_MOVE,
      arguments: ErrMdcHomeClearAllEditingDataAndMoveArgument(
        onOKButtonClicked: (_) {
          final DeviceErrorCode deviceError =
              GlobalPopupRoute().resetErrorState();
          if (deviceError != DeviceErrorCode.devNoError) {
            return;
          }

          /// カメラ機能閉じる
          final cameraRet = maybeCloseCamera();
          if (cameraRet == true) {
            state = state.copyWith(cameraButtonState: ButtonState.normal);
          }

          embGoToHome();
          _gotoHomePrecess();

          /// home画面へ
          PagesRoute().popUntil(nextRoute: PageRouteEnum.home);
        },
        onCancelButtonClicked: (_) {
          final DeviceErrorCode deviceError =
              GlobalPopupRoute().resetErrorState();
          if (deviceError != DeviceErrorCode.devNoError) {
            return;
          }
        },
      ),
    );
    return;
  }

  ///
  /// MdcHasDataError 処理
  ///
  void _handleMdcHasDataError() {
    /// エラーセット
    if (MachineConfigModel().getCurrentModel() == AppModelEnum.brother) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_MDC_HOME_B,
        arguments: ErrMdcHomeBArgument(
          onOKButtonClicked: (_) {
            final DeviceErrorCode deviceError =
                GlobalPopupRoute().resetErrorState();
            if (deviceError != DeviceErrorCode.devNoError) {
              return;
            }

            /// カメラ機能閉じる
            final cameraRet = maybeCloseCamera();
            if (cameraRet == true) {
              state = state.copyWith(cameraButtonState: ButtonState.normal);
            }

            _gotoHomePrecess();

            /// home画面へ
            PagesRoute().popUntil(nextRoute: PageRouteEnum.home);
          },
          onCancelButtonClicked: (_) {
            final DeviceErrorCode deviceError =
                GlobalPopupRoute().resetErrorState();
            if (deviceError != DeviceErrorCode.devNoError) {
              return;
            }
          },
        ),
      );
    } else {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_MDC_HOME_T,
        arguments: ErrMdcHomeTArgument(
          onOKButtonClicked: (_) {
            final DeviceErrorCode deviceError =
                GlobalPopupRoute().resetErrorState();
            if (deviceError != DeviceErrorCode.devNoError) {
              return;
            }

            /// カメラ機能閉じる
            final cameraRet = maybeCloseCamera();
            if (cameraRet == true) {
              state = state.copyWith(cameraButtonState: ButtonState.normal);
            }

            _gotoHomePrecess();

            /// home画面へ
            PagesRoute().popUntil(nextRoute: PageRouteEnum.home);
          },
          onCancelButtonClicked: (_) {
            final DeviceErrorCode deviceError =
                GlobalPopupRoute().resetErrorState();
            if (deviceError != DeviceErrorCode.devNoError) {
              return;
            }
          },
        ),
      );
    }
  }
}
