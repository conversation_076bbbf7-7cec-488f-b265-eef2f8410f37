import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'line_candle_spacing_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class LineCandleSpaceState with _$LineCandleSpaceState {
  const factory LineCandleSpaceState({
    required String spaceDisplayValue,
    required bool plusButtonValid,
    required bool minusButtonValid,
    required bool isDefaultStyle,
  }) = _LineCandleSpaceState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class LineCandleSpaceStateViewInterface
    extends ViewModel<LineCandleSpaceState> {
  LineCandleSpaceStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress);

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
