import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'line_v_stitch_thickness_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class LineVStitchThicknessState with _$LineVStitchThicknessState {
  const factory LineVStitchThicknessState(
      {required String thicknessDisplayValue,
      required bool isDefaultValue,
      required bool plusButtonValid,
      required bool minusButtonValid}) = _LineVStitchThicknessState;
}

abstract class LineVStitchThicknessStateViewInterface
    extends ViewModel<LineVStitchThicknessState> {
  LineVStitchThicknessStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress);

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
