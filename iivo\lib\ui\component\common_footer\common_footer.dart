import 'package:common_component/common/tooltip.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';
import 'common_footer_view_interface.dart';
import 'digital_clock/digital_clock.dart';

class CommonFooter extends ConsumerWidget {
  const CommonFooter({super.key, required this.commonFooterViewModelProvider});
  final AutoDisposeStateNotifierProvider<CommonFooterViewInterface,
      CommonFooterState> commonFooterViewModelProvider;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(commonFooterViewModelProvider);
    final viewModel = ref.watch(commonFooterViewModelProvider.notifier);
    Key containerKey = UniqueKey();
    return SizedBox(
      height: 61,
      width: 800,
      child: Stack(
        children: [
          CustomTooltip(
            message: state.footerTipMessageString,
            child: Stack(
              children: [
                Opacity(
                  opacity: state.isClockDisplay ? 1 : 0,
                  child: grp_btn_text_footer_theme2(
                    feedBackControl: null,
                    onTap: () => viewModel.onClockButtonClick(context),
                    buttonState: state.footerButtonState,
                    child: Row(
                      children: [
                        const Spacer(flex: 272),
                        Expanded(
                          flex: 256,
                          child: DigitalClock(
                            key: containerKey,
                          ),
                        ),
                        const Spacer(flex: 272),
                      ],
                    ),
                  ),
                ),
                Opacity(
                  opacity: state.isClockDisplay ? 0 : 1,
                  child: grp_btn_clock_footer(
                    feedBackControl: null,
                    onTap: () => viewModel.onClockButtonClick(context),
                    svgByteData: state.clockSvgData,
                    buttonState: state.footerButtonState,
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              const Spacer(flex: 650),
              Visibility(
                visible: state.isPdfButtonVisible,
                child: Expanded(
                  flex: 54,
                  child: grp_btn_teaching_pdf(
                    onTap: () => viewModel.onPdfButtonClick(context),
                  ),
                ),
              ),
              const Spacer(flex: 96),
            ],
          ),
          state.isScreenShot
              ? Container()
              : Row(
                  children: [
                    const Spacer(flex: 670),
                    Expanded(
                      flex: 130,
                      child: GestureDetector(
                        onTap: () => viewModel.onScreenshotButtonClick(context),
                        child: Center(
                          child: Container(
                            width: 130,
                            height: 61,
                            color: Colors.transparent,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }
}
