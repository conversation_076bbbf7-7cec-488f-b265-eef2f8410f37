package com.brother.ph.eel.usb_manager

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.storage.StorageManager
import android.util.Log
import androidx.annotation.NonNull
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import net.lingala.zip4j.ZipFile
import net.lingala.zip4j.exception.ZipException
import net.lingala.zip4j.io.inputstream.ZipInputStream
import net.lingala.zip4j.model.LocalFileHeader
import net.lingala.zip4j.model.ZipParameters
import net.lingala.zip4j.model.enums.AesKeyStrength
import net.lingala.zip4j.model.enums.CompressionLevel
import net.lingala.zip4j.model.enums.CompressionMethod
import net.lingala.zip4j.model.enums.EncryptionMethod
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.nio.charset.StandardCharsets


/** UsbManagerPlugin */
class UsbManagerPlugin : FlutterPlugin, MethodCallHandler, ActivityAware {
  /// The MethodChannel that will the communication between Flutter and native Android
  ///
  /// This local reference serves to register the plugin with the Flutter Engine and unregister it
  /// when the Flutter Engine is detached from the Activity
  private lateinit var usbManagerChannel: MethodChannel
  private lateinit var usbMonitorChannel: MethodChannel
  private lateinit var mouseMonitorChannel: MethodChannel
  private lateinit var activity: Activity
  private lateinit var applicationContext: Context
  private lateinit var usbMonitor: UsbMonitor
  private val debugLogTag: String = "UsbManagerPlugin"

  private val mBrotherFunctions = BrotherFunctions()
  private lateinit var mainScope: CoroutineScope

  private lateinit var mouseConnectionMonitor: MouseConnectionMonitor

  override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
    usbManagerChannel = MethodChannel(flutterPluginBinding.binaryMessenger, "usb_manager")
    usbMonitorChannel = MethodChannel(flutterPluginBinding.binaryMessenger, "usb_monitor")
    mouseMonitorChannel = MethodChannel(flutterPluginBinding.binaryMessenger, "mouse_monitor")
    applicationContext = flutterPluginBinding.applicationContext
    usbManagerChannel.setMethodCallHandler(this)
    mouseMonitorChannel.setMethodCallHandler(this)
    mainScope = MainScope()
  }

  override fun onAttachedToActivity(binding: ActivityPluginBinding) {
    activity = binding.activity
  }

  override fun onDetachedFromActivityForConfigChanges() {}
  override fun onDetachedFromActivity() {
    if (::usbMonitor.isInitialized) {
      usbMonitor.destroy()
    }
  }

  @delegate:SuppressLint("PrivateApi")
  private val isUsbHiddenApiSupported by lazy {
    runCatching {
      StorageManager::class.java.getDeclaredMethod("getMountDelayVolumeList")
    }.isSuccess
  }

  override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {}

  override fun onMethodCall(call: MethodCall, result: Result) {
    when (call.method) {
      "usbManagerInit" -> if (::usbMonitor.isInitialized) {
        result.success(null)
      } else {
        if (isUsbHiddenApiSupported) {
          usbMonitor = UsbMonitor(activity, usbMonitorChannel)
          usbMonitor.initialize()
          result.success(true)
        } else {
          result.success(false)
        }
      }


      "readJsonFromZip" -> {
        readJsonFromZipAsync(call, result)
      }

      "unZip" -> {
        unZipAsync(call, result)
      }

      "calculateSHA256" -> {
        calculateSHA256Async(call, result)
      }

      "getUsbFreeSpace" -> {
        result.success(mBrotherFunctions.get_usb_free_space(call.argument("path")));
      }

      "extractDirFromUpf" -> {
        extractDirFromUpfAsync(call, result)
      }

      "verifyUPFIntegrity" -> {
        verifyUPFIntegrityAsync(call, result)
      }

      "compressFolder" -> {
        compressFolderAsync(call, result)
      }

      "startMonitoringMouseConnection" -> {
        if (!::mouseConnectionMonitor.isInitialized) {
          mouseConnectionMonitor = MouseConnectionMonitor(applicationContext)
          mouseConnectionMonitor.startMonitoring(sticky = false) { isMouseConnected ->
            mouseMonitorChannel.invokeMethod("onMouseConnectionChanged", isMouseConnected)
          }
        } else {
          Log.w("UsbManagerPlugin", "Mouse connection monitor is already running")
        }
        result.success(null)
      }

      else -> {
        result.notImplemented();
      }
    }
  }

  ///
  /// IOスレッドプール内でファイルのハッシュ値を計算し、メインスレッドのブロックを防ぐる
  ///
  private fun calculateSHA256Async(call: MethodCall, result: Result) {
    val filePath = call.argument<String>("filePath")
    var fileHash = ""
    try {
      fileHash = CalculateUtils.calculateSHA256(filePath)
    } catch (e: Exception) {
      e.printStackTrace()
    }
    result.success(fileHash)
  }

  ///
  /// IOスレッドプール内でZIPからファイル情報を読み取ることで、メインスレッドのブロックを防ぐる
  ///
  private fun readJsonFromZipAsync(call: MethodCall, result: Result) {
    mainScope.launch(Dispatchers.IO) {
      var jsonData = ""
      try {
        val zipFilePath = call.argument<String>("zipFilePath")
        val fileName = call.argument<String>("fileName")
        val password = call.argument<String>("password")

        jsonData = readJsonFromZip(
          zipFilePath.toString(), fileName.toString(), password.toString()
        )
      } catch (e: Exception) {
        e.printStackTrace()
      }
      result.success(jsonData)
    }
  }

  ///
  /// 読む UPFHeader.json
  ///
  private fun readJsonFromZip(zipFilePath: String, fileName: String, password: String): String {
    val outputDir = activity.getFilesDir().path
    val isSuccess = unzipSpecificFilesAndFolders(zipFilePath, outputDir, fileName, password);

    if (!isSuccess) {
      return ""
    }

    val file = File(outputDir, fileName)
    val fis = FileInputStream(file)
    try {
      val size = fis.available()
      val buffer = ByteArray(size)
      fis.read(buffer)
      val jsonString = String(buffer, StandardCharsets.UTF_8)
      return jsonString
    } catch (e: Exception) {
      e.printStackTrace()
      return ""
    } finally {
      fis.close()
      file.delete()
    }
  }

  ///
  /// パスワードを使用してzipパッケージを解凍します
  ///
  @SuppressLint("SuspiciousIndentation")
  fun unzipSpecificFilesAndFolders(
    zipFilePath: String,
    outputDir: String,
    targetPath: String,
    password: String
  ): Boolean {
    val pass = password.toCharArray()
    val zipFile = File(zipFilePath)
    if (!zipFile.exists()) {
      System.err.println("Error: ZIP file does not exist: $zipFilePath")
      return false
    }
    println("Opening ZIP file: $zipFilePath")
    val zip4jFile = ZipFile(zipFile)
    if (zip4jFile.isEncrypted) {
      println("ZIP file is encrypted, setting password." + password)
      zip4jFile.setPassword(pass)
    } else {
      println("ZIP file is not encrypted.")
      return false
    }
    return try {
      val fileHeaders = zip4jFile.fileHeaders
      if (fileHeaders == null || fileHeaders.isEmpty()) {
        System.err.println("Error: No file headers found in the zip file. Possible reasons: invalid ZIP file or incorrect password.")
        return false
      } else {
        println("Number of file headers found: " + fileHeaders.size)
      }
      var foundAny = false
      var found = false
      for (fileHeader in fileHeaders) {
        val entryName = fileHeader.fileName
        if (entryName == targetPath || entryName.startsWith("$targetPath/")) {
          found = true
          foundAny = true
          if (fileHeader.isDirectory) {
            val dir = File(outputDir, entryName)
            if (!dir.exists() && !dir.mkdirs()) {
              System.err.println("Error: Failed to create directory " + dir.absolutePath)
              return false
            }
          } else {
            zip4jFile.extractFile(fileHeader, outputDir)
          }
        }
      }
      if (!found) {
        System.err.println("Error: Target path not found in the zip file: $targetPath")
      }

      if (!foundAny) {
        System.err.println("Error: None of the target paths were found in the zip file.")
        return false
      }
      true
    } catch (e: ZipException) {
      e.printStackTrace()
      System.err.println("Error using Zip4j: " + e.message)
      false
    } finally {
      zip4jFile.close()
    }
  }

  ///
  /// IOスレッドプール内でZIPの解凍を実行し、メインスレッドのブロックを防ぐる
  ///
  private fun unZipAsync(call: MethodCall, result: Result) {
    mainScope.launch(Dispatchers.IO) {
      val zipFilePath = call.argument<String>("zipFilePath")
      val dirPath = call.argument<String>("dirPath")
      val password = call.argument<String>("password")
      val isSuccess = unZip(zipFilePath.toString(), dirPath.toString(), password.toString())
      result.success(isSuccess)
    }
  }

  ///
  /// ZIPを解凍する
  ///
  private fun unZip(zipFilePath: String, outputDir: String, password: String): Boolean {
    val pass = password.toCharArray()
    return try {
      val zipFile = File(zipFilePath)
      if (!zipFile.exists()) {
        System.err.println("Error: ZIP file does not exist: $zipFilePath")
        return false
      }
      println("Opening ZIP file: $zipFilePath")
      val zip4jFile = ZipFile(zipFile)
      if (zip4jFile.isEncrypted) {
        println("ZIP file is encrypted, setting password." + password)
        zip4jFile.setPassword(pass)
      } else {
        println("ZIP file is not encrypted.")
        return false
      }
      val fileHeaders = zip4jFile.fileHeaders
      if (fileHeaders == null || fileHeaders.isEmpty()) {
        System.err.println("Error: No file headers found in the zip file. Possible reasons: invalid ZIP file or incorrect password.")
        return false
      } else {
        println("Number of file headers found: " + fileHeaders.size)
      }
      for (fileHeader in fileHeaders) {
        val entryName = fileHeader.fileName
        if (fileHeader.isDirectory) {
          val dir = File(outputDir, entryName)
          if (!dir.exists() && !dir.mkdirs()) {
            System.err.println("Error: Failed to create directory " + dir.absolutePath)
            return false
          }
        } else {
          zip4jFile.extractFile(fileHeader, outputDir)
        }
      }
      true
    } catch (e: ZipException) {
      e.printStackTrace()
      System.err.println("Error using Zip4j: " + e.message)
      false
    }
  }

  override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
    usbManagerChannel.setMethodCallHandler(null)
    mainScope.cancel()
    mouseConnectionMonitor.stopMonitoring()
  }

  /**
   * 特定の [name] を持つ [LocalFileHeader] に現在の [ZipInputStream] をシークします。
   */
  private fun ZipInputStream.seekToEntry(name: String) {
    var entry: LocalFileHeader? = nextEntry
    while (entry != null) {
      if (entry.fileName == name) {
        return
      }
      entry = nextEntry
    }
    error("Could not find entry with name $name")
  }

  /**
   * メインスレッドのブロックを防ぐために、IO スレッド プールで EULA PDF を抽出する操作を実行します。
   */
  private fun extractDirFromUpfAsync(call: MethodCall, result: Result) {
    mainScope.launch(Dispatchers.IO) {
      val upfFilepath = call.argument<String>("upfFilepath")!!
      val outputDirPath = call.argument<String>("outputDirPath")!!
      val dirName = call.argument<String>("dirName")!!
      val password = call.argument<String>("password")
      runCatching {
        extractDirFromUpf(upfFilepath, outputDirPath, dirName, password)
      }.onFailure {
        Log.e(
          debugLogTag, "Failed to extract dir $dirName from " +
            "$upfFilepath to $outputDirPath: \n${it.stackTraceToString()}"
        )
        result.success(false)
      }.onSuccess {
        result.success(true)
      }
    }
  }

  /**
   * UPF 内の [LocalFileHeader] を FileUpdatePackage 下の特定のディレクトリにシークします。
   */
  private inline fun seekToSpecificDirInUpf(
    upfFilePath: String,
    dirName: String,
    password: String?,
    block: ZipInputStream.(String) -> Unit
  ) {
    val zipFile = ZipFile(upfFilePath)
    val passwordChars = if (zipFile.isEncrypted) {
      password?.toCharArray()
    } else {
      throw RuntimeException("Illegal upf file")
    }
    zipFile.close()
    FileInputStream(upfFilePath).use {
      ZipInputStream(it, passwordChars).use { upf ->
        val rootEntryName = "FileUpdate/FileUpdatePackage/"
        val entryName = if (dirName.isEmpty()) rootEntryName else "$rootEntryName$dirName/"
        upf.seekToEntry(entryName)
        upf.block(entryName)
      }
    }
  }

  /**
   * UPF 内の特定の [dirName] をそのまま特定の [outputPath] に抽出します。
   */
  private fun extractDirFromUpf(
    upfFilePath: String,
    outputPath: String,
    dirName: String,
    password: String?
  ) {
    seekToSpecificDirInUpf(upfFilePath, dirName, password) { entryName ->
      var entry = nextEntry
      while (entry != null) {
        val path = entry.fileName
        // 指定したフォルダー内にあることを確認してください
        if (!path.startsWith(entryName)) return
        val relativePath = path.substring(entryName.length)
        val destFile = File(outputPath, relativePath)
        if (entry.isDirectory) {
          if (!destFile.exists()) destFile.mkdirs()
        } else {
          if (!destFile.exists()) destFile.createNewFile()
          destFile.outputStream().use {
            copyTo(it)
          }
        }
        entry = nextEntry
      }
    }
  }

  ///
  /// IOスレッドプール内でUPFの完全性を検証し、メインスレッドのブロックを防ぐる。
  ///
  private fun verifyUPFIntegrityAsync(call: MethodCall, result: Result) {
    mainScope.launch(Dispatchers.IO) {
      val zipFilePath = call.argument<String>("zipFilePath")
      val password = call.argument<String>("password")
      val publicKeyPemDataList = call.argument<ByteArray>("publicKeyPemDataList")
      val isVerified = CalculateUtils.verifyUPFIntegrity(zipFilePath, publicKeyPemDataList, password)
      result.success(isVerified)
    }
  }


  ///
  /// IOスレッドプール内で指定のフォルダリストを圧縮し、任意のディレクトリにZIPファイルとして保存する。
  ///
  private fun compressFolderAsync(call: MethodCall, result: Result) {
    mainScope.launch(Dispatchers.IO) {
      val zipFilePath = call.argument<List<String>>("sourceFolderPathList")
      val outputZipFilePath = call.argument<String>("outputZipFilePath")
      val password = call.argument<String>("password")
      if (zipFilePath != null && outputZipFilePath != null && password != null) {
        val isSuccess = compressFolder(zipFilePath, outputZipFilePath, password)
        result.success(isSuccess)
      } else {
        result.success(null)
      }
    }
  }


  /**
   * 指定のフォルダリストを圧縮し、任意のディレクトリにZIPファイルとして保存する。
   * 圧縮時にAES256位暗号化を利用し、パスワードを付与する。
   *
   * @param sourceFolderPathList 圧縮対象のフォルダパスリスト
   * @param outputZipFilePath    出力先のZIPファイルパス（任意のディレクトリ）
   * @param password             圧縮ファイルのパスワード
   * @return                     圧縮に成功したか否か
   */
  private fun compressFolder(
    sourceFolderPathList: List<String>,
    outputZipFilePath: String,
    password: String
  ): Boolean {
    // ZIPファイルのインスタンスを生成
    val zipFile = ZipFile(outputZipFilePath)
    return try {


      // 圧縮パラメータを設定
      val zipParameters = ZipParameters()
      zipParameters.compressionMethod = CompressionMethod.DEFLATE // 圧縮方式をDEFLATEに指定
      zipParameters.compressionLevel = CompressionLevel.FASTEST // 圧縮レベルをに指定
      zipParameters.encryptionMethod = EncryptionMethod.ZIP_STANDARD // 暗号化方式をZIP_STANDARDに指定
      zipParameters.aesKeyStrength = AesKeyStrength.KEY_STRENGTH_128 // AESキーの強度を128位に指定
      zipParameters.isEncryptFiles = true // ファイルの暗号化を有効化

      // 暗号化設定
      val pass = password.toCharArray()
      zipFile.setPassword(pass)

      // フォルダリストを遍歴し、ZIPファイルに追加圧縮
      for (folderPath in sourceFolderPathList) {
        zipFile.addFolder(File(folderPath), zipParameters)
      }

      // 圧縮成功を返却
      true
    } catch (e: IOException) {
      // 例外発生時、エラーを出力し、圧縮失敗を返却
      e.printStackTrace()
      false
    }finally {
      // ZIPファイルを閉じ、資源を解放
      zipFile.close()
    }
  }
}
