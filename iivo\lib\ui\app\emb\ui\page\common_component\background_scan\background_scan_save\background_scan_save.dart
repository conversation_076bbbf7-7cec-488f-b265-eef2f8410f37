import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'background_scan_save_view_model.dart';

class BackgroundScanSavePopup extends ConsumerStatefulWidget {
  const BackgroundScanSavePopup({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState<BackgroundScanSavePopup> createState() =>
      _BackgroundScanSavePopupState();
}

class _BackgroundScanSavePopupState
    extends ConsumerState<BackgroundScanSavePopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final viewModel =
        ref.read(backgroundScanSavePopupViewModeProvider.notifier);
    final state = ref.watch(backgroundScanSavePopupViewModeProvider);

    ///
    /// USBディスプレイの最大数
    ///
    const int maxUsbDisplayNumber = 2;

    return Column(
      children: [
        const Spacer(flex: 778),
        Expanded(
          flex: 433,
          child: Row(
            children: [
              const Spacer(flex: 6),
              Expanded(
                flex: 788,
                child: Material(
                  color: Colors.transparent,
                  child: Stack(
                    children: [
                      const pic_half_popup(),
                      Column(
                        children: [
                          const Spacer(flex: 166),
                          Expanded(
                            flex: 70,
                            child: Row(
                              children: [
                                const Spacer(flex: 11),
                                Expanded(
                                  flex: 765,
                                  child: grp_btngrp_cernterc(
                                    length: 765,
                                    buttonLength: 205,
                                    spaceLength: 8,
                                    buttonList: state.usbInfoList.length <
                                            maxUsbDisplayNumber
                                        ? [
                                            grp_btn_usb1_h70(
                                              onTap: () => viewModel
                                                  .onUsb1ButtonClicked(context),
                                              usbInfo: state.usbInfoList.isEmpty
                                                  ? null
                                                  : state.usbInfoList[
                                                      viewModel.getUsbButton1],
                                              feedBackControl: null,
                                            ),
                                          ]
                                        : [
                                            grp_btn_usb1_h70(
                                              onTap: () => viewModel
                                                  .onUsb1ButtonClicked(context),
                                              usbInfo: state.usbInfoList[
                                                  viewModel.getUsbButton1],
                                              feedBackControl: null,
                                            ),
                                            grp_btn_usb2_h70(
                                              onTap: () => viewModel
                                                  .onUsb2ButtonClicked(context),
                                              usbInfo: state.usbInfoList[
                                                  viewModel.getUsbButton2],
                                              feedBackControl: null,
                                            ),
                                          ],
                                  ),
                                ),
                                const Spacer(flex: 12),
                              ],
                            ),
                          ),
                          const Spacer(flex: 105),
                          Expanded(
                            flex: 80,
                            child: Row(
                              children: [
                                const Spacer(flex: 12),
                                Expanded(
                                  flex: 152,
                                  child: grp_btn_cancel(
                                    text: l10n.icon_cancel,
                                    onTap: () =>
                                        PopupNavigator.pop(context: context),
                                  ),
                                ),
                                const Spacer(flex: 624),
                              ],
                            ),
                          ),
                          const Spacer(flex: 12),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const Spacer(flex: 6),
            ],
          ),
        ),
        const Spacer(flex: 69),
      ],
    );
  }
}
