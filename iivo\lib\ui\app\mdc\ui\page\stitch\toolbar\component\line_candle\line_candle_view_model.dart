import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/device_info_model.dart';
import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/line_candle_model.dart';
import '../../../../../../model/stitch/toolbar_model.dart';
import '../../../stitch_page_view_model.dart';
import 'line_candle_view_interface.dart';

typedef Unit = DisplayUnit;

///
/// 白い背景に黒いテキスト
///
const TextStyle blackTextWhiteBackground =
    TextStyle(color: Colors.white, backgroundColor: Colors.black);

///
/// 黒い背景に白いテキスト
///
const TextStyle whiteTextBlackBackground =
    TextStyle(color: Colors.black, backgroundColor: Colors.white);

/// 読み出したSizeデータの変換倍率 '読み出したデータの単位/10=mm'
const double _conversionRate = 10.0;

typedef LineCandleViewModelProvider = AutoDisposeStateNotifierProvider<
    LineCandleViewInterface, LineCandleState>;

class LineCandleViewModel extends LineCandleViewInterface {
  LineCandleViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineCandleState(
              sizeDisplayValue: "",
              sizeDisplayDefault: false,
              spaceDisplayValue: "",
              spaceDisplayDefault: false,
            ),
            ref) {
    update();
  }

  ///
  /// 単位取得する
  ///
  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  ///
  /// キャンドルウィックステッチの既定値
  ///
  final defaultSizeValue = LineCandleModel().sizeDefaultValue;
  final defaultSpaceValue = LineCandleModel().spaceDefaultValue;

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    state = state.copyWith(
      sizeDisplayValue: _getSizeDisplayValue(),
      sizeDisplayDefault: _getSizeDefault(),
      spaceDisplayValue: _getSpaceDisplayValue(),
      spaceDisplayDefault: _getSpaceDisplayDefault(),
    );
  }

  ///
  /// サイズ設定ポップアップウィンドウを開きます
  ///
  @override
  void openSizeSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineCandleSize.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// 間隔設定ポップアップウィンドウを開く
  ///
  @override
  void openSpaceSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineCandleSpace.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// 大きさの表示値を取得する
  ///
  String _getSizeDisplayValue() {
    /// cmからmmへ
    double sizeValue = LineCandleModel().getSize() / _conversionRate;

    if (LineCandleModel().getSize() == LineCandleModel.sizeNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return sizeValue.toStringAsFixed(1);
    }

    return ToolbarModel.getDisplayInchShowValue(sizeValue);
  }

  ///
  /// 大きさ表示テキストスタイルを取得します
  ///
  bool _getSizeDefault() {
    int size = LineCandleModel().getSize();
    if (size == defaultSizeValue || size == LineCandleModel.sizeNotUpdating) {
      return true;
    }

    return false;
  }

  ///
  /// 間隔の表示値を取得します
  ///
  String _getSpaceDisplayValue() {
    int space = LineCandleModel().getSpace();

    /// cmからmmへ
    double spaceValue = space / _conversionRate;

    if (space == LineCandleModel.spacingNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return spaceValue.toStringAsFixed(1);
    }

    return ToolbarModel.getDisplayInchShowValue(spaceValue);
  }

  ///
  /// 間隔表示テキスト スタイルを取得します
  ///
  bool _getSpaceDisplayDefault() {
    int space = LineCandleModel().getSpace();
    if (space == defaultSpaceValue ||
        space == LineCandleModel.spacingNotUpdating) {
      return true;
    }
    return false;
  }
}
