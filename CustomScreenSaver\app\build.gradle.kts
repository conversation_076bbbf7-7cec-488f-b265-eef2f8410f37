plugins {
    alias(libs.plugins.androidApplication)
    alias(libs.plugins.jetbrainsKotlinAndroid)
}

android {
    namespace = "com.brother.ph.iivo.screensaver"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.brother.ph.iivo.screensaver"
        minSdk = 29
        targetSdk = 34
        versionCode = 1
        /// 重要事項：Release時は(dev)タグを削除してください。TODO：自動化したい
        versionName = "1.1.1 (dev)"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
    }

    signingConfigs {

        create("machine") {
            storeFile = file("key/platform.jks")
            keyAlias = "systemKey"
            keyPassword = "123456"
            storePassword = "123456"
        }

        create("androidSim") {
            storeFile = file("key/platform.jks")
            keyAlias = "systemkey"
            keyPassword = "123456"
            storePassword = "123456"
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("androidSim")
        }
        debug {
            signingConfig = signingConfigs.getByName("androidSim")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    buildFeatures {
        compose = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.1"
    }
    packagingOptions {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

dependencies {
  implementation("com.github.bumptech.glide:glide:4.12.0")
  annotationProcessor("com.github.bumptech.glide:compiler:4.12.0")
  implementation("androidx.recyclerview:recyclerview:1.3.0")
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}
