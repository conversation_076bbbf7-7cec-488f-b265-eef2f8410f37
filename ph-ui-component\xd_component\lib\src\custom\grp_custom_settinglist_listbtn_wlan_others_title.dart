// ignore_for_file: camel_case_types

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';

import '../../xd_component.dart';

class grp_custom_settinglist_listbtn_wlan_others_title
    extends StatelessWidget {
  const grp_custom_settinglist_listbtn_wlan_others_title({
    super.key,
    this.onTap,
    this.voiceControlFunction,
    this.centerLText = "",
    this.state = ButtonState.normal,
    this.feedBackControl = const FeedBackControl(),
  });

  final void Function()? onTap;
  final void Function()? voiceControlFunction;
  final String centerLText;
  final ButtonState state;
  final FeedBackControl? feedBackControl;
  @override
  Widget build(BuildContext context) => Stack(
        children: [
          custom_settinglist_listbtn_wifi1line(
            centerLText: centerLText,
            state: state,
            onTap: onTap,
            feedBackControl: feedBackControl,
          ),
          const Column(
            children: [
              Spacer(flex: 33),
              Expanded(
                flex: 46,
                child: Row(
                  children: [
                    Spacer(flex: 738),
                    Expanded(
                      flex: 30,
                      child: IgnorePointer(
                        child: ico_rightarrow(),
                      ),
                    ),
                    Spacer(flex: 8),
                  ],
                ),
              ),
              Spacer(flex: 32),
            ],
          ),
        ],
      );
}
