param(
  [Alias('a')]
  [string]$ApkPath = ''
)

# 重新构建
$requestRebuild = $args -contains '-r' -or $args -contains '-R'
# 重新安装
$forceInstall = $args -contains '-f' -or $args -contains '-F'
# 是否为Panel App，不指定则为Brother App
$isPanel = $args -contains '-p' -or $args -contains '-P'
# 打开/关闭设置
$openSettings = $args -contains '-s' -or $args -contains '-S'

# 获取设备列表并提取实机设备
$machineDevice = & adb.exe devices | Select-String -Pattern '^(?!List of devices attached$)(\S+)\s+device$' | ForEach-Object { $_.Matches[0].Groups[1].Value } | Where-Object { $_ -notmatch 'emulator' }

function Test-ExitCodeOrThrow {
  param(
    [string]$ErrorMessage
  )
  if ($LASTEXITCODE -ne 0) {
    throw $ErrorMessage
  }
}


try {
  if (-not $machineDevice -or $machineDevice -eq '') {
    throw 'No device found, please connect a device.'
  }

  if ($openSettings) {
    # 获取当前设备正在运行的应用包名
    $currentPackage = & adb.exe -s $machineDevice shell dumpsys window | Select-String -Pattern 'mCurrentFocus=Window{.* (.*)\/' | ForEach-Object {
      $_.Matches[0].Groups[1].Value
    }
    Write-Host $currentPackage

    # 判断当前应用是否为Settings
    if ($currentPackage -eq 'com.android.settings') {
      # 如果是Settings，则强制停止
      & adb.exe -s $machineDevice shell am force-stop com.android.settings
    }
    else {
      # 如果不是Settings，则启动Settings
      & adb.exe -s $machineDevice shell am start -n com.android.settings/.Settings
    }
    return
  }

  $isDefaultPath = $true


  if ($ApkPath -and -not $ApkPath.EndsWith('.apk') -and -not $ApkPath.EndsWith(".apk`"")) {
    throw "$ApkPath is not a valid apk file"
  }


  # 如果用户没有输入，则使用默认路径
  if (-not $ApkPath) {
    $ApkPath = Join-Path $PSScriptRoot '..\iivo\build\app\outputs\flutter-apk\app-brothermachine-release.apk'
  }
  else {
    $isDefaultPath = $false
  }
  $shouldRebuild = $isDefaultPath -and $requestRebuild
  if ($shouldRebuild) {
    Push-Location (Join-Path $PSScriptRoot '..\iivo')
    try {
      if ($isPanel) {
        fvm flutter build apk -t lib/panel.dart --flavor=panelmachine --release  --target-platform=android-arm
      }
      else {
        fvm flutter build apk -t lib/panel.dart --flavor=brothermachine --release  --target-platform=android-arm
      }
      Test-ExitCodeOrThrow 'Build failed, please check the build log.'
    }
    finally {
      Pop-Location
    }
  }
  $shouldInstall = $forceInstall -or (-not $isDefaultPath -or $requestRebuild)
  # 执行安装命令
  if ($shouldInstall) {
    Write-Host "device: $machineDevice"
    & adb.exe -s $machineDevice install $ApkPath
    Test-ExitCodeOrThrow 'Installation failed, please retry with `run_brother.ps1 -f`'
  }
  else {
    Write-Host 'No need to install apk.'
  }
  Write-Host 'Restart brother panel'

  # 在新 PowerShell 窗口中执行重启 Brother 面板的脚本，确保不受当前窗口影响
  Start-Process powershell (Join-Path $PSScriptRoot '.\restartbrotherpanel.ps1')
  # 延时5秒
  Start-Sleep -Seconds 5

  Write-Host 'Launch app'
  & adb.exe -s $machineDevice shell am force-stop com.brother.ph.iivo.screensaver
  & adb.exe -s $machineDevice shell am force-stop com.brother.ph.iivo.panel
  & adb.exe -s $machineDevice shell am force-stop com.brother.ph.iivo.home
  & adb.exe -s $machineDevice shell am force-stop com.brother.ph.iivo.brother
    
  # 安装成功，启动应用
  if (-not $isPanel) {
    & adb.exe -s $machineDevice shell am start -n com.brother.ph.iivo.brother/com.brother.ph.iivo.iivo.MainActivity
  }
  else {
    & adb.exe -s $machineDevice shell am start -n com.brother.ph.iivo.panel/com.brother.ph.iivo.iivo.MainActivity
  }
}
catch {
  Write-Host "[Error] $($_.Exception.Message)" -ForegroundColor Red
  exit 1
}
