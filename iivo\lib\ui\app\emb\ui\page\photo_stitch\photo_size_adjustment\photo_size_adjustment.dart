import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../component/emb_footer/emb_footer.dart';
import '../../../component/emb_header/emb_header.dart';
import 'photo_size_adjustment_view_model.dart';

class PhotoSizeAdjustment extends StatefulPage {
  const PhotoSizeAdjustment({super.key});

  @override
  PageState<PhotoSizeAdjustment> createState() => _PhotoSizeAdjustmentState();
}

class _PhotoSizeAdjustmentState extends PageState<PhotoSizeAdjustment> {
  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final viewModel = ref.read(photoSizeAdjustmentViewModelProvider.notifier);

    return Material(
      child: pre_base_embroidery(
        child: pre_base_gray(
          child: Column(
            children: [
              Expanded(
                flex: 1117,
                child: pre_base_white(
                  child: Column(
                    children: [
                      const Expanded(
                        flex: 71,
                        child: EmbHeader(),
                      ),
                      Expanded(
                        flex: 165,
                        child: Column(
                          children: [
                            const Spacer(flex: 11),
                            Expanded(
                              flex: 34,
                              child: Row(
                                children: [
                                  const Spacer(flex: 32),
                                  Expanded(
                                    flex: 734,
                                    child:
                                        grp_str_text(text: l10n.icon_photos_02),
                                  ),
                                  const Spacer(flex: 34),
                                ],
                              ),
                            ),
                            const Spacer(flex: 121),
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 529,
                        child: Row(
                          children: [
                            const Spacer(flex: 7),
                            Expanded(
                              flex: 786,
                              child: pre_square_base(
                                child: Row(
                                  children: [
                                    const Spacer(flex: 124),
                                    Consumer(
                                      builder: (context, notifier, child) {
                                        final (
                                          contentImage,
                                          imageWidth,
                                          imageHeight
                                        ) = notifier.watch(
                                          photoSizeAdjustmentViewModelProvider
                                              .select(
                                            (value) => (
                                              value.contentImage,
                                              value.imageWidth,
                                              value.imageHeight
                                            ),
                                          ),
                                        );
                                        return Expanded(
                                          flex: 539,
                                          child: Visibility(
                                            visible: contentImage != null,
                                            child: Stack(
                                              children: [
                                                const pic_photo_preview(),
                                                Center(
                                                  child: viewModel.isCircle
                                                      ? ClipOval(
                                                          child: ColoredBox(
                                                            color: Colors.red,
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .all(1),
                                                              child: Image(
                                                                fit:
                                                                    BoxFit.fill,
                                                                image:
                                                                    contentImage!
                                                                        .image,
                                                                width:
                                                                    imageWidth,
                                                                height:
                                                                    imageHeight,
                                                                gaplessPlayback:
                                                                    true,
                                                              ),
                                                            ),
                                                          ),
                                                        )
                                                      : Container(
                                                          width: imageWidth,
                                                          height: imageHeight,
                                                          decoration:
                                                              BoxDecoration(
                                                            border: Border.all(
                                                                color:
                                                                    Colors.red,
                                                                width: 1),
                                                            borderRadius: BorderRadius
                                                                .circular(viewModel
                                                                    .getContentImageRadius()),
                                                          ),
                                                          child: ClipRRect(
                                                            borderRadius: BorderRadius
                                                                .circular(viewModel
                                                                    .getContentImageRadius()),
                                                            child: Image(
                                                              fit: BoxFit.fill,
                                                              image:
                                                                  contentImage!
                                                                      .image,
                                                            ),
                                                          ),
                                                        ),
                                                )
                                              ],
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                    const Spacer(flex: 123),
                                  ],
                                ),
                              ),
                            ),
                            const Spacer(flex: 7),
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 352,
                        child: Column(
                          children: [
                            const Spacer(flex: 31),
                            Expanded(
                              flex: 64,
                              child: Row(
                                children: [
                                  const Spacer(flex: 35),
                                  const Expanded(
                                    flex: 32,
                                    child: Column(
                                      children: [
                                        Spacer(flex: 9),
                                        Expanded(
                                          flex: 32,
                                          child: ico_shape_size_01(),
                                        ),
                                        Spacer(flex: 23),
                                      ],
                                    ),
                                  ),
                                  const Spacer(flex: 8),
                                  Expanded(
                                    flex: 80,
                                    child: Column(
                                      children: [
                                        Expanded(
                                          flex: 30,
                                          child: Consumer(
                                            builder:
                                                (context, notifier, child) {
                                              final heightValue = notifier
                                                  .watch(
                                                      photoSizeAdjustmentViewModelProvider)
                                                  .heightValue;
                                              return grp_str_number1(
                                                text: heightValue,
                                                alignment:
                                                    Alignment.bottomRight,
                                                textAlign: TextAlign.right,
                                                textStyle: const TextStyle(
                                                  fontFamily: "Roboto",
                                                  fontSize: 30,
                                                  height: 39 / 30,
                                                  color: Colors.black,
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                        const Spacer(flex: 4),
                                        Expanded(
                                          flex: 30,
                                          child: Consumer(
                                            builder:
                                                (context, notifier, child) {
                                              final widthValue = notifier
                                                  .watch(
                                                      photoSizeAdjustmentViewModelProvider)
                                                  .widthValue;
                                              return grp_str_number2(
                                                text: widthValue,
                                                alignment:
                                                    Alignment.bottomRight,
                                                textAlign: TextAlign.right,
                                                textStyle: const TextStyle(
                                                  fontFamily: "Roboto",
                                                  fontSize: 30,
                                                  height: 39 / 30,
                                                  color: Colors.black,
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const Spacer(flex: 6),
                                  Expanded(
                                    flex: 50,
                                    child: Consumer(
                                      builder: (context, notifier, child) {
                                        final isInch = notifier
                                            .watch(
                                                photoSizeAdjustmentViewModelProvider)
                                            .isInch;
                                        return Column(
                                          children: [
                                            const Spacer(flex: 10),
                                            Expanded(
                                              flex: 20,
                                              child: grp_str_mm1(
                                                text: isInch
                                                    ? l10n.icon_00226
                                                    : l10n.icon_00225,
                                                alignment: Alignment.bottomLeft,
                                                textAlign: TextAlign.left,
                                                textStyle: const TextStyle(
                                                  fontFamily: "Roboto",
                                                  fontSize: 20,
                                                  height: 26 / 20,
                                                  color: Colors.black,
                                                ),
                                              ),
                                            ),
                                            const Spacer(flex: 14),
                                            Expanded(
                                              flex: 20,
                                              child: grp_str_mm2(
                                                text: isInch
                                                    ? l10n.icon_00226
                                                    : l10n.icon_00225,
                                                alignment: Alignment.bottomLeft,
                                                textAlign: TextAlign.left,
                                                textStyle: const TextStyle(
                                                  fontFamily: "Roboto",
                                                  fontSize: 20,
                                                  height: 26 / 20,
                                                  color: Colors.black,
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      },
                                    ),
                                  ),
                                  const Spacer(flex: 589),
                                ],
                              ),
                            ),
                            const Spacer(flex: 24),
                            Expanded(
                              flex: 70,
                              child: Row(
                                children: [
                                  const Spacer(flex: 80),
                                  Expanded(
                                    flex: 98,
                                    child: grp_btn_size_reduce(
                                      longPressTriggerTick: 4,
                                      onTap: () => viewModel
                                          .onXYReduceButtonClicked(false),
                                      onLongPress: () => viewModel
                                          .onXYReduceButtonClicked(true),
                                      onTapUp: viewModel.onTapeCancel,
                                      onTapCancel: viewModel.onTapeCancel,
                                      feedBackControl:
                                          FeedBackControl.onlyDisable,
                                    ),
                                  ),
                                  const Spacer(flex: 8),
                                  Expanded(
                                    flex: 98,
                                    child: grp_btn_size_hight_reduce(
                                      longPressTriggerTick: 4,
                                      onTap: () => viewModel
                                          .onYReduceButtonClicked(false),
                                      onLongPress: () => viewModel
                                          .onYReduceButtonClicked(true),
                                      onTapUp: viewModel.onTapeCancel,
                                      onTapCancel: viewModel.onTapeCancel,
                                      feedBackControl:
                                          FeedBackControl.onlyDisable,
                                    ),
                                  ),
                                  const Spacer(flex: 8),
                                  Expanded(
                                    flex: 98,
                                    child: grp_btn_size_width_reduce(
                                      longPressTriggerTick: 4,
                                      onTap: () => viewModel
                                          .onXReduceButtonClicked(false),
                                      onLongPress: () => viewModel
                                          .onXReduceButtonClicked(true),
                                      onTapUp: viewModel.onTapeCancel,
                                      onTapCancel: viewModel.onTapeCancel,
                                      feedBackControl:
                                          FeedBackControl.onlyDisable,
                                    ),
                                  ),
                                  const Spacer(flex: 8),
                                  Expanded(
                                    flex: 98,
                                    child: CustomTooltip(
                                      message: l10n
                                          .tt_emb_photositich_size_change_reset,
                                      child: grp_btn_reset(
                                        text: l10n.icon_reset,
                                        onTap: viewModel.onResetButtonClicked,
                                        isEnglish: false,
                                        feedBackControl: null,
                                      ),
                                    ),
                                  ),
                                  const Spacer(flex: 126),
                                  Expanded(
                                    flex: 98,
                                    child: grp_btn_rotate_right90_01(
                                      onTapDown: (p0) {
                                        viewModel.onRotateRight90();
                                      },
                                    ),
                                  ),
                                  const Spacer(flex: 80),
                                ],
                              ),
                            ),
                            const Spacer(flex: 8),
                            Expanded(
                              flex: 70,
                              child: Row(
                                children: [
                                  const Spacer(flex: 80),
                                  Expanded(
                                    flex: 98,
                                    child: grp_btn_size_enlarge(
                                      longPressTriggerTick: 4,
                                      onTap: () => viewModel
                                          .onXYEnlargeButtonClicked(false),
                                      onLongPress: () => viewModel
                                          .onXYEnlargeButtonClicked(true),
                                      onTapUp: viewModel.onTapeCancel,
                                      onTapCancel: viewModel.onTapeCancel,
                                      feedBackControl:
                                          FeedBackControl.onlyDisable,
                                    ),
                                  ),
                                  const Spacer(flex: 8),
                                  Expanded(
                                    flex: 98,
                                    child: grp_btn_size_hight_enlarge(
                                      longPressTriggerTick: 4,
                                      onTap: () => viewModel
                                          .onYEnlargeButtonClicked(false),
                                      onLongPress: () => viewModel
                                          .onYEnlargeButtonClicked(true),
                                      onTapUp: viewModel.onTapeCancel,
                                      onTapCancel: viewModel.onTapeCancel,
                                      feedBackControl:
                                          FeedBackControl.onlyDisable,
                                    ),
                                  ),
                                  const Spacer(flex: 8),
                                  Expanded(
                                      flex: 98,
                                      child: grp_btn_size_width_enlarge(
                                        longPressTriggerTick: 4,
                                        onTap: () => viewModel
                                            .onXEnlargeButtonClicked(false),
                                        onLongPress: () => viewModel
                                            .onXEnlargeButtonClicked(true),
                                        onTapUp: viewModel.onTapeCancel,
                                        onTapCancel: viewModel.onTapeCancel,
                                        feedBackControl:
                                            FeedBackControl.onlyDisable,
                                      )),
                                  const Spacer(flex: 410),
                                ],
                              ),
                            ),
                            const Spacer(flex: 85),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                flex: 102,
                child: Stack(
                  children: [
                    const pre_base_embroidery(),
                    Row(
                      children: [
                        const Spacer(flex: 12),
                        Expanded(
                          flex: 152,
                          child: Column(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 80,
                                child: grp_btn_return(
                                  buttonText: l10n.icon_return,
                                  onTap: () =>
                                      viewModel.onReturnButtonClicked(),
                                ),
                              ),
                              const Spacer(flex: 10),
                            ],
                          ),
                        ),
                        const Spacer(flex: 102),
                        Expanded(
                          flex: 84,
                          child: Column(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 80,
                                child: CustomTooltip(
                                  message: l10n.tt_emb_photostitch_backremoval,
                                  child: grp_backgroundremove(
                                    onTap: () => viewModel
                                        .onBackgroundRemoveButtonClicked(
                                            context),
                                  ),
                                ),
                              ),
                              const Spacer(flex: 10),
                            ],
                          ),
                        ),
                        const Spacer(flex: 8),
                        Expanded(
                          flex: 84,
                          child: Column(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 80,
                                child: CustomTooltip(
                                  message: l10n.tt_emb_photostitch_framing,
                                  child: grp_btn_mask(
                                    onTap: () =>
                                        viewModel.onMaskButtonClicked(context),
                                  ),
                                ),
                              ),
                              const Spacer(flex: 10),
                            ],
                          ),
                        ),
                        const Spacer(flex: 8),
                        Expanded(
                          flex: 84,
                          child: Column(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 80,
                                child: CustomTooltip(
                                  message: l10n.tt_emb_photostitch_fittoframe,
                                  child: grp_btn_fittoframe(
                                    onTap: () => viewModel
                                        .onFrameFitButtonClicked(context),
                                  ),
                                ),
                              ),
                              const Spacer(flex: 10),
                            ],
                          ),
                        ),
                        const Spacer(flex: 102),
                        Expanded(
                          flex: 152,
                          child: Column(
                            children: [
                              const Spacer(flex: 12),
                              Expanded(
                                flex: 80,
                                child: grp_btn_next(
                                  buttonText: l10n.icon_00080,
                                  onTap: viewModel.onNextButtonClicked,
                                ),
                              ),
                              const Spacer(flex: 10),
                            ],
                          ),
                        ),
                        const Spacer(flex: 12),
                      ],
                    ),
                  ],
                ),
              ),
              const Expanded(
                flex: 61,
                child: EmbFooter(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup(WidgetRef ref) => ref
      .read(photoSizeAdjustmentViewModelProvider.notifier)
      .registerNamedPopup();
}
