import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

part 'line_rough_zigzag_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class LineRoughZigzagState with _$LineRoughZigzagState {
  const factory LineRoughZigzagState({
    required String widthDisplayValue,
    required String densityDisplayValue,
    required bool isWidthDefaultValue,
    required bool isDensityDefaultValue,
  }) = _LineRoughZigzagState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class LineRoughZigzagStateViewInterface
    extends ViewModel<LineRoughZigzagState> {
  LineRoughZigzagStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// 密度設定ポップアップを開く/閉める
  ///
  void openDensitySettingPopup(context);

  ///
  /// 密度設定ポップアップを開く/閉める
  ///
  void openWidthSettingPopup(context);

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;

  ///
  /// ラインジグザグプロパティの既定値
  ///
  int get defaultDensityIndex;
  int get defaultWidthValue;
}
