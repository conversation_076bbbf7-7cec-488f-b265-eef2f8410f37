import 'app_localizations.dart';

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get color_001 => 'ROSADO';

  @override
  String get color_002 => 'ROSA CENIZA';

  @override
  String get color_003 => 'ROSA PÉTALO';

  @override
  String get color_004 => 'ROSA CARNE';

  @override
  String get color_005 => 'CORAL CLARO';

  @override
  String get color_006 => 'JENGIBRE\nROSADO';

  @override
  String get color_007 => 'BREZO\nROSADO';

  @override
  String get color_008 => 'CHAMPÁN\nROSADO';

  @override
  String get color_009 => 'MALVA\nOSCURO';

  @override
  String get color_010 => 'BREZO';

  @override
  String get color_011 => 'ROSA\nCONFORT';

  @override
  String get color_012 => 'ROSA\nMONTAÑA';

  @override
  String get color_013 => 'ROSA CEREZO';

  @override
  String get color_014 => 'ENCARNADO';

  @override
  String get color_015 => 'SALMÓN';

  @override
  String get color_016 => 'CAMARÓN';

  @override
  String get color_017 => 'CORAL\nOSCURO';

  @override
  String get color_018 => 'SALMÓN\nROSADO';

  @override
  String get color_019 => 'BURDEOS';

  @override
  String get color_020 => 'GRANATE\nCÁLIDO';

  @override
  String get color_021 => 'ROJIZO';

  @override
  String get color_022 => 'CIRUELA';

  @override
  String get color_023 => 'GRANATE';

  @override
  String get color_024 => 'BLASÓN REAL';

  @override
  String get color_025 => 'ROSA FUERTE';

  @override
  String get color_026 => 'RUBÍ';

  @override
  String get color_027 => 'FUCSIA\nOSCURO';

  @override
  String get color_028 => 'CARMIN';

  @override
  String get color_029 => 'ROSA INTENSO';

  @override
  String get color_030 => 'BEGOÑA';

  @override
  String get color_031 => 'AZALEA';

  @override
  String get color_032 => 'ROJO RUBÍN';

  @override
  String get color_033 => 'FRESA';

  @override
  String get color_034 => 'ROJO INFIERNO';

  @override
  String get color_035 => 'ROJO MANZ.\nCARAMELO';

  @override
  String get color_036 => 'ROJO MALVA';

  @override
  String get color_037 => 'ROJO\nTOSTADO';

  @override
  String get color_038 => 'ROJO FUEGO';

  @override
  String get color_039 => 'ROJO';

  @override
  String get color_040 => 'ROJO JOCKEY';

  @override
  String get color_041 => 'ROJO\nRADIANTE';

  @override
  String get color_042 => 'ROJO BERRY';

  @override
  String get color_043 => 'ROJO INTENSO';

  @override
  String get color_044 => 'ROJO PINTA\nLABIOS';

  @override
  String get color_045 => 'ROJO\nNAVIDAD';

  @override
  String get color_046 => 'ESCARLATA';

  @override
  String get color_047 => 'ESCARLATA\nINTENSO';

  @override
  String get color_048 => 'ARÁNDANO';

  @override
  String get color_049 => 'ROSA CLARO';

  @override
  String get color_050 => 'ROSA BISQUE';

  @override
  String get color_051 => 'ROSA\nFLAMINGO';

  @override
  String get color_052 => 'MELÓN';

  @override
  String get color_053 => 'SALMÓN\nDURAZNO';

  @override
  String get color_054 => 'NARANJA\nROSADO';

  @override
  String get color_055 => 'NARANJA\nOSCURO';

  @override
  String get color_056 => 'AZUL PÁLIDO';

  @override
  String get color_057 => 'AZUL ÓPALO';

  @override
  String get color_058 => 'AZUL HIELO';

  @override
  String get color_059 => 'AZUL MEDIO';

  @override
  String get color_060 => 'AZUL\nROCKPORT';

  @override
  String get color_061 => 'AZUL PASTEL';

  @override
  String get color_062 => 'AZUL BEBÉ';

  @override
  String get color_063 => 'CELESTE';

  @override
  String get color_064 => 'AZUL CLARO';

  @override
  String get color_065 => 'AZUL LAGO';

  @override
  String get color_066 => 'AZUL FELIZ';

  @override
  String get color_067 => 'AZUL ULTRA';

  @override
  String get color_068 => 'AZUL TRÓPICO';

  @override
  String get color_069 => 'AZUL\nCENTAURA';

  @override
  String get color_070 => 'AZUL LUNA';

  @override
  String get color_071 => 'ZAFIRO';

  @override
  String get color_072 => 'AZUL PIZARRA';

  @override
  String get color_073 => 'AZUL OSCURO';

  @override
  String get color_074 => 'AZUL INTENSO';

  @override
  String get color_075 => 'AZUL\nPROFUNDO';

  @override
  String get color_076 => 'AZUL BÁLTICO';

  @override
  String get color_077 => 'AZUL\nCALIFORNIA';

  @override
  String get color_078 => 'AZUL CERÚLEO';

  @override
  String get color_079 => 'AZUL SOLAR';

  @override
  String get color_080 => 'AZUL\nELECTRICO';

  @override
  String get color_081 => 'AZUL PACÍFICO';

  @override
  String get color_082 => 'AZUL';

  @override
  String get color_083 => 'AZUL IMPERIAL';

  @override
  String get color_084 => 'AZUL\nESCUADRA';

  @override
  String get color_085 => 'AZUL LAZO';

  @override
  String get color_086 => 'AZUL MARINO\nCLARO';

  @override
  String get color_087 => 'AZUL MARINO\nMEDIO';

  @override
  String get color_088 => 'AZUL IMPERIO';

  @override
  String get color_089 => 'AZUL\nNOCTURNO';

  @override
  String get color_090 => 'ZAFIRO\nOSCURO';

  @override
  String get color_091 => 'AZUL ANTE';

  @override
  String get color_092 => 'AZUL DE\nULTRAMAR';

  @override
  String get color_093 => 'AZUL REAL';

  @override
  String get color_094 => 'AZUL COBALTO';

  @override
  String get color_095 => 'AZUL DE\nPRUSIA';

  @override
  String get color_096 => 'AZUL NASAO';

  @override
  String get color_097 => 'AZUL CHOW';

  @override
  String get color_098 => 'AZUL PARÍS';

  @override
  String get color_099 => 'LAVANDA\nAZUL';

  @override
  String get color_100 => 'LAVANDA\nMEDIO';

  @override
  String get color_101 => 'LAVANDA';

  @override
  String get color_102 => 'LAVANDA\nTULIPÁN';

  @override
  String get color_103 => 'VIOLETA\nVISTARIA';

  @override
  String get color_104 => 'MORADO\nREINA';

  @override
  String get color_105 => 'LAVANDA\nPÁLIDA';

  @override
  String get color_106 => 'MORADO\nCHARIOT';

  @override
  String get color_107 => 'MORADO MAÍZ';

  @override
  String get color_108 => 'MORADO\nINTENSO';

  @override
  String get color_109 => 'MORADO\nACCENT';

  @override
  String get color_110 => 'MORADO REY';

  @override
  String get color_111 => 'VIOLETA REAL';

  @override
  String get color_112 => 'MORADO MOD';

  @override
  String get color_113 => 'PURPURA';

  @override
  String get color_114 => 'VIOLETA';

  @override
  String get color_115 => 'MORADO\nPÁLIDO';

  @override
  String get color_116 => 'MAGENTA';

  @override
  String get color_117 => 'LILA SUAVE';

  @override
  String get color_118 => 'LILA';

  @override
  String get color_119 => 'IRIS\nSIBERIANO';

  @override
  String get color_120 => 'ROSA\nFANTASÍA';

  @override
  String get color_121 => 'ROSA SUEÑO';

  @override
  String get color_122 => 'ROSA\nEXCLUSIVO';

  @override
  String get color_123 => 'ROSA BRILLO';

  @override
  String get color_124 => 'ROSA\nSALVAJE';

  @override
  String get color_125 => 'ROJO AMBAR';

  @override
  String get color_126 => 'FRAMBUESA';

  @override
  String get color_127 => 'BERENJENA';

  @override
  String get color_128 => 'AZUL BAMBINO';

  @override
  String get color_129 => 'AZUL FRANJA';

  @override
  String get color_130 => 'AGUA MARINA';

  @override
  String get color_131 => 'AZUL\nWISTARIA';

  @override
  String get color_132 => 'AZUL ÁNGEL';

  @override
  String get color_133 => 'AZUL MALLARD';

  @override
  String get color_134 => 'AZUL PAVO\nREAL';

  @override
  String get color_135 => 'JADE OSCURO';

  @override
  String get color_136 => 'VERDE\nPARAÍSO';

  @override
  String get color_137 => 'AZUL CERCETA';

  @override
  String get color_138 => 'AZUL VENUS';

  @override
  String get color_139 => 'AZUL PLUMA';

  @override
  String get color_140 => 'MENTA JULEPE';

  @override
  String get color_141 => 'MAR CRISTAL';

  @override
  String get color_142 => 'TURQUESA';

  @override
  String get color_143 => 'CERCETA\nMíSTICA';

  @override
  String get color_144 => 'VERDE MAR';

  @override
  String get color_145 => 'VERDE\nOCÉANO';

  @override
  String get color_146 => 'TURQUESA\nTEMPESTAD';

  @override
  String get color_147 => 'VERDE MD';

  @override
  String get color_148 => 'CERCETA\nOSCURA';

  @override
  String get color_149 => 'VERDE MAR\nOCÉANO';

  @override
  String get color_150 => 'PINO REAL';

  @override
  String get color_151 => 'VERDE\nNEWPORT';

  @override
  String get color_152 => 'VERDE BAHÍA';

  @override
  String get color_153 => 'VERDE\nCELADON';

  @override
  String get color_154 => 'VERDE KIWI';

  @override
  String get color_155 => 'VERDE OLIVA';

  @override
  String get color_156 => 'VERDE PUERTO';

  @override
  String get color_157 => 'VERDE\nESPECIAL';

  @override
  String get color_158 => 'VERDE OSCURO\nMILITAR';

  @override
  String get color_159 => 'VERDE FUERTE';

  @override
  String get color_160 => 'VERDE ALPINO';

  @override
  String get color_161 => 'VERDE CAMPO';

  @override
  String get color_162 => 'VERDE VELA';

  @override
  String get color_163 => 'CERCETA\nNOCTURNA';

  @override
  String get color_164 => 'BRUMA\nMARÍNA';

  @override
  String get color_165 => 'VERDE SAUCE';

  @override
  String get color_166 => 'VERDE HOJA\nTÉ';

  @override
  String get color_167 => 'VERDE ISLA';

  @override
  String get color_168 => 'VERDE PINO';

  @override
  String get color_169 => 'JADE';

  @override
  String get color_170 => 'PIPERMÍN';

  @override
  String get color_171 => 'VERDE\nAZULADO';

  @override
  String get color_172 => 'VERDE\nINTENSO';

  @override
  String get color_173 => 'VERDE\nCLÁSICO';

  @override
  String get color_174 => 'VERDE PINO\nOSCURO';

  @override
  String get color_175 => 'VERDE';

  @override
  String get color_176 => 'VERDE\nIRLANDÉS';

  @override
  String get color_177 => 'VERDE\nESMERALDA';

  @override
  String get color_178 => 'VERDE TRÉBOL';

  @override
  String get color_179 => 'VERDE MUSGO';

  @override
  String get color_180 => 'VERDE KELLY\nCLARO';

  @override
  String get color_181 => 'VERDE KELLY';

  @override
  String get color_182 => 'VERDE DE\nHOJA';

  @override
  String get color_183 => 'VERDE CLARO';

  @override
  String get color_184 => 'VERDE ROBLE';

  @override
  String get color_185 => 'VERDE MENTA';

  @override
  String get color_186 => 'VERDE FRESCO';

  @override
  String get color_187 => 'VERDE VAINA\nGUISANTE';

  @override
  String get color_188 => 'VERDE\nPASTORAL';

  @override
  String get color_189 => 'VERDE AGUA-\nCATE CLARO';

  @override
  String get color_190 => 'VERDE\nVENDIMIA';

  @override
  String get color_191 => 'POLVO\nVERDOSO';

  @override
  String get color_192 => 'VERDE LIMA';

  @override
  String get color_193 => 'VERDE ERIN';

  @override
  String get color_194 => 'VERDE\nFOLLAJE';

  @override
  String get color_195 => 'AMARILLO\nGIRASOL';

  @override
  String get color_196 => 'DORADO';

  @override
  String get color_197 => 'AMARILLO\nOTOÑO';

  @override
  String get color_198 => 'VERDE OLIVA\nCLARO';

  @override
  String get color_199 => 'VERDE PRADO';

  @override
  String get color_200 => 'VERDE SALVIA';

  @override
  String get color_201 => 'VERDE OLIVA\nOSCURO';

  @override
  String get color_202 => 'VERDE MILITAR';

  @override
  String get color_203 => 'DORADO\nCORONACIÓN';

  @override
  String get color_204 => 'AMARILLO\nLIMON';

  @override
  String get color_205 => 'AMARILLO\nCHILLÓN';

  @override
  String get color_206 => 'AMARILLO';

  @override
  String get color_207 => 'DORADO\nOMBRE';

  @override
  String get color_208 => 'MANILA';

  @override
  String get color_209 => 'DORADO\nOSCURO';

  @override
  String get color_210 => 'AMARILLO SOL';

  @override
  String get color_211 => 'DORADO\nPOLEN';

  @override
  String get color_212 => 'DAY LILLY';

  @override
  String get color_213 => 'DORADO\nESTRELLA';

  @override
  String get color_214 => 'DORADO SOL';

  @override
  String get color_215 => 'BRONCE';

  @override
  String get color_216 => 'NARANJA';

  @override
  String get color_217 => 'AMARILLO ORO';

  @override
  String get color_218 => 'AMARILLO\nTRIGO';

  @override
  String get color_219 => 'AMARILLO\nNIEBLA';

  @override
  String get color_220 => 'MOSTAZA';

  @override
  String get color_221 => 'COBRE';

  @override
  String get color_222 => 'NARANJA\nROJIZO';

  @override
  String get color_223 => 'NARANJADA';

  @override
  String get color_224 => 'PIMENTÓN';

  @override
  String get color_225 => 'BERMELLON';

  @override
  String get color_226 => 'AZAFRÁN';

  @override
  String get color_227 => 'CAOBA';

  @override
  String get color_228 => 'TERRACOTA';

  @override
  String get color_229 => 'TEJA OSCURO';

  @override
  String get color_230 => 'MELÓN\nOSCURO';

  @override
  String get color_231 => 'BRONCEADO\nINTENSO';

  @override
  String get color_232 => 'NARANJA\nSUAVE';

  @override
  String get color_233 => 'TOSTADO\nINTENSO';

  @override
  String get color_234 => 'ALBARICOQUE\nOSCURO';

  @override
  String get color_235 => 'TANGERINA';

  @override
  String get color_236 => 'CALABAZA';

  @override
  String get color_237 => 'QUEMADO';

  @override
  String get color_238 => 'DORADO\nPICANTE';

  @override
  String get color_239 => 'MIEL';

  @override
  String get color_240 => 'ALMENDRA';

  @override
  String get color_241 => 'MARRON\nROJIZO';

  @override
  String get color_242 => 'MARRON\nTIERRA';

  @override
  String get color_243 => 'MARRON\nBERMEJO';

  @override
  String get color_244 => 'MARRON\nCREMA';

  @override
  String get color_245 => 'AMARILLO\nCREMA';

  @override
  String get color_246 => 'RESPLANDOR';

  @override
  String get color_247 => 'PISTACHO';

  @override
  String get color_248 => 'DORADO\nCANELA';

  @override
  String get color_249 => 'JENGIBRE';

  @override
  String get color_250 => 'DORADO\nTEMPLO';

  @override
  String get color_251 => 'CANELA';

  @override
  String get color_252 => 'BRONCEADO\nSUAVE';

  @override
  String get color_253 => 'CARAMELO';

  @override
  String get color_254 => 'BEIGE';

  @override
  String get color_255 => 'TOSTADO\nSUAVE';

  @override
  String get color_256 => 'BEIGE OSCURO';

  @override
  String get color_257 => 'LATÓN';

  @override
  String get color_258 => 'CAFÉ';

  @override
  String get color_259 => 'LINO';

  @override
  String get color_260 => 'CONCHA\nMARINA';

  @override
  String get color_261 => 'ECRU';

  @override
  String get color_262 => 'ROSA SALMON';

  @override
  String get color_263 => 'CACAO CLARO';

  @override
  String get color_264 => 'NARANJA\nCENICA';

  @override
  String get color_265 => 'MARRON\nCLARO';

  @override
  String get color_266 => 'CAQUI';

  @override
  String get color_267 => 'MARRÓN\nCAFÉ';

  @override
  String get color_268 => 'MARRÓN\nPIEDRA';

  @override
  String get color_269 => 'MARRÓN\nINTENSO';

  @override
  String get color_270 => 'MARRON\nOSCURO';

  @override
  String get color_271 => 'MARRÓN';

  @override
  String get color_272 => 'HAPPY TRAIL';

  @override
  String get color_273 => 'TAUPE\nOSCURO';

  @override
  String get color_274 => 'GRIS CALIENTE';

  @override
  String get color_275 => 'GRIS INTENSO';

  @override
  String get color_276 => 'METAL';

  @override
  String get color_277 => 'NEGRO CROMO';

  @override
  String get color_278 => 'CARBÓN';

  @override
  String get color_279 => 'GRIS MEDIO';

  @override
  String get color_280 => 'GRIS FRÍO';

  @override
  String get color_281 => 'GRIS HUMO';

  @override
  String get color_282 => 'PELTRE';

  @override
  String get color_283 => 'GRIS OSCURO';

  @override
  String get color_284 => 'GRIS';

  @override
  String get color_285 => 'GRIS CLARO';

  @override
  String get color_286 => 'CROMO';

  @override
  String get color_287 => 'ORO VIEJO';

  @override
  String get color_288 => 'PLATA';

  @override
  String get color_289 => 'NEGRO';

  @override
  String get color_290 => 'BLANCO\nNATURAL';

  @override
  String get color_291 => 'BLANCO';

  @override
  String get color_292 => 'ROSA NEÓN';

  @override
  String get color_293 => 'ROSA OSCURO';

  @override
  String get color_294 => 'SANDÍA';

  @override
  String get color_295 => 'ROSA SUAVE';

  @override
  String get color_296 => 'NARANJA\nROS. CLARO';

  @override
  String get color_297 => 'NARANJA\nNIEBLA';

  @override
  String get color_298 => 'AMARILLO\nHABANA';

  @override
  String get color_299 => 'CEREZO\nOSCURO';

  @override
  String get color_300 => 'AZUL TINTA';

  @override
  String get color_301 => 'MATERIAL DEL\nAPLIQUE';

  @override
  String get color_302 => 'POSICIÓN DEL\nAPLIQUE';

  @override
  String get color_303 => 'APLIQUE';

  @override
  String get id_icon_test00001 => '\$\$\$\$\$';

  @override
  String get icon_00002 => 'Coser';

  @override
  String get icon_00003_1 => 'Bordado';

  @override
  String get icon_00006_3 => 'Costura de\nutilidad';

  @override
  String get icon_00007_3 => 'Costura\ndecorativa o\nde caracteres';

  @override
  String get icon_stitch => 'Costura';

  @override
  String get icon_close_1 => 'Cerrar';

  @override
  String get icon_cancel => 'Cancelar';

  @override
  String get icon_ok => 'OK';

  @override
  String get icon_00011_zz => '%%%icon%%%';

  @override
  String get icon_00011_zz_s => '%%%icon%%%';

  @override
  String get icon_00011 => 'Elim.';

  @override
  String get icon_00012_zz => '%%%icon%%%';

  @override
  String get icon_00012_zz_s => '%%%icon%%%';

  @override
  String get icon_reset_zz => '%%%icon%%%';

  @override
  String get icon_reset_zz_s => '%%%icon%%%';

  @override
  String get icon_reset => 'Reiniciar';

  @override
  String get icon_reset_v => 'Borrado';

  @override
  String get icon_00014_zz => '%%%icon%%%';

  @override
  String get icon_00014_zz_s => '%%%icon%%%';

  @override
  String get icon_00014 => 'Memoria';

  @override
  String get icon_save => 'Guardar';

  @override
  String get icon_00015_zz => '%%%icon%%%';

  @override
  String get icon_00015_zz_s => '%%%icon%%%';

  @override
  String get icon_util_mem_retrieve => 'Recuperar';

  @override
  String get icon_util_mem_memory => 'Memoria';

  @override
  String get icon_util_mem_reset => 'Borrado';

  @override
  String get icon_util_mem_delete => 'Eliminar';

  @override
  String get icon_util_mem_alldelete => 'Elim. todo';

  @override
  String get icon_00017_zz => '%%%icon%%%';

  @override
  String get icon_00017_zz_s => '%%%icon%%%';

  @override
  String get icon_00018_zz => '%%%icon%%%';

  @override
  String get icon_00018_zz_s => '%%%icon%%%';

  @override
  String get icon_00019_zz => '%%%icon%%%';

  @override
  String get icon_00019_zz_s => '%%%icon%%%';

  @override
  String get icon_00020_zz => '%%%icon%%%';

  @override
  String get icon_00020_zz_s => '%%%icon%%%';

  @override
  String get icon_util_width => 'Anchura';

  @override
  String get icon_util_length => 'Longitud';

  @override
  String get icon_util_lrshift => 'Mover I/D';

  @override
  String get icon_util_tension => 'Tensión';

  @override
  String get icon_util_slitlength => 'Ranura';

  @override
  String get icon_00021_zz => '%%%icon%%%';

  @override
  String get icon_00021_zz_s => '%%%icon%%%';

  @override
  String get icon_00022_zz => '%%%icon%%%';

  @override
  String get icon_00022_zz_s => '%%%icon%%%';

  @override
  String get icon_00027_zz => '%%%icon%%%';

  @override
  String get icon_00027_zz_s => '%%%icon%%%';

  @override
  String get icon_00028_zz => '%%%icon%%%';

  @override
  String get icon_00028_zz_s => '%%%icon%%%';

  @override
  String get icon_00029_zz => '%%%icon%%%';

  @override
  String get icon_00029_zz_s => '%%%icon%%%';

  @override
  String get icon_00038_zz => '%%%icon%%%';

  @override
  String get icon_00038_zz_s => '%%%icon%%%';

  @override
  String get icon_00030_1 => 'Prueba';

  @override
  String get icon_guidel_guideline => 'Guía';

  @override
  String get icon_guidel_main => 'Principal';

  @override
  String get icon_guidel_sub => 'Secundaria';

  @override
  String get icon_guidel_mainline => 'Línea principal';

  @override
  String get icon_guidel_subline => 'Línea\nsecundaria';

  @override
  String get icon_guidel_linelength => 'Long. de la línea';

  @override
  String get icon_guidel_line_l => 'L';

  @override
  String get icon_guidel_line_m => 'M';

  @override
  String get icon_guidel_line_s => 'S';

  @override
  String get icon_guidel_color => 'Color';

  @override
  String get icon_guidel_position => 'Posición';

  @override
  String get icon_guidel_main_pos => 'Posición de la línea principal';

  @override
  String get icon_guidel_sub_pos => 'Posición de la línea secundaria';

  @override
  String get icon__guidel_sub_frommain => 'Distancia desde la línea principal';

  @override
  String get icon_guidel_gridsize => 'Tamaño de la cuadrícula';

  @override
  String get icon_guidel_angle => 'Ángulo';

  @override
  String get icon_guidel_seamallowance => 'Margen de costura';

  @override
  String get icon_guidel_spacing => 'Espacio';

  @override
  String get icon_guidel_lengthl_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengthl_zz_s => '%%%icon%%%';

  @override
  String get icon_guidel_lengthm_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengthm_zz_s => '%%%icon%%%';

  @override
  String get icon_guidel_lengths_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengths_zz_s => '%%%icon%%%';

  @override
  String get icon_position => 'Posición';

  @override
  String get icon_00031_2 => 'Editar';

  @override
  String get icon_00033_1 => 'Añadir';

  @override
  String get icon_00035 => 'Bordado';

  @override
  String get icon_return => 'Retorno';

  @override
  String get icon_00038_1 => 'Configurar';

  @override
  String get icon_00038_2 => 'Con-\nfigurar ';

  @override
  String get icon_00039 => 'min';

  @override
  String get icon_00041_1 => 'Seleccionar';

  @override
  String get icon_select => 'Seleccionar';

  @override
  String get icon_select_2 => 'Selec-\ncionar';

  @override
  String get icon_00041_2 => 'Selec-\ncionar';

  @override
  String get icon_00042 => 'Presione siempre al\nquitar la unidad de bordado.';

  @override
  String get icon_00046_zz => '%%%icon%%%';

  @override
  String get icon_00046_zz_s => '%%%icon%%%';

  @override
  String get icon_00048 => 'Número';

  @override
  String get icon_00049 => 'Lista';

  @override
  String get icon_00050 => 'Cargar';

  @override
  String get icon_00051_zz => '%%%icon%%%';

  @override
  String get icon_00051_zz_s => '%%%icon%%%';

  @override
  String get icon_00052_zz => '%%%icon%%%';

  @override
  String get icon_00052_zz_s => '%%%icon%%%';

  @override
  String get icon_00053_b1 => '%%%none%%%';

  @override
  String get icon_00053_b2 => '%%%none%%%';

  @override
  String get icon_00053_t1 => 'Color Visualizer';

  @override
  String get icon_00053_t2 => 'Color\nVisualizer';

  @override
  String get icon_00055_1 => 'Aleatorio';

  @override
  String get icon_00055_2 => 'Aleatorio';

  @override
  String get icon_00056_1 => 'Gradación';

  @override
  String get icon_00056_2 => 'Gradación';

  @override
  String get icon_00057 => 'Intenso';

  @override
  String get icon_00054 => 'Suave';

  @override
  String get icon_00058_1 => 'Cantidad de colores';

  @override
  String get icon_00059 => 'Actualizar';

  @override
  String get icon_00060 => 'No especificado';

  @override
  String get icon_emb_tension => 'Tensión';

  @override
  String get icon_emb_threadcutting => 'Corta hilos';

  @override
  String get icon_00063_a => 'Corte al final del color';

  @override
  String get icon_00064_a => 'Corte de salto de puntada';

  @override
  String get icon_00065 => 'Densidad';

  @override
  String get icon_00066 => 'Tamaño';

  @override
  String get icon_00067_zz => '%%%icon%%%';

  @override
  String get icon_00067_zz_s => '%%%icon%%%';

  @override
  String get icon_00068_zz => '%%%icon%%%';

  @override
  String get icon_00068_zz_s => '%%%icon%%%';

  @override
  String get icon_00070_zz => '%%%icon%%%';

  @override
  String get icon_00070_zz_s => '%%%icon%%%';

  @override
  String get icon_00071_zz => '%%%icon%%%';

  @override
  String get icon_00071_zz_s => '%%%icon%%%';

  @override
  String get icon_00072 => 'Disposición ';

  @override
  String get icon_00075_zz => '%%%icon%%%';

  @override
  String get icon_00075_zz_s => '%%%icon%%%';

  @override
  String get icon_00076_zz => '%%%icon%%%';

  @override
  String get icon_00076_zz_s => '%%%icon%%%';

  @override
  String get icon_00077_zz => '%%%icon%%%';

  @override
  String get icon_00077_zz_s => '%%%icon%%%';

  @override
  String get icon_00079 => 'Posición de entrada de la aguja';

  @override
  String get icon_00080 => 'Siguiente';

  @override
  String get icon_prev => 'Anterior';

  @override
  String get icon_segment => 'Segmento';

  @override
  String get icon_00083 => 'Ajuste del punto final';

  @override
  String get icon_00084 => 'Ajuste preciso de longitud';

  @override
  String get icon_00085 => 'Ajuste del punto final\nParada temporal';

  @override
  String get icon_00088 => 'Explorar';

  @override
  String get icon_00089 => 'Vídeo';

  @override
  String get icon_00090 => 'Repetir';

  @override
  String get icon_00091_1 => 'Selección múltiple';

  @override
  String get icon_00091_2 => 'Selección\nmúltiple';

  @override
  String get icon_00093_zz => '%%%icon%%%';

  @override
  String get icon_00093_zz_s => '%%%icon%%%';

  @override
  String get icon_00094_zz => '%%%icon%%%';

  @override
  String get icon_00094_zz_s => '%%%icon%%%';

  @override
  String get icon_00095 => 'Salir';

  @override
  String get icon_00096 => 'División automática de las piezas del acolchado';

  @override
  String get icon_resettodef => 'Restablecer la configuración predeterminada';

  @override
  String get icon_resettodefall => 'Restablecer la configuración predeterminada';

  @override
  String get icon_resettodefall_2 => 'Restablecer la configuración\npredeterminada';

  @override
  String get icon_00100 => 'Idioma';

  @override
  String get icon_00101_a => 'Español';

  @override
  String get icon_00101_b => 'Spanish';

  @override
  String get icon_00102 => 'Brillo';

  @override
  String get icon_00103 => 'Brillo de la pantalla';

  @override
  String get icon_00104 => 'Protector de pantalla';

  @override
  String get icon_00105 => 'Predeterminados';

  @override
  String get icon_00106 => 'Personalizar';

  @override
  String get icon_00107 => 'Modo Eco';

  @override
  String get icon_00108 => 'Modo de compatibilidad\nde apagado';

  @override
  String get icon_00109 => 'Luz';

  @override
  String get icon_00112 => 'Volumen del altavoz\nde la máquina';

  @override
  String get icon_00114 => 'Volumen';

  @override
  String get icon_00115 => 'Puntero del ratón';

  @override
  String get icon_00116 => 'mm / \"(inch)';

  @override
  String get icon_00118 => 'Página principal';

  @override
  String get icon_00119 => 'Pantalla de\ncostura/\nbordado';

  @override
  String get icon_00192 => 'Pantalla inicial';

  @override
  String get icon_00121 => 'Pantalla de\npresentación';

  @override
  String get icon_00122 => 'Página inicial de puntadas';

  @override
  String get icon_00123 => 'LED del soporte de carretes';

  @override
  String get icon_00124 => 'Control de anchura';

  @override
  String get icon_00125_1 => 'Ajuste preciso vert.';

  @override
  String get icon_00126_1 => 'Ajuste preciso horiz.';

  @override
  String get icon_00127_1 => 'Altura del pie prensatela';

  @override
  String get icon_00128_1 => 'Presión del pie prensatela';

  @override
  String get icon_00129 => 'Posición inicial';

  @override
  String get icon_00130_1 => 'Altura de giro';

  @override
  String get icon_00131_1 => 'Altura del pie en\ncostura libre';

  @override
  String get icon_00134 => 'Sist. sensor aut. tejido';

  @override
  String get icon_00135 => 'Sensor de grosor\ndel tejido';

  @override
  String get icon_00136_2 => 'Posición de la aguja -\nArriba/Abajo';

  @override
  String get icon_00137 => 'Posición de la aguja -\nColocación de puntada';

  @override
  String get icon_00138 => 'Sensor de hilo\nsuperior y de la bobina';

  @override
  String get icon_00140 => 'Ajuste de avance\nde avance doble';

  @override
  String get icon_00141 => 'Pedal multifunción';

  @override
  String get icon_00142 => 'Golpe con el talón';

  @override
  String get icon_00143 => 'Pedal lateral';

  @override
  String get icon_00144_a => 'Posición\nde la aguja\nArriba/Abajo';

  @override
  String get icon_00145 => 'Corte del hilo';

  @override
  String get icon_00146 => 'Puntada única';

  @override
  String get icon_00147 => 'Puntada en\nreversa';

  @override
  String get icon_00243 => 'Pie prensatela\nlevantado/bajado';

  @override
  String get icon_00244 => 'Sin ajuste';

  @override
  String get icon_00249 => 'Puntada de remate';

  @override
  String get icon_00148 => 'Longitud de la ranura';

  @override
  String get icon_00148_zz => '%%%icon%%%';

  @override
  String get icon_00148_zz_s => '%%%icon%%%';

  @override
  String get icon_00150 => 'Prioridad del remate';

  @override
  String get icon_00152_1 => 'Pantalla de bastidores';

  @override
  String get icon_00155_1 => 'Tamaño de miniaturas';

  @override
  String get icon_00157 => 'Color de fondo\ndel bordado';

  @override
  String get icon_00159 => 'Color de fondo\nde miniatura';

  @override
  String get icon_00163_a => 'Visualización\nde imagen de fondo';

  @override
  String get icon_00163 => 'Imagen de\nfondo';

  @override
  String get icon_00164 => 'Escanear imagen';

  @override
  String get icon_00165 => 'Estándar';

  @override
  String get icon_00166 => 'Fina';

  @override
  String get icon_00167 => 'Calidad del escaneado';

  @override
  String get icon_00168 => 'LED del soporte de carretes';

  @override
  String get icon_00178 => 'Distancia del hilvanado\nde bordado';

  @override
  String get icon_00180 => 'Distancia del adorno de bordado';

  @override
  String get icon_00182_1 => 'Velocidad máx.\nde bordado';

  @override
  String get icon_00183_1 => 'Tensión de bordado';

  @override
  String get icon_00184_1 => 'Altura del pie de bordado';

  @override
  String get icon_00185 => 'Tamaño del bastidor';

  @override
  String get icon_00186 => 'Cuadrícula';

  @override
  String get icon_00187 => 'Cambiar';

  @override
  String get icon_00188 => 'Elim.';

  @override
  String get icon_00191 => 'Color';

  @override
  String get icon_00193 => 'Visualización del reloj';

  @override
  String get icon_00194 => 'AM';

  @override
  String get icon_00195 => 'PM';

  @override
  String get icon_00196 => '24h';

  @override
  String get icon_clock_msg1 => 'Establecer la fecha correcta para la conexión a la red.';

  @override
  String get icon_00197 => 'Calibración de la función\ndel sensor';

  @override
  String get icon_00199 => 'Ajuste del marcador de guía';

  @override
  String get icon_00200 => 'Luminosidad del\nmarcador de guía';

  @override
  String get icon_00201_1 => 'Pie de bordado con\npuntero LED ajuste';

  @override
  String get icon_00202_p => 'Brillo';

  @override
  String get icon_00206_1 => 'Certific.';

  @override
  String get icon_00207_a => 'Kit';

  @override
  String get icon_00208 => 'Iniciar';

  @override
  String get icon_00209 => 'Parar';

  @override
  String get icon_00211 => 'Recuento de uso';

  @override
  String get icon_00212 => 'SCS';

  @override
  String get icon_00214 => 'Número total';

  @override
  String get icon_00218 => 'No.';

  @override
  String get icon_00220 => 'Versión';

  @override
  String get icon_00222 => 'YYYY';

  @override
  String get icon_00223 => 'MM';

  @override
  String get icon_00224 => 'DD';

  @override
  String get icon_00225 => 'mm';

  @override
  String get icon_00226 => '\"';

  @override
  String get icon_on => 'ON';

  @override
  String get icon_off => 'OFF';

  @override
  String get icon_00229 => 'KB';

  @override
  String get icon_00230 => 'bPocket';

  @override
  String get icon_00231 => '1';

  @override
  String get icon_00232 => '2';

  @override
  String get icon_00233 => '3';

  @override
  String get icon_00234 => '4';

  @override
  String get icon_00235 => '5';

  @override
  String get icon_00236 => '6';

  @override
  String get icon_00237 => '7';

  @override
  String get icon_00238 => '8';

  @override
  String get icon_00239 => '9';

  @override
  String get icon_00240 => '0';

  @override
  String get icon_00241 => 'C';

  @override
  String get icon_00242 => '%';

  @override
  String get icon_00245 => 'Elevación automática del\npie prensatela';

  @override
  String get icon_00246 => 'Abajo';

  @override
  String get icon_00247 => 'Arriba';

  @override
  String get icon_00248_zz => '%%%icon%%%';

  @override
  String get icon_00248_zz_s => '%%%icon%%%';

  @override
  String get icon_00248 => 'Presionar para recortar';

  @override
  String get icon_00251 => 'Proyector';

  @override
  String get icon_00253 => 'Color de fondo';

  @override
  String get icon_00254 => 'Coser:\nContorno del patrón';

  @override
  String get icon_00255 => 'Bordado:\nColor del puntero';

  @override
  String get icon_pointershape => 'Forma del puntero';

  @override
  String get icon_00256 => 'Cámara';

  @override
  String get icon_00257 => 'Calibración de la aguja\npara cámara/proyector';

  @override
  String get icon_recog_ok => 'OK';

  @override
  String get icon_recog_ng => 'NG';

  @override
  String get icon_00258 => 'Bordado\nPosición de parada\nde la aguja';

  @override
  String get icon_00259 => 'Unidad';

  @override
  String get icon_00260 => 'Color del hilo';

  @override
  String get icon_00261 => 'Marca de hilo';

  @override
  String get icon_00264 => 'Nombre de color';

  @override
  String get icon_00265 => '# 123';

  @override
  String get icon_00266 => 'Hora';

  @override
  String get icon_00268 => 'Original';

  @override
  String get icon_00269 => 'Embroidery';

  @override
  String get icon_00269_t => 'Embroidery';

  @override
  String get icon_00270 => 'Country';

  @override
  String get icon_00270_t => 'Country';

  @override
  String get icon_00271 => 'Madeira\nPoly';

  @override
  String get icon_00272 => 'Madeira\nRayon';

  @override
  String get icon_00273 => 'Sulky';

  @override
  String get icon_00274 => 'Robison-Anton';

  @override
  String get icon_00275 => 'Robison-Anton\nPoly';

  @override
  String get icon_00276 => 'Robison-Anton\nRayon';

  @override
  String get icon_00277 => 'Isacord';

  @override
  String get icon_00278 => 'Gütermann';

  @override
  String get icon_00279 => 'Simplicity Pro';

  @override
  String get icon_00279_p => 'Pacesetter Pro';

  @override
  String get icon_00280 => 'Floriani';

  @override
  String get icon_00281 => 'Iris';

  @override
  String get icon_00282 => 'Aurifil';

  @override
  String get icon_00283 => 'WonderFil ';

  @override
  String get icon_00284 => 'Polyfast';

  @override
  String get icon_00290 => 'Si ha comprado el kit de actualización y\ndesea certificar la máquina de coser,\npulse [CERTIFIC.].';

  @override
  String get icon_00291 => 'KIT I';

  @override
  String get icon_00292 => 'KIT II';

  @override
  String get icon_00293 => 'KIT III';

  @override
  String get icon_00294 => 'KIT IV';

  @override
  String get icon_00295 => 'KIT V';

  @override
  String get icon_00296 => 'KIT VI';

  @override
  String get icon_00297 => 'KIT VII';

  @override
  String get icon_00298 => 'KIT VIII';

  @override
  String get icon_00299 => 'KIT IX';

  @override
  String get icon_00300 => 'KIT X';

  @override
  String get icon_00643_s => 'Ninguna';

  @override
  String get icon_00301 => 'Guía de operación';

  @override
  String get icon_00302 => 'Guía de costura';

  @override
  String get icon_00303 => 'Explicación de la puntada';

  @override
  String get icon_manuals => 'Manuales';

  @override
  String get icon_operariong_b => 'Manual de instrucciones (English)';

  @override
  String get icon_operariong_t => 'Guía de referencia y de instrucciones (English)';

  @override
  String get icon_pdf => 'Manual en PDF';

  @override
  String get icon_supportsite => 'Sitio web de ayuda';

  @override
  String get icon_pdf_eula => 'Acuerdos de licencia de usuario\nfinal (EULA)';

  @override
  String get icon_pdf_sewing => 'Coser (English)';

  @override
  String get icon_pdf_emb => 'Bordado (English)';

  @override
  String get icon_pdf_sewing_ef => 'Coser (English)';

  @override
  String get icon_pdf_emb_ef => 'Bordado (English)';

  @override
  String get icon_pdf_sewing_t => 'Coser (English)';

  @override
  String get icon_pdf_emb_t => 'Bordado (English)';

  @override
  String get icon_f_omadendum => 'Anexo (English)';

  @override
  String get icon_f_omadendum_ef => 'Anexo (English)';

  @override
  String get icon_f_omadendum_l => 'Anexo del Manual\nde instrucciones';

  @override
  String get icon_f_om_kit1 => 'KIT I (English)';

  @override
  String get icon_f_om_kit2 => 'KIT II (English)';

  @override
  String get icon_f_om_kit3 => 'KIT III (English)';

  @override
  String get icon_f_om_kit1_l => 'Manual de instrucciones\nKIT I';

  @override
  String get icon_f_omadendum_t => 'Anexo (English)';

  @override
  String get icon_f_om_kit1_t => 'KIT I (English)';

  @override
  String get icon_f_om_kit2_t => 'KIT II (English)';

  @override
  String get icon_f_om_kit3_t => 'KIT III (English)';

  @override
  String get icon_t_pdf_iivo_url_b => 'Si desea ver los manuales en su dispositivo móvil o PC, acceda a\nhttps://s.brother/fmraa.';

  @override
  String get icon_t_pdf_iivo_url_t => 'Si desea ver el manual en su dispositivo móvil o PC, acceda a\nhttps://babylock.com/radiance-instruction-and-reference-guide.';

  @override
  String get icon_t_video_iivo_url_b => 'Visítenos en\n https://s.brother/fvraa\nsi desea ver los tutoriales en vídeo acerca de este modelo.';

  @override
  String get icon_t_video_iivo_url_t => 'Visítenos en\n https://babylock.com/radiance-training\nsi desea ver los tutoriales en vídeo acerca de este modelo.';

  @override
  String get icon_pdf_url_qr_t => 'www.babylock.com';

  @override
  String get icon_nettool => 'Herramienta de diagnóstico de red';

  @override
  String get icon_iagree => 'Acepto';

  @override
  String get icon_terms_cancel => 'Cancelar';

  @override
  String get icon_confirm => 'Confirmar';

  @override
  String get icon_00304 => 'Piezas principales';

  @override
  String get icon_00305 => 'Botones principales';

  @override
  String get icon_00306 => 'Funcionamiento';

  @override
  String get icon_00307 => 'Bordado básico';

  @override
  String get icon_00308 => 'Problemas';

  @override
  String get icon_00309 => 'Mantenimiento';

  @override
  String get icon_00310 => 'El hilo está enredado en\nel revés de la tela';

  @override
  String get icon_00311 => 'No se puede enhebrar la aguja';

  @override
  String get icon_00312 => 'No se puede utilizar\nel enhebrador';

  @override
  String get icon_00313 => 'Tensión del hilo incorrecta';

  @override
  String get icon_00314 => 'El hilo superior se rompe';

  @override
  String get icon_00315 => 'El hilo de la bobina se rompe';

  @override
  String get icon_00316 => 'Puntadas sueltas';

  @override
  String get icon_00317 => 'La aguja se rompe';

  @override
  String get icon_00318 => 'La máquina no funciona';

  @override
  String get icon_00320 => 'No se realiza la costura\nde caracteres';

  @override
  String get icon_00321 => 'La tela no se arrastra bien\npor la máquina';

  @override
  String get icon_00322 => 'Frunces en la tela';

  @override
  String get icon_00323 => 'La máquina hace mucho ruido';

  @override
  String get icon_00325 => 'Costura incorrecta de\npuntada de bordado';

  @override
  String get icon_00326 => 'La unidad de bordado\nno funciona';

  @override
  String get icon_00331 => 'Puntilla de unión';

  @override
  String get icon_00332 => 'Puntada invisible';

  @override
  String get icon_00333 => 'Ojales';

  @override
  String get icon_00334 => 'Botones';

  @override
  String get icon_00335 => 'Costura de pinzas';

  @override
  String get icon_00336 => 'Costura sobrecargada';

  @override
  String get icon_00337 => 'Fruncidos';

  @override
  String get icon_00338 => 'Sobrehilado';

  @override
  String get icon_00339 => 'Pliegue cosido';

  @override
  String get icon_00340 => 'Puntada festón';

  @override
  String get icon_00341 => 'Puntada recta';

  @override
  String get icon_00342 => 'Inserción de cremalleras';

  @override
  String get icon_00343 => 'Remiendos';

  @override
  String get icon_00344 => 'Acolchado libre';

  @override
  String get icon_00345 => 'Acolchado';

  @override
  String get icon_00346 => 'Acolchado repetido';

  @override
  String get icon_00347 => 'Aplicación 1';

  @override
  String get icon_00348 => 'Aplicación 2';

  @override
  String get icon_search => 'Buscar';

  @override
  String get icon_00353 => 'Enhebrado del hilo superior de la máquina';

  @override
  String get icon_00354 => 'Devanar la bobina';

  @override
  String get icon_00355 => 'Cambiar la aguja';

  @override
  String get icon_00356 => 'Cambiar el pie prensatela';

  @override
  String get icon_00357 => 'Colocar la bobina';

  @override
  String get icon_00358 => 'Función de costura';

  @override
  String get icon_00359 => 'Utilizar la función de corte del hilo';

  @override
  String get icon_00360 => 'Utilizar el destornillador especial';

  @override
  String get icon_00361 => 'Utilizar la función de giro';

  @override
  String get icon_00362 => 'Ajustar la anchura y longitud de puntada';

  @override
  String get icon_00363 => 'Utilizar el destornillador multiusos';

  @override
  String get icon_00364 => 'Utilizar el sensor automático de tela (presión automática del pie prensatela)';

  @override
  String get icon_00365 => 'Utilizar My Custom Stitch (Mi puntada preferida)';

  @override
  String get icon_00366 => 'Utilizar la función de costura de bordes';

  @override
  String get icon_00367 => 'Crear efectos decorativos con bobinas (costura)';

  @override
  String get icon_00368 => 'Crear efectos decorativos con bobinas (bordado)';

  @override
  String get icon_00369 => 'Preparativos para la creación de efectos decorativos con bobinas';

  @override
  String get icon_00370 => 'Preparativos para la creación de efectos decorativos inversos con bobinas';

  @override
  String get icon_00371 => 'Crear efectos decorativos inversos con bobinas (costura)';

  @override
  String get icon_00372 => 'Utilizar la cámara integrada en el modo de costura';

  @override
  String get icon_00373 => 'Ajustar la posición de entrada de la aguja con el marcador de guía en la pantalla de ajustes';

  @override
  String get icon_00374 => 'Ajustar el brillo del marcador de guía en la pantalla de ajustes';

  @override
  String get icon_00375 => 'Ajustar la tensión del hilo';

  @override
  String get icon_00376 => 'Colocar estabilizadores para planchar';

  @override
  String get icon_00377 => 'Colocar el tejido en el bastidor de bordado';

  @override
  String get icon_00378 => 'Colocar/retirar el bastidor de bordado';

  @override
  String get icon_00379 => 'Colocar/retirar la unidad de bordado/la unidad plana';

  @override
  String get icon_00380 => 'Colocar/retirar el soporte del pie prensatela';

  @override
  String get icon_00381 => 'Función de bordado';

  @override
  String get icon_00382 => 'Utilizar la función de imprimir y coser';

  @override
  String get icon_00383 => 'Utilizar la función de mezcla de colores';

  @override
  String get icon_00384 => 'Utilizar mi centro de diseño';

  @override
  String get icon_00385 => 'Escanear dibujos de las líneas';

  @override
  String get icon_00386 => 'Escanear ilustraciones';

  @override
  String get icon_00387 => 'Utilizar el marco de escaneado';

  @override
  String get icon_00388 => 'Visualizar el tejido en la LCD (escanear con la cámara integrada)';

  @override
  String get icon_00389 => 'Alinear la posición de bordado con el adhesivo de posición';

  @override
  String get icon_00390 => 'Conectar patrones utilizando la cámara integrada';

  @override
  String get icon_00391 => 'Alinear la posición de bordado utilizando la cámara integrada';

  @override
  String get icon_00392 => '';

  @override
  String get icon_00393 => 'Ajustes';

  @override
  String get icon_00394 => 'Ajuste de posición de la aguja de la cámara';

  @override
  String get icon_00395 => 'Actualizar el software de la máquina';

  @override
  String get icon_00396 => 'Ajustar el marcador de guía en la pantalla de ajustes';

  @override
  String get icon_00397 => 'Ajustar la hora/fecha';

  @override
  String get icon_00398 => 'Utilizar la costura de remate automática';

  @override
  String get icon_00399 => 'Otros';

  @override
  String get icon_00400 => 'Ver/guardar vídeos';

  @override
  String get icon_00401 => 'Lápiz con sensor';

  @override
  String get icon_00402 => 'Conectar el lápiz con sensor';

  @override
  String get icon_00403 => 'Calibrar el lápiz con sensor';

  @override
  String get icon_00404 => 'Especificar la posición del marcador de guía con el lápiz con sensor';

  @override
  String get icon_00405 => 'Especificar la posición de entrada de la aguja con el lápiz con sensor';

  @override
  String get icon_00406 => 'Especificar la posición/anchura de la puntada con el lápiz con sensor';

  @override
  String get icon_00407 => 'Especificar el punto final de la costura con el lápiz con sensor';

  @override
  String get icon_00408 => 'Especificar la posición de bordado con el lápiz con sensor';

  @override
  String get icon_00409 => 'Accesorio';

  @override
  String get icon_00410 => 'Utilizar el elevador de rodilla';

  @override
  String get icon_00411 => 'Utilizar el destornillador múltiple';

  @override
  String get icon_00412 => 'Utilizar el destornillador multiusos';

  @override
  String get icon_00416 => 'Instalar el pedal multifunción';

  @override
  String get icon_00417 => 'Asignar funciones al pedal multifunción';

  @override
  String get icon_00418 => 'Instalar/retirar el pie de bordado con puntero LED';

  @override
  String get icon_00419 => 'Ajustar el pie de bordado con puntero LED';

  @override
  String get icon_00420 => 'Crear patrones de bordado con puntadas intermitentes utilizando la cámara integrada';

  @override
  String get icon_00421 => 'Colocar el pie prensatela con el adaptador incluido';

  @override
  String get icon_00422 => 'Utilizar el estuche de accesorios';

  @override
  String get icon_00423 => 'Mantenimiento (limpiar la guía)';

  @override
  String get icon_00500 => 'Mi centro de diseño';

  @override
  String get icon_00500_2 => 'Mi centro\nde diseño';

  @override
  String get icon_iqdesigner => 'IQ Designer';

  @override
  String get icon_00501 => 'Escaneado de líneas';

  @override
  String get icon_00503_zz => '%%%icon%%%';

  @override
  String get icon_00503_zz_s => '%%%icon%%%';

  @override
  String get icon_00505 => 'Escaneado de ilustraciones';

  @override
  String get icon_imagescan => 'Escanear imagen';

  @override
  String get icon_linedesign => 'Diseño de la línea';

  @override
  String get icon_illustrationdesign => 'Diseño de la ilustración';

  @override
  String get icon_00509_zz => '%%%icon%%%';

  @override
  String get icon_00510 => 'Reconocer';

  @override
  String get icon_00511_1 => 'Previsual.';

  @override
  String get icon_00511_2 => 'Previsual.';

  @override
  String get icon_showpreview => 'Mostrar vista previa';

  @override
  String get icon_00512 => 'Reintentar';

  @override
  String get icon_00514 => 'Ignorar tamaño del objeto';

  @override
  String get icon_00516 => 'Nivel de detección de escala de grises';

  @override
  String get icon_00503 => 'Línea';

  @override
  String get icon_00518 => 'Borrador';

  @override
  String get icon_00520 => 'Visualización\noriginal';

  @override
  String get icon_00521 => 'Visualización\nfinal';

  @override
  String get icon_00522 => 'Visualización final';

  @override
  String get icon_00523 => 'Número máximo\nde colores';

  @override
  String get icon_00525 => 'Eliminar\nfondo';

  @override
  String get icon_00526 => 'Reconocer';

  @override
  String get icon_00528 => 'Ajustes de bordado';

  @override
  String get icon_00529 => 'Propiedades de la línea';

  @override
  String get icon_00530 => 'Propiedades de la región';

  @override
  String get icon_00533 => 'Tamaño';

  @override
  String get icon_00537 => 'Anchura de la\npuntada zigzag';

  @override
  String get icon_00538 => 'Densidad';

  @override
  String get icon_00539 => 'Utilizar\npuntadas';

  @override
  String get icon_00540 => 'Puntada de relleno';

  @override
  String get icon_00541 => 'Dirección';

  @override
  String get icon_00544 => 'Compensación\nde tirones';

  @override
  String get icon_00545 => 'Costura de\nrefuerzo';

  @override
  String get icon_00547 => 'Espacio';

  @override
  String get icon_00548_1 => 'Manual';

  @override
  String get icon_00548_2 => 'Manual';

  @override
  String get icon_00549_1 => 'Auto';

  @override
  String get icon_00549_2 => 'Auto';

  @override
  String get icon_00550 => 'A coser';

  @override
  String get icon_00551 => 'Encuadrar la imagen';

  @override
  String get icon_00552 => 'Especificación del color';

  @override
  String get icon_00553 => 'Siguiente';

  @override
  String get icon_00554 => 'Distancia';

  @override
  String get icon_00555 => 'Guardar contornos';

  @override
  String get icon_00556 => 'Formas cerradas';

  @override
  String get icon_00557 => 'Formas abiertas';

  @override
  String get icon_00558 => 'Contornos guardados';

  @override
  String get icon_00559 => 'Áreas de bordado del bastidor';

  @override
  String get icon_00562 => 'Contorno';

  @override
  String get icon_00564 => 'Grosor';

  @override
  String get icon_00565 => 'Desplazamiento\naleatorio';

  @override
  String get icon_00566 => 'Desplazamiento\nde la posición';

  @override
  String get icon_inside => 'Interior';

  @override
  String get icon_outside => 'Exterior';

  @override
  String get icon_00567 => 'Invertir';

  @override
  String get icon_00568 => 'Anchura\nde la puntada';

  @override
  String get icon_00569 => 'Actual';

  @override
  String get icon_00570 => 'Nuevo';

  @override
  String get icon_frame_297_465_mm => '297 × 465 mm';

  @override
  String get icon_frame_297_465_inch => '11-5/8\"× 18-1/4\"';

  @override
  String get icon_frame_272_408_mm => '272 × 408 mm';

  @override
  String get icon_frame_272_408_inch => '10-5/8\"× 16\"';

  @override
  String get icon_frame_254_254_mm => '254 × 254 mm';

  @override
  String get icon_frame_254_254_inch => '10\"× 10\"';

  @override
  String get icon_frame_240_360_mm => '240 × 360 mm';

  @override
  String get icon_frame_240_360_inch => '9-1/2\"× 14\"';

  @override
  String get icon_frame_180_360_mm => '180 × 360 mm';

  @override
  String get icon_frame_180_360_inch => ' 7\" × 14\"';

  @override
  String get icon_frame_180_300_mm => '180 × 300 mm';

  @override
  String get icon_frame_180_300_inch => ' 7\" × 12\"';

  @override
  String get icon_frame_200_300_mm => '200 × 300 mm';

  @override
  String get icon_frame_200_300_inch => '8\"×12\"';

  @override
  String get icon_frame_100_300_mm => '100 × 300 mm';

  @override
  String get icon_frame_100_300_inch => '4\"× 12\"';

  @override
  String get icon_frame_160_260_mm => '160 × 260 mm';

  @override
  String get icon_frame_160_260_inch => '6-1/4\"× 10-1/4\"';

  @override
  String get icon_frame_240_240_mm => '240 × 240 mm';

  @override
  String get icon_frame_240_240_inch => '9-1/2\"× 9-1/2\"';

  @override
  String get icon_frame_200_200_mm => '200 × 200 mm';

  @override
  String get icon_frame_200_200_inch => '8\"× 8\"';

  @override
  String get icon_frame_130_180_mm => '130 × 180 mm';

  @override
  String get icon_frame_130_180_inch => '5\"× 7\"';

  @override
  String get icon_frame_100_180_mm => '100 × 180 mm';

  @override
  String get icon_frame_100_180_inch => '4\"× 7\"';

  @override
  String get icon_frame_150_150_mm => '150 × 150 mm';

  @override
  String get icon_frame_150_150_inch => '6\"× 6\"';

  @override
  String get icon_frame_100_100_mm => '100 × 100 mm';

  @override
  String get icon_frame_100_100_inch => '4\"× 4\"';

  @override
  String get icon_frame_60_20_mm => '60 × 20 mm';

  @override
  String get icon_frame_60_20_inch => '2-3/8\"× 3/4\"';

  @override
  String get icon_zoom_50 => '50';

  @override
  String get icon_zoom_100 => '100';

  @override
  String get icon_zoom_125 => '125';

  @override
  String get icon_zoom_150 => '150';

  @override
  String get icon_zoom_200 => '200';

  @override
  String get icon_zoom_400 => '400';

  @override
  String get icon_zoom_800 => '800';

  @override
  String get icon_zoom_120 => '120';

  @override
  String get icon_zoom_240 => '240';

  @override
  String get icon_zoom_480 => '480';

  @override
  String get icon_zoom_960 => '960';

  @override
  String get icon_00600 => 'Activar LAN inalámbrica';

  @override
  String get icon_00600_1 => 'Activar WLAN';

  @override
  String get icon_00601 => 'SSID';

  @override
  String get icon_00602 => 'Seleccionar SSID...';

  @override
  String get icon_00603 => 'Nombre de la máquina';

  @override
  String get icon_00604 => 'WPS (Pulsar)';

  @override
  String get icon_00605 => 'WPS (Pin)';

  @override
  String get icon_00606 => 'Otros';

  @override
  String get icon_00608 => 'Estado de la LAN inalámbrica';

  @override
  String get icon_00608_1 => 'Estado WLAN';

  @override
  String get icon_00609 => 'SSID\nguardado';

  @override
  String get icon_00609_1 => 'SSID guardado';

  @override
  String get icon_00610 => 'Nuevo SSID';

  @override
  String get icon_wlan_title => 'LAN inalámbrica';

  @override
  String get icon_wlan_connection => 'Conexión de LAN inalámbrica';

  @override
  String get icon_wlan_networks => 'Redes LAN inalámbricas';

  @override
  String get icon_wlan_enable => 'Activar LAN inalámbrica';

  @override
  String get icon_wlan_setinfo_01 => 'Para ver las redes disponibles, active Activar LAN inalámbrica.';

  @override
  String get icon_wlan_setinfo_02 => 'Buscando redes LAN inalámbricas…';

  @override
  String get icon_wlan_setinfo_03 => 'Conectando a la LAN inalámbrica…';

  @override
  String get icon_wlan_setinfo_05 => 'Activando la LAN inalámbrica…';

  @override
  String get icon_wlan_setinfo_06 => 'LAN inalámbrica activada';

  @override
  String get icon_wlan_setinfo_04 => 'Desactivando la LAN inalámbrica…';

  @override
  String get icon_wlan_setinfo_07 => 'Se recuperarán todos los ajustes de red, como:·LAN inalámbrica';

  @override
  String get icon_wlan_networkreset => 'Restaurar red';

  @override
  String get icon_wlan_limitedconnect => 'No se puede conectar a la red. Por favor, revise la configuración del reloj.';

  @override
  String get icon_00630 => 'Red';

  @override
  String get icon_00631 => 'Asistente de configuración\nde la LAN inalámbrica';

  @override
  String get icon_00631_1 => 'Conf.Asistente';

  @override
  String get icon_00632 => 'Detall';

  @override
  String get icon_00633 => 'Estado';

  @override
  String get icon_00634 => 'Señal';

  @override
  String get icon_00635 => 'Modo comunic.';

  @override
  String get icon_00636 => 'Activa (11b)';

  @override
  String get icon_00637 => 'Activa (11g)';

  @override
  String get icon_00638 => 'Activa (11n)';

  @override
  String get icon_00639 => 'Conexión fallida';

  @override
  String get icon_00640 => 'Fuerte';

  @override
  String get icon_00641 => 'Media';

  @override
  String get icon_00642 => 'Débil';

  @override
  String get icon_00643 => 'Ninguna';

  @override
  String get icon_00644 => 'Ad-hoc';

  @override
  String get icon_00645 => 'Infraestructura';

  @override
  String get icon_00646 => 'TCP/IP';

  @override
  String get icon_00647 => 'Dirección MAC';

  @override
  String get icon_00648 => 'Ajustes proxy';

  @override
  String get icon_00649 => 'Metod arranque';

  @override
  String get icon_00650 => 'Dirección IP';

  @override
  String get icon_00651 => 'Máscara Subred';

  @override
  String get icon_00652 => 'Puerta acceso';

  @override
  String get icon_00653 => 'Nombre de nodo';

  @override
  String get icon_00654 => 'Config WINS';

  @override
  String get icon_00655 => 'Servidor WINS';

  @override
  String get icon_00656 => 'Servidor DNS';

  @override
  String get icon_00656_p => 'Servidor DNS Primario';

  @override
  String get icon_00656_s => 'Servidor DNS Secundario';

  @override
  String get icon_00657 => 'APIPA';

  @override
  String get icon_00658 => 'Conexión proxy';

  @override
  String get icon_00659 => 'Dirección';

  @override
  String get icon_00660 => 'Puerto';

  @override
  String get icon_00661 => 'Nombre usuario';

  @override
  String get icon_00662 => 'Clave';

  @override
  String get icon_00663 => 'Primario';

  @override
  String get icon_00664 => 'Secundario';

  @override
  String get icon_00665 => 'Búsqueda SSID...';

  @override
  String get icon_00666 => 'SSID del punto de acceso';

  @override
  String get icon_00667 => 'Clave de red';

  @override
  String get icon_00668 => 'Sí';

  @override
  String get icon_00669 => 'No';

  @override
  String get icon_00670 => 'Selec. Autent.';

  @override
  String get icon_00671 => 'Sistema abierto';

  @override
  String get icon_00672 => 'Clave compartida';

  @override
  String get icon_00673 => 'WPA/WPA2-PSK';

  @override
  String get icon_00674 => 'Tipo Encripción';

  @override
  String get icon_00674_a => 'Tipo Encripción (Sistema abierto)';

  @override
  String get icon_00674_c => 'Tipo Encripción (WPA/WPA2-PSK)';

  @override
  String get icon_00675 => 'WEP';

  @override
  String get icon_00676 => 'AES';

  @override
  String get icon_00677 => 'TKIP';

  @override
  String get icon_00678 => 'Desactivada';

  @override
  String get icon_00679 => 'Estático';

  @override
  String get icon_00680 => 'Automático';

  @override
  String get icon_00681 => 'WPA';

  @override
  String get icon_00682 => 'Fecha';

  @override
  String get icon_cert_key => 'Certificación normal';

  @override
  String get icon_cert_web => 'Certificación\nde máquinas online';

  @override
  String get icon_status_t => 'Estado';

  @override
  String get icon_status_a1 => 'No comprobado';

  @override
  String get icon_status_a2 => 'Comprobando';

  @override
  String get icon_status_a3 => 'Comprobado: Ya actualizado';

  @override
  String get icon_status_a4 => 'Nueva actualización en el servidor';

  @override
  String get icon_status_b1 => 'No descargado';

  @override
  String get icon_status_b2 => 'Descargando';

  @override
  String get icon_status_b3 => 'Descargado';

  @override
  String get icon_cancel_downloading => 'Cancelar la descarga';

  @override
  String get icon_pause_downloading2 => 'Pausar la descarga\nPulse la tecla Reanudar para continuar la descarga';

  @override
  String get icon_status_c1 => 'Todavía no se ha instalado la nueva actualización.';

  @override
  String get icon_status_c2 => 'Se ha instalado la nueva actualización.';

  @override
  String get icon_app_dl_moniter => 'Descargar la aplicación de monitorización';

  @override
  String get icon_shape => 'Forma';

  @override
  String get icon_favorite => 'Favoritos';

  @override
  String get icon_sash_4section => '4 secciones (2×2)';

  @override
  String get icon_sash_1direction => 'Una dirección';

  @override
  String get icon_sash_1dtotal => 'Piezas totales';

  @override
  String get icon_offset => 'Desplazamiento';

  @override
  String get icon_startpoint => 'Punto inicial';

  @override
  String get icon_endpoint => 'Punto final';

  @override
  String get icon_embfootdwn => 'Bajada automática\ndel pie de bordado';

  @override
  String get icon_frame_272_272_mm => '272 × 272 mm';

  @override
  String get icon_frame_272_272_inch => '10-5/8\"× 10-5/8\"';

  @override
  String get icon_appguide_w => 'Guía de \nla aplicación';

  @override
  String get icon_appguide => 'Guía de \nla aplicación';

  @override
  String get icon_mobileapp => 'Aplicación móvil';

  @override
  String get icon_app => 'Aplicación';

  @override
  String get icon_emb1 => 'Bordado 1';

  @override
  String get icon_emb2 => 'Bordado 2';

  @override
  String get icon_00185_2 => 'Tamaño del bastidor';

  @override
  String get icon_type => 'Tipo';

  @override
  String get icon_typea => 'Tipo A';

  @override
  String get icon_typeb => 'Tipo B';

  @override
  String get icon_typec => 'Tipo C';

  @override
  String get icon_sash_typesplit => 'Tipo de división';

  @override
  String get icon_mystitchmonitor => 'My Stitch Monitor';

  @override
  String get icon_mydesignsnap => 'My Design Snap';

  @override
  String get icon_mystitchmonitor_t => 'IQ Intuition Monitoring';

  @override
  String get icon_mydesignsnap_t => 'IQ Intuition Positioning';

  @override
  String get icon_actcode => 'Código de activación';

  @override
  String get icon_machineno => 'Número de máquina (No.)';

  @override
  String get icon_autodl => 'Descarga automática';

  @override
  String get icon_updatemanu => 'Actualizar manualmente';

  @override
  String get icon_dl_updateprogram => 'Descargue el programa de actualización';

  @override
  String get icon_dl_updateprogram_2 => 'Descargue el programa de\nactualización';

  @override
  String get icon_chk_update => 'Buscar actualizaciones';

  @override
  String get icon_pause => 'Pausa';

  @override
  String get icon_resume => 'Reanudar';

  @override
  String get icon_cert_method => 'Método de certificación';

  @override
  String get icon_latestver => 'Última versión';

  @override
  String get icon_latestveravail => 'Última versión disponible';

  @override
  String get icon_device_ios => 'Para dispositivos\niOS';

  @override
  String get icon_device_android => 'Para dispositivos\nAndroid™';

  @override
  String get icon_f_ios => 'Para iOS';

  @override
  String get icon_f_android => 'Para Android™';

  @override
  String get icon_cws_myconnection => 'CanvasWorkspace\n (Mi Conexión)';

  @override
  String get icon_step1 => 'PASO1:';

  @override
  String get icon_step2 => 'PASO2:';

  @override
  String get icon_step3 => 'PASO3:';

  @override
  String get icon_step4 => 'PASO4:';

  @override
  String get icon_step5 => 'PASO5:';

  @override
  String get icon_register => 'Registrar';

  @override
  String get icon_loginid => 'ID de usuario:';

  @override
  String get icon_id => 'ID:';

  @override
  String get icon_appq1 => 'Parche de aplique\n (normal)';

  @override
  String get icon_appq2 => 'Parche de aplique\npara colores\nseleccionados';

  @override
  String get icon_original_img => 'Imagen original';

  @override
  String get icon_appq_stitch_1 => 'Puntada\nen zigzag';

  @override
  String get icon_appq_stitch_2 => 'Puntada\nde satén';

  @override
  String get icon_appq_stitch_3 => 'Puntada recta';

  @override
  String get icon_stamp_web => 'Cortar contornos';

  @override
  String get icon_cws_rgs_title => 'Obtenga un código PIN para registrar su máquina.';

  @override
  String get icon_cws_rgs_s1 => 'Inicie sesión en CanvasWorkspace.\nhttp://CanvasWorkspace.Brother.com';

  @override
  String get icon_cws_rgs_s2 => 'Pulse [Ajustes de cuenta].';

  @override
  String get icon_pincode => 'Código PIN';

  @override
  String get icon_kitsnc => 'ScanNCut (Mi Conexión)';

  @override
  String get icon_snc1 => 'ScanNCut';

  @override
  String get icon_f_om_kitsnc => 'ScanNCut (Mi Conexión)\n   (English)';

  @override
  String get icon_density_mm => 'línea/mm';

  @override
  String get icon_density_inch => 'línea/inch';

  @override
  String get icon_machineregist => 'Registro de la máquina';

  @override
  String get icon_snj_myconnection => 'Artspira / Mi Conexión';

  @override
  String get icon_snj_rgs_title => 'Obtenga un código PIN para registrar su máquina.';

  @override
  String get icon_snj_rgs_s1_iivo1 => 'Inicie sesión en Artspira.\nhttps://s.brother/snjumq4211';

  @override
  String get icon_snj_rgs_s2 => 'Toque [Configuración de la máquina] y toque [Registrar] en Máquina de bordar; a continuación, seleccione [Modelo de LAN inalámbrica].';

  @override
  String get icon_snj_rgs_s3 => 'Introduzca el número siguiente en Artspira y obtenga el código PIN.';

  @override
  String get icon_snj_rgs_pin => 'Introduzca el código PIN en la pantalla siguiente.';

  @override
  String get icon_cws_rgs_s3 => 'Pulse [Registro de la(s) Máquina(s)] y seleccione [Registre una Nueva Máquina de coser].';

  @override
  String get icon_cws_rgs_s4 => 'Introduzca el número siguiente en la pantalla web y obtenga el código PIN.';

  @override
  String get icon_cws_rgs_pin => 'Introduzca el código PIN en la pantalla siguiente.';

  @override
  String get icon_transfer => 'Transfer.';

  @override
  String get icon_app_selectcolor => 'Seleccione los colores para el parche de aplique';

  @override
  String get icon_texture => 'Textura';

  @override
  String get icon_userthread => 'Hilo del usuario';

  @override
  String get icon_senju => 'Artspira';

  @override
  String get icon_notnow => 'Ahora no';

  @override
  String get icon_builtin => 'Integrado';

  @override
  String get icon_user => 'Personalizado';

  @override
  String get icon_clearall => 'Borrar todo';

  @override
  String get icon_taperingtitle => 'Estrechamiento';

  @override
  String get icon_tapering01 => 'Inicio';

  @override
  String get icon_tapering02 => 'Fin';

  @override
  String get icon_tapering03 => 'Estilo de finalización';

  @override
  String get icon_tapering03_2 => 'Estilo de final.';

  @override
  String get icon_tapering04 => 'Ángulo inicial';

  @override
  String get icon_tapering05 => 'Ángulo final';

  @override
  String get icon_tapering06 => 'Repetición de patrones';

  @override
  String get icon_tapering06_s => 'Repetición    ';

  @override
  String get icon_times => 'veces';

  @override
  String get icon_approx_s => 'Aprox.';

  @override
  String get icon_e2etitle => 'Edge-To-Edge Quilt';

  @override
  String get icon_e2e01 => 'Opción de invertir';

  @override
  String get icon_e2e01_2 => 'Opción de invertir';

  @override
  String get icon_e2e02 => 'fila(s)';

  @override
  String get icon_e2e03 => 'pieza(s)';

  @override
  String get icon_sr_title => 'Regulador de puntadas';

  @override
  String get icon_sr_mode_title => 'Modo';

  @override
  String get icon_sr_mode_00exp => 'Paso 1 - Seleccione un modo.\nPaso 2 - Seleccione una puntada.\n  *La puntada de hilvanado se selecciona automáticamente en el modo 3.\nPaso 3 - Empiece a coser.';

  @override
  String get icon_sr_mode01exp => 'Modo intermitente\n\nCuando no hay movimiento de la tela, la aguja se detiene en la parte superior, y la aguja baja después de moverse la longitud especificada. Tenga cuidado de no poner la mano debajo de la aguja.';

  @override
  String get icon_sr_mode02exp => 'Modo continuo\n\nCuando no hay movimiento de la tela, la aguja baja lentamente en la misma posición para la costura de remates o para una longitud de puntada más corta que la especificada, como en las esquinas de un patrón.';

  @override
  String get icon_sr_mode03exp => 'Modo de hilvanado\n\nLa aguja baja a intervalos más largos para el hilvanado. Tenga cuidado de no poner la mano debajo de la aguja.';

  @override
  String get icon_sr_mode04exp => 'Modo de costura libre\n\nCoser a la velocidad especificada';

  @override
  String get icon_sr_mem_mode01 => 'Intermitente';

  @override
  String get icon_sr_mem_mode02 => 'Continuo';

  @override
  String get icon_sr_modemem_03 => 'Hilvanado';

  @override
  String get icon_sr_mem_mode04 => 'Movimiento libre';

  @override
  String get icon_sr_sensingline => 'Línea de detección';

  @override
  String get icon_sr_footheight => 'Altura SR';

  @override
  String get icon_unselect => 'Deseleccionar';

  @override
  String get icon_filter => 'Filtro';

  @override
  String get icon_filterapplied => 'Filtro aplicado';

  @override
  String get icon_apply => 'Aplicar';

  @override
  String get icon_upperlimit => 'Limite superior';

  @override
  String get icon_lowerlimit => 'Límite inferior';

  @override
  String get icon_all => 'Todos';

  @override
  String get icon_bh_guide01 => 'Guía de ojales';

  @override
  String get icon_bh_guide02 => 'Adorno';

  @override
  String get icon_bh_guide03 => 'Espacio';

  @override
  String get icon_bh_guide04 => 'Guía de bordes de tela';

  @override
  String get icon_bh_guide05 => 'Distancia desde el borde';

  @override
  String get icon_colorchanges => 'Cambios de color';

  @override
  String get icon_voiceguidance_title => 'Guía de voz';

  @override
  String get icon_voicevolume => 'Volumen de voz';

  @override
  String get icon_voice_01eng_a => 'English-A';

  @override
  String get icon_voice_01eng_b => 'English-B';

  @override
  String get icon_voice_02deu_a => 'Deutsch-A';

  @override
  String get icon_voice_02deu_b => 'Deutsch-B';

  @override
  String get icon_voice_03fra_a => 'Français-A';

  @override
  String get icon_voice_03fra_b => 'Français-B';

  @override
  String get icon_voice_04ita_a => 'Italiano-A';

  @override
  String get icon_voice_04ita_b => 'Italiano-B';

  @override
  String get icon_voice_05nld_a => 'Nederlands-A';

  @override
  String get icon_voice_05nld_b => 'Nederlands-B';

  @override
  String get icon_voice_06esp_a => 'Español-A';

  @override
  String get icon_voice_06esp_b => 'Español-B';

  @override
  String get icon_voice_07jpn_a => '日本語-A';

  @override
  String get icon_voice_07jpn_b => '日本語-B';

  @override
  String get icon_embcate_photostitch => 'Bordado\nPicture Play';

  @override
  String get icon_photos_title => 'Función de bordado Picture Play';

  @override
  String get icon_photos_01 => 'Seleccione un archivo de imagen (JPG, BMP, PNG).';

  @override
  String get icon_photos_02 => 'Ajuste del tamaño';

  @override
  String get icon_photos_03 => 'Eliminar fondo';

  @override
  String get icon_photos_04 => 'Encuadrar la imagen';

  @override
  String get icon_photos_05 => 'Ajustar al bastidor';

  @override
  String get icon_photos_06 => 'Auto (AI)';

  @override
  String get icon_photos_07 => 'Manual';

  @override
  String get icon_photos_08 => 'Seleccione un estilo para convertirlo mediante IA.';

  @override
  String get icon_photos_09 => 'Ajuste de color';

  @override
  String get icon_photos_10 => 'Bordes acentuados';

  @override
  String get icon_photos_11 => 'Brillo';

  @override
  String get icon_photos_12 => 'Contraste';

  @override
  String get icon_photos_13 => 'Saturación';

  @override
  String get icon_photos_14 => 'Importar un archivo de imagen (JPG, BMP, PNG)';

  @override
  String get icon_photos_15 => 'Ajustes de bordado';

  @override
  String get icon_style0 => 'Original';

  @override
  String get icon_style1 => 'Estilo 1';

  @override
  String get icon_style2 => 'Estilo 2';

  @override
  String get icon_style3 => 'Estilo 3';

  @override
  String get icon_style4 => 'Estilo 4';

  @override
  String get icon_style5 => 'Estilo 5';

  @override
  String get icon_style6 => 'Estilo 6';

  @override
  String get icon_style7 => 'Estilo 7';

  @override
  String get icon_style8 => 'Estilo 8';

  @override
  String get icon_style9 => 'Estilo 9';

  @override
  String get icon_style10 => 'Estilo 10';

  @override
  String get icon_style1_name => 'Arte iconográfico';

  @override
  String get icon_style2_name => 'Art Déco';

  @override
  String get icon_style3_name => 'Boceto a lápiz';

  @override
  String get icon_style4_name => 'Pasteles al óleo';

  @override
  String get icon_style5_name => 'Cartel de neón';

  @override
  String get icon_style6_name => 'Modernismo';

  @override
  String get icon_style7_name => 'Atrevido y vivo';

  @override
  String get icon_style8_name => 'Vitral';

  @override
  String get icon_style9_name => 'Arte geográfico';

  @override
  String get icon_style10_name => 'Dibujos animados';

  @override
  String get icon_stylusedit => 'Edición del proyector con lápiz óptico';

  @override
  String get icon_projectorsettings => 'Ajustes del proyector';

  @override
  String get icon_setting_srvolume => 'Volumen del timbre SR';

  @override
  String get icon_embcate_bt_01 => 'Colcha';

  @override
  String get icon_embcate_bt_02 => 'Applique';

  @override
  String get icon_embcate_bt_03 => 'Botánico/a';

  @override
  String get icon_embcate_bt_04 => 'Animales';

  @override
  String get icon_embcate_bt_05 => 'Carta';

  @override
  String get icon_embcate_bt_06 => 'Decoración';

  @override
  String get icon_embcate_bt_07 => 'Estaciones';

  @override
  String get icon_embcate_bt_08 => 'Encaje 3D';

  @override
  String get icon_embcate_bt_09 => 'Encaje de ganch.';

  @override
  String get icon_embcate_bt_10 => 'En el aro';

  @override
  String get icon_embcate_b_01 => 'Colcha 2';

  @override
  String get icon_embcate_b_02 => 'Diseños de colcha de Anna Aldmon';

  @override
  String get icon_embcate_b_03 => 'Applique 2';

  @override
  String get icon_embcate_b_04 => 'Botánico/a 2';

  @override
  String get icon_embcate_b_05 => 'Rosas de Pierre-Joseph Redouté';

  @override
  String get icon_embcate_b_06 => 'Diseño Zündt';

  @override
  String get icon_embcate_b_07 => 'Zentangle';

  @override
  String get icon_embcate_b_08 => 'Animales 2';

  @override
  String get icon_embcate_b_09 => 'Carta 2';

  @override
  String get icon_embcate_b_10 => 'Deportivo/a';

  @override
  String get icon_embcate_b_11 => 'Marino/a';

  @override
  String get icon_embcate_b_12 => 'Víveres';

  @override
  String get icon_embcate_b_13 => 'Niños/as';

  @override
  String get icon_embcate_b_14 => 'Decoración 2';

  @override
  String get icon_embcate_b_15 => 'Encaje 3D 2';

  @override
  String get icon_legalinfo => 'Información legal';

  @override
  String get icon_legal_opensource => 'Open Source Licensing Remarks\n(Observaciones sobre licencias de código abiert)';

  @override
  String get icon_legal_thirdpartysoft => 'Third-Party Software\n(Software de terceros)';

  @override
  String get icon_nousb => '－－－－－－';

  @override
  String get icon_randomfill => 'Relleno aleatorio';

  @override
  String get icon_selarea => 'Seleccionar zona';

  @override
  String get icon_maxnumber_patt => 'Distancia mín.';

  @override
  String get t_err01 => 'El dispositivo de seguridad ha sido activado.\n¿Está el hilo enredado?\n¿Está la aguja doblada?';

  @override
  String get t_err02 => 'Compruebe y enhebre de nuevo el hilo superior.';

  @override
  String get t_err02_emb => 'Compruebe y enhebre de nuevo el hilo superior.\n\n* Toque la tecla de cambio de posición del bastidor en la pantalla de bordado para mover el bastidor al centro.';

  @override
  String get t_err03 => 'Suba la palanca del pie prensatela.';

  @override
  String get t_err04 => 'Baje la palanca del pie prensatela.';

  @override
  String get t_err05 => 'No hay ninguna tarjeta de memoria de bordado en la unidad de lectura. Inserte una tarjeta de memoria de bordado.';

  @override
  String get t_err06 => 'Esta tarjeta de memoria de bordado no se puede usar. Las tarjetas no utilizables son tarjetas con licencia de venta en otros países, tarjetas que no contienen patrones de bordado, etc.';

  @override
  String get t_err07 => 'Imposible añadir más costuras a esta combinación.';

  @override
  String get t_err07_u => 'No pueden combinarse mas puntadas.';

  @override
  String get t_err08 => 'Este botón no funciona si la unidad de bordado no está colocada. Apague la máquina y coloque la unidad de bordado.';

  @override
  String get t_err09 => 'Este botón no funciona si la unidad de bordado está colocada.';

  @override
  String get t_err10 => 'Este botón no funciona si la unidad de bordado está colocada. Apague la máquina y desmonte la unidad de bordado.';

  @override
  String get t_err11 => 'El pedal no se puede utilizar cuando la unidad de bordado está colocada.\nDesmonte el pedal.';

  @override
  String get t_err_corrupteddataturnoff => 'Imposible leer los datos. Puede que los datos estén dañados.\n\nApague la máquina y vuelva a encenderla.';

  @override
  String get t_err12 => 'El volumen de datos es demasiado grande para esta costura.';

  @override
  String get t_err13 => 'Esta tecla no funciona cuando la aguja está en posición baja. Eleve la aguja y pulse la tecla de nuevo.';

  @override
  String get t_err15 => 'El botón de \"inicio/parar\" no funciona con el pedal colocado.\nDesmonte el pedal.';

  @override
  String get t_err16 => 'Termine de editar la costura antes de coserla.';

  @override
  String get t_err16_e => 'Acabe de editar los patrones antes de bordar.';

  @override
  String get t_err16_u => 'Acabe de editar los datos de puntadas antes de coser.';

  @override
  String get t_err17 => 'Suba la palanca para ojales.';

  @override
  String get t_err18 => 'Baje la palanca para ojales.';

  @override
  String get t_err19 => 'Imposible cambiar la configuración de caracteres.';

  @override
  String get t_err22 => 'Seleccione una costura.';

  @override
  String get t_err22_u => 'Seleccione una puntada.';

  @override
  String get t_err23 => 'Guardando…';

  @override
  String get t_err24 => 'El hilo de la bobina está casi agotado.';

  @override
  String get t_err25 => 'El cartucho de la unidad de bordado se moverá.\nMantenga las manos, etc. alejadas del cartucho.';

  @override
  String get t_err26 => 'Borrando…';

  @override
  String get t_err27 => 'No hay suficiente memoria disponible para guardar la costura.\n¿Desea borrar otra costura?';

  @override
  String get t_err27_d => 'No hay suficiente memoria disponible para guardar la costura.\n¿Desea borrar otros datos?';

  @override
  String get t_err61 => 'No hay suficiente memoria disponible para guardar la costura.';

  @override
  String get t_err61_d => 'No hay suficiente memoria disponible para guardar la costura.\nElimine algunos patrones o utilice un dispositivo diferente.';

  @override
  String get t_err61_dd => 'No hay suficiente memoria disponible para guardar la costura.\nElimine algunos datos o utilice un dispositivo diferente.';

  @override
  String get t_err28 => 'Recuperando costura. Por favor, espere.';

  @override
  String get t_err28_d => 'Recuperando costura. Por favor, espere.';

  @override
  String get t_err29 => '¿Desea borrar la costura seleccionada?';

  @override
  String get t_err65 => '¿Desea borrar la costura seleccionada?';

  @override
  String get t_err29_d => '¿Desea eliminar los archivos de datos seleccionados?';

  @override
  String get t_err30 => '¿Desea guardar esta configuración?';

  @override
  String get t_err33 => 'Desmonte el bastidor.';

  @override
  String get t_err34 => 'Coloque el bastidor.';

  @override
  String get t_err36 => 'Cuando el control de velocidad está ajustado para controlar la anchura de la costura de zig-zag, el botón de \"inicio/parar\" no funciona.';

  @override
  String get t_err37 => 'El dispositivo de seguridad de la devanadora está activado.\n¿Está el hilo enredado?';

  @override
  String get t_err38 => 'Imposible usar esta función con la máquina en modo de aguja doble.\nCancele el modo de aguja doble y seleccione de nuevo la función.';

  @override
  String get t_err_twinn_10 => 'La placa de la aguja recta no puede utilizarse en modo de aguja gemela.\nRetire la aguja gemela y cancele el modo de aguja gemela.';

  @override
  String get t_err_twinn_11 => 'Se canceló el modo de aguja gemela.\nRetire la aguja gemela.';

  @override
  String get t_err_twinn_12 => 'Confirme que se ha retirado la aguja gemela.';

  @override
  String get t_err42 => 'Compruebe el resultado y pulse OK.';

  @override
  String get t_err48 => 'Imposible leer los datos de la costura seleccionada. Puede que los datos estén dañados.';

  @override
  String get t_err50 => 'Coloque el portabobina a la izquierda.';

  @override
  String get t_err53 => 'La aguja está en posición baja.\nPulse el botón de posición de aguja para subir la aguja.';

  @override
  String get t_err55 => 'Coloque el pie para ojales “A＋”.\nLa cámara incorporada detecta el pie para ojales \"A＋\" por la marca \"A＋\" y los tres puntos.';

  @override
  String get t_err56 => 'No se puede reconocer el tamaño del botón.\nCompruebe que los tres puntos sean fácilmente visibles, o introduzca los valores para la longitud de la ranura y vuelva a intentarlo.';

  @override
  String get t_err63 => 'Imposible usar esta función de edición mientras la costura se encuentre fuera del margen de la línea roja. Desplace primero el dibujo de la costura.';

  @override
  String get t_err64 => 'Incluye un patrón especial que no puede guardarse en la memoria externa.\nGuarde el patrón en la memoria de la máquina.';

  @override
  String get t_err69 => 'Existe un patrón que no puede guardarse incluido.';

  @override
  String get t_err76 => 'Coloque un bastidor más grande.';

  @override
  String get t_err77 => 'Este patrón no puede utilizarse con este bastidor de bordado.\nSustitúyalo por el bastidor apropiado.';

  @override
  String get t_err82 => '¿Desea volver a los cambios de color anteriores?';

  @override
  String get t_err83 => '¿Desea sobrescribir los datos?';

  @override
  String get t_err84 => '¿Desea recuperar y reanudar la memoria anterior?';

  @override
  String get t_err88 => 'No se puede bordar porque la unidad de bordado no está colocada. Apague la máquina y coloque la unidad de bordado.';

  @override
  String get t_err89 => 'El dispositivo USB no está cargado. Cárguelo.';

  @override
  String get t_err90 => 'Este dispositivo USB no puede utilizarse.';

  @override
  String get t_err_usb_notcompati => 'El dispositivo USB conectado no es compatible con el equipo.\nUtilice un dispositivo USB diferente.';

  @override
  String get t_err93 => 'El dispositivo USB puede estar dañado.\nUtilice un dispositivo USB diferente y, a continuación, intente guardar de nuevo.';

  @override
  String get t_err94 => 'No hay suficiente espacio.\nBorre algunas costuras o utilice un dispositivo USB distinto.';

  @override
  String get t_err94_cmn => 'No hay suficiente espacio.\nElimine algunos patrones o utilice un dispositivo diferente.';

  @override
  String get t_err95 => 'Se ha cambiado el dispositivo USB. No cambie el dispositivo USB mientras se está leyendo.';

  @override
  String get t_err95_cmn => 'Se ha cambiado el dispositivo.\nNo cambie el dispositivo mientras se está leyendo.';

  @override
  String get t_err96 => 'El dispositivo USB está protegido contra escritura, por lo que los datos no pueden guardarse. Cancele la protección contra escritura antes de intentar guardar los datos.';

  @override
  String get t_err96_cmn => 'El dispositivo está protegido contra escritura, por lo que los datos no pueden guardarse.\nCancele la protección contra escritura antes de intentar guardar los datos.';

  @override
  String get t_err97 => 'El dispositivo USB está protegido contra escritura por lo que los datos no pueden borrarse. Cancele la protección contra escritura antes de intentar borrar los datos.';

  @override
  String get t_err97_cmn => 'El dispositivo está protegido contra escritura, por lo que los datos no pueden borrarse.\nCancele la protección contra escritura antes de intentar borrar los datos.';

  @override
  String get t_err98 => 'Error del dispositivo USB';

  @override
  String get t_err98_cmn => 'Error en el dispositivo';

  @override
  String get t_err99 => 'No puede leerse el dispositivo USB.\nEl dispositivo USB puede estar dañado.';

  @override
  String get t_err100 => 'Formateando el dispositivo USB…';

  @override
  String get t_err101 => 'Transmitiendo por USB';

  @override
  String get t_err101_cmn => 'Acceder al dispositivo';

  @override
  String get t_err103 => 'Por favor, espere.';

  @override
  String get t_err104 => 'No funciona cuando las costuras sobrepasan el marco azul.';

  @override
  String get t_err106 => '¿Desea bordar el segmento siguiente?';

  @override
  String get t_err107 => 'Bordado terminado.';

  @override
  String get t_err108 => 'Los bolsillos están llenos.';

  @override
  String get t_err109 => 'Utilice el botón del elevador del pie prensatela para subir el pie.';

  @override
  String get t_err110 => 'Utilice el botón del elevador del pie prensatela para bajar el pie.';

  @override
  String get t_err111 => 'Enhebrado incorrecto. Pulse de nuevo el botón de enhebrado automático.';

  @override
  String get t_err113 => 'El programa se actualizará.\nCargue el programa en la máquina con el USB.';

  @override
  String get t_err116 => 'Error de datos';

  @override
  String get t_err117 => 'Error de ROM Flash';

  @override
  String get t_err118 => 'Ha ocurrido un fallo. Apague la máquina y vuelva a encenderla.';

  @override
  String get t_err119 => 'Desactive la máquina antes de colocar o retirar la placa de la aguja.';

  @override
  String get t_err120 => '¿Desea mover el carro de bordado a su posición anterior?';

  @override
  String get t_err120_2 => 'Retire el bastidor de bordado y sustituya la bobina.\nA continuación, coloque el bastidor y toque Aceptar para moverlo a la posición anterior.';

  @override
  String get t_err121 => '¿Desea separar la costura con bordes combinada?';

  @override
  String get t_err122 => 'Este dispositivo USB es incompatible.';

  @override
  String get t_err122_cmn => 'Este dispositivo es incompatible.';

  @override
  String get t_err123 => 'El dispositivo USB no está colocado o el conector no está enchufado.';

  @override
  String get t_err124 => 'Ha ocurrido un fallo.\nApague la máquina y vuelva a encenderla.';

  @override
  String get t_err125 => 'Es posible que el hilo superior no esté enhebrado correctamente.\nEnhebre el hilo superior desde el principio.';

  @override
  String get t_err126 => '¿Desea bajar automáticamente el pie prensatela?';

  @override
  String get t_err127 => 'No se puede utilizar el botón de enhebrado automático en el modo de aguja doble.';

  @override
  String get t_err128 => 'Asegúrese de que el bastidor está colocado lo más atrás posible. BLOQUEE LA PALANCA DE FIJACIÓN DEL BASTIDOR.';

  @override
  String get t_err129 => 'Coloque el portabobina a la derecha.';

  @override
  String get t_err130 => 'El pie prensatela se moverá para arriba y abajo. Mantenga sus manos, etc. lejos del pie prensatela.';

  @override
  String get t_err131 => 'Este patrón no puede utilizarse.';

  @override
  String get t_err132 => 'Antes de bordar, asegúrese de que el bastidor está colocado lo más atrás posible y la palanca de fijación está bloqueada. Pulse \"inicio/parar\".';

  @override
  String get t_err133 => 'Este bastidor no puede utilizarse.';

  @override
  String get t_err134 => 'Este diseño no puede utilizarse porque sobrepasa el límite de la capacidad de datos.';

  @override
  String get t_err136 => 'Mantenimiento preventivo recomendado.';

  @override
  String get t_err137 => 'Mantenimiento preventivo recomendado.\nSe han superado las 1000 horas.';

  @override
  String get t_err208 => 'Calculando…';

  @override
  String get t_err209 => 'El carro de la unidad de bordado se moverá.';

  @override
  String get t_err210 => 'Reconociendo…';

  @override
  String get t_err213 => 'No se puede reconocer la marca de posición de bordado.';

  @override
  String get t_err215 => 'Quite la marca de posición de bordado.';

  @override
  String get t_err224 => 'Hay polvo o manchas en el papel blanco o la tela. Sustituya por papel blanco o tela limpios.';

  @override
  String get t_err227 => 'Ajuste o reconocimiento incorrectos.';

  @override
  String get t_err228 => 'Este archivo supera la capacidad de datos y no se puede utilizar.\nUse un archivo con el tamaño adecuado.';

  @override
  String get t_err229 => 'No se puede utilizar este archivo.';

  @override
  String get t_err229_b => 'No puede leerse esta versión del archivo.';

  @override
  String get t_err239 => 'Conectado al PC. No desconecte el cable USB.';

  @override
  String get t_err241 => 'No se puede leer el archivo.';

  @override
  String get t_err242 => 'Fallo al guardar el archivo.';

  @override
  String get t_err244 => '¿Desea borrar la imagen seleccionada?';

  @override
  String get t_err245 => 'Esta tecla no se puede utilizar en este momento.';

  @override
  String get t_err246 => 'El patrón sobrepasa la zona de patrón.\nCambie la posición del patrón y escanee la zona nueva.';

  @override
  String get t_err247 => 'No hay suficiente memoria disponible para guardar.';

  @override
  String get t_err248 => '¿Desea borrar el ajuste?';

  @override
  String get t_err249 => 'No se puede reconocer el borde de la tela.';

  @override
  String get t_err250 => 'Este diseño no puede convertirse.';

  @override
  String get t_err251 => '¿Desea reajustar el tamaño y la posición del bordado?';

  @override
  String get t_err252 => '¿Desea reajustar el tamaño del bordado?';

  @override
  String get t_err253 => 'Detecta grosor del tejido.\nColoque la pegatina de posición en la línea roja.';

  @override
  String get t_err254 => 'Detección correcta.\nRetire la marca de posición del bordado. Pulse la tecla OK para iniciar la captura del fondo.';

  @override
  String get t_err255 => 'Pulse la tecla OK y, a continuación, el bastidor se moverá y se iniciará la captura del fondo.';

  @override
  String get t_err256 => 'Fallo en la detección.\n¿Desea repetirla?';

  @override
  String get t_err257 => 'Certificación correcta.\nReinicie la máquina de coser.';

  @override
  String get t_err257_1 => 'Certificación correcta.\nReinicie la máquina de coser.';

  @override
  String get t_err257_2 => 'Certificación correcta.\nReinicie la máquina.';

  @override
  String get t_err259 => 'Certificar kit de actualización.\n\nIndique Número de kit para certificar.';

  @override
  String get t_err260 => 'Introduzca la clave de certificación y, a continuación, pulse [CONFIGURAR].';

  @override
  String get t_err261 => 'Certificando…';

  @override
  String get t_err262 => 'Clave de certificación incorrecta.\nCompruebe la clave y vuelva a escribirla.';

  @override
  String get t_err263 => 'KIT';

  @override
  String get t_err264 => '¿Desea eliminar la imagen del fondo?';

  @override
  String get t_err265 => '¿Desea restablecer el patrón a su tamaño, ángulo y posición originales?';

  @override
  String get t_err343 => '¿Desea volver al ángulo y/o posición originales?';

  @override
  String get t_err266 => 'Kit de actualización de certificado.';

  @override
  String get t_err270 => 'Pegue la primera marca de posición del bordado en el material de forma segura, de modo que la marca quede dentro del marco rojo. El carro de la unidad de bordado se moverá después de pulsar la tecla Escaneo.';

  @override
  String get t_err271 => 'Pegue la segunda marca de posición del bordado en el material de forma segura, de modo que la marca quede dentro del marco rojo. El carro de la unidad de bordado se moverá después de pulsar la tecla Escaneo.';

  @override
  String get t_err273 => 'La marca de posición del bordado no está colocada correctamente.\nQuítela y vuelva a colocarla.';

  @override
  String get t_err274 => 'Marcas de posición reconocidas.\nDeje colocadas las marcas y vuelva a colocar el material en el bastidor. Centros de las marcas de posición en la zona de bordado para después seleccionar un patrón.';

  @override
  String get t_err276 => 'Marcas de posición del bordado reconocidas.\nQuite las marcas de posición del bordado.';

  @override
  String get t_err277 => '¿Seguro que desea “Cancelar” la conexión del patrón?';

  @override
  String get t_err278 => 'El patrón de la siguiente sección no puede bordarse después de salir. ¿Seguro que desea finalizar la conexión del patrón?';

  @override
  String get t_err279 => 'Bordado finalizado.\n¿Desea conectar el siguiente patrón?\n\n* No retire el material del bastidor.\n* Si desea continuar más tarde, seleccione y ajuste la siguiente sección del patrón. Se puede reanudar si la máquina lo ha leído.';

  @override
  String get t_err282 => 'No se puede introducir nada más.';

  @override
  String get t_err283 => 'La aplicación se cerrará.';

  @override
  String get t_err284 => 'Este dato es demasiado complicado y no se puede convertir.';

  @override
  String get t_err286 => '¿Desea terminar de editar la línea?';

  @override
  String get t_err288 => 'Marcas de posición del bordado reconocidas. Quite y vuelva a colocar las marcas de posición del bordado en las nuevas posiciones.';

  @override
  String get t_err290 => 'Marcas de posición del bordado no reconocidas.\nQuite y vuelva a pegar las marcas de posición del bordado.\nLos centros de las marcas de posición del bordado deben estar en la zona de bordado.';

  @override
  String get t_err291 => 'No hay suficiente espacio. Utilice un dispositivo USB diferente.';

  @override
  String get t_err291_cmn => 'No hay suficiente espacio.\nUtilice un dispositivo diferente.';

  @override
  String get t_err292 => 'No hay suficientes colores en la tabla de hilos para el modo seleccionado.';

  @override
  String get t_err292_s => 'No hay suficientes colores en la paleta actual para el modo seleccionado.';

  @override
  String get t_err297 => 'Repita el proceso desde el paso 3 al 4.';

  @override
  String get t_err298 => 'El adhesivo está sucio.';

  @override
  String get t_err_cameracalib_ng_msg => 'Reconocimiento incorrecto.\nPegue un nuevo adhesivo blanco.';

  @override
  String get t_err_cameracalib_ok_msg => 'Pulse la tecla Aceptar para memorizar la posición de entrada de la aguja.';

  @override
  String get t_err299 => 'Si después de varias pruebas el ajuste no es correcto, póngase en contacto con el distribuidor más cercano.';

  @override
  String get t_err300 => 'Si fuera necesario, consulte el manual de instrucciones y la guía de referencia para ver la aguja recomendada.';

  @override
  String get t_err_cameracalib_title => 'Calibración de la aguja para cámara/proyector';

  @override
  String get t_err_cameracalib_1_4 => '1. Pulse el botón de posición de aguja para subir la\n aguja.\n2. Después de retirar la aguja y el pie prensatela, pegue\n el adhesivo blanco en el área del punto de entrada de\n la aguja.\n3. Inserte la aguja (tamaño estándar 75/11 o 90/14).\n4. Pulse la tecla INICIAR para comenzar el proceso de\n calibración.\nPara su seguridad, compruebe que la zona de alrededor\n de la aguja esté despejada antes de pulsar la tecla\n \"INICIAR\".\n\n* Asegúrese de mantener las manos y otros objetos\n alejados de la aguja, de lo contrario podría hacerse daño.';

  @override
  String get t_err303 => 'Bordado finalizado.\n¿Desea conectar el siguiente patrón?';

  @override
  String get t_err304 => 'No retire el material del bastidor.\nPulse la tecla OK para seleccionar el siguiente patrón.';

  @override
  String get t_err307 => 'No quite las marcas de posición del bordado.\nVuelva a colocar el material en el bastidor de forma que el siguiente patrón y los centros de las dos marcas de posición del bordado se encuentren en la zona de bordado.';

  @override
  String get t_err308 => 'El patrón siguiente está fuera de la zona de bordado.\nVuelva a colocar el material en el bastidor de forma que el siguiente patrón y los centros de las dos marcas de posición del bordado se encuentren en la zona de bordado.';

  @override
  String get t_err309 => 'No se reconoce la marca de posición del bordado.\nVuelva a colocar el material en el bastidor de forma que el siguiente patrón y los centros de las dos marcas de posición del bordado se encuentren en la zona de bordado.';

  @override
  String get t_err310 => 'Se ha cambiado la posición de las marcas de posición del bordado.\nVuelva a colocar el material en el bastidor de forma que el siguiente patrón y los centros de las dos marcas de posición del bordado se encuentren en la zona de bordado.';

  @override
  String get t_err311 => 'Marcas de posición del bordado reconocidas.\nQuite las marcas de posición del bordado y borde el patrón.';

  @override
  String get t_err311_size => 'Marcas de posición del bordado reconocidas.\nQuite las marcas de posición del bordado y borde el patrón.\n\n* El tamaño del siguiente patrón se ha ajustado automáticamente, ya que la distancia entre las marcas ha cambiado al volver a colocar la tela.';

  @override
  String get t_err311_rehoop => 'La distancia entre las marcas ha quedado fuera de posición al volver a colocar la tela.\nVuelva a colocar la tela para que la distancia entre las marcas sea de la siguiente longitud.';

  @override
  String get t_err312 => 'No quite las marcas de posición del bordado.\nEs necesario volver a colocar las marcas de posición del bordado.\nVuelva a colocar el material en el bastidor.';

  @override
  String get t_err313 => 'No se reconoce la marca de posición del bordado.\nVuelva a colocar el material en el bastidor.';

  @override
  String get t_err314 => 'Marcas de posición del bordado reconocidas.\nQuite las marcas de posición del bordado.';

  @override
  String get t_err354 => 'Se ha activado el modo de compatibilidad de apagado. Apague la máquina.';

  @override
  String get t_err356 => 'Esta puntada no es compatible con el \"Modo de avance doble\".';

  @override
  String get t_err359 => 'Extraiga el módulo de avance doble de la máquina.';

  @override
  String get t_err360 => 'Ajuste el reloj.';

  @override
  String get t_err361 => 'Seleccione el idioma.';

  @override
  String get t_err362 => 'Extraiga el pie de bordado con puntero LED de la máquina.';

  @override
  String get t_err364 => 'Error del módulo';

  @override
  String get t_err368 => '¿Desea restablecer el ajuste de borde, la posición y/o el ángulo del patrón?';

  @override
  String get t_err373 => 'El bastidor de bordado se ha cambiado; sustitúyalo por el bastidor original.';

  @override
  String get t_err380 => 'Para enhebrar la aguja, retire la tela de debajo del pie prensatela.';

  @override
  String get t_err381 => '¿Desea cancelar el ajuste del punto final?';

  @override
  String get t_err382 => 'El modo de ajuste del punto final de costura no está disponible con la puntada seleccionada. \nSeleccione una puntada diferente o cambie la longitud de la puntada.';

  @override
  String get t_err383 => 'Después de quitar el adhesivo del punto final, continúe cosiendo.';

  @override
  String get t_err384 => 'El modo de ajuste del fin de puntada no puede utilizarse en este momento. \nSe cancelará el ajuste del punto final.';

  @override
  String get t_err385 => 'Esta distancia es demasiado corta para usar el ajuste del punto final.\nPuede usarlo si la distancia es mayor o si el ajuste de \"Parada temporal\" está desactivado.';

  @override
  String get t_err386 => 'Esta función no puede utilizarse mientras el ajuste del punto final está activado.';

  @override
  String get t_err390 => '¿Quiere eliminar todos los datos de edición e ir a la pantalla inicial?';

  @override
  String get t_err390_old => '¿Desea eliminar todos los patrones y volver a la pantalla inicial?';

  @override
  String get t_err391 => '¿Desea cancelar la actual selección de patrón?';

  @override
  String get t_err391_u => '¿Desea cancelar la actual selección de puntada?';

  @override
  String get t_err392 => 'Transmitir por tarjeta de bordado.';

  @override
  String get t_err393 => 'Este patrón no puede combinarse.';

  @override
  String get t_err394 => 'No hay suficiente memoria disponible para guardar el patrón. \n¿Eliminar un patrón guardado?';

  @override
  String get t_err395 => 'Este patrón no puede cargarse porque supera el área editable.';

  @override
  String get t_err396 => 'Este patrón no puede cargarse porque supera el área editable. \nUtilice los cursores para mover el punto hasta adaptarse al área editable.';

  @override
  String get t_err397 => '¿Desea eliminar la puntada actual?';

  @override
  String get t_err398 => 'Guardar como nuevo archivo…';

  @override
  String get t_err400 => 'El patrón supera los límites del bastidor de bordado.\nSi desea añadir más costuras, gire la combinación de costuras.';

  @override
  String get t_err401 => 'El patrón supera los límites del bastidor de bordado.';

  @override
  String get t_err402 => 'El patrón supera los límites del bastidor de bordado.\nNo añada caracteres adicionales.';

  @override
  String get t_err403 => 'El patrón supera los límites del bastidor de bordado.\nEsta función no se puede utilizar en este momento.';

  @override
  String get t_err404 => 'No se puede cambiar la fuente, ya que algunas letras no están incluidas en la fuente seleccionada.';

  @override
  String get t_err405 => 'El campo del patrón seleccionado supera los límites del bastidor de bordado.';

  @override
  String get t_err406 => 'El patrón supera los límites del bastidor de bordado seleccionado. \n¿Desea cancelar el patrón seleccionado?';

  @override
  String get t_err408 => 'Esta función no puede utilizarse si los patrones se superponen.';

  @override
  String get t_err410 => 'El patrón se puede bordar con un centro o una esquina alineados con la marca de posición del bordado. Coloque la marca de posición del bordado en la posición deseada.';

  @override
  String get t_err411 => 'Una vez completados los preparativos necesarios, pulse la tecla [Escanear].';

  @override
  String get t_err412 => 'No se ha encontrado la marca de posición del bordado en el área de detección.';

  @override
  String get t_err413 => 'Utilice la marca de posición del bordado para conectar patrones.';

  @override
  String get t_err414 => 'Seleccione la posición en la que se\nconectará el siguiente patrón.';

  @override
  String get t_err415 => 'No se pueden leer los datos.';

  @override
  String get t_err416 => 'Se han guardado los datos.\nNombre de archivo:';

  @override
  String get t_err417 => 'Se están leyendo los datos.\nPor favor, espere.';

  @override
  String get t_err418 => 'No se puede utilizar este tipo de archivo.';

  @override
  String get t_err419 => 'Este archivo no puede utilizarse porque es demasiado grande. ';

  @override
  String get t_err420 => 'Error al delinear la imagen. ';

  @override
  String get t_err421 => 'El número de colores de una imagen se reducirá al número que se especifique aquí y, a continuación, se extraerá el contorno.';

  @override
  String get t_err422 => 'Coloque el papel con la ilustración o el dibujo lineal en el marco de escaneado.';

  @override
  String get t_err423 => 'No puede utilizarse un bastidor de bordado. Utilice siempre el bastidor de escaneado.';

  @override
  String get t_err424 => 'La detección puede tardar unos minutos.';

  @override
  String get t_err425 => '¿Desea ir a la pantalla \"Mi centro de diseño\"?';

  @override
  String get t_err426 => '¿Desea ir a la pantalla \"IQ Designer\"?';

  @override
  String get t_err428 => 'Los datos de imagen creados en \"Mi centro de diseño\" no se guardarán. ¿Desea continuar?';

  @override
  String get t_err429 => 'Los datos de \"IQ Designer\" no se guardarán. ¿Desea continuar?';

  @override
  String get t_err430 => 'El bastidor de escaneado no puede utilizarse para bordar.\nSustitúyalo por el bastidor de bordado.';

  @override
  String get t_err432 => 'Coloque en la máquina el bastidor que contiene la imagen que desea escanear.';

  @override
  String get t_err433 => 'Cuando convierta una imagen en imágenes\nde línea o de relleno, utilice el bastidor de escaneado\npara recuperar la correspondiente información\nde color del hilo.';

  @override
  String get t_err440 => 'Seleccione el archivo de imagen.';

  @override
  String get t_err445 => 'Este archivo de imagen no puede utilizarse.';

  @override
  String get t_err446 => 'Escaneando…';

  @override
  String get t_err447 => 'Reconociendo…';

  @override
  String get t_err448 => 'Procesando…';

  @override
  String get t_err451 => '¿Desea borrar todos los datos de imagen editados?';

  @override
  String get t_err452 => 'Los archivos de imagen que pueden utilizarse son archivos JPG, PNG o BMP de un tamaño inferior a 5 MB y 1,2 millones de píxeles.';

  @override
  String get t_err453 => '¿Desea restaurar la configuración por defecto de esta página?';

  @override
  String get t_err454 => 'La combinación de patrones es demasiado grande para el bastidor de bordado identificado. Si desea añadir más patrones, gire la combinación de patrones.';

  @override
  String get t_err455 => 'La combinación de patrones es demasiado grande para el bastidor de bordado identificado.';

  @override
  String get t_err457 => 'Desactive (OFF) la vista de identificación del bastidor de bordado.';

  @override
  String get t_err458 => 'Importar un archivo de imagen.';

  @override
  String get t_err459 => 'Esta unidad de bordado no puede utilizarse.';

  @override
  String get t_err460 => 'Los patrones mostrados en el área de la imagen pueden convertirse.';

  @override
  String get t_err461 => 'El bastidor se moverá y se escaneará con la cámara integrada.';

  @override
  String get t_err462_pp => 'Este archivo de imagen no puede utilizarse.\nLos archivos de imagen que pueden utilizarse son archivos JPG, PNG o BMP de menos de 6 MB y un máximo de 16 millones de píxeles.';

  @override
  String get t_err463 => 'Retire el bastidor de bordado o el bastidor de escaneado.';

  @override
  String get t_err464 => 'El patrón se extiende fuera de la zona de bordado y no puede convertirse.';

  @override
  String get t_err465 => 'Una vez convertido al patrón de bordado, se saldrá de Mi centro de diseño.\n¿Desea ir a la pantalla de edición del bordado?';

  @override
  String get t_err466 => '¿Desea salir de Mi centro de diseño?';

  @override
  String get t_err467 => 'Esta función no puede utilizarse en el modo de patrones de conexión.';

  @override
  String get t_err468 => 'Desactivación de la PCB de la máquina';

  @override
  String get t_err469 => 'Una vez convertido al patrón de bordado, se saldrá de IQ Designer. ¿Desea ir a la pantalla de edición del bordado?';

  @override
  String get t_err470 => '¿Desea salir de IQ Designer?';

  @override
  String get t_err471 => '¿Desea eliminar los datos seleccionados?';

  @override
  String get t_err472 => 'Seleccione múltiples patrones.';

  @override
  String get t_err473 => 'El(los) patrón(es) distribuido(s) se ha(n) guardado como imagen(es).';

  @override
  String get t_err474 => 'Se ha guardado el contorno del(los) patrón(es) distribuido(s).';

  @override
  String get t_err475 => 'Especifique la distancia de desplazamiento alrededor del patrón.';

  @override
  String get t_err478 => 'Recuperación desde la lista de patrones de estampación Mi centro de diseño.';

  @override
  String get t_err478_tc => 'Recuperación desde la lista de patrones de estampación \"IQ Designer\".';

  @override
  String get t_err479 => 'Un patrón para efectos decorativos con bobinas no puede combinarse con un patrón de una categoría diferente.';

  @override
  String get t_err480 => 'Se desplaza a la primera posición de la aguja.';

  @override
  String get t_err481 => 'Ha finalizado la costura de un patrón para efectos decorativos con bobinas.';

  @override
  String get t_err482 => 'Ha finalizado la costura de todos los patrones para efectos decorativos con bobinas.';

  @override
  String get t_err483 => 'Corte los hilos.';

  @override
  String get t_err484_old => 'Antes de bordar los siguientes patrones, compruebe la cantidad y el tipo de hilo de la bobina instalada.';

  @override
  String get t_err484 => 'Vuelva a colocar el hilo de la bobina, y luego coloque el bastidor de bordado.\nEl carro de la unidad de bordado se desplazará después de pulsar OK.';

  @override
  String get t_err485 => 'Se bordará el siguiente patrón para efectos decorativos con bobinas.';

  @override
  String get t_err486 => 'Gire la polea para bajar la aguja en el tejido y, a continuación, tire del hilo de la bobina.';

  @override
  String get t_err489 => 'No existen datos para convertir.';

  @override
  String get t_err496 => '¿Desea aplicar la configuración a todas las áreas?';

  @override
  String get t_err497 => 'Contorno';

  @override
  String get t_err501 => 'No pueden aplicarse los ajustes. No hay suficiente memoria disponible para guardar los atributos.';

  @override
  String get t_err502 => 'La distancia entre los patrones seleccionados es demasiado grande.';

  @override
  String get t_err503 => 'La forma de la línea del aplique es demasiada compleja.';

  @override
  String get t_err503_new => 'Forma demasiado compleja o inadecuada para la línea de aplique.\nCambie los ajustes del aplique o seleccione un patrón diferente.\n* El resultado puede diferir según la posición y el ángulo.';

  @override
  String get t_err504 => 'El tamaño es demasiado grande para añadir una línea de aplique.';

  @override
  String get t_err509 => 'Una parte de la textura no puede mostrarse debido a la estructura de los datos.';

  @override
  String get t_err505 => 'Esta función no puede utilizarse cuando se realiza la clasificación de color.';

  @override
  String get t_err506 => '¿Desea eliminar la marca de hilo?';

  @override
  String get t_err508 => 'La función de marca de hilo no puede utilizarse cuando se anula la selección de la última zona de color.\n¿Desea eliminar la marca de hilo?';

  @override
  String get t_err507 => 'Auto';

  @override
  String get t_err510 => '¿Desea utilizar la imagen actual?';

  @override
  String get t_err511 => 'Los datos se guardaron en la memoria de la máquina.\n¿Desea bordar los datos?';

  @override
  String get t_err515 => 'Desactive la máquina para enfriar el proyector integrado.';

  @override
  String get t_err516 => 'Esta puntada no puede utilizarse con la placa de la aguja actual.';

  @override
  String get t_err517 => 'Cambie la placa de la aguja para coser esta puntada.';

  @override
  String get t_err518 => 'Se ha cambiado el bastidor de bordado. El carro de la unidad de bordado se moverá.\n';

  @override
  String get t_err519 => 'El proyector se desactivará.';

  @override
  String get t_err520 => 'Mantenga las manos, etc. alejadas del cartucho.';

  @override
  String get t_err_521 => 'Pueden descargarse tutoriales en vídeo.';

  @override
  String get t_err574 => 'Se ha producido un funcionamiento incorrecto.\n No puede accederse a EEPROM.';

  @override
  String get t_err575 => 'El hilo de la bobina está casi agotado.\n\n* Utilice el botón “Puntada de remate” para coser una sola puntada de forma repetida y anude las puntadas.\n* Utilice la tecla de mover el bastidor para mover el carro de bordado, de modo que pueda quitarse o instalarse el bastidor de bordado. Después, el carro vuelve a su posición anterior.';

  @override
  String get t_err577 => 'Un patrón para coser cordones no puede combinarse con un patrón de una categoría diferente.';

  @override
  String get t_err578 => 'No pueden seleccionarse más combinaciones de color como favoritas.';

  @override
  String get t_err581 => 'Empiece a coser desde la esquina superior derecha de la tela.\nColoque el bastidor de bordado en la posición de costura inicial.';

  @override
  String get t_err581_b => 'Empiece a coser desde la esquina superior izquierda de la tela.\nColoque el bastidor de bordado en la posición de costura inicial.';

  @override
  String get t_err582 => 'Se ha cosido un lado. Gire la tela 90° hacia la izquierda y vuelva a colocarla en el bastidor de bordado.';

  @override
  String get t_err582_n => 'Se ha cosido un lado. Gire la tela hacia la izquierda y vuelva a colocarla para la siguiente esquina.';

  @override
  String get t_err582_e => 'Una fila está terminada. Para comenzar con la siguiente fila, vuelva a colocar la tela en el borde izquierdo de la siguiente fila, incluyendo la marca de hilo del patrón superior en el aro.';

  @override
  String get t_err583 => 'Utilice las teclas de movimiento del patrón para ajustar la esquina interior del patrón.';

  @override
  String get t_err583_e => 'Utilice las teclas de movimiento del patrón para alinear la esquina superior izquierda del área del patrón con la esquina superior izquierda (marca) del área que desea bordar.';

  @override
  String get t_err584 => 'Utilice las teclas de movimiento del patrón para alinear el punto inicial con el punto final del anterior patrón.';

  @override
  String get t_err584_e => 'Utilice las teclas de movimiento del patrón para alinear la esquina superior izquierda del área del patrón con la marca de hilo inferior izquierda del patrón superior.';

  @override
  String get t_err585 => 'Utilice las teclas de rotación para ajustar el ángulo del patrón, observando los puntos situados alrededor del patrón.';

  @override
  String get t_err586 => 'Ajuste el tamaño del patrón de modo que el punto inferior izquierdo sea la esquina interior del siguiente patrón.';

  @override
  String get t_err586_b => 'Ajuste el tamaño del patrón de modo que el punto inferior derecho sea la esquina interior del siguiente patrón.';

  @override
  String get t_err586_e => 'Utilice las teclas de giro y las teclas de tamaño para ajustar el ángulo y el tamaño del patrón, observando los bordes del patrón.';

  @override
  String get t_err587 => 'Utilice las teclas de rotación y las teclas de tamaño para alinear el punto final con el punto inicial del primer patrón.';

  @override
  String get t_err588 => 'Vuelva a colocar el material en el bastidor.';

  @override
  String get t_err588_e => 'Vuelva a colocar la tela en el lateral derecho, incluyendo el borde derecho del patrón en la parte izquierda del aro.';

  @override
  String get t_err588_e_2 => 'Vuelva a colocar la tela hacia la derecha, incluyendo el borde derecho del patrón en la derecha y la marca de hilo del patrón en la parte superior.';

  @override
  String get t_err590 => 'Pulse CARGAR para instalar el archivo de actualización.';

  @override
  String get t_err591 => 'Hay una nueva actualización disponible. Para instalar la actualización, desactive la máquina, mantenga pulsado el botón “Enhebrado automático” y vuelva a activar la máquina.';

  @override
  String get t_err_dl_updateprogram2 => 'El nuevo programa de actualización está listo. \nPara instalar la actualización, desactive la máquina, mantenga pulsado el botón “Enhebrado automático” y vuelva a activar la máquina.';

  @override
  String get t_err592 => 'Pulse CARGAR para guardar el archivo de actualización.';

  @override
  String get t_err593 => 'Seleccione el dispositivo donde se guardará el archivo de actualización.';

  @override
  String get t_err594 => 'Seleccione el dispositivo donde se ha guardado el archivo de actualización.';

  @override
  String get t_err_dl_updateprogram => 'Pulse la tecla Iniciar para descargar el programa de actualización.';

  @override
  String get t_err_dl_fail => 'Error en la descarga: el almacenamiento interno está lleno.';

  @override
  String get t_err_networkconnectionerr => 'Se ha perdido la conexión de red.\nCompruebe que la máquina esté conectada a una red inalámbrica.';

  @override
  String get t_err_not_turnoff => 'No apague la máquina.';

  @override
  String get t_err_pressresume_continuedl => 'Pulse la tecla Reanudar para continuar la descarga.';

  @override
  String get t_err_updateformovie => 'Actualice de nuevo para instalar los vídeos.';

  @override
  String get t_err595 => 'Introduzca el código de activación de 16 dígitos.';

  @override
  String get t_err596 => 'Introduzca el código de activación de 16 dígitos y pulse la tecla [Configurar].';

  @override
  String get t_err597 => 'El número de máquina y el código de activación se enviarán al servidor.';

  @override
  String get t_err598 => 'Se recomienda la \"Certificación de máquinas online\" para máquinas conectadas a la LAN inalámbrica.';

  @override
  String get t_err599 => 'El código de activación es incorrecto.\nCompruebe la clave y vuelva a escribirla.';

  @override
  String get t_err599_used => 'El código introducido ya se ha registrado con una máquina diferente.';

  @override
  String get t_err601 => '¿Activar la LAN inalámbrica?';

  @override
  String get t_err602 => 'Búsqueda SSID…';

  @override
  String get t_err603 => '¿Aplicar configuraciones?';

  @override
  String get t_err604 => 'Conectada a la LAN inalámbrica.';

  @override
  String get t_err605 => 'Conectando a la LAN inalámbrica.';

  @override
  String get t_err606 => 'No está conectado a la red.\nCompruebe la configuración de la LAN inalámbrica.';

  @override
  String get t_err607 => 'Fallo al conectar con el servidor.\nRevise configuración d red.';

  @override
  String get t_err608 => 'Falla la autenticación en la conexión con servidor.\nRevise config servidr proxy.';

  @override
  String get t_err609 => 'Se ha producido un error de red.';

  @override
  String get t_err611 => 'Desactivada';

  @override
  String get t_err612 => 'Se han producido errores en la función de red.';

  @override
  String get t_err613 => 'Los datos no se han podido importar.\nVuelva a empezar el proceso.';

  @override
  String get t_err614 => 'Existe información guardada sobre el Punto de acceso.\n¿Desea conectar con esta información?';

  @override
  String get t_err615 => 'Error de conexión 01';

  @override
  String get t_err616 => 'Clave de red incorrecta.';

  @override
  String get t_err617 => 'Clave de red incorrecta.';

  @override
  String get t_err618 => '¿Reconfigurar red?';

  @override
  String get t_err620 => 'Apague la máquina y vuelva a encenderla. ';

  @override
  String get t_err621 => 'Falla la autenticación en la conexión con servidor.\nRevise config servidr proxy.';

  @override
  String get t_err622 => 'Error de autenticación.\nConfirme nombre usuar. o contr.';

  @override
  String get t_err623 => 'Cancelando';

  @override
  String get t_err624 => 'Error comunicación';

  @override
  String get t_err625 => 'Completado';

  @override
  String get t_err626 => 'Confirm I/F';

  @override
  String get t_err627 => 'Err. Conexión';

  @override
  String get t_err628 => 'Fallo al conectar con el servidor.\nRevise configuración d red.';

  @override
  String get t_err629 => 'Fallo al conectar con el servidor.\nPruebe otra vez más tarde.';

  @override
  String get t_err630 => 'Descargando.\nPor favor espere.';

  @override
  String get t_err631 => 'Ha ocurrido un error al realizar la descarga.\nComience nuevamente.';

  @override
  String get t_err632 => 'No hay punto de acceso.';

  @override
  String get t_err633 => '!No hay datos!';

  @override
  String get t_err634 => 'Consulte el capítulo Solución de problemas de la Guía del usuario.';

  @override
  String get t_err636 => 'Imposible encontrar servidor, compruebe el nombre o dirección o escriba otro servidor LDAP.';

  @override
  String get t_err637 => 'Tiempo de espera del servidor agotado.\nPruebe otra vez más tarde.';

  @override
  String get t_err638 => 'Transfiriendo…';

  @override
  String get t_err697 => 'No puede realizarse la transferencia de datos a través del cable USB.';

  @override
  String get t_err84_mdc => '¿Desea recuperar y reanudar la memoria anterior?\n(Mi centro de diseño).';

  @override
  String get t_err84_iqd => '¿Desea recuperar y reanudar la memoria anterior?\n(IQ Designer).';

  @override
  String get t_err703_b => 'Instale \"My Stitch Monitor\" para monitorizar el bordado.';

  @override
  String get t_err703_t => 'Instale \"IQ Intuition Monitoring\" para monitorizar el bordado.';

  @override
  String get t_err704_b => 'Instale la aplicación \"My Stitch Monitor\" para controlar el bordado en su dispositivo inteligente. \n\nPuede controlar el estado del progreso de su bordado en su dispositivo inteligente. \nTambién puede comprobar toda la información del color del hilo utilizado para bordar.';

  @override
  String get t_err704_t => 'Instale la aplicación \"IQ Intuition Monitoring\" para controlar el bordado en su dispositivo inteligente. \n\nPuede controlar el estado del progreso de su bordado en su dispositivo inteligente. \nTambién puede comprobar toda la información del color del hilo utilizado para bordar.';

  @override
  String get t_err705_b => 'Instale la aplicación \"My Design Snap\" para enviar imágenes de su dispositivo inteligente a su máquina. \n\nEn Mi centro de diseño puede crear fácilmente patrones de bordado a partir de las imágenes.';

  @override
  String get t_err705_t => 'Instale la aplicación \"IQ Intuition Positioning\" para enviar imágenes de su dispositivo inteligente a su máquina. \n\nEn \"IQ Designer\" puede crear fácilmente patrones de bordado a partir de las imágenes.';

  @override
  String get t_err708 => 'Se requiere el KIT1 para utilizar esta aplicación.';

  @override
  String get t_err709 => 'Se requiere el KIT2 para utilizar esta aplicación.';

  @override
  String get t_err711 => 'Estos datos de bordado no incluyen suficiente información de hilo.\nPara mostrar la información correcta del hilo, introduzca el número de color del hilo en la pantalla de cambio de color del hilo.';

  @override
  String get t_err713 => 'Lea los acuerdos de licencia de usuario final (EULA) antes del uso.';

  @override
  String get t_err715 => 'Acepto los términos del acuerdo de licencia.';

  @override
  String get t_err01_heated => 'Se ha activado el dispositivo de seguridad porque se ha calentado el motor del eje principal. ¿Se ha enredado el hilo?';

  @override
  String get t_err01_motor => 'Se ha activado el dispositivo de seguridad porque se ha detenido el motor del eje principal. ¿Se ha enredado el hilo?';

  @override
  String get t_err01_npsensor => 'Se ha activado el dispositivo de seguridad porque el sensor de posición de la aguja no funciona correctamente.';

  @override
  String get t_err734 => 'Para iOS';

  @override
  String get t_err735 => 'Para Android™';

  @override
  String get t_err738 => 'Se cambiará el orden de bordado.';

  @override
  String get t_err739 => 'Esta función no puede utilizarse si se selecciona un patrón especial.';

  @override
  String get t_err740 => 'La puntada se solapará con las puntadas en el otro lado. Disminuya la anchura de la puntada o aumente la distancia de reproducción.';

  @override
  String get t_err741 => 'Los datos se han reducido a un tamaño compatible.';

  @override
  String get t_err742 => '¿Desea acceder a los ajustes para conectar su máquina a una red inalámbrica?';

  @override
  String get t_err743 => 'No puede llevar a cabo esta función porque los servicios de CanvasWorkspace están actualmente fuera de servicio por razones de mantenimiento. Espere hasta que se haya completado la restauración.';

  @override
  String get t_err743_s => 'No puede llevar a cabo esta función porque los servicios de Artspira están actualmente fuera de servicio por razones de mantenimiento. Espere hasta que se haya completado la restauración.';

  @override
  String get t_err744 => 'Su máquina no pudo registrarse en CanvasWorkspace.\nComience nuevamente.';

  @override
  String get t_err744_s => 'Su máquina no pudo registrarse en Artspira.\nComience nuevamente.';

  @override
  String get t_err745 => 'El código PIN es incorrecto. Introduzca de nuevo el código PIN.';

  @override
  String get t_err746 => 'Su máquina no está conectada a Internet.';

  @override
  String get t_err747 => 'No conectada al servidor de Internet.\nCompruebe los ajustes de proxy.';

  @override
  String get t_err748 => 'Tiempo de espera del servidor agotado.\nInténtelo de nuevo más tarde.';

  @override
  String get t_err749 => '¿Desea desconectarse de CanvasWorkspace?';

  @override
  String get t_err749_s => '¿Desea desconectarse de Artspira?';

  @override
  String get t_err750 => 'Su máquina no pudo desconectarse de CanvasWorkspace. Desconéctese manualmente desde la página web.';

  @override
  String get t_err750_s => 'Su máquina no pudo desconectarse de Artspira.\nDesconéctese manualmente desde la aplicación.';

  @override
  String get t_err751 => 'Su máquina de coser debe estar registrada en CanvasWorkspace para poder enviar datos (registro con PIN).\n¿Desea ir a la pantalla de ajustes?';

  @override
  String get t_err751_s => 'Su máquina de coser debe estar registrada en Artspira para poder enviar datos (registro con PIN).\n¿Desea ir a la pantalla de ajustes?';

  @override
  String get t_err752 => 'Es posible que su máquina no esté registrada. Compruébelo en CanvasWorkspace.';

  @override
  String get t_err752_s => 'Es posible que su máquina no esté registrada. Compruébelo en Artspira.';

  @override
  String get t_err753 => 'No pudieron cargarse los datos.\nComience nuevamente.';

  @override
  String get t_err754 => 'Los datos no se han podido descargar.\nComience nuevamente.';

  @override
  String get t_err755 => 'Certificación correcta.\nReinicie la máquina de coser.\n\nPara enviar datos a su máquina ScanNCut, reinicie su máquina y regístrela en el servidor CanvasWorkspace siguiendo lo indicado en la página 13 de los Ajustes.';

  @override
  String get t_err755_s => 'Certificación correcta.\nReinicie la máquina de coser.';

  @override
  String get t_err756 => '¿Desea reemplazar los datos existentes con datos nuevos?\n* Los datos en la carpeta de datos temporales se eliminarán automáticamente tras un periodo de tiempo determinado.';

  @override
  String get t_err757 => '¿Desea enviar los datos a la carpeta de datos temporales del servidor?\n* Los datos en la carpeta de datos temporales se eliminarán automáticamente tras un periodo de tiempo determinado.';

  @override
  String get t_err761 => 'No existen datos en el Pocket de datos temporal.';

  @override
  String get t_err762 => 'No se encontraron datos legibles en la carpeta de datos temporales.';

  @override
  String get t_err763 => 'Su ScanNCut y su máquina de coser deben estar registrados en CanvasWorkspace para recibir los datos de corte (registro con PIN).\n¿Desea ir a la pantalla de ajustes?';

  @override
  String get t_err763_s => 'Registre su máquina de coser para recibir datos usando Artspira. (Es necesario registrar el código PIN) \n¿Desea ir a la pantalla de ajustes para registrarse?';

  @override
  String get t_err764 => 'Instale \"Artspira\" en su dispositivo inteligente. \nCreando su cuenta Artspira ampliará enormemente las posibilidades de bordado. \nEscanee el código QR para obtener más detalles.';

  @override
  String get t_err765 => 'No se ha podido cambiar el nombre de la máquina.';

  @override
  String get t_err766 => 'Active la LAN inalámbrica para cambiar el nombre de la máquina.';

  @override
  String get t_err770 => 'No se ha podido borrar.';

  @override
  String get t_err771 => 'Si borra todos los patrones personalizados, los patrones que se están utilizando para la edición se sustituirán por otro patrón.\n¿Desea borrar todos los patrones personalizados?';

  @override
  String get t_err772 => 'Si borra o cambia el patrón personalizado importado después de guardarlo, es posible que los datos cambien respecto al original.';

  @override
  String get t_err773 => 'Los datos que incluyen patrones personalizados importados no pueden guardarse en una unidad de memoria externa.';

  @override
  String get t_err774 => 'Solo se guardarán los datos de puntadas del bordado. Los datos de edición, que incluyen patrones personalizados, no pueden guardarse en una unidad de memoria externa. Guárdelos en la unidad de memoria interna.';

  @override
  String get t_err775 => 'El almacenamiento de datos está lleno.\nElija un patrón personalizado para sustituirlo por el nuevo patrón.';

  @override
  String get t_err776 => 'Si sustituye el patrón personalizado, es posible que cambie mientras utiliza el patrón anterior. ¿Desea continuar?';

  @override
  String get t_err_taper01 => 'No se ha podido establecer la puntada de estrechamiento. Aumente la distancia o el ángulo.';

  @override
  String get t_err_taper02 => 'Termine de ajustar antes de coser.';

  @override
  String get t_err_taper03 => '¿Desea cancelar el ajuste de la puntada de estrechamiento?';

  @override
  String get t_err_taper04 => '¿Desea cancelar el estado actual de la puntada de estrechamiento?';

  @override
  String get t_err_taper05 => 'Esta función no se puede utilizar con la puntada de estrechamiento.';

  @override
  String get t_err_tapering07 => 'Pulse el botón de puntada en reversa para empezar a coser el extremo de estrechamiento.';

  @override
  String get t_err_tapering08 => 'El estrechamiento se detendrá después del número especificado de repeticiones.';

  @override
  String get t_err_tapering09 => 'El estrechamiento terminará en la posición del sticker para puntada de cierre.';

  @override
  String get t_err785 => 'Compruebe que haya suficiente hilo superior e hilo de la bobina para coser completamente el diseño, ya que es imposible obtener unos resultados satisfactorios si se acaba cualquiera de los dos hilos.';

  @override
  String get t_err790 => 'Todos los datos guardados, los ajustes y la información de la red se restablecerán a la configuración predeterminada. \n¿Desea continuar?';

  @override
  String get t_err791 => 'Borrando…\nNo apague la máquina.';

  @override
  String get t_err792 => 'Se ha completado el restablecimiento.\nApague la máquina.';

  @override
  String get t_err_paidcont_update => 'Para usar estos datos, debe actualizar el software de su máquina a la versión más reciente.';

  @override
  String get t_err_embcarriageevacuate => 'Toque OK para mover el carro de la unidad de bordado a la posición original.';

  @override
  String get t_err_sr_01 => 'Retire el módulo regulador de puntadas de la máquina.';

  @override
  String get t_err_sr_02 => 'Se ha cancelado el estado de la costura porque no se ha colocado ninguna tela durante varios segundos.';

  @override
  String get t_err_sr_03 => 'Puede comenzar a acolchar/hilvanar a manos libres con el regulador de puntadas.\n\nTenga cuidado de no tirar de la tela con fuerza, ya que podría romper la aguja.';

  @override
  String get t_err_sr_04 => 'Empiece a coser después de seleccionar el modo.';

  @override
  String get t_err_sr_05 => 'La aguja se mueve. Retire la mano de la aguja.';

  @override
  String get t_err_sr_06 => 'Se ha desconectado el módulo regulador de puntadas. Se cerrará la pantalla específica del regulador de puntadas.   \nPara volver a entrar en la pantalla, conéctelo.';

  @override
  String get t_err_sr_08 => 'Tenga cuidado de no romper la aguja debido a tirar de la tela con demasiada fuerza si ajusta la tensión del hilo a un valor más alto.';

  @override
  String get t_err_sr_09 => 'El modo de aguja gemela no se puede utilizar con esta función.\nDesconecte el conector del módulo regulador de puntadas y, a continuación, desactive el modo de aguja gemela para volver a intentarlo.';

  @override
  String get t_err_sr_10 => 'Cuando utilice puntadas de hilvanado, no utilice el prensatela de acolchado de punta abierta del regulador de puntadas. De lo contrario, la aguja podría romperse y causar lesiones.';

  @override
  String get t_err_manual_01_b => 'Si desea ver los manuales en su dispositivo móvil o PC, acceda a XXX (URL).';

  @override
  String get t_err_manual_02_t => 'Si desea ver el manual en su dispositivo móvil o PC, acceda a XXX (URL).';

  @override
  String get t_err_proj_emb_001 => 'La funcionalidad del proyector está limitada debido al pequeño tamaño del bastidor de bordado. No es posible utilizar la función “Edición del proyector con lápiz óptico”, pero se proyectan patrones de bordado.\n\n*El carro de la unidad de bordado se desplazará después de pulsar OK.';

  @override
  String get t_err_proj_emb_002 => 'La funcionalidad del proyector está limitada debido al pequeño tamaño del bastidor de bordado. No es posible utilizar la función “Edición del proyector con lápiz óptico”, pero se proyectan patrones de bordado.';

  @override
  String get t_err_proj_emb_003 => 'El proyector se apaga.';

  @override
  String get t_err_proj_emb_004 => 'El proyector se apaga porque se ha retirado el bastidor de bordado.\n\n* El carro de la unidad de bordado se desplazará después de pulsar OK.';

  @override
  String get t_err_proj_emb_005 => 'El proyector se apaga porque se ha retirado el bastidor de bordado.';

  @override
  String get t_err_proj_emb_006 => 'El proyector se apaga.\n\n* El carro de la unidad de bordado se desplazará después de pulsar OK.';

  @override
  String get t_err_proj_emb_007 => '¿Desea cancelar la actual selección de patrón?\n\n* El carro de la unidad de bordado se desplazará después de pulsar OK.';

  @override
  String get t_err_proj_emb_008 => 'Una vez convertido al patrón de bordado, se saldrá de Mi centro de diseño.\n¿Desea ir a la pantalla de edición del bordado?\n\n* El proyector se apaga y el carro de la unidad de bordado se desplazará después de pulsar OK.';

  @override
  String get t_err_proj_emb_009 => 'Una vez convertido al patrón de bordado, se saldrá de IQ Designer.\n¿Desea ir a la pantalla de edición del bordado?\n\n* El proyector se apaga y el carro de la unidad de bordado se desplazará después de pulsar OK.';

  @override
  String get t_err_proj_emb_010 => 'Procesando…';

  @override
  String get t_err_proj_emb_011 => 'Cerrando...';

  @override
  String get t_err_proj_emb_012 => 'El proyector se enciende.\n\n* El carro de la unidad de bordado se desplazará después de pulsar OK.';

  @override
  String get t_err_proj_emb_013 => 'El proyector se enciende.';

  @override
  String get t_err_proj_emb_014 => 'Esta función no se puede utilizar mientras el proyector esté en funcionamiento.';

  @override
  String get t_err_proj_smallframe => 'No disponible debido al pequeño tamaño del bastidor de bordado.';

  @override
  String get t_err_mdc_import_01 => 'Ajuste siempre los datos, ya que al cargarlos se modifica el tamaño.';

  @override
  String get t_err_voiceg_01 => 'Comprobando los datos de la guía de voz...';

  @override
  String get t_err_voiceg_02 => 'La guía de voz está lista y el ajuste activado.';

  @override
  String get t_err_photos_01 => 'Suelte la máscara de la imagen.';

  @override
  String get t_err_photos_02 => '¿Desea restablecer el ajuste del tamaño de la imagen?';

  @override
  String get t_err_photos_03 => '¿Desea cancelar la eliminación del fondo?';

  @override
  String get t_err_photos_04 => 'Convierta a bordado.';

  @override
  String get t_err_photos_05 => 'Una vez convertido al patrón de bordado, se saldrá de Función de bordado Picture Play.\n¿Desea ir a la pantalla de edición del bordado?';

  @override
  String get t_err_photos_06 => 'Por favor, espere.\nLa conexión LAN inalámbrica se desactiva temporalmente mientras la conversión está en curso.';

  @override
  String get t_err_photos_exit => '¿Desea salir de Función de bordado Picture Play?';

  @override
  String get t_err_font_old_new => '¿Desea convertir el archivo al formato de datos más reciente, ya que se utiliza el formato antiguo?';

  @override
  String get t_err_font_old_lomited => 'La función de edición está limitada debido al antiguo formato de datos.';

  @override
  String get t_err_firstset_wlan => 'Configure una LAN inalámbrica.\n¿Desea ir a la configuración para conectar su máquina a la red inalámbrica?';

  @override
  String get t_err_firstset_voiceguidance => 'Ajuste la función Guía de voz.\n¿Desea ir a la configuración de la Guía de voz?';

  @override
  String get t_err_wlan_function_01 => 'Para utilizar la función, debe activarse la configuración de la LAN inalámbrica en la máquina y esta debe estar conectada a una red inalámbrica.\n¿Desea ir a la configuración para conectar su máquina a una red inalámbrica?';

  @override
  String get t_err_teachingimage => 'Las imágenes solo tienen fines ilustrativos, alguna imagen puede variar según el modelo.';

  @override
  String get t_err_photo_disclaimers => 'Al utilizar esta función, usted acepta que no se utilizará ninguna parte del contenido:\n• para finalidades contrarias a cualquier ley y reglamento aplicable (incluidos, en particular, contenidos racistas, discriminatorios, que inciten al odio, pornográficos o de pornografía infantil y/o declaraciones que atenten contra el orden público o la decencia pública);\n• para violar el derecho a la intimidad o a la publicidad de cualquier persona;\n• para infringir cualquier derecho de autor, marca comercial u otro derecho de propiedad intelectual propiedad de un tercero;\n• para incluir cualquier URL o palabra clave que dirija a los espectadores a sitios maliciosos;\nEl usuario acepta y reconoce ser el único responsable del contenido utilizado.\nPara más información, consulte las Condiciones de uso.\n\nAl utilizar el contenido, certifico que he leído y entendido completamente las Condiciones del servicio y las Directrices.';

  @override
  String get t_err_framemovekey => '* Toque la tecla de cambio de posición del bastidor en la pantalla de bordado para mover el bastidor al centro.';

  @override
  String get speech_colorchangeinfo => 'Se han finalizado las puntadas.\nSeleccione el siguiente color del hilo.';

  @override
  String get t_err_updateinfo_01 => 'Actualización importante disponible.\nActualice su máquina descargando el archivo de actualización desde [Descargar el programa de actualización] en los \"Ajustes de la máquina\".';

  @override
  String get t_err_updateinfo_02 => 'Conecte su máquina a una red inalámbrica para recibir notificaciones acerca de las últimas versiones del software. \nTambién puede visitar el Brother support web (Página web de soporte de Brother) para obtener las últimas actualizaciones de software.';

  @override
  String get t_err_removecarriage => 'Desactive la máquina antes de colocar o retirar la unidad de bordado.';

  @override
  String get t_err_filter_removed => 'El filtro se ha borrado porque la categoría no es compatible.';

  @override
  String get t_err_filter_cleared => 'Debe borrarse el filtro porque la función de filtro no es aplicable en esta categoría.';

  @override
  String get t_principal07 => '[Palanca del pie prensatela]';

  @override
  String get t_principal07_01 => '\nLevante y baje la palanca del pie prensatela para subir y bajar respectivamente el pie prensatela.';

  @override
  String get t_principal07_02 => '(a) Pie prensatela\n(b) Palanca del pie prensatela';

  @override
  String get t_principal03 => '[Control de velocidad manual]';

  @override
  String get t_principal03_01 => '\nUse este control para ajustar la velocidad de cosido.\nDeslice el pasador hacia la izquierda para coser más lentamente.\nDeslícelo hacia la derecha para coser más rápidamente.';

  @override
  String get t_principal03_02 => '(a) Palanca\n(b) Lento\n(c) Rápido';

  @override
  String get t_principal12 => '[Polea]';

  @override
  String get t_principal12_01 => 'Gire la polea hacia usted para subir y bajar la aguja.\nLa polea debe girarse hacia la parte delantera de la máquina.';

  @override
  String get t_principal08 => '[Unidad de cama plana con compartimento para accesorios]';

  @override
  String get t_principal08_01 => 'Guarde el pie prensatela y las bobinas en el compartimento para accesorios de la unidad de cama plana.\nAl coser piezas cilíndricas, retire la unidad de cama plana.';

  @override
  String get t_principal10 => '[Elevador de rodilla]';

  @override
  String get t_principal10_00 => '(a) Elevador de rodilla';

  @override
  String get t_principal10_01 => '\nUtilice el elevador de rodilla para subir y bajar el pie prensatela con la rodilla, dejando ambas manos libres para manejar la tela.\n\n1. Haga coincidir las patillas del elevador de rodilla con las muescas de la conexión eléctrica y, a continuación, inserte el elevador de rodilla hasta el fondo.';

  @override
  String get t_principal10_03_00 => '(a) Pie prensatela\n';

  @override
  String get t_principal10_03 => '2. Para levantar el pie prensatela, mueva con la rodilla el elevador de rodilla hacia la derecha.\nSuelte el elevador de rodilla para bajar el pie prensatela.';

  @override
  String get t_principal11 => '[Pedal]';

  @override
  String get t_principal11_00 => '\nTambién puede utilizar el pedal para iniciar y detener la costura.\n\n1. Introduzca la clavija del pedal en la conexión eléctrica correspondiente de la máquina.';

  @override
  String get t_principal11_02 => '2. Pise lentamente el pedal para comenzar a coser.\nSuelte el pedal para detener la máquina.\n\n*La velocidad ajustada con el control de velocidad manual es la velocidad de cosido máxima del pedal.';

  @override
  String get t_xv_principal11_01 => 'Con el pedal multifunción pueden especificarse distintas operaciones de la máquina de coser, además de iniciar/detener la costura, como por ejemplo el corte del hilo y la costura en reversa.';

  @override
  String get t_xv_principal11_02 => '1. Alinee el lado ancho de la placa de montaje con la muesca de la parte inferior del pedal principal y, a continuación, únalos con un tornillo.';

  @override
  String get t_xv_principal11_03 => '2. Alinee el otro lado de la placa de montaje con la muesca de la parte inferior del pedal lateral y, a continuación, únalos con un tornillo.';

  @override
  String get t_xv_principal11_04 => '3. Inserte el conector del pedal lateral en el jack de la parte posterior del pedal principal.';

  @override
  String get t_xv_principal11_05 => '4. Inserte el conector redondo del pedal principal en el jack del pedal, en el lado derecho de la máquina.';

  @override
  String get t_xv_principal11_06 => '5. Pise lentamente el pedal para comenzar a coser. Suelte el pedal para detener la máquina.\n\n*La velocidad ajustada con el control de velocidad manual es la velocidad de cosido máxima del pedal.';

  @override
  String get t_principal11_01_02 => '(a) Pedal\n(b) Conexión eléctrica para el pedal';

  @override
  String get t_principal09 => '[Selector de posición de dientes de arrastre]';

  @override
  String get t_principal09_01 => '\nUtilice el selector de posición de dientes de arrastre para bajar los dientes de arrastre.';

  @override
  String get t_principal09_02 => '(a) Selector de posición de dientes de arrastre';

  @override
  String get t_principal_buttons_01 => '[Botón de \"Posición de aguja\"]';

  @override
  String get t_principal_buttons_01_01 => 'Utilice este botón cuando cambie la dirección de costura o para el cosido de detalles en pequeñas áreas.\nPulse este botón para subir o bajar la aguja.\nPulse este botón dos veces para coser puntadas únicas.';

  @override
  String get t_principal_buttons_02 => '[Botón de \"corte de hilo\"]';

  @override
  String get t_principal_buttons_02_01 => 'Pulse este botón después de coser para cortar automáticamente el hilo restante.';

  @override
  String get t_principal_buttons_06 => '[Botón del \"elevador del pie prensatela\"]';

  @override
  String get t_principal_buttons_06_01 => 'Pulse este botón para bajar el pie prensatela y aplicar presión a la tela.\nVuelva a pulsarlo para levantar el pie prensatela.\nPuede subir el pie prensatela al nivel más alto si mantiene pulsado el botón de pie prensatela.';

  @override
  String get t_principal_buttons_05 => '[Botón de \"enhebrado automático\"]';

  @override
  String get t_principal_buttons_05_01 => 'Utilice este botón para enhebrar la aguja automáticamente.';

  @override
  String get t_principal_buttons_04 => '[Botón de \"inicio/parar\"]';

  @override
  String get t_principal_buttons_04_01 => 'Si pulsa este botón, la máquina coserá algunas puntadas a baja velocidad y luego coserá a la velocidad configurada en el control de velocidad manual.\nPulse de nuevo este botón para detener la máquina.\nMantenga pulsado este botón para coser a la velocidad más baja.\nEl botón cambiará de color según el modo de funcionamiento de la máquina.\n\nVerde : la máquina está lista para coser o ya está cosiendo.\nRojo : la máquina no puede coser.';

  @override
  String get t_principal_buttons_03 => '[Botón de \"en reversa\"]';

  @override
  String get t_principal_buttons_03_01 => 'Utilice este botón para coser puntadas en reversa al principio y al final de la costura.\nPara patrones de costura recta y de zig-zag, la máquina coserá puntadas en reversa a baja velocidad, mientras se mantiene pulsado el botón de costura \"en reversa\". (Las puntadas se cosen en la dirección contraria).\nPara los demás patrones de costura, la máquina coserá puntadas de remate. Pulse este botón y la máquina coserá de 3 a 5 puntadas en el mismo punto y se detendrá automáticamente.';

  @override
  String get t_principal_buttons_07 => '[Botón del \"remate/amarre\"]';

  @override
  String get t_principal_buttons_07_01 => 'Utilice este botón para coser puntadas de remate al principio y al final de la costura.\nPara patrones de costuras de utilidad, pulse este botón y la máquina coserá de 3 a 5 puntadas en el mismo punto y se detendrá automáticamente.\nPara patrones de costura de caracteres/decorativa, la máquina coserá puntadas de remate después de coser una sola unidad del patrón.';

  @override
  String get t_basic13 => '[Hilo superior]';

  @override
  String get t_basic13_01_02 => '(a) Botón del \"elevador del pie prensatela\"\n';

  @override
  String get t_basic13_01 => 'Pulse la tecla de la película para ver el vídeo de las instrucciones mostradas.\n\n1. Pulse el botón del \"elevador del pie prensatela\" para subir el pie prensatela. ';

  @override
  String get t_basic13_02_00 => '(a) Botón de \"Posición de aguja\"';

  @override
  String get t_basic13_02 => '\n2. Pulse el botón de \"Posición de aguja\" para subir la aguja. ';

  @override
  String get t_basic13_03_02 => '(a) Portacarrete\n(b) Tapa del carrete\n(c) Carrete de hilo';

  @override
  String get t_basic13_03 => '\n3. Gire el portacarrete de manera que forme un ángulo hacia arriba.\nColoque el carrete de hilo en el portacarrete de manera que el hilo se desenrolle desde la parte delantera del carrete.\nEmpuje al máximo la tapa del carrete en el portacarrete y, a continuación, coloque de nuevo el portacarrete en su posición original. ';

  @override
  String get t_basic13_11_02 => '(a) Tapa de carrete (pequeña)  \n(b) Carrete (hilo cruzado)  \n(c) Espacio ';

  @override
  String get t_basic13_11 => '\nCuando cosa con hilo fino cruzado, utilice la tapa de carrete pequeña y deje un pequeño espacio entre la tapa y el carrete de hilo. ';

  @override
  String get t_basic13_04_02 => '(a) Placa de la guía del hilo ';

  @override
  String get t_basic13_04 => '\n4. Sujetando el hilo con las manos, pase el hilo por debajo y hacia arriba por la izquierda de la placa de la guía del hilo.';

  @override
  String get t_basic13_05 => '5. Sujetando el carrete de hilo con la mano derecha, siga la trayectoria del hilo con el extremo del hilo en la mano izquierda. Pase el hilo por debajo, hacia arriba y, de nuevo, por debajo a través de las ranuras.';

  @override
  String get t_basic13_06_02 => '(a) Guía del hilo de la varilla de la aguja';

  @override
  String get t_basic13_06 => '\n6. Pase el hilo por detrás de la guía del hilo de la varilla de la aguja. \n';

  @override
  String get t_basic13_07 => '7. Pulse el botón del \"elevador del pie prensatela\" para bajar el pie prensatela. ';

  @override
  String get t_basic13_08_02 => '(a) Discos de la guía del hilo';

  @override
  String get t_basic13_08 => '\n8. Pase el hilo por los discos de la guía del hilo. Asegúrese de que el hilo pasa por la ranura en la guía del hilo. El hilo debe entrar bien por los discos de la guía del hilo. Es posible que la aguja no esté enhebrada. \n';

  @override
  String get t_basic13_09_02 => '(b) Corte de hilo';

  @override
  String get t_basic13_09 => '\n9. Tire del hilo hacia arriba por debajo del cortador de hilo para cortarlo. ';

  @override
  String get t_basic13_10_02 => '(a) Botón de \"enhebrado automático\"';

  @override
  String get t_basic13_10 => '\n10. Pulse el botón de \"enhebrado automático\" para hacer que la máquina enhebre la aguja automáticamente. ';

  @override
  String get t_basic14 => '[Devanado de bobina]';

  @override
  String get t_basic14_01_02 => '(a) Ranura de la bobina\n(b) Resorte de la clavija';

  @override
  String get t_basic14_00 => '\nPulse la tecla de la película para ver el vídeo de las instrucciones mostradas.\n\n1. Haga coincidir la ranura de la bobina con el resorte de la clavija de la devanadora y coloque la bobina en la clavija. ';

  @override
  String get t_basic14_02 => '\n2. Coloque el portacarrete suplementario en la posición \"hacia arriba\". ';

  @override
  String get t_basic14_02_02 => '(a) Portacarrete suplementario ';

  @override
  String get t_basic14_03 => '\n3. Coloque el carrete de hilo en el portacarrete suplementario de manera que el hilo se desenrolle desde la parte delantera del carrete. A continuación, deslice la tapa del carrete en el portacarrete lo más lejos posible para sujetar el carrete. ';

  @override
  String get t_basic14_03_02 => '(a) Tapa del carrete\n(b) Portacarrete\n(c) Carrete de hilo ';

  @override
  String get t_basic14_04 => '\n4. Con la mano derecha, sujete el hilo cerca del carrete. Con la mano izquierda, sujete el extremo del hilo y, con ambas manos, pase el hilo alrededor de la guía del hilo. ';

  @override
  String get t_basic14_04_02 => '(a) Guía del hilo';

  @override
  String get t_basic14_05 => '\n5. Pase el hilo alrededor del disco de pretensión asegurándose de que el hilo queda por debajo del disco. ';

  @override
  String get t_basic14_05_02 => '(a)  Disco de pretensión';

  @override
  String get t_basic14_06 => '6. Enrolle el hilo hacia la derecha alrededor de la bobina 5 o 6 veces. ';

  @override
  String get t_basic14_07 => '(a) Ranura de la guía (con cortador incorporado)\n(b) Base de la devanadora\n\n7. Pase el extremo del hilo por la ranura de la guía en la base de la devanadora y tire del hilo hacia la derecha para cortarlo con el cortador. ';

  @override
  String get t_basic14_08_02 => '\n8. Empuje el selector de devanado de bobina hacia la izquierda, hasta que quede ajustado en su posición.  La ventana de devanado de bobina se mostrará en la pantalla LCD.';

  @override
  String get t_basic14_08_03 => '(a) Selector de devanado de bobina';

  @override
  String get t_basic14_08_04 => '\n* Utilice el control deslizante de devanado de la bobina para ajustar la cantidad de hilo devanado en la bobina a uno de cinco niveles.';

  @override
  String get t_basic14_08_05 => '(a) Control deslizante de devanado de la bobina\n(b) Más\n(c) Menos';

  @override
  String get t_basic14_09 => '9. Pulse la tecla de \"inicio/parar devanado de bobina\". La bobina comienza a enrollarse automáticamente.\n';

  @override
  String get t_basic14_10 => '10. Puede cambiar la velocidad de devanado pulsando - o + en la ventana de devanado de bobina. Pulse \"OK\" para minimizar esta ventana.';

  @override
  String get t_basic14_101 => '11. La bobina deja de girar cuando se termina el devanado. El selector de devanado de bobina volverá a su posición original.';

  @override
  String get t_basic14_102 => '12. Corte el hilo con el cortador y retire la bobina. ';

  @override
  String get t_basic14_11 => '\n*Uso del portacarrete \nSe puede utilizar el portacarrete principal para devanar la bobina antes de coser. \n\nNota: No utilice este portacarrete para devanar la bobina mientras la máquina esté cosiendo.\n\n1. Haga coincidir la ranura de la bobina con el resorte de la clavija de la devanadora y coloque la bobina en la clavija. ';

  @override
  String get t_basic14_11_02 => '(a) Ranura de la bobina\n(b) Resorte de la clavija ';

  @override
  String get t_basic14_12 => '\n2. Gire el portacarrete de manera que forme un ángulo hacia arriba.\nColoque el carrete de hilo en el portacarrete de manera que el hilo se desenrolle desde la parte delantera del carrete.\nEmpuje al máximo la tapa del carrete en el portacarrete y, a continuación, coloque el portacarrete en su posición original. ';

  @override
  String get t_basic14_12_02 => '(a) Portacarrete\n(b) Tapa del carrete\n(c) Carrete de hilo ';

  @override
  String get t_basic14_13 => '\n3. Sujetando el hilo con las manos, introdúzcalo por las aberturas de la placa de la guía del hilo.\nPase el hilo por la guía del hilo. ';

  @override
  String get t_basic14_13_02 => '(a) Placa de la guía del hilol\n(b) Guía del hilo';

  @override
  String get t_basic14_14 => '\n4. Pase el hilo alrededor del disco de pretensión asegurándose de que el hilo queda por debajo del disco. ';

  @override
  String get t_basic14_15_02 => '(a) Disco de pretensión';

  @override
  String get t_basic14_16 => '5. Enrolle el hilo hacia la derecha alrededor de la bobina 5 o 6 veces. ';

  @override
  String get t_basic14_17 => '(a) Ranura de la guía (con cortador incorporado)\n(b) Base de la devanadora\n\n6. Pase el extremo del hilo por la ranura de la guía en la base de la devanadora y tire del hilo hacia la derecha para cortarlo con el cortador. ';

  @override
  String get t_basic14_18 => '\n7. Empuje el selector de devanado de bobina hacia la izquierda, hasta que quede ajustado en su posición.  La ventana de devanado de bobina se mostrará en la pantalla LCD.';

  @override
  String get t_basic14_18_02 => '(a) Selector de devanado de bobina  ';

  @override
  String get t_basic14_20 => '8. Pulse la tecla de \"inicio/parar devanado de bobina\". La bobina comienza a enrollarse automáticamente.\n';

  @override
  String get t_basic14_201 => '9. Puede cambiar la velocidad de devanado pulsando - o + en la ventana de devanado de bobina. Pulse \"OK\" para minimizar esta ventana.';

  @override
  String get t_basic14_202 => '10. La bobina deja de girar cuando se termina el devanado. El selector de devanado de bobina volverá a su posición original.';

  @override
  String get t_basic14_203 => '11. Corte el hilo con el cortador y retire la bobina. ';

  @override
  String get t_basic14_21_02 => '\nCuando cosa con hilo fino cruzado, utilice la tapa de carrete pequeña y deje un pequeño espacio entre la tapa y el carrete de hilo. ';

  @override
  String get t_basic14_21_03 => '(a) Tapa de carrete (pequeña)\n(b) Carrete (hilo cruzado) \n(c) Espacio ';

  @override
  String get t_basic15 => '[Cambio de aguja]';

  @override
  String get t_basic15_00 => '\nPara revisar la aguja correctamente:\nPara revisar la aguja, coloque la parte plana de la aguja sobre una superficie plana.\nCompruebe la aguja por la parte superior y los laterales.\nTire las agujas que estén dobladas.';

  @override
  String get t_basic15_00_01 => '(a) Espacio paralelo\n(b) Superficie nivelada (tapa de bobina, cristal, etc.) ';

  @override
  String get t_basic15_01 => '\n1. Pulse el botón de \"Posición de aguja\" para subir la aguja.';

  @override
  String get t_basic15_02 => '2. Pulse la tecla de cambio del pie prensatela/aguja.';

  @override
  String get t_basic15_03 => '3. Utilice el destornillador para girar el tornillo hacia la parte delantera de la máquina de coser, aflójelo, y extraiga la aguja.';

  @override
  String get t_basic15_04 => '\n4. Con la parte plana de la aguja mirando hacia la parte trasera de la máquina, inserte la nueva aguja hasta el final de la cavidad (mirilla) en la presilla de la aguja.\nUtilice el destornillador para apretar bien el tornillo de la presilla de la aguja.';

  @override
  String get t_basic15_04_02 => '(a) Final de la cavidad\n(b) Orificio para la aguja\n(c) Parte plana de la aguja';

  @override
  String get t_basic15_05 => '5. Pulse la tecla de cambio del pie prensatela/aguja para desbloquear todas las teclas y botones.';

  @override
  String get t_basic16 => '[Cambio del pie prensatela]';

  @override
  String get t_basic16_01 => '*Extracción del pie prensatela\n\n1. Pulse el botón de \"Posición de aguja\" para subir la aguja.';

  @override
  String get t_basic16_02 => '2. Pulse la tecla de cambio del pie prensatela/aguja.';

  @override
  String get t_basic16_03 => '\n3. Suba la palanca del pie prensatela.';

  @override
  String get t_basic16_03_02 => '(a) Pie prensatela\n(b) Palanca del pie prensatela';

  @override
  String get t_basic16_04 => '\n4. Pulse el botón negro de la parte posterior del soporte del pie prensatela y extraiga el pie prensatela.';

  @override
  String get t_basic16_04_02 => '(a) Botón negro\n(b) Soporte del pie prensatela';

  @override
  String get t_basic16_05 => '\n*Colocación del pie prensatela\n\n1. Coloque el nuevo pie prensatela debajo del soporte, haciendo coincidir la patilla del pie con la muesca del soporte.\nBaje el pie prensatela de manera que la patilla del pie se ajuste en la muesca en el soporte.';

  @override
  String get t_basic16_05_02 => '(a) Muesca\n(b) Patilla';

  @override
  String get t_basic16_06 => '2. Pulse la tecla de cambio del pie prensatela/aguja para desbloquear todas las teclas y botones.';

  @override
  String get t_basic16_07 => '(a) Pie prensatela\n(b) Palanca del pie prensatela\n\n3. Suba la palanca del pie prensatela.';

  @override
  String get t_basic17 => '[Colocación de la bobina]';

  @override
  String get t_basic17_01 => 'Pulse la tecla de la película para ver el vídeo de las instrucciones mostradas.\n\n1. Pulse el botón del \"elevador del pie prensatela\" para subir el pie prensatela.';

  @override
  String get t_basic17_02 => '(a) Tapa de bobina\n(b) Llave\n\n2. Deslice la llave de la tapa de la bobina hacia la derecha. Se abrirá la tapa de la bobina.\nRetire la tapa de la bobina.';

  @override
  String get t_basic17_03 => '3. Sujete la bobina con la mano derecha y sostenga el extremo del hilo con la izquierda.';

  @override
  String get t_basic17_04 => '4. Coloque la bobina en la lanzadera de manera que el hilo se desenrolle hacia la izquierda.\nSujete la bobina con la mano derecha presionando ligeramente hacia abajo, y a continuación guíe el hilo con la mano izquierda tal y como se indica en el dibujo.';

  @override
  String get t_basic17_05 => '\n5. Pase el hilo por la guía y, a continuación, tire del hilo hacia la parte delantera.\nEl cortador cortará el hilo.';

  @override
  String get t_basic17_05_02 => '(a) Cortador';

  @override
  String get t_basic17_06 => '6. Inserte la patilla en la esquina inferior izquierda de la tapa de la bobina y, a continuación, pulse ligeramente sobre el lado derecho para cerrar la tapa.';

  @override
  String get t_embbasic17 => '[Colocación de estabilizadores (refuerzo) para planchar en la tela]';

  @override
  String get t_embbasic17_00 => '\nPara obtener los mejores resultados en las labores de bordado, use siempre un material estabilizador.\nSiga las instrucciones del paquete para el estabilizador que se va a utilizar.';

  @override
  String get t_embbasic17_01 => '\n1. Utilice un trozo de material estabilizador que sea más grande que el bastidor que se vaya a utilizar.';

  @override
  String get t_embbasic17_01_02 => '(a) Tamaño del bastidor\n(b) Estabilizador (refuerzo) para planchar';

  @override
  String get t_embbasic17_02 => '\n2. Planche el material estabilizador en el revés de la tela.\n\n*Cuando trabaje con telas que no se pueden planchar (como toallas o telas con lazos que se agrandan con la plancha) o en lugares donde es difícil planchar, coloque el material estabilizador debajo de la tela sin fijarlo; a continuación, coloque la tela y el estabilizador en el bastidor o pregunte a un vendedor autorizado cuál es el estabilizador adecuado que hay que utilizar.';

  @override
  String get t_embbasic17_02_02 => '(a) Lado fusible del estabilizador\n(b) Tela (revés) ';

  @override
  String get t_embbasic17_03 => '\n*Para obtener los mejores resultados cuando borde sobre telas finas, como organdí o lino, o sobre telas con pelillo, como toallas o pana, use material estabilizador soluble en agua (se vende por separado) .\nEste tipo de material estabilizador se disolverá completamente en agua, dando a la labor un acabado más bonito.';

  @override
  String get t_embbasic18 => '[Inserción de la tela]';

  @override
  String get t_embbasic18_01 => '1. Levante y afloje el tornillo de ajuste del cuadro del bastidor y retire el bastidor.';

  @override
  String get t_embbasic18_02 => '\n2. Coloque la tela con el lado derecho hacia arriba encima del cuadro del bastidor.\nVuelva a insertar el bastidor asegurándose de hacer coincidir el Δ del bastidor con el Δ del cuadro.';

  @override
  String get t_embbasic18_02_02 => '(a) Bastidor\n(b) Cuadro del bastidor\n(c) Tornillo de ajuste';

  @override
  String get t_embbasic18_03 => '3. Apriete ligeramente el tornillo de ajuste del bastidor y quite los hilos sueltos de la tela tirando de los bordes y esquinas.\nNo afloje el tornillo.';

  @override
  String get t_embbasic18_04 => '\n4. Estire suavemente la tela para tensarla y apriete el tornillo de ajuste del bastidor para evitar que la tela se afloje después de estirarla.\n\n* Después de estirar la tela, asegúrese de que queda tensa.\n\n* Asegúrese de que el bastidor está al mismo nivel que el cuadro del bastidor antes de comenzar a bordar.\n\n*Nota\nEstire la tela por las cuatro puntas y los cuatro costados. Mientras estira la tela, apriete el tornillo de ajuste del bastidor.';

  @override
  String get t_embbasic18_04_02 => '(a) Cuadro del bastidor\n(b) Bastidor\n(c) Tela';

  @override
  String get t_embbasic18_04_11 => '*Si se utiliza un bastidor de bordado con una palanca adjunta.\n';

  @override
  String get t_embbasic18_04_12 => '\n1. Baje la palanca.';

  @override
  String get t_embbasic18_04_13 => '2. Afloje manualmente el tornillo de ajuste del bastidor y retire el bastidor interno.';

  @override
  String get t_embbasic18_04_14 => '\n3. Coloque la tela en su posición.';

  @override
  String get t_embbasic18_04_15 => '4．Apriete manualmente el tornillo de ajuste del bastidor.';

  @override
  String get t_embbasic18_04_16 => '5. Vuelva a colocar la palanca en su posición original.';

  @override
  String get t_embbasic18_05 => '\n[Uso de la hoja de bordado]';

  @override
  String get t_embbasic18_05_01 => 'Si desea bordar una costura en un determinado lugar, utilice la hoja de bordado con el bastidor.\n\n1. Con una tiza, marque el área de la tela que desea bordar.';

  @override
  String get t_embbasic18_05_02 => '(a) Patrón de bordado\n(b) Marca';

  @override
  String get t_embbasic18_06 => '\n2. Coloque la hoja de bordado en el bastidor. Haga coincidir las líneas de guía de la hoja de bordado con la marca que ha hecho sobre la tela.';

  @override
  String get t_embbasic18_06_02 => '(a) Bastidor\n(b) Línea de guía';

  @override
  String get t_embbasic18_07 => '\n3. Estire suavemente la tela para que no queden dobleces ni arrugas e inserte el bastidor en el cuadro.';

  @override
  String get t_embbasic18_07_02 => '(a) Bastidor\n(b) Cuadro del bastidor';

  @override
  String get t_embbasic18_08 => '4. Retire la hoja de bordado.';

  @override
  String get t_embbasic19 => '[Colocación del bastidor]';

  @override
  String get t_embbasic19_01 => '*Enrolle e inserte la bobina antes de montar el bastidor.\n\n1. Pulse el botón del \"elevador del pie prensatela\" para subir el pie prensatela.';

  @override
  String get t_embbasic19_02 => '\n2. Suba la palanca de fijación del bastidor.';

  @override
  String get t_embbasic19_03 => '\n3. Haga coincidir la guía del bastidor con el lado derecho del soporte del bastidor.';

  @override
  String get t_embbasic19_03_02 => '(a) Soporte del bastidor\n(b) Guía del bastidor';

  @override
  String get t_embbasic19_04 => '4. Deslice el bastidor en el soporte, asegurándose de alinear el Δ del bastidor con el Δ del soporte.';

  @override
  String get t_embbasic19_05 => '\n5. Baje la palanca de fijación del bastidor para sujetar el bastidor en el soporte.\n\n*Si no se baja la palanca de fijación del bastidor, puede que el patrón de bordado no se cosa correctamente o que el pie prensatela golpee el bastidor y lo dañe.';

  @override
  String get t_embbasic19_05_02 => '(a) Palanca de fijación del bastidor';

  @override
  String get t_embbasic19_06 => '\n[Extracción del bastidor]\n\n1. Suba la palanca de fijación del bastidor.';

  @override
  String get t_embbasic19_07 => '2. Tire del bastidor hacia usted.';

  @override
  String get t_embbasic20 => '[Colocación de la unidad de bordado]';

  @override
  String get t_embbasic20_01 => 'Antes de apagar la máquina, lea los pasos que se explican a continuación.\n\n1. Desconecte la alimentación y retire la unidad de cama plana (si su máquina la utiliza).';

  @override
  String get t_embbasic20_03 => '\n2. Inserte la conexión de la unidad de bordado en el puerto de conexión de la máquina. Empuje ligeramente hasta que la unidad quede ajustada en su posición.';

  @override
  String get t_embbasic20_03_02 => '(a) Conexión de la unidad de bordado\n(b) Puerto de conexión de la máquina';

  @override
  String get t_embbasic20_04 => '(a) APAGADO\n(b) ENCENDIDO\n\n3. Encienda la máquina de coser (interruptor en ON) .';

  @override
  String get t_embbasic20_05 => '4. Pulse la tecla Aceptar. El cartucho se desplazará hacia la posición de inicio.';

  @override
  String get t_embbasic20_06 => '[Extracción de la unidad de bordado]';

  @override
  String get t_embbasic20_06_02 => '\n(a) APAGADO\n(b) ENCENDIDO\n\nAntes de apagar la máquina, lea los pasos que se explican a continuación.\n\n1. Apague la máquina de coser (interruptor en OFF) .';

  @override
  String get t_embbasic20_07 => '(a) Botón de liberación (situado debajo de la unidad de bordado)\n\n2. Mantenga pulsado el botón de liberación a la vez que tira de la unidad de bordado.';

  @override
  String get t_xp_embbasic21 => '[Colocación del pie de bordado \"W\"]';

  @override
  String get t_xp_embbasic21_01 => '1. Pulse el botón de \"Posición de aguja\" para subir la aguja.';

  @override
  String get t_xp_embbasic21_02 => '2. Pulse la tecla de cambio del pie prensatela/aguja.';

  @override
  String get t_xp_embbasic21_03 => '\n3. Suba la palanca del pie prensatela.';

  @override
  String get t_xp_embbasic21_04 => '\n4. Pulse el botón negro del soporte del pie prensatela y extraiga el pie prensatela.';

  @override
  String get t_xp_embbasic21_04_02 => '(a) Botón negro\n(b) Soporte del pie prensatela';

  @override
  String get t_xp_embbasic21_05 => '\n5. Con el destornillador suministrado, afloje el tornillo del soporte del pie prensatela para extraer el soporte.';

  @override
  String get t_xp_embbasic21_05_02 => '(a) Destornillador\n(b) Soporte del pie prensatela\n(c) Tornillo del soporte del pie prensatela';

  @override
  String get t_xp_embbasic21_06 => '(a) Palanca del pie prensatela\n\n6. Baje la palanca del pie prensatela.';

  @override
  String get t_xp_embbasic21_07_01 => '(a) Barra del pie prensatela\n';

  @override
  String get t_xp_embbasic21_07_02 => '7. Coloque el pie de bordado \"W\" en la barra del pie prensatela desde detrás.';

  @override
  String get t_xp_embbasic21_08_01 => '(a) Tornillo del soporte del pie prensatela\n';

  @override
  String get t_xp_embbasic21_08_02 => '8. Coloque en su sitio el pie de bordado con la mano derecha y, a continuación, apriete bien el tornillo del soporte del pie prensatela utilizando el destornillador suministrado.';

  @override
  String get t_xp_embbasic21_09 => '9. Pulse la tecla de cambio del pie prensatela/aguja para desbloquear todas las teclas y botones.';

  @override
  String get t_embbasic21 => '[Colocación del pie de bordado \"W\"]';

  @override
  String get t_embbasic21_01 => '1. Pulse el botón de \"Posición de aguja\" para subir la aguja.';

  @override
  String get t_embbasic21_02 => '2. Pulse la tecla de cambio del pie prensatela/aguja.';

  @override
  String get t_embbasic21_03 => '\n3. Suba la palanca del pie prensatela.';

  @override
  String get t_embbasic21_04 => '\n4. Pulse el botón negro del soporte del pie prensatela y extraiga el pie prensatela.';

  @override
  String get t_embbasic21_04_02 => '(a) Botón negro\n(b) Soporte del pie prensatela';

  @override
  String get t_embbasic21_05 => '\n5. Con el destornillador suministrado, afloje el tornillo del soporte del pie prensatela para extraer el soporte.';

  @override
  String get t_embbasic21_05_02 => '(a) Destornillador\n(b) Soporte del pie prensatela\n(c) Tornillo del soporte del pie prensatela';

  @override
  String get t_embbasic21_06 => '(a) Palanca del pie prensatela\n\n6. Baje la palanca del pie prensatela.';

  @override
  String get t_embbasic21_07 => '(a) Brazo\n(b) Tornillo de la presilla de la aguja\n(c) Tornillo del soporte del pie prensatela\n(d) Escobilla\n7. Coloque el pie para bordado \"W\" en la barra del pie prensatela de forma que el brazo del pie para bordado \"W\" esté en contacto con la parte posterior del soporte de la aguja.';

  @override
  String get t_embbasic21_08 => '8. Con el destornillador suministrado, apriete bien el tornillo del soporte del pie prensatela.';

  @override
  String get t_embbasic21_09 => '9. Pulse la tecla de cambio del pie prensatela/aguja para desbloquear todas las teclas y botones.';

  @override
  String get t_embbasic21_emb_07 => '(a) Brazo\n(b) Tornillo de la presilla de la aguja\n(c) Tornillo del soporte del pie prensatela\n(d) Escobilla\n3. Coloque el pie para bordado \"W\" en la barra del pie prensatela de forma que el brazo del pie para bordado \"W\" esté en contacto con la parte posterior del soporte de la aguja.';

  @override
  String get t_embbasic21_emb_08 => '4. Con el destornillador suministrado, apriete bien el tornillo del soporte del pie prensatela.';

  @override
  String get t_embbasic21_emb_09 => '5. Pulse la tecla de cambio del pie prensatela/aguja para desbloquear todas las teclas y botones.';

  @override
  String get t_xv_embbasic21 => '[Colocar el pie de bordado \"W+\"]';

  @override
  String get t_xv_embbasic21_05 => '\n5. Con el destornillador suministrado, retire el tornillo del soporte del pie prensatela para extraer el soporte.';

  @override
  String get t_xv_embbasic21_07_01 => '(a) Barra del pie prensatela\n';

  @override
  String get t_xv_embbasic21_07_02 => '7. Coloque el pie de bordado \"W+\" en la barra del pie prensatela desde detrás.';

  @override
  String get t_xv_embbasic21_08_01 => '(a) Tornillo del soporte del pie prensatela\n';

  @override
  String get t_xv_embbasic21_08_02 => '8. Coloque en su sitio el pie de bordado con la mano derecha y, a continuación, apriete bien el tornillo del soporte del pie prensatela utilizando el destornillador suministrado.';

  @override
  String get t_xv_embbasic21_09 => '9. Conecte el conector del pie de bordado \"W+\" con puntero LED al jack de la parte izquierda de la máquina.';

  @override
  String get t_xv_embbasic21_10 => '10. Pulse la tecla de cambio del pie prensatela/aguja para desbloquear todas las teclas y botones.';

  @override
  String get t_embbasic22 => '[Estabilizador adecuado para utilizar]';

  @override
  String get t_embbasic22_00_01 => '1. Telas que pueden plancharse';

  @override
  String get t_embbasic22_00_02 => '2. Telas que no pueden plancharse';

  @override
  String get t_embbasic22_00_03 => '3. Telas finas';

  @override
  String get t_embbasic22_00_04 => '4. Telas con pelillo';

  @override
  String get t_embbasic22_00_05 => '\nPara obtener los mejores resultados en las labores de bordado, use siempre un material estabilizador. Siga las instrucciones del paquete para el estabilizador que se va a utilizar.';

  @override
  String get t_embbasic22_01 => '\n[1. Telas que pueden plancharse]';

  @override
  String get t_embbasic22_01_02 => '\nPlanche el material estabilizador en el revés de la tela. Utilice un trozo de material estabilizador que sea más grande que el bastidor que se vaya a utilizar.';

  @override
  String get t_embbasic22_01_03 => '(a) Tamaño del bastidor\n(b) Estabilizador (refuerzo) para planchar';

  @override
  String get t_embbasic22_02 => '[2. Telas que no pueden plancharse]';

  @override
  String get t_embbasic22_02_02 => '\nColoque el material estabilizador debajo de la tela sin fijarlo y, a continuación, coloque la tela y el estabilizador en el bastidor.';

  @override
  String get t_embbasic22_03 => '[3. Telas finas]';

  @override
  String get t_embbasic22_03_02 => '\nUtilice material estabilizador soluble en agua (se vende por separado) para obtener los mejores resultados. Este tipo de material estabilizador se disolverá completamente en agua, dando a la labor un acabado más bonito.';

  @override
  String get t_embbasic22_04 => '[4. Telas con pelillo]';

  @override
  String get t_embbasic22_04_02 => '\nCuando utilice telas que no se puedan planchar (como toallas o telas con lazos que se agrandan con la plancha) , coloque el material estabilizador debajo de la tela sin fijarlo y, a continuación, coloque la tela y el estabilizador en el bastidor o utilice material estabilizador soluble en agua (se vende por separado) .';

  @override
  String get t_embbasic23 => '[Ajustes de tensión del hilo]';

  @override
  String get t_embbasic23_01 => 'Para bordar, la tensión del hilo debe ajustarse de manera que pueda verse un poco el hilo superior en el revés de la tela.';

  @override
  String get t_embbasic23_01_01 => '1. Tensión adecuada del hilo';

  @override
  String get t_embbasic23_01_02 => '2. El hilo superior está demasiado tenso';

  @override
  String get t_embbasic23_01_03 => '3. El hilo superior está demasiado flojo';

  @override
  String get t_embbasic23_02 => '[1. Tensión adecuada del hilo]';

  @override
  String get t_embbasic23_02_02 => '\nPuede verse la costura desde el revés de la tela. Si la tensión del hilo no es correcta, la costura no tendrá un acabado adecuado. La tela puede fruncirse o el hilo romperse.';

  @override
  String get t_embbasic23_03 => '[2. El hilo superior está demasiado tenso]';

  @override
  String get t_embbasic23_03_02 => '\nHay demasiada tensión en el hilo superior por lo que el hilo de la bobina es visible desde el lado derecho de la tela.';

  @override
  String get t_embbasic23_03_03 => 'Pulse - para aflojar la tensión del hilo superior.';

  @override
  String get t_embbasic23_04 => '[3. El hilo superior está demasiado flojo]';

  @override
  String get t_embbasic23_04_02 => '\nNo hay suficiente tensión en el hilo superior por lo que el hilo superior está flojo y aparecen remates flojos u ondas en el lado derecho de la tela.';

  @override
  String get t_embbasic23_04_03 => 'Pulse + para aumentar la tensión del hilo.';

  @override
  String get t_trouble22 => '[El hilo superior se rompe]';

  @override
  String get t_trouble22_01 => '*Causa 1\nLa máquina no está enhebrada correctamente (la tapa del carrete utilizada es inadecuada, la tapa del carrete está suelta, el hilo no pasó por la varilla de aguja enhebrada, etc.) .\n\n*Solución\nVuelva a enhebrar la máquina correctamente.';

  @override
  String get t_trouble22_02 => '*Causa 2\nEl hilo está anudado o enredado.\n\n*Solución\nVuelva a enhebrar los hilos superior e inferior.';

  @override
  String get t_trouble22_03 => '*Causa 3\nEl hilo es demasiado grueso para la aguja.\n\n*Solución\nCompruebe las combinaciones de hilo y aguja.';

  @override
  String get t_trouble22_04 => '*Causa 4\nEl hilo superior está demasiado tenso.\n\n*Solución\nAjuste la tensión del hilo.';

  @override
  String get t_trouble22_05 => '*Causa 5\nEl hilo está enrollado.\n\n*Solución\nUse tijeras, etc. para cortar el hilo enrollado y sáquelo de la guía, etc.';

  @override
  String get t_trouble22_06 => '*Causa 6\nLa aguja está girada, doblada o la punta está desafilada.\n\n*Solución\nReemplace la aguja.';

  @override
  String get t_trouble22_07 => '*Causa 7\nLa aguja no está colocada correctamente.\n\n*Solución\nColoque la aguja correctamente.';

  @override
  String get t_trouble22_08 => '*Causa 8\nHay hendiduras junto al orificio de la placa de la aguja.\n\n*Solución\nReemplace la placa de la aguja o consulte con el vendedor más cercano.';

  @override
  String get t_trouble22_09 => '*Causa 9\nHay hendiduras junto al orificio del pie prensatela.\n\n*Solución\nReemplace el pie prensatela o consulte con el vendedor más cercano.';

  @override
  String get t_trouble22_10 => '*Causa 10\nHay hendiduras en la guía.\n\n*Solución\nReemplace la guía o consulte con el vendedor más cercano.';

  @override
  String get t_trouble23 => '[El hilo de la bobina se rompe]';

  @override
  String get t_trouble23_01 => '*Causa 1\nLa bobina está colocada incorrectamente.\n\n*Solución\nVuelva a ajustar el hilo de la bobina correctamente.';

  @override
  String get t_trouble23_02 => '*Causa 2\nHay hendiduras en la bobina o ésta no gira correctamente.\n\n*Solución\nReemplace la bobina.';

  @override
  String get t_trouble23_03 => '*Causa 3\nEl hilo está enrollado.\n\n*Solución\nUse tijeras, etc. para cortar el hilo enrollado y sáquelo de la guía, etc.';

  @override
  String get t_trouble24 => '[Puntadas sueltas]';

  @override
  String get t_trouble24_01 => '*Causa 1\nLa máquina no enhebra correctamente.\n\n*Solución\nCompruebe los pasos para enhebrar la máquina y vuelva a enhebrarla correctamente.';

  @override
  String get t_trouble24_02 => '*Causa 2\nAguja o hilo inadecuados para la tela escogida.\n\n*Solución\nCompruebe el diagrama de \"Combinaciones de tela/hilo/aguja\".';

  @override
  String get t_trouble24_03 => '*Causa 3\nLa aguja está girada, doblada o la punta está desafilada.\n\n*Solución\nReemplace la aguja.';

  @override
  String get t_trouble24_04 => '*Causa 4\nLa aguja no está colocada correctamente.\n\n*Solución\nColoque la aguja correctamente.';

  @override
  String get t_trouble24_05 => '*Causa 5\nLa aguja es defectuosa.\n\n*Solución\nReemplace la aguja.';

  @override
  String get t_trouble24_06 => '*Causa 6\nHay polvo o pelusa debajo de la placa de la aguja.\n\n*Solución\nElimine el polvo o la pelusa con el cepillo.';

  @override
  String get t_trouble25 => '[Frunces en la tela]';

  @override
  String get t_trouble25_01 => '*Causa 1\nFallo al enhebrar el hilo o la bobina superior.\n\n*Solución\nCompruebe los pasos para enhebrar la máquina y vuelva a enhebrarla correctamente.';

  @override
  String get t_trouble25_02 => '*Causa 2\nLa tapa del carrete está colocada incorrectamente.\n\n*Solución\nCompruebe el método para colocar la tapa del carrete y colóquela de nuevo.';

  @override
  String get t_trouble25_03 => '*Causa 3\nAguja o hilo inadecuados para la tela escogida.\n\n*Solución\nCompruebe el diagrama de \"Combinaciones de tela/hilo/aguja\".';

  @override
  String get t_trouble25_04 => '*Causa 4\nLa aguja está girada, doblada o la punta está desafilada.\n\n*Solución\nReemplace la aguja.';

  @override
  String get t_trouble25_05 => '*Causa 5\nPuntadas demasiado largas al coser en telas finas.\n\n*Solución\nAcorte la longitud de costura.';

  @override
  String get t_trouble25_06 => '*Causa 6\nTensión del hilo incorrecta.\n\n*Solución\nAjuste la tensión del hilo.';

  @override
  String get t_trouble25_07 => '*Causa 7\nPie prensatela inadecuado.\n\n*Solución\nUse el pie prensatela adecuado.';

  @override
  String get t_trouble26 => '[La máquina hace mucho ruido]';

  @override
  String get t_trouble26_01 => '*Causa 1\nHay polvo o pelusa entre los dientes de arrastre.\n\n*Solución\nElimine el polvo o la pelusa.';

  @override
  String get t_trouble26_02 => '*Causa 2\nHay trozos de hilo en la guía.\n\n*Solución\nLimpie la guía.';

  @override
  String get t_trouble26_03 => '*Causa 3\nEl hilo superior está enhebrado incorrectamente.\n\n*Solución\nCompruebe los pasos para enhebrar la máquina y vuelva a enhebrarla.';

  @override
  String get t_trouble26_04 => '*Causa 4\nHay hendiduras en la guía.\n\n*Solución\nReemplace la guía o consulte con el vendedor más cercano.';

  @override
  String get t_trouble27 => '[No se puede utilizar el enhebrador]';

  @override
  String get t_trouble27_01 => '*Causa 1\nLa aguja no está en la posición correcta.\n\n*Solución\nPulse el botón de \"Posición de aguja\" para subir la aguja.';

  @override
  String get t_trouble27_02 => '*Causa 2\nEl ganchillo no pasa por el ojo de la aguja.\n\n*Solución\nPulse el botón de \"Posición de aguja\" para subir la aguja.';

  @override
  String get t_trouble27_03 => '*Causa 3\nLa aguja no está colocada correctamente.\n\n*Solución\nColoque la aguja correctamente.';

  @override
  String get t_trouble28 => '[Tensión del hilo incorrecta]';

  @override
  String get t_trouble28_01 => '*Causa 1\nEl hilo superior está enhebrado incorrectamente.\n\n*Solución\nCompruebe los pasos para enhebrar la máquina y vuelva a enhebrarla.';

  @override
  String get t_trouble28_02 => '*Causa 2\nLa bobina está colocada incorrectamente.\n\n*Solución\nReajuste la bobina.';

  @override
  String get t_trouble28_03 => '*Causa 3\nAguja o hilo inadecuados para la tela escogida.\n\n*Solución\nCompruebe el diagrama de \"Combinaciones de tela/hilo/aguja\".';

  @override
  String get t_trouble28_04 => '*Causa 4\nSoporte del pie prensatela colocado incorrectamente.\n\n*Solución\nColoque correctamente el soporte del pie prensatela.';

  @override
  String get t_trouble28_05 => '*Causa 5\nTensión del hilo incorrecta.\n\n*Solución\nAjuste la tensión del hilo.';

  @override
  String get t_trouble29 => '[No se realiza la costura de caracteres]';

  @override
  String get t_trouble29_01 => '*Causa 1\nSe ha utilizado un pie prensatela inadecuado.\n\n*Solución\nColoque el pie prensatela adecuado.';

  @override
  String get t_trouble29_02 => '*Causa 2\nAjustes de selección de costura incorrectos.\n\n*Solución\nRevise los ajustes de selección de costura.';

  @override
  String get t_trouble29_03 => '*Causa 3\nNo utilizó un material estabilizador con telas finas o elásticas.\n\n*Solución\nColoque un material estabilizador.';

  @override
  String get t_trouble29_04 => '*Causa 4\nTensión del hilo incorrecta.\n\n*Solución\nAjuste la tensión del hilo.';

  @override
  String get t_trouble30 => '[Costura incorrecta de puntada de bordado]';

  @override
  String get t_trouble30_01 => '*Causa 1\nEl hilo está enrollado.\n\n*Solución\nUse tijeras, etc. para cortar el hilo enrollado y sáquelo de la guía, etc.';

  @override
  String get t_trouble30_02 => '*Causa 2\nTela no insertada correctamente en el bastidor (tela suelta, etc.) .\n\n*Solución\nSi la tela no está tensa en el bastidor, la costura no se bordará bien o quedará fruncida. Coloque la tela correctamente en el bastidor.';

  @override
  String get t_trouble30_03 => '*Causa 3\nMaterial estabilizador no colocado.\n\n*Solución\nUtilice siempre un material estabilizador, especialmente con telas elásticas, ligeras, muy trenzadas o que encojan fácilmente. Pregunte al vendedor más cercano cuál es el estabilizador adecuado.';

  @override
  String get t_trouble30_04 => '*Causa 4\nHabía un objeto cerca de la máquina y el cartucho o el bastidor tropezó con él durante la costura.\n\n*Solución\nSi el bastidor choca con algún objeto durante el bordado, la costura no se realizará correctamente. No coloque ningún objeto en la zona donde el bastidor pueda golpearlo durante la costura.';

  @override
  String get t_trouble30_05 => '*Causa 5\nLa tela fuera de los bordes del bastidor interfiere con el brazo de bordado, por lo que la unidad de bordado no puede moverse.\n\n*Solución\nVuelva a insertar la tela en el bastidor de manera que la tela sobrante esté alejada del brazo de costura y pueda girar la costura 180 grados.';

  @override
  String get t_trouble30_06 => '*Causa 6\nTela demasiado pesada, por lo que la unidad de bordado no puede moverse libremente.\n\n*Solución\nColoque un libro grueso o un objeto similar debajo de la cabeza del brazo de costura para elevar ligeramente la parte pesada y mantenerla nivelada.';

  @override
  String get t_trouble30_07 => '*Causa 7\nLa tela cuelga de la mesa.\n\n*Solución\nSi la tela cuelga de la mesa durante el bordado, la unidad de bordado no podrá moverse libremente. Coloque la tela de tal manera que no cuelgue de la mesa (o sujétela para evitar que arrastre) .';

  @override
  String get t_trouble30_08 => '*Causa 8\nTela pillada o enganchada en algo.\n\n*Solución\nDetenga la máquina y coloque la tela de tal manera que no se enganche.';

  @override
  String get t_trouble30_09 => '*Causa 9\nEl bastidor se retiró durante la costura (por ejemplo, para ajustar la bobina) . Se golpeó o movió el pie prensatela mientras retiraba o colocaba el bastidor o se movió la unidad de bordado.\n\n*Solución\nSi el pie prensatela tropieza o la unidad de bordado se mueve durante la costura, la costura no se realizará correctamente. Tenga cuidado cuando retire o coloque el bastidor durante el bordado.';

  @override
  String get t_trouble31 => '[La aguja se rompe]';

  @override
  String get t_trouble31_01 => '*Causa 1\nLa aguja no está colocada correctamente.\n\n*Solución\nColoque la aguja correctamente.';

  @override
  String get t_trouble31_02 => '*Causa 2\nEl tornillo de la presilla de la aguja está suelto.\n\n*Solución\nApriete el tornillo de la presilla de la aguja.';

  @override
  String get t_trouble31_03 => '*Causa 3\nLa aguja está girada o doblada.\n\n*Solución\nReemplace la aguja.';

  @override
  String get t_trouble31_04 => '*Causa 4\nAguja o hilo inadecuados para la tela escogida.\n\n*Solución\nCompruebe el diagrama de \"Combinaciones de tela/hilo/aguja\".';

  @override
  String get t_trouble31_05 => '*Causa 5\nSe ha utilizado un pie prensatela inadecuado.\n\n*Solución\nUse el pie prensatela recomendado.';

  @override
  String get t_trouble31_06 => '*Causa 6\nEl hilo superior está demasiado tenso.\n\n*Solución\nAjuste la tensión del hilo.';

  @override
  String get t_trouble31_07 => '*Causa 7\nLa tela es empujada durante la costura.\n\n*Solución\nNo empuje la tela durante la costura.';

  @override
  String get t_trouble31_08 => '*Causa 8\nLa tapa del carrete está colocada incorrectamente.\n\n*Solución\nCompruebe el método para colocar la tapa del carrete y colóquela de nuevo.';

  @override
  String get t_trouble31_09 => '*Causa 9\nHay hendiduras alrededor de los orificios de la placa de la aguja.\n\n*Solución\nReemplace la placa de la aguja o consulte con el vendedor más cercano.';

  @override
  String get t_trouble31_10 => '*Causa 10\nHay hendiduras alrededor de los orificios en el pie prensatela.\n\n*Solución\nReemplace el pie prensatela o consulte con el vendedor más cercano.';

  @override
  String get t_trouble31_11 => '*Causa 11\nHay hendiduras en la guía.\n\n*Solución\nReemplace la guía o consulte con el vendedor más cercano.';

  @override
  String get t_trouble31_12 => '*Causa 12\nLa aguja es defectuosa.\n\n*Solución\nReemplace la aguja.';

  @override
  String get t_trouble32 => '[La tela no se arrastra bien por la máquina]';

  @override
  String get t_trouble32_01 => '*Causa 1\nLos dientes de arrastre están en posición baja.\n\n*Solución\nPulse la tecla para la modalidad de bordado libre y a continuación gire la polea para elevar los dientes de arrastre.';

  @override
  String get t_trouble32_02 => '*Causa 2\nPuntadas demasiado juntas.\n\n*Solución\nEscoja una puntada más larga.';

  @override
  String get t_trouble32_03 => '*Causa 3\nSe ha utilizado un pie prensatela inadecuado.\n\n*Solución\nUse el pie prensatela adecuado.';

  @override
  String get t_trouble32_04 => '*Causa 4\nLa aguja está girada, doblada o la punta está desafilada.\n\n*Solución\nReemplace la aguja.';

  @override
  String get t_trouble32_05 => '*Causa 5\nEl hilo está enredado.\n\n*Solución\nCorte el hilo enredado y sáquelo de la guía.';

  @override
  String get t_trouble33 => '[La máquina no funciona]';

  @override
  String get t_trouble33_01 => '*Causa 1\nNo se ha seleccionado ninguna costura.\n\n*Solución\nSeleccione una costura.';

  @override
  String get t_trouble33_02 => '*Causa 2\nBotón de \"inicio/parar\" no accionado.\n\n*Solución\nPulse el botón de \"inicio/parar\" .';

  @override
  String get t_trouble33_03 => '*Causa 3\nLa máquina no está enchufada a la corriente.\n\n*Solución\nEncienda la máquina de coser (con el interruptor de la alimentación) .';

  @override
  String get t_trouble33_04 => '*Causa 4\nEl pie prensatela no está bajo.\n\n*Solución\nBaje el pie prensatela.';

  @override
  String get t_trouble33_05 => '*Causa 5\nPulsó el botón de \"inicio/parar\" con el pedal colocado.\n\n*Solución\nRetire el pedal o úselo para operar la máquina.';

  @override
  String get t_trouble33_06 => '*Causa 6\nLa palanca de control de velocidad está ajustada para controlar el ancho de la puntada de zig-zag.\n\n*Solución\nUse el pedal, en lugar del botón de \"inicio/parar\", para operar la máquina.';

  @override
  String get t_trouble34 => '[La unidad de bordado no funciona]';

  @override
  String get t_trouble34_01 => '*Causa 1\nNo se ha seleccionado ninguna costura.\n\n*Solución\nSeleccione una costura.';

  @override
  String get t_trouble34_02 => '*Causa 2\nLa máquina no está enchufada a la corriente.\n\n*Solución\nEncienda la máquina de coser (con el interruptor de la alimentación) .';

  @override
  String get t_trouble34_03 => '*Causa 3\nUnidad de bordado colocada incorrectamente.\n\n*Solución\nColoque la unidad de bordado correctamente.';

  @override
  String get t_trouble34_04 => '*Causa 4\nBastidor montado antes de que la unidad arrancase.\n\n*Solución\nEjecute el proceso de arranque correctamente.';

  @override
  String get t_trouble35 => ' [El hilo está enredado en el revés de la tela]';

  @override
  String get t_trouble35_01 => ' *Causa 1\nAjuste incorrecto del contraste de la pantalla LCD.\n\n*Solución\nAjuste el contraste de la pantalla LCD.';

  @override
  String get t_maintenance36 => '[Limpieza de la guía y la lanzadera]';

  @override
  String get t_maintenance36_00 => 'Si el polvo o la suciedad entran en la guía o en la lanzadera, la máquina no funcionará correctamente y es posible que la característica de detección del hilo de la bobina no funcione. Para un óptimo resultado, mantenga la máquina limpia.\nAntes de apagar la máquina, lea los pasos que se explican a continuación.';

  @override
  String get t_maintenance36_01 => '\n1. Pulse el botón de \"Posición de aguja\" para subir la aguja.';

  @override
  String get t_maintenance36_02 => '2. Baje el pie prensatela.';

  @override
  String get t_maintenance36_03 => '(a) APAGADO\n(b) ENCENDIDO\n\n3. Apague la máquina de coser (interruptor en OFF) .';

  @override
  String get t_maintenance36_04 => '4. Extraiga la aguja y el soporte del pie prensatela.';

  @override
  String get t_maintenance36_05_11 => '5. Retire la unidad de cama plana o la unidad de bordado si están instaladas.\nDeslice la palanca de la placa de la aguja hacia usted.\nSe abre la placa de la aguja.';

  @override
  String get t_maintenance36_05_12 => '(a) Deslice hacia usted.\n';

  @override
  String get t_maintenance36_05_13 => '6. Deslice la placa de la aguja con la mano derecha para quitarla.';

  @override
  String get t_maintenance36_05_14 => '(a) Cubierta de la placa de la aguja\n';

  @override
  String get t_maintenance36_05_15 => '\n7. Sujete la caja de la bobina y levántela con cuidado.';

  @override
  String get t_maintenance36_07_02 => '(a) Caja de la bobina';

  @override
  String get t_maintenance36_08 => '\n8. Utilice el cepillo de limpieza o una aspiradora para eliminar la pelusa y el polvo de la guía y la zona de alrededor.\n\n* No aplique aceite lubricante a la caja de la bobina.';

  @override
  String get t_maintenance36_08_02 => '(a) Cepillo de limpieza\n(b) Guía';

  @override
  String get t_embbasic18_04_21 => '\n9. Inserte la caja de la bobina de modo que la marca ▲ de la caja quede alineada con la marca ● de la máquina.';

  @override
  String get t_embbasic18_04_22 => '(a)  marca ▲ en la caja de la bobina\n(b)  marca ● en la máquina';

  @override
  String get t_embbasic18_04_23 => '\n10. Inserte las pestañas de la placa de la aguja en las muescas de la máquina.';

  @override
  String get t_embbasic18_04_24 => '(a) Pestañas\n(b) Muescas';

  @override
  String get t_embbasic18_04_25 => '11. Presione en el lado derecho de la placa de la aguja para fijarla.';

  @override
  String get t_sewing01_00 => 'Selección del tipo de costura';

  @override
  String get t_sewing01_00_01 => '1-01:Costura normal\n1-05:Costura de refuerzo (remate) \n1-06:Costura de tela elástica';

  @override
  String get t_sewing01_00_01_s_normal => 'Costura normal';

  @override
  String get t_sewing01_00_01_s_reinforced => 'Costura de refuerzo (remate)';

  @override
  String get t_sewing01_00_01_s_stretch => 'Costura de tela elástica';

  @override
  String get t_sewing01 => '[Puntadas rectas]';

  @override
  String get t_sewing01_01 => '\n1. Coloque el pie prensatela \"J\".\nSujete los extremos del hilo y la tela con la mano izquierda y gire el volante de graduación con la mano derecha para insertar la aguja en la tela.';

  @override
  String get t_sewing01_01_02 => '(a) Posición de inicio de costura';

  @override
  String get t_sewing01_02 => '\n2. Baje el pie prensatela y pulse el botón de \"Costura en reversa/remate\" para coser 3 o 4 puntadas. La máquina cose puntadas en reversa (o remates) .\nPulse el botón de \"inicio/parar\" para coser hacia delante. La máquina empezará a coser lentamente.';

  @override
  String get t_sewing01_03 => '(a) Costuras en reversa';

  @override
  String get t_sewing01_04 => '3. Cuando termine de coser, pulse el botón de \"Costura en reversa/remate\" para coser 3 o 4 puntadas en reversa (o remates) al final de la costura.';

  @override
  String get t_sewing01_05 => '4. Después de coser, pulse el botón de \"corte de hilo\" para cortar los hilos.\n\n* Si se seleccionan en la pantalla las funciones para cortar el hilo automáticamente o coser puntadas de remate automáticamente, las puntadas en reversa (o los remates) se coserán automáticamente al principio de la costura al pulsar el botón de \"inicio/parar\".Pulse el botón de \"Costura en reversa/remate\" para coser puntadas en reversa (o remates) y cortar el hilo automáticamente al final de la costura.';

  @override
  String get t_sewing01_06 => '\n * Cambiar la posición de la aguja \n Cuando seleccione puntadas con la posición de la aguja a la izquierda o en el centro, puede utilizar las teclas \"+\" y \"-\" en la pantalla de movimiento I/D para cambiar la posición de la aguja. Para conseguir un bonito acabado, ajuste la misma distancia entre el borde derecho del pie prensatela y la aguja con el margen de costura y mantenga el borde del pie prensatela paralelo al borde de la tela durante la costura.';

  @override
  String get t_sewing01_07 => '(a) Margen de costura';

  @override
  String get t_sewing02 => '[Sobrehilado]';

  @override
  String get t_sewing02_00 => 'Selección del tipo de costura';

  @override
  String get t_sewing02_00_01 => '1-16:Tela fina y mediana\n1-17:Tela gruesa\n1-18:Telas medianas, pesadas y fáciles de arrugar\n1-19:Tela elástica\n1-20:Tela elástica gruesa y mediana\n1-21:Tela elástica';

  @override
  String get t_sewing02_00_01_f_lightandmedium => 'Tela fina y mediana';

  @override
  String get t_sewing02_00_01_f_heavy => 'Tela gruesa';

  @override
  String get t_sewing02_00_01_f_mediumstretch => 'Telas medianas, pesadas y fáciles de arrugar';

  @override
  String get t_sewing02_00_01_f_stretch1 => 'Tela elástica';

  @override
  String get t_sewing02_00_01_f_thickandmediumstretch => 'Tela elástica gruesa y mediana';

  @override
  String get t_sewing02_00_01_f_stretch2 => 'Tela elástica';

  @override
  String get t_sewing02_01 => '1. Coloque el pie prensatela \"G\". Baje el pie prensatela de manera que la guía del pie quede nivelada con el borde de la tela.';

  @override
  String get t_sewing02_02 => '\n2. Cosa a lo largo de la guía del pie prensatela.';

  @override
  String get t_sewing02_02_02 => '(a) Guía';

  @override
  String get t_sewing02_03 => '\n1. Coloque el pie prensatela \"J\". Cosa con la aguja ligeramente fuera del borde de la tela.';

  @override
  String get t_sewing02_04 => '(a) Posición de entrada de la aguja\n';

  @override
  String get t_sewing02_05 => '\n*Una vez ajustado el ancho de la puntada, gire con la mano el volante de graduación hacia usted y compruebe que la aguja no roza el pie prensatela. Si la aguja toca el pie prensatela, podría romperse y causar lesiones.';

  @override
  String get t_sewing02_05_02 => '(a) La aguja no debe tocar';

  @override
  String get t_sewing04 => '[Puntadas de festón]';

  @override
  String get t_sewing04_01 => 'Esta puntada para satén ondulada recibe el nombre de puntada de festón. Utilice esta puntada para decorar los bordes de cuellos de blusas y pañuelos, o como dobladillos.\nEs posible que necesite un pulverizador adhesivo temporal para las telas ligeras. Antes de coser la labor, cosa unas puntadas de prueba sobre la tela.';

  @override
  String get t_sewing04_02 => '1. Coloque el pie prensatela \"N+\". Cosa puntadas festón a lo largo del borde de la tela. No cosa directamente sobre el borde de la tela.';

  @override
  String get t_sewing04_03 => '2. Recorte a lo largo de la costura, asegurándose de que no corta las puntadas.';

  @override
  String get t_sewing05_00 => 'Selección del tipo de costura';

  @override
  String get t_sewing05_00_01 => '4-01:Tela fina y mediana (para ojales horizontales)\n4-07:Tela fina o mediana\n4-10:Tela elástica con trenzados\n4-11:Tela elástica\n4-13:Trajes, abrigos\n4-14:Vaqueros, pantalones\n4-15:Abrigos gruesos';

  @override
  String get t_sewing05_00_01_f_lighttomediumhorizhole => 'Tela fina y mediana (para ojales horizontales)';

  @override
  String get t_sewing05_00_01_f_lighttomedium => 'Tela fina o mediana';

  @override
  String get t_sewing05_00_01_f_stretchweaves => 'Tela elástica con trenzados';

  @override
  String get t_sewing05_00_01_f_stretch => 'Tela elástica';

  @override
  String get t_sewing05_00_01_f_suitsandovercoat => 'Trajes, abrigos';

  @override
  String get t_sewing05_00_01_f_jeansandtrousers => 'Vaqueros, pantalones';

  @override
  String get t_sewing05_00_01_f_thickcoats => 'Abrigos gruesos';

  @override
  String get t_sewing05 => '[Ojales]';

  @override
  String get t_sewing05_02 => '1. Marque la posición y longitud del ojal en la tela.';

  @override
  String get t_sewing05_03 => '\n2. Coloque el pie para ojales \"A+\". Extraiga la placa del soporte del botón en el pie prensatela e inserte el botón que desee utilizar para el ojal. A continuación, apriete la placa del soporte del botón alrededor del botón.\n\n* El tamaño del ojal viene dado por el tamaño del botón colocado en la placa del soporte del botón.';

  @override
  String get t_sewing05_04 => '(a) Placa del soporte del botón\n';

  @override
  String get t_sewing05_05 => '\n3. Haga coincidir el pie prensatela con la marca en la tela y baje el pie prensatela.';

  @override
  String get t_sewing05_06 => '(a) Marca en la tela\n(b) Marcas en el pie prensatela';

  @override
  String get t_sewing05_07 => '\n4. Baje la palanca para ojales de manera que quede detrás del corchete de metal del pie prensatela.';

  @override
  String get t_sewing05_08 => '(a) Corchete de metal';

  @override
  String get t_sewing05_09 => '4. Sujete suavemente el extremo del hilo superior y comience a coser. Arrastre la tela con la mano suavemente mientras cose el ojal.\nUna vez cosido el ojal, la máquina coserá automáticamente puntadas de remate y se detendrá.';

  @override
  String get t_sewing05_10 => '\n5. Inserte un alfiler atravesado por la parte interior de una de las barras de remate, clave el abreojales en el centro del ojal y corte hacia el alfiler.';

  @override
  String get t_sewing05_11 => '(a) Alfiler de hilvanado\n(b) Abreojales';

  @override
  String get t_sewing05_12 => '\n[Para ojales de cerradura]\nUtilice el perforador para hacer un orificio en el extremo redondo del ojal. Inserte un alfiler atravesado por la parte interior de una de las barras de remate, clave el abreojales en el orificio que ha realizado con el perforador y corte hacia el alfiler.';

  @override
  String get t_sewing05_13 => '(a) Lápiz Calador\n(b) Alfiler de hilvanado\n';

  @override
  String get t_sewing05_14 => '*Costura de telas elásticas\nCuando cosa telas elásticas con 4-10 o 4-11, cosa las puntadas del ojal sobre hilo galón.';

  @override
  String get t_sewing05_16 => '\n1. Enganche el hilo galón en la parte trasera del pie prensatela \"A+\". Inserte los extremos en las ranuras de la parte delantera del pie prensatela y haga un nudo provisional.';

  @override
  String get t_sewing05_17 => '(a) Hilo superior';

  @override
  String get t_sewing05_18 => '2. Baje el pie prensatela y empiece a coser.';

  @override
  String get t_sewing05_19 => '3. Cuando termine de coser, tire con suavidad del hilo galón para que no queden hilos flojos y corte el hilo sobrante.';

  @override
  String get t_sewing05_20 => '\n*Botones irregulares/Botones que no caben en la placa del soporte del prensatelas\nUtilice las marcas en la regla del pie prensatela para determinar el tamaño del ojal. Una marca en la regla del pie prensatela equivale a 5mm. Sume el diámetro y el grosor del botón y ajuste la placa con el valor calculado.';

  @override
  String get t_sewing05_21 => '(a) Regla del pie prensatela\n(b) Placa del soporte del botón\n(c) Medida calculada del diámetro + grosor\n(d) 5 mm';

  @override
  String get t_sewing05_22 => '\nEjemplo:\nPara un botón con un diámetro de 15 mm, un grosor de 10 mm, debe ajustar la regla a 25 mm.';

  @override
  String get t_sewing05_23 => '(a) 10 mm\n(b) 15 mm';

  @override
  String get t_sewing06 => '[Costura de botón]';

  @override
  String get t_sewing06_01 => 'No utilice la función de corte automático del hilo cuando cosa botones. De lo contrario, perderá los extremos del hilo.\n \n1. Coloque el pie para costura de botón \"M\", deslice el botón a lo largo de la placa de metal introduciéndolo en el pie prensatela y baje el pie prensatela.';

  @override
  String get t_sewing06_01_02 => '(a) Botón \n(b) Placa de metal\n';

  @override
  String get t_sewing06_02 => '2. Gire la polea para comprobar que la aguja se inserta en cada orificio correctamente. Sujete suavemente el extremo del hilo superior y comience a coser. La máquina se detendrá automáticamente cuando finalice la costura.';

  @override
  String get t_sewing06_03 => '3. Desde el revés de la tela, tire del extremo del hilo de la bobina para atravesar el hilo superior por el revés de la tela. Haga un nudo con los extremos del hilo y corte los hilos.';

  @override
  String get t_sewing06_04 => '*Costura de botones de 4 orificios';

  @override
  String get t_sewing06_05 => 'Cosa primero por los orificios más cercanos a usted. Levante el pie prensatela y mueva la tela de manera que la aguja se inserte en los siguientes dos orificios; a continuación, cosa del mismo modo.';

  @override
  String get t_sewing06_06 => '*Costura de una lengüeta a un botón\n\n1. Tire de la palanca para lengüetas hacia usted antes de empezar a coser.';

  @override
  String get t_sewing06_07 => '(a) Palanca para lengüetas\n';

  @override
  String get t_sewing06_08 => '2. Tire de los extremos del hilo superior entre el botón y la tela, enróllelos alrededor de la lengüeta y haga un nudo firme.\nAte los extremos del hilo de la bobina del principio y del fin de la costura en la parte del revés de la tela.';

  @override
  String get t_sewing07 => '[Barra de remate]';

  @override
  String get t_sewing07_01 => 'Utilice las barras de remate para reforzar las zonas que soportan más tirantez, como las juntas de los bolsillos.';

  @override
  String get t_sewing07_02 => '\n1. Coloque el pie para ojales \"A+\" y ajuste la regla a la longitud de la barra de remate que desee coser.';

  @override
  String get t_sewing07_03 => '(a) Regla del pie prensatela\n(b) Medida de longitud completada\n(c) 5 mm';

  @override
  String get t_sewing07_04 => '2. Coloque la tela de tal manera que el bolsillo se desplace hacia usted cuando cosa.';

  @override
  String get t_sewing07_05 => '\n3. Compruebe el primer punto de caída de la aguja y baje el pie prensatela.';

  @override
  String get t_sewing07_06 => '(a) 2 mm';

  @override
  String get t_sewing07_09 => '4. Sujete suavemente el extremo del hilo superior y comience a coser. Una vez terminada la labor, la máquina coserá automáticamente una puntada de refuerzo (remate) y se detendrá.';

  @override
  String get t_sewing07_10 => '\n*Barra de remate en telas gruesas\nColoque un trozo de tela doblada o cartón junto a la tela que desee coser para nivelar el pie para ojales y facilitar el arrastre de la tela.';

  @override
  String get t_sewing07_11 => '(a) Pie prensatela\n(b) Papel grueso\n(c) Tela';

  @override
  String get t_sewing08 => '[Costura de cremalleras]';

  @override
  String get t_sewing08_00 => '\n*Cremallera centrada\nPara bolsos y otras aplicaciones.\n\n1. Coloque el pie prensatela \"J\" y cosa puntadas rectas hasta la abertura de la cremallera. Cambie a una costura de hilvanado y cosa hacia la parte superior de la tela.';

  @override
  String get t_sewing08_02 => '(a) Costuras de hilvanado\n(b) Costuras en reversa\n(c) Fin de la abertura de la cremallera\n(d) Revés';

  @override
  String get t_sewing08_03 => '\n2. Abra el margen de costura y cosa la cremallera con costuras de hilvanado en el centro de cada lado de la cremallera.';

  @override
  String get t_sewing08_04 => '(a) Costuras de hilvanado\n(b) Cremallera\n(c) Revés';

  @override
  String get t_sewing08_05 => '\n3. Extraiga el pie prensatela \"J\". Haga coincidir el lado derecho de la patilla en el pie para cremalleras \"I\" con el soporte del pie prensatela y coloque el pie para cremalleras.';

  @override
  String get t_sewing08_06 => '(a) Derecha\n(b) Izquierda\n(c) Posición de entrada de la aguja';

  @override
  String get t_sewing08_07 => '4. Cosa a una distancia entre 7 y 10 mm desde el borde cosido de la tela y quite los hilvanados.';

  @override
  String get t_sewing08_08 => '\n*Inserción de una cremallera lateral\nPara cremalleras laterales en faldas o vestidos.\n\n1. Coloque el pie prensatela \"J\" y cosa puntadas rectas hasta la abertura de la cremallera. Cambie a una costura de hilvanado y cosa hacia la parte superior de la tela.';

  @override
  String get t_sewing08_11 => '(a) Costuras en reversa\n(b) Revés de la tela\n(c) Costuras de hilvanado\n(d) Fin de la abertura de la cremallera';

  @override
  String get t_sewing08_12 => '\n2. Abra el margen de costura y haga coincidir el dobladillo con los dientes de la cremallera, manteniendo 3 mm para el espacio de la costura.';

  @override
  String get t_sewing08_13 => '(a) Inicio de la abertura de la cremallera\n(b) Revés de la tela\n(c) Dientes de la cremallera\n(d) Fin de la abertura de la cremallera\n(e) 3 mm';

  @override
  String get t_sewing08_14 => '\n3. Extraiga el pie prensatela \"J\". Haga coincidir el lado derecho de la patilla del pie para cremalleras \"I\" con el soporte del pie prensatela y coloque el pie para cremalleras.';

  @override
  String get t_sewing08_15 => '(a) Derecha\n(b) Izquierda\n(c) Posición de entrada de la aguja';

  @override
  String get t_sewing08_16 => '\n4. Coloque el pie prensatela en el margen de 3 mm. Comenzando desde el final de la abertura de la cremallera, cosa hasta un punto a unos 50 mm del borde de la tela y detenga, a continuación, la máquina de coser. Baje el riel de la cremallera y continúe cosiendo hacia el borde de la tela.';

  @override
  String get t_sewing08_17 => '(a) 50 mm\n(b) 3 mm';

  @override
  String get t_sewing08_18 => '\n5. Cierre la cremallera, dé la vuelta a la tela y realice una costura de hilvanado.';

  @override
  String get t_sewing08_19 => '(a) Parte delantera de la falda (revés de la tela) \n(b) Costuras de hilvanado\n(c) Parte delantera de la falda (lado derecho de la tela) \n(d) Parte trasera de la falda (lado derecho de la tela) ';

  @override
  String get t_sewing08_20 => '\n6. Extraiga el pie prensatela y colóquelo de nuevo de manera que la parte izquierda de la patilla quede sujeta al soporte del pie prensatela.\n\n* Cuando cosa la parte izquierda de la cremallera, la aguja debe caer en la parte derecha del pie prensatela. Cuando cosa la parte derecha de la cremallera, la aguja debe caer en la parte izquierda del pie prensatela.';

  @override
  String get t_sewing08_21 => '(a) Derecha\n(b) Izquierda\n(c) Posición de entrada de la aguja';

  @override
  String get t_sewing08_22 => '\n7. Coloque la tela de forma que el borde izquierdo del pie prensatela toque el borde de los dientes de la cremallera. Cosa puntadas en reversa en la parte delantera de la cremallera y continúe cosiendo. Deje de coser a unos 50 mm del borde de la tela, deje la aguja insertada en la tela y retire las costuras de hilvanado. Abra la cremallera y cosa el resto de la costura.';

  @override
  String get t_sewing08_23 => '(a) Costuras de hilvanado\n(b) De 7 a 10 mm \n(c) Costuras en reversa\n(d) 50 mm';

  @override
  String get t_sewing09_00 => 'Selección del tipo de costura';

  @override
  String get t_sewing09_00_01 => 'Seleccione estas puntadas para coser dobladillos o puños de vestidos, blusas, pantalones o faldas.';

  @override
  String get t_sewing09_00_02 => '2-01:Otra tela\n2-02:Tela elástica';

  @override
  String get t_sewing09_00_02_f_other => 'Otra tela';

  @override
  String get t_sewing09_00_02_f_stretch => 'Tela elástica';

  @override
  String get t_sewing09 => '[Puntadas invisibles para dobladillo]';

  @override
  String get t_sewing09_01 => '\n1. Coloque hacia arriba el revés de la tela y doble e hilvane la tela.';

  @override
  String get t_sewing09_02 => '(a) 5 mm\n(b) Costuras de hilvanado\n(c) Revés de la tela';

  @override
  String get t_sewing09_03 => '\n2. Coloque el pie para puntadas invisibles para dobladillo \"R\" y baje el pie prensatela. Disponga la tela de tal manera que el borde doblado toque la guía del pie prensatela.';

  @override
  String get t_sewing09_04 => '(a) Guía\n(b) Doblez';

  @override
  String get t_sewing09_05 => '\n3. Cosa la tela, manteniendo el borde doblado en contacto con el pie prensatela.';

  @override
  String get t_sewing09_06 => '(a) Posición de la aguja';

  @override
  String get t_sewing09_07 => '\n4. Retire las costuras de hilvanado y dé la vuelta a la tela.';

  @override
  String get t_sewing09_08 => '(a) Revés de la tela\n(b) Lado derecho de la tela';

  @override
  String get t_sewing10 => '[Aplicaciones]';

  @override
  String get t_sewing10_01 => '\n1. Utilice un pulverizador adhesivo temporal, pegamento para telas o una costura de hilvanado para sujetar la aplicación a la tela. De esta manera evitará que la tela se mueva mientras cose.';

  @override
  String get t_sewing10_02 => '(a) Aplicación\n(b) Pegamento para telas';

  @override
  String get t_sewing10_03 => '\n2. Coloque el pie prensatela \"J\". Compruebe que la aguja se clava ligeramente fuera del borde de la aplicación y, a continuación, comience a coser.';

  @override
  String get t_sewing10_04 => '(a) Material de aplicación\n(b) Posición de entrada de la aguja';

  @override
  String get t_sewing10_06 => '*Costuras de curvas pronunciadas\n';

  @override
  String get t_sewing10_07 => 'Detenga la máquina de coser con la aguja insertada en la tela y fuera de la aplicación. Para conseguir un acabado bonito, levante el pie prensatela y gire un poco la tela a la vez que cose.';

  @override
  String get t_sewing11 => '[Doblez]';

  @override
  String get t_sewing11_01 => '\n1. Marque a lo largo de las dobleces en el revés de la tela.';

  @override
  String get t_sewing11_01_02 => '(a) Revés';

  @override
  String get t_sewing11_02 => '\n2. Gire la tela y planche sólo las partes dobladas.';

  @override
  String get t_sewing11_02_02 => '(a) Superficie';

  @override
  String get t_sewing11_03 => '\n3. Coloque el pie prensatela \"I\".\nCosa una puntada recta a lo largo de la doblez.';

  @override
  String get t_sewing11_04_02 => '(a) Ancho para fruncido\n(b) Revés\n(c) Superficie';

  @override
  String get t_sewing11_05 => '4. Planche las dobleces en el mismo sentido.';

  @override
  String get t_sewing12 => '[Fruncidos]';

  @override
  String get t_sewing12_00 => '\nSe utilizan en las cinturas de faldas, mangas de camisas, etc.';

  @override
  String get t_sewing12_01 => '\n1. Tire de la bobina y los hilos superiores unos 50 mm.';

  @override
  String get t_sewing12_01_02 => '(a) Hilo superior\n(b) Hilo de la bobina\n(c) Unos 50 mm';

  @override
  String get t_sewing12_02 => '\n2. Cosa dos filas de puntadas rectas paralelas a la línea de la costura y, a continuación, recorte el hilo sobrante dejando 50 mm.';

  @override
  String get t_sewing12_02_02 => '(a) Línea de la costura\n(b) De 10 a 15 mm \n(c) Unos 50 mm';

  @override
  String get t_sewing12_03 => '3. Tire de los hilos de la bobina para conseguir la cantidad de fruncido que desee y anude los hilos.';

  @override
  String get t_sewing12_04 => '4. Alise los fruncidos con la plancha.\n';

  @override
  String get t_sewing12_05 => '5. Cosa por la línea de la costura y quite la costura de hilvanado.';

  @override
  String get t_sewing13 => '[Costura de pinzas]';

  @override
  String get t_sewing13_01 => '\n1. Realice una costura en reversa al principio de la pinza y, a continuación, cosa desde el extremo ancho hasta el otro extremo sin estirar la tela.';

  @override
  String get t_sewing13_01_02 => '(a) Hilvanado\n(b) Superficie\n(c) Revés';

  @override
  String get t_sewing13_02 => '2. Corte el hilo en el extremo dejando 50 mm y anude ambos extremos.\n\n* No cosa una costura en reversa en el extremo.';

  @override
  String get t_sewing13_03 => '3. Inserte los extremos del hilo en la pinza con una aguja de coser a mano.';

  @override
  String get t_sewing13_04 => '4. Planche la pinza por un solo lado para que quede lisa.';

  @override
  String get t_sewing14 => '[Costura de corte plana]';

  @override
  String get t_sewing14_00 => '\nSe utiliza para reforzar costuras y dar un acabado preciso a los bordes.\n\n1. Cosa la línea de terminación y corte la mitad del margen de costura desde el lado en el que se colocará la costura de corte plana.';

  @override
  String get t_sewing14_01_02 => '(a) Revés\n(b) Unos 12 mm';

  @override
  String get t_sewing14_02 => '\n2. Extienda la tela a lo largo de la línea de terminación.';

  @override
  String get t_sewing14_02_02 => '(a) Revés\n(b) Línea de terminación';

  @override
  String get t_sewing14_03 => '\n3. Disponga ambos márgenes de costura en el lado de la costura más corta (costura cortada) y plánchelos.';

  @override
  String get t_sewing14_03_02 => '(a) Revés';

  @override
  String get t_sewing14_04 => '\n4. Doble el margen de costura más largo alrededor del más corto y cosa el borde de la doblez.';

  @override
  String get t_sewing14_04_01 => '(a) Revés';

  @override
  String get t_sewing15_00 => 'Selección del tipo de costura';

  @override
  String get t_sewing15_00_01 => 'Q-01:Puntada para remendar(medio)\nQ-02:Puntada para remendar(derecha)\nQ-03:Puntada para remendar(izquierda)';

  @override
  String get t_sewing15_00_01_s_piecingmiddle => 'Puntada para remendar(medio)';

  @override
  String get t_sewing15_00_01_s_piecingright => 'Puntada para remendar(derecha)';

  @override
  String get t_sewing15_00_01_s_piecingleft => 'Puntada para remendar(izquierda)';

  @override
  String get t_sewing15 => '[Remiendos]';

  @override
  String get t_sewing15_01 => '(a) Margen de costura: 6,5mm(aprox.1/4 pulg.)\n      (cuando se selecciona Q-02)\n(b) Alineación con el lado derecho del pie prensatela.\n\n1. Coloque el pie prensatela \"J\".\nCosa con el borde de la tela alineado con el lado del pie prensatela.';

  @override
  String get t_sewing15_012 => '(a) Margen de costura: 7mm\n      (cuando se selecciona Q-02)\n(b) Alineación con el lado derecho del pie prensatela.\n\n1. Coloque el pie prensatela \"J\".\nCosa con el borde de la tela alineado con el lado del pie prensatela.';

  @override
  String get t_sewing15_01_02 => '(a) Margen de costura: 6,5mm(aprox.1/4 pulg.)\n       (cuando se selecciona Q-03)\n(b) Alineación con el lado izquierdo del pie prensatela.\n\n1. Coloque el pie prensatela \"J\".\nCosa con el borde de la tela alineado con el lado del pie prensatela.';

  @override
  String get t_sewing15_01_022 => '(a) Margen de costura: 7mm\n       (cuando se selecciona Q-03)\n(b) Alineación con el lado izquierdo del pie prensatela.\n\n1. Coloque el pie prensatela \"J\".\nCosa con el borde de la tela alineado con el lado del pie prensatela.';

  @override
  String get t_sewing15_02 => '(a) Guía\n\nEste pie para acolchado de 1/4\" con guía puede coser un margen de costura exacto de 1/4 pulgadas o de 1/8 pulgadas.Puede utilizarse para remendar un acolchado o para costuras sobrepuestas.\n\n1. Seleccione Q-01 y coloque el pie para acolchado de 1/4\" con la guía.';

  @override
  String get t_sewing15_03 => '(a) Guía\n(b) 1/4 pulg. (6,4mm)\n\n2. Utilice la guía del pie prensatela y las marcas para coser márgenes de costura exactos.\n\n\"Remiendo con un margen de costura de 1/4 pulg. (6,4mm)\"\nCosa manteniendo el borde de las telas contra la guía.';

  @override
  String get t_sewing15_04 => '(a) Alineación de esta marca con el borde de la tela para comenzar\n(b) Comienzo de la costura\n(c) Final de la costura\n(d) Borde opuesto de la tela para finalizar o girar\n(e) 1/4 pulg. (6,4mm)\n\n\"Creación de un margen de costura exacto\"\nUtilice la marca del pie para comenzar o finalizar la costura, o girar 1/4 pulg. respecto al borde de la tela.';

  @override
  String get t_sewing15_05 => '(a) Superficie de la tela\n(b) Costura\n(c) 1/8 pulg. (3,2mm)\n\n\"Acolchado con costuras sobrepuestas, 1/8 pulg. (3,2mm)\"\nCosa con el borde de la tela alineado con el lado izquierdo del extremo del pie prensatela.';

  @override
  String get t_sewing16 => '[Acolchados]';

  @override
  String get t_sewing16_01 => '1. Retire el pie prensatela, y afloje el tornillo del soporte del pie prensatela para extraer dicho soporte.\n\nColoque el adaptador en la barra del pie prensatela, alineando el lado plano de la abertura del adaptador con el lado plano de la barra. Empújela al máximo hacia arriba, y luego apriete firmemente el tornillo con el destornillador.';

  @override
  String get t_sewing16_02 => '(a) Palanca de operación\n(b) Tornillo de la presilla de la aguja\n(c) Horquilla\n(d) Barra del pie prensatela\n\n2. Ajuste la palanca de operación del pie móvil de forma que el tornillo de la presilla de la aguja quede entre la horquilla, y coloque el pie móvil sobre la barra del pie prensatela.';

  @override
  String get t_sewing16_03 => '3. Baje la palanca del pie prensatela. Apriete bien el tornillo con un destornillador.';

  @override
  String get t_sewing16_04 => '4. Coloque una mano en cada lado delo pie prensatela para sujetar con seguridad la tela mientras cosa.';

  @override
  String get t_sewing16_05 => '* Si el \"Sistema sensor automático de tela\" de la pantalla de ajustes de la máquina está ajustado a \"ON\", la tela podrá avanzar suavemente para obtener los mejores resultados de costura.';

  @override
  String get t_sewing17 => '[Acolchado libre]';

  @override
  String get t_sewing17_00 => '(a) Pie para acolchado libre \"C\"\n(b) Pie para acolchado libre de puntera abierta \"O\"\n\nPara el acolchado libre, utilice el pie para acolchado libre \"C\" o el pie para acolchado libre de puntera abierta \"O\" según el patrón que haya seleccionado.';

  @override
  String get t_sewing17_01 => '1. Pulse la tecla de dientes de arrastre para ajustar la máquina en el modo de costura libre.';

  @override
  String get t_sewing17_02_01 => '(a) Tornillo del soporte del pie prensatela\n(b) Muesca\n2. Coloque el pie para acolchado libre \"C\" en la parte delantera, con el tornillo del soporte del pie prensatela alineado con la muesca de dicho pie.\nDespués apriete el tornillo del soporte del pie prensatela.';

  @override
  String get t_sewing17_02_02 => '(a) Pasador\n(b) Tornillo de la presilla de la aguja\n(c) Barra del pie prensatela\n\nColoque el pie para acolchado libre de puntera abierta \"O\" situando la patilla del pie prensatela encima del tornillo de la presilla de la aguja y alineando la parte inferior izquierda y la barra del pie prensatela.\nDespués apriete el tornillo del soporte del pie prensatela.';

  @override
  String get t_sewing17_03 => '(a) Puntada\n\n3.Utilice las dos manos para estirar la tela , y después mueva ésta a ritmo normal a fin de coser con puntadas uniformes de unos 2,0-2,5 mm (aprox. 1/6\'3/32 pulgadas) de longitud.\n\n* Recomendamos fijar el controlador del pie y coser a velocidad constante.';

  @override
  String get t_sewing18 => '[Acolchado repetido]';

  @override
  String get t_sewing18_00 => '(a) 6,4mm(aprox.1/4 pulg.)\n(b) 9,5mm(aprox.3/8 pulg.)\n\nPie para acolchado de repetición libre \"E\".';

  @override
  String get t_sewing18_01 => '2. Retire el pie prensatela, afloje el tornillo del soporte del pie prensatela,  y después extraiga el tornillo y el soporte del pie prensatela.\n\nColoque el adaptador en la barra del pie prensatela, alineando el lado plano de la abertura del adaptador con el lado plano de la barra. Empújela al máximo hacia arriba, y luego apriete firmemente el tornillo con el destornillador.';

  @override
  String get t_sewing18_02 => '3. Coloque el pie para acolchado de repetición libre \"E\" en el lado izquierdo de la barra del pie prensatela, con los orificios del pie y de la barra alineados.\n\nGire el tornillo pequeño incluido 2 ó 3 veces con la mano.';

  @override
  String get t_sewing18_03 => '4. Apriete el tornillo.';

  @override
  String get t_sewing18_04 => '(a) 6,4mm(aprox.1/4 pulg.)\n\n5. Utilizando la escala del pie de repetición a modo de guía, cosa alrededor del motivo.';

  @override
  String get t_sewing18_05 => 'Labor de costura finalizada';

  @override
  String get t_sewing19 => '[Aplicaciones]';

  @override
  String get t_sewing19_01 => '(a) Margen de costura de 3 a 5 mm\n\n1. Marque el dibujo sobre la tela de la aplicación y recorte alrededor.';

  @override
  String get t_sewing19_02 => '2. Corte un trozo de papel grueso o material estabilizador según la medida del diseño de la aplicación, colóquelo en la parte posterior de la aplicación y dóblelo sobre el margen de costura con una plancha.';

  @override
  String get t_sewing19_03 => '3. Dé la vuelta al aplique y retire el estabilizador o el papel. Prenda con alfileres o hilvane la aplicación en la la tela principal.';

  @override
  String get t_sewing19_04 => '(a) Punto de caída de la aguja\n\n4. Coloque el pie prensatela \"J\".\nCompruebe el punto de entrada de la aguja y cosa a lo largo del borde de la aplicación, asegurándose de que la aguja se clava ligeramente fuera del borde de la tela.';

  @override
  String get t_explain_use => '[Uso]';

  @override
  String get t_explain01_01 => 'Costura general, fruncidos, dobleces, etc. La costura en reversa se cose a la vez que pulsa el botón de \"Costura en reversa/remate\".';

  @override
  String get t_explain01_01_01 => '\n * Cambiar la posición de la aguja \n Cuando seleccione puntadas con la posición de la aguja a la izquierda o en el centro, puede utilizar las teclas \"+\" y \"-\" en la pantalla de movimiento I/D para cambiar la posición de la aguja. Para conseguir un bonito acabado, ajuste la misma distancia entre el borde derecho del pie prensatela y la aguja con el margen de costura y mantenga el borde del pie prensatela paralelo al borde de la tela durante la costura.';

  @override
  String get t_explain01_02 => 'Costura general, fruncidos, dobleces, etc. La costura reforzada se cose a la vez que pulsa el botón de \"Costura en reversa/remate\".';

  @override
  String get t_explain01_03 => 'Costura general, fruncidos, dobleces, etc. La costura en reversa se cose a la vez que pulsa el botón de \"Costura en reversa/remate\".';

  @override
  String get t_explain01_04 => 'Costura general, fruncidos, dobleces, etc. La costura reforzada se cose a la vez que pulsa el botón de \"Costura en reversa/remate\".';

  @override
  String get t_explain01_05 => 'Costura general para costuras sobrepuestas decorativas y de remate.';

  @override
  String get t_explain01_06 => 'Costuras de remate, aplicaciones decorativas y costura.';

  @override
  String get t_explain01_07 => 'Puntadas decorativas y costuras sobrepuestas.';

  @override
  String get t_explain01_08 => 'Hilvanado.';

  @override
  String get t_explain01_09 => 'Para sobrehilado y remiendos. La costura en reversa se cose la vez que pulsa el botón de \"Costura en reversa/remate\".';

  @override
  String get t_explain01_10 => 'Para sobrehilado y remiendos. La costura de remate se cose a la vez que se pulsa el botón de \"Costura en reversa/remate\".';

  @override
  String get t_explain01_11 => 'Comience con la posición de la aguja a la derecha y la costura en zig-zag a la izquierda.';

  @override
  String get t_explain01_12 => 'Comience con la posición de la aguja a la izquierda y la costura en zig-zag a la derecha.';

  @override
  String get t_explain01_13 => 'Sobrehilado (telas medianas y elásticas) , cintas y gomas.';

  @override
  String get t_explain01_14 => 'Sobrehilado (telas medianas, pesadas y elásticas) , cintas y gomas.';

  @override
  String get t_explain01_14a => 'Sobrehilado (telas medianas, pesadas y elásticas) , cintas y gomas.';

  @override
  String get t_explain01_15 => 'Refuerzo de telas ligeras y medianas.';

  @override
  String get t_explain01_16 => 'Refuerzo de telas pesadas.';

  @override
  String get t_explain01_17 => 'Refuerzo de telas medianas, pesadas y fáciles de arrugar o con puntadas decorativas.';

  @override
  String get t_explain01_18 => 'Costura de remate en tela elástica.';

  @override
  String get t_explain01_19 => 'Refuerzo de telas elásticas medianas y pesadas y puntadas decorativas.';

  @override
  String get t_explain01_20 => 'Refuerzo de telas elásticas o puntadas decorativas.';

  @override
  String get t_explain01_21 => 'Costura de punto elástico.';

  @override
  String get t_explain01_22 => 'Refuerzo y costura de telas elásticas.';

  @override
  String get t_explain01_23 => 'Refuerzo de telas elásticas.';

  @override
  String get t_explain01_24 => 'Puntada recta mientras se corta la tela.';

  @override
  String get t_explain01_25 => 'Puntada de zig-zag mientras se corta la tela.';

  @override
  String get t_explain01_26 => 'Puntada de sobrehilado mientras se corta la tela.';

  @override
  String get t_explain01_27 => 'Puntada de sobrehilado mientras se corta la tela.';

  @override
  String get t_explain01_28 => 'Puntada de sobrehilado mientras se corta la tela.';

  @override
  String get t_explain01_29 => 'Margen de costura a la derecha de 6,5mm para retales/patchwork.';

  @override
  String get t_explain01_292 => 'Margen de costura a la derecha de 7mm para retales/patchwork.';

  @override
  String get t_explain01_29a => 'Retales/patchwork';

  @override
  String get t_explain01_30 => 'Margen de costura a la izquierda de 6,5mm para retales/patchwork.';

  @override
  String get t_explain01_302 => 'Margen de costura a la izquierda de 7mm para retales/patchwork.';

  @override
  String get t_explain01_31 => 'Puntada de acolchado para dar una apariencia de confección manual.';

  @override
  String get t_explain01_32 => 'Puntada de zig-zag para acolchado y costuras en retales de acolchado para aplicaciones.';

  @override
  String get t_explain01_33 => 'Costura de acolchado para juntas o costuras invisibles de aplicaciones.';

  @override
  String get t_explain01_34 => 'Acolchado de fondo.';

  @override
  String get t_explain02_01 => 'Dobladillo de telas trenzadas.';

  @override
  String get t_explain02_02 => 'Dobladillo de telas elásticas.';

  @override
  String get t_explain02_03 => 'Aplicaciones y puntadas decorativas para sábanas.';

  @override
  String get t_explain02_04 => 'Acabado de borde invisible en telas. Aumente la tensión del hilo superior para conseguir un bonito acabado festón en puntadas de concha.';

  @override
  String get t_explain02_05 => 'Decoración de cuellos de camisa y bordes de pañuelos.';

  @override
  String get t_explain02_06 => 'Decoración de cuellos de camisa y bordes de pañuelos.';

  @override
  String get t_explain02_07 => 'Puntadas patchwork y puntadas decorativas.';

  @override
  String get t_explain02_08 => 'Puntadas patchwork y puntadas decorativas.';

  @override
  String get t_explain02_09 => 'Puntadas decorativas, unión de cordeles y punto de Bolonia.';

  @override
  String get t_explain02_10 => 'Puntadas de smoking y decorativas.';

  @override
  String get t_explain02_11 => 'Vainicas y puntadas decorativas.';

  @override
  String get t_explain02_12 => 'Vainicas, puntadas de puente y decorativas.';

  @override
  String get t_explain02_13 => 'Costura de cintas en telas elásticas.';

  @override
  String get t_explain02_14 => 'Puntadas decorativas.';

  @override
  String get t_explain02_15 => 'Costuras sobrepuestas decorativas.';

  @override
  String get t_explain02_15a => 'Puntadas decorativas.';

  @override
  String get t_explain02_16 => 'Puntadas decorativas.';

  @override
  String get t_explain02_17 => 'Puntadas decorativas y costura de gomas.';

  @override
  String get t_explain02_18 => 'Puntadas decorativas y aplicaciones.';

  @override
  String get t_explain02_19 => 'Puntadas decorativas.';

  @override
  String get t_explain03_01 => 'Dobladillos decorativos y puntada recta triple a la izquierda.';

  @override
  String get t_explain03_02 => 'Dobladillos decorativos y puntada recta triple en el centro.';

  @override
  String get t_explain03_03 => 'Dobladillos decorativos y costuras sobrepuestas.';

  @override
  String get t_explain03_04 => 'Dobladillos decorativos y costura de unión de lazos.';

  @override
  String get t_explain03_05 => 'Dobladillos decorativos.';

  @override
  String get t_explain03_06 => 'Puntada de dobladillos decorativos Daisy.';

  @override
  String get t_explain03_07 => 'Broches y adornos, dobladillos decorativos.';

  @override
  String get t_explain03_08 => 'Broches y adornos, dobladillos decorativos.';

  @override
  String get t_explain03_09 => 'Broches y adornos, dobladillos decorativos.';

  @override
  String get t_explain03_10 => 'Broches y adornos, dobladillos decorativos.';

  @override
  String get t_explain03_11 => 'Broches y adornos, dobladillos decorativos.';

  @override
  String get t_explain03_12 => 'Broches y adornos, dobladillos decorativos.';

  @override
  String get t_explain03_13 => 'Broches y adornos, dobladillos decorativos.';

  @override
  String get t_explain03_14 => 'Broches y adornos, dobladillos decorativos.';

  @override
  String get t_explain03_15 => 'Broches y adornos, dobladillos decorativos.';

  @override
  String get t_explain03_16 => 'Broches y adornos, dobladillos decorativos.';

  @override
  String get t_explain03_17 => 'Broches y adornos, dobladillos decorativos.';

  @override
  String get t_explain03_18 => 'Broches y adornos, dobladillos decorativos.';

  @override
  String get t_explain03_19 => 'Dobladillos decorativos y puntada de puente.';

  @override
  String get t_explain03_20 => 'Dobladillos decorativos. Vainicas y costura de gomas.';

  @override
  String get t_explain03_21 => 'Dobladillos decorativos y smoking.';

  @override
  String get t_explain03_22 => 'Dobladillos decorativos y smoking.';

  @override
  String get t_explain03_23 => 'Dobladillos decorativos y smoking.';

  @override
  String get t_explain03_24 => 'Dobladillos decorativos.';

  @override
  String get t_explain03_25 => 'Puntadas decorativas.';

  @override
  String get t_explain04_01 => 'Ojal en telas ligeras y medianas.';

  @override
  String get t_explain04_02 => 'Ojal con espacio extra para botones más grandes.';

  @override
  String get t_explain04_03 => 'Ojales entallados reforzados.';

  @override
  String get t_explain04_04 => 'Ojales con barra de remate vertical en telas pesadas.';

  @override
  String get t_explain04_05 => 'Ojales con barra de remate.';

  @override
  String get t_explain04_06 => 'Ojal para telas finas y medianas a pesadas.';

  @override
  String get t_explain04_07 => 'Ojales para telas ligeras a medianas.';

  @override
  String get t_explain04_08 => 'Ojales con espacio extra para botones decorativos más grandes.';

  @override
  String get t_explain04_09 => 'Ojales resistentes con barras de remate verticales.';

  @override
  String get t_explain04_10 => 'Ojales para telas elásticas o trenzadas.';

  @override
  String get t_explain04_11 => 'Ojales para broches y adornos y telas elásticas.';

  @override
  String get t_explain04_12 => 'El primer paso para realizar ojales básicos.';

  @override
  String get t_explain04_13 => 'Ojales en telas pesadas o gruesas para botones planos más grandes.';

  @override
  String get t_explain04_14 => 'Ojales en telas medianas a pesadas para botones planos más grandes.';

  @override
  String get t_explain04_15 => 'Ojales con barra de remate vertical para reforzar telas pesadas o gruesas.';

  @override
  String get t_explain04_15a => 'Lado izquierdo de un ojal de 4 pasos.';

  @override
  String get t_explain04_15b => 'Barra de remate de un ojal de 4 pasos.';

  @override
  String get t_explain04_15c => 'Lado derecho de un ojal de 4 pasos.';

  @override
  String get t_explain04_15d => 'Barra de remate de un ojal de 4 pasos.';

  @override
  String get t_explain04_16 => 'Zurcido de telas medianas.';

  @override
  String get t_explain04_17 => 'Zurcido de telas pesadas.';

  @override
  String get t_explain04_18 => 'Refuerzo de bolsillos, etc.';

  @override
  String get t_explain04_19 => 'Costura de botones.';

  @override
  String get t_explain04_20 => 'Para realizar calado, orificios en cinturones, etc. Si la costura no es del todo correcta, ajuste el patrón de costura.';

  @override
  String get t_explain04_21 => 'Para realizar calados u orificios en forma de estrella. Si la costura no es del todo correcta, ajuste el patrón de costura.';

  @override
  String get t_explain05_01 => 'Para colocar aplicaciones en telas con forma de tubo o con esquinas.';

  @override
  String get t_explain05_02 => 'Para colocar aplicaciones en telas con forma de tubo o con esquinas.';

  @override
  String get t_explain05_03 => 'Para colocar aplicaciones en telas con forma de tubo o con esquinas.';

  @override
  String get t_explain05_04 => 'Para colocar aplicaciones en telas con forma de tubo.';

  @override
  String get t_explain05_05 => 'Para colocar aplicaciones en telas con forma de tubo.';

  @override
  String get t_explain05_06 => 'Para colocar aplicaciones en telas con forma de tubo o con esquinas.';

  @override
  String get t_explain05_07 => 'Para colocar aplicaciones en telas con forma de tubo o con esquinas.';

  @override
  String get t_explain05_08 => 'Para colocar aplicaciones en telas con forma de tubo o con esquinas.';

  @override
  String get t_explain05_09 => 'Para colocar aplicaciones en telas con forma de tubo.';

  @override
  String get t_explain05_10 => 'Para colocar aplicaciones en telas con forma de tubo.';

  @override
  String get t_explain05_11 => 'Para colocar aplicaciones en telas con forma de tubo o con esquinas.';

  @override
  String get t_explain05_12 => 'Para colocar aplicaciones en telas con forma de tubo o con esquinas.';

  @override
  String get t_explain06_01 => 'Para crear adornos con hilos, etc., y crear efectos decorativos de adorno con costura libre.';

  @override
  String get t_explain06_02 => 'Hilvanado de costura libre\nCuando se bajan los dientes de arrastre, se puede coser el hilvanado mientras se mueve libremente la tela.';

  @override
  String get t_explain06_03a => 'Este patrón de puntada se compone de varias puntadas cortas.\nCosa este patrón de puntada con un hilo de nailon transparente o con un hilo fino de un color similar a la tela del hilo superior para que su proyecto parezca cosido a mano. Si el color del hilo de la bobina es distinto al de la tela, el patrón de puntada quedará destacado.';

  @override
  String get t_explain07_01 => 'Apliques, puntada decorativa.';

  @override
  String get t_explain07_02 => 'El patrón de puntada puede estrecharse al principio o al final de la costura.';

  @override
  String get t_explaindeco00_01 => 'Costura de bonitos acabados';

  @override
  String get t_explaindeco00_02 => 'Realización de ajustes';

  @override
  String get t_explaindeco01_00 => '[Costura de bonitos acabados]';

  @override
  String get t_explaindeco01_01 => 'Para realizar bonitos acabados en las puntadas decorativas o de caracteres, compruebe las combinaciones de tela/aguja/hilo. Otros factores, como el grosor de la tela, el material estabilizador, etc., pueden influir también en la puntada; por lo tanto, realice siempre unas puntadas de prueba antes de comenzar realmente la labor.';

  @override
  String get t_explaindeco01_02 => '(a) Tela\n(b) Estabilizador\n(c) Papel fino\n\n*Tela: Cuando cosa telas elásticas, ligeras o muy trenzadas, coloque opcionalmente un material estabilizador. Si no desea esta opción, coloque la tela sobre un papel fino, como el papel de patrones.\n\n*Hilo\n#50 - #60\n\n*Aguja\nCon telas ligeras, normales o elásticas: aguja de punta redonda (color dorado) \nCon telas pesadas: aguja de máquina de coser doméstica 90/14\n\n*Pie prensatela\nPie para monogramas \"N+\". El uso de otro pie prensatela puede dar peores resultados.';

  @override
  String get t_explaindeco02_00 => '[Realización de ajustes]';

  @override
  String get t_explaindeco02_01 => 'La costura puede en ocasiones resultar deficiente, según el tipo y grosor de la tela, el material estabilizador empleado, la velocidad de cosido, etc. Si la costura no es del todo correcta, realice algunas puntadas de prueba en las mismas condiciones que si se tratase de una costura real; además, ajuste el patrón de costura como se explica a continuación. Si el resultado de la costura no es satisfactorio,  incluso después de haber hecho ajustes según la costura 6-120, realice los ajustes para cada costura por separado.';

  @override
  String get t_explaindeco02_02 => '1. Seleccione 6-120. Coloque el pie para monogramas \"N+\" y cosa la costura.';

  @override
  String get t_explaindeco02_03_00 => '\n2. Compare la costura terminada con la ilustración de la costura siguiente.';

  @override
  String get t_explaindeco02_04_00 => '[1. Si la costura está arrugada]';

  @override
  String get t_explaindeco02_04_01 => 'Pulse + en la pantalla de ajuste vertical preciso. Cosa de nuevo. Si la costura sigue siendo incorrecta, realice los ajustes de nuevo. Realice ajustes hasta que la costura quede correctamente.';

  @override
  String get t_explaindeco02_05_00 => '[2. Si la costura tiene tramos sin coser]';

  @override
  String get t_explaindeco02_05_01 => 'Pulse - en la pantalla de ajuste vertical preciso. Cosa de nuevo. Si la costura sigue siendo incorrecta, realice los ajustes de nuevo. Realice ajustes hasta que la costura quede correctamente.';

  @override
  String get t_explaindeco02_06_00 => '[3. Si la costura se tuerce hacia la izquierda]';

  @override
  String get t_explaindeco02_06_01 => 'Pulse + en la pantalla de ajuste horizontal preciso. Si la costura sigue siendo incorrecta, realice los ajustes de nuevo. Realice ajustes hasta que la costura quede correctamente.';

  @override
  String get t_explaindeco02_07_00 => '[4. Si la costura se tuerce hacia la derecha]';

  @override
  String get t_explaindeco02_07_01 => 'Pulse - en la pantalla de ajuste horizontal preciso. Si la costura sigue siendo incorrecta, realice los ajustes de nuevo. Realice ajustes hasta que la costura quede correctamente.';

  @override
  String get t_terms_read => 'Lea atentamente los siguientes términos.';

  @override
  String get t_terms_cert_read => 'Lea atentamente los siguientes términos.';

  @override
  String get t_terms_cert_01_00 => 'Certificación del KIT de actualización';

  @override
  String get t_terms_cert_01_01 => 'Términos relativos a la certificación del KIT de actualización\n';

  @override
  String get t_terms_cert_01_02 => '  Cuando active cualquier función opcional en este software (“Software”), incluida, entre otras, las licencias, manuales, documentos y otros materiales pagados, así como cualquiera de sus actualizaciones (en conjunto, las “Herramientas”), es posible que se le solicite, de manera directa o indirecta, que facilite ciertos códigos de licencia, números de producto, números de serie y demás información relacionada (“Datos del usuario”) para poder utilizar las Herramientas.\n';

  @override
  String get t_terms_cert_01_03 => '  Parte de la información incluida en los Datos del usuario puede estar relacionada con los datos que pueda haber registrado en Brother Industries, Ltd. (“Empresa”) o en el sitio web de registro de productos de su empresa filial.  No obstante, la Empresa no utilizará los Datos del usuario para identificarle ni para ninguna otra finalidad que no sea la de activar las Herramientas. Los Datos del usuario pueden transmitirse al servidor administrativo de la Empresa o a los servidores de proveedores de servicios en la nube como Microsoft y Amazon, que pueden estar ubicados en países donde el nivel de protección de los datos personales no sea igual al de su país. No obstante, la Empresa protegerá sus Datos del usuario de acuerdo con la ley aplicable empleando las medidas de seguridad adecuadas para evitar el uso o divulgación no autorizados.';

  @override
  String get t_terms_nettool_read => 'Lea atentamente los siguientes términos.';

  @override
  String get t_terms_nettool_01_00 => 'Herramienta de diagnóstico de red';

  @override
  String get t_terms_nettool_01_01 => 'Términos relativos a la herramienta de diagnóstico de red\n';

  @override
  String get t_terms_nettool_01_02 => '  Si observa algún problema con su conexión de red, puede ejecutar la herramienta de diagnóstico de red en este software (“Software”).  Cuando realice un diagnóstico de red, se mostrará en pantalla la información de los productos de costura o artesanales (“Productos de la Empresa”) y de cualquier dispositivo conectado a dichos productos, incluida, entre otras, la dirección IP (protocolo de Internet) o la dirección MAC (control de acceso a medios), la información de conexión de proxy, la máscara de subred, la puerta de enlace, el servidor DNS y demás información relacionada (“Información relacionada con la red”).  ';

  @override
  String get t_terms_nettool_01_03 => '  Si experimenta problemas con la conexión a Internet del Producto de la Empresa y desea obtener asistencia técnica, es posible que se le solicite que facilite su Información relacionada con la red a sus distribuidores o proveedores locales, o a Brother Industries, Ltd. (“Empresa”) y/o a sus filiales en persona, por teléfono, por correo, por fax o por Internet.  Si decide facilitar su Información relacionada con la red, usted acepta y entiende que dicha información puede transmitirse a la Empresa o a sus filiales únicamente con la finalidad de analizar o arreglar su problema con la red, y que su información se protegerá de acuerdo con la ley aplicable.\n  Con excepción de lo establecido en este documento, la Empresa o sus filiales no recopilarán ni almacenarán su Información relacionada con la red, a menos que dispongan de su aprobación previa por separado.';

  @override
  String get t_terms_cert_read_t => 'Lea atentamente los siguientes términos.';

  @override
  String get t_terms_cert_01_01_t => 'Términos relativos a la certificación del KIT de actualización\n';

  @override
  String get t_terms_cert_01_02_t => '  Cuando active cualquier función opcional en este software (“Software”), incluida, entre otras, las licencias, manuales, documentos y otros materiales pagados, así como cualquiera de sus actualizaciones (en conjunto, las “Herramientas”), es posible que se le solicite, de manera directa o indirecta, que facilite ciertos códigos de licencia, números de producto y números de serie (“Datos del usuario”) para poder utilizar las Herramientas.\n';

  @override
  String get t_terms_cert_01_03_t => '  Parte de la información incluida en los Datos del usuario puede estar relacionada con los datos que pueda haber registrado en el sitio web de registro de productos de Tacony Corporation, que opera con el nombre comercial de Baby Lock (“Empresa”).  No obstante, la Empresa no utilizará los Datos del usuario para identificarle ni para ninguna otra finalidad que no sea la de activar las Herramientas. Los Datos del usuario pueden transmitirse al servidor administrativo de la Empresa o a los servidores de proveedores de servicios en la nube como Microsoft y AWS, que pueden estar ubicados en países donde el nivel de protección de los datos personales no sea igual al de su país.  No obstante, la Empresa protegerá sus Datos del usuario de acuerdo con la ley aplicable empleando las medidas de seguridad adecuadas para evitar el uso o divulgación no autorizados.';

  @override
  String get t_terms_nettool_read_t => 'Lea atentamente los siguientes términos.';

  @override
  String get t_terms_nettool_01_01_t => 'Términos relativos a la herramienta de diagnóstico de red\n';

  @override
  String get t_terms_nettool_01_02_t => '  Si observa algún problema con su conexión de red, puede ejecutar la herramienta de diagnóstico de red en este software (“Software”).  Cuando realice un diagnóstico de red, se mostrará en pantalla la información de los productos de  costura (“Productos de la Empresa”) y de cualquier dispositivo conectado a dichos productos, incluida, entre otras, la dirección IP (protocolo de Internet) o la dirección MAC (control de acceso a medios), la información de conexión de proxy, la máscara de subred, la puerta de enlace, el servidor DNS y demás información relacionada (“Información relacionada con la red”).  ';

  @override
  String get t_terms_nettool_01_03_t => '  Si experimenta problemas con la conexión a Internet del Producto de la Empresa y desea obtener asistencia técnica, es posible que se le solicite que facilite su Información relacionada con la red a su distribuidor local o a Tacony Corporation, que opera con el nombre comercial de Baby Lock (“Empresa”) en persona, por teléfono, por correo, por fax o por Internet.  Si decide facilitar su Información relacionada con la red, usted acepta y entiende que dicha información puede transmitirse a la Empresa únicamente con la finalidad de analizar o arreglar su problema con la red, y que su información se protegerá de acuerdo con la ley aplicable.\n  Con excepción de lo establecido en este documento, la Empresa no recopilará ni almacenará su Información relacionada con la red, a menos que disponga de su aprobación previa por separado.';

  @override
  String get t_terms_mnmpinmac_01_b => 'Al hacer clic en \"Aceptar\", su código PIN, dirección MAC y número de máquina se enviarán al servidor de Brother para emparejar la máquina de coser con su ScanNCut y sus otras máquinas de coser.\nLa información proporcionada no se usará con fines distintos a los indicados anteriormente.';

  @override
  String get t_terms_snj_pair_01 => 'Al hacer clic en “OK”, el código PIN, la dirección MAC, el nombre y el número de la máquina se enviarán al servidor de Brother para emparejar la máquina de coser con los servicios/dispositivos Brother. \nLa información proporcionada no se usará con fines distintos a los indicados anteriormente.';

  @override
  String get upg_01 => 'Conecte el dispositivo USB.';

  @override
  String get upg_02 => 'No se puede leer el archivo.';

  @override
  String get upg_03 => 'No se encuentra el archivo correcto.';

  @override
  String get upg_04 => 'Compruebe el error de suma';

  @override
  String get upg_05 => 'Fallo al guardar el archivo.';

  @override
  String get upg_06 => 'La dirección del archivo es incorrecta.';

  @override
  String get upg_07 => 'Conectado al PC. No desconecte el cable USB.';

  @override
  String get upg_08 => 'Guardando el archivo de actualización. \nNo desconecte la alimentación.';

  @override
  String get update_08 => 'Guardando el archivo de actualización. \nNo desconecte la alimentación.';

  @override
  String get upg_09 => 'Actualización terminada.';

  @override
  String get update_09 => 'Actualización terminada.';

  @override
  String get upg_10 => 'Pulse CARGAR después de conectar el dispositivo USB que contiene el archivo de actualización.';

  @override
  String get update_10 => 'Pulse CARGAR después de conectar el dispositivo USB que contiene el archivo de actualización.';

  @override
  String get upg_12 => 'Pulse CARGAR para instalar el archivo de actualización.';

  @override
  String get update_13 => 'No se puede actualizar directamente a la nueva versión desde la versión actual.';

  @override
  String get update_14 => 'Actualice el software a la versión que se muestra a continuación desactivando la máquina y actualice con el dispositivo USB que contiene el archivo de actualización.';

  @override
  String get update_15 => 'Desactive la máquina y actualícela con el dispositivo USB que contiene el archivo de actualización.';

  @override
  String get icon_00037 => 'Retorno';

  @override
  String get icon_00008_u => 'Cerrar';

  @override
  String get icon_00009_u => 'Cancelar';

  @override
  String get icon_00010_u => 'ＯＫ';

  @override
  String get icon_00050_u => 'Cargar';

  @override
  String get upg_16 => 'Error en la actualización. \nInténtelo de nuevo instalando el programa de actualización. \n* Si el problema persiste, vuelva a descargar e instalar el programa.';

  @override
  String get upg_17 => 'Error en la actualización. \nDescargue e instale de nuevo el programa de actualización.';

  @override
  String get upg_18 => 'ERR_UPEND';

  @override
  String get upg_19 => 'Reinicie la máquina.\nPuede que tarde un poco en encenderla inicialmente. La pantalla puede oscurecerse temporalmente.';

  @override
  String get upg_20 => 'No apague la máquina incluso si la pantalla se oscurece.';

  @override
  String get upg_21 => 'Error en la detección de daños en el sistema de archivos.\nApague la máquina y vuelva a encenderla.';

  @override
  String get upg_22 => 'No se pudieron reparar los archivos del sistema dañados.\nApague la máquina y vuelva a encenderla.';

  @override
  String get upg_23 => 'Error en la actualización. \nDespués de iniciar la máquina normalmente, apáguela y luego intente instalar el programa de actualización nuevamente.';

  @override
  String get t_name_01_01 => 'Puntada recta (izquierda)';

  @override
  String get t_name_01_02 => 'Puntada recta (izquierda)';

  @override
  String get t_name_01_03 => 'Puntada recta (medio)';

  @override
  String get t_name_01_04 => 'Puntada recta (medio)';

  @override
  String get t_name_01_05 => 'Costura elástica triple';

  @override
  String get t_name_01_06 => 'Puntada quebrada';

  @override
  String get t_name_01_07 => 'Puntada decorativa';

  @override
  String get t_name_01_08 => 'Costura de hilvanado';

  @override
  String get t_name_01_09 => 'Puntada de zig-zag';

  @override
  String get t_name_01_10 => 'Puntada de zig-zag';

  @override
  String get t_name_01_11 => 'Puntada de zig-zag (derecha)';

  @override
  String get t_name_01_12 => 'Puntada de zig-zag (izquierda)';

  @override
  String get t_name_01_13 => 'Costura doble elástica en zig-zag';

  @override
  String get t_name_01_14 => 'Costura doble elástica en zig-zag';

  @override
  String get t_name_01_14a => 'Costura triple elástica en zig-zag';

  @override
  String get t_name_01_15 => 'Puntada de sobrehilado';

  @override
  String get t_name_01_16 => 'Puntada de sobrehilado';

  @override
  String get t_name_01_17 => 'Puntada de sobrehilado';

  @override
  String get t_name_01_18 => 'Puntada de sobrehilado';

  @override
  String get t_name_01_19 => 'Puntada de sobrehilado';

  @override
  String get t_name_01_20 => 'Puntada de sobrehilado';

  @override
  String get t_name_01_21 => 'Puntada de sobrehilado';

  @override
  String get t_name_01_22 => 'Sobrehilado en diamante sencillo';

  @override
  String get t_name_01_23 => 'Sobrehilado en diamante sencillo';

  @override
  String get t_name_01_24 => 'Con cuchilla lateral';

  @override
  String get t_name_01_25 => 'Con cuchilla lateral';

  @override
  String get t_name_01_26 => 'Con cuchilla lateral';

  @override
  String get t_name_01_27 => 'Con cuchilla lateral';

  @override
  String get t_name_01_28 => 'Con cuchilla lateral';

  @override
  String get t_name_01_29 => 'Puntada para remendar (derecha)';

  @override
  String get t_name_01_29a => 'Puntada para remendar (medio)';

  @override
  String get t_name_01_30 => 'Puntada para remendar(izquierda)';

  @override
  String get t_name_01_31 => 'Acolchado a mano';

  @override
  String get t_name_01_32 => 'Puntada de acolchado zigzag para aplicac.';

  @override
  String get t_name_01_33 => 'Puntada de acolchado para aplicaciones';

  @override
  String get t_name_01_34 => 'Puntada intermitente para acolchado';

  @override
  String get t_name_02_01 => 'Costura invisible para dobladillo';

  @override
  String get t_name_02_02 => 'Costura elástica para dobladillo';

  @override
  String get t_name_02_03 => 'Costura para sábanas';

  @override
  String get t_name_02_03a => 'Costura para sábanas';

  @override
  String get t_name_02_04 => 'Borde invisible';

  @override
  String get t_name_02_05 => 'Puntada de festón para satén';

  @override
  String get t_name_02_06 => 'Puntada de festón';

  @override
  String get t_name_02_07 => 'Puntada de unión de patchwork';

  @override
  String get t_name_02_08 => 'Costura overlock doble patchwork';

  @override
  String get t_name_02_09 => 'Costura de punto de Bolonia';

  @override
  String get t_name_02_10 => 'Puntada de smoking';

  @override
  String get t_name_02_11 => 'Puntada de plumas';

  @override
  String get t_name_02_12 => 'Puntada cruzada de vainicas';

  @override
  String get t_name_02_13 => 'Costura de cintas';

  @override
  String get t_name_02_14 => 'Puntada de escalera';

  @override
  String get t_name_02_15 => 'Puntada rick-rack';

  @override
  String get t_name_02_15a => 'Puntada decorativa';

  @override
  String get t_name_02_16 => 'Puntada decorativa';

  @override
  String get t_name_02_17 => 'Puntada serpentina';

  @override
  String get t_name_02_18 => 'Puntada decorativa';

  @override
  String get t_name_02_19 => 'Costura decorativa puntada';

  @override
  String get t_name_03_01 => 'Puntada de dobladillo';

  @override
  String get t_name_03_02 => 'Puntada de dobladillo';

  @override
  String get t_name_03_03 => 'Puntada de dobladillo zigzag';

  @override
  String get t_name_03_04 => 'Puntada de dobladillo';

  @override
  String get t_name_03_05 => 'Puntada de dobladillo';

  @override
  String get t_name_03_06 => 'Puntada de dobladillo';

  @override
  String get t_name_03_07 => 'Puntada de dobladillo';

  @override
  String get t_name_03_08 => 'Puntada de dobladillo';

  @override
  String get t_name_03_09 => 'Puntada de dobladillo';

  @override
  String get t_name_03_10 => 'Puntada de dobladillo';

  @override
  String get t_name_03_11 => 'Puntada de dobladillo';

  @override
  String get t_name_03_12 => 'Puntada panal de abeja';

  @override
  String get t_name_03_13 => 'Puntada panal de abeja';

  @override
  String get t_name_03_14 => 'Puntada de dobladillo';

  @override
  String get t_name_03_15 => 'Puntada de dobladillo';

  @override
  String get t_name_03_16 => 'Puntada de dobladillo';

  @override
  String get t_name_03_17 => 'Puntada de dobladillo';

  @override
  String get t_name_03_18 => 'Puntada de dobladillo';

  @override
  String get t_name_03_19 => 'Puntada de dobladillo';

  @override
  String get t_name_03_20 => 'Puntada de dobladillo';

  @override
  String get t_name_03_21 => 'Puntada de dobladillo';

  @override
  String get t_name_03_22 => 'Puntada de dobladillo';

  @override
  String get t_name_03_23 => 'Puntada de dobladillo';

  @override
  String get t_name_03_24 => 'Puntada de dobladillo';

  @override
  String get t_name_03_25 => 'Puntada de escalera';

  @override
  String get t_name_04_01 => 'Ojal estrecho redondeado';

  @override
  String get t_name_04_02 => 'Ojal ancho de punta redondeada';

  @override
  String get t_name_04_03 => 'Ojal alargado con punta redondeada';

  @override
  String get t_name_04_04 => 'Ojal con punta redondeada';

  @override
  String get t_name_04_05 => 'Ojal con punta redondeada';

  @override
  String get t_name_04_06 => 'Ojal con puntas redondeadas';

  @override
  String get t_name_04_07 => 'Ojal estrecho de punta cuadrada';

  @override
  String get t_name_04_08 => 'Ojal ancho de punta cuadrada';

  @override
  String get t_name_04_09 => 'Ojal de punta cuadrada';

  @override
  String get t_name_04_10 => 'Ojal elástico';

  @override
  String get t_name_04_11 => 'Ojal para adornos';

  @override
  String get t_name_04_12 => 'Ojal básico';

  @override
  String get t_name_04_13 => 'Ojal de cerradura';

  @override
  String get t_name_04_14 => 'Ojal de cerradura alargado';

  @override
  String get t_name_04_15 => 'Ojal de cerradura';

  @override
  String get t_name_04_15a => 'Ojal 1 de 4 pasos';

  @override
  String get t_name_04_15b => 'Ojal 2 de 4 pasos';

  @override
  String get t_name_04_15c => 'Ojal 3 de 4 pasos';

  @override
  String get t_name_04_15d => 'Ojal 4 de 4 pasos';

  @override
  String get t_name_04_16 => 'Zurcidos';

  @override
  String get t_name_04_17 => 'Zurcidos';

  @override
  String get t_name_04_18 => 'Barra de remate';

  @override
  String get t_name_04_19 => 'Costura de botón';

  @override
  String get t_name_04_20 => 'Calado';

  @override
  String get t_name_04_21 => 'Calado en estrella';

  @override
  String get t_name_05_01 => 'Diagonal izquierda hacia arriba (recta)';

  @override
  String get t_name_05_02 => 'En reversa (recta)';

  @override
  String get t_name_05_03 => 'Diagonal derecha hacia arriba (recta)';

  @override
  String get t_name_05_04 => 'Lateral a la izquierda (recta)';

  @override
  String get t_name_05_05 => 'Lateral a la derecha (recta)';

  @override
  String get t_name_05_06 => 'Diagonal izquierda hacia abajo (recta)';

  @override
  String get t_name_05_07 => 'Adelante (recta)';

  @override
  String get t_name_05_08 => 'Diagonal derecha hacia abajo (recta)';

  @override
  String get t_name_05_09 => 'Lateral a la izquierda (zig-zag)';

  @override
  String get t_name_05_10 => 'Lateral a la derecha (zig-zag)';

  @override
  String get t_name_05_11 => 'Adelante (zig-zag)';

  @override
  String get t_name_05_12 => 'En reversa (zig-zag)';

  @override
  String get t_name_06_01 => 'Costura libre de punto de Bolonia';

  @override
  String get t_name_06_02 => 'Hilvanado de costura libre';

  @override
  String get t_name_06_03 => 'Acolchado a mano';

  @override
  String get t_name_06_04 => 'Acolchado a mano';

  @override
  String get t_name_06_05 => 'Acolchado a mano';

  @override
  String get t_name_06_06 => 'Puntada de fieltrado';

  @override
  String get t_name_07_01 => 'Puntada para aplique';

  @override
  String get t_name_07_02 => 'Puntada de estrechamiento';

  @override
  String get t_name_sr_01 => 'Puntada recta (medio)';

  @override
  String get t_name_sr_02 => 'Puntada de zig-zag';

  @override
  String get t_name_sr_03 => 'Hilvanado de costura libre';

  @override
  String get tt_head_wifi => 'Ajustes de la LAN inalámbrica';

  @override
  String get tt_head_camera => 'Vista de la cámara';

  @override
  String get tt_head_setting => 'Ajustes de la máquina';

  @override
  String get tt_head_teaching => 'Ayuda de la máquina';

  @override
  String get tt_head_osae => 'Cambio del pie prensatela/aguja';

  @override
  String get tt_head_lock => 'Bloqueo de la pantalla';

  @override
  String get tt_head_home => 'Página principal';

  @override
  String get tt_foot_clock => 'Ajustes de hora/fecha';

  @override
  String get tt_tch_og_principal_parts1 => '[Palanca del pie prensatela]';

  @override
  String get tt_tch_og_principal_parts2 => '[Control de velocidad manual]';

  @override
  String get tt_tch_og_principal_parts3 => '[Polea]';

  @override
  String get tt_tch_og_principal_parts4 => '[Unidad de cama plana con compartimento para accesorios]';

  @override
  String get tt_tch_og_mb_knee_lifter => '[Elevador de rodilla]';

  @override
  String get tt_tch_og_principal_parts6 => '[Pedal]';

  @override
  String get tt_tch_og_principalbuttons1 => '[Botón de \"Posición de aguja\"]';

  @override
  String get tt_tch_og_principalbuttons2 => '[Botón de \"corte de hilo\"]';

  @override
  String get tt_tch_og_principalbuttons3 => '[Botón del \"elevador del pie prensatela\"]';

  @override
  String get tt_tch_og_principalbuttons4 => '[Botón de \"enhebrado automático\"]';

  @override
  String get tt_tch_og_principalbuttons5 => '[Botón de \"inicio/parar\"]';

  @override
  String get tt_tch_og_principalbuttons6 => '[Botón de \"en reversa\"]';

  @override
  String get tt_tch_og_principalbuttons7 => '[Botón del \"remate/amarre\"]';

  @override
  String get tt_tch_og_basic_operation1 => '[Hilo superior]';

  @override
  String get tt_tch_og_basic_operation2 => '[Devanado de bobina]';

  @override
  String get tt_tch_og_basic_operation3 => '[Cambio de aguja]';

  @override
  String get tt_tch_og_basic_operation4 => '[Cambio del pie prensatela]';

  @override
  String get tt_tch_og_basic_operation5 => '[Colocación de la bobina]';

  @override
  String get tt_tch_og_emb_basic_operation1 => '[Ajustes de tensión del hilo]';

  @override
  String get tt_tch_og_emb_basic_operation2 => '[Colocación de estabilizadores (refuerzo) para planchar en la tela]';

  @override
  String get tt_tch_og_emb_basic_operation3 => '[Inserción de la tela]';

  @override
  String get tt_tch_og_emb_basic_operation4 => '[Colocación del bastidor]';

  @override
  String get tt_tch_og_emb_basic_operation5 => '[Colocación de la unidad de bordado]';

  @override
  String get tt_tch_og_emb_basic_operation6 => '[Colocación del pie de bordado \"W\"]';

  @override
  String get tt_tch_og_emb_basic_operation7 => '[Estabilizador adecuado para utilizar]';

  @override
  String get tt_tch_maintenance1 => '[Limpieza de la guía y la lanzadera]';

  @override
  String get tt_utl_category01 => 'Puntada recta/sobrehilado';

  @override
  String get tt_utl_category02 => 'Puntadas decorativas';

  @override
  String get tt_utl_category03 => 'Puntadas de broches y adornos';

  @override
  String get tt_utl_category04 => 'Ojales/barras de remate';

  @override
  String get tt_utl_category05 => 'Costura multidireccional';

  @override
  String get tt_utl_category_q => 'Puntadas de acolchado';

  @override
  String get tt_utl_category_s => 'Otras puntadas';

  @override
  String get tt_utl_category_t => 'Reducción de puntadas';

  @override
  String get tt_utl_stitchpreview => 'Vista previa';

  @override
  String get tt_utl_projecter => 'Funciones de proyector';

  @override
  String get tt_utl_guideline => 'Marcador de guía';

  @override
  String get tt_utl_editmenu => 'Menú Editar';

  @override
  String get tt_utl_freemotion => 'Modo de costura libre';

  @override
  String get tt_utl_repeat_stitch_atamadashi => 'Volver al principio';

  @override
  String get tt_utl_alone_repeat => 'Costura sencilla/doble';

  @override
  String get tt_utl_utilityflipvertical => 'Efecto espejo';

  @override
  String get tt_utl_twinneedle => 'Aguja sencilla/aguja gemela';

  @override
  String get tt_utl_buttonholemanual => 'Longitud de la ranura del ojal';

  @override
  String get tt_utl_endpointsetting => 'Ajuste del punto final';

  @override
  String get tt_utl_tapering => 'Reducción de puntadas';

  @override
  String get tt_utl_autoreverse => 'Puntada de remate automática';

  @override
  String get tt_utl_scissor => 'Corte automático del hilo';

  @override
  String get tt_utl_needlestopposition => 'Ajuste de la posición de parada de la aguja';

  @override
  String get tt_utl_pivot => 'Girar/Arriba automático';

  @override
  String get tt_utl_threadcolor => 'Cambio del color del hilo';

  @override
  String get tt_utl_category06 => 'Amplia, variado';

  @override
  String get tt_utl_category07 => 'Amplia, botánico';

  @override
  String get tt_utl_category08 => 'Amplia, adornos y mensajes';

  @override
  String get tt_utl_category09 => 'Estrecha, variada';

  @override
  String get tt_utl_category10 => 'Estrecha, botánico';

  @override
  String get tt_utl_category11 => 'Puntada de candlewick';

  @override
  String get tt_utl_category12 => 'Satén grande';

  @override
  String get tt_utl_category13 => 'Satén';

  @override
  String get tt_utl_category14 => 'Puntadas de punto de cruz';

  @override
  String get tt_utl_category15 => 'Utilidad combinable';

  @override
  String get tt_utl_category16 => 'Disney';

  @override
  String get tt_utl_category17 => 'Fuente gótica';

  @override
  String get tt_utl_category18 => 'Fuente de escritura a mano';

  @override
  String get tt_utl_category19 => 'Fuente con borde';

  @override
  String get tt_utl_category20 => 'Fuente cirílica';

  @override
  String get tt_deco_category_pocket => 'Bolsillo(Memoria de la máquina/externa)';

  @override
  String get tt_deco_mycustomsititch => 'Función “MY CUSTOM STITCH” (MI PUNTADA PREFERIDA)';

  @override
  String get tt_deco_stitchpreview => 'Vista previa';

  @override
  String get tt_deco_projecter => 'Funciones de proyector';

  @override
  String get tt_deco_guidline => 'Marcador de guía';

  @override
  String get tt_deco_editmenu => 'Menú Editar';

  @override
  String get tt_deco_memory => 'Guardar datos del patrón de puntada';

  @override
  String get tt_deco_threadcolor => 'Cambio del color del hilo';

  @override
  String get tt_deco_stitchplus => 'Añadir patrón de puntada';

  @override
  String get tt_deco_stitchselectall => 'Activar/desactivar Seleccionar todo';

  @override
  String get tt_deco_pivot => 'Girar/Arriba automático';

  @override
  String get tt_deco_needlestopposition => 'Ajuste de la posición de parada de la aguja';

  @override
  String get tt_deco_scissor => 'Corte automático del hilo';

  @override
  String get tt_deco_autoreverse => 'Puntada de remate automática';

  @override
  String get tt_deco_stitchstep1 => 'Efecto escalonado';

  @override
  String get tt_deco_stitchstep2 => 'Efecto escalonado';

  @override
  String get tt_deco_filemanager => 'Administrador de archivos';

  @override
  String get tt_deco_filemanager_selectall => 'Seleccionar todo';

  @override
  String get tt_deco_filemanager_selectnone => 'Deseleccionar todo';

  @override
  String get tt_deco_filemanager_delete => 'Eliminar';

  @override
  String get tt_deco_filemanager_memory => 'Guardar los patrones de puntadas seleccionados en la memoria de la máquina.';

  @override
  String get tt_deco_freemotion => 'Modo de costura libre';

  @override
  String get tt_deco_repeat_stitch_atamadashi => 'Volver al principio';

  @override
  String get tt_deco_alone_repeat => 'Costura sencilla/doble';

  @override
  String get tt_deco_utilityfliphorizon => 'Efecto espejo horizontal';

  @override
  String get tt_deco_utilityflipvertical => 'Efecto espejo vertical';

  @override
  String get tt_deco_alone_single => 'Aguja sencilla/aguja gemela';

  @override
  String get tt_deco_delete => 'Elim.';

  @override
  String get tt_deco_density => 'Densidad del hilo';

  @override
  String get tt_deco_elongator => 'Alargar';

  @override
  String get tt_deco_spacing => 'Espacio entre caracteres';

  @override
  String get tt_deco_stitchsizelink => 'Mantener la relación de aspecto';

  @override
  String get tt_deco_endpointsetting => 'Ajuste del punto final';

  @override
  String get tt_mcs_triplesewing => 'Costura sencilla/triple';

  @override
  String get tt_mcs_pointdelete => 'Eliminar punto';

  @override
  String get tt_mcs_blockmove => 'Mover bloque';

  @override
  String get tt_mcs_insert => 'Insertar';

  @override
  String get tt_utl_mcspointset => 'Configurar';

  @override
  String get tt_mcs_contents => 'Importar patrones de puntadas';

  @override
  String get tt_mcs_memory => 'Guardar datos del patrón de puntada';

  @override
  String get tt_utl_sr_guideline => 'Marcador de guía';

  @override
  String get tt_utl_sr_sensingline => 'Línea de detección';

  @override
  String get tt_utl_sr_srstatus => 'Estado del regulador de puntadas';

  @override
  String get tt_embcate_embpatterns => 'Patrones de bordado';

  @override
  String get tt_embcate_character => 'Patrones de caracteres';

  @override
  String get tt_embcate_decoalphabet => 'Patrones de alfabeto decorativo';

  @override
  String get tt_embcate_frame => 'Patrones de marcos/escudos';

  @override
  String get tt_embcate_utility => 'Patrones para ojales/ Patrones de bordado de utilidad';

  @override
  String get tt_embcate_split => 'Patrones de bordado divididos';

  @override
  String get tt_embcate_long_stitch => 'Patrones de bordado de puntada larga';

  @override
  String get tt_embcate_quilt => 'Pieza de acolchado y patrones de acolchado de borde a borde';

  @override
  String get tt_embcate_b_disney => 'Patrones de Disney';

  @override
  String get tt_embcate_couching => 'Patrones couching';

  @override
  String get tt_embcate_t_exclusives => 'Exclusives';

  @override
  String get tt_embcate_memory => 'Patrones guardados en la memoria de la máquina, dispositivo USB, etc.';

  @override
  String get tt_emb_pantool => 'Herramienta de mano';

  @override
  String get tt_emb_backgroundscan => 'Escaneo de telas';

  @override
  String get tt_emb_realpreview => 'Vista previa';

  @override
  String get tt_emb_memory => 'Memoria';

  @override
  String get tt_emb_redo => 'Rehacer';

  @override
  String get tt_emb_undo => 'Deshacer';

  @override
  String get tt_emb_delete => 'Eliminar';

  @override
  String get tt_emb_select => 'Seleccionar';

  @override
  String get tt_emb_multipleselect => 'Selección múltiple';

  @override
  String get tt_emb_editsize => 'Tamaño';

  @override
  String get tt_emb_editmove => 'Mover';

  @override
  String get tt_emb_editgroup => 'Agrupar/desagrupar';

  @override
  String get tt_emb_editrotate => 'Girar';

  @override
  String get tt_emb_editflip => 'Invertir horizontal';

  @override
  String get tt_emb_editduplicate => 'Duplicar';

  @override
  String get tt_emb_editdensity => 'Densidad';

  @override
  String get tt_emb_editborder => 'Función de borde (Diseñar patrones repetitivos)';

  @override
  String get tt_emb_editapplique => 'Pieza de aplique';

  @override
  String get tt_emb_editchangecolor => 'Paleta de hilos';

  @override
  String get tt_emb_edittextedit => 'Editar patrones de caracteres';

  @override
  String get tt_emb_editalign => 'Alineación';

  @override
  String get tt_emb_editstippling => 'Punteado';

  @override
  String get tt_emb_editoutline => 'Extracción de contornos';

  @override
  String get tt_emb_editorder => 'Orden de bordado';

  @override
  String get tt_emb_editnotsew => 'Sin ajuste de costura';

  @override
  String get tt_emb_textsize => 'Tamaño';

  @override
  String get tt_emb_textarray => 'Adorno';

  @override
  String get tt_emb_textspacing => 'Espacio entre caracteres';

  @override
  String get tt_emb_textalign => 'Alineación';

  @override
  String get tt_emb_embfootw => 'Comprobar el punto de entrada de la aguja';

  @override
  String get tt_emb_emb_projectorsetting => 'Ajustes del proyector';

  @override
  String get tt_emb_embprojector => 'Proyector';

  @override
  String get tt_emb_embmove => 'Mover';

  @override
  String get tt_emb_embrotate => 'Girar';

  @override
  String get tt_emb_embbasting => 'Hilvanado';

  @override
  String get tt_emb_embsnowman => 'Colocación del bordado';

  @override
  String get tt_emb_embonecolorsew => 'Bordado ininterrumpido';

  @override
  String get tt_emb_embcolorsorting => 'Clasificación por color';

  @override
  String get tt_emb_embconnectsew => 'Conexión de patrones';

  @override
  String get tt_emb_embframemove => 'Cambio de posición de la bastidor: El marco se moverá temporalmente al centro.';

  @override
  String get tt_emb_embmemory => 'Memoria';

  @override
  String get tt_emb_embmasktrace => 'Área de trazo';

  @override
  String get tt_emb_embstartposition => 'Punto inicial';

  @override
  String get tt_emb_embneedlenumber => 'Adelante/atrás';

  @override
  String get tt_emb_embfbcamera => 'Vista de la cámara';

  @override
  String get tt_emb_embthreadcutting => 'Corte/tensión';

  @override
  String get tt_emb_embcolorbar => 'Un color/todos los colores para la barra de progreso';

  @override
  String get tt_emb_patterninfo => 'Información del patrón';

  @override
  String get tt_emb_previewsim => 'Simulador de puntadas';

  @override
  String get tt_emb_sewtrim_endcolor => 'Corte al final del color';

  @override
  String get tt_emb_sewtrim_jumpstitch => 'Corte de salto de puntada';

  @override
  String get tt_emb_previewframe => 'Vista previa del bastidor de bordado';

  @override
  String get tt_emb_size_normalstb => 'Cambia el tamaño del patrón mientras mantiene el número de puntadas/la densidad del hilo';

  @override
  String get tt_emb_edit_border_vert => 'Repetir/eliminar patrón verticalmente';

  @override
  String get tt_emb_edit_border_horiz => 'Repetir/eliminar patrón horizontalmente';

  @override
  String get tt_emb_edit_border_dividervert => 'Cortar patrón verticalmente';

  @override
  String get tt_emb_edit_border_dividehoriz => 'Cortar patrón horizontalmente';

  @override
  String get tt_emb_edit_border_threadmark => 'Marcas de hilo';

  @override
  String get tt_emb_edit_border_reset => 'Borrado';

  @override
  String get tt_emb_emb_rotate_reset => 'Borrado';

  @override
  String get tt_emb_edit_rotate_reset => 'Borrado';

  @override
  String get tt_emb_camera_rotate_reset => 'Borrado';

  @override
  String get tt_emb_edit_font_spacing_reset => 'Borrado';

  @override
  String get tt_emb_edit_align_reset => 'Borrado';

  @override
  String get tt_emb_edit_size_reset => 'Borrado';

  @override
  String get tt_emb_edit_order_reset => 'Borrado';

  @override
  String get tt_emb_quiltborder_color_reset => 'Borrado';

  @override
  String get tt_emb_edit_color_reset => 'Borrado';

  @override
  String get tt_emb_photositich_size_change_reset => 'Borrado';

  @override
  String get tt_emb_edit_projlcd_switch_fb_reset => 'Borrado';

  @override
  String get tt_emb_edit_projlcd_align_reset => 'Borrado';

  @override
  String get tt_emb_edit_projlcd_border_reset => 'Borrado';

  @override
  String get tt_emb_edit_projlcd_rotate_reset => 'Borrado';

  @override
  String get tt_emb_edit_projlcd_size_reset => 'Borrado';

  @override
  String get tt_mdc_paint_rotate_reset => 'Borrado';

  @override
  String get tt_mdc_paint_size_input_reset => 'Borrado';

  @override
  String get tt_mdc_paint_size_reset => 'Borrado';

  @override
  String get tt_emb_newapplique_color_selectall => 'Seleccionar todo';

  @override
  String get tt_emb_newapplique_color_selectnone => 'Deseleccionar todo';

  @override
  String get tt_emb_color_selectall => 'Seleccionar un solo color/todos los colores';

  @override
  String get tt_emb_colorcolorshuffling => 'Color Shuffling(Mezcla de colores)';

  @override
  String get tt_emb_colorvisualizer => 'Color Visualizer';

  @override
  String get tt_emb_editselectall => 'Seleccionar todo';

  @override
  String get tt_emb_editdeselectall => 'Deseleccionar todo';

  @override
  String get tt_emb_infoprintimage => 'Combinado con la imagen impresa';

  @override
  String get tt_emb_infooutputfiles => 'Se copian 3 archivos PDF (para tela imprimible/transferencia para planchar/colocación) en el dispositivo USB.';

  @override
  String get tt_emb_filemanager => 'Administrador de archivos';

  @override
  String get tt_emb_filemanager_selectall => 'Seleccionar todo';

  @override
  String get tt_emb_filemanager_selectnone => 'Deseleccionar todo';

  @override
  String get tt_emb_filemanager_delete => 'Eliminar';

  @override
  String get tt_emb_filemanager_memory => 'Guardar los patrones seleccionados en la memoria de la máquina.';

  @override
  String get tt_emb_easystippling_stippling => 'Patrón punteado';

  @override
  String get tt_emb_easystippling_echo => 'Patrón de acolchado repetido';

  @override
  String get tt_emb_easystippling_decorativefill => 'Patrón de relleno decorativo';

  @override
  String get tt_emb_quitlsash_startpoint => 'Punto inicial del proyecto';

  @override
  String get tt_emb_quitlsash_endtpoint => 'Punto final del proyecto';

  @override
  String get tt_emb_connect_migimawari => 'La segunda posición del patrón se mueve hacia la derecha';

  @override
  String get tt_emb_connect_hidarimawari => 'La segunda posición del patrón se mueve hacia la izquierda';

  @override
  String get tt_emb_connect_rotate => 'Girar';

  @override
  String get tt_emb_quiltborder_save => 'Memoria';

  @override
  String get tt_mdc_pantool => 'Herramienta de mano';

  @override
  String get tt_mdc_scanmenu => 'Se pueden crear patrones personalizados utilizando imágenes escaneadas o archivos de datos de imagen.';

  @override
  String get tt_mdc_datacall => 'Recuperar los datos de dibujo del patrón (.pm9)';

  @override
  String get tt_mdc_linetool => 'Herramienta de línea';

  @override
  String get tt_mdc_lineproperty => 'Propiedades de la línea';

  @override
  String get tt_mdc_linespoit => 'Herramienta cuentagotas para la línea';

  @override
  String get tt_mdc_linepouring => 'Herramienta cubo para la línea';

  @override
  String get tt_mdc_brushtool => 'Herramienta de pincel';

  @override
  String get tt_mdc_brushproperty => 'Propiedades de la región';

  @override
  String get tt_mdc_brushspoit => 'Herramienta cuentagotas para la zona';

  @override
  String get tt_mdc_brushpouring => 'Herramienta cubo para la zona';

  @override
  String get tt_mdc_painteraser => 'Goma de borrar';

  @override
  String get tt_mdc_paintstamp => 'Formas del sello';

  @override
  String get tt_mdc_paintsize => 'Tamaño';

  @override
  String get tt_mdc_paintrotate => 'Girar';

  @override
  String get tt_mdc_paintflip => 'Efecto espejo';

  @override
  String get tt_mdc_paintduplicate => 'Duplicar';

  @override
  String get tt_mdc_paintcut => 'Cortar';

  @override
  String get tt_mdc_paintpaste => 'Pegar';

  @override
  String get tt_mdc_memory => 'Guarde los datos de dibujo del patrón (.pm9)';

  @override
  String get tt_mdc_select => 'Seleccionar';

  @override
  String get tt_mdc_redo => 'Rehacer';

  @override
  String get tt_mdc_undo => 'Deshacer';

  @override
  String get tt_mdc_allclear => 'Borrar todo';

  @override
  String get tt_mdc_lineopen => 'Línea a mano alzada con el extremo abierto';

  @override
  String get tt_mdc_lineclose => 'Línea a mano alzada cerrando el extremo automáticamente';

  @override
  String get tt_mdc_lineline => 'Línea recta con un solo trazo';

  @override
  String get tt_mdc_linepolygonal => 'Forma poligonal';

  @override
  String get tt_mdc_stitchzigzag => 'Puntada en zigzag';

  @override
  String get tt_mdc_stitchrunning => 'Puntada recta';

  @override
  String get tt_mdc_stitchtriple => 'Puntada triple';

  @override
  String get tt_mdc_stitchcandle => 'Puntada de candlewick';

  @override
  String get tt_mdc_stitchchain => 'Punto de cadeneta';

  @override
  String get tt_mdc_stitchestitch => 'Puntada en E';

  @override
  String get tt_mdc_stitchvsitich => 'Puntada en V';

  @override
  String get tt_mdc_stitchmotif => 'Puntadas ornamentales';

  @override
  String get tt_mdc_stitchnnotsew => 'Línea sin puntada';

  @override
  String get tt_mdc_stitchzigzaglowdensity => 'Puntada en zigzag para aplique';

  @override
  String get tt_mdc_regiontatami => 'Patrón de puntadas de relleno';

  @override
  String get tt_mdc_regionstippling => 'Patrón punteado';

  @override
  String get tt_mdc_regiondecorativefill => 'Patrones de relleno decorativos';

  @override
  String get tt_mdc_regionnotsew => 'Sin puntadas';

  @override
  String get tt_mdc_stamp1 => 'Formas básicas';

  @override
  String get tt_mdc_stamp2 => 'Formas cerradas';

  @override
  String get tt_mdc_stamp3 => 'Formas abiertas';

  @override
  String get tt_mdc_stamp4 => 'Contornos guardados';

  @override
  String get tt_mdc_stamp5 => 'Áreas de bordado del bastidor';

  @override
  String get tt_mdc_stamp6 => 'Cortar contornos';

  @override
  String get tt_mdc_select_rectangle => 'Selección en forma de cuadrado';

  @override
  String get tt_mdc_select_continuousrectangle => 'Selección en forma de polígono';

  @override
  String get tt_mdc_select_free => 'Selección de curva libre';

  @override
  String get tt_mdc_select_auto => 'Selección automática';

  @override
  String get tt_mdc_select_all => 'Seleccionar todo';

  @override
  String get tt_mdc_memory_drawemb => 'Guarde los datos de dibujo del patrón (.pm9) y los datos de bordado (.phx).';

  @override
  String get tt_mdc_embset_pantool => 'Herramienta de mano';

  @override
  String get tt_mdc_embset_patterninfo => 'Información del patrón';

  @override
  String get tt_mdc_embset_realpreview => 'Vista previa';

  @override
  String get tt_mdc_embset_projector => 'Proyector';

  @override
  String get tt_mdc_embset_projectorsetting => 'Ajustes del proyector';

  @override
  String get tt_mdc_zigzagwidth => 'Anchura de la puntada zigzag';

  @override
  String get tt_mdc_zigzagdensity => 'Densidad';

  @override
  String get tt_mdc_runpitch => 'Utilizar puntadas';

  @override
  String get tt_mdc_running_undersew => 'Costura de refuerzo';

  @override
  String get tt_mdc_candlewicksize => 'Tamaño';

  @override
  String get tt_mdc_candlewickspacing => 'Espacio';

  @override
  String get tt_mdc_chainsize => 'Tamaño';

  @override
  String get tt_mdc_chainthickness => 'Grosor';

  @override
  String get tt_mdc_estitchwidth => 'Anchura de la puntada';

  @override
  String get tt_mdc_estitchspacing => 'Espacio';

  @override
  String get tt_mdc_estitchthickness => 'Grosor';

  @override
  String get tt_mdc_estitchflip => 'Invertir';

  @override
  String get tt_mdc_vstitchwidth => 'Anchura de la puntada';

  @override
  String get tt_mdc_vstitchspacing => 'Espacio';

  @override
  String get tt_mdc_vstitchthickness => 'Grosor';

  @override
  String get tt_mdc_vstitchflip => 'Invertir';

  @override
  String get tt_mdc_motifstitchsize => 'Tamaño';

  @override
  String get tt_mdc_motifstitchspacing => 'Espacio';

  @override
  String get tt_mdc_motifstitchflip => 'Invertir';

  @override
  String get tt_mdc_zigzagwidth_2 => 'Anchura de la puntada zigzag';

  @override
  String get tt_mdc_zigzagdensity_2 => 'Densidad';

  @override
  String get tt_mdc_tatamiderection => 'Dirección';

  @override
  String get tt_mdc_tatamidensity => 'Densidad';

  @override
  String get tt_mdc_tatamipullconpen => 'Compensación de tirones';

  @override
  String get tt_mdc_tatamiundersewing => 'Costura de refuerzo';

  @override
  String get tt_mdc_stiprunpitch => 'Utilizar puntadas';

  @override
  String get tt_mdc_stipspacing => 'Espacio';

  @override
  String get tt_mdc_stipdistance => 'Distancia';

  @override
  String get tt_mdc_stipsingletriple => 'Puntada sencilla/triple';

  @override
  String get tt_mdc_decofillsize => 'Tamaño';

  @override
  String get tt_mdc_decofilldirection => 'Dirección';

  @override
  String get tt_mdc_decofilloutline => 'Contorno para reducir el corte de hilo';

  @override
  String get tt_mdc_decofillrandomshift => 'Desplazamiento aleatorio';

  @override
  String get tt_mdc_decofillpositionoffset => 'Desplazamiento de la posición';

  @override
  String get tt_mdc_decofillthickness1 => 'Grosor';

  @override
  String get tt_mdc_decofillthickness3 => 'Grosor';

  @override
  String get tt_mdc_decofillthickness1_2 => 'Individual-doble';

  @override
  String get tt_mdc_decofillthickness2_3 => 'Doble-triple';

  @override
  String get tt_mdc_stitchlink => 'Selecciona objetos con los mismos ajustes de puntada a la vez';

  @override
  String get tt_mdc_fill_linereading => 'Los perímetros y las áreas lineales se convierten en contornos. Seleccione el grosor del contorno.';

  @override
  String get tt_mdc_fill_linecolor => 'Los contornos extraídos con el color especificado se convierten en atributos de línea.';

  @override
  String get tt_emb_photostitch_backremoval => 'Eliminar fondo';

  @override
  String get tt_emb_photostitch_framing => 'Recortar';

  @override
  String get tt_emb_photostitch_fittoframe => 'Ajustar al bastidor';

  @override
  String get tt_emb_photostitch_backremoval_scopeplus => 'Añadir una nueva zona de recorte:Marque con una línea la zona que desea recortar.';

  @override
  String get tt_emb_photostitch_backremoval_scopeminus => 'Limpiar la zona de recorte:Marque la zona que no desee recortar con una línea.';

  @override
  String get tt_emb_photostitch_backremoval_erase => 'Elimine la línea de dibujo especificada.';

  @override
  String get tt_emb_photostitch_backremoval_trash => 'Elimine todas las líneas de dibujo.';

  @override
  String get tt_emb_photostitch_backremoval_blind => 'Mostrar/ocultar todas las líneas dibujadas con los lápices.';

  @override
  String get tt_emb_photostitch_styleusecolor => 'ON: Utilizar los colores de la imagen de estilo/ OFF: Utilizar los colores de la foto original';

  @override
  String get tt_emb_photostitch_colortune => 'Ajuste de color';

  @override
  String get tt_emb_photostitch_colorlis_allselect => 'Mantener/Borrar todos los colores del hilo de la lista de colores';

  @override
  String get tt_emb_photostitch_colorlis_add => 'Añada un color a la lista de colores';

  @override
  String get tt_emb_photostitch_colorlis_remove => 'Eliminar el color seleccionado de la lista de colores';

  @override
  String get tt_emb_photostitch_pantool => 'Herramienta de mano';

  @override
  String get tt_emb_photostitch_memory => 'Memoria';

  @override
  String get tt_emb_edit_projectorsetting => 'Ajustes del proyector';

  @override
  String get tt_emb_edit_projector => 'Proyector';

  @override
  String get tt_settings_reset_3type => 'Restablecer los ajustes (Costura/General/Bordado)';

  @override
  String get tt_settings_screenimage_usb => 'Guardar una imagen de la pantalla de ajustes en un dispositivo USB';

  @override
  String get tt_camera_emb_screenshot => 'Guarda una imagen de la cámara en el dispositivo USB.';

  @override
  String get tt_camera_emb_grid => 'Mostrar/ocultar cuadrícula';

  @override
  String get tt_camera_emb_needlepoint => 'Mostrar/ocultar el punto de entrada de la aguja';

  @override
  String get tt_camera_util_screenshot => 'Guarda una imagen de la cámara en el dispositivo USB.';

  @override
  String get tt_camera_util_grid => 'Mostrar/ocultar cuadrícula';

  @override
  String get tt_camera_util_needlepoint => 'Mostrar/ocultar el punto de entrada de la aguja';
}
