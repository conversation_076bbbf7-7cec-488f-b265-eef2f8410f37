import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

part 'surface_tatami_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

///
/// 白い背景に黒いテキスト
///
const TextStyle blackTextWhiteBackground =
    TextStyle(color: Colors.white, backgroundColor: Colors.black);

///
/// 黒い背景に白いテキスト
///
const TextStyle whiteTextBlackBackground =
    TextStyle(color: Colors.black, backgroundColor: Colors.white);

@freezed
class SurfaceTatamiState with _$SurfaceTatamiState {
  const factory SurfaceTatamiState({
    @Default(false) bool directionSettingPopup,
    @Default(false) bool densitySettingPopup,
    @Default(false) bool pullCompensationSettingPopup,
    @Default(false) bool underSewingSettingPopup,
    @Default(true) bool autoButtonState,
    @Default(false) bool isUnderSewingValeSame,
    @Default(MDCIsOnOff.mdcIs_off) MDCIsOnOff underSewingState,
    @Default("") String directionValue,
    @Default("") String densityValue,
    @Default("") String pullCompensationValue,
  }) = _SurfaceTatamiState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class SurfaceTatamStateViewInterface
    extends ViewModel<SurfaceTatamiState> {
  SurfaceTatamStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// タタミ縫いの方向ボタンをクリックする
  ///
  void onDirectionButtonClicked(context);

  ///
  /// 糸密度ボタンをクリックする
  ///
  void onDensityButtonClicked(context);

  ///
  /// 縫い縮み設定ボタンをクリックする
  ///
  void onPullCompensationButtonClicked(context);

  ///
  /// 下打ち有無ボタンをクリックする
  ///
  void openUnderSewingSettingPopup(context);

  ///
  /// タタミ縫いの方向Auto状態の判断
  ///
  bool isDirectionAutoState();

  ///
  /// タタミ縫いの方向のテキストスタイルを取得する
  ///
  bool getDirectionDefault();

  ///
  /// 糸密度のテキストスタイルを取得する
  ///
  bool getDensityDefault();

  ///
  /// 縫い縮み設定(ステッチの長さ補正)のテキストスタイルを取得する
  ///
  bool getPullCompensationDefault();

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
