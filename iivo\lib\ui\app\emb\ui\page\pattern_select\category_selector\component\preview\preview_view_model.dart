import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../../model/frame_model.dart';
import '../../../../../../../../../model/preview_area_size_model.dart';
import '../../../../../../model/pattern_model.dart';
import '../../../../../../model/preview_model.dart';
import '../../../../../../model/scan_model.dart';
import '../../category_selector_view_interface.dart';
import '../../category_selector_view_model.dart';
import 'preview_view_interface.dart';

typedef PatternViewDisplayInfo = PatternDisplayInfo;
typedef EmbBorderViewDisplayInfo = EmbBorderDisplayInfo;
typedef EmbGroupViewDisplayInfo = EmbGroupDisplayInfo;
typedef EmbPatternViewDisplayInfo = EmbPatternDisplayInfo;

/// 10mm定義
const int _gridLine10MmValue = 10;

/// 25mm定義
const int _gridLine25MmValue = 25;

/// フレームの破線の一段の長さ
const double _dashedLineLength = 2.0;

/// フレームの破線の間隔
const double _dashedLineSpace = 2.0;

/// 黒点行列の間隔
const double _blackPointsSpace = 10.0;

/// プレビュー中心点
final Offset originalCenter = selectPreviewSize / 2;

/// 1mmに対応する画素数
double _pixelOfOneMm = selectPreviewSize.dx / frame297x465MmSize.dx;

/// 1本指
const int _oneFinger = 1;

final previewViewModelProvider =
    StateNotifierProvider.autoDispose<PreviewViewModelInterface, PreviewState>(
  (ref) => PreviewViewModel(ref),
);

class PreviewViewModel extends PreviewViewModelInterface {
  PreviewViewModel(Ref ref)
      : super(
          PreviewState(
            backgroundColor: Colors.transparent,
            maskColor: const Color.fromARGB(255, 235, 0, 0),
            gridTypeIndex: 0,
            gridColor: Colors.transparent,
            frameDrawPath: Path(),
            frameColor: Colors.transparent,
            blackPoints: [],
            backgroundImage: null,
          ),
          ref,
        ) {
    update();
  }

  @override
  void update() {
    /// view更新
    state = state.copyWith(
      backgroundColor: PreviewModel().getEmbroideryBackgroundColor(),
      gridTypeIndex: DeviceLibrary().apiBinding.getGrid().gridType.index,
      frameDrawPath: _getFrameDrawPath(),
      gridColor: PreviewModel().getEmbroideryGridColor(),
      frameColor: PreviewModel().getEmbroideryFrameColor(),
      maskColor: const Color.fromARGB(255, 235, 0, 0),
      blackPoints: _getBlackPoints(),
      backgroundImage: _getScanBackGroundImage(),
    );
  }

  @override
  double get getMagnification =>
      PatternModel().selectedZoomScaleInSelectPage / zoomList.first;

  @override
  Offset get getPreviewSize => selectPreviewSize;

  @override
  List<double> getGridVerticalList() {
    List<double> verticalList = [];
    double xOffset = originalCenter.dx;
    int mmValue = state.gridTypeIndex == EmbGridType.embGridGridLine10.index
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    /// 中心点の左端の線の位置を計算するには
    xOffset -= _pixelOfOneMm * mmValue;
    while (xOffset >= 0) {
      verticalList.add(xOffset);
      xOffset -= _pixelOfOneMm * mmValue;
    }

    xOffset = originalCenter.dx;

    /// 中心点の右端の線の位置を計算するには
    xOffset += _pixelOfOneMm * mmValue;
    while (xOffset <= selectPreviewSize.dx) {
      verticalList.add(xOffset);
      xOffset += _pixelOfOneMm * mmValue;
    }

    return verticalList;
  }

  @override
  List<double> getGridHorizontalList() {
    List<double> horizontalList = [];
    double yOffset = originalCenter.dy;
    int mmValue = state.gridTypeIndex == EmbGridType.embGridGridLine10.index
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    /// 中心点上端の線の位置を計算するには
    yOffset -= _pixelOfOneMm * mmValue;
    while (yOffset >= 0) {
      horizontalList.add(yOffset);
      yOffset -= _pixelOfOneMm * mmValue;
    }

    yOffset = originalCenter.dy;

    /// 中心点の下端の線の位置を計算するには
    yOffset += _pixelOfOneMm * mmValue;
    while (yOffset <= selectPreviewSize.dy) {
      horizontalList.add(yOffset);
      yOffset += _pixelOfOneMm * mmValue;
    }

    return horizontalList;
  }

  ///
  /// 2本指でZoom倍率を拡大する前のZoom値バックアップ
  ///
  int _selectedZoomScaleBack = PatternModel().selectedZoomScaleInSelectPage;

  @override
  void dargPreviewStart(ScaleStartDetails details) {
    _selectedZoomScaleBack = PatternModel().selectedZoomScaleInSelectPage;
  }

  @override
  void dargPreview(ScaleUpdateDetails details) {
    if (details.pointerCount <= _oneFinger) {
      return;
    }

    int index = zoomList.indexOf(_selectedZoomScaleBack);
    int step = _getDoubleFingerStep(details.scale);
    index = (index + step).clamp(0, zoomList.length - 1);

    /// 必要でない場合は更新しない
    if (PatternModel().selectedZoomScaleInSelectPage != zoomList[index]) {
      /// Model更新
      PatternModel().selectedZoomScaleInSelectPage = zoomList[index];

      /// View更新
      update();

      /// 他のページへの更新の通知
      ref
          .read(categorySelectorViewModelProvider.notifier)
          .updateTopPageByChild(CategorySelectorModuleType.preview);
    } else {
      /// Do noting
    }
  }

  ///
  /// 二重指拡張時のScale値変換
  /// 拡大時、scale範囲は1より大きく、scale値が大きいほど拡大する(1精度)
  /// 縮小時、scale範囲0 ~ 1、scale値が小さいほど縮小されます(0.1精度)
  ///
  int _getDoubleFingerStep(double scale) {
    int step = 0;
    if (scale > 1) {
      /// 例：Scale=1.2   ===>   Step = 1；
      /// 例：Scale=3.2   ===>   Step = 3；
      step = scale.toInt();
    } else if (scale >= 0 && scale < 1) {
      /// 例：Scale=0.8   ===>   Step = -2；
      /// 例：Scale=0.5   ===>   Step = -5；
      step = -((1 - scale) * 10).toInt();
    } else {
      step = 0;
    }

    return step;
  }

  ///
  /// 選択した枠のプレビュー表示データを取得します
  ///
  Path _getFrameDrawPath() {
    List<FrameSizeAndArea>? frameSizeAndAreaList = getFrameDisplaySizeAndArea(
        DeviceLibrary().apiBinding.getEmbroideryFrameDisplay().frameDispType);
    assert(frameSizeAndAreaList != null, "対応するサイズのボックスが見つかりません");

    Path drawPath = Path();
    Path basePath = Path();

    if (frameSizeAndAreaList!.length > 1) {
      basePath =
          _get60x20FrameBasePath(frameSizeAndAreaList: frameSizeAndAreaList);
    } else {
      double width = PreviewModel.convertMmToPixels(
          value: frameSizeAndAreaList.first.width, pixelOfOneMm: _pixelOfOneMm);
      double height = PreviewModel.convertMmToPixels(
          value: frameSizeAndAreaList.first.height,
          pixelOfOneMm: _pixelOfOneMm);
      double startX = PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList.first.left,
              pixelOfOneMm: _pixelOfOneMm) +
          originalCenter.dx;
      double startY = PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList.first.top,
              pixelOfOneMm: _pixelOfOneMm) +
          originalCenter.dy;

      Rect rect = Rect.fromLTWH(startX, startY, width, height);
      basePath.moveTo(rect.left, rect.top);
      basePath.lineTo(rect.left, rect.bottom);
      basePath.lineTo(rect.right, rect.bottom);
      basePath.lineTo(rect.right, rect.top);
      basePath.lineTo(rect.left, rect.top);
    }

    /// フレームの破線の一段の長さ
    double dashWidth = _dashedLineLength / getMagnification;
    double dashSpace = _dashedLineSpace / getMagnification;

    /// 描画Pathを計算する
    double distance = 0.0;
    for (PathMetric pathMetric in basePath.computeMetrics()) {
      while (distance < pathMetric.length) {
        drawPath.addPath(
          pathMetric.extractPath(distance, distance + dashWidth),
          Offset.zero,
        );
        distance += dashWidth;
        distance += dashSpace;
      }
    }
    return drawPath;
  }

  ///
  /// 60*20枠のプレビューにはデータが表示されま
  ///
  Path _get60x20FrameBasePath(
      {required List<FrameSizeAndArea> frameSizeAndAreaList}) {
    Path basePath = Path();

    /// List順：60*20 mm、50*30 mm、30*40 mm
    List<Rect> rectList = List.generate(
      frameSizeAndAreaList.length,
      (index) => Rect.fromLTWH(
          PreviewModel.convertMmToPixels(
                  value: frameSizeAndAreaList[index].left,
                  pixelOfOneMm: _pixelOfOneMm) +
              originalCenter.dx,
          PreviewModel.convertMmToPixels(
                  value: frameSizeAndAreaList[index].top,
                  pixelOfOneMm: _pixelOfOneMm) +
              originalCenter.dy,
          PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList[index].width,
              pixelOfOneMm: _pixelOfOneMm),
          PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList[index].height,
              pixelOfOneMm: _pixelOfOneMm)),
    );

    /// 1番目と2番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*40 mm
    rectList.insert(
      1,
      Rect.fromPoints(
        Offset(rectList[1].left, rectList.first.top),
        Offset(rectList[1].right, rectList.first.bottom),
      ),
    );

    /// 2番目と3番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*30 mm、30*40 mm
    rectList.insert(
      3,
      Rect.fromPoints(
        Offset(rectList.last.left, rectList[2].top),
        Offset(rectList.last.right, rectList[2].bottom),
      ),
    );

    /// 60*20 mmの左上点から描画
    basePath.moveTo(rectList.first.left, rectList.first.top);

    /// Rectの左上点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].left, rectList[index].top);
    }

    /// Rectの右上点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].right, rectList[index].top);
    }

    /// Rectの右下点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].right, rectList[index].bottom);
    }

    /// Rectの左下点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].left, rectList[index].bottom);
    }

    /// 接続開始点
    basePath.lineTo(rectList.first.left, rectList.first.top);

    return basePath;
  }

  ///
  /// 黒点行列の表示データを取得します
  ///
  List<Offset> _getBlackPoints() {
    double space = _blackPointsSpace / getMagnification;
    List<Offset> points = [];

    /// 黒点行列は以下の場合には表示しない
    if (PatternModel().selectedZoomScaleInSelectPage == zoomList.first ||
        [
          EmbGridType.embGridCenterLine,
          EmbGridType.embGridGridLine10,
          EmbGridType.embGridGridLine_25
        ].contains(DeviceLibrary().apiBinding.getGrid())) {
      return points;
    } else {
      for (double y = space; y < selectPreviewSize.dy; y += space) {
        for (double x = space; x < selectPreviewSize.dx; x += space) {
          points.add(Offset(x, y));
        }
      }
    }

    return points;
  }

  ///
  /// スキャンした背景画像を取得する
  ///
  ///TODO:背景画像はSelect画面に表示されます
  ///http://apngoap306.ap.brothergroup.net:8080/jira/browse/PHBSH-2716
  Widget? _getScanBackGroundImage() {
    if (ScanModel().hasBackgroundImage == false ||
        ScanModel().getBackgroundShowStatus() == false) {
      return null;
    }

    if (ScanModel.createSketchesImage() == null) {
      return null;
    }

    return Opacity(
      opacity: ScanModel().backgroundDensityValue,
      child: Image.memory(
        ScanModel.createSketchesImage()!,
        scale: embPreviewSizeDot.dx / selectPreviewSize.dx,
      ),
    );
  }
}
