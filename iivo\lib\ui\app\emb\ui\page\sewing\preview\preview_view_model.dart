import 'dart:ffi';
import 'dart:math';
import 'dart:ui';

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../model/frame_model.dart';
import '../../../../../../../model/preview_area_size_model.dart';
import '../../../../../../../model/projector_model.dart';
import '../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/preview_model.dart';
import '../../../../model/scan_model.dart';
import '../../../../model/sewing_model.dart';
import '../../common_component/function_provider/move_function_provider/move_function_interface.dart';
import '../../common_component/function_provider/move_function_provider/move_function_provider.dart';
import '../../common_component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../sewing_view_interface.dart';
import '../sewing_view_model.dart';
import 'preview_view_interface.dart';

typedef PatternViewDisplayInfo = PatternDisplayInfo;
typedef EmbBorderViewDisplayInfo = EmbBorderDisplayInfo;
typedef EmbGroupViewDisplayInfo = EmbGroupDisplayInfo;
typedef PatternViewRedPointInfo = RedPointInfo;
typedef ThreadMarkDisplayInfo = ThreadMarkInfo;
typedef EmbPatternViewDisplayInfo = EmbPatternDisplayInfo;

/// 移動量　0.5mm単位　1で0.5mm移動
const double _oneMoveStepInMm = 0.5;

/// Quilt移動量　0.2mm単位　1で0.2mm移動
const double _oneMoveStepInMmQuilt = 0.2;

/// 10mm定義
const int _gridLine10MmValue = 10;

/// 25mm定義
const int _gridLine25MmValue = 25;

/// フレームの破線の一段の長さ
const double _dashedLineLength = 2.0;

/// フレームの破線の間隔
const double _dashedLineSpace = 2.0;

/// 角度の定義
const int _angle360 = 360;
const int _angle180 = 180;
const int _angle90 = 90;

/// プレビュー中心点
final Offset _originalCenter = embPreviewSizeDot / 2;

final previewViewModelProvider =
    StateNotifierProvider.autoDispose<PreviewViewModelInterface, PreviewState>(
  (ref) => PreviewViewModel(ref),
);

class PreviewViewModel extends PreviewViewModelInterface {
  PreviewViewModel(this._ref)
      : super(
          PreviewState(
            patternDisplayInfoList: [],
            backgroundColor: Colors.transparent,
            maskColor: const Color.fromARGB(255, 235, 0, 0),
            gridTypeIndex: 0,
            gridColor: Colors.transparent,
            frameDrawPath: Path(),
            frameColor: Colors.transparent,
            isRedPointOverPreview: false,
            needlePointerCenterOffset: null,
            backgroundImage: null,
            threadMarkInfoList: [],
            isProjectorON: false,
            maskDisplayInfo: null,
            rotateRedPoint: null,
            rotateCenter: const Offset(0, 0),
          ),
          _ref,
        ) {
    _openListen();

    /// ページに入るときは、現在接続されている刺繍フレームを読み取り、回転と移動の制限範囲を設定します。
    FrameSize frameSize = _ref.read(appDisplayEmbStateProvider
        .select((value) => value.embSetting.ref.frameSize));
    _needleCurrent = _ref.read(appDisplayEmbStateProvider
        .select((value) => value.sewingInfo.ref.needleCurrent));
    _updateZoomPreviewRectList(frameSize);
  }

  /// providerのref
  final AutoDisposeStateNotifierProviderRef _ref;

  /// 1画素に対応するmm
  final double _mmOfOnePixel = frame297x465MmSize.dx / embPreviewSizeDot.dx;

  /// 1mmに対応する画素数
  final double _pixelOfOneMm = embPreviewSizeDot.dx / frame297x465MmSize.dx;

  /// ドットの使用
  bool _isUsingRedPoint = false;

  ///
  /// 前回の更新時に、画面上で指がドラッグした座標を記録する。
  /// 更新されるたびに、この座標は最新のドラッグ位置に更新される。
  ///
  /// 例えば: Rotate Point Drag、Move Drag
  ///
  Offset _lastDragPosition = const Offset(0, 0);

  /// UIでのプレビューの実際のスコープ
  final List<Rect> _zoomPreviewRectList = [
    Rect.fromCenter(
      center: _originalCenter,
      width: embPreviewSizeDot.dx,
      height: embPreviewSizeDot.dy,
    )
  ];

  @override
  void update() {
    var (
      patternDisplayInfoList: patternDisplayInfoList,
      rotateMask: rotateMask,
      quiltMask: quiltMask,
      rotateCenter: rotateCenter,
    ) = _getPatternDisplayInfoList();
    PreviewModel previewModel = PreviewModel();

    Mask? maskDisplayInfo;
    RedPointDisplayInfo? rotateRedPoint;
    if (rotateMask != null) {
      maskDisplayInfo = rotateMask;
      rotateRedPoint =
          previewModel.getSewingRotateRedPointInfo(mask: rotateMask);
    } else {
      /// Do noting
    }

    /// view更新
    state = state.copyWith(
      patternDisplayInfoList: patternDisplayInfoList,
      backgroundColor: previewModel.getEmbroideryBackgroundColor(),
      gridTypeIndex: DeviceLibrary().apiBinding.getGrid().gridType.index,
      frameDrawPath: _getFrameDrawPath(),
      gridColor: previewModel.getEmbroideryGridColor(),
      frameColor: previewModel.getEmbroideryFrameColor(),
      isRedPointOverPreview: maskDisplayInfo == null
          ? false
          : _isRedPointOverPreview(
              viewCenter: _originalCenter,
              topLeft: maskDisplayInfo.topLeft,
              bottomRight: maskDisplayInfo.bottomRight,
            ),
      backgroundImage: _getScanBackGroundImage(),
      threadMarkInfoList: PreviewModel.getThreadMarkInfoList(
        centerPoint: _originalCenter,
        pixelOfOneMm: _pixelOfOneMm,
        patternDisplayInfoList: patternDisplayInfoList,
      ),
      isProjectorON: ProjectorModel().embProjector.isEmbProjectorViewOpen,
      maskDisplayInfo: maskDisplayInfo,
      rotateRedPoint: rotateRedPoint,
      rotateCenter: rotateCenter,
      dragAngle: null,
      quiltMask: quiltMask,
      needlePointerCenterOffset:
          SewingModel().getCurrentNeedlePosition(pixelOfOneMm: _pixelOfOneMm),
    );
  }

  @override
  void updateByRotateLongPress() {
    double currentAngle = PreviewModel().uiRotateAngle;
    List<PatternDisplayInfo> newPatternDisplayInfoList = [];

    for (int index = 0; index < _angle360; index++) {
      newPatternDisplayInfoList.clear();
      newPatternDisplayInfoList = [];
      bool isAllSuccess = true;
      for (var patternDisplayInfo in _patternDisplayInfoListBack) {
        var newPatternInfo = PreviewModel().getNewPatternDisplayInfoInRotate(
          angle: currentAngle,
          patternDisplayInfo: patternDisplayInfo,
          rotateCenter: state.rotateCenter,
          pixelOfOneMm: _pixelOfOneMm,
        );
        bool isSuccess = true;
        for (var zoomPreviewRect in _zoomPreviewRectList) {
          if (zoomPreviewRect
                      .contains(newPatternInfo.maskDisplayInfo.topLeft) ==
                  false ||
              zoomPreviewRect
                      .contains(newPatternInfo.maskDisplayInfo.bottomRight) ==
                  false) {
            isSuccess = false;
            continue;
          } else {
            isSuccess = true;
            break;
          }
        }

        if (isSuccess == false) {
          isAllSuccess = false;
          break;
        } else {
          newPatternDisplayInfoList.add(newPatternInfo);
        }
      }

      if (isAllSuccess == false) {
        /// 使用する赤い点が回転し続けると、対応する角度に回転しても更新されません。
        if (_isUsingRedPoint == true) {
          return;
        } else {
          /// それ以外の場合は、ボタンを押し続けて次の移動角度を計算する必要があります。
          /// 回転方向を確認します。
          currentAngle = PreviewModel().isRotatingForward
              ? (currentAngle + 1) % _angle360
              : (currentAngle - 1) % _angle360;
          continue;
        }
      } else {
        break;
      }
    }

    /// Model更新
    PreviewModel().uiRotateAngle = currentAngle;

    final quiltMaskPath =
        _getQuiltMoveAndRotateMask(PatternModel().getCurrentGroup());

    state = state.copyWith(
      dragAngle: currentAngle,
      maskDisplayInfo: null,
      patternDisplayInfoList: newPatternDisplayInfoList,
      threadMarkInfoList: PreviewModel.getThreadMarkInfoList(
        centerPoint: _originalCenter,
        pixelOfOneMm: _pixelOfOneMm,
        patternDisplayInfoList: newPatternDisplayInfoList,
      ),
      quiltMask: quiltMaskPath,
    );

    /// 他のページへの更新の通知
    ref
        .read(sewingProvider.notifier)
        .updateSewingByChild(ModuleType.rotateRedPoint);
  }

  @override
  Offset get getPreviewSize => embPreviewSizeDot;

  //////////////////////////////////////////////////////////////////
  ///
  /// ↓ ↓ ↓ ↓ ↓ ↓ ↓ パターンの移動に関連するデータと関数  ↓ ↓ ↓ ↓ ↓ ↓ ↓
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// 移動開始時のバックアップの模様の表示情報
  ///
  List<PatternDisplayInfo> _patternMoveDispInfoListBackup = [];

  ///
  /// 移動開始時のバックアップされた模様の最大範囲
  ///
  Rect _maxRectSizeBackup = Rect.largest;

  ///
  /// ドラッグが開始される位置
  ///
  Offset _startMovePoint = const Offset(0, 0);

  ///
  /// 最後に移動したlib値をバックアップします
  ///
  int _moveLibXBackup = 0;
  int _moveLibYBackup = 0;

  @override
  void backupPatternDisplayInfoInMove() {
    _patternMoveDispInfoListBackup.clear();
    _patternMoveDispInfoListBackup = List.from(state.patternDisplayInfoList);

    _maxRectSizeBackup = _getMaxRectSize();
    _moveLibXBackup = 0;
    _moveLibYBackup = 0;
  }

  bool _isAutoOneDirection = false;

  @override
  void dargPatternStart(ScaleStartDetails details) {
    if (SewingModel().isSewingPreviewCanDarg == false || _needleCurrent != 0) {
      return;
    }
    _startMovePoint = details.localFocalPoint;
    _isAutoOneDirection = SewingModel().isAutoOneDirection();
    _lastDragPosition = details.localFocalPoint;

    /// パターンの開始情報をバックアップする
    backupPatternDisplayInfoInMove();
  }

  @override
  void dargPattern(ScaleUpdateDetails details) {
    if (SewingModel().isSewingPreviewCanDarg == false || _needleCurrent != 0) {
      return;
    }

    Offset currentPoint = details.localFocalPoint;

    /// 10ドット以下の変化は無視する
    final Offset diffWithDrag = _lastDragPosition - currentPoint;
    if (diffWithDrag.dx.abs() <= dragDebounceThreshold &&
        diffWithDrag.dy.abs() <= dragDebounceThreshold) {
      return;
    }

    /// 今回は移動の合計量を計算
    Offset moveAllOffset = currentPoint - _startMovePoint;

    /// プレビュー領域内のパターンの動きを制限します
    double newLeft = _maxRectSizeBackup.left + moveAllOffset.dx;
    double newTop = _maxRectSizeBackup.top + moveAllOffset.dy;

    double minTop = _zoomPreviewRectList.first.top;
    double minLeft = _zoomPreviewRectList.first.left;
    double maxRight = _zoomPreviewRectList.first.right;
    double maxBottom = _zoomPreviewRectList.first.bottom;
    for (var zoomPreviewRect in _zoomPreviewRectList) {
      minTop = min(minTop, zoomPreviewRect.top);
      minLeft = min(minLeft, zoomPreviewRect.left);
      maxRight = max(maxRight, zoomPreviewRect.right);
      maxBottom = max(maxBottom, zoomPreviewRect.bottom);
    }

    newLeft = newLeft.clamp(
      minLeft,
      maxRight - _maxRectSizeBackup.width,
    );
    newTop = newTop.clamp(
      minTop,
      maxBottom - _maxRectSizeBackup.height,
    );

    /// パターンの実際の移動量を計算します
    moveAllOffset = Offset(
        newLeft - _maxRectSizeBackup.left, newTop - _maxRectSizeBackup.top);

    /// APIに必要な水平方向と垂直方向の移動量は、
    /// 実際の移動量に基づいて計算され、移動は0.5mm未満です.5mm舍去
    final double oneMoveStepInMm =
        _isAutoOneDirection ? _oneMoveStepInMmQuilt : _oneMoveStepInMm;
    int moveAllLibX = (moveAllOffset.dx * _mmOfOnePixel) ~/ oneMoveStepInMm;
    int moveAllLibY = (moveAllOffset.dy * _mmOfOnePixel) ~/ oneMoveStepInMm;

    (
      newMoveLibX: moveAllLibX,
      newMoveLibY: moveAllLibY,
    ) = _getNewMoveLibXY(
        moveLibX: moveAllLibX,
        moveLibY: moveAllLibY,
        oneMoveStepInMm: oneMoveStepInMm);

    /// 今回はlibの移動量を計算してみて
    final int moveLibX = moveAllLibX - _moveLibXBackup;
    final int moveLibY = moveAllLibY - _moveLibYBackup;

    /// API呼び出し条件に達していない場合は、APIが呼び出されます
    if (moveLibX == 0 && moveLibY == 0) {
      return;
    }

    /// 模様移動
    ref.read(moveFunctionProvider.notifier).movePatternByXY(
          moveLibX,
          moveLibY,
          apiType: _isAutoOneDirection
              ? MoveAPIType.moveQuiltEmb
              : MoveAPIType.moveEmbAll,
        );

    _moveLibXBackup = moveAllLibX;
    _moveLibYBackup = moveAllLibY;
    _lastDragPosition = currentPoint;

    /// view 更新
    if (_isAutoOneDirection) {
      update();
    } else {
      /// UIの実際の移動は、APIに必要な水平方向と垂直方向の移動量によって計算されます
      final double moveXOffset = moveAllLibX * oneMoveStepInMm * _pixelOfOneMm;
      final double moveYOffset = moveAllLibY * oneMoveStepInMm * _pixelOfOneMm;
      moveAllOffset = Offset(moveXOffset, moveYOffset);
      _updateMovedPatternDisplayInfo(moveAllOffset);
    }
  }

  @override
  void dargPatternEnd() {
    if (SewingModel().isSewingPreviewCanDarg == false || _needleCurrent != 0) {
      return;
    }

    /// データの初期化
    _startMovePoint = const Offset(0, 0);
    _moveLibXBackup = 0;
    _moveLibYBackup = 0;

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(
      redrawEmbPattern: true,
      isQuilt: _isAutoOneDirection,
    );
  }

  @override
  bool moveByArrowKey() {
    /// modelの使用回数を減らすために必要な値を定義します
    int moveTotalLibX = SewingModel().sewingMoveTotalLibX;
    int moveTotalLibY = SewingModel().sewingMoveTotalLibY;

    /// 今回は移動の合計量を計算
    (
      newMoveLibX: moveTotalLibX,
      newMoveLibY: moveTotalLibY,
    ) = _getNewMoveLibXY(
      moveLibX: moveTotalLibX,
      moveLibY: moveTotalLibY,
      oneMoveStepInMm: _oneMoveStepInMm,
    );

    /// API呼び出し条件に達していない場合は、APIが呼び出されます
    if (moveTotalLibX == 0 && moveTotalLibY == 0) {
      return true;
    }

    /// Model更新
    ref.read(moveFunctionProvider.notifier).movePatternByXY(
          moveTotalLibX,
          moveTotalLibY,
          apiType: MoveAPIType.moveEmbAll,
        );
    SewingModel()
      ..sewingMoveTotalLibX = moveTotalLibX
      ..sewingMoveTotalLibY = moveTotalLibY;

    /// view更新
    double moveXOffset = moveTotalLibX * _oneMoveStepInMm * _pixelOfOneMm;
    double moveYOffset = moveTotalLibY * _oneMoveStepInMm * _pixelOfOneMm;
    Offset moveAllOffset = Offset(moveXOffset, moveYOffset);
    _updateMovedPatternDisplayInfo(moveAllOffset);
    return false;
  }

  ///
  /// 選択したすべての模様の最大四角形範囲を取得します
  ///
  Rect _getMaxRectSize() {
    final (errorCode: errorCode, area: area) =
        EmbLibrary().apiBinding.getEmbArea();

    /// getEmbAreaが利用可能な場合は、getEmbAreaのデータを使用して計算することを優先します。
    if (errorCode == EmbLibraryError.EMB_NO_ERR) {
      final embSewingAttrib =
          TpdLibrary().apiBinding.bPIFGetAppDisplayEmb().embSewingAttrib.ref;

      final result = Rect.fromLTRB(
        PreviewModel.convertXMmToPixelsWithCenter(
            area.left, _originalCenter, _pixelOfOneMm),
        PreviewModel.convertYMmToPixelsWithCenter(
            area.top, _originalCenter, _pixelOfOneMm),
        PreviewModel.convertXMmToPixelsWithCenter(
            area.right, _originalCenter, _pixelOfOneMm),
        PreviewModel.convertYMmToPixelsWithCenter(
            area.bottom, _originalCenter, _pixelOfOneMm),
      ).shift(Offset(
        PreviewModel.convertMmToPixels(
            value: embSewingAttrib.disHorizon, pixelOfOneMm: _pixelOfOneMm),
        PreviewModel.convertMmToPixels(
            value: -embSewingAttrib.disVertical, pixelOfOneMm: _pixelOfOneMm),
      ));

      return result;
    }

    Log.errorTrace("getEmbAreaがERRORを返しました");

    List<double> xList = [];
    List<double> yList = [];

    List<PatternDisplayInfo> patternList =
        List.from(_patternMoveDispInfoListBackup);

    for (var pattern in patternList) {
      if (pattern.isBorder) {
        /// Group模様の刺繡イメージ表示エリア上の相対位置オフセットの回復します
        final Offset groupMaskTopLeftStitchAreaOffset =
            pattern.maskDisplayInfo.topLeft;
        final Offset groupMaskBottomRightStitchAreaOffset =
            pattern.maskDisplayInfo.bottomRight;

        xList.add(groupMaskTopLeftStitchAreaOffset.dx);
        yList.add(groupMaskTopLeftStitchAreaOffset.dy);
        xList.add(groupMaskBottomRightStitchAreaOffset.dx);
        yList.add(groupMaskBottomRightStitchAreaOffset.dy);
      } else {
        final Offset patternMaskTopLeftStitchAreaOffset =
            Offset(pattern.left, pattern.top);
        for (var border in pattern.borderDisplayInfoList) {
          final Offset borderMaskTopLeftPatternMaskOffset =
              Offset(border.left, border.top);
          for (var group in border.groupDisplayInfoList) {
            final Offset groupMaskTopLeftBorderMaskOffset =
                Offset(group.left, group.top);

            /// Group模様の刺繡イメージ表示エリア上の相対位置オフセットの回復します
            final Offset groupMaskTopLeftStitchAreaOffset =
                groupMaskTopLeftBorderMaskOffset +
                    borderMaskTopLeftPatternMaskOffset +
                    patternMaskTopLeftStitchAreaOffset;
            for (var embPattern in group.embPatternDisplayInfoList) {
              final double imageTop =
                  embPattern.imageTop + groupMaskTopLeftStitchAreaOffset.dy;
              final double imageLeft =
                  embPattern.imageLeft + groupMaskTopLeftStitchAreaOffset.dx;
              final double imageBottom = imageTop + embPattern.imageHeight;
              final double imageRight = imageLeft + embPattern.imageWidth;

              xList.add(imageLeft);
              xList.add(imageRight);
              yList.add(imageTop);
              yList.add(imageBottom);
            }
          }
        }
      }
    }

    final (
      topMin: topMin,
      leftMin: leftMin,
      bottomMax: bottomMax,
      rightMax: rightMax
    ) = PreviewModel.getMaxOuterWithXYList(xList: xList, yList: yList);

    return Rect.fromLTRB(leftMin, topMin, rightMax, bottomMax);
  }

  ///
  /// 新しいマスクの表示情報を取得する
  ///
  ({
    Mask? maskDisplayInfo,
    RedPointDisplayInfo? rotateRedPoint,
  }) _getMovedMaskDisplayInfo(List<PatternDisplayInfo> patternDisplayInfoList) {
    Mask? rotateMask;

    /// Sewing ページのマスクとドット位置の計算。
    if (SewingModel().toolbarPopupId == ToolbarPopupId.rotate) {
      for (var element in patternDisplayInfoList) {
        final Offset patternMaskTopLeftStitchAreaOffset =
            Offset(element.left, element.top);
        for (var border in element.borderDisplayInfoList) {
          final Offset borderMaskTopLeftPatternMaskOffset =
              Offset(border.left, border.top);
          for (var group in border.groupDisplayInfoList) {
            if (group.isAllNotSewing) {
              /// Do nothing
            } else {
              final Offset groupMaskTopLeftBorderMaskOffset =
                  Offset(group.left, group.top);

              /// Group模様の刺繡イメージ表示エリア上の相対位置オフセットの回復します
              final Offset groupMaskTopLeftStitchAreaOffset =
                  groupMaskTopLeftBorderMaskOffset +
                      borderMaskTopLeftPatternMaskOffset +
                      patternMaskTopLeftStitchAreaOffset;

              for (var embPattern in group.embPatternDisplayInfoList) {
                ///「Rotate」 ページで、マスクが表示されている範囲を探します。
                rotateMask = _combineAndEnlargeMask(
                  left: embPattern.imageLeft +
                      groupMaskTopLeftStitchAreaOffset.dx,
                  top:
                      embPattern.imageTop + groupMaskTopLeftStitchAreaOffset.dy,
                  width: embPattern.imageWidth,
                  height: embPattern.imageHeight,
                  beforeMask: rotateMask,
                );
              }
            }
          }

          /// マークがある場合は、マークの範囲を使用してマスクの表示範囲を再計算する必要があります。
          if (border.hasMark == true) {
            rotateMask = _combineAndEnlargeMask(
              left: border.maskDisplayInfo.topLeft.dx,
              top: border.maskDisplayInfo.topLeft.dy,
              width: border.maskDisplayInfo.bottomRight.dx -
                  border.maskDisplayInfo.topLeft.dx,
              height: border.maskDisplayInfo.bottomRight.dy -
                  border.maskDisplayInfo.topLeft.dy,
              beforeMask: rotateMask,
            );
          } else {
            /// Do nothing
          }
        }
      }
    } else {
      /// Do noting
    }

    Mask? maskDisplayInfo;
    RedPointDisplayInfo? rotateRedPoint;
    if (rotateMask != null) {
      maskDisplayInfo = rotateMask;
      rotateRedPoint =
          PreviewModel().getSewingRotateRedPointInfo(mask: rotateMask);
    } else {
      /// Do noting
    }

    return (
      maskDisplayInfo: maskDisplayInfo,
      rotateRedPoint: rotateRedPoint,
    );
  }

  ///
  /// 移動後に表示データを更新する
  ///
  void _updateMovedPatternDisplayInfo(Offset moveAllOffset) {
    List<PatternDisplayInfo> updatedPatternDisplayInfoList = [];
    for (var pattern in _patternMoveDispInfoListBackup) {
      List<EmbBorderDisplayInfo> updateBorderDisplayInfoList = [];
      for (var border in pattern.borderDisplayInfoList) {
        updateBorderDisplayInfoList.add(border.copyWith(
          maskDisplayInfo: Mask(
            topLeft: border.maskDisplayInfo.topLeft + moveAllOffset,
            topRight: border.maskDisplayInfo.topRight + moveAllOffset,
            bottomLeft: border.maskDisplayInfo.bottomLeft + moveAllOffset,
            bottomRight: border.maskDisplayInfo.bottomRight + moveAllOffset,
          ),
        ));
      }

      updatedPatternDisplayInfoList.add(
        pattern.copyWith(
          left: pattern.left + moveAllOffset.dx,
          top: pattern.top + moveAllOffset.dy,
          maskDisplayInfo: Mask(
            topLeft: pattern.maskDisplayInfo.topLeft + moveAllOffset,
            topRight: pattern.maskDisplayInfo.topRight + moveAllOffset,
            bottomLeft: pattern.maskDisplayInfo.bottomLeft + moveAllOffset,
            bottomRight: pattern.maskDisplayInfo.bottomRight + moveAllOffset,
          ),
          borderDisplayInfoList: updateBorderDisplayInfoList,
        ),
      );
    }

    Offset patternTopLeft = Offset(
      updatedPatternDisplayInfoList.first.left,
      updatedPatternDisplayInfoList.first.top,
    );
    Offset patternBottomRight = Offset(
      updatedPatternDisplayInfoList.first.left +
          updatedPatternDisplayInfoList.first.width,
      updatedPatternDisplayInfoList.first.top +
          updatedPatternDisplayInfoList.first.height,
    );

    var (
      maskDisplayInfo: maskDisplayInfo,
      rotateRedPoint: rotateRedPoint,
    ) = _getMovedMaskDisplayInfo(updatedPatternDisplayInfoList);

    EmbGroup currentGroup = PatternModel().getCurrentGroup();

    /// view更新
    state = state.copyWith(
      patternDisplayInfoList: updatedPatternDisplayInfoList,
      isRedPointOverPreview: maskDisplayInfo == null
          ? false
          : _isRedPointOverPreview(
              viewCenter: _originalCenter,
              topLeft: patternTopLeft,
              bottomRight: patternBottomRight,
            ),
      threadMarkInfoList: PreviewModel.getThreadMarkInfoList(
        centerPoint: _originalCenter,
        pixelOfOneMm: _pixelOfOneMm,
        patternDisplayInfoList: updatedPatternDisplayInfoList,
      ),
      maskDisplayInfo: maskDisplayInfo,
      rotateRedPoint: rotateRedPoint,
      rotateCenter: _getRotateCenter(currentGroup),
      quiltMask: _getQuiltMoveAndRotateMask(currentGroup,
          moveAllOffset: moveAllOffset),
      needlePointerCenterOffset:
          SewingModel().getCurrentNeedlePosition(pixelOfOneMm: _pixelOfOneMm),
    );
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// ↑ ↑ ↑ ↑ ↑ ↑ ↑ パターンの移動に関連するデータと関数  ↑ ↑ ↑ ↑ ↑ ↑ ↑
  ///
  //////////////////////////////////////////////////////////////////

  /// 回転前のバックアップデータ。
  List<PatternDisplayInfo> _patternDisplayInfoListBack = [];

  @override
  void backupPatternDisplayInfoInRotate() {
    /// 回転前のすべての模様の情報をバックアップします。
    _patternDisplayInfoListBack.clear();
    _patternDisplayInfoListBack = List.from(state.patternDisplayInfoList);
  }

  ///
  /// Rotateドットの表示位置情報の中心
  ///
  Offset _rotateRedPointAreaCenter = const Offset(0, 0);
  @override
  void dargRotatePointStart(Offset centerPoint, Offset startPoint) {
    _rotateRedPointAreaCenter = centerPoint;
    _isUsingRedPoint = true;
    _lastDragPosition = startPoint;

    /// Rotateの前に、Rotate中にUIで使用する必要があるデータをバックアップします
    backupPatternDisplayInfoInRotate();
  }

  @override
  void dargRotatePoint(Offset currentPoint) {
    /// 10ドット以下の変化は無視する
    final Offset diffWithDrag = _lastDragPosition - currentPoint;
    if (diffWithDrag.dx.abs() <= dragDebounceThreshold &&
        diffWithDrag.dy.abs() <= dragDebounceThreshold) {
      return;
    }

    final Offset diffWithCenter = currentPoint - _rotateRedPointAreaCenter;

    /// 第1象限を0~90°に、正のX軸を0°に、正のY軸を90°に設定します
    final double baseAngle =
        atan2(diffWithCenter.dy, diffWithCenter.dx) * _angle180 / pi + _angle90;

    /// 回転が始まる角度に0°をリダイレクトし、角度の値が0~360の間であることを確認します
    final double currentAngle = baseAngle % _angle360;

    /// 1°旋转
    if (state.dragAngle == currentAngle.toInt()) {
      return;
    } else {
      PreviewModel().uiRotateAngle = currentAngle.ceilToDouble();
      _lastDragPosition = currentPoint;
      updateByRotateLongPress();
      return;
    }
  }

  @override
  void dargRotatePointEnd() {
    /// 角度の値を Lib 使用量の値に変換します。
    int degree = (state.dragAngle ?? 0).toInt() * conversionRate;
    _isUsingRedPoint = false;

    _rotateRedPointAreaCenter = const Offset(0, 0);
    PreviewModel().uiRotateAngle = 0;

    /// Lib通知
    /// 回転中に範囲外に出た場合は、画面を更新しません
    if (SewingModel().rotateEmbAll(degree) == EmbLibraryError.EMB_NO_ERR) {
      /// View更新
      update();

      /// 他のページへの更新の通知
      ref.read(sewingProvider.notifier).updateSewingByChild(ModuleType.preview);

      /// プロジェクト投影画面を更新する
      final embProjectorFunction =
          ref.read(embProjectorFunctionProvider.notifier);
      embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
    } else {
      /// Do noting
    }
  }

  @override
  List<double> getGridVerticalList() {
    List<double> verticalList = [];
    double xOffset = _originalCenter.dx;
    int mmValue = state.gridTypeIndex == EmbGridType.embGridGridLine10.index
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    /// 中心点の左端の線の位置を計算するには
    xOffset -= _pixelOfOneMm * mmValue;
    while (xOffset >= 0) {
      verticalList.add(xOffset);
      xOffset -= _pixelOfOneMm * mmValue;
    }

    xOffset = _originalCenter.dx;

    /// 中心点の右端の線の位置を計算するには
    xOffset += _pixelOfOneMm * mmValue;
    while (xOffset <= embPreviewSizeDot.dx) {
      verticalList.add(xOffset);
      xOffset += _pixelOfOneMm * mmValue;
    }

    return verticalList;
  }

  @override
  List<double> getGridHorizontalList() {
    List<double> horizontalList = [];
    double yOffset = _originalCenter.dy;
    int mmValue = state.gridTypeIndex == EmbGridType.embGridGridLine10.index
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    /// 中心点上端の線の位置を計算するには
    yOffset -= _pixelOfOneMm * mmValue;
    while (yOffset >= 0) {
      horizontalList.add(yOffset);
      yOffset -= _pixelOfOneMm * mmValue;
    }

    yOffset = _originalCenter.dy;

    /// 中心点の下端の線の位置を計算するには
    yOffset += _pixelOfOneMm * mmValue;
    while (yOffset <= embPreviewSizeDot.dy) {
      horizontalList.add(yOffset);
      yOffset += _pixelOfOneMm * mmValue;
    }

    return horizontalList;
  }

  @override
  void dispose() {
    super.dispose();

    /// データの停止
    _listener.close();
  }

  ///
  /// 選択した枠のプレビュー表示データを取得します
  ///
  Path _getFrameDrawPath() {
    List<FrameSizeAndArea>? frameSizeAndAreaList = getFrameDisplaySizeAndArea(
        DeviceLibrary().apiBinding.getEmbroideryFrameDisplay().frameDispType);
    assert(frameSizeAndAreaList != null, "対応するサイズのボックスが見つかりません");

    Path drawPath = Path();
    Path basePath = Path();

    if (frameSizeAndAreaList!.length > 1) {
      basePath =
          _get60x20FrameBasePath(frameSizeAndAreaList: frameSizeAndAreaList);
    } else {
      double width = PreviewModel.convertMmToPixels(
          value: frameSizeAndAreaList.first.width, pixelOfOneMm: _pixelOfOneMm);
      double height = PreviewModel.convertMmToPixels(
          value: frameSizeAndAreaList.first.height,
          pixelOfOneMm: _pixelOfOneMm);
      double startX = PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList.first.left,
              pixelOfOneMm: _pixelOfOneMm) +
          _originalCenter.dx;
      double startY = PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList.first.top,
              pixelOfOneMm: _pixelOfOneMm) +
          _originalCenter.dy;

      Rect rect = Rect.fromLTWH(startX, startY, width, height);
      basePath.moveTo(rect.left, rect.top);
      basePath.lineTo(rect.left, rect.bottom);
      basePath.lineTo(rect.right, rect.bottom);
      basePath.lineTo(rect.right, rect.top);
      basePath.lineTo(rect.left, rect.top);
    }

    /// フレームの破線の一段の長さ
    double dashWidth = _dashedLineLength;
    double dashSpace = _dashedLineSpace;

    /// 描画Pathを計算する
    double distance = 0.0;
    for (PathMetric pathMetric in basePath.computeMetrics()) {
      while (distance < pathMetric.length) {
        drawPath.addPath(
          pathMetric.extractPath(distance, distance + dashWidth),
          Offset.zero,
        );
        distance += dashWidth;
        distance += dashSpace;
      }
    }
    return drawPath;
  }

  ///
  /// 60*20枠のプレビューにはデータが表示されま
  ///
  Path _get60x20FrameBasePath(
      {required List<FrameSizeAndArea> frameSizeAndAreaList}) {
    Path basePath = Path();

    /// List順：60*20 mm、50*30 mm、30*40 mm
    List<Rect> rectList = List.generate(
      frameSizeAndAreaList.length,
      (index) => Rect.fromLTWH(
          PreviewModel.convertMmToPixels(
                  value: frameSizeAndAreaList[index].left,
                  pixelOfOneMm: _pixelOfOneMm) +
              _originalCenter.dx,
          PreviewModel.convertMmToPixels(
                  value: frameSizeAndAreaList[index].top,
                  pixelOfOneMm: _pixelOfOneMm) +
              _originalCenter.dy,
          PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList[index].width,
              pixelOfOneMm: _pixelOfOneMm),
          PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList[index].height,
              pixelOfOneMm: _pixelOfOneMm)),
    );

    /// 1番目と2番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*40 mm
    rectList.insert(
      1,
      Rect.fromPoints(
        Offset(rectList[1].left, rectList.first.top),
        Offset(rectList[1].right, rectList.first.bottom),
      ),
    );

    /// 2番目と3番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*30 mm、30*40 mm
    rectList.insert(
      3,
      Rect.fromPoints(
        Offset(rectList.last.left, rectList[2].top),
        Offset(rectList.last.right, rectList[2].bottom),
      ),
    );

    /// 60*20 mmの左上点から描画
    basePath.moveTo(rectList.first.left, rectList.first.top);

    /// Rectの左上点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].left, rectList[index].top);
    }

    /// Rectの右上点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].right, rectList[index].top);
    }

    /// Rectの右下点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].right, rectList[index].bottom);
    }

    /// Rectの左下点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].left, rectList[index].bottom);
    }

    /// 接続開始点
    basePath.lineTo(rectList.first.left, rectList.first.top);

    return basePath;
  }

  ///
  /// maskボックスがPreview表示領域を超えていないか
  /// 超えた場合、赤いドットは表示されません
  ///
  bool _isRedPointOverPreview({
    required Offset viewCenter,
    required Offset topLeft,
    required Offset bottomRight,
  }) {
    bool isMaskOutPreviewArea = false;

    /// Maskボックス位置修正（データに赤いドットの範囲が追加されているので、ここで削除）
    topLeft = Offset(
      topLeft.dx + maskOutArea,
      topLeft.dy + maskOutArea,
    );
    bottomRight = Offset(
      bottomRight.dx - maskOutArea,
      bottomRight.dy - maskOutArea,
    );

    /// 現在Previewの表示範囲
    Rect previewRect = Rect.fromCenter(
      center: viewCenter,
      width: embPreviewSizeDot.dx,
      height: embPreviewSizeDot.dy,
    );

    /// 範囲判断
    if (topLeft.dx <= previewRect.left ||
        topLeft.dy <= previewRect.top ||
        bottomRight.dx >= previewRect.right ||
        bottomRight.dy >= previewRect.bottom) {
      isMaskOutPreviewArea = true;
    } else {
      isMaskOutPreviewArea = false;
    }

    return isMaskOutPreviewArea;
  }

  ///
  /// Viewは最初にPattern情報変換を使用する
  ///
  ({
    List<PatternDisplayInfo> patternDisplayInfoList,
    Mask? rotateMask,
    Path? quiltMask,
    Offset rotateCenter,
  }) _getPatternDisplayInfoList() {
    List<PatternDisplayInfo> patternDisplayInfoList = [];
    Mask? rotateMask;
    final bool isNeedRotateMask =
        SewingModel().toolbarPopupId == ToolbarPopupId.rotate;

    /// カレントグループ
    EmbGroup currentGroup = PatternModel().getCurrentGroup();

    PatternModel().getAllPattern().asMap().forEach((sewingIndex, pattern) {
      if (pattern is EmbGroup) {
        patternDisplayInfoList.add(PreviewModel().getGroupPatternDisplayInfo(
            scrollType: ScrollCenterType.IMAGE_SEWING,
            group: pattern,
            centerPoint: _originalCenter,
            pixelOfOneMm: _pixelOfOneMm,
            sewingIndex: sewingIndex,
            currentGroupHandle: currentGroup.handle,
            zoomScale: zoomList.first));
      } else if (pattern is EmbBorder) {
        patternDisplayInfoList.add(PreviewModel().getBorderPatternDisplayInfo(
            scrollType: ScrollCenterType.IMAGE_SEWING,
            border: pattern,
            centerPoint: _originalCenter,
            pixelOfOneMm: _pixelOfOneMm,
            sewingIndex: sewingIndex,
            currentGroupHandle: currentGroup.handle,
            zoomScale: zoomList.first));
      } else {
        /// Do noting
      }

      /// Sewing ページのマスクとドット位置の計算。
      if (isNeedRotateMask == true) {
        final Offset patternMaskTopLeftStitchAreaOffset = Offset(
            patternDisplayInfoList.last.left, patternDisplayInfoList.last.top);
        for (var border in patternDisplayInfoList.last.borderDisplayInfoList) {
          final Offset borderMaskTopLeftPatternMaskOffset =
              Offset(border.left, border.top);
          for (var group in border.groupDisplayInfoList) {
            if (group.isAllNotSewing) {
              /// Do nothing
            } else {
              final Offset groupMaskTopLeftBorderMaskOffset =
                  Offset(group.left, group.top);

              /// Group模様の刺繡イメージ表示エリア上の相対位置オフセットの回復します
              final Offset groupMaskTopLeftStitchAreaOffset =
                  groupMaskTopLeftBorderMaskOffset +
                      borderMaskTopLeftPatternMaskOffset +
                      patternMaskTopLeftStitchAreaOffset;
              for (var embPattern in group.embPatternDisplayInfoList) {
                ///「Rotate」 ページで、マスクが表示されている範囲を探します。
                rotateMask = _combineAndEnlargeMask(
                  left: embPattern.imageLeft +
                      groupMaskTopLeftStitchAreaOffset.dx,
                  top:
                      embPattern.imageTop + groupMaskTopLeftStitchAreaOffset.dy,
                  width: embPattern.imageWidth,
                  height: embPattern.imageHeight,
                  beforeMask: rotateMask,
                );
              }
            }
          }

          /// マークがある場合は、マークの範囲を使用してマスクの表示範囲を再計算する必要があります。
          if (border.hasMark == true) {
            rotateMask = _combineAndEnlargeMask(
              left: border.maskDisplayInfo.topLeft.dx,
              top: border.maskDisplayInfo.topLeft.dy,
              width: border.maskDisplayInfo.bottomRight.dx -
                  border.maskDisplayInfo.topLeft.dx,
              height: border.maskDisplayInfo.bottomRight.dy -
                  border.maskDisplayInfo.topLeft.dy,
              beforeMask: rotateMask,
            );
          } else {
            /// Do nothing
          }
        }
      } else {
        /// Do noting
      }
    });

    return (
      patternDisplayInfoList: patternDisplayInfoList,
      rotateMask: isNeedRotateMask ? rotateMask : null,
      quiltMask: _getQuiltMoveAndRotateMask(currentGroup),
      rotateCenter: _getRotateCenter(currentGroup),
    );
  }

  ///
  /// 全体回転原点を取得します。
  ///
  Offset _getRotateCenter(EmbGroup currentGroup) {
    final embInfo = currentGroup.embGroupInfo.embInfo;
    final int centerX = embInfo.embRotateOrigine.X + embInfo.frameOffset.X;
    final int centerY = embInfo.embRotateOrigine.Y + embInfo.frameOffset.Y;

    return Offset(
      PreviewModel.convertXMmToPixelsWithCenter(
          centerX, _originalCenter, _pixelOfOneMm),
      PreviewModel.convertYMmToPixelsWithCenter(
          centerY, _originalCenter, _pixelOfOneMm),
    );
  }

  ///
  /// 元のマスクと組み合わせると、より大きなマスクになります。
  ///
  Mask _combineAndEnlargeMask({
    Mask? beforeMask,
    required double left,
    required double top,
    required double width,
    required double height,
  }) {
    double right = left + width;
    double bottom = top + height;

    return beforeMask == null
        ? beforeMask = Mask(
            topLeft: Offset(left, top),
            topRight: Offset(right, top),
            bottomLeft: Offset(left, bottom),
            bottomRight: Offset(right, bottom),
          )
        : Mask(
            topLeft: Offset(
              min(beforeMask.topLeft.dx, left),
              min(beforeMask.topLeft.dy, top),
            ),
            topRight: Offset(
              max(beforeMask.topRight.dx, right),
              min(beforeMask.topRight.dy, top),
            ),
            bottomLeft: Offset(
              min(beforeMask.bottomLeft.dx, left),
              max(beforeMask.bottomLeft.dy, bottom),
            ),
            bottomRight: Offset(
              max(beforeMask.bottomRight.dx, right),
              max(beforeMask.bottomRight.dy, bottom),
            ),
          );
  }

  ///
  /// スキャンした背景画像を取得する
  ///
  Widget? _getScanBackGroundImage() {
    if (ScanModel().hasBackgroundImage == false ||
        ScanModel().getBackgroundShowStatus() == false) {
      return null;
    }

    if (ScanModel.createSketchesImage() == null) {
      return null;
    }

    return Opacity(
      opacity: ScanModel().backgroundDensityValue,
      child: Image.memory(ScanModel.createSketchesImage()!),
    );
  }

  ///
  /// データ更新ページをリッスンする
  ///
  late final ProviderSubscription _listener;

  ///
  /// 現在接続されている刺しゅうフレームで、回転と移動の制限範囲を更新します
  ///
  void _updateZoomPreviewRectList(FrameSize frameSize) {
    _zoomPreviewRectList.clear();

    if (frameSize == FrameSizeType.FRAME_NOTHING) {
      _zoomPreviewRectList.add(Rect.fromCenter(
        center: _originalCenter,
        width: embPreviewSizeDot.dx,
        height: embPreviewSizeDot.dy,
      ));
    } else {
      frameSizeAreaMap[frameSize]?.forEach((element) {
        _zoomPreviewRectList.add(Rect.fromCenter(
          center: _originalCenter,
          width: PreviewModel.convertMmToPixels(
              value: element.width, pixelOfOneMm: _pixelOfOneMm),
          height: PreviewModel.convertMmToPixels(
              value: element.height, pixelOfOneMm: _pixelOfOneMm),
        ));
      });
    }

    if (_zoomPreviewRectList.isEmpty) {
      _zoomPreviewRectList.add(Rect.fromCenter(
        center: _originalCenter,
        width: embPreviewSizeDot.dx,
        height: embPreviewSizeDot.dy,
      ));
    } else {
      /// Do nothing
    }
  }

  ///
  /// 移動量が枠の範囲外であるかどうかを確認し、範囲外の場合は、更新された移動量が移動量を超えていない状態です。
  ///
  ({
    int newMoveLibX,
    int newMoveLibY,
  }) _getNewMoveLibXY({
    required int moveLibX,
    required int moveLibY,
    required double oneMoveStepInMm,
  }) {
    while (true) {
      bool isContain = true;

      /// 今回は移動の合計量を計算
      double moveXOffset = moveLibX * oneMoveStepInMm * _pixelOfOneMm;
      double moveYOffset = moveLibY * oneMoveStepInMm * _pixelOfOneMm;

      /// 新しい頂点座標は、移動量から計算されます
      double newLeft = _maxRectSizeBackup.left + moveXOffset;
      double newTop = _maxRectSizeBackup.top + moveYOffset;
      double newRight = newLeft + _maxRectSizeBackup.width;
      double newBottom = newTop + _maxRectSizeBackup.height;

      for (var zoomPreviewRect in _zoomPreviewRectList) {
        if (zoomPreviewRect.contains(Offset(newLeft, newTop)) == false ||
            zoomPreviewRect.contains(Offset(newRight, newBottom)) == false) {
          isContain = false;
          continue;
        } else {
          isContain = true;
          break;
        }
      }

      /// 正常に移動するか、移動されていない状態に戻ると、ループを終了します。
      if (isContain == true || (moveLibX == 0 && moveLibY == 0)) {
        break;
      } else {
        /// 範囲外では、1移動ユニットの最大移動可能距離を探します。
        /// Xを探します
        if (moveLibX == 0) {
          moveLibX = 0;
        } else if (moveLibX > 0) {
          moveLibX--;
        } else {
          moveLibX++;
        }

        /// Yを探します
        if (moveLibY == 0) {
          moveLibY = 0;
        } else if (moveLibY > 0) {
          moveLibY--;
        } else {
          moveLibY++;
        }
      }
    }
    return (newMoveLibX: moveLibX, newMoveLibY: moveLibY);
  }

  ///
  /// キルトサッシのmaskを取る
  ///
  Path? _getQuiltMoveAndRotateMask(EmbGroup quiltGroup,
      {Offset? moveAllOffset}) {
    ///  EdgeToEdgeキルトサッシと1方向キルトサッシでない場合は、null が返されます
    if (SewingModel().isAutoOneDirection() == false) {
      return null;
    }

    Path quiltMaskPath = Path();
    Offset firstPoint = const Offset(0, 0);

    /// 「回転」と「移動」がポップアップしたとき
    if ([
      ToolbarPopupId.quiltSashMove,
      ToolbarPopupId.quiltSashRotate,
      ToolbarPopupId.quiltEdgeToEdgeMove,
      ToolbarPopupId.quiltEdgeToEdgeRotate,
    ].contains(SewingModel().toolbarPopupId)) {
      int pointCount = quiltGroup
          .embGroupInfo.embPatternInfo.embPatterns.first.quiltRectanglePointNum;

      /// データが空の場合は、null を返します
      if (pointCount == 0) {
        return null;
      }

      List<SSPoint> pointList = quiltGroup
          .embGroupInfo.embPatternInfo.embPatterns.first.quiltRectanglePoint;

      for (var i = 0; i < pointCount; i++) {
        double x = pointList[i].X.toDouble() - quiltMaskStartPoint.dx;
        double y = pointList[i].Y.toDouble() - quiltMaskStartPoint.dy;

        if (i == 0) {
          quiltMaskPath.moveTo(x, y);
          firstPoint = Offset(x, y);
        } else {
          quiltMaskPath.lineTo(x, y);
        }
      }
      quiltMaskPath.lineTo(firstPoint.dx, firstPoint.dy);

      return quiltMaskPath;
    }

    /// それ以外の場合は、null が返されます
    return null;
  }

  ///
  /// 縫製情報データ変更監視がONになっている
  ///
  int _needleCurrent = 0;
  void _openListen() {
    _listener = _ref.stateListen(
      fireImmediately: true,
      appDisplayEmbStateProvider.select((value) => value.embSetting.ref.mode),
      (_, nextState) {
        Log.d(
            tag: "embSetting.mode", description: "embSetting.mode:$nextState");
        if (nextState == ScrollCenterType.IMAGE_SEWING) {
          PatternModel().clearCurrentGroupInfoCache();
          update();
        } else {
          /// Do nothing
        }
      },
    );

    /// 針のドロップポイントが更新されます
    _ref.stateListen(
      appDisplayEmbStateProvider.select((value) => (
            needleCurrent: value.sewingInfo.ref.needleCurrent,
            frameSize: value.embSetting.ref.frameSize,
            embSewingStartPosition:
                value.embSewingAttrib.ref.embSewingStartPosition,
            embSewingQultStartPosition:
                value.embSewingAttrib.ref.embSewingQultStartPosition,
          )),
      (previous, nextState) {
        _needleCurrent = nextState.needleCurrent;
        state = state.copyWith(
          needlePointerCenterOffset: SewingModel()
              .getCurrentNeedlePosition(pixelOfOneMm: _pixelOfOneMm),
        );

        /// 現在接続されている刺繍フレームの変更を聞いてください
        if (previous?.frameSize != nextState.frameSize) {
          _updateZoomPreviewRectList(nextState.frameSize);
        } else {
          /// Do nothing
        }
      },
    );

    /// 模様変更可能性が更新されました
    _ref.stateListen(
      appDisplayEmbStateProvider
          .select((value) => (value.embSewingAttrib.ref.QuiltExtCurrentNum,)),
      (_, nextState) {
        update();
      },
    );
  }
}
