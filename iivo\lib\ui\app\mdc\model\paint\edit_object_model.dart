import 'dart:ui' as ui;

import 'package:audio_player/audio_player_interface.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../ui/page/paint/top_bar/illustration_design_retry_page/illustration_design_retry_page_view_interface.dart';
import '../resume_history_model.dart';
import 'clip_board_model.dart';
import 'draw_canvas_model.dart';
import 'drawing_type_model.dart';
import 'pen_model.dart';
import 'size_adjustment_model.dart';

class EditObjectModel {
  EditObjectModel._internal();

  factory EditObjectModel() => _instance;
  static final EditObjectModel _instance = EditObjectModel._internal();

  ///
  /// 編集指示情報
  ///
  MdcUnsettledObjectInfo objectInfo = MdcUnsettledObjectInfo();

  ///
  /// 元編集指示情報
  ///
  MdcUnsettledObjectInfo originObjectInfo = MdcUnsettledObjectInfo();

  ///
  /// タッチ情報
  ///
  MdcPenTouchInfo penTouchInfo = MdcPenTouchInfo();

  ///
  /// ﾌﾟﾛｾｽ情報
  ///
  MdcReqProc reqProc = const MdcReqProc();

  ///
  /// 選択した部分模様描画イメージ
  ///
  MdcImageInfo _workImageInfoParts = MdcImageInfo.empty();

  ///
  /// 部分模様なし全体描画イメージ
  ///
  MdcImageInfo _workImgInfoBackGround = MdcImageInfo.empty();

  ///
  /// エラーコード
  ///
  MdcLibraryError clipErrorCode = MdcLibraryError.mdcNoError;

  ///
  /// 単位変換率（mm/dot）
  ///
  static const double ratio = PenModel.ratio;

  ///
  /// 背景画像
  ///
  bool existBackImage = false;

  ///
  /// 貼り付けできるの判断
  ///
  bool existClipboard = false;

  ///
  /// 拡張ボタンが長押しかどうか
  ///
  bool isLongPress = false;

  ///
  /// 部分模様はスタンプ
  ///
  bool isStampParts = false;

  ///
  /// 保存したのファイルを上書きできるかどうか
  ///
  bool _canBeCovertFile = false;

  ///
  /// 模様の情報があります
  ///
  bool hasPartsImageInfo() => getMdcPartsImageInfo().imageData.isNotEmpty;

  ///
  /// 部分模様処理を拒絶為にLockフラグを追加する
  ///
  bool _lock = false;
  bool isPartsProcessLocked() => _lock;
  void lockPartsProcess() => _lock = true;
  void unlockPartsProcess() => _lock = false;

  ///
  /// 有効なお絵かきデータが存在するかチェックする
  ///
  bool checkCanvas() {
    return getMdcPartsImageInfo().imageData.isNotEmpty ||
        MdcLibrary().apiBinding.isMdcDrawingPatternPresence().result;
  }

  ///
  /// 領域選択Press
  ///
  void press(MdcClippingTypes type, ui.Offset offset) {
    _canBeCovertFile = false;
    if (hasPartsImageInfo()) {
      ClipBoardModel().confirmUnsettledObject(needUpdateUI: true);
      ResumeHistoryModel().backSnapshot();
      _canBeCovertFile = true;
    }

    MdcLibrary()
        .apiBinding
        .setMdcDrawingType(MdcDrawingType.drawtype_area_select);
    penTouchInfo = penTouchInfo.copyWith(
      posX: offset.dx.toInt(),
      posY: offset.dy.toInt(),
      pstat: MdcPenTouchStatus.press,
    );
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.beginning);

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    switch (type) {
      case MdcClippingTypes.rectangle:
        _rectangleAreaSelect();
        return;
      case MdcClippingTypes.lineMulti:

        /// お絵描きエリアにデータなし
        if (!MdcLibrary().apiBinding.isMdcDrawingPatternPresence().result ==
            true) {
          return;
        }

        /// 開始点
        if (reqProc.status == InstructionProgressStatus.beginning &&
            clipErrorCode == MdcLibraryError.mdcNoError) {
          _lineMultiAreaSelect();
          return;
        }

        /// 途中点
        if (clipErrorCode == MdcLibraryError.mdcNoErrorNoImage &&
            ClipBoardModel().clipPositionList.isNotEmpty) {
          reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
        }
        return;

      case MdcClippingTypes.dropperSelect:
        _dropperSelectAreaSelect();
        _saveOrCovertHistoryFile();
        return;
      case MdcClippingTypes.allSelect:
        if (_canBeCovertFile) {
          _canBeCovertFile = false;

          /// 部分模様ありの場合、部分模様確認だけ操作を実行する
          SystemSoundPlayer().play(SystemSoundEnum.invalid);
          clipErrorCode = MdcLibraryError.mdcErrorSelectedAreaNotImage;
          return;
        }

        _allSelectAreaSelect();
        _saveOrCovertHistoryFile();
        return;
      case MdcClippingTypes.freeClose:
      default:
        return;
    }
  }

  ///
  /// 領域選択release
  ///
  void release(MdcClippingTypes type) {
    if (type == MdcClippingTypes.dropperSelect ||
        type == MdcClippingTypes.allSelect) {
      return;
    }

    /// 「直線(連続)選択」時に１つも模様がない場合は、選択処理を強制終了させる。
    final hasNoData =
        !MdcLibrary().apiBinding.isMdcDrawingPatternPresence().result;
    if (type == MdcClippingTypes.lineMulti && hasNoData) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    if (type == MdcClippingTypes.freeClose) {
      _freeCloseAreaSelect();
      _saveOrCovertHistoryFile();
      return;
    }

    reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
    ui.Offset position;
    if (ClipBoardModel().clipPositionList.isEmpty) {
      if (hasPartsImageInfo()) {
        penTouchInfo = penTouchInfo.copyWith(
          posX: objectInfo.centerPosX,
          posY: objectInfo.centerPosY,
          pstat: MdcPenTouchStatus.release,
        );
      } else {
        Log.errorTrace('範囲選択座標なし');
        return;
      }
    } else {
      position = ClipBoardModel().clipPositionList.last;
      penTouchInfo = penTouchInfo.copyWith(
        posX: position.dx.toInt(),
        posY: position.dy.toInt(),
        pstat: MdcPenTouchStatus.release,
      );
    }

    switch (type) {
      case MdcClippingTypes.rectangle:
        _rectangleAreaSelect();
        _saveOrCovertHistoryFile();
        return;
      case MdcClippingTypes.lineMulti:
        _lineMultiAreaSelect();
        _saveOrCovertLineMultiHistoryFile();
        return;
      default:
        return;
    }
  }

  ///
  /// 四角形選択
  ///
  void _rectangleAreaSelect() {
    switch (reqProc.status) {
      case InstructionProgressStatus.beginning:
        final (
          errorCode: outErrorCode,
          reqProc: outReqProc,
          objectInfo: _,
          imgInfoParts: _,
          imgInfoBackGround: _,
        ) = MdcLibrary()
            .apiBinding
            .editMdcAreaSelect(penTouchInfo, reqProc, 0, []);

        clipErrorCode = outErrorCode;
        reqProc = outReqProc;
        if (clipErrorCode != MdcLibraryError.mdcNoErrorNoImage) {
          SystemSoundPlayer().play(SystemSoundEnum.invalid);
          _setReqProcClosingAndUpdateCanvasImageWhenHasError();
          Log.errorTrace("editMdcAreaSelect : 四角形範囲選択失敗");
          return;
        }

        if (outReqProc.status != InstructionProgressStatus.during) {
          Log.errorTrace(
              "editMdcAreaSelect実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
        }
        return;
      case InstructionProgressStatus.during:
        final (
          errorCode: outErrorCode,
          reqProc: outReqProc,
          objectInfo: outObjectInfo,
          imgInfoParts: imgInfoParts,
          imgInfoBackGround: imgInfoBackGround,
        ) = MdcLibrary()
            .apiBinding
            .editMdcAreaSelect(penTouchInfo, reqProc, 0, []);

        clipErrorCode = outErrorCode;
        reqProc = outReqProc;
        if (clipErrorCode != MdcLibraryError.mdcNoError) {
          SystemSoundPlayer().play(SystemSoundEnum.invalid);
          _setReqProcClosingAndUpdateCanvasImageWhenHasError();
          Log.errorTrace("editMdcAreaSelect : 四角形範囲選択失敗");
          return;
        }

        if (outReqProc.status != InstructionProgressStatus.during) {
          Log.errorTrace(
              "editMdcAreaSelect実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
        }

        originObjectInfo = objectInfo = outObjectInfo;
        setMdcBothImageInfo(
            updateParts: imgInfoParts,
            updateBackground: imgInfoBackGround,
            needUpdateUI: true);
        updateMdcSize();
        return;

      case InstructionProgressStatus.closing:
      case InstructionProgressStatus.single:
      default:
        clipErrorCode = MdcLibraryError.mdcNoErrorNoImage;
        Log.w(tag: '範囲選択', description: "mdcNoErrorNoImage");
        return;
    }
  }

  ///
  /// 直線連続選択
  ///
  void _lineMultiAreaSelect() {
    switch (reqProc.status) {
      case InstructionProgressStatus.beginning:
        final (
          errorCode: outErrorCode,
          reqProc: beginReqProc,
          objectInfo: _,
          imgInfoParts: _,
          imgInfoBackGround: _,
        ) = MdcLibrary()
            .apiBinding
            .editMdcAreaSelect(penTouchInfo, reqProc, 0, []);

        clipErrorCode = outErrorCode;
        reqProc = beginReqProc;
        if (clipErrorCode != MdcLibraryError.mdcNoErrorNoImage) {
          SystemSoundPlayer().play(SystemSoundEnum.invalid);
          _setReqProcClosingAndUpdateCanvasImageWhenHasError();
          Log.errorTrace("直線連続範囲選択失敗");
          return;
        }

        if (beginReqProc.status != InstructionProgressStatus.during) {
          Log.errorTrace(
              "editMdcAreaSelect実行後reqProc.status不正です。 実際値：${beginReqProc.status}  予期値：InstructionProgressStatus.during ");
        }
        return;
      case InstructionProgressStatus.during:
        final (
          errorCode: outErrorCode,
          reqProc: closingReqProc,
          objectInfo: outObjectInfo,
          imgInfoParts: imgInfoParts,
          imgInfoBackGround: imgInfoBackGround,
        ) = MdcLibrary()
            .apiBinding
            .editMdcAreaSelect(penTouchInfo, reqProc, 0, []);

        clipErrorCode = outErrorCode;

        switch (outErrorCode) {
          case MdcLibraryError.mdcNoError:
            if (closingReqProc.status != InstructionProgressStatus.closing) {
              Log.errorTrace(
                  "editMdcAreaSelect実行後reqProc.status不正です。 実際値：${closingReqProc.status}  予期値：InstructionProgressStatus.closing ");
            }
            reqProc = closingReqProc;
            originObjectInfo = objectInfo = outObjectInfo;
            setMdcBothImageInfo(
                updateParts: imgInfoParts,
                updateBackground: imgInfoBackGround,
                needUpdateUI: true);
            updateMdcSize();
            break;
          case MdcLibraryError.mdcNoErrorNoImage:
            Log.w(tag: '範囲選択', description: "mdcNoErrorNoImage");
            break;
          case MdcLibraryError.mdcErrorSelectedAreaNotImage:
            SystemSoundPlayer().play(SystemSoundEnum.invalid);
            _setReqProcClosingAndUpdateCanvasImageWhenHasError();
            Log.errorTrace("部分模様がなかった");
            break;
          case MdcLibraryError.mdcErrorDefault:
            SystemSoundPlayer().play(SystemSoundEnum.invalid);
            _setReqProcClosingAndUpdateCanvasImageWhenHasError();
            Log.errorTrace("内部処理エラー");
            break;
          default:
            SystemSoundPlayer().play(SystemSoundEnum.invalid);
            _setReqProcClosingAndUpdateCanvasImageWhenHasError();
            Log.errorTrace("範囲選択失敗");
            return;
        }
        return;

      case InstructionProgressStatus.closing:
      case InstructionProgressStatus.single:
      default:
        reqProc = reqProc.copyWith(status: InstructionProgressStatus.closing);
        clipErrorCode = MdcLibraryError.mdcNoError;
        Log.errorTrace("status不正です : state = ${reqProc.status}");
        return;
    }
  }

  ///
  /// 自由曲線選択
  ///
  void _freeCloseAreaSelect() {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.beginning);
    penTouchInfo = penTouchInfo.copyWith(
      posX: 0,
      posY: 0,
      pstat: MdcPenTouchStatus.press,
    );

    List<SSPoint> pointList = ClipBoardModel()
        .clipPositionList
        .map((offset) => SSPoint(X: offset.dx.toInt(), Y: offset.dy.toInt()))
        .toList();

    final (
      errorCode: errorCode,
      reqProc: outReqProc,
      objectInfo: outObjectInfo,
      imgInfoParts: imgInfoParts,
      imgInfoBackGround: imgInfoBackGround,
    ) = MdcLibrary()
        .apiBinding
        .editMdcAreaSelect(penTouchInfo, reqProc, pointList.length, pointList);

    if (errorCode != MdcLibraryError.mdcNoError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      _setReqProcClosingAndUpdateCanvasImageWhenHasError();
      Log.errorTrace("自由曲線範囲選択失敗");
      return;
    }

    if (outReqProc.status != InstructionProgressStatus.during) {
      Log.errorTrace(
          "editMdcAreaSelect実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
    }

    reqProc = outReqProc;
    originObjectInfo = objectInfo = outObjectInfo;
    setMdcBothImageInfo(
        updateParts: imgInfoParts,
        updateBackground: imgInfoBackGround,
        needUpdateUI: true);
    updateMdcSize();
    return;
  }

  ///
  /// 自動選択
  ///
  void _dropperSelectAreaSelect() {
    switch (reqProc.status) {
      case InstructionProgressStatus.beginning:
        final (
          errorCode: outErrorCode,
          reqProc: outReqProc,
          objectInfo: outObjectInfo,
          imgInfoParts: imgInfoParts,
          imgInfoBackGround: imgInfoBackGround,
        ) = MdcLibrary()
            .apiBinding
            .editMdcAreaSelect(penTouchInfo, reqProc, 0, []);

        if (outReqProc.status != InstructionProgressStatus.during) {
          Log.errorTrace(
              "editMdcAreaSelect実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
        }

        reqProc = outReqProc;
        clipErrorCode = outErrorCode;
        switch (outErrorCode) {
          case MdcLibraryError.mdcNoError:
            originObjectInfo = objectInfo = outObjectInfo;
            setMdcBothImageInfo(
                updateParts: imgInfoParts,
                updateBackground: imgInfoBackGround,
                needUpdateUI: true);
            updateMdcSize();
            break;
          case MdcLibraryError.mdcErrorSelectedAreaNotImage:
            SystemSoundPlayer().play(SystemSoundEnum.invalid);
            _setReqProcClosingAndUpdateCanvasImageWhenHasError();
            Log.errorTrace("editMdcAreaSelect : 部分模様がなかった");
            break;
          case MdcLibraryError.mdcErrorDefault:
            SystemSoundPlayer().play(SystemSoundEnum.invalid);
            _setReqProcClosingAndUpdateCanvasImageWhenHasError();
            Log.errorTrace("editMdcAreaSelect : 内部処理エラー");
            break;
          default:
            SystemSoundPlayer().play(SystemSoundEnum.invalid);
            _setReqProcClosingAndUpdateCanvasImageWhenHasError();
            Log.errorTrace("editMdcAreaSelect : 範囲選択失敗");
            break;
        }
        return;

      case InstructionProgressStatus.during:
      case InstructionProgressStatus.closing:
      case InstructionProgressStatus.single:
      default:
        reqProc = reqProc.copyWith(status: InstructionProgressStatus.closing);
        clipErrorCode = MdcLibraryError.mdcNoError;
        Log.errorTrace("status不正です : state = ${reqProc.status}");
        return;
    }
  }

  ///
  /// 自動選択
  ///
  void _allSelectAreaSelect() {
    switch (reqProc.status) {
      case InstructionProgressStatus.beginning:
        var (
          errorCode: outErrorCode,
          reqProc: outReqProc,
          objectInfo: outObjectInfo,
          imgInfoParts: imgInfoParts,
          imgInfoBackGround: imgInfoBackGround,
        ) = MdcLibrary()
            .apiBinding
            .editMdcAreaSelect(penTouchInfo, reqProc, 0, []);

        if (outReqProc.status != InstructionProgressStatus.during) {
          Log.errorTrace(
              "editMdcAreaSelect実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
        }

        reqProc = outReqProc;
        clipErrorCode = outErrorCode;

        switch (outErrorCode) {
          case MdcLibraryError.mdcNoError:
            originObjectInfo = objectInfo = outObjectInfo;
            setMdcBothImageInfo(
                updateParts: imgInfoParts,
                updateBackground: imgInfoBackGround,
                needUpdateUI: true);
            updateMdcSize();
            break;
          case MdcLibraryError.mdcErrorSelectedAreaNotImage:
            SystemSoundPlayer().play(SystemSoundEnum.invalid);
            _setReqProcClosingAndUpdateCanvasImageWhenHasError();
            Log.debugTrace("editMdcAreaSelect : 部分模様がなかった");
            break;
          case MdcLibraryError.mdcErrorDefault:
            SystemSoundPlayer().play(SystemSoundEnum.invalid);
            _setReqProcClosingAndUpdateCanvasImageWhenHasError();
            Log.debugTrace("editMdcAreaSelect : 内部処理エラー");
            break;
          default:
            SystemSoundPlayer().play(SystemSoundEnum.invalid);
            _setReqProcClosingAndUpdateCanvasImageWhenHasError();
            Log.debugTrace("自動範囲選択失敗");
            break;
        }
        return;

      case InstructionProgressStatus.during:
      case InstructionProgressStatus.closing:
      case InstructionProgressStatus.single:
      default:
        reqProc = reqProc.copyWith(status: InstructionProgressStatus.closing);
        clipErrorCode = MdcLibraryError.mdcNoError;
        Log.errorTrace("status不正です : state = ${reqProc.status}");
        return;
    }
  }

  ///
  /// 全選択ボタン処理
  ///
  void allSelectAreaSelect() {
    ClipBoardModel().confirmUnsettledObject(needUpdateUI: false);
    MdcLibrary()
        .apiBinding
        .setMdcDrawingType(MdcDrawingType.drawtype_area_select);
    penTouchInfo = penTouchInfo.copyWith(
      posX: 0,
      posY: 0,
      pstat: MdcPenTouchStatus.press,
    );
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.beginning);
    _allSelectAreaSelect();
  }

  ///
  /// サイズ調整の編集操作
  ///
  MdcLibraryError resizeUnsettledObject(int width, int height) {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
    objectInfo = objectInfo.copyWith(
      inst: MdcEditFormInstruct.resize,
      width: width,
      height: height,
    );

    final (
      errorCode: outErrorCode,
      unsettledObjectInfo: outObjectInfo,
      reqProc: outReqProc,
      imgInfoParts: imgInfoParts,
      imgInfoBackGround: imgInfoBackGround
    ) = MdcLibrary().apiBinding.editMdcUnsettledObject(objectInfo, reqProc);

    if (outErrorCode != MdcLibraryError.mdcNoError) {
      Log.errorTrace("editMdcUnsettledObject : 部分模様Resize編集操作失敗");
      return outErrorCode;
    }

    if (outReqProc.status != InstructionProgressStatus.during) {
      Log.errorTrace(
          "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
    }

    reqProc = outReqProc;
    objectInfo = outObjectInfo;
    setMdcBothImageInfo(
        updateParts: imgInfoParts,
        updateBackground: imgInfoBackGround,
        needUpdateUI: true);
    return MdcLibraryError.mdcNoError;
  }

  ///
  /// 回転の編集操作
  ///
  MdcLibraryError rotateUnsettledObject(double rotate) {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
    objectInfo = objectInfo.copyWith(
      inst: MdcEditFormInstruct.rotate,
      rotate: rotate,
    );

    final (
      errorCode: outErrorCode,
      unsettledObjectInfo: outObjectInfo,
      reqProc: outReqProc,
      imgInfoParts: imgInfoParts,
      imgInfoBackGround: imgInfoBackGround
    ) = MdcLibrary().apiBinding.editMdcUnsettledObject(objectInfo, reqProc);
    if (outErrorCode != MdcLibraryError.mdcNoError) {
      Log.errorTrace("editMdcUnsettledObject : 部分模様Rotate編集操作失敗");
      return outErrorCode;
    }

    if (outReqProc.status != InstructionProgressStatus.during) {
      Log.errorTrace(
          "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
    }

    reqProc = outReqProc;
    objectInfo = outObjectInfo;
    setMdcBothImageInfo(
        updateParts: imgInfoParts,
        updateBackground: imgInfoBackGround,
        needUpdateUI: true);
    return MdcLibraryError.mdcNoError;
  }

  ///
  /// 左右反転の編集操作
  ///
  MdcLibraryError reverseUnsettledObject() {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
    objectInfo = objectInfo.copyWith(inst: MdcEditFormInstruct.reverse);
    final (
      errorCode: outErrorCode,
      unsettledObjectInfo: outObjectInfo,
      reqProc: outReqProc,
      imgInfoParts: imgInfoParts,
      imgInfoBackGround: imgInfoBackGround
    ) = MdcLibrary().apiBinding.editMdcUnsettledObject(objectInfo, reqProc);
    if (outErrorCode != MdcLibraryError.mdcNoError) {
      Log.errorTrace("editMdcUnsettledObject : 部分模様左右反転編集操作失敗");
      return outErrorCode;
    }

    if (outReqProc.status != InstructionProgressStatus.during) {
      Log.errorTrace(
          "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
    }

    reqProc = outReqProc;
    objectInfo = outObjectInfo;
    setMdcBothImageInfo(
        updateParts: imgInfoParts,
        updateBackground: imgInfoBackGround,
        needUpdateUI: true);
    return MdcLibraryError.mdcNoError;
  }

  ///
  /// コピーの編集操作
  ///
  MdcLibraryError duplicateUnsettledObject(String clipFilename) {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
    objectInfo = objectInfo.copyWith(
      inst: MdcEditFormInstruct.duplicate,
      clipFilename: clipFilename,
    );

    final (
      errorCode: copyErrorCode,
      unsettledObjectInfo: outObjectInfo,
      reqProc: outReqProc,
      imgInfoParts: imgInfoParts,
      imgInfoBackGround: imgInfoBackGround
    ) = MdcLibrary().apiBinding.editMdcUnsettledObject(objectInfo, reqProc);

    if (copyErrorCode != MdcLibraryError.mdcNoError) {
      Log.errorTrace("editMdcUnsettledObject : コピー編集操作失敗");
      existClipboard = false;
      DeviceLibrary().apiBinding.setMdcClipboardState(false);
      return copyErrorCode;
    }

    if (outReqProc.status != InstructionProgressStatus.during) {
      Log.errorTrace(
          "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
    }

    reqProc = outReqProc;
    objectInfo = outObjectInfo;
    existClipboard = true;
    DeviceLibrary().apiBinding.setMdcClipboardState(true);
    setMdcBothImageInfo(
        updateParts: imgInfoParts,
        updateBackground: imgInfoBackGround,
        needUpdateUI: true);
    return MdcLibraryError.mdcNoError;
  }

  ///
  /// 切り取りの編集操作
  ///
  MdcLibraryError cutUnsettledObject(String clipFilename) {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
    objectInfo = objectInfo.copyWith(
      inst: MdcEditFormInstruct.cut,
      clipFilename: clipFilename,
    );

    final (
      errorCode: outErrorCode,
      unsettledObjectInfo: _,
      reqProc: outReqProc,
      imgInfoParts: _,
      imgInfoBackGround: imgInfoBackGround
    ) = MdcLibrary().apiBinding.editMdcUnsettledObject(objectInfo, reqProc);

    if (outErrorCode != MdcLibraryError.mdcNoError) {
      Log.errorTrace("editMdcUnsettledObject : 切り取り編集操作失敗");
      existClipboard = false;
      DeviceLibrary().apiBinding.setMdcClipboardState(false);
      return outErrorCode;
    }

    if (outReqProc.status != InstructionProgressStatus.closing) {
      Log.errorTrace(
          "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.closing ");
    }

    reqProc = outReqProc;
    existClipboard = true;
    DeviceLibrary().apiBinding.setMdcClipboardState(true);
    setMdcBothImageInfo(
        updateParts: ImageInfo.empty(),
        updateBackground: imgInfoBackGround,
        needUpdateUI: true);
    return MdcLibraryError.mdcNoError;
  }

  ///
  /// 貼り付け(選択中)の編集操作
  ///
  MdcLibraryError pasteUnsettledObject(String clipFilename) {
    MdcLibrary()
        .apiBinding
        .setMdcDrawingType(MdcDrawingType.drawtype_area_select);

    reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
    objectInfo = objectInfo.copyWith(
      inst: MdcEditFormInstruct.paste,
      clipFilename: clipFilename,
    );

    /// 模様選択中の貼り付け
    if (hasPartsImageInfo()) {
      final (
        errorCode: outErrorCode,
        unsettledObjectInfo: outObject,
        reqProc: outReqProc,
        imgInfoParts: imgInfoParts,
        imgInfoBackGround: imgInfoBackGround
      ) = MdcLibrary().apiBinding.editMdcUnsettledObject(objectInfo, reqProc);

      if (outErrorCode != MdcLibraryError.mdcNoError) {
        Log.errorTrace("editMdcUnsettledObject : お絵描き選択の状態で貼り付け(選択中)の編集操作失敗");
        existClipboard = false;
        DeviceLibrary().apiBinding.setMdcClipboardState(false);
        return outErrorCode;
      }

      if (outReqProc.status != InstructionProgressStatus.during) {
        Log.errorTrace(
            "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
      }

      reqProc = outReqProc;
      objectInfo = outObject;
      setMdcBothImageInfo(
          updateParts: imgInfoParts,
          updateBackground: imgInfoBackGround,
          needUpdateUI: true);
      updateMdcSize();
      return MdcLibraryError.mdcNoError;
    }

    /// 部分模様なし貼り付け
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.beginning);
    final (
      errorCode: outErrorCode,
      unsettledObjectInfo: outObject,
      reqProc: outReqProc,
      imgInfoParts: imgInfoParts,
      imgInfoBackGround: imgInfoBackGround
    ) = MdcLibrary()
        .apiBinding
        .editMdcUnsettledObjectNewPaste(objectInfo, reqProc);

    if (outErrorCode != MdcLibraryError.mdcNoError) {
      Log.errorTrace(
          "editMdcUnsettledObjectNewPaste : お絵描き未選択の状態で貼り付け(選択中)編集操作失敗");
      return outErrorCode;
    }

    if (outReqProc.status != InstructionProgressStatus.during) {
      Log.errorTrace(
          "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
    }

    reqProc = outReqProc;
    objectInfo = outObject;
    setMdcBothImageInfo(
        updateParts: imgInfoParts,
        updateBackground: imgInfoBackGround,
        needUpdateUI: true);
    updateMdcSize();
    return MdcLibraryError.mdcNoError;
  }

  ///
  /// 移動の編集操作
  ///
  MdcLibraryError moveUnsettledObject(int posX, int posY) {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
    objectInfo = objectInfo.copyWith(
      inst: MdcEditFormInstruct.move,
      centerPosX: posX,
      centerPosY: posY,
    );

    final (
      errorCode: outErrorCode,
      unsettledObjectInfo: outObjectInfo,
      reqProc: outReqProc,
      imgInfoParts: imgInfoParts,
      imgInfoBackGround: imgInfoBackGround
    ) = MdcLibrary().apiBinding.editMdcUnsettledObject(objectInfo, reqProc);
    if (outErrorCode != MdcLibraryError.mdcNoError) {
      Log.errorTrace("editMdcUnsettledObject : 移動編集操作失敗");
      return outErrorCode;
    }

    if (outReqProc.status != InstructionProgressStatus.during) {
      Log.errorTrace(
          "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
    }

    reqProc = outReqProc;
    objectInfo = outObjectInfo;
    setMdcBothImageInfo(
        updateParts: imgInfoParts,
        updateBackground: imgInfoBackGround,
        needUpdateUI: true);
    return MdcLibraryError.mdcNoError;
  }

  ///
  /// サイズリセットの前準備としてサイズを変更する
  ///
  MdcLibraryError sizeResetUnsettledObject() {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
    objectInfo = objectInfo.copyWith(
      inst: MdcEditFormInstruct.sizeReset,
      width: originObjectInfo.width,
      height: originObjectInfo.height,
    );
    final (
      errorCode: outErrorCode,
      unsettledObjectInfo: outObjectInfo,
      reqProc: outReqProc,
      imgInfoParts: imgInfoParts,
      imgInfoBackGround: imgInfoBackGround
    ) = MdcLibrary().apiBinding.editMdcUnsettledObject(objectInfo, reqProc);
    if (outErrorCode != MdcLibraryError.mdcNoError) {
      Log.errorTrace("editMdcUnsettledObject : サイズリセット編集操作失敗");
      return outErrorCode;
    }

    if (outReqProc.status != InstructionProgressStatus.during) {
      Log.errorTrace(
          "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
    }

    reqProc = outReqProc;
    objectInfo = outObjectInfo;
    setMdcBothImageInfo(
        updateParts: imgInfoParts,
        updateBackground: imgInfoBackGround,
        needUpdateUI: true);
    updateMdcSize();
    return MdcLibraryError.mdcNoError;
  }

  ///
  /// 角度リセット
  ///
  MdcLibraryError rotateResetUnsettledObject() {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
    objectInfo = objectInfo.copyWith(
      inst: MdcEditFormInstruct.rotateReset,
      rotate: originObjectInfo.rotate,
    );
    final (
      errorCode: outErrorCode,
      unsettledObjectInfo: outObjectInfo,
      reqProc: outReqProc,
      imgInfoParts: imgInfoParts,
      imgInfoBackGround: imgInfoBackGround
    ) = MdcLibrary().apiBinding.editMdcUnsettledObject(objectInfo, reqProc);
    if (outErrorCode != MdcLibraryError.mdcNoError) {
      Log.errorTrace("editMdcUnsettledObject : 角度リセット編集操作失敗");
      return outErrorCode;
    }

    if (outReqProc.status != InstructionProgressStatus.during) {
      Log.errorTrace(
          "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
    }

    reqProc = outReqProc;
    objectInfo = outObjectInfo;
    setMdcBothImageInfo(
        updateParts: imgInfoParts,
        updateBackground: imgInfoBackGround,
        needUpdateUI: true);
    return MdcLibraryError.mdcNoError;
  }

  ///
  /// 位置リセットの前準備として位置を変更する
  ///
  MdcLibraryError moveResetUnsettledObject() {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
    objectInfo = objectInfo.copyWith(
      inst: MdcEditFormInstruct.moveReset,
      centerPosX: originObjectInfo.centerPosX,
      centerPosY: originObjectInfo.centerPosY,
    );
    final (
      errorCode: outErrorCode,
      unsettledObjectInfo: outObjectInfo,
      reqProc: outReqProc,
      imgInfoParts: imgInfoParts,
      imgInfoBackGround: imgInfoBackGround
    ) = MdcLibrary().apiBinding.editMdcUnsettledObject(objectInfo, reqProc);
    if (outErrorCode != MdcLibraryError.mdcNoError) {
      Log.errorTrace("editMdcUnsettledObject : 位置リセット編集操作失敗");
      return outErrorCode;
    }

    if (outReqProc.status != InstructionProgressStatus.during) {
      Log.errorTrace(
          "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
    }

    reqProc = outReqProc;
    objectInfo = outObjectInfo;
    setMdcBothImageInfo(
        updateParts: imgInfoParts,
        updateBackground: imgInfoBackGround,
        needUpdateUI: true);
    return MdcLibraryError.mdcNoError;
  }

  ///
  /// 部分模様の確定(切り取り以外)
  ///
  void confirmUnsettledObject({required bool needUpdateUI}) {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.closing);
    objectInfo = objectInfo.copyWith(inst: MdcEditFormInstruct.move);
    ResumeHistoryModel().setEnableShort(true);

    final (
      errorCode: outErrorCode,
      unsettledObjectInfo: _,
      reqProc: outReqProc,
      imgInfoParts: _,
      imgInfoBackGround: imgInfoBackGround,
    ) = MdcLibrary().apiBinding.editMdcUnsettledObject(objectInfo, reqProc);

    if (outErrorCode != MdcLibraryError.mdcNoError) {
      Log.errorTrace("editMdcUnsettledObject : 部分模様の確定失敗");
      clipErrorCode = outErrorCode;
      reqProc = outReqProc;
      setMdcPartsImageInfo(MdcImageInfo.empty(), needUpdateUI: false);
      updateCanvasImageWithoutParts();
      return;
    }

    if (outReqProc.status != InstructionProgressStatus.closing) {
      Log.errorTrace(
          "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.closing ");
    }

    clipErrorCode = MdcLibraryError.mdcNoError;
    reqProc = outReqProc;
    setMdcBothImageInfo(
        updateParts: ImageInfo.empty(),
        updateBackground: imgInfoBackGround,
        needUpdateUI: needUpdateUI);
  }

  ///
  /// 部分模様
  ///
  MdcImageInfo getMdcPartsImageInfo() => _workImageInfoParts;
  void setMdcPartsImageInfo(MdcImageInfo imageInfo,
      {required bool needUpdateUI}) {
    setMdcBothImageInfo(
        updateParts: imageInfo,
        updateBackground: _workImgInfoBackGround,
        needUpdateUI: needUpdateUI);
  }

  ///
  /// 背景模様
  ///
  MdcImageInfo getMdcBackGroundImageInfo() => _workImgInfoBackGround;
  void setMdcBackGroundImageInfo(MdcImageInfo imageInfo,
      {required bool needUpdateUI}) {
    setMdcBothImageInfo(
        updateParts: _workImageInfoParts,
        updateBackground: imageInfo,
        needUpdateUI: needUpdateUI);
  }

  ///
  /// 全体模様
  ///
  void setMdcBothImageInfo(
      {required MdcImageInfo updateParts,
      required MdcImageInfo updateBackground,
      required bool needUpdateUI}) {
    _workImageInfoParts = updateParts;
    _workImgInfoBackGround = updateBackground;
    if (needUpdateUI) {
      DrawCanvasModel().updateUiImageInfoCallBack?.call();
    }
  }

  ///
  /// 部分模様表示位置更新
  ///
  void updateMdcPartsImageInfoPosition(ui.Offset offset) {
    /// 開始点変更
    _workImageInfoParts = _workImageInfoParts.copyWith(
      startPointX: (_workImageInfoParts.startPointX + offset.dx).round(),
      startPointY: (_workImageInfoParts.startPointY + offset.dy).round(),
    );

    /// 中心点変更
    final parts = _workImageInfoParts;
    ui.Rect rect = ui.Rect.fromLTWH(
      parts.startPointX.toDouble(),
      parts.startPointY.toDouble(),
      parts.imageWidth.toDouble(),
      parts.imageHeight.toDouble(),
    );
    objectInfo = objectInfo.copyWith(
        centerPosX: rect.center.dx.toInt(), centerPosY: rect.center.dy.toInt());
  }

  ///
  /// リセット
  ///
  void reset() {
    objectInfo = MdcUnsettledObjectInfo();
    penTouchInfo = MdcPenTouchInfo();
    reqProc = const MdcReqProc();
    allClear();
  }

  void allClear() {
    clipErrorCode = MdcLibraryError.mdcNoError;
    isStampParts = false;
    existBackImage = false;
    existClipboard = false;

    setMdcBothImageInfo(
        updateParts: MdcImageInfo.empty(),
        updateBackground: MdcImageInfo.empty(),
        needUpdateUI: true);
    SizeAdjustmentModel().sizeInput = const MdcSize(width: 0, height: 0);
    ClipBoardModel().clipBoardProperty = ClipBoardProperty();
    ClipBoardModel().clipBoardState = ClipBoardState.init();
    DeviceLibrary().apiBinding.setMdcBackgroundState(false);
    DeviceLibrary().apiBinding.setMdcClipboardState(false);
  }

  ///
  /// サイズ調整
  ///
  void updateMdcSize() {
    SizeAdjustmentModel().sizeInput = MdcSize(
      width: objectInfo.width * ratio,
      height: objectInfo.height * ratio,
    );
  }

  ///
  /// 濃度変更
  ///
  void densityChange() {
    if (hasPartsImageInfo()) {
      _updateDensityWithParts();
    } else {
      updateCanvasImageWithoutParts();
    }
  }

  ///
  /// 濃度変更の場合、部分模様ある画面模様更新
  ///
  void _updateDensityWithParts() {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.during);
    objectInfo = objectInfo.copyWith(inst: MdcEditFormInstruct.densityChange);

    final (
      errorCode: outErrorCode,
      unsettledObjectInfo: outObjectInfo,
      reqProc: outReqProc,
      imgInfoParts: imgInfoParts,
      imgInfoBackGround: imgInfoBackGround
    ) = MdcLibrary().apiBinding.editMdcUnsettledObject(objectInfo, reqProc);
    if (outErrorCode != MdcLibraryError.mdcNoError) {
      Log.e(tag: "エラー", description: "編集操作失敗");
      return;
    }

    if (outReqProc.status != InstructionProgressStatus.during) {
      Log.errorTrace(
          "editMdcUnsettledObject実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
    }

    reqProc = MdcReqProc(
        procId: outReqProc.procId, status: InstructionProgressStatus.during);
    objectInfo = outObjectInfo;
    setMdcBothImageInfo(
        updateParts: imgInfoParts,
        updateBackground: imgInfoBackGround,
        needUpdateUI: true);
  }

  ///
  /// 部分模様なしの場合、画面模様更新
  ///
  void updateCanvasImageWithoutParts() {
    final isSingleState = _isSingleState();
    if (isSingleState) {
      EditObjectModel().reqProc = EditObjectModel()
          .reqProc
          .copyWith(status: InstructionProgressStatus.single);
    } else {
      EditObjectModel().reqProc = EditObjectModel()
          .reqProc
          .copyWith(status: InstructionProgressStatus.during);
    }

    var (
      errorCode: _,
      reqProc: outReqProc,
      backGroundImageInfo: backGroundImageInfo
    ) = MdcLibrary()
        .apiBinding
        .getMdcCurrentDrawingImage(EditObjectModel().reqProc);

    if (outReqProc.status != InstructionProgressStatus.single &&
        isSingleState) {
      Log.errorTrace(
          "getMdcCurrentDrawingImage実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.single ");
    } else if (outReqProc.status != InstructionProgressStatus.during &&
        isSingleState == false) {
      Log.errorTrace(
          "getMdcCurrentDrawingImage実行後reqProc.status不正です。 実際値：${outReqProc.status}  予期値：InstructionProgressStatus.during ");
    } else {
      /// Do Nothing
    }

    reqProc = outReqProc;
    setMdcBackGroundImageInfo(backGroundImageInfo, needUpdateUI: true);
  }

  ///
  /// リクエストプロセス状態の判断
  ///
  bool _isSingleState() {
    switch (DrawingTypeModel.mdcDrawingType) {
      /// 描画種別が線
      case MdcDrawingType.drawtype_pencil:
      case MdcDrawingType.drawtype_line_fill:
      case MdcDrawingType.drawtype_line_color:

      /// 描画種別が面
      case MdcDrawingType.drawtype_blush:
      case MdcDrawingType.drawtype_surface_fill:
      case MdcDrawingType.drawtype_surface_color:
        return true;

      /// 描画種別「消しゴム」、描画種別「範囲選択」かつ部分模様未選択
      case MdcDrawingType.drawtype_eraser:
      case MdcDrawingType.drawtype_area_select:
        if (EditObjectModel().getMdcPartsImageInfo().imageData.isEmpty) {
          return true;
        } else {
          return false;
        }

      default:
        return false;
    }
  }

  ///
  /// 範囲取得失敗の場合の処理
  ///
  void _setReqProcClosingAndUpdateCanvasImageWhenHasError() {
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.closing);
    updateCanvasImageWithoutParts();
  }

  ///
  /// 保存ファイル
  ///
  void _saveOrCovertHistoryFile() {
    /// 前回部分模様存在しない、今回部分模様作成完了
    if (_canBeCovertFile == false &&
        clipErrorCode == MdcLibraryError.mdcNoError) {
      ResumeHistoryModel().backSnapshot();
    }

    /// 前回部分模様存在し、今回の変更履歴で前回の変更履歴を上書きしてください
    if (_canBeCovertFile && clipErrorCode == MdcLibraryError.mdcNoError) {
      ResumeHistoryModel().coverSnapshot();
    }
    _canBeCovertFile = false;
  }

  ///
  /// 保存ファイル
  ///
  void _saveOrCovertLineMultiHistoryFile() {
    /// 描画の点の保存
    if (_canBeCovertFile == false &&
        (clipErrorCode == MdcLibraryError.mdcNoErrorNoImage ||
            clipErrorCode == MdcLibraryError.mdcNoError)) {
      ResumeHistoryModel().backSnapshot();
    }

    /// 前回部分模様存在し、今回の変更履歴で前回の変更履歴を上書きしてください
    if (_canBeCovertFile &&
        clipErrorCode == MdcLibraryError.mdcNoErrorNoImage) {
      ResumeHistoryModel().coverSnapshot();
    }
    _canBeCovertFile = false;
  }
}
