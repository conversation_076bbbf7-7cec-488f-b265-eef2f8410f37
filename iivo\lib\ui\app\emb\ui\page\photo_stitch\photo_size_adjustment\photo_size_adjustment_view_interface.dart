import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'photo_size_adjustment_view_interface.freezed.dart';

@freezed
class PhotoSizeAdjustmentState with _$PhotoSizeAdjustmentState {
  const factory PhotoSizeAdjustmentState({
    @Default(false) bool isInch,
    @Default('----') String widthValue,
    @Default('----') String heightValue,
    @Default(0.0) double imageWidth,
    @Default(0.0) double imageHeight,
    @Default(null) Image? contentImage,
  }) = _PhotoSizeAdjustmentState;
}

abstract class PhotoSizeAdjustmentViewInterface
    extends ViewModel<PhotoSizeAdjustmentState> {
  PhotoSizeAdjustmentViewInterface(super.state);

  ///
  /// 背景除去ボタンをクリックする
  ///
  void onBackgroundRemoveButtonClicked(BuildContext context);

  ///
  /// FrameFitボタンをクリックする
  ///
  void onFrameFitButtonClicked(BuildContext context);

  ///
  /// マスクボタンをクリックする
  ///
  void onMaskButtonClicked(BuildContext context);

  ///
  /// 均等拡大キーのクリック関数
  ///
  bool onXYEnlargeButtonClicked(bool isLongPress);

  ///
  /// 水平方向拡大キーのクリック関数
  ///
  bool onXEnlargeButtonClicked(bool isLongPress);

  ///
  /// 垂直方向拡大キーのクリック関数
  ///
  bool onYEnlargeButtonClicked(bool isLongPress);

  ///
  /// 均等縮小キーのクリック関数
  ///
  bool onXYReduceButtonClicked(bool isLongPress);

  ///
  /// 水平方向縮小キーのクリック関数
  ///
  bool onXReduceButtonClicked(bool isLongPress);

  ///
  /// 垂直方向縮小キーのクリック関数
  ///
  bool onYReduceButtonClicked(bool isLongPress);

  ///
  /// 元に戻すキーのクリック関数
  ///
  void onResetButtonClicked();

  ///
  /// nextボタンクリック
  ///
  void onNextButtonClicked();

  ///
  /// returnボタンクリック
  ///
  void onReturnButtonClicked();

  ///
  /// ボタンを離すとデータが更新されます
  ///
  void onTapeCancel();

  ///
  /// 名前付きルートの登録
  ///
  Map<String, PopupRouteBuilder> registerNamedPopup();

  ///
  ///時計回りに90度回転
  ///
  void onRotateRight90();

  ///
  /// 画像表示領域を取得する
  ///
  Rect getContentImageRect();

  ///
  /// 領域を表すラジアンを取得します
  ///
  double getContentImageRadius();

  ///
  /// 選択するかどうかを判断する円です
  ///
  bool get isCircle;
}
