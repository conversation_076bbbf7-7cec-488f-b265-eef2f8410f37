import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_decorative_fill_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'size_setting_popup_view_interface.dart';

final sizeSettingPopupViewModelProvider = StateNotifierProvider.autoDispose<
    SizeSettingPopupStateViewInterface,
    SizeSettingPopupState>((ref) => SizeSettingPopupViewModel(ref));

class SizeSettingPopupViewModel extends SizeSettingPopupStateViewInterface {
  SizeSettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const SizeSettingPopupState(), ref) {
    _sizeState =
        SurfaceDecoFillModel().getSize() != SurfaceDecoFillModel.sizeNotUpdating
            ? DecoFillSettingState.settingCompleted
            : DecoFillSettingState.unknown;

    /// view更新
    update();
  }

  ///
  /// 最大密度値
  ///
  final int _maxSizeValue = 200;

  ///
  /// 最小密度値
  ///
  final int _minSizeValue = 50;

  ///
  /// ステップ量
  ///
  final int _stepValue = 5;

  ///
  /// 倍率/サイズの状態
  ///
  DecoFillSettingState _sizeState = DecoFillSettingState.settingCompleted;

  ///
  /// サイズ値
  ///
  int _sizeValue = SurfaceDecoFillModel().getSize();

  @override
  void update() {
    state = state.copyWith(
      settingValue: _getSizeValue(),
      isMainValue: _isMinValue(),
      isMaxValue: _isMaxValue(),
    );
  }

  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_sizeState == DecoFillSettingState.unknown) {
      _sizeValue = SurfaceDecoFillModel().sizeDefaultValue;
    } else {
      _sizeValue = _sizeValue - _stepValue;
      if (_sizeValue < _minSizeValue) {
        _sizeValue = _minSizeValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }

    _sizeState = DecoFillSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    state = state.copyWith(
      settingValue: _getSizeValue(),
      isMaxValue: false,
      isMainValue: _sizeValue == _minSizeValue ? true : false,
    );
    return true;
  }

  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_sizeState == DecoFillSettingState.unknown) {
      _sizeValue = SurfaceDecoFillModel().sizeDefaultValue;
    } else {
      _sizeValue = _sizeValue + _stepValue;
      if (_sizeValue > _maxSizeValue) {
        _sizeValue = _maxSizeValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }

    _sizeState = DecoFillSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    state = state.copyWith(
      settingValue: _getSizeValue(),
      isMaxValue: _sizeValue == _maxSizeValue ? true : false,
      isMainValue: false,
    );
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.surfaceDecorativeFillSize.toString());
    if (_sizeState == DecoFillSettingState.unknown) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    /// sizeの初期値
    int oldSizeValue = SurfaceDecoFillModel().getSize();

    /// Model更新
    SurfaceDecoFillModel().setSize(_sizeValue);
    if (SurfaceDecoFillModel().setDecorativeFillSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (_sizeValue != oldSizeValue) {
      ResumeHistoryModel().backSnapshot();
    } else {
      /// do nothing
    }

    _sizeState = DecoFillSettingState.settingCompleted;
    CreationModel().changeStitchCreation();
  }

  @override
  bool getSizeTextStyle() {
    if (_sizeState == DecoFillSettingState.unknown) {
      return true;
    }

    if (_sizeValue == SurfaceDecoFillModel().sizeDefaultValue) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 表示値を取得する
  ///
  String _getSizeValue() {
    if (_sizeState == DecoFillSettingState.unknown) {
      return SurfaceDecoFillModel.oneStarValue;
    } else {
      return _sizeValue.toString();
    }
  }

  ///
  /// 最大値の判断
  ///
  bool _isMaxValue() {
    if (_sizeState == DecoFillSettingState.unknown) {
      return false;
    } else {
      return _sizeValue == _maxSizeValue ? true : false;
    }
  }

  ///
  /// 最小値の判断
  ///
  bool _isMinValue() {
    if (_sizeState == DecoFillSettingState.unknown) {
      return false;
    } else {
      return _sizeValue == _minSizeValue ? true : false;
    }
  }
}
