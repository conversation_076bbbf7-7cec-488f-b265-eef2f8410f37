import 'dart:io'
    show
        FileSystemException,
        IOException,
        PathAccessException,
        PathNotFoundException;

import 'package:log/log.dart';
import '../../memory_interface.dart';

const String memoryAccessErrorLogTag = "memoryAccessErrorLog";

///
/// 外部机器に保存するが机器のスペースが足りない場合IOExceptionで捕捉できる例外です
/// errorというエラーが返ってきます
/// No space left on device.
///
const int _eNoSpace = 28;

///
/// メモリーへ書き出し発生状態
///
enum AccessError {
  /// メモリーFull
  memoryFull,

  /// パスが存在しません
  pathNotFoundException,

  /// ファイルが異常に書き込まれました
  pathAccessWriteException,

  /// ファイルの削除が異常です
  pathAccessDeleteException,

  /// ファイル破損異常です
  fileCorruptionException,

  /// ファイル保存エラー
  fileSaveInvalidException,

  /// Internalエラー
  internalException,

  /// ライブラリ側のメモリフル
  libSaveFileMemoryFull,

  /// その他
  other,

  /// エラーなし
  none,
}

///
/// メモリ読み込みの返却の種類
///
class MemoryAccessResponse<T> {
  MemoryAccessResponse({required this.error, required this.data});
  AccessError error;
  T data;
}

///
/// 例外[e]が記憶領域不足の例外であるかどうかを判定する
///
bool isMemoryFullException(Object e) =>
    e is FileSystemException &&
    (e is FullMemoryException || e.osError?.errorCode == _eNoSpace);

///
/// memoryアクサスのExceptionについて エラー状態を計算する
/// [bool] isWriteError : 書き込み操作かどうか
///
AccessError memoryExceptionToAccessError(IOException e,
    {bool isWriteError = true}) {
  Log.d(tag: memoryAccessErrorLogTag, description: e.toString());

  /// FileSystemException以外のException
  if (e is! FileSystemException) {
    return AccessError.other;
  }

  /// メモリーFullのException
  if (isMemoryFullException(e)) {
    return AccessError.memoryFull;
  }

  /// アクションは許可されません。
  /// これを行うことができるのは、ファイル (またはその他のリソース) の所有者、または特別な権限を持つプロセスだけです
  if (e is PathAccessException) {
    if (isWriteError) {
      return AccessError.pathAccessWriteException;
    } else {
      return AccessError.pathAccessDeleteException;
    }
  }

  /// そのようなファイルやディレクトリはありません。
  /// これは、既に存在すると予想されるコンテキストで参照される通常のファイルに対する "ファイルが存在しません" というエラーです
  if (e is PathNotFoundException) {
    return AccessError.pathNotFoundException;
  } else {
    return AccessError.other;
  }
}
