import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import '../../../../../../model/color_change_model.dart';
import '../../../../../../model/pattern_model.dart';
import '../../../../../../model/thread_color_list_model.dart';
import '../color_change_view_model.dart';
import 'thread_color_list_interface.dart';

final threadColorListViewModelProvider = StateNotifierProvider.autoDispose<
    ThreadColorListViewModelInterface,
    ThreadColorListState>((ref) => ThreadColorListViewModel(ref));

class ThreadColorListViewModel extends ThreadColorListViewModelInterface {
  ThreadColorListViewModel(Ref ref)
      : super(
            ThreadColorListState(
              displayList: [],
              scrollController: <PERSON>rollController(),
              isAllSelect: false,
            ),
            ref) {
    /// Lib更新
    ColorChangeModel().selectPartsInGroupColorChange(
      ColorChangeModel().selectPatternIndex,
      ColorChangeModel().selectColorIndex,
    );

    /// Modelの更新
    ColorChangeModel().isAllSelect = false;
    ColorChangeModel().updateDisplayList(
      selectedPatternIndex: ColorChangeModel().selectPatternIndex,
      selectedColorItemIndex: ColorChangeModel().selectColorIndex,
      isAllSelectOn: ColorChangeModel().isAllSelect,
      zoomScale: PatternModel().selectedZoomScaleInEditPage,
    );

    /// Viewの更新
    state = state.copyWith(
      thumbnailImage: ColorChangeModel().getThumbnailImage(),
      isAllSelect: ColorChangeModel().isAllSelect,
      displayList: ColorChangeModel().displayList,
      scrollController: ScrollController(
        initialScrollOffset: _getJumpPositioned(
          ColorChangeModel().displayList,
          ColorChangeModel().selectPatternIndex,
          ColorChangeModel().selectColorIndex,
          ColorChangeModel().isAllSelect,
        ),
      ),
    );
  }

  @override
  void update() {
    ColorChangeModel().updateDisplayList(
      selectedPatternIndex: ColorChangeModel().selectPatternIndex,
      selectedColorItemIndex: ColorChangeModel().selectColorIndex,
      isAllSelectOn: ColorChangeModel().isAllSelect,
      zoomScale: PatternModel().selectedZoomScaleInEditPage,
    );

    /// stateの更新
    state = state.copyWith(
      thumbnailImage: ColorChangeModel().getThumbnailImage(),
      isAllSelect: ColorChangeModel().isAllSelect,
      displayList: ColorChangeModel().displayList,
    );

    if (ColorChangeModel().isColorShuffling) {
      state.scrollController.jumpTo(_getJumpPositioned(
          state.displayList,
          ColorChangeModel().selectPatternIndex,
          ColorChangeModel().selectColorIndex,
          ColorChangeModel().isAllSelect));
    }
  }

  @override
  void onColorItemClicked(int patternIndex, int itemIndex) {
    if (ColorChangeModel().isAllSelect == true &&
        ColorChangeModel().selectPatternIndex == patternIndex) {
      return;
    }

    /// Modelの更新
    ThreadColorListModel().selectedGroupIndex = patternIndex;
    ColorChangeModel().selectPatternIndex = patternIndex;
    ColorChangeModel().selectColorIndex = itemIndex;
    ColorChangeModel().selectPartsInGroupColorChange(
        ColorChangeModel().selectPatternIndex,
        ColorChangeModel().selectColorIndex);

    update();

    /// 他のページへの更新の通知
    ref
        .read(colorChangeViewModelProvider.notifier)
        .updateColorChangeByChild(ModuleType.colorChangeThreadList);
  }

  @override
  void onSelectModeButtonClicked() {
    ColorChangeModel().isAllSelect = !ColorChangeModel().isAllSelect;
    ColorChangeModel().selectColorIndex = 0;

    ColorChangeModel().selectPartsInGroupColorChange(
        ColorChangeModel().selectPatternIndex,
        ColorChangeModel().selectColorIndex);

    /// スクロールバーの更新
    if (ColorChangeModel().isAllSelect) {
      state.scrollController.jumpTo(
        _getJumpPositioned(
            state.displayList,
            ColorChangeModel().selectPatternIndex,
            ColorChangeModel().selectColorIndex,
            ColorChangeModel().isAllSelect),
      );
    }

    update();

    /// 他のページへの更新の通知
    ref
        .read(colorChangeViewModelProvider.notifier)
        .updateColorChangeByChild(ModuleType.colorChangeThreadList);
  }

  ///
  /// リスト内でジャンプ可能な座標の計算
  ///
  double _getJumpPositioned(
    List<ColorListDisplayInfo> displayList,
    int patternItemIndex,
    int colorItemIndex,
    bool isAllSelect,
  ) {
    /// リスト領域の高さ
    const double listAreaHeight = 499.0;

    /// Itemの間隔
    const double itemSpace = 8;

    /// 線色リストのItemの高さ
    const double patternItemHeight = 134.0 + itemSpace;

    /// カラーリストのItemの高さ
    const double threadItemHeight = 63.0 + itemSpace;

    /// ジャンプ可能な最大座標
    double maxJumpPositioned;

    /// リストの最大高さ
    double maxListHeight = 0.0;

    /// ジャンプの位置値
    double positioned = 0.0;

    for (int index = 0; index < displayList.length; index++) {
      maxListHeight += patternItemHeight +
          (displayList[index].threadInfoDisplayList.length * threadItemHeight);

      ///ジャンプの具体的な位置値の計算
      if (index < patternItemIndex) {
        positioned += patternItemHeight +
            (displayList[index].threadInfoDisplayList.length *
                threadItemHeight);
      }
      if (isAllSelect == false && (index == patternItemIndex)) {
        positioned += patternItemHeight + (colorItemIndex * threadItemHeight);
      }
    }

    if (maxListHeight < listAreaHeight) {
      /// リストの全長が表示領域を超えていない場合
      maxJumpPositioned = 0;
    } else {
      /// リストの全長が表示領域を超えた場合
      maxJumpPositioned = maxListHeight - listAreaHeight;
    }

    return positioned.clamp(0, maxJumpPositioned);
  }
}
