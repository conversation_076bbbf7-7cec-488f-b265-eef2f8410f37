import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'top_bar_view_interface.freezed.dart';

enum MoveType {
  topLeft,
  top,
  topRight,
  left,
  right,
  bottomLeft,
  bottom,
  bottomRight
}

@freezed
class TopBarState with _$TopBarState {
  const factory TopBarState({
    @Default(0) int magnificationIndex,
    @Default(ButtonState.disable) ButtonState dragMoveButtonState,
    @Default(ButtonState.disable) ButtonState informationButtonState,
    @Default(ButtonState.disable) ButtonState projectorButtonState,
    @Default(false) bool isDensityShow,
    @Default(1) int densityLevelIndex,
    @Default(false) bool isProjectorON,
  }) = _TopBarState;
}

abstract class TopBarViewInterface extends ViewModel<TopBarState>
    with DeviceLibraryEventObserver {
  TopBarViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// ステッチ展開処理かどうか
  ///
  bool get isProcessImage;

  ///
  /// 下絵を取り込むかどうかの判断
  ///
  bool get isDensityShow;

  ///
  /// 拡大レベルの取得
  ///
  Widget get getMagnificationLevel;

  ///
  /// インフォメーションボタンをクリックする
  ///
  void onInformationButtonClicked(BuildContext context);

  ///
  /// 下絵　濃いのクリック関数
  ///
  void onDensityAddButtonClicked();

  ///
  /// 下絵　薄いのクリック関数
  ///
  void onDensityReduceButtonClicked();

  ///
  /// 拡大倍率ボタンのクリック関数
  ///
  void onMagnificationButtonClicked(BuildContext context);

  ///
  /// ドラッグ移動ボタンがクリックされました
  ///
  void onDragMoveButtonClicked();

  ///
  /// View更新
  ///
  @override
  void update();

  ///
  /// projectorButton状態更新
  ///
  void updateProjectorButtonState();

  ///
  /// プロジェクターボタンをクリック
  ///
  void onProjectorButtonClick(BuildContext context);

  ///
  /// [Projection Settings] ボタンでイベントをクリック
  ///
  void onProjectorSettingButtonClick(BuildContext context);

  void onRealPreviewButtonClicked();
}
