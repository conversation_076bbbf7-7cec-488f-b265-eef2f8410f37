import 'package:flutter/material.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../model/thread_color_model.dart';
import '../../../../model/user_data/user_data.dart';
import '../../../../model/user_data/user_data_keys.dart';
import 'pattern_model.dart';
import 'thread_color_list_model.dart';
import 'thread_model.dart';

class AppliqueModel {
  AppliqueModel._internal();
  factory AppliqueModel() {
    if (_instance._isInitialized == false) {
      _instance._isInitialized = true;
    }
    return _instance;
  }

  bool _isInitialized = false;
  static final AppliqueModel _instance = AppliqueModel._internal();

  static AppliqueOutlineMode appliquePreviewType =
      AppliqueOutlineMode.appOutlineModeEx;

  static AppliqueSatinKind appliqueExternalStitchType =
      AppliqueSatinKind.appSatinKindTacDown;

  static AppliqueRunKind appliqueInternalStitchType =
      AppliqueRunKind.appRunKindRun;

  static bool isNextState = false;

  ///
  /// 初期間隔値
  ///
  final int outLineDistanceDefaultValue = 30;

  ///
  /// 密度mm単位の値
  ///
  static List<double> stitchDensityMmValue = [
    0.25,
    0.50,
    0.75,
    1.00,
    1.25,
    1.50,
    1.75,
    2.00,
    2.50,
    3.00,
    3.50,
    4.00,
    4.50,
    5.00,
    5.50
  ];

  ///
  /// 密度inch単位の値
  ///
  static List<double> stitchDensityInchValue = [
    6.4,
    12.7,
    19.1,
    25.4,
    31.8,
    38.1,
    44.5,
    50.8,
    63.5,
    76.2,
    88.9,
    101.6,
    114.3,
    127.0,
    139.7
  ];

  ///
  /// zigzagDensityの下付き文字
  ///
  static int zigzagDensityIndex = 1;

  ///
  /// satinDensityの下付き文字
  ///
  static int satinDensityIndex = 13;

  ///
  /// 振り幅mm単位の値
  ///
  static List<double> widthMmValue = [
    2.0,
    2.5,
    3.0,
    3.5,
    4.0,
    4.5,
    5.0,
    5.5,
    6.0,
  ];

  ///
  /// デフォルトの下付き文字
  ///
  static int defaultIndex = 3;

  ///
  /// zigzagDensityの下付き文字
  ///

  static int zigzagWidthIndex = defaultIndex;

  ///
  /// satinDensityの下付き文字
  ///
  static int satinWidthIndex = defaultIndex;

  ///
  /// ZigzagWidthの初期値
  ///
  static double widthDefaultValue = widthMmValue[defaultIndex];

  ///
  /// ZigzagDensityの初期値
  ///
  static double zigzagDensityDefaultValue =
      stitchDensityMmValue[zigzagDensityIndex];

  ///
  /// SatinDensityの初期値
  ///
  static double satinDensityDefaultValue =
      stitchDensityMmValue[satinDensityIndex];

  ///
  /// ZigzagDistanceの初期値
  ///
  static int distanceDefaultValue = 0;

  ///
  /// ZigzagDistanceのインプット値
  ///
  static int distanceInputValue = distanceDefaultValue;

  ///
  /// InternalDistanceの初期値
  ///
  static int internalDistanceDefaultValue = 25;

  ///
  /// InternalDistanceのインプット値
  ///
  static int internalDistanceInputValue = internalDistanceDefaultValue;

  ///
  /// appliqueが入るとき、編集される模様はPatternListの位置にあります。
  ///
  int _editPatternIndex = 0;
  ({Pattern editPattern, List<EmbGroup> editPatternGroupList})
      get getEditPattern {
    final Pattern editPattern =
        PatternModel().getAllPattern()[_editPatternIndex];

    if (editPattern is EmbGroup) {
      return (
        editPattern: editPattern,
        editPatternGroupList: [editPattern],
      );
    } else if (editPattern is EmbBorder) {
      return (
        editPattern: editPattern,
        editPatternGroupList: editPattern.groupList,
      );
    }

    Log.assertTrace('編集中の模様がない');
    return (
      editPattern: editPattern,
      editPatternGroupList: [],
    );
  }

  ///
  /// ThreadItemの選択状態
  /// インタフェースを使用しない場合は、選択したItemを縫いないように設定します
  ///
  List<List<bool>> isThreadSelectedList = [];

  ///
  /// アップリケパーツ選択apiの画像を返す
  ///
  Image? partsImage;

  ///
  /// 選択されたパーツのアップリケの作成画像返す
  ///
  Image? partsPreviewImage;

  ///
  /// 選択されたパーツのアップリケの作成
  ///
  EmbLibraryError makeAppliqueSelectedParts(
      AppliqueOutlineMode appliqueOutlineMode,
      AppliqueSatinKind appliqueSatinKind,
      AppliqueRunKind appliqueRunKind,
      int stitchWidthIndex,
      int stitchDensIndex,
      int distance,
      bool texture) {
    /// Lib 更新
    AppliquePartsParam param = AppliquePartsParam(
        outlineType: appliqueOutlineMode,
        satinType: appliqueSatinKind,
        runType: appliqueRunKind,
        stitchWidth: AppliqueStitchWidth.values[stitchWidthIndex],
        stitchDensity: AppliqueStitchDens.values[stitchDensIndex],
        distance: distance,
        texture: texture);
    var result = EmbLibrary().apiBinding.makeAppliqueSelectedParts(param);

    if (result.errorCode == EmbLibraryError.EMB_NO_ERR) {
      PatternModel()
        ..clearTemporaryPatternList()
        ..addTemporaryGroupList([result.handle]);
      partsPreviewImage = result.image;
    } else {
      PatternModel().clearTemporaryPatternList();
    }

    return result.errorCode;
  }

  ///
  /// AppliqueNotSewingパターン状態取得
  ///
  EmbLibraryError getAppliqueNotSewingErr() =>
      EmbLibrary().apiBinding.isAppliqueTextureNotAllDraw();

  ///
  /// Appliqueの作成
  ///
  EmbLibraryError makeApplique() {
    var (errorCode: error, handle: _) = EmbLibrary().apiBinding.makeApplique();

    return error;
  }

  ///
  /// Applique選択モードの初期化
  ///
  EmbLibraryError initAppliqueSelect() {
    /// Appliqueは現在選択されている模様を変更するため、今の模様のバックアップを作成します
    reset();
    _editPatternIndex = PatternModel().getCurrentGroupSewingIndex();
    return EmbLibrary().apiBinding.initAppliqueSelect();
  }

  ///
  /// Appliqueに入るときは、BorderMarkをキャンセルします(initAppliqueSelectの後に呼ばれる)
  ///
  EmbLibraryError cancelBorderBeforeWappen() {
    var (errorCode: error, handle: _) =
        EmbLibrary().apiBinding.cancelBorderBeforeWappen();
    if (error == EmbLibraryError.EMB_NO_ERR) {
      PatternModel().reloadAllPattern();
    } else {
      /// Do nothing
    }

    return error;
  }

  ///
  /// Appliqueに入るときは、BorderMarkを削除します(initAppliqueSelectの後に呼ばれる)
  ///
  EmbLibraryError deleteMarkBeforeWappen() {
    var error = EmbLibrary().apiBinding.deleteMarkBeforeWappen();
    if (error == EmbLibraryError.EMB_NO_ERR) {
      PatternModel().reloadAllPattern();
    } else {
      /// Do nothing
    }

    return error;
  }

  ///
  /// テクスチャー設定
  ///
  EmbLibraryError setTexture(bool value) {
    var (errCode: errCode, image: image) =
        EmbLibrary().apiBinding.setTexture(value);
    if (errCode == EmbLibraryError.EMB_NO_ERR) {
      partsPreviewImage = Image.memory(image);
    } else {
      /// Do nothing
    }

    return errCode;
  }

  ///
  /// アップリケの編集画面のイメージ取得
  ///
  Image? getWappenPreviewTexture() {
    var (errCode: errCode, image: image) =
        EmbLibrary().apiBinding.getWappenPreviewTexture();
    if (errCode != EmbLibraryError.EMB_NO_ERR) {
      return null;
    } else {
      /// Do nothing
    }

    return Image.memory(image);
  }

  ///
  /// 色選択Applique編集モードへ移行
  ///
  EmbLibraryError editSelectedApplique() {
    EmbLibraryError error = EmbLibrary().apiBinding.editSelectedApplique();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }
    PatternModel()
      ..clearCurrentThreadInfoCache(pattern: getEditPattern.editPattern)
      ..clearCurrentPatternImageCache(pattern: getEditPattern.editPattern);
    return error;
  }

  ///
  /// オフセット距離設定とDistanceの値を保存
  ///
  EmbLibraryError setDistanceApplique(int distanceValue) {
    UserData().setInt(UserDataKeys.embAppliqueDistanceValue, distanceValue);

    /// Lib更新
    var (errorCode: error, handle: handle) =
        EmbLibrary().apiBinding.setDistanceApplique(distanceValue);

    if (error == EmbLibraryError.EMB_NO_ERR) {
      PatternModel().addTemporaryGroupList([handle]);
    } else {
      PatternModel().clearTemporaryPatternList();
    }

    return error;
  }

  ///
  /// アップリケパーツの選択
  ///
  ({EmbLibraryError errorCode, Image image}) selectAppliqueParts(
      int selectedPatternIndex, int colorIndex, bool selection) {
    final MemHandle handle =
        getEditPattern.editPatternGroupList[selectedPatternIndex].handle;

    return EmbLibrary()
        .apiBinding
        .selectAppliqueParts(handle, colorIndex, selection);
  }

  ///
  /// アップリケパーツの全選択
  ///
  ({EmbLibraryError errorCode, Image image}) selectAppliquePartsAll(
      bool select) {
    final MemHandle handle = getEditPattern.editPatternGroupList.first.handle;
    return EmbLibrary().apiBinding.selectAppliquePartsAll(handle, select);
  }

  ///
  /// 選択したアップリケを完成させる
  ///
  EmbLibraryError completeSelectedApplique() {
    EmbLibraryError error = EmbLibrary().apiBinding.completeSelectedApplique();
    if (error == EmbLibraryError.EMB_NO_ERR) {
      PatternModel()
        ..loadNewPattern(markToReloadPatterns: [getEditPattern.editPattern])
        ..clearTemporaryPatternList();
    } else {
      /// Do nothing
    }
    return error;
  }

  ///
  /// Distanceの値を取得
  ///
  int? getAppliqueDistanceValue() =>
      UserData().getInt(UserDataKeys.embAppliqueDistanceValue);

  ///
  /// テクスチャの描画が可能かどうか確認する
  ///
  EmbLibraryError checkTextureDrawing() =>
      EmbLibrary().apiBinding.checkTextureDrawing();

  ///
  /// サムネイルの取得
  ///
  Image getThumbnailImage({required int selectedPatternIndex}) {
    List<EmbGroup> groupList = [];
    if (PatternModel().temporaryGroupList.firstOrNull != null) {
      groupList.add(PatternModel().temporaryGroupList.first);
    } else {
      /// Do noting
    }
    groupList.addAll(getEditPattern.editPatternGroupList);

    return Image.memory(groupList[selectedPatternIndex]
        .subImage(ScrollCenterType.IMAGE_EDITING, 100));
  }

  ///
  ///「NoSewing」設定の変更
  ///
  EmbLibraryError changeSettingsSewing(
      {required int patternIndex,
      required int colorIndex,
      required bool notSewing}) {
    List<EmbGroup> groupList = [];
    if (PatternModel().temporaryGroupList.firstOrNull != null) {
      groupList.add(PatternModel().temporaryGroupList.first);
    } else {
      /// Do noting
    }

    groupList.addAll(getEditPattern.editPatternGroupList);

    /// ThreadInfoを変更し、キャッシュされたデータを消去し、再取得します
    groupList[patternIndex].clearThreadInfoCache();
    groupList[patternIndex].clearMainImageCache();
    return EmbLibrary().apiBinding.changeSettingsSewing(
        groupList[patternIndex].handle, colorIndex, notSewing);
  }

  ///
  /// Colorをクリックしたときにlibが選択したPatternと線色を通知する
  ///
  void selectColor(
      {required int selectedPatternIndex,
      required int selectedColorItemIndex}) {
    List<EmbGroup> groupList = [];
    if (PatternModel().temporaryGroupList.firstOrNull != null) {
      groupList.add(PatternModel().temporaryGroupList.first);
    } else {
      /// Do noting
    }
    groupList.addAll(getEditPattern.editPatternGroupList);

    if (groupList.length <= selectedPatternIndex) {
      return;
    }

    /// Lib更新
    EmbLibrary().apiBinding.selectPartsInGroupColorChange(
        groupList[selectedPatternIndex].handle, selectedColorItemIndex);
  }

  ///
  /// 各item状態を取得
  ///
  bool isColorItemEnable() {
    for (var threadSelected in isThreadSelectedList) {
      for (var element in threadSelected) {
        if (element == true) {
          return true;
        }
      }
    }

    return false;
  }

  ///
  /// データ更新
  ///
  List<List<bool>> getAllColorItemState(bool isSelect) {
    List<List<bool>> isColorItemSelect = [];
    final List<EmbGroup> groupList = getEditPattern.editPatternGroupList;

    for (int groupIndex = 0; groupIndex < groupList.length; groupIndex++) {
      List<bool> temples = [];
      EmbGroup group = groupList[groupIndex];
      List<ThreadInfo> threadList = group.threadInfo;

      for (int threadIndex = 0;
          threadIndex < threadList.length;
          threadIndex++) {
        temples.add(isSelect);
      }

      isColorItemSelect.add(temples);
    }

    return isColorItemSelect;
  }

  ///
  /// 色変更ページの線色情報取得
  /// libの情報をmodel用の線色情報に変換する
  ///
  List<ColorListDisplayInfo> getDisplayList({
    required int? selectedPatternIndex,
    required int zoomScale,
  }) {
    final List<EmbGroup> groupList = getEditPattern.editPatternGroupList;
    List<ColorListDisplayInfo> displayList = [];

    for (int index = 0; index < groupList.length; index++) {
      bool isPatternSelected =
          selectedPatternIndex != null && selectedPatternIndex == index
              ? true
              : false;

      List<ThreadColorDispInfo> displayThreadInfoList =
          _getThreadInfoDisplayList(
        patternIndex: index,
        threadInfoList: groupList[index].threadInfo,
      );

      /// 線の色リストのサムネイル画像
      final Widget patternImage =
          ThreadColorListModel().getEmbGroupThreadImageList(
        groupList[index],
        ScrollCenterType.IMAGE_EDITING,
        zoomScale: zoomScale,
      );

      displayList.add(ColorListDisplayInfo(
          isSelectedPattern: isPatternSelected,
          patternImage: patternImage,
          threadInfoDisplayList: displayThreadInfoList));
    }

    return displayList;
  }

  ///
  /// 表示用の線色情報の取得
  ///
  List<ThreadColorDispInfo> _getThreadInfoDisplayList({
    required List<ThreadInfo> threadInfoList,
    required int patternIndex,
  }) {
    List<ThreadColorDispInfo> displayThreadInfoList = [];
    bool isThreadColorDefault = ThreadModel.getThreadColor();

    for (int index = 0; index < threadInfoList.length; index++) {
      displayThreadInfoList.add(ThreadColorDispInfo(
        isSelected: isThreadSelectedList[patternIndex][index],
        isThreadNotSewing: threadInfoList[index].notSewing,
        treadColor: threadInfoList[index].colorRGB,
        threadCode: ThreadModel.getThreadCode(threadInfoList[index].threadCode,
            threadInfoList[index].threadCodeDigit, isThreadColorDefault),
        threadBrandName: _getThreadBrandName(threadInfoList[index].brandCode,
            threadInfoList[index].threadCode, isThreadColorDefault),
        threadColorName: _getThreadColorName(threadInfoList[index].index300,
            threadInfoList[index].threadCode, isThreadColorDefault),
        threadSewingTime: PatternModel()
            .changeLibSewingTimeToUiSewingTime(threadInfoList[index].sewingTime)
            .toString(),
        appliqueIcon: ThreadModel.getAppliqueIcon(
          threadInfoList[index].threadCode,
          false,
          isThreadColorDefault,
        ),
      ));
    }

    return displayThreadInfoList;
  }

  ///
  /// 色変更ページの線色情報取得
  /// libの情報をmodel用の線色情報に変換する
  ///
  List<ColorListDisplayInfo> getDisplayListInNotSewingPage({
    required int selectedPatternIndex,
    required int selectedColorItemIndex,
    required int zoomScale,
  }) {
    List<EmbGroup> groupList = [];
    if (PatternModel().temporaryGroupList.firstOrNull != null) {
      groupList.add(PatternModel().temporaryGroupList.first);
    } else {
      /// Do noting
    }
    groupList.addAll(getEditPattern.editPatternGroupList);

    if (groupList.length <= selectedPatternIndex) {
      return [];
    }

    List<ColorListDisplayInfo> displayList = [];
    for (int index = 0; index < groupList.length; index++) {
      List<ThreadColorDispInfo> displayThreadInfoList =
          _getThreadInfoDisplayListInNotSewingPage(
        threadInfoList: groupList[index].threadInfo,
        isSelectedPattern: selectedPatternIndex == index,
        selectedColorItemIndex: selectedColorItemIndex,
      );

      /// 線の色リストのサムネイル画像
      final Widget patternImage =
          ThreadColorListModel().getEmbGroupThreadImageList(
        groupList[index],
        ScrollCenterType.IMAGE_EDITING,
        zoomScale: zoomScale,
      );

      displayList.add(ColorListDisplayInfo(
          patternImage: patternImage,
          threadInfoDisplayList: displayThreadInfoList));
    }

    return displayList;
  }

  ///
  /// 表示用の線色情報の取得
  ///
  List<ThreadColorDispInfo> _getThreadInfoDisplayListInNotSewingPage(
      {required List<ThreadInfo> threadInfoList,
      required bool isSelectedPattern,
      required int selectedColorItemIndex}) {
    List<ThreadColorDispInfo> displayThreadInfoList = [];
    bool isThreadColorDefault = ThreadModel.getThreadColor();

    for (int index = 0; index < threadInfoList.length; index++) {
      displayThreadInfoList.add(ThreadColorDispInfo(
        isSelected: isSelectedPattern && selectedColorItemIndex == index,
        isThreadNotSewing: threadInfoList[index].notSewing,
        treadColor: threadInfoList[index].colorRGB,
        threadCode: ThreadModel.getThreadCode(threadInfoList[index].threadCode,
            threadInfoList[index].threadCodeDigit, isThreadColorDefault),
        threadBrandName: _getThreadBrandName(threadInfoList[index].brandCode,
            threadInfoList[index].threadCode, isThreadColorDefault),
        threadColorName: _getThreadColorName(threadInfoList[index].index300,
            threadInfoList[index].threadCode, isThreadColorDefault),
        threadSewingTime: PatternModel()
            .changeLibSewingTimeToUiSewingTime(threadInfoList[index].sewingTime)
            .toString(),
        appliqueIcon: ThreadModel.getAppliqueIcon(
          threadInfoList[index].threadCode,
          false,
          isThreadColorDefault,
        ),
      ));
    }

    return displayThreadInfoList;
  }

  ///
  /// ブランド名の取得
  ///
  String _getThreadBrandName(
      int brandCode, int threadCode, bool isThreadColorDefault) {
    if (isThreadColorDefault == false ||
        ThreadModel.isAppliqueThreadCode(threadCode)) {
      return '';
    }

    ThreadBrandName threadBrandName =
        ThreadBrandName.getValuesByNumber(brandCode);

    return ThreadColorModel().getThreadBrandName(threadBrandName);
  }

  ///
  /// カラー名の取得
  ///
  String _getThreadColorName(
      int index300, int threadCode, bool isThreadColorDefault) {
    if (isThreadColorDefault == true) {
      return '';
    }

    return ThreadColorModel().getThreadColorNameWithIndex300(index300);
  }

  ///
  /// Partsページに再度アクセスしたら、必要なデータをクリーンアップします。
  ///
  void clearAppliquePartsInfo() {
    partsPreviewImage = null;
    partsImage = null;
    appliquePreviewType = AppliqueOutlineMode.appOutlineModeEx;
    appliqueExternalStitchType = AppliqueSatinKind.appSatinKindTacDown;
    appliqueInternalStitchType = AppliqueRunKind.appRunKindRun;
    zigzagDensityIndex = 1;
    satinDensityIndex = 13;
    zigzagWidthIndex = 3;
    satinWidthIndex = 3;
    isNextState = false;
    distanceInputValue = distanceDefaultValue;
    internalDistanceInputValue = internalDistanceDefaultValue;
  }

  ///
  /// リスト関数
  ///
  void reset() {
    partsPreviewImage = null;
    partsImage = null;
    appliquePreviewType = AppliqueOutlineMode.appOutlineModeEx;
    appliqueExternalStitchType = AppliqueSatinKind.appSatinKindTacDown;
    appliqueInternalStitchType = AppliqueRunKind.appRunKindRun;
    zigzagDensityIndex = 1;
    satinDensityIndex = 13;
    zigzagWidthIndex = 3;
    satinWidthIndex = 3;
    isNextState = false;
    distanceInputValue = distanceDefaultValue;
    internalDistanceInputValue = internalDistanceDefaultValue;
    _editPatternIndex = 0;
  }
}
