import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../emb/model/pattern_data_reader/category_image_reader.dart';
import '../../../../emb/model/pattern_data_reader/character_font_image_reader.dart';
import '../../../../emb/model/pattern_data_reader/exclusives_pattern_reader.dart';
import '../../../../setting/model/general_setting_page1_model.dart';
import 'language_setting_guidance_popup_view_interface.dart';

final languageSettingGuidancePopupViewModelProvider =
    StateNotifierProvider.autoDispose<LanguageSettingGuidancePopupViewInterface,
            LanguageSettingGuidancePopupState>(
        (ref) => LanguageSettingGuidancePopupViewModel(ref));

class LanguageSettingGuidancePopupViewModel
    extends LanguageSettingGuidancePopupViewInterface {
  LanguageSettingGuidancePopupViewModel(Ref ref)
      : super(const LanguageSettingGuidancePopupState(), ref) {
    /// Model更新
    _upDateDisplayLanguage();

    /// View更新
    update();
  }

  ///
  /// 画面更新
  ///
  @override
  void update() {
    state =
        state.copyWith(language: _languageList[_currentSelectedIndex].display);
  }

  ///
  /// Leftボタンのクリク事件処理
  ///
  @override
  void onLeftButtonClicked() {
    _currentSelectedIndex--;
    if (_currentSelectedIndex < 0) {
      _currentSelectedIndex = _maxLanguageList - 1;
    }

    /// Model更新
    _upDateDisplayLanguage();

    /// View更新
    update();
  }

  ///
  /// OKボタンのクリク事件処理
  ///
  @override
  void onOkButtonClicked(BuildContext context) {
    DeviceLibrary().apiBinding.setSetupLangFlag();

    /// Category画像情報キャッシュを更新
    CategoryImageReader().updateCategoryImagesInfoCache();
    CharacterFontImageReader().updateCharacterFontImagesInfoCache();
    ExclusivesPatternReader().updateExclusivesImagesInfoCache();
    PopupNavigator.pop(context: context);
  }

  ///
  /// Rightボタンのクリク事件処理
  ///
  @override
  void onRightButtonClicked() {
    _currentSelectedIndex++;
    if (_currentSelectedIndex >= _maxLanguageList) {
      _currentSelectedIndex = 0;
    } else {
      /// Do Nothing
    }

    /// Model更新
    _upDateDisplayLanguage();

    /// View更新
    update();
  }

  ///
  /// 画面表示言語を切り替える
  ///
  void _upDateDisplayLanguage() {
    Future(
      () {
        GeneralSettingPage1Model()
            .setLanguage(Language.values[_currentSelectedIndex]);
      },
    );
  }

  ///
  /// 今選択したの言語
  ///
  int _currentSelectedIndex = 0;
  void refreshCurrentSelectedIndex() {
    _currentSelectedIndex = 0;
    var apiLanguage = DeviceLibrary().apiBinding.getLanguage().value;
    final displayList = GeneralSettingPage1Model.systemLanguageDisplayList();
    for (int i = 0; i < displayList.length; i++) {
      var displayValue = displayList[i];
      if (displayValue.value == apiLanguage) {
        _currentSelectedIndex = i;
        break;
      }
    }
  }

  ///
  /// 言語選択リスト
  ///
  List<DisplayValue<Language>> get _languageList =>
      GeneralSettingPage1Model.systemLanguageDisplayList();

  ///
  /// 言語選択リスト長さ
  ///
  int get _maxLanguageList =>
      GeneralSettingPage1Model.systemLanguageDisplayList().length;
}
