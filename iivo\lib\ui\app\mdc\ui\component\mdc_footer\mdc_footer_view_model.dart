import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/projector_model.dart';
import '../../../../../component/common_footer/common_footer_view_interface.dart';
import '../../../../../component/common_footer/common_footer_view_model.dart';
import '../../../../../component/real_preview/component/real_preview_controller.dart';
import '../../../../../component/real_preview/real_preview_view_interface.dart';
import '../../../../../component/real_preview/real_preview_view_model.dart';
import '../../page/stitch/stitch_page_view_model.dart';
import '../function_provider/projector_function_provider/projector_function_provider.dart';
import '../mdc_header/mdc_header_view_model.dart';

/// view _modelに必要な構造
final mdcFooterViewModelProvider =
    AutoDisposeStateNotifierProvider<CommonFooterViewModel, CommonFooterState>(
        (ref) => MdcFooterViewModel(ref));

class MdcFooterViewModel extends CommonFooterViewModel
    with DeviceLibraryEventObserver {
  MdcFooterViewModel(ref) : super(ref);

  @override
  void onClockButtonClick(BuildContext context) {
    final provider = realPreviewViewModelProvider(RealPreviewType.mdc);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.accept);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// プロジェクタを閉じます。
    final projectorRet = _maybeCloseProjector();
    if (projectorRet == true) {
      return;
    }

    /// ClockSetting画面遷移前にキー関数を先に実行する、エラー確認など
    final errCode = TpdLibrary().apiBinding.gotoClockSetting();
    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      return;
    }

    if (errCode != DeviceErrorCode.devNoError) {
      return;
    }

    /// カメラ機能を閉じる
    if (ref.exists(mdcHeaderViewModelProvider)) {
      final headerFunction = ref.read(mdcHeaderViewModelProvider.notifier);
      final closeRet = headerFunction.maybeCloseCamera();
      if (closeRet == true) {
        headerFunction.updateCameraButtonToNormalByFooterCloseCamera();
      }
    } else {
      Log.e(tag: "Footer", description: "unknown header");
    }

    super.onClockButtonClick(context);
  }

  ///
  /// プロジェクト起動時、プロジェクト閉じる流れ処理
  /// 戻る値: true(閉じる処理が必要ですので、普通処理をしない)
  ///
  bool _maybeCloseProjector() {
    if (ProjectorModel().mdcProjector.isMdcProjectorViewOpen == true) {
      final mdcProjectorFunction =
          ref.read(mdcProjectorFunctionProvider.notifier);

      /// プロジェクト起動・停止前にエラーCheckをする
      /// エラーがあれば ポープアープを表示する
      final hasError = mdcProjectorFunction
          .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
      if (hasError) {
        return true;
      }

      mdcProjectorFunction.closeMdcProjectorView(
        closingHandleCallback: () {
          ProjectorModel().mdcProjector.isMdcProjectorViewOpen = false;

          /// 他のページへの更新の通知
          ref
              .read(stitchPageViewModelProvider.notifier)
              .updatePageByChild(ComponentType.projector);
          ref.read(mdcHeaderViewModelProvider.notifier).update();
        },
        afterClosedHandleCallback: () {},
      );

      return true;
    }

    return false;
  }
}
