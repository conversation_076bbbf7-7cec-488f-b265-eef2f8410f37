// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'real_preview_view_interface.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RealPreviewState {
  bool get isZoomOut => throw _privateConstructorUsedError;
  bool get isPressFootDisplay => throw _privateConstructorUsedError;
  RealPreviewAction get animationPlayState =>
      throw _privateConstructorUsedError;
  RealPreviewTableType get frameTableState =>
      throw _privateConstructorUsedError;
  RealPreviewTableType get stitchSimulatorTableState =>
      throw _privateConstructorUsedError;
  RealPreviewFrameType get selectedFrame => throw _privateConstructorUsedError;
  PresserFoot get displayPressFootType => throw _privateConstructorUsedError;
  List<ButtonState> get frameButtonStateList =>
      throw _privateConstructorUsedError;
  List<ButtonState> get speedButtonStateList =>
      throw _privateConstructorUsedError;

  /// 背景スキャンの画像
  Widget? get backgroundImage => throw _privateConstructorUsedError;

  /// プリント＆ステッチ模様で表示される背景画像
  Image? get printBackGroundImage => throw _privateConstructorUsedError;
  Widget? get realPreviewImage => throw _privateConstructorUsedError;
  bool get isBusyDrawing => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $RealPreviewStateCopyWith<RealPreviewState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RealPreviewStateCopyWith<$Res> {
  factory $RealPreviewStateCopyWith(
          RealPreviewState value, $Res Function(RealPreviewState) then) =
      _$RealPreviewStateCopyWithImpl<$Res, RealPreviewState>;
  @useResult
  $Res call(
      {bool isZoomOut,
      bool isPressFootDisplay,
      RealPreviewAction animationPlayState,
      RealPreviewTableType frameTableState,
      RealPreviewTableType stitchSimulatorTableState,
      RealPreviewFrameType selectedFrame,
      PresserFoot displayPressFootType,
      List<ButtonState> frameButtonStateList,
      List<ButtonState> speedButtonStateList,
      Widget? backgroundImage,
      Image? printBackGroundImage,
      Widget? realPreviewImage,
      bool isBusyDrawing});
}

/// @nodoc
class _$RealPreviewStateCopyWithImpl<$Res, $Val extends RealPreviewState>
    implements $RealPreviewStateCopyWith<$Res> {
  _$RealPreviewStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isZoomOut = null,
    Object? isPressFootDisplay = null,
    Object? animationPlayState = freezed,
    Object? frameTableState = null,
    Object? stitchSimulatorTableState = null,
    Object? selectedFrame = null,
    Object? displayPressFootType = freezed,
    Object? frameButtonStateList = null,
    Object? speedButtonStateList = null,
    Object? backgroundImage = freezed,
    Object? printBackGroundImage = freezed,
    Object? realPreviewImage = freezed,
    Object? isBusyDrawing = null,
  }) {
    return _then(_value.copyWith(
      isZoomOut: null == isZoomOut
          ? _value.isZoomOut
          : isZoomOut // ignore: cast_nullable_to_non_nullable
              as bool,
      isPressFootDisplay: null == isPressFootDisplay
          ? _value.isPressFootDisplay
          : isPressFootDisplay // ignore: cast_nullable_to_non_nullable
              as bool,
      animationPlayState: freezed == animationPlayState
          ? _value.animationPlayState
          : animationPlayState // ignore: cast_nullable_to_non_nullable
              as RealPreviewAction,
      frameTableState: null == frameTableState
          ? _value.frameTableState
          : frameTableState // ignore: cast_nullable_to_non_nullable
              as RealPreviewTableType,
      stitchSimulatorTableState: null == stitchSimulatorTableState
          ? _value.stitchSimulatorTableState
          : stitchSimulatorTableState // ignore: cast_nullable_to_non_nullable
              as RealPreviewTableType,
      selectedFrame: null == selectedFrame
          ? _value.selectedFrame
          : selectedFrame // ignore: cast_nullable_to_non_nullable
              as RealPreviewFrameType,
      displayPressFootType: freezed == displayPressFootType
          ? _value.displayPressFootType
          : displayPressFootType // ignore: cast_nullable_to_non_nullable
              as PresserFoot,
      frameButtonStateList: null == frameButtonStateList
          ? _value.frameButtonStateList
          : frameButtonStateList // ignore: cast_nullable_to_non_nullable
              as List<ButtonState>,
      speedButtonStateList: null == speedButtonStateList
          ? _value.speedButtonStateList
          : speedButtonStateList // ignore: cast_nullable_to_non_nullable
              as List<ButtonState>,
      backgroundImage: freezed == backgroundImage
          ? _value.backgroundImage
          : backgroundImage // ignore: cast_nullable_to_non_nullable
              as Widget?,
      printBackGroundImage: freezed == printBackGroundImage
          ? _value.printBackGroundImage
          : printBackGroundImage // ignore: cast_nullable_to_non_nullable
              as Image?,
      realPreviewImage: freezed == realPreviewImage
          ? _value.realPreviewImage
          : realPreviewImage // ignore: cast_nullable_to_non_nullable
              as Widget?,
      isBusyDrawing: null == isBusyDrawing
          ? _value.isBusyDrawing
          : isBusyDrawing // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RealPreviewStateImplCopyWith<$Res>
    implements $RealPreviewStateCopyWith<$Res> {
  factory _$$RealPreviewStateImplCopyWith(_$RealPreviewStateImpl value,
          $Res Function(_$RealPreviewStateImpl) then) =
      __$$RealPreviewStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isZoomOut,
      bool isPressFootDisplay,
      RealPreviewAction animationPlayState,
      RealPreviewTableType frameTableState,
      RealPreviewTableType stitchSimulatorTableState,
      RealPreviewFrameType selectedFrame,
      PresserFoot displayPressFootType,
      List<ButtonState> frameButtonStateList,
      List<ButtonState> speedButtonStateList,
      Widget? backgroundImage,
      Image? printBackGroundImage,
      Widget? realPreviewImage,
      bool isBusyDrawing});
}

/// @nodoc
class __$$RealPreviewStateImplCopyWithImpl<$Res>
    extends _$RealPreviewStateCopyWithImpl<$Res, _$RealPreviewStateImpl>
    implements _$$RealPreviewStateImplCopyWith<$Res> {
  __$$RealPreviewStateImplCopyWithImpl(_$RealPreviewStateImpl _value,
      $Res Function(_$RealPreviewStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isZoomOut = null,
    Object? isPressFootDisplay = null,
    Object? animationPlayState = freezed,
    Object? frameTableState = null,
    Object? stitchSimulatorTableState = null,
    Object? selectedFrame = null,
    Object? displayPressFootType = freezed,
    Object? frameButtonStateList = null,
    Object? speedButtonStateList = null,
    Object? backgroundImage = freezed,
    Object? printBackGroundImage = freezed,
    Object? realPreviewImage = freezed,
    Object? isBusyDrawing = null,
  }) {
    return _then(_$RealPreviewStateImpl(
      isZoomOut: null == isZoomOut
          ? _value.isZoomOut
          : isZoomOut // ignore: cast_nullable_to_non_nullable
              as bool,
      isPressFootDisplay: null == isPressFootDisplay
          ? _value.isPressFootDisplay
          : isPressFootDisplay // ignore: cast_nullable_to_non_nullable
              as bool,
      animationPlayState: freezed == animationPlayState
          ? _value.animationPlayState
          : animationPlayState // ignore: cast_nullable_to_non_nullable
              as RealPreviewAction,
      frameTableState: null == frameTableState
          ? _value.frameTableState
          : frameTableState // ignore: cast_nullable_to_non_nullable
              as RealPreviewTableType,
      stitchSimulatorTableState: null == stitchSimulatorTableState
          ? _value.stitchSimulatorTableState
          : stitchSimulatorTableState // ignore: cast_nullable_to_non_nullable
              as RealPreviewTableType,
      selectedFrame: null == selectedFrame
          ? _value.selectedFrame
          : selectedFrame // ignore: cast_nullable_to_non_nullable
              as RealPreviewFrameType,
      displayPressFootType: freezed == displayPressFootType
          ? _value.displayPressFootType
          : displayPressFootType // ignore: cast_nullable_to_non_nullable
              as PresserFoot,
      frameButtonStateList: null == frameButtonStateList
          ? _value._frameButtonStateList
          : frameButtonStateList // ignore: cast_nullable_to_non_nullable
              as List<ButtonState>,
      speedButtonStateList: null == speedButtonStateList
          ? _value._speedButtonStateList
          : speedButtonStateList // ignore: cast_nullable_to_non_nullable
              as List<ButtonState>,
      backgroundImage: freezed == backgroundImage
          ? _value.backgroundImage
          : backgroundImage // ignore: cast_nullable_to_non_nullable
              as Widget?,
      printBackGroundImage: freezed == printBackGroundImage
          ? _value.printBackGroundImage
          : printBackGroundImage // ignore: cast_nullable_to_non_nullable
              as Image?,
      realPreviewImage: freezed == realPreviewImage
          ? _value.realPreviewImage
          : realPreviewImage // ignore: cast_nullable_to_non_nullable
              as Widget?,
      isBusyDrawing: null == isBusyDrawing
          ? _value.isBusyDrawing
          : isBusyDrawing // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$RealPreviewStateImpl implements _RealPreviewState {
  const _$RealPreviewStateImpl(
      {this.isZoomOut = false,
      this.isPressFootDisplay = false,
      this.animationPlayState = RealPreviewAction.stop,
      this.frameTableState = RealPreviewTableType.selected,
      this.stitchSimulatorTableState = RealPreviewTableType.unselected,
      this.selectedFrame = RealPreviewFrameType.frame297x465,
      this.displayPressFootType = PresserFoot.EMB_PRESSER_FOOT_W,
      final List<ButtonState> frameButtonStateList = const [
        ButtonState.select,
        ButtonState.normal,
        ButtonState.normal,
        ButtonState.normal
      ],
      final List<ButtonState> speedButtonStateList = const [
        ButtonState.select,
        ButtonState.normal,
        ButtonState.normal
      ],
      this.backgroundImage = null,
      this.printBackGroundImage = null,
      this.realPreviewImage = null,
      this.isBusyDrawing = true})
      : _frameButtonStateList = frameButtonStateList,
        _speedButtonStateList = speedButtonStateList;

  @override
  @JsonKey()
  final bool isZoomOut;
  @override
  @JsonKey()
  final bool isPressFootDisplay;
  @override
  @JsonKey()
  final RealPreviewAction animationPlayState;
  @override
  @JsonKey()
  final RealPreviewTableType frameTableState;
  @override
  @JsonKey()
  final RealPreviewTableType stitchSimulatorTableState;
  @override
  @JsonKey()
  final RealPreviewFrameType selectedFrame;
  @override
  @JsonKey()
  final PresserFoot displayPressFootType;
  final List<ButtonState> _frameButtonStateList;
  @override
  @JsonKey()
  List<ButtonState> get frameButtonStateList {
    if (_frameButtonStateList is EqualUnmodifiableListView)
      return _frameButtonStateList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_frameButtonStateList);
  }

  final List<ButtonState> _speedButtonStateList;
  @override
  @JsonKey()
  List<ButtonState> get speedButtonStateList {
    if (_speedButtonStateList is EqualUnmodifiableListView)
      return _speedButtonStateList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_speedButtonStateList);
  }

  /// 背景スキャンの画像
  @override
  @JsonKey()
  final Widget? backgroundImage;

  /// プリント＆ステッチ模様で表示される背景画像
  @override
  @JsonKey()
  final Image? printBackGroundImage;
  @override
  @JsonKey()
  final Widget? realPreviewImage;
  @override
  @JsonKey()
  final bool isBusyDrawing;

  @override
  String toString() {
    return 'RealPreviewState(isZoomOut: $isZoomOut, isPressFootDisplay: $isPressFootDisplay, animationPlayState: $animationPlayState, frameTableState: $frameTableState, stitchSimulatorTableState: $stitchSimulatorTableState, selectedFrame: $selectedFrame, displayPressFootType: $displayPressFootType, frameButtonStateList: $frameButtonStateList, speedButtonStateList: $speedButtonStateList, backgroundImage: $backgroundImage, printBackGroundImage: $printBackGroundImage, realPreviewImage: $realPreviewImage, isBusyDrawing: $isBusyDrawing)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RealPreviewStateImpl &&
            (identical(other.isZoomOut, isZoomOut) ||
                other.isZoomOut == isZoomOut) &&
            (identical(other.isPressFootDisplay, isPressFootDisplay) ||
                other.isPressFootDisplay == isPressFootDisplay) &&
            const DeepCollectionEquality()
                .equals(other.animationPlayState, animationPlayState) &&
            (identical(other.frameTableState, frameTableState) ||
                other.frameTableState == frameTableState) &&
            (identical(other.stitchSimulatorTableState,
                    stitchSimulatorTableState) ||
                other.stitchSimulatorTableState == stitchSimulatorTableState) &&
            (identical(other.selectedFrame, selectedFrame) ||
                other.selectedFrame == selectedFrame) &&
            const DeepCollectionEquality()
                .equals(other.displayPressFootType, displayPressFootType) &&
            const DeepCollectionEquality()
                .equals(other._frameButtonStateList, _frameButtonStateList) &&
            const DeepCollectionEquality()
                .equals(other._speedButtonStateList, _speedButtonStateList) &&
            (identical(other.backgroundImage, backgroundImage) ||
                other.backgroundImage == backgroundImage) &&
            (identical(other.printBackGroundImage, printBackGroundImage) ||
                other.printBackGroundImage == printBackGroundImage) &&
            (identical(other.realPreviewImage, realPreviewImage) ||
                other.realPreviewImage == realPreviewImage) &&
            (identical(other.isBusyDrawing, isBusyDrawing) ||
                other.isBusyDrawing == isBusyDrawing));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isZoomOut,
      isPressFootDisplay,
      const DeepCollectionEquality().hash(animationPlayState),
      frameTableState,
      stitchSimulatorTableState,
      selectedFrame,
      const DeepCollectionEquality().hash(displayPressFootType),
      const DeepCollectionEquality().hash(_frameButtonStateList),
      const DeepCollectionEquality().hash(_speedButtonStateList),
      backgroundImage,
      printBackGroundImage,
      realPreviewImage,
      isBusyDrawing);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RealPreviewStateImplCopyWith<_$RealPreviewStateImpl> get copyWith =>
      __$$RealPreviewStateImplCopyWithImpl<_$RealPreviewStateImpl>(
          this, _$identity);
}

abstract class _RealPreviewState implements RealPreviewState {
  const factory _RealPreviewState(
      {final bool isZoomOut,
      final bool isPressFootDisplay,
      final RealPreviewAction animationPlayState,
      final RealPreviewTableType frameTableState,
      final RealPreviewTableType stitchSimulatorTableState,
      final RealPreviewFrameType selectedFrame,
      final PresserFoot displayPressFootType,
      final List<ButtonState> frameButtonStateList,
      final List<ButtonState> speedButtonStateList,
      final Widget? backgroundImage,
      final Image? printBackGroundImage,
      final Widget? realPreviewImage,
      final bool isBusyDrawing}) = _$RealPreviewStateImpl;

  @override
  bool get isZoomOut;
  @override
  bool get isPressFootDisplay;
  @override
  RealPreviewAction get animationPlayState;
  @override
  RealPreviewTableType get frameTableState;
  @override
  RealPreviewTableType get stitchSimulatorTableState;
  @override
  RealPreviewFrameType get selectedFrame;
  @override
  PresserFoot get displayPressFootType;
  @override
  List<ButtonState> get frameButtonStateList;
  @override
  List<ButtonState> get speedButtonStateList;
  @override

  /// 背景スキャンの画像
  Widget? get backgroundImage;
  @override

  /// プリント＆ステッチ模様で表示される背景画像
  Image? get printBackGroundImage;
  @override
  Widget? get realPreviewImage;
  @override
  bool get isBusyDrawing;
  @override
  @JsonKey(ignore: true)
  _$$RealPreviewStateImplCopyWith<_$RealPreviewStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
