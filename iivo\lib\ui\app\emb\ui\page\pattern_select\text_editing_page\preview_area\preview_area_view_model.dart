import 'dart:ui';

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/frame_model.dart';
import '../../../../../../../../model/preview_area_size_model.dart';
import '../../../../../model/key_board_font_model.dart';
import '../../../../../model/pattern_model.dart';
import '../../../../../model/preview_model.dart';
import '../text_editing_page_view_model.dart';
import 'preview_area_interface.dart';

typedef PatternViewDisplayInfo = PatternDisplayInfo;
typedef EmbBorderViewDisplayInfo = EmbBorderDisplayInfo;
typedef EmbGroupViewDisplayInfo = EmbGroupDisplayInfo;
typedef EmbPatternViewDisplayInfo = EmbPatternDisplayInfo;

/// 10mm定義
const int _gridLine10MmValue = 10;

/// 25mm定義
const int _gridLine25MmValue = 25;

/// フレームの破線の一段の長さ
const double _dashedLineLength = 2.0;

/// フレームの破線の間隔
const double _dashedLineSpace = 2.0;

/// 黒点行列の間隔
const double _blackPointsSpace = 10.0;

/// 1本指
const int _oneFinger = 1;

/// 1mmに対応する画素数
final double _pixelOfOneMm = charInputPreviewSize.dx / frame297x465MmSize.dx;

/// プレビュー中心点
final Offset _originalCenter = charInputPreviewSize / 2;

/// 移動量　0.5mm単位　1で0.5mm移動
const double _oneMoveStepInMm = 0.5;

/// 1画素に対応するmm
final double _mmOfOnePixel = frame297x465MmSize.dx / charInputPreviewSize.dx;

final previewAreaViewModelProvider = StateNotifierProvider.autoDispose<
    PreviewAreaViewModelInterface, PreviewAreaState>(
  (ref) => PreviewAreaViewModel(ref),
);

class PreviewAreaViewModel extends PreviewAreaViewModelInterface {
  PreviewAreaViewModel(Ref ref)
      : super(
          PreviewAreaState(
            charGroupPatternDisplayInfoList: [],
            gridTypeIndex: 0,
            gridColor: Colors.transparent,
            frameDrawPath: Path(),
            frameColor: Colors.transparent,
            blackPoints: [],
            isDragPreviewEnable: false,
            zoomOutPoint: const Offset(0, 0),
            isFirstInput: true,
          ),
          ref,
        ) {
    update();
  }

  ///
  /// 現在の表示倍率のバックアップ
  ///
  int _prevZoomScale = PatternModel().selectedZoomScaleInSelectPage;

  ///
  /// Previewドラッグの開始点
  ///
  Offset _previewStartDargPoint = const Offset(0, 0);

  ///
  /// 2本指でZoom倍率を拡大する前のZoom値バックアップ
  ///
  int _selectedZoomScaleBack = PatternModel().selectedZoomScaleInSelectPage;

  ///
  /// 2本指の拡大中、前回の拡大倍数
  ///
  int _prevDoubleFingerSizeStep = 0;

  ///
  /// 表示用データ
  ///
  List<PatternDisplayInfo>? displayData;

  @override
  void update() {
    /// view更新
    state = state.copyWith(
      charGroupPatternDisplayInfoList: _getCharGroupPatternDisplayInfoList(),
      gridTypeIndex: DeviceLibrary().apiBinding.getGrid().gridType.index,
      frameDrawPath: _getFrameDrawPath(),
      gridColor: PreviewModel().getEmbroideryGridColor(),
      frameColor: PreviewModel().getEmbroideryFrameColor(),
      blackPoints: _getBlackPoints(),
      isDragPreviewEnable: PreviewModel().isInDragPreviewMode,
      zoomOutPoint: _getZoomOutCenterPoint(),
      isFirstInput: KeyBoardFontModel().isReEditMode == false,
    );
  }

  @override
  Offset get getPreviewSize => charInputPreviewSize;

  @override
  double get getMagnification =>
      PatternModel().selectedZoomScaleInSelectPage / zoomList.first;

  @override
  List<double> getGridVerticalList() {
    List<double> verticalList = [];
    double xOffset = _originalCenter.dx;
    int mmValue = state.gridTypeIndex == EmbGridType.embGridGridLine10.index
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    /// 中心点の左端の線の位置を計算するには
    xOffset -= _pixelOfOneMm * mmValue;
    while (xOffset >= 0) {
      verticalList.add(xOffset);
      xOffset -= _pixelOfOneMm * mmValue;
    }

    xOffset = _originalCenter.dx;

    /// 中心点の右端の線の位置を計算するには
    xOffset += _pixelOfOneMm * mmValue;
    while (xOffset <= charInputPreviewSize.dx) {
      verticalList.add(xOffset);
      xOffset += _pixelOfOneMm * mmValue;
    }

    return verticalList;
  }

  @override
  List<double> getGridHorizontalList() {
    List<double> horizontalList = [];
    double yOffset = _originalCenter.dy;
    int mmValue = state.gridTypeIndex == EmbGridType.embGridGridLine10.index
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    /// 中心点上端の線の位置を計算するには
    yOffset -= _pixelOfOneMm * mmValue;
    while (yOffset >= 0) {
      horizontalList.add(yOffset);
      yOffset -= _pixelOfOneMm * mmValue;
    }

    yOffset = _originalCenter.dy;

    /// 中心点の下端の線の位置を計算するには
    yOffset += _pixelOfOneMm * mmValue;
    while (yOffset <= charInputPreviewSize.dy) {
      horizontalList.add(yOffset);
      yOffset += _pixelOfOneMm * mmValue;
    }

    return horizontalList;
  }

  @override
  void dargPreviewStart(ScaleStartDetails details) {
    _previewStartDargPoint = details.localFocalPoint;
    _selectedZoomScaleBack = PatternModel().selectedZoomScaleInSelectPage;
  }

  @override
  void dargPreview(ScaleUpdateDetails details) {
    if (details.pointerCount > _oneFinger) {
      if (PreviewModel().isInDragPreviewMode == false) {
        return;
      }

      int index = zoomList.indexOf(_selectedZoomScaleBack);
      int step = _getDoubleFingerStep(details.scale);
      index = (index + step).clamp(0, zoomList.length - 1);

      /// 必要でない場合は更新しない
      if (PatternModel().selectedZoomScaleInSelectPage != zoomList[index]) {
        /// Model更新
        PatternModel().selectedZoomScaleInSelectPage = zoomList[index];

        /// View更新
        update();

        /// 他のページへの更新の通知
        ref
            .read(textEditingPageViewModelProvider.notifier)
            .updateTextEditingPageByChild(TextEditingType.preview);
      } else {
        /// Do noting
      }
    } else {
      Offset offset = details.localFocalPoint - _previewStartDargPoint;
      Offset zoomOutPoint = state.zoomOutPoint - offset;

      /// 新しい移動開始点の設定
      _previewStartDargPoint = details.localFocalPoint;

      /// 範囲外の制御
      zoomOutPoint = Offset(
          zoomOutPoint.dx
              .clamp(-charInputPreviewSize.dx / 2, charInputPreviewSize.dx / 2),
          zoomOutPoint.dy.clamp(
              -charInputPreviewSize.dy / 2, charInputPreviewSize.dy / 2));

      /// 拡大された現在のPreviewの中心点をオフセット
      offset = -offset;

      /// View更新
      state = state.copyWith(
        zoomOutPoint: zoomOutPoint,
      );
    }
  }

  @override
  void onCharacterClick(int lineIndex, int charIndex) {
    if (_isDaring || _isTwoFinger) {
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    KeyBoardFontModel().moveCursorToPosition(lineIndex, charIndex);

    /// 更新
    update();

    /// 他のページへの更新の通知
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.preview);
  }

  ///
  /// 選択した枠のプレビュー表示データを取得します
  ///
  Path _getFrameDrawPath() {
    List<FrameSizeAndArea>? frameSizeAndAreaList = getFrameDisplaySizeAndArea(
        DeviceLibrary().apiBinding.getEmbroideryFrameDisplay().frameDispType);
    assert(frameSizeAndAreaList != null, "対応するサイズのボックスが見つかりません");

    Path drawPath = Path();
    Path basePath = Path();
    double magnification = getMagnification;

    if (frameSizeAndAreaList!.length > 1) {
      basePath =
          _get60x20FrameBasePath(frameSizeAndAreaList: frameSizeAndAreaList);
    } else {
      double width = PreviewModel.convertMmToPixels(
          value: frameSizeAndAreaList.first.width, pixelOfOneMm: _pixelOfOneMm);
      double height = PreviewModel.convertMmToPixels(
          value: frameSizeAndAreaList.first.height,
          pixelOfOneMm: _pixelOfOneMm);
      double startX = PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList.first.left,
              pixelOfOneMm: _pixelOfOneMm) +
          _originalCenter.dx;
      double startY = PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList.first.top,
              pixelOfOneMm: _pixelOfOneMm) +
          _originalCenter.dy;

      Rect rect = Rect.fromLTWH(startX, startY, width, height);
      basePath.moveTo(rect.left, rect.top);
      basePath.lineTo(rect.left, rect.bottom);
      basePath.lineTo(rect.right, rect.bottom);
      basePath.lineTo(rect.right, rect.top);
      basePath.lineTo(rect.left, rect.top);
    }

    /// フレームの破線の一段の長さ
    double dashWidth = _dashedLineLength / magnification;
    double dashSpace = _dashedLineSpace / magnification;

    /// 描画Pathを計算する
    double distance = 0.0;
    for (PathMetric pathMetric in basePath.computeMetrics()) {
      while (distance < pathMetric.length) {
        drawPath.addPath(
          pathMetric.extractPath(distance, distance + dashWidth),
          Offset.zero,
        );
        distance += dashWidth;
        distance += dashSpace;
      }
    }
    return drawPath;
  }

  ///
  /// 60*20枠のプレビューにはデータが表示されま
  ///
  Path _get60x20FrameBasePath(
      {required List<FrameSizeAndArea> frameSizeAndAreaList}) {
    Path basePath = Path();

    /// List順：60*20 mm、50*30 mm、30*40 mm
    List<Rect> rectList = List.generate(
      frameSizeAndAreaList.length,
      (index) => Rect.fromLTWH(
          PreviewModel.convertMmToPixels(
                  value: frameSizeAndAreaList[index].left,
                  pixelOfOneMm: _pixelOfOneMm) +
              _originalCenter.dx,
          PreviewModel.convertMmToPixels(
                  value: frameSizeAndAreaList[index].top,
                  pixelOfOneMm: _pixelOfOneMm) +
              _originalCenter.dy,
          PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList[index].width,
              pixelOfOneMm: _pixelOfOneMm),
          PreviewModel.convertMmToPixels(
              value: frameSizeAndAreaList[index].height,
              pixelOfOneMm: _pixelOfOneMm)),
    );

    /// 1番目と2番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*40 mm
    rectList.insert(
      1,
      Rect.fromPoints(
        Offset(rectList[1].left, rectList.first.top),
        Offset(rectList[1].right, rectList.first.bottom),
      ),
    );

    /// 2番目と3番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*30 mm、30*40 mm
    rectList.insert(
      3,
      Rect.fromPoints(
        Offset(rectList.last.left, rectList[2].top),
        Offset(rectList.last.right, rectList[2].bottom),
      ),
    );

    /// 60*20 mmの左上点から描画
    basePath.moveTo(rectList.first.left, rectList.first.top);

    /// Rectの左上点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].left, rectList[index].top);
    }

    /// Rectの右上点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].right, rectList[index].top);
    }

    /// Rectの右下点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].right, rectList[index].bottom);
    }

    /// Rectの左下点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].left, rectList[index].bottom);
    }

    /// 接続開始点
    basePath.lineTo(rectList.first.left, rectList.first.top);

    return basePath;
  }

  ///
  /// 黒点行列の表示データを取得します
  ///
  List<Offset> _getBlackPoints() {
    double space = _blackPointsSpace / getMagnification;
    List<Offset> points = [];

    /// 黒点行列は以下の場合には表示しない
    if (PatternModel().selectedZoomScaleInSelectPage == zoomList.first ||
        [
          EmbGridType.embGridCenterLine,
          EmbGridType.embGridGridLine10,
          EmbGridType.embGridGridLine_25
        ].contains(DeviceLibrary().apiBinding.getGrid())) {
      return points;
    } else {
      for (double y = space; y < charInputPreviewSize.dy; y += space) {
        for (double x = space; x < charInputPreviewSize.dx; x += space) {
          points.add(Offset(x, y));
        }
      }
    }

    return points;
  }

  ///
  /// 文字Pattern情報変換を使用する
  ///
  List<PatternDisplayInfo> _getCharGroupPatternDisplayInfoList() {
    if (KeyBoardFontModel().getTextBuffer().isEmpty) {
      return [];
    }

    if (displayData == null ||
        displayData!.isEmpty ||
        KeyBoardFontModel().needUpdateAllPreviewData == true) {
      _updateAllDisplayData();
      KeyBoardFontModel().updateAllPreviewDataFinished();
    } else {
      _updateCurrentLineDisplayData();
    }

    return displayData ?? [];
  }

  ///
  /// すべでの文字Pattern情報を更新する
  ///
  void _updateAllDisplayData() {
    final List<PatternDisplayInfo> patternDisplayInfoList = [];
    final MemHandle cursorHandle = KeyBoardFontModel().getCursorPattern();
    final CursorType cursorType = KeyBoardFontModel().getCursorType();

    /// カレントグループのハンドル
    final currentGroupHandle = PatternModel().getCurrentGroupHandle();
    final List<LineData> allLineList = KeyBoardFontModel().getAllLineData();
    for (int index = 0; index < allLineList.length; index++) {
      if (allLineList[index].charList.isEmpty) {
        continue;
      }
      Pattern pattern = allLineList[index].embGroup;
      if (pattern is EmbGroup) {
        patternDisplayInfoList.add(PreviewModel().getGroupPatternDisplayInfo(
          scrollType: ScrollCenterType.IMAGE_SELECT_FONT,
          group: pattern,
          centerPoint: _originalCenter,
          pixelOfOneMm: _pixelOfOneMm,
          sewingIndex: index,
          cursorHandle: cursorHandle,
          cursorType: cursorType,
          currentGroupHandle: currentGroupHandle,
          zoomScale: PatternModel().selectedZoomScaleInSelectPage,
        ));
      } else {
        /// Do noting
      }
    }

    displayData = patternDisplayInfoList;
  }

  ///
  /// 現在の文字行のPattern情報を更新する
  ///
  void _updateCurrentLineDisplayData() {
    if (displayData == null || displayData!.isEmpty) {
      _updateAllDisplayData();
    } else {
      final MemHandle cursorHandle = KeyBoardFontModel().getCursorPattern();
      final CursorType cursorType = KeyBoardFontModel().getCursorType();
      int lineIndex = KeyBoardFontModel().getLineIndex();
      LineData line = KeyBoardFontModel().getAllLineData()[lineIndex];
      if (line.groupHandle == KeyBoardFontModel.fakeLineHandle) {
        lineIndex = lineIndex - 1;
        line = KeyBoardFontModel().getAllLineData()[lineIndex];
      } else {
        /// do nothing
      }

      /// カレントグループのハンドル
      final currentGroupHandle = PatternModel().getCurrentGroupHandle();
      if (line.charList.isNotEmpty) {
        Pattern pattern = line.embGroup;

        if (pattern is EmbGroup) {
          final data = PreviewModel().getGroupPatternDisplayInfo(
            scrollType: ScrollCenterType.IMAGE_SELECT_FONT,
            group: pattern,
            centerPoint: _originalCenter,
            pixelOfOneMm: _pixelOfOneMm,
            sewingIndex: 0,
            cursorHandle: cursorHandle,
            cursorType: cursorType,
            currentGroupHandle: currentGroupHandle,
            zoomScale: PatternModel().selectedZoomScaleInSelectPage,
          );

          displayData![lineIndex] = data;
        } else {
          /// Do noting
        }
      }
    }
  }

  ///
  /// 拡大率が変化すると、ZoomOutの拡大点変換
  ///
  Offset _getZoomOutCenterPoint() {
    Offset zoomOutPoint = state.zoomOutPoint;
    int currentZoomScale = PatternModel().selectedZoomScaleInSelectPage;
    double magnification = getMagnification;

    /// 拡大率は変化していない
    if (_prevZoomScale == currentZoomScale) {
      /// Do noting
    } else if (currentZoomScale == zoomList.first) {
      /// 拡大率が100%に戻ると、拡大点がリセットされます。
      zoomOutPoint = const Offset(0, 0);
    } else if (currentZoomScale > _prevZoomScale) {
      /// 拡大時、画面の現在の拡大点を探す
      /// 現在位置から（0、0）までの長さ と 新しい拡大点までの長さには比例があります。
      /// 例:400%の場合、拡大点から（0、0）まで、1/4箇所が新しい拡大点です
      zoomOutPoint = zoomOutPoint - (zoomOutPoint / magnification);
    } else {
      /// 縮小時、拡大点は変わらない
      /// Do noting
    }

    /// 新しいView中心点の計算
    /// 拡大点から元の中心点までの距離 と View中心点から元の中心点までの距離 には比例があります。
    /// 上で計算された拡大点の割合と同じです。

    /// バックアップ拡大率
    _prevZoomScale = PatternModel().selectedZoomScaleInSelectPage;

    return zoomOutPoint;
  }

  ///
  /// 二重指拡張時のScale値変換
  /// 拡大時、scale範囲は1より大きく、scale値が大きいほど拡大する(1精度)
  /// 縮小時、scale範囲0 ~ 1、scale値が小さいほど縮小されます(0.1精度)
  ///
  int _getDoubleFingerStep(double scale) {
    int step = 0;
    if (scale > 1) {
      /// 例：Scale=1.2   ===>   Step = 1；
      /// 例：Scale=3.2   ===>   Step = 3；
      step = scale.toInt();
    } else if (scale >= 0 && scale < 1) {
      /// 例：Scale=0.8   ===>   Step = -2；
      /// 例：Scale=0.5   ===>   Step = -5；
      step = -((1 - scale) * 10).toInt();
    } else {
      step = 0;
    }

    return step;
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// ↓ ↓ ↓ ↓ ↓ ↓ ↓ パターンの移動に関連するデータと関数  ↓ ↓ ↓ ↓ ↓ ↓ ↓
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// 2本指モード
  ///
  bool _isTwoFinger = false;

  ///
  /// ドラッグモード
  ///
  bool _isDaring = false;

  ///
  /// 移動開始時のバックアップの模様の表示情報
  ///
  List<PatternDisplayInfo> _patternMoveDispInfoListBackup = [];

  ///
  /// ドラッグが開始される位置
  ///
  Offset _startMovePoint = const Offset(0, 0);

  ///
  /// 移動開始時のバックアップされた模様の最大範囲
  ///
  Rect _maxRectSizeBackup = Rect.largest;

  ///
  /// 選択したすべての模様のX座標とY座標のリスト
  ///
  final List<double> _xList = [];
  final List<double> _yList = [];

  /// UIでのプレビューの実際のスコープ
  final Rect _zoomPreviewRect = Rect.fromCenter(
    center: _originalCenter,
    width: charInputPreviewSize.dx,
    height: charInputPreviewSize.dy,
  );

  ///
  /// Patternをドラッグできますか
  ///
  bool _patternDragEnable() {
    if (PreviewModel().isInDragPreviewMode == true) {
      return false;
    }

    return true;
  }

  @override
  void backupPatternDisplayInfoInMove() {
    _patternMoveDispInfoListBackup.clear();
    _patternMoveDispInfoListBackup =
        List.from(state.charGroupPatternDisplayInfoList);
    _maxRectSizeBackup = _getMaxRectSize();
  }

  @override
  void dargPatternStart(ScaleStartDetails details) {
    if (_patternDragEnable() == false) {
      return;
    }

    if (details.pointerCount > _oneFinger) {
      _isTwoFinger = true;
    } else {
      /// do nothing
    }

    _isDaring = true;

    _startMovePoint = details.localFocalPoint;

    /// パターンの開始情報をバックアップする
    backupPatternDisplayInfoInMove();

    /// View更新
    update();
  }

  @override
  void dargPattern(ScaleUpdateDetails details) {
    if (_patternDragEnable() == false) {
      return;
    }

    if (details.pointerCount > _oneFinger) {
      int step = _getDoubleFingerStep(details.scale);

      /// 変更されていない場合は更新しない
      if (_prevDoubleFingerSizeStep == step) {
        return;
      }

      /// 模様サイズを変更する
      final EmbGroup currentGroup = PatternModel().getCurrentGroup();
      currentGroup.changeSizeEmbGroup(
          type: MagType.xyAll, step: step - _prevDoubleFingerSizeStep);

      final Pattern currentPattern =
          PatternModel().getCurrentPattern(curGroupHandle: currentGroup.handle);
      if (currentPattern is EmbBorder) {
        currentPattern
          ..clearGroupInfoCache()
          ..clearBorderCompInfoCache()
          ..clearBorderInfoCache()
          ..clearGroupImageCache();
      } else if (currentPattern is EmbGroup) {
        currentPattern
          ..clearGroupInfoCache()
          ..clearMainImageCache();
      } else {
        /// Do nothing
      }

      _prevDoubleFingerSizeStep = step;

      /// View更新
      update();
    } else {
      /// 初めて文字を入力するときは、ドラッグできません
      if (KeyBoardFontModel().isReEditMode == false) {
        return;
      }

      Offset offset = details.localFocalPoint;

      int moveTotalLibX = PatternModel().moveTotalLibX;
      int moveTotalLibY = PatternModel().moveTotalLibY;

      /// 今回は移動の合計量を計算
      Offset moveOffset = offset - _startMovePoint;

      /// プレビュー領域内のパターンの動きを制限します
      double newLeft = (_maxRectSizeBackup.left + moveOffset.dx).clamp(
        _zoomPreviewRect.left,
        _zoomPreviewRect.right - _maxRectSizeBackup.width,
      );
      double newTop = (_maxRectSizeBackup.top + moveOffset.dy).clamp(
        _zoomPreviewRect.top,
        _zoomPreviewRect.bottom - _maxRectSizeBackup.height,
      );

      /// パターンの実際の移動量を計算します
      moveOffset = Offset(
          newLeft - _maxRectSizeBackup.left, newTop - _maxRectSizeBackup.top);

      /// APIに必要な水平方向と垂直方向の移動量は、
      /// 実際の移動量に基づいて計算され、移動は0.5mm未満です.5mm舍去
      moveTotalLibX = (moveOffset.dx * _mmOfOnePixel) ~/ _oneMoveStepInMm;
      moveTotalLibY = (moveOffset.dy * _mmOfOnePixel) ~/ _oneMoveStepInMm;

      /// UIの実際の移動は、APIに必要な水平方向と垂直方向の移動量によって計算されます
      double moveXOffset = moveTotalLibX * _oneMoveStepInMm * _pixelOfOneMm;
      double moveYOffset = moveTotalLibY * _oneMoveStepInMm * _pixelOfOneMm;
      moveOffset = Offset(moveXOffset, moveYOffset);

      /// Model 更新
      PatternModel()
        ..moveTotalLibX = moveTotalLibX
        ..moveTotalLibY = moveTotalLibY;

      /// view更新
      _updateMovedPatternDisplayInfo(moveOffset);
    }
  }

  @override
  void dargPatternEnd() {
    if (_patternDragEnable() == false) {
      return;
    }

    /// データの初期化
    _prevDoubleFingerSizeStep = 0;
    _startMovePoint = const Offset(0, 0);

    /// 初めて文字を入力するときは、ドラッグできません
    if (KeyBoardFontModel().isReEditMode == true) {
      /// Model更新
      PatternModel()
        ..moveEmb(PatternModel().moveTotalLibX, PatternModel().moveTotalLibY)
        ..moveTotalLibX = 0
        ..moveTotalLibY = 0;
    } else {
      /// do nothing
    }

    _isDaring = false;
    _isTwoFinger = false;

    /// view更新
    update();
  }

  @override
  bool moveByArrowKey() {
    /// フレームを超えているかどうか
    bool isOverFrame = false;

    /// modelの使用回数を減らすために必要な値を定義します
    int moveTotalLibX = PatternModel().moveTotalLibX;
    int moveTotalLibY = PatternModel().moveTotalLibY;

    /// 今回は移動の合計量を計算
    double moveXOffset = moveTotalLibX * _oneMoveStepInMm * _pixelOfOneMm;
    double moveYOffset = moveTotalLibY * _oneMoveStepInMm * _pixelOfOneMm;

    /// 新しい頂点座標は、移動量から計算されます
    double newLeft = _maxRectSizeBackup.left + moveXOffset;
    double newTop = _maxRectSizeBackup.top + moveYOffset;
    double newRight = newLeft + _maxRectSizeBackup.width;
    double newBottom = newTop + _maxRectSizeBackup.height;

    /// 右の境界か左の境界を越える
    if (newRight > _zoomPreviewRect.right || newLeft < _zoomPreviewRect.left) {
      isOverFrame = true;
      newLeft = newLeft.clamp(_zoomPreviewRect.left,
          _zoomPreviewRect.right - _maxRectSizeBackup.width);

      double newMoveXOffset = newLeft - _maxRectSizeBackup.left;
      moveTotalLibX = (newMoveXOffset * _mmOfOnePixel) ~/ _oneMoveStepInMm;

      /// 左上、左下、右上、右下に移動すると、X方向とY方向は同じ量だけ移動します
      if (moveTotalLibY != 0) {
        moveTotalLibY =
            moveTotalLibY > 0 ? moveTotalLibX.abs() : -moveTotalLibX.abs();
      } else {
        /// Do nothing
      }
    } else if (newBottom > _zoomPreviewRect.bottom ||
        newTop < _zoomPreviewRect.top) {
      isOverFrame = true;

      /// 上の境界か下の境界を越える
      newTop = newTop.clamp(_zoomPreviewRect.top,
          _zoomPreviewRect.bottom - _maxRectSizeBackup.height);

      double newMoveYOffset = newTop - _maxRectSizeBackup.top;
      moveTotalLibY = (newMoveYOffset * _mmOfOnePixel) ~/ _oneMoveStepInMm;

      /// 左上、左下、右上、右下に移動すると、X方向とY方向は同じ量だけ移動します
      if (moveTotalLibX != 0) {
        moveTotalLibX =
            moveTotalLibX > 0 ? moveTotalLibY.abs() : -moveTotalLibY.abs();
      } else {
        /// Do nothing
      }
    }

    moveXOffset = moveTotalLibX * _oneMoveStepInMm * _pixelOfOneMm;
    moveYOffset = moveTotalLibY * _oneMoveStepInMm * _pixelOfOneMm;
    Offset moveOffset = Offset(moveXOffset, moveYOffset);

    /// Model更新
    PatternModel()
      ..moveTotalLibX = moveTotalLibX
      ..moveTotalLibY = moveTotalLibY;

    /// view更新
    _updateMovedPatternDisplayInfo(moveOffset);

    return isOverFrame;
  }

  ///
  /// 選択したすべての模様の最大四角形範囲を取得します
  ///
  Rect _getMaxRectSize() {
    _xList.clear();
    _yList.clear();

    List<PatternDisplayInfo> patternList =
        List.from(_patternMoveDispInfoListBackup);
    List<PatternDisplayInfo> selectedPatternInfoList = [];

    for (var element in patternList) {
      if (element.isSelected || element.isCurrentPattern) {
        selectedPatternInfoList.add(element);
      }
    }

    /// 模様が選択されている場合
    if (selectedPatternInfoList.length == 1) {
      PatternDisplayInfo patternInfo = selectedPatternInfoList.first;
      EmbBorderDisplayInfo borderInfo = patternInfo.borderDisplayInfoList.first;
      EmbGroupDisplayInfo groupInfo = borderInfo.groupDisplayInfoList.first;
      EmbPatternDisplayInfo embPatternInfo =
          groupInfo.embPatternDisplayInfoList.first;

      if (groupInfo.isFont) {
        /// 文字模様
        Offset topLeft = patternInfo.maskDisplayInfo.topLeft;
        Offset bottomRight = patternInfo.maskDisplayInfo.bottomRight;

        _xList.add(topLeft.dx);
        _yList.add(topLeft.dy);
        _xList.add(bottomRight.dx);
        _yList.add(bottomRight.dy);
      } else {
        if (patternInfo.isBorder) {
          /// 組み合わせ模様
          Offset topLeft = patternInfo.maskDisplayInfo.topLeft;
          Offset bottomRight = patternInfo.maskDisplayInfo.bottomRight;

          _xList.add(topLeft.dx);
          _yList.add(topLeft.dy);
          _xList.add(bottomRight.dx);
          _yList.add(bottomRight.dy);
        } else {
          double top = groupInfo.top + borderInfo.top + patternInfo.top;
          double left = groupInfo.left + borderInfo.left + patternInfo.left;

          double imageTop = embPatternInfo.imageTop + top;
          double imageLeft = embPatternInfo.imageLeft + left;
          double imageBottom = imageTop + embPatternInfo.imageHeight;
          double imageRight = imageLeft + embPatternInfo.imageWidth;

          _xList.add(imageLeft);
          _yList.add(imageTop);
          _xList.add(imageRight);
          _yList.add(imageBottom);
        }
      }
    } else if (selectedPatternInfoList.length > 1) {
      /// 複数の模様を選択した場合

      List<double> patternItemXList = [];
      List<double> patternItemYList = [];

      for (var pattern in selectedPatternInfoList) {
        if (pattern
            .borderDisplayInfoList.first.groupDisplayInfoList.first.isFont) {
          /// 文字模様
          Offset topLeft = pattern.maskDisplayInfo.topLeft;
          Offset bottomRight = pattern.maskDisplayInfo.bottomRight;

          _xList.add(topLeft.dx);
          _yList.add(topLeft.dy);
          _xList.add(bottomRight.dx);
          _yList.add(bottomRight.dy);
        } else {
          /// 文字以外の模様
          Offset patternTopLeft = Offset(pattern.left, pattern.top);
          for (var border in pattern.borderDisplayInfoList) {
            Offset borderTopLeft = Offset(border.left, border.top);
            for (var group in border.groupDisplayInfoList) {
              Offset groupTopLeft = Offset(group.left, group.top);
              for (var embPattern in group.embPatternDisplayInfoList) {
                /// ページ上の相対位置オフセットの回復します
                Offset topLeft = groupTopLeft + borderTopLeft + patternTopLeft;
                double imageTop = embPattern.imageTop + topLeft.dy;
                double imageLeft = embPattern.imageLeft + topLeft.dx;
                double imageBottom = imageTop + embPattern.imageHeight;
                double imageRight = imageLeft + embPattern.imageWidth;

                patternItemXList.add(imageLeft);
                patternItemXList.add(imageRight);
                patternItemYList.add(imageTop);
                patternItemYList.add(imageBottom);
              }
            }
          }

          final (
            topMin: top,
            leftMin: left,
            bottomMax: bottom,
            rightMax: right
          ) = PreviewModel.getMaxOuterWithXYList(
            xList: patternItemXList,
            yList: patternItemYList,
          );

          _xList.add(left);
          _yList.add(top);
          _xList.add(right);
          _yList.add(bottom);
        }
      }
    } else {
      /// Error
    }

    final (
      topMin: topMin,
      leftMin: leftMin,
      bottomMax: bottomMax,
      rightMax: rightMax
    ) = PreviewModel.getMaxOuterWithXYList(xList: _xList, yList: _yList);

    return Rect.fromLTRB(leftMin, topMin, rightMax, bottomMax);
  }

  ///
  /// 移動後に表示データを更新する
  ///
  void _updateMovedPatternDisplayInfo(Offset moveOffset) {
    /// 選択したすべての模様の位置情報を更新します
    List<PatternDisplayInfo> updatedPatternInfoList = [];
    for (var pattern in _patternMoveDispInfoListBackup) {
      if (pattern.isSelected || pattern.isCurrentPattern) {
        List<EmbBorderDisplayInfo> borderDisplayInfoList = [];
        for (var border in pattern.borderDisplayInfoList) {
          List<EmbGroupDisplayInfo> groupDisplayInfoList = [];
          for (var group in border.groupDisplayInfoList) {
            groupDisplayInfoList
                .add(group.copyWith(arcImageOffset: moveOffset));
          }
          borderDisplayInfoList.add(
            border.copyWith(
              maskDisplayInfo: Mask(
                topLeft: border.maskDisplayInfo.topLeft + moveOffset,
                topRight: border.maskDisplayInfo.topRight + moveOffset,
                bottomLeft: border.maskDisplayInfo.bottomLeft + moveOffset,
                bottomRight: border.maskDisplayInfo.bottomRight + moveOffset,
              ),
              baseMaskInfo: Mask(
                topLeft: border.baseMaskInfo.topLeft + moveOffset,
                topRight: border.baseMaskInfo.topRight + moveOffset,
                bottomLeft: border.baseMaskInfo.bottomLeft + moveOffset,
                bottomRight: border.baseMaskInfo.bottomRight + moveOffset,
              ),
              groupDisplayInfoList: groupDisplayInfoList,
            ),
          );
        }

        updatedPatternInfoList.add(
          pattern.copyWith(
            left: pattern.left + moveOffset.dx,
            top: pattern.top + moveOffset.dy,
            maskDisplayInfo: Mask(
              topLeft: pattern.maskDisplayInfo.topLeft + moveOffset,
              topRight: pattern.maskDisplayInfo.topRight + moveOffset,
              bottomLeft: pattern.maskDisplayInfo.bottomLeft + moveOffset,
              bottomRight: pattern.maskDisplayInfo.bottomRight + moveOffset,
            ),
            borderDisplayInfoList: borderDisplayInfoList,
          ),
        );
      } else {
        updatedPatternInfoList.add(pattern);
      }
    }

    /// view更新
    state = state.copyWith(
      charGroupPatternDisplayInfoList: updatedPatternInfoList,
    );
  }

  @override
  void dispose() {
    super.dispose();
    KeyBoardFontModel().reset();
  }
}
