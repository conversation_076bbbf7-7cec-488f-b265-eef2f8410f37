import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../global_popup/global_popups/err_emb_pattern_exceeded/err_emb_pattern_exceeded_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_emb_too_much_selected_go_menu/err_emb_too_much_selected_go_menu_view_model.dart';
import '../../../../../model/area_model.dart';
import '../../../../../model/key_board_font_model.dart';
import '../../../../../model/pattern_data_reader/character_font_image_reader.dart';
import '../../../../../model/pattern_model.dart';
import '../../../../../model/preview_model.dart';
import '../../../../page_route.dart';
import '../text_editing_page_view_model.dart';
import 'font_select_popup/font_select_popup.dart';
import 'parameter_set_interface.dart';

///
/// 通常書体のフォントサイズの最大値
///
const int _normalFontSizeMaxValue = 1000;

///
/// 通常書体のフォントサイズの最小値
///
const int _normalFontSizeMinValue = 70;

///
/// スモール書体のフォントサイズの最大値
///
const int _smallFontSizeMaxValue = 100;

///
/// スモール書体のフォントサイズの最小値
///
const int _smallFontSizeMinValue = 40;

///
/// デフォルトで使用可能なボックスのインデックス
///
const int _defaultFrameIndex = 0;

///
/// サイズ変更の小ステップ（1）
///
const int _sizeSmallStep1 = 10;

///
/// サイズ変更の大ステップ（5）
///
const int _sizeLargeStep5 = 50;

///
/// 最小の大ステップのフォントサイズ
/// フォントサイズがこの値(10)未満のとき、ステップを 1 に固定する
///
const int _minLargeStepSize10 = 100;

///
/// スモール書体のサイズ変更短押ステップ(0.5)
///
const int _smallFontSizeSmallStep = 5;

///
/// スモール書体のサイズ変更長押ステップ(1)
///
const int _smallFontSizeLargeStep = 10;

final parameterSetViewModelProvider = StateNotifierProvider.autoDispose<
    ParameterSetViewInterface, ParameterSetState>(
  (ref) => ParameterSetViewModel(ref),
);

class ParameterSetViewModel extends ParameterSetViewInterface {
  ParameterSetViewModel(Ref ref) : super(ParameterSetState(fontType: ''), ref) {
    update();
  }

  ///
  /// フォントのポップアップがポップアップしました
  ///
  bool isFontPopupOpen = false;

  @override
  void update() {
    ///
    /// すべてのデータコンテンツ
    ///
    final List<List<DisPlayCharInfo>> displayCharData = _getDisPalyCharDate();

    /// カーソルが置かれている行数を返します。
    final int cursorAreaLine = _getCursorLocalLines();

    /// 表示されるデータの範囲を変更する
    final (disPlayStartLineIndex, disPlayEndLineIndex) = _getDisplayDataRange(
      cursorAreaLine,
      displayCharData,
      _disPlayEndLineIndex,
      KeyBoardFontModel().getTextBufferIndex(),
      KeyBoardFontModel().getTextBuffer(),
    );

    _disPlayStartLineIndex = disPlayStartLineIndex;
    _disPlayEndLineIndex = disPlayEndLineIndex;

    /// テキスト画像のサイズを取得する
    String patternWidth = "----";
    String patternHight = "----";

    /// 使用可能なボックスのインデックス
    int frameIndex = _defaultFrameIndex;

    /// 入力テキストが空でない場合は、パターンのサイズが取得されます
    if (KeyBoardFontModel().getTextBuffer().isNotEmpty) {
      var (width, hight) = KeyBoardFontModel().getCharacterPatternSize();
      patternWidth = PatternModel().changeValueToDisplay(width);
      patternHight = PatternModel().changeValueToDisplay(hight);
      int characterFrameIndex = KeyBoardFontModel().getFrameIndex(width, hight);
      PatternDispInfo patternDispInfo =
          PatternModel().getPatternInfo(isAllPattern: true);
      int totalFrameIndex = AreaModel().getEmbTopBarFrameIndex(patternDispInfo);
      frameIndex = totalFrameIndex > characterFrameIndex
          ? totalFrameIndex
          : characterFrameIndex;
    } else {
      /// do nothing
    }

    final bool isMultiCharSelectMode =
        KeyBoardFontModel().getSelectMode() == SelectModeEnum.multiLetter;

    state = state.copyWith(
      fontType: KeyBoardFontModel().fontName,
      fontTypeImage: _getFontImage(),
      fontSize: _getDisplayFontSize(),
      isInch: PatternModel().isUnitMm == false,
      patternWidth: patternWidth,
      patternHight: patternHight,
      frameIndex: frameIndex,
      isMultiCharSelectMode: isMultiCharSelectMode,
      redLinePosition: _getRedLineOffset(
        _disPlayStartLineIndex,
        _disPlayEndLineIndex,
        KeyBoardFontModel().getTextBufferIndex(),
        displayCharData,
        KeyBoardFontModel().getTextBuffer(),
      ),
      dashedBoxRect: _getDashedBoxRect(
        _disPlayStartLineIndex,
        _disPlayEndLineIndex,
        KeyBoardFontModel().getTextBufferIndex(),
        displayCharData,
        KeyBoardFontModel().getTextBuffer(),
      ),
      leftMoveButtonState: _leftMoveButtonState(),
      rightMoveButtonState: _rightMoveButtonState(),
      disPlayText: _getActuallyDisPalyData(
        _disPlayStartLineIndex,
        _disPlayEndLineIndex,
        displayCharData,
      ),
      realPreviewButtonState: _realPreviewButtonState(),
      zoomScaleIndex:
          zoomDisplayList.indexOf(PatternModel().selectedZoomScaleInSelectPage),
      isHandleSelected: PreviewModel().isInDragPreviewMode,
      isExclusiveType: _isExclusiveScriptType(),
    );
  }

  @override
  List<int> get zoomDisplayList => zoomList;

  ///
  /// SelectAllクリックイベント
  ///
  @override
  void onSelectAllButtonClicked() {
    /// Model更新
    KeyBoardFontModel().setSelectMode(SelectModeEnum.multiLetter);

    /// View 更新
    update();

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.parameterSet);
  }

  ///
  /// Radioクリックイベント
  /// TODO :http://apngoap306.ap.brothergroup.net:8080/jira/browse/PHBSH-2301
  ///
  @override
  void onOneLetterButtonClicked() {
    /// Model更新
    KeyBoardFontModel().setSelectMode(SelectModeEnum.oneLetter);

    /// View 更新
    update();

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.parameterSet);
  }

  @override
  void onOpenFontPopupClicked(BuildContext context) {
    if (KeyBoardFontModel().getTextBuffer().isNotEmpty &&
        KeyBoardFontModel().getTextBuffer().last == KeyBoardFontModel.cr) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    if (isFontPopupOpen == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      isFontPopupOpen = true;
      PopupNavigator.push(
        context: context,
        popup: PopupRouteBuilder(
          builder: (context) => const FontSelectPopup(),
          barrier: false,
        ),
      ).then((value) => isFontPopupOpen = false);
    } else {
      /// do nothing
    }
  }

  ///
  /// 現在実装されていないカーソルの移動方法(削除しない)
  ///
  @override
  void onLeftButtonOnClicked() {
    if (KeyBoardFontModel().isBeginPosition()) {
      return;
    }

    /// グアンビャオを移動
    KeyBoardFontModel().moveCursorLeft();

    /// 更新
    update();

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.parameterSet);
  }

  @override
  void onRightButtonOnClicked() {
    if (KeyBoardFontModel().isFinalPosition()) {
      return;
    }

    /// グアンビャオを移動
    KeyBoardFontModel().moveCursorRight();

    /// 更新
    update();

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.parameterSet);
  }

  @override
  bool onMinusButtonOnClicked(bool isLongPress) {
    int displayFontSize = KeyBoardFontModel().displayFontSize;
    final fontType = _getSelectCharacterFontInfo().type;

    /// スモールフォント
    if (fontType == KeyBoardFontModel.emcTypeSmall) {
      if (displayFontSize <= _smallFontSizeMinValue) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }

      if (isLongPress == false) {
        /// 短押しの場合、ステップは0.5
        displayFontSize -= _smallFontSizeSmallStep;
      } else {
        /// 長押しの場合、ステップは1
        final int remainder = displayFontSize % _smallFontSizeLargeStep;
        displayFontSize -=
            (remainder != 0) ? remainder : _smallFontSizeLargeStep;
      }
    }

    /// 通常サイズの書体
    else if (fontType == KeyBoardFontModel.emcTypeAlp) {
      if (displayFontSize <= _normalFontSizeMinValue) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }

      /// サイズが10未満または短押しのとき、ステップは1
      if (isLongPress == false || displayFontSize <= _minLargeStepSize10) {
        displayFontSize -= _sizeSmallStep1;
      } else {
        /// サイズが10を超えて長押しの場合、ステップは5
        final int remainder = displayFontSize % _sizeLargeStep5;
        displayFontSize -= (remainder != 0) ? remainder : _sizeLargeStep5;
      }
    }

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// model更新
    EmbCharLibraryError error =
        KeyBoardFontModel().setSizeInputChar(displayFontSize);
    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      if (error == EmbCharLibraryError.EMB_DATAOVER_ERR) {
        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
            arguments: ErrEmbTooMuchSelectedGoMenuArgument(
              onOKButtonClicked: GlobalPopupRoute().resetErrorState,
            ));
        return false;
      }
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_EMB_PATTERN_EXCEEDED,
        arguments: EmbPatternExceededArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return false;
    } else {
      /// do nothing
    }

    /// View 更新
    update();

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.parameterSet);

    return true;
  }

  @override
  bool onPlusButtonOnClicked(bool isLongPress) {
    int displayFontSize = KeyBoardFontModel().displayFontSize;
    final fontType = _getSelectCharacterFontInfo().type;

    /// スモールフォント
    if (fontType == KeyBoardFontModel.emcTypeSmall) {
      if (displayFontSize >= _smallFontSizeMaxValue) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }

      if (isLongPress == false) {
        /// 短押しの場合、ステップは0.5
        displayFontSize += _smallFontSizeSmallStep;
      } else {
        /// 長押しの場合、ステップは1
        final int remainder = displayFontSize % _smallFontSizeLargeStep;
        displayFontSize += (remainder != 0)
            ? (_smallFontSizeLargeStep - remainder)
            : _smallFontSizeLargeStep;
      }
    }

    /// 通常サイズの書体
    else if (fontType == KeyBoardFontModel.emcTypeAlp) {
      if (displayFontSize >= _normalFontSizeMaxValue) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }

      /// サイズが10未満または短押しのとき、ステップは1
      if (isLongPress == false || displayFontSize < _minLargeStepSize10) {
        displayFontSize += _sizeSmallStep1;
      } else {
        /// サイズが10を超えて長押しの場合、ステップは5
        final int remainder = displayFontSize % _sizeLargeStep5;
        displayFontSize +=
            (remainder != 0) ? (_sizeLargeStep5 - remainder) : _sizeLargeStep5;
      }
    }

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// model更新
    EmbCharLibraryError error =
        KeyBoardFontModel().setSizeInputChar(displayFontSize);

    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      if (error == EmbCharLibraryError.EMB_DATAOVER_ERR) {
        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
            arguments: ErrEmbTooMuchSelectedGoMenuArgument(
              onOKButtonClicked: GlobalPopupRoute().resetErrorState,
            ));
        return false;
      }
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_EMB_PATTERN_EXCEEDED,
        arguments: EmbPatternExceededArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return false;
    } else {
      /// do nothing
    }

    /// View 更新
    update();

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.parameterSet);
    return true;
  }

  @override
  void onZoomPopupButtonClicked(int zoomValue) {
    /// Model更新
    PatternModel()
      ..selectedZoomScaleInSelectPage = zoomValue
      ..clearAllPatternImageCache();

    /// view更新
    state = state.copyWith(
      isZoomPopupOn: false,
      zoomScaleIndex: zoomDisplayList.indexOf(zoomValue),
    );

    /// 他のページへの更新の通知
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.zoomPopup);
  }

  @override
  void onZoomButtonClicked() {
    state = state.copyWith(isZoomPopupOn: true);
  }

  @override
  void closeZoomPopup() {
    state = state.copyWith(
      isZoomPopupOn: false,
    );
  }

  @override
  void onHandleButtonClicked() {
    /// Model更新
    PreviewModel().isInDragPreviewMode = !PreviewModel().isInDragPreviewMode;

    /// View更新
    state =
        state.copyWith(isHandleSelected: PreviewModel().isInDragPreviewMode);

    /// 他のページへの更新の通知
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.parameterSet);
  }

  @override
  void onRealPreviewClicked() {
    if (KeyBoardFontModel().isAllCharBlank() == true) {
      return;
    }

    /// model更新
    PatternModel().realPreviewDisplayType =
        KeyBoardFontModel().isReEditMode == false
            ? RealPreviewDisplayType.patternSelect
            : RealPreviewDisplayType.patternEdit;

    /// view更新
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.embRealPreview);
  }

  ///
  /// 全てのカテゴリのイコンデータを取得する
  /// - List<CategoryImageGroup>: 順番保存されているの模様イコンデータ
  ///
  List<CharacterFontImageGroup> _getAllCharacterFontImagesInfo() =>
      CharacterFontImageReader().getAllCharacterFontImagesInfo();

  ///
  /// 現在選択されているカテゴリのフォント データを取得します
  ///
  CharacterFontImageGroup _getSelectCharacterFontInfo() =>
      _getAllCharacterFontImagesInfo().firstWhere(
          (element) => element.fontNumber == KeyBoardFontModel().fontNumber);

  ///
  /// フォント画像を取得
  ///
  Widget _getFontImage() {
    CharacterFontImageGroup group = _getSelectCharacterFontInfo();
    return group.image;
  }

  ///
  /// ExclusiveScriptフォントの判断
  ///
  bool _isExclusiveScriptType() {
    CharacterFontImageGroup group = _getSelectCharacterFontInfo();
    return group.fontNumber == KeyBoardFontModel.exclusiveScriptFontNumber;
  }

  ///
  /// 左ボタンの状態に移動
  ///
  static ButtonState _leftMoveButtonState() {
    final List<String> textBuffer = KeyBoardFontModel().getTextBuffer();
    final bool isBeginPosition = KeyBoardFontModel().isBeginPosition();

    if (textBuffer.isEmpty || isBeginPosition) {
      return ButtonState.disable;
    } else {
      return ButtonState.normal;
    }
  }

  ///
  /// ボタンの状態を右に移動する
  ///
  static ButtonState _rightMoveButtonState() {
    final List<String> textBuffer = KeyBoardFontModel().getTextBuffer();
    final bool isFinalPosition = KeyBoardFontModel().isFinalPosition();

    if (textBuffer.isEmpty || isFinalPosition) {
      return ButtonState.disable;
    } else {
      return ButtonState.normal;
    }
  }

  ///
  /// リアルプレビューボタンの状態
  ///
  ButtonState _realPreviewButtonState() {
    /// すべての文字が空白です
    bool isAllCharBlank = KeyBoardFontModel().isAllCharBlank();
    if (isAllCharBlank) {
      return ButtonState.disable;
    }

    /// 入力文字なし
    bool isInputCharEmpty = KeyBoardFontModel().getTextBuffer().isEmpty;
    if (isInputCharEmpty) {
      return ButtonState.disable;
    }

    return ButtonState.normal;
  }

  ///
  /// 表示されるデータの範囲を取得する
  ///
  static List<ShowDataList> _getActuallyDisPalyData(
    int disPlayStartLineIndex,
    int disPlayEndLineIndex,
    List<List<DisPlayCharInfo>> displayCharData,
  ) {
    /// 各計算の前に、実際に表示されるデータをクリアします
    List<ShowDataList> actuallyDisPalyData = [];

    /// すべての文字長
    double allTextWidth = 0.0;

    /// すべての文字高い
    double allTextHigh = 0.0;

    if (displayCharData.isEmpty) {
      return [];
    }

    if (disPlayEndLineIndex >= displayCharData.length) {
      disPlayEndLineIndex = displayCharData.length - _cursorMoveStep_1;
    } else {
      /// Do thing
    }

    /// すべてのデータ
    List<List<DisPlayCharInfo>> displayData = displayCharData.sublist(
      disPlayStartLineIndex,
      disPlayEndLineIndex + _cursorMoveStep_1,
    );

    for (var line in displayData) {
      allTextWidth = 0;

      for (var row in line) {
        double disPlayCharWidth = row.disPlayCharWidth;
        actuallyDisPalyData.add(
          ShowDataList(
            disPlayCharUniCode: row.disPlayCharUniCode,
            disPlayCharMarginLeft: allTextWidth,
            disPlayCharMarginTop: allTextHigh,
            disPlayCharWidth: disPlayCharWidth,
          ),
        );
        allTextWidth += disPlayCharWidth;
      }
      allTextHigh += _cursorMarginTop;
    }

    return actuallyDisPalyData;
  }

  ///
  /// 表示データ全体を取得します
  ///
  static List<List<DisPlayCharInfo>> _getDisPalyCharDate() {
    /// 各行の初期幅
    double eachLineWidth = 0.0;

    /// すべての文字オブジェクト
    List<DisPlayCharInfo> eachLineCharInfoList = [];

    /// データを変更するたびに、以前のデータが消去されます
    List<List<DisPlayCharInfo>> displayCharData = [];

    for (List<String> line in KeyBoardFontModel().getAllText()) {
      for (String item in line) {
        /// 各文字の幅を取得する
        final Size size = _boundingTextSize(item);

        /// 累積文字の幅
        eachLineWidth = eachLineWidth + size.width;

        /// 各文字オブジェクトを準備する
        final DisPlayCharInfo charInfo = DisPlayCharInfo(
          disPlayCharWidth: size.width,
          disPlayCharHeight: size.height,
          disPlayCharUniCode: item,
        );

        /// コレクションに 1 文字のオブジェクトを追加して、データの各行を形成します
        eachLineCharInfoList.add(charInfo);

        /// 各行が自動的にいっぱいになった場合
        if (eachLineWidth > _lineMaxWidth) {
          /// 上部を超えているものを削除します
          eachLineCharInfoList.removeLast();

          displayCharData.add(List.from(eachLineCharInfoList));

          eachLineCharInfoList.clear();

          /// 最後の行を次の行の初期値として追加します
          eachLineCharInfoList.add(charInfo);

          eachLineWidth = size.width;
        } else {
          /// do nothing
        }
      }
    }

    /// 行数が 3 行未満の場合は、3 行のデータを構成します
    displayCharData.add(eachLineCharInfoList);
    if (displayCharData.length < _maxDisplayLineNumbers) {
      int addEmptyLineCount = _maxDisplayLineNumbers - displayCharData.length;
      for (var i = 0; i < addEmptyLineCount; i++) {
        displayCharData.add([]);
      }
    } else {
      /// Do thing
    }

    return displayCharData;
  }

  ///
  /// 通常のテキストを入力すると、データの変更が表示されます
  ///
  (int, int) _getDisplayDataRange(
    int cursorAreaLineIndex,
    List<List<DisPlayCharInfo>> displayData,
    int lastDisplayEndLineIndex,
    int textBufferIndex,
    List<String> textBuffer,
  ) {
    /// データ全体が 2 行未満の場合
    if (displayData.length <= _maxDisplayLineNumbers) {
      _disPlayStartLineIndex = _minDisplayLineNumbers;
      _disPlayEndLineIndex = _maxDisplayLineNumbers;
      return (_disPlayStartLineIndex, _disPlayEndLineIndex);
    }

    /// 最後の行のインデックス
    int endLineIndex = displayData.length - 1;

    /// カーソルが一行目にあるとき
    if (cursorAreaLineIndex == 0) {
      return (cursorAreaLineIndex, _maxDisplayLineNumbers);
    }

    /// カーソルが最終行にある場合
    else if (cursorAreaLineIndex == endLineIndex) {
      return (endLineIndex - _maxDisplayLineNumbers, endLineIndex);
    }

    /// カーソルが別の行にある場合
    else {
      return (cursorAreaLineIndex - 1, cursorAreaLineIndex + 1);
    }
  }

  ///
  /// 現在の破線のボックスの位置を取得します
  ///
  static Rect? _getDashedBoxRect(
    int disPlayStartLineIndex,
    int disPlayEndLineIndex,
    int textBufferIndex,
    List<List<DisPlayCharInfo>> displayCharData,
    List<String> textBuffer,
  ) {
    double dashedBoxMarginLeft = 0.0;
    double dashedBoxMarginTop = 0.0;
    double dashedBoxWidth = 0.0;

    /// 左上に移動すると、点線のボックスは表示されません。 赤い線は1本だけです
    if (KeyBoardFontModel().getTextBufferIndex() ==
            KeyBoardFontModel.defaultTextBufferCursorIndex ||
        KeyBoardFontModel().getTextBuffer().isEmpty ||
        KeyBoardFontModel().getCursorType() != CursorType.selected) {
      return null;
    } else {
      /// 表示領域内のカーソルindex
      int disPlayCursorIndex = _getDisplayCursorIndex(
        displayCharData,
        disPlayStartLineIndex,
        textBufferIndex,
        textBuffer,
      );

      /// 実際に表示されている 3 行のデータを取得する
      List<ShowDataList> actuallyDisPalyData = _getActuallyDisPalyData(
        disPlayStartLineIndex,
        disPlayEndLineIndex,
        displayCharData,
      );

      dashedBoxMarginLeft =
          actuallyDisPalyData[disPlayCursorIndex].disPlayCharMarginLeft;

      dashedBoxMarginTop =
          actuallyDisPalyData[disPlayCursorIndex].disPlayCharMarginTop;

      dashedBoxWidth = actuallyDisPalyData[disPlayCursorIndex].disPlayCharWidth;

      Rect rect = Rect.fromLTWH(
          dashedBoxMarginLeft, dashedBoxMarginTop, dashedBoxWidth, _boxHeight);

      return rect;
    }
  }

  /// 実際の表示領域の位置を返します。
  static int _getDisplayCursorIndex(
    List<List<DisPlayCharInfo>> displayData,
    int disPlayStartLineIndex,
    int textBufferIndex,
    List<String> textBuffer,
  ) {
    /// カーソルが置かれている行数
    int cursorAreaLine = _getCursorLocalLines();

    /// 現在の行の前にある要素の数
    int currentRowFontElementsIndex =
        KeyBoardFontModel.defaultTextBufferCursorIndex;

    /// 中央の行
    int middleRow = disPlayStartLineIndex + _cursorMoveStep_1;

    /// 末尾行
    int trailing = disPlayStartLineIndex + _cursorMoveStep_2;

    // 表示位置でのカーソルの実際の位置
    int displayCursorIndex = 0;

    /// 0以下の場合、textButterカーソルが置かれている場所、 は実際に表示されるカーソルです。
    if (disPlayStartLineIndex <= 0) {
      return textBufferIndex;
    } else {
      for (int i = 0; i < cursorAreaLine; i++) {
        for (int j = 0; j < displayData[i].length; j++) {
          currentRowFontElementsIndex++;
        }
      }
      currentRowFontElementsIndex =
          textBufferIndex - currentRowFontElementsIndex;

      if (cursorAreaLine == disPlayStartLineIndex) {
        /// 現在の表示はスタートラインにあります

        displayCursorIndex = currentRowFontElementsIndex;
      } else if (cursorAreaLine == middleRow) {
        /// 現在、中央の列にあります
        displayCursorIndex = displayData[disPlayStartLineIndex].length +
            currentRowFontElementsIndex -
            _cursorMoveStep_1;
      } else if (cursorAreaLine == trailing) {
        /// 現在、行末
        displayCursorIndex = displayData[middleRow].length +
            displayData[disPlayStartLineIndex].length +
            currentRowFontElementsIndex -
            _cursorMoveStep_1;
      } else {
        /// do nothing
      }
      return displayCursorIndex;
    }
  }

  ///
  /// 赤い線の位置を取得する
  ///
  static Offset? _getRedLineOffset(
    int disPlayStartLineIndex,
    int disPlayEndLineIndex,
    int textBufferIndex,
    List<List<DisPlayCharInfo>> displayCharData,
    List<String> textBuffer,
  ) {
    double redLineMarginLeft = 0.0;
    double redLineMarginTop = 0.0;
    double width = 0.0;

    if (KeyBoardFontModel().getTextBuffer().isNotEmpty) {
      if (KeyBoardFontModel().isBeginPosition()) {
        return Offset(redLineMarginLeft, redLineMarginTop);
      } else {
        /// カーソルがどこにあるかを実際に表示します
        int disPlayCursorIndex = _getDisplayCursorIndex(
          displayCharData,
          disPlayStartLineIndex,
          textBufferIndex,
          textBuffer,
        );

        /// 表示される実際の 3 行のデータ
        List<ShowDataList> actuallyDisPalyData = _getActuallyDisPalyData(
          disPlayStartLineIndex,
          disPlayEndLineIndex,
          displayCharData,
        );

        if (KeyBoardFontModel().getCharIndex() == 0 &&
            KeyBoardFontModel().getCursorType() == CursorType.unselectLeft) {
          return Offset(
              actuallyDisPalyData[disPlayCursorIndex].disPlayCharMarginLeft,
              actuallyDisPalyData[disPlayCursorIndex].disPlayCharMarginTop);
        } else {
          width = actuallyDisPalyData[disPlayCursorIndex].disPlayCharWidth;
          redLineMarginLeft =
              actuallyDisPalyData[disPlayCursorIndex].disPlayCharMarginLeft +
                  width;
          redLineMarginTop =
              actuallyDisPalyData[disPlayCursorIndex].disPlayCharMarginTop;

          return Offset(redLineMarginLeft, redLineMarginTop);
        }
      }
    } else {
      return null;
    }
  }

  ///
  /// カーソルが置かれている行数を返します。
  ///
  static int _getCursorLocalLines() {
    List<List<String>> textBuffer = KeyBoardFontModel().getAllText();
    int lineIndex = KeyBoardFontModel().getLineIndex();
    int charIndex = KeyBoardFontModel().getCharIndex();

    /// 各行の初期幅
    double eachLineWidth = 0;
    int lineCount = 0;
    for (var i = 0; i < textBuffer.length; i++) {
      if (lineIndex == i && textBuffer[i].isEmpty) {
        return lineCount;
      }
      for (var j = 0; j < textBuffer[i].length; j++) {
        final String text = textBuffer[i][j];

        /// 各文字の幅を取得する
        final double width = _boundingTextSize(text).width;

        eachLineWidth += width;

        if (eachLineWidth > _lineMaxWidth) {
          lineCount++;
          eachLineWidth = width;
        }
        if (lineIndex == i && charIndex == j) {
          return lineCount;
        }
      }
    }
    return 0;
  }

  ///
  /// 文字の幅を計算します
  ///
  static Size _boundingTextSize(String text,
      {double maxWidth = double.infinity}) {
    if (text.isEmpty) {
      return Size.zero;
    }
    final TextPainter textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      text: TextSpan(
        text: text,
        style: const TextStyle(
          fontFamily: "Roboto",
          fontSize: 35,
          fontWeight: FontWeight.bold,
          height: 0,
          letterSpacing: 0,
        ),
      ),
    )..layout(maxWidth: maxWidth);
    return textPainter.size;
  }

  ///
  /// 表示するフォントサイズを取得する
  ///
  String _getDisplayFontSize() {
    int size = KeyBoardFontModel().getSelectedCharSize();
    KeyBoardFontModel().displayFontSize = size;

    return (size / conversionRate).toString().replaceAll(".0", "");
  }

  ///
  /// 点線のボックスの高さを固定する
  ///
  static const double _boxHeight = 35.0;

  ///
  /// 上からの文字距離
  ///
  static const double _cursorMarginTop = 61;

  ///
  /// カーソルはその都度位置を移動します
  ///
  static const int _cursorMoveStep_1 = 1;

  ///
  /// カーソルはその都度位置を移動します
  ///
  static const int _cursorMoveStep_2 = 2;

  ///
  /// 各行の最大幅
  ///
  static const int _lineMaxWidth = 294;

  ///
  /// 表示する最大行数
  ///
  static const int _maxDisplayLineNumbers = 2;

  ///
  /// 表示する最小行数
  ///
  static const int _minDisplayLineNumbers = 0;

  ///
  /// データ範囲の開始行を表示します
  ///
  int _disPlayStartLineIndex = 0;

  ///
  /// データ範囲の終了行を表示します
  ///
  int _disPlayEndLineIndex = 2;
}

///
/// テキストプレゼンテーション情報
///
class ShowDataList {
  const ShowDataList({
    required this.disPlayCharMarginLeft,
    required this.disPlayCharMarginTop,
    required this.disPlayCharUniCode,
    required this.disPlayCharWidth,
  });
  final double disPlayCharMarginLeft;
  final double disPlayCharMarginTop;
  final String disPlayCharUniCode;
  final double disPlayCharWidth;
}
