import 'dart:io' show IOException;
import 'dart:ui' as ui;

import 'package:panel_library/panel_library.dart';
import 'package:path/path.dart';

import '../../../../memory/memory.dart';
import '../../../../model/device_memory_model.dart';
import 'pattern_data_reader/disney_pattern_reader.dart';
import 'pattern_data_reader/one_point_pattern_reader.dart';
import 'pattern_model.dart';

/// 模様種類
enum CategoryType {
  unknownPattern, // 未知種類番号
  couching, // カウチング模様
  character, // 文字模様
  characterFont, // 文字フォント
  frame, // 枠模様
  bh, // BH模様
  onePoint, // one point模様
  disney, // Disney模様
  quilt, // Quilt模様
  exclusives, // Exclusives模様 taconyのみ
  longStitch, // longStitch模様
  largeConnect, // largeConnect模様
  memory; // memory 検査目的のみ
}

class SelectInformationModel {
  SelectInformationModel._internal();

  factory SelectInformationModel() => _instance;
  static final SelectInformationModel _instance =
      SelectInformationModel._internal();

  /// 読み込むUSBPath
  String selectedUSBPath = "";

  ///
  /// Pdfコピー関数
  ///
  Future<AccessError> copyPDFFileToDevice() =>
      DeviceMemoryModel().beginUsbTransaction(
          selectedUSBPath, () => _copyPDFFileToDevice(printAndStitchType));

  ///
  /// Pdfコピー関数
  ///
  Future<AccessError> _copyPDFFileToDevice(
      PrintAndStitchType printAndStitchMode) async {
    await Future.delayed(const Duration(seconds: 1));

    List<FileEntity> sourceFileList = [];
    List<String> sourceFileNameList =
        printAndStitchMode == PrintAndStitchType.whale
            ? [
                'EPdfBrotherEx11n.pdf',
                'EPdfBrotherEx11r.pdf',
                'EPdfBrotherEx11p.pdf'
              ]
            : ['EPdfDi2n.pdf', 'EPdfDi2r.pdf', 'EPdfDi2p.pdf'];

    List<String> targetFileNameList =
        printAndStitchMode == PrintAndStitchType.whale
            ? ['E_001n.pdf', 'E_001r.pdf', 'E_001p.pdf']
            : ['E_002n.pdf', 'E_002r.pdf', 'E_002p.pdf'];

    String sourceRootDirectory = memorySector.pnsPdf.absolutePath;
    String targetRootDirectory = join(selectedUSBPath, 'bPocket');

    AccessError accessError = AccessError.none;
    try {
      if (DirectoryEntity(targetRootDirectory).existsSync() == false) {
        DirectoryEntity(targetRootDirectory).createSync(recursive: true);
      } else {
        /// Do nothing
      }
    } on IOException catch (e) {
      accessError = memoryExceptionToAccessError(e, isWriteError: true);
      return accessError;
    }

    for (var sourceFileName in sourceFileNameList) {
      FileEntity sourceFile =
          FileEntity(join(sourceRootDirectory, sourceFileName));

      sourceFileList.add(sourceFile);
    }

    /// ファイルコピー
    for (int index = 0; index < targetFileNameList.length; index++) {
      String baseTargetPath =
          join(targetRootDirectory, targetFileNameList[index]);

      int loopTimes = 1;
      int inSertIndex = baseTargetPath.length - '.pdf'.length;
      String targetPath = baseTargetPath;

      /// 発生する可能性のある削除の例外
      try {
        while (FileEntity(targetPath).existsSync()) {
          targetPath =
              '${baseTargetPath.substring(0, inSertIndex)}($loopTimes)${baseTargetPath.substring(inSertIndex)}';
          loopTimes++;
        }
      } on IOException catch (e) {
        accessError = memoryExceptionToAccessError(e, isWriteError: false);
        return accessError;
      }

      /// 考えられる書き込み例外
      try {
        sourceFileList[index].copySync(targetPath);
      } on IOException catch (e) {
        accessError = memoryExceptionToAccessError(e, isWriteError: true);
        return accessError;
      }
    }

    return accessError;
  }

  ///
  /// 線色情報の取得
  /// libの情報をmodel用の線色情報に変換する
  ///
  static List<ThreadInfo> getTemporaryGroupThreadInfo() {
    List<ThreadInfo> threadInfoList = [];

    for (var temporaryGroup in PatternModel().temporaryGroupList) {
      threadInfoList.addAll(temporaryGroup.threadInfo);
    }

    return threadInfoList;
  }

  ///
  /// プリント＆ステッチ模様で表示される背景画像
  ///
  ui.Image? printBackgroundImage;

  ///
  ///	プリント＆ステッチカテゴリ
  ///
  PrintAndStitchType printAndStitchType = PrintAndStitchType.normal;

  ///
  /// 黒い背景を開く(true)/ 白い背景を開く(false)
  ///
  bool isBackGroundColorOn = false;

  ///
  /// 印刷データ表示(true)/ 印刷データ非表示(false)
  ///
  bool isImageDisplayOn = false;

  ///
  /// 全ての中カテゴリのイコンデータを取得する
  ///
  /// ##@return
  /// - List<DisneyPatternGroup>: 順番保存されているの模様イコンデータ
  ///
  List<DisneyPatternGroup> getDisneyPatternAllCategoryImagesInfo() =>
      DisneyPatternReader().getAllDisneyPatternInfo();

  ///
  /// 全ての中カテゴリのイコンデータを取得する
  ///
  /// ##@return
  /// - List<OnePointPatternGroup>: 順番保存されているの模様イコンデータ
  ///
  List<OnePointPatternGroup> getOnePointPatternAllCategoryImagesInfo() =>
      OnePointPatternReader().getAllOnePointPatternInfo();

  ///
  /// selectInformation専用 一時的に選択パタン
  ///
  CategoryType _temporaryCategory = CategoryType.unknownPattern;
  CategoryType get temporaryCategory => _temporaryCategory;
  CategoryType setTemporaryPatternAutoKind(CategoryType type) =>
      _temporaryCategory = type;

  ///
  /// このModelのすべてのデータを初期化する
  ///
  void reset() {
    selectedUSBPath = "";
    printBackgroundImage?.dispose();
    printBackgroundImage = null;
    printAndStitchType = PrintAndStitchType.normal;
    _temporaryCategory = CategoryType.unknownPattern;
  }
}
