import 'dart:io';

import 'package:flutter/material.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import 'area_model.dart';
import 'pattern_data_reader/character_font_image_reader.dart';
import 'pattern_model.dart';

enum BottomPopupOpenState {
  size,
  spacing,
  array,
  fontTypeChange,
  none,
}

enum SelectModeEnum {
  oneLetter,
  multiLetter,
}

///
/// キーボード領域の文字ラベル
///
enum Keyboard {
  tab1(left: 0, right: 26),
  tab2(left: 26, right: 52),
  tab3(left: 52, right: 76),
  tab4(left: 76, right: 100),
  tab5(left: 100, right: 127),
  tab6(left: 127, right: 159),
  tab7(left: 159, right: 190),
  tab8(left: 190, right: 217);

  const Keyboard({this.left = 0, this.right = 0});
  final int left;
  final int right;
}

///
/// テキスト基本情報
///
class DisPlayCharInfo {
  DisPlayCharInfo({
    required this.disPlayCharWidth,
    required this.disPlayCharHeight,
    required this.disPlayCharUniCode,
  });
  final double disPlayCharWidth;
  final double disPlayCharHeight;
  final String disPlayCharUniCode;
}

///
/// カーソルの種類
///
enum CursorType {
  unselectLeft,
  unselectRight,
  selected,
}

/// 文字配列のタイプ
enum ArrayTypes {
  /// 直線
  flat,

  /// 配列（斜め）
  slant,

  /// 配列（円弧系1）
  looseOutSide,

  /// 配列（円弧系3）
  steepOutSide,

  /// 配列（円弧系2）
  looseInSide,

  /// 配列（円弧系4）
  steepInSide;
}

///
/// カーソル情報
///
class CursorPosition {
  CursorPosition({
    required this.lineIndex,
    required this.charIndex,
  });

  /// 選択したテキスト行のインデックス、最初の行は 0 です
  int lineIndex;

  /// 選択したテキストのインデックスで、最初の文字は 0 です
  int charIndex;

  /// 現在のカーソル
  CursorType get cursorType {
    final result = EmbCharLibrary().apiBinding.getCursorInfo();

    if (result.caret == 2) {
      return CursorType.unselectRight;
    } else if (result.caret == 1) {
      return CursorType.unselectLeft;
    } else {
      return CursorType.selected;
    }
  }
}

///
/// 文字行のデータ構造
///
class LineData {
  LineData({
    required this.groupHandle,
  });
  MemHandle groupHandle;
  ArrayTypes get arrayType => embGroup.charArray;
  List<LineCharacterData> charList = [];

  /// 文字行のEmbGroup
  EmbGroup get embGroup {
    final embGroup = EmbGroup(groupHandle, _embType);
    _embType ??= embGroup.getEmbPatternType();
    return embGroup;
  }

  /// 模様タイプ
  EmbPatternType? _embType;
}

///
/// 文字のデータ構造
///
class LineCharacterData {
  LineCharacterData({
    required this.char,
    required this.handle,
    this.isSelected = false,
  });
  final String char;
  MemHandle handle;
  bool isSelected;
}

class KeyBoardFontModel {
  KeyBoardFontModel._internal();

  factory KeyBoardFontModel() => _instance;
  static final KeyBoardFontModel _instance = KeyBoardFontModel._internal();

  /// 最初に選択したフォントの種類
  String fontName = "";

  ///　フォント番号
  String fontNumber = "0";

  /// フォントの種類
  String selectedFontType = "EMC_TYPE_ALP";

  /// 文字入力サイズ
  int displayFontSize = 300;

  /// 下部のボタンの種類
  BottomPopupOpenState bottomPopupOpenState = BottomPopupOpenState.none;

  /// カーソル開始位置
  static const int defaultTextBufferCursorIndex = -1;

  /// 排他スクリプトの種類 フォント番号
  static const String emcTypeAlp = "EMC_TYPE_ALP";

  /// 排他スクリプトの種類 フォント番号
  static const String emcTypeSmall = "EMC_TYPE_SMALL";

  ///  [exclusiveScript]のフォント番号
  static const String exclusiveScriptFontNumber = "55";

  /// Exclusive Script フォントインデックス
  static const int exclusiveScriptFontIndex = -1;

  /// 改行の上書き
  static const String cr = "\u21B5";

  /// スペースの Unicode
  static const String blank = "\u0020";

  /// 通常書体のデフォルトサイズ
  static const int defaultFontSizeAlp = 300;

  /// スモール書体のデフォルトサイズ
  static const int defaultFontSizeSmall = 50;

  /// 偽のラインハンドル
  static const MemHandle fakeLineHandle = -1;

  ///
  /// 現在位置より1文字左に移動
  ///
  final int _moveCurLeft = -1;

  ///
  /// 現在位置より1文字右に移動
  ///
  final int _moveCurRight = 1;

  ///
  /// 再編集モードかどうか
  ///
  bool _isReEdit = false;
  bool get isReEditMode => _isReEdit;

  ///
  /// サイズ変更ステップ
  ///
  final int _sizeChangeStep = 1;

  ///
  /// すべてのテキスト行のデータ
  ///
  final List<LineData> _lineDataList = [];

  ///
  /// カーソルのデータ
  ///
  final CursorPosition _cursorPosition =
      CursorPosition(lineIndex: 0, charIndex: 0);

  ///
  /// 一時データが追加されました
  ///
  bool _addTemporaryLine = false;

  ///
  /// すべての文字を更新する必要があります
  ///
  bool _needUpdateAllPreviewData = true;
  bool get needUpdateAllPreviewData => _needUpdateAllPreviewData;

  ///
  /// テキスト画像のサイズ
  ///
  (int width, int hight) _characterPatternSize = (0, 0);

  ///
  /// 現在選択されているモード
  ///
  SelectModeEnum? _selectModeCache;
  SelectModeEnum get _selectMode {
    if (_selectModeCache == null) {
      final result = EmbCharLibrary().apiBinding.getCharSelectionMode();
      if (result.errorCode != EmbCharLibraryError.EMB_NO_ERR) {
        while (true) {
          Log.errorTrace(
              "getCharSelectionMode return error:${result.errorCode}");
          sleep(Durations.extralong4);
        }
      } else {
        _selectModeCache = SelectModeEnum.values[result.charSelectMode];
      }
    }
    return _selectModeCache!;
  }

  ///
  /// 画面上のすべての表示データが更新されました。
  ///
  void updateAllPreviewDataFinished() => _needUpdateAllPreviewData = false;

  ///
  /// 文字編集開始（入力or再編集）
  ///
  EmbCharLibraryError startEmbCharEdit(bool isReEdit) {
    _isReEdit = isReEdit;
    return EmbCharLibrary().apiBinding.startEmbCharEdit(isReEdit);
  }

  ///
  /// 文字入力終了編集画面移行
  ///
  EmbCharLibraryError finishEmbCharEdit() =>
      EmbCharLibrary().apiBinding.finishEmbCharEdit();

  ///
  /// 文字入力キャンセル、前の画面移行
  ///
  EmbCharLibraryError cancelEmbCharEdit() =>
      EmbCharLibrary().apiBinding.cancelEmbCharEdit();

  ///
  /// 文字ラベルが有効かどうかのリスト
  ///
  List<bool> getTabEnableList() {
    List<bool> list = _getAllCharacterExistList();

    List<bool> tab1List =
        list.getRange(Keyboard.tab1.left, Keyboard.tab1.right).toList();
    List<bool> tab2List =
        list.getRange(Keyboard.tab2.left, Keyboard.tab2.right).toList();
    List<bool> tab3List =
        list.getRange(Keyboard.tab3.left, Keyboard.tab3.right).toList();
    List<bool> tab4List =
        list.getRange(Keyboard.tab4.left, Keyboard.tab4.right).toList();
    List<bool> tab5List =
        list.getRange(Keyboard.tab5.left, Keyboard.tab5.right).toList();
    List<bool> tab6List =
        list.getRange(Keyboard.tab6.left, Keyboard.tab6.right).toList();
    List<bool> tab7List =
        list.getRange(Keyboard.tab7.left, Keyboard.tab7.right).toList();
    List<bool> tab8List =
        list.getRange(Keyboard.tab8.left, Keyboard.tab8.right).toList();

    final bool isTab1Enable = tab1List.any((element) => element == true);
    final bool isTab2Enable = tab2List.any((element) => element == true);
    final bool isTab3Enable = tab3List.any((element) => element == true);
    final bool isTab4Enable = tab4List.any((element) => element == true);
    final bool isTab5Enable = tab5List.any((element) => element == true);
    final bool isTab6Enable = tab6List.any((element) => element == true);
    final bool isTab7Enable = tab7List.any((element) => element == true);
    final bool isTab8Enable = tab8List.any((element) => element == true);

    return [
      isTab1Enable,
      isTab2Enable,
      isTab3Enable,
      isTab4Enable,
      isTab5Enable,
      isTab6Enable,
      isTab7Enable,
      isTab8Enable
    ];
  }

  ///
  /// タグ内の文字が有効かどうか
  ///
  List<bool> getCharactersEnableList(int tabIndex) {
    List<bool> list = _getAllCharacterExistList();

    final List<List<bool>> tabList = [
      list.getRange(Keyboard.tab1.left, Keyboard.tab1.right).toList(),
      list.getRange(Keyboard.tab2.left, Keyboard.tab2.right).toList(),
      list.getRange(Keyboard.tab3.left, Keyboard.tab3.right).toList(),
      list.getRange(Keyboard.tab4.left, Keyboard.tab4.right).toList(),
      list.getRange(Keyboard.tab5.left, Keyboard.tab5.right).toList(),
      list.getRange(Keyboard.tab6.left, Keyboard.tab6.right).toList(),
      list.getRange(Keyboard.tab7.left, Keyboard.tab7.right).toList(),
      list.getRange(Keyboard.tab8.left, Keyboard.tab8.right).toList(),
    ];

    return tabList[tabIndex];
  }

  ///
  /// 文字パターン 90°回転
  ///
  void charRotate90() {
    EmbCharLibrary().apiBinding.charInputRotate90Set();
  }

  ///
  /// フレームインデックスを取得
  /// [width] : 現在のパターンの幅
  /// [hight] : 現在のパターンの高さ
  /// [centerPoint] : 現在のパターンの中心点の位置
  ///
  int getFrameIndex(int width, int hight) {
    int frameIndex = 0;
    List<FrameSize> embFrameList = [
      FrameSizeType.FRAME_100_100,
      FrameSizeType.FRAME_130_180,
      FrameSizeType.FRAME_272_272,
      FrameSizeType.FRAME_297_465,
    ];

    SSPoint centerPoint = const SSPoint(X: 0, Y: 0);
    frameIndex = embFrameList.indexWhere(
      (element) => AreaModel.checkPatternArea(
        element,
        RectanArea(
          left: -width ~/ 2,
          right: width ~/ 2,
          top: -hight ~/ 2,
          bottom: hight ~/ 2,
        ),
        centerPoint,
      ),
    );

    return frameIndex == -1 ? embFrameList.length - 1 : frameIndex;
  }

  ///
  /// 文字入力サイズ変更
  ///
  EmbCharLibraryError setSizeInputChar(int displayFontSize) {
    final error = EmbCharLibrary().apiBinding.setSizeInputChar(displayFontSize);

    if (error == EmbCharLibraryError.EMB_NO_ERR ||
        error == EmbCharLibraryError.EMB_EMBNODAT_ERR) {
      this.displayFontSize = displayFontSize;

      return EmbCharLibraryError.EMB_NO_ERR;
    } else {
      EmbCharLibrary().apiBinding.setSizeInputChar(this.displayFontSize);
      return error;
    }
  }

  ///
  /// すべての文字が有効かどうか
  ///
  List<bool> _getAllCharacterExistList() {
    final (errorCode: error, existList: existList) =
        EmbCharLibrary().apiBinding.getPf1CharExistList(int.parse(fontNumber));

    return error == EmbCharLibraryError.EMB_NO_ERR
        ? existList
        : List<bool>.generate(217, (index) => false);
  }

  ///
  /// すべてのデータが更新されます
  ///
  void _updateAllData() {
    /// 1 行目の GroupHandle
    final MemHandle topLineHandle =
        EmbCharLibrary().apiBinding.getCharEditTopHandle().handle;

    /// すべてのGroupHandle
    final List<MemHandle> handleList =
        EmbLibrary().apiBinding.getGroupHandleAll().handleList;

    /// すべてのキャラクターのGroupHandle
    List<MemHandle> lineHandleList;

    /// 再編集モードには1行のhandleしかありません
    if (_isReEdit) {
      lineHandleList = [topLineHandle];
    } else {
      lineHandleList = handleList.sublist(handleList.indexOf(topLineHandle));
    }

    /// すべてのテキストのリスト
    final List<LineCharacterData> charDataList = [];

    /// 全文を読む
    for (var lineData in _lineDataList) {
      for (var charData in lineData.charList) {
        if (charData.char != cr) {
          charDataList.add(charData);
        } else {
          /// do nothing
        }
      }
    }

    /// 元のデータを消去する
    _lineDataList.clear();

    /// 反復処理して新しいデータを追加する
    for (int lineIndex = 0; lineIndex < lineHandleList.length; lineIndex++) {
      var groupHandle = lineHandleList[lineIndex];
      LineData lineData = LineData(groupHandle: groupHandle);

      /// テキスト行の文字数
      final int patternNum =
          EmbCharLibrary().apiBinding.getPatternNum(groupHandle).patternNum;
      for (int charIndex = 0; charIndex < patternNum; charIndex++) {
        String char = "";
        if (charDataList.isNotEmpty) {
          char = charDataList.first.char;
          charDataList.removeAt(0);
        }

        /// テキストのhandle
        final MemHandle setHandle = EmbCharLibrary()
            .apiBinding
            .getPatternHandle(groupHandle, charIndex)
            .setHandle;

        /// テキストのデータ構造
        final LineCharacterData lineCharacterData = LineCharacterData(
          char: char,
          handle: setHandle,
        );

        lineData.charList.add(lineCharacterData);
      }

      _lineDataList.add(lineData);
    }
  }

  ///
  /// 現在の行のデータが更新されます
  ///
  void _updateCurrentLineData(bool forceUpdate) {
    final LineData oldLineData = _lineDataList[_cursorPosition.lineIndex];
    final MemHandle groupHandle = oldLineData.groupHandle;
    final List<LineCharacterData> charDataList = [];

    /// テキスト行の文字数
    final int patternNum =
        EmbCharLibrary().apiBinding.getPatternNum(groupHandle).patternNum;

    for (int charIndex = 0; charIndex < patternNum; charIndex++) {
      LineCharacterData oldCharData = oldLineData.charList[charIndex];

      final result =
          EmbCharLibrary().apiBinding.getPatternHandle(groupHandle, charIndex);
      if (result.errorCode != EmbCharLibraryError.EMB_NO_ERR) {
        return;
      }

      /// テキストのhandle
      final MemHandle setHandle = result.setHandle;

      bool isSelected = false;
      if (forceUpdate) {
        EmbCharLibrary()
            .apiBinding
            .setStatusCharSelection(setHandle, oldCharData.isSelected);
        isSelected = oldCharData.isSelected;
      } else {
        isSelected = _cursorPosition.cursorType == CursorType.selected;
      }

      /// テキスト・データ構造
      final LineCharacterData lineCharacterData = LineCharacterData(
        char: oldCharData.char,
        handle: setHandle,
        isSelected: isSelected,
      );

      charDataList.add(lineCharacterData);
    }
    _lineDataList[_cursorPosition.lineIndex].charList.clear();
    _lineDataList[_cursorPosition.lineIndex].charList.addAll(charDataList);
  }

  ///
  /// 文字を入力
  ///
  EmbCharLibraryError inputCharacter(String char) {
    if (_addTemporaryLine) {
      _lineDataList
          .removeWhere((element) => element.groupHandle == fakeLineHandle);
      _inputRealNewLine();
    }
    CursorType oldCursorType = _cursorPosition.cursorType;

    /// 文字を入力する
    final (errorCode: error, handle: handle) =
        EmbCharLibrary().apiBinding.pf1FontRead(
              int.parse(fontNumber),
              displayFontSize,
              char.runes.first,
              false,
            );

    if (error != EmbCharLibraryError.EMB_NO_ERR &&
        error != EmbCharLibraryError.EMB_CHAR_ROTATE90_ERR) {
      if (_lineDataList.isEmpty) {
        return error;
      }

      if (_lineDataList.last.charList.isEmpty) {
        _lineDataList.removeLast();
        _cursorPosition.lineIndex--;
        _cursorPosition.charIndex = _lineDataList.last.charList.length - 1;
      } else {
        /// do nothing
      }
      return error;
    }

    /// テキスト・データ構造
    final LineCharacterData lineCharacterData = LineCharacterData(
      char: char,
      handle: handle,
    );

    if (_lineDataList.isEmpty) {
      /// 1 行目の最初のテキスト
      final MemHandle topLineHandle =
          EmbCharLibrary().apiBinding.getCharEditTopHandle().handle;

      final LineData lineData = LineData(groupHandle: topLineHandle);

      lineData.charList = [lineCharacterData];
      _lineDataList.add(lineData);
    } else {
      if (oldCursorType == CursorType.unselectLeft ||
          _lineDataList[_cursorPosition.lineIndex].charList.isEmpty) {
        /// データを追加する
        _lineDataList[_cursorPosition.lineIndex]
            .charList
            .insert(_cursorPosition.charIndex, lineCharacterData);
      } else {
        /// テキストを追加し、カーソルを右に移動
        _cursorPosition.charIndex++;

        /// データを追加する
        _lineDataList[_cursorPosition.lineIndex]
            .charList
            .insert(_cursorPosition.charIndex, lineCharacterData);
      }
    }

    /// データを更新する
    _updateCurrentLineData(false);

    if (_selectMode == SelectModeEnum.multiLetter) {
      _selectAllCharInThisLine(
        selectLineIndex: _cursorPosition.lineIndex,
        exceptCharIndex: _cursorPosition.charIndex,
      );
      setCharSelectionStatus(getCurrentCharData().handle, true);
    } else {
      setCharSelectionStatus(
        getCurrentCharData().handle,
        _cursorPosition.cursorType == CursorType.selected,
      );
    }

    return error;
  }

  ///
  /// キャリッジリターンを入力します(正式なデータを追加する)
  ///
  EmbCharLibraryError _inputRealNewLine() {
    if (_addTemporaryLine) {
      if (_cursorPosition.cursorType == CursorType.selected) {
        setCharSelectionStatus(getCursorPattern(), false);
      } else {
        /// do nothing
      }

      final EmbCharLibraryError error = EmbCharLibrary().apiBinding.newLine();
      _lineDataList
          .removeWhere((element) => element.groupHandle == fakeLineHandle);
      _updateAllData();
      _addTemporaryLine = false;
      _needUpdateAllPreviewData = true;
      return error;
    }
    return EmbCharLibraryError.EMB_NO_ERR;
  }

  ///
  /// キャリッジリターンを入力します
  ///
  EmbCharLibraryError inputNewLine() {
    if (_cursorPosition.lineIndex == _lineDataList.length - 1 &&
        _cursorPosition.charIndex == _lineDataList.last.charList.length - 1) {
      /// 偽のデータを追加する
      _addTemporaryLine = true;
      LineData fakeLine = LineData(groupHandle: fakeLineHandle);
      _lineDataList.add(fakeLine);
    } else {
      final EmbCharLibraryError error = EmbCharLibrary().apiBinding.newLine();
      if (error != EmbCharLibraryError.EMB_NO_ERR) {
        return error;
      }
      _needUpdateAllPreviewData = true;

      /// データを更新する
      _updateAllData();
    }

    _cursorPosition.lineIndex++;
    _cursorPosition.charIndex = 0;

    return EmbCharLibraryError.EMB_NO_ERR;
  }

  ///
  /// 文字の削除
  ///
  EmbCharLibraryError deleteCharacter() {
    if (_lineDataList.isEmpty) {
      return EmbCharLibraryError.EMB_NO_ERR;
    }

    CursorType oldType = _cursorPosition.cursorType;
    EmbCharLibraryError error;
    if (_addTemporaryLine == false) {
      error = EmbCharLibrary().apiBinding.delChar();
      if (error != EmbCharLibraryError.EMB_NO_ERR) {
        return error;
      }
    } else {
      /// do nothing
    }

    /// 対応する場所のデータを削除します
    if (getCurrentLineData().charList.isNotEmpty) {
      if (oldType != CursorType.unselectLeft) {
        _lineDataList[_cursorPosition.lineIndex]
            .charList
            .removeAt(_cursorPosition.charIndex);
      } else {
        /// do nothing
      }
    } else {
      /// do nothing
    }

    /// カーソル位置の移動
    _cursorPosition.charIndex--;
    if (_cursorPosition.lineIndex > 0 && _cursorPosition.charIndex < 0) {
      /// 1 行のすべての文字が削除されます
      if (getCurrentLineData().charList.isEmpty) {
        _cursorPosition.lineIndex--;
        _cursorPosition.charIndex =
            _lineDataList[_cursorPosition.lineIndex].charList.length - 1;
        if (_addTemporaryLine == true) {
          _lineDataList
              .removeWhere((element) => element.groupHandle == fakeLineHandle);
          _addTemporaryLine = false;
        }
      }

      /// 改行の削除
      else if (oldType == CursorType.unselectLeft) {
        _cursorPosition.lineIndex--;
        _cursorPosition.charIndex =
            _lineDataList[_cursorPosition.lineIndex].charList.length - 1;
      }

      /// 最初の文字が削除されます
      else if (oldType == CursorType.selected) {
        _cursorPosition.charIndex = 0;
      }
    } else {
      /// do nothing
    }

    /// データ削除後のデフォルト値の復元
    if (_cursorPosition.lineIndex == 0 && _cursorPosition.charIndex == -1) {
      _cursorPosition.charIndex = 0;
    } else {
      /// do nothing
    }

    /// すべてのデータを更新する
    _updateAllData();

    if (getCurrentLineData().charList.isNotEmpty) {
      if (_selectMode == SelectModeEnum.multiLetter) {
        _selectAllCharInThisLine(
          selectLineIndex: _cursorPosition.lineIndex,
          exceptCharIndex: _cursorPosition.charIndex,
        );
        setCharSelectionStatus(getCurrentCharData().handle, true);
      } else {
        setCharSelectionStatus(
          getCurrentCharData().handle,
          _cursorPosition.cursorType == CursorType.selected,
        );
      }
    } else {
      /// do nothing
    }
    return EmbCharLibraryError.EMB_NO_ERR;
  }

  ///
  /// 行末ですか
  ///
  bool isEndOfLine() {
    /// カーソルがある行が空であるため、行の末尾を判断しない
    if (_lineDataList.isEmpty) {
      return false;
    }

    /// 行の先頭にあるカーソルを排除する場合
    return (_cursorPosition.charIndex ==
            getCurrentLineData().charList.length - 1) &&
        (_cursorPosition.cursorType != CursorType.unselectLeft);
  }

  ///
  /// 直線arrayではない行
  ///
  bool isOnNotStraightLine() {
    if (_lineDataList.isEmpty) {
      return false;
    }

    return _lineDataList[_cursorPosition.lineIndex].arrayType !=
        ArrayTypes.flat;
  }

  ///
  /// 行頭ではないのか
  ///
  bool isStartOfLine() {
    if (_lineDataList.isEmpty) {
      return false;
    }

    return _cursorPosition.charIndex == 0 &&
        _cursorPosition.cursorType == CursorType.unselectLeft;
  }

  ///
  /// 前の行は配列ではありません
  ///
  bool isPrevLineNotStraight() {
    if (_lineDataList.isEmpty) {
      return false;
    }

    if (_cursorPosition.lineIndex > 0 &&
        _cursorPosition.lineIndex < _lineDataList.length) {
      return _lineDataList[_cursorPosition.lineIndex - 1].arrayType !=
          ArrayTypes.flat;
    }

    return false;
  }

  ///
  /// すべての文字が空白です
  ///
  bool isAllCharBlank() {
    bool isBlank = true;
    for (LineData line in _lineDataList) {
      for (LineCharacterData item in line.charList) {
        isBlank = item.char == blank;
        if (isBlank == false) {
          break;
        }
      }
    }

    return isBlank;
  }

  ///
  /// 現在のカーソルの文字データを取得します
  ///
  LineCharacterData getCurrentCharData() =>
      getCurrentLineData().charList[_cursorPosition.charIndex];

  ///
  /// 現在のカーソルの行データを取得する
  ///
  LineData getCurrentLineData() {
    if (_cursorPosition.lineIndex >= _lineDataList.length) {
      return _lineDataList.last;
    } else {
      return _lineDataList[_cursorPosition.lineIndex];
    }
  }

  ///
  /// すべてのデータを取得する
  ///
  List<LineData> getAllLineData() => _lineDataList;

  ///
  /// 入力されたテキスト文字列を取得します
  ///
  List<String> getTextBuffer() {
    List<String> textBuffer = [];
    for (LineData line in _lineDataList) {
      textBuffer.addAll(line.charList.map((e) => e.char));
      if (_lineDataList.last != line) {
        textBuffer.add(cr);
      } else {
        /// do nothing
      }
    }

    return textBuffer;
  }

  ///
  /// 入力されたテキスト文字列を取得します
  ///
  List<List<String>> getAllText() {
    List<List<String>> lineList = [];
    for (LineData line in _lineDataList) {
      List<String> lineData = [];
      lineData.addAll(line.charList.map((e) => e.char).toList());
      if (_lineDataList.last != line) {
        lineData.add(cr);
      } else {
        /// do nothing
      }
      lineList.add(lineData);
    }

    return lineList;
  }

  ///
  /// カーソルの位置を取得する
  ///
  int getTextBufferIndex() {
    int index = defaultTextBufferCursorIndex;
    for (var i = 0; i < _lineDataList.length; i++) {
      LineData line = _lineDataList[i];

      /// 最初の行が最初の行でない場合は、前の行の末尾のキャリッジリターンが計算され、インデックス+1
      if (i != 0) index++;
      for (var j = 0; j < line.charList.length; j++) {
        /// 行にテキストがない場合は、1 を追加しないでください
        if (line.charList.isNotEmpty) index++;

        /// を指定された場所に移動し、インデックスを返します
        if (i == getLineIndex() && j == getCharIndex()) {
          return index;
        }
      }
    }

    return index;
  }

  ///
  /// カーソルが置かれている行の位置を取得します
  ///
  int getLineIndex() => _cursorPosition.lineIndex;

  ///
  /// カーソルが置かれている文字の位置を取得します
  ///
  int getCharIndex() => _cursorPosition.charIndex;

  ///
  /// カーソルの状態を取得する
  ///
  CursorType getCursorType() => _cursorPosition.cursorType;

  ///
  /// カーソルの状態を取得する
  ///
  MemHandle getCursorPattern() =>
      EmbCharLibrary().apiBinding.getCursorInfo().memHandle;

  ///
  /// カーソルが最後の位置にあるかどうか
  ///
  bool isFinalPosition() {
    if (_lineDataList.isEmpty) {
      return false;
    }

    /// 最終行にあるかどうか
    bool isFinalLine = _cursorPosition.lineIndex == _lineDataList.length - 1;

    /// 最終行の最後の文字の位置にあるかどうか
    bool isFinalChar = _cursorPosition.charIndex ==
            _lineDataList.last.charList.length - 1 ||
        (_cursorPosition.charIndex == 0 && _lineDataList.last.charList.isEmpty);

    /// カーソル・タイプが正しいかどうか
    bool isCorrectCursorType =
        _cursorPosition.cursorType == CursorType.unselectRight;

    return isFinalLine && isFinalChar && isCorrectCursorType;
  }

  ///
  /// カーソルが先頭にあるかどうか
  ///
  bool isBeginPosition() {
    if (_lineDataList.isEmpty) {
      return false;
    }

    /// 1行目にあるかどうか
    bool isFirstLine = _cursorPosition.lineIndex == 0;

    /// 最終行の最後の文字の位置にあるかどうか
    bool isFirstChar = _cursorPosition.charIndex == 0;

    /// カーソル・タイプが正しいかどうか
    bool isCorrectCursorType =
        _cursorPosition.cursorType == CursorType.unselectLeft;

    return isFirstLine && isFirstChar && isCorrectCursorType;
  }

  ///
  /// テキスト画像のサイズを取得する
  ///  (int width, int hight)
  ///
  (int width, int hight) getCharacterPatternSize() {
    final result = EmbCharLibrary().apiBinding.getStringEmbSize();
    if (result.size.Y != _characterPatternSize.$2) {
      _needUpdateAllPreviewData = true;
    }
    _characterPatternSize = (result.size.X, result.size.Y);
    return _characterPatternSize;
  }

  ///
  /// 文字選択モード設定
  ///
  void setSelectMode(SelectModeEnum mode) {
    final result = EmbCharLibrary().apiBinding.setCharSelectionMode(mode.index);
    if (result != EmbCharLibraryError.EMB_NO_ERR) {
      while (true) {
        Log.errorTrace("setCharSelectionMode return error:$result");
        sleep(Durations.extralong4);
      }
    } else {
      /// do nothing
    }
    _selectModeCache = null;

    if (_lineDataList.isEmpty) {
      return;
    }

    /// 単選モードでは、一連の文字をすべて選択することはできず、一つの文字のみを選択することができます。
    if (mode == SelectModeEnum.multiLetter) {
      _selectAllCharInThisLine(
        selectLineIndex: _cursorPosition.lineIndex,
        exceptCharIndex: _cursorPosition.charIndex,
      );
      setCharSelectionStatus(getCurrentCharData().handle, true);
    } else {
      final handle = _lineDataList[_cursorPosition.lineIndex]
          .charList[_cursorPosition.charIndex]
          .handle;
      final isSelect = _cursorPosition.cursorType == CursorType.selected;
      setCharSelectionStatus(
        handle,
        isSelect,
      );
    }
  }

  ///
  /// 文字選択モードを取得する
  ///
  SelectModeEnum getSelectMode() {
    return _selectMode;
  }

  ///
  /// 選択した状態のリスト
  ///
  void setCharSelectionStatus(MemHandle patternHandle, bool isSelected) {
    for (LineData line in _lineDataList) {
      for (LineCharacterData item in line.charList) {
        if (item.isSelected == true &&
            _selectMode == SelectModeEnum.oneLetter) {
          item.isSelected = false;
          EmbCharLibrary()
              .apiBinding
              .setStatusCharSelection(item.handle, false);
        } else {
          /// do nothing
        }
      }
    }

    for (LineData line in _lineDataList) {
      for (LineCharacterData item in line.charList) {
        if (item.handle == patternHandle) {
          item.isSelected = isSelected;
          EmbCharLibrary()
              .apiBinding
              .setStatusCharSelection(item.handle, isSelected);
        } else {
          /// do nothing
        }
      }
    }
  }

  ///
  /// 文字の選択・非選択状態取得
  ///
  bool getCharSelectionStatus() {
    MemHandle patternHandle =
        _lineDataList[getLineIndex()].charList[getCharIndex()].handle;

    final result =
        EmbCharLibrary().apiBinding.getStatusCharSelection(patternHandle);

    if (result.errorCode != EmbCharLibraryError.EMB_NO_ERR) {
      return false;
    }
    return result.selection;
  }

  ///
  /// 選択した行の選択ステータスを更新します。
  ///
  /// selectLineIndex:全選択にする必要がある文字行
  /// exceptCharIndex:全選択された行の中で選択状態に設定したくない文字
  ///
  void _selectAllCharInThisLine({
    required int selectLineIndex,
    int? exceptCharIndex,
  }) {
    /// 単選モードでは、一連の文字をすべて選択することはできず、一つの文字のみを選択することができます。
    if (_selectMode == SelectModeEnum.oneLetter) {
      return;
    }

    /// 選択された行以外のすべての行を非選択状態に設定します。
    for (var line in _lineDataList.asMap().entries) {
      final bool isSelectedLine = line.key == selectLineIndex;
      if (isSelectedLine == false) {
        for (var item in line.value.charList.asMap().entries) {
          _lineDataList[line.key].charList[item.key].isSelected = false;
          EmbCharLibrary()
              .apiBinding
              .setStatusCharSelection(item.value.handle, false);
        }
      }
    }

    /// 現在選択されている行の文字を選択し、特定の文字を除きます。
    for (var line in _lineDataList.asMap().entries) {
      final bool isSelectedLine = line.key == selectLineIndex;
      if (isSelectedLine == true) {
        for (var item in line.value.charList.asMap().entries) {
          if (item.key == exceptCharIndex) {
            continue;
          }
          _lineDataList[line.key].charList[item.key].isSelected = true;
          EmbCharLibrary()
              .apiBinding
              .setStatusCharSelection(item.value.handle, true);
        }
        break;
      }
    }
  }

  ///
  /// カーソル位置が左にシフトします
  ///
  bool moveCursorLeft() {
    CursorType cursorType = _cursorPosition.cursorType;
    if (_addTemporaryLine == false) {
      /// 移動後、全文の冒頭です (unselectLeft ← selected)
      if (getLineIndex() == 0 &&
          getCharIndex() == 0 &&
          cursorType == CursorType.selected) {
        /// 文字選択状態を設定します
        if (_selectMode == SelectModeEnum.oneLetter) {
          MemHandle patternHandle = getCurrentCharData().handle;
          setCharSelectionStatus(patternHandle, false);
        } else {
          /// do nothing
        }

        /// カーソルを移動する
        if (_moveCursor(_moveCurLeft) == false) {
          return false;
        }
      }

      /// 移動後が行頭 (unselectLeft ← selected)
      else if (getCharIndex() == 0 && cursorType == CursorType.selected) {
        /// 文字選択状態を設定します
        if (_selectMode == SelectModeEnum.oneLetter) {
          MemHandle patternHandle = getCurrentCharData().handle;
          setCharSelectionStatus(patternHandle, false);
        } else {
          /// do nothing
        }

        /// カーソルを移動する
        if (_moveCursor(_moveCurLeft) == false) {
          return false;
        }
        return true;
      }

      /// 現在は行の先頭にあり、移動後は行末です (unselectRight ← unselectLeft)
      else if (getCharIndex() == 0 && cursorType == CursorType.unselectLeft) {
        /// カーソルを移動する
        if (_moveCursor(_moveCurLeft) == false) {
          return false;
        }

        /// 文字選択状態を設定します
        if (_selectMode == SelectModeEnum.multiLetter) {
          _selectAllCharInThisLine(selectLineIndex: getLineIndex() - 1);
        } else {
          /// do nothing
        }
        _needUpdateAllPreviewData = true;
      }

      /// 現在、行末にあり、移動後に行の最後の文字が選択されます (selected ← unselectRight)
      else if (getCharIndex() ==
              _lineDataList[getLineIndex()].charList.length - 1 &&
          cursorType == CursorType.unselectRight) {
        /// 文字選択状態を設定します
        if (_selectMode == SelectModeEnum.oneLetter) {
          MemHandle patternHandle = getCurrentCharData().handle;
          setCharSelectionStatus(patternHandle, true);
        } else {
          /// do nothing
        }

        /// カーソルを移動する
        if (_moveCursor(_moveCurLeft) == false) {
          return false;
        }
        return true;
      }

      /// その他の状況  (selected ← selected)
      else {
        /// カーソルを移動する
        if (_moveCursor(_moveCurLeft) == false) {
          return false;
        }

        /// 文字選択状態を設定します
        if (_selectMode == SelectModeEnum.oneLetter) {
          MemHandle patternHandle = getCurrentLineData()
              .charList[_cursorPosition.charIndex - 1]
              .handle;
          setCharSelectionStatus(patternHandle, true);
        } else {
          /// do nothing
        }
      }
    } else {
      /// do nothing
    }

    /// 最後の空行を削除
    if (isBeginPosition() == false && _lineDataList.last.charList.isEmpty) {
      _lineDataList.removeLast();
    } else {
      /// do nothing
    }

    ///
    /// カーソル位置データが変更されました
    ///
    _cursorPosition.charIndex--;
    if (_cursorPosition.charIndex < 0) {
      _cursorPosition.charIndex = 0;
      if (_cursorPosition.lineIndex > 0) {
        _cursorPosition.lineIndex--;
        _cursorPosition.charIndex = getCurrentLineData().charList.length - 1;
      } else {
        /// do nothing
      }
    } else {
      /// do nothing
    }

    ///
    /// 偽のラインデータのタグをリセットする
    /// カーソルの位置(lineIndex&charIndex)が移動され、新しい位置でデータを設定する必要があるため、最後に配置されます。
    ///
    if (_addTemporaryLine == true) {
      _addTemporaryLine = false;

      /// 文字の選択状態をカーソルと同じに保ちます
      if (cursorType == CursorType.selected &&
          getCharSelectionStatus() != true) {
        setCharSelectionStatus(getCurrentCharData().handle, true);
      } else {
        /// do nothing
      }
    } else {
      ///do nothing
    }

    return true;
  }

  ///
  /// カーソル位置が右にシフトします
  ///
  bool moveCursorRight() {
    CursorType cursorType = _cursorPosition.cursorType;
    if (_addTemporaryLine == true) {
      /// カーソルを移動する
      return _moveCursor(_moveCurRight);
    } else {
      /// 全文の先頭は、最初の文字の選択状態に移動します   (unselectLeft → selected)
      if (isBeginPosition()) {
        /// カーソルを移動する
        if (_moveCursor(_moveCurRight) == false) {
          return false;
        }

        /// 文字選択状態を設定します
        if (_selectMode == SelectModeEnum.oneLetter) {
          MemHandle patternHandle = getCurrentCharData().handle;
          setCharSelectionStatus(patternHandle, true);
        } else {
          /// do nothing
        }
        return true;
      }

      /// 全文の末尾へ移動 (selected → unselectRight)
      else if (_cursorPosition.lineIndex == _lineDataList.length - 1 &&
          _cursorPosition.charIndex == _lineDataList.last.charList.length - 1 &&
          cursorType == CursorType.selected) {
        /// カーソルを移動する
        if (_moveCursor(_moveCurRight) == false) {
          return false;
        }

        /// 文字選択状態を設定します
        if (_selectMode == SelectModeEnum.oneLetter) {
          MemHandle patternHandle = getCurrentCharData().handle;
          setCharSelectionStatus(patternHandle, false);
        } else {
          /// do nothing
        }
        return true;
      }

      /// 行末文字の選択状態は、行末文字の選択されていない状態に移行します (selected → unselectRight)
      else if (cursorType == CursorType.selected &&
          getCurrentLineData().charList.last.handle ==
              getCurrentCharData().handle) {
        /// カーソルを移動する
        if (_moveCursor(_moveCurRight) == false) {
          return false;
        }

        /// 文字選択状態を設定します
        if (_selectMode == SelectModeEnum.oneLetter) {
          MemHandle patternHandle = getCurrentCharData().handle;
          setCharSelectionStatus(patternHandle, false);
        } else {
          /// do nothing
        }
        return true;
      }

      /// 行末の非選択状態は、次の行の先頭で選択されていない状態に移行します (unselectRight → unselectLeft)
      else if (cursorType == CursorType.unselectRight &&
          getCurrentLineData().charList.last.handle ==
              getCurrentCharData().handle) {
        /// 文字選択状態を設定します
        if (_selectMode == SelectModeEnum.multiLetter) {
          _selectAllCharInThisLine(selectLineIndex: getLineIndex() + 1);
        } else {
          /// do nothing
        }

        /// カーソルを移動する
        if (_moveCursor(_moveCurRight) == false) {
          return false;
        }
        _needUpdateAllPreviewData = true;
      }

      /// 行頭の未選択状態は、行頭の選択状態に移行します (unselectLeft → selected)
      else if (cursorType == CursorType.unselectLeft &&
          getCurrentLineData().charList.first.handle ==
              getCurrentCharData().handle) {
        /// カーソルを移動する
        if (_moveCursor(_moveCurRight) == false) {
          return false;
        }

        /// 文字選択状態を設定します
        if (_selectMode == SelectModeEnum.oneLetter) {
          MemHandle patternHandle = getCurrentCharData().handle;
          setCharSelectionStatus(patternHandle, true);
        } else {
          /// do nothing
        }
        return true;
      }

      /// その他の状況 (selected → selected)
      else {
        /// カーソルを移動する
        if (_moveCursor(_moveCurRight) == false) {
          return false;
        }
        if (_selectMode == SelectModeEnum.oneLetter) {
          /// 文字選択状態を設定します
          MemHandle patternHandle = getCurrentLineData()
              .charList[_cursorPosition.charIndex + 1]
              .handle;
          setCharSelectionStatus(patternHandle, true);
        } else {
          /// do nothing
        }
      }
    }

    _cursorPosition.charIndex++;

    if (_cursorPosition.lineIndex < _lineDataList.length - 1) {
      int length = getCurrentLineData().charList.length;
      if (_cursorPosition.charIndex >= length) {
        _cursorPosition.lineIndex++;
        _cursorPosition.charIndex = 0;
      } else {
        /// do Nothing
      }
    } else {
      /// do Nothing
    }

    return true;
  }

  ///
  /// カーソルが特定の位置に移動します
  ///
  void moveCursorToPosition(int targetLine, int targetChar) {
    if (_addTemporaryLine) {
      if (_lineDataList.last.charList.isEmpty) {
        _addTemporaryLine = false;
        _lineDataList.removeLast();
        _cursorPosition.lineIndex--;
        _cursorPosition.charIndex = _lineDataList.last.charList.length - 1;
      } else {
        /// do nothing
      }
    } else {
      /// do nothing
    }

    /// カーソルを左に移動
    if (targetLine < _cursorPosition.lineIndex ||
        (targetLine == _cursorPosition.lineIndex &&
            targetChar < _cursorPosition.charIndex)) {
      if (targetLine == _cursorPosition.lineIndex &&
          _selectMode == SelectModeEnum.multiLetter) {
        /// 全選択モードの場合、カーソルの移動処理についてです。
        final handle = _lineDataList[targetLine].charList[targetChar].handle;
        final isSelected =
            _lineDataList[targetLine].charList[targetChar].isSelected;

        if (_cursorPosition.cursorType == CursorType.unselectLeft) {
          _moveCursor(_moveCurRight);
        } else if (_cursorPosition.cursorType == CursorType.unselectRight) {
          _moveCursor(_moveCurLeft);
        } else {
          /// do nothing
        }

        _cursorPosition.lineIndex = targetLine;
        _cursorPosition.charIndex = targetChar;
        EmbCharLibrary().apiBinding.setStatusCharSelection(handle, !isSelected);
        _lineDataList[targetLine].charList[targetChar].isSelected = !isSelected;
      } else {
        while (true) {
          bool result = moveCursorLeft();
          if (result == false) {
            break;
          }
          if (targetLine == _cursorPosition.lineIndex &&
              targetChar == _cursorPosition.charIndex &&
              _cursorPosition.cursorType == CursorType.selected) {
            break;
          }
        }
      }
    }

    /// カーソルを右に移動
    else if (targetLine > _cursorPosition.lineIndex ||
        (targetLine == _cursorPosition.lineIndex &&
            targetChar > _cursorPosition.charIndex)) {
      if (targetLine == _cursorPosition.lineIndex &&
          _selectMode == SelectModeEnum.multiLetter) {
        /// 全選択モードの場合、カーソルの移動処理についてです。
        final handle = _lineDataList[targetLine].charList[targetChar].handle;
        final isSelected =
            _lineDataList[targetLine].charList[targetChar].isSelected;

        if (_cursorPosition.cursorType == CursorType.unselectLeft) {
          _moveCursor(_moveCurRight);
        } else if (_cursorPosition.cursorType == CursorType.unselectRight) {
          _moveCursor(_moveCurLeft);
        } else {
          /// do nothing
        }

        _cursorPosition.lineIndex = targetLine;
        _cursorPosition.charIndex = targetChar;
        EmbCharLibrary().apiBinding.setStatusCharSelection(handle, !isSelected);
        _lineDataList[targetLine].charList[targetChar].isSelected = !isSelected;
      } else {
        while (true) {
          bool result = moveCursorRight();
          if (result == false) {
            break;
          }
          if (targetLine == _cursorPosition.lineIndex &&
              targetChar == _cursorPosition.charIndex &&
              _cursorPosition.cursorType == CursorType.selected) {
            break;
          }
        }
      }
    }

    /// 現在のテキストをクリックします
    else if (targetLine == _cursorPosition.lineIndex &&
        targetChar == _cursorPosition.charIndex) {
      if (_cursorPosition.cursorType == CursorType.unselectLeft) {
        moveCursorRight();
      } else if (_cursorPosition.cursorType == CursorType.unselectRight) {
        moveCursorLeft();
      }
      if (_selectMode == SelectModeEnum.multiLetter) {
        final handle = _lineDataList[targetLine].charList[targetChar].handle;
        final isSelected =
            _lineDataList[targetLine].charList[targetChar].isSelected;

        EmbCharLibrary().apiBinding.setStatusCharSelection(handle, !isSelected);
        _lineDataList[targetLine].charList[targetChar].isSelected = !isSelected;
      } else {
        /// do nothing
      }
    } else {
      /// do nothing
    }
  }

  ///
  /// カーソルを移動するための API
  ///
  bool _moveCursor(int moveCurDir) {
    var result = EmbCharLibrary().apiBinding.moveCursor(moveCurDir);

    if (result.errorCode != EmbCharLibraryError.EMB_NO_ERR) {
      return false;
    }

    return true;
  }

  ///
  /// 選択した文字サイズを取得
  ///
  int getSelectedCharSize() {
    if (_cursorPosition.cursorType != CursorType.selected ||
        getCurrentLineData().charList.isEmpty) {
      return displayFontSize;
    } else {
      EmbGroup embGroup = getCurrentLineData().embGroup;
      RectanArea charSize = embGroup.embGroupInfo.embPatternInfo
          .embPatterns[_cursorPosition.charIndex].size;
      return charSize.bottom - charSize.top;
    }
  }

  ///
  /// フォントを変更する
  ///
  EmbCharLibraryError changeFont(
    String fontName,
    String fontNumber,
    String selectedFontType,
  ) {
    final (errorCode: error, handle: _) = EmbCharLibrary()
        .apiBinding
        .cngSelectedPatternFontNumber(int.parse(fontNumber));

    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      return error;
    }
    this
      ..fontName = fontName
      ..fontNumber = fontNumber
      ..selectedFontType = selectedFontType;

    _updateAllData();

    /// 再編集のgroupのhandleが変化し、既にログインしたpatternデータの更新
    if (_isReEdit) {
      PatternModel().reloadAllPattern();
    } else {
      /// do nothing
    }
    return error;
  }

  ///
  /// 文字列整列
  ///
  EmbCharLibraryError alignString(StringAlignType align) {
    final EmbCharLibraryError error =
        EmbCharLibrary().apiBinding.alignString(align);
    _needUpdateAllPreviewData = true;
    return error;
  }

  ///
  /// 文字列の水平配置
  ///
  EmbCharLibraryError setStringFlat() {
    final group = getCurrentLineData().groupHandle;

    final EmbCharLibraryError error =
        EmbCharLibrary().apiBinding.setStringFlat(group);
    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      return error;
    }
    _needUpdateAllPreviewData = true;
    return error;
  }

  ///
  /// 文字列の円弧状配置
  ///
  EmbCharLibraryError setStringArc(StringArcType type) {
    final group = getCurrentLineData().groupHandle;

    final EmbCharLibraryError error =
        EmbCharLibrary().apiBinding.setStringArc(group, type);
    _needUpdateAllPreviewData = true;
    return error;
  }

  ///
  /// 文字列の斜め配置
  ///
  EmbCharLibraryError setStringSlant() {
    final group = getCurrentLineData().groupHandle;

    final EmbCharLibraryError error =
        EmbCharLibrary().apiBinding.setStringSlant(group);
    _needUpdateAllPreviewData = true;
    return error;
  }

  ///
  /// 円弧開度変更
  ///
  EmbCharLibraryError changeArcOpenningDegree(
      ArcOpenningDegreeChangeType openingDegree) {
    final group = getCurrentLineData().groupHandle;
    final EmbCharLibraryError error = EmbCharLibrary()
        .apiBinding
        .changeArcOpenningDegree(group, openingDegree);
    _needUpdateAllPreviewData = true;
    return error;
  }

  ///
  /// 斜度変更
  ///
  EmbCharLibraryError changeStringSlant(StringSlantChangeType slant) {
    final group = getCurrentLineData().groupHandle;
    final EmbCharLibraryError error =
        EmbCharLibrary().apiBinding.changeStringSlant(group, slant);
    _needUpdateAllPreviewData = true;
    return error;
  }

  ///
  /// 現在の行の配列型を取得する
  ///
  ArrayTypes getCurrentLineArrayType() =>
      _lineDataList[_cursorPosition.lineIndex].arrayType;

  ///
  /// 選択したすべての文字のサイズを変更する
  ///
  EmbCharLibraryError _changeAllSelectedCharSize(MagType type, int size) {
    final EmbCharLibraryError error =
        EmbCharLibrary().apiBinding.changeSizeSelectedChar(type, size);

    return error;
  }

  ///
  /// フォントサイズを小さくする
  ///
  EmbCharLibraryError reduceCharSize() =>
      _changeAllSelectedCharSize(MagType.xyAll, -_sizeChangeStep);

  ///
  /// フォントサイズを拡大する
  ///
  EmbCharLibraryError enlargeCharSize() =>
      _changeAllSelectedCharSize(MagType.xyAll, _sizeChangeStep);

  ///
  /// 文字サイズの高い拡大
  ///
  EmbCharLibraryError enlargeCharHight() =>
      _changeAllSelectedCharSize(MagType.yOnly, _sizeChangeStep);

  ///
  /// 文字のサイズの高さが小さくなります
  ///
  EmbCharLibraryError reduceCharHight() =>
      _changeAllSelectedCharSize(MagType.yOnly, -_sizeChangeStep);

  ///
  /// 文字のサイズの高さが小さくなります
  ///
  EmbCharLibraryError enlargeCharWidth() =>
      _changeAllSelectedCharSize(MagType.xOnly, _sizeChangeStep);

  ///
  /// 文字のサイズの高さが小さくなります
  ///
  EmbCharLibraryError reduceCharWidth() =>
      _changeAllSelectedCharSize(MagType.xOnly, -_sizeChangeStep);

  ///
  /// 文字のサイズをリセットします
  ///
  EmbCharLibraryError resetCharSize() {
    final EmbCharLibraryError error =
        EmbCharLibrary().apiBinding.changeSizeSelectedChar(MagType.xyAll, 0);

    return error;
  }

  ///
  /// 文字間隔変更
  ///
  EmbCharLibraryError changeCharSpace(CharSpaceChangeType charSpace) {
    final group = getCurrentLineData().groupHandle;
    final EmbCharLibraryError error =
        EmbCharLibrary().apiBinding.changeCharSpace(group, charSpace);

    return error;
  }

  ///
  /// 文字上下位置変更
  ///
  EmbCharLibraryError changeVerticalPosition(
      VerticlalPositionChangeType charVposition) {
    final group = getCurrentLineData().groupHandle;
    final EmbCharLibraryError error = EmbCharLibrary()
        .apiBinding
        .changeVerticlalPosition(group, charVposition);

    return error;
  }

  ///
  /// 文字間隔リセット
  ///
  EmbCharLibraryError resetCharSpace() {
    final group = getCurrentLineData().groupHandle;
    final EmbCharLibraryError error =
        EmbCharLibrary().apiBinding.resetCharSpace(group);

    return error;
  }

  ///
  /// 文字データは、索引によって取得されます
  ///
  LineCharacterData getCharDataByIndex(int lineIndex, int charIndex) =>
      _lineDataList[lineIndex].charList[charIndex];

  ///
  /// 「groupHandle」を全て入手
  ///
  List<MemHandle> getAllHandleList() {
    List<MemHandle> list = [];
    for (var line in _lineDataList) {
      list.add(line.groupHandle);
    }
    return list;
  }

  ///
  /// 文字の「EmbGroup」を全て入手
  ///
  List<EmbGroup> getAllGroup() {
    List<EmbGroup> list = [];
    for (var line in _lineDataList) {
      if (line.groupHandle != fakeLineHandle) {
        list.add(line.embGroup);
      } else {
        /// do nothing
      }
    }
    return list;
  }

  ///
  /// 再編集したデータを取得する
  ///
  void getReEditData() {
    /// 編集時の文字行のHandle
    final EmbGroup charGroup = PatternModel().getCurrentGroup();

    /// フォントデータを取得します
    final int lastPatternFontNumber =
        charGroup.embGroupInfo.embPatternInfo.embPatterns.last.fontNumber;
    final CharacterFontImageGroup fontImageGroup = CharacterFontImageReader()
        .getCharacterFontImagesInfoByFontNumber(lastPatternFontNumber);

    /// 文字フォント名を初期化します

    fontName = fontImageGroup.name;
    fontNumber = fontImageGroup.fontNumber;
    selectedFontType = fontImageGroup.type;
    displayFontSize = fontImageGroup.type == emcTypeSmall
        ? defaultFontSizeSmall
        : defaultFontSizeAlp;

    final MemHandle groupHandle = charGroup.handle;
    final List<String> charList = charGroup.charList;

    final LineData line = LineData(groupHandle: groupHandle);

    /// 文字行の文字数
    final int patternNum =
        EmbCharLibrary().apiBinding.getPatternNum(groupHandle).patternNum;

    _cursorPosition.charIndex = patternNum - 1;

    for (var charIndex = 0; charIndex < patternNum; charIndex++) {
      /// テキストのhandle
      final MemHandle setHandle = EmbCharLibrary()
          .apiBinding
          .getPatternHandle(groupHandle, charIndex)
          .setHandle;

      LineCharacterData data = LineCharacterData(
        char: charList[charIndex],
        handle: setHandle,
      );

      line.charList.add(data);
    }
    _lineDataList.add(line);
  }

  ///
  /// 文字間隔とビートクラスをリセットする
  ///
  void curGroupCharArrayBack() =>
      EmbLibrary().apiBinding.curGroupCharArrayBack();

  ///
  /// modelのreset関数
  ///
  void reset() {
    _cursorPosition
      ..charIndex = 0
      ..lineIndex = 0;
    _isReEdit = false;
    bottomPopupOpenState = BottomPopupOpenState.none;
    _lineDataList.clear();
    _needUpdateAllPreviewData = false;
    _selectModeCache = null;
  }
}
