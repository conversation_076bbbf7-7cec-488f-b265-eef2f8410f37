import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../../model/key_board_font_model.dart';
import '../../../../../model/pattern_model.dart';
import '../../../../../model/redo_undo_model.dart';
import '../../../../page_route.dart';
import '../text_editing_page_view_model.dart';
import 'bottom_bar_interface.dart';
import 'component/font_array/font_array_popup.dart';
import 'component/font_size/font_size_popup.dart';
import 'component/font_spacing/font_spacing_popup.dart';

final bottomBarViewModelProvider =
    StateNotifierProvider.autoDispose<BottomBarStateInterface, BottomBarState>(
  (ref) => BottomBarViewModel(ref),
);

class BottomBarViewModel extends BottomBarStateInterface {
  BottomBarViewModel(Ref ref) : super(const BottomBarState(), ref) {
    update();
  }

  @override
  void update() {
    state = state.copyWith(
      isEnglish: DeviceLibrary().apiBinding.getLanguage().value ==
          Language.LANG_ENGLISH,
      isArrayAvailable: _getArrayAvailable(),
      isSpacingAvailable: _getSpacingAvailable(),
      isAlignAvailable: _getAlignAvailable(),
      isReedit: KeyBoardFontModel().isReEditMode == true,
      isNextButtonEnable: _getNextButtonStatus(),
    );
  }

  ///
  /// Returnのクリック関数
  ///
  @override
  void onReturnButtonClicked(BuildContext context) {
    if (KeyBoardFontModel().isReEditMode == false) {
      /// 文字入力中止
      KeyBoardFontModel()
        ..cancelEmbCharEdit()
        ..reset();
      PatternModel().reloadAllPattern();
      PagesRoute().pop();
    } else {
      /// 文字再編集中止
      KeyBoardFontModel()
        ..cancelEmbCharEdit()
        ..reset();
      RedoUndoModel().undoRedo();
      PatternModel().reloadAllPattern();
      PagesRoute().pop();
    }
  }

  ///
  /// Arrayのクリック関数
  ///
  @override
  void onArrayButtonClicked(BuildContext context) {
    if (state.isArrayAvailable == false) {
      return;
    }
    KeyBoardFontModel().bottomPopupOpenState = BottomPopupOpenState.array;
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => const FontArrayPopup(),
        barrier: false,
      ),
    ).then((value) {
      KeyBoardFontModel().bottomPopupOpenState = BottomPopupOpenState.none;
    });
  }

  ///
  /// Spacingのクリック関数
  ///
  @override
  void onSpacingButtonClicked(BuildContext context) {
    if (state.isSpacingAvailable == false) {
      return;
    }

    /// Model更新
    KeyBoardFontModel().bottomPopupOpenState = BottomPopupOpenState.spacing;

    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => const FontSpacingPopup(),
        barrier: false,
      ),
    ).then((value) {
      KeyBoardFontModel().bottomPopupOpenState = BottomPopupOpenState.none;

      /// 他の画面を更新する
      ref
          .read(textEditingPageViewModelProvider.notifier)
          .updateTextEditingPageByChild(TextEditingType.bottomBar);
    });

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.bottomBar);
  }

  @override
  void onSizeButtonClicked(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => const FontSizePopup(),
        barrier: false,
      ),
    );
  }

  ///
  /// アリニメントの調整
  ///
  @override
  void onAlignmentButtonClicked() {
    if (state.isAlignAvailable == false) {
      return;
    }

    if (state.alignmentType == AlignmentType.right) {
      final EmbCharLibraryError error =
          KeyBoardFontModel().alignString(StringAlignType.alignStrLeft);
      if (error == EmbCharLibraryError.EMB_NO_ERR) {
        state = state.copyWith(alignmentType: AlignmentType.left);
      } else {
        /// do nothing
      }
    } else if (state.alignmentType == AlignmentType.center) {
      final EmbCharLibraryError error =
          KeyBoardFontModel().alignString(StringAlignType.alignStrRight);
      if (error == EmbCharLibraryError.EMB_NO_ERR) {
        state = state.copyWith(alignmentType: AlignmentType.right);
      } else {
        /// do nothing
      }
    } else {
      final EmbCharLibraryError error =
          KeyBoardFontModel().alignString(StringAlignType.alignStrCenter);
      if (error == EmbCharLibraryError.EMB_NO_ERR) {
        state = state.copyWith(alignmentType: AlignmentType.center);
      } else {
        /// do nothing
      }
    }

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.bottomBar);
  }

  @override
  void onSetButtonClicked() {
    if (KeyBoardFontModel().isReEditMode == false) {
      PatternModel()
        ..selectedZoomScaleInEditPage = zoomList.first
        ..selectedZoomScaleInSelectPage = zoomList.first;
    } else {
      /// do nothing
    }

    /// 文字入力終了
    KeyBoardFontModel().finishEmbCharEdit();
    if (KeyBoardFontModel().isReEditMode == false) {
      /// Model更新
      PatternModel().reloadAllPattern();
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      PagesRoute().pushNamedAndRemoveUntil(
          nextRoute: PageRouteEnum.patternEdit, untilRoute: PageRouteEnum.home);
    } else {
      PatternModel().reloadAllPattern();
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
        return;
      }

      PagesRoute().pop();
    }
    KeyBoardFontModel().reset();
  }

  ///
  /// 配列ボタンは使用できません
  ///
  bool _getArrayAvailable() {
    final list = KeyBoardFontModel().getTextBuffer();
    if (list.isEmpty) {
      return false;
    }

    final line = KeyBoardFontModel().getCurrentLineData().charList;
    if (list.isNotEmpty && line.isNotEmpty) {
      return true;
    }
    return false;
  }

  ///
  /// 間隔ボタンは使用できません
  ///
  bool _getSpacingAvailable() {
    final list = KeyBoardFontModel().getTextBuffer();
    if (list.isEmpty) {
      return false;
    }
    final List<LineCharacterData> line =
        KeyBoardFontModel().getCurrentLineData().charList;
    if (list.length >= 2 && line.isNotEmpty) {
      return true;
    }

    return false;
  }

  ///
  /// 整列ボタンは使用できません
  ///
  bool _getAlignAvailable() {
    if (KeyBoardFontModel().isReEditMode == true) {
      return false;
    } else {
      return KeyBoardFontModel().getTextBuffer().isNotEmpty;
    }
  }

  ///
  /// nextボタンの状態
  ///
  bool _getNextButtonStatus() {
    /// すべての文字が空白です
    bool isAllCharBlank = KeyBoardFontModel().isAllCharBlank();

    final list = KeyBoardFontModel().getTextBuffer();
    if (list.isEmpty) {
      return false;
    }

    /// 入力文字なし
    bool isInputCharEmpty =
        KeyBoardFontModel().getCurrentLineData().charList.isEmpty;

    return isAllCharBlank || isInputCharEmpty ? false : true;
  }
}
