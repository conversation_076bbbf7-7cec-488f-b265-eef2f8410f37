import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'outline_setting_popup_view_interface.freezed.dart';

@freezed
class OutlineSettingPopupState with _$OutlineSettingPopupState {
  const factory OutlineSettingPopupState({
    @Default(false) bool isOnButtonSelected,
    @Default(false) bool isOFFButtonSelected,
  }) = _OutlineSettingPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class OutlineSettingStateViewModelInterface
    extends ViewModel<OutlineSettingPopupState> {
  OutlineSettingStateViewModelInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// ONボタンがクリックされました
  ///
  void onONButtonClicked();

  ///
  /// OFFボタンがクリックされました
  ///
  void onOFFButtonClicked();
}
