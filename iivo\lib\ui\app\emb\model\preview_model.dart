import 'dart:math';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:panel_library/panel_library.dart';

import 'edit_model.dart';
import 'key_board_font_model.dart';
import 'pattern_model.dart';

/// パンツール「赤いドット」の画像サイズ
const double redPointImageSize = 17;

/// パンツール「赤いドット」の画像間隔
const double redPointImageSpace = 10;

/// パンツール「赤いドット」は最大50dotの正方形エリア
const double redPointMaxTouchArea = 50;

/// 小数によるマスクの表示が異常になるのを防ぐために、ピクセル領域が追加されています。
const double maskOutArea = 1;

/// ドラッグが停止した際のデバウンス用のピクセル閾値。
/// ユーザーがドラッグを終了した後に発生する意図しない動きを防ぐために使用されます。
const int dragDebounceThreshold = 10;

class PreviewModel {
  PreviewModel._internal();
  factory PreviewModel() {
    if (_instance._isInitialized == false) {
      _instance._isInitialized = true;
    }

    return _instance;
  }

  ///
  /// 選択した画像を取得し、プレビューに表示します
  ///
  Image? quiltImage;

  bool _isInitialized = false;
  static final PreviewModel _instance = PreviewModel._internal();

  ///
  /// ドラッグプレビューモードかどうか
  ///
  bool isInDragPreviewMode = false;

  ///
  /// 現在の ui 側の回転角度
  ///
  double uiRotateAngle = 0;

  ///
  /// 正方向に回転するかどうか
  ///
  bool isRotatingForward = true;

  ///
  /// GroupPatternの表示情報の取得
  ///
  PatternDisplayInfo getGroupPatternDisplayInfo({
    required int scrollType,
    required EmbGroup group,
    required Offset centerPoint,
    required double pixelOfOneMm,
    required int sewingIndex,
    required MemHandle currentGroupHandle,
    required int zoomScale,
    MemHandle? cursorHandle,
    CursorType? cursorType,
  }) {
    EmbGroupDisplayInfo info = getEmbGroupDisplayInfo(
      centerPoint: centerPoint,
      pixelOfOneMm: pixelOfOneMm,
      sewingIndex: sewingIndex,
      group: group,
      scrollType: scrollType,
      cursorHandle: cursorHandle,
      cursorType: cursorType,
      currentGroupHandle: currentGroupHandle,
      zoomScale: zoomScale,
    );
    EmbBorderDisplayInfo borderInfo = EmbBorderDisplayInfo(
      sewingIndex: info.sewingIndex,
      isSelected: info.isSelected,
      isCurrentPattern: info.isCurrentPattern,
      isBaseBorder: true,
      left: info.left,
      top: info.top,
      width: info.width,
      height: info.height,
      maskDisplayInfo: info.mask,
      baseMaskInfo: info.mask,
      groupDisplayInfoList: [info.copyWith(top: 0, left: 0)],
    );

    return PatternDisplayInfo(
        top: borderInfo.top,
        left: borderInfo.left,
        width: borderInfo.width,
        height: borderInfo.height,
        isGroup: true,
        isSelected: info.isSelected,
        isCurrentPattern: info.isCurrentPattern,
        maskDisplayInfo: borderInfo.maskDisplayInfo,
        style: const SSPoint(X: 1, Y: 1),
        space: const SSPoint(X: 0, Y: 0),
        borderDisplayInfoList: [borderInfo.copyWith(top: 0, left: 0)]);
  }

  ///
  /// BorderPatternの表示情報の取得
  ///
  PatternDisplayInfo getBorderPatternDisplayInfo({
    required int scrollType,
    required EmbBorder border,
    required Offset centerPoint,
    required double pixelOfOneMm,
    required int sewingIndex,
    required MemHandle currentGroupHandle,
    required int zoomScale,
  }) {
    BorderInfo borderInfo = border.borderInfo;
    EmbBorderDisplayInfo baseBorderInfo = _getBaseBorderDisplayInfo(
      border: border,
      centerPoint: centerPoint,
      pixelOfOneMm: pixelOfOneMm,
      sewingIndex: sewingIndex,
      scrollType: scrollType,
      borderInfo: borderInfo,
      currentGroupHandle: currentGroupHandle,
      zoomScale: zoomScale,
    );
    return _getRepeatBorderPatternDisplayInfo(
      border: border,
      borderInfo: borderInfo,
      baseBorderInfo: baseBorderInfo,
      pixelOfOneMm: pixelOfOneMm,
    );
  }

  ///
  /// 現在の模様が回転した後の新しい模様を取得します。
  ///
  PatternDisplayInfo getNewPatternDisplayInfoInRotate({
    required double angle,
    required PatternDisplayInfo patternDisplayInfo,
    required double pixelOfOneMm,
    required Offset rotateCenter,
  }) {
    List<double> xList = [];
    List<double> yList = [];

    /// 先頭にあるBaseBorderの情報を取り出します。
    EmbBorderDisplayInfo baseBorderDisplayInfo =
        patternDisplayInfo.borderDisplayInfoList.first.copyWith();

    /// PatternとBorderの左上隅
    Offset patternTopLeft = patternDisplayInfo.topLeft;
    Offset borderTopLeft = baseBorderDisplayInfo.topLeft;

    /// 中心点に従って、すべてのembPatternの新しいMask位置が取得されます
    for (var groupDisplayInfo in baseBorderDisplayInfo.groupDisplayInfoList) {
      /// Groupの左上隅
      Offset groupTopLeft = groupDisplayInfo.topLeft;
      for (var embPatternDisplayInfo
          in groupDisplayInfo.embPatternDisplayInfoList) {
        /// ページ上の相対位置オフセットの回復します
        Offset topLeft = embPatternDisplayInfo.topLeft +
            groupTopLeft +
            borderTopLeft +
            patternTopLeft;

        /// 新しいマスク位置を計算します
        Mask mask = Mask(
          topLeft: rotatePoint(
              point: embPatternDisplayInfo.mask.topLeft + topLeft,
              angle: angle,
              center: rotateCenter),
          topRight: rotatePoint(
              point: embPatternDisplayInfo.mask.topRight + topLeft,
              angle: angle,
              center: rotateCenter),
          bottomLeft: rotatePoint(
              point: embPatternDisplayInfo.mask.bottomLeft + topLeft,
              angle: angle,
              center: rotateCenter),
          bottomRight: rotatePoint(
              point: embPatternDisplayInfo.mask.bottomRight + topLeft,
              angle: angle,
              center: rotateCenter),
        );

        xList.addAll([
          mask.topLeft.dx,
          mask.topRight.dx,
          mask.bottomLeft.dx,
          mask.bottomRight.dx,
        ]);
        yList.addAll([
          mask.topLeft.dy,
          mask.topRight.dy,
          mask.bottomLeft.dy,
          mask.bottomRight.dy,
        ]);
      }
    }

    /// 新しい Mask 位置に基づいて、回転した BaseBorder の Mask サイズを計算します
    final (
      topMin: topMin,
      leftMin: leftMin,
      bottomMax: bottomMax,
      rightMax: rightMax
    ) = getMaxOuterWithXYList(xList: xList, yList: yList);

    /// BaseBorderのマスクを更新します
    baseBorderDisplayInfo = baseBorderDisplayInfo.copyWith(
      top: patternDisplayInfo.top,
      left: patternDisplayInfo.left,
      maskDisplayInfo: Mask(
        topLeft: Offset(leftMin, topMin),
        topRight: Offset(rightMax, topMin),
        bottomLeft: Offset(leftMin, bottomMax),
        bottomRight: Offset(rightMax, bottomMax),
      ),
    );

    List<ThreadMarkState> threadMarkStates = [];
    for (var element in patternDisplayInfo.borderDisplayInfoList) {
      threadMarkStates.add(element.threadMarkState);
    }

    /// 新しいPatternをBaseBorderで入手します
    return _getPatternDisplayInfoInRotate(
      baseBorderInfo: baseBorderDisplayInfo,
      style: patternDisplayInfo.style,
      space: patternDisplayInfo.space,
      isGroup: patternDisplayInfo.isGroup,
      pixelOfOneMm: pixelOfOneMm,
      threadMarkStates: threadMarkStates,
    );
  }

  ///
  /// コピーするベース模様としてBorderの表示情報を取得します
  ///
  /// ベース模様:borderInfo.style.X == 1 && borderInfo.style.Y == 1 の模様
  ///
  EmbBorderDisplayInfo _getBaseBorderDisplayInfo({
    required int scrollType,
    required Offset centerPoint,
    required double pixelOfOneMm,
    required int sewingIndex,
    required EmbBorder border,
    required BorderInfo borderInfo,
    required MemHandle currentGroupHandle,
    required int zoomScale,
  }) {
    double? left;
    double? top;
    double? right;
    double? bottom;
    double? maskTop;
    double? maskLeft;
    double? maskRight;
    double? maskBottom;
    List<EmbGroupDisplayInfo> sourceGroupDisplayInfoList = [];
    bool isCurrentPattern = border.isContainCurrentPattern(currentGroupHandle);

    List<EmbGroup> groupList = [];
    if (borderInfo.style.X * borderInfo.style.Y > 1) {
      groupList.addAll(border.groupList.getRange(0, borderInfo.groupNum));
    } else {
      groupList.addAll(border.groupList);
    }

    for (var group in groupList) {
      EmbGroupDisplayInfo info = getEmbGroupDisplayInfo(
        centerPoint: centerPoint,
        pixelOfOneMm: pixelOfOneMm,
        sewingIndex: sewingIndex,
        group: group,
        scrollType: scrollType,
        currentGroupHandle: currentGroupHandle,
        zoomScale: zoomScale,
      );
      sourceGroupDisplayInfoList.add(info);

      /// Border中に最初の模様ですか
      if (left == null ||
          top == null ||
          right == null ||
          bottom == null ||
          maskTop == null ||
          maskLeft == null ||
          maskRight == null ||
          maskBottom == null) {
        left = info.left;
        top = info.top;
        right = info.left + info.width;
        bottom = info.top + info.height;

        var result = getMaxOuterWithMask(
          maskTopLeft: info.mask.topLeft,
          maskTopRight: info.mask.topRight,
          maskBottomLeft: info.mask.bottomLeft,
          maskBottomRight: info.mask.bottomRight,
        );
        maskTop = result.topMin;
        maskLeft = result.leftMin;
        maskBottom = result.bottomMax;
        maskRight = result.rightMax;
      } else {
        left = min(left, info.left);
        top = min(top, info.top);
        right = max(right, info.left + info.width);
        bottom = max(bottom, info.top + info.height);
        var result = getMaxOuterWithMask(
          maskTopLeft: info.mask.topLeft,
          maskTopRight: info.mask.topRight,
          maskBottomLeft: info.mask.bottomLeft,
          maskBottomRight: info.mask.bottomRight,
        );
        maskTop = min(maskTop, result.topMin);
        maskLeft = min(maskLeft, result.leftMin);
        maskBottom = max(maskBottom, result.bottomMax);
        maskRight = max(maskRight, result.rightMax);
      }
    }

    /// 左上隅の位置変換
    Mask mask = Mask(
      topLeft: Offset(maskLeft!, maskTop!),
      topRight: Offset(maskRight!, maskTop),
      bottomLeft: Offset(maskLeft, maskBottom!),
      bottomRight: Offset(maskRight, maskBottom),
    );

    /// 所有Group的位置进行偏移
    List<EmbGroupDisplayInfo> groupDisplayInfoList = [];
    for (var element in sourceGroupDisplayInfoList) {
      groupDisplayInfoList.add(
        element.copyWith(
          top: element.top - top!,
          left: element.left - left!,
        ),
      );
    }

    return EmbBorderDisplayInfo(
      sewingIndex: sewingIndex,
      isSelected: groupDisplayInfoList.first.isSelected,
      isCurrentPattern: isCurrentPattern,
      left: left!,
      top: top!,
      width: right! - left,
      height: bottom! - top,
      maskDisplayInfo: mask,
      baseMaskInfo: mask,
      isBaseBorder: true,
      groupDisplayInfoList: groupDisplayInfoList,
    );
  }

  ///
  /// RepeatBorderPatternの表示情報の取得
  ///
  PatternDisplayInfo _getRepeatBorderPatternDisplayInfo({
    required EmbBorderDisplayInfo baseBorderInfo,
    required double pixelOfOneMm,
    required EmbBorder border,
    required BorderInfo borderInfo,
  }) {
    List<EmbBorderDisplayInfo> borderDisplayInfoList = [];

    /// 基本模様を取得します
    double? patternLeft;
    double? patternTop;
    double? patternRight;
    double? patternBottom;
    double? patternMaskTop;
    double? patternMaskLeft;
    double? patternMaskRight;
    double? patternMaskBottom;

    SSPoint style = borderInfo.style;
    SSPoint space = borderInfo.space;
    Offset spaceOffset = Offset(
      convertMmToPixels(value: space.X, pixelOfOneMm: pixelOfOneMm),
      convertMmToPixels(value: space.Y, pixelOfOneMm: pixelOfOneMm),
    );
    for (int indexY = 0; indexY < style.Y; indexY++) {
      for (int indexX = 0; indexX < style.X; indexX++) {
        final mark = border.getBorderCompInfo(indexY * style.X + indexX);

        double borderLeft =
            indexX * (baseBorderInfo.width - maskOutArea * 2 + spaceOffset.dx);
        double borderTop =
            indexY * (baseBorderInfo.height - maskOutArea * 2 + spaceOffset.dy);

        double borderMaskTop =
            baseBorderInfo.maskDisplayInfo.topLeft.dy + borderTop;
        double borderMaskLeft =
            baseBorderInfo.maskDisplayInfo.topLeft.dx + borderLeft;
        double borderMaskRight =
            baseBorderInfo.maskDisplayInfo.bottomRight.dx + borderLeft;
        double borderMaskBottom =
            baseBorderInfo.maskDisplayInfo.bottomRight.dy + borderTop;

        /// 糸印が存在する場合、赤いボックスの位置がオフセットされます
        if (space.Y >= 0 && (mark.top || mark.topLeft || mark.topRight)) {
          borderMaskTop -= spaceOffset.dy;
        }
        if (space.Y >= 0 &&
            (mark.bottom || mark.bottomLeft || mark.bottomRight)) {
          borderMaskBottom += spaceOffset.dy;
        }
        if (space.X >= 0 &&
            (mark.centerLeft || mark.topLeft || mark.bottomLeft)) {
          borderMaskLeft -= spaceOffset.dx;
        }
        if (space.X >= 0 &&
            (mark.centerRight || mark.topRight || mark.bottomRight)) {
          borderMaskRight += spaceOffset.dx;
        }

        EmbBorderDisplayInfo info = EmbBorderDisplayInfo(
          isBaseBorder: indexX == 0 && indexY == 0 ? true : false,
          left: borderLeft,
          top: borderTop,
          width: baseBorderInfo.width,
          height: baseBorderInfo.height,
          sewingIndex: baseBorderInfo.sewingIndex,
          isSelected: baseBorderInfo.isSelected,
          isCurrentPattern: baseBorderInfo.isCurrentPattern,
          baseMaskInfo: baseBorderInfo.baseMaskInfo,
          maskDisplayInfo: Mask(
            topLeft: Offset(borderMaskLeft, borderMaskTop),
            topRight: Offset(borderMaskRight, borderMaskTop),
            bottomLeft: Offset(borderMaskLeft, borderMaskBottom),
            bottomRight: Offset(borderMaskRight, borderMaskBottom),
          ),
          threadMarkState: mark,
          groupDisplayInfoList: baseBorderInfo.groupDisplayInfoList,
        );
        borderDisplayInfoList.add(info);

        if (patternLeft == null ||
            patternTop == null ||
            patternRight == null ||
            patternBottom == null ||
            patternMaskTop == null ||
            patternMaskLeft == null ||
            patternMaskRight == null ||
            patternMaskBottom == null) {
          patternLeft = info.left;
          patternTop = info.top;
          patternRight = info.left + info.width;
          patternBottom = info.top + info.height;

          var (
            topMin: topMin,
            leftMin: leftMin,
            rightMax: rightMax,
            bottomMax: bottomMax,
          ) = getMaxOuterWithMask(
            maskTopLeft: info.maskDisplayInfo.topLeft,
            maskTopRight: info.maskDisplayInfo.topRight,
            maskBottomLeft: info.maskDisplayInfo.bottomLeft,
            maskBottomRight: info.maskDisplayInfo.bottomRight,
          );

          patternMaskTop = topMin;
          patternMaskLeft = leftMin;
          patternMaskBottom = bottomMax;
          patternMaskRight = rightMax;
        } else {
          patternLeft = min(patternLeft, info.left);
          patternTop = min(patternTop, info.top);
          patternRight = max(patternRight, info.left + info.width);
          patternBottom = max(patternBottom, info.top + info.height);
          var (
            topMin: topMin,
            leftMin: leftMin,
            rightMax: rightMax,
            bottomMax: bottomMax,
          ) = getMaxOuterWithMask(
            maskTopLeft: info.maskDisplayInfo.topLeft,
            maskTopRight: info.maskDisplayInfo.topRight,
            maskBottomLeft: info.maskDisplayInfo.bottomLeft,
            maskBottomRight: info.maskDisplayInfo.bottomRight,
          );

          patternMaskTop = min(patternMaskTop, topMin);
          patternMaskLeft = min(patternMaskLeft, leftMin);
          patternMaskBottom = max(patternMaskBottom, bottomMax);
          patternMaskRight = max(patternMaskRight, rightMax);
        }
      }
    }

    return PatternDisplayInfo(
      top: baseBorderInfo.top,
      left: baseBorderInfo.left,
      width: patternRight! - patternLeft!,
      height: patternBottom! - patternTop!,
      isGroup: false,
      isSelected: baseBorderInfo.isSelected,
      isCurrentPattern: baseBorderInfo.isCurrentPattern,
      maskDisplayInfo: Mask(
        topLeft: Offset(patternMaskLeft!, patternMaskTop!),
        topRight: Offset(patternMaskRight!, patternMaskTop),
        bottomLeft: Offset(patternMaskLeft, patternMaskBottom!),
        bottomRight: Offset(patternMaskRight, patternMaskBottom),
      ),
      style: style,
      space: space,
      borderDisplayInfoList: borderDisplayInfoList,
    );
  }

  ///
  /// 回転過程中に、再度pattern情報を再計算します
  ///
  PatternDisplayInfo _getPatternDisplayInfoInRotate({
    required EmbBorderDisplayInfo baseBorderInfo,
    required SSPoint style,
    required SSPoint space,
    required bool isGroup,
    required double pixelOfOneMm,
    required List<ThreadMarkState> threadMarkStates,
  }) {
    List<EmbBorderDisplayInfo> patternDisplayInfoList = [];

    double? patternLeft;
    double? patternTop;
    double? patternRight;
    double? patternBottom;
    double? patternMaskTop;
    double? patternMaskLeft;
    double? patternMaskRight;
    double? patternMaskBottom;

    var (
      topMin: maskTopMin,
      leftMin: maskLeftMin,
      bottomMax: maskBottomMax,
      rightMax: maskRightMax
    ) = getMaxOuterWithMask(
      maskTopLeft: baseBorderInfo.maskDisplayInfo.topLeft,
      maskTopRight: baseBorderInfo.maskDisplayInfo.topRight,
      maskBottomLeft: baseBorderInfo.maskDisplayInfo.bottomLeft,
      maskBottomRight: baseBorderInfo.maskDisplayInfo.bottomRight,
    );

    double maskWidth = maskRightMax - maskLeftMin;
    double maskHeight = maskBottomMax - maskTopMin;

    Offset spaceOffset = Offset(
      convertMmToPixels(value: space.X, pixelOfOneMm: pixelOfOneMm),
      convertMmToPixels(value: space.Y, pixelOfOneMm: pixelOfOneMm),
    );

    for (int indexY = 0; indexY < style.Y; indexY++) {
      for (int indexX = 0; indexX < style.X; indexX++) {
        var mark = threadMarkStates[indexY * style.X + indexX];
        double borderTop = indexY * (maskHeight + spaceOffset.dy);
        double borderLeft = indexX * (maskWidth + spaceOffset.dx);
        double borderMaskTop =
            baseBorderInfo.maskDisplayInfo.topLeft.dy + borderTop;
        double borderMaskLeft =
            baseBorderInfo.maskDisplayInfo.topLeft.dx + borderLeft;
        double borderMaskRight =
            baseBorderInfo.maskDisplayInfo.bottomRight.dx + borderLeft;
        double borderMaskBottom =
            baseBorderInfo.maskDisplayInfo.bottomRight.dy + borderTop;

        /// 糸印が存在する場合、赤いボックスの位置がオフセットされます
        if (space.Y >= 0 && (mark.top || mark.topLeft || mark.topRight)) {
          borderMaskTop -= spaceOffset.dy;
        }
        if (space.Y >= 0 &&
            (mark.bottom || mark.bottomLeft || mark.bottomRight)) {
          borderMaskBottom += spaceOffset.dy;
        }
        if (space.X >= 0 &&
            (mark.centerLeft || mark.topLeft || mark.bottomLeft)) {
          borderMaskLeft -= spaceOffset.dx;
        }
        if (space.X >= 0 &&
            (mark.centerRight || mark.topRight || mark.bottomRight)) {
          borderMaskRight += spaceOffset.dx;
        }

        EmbBorderDisplayInfo info = EmbBorderDisplayInfo(
          isBaseBorder: indexX == 0 && indexY == 0 ? true : false,
          top: borderTop,
          left: borderLeft,
          width: baseBorderInfo.width,
          height: baseBorderInfo.height,
          sewingIndex: baseBorderInfo.sewingIndex,
          isSelected: baseBorderInfo.isSelected,
          isCurrentPattern: baseBorderInfo.isCurrentPattern,
          baseMaskInfo: baseBorderInfo.baseMaskInfo,
          maskDisplayInfo: Mask(
            topLeft: Offset(borderMaskLeft, borderMaskTop),
            topRight: Offset(borderMaskRight, borderMaskTop),
            bottomLeft: Offset(borderMaskLeft, borderMaskBottom),
            bottomRight: Offset(borderMaskRight, borderMaskBottom),
          ),
          threadMarkState: mark,
          groupDisplayInfoList: baseBorderInfo.groupDisplayInfoList,
        );
        patternDisplayInfoList.add(info);

        if (patternLeft == null ||
            patternTop == null ||
            patternRight == null ||
            patternBottom == null ||
            patternMaskTop == null ||
            patternMaskLeft == null ||
            patternMaskRight == null ||
            patternMaskBottom == null) {
          patternLeft = info.left;
          patternTop = info.top;
          patternRight = info.left + info.width;
          patternBottom = info.top + info.height;

          var (
            topMin: topMin,
            leftMin: leftMin,
            bottomMax: bottomMax,
            rightMax: rightMax
          ) = getMaxOuterWithMask(
            maskTopLeft: info.maskDisplayInfo.topLeft,
            maskTopRight: info.maskDisplayInfo.topRight,
            maskBottomLeft: info.maskDisplayInfo.bottomLeft,
            maskBottomRight: info.maskDisplayInfo.bottomRight,
          );
          patternMaskTop = topMin;
          patternMaskLeft = leftMin;
          patternMaskBottom = bottomMax;
          patternMaskRight = rightMax;
        } else {
          patternLeft = min(patternLeft, info.left);
          patternTop = min(patternTop, info.top);
          patternRight = max(patternRight, info.left + info.width);
          patternBottom = max(patternBottom, info.top + info.height);
          var (
            topMin: topMin,
            leftMin: leftMin,
            bottomMax: bottomMax,
            rightMax: rightMax
          ) = getMaxOuterWithMask(
            maskTopLeft: info.maskDisplayInfo.topLeft,
            maskTopRight: info.maskDisplayInfo.topRight,
            maskBottomLeft: info.maskDisplayInfo.bottomLeft,
            maskBottomRight: info.maskDisplayInfo.bottomRight,
          );
          patternMaskTop = min(patternMaskTop, topMin);
          patternMaskLeft = min(patternMaskLeft, leftMin);
          patternMaskBottom = max(patternMaskBottom, bottomMax);
          patternMaskRight = max(patternMaskRight, rightMax);
        }
      }
    }

    return PatternDisplayInfo(
      top: baseBorderInfo.top,
      left: baseBorderInfo.left,
      width: patternRight! - patternLeft!,
      height: patternBottom! - patternTop!,
      isGroup: isGroup,
      isSelected: baseBorderInfo.isSelected,
      isCurrentPattern: baseBorderInfo.isCurrentPattern,
      maskDisplayInfo: Mask(
        topLeft: Offset(patternMaskLeft!, patternMaskTop!),
        topRight: Offset(patternMaskRight!, patternMaskTop),
        bottomLeft: Offset(patternMaskLeft, patternMaskBottom!),
        bottomRight: Offset(patternMaskRight, patternMaskBottom),
      ),
      style: style,
      space: space,
      borderDisplayInfoList: patternDisplayInfoList,
    );
  }

  ///
  /// Patternの表示情報の取得
  ///
  EmbGroupDisplayInfo getEmbGroupDisplayInfo({
    required Offset centerPoint,
    required double pixelOfOneMm,
    required int sewingIndex,
    required EmbGroup group,
    required int scrollType,
    required MemHandle currentGroupHandle,
    required int zoomScale,
    MemHandle? cursorHandle,
    CursorType? cursorType,
  }) {
    bool isAllNotSewing =
        group.threadInfo.every((element) => element.notSewing == true);
    bool isCurrentPattern = group.isCurrentGroup(currentGroupHandle);

    ///
    /// group内のすべての画像を取得する
    ///
    List<Uint8List> imageList = group.mainImage(scrollType, zoomScale);
    Iterator<Uint8List> imageListIterator = imageList.iterator;

    /// ミリメートルをピクセルに変換
    var (embInfo: embInfo, embGrp: embGroup, embPatternInfo: embPatternInfo) =
        group.embGroupInfo;
    List<EmbPatternDisplayInfo> sourceEmbPatternDisplayInfoList =
        _getEmbPatternDisplayInfo(
      group: group,
      centerPoint: centerPoint,
      pixelOfOneMm: pixelOfOneMm,
      embInfo: embInfo,
      embGrp: embGroup,
      embPatternInfo: embPatternInfo,
      cursorHandle: cursorHandle,
      cursorType: cursorType,
    );

    double? leftMin;
    double? topMin;
    double? rightMax;
    double? bottomMax;
    double? maskTop;
    double? maskLeft;
    double? maskRight;
    double? maskBottom;
    for (var info in sourceEmbPatternDisplayInfoList) {
      Offset topLeft = Offset(info.left, info.top);
      if (leftMin == null ||
          topMin == null ||
          rightMax == null ||
          bottomMax == null ||
          maskTop == null ||
          maskLeft == null ||
          maskRight == null ||
          maskBottom == null) {
        leftMin = info.left;
        topMin = info.top;
        rightMax = info.left + info.width;
        bottomMax = info.top + info.height;
        var result = getMaxOuterWithMask(
          maskTopLeft: info.mask.topLeft + topLeft,
          maskTopRight: info.mask.topRight + topLeft,
          maskBottomLeft: info.mask.bottomLeft + topLeft,
          maskBottomRight: info.mask.bottomRight + topLeft,
        );
        maskTop = result.topMin;
        maskLeft = result.leftMin;
        maskBottom = result.bottomMax;
        maskRight = result.rightMax;
      } else {
        leftMin = min(leftMin, info.left);
        topMin = min(topMin, info.top);
        rightMax = max(rightMax, info.left + info.width);
        bottomMax = max(bottomMax, info.top + info.height);
        var result = getMaxOuterWithMask(
          maskTopLeft: info.mask.topLeft + topLeft,
          maskTopRight: info.mask.topRight + topLeft,
          maskBottomLeft: info.mask.bottomLeft + topLeft,
          maskBottomRight: info.mask.bottomRight + topLeft,
        );
        maskTop = min(maskTop, result.topMin);
        maskLeft = min(maskLeft, result.leftMin);
        maskBottom = max(maskBottom, result.bottomMax);
        maskRight = max(maskRight, result.rightMax);
      }

      /// embPatternの画像を追加する
      if (imageListIterator.moveNext()) {
        info.displayImage = imageListIterator.current;
      } else {
        /// do nothing
      }
    }

    /// 左上隅の位置変換
    Mask mask = Mask(
      topLeft: Offset(maskLeft!, maskTop!),
      topRight: Offset(maskRight!, maskTop),
      bottomLeft: Offset(maskLeft, maskBottom!),
      bottomRight: Offset(maskRight, maskBottom),
    );
    List<EmbPatternDisplayInfo> embPatternDisplayInfoList = [];
    for (var element in sourceEmbPatternDisplayInfoList) {
      embPatternDisplayInfoList.add(
        element.copyWith(
          top: element.top - topMin!,
          left: element.left - leftMin!,
          imageTop: element.imageTop - topMin,
          imageLeft: element.imageLeft - leftMin,
        ),
      );
    }

    ///
    /// 円弧下線イメージの取得
    ///
    Uint8List? arcImage;
    if (embGroup.patternLayOutMethod != PatternLayOut.flat &&
        embGroup.patternLayOutMethod != PatternLayOut.slant) {
      arcImage = group.getArcImage(scrollType);
    } else {
      /// do nothing
    }

    return EmbGroupDisplayInfo(
      sewingIndex: sewingIndex,
      left: leftMin!,
      top: topMin!,
      width: rightMax! - leftMin,
      height: bottomMax! - topMin,
      angle: embGroup.angle / conversionRate,
      isSelected: embGroup.selected,
      isCurrentPattern: isCurrentPattern,
      mask: mask,
      isAllNotSewing: isAllNotSewing,
      isFont: group.isFont,
      embPatternDisplayInfoList: embPatternDisplayInfoList,
      arcImage: arcImage,
    );
  }

  ///
  /// EmbPatternの表示情報の取得
  ///
  List<EmbPatternDisplayInfo> _getEmbPatternDisplayInfo({
    required Offset centerPoint,
    required double pixelOfOneMm,
    required EmbInfo embInfo,
    required EmbGrp embGrp,
    required EmbPtrnInfo embPatternInfo,
    required EmbGroup group,
    MemHandle? cursorHandle,
    CursorType? cursorType,
  }) {
    final List<EmbPatternDisplayInfo> embPatternDisplayInfoList = [];

    for (var index = 0; index < embPatternInfo.embPatterns.length; index++) {
      EmbPtrn embPtrn = embPatternInfo.embPatterns[index];

      /// ミリメートルをピクセルに変換
      final SSPoint position = SSPoint(
        X: embPtrn.inGrpOffset.X + embGrp.position.X + embInfo.frameOffset.X,
        Y: embPtrn.inGrpOffset.Y + embGrp.position.Y + embInfo.frameOffset.Y,
      );

      /// Patternのサイズを取得
      final RectanArea size = embPtrn.size;
      final double patternWidth = convertXMmToPixelsWithCenter(
              position.X + size.right, centerPoint, pixelOfOneMm) -
          convertXMmToPixelsWithCenter(
              position.X + size.left, centerPoint, pixelOfOneMm);
      final double patternHeight = convertYMmToPixelsWithCenter(
              position.Y + size.bottom, centerPoint, pixelOfOneMm) -
          convertYMmToPixelsWithCenter(
              position.Y + size.top, centerPoint, pixelOfOneMm);

      /// Libの赤枠データを表示情報に変換する
      final Rectangle mask = embPtrn.rotatedMask;
      Offset maskTopLeft = Offset(
          convertXMmToPixelsWithCenter(
              position.X + mask.positionTopLeft.X, centerPoint, pixelOfOneMm),
          convertYMmToPixelsWithCenter(
              position.Y + mask.positionTopLeft.Y, centerPoint, pixelOfOneMm));
      Offset maskTopRight = Offset(
          convertXMmToPixelsWithCenter(
              position.X + mask.positionTopRight.X, centerPoint, pixelOfOneMm),
          convertYMmToPixelsWithCenter(
              position.Y + mask.positionTopRight.Y, centerPoint, pixelOfOneMm));
      Offset maskBottomLeft = Offset(
          convertXMmToPixelsWithCenter(position.X + mask.positionBottomLeft.X,
              centerPoint, pixelOfOneMm),
          convertYMmToPixelsWithCenter(position.Y + mask.positionBottomLeft.Y,
              centerPoint, pixelOfOneMm));
      Offset maskBottomRight = Offset(
          convertXMmToPixelsWithCenter(position.X + mask.positionBottomRight.X,
              centerPoint, pixelOfOneMm),
          convertYMmToPixelsWithCenter(position.Y + mask.positionBottomRight.Y,
              centerPoint, pixelOfOneMm));

      /// Patternの表示範囲の計算
      var (
        topMin: topMin,
        leftMin: leftMin,
        bottomMax: bottomMax,
        rightMax: rightMax
      ) = getMaxOuterWithMask(
        maskTopLeft: maskTopLeft,
        maskTopRight: maskTopRight,
        maskBottomLeft: maskBottomLeft,
        maskBottomRight: maskBottomRight,
      );

      /// 赤い点の追加タッチ領域を追加する
      rightMax = rightMax + maskOutArea;
      bottomMax = bottomMax + maskOutArea;
      leftMin = leftMin - maskOutArea;
      topMin = topMin - maskOutArea;

      /// Maskのペイント開始点をPattern表示領域の開始点に変更
      maskTopLeft = Offset(
        maskTopLeft.dx - leftMin,
        maskTopLeft.dy - topMin,
      );
      maskTopRight = Offset(
        maskTopRight.dx - leftMin,
        maskTopRight.dy - topMin,
      );
      maskBottomLeft = Offset(
        maskBottomLeft.dx - leftMin,
        maskBottomLeft.dy - topMin,
      );
      maskBottomRight = Offset(
        maskBottomRight.dx - leftMin,
        maskBottomRight.dy - topMin,
      );

      /// Imageのサイズと開始点を取得
      RectanArea packedMask = embPtrn.packedMask;
      double imageWidth = convertMmToPixels(
        value: packedMask.right - packedMask.left,
        pixelOfOneMm: pixelOfOneMm,
      );
      double imageHeight = convertMmToPixels(
        value: packedMask.bottom - packedMask.top,
        pixelOfOneMm: pixelOfOneMm,
      );
      Offset imageTopLeft = Offset(
          convertXMmToPixelsWithCenter(
              position.X + packedMask.left, centerPoint, pixelOfOneMm),
          convertYMmToPixelsWithCenter(
              position.Y + packedMask.top, centerPoint, pixelOfOneMm));

      /// カーソル位置
      bool isCursorLeft = false, isCursorRight = false;
      if (cursorType != null && group.isFont) {
        List<MemHandle> embPatterns = group.getEmbCharPatternHandleList();
        isCursorLeft = cursorHandle == embPatterns[index] &&
            cursorType == CursorType.unselectLeft;
        isCursorRight = cursorHandle == embPatterns[index] &&
            cursorType != CursorType.unselectLeft;
      } else {
        /// do nothing
      }

      final EmbPatternDisplayInfo embPatternDisplayInfo = EmbPatternDisplayInfo(
        left: leftMin,
        top: topMin,
        width: rightMax - leftMin,
        height: bottomMax - topMin,
        patternWidth: patternWidth,
        patternHeight: patternHeight,
        angle: embGrp.angle / conversionRate,
        imageTop: imageTopLeft.dy,
        imageLeft: imageTopLeft.dx,
        imageWidth: imageWidth,
        imageHeight: imageHeight,
        mask: Mask(
          topLeft: maskTopLeft,
          topRight: maskTopRight,
          bottomLeft: maskBottomLeft,
          bottomRight: maskBottomRight,
        ),
        isCursorLeft: isCursorLeft,
        isCursorRight: isCursorRight,
        isSelected: embPtrn.selected,
      );

      embPatternDisplayInfoList.add(embPatternDisplayInfo);
    }
    return embPatternDisplayInfoList;
  }

  ///
  /// 赤いドットの追加クリック領域
  /// 上下左右に' redPointTouchOutArea'ずつ増やします。
  ///
  double getRedPointTouchOutArea({
    required Offset topLeft,
    required Offset topRight,
    required Offset bottomLeft,
    required double magnification,
  }) {
    final double firstWidth =
        _calculateDistance(topLeft, topRight) * magnification;
    final double firstHeight =
        _calculateDistance(topLeft, bottomLeft) * magnification;

    /// (( マスクの縦方向の長さのdot ) - ( 間隔dot * 2 )) / 3;
    final double topOneIconMaxSize =
        (firstHeight - (redPointImageSpace * 2)) / 3;

    /// (( マスクの横方向の長さのdot ) - ( 間隔dot * 2 )) / 3;
    final double sideOneIconMaxSize =
        (firstWidth - (redPointImageSpace * 2)) / 3;

    /// 横と縦の小さい方の大きさに応じて、パンツールの収容可能区域を計算します。
    double touchAreaSize = topOneIconMaxSize > sideOneIconMaxSize
        ? sideOneIconMaxSize
        : topOneIconMaxSize;

    /// 最大50dot、最小画像サイズ（17dot）内で制御
    touchAreaSize =
        touchAreaSize.clamp(redPointImageSize, redPointMaxTouchArea);

    /// 制御したサイズの半分が拡張エリアとなります
    return ((touchAreaSize - redPointImageSize) / 2) / magnification;
  }

  ///
  /// Rotate赤色ドットの描画情報を取得する
  ///
  List<RedPointInfo> getRotateRedPointInfo({
    required Offset topLeft,
    required Offset topRight,
    required Offset bottomLeft,
    required Offset bottomRight,
    required double magnification,
    required ToolbarPopupId toolbarPopupId,
    required bool isBorder,
  }) {
    if (toolbarPopupId != ToolbarPopupId.rotate &&
        toolbarPopupId != ToolbarPopupId.none &&
        toolbarPopupId != ToolbarPopupId.edit) {
      return [];

      /// 特殊な場合は、ドットを表示しない
    } else if (isInDragPreviewMode) {
      return [];
    } else if (isBorder) {
      return [];
    }

    /// 現在の拡大倍率
    /// ドット表示スペースが不足している場合は、表示しない
    double firstWidth = _calculateDistance(topLeft, topRight) * magnification;
    double firstHeight =
        _calculateDistance(topLeft, bottomLeft) * magnification;
    if (firstWidth < (redPointImageSize * 3 + redPointImageSpace * 2) ||
        firstHeight < (redPointImageSize * 3 + redPointImageSpace * 2)) {
      return [];
    }

    /// Rotate調整ができない場合
    /// API呼び出しの数を減らすために、最後の判断に調整します。
    if (EditModel().isEmbEditRotateDisable()) {
      return [];
    }

    return [
      _getRedPointInfo(
        topLeft: topLeft,
        topRight: topRight,
        bottomLeft: bottomLeft,
        bottomRight: bottomRight,
        pointType: PointType.rotate,
        magnification: magnification,
        redPointPositionType: PointPositionType.topCenter,
      )
    ];
  }

  ///
  /// Size赤色ドットの描画情報を取得する
  ///
  List<RedPointInfo> getSizeRedPointInfo({
    required Offset topLeft,
    required Offset topRight,
    required Offset bottomLeft,
    required Offset bottomRight,
    required double magnification,
    required ToolbarPopupId toolbarPopupId,
    required bool isBorder,
  }) {
    /// 赤いドットの表示モードを判断する
    List<PointPositionType> redPointPositionTypeList;
    List<RedPointInfo> sizeRedPointInfoList = [];

    if (toolbarPopupId == ToolbarPopupId.sizeAdjustment) {
      redPointPositionTypeList = PointPositionType.values;
    } else if (isBorder) {
      return [];
    } else if (toolbarPopupId == ToolbarPopupId.none ||
        toolbarPopupId == ToolbarPopupId.edit) {
      redPointPositionTypeList = [
        PointPositionType.topLeft,
        PointPositionType.topRight,
        PointPositionType.bottomLeft,
        PointPositionType.bottomRight,
      ];
    } else {
      return [];
    }

    /// サイズ調整ができない場合
    if (isInDragPreviewMode) {
      return [];
    }

    /// 現在の拡大倍率
    /// ドット表示スペースが不足している場合は、表示しない
    double firstWidth = _calculateDistance(topLeft, topRight) * magnification;
    double firstHeight =
        _calculateDistance(topLeft, bottomLeft) * magnification;
    if (firstWidth < (redPointImageSize * 3 + redPointImageSpace * 2) ||
        firstHeight < (redPointImageSize * 3 + redPointImageSpace * 2)) {
      return [];
    }

    /// サイズ調整ができない場合
    /// API呼び出しの数を減らすために、最後の判断に調整します。
    if (EditModel().isEmbEditSizeDisable()) {
      return [];
    }

    for (var redPointPositionType in redPointPositionTypeList) {
      sizeRedPointInfoList.add(
        _getRedPointInfo(
          topLeft: topLeft,
          topRight: topRight,
          bottomLeft: bottomLeft,
          bottomRight: bottomRight,
          pointType: PointType.size,
          magnification: magnification,
          redPointPositionType: redPointPositionType,
        ),
      );
    }

    return sizeRedPointInfoList;
  }

  ///
  /// Sewing ページの点の位置を取得します。
  ///
  RedPointDisplayInfo getSewingRotateRedPointInfo({
    required Mask mask,
  }) {
    final List<RedPointInfo> rotateRedPointInfoList = [];

    /// 左上隅が座標原点となったため、他の位置をオフセットする必要があります。
    final Offset baseTopLeft = mask.topLeft;
    final Offset topLeft = mask.topLeft - baseTopLeft;
    final Offset topRight = mask.topRight - baseTopLeft;
    final Offset bottomLeft = mask.bottomLeft - baseTopLeft;
    final Offset bottomRight = mask.bottomRight - baseTopLeft;

    /// サーズを计算する
    final double firstWidth = _calculateDistance(topLeft, topRight);
    final double firstHeight = _calculateDistance(topLeft, bottomLeft);

    /// ドット表示スペースが不足している場合は、表示しない
    if (firstWidth < (redPointImageSize * 3 + redPointImageSpace * 2) ||
        firstHeight < (redPointImageSize * 3 + redPointImageSpace * 2)) {
      rotateRedPointInfoList.clear();
    } else {
      rotateRedPointInfoList.add(
        _getRedPointInfo(
          topLeft: topLeft,
          topRight: topRight,
          bottomLeft: bottomLeft,
          bottomRight: bottomRight,
          pointType: PointType.rotate,
          magnification: 1,
          redPointPositionType: PointPositionType.topCenter,
        ),
      );
    }

    /// 赤いドットの追加タッチ領域サイズ
    final double redPointTouchOutAreaInView = getRedPointTouchOutArea(
      topLeft: topLeft,
      topRight: topRight,
      bottomLeft: bottomLeft,
      magnification: 1,
    );

    return RedPointDisplayInfo(
      topLeft: baseTopLeft -
          Offset(redPointTouchOutAreaInView, redPointTouchOutAreaInView),
      width: firstWidth + redPointTouchOutAreaInView * 2,
      height: firstHeight + redPointTouchOutAreaInView * 2,
      redPointImageSize: redPointImageSize,
      redPointTouchOutArea: redPointTouchOutAreaInView,
      redPointInfos: rotateRedPointInfoList,

      /// 縫製画面に、angleは使用ではない、0にします
      angle: 0,
    );
  }

  ///
  /// プレビュー領域に描画された画像の開始点とサイズを取得します
  ///
  /// 戻り値   :
  /// - [Offset] : 画像の開始点
  /// - [Offset] : 画像のサイズ
  ///
  List<TopLeftAndSize> getImageTopLeftAndSize({
    required EmbGroup group,
    required Offset centerPoint,
    required double pixelOfOneMm,
  }) {
    final List<TopLeftAndSize> allStartPointAndSizeList = [];

    /// ミリメートルをピクセルに変換
    var (embInfo: _, embGrp: embGroup, embPatternInfo: embPatternInfo) =
        group.embGroupInfo;
    for (var embPattern in embPatternInfo.embPatterns) {
      SSPoint position = SSPoint(
        X: embPattern.inGrpOffset.X + embGroup.position.X,
        Y: embPattern.inGrpOffset.Y + embGroup.position.Y,
      );

      /// Imageのサイズと開始点を取得
      RectanArea packedMask = embPattern.packedMask;
      Offset size = Offset(
        convertMmToPixels(
            value: packedMask.right - packedMask.left,
            pixelOfOneMm: pixelOfOneMm),
        convertMmToPixels(
            value: packedMask.bottom - packedMask.top,
            pixelOfOneMm: pixelOfOneMm),
      );
      Offset topLeft = Offset(
        convertXMmToPixelsWithCenter(
            position.X + packedMask.left, centerPoint, pixelOfOneMm),
        convertYMmToPixelsWithCenter(
            position.Y + packedMask.top, centerPoint, pixelOfOneMm),
      );

      allStartPointAndSizeList.add(TopLeftAndSize(
        topLeft: topLeft,
        size: size,
      ));
    }
    return allStartPointAndSizeList;
  }

  ///
  /// すべての模様の写真を組み合わせます(PNG)
  ///
  Future<Uint8List> combineAllPatternImage({
    required Offset centerPoint,
    required double pixelOfOneMm,
  }) async {
    List<Offset> sizeList = [];
    List<Offset> startPointList = [];
    List<ui.Image> imageList = [];
    double imageWidth = 0;
    double imageHeight = 0;
    Offset? minTopLeft;

    List<EmbGroup> groupList = PatternModel().getAllGroup();
    for (var group in groupList) {
      /// すべての模様の画像と位置データを取得します
      var list = PreviewModel().getImageTopLeftAndSize(
        group: group,
        centerPoint: centerPoint,
        pixelOfOneMm: pixelOfOneMm,
      );
      List<Uint8List> groupImageList =
          group.mainImage(ScrollCenterType.IMAGE_EDITING, 100);

      /// 最小開始点と最大範囲を計算する
      for (int i = 0; i < list.length; i++) {
        Offset size = list[i].size;
        Offset topLeft = list[i].topLeft;

        if (minTopLeft != null) {
          minTopLeft = Offset(
            min(minTopLeft.dx, topLeft.dx),
            min(minTopLeft.dy, topLeft.dy),
          );
        } else {
          minTopLeft = topLeft;
        }
        imageWidth = max(imageWidth, topLeft.dx + size.dx);
        imageHeight = max(imageHeight, topLeft.dy + size.dy);

        sizeList.add(size);
        startPointList.add(topLeft);
        imageList.add(
          await decodeImageFromList(groupImageList[i]),
        );
      }
    }

    /// 画像の幅と高さは、開始座標からさらに計算されます。
    imageWidth -= minTopLeft!.dx;
    imageHeight -= minTopLeft.dy;

    /// キャンバスを作成し、画像を合成する
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(
      recorder,
      Rect.fromPoints(
        const Offset(0, 0),
        Offset(imageWidth, imageHeight),
      ),
    );

    /// キャンバスに絵を描く
    for (int index = 0; index < imageList.length; index++) {
      canvas.drawImage(
          imageList[index], startPointList[index] - minTopLeft, Paint());
    }

    /// 描画を終了し、画像を生成する
    final picture = recorder.endRecording();
    final img = await picture.toImage(imageWidth.ceil(), imageHeight.ceil());

    /// バイトデータをPNG形式に変換する
    final ByteData? byteData =
        await img.toByteData(format: ui.ImageByteFormat.png);

    return byteData!.buffer.asUint8List();
  }

  ///
  /// 背景色
  ///
  Color getEmbroideryBackgroundColor() => _backGroundColorTable[
      DeviceLibrary().apiBinding.getEmbroideryBackgroundColor().value];
  static const List<Color> _backGroundColorTable = [
    Color.fromARGB(255, 255, 255, 255),
    Color.fromARGB(255, 255, 255, 201),
    Color.fromARGB(255, 255, 248, 155),
    Color.fromARGB(255, 240, 255, 156),
    Color.fromARGB(255, 212, 255, 212),
    Color.fromARGB(255, 206, 248, 255),
    Color.fromARGB(255, 209, 232, 255),
    Color.fromARGB(255, 233, 217, 255),
    Color.fromARGB(255, 247, 207, 255),
    Color.fromARGB(255, 255, 209, 236),
    Color.fromARGB(255, 255, 219, 219),
    Color.fromARGB(255, 204, 204, 204),
    Color.fromARGB(255, 255, 191, 143),
    Color.fromARGB(255, 255, 245, 100),
    Color.fromARGB(255, 216, 255, 0),
    Color.fromARGB(255, 161, 255, 161),
    Color.fromARGB(255, 152, 239, 255),
    Color.fromARGB(255, 163, 209, 255),
    Color.fromARGB(255, 210, 178, 255),
    Color.fromARGB(255, 241, 168, 255),
    Color.fromARGB(255, 255, 168, 219),
    Color.fromARGB(255, 255, 160, 160),
    Color.fromARGB(255, 153, 153, 153),
    Color.fromARGB(255, 255, 158, 83),
    Color.fromARGB(255, 244, 227, 0),
    Color.fromARGB(255, 197, 235, 0),
    Color.fromARGB(255, 89, 240, 89),
    Color.fromARGB(255, 14, 209, 244),
    Color.fromARGB(255, 97, 176, 255),
    Color.fromARGB(255, 182, 130, 255),
    Color.fromARGB(255, 230, 107, 255),
    Color.fromARGB(255, 255, 102, 186),
    Color.fromARGB(255, 255, 88, 88),
    Color.fromARGB(255, 102, 102, 102),
    Color.fromARGB(255, 252, 114, 0),
    Color.fromARGB(255, 212, 196, 0),
    Color.fromARGB(255, 150, 198, 0),
    Color.fromARGB(255, 20, 200, 20),
    Color.fromARGB(255, 0, 161, 192),
    Color.fromARGB(255, 13, 131, 249),
    Color.fromARGB(255, 129, 61, 255),
    Color.fromARGB(255, 189, 0, 227),
    Color.fromARGB(255, 237, 26, 156),
    Color.fromARGB(255, 244, 26, 26),
    Color.fromARGB(255, 51, 51, 51),
    Color.fromARGB(255, 159, 69, 0),
    Color.fromARGB(255, 142, 131, 0),
    Color.fromARGB(255, 113, 144, 0),
    Color.fromARGB(255, 8, 127, 8),
    Color.fromARGB(255, 0, 105, 123),
    Color.fromARGB(255, 0, 77, 153),
    Color.fromARGB(255, 74, 0, 179),
    Color.fromARGB(255, 123, 0, 148),
    Color.fromARGB(255, 167, 0, 84),
    Color.fromARGB(255, 191, 10, 10),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 88, 39, 0),
    Color.fromARGB(255, 79, 72, 0),
    Color.fromARGB(255, 63, 79, 0),
    Color.fromARGB(255, 0, 71, 0),
    Color.fromARGB(255, 0, 72, 89),
    Color.fromARGB(255, 0, 48, 95),
    Color.fromARGB(255, 47, 7, 102),
    Color.fromARGB(255, 68, 0, 82),
    Color.fromARGB(255, 81, 0, 40),
    Color.fromARGB(255, 94, 0, 0),
  ];

  ///
  /// Gridの線色
  ///
  Color getEmbroideryGridColor() => _gridForBackGroundColor[
      DeviceLibrary().apiBinding.getEmbroideryBackgroundColor().value];
  static const List<Color> _gridForBackGroundColor = [
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 64, 64, 64),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 64, 64, 64),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 64, 64, 64),
    Color.fromARGB(255, 64, 64, 64),
    Color.fromARGB(255, 64, 64, 64),
    Color.fromARGB(255, 64, 64, 64),
    Color.fromARGB(255, 64, 64, 64),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 64, 64, 64),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 100, 100, 100),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 180, 180, 180),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 140, 140, 140),
    Color.fromARGB(255, 140, 140, 140)
  ];

  ///
  /// Frameの線色
  ///
  Color getEmbroideryFrameColor() => _frameForBackGroundColor[
      DeviceLibrary().apiBinding.getEmbroideryBackgroundColor().value];
  static const List<Color> _frameForBackGroundColor = [
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 160, 160, 160),
    Color.fromARGB(255, 160, 160, 160),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 160, 160, 160),
    Color.fromARGB(255, 160, 160, 160),
    Color.fromARGB(255, 160, 160, 160),
    Color.fromARGB(255, 160, 160, 160),
    Color.fromARGB(255, 160, 160, 160),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 160, 160, 160),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 160, 160, 160),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 160, 160, 160),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 160, 160, 160),
    Color.fromARGB(255, 211, 211, 211),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 160, 160, 160),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0),
    Color.fromARGB(255, 0, 0, 0)
  ];

  ///
  /// MMをピクセルに変換(横方向 中心点に基づいています)
  ///
  static double convertXMmToPixelsWithCenter(
          num x, Offset centerPoint, double pixelOfOneMm) =>
      convertMmToPixels(value: x, pixelOfOneMm: pixelOfOneMm) + centerPoint.dx;

  ///
  /// MMをピクセルに変換(縦方向 中心点に基づいています)
  ///
  static double convertYMmToPixelsWithCenter(
          num y, Offset centerPoint, double pixelOfOneMm) =>
      convertMmToPixels(value: y, pixelOfOneMm: pixelOfOneMm) + centerPoint.dy;

  ///
  /// MMをピクセルに変換
  ///
  static double convertMmToPixels(
          {required num value, required double pixelOfOneMm}) =>
      value * pixelOfOneMm / conversionRate;

  ///
  /// 2点間の距離を計算する.
  ///
  static double _calculateDistance(Offset p1, Offset p2) {
    double dx = p2.dx - p1.dx;
    double dy = p2.dy - p1.dy;
    return sqrt(dx * dx + dy * dy);
  }

  ///
  /// 線分からオフセット長だけ離れた平行な線分を作成する.
  ///
  static Line _makeParallelLine(Line line, double offset) {
    double distance = _calculateDistance(line.startPoint, line.endPoint);

    ///  ベクトルV := (vx, vy)を90度回転した単位ベクトルは(-vy/|V|, vx/V|)
    ///  |V|:=ベクトル(vx, vy)の長さ
    double startPointX = line.startPoint.dx -
        (offset * (line.endPoint.dy - line.startPoint.dy) / distance);
    double startPointY = line.startPoint.dy +
        (offset * (line.endPoint.dx - line.startPoint.dx) / distance);
    double endPointX = line.endPoint.dx -
        (offset * (line.endPoint.dy - line.startPoint.dy) / distance);
    double endPointY = line.endPoint.dy +
        (offset * (line.endPoint.dx - line.startPoint.dx) / distance);

    Line lineNew = Line(
      startPoint: Offset(startPointX, startPointY),
      endPoint: Offset(endPointX, endPointY),
    );

    return lineNew;
  }

  ///
  /// 2元1次方程式の解を求める.
  ///  A11*X1 + A12*X2 = x
  ///  A21*X1 + A22*X2 = y
  ///
  static Offset _geoSolve2x2LinearEquation({
    required double a11,
    required double a12,
    required double a21,
    required double a22,
    required double b1,
    required double b2,
  }) {
    double det = a11 * a22 - a12 * a21;

    double x = (a22 * b1 - a12 * b2) / det;
    double y = (-a21 * b1 + a11 * b2) / det;

    return Offset(x, y);
  }

  ///
  /// 2つの直線の交点を求める.
  ///
  static Offset _getLineLineCrossPoint(Line line1, Line line2) =>
      _geoSolve2x2LinearEquation(
        a11: line1.endPoint.dy - line1.startPoint.dy,
        a12: -(line1.endPoint.dx - line1.startPoint.dx),
        a21: line2.endPoint.dy - line2.startPoint.dy,
        a22: -(line2.endPoint.dx - line2.startPoint.dx),
        b1: line1.endPoint.dy * line1.startPoint.dx -
            line1.endPoint.dx * line1.startPoint.dy,
        b2: line2.endPoint.dy * line2.startPoint.dx -
            line2.endPoint.dx * line2.startPoint.dy,
      );

  ///
  /// 赤のドット指定位置の座標を取得するには
  ///
  static RedPointInfo _getRedPointInfo({
    required Offset topLeft,
    required Offset topRight,
    required Offset bottomLeft,
    required Offset bottomRight,
    required PointType pointType,
    required double magnification,
    required PointPositionType redPointPositionType,
  }) {
    double x = 0;
    double y = 0;

    Line line1 = Line(startPoint: topLeft, endPoint: topRight);
    Line line2 = Line(startPoint: topRight, endPoint: bottomRight);
    Line line3 = Line(startPoint: bottomRight, endPoint: bottomLeft);
    Line line4 = Line(startPoint: bottomLeft, endPoint: topLeft);

    /// 内側にオフセットした線分
    Line line1New =
        _makeParallelLine(line1, redPointImageSize / magnification / 2);
    Line line2New =
        _makeParallelLine(line2, redPointImageSize / magnification / 2);
    Line line3New =
        _makeParallelLine(line3, redPointImageSize / magnification / 2);
    Line line4New =
        _makeParallelLine(line4, redPointImageSize / magnification / 2);

    /// 交差点(ドットの中心)
    Offset topLeftNew = _getLineLineCrossPoint(line1New, line4New);
    Offset topRightNew = _getLineLineCrossPoint(line2New, line1New);
    Offset bottomRightNew = _getLineLineCrossPoint(line3New, line2New);
    Offset bottomLeftNew = _getLineLineCrossPoint(line4New, line3New);

    double redPointHalfArea = redPointImageSize / magnification / 2;

    /// アイコン位置に補正(ペイントドット素材の始点位置)
    topLeftNew = Offset(
      topLeftNew.dx - redPointHalfArea,
      topLeftNew.dy - redPointHalfArea,
    );
    topRightNew = Offset(
      topRightNew.dx - redPointHalfArea,
      topRightNew.dy - redPointHalfArea,
    );
    bottomRightNew = Offset(
      bottomRightNew.dx - redPointHalfArea,
      bottomRightNew.dy - redPointHalfArea,
    );
    bottomLeftNew = Offset(
      bottomLeftNew.dx - redPointHalfArea,
      bottomLeftNew.dy - redPointHalfArea,
    );

    switch (redPointPositionType) {
      case PointPositionType.topLeft:
        x = topLeftNew.dx;
        y = topLeftNew.dy;
        break;
      case PointPositionType.topRight:
        x = topRightNew.dx;
        y = topRightNew.dy;
        break;
      case PointPositionType.left:
        x = ((topLeftNew + bottomLeftNew) / 2).dx;
        y = ((topLeftNew + bottomLeftNew) / 2).dy;
        break;
      case PointPositionType.right:
        x = ((topRightNew + bottomRightNew) / 2).dx;
        y = ((topRightNew + bottomRightNew) / 2).dy;
        break;
      case PointPositionType.bottomLeft:
        x = bottomLeftNew.dx;
        y = bottomLeftNew.dy;
        break;
      case PointPositionType.bottomCenter:
        x = ((bottomLeftNew + bottomRightNew) / 2).dx;
        y = ((bottomLeftNew + bottomRightNew) / 2).dy;
        break;
      case PointPositionType.bottomRight:
        x = bottomRightNew.dx;
        y = bottomRightNew.dy;
        break;
      case PointPositionType.topCenter:
        x = ((topRightNew + topLeftNew) / 2).dx;
        y = ((topRightNew + topLeftNew) / 2).dy;
      default:
        break;
    }

    return RedPointInfo(
      topLeft: Offset(x, y),
      pointType: pointType,
      redPointPositionType: redPointPositionType,
    );
  }

  ///
  /// 上、下、左、右の 4 つの制限値を取得します
  ///
  static ({double topMin, double leftMin, double bottomMax, double rightMax})
      getMaxOuterWithMask({
    required Offset maskTopLeft,
    required Offset maskTopRight,
    required Offset maskBottomLeft,
    required Offset maskBottomRight,
  }) {
    double topMin = [
      maskTopLeft.dy,
      maskTopRight.dy,
      maskBottomLeft.dy,
      maskBottomRight.dy,
    ].reduce(
        (currentMin, element) => currentMin < element ? currentMin : element);
    double leftMin = [
      maskTopLeft.dx,
      maskTopRight.dx,
      maskBottomLeft.dx,
      maskBottomRight.dx,
    ].reduce(
        (currentMin, element) => currentMin < element ? currentMin : element);
    double bottomMax = [
      maskTopLeft.dy,
      maskTopRight.dy,
      maskBottomLeft.dy,
      maskBottomRight.dy,
    ].reduce(
        (currentMax, element) => currentMax > element ? currentMax : element);
    double rightMax = [
      maskTopLeft.dx,
      maskTopRight.dx,
      maskBottomLeft.dx,
      maskBottomRight.dx,
    ].reduce(
        (currentMax, element) => currentMax > element ? currentMax : element);

    return (
      topMin: topMin,
      leftMin: leftMin,
      bottomMax: bottomMax,
      rightMax: rightMax
    );
  }

  ///
  /// 上、下、左、右の 4 つの制限値を取得します
  ///
  static ({double topMin, double leftMin, double bottomMax, double rightMax})
      getMaxOuterWithXYList({
    required List<double> xList,
    required List<double> yList,
  }) {
    double topMin = yList.reduce(
        (currentMin, element) => currentMin < element ? currentMin : element);
    double leftMin = xList.reduce(
        (currentMin, element) => currentMin < element ? currentMin : element);
    double bottomMax = yList.reduce(
        (currentMax, element) => currentMax > element ? currentMax : element);
    double rightMax = xList.reduce(
        (currentMax, element) => currentMax > element ? currentMax : element);

    return (
      topMin: topMin,
      leftMin: leftMin,
      bottomMax: bottomMax,
      rightMax: rightMax
    );
  }

  ///
  /// 中心点を基準にして回転した点の新しい位置を計算します
  ///
  static Offset rotatePoint(
      {required Offset point, required double angle, required Offset center}) {
    double theta = angle * (pi / 180.0);

    /// ポイントを原点に移動
    double tempX = point.dx - center.dx;
    double tempY = point.dy - center.dy;

    /// ポイントを回転します
    double rotatedX = tempX * cos(theta) - tempY * sin(theta);
    double rotatedY = tempX * sin(theta) + tempY * cos(theta);

    /// ポイントを戻します
    return Offset(rotatedX + center.dx, rotatedY + center.dy);
  }

  ///
  /// markを取得します
  ///
  static List<ThreadMarkInfo> getThreadMarkInfoList({
    required Offset centerPoint,
    required double pixelOfOneMm,
    required List<PatternDisplayInfo> patternDisplayInfoList,
  }) {
    List<ThreadMarkInfo> threadMarkInfoList = [];

    for (var patternDisplayInfo in patternDisplayInfoList) {
      for (var borderDisplayInfo in patternDisplayInfo.borderDisplayInfoList) {
        Rect uiMask = Rect.fromPoints(
          borderDisplayInfo.maskDisplayInfo.topLeft,
          borderDisplayInfo.maskDisplayInfo.bottomRight,
        );

        /// 「1」、「6」、「8」などの数字は、 maskの各頂点に対する糸印表示位置のオフセットです
        if (borderDisplayInfo.threadMarkState.top) {
          threadMarkInfoList.add(ThreadMarkInfo(
              threadMarkIndex: ThreadPosition.top.number,
              topLeft:
                  Offset(uiMask.topCenter.dx - 6, uiMask.topCenter.dy - 1)));
        }
        if (borderDisplayInfo.threadMarkState.topLeft) {
          threadMarkInfoList.add(ThreadMarkInfo(
              threadMarkIndex: ThreadPosition.topLeft.number,
              topLeft: Offset(uiMask.topLeft.dx - 1, uiMask.topLeft.dy - 1)));
        }
        if (borderDisplayInfo.threadMarkState.topRight) {
          threadMarkInfoList.add(ThreadMarkInfo(
              threadMarkIndex: ThreadPosition.topRight.number,
              topLeft: Offset(uiMask.topRight.dx - 8, uiMask.topRight.dy - 1)));
        }
        if (borderDisplayInfo.threadMarkState.centerLeft) {
          threadMarkInfoList.add(ThreadMarkInfo(
              threadMarkIndex: ThreadPosition.centerLeft.number,
              topLeft:
                  Offset(uiMask.centerLeft.dx - 1, uiMask.centerLeft.dy - 6)));
        }
        if (borderDisplayInfo.threadMarkState.centerRight) {
          threadMarkInfoList.add(ThreadMarkInfo(
              threadMarkIndex: ThreadPosition.centerRight.number,
              topLeft: Offset(
                  uiMask.centerRight.dx - 4, uiMask.centerRight.dy - 6)));
        }
        if (borderDisplayInfo.threadMarkState.bottomLeft) {
          threadMarkInfoList.add(ThreadMarkInfo(
              threadMarkIndex: ThreadPosition.bottomLeft.number,
              topLeft:
                  Offset(uiMask.bottomLeft.dx - 1, uiMask.bottomLeft.dy - 8)));
        }
        if (borderDisplayInfo.threadMarkState.bottomRight) {
          threadMarkInfoList.add(ThreadMarkInfo(
              threadMarkIndex: ThreadPosition.bottomRight.number,
              topLeft: Offset(
                  uiMask.bottomRight.dx - 8, uiMask.bottomRight.dy - 8)));
        }
        if (borderDisplayInfo.threadMarkState.bottom) {
          threadMarkInfoList.add(ThreadMarkInfo(
              threadMarkIndex: ThreadPosition.bottom.number,
              topLeft: Offset(
                  uiMask.bottomCenter.dx - 6, uiMask.bottomCenter.dy - 4)));
        }
      }
    }

    return threadMarkInfoList;
  }

  void reset() {
    isInDragPreviewMode = false;
    uiRotateAngle = 0;
    isRotatingForward = true;
    quiltImage = null;
  }
}

///
/// ドットが存在する可能性のある場所
///
enum PointPositionType {
  topLeft,
  topCenter,
  topRight,
  left,
  right,
  bottomLeft,
  bottomCenter,
  bottomRight
}

///
/// ドットの種類
///
enum PointType {
  rotate,
  size,
}

///
/// 赤い点の表示情報
///
class RedPointInfo {
  const RedPointInfo({
    required this.topLeft,
    required this.pointType,
    required this.redPointPositionType,
  });

  final Offset topLeft;
  final PointType pointType;
  final PointPositionType redPointPositionType;

  bool get isRotatePoint => pointType == PointType.rotate;

  RedPointInfo copyWith({
    Offset? topLeft,
    PointType? pointType,
    PointPositionType? redPointPositionType,
  }) =>
      RedPointInfo(
        topLeft: topLeft ?? this.topLeft,
        pointType: pointType ?? this.pointType,
        redPointPositionType: redPointPositionType ?? this.redPointPositionType,
      );
}

///
/// 赤いドットの描画情報
///
class RedPointDisplayInfo {
  const RedPointDisplayInfo({
    required this.topLeft,
    required this.width,
    required this.height,
    required this.angle,
    required this.redPointImageSize,
    required this.redPointTouchOutArea,
    required this.redPointInfos,
  });

  final Offset topLeft;
  final double width;
  final double height;
  final double angle;
  final double redPointImageSize;

  /// 赤いドットの追加クリック領域
  /// 上下左右に' redPointTouchOutArea'ずつ増やします。
  final double redPointTouchOutArea;
  final List<RedPointInfo> redPointInfos;

  RedPointDisplayInfo copyWith({
    Offset? topLeft,
    double? width,
    double? height,
    double? angle,
    double? redPointImageSize,
    double? redPointTouchOutArea,
    List<RedPointInfo>? redPointInfos,
  }) =>
      RedPointDisplayInfo(
        topLeft: topLeft ?? this.topLeft,
        width: width ?? this.width,
        height: height ?? this.height,
        angle: angle ?? this.angle,
        redPointImageSize: redPointImageSize ?? this.redPointImageSize,
        redPointTouchOutArea: redPointTouchOutArea ?? this.redPointTouchOutArea,
        redPointInfos: redPointInfos ?? this.redPointInfos,
      );
}

///
/// ページ表示EmbPatternで使用されるデータ
///
/// [left] :  PatternはPreview始点座標のX座標に基づいている。
/// [top] : PatternはPreview始点座標のY座標に基づいている。
/// [width] : Patternの総描画幅、Patternサイズ+RedPointの上下左右に広がる範囲
/// [height] :  Patternの総描画高さ、Patternサイズ+RedPointの上下左右に広がる範囲
/// [patternHeight] : PatternのImageの表示幅、[width]と赤点が異なる拡大領域
/// [patternWidth] :  PatternのImageの表示高さ、[height]と赤点が異なる拡大領域
/// [angle] :  Patternの回転角度
/// [mask] : maskは、[left] と[top]に基づいています
/// [imageTop] :  PatternのImage始点座標のY座標に基づいている。
/// [imageLeft] : PatternのImage始点座標のX座標に基づいている。
/// [imageHeight] : PatternのImageの表示幅、[width]と赤点が異なる拡大領域
/// [imageWidth] :  PatternのImageの表示高さ、[height]と赤点が異なる拡大領域
/// [displayImage] : EmbPtrnの画像データ
/// [isSelected] : EmbPtrnの選択状態
/// [isCursorLeft] : カーソルは左側にあります
/// [isCursorLeft] : カーソルは右側にあります
///
class EmbPatternDisplayInfo {
  EmbPatternDisplayInfo({
    required this.left,
    required this.top,
    required this.width,
    required this.height,
    required this.patternHeight,
    required this.patternWidth,
    required this.angle,
    required this.mask,
    required this.imageTop,
    required this.imageLeft,
    required this.imageWidth,
    required this.imageHeight,
    this.displayImage,
    this.isSelected = false,
    this.isCursorLeft = false,
    this.isCursorRight = false,
  });
  double angle;
  double left;
  double top;
  double width;
  double height;
  double patternWidth;
  double patternHeight;
  Mask mask;
  double imageTop;
  double imageLeft;
  double imageWidth;
  double imageHeight;
  Uint8List? displayImage;
  bool isSelected;
  bool isCursorLeft;
  bool isCursorRight;

  Offset get topLeft => Offset(left, top);

  EmbPatternDisplayInfo copyWith({
    double? angle,
    double? left,
    double? top,
    double? width,
    double? height,
    double? patternWidth,
    double? patternHeight,
    Mask? mask,
    double? imageTop,
    double? imageLeft,
    double? imageWidth,
    double? imageHeight,
    Uint8List? displayImage,
    bool? isSelected,
    bool? isCursorLeft,
    bool? isCursorRight,
  }) =>
      EmbPatternDisplayInfo(
        left: left ?? this.left,
        top: top ?? this.top,
        width: width ?? this.width,
        height: height ?? this.height,
        patternHeight: patternHeight ?? this.patternHeight,
        patternWidth: patternWidth ?? this.patternWidth,
        angle: angle ?? this.angle,
        mask: mask ?? this.mask,
        imageTop: imageTop ?? this.imageTop,
        imageLeft: imageLeft ?? this.imageLeft,
        imageWidth: imageWidth ?? this.imageWidth,
        imageHeight: imageHeight ?? this.imageHeight,
        displayImage: displayImage ?? this.displayImage,
        isSelected: isSelected ?? this.isSelected,
        isCursorLeft: isCursorLeft ?? this.isCursorLeft,
        isCursorRight: isCursorRight ?? this.isCursorRight,
      );
}

///
/// ページ表示Groupで使用されるデータ
///
/// [sewingIndex] :  パタンの縫い順
/// [isSelected] :  Patternの選択状態
/// [isCurrentPattern] :  現在選択されているPatternがないかどうか
/// [left] :  PatternはPreview始点座標のX座標に基づいている。
/// [top] : PatternはPreview始点座標のY座標に基づいている。
/// [width] : Patternの総描画幅、Patternサイズ+RedPointの上下左右に広がる範囲
/// [height] :  Patternの総描画高さ、Patternサイズ+RedPointの上下左右に広がる範囲
/// [angle] :  Patternの回転角度
/// [mask] : maskは、[left] と[top]に基づいています
/// [arcImage] : 円弧下線イメージ
/// [arcImageOffset] : 円弧下線イメージの移動量
///
class EmbGroupDisplayInfo {
  EmbGroupDisplayInfo({
    required this.sewingIndex,
    required this.isSelected,
    required this.isCurrentPattern,
    required this.left,
    required this.top,
    required this.width,
    required this.height,
    required this.angle,
    required this.mask,
    required this.isAllNotSewing,
    required this.isFont,
    required this.embPatternDisplayInfoList,
    this.arcImage,
    this.arcImageOffset = Offset.zero,
  });
  int sewingIndex;
  bool isSelected;
  bool isCurrentPattern;
  double angle;
  double left;
  double top;
  double width;
  double height;
  Uint8List? arcImage;
  Mask mask;
  bool isAllNotSewing;
  bool isFont;
  List<EmbPatternDisplayInfo> embPatternDisplayInfoList;
  Offset arcImageOffset;
  Offset get topLeft => Offset(left, top);

  EmbGroupDisplayInfo copyWith({
    int? sewingIndex,
    bool? isSelected,
    bool? isCurrentPattern,
    double? angle,
    double? left,
    double? top,
    double? width,
    double? height,
    Uint8List? arcImage,
    Mask? mask,
    bool? isAllNotSewing,
    bool? isFont,
    List<EmbPatternDisplayInfo>? embPatternDisplayInfoList,
    Offset? arcImageOffset,
  }) =>
      EmbGroupDisplayInfo(
        sewingIndex: sewingIndex ?? this.sewingIndex,
        isSelected: isSelected ?? this.isSelected,
        isCurrentPattern: isCurrentPattern ?? this.isCurrentPattern,
        left: left ?? this.left,
        top: top ?? this.top,
        width: width ?? this.width,
        height: height ?? this.height,
        angle: angle ?? this.angle,
        mask: mask ?? this.mask,
        isAllNotSewing: isAllNotSewing ?? this.isAllNotSewing,
        isFont: isFont ?? this.isFont,
        embPatternDisplayInfoList:
            embPatternDisplayInfoList ?? this.embPatternDisplayInfoList,
        arcImage: arcImage ?? this.arcImage,
        arcImageOffset: arcImageOffset ?? this.arcImageOffset,
      );
}

///
/// ページ表示Borderで使用されるデータ
/// [sewingIndex] :  パタンの縫い順
/// [isSelected] :  Borderの選択状態
/// [isCurrentPattern] :  現在選択されているBorderがないかどうか
/// [isBaseBorder] :
/// [left] :  BorderはPreview始点座標のX座標に基づいている。
/// [top] : BorderはPreview始点座標のY座標に基づいている。
/// [width] : Borderの総描画幅、Borderサイズ+RedPointの上下左右に広がる範囲
/// [height] :  Borderの総描画高さ、Borderサイズ+RedPointの上下左右に広がる範囲
///
class EmbBorderDisplayInfo {
  EmbBorderDisplayInfo({
    required this.sewingIndex,
    required this.isSelected,
    required this.isCurrentPattern,
    required this.isBaseBorder,
    required this.left,
    required this.top,
    required this.width,
    required this.height,
    required this.maskDisplayInfo,
    required this.baseMaskInfo,
    required this.groupDisplayInfoList,
    this.threadMarkState = const ThreadMarkState(
        top: false,
        topLeft: false,
        topRight: false,
        centerLeft: false,
        centerRight: false,
        bottomLeft: false,
        bottomRight: false,
        bottom: false),
  });

  int sewingIndex;
  bool isSelected;
  bool isCurrentPattern;
  bool isBaseBorder;
  double left;
  double top;
  double width;
  double height;
  Mask maskDisplayInfo;
  Mask baseMaskInfo;
  ThreadMarkState threadMarkState;
  List<EmbGroupDisplayInfo> groupDisplayInfoList;

  Offset get topLeft => Offset(left, top);

  bool get hasMark =>
      threadMarkState.top ||
      threadMarkState.topLeft ||
      threadMarkState.topRight ||
      threadMarkState.centerLeft ||
      threadMarkState.centerRight ||
      threadMarkState.bottom ||
      threadMarkState.bottomLeft ||
      threadMarkState.bottomRight;

  EmbBorderDisplayInfo copyWith({
    int? sewingIndex,
    bool? isSelected,
    bool? isCurrentPattern,
    bool? isBaseBorder,
    double? left,
    double? top,
    double? width,
    double? height,
    Mask? maskDisplayInfo,
    Mask? baseMaskInfo,
    ThreadMarkState? threadMarkState,
    List<EmbGroupDisplayInfo>? groupDisplayInfoList,
  }) =>
      EmbBorderDisplayInfo(
        sewingIndex: sewingIndex ?? this.sewingIndex,
        isSelected: isSelected ?? this.isSelected,
        isCurrentPattern: isCurrentPattern ?? this.isCurrentPattern,
        isBaseBorder: isBaseBorder ?? this.isBaseBorder,
        left: left ?? this.left,
        top: top ?? this.top,
        width: width ?? this.width,
        height: height ?? this.height,
        maskDisplayInfo: maskDisplayInfo ?? this.maskDisplayInfo,
        baseMaskInfo: baseMaskInfo ?? this.baseMaskInfo,
        threadMarkState: threadMarkState ?? this.threadMarkState,
        groupDisplayInfoList: groupDisplayInfoList ?? this.groupDisplayInfoList,
      );
}

///
/// ページ表示patternで使用されるデータ
/// [left] :  patternはPreview始点座標のX座標に基づいている。
/// [top] : patternはPreview始点座標のY座標に基づいている。
/// [width] : patternの総描画幅、patternサイズ+RedPointの上下左右に広がる範囲
/// [height] :  patternの総描画高さ、patternサイズ+RedPointの上下左右に広がる範囲
/// [isGroup] : patternがGroupかどうか
/// [isSelected] :  patternの選択状態
/// [isCurrentPattern] :  現在選択されているpatternがないかどうか
///
class PatternDisplayInfo {
  PatternDisplayInfo({
    required this.left,
    required this.top,
    required this.width,
    required this.height,
    required this.isGroup,
    required this.isSelected,
    required this.isCurrentPattern,
    required this.maskDisplayInfo,
    required this.style,
    required this.space,
    required this.borderDisplayInfoList,
  });

  double left;
  double top;
  double width;
  double height;
  bool isSelected;
  bool isCurrentPattern;
  bool isGroup;
  Mask maskDisplayInfo;
  SSPoint style;
  SSPoint space;
  List<EmbBorderDisplayInfo> borderDisplayInfoList;

  bool get isBorder => isGroup == false;
  Offset get topLeft => Offset(left, top);

  PatternDisplayInfo copyWith({
    double? left,
    double? width,
    double? top,
    double? height,
    bool? isSelected,
    bool? isGroup,
    bool? isCurrentPattern,
    Mask? maskDisplayInfo,
    SSPoint? style,
    SSPoint? space,
    List<EmbBorderDisplayInfo>? borderDisplayInfoList,
  }) =>
      PatternDisplayInfo(
        left: left ?? this.left,
        top: top ?? this.top,
        width: width ?? this.width,
        height: height ?? this.height,
        isGroup: isGroup ?? this.isGroup,
        isSelected: isSelected ?? this.isSelected,
        isCurrentPattern: isCurrentPattern ?? this.isCurrentPattern,
        maskDisplayInfo: maskDisplayInfo ?? this.maskDisplayInfo,
        style: style ?? this.style,
        space: space ?? this.space,
        borderDisplayInfoList:
            borderDisplayInfoList ?? this.borderDisplayInfoList,
      );
}

///
/// ページ上の糸印の表示情報
///
class ThreadMarkInfo {
  ThreadMarkInfo({
    required this.topLeft,
    required this.threadMarkIndex,
  });
  final Offset topLeft;
  final int threadMarkIndex;

  ThreadMarkInfo copyWith({
    Offset? topLeft,
    int? threadMarkIndex,
  }) =>
      ThreadMarkInfo(
        topLeft: topLeft ?? this.topLeft,
        threadMarkIndex: threadMarkIndex ?? this.threadMarkIndex,
      );
}

///
/// 1本の線の定義
///
class Line {
  Line({
    required this.startPoint,
    required this.endPoint,
  });
  final Offset startPoint;
  final Offset endPoint;

  Line copyWith({
    Offset? startPoint,
    Offset? endPoint,
  }) =>
      Line(
        startPoint: startPoint ?? this.startPoint,
        endPoint: endPoint ?? this.endPoint,
      );
}

///
/// 四角形の定義
///
class Mask {
  Mask({
    required this.topLeft,
    required this.topRight,
    required this.bottomLeft,
    required this.bottomRight,
  });
  final Offset topLeft;
  final Offset topRight;
  final Offset bottomLeft;
  final Offset bottomRight;

  Mask copyWith({
    Offset? topLeft,
    Offset? topRight,
    Offset? bottomLeft,
    Offset? bottomRight,
  }) =>
      Mask(
        topLeft: topLeft ?? this.topLeft,
        topRight: topRight ?? this.topRight,
        bottomLeft: bottomLeft ?? this.bottomLeft,
        bottomRight: bottomRight ?? this.bottomRight,
      );
}

///
/// 画像を結合するときに使用する構造体
///
class TopLeftAndSize {
  TopLeftAndSize({
    required this.topLeft,
    required this.size,
  });
  final Offset topLeft;
  final Offset size;

  TopLeftAndSize copyWith({
    Offset? topLeft,
    Offset? size,
  }) =>
      TopLeftAndSize(
        topLeft: topLeft ?? this.topLeft,
        size: size ?? this.size,
      );
}
