import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../model/resume_history_model.dart';
import '../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../model/stitch/line_running_model.dart';
import '../../../../../../../model/stitch/surface_tatami_model.dart';
import '../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../stitch_page_view_model.dart';
import 'under_sewing_setting_popup_view_interface.dart';

final underSewingSettingPopupViewModelProvider =
    StateNotifierProvider.autoDispose<UnderSewingSettingPopupStateViewInterface,
            UnderSewingSettingPopupState>(
        (ref) => UnderSewingSettingPopupViewModel(ref));

class UnderSewingSettingPopupViewModel
    extends UnderSewingSettingPopupStateViewInterface {
  UnderSewingSettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const UnderSewingSettingPopupState(), ref) {
    _underSewingState = LineRunningModel().getUnderSewing() !=
            LineRunningModel.underSewingNotUpdating
        ? TatamiSettingState.settingCompleted
        : TatamiSettingState.unknown;
    update();
  }

  ///
  /// 縫い縮み設定(ステッチの長さ補正)の状態
  ///
  TatamiSettingState _underSewingState = TatamiSettingState.settingCompleted;

  ///
  /// 下縫い設定値
  ///
  MDCIsOnOff _underSewingValue = LineRunningModel().getUnderSewing();

  @override
  void update() {
    state = state.copyWith(
      isOnButtonSelected: _underSewingValue == MDCIsOnOff.mdcIs_on,
      isOFFButtonSelected: _underSewingValue == MDCIsOnOff.mdcIs_off,
    );
  }

  @override
  void onOkButtonClicked(BuildContext context) {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineRunningUnderSewing.toString());
    if (_underSewingState == TatamiSettingState.unknown) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    if (state.isOnButtonSelected == true) {
      _underSewingValue = MDCIsOnOff.mdcIs_on;
    } else {
      _underSewingValue = MDCIsOnOff.mdcIs_off;
    }

    /// UnderSewingの初期値
    MDCIsOnOff oldUnderSewingValue = LineRunningModel().getUnderSewing();

    /// Model更新
    /// ToDo: 既存問題、一緒に修正必要。
    LineRunningModel().setUnderSewing(_underSewingValue);
    if (LineRunningModel().setMdcRunningStitchSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    if (_underSewingValue != oldUnderSewingValue) {
      ResumeHistoryModel().backSnapshot();
    } else {
      /// do nothing
    }

    CreationModel().changeStitchCreation();
  }

  @override
  void onONButtonClicked() {
    if (state.isOnButtonSelected) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    _underSewingValue = MDCIsOnOff.mdcIs_on;
    _underSewingState = TatamiSettingState.change;

    /// view更新
    state = state.copyWith(
      isOnButtonSelected: true,
      isOFFButtonSelected: false,
    );
  }

  @override
  void onOFFButtonClicked() {
    if (state.isOFFButtonSelected) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    _underSewingValue = MDCIsOnOff.mdcIs_off;
    _underSewingState = TatamiSettingState.change;

    /// view更新
    state = state.copyWith(
      isOnButtonSelected: false,
      isOFFButtonSelected: true,
    );
  }
}
