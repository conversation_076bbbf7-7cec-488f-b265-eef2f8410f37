import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'pull_compensation_setting_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class PullCompensationState with _$PullCompensationState {
  const factory PullCompensationState({
    @Default("3") String pullCompensationValue,
    @Default(false) bool isMaxValue,
    @Default(false) bool isMainValue,
  }) = _PullCompensationState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class PullCompensationStateViewInterface
    extends ViewModel<PullCompensationState> {
  PullCompensationStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress);

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;

  ///
  ///  縫い縮み設定(ステッチの長さ補正)表示テキストスタイルを取得します
  ///
  bool getPullCompensationTextStyle();
}
