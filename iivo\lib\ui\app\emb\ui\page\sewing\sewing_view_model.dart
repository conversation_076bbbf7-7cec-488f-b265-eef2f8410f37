import 'dart:async';
import 'dart:ffi';

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/projector_model.dart';
import '../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../model/sewing_global_model.dart';
import '../../../../../../network/server/http/route/my_stitch_monitoring/common.dart';
import '../../../../../../network/server/http/route/my_stitch_monitoring/emb_image.dart';
import '../../../../../../network/server/http/route/my_stitch_monitoring/emb_prog_image.dart';
import '../../../../../global_popup/global_popups/err_sewing_over/err_sewing_over_view_model.dart'
    show errSewingOverFunc, hideSewingPageBackgroundScanFunc;
import '../../../../../global_popup/global_popups/err_sewing_over_mark_pat_cnct/err_sewing_over_mark_pat_cnct_view_model.dart'
    show errSewingOverMarkPatCnctCancelFunc;
import '../../../model/color_progress_bar_model.dart';
import '../../../model/connect_sewing_model.dart';
import '../../../model/layout_menu_model.dart';
import '../../../model/pattern_model.dart';
import '../../../model/scan_model.dart';
import '../../../model/sewing_model.dart';
import '../../../model/snowman_model.dart';
import '../../component/emb_header/emb_header_view_model.dart';
import '../../component/stop_sewing/stop_sewing_view_model.dart';
import '../common_component/background_scan/background_scan_view_model.dart';
import '../common_component/function_provider/projector_function_provider/projector_function_provider.dart';
import 'bottom/bottom_view_model.dart';
import 'bottom/stitch_forward_backward/stitch_forward_backward_view_model.dart';
import 'component/color_progress_bar/color_progress_bar_view_model.dart';
import 'preview/preview_view_model.dart';
import 'sewing_view_interface.dart';
import 'tool_bar/component/layout_menu_popup/layout_menu_popup.dart';
import 'tool_bar/component/layout_menu_popup/layout_menu_popup_view_model.dart';
import 'tool_bar/component/layout_menu_popup/layout_move/layout_move.dart';
import 'tool_bar/component/layout_menu_popup/layout_move/layout_move_view_model.dart';
import 'tool_bar/component/layout_menu_popup/quilt_edge_to_edge/quilt_move/quilt_move.dart';
import 'tool_bar/component/layout_menu_popup/quilt_edge_to_edge/quilt_move/quilt_move_view_model.dart';
import 'tool_bar/component/layout_menu_popup/quilt_edge_to_edge/quilt_rotate/quilt_rotate.dart';
import 'tool_bar/component/layout_menu_popup/quilt_edge_to_edge/quilt_rotate/quilt_rotate_view_model.dart';
import 'tool_bar/component/layout_menu_popup/quilt_sash_one_direction/move/quilt_move.dart'
    as one_direction_move;
import 'tool_bar/component/layout_menu_popup/quilt_sash_one_direction/move/quilt_move_view_model.dart'
    as one_direction_move_view_model;
import 'tool_bar/component/layout_menu_popup/quilt_sash_one_direction/rotate/quilt_rotate.dart'
    as one_direction_rotate;
import 'tool_bar/component/layout_menu_popup/quilt_sash_one_direction/rotate/quilt_rotate_view_model.dart'
    as one_direction_rotate_view_model;
import 'tool_bar/component/layout_menu_popup/rotate/rotate.dart';
import 'tool_bar/component/layout_menu_popup/rotate/rotate_view_model.dart';
import 'tool_bar/component/layout_menu_popup/snowman/snowman.dart';
import 'tool_bar/component/layout_menu_popup/snowman/snowman_view_model.dart';
import 'tool_bar/tool_bar_view_model.dart';
import 'top_bar/top_bar_view_model.dart';

final sewingViewInfoProvider =
    AutoDisposeNotifierProvider<_SewingViewInfo, void>(() => _SewingViewInfo());

class _SewingViewInfo extends AutoDisposeNotifier<void> {
  @override
  void build() {}

  BuildContext? _context;
  BuildContext get context => _context!;
  set context(value) => _context = value;
}

final sewingProvider =
    StateNotifierProvider.autoDispose<SewingViewModel, SewingState>((ref) {
  final context = ref.read(sewingViewInfoProvider.notifier).context;
  return SewingViewModel(ref, context);
});

class SewingViewModel extends SewingViewModelInterface
    with DeviceLibraryEventObserver {
  SewingViewModel(AutoDisposeStateNotifierProviderRef ref, BuildContext context)
      : super(const SewingState(), ref, context) {
    /// Model層データ初期化する
    SewingModel().reset();
    LayoutMenuModel().reset();
    ColorProgressBarModel().reset();
    SewingGlobalModel().isInSewingPage = true;

    if (SewingModel().isAutoOneDirection()) {
      ProjectorModel()
          .embProjector
          .openEmbProjector(initType: ProjectorInitType.autoPatCnt);
    }

    /// MyStitchMonitor App接続の場合
    /// 1. Monitoring  machine status初期化
    /// 2. "/embinfo"返信でーたを全て更新する
    initMonitoringEmbStatus();
    updateMonitoringEmbInfoWithColor();

    /// MyStitchMonitor App接続の場合
    /// statusのhoopを更新する
    updateMonitoringStatusHoop();
    _startEmbProgImageTimer();

    _initUiState();
    _openSewingListen();

    /// 以下理解でここは Futureを使って、他のViewModelを後に更新する
    Future(() {
      errSewingOverFunc = () {
        PatternModel().reloadAllPattern();
        updateSewingByChild(ModuleType.toolbar);
      };

      /// すべての縫製が完了した後、背景画像を閉じてください
      hideSewingPageBackgroundScanFunc = () {
        ScanModel().cleanBackgroundImageWhenFinishSewing();
        updateSewingByChild(ModuleType.scan);
      };
      errSewingOverMarkPatCnctCancelFunc = () {
        updateSewingByChild(ModuleType.toolbar);
      };
    });
  }

  @override
  void onEmbFrameChange() {
    super.onEmbFrameChange();

    /// MyStitchMonitor App接続の場合
    /// statusのhoopを更新する
    updateMonitoringStatusHoop();
  }

  @override
  void onPressFootStatusChange() {
    /// 押え金を押し下げたときに W 押え金プロジェクターの電源を切ります
    if (
        // DeviceLibrary().apiBinding.isPressFootDown() == true &&
        ProjectorModel().embProjector.isEmbWFooterProjectorOpen() == true) {
      ProjectorModel().embProjector.closeEmbProjector();
    } else {
      /// Do Nothing
    }
  }

  @override
  void popSnowManPopup() {
    if (ref.exists(snowmanViewModelProvider) == true) {
      PopupNavigator.popUntil(
          context: context, nextRouteName: PopupEnum.snowman);
      PopupNavigator.pop(context: context);
    } else {
      Log.errorTrace("snowmanのポップアップウィンドウが表示されない");
    }
  }

  @override
  void positionAreaOverErrorRePushSnowManPopup() {
    if (ref.exists(snowmanViewModelProvider) != true) {
      EmbLibraryError error = EmbLibrary().apiBinding.embSewingSnowmanStartOk();
      if (error != EmbLibraryError.EMB_NO_ERR) {
        return;
      }
      final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
      if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
        return;
      }

      /// Model更新
      SnowmanModel().isSnowManPopupOpen = true;
      PatternModel().reloadAllPattern();

      /// View更新
      PopupNavigator.pushNamed(
              context: context, nextRouteName: PopupEnum.snowman)
          .then((_) => SnowmanModel().isSnowManPopupOpen = false);
      ref.read(sewingProvider.notifier).updateSewingByChild(ModuleType.toolbar);
    } else {
      /// Do noting
    }
  }

  @override
  void updateSewingByChild(ModuleType vm) {
    switch (vm) {
      case ModuleType.topBar:
        ref.read(bottomProvider.notifier).update();
        ref.read(toolBarViewModelProvider.notifier).update();
        break;
      case ModuleType.toolbar:

        /// ポップアップ状態の取得
        ref.read(bottomProvider.notifier).update();
        ref.read(topBarViewModelProvider.notifier).update();
        if (ref.exists(layoutMenuViewModelProvider)) {
          ref.read(layoutMenuViewModelProvider.notifier).update();
        }
        ref.read(previewViewModelProvider.notifier).update();
        ref.read(toolBarViewModelProvider.notifier).update();
        ref.read(colorProgressBarViewModelProvider.notifier).update();

        /// Bottom表示設定する
        state = state.copyWith(
            isDisplayBottom:
                SewingModel().toolbarPopupId == ToolbarPopupId.none ||
                    SewingModel().toolbarPopupId == ToolbarPopupId.menu);
        break;
      case ModuleType.bottom:
        ref.read(toolBarViewModelProvider.notifier).update();
        ref.read(topBarViewModelProvider.notifier).update();
        ref.read(previewViewModelProvider.notifier).update();
        break;
      case ModuleType.colorProgressBar:
        ref.read(topBarViewModelProvider.notifier).update();
        ref.read(stitchForwardBackwardProvider.notifier).update();
        ref.read(colorProgressBarViewModelProvider.notifier).update();
        break;
      case ModuleType.preview:
        ref.read(toolBarViewModelProvider.notifier).update();
        ref.read(embSewingRotateViewModelProvider.notifier).update();
        ref.read(layoutMoveViewModelProvider.notifier).update();

        /// プロジェクト投影画面を更新する
        final embProjectorFunction =
            ref.read(embProjectorFunctionProvider.notifier);
        embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
        break;
      case ModuleType.projector:
        ref.read(previewViewModelProvider.notifier).update();
        ref.read(bottomProvider.notifier).update();
        ref.read(toolBarViewModelProvider.notifier).update();
        ref.read(topBarViewModelProvider.notifier).update();
        if (ref.exists(layoutMenuViewModelProvider)) {
          ref.read(layoutMenuViewModelProvider.notifier).update();
        }
        break;

      /// 背景スキャン
      case ModuleType.scan:
        ref.read(previewViewModelProvider.notifier).update();
        ref.readAutoNotifierIfExists(backgroundScanViewModelProvider)?.update();
        if (SewingModel().toolbarPopupId == ToolbarPopupId.move) {
          ref.read(layoutMoveViewModelProvider.notifier).update();
        } else if (SewingModel().toolbarPopupId ==
            ToolbarPopupId.quiltEdgeToEdgeMove) {
          ref.read(quiltMoveViewModelProvider.notifier).update();
        } else if (SewingModel().toolbarPopupId ==
            ToolbarPopupId.quiltSashMove) {
          ref
              .read(one_direction_move_view_model
                  .quiltMoveViewModelProvider.notifier)
              .update();
        } else if (SewingModel().toolbarPopupId == ToolbarPopupId.rotate) {
          ref.read(embSewingRotateViewModelProvider.notifier).update();
        } else if (SewingModel().toolbarPopupId ==
            ToolbarPopupId.quiltEdgeToEdgeRotate) {
          ref.read(quiltRotateViewModelProvider.notifier).update();
        } else if (SewingModel().toolbarPopupId ==
            ToolbarPopupId.quiltSashRotate) {
          ref
              .read(one_direction_rotate_view_model
                  .quiltRotateViewModelProvider.notifier)
              .update();
        } else {
          ref.read(layoutMenuViewModelProvider.notifier).update();
        }
        break;

      case ModuleType.layoutCamera:
        ref.read(embHeaderViewModelProvider.notifier).update();
        ref.read(previewViewModelProvider.notifier).update();
        break;

      case ModuleType.rotate:
        ref.read(previewViewModelProvider.notifier).updateByRotateLongPress();
        break;
      case ModuleType.beforeRotate:
        ref
            .read(previewViewModelProvider.notifier)
            .backupPatternDisplayInfoInRotate();
        break;
      case ModuleType.rotateRedPoint:
        if (SewingModel().toolbarPopupId == ToolbarPopupId.rotate) {
          ref
              .read(embSewingRotateViewModelProvider.notifier)
              .updateByRotateLongPress();
        } else {
          /// Do nothing
        }
        break;
      default:
        break;
    }

    /// toolbarが展開する画面では、単色設定設定を変更した直後には通知されません
    /// そのため、toolbarの変更はtoolbarのupdateで個別に処理されます
    if (vm != ModuleType.toolbar) {
      /// MyStitchMonitor App接続の場合
      /// 1.刺しゅう模様（全体マスク）left,top,right,bottomの座標。
      /// 2.刺しゅう模様の全針数。総縫製時間(分)
      /// 3.単色設定
      /// を更新する
      updateMonitoringEmbInfo();

      /// 現在の、縫製済みの針数。縫製経過時間。<分>.縫製中の色順 更新
      updateMonitoringEmbStatus();
    }
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup() => {
        PopupEnum.move.toString(): PopupRouteBuilder(
          builder: (context) => const LayoutMovePopup(),
          barrier: false,
        ),
        PopupEnum.menu.toString(): PopupRouteBuilder(
          builder: (context) => const LayoutMenuPopup(),
          barrier: false,
        ),
        PopupEnum.rotate.toString(): PopupRouteBuilder(
          builder: (context) => const Rotate(),
          barrier: false,
        ),
        PopupEnum.snowman.toString(): PopupRouteBuilder(
          builder: (context) => const Snowman(),
          barrier: false,
        ),
        PopupEnum.quiltEdgeToEdgeMove.toString(): PopupRouteBuilder(
          builder: (context) => const QuiltMove(),
          barrier: false,
        ),
        PopupEnum.quiltEdgeToEdgeRotate.toString(): PopupRouteBuilder(
          builder: (context) => const QuiltRotate(),
          barrier: false,
        ),
        PopupEnum.quiltSashMove.toString(): PopupRouteBuilder(
          builder: (context) => const one_direction_move.QuiltMove(),
          barrier: false,
        ),
        PopupEnum.quiltSashRotate.toString(): PopupRouteBuilder(
          builder: (context) => const one_direction_rotate.QuiltRotate(),
          barrier: false,
        ),
      };

  @override
  void dispose() {
    super.dispose();
    SewingModel().toolbarPopupId = ToolbarPopupId.none;
    SewingGlobalModel().isInSewingPage = false;
    EmbPngImage.reset();
    _stopEmbProgImageTimer();
    DrawProgPngImage().reset();
    EmbProgPngImage.reset();
    SewingModel().reset();
    popSnowManPopup();
    hideSewingPageBackgroundScanFunc = null;
    errSewingOverFunc = null;
    errSewingOverMarkPatCnctCancelFunc = null;
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// 内部で使われる関数群
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// ページの状態を初期化する
  ///
  void _initUiState() {
    final embSewingAttrib = ref.read(appDisplayEmbStateProvider
        .select((value) => value.embSewingAttrib.ref));
    state = state.copyWith(
      isDisplayConnectSewMark: embSewingAttrib.connectSewMark,
    );
  }

  ///
  /// 縫製情報データ変更監視がONになっている
  ///
  void _openSewingListen() {
    /// 縫製開始
    ref.stateListen(
      appDisplayEmbStateProvider
          .select((value) => value.embSetting.ref.isEmbSewing),
      (_, nextState) {
        Log.debugTrace('isEmbSewing $nextState');
        if (nextState == true) {
          /// 縫製開始時に投影をオフにします。
          if (CameraProjectLibrary().apiBinding.isProjectorBackLightOn()) {
            CameraProjectLibrary().apiBinding.projectorOff();
          } else {
            /// Do nothing
          }
        } else {
          /// 縫製が停止した際、特殊な状況では投影を再度オンにします。
          /// 縫製が終わったら、再度プロジェクションをONにする必要はありません。
          if (SewingModel().isAutoOneDirection() &&
              TpdLibrary().apiBinding.bpIFGetError().errorCode !=
                  ErrCode_t.ERR_SEWING_OVER_MARK_PAT_CNCT.index) {
            Log.debugTrace('setNoMaskDrawKindAndRefreshProjectorUI start');
            SewingModel().setNoMaskDrawKindAndRefreshProjectorUI().then(
                (value) => Log.debugTrace(
                    'setNoMaskDrawKindAndRefreshProjectorUI end'));
          } else {
            Log.debugTrace('The projection on condition is not met');
          }
        }
      },
    );

    /// 主軸が動作している時にクリックすると、縫製がキャンセルされます。
    ref.stateListen(
      appDisplayGlobalStateProvider.select((value) => value.isStopingMotor),
      (previous, nextState) {
        Log.debugTrace('isStopingMotor $nextState');
        if (previous == true && nextState == false) {
          /// 縫製が始まったら、ストップマスクを開けます
          ref
              .read(stopSewingPopupViewModelProvider.notifier)
              .openStopSewingPopup(context);
        } else {
          /// 縫製停止、クローズ停止マスキング
          ref
              .read(stopSewingPopupViewModelProvider.notifier)
              .closeStopSewingPopup();
        }
      },
    );

    /// 画面を閉じる際、StopSewingが確実に閉じるようにする
    ref.onDispose(() => ref
        .read(stopSewingPopupViewModelProvider.notifier)
        .closeStopSewingPopup());

    /// isStopingFrame が false -> true に変化したタイミングでもプロジェクター投影内容を1度更新する
    /// 　①1色目が縫製完了(isEmbSewing が true -> false)
    ///     →アプリがプロジェクター投影開始
    ///   ②色替えポップアップ表示
    ///   ③次の色の縫製開始位置に枠が移動開始(isStopingFrame が true -> false )
    ///   ④次の色の縫製開始位置に枠が移動完了(isStopingFrame が false -> true )
    ref.stateListen(
      appDisplayGlobalStateProvider.select((value) => value.isStopingFrame),
      (previous, nextState) {
        final connectSewingModel = ConnectSewingModel();

        if (previous = true &&
            nextState == false &&
            connectSewingModel.projectorRefreshInOneColorState ==
                ProjectorRefreshInOneColorState.oneColorSewingEnd) {
          /// ステータスが値と一致する場合、ステートは次のステートに切り替わります。
          connectSewingModel.changeToNextState();
        } else if (previous == false &&
            nextState == true &&
            connectSewingModel.projectorRefreshInOneColorState ==
                ProjectorRefreshInOneColorState.frameMoveStart &&
            SewingModel().isAutoOneDirection()) {
          /// ステータスが値と一致する場合、ステートは次のステートに切り替わります。
          connectSewingModel.changeToNextState();

          Log.debugTrace('setNoMaskDrawKindAndRefreshProjectorUI start');
          SewingModel().setNoMaskDrawKindAndRefreshProjectorUI().then((value) =>
              Log.debugTrace('setNoMaskDrawKindAndRefreshProjectorUI end'));
        } else {
          /// Do nothing
        }
      },
    );

    /// つなぎマークを表示するか？
    ref.stateListen(
      appDisplayEmbStateProvider
          .select((value) => value.embSewingAttrib.ref.connectSewMark),
      (_, nextState) {
        state = state.copyWith(isDisplayConnectSewMark: nextState);
      },
    );

    /// キルトの編集モード
    ref.stateListen(
      fireImmediately: true,
      appDisplayEmbStateProvider
          .select((value) => value.embSewingAttrib.ref.quirEditMode),
      (_, nextState) {
        Future(() {
          EmbSewingScr embSewingScr = EmbSewingScr.values[nextState];
          PopupEnum? popupEnum;
          ToolbarPopupId popupId = SewingModel().toolbarPopupId;

          switch (embSewingScr) {
            case EmbSewingScr.DistanceQuilt:
              popupEnum = PopupEnum.quiltSashMove;
              popupId = ToolbarPopupId.quiltSashMove;
              break;
            case EmbSewingScr.RotateQuilt:
              popupEnum = PopupEnum.quiltSashRotate;
              popupId = ToolbarPopupId.quiltSashRotate;
              break;
            case EmbSewingScr.DistanceEdgeQuilt:
              popupEnum = PopupEnum.quiltEdgeToEdgeMove;
              popupId = ToolbarPopupId.quiltEdgeToEdgeMove;
              break;
            case EmbSewingScr.RotateEdgeQuilt:
              popupEnum = PopupEnum.quiltEdgeToEdgeRotate;
              popupId = ToolbarPopupId.quiltEdgeToEdgeRotate;
              break;
            case EmbSewingScr.OtherScr:
            case EmbSewingScr.DistanceScr:
            case EmbSewingScr.RotateScr:
            default:
              break;
          }

          if (popupEnum != null) {
            SewingModel().toolbarPopupId = popupId;

            PopupNavigator.pushNamed(
              context: context,
              nextRouteName: popupEnum,
            );

            /// view更新
            state = state.copyWith(isDisplayBottom: false);
            updateSewingByChild(ModuleType.toolbar);
          } else {
            /// Do nothing
          }
        });
      },
    );
  }

  @override
  bool updateSewingByMoveButton(SewingMoveType vm) {
    switch (vm) {
      case SewingMoveType.beforeMove:
        ref
            .read(previewViewModelProvider.notifier)
            .backupPatternDisplayInfoInMove();
        return false;
      case SewingMoveType.moveArrowKey:
        return ref.read(previewViewModelProvider.notifier).moveByArrowKey();
      case SewingMoveType.moveCenter:
        ref.read(previewViewModelProvider.notifier).update();
        return false;
    }
  }

  /// 刺繍中の画像を定期的に生成するTimer
  Timer? _updateEmbProgImageTimer;

  ///
  /// タイマーを終わります
  /// 使用場合：Swing中場合
  ///
  void _startEmbProgImageTimer() {
    /// タイマーの時間間隔 3s
    const int intervalTime = 3;
    _updateEmbProgImageTimer?.cancel();
    _updateEmbProgImageTimer = null;

    _updateEmbProgImageTimer =
        LoopTimer.periodic(const Duration(seconds: intervalTime), (_) async {
      /// EmbProgPngImageのデータ更新
      await drawEmbProgSewingPng();
    });
  }

  ///
  /// タイマーを終わります
  /// 使用場合：Swing中場合
  ///
  void _stopEmbProgImageTimer() {
    /// タイマーが既に存在する場合は、前のタイマーをキャンセルします
    _updateEmbProgImageTimer?.cancel();
    _updateEmbProgImageTimer = null;
  }
}
