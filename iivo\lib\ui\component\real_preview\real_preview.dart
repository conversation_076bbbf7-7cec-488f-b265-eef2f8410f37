import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../app/emb/ui/component/emb_footer/emb_footer.dart';
import '../../app/emb/ui/component/emb_header/emb_header.dart';
import '../../app/mdc/ui/component/mdc_footer/mdc_footer.dart';
import '../../app/mdc/ui/component/mdc_header/mdc_header.dart';
import 'component/real_preview_background.dart';
import 'component/real_preview_controller.dart';
import 'component/real_preview_needle.dart';
import 'component/real_preview_pattern.dart';
import 'real_preview_view_interface.dart';
import 'real_preview_view_model.dart';

///
/// リアルプレビュー画面
/// 刺しゅう枠とともに、模様のステッチを立体的に表示する。
///
class RealPreview extends StatefulPage {
  const RealPreview({super.key, required this.type});
  final RealPreviewType type;
  @override
  PageState<RealPreview> createState() => _RealPreviewState();
}

class _RealPreviewState extends PageState<RealPreview> {
  // タイプを widget から取得する
  RealPreviewType get type => widget.type;

  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(realPreviewViewModelProvider(type));
    final viewModel = ref.read(realPreviewViewModelProvider(type).notifier);
    FlexNum printBackGroundFlex = viewModel.printBackGroundFlex;
    int frameImageIndex =
        state.frameButtonStateList.contains(ButtonState.select)
            ? state.frameButtonStateList.indexOf(ButtonState.select)
            : RealPreviewFrameType.frame297x465.index;

    return Scaffold(
        body: Stack(
      children: [
        Material(
          color: Colors.white,
          child: Stack(
            children: [
              /// 押え表示
              state.isPressFootDisplay
                  ? Column(
                      children: [
                        const Spacer(flex: 84),
                        Expanded(
                          flex: 65,
                          child: Row(
                            children: [
                              const Spacer(flex: 9),
                              Expanded(
                                flex: 65,
                                child: Center(
                                  child: [
                                    const ico_presserfootledplusw(),
                                    const ico_presserfootledw(),
                                    const ico_presserfooty(),
                                  ][state.displayPressFootType.index],
                                ),
                              ),
                              const Spacer(flex: 726),
                            ],
                          ),
                        ),
                        const Spacer(flex: 1131),
                      ],
                    )
                  : Container(),

              /// プリント＆ステッチ模様で表示される背景画像
              type == RealPreviewType.emb && state.printBackGroundImage != null
                  ? Row(
                      children: [
                        Spacer(flex: printBackGroundFlex.left),
                        Expanded(
                          flex: printBackGroundFlex.centerX,
                          child: Column(
                            children: [
                              Spacer(flex: printBackGroundFlex.top),
                              Expanded(
                                flex: printBackGroundFlex.centerY,
                                child: state.printBackGroundImage!,
                              ),
                              Spacer(flex: printBackGroundFlex.bottom),
                            ],
                          ),
                        ),
                        Spacer(flex: printBackGroundFlex.right),
                      ],
                    )
                  : Container(),

              /// Real Preview
              Column(
                children: [
                  const Spacer(flex: 166),
                  Expanded(
                    flex: 838,
                    child: Stack(
                      children: [
                        Visibility(
                          visible: type == RealPreviewType.emb,
                          child: RealPreviewBackground(
                            controller: viewModel.realPreviewController,
                          ),
                        ),
                        RealPreviewPattern(
                          controller: viewModel.realPreviewController,
                        ),
                        RealPreviewNeedle(
                          controller: viewModel.realPreviewController,
                        ),
                      ],
                    ),
                  ),
                  const Spacer(flex: 276),
                ],
              ),

              /// Frame
              state.isZoomOut
                  ? [
                      /// 297x465
                      const Column(
                        children: [
                          Spacer(flex: 33),
                          Expanded(
                            flex: 1104,
                            child: Row(
                              children: [
                                Spacer(flex: 75),
                                Expanded(
                                  flex: 620,
                                  child: ico_s0_flame297x465(),
                                ),
                                Spacer(flex: 105),
                              ],
                            ),
                          ),
                          Spacer(flex: 143),
                        ],
                      ),

                      /// 272x272
                      const Column(
                        children: [
                          Spacer(flex: 246),
                          Expanded(
                            flex: 700,
                            child: Row(
                              children: [
                                Spacer(flex: 108),
                                Expanded(
                                  flex: 555,
                                  child: ico_s1_flame272x272(),
                                ),
                                Spacer(flex: 137),
                              ],
                            ),
                          ),
                          Spacer(flex: 334),
                        ],
                      ),

                      /// 130x180
                      const Column(
                        children: [
                          Spacer(flex: 354),
                          Expanded(
                            flex: 509,
                            child: Row(
                              children: [
                                Spacer(flex: 160),
                                Expanded(
                                  flex: 411,
                                  child: ico_s2_flame130x180(),
                                ),
                                Spacer(flex: 229),
                              ],
                            ),
                          ),
                          Spacer(flex: 417),
                        ],
                      ),

                      /// 100x100
                      const Column(
                        children: [
                          Spacer(flex: 426),
                          Expanded(
                            flex: 362,
                            child: Row(
                              children: [
                                Spacer(flex: 158),
                                Expanded(
                                  flex: 382,
                                  child: ico_s3_flame100x100(),
                                ),
                                Spacer(flex: 260),
                              ],
                            ),
                          ),
                          Spacer(flex: 492),
                        ],
                      ),
                    ][frameImageIndex]
                  : Container(),

              /// other UI
              Column(
                children: [
                  const Spacer(flex: 88),
                  Expanded(
                    flex: 63,
                    child: Row(
                      children: [
                        const Spacer(flex: 654),

                        /// 縮小表示
                        Expanded(
                          flex: 63,
                          child:
                              state.animationPlayState == RealPreviewAction.play
                                  ? Container()
                                  : grp_btn_zoomout_01(
                                  feedBackControl: FeedBackControl.onlyDisable,
                                  state: state.isBusyDrawing
                                      ? ButtonState.disable
                                      : state.isZoomOut
                                          ? ButtonState.select
                                          : ButtonState.normal,
                                      onTap: viewModel.onZoomOutButtonClick,
                                    ),
                        ),
                        const Spacer(flex: 8),

                        /// 拡大表示
                        Expanded(
                          flex: 63,
                          child:
                              state.animationPlayState == RealPreviewAction.play
                                  ? Container()
                                  : grp_btn_zoomin_01(
                                  feedBackControl: FeedBackControl.onlyDisable,
                                  state: state.isBusyDrawing
                                      ? ButtonState.disable
                                      : state.isZoomOut
                                          ? ButtonState.normal
                                          : ButtonState.select,
                                      onTap: viewModel.onZoomInButtonClick,
                                    ),
                        ),
                        const Spacer(flex: 12),
                      ],
                    ),
                  ),
                  const Spacer(flex: 899),
                  Expanded(
                    flex: 62,
                    child: Row(
                      children: [
                        const Spacer(flex: 12),

                        /// リアルプレビュー　タブ
                        Expanded(
                          flex: 90,
                          child: state.frameTableState ==
                                  RealPreviewTableType.hide
                              ? Container()
                              : CustomTooltip(
                                  message: l10n.tt_emb_previewframe,
                                  child: grp_btn_pr_flame(
                                      feedBackControl:
                                          FeedBackControl.onlyDisable,
                                      state: state.isBusyDrawing
                                          ? ButtonState.disable
                                          : state.frameTableState ==
                                            RealPreviewTableType.selected
                                        ? ButtonState.select
                                        : ButtonState.normal,
                                      onTap: viewModel.onFrameTableButtonClick
                                  ),
                                ),
                        ),
                        const Spacer(flex: 8),

                        /// ステッチシミュレーター　タブ
                        Expanded(
                          flex: 90,
                          child: state.stitchSimulatorTableState ==
                                  RealPreviewTableType.hide
                              ? Container()
                              : CustomTooltip(
                                  message: l10n.tt_emb_previewsim,
                                  child: grp_btn_pr_simulator(
                                      feedBackControl:
                                          FeedBackControl.onlyDisable,
                                      state: state.isBusyDrawing
                                          ? ButtonState.disable
                                          : state.stitchSimulatorTableState ==
                                            RealPreviewTableType.selected
                                        ? ButtonState.select
                                        : ButtonState.normal,
                                    onTap: viewModel
                                          .onStitchSimulatorTableButtonClick
                                  ),
                                ),
                        ),
                        const Spacer(flex: 600),
                      ],
                    ),
                  ),
                  const Spacer(flex: 17),
                  Expanded(
                    flex: 80,
                    child: Stack(
                      children: [
                        /// 刺しゅう枠選択
                        state.frameTableState != RealPreviewTableType.unselected
                            ? Column(
                                children: [
                                  const Spacer(flex: 5),
                                  Expanded(
                                    flex: 70,
                                    child: Row(
                                      children: [
                                        const Spacer(flex: 12),
                                        Expanded(
                                          flex: 98,
                                          child: grp_btn_pr_selectflame272x408(
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: state.isBusyDrawing
                                                ? ButtonState.disable
                                                : state.frameButtonStateList[
                                                RealPreviewFrameType
                                                    .frame297x465.index],
                                            onTap: () =>
                                                viewModel.onFrameButtonClick(
                                              RealPreviewFrameType.frame297x465,
                                            ),
                                          ),
                                        ),
                                        const Spacer(flex: 8),
                                        Expanded(
                                          flex: 98,
                                          child: grp_btn_pr_selectflame240x240(
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: state.isBusyDrawing
                                                ? ButtonState.disable
                                                : state.frameButtonStateList[
                                                RealPreviewFrameType
                                                    .frame272x272.index],
                                            onTap: () =>
                                                viewModel.onFrameButtonClick(
                                              RealPreviewFrameType.frame272x272,
                                            ),
                                          ),
                                        ),
                                        const Spacer(flex: 8),
                                        Expanded(
                                          flex: 98,
                                          child: grp_btn_pr_selectflame130x180(
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: state.isBusyDrawing
                                                ? ButtonState.disable
                                                : state.frameButtonStateList[
                                                RealPreviewFrameType
                                                    .frame130x180.index],
                                            onTap: () =>
                                                viewModel.onFrameButtonClick(
                                              RealPreviewFrameType.frame130x180,
                                            ),
                                          ),
                                        ),
                                        const Spacer(flex: 8),
                                        Expanded(
                                          flex: 98,
                                          child: grp_btn_pr_selectflame100x100(
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: state.isBusyDrawing
                                                ? ButtonState.disable
                                                : state.frameButtonStateList[
                                                RealPreviewFrameType
                                                    .frame100x100.index],
                                            onTap: () =>
                                                viewModel.onFrameButtonClick(
                                              RealPreviewFrameType.frame100x100,
                                            ),
                                          ),
                                        ),
                                        const Spacer(flex: 372),
                                      ],
                                    ),
                                  ),
                                  const Spacer(flex: 5),
                                ],
                              )
                            : Container(),

                        /// 速度再生キー/頭出しキー/再生キー / 一時停止キー
                        state.stitchSimulatorTableState ==
                                RealPreviewTableType.selected
                            ? Row(
                                children: [
                                  const Spacer(flex: 11),
                                  Expanded(
                                    flex: 63,
                                    child: Column(
                                      children: [
                                        const Spacer(flex: 5),
                                        Expanded(
                                          flex: 63,
                                          child: grp_btn_pr_simulator_speed1(
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: state.isBusyDrawing
                                                ? ButtonState.disable
                                                : state.speedButtonStateList[
                                                RealPreviewSpeed.low.index],
                                            onTap: () =>
                                                viewModel.onSpeedButtonClick(
                                              RealPreviewSpeed.low,
                                            ),
                                          ),
                                        ),
                                        const Spacer(flex: 12),
                                      ],
                                    ),
                                  ),
                                  const Spacer(flex: 8),
                                  Expanded(
                                    flex: 63,
                                    child: Column(
                                      children: [
                                        const Spacer(flex: 5),
                                        Expanded(
                                          flex: 63,
                                          child: grp_btn_pr_simulator_speed2(
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: state.isBusyDrawing
                                                ? ButtonState.disable
                                                : state.speedButtonStateList[
                                                RealPreviewSpeed.middle.index],
                                            onTap: () =>
                                                viewModel.onSpeedButtonClick(
                                              RealPreviewSpeed.middle,
                                            ),
                                          ),
                                        ),
                                        const Spacer(flex: 12),
                                      ],
                                    ),
                                  ),
                                  const Spacer(flex: 8),
                                  Expanded(
                                    flex: 63,
                                    child: Column(
                                      children: [
                                        const Spacer(flex: 5),
                                        Expanded(
                                          flex: 63,
                                          child: grp_btn_pr_simulator_speed3(
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: state.isBusyDrawing
                                                ? ButtonState.disable
                                                : state.speedButtonStateList[
                                                RealPreviewSpeed.high.index],
                                            onTap: () =>
                                                viewModel.onSpeedButtonClick(
                                              RealPreviewSpeed.high,
                                            ),
                                          ),
                                        ),
                                        const Spacer(flex: 12),
                                      ],
                                    ),
                                  ),
                                  const Spacer(flex: 8),
                                  Expanded(
                                    flex: 98,
                                    child: Column(
                                      children: [
                                        const Spacer(flex: 5),
                                        Expanded(
                                          flex: 70,
                                          child: grp_btn_pr_simulator_cueing(
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: state.isBusyDrawing
                                                ? ButtonState.disable
                                                : ButtonState.normal,
                                            onTap:
                                                viewModel.onRrePlayButtonClick,
                                          ),
                                        ),
                                        const Spacer(flex: 5),
                                      ],
                                    ),
                                  ),
                                  const Spacer(flex: 8),
                                  Expanded(
                                    flex: 98,
                                    child: Column(
                                      children: [
                                        const Spacer(flex: 5),
                                        Expanded(
                                          flex: 70,
                                          child: state.animationPlayState ==
                                                  RealPreviewAction.play
                                              ? grp_btn_pr_simulator_playstop(
                                                  feedBackControl:
                                                      FeedBackControl
                                                          .onlyDisable,
                                                  state: state.isBusyDrawing
                                                      ? ButtonState.disable
                                                      : ButtonState.normal,
                                                  onTap: viewModel
                                                      .onPlayButtonClick,
                                                )
                                              : grp_btn_pr_simulator_play(
                                                  state: state.isBusyDrawing
                                                      ? ButtonState.disable
                                                      : ButtonState.normal,
                                                  feedBackControl:
                                                      FeedBackControl
                                                          .onlyDisable,
                                                  onTap: viewModel
                                                      .onPlayButtonClick,
                                                ),
                                        ),
                                        const Spacer(flex: 5),
                                      ],
                                    ),
                                  ),
                                  const Spacer(flex: 372),
                                ],
                              )
                            : Container(),

                        /// Close
                        Row(
                          children: [
                            const Spacer(flex: 636),
                            Expanded(
                              flex: 152,
                              child: grp_btn_positive(
                                text: l10n.icon_close_1,
                                onTap: viewModel.onCloseButtonClick,
                              ),
                            ),
                            const Spacer(flex: 12),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const Spacer(flex: 71),
                ],
              ),
            ],
          ),
        ),
        Column(
          children: [
            Expanded(
              flex: 71,
              child: type == RealPreviewType.emb
                  ? const EmbHeader()
                  : const MdcHeader(),
            ),
            const Spacer(flex: 1209),
          ],
        ),
        Column(
          children: [
            const Spacer(flex: 1219),
            Expanded(
              flex: 61,
              child: type == RealPreviewType.emb
                  ? const EmbFooter()
                  : const MdcFooter(),
            ),
          ],
        ),
      ],
    ));
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup(WidgetRef ref) => {};
}
