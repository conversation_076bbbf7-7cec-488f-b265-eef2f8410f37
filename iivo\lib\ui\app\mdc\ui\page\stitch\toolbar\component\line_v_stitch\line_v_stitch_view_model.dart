import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/device_info_model.dart';
import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/line_v_stitch_model.dart';
import '../../../../../../model/stitch/toolbar_model.dart';
import '../../../stitch_page_view_model.dart';
import 'line_v_stitch_view_interface.dart';

typedef Unit = DisplayUnit;

///
/// 白い背景に黒いテキスト
///
const TextStyle blackTextWhiteBackground =
    TextStyle(color: Colors.white, backgroundColor: Colors.black);

///
/// 黒い背景に白いテキスト
///
const TextStyle whiteTextBlackBackground =
    TextStyle(color: Colors.black, backgroundColor: Colors.white);

/// 読み出したSizeデータの変換倍率 '読み出したデータの単位/10=mm'
const double _conversionRate = 10.0;

typedef LineVStitchViewModelProvider = AutoDisposeStateNotifierProvider<
    LineVStitchViewInterface, LineVStitchState>;

class LineVStitchViewModel extends LineVStitchViewInterface {
  LineVStitchViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineVStitchState(
              widthDisplayValue: "",
              isWidthDefaultValue: false,
              spaceDisplayValue: "",
              isSpaceDefaultValue: false,
              thicknessDisplayValue: "",
              isThicknessDefaultValue: false,
              flipDisplayValue: "",
              isFlipDefaultValue: false,
            ),
            ref) {
    update();
  }

  ///
  /// 単位取得する
  ///
  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  ///
  /// V ピン プロパティの既定値
  ///
  final defaultWidthValue = LineVStitchModel().widthDefaultValue;
  final defaultSpaceValue = LineVStitchModel().spaceDefaultValue;
  final defaultThicknessValue = LineVStitchModel().thicknessDefaultValue;

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    FlipSide flip = LineVStitchModel().getFlip();
    state = state.copyWith(
        widthDisplayValue: _getWidthDisplayValue(),
        isWidthDefaultValue: _getWidthDisplayTextStyle(),
        spaceDisplayValue: _getSpaceDisplayValue(),
        isSpaceDefaultValue: _getSpaceDisplayTextStyle(),
        thicknessDisplayValue: _getThicknessDisplayValue(),
        isThicknessDefaultValue: _getThicknessDisplayTextStyle(),
        flipDisplayValue: _getFlipDisplayValue(),
        isFlipDefaultValue: _getFlipDisplayTextStyle());
    if (flip != FlipSide.flip_invalid) {
      state = state.copyWith(
          isFlipSelect: LineVStitchModel().getFlip() == FlipSide.flip_inside
              ? true
              : false);
    }
  }

  ///
  /// 幅設定ポップアップウィンドウを開きます
  ///
  @override
  void openWidthSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineVStitchWidth.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// 間隔設定ポップアップウィンドウを開く
  ///
  @override
  void openSpaceSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineVStitchSpace.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// 厚さ設定ポップアップウィンドウを開く
  ///
  @override
  void openThicknessSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineVStitchThickness.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// 方向設定ポップアップウィンドウを開きます
  ///
  @override
  void openFlipSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    if (LineVStitchModel().getFlip() == FlipSide.flip_inside) {
      ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
          PopupEnum.lineVStitchFlip.toString(),
          () => ref
              .read(stitchPageViewModelProvider.notifier)
              .updateRelatedInStitchPage(update));
      state = state.copyWith(isFlipSelect: true);
    } else {
      ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
          PopupEnum.lineVStitchFlip.toString(),
          () => ref
              .read(stitchPageViewModelProvider.notifier)
              .updateRelatedInStitchPage(update));
      state = state.copyWith(isFlipSelect: false);
    }

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  String get textSignal => ToolbarModel.xCharCode;

  ///
  /// 幅の表示値を取得する
  ///
  String _getWidthDisplayValue() {
    int width = LineVStitchModel().getWidth();

    /// cmからmmへ
    double lineVStitchWidthValue = width / _conversionRate;

    if (width == LineVStitchModel.widthNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return lineVStitchWidthValue.toStringAsFixed(1);
    }

    return ToolbarModel.getDisplayInchShowValue(lineVStitchWidthValue);
  }

  ///
  /// 幅表示テキストスタイルを取得します
  ///
  bool _getWidthDisplayTextStyle() {
    int width = LineVStitchModel().getWidth();
    if (width == LineVStitchModel.widthNotUpdating ||
        width == defaultWidthValue) {
      return true;
    }

    return false;
  }

  ///
  /// 間隔の表示値を取得します
  ///
  String _getSpaceDisplayValue() {
    int space = LineVStitchModel().getSpace();

    /// cmからmmへ
    double lineVStitchSpaceValue = space / _conversionRate;

    if (space == LineVStitchModel.spacingNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return lineVStitchSpaceValue.toStringAsFixed(1);
    }

    return ToolbarModel.getDisplayInchShowValue(lineVStitchSpaceValue);
  }

  ///
  /// 間隔表示テキスト スタイルを取得します
  ///
  bool _getSpaceDisplayTextStyle() {
    int space = LineVStitchModel().getSpace();
    if (space == LineVStitchModel.spacingNotUpdating ||
        space == defaultSpaceValue) {
      return true;
    }

    return false;
  }

  ///
  /// 厚さの表示値を取得します
  ///
  String _getThicknessDisplayValue() {
    int thickNess = LineVStitchModel().getThickness();
    if (thickNess == LineVStitchModel.thicknessNotUpdating) {
      return "*";
    }
    return LineVStitchModel().getThickness().toString();
  }

  ///
  /// 太さ表示のテキスト スタイルを取得します
  ///
  bool _getThicknessDisplayTextStyle() {
    int thickNess = LineVStitchModel().getThickness();
    if (thickNess == defaultThicknessValue ||
        thickNess == LineVStitchModel.thicknessNotUpdating) {
      return true;
    }

    return false;
  }

  ///
  /// 方向の表示値を取得します
  ///
  String _getFlipDisplayValue() {
    if (LineVStitchModel().getFlip() == LineVStitchModel.sideNotUpdating) {
      return "**";
    } else {
      return "";
    }
  }

  ///
  /// 方向表示のテキスト スタイルを取得します
  ///
  bool _getFlipDisplayTextStyle() =>
      LineVStitchModel().getFlip() != LineVStitchModel.sideNotUpdating
          ? true
          : false;
}
