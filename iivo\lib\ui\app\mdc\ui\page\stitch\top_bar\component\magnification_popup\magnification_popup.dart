import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'magnification_popup_view_model.dart';

class MagnificationPopup extends ConsumerStatefulWidget {
  const MagnificationPopup({Key? key}) : super(key: key);

  @override
  ConsumerState<MagnificationPopup> createState() => _MagnificationPopupState();
}

class _MagnificationPopupState extends ConsumerState<MagnificationPopup> {
  @override
  Widget build(BuildContext context) {
    final viewModel = ref.read(magnificationPopupViewModelProvider.notifier);
    final state = ref.watch(magnificationPopupViewModelProvider);

    return Column(
      children: [
        const Spacer(flex: 78),
        Expanded(
          flex: 292,
          child: Row(
            children: [
              const Spacer(flex: 6),
              Expanded(
                flex: 145,
                child: Material(
                  color: Colors.transparent,
                  child: Stack(
                    children: [
                      const pic_zoombase(),
                      Row(
                        children: [
                          const Spacer(
                            flex: 6,
                          ),
                          Expanded(
                            flex: 133,
                            child: Column(
                              children: [
                                const Spacer(flex: 6),
                                Expanded(
                                  flex: 63,
                                  child: grp_btn_zoom(
                                    onTap:
                                        viewModel.maybeCloseMagnificationPopup,
                                    state: ButtonState.select,
                                    child: viewModel.selectedImagePathList[
                                        state.magnificationIndex],
                                  ),
                                ),
                                const Spacer(flex: 12),
                                Expanded(
                                  flex: 211,
                                  child: Column(
                                    children: [
                                      Expanded(
                                        flex: 63,
                                        child: ListView.separated(
                                          scrollDirection: Axis.vertical,
                                          itemCount: viewModel
                                              .unselectedPopupImagePathList
                                              .length,
                                          itemBuilder: (context, index) =>
                                              SizedBox(
                                            width: 133,
                                            height: 63,
                                            child: grp_btn_zoom(
                                              onTap: () {
                                                viewModel
                                                    .maybeCloseMagnificationPopup();
                                                viewModel
                                                    .onMagnificationLevelClicked(
                                                        index);
                                              },
                                              state: index ==
                                                      state.magnificationIndex
                                                  ? ButtonState.select
                                                  : ButtonState.normal,
                                              child: viewModel
                                                      .unselectedPopupImagePathList[
                                                  index],
                                            ),
                                          ),
                                          separatorBuilder: (context, index) =>
                                              const SizedBox(height: 8),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Spacer(
                            flex: 6,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const Spacer(flex: 655),
            ],
          ),
        ),
        const Spacer(flex: 890),
      ],
    );
  }
}
