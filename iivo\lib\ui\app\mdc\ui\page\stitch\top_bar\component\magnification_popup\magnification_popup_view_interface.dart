import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:xd_component/xd_component.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'magnification_popup_view_interface.freezed.dart';

@freezed
class MagnificationPopupState with _$MagnificationPopupState {
  const factory MagnificationPopupState({
    @Default(0) int magnificationIndex,
  }) = _MagnificationPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class MagnificationPopupViewInterface
    extends ViewModel<MagnificationPopupState> {
  MagnificationPopupViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// 選択した画像パスのリスト
  ///
  final List<Widget> selectedImagePathList = [
    const ico_zoom100(),
    const ico_zoom200(),
    const ico_zoom400(),
  ];

  ///
  /// 選択されていないポップアップ画像パスのリスト
  ///
  final List<Widget> unselectedPopupImagePathList = [
    const ico_zoom100_text(),
    const ico_zoom200_text(),
    const ico_zoom400_text(),
  ];

  ///
  /// クリックしてレベルを拡大
  ///
  void onMagnificationLevelClicked(int index);

  ///
  /// 拡大表示ポップアップを開く
  ///
  void maybeCloseMagnificationPopup();
}
