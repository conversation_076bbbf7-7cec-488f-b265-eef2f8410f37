param (
  [Alias('r')]
  [switch]$Reboot
)

Push-Location "$PSScriptRoot/../iivo"
try {
  Write-Host "Compile panel release..." -ForegroundColor Cyan
  & fvm flutter build apk -t lib/panel.dart --flavor=panelmachine --release  --target-platform=android-arm
  if ($LASTEXITCODE -ne 0) {
    return;
  }
}
finally {
  Pop-Location
}
Push-Location "$PSScriptRoot"
try {
  Write-Host "Update panel apk..." -ForegroundColor Cyan
  & ./updatepanelapk.ps1 -d
  Write-Host "Update brother panel..." -ForegroundColor Cyan
  & ./updatabrotherpanel -useDefault
  if ($Reboot -and ($? -eq 0)) {
    Write-Host "Reboot device"
    & adb.exe reboot
  }
}
finally {
  Pop-Location
}
