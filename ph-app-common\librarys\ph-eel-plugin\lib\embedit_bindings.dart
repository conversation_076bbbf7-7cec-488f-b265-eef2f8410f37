import 'dart:ffi';
import 'dart:io';
import 'dart:typed_data';

import 'package:ffi/ffi.dart' as ffi;
import 'package:path_provider/path_provider.dart';

import 'embedit_bindings_generated.dart';
import 'lib_base.dart';

final EmbEditBindings _embBindings = EmbEditBindings(panellib);
EmbEditBindings getEmbEditBindings() => _embBindings;

class EmbEdit {
  String s = "";

  ///2.1.	刺繍モード開始
  int openEmbMode() {
    return _embBindings.openEmbMode();
  }

  ///2.2.	刺繍モード終了
  void closeEmbMode() {
    return _embBindings.closeEmbMode();
  }

  ///2.4.	出力するBMPの色数を指定する
  void setOutColorDepth(int colorDepth) {
    _embBindings.setOutColorDepth(colorDepth);
  }

  ///2.5.	出力されるBMPの色数を取得する
  int getOutColorDepth() {
    return _embBindings.getOutColorDepth();
  }

  ///3.4.	模様選択
  int selectEmb(int embIndex, int category, int bh) {
    return _embBindings.selectEmb(embIndex, category, bh, _gHandleEmb);
  }

  // int selectEmbPf1(int unicodeIdx, int fontIdx) {
  //   return _embBindings.selectEmbPf1(unicodeIdx, fontIdx, _gHandleEmb);
  // }

  int deleteQuiltThreadInfo() {
    return _embBindings.deleteThreadInfo(_threadInfo);
  }

  ///6.1.	キルトイサムネイルイメージの取得
  int getQuiltThumbnailImage(int embQuiltIndex, int quiltCategory) {
    return _embBindings.getQuiltThumbnailImage(embQuiltIndex, quiltCategory,
        _embImageInfo, _threadInfo, _threadInfoNum);
  }

  ///6.2.	キルトイメージの取得
  int getQuiltImage(int embQuiltIndex, int quiltCategory, int color, int flip) {
    return _embBindings.getQuiltImage(
        embQuiltIndex, quiltCategory, color, flip, _embImageInfo);
  }

  ///6.4.	キルトイプレビューイメージの取得(4分割)
  int getQuiltPreviewImage(bool dividingLine) {
    return _embBindings.getQuiltPreviewImage(
      dividingLine,
      _division6_4,
      _embImageInfo,
    );
  }

  ///6.5.	キルトイプレビューイメージの取得(1方向)
  int getQuiltPreviewExtensionImage(bool frameBorder) {
    return _embBindings.getQuiltPreviewExtensionImage(
        frameBorder, _division6_5, _embImageInfo);
  }

  ///6.6.	キルトイプレビューイメージの取得(6角形)
  int getQuiltPreviewHexagonImage(bool frameBorder) {
    return _embBindings.getQuiltPreviewHexagonImage(
        frameBorder, _division6_6, _embImageInfo);
  }

  ///6.7.	キルトサイズ入力上限下限値取得
  int getQuiltSizeLimit(int quiltType, int frameType) {
    return _embBindings.getQuiltSizeLimit(quiltType, frameType, _sizeLimit);
  }

  ///6.8.	キルトサイズ幅設定
  int setQuiltWidth(int wValue) {
    return _embBindings.setQuiltWidth(
      wValue,
      _sizeLimit,
    );
  }

  ///6.9.	キルトサイズ高さ設定
  int setQuiltHeight(int hValue) {
    return _embBindings.setQuiltHeight(hValue, sizeLimit);
  }

  ///6.10.	キルトサイズバンド幅設定
  int setQuiltBand(int bValue) {
    return _embBindings.setQuiltBand(bValue, sizeLimit);
  }

  ///6.11.	キルト糸色設定
  void changeQuiltColor(int colorNum, int r, int g, int b) {
    _embBindings.changeQuiltColor(colorNum, r, g, b);
  }

  ///6.12.	キルトデータ保存
  int saveQuiltData(int type, int sewType, String name) {
    _fileName = StoC(name);
    return _embBindings.saveQuiltData(type, sewType, _fileName);
  }

  ///6.13.	EdgeToEdgeキルトのフリップオプションの1パーツ取得
  int getFlipPart(
    int embQuiltIndex,
    int flip,
    bool rect,
  ) {
    return _embBindings.getFlipPart(embQuiltIndex, flip, rect, _embImageInfo);
  }

  ///6.14.	EdgeToEdgeキルトの縫製情報取得
  int getSewingInfoEdgeToEdge() {
    return _embBindings.getSewingInfoEdgeToEdge(_edgToEdgInfo);
  }

  ///6.15.	キルトプレビューイメージの取得(Edge To Edge)
  int getQuiltPreviewEdgeToEdgeImage(frameBorder) {
    return _embBindings.getQuiltPreviewEdgeToEdgeImage(
        frameBorder, _division, embImageInfo);
  }

  ///7.1.	ファイルデータのサムネイル取得
  int getThumbnailSavedFile(int size, bool transparent) {
    return _embBindings.getThumbnailSavedFile(
        _fileName, size, transparent, _param, _embImageInfo);
  }

  ///7.2.	ファイルデータの分割サムネイル取得
  int getThumbnailPartsSavedFile(int size, int index, bool transparent) {
    return _embBindings.getThumbnailPartsSavedFile(
        _fileName, size, index, transparent, _param, _embImageInfo);
  }

  ///7.3.	ファイルデータの分割数を求める
  int getPartsNumSavedFile() {
    return _embBindings.getPartsNumSavedFile(_fileName, _parts);
  }

  ///7.4.	ファイルデータを選択
  int selectEditFile(int partsNo, String filename) {
    //_fileName = StoC(filename);
    return _embBindings.selectEditFile(
        _fileName, 0, 0, partsNo, _gHandleEmb, handleNum);
  }

  ///9.1.	模様情報取得
  int getEmbInfo(MemHandle_t gHandle) {
    int i = _embBindings.getEmbInfo(gHandle, _embInfo);
    return i;
  }

  ///9.2.	刺繍模様グループパターン情報取得
  int getEmbGrpInfo(MemHandle_t gHandle) {
    return _embBindings.getEmbGrpInfo(gHandle, embInfo, grpInfo, ptrnInfo);
  }

  int deleteEmbEditThreadInfo() {
    return _embBindings.deleteThreadInfo(threadInfo);
  }

  ///9.3.	模様糸情報取得
  int getEmbEditThreadInfo() {
    return _embBindings.getEmbEditThreadInfo(
        _gHandleEmb.value, threadInfo, threadInfoNum);
  }

  ///9.4.	模様追加前処理
  int prepareAddEmb() {
    return _embBindings.prepareAddEmb();
  }

  ///9.5.	模様削除
  static bool isFirst = true;
  int deleteEmb() {
    if (isFirst) {
      isFirst = false;
      return 0;
    }
    return _embBindings.deleteEmb();
  }

  ///9.6.	模様保存
  int saveEmbData(String name) {
    _fileName = StoC(name);
    return _embBindings.saveEmbData(_fileName);
  }

  int saveEmbResumeData(String name) {
    _fileName = StoC(name);
    return _embBindings.saveEmbResumeData(_fileName);
  }

  ///9.7.	模様タイプ確認
  int checkEmbPatternType(int embType) {
    return _embBindings.checkEmbPatternType(
        _gHandleEmb.value, embType, _result97);
  }

  ///9.8.	模様パターン情報取得
  int getEmbGrpPatternInfo() {
    return 0;
    //return _embBindings.getEmbGrpPatternInfo(_gHandleEmb.value,ptrnInfo);
  }

  ///10.3.	パーツ(短冊)の選択
  int selectPartsInGroupColorChange(int index) {
    _embBindings.selectPartsInGroupColorChange(_gHandleEmb.value, index);
    return index;
  }

  ///10.4.	糸色テーブル取得
  int getThreadColorTable(
    int brandCode,
  ) {
    return _embBindings.getThreadColorTable(brandCode, _tcTable, _recordNum);
  }

  ///10.5.	糸色変更　カラーパレットの選択
  int changeColorByPalette(int brandCode, int index) {
    return _embBindings.changeColorByPalette(brandCode, index);
  }

  ///10.6.	糸色変更　RGB指定
  int changeColor(int r, int g, int b) {
    return _embBindings.changeColor(r, g, b);
  }

  ///10.8.	糸色名取得
  int getThreadColorName(int brandCode, int threadCode) {
    return _embBindings.getThreadColorName(
        brandCode, threadCode, _pRGBCode, _colorName);
  }

  ///11.2	カラーシャッフルできる最大色数の取得
  int getMaxNumColorShuffle(int brandCode) {
    return _embBindings.getMaxNumColorShuffle(brandCode, colors);
  }

  ///11.4.	ランダム色でシャッフルする
  int shuffleRandom(int shuffuleColorNum) {
    return _embBindings.shuffleRandom(shuffuleColorNum, _shuffle, _thumbnail);
  }

  ///11.5.	ビビッド色でシャッフルする
  int shuffleVivid(int shuffuleColorNum) {
    return _embBindings.shuffleVivid(shuffuleColorNum, _thumbnail);
  }

  ///11.6.	グラデーション色でシャッフルする
  int shuffleGradation(
    int shuffuleColorNum,
  ) {
    return _embBindings.shuffleGradation(
        shuffuleColorNum, _shuffle, _thumbnail);
  }

  ///11.7.	ソフト色でシャッフルする
  int shuffleSoft(int shuffuleColorNum) {
    return _embBindings.shuffleSoft(shuffuleColorNum, _thumbnail);
  }

  ///11.8.	模様サムネイルの配色を更新追加する
  int addShufflePattern() {
    return _embBindings.addShufflePattern(_thumbnail);
  }

  ///13.2.	カラーシャッフル結果画像抽出
  int getShuffledImage(
    int thumbNum,
    int type,
  ) {
    return _embBindings.getShuffledImage(thumbNum - 1, type, _embImageInfo);
  }

  ///15.2.	編集用に新色を設定する
  int setShuffledNewColor(int thumbNum) {
    return _embBindings.setShuffledNewColor(thumbNum);
  }

  ///16.1.	編集対象選択
  int selectEmbToEdit(MemHandle_t gHandle) {
    return _embBindings.selectEmbToEdit(gHandle);
  }

  ///16.2.	模様の左右反転
  int flipEmbHorizontal() {
    return _embBindings.flipEmbHorizontal();
  }

  ///16.3.	複数模様のグループ化
  int groupingMultiplePatterns() {
    return _embBindings.groupingMultiplePatterns(_borderH);
  }

  ///16.4.	グループ化された模様のグループ化解除
  int ungroupingGroupedPatterns() {
    return _embBindings.ungroupingGroupedPatterns(_borderH.value);
  }

  ///16.5.	模様の複製
  int duplicateEmb() {
    return _embBindings.duplicateEmb(_dupHArray, _handleNum16_5);
  }

  ///16.6.	アウトライン作成
  int makeOutline(int distance, bool inside, bool back) {
    return _embBindings.makeOutline(distance, inside, back, _embImageInfo);
  }

  ///16.7.	スティップリング作成
  int makeStipple(int type) {
    return _embBindings.makeStipple(type, _gHandleStipple);
  }

  ///16.8.	ボーダー処理を開始する
  int startEmbBorder() {
    return _embBindings.startEmbBorder();
  }

  ///16.9.	スティップリングの初期化
  int initMakingEasyStipple() {
    return _embBindings.initMakingEasyStipple();
  }

  ///17.2.	模様の拡大縮小
  int changeSizeEmbGroup(int type, int step) {
    return _embBindings.changeSizeEmbGroup(type, step);
  }

  ///17.3.	模様の拡大縮小STB
  int changeSizeEmbGroupSTB(int type, int step) {
    return _embBindings.changeSizeEmbGroupSTB(type, step);
  }

  ///18.1.	模様の移動
  int moveEmb(int dirX, int dirY) {
    return _embBindings.moveEmb(dirX, dirY);
  }

  ///18.2.	模様中央に移動させる
  int moveEmbCenter() {
    return _embBindings.moveEmbCenter();
  }

  ///18.3.	模様全体の移動
  int moveEmbAll(int dirX, int dirY, bool flgLongPress) {
    return _embBindings.moveEmbAll(dirX, dirY, flgLongPress);
  }

  ///18.4.	模様全体の中央移動
  int moveEmbAllCenter() {
    return _embBindings.moveEmbAllCenter();
  }

  ///19.1.	模様の回転
  int rotateEmb(int angle) {
    return _embBindings.rotateEmb(angle);
  }

  ///19.2.	模様全体の回転
  int rotateEmbAll(int angle) {
    return _embBindings.rotateEmbAll(angle);
  }

  ///20.1.	刺繍密度の設定
  int setEmbDensity(int density) {
    return _embBindings.setEmbDensity(_gHandleEmb.value, density);
  }

  ///21.2.	ボーダー模様の追加
  int addBorder(int borderPos) {
    return _embBindings.addBorder(borderPos, _borderH);
  }

  ///21.3.	ボーダー模様の削除
  int delBorder(int borderPos) {
    return _embBindings.delBorder(borderPos);
  }

  ///21.4.	ボーダー模様の分割
  int divideBorder(int direction, int index) {
    return _embBindings.divideBorder(direction, index);
  }

  ///21.5.	ボーダー間隔の設定
  int setBorderSpace(int direction, int space) {
    return _embBindings.setBorderSpace(direction, space);
  }

  ///21.7.	全ボーダーハンドル取得
  int getBorderHandleAll() {
    return _embBindings.getBorderHandleAll(_borderHArray, _handleNum);
  }

  ///21.8.	ボーダー内の全グループハンドル取得
  int getGroupHandleInBorder(Pointer<EmbBorder_t> borderH) {
    return _embBindings.getGroupHandleInBorder(borderH, _grpHArray, _handleNum);
  }

  ///21.9.	ボーダー情報取得
  int getBorderInfo() {
    return _embBindings.getBorderInfo(_borderH.value, _borderInfo);
  }

  ///21.10.	指定したグループハンドルを含むボーダーハンドルを取得する
  int getBorderHandleIncludeGroup(
      MemHandle_t gHandle, Pointer<Pointer<EmbBorder_t>> borderH) {
    return _embBindings.getBorderHandleIncludeGroup(gHandle, borderH);
  }

  ///21.11.	ボーダー構成要素の情報取得
  int getBorderCompInfo(int index) {
    return _embBindings.getBorderCompInfo(
        (borderH.value as MemHandle_t), index, elementPos, mark);
  }

  ///21.12.	糸印アサイン
  int asignThreadMark(int index, int setReset, int position) {
    return _embBindings.asignThreadMark(index, setReset, position);
  }

  ///21.13.ボーダー処理の終了
  int endEmbBorder() {
    return _embBindings.endEmbBorder();
  }

  ///22.1.	Applique 作成可能かの確認
  int initAppliqueSelect() {
    return _embBindings.initAppliqueSelect();
  }

  ///22.2.	Applique 作成の前に糸印を消す
  int deleteMarkBeforeWappen() {
    return _embBindings.deleteMarkBeforeWappen();
  }

  ///22.3.	Applique 作成の前にボーダー属性を消し、新たにグループ化する
  int cancelBorderBeforeWappen() {
    return _embBindings.cancelBorderBeforeWappen(borderH);
  }

  ///22.4.	Applique 作成
  int makeApplique() {
    return _embBindings.makeApplique(_gHandleApp);
  }

  ///23.1.	オフセット距離設定
  int setDistanceApplique(int distance) {
    return _embBindings.setDistanceApplique(distance, _gHandleApp);
  }

  ///23.2.	Applique 作成取り消し
  int cancelApplique() {
    return _embBindings.cancelApplique();
  }

  ///23.3.	Applique 作成完了
  int finishApplique() {
    return _embBindings.finishApplique();
  }

  ///24.1.	24.1.	アップリケのパーツを選択するモードに移行可能かチェックする
  int checkUsableAppliqueParts() {
    return _embBindings.checkUsableAppliqueParts();
  }

  ///24.2.	Applique 色選択モード初期化
  int goAppliquePartsSelection() {
    return _embBindings.goAppliquePartsSelection();
  }

  int goAppliqueParameterSetting() {
    return _embBindings.goAppliqueParameterSetting();
  }

  ///24.3.	アップリケパーツの選択
  int selectAppliqueParts(int index, bool selection) {
    return _embBindings.selectAppliqueParts(
        _gHandleEmb.value, index, selection, _embImageInfo);
  }

  ///24.4.	アップリケパーツの全選択
  int selectAppliquePartsAll(bool selection) {
    return _embBindings.selectAppliquePartsAll(
        _gHandleEmb.value, selection, _embImageInfo);
  }

  ///25.7.	パーツのアップリケ作成
//  int makeAppliqueSelectedParts(String filepath) {
//    _fileName= StoC(filepath);
//    return _embBindings.makeAppliqueSelectedPartsFile(_appParam,_gHandleApp,_fileName);
  int makeAppliqueSelectedParts() {
    return _embBindings.makeAppliqueSelectedParts(
        _appParam, _gHandleApp, _embImageInfo);
  }

  ///25.8.	テクスチャー設定
  int setTexture(bool texture) {
    return _embBindings.setTexture(texture, embImageInfo);
  }

  ///25.9	色選択AppliquePreview取り消し
  int cancelPreviewApplique() {
    return _embBindings.cancelPreviewApplique();
  }

  ///25.10.	テクスチャの描画が可能かどうか確認する
  int checkTextureDrawing() {
    return _embBindings.checkTextureDrawing();
  }

  ///25.11.	色選択Applique編集モードへ移行
  int editSelectedApplique() {
    return _embBindings.editSelectedApplique();
  }

  ///25.14.	色選択Applique編集モード　縫製/非縫製設定
  int changeSettingsSweing(
    int type,
    int index,
    bool notSewing,
  ) {
    return _embBindings.changeSettingsSweing(
      _gHandleEmb.value,
      type,
      index,
      notSewing,
    );
  }

  ///25.15.	アップリケの編集画面のイメージ取得
  int getWappenPreviewTexture() {
    return _embBindings.getWappenPreviewTexture(embImageInfo);
  }

  ///25.16.	色選択Applique編集モード　処理完了編集に移動
  int completeSelectedApplique() {
    return _embBindings.completeSelectedApplique();
  }

  int cancelSelectedApplique() {
    return _embBindings.cancelSelectedApplique();
  }

  ///26.2.	模様の整列
  int alignEmb(int alighnment) {
    return _embBindings.alignEmb(alighnment);
  }

  ///27.1.	スティップリング指定
  int setStipple(int frSize, int distance, int space) {
    return _embBindings.setStipple(frSize, distance, space, _gHandleStipple);
  }

  ///27.2.	エコーキルト指定
  int setEchoStipple(
    int frSize,
    int distance,
    int space,
  ) {
    return _embBindings.setEchoStipple(
        frSize, distance, space, _gHandleStipple);
  }

  ///27.3.	デコラティブ指定
  int setDecoFillStipple(bool decorativeType, int decorativeNo, int frSize,
      int distance, int fillsize) {
    return _embBindings.setDecoFillStipple(decorativeType, decorativeNo, frSize,
        distance, fillsize, _gHandleStipple);
  }

  // ///27.9.	スティップリング処理完了
  // int completeStipple() {
  //   return _embBindings.completeStipple();
  // }

  ///28.4.	アウトラインデータ保存
  int saveOutline(String name) {
    _fileName = StoC(name);
    return _embBindings.saveOutline(_fileName);
  }

  ///29.1.	複数選択モードに入る
  void enterMultiSelectMode() {
    return _embBindings.enterMultiSelectMode();
  }

  ///29.2.	複数選択モードを終了する
  void exitMultiSelectMode() {
    return _embBindings.exitMultiSelectMode();
  }

  ///29.3.	複数選択
  int selectMultiEmb(MemHandle_t gHandle, int selectCondition) {
    return _embBindings.selectMultiEmb(gHandle, selectCondition);
  }

  ///31.2.	模様の並び(縫製)順を一つ後ろにする
  int changeOrderNext() {
    return _embBindings.changeOrderNext();
  }

  ///31.3.	模様の並び(縫製)順を一つ前にする
  int changeOrderPrevious() {
    return _embBindings.changeOrderPrevious();
  }

  ///31.4.	模様の並び(縫製)順を最後にする
  int changeOrderLast() {
    return _embBindings.changeOrderLast();
  }

  ///31.5.	模様の並び(縫製)順を最初にする
  int changeOrderFirst() {
    return _embBindings.changeOrderFirst();
  }

  ///33.1.	縫製前処理
  int prepareSewing() {
    return _embBindings.prepareSewing();
  }

  ///34.1.	しつけ糸追加
  int addBasting() {
    return _embBindings.addBasting(_gHandleBasting);
  }

  ///34.2.	しつけ糸削除
  int deleteBasting() {
    return _embBindings.deleteBasting();
  }

  ///34.3.	針数から針位置を取得する
  int getNeedlePosition(int stitch) {
    return _embBindings.getNeedlePosition(stitch, _needlePos);
  }

  ///34.4.	縫製開始位置の座標を取得する
  int getNeedleStartPosition(position) {
    return _embBindings.getNeedleStartPosition(position, _needlePos);
  }

  ///34.5.	縫製情報取得
  int getEmbSewInfo() {
    return _embBindings.getEmbSewInfo(_sewingInfo);
  }

  ///35.1.	全グループハンドル取得
  int getGroupHandleAll() {
    return _embBindings.getGroupHandleAll(_grpHArray, _handleNum);
  }

  ///35.3.	指定グループのイメージデータの抽出
  int getSelectedGroupARGBImage(
      MemHandle_t gHandle, int centerType, int scale, bool imageType) {
    return _embBindings.getSelectedGroupARGBImage(
        gHandle, centerType, scale, imageType, _embImageInfo);
  }

  ///35.4.	リアルイメージデータの取得
  int getRealImage() {
    bool isMdc = false;
    bool backDisplay = true;
    bool displayPosition = true;
    int scale = 0;
    return _embBindings.getRealImage(
        isMdc, backDisplay, displayPosition, scale, _realImageInfo);
  }

  ///35.5.	インフォメーションイメージデータの取得
  int getInfoImage(bool Display, int backColor) {
    return _embBindings.getInfoImage(Display, backColor, _infoImage);
  }

  ///35.6.	刺繍領域情報取得
  int getEmbArea() {
    return _embBindings.getEmbArea(_area);
  }

  ///getSelectedGroupARGBImageFile
  int getSelectedGroupARGBImageFile(MemHandle_t gHandle, int centerType,
      int scale, bool imageType, String name) {
    _outFile = StoC(name);
    return _embBindings.getSelectedGroupARGBImageFile(gHandle, centerType,
        scale, imageType, _size_x, _size_y, _pageNum, _outFile);
  }

  ///35.7.	指定グループのイメージデータの削除
  int delSelectedGroupImage() {
    return _embBindings.delSelectedGroupImage(_embImageInfo);
  }

  ///35.8.	リアルイメージデータの削除
  int delRealImage() {
    return _embBindings.delRealImage(_realImageInfo.ref);
  }

  ///35.9.	インフォメーションイメージデータの削除
  int delInfoImage() {
    return _embBindings.delInfoImage(_embImageInfo.ref.embImage.ref);
  }

  ///35.10.	カレントグループのハンドルを返す
  int getCurrentGroupHandle() {
    return _embBindings.getCurrentGroupHandle(_gHandleEmb);
  }

  int getEmbSewingThreadInfoAll() {
    return _embBindings.getEmbSewingThreadInfoAll(
        threadInfo, threadInfoNum, threadInfoCurIdx);
  }

  int checkPatternNoMirrorNoCharacter() {
    return _embBindings.checkPatternNoMirrorNoCharacter(gHandleEmb.value, attr);
  }

  /// スキャン画像を取得する ( テストモード64. Scan Check 専用 )
  int getCameraScanTestModeImage(int kind) {
    return _embBindings.getCameraScanTestModeImage(kind, _infoImage);
  }

  ///～～～～～～追加分～～～～～～

  int isEmbCustomDesign() {
    return _embBindings.isEmbCustomDesign(_pResult);
  }

  bool getEmbMarkPatCntAutoCustom() {
    return _embBindings.getEmbMarkPatCntAutoCustom();
  }

  int getEmbDensity() {
    return _embBindings.getEmbDensity(_pDensity);
  }

  int resetEmbBorderMark() {
    return _embBindings.resetEmbBorderMark();
  }

  // int setEmbBorderMark(int MarkPos) {
  //   return _embBindings.setEmbBorderMark(MarkPos);
  // }

  int getEmbBorderMark() {
    return _embBindings.getEmbBorderMark(_pMarkPos);
  }

  bool isEmbMarkPatCntMode() {
    return _embBindings.isEmbMarkPatCntMode();
  }

  int getCurrentStitchNumber() {
    return _embBindings.getCurrentStitchNumber();
  }

  int setCurrentStitchNumber(int NeedleCount) {
    return _embBindings.setCurrentStitchNumber(NeedleCount);
  }

  void setEmbSewNonStopSewing(bool setData) {
    return _embBindings.setEmbSewNonStopSewing(setData);
  }

  bool isEmbSewOneColorNG() {
    return _embBindings.isEmbSewOneColorNG();
  }

  bool isEmbSewOneColorON() {
    return _embBindings.isEmbSewOneColorON();
  }

  int setEmbSewPatternConnectReserve() {
    return _embBindings.setEmbSewPatternConnectReserve();
  }

  bool embEditPositionNG() {
    return _embBindings.isEmbSewOneColorON();
  }

  int resetStbMode() {
    return _embBindings.resetStbMode();
  }

  int setStbMode() {
    return _embBindings.setStbMode();
  }

  int moveQuiltEmb(int dirX, int dirY, bool flgLongPress) {
    return _embBindings.moveQuiltEmb(dirX, dirY, flgLongPress);
  }

  int rotateQuiltEmb(int angle) {
    return _embBindings.rotateQuiltEmb(angle);
  }

  int changeSizeQuiltEmb(int type, int step) {
    return _embBindings.changeSizeQuiltEmb(type, step);
  }

  int getThreadColor61Table(int brandCode) {
    return _embBindings.getThreadColor61Table(brandCode, tcTable);
  }

  int saveUndoRedoFile() {
    return _embBindings.saveUndoRedoFile(fileName);
  }

  int undoRedo() {
    return _embBindings.undoRedo(fileName);
  }

  int changeQuiltColorByPalette(int colorNum, int Brand, int index) {
    return _embBindings.changeQuiltColorByPalette(colorNum, Brand, index);
  }

  int getQuiltColorInfo(int colorNum) {
    return _embBindings.getQuiltColorInfo(colorNum, colorInfo);
  }

  int cancelStipple() {
    return _embBindings.cancelStipple();
  }

  int failureCancelStipple() {
    return _embBindings.failureCancelStipple();
  }

  int checkBeforeBorderProc() {
    return _embBindings.checkBeforeBorderProc();
  }

  int checkBorderMarkStipple() {
    return _embBindings.checkBorderMarkStipple();
  }

  int scalingQuiltEmb(int scale) {
    return _embBindings.scalingQuiltEmb(scale);
  }

  int startPointSetQuiltEmb(int point) {
    return _embBindings.startPointSetQuiltEmb(point);
  }

  int selectPartsQuitEmb(int select) {
    return _embBindings.selectPartsQuitEmb(select);
  }

  /// つなぎ
  void embMarkPatCnctEditParamSet(_embPos, int cnctSetting, int angle) {
    return _embBindings.embMarkPatCnctEditParamSet(_embPos, cnctSetting, angle);
  }

  /// PANEL_API MarkPatCnctSetting_t embMarkPatCnctSettingGet(void);
  /// つなぎコネクト位置の取得
  int embMarkPatCnctSettingGet() {
    return _embBindings.embMarkPatCnctSettingGet();
  }

  void embMarkPatCnctDistanceGet(_markDis) {
    return _embBindings.embMarkPatCnctDistanceGet(_markDis);
  }

  /// つなぎ 画像表示用パラメータ取得
  void embMarkPatCnctImgDrawPrmGet(markNo, _drawParam) {
    return _embBindings.embMarkPatCnctImgDrawPrmGet(markNo, _drawParam);
  }

  /// スノーマンマーク表示用パラメータ取得
  void embMarkPatCnctSnowmanDrawPrmGet(markNo, _snowmanParam) {
    return _embBindings.embMarkPatCnctSnowmanDrawPrmGet(markNo, _snowmanParam);
  }

  ///変数

  static Pointer<MemHandle_t> get gHandleEmb => _gHandleEmb;
  static set gHandleEmb(Pointer<MemHandle_t> value) {
    _gHandleEmb = value;
  }

  static Pointer<MemHandle_t> _gHandleEmb = () {
    final pointer = ffi.calloc<MemHandle_t>();
    pointer.value = panellib.handle;
    return pointer;
  }();

  static Pointer<MemHandle_t> get gHandleEmbPrev => _gHandleEmbPrev;
  static set gHandleEmbPrev(Pointer<MemHandle_t> value) {
    _gHandleEmbPrev = value;
  }

  static Pointer<MemHandle_t> _gHandleEmbPrev = () {
    final pointer = ffi.calloc<MemHandle_t>();
    pointer.value = panellib.handle;
    return pointer;
  }();

  static Pointer<embInfo_t> get embInfo => _embInfo;
  static set embInfo(Pointer<embInfo_t> value) {
    _embInfo = value;
  }

  static Pointer<embInfo_t> _embInfo = () {
    final pointer = embInfo_t.allocate();
    return pointer;
  }();

  static Pointer<Pointer<threadInfo_t>> get threadInfo => _threadInfo;
  static set threadInfo(Pointer<Pointer<threadInfo_t>> value) {
    _threadInfo = value;
  }

  static Pointer<Pointer<threadInfo_t>> _threadInfo = () {
    final pointer = threadInfo_t.allocate_p();
    pointer.value = threadInfo_t.allocate();
    return pointer;
  }();

  static Pointer<Int32> get infoNum => _infoNum;
  static set threadinfoNum(Pointer<Int32> value) {
    _infoNum = value;
  }

  static Pointer<Int32> _infoNum = () {
    final pointer = ffi.calloc<Int32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<Int32> get threadInfoNum => _threadInfoNum;
  static set threadInfoNum(Pointer<Int32> value) {
    _threadInfoNum = value;
  }

  static Pointer<Int32> _threadInfoNum = () {
    final pointer = ffi.calloc<Int32>();
    pointer.value = 0;
    return pointer;
  }();

  //static Pointer<Char> get outFile =>
  //    _outFile;
  //static set outFile(Pointer<Char> value){
  //  _outFile = value ;
  //}
  //static Pointer<Char> _outFile=(){
  //  final pointer = ffi.calloc<Char>();
  //  return pointer;
  //}();

  static Pointer<Pointer<realImg_t>> get drawInfoArray => _drawInfoArray;
  static set drawInfo(Pointer<Pointer<realImg_t>> value) {
    _drawInfoArray = value;
  }

  static Pointer<Pointer<realImg_t>> _drawInfoArray = () {
    final pointer = realImg_t.allocate_p();
    pointer.value = realImg_t.allocate();
    return pointer;
  }();

  static Pointer<embImageInfo_t> get embImageInfo => _embImageInfo;
  static set embImageArray(Pointer<embImageInfo_t> value) {
    _embImageInfo = value;
  }

  static Pointer<embImageInfo_t> _embImageInfo = () {
    final pointer = embImageInfo_t.allocate();
    return pointer;
  }();

  static Pointer<Int32> get embImageNum => _embImageNum;
  static set embImageNum(Pointer<Int32> value) {
    _embImageNum = value;
  }

  static Pointer<Int32> _embImageNum = () {
    final pointer = ffi.calloc<Int32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<UnsignedChar> get pRGBCode => _pRGBCode;
  static set pRGBCode(Pointer<UnsignedChar> value) {
    _pRGBCode = value;
  }

  static Pointer<UnsignedChar> _pRGBCode = () {
    final pointer = ffi.calloc<UnsignedChar>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<Pointer<UnsignedShort>> get colorName => _colorName;
  static set colorName(Pointer<Pointer<UnsignedShort>> value) {
    _colorName = value;
  }

  static Pointer<Pointer<UnsignedShort>> _colorName = () {
    final pointer = ffi.calloc<Pointer<UnsignedShort>>();
    pointer.value = ffi.calloc<UnsignedShort>();
    pointer.value.value = 0;
    return pointer;
  }();

  static Pointer<Char> get fileName => _fileName;
  static set fileName(Pointer<Char> value) {
    _fileName = value;
  }

  static Pointer<Char> _fileName = () {
    final pointer = ffi.calloc<Char>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<MemHandle_t> get dupHArray => _dupHArray;
  static set dupHArray(Pointer<MemHandle_t> value) {
    _dupHArray = value;
  }

  static Pointer<MemHandle_t> _dupHArray = () {
    final pointer = ffi.calloc<MemHandle_t>();
    pointer.value = panellib.handle;
    return pointer;
  }();

  static Pointer<Int32> get handleNum16_5 => _handleNum16_5;
  static set handleNum16_5(Pointer<Int32> value) {
    _handleNum16_5 = value;
  }

  static Pointer<Int32> _handleNum16_5 = () {
    final pointer = ffi.calloc<Int32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<MemHandle_t> get borderHArray => _borderHArray;
  static set borderHArray(Pointer<MemHandle_t> value) {
    _borderHArray = value;
  }

  static Pointer<MemHandle_t> _borderHArray = () {
    final pointer = ffi.calloc<MemHandle_t>();
    pointer.value = panellib.handle;
    return pointer;
  }();

  static Pointer<Int32> get handleNum => _handleNum;
  static set handleNum(Pointer<Int32> value) {
    _handleNum = value;
  }

  static Pointer<Int32> _handleNum = () {
    final pointer = ffi.calloc<Int32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<Int32> _division6_4 = () {
    final pointer = ffi.calloc<Int32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<Int32> _division6_5 = () {
    final pointer = ffi.calloc<Int32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<Int32> _division6_6 = () {
    final pointer = ffi.calloc<Int32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<quiltSashesLimit_t> get sizeLimit => _sizeLimit;
  static set sizeLimit(Pointer<quiltSashesLimit_t> value) {
    _sizeLimit = value;
  }

  static Pointer<quiltSashesLimit_t> _sizeLimit = () {
    final pointer = quiltSashesLimit_t.allocate();
    return pointer;
  }();

  static Pointer<MemHandle_t> get gHandleApp => _gHandleApp;
  static set gHandleApp(Pointer<MemHandle_t> value) {
    _gHandleApp = value;
  }

  static Pointer<MemHandle_t> _gHandleApp = () {
    final pointer = ffi.calloc<MemHandle_t>();
    pointer.value = panellib.handle;
    return pointer;
  }();

  //static Pointer<EmbBorder_t> get borderHandle =>
  //    _borderHandle;
  //static set borderHandle(Pointer<EmbBorder_t> value) {
  //  _borderHandle = value;
  //}
  //static Pointer<EmbBorder_t> _borderHandle=(){
  //  final pointer = EmbBorder_t.allocate();
  //  pointer.ref.Link.parentGroup=_emblib.handle;
  //  pointer.ref.Link.Next=_emblib.handle;
  //  pointer.ref.Link.Prev=_emblib.handle;
  //  return pointer;
  //}();

  static Pointer<MemHandle_t> get grpHArray => _grpHArray;
  static set grpHArray(Pointer<MemHandle_t> value) {
    _grpHArray = value;
  }

  static Pointer<MemHandle_t> _grpHArray = () {
    final pointer = ffi.calloc<MemHandle_t>();
    pointer.value = panellib.handle;
    return pointer;
  }();

  //static Pointer<Pointer<Uint32>> get grpHArray =>
  //    _grpHArray;
  //static set grpHArray(Pointer<Pointer<Uint32>> value) {
  //  _grpHArray = value;
  //}
  //static Pointer<Pointer<Uint32>> _grpHArray = (){
  //  final pointer = ffi.calloc<Pointer<Uint32>>();
  //  pointer.value=ffi.calloc<Uint32>();
  //  pointer.value.value=0;
  //  return pointer;
  //}();

  static Pointer<Int16> get colors => _colors;
  static set colors(Pointer<Int16> value) {
    _colors = value;
  }

  static Pointer<Int16> _colors = () {
    final pointer = ffi.calloc<Int16>();
    pointer.value = 0;
    return pointer;
  }();
  static Pointer<Pointer<EmbBorder_t>> get borderH => _borderH;
  static set borderH(Pointer<Pointer<EmbBorder_t>> value) {
    _borderH = value;
  }

  static Pointer<Pointer<EmbBorder_t>> _borderH = () {
    final pointer = EmbBorder_t.allocate_p();
    pointer.value = EmbBorder_t.allocate();
//    pointer.value.ref.Link.parentGroup=_emblib.handle;
    pointer.value.ref.Link.Next = panellib.handle;
    pointer.value.ref.Link.Prev = panellib.handle;
    return pointer;
  }();

  static Pointer<MemHandle_t> get gHandleStipple => _gHandleStipple;
  static set gHandleStipple(Pointer<MemHandle_t> value) {
    _gHandleStipple = value;
  }

  static Pointer<MemHandle_t> _gHandleStipple = () {
    final pointer = ffi.calloc<MemHandle_t>();
    pointer.value = panellib.handle;
    return pointer;
  }();

  static Pointer<Int32> _thumbnail = () {
    final pointer = ffi.calloc<Int32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<shuffleBaseColor_t> get shuffle => _shuffle;
  static set shuffle(Pointer<shuffleBaseColor_t> value) {
    _shuffle = value;
  }

  static Pointer<shuffleBaseColor_t> _shuffle = () {
    final pointer = shuffleBaseColor_t.allocate();
    return pointer;
  }();

  static Pointer<MemHandle_t> get gHandleBasting => _gHandleBasting;
  static set gHandleBasting(Pointer<MemHandle_t> value) {
    _gHandleBasting = value;
  }

  static Pointer<MemHandle_t> _gHandleBasting = () {
    final pointer = ffi.calloc<MemHandle_t>();
    pointer.value = panellib.handle;
    return pointer;
  }();

  static Pointer<borderInfo_t> get borderInfo => _borderInfo;
  static set borderInfo(Pointer<borderInfo_t> value) {
    _borderInfo = value;
  }

  static Pointer<borderInfo_t> _borderInfo = () {
    final pointer = borderInfo_t.allocate();
    pointer.ref.parentGroup = panellib.handle;
    return pointer;
  }();

  static Pointer<SSPoint_t> get needlePos => _needlePos;
  static set needlePos(Pointer<SSPoint_t> value) {
    _needlePos = value;
  }

  static Pointer<SSPoint_t> _needlePos = () {
    final pointer = SSPoint_t.allocate();
    return pointer;
  }();

  static Pointer<SSPoint_t> get embPos => _embPos;
  static set embPos(Pointer<SSPoint_t> value) {
    _embPos = value;
  }

  static Pointer<SSPoint_t> _embPos = () {
    final pointer = SSPoint_t.allocate();
    return pointer;
  }();

  static Pointer<Double> get markDis => _markDis;
  static set markDis(Pointer<Double> value) {
    _markDis = value;
  }

  static Pointer<Double> _markDis = () {
    final pointer = ffi.calloc<Double>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<SSPoint_t> get offsetPos => _offsetPos;
  static set offsetPos(Pointer<SSPoint_t> value) {
    _offsetPos = value;
  }

  static Pointer<SSPoint_t> _offsetPos = () {
    final pointer = SSPoint_t.allocate();
    return pointer;
  }();

  static Pointer<EmbMarkPatImgDrawParam_t> get drawParam => _drawParam;
  static set drawParam(Pointer<EmbMarkPatImgDrawParam_t> value) {
    _drawParam = value;
  }

  static Pointer<EmbMarkPatImgDrawParam_t> _drawParam = () {
    final pointer = EmbMarkPatImgDrawParam_t.allocate();
    return pointer;
  }();

  static Pointer<EmbMarkPatSnowmanDrawParam_t> get snowmanParam =>
      _snowmanParam;
  static set snowmanParam(Pointer<EmbMarkPatSnowmanDrawParam_t> value) {
    _snowmanParam = value;
  }

  static Pointer<EmbMarkPatSnowmanDrawParam_t> _snowmanParam = () {
    final pointer = EmbMarkPatSnowmanDrawParam_t.allocate();
    return pointer;
  }();

  static Pointer<savedFileInfo_t> get param => _param;
  static set param(Pointer<savedFileInfo_t> value) {
    _param = value;
  }

  static Pointer<savedFileInfo_t> _param = () {
    final pointer = savedFileInfo_t.allocate();
    return pointer;
  }();

  static Pointer<Char> _outFile = () {
    final pointer = ffi.calloc<Char>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<Uint32> _size_x = () {
    final pointer = ffi.calloc<Uint32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<Uint32> _size_y = () {
    final pointer = ffi.calloc<Uint32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<Int32> _pageNum = () {
    final pointer = ffi.calloc<Int32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<Int32> get parts => _parts;
  static Pointer<Int32> _parts = () {
    final pointer = ffi.calloc<Int32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<RectanArea_t> get area => _area;
  static set parea(Pointer<RectanArea_t> value) {
    _area = value;
  }

  static Pointer<RectanArea_t> _area = () {
    final pointer = ffi.calloc<RectanArea_t>();
    pointer.ref.set();
    return pointer;
  }();

  static Pointer<realImageInfo_t> get realImageInfo => _realImageInfo;
  static set realImageInfo(Pointer<realImageInfo_t> value) {
    _realImageInfo = value;
  }

  static Pointer<realImageInfo_t> _realImageInfo = () {
    final pointer = realImageInfo_t.allocate();
    return pointer;
  }();

  static Pointer<appliquePartsParam_t> get appParam => _appParam;
  static set appParam(Pointer<appliquePartsParam_t> value) {
    _appParam = value;
  }

  static Pointer<appliquePartsParam_t> _appParam = () {
    final pointer = appliquePartsParam_t.allocate();
    return pointer;
  }();

  static Pointer<embImg_t> get infoImage => _infoImage;
  static set infoImage(Pointer<embImg_t> value) {
    _infoImage = infoImage;
  }

  static Pointer<embImg_t> _infoImage = () {
    final pointer = embImg_t.allocate();
    return pointer;
  }();

  static Pointer<Int32> _division = () {
    final pointer = ffi.calloc<Int32>();
    return pointer;
  }();

  static Pointer<edgeToEdgeInfo_t> get edgToEdgInfo => _edgToEdgInfo;
  static set edgToEdgeInfo(Pointer<edgeToEdgeInfo_t> value) {
    _edgToEdgInfo = edgToEdgInfo;
  }

  static Pointer<edgeToEdgeInfo_t> _edgToEdgInfo = () {
    final pointer = edgeToEdgeInfo_t.allocate();
    return pointer;
  }();

  Uint8List getImage() {
    embImageInfo.ref.embImage.ref.imageSize += 138;
    print(embImageInfo.ref.embImage.ref.imageSize.toString());
    final pixel = embImageInfo!.ref.embImage.ref.imageData
        .asTypedList(embImageInfo.ref.embImage.ref.imageSize);
    //saveFile(pixel);
    //delSelectedGroupImage();
    return pixel;
  }

  Uint8List getImage2() {
    return _infoImage.ref.imageData.asTypedList(_infoImage.ref.imageSize);
  }

  ///StringからChar*への変換
  Pointer<Char> StoC(String str) {
    //print(str);
    Pointer<Char> char = ffi.calloc<Char>(255);
    final strCode = str.runes.toList();
    //print(strCode.length);
    int i = 0;
    for (i = 0; i < strCode.length; i++) {
      char.elementAt(i).value = strCode[i];
      //print(String.fromCharCode(char.elementAt(i).value));
    }
    char.elementAt(i).value = 0;
    return char;
  }

  ///デバッグ用
  void saveFile(Uint8List pixels) async {
    final path =
        "/sdcard/Android/data/com.brother.ph.eel.plugin_sample/files/Documents/";
    File('$path/1.bmp').writeAsBytes(pixels, flush: true);
  }

  void saveText(String text) async {
    final path =
        "/sdcard/Android/data/com.brother.ph.eel.plugin_sample/files/Documents/";
    File('$path/text.txt').writeAsString(text, flush: true);
  }

  ///～～～～～追加分～～～～～

  static Pointer<Bool> get pResult => _pResult;
  static set pResult(Pointer<Bool> value) {
    _pResult = pResult;
  }

  static Pointer<Bool> _pResult = () {
    final pointer = ffi.calloc<Bool>();
    return pointer;
  }();

  static Pointer<Uint8> get pDensity => _pDensity;
  static set pDensity(Pointer<Uint8> value) {
    _pDensity = pDensity;
  }

  static Pointer<Uint8> _pDensity = () {
    final pointer = ffi.calloc<Uint8>();
    return pointer;
  }();

  static Pointer<Uint8> get pMarkPos => _pMarkPos;
  static set pMarkPos(Pointer<Uint8> value) {
    _pMarkPos = pMarkPos;
  }

  static Pointer<Uint8> _pMarkPos = () {
    final pointer = ffi.calloc<Uint8>();
    return pointer;
  }();

  static Pointer<Bool> get result97 => _result97;
  static set result97(Pointer<Bool> value) {
    _result97 = result97;
  }

  static Pointer<Bool> _result97 = () {
    final pointer = ffi.calloc<Bool>();
    return pointer;
  }();

  static Pointer<Pointer<BrandColorTbl>> get tcTable => _tcTable;
  static set tcTable(Pointer<Pointer<BrandColorTbl>> value) {
    _tcTable = tcTable;
  }

  static Pointer<Pointer<BrandColorTbl>> _tcTable = () {
    final pointer = ffi.calloc<Pointer<BrandColorTbl>>();
    pointer.value = ffi.calloc<BrandColorTbl>();
    return pointer;
  }();

  static Pointer<Uint32> get recordNum => _recordNum;
  static set recordNum(Pointer<Uint32> value) {
    _recordNum = recordNum;
  }

  static Pointer<Uint32> _recordNum = () {
    final pointer = ffi.calloc<Uint32>();
    return pointer;
  }();

  static Pointer<embSewingInfo_t> get sewingInfo => _sewingInfo;
  static set sewingInfo(Pointer<embSewingInfo_t> value) {
    _sewingInfo = sewingInfo;
  }

  static Pointer<embSewingInfo_t> _sewingInfo = () {
    final pointer = ffi.calloc<embSewingInfo_t>();
    return pointer;
  }();

  static Pointer<embGrp_t> get grpInfo => _grpInfo;
  static set grpInfo(Pointer<embGrp_t> value) {
    _grpInfo = grpInfo;
  }

  static Pointer<embGrp_t> _grpInfo = () {
    final pointer = ffi.calloc<embGrp_t>();
    return pointer;
  }();

  static Pointer<embPtrnInfo_t> get ptrnInfo => _ptrnInfo;
  static set ptrnInfo(Pointer<embPtrnInfo_t> value) {
    _ptrnInfo = ptrnInfo;
  }

  static Pointer<embPtrnInfo_t> _ptrnInfo = () {
    final pointer = ffi.calloc<embPtrnInfo_t>();
    return pointer;
  }();

  static Pointer<RectanArea_t> get elementPos => _elementPos;
  static set elementPos(Pointer<RectanArea_t> value) {
    _elementPos = value;
  }

  static Pointer<RectanArea_t> _elementPos = () {
    final pointer = ffi.calloc<RectanArea_t>();
    return pointer;
  }();

  static Pointer<threadMarkState_t> get mark => _mark;
  static set mark(Pointer<threadMarkState_t> value) {
    _mark = value;
  }

  static Pointer<threadMarkState_t> _mark = () {
    final pointer = ffi.calloc<threadMarkState_t>();
    return pointer;
  }();

  static Pointer<EmbColorGr_t> get colorInfo => _colorInfo;
  static set colorInfo(Pointer<EmbColorGr_t> value) {
    _colorInfo = value;
  }

  static Pointer<EmbColorGr_t> _colorInfo = () {
    final pointer = ffi.calloc<EmbColorGr_t>();
    return pointer;
  }();

  static Pointer<Int32> threadInfoCurIdx = () {
    final pointer = ffi.calloc<Int32>();
    pointer.value = 0;
    return pointer;
  }();

  static Pointer<Bool> attr = ffi.calloc<Bool>();
}
