import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'density_setting_popup_view_interface.freezed.dart';

@freezed
class DensitySettingPopupState with _$DensitySettingPopupState {
  const factory DensitySettingPopupState({
    @Default("100") String densityValue,
    @Default(false) bool isMaxValue,
    @Default(false) bool isMainValue,
  }) = _DensitySettingPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class DensitySettingPopupStateViewInterface
    extends ViewModel<DensitySettingPopupState> {
  DensitySettingPopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress);

  ///
  /// 密度表示テキストスタイルを取得します
  ///
  bool getDensityTextStyle();
}
