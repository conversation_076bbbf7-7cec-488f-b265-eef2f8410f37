import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'under_sewing_setting_popup_view_model.dart';

class UnderSewingSettingPopup extends ConsumerStatefulWidget {
  const UnderSewingSettingPopup({super.key});

  @override
  ConsumerState<UnderSewingSettingPopup> createState() =>
      _UnderSewingSettingPopupState();
}

class _UnderSewingSettingPopupState
    extends ConsumerState<UnderSewingSettingPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(underSewingSettingPopupViewModelProvider);
    final viewModel =
        ref.read(underSewingSettingPopupViewModelProvider.notifier);

    return Row(
      children: [
        const Spacer(
          flex: 571,
        ),
        Expanded(
          flex: 229,
          child: Column(
            children: [
              const Spacer(
                flex: 159,
              ),
              Expanded(
                flex: 1052,
                child: Scaffold(
                  backgroundColor: Colors.transparent,
                  body: pre_edit_toolbar_mdc(
                    child: Column(
                      children: [
                        const Spacer(
                          flex: 37,
                        ),
                        const Expanded(
                          flex: 68,
                          child: Row(
                            children: [
                              Spacer(
                                flex: 52,
                              ),
                              Expanded(
                                flex: 126,
                                child: ico_mdcstitch_fill_undersewin(),
                              ),
                              Spacer(
                                flex: 51,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 24,
                        ),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_str_undersewing_mdc(
                                  text: l10n.icon_00545,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 89,
                        ),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 98,
                                child: grp_btn_on(
                                  onTap: viewModel.onONButtonClicked,
                                  text: l10n.icon_on,
                                  state: state.isOnButtonSelected
                                      ? ButtonState.select
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 9,
                              ),
                              Expanded(
                                flex: 98,
                                child: grp_btn_off(
                                  onTap: viewModel.onOFFButtonClicked,
                                  text: l10n.icon_off,
                                  state: state.isOFFButtonSelected
                                      ? ButtonState.select
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 613,
                        ),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_btn_positive_mdc(
                                  onTap: viewModel.onOkButtonClicked,
                                  text: l10n.icon_ok,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 12,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const Spacer(
                flex: 69,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
