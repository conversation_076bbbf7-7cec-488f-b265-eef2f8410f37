// ignore_for_file: camel_case_types
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';

import '../icon/ico_pr_simulator.dart';
import 'theme_button.dart';

class grp_btn_pr_simulator extends StatelessWidget {
  const grp_btn_pr_simulator({
    super.key,
    this.onTap,
    this.state = ButtonState.normal,
    this.feedBackControl = const FeedBackControl(),
  });

  final void Function()? onTap;
  final ButtonState state;
  final FeedBackControl? feedBackControl;

  @override
  Widget build(BuildContext context) => FeedBackButton(
        feedBackControl: feedBackControl,
        onTap: state == ButtonState.disable ? null : () => onTap?.call(),
        state: state,
        style: ThemeButton.btn_n_size90x62_theme2,
        child: const Row(
          children: [
            Spacer(flex: 23),
            Expanded(
              flex: 41,
              child: Column(
                children: [
                  Spacer(flex: 9),
                  Expanded(
                    flex: 45,
                    child: ico_pr_simulator(),
                  ),
                  Spacer(flex: 8),
                ],
              ),
            ),
            Spacer(flex: 26),
          ],
        ),
      );
}
