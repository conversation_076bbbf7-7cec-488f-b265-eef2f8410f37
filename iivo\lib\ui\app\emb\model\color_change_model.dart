import 'package:flutter/material.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../model/thread_color_model.dart';
import 'pattern_model.dart';
import 'redo_undo_model.dart';
import 'thread_color_list_model.dart';
import 'thread_model.dart';

///
/// パレットで選択したカラーブロックの位置情報
///
class SelectedColorPositionInfo {
  const SelectedColorPositionInfo({required this.index});
  final int index;

  /// インデックスに基づいた位置の計算
  double get left => (7 + 48) * (index % 11);
  double get top => (7 + 48) * (index ~/ 11).toDouble();
}

class BackThreadInfo {
  const BackThreadInfo({
    required this.brand,
    required this.indexInBrand,
  });
  final ThreadBrandName brand;
  final int indexInBrand;
}

///
/// カラー変更ページに関する情報
///
class ColorChangeModel {
  ColorChangeModel._internal();
  factory ColorChangeModel() {
    if (_instance._isInitialized == false) {
      _instance._isInitialized = true;
    }
    return _instance;
  }
  bool _isInitialized = false;
  static final ColorChangeModel _instance = ColorChangeModel._internal();

  ///
  /// 現在のブランド色情報
  ///
  List<BrandColorInfo> currentBrandColorList = [];

  ///
  /// リストパターン線色データ
  ///
  List<ColorListDisplayInfo> displayList = [];

  ///
  /// バックアップの線色
  ///
  Map<MemHandle, List<BackThreadInfo>> threadInfoBackUpMap = {};

  ///
  /// allSelectモードを選択するかどうか
  ///
  bool isAllSelect = false;

  ///
  /// カラーラベルはその時点で選択されます
  ///
  bool isTapColorSelected = true;

  ///
  /// 現在の線色リストで選択されている模様
  ///
  int selectPatternIndex = 0;

  ///
  /// 現在の線色リストクリックの糸色
  ///
  int selectColorIndex = 0;

  ///
  /// 線の色を変更する際に使用できる色のブランド
  ///
  ThreadBrandName threadBrandName = ThreadBrandName.embroideryCustom;

  ///
  /// 選択したカラーブロックの位置情報
  ///
  SelectedColorPositionInfo? selectedColorPositionInfo;

  ///
  /// ブランド名リスト
  ///
  final List<ThreadBrandName> threadBrandNameList = [
    ThreadBrandName.embroideryCustom,
    ThreadBrandName.countryCustom,
    ThreadBrandName.madeiraPoly,
    ThreadBrandName.madeiraRayon,
    ThreadBrandName.suRayon,
    ThreadBrandName.robisonAntonPoly,
    ThreadBrandName.robisonAntonRayon,
    ThreadBrandName.isacord,
    ThreadBrandName.gutermann,
    ThreadBrandName.pacesetterPro,
    ThreadBrandName.polyFast,
    ThreadBrandName.iris,
    ThreadBrandName.floriani,
  ];

  ///
  /// カラーシャッフルモードかどうかを判断する
  ///
  bool isColorShuffling = false;

  ///
  /// 色が変わるかどうかを判断する
  ///
  bool isColorChanged = false;

  ///
  /// 全ブランドのカラー情報
  ///
  final Map<ThreadBrandName, List<BrandColorInfo>> _currentBrandColorListMap =
      {};

  ///
  /// すべてのブランドの色情報を取得する
  ///
  Future<void> getAllCurrentBrandColor() async {
    for (var threadBrandName in threadBrandNameList) {
      ({
        EmbLibraryError errorCode,
        List<BrandColorInfo> brandColorInfoList
      }) threadColorTable =
          EmbLibrary().apiBinding.getThreadColorTable(threadBrandName);

      if (threadColorTable.errorCode == EmbLibraryError.EMB_NO_ERR) {
        _currentBrandColorListMap[threadBrandName] =
            threadColorTable.brandColorInfoList;
      } else {
        _currentBrandColorListMap[threadBrandName] = [];
      }
    }
  }

  ///
  /// 個々のブランドの色情報を取得する
  ///
  List<BrandColorInfo> getBrandColorInfoList(ThreadBrandName threadBrandName) {
    List<BrandColorInfo>? brandColorInfoList =
        _currentBrandColorListMap[threadBrandName];

    if (brandColorInfoList == null || brandColorInfoList.isEmpty) {
      brandColorInfoList = EmbLibrary()
          .apiBinding
          .getThreadColorTable(threadBrandName)
          .brandColorInfoList;
      _currentBrandColorListMap[threadBrandName] = brandColorInfoList;
    }

    return brandColorInfoList;
  }

  ///
  /// サムネイルの取得
  ///
  Image getThumbnailImage() {
    List<EmbGroup> groupList = PatternModel().getAllGroup();

    return Image.memory(groupList[selectPatternIndex]
        .subImage(ScrollCenterType.IMAGE_EDIT_COLOR_CNG, 100));
  }

  ///
  /// ブランド名リストの取得
  ///
  BrandColorInfo? getThreadColor(String threadCode) {
    if (threadCode != "") {
      for (var colorInfo in currentBrandColorList) {
        if (colorInfo.threadCode == int.parse(threadCode)) {
          return colorInfo;
        }
      }
    }
    return null;
  }

  ///
  /// リスト内の線の色の位置を取得します
  ///
  int? getThreadColorIndex(String threadCode) {
    if (threadCode != "") {
      for (int index = 0; index < currentBrandColorList.length; index++) {
        if (currentBrandColorList[index].threadCode == int.parse(threadCode)) {
          return index;
        }
      }
    }
    return null;
  }

  ///
  /// スプールカラーの復元
  ///
  void resetEmbThreadColor() {
    selectedColorPositionInfo = null;

    /// Lib通知
    RedoUndoModel().undoRedo();
    PatternModel().reloadAllPattern();
  }

  ///
  /// パーツ(短冊)の選択
  ///
  void selectPartsInGroupColorChange(int patternIndex, int itemIndex) {
    PatternModel().selectPatternWithGroupIndex(patternIndex);

    /// Lib更新
    EmbLibrary().apiBinding.selectPartsInGroupColorChange(
          PatternModel().getAllGroup()[patternIndex].handle,
          itemIndex,
        );
  }

  ///
  /// 糸色変更　RGB指定
  ///
  void changeColor(BrandColorInfo color) {
    int count = displayList[selectPatternIndex].threadInfoDisplayList.length;
    EmbGroup currentGroup = PatternModel().getCurrentGroup();

    if (isAllSelect == true) {
      /// すべて選択すると、最初から最後までPatternの線の色が選択され、色が変更されます
      for (var index = 0; index < count; index++) {
        EmbLibrary().apiBinding.selectPartsInGroupColorChange(
              currentGroup.handle,
              index,
            );
        EmbLibrary().apiBinding.changeColor(color.r, color.g, color.b);
      }

      /// 最初の線色の画像として表示されます
      EmbLibrary()
          .apiBinding
          .selectPartsInGroupColorChange(currentGroup.handle, 0);
    } else {
      /// 非全選択時における単一線色の変更
      EmbLibrary().apiBinding.changeColor(color.r, color.g, color.b);
    }

    /// ThreadInfoを変更し、キャッシュされたデータを消去し、再取得します
    Pattern currentPattern = PatternModel().getCurrentPattern();
    if (currentPattern is EmbBorder && currentPattern.isRepeatBorder) {
      currentPattern.clearThreadInfoCache();
    } else {
      currentGroup.clearThreadInfoCache();
    }
  }

  ///
  /// 糸色変更　カラーパレットの選択
  ///
  void changeColorByPalette(ThreadBrandName brandCode, int colorIndex) {
    int count = displayList[selectPatternIndex].threadInfoDisplayList.length;
    EmbGroup currentGroup = PatternModel().getCurrentGroup();

    if (isAllSelect == true) {
      /// すべて選択すると、最初から最後までPatternの線の色が選択され、色が変更されます
      for (var index = 0; index < count; index++) {
        EmbLibraryError selectPartsInGroupColorChangeError =
            EmbLibrary().apiBinding.selectPartsInGroupColorChange(
                  currentGroup.handle,
                  index,
                );
        if (selectPartsInGroupColorChangeError != EmbLibraryError.EMB_NO_ERR) {
          continue;
        }

        EmbLibraryError changeColorByPaletteError =
            EmbLibrary().apiBinding.changeColorByPalette(brandCode, colorIndex);

        if (changeColorByPaletteError != EmbLibraryError.EMB_NO_ERR) {
          continue;
        }
      }

      /// 最初の線色の画像として表示されます
      EmbLibrary()
          .apiBinding
          .selectPartsInGroupColorChange(currentGroup.handle, 0);
    } else {
      /// 非全選択時における単一線色の変更
      EmbLibrary().apiBinding.changeColorByPalette(brandCode, colorIndex);
    }

    /// ThreadInfoを変更し、キャッシュされたデータを消去し、再取得します
    Pattern currentPattern = PatternModel().getCurrentPattern();
    if (currentPattern is EmbBorder && currentPattern.isRepeatBorder) {
      currentPattern.clearGroupImageCache();
      currentPattern.clearThreadInfoCache();
    } else {
      currentGroup.clearMainImageCache();
      currentGroup.clearThreadInfoCache();
    }
  }

  ///
  /// ブランドカラー情報の取得
  ///
  void updateBrandColorInfoList() {
    currentBrandColorList.clear();

    List<BrandColorInfo> brandColorInfoList =
        getBrandColorInfoList(threadBrandName);

    currentBrandColorList.addAll(brandColorInfoList);
  }

  ///
  /// ブランド名を取得する
  ///
  String getThreadBrandName() =>
      ThreadColorModel().getThreadBrandName(threadBrandName);

  ///
  /// 色変更ページの線色情報取得
  /// libの情報をmodel用の線色情報に変換する
  ///
  void updateDisplayList({
    required int selectedPatternIndex,
    required int selectedColorItemIndex,
    required bool isAllSelectOn,
    required int zoomScale,
  }) {
    List<EmbGroup> groupList = PatternModel().getAllGroup();
    displayList.clear();

    for (int index = 0; index < groupList.length; index++) {
      List<ThreadColorDispInfo> displayThreadInfoList =
          _getThreadInfoDisplayList(
        isAllSelectOn: isAllSelectOn,
        isSelectedPattern: selectedPatternIndex == index,
        selectedColorItemIndex: selectedColorItemIndex,
        threadInfoList: groupList[index].threadInfo,
      );

      /// 線の色リストのサムネイル画像
      final Widget patternImage =
          ThreadColorListModel().getEmbGroupThreadImageList(
        groupList[index],
        ScrollCenterType.IMAGE_EDIT_COLOR_CNG,
        zoomScale: zoomScale,
      );

      displayList.add(ColorListDisplayInfo(
          patternImage: patternImage,
          threadInfoDisplayList: displayThreadInfoList));
    }
  }

  ///
  ///  ColorChangeページ に移動すると、現在のすべての線色がバックアップされます。
  ///
  void backupThreadInfo() {
    if (threadInfoBackUpMap.isNotEmpty) {
      threadInfoBackUpMap.clear();
    } else {
      /// Do nothing
    }

    PatternModel().getAllGroup().forEach((group) {
      List<ThreadInfo> threadInfoList = group.threadInfo;
      List<BackThreadInfo> backThreadInfoList = [];

      for (var threadInfo in threadInfoList) {
        ThreadBrandName threadBrandName = ThreadBrandName.values.firstWhere(
            (brandName) => threadInfo.brandCode == brandName.number);

        List<BrandColorInfo> brandColorInfoList =
            getBrandColorInfoList(threadBrandName);

        backThreadInfoList.add(
          BackThreadInfo(
              brand: threadBrandName,
              indexInBrand: brandColorInfoList.indexWhere(
                  (element) => element.threadCode == threadInfo.threadCode)),
        );
      }

      final newEntries = <int, List<BackThreadInfo>>{
        group.handle: backThreadInfoList
      };
      threadInfoBackUpMap.addEntries(newEntries.entries);
    });
  }

  ///
  /// 表示用の線色情報の取得
  ///
  List<ThreadColorDispInfo> _getThreadInfoDisplayList({
    required List<ThreadInfo> threadInfoList,
    required bool isSelectedPattern,
    required bool isAllSelectOn,
    required int selectedColorItemIndex,
  }) {
    List<ThreadColorDispInfo> displayThreadInfoList = [];
    bool isThreadColorDefault = ThreadModel.getThreadColor();

    for (int index = 0; index < threadInfoList.length; index++) {
      displayThreadInfoList.add(ThreadColorDispInfo(
        isSelected: isSelectedPattern &&
            (isAllSelectOn || selectedColorItemIndex == index),
        isThreadNotSewing: threadInfoList[index].notSewing,
        treadColor: threadInfoList[index].colorRGB,
        threadCode: ThreadModel.getThreadCode(threadInfoList[index].threadCode,
            threadInfoList[index].threadCodeDigit, isThreadColorDefault),
        threadBrandName: _getThreadBrandName(threadInfoList[index].brandCode,
            threadInfoList[index].threadCode, isThreadColorDefault),
        threadColorName: getThreadColorName(threadInfoList[index].index300,
            threadInfoList[index].threadCode, isThreadColorDefault),
        threadSewingTime: PatternModel()
            .changeLibSewingTimeToUiSewingTime(threadInfoList[index].sewingTime)
            .toString(),
        appliqueIcon: ThreadModel.getAppliqueIcon(
          threadInfoList[index].threadCode,
          false,
          isThreadColorDefault,
        ),
      ));
    }

    return displayThreadInfoList;
  }

  ///
  /// ブランド名の取得
  ///
  String _getThreadBrandName(
      int brandCode, int threadCode, bool isThreadColorDefault) {
    if (isThreadColorDefault == false ||
        ThreadModel.isAppliqueThreadCode(threadCode)) {
      return '';
    }

    ThreadBrandName threadBrandName =
        ThreadBrandName.getValuesByNumber(brandCode);

    return ThreadColorModel().getThreadBrandName(threadBrandName);
  }

  ///
  /// カラー名の取得
  ///
  String getThreadColorName(
      int index300, int threadCode, bool isThreadColorDefault) {
    if (isThreadColorDefault == true) {
      return '';
    }

    return ThreadColorModel().getThreadColorNameWithIndex300(index300);
  }

  ///
  /// カラーシャッフルの可用性状況を確認する
  ///
  static bool checkColorShuffle() {
    return EmbLibrary().apiBinding.checkColorShuffle() ==
        EmbLibraryError.EMB_NO_ERR;
  }

  void reset() {
    isColorChanged = false;
    isAllSelect = false;
    selectPatternIndex = 0;
    selectColorIndex = 0;
    threadInfoBackUpMap.clear();
  }
}
