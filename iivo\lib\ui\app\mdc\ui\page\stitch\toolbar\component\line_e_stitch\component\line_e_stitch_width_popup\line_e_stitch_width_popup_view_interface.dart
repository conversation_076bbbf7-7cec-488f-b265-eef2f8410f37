import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'line_e_stitch_width_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class LineEStitchWidthState with _$LineEStitchWidthState {
  const factory LineEStitchWidthState({
    required String widthDisplayValue,
    required bool isDefaultStyle,
    required bool plusButtonValid,
    required bool minusButtonValid,
  }) = _LineEStitchWidthState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class LineEStitchWidthStateViewInterface
    extends ViewModel<LineEStitchWidthState> {
  LineEStitchWidthStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress);

  ///
  /// 最大幅値
  ///
  int get maxWidthValue;

  ///
  /// 最小幅値
  ///
  int get minWidthValue;

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
