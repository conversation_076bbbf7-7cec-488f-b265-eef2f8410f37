import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/projector/camera_pen/camera_pen_type.dart';
import '../../../../../../../../model/projector_model.dart';
import '../../../../../../../global_popup/global_popup_export.dart';
import '../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../global_popup/global_popups/err_tapering_current_finish_cuetop/err_tapering_current_finish_cuetop_view_model.dart';
import '../../../../../../../global_popup/panel_popup_route.dart';
import '../../../../../model/pattern_model.dart';
import '../../../../../model/pattern_status_model.dart';
import '../../../../../model/preview_model.dart';
import '../../../../component/utl_header/utl_header_view_model.dart';
import '../../common/camera_pen/camera_pen_view_model.dart';
import '../../common/endpoint/endpoint_popup.dart';
import '../utility_view_interface.dart';
import '../utility_view_model.dart';
import 'edit_menu_view_interface.dart';
import 'tapering/tapering_popup.dart';

/// 編集メニューのview _modelに必要な構造
final utilityEditMenuViewModeProvider = StateNotifierProvider.autoDispose<
    UtilityEditMenuViewMode,
    UtilityEditMenuState>((ref) => UtilityEditMenuViewMode(ref));

class UtilityEditMenuViewMode extends UtilityEditMenuViewModelInterface
    with DeviceLibraryEventObserver {
  /// view_dataの初期化データ
  UtilityEditMenuViewMode(this._ref) : super(const UtilityEditMenuState());

  @override
  void build() {
    super.build();
    update();

    PatternStatusModel().listenEditMenuState(_ref, update);
  }

  /// providerのref
  final AutoDisposeStateNotifierProviderRef _ref;

  ///
  /// ViewModel更新
  ///
  @override
  void update() {
    /// modelから編集メニュー設定値を取得して、stateへ保存する
    state = state.copyWith(
      freeMotionState: _getFreeMotionState(),
      repeatState: _getRepeatState(),
      mirrorState: _getMirrorState(),
      needleModeState: _getNeedleModeState(),
      buttonHoleState: _getButtonHoleState(),
      endPointState: _getEndPointState(),
      taperingState: _getTaperingState(),
    );
  }

  ///
  /// フリーモーションモードボタンの状態取得
  ///
  ButtonState _getFreeMotionState() {
    final freeMotion = PatternStatusModel().getEditMenuStatus().freeMotion;
    ButtonState buttonState = ButtonState.normal;
    if (freeMotion == AttribState.disable) {
      buttonState = ButtonState.disable;
    } else if (freeMotion == AttribState.enableOn) {
      buttonState = ButtonState.select;
    } else if (freeMotion == AttribState.enableOff) {
      buttonState = ButtonState.normal;
    } else {
      buttonState = ButtonState.disable;
    }

    return buttonState;
  }

  ///
  /// 連続／単独ぬい切り替えボタンの状態取得
  ///
  ButtonState _getRepeatState() {
    final stitchMode = PatternStatusModel().getEditMenuStatus().stitchMode;
    ButtonState buttonState = ButtonState.normal;
    if (stitchMode == AttribState.disable) {
      buttonState = ButtonState.disable;
    } else if (stitchMode == AttribState.singleSew) {
      buttonState = ButtonState.select;
    } else if (stitchMode == AttribState.continuousSew) {
      buttonState = ButtonState.normal;
    } else {
      buttonState = ButtonState.disable;
    }

    return buttonState;
  }

  ///
  /// 左右反転ボタンの状態取得
  ///
  ButtonState _getMirrorState() {
    final horizontalMirror =
        PatternStatusModel().getEditMenuStatus().horizontalMirror;

    ButtonState buttonState = ButtonState.normal;
    if (horizontalMirror == AttribState.disable) {
      buttonState = ButtonState.disable;
    } else if (horizontalMirror == AttribState.enableOn) {
      buttonState = ButtonState.select;
    } else if (horizontalMirror == AttribState.enableOff) {
      buttonState = ButtonState.normal;
    } else {
      buttonState = ButtonState.disable;
    }

    return buttonState;
  }

  ///
  /// 2本針モード設定ボタンの状態取得
  ///
  ButtonState _getNeedleModeState() {
    final needleNum = PatternStatusModel().getEditMenuStatus().needleNum;
    ButtonState buttonState = ButtonState.normal;
    if (needleNum == AttribState.disable) {
      buttonState = ButtonState.disable;
    } else if (needleNum == AttribState.twinNeedle) {
      buttonState = ButtonState.select;
    } else if (needleNum == AttribState.singleNeedle) {
      buttonState = ButtonState.normal;
    } else {
      buttonState = ButtonState.disable;
    }

    return buttonState;
  }

  ///
  /// ボタンホールの長さボタンの状態取得
  ///
  ButtonState _getButtonHoleState() {
    final bhLength = PatternStatusModel().getEditMenuStatus().bhLength;
    ButtonState buttonState = ButtonState.normal;
    if (bhLength == AttribState.disable) {
      buttonState = ButtonState.disable;
    } else if (bhLength == AttribState.enableOn) {
      buttonState = ButtonState.select;
    } else if (bhLength == AttribState.enableOff) {
      buttonState = ButtonState.normal;
    } else {
      buttonState = ButtonState.disable;
    }

    return buttonState;
  }

  ///
  /// 終点設定ボタンの状態取得
  ///
  ButtonState _getEndPointState() {
    final sewAutoStop = PatternStatusModel().getEditMenuStatus().sewAutoStop;
    ButtonState buttonState = ButtonState.normal;
    if (sewAutoStop == AttribState.disable) {
      buttonState = ButtonState.disable;
    } else if (sewAutoStop == AttribState.enableOn) {
      buttonState = ButtonState.select;
    } else if (sewAutoStop == AttribState.enableOff) {
      buttonState = ButtonState.normal;
    } else {
      buttonState = ButtonState.disable;
    }

    return buttonState;
  }

  ///
  /// テーパリング設定ボタンの状態取得
  ///
  ButtonState _getTaperingState() {
    final sewTapering = PatternStatusModel().getTaperingStatus().sewTapering;
    ButtonState buttonState = ButtonState.normal;
    if (sewTapering == AttribState.disable ||
        ProjectorModel().isUtlProjectorOpened(UtlProjectorType.sewingPattern)) {
      buttonState = ButtonState.disable;
    } else if (sewTapering == AttribState.enableOn) {
      buttonState = ButtonState.select;
    } else if (sewTapering == AttribState.enableOff) {
      buttonState = ButtonState.normal;
    } else {
      buttonState = ButtonState.disable;
    }

    return buttonState;
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// ボタンのクリック関数群
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// フリーモーションモードボタンのクリック関数
  ///
  @override
  void onFreeMotionButtonClick() {
    if (state.freeMotionState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    UtlLibraryError error = UtlLibraryError.utlNoError;
    if (state.freeMotionState == ButtonState.normal) {
      error = PatternDataModel().setFreeMotionMode(true);
    } else if (state.freeMotionState == ButtonState.select) {
      error = PatternDataModel().setFreeMotionMode(false);
    }
    if (error != UtlLibraryError.utlNoError) {
      final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
      if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
        if (bPIFErrorPointer.errorCode == ErrCode_t.ERR_EPS_FINISH.index) {
          errEpsFinishFunc = _onErrEpsFinishOKButtonClicked;
        }
        if (bPIFErrorPointer.errorCode == ErrCode_t.ERR_TAPERING_FINISH.index) {
          errTaperingFinishFunc = _onErrTaperingFinishOKButtonClicked;
        }
        return;
      }
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// View更新
    update();
    _updatePageOtherWidget();
  }

  ///
  /// 模様の頭出しボタンのクリック関数
  ///
  @override
  void onCueTopButtonClick() {
    PatternDataModel().cueingDispPattern();

    final taperingStatus = PatternStatusModel().getTaperingStatus();
    if (taperingStatus.sewTaperingOnOFF == true &&
        taperingStatus.sewTaperingSewingStatus !=
            TaperingSewingStatus.beforeSewing) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TAPERING_CURRENT_FINISH_CUETOP,
      );
      errTaperingCurrentFinishCueTopOkFunc = _clearAppErrorState;
      errTaperingCurrentFinishCueTopCancelFunc = _clearAppErrorState;

      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    PatternDataModel().isNotPatternFirstTime = true;

    _updatePageOtherWidget();
  }

  ///
  /// 連続／単独ぬい切り替えボタンのクリック関数
  ///
  @override
  void onRepeatButtonClick() {
    UtlImageInfo? utlImageInfo;
    if (state.repeatState == ButtonState.normal) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      utlImageInfo = PatternDataModel().setRepeatStatus(false);
    } else if (state.repeatState == ButtonState.select) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      utlImageInfo = PatternDataModel().setRepeatStatus(true);
    } else {
      _utlChangeDisable();
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    /// Model更新
    PreviewDataModel().updatePreviewImage(utlImageInfo);

    /// View更新
    update();
    _updatePageOtherWidget();
  }

  ///
  /// 左右反転ボタンのクリック関数
  ///
  @override
  void onMirrorButtonClick() {
    UtlImageInfo? utlImageInfo;
    if (state.mirrorState == ButtonState.normal) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      utlImageInfo = PatternDataModel().setUtilityMirrorStatus(true);
    } else if (state.mirrorState == ButtonState.select) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      utlImageInfo = PatternDataModel().setUtilityMirrorStatus(false);
    } else {
      _utlChangeDisable();

      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      if (bPIFErrorPointer.errorCode ==
          ErrCode_t.ERR_TAPERING_CURRENT_FINISH_FLIP_ON.index) {
        errTaperingCurrentFinishFlipOnFunc =
            _onErrTaperingCurrentFinishFlipOnOKButtonClicked;
      } else if (bPIFErrorPointer.errorCode ==
          ErrCode_t.ERR_TAPERING_CURRENT_FINISH_FLIP_OFF.index) {
        errTaperingCurrentFinishFlipOffFunc =
            _onErrTaperingCurrentFinishFlipOffOKButtonClicked;
      }

      return;
    }

    /// Model更新
    PreviewDataModel().updatePreviewImage(utlImageInfo);

    /// View更新
    update();
    _updatePageOtherWidget();
  }

  ///
  ///１／２本針切り替えボタンのクリック関数
  ///
  @override
  void onDoubleNeedleButtonClick() {
    if (state.needleModeState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    if (state.needleModeState == ButtonState.normal) {
      PatternDataModel().setDoubleNeedle(true);
    } else {
      PatternDataModel().setDoubleNeedle(false);
    }

    update();
    _updatePageOtherWidget();
  }

  ///
  /// ボタンホールの長さボタンのクリック関数
  ///
  @override
  void onButtonHoleButtonClick() {
    if (state.buttonHoleState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    if (state.buttonHoleState == ButtonState.normal) {
      PatternDataModel().setButtonHole(true);
    } else {
      PatternDataModel().setButtonHole(false);
    }

    update();
    _updatePageOtherWidget();
  }

  ///
  /// 終点設定ボタンのクリック関数
  ///
  @override
  void onEndPointButtonClick(BuildContext context) {
    /// 縫製状態は編集不可
    if (TpdLibrary()
            .apiBinding
            .bpIFGetAppDisplayUtl()
            .utlFuncSetting
            .ref
            .isUtlSewing ==
        true) {
      return;
    }

    if (state.endPointState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// RealPreview画像入るとカメラ機能を閉じる
    /// EndPoint時にボタン状態更新は utlHeaderViewModelProviderで実現
    final utlHeaderFunction = _ref.read(utlHeaderViewModelProvider.notifier);
    utlHeaderFunction.maybeCloseCamera();

    if (state.endPointState == ButtonState.normal) {
      /// 終点設定が開くのを待ちます
      PanelPopupRoute().openPleaseWaitPopup(playPopupSound: false);

      Future(
        () async {
          final Future delayFuture = Future.delayed(const Duration(
              milliseconds: ProjectorModel.projectorStartStopDelayMS));
          await _ref
              .read(cameraPenViewModelProvider.notifier)
              .closeCameraPenAndCameraPenUI(needUpdateProjectorUI: false);
          final Future projectorStartFuture =
              ProjectorModel().openUtlProjector(UtlProjectorType.endpoint);

          await Future.wait([delayFuture, projectorStartFuture]);

          if (!context.mounted) {
            return;
          }

          /// 画像更新
          update();

          /// 終了点設定Popupを開く
          PanelPopupRoute().closePleaseWaitPopup();

          /// Libのキー関数を呼ぶ
          UtlLibrary().apiBinding.startEndPoint();
          _showEndPointPopup(context);
          PatternDataModel().isEndPointPopupOn = true;
          _updatePageOtherWidget();
        },
      );
    } else {
      /// Libのキー関数を呼ぶ
      UtlLibrary().apiBinding.startEndPoint();

      _showEndPointPopup(context);
      PatternDataModel().isEndPointPopupOn = true;
      update();
      _updatePageOtherWidget();
    }
    utlHeaderFunction.update();
  }

  ///
  /// テーパリングボタンのクリック関数
  ///
  @override
  void onTaperingButtonClick(BuildContext context) {
    if (state.taperingState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// lib更新
    var (errorCode: errorCode, imageInfo: imageInfo) =
        UtlLibrary().apiBinding.startTapering();
    if (errorCode == UtlLibraryError.utlErrorInvalid) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else if (errorCode == UtlLibraryError.utlErrorInvalidPanel) {
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    PreviewDataModel().updatePreviewImage(imageInfo);

    /// RealPreview画像入るとカメラ機能を閉じる
    /// EndPoint時にボタン状態更新は utlHeaderViewModelProviderで実現
    final utlHeaderFunction = _ref.read(utlHeaderViewModelProvider.notifier);
    utlHeaderFunction.maybeCloseCamera();

    /// 直接 tapering画面に遷移
    if (state.taperingState == ButtonState.select) {
      _openTaperingPopup(context);

      utlHeaderFunction.update();
      return;
    }

    if (PatternStatusModel().getTaperingStatus().sewTaperingEndStyle !=
        TaperingHowToEnd.TAPERING_END_AUTOSTOP) {
      /// view更新
      update();
      _updatePageOtherWidget();

      /// tapering画面に遷移
      _openTaperingPopup(context);
    } else {
      /// 終了方法が終点設定の場合、投影を開く必要があります。

      PanelPopupRoute().openPleaseWaitPopup(playPopupSound: false);
      Future(() async {
        /// 起動のDelay
        final Future delayFuture = Future.delayed(const Duration(
            milliseconds: ProjectorModel.projectorStartStopDelayMS));

        /// Endpoint投影を開く前に、カメラを閉じます。
        await _ref
            .read(cameraPenViewModelProvider.notifier)
            .closeCameraPenAndCameraPenUI(needUpdateProjectorUI: false);

        /// EndpointのProjector開ける
        final Future openOrCloseProjectorFuture =
            ProjectorModel().openUtlProjector(UtlProjectorType.endpoint);

        /// 起動完成待ち
        await Future.wait([openOrCloseProjectorFuture, delayFuture]);

        if (!context.mounted) {
          return;
        }

        /// View更新
        update();
        PanelPopupRoute().closePleaseWaitPopup();
        _updatePageOtherWidget();

        utlHeaderFunction.update();

        /// tapering画面に遷移
        _openTaperingPopup(context);
      });
    }
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// ポップアップのクリック関数群
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// ポップアップのOKボタンのクリック関数
  /// ぬい終わり位置設定を解除しますが、よろしいですか？
  ///
  void _onErrEpsFinishOKButtonClicked() {
    if (ProjectorModel().isUtlProjectorOpened(UtlProjectorType.endpoint)) {
      ProjectorModel().closeUtlProjector(UtlProjectorType.endpoint).then((_) {
        update();
        _updatePageOtherWidget();
      });
    } else {
      /// Do nothing
    }
  }

  ///
  /// ポップアップのOKボタンのクリック関数
  /// テーパリング設定を終わりますよろしいですか？
  ///
  void _onErrTaperingFinishOKButtonClicked() {
    if (ProjectorModel().isUtlProjectorOpened(UtlProjectorType.endpoint)) {
      ProjectorModel().closeUtlProjector(UtlProjectorType.endpoint).then((_) {
        // Preview IMage更新：Preview_view_model.dartファイルのBuild内で更新を監視
        _updatePageOtherWidget();
        _ref
            .read(utilityViewModelProvider.notifier)
            .updateUtilityPageByChild(UtilityModuleType.tapering);
      });
    } else {
      // Preview IMage更新：Preview_view_model.dartファイルのBuild内で更新を監視
      _ref
          .read(utilityViewModelProvider.notifier)
          .updateUtilityPageByChild(UtilityModuleType.tapering);
    }
  }

  ///
  /// ポップアップのOKボタンのクリック関数
  /// 現在のテーパリング縫製を終了しますよろしいですか？(反転キー押下)
  ///
  void _onErrTaperingCurrentFinishFlipOnOKButtonClicked() {
    PatternDataModel().setUtilityMirrorStatus(true);
    update();
    _updatePageOtherWidget();
  }

  ///
  /// ポップアップのOKボタンのクリック関数
  /// 現在のテーパリング縫製を終了しますよろしいですか？(反転キー押下)
  ///
  void _onErrTaperingCurrentFinishFlipOffOKButtonClicked() {
    PatternDataModel().setUtilityMirrorStatus(false);
    update();
    _updatePageOtherWidget();
  }

  ///
  /// 終点設定ポップアップを開く
  ///
  void _openTaperingPopup(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (_) => TaperingPopup(
          onCloseClick: () {
            /// lib更新
            var (errorCode: errorCode, imageInfo: imageInfo) =
                UtlLibrary().apiBinding.closeTapering();
            if (errorCode == UtlLibraryError.utlErrorInvalid) {
              SystemSoundPlayer().play(SystemSoundEnum.invalid);
              return;
            } else if (errorCode == UtlLibraryError.utlErrorInvalidPanel) {
              return;
            }
            SystemSoundPlayer().play(SystemSoundEnum.accept);

            /// view更新
            PreviewDataModel().updatePreviewImage(imageInfo);
            PopupNavigator.maybePop(context: context);
          },
        ),
        barrier: false,
      ),
    );
  }

  ///
  /// 終点設定ポップアップを開く
  ///
  void _showEndPointPopup(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (_) => const EndPointPopup(),
        barrier: false,
      ),
    ).then((_) {
      update();
      PatternDataModel().isEndPointPopupOn = false;
      _updatePageOtherWidget();
    });
  }

  ///
  /// ページ通知
  ///
  void _updatePageOtherWidget() {
    _ref
        .read(utilityViewModelProvider.notifier)
        .updateUtilityPageByChild(UtilityModuleType.editMenu);
  }

  ///
  /// アプリのエラー状態をクリアする
  ///
  void _clearAppErrorState() {
    GlobalPopupRoute().resetErrorState();
  }

  ///
  /// UTLの変更が無効になっている場合に誤った判断が必要
  ///
  void _utlChangeDisable() {
    UtlLibraryError errorCode =
        UtlLibrary().apiBinding.utlChangeDisable().errorCode;
    if (errorCode == UtlLibraryError.utlErrorInvalid) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }
  }
}
