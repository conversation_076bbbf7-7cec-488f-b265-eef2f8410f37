import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_ledpt_connect_when_utl_view_interface.dart';

final errLedptConnectWhenUtlViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrLedptConnectWhenUtlViewInterface,
            ErrLedptConnectWhenUtlState, BuildContext>(
        (ref, context) => ErrLedptConnectWhenUtlViewModel(ref, context));

class ErrLedptConnectWhenUtlViewModel
    extends ErrLedptConnectWhenUtlViewInterface {
  ErrLedptConnectWhenUtlViewModel(Ref ref, BuildContext context)
      : super(const ErrLedptConnectWhenUtlState(), ref, context);

  @override
  void onOKButtonClicked() {
    final int errCode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERRORLEDPOINTERREMOVEWHENUTL);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }
}
