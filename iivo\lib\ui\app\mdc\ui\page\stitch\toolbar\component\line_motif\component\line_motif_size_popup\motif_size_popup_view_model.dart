import 'dart:typed_data';

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/motif_popup_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../../../model/stitch/line_motif_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'motif_size_popup_view_interface.dart';

final motifSizeViewModelProvider = StateNotifierProvider.autoDispose<
    MotifSizePopupStateViewInterface,
    MotifSizePopupState>((ref) => MotifSizeViewModel(ref));

class MotifSizeViewModel extends MotifSizePopupStateViewInterface {
  MotifSizeViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const MotifSizePopupState(
              isSizeMinusToLimit: false,
              isSizePlusToLimit: false,
              sizeInputValue: "",
              isDefaultStyle: false,
            ),
            ref) {
    update();
  }

  @override
  void update() {
    state = state.copyWith(
      isSizeMinusToLimit: _isMinusToLimit(),
      isSizePlusToLimit: _isPlusToLimit(),
      sizeInputValue: _getSizeDisplayValue(),
      isDefaultStyle: _isDefaultStyle(),
    );
  }

  ///
  /// ステップ量
  ///
  final int _stepValue = 10;

  ///
  /// ステップ量
  ///
  final int _stepValue2 = 5;

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  @override
  int defaultValue = LineMotifModel().defaultSizeValue;

  ///
  /// サイズ値ディスプレイスター
  ///
  bool _isSizeValueDisplayStar =
      LineMotifModel().getSize() != LineMotifModel.sizeNotUpdating
          ? false
          : true;

  ///
  ///　サイズ値
  ///
  int _sizeValue = LineMotifModel().getSize();

  @override
  bool plusSize(bool isLongPress) {
    if (_isSizeValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSizeValueDisplayStar = false;

      ///  Model 更新
      _sizeValue = defaultValue;

      /// View更新
      update();

      return false;
    }
    if (_sizeValue >= LineMotifModel.maxiSizeValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _sizeValue = _sizeValue + _stepValue2 > LineMotifModel.maxiSizeValue
        ? LineMotifModel.maxiSizeValue
        : _sizeValue + _stepValue2;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  bool miniSize(bool isLongPress) {
    if (_isSizeValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSizeValueDisplayStar = false;

      ///  Model 更新
      _sizeValue = defaultValue;

      /// View更新
      update();

      return false;
    }
    if (_sizeValue <= LineMotifModel.miniSizeValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _sizeValue = _sizeValue - _stepValue2 < LineMotifModel.miniSizeValue
        ? LineMotifModel.miniSizeValue
        : _sizeValue - _stepValue2;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineMotifSize.toString());
    if (_isSizeValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int sizeValue = LineMotifModel().getSize();

    /// Model 更新
    LineMotifModel().setSize(_sizeValue);
    if (LineMotifModel().setMdcMotifLinePropertyValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (sizeValue != _sizeValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// Sizeの表示値を取得する
  ///
  String _getSizeDisplayValue() {
    /// cmからmmへ
    double motifSizeValue = _sizeValue / _stepValue;
    if (_isSizeValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return motifSizeValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(motifSizeValue);
  }

  ///
  /// 縮小ボタンの状態の取得
  ///
  bool _isMinusToLimit() {
    if (_isSizeValueDisplayStar) {
      return false;
    }

    if (_sizeValue <= LineMotifModel.miniSizeValue) {
      return true;
    }
    return false;
  }

  ///
  /// 増大ボタン状態の取得
  ///
  bool _isPlusToLimit() {
    if (_isSizeValueDisplayStar) {
      return false;
    }

    if (_sizeValue >= LineMotifModel.maxiSizeValue) {
      return true;
    }
    return false;
  }

  ///
  /// テキストスタイルの取得
  ///
  bool _isDefaultStyle() {
    if (_isSizeValueDisplayStar) {
      return true;
    }

    if (_sizeValue == defaultValue) {
      return true;
    }

    return false;
  }

  ///
  /// 選択されたモチーフサムネイルデータ
  ///
  @override
  Uint8List selectedThumbnailData() {
    bool isCustom = (DrawRegionModel().getPatternInfo().stitch as LineMotifType)
        .motifTypeValue;
    int motifNoValue =
        (DrawRegionModel().getPatternInfo().stitch as LineMotifType)
            .motifNoValue;
    return MotifPopupModel.getSelectedPatternThumbnailData(
        isCustom, motifNoValue);
  }
}
