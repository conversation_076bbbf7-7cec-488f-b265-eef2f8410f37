import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_osae_up_emb_view_interface.dart';

final errOsaeUpEmbViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrOsaeUpEmbViewInterface, ErrOsaeUpEmbState, BuildContext>(
        (ref, context) => ErrOsaeUpEmbViewModel(ref, context));

class ErrOsaeUpEmbViewModel extends ErrOsaeUpEmbViewInterface {
  ErrOsaeUpEmbViewModel(Ref ref, BuildContext context)
      : super(const ErrOsaeUpEmbState(), ref, context);

  ///
  /// leftボタンクリク関数
  ///
  @override
  void onLeftButtonClicked() {
    final int errCode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERROSAEUP);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }
}
