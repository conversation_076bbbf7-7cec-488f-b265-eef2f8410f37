import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../model/preview_area_size_model.dart';
import 'pattern_model.dart';
import 'preview_model.dart';
import 'thread_color_list_model.dart';

///
/// Order の item、画像表示領域の高さと幅
///
const int orderItemImageAreaWidth = 189;
const int orderItemImageAreaHeight = 128;

class OrderModel {
  ///
  /// リストを取得するには、まず画像リストを使用します
  ///
  List<Widget> getOrderDisplayImage({required int zoomScale}) {
    List<Widget> imageList = [];
    List<Pattern> patternList = PatternModel().getAllPattern();

    for (var pattern in patternList) {
      if (pattern is EmbGroup) {
        imageList.add(ThreadColorListModel().getEmbGroupThreadImageList(
          pattern,
          ScrollCenterType.IMAGE_EDIT_CHANGE_ORDER,
          imageAreaWidth: orderItemImageAreaWidth,
          imageAreaHeight: orderItemImageAreaHeight,
          zoomScale: zoomScale,
        ));
      } else if (pattern is EmbBorder) {
        imageList.addAll(
            _getEmbBorderImageList(pattern: pattern, zoomScale: zoomScale));
      } else {
        /// Do noting
      }
    }

    return imageList;
  }

  ///
  /// EmbBorderのすべての画像を取得する
  ///
  List<Widget> _getEmbBorderImageList({
    required EmbBorder pattern,
    required int zoomScale,
  }) {
    List<Widget> imageList = [];
    List<Offset> sizeList = [];
    List<Offset> startPointList = [];
    List<Uint8List> imageDataList = [];
    for (var group in pattern.groupList) {
      /// プレビュー領域に描画された画像の開始点とサイズを取得します
      var list = PreviewModel().getImageTopLeftAndSize(
        group: group,
        centerPoint: embPreviewSizeDot / 2,
        pixelOfOneMm: embPreviewSizeDot.dx / frame297x465MmSize.dx,
      );
      List<Uint8List> groupImageList = group.mainImage(
        ScrollCenterType.IMAGE_EDIT_CHANGE_ORDER,
        zoomScale,
      );
      for (int i = 0; i < list.length; i++) {
        sizeList.add(list[i].size);
        startPointList.add(list[i].topLeft);
        imageDataList.add(groupImageList[i]);
      }
    }

    imageList.add(
      ThreadColorListModel().createItemImageFromPreviewData(
        imageAreaWidth: orderItemImageAreaWidth,
        imageAreaHeight: orderItemImageAreaHeight,
        imageDataList: imageDataList,
        sizeList: sizeList,
        startPointList: startPointList,
      ),
    );

    return imageList;
  }

  ///
  /// 模様の並び(縫製)順を一つ後ろにする
  ///
  static EmbLibraryError changeOrderNext() {
    EmbLibraryError error = EmbLibrary().apiBinding.changeOrderNext();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    /// ここではファサードの再読み込みはなく、既存のデータを並べ替えるだけです
    PatternModel().loadNewPattern(markToReloadPatterns: []);
    return error;
  }

  ///
  /// 模様の並び(縫製)順を一つ前にする
  ///
  static EmbLibraryError changeOrderPrevious() {
    EmbLibraryError error = EmbLibrary().apiBinding.changeOrderPrevious();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    /// ここではファサードの再読み込みはなく、既存のデータを並べ替えるだけです
    PatternModel().loadNewPattern(markToReloadPatterns: []);
    return error;
  }

  ///
  /// 模様の並び(縫製)順を最後にする
  ///
  static EmbLibraryError changeOrderLast() {
    EmbLibraryError error = EmbLibrary().apiBinding.changeOrderLast();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    /// ここではファサードの再読み込みはなく、既存のデータを並べ替えるだけです
    PatternModel().loadNewPattern(markToReloadPatterns: []);
    return error;
  }

  ///
  /// 模様の並び(縫製)順を最初にする
  ///
  static EmbLibraryError changeOrderFirst() {
    EmbLibraryError error = EmbLibrary().apiBinding.changeOrderFirst();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return error;
    }

    /// ここではファサードの再読み込みはなく、既存のデータを並べ替えるだけです
    PatternModel().loadNewPattern(markToReloadPatterns: []);
    return error;
  }

  ///
  /// すべてのパターンの組合状態を取得する
  ///
  static List<bool> getEmbIsBorder() {
    List<bool> isGroupList = [];

    PatternModel().getAllPattern().forEach((pattern) {
      if (pattern is EmbGroup) {
        isGroupList.add(false);
      } else if (pattern is EmbBorder) {
        isGroupList.add(true);
      } else {
        /// Do noting
      }
    });

    return isGroupList;
  }
}
