import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_decorative_fill_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'outline_setting_popup_view_interface.dart';

final outlineSettingPopupViewModelProvider = StateNotifierProvider.autoDispose<
    OutlineSettingPopupViewModel,
    OutlineSettingPopupState>((ref) => OutlineSettingPopupViewModel(ref));

class OutlineSettingPopupViewModel
    extends OutlineSettingStateViewModelInterface {
  OutlineSettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const OutlineSettingPopupState(), ref) {
    _outlineState = SurfaceDecoFillModel().getOutline() !=
            SurfaceDecoFillModel.outlineNotUpdating
        ? DecoFillSettingState.settingCompleted
        : DecoFillSettingState.unknown;
    update();
  }

  ///
  /// 輪郭縫いの状態
  ///
  DecoFillSettingState _outlineState = DecoFillSettingState.settingCompleted;

  ///
  /// 輪郭縫い値
  ///
  MDCIsOnOff _outlineValue = SurfaceDecoFillModel().getOutline();

  @override
  void update() {
    state = state.copyWith(
      isOnButtonSelected: _outlineState == DecoFillSettingState.unknown
          ? false
          : _outlineValue == MDCIsOnOff.mdcIs_on
              ? true
              : false,
      isOFFButtonSelected: _outlineState == DecoFillSettingState.unknown
          ? false
          : _outlineValue == MDCIsOnOff.mdcIs_off
              ? true
              : false,
    );
  }

  @override
  void onOkButtonClicked() {
    ref.read(stitchPageViewModelProvider.notifier).maybeRemovePopupRoute(
        PopupEnum.surfaceDecorativeFillOutline.toString());
    if (_outlineState == DecoFillSettingState.unknown) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    /// outlineの初期値
    MDCIsOnOff oldOutlineValue = SurfaceDecoFillModel().getOutline();

    /// Model 更新
    SurfaceDecoFillModel().setOutline(_outlineValue);
    if (SurfaceDecoFillModel().setDecorativeFillSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (_outlineValue != oldOutlineValue) {
      ResumeHistoryModel().backSnapshot();
    } else {
      /// do nothing
    }

    _outlineState = DecoFillSettingState.settingCompleted;
    CreationModel().changeStitchCreation();
  }

  @override
  void onONButtonClicked() {
    if (state.isOnButtonSelected) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// Model更新
    _outlineValue = MDCIsOnOff.mdcIs_on;
    _outlineState = DecoFillSettingState.change;

    /// view更新
    state = state.copyWith(
      isOnButtonSelected: true,
      isOFFButtonSelected: false,
    );
  }

  @override
  void onOFFButtonClicked() {
    if (state.isOFFButtonSelected) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// Model更新
    _outlineValue = MDCIsOnOff.mdcIs_off;
    _outlineState = DecoFillSettingState.change;

    /// view更新
    state = state.copyWith(
      isOnButtonSelected: false,
      isOFFButtonSelected: true,
    );
  }
}
