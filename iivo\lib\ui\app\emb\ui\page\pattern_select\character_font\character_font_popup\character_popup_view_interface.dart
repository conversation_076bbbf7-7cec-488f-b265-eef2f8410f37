import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'character_popup_view_interface.freezed.dart';

@freezed
class CharacterPopupState with _$CharacterPopupState {
  const factory CharacterPopupState({
    @Default(0) int customData,
  }) = _CharacterPopupState;
}

abstract class CharacterPopupViewModelInterface
    extends ViewModel<CharacterPopupState> {
  CharacterPopupViewModelInterface(super.state, this.ref);

  Ref ref;

  ScrollController scrollController = ScrollController();

  ///
  /// Cancelのクリック関数
  ///
  void onCancelButtonClicked(BuildContext context);

  ///
  /// OKボタンクリックイベント
  ///
  void onOKButtonClicked(BuildContext context, int index);

  ///
  /// フォントの画像を取得します
  ///
  Widget getCharacterFontImage(int index);

  ///
  /// 全文字プレビューの画像を取得します
  ///
  Image getCharacterFontPreviewImage(int index);

  ///
  /// ExclusiveScriptフォントの判断
  ///
  bool isExclusiveScriptType(int index);
}
