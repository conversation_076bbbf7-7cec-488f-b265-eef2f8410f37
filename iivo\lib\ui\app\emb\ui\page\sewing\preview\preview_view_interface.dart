import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../model/preview_model.dart'
    show PatternDisplayInfo, ThreadMarkInfo, Mask, RedPointDisplayInfo;

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'preview_view_interface.freezed.dart';

@freezed
class PreviewState with _$PreviewState {
  const factory PreviewState({
    /// Pattern表示情報
    required List<PatternDisplayInfo> patternDisplayInfoList,

    /// maskボックスがPreview表示領域を超えていないか
    /// 超えた場合、赤いドットは表示されません
    required bool isRedPointOverPreview,

    /// セッティング画面　刺しゅう２　（ページ：9）
    /// 背景色
    required Color backgroundColor,

    /// 赤枠の色
    required Color maskColor,

    /// セッティング画面　刺しゅう１　（ページ：8）
    /// グリッド
    required int gridTypeIndex,

    /// グリッドグリッド
    required Color gridColor,

    /// 枠サイズ
    required Path frameDrawPath,

    /// 枠サイズ色
    required Color frameColor,

    /// 針数から針位置
    required Offset? needlePointerCenterOffset,

    /// 背景スキャンの画像
    required Widget? backgroundImage,

    /// ページ上の糸印の表示情報
    required List<ThreadMarkInfo> threadMarkInfoList,

    /// 投影がオンになっているかどうか
    required bool isProjectorON,

    /// Mask ボックスの表示情報
    Mask? maskDisplayInfo,

    /// Rotateドットの表示位置情報
    RedPointDisplayInfo? rotateRedPoint,

    /// 回転の中心点
    required Offset rotateCenter,

    /// ドラッグまたは長押しで設定された角度
    double? dragAngle,

    /// キルトマスク
    Path? quiltMask,
  }) = _PreviewState;
}

abstract class PreviewViewModelInterface extends ViewModel<PreviewState> {
  PreviewViewModelInterface(
    super.state,
    this.ref,
  );

  ///
  /// providerのref
  ///
  Ref ref;

  ///
  /// Preview領域の画面幅と高さを取得する
  ///
  Offset get getPreviewSize;

  ///
  /// Rotateの前に、Rotate中にUIで使用する必要があるデータをバックアップします
  ///
  void backupPatternDisplayInfoInRotate();

  ///
  /// キーを長押しするとドットを使って「 rotate 」操作をするときの更新関数
  ///
  void updateByRotateLongPress();

  ///
  /// パターンの開始情報をバックアップする
  ///
  void backupPatternDisplayInfoInMove();

  ///
  /// ドラッグ開始時に呼び出す
  ///
  void dargPatternStart(ScaleStartDetails details);

  ///
  /// Patternを選択してドラッグ
  ///
  void dargPattern(ScaleUpdateDetails details);

  ///
  /// ドラッグ終了時に呼び出す
  ///
  void dargPatternEnd();

  ///
  /// 矢印キーを使用してパターンを移動します
  ///
  /// [bool] :  移動時にエッジに到達しているかどうか
  ///           true:エッジに移動されました
  ///           false:まだエッジに移行していません
  ///
  bool moveByArrowKey();

  ///
  /// Rotateドットのドラッグ開始時の関数
  ///
  void dargRotatePointStart(Offset centerPoint, Offset startPoint);

  ///
  /// Rotateドットドラッグ時の関数
  ///
  void dargRotatePoint(Offset currentPoint);

  ///
  /// Rotateドットのドラッグ終了時の関数
  ///
  void dargRotatePointEnd();

  ///
  /// 縦線リストの取得
  ///
  List<double> getGridVerticalList();

  ///
  /// 横線リストの取得
  ///
  List<double> getGridHorizontalList();
}
