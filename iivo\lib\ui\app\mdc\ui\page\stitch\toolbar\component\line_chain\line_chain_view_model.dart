import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../../model/device_info_model.dart';
import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/line_chain_model.dart';
import '../../../../../../model/stitch/toolbar_model.dart';
import '../../../stitch_page_view_model.dart';
import 'line_chain_view_interface.dart';

typedef LineChainViewModelProvider = AutoDisposeStateNotifierProvider<
    LineChainStateViewInterface, LineChainState>;

class LineChainViewModel extends LineChainStateViewInterface {
  LineChainViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineChainState(
              candleSizeValue: "",
              candleThicknessValue: "",
              isSizePopupOn: false,
              isThicknessPopupOn: false,
              isSizeDefaultValue: false,
              isThicknessDefaultValue: false,
            ),
            ref) {
    update();
  }

  @override
  String get textSignal => ToolbarModel.xCharCode;

  @override
  void update() {
    state = state.copyWith(
      candleSizeValue: _getSizeDisplayValue(),
      candleThicknessValue: _getThicknessDisplayValue(),
      isSizeDefaultValue: _getSizeDisplayTextStyle(),
      isThicknessDefaultValue: _getThicknessDisplayTextStyle(),
    );
  }

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  @override
  int get defaultSizeValue => LineChainModel().sizeDefaultValue;
  @override
  int get defaultThicknessValue => LineChainModel().thicknessDefaultValue;

  ///
  /// ステップ量
  ///
  final int _stepValue = 10;

  @override
  void onCandleSizeClick(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineChainSize.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void onCandleThicknessClick(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineChainThickness.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// Sizeの表示値を取得する
  ///
  String _getSizeDisplayValue() {
    int size = LineChainModel().getSize();

    /// cmからmmへ
    double lineZigzagWidthValue = size / _stepValue;
    if (size == LineChainModel.sizeNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }
    if (currentSelectedUnit == Unit.mm) {
      return lineZigzagWidthValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(lineZigzagWidthValue);
  }

  ///
  /// 厚度表示値を取得します
  ///
  String _getThicknessDisplayValue() {
    int thickNess = LineChainModel().getThickness();
    if (thickNess == LineChainModel.thicknessNotUpdating) {
      return "*";
    }
    return LineChainModel().getThickness().toString();
  }

  ///
  /// 表示厚さのテキストスタイルを取得するには
  ///
  bool _getThicknessDisplayTextStyle() {
    int thickNess = LineChainModel().getThickness();
    if (LineChainModel().getThickness() == defaultThicknessValue ||
        thickNess == LineChainModel.thicknessNotUpdating) {
      return true;
    }

    return false;
  }

  ///
  /// 表示サイズのテキストスタイルを取得する
  ///
  bool _getSizeDisplayTextStyle() {
    int size = LineChainModel().getSize();
    if (size == defaultSizeValue || size == LineChainModel.sizeNotUpdating) {
      return true;
    }

    return false;
  }
}
