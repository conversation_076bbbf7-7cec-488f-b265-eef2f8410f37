import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../model/edit_model.dart';
import '../../../model/pattern_model.dart';
import '../common_component/function_provider/size_function_provider/size_function_provider.dart';
import 'bottom/bottom_view_model.dart';
import 'bottom/multiple_selection/multiple_selection.dart';
import 'bottom/multiple_selection/multiple_selection_view_model.dart';
import 'edit_menu/edit_menu_view_model.dart';
import 'edit_menu/thread_color_list/thread_color_list_view_model.dart';
import 'edit_menu/toolbar/alignment/alignment.dart';
import 'edit_menu/toolbar/alignment/alignment_view_model.dart';
import 'edit_menu/toolbar/applique/applique.dart';
import 'edit_menu/toolbar/applique/applique_outline/applique_outline.dart';
import 'edit_menu/toolbar/applique/applique_parts/applique_parts.dart';
import 'edit_menu/toolbar/applique/applique_parts/applique_preview/applique_notsewing/applique_notsewing.dart';
import 'edit_menu/toolbar/applique/applique_parts/applique_preview/applique_preview.dart';
import 'edit_menu/toolbar/applique/applique_parts/applique_preview/external_contours_density/external_contours_density.dart';
import 'edit_menu/toolbar/applique/applique_parts/applique_preview/external_contours_distance/external_contours_distance.dart';
import 'edit_menu/toolbar/applique/applique_parts/applique_preview/external_contours_width/external_contours_width.dart';
import 'edit_menu/toolbar/applique/applique_parts/applique_preview/internal_distance/internal_distance.dart';
import 'edit_menu/toolbar/border/border.dart';
import 'edit_menu/toolbar/border/border_view_model.dart';
import 'edit_menu/toolbar/density/density.dart';
import 'edit_menu/toolbar/density/density_view_model.dart';
import 'edit_menu/toolbar/move/move.dart';
import 'edit_menu/toolbar/move/move_view_model.dart';
import 'edit_menu/toolbar/not_sewing/not_sewing.dart';
import 'edit_menu/toolbar/order_change/order_change.dart';
import 'edit_menu/toolbar/order_change/order_change_view_model.dart';
import 'edit_menu/toolbar/outline/outline.dart';
import 'edit_menu/toolbar/rotate/rotate.dart';
import 'edit_menu/toolbar/rotate/rotate_view_model.dart';
import 'edit_menu/toolbar/size_adjustment/size_adjustment.dart';
import 'edit_menu/toolbar/size_adjustment/size_adjustment_view_model.dart';
import 'edit_menu/toolbar/stippling/decorative_fill_popup/decorative_fill_popup.dart';
import 'edit_menu/toolbar/stippling/stippling.dart';
import 'edit_menu/toolbar/toolbar.dart';
import 'edit_menu/toolbar/toolbar_view_model.dart';
import 'pattern_edit_state.dart';
import 'preview/preview_view_model.dart';
import 'top_bar/top_bar_view_model.dart';

final patternEditViewModelProvider =
    StateNotifierProvider.autoDispose<PatternEditViewModel, PatternEditState>(
        (ref) => PatternEditViewModel(ref));

class PatternEditViewModel extends ViewModel<PatternEditState> {
  PatternEditViewModel(this._ref) : super(const PatternEditState()) {
    PatternModel().selectedZoomScaleInEditPage = zoomList.first;
  }

  ///
  /// Providerのref
  ///
  final Ref _ref;

  ///
  /// TopPage画面のView更新
  ///
  void updateEditPageByChild(ModuleType vm) {
    switch (vm) {
      /// 上部領域
      case ModuleType.topBar:
        _ref.read(previewViewModelProvider.notifier).update();
        break;

      /// 編集メニュー の ツールバー
      case ModuleType.toolBar:
        _ref.read(bottomViewModelProvider.notifier).update();
        _ref.read(topBarViewModelProvider.notifier).update();
        _ref.read(threadColorListViewModelProvider.notifier).update();
        _ref.read(previewViewModelProvider.notifier).update();

        if (_ref.exists(sizeFunctionProvider)) {
          _ref.read(sizeFunctionProvider.notifier).update();
        }
        break;

      /// パターン の 線色リスト
      case ModuleType.colorList:
        _ref.read(topBarViewModelProvider.notifier).update();
        _ref.read(previewViewModelProvider.notifier).update();
        break;

      /// Preview
      case ModuleType.preview:
        if (EditModel().toolbarPopupId == ToolbarPopupId.move) {
          _ref.read(moveViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId ==
            ToolbarPopupId.sizeAdjustment) {
          _ref.read(sizeAdjustmentViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.rotate) {
          _ref.read(embEditRotateViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.density) {
          _ref.read(densityViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId ==
            ToolbarPopupId.multipleSelection) {
          _ref.read(multipleSelectionViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.order) {
          _ref.read(orderChangeViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.alignment) {
          _ref.read(alignmentViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.border) {
          _ref.read(borderViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.edit) {
          _ref.read(toolbarViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.none) {
          _ref.read(threadColorListViewModelProvider.notifier).update();
        }
        _ref.read(topBarViewModelProvider.notifier).update();
        _ref.read(bottomViewModelProvider.notifier).update();

        if (_ref.exists(sizeFunctionProvider)) {
          _ref.read(sizeFunctionProvider.notifier).update();
        }
        break;

      /// multipleSelection Preview
      case ModuleType.multipleSelectionPreview:
        if (_ref.exists(multipleSelectionViewModelProvider)) {
          _ref.read(multipleSelectionViewModelProvider.notifier).update();
        } else if (_ref.exists(alignmentViewModelProvider)) {
          _ref.read(alignmentViewModelProvider.notifier).update();
        }
        break;

      /// ボトムエリア
      case ModuleType.bottom:
        if (_ref.exists(sizeFunctionProvider)) {
          _ref.read(sizeFunctionProvider.notifier).update();
        }

        if (EditModel().toolbarPopupId == ToolbarPopupId.move) {
          _ref.read(moveViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId ==
            ToolbarPopupId.sizeAdjustment) {
          _ref.read(sizeAdjustmentViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.density) {
          _ref.read(densityViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.border) {
          _ref.read(borderViewModelProvider.notifier).update();
        } else if (EditModel().toolbarPopupId ==
            ToolbarPopupId.multipleSelection) {
          _ref.read(editMenuViewModelProvider.notifier).update();
        } else {
          _ref.read(toolbarViewModelProvider.notifier).update();
        }
        if (_ref.exists(sizeFunctionProvider)) {
          _ref.read(sizeFunctionProvider.notifier).update();
        }
        _ref.read(threadColorListViewModelProvider.notifier).update();
        _ref.read(previewViewModelProvider.notifier).update();
        _ref.read(topBarViewModelProvider.notifier).update();
        break;

      /// realPreview
      case ModuleType.realPreview:
        break;
      case ModuleType.multipleSelection:
        _ref.read(bottomViewModelProvider.notifier).update();
        _ref.read(toolbarViewModelProvider.notifier).update();
        _ref.read(editMenuViewModelProvider.notifier).update();
        _ref.read(previewViewModelProvider.notifier).update();

        if (_ref.exists(sizeFunctionProvider)) {
          _ref.read(sizeFunctionProvider.notifier).update();
        }
        break;

      /// プロジェクター
      case ModuleType.projector:
        _ref.read(toolbarViewModelProvider.notifier).update();
        _ref.read(topBarViewModelProvider.notifier).update();
        _ref.read(bottomViewModelProvider.notifier).update();
        _ref.read(threadColorListViewModelProvider.notifier).update();
        _ref.read(previewViewModelProvider.notifier).update();
        _ref.read(editMenuViewModelProvider.notifier).update();

        if (_ref.exists(sizeFunctionProvider)) {
          _ref.read(sizeFunctionProvider.notifier).update();
        }
        break;

      /// 背景スキャン
      case ModuleType.scan:
        _ref.read(editMenuViewModelProvider.notifier).update();

        if (_ref.exists(sizeFunctionProvider)) {
          _ref.read(sizeFunctionProvider.notifier).update();
        }

        if (EditModel().toolbarPopupId == ToolbarPopupId.move) {
          _ref.read(moveViewModelProvider.notifier).updateByScanPopupChange();
        } else if (EditModel().toolbarPopupId ==
            ToolbarPopupId.sizeAdjustment) {
          _ref
              .read(sizeAdjustmentViewModelProvider.notifier)
              .updateByScanPopupChange();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.density) {
          _ref
              .read(densityViewModelProvider.notifier)
              .updateByScanPopupChange();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.border) {
          _ref.read(borderViewModelProvider.notifier).updateByScanPopupChange();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.rotate) {
          _ref
              .read(embEditRotateViewModelProvider.notifier)
              .updateByScanPopupChange();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.alignment) {
          _ref
              .read(alignmentViewModelProvider.notifier)
              .updateByScanPopupChange();
        } else if (EditModel().toolbarPopupId == ToolbarPopupId.order) {
          _ref
              .read(orderChangeViewModelProvider.notifier)
              .updateByScanPopupChange();
        } else if (EditModel().toolbarPopupId ==
            ToolbarPopupId.multipleSelection) {
          _ref
              .read(multipleSelectionViewModelProvider.notifier)
              .updateByScanPopupChange();
        } else {
          _ref
              .read(toolbarViewModelProvider.notifier)
              .updateByScanPopupChange();
        }

        if (_ref.exists(sizeFunctionProvider)) {
          _ref.read(sizeFunctionProvider.notifier).update();
        }
        _ref.read(previewViewModelProvider.notifier).update();
        break;

      /// Rotate
      case ModuleType.beforeRotate:
        _ref
            .read(previewViewModelProvider.notifier)
            .backupPatternDisplayInfoInRotate();
        break;
      case ModuleType.rotateWithKey:
        _ref.read(previewViewModelProvider.notifier).updateByRotateLongPress();
        break;
      case ModuleType.rotateWithRedPoint:
        if (EditModel().toolbarPopupId == ToolbarPopupId.rotate) {
          _ref
              .read(embEditRotateViewModelProvider.notifier)
              .updateByRotateLongPress();
        } else {
          /// Do nothing
        }
        break;

      /// Move
      case ModuleType.moveWithDrag:
        if (_ref.exists(moveViewModelProvider)) {
          _ref.read(moveViewModelProvider.notifier).updateByDragPatternMove();
        }
        break;
      default:
        break;
    }
  }

  ///
  /// 移動ボタンで編集ページを更新
  ///
  /// [bool] : 移動時にエッジに到達しているかどうか
  ///          true:エッジに移動されました false:まだエッジに移行していません
  ///
  bool updateEditPageByMoveButton(EditMoveType vm) {
    switch (vm) {
      case EditMoveType.beforeMove:
        _ref
            .read(previewViewModelProvider.notifier)
            .backupPatternDisplayInfoInMove();
        return false;
      case EditMoveType.moveWithKey:
        final bool isOverFrame =
            _ref.read(previewViewModelProvider.notifier).moveByArrowKey();
        if (_ref.exists(moveViewModelProvider)) {
          _ref.read(moveViewModelProvider.notifier).updateByDragPatternMove();
        }
        return isOverFrame;
      case EditMoveType.moveEnd:
        if (_ref.exists(moveViewModelProvider)) {
          _ref.read(moveViewModelProvider.notifier).update();
        }
        if (_ref.exists(previewViewModelProvider)) {
          _ref.read(previewViewModelProvider.notifier).update();
        }
        if (_ref.exists(topBarViewModelProvider)) {
          _ref.read(topBarViewModelProvider.notifier).update();
        }
        return false;
    }
  }

  ///
  /// 名前付きルートの登録
  ///
  Map<String, PopupRouteBuilder> registerNamedPopup() => {
        PopupEnum.toolBar.toString(): PopupRouteBuilder(
          builder: (context) => const ToolBar(),
          barrier: false,
        ),
        PopupEnum.sizeAdjustment.toString(): PopupRouteBuilder(
          builder: (context) => const SizeAdjustment(),
          barrier: false,
        ),
        PopupEnum.rotate.toString(): PopupRouteBuilder(
          builder: (context) => const Rotate(),
          barrier: false,
        ),
        PopupEnum.multipleSelection.toString(): PopupRouteBuilder(
          builder: (context) => const MultipleSelection(),
          barrier: false,
        ),
        PopupEnum.move.toString(): PopupRouteBuilder(
          builder: (context) => const MovePopup(),
          barrier: false,
        ),
        PopupEnum.density.toString(): PopupRouteBuilder(
          builder: (context) => const DensityPopup(),
          barrier: false,
        ),
        PopupEnum.outline.toString(): PopupRouteBuilder(
          builder: (context) => const OutlinePopup(),
          barrier: false,
        ),
        PopupEnum.alignment.toString(): PopupRouteBuilder(
          builder: (context) => const AlignmentPopup(),
          barrier: false,
        ),
        PopupEnum.stippling.toString(): PopupRouteBuilder(
          builder: (context) => const StipplingPopup(),
          barrier: false,
        ),
        PopupEnum.order.toString(): PopupRouteBuilder(
          builder: (context) => const OrderChange(),
          barrier: false,
        ),
        PopupEnum.surfaceDecoFill.toString(): PopupRouteBuilder(
          builder: (context) => const DecorativeFillPopup(),
          barrier: false,
        ),
        PopupEnum.notSewing.toString(): PopupRouteBuilder(
          builder: (context) => const NotSewingPopup(),
          barrier: false,
        ),
        PopupEnum.border.toString(): PopupRouteBuilder(
          builder: (context) => const BorderPopup(),
          barrier: false,
        ),
        PopupEnum.applique.toString(): PopupRouteBuilder(
          builder: (context) => const AppliquePopup(),
          barrier: false,
        ),
        PopupEnum.appliqueOutline.toString(): PopupRouteBuilder(
          builder: (context) => const AppliqueOutlinePopup(),
          barrier: false,
        ),
        PopupEnum.appliqueParts.toString(): PopupRouteBuilder(
          builder: (context) => const AppliquePartsPopup(),
          barrier: false,
        ),
        PopupEnum.appliquePreview.toString(): PopupRouteBuilder(
          builder: (context) => const AppliquePreviewPopup(),
          barrier: false,
        ),
        PopupEnum.appliquePreviewInternalDistance.toString(): PopupRouteBuilder(
          builder: (context) => const InternalDistancePopup(),
          barrier: false,
        ),
        PopupEnum.appliquePreviewExternalContoursDistance.toString():
            PopupRouteBuilder(
          builder: (context) => const ExternalContoursDistancePopup(),
          barrier: false,
        ),
        PopupEnum.appliquePreviewExternalContoursWidth.toString():
            PopupRouteBuilder(
          builder: (context) => const ExternalContoursWidthPopup(),
          barrier: false,
        ),
        PopupEnum.appliquePreviewExternalContoursDensity.toString():
            PopupRouteBuilder(
          builder: (context) => const ExternalContoursDensityPopup(),
          barrier: false,
        ),
        PopupEnum.appliqueNotSewing.toString(): PopupRouteBuilder(
          builder: (context) => const AppliqueNotSewingPopup(),
          barrier: false,
        ),
      };

  @override
  void dispose() {
    EditModel().toolbarPopupId = ToolbarPopupId.none;
    super.dispose();
  }
}

enum ModuleType {
  topBar,
  toolBar,
  colorList,
  preview,
  multipleSelectionPreview,
  bottom,
  realPreview,
  multipleSelection,
  projector,
  scan,
  cameraPenUI,
  rotateWithKey,
  beforeRotate,
  rotateWithRedPoint,
  moveWithDrag,
}

enum EditMoveType {
  beforeMove,
  moveWithKey,
  moveEnd,
}

enum PopupEnum {
  toolBar,
  sizeAdjustment,
  rotate,
  move,
  density,
  multipleSelection,
  outline,
  alignment,
  stippling,
  order,
  surfaceDecoFill,
  border,
  notSewing,
  applique,
  appliqueOutline,
  appliqueParts,
  appliquePreview,
  appliqueNotSewing,
  appliquePreviewInternalDistance,
  appliquePreviewExternalContoursDistance,
  appliquePreviewExternalContoursWidth,
  appliquePreviewExternalContoursDensity,
}
