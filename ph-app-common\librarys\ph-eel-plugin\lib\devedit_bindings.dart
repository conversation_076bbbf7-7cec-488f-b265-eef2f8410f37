import 'dart:ffi';
import 'dart:io';
import 'dart:typed_data';
import 'package:ffi/ffi.dart' as ffi;
import 'package:flutter/material.dart';

import 'package:ffi/ffi.dart' as ffi;

import 'devedit_bindings_generated.dart';
import 'lib_base.dart';

final DevEditBindings _devbindings = DevEditBindings(panellib);
DevEditBindings getDevEditBindings() => _devbindings;

//

//
// クラス定義
//
class DevEdit {
  int setWidthControl(bool isOn) {
    return _devbindings.setWidthControl(isOn);
  }

  int getWidthControl() {
    return _devbindings.getWidthControl(widthControlIsOn);
  }

  int getWidthControlDefault() {
    return _devbindings.getWidthControlDefault(widthControlIsOn);
  }

  int setFineAdjustVerti(int value) {
    return _devbindings.setFineAdjustVerti(value);
  }

  int getFineAdjustVerti() {
    return _devbindings.getFineAdjustVerti(vertiValue);
  }

  int getFineAdjustVertiValueList() {
    return _devbindings.getFineAdjustVertiValueList(vertiValueInfo);
  }

  int setFineAdjustHoriz(int value) {
    return _devbindings.setFineAdjustHoriz(value);
  }

  int getFineAdjustHoriz() {
    return _devbindings.getFineAdjustHoriz(horizValue);
  }

  int getFineAdjustHorizValueList() {
    return _devbindings.getFineAdjustHorizValueList(horizValueInfo);
  }

  int setPresserFootHeight(int value) {
    return _devbindings.setPresserFootHeight(value);
  }

  int getPresserFootHeight() {
    return _devbindings.getPresserFootHeight(footHeightValue);
  }

  int getPresserFootHeightValueList() {
    return _devbindings.getPresserFootHeightValueList(footHeightValueInfo);
  }

  int setPresserFootPressure(int value) {
    return _devbindings.setPresserFootPressure(value);
  }

  int getPresserFootPressure() {
    return _devbindings.getPresserFootPressure(footPressureValue);
  }

  int getPresserFootPressureValueList() {
    return _devbindings.getPresserFootPressureValueList(footPressureValueInfo);
  }

  int setAutomaticFabricSensorSystem(bool isOn) {
    return _devbindings.setAutomaticFabricSensorSystem(isOn);
  }

  int getAutomaticFabricSensorSystem() {
    return _devbindings.getAutomaticFabricSensorSystem(sensorSystemIsOn);
  }

  int getAutomaticFabricSensorSystemDefault() {
    return _devbindings.getAutomaticFabricSensorSystemDefault(sensorSystemIsOn);
  }

  int setBaseLine(bool isCenter) {
    return _devbindings.setBaseLine(isCenter);
  }

  int getBaseLine() {
    return _devbindings.getBaseLine(isCenter);
  }

  int getBaseLineDefault() {
    return _devbindings.getBaseLineDefault(isCenter);
  }

  int setPivotingHeight(int value) {
    return _devbindings.setPivotingHeight(value);
  }

  int getPivotingHeight() {
    return _devbindings.getPivotingHeight(pivotingHeightValue);
  }

  int getPivotingHeightValueList() {
    return _devbindings.getPivotingHeightValueList(pivotingHeightValueInfo);
  }

  int setFreeMotionFootHeight(int value) {
    return _devbindings.setFreeMotionFootHeight(value);
  }

  int getFreeMotionFootHeight() {
    return _devbindings.getFreeMotionFootHeight(freeMotionValue);
  }

  int getFreeMotionFootHeightValueList() {
    return _devbindings.getFreeMotionFootHeightValueList(freeMotionValueInfo);
  }

  int setDualFeedFeedAdjustment(int value) {
    return _devbindings.setDualFeedFeedAdjustment(value);
  }

  int getDualFeedFeedAdjustment() {
    return _devbindings.getDualFeedFeedAdjustment(feedAdjustmentValue);
  }

  int getDualFeedFeedAdjustmentValueList() {
    return _devbindings
        .getDualFeedFeedAdjustmentValueList(feedAdjustmentValueInfo);
  }

  int setUtlAutoDown(bool isOn) {
    return _devbindings.setUtlAutoDown(isOn);
  }

  int getUtlAutoDown() {
    return _devbindings.getUtlAutoDown(utlAutoDownIsOn);
  }

  int getUtlAutoDownDefault() {
    return _devbindings.getUtlAutoDownDefault(utlAutoDownIsOn);
  }

  int setPressToTrim(bool isOn) {
    return _devbindings.setPressToTrim(isOn);
  }

  int getPressToTrim() {
    return _devbindings.getPressToTrim(pressToTrimIsOn);
  }

  int getPressToTrimDefault() {
    return _devbindings.getPressToTrimDefault(pressToTrimIsOn);
  }

  int setInitialStitchPage(bool isQuilt) {
    return _devbindings.setInitialStitchPage(isQuilt);
  }

  int getInitialStitchPage() {
    return _devbindings.getInitialStitchPage(isQuilt);
  }

  int getInitialStitchPageDefault() {
    return _devbindings.getInitialStitchPageDefault(isQuilt);
  }

  int setReinforcementPriority(bool isOn) {
    return _devbindings.setReinforcementPriority(isOn);
  }

  int getReinforcementPriority() {
    return _devbindings.getReinforcementPriority(priorityIsOn);
  }

  int getReinforcementPriorityDefault() {
    return _devbindings.getReinforcementPriorityDefault(priorityIsOn);
  }

  int setHeelSwitch(int value) {
    return _devbindings.setHeelSwitch(value);
  }

  int getHeelSwitch() {
    return _devbindings.getHeelSwitch(heelSwitchValue);
  }

  int getHeelSwitchValueList() {
    return _devbindings.getHeelSwitchValueList(heelSwitchValueInfo);
  }

  int setSidePedal(int value) {
    return _devbindings.setSidePedal(value);
  }

  int getSidePedal() {
    return _devbindings.getSidePedal(sidePedalValue);
  }

  int setEndPointSettingTemporaryStop(bool isOn) {
    return _devbindings.setEndPointSettingTemporaryStop(isOn);
  }

  int getEndPointSettingTemporaryStop() {
    return _devbindings.getEndPointSettingTemporaryStop(temporaryStopIsOn);
  }

  int getEndPointSettingTemporaryStopDefault() {
    return _devbindings
        .getEndPointSettingTemporaryStopDefault(temporaryStopIsOn);
  }

  int saveLanguage(int value) {
    return _devbindings.saveLanguage(value);
  }

  int getLanguage() {
    return _devbindings.getLanguage(languageValue);
  }

  int setLight(int value) {
    return _devbindings.setLight(value);
  }

  int getLight() {
    return _devbindings.getLight(lightValue);
  }

  int getLightValueList() {
    return _devbindings.getLightValueList(lightValueInfo);
  }

  int saveScreenDisplayBrightness(int value) {
    return _devbindings.setScreenDisplayBrightness(value);
  }

  int getScreenDisplayBrightness() {
    return _devbindings.getScreenDisplayBrightness(brightnessValue);
  }

  int setUpperAndBobbinThreadSensor(bool isDisabled) {
    return _devbindings.setUpperAndBobbinThreadSensor(isDisabled);
  }

  int getUpperAndBobbinThreadSensor() {
    return _devbindings.getUpperAndBobbinThreadSensor(isDisabled);
  }

  int getUpperAndBobbinThreadSensorDefault() {
    return _devbindings.getUpperAndBobbinThreadSensorDefault(isDisabled);
  }

  int saveMachineSpeakerVolume(int value) {
    return _devbindings.saveMachineSpeakerVolume(value);
  }

  int getMachineSpeakerVolume() {
    return _devbindings.getMachineSpeakerVolume(volumeValue);
  }

  int setNeedlePositionStitchPlacement(bool isOn) {
    return _devbindings.setNeedlePositionStitchPlacement(isOn);
  }

  int getNeedlePositionStitchPlacement() {
    return _devbindings.getNeedlePositionStitchPlacement(placementIsOn);
  }

  int getNeedlePositionStitchPlacementDefault() {
    return _devbindings.getNeedlePositionStitchPlacementDefault(placementIsOn);
  }

  // int setInitialScreen(int value) {
  //   return _devbindings.setInitialScreen(value);
  // }

  int getInitialScreen() {
    return _devbindings.getInitialScreen(initialScreenValue);
  }

  int setEcoMode(int value) {
    return _devbindings.saveEcoMode(value);
  }

  int getEcoMode() {
    return _devbindings.getEcoMode(ecoModeValue);
  }

  int setShutoffSupportMode(int value) {
    return _devbindings.saveShutoffSupportMode(value);
  }

  int getShutoffSupportMode() {
    return _devbindings.getShutoffSupportMode(supportModeValue);
  }


  int saveScreenSaverTime(int value) {
    return _devbindings.saveScreenSaverTime(value);
  }

  int getScreenSaverTime() {
    return _devbindings.getScreenSaverTime(saverTimeValue);
  }

  int saveSettingsScreenSaverType(int value) {
    return _devbindings.saveSettingsScreenSaverType(value);
  }

  int getSettingsScreenSaverType() {
    return _devbindings.getSettingsScreenSaverType(saverTypeValue);
  }

  int saveMousePointer(int value) {
    return _devbindings.saveMousePointer(value);
  }

  int getMousePointer() {
    return _devbindings.getMousePointer(mousePointerValue);
  }

  // int setProjectorBrightness(int value) {
  //   return _devbindings.setProjectorBrightness(value);
  // }
  int setProjectorBrightnessMinus() {
    return _devbindings.setProjectorBrightnessMinus();
  }
  int setProjectorBrightnessPlus() {
    return _devbindings.setProjectorBrightnessPlus();
  }

  //
  // int getProjectorBrightness() {
  //   return _devbindings.getProjectorBrightness();
  // }
  //
  // int getProjectorBrightnessValueList() {
  //   return _devbindings
  //       .getProjectorBrightnessValueList(projectorBrightnessValueInfo);
  // }
  //
  // int setProjectorBackbroundColor(int value) {
  //   return _devbindings.setProjectorBackbroundColor(value);
  // }
  //
  // int getProjectorBackbroundColor() {
  //   return _devbindings.getProjectorBackbroundColor(backbroundColorValue);
  // }
  //
  // int getProjectorBackbroundColorValueList() {
  //   return _devbindings
  //       .getProjectorBackbroundColorValueList(backbroundColorValueInfo);
  // }

  int setProjectorPatternOutline(bool isOn) {
    return _devbindings.setProjectorPatternOutline(isOn);
  }

  int getProjectorPatternOutline() {
    return _devbindings.getProjectorPatternOutline(patternOutlineIsOn);
  }

  int getProjectorPatternOutlineDefault() {
    return _devbindings.getProjectorPatternOutlineDefault(patternOutlineIsOn);
  }

  int setPointerColor(int value) {
    return _devbindings.setPointerColor(value);
  }

  int getPointerColor() {
    return _devbindings.getPointerColor(pointerColorValue);
  }

  // int getPointerColorValueList() {
  //   return _devbindings.getPointerColorValueList(pointerColorValueInfo);
  // }

  int setPointerSharpe(int value) {
    return _devbindings.setPointerSharpe(value);
  }

  int getPointerSharpe() {
    return _devbindings.getPointerSharpe(pointerSharpeValue);
  }

  // int getPointerSharpeValueList() {
  //   return _devbindings.getPointerSharpeValueList(pointerSharpeValueInfo);
  // }



  int getServiceCount() {
    return _devbindings.getServiceCount(serviceCountValue);
  }

  int getTotalCount() {
    return _devbindings.getTotalCount(totalCountValue);
  }

  int setKitActivationCode(int kitID, int CertificKey) {
    return _devbindings.setKitActivationCode(kitID, CertificKey);
  }

  // int getMachineName() {
  //   return _devbindings.getMachineName(MachineName);
  // }
  //
  // int setMachineName() {
  //   return _devbindings.setMachineName(MachineName);
  // }

  int setEmbroideryFrameDisplay(int value) {
    return _devbindings.setEmbroideryFrameDisplay(value);
  }

  int getEmbroideryFrameDisplay() {
    return _devbindings.getEmbroideryFrameDisplay(frameDisplayValue);
  }

  int getEmbroideryFrameDisplayValueLis() {
    return _devbindings
        .getEmbroideryFrameDisplayValueLis(frameDisplayValueInfo);
  }

  int saveGrid(int value) {
    return _devbindings.saveGrid(value);
  }

  int getGrid() {
    return _devbindings.getGrid(gridValue);
  }

  int setMaxEmbroiderySpeed(int value) {
    return _devbindings.setMaxEmbroiderySpeed(value);
  }

  int getMaxEmbroiderySpeed() {
    return _devbindings.getMaxEmbroiderySpeed(embroiderySpeedValue);
  }

  void UserSettingItemValueListFree() {
    _devbindings.
      UserSettingItemValueListFree(embroiderySpeedValueInfo);
  }

  int getMaxEmbroiderySpeedValueList() {
    return _devbindings
        .getMaxEmbroiderySpeedValueList(embroiderySpeedValueInfo);
  }

  int setEmbroideryTension(int value) {
    return _devbindings.setEmbroideryTension(value);
  }

  int getEmbroideryTension() {
    return _devbindings.getEmbroideryTension(embroideryTensionValue);
  }

  int getEmbroideryTensionValueList() {
    return _devbindings
        .getEmbroideryTensionValueList(embroideryTensionValueInfo);
  }

  int setEmbroideryFootHeight(int value) {
    return _devbindings.setEmbroideryFootHeight(value);
  }

  int getEmbroideryFootHeight() {
    return _devbindings.getEmbroideryFootHeight(embroideryFootHeightValue);
  }

  int getEmbroideryFootHeightValueList() {
    return _devbindings
        .getEmbroideryFootHeightValueList(embroideryFootHeightValueInfo);
  }

  int setEmbroideryNeedleStopPosition(bool isUP) {
    return _devbindings.setEmbroideryNeedleStopPosition(isUP);
  }

  int getEmbroideryNeedleStopPosition() {
    return _devbindings.getEmbroideryNeedleStopPosition(stopPositionIsUP);
  }

  int getEmbroideryNeedleStopPositionDefault() {
    return _devbindings
        .getEmbroideryNeedleStopPositionDefault(stopPositionIsUP);
  }

  int setEmbAutoDown(bool isOn) {
    return _devbindings.setEmbAutoDown(isOn);
  }

  int getEmbAutoDown() {
    return _devbindings.getEmbAutoDown(embAutoDownIsOn);
  }

  int getEmbAutoDownDefault() {
    return _devbindings.getEmbAutoDownDefault(embAutoDownIsOn);
  }

  int saveDisplayUnit(bool isMM) {
    return _devbindings.saveDisplayUnit(isMM);
  }

  int getDisplayUnit() {
    Pointer<Int8> isMMGet = ffi.calloc<Int8>();
    final ret = _devbindings.getDisplayUnit(isMMGet);
    isMM.value = isMMGet.value == ENUM_LIB.TRUE ? true : false;
    ffi.calloc.free(isMMGet);
    return ret;

   }

  int saveThreadColor(bool isThreadCode) {
    return _devbindings.saveThreadColor(isThreadCode);
  }

  int getThreadColor() {
    return _devbindings.getThreadColor(threadCode);
  }

  int setThreadBrand(int value) {
    return _devbindings.setThreadBrand(value);
  }

  int getThreadBrand() {
    return _devbindings.getThreadBrand(threadBrandValue);
  }

  int getThreadBrandValueList() {
    return _devbindings.getThreadBrandValueList(threadBrandValueInfo);
  }

  int saveEmbroideryBackgroundColor(int value) {
    return _devbindings.saveEmbroideryBackgroundColor(value);
  }

  int getEmbroideryBackgroundColor() {
    return _devbindings.getEmbroideryBackgroundColor(embBackgroundColorValue);
  }

  int saveThumbnailBackgroundColor(int value) {
    return _devbindings.saveThumbnailBackgroundColor(value);
  }

  int getThumbnailBackgroundColor() {
    return _devbindings
        .getThumbnailBackgroundColor(thumbnailBackgroundColorValue);
  }

  int saveThumbnailSize(int value) {
    return _devbindings.saveThumbnailSize(value);
  }

  int getThumbnailSize() {
    return _devbindings.getThumbnailSize(thumbnailSizeValue);
  }

  int setEmbroideryBastingDistance(int value) {
    return _devbindings.setEmbroideryBastingDistance(value);
  }

  int getEmbroideryBastingDistance() {
    return _devbindings.getEmbroideryBastingDistance(embBastingDistanceValue);
  }

  int getEmbroideryBastingDistanceValueList() {
    return _devbindings
        .getEmbroideryBastingDistanceValueList(embBastingDistanceValueInfo);
  }

  int setScanQuality(bool isQuality) {
    return _devbindings.setScanQuality(isQuality);
  }

  int getScanQuality() {
    Pointer<Int8> isQualityValue = ffi.calloc<Int8>();
    isQualityValue.value = isQuality.value ? 1 : 0;
    int err = _devbindings.getScanQuality(isQualityValue);
    isQuality.value = (isQualityValue.value == 1) ? true : false;
    return err;
  }

  int getScanQualityDefault() {
    return _devbindings.getScanQualityDefault(isQuality);
  }

  int setFabricThicknessSensor(bool value) {
    return _devbindings.setFabricThicknessSensor(value);
  }

  int getFabricThicknessSensor() {
    return _devbindings.getFabricThicknessSensor(thicknessSensorIsOn);
  }

  int getFabricThicknessSensorDefault() {
    return _devbindings.getFabricThicknessSensorDefault(thicknessSensorIsOn);
  }


  int saveCompFrameScanBackImgView() {
    return _devbindings.deleteCompFrameScanBackImgView();
  }

  int getCompFrameScanBackImgView() {
    return _devbindings.getCompFrameScanBackImgView(imgViewIsOn);
  }

  int getDeviceSettingInfo() {
    return _devbindings.getDeviceSettingInfo(deviceInfo);
  }

  int getCommonUserSettingInfo() {
    return _devbindings.getCommonUserSettingInfo(commonUserInfo);
  }

  int getEmbUserSettingInfo() {
    return _devbindings.getEmbUserSettingInfo(embUserInfo);
  }

  int getUtlUserSettingInfo() {
    return _devbindings.getUtlUserSettingInfo(utlUserInfo);
  }

  bool checkRecoveryRequirement() {
    return _devbindings.checkRecoveryRequirement();
  }

  void resetUtlUserSetting() {
    _devbindings.resetUtlUserSetting();
  }

  void resetCommonUserSetting() {
    _devbindings.resetCommonUserSetting();
  }

  void resetEmbUserSetting() {
    _devbindings.resetEmbUserSetting();
  }

  void getUserSettingEnableInfo() {
    _devbindings.getUserSettingEnableInfo(userSettingEnableInfo);

  }

//カメラペンUIの投影ON/OFF設定値を保存する
  int saveEmbCameraUIProjectionSetting(int cameraUIProjection) {
    debugPrint(
        'projector_bindings_saveEmbCameraUIProjectionSetting.cameraUIProjectionValue = ${cameraUIProjectionValue.value}');
    return _devbindings.saveEmbCameraUIProjectionSetting(cameraUIProjection);
  }

//カメラペンUIの投影ON/OFF設定値を取得する
  int getEmbCameraUIProjectionSetting() {
    int ret;
    //return _bindings.getEmbCameraUIProjectionSetting(cameraUIProjectionValue);
    ret =
        _devbindings.getEmbCameraUIProjectionSetting(cameraUIProjectionValue);
    debugPrint(
        'projector_bindings_getEmbCameraUIProjectionSetting.cameraUIProjectionValue = ${cameraUIProjectionValue.value}');
    return ret;
  }

  Pointer<Int8> cameraUIProjectionValue = ffi.calloc<Int8>();

  Pointer<Bool> widthControlIsOn = ffi.calloc<Bool>();
  Pointer<Int16> vertiValue = ffi.calloc<Int16>();
  Pointer<UserSettingItemValue> vertiValueInfo =
      UserSettingItemValue.allocate();
  Pointer<Int16> horizValue = ffi.calloc<Int16>();
  Pointer<UserSettingItemValue> horizValueInfo =
      UserSettingItemValue.allocate();
  Pointer<Int16> footHeightValue = ffi.calloc<Int16>();
  Pointer<UserSettingItemValueList> footHeightValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int16> footPressureValue = ffi.calloc<Int16>();
  Pointer<UserSettingItemValueList> footPressureValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Bool> sensorSystemIsOn = ffi.calloc<Bool>();
  Pointer<Bool> isCenter = ffi.calloc<Bool>();
  Pointer<Bool> baseLineIsOn = ffi.calloc<Bool>();
  Pointer<Int16> pivotingHeightValue = ffi.calloc<Int16>();
  Pointer<UserSettingItemValueList> pivotingHeightValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int8> freeMotionValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValueList> freeMotionValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int8> feedAdjustmentValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValue> feedAdjustmentValueInfo =
      UserSettingItemValue.allocate();
  Pointer<Bool> utlAutoDownIsOn = ffi.calloc<Bool>();
  Pointer<Bool> pressToTrimIsOn = ffi.calloc<Bool>();
  Pointer<Bool> isQuilt = ffi.calloc<Bool>();
  Pointer<Bool> priorityIsOn = ffi.calloc<Bool>();
  Pointer<Int8> heelSwitchValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValueList> heelSwitchValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int8> sidePedalValue = ffi.calloc<Int8>();
  Pointer<Bool> temporaryStopIsOn = ffi.calloc<Bool>();
  Pointer<Int8> languageValue = ffi.calloc<Int8>();
  Pointer<Int8> lightValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValueList> lightValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int8> brightnessValue = ffi.calloc<Int8>();
  Pointer<Bool> isDisabled = ffi.calloc<Bool>();
  Pointer<Int8> volumeValue = ffi.calloc<Int8>();
  Pointer<Bool> placementIsOn = ffi.calloc<Bool>();
  Pointer<Int8> initialScreenValue = ffi.calloc<Int8>();
  Pointer<Int8> ecoModeValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValueList> ecoModeValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int8> supportModeValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValueList> supportModeValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int8> saverTimeValue = ffi.calloc<Int8>();
  Pointer<Int8> saverTypeValue = ffi.calloc<Int8>();
  Pointer<Int8> mousePointerValue = ffi.calloc<Int8>();
  Pointer<Int8> projectorBrightnessValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValueList> projectorBrightnessValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int8> backbroundColorValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValue> backbroundColorValueInfo =
      UserSettingItemValue.allocate();
  Pointer<Bool> patternOutlineIsOn = ffi.calloc<Bool>();
  Pointer<Int8> pointerColorValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValue> pointerColorValueInfo =
      UserSettingItemValue.allocate();
  Pointer<Int8> pointerSharpeValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValueList> pointerSharpeValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int64> serviceCountValue = ffi.calloc<Int64>();
  Pointer<Int64> totalCountValue = ffi.calloc<Int64>();
  Pointer<Char> MachineName = ffi.calloc<Char>(255);
  Pointer<Int32> frameDisplayValue = ffi.calloc<Int32>();
  Pointer<UserSettingItemValueList> frameDisplayValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int32> gridValue = ffi.calloc<Int32>();
  Pointer<Int8> embroiderySpeedValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValueList> embroiderySpeedValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int8> embroideryTensionValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValueList> embroideryTensionValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int8> embroideryFootHeightValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValue> embroideryFootHeightValueInfo =
      UserSettingItemValue.allocate();
  Pointer<Bool> stopPositionIsUP = ffi.calloc<Bool>();
  Pointer<Bool> embAutoDownIsOn = ffi.calloc<Bool>();
  Pointer<Bool> isMM = ffi.calloc<Bool>();
  Pointer<Int8> threadCode = ffi.calloc<Int8>();
  Pointer<Int8> threadBrandValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValueList> threadBrandValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int8> embBackgroundColorValue = ffi.calloc<Int8>();
  Pointer<Int8> thumbnailBackgroundColorValue = ffi.calloc<Int8>();
  Pointer<Int8> thumbnailSizeValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValueList> thumbnailSizeValueInfo =
      UserSettingItemValueList.allocate();
  Pointer<Int8> embBastingDistanceValue = ffi.calloc<Int8>();
  Pointer<UserSettingItemValue> embBastingDistanceValueInfo =
      UserSettingItemValue.allocate();
  Pointer<Bool> isQuality = ffi.calloc<Bool>();
  Pointer<Bool> thicknessSensorIsOn = ffi.calloc<Bool>();
  Pointer<Bool> imgViewIsOn = ffi.calloc<Bool>();
  Pointer<DeviceSettingInfo> deviceInfo = DeviceSettingInfo.allocate();
  Pointer<CommonUserSettingInfo> commonUserInfo =
      CommonUserSettingInfo.allocate();
  Pointer<EmbUserSettingInfo> embUserInfo = EmbUserSettingInfo.allocate();
  Pointer<UtlUserSettingInfo> utlUserInfo = UtlUserSettingInfo.allocate();
  Pointer<UserSettingEnabledInfo> userSettingEnableInfo =
      UserSettingEnabledInfo.allocate();

  ///StringからChar*への変換
  Pointer<Char> StoC(String str) {
    //print(str);
    Pointer<Char> char = ffi.calloc<Char>(255);
    final strCode = str.runes.toList();
    //print(strCode.length);
    int i = 0;
    for (i = 0; i < strCode.length; i++) {
      char.elementAt(i).value = strCode[i];
      //print(String.fromCharCode(char.elementAt(i).value));
    }
    char.elementAt(i).value = 0;
    return char;
  }

  ///Char*からStringへの変換
  String CtoS(Pointer<Char> char) {
    String str = "";
    for (int i = 0; char[i] != 0; i++) {
      str += String.fromCharCode(char.elementAt(i).value);
    }
    return str;
  }
}
