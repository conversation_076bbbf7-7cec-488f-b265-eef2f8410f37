import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../common_component/function_provider/size_function_provider/size_function_provider.dart';
import '../../../../common_component/move/move.dart';
import 'size_adjustment_view_model.dart';

///
/// 編集メイン画面
///
class SizeAdjustment extends ConsumerStatefulWidget {
  const SizeAdjustment({super.key});

  @override
  ConsumerState<SizeAdjustment> createState() => _SizeAdjustmentState();
}

class _SizeAdjustmentState extends ConsumerState<SizeAdjustment> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(sizeAdjustmentViewModelProvider);
    final viewModel = ref.read(sizeAdjustmentViewModelProvider.notifier);

    ///
    /// ボタン数
    ///
    const int btnSize = 6;
    return Offstage(
      offstage: state.isScanPopupOpen,
      child: Column(
        children: [
          const Spacer(flex: 236),
          Expanded(
            flex: 975,
            child: Row(
              children: [
                const Spacer(flex: 571),
                Expanded(
                  flex: 229,
                  child: Material(
                    color: Colors.transparent,
                    child: Stack(
                      children: [
                        const pic_embedit_individual_toolbar(),
                        Column(
                          children: [
                            const Spacer(flex: 12),
                            Expanded(
                              flex: 75,
                              child: Stack(
                                children: [
                                  Row(
                                    children: [
                                      const Spacer(flex: 12),
                                      Expanded(
                                        flex: 142,
                                        child: Column(
                                          children: [
                                            Expanded(
                                              flex: 37,
                                              child: grp_str_number1_01(
                                                text: state.heightValue,
                                              ),
                                            ),
                                            const Spacer(flex: 1),
                                            Expanded(
                                              flex: 37,
                                              child: grp_str_number2_01(
                                                text: state.widthValue,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const Spacer(flex: 3),
                                      Expanded(
                                        flex: 60,
                                        child: Column(
                                          children: [
                                            const Spacer(flex: 8),
                                            Expanded(
                                              flex: 26,
                                              child: grp_str_unit_mm1_01(
                                                text: state.isInch
                                                    ? l10n.icon_00226
                                                    : l10n.icon_00225,
                                              ),
                                            ),
                                            const Spacer(flex: 12),
                                            Expanded(
                                              flex: 26,
                                              child: grp_str_unit_mm2_01(
                                                text: state.isInch
                                                    ? l10n.icon_00226
                                                    : l10n.icon_00225,
                                              ),
                                            ),
                                            const Spacer(flex: 3),
                                          ],
                                        ),
                                      ),
                                      const Spacer(flex: 12),
                                    ],
                                  ),
                                  const Row(
                                    children: [
                                      Spacer(flex: 32),
                                      Expanded(
                                        flex: 32,
                                        child: Column(
                                          children: [
                                            Spacer(flex: 22),
                                            Expanded(
                                              flex: 32,
                                              child: ico_shape_size_01(),
                                            ),
                                            Spacer(flex: 21),
                                          ],
                                        ),
                                      ),
                                      Spacer(flex: 165),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            const Spacer(flex: 9),
                            Expanded(
                              flex: 70,
                              child: Row(
                                children: [
                                  const Spacer(flex: 12),
                                  Expanded(
                                    flex: 205,
                                    child: CustomTooltip(
                                      message: l10n.tt_emb_size_normalstb,
                                      child: grp_stbtn_size_mode_select(
                                        onTap:
                                            viewModel.onSTBChangeButtonClicked,
                                        state: state.isTabDisable
                                            ? ButtonState.disable
                                            : ButtonState.normal,
                                        isSelect: state.isPatternDoSTB,
                                      ),
                                    ),
                                  ),
                                  const Spacer(flex: 12),
                                ],
                              ),
                            ),
                            const Spacer(flex: 9),
                            Expanded(
                              flex: 227,
                              child: Row(
                                children: [
                                  const Spacer(flex: 12),
                                  Consumer(builder: (context, ref, child) {
                                    final isSubSizeButtonDisable = ref.watch(
                                        sizeFunctionProvider.select((value) =>
                                            value.isSubSizeButtonDisable));
                                    return Expanded(
                                      flex: 205,
                                      child: GridView.builder(
                                        itemCount: btnSize,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        gridDelegate:
                                            const SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: 2,
                                          crossAxisSpacing: 9,
                                          mainAxisSpacing: 9,
                                          childAspectRatio: 98 / 70,
                                        ),
                                        itemBuilder: (context, index) => [
                                          grp_btn_size_reduce(
                                            longPressTriggerTick: 4,
                                            onTap: () => viewModel
                                                .onSizeTabReduceButton(false),
                                            onLongPress: () => viewModel
                                                .onSizeTabReduceButton(true),
                                            onTapUp:
                                                viewModel.sizeAdjustmentCancel,
                                            onTapCancel:
                                                viewModel.sizeAdjustmentCancel,
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: isSubSizeButtonDisable
                                                ? ButtonState.disable
                                                : ButtonState.normal,
                                          ),
                                          grp_btn_size_enlarge(
                                            longPressTriggerTick: 4,
                                            onTap: () => viewModel
                                                .onSizeTabEnlargeButton(false),
                                            onLongPress: () => viewModel
                                                .onSizeTabEnlargeButton(true),
                                            onTapUp:
                                                viewModel.sizeAdjustmentCancel,
                                            onTapCancel:
                                                viewModel.sizeAdjustmentCancel,
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: isSubSizeButtonDisable
                                                ? ButtonState.disable
                                                : ButtonState.normal,
                                          ),
                                          grp_btn_size_hight_reduce(
                                            longPressTriggerTick: 4,
                                            onTap: () => viewModel
                                                .onSizeTabHeightReduceButton(
                                                    false),
                                            onLongPress: () => viewModel
                                                .onSizeTabHeightReduceButton(
                                                    true),
                                            onTapUp:
                                                viewModel.sizeAdjustmentCancel,
                                            onTapCancel:
                                                viewModel.sizeAdjustmentCancel,
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: isSubSizeButtonDisable
                                                ? ButtonState.disable
                                                : ButtonState.normal,
                                          ),
                                          grp_btn_size_hight_enlarge(
                                            longPressTriggerTick: 4,
                                            onTap: () => viewModel
                                                .onSizeTabHeightEnlargeButton(
                                                    false),
                                            onLongPress: () => viewModel
                                                .onSizeTabHeightEnlargeButton(
                                                    true),
                                            onTapUp:
                                                viewModel.sizeAdjustmentCancel,
                                            onTapCancel:
                                                viewModel.sizeAdjustmentCancel,
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: isSubSizeButtonDisable
                                                ? ButtonState.disable
                                                : ButtonState.normal,
                                          ),
                                          grp_btn_size_width_reduce(
                                            longPressTriggerTick: 4,
                                            onTap: () => viewModel
                                                .onSizeTabWidthReduceButton(
                                                    false),
                                            onLongPress: () => viewModel
                                                .onSizeTabWidthReduceButton(
                                                    true),
                                            onTapUp:
                                                viewModel.sizeAdjustmentCancel,
                                            onTapCancel:
                                                viewModel.sizeAdjustmentCancel,
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: isSubSizeButtonDisable
                                                ? ButtonState.disable
                                                : ButtonState.normal,
                                          ),
                                          grp_btn_size_width_enlarge(
                                            longPressTriggerTick: 4,
                                            onTap: () => viewModel
                                                .onSizeTabWidthEnlargeButton(
                                                    false),
                                            onLongPress: () => viewModel
                                                .onSizeTabWidthEnlargeButton(
                                                    true),
                                            onTapUp:
                                                viewModel.sizeAdjustmentCancel,
                                            onTapCancel:
                                                viewModel.sizeAdjustmentCancel,
                                            feedBackControl:
                                                FeedBackControl.onlyDisable,
                                            state: isSubSizeButtonDisable
                                                ? ButtonState.disable
                                                : ButtonState.normal,
                                          )
                                        ][index],
                                      ),
                                    );
                                  }),
                                  const Spacer(flex: 12),
                                ],
                              ),
                            ),
                            const Spacer(flex: 9),
                            Expanded(
                              flex: 70,
                              child: Row(
                                children: [
                                  const Spacer(flex: 119),
                                  Expanded(
                                    flex: 98,
                                    child: CustomTooltip(
                                      message:
                                          l10n.tt_emb_edit_projlcd_border_reset,
                                      child: Consumer(
                                        builder: (context, ref, child) {
                                          final isSizeFunctionDisable =
                                              ref.watch(sizeFunctionProvider
                                                  .select((value) => value
                                                      .isSizeFunctionDisable));
                                          return grp_btn_reset_01(
                                            text: l10n.icon_reset,
                                            isEnglish: false,
                                            onTap:
                                                viewModel.onResetButtonClicked,
                                            state: isSizeFunctionDisable
                                                ? ButtonState.disable
                                                : ButtonState.normal,
                                            style: ThemeButton
                                                .btn_n_size98x70_theme1,
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                  const Spacer(flex: 12),
                                ],
                              ),
                            ),
                            const Spacer(flex: 182),
                            const Expanded(
                              flex: 205,
                              child: Row(
                                children: [
                                  Spacer(flex: 12),
                                  Expanded(
                                    flex: 205,
                                    child: Move(),
                                  ),
                                  Spacer(flex: 12),
                                ],
                              ),
                            ),
                            const Spacer(flex: 24),
                            Expanded(
                              flex: 70,
                              child: Row(
                                children: [
                                  const Spacer(flex: 12),
                                  Expanded(
                                    flex: 205,
                                    child: grp_btn_positive(
                                      style:
                                          ThemeButton.btn_n_size205x70_theme1,
                                      text: l10n.icon_ok,
                                      onTap: () =>
                                          viewModel.onOKButtonClick(context),
                                    ),
                                  ),
                                  const Spacer(flex: 12),
                                ],
                              ),
                            ),
                            const Spacer(flex: 12),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Spacer(flex: 69)
        ],
      ),
    );
  }
}
