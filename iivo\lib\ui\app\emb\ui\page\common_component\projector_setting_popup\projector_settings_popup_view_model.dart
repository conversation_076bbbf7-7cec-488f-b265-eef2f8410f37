import 'dart:async';

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/devedit_bindings_generated.dart';

import '../../../../../../../model/projector_model.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../camera_pen/camera_pen_view_model.dart';
import '../background_color_popup/background_color_popup.dart';
import '../function_provider/projector_function_provider/projector_function_provider.dart';
import 'projector_settings_popup_view_interface.dart';

final projectorSettingsPopupViewModelProvider =
    StateNotifierProvider.autoDispose<ProjectorSettingsPopupViewModelInterface,
            ProjectorSettingsPopupState>(
        (ref) => ProjectorSettingsPopupViewModel(ref));

class ProjectorSettingsPopupViewModel
    extends ProjectorSettingsPopupViewModelInterface {
  ProjectorSettingsPopupViewModel(Ref ref)
      : super(
          const ProjectorSettingsPopupState(),
          ref,
        ) {
    ProjectorModel().embProjector.isProjectorSettingsPopOpen = true;
    update();
  }
  @override
  void update() {
    state = state.copyWith(
      isProjectorUIOn:
          ProjectorModel().embProjector.isEmbCameraPenUiProjectorOn,
      isFrameAvailable:
          ProjectorModel().embProjector.isEmbCameraPenUiProjectorValid(),
      projectorBackgroundColor:
          ProjectorModel().getProjectorBackgroundColor().color,
    );
  }

  @override
  void onProjectorUISwitchButtonClick(BuildContext context, bool isUIOn) {
    if (ProjectorModel().embProjector.isEmbCameraPenUiProjectorValid() ==
        false) {
      return;
    }
    GlobalPopupRoute().showPleaseWaitPopup();

    Future(
      () {
        /// CameraPenUi設定更新
        final CameraUISetting embCameraUIProjectionSetting =
            ProjectorModel().cameraPen.getEmbCameraUIProjectionSetting();

        return switch (embCameraUIProjectionSetting) {
          CameraUISetting.EMBCAMERAUI_PROJECTION_ON => () {
              ProjectorModel().cameraPen.saveCameraUIProjectionSetting(
                  CameraUISetting.EMBCAMERAUI_PROJECTION_OFF);

              return ProjectorModel().cameraPen.finishCamera().then((_) =>
                  ref.read(cameraPenViewModelProvider.notifier).update());
            }(),
          CameraUISetting.EMBCAMERAUI_PROJECTION_OFF => () {
              ProjectorModel().cameraPen.saveCameraUIProjectionSetting(
                    CameraUISetting.EMBCAMERAUI_PROJECTION_ON,
                  );
              return ProjectorModel().cameraPen.initCamera().then((_) =>
                  ref.read(cameraPenViewModelProvider.notifier).update());
            }(),
          EMBCAMERAUI_PROJECTION_E.EMBCAMERAUI_PROJECTION_ON_DISABLE ||
          EMBCAMERAUI_PROJECTION_E.EMBCAMERAUI_PROJECTION_OFF_DISABLE ||
          EMBCAMERAUI_PROJECTION_E.EMBCAMERAUI_PROJECTION_MAX =>
            Future.value(null)
        };
      },
    ).then((_) async {
      final embProjectorFunction =
          ref.read(embProjectorFunctionProvider.notifier);
      final finishFuture = embProjectorFunction.maybeUpdateProjectorScreen(
          redrawEmbPattern: false);
      Future.wait([
        Future.delayed(const Duration(
            milliseconds: ProjectorModel.projectorStartStopDelayMS)),
        finishFuture,
      ]).then((_) {
        update();
        GlobalPopupRoute().resetPleaseWaitPopup();
      });
    });
  }

  @override
  void onBackgroundColorButtonClick(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => const BackgroundColorPopup(),
      ),
    ).then(
      (_) => update(),
    );
  }

  @override
  void onCloseButtonClick(BuildContext context) {
    PopupNavigator.pop(context: context);
  }

  @override
  void dispose() {
    super.dispose();
    ProjectorModel().embProjector.isProjectorSettingsPopOpen = false;
  }
}
