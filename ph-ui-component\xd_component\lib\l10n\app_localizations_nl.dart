import 'app_localizations.dart';

/// The translations for Dutch Flemish (`nl`).
class AppLocalizationsNl extends AppLocalizations {
  AppLocalizationsNl([String locale = 'nl']) : super(locale);

  @override
  String get color_001 => 'ROSE';

  @override
  String get color_002 => 'OUDROZE';

  @override
  String get color_003 => 'BLOESEM';

  @override
  String get color_004 => 'VLEESROZE';

  @override
  String get color_005 => 'LICHTKORAAL';

  @override
  String get color_006 => 'FLETSROZE';

  @override
  String get color_007 => 'HEIDEMIST';

  @override
  String get color_008 => 'CHAMPAGNE';

  @override
  String get color_009 => 'DONKERMAUVE';

  @override
  String get color_010 => 'HEIDE';

  @override
  String get color_011 => 'PURPERROZE';

  @override
  String get color_012 => 'BERGROOS';

  @override
  String get color_013 => 'JAPANSE KERS';

  @override
  String get color_014 => 'ANJERROZE';

  @override
  String get color_015 => 'ZALM';

  @override
  String get color_016 => 'DAGERAAD';

  @override
  String get color_017 => 'DONKER-\nKORAAL';

  @override
  String get color_018 => 'MEEKRAP';

  @override
  String get color_019 => 'WIJNROOD';

  @override
  String get color_020 => 'GLÜHWEIN';

  @override
  String get color_021 => 'GOUDREINET';

  @override
  String get color_022 => 'PRUIM';

  @override
  String get color_023 => 'TAMME\nKASTANJE';

  @override
  String get color_024 => 'OSSENBLOED';

  @override
  String get color_025 => 'CYCLAAM';

  @override
  String get color_026 => 'ROBIJN';

  @override
  String get color_027 => 'DONKER\nFUCHSIA';

  @override
  String get color_028 => 'KARMIJN';

  @override
  String get color_029 => 'ROSEROOD';

  @override
  String get color_030 => 'BEGONIA';

  @override
  String get color_031 => 'AZALEA';

  @override
  String get color_032 => 'PIOEN';

  @override
  String get color_033 => 'AARDBEI';

  @override
  String get color_034 => 'VAGEVUUR';

  @override
  String get color_035 => 'SIGNAALROOD';

  @override
  String get color_036 => 'VIOLIER';

  @override
  String get color_037 => 'GLOEDROOD';

  @override
  String get color_038 => 'VUURROOD';

  @override
  String get color_039 => 'ROOD';

  @override
  String get color_040 => 'FLETSROOD';

  @override
  String get color_041 => 'DIEPROOD';

  @override
  String get color_042 => 'PHLOX';

  @override
  String get color_043 => 'STEENROOD';

  @override
  String get color_044 => 'LIPSTICK';

  @override
  String get color_045 => 'KERSTROOD';

  @override
  String get color_046 => 'SCHARLAKEN';

  @override
  String get color_047 => 'DIEPSCHAR-\nLAKEN';

  @override
  String get color_048 => 'CRANBERRY';

  @override
  String get color_049 => 'LICHTROSE';

  @override
  String get color_050 => 'GELEI';

  @override
  String get color_051 => 'KWARTS';

  @override
  String get color_052 => 'MELOEN';

  @override
  String get color_053 => 'PÊCHE';

  @override
  String get color_054 => 'KAMPERFOELIE';

  @override
  String get color_055 => 'DONKER-\nORANJE';

  @override
  String get color_056 => 'DIAMANT';

  @override
  String get color_057 => 'POOLBLAUW';

  @override
  String get color_058 => 'IJS';

  @override
  String get color_059 => 'GOLF';

  @override
  String get color_060 => 'VERGEET-\nMIJNIET';

  @override
  String get color_061 => 'PASTELBLAUW';

  @override
  String get color_062 => 'BABYBLAUW';

  @override
  String get color_063 => 'HEMELS BLAUW';

  @override
  String get color_064 => 'LICHT BLAUW';

  @override
  String get color_065 => 'FJORD';

  @override
  String get color_066 => 'ZEEBLAUW';

  @override
  String get color_067 => 'ULTRABLAUW';

  @override
  String get color_068 => 'TROPISCH\nBLAUW';

  @override
  String get color_069 => 'KORENBLOE-\nMEN BLAUW';

  @override
  String get color_070 => 'MAANBLAUW';

  @override
  String get color_071 => 'SAFFIER';

  @override
  String get color_072 => 'LEIBLAUW';

  @override
  String get color_073 => 'DONKERBLAUW';

  @override
  String get color_074 => 'DIEPBLAUW';

  @override
  String get color_075 => 'GENTIAAN';

  @override
  String get color_076 => 'BALTISCH\nBLAUW';

  @override
  String get color_077 => 'CALIFORNISCH\nBLAUW';

  @override
  String get color_078 => 'AZUUR';

  @override
  String get color_079 => 'ATLANTISCH\nBLAUW';

  @override
  String get color_080 => 'STAAL BLAUW';

  @override
  String get color_081 => 'PACIFISCH\nBLAUW';

  @override
  String get color_082 => 'BLAUW';

  @override
  String get color_083 => 'KEIZERSBLAUW';

  @override
  String get color_084 => 'VLOOTBLAUW';

  @override
  String get color_085 => 'UNIFORM-\nBLAUW';

  @override
  String get color_086 => 'LICHT-\nMARINEBLAUW';

  @override
  String get color_087 => 'DONKER-\nMARINEBLAUW';

  @override
  String get color_088 => 'PRAALBLAUW';

  @override
  String get color_089 => 'NACHTBLAUW';

  @override
  String get color_090 => 'DONKER-\nSAFFIER';

  @override
  String get color_091 => 'ZWEEDS\nBLAUW';

  @override
  String get color_092 => 'ULTRA MARIJN';

  @override
  String get color_093 => 'KONINGS-\nBLAUW';

  @override
  String get color_094 => 'KOBALTBLAUW';

  @override
  String get color_095 => 'PRUISISCH\nBLAUW';

  @override
  String get color_096 => 'NASSAUBLAUW';

  @override
  String get color_097 => 'ELEKTRISCH\nBLAUW';

  @override
  String get color_098 => 'PARIJS BLAUW';

  @override
  String get color_099 => 'LAVEN-\nDELBLAUW';

  @override
  String get color_100 => 'LAVENDEL-\nGRIJS';

  @override
  String get color_101 => 'LAVENDEL';

  @override
  String get color_102 => 'DIEPLAVENDEL';

  @override
  String get color_103 => 'BLAUWE-\nREGEN PAARS';

  @override
  String get color_104 => 'DONKERINDIGO';

  @override
  String get color_105 => 'LICHTINDIGO';

  @override
  String get color_106 => 'INDIGO';

  @override
  String get color_107 => 'PURPER';

  @override
  String get color_108 => 'DIEPPURPER';

  @override
  String get color_109 => 'FELPURPER';

  @override
  String get color_110 => 'DONKERPURPER';

  @override
  String get color_111 => 'DIEPPAARS';

  @override
  String get color_112 => 'PIMPELPAARS';

  @override
  String get color_113 => 'PAARS';

  @override
  String get color_114 => 'VIOLET';

  @override
  String get color_115 => 'FLETSPAARS';

  @override
  String get color_116 => 'MAGENTA';

  @override
  String get color_117 => 'LICHT LILA';

  @override
  String get color_118 => 'LILA';

  @override
  String get color_119 => 'FLETSLILA';

  @override
  String get color_120 => 'FANTASIEROZE';

  @override
  String get color_121 => 'DROOMROZE';

  @override
  String get color_122 => 'GRIJSROZE';

  @override
  String get color_123 => 'MARMERROZE';

  @override
  String get color_124 => 'WILDROZE';

  @override
  String get color_125 => 'AMBER ROOD';

  @override
  String get color_126 => 'PAARSROZE';

  @override
  String get color_127 => 'AUBERGINE';

  @override
  String get color_128 => 'HORTENSIA';

  @override
  String get color_129 => 'MAAGDEPALM';

  @override
  String get color_130 => 'AQUAMARIJN';

  @override
  String get color_131 => 'BLAUWE\nREGEN';

  @override
  String get color_132 => 'ENGELEN-\nBLAUW';

  @override
  String get color_133 => 'BLAUWE\nWISTERIA';

  @override
  String get color_134 => 'PAUWEN\nBLAUW';

  @override
  String get color_135 => 'DONKERJADE';

  @override
  String get color_136 => 'PARADIJS-\nGROEN';

  @override
  String get color_137 => 'GRIJSBLAUW';

  @override
  String get color_138 => 'VENUSBLAUW';

  @override
  String get color_139 => 'IJSGLAS';

  @override
  String get color_140 => 'OCEAANMIST';

  @override
  String get color_141 => 'ZEEGLAS';

  @override
  String get color_142 => 'TURQUOISE';

  @override
  String get color_143 => 'DONKER-\nTURQUOISE';

  @override
  String get color_144 => 'AQUA';

  @override
  String get color_145 => 'OCEAANGROEN';

  @override
  String get color_146 => 'GROENBLAUW';

  @override
  String get color_147 => 'ZEEGROEN';

  @override
  String get color_148 => 'DONKERTALING';

  @override
  String get color_149 => 'OCEAAN-\nBLAUW';

  @override
  String get color_150 => 'LAURIER';

  @override
  String get color_151 => 'KLIMOP';

  @override
  String get color_152 => 'SCANDINAV.\nGROEN';

  @override
  String get color_153 => 'BLAUW GROEN';

  @override
  String get color_154 => 'BLAUWSPAR';

  @override
  String get color_155 => 'OLIJF GROEN';

  @override
  String get color_156 => 'HAVENGROEN';

  @override
  String get color_157 => 'BOERENGROEN';

  @override
  String get color_158 => 'DONKERLEGER-\nGROEN';

  @override
  String get color_159 => 'PETROLEUM';

  @override
  String get color_160 => 'ALPENGROEN';

  @override
  String get color_161 => 'BOSGROEN';

  @override
  String get color_162 => 'MONUMENTEN-\nGROEN';

  @override
  String get color_163 => 'TALINGGRIJS';

  @override
  String get color_164 => 'ZEEMIST';

  @override
  String get color_165 => 'WILGENGROEN';

  @override
  String get color_166 => 'ALSEM';

  @override
  String get color_167 => 'EILANDGROEN';

  @override
  String get color_168 => 'DENNEGROEN';

  @override
  String get color_169 => 'JADE';

  @override
  String get color_170 => 'PEPERMUNT';

  @override
  String get color_171 => 'TALING GROEN';

  @override
  String get color_172 => 'DIEPGROEN';

  @override
  String get color_173 => 'KLASSIEK\nGROEN';

  @override
  String get color_174 => 'DONKER-\nDENNEGROEN';

  @override
  String get color_175 => 'GROEN';

  @override
  String get color_176 => 'IERS GROEN';

  @override
  String get color_177 => 'SMARAGD\nGROEN';

  @override
  String get color_178 => 'KLAVERGROEN';

  @override
  String get color_179 => 'MOSGROEN';

  @override
  String get color_180 => 'PLATAAN-\nGROEN';

  @override
  String get color_181 => 'KELTISCH\nGROEN';

  @override
  String get color_182 => 'BLADGROEN';

  @override
  String get color_183 => 'SALIEGROEN';

  @override
  String get color_184 => 'STEENEIK';

  @override
  String get color_185 => 'MUNT GROEN';

  @override
  String get color_186 => 'LICHT GROEN';

  @override
  String get color_187 => 'PEULENGROEN';

  @override
  String get color_188 => 'HERDERS-\nGROEN';

  @override
  String get color_189 => 'LICHT-\nAVOCADO';

  @override
  String get color_190 => 'OOGSTGROEN';

  @override
  String get color_191 => 'TOENDRA-\nGROEN';

  @override
  String get color_192 => 'LIMOEN GROEN';

  @override
  String get color_193 => 'KIWIGROEN';

  @override
  String get color_194 => 'LOMMERGROEN';

  @override
  String get color_195 => 'ZONNEBLOEM';

  @override
  String get color_196 => 'GOUD';

  @override
  String get color_197 => 'HERFSTGROEN';

  @override
  String get color_198 => 'FLETSOLIJF';

  @override
  String get color_199 => 'SCHORSGROEN';

  @override
  String get color_200 => 'ALGENGROEN';

  @override
  String get color_201 => 'DONKER OLIJF';

  @override
  String get color_202 => 'LEGERGROEN';

  @override
  String get color_203 => 'KALAHARI';

  @override
  String get color_204 => 'CITRO ENGEEL';

  @override
  String get color_205 => 'LICHTGEEL';

  @override
  String get color_206 => 'GEEL';

  @override
  String get color_207 => 'OMBER';

  @override
  String get color_208 => 'MANILLA';

  @override
  String get color_209 => 'GULDEN ROEDE';

  @override
  String get color_210 => 'SAVANNE';

  @override
  String get color_211 => 'HONINGGOUD';

  @override
  String get color_212 => 'DAGLELIE';

  @override
  String get color_213 => 'STERRENGOUD';

  @override
  String get color_214 => 'ZONNESCHIJN';

  @override
  String get color_215 => 'KOPER';

  @override
  String get color_216 => 'ORANJE';

  @override
  String get color_217 => 'DIEPGOUD';

  @override
  String get color_218 => 'OOGST\nKLEURIG GOUD';

  @override
  String get color_219 => 'ROOIBOS';

  @override
  String get color_220 => 'MOSTERD';

  @override
  String get color_221 => 'MESSING';

  @override
  String get color_222 => 'SIENNA';

  @override
  String get color_223 => 'ORANGEADE';

  @override
  String get color_224 => 'PIMENT';

  @override
  String get color_225 => 'VERMIL JOEN';

  @override
  String get color_226 => 'PAPRIKA';

  @override
  String get color_227 => 'WINGERD';

  @override
  String get color_228 => 'TERRACOTTA';

  @override
  String get color_229 => 'DONKERROEST';

  @override
  String get color_230 => 'TERRA';

  @override
  String get color_231 => 'TAANKLEURIG';

  @override
  String get color_232 => 'ZACHTPERZIK';

  @override
  String get color_233 => 'ROEST';

  @override
  String get color_234 => 'DONKER-\nABRIKOOS';

  @override
  String get color_235 => 'FELORANJE';

  @override
  String get color_236 => 'POMPOEN';

  @override
  String get color_237 => 'ZONNEGLOED';

  @override
  String get color_238 => 'KERRIE';

  @override
  String get color_239 => 'HONING';

  @override
  String get color_240 => 'AMANDEL';

  @override
  String get color_241 => 'ROSSING BRUIN';

  @override
  String get color_242 => 'KLEIBRUIN';

  @override
  String get color_243 => 'ROODBRUIN';

  @override
  String get color_244 => 'CREME BRUIN';

  @override
  String get color_245 => 'CREMEGEEL';

  @override
  String get color_246 => 'SAHARA';

  @override
  String get color_247 => 'PISTACHE';

  @override
  String get color_248 => 'LEEM';

  @override
  String get color_249 => 'GEMBER';

  @override
  String get color_250 => 'TEMPELGOUD';

  @override
  String get color_251 => 'ZANDBEIGE';

  @override
  String get color_252 => 'GEBRONSD';

  @override
  String get color_253 => 'LICHTBEIGE';

  @override
  String get color_254 => 'BEIGE';

  @override
  String get color_255 => 'ROTAN';

  @override
  String get color_256 => 'DONKERBEIGE';

  @override
  String get color_257 => 'BRONS';

  @override
  String get color_258 => 'KOFFIE';

  @override
  String get color_259 => 'LINNEN';

  @override
  String get color_260 => 'SCHELP';

  @override
  String get color_261 => 'ECRU';

  @override
  String get color_262 => 'ZALMROSE';

  @override
  String get color_263 => 'BARNSTEEN';

  @override
  String get color_264 => 'PERZIKROZE';

  @override
  String get color_265 => 'LICHT BRUIN';

  @override
  String get color_266 => 'KHAKI';

  @override
  String get color_267 => 'KOFFIEBOON';

  @override
  String get color_268 => 'CHOCOLA';

  @override
  String get color_269 => 'DIEPBRUIN';

  @override
  String get color_270 => 'DONKER BRUIN';

  @override
  String get color_271 => 'BRUIN';

  @override
  String get color_272 => 'TABAK';

  @override
  String get color_273 => 'TAUPE';

  @override
  String get color_274 => 'WARM GRIJS';

  @override
  String get color_275 => 'DIEPGRIJS';

  @override
  String get color_276 => 'STAALGRIJS';

  @override
  String get color_277 => 'ZWART-\nCHROOM';

  @override
  String get color_278 => 'HOUTSKOOL';

  @override
  String get color_279 => 'MIDDELGRIJS';

  @override
  String get color_280 => 'KOELGRIJS';

  @override
  String get color_281 => 'ROOKGRIJS';

  @override
  String get color_282 => 'TINKLEURIG';

  @override
  String get color_283 => 'DONKER GRIJS';

  @override
  String get color_284 => 'GRIJS';

  @override
  String get color_285 => 'LICHTGRIJS';

  @override
  String get color_286 => 'CHROOM';

  @override
  String get color_287 => 'ANTIEK GOUD';

  @override
  String get color_288 => 'ZILVER';

  @override
  String get color_289 => 'ZWART';

  @override
  String get color_290 => 'NATUURLIJK\nWIT';

  @override
  String get color_291 => 'WIT';

  @override
  String get color_292 => 'NEONROZE';

  @override
  String get color_293 => 'DONKERROZE';

  @override
  String get color_294 => 'WATERMELOEN';

  @override
  String get color_295 => 'ZACHTROZE';

  @override
  String get color_296 => 'FLETSKORAAL';

  @override
  String get color_297 => 'FLETS-\nABRIKOOS';

  @override
  String get color_298 => 'HAVANAGEEL';

  @override
  String get color_299 => 'DONKERCERISE';

  @override
  String get color_300 => 'INKTBLAUW';

  @override
  String get color_301 => 'APPLICATIE-\nMATERIAAL';

  @override
  String get color_302 => 'APPLICATIE-\nPOSITIE';

  @override
  String get color_303 => 'APPLICATIE';

  @override
  String get id_icon_test00001 => '\$\$\$\$\$';

  @override
  String get icon_00002 => 'Naaien';

  @override
  String get icon_00003_1 => 'Borduren';

  @override
  String get icon_00006_3 => 'Nuttige\nsteken';

  @override
  String get icon_00007_3 => 'Letters en\ndecoratieve\nsteken';

  @override
  String get icon_stitch => 'Steek';

  @override
  String get icon_close_1 => 'Sluiten';

  @override
  String get icon_cancel => 'Annuleren';

  @override
  String get icon_ok => 'OK';

  @override
  String get icon_00011_zz => '%%%icon%%%';

  @override
  String get icon_00011_zz_s => '%%%icon%%%';

  @override
  String get icon_00011 => 'Verwijderen';

  @override
  String get icon_00012_zz => '%%%icon%%%';

  @override
  String get icon_00012_zz_s => '%%%icon%%%';

  @override
  String get icon_reset_zz => '%%%icon%%%';

  @override
  String get icon_reset_zz_s => '%%%icon%%%';

  @override
  String get icon_reset => 'Herstellen';

  @override
  String get icon_reset_v => 'Herstellen';

  @override
  String get icon_00014_zz => '%%%icon%%%';

  @override
  String get icon_00014_zz_s => '%%%icon%%%';

  @override
  String get icon_00014 => 'Geheugen';

  @override
  String get icon_save => 'Opslaan';

  @override
  String get icon_00015_zz => '%%%icon%%%';

  @override
  String get icon_00015_zz_s => '%%%icon%%%';

  @override
  String get icon_util_mem_retrieve => 'Ophalen';

  @override
  String get icon_util_mem_memory => 'Geheugen';

  @override
  String get icon_util_mem_reset => 'Herstellen';

  @override
  String get icon_util_mem_delete => 'Verwijder.';

  @override
  String get icon_util_mem_alldelete => 'All. verw.';

  @override
  String get icon_00017_zz => '%%%icon%%%';

  @override
  String get icon_00017_zz_s => '%%%icon%%%';

  @override
  String get icon_00018_zz => '%%%icon%%%';

  @override
  String get icon_00018_zz_s => '%%%icon%%%';

  @override
  String get icon_00019_zz => '%%%icon%%%';

  @override
  String get icon_00019_zz_s => '%%%icon%%%';

  @override
  String get icon_00020_zz => '%%%icon%%%';

  @override
  String get icon_00020_zz_s => '%%%icon%%%';

  @override
  String get icon_util_width => 'Breedte';

  @override
  String get icon_util_length => 'Lengte';

  @override
  String get icon_util_lrshift => 'L/R-versc.';

  @override
  String get icon_util_tension => 'Spanning';

  @override
  String get icon_util_slitlength => 'Gleuf';

  @override
  String get icon_00021_zz => '%%%icon%%%';

  @override
  String get icon_00021_zz_s => '%%%icon%%%';

  @override
  String get icon_00022_zz => '%%%icon%%%';

  @override
  String get icon_00022_zz_s => '%%%icon%%%';

  @override
  String get icon_00027_zz => '%%%icon%%%';

  @override
  String get icon_00027_zz_s => '%%%icon%%%';

  @override
  String get icon_00028_zz => '%%%icon%%%';

  @override
  String get icon_00028_zz_s => '%%%icon%%%';

  @override
  String get icon_00029_zz => '%%%icon%%%';

  @override
  String get icon_00029_zz_s => '%%%icon%%%';

  @override
  String get icon_00038_zz => '%%%icon%%%';

  @override
  String get icon_00038_zz_s => '%%%icon%%%';

  @override
  String get icon_00030_1 => 'Testen';

  @override
  String get icon_guidel_guideline => 'Richtlijn';

  @override
  String get icon_guidel_main => 'Hoofd';

  @override
  String get icon_guidel_sub => 'Sub';

  @override
  String get icon_guidel_mainline => 'Hoofdlijn';

  @override
  String get icon_guidel_subline => 'Sublijn';

  @override
  String get icon_guidel_linelength => 'Lijnlengte';

  @override
  String get icon_guidel_line_l => 'L';

  @override
  String get icon_guidel_line_m => 'M';

  @override
  String get icon_guidel_line_s => 'S';

  @override
  String get icon_guidel_color => 'Kleur';

  @override
  String get icon_guidel_position => 'Positie';

  @override
  String get icon_guidel_main_pos => 'Positie hoofdlijn';

  @override
  String get icon_guidel_sub_pos => 'Positie sublijn';

  @override
  String get icon__guidel_sub_frommain => 'Afstand vanaf de hoofdlijn';

  @override
  String get icon_guidel_gridsize => 'Rastergrootte';

  @override
  String get icon_guidel_angle => 'Hoek';

  @override
  String get icon_guidel_seamallowance => 'Marge';

  @override
  String get icon_guidel_spacing => 'Afstand';

  @override
  String get icon_guidel_lengthl_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengthl_zz_s => '%%%icon%%%';

  @override
  String get icon_guidel_lengthm_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengthm_zz_s => '%%%icon%%%';

  @override
  String get icon_guidel_lengths_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengths_zz_s => '%%%icon%%%';

  @override
  String get icon_position => 'Positie';

  @override
  String get icon_00031_2 => 'Bewerken';

  @override
  String get icon_00033_1 => 'Toevoegen';

  @override
  String get icon_00035 => 'Borduren';

  @override
  String get icon_return => 'Terug';

  @override
  String get icon_00038_1 => 'Instellen';

  @override
  String get icon_00038_2 => 'Instellen';

  @override
  String get icon_00039 => 'min';

  @override
  String get icon_00041_1 => 'Selecteren';

  @override
  String get icon_select => 'Selecteren';

  @override
  String get icon_select_2 => 'Selec-\nteren';

  @override
  String get icon_00041_2 => 'Selec-\nteren';

  @override
  String get icon_00042 => 'Altijd indrukken tijdens\nhet verwijderen van de\nborduureenheid.';

  @override
  String get icon_00046_zz => '%%%icon%%%';

  @override
  String get icon_00046_zz_s => '%%%icon%%%';

  @override
  String get icon_00048 => 'Nummer';

  @override
  String get icon_00049 => 'Lijst';

  @override
  String get icon_00050 => 'Laden';

  @override
  String get icon_00051_zz => '%%%icon%%%';

  @override
  String get icon_00051_zz_s => '%%%icon%%%';

  @override
  String get icon_00052_zz => '%%%icon%%%';

  @override
  String get icon_00052_zz_s => '%%%icon%%%';

  @override
  String get icon_00053_b1 => '%%%none%%%';

  @override
  String get icon_00053_b2 => '%%%none%%%';

  @override
  String get icon_00053_t1 => 'Color Visualizer';

  @override
  String get icon_00053_t2 => 'Color\nVisualizer';

  @override
  String get icon_00055_1 => 'Willekeurig';

  @override
  String get icon_00055_2 => 'Willekeurig';

  @override
  String get icon_00056_1 => 'Gradatie';

  @override
  String get icon_00056_2 => 'Gradatie';

  @override
  String get icon_00057 => 'Levendig';

  @override
  String get icon_00054 => 'Zacht';

  @override
  String get icon_00058_1 => 'Aantal kleuren';

  @override
  String get icon_00059 => 'Verversen';

  @override
  String get icon_00060 => 'Geen opgegeven';

  @override
  String get icon_emb_tension => 'Spanning';

  @override
  String get icon_emb_threadcutting => 'Draadknip';

  @override
  String get icon_00063_a => 'Einde kleur knippen';

  @override
  String get icon_00064_a => 'Overspringende steken knippen';

  @override
  String get icon_00065 => 'Dichtheid';

  @override
  String get icon_00066 => 'For-maat';

  @override
  String get icon_00067_zz => '%%%icon%%%';

  @override
  String get icon_00067_zz_s => '%%%icon%%%';

  @override
  String get icon_00068_zz => '%%%icon%%%';

  @override
  String get icon_00068_zz_s => '%%%icon%%%';

  @override
  String get icon_00070_zz => '%%%icon%%%';

  @override
  String get icon_00070_zz_s => '%%%icon%%%';

  @override
  String get icon_00071_zz => '%%%icon%%%';

  @override
  String get icon_00071_zz_s => '%%%icon%%%';

  @override
  String get icon_00072 => 'Lay-Out';

  @override
  String get icon_00075_zz => '%%%icon%%%';

  @override
  String get icon_00075_zz_s => '%%%icon%%%';

  @override
  String get icon_00076_zz => '%%%icon%%%';

  @override
  String get icon_00076_zz_s => '%%%icon%%%';

  @override
  String get icon_00077_zz => '%%%icon%%%';

  @override
  String get icon_00077_zz_s => '%%%icon%%%';

  @override
  String get icon_00079 => 'Naaldpositie';

  @override
  String get icon_00080 => 'Volgende';

  @override
  String get icon_prev => 'Vorige';

  @override
  String get icon_segment => 'Segment';

  @override
  String get icon_00083 => 'Instelling eindpunt';

  @override
  String get icon_00084 => 'Afstelling lengte';

  @override
  String get icon_00085 => 'Instelling eindpunt\nTijdelijke stop';

  @override
  String get icon_00088 => 'Scan';

  @override
  String get icon_00089 => 'Video';

  @override
  String get icon_00090 => 'Herhalen';

  @override
  String get icon_00091_1 => 'Meervoudige selectie';

  @override
  String get icon_00091_2 => 'Meervoudige\nselectie';

  @override
  String get icon_00093_zz => '%%%icon%%%';

  @override
  String get icon_00093_zz_s => '%%%icon%%%';

  @override
  String get icon_00094_zz => '%%%icon%%%';

  @override
  String get icon_00094_zz_s => '%%%icon%%%';

  @override
  String get icon_00095 => 'Afsluiten';

  @override
  String get icon_00096 => 'Automatische splitsing van quilt-tussenstroken';

  @override
  String get icon_resettodef => 'Terugzetten naar standaardinstellingen';

  @override
  String get icon_resettodefall => 'Terugzetten naar standaard';

  @override
  String get icon_resettodefall_2 => 'Terugzetten naar\nstandaard';

  @override
  String get icon_00100 => 'Taal';

  @override
  String get icon_00101_a => 'Nederlands';

  @override
  String get icon_00101_b => 'Dutch';

  @override
  String get icon_00102 => 'HelderheidLCD-scherm';

  @override
  String get icon_00103 => 'Helderheid beeldscherm';

  @override
  String get icon_00104 => 'Schermbeveiliging';

  @override
  String get icon_00105 => 'Standaard';

  @override
  String get icon_00106 => 'Aanpassen';

  @override
  String get icon_00107 => 'Ecomodus';

  @override
  String get icon_00108 => 'Afsluitondersteun-\nmodus';

  @override
  String get icon_00109 => 'Licht';

  @override
  String get icon_00112 => 'Luidsprekervolume';

  @override
  String get icon_00114 => 'Volume';

  @override
  String get icon_00115 => 'Muisaanwijzer';

  @override
  String get icon_00116 => 'mm / \"(inch)';

  @override
  String get icon_00118 => 'Startpagina';

  @override
  String get icon_00119 => 'Naai/\nborduurscherm';

  @override
  String get icon_00192 => 'Beginscherm';

  @override
  String get icon_00121 => 'Beginscherm';

  @override
  String get icon_00122 => 'Eerste steekpagina';

  @override
  String get icon_00123 => 'LED kloshouder';

  @override
  String get icon_00124 => 'Steekbreedte regeling';

  @override
  String get icon_00125_1 => 'Verticale fijnafstelling';

  @override
  String get icon_00126_1 => 'Horizontale fijnafstelling';

  @override
  String get icon_00127_1 => 'Persvoethoogte';

  @override
  String get icon_00128_1 => 'Persvoetdruk';

  @override
  String get icon_00129 => 'Beginpositie';

  @override
  String get icon_00130_1 => 'Draaipositie hoogte';

  @override
  String get icon_00131_1 => 'Persvoethoogte\nVrije hand naaien';

  @override
  String get icon_00134 => 'Automatisch\nstofsensor systeem';

  @override
  String get icon_00135 => 'Stofdiktesensor';

  @override
  String get icon_00136_2 => 'Naaldstand -\nOmhoog/Omlaag';

  @override
  String get icon_00137 => 'Naaldstand -\nsteek plaatsen';

  @override
  String get icon_00138 => 'Boven- en\nonderdraadsensor';

  @override
  String get icon_00140 => 'Transport aanpassen\nvan boventransport';

  @override
  String get icon_00141 => 'Multifunctioneel\nvoetpedaal';

  @override
  String get icon_00142 => 'Hielaanslag';

  @override
  String get icon_00143 => 'Zijpedaal';

  @override
  String get icon_00144_a => 'Naaldstand\nomhoog/omlaag';

  @override
  String get icon_00145 => 'Draad afsnijden';

  @override
  String get icon_00146 => 'Enkele steek';

  @override
  String get icon_00147 => 'Achteruitsteek';

  @override
  String get icon_00243 => 'Persvoet\nomhoog/omlaag';

  @override
  String get icon_00244 => 'Geen instelling';

  @override
  String get icon_00249 => 'Verstevigings-\nsteek';

  @override
  String get icon_00148 => 'Gleuflengte';

  @override
  String get icon_00148_zz => '%%%icon%%%';

  @override
  String get icon_00148_zz_s => '%%%icon%%%';

  @override
  String get icon_00150 => 'Verstevigingsprioriteit';

  @override
  String get icon_00152_1 => 'Borduurraamscherm';

  @override
  String get icon_00155_1 => 'Miniatuurafbeelding\nformaat';

  @override
  String get icon_00157 => 'Achtergrondkleur\nborduurpatroon';

  @override
  String get icon_00159 => 'Achtergrondkleur\nMiniatuurafbeelding';

  @override
  String get icon_00163_a => 'Weergave\nachtergrondafbeelding';

  @override
  String get icon_00163 => 'Achtergrond-\nafbeelding';

  @override
  String get icon_00164 => 'Afbeelding scannen';

  @override
  String get icon_00165 => 'Standaard';

  @override
  String get icon_00166 => 'Fijn';

  @override
  String get icon_00167 => 'Scankwaliteit';

  @override
  String get icon_00168 => 'LED klossenstandaard';

  @override
  String get icon_00178 => 'Afstand borduurpatroon -\nrijgsteek';

  @override
  String get icon_00180 => 'Afstand borduurpatroon -\napplicatie';

  @override
  String get icon_00182_1 => 'Max. borduursnelheid';

  @override
  String get icon_00183_1 => 'Borduurspanning';

  @override
  String get icon_00184_1 => 'Borduurvoethoogte';

  @override
  String get icon_00185 => 'Raamgrootte';

  @override
  String get icon_00186 => 'Raster';

  @override
  String get icon_00187 => 'Wijzigen';

  @override
  String get icon_00188 => 'Verwijderen';

  @override
  String get icon_00191 => 'Kleur';

  @override
  String get icon_00193 => 'Klokscherm';

  @override
  String get icon_00194 => 'AM';

  @override
  String get icon_00195 => 'PM';

  @override
  String get icon_00196 => '24h';

  @override
  String get icon_clock_msg1 => 'Stel de juiste datum in voor de netwerkverbinding.';

  @override
  String get icon_00197 => 'Kalibreren sensorfunctie';

  @override
  String get icon_00199 => 'Aanpassen\ngeleidelijnmarkering';

  @override
  String get icon_00200 => 'Helderheid\ngeleidelijnmarkering';

  @override
  String get icon_00201_1 => 'Aanpassen borduurvoet\nmet LED-pointer';

  @override
  String get icon_00202_p => 'Helderheid';

  @override
  String get icon_00206_1 => 'Certifcatie';

  @override
  String get icon_00207_a => 'Kit';

  @override
  String get icon_00208 => 'Start';

  @override
  String get icon_00209 => 'Stop';

  @override
  String get icon_00211 => 'Serviceherinnering';

  @override
  String get icon_00212 => 'SCS';

  @override
  String get icon_00214 => 'Totaalaantal';

  @override
  String get icon_00218 => 'No.';

  @override
  String get icon_00220 => 'Versie';

  @override
  String get icon_00222 => 'YYYY';

  @override
  String get icon_00223 => 'MM';

  @override
  String get icon_00224 => 'DD';

  @override
  String get icon_00225 => 'mm';

  @override
  String get icon_00226 => '\"';

  @override
  String get icon_on => 'ON';

  @override
  String get icon_off => 'OFF';

  @override
  String get icon_00229 => 'KB';

  @override
  String get icon_00230 => 'bPocket';

  @override
  String get icon_00231 => '1';

  @override
  String get icon_00232 => '2';

  @override
  String get icon_00233 => '3';

  @override
  String get icon_00234 => '4';

  @override
  String get icon_00235 => '5';

  @override
  String get icon_00236 => '6';

  @override
  String get icon_00237 => '7';

  @override
  String get icon_00238 => '8';

  @override
  String get icon_00239 => '9';

  @override
  String get icon_00240 => '0';

  @override
  String get icon_00241 => 'C';

  @override
  String get icon_00242 => '%';

  @override
  String get icon_00245 => 'Automatisch omhoog\nbrengen/omlaag brengen\nvan de naaivoet';

  @override
  String get icon_00246 => 'Omlaag';

  @override
  String get icon_00247 => 'Omhoog';

  @override
  String get icon_00248_zz => '%%%icon%%%';

  @override
  String get icon_00248_zz_s => '%%%icon%%%';

  @override
  String get icon_00248 => 'Indrukken om af te\nknippen';

  @override
  String get icon_00251 => 'Projector';

  @override
  String get icon_00253 => 'Achtergrondkleur';

  @override
  String get icon_00254 => 'Naaien:\nPatroonomtrek';

  @override
  String get icon_00255 => 'Borduren:\nAanwijzerkleur';

  @override
  String get icon_pointershape => 'Aanwijzervorm';

  @override
  String get icon_00256 => 'Camera';

  @override
  String get icon_00257 => 'Naald kalibreren voor\ncamera/projector';

  @override
  String get icon_recog_ok => 'OK';

  @override
  String get icon_recog_ng => 'NG';

  @override
  String get icon_00258 => 'Borduur-\nnaaldstopstand';

  @override
  String get icon_00259 => 'Eenheid';

  @override
  String get icon_00260 => 'Garenkleur';

  @override
  String get icon_00261 => 'Garenmerk';

  @override
  String get icon_00264 => 'Naam Kleur';

  @override
  String get icon_00265 => '# 123';

  @override
  String get icon_00266 => 'Tijd';

  @override
  String get icon_00268 => 'Origineel';

  @override
  String get icon_00269 => 'Embroidery';

  @override
  String get icon_00269_t => 'Embroidery';

  @override
  String get icon_00270 => 'Country';

  @override
  String get icon_00270_t => 'Country';

  @override
  String get icon_00271 => 'Madeira\nPoly';

  @override
  String get icon_00272 => 'Madeira\nRayon';

  @override
  String get icon_00273 => 'Sulky';

  @override
  String get icon_00274 => 'Robison-Anton';

  @override
  String get icon_00275 => 'Robison-Anton\nPoly';

  @override
  String get icon_00276 => 'Robison-Anton\nRayon';

  @override
  String get icon_00277 => 'Isacord';

  @override
  String get icon_00278 => 'Gütermann';

  @override
  String get icon_00279 => 'Simplicity Pro';

  @override
  String get icon_00279_p => 'Pacesetter Pro';

  @override
  String get icon_00280 => 'Floriani';

  @override
  String get icon_00281 => 'Iris';

  @override
  String get icon_00282 => 'Aurifil';

  @override
  String get icon_00283 => 'WonderFil ';

  @override
  String get icon_00284 => 'Polyfast';

  @override
  String get icon_00290 => 'Als u de upgrade-kit heeft aangeschaft\nen u de machine wilt certificeren,\ndruk dan op [CERTIFICATIE].';

  @override
  String get icon_00291 => 'KIT I';

  @override
  String get icon_00292 => 'KIT II';

  @override
  String get icon_00293 => 'KIT III';

  @override
  String get icon_00294 => 'KIT IV';

  @override
  String get icon_00295 => 'KIT V';

  @override
  String get icon_00296 => 'KIT VI';

  @override
  String get icon_00297 => 'KIT VII';

  @override
  String get icon_00298 => 'KIT VIII';

  @override
  String get icon_00299 => 'KIT IX';

  @override
  String get icon_00300 => 'KIT X';

  @override
  String get icon_00643_s => 'Geen';

  @override
  String get icon_00301 => 'Gebruiksaanwijzing';

  @override
  String get icon_00302 => 'Naaiaanwijzing';

  @override
  String get icon_00303 => 'Patroonuitleg';

  @override
  String get icon_manuals => 'Handleidingen';

  @override
  String get icon_operariong_b => 'Bedieningshandleiding';

  @override
  String get icon_operariong_t => 'Gebruiksaanwijzing en naslaggids (English)';

  @override
  String get icon_pdf => 'PDF-versie handleiding';

  @override
  String get icon_supportsite => 'Ondersteuningssite';

  @override
  String get icon_pdf_eula => 'Licentieovereenkomsten voor\neindgebruikers (EULA)';

  @override
  String get icon_pdf_sewing => 'Naaien';

  @override
  String get icon_pdf_emb => 'Borduren';

  @override
  String get icon_pdf_sewing_ef => 'Naaien (English)';

  @override
  String get icon_pdf_emb_ef => 'Borduren (English)';

  @override
  String get icon_pdf_sewing_t => 'Naaien (English)';

  @override
  String get icon_pdf_emb_t => 'Borduren (English)';

  @override
  String get icon_f_omadendum => 'Bijvoegsel';

  @override
  String get icon_f_omadendum_ef => 'Bijvoegsel (English)';

  @override
  String get icon_f_omadendum_l => 'Bedieningshandleiding\nBijvoegsel';

  @override
  String get icon_f_om_kit1 => 'KIT I';

  @override
  String get icon_f_om_kit2 => 'KIT II';

  @override
  String get icon_f_om_kit3 => 'KIT III';

  @override
  String get icon_f_om_kit1_l => 'Bedieningshandleiding\nKIT I';

  @override
  String get icon_f_omadendum_t => 'Bijvoegsel (English)';

  @override
  String get icon_f_om_kit1_t => 'KIT I (English)';

  @override
  String get icon_f_om_kit2_t => 'KIT II (English)';

  @override
  String get icon_f_om_kit3_t => 'KIT III (English)';

  @override
  String get icon_t_pdf_iivo_url_b => 'Als u de handleidingen wilt weergeven op uw mobiele apparaat of PC, gaat u naar\nhttps://s.brother/fmraa.';

  @override
  String get icon_t_pdf_iivo_url_t => 'Als u de handleiding wilt weergeven op uw mobiele apparaat of PC, gaat u naar\nhttps://babylock.com/radiance-instruction-and-reference-guide.';

  @override
  String get icon_t_video_iivo_url_b => 'Ga naar\n https://s.brother/fvraa\nom de instructievideo’s voor dit model te bekijken.';

  @override
  String get icon_t_video_iivo_url_t => 'Ga naar\n https://babylock.com/radiance-training\nom de instructievideo’s voor dit model te bekijken.';

  @override
  String get icon_pdf_url_qr_t => 'www.babylock.com';

  @override
  String get icon_nettool => 'Netwerkdiagnosetool';

  @override
  String get icon_iagree => 'Ik ga akkoord';

  @override
  String get icon_terms_cancel => 'Annuleren';

  @override
  String get icon_confirm => 'Bevestigen';

  @override
  String get icon_00304 => 'Belangrijkste onderdelen';

  @override
  String get icon_00305 => 'Belangrijkste Toetsen';

  @override
  String get icon_00306 => 'Basisbediening';

  @override
  String get icon_00307 => 'Borduren\nBasisbediening';

  @override
  String get icon_00308 => 'Probleem oplossen';

  @override
  String get icon_00309 => 'Onderhoud';

  @override
  String get icon_00310 => 'De draad is verstrikt geraakt\nop de achterkant van de stof';

  @override
  String get icon_00311 => 'Kan de naald niet\ninrijgen';

  @override
  String get icon_00312 => 'Kan de naaldinrijger\nniet gebruiken';

  @override
  String get icon_00313 => 'Draadspanning is onjuist';

  @override
  String get icon_00314 => 'Bovendraad breekt';

  @override
  String get icon_00315 => 'Onderdraad breekt';

  @override
  String get icon_00316 => 'Overgeslagen steken';

  @override
  String get icon_00317 => 'Naald breekt';

  @override
  String get icon_00318 => 'Naaimachine werkt niet';

  @override
  String get icon_00320 => 'Letterpatroon valt verkeerd uit';

  @override
  String get icon_00321 => 'Stof wordt niet door de\nmachine heen gevoerd';

  @override
  String get icon_00322 => 'Stof rimpelt';

  @override
  String get icon_00323 => 'De machine maakt veel lawaai';

  @override
  String get icon_00325 => 'Borduurpatroon wordt niet\ngoed genaaid';

  @override
  String get icon_00326 => 'Borduurtafel werkt niet';

  @override
  String get icon_00331 => 'Bartack';

  @override
  String get icon_00332 => 'Blindzoomsteek';

  @override
  String get icon_00333 => 'Knoopsgat';

  @override
  String get icon_00334 => 'Knopen';

  @override
  String get icon_00335 => 'Figuurnaadzoom';

  @override
  String get icon_00336 => 'Platte Zoomsteek';

  @override
  String get icon_00337 => 'Plisseren';

  @override
  String get icon_00338 => 'Overhandse Steek';

  @override
  String get icon_00339 => 'Rechte Steek';

  @override
  String get icon_00340 => 'Schelp';

  @override
  String get icon_00341 => 'Rechte Steek';

  @override
  String get icon_00342 => 'Rits Inzetten';

  @override
  String get icon_00343 => 'Stukken Stof Aan Elkaar Zetten';

  @override
  String get icon_00344 => 'Vrij Quilten';

  @override
  String get icon_00345 => 'Quilten';

  @override
  String get icon_00346 => 'Echoquilten';

  @override
  String get icon_00347 => 'Applicatie 1';

  @override
  String get icon_00348 => 'Applicatie 2';

  @override
  String get icon_search => 'Zoeken';

  @override
  String get icon_00353 => 'De bovendraad inrijgen';

  @override
  String get icon_00354 => 'De spoel opwinden';

  @override
  String get icon_00355 => 'De naald verwisselen';

  @override
  String get icon_00356 => 'De persvoet verwisselen';

  @override
  String get icon_00357 => 'De spoel installeren';

  @override
  String get icon_00358 => 'Naaifunctie';

  @override
  String get icon_00359 => 'Werken met de draadknipfunctie';

  @override
  String get icon_00360 => 'Werken met de speciale schroevendraaier';

  @override
  String get icon_00361 => 'Werken met de spilfunctie';

  @override
  String get icon_00362 => 'De steekbreedte en steeklengte instellen';

  @override
  String get icon_00363 => 'Werken met de multifunctionele schroevendraaier';

  @override
  String get icon_00364 => 'Werken met de automatische stofsensor (automatische persvoetdruk)';

  @override
  String get icon_00365 => 'Werken met My Custom Stitch';

  @override
  String get icon_00366 => 'Werken met de randnaaifunctie';

  @override
  String get icon_00367 => 'Werken met de spoel (naaien)';

  @override
  String get icon_00368 => 'Werken met de spoel (borduren)';

  @override
  String get icon_00369 => 'Voorbereidingen voor het werken met de spoel';

  @override
  String get icon_00370 => 'Voorbereidingen voor omgekeerd werken met de spoel';

  @override
  String get icon_00371 => 'Omgekeerd werken met de spoel (naaien)';

  @override
  String get icon_00372 => 'Werken met de ingebouwde camera in de naaimodus';

  @override
  String get icon_00373 => 'De naaldpositie aanpassen met de geleidelijnmarkering in het instellingenscherm';

  @override
  String get icon_00374 => 'De helderheid van de geleidelijnmarkering aanpassen in het instellingenscherm';

  @override
  String get icon_00375 => 'De draadspanning instellen';

  @override
  String get icon_00376 => 'Opstrijksteunstof bevestigen';

  @override
  String get icon_00377 => 'Stof vastzetten in het borduurraam';

  @override
  String get icon_00378 => 'Het borduurraam bevestigen/verwijderen';

  @override
  String get icon_00379 => 'De borduurtafel/accessoiretafel bevestigen/verwijderen';

  @override
  String get icon_00380 => 'De persvoethouder bevestigen/verwijderen';

  @override
  String get icon_00381 => 'Borduurfunctie';

  @override
  String get icon_00382 => 'Werken met de functie Afdrukken & Steken';

  @override
  String get icon_00383 => 'Werken met de functie Kleurvariaties';

  @override
  String get icon_00384 => 'Werken met My Design Center';

  @override
  String get icon_00385 => 'Lijntekeningen scannen';

  @override
  String get icon_00386 => 'Illustraties scannen';

  @override
  String get icon_00387 => 'Werken met het scankader';

  @override
  String get icon_00388 => 'De stof weergeven op het LCD-scherm (scannen met ingebouwde camera)';

  @override
  String get icon_00389 => 'De borduurpositie uitlijnen met de positiesticker';

  @override
  String get icon_00390 => 'Patronen verbinden met de ingebouwde camera';

  @override
  String get icon_00391 => 'De borduurpositie uitlijnen met de ingebouwde camera';

  @override
  String get icon_00392 => '';

  @override
  String get icon_00393 => 'Instellingen';

  @override
  String get icon_00394 => 'Naaldpositie instellen met de camera';

  @override
  String get icon_00395 => 'Een software-upgrade uitvoeren op de machine';

  @override
  String get icon_00396 => 'Geleidelijnmarkering aanpassen in het instellingenscherm';

  @override
  String get icon_00397 => 'De tijd/datum instellen';

  @override
  String get icon_00398 => 'Werken met automatische verstevigingssteken';

  @override
  String get icon_00399 => 'Overige';

  @override
  String get icon_00400 => 'Video\'s weergeven/opslaan';

  @override
  String get icon_00401 => 'Sensorpen';

  @override
  String get icon_00402 => 'De sensorpen aansluiten';

  @override
  String get icon_00403 => 'De sensorpen kalibreren';

  @override
  String get icon_00404 => 'De positie van de geleidelijnmarkering opgeven met de sensorpen';

  @override
  String get icon_00405 => 'De naaldpositie opgeven met de sensorpen';

  @override
  String get icon_00406 => 'De steekbreedte/positie opgeven met de sensorpen';

  @override
  String get icon_00407 => 'Het naai-eindpunt opgeven met de sensorpen';

  @override
  String get icon_00408 => 'De borduurpositie opgeven met de sensorpen';

  @override
  String get icon_00409 => 'Accessoire';

  @override
  String get icon_00410 => 'Werken met de kniehevel';

  @override
  String get icon_00411 => 'Werken met de multifunctionele schroevendraaier';

  @override
  String get icon_00412 => 'Gebruik van de multifunctionele schroevendraaier';

  @override
  String get icon_00416 => 'Het multifunctionele voetpedaal installeren';

  @override
  String get icon_00417 => 'Functies toekennen aan het multifunctionele voetpedaal';

  @override
  String get icon_00418 => 'De borduurvoet met LED-aanwijzer bevestigen/verwijderen';

  @override
  String get icon_00419 => 'De borduurvoet met LED-aanwijzer bevestigen';

  @override
  String get icon_00420 => 'Stippelborduurpatronen maken met de ingebouwde camera';

  @override
  String get icon_00421 => 'De persvoet bevestigen met het meegeleverde opzetstuk';

  @override
  String get icon_00422 => 'Werken met de accessoirehouder';

  @override
  String get icon_00423 => 'Onderhoud (het loophuis reinigen)';

  @override
  String get icon_00500 => 'Mijn Design Center';

  @override
  String get icon_00500_2 => 'Mijn Design\nCenter';

  @override
  String get icon_iqdesigner => 'IQ Designer';

  @override
  String get icon_00501 => 'Lijn scannen';

  @override
  String get icon_00503_zz => '%%%icon%%%';

  @override
  String get icon_00503_zz_s => '%%%icon%%%';

  @override
  String get icon_00505 => 'Illustratie scannen';

  @override
  String get icon_imagescan => 'Afbeeldingsscan';

  @override
  String get icon_linedesign => 'Lijnontwerp';

  @override
  String get icon_illustrationdesign => 'Afbeeldingsontwerp';

  @override
  String get icon_00509_zz => '%%%icon%%%';

  @override
  String get icon_00510 => 'Herkennen';

  @override
  String get icon_00511_1 => 'Voorbeeld';

  @override
  String get icon_00511_2 => 'Voorbeeld';

  @override
  String get icon_showpreview => 'Voorbeeld weergeven';

  @override
  String get icon_00512 => 'Opnieuw\nproberen';

  @override
  String get icon_00514 => 'Objectformaat negeren';

  @override
  String get icon_00516 => 'Detectieniveau grijstonen';

  @override
  String get icon_00503 => 'Lijn';

  @override
  String get icon_00518 => 'Vlakgom';

  @override
  String get icon_00520 => 'Weergave\norigineel';

  @override
  String get icon_00521 => 'Weergave\nresultaat';

  @override
  String get icon_00522 => 'Weergave resultaat';

  @override
  String get icon_00523 => 'Max. aantal\nkleuren';

  @override
  String get icon_00525 => 'Achtergrond\nverwijderen';

  @override
  String get icon_00526 => 'Herkennen';

  @override
  String get icon_00528 => 'Borduurinstellingen';

  @override
  String get icon_00529 => 'Eigenschappen lijn';

  @override
  String get icon_00530 => 'Eigenschappen gebied';

  @override
  String get icon_00533 => 'Afmeting';

  @override
  String get icon_00537 => 'Zigzagsteek-\nbreedte';

  @override
  String get icon_00538 => 'Dichtheid';

  @override
  String get icon_00539 => 'Steeklengte';

  @override
  String get icon_00540 => 'Vulsteek';

  @override
  String get icon_00541 => 'Richting';

  @override
  String get icon_00544 => 'Trek-\ncompensatie';

  @override
  String get icon_00545 => 'Met\nversteviging';

  @override
  String get icon_00547 => 'Afstand';

  @override
  String get icon_00548_1 => 'Handmatig';

  @override
  String get icon_00548_2 => 'Handm.';

  @override
  String get icon_00549_1 => 'Automatisch';

  @override
  String get icon_00549_2 => 'Auto';

  @override
  String get icon_00550 => 'Naar\nSteek';

  @override
  String get icon_00551 => 'Afbeelding in een kader plaatsen';

  @override
  String get icon_00552 => 'Kleur specificatie';

  @override
  String get icon_00553 => 'Volgende';

  @override
  String get icon_00554 => 'Afstand';

  @override
  String get icon_00555 => 'Omtrekken opslaan';

  @override
  String get icon_00556 => 'Gesloten vormen';

  @override
  String get icon_00557 => 'Open vormen';

  @override
  String get icon_00558 => 'Opgeslagen omtrekken';

  @override
  String get icon_00559 => 'Borduurgebieden raam';

  @override
  String get icon_00562 => 'Omtrek';

  @override
  String get icon_00564 => 'Dikte';

  @override
  String get icon_00565 => 'Willekeurige\nverschuiving';

  @override
  String get icon_00566 => 'Positie-\nverschuiving';

  @override
  String get icon_inside => 'Binnen';

  @override
  String get icon_outside => 'Buiten';

  @override
  String get icon_00567 => 'Spiegelen';

  @override
  String get icon_00568 => 'Steekbreedte';

  @override
  String get icon_00569 => 'Actueel';

  @override
  String get icon_00570 => 'Nieuw';

  @override
  String get icon_frame_297_465_mm => '297 × 465 mm';

  @override
  String get icon_frame_297_465_inch => '11-5/8\"× 18-1/4\"';

  @override
  String get icon_frame_272_408_mm => '272 × 408 mm';

  @override
  String get icon_frame_272_408_inch => '10-5/8\"× 16\"';

  @override
  String get icon_frame_254_254_mm => '254 × 254 mm';

  @override
  String get icon_frame_254_254_inch => '10\"× 10\"';

  @override
  String get icon_frame_240_360_mm => '240 × 360 mm';

  @override
  String get icon_frame_240_360_inch => '9-1/2\"× 14\"';

  @override
  String get icon_frame_180_360_mm => '180 × 360 mm';

  @override
  String get icon_frame_180_360_inch => ' 7\" × 14\"';

  @override
  String get icon_frame_180_300_mm => '180 × 300 mm';

  @override
  String get icon_frame_180_300_inch => ' 7\" × 12\"';

  @override
  String get icon_frame_200_300_mm => '200 × 300 mm';

  @override
  String get icon_frame_200_300_inch => '8\"×12\"';

  @override
  String get icon_frame_100_300_mm => '100 × 300 mm';

  @override
  String get icon_frame_100_300_inch => '4\"× 12\"';

  @override
  String get icon_frame_160_260_mm => '160 × 260 mm';

  @override
  String get icon_frame_160_260_inch => '6-1/4\"× 10-1/4\"';

  @override
  String get icon_frame_240_240_mm => '240 × 240 mm';

  @override
  String get icon_frame_240_240_inch => '9-1/2\"× 9-1/2\"';

  @override
  String get icon_frame_200_200_mm => '200 × 200 mm';

  @override
  String get icon_frame_200_200_inch => '8\"× 8\"';

  @override
  String get icon_frame_130_180_mm => '130 × 180 mm';

  @override
  String get icon_frame_130_180_inch => '5\"× 7\"';

  @override
  String get icon_frame_100_180_mm => '100 × 180 mm';

  @override
  String get icon_frame_100_180_inch => '4\"× 7\"';

  @override
  String get icon_frame_150_150_mm => '150 × 150 mm';

  @override
  String get icon_frame_150_150_inch => '6\"× 6\"';

  @override
  String get icon_frame_100_100_mm => '100 × 100 mm';

  @override
  String get icon_frame_100_100_inch => '4\"× 4\"';

  @override
  String get icon_frame_60_20_mm => '60 × 20 mm';

  @override
  String get icon_frame_60_20_inch => '2-3/8\"× 3/4\"';

  @override
  String get icon_zoom_50 => '50';

  @override
  String get icon_zoom_100 => '100';

  @override
  String get icon_zoom_125 => '125';

  @override
  String get icon_zoom_150 => '150';

  @override
  String get icon_zoom_200 => '200';

  @override
  String get icon_zoom_400 => '400';

  @override
  String get icon_zoom_800 => '800';

  @override
  String get icon_zoom_120 => '120';

  @override
  String get icon_zoom_240 => '240';

  @override
  String get icon_zoom_480 => '480';

  @override
  String get icon_zoom_960 => '960';

  @override
  String get icon_00600 => 'Inschakeling draadloos LAN';

  @override
  String get icon_00600_1 => 'WLAN Activeren';

  @override
  String get icon_00601 => 'SSID';

  @override
  String get icon_00602 => 'SSID selecteren...';

  @override
  String get icon_00603 => 'Machinenaam';

  @override
  String get icon_00604 => 'WPS (drukknop)';

  @override
  String get icon_00605 => 'WPS (PIN)';

  @override
  String get icon_00606 => 'Overige';

  @override
  String get icon_00608 => 'Status draadloos LAN';

  @override
  String get icon_00608_1 => 'Status WLAN';

  @override
  String get icon_00609 => 'Opgeslagen SSID';

  @override
  String get icon_00609_1 => 'Opgeslagen SSID';

  @override
  String get icon_00610 => 'Nieuwe\nSSID';

  @override
  String get icon_wlan_title => 'WLAN Draadloze verbinding';

  @override
  String get icon_wlan_connection => 'Draadloze verbinding (WLAN)';

  @override
  String get icon_wlan_networks => 'Netwerken WLAN Draadloze verbinding';

  @override
  String get icon_wlan_enable => 'Inschakeling draadloos LAN';

  @override
  String get icon_wlan_setinfo_01 => 'Om beschikbare netwerken te zien, schakelt u Inschakeling draadloos LAN in.';

  @override
  String get icon_wlan_setinfo_02 => 'Zoeken naar draadloze LAN-netwerken…';

  @override
  String get icon_wlan_setinfo_03 => 'Verbinden met WLAN Draadloze verbinding…';

  @override
  String get icon_wlan_setinfo_05 => 'Inschakelen van WLAN Draadloze verbinding…';

  @override
  String get icon_wlan_setinfo_06 => 'WLAN Draadloze verbinding ingeschakeld';

  @override
  String get icon_wlan_setinfo_04 => 'WLAN Draadloze verbinding uitschakelen…';

  @override
  String get icon_wlan_setinfo_07 => 'Hiermee worden alle netwerkinstellingen gereset, waaronder:·Draadloos LAN';

  @override
  String get icon_wlan_networkreset => 'Netwerk resetten';

  @override
  String get icon_wlan_limitedconnect => 'Kan geen verbinding maken met het netwerk. Controleer de klokinstellingen.';

  @override
  String get icon_00630 => 'Netwerk';

  @override
  String get icon_00631 => 'Installatiewizard\nvoor draadloos LAN';

  @override
  String get icon_00631_1 => 'Inst. Wizard';

  @override
  String get icon_00632 => 'Detail';

  @override
  String get icon_00633 => 'Status';

  @override
  String get icon_00634 => 'Signaal';

  @override
  String get icon_00635 => 'Comm. Modus';

  @override
  String get icon_00636 => 'Actief(11b)';

  @override
  String get icon_00637 => 'Actief(11g)';

  @override
  String get icon_00638 => 'Actief(11n)';

  @override
  String get icon_00639 => 'Verbinding mislukt';

  @override
  String get icon_00640 => 'Sterk';

  @override
  String get icon_00641 => 'Gemiddeld';

  @override
  String get icon_00642 => 'Zwak';

  @override
  String get icon_00643 => 'Geen';

  @override
  String get icon_00644 => 'Ad-hoc';

  @override
  String get icon_00645 => 'Infrastructuur';

  @override
  String get icon_00646 => 'TCP/IP';

  @override
  String get icon_00647 => 'MAC-adres';

  @override
  String get icon_00648 => 'Proxy-instell.';

  @override
  String get icon_00649 => 'BOOT Method';

  @override
  String get icon_00650 => 'IP Address';

  @override
  String get icon_00651 => 'Subnet Mask';

  @override
  String get icon_00652 => 'Gateway';

  @override
  String get icon_00653 => 'Knooppuntnaam';

  @override
  String get icon_00654 => 'WINS Config';

  @override
  String get icon_00655 => 'WINS-server';

  @override
  String get icon_00656 => 'DNS-server';

  @override
  String get icon_00656_p => 'DNS-server Primary';

  @override
  String get icon_00656_s => 'DNS-server Secondary';

  @override
  String get icon_00657 => 'APIPA';

  @override
  String get icon_00658 => 'Proxy-verbinding';

  @override
  String get icon_00659 => 'Adres';

  @override
  String get icon_00660 => 'Poort';

  @override
  String get icon_00661 => 'Gebruikersnaam';

  @override
  String get icon_00662 => 'Wachtwoord';

  @override
  String get icon_00663 => 'Primary';

  @override
  String get icon_00664 => 'Secondary';

  @override
  String get icon_00665 => 'Zoekend naarSSID...';

  @override
  String get icon_00666 => 'SSID van toegangspoort';

  @override
  String get icon_00667 => 'Netwerksleutel';

  @override
  String get icon_00668 => 'Ja';

  @override
  String get icon_00669 => 'Nee';

  @override
  String get icon_00670 => 'Selectie Auth.';

  @override
  String get icon_00671 => 'Open systeem';

  @override
  String get icon_00672 => 'Gedeelde sleutel';

  @override
  String get icon_00673 => 'WPA/WPA2-PSK';

  @override
  String get icon_00674 => 'Type Codering';

  @override
  String get icon_00674_a => 'Type Codering (Open systeem)';

  @override
  String get icon_00674_c => 'Type Codering (WPA/WPA2-PSK)';

  @override
  String get icon_00675 => 'WEP';

  @override
  String get icon_00676 => 'AES';

  @override
  String get icon_00677 => 'TKIP';

  @override
  String get icon_00678 => 'Uitgeschakeld';

  @override
  String get icon_00679 => 'Statisch';

  @override
  String get icon_00680 => 'Automatisch';

  @override
  String get icon_00681 => 'WPA';

  @override
  String get icon_00682 => 'Datum';

  @override
  String get icon_cert_key => 'Normale certificering';

  @override
  String get icon_cert_web => 'Online-\nmachinecertificering';

  @override
  String get icon_status_t => 'Status';

  @override
  String get icon_status_a1 => 'Niet gecontroleerd';

  @override
  String get icon_status_a2 => 'Controleren...';

  @override
  String get icon_status_a3 => 'Gecontroleerd: Reeds bijgewerkt';

  @override
  String get icon_status_a4 => 'Nieuwe update op server';

  @override
  String get icon_status_b1 => 'Niet gedownload';

  @override
  String get icon_status_b2 => 'Downloaden...';

  @override
  String get icon_status_b3 => 'Gedownload';

  @override
  String get icon_cancel_downloading => 'Download annuleren';

  @override
  String get icon_pause_downloading2 => 'Download onderbreken\nDruk op de toets Hervatten om door te gaan met downloaden';

  @override
  String get icon_status_c1 => 'De nieuwe update is nog niet geïnstalleerd.';

  @override
  String get icon_status_c2 => 'De nieuwe update is geïnstalleerd.';

  @override
  String get icon_app_dl_moniter => 'Monitoring App downloaden';

  @override
  String get icon_shape => 'Vorm';

  @override
  String get icon_favorite => 'Favorieten';

  @override
  String get icon_sash_4section => '4 delen (2×2)';

  @override
  String get icon_sash_1direction => 'Eén richting';

  @override
  String get icon_sash_1dtotal => 'Totaal aantal stuks';

  @override
  String get icon_offset => 'Compensatie';

  @override
  String get icon_startpoint => 'Beginpunt';

  @override
  String get icon_endpoint => 'Eindpunt';

  @override
  String get icon_embfootdwn => 'Borduurvoet\nAutomatisch omlaag';

  @override
  String get icon_frame_272_272_mm => '272 × 272 mm';

  @override
  String get icon_frame_272_272_inch => '10-5/8\"× 10-5/8\"';

  @override
  String get icon_appguide_w => 'Apphandleiding';

  @override
  String get icon_appguide => 'App-\nhandleiding';

  @override
  String get icon_mobileapp => 'Mobiele app';

  @override
  String get icon_app => 'App';

  @override
  String get icon_emb1 => 'Borduren 1';

  @override
  String get icon_emb2 => 'Borduren 2';

  @override
  String get icon_00185_2 => 'Raamgrootte';

  @override
  String get icon_type => 'Soort';

  @override
  String get icon_typea => 'Type A';

  @override
  String get icon_typeb => 'Type B';

  @override
  String get icon_typec => 'Type C';

  @override
  String get icon_sash_typesplit => 'Type split';

  @override
  String get icon_mystitchmonitor => 'My Stitch Monitor';

  @override
  String get icon_mydesignsnap => 'My Design Snap';

  @override
  String get icon_mystitchmonitor_t => 'IQ Intuition Monitoring';

  @override
  String get icon_mydesignsnap_t => 'IQ Intuition Positioning';

  @override
  String get icon_actcode => 'Activatiecode';

  @override
  String get icon_machineno => 'Machinenummer (No.)';

  @override
  String get icon_autodl => 'Automatisch downloaden';

  @override
  String get icon_updatemanu => 'Handmatig bijwerken';

  @override
  String get icon_dl_updateprogram => 'Software update downloaden';

  @override
  String get icon_dl_updateprogram_2 => 'Software update downloaden';

  @override
  String get icon_chk_update => 'Zoeken naar beschikbare updates';

  @override
  String get icon_pause => 'Onderbreken';

  @override
  String get icon_resume => 'Hervatten';

  @override
  String get icon_cert_method => 'Certificeringsmethode';

  @override
  String get icon_latestver => 'Nieuwste versie';

  @override
  String get icon_latestveravail => 'Nieuwste versie beschikbaar';

  @override
  String get icon_device_ios => 'Voor iOS-\napparaten';

  @override
  String get icon_device_android => 'Voor Android™-\napparaten';

  @override
  String get icon_f_ios => 'Voor iOS';

  @override
  String get icon_f_android => 'Voor Android™';

  @override
  String get icon_cws_myconnection => 'CanvasWorkspace\n (My Connection)';

  @override
  String get icon_step1 => 'STAP1:';

  @override
  String get icon_step2 => 'STAP2:';

  @override
  String get icon_step3 => 'STAP3:';

  @override
  String get icon_step4 => 'STAP4:';

  @override
  String get icon_step5 => 'STAP5:';

  @override
  String get icon_register => 'Registreren';

  @override
  String get icon_loginid => 'Login ID:';

  @override
  String get icon_id => 'ID:';

  @override
  String get icon_appq1 => 'Applicatielapje\n (normaal)';

  @override
  String get icon_appq2 => 'Applicatielapje\nvoor geselecteerde\nkleuren';

  @override
  String get icon_original_img => 'Originele afbeelding';

  @override
  String get icon_appq_stitch_1 => 'Zigzagsteek';

  @override
  String get icon_appq_stitch_2 => 'Satijnsteek';

  @override
  String get icon_appq_stitch_3 => 'Stiksteek';

  @override
  String get icon_stamp_web => 'Omtrek snijden';

  @override
  String get icon_cws_rgs_title => 'Ontvang een pincode om uw machine te registreren.';

  @override
  String get icon_cws_rgs_s1 => 'Meld u aan bij CanvasWorkspace.\nhttp://CanvasWorkspace.Brother.com';

  @override
  String get icon_cws_rgs_s2 => 'Tik op [Accountinstellingen].';

  @override
  String get icon_pincode => 'Pincode';

  @override
  String get icon_kitsnc => 'ScanNCut (My Connection)';

  @override
  String get icon_snc1 => 'ScanNCut';

  @override
  String get icon_f_om_kitsnc => 'ScanNCut (My Connection)';

  @override
  String get icon_density_mm => 'lijn/mm';

  @override
  String get icon_density_inch => 'lijn/inch';

  @override
  String get icon_machineregist => 'Registratie van machine';

  @override
  String get icon_snj_myconnection => 'Artspira / My Connection';

  @override
  String get icon_snj_rgs_title => 'Ontvang een pincode om uw machine te registreren.';

  @override
  String get icon_snj_rgs_s1_iivo1 => 'Meld u aan bij Artspira.\nhttps://s.brother/snjumq4211';

  @override
  String get icon_snj_rgs_s2 => 'Tik op [Machine-instellingen] en tik op [Registeren] bij de borduurmachine en selecteer vervolgens [Model met WLAN Draadloze verbinding].';

  @override
  String get icon_snj_rgs_s3 => 'Typ het nummer hieronder op de Artspira App en ontvang de pincode.';

  @override
  String get icon_snj_rgs_pin => 'Typ de pincode op het volgende scherm.';

  @override
  String get icon_cws_rgs_s3 => 'Tik op [Registratie van machine(s)] en selecteer [Een nieuwe naaimachine registreren].';

  @override
  String get icon_cws_rgs_s4 => 'Typ het nummer hieronder op het webscherm en ontvang de pincode.';

  @override
  String get icon_cws_rgs_pin => 'Typ de pincode op het volgende scherm.';

  @override
  String get icon_transfer => 'Overdracht';

  @override
  String get icon_app_selectcolor => 'Kleuren selecteren voor applicatielapje';

  @override
  String get icon_texture => 'Textuur';

  @override
  String get icon_userthread => 'Garen gebruiker';

  @override
  String get icon_senju => 'Artspira';

  @override
  String get icon_notnow => 'Niet nu';

  @override
  String get icon_builtin => 'Ingebouwd';

  @override
  String get icon_user => 'Eigen';

  @override
  String get icon_clearall => 'Alles\nwissen';

  @override
  String get icon_taperingtitle => 'Taps toelopend';

  @override
  String get icon_tapering01 => 'Start';

  @override
  String get icon_tapering02 => 'Einde';

  @override
  String get icon_tapering03 => 'Eindstijl';

  @override
  String get icon_tapering03_2 => 'Eindstijl';

  @override
  String get icon_tapering04 => 'Starthoek';

  @override
  String get icon_tapering05 => 'Eindhoek';

  @override
  String get icon_tapering06 => 'Patroonherhaling';

  @override
  String get icon_tapering06_s => 'Herhaling';

  @override
  String get icon_times => 'keer';

  @override
  String get icon_approx_s => 'Ca.';

  @override
  String get icon_e2etitle => 'Edge-To-Edge Quilt';

  @override
  String get icon_e2e01 => 'Optie spiegelen';

  @override
  String get icon_e2e01_2 => 'Optie spiegelen';

  @override
  String get icon_e2e02 => 'rij(en)';

  @override
  String get icon_e2e03 => 'stuk(s)';

  @override
  String get icon_sr_title => 'Steekregulator';

  @override
  String get icon_sr_mode_title => 'Modus';

  @override
  String get icon_sr_mode_00exp => 'Stap 1 - Selecteer een modus.\nStap 2 - Selecteer een steek.\n  *In modus 3 is de rijgsteek automatisch geselecteerd.\nStap 3 - Begin te naaien.';

  @override
  String get icon_sr_mode01exp => 'Intervalmodus\n\nWanneer u de stof niet beweegt, stopt de naald erboven en komt pas omlaag nadat u de stof over de opgegeven lengte hebt verplaatst. Pas op dat uw hand niet onder de naald komt.';

  @override
  String get icon_sr_mode02exp => 'Continumodus\n\nWanneer u de stof niet beweegt, komt de naald langzaam omlaag in dezelfde stiksteekpositie of met een kortere steeklengte dan opgegeven, bijvoorbeeld bij de hoeken van een patroon.';

  @override
  String get icon_sr_mode03exp => 'Rijgmodus\n\nDe naald komt bij rijgen met langere tussenruimten omlaag. Pas op dat uw hand niet onder de naald komt.';

  @override
  String get icon_sr_mode04exp => 'Vrijmodus\n\nNaaien met de opgegeven snelheid';

  @override
  String get icon_sr_mem_mode01 => 'Interval';

  @override
  String get icon_sr_mem_mode02 => 'Continu';

  @override
  String get icon_sr_modemem_03 => 'Rijgsteken';

  @override
  String get icon_sr_mem_mode04 => 'Vrije hand ';

  @override
  String get icon_sr_sensingline => 'Detectielijn';

  @override
  String get icon_sr_footheight => 'SR-hoogte';

  @override
  String get icon_unselect => 'Deselecteren';

  @override
  String get icon_filter => 'Filter';

  @override
  String get icon_filterapplied => 'Filter is toegepast.';

  @override
  String get icon_apply => 'Toepassen';

  @override
  String get icon_upperlimit => 'Bovengrens';

  @override
  String get icon_lowerlimit => 'Ondergrens';

  @override
  String get icon_all => 'Alle';

  @override
  String get icon_bh_guide01 => 'Geleider voor knoopsgaten';

  @override
  String get icon_bh_guide02 => 'Reeks';

  @override
  String get icon_bh_guide03 => 'Afstand';

  @override
  String get icon_bh_guide04 => 'Geleider voor stofrand';

  @override
  String get icon_bh_guide05 => 'Afstand vanaf de rand';

  @override
  String get icon_colorchanges => 'Kleurwisselingen';

  @override
  String get icon_voiceguidance_title => 'Spraakhulp';

  @override
  String get icon_voicevolume => 'Spraakvolume';

  @override
  String get icon_voice_01eng_a => 'English-A';

  @override
  String get icon_voice_01eng_b => 'English-B';

  @override
  String get icon_voice_02deu_a => 'Deutsch-A';

  @override
  String get icon_voice_02deu_b => 'Deutsch-B';

  @override
  String get icon_voice_03fra_a => 'Français-A';

  @override
  String get icon_voice_03fra_b => 'Français-B';

  @override
  String get icon_voice_04ita_a => 'Italiano-A';

  @override
  String get icon_voice_04ita_b => 'Italiano-B';

  @override
  String get icon_voice_05nld_a => 'Nederlands-A';

  @override
  String get icon_voice_05nld_b => 'Nederlands-B';

  @override
  String get icon_voice_06esp_a => 'Español-A';

  @override
  String get icon_voice_06esp_b => 'Español-B';

  @override
  String get icon_voice_07jpn_a => '日本語-A';

  @override
  String get icon_voice_07jpn_b => '日本語-B';

  @override
  String get icon_embcate_photostitch => 'Picture Play\nborduren';

  @override
  String get icon_photos_title => 'Picture Play borduurfunctie';

  @override
  String get icon_photos_01 => 'Selecteer een beeldbestand (JPG, BMP, PNG).';

  @override
  String get icon_photos_02 => 'Aanpassing grootte';

  @override
  String get icon_photos_03 => 'Achtergrond verwijderen';

  @override
  String get icon_photos_04 => 'Afbeelding in een kader plaatsen';

  @override
  String get icon_photos_05 => 'Aanpassen aan kader';

  @override
  String get icon_photos_06 => 'Auto (AI)';

  @override
  String get icon_photos_07 => 'Handmatig';

  @override
  String get icon_photos_08 => 'Selecteer een door AI te converteren stijl.';

  @override
  String get icon_photos_09 => 'Aanpassing kleur';

  @override
  String get icon_photos_10 => 'Geaccentueerde randen';

  @override
  String get icon_photos_11 => 'Helderheid';

  @override
  String get icon_photos_12 => 'Contrast';

  @override
  String get icon_photos_13 => 'Verzadiging';

  @override
  String get icon_photos_14 => 'Een beeldbestand (JPG, BMP, PNG) importeren';

  @override
  String get icon_photos_15 => 'Borduurinstellingen';

  @override
  String get icon_style0 => 'Origineel';

  @override
  String get icon_style1 => 'Stijl 1';

  @override
  String get icon_style2 => 'Stijl 2';

  @override
  String get icon_style3 => 'Stijl 3';

  @override
  String get icon_style4 => 'Stijl 4';

  @override
  String get icon_style5 => 'Stijl 5';

  @override
  String get icon_style6 => 'Stijl 6';

  @override
  String get icon_style7 => 'Stijl 7';

  @override
  String get icon_style8 => 'Stijl 8';

  @override
  String get icon_style9 => 'Stijl 9';

  @override
  String get icon_style10 => 'Stijl 10';

  @override
  String get icon_style1_name => 'Icoonkunst';

  @override
  String get icon_style2_name => 'Art deco';

  @override
  String get icon_style3_name => 'Potloodschets';

  @override
  String get icon_style4_name => 'Oliepastels';

  @override
  String get icon_style5_name => 'Neonkleuren';

  @override
  String get icon_style6_name => 'Art nouveau';

  @override
  String get icon_style7_name => 'Gedurfd & levendig';

  @override
  String get icon_style8_name => 'Glas in lood';

  @override
  String get icon_style9_name => 'Geokunst';

  @override
  String get icon_style10_name => 'Beeldverhaal';

  @override
  String get icon_stylusedit => 'Projectorbewerking met stylus';

  @override
  String get icon_projectorsettings => 'Projectorinstellingen';

  @override
  String get icon_setting_srvolume => 'Volume geluidssignaal van SR';

  @override
  String get icon_embcate_bt_01 => 'Quilt  ';

  @override
  String get icon_embcate_bt_02 => 'Applique';

  @override
  String get icon_embcate_bt_03 => 'Bloemen/Planten';

  @override
  String get icon_embcate_bt_04 => 'Dieren';

  @override
  String get icon_embcate_bt_05 => 'Letters';

  @override
  String get icon_embcate_bt_06 => 'Decoratie';

  @override
  String get icon_embcate_bt_07 => 'Seizoenen';

  @override
  String get icon_embcate_bt_08 => '3D kant';

  @override
  String get icon_embcate_bt_09 => 'Gehaakt kant';

  @override
  String get icon_embcate_bt_10 => 'In The Hoop';

  @override
  String get icon_embcate_b_01 => 'Quilt 2';

  @override
  String get icon_embcate_b_02 => 'Quilt ontwerpen van Anna Aldmon';

  @override
  String get icon_embcate_b_03 => 'Applique 2';

  @override
  String get icon_embcate_b_04 => 'Bloemen/Planten 2';

  @override
  String get icon_embcate_b_05 => 'Rozen van Pierre-Joseph Redouté';

  @override
  String get icon_embcate_b_06 => 'Zündt ontwerpen';

  @override
  String get icon_embcate_b_07 => 'Zentangle';

  @override
  String get icon_embcate_b_08 => 'Dieren 2';

  @override
  String get icon_embcate_b_09 => 'Letters 2';

  @override
  String get icon_embcate_b_10 => 'Sport';

  @override
  String get icon_embcate_b_11 => 'Nautisch';

  @override
  String get icon_embcate_b_12 => 'Voedsel';

  @override
  String get icon_embcate_b_13 => 'Kinderen';

  @override
  String get icon_embcate_b_14 => 'Decoratie 2';

  @override
  String get icon_embcate_b_15 => '3D kant 2';

  @override
  String get icon_legalinfo => 'Legale informatie';

  @override
  String get icon_legal_opensource => 'Open Source Licensing Remarks\n(Opmerkingen over open source-licenties)';

  @override
  String get icon_legal_thirdpartysoft => 'Third-Party Software\n(Software van derden)';

  @override
  String get icon_nousb => '－－－－－－';

  @override
  String get icon_randomfill => 'Willekeurig vullen';

  @override
  String get icon_selarea => 'Gebied selecteren';

  @override
  String get icon_maxnumber_patt => 'Minimale afstand';

  @override
  String get t_err01 => 'De veiligheidsvoorziening is geactiveerd.\nIs de draad verstrikt geraakt?\nIs de naald verbogen?';

  @override
  String get t_err02 => 'Controleer de bovendraad en rijg deze opnieuw in.';

  @override
  String get t_err02_emb => 'Controleer de bovendraad en rijg deze opnieuw in.\n\n* Druk op de toets Borduurraam verplaatsen op het borduurscherm om het borduurraam naar het midden te verplaatsen.';

  @override
  String get t_err03 => 'Zet de persvoethendel omhoog.';

  @override
  String get t_err04 => 'Zet de persvoethendel omlaag.';

  @override
  String get t_err05 => 'Er zit geen borduurkaart in de kaartopening.\nVoer een borduurkaart in.';

  @override
  String get t_err06 => 'U kunt deze borduurkaart niet gebruiken. Onbruikbare kaarten zijn kaarten die in het buitenland mogen worden verkocht, kaarten zonder borduurpatroon enz.';

  @override
  String get t_err07 => 'U kunt niet meer patronen toevoegen aan deze combinatie.';

  @override
  String get t_err07_u => 'Er kunnen geen steken meer worden gecombineerd.';

  @override
  String get t_err08 => 'Deze toets werkt niet wanneer de Borduureenheid niet bevestigd is. Schakel de naaimachine uit en bevestig de Borduureenheid.';

  @override
  String get t_err09 => 'Deze toets werkt niet wanneer de Borduureenheid is bevestigd.';

  @override
  String get t_err10 => 'Deze toets werkt niet wanneer de Borduureenheid is bevestigd. Schakel de naaimachine uit en verwijder de Borduureenheid.';

  @override
  String get t_err11 => 'U kunt het voetpedaal niet gebruiken wanneer de Borduureenheid bevestigd is.\nVerwijder het voetpedaal.';

  @override
  String get t_err_corrupteddataturnoff => 'De gegevens worden niet herkend. De gegevens zijn misschien corrupt.\n\nZet de machine uit en weer aan.';

  @override
  String get t_err12 => 'Gegevens volume is te groot voor dit patroon.';

  @override
  String get t_err13 => 'Deze toets werkt niet wanneer de naald naar beneden staat. Zet de naald omhoog en druk nogmaals op de toets.';

  @override
  String get t_err15 => 'De \"Start/Stop\"-toets werkt niet wanneer het voetpedaal is aangesloten.\nVerwijder het voetpedaal.';

  @override
  String get t_err16 => 'Voltooi de bewerking van het patroon voordat u het patroon naait.';

  @override
  String get t_err16_e => 'Voltooi de bewerking van de patronen voordat u begint met borduren.';

  @override
  String get t_err16_u => 'Voltooi de bewerking van de steekgegevens voordat u begint met naaien.';

  @override
  String get t_err17 => 'Zet de knoopsgathendel omhoog.';

  @override
  String get t_err18 => 'Zet de knoopsgathendel omlaag.';

  @override
  String get t_err19 => 'Kan de configuratie van de letters niet wijzigen.';

  @override
  String get t_err22 => 'Kies een patroon.';

  @override
  String get t_err22_u => 'Selecteer een steek.';

  @override
  String get t_err23 => 'Opslaan…';

  @override
  String get t_err24 => 'De onderdraad is bijna op.';

  @override
  String get t_err25 => 'De borduurarm van de Borduureenheid zal bewegen.\nHoud uw handen enz. uit de buurt van de borduurarm.';

  @override
  String get t_err26 => 'Wissen…';

  @override
  String get t_err27 => 'Onvoldoende geheugen beschikbaar om het patroon op te slaan.\nEen ander patroon wissen?';

  @override
  String get t_err27_d => 'Onvoldoende geheugen beschikbaar om het patroon op te slaan.\nAndere gegevens wissen?';

  @override
  String get t_err61 => 'Onvoldoende geheugen beschikbaar om het patroon op te slaan.';

  @override
  String get t_err61_d => 'Onvoldoende geheugen beschikbaar om het patroon op te slaan.\nVerwijder enkele patronen of gebruik een ander medium.';

  @override
  String get t_err61_dd => 'Onvoldoende geheugen beschikbaar om het patroon op te slaan.\nVerwijder een aantal gegevens of gebruik een ander medium.';

  @override
  String get t_err28 => 'Oproepen van een patroon.\nEven geduld a.u.b.';

  @override
  String get t_err28_d => 'Oproepen van een patroon.\nEven geduld a.u.b.';

  @override
  String get t_err29 => 'OK om het gekozen patroon te wissen?';

  @override
  String get t_err65 => 'OK om het gekozen patroon te wissen?';

  @override
  String get t_err29_d => 'OK om de geselecteerde gegevens te wissen?';

  @override
  String get t_err30 => 'OK om huidige instellingen op te slaan?';

  @override
  String get t_err33 => 'Verwijder het borduurraam.';

  @override
  String get t_err34 => 'Bevestig het borduurraam.';

  @override
  String get t_err36 => 'Wanneer de schuifknop voor de snelheidsregeling is ingesteld op de zigzagsteekbreedte, werkt de \"Start/Stop\"-toets niet.';

  @override
  String get t_err37 => 'De veiligheidsvoorziening voor het spoelwinden is in werking getreden.\nIs de draad verstrikt geraakt?';

  @override
  String get t_err38 => 'Deze functie kan niet worden gebruikt wanneer de naaimachine in de stand voor tweelingnaald staat.\nSchakel de stand voor tweelingnaald uit en kies de functie opnieuw.';

  @override
  String get t_err_twinn_10 => 'U kunt de steekplaat voor rechte steken niet gebruiken in de tweelingnaaldstand.\nVerwijder de tweelingnaald en annuleer de tweelingnaaldstand.';

  @override
  String get t_err_twinn_11 => 'De tweelingnaaldstand is geannuleerd.\nVerwijder de tweelingnaald.';

  @override
  String get t_err_twinn_12 => 'Controleer of de tweelingnaald is verwijderd.';

  @override
  String get t_err42 => 'Controleer het resultaat en druk vervolgens op OK.';

  @override
  String get t_err48 => 'Kan de gegevens voor het geselecteerde patroon niet herkennen. De gegevens zijn mogelijk beschadigd.';

  @override
  String get t_err50 => 'Zet de spoelhouder in de linkerstand.';

  @override
  String get t_err53 => 'De naald staat omlaag.\nDruk op de naaldstandtoets om de naald omhoog te zetten.';

  @override
  String get t_err55 => 'Bevestig knoopsgatvoet \"A＋\".\nDe ingebouwde camera detecteert knoopsgatvoet \"A＋\" aan de hand van de markering \"A＋\" en de drie stippen.';

  @override
  String get t_err56 => 'Kan de afmeting van de knoop niet herkennen.\nControleer of de drie stippen goed zichtbaar zijn of voer de waarden voor de gleuflengte in en probeer het opnieuw.';

  @override
  String get t_err63 => 'U kunt deze bewerkingsfunctie niet gebruiken wanneer het patroon buiten de rode omtreklijn valt. Gebruik deze functie nadat u het patroon hebt verplaatst.';

  @override
  String get t_err64 => 'Er zit een speciaal patroon bij dat niet kan worden opgeslagen in het externe geheugen.\nSla het patroon op in het geheugen van de machine.';

  @override
  String get t_err69 => 'Er is een patroon opgenomen dat u niet kunt opslaan.';

  @override
  String get t_err76 => 'Gebruik een groter borduurraam.';

  @override
  String get t_err77 => 'U kunt dit patroon niet gebruiken met dit borduurraam.\nGebruik een geschikt borduurraam.';

  @override
  String get t_err82 => 'OK om terug te gaan naar vorige kleurwijzigingen?';

  @override
  String get t_err83 => 'OK om de gegevens te overschrijven?';

  @override
  String get t_err84 => 'OK om het vorige geheugen op te roepen en opnieuw te gebruiken?';

  @override
  String get t_err88 => 'Kan niet borduren, omdat de Borduureenheid niet is bevestigd. Zet de machine uit en bevestig de Borduureenheid.';

  @override
  String get t_err89 => 'USB-medium is niet geladen.\nLaad het USB-medium.';

  @override
  String get t_err90 => 'Dit USB-medium kan niet worden gebruikt.';

  @override
  String get t_err_usb_notcompati => 'Het aangesloten USB-medium is niet compatibel met het apparaat.\nGebruik een ander USB-medium.';

  @override
  String get t_err93 => 'Dit USB-medium is mogelijk beschadigd.\nGebruik een ander USB-medium en probeer opnieuw op te slaan.';

  @override
  String get t_err94 => 'Onvoldoende ruimte.\nWis enkele patronen of gebruik een ander USB-medium.';

  @override
  String get t_err94_cmn => 'Onvoldoende ruimte.\nVerwijder enkele patronen of gebruik een ander medium.';

  @override
  String get t_err95 => 'Het USB-medium is veranderd. Verander het USB-medium niet terwijl het wordt gelezen.';

  @override
  String get t_err95_cmn => 'Het medium is verwisseld.\nVerwissel een medium niet terwijl het wordt gelezen.';

  @override
  String get t_err96 => 'Het USB-medium is beschermd tegen overschrijven; de gegevens kunnen niet worden opgeslagen. Annuleer de bescherming tegen overschrijven voordat u de gegevens probeert op te slaan.';

  @override
  String get t_err96_cmn => 'Het medium is beschermd tegen overschrijven. U kunt de gegevens dus niet opslaan.\nAnnuleer de bescherming tegen overschrijven voordat u de gegevens probeert op te slaan.';

  @override
  String get t_err97 => 'Het USB-medium is beschermd tegen overschrijven; gegevens kunnen niet worden gewist.\nAnnuleer de bescherming tegen overschrijven voordat u de gegevens probeert te wissen.';

  @override
  String get t_err97_cmn => 'Het medium is beschermd tegen overschrijven. U kunt de gegevens dus niet verwijderen.\nAnnuleer de bescherming tegen overschrijven voordat u probeert de gegevens te verwijderen.';

  @override
  String get t_err98 => 'Fout op USB-medium';

  @override
  String get t_err98_cmn => 'Mediumfout';

  @override
  String get t_err99 => 'Dit USB-medium kan niet worden gelezen.\nDit USB-medium is mogelijk beschadigd.';

  @override
  String get t_err100 => 'Het USB-medium formatteren';

  @override
  String get t_err101 => 'Verzenden via USB';

  @override
  String get t_err101_cmn => 'Toegang verkrijgen tot medium';

  @override
  String get t_err103 => 'Even geduld a.u.b.';

  @override
  String get t_err104 => 'Werkt niet wanneer patronen buiten het blauwe raam vallen.';

  @override
  String get t_err106 => 'Volgende segment borduren?';

  @override
  String get t_err107 => 'Borduren beëindigd.';

  @override
  String get t_err108 => 'De opslag is vol.';

  @override
  String get t_err109 => 'Zet de persvoet omhoog met de persvoethendeltoets.';

  @override
  String get t_err110 => 'Zet de persvoet omlaag met de persvoethendeltoets.';

  @override
  String get t_err111 => 'Niet goed ingeregen. Druk opnieuw op de knop voor automatisch inrijgen.';

  @override
  String get t_err113 => 'Dit programma krijgt een vernieuwde versie (upgrade).\nLaad het programma in de machine via USB.';

  @override
  String get t_err116 => 'Gegevensfout';

  @override
  String get t_err117 => 'Flash-ROM-fout';

  @override
  String get t_err118 => 'Er is een storing opgetreden.\nZet de machine uit en weer aan.';

  @override
  String get t_err119 => 'Zet de machine uit voordat u de steekplaat installeert of verwijdert.';

  @override
  String get t_err120 => 'OK om de borduurarm te verplaatsen naar de vorige stand?';

  @override
  String get t_err120_2 => 'Verwijder het borduurraam en vervang de spoel.\nBevestig daarna het boruurraam en tik op OK om het te verplaatsen naar de vorige positie.';

  @override
  String get t_err121 => 'OK om het gecombineerde randpatroon te splitsen?';

  @override
  String get t_err122 => 'Dit USB-medium is incompatibel.';

  @override
  String get t_err122_cmn => 'Dit medium is incompatibel.';

  @override
  String get t_err123 => 'Het USB-medium is verwijderd of de stekker is uit het contact gehaald.';

  @override
  String get t_err124 => 'Er is een storing opgetreden.\nZet de machine uit en weer aan.';

  @override
  String get t_err125 => 'De bovendraad is mogelijk niet goed ingeregen.\nRijg de bovendraad in vanaf het begin.';

  @override
  String get t_err126 => 'OK om de persvoet automatisch omlaag te zetten?';

  @override
  String get t_err127 => 'In de tweelingnaaldstand kunt u de automatische naaldinrijgtoets niet gebruiken.';

  @override
  String get t_err128 => 'Plaats het borduurraam zo ver mogelijk naar achteren. ZET DE BORDUURRAAM VERGRENDELING OMLAAG OM HET BORDUURRAAM VAST TE ZETTEN.';

  @override
  String get t_err129 => 'Zet de spoelhouder in de rechterstand.';

  @override
  String get t_err130 => 'De persvoet beweegt op en neer. Houd uw handen enz. buiten bereik van de persvoet.';

  @override
  String get t_err131 => 'U kunt dit patroon niet gebruiken.';

  @override
  String get t_err132 => 'Controleer voordat u begint te borduren of het borduurraam geheel naar achteren is geplaatst en de raambevestigingshendel omlaag staat. Druk dan op de \"Start/Stop\"-toets.';

  @override
  String get t_err133 => 'Dit borduurraam kan niet worden gebruikt.';

  @override
  String get t_err134 => 'U kunt dit patroon niet gebruiken omdat de gegevenscapaciteit is overschreden.';

  @override
  String get t_err136 => 'Preventief onderhoud is aanbevolen.';

  @override
  String get t_err137 => 'Preventief onderhoud is aanbevolen.\n1000 uur overschreden.';

  @override
  String get t_err208 => 'Is aan het berekenen…';

  @override
  String get t_err209 => 'De borduurarm van de borduureenheid zal bewegen.';

  @override
  String get t_err210 => 'Is aan het herkennen…';

  @override
  String get t_err213 => 'Kan de borduurpositiemarkering niet herkennen.';

  @override
  String get t_err215 => 'Verwijder de borduurpositiemarkering.';

  @override
  String get t_err224 => 'Er zit stof of een vlek op het witte papier/stof. Vervang door schoon wit papier of schone witte stof.';

  @override
  String get t_err227 => 'Correct instellen of herkennen is niet mogelijk.';

  @override
  String get t_err228 => 'Dit bestand overschrijdt de gegevenslimiet en kan niet worden gebruikt.\nGebruik een bestand van geschikte grootte.';

  @override
  String get t_err229 => 'Dit bestand is onbruikbaar.';

  @override
  String get t_err229_b => 'Deze bestandsversie kan niet worden gelezen.';

  @override
  String get t_err239 => 'Aangesloten op pc.\nMaak de USB-kabel niet los.';

  @override
  String get t_err241 => 'Kan bestand niet lezen.';

  @override
  String get t_err242 => 'Bestand niet opgeslagen.';

  @override
  String get t_err244 => 'OK om geselecteerde afbeelding te verwijderen?';

  @override
  String get t_err245 => 'Deze toets kan momenteel niet worden gebruikt.';

  @override
  String get t_err246 => 'Het borduurpatroon overschrijdt de grens van het patroongebied.\nVerplaats het patroon en scan het nieuwe gebied.';

  @override
  String get t_err247 => 'Onvoldoende opslagruimte.';

  @override
  String get t_err248 => 'Instelling wissen?';

  @override
  String get t_err249 => 'Kan de rand van de stof niet herkennen.';

  @override
  String get t_err250 => 'Dit patroon kan niet worden omgezet.';

  @override
  String get t_err251 => 'OK om de borduurgrootte en positie opnieuw in te stellen?';

  @override
  String get t_err252 => 'OK om de borduurgrootte opnieuw in te stellen?';

  @override
  String get t_err253 => 'Stofdikte detecteren.\nPositiesticker bevestigen binnen rode lijn.';

  @override
  String get t_err254 => 'Detectie geslaagd.\nVerwijder de borduurpositiemarkering. Druk op OK om  de achtergrondscan te laden.';

  @override
  String get t_err255 => 'Druk op OK. Dan verplaatst het borduurraam zich en start met het laden van de achtergrondscan.';

  @override
  String get t_err256 => 'Detectie niet geslaagd.\nOpnieuw proberen?';

  @override
  String get t_err257 => 'Certificatie geslaagd.\nStart machine opnieuw op.';

  @override
  String get t_err257_1 => 'Certificatie geslaagd.\nStart naaimachine opnieuw op.';

  @override
  String get t_err257_2 => 'Certificatie geslaagd.\nStart machine opnieuw op.';

  @override
  String get t_err259 => 'Certificatie upgrade-kit.\n\nDruk op kitnummer om te certificeren.';

  @override
  String get t_err260 => 'Voer de certificatiecode in en druk vervolgens op [INSTELLEN].';

  @override
  String get t_err261 => 'Bezig met certificeren…';

  @override
  String get t_err262 => 'De certificatiecode is onjuist.\nControleer de code en typ deze opnieuw in.';

  @override
  String get t_err263 => 'KIT';

  @override
  String get t_err264 => 'Is het OK om de achtergrondafbeelding te verwijderen?';

  @override
  String get t_err265 => 'Is het OK de steekinstellingen terug te zetten op oorspronkelijke grootte, hoek en positie?';

  @override
  String get t_err343 => 'OK om de oorspronkelijke hoek en/of positie te herstellen?';

  @override
  String get t_err266 => 'Certificatie-upgradekit.';

  @override
  String get t_err270 => 'Bevestig de eerste borduurpositiemarkering goed op het materiaal en zorg ervoor dat de markering zich binnen het rode kader bevindt. De borduurarm van de borduureenheid verplaatst zich nadat u op de scantoets hebt gedrukt.';

  @override
  String get t_err271 => 'Bevestig de tweede borduurpositiemarkering goed op het materiaal en zorg ervoor dat de markering zich binnen het rode kader bevindt. De borduurarm van de borduureenheid verplaatst zich nadat u op de scantoets hebt gedrukt.';

  @override
  String get t_err273 => 'De borduurpositiemarkering is niet goed bevestigd.\nVerwijder de borduurpositiemarkering en bevestig deze opnieuw.';

  @override
  String get t_err274 => 'De positiemarkeringen zijn herkend.\nLaat de borduurpositiemarkeringen bevestigd en plaats stof weer in borduurraam. Zorg dat middelpunten van positiemarkeringen zich in borduurgebied bevinden en selecteer het patroon.';

  @override
  String get t_err276 => 'De borduurpositiemarkeringen zijn herkend.\nVerwijder de borduurpositiemarkeringen.';

  @override
  String get t_err277 => 'Weet u zeker dat u de verbinding van de borduurpatronen wilt “Annuleren”?';

  @override
  String get t_err278 => 'U kunt het volgende gedeelte van het patroon niet borduren nadat u hebt afgesloten. Weet u zeker dat u de verbinding van de borduurpatronen wilt beëindigen?';

  @override
  String get t_err279 => 'Borduren is voltooid.\nIs het OK om te verbinden met het volgende patroondeel?\n\n* Haal de stof niet uit het borduurraam.\n* Als u later wilt doorgaan, moet u het volgende gedeelte van het patroon selecteren en bevestigen. Het kan worden voortgezet als machine de gegevens herkend heeft.';

  @override
  String get t_err282 => 'U kunt niets meer invoeren.';

  @override
  String get t_err283 => 'Deze applicatie wordt afgesloten.';

  @override
  String get t_err284 => 'Deze gegevens zijn te gecompliceerd en kunnen niet worden geconverteerd.';

  @override
  String get t_err286 => 'OK om bewerken van lijn te voltooien?';

  @override
  String get t_err288 => 'De borduurpositiemarkeringen zijn herkend. Verwijder de borduurpositiemarkeringen en bevestig deze op een nieuwe positie.';

  @override
  String get t_err290 => 'Kan de borduurpositiemarkeringen niet herkennen.\nVerwijder de borduurpositiemarkeringen en bevestig deze opnieuw.\nDe middelpunten van de borduurpositiemarkeringen moeten zich in het borduurgebied bevinden.';

  @override
  String get t_err291 => 'Onvoldoende ruimte. Gebruik een ander USB-medium.';

  @override
  String get t_err291_cmn => 'Onvoldoende ruimte.\nGebruik een ander medium.';

  @override
  String get t_err292 => 'Voor de geselecteerde modus zijn er onvoldoende kleuren op de garenkleurkaart.';

  @override
  String get t_err292_s => 'Het huidige palet bevat niet voldoende kleuren voor de geselecteerde modus.';

  @override
  String get t_err297 => 'Herhaal het proces vanaf stap 3 t/m 4.';

  @override
  String get t_err298 => 'De sticker is vuil.';

  @override
  String get t_err_cameracalib_ng_msg => 'Kan niet gevonden worden.\nBevestig een nieuwe, witte sticker.';

  @override
  String get t_err_cameracalib_ok_msg => 'Druk op de OK-toets om de plaats te onthouden waar de naald neerkomt.';

  @override
  String get t_err299 => 'Neem contact op met uw dealer als na meerdere keren proberen de configuratie niet lukt.';

  @override
  String get t_err300 => 'Raadpleeg zo nodig de bedieningshandleiding voor de aanbevolen naald.';

  @override
  String get t_err_cameracalib_title => 'Naald kalibreren voor camera/projector';

  @override
  String get t_err_cameracalib_1_4 => '1. Druk op de naaldstandtoets om de naald omhoog te\n zetten.\n2. Nadat u de naald en de persvoet hebt verwijderd,\n bevestigt u een witte sticker op de plaats waar de\n naald neerkomt.\n3. Breng de naald in (standaardmaat 75/11 of 90/14).\n4. Druk op de toets START om het kalibratieproces te\n starten.\nControleer voor de veiligheid of het gebied rond de\n naald vrij is voordat u op de toets \"START\" drukt.\n\n* Houd handen en voorwerpen uit de buurt van de naald.\n Anders kunt u letsel oplopen.';

  @override
  String get t_err303 => 'Borduren is voltooid.\nIs het OK om te verbinden met het volgende patroon?';

  @override
  String get t_err304 => 'Haal de stof niet uit het borduurraam.\nDruk op OK om het volgende patroon te selecteren.';

  @override
  String get t_err307 => 'Verwijder de borduurpositiemarkeringen niet.\nSpan de stof opnieuw in het borduurraam, zodat het volgende patroon en de middelpunten van de twee borduurpositiemarkeringen zich in het borduurgebied bevinden.';

  @override
  String get t_err308 => 'Het volgende patroon bevindt zich buiten het borduurgebied.\nSpan de stof opnieuw in het borduurraam, zodat het volgende patroon en de middelpunten van de twee borduurpositiemarkeringen zich in het borduurgebied bevinden.';

  @override
  String get t_err309 => 'Kan de borduurpositiemarkering niet herkennen.\nSpan de stof opnieuw in het borduurraam, zodat het volgende patroon en de middelpunten van de twee borduurpositiemarkeringen zich in het borduurgebied bevinden.';

  @override
  String get t_err310 => 'De positie van de borduurpositiemarkeringen is veranderd.\nSpan de stof opnieuw in het borduurraam, zodat het volgende patroon en de middelpunten van de twee borduurpositiemarkeringen zich in het borduurgebied bevinden.';

  @override
  String get t_err311 => 'De borduurpositiemarkeringen zijn herkend.\nVerwijder de borduurpositiemarkeringen en borduur het patroon.';

  @override
  String get t_err311_size => 'De borduurpositiemarkeringen zijn herkend.\nVerwijder de borduurpositiemarkeringen en borduur het patroon.\n\n* De grootte van het volgende patroon is automatisch bijgesteld, omdat de afstand tussen de markeringen is gewijzigd tijdens het opnieuw inspannen van de stof in het borduurraam.';

  @override
  String get t_err311_rehoop => 'De afstand tussen de markeringen is niet langer correct vanwege het opnieuw inspannen van de stof in het borduurraam.\nSpan de stof nogmaals in het borduurraam, zodat de afstand tussen de markeringen overeenkomt met de volgende lengte.';

  @override
  String get t_err312 => 'Verwijder de borduurpositiemarkeringen niet.\nU moet de borduurpositiemarkeringen opnieuw bevestigen.\nPlaats de stof opnieuw in het borduurraam.';

  @override
  String get t_err313 => 'Kan de borduurpositiemarkering niet herkennen.\nPlaats de stof opnieuw in het borduurraam.';

  @override
  String get t_err314 => 'De borduurpositiemarkeringen zijn herkend.\nVerwijder de borduurpositiemarkeringen.';

  @override
  String get t_err354 => 'De afsluitondersteunmodus is geactiveerd.\nZet de machine uit.';

  @override
  String get t_err356 => 'Deze steek is niet compatibel met de modus \"Boventransport\".';

  @override
  String get t_err359 => 'Verwijder de module voor boventransport van de machine.';

  @override
  String get t_err360 => 'Stel de klok in.';

  @override
  String get t_err361 => 'Selecteer uw taal.';

  @override
  String get t_err362 => 'Verwijder de borduurvoet met LED-aanwijzer van de machine.';

  @override
  String get t_err364 => 'Modulefout';

  @override
  String get t_err368 => 'OK om de randinstelling, positie en/of hoek van het patroon terug te zetten?';

  @override
  String get t_err373 => 'Het borduurraam is verwisseld, vervang het door het oorspronkelijke raam.';

  @override
  String get t_err380 => 'Verwijder de stof onder de persvoet tijdens het inrijgen van de naald.';

  @override
  String get t_err381 => 'OK om de eindpuntinstelling te annuleren?';

  @override
  String get t_err382 => 'U kunt de instelling van het naaieindpunt in combinatie met de geselecteerde steek niet aanpassen. \nSelecteer een andere steek of wijzig de lengte van de steek.';

  @override
  String get t_err383 => 'Ga door met naaien nadat u de eindpuntsticker hebt verwijderd.';

  @override
  String get t_err384 => 'U kunt de instelling van het steekeinde op dit moment niet aanpassen. \nDe instelling van het eindpunt wordt geannuleerd.';

  @override
  String get t_err385 => 'Instelling eindpunt \nTijdelijke stop';

  @override
  String get t_err386 => 'Deze functie kan niet worden gebruikt wanneer de instelling van het eindpunt is geactiveerd.';

  @override
  String get t_err390 => 'Wilt u alle bewerkingsgegevens wissen en naar de startpagina gaan?';

  @override
  String get t_err390_old => 'OK om alle patronen te verwijderen en terug te gaan naar de startpagina?';

  @override
  String get t_err391 => 'OK om huidige patroonkeuze te annuleren?';

  @override
  String get t_err391_u => 'OK om de huidige steekselectie te annuleren?';

  @override
  String get t_err392 => 'Overzetten met borduurkaart.';

  @override
  String get t_err393 => 'Dit patroon kan niet worden gecombineerd.';

  @override
  String get t_err394 => 'Onvoldoende geheugen beschikbaar om het patroon op te slaan. \nEen opgeslagen patroon verwijderen?';

  @override
  String get t_err395 => 'Dit patroon kan niet worden geladen aangezien het deels buiten het bewerkbare vlak valt.';

  @override
  String get t_err396 => 'Dit patroon kan niet worden geladen aangezien het deels buiten het bewerkbare vlak valt. \nVerplaats met de cursors het punt zodat het binnen het bewerkbare vlak valt.';

  @override
  String get t_err397 => 'OK om de huidige steek te verwijderen?';

  @override
  String get t_err398 => 'Opslaan als nieuw bestand…';

  @override
  String get t_err400 => 'Patroon valt deels buiten het borduurraam.\nAls u meer patronen wilt toevoegen, draait u de patrooncombinatie.';

  @override
  String get t_err401 => 'Patroon valt deels buiten het borduurraam.';

  @override
  String get t_err402 => 'Patroon valt deels buiten het borduurraam.\nVoeg geen extra letters toe.';

  @override
  String get t_err403 => 'Patroon valt deels buiten het borduurraam.\nU kunt deze functie op dit moment niet gebruiken.';

  @override
  String get t_err404 => 'U kunt het lettertype niet wijzigen aangezien sommige letters niet zijn inbegrepen in het geselecteerde lettertype.';

  @override
  String get t_err405 => 'Geselecteerd patroonveld valt deels buiten het borduurraam.';

  @override
  String get t_err406 => 'Patroon valt deels buiten het geselecteerde borduurraam. \nOK om het geselecteerde patroon te annuleren?';

  @override
  String get t_err408 => 'U kunt deze functie niet gebruiken wanneer patronen overlappen.';

  @override
  String get t_err410 => 'Het patroon kan worden geborduurd met het middelpunt of een hoek uitgelijnd met de borduurpositiemarkering. Bevestig de borduurpositiemarkering op de gewenste plaats.';

  @override
  String get t_err411 => 'Na het voltooien van de nodige voorbereidingen drukt u op de [Scan]-toets.';

  @override
  String get t_err412 => 'De borduurpositiemarkering is niet aangetroffen in het detectievlak.';

  @override
  String get t_err413 => 'Gebruik de borduurpositiemarkering om patronen te verbinden.';

  @override
  String get t_err414 => 'Selecteer de positie waar het volgende\npatroon wordt verbonden.';

  @override
  String get t_err415 => 'De gegevens kunnen niet worden gelezen.';

  @override
  String get t_err416 => 'De gegevens zijn opgeslagen.\nBestandsnaam:';

  @override
  String get t_err417 => 'De gegevens worden gelezen.\nEven geduld a.u.b.';

  @override
  String get t_err418 => 'Dit bestandstype kan niet worden gebruikt.';

  @override
  String get t_err419 => 'Dit bestand is te groot. U kunt het niet gebruiken. ';

  @override
  String get t_err420 => 'Overtrekken afbeelding mislukt. ';

  @override
  String get t_err421 => 'Het aantal kleuren in een afbeelding wordt teruggebracht tot het aantal dat u hier opgeeft. Vervolgens wordt de omtreklijn losgemaakt van de afbeelding.';

  @override
  String get t_err422 => 'Plaats het papier met de illustratie of lijntekening in het scanraam.';

  @override
  String get t_err423 => 'U kunt geen borduurraam gebruiken. Gebruik het scanraam.';

  @override
  String get t_err424 => 'Het detecteren kan enkele minuten duren.';

  @override
  String get t_err425 => 'OK om door te gaan naar het scherm \"Mijn Design Center\"?';

  @override
  String get t_err426 => 'OK om door te gaan naar het \"IQ Designer\"-scherm?';

  @override
  String get t_err428 => 'De beeldgegevens die u hebt gemaakt in \"Mijn Design Center\", worden niet opgeslagen. OK om door te gaan?';

  @override
  String get t_err429 => 'De \"IQ Designer\"-gegevens worden niet opgeslagen. OK om door te gaan?';

  @override
  String get t_err430 => 'U kunt het scanraam niet gebruiken om te borduren.\nVervang het scanraam door het borduurraam.';

  @override
  String get t_err432 => 'Bevestig het raam met de te scannen afbeelding aan de machine.';

  @override
  String get t_err433 => 'Wanneer u een afbeelding omzet in lijntekeningen\nof vullingafbeeldingen, haalt u met scanraam de\njuiste garenkleurinformatie op.';

  @override
  String get t_err440 => 'Selecteer het beeldbestand.';

  @override
  String get t_err445 => 'U kunt dit afbeeldingsbestand niet gebruiken.';

  @override
  String get t_err446 => 'Scannen…';

  @override
  String get t_err447 => 'Is aan het herkennen…';

  @override
  String get t_err448 => 'Bezig met verwerken…';

  @override
  String get t_err451 => 'OK om alle bewerkte beeldgegevens te verwijderen?';

  @override
  String get t_err452 => 'JPG, PNG en BMP bestanden, kleiner dan 5MB, 12 milhjoen pixels kunnen worden gebruikt.';

  @override
  String get t_err453 => 'OK om terug te gaan naar standaard instellingen op deze pagina?';

  @override
  String get t_err454 => 'De patrooncombinatie is te groot voor het geïdentificeerde borduurraam. Als u meer patronen wilt toevoegen, draait u de patrooncombinatie.';

  @override
  String get t_err455 => 'De patrooncombinatie is te groot voor het geïdentificeerde borduurraam.';

  @override
  String get t_err457 => 'Zet de weergave Identificatie borduurraam UIT.';

  @override
  String get t_err458 => 'Beeldbestand importeren.';

  @override
  String get t_err459 => 'U kunt deze borduureenheid niet gebruiken.';

  @override
  String get t_err460 => 'De patronen die worden weergegeven in het beeldgebied kunt u converteren.';

  @override
  String get t_err461 => 'Het kader wordt verplaatst om te worden gescand met de ingebouwde camera.';

  @override
  String get t_err462_pp => 'U kunt dit beeldbestand niet gebruiken.\nDe afbeeldingsbestanden die u kunt gebruiken, zijn JPG-, PNG- of BMP-bestanden van minder dan 6 MB en met maximaal 16 miljoen pixels.';

  @override
  String get t_err463 => 'Verwijder het borduurraam of scankader.';

  @override
  String get t_err464 => 'Patroon valt ook buiten het borduurgebied en kan niet worden geconverteerd.';

  @override
  String get t_err465 => 'Geconverteerd naar borduurpatroon en Mijn Design Center wordt afgesloten.\nOK om door te gaan naar borduurcombinatiescherm?';

  @override
  String get t_err466 => 'OK om Mijn Design Center af te sluiten?';

  @override
  String get t_err467 => 'U kunt deze functie niet gebruiken met de patroonverbindmodus.';

  @override
  String get t_err468 => 'Stroom machineprintplaat wordt uitgeschakeld';

  @override
  String get t_err469 => 'Geconverteerd naar borduurpatroon en IQ Designer wordt afgesloten. OK om door te gaan naar borduurcombinatiescherm?';

  @override
  String get t_err470 => 'OK om IQ Designer af te sluiten?';

  @override
  String get t_err471 => 'OK om de geselecteerde gegevens te wissen?';

  @override
  String get t_err472 => 'Selecteer meerdere patronen.';

  @override
  String get t_err473 => 'Ingedeeld(e) patroon/patronen opgeslagen als afbeelding(en).';

  @override
  String get t_err474 => 'Omtrek van ingedeeld(e) patroon/patronen opgeslagen.';

  @override
  String get t_err475 => 'Geef afstand parallelle lijn rond patroon op.';

  @override
  String get t_err478 => 'Ophalen uit lijst stempelpatronen Mijn Design Center.';

  @override
  String get t_err478_tc => 'Ophalen uit lijst stempelpatronen \"IQ Designer\".';

  @override
  String get t_err479 => 'Een patroon voor werken met de spoel kan niet worden gecombineerd met een patroon uit een andere categorie.';

  @override
  String get t_err480 => 'Gaat naar de eerste naaldstand.';

  @override
  String get t_err481 => 'Naaien van patroon voor werken met de spoel voltooid.';

  @override
  String get t_err482 => 'Naaien van alle patronen voor werken met de spoel voltooid.';

  @override
  String get t_err483 => 'Knip de draden af.';

  @override
  String get t_err484_old => 'Alvorens onderstaande patronen te borduren controleert u hoeveel en welke onderdraad is geïnstalleerd.';

  @override
  String get t_err484 => 'Vervang de onderdraad en bevestig vervolgens het borduurraam.\nDe borduurarm verplaatst zich wanneer u op OK drukt.';

  @override
  String get t_err485 => 'Onderstaand patroon om te werken met de spoel wordt geborduurd.';

  @override
  String get t_err486 => 'Draai het handwiel om de naald in de stof te laten zakken en haal de onderdraad omhoog.';

  @override
  String get t_err489 => 'Er worden geen gegevens geconverteerd.';

  @override
  String get t_err496 => 'OK om de instelling toe te passen op alle gebieden?';

  @override
  String get t_err497 => 'Omtrek';

  @override
  String get t_err501 => 'Instellingen kunnen niet worden toegepast. Onvoldoende geheugen om de eigenschappen op te slaan.';

  @override
  String get t_err502 => 'Afstand tussen geselecteerde patronen te groot.';

  @override
  String get t_err503 => 'Vorm te complex voor applicatielijn.';

  @override
  String get t_err503_new => 'Vorm is te complex of ongeschikt voor applicatielijn.\nWijzig de applicatie-instellingen of selecteer een ander patroon.\n* Het resultaat kan afwijken, afhankelijk van de positie en richtingshoek.';

  @override
  String get t_err504 => 'Te groot formaat om applicatielijn toe te voegen.';

  @override
  String get t_err509 => 'Een deel van de textuur kan niet worden weergegeven als gevolg van de gegevensstructuur.';

  @override
  String get t_err505 => 'U kunt deze functie niet gebruiken wanneer kleur sorteren wordt uitgevoerd.';

  @override
  String get t_err506 => 'OK om de draadmarkering te verwijderen?';

  @override
  String get t_err508 => 'De draadmarkeringsfunctie kan niet worden gebruikt wanneer de selectie van het laatste kleurgebied ongedaan wordt gemaakt.\nOk om de draadmarkering te verwijderen?';

  @override
  String get t_err507 => 'Auto';

  @override
  String get t_err510 => 'De actuele afbeelding gebruiken?';

  @override
  String get t_err511 => 'De gegevens zijn opgeslagen in het geheugen van de machine.\nGegevens borduren?';

  @override
  String get t_err515 => 'Schakel de machine uit om de ingebouwde projector te laten afkoelen.';

  @override
  String get t_err516 => 'U kunt deze steek niet gebruiken in combinatie met de actuele steekplaat.';

  @override
  String get t_err517 => 'Verander van steekplaat om deze steek te naaien.';

  @override
  String get t_err518 => 'Het borduurraam is gewijzigd. De borduurarm van de borduureenheid zal zich verplaatsen.\n';

  @override
  String get t_err519 => 'De projector wordt uitgeschakeld.';

  @override
  String get t_err520 => 'Houd uw handen enz. uit de buurt van de borduurarm.';

  @override
  String get t_err_521 => 'U kunt instructievideo’s downloaden.';

  @override
  String get t_err574 => 'Er is een storing opgetreden.\n EEPROM niet toegankelijk.';

  @override
  String get t_err575 => 'De onderdraad is bijna op.\n\n* Gebruik de “Verstevigingssteektoets” om één steek meerdere malen te naaien en vervolgens af te hechten.\n* Gebruik de toets voor het verplaatsen van het borduurraam om de borduurarm te verplaatsen zodat het borduurraam kan worden verwijderd of geplaatst. Daarna gaat de borduurarm terug naar de vorige stand.';

  @override
  String get t_err577 => 'Een couching-patroon kan niet worden gecombineerd met een patroon uit een andere categorie.';

  @override
  String get t_err578 => 'Het is niet mogelijk om nog meer kleurthema\'s te selecteren als favorieten.';

  @override
  String get t_err581 => 'Start met borduren vanaf de rechterbovenhoek van de stof.\nBevestig het borduurraam in de oorspronkelijke borduurpositie';

  @override
  String get t_err581_b => 'Start met borduren vanaf de linkerbovenhoek van de stof.\n Bevestig het borduurraam in de oorspronkelijke borduurpositie';

  @override
  String get t_err582 => 'Eén zijde is klaar. Draai de stof 90° tegen de klok in en span de stof opnieuw in het borduurraam.';

  @override
  String get t_err582_n => 'Eén zijde is geborduurd. Draai de stof linksom en plaats de stof opnieuw in het borduurraam voor de volgende rand.';

  @override
  String get t_err582_e => 'Eén rij is voltooid. Als u met de volgende rij wilt starten, moet u de stof aan de linkerrand van de volgende rij opnieuw inspannen, daarbij is het belangrijk dat de draadmarkering van het bovenste patroon binnen het borduurraam valt.';

  @override
  String get t_err583 => 'Gebruik de verplaatsingstoetsen om de binnenhoek van het patroon aan te passen.';

  @override
  String get t_err583_e => 'Gebruik de toetsen voor verplaatsing van het patroon om de linkerbovenhoek van het patroongebied uit te lijnen met de linkerbovenhoek (markering) van het te borduren gebied.';

  @override
  String get t_err584 => 'Gebruik de toetsen voor het verplaatsen van het patroon om het beginpunt uit te lijnen met het eindpunt van het vorige patroon.';

  @override
  String get t_err584_e => 'Gebruik de toetsen voor verplaatsing van het patroon om de linkerbovenhoek van het patroongebied uit te lijnen met de draadmarkering linksonder van het bovenliggende patroon.';

  @override
  String get t_err585 => 'Gebruik de rotatietoets om de hoek van het patroon aan te passen. Let daarbij goed op de punten rond het patroon.';

  @override
  String get t_err586 => 'Pas het grootte van het patroon aan zodat het punt linksonder de binnenhoek wordt van het volgende patroon.';

  @override
  String get t_err586_b => 'Pas de grootte van het patroon aan zodat het punt rechtsonder de binnenhoek wordt van het volgende patroon.';

  @override
  String get t_err586_e => 'Gebruik de rotatietoetsen en de toetsen om te vergroten/verkleinen om de hoek en de grootte van het patroon aan te passen, zonder de randen van het patroon uit het oog te verliezen.';

  @override
  String get t_err587 => 'Gebruik de rotatietoetsen en de toetsen om te vergroten/verkleinen om het eindpunt uit te lijnen met het beginpunt van het eerste patroon.';

  @override
  String get t_err588 => 'Span de stof opnieuw in het borduurraam.';

  @override
  String get t_err588_e => 'Span de stof opnieuw in naar de rechterkant, daarbij rekening houdend dat de rechterrand van het patroon aan de linkerkant binnen het borduurraam komt.';

  @override
  String get t_err588_e_2 => 'Span de stof opnieuw in naar rechts, daarbij rekenening houdend dat de rechterrand van het patroon aan de rechterkant en de draadmarkering van het bovenliggende patroon binnen het borduurraam komt.';

  @override
  String get t_err590 => 'Druk op LADEN om het updatebestand te installeren.';

  @override
  String get t_err591 => 'Er is een nieuwe update beschikbaar. Om de update te installeren moet u de machine eerst uitschakelen en vervolgens opnieuw inschakelen met de toets “Automatisch draadinrijgen” ingedrukt.';

  @override
  String get t_err_dl_updateprogram2 => 'Het nieuwe bijwerkprogramma is klaar.\nOm de update te installeren moet u de machine eerst uitschakelen en vervolgens opnieuw inschakelen met de toets “Automatisch draadinrijgen” ingedrukt.';

  @override
  String get t_err592 => 'Druk op LADEN om het updatebestand op te slaan.';

  @override
  String get t_err593 => 'Selecteer het apparaat waarop u het updatebestand wilt opslaan.';

  @override
  String get t_err594 => 'Selecteer het apparaat waarop het updatebestand is opgeslagen.';

  @override
  String get t_err_dl_updateprogram => 'Druk op de toets Start om het bijwerkprogramma te downloaden.';

  @override
  String get t_err_dl_fail => 'Downloaden mislukt: interne opslag is vol.';

  @override
  String get t_err_networkconnectionerr => 'U hebt geen netwerkverbinding meer.\nZorg ervoor dat de machine is verbonden met een draadloos netwerk.';

  @override
  String get t_err_not_turnoff => 'Zet machine niet uit.';

  @override
  String get t_err_pressresume_continuedl => 'Druk op de toets Hervatten om door te gaan met downloaden.';

  @override
  String get t_err_updateformovie => 'Werk opnieuw bij om de filmpjes te installeren.';

  @override
  String get t_err595 => 'Typ de activatiecode van 16 cijfers.';

  @override
  String get t_err596 => 'Typ de activatiecode van 16 cijfers en druk vervolgens op de toets [Instellen].';

  @override
  String get t_err597 => 'Het machinenummer en de activatiecode worden verzonden naar de server.';

  @override
  String get t_err598 => '\'Online-machinecertificering\' wordt aanbevolen als de machine verbinding heeft met een draadloos netwerk.';

  @override
  String get t_err599 => 'De activatiecode is niet juist.\nControleer de sleutel en typ de sleutel vervolgens opnieuw in.';

  @override
  String get t_err599_used => 'De code die u hebt opgegeven, is al geregistreerd met een andere machine.';

  @override
  String get t_err601 => 'Draadloos LAN inschakelen?';

  @override
  String get t_err602 => 'Zoekend naarSSID…';

  @override
  String get t_err603 => 'Instellingen toepassen?';

  @override
  String get t_err604 => 'Verbonden met draadloos LAN.';

  @override
  String get t_err605 => 'Verbinding maken met draadloos LAN.';

  @override
  String get t_err606 => 'Niet verbonden met netwerk.\nControleer de instelling voor draadloos LAN.';

  @override
  String get t_err607 => 'Verbinding met server mislukt.\nControleer de netwerkinstellingen.';

  @override
  String get t_err608 => 'Verificatie bij verbinding met server mislukt.\nControleer proxyserverinstelling.';

  @override
  String get t_err609 => 'Er heeft zich een netwerkfout voorgedaan.';

  @override
  String get t_err611 => 'Uitgeschakeld';

  @override
  String get t_err612 => 'Er zijn fouten opgetreden in de netwerkfunctie.';

  @override
  String get t_err613 => 'De gegevens kunnen niet worden geïmporteerd.\nBegin opnieuw.';

  @override
  String get t_err614 => 'Er is toegangspuntinformatie opgeslagen.\nOK om verbinding te maken met deze informatie?';

  @override
  String get t_err615 => 'Verbindingsfout 01';

  @override
  String get t_err616 => 'Netwerksleutel fout.';

  @override
  String get t_err617 => 'Netwerksleutel fout.';

  @override
  String get t_err618 => 'Netwerk resetten?';

  @override
  String get t_err620 => 'Schakel de machine uit en weer in. ';

  @override
  String get t_err621 => 'Verificatie bij verbinding met server mislukt.\nControleer proxyserverinstelling.';

  @override
  String get t_err622 => 'Verificatie mislukt.\nControleer gebruikersnaam of wachtwoord.';

  @override
  String get t_err623 => 'Annuleren';

  @override
  String get t_err624 => 'Communicatiefout';

  @override
  String get t_err625 => 'Voltooid';

  @override
  String get t_err626 => 'Interface bevestigen';

  @override
  String get t_err627 => 'Verbinding NG';

  @override
  String get t_err628 => 'Verbinding met server mislukt.\nControleer de netwerkinstellingen.';

  @override
  String get t_err629 => 'Verbinding met server mislukt.\nProbeer het later opnieuw.';

  @override
  String get t_err630 => 'Bezig met downloaden.\nEven wachten.';

  @override
  String get t_err631 => 'Er is een fout opgetreden tijdens het downloaden.\nBegin opnieuw.';

  @override
  String get t_err632 => 'Geen toegangspunt.';

  @override
  String get t_err633 => 'Geen data!';

  @override
  String get t_err634 => 'Zie Problemen oplossen in gebruikershandleiding.';

  @override
  String get t_err636 => 'Server niet gevonden.\nControleer naam of adres of voer andere LDAP-server in.';

  @override
  String get t_err637 => 'Time-out van server.\nProbeer het later opnieuw.';

  @override
  String get t_err638 => 'Overbrengen…';

  @override
  String get t_err697 => 'Geen gegevensoverdracht mogelijk via USB-kabel.';

  @override
  String get t_err84_mdc => 'OK om het vorige geheugen op te roepen en opnieuw te gebruiken?\n(Mijn Design Center).';

  @override
  String get t_err84_iqd => 'OK om het vorige geheugen op te roepen en opnieuw te gebruiken?\n(IQ Designer).';

  @override
  String get t_err703_b => 'Installeer \"My Stitch Monitor\" om het borduren te controleren.';

  @override
  String get t_err703_t => 'Installeer \"IQ Intuition Monitoring\" om het borduren te controleren.';

  @override
  String get t_err704_b => 'Installeer de app \"My Stitch Monitor\" om het borduren op uw slimme apparaat te controleren. \n\nU kunt de voortgang van uw borduurwerk controleren op uw slimme apparaat. \nU kunt controleren welke garenkleuren worden gebruikt voor het borduren.';

  @override
  String get t_err704_t => 'Installeer de app \"IQ Intuition Monitoring\" om het borduren op uw slimme apparaat te controleren. \n\nU kunt de voortgang van uw borduurwerk controleren op uw slimme apparaat. \nU kunt controleren welke garenkleuren worden gebruikt voor het borduren.';

  @override
  String get t_err705_b => 'Installeer de app \"My Design Snap\" om afbeeldingen te verzenden van uw slimme apparaat naar uw machine. \n\nU kunt in Mijn Design Center gemakkelijk borduurpatronen maken van de afbeeldingen.';

  @override
  String get t_err705_t => 'Installeer de app \"IQ Intuition Positioning\" om afbeeldingen te verzenden van uw slimme apparaat naar uw machine. \n\nU kunt in \"IQ Designer\" gemakkelijk borduurpatronen maken van de afbeeldingen.';

  @override
  String get t_err708 => 'KIT1 is vereist voor het gebruik van deze app.';

  @override
  String get t_err709 => 'KIT2 is vereist voor het gebruik van deze app.';

  @override
  String get t_err711 => 'Deze borduurgegevens bevatten niet voldoende informatie over de garens.\nAls u correcte informatie over uw garen wilt zien, moet u het garenkleurnummer invoeren in het scherm voor garenkleurwisseling.';

  @override
  String get t_err713 => 'Lees voor gebruik de licentieovereenkomst voor eindgebruikers (EULA).';

  @override
  String get t_err715 => 'Ik accepteer de voorwaarden van de licentieovereenkomst.';

  @override
  String get t_err01_heated => 'Beveiliging geactiveerd wegens oververhitting van de motor die de as aandrijft. Zit de draad verstrikt?';

  @override
  String get t_err01_motor => 'Vastlopen van de motor die de as aandrijft, heeft geleid tot het activeren van een beveiliging. Zit de draad verstrikt?';

  @override
  String get t_err01_npsensor => 'Een defect van de naaldstandsensor heeft geleid tot het activeren van een beveiliging.';

  @override
  String get t_err734 => 'Voor iOS';

  @override
  String get t_err735 => 'Voor Android™';

  @override
  String get t_err738 => 'De borduurvolgorde wordt gewijzigd.';

  @override
  String get t_err739 => 'U kunt deze functie niet gebruiken wanneer u een speciaal patroon selecteert.';

  @override
  String get t_err740 => 'De steek overlapt met de steken aan de andere kant. Verklein de steekbreedte of vergroot de afstand van de verschuiving.';

  @override
  String get t_err741 => 'De gegevens zijn teruggebracht tot een compatibele grootte.';

  @override
  String get t_err742 => 'Wilt u naar de instellingen gaan waarmee u uw machine kunt verbinden met een draadloos netwerk?';

  @override
  String get t_err743 => 'U kunt deze functie niet gebruiken, aangezien de CanvasWorkspace-services momenteel niet beschikbaar zijn vanwege onderhoud. Even geduld a.u.b. totdat ze zijn hersteld.';

  @override
  String get t_err743_s => 'U kunt deze functie niet gebruiken, aangezien de Artspira-services momenteel niet beschikbaar zijn vanwege onderhoud. Even geduld a.u.b. totdat ze zijn hersteld.';

  @override
  String get t_err744 => 'Kan uw machine niet registreren in CanvasWorkspace.\nBegin opnieuw.';

  @override
  String get t_err744_s => 'Kan uw machine niet registreren in Artspira.\nBegin opnieuw.';

  @override
  String get t_err745 => 'De pincode is niet juist. Voer de pincode opnieuw in.';

  @override
  String get t_err746 => 'Uw machine is niet verbonden met internet.';

  @override
  String get t_err747 => 'Niet verbonden met de internetserver.\nControleer de proxy-instelling.';

  @override
  String get t_err748 => 'Er is een time-out opgetreden bij de server.\nProbeer het later opnieuw.';

  @override
  String get t_err749 => 'Wilt u de verbinding met CanvasWorkspace verbreken?';

  @override
  String get t_err749_s => 'Wilt u de verbinding met Artspira verbreken?';

  @override
  String get t_err750 => 'Kan de verbinding van uw machine met CanvasWorkspace niet verbreken. Verbreek de verbinding handmatig vanaf de webpagina.';

  @override
  String get t_err750_s => 'Kan de verbinding van uw machine met Artspira niet verbreken.\nVerbreek de verbinding handmatig vanuit de app.';

  @override
  String get t_err751 => 'Uw naaimachine moet zijn geregistreerd bij CanvasWorkspace om gegevens te kunnen verzenden (PIN-registratie).\nWilt u naar het instellingenscherm gaan?';

  @override
  String get t_err751_s => 'Uw naaimachine moet zijn geregistreerd bij Artspira om gegevens te kunnen verzenden (PIN-registratie).\nWilt u naar het instellingenscherm gaan?';

  @override
  String get t_err752 => 'Uw machine is mogelijk niet geregistreerd. Controleer op CanvasWorkspace.';

  @override
  String get t_err752_s => 'Uw machine is mogelijk niet geregistreerd. Controleer op Artspira.';

  @override
  String get t_err753 => 'De gegevens kunnen niet worden geüpload.\nBegin opnieuw.';

  @override
  String get t_err754 => 'De gegevens konden niet worden gedownload.\nBegin opnieuw.';

  @override
  String get t_err755 => 'Certificatie geslaagd.\nStart machine opnieuw op.\n\nAls u gegevens wilt verzenden naar uw ScanNCut-machine, moet u uw machine opnieuw starten en uw machine registreren bij de CanvasWorkspace-server vanaf pagina 13 van Instellingen.';

  @override
  String get t_err755_s => 'Certificatie geslaagd.\nStart machine opnieuw op.';

  @override
  String get t_err756 => 'Wilt u de bestaande gegevens vervangen door nieuwe gegevens?\n* De gegevens in de tijdelijke data opslag worden na een bepaalde tijd automatisch verwijderd.';

  @override
  String get t_err757 => 'Wilt u gegevens verzenden naar de tijdelijke data opslag op de server?\n* De gegevens in de tijdelijke data opslag worden na een bepaalde tijd automatisch verwijderd.';

  @override
  String get t_err761 => 'De tijdelijke data opslag bevat geen gegevens.';

  @override
  String get t_err762 => 'De tijdelijke data opslag bevat geen leesbare gegevens.';

  @override
  String get t_err763 => 'Uw ScanNCut- en naaimachine moeten zijn geregistreerd bij CanvasWorkspace om snijgegevens te kunnen ontvangen (PIN-registratie).\nWilt u naar het instellingenscherm gaan?';

  @override
  String get t_err763_s => 'Registreer uw naaimachine om gegevens te ontvangen met Artspira. (pincoderegistratie) \nWilt u naar het instellingenscherm gaan voor registratie?';

  @override
  String get t_err764 => 'Installeer “Artspira” op uw Smartphone of tablet. \nMaak uw eigen Artspira-account aan en borduren wordt nog leuker! \nScan de QR-code voor meer informatie.';

  @override
  String get t_err765 => 'De machinenaam kan niet worden gewijzigd.';

  @override
  String get t_err766 => 'Schakel de WLAN Draadloze verbinding in om de machinenaam te wijzigen.';

  @override
  String get t_err770 => 'Verwijderen is mislukt.';

  @override
  String get t_err771 => 'Als u alle eigen patronen verwijdert, worden de voor bewerking gebruikte patronen vervangen door een ander patroon.\nOK om alle eigen patronen te verwijderen?';

  @override
  String get t_err772 => 'Als u het geïmporteerde eigen patroon verwijdert of verandert nadat het is opgeslagen, kunnen de gegevens veranderen ten opzichte van het origineel.';

  @override
  String get t_err773 => 'Gegevens met geïmporteerde eigen patronen kunt u niet opslaan op een extern geheugenstation.';

  @override
  String get t_err774 => 'Alleen de borduursteekgegevens kunt u opslaan. Bewerkte gegevens, inclusief eigen patronen, kunt u niet opslaan op een extern geheugenstation. Sla deze gegevens op een intern geheugenstation op.';

  @override
  String get t_err775 => 'De gegevensopslag is vol.\nKies een eigen patroon voor vervanging door een nieuw patroon.';

  @override
  String get t_err776 => 'Als u het eigen patroon vervangt, kan het veranderen terwijl u het vorige patroon gebruikt. OK om door te gaan?';

  @override
  String get t_err_taper01 => 'Kan de tapse steek niet instellen. Vergroot de afstand of de hoek.';

  @override
  String get t_err_taper02 => 'Voltooi de instelling voordat u begint met naaien.';

  @override
  String get t_err_taper03 => 'OK om de instelling van de tapse steek te annuleren?';

  @override
  String get t_err_taper04 => 'OK om de huidige status van de tapse steek te annuleren?';

  @override
  String get t_err_taper05 => 'U kunt deze functie niet gebruiken bij de tapse steek.';

  @override
  String get t_err_tapering07 => 'Druk op de achteruitsteektoets om te starten met het naaien van het tapse einde.';

  @override
  String get t_err_tapering08 => 'Taps toelopen wordt gestopt na het opgegeven aantal herhalingen.';

  @override
  String get t_err_tapering09 => 'Taps toelopen eindigt bij de eindpunt sticker.';

  @override
  String get t_err785 => 'Zorg voor voldoende boven- en onderdraad om het borduurpatroon volledig te kunnen borduren; het resultaat zal niet naar tevredenheid zijn als één van beide draden op raakt.';

  @override
  String get t_err790 => 'Alle opgeslagen gegevens, instellingen en netwerkinformatie worden teruggezet naar standaard. \nWilt u doorgaan?';

  @override
  String get t_err791 => 'Verwijderen…\nZet de machine niet uit.';

  @override
  String get t_err792 => 'Terugzetten is voltooid.\nZet de machine uit.';

  @override
  String get t_err_paidcont_update => 'Als u deze gegevens wilt gebruiken, moet u de software van deze machine bijwerken naar de nieuwste versie.';

  @override
  String get t_err_embcarriageevacuate => 'Tik op OK om de borduurarm van de borduureenheid te verplaatsen naar de oorspronkelijk positie.';

  @override
  String get t_err_sr_01 => 'Verwijder de steekregulatormodule van de machine.';

  @override
  String get t_err_sr_02 => 'De naaistatus is geannuleerd, omdat er een aantal seconden geen stof is doorgevoerd.';

  @override
  String get t_err_sr_03 => 'U kunt beginnen met quilten uit de vrije hand/rijgen met de steekregulator.\n\nTrek de stof niet te strak; de naald kan anders breken.';

  @override
  String get t_err_sr_04 => 'Begin te naaien, nadat u deze modus hebt geselecteerd.';

  @override
  String get t_err_sr_05 => 'De naald verplaatst zich. Haal uw hand bij de naald weg.';

  @override
  String get t_err_sr_06 => 'De steekregulatormodule is gedeactiveerd. Het steekregulatorscherm wordt gesloten.   \nAls u het scherm weer wilt weergeven, activeert u de module opnieuw.';

  @override
  String get t_err_sr_08 => 'Trek niet te krachtig aan de stof om naaldbreuk te voorkomen; stel in plaats daarvan de draadspanning strakker in.';

  @override
  String get t_err_sr_09 => 'U kunt de tweelingnaaldmodus niet gebruiken in combinatie met deze functie.\nAls u het opnieuw wilt proberen, trekt u de aansluiting van de module voor steekregeling los en schakelt u de tweelingnaaldmodus uit.';

  @override
  String get t_err_sr_10 => 'Wanneer u rijgsteken gebruikt, gebruik dan niet de open quiltvoet voor de steekregulator. Anders kan de naald breken, waardoor u letsel kunt oplopen.';

  @override
  String get t_err_manual_01_b => 'Als u de handleidingen wilt weergeven op uw mobiele apparaat of PC, gaat u naar XXX (URL).';

  @override
  String get t_err_manual_02_t => 'Als u de handleiding wilt weergeven op uw mobiele apparaat of PC, gaat u naar XXX (URL).';

  @override
  String get t_err_proj_emb_001 => 'De functionaliteit van de projector is beperkt door het kleine borduurraam. “Projectorbewerking met stylus” wordt niet ondersteund, maar borduurpatronen worden wel geprojecteerd.\n\n*De borduurarm van de borduureenheid verplaatst zich wanneer u op OK drukt.';

  @override
  String get t_err_proj_emb_002 => 'De functionaliteit van de projector is beperkt door het kleine borduurraam. “Projectorbewerking met stylus” wordt niet ondersteund, maar borduurpatronen worden wel geprojecteerd.';

  @override
  String get t_err_proj_emb_003 => 'Projector schakelt uit.';

  @override
  String get t_err_proj_emb_004 => 'Projector schakelt uit omdat borduurraam is verwijderd.\n\n* De borduurarm verplaatst zich wanneer u op OK drukt.';

  @override
  String get t_err_proj_emb_005 => 'Projector schakelt uit omdat borduurraam is verwijderd.';

  @override
  String get t_err_proj_emb_006 => 'Projector schakelt uit.\n\n* De borduurarm verplaatst zich wanneer u op OK drukt.';

  @override
  String get t_err_proj_emb_007 => 'OK om huidige patroonkeuze te annuleren?\n\n* De borduurarm verplaatst zich wanneer u op OK drukt.';

  @override
  String get t_err_proj_emb_008 => 'Geconverteerd naar borduurpatroon en Mijn Design Center wordt afgesloten.\nOK om door te gaan naar borduurcombinatiescherm?\n\n* Projector schakelt uit en de borduurarm van de borduureenheid zal zich verplaatsen nadat u op OK hebt gedrukt.';

  @override
  String get t_err_proj_emb_009 => 'Geconverteerd naar borduurpatroon en IQ Designer wordt afgesloten.\nOK om door te gaan naar borduurcombinatiescherm?\n\n* Projector schakelt uit en de borduurarm van de borduureenheid zal zich verplaatsen nadat u op OK hebt gedrukt.';

  @override
  String get t_err_proj_emb_010 => 'Bezig met verwerken…';

  @override
  String get t_err_proj_emb_011 => 'Sluiten...';

  @override
  String get t_err_proj_emb_012 => 'Projector schakelt in.\n\n* De borduurarm verplaatst zich wanneer u op OK drukt.';

  @override
  String get t_err_proj_emb_013 => 'Projector schakelt in.';

  @override
  String get t_err_proj_emb_014 => 'Deze functie kan niet worden gebruikt terwijl de projector in werking is.';

  @override
  String get t_err_proj_smallframe => 'Niet beschikbaar vanwege het kleine borduurraam.';

  @override
  String get t_err_mdc_import_01 => 'Pas de gegevens aan wanneer de grootte ervan wijzigt bij het laden.';

  @override
  String get t_err_voiceg_01 => 'Controleren op spraakhulpgegevens...';

  @override
  String get t_err_voiceg_02 => 'De spraakhulp is klaar en de instelling is ingeschakeld.';

  @override
  String get t_err_photos_01 => 'Verwijder het masker van de afbeelding.';

  @override
  String get t_err_photos_02 => 'OK om de aanpassing van de grootte van de afbeelding terug te zetten?';

  @override
  String get t_err_photos_03 => 'OK om de verwijdering van de achtergrond te annuleren?';

  @override
  String get t_err_photos_04 => 'Converteer naar borduurwerk.';

  @override
  String get t_err_photos_05 => 'Geconverteerd naar borduurpatroon en Picture Play borduurfunctie wordt afgesloten.\nOK om door te gaan naar borduurbewerkingsscherm?';

  @override
  String get t_err_photos_06 => 'Even geduld a.u.b.\nTijdens de conversie wordt de WLAN Draadloze verbinding verbinding tijdelijk uitgeschakeld.';

  @override
  String get t_err_photos_exit => 'OK om Picture Play borduurfunctie af te sluiten?';

  @override
  String get t_err_font_old_new => 'OK om het bestand te converteren naar de nieuwere gegevensindeling terwijl de oude indeling in gebruik is?';

  @override
  String get t_err_font_old_lomited => 'De bewerkingsfunctie is beperkt als gevolg van de oude gegevensindeling.';

  @override
  String get t_err_firstset_wlan => 'Stel een WLAN Draadloze verbinding in.\nWilt u naar de instellingen gaan om uw machine op het draadloze netwerk aan te sluiten?';

  @override
  String get t_err_firstset_voiceguidance => 'Stel de functie Spraakbegeleiding in.\nWilt u naar de instellingen voor Spraakbegeleiding?';

  @override
  String get t_err_wlan_function_01 => 'Om de functie te kunnen gebruiken, moet de instelling voor de WLAN Draadloze verbinding op de machine zijn ingeschakeld en moet de machine zijn aangesloten op een draadloos netwerk.\nWilt u naar de instellingen gaan om uw machine op een draadloos netwerk aan te sluiten?';

  @override
  String get t_err_teachingimage => 'Afbeeldingen dienen uitsluitend ter illustratie, sommige afbeeldingen kunnen per model verschillen.';

  @override
  String get t_err_photo_disclaimers => 'Wanneer u deze functie gebruikt, stemt u ermee in dat de inhoud niet zal worden gebruikt:\n• voor doeleinden die in strijd zijn met de van toepassing zijnde wet- en regelgeving (waaronder met name racistische, discriminerende, haatdragende of (kinder)pornografische inhoud en/of verklaringen die in strijd zijn met de goede zeden of de openbare orde);\n• om het recht van personen op privacy of publiciteit te schenden;\n• om inbreuk te maken op auteursrechten, handelsmerken of andere intellectuele eigendomsrechten van derden;\n• om een URL of trefwoord te bevatten dat kijkers naar kwaadaardige sites leidt.\nDe gebruiker aanvaardt en erkent als enige verantwoordelijk te zijn voor de gebruikte inhoud.\nRaadpleeg voor meer informatie de Gebruiksvoorwaarden.\n\nDoor de inhoud te gebruiken, verklaar ik dat ik de Servicevoorwaarden en Richtlijnen heb gelezen en volledig begrijp.';

  @override
  String get t_err_framemovekey => '* Druk op de toets Borduurraam verplaatsen op het borduurscherm om het borduurraam naar het midden te verplaatsen.';

  @override
  String get speech_colorchangeinfo => 'De steken zijn voltooid.\nStel de volgende kleur draad in.';

  @override
  String get t_err_updateinfo_01 => 'Er is een belangrijke update beschikbaar.\nDownload het update-bestand via [Bijwerkprogramma downloaden] in “Machine-instellingen” om uw machine bij te werken.';

  @override
  String get t_err_updateinfo_02 => 'Verbind uw machine met een draadloos netwerk om meldingen te ontvangen over de nieuwste software. \nU kunt ook naar de Brother support website gaan voor de nieuwste software-updates.';

  @override
  String get t_err_removecarriage => 'Schakel de machine uit voordat u de borduureenheid bevestigt of verwijdert.';

  @override
  String get t_err_filter_removed => 'Het filter is gewist omdat de categorie niet wordt ondersteund.';

  @override
  String get t_err_filter_cleared => 'Het filter kan worden gewist omdat de filterfunctie niet van toepassing is in deze categorie.';

  @override
  String get t_principal07 => '[Persvoethendel]';

  @override
  String get t_principal07_01 => '\nZet de persvoethendel omhoog en omlaag om de persvoet omhoog en omlaag te zetten.';

  @override
  String get t_principal07_02 => '(a) Persvoet\n(b) Persvoethendel';

  @override
  String get t_principal03 => '[Schuifknop voor snelheidsregeling]';

  @override
  String get t_principal03_01 => '\nMet deze schuifknop stelt u de naaisnelheid in.\nSchuif de schuifknop naar links om op lagere snelheid te naaien.\nSchuif de schuifknop naar rechts om op hogere snelheid te naaien.';

  @override
  String get t_principal03_02 => '(a) Hendel\n(b) Langzaam\n(c) Snel';

  @override
  String get t_principal12 => '[Handwiel]';

  @override
  String get t_principal12_01 => 'Draai het handwiel naar u toe om de naald omhoog en omlaag te zetten.\nU moet het wiel naar de voorkant van de naaimachine draaien.';

  @override
  String get t_principal08 => '[Afneembare accessoiretafel]';

  @override
  String get t_principal08_01 => 'Bewaar de persvoet en de spoelen in de accessoireruimte van de afneembare accessoiretafel.\nOm cilindrische stukken te naaien verwijdert u de accessoiretafel.';

  @override
  String get t_principal10 => '[Kniehevel]';

  @override
  String get t_principal10_00 => '(a) Kniehevel';

  @override
  String get t_principal10_01 => '\nDe kniehevel stelt u in staat om de persvoet omhoog en omlaag te zetten met uw knie. U hebt dan beide handen vrij om de stof te manoeuvreren.\n\n1. Zet de lipjes op de kniehevel tegenover de groeven in de aansluiting en steek de kniehevel zo ver mogelijk naar binnen.';

  @override
  String get t_principal10_03_00 => '(a) Persvoet';

  @override
  String get t_principal10_03 => '\n2. Beweeg met uw knie de kniehevel naar rechts om de persvoet omhoog te zetten.\nLaat de kniehevel los om de persvoet omlaag te zetten.';

  @override
  String get t_principal11 => '[Voetpedaal]';

  @override
  String get t_principal11_00 => '\nU kunt ook het voetpedaal gebruiken om te starten en stoppen met naaien.\n\n1. Steek de stekker van het voetpedaal in de betreffende aansluiting op de machine.';

  @override
  String get t_principal11_02 => '2. Druk langzaam het voetpedaal in om te starten met naaien.\nLaat het voetpedaal los om de naaimachine te stoppen.\n\n* De snelheid die is ingesteld met de schuifknop voor de snelheidsregeling, is de maximale naaisnelheid van het voetpedaal.';

  @override
  String get t_xv_principal11_01 => 'Met het multifunctionele voetpedaal kunt u naast het starten/stoppen van naaien ook andere naaimachinehandelingen uitvoeren, zoals draad afsnijden en achteruitnaaien.';

  @override
  String get t_xv_principal11_02 => '1. Plaats de brede zijde van de bevestigingsplaat in één lijn met de inkeping aan de onderzijde van het hoofdvoetpedaal en zet deze vervolgens samen vast met een schroef.';

  @override
  String get t_xv_principal11_03 => '2. Plaats de andere zijde van de bevestigingsplaat in één lijn met de inkeping aan de onderzijde van het zijpedaal en zet deze vervolgens samen vast met een schroef.';

  @override
  String get t_xv_principal11_04 => '3. Steek de stekker van het zijpedaal in de aansluiting aan de achterzijde van het hoofdvoetpedaal.';

  @override
  String get t_xv_principal11_05 => '4. Steek de ronde stekker van het hoofdvoetpedaal in de voetpedaalaansluiting aan de rechterzijde van de machine.';

  @override
  String get t_xv_principal11_06 => '5. Druk langzaam het voetpedaal in om te starten met naaien. Laat het voetpedaal los om de naaimachine te stoppen.\n\n* De snelheid die is ingesteld met de schuifknop voor de snelheidsregeling, is de maximale naaisnelheid van het voetpedaal.';

  @override
  String get t_principal11_01_02 => '(a) Voetpedaal\n(b) Voetpedaalaansluiting';

  @override
  String get t_principal09 => '[Transporteurstandschakelaar]';

  @override
  String get t_principal09_01 => '\nZet de transporteur omlaag met de transporteurstandschakelaar.';

  @override
  String get t_principal09_02 => '(a) Transporteurstandschakelaar';

  @override
  String get t_principal_buttons_01 => '[\"Naaldstand\"-toets]';

  @override
  String get t_principal_buttons_01_01 => 'Gebruik deze toets wanneer u van naairichting verandert of op kleine stukken stof naait.\nDruk op deze toets om de naald omhoog of omlaag te zetten.\nDruk tweemaal op deze toets om een enkele steek te naaien.';

  @override
  String get t_principal_buttons_02 => '[\"Draadknip\"-toets]';

  @override
  String get t_principal_buttons_02_01 => 'Druk na het naaien op deze toets om de overtollige draad automatisch af te knippen.';

  @override
  String get t_principal_buttons_06 => '[\"Persvoethendel\"-toets]';

  @override
  String get t_principal_buttons_06_01 => 'Druk op deze toets om de persvoet omlaag te zetten en druk uit te oefenen op de stof.\nDruk opnieuw op deze toets om de persvoet omhoog te zetten.\nU zet de persvoet in de hoogste stand door de \"Persvoethendel\"-toets ingedrukt te houden.';

  @override
  String get t_principal_buttons_05 => '[\"Automatisch inrijgen\"-toets]';

  @override
  String get t_principal_buttons_05_01 => 'Met deze toets rijgt u de naald automatisch in.';

  @override
  String get t_principal_buttons_04 => '[\"Start/Stop\"-toets]';

  @override
  String get t_principal_buttons_04_01 => 'Als u op deze toets drukt, naait de machine een paar steken op lage snelheid en begint vervolgens te naaien op de snelheid die is ingesteld met de schuifknop voor snelheidsregeling. Druk nogmaals op de toets om de naaimachine stil te zetten.\nHoud de toets ingedrukt om op de laagste snelheid te naaien.\nDe toets verandert van kleur naar gelang de bedieningsstand van de naaimachine.\n\nGroen: de naaimachine is klaar om te naaien of is bezig met naaien.\nRood: de naaimachine kan nu niet naaien.';

  @override
  String get t_principal_buttons_03 => '[\"Achteruit\"-toets]';

  @override
  String get t_principal_buttons_03_01 => 'Met deze knop naait u steken achteruit aan het begin en eind van een stiksel.\nVoor patronen met rechte steken en zigzagsteken naait de machine steken achteruit wanneer u de knop \"Achteruit\"-steek ingedrukt houdt. (Steken worden genaaid in tegengestelde richting).\nVoor de andere steekpatronen naait de machine verstevigingssteken. Wanneer u op deze knop drukt, naait de machine 3 tot 5 steken op dezelfde plek en stopt daarna automatisch.';

  @override
  String get t_principal_buttons_07 => '[\"Versteviging/Afhechten\"-toets]';

  @override
  String get t_principal_buttons_07_01 => 'Met deze knop naait u verstevigingssteken aan het begin en eind van een stiksel.\nVoor naaisteekpatronen: wanneer u op deze knop drukt, naait de machine 3 tot 5 steken op dezelfde plek en stopt daarna automatisch.\nVoor letter-/decoratieve steekpatronen naait de machine verstevigingssteken nadat u één eenheid van het patroon hebt genaaid.';

  @override
  String get t_basic13 => '[Bovendraad inrijgen]';

  @override
  String get t_basic13_01_02 => '(a) \"Persvoethendel\"-toets\n';

  @override
  String get t_basic13_01 => 'Druk op de filmtoets om een video over de getoonde aanwijzingen te bekijken.\n\n1. Druk op de \"Persvoethendel\"-toets om de persvoet omhoog te zetten. ';

  @override
  String get t_basic13_02_00 => '(a) \"Naaldstand\"-toets';

  @override
  String get t_basic13_02 => '\n2. Druk op de \"Naaldstand\"-toets om de naald omhoog te zetten.';

  @override
  String get t_basic13_03_02 => '(a) Klospen\n(b) Kloskap\n(c) Draadklos ';

  @override
  String get t_basic13_03 => '\n3. Draai de klospen zo dat deze omhoog wijst.\nZet de draadklos zo op de klospen dat de draad vanaf de voorkant van de klos afwikkelt.\nDuw de kloskap zo ver mogelijk op de klospen en zet de klospen weer in de oorspronkelijke stand. ';

  @override
  String get t_basic13_11_02 => '(a) Kloskap (klein)  \n(b) Draadklos (kruiswikkeldraad) \n(c) Ruimte ';

  @override
  String get t_basic13_11 => '\nWanneer u naait met fijne kruiswikkeldraad, gebruikt u de kleine kloskap en laat u enige ruimte tussen de kap en de draadklos. ';

  @override
  String get t_basic13_04_02 => '(a) Draadgeleiderplaat ';

  @override
  String get t_basic13_04 => '\n4. Houd de draad met beide handen vast en leid de draad onder en links omhoog langs de draadgeleiderplaat.';

  @override
  String get t_basic13_05 => '5. Houd de draadklos in uw rechterhand, volg de draadroute met het eind van de draad in uw linkerhand. Leid de draad omlaag, omhoog en vervolgens omlaag door de groeven.';

  @override
  String get t_basic13_06_02 => '(a) Draadgeleider op naaldstang';

  @override
  String get t_basic13_06 => '\n6. Leid de draad achter de draadgeleider op de naaldstang. \n';

  @override
  String get t_basic13_07 => '7. Druk op de \"Persvoethendeltoets\" om de persvoet omlaag te zetten. ';

  @override
  String get t_basic13_08_02 => '(a) Schijven draadgeleider ';

  @override
  String get t_basic13_08 => '\n8. Leid de draad door de draadgeleiderschijven. Zorg dat de draad door de gleuf in de draadgeleider gaat. De draad moet stevig in de draadgeleiderschijven gaan. De naald mag niet worden ingeregen. \n';

  @override
  String get t_basic13_09_02 => '(b) Draadknip';

  @override
  String get t_basic13_09 => '\n9. Trek de draad omhoog van onder de draadafsnijder. ';

  @override
  String get t_basic13_10_02 => '(a) \"Automatisch inrijgen\"-toets';

  @override
  String get t_basic13_10 => '\n10. Druk op de \"Automatisch inrijgen\"-toets, zodat de machine de naald automatisch inrijgt. ';

  @override
  String get t_basic14 => '[Spoel opwinden]';

  @override
  String get t_basic14_01_02 => '(a) Gleuf in de spoel\n(b) Veer op de as';

  @override
  String get t_basic14_00 => '\nDruk op de filmtoets om een video over de getoonde aanwijzingen te bekijken.\n\n1. Zet de gleuf in de spoel tegenover de veer op de spoelwinderas en plaats de spoel op de as. ';

  @override
  String get t_basic14_02 => '\n2. Plaats de extra klospen omhoog. ';

  @override
  String get t_basic14_02_02 => '(a) Extra klospen ';

  @override
  String get t_basic14_03 => '\n3. Zet de draadklos zo op de extra klospen dat de draad aan de voorkant van de klos afwikkelt. Duw de kloskap zo ver mogelijk op de klospen om de draadklos vast te zetten.';

  @override
  String get t_basic14_03_02 => '(a) Kloskap\n(b) Klospen\n(c) draadklos';

  @override
  String get t_basic14_04 => '\n4. Houd de draad met uw rechterhand vast bij de draadklos. Houd met uw linkerhand het uiteinde van de draad vast en leid de draad met beide handen rond de draadgeleider. ';

  @override
  String get t_basic14_04_02 => '(a) Draadgeleider';

  @override
  String get t_basic14_05 => '\n5. Leid de draad rond de voorspanningsschijf. Zorg dat de draad zich onder de voorspanningsschijf bevindt. ';

  @override
  String get t_basic14_05_02 => '(a)  Voorspanningsschijf';

  @override
  String get t_basic14_06 => '6. Wind de draad vijf à zes maal met de klok mee rond de spoel. ';

  @override
  String get t_basic14_07 => '(a) Geleidesleuf (met ingebouwde snijder)\n(b) Spoelwinderbasis\n\n7. Leid het uiteinde van de draad door de geleidegleuf in de spoelwinderbasis en trek de draad vervolgens naar rechts om de draad af te snijden met de draadafsnijder. ';

  @override
  String get t_basic14_08_02 => '\n8. Duw de spoelhouder naar links, totdat deze vastklikt. Het spoelwindvenster verschijnt op het LCD-scherm.';

  @override
  String get t_basic14_08_03 => '(a) spoelhouder (schakelaar) ';

  @override
  String get t_basic14_08_04 => '\n* Gebruik de regelaar voor spoelwindhoeveelheid om de hoeveelheid draad aan te passen die om de spoel wordt gewonden tot een van de vijf niveaus.';

  @override
  String get t_basic14_08_05 => '(a) Regelaar voor spoelwindhoeveelheid\n(b) Meer\n(c) Minder';

  @override
  String get t_basic14_09 => '9. Druk op de toets \"Start/stop spoelwinden\". De spoel begint automatisch op te winden.\n';

  @override
  String get t_basic14_10 => '10. U kunt de spoelwindsnelheid wijzigen door op - of + te drukken in het spoelwindvenster. Druk op \"OK\" om het spoelwindvenster te minimaliseren.';

  @override
  String get t_basic14_101 => '11. De spoel stopt wanneer het spoelwinden is voltooid. De spoelhouder gaat terug in de oorspronkelijke stand.';

  @override
  String get t_basic14_102 => '12. Knip de draad af met de draadafsnijder en verwijder de spoel. ';

  @override
  String get t_basic14_11 => '\n* Gebruik van de klospen \nU kunt de hoofdklospen gebruiken om de spoel te winden voordat u gaat naaien.\n\nOpmerking: gebruik deze klospen niet om de spoel te winden tijdens het naaien.\n\n1. Zet de gleuf in de spoel tegenover de veer op de spoelwinderas en plaats de spoel op de as. ';

  @override
  String get t_basic14_11_02 => '(a) Gleuf in de spoel\n(b) Veer op de as ';

  @override
  String get t_basic14_12 => '\n2. Draai de klospen zo dat deze omhoog wijst.\nZet de draadklos zo op de klospen dat de draad vanaf de voorkant van de klos afwikkelt.\nDuw de kloskap zo ver mogelijk op de klospen en zet de klospen weer in de oorspronkelijke stand. ';

  @override
  String get t_basic14_12_02 => '(a) Klospen\n(b) Kloskap\n(c) Draadklos';

  @override
  String get t_basic14_13 => '\n3. Houd de draad in uw hand en schuif deze in de groeven op de draadgeleiderplaat.\nLeid de draad door de draadgeleider. ';

  @override
  String get t_basic14_13_02 => '(a) Draadgeleiderplaatl\n(b) Draadgeleider ';

  @override
  String get t_basic14_14 => '\n4. Leid de draad rond de voorspanningsschijf. Zorg dat de draad zich onder de voorspanningsschijf bevindt. ';

  @override
  String get t_basic14_15_02 => '(a)  Voorspanningsschijf';

  @override
  String get t_basic14_16 => '5. Wind de draad vijf à zes keer met de klok mee rond de spoel. ';

  @override
  String get t_basic14_17 => '(a) Geleidesleuf (met ingebouwde snijder)\n(b) Spoelwinderbasis\n\n6. Leid het uiteinde van de draad door de geleidegleuf in de spoelwinderbasis en trek de draad vervolgens naar rechts om de draad af te snijden met de draadafsnijder. ';

  @override
  String get t_basic14_18 => '\n7. Duw de spoelhouder naar links, totdat deze vastklikt. Het spoelwindervenster verschijnt op het LCD-scherm.';

  @override
  String get t_basic14_18_02 => '(a) Spoelhouder (schakelaar)  ';

  @override
  String get t_basic14_20 => '8. Druk op de toets \"Start/stop spoelwinden\". De spoel begint automatisch op te winden.\n';

  @override
  String get t_basic14_201 => '9. U kunt de spoelwindsnelheid wijzigen door op - of + te drukken in het spoelwindvenster. Druk op \"OK\" om het spoelwindvenster te minimaliseren.';

  @override
  String get t_basic14_202 => '10. De spoel stopt met draaien wanneer het spoelwinden is voltooid. De spoelhouder gaat terug in de oorspronkelijke stand.';

  @override
  String get t_basic14_203 => '11. Knip de draad af met de draadafsnijder en verwijder de spoel. ';

  @override
  String get t_basic14_21_02 => '\nWanneer u naait met fijne kruiswikkeldraad, gebruikt u de kleine kloskap en laat u enige ruimte tussen de kap en de draadklos. ';

  @override
  String get t_basic14_21_03 => '(a) Kloskap (klein)\n(b) Draadklos (kruiswikkeldraad) \n(c) Ruimte ';

  @override
  String get t_basic15 => '[Naald verwisselen]';

  @override
  String get t_basic15_00 => '\nDe naald controleren:\nLeg de platte kant van de naald op een plat oppervlak.\nControleer de punt en de zijkanten van de naald.\nGooi verbogen naalden weg.';

  @override
  String get t_basic15_00_01 => '(a) Parallel\n(b) Plat oppervlak (spoelhuisdeksel, glas enz.) ';

  @override
  String get t_basic15_01 => '1. Druk op de \"Naaldstand\"-toets om de naald omhoog te zetten.';

  @override
  String get t_basic15_02 => '2. Druk op de \"Persvoet/naald verwisselen\"-toets.';

  @override
  String get t_basic15_03 => '3. Draai met de schroevendraaier de schroef los (naar de voorkant van de naaimachine) . Verwijder vervolgens de naald.';

  @override
  String get t_basic15_04 => '\n4. Steek de naald met de platte kant naar achteren zo ver mogelijk in de opening tot aan de naaldstopper (kijkvenster) in de naaldklem.\nDraai met een schroevendraaier de naaldklemschroef stevig vast.';

  @override
  String get t_basic15_04_02 => '(a) Naaldstopper\n(b) Opening voor het inbrengen van de naald\n(c) Platte kant van de naald';

  @override
  String get t_basic15_05 => '5. Druk op de \"Persvoet/naald verwisselen\"-toets om alle bedieningstoetsen te ontgrendelen.';

  @override
  String get t_basic16 => '[Persvoet verwisselen]';

  @override
  String get t_basic16_01 => '* Persvoet verwijderen\n\n1. Druk op de \"Naaldstand\"-toets om de naald omhoog te zetten.';

  @override
  String get t_basic16_02 => '2. Druk op de \"Persvoet/naald verwisselen\"-toets.';

  @override
  String get t_basic16_03 => '\n3. Zet de persvoethendel omhoog.';

  @override
  String get t_basic16_03_02 => '(a) Persvoet\n(b) Persvoethendel';

  @override
  String get t_basic16_04 => '\n4. Druk op de zwarte toets achter op de persvoethouder en verwijder de persvoet.';

  @override
  String get t_basic16_04_02 => '(a) Zwarte toets\n(b) Persvoethouder';

  @override
  String get t_basic16_05 => '\n* Persvoet bevestigen\n\n1. Plaats de nieuwe persvoetpen recht onder de inkeping op de houder.\nZet de persvoethendel omlaag, zodat de persvoetpen vastklikt in de inkeping in de houder.';

  @override
  String get t_basic16_05_02 => '(a) Inkeping\n(b) Pen';

  @override
  String get t_basic16_06 => '2. Druk op de \"Persvoet/naald verwisselen\"-toets om alle bedieningstoetsen te ontgrendelen.';

  @override
  String get t_basic16_07 => '(a) Persvoet\n(b) Persvoethendel\n\n3. Zet de persvoethendel omhoog.';

  @override
  String get t_basic17 => '[Spoel aanbrengen]';

  @override
  String get t_basic17_01 => 'Druk op de filmtoets om een video over de getoonde aanwijzingen te bekijken.\n\n1. Druk op de \"Persvoethendel\"-toets om de persvoet omhoog te zetten.';

  @override
  String get t_basic17_02 => '(a) Spoelhuisdeksel\n(b) Grendel\n\n2. Schuif de grendel van het spoelhuisdeksel naar rechts. Het spoelhuisdeksel gaat open.\nVerwijder het spoelhuisdeksel.';

  @override
  String get t_basic17_03 => '3. Houd de spoel vast met uw rechterhand en het uiteinde van de draad met uw linkerhand.';

  @override
  String get t_basic17_04 => '4. Plaats de spoel in het spoelhuis, zodat de draad naar links wordt afgewikkeld.\nHoud de spoel vast met uw rechterhand en leid de draad met uw linkerhand zoals aangegeven.';

  @override
  String get t_basic17_05 => '\n5. Leid de draad door de geleider en trek de draad uit naar de voorkant.\nDe draadafsnijder knipt de draad af.';

  @override
  String get t_basic17_05_02 => '(a) Draadafsnijder';

  @override
  String get t_basic17_06 => '6. Plaats het lipje in de linkerbenedenhoek van het spoelhuisdeksel en druk zachtjes op de rechterkant om het deksel te sluiten.';

  @override
  String get t_embbasic17 => '[Opstrijksteunstof bevestigen]';

  @override
  String get t_embbasic17_00 => '\nVoor het beste resultaat gebruikt u altijd borduursteunstof.\nVolg de instructies op de verpakking van de steunstof die u gebruikt.';

  @override
  String get t_embbasic17_01 => '\n1. Gebruik een stuk steunstof dat groter is dan het borduurraam.';

  @override
  String get t_embbasic17_01_02 => '(a) Grootte van het borduurraam\n(b) Opstrijksteunstof';

  @override
  String get t_embbasic17_02 => '\n2. Strijk de steunstof op de achterkant van de stof.\n\n* Werkt u met stoffen die niet gestreken kunnen worden (zoals badstof of stoffen met lussen die groter worden bij het strijken) of met gedeelten die u niet gemakkelijk kunt strijken? Leg dan de steunstof onder de stof zonder deze te bevestigen en plaats de stof plus steunstof vervolgens in het borduurraam. Of vraag aan uw erkende dealer wat de juiste steunstof is.';

  @override
  String get t_embbasic17_02_02 => '(a) Bevestigingskant van de steunstof\n(b) Stof (achterkant) ';

  @override
  String get t_embbasic17_03 => '\n* Borduurt u op dunne stof, zoals organdie of batist, of op ruwharige stof zoals badstof of corduroy? Dan krijgt u het beste resultaat met wateroplosbare steunstof (afzonderlijk verkrijgbaar) .\nDe wateroplosbare steunstof lost volledig op in water, waardoor het borduurwerk een mooiere afwerking verkrijgt.';

  @override
  String get t_embbasic18 => '[Stof plaatsen]';

  @override
  String get t_embbasic18_01 => '1. Haal de afstelschroef van het buitenste borduurraam omhoog en los en verwijder het binnenraam.';

  @override
  String get t_embbasic18_02 => '\n2. Leg de stof met de voorkant naar boven op het buitenraam.\nPlaats het binnenraam opnieuw in het buitenraam met Δ van het binnenraam tegenover Δ van het buitenraam.';

  @override
  String get t_embbasic18_02_02 => '(a) Binnenraam\n(b) Buitenraam\n(c) Afstelschroef';

  @override
  String get t_embbasic18_03 => '3. Draai de afstelschroef licht aan en trek aan de randen en de hoeken van de stof om deze glad te trekken.\nDraai de schroef niet los.';

  @override
  String get t_embbasic18_04 => '\n4. Trek de stof enigszins strak en draai de afstelschroef vast, zodat de stof licht gespannen blijft.\n\n* Nadat u de stof strak hebt getrokken, controleert u of deze licht gespannen is.\n\n* Controleer of het binnen- en buitenraam vlak zijn voordat u begint met borduren.\n\n* Opmerking\nTrek aan alle vier de hoeken en alle vier de randen aan de stof. Terwijl u aan de stof trekt, draait u de afstelschroef van het raam vast.';

  @override
  String get t_embbasic18_04_02 => '(a) Buitenraam\n(b) Binnenraam\n(c) Stof';

  @override
  String get t_embbasic18_04_11 => '*Bij gebruik van een borduurraam waaraan een hendel is bevestigd.\n';

  @override
  String get t_embbasic18_04_12 => '\n1. Zet de hendel omlaag.';

  @override
  String get t_embbasic18_04_13 => '2. Draai de afstelschroef van het raam met de hand los en verwijder het binnenraam.';

  @override
  String get t_embbasic18_04_14 => '\n3. Plaats de stof.';

  @override
  String get t_embbasic18_04_15 => '4. Draai de afstelschroef van het raam met de hand vast.';

  @override
  String get t_embbasic18_04_16 => '5. Zet de hendel terug in de oorspronkelijke stand.';

  @override
  String get t_embbasic18_05 => '\n[Gebruik van het borduurvel]';

  @override
  String get t_embbasic18_05_01 => 'Wanneer u het patroon op een speciale plek wilt borduren, gebruikt u het borduurvel met het raam.\n\n1. Markeer met een krijtje op de stof waar u wilt borduren.';

  @override
  String get t_embbasic18_05_02 => '(a) Borduurpatroon\n(b) Markering';

  @override
  String get t_embbasic18_06 => '\n2. Plaats het borduurvel op het binnenraam. Laat de lijnen op het borduurvel samenvallen met de markering die u op de stof hebt aangebracht.';

  @override
  String get t_embbasic18_06_02 => '(a) Binnenraam\n(b) Richtlijn';

  @override
  String get t_embbasic18_07 => '\n3. Rek de stof enigszins, zodat vouwen en kreukels verdwijnen. Plaats vervolgens het binnenraam in het buitenraam.';

  @override
  String get t_embbasic18_07_02 => '(a) Binnenraam\n(b) Buitenraam';

  @override
  String get t_embbasic18_08 => '4. Verwijder het borduurvel.';

  @override
  String get t_embbasic19 => '[Borduurraam bevestigen]';

  @override
  String get t_embbasic19_01 => '* Wind de spoel op en plaats de spoel voordat u het borduurraam aanbrengt.\n\n1. Druk op de \"Persvoethendel\"-toets om de persvoet omhoog te zetten.';

  @override
  String get t_embbasic19_02 => '\n2. Zet de raambevestigingshendel omhoog.';

  @override
  String get t_embbasic19_03 => '\n3. Laat borduurraamgeleider langs de rechterrand van de borduurraamhouder vallen.';

  @override
  String get t_embbasic19_03_02 => '(a) Borduurraamhouder\n(b) Borduurraamgeleider';

  @override
  String get t_embbasic19_04 => '4. Schuif het borduurraam in de houder. Zorg dat Δ op het borduurraam tegenover Δ op de houder staat.';

  @override
  String get t_embbasic19_05 => '\n5. Zet de borduurraambevestigingshendel omlaag om het borduurraam vast te zetten in de borduurraamhouder.\n\n* Als u de raambevestigingshendel niet omlaag zet, wordt het borduurpatroon mogelijk niet goed genaaid, of raakt de persvoet het borduurraam, waardoor u mogelijk letsel oploopt.';

  @override
  String get t_embbasic19_05_02 => '(a) Raambevestigingshendel';

  @override
  String get t_embbasic19_06 => '\n[Borduurraam verwijderen]\n\n1. Zet de raambevestigingshendel omhoog.';

  @override
  String get t_embbasic19_07 => '2. Trek het borduurraam naar u toe.';

  @override
  String get t_embbasic20 => '[borduureenheid bevestigen]';

  @override
  String get t_embbasic20_01 => 'Lees onderstaande stappen voordat u de machine uitschakelt.\n\n1. Zet de hoofdschakelaar uit en verwijder de accessoiretafel (indien aanwezig op de machine).';

  @override
  String get t_embbasic20_03 => '\n2. Steek de verbindingspen van de borduureenheid in het aansluitpunt voor de borduureenheid op de machine. Duw zacht totdat de borduureenheid op zijn plaats klikt.';

  @override
  String get t_embbasic20_03_02 => '(a) Verbindingspen van de borduureenheid\n(b) Aansluitpunt voor de borduureenheid op de machine';

  @override
  String get t_embbasic20_04 => '(a) UIT\n(b) AAN\n\n3. Zet de hoofdschakelaar aan.';

  @override
  String get t_embbasic20_05 => '4. Druk op de OK-toets. De wagen komt in de initialisatiestand te staan.';

  @override
  String get t_embbasic20_06 => '[borduureenheid verwijderen]';

  @override
  String get t_embbasic20_06_02 => '\n(a) UIT\n(b) AAN\n\nLees onderstaande stappen voordat u de machine uitschakelt.\n\n1. Zet de hoofdschakelaar uit.';

  @override
  String get t_embbasic20_07 => '(a) Ontgrendelknop (onder borduureenheid)\n\n2. Houd de ontgrendelknop ingedrukt en trek de borduureenheid uit de machine.';

  @override
  String get t_xp_embbasic21 => '[Borduurvoet \"W\" bevestigen]';

  @override
  String get t_xp_embbasic21_01 => '1. Druk op de \"Naaldstand\"-toets om de naald omhoog te zetten.';

  @override
  String get t_xp_embbasic21_02 => '2. Druk op de \"Persvoet/naald verwisselen\"-toets.';

  @override
  String get t_xp_embbasic21_03 => '\n3. Zet de persvoethendel omhoog.';

  @override
  String get t_xp_embbasic21_04 => '\n4. Druk op de zwarte toets op de persvoethouder en verwijder de persvoet.';

  @override
  String get t_xp_embbasic21_04_02 => '(a) Zwarte toets\n(b) Persvoethouder';

  @override
  String get t_xp_embbasic21_05 => '\n5. Draai met de bijgeleverde schroevendraaier de persvoethouderschroef los en verwijder de persvoethouder.';

  @override
  String get t_xp_embbasic21_05_02 => '(a) Schroevendraaier\n(b) Persvoethouder\n(c) Persvoethouderschroef';

  @override
  String get t_xp_embbasic21_06 => '(a) Persvoethendel\n\n6. Zet de persvoethendel omlaag.';

  @override
  String get t_xp_embbasic21_07_01 => '(a) Persvoetstang\n';

  @override
  String get t_xp_embbasic21_07_02 => '7. Plaats borduurvoet \"W\" vanaf de achterkant op de persvoetstang.';

  @override
  String get t_xp_embbasic21_08_01 => '(a) Persvoethouderschroef\n';

  @override
  String get t_xp_embbasic21_08_02 => '8. Houd de borduurvoet met uw rechterhand op zijn plaats en draai vervolgens de persvoethouderschroef stevig vast met de bijgeleverde schroevendraaier.';

  @override
  String get t_xp_embbasic21_09 => '9. Druk op de \"Persvoet/naald verwisselen\"-toets om alle bedieningstoetsen te ontgrendelen.';

  @override
  String get t_embbasic21 => '[Borduurvoet \"W\" bevestigen]';

  @override
  String get t_embbasic21_01 => '1. Druk op de \"Naaldstand\"-toets om de naald omhoog te zetten.';

  @override
  String get t_embbasic21_02 => '2. Druk op de \"Persvoet/naald verwisselen\"-toets.';

  @override
  String get t_embbasic21_03 => '\n3. Zet de persvoethendel omhoog.';

  @override
  String get t_embbasic21_04 => '\n4. Druk op de zwarte toets op de persvoethouder en verwijder de persvoet.';

  @override
  String get t_embbasic21_04_02 => '(a) Zwarte toets\n(b) Persvoethouder';

  @override
  String get t_embbasic21_05 => '\n5. Draai met de bijgeleverde schroevendraaier de persvoethouderschroef los en verwijder de persvoethouder.';

  @override
  String get t_embbasic21_05_02 => '(a) Schroevendraaier\n(b) Persvoethouder\n(c) Persvoethouderschroef';

  @override
  String get t_embbasic21_06 => '(a) Persvoethendel\n\n6. Zet de persvoethendel omlaag.';

  @override
  String get t_embbasic21_07 => '(a) Arm\n(b) Naaldklemschroef\n(c) Persvoethouderschroef\n(d) Wisser\n\n7. Plaats borduurvoet \"W\" zo op de persvoetstang dat de arm van borduurvoet \"W\" de achterkant van de naaldhouder raakt.';

  @override
  String get t_embbasic21_08 => '8. Draai met de bijgeleverde schroevendraaier de schroef op de persvoethouder stevig vast.';

  @override
  String get t_embbasic21_09 => '9. Druk op de \"Persvoet/naald verwisselen\"-toets om alle bedieningstoetsen te ontgrendelen.';

  @override
  String get t_embbasic21_emb_07 => '(a) Arm\n(b) Naaldklemschroef\n(c) Persvoethouderschroef\n(d) Wisser\n\n3. Plaats borduurvoet \"W\" zo op de persvoetstang dat de arm van borduurvoet \"W\" de achterkant van de naaldhouder raakt.';

  @override
  String get t_embbasic21_emb_08 => '4. Draai met de bijgeleverde schroevendraaier de schroef op de persvoethouder stevig vast.';

  @override
  String get t_embbasic21_emb_09 => '5. Druk op de \"Persvoet/naald verwisselen\"-toets om alle bedieningstoetsen te ontgrendelen.';

  @override
  String get t_xv_embbasic21 => '[Borduurvoet \"W+\" bevestigen]';

  @override
  String get t_xv_embbasic21_05 => '\n5. Verwijder de schroef van de persvoethouder met de bijgeleverde schroevendraaier en verwijder vervolgens de persvoethouder.';

  @override
  String get t_xv_embbasic21_07_01 => '(a) Persvoetstang\n';

  @override
  String get t_xv_embbasic21_07_02 => '7. Plaats borduurvoet \"W+\" vanaf de achterkant op de persvoetstang.';

  @override
  String get t_xv_embbasic21_08_01 => '(a) Persvoethouderschroef\n';

  @override
  String get t_xv_embbasic21_08_02 => '8. Houd de borduurvoet met uw rechterhand op zijn plaats en draai vervolgens de persvoethouderschroef stevig vast met de bijgeleverde schroevendraaier.';

  @override
  String get t_xv_embbasic21_09 => '9. Plaats de aansluiting van de borduurvoet \"W+\" met LED-aanwijzer in de aansluiting aan de linkerkant van uw machine.';

  @override
  String get t_xv_embbasic21_10 => '10. Druk op de \"Persvoet/naald verwisselen\"-toets om alle bedieningstoetsen te ontgrendelen.';

  @override
  String get t_embbasic22 => '[Juiste steunstof]';

  @override
  String get t_embbasic22_00_01 => '1. Stof die u mag strijken';

  @override
  String get t_embbasic22_00_02 => '2. Stof die u niet mag strijken';

  @override
  String get t_embbasic22_00_03 => '3. Dunne stof';

  @override
  String get t_embbasic22_00_04 => '4. Ruwharige stof';

  @override
  String get t_embbasic22_00_05 => '\nVoor het beste resultaat gebruikt u altijd borduursteunstof. Volg de instructies op de verpakking van de steunstof die u gebruikt.';

  @override
  String get t_embbasic22_01 => '\n[1. Stof die u mag strijken]';

  @override
  String get t_embbasic22_01_02 => '\nStrijk de steunstof op de achterkant van de stof. Gebruik een stuk steunstof dat groter is dan het borduurraam.';

  @override
  String get t_embbasic22_01_03 => '(a) Grootte van het borduurraam\n(b) Opstrijksteunstof';

  @override
  String get t_embbasic22_02 => '[2. Stof die u niet mag strijken]';

  @override
  String get t_embbasic22_02_02 => '\nPlaats de steunstof onder de stof zonder de steunstof te bevestigen. Plaats vervolgens de stof plus steunstof in het borduurraam.';

  @override
  String get t_embbasic22_03 => '[3. Dunne stof]';

  @override
  String get t_embbasic22_03_02 => '\nDe beste resultaten behaalt u met wateroplosbare steunstof (afzonderlijk verkrijgbaar) . De wateroplosbare steunstof lost volledig op in water, waardoor het borduurwerk een mooiere afwerking verkrijgt.';

  @override
  String get t_embbasic22_04 => '[4. Ruwharige stof]';

  @override
  String get t_embbasic22_04_02 => '\nBij het maken van een quilt zult merken dat het handig is gebruik te maken van de kniehevel en het voetpedaal Werkt u met stof die u niet mag strijken (badstof of stof met lussen die groter worden bij het strijken) ? Plaats de steunstof dan onder de stof zonder de steunstof te bevestigen. Plaats vervolgens de stof plus steunstof in het borduurraam. Of gebruik anders wateroplosbare steunstof (afzonderlijk verkrijgbaar) .';

  @override
  String get t_embbasic23 => '[Draadspanning aanpassen]';

  @override
  String get t_embbasic23_01 => 'Bij het borduren moet u de draadspanning zo instellen dat de bovendraad net zichtbaar is aan de achterkant van de stof.';

  @override
  String get t_embbasic23_01_01 => '1. Juiste draadspanning';

  @override
  String get t_embbasic23_01_02 => '2. De bovendraad is te strak';

  @override
  String get t_embbasic23_01_03 => '3. De bovendraad is te los';

  @override
  String get t_embbasic23_02 => '[1. Juiste draadspanning]';

  @override
  String get t_embbasic23_02_02 => '\nHet patroon is zichtbaar aan de achterkant van de stof. Als de draadspanning niet juist is ingesteld, wordt het patroon niet mooi afgewerkt. De stof kan gaan trekken of de draad kan breken.';

  @override
  String get t_embbasic23_03 => '[2. De bovendraad is te strak]';

  @override
  String get t_embbasic23_03_02 => '\nDe spanning van de bovendraad is de hoog. Hierdoor is de onderdraad zichtbaar aan de voorkant van de stof.';

  @override
  String get t_embbasic23_03_03 => 'Druk op - om de spanning van de bovendraad te verlagen.';

  @override
  String get t_embbasic23_04 => '[3. De bovendraad is te los]';

  @override
  String get t_embbasic23_04_02 => '\nDe spanning van de bovendraad is te laag. Hierdoor is de bovendraad te los en kunnen lussen ontstaan aan de voorkant van de stof.';

  @override
  String get t_embbasic23_04_03 => 'Druk op + om de spanning van de bovendraad te verhogen.';

  @override
  String get t_trouble22 => '[Bovendraad breekt]';

  @override
  String get t_trouble22_01 => '* Oorzaak 1\nDe machine is niet juist ingeregen (verkeerde extra klospenhouder, extra klospenhouder zit los, draad heeft inrijger naaldstang niet gepakt enz.) .\n\n* Oplossing\nRijg de naaimachine juist in.';

  @override
  String get t_trouble22_02 => '* Oorzaak 2\nGeknoopte of verstrikt geraakte draad wordt gebruikt.\n\n* Oplossing\nVerwijder alle knopen uit de draad.';

  @override
  String get t_trouble22_03 => '* Oorzaak 3\nDe geselecteerde naald is niet geschikt voor de draad die wordt gebruikt.\n\n* Oplossing\nSelecteer een naald die geschikt is voor de soort draad die wordt gebruikt.';

  @override
  String get t_trouble22_04 => '* Oorzaak 4\nBovendraadspanning is te hoog.\n\n* Oplossing\nPas de draadspanning aan.';

  @override
  String get t_trouble22_05 => '* Oorzaak 5\nDraad is gedraaid.\n\n* Oplossing\nKnip bijvoorbeeld met een schaar de gedraaide draad af en haal deze uit de grijper enz.';

  @override
  String get t_trouble22_06 => '* Oorzaak 6\nNaald is gedraaid, verbogen of de punt is bot.\n\n* Oplossing\nVervang de naald.';

  @override
  String get t_trouble22_07 => '* Oorzaak 7\nNaald is onjuist geplaatst.\n\n* Oplossing\nPlaats de naald opnieuw op de juiste wijze.';

  @override
  String get t_trouble22_08 => '* Oorzaak 8\nEr zitten krassen bij de opening van de steekplaat.\n\n* Oplossing\nVervang de steekplaat of neem contact op met de dichtstbijzijnde dealer.';

  @override
  String get t_trouble22_09 => '* Oorzaak 9\nEr zitten krassen bij de opening in de persvoet.\n\n* Oplossing\nVervang de persvoet of neem contact op met de dichtstbijzijnde dealer.';

  @override
  String get t_trouble22_10 => '* Oorzaak 10\nEr zitten krassen op het spoelhuis.\n\n* Oplossing\nVervang het spoelhuis of neem contact op met de dichtstbijzijnde dealer.';

  @override
  String get t_trouble23 => '[Onderdraad breekt]';

  @override
  String get t_trouble23_01 => '* Oorzaak 1\nSpoel is niet juist geplaatst.\n\n* Oplossing\nPlaats de onderdraad op de juiste manier.';

  @override
  String get t_trouble23_02 => '* Oorzaak 2\nEr zitten krassen op de spoel of de spoel draait niet goed.\n\n* Oplossing\nPlaats de spoel opnieuw.';

  @override
  String get t_trouble23_03 => '* Oorzaak 3\nDraad is verdraaid.\n\n* Oplossing\nKnip bijvoorbeeld met een schaar de verdraaide draad af en verwijder deze uit de grijper enz.';

  @override
  String get t_trouble24 => '[Overgeslagen steken]';

  @override
  String get t_trouble24_01 => '* Oorzaak 1\nDe machine is onjuist ingeregen.\n\n* Oplossing\nRaadpleeg de stappen voor het inrijgen van de naaimachine en rijg de machine juist in.';

  @override
  String get t_trouble24_02 => '* Oorzaak 2\nOngeschikte naald of draad voor gekozen stof.\n\n* Oplossing\nRaadpleeg \"Overzichtsschema stoffen/draad/naald\".';

  @override
  String get t_trouble24_03 => '* Oorzaak 3\nNaald is verdraaid, verbogen of de punt is stomp.\n\n* Oplossing\nVervang de naald.';

  @override
  String get t_trouble24_04 => '* Oorzaak 4\nNaald is niet juist geplaatst.\n\n* Oplossing\nPlaats de naald nu op de juiste wijze.';

  @override
  String get t_trouble24_05 => '* Oorzaak 5\nNaald is defect.\n\n* Oplossing\nVervang de naald.';

  @override
  String get t_trouble24_06 => '* Oorzaak 6\nStof of pluisjes onder de steekplaat.\n\n* Oplossing\nVerwijder stof of pluisjes met het schoonmaakborsteltje.';

  @override
  String get t_trouble25 => '[Stof rimpelt]';

  @override
  String get t_trouble25_01 => '* Oorzaak 1\nDe boven- of onderdraad is verkeerd ingeregen.\n\n* Oplossing\nRaadpleeg de stappen voor het inrijgen van de naaimachine en rijg de machine juist in.';

  @override
  String get t_trouble25_02 => '* Oorzaak 2\nExtra klospenhouder is onjuist aangebracht.\n\n* Oplossing\nRaadpleeg de methode voor het bevestigen van de extra klospenhouder en bevestig de extra klospenhouder opnieuw.';

  @override
  String get t_trouble25_03 => '* Oorzaak 3\nOngeschikte naald of onjuiste draad voor gekozen stof.\n\n* Oplossing\nRaadpleeg \"Overzichtsschema stoffen/draad/naald\".';

  @override
  String get t_trouble25_04 => '* Oorzaak 4\nNaald is verdraaid, verbogen of de punt is stomp.\n\n* Oplossing\nVervang de naald.';

  @override
  String get t_trouble25_05 => '* Oorzaak 5\nSteken zijn te lang wanneer u dunne stoffen naait.\n\n* Oplossing\nVerkort de steeklengte.';

  @override
  String get t_trouble25_06 => '* Oorzaak 6\nDraadspanning is niet juist ingesteld.\n\n* Oplossing\nPas de draadspanning aan.';

  @override
  String get t_trouble25_07 => '* Oorzaak 7\nU hebt niet de juiste persvoet gebruikt.\n\n* Oplossing\nGebruik de juiste persvoet.';

  @override
  String get t_trouble26 => '[De machine maakt veel lawaai]';

  @override
  String get t_trouble26_01 => '* Oorzaak 1\nDraad of pluisjes zitten vast in de transporteur.\n\n* Oplossing\nVerwijder stof of pluisjes.';

  @override
  String get t_trouble26_02 => '* Oorzaak 2\nEr zitten stukjes draad vast in de grijper.\n\n* Oplossing\nReinig de grijper.';

  @override
  String get t_trouble26_03 => '* Oorzaak 3\nDe bovendraad is onjuist ingeregen.\n\n* Oplossing\nRaadpleeg de stappen voor het inrijgen van de naaimachine en rijg de machine correct in.';

  @override
  String get t_trouble26_04 => '* Oorzaak 4\nEr zitten krassen op de grijper.\n\n* Oplossing\nVervang de grijper of neem contact op met de dichtstbijzijnde dealer.';

  @override
  String get t_trouble27 => '[Kan de naaldinrijger niet gebruiken]';

  @override
  String get t_trouble27_01 => '* Oorzaak 1\nNaald staat in onjuiste stand.\n\n* Oplossing\nDruk op de \"Naaldstand\"-toets om de naald omhoog te zetten.';

  @override
  String get t_trouble27_02 => '* Oorzaak 2\nInrijghaak gaat niet door het oog van de naald.\n\n* Oplossing\nDruk op de \"Naaldstand\"-toets om de naald omhoog te zetten.';

  @override
  String get t_trouble27_03 => '* Oorzaak 3\nNaald is onjuist geplaatst.\n\n* Oplossing\nPlaats de naald nu op de juiste wijze.';

  @override
  String get t_trouble28 => '[Draadspanning is onjuist]';

  @override
  String get t_trouble28_01 => '* Oorzaak 1\nDe bovendraad is onjuist ingeregen.\n\n* Oplossing\nRaadpleeg de stappen voor het inrijgen van de naaimachine en rijg de machine correct in.';

  @override
  String get t_trouble28_02 => '* Oorzaak 2\nSpoel is onjuist geplaatst.\n\n* Oplossing\nPlaats de spoel opnieuw.';

  @override
  String get t_trouble28_03 => '* Oorzaak 3\nOnjuiste naald of onjuiste draad voor gekozen stof.\n\n* Oplossing\nRaadpleeg \"Overzichtsschema stoffen/draad/naald\".';

  @override
  String get t_trouble28_04 => '* Oorzaak 4\nPersvoethouder is onjuist bevestigd.\n\n* Oplossing\nBevestig de persvoethouder op de juiste wijze.';

  @override
  String get t_trouble28_05 => '* Oorzaak 5\nDraadspanning is onjuist ingesteld.\n\n* Oplossing\nPas de draadspanning aan.';

  @override
  String get t_trouble29 => '[Letterpatroon valt verkeerd uit]';

  @override
  String get t_trouble29_01 => '* Oorzaak 1\nU hebt niet de juiste persvoet gebruikt.\n\n* Oplossing\nBevestig de juiste persvoet.';

  @override
  String get t_trouble29_02 => '* Oorzaak 2\nPatrooninstellingen waren onjuist.\n\n* Oplossing\nWijzig de patrooninstellingen.';

  @override
  String get t_trouble29_03 => '* Oorzaak 3\nGeen steunstof gebruikt op dunne stof of stretchstof.\n\n* Oplossing\nBevestig een steunstof.';

  @override
  String get t_trouble29_04 => '* Oorzaak 4\nDraadspanning is onjuist ingesteld.\n\n* Oplossing\nPas de draadspanning aan.';

  @override
  String get t_trouble30 => '[Borduurpatroon wordt niet goed genaaid]';

  @override
  String get t_trouble30_01 => '* Oorzaak 1\nDraad is gedraaid.\n\n* Oplossing\nKnip bijvoorbeeld met een schaar de gedraaide draad af en haal deze uit de grijper enz.';

  @override
  String get t_trouble30_02 => '* Oorzaak 2\nStof is niet juist gespannen in het raam (stof te los enz.) .\n\n* Oplossing\nAls de stof niet strak wordt getrokken in het raam, kan het patroon niet goed uitvallen of wordt het vervormd. Plaats de stof op de juiste wijze in het raam.';

  @override
  String get t_trouble30_03 => '* Oorzaak 3\nGeen steunstof bevestigd.\n\n* Oplossing\nGebruik altijd steunstof, vooral bij stretchstoffen, dunne stoffen, grof geweven stoffen of stoffen waarbij het patroon kan vervormen. Neem contact op met de dichtstbijzijnde dealer voor de juiste steunstof.';

  @override
  String get t_trouble30_04 => '* Oorzaak 4\nEr stond een voorwerp bij de naaimachine en de wagen van het borduurraam heeft het voorwerp tijdens het naaien geraakt.\n\n* Oplossing\nAls het raam ergens tegenaan komt tijdens het naaien, wordt het patroon niet goed genaaid. Leg niets neer waar het raam tegenaan kan botsen tijdens het naaien.';

  @override
  String get t_trouble30_05 => '* Oorzaak 5\nStof buiten de raamrand belemmert de naaiarm, waardoor de borduureenheid niet kan bewegen.\n\n* Oplossing\nPlaats de stof zo in het borduurraam dat de overtollige stof niet in de buurt van de naaiarm komt en draai het patroon 180 graden.';

  @override
  String get t_trouble30_06 => '* Oorzaak 6\nStof is te zwaar, waardoor de borduureenheid niet vrij kan bewegen.\n\n* Oplossing\nLeg een groot, dik boek of dergelijk voorwerp onder de bovenkant van de arm om de zware kant enigszins omhoog te tillen, waardoor de tafel gelijk komt te staan.';

  @override
  String get t_trouble30_07 => '* Oorzaak 7\nStof hangt van de tafel af.\n\n* Oplossing\nAls de stof van de tafel hangt tijdens het borduren, kan de borduureenheid niet vrij bewegen. Leg de stof zo neer dat deze niet van de tafel hangt (of houd de stof vast om te voorkomen dat deze gaat slepen) .';

  @override
  String get t_trouble30_08 => '* Oorzaak 8\nStof zit vast of is ergens aan blijven haken.\n\n* Oplossing\nStop de naaimachine en leg de stof zo neer dat deze niet vast komt te zitten of blijft haken.';

  @override
  String get t_trouble30_09 => '* Oorzaak 9\nBorduurraam is verwijderd tijdens het naaien (bijvoorbeeld om de spoel opnieuw te plaatsen) . Er is tegen de persvoet aangestoten of hij is verplaatst terwijl het borduurraam werd verwijderd of aangebracht of de borduureenheid is verplaatst.\n\n* Oplossing\nAls u tegen de persvoet stoot of de borduureenheid beweegt tijdens het naaien, valt het patroon niet goed uit. Wees voorzichtig wanneer u het borduurraam verwijdert of opnieuw bevestigt tijdens het naaien.';

  @override
  String get t_trouble31 => '[Naald breekt]';

  @override
  String get t_trouble31_01 => '* Oorzaak 1\nNaald is onjuist geplaatst.\n\n* Oplossing\nPlaats de naald opnieuw op de juiste wijze.';

  @override
  String get t_trouble31_02 => '* Oorzaak 2\nNaaldklemschroef is niet vastgedraaid.\n\n* Oplossing\nDraai de naaldklemschroef vast.';

  @override
  String get t_trouble31_03 => '* Oorzaak 3\nNaald is verbogen of gedraaid.\n\n* Oplossing\nVervang de naald.';

  @override
  String get t_trouble31_04 => '* Oorzaak 4\nOngeschikte naald of draad voor gekozen stof.\n\n* Oplossing\nRaadpleeg \"Overzichtsschema stoffen/draad/naald\".';

  @override
  String get t_trouble31_05 => '* Oorzaak 5\nU hebt niet de juiste persvoet gebruikt.\n\n* Oplossing\nGebruik de aanbevolen persvoet.';

  @override
  String get t_trouble31_06 => '* Oorzaak 6\nBovendraadspanning is te hoog.\n\n* Oplossing\nPas de draadspanning aan.';

  @override
  String get t_trouble31_07 => '* Oorzaak 7\nEr is tijdens het naaien aan de stof getrokken.\n\n* Oplossing\nTrek niet aan de stof tijdens het naaien.';

  @override
  String get t_trouble31_08 => '* Oorzaak 8\nExtra klospenhouder is niet juist aangebracht.\n\n* Oplossing\nRaadpleeg de methode voor het bevestigen van de extra klospenhouder en bevestig de extra klospenhouder opnieuw.';

  @override
  String get t_trouble31_09 => '* Oorzaak 9\nEr zitten krassen rondom de openingen in de steekplaat.\n\n * Oplossing\nVervang de steekplaat of neem contact op met de dichtstbijzijnde dealer.';

  @override
  String get t_trouble31_10 => '* Oorzaak 10\nEr zitten krassen rondom de opening(en) in de persvoet.\n\n* Oplossing\nVervang de persvoet of neem contact op met de dichtstbijzijnde dealer.';

  @override
  String get t_trouble31_11 => '* Oorzaak 11\nEr zitten krassen op het spoelhuis.\n\n* Oplossing\nVervang de grijper of neem contact op met de dichtstbijzijnde dealer.';

  @override
  String get t_trouble31_12 => '* Oorzaak 12\nNaald is defect.\n\n* Oplossing\nVervang de naald.';

  @override
  String get t_trouble32 => '[Stof wordt niet door de machine heen gevoerd]';

  @override
  String get t_trouble32_01 => '* Oorzaak 1\nTransporteur staat omlaag.\n\n* Oplossing\nDruk op de vrij-modustoets en draai het handwiel, om de transporteurstandschakelaar omhoog te zetten.';

  @override
  String get t_trouble32_02 => '* Oorzaak 2\nSteken zitten te dicht op elkaar.\n\n* Oplossing\nVerhoog de instelling voor de steeklengte.';

  @override
  String get t_trouble32_03 => '* Oorzaak 3\nU hebt niet de juiste persvoet gebruikt.\n\n* Oplossing\nGebruik de juiste persvoet.';

  @override
  String get t_trouble32_04 => '* Oorzaak 4\nNaald is verdraaid, verbogen of de punt is stomp.\n\n* Oplossing\nVervang de naald.';

  @override
  String get t_trouble32_05 => '* Oorzaak 5\nDraad is verstrikt.\n\n* Oplossing\nKnip de verstrikte draad af en verwijder deze uit de grijper.';

  @override
  String get t_trouble33 => '[Naaimachine werkt niet]';

  @override
  String get t_trouble33_01 => '* Oorzaak 1\nEr is geen patroon geselecteerd.\n\n* Oplossing\nKies een patroon.';

  @override
  String get t_trouble33_02 => '* Oorzaak 2\nU hebt niet op de \"Start/Stoptoets\" gedrukt.\n\n* Oplossing\nDruk op de \"Start/Stoptoets\".';

  @override
  String get t_trouble33_03 => '* Oorzaak 3\nDe hoofdschakelaar is niet aangezet.\n\n* Oplossing\nZet de naaimachine aan.';

  @override
  String get t_trouble33_04 => '* Oorzaak 4\nPersvoet staat niet omlaag.\n\n* Oplossing\nZet de persvoet omlaag.';

  @override
  String get t_trouble33_05 => '* Oorzaak 5\nU hebt op de \"Start/Stoptoets\" gedrukt terwijl het voetpedaal was aangesloten.\n\n* Oplossing\nVerwijder het voetpedaal of gebruik het voetpedaal om de naaimachine te bedienen.';

  @override
  String get t_trouble33_06 => '* Oorzaak 6\nSchuifknop voor snelheidsregeling is ingesteld om de zigzagsteekbreedte te regelen.\n\n* Oplossing\nGebruik het voetpedaal in plaats van de \"Start/Stop\"-toets om de naaimachine te bedienen.';

  @override
  String get t_trouble34 => '[borduureenheid werkt niet]';

  @override
  String get t_trouble34_01 => '* Oorzaak 1\nEr is geen patroon gekozen.\n\n* Oplossing\nKies een patroon.';

  @override
  String get t_trouble34_02 => '* Oorzaak 2\nDe hoofdschakelaar is niet aangezet.\n\n* Oplossing\nZet de naaimachine aan.';

  @override
  String get t_trouble34_03 => '* Oorzaak 3\nborduureenheid is niet juist bevestigd.\n\n* Oplossing\nBevestig de borduureenheid op de juiste manier.';

  @override
  String get t_trouble34_04 => '* Oorzaak 4\nBorduurraam is bevestigd voordat de tafel geïnitialiseerd was.\n\n* Oplossing\nVoer de initialisatieprocedure op de juiste wijze uit.';

  @override
  String get t_trouble35 => ' [De draad is verstrikt geraakt op de achterkant van de stof]';

  @override
  String get t_trouble35_01 => ' * Oorzaak 1\nHet contrast van de display is niet goed ingesteld.\n\n* Oplossing\nPas het contrast van de display aan.';

  @override
  String get t_maintenance36 => '[Grijper en spoelhuis reinigen]';

  @override
  String get t_maintenance36_00 => 'Met stofresten of vuil in de grijper of het spoelhuis functioneert de naaimachine niet goed. Bovendien kan de onderdraad dan misschien niet worden opgepakt. De beste resultaten krijgt u met een schone naaimachine.\nLees onderstaande stappen voordat u de machine uitschakelt.';

  @override
  String get t_maintenance36_01 => '\n1. Druk op de \"Naaldstand\"-toets om de naald omhoog te zetten.';

  @override
  String get t_maintenance36_02 => '2. Zet de persvoet omlaag.';

  @override
  String get t_maintenance36_03 => '(a) UIT\n(b) AAN\n\n3. Zet de hoofdschakelaar uit.';

  @override
  String get t_maintenance36_04 => '4. Verwijder de persvoethouder en de naald.';

  @override
  String get t_maintenance36_05_11 => '5. Verwijder de accessoirestafel of de borduureenheid als deze aan de machine bevestigd zijn.\nSchuif de steekplaatontgrendeling naar u toe.\nDe steekplaat gaat open.';

  @override
  String get t_maintenance36_05_12 => '(a) Schuif naar u toe.\n';

  @override
  String get t_maintenance36_05_13 => '6. Schuif de steekplaat met uw rechterhand eruit om de plaat te verwijderen.';

  @override
  String get t_maintenance36_05_14 => '(a) Steekplaatdeksel\n';

  @override
  String get t_maintenance36_05_15 => '\n7. Pak het spoelhuis vast en til het er uit.';

  @override
  String get t_maintenance36_07_02 => '(a) Spoelhuis';

  @override
  String get t_maintenance36_08 => '\n8. Verwijder met het schoonmaakborsteltje of een stofzuiger pluis en stof uit de grijper en daar omheen.\n\n* Breng geen olie aan op het spoelhuis.';

  @override
  String get t_maintenance36_08_02 => '(a) Schoonmaakborsteltje\n(b) Grijper';

  @override
  String get t_embbasic18_04_21 => '\n9. Plaats het spoelhuis zo dat de ▲-markering op het spoelhuis zich tegenover de ●-markering op de machine bevindt.';

  @override
  String get t_embbasic18_04_22 => '(a) ▲-markering op het spoelhuis\n(b) ●-markering op de machine';

  @override
  String get t_embbasic18_04_23 => '\n10. Steek de lipjes op de steekplaat in de inkepingen van de machine.';

  @override
  String get t_embbasic18_04_24 => '(a) Lipjes\n(b) Inkepingen';

  @override
  String get t_embbasic18_04_25 => '11. Druk op de rechterkant van de steekplaat om deze weer vast te zetten.';

  @override
  String get t_sewing01_00 => 'Naaien geselecteerde steektype';

  @override
  String get t_sewing01_00_01 => '1-01:Normaal naaien\n1-05:Verstevigingssteken naaien\n1-06:Stretchstoffen naaien';

  @override
  String get t_sewing01_00_01_s_normal => 'Normaal naaien';

  @override
  String get t_sewing01_00_01_s_reinforced => 'Verstevigingssteken naaien';

  @override
  String get t_sewing01_00_01_s_stretch => 'Stretchstoffen naaien';

  @override
  String get t_sewing01 => '[Rechte steken]';

  @override
  String get t_sewing01_01 => '\n1. Bevestig persvoet \"J\".\nHoud de uiteinden van de draden en de stof in uw linkerhand en draai het handwiel met uw rechterhand naar u toe om de naald in de stof te steken.';

  @override
  String get t_sewing01_01_02 => '(a) Startpositie om te naaien';

  @override
  String get t_sewing01_02 => '\n2. Zet de persvoet omlaag en houd de \"Achteruit/Verstevigingssteek\"-toets ingedrukt om drie à vier steken te naaien. De machine naait achteruit (of verstevigingssteken) .\nDruk op de \"Start/Stop\"-toets om vooruit te naaien. De naaimachine begint langzaam te naaien.';

  @override
  String get t_sewing01_03 => '(a) Achteruit genaaide steken';

  @override
  String get t_sewing01_04 => '3. Als u klaar bent met naaien, houdt u de \"Achteruit/Verstevigingssteek\"-toets ingedrukt om drie à vier achteruitsteken (of verstevigingssteken) aan het eind van de naad te naaien.';

  @override
  String get t_sewing01_05 => '4. Druk na het naaien op de \"Draadknip\"-toets om de draden af te knippen.\n\n* Zijn de \"Automatische draadknip\"-toets en de \"Automatische verstevigingssteek\"-toets op het scherm geselecteerd? Dan worden automatisch verstevigingssteken (of achteruitsteken) genaaid aan het begin van het naaiwerk wanneer u op de \"Start/Stop\"-toets drukt.\nDruk op de \"Achteruit/Verstevigingssteek\"-toets om verstevigingssteken (of achteruitsteken) te naaien en de draad automatisch af te knippen aan het eind van het naaiwerk.';

  @override
  String get t_sewing01_06 => '\n * De naaldstand wijzigen \n Wanneer u steken voor de linkernaaldstand of middelste naaldstand kiest, kunt u met de toetsen \"+\" en \"-\" in het scherm voor L/R-verschuiving de naaldstand wijzigen. Stem de afstand van de rechterhoek van de persvoet tot aan de naald af op de marge. Zet de rand van de persvoet tijdens het naaien op één lijn met de rand van de stof voor een aantrekkelijke afwerking.';

  @override
  String get t_sewing01_07 => '(a) Marge';

  @override
  String get t_sewing02 => '[Overhandse steken]';

  @override
  String get t_sewing02_00 => 'Naaien geselecteerde steektype';

  @override
  String get t_sewing02_00_01 => '1-16:Dunne en middelmatig dikke stof\n1-17:Dikke stof\n1-18:Middelmatig dikke stof, dikke stof en stof die snel rafelt\n1-19:Stretchstof\n1-20:Dikke en middelmatig dikke stretchstof\n1-21:Stretchstof';

  @override
  String get t_sewing02_00_01_f_lightandmedium => 'Dunne en middelmatig dikke stof';

  @override
  String get t_sewing02_00_01_f_heavy => 'Dikke stof';

  @override
  String get t_sewing02_00_01_f_mediumstretch => 'Middelmatig dikke stof, dikke stof en stof die snel rafelt';

  @override
  String get t_sewing02_00_01_f_stretch1 => 'Stretchstof';

  @override
  String get t_sewing02_00_01_f_thickandmediumstretch => 'Dikke en middelmatig dikke stretchstof';

  @override
  String get t_sewing02_00_01_f_stretch2 => 'Stretchstof';

  @override
  String get t_sewing02_01 => '1. Bevestig persvoet \"G\". Zet de persvoet zo omlaag dat de persvoetgeleider precies tegen de rand van de stof komt.';

  @override
  String get t_sewing02_02 => '\n2. Naai langs de persvoetgeleider.';

  @override
  String get t_sewing02_02_02 => '(a) Geleider';

  @override
  String get t_sewing02_03 => '\n1. Bevestig persvoet \"J\". Zorg dat de naald tijdens het naaien net naast de stofrand valt.';

  @override
  String get t_sewing02_04 => '(a) Naaldpositie';

  @override
  String get t_sewing02_05 => '\n* Na het instellen van de steekbreedte draait u het handwiel handmatig naar u toe om te controleren of de naald de persvoet niet raakt. Als de naald de persvoet raakt, kan de naald breken en letsel veroorzaken.';

  @override
  String get t_sewing02_05_02 => '(a) De naald mag de persvoet niet raken';

  @override
  String get t_sewing04 => '[Schelpsteken]';

  @override
  String get t_sewing04_01 => 'Dit golfvormige patroon voor satijnsteken wordt het schelpsteekpatroon genoemd. Met dit steekpatroon versiert u randen van kragen van blouses en zakdoeken of geeft u een zoom een vrolijke afwerking.\nVoor lichte stoffen moet u soms textiellijm aanbrengen. Naai een proeflapje met de stof voordat u aan het echte project begint.';

  @override
  String get t_sewing04_02 => '1. Bevestig persvoet \"N+\". Naai schelpsteken langs de rand van de stof. Naai niet direct op de rand van de stof.';

  @override
  String get t_sewing04_03 => '2. Knip de stof langs de schelprand af. Pas op dat u niet in het stiksel knipt.';

  @override
  String get t_sewing05_00 => 'Naaien geselecteerde steektype';

  @override
  String get t_sewing05_00_01 => '4-01:Dunne en middelmatig dikke stof (voor horizontale gaten)\n4-07:Dunne of middelmatig dikke stof\n4-10:Grof geweven stretchstof\n4-11:Stretchstof\n4-13:Pakken, overjassen\n4-14:Jeans, broeken\n4-15:Dikke jassen';

  @override
  String get t_sewing05_00_01_f_lighttomediumhorizhole => 'Dunne en middelmatig dikke stof (voor horizontale gaten)';

  @override
  String get t_sewing05_00_01_f_lighttomedium => 'Dunne of middelmatig dikke stof';

  @override
  String get t_sewing05_00_01_f_stretchweaves => 'Grof geweven stretchstof';

  @override
  String get t_sewing05_00_01_f_stretch => 'Stretchstof';

  @override
  String get t_sewing05_00_01_f_suitsandovercoat => 'Pakken, overjassen';

  @override
  String get t_sewing05_00_01_f_jeansandtrousers => 'Jeans, broeken';

  @override
  String get t_sewing05_00_01_f_thickcoats => 'Dikke jassen';

  @override
  String get t_sewing05 => '[Knoopsgaten]';

  @override
  String get t_sewing05_02 => '1. Markeer de positie en de lengte van het knoopsgat op de stof.';

  @override
  String get t_sewing05_03 => '\n2. Bevestig knoopsgatvoet \"A+\". Trek de knoophouderplaat op de persvoet uit en plaats de knoop waarvoor het knoopsgat is bestemd. Zorg dat de knoophouderplaat strak om de knoop zit.\n\n* Het formaat knoopsgat wordt bepaald door de grootte van de knoop in de knoophouderplaat.';

  @override
  String get t_sewing05_04 => '(a) Knoophouderplaat';

  @override
  String get t_sewing05_05 => '\n3. Zet de markering op de persvoet op één lijn met de markering op de stof en zet de persvoethendel omlaag.';

  @override
  String get t_sewing05_06 => '(a) Markering op de stof\n(b) Markering op de persvoet';

  @override
  String get t_sewing05_07 => '\n4. Zet de knoopsgathendel omlaag, zodat deze achter het metalen uitsteeksel op de knoopsgatvoet terechtkomt.';

  @override
  String get t_sewing05_08 => '(a) Metalen uitsteeksel';

  @override
  String get t_sewing05_09 => '4. Houd het uiteinde van de bovendraad losjes vast en begin met naaien. Voer de stof langzaam met de hand door, terwijl het knoopsgat wordt genaaid.\nWanneer u klaar bent met naaien, naait de naaimachine automatisch verstevigingssteken en stopt daarna.';

  @override
  String get t_sewing05_10 => '\n5. Steek een speld ter hoogte van een van de trenzen. Steek het tornmesje daarna in het midden van het knoopsgat en snijd het knoopsgat open naar de speld toe.';

  @override
  String get t_sewing05_11 => '(a) Rijgspeld\n(b) Tornmesje';

  @override
  String get t_sewing05_12 => '\n[Bij lingerieknoopsgaten]\nMaak een gaatje met de gaatjesponser in het afgeronde uiteinde van het knoopsgat. Steek vervolgens een speld ter hoogte van een van de trenzen. Steek een tornmesje in het gaatje dat u met de gaatjesponser hebt gemaakt en snijd het knoopsgat open naar de speld toe.';

  @override
  String get t_sewing05_13 => '(a) Gaatjesponser\n(b) Rijgspeld';

  @override
  String get t_sewing05_14 => '\n* Stretchstoffen naaien\nWanneer u stretchstof naait met 4-10 of 4-11, naait u de knoopsgatsteken over een contourdraad.';

  @override
  String get t_sewing05_16 => '\n1. Haak de contourdraad in het uiteinde van persvoet \"A+\". Steek de uiteinden in de gleuven aan de voorkant van de persvoet en bind ze daar tijdelijk vast.';

  @override
  String get t_sewing05_17 => '(a) Bovendraad';

  @override
  String get t_sewing05_18 => '2. Zet de persvoet omlaag en begin met naaien.';

  @override
  String get t_sewing05_19 => '3. Als u klaar bent met naaien trekt u de contourdraad voorzichtig strak. Knip het overtollige gedeelte af.';

  @override
  String get t_sewing05_20 => '\n* Knopen die niet in de knopenvoet passen (knopen met aparte vormen) \nStel de grootte van het knoopsgat in met behulp van de streepjes op de schaalverdeling van de persvoet. Eén streepje op de schaalverdeling staat gelijk aan 5 mm (ca. 3/16 inch) . Tel de doorsnede op bij de dikte van de knoop en stel de voet in op de berekende waarde.';

  @override
  String get t_sewing05_21 => '(a) Persvoetschaal\n(b) Knopenvoet\n(c) Gemeten doorsnede + dikte\n(d) 5 mm (ca. 3/16 inch) ';

  @override
  String get t_sewing05_22 => '\nVoorbeeld:\nVoor een knoopsgat met een doorsnede van 15 mm (ca.9/16 inch) en een dikte van 10 mm (ca.3/8 inch) stelt u de schaalverdeling in op 25 mm (ca.1 inch) .';

  @override
  String get t_sewing05_23 => '(a) 10 mm (ca. 3/8 inch) \n(b) 15 mm (ca. 9/16 inch) ';

  @override
  String get t_sewing06 => '[Knopen aanzetten]';

  @override
  String get t_sewing06_01 => 'Gebruik de functie Automatisch draadknippen niet wanneer u knopen aanzet. Anders verliest u de draaduiteinden. \n\n1. Bevestig knoopbevestigingsvoet \"M\". Schuif de knoop langs de metalen plaat in de persvoet en zet de persvoet omlaag.';

  @override
  String get t_sewing06_01_02 => '(a) Knoop\n(b) Metalen plaat\n';

  @override
  String get t_sewing06_02 => '2. Draai het handwiel om te controleren of de naald op de juiste manier door ieder gat gaat. Houd vervolgens het uiteinde van de bovendraad losjes vast en begin met naaien. De naaimachine stopt automatisch wanneer het naaiwerk voltooid is.';

  @override
  String get t_sewing06_03 => '3. Trek aan de achterkant van de stof aan het uiteinde van de onderdraad om de bovendraad naar de achterkant van de stof te trekken. Knoop de twee draden aan elkaar en knip ze af.';

  @override
  String get t_sewing06_04 => '\n* 4-gatsknoop bevestigen';

  @override
  String get t_sewing06_05 => 'Naai de twee dichtstbijzijnde gaten. Zet de persvoethendel omhoog en verplaats de stof zo dat de naald in de twee volgende gaten gaat. Naai deze gaten op dezelfde wijze.';

  @override
  String get t_sewing06_06 => '* Knoopvoet bevestigen\n\n1. Trek de knoopvoethendel naar u toe voordat u begint met naaien.';

  @override
  String get t_sewing06_07 => '(a) Knoopvoethendel\n';

  @override
  String get t_sewing06_08 => '2. Houd de uiteinden van de bovendraad tussen de knoop en de stof vast en wind ze om de knoopvoet heen. Knoop ze vervolgens stevig aan elkaar.\nKnoop de uiteinden van de onderdraad aan het begin en het eind van het naaiwerk aan de achterkant van de stof aan elkaar.';

  @override
  String get t_sewing07 => '[Trenssteken]';

  @override
  String get t_sewing07_01 => 'Met trenssteken verstevigt u stukken die zwaar belast zullen worden, zoals de hoeken van zakken.';

  @override
  String get t_sewing07_02 => '\n1. Bevestig knoopsgatvoet \"A+\" en stel de schaalverdeling in op de lengte van de trenssteek die u wilt naaien.';

  @override
  String get t_sewing07_03 => '(a) Persvoetschaal\n(b) Gemeten lengte\n(c) 5 mm (ca. 3/16 inch) ';

  @override
  String get t_sewing07_04 => '2. Plaats de stof zo dat de zak naar u toe beweegt tijdens het naaien.';

  @override
  String get t_sewing07_05 => '\n3. Controleer de eerste naaldpositie en zet de persvoet omlaag.';

  @override
  String get t_sewing07_06 => '(a) 2 mm (ca. 1/16 inch) ';

  @override
  String get t_sewing07_09 => '4. Houd het uiteinde van de bovendraad losjes vast en begin met naaien. Wanneer u kaar bent met naaien, naait de naaimachine automatisch verstevigingssteken en stopt daarna.';

  @override
  String get t_sewing07_10 => '\n* Trenssteken op dikke stof\nLeg een stuk gevouwen stof of karton naast de stof om de knoopsgatvoet op gelijke hoogte te krijgen en het doorvoeren te vergemakkelijken.';

  @override
  String get t_sewing07_11 => '(a) Persvoet\n(b) Dik papier\n(c) Stof';

  @override
  String get t_sewing08 => '[Rits inzetten]';

  @override
  String get t_sewing08_00 => '\n* Rits in het midden\nVoor tassen en andere vergelijkbare toepassingen.\n\n1. Bevestig persvoet \"J\" en naai rechte steken tot aan de ritsopening. Schakel over op rijgsteken en naai naar de bovenrand van de stof.';

  @override
  String get t_sewing08_02 => '(a) Rijgsteken\n(b) Achteruit genaaide steken\n(c) Einde ritsopening\n(d) Achterkant';

  @override
  String get t_sewing08_03 => '\n2. Druk de marge tot aan de naad open en bevestig de rits met rijgsteken in het midden van elke kant van de ritsband.';

  @override
  String get t_sewing08_04 => '(a) Rijgsteken\n(b) Rits\n(c) Achterkant';

  @override
  String get t_sewing08_05 => '\n3. Verwijder persvoet \"J\". Plaats het rechteruiteinde van de pen van ritsvoet \"I\" in de persvoethouder en bevestig de ritsvoet.';

  @override
  String get t_sewing08_06 => '(a) Rechts\n(b) Links\n(c) Naaldpositie';

  @override
  String get t_sewing08_07 => '4. Naai 7 tot 10 mm (ca. 1/4 tot 3/8 inch) vanaf de rand van de stof en verwijder de rijgdraad daarna.';

  @override
  String get t_sewing08_08 => '\n* Zijrits inzetten\nVoor zijritsen in rokken of jurken.\n\n1.  Bevestig persvoet \"J\" en naai rechte steken tot aan de ritsopening. Schakel over op rijgsteken en naai naar de bovenrand van de stof.';

  @override
  String get t_sewing08_11 => '(a) Achteruit genaaide steken\n(b) Achterkant van de stof\n(c) Rijgsteken\n(d) Einde ritsopening';

  @override
  String get t_sewing08_12 => '\n2. Druk de marge tot aan de rand open en plaats de binnenrand van het kledingstuk langs de tanden van de rits. Laat hierbij 3 mm (ca. 1/8 inch) vrij om te naaien.';

  @override
  String get t_sewing08_13 => '(a) Trekker of top van de rits\n(b) Achterkant van de stof\n(c) Ritstanden\n(d) Einde ritsopening\n(e) 3 mm (1/8 inch) ';

  @override
  String get t_sewing08_14 => '\n3. Verwijder persvoet \"J\". Plaats het rechteruiteinde van de pen van ritsvoet \"I\" in de persvoethouder en bevestig de persvoet.';

  @override
  String get t_sewing08_15 => '(a) Rechts\n(b) Links\n(c) Naaldpositie';

  @override
  String get t_sewing08_16 => '\n4. Stel de persvoet af op een marge van 3 mm (1/8 inch) . Begin bij het eind van de ritsopening. Naai tot ongeveer 50 mm (ca. 2 inch) van de rand van de stof en zet de naaimachine stil. Trek de trekker van de rits voorbij de persvoet omlaag en ga door met naaien tot aan de rand van de stof.';

  @override
  String get t_sewing08_17 => '(a) 50 mm (ca. 2 inch) \n(b) 3 mm (ca. 1/8 inch) ';

  @override
  String get t_sewing08_18 => '\n5. Sluit de rits, keer de stof om en naai een rijgsteek.';

  @override
  String get t_sewing08_19 => '(a) Voorkant van de rok (achterkant van de stof) \n(b) Rijgsteken\n(c) Voorkant van de rok (voorkant van de stof) \n(d) Achterkant van de rok (voorkant van de stof) ';

  @override
  String get t_sewing08_20 => '\n6. Verwijder de persvoet en bevestig deze opnieuw, maar nu met het linkeruiteinde van de persvoet in de persvoethouder.\n\n* Wanneer u de linkerkant van de rits naait, moet de naald rechts van de persvoet vallen. Wanneer u de rechterkant van de rits naait, moet de naald links van de persvoet vallen.';

  @override
  String get t_sewing08_21 => '(a) Rechts\n(b) Links\n(c) Naaldpositie';

  @override
  String get t_sewing08_22 => '\n7. Plaats de stof zo dat de linkerrand van de persvoet de rand van de tanden van de rits raakt. Naai achteruitsteken aan de bovenkant van de rits en ga daarna door met naaien. Stop ongeveer 50 mm (ca. 2 inch) van de rand van de stof met naaien. Laat de naald in de stof steken en verwijder de rijgsteken. Open de rits en naai de rest van de naad.';

  @override
  String get t_sewing08_23 => '(a) Rijgsteken\n(b) 7 tot 10 mm (ca.1/4 inch tot 3/8 inch) \n(c) Achteruit genaaide steken\n(d) 50 mm (ca. 2 inch) ';

  @override
  String get t_sewing09_00 => 'Naaien geselecteerde steektype';

  @override
  String get t_sewing09_00_01 => 'Kies uit deze steken voor het naaien van zomen bij manchetten of jurken, blouses, broeken of rokken.';

  @override
  String get t_sewing09_00_02 => '2-01:Andere stoffen\n2-02:Stretchstoffen';

  @override
  String get t_sewing09_00_02_f_other => 'Andere stoffen';

  @override
  String get t_sewing09_00_02_f_stretch => 'Stretchstoffen';

  @override
  String get t_sewing09 => '[Blindzoomsteken]';

  @override
  String get t_sewing09_01 => '\n1. Plaats de stof met de achterkant omhoog. Vouw en rijg de stof.';

  @override
  String get t_sewing09_02 => '(a) 5 mm (ca. 3/16 cm) \n(b) Rijgsteken\n(c) Achterkant van de stof';

  @override
  String get t_sewing09_03 => '\n2. Bevestig blindzoomsteekvoet \"R\" en zet de persvoet omlaag. Plaats de stof zo dat de gevouwen rand de geleider van de persvoet raakt.';

  @override
  String get t_sewing09_04 => '(a) Geleider\n(b) Vouw';

  @override
  String get t_sewing09_05 => '\n3. Naai de stof en houd de gevouwen rand in contact met de persvoet.';

  @override
  String get t_sewing09_06 => '(a) Naaldpositie';

  @override
  String get t_sewing09_07 => '\n4. Verwijder de rijgsteken en keer de stof om.';

  @override
  String get t_sewing09_08 => '(a) Achterkant van de stof\n(b) Voorkant van de stof';

  @override
  String get t_sewing10 => '[Appliceren]';

  @override
  String get t_sewing10_01 => '\n1. Zet de applicatie met textiellijm of rijgsteken aan de stof vast. Zo zal de stof tijdens het naaien niet gaan schuiven.';

  @override
  String get t_sewing10_02 => '(a) Applicatie\n(b) Textiellijm';

  @override
  String get t_sewing10_03 => '\n2. Bevestig persvoet \"J\". Controleer of de naald net naast de rand van de applicatie valt en begin met naaien.';

  @override
  String get t_sewing10_04 => '(a) Applicatiestof\n(b) Naaldpositie';

  @override
  String get t_sewing10_06 => '* Scherpe bochten naaien\n';

  @override
  String get t_sewing10_07 => 'Stop de naaimachine wanneer de naald in de stof buiten de applicatie steekt. Zet de persvoet omhoog en draai de stof een beetje tijdens het naaien. Zo krijgt de naad een aantrekkelijke afwerking.';

  @override
  String get t_sewing11 => '[Gepaspelde naad]';

  @override
  String get t_sewing11_01 => '\n1. Breng langs de vouwen op de achterkant van de stof een markering aan.';

  @override
  String get t_sewing11_01_02 => '(a) Achterkant';

  @override
  String get t_sewing11_02 => '\n2. Draai de stof om en strijk alleen de gevouwen gedeelten.';

  @override
  String get t_sewing11_02_02 => '(a) Voorkant';

  @override
  String get t_sewing11_03 => '\n3. Bevestig persvoet \"I\".\n Naai een rechte steek langs de vouw.';

  @override
  String get t_sewing11_04_02 => '(a) Breedte voor gepaspelde naad\n(b) Achterkant\n(c) Voorkant';

  @override
  String get t_sewing11_05 => '4. Strijk de vouwen in dezelfde richting.';

  @override
  String get t_sewing12 => '[Plooien]';

  @override
  String get t_sewing12_00 => '\nVoor tailles van jurken, mouwen van blouses, enz.';

  @override
  String get t_sewing12_01 => '\n1. Trek de spoel en bovendraden ongeveer 50 mm (ca.1-15/16 inch) uit.';

  @override
  String get t_sewing12_01_02 => '(a) Bovendraad\n(b) Onderdraad\n(c) Ongeveer 50 mm (ca.1-15/16 inch) ';

  @override
  String get t_sewing12_02 => '\n2. Naai twee rijen rechte steken parallel aan de naadlijn. Knip vervolgens de overtollige draad af. Laat ongeveer 50 mm draad (ca.1-15/16 inch) .';

  @override
  String get t_sewing12_02_02 => '(a) Naadlijn\n(b) 10 tot 15 mm (ca. 3/8 inch tot 9/16 inch) \n(c) Ongeveer 50 mm (ca.1-15/16 inch) ';

  @override
  String get t_sewing12_03 => '3. Trek de onderdraden uit tot u de gewenste plooi heeft en knoop de draden vast.';

  @override
  String get t_sewing12_04 => '4. Strijk de plooien.\n';

  @override
  String get t_sewing12_05 => '5. Naai op de naadlijn en verwijder de rijgsteek.';

  @override
  String get t_sewing13 => '[Figuurnaad]';

  @override
  String get t_sewing13_01 => '\n1. Naai een steek achteruit aan het begin van de figuurnaad. Naar vervolgens van het brede eind naar het andere, zonder de stof uit te rekken.';

  @override
  String get t_sewing13_01_02 => '(a) Rijgsteken\n(b) Voorkant\n(c) Achterkant';

  @override
  String get t_sewing13_02 => '2. Knip de draad aan het eind af. Laat hierbij 50 mm (ca.1-15/16 inch) over en knoop deze einden aan elkaar.\n\n* Naai geen steek achteruit aan het eind.';

  @override
  String get t_sewing13_03 => '3. Steek het eind van de draad met een handnaald in de figuurnaad.';

  @override
  String get t_sewing13_04 => '4. Strijk de figuurnaad naar één kant, zodat deze vlak wordt.';

  @override
  String get t_sewing14 => '[Engelse naad]';

  @override
  String get t_sewing14_00 => '\nOm naden te verstevigen en randen netjes af te werken.\n\n1. Naai de afwerkingslijn. Knip de helft van de marge vanaf de kant waar de Engelse naad moet komen.';

  @override
  String get t_sewing14_01_02 => '(a) Achterkant\n(b) Ongeveer 12 mm (ca. 1/2 inch) ';

  @override
  String get t_sewing14_02 => '\n2. Spreid de stof uit langs de afwerkingslijn.';

  @override
  String get t_sewing14_02_02 => '(a) Achterkant\n(b) Afwerkingslijn';

  @override
  String get t_sewing14_03 => '\n3. Leg beide marges aan de kant van de smallere naad (geknipte naad) en strijk ze.';

  @override
  String get t_sewing14_03_02 => '(a) Achterkant';

  @override
  String get t_sewing14_04 => '\n4. Vouw de bredere marge rond de smallere en naai de rand van de vouw.';

  @override
  String get t_sewing14_04_01 => '(a) Achterkant';

  @override
  String get t_sewing15_00 => 'Steekkeuze';

  @override
  String get t_sewing15_00_01 => 'Q-01: Verbindingssteek (midden) \nQ-02: Verbindingssteek (rechts) \nQ-03: Verbindingssteek (links) ';

  @override
  String get t_sewing15_00_01_s_piecingmiddle => 'Verbindingssteek (midden) ';

  @override
  String get t_sewing15_00_01_s_piecingright => 'Verbindingssteek (rechts) ';

  @override
  String get t_sewing15_00_01_s_piecingleft => 'Verbindingssteek (links) ';

  @override
  String get t_sewing15 => '[Stukken stof aan elkaar zetten]';

  @override
  String get t_sewing15_01 => '(a) Marge: 6,5mm (ca. 1/4 inch)\n       (wanneer Q-02 is geselecteerd)\n(b) Op één lijn met de rechterkant van de persvoet.\n\n1. Bevestig persvoet \"J\".\nNaai met de rand van de stof op één lijn met de rechterkant van de persvoet.';

  @override
  String get t_sewing15_012 => '(a) Marge: 7mm\n       (wanneer Q-02 is geselecteerd)\n(b) Op één lijn met de rechterkant van de persvoet.\n\n1. Bevestig persvoet \"J\".\nNaai met de rand van de stof op één lijn met de rechterkant van de persvoet.';

  @override
  String get t_sewing15_01_02 => '(a) Marge: 6,5mm (ca. 1/4 inch)\n       (wanneer Q-03 is geselecteerd)\n(b) Op één lijn met de linkerkant van de persvoet.\n\n1. Bevestig persvoet \"J\".\nNaai met de rand van de stof op één lijn met de rechterkant van de persvoet.';

  @override
  String get t_sewing15_01_022 => '(a) Marge: 7mm\n       (wanneer Q-03 is geselecteerd)\n(b) Op één lijn met de linkerkant van de persvoet.\n\n1. Bevestig persvoet \"J\".\nNaai met de rand van de stof op één lijn met de rechterkant van de persvoet.';

  @override
  String get t_sewing15_02 => '(a) Geleider\n\nMet deze 1/4\" quiltvoet met geleider kunt u een accurate marge van 1/4 inch of 1/8 inch naaien. Hiermee kunt u een quilt aan elkaar zetten of afwerksteken naaien.\n\n1. Selecteer Q-01 en bevestig de 1/4\" quiltvoet met geleider.';

  @override
  String get t_sewing15_03 => '(a) Geleider\n(b) 1/4 inch\n\n2. Met behulp van de geleider op de persvoet en de markeringen naait u een accurate marge.\n\n\"Stukken stof aan elkaar zetten met een marge van 1/4 inch\"\nNaai terwijl u de rand van de stukken stof tegen de geleider houdt.';

  @override
  String get t_sewing15_04 => '(a) Om te beginnen zorgt u dat deze markering op één lijn staat met de rand van de stof\n(b) Begin van het stiksel\n(c) Eind van het stiksel\n(d) Rand van stof tegenover eind of spil\n(e) 1/4 inch\n\n\"Een accurate marge creëren\"\nMet de markering op de voet kunt u 1/4 inch van de rand van de stof beginnen, eindigen of draaien.';

  @override
  String get t_sewing15_05 => '(a) Voorkant van de stof\n(b) Naad\n(c) 1/8 inch\n\n\"Quilten met afwerksteken, 1/8 inch\"\nNaai met de rand van de stof op een lijn met de linkerkant van het persvoetuiteinde.';

  @override
  String get t_sewing16 => '[Quilten]';

  @override
  String get t_sewing16_01 => '1. Verwijder de persvoet; draai de schroef van de persvoethouder los om de persvoethouder te verwijderen.\n\nPlaats de adapter op de persvoetstang en lijn de vlakke kant van de adapteropening uit met de vlakke zijde van de persvoetstang. Duw deze zover mogelijk omhoog en draai de schroef stevig vast met de schroevendraaier.';

  @override
  String get t_sewing16_02 => '(a) Bedieningshendel\n(b) Naaldklemschroef\n(c) Vork\n(d) Persvoetstang\n\n2. Stel de bedieningshendel van de boventransportvoet zo af dat de vork om de naaldklem komt te zitten en zet de boventransportvoet op de persvoetstang.';

  @override
  String get t_sewing16_03 => '3. Zet de persvoethendel omlaag. Draai de schroef stevig vast met de schroevendraaier.';

  @override
  String get t_sewing16_04 => '4. Plaats een hand aan beide kanten van de persvoet om de stof stevig vast te houden.';

  @override
  String get t_sewing16_05 => '* Als \"AUTOMATISCHE STOFSENSOR\" op \"ON\" staat in het scherm voor machine-instellingen, kunt u de stof vloeiend doorvoeren. Dat geeft een mooi resultaat.';

  @override
  String get t_sewing17 => '[Vrij quilten]';

  @override
  String get t_sewing17_00 => '(a) Vrije quiltvoet \"C\"\n(b) Vrije open quiltvoet \"O\"\n\nVoor vrij quilten gebruikt u de vrije quiltvoet \"C\" en de vrije open quiltvoet \"O\" naargelang het steekpatroon dat u hebt geselecteerd.';

  @override
  String get t_sewing17_01 => '1. Druk op de transporteur-omlaagtoets om de machine in vrij-modus te zetten.';

  @override
  String get t_sewing17_02_01 => '(a) Persvoethouderschroef\n(b) Inkeping\n\n2. Bevestig vrije quiltvoet \"C\" aan de voorkant met de persvoethouderschroef op één lijn met de inkeping in de voet.\nDraai vervolgens de persvoethouderschroef vast.';

  @override
  String get t_sewing17_02_02 => '(a) Pen\n(b) Naaldklemschroef\n(c) Persvoetstang\n\nBevestig de vrije open quiltvoet \"O\" door de pen van de voet boven de naaldklemschroef te houden met het linker benedenstuk van de voet op één lijn met de persvoetstang.\nDraai vervolgens de persvoethouderschroef vast.';

  @override
  String get t_sewing17_03 => '(a) Steek\n\n3. Span de stof met beide handen. Voer de stof gelijkmatig door zodat u uniforme steken naait van ongeveer 2,0 - 2,5 mm (ca. 1/16 - 3/32 inch).\n\n* Wij adviseren u het voetpedaal aan te sluiten en op gelijkmatige snelheid te naaien.';

  @override
  String get t_sewing18 => '[Echoquilten]';

  @override
  String get t_sewing18_00 => '(a) 6,4mm (ca. 1/4 inch)\n(b) 9,5mm (ca. 3/8 inch)\n\nVrije echoquiltvoet \"E\".';

  @override
  String get t_sewing18_01 => '2. Verwijder de persvoet; draai de schroef van de persvoethouder los en verwijder de schroef en de persvoethouder.\n\nPlaats de adapter op de persvoetstang en lijn de vlakke kant van de adapteropening uit met de vlakke zijde van de persvoetstang. Duw deze zover mogelijk omhoog en draai de schroef stevig vast met de schroevendraaier.';

  @override
  String get t_sewing18_02 => '3. Plaats de vrije echoquiltvoet \"E\" aan de linkerkant van de persvoetstang, met de gaten in de echovoet en de persvoetstang op één lijn.\n\nDraai het meegeleverde schroefje 2 of 3 keer met de hand aan.';

  @override
  String get t_sewing18_03 => '4. Draai de schroef vast.';

  @override
  String get t_sewing18_04 => '(a) 6,4mm (ca. 1/4 inch)\n\n5. Naai rond het motief met de maatverdeling op de echovoet als geleider.';

  @override
  String get t_sewing18_05 => 'Voltooid project';

  @override
  String get t_sewing19 => '[Appliceren]';

  @override
  String get t_sewing19_01 => '(a) Marge: 3 - 5 mm\n\n1. Trek het patroon over op de applicatiestof en knip het daarom heen uit.';

  @override
  String get t_sewing19_02 => '2. Knip een stuk dik papier of steunstof ter grootte van het applicatieontwerp, plaats dit op de achterkant van de applicatie en vouw vervolgens de marge om met een strijkijzer.';

  @override
  String get t_sewing19_03 => '3. Draai de applicatie om en verwijder de steunstof of het papier. Speld of rijg de applicatie vast op de hoofdstof.';

  @override
  String get t_sewing19_04 => '(a) Naaldpositie\n\n4. Bevestig persvoet \"J\".\nControleer de naaldpositie (waar de naald neerkomt) en naai vervolgens langs de rand van de applicatie, terwijl de naald iets naast de rand van de stof neerkomt.';

  @override
  String get t_explain_use => '[Gebruik]';

  @override
  String get t_explain01_01 => 'Algemeen naaien, plooien, gepaspelde naden enz. Achteruitsteken worden genaaid wanneer u op de \"Achteruit/Verstevigingssteek\"-toets drukt.';

  @override
  String get t_explain01_01_01 => '\n * De naaldstand wijzigen \n Wanneer u steken voor de linkernaaldstand of middelste naaldstand kiest, kunt u met de toetsen \"+\" en \"-\" in het scherm voor L/R-verschuiving de naaldstand wijzigen. Stem de afstand van de rechterhoek van de persvoet tot aan de naald af op de marge. Zet de rand van de persvoet tijdens het naaien op één lijn met de rand van de stof voor een aantrekkelijke afwerking.';

  @override
  String get t_explain01_02 => 'Algemeen naaien, plooien, gepaspelde naden enz. Verstevigingssteken worden genaaid wanneer u op de \"Achteruit/verstevigingssteken\"-toets  drukt.';

  @override
  String get t_explain01_03 => 'Algemeen naaien, plooien, gepaspelde naden enz. Achteruitsteken worden genaaid wanneer u op de \"Achteruit/Verstevigingssteek\"-toets drukt.';

  @override
  String get t_explain01_04 => 'Algemeen naaien, plooien, gepaspelde naden enz. Verstevigingssteken worden genaaid wanneer u op de \"Achteruit/verstevigingssteken\"-toets  drukt.';

  @override
  String get t_explain01_05 => 'Algemeen naaien voor versteviging en decoratieve randen.';

  @override
  String get t_explain01_06 => 'Verstevigd naaien, naaien en decoratieve toepassingen.';

  @override
  String get t_explain01_07 => 'Decoratieve steken, randen stikken.';

  @override
  String get t_explain01_08 => 'Rijgen.';

  @override
  String get t_explain01_09 => 'Overhands naaien, repareren. Achteruitsteken worden genaaid wanneer u op de \"Achteruit/Verstevigingssteek\"-toets drukt.';

  @override
  String get t_explain01_10 => 'Overhands naaien, repareren. Verstevigingssteken worden genaaid wanneer u op de \"Achteruit/Verstevigingssteek\"-toets drukt.';

  @override
  String get t_explain01_11 => 'Beginnen vanuit rechternaaldstand, zigzagnaaien links.';

  @override
  String get t_explain01_12 => 'Beginnen vanuit linkernaaldstand, zigzagnaaien rechts.';

  @override
  String get t_explain01_13 => 'Overhands naaien (middelzware stof en stretchstof) , band en elastiek.';

  @override
  String get t_explain01_14 => 'Overhands naaien (middelzware stof, zware stof en stretchstof) , band en elastiek.';

  @override
  String get t_explain01_14a => 'Overhands naaien (middelzware stof, zware stof en stretchstof) , band en elastiek.';

  @override
  String get t_explain01_15 => 'Verstevigd naaien van lichte en middelmatig zware stof.';

  @override
  String get t_explain01_16 => 'Verstevigd naaien van zware stof.';

  @override
  String get t_explain01_17 => 'Verstevigd naaien van middelmatig dikke stof, dikke stof en stof die snel rafelt of voor decoratieve steken.';

  @override
  String get t_explain01_18 => 'Verstevigd naaien van naden bij stretchstof.';

  @override
  String get t_explain01_19 => 'Verstevigd naaien van middelzware stretchstof en zware stof of voor decoratieve steken.';

  @override
  String get t_explain01_20 => 'Verstevigd naaien van stretchstof of voor decoratieve steken.';

  @override
  String get t_explain01_21 => 'Naden van gebreide stretchstoffen.';

  @override
  String get t_explain01_22 => 'Verstevigd naaien van naden bij stretchstof.';

  @override
  String get t_explain01_23 => 'Verstevigd naaien van stretchstof.';

  @override
  String get t_explain01_24 => 'Rechte steek tijdens afknippen van stof.';

  @override
  String get t_explain01_25 => 'Zigzagsteek tijdens afknippen van stof.';

  @override
  String get t_explain01_26 => 'Overhandse steek tijdens afknippen van stof.';

  @override
  String get t_explain01_27 => 'Overhandse steek tijdens afknippen van stof.';

  @override
  String get t_explain01_28 => 'Overhandse steek tijdens afknippen van stof.';

  @override
  String get t_explain01_29 => 'Aanzetten/patchwork 6,5 mm (1/4 inch) marge rechts.';

  @override
  String get t_explain01_292 => 'Aanzetten/patchwork 7 mm marge rechts.';

  @override
  String get t_explain01_29a => 'Aanzetten/patchwork';

  @override
  String get t_explain01_30 => 'Aanzetten/patchwork 6,5 mm (1/4 inch) marge links.';

  @override
  String get t_explain01_302 => 'Aanzetten/patchwork 7 mm marge links.';

  @override
  String get t_explain01_31 => 'Quiltsteek die eruit ziet alsof hij met de hand is gemaakt.';

  @override
  String get t_explain01_32 => 'Zigzagsteek voor quilts en naaien op geappliceerde quiltstukken.';

  @override
  String get t_explain01_33 => 'Quiltsteek voor onzichtbare applicatie of het bevestigen van band.';

  @override
  String get t_explain01_34 => 'Voor op de achtergrond.';

  @override
  String get t_explain02_01 => 'Afzomen van geweven stof.';

  @override
  String get t_explain02_02 => 'Afzomen van stretchstof.';

  @override
  String get t_explain02_03 => 'Applicaties, decoratieve dekensteek.';

  @override
  String get t_explain02_04 => 'Afwerking met schelprijgsteek. Verhoog de spanning van de bovendraad voor een aantrekkelijke schelpafwerking van de schelprijgsteken.';

  @override
  String get t_explain02_05 => 'Decoreren kraag van blouse, rand zakdoek.';

  @override
  String get t_explain02_06 => 'Decoreren kraag van blouse, rand zakdoek.';

  @override
  String get t_explain02_07 => 'Patchworksteken, decoratieve steken.';

  @override
  String get t_explain02_08 => 'Patchworksteken, decoratieve steken.';

  @override
  String get t_explain02_09 => 'Decoratieve steken voor bevestigen koord en couching.';

  @override
  String get t_explain02_10 => 'Smockwerk, decoratieve steken.';

  @override
  String get t_explain02_11 => 'Fagotsteken, decoratieve steken.';

  @override
  String get t_explain02_12 => 'Fagotsteken, brugsteken en decoratieve steken.';

  @override
  String get t_explain02_13 => 'Band bevestigen aan zoom in stretchstof.';

  @override
  String get t_explain02_14 => 'Decoratieve steken.';

  @override
  String get t_explain02_15 => 'Decoratief stikken van randen.';

  @override
  String get t_explain02_15a => 'Decoratieve steken.';

  @override
  String get t_explain02_16 => 'Decoratieve steken.';

  @override
  String get t_explain02_17 => 'Decoratieve steken en elastiek bevestigen.';

  @override
  String get t_explain02_18 => 'Decoratieve steken en applicaties.';

  @override
  String get t_explain02_19 => 'Decoratieve steken.';

  @override
  String get t_explain03_01 => 'Decoratieve zomen, drievoudige rechte steek links.';

  @override
  String get t_explain03_02 => 'Decoratieve zomen, drievoudige rechte steek in het midden.';

  @override
  String get t_explain03_03 => 'Decoratieve zomen, randen stikken.';

  @override
  String get t_explain03_04 => 'Decoratieve zomen, kant bevestigen met pensteek.';

  @override
  String get t_explain03_05 => 'Decoratieve zomen.';

  @override
  String get t_explain03_06 => 'Decoratieve zomen, bloemetjessteek.';

  @override
  String get t_explain03_07 => 'Erfstukwerk, decoratieve zomen.';

  @override
  String get t_explain03_08 => 'Erfstukwerk, decoratieve zomen.';

  @override
  String get t_explain03_09 => 'Erfstukwerk, decoratieve zomen.';

  @override
  String get t_explain03_10 => 'Erfstukwerk, decoratieve zomen.';

  @override
  String get t_explain03_11 => 'Erfstukwerk, decoratieve zomen.';

  @override
  String get t_explain03_12 => 'Erfstukwerk, decoratieve zomen.';

  @override
  String get t_explain03_13 => 'Erfstukwerk, decoratieve zomen.';

  @override
  String get t_explain03_14 => 'Erfstukwerk, decoratieve zomen.';

  @override
  String get t_explain03_15 => 'Erfstukwerk, decoratieve zomen.';

  @override
  String get t_explain03_16 => 'Erfstukwerk, decoratieve zomen.';

  @override
  String get t_explain03_17 => 'Erfstukwerk, decoratieve zomen.';

  @override
  String get t_explain03_18 => 'Erfstukwerk, decoratieve zomen.';

  @override
  String get t_explain03_19 => 'Decoratieve zomen en brugsteek.';

  @override
  String get t_explain03_20 => 'Decoratieve zomen. Lint bevestigen met fagotsteken.';

  @override
  String get t_explain03_21 => 'Decoratieve zomen, smocksteken.';

  @override
  String get t_explain03_22 => 'Decoratieve zomen, smockwerk.';

  @override
  String get t_explain03_23 => 'Decoratieve zomen, smockwerk.';

  @override
  String get t_explain03_24 => 'Decoratieve zomen.';

  @override
  String get t_explain03_25 => 'Decoratieve steken.';

  @override
  String get t_explain04_01 => 'Knoopsgaten voor lichte tot middelzware stof.';

  @override
  String get t_explain04_02 => 'Knoopsgaten met extra ruimte voor grotere knopen.';

  @override
  String get t_explain04_03 => 'Verstevigde, taps toelopende knoopsgaten voor broek of rokband.';

  @override
  String get t_explain04_04 => 'Knoopsgaten met verticale trens voor zware stof.';

  @override
  String get t_explain04_05 => 'Knoopsgaten met trens.';

  @override
  String get t_explain04_06 => 'Knoopsgaten voor fijne, middelzware tot dikke stof.';

  @override
  String get t_explain04_07 => 'Knoopsgaten voor lichte tot middelzware stof.';

  @override
  String get t_explain04_08 => 'Knoopsgaten met extra ruimte voor grotere, decoratieve knopen.';

  @override
  String get t_explain04_09 => 'Knoopsgaten voor zwaar gebruik met verticale trenzen.';

  @override
  String get t_explain04_10 => 'Knoopsgaten voor stretchstof of geweven stof.';

  @override
  String get t_explain04_11 => 'Knoopsgaten voor erfstuk- en stretchstof.';

  @override
  String get t_explain04_12 => 'Eerste stap bij het maken van knoopsgaten in leer.';

  @override
  String get t_explain04_13 => 'Knoopsgaten in zware of dikke stoffen, voor grotere platte knopen.';

  @override
  String get t_explain04_14 => 'Knoopsgaten in middelzware tot zware stoffen, voor grotere platte knopen.';

  @override
  String get t_explain04_15 => 'Knoopsgaten met verticale trens voor versteviging van stof voor zware of dikke stof.';

  @override
  String get t_explain04_15a => 'Linkerkant van knoopsgat in vier stappen.';

  @override
  String get t_explain04_15b => 'Trens van knoopsgat in 4 stappen.';

  @override
  String get t_explain04_15c => 'Rechterkant van knoopsgat in vier stappen.';

  @override
  String get t_explain04_15d => 'Trens van knoopsgat in 4 stappen.';

  @override
  String get t_explain04_16 => 'Stoppen van middelzware stof.';

  @override
  String get t_explain04_17 => 'Stoppen van zware stof.';

  @override
  String get t_explain04_18 => 'Verstevigd naaien van zakopening enz.';

  @override
  String get t_explain04_19 => 'Knopen aanzetten.';

  @override
  String get t_explain04_20 => 'Oogjes maken of gaatjes, bijvoorbeeld in ceintuur. Als het naaiwerk geen goed resultaat oplevert, pas dan het steekpatroon aan.';

  @override
  String get t_explain04_21 => 'Stervormige oogjes of gaatjes maken. Als het naaiwerk geen goed resultaat oplevert, pas dan het steekpatroon aan.';

  @override
  String get t_explain05_01 => 'Bevestigen van applicaties op pijpvormige stukken stof en het naaien van verstekhoeken.';

  @override
  String get t_explain05_02 => 'Bevestigen van applicaties op pijpvormige stukken stof en het naaien van verstekhoeken.';

  @override
  String get t_explain05_03 => 'Bevestigen van applicaties op pijpvormige stukken stof en het naaien van verstekhoeken.';

  @override
  String get t_explain05_04 => 'Bevestigen van applicaties op pijpvormige stukken stof.';

  @override
  String get t_explain05_05 => 'Bevestigen van applicaties op pijpvormige stukken stof.';

  @override
  String get t_explain05_06 => 'Bevestigen van applicaties op pijpvormige stukken stof en het naaien van verstekhoeken.';

  @override
  String get t_explain05_07 => 'Bevestigen van applicaties op pijpvormige stukken stof en het naaien van verstekhoeken.';

  @override
  String get t_explain05_08 => 'Bevestigen van applicaties op pijpvormige stukken stof en het naaien van verstekhoeken.';

  @override
  String get t_explain05_09 => 'Bevestigen van applicaties op pijpvormige stukken stof.';

  @override
  String get t_explain05_10 => 'Bevestigen van applicaties op pijpvormige stukken stof.';

  @override
  String get t_explain05_11 => 'Bevestigen van applicaties op pijpvormige stukken stof en het naaien van verstekhoeken.';

  @override
  String get t_explain05_12 => 'Bevestigen van applicaties op pijpvormige stukken stof en het naaien van verstekhoeken.';

  @override
  String get t_explain06_01 => 'Voor applicaties met garen enz., om naaiend uit de vrije hand decoratieve verfraaiingen aan te brengen.';

  @override
  String get t_explain06_02 => 'Rijgsteken uit de vrije hand\nAls de transporteur omlaag staat, is het mogelijk om rijgsteken te maken uit de vrije hand.';

  @override
  String get t_explain06_03a => 'Het steekpatroon bestaat uit een aantal korte steken.\nNaai dit steekpatroon met doorzichtig nylon draad of lichtgewicht draad in een kleur die overeenkomt met de stof voor de bovendraad om uw project een handgenaaide uitstraling te geven. Als de kleur van de onderdraad en de stof verschillen, zal de quiltsteek opvallen.';

  @override
  String get t_explain07_01 => 'Applicaties, decoratieve steek.';

  @override
  String get t_explain07_02 => 'U kunt een steekpatroon taps laten toelopen aan het begin of het einde van het naaiwerk.';

  @override
  String get t_explaindeco00_01 => 'Aantrekkelijke afwerkingen maken';

  @override
  String get t_explaindeco00_02 => 'Aanpassingen';

  @override
  String get t_explaindeco01_00 => '[Aantrekkelijke afwerkingen maken]';

  @override
  String get t_explaindeco01_01 => 'Als u aantrekkelijk afwerkingen wilt maken voor letter-/decoratieve steken, controleer dan de combinatie stof/naald/draad. Andere factoren, zoals de dikte van de stof, steunstof enz., zijn ook van invloed op de steek. Naai daarom altijd een paar proefsteken voordat u aan uw project begint.';

  @override
  String get t_explaindeco01_02 => '(a) Stof\n(b) Steunstof\n(c) Dun papier\n\n* Wanneer u stretchstof, lichte stof of grof geweven stof naait, kunt u steunstof bevestigen. Als u geen steunstof wilt gebruiken, plaats dan de stof op een stuk dun papier, zoals overtrekpapier.\n\n* Draad\n#50 - #60\n\n* Naald\nVoor dunne stof, gewone stof of stretchstof: Ballpointnaald (goudkleurig) \nVoor zware stof: Naaimachinenaald voor huishoudelijk gebruik 90/14\n\n* Persvoet\nMonogramvoet \"N+\". Het gebruik van een andere persvoet kan slechtere resultaten geven.';

  @override
  String get t_explaindeco02_00 => '[Aanpassingen]';

  @override
  String get t_explaindeco02_01 => 'Afhankelijk van het soort of de dikte van de stof, de gebruikte steunstof, de naaisnelheid enz. wordt uw steekpatroon soms niet mooi. Naai in dat geval onder dezelfde omstandigheden als tijdens het echte naaiwerk proefsteken en pas het steekpatroon op de hieronder beschreven wijze aan. Als het steekpatroon, zelfs nadat u het op basis van het 6-120 patroon hebt aangepast, nog niet mooi wordt, dient u elk patroon afzonderlijk aan te passen.';

  @override
  String get t_explaindeco02_02 => '1. Kies 6-120. Bevestig monogramvoet \"N+\" en naai het patroon.';

  @override
  String get t_explaindeco02_03_00 => '\n2. Vergelijk het afgewerkte patroon met de illustratie van het patroon hieronder.';

  @override
  String get t_explaindeco02_04_00 => '[1. Als de steken opbollen]';

  @override
  String get t_explaindeco02_04_01 => 'Druk op + in VERTICALE FIJNAFSTELLING. Naai het steekpatroon nogmaals. Als het steekpatroon nu nog niet mooi is, past u het opnieuw aan totdat het steekpatroon juist is. Pas het steekpatroon aan totdat het wel goed wordt genaaid.';

  @override
  String get t_explaindeco02_05_00 => '[2. Als er ruimten tussen de steken zitten]';

  @override
  String get t_explaindeco02_05_01 => 'Druk op - in VERTICALE FIJNAFSTELLING. Naai het steekpatroon nogmaals. Als het steekpatroon nu nog niet mooi is, past u het opnieuw aan totdat het steekpatroon juist is. Pas het steekpatroon aan totdat het wel goed wordt genaaid.';

  @override
  String get t_explaindeco02_06_00 => '[3. Als de steken schuin naar links lopen]';

  @override
  String get t_explaindeco02_06_01 => 'Druk op + in HORIZONTALE FIJNAFSTELLING. Als het steekpatroon nu nog niet mooi is, past u het opnieuw aan totdat het steekpatroon juist is. Pas het steekpatroon aan totdat het wel goed wordt genaaid.';

  @override
  String get t_explaindeco02_07_00 => '[4. Als de steken schuin naar rechts lopen]';

  @override
  String get t_explaindeco02_07_01 => 'Druk op - in HORIZONTALE FIJNAFSTELLING. Als het steekpatroon nu nog niet mooi is, past u het opnieuw aan totdat het steekpatroon juist is. Pas het steekpatroon aan totdat het wel goed wordt genaaid.';

  @override
  String get t_terms_read => 'Lees aandachtig de onderstaande voorwaarden.';

  @override
  String get t_terms_cert_read => 'Lees aandachtig de onderstaande voorwaarden.';

  @override
  String get t_terms_cert_01_00 => 'Certificatie van de Upgrade KIT';

  @override
  String get t_terms_cert_01_01 => 'Voorwaarden voor certificatie van de Upgrade KIT\n';

  @override
  String get t_terms_cert_01_02 => '  Wanneer u optionele functies in deze software (“Software”) activeert, met inbegrip van, maar niet beperkt tot betaalde licenties, handleidingen, documenten en ander materiaal en de daarbij behorende updates (gezamenlijk de “Tools”), kunt u verplicht worden, direct of indirect, een bepaalde licentiecode, een bepaald productnummer, serienummer en andere, gerelateerde informatie (“Gebruikersgegevens”) te verstrekken om de Tools te kunnen gebruiken.\n';

  @override
  String get t_terms_cert_01_03 => '  Een deel van de informatie in de Gebruikersgegevens kan worden gekoppeld aan de gegevens die u registreert via de productregistratiewebsite bij Brother Industries, Ltd. (“Bedrijf”) of zijn dochteronderneming.  Het Bedrijf zal de Gebruikersgegevens echter niet gebruiken om u te identificeren of voor een ander doel gebruiken dan activering van de Tools. De Gebruikersgegevens kunnen worden verzonden naar de bedrijfsadministratieserver of servers van cloudservice-aanbieders zoals Microsoft en Amazon, die zich kunnen bevinden in landen zonder een passend beschermingsniveau voor persoonsgegevens in vergelijking met het niveau in uw land. Het Bedrijf zal uw Gebruikersgegevens echter beschermen in overeenstemming met de van toepassing zijnde wetgeving door passende beveiligingsmaatregelen te nemen ter voorkoming van ongeoorloofd gebruik of ongeoorloofde openbaarmaking.';

  @override
  String get t_terms_nettool_read => 'Lees aandachtig de onderstaande voorwaarden.';

  @override
  String get t_terms_nettool_01_00 => 'Netwerkdiagnosetool';

  @override
  String get t_terms_nettool_01_01 => 'Voorwaarden voor de netwerkdiagnosetool\n';

  @override
  String get t_terms_nettool_01_02 => '  Wanneer u een probleem hebt met uw netwerkverbinding, kunt u ervoor kiezen de netwerkdiagnosetool in deze software (“Software”) uit te voeren.  Wanneer u een netwerkdiagnose uitvoert, wordt er informatie van de naai- of handwerkproducten (“Bedrijfsproduct”) en de apparaten die zijn verbonden met het Bedrijfsproduct, met inbegrip van, maar niet beperkt tot Internet Protocol (IP)- of Media Access Control (MAC)-adres, proxyverbindingsinformatie, subnetmasker, gateway, DNS-server en overige, gerelateerde informatie (“Netwerkgerelateerde Informatie”) weergegeven op het scherm.  ';

  @override
  String get t_terms_nettool_01_03 => '  Wanneer u problemen ondervindt met de internetverbinding van het Bedrijfsproduct en technische ondersteuning wilt hebben, kan u worden gevraagd zelf uw Netwerkgerelateerde Informatie te communiceren naar uw lokale dealer of verkoper of naar Brother Industries, Ltd. (“Bedrijf”) en/of zijn dochterondernemingen via telefoon, e-mail, fax of internet.  Als u ervoor kiest uw Netwerkgerelateerde Informatie te verstrekken, erkent u en gaat u ermee akkoord dat de Netwerkgerelateerde Informatie uitsluitend ten behoeve van analyse en oplossing van uw netwerkprobleem kan worden verzonden naar het Bedrijf en zijn dochterondernemingen en dat uw informatie zal worden beschermd in overeenstemming met de van toepassing zijnde wetgeving.\n  Uw Netwerkgerelateerde Informatie zal niet anders dan hierboven uiteengezet worden verzameld of opgeslagen door het Bedrijf of zijn dochterondernemingen, tenzij het Bedrijf en/of zijn dochterondernemingen hiervoor vooraf en separaat uw goedkeuring hebben verkregen.';

  @override
  String get t_terms_cert_read_t => 'Lees aandachtig de onderstaande voorwaarden.';

  @override
  String get t_terms_cert_01_01_t => 'Voorwaarden voor certificatie van de Upgrade KIT\n';

  @override
  String get t_terms_cert_01_02_t => '  Wanneer u optionele functies in deze software (“Software”) activeert, met inbegrip van, maar niet beperkt tot betaalde licenties, handleidingen, documenten en ander materiaal en de daarbij behorende updates (gezamenlijk de “Tools”), kunt u verplicht worden, direct of indirect, een bepaalde licentiecode, een bepaald productnummer en serienummer (“Gebruikersgegevens”) te verstrekken om de Tools te kunnen gebruiken.\n';

  @override
  String get t_terms_cert_01_03_t => '  Een deel van de informatie in de Gebruikersgegevens kan worden gekoppeld aan de gegevens die u registreert via de productregistratiewebsite bij Tacony Corporation handelend onder de naam Baby Lock (“Bedrijf”).  Het Bedrijf zal de Gebruikersgegevens echter niet gebruiken om u te identificeren of voor een ander doel gebruiken dan activering van de Tools. De Gebruikersgegevens kunnen worden verzonden naar de bedrijfsadministratieserver of servers van cloudservice-aanbieders zoals Microsoft en AWS, die zich kunnen bevinden in landen zonder een passend beschermingsniveau voor persoonsgegevens in vergelijking met het niveau in uw land.  Het Bedrijf zal uw Gebruikersgegevens echter beschermen in overeenstemming met de van toepassing zijnde wetgeving door passende beveiligingsmaatregelen te nemen ter voorkoming van ongeoorloofd gebruik of ongeoorloofde openbaarmaking.';

  @override
  String get t_terms_nettool_read_t => 'Lees aandachtig de onderstaande voorwaarden.';

  @override
  String get t_terms_nettool_01_01_t => 'Voorwaarden voor de netwerkdiagnosetool\n';

  @override
  String get t_terms_nettool_01_02_t => '  Wanneer u een probleem hebt met uw netwerkverbinding, kunt u ervoor kiezen de netwerkdiagnosetool in deze software (“Software”) uit te voeren.  Wanneer u een netwerkdiagnose uitvoert, wordt er informatie van de naaiproducten (“Bedrijfsproduct”) en de apparaten die zijn verbonden met het Bedrijfsproduct, met inbegrip van, maar niet beperkt tot Internet Protocol (IP)- of Media Access Control (MAC)-adres, proxyverbindingsinformatie, subnetmasker, gateway, DNS-server en overige, gerelateerde informatie (“Netwerkgerelateerde Informatie”) weergegeven op het scherm.  ';

  @override
  String get t_terms_nettool_01_03_t => '  Wanneer u problemen ondervindt met de internetverbinding van het Bedrijfsproduct en technische ondersteuning wilt hebben, kan u worden gevraagd zelf uw Netwerkgerelateerde Informatie te communiceren naar uw lokale detaillist of naar Tacony Corporation handelend onder de naam Baby Lock (“Bedrijf”) via telefoon, e-mail, fax of internet.  Als u ervoor kiest uw Netwerkgerelateerde Informatie te verstrekken, erkent u en gaat u ermee akkoord dat de Netwerkgerelateerde Informatie uitsluitend ten behoeve van analyse en oplossing van uw netwerkprobleem kan worden verzonden naar het Bedrijf en dat uw informatie zal worden beschermd in overeenstemming met de van toepassing zijnde wetgeving.\n  Uw Netwerkgerelateerde Informatie zal niet anders dan hierboven uiteengezet worden verzameld of opgeslagen door het Bedrijf, tenzij het Bedrijf hiervoor vooraf en separaat uw goedkeuring heeft verkregen.';

  @override
  String get t_terms_mnmpinmac_01_b => 'Als u op \"OK\" klikt, worden uw pincode, MAC-adres en machinenummer naar de server van Brother verzonden om uw naaimachine te koppelen aan uw ScanNCut en uw andere naaimachines.\nDe verschafte informatie wordt uitsluitend gebruikt voor de hierboven genoemde doeleinden.';

  @override
  String get t_terms_snj_pair_01 => 'Wanneer u op “OK” klikt, worden uw pincode, het MAC-adres, de naam van de machine en het nummer van de machine naar de server van Brother verzonden om uw naaimachine te koppelen aan uw Brother-apparaten/diensten.\nDe verschafte informatie wordt uitsluitend gebruikt voor de hierboven genoemde doeleinden.';

  @override
  String get upg_01 => 'Sluit USB-medium aan.';

  @override
  String get upg_02 => 'Kan bestand niet lezen.';

  @override
  String get upg_03 => 'Kan juiste bestand niet vinden.';

  @override
  String get upg_04 => 'Fout controletotaal';

  @override
  String get upg_05 => 'Bestand niet opgeslagen.';

  @override
  String get upg_06 => 'Bestandsadres onjuist.';

  @override
  String get upg_07 => 'Aangesloten op pc. Maak de USB-kabel niet los.';

  @override
  String get upg_08 => 'Updatebestand wordt opgeslagen. \nZet de naaimachine niet uit.';

  @override
  String get update_08 => 'Updatebestand wordt opgeslagen. \nZet de naaimachine niet uit.';

  @override
  String get upg_09 => 'Bijwerken is voltooid.';

  @override
  String get update_09 => 'Bijwerken is voltooid.';

  @override
  String get upg_10 => 'Druk op LADEN nadat u het USB-medium met het bijwerkbestand hebt aangesloten.';

  @override
  String get update_10 => 'Druk op LADEN nadat u het USB-medium met het bijwerkbestand hebt aangesloten.';

  @override
  String get upg_12 => 'Druk op LADEN om het updatebestand te installeren.';

  @override
  String get update_13 => 'Kan niet rechtstreeks vanuit de actuele versie bijwerken naar de nieuwe versie.';

  @override
  String get update_14 => 'Voer een update van de software uit naar de versie hieronder door de machine uit te schakelen en vervolgens de update uit te voeren vanaf het USB-medium met het updatebestand.';

  @override
  String get update_15 => 'Schakel de machine uit en werk de software vervolgens bij vanaf het USB-medium met het bijwerkbestand.';

  @override
  String get icon_00037 => 'Terug';

  @override
  String get icon_00008_u => 'Sluiten';

  @override
  String get icon_00009_u => 'Annuleren';

  @override
  String get icon_00010_u => 'ＯＫ';

  @override
  String get icon_00050_u => 'Laden';

  @override
  String get upg_16 => 'Update mislukt. \nInstalleer het updateprogramma opnieuw. \n* Als het probleem aanhoudt, downloadt en installeert u het programma opnieuw.';

  @override
  String get upg_17 => 'Update mislukt. \nDownload en installeer het updateprogramma opnieuw.';

  @override
  String get upg_18 => 'ERR_UPEND';

  @override
  String get upg_19 => 'Start de machine opnieuw op.\nHet kan even duren voordat de machine is opgestart. Het scherm kan tijdelijk donker worden.';

  @override
  String get upg_20 => 'Schakel het apparaat niet uit, zelfs niet als het scherm zwart wordt.';

  @override
  String get upg_21 => 'Detectie van corruptie van het bestandssysteem is mislukt.\nZet de machine uit en weer aan.';

  @override
  String get upg_22 => 'Kan beschadigde systeembestanden niet repareren.\nZet de machine uit en weer aan.';

  @override
  String get upg_23 => 'Update mislukt. \nNadat u de computer normaal hebt opgestart, schakelt u de computer uit en probeert u het updateprogramma opnieuw te installeren.';

  @override
  String get t_name_01_01 => 'Rechte steek (links)';

  @override
  String get t_name_01_02 => 'Rechte steek (links)';

  @override
  String get t_name_01_03 => 'Rechte steek (midden)';

  @override
  String get t_name_01_04 => 'Rechte steek (midden)';

  @override
  String get t_name_01_05 => 'Drievoudige stretchsteek';

  @override
  String get t_name_01_06 => 'Stamsteek';

  @override
  String get t_name_01_07 => 'Decoratieve steek';

  @override
  String get t_name_01_08 => 'Rijgsteek';

  @override
  String get t_name_01_09 => 'Zigzagsteek';

  @override
  String get t_name_01_10 => 'Zigzagsteek';

  @override
  String get t_name_01_11 => 'Zigzagsteek (rechts)';

  @override
  String get t_name_01_12 => 'Zigzagsteek (links)';

  @override
  String get t_name_01_13 => 'Elastische zigzag in 2 stappen';

  @override
  String get t_name_01_14 => 'Elastische zigzag in 2 stappen';

  @override
  String get t_name_01_14a => 'Elastische zigzag in 3 stappen';

  @override
  String get t_name_01_15 => 'Afwerksteek';

  @override
  String get t_name_01_16 => 'Afwerksteek';

  @override
  String get t_name_01_17 => 'Afwerksteek';

  @override
  String get t_name_01_18 => 'Afwerksteek';

  @override
  String get t_name_01_19 => 'Afwerksteek';

  @override
  String get t_name_01_20 => 'Afwerksteek';

  @override
  String get t_name_01_21 => 'Afwerksteek';

  @override
  String get t_name_01_22 => 'Enkelvoudige ruit afwerksteek';

  @override
  String get t_name_01_23 => 'Enkelvoudige ruit afwerksteek';

  @override
  String get t_name_01_24 => 'Met zijsnijder';

  @override
  String get t_name_01_25 => 'Met zijsnijder';

  @override
  String get t_name_01_26 => 'Met zijsnijder';

  @override
  String get t_name_01_27 => 'Met zijsnijder';

  @override
  String get t_name_01_28 => 'Met zijsnijder';

  @override
  String get t_name_01_29 => 'Verbindingssteek (rechts)';

  @override
  String get t_name_01_29a => 'Verbindingssteek (midden)';

  @override
  String get t_name_01_30 => 'Verbindingssteek (links)';

  @override
  String get t_name_01_31 => 'Quiltsteek met hand- gemaakt uiterlijk';

  @override
  String get t_name_01_32 => 'Zigzagsteek voor quiltapplicatie';

  @override
  String get t_name_01_33 => 'Appliceersteek voor quilts';

  @override
  String get t_name_01_34 => 'Stippelsteek voor quilts';

  @override
  String get t_name_02_01 => 'Blindzoomsteek';

  @override
  String get t_name_02_02 => 'Blindzoomsteek stretchstof';

  @override
  String get t_name_02_03 => 'Dekensteek';

  @override
  String get t_name_02_03a => 'Dekensteek';

  @override
  String get t_name_02_04 => 'Schelprijgsteek voor randen';

  @override
  String get t_name_02_05 => 'Satijnen schelpsteek';

  @override
  String get t_name_02_06 => 'Schelpsteek';

  @override
  String get t_name_02_07 => 'Verbindingssteek voor patchwork';

  @override
  String get t_name_02_08 => 'Dubbele overlock- steek voor patchwork';

  @override
  String get t_name_02_09 => 'Couching-steek';

  @override
  String get t_name_02_10 => 'Smocksteek';

  @override
  String get t_name_02_11 => 'Veersteek';

  @override
  String get t_name_02_12 => 'Fagot kruissteek';

  @override
  String get t_name_02_13 => 'Bevestigen band';

  @override
  String get t_name_02_14 => 'Laddersteek';

  @override
  String get t_name_02_15 => 'Zigzag sierzoomsteek';

  @override
  String get t_name_02_15a => 'Decoratieve steek';

  @override
  String get t_name_02_16 => 'Decoratieve steek';

  @override
  String get t_name_02_17 => 'Serpentsteek';

  @override
  String get t_name_02_18 => 'Decoratieve steek';

  @override
  String get t_name_02_19 => 'Decoratieve stippelsteek';

  @override
  String get t_name_03_01 => 'Zoomsteken';

  @override
  String get t_name_03_02 => 'Zoomsteken';

  @override
  String get t_name_03_03 => 'Zoomsteken zigzag';

  @override
  String get t_name_03_04 => 'Zoomsteken';

  @override
  String get t_name_03_05 => 'Zoomsteken';

  @override
  String get t_name_03_06 => 'Zoomsteken';

  @override
  String get t_name_03_07 => 'Zoomsteken';

  @override
  String get t_name_03_08 => 'Zoomsteken';

  @override
  String get t_name_03_09 => 'Zoomsteken';

  @override
  String get t_name_03_10 => 'Zoomsteken';

  @override
  String get t_name_03_11 => 'Zoomsteken';

  @override
  String get t_name_03_12 => 'Honingraatsteek';

  @override
  String get t_name_03_13 => 'Honingraatsteek';

  @override
  String get t_name_03_14 => 'Zoomsteken';

  @override
  String get t_name_03_15 => 'Zoomsteken';

  @override
  String get t_name_03_16 => 'Zoomsteken';

  @override
  String get t_name_03_17 => 'Zoomsteken';

  @override
  String get t_name_03_18 => 'Zoomsteken';

  @override
  String get t_name_03_19 => 'Zoomsteken';

  @override
  String get t_name_03_20 => 'Zoomsteken';

  @override
  String get t_name_03_21 => 'Zoomsteken';

  @override
  String get t_name_03_22 => 'Zoomsteken';

  @override
  String get t_name_03_23 => 'Zoomsteken';

  @override
  String get t_name_03_24 => 'Zoomsteken';

  @override
  String get t_name_03_25 => 'Laddersteek';

  @override
  String get t_name_04_01 => 'Smal afgerond knoopsgat';

  @override
  String get t_name_04_02 => 'Breed afgerond knoopsgat';

  @override
  String get t_name_04_03 => 'Taps toelopend afgerond knoopsgat';

  @override
  String get t_name_04_04 => 'Afgerond knoopsgat';

  @override
  String get t_name_04_05 => 'Afgerond knoopsgat';

  @override
  String get t_name_04_06 => 'Aan beide zijden afgerond knoopsgat';

  @override
  String get t_name_04_07 => 'Smal vierkant knoopsgat';

  @override
  String get t_name_04_08 => 'Breed vierkant knoopsgat';

  @override
  String get t_name_04_09 => 'Vierkant knoopsgat';

  @override
  String get t_name_04_10 => 'Stretchknoopsgat';

  @override
  String get t_name_04_11 => 'Erfstukknoopsgat';

  @override
  String get t_name_04_12 => 'Knoopsgat in leer';

  @override
  String get t_name_04_13 => 'Lingerieknoopsgat';

  @override
  String get t_name_04_14 => 'Taps toelopend lingerieknoopsgat';

  @override
  String get t_name_04_15 => 'Lingerieknoopsgat';

  @override
  String get t_name_04_15a => 'Knoopsgat in vier stappen - 1';

  @override
  String get t_name_04_15b => 'Knoopsgat in vier stappen - 2';

  @override
  String get t_name_04_15c => 'Knoopsgat in vier stappen - 3';

  @override
  String get t_name_04_15d => 'Knoopsgat in vier stappen - 4';

  @override
  String get t_name_04_16 => 'Stoppen';

  @override
  String get t_name_04_17 => 'Stoppen';

  @override
  String get t_name_04_18 => 'Trens';

  @override
  String get t_name_04_19 => 'Knopen aanzetten';

  @override
  String get t_name_04_20 => 'Oogje';

  @override
  String get t_name_04_21 => 'Stervormig oogje';

  @override
  String get t_name_05_01 => 'Diagonaal links omhoog (recht)';

  @override
  String get t_name_05_02 => 'Achteruit (recht)';

  @override
  String get t_name_05_03 => 'Diagonaal rechts omhoog (recht)';

  @override
  String get t_name_05_04 => 'Zijwaarts naar links (recht)';

  @override
  String get t_name_05_05 => 'Zijwaarts naar rechts (recht)';

  @override
  String get t_name_05_06 => 'Diagonaal links omlaag (recht)';

  @override
  String get t_name_05_07 => 'Voorwaarts (recht)';

  @override
  String get t_name_05_08 => 'Diagonaal rechts omlaag (recht)';

  @override
  String get t_name_05_09 => 'Zijwaarts naar links (zigzag)';

  @override
  String get t_name_05_10 => 'Zijwaarts naar rechts (zigzag)';

  @override
  String get t_name_05_11 => 'Voorwaarts (zigzag)';

  @override
  String get t_name_05_12 => 'Achteruit (zigzag)';

  @override
  String get t_name_06_01 => 'Vrije hand koordvoet';

  @override
  String get t_name_06_02 => 'Rijgsteken uit de vrije hand';

  @override
  String get t_name_06_03 => 'Quiltsteek met hand- gemaakt uiterlijk';

  @override
  String get t_name_06_04 => 'Quiltsteek met hand- gemaakt uiterlijk';

  @override
  String get t_name_06_05 => 'Quiltsteek met hand- gemaakt uiterlijk';

  @override
  String get t_name_06_06 => 'Steek voor naaldvilten';

  @override
  String get t_name_07_01 => 'Applicatiesteek';

  @override
  String get t_name_07_02 => 'Tapse steek';

  @override
  String get t_name_sr_01 => 'Rechte steek (midden)';

  @override
  String get t_name_sr_02 => 'Zigzagsteek';

  @override
  String get t_name_sr_03 => 'Rijgsteken uit de vrije hand';

  @override
  String get tt_head_wifi => 'WLAN Draadloze verbinding-instellingen';

  @override
  String get tt_head_camera => 'Camerabeeld';

  @override
  String get tt_head_setting => 'Instellingen machine';

  @override
  String get tt_head_teaching => 'Help machine';

  @override
  String get tt_head_osae => 'Verwisselen persvoet/naald ';

  @override
  String get tt_head_lock => 'Schermvergrendeling';

  @override
  String get tt_head_home => 'Startpagina';

  @override
  String get tt_foot_clock => 'Instellingen datum/tijd';

  @override
  String get tt_tch_og_principal_parts1 => '[Persvoethendel]';

  @override
  String get tt_tch_og_principal_parts2 => '[Schuifknop voor snelheidsregeling]';

  @override
  String get tt_tch_og_principal_parts3 => '[Handwiel]';

  @override
  String get tt_tch_og_principal_parts4 => '[Afneembare accessoires-opbergtafel]';

  @override
  String get tt_tch_og_mb_knee_lifter => '[Kniehevel]';

  @override
  String get tt_tch_og_principal_parts6 => '[Voetpedaal]';

  @override
  String get tt_tch_og_principalbuttons1 => '[\"Naaldstand\"-toets]';

  @override
  String get tt_tch_og_principalbuttons2 => '[\"Draadknip\"-toets]';

  @override
  String get tt_tch_og_principalbuttons3 => '[\"Persvoethendel\"-toets]';

  @override
  String get tt_tch_og_principalbuttons4 => '[\"Automatisch inrijgen\"-toets]';

  @override
  String get tt_tch_og_principalbuttons5 => '[\"Start/Stop\"-toets]';

  @override
  String get tt_tch_og_principalbuttons6 => '[\"Achteruit\"-toets]';

  @override
  String get tt_tch_og_principalbuttons7 => '[\"Versteviging/Afhechten\"-toets]';

  @override
  String get tt_tch_og_basic_operation1 => '[Bovendraad inrijgen]';

  @override
  String get tt_tch_og_basic_operation2 => '[Spoel opwinden]';

  @override
  String get tt_tch_og_basic_operation3 => '[Naald verwisselen]';

  @override
  String get tt_tch_og_basic_operation4 => '[Persvoet verwisselen]';

  @override
  String get tt_tch_og_basic_operation5 => '[Spoel plaatsen]';

  @override
  String get tt_tch_og_emb_basic_operation1 => '[Draadspanning aanpassen]';

  @override
  String get tt_tch_og_emb_basic_operation2 => '[Opstrijkbare versteviging aanbrengen op de stof]';

  @override
  String get tt_tch_og_emb_basic_operation3 => '[Stof plaatsen]';

  @override
  String get tt_tch_og_emb_basic_operation4 => '[Borduureenheid bevestigen]';

  @override
  String get tt_tch_og_emb_basic_operation5 => '[Borduurtafel bevestigen]';

  @override
  String get tt_tch_og_emb_basic_operation6 => '[Borduurvoet \"W\" bevestigen]';

  @override
  String get tt_tch_og_emb_basic_operation7 => '[Juiste steunstof/versteviging gebruiken]';

  @override
  String get tt_tch_maintenance1 => '[Grijper en spoelhuis reinigen]';

  @override
  String get tt_utl_category01 => 'Rechte steken /afwerksteken';

  @override
  String get tt_utl_category02 => 'Decoratieve steken';

  @override
  String get tt_utl_category03 => 'Nostalgische steken';

  @override
  String get tt_utl_category04 => 'Knoopsgaten/trenzen';

  @override
  String get tt_utl_category05 => 'Steken in verschillende richtingen';

  @override
  String get tt_utl_category_q => 'Quiltsteken';

  @override
  String get tt_utl_category_s => 'Overige steken';

  @override
  String get tt_utl_category_t => 'Taps toelopende steken';

  @override
  String get tt_utl_stitchpreview => 'Voorbeeld';

  @override
  String get tt_utl_projecter => 'Projectorfuncties';

  @override
  String get tt_utl_guideline => 'Geleidelijnmarkering';

  @override
  String get tt_utl_editmenu => 'Menu Bewerken';

  @override
  String get tt_utl_freemotion => 'Vrije hand modus';

  @override
  String get tt_utl_repeat_stitch_atamadashi => 'Terug naar begin';

  @override
  String get tt_utl_alone_repeat => 'Enkele steek/herhalende steek';

  @override
  String get tt_utl_utilityflipvertical => 'Spiegelbeeld';

  @override
  String get tt_utl_twinneedle => 'Enkele naald/tweelingnaald';

  @override
  String get tt_utl_buttonholemanual => 'Knoopsgatlengte';

  @override
  String get tt_utl_endpointsetting => 'Instelling eindpunt';

  @override
  String get tt_utl_tapering => 'Steek-tapering';

  @override
  String get tt_utl_autoreverse => 'Automatisch achteruit/verstevigingssteek';

  @override
  String get tt_utl_scissor => 'Automatisch draadknippen';

  @override
  String get tt_utl_needlestopposition => 'Instelling naaldstopstand';

  @override
  String get tt_utl_pivot => 'Draaipositie/Automatisch omhoog';

  @override
  String get tt_utl_threadcolor => 'Garenkleurwisseling';

  @override
  String get tt_utl_category06 => 'Brede en gevarieerde steken';

  @override
  String get tt_utl_category07 => 'Brede botanische steken';

  @override
  String get tt_utl_category08 => 'Brede motiefsteken en teksten';

  @override
  String get tt_utl_category09 => 'Smalle en gevarieerde steken';

  @override
  String get tt_utl_category10 => 'Smalle botanische steken';

  @override
  String get tt_utl_category11 => 'Bolletjessteken';

  @override
  String get tt_utl_category12 => 'Grote satijnsteken';

  @override
  String get tt_utl_category13 => 'Satijnsteken';

  @override
  String get tt_utl_category14 => 'Kruissteken';

  @override
  String get tt_utl_category15 => 'Combinatiesteken';

  @override
  String get tt_utl_category16 => 'Disney';

  @override
  String get tt_utl_category17 => 'Gotisch lettertype';

  @override
  String get tt_utl_category18 => 'Handgeschreven lettertype';

  @override
  String get tt_utl_category19 => 'Contourlettertype';

  @override
  String get tt_utl_category20 => 'Cyrillisch lettertype';

  @override
  String get tt_deco_category_pocket => 'Zak (Machinegeheugen/extern geheugen)';

  @override
  String get tt_deco_mycustomsititch => 'Functie “MY CUSTOM STITCH”';

  @override
  String get tt_deco_stitchpreview => 'Voorbeeld';

  @override
  String get tt_deco_projecter => 'Projectorfuncties';

  @override
  String get tt_deco_guidline => 'Geleidelijnmarkering';

  @override
  String get tt_deco_editmenu => 'Menu Bewerken';

  @override
  String get tt_deco_memory => 'Steekgegevens opslaan';

  @override
  String get tt_deco_threadcolor => 'Garenkleurwisseling';

  @override
  String get tt_deco_stitchplus => 'Steekpatroon toevoegen';

  @override
  String get tt_deco_stitchselectall => 'Alles aan/uit selecteren';

  @override
  String get tt_deco_pivot => 'Draaipositie/Automatisch omhoog';

  @override
  String get tt_deco_needlestopposition => 'Instelling naaldstopstand';

  @override
  String get tt_deco_scissor => 'Automatisch draadknippen';

  @override
  String get tt_deco_autoreverse => 'Automatisch achteruit/verstevigingssteek';

  @override
  String get tt_deco_stitchstep1 => 'Trapeffect';

  @override
  String get tt_deco_stitchstep2 => 'Trapeffect';

  @override
  String get tt_deco_filemanager => 'Bestandsbeheer';

  @override
  String get tt_deco_filemanager_selectall => 'Alles selecteren';

  @override
  String get tt_deco_filemanager_selectnone => 'Alles deselecteren';

  @override
  String get tt_deco_filemanager_delete => 'Verwijderen';

  @override
  String get tt_deco_filemanager_memory => 'Sla de geselecteerde steekpatronen op in het geheugen van de machine.';

  @override
  String get tt_deco_freemotion => 'Vrije hand modus';

  @override
  String get tt_deco_repeat_stitch_atamadashi => 'Terug naar begin';

  @override
  String get tt_deco_alone_repeat => 'Enkele steek/herhalende steken';

  @override
  String get tt_deco_utilityfliphorizon => 'Horizontaal spiegelbeeld';

  @override
  String get tt_deco_utilityflipvertical => 'Verticaal spiegelbeeld';

  @override
  String get tt_deco_alone_single => 'Enkele naald/tweelingnaald';

  @override
  String get tt_deco_delete => 'Verwijderen';

  @override
  String get tt_deco_density => 'Steekdichtheid';

  @override
  String get tt_deco_elongator => 'Verlenging';

  @override
  String get tt_deco_spacing => 'Tussenruimteletters';

  @override
  String get tt_deco_stitchsizelink => 'Hoogte-breedteverhouding behouden';

  @override
  String get tt_deco_endpointsetting => 'Instelling eindpunt';

  @override
  String get tt_mcs_triplesewing => 'Enkele/drievoudige steek';

  @override
  String get tt_mcs_pointdelete => 'Punt wissen';

  @override
  String get tt_mcs_blockmove => 'Groep steken verplaatsen';

  @override
  String get tt_mcs_insert => 'Invoegen';

  @override
  String get tt_utl_mcspointset => 'Instellen';

  @override
  String get tt_mcs_contents => 'Steekpatronen importeren';

  @override
  String get tt_mcs_memory => 'Steekgegevens opslaan';

  @override
  String get tt_utl_sr_guideline => 'Geleidelijnmarkering';

  @override
  String get tt_utl_sr_sensingline => 'Detectielijn';

  @override
  String get tt_utl_sr_srstatus => 'Status steekregeling';

  @override
  String get tt_embcate_embpatterns => 'Borduurpatronen';

  @override
  String get tt_embcate_character => 'Letters in diverse lettertypen';

  @override
  String get tt_embcate_decoalphabet => 'Decoratieve letterpatronen';

  @override
  String get tt_embcate_frame => 'Kaders en omrandingen';

  @override
  String get tt_embcate_utility => 'Knoopsgatpatronen/ Decoratieve steken';

  @override
  String get tt_embcate_split => 'Gesplitste borduurpatronen';

  @override
  String get tt_embcate_long_stitch => 'Borduurpatronen met lange steken';

  @override
  String get tt_embcate_quilt => 'Quiltrandpatronen en Edge-to-Edge quiltpatronen';

  @override
  String get tt_embcate_b_disney => 'Disneypatronen';

  @override
  String get tt_embcate_couching => 'Couching-patronen';

  @override
  String get tt_embcate_t_exclusives => 'Exclusives';

  @override
  String get tt_embcate_memory => 'Patronen die zijn opgeslagen in het geheugen van de machine, op een USB-medium enz.';

  @override
  String get tt_emb_pantool => 'Handje';

  @override
  String get tt_emb_backgroundscan => 'Stofscan';

  @override
  String get tt_emb_realpreview => 'Voorbeeld';

  @override
  String get tt_emb_memory => 'Geheugen';

  @override
  String get tt_emb_redo => 'Opnieuw';

  @override
  String get tt_emb_undo => 'Ongedaan maken';

  @override
  String get tt_emb_delete => 'Verwijderen';

  @override
  String get tt_emb_select => 'Selecteren';

  @override
  String get tt_emb_multipleselect => 'Meervoudige selectie';

  @override
  String get tt_emb_editsize => 'Afmeting';

  @override
  String get tt_emb_editmove => 'Verplaatsen';

  @override
  String get tt_emb_editgroup => 'Groeperen/groepering opheffen';

  @override
  String get tt_emb_editrotate => 'Roteren';

  @override
  String get tt_emb_editflip => 'Horizontaal spiegelen';

  @override
  String get tt_emb_editduplicate => 'Dupliceren';

  @override
  String get tt_emb_editdensity => 'Dichtheid';

  @override
  String get tt_emb_editborder => 'Randfunctie (Herhaalpatronen ontwerpen)';

  @override
  String get tt_emb_editapplique => 'Applicatie';

  @override
  String get tt_emb_editchangecolor => 'Garenkleurenpalet';

  @override
  String get tt_emb_edittextedit => 'Letterpatronen bewerken';

  @override
  String get tt_emb_editalign => 'Uitlijning';

  @override
  String get tt_emb_editstippling => 'Stippling/meandersteken';

  @override
  String get tt_emb_editoutline => 'Omtrek extraheren';

  @override
  String get tt_emb_editorder => 'Borduurvolgorde';

  @override
  String get tt_emb_editnotsew => 'Niet-borduren instelling';

  @override
  String get tt_emb_textsize => 'Afmeting';

  @override
  String get tt_emb_textarray => 'Rangschikken';

  @override
  String get tt_emb_textspacing => 'Tussenruimte letters';

  @override
  String get tt_emb_textalign => 'Uitlijning';

  @override
  String get tt_emb_embfootw => 'Naaldpositiecontrole';

  @override
  String get tt_emb_emb_projectorsetting => 'Projectorinstellingen';

  @override
  String get tt_emb_embprojector => 'Projector';

  @override
  String get tt_emb_embmove => 'Verplaatsen';

  @override
  String get tt_emb_embrotate => 'Roteren';

  @override
  String get tt_emb_embbasting => 'Rijgsteken';

  @override
  String get tt_emb_embsnowman => 'Borduurpositie';

  @override
  String get tt_emb_embonecolorsew => 'Ononderbroken borduren';

  @override
  String get tt_emb_embcolorsorting => 'Kleurvolgorde';

  @override
  String get tt_emb_embconnectsew => 'Patroonverbinding';

  @override
  String get tt_emb_embframemove => 'Borduurraam verplaatsen: Het frame wordt tijdelijk naar het midden verplaatst.';

  @override
  String get tt_emb_embmemory => 'Geheugen';

  @override
  String get tt_emb_embmasktrace => 'Traceergebied';

  @override
  String get tt_emb_embstartposition => 'Beginpunt';

  @override
  String get tt_emb_embneedlenumber => 'Vooruit/terug';

  @override
  String get tt_emb_embfbcamera => 'Camerabeeld';

  @override
  String get tt_emb_embthreadcutting => 'Knip/spanning';

  @override
  String get tt_emb_embcolorbar => 'Eén kleur/alle kleuren op voortgangsbalk';

  @override
  String get tt_emb_patterninfo => 'Patrooninformatie';

  @override
  String get tt_emb_previewsim => 'Steeksimulator';

  @override
  String get tt_emb_sewtrim_endcolor => 'Einde kleur knippen';

  @override
  String get tt_emb_sewtrim_jumpstitch => 'Sprongsteek knippen';

  @override
  String get tt_emb_previewframe => 'Voorbeeld borduurraam weergeven';

  @override
  String get tt_emb_size_normalstb => 'De grootte van het borduurpatroon wijzigen met behoud van het aantal steken/de draaddichtheid.';

  @override
  String get tt_emb_edit_border_vert => 'Patroonherhaling/verticaal verwijderen';

  @override
  String get tt_emb_edit_border_horiz => 'Patroonherhaling/horizontaal verwijderen';

  @override
  String get tt_emb_edit_border_dividervert => 'Patroon verticaal knippen';

  @override
  String get tt_emb_edit_border_dividehoriz => 'Patroon horizontaal knippen';

  @override
  String get tt_emb_edit_border_threadmark => 'Draadmarkeringen';

  @override
  String get tt_emb_edit_border_reset => 'Herstellen';

  @override
  String get tt_emb_emb_rotate_reset => 'Herstellen';

  @override
  String get tt_emb_edit_rotate_reset => 'Herstellen';

  @override
  String get tt_emb_camera_rotate_reset => 'Herstellen';

  @override
  String get tt_emb_edit_font_spacing_reset => 'Herstellen';

  @override
  String get tt_emb_edit_align_reset => 'Herstellen';

  @override
  String get tt_emb_edit_size_reset => 'Herstellen';

  @override
  String get tt_emb_edit_order_reset => 'Herstellen';

  @override
  String get tt_emb_quiltborder_color_reset => 'Herstellen';

  @override
  String get tt_emb_edit_color_reset => 'Herstellen';

  @override
  String get tt_emb_photositich_size_change_reset => 'Herstellen';

  @override
  String get tt_emb_edit_projlcd_switch_fb_reset => 'Herstellen';

  @override
  String get tt_emb_edit_projlcd_align_reset => 'Herstellen';

  @override
  String get tt_emb_edit_projlcd_border_reset => 'Herstellen';

  @override
  String get tt_emb_edit_projlcd_rotate_reset => 'Herstellen';

  @override
  String get tt_emb_edit_projlcd_size_reset => 'Herstellen';

  @override
  String get tt_mdc_paint_rotate_reset => 'Herstellen';

  @override
  String get tt_mdc_paint_size_input_reset => 'Herstellen';

  @override
  String get tt_mdc_paint_size_reset => 'Herstellen';

  @override
  String get tt_emb_newapplique_color_selectall => 'Alles selecteren';

  @override
  String get tt_emb_newapplique_color_selectnone => 'Alles deselecteren';

  @override
  String get tt_emb_color_selectall => 'Eén kleur/alle kleuren selecteren';

  @override
  String get tt_emb_colorcolorshuffling => 'Color Shuffling(Kleurnuances)';

  @override
  String get tt_emb_colorvisualizer => 'Color Visualizer';

  @override
  String get tt_emb_editselectall => 'Alles selecteren';

  @override
  String get tt_emb_editdeselectall => 'Alles deselecteren';

  @override
  String get tt_emb_infoprintimage => 'Gecombineerd met de afgedrukte afbeelding';

  @override
  String get tt_emb_infooutputfiles => 'Er worden 3 PDF-bestanden (voor bedrukbare stof/om op transferpapier te strijkenl/voor de plaatsing) naar het USB-medium gekopieerd.';

  @override
  String get tt_emb_filemanager => 'Bestandsbeheer';

  @override
  String get tt_emb_filemanager_selectall => 'Alles selecteren';

  @override
  String get tt_emb_filemanager_selectnone => 'Alles deselecteren';

  @override
  String get tt_emb_filemanager_delete => 'Verwijderen';

  @override
  String get tt_emb_filemanager_memory => 'Sla de geselecteerde patronen op de machine op.';

  @override
  String get tt_emb_easystippling_stippling => 'Stippling/meandersteken';

  @override
  String get tt_emb_easystippling_echo => 'Echo-quiltsteken';

  @override
  String get tt_emb_easystippling_decorativefill => 'Decoratieve vulsteken';

  @override
  String get tt_emb_quitlsash_startpoint => 'Beginpunt project';

  @override
  String get tt_emb_quitlsash_endtpoint => 'Eindpunt project';

  @override
  String get tt_emb_connect_migimawari => 'De positie van het tweede patroon wijzigt met de klok mee.';

  @override
  String get tt_emb_connect_hidarimawari => 'De positie van het tweede patroon wijzigt tegen de klok in.';

  @override
  String get tt_emb_connect_rotate => 'Roteren';

  @override
  String get tt_emb_quiltborder_save => 'Geheugen';

  @override
  String get tt_mdc_pantool => 'Handje';

  @override
  String get tt_mdc_scanmenu => 'U kunt uw eigen patronen maken met gebruik van gescande afbeeldingen of beeldgegevensbestanden.';

  @override
  String get tt_mdc_datacall => 'Patroonontwerpgegevens (.pm9) laden';

  @override
  String get tt_mdc_linetool => 'Lijnfunctie';

  @override
  String get tt_mdc_lineproperty => 'Eigenschappen lijn';

  @override
  String get tt_mdc_linespoit => 'Pipet gereedschap voor lijn ';

  @override
  String get tt_mdc_linepouring => 'Vulfunctie voor lijn';

  @override
  String get tt_mdc_brushtool => 'Penseel';

  @override
  String get tt_mdc_brushproperty => 'Eigenschappen gebied';

  @override
  String get tt_mdc_brushspoit => 'Pipet gereedschap voor regio';

  @override
  String get tt_mdc_brushpouring => 'Vulfunctie voor regio';

  @override
  String get tt_mdc_painteraser => 'Gum';

  @override
  String get tt_mdc_paintstamp => 'Stempelvormen ';

  @override
  String get tt_mdc_paintsize => 'Afmeting';

  @override
  String get tt_mdc_paintrotate => 'Roteren';

  @override
  String get tt_mdc_paintflip => 'Spiegelbeeld';

  @override
  String get tt_mdc_paintduplicate => 'Kopie';

  @override
  String get tt_mdc_paintcut => 'Knippen';

  @override
  String get tt_mdc_paintpaste => 'Plakken';

  @override
  String get tt_mdc_memory => 'Sla de getekende ontwerpgegevens (.pm9) op';

  @override
  String get tt_mdc_select => 'Selecteren';

  @override
  String get tt_mdc_redo => 'Opnieuw';

  @override
  String get tt_mdc_undo => 'Ongedaan maken';

  @override
  String get tt_mdc_allclear => 'Alles Wissen';

  @override
  String get tt_mdc_lineopen => 'Lijn uit de vrije hand met open einde';

  @override
  String get tt_mdc_lineclose => 'Lijn uit de vrije hand met automatisch gesloten einde';

  @override
  String get tt_mdc_lineline => 'Rechte lijn in één streek';

  @override
  String get tt_mdc_linepolygonal => 'Veelhoek';

  @override
  String get tt_mdc_stitchzigzag => 'Zigzagsteek';

  @override
  String get tt_mdc_stitchrunning => 'Stiksteek';

  @override
  String get tt_mdc_stitchtriple => 'Drievoudige steek';

  @override
  String get tt_mdc_stitchcandle => 'Bolletjessteek';

  @override
  String get tt_mdc_stitchchain => 'Kettingsteek';

  @override
  String get tt_mdc_stitchestitch => 'E-steek (festonsteek)';

  @override
  String get tt_mdc_stitchvsitich => 'V-steek';

  @override
  String get tt_mdc_stitchmotif => 'Motiefsteken';

  @override
  String get tt_mdc_stitchnnotsew => 'Lijn zonder steek';

  @override
  String get tt_mdc_stitchzigzaglowdensity => 'Zigzagsteek voor applicatie';

  @override
  String get tt_mdc_regiontatami => 'Vulsteekpatroon';

  @override
  String get tt_mdc_regionstippling => 'Stippling/meanderpatroon';

  @override
  String get tt_mdc_regiondecorativefill => 'Decoratieve vulsteken';

  @override
  String get tt_mdc_regionnotsew => 'Geen steken';

  @override
  String get tt_mdc_stamp1 => 'Standaard vormen';

  @override
  String get tt_mdc_stamp2 => 'Gesloten vormen';

  @override
  String get tt_mdc_stamp3 => 'Open vormen';

  @override
  String get tt_mdc_stamp4 => 'Opgeslagen omtrekken';

  @override
  String get tt_mdc_stamp5 => 'Borduurraamgebieden';

  @override
  String get tt_mdc_stamp6 => 'Omtrek snijden';

  @override
  String get tt_mdc_select_rectangle => 'Kaderselectie';

  @override
  String get tt_mdc_select_continuousrectangle => 'Veelhoekselectie';

  @override
  String get tt_mdc_select_free => 'Selectie uit de vrije hand';

  @override
  String get tt_mdc_select_auto => 'Automatische selectie';

  @override
  String get tt_mdc_select_all => 'Alles selecteren';

  @override
  String get tt_mdc_memory_drawemb => 'Sla de patroonontwerpgegevens (.pm9) en de borduurgegevens (.phx) op.';

  @override
  String get tt_mdc_embset_pantool => 'Handje';

  @override
  String get tt_mdc_embset_patterninfo => 'Patrooninformatie';

  @override
  String get tt_mdc_embset_realpreview => 'Voorbeeld';

  @override
  String get tt_mdc_embset_projector => 'Projector';

  @override
  String get tt_mdc_embset_projectorsetting => 'Projectorinstellingen';

  @override
  String get tt_mdc_zigzagwidth => 'Zigzagsteekbreedte';

  @override
  String get tt_mdc_zigzagdensity => 'Dichtheid';

  @override
  String get tt_mdc_runpitch => 'Steeklengte';

  @override
  String get tt_mdc_running_undersew => 'Verstevigingssteken';

  @override
  String get tt_mdc_candlewicksize => 'Afmeting';

  @override
  String get tt_mdc_candlewickspacing => 'Afstand';

  @override
  String get tt_mdc_chainsize => 'Afmeting';

  @override
  String get tt_mdc_chainthickness => 'Dikte';

  @override
  String get tt_mdc_estitchwidth => 'Steekbreedte';

  @override
  String get tt_mdc_estitchspacing => 'Afstand';

  @override
  String get tt_mdc_estitchthickness => 'Dikte';

  @override
  String get tt_mdc_estitchflip => 'Spiegelen';

  @override
  String get tt_mdc_vstitchwidth => 'Steekbreedte';

  @override
  String get tt_mdc_vstitchspacing => 'Afstand';

  @override
  String get tt_mdc_vstitchthickness => 'Dikte';

  @override
  String get tt_mdc_vstitchflip => 'Spiegelen';

  @override
  String get tt_mdc_motifstitchsize => 'Afmeting';

  @override
  String get tt_mdc_motifstitchspacing => 'Afstand';

  @override
  String get tt_mdc_motifstitchflip => 'Spiegelen';

  @override
  String get tt_mdc_zigzagwidth_2 => 'Zigzagsteekbreedte';

  @override
  String get tt_mdc_zigzagdensity_2 => 'Dichtheid';

  @override
  String get tt_mdc_tatamiderection => 'Richting';

  @override
  String get tt_mdc_tatamidensity => 'Dichtheid';

  @override
  String get tt_mdc_tatamipullconpen => 'Trekcompensatie';

  @override
  String get tt_mdc_tatamiundersewing => 'Verstevigingssteken';

  @override
  String get tt_mdc_stiprunpitch => 'Steeklengte';

  @override
  String get tt_mdc_stipspacing => 'Tussenruimte';

  @override
  String get tt_mdc_stipdistance => 'Afstand';

  @override
  String get tt_mdc_stipsingletriple => 'Enkele/drievoudige steek';

  @override
  String get tt_mdc_decofillsize => 'Afmeting';

  @override
  String get tt_mdc_decofilldirection => 'Richting';

  @override
  String get tt_mdc_decofilloutline => 'Omtrek ter vermindering van draadknippen';

  @override
  String get tt_mdc_decofillrandomshift => 'Willekeurige verschuiving';

  @override
  String get tt_mdc_decofillpositionoffset => 'Positie verschuiving';

  @override
  String get tt_mdc_decofillthickness1 => 'Dikte';

  @override
  String get tt_mdc_decofillthickness3 => 'Dikte';

  @override
  String get tt_mdc_decofillthickness1_2 => 'Enkel-dubbel';

  @override
  String get tt_mdc_decofillthickness2_3 => 'Dubbel-drievoudig';

  @override
  String get tt_mdc_stitchlink => 'Objecten met dezelfde steekinstellingen in een keer selecteren';

  @override
  String get tt_mdc_fill_linereading => 'Lineaire gebieden en perimeters worden geconverteerd naar omtrekken. Kies de dikte van de omtrek.';

  @override
  String get tt_mdc_fill_linecolor => 'De geëxtraheerde omtrekken met de opgegeven kleur worden geconverteerd naar lijneigenschappen.';

  @override
  String get tt_emb_photostitch_backremoval => 'Achtergrond verwijderen';

  @override
  String get tt_emb_photostitch_framing => 'Afbeelding in een kader plaatsen';

  @override
  String get tt_emb_photostitch_fittoframe => 'Aanpassen aan kader';

  @override
  String get tt_emb_photostitch_backremoval_scopeplus => 'Voeg een nieuw bijsnijgebied toe:markeer met een lijn het gebied dat u wilt bijsnijden.';

  @override
  String get tt_emb_photostitch_backremoval_scopeminus => 'Wis het bijsnijgebied:markeer met een lijn het gebied dat u niet wilt bijsnijden.';

  @override
  String get tt_emb_photostitch_backremoval_erase => 'Verwijder de opgegeven tekenlijn.';

  @override
  String get tt_emb_photostitch_backremoval_trash => 'Verwijder alle tekenlijnen.';

  @override
  String get tt_emb_photostitch_backremoval_blind => 'Alle met pennen getekende lijnen weergeven/verbergen.';

  @override
  String get tt_emb_photostitch_styleusecolor => 'AAN: kleuren van stijlafbeelding gebruiken/UIT: kleuren van oorspronkelijke foto gebruiken';

  @override
  String get tt_emb_photostitch_colortune => 'Aanpassing kleur';

  @override
  String get tt_emb_photostitch_colorlis_allselect => 'Alle garenkleuren in de kleurenlijst behouden/wissen';

  @override
  String get tt_emb_photostitch_colorlis_add => 'Voeg een kleur toe aan de kleurenlijst';

  @override
  String get tt_emb_photostitch_colorlis_remove => 'Geselecteerde kleur verwijderen uit de kleurenlijst';

  @override
  String get tt_emb_photostitch_pantool => 'Handje';

  @override
  String get tt_emb_photostitch_memory => 'Geheugen';

  @override
  String get tt_emb_edit_projectorsetting => 'Projectorinstellingen';

  @override
  String get tt_emb_edit_projector => 'Projector';

  @override
  String get tt_settings_reset_3type => 'De instellingen (naaien/algemeen/borduren) terugzetten';

  @override
  String get tt_settings_screenimage_usb => 'Afbeelding van het instellingenscherm opslaan op een USB-medium';

  @override
  String get tt_camera_emb_screenshot => 'Een camerabeeld opslaan op het USB-medium.';

  @override
  String get tt_camera_emb_grid => 'Raster weergeven/verbergen';

  @override
  String get tt_camera_emb_needlepoint => 'De naaldsteekpositie weergeven/verbergen';

  @override
  String get tt_camera_util_screenshot => 'Een camerabeeld opslaan op het USB-medium.';

  @override
  String get tt_camera_util_grid => 'Raster weergeven/verbergen';

  @override
  String get tt_camera_util_needlepoint => 'De naaldsteekpositie weergeven/verbergen';
}
