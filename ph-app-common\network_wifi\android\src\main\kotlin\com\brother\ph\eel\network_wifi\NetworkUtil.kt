package com.brother.ph.eel.network_wifi

import android.annotation.SuppressLint
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.os.Build
import androidx.annotation.RequiresApi
import java.net.NetworkInterface
import java.util.Collections

object NetworkUtil {

  /**
   *これは[NetworkCapabilities]中の隠しフィールドです。現在のネットワークが部分的な接続状態にあるかどうかを示します。
   * WLANネットワークの場合、設定ページにおいてWLAN名の表示の下に「Limited connection」という文字が表示される原因となります。
   *
   * 以下の参照先で詳細情報をご確認ください:
   * [cs.android.com](https://cs.android.com/android/platform/superproject/+/android-mainline-10.0.0_r12:frameworks/base/core/java/android/net/NetworkCapabilities.java;l=314)
   */
  private const val NET_CAPABILITY_PARTIAL_CONNECTIVITY = 24

  fun longToIP(longIp: Int): String {
    val sb = StringBuilder("")
    val strip = arrayOfNulls<String>(4)
    strip[3] = (longIp ushr 24).toString()
    strip[2] = (longIp and 0x00FFFFFF ushr 16).toString()
    strip[1] = (longIp and 0x0000FFFF ushr 8).toString()
    strip[0] = (longIp and 0x000000FF).toString()
    sb.append(strip[0])
    sb.append(".")
    sb.append(strip[1])
    sb.append(".")
    sb.append(strip[2])
    sb.append(".")
    sb.append(strip[3])
    return sb.toString()
  }

  @RequiresApi(Build.VERSION_CODES.M)
  fun hasInternet(connectivityManager: ConnectivityManager, network: Network): Boolean {
    return connectivityManager.getNetworkCapabilities(network)
      ?.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED) ?: false
  }

  @SuppressLint("WrongConstant") // Suppress this because we are using a hidden constant
  @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
  fun isLimitedConnection(connectivityManager: ConnectivityManager, network: Network): Boolean {
    return connectivityManager.getNetworkCapabilities(network)
      ?.hasCapability(NET_CAPABILITY_PARTIAL_CONNECTIVITY) ?: false
  }

  /**
   * 获取设备的物理Wi-Fi MAC地址。
   *
   * 警告：此方法仅适用于拥有 `android.permission.LOCAL_MAC_ADDRESS` 权限的系统级应用。
   * 对于普通应用，在Android 6.0及以上版本将返回null或固定的 "02:00:00:00:00:00"。
   *
   * @return 格式化的MAC地址字符串 (例: "1A:2B:3C:4D:5E:6F")，如果无法获取则返回 null。
   */
  fun getDeviceMacAddress(): String? {
    try {
      // 获取所有网络接口的列表
      val allInterfaces: List<NetworkInterface> =
        Collections.list(NetworkInterface.getNetworkInterfaces())

      // 遍历所有网络接口
      for (netInterface in allInterfaces) {
        // 我们只对Wi-Fi接口感兴趣，其名称通常是 "wlan0"
        if (!netInterface.name.equals("wlan0", ignoreCase = true)) {
          continue
        }

        // 获取接口的硬件地址（MAC地址）
        val macBytes: ByteArray? = netInterface.hardwareAddress

        if (macBytes == null || macBytes.isEmpty()) {
          continue
        }

        // 将字节数组转换为格式化的MAC地址字符串
        val macAddress = StringBuilder()
        for (b in macBytes) {
          // 使用 "%02X" 格式化，确保每个字节都显示为两位十六进制数
          macAddress.append(String.format("%02X:", b))
        }

        // 移除末尾多余的冒号
        if (macAddress.isNotEmpty()) {
          macAddress.deleteCharAt(macAddress.length - 1)
        }

        return macAddress.toString()
      }
    } catch (ex: Exception) {
      // 捕获异常，例如 SecurityException
      ex.printStackTrace()
    }

    // 如果没有找到 wlan0 接口或发生异常，返回 null
    return null
  }
}
