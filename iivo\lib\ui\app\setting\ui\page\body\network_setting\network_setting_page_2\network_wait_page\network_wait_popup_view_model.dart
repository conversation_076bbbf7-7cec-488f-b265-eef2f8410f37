﻿import 'dart:async';

import 'package:common_component/common_component.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../../../network/upgrade/upgrade.dart';
import '../../../../../../../../page_route/page_route.dart';
import '../../../../../../../home/<USER>/home_model.dart';
import '../../../../../../../teaching/model/pdf_model.dart';
import '../../../../../../model/network_setting_page2_model.dart';
import 'network_wait_popup_interface.dart';

final networkWaitPopupViewModelProvider = StateNotifierProvider.family
    .autoDispose<NetworkWaitPopupModel, NetworkWaitPopupState, BuildContext>(
        (ref, context) => NetworkWaitPopupModel(ref, context));

class NetworkWaitPopupModel extends NetworkWaitPopupInterface {
  NetworkWaitPopupModel(Ref ref, context)
      : super(
            const NetworkWaitPopupState(
                isPause: true,
                text: '',
                tipText: '',
                showPauseButton: true,
                progressValue: 0),
            ref,
            context) {
    /// 初期化中にDownLoadに入る際、メカキーの状態はLibによって制御されます。
    if (HomeModel().isHomeInitCompleted == true) {
      // メカキーの状態を設定する
      TpdLibrary()
          .apiBinding
          .setMatrixEnableList(MachineKeyState.machineKeyEnableAllNG);
    }

    update();
    _startDownloading();
  }

  /// ダウンロードを開始する進行状況の既定値
  final int _defaultProgressValue = 0;
  final int _maxProgressValue = 20;

  late CancelToken cancelToken;

  ///
  /// ダウンロード進行状況を更新する
  ///
  void _updateProgressByFraction(
      int totalCount, int currentCount, double fraction) {
    final progress = (fraction * _maxProgressValue).toInt();
    final text = "($currentCount/$totalCount)";
    state = state.copyWith(progressValue: progress, text: text);
    NetworkSettingPage2Model().downloadingProgress = progress;
  }

  ///
  /// 更新のダウンロードを開始する
  ///
  void _startDownloading() {
    NetworkSettingPage2Model().downloadingProgress = 0;

    if (Upgrade().prepareDownload(downloadVoiceOnly: false) == false) {
      _exitWith(WlanUpgradeDownloadState.failed);
      return;
    }
    cancelToken = CancelToken();
    _downloadAll();
  }

  ///
  /// 新しい EULA PDF をユーザーに表示する
  ///
  void _showNewEulaPdf(Completer<bool> completer) {
    PdfModel().selectSettingPdfRoute = SettingPdfRouteEnum.endUser;
    PdfModel().isSettingFrom = false;
    PdfModel().isTeachingFrom = false;
    PagesRoute()
        .pushNamed(nextRoute: PageRouteEnum.settingPdfPreview)
        .then((eulaAgreed) {
      // ユーザーが同意してローカル EULA バージョンを更新した後、
      // ダウンロードを続行します。
      completer.complete(eulaAgreed == true);
    });
  }

  ///
  /// ダウンロードステータスを設定した後終了します
  ///
  void _exitWith(WlanUpgradeDownloadState status) {
    NetworkSettingPage2Model().downloadState = status;

    if (HomeModel().isHomeInitCompleted == true) {
      /// メカキーの状態を設定する
      TpdLibrary()
          .apiBinding
          .setMatrixEnableList(MachineKeyState.machineKeyEnableAll);
    }

    PopupNavigator.pop(context: context);
  }

  ///
  /// すべての更新ファイルをダウンロードします。
  ///
  void _downloadAll() {
    Upgrade()
        .downloadAll(_updateProgressByFraction, _setPauseable, _showNewEulaPdf,
            cancelToken)
        .then((value) {
      switch (value) {
        case DownloadExitStatus.failed:
          NetworkSettingPage2Model().downloadState =
              WlanUpgradeDownloadState.failed;
          break;
        case DownloadExitStatus.failedMemoryFull:
          NetworkSettingPage2Model().downloadState =
              WlanUpgradeDownloadState.failedMemoryFull;
          break;
        case DownloadExitStatus.paused:
          NetworkSettingPage2Model().downloadState =
              WlanUpgradeDownloadState.paused;
          break;
        case DownloadExitStatus.done:
          NetworkSettingPage2Model().downloadState =
              WlanUpgradeDownloadState.idleNoUpdate;
          break;
        case DownloadExitStatus.waitForReboot:
          NetworkSettingPage2Model().downloadState =
              WlanUpgradeDownloadState.completed;
          break;
      }

      if (HomeModel().isHomeInitCompleted == true) {
        /// メカキーの状態を設定する
        TpdLibrary()
            .apiBinding
            .setMatrixEnableList(MachineKeyState.machineKeyEnableAll);
      }
      PopupNavigator.pop(context: context);
    });
  }

  ///
  /// 一時停止ボタンを表示するかどうかを設定します
  ///
  void _setPauseable(bool isPauseable) {
    state = state.copyWith(showPauseButton: isPauseable);
  }

  @override
  Future<void> update() async {
    NetworkSettingPage2Model().downloadingProgress = _defaultProgressValue;
    state = state.copyWith(
      progressValue: _defaultProgressValue,
      tipText: AppLocalizations.of(context)!.t_err_not_turnoff,
    );
  }

  @override
  void onPauseClickButton(BuildContext context) {
    if (state.isPause == false) return;
    state = state.copyWith(isPause: false);
    cancelToken.cancel("User cancelled");
  }
}
