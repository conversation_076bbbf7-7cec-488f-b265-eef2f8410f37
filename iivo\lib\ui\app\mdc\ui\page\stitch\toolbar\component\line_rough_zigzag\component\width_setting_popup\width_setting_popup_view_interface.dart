import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

part 'width_setting_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class WidthSettingPopupState with _$WidthSettingPopupState {
  const factory WidthSettingPopupState({
    required String widthDisplayValue,
    required bool isDefaultStyle,
    required bool plusButtonValid,
    required bool minusButtonValid,
  }) = _WidthSettingPopupState;
}

abstract class WidthSettingPopupViewInterface
    extends ViewModel<WidthSettingPopupState> {
  WidthSettingPopupViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress);

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked(BuildContext context);

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
