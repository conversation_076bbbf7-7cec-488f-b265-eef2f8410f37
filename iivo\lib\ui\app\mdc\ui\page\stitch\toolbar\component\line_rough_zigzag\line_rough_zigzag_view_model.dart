import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/device_info_model.dart';
import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/line_rough_zigzag_model.dart';
import '../../../../../../model/stitch/toolbar_model.dart';
import '../../../stitch_page_view_model.dart';
import 'line_rough_zigzag_view_interface.dart';

typedef LineRoughZigzagViewModelProvider = AutoDisposeStateNotifierProvider<
    LineRoughZigzagStateViewInterface, LineRoughZigzagState>;

class LineRoughZigzagViewModel extends LineRoughZigzagStateViewInterface {
  LineRoughZigzagViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineRoughZigzagState(
              widthDisplayValue: "",
              densityDisplayValue: "",
              isWidthDefaultValue: false,
              isDensityDefaultValue: false,
            ),
            ref) {
    update();
  }

  ///
  /// width割り切れ係数
  ///
  static const double _widthDivisibilityFactor = 10.0;

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  @override
  int get defaultDensityIndex =>
      LineRoughZigzagModel().defaultDensityValue.index;
  @override
  int get defaultWidthValue => LineRoughZigzagModel().defaultWidthValue;

  @override
  void update() {
    if (!mounted) {
      return;
    }

    state = state.copyWith(
      widthDisplayValue: _getWidthDisplayValue(),
      densityDisplayValue: _getDensityDisplayValue(),
      isWidthDefaultValue: _getWidthDisplayTextStyle(),
      isDensityDefaultValue: _getDensityDisplayTextStyle(),
    );
  }

  @override
  void openDensitySettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineRoughZigzagDensity.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void openWidthSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineRoughZigzagWidth.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// 幅の表示値を取得する
  ///
  String _getWidthDisplayValue() {
    int width = LineRoughZigzagModel().getWidth();

    /// cmからmmへ
    double lineZigzagWidthValue = width / _widthDivisibilityFactor;

    if (width == LineRoughZigzagModel.widthNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      }
      return "*.***";
    }

    if (currentSelectedUnit == Unit.mm) {
      return lineZigzagWidthValue.toStringAsFixed(1);
    }

    return ToolbarModel.getDisplayInchShowValue(lineZigzagWidthValue);
  }

  ///
  /// 密度表示値を取得します
  ///
  String _getDensityDisplayValue() {
    int densityIndex = LineRoughZigzagModel().getDensityIndex();
    if (densityIndex == LineRoughZigzagModel.densityNotUpdating.index) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.**";
      }
      return "*.***";
    }

    // mdc_rough_zigzag_density_invalidのプレースホルダーを減算します
    final int densityValueIndex = MdcRoughZigzagDensity.values.length - 1;
    if (densityIndex < 0 ||
        densityIndex > densityValueIndex ||
        LineRoughZigzagModel.stitchDensityMmValue.length != densityValueIndex) {
      Log.assertTrace("MdcRoughZigzagDensity 配列が範囲外です");
    }
    // mdc_rough_zigzag_density_invalidのプレースホルダーを減算します
    final int densityIndexValue = densityIndex - 1;
    if (currentSelectedUnit == Unit.mm) {
      return LineRoughZigzagModel.stitchDensityMmValue[densityIndexValue];
    }

    return LineRoughZigzagModel.stitchDensityInchValue[densityIndexValue];
  }

  ///
  ///  幅表示テキストスタイルを取得します
  ///
  bool _getWidthDisplayTextStyle() {
    int width = LineRoughZigzagModel().getWidth();
    if (width == LineRoughZigzagModel.widthNotUpdating ||
        width == defaultWidthValue) {
      return true;
    }

    return false;
  }

  ///
  /// 密度表示テキストスタイルを取得します
  ///
  bool _getDensityDisplayTextStyle() {
    int densityIndex = LineRoughZigzagModel().getDensityIndex();
    if (densityIndex == LineRoughZigzagModel.densityNotUpdating.index ||
        densityIndex == defaultDensityIndex) {
      return true;
    }

    return false;
  }
}
