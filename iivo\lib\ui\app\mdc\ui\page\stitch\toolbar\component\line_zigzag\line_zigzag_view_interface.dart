import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

part 'line_zigzag_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class LineZigzagState with _$LineZigzagState {
  const factory LineZigzagState({
    required bool densitySettingPopup,
    required bool widthSettingPopup,
    required String widthDisplayValue,
    required String densityDisplayValue,
    required bool isWidthDefaultValue,
    required bool isDensityDefaultValue,
  }) = _LineZigzagState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class LineZigzagStateViewInterface extends ViewModel<LineZigzagState> {
  LineZigzagStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// 密度設定ポップアップを開く/閉める
  ///
  void openDensitySettingPopup(context);

  ///
  /// 密度設定ポップアップを開く/閉める
  ///
  void openWidthSettingPopup(context);

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;

  ///
  /// ラインジグザグプロパティの既定値
  ///
  int get defaultDensityValueIndex;
  int get defaultWidthValue;
}
