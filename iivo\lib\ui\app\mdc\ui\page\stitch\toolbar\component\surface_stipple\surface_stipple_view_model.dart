import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/device_info_model.dart';
import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/surface_stipple_model.dart';
import '../../../../../../model/stitch/toolbar_model.dart';
import '../../../stitch_page_view_model.dart';
import 'surface_stipple_view_interface.dart';

typedef SurfaceStippleViewModelProvider = AutoDisposeStateNotifierProvider<
    SurfaceStippleStateViewInterface, SurfaceStippleState>;

class SurfaceStippleViewModel extends SurfaceStippleStateViewInterface {
  SurfaceStippleViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const SurfaceStippleState(
              stippleDistancePopup: false,
              stippleRunPitchPopup: false,
              stippleSpacingPopup: false,
              stippleStitchPopup: false,
              mdcStichLine: StichLine.mdc_stichline_single,
              distanceDisplayValue: "",
              spacingDisplayValue: "",
              runPitchDisplayValue: "",
              singleDisplayDefault: "",
              spacingDisplayDefault: false,
              runPitchDisplayDefault: false,
              distanceDisplayDefault: false,
              tMdcStichLineType: false,
            ),
            ref) {
    update();
  }

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  ///
  /// ステップ量
  ///
  final int _stepValue = 10;

  @override
  int get defaultDistanceValue => SurfaceStippleModel().distanceDefaultValue;
  @override
  StichLine get defaultStitchValue => SurfaceStippleModel.stichLineTypes;
  @override
  int get defaultRunPitchValue => SurfaceStippleModel().runPitchDefaultValue;
  @override
  int get defaultSpacingValue => SurfaceStippleModel().spacingDefaultValue;

  @override
  void update() {
    state = state.copyWith(
      distanceDisplayValue: _getDistanceDisplayValue(),
      spacingDisplayValue: _getSpacingDisplayValue(),
      runPitchDisplayValue: _getRunPitchDisplayValue(),
      spacingDisplayDefault: _getSpacingDisplayDefault(),
      runPitchDisplayDefault: _getRunPitchDisplayDefault(),
      distanceDisplayDefault: _getDistanceDisplayDefault(),
      singleDisplayDefault: _getSingleDisplayValue(),
    );
    if (SurfaceStippleModel().getStitchLine() ==
        StichLine.mdc_stichline_invalid) {
      return;
    }
    state = state.copyWith(
        tMdcStichLineType:
            SurfaceStippleModel().getStitchLine() == defaultStitchValue
                ? true
                : false);
  }

  @override
  void onCandleRunPitchClick(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceStippleRunPitch.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void onCandleSpacingClick(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceStippleSpacing.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void onCandleDistanceClick(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceStippleDistance.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void onCandleStitchClick(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceStippleStitch.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// RunPitchの表示値を取得する
  ///
  String _getRunPitchDisplayValue() {
    int runPitch = SurfaceStippleModel().getRunPitch();

    /// cmからmmへ
    double stippleRunPitchValue = runPitch / _stepValue;
    if (runPitch == SurfaceStippleModel.runPitchNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }
    if (currentSelectedUnit == Unit.mm) {
      return stippleRunPitchValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(stippleRunPitchValue);
  }

  ///
  /// Spacingの表示値を取得する
  ///
  String _getSpacingDisplayValue() {
    int spacing = SurfaceStippleModel().getSpacing();

    /// cmからmmへ
    double stippleSpacingValue = spacing / _stepValue;
    if (spacing == SurfaceStippleModel.spacingNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }
    if (currentSelectedUnit == Unit.mm) {
      return stippleSpacingValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(stippleSpacingValue);
  }

  ///
  /// Distanceの表示値を取得する
  ///
  String _getDistanceDisplayValue() {
    int distance = SurfaceStippleModel().getDistance();

    /// cmからmmへ
    double stippleDistanceValue = distance / _stepValue;
    if (distance == SurfaceStippleModel.distanceNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }
    if (currentSelectedUnit == Unit.mm) {
      return stippleDistanceValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(stippleDistanceValue);
  }

  ///
  /// RunPitchを表示するテキストスタイルを取得するには
  ///
  bool _getRunPitchDisplayDefault() {
    int runPitch = SurfaceStippleModel().getRunPitch();
    if (runPitch == SurfaceStippleModel.runPitchNotUpdating ||
        SurfaceStippleModel().getRunPitch() == defaultRunPitchValue) {
      return true;
    }
    return false;
  }

  ///
  /// Spacingを表示するテキストスタイルを取得するには
  ///
  bool _getSpacingDisplayDefault() {
    int spacing = SurfaceStippleModel().getSpacing();
    if (spacing == SurfaceStippleModel.spacingNotUpdating ||
        SurfaceStippleModel().getSpacing() == defaultSpacingValue) {
      return true;
    }
    return false;
  }

  ///
  /// Distanceを表示するテキストスタイルを取得するには
  ///
  bool _getDistanceDisplayDefault() {
    int distance = SurfaceStippleModel().getDistance();
    if (distance == SurfaceStippleModel.distanceNotUpdating ||
        SurfaceStippleModel().getDistance() == defaultDistanceValue) {
      return true;
    }
    return false;
  }

  ///
  /// スティップリング：縫い重ね回数
  ///
  String _getSingleDisplayValue() {
    if (SurfaceStippleModel().getStitchLine() ==
        SurfaceStippleModel.stitchNotUpdating) {
      return "**";
    } else {
      return "";
    }
  }
}
