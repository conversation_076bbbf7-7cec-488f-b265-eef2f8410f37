import '../../../../global_popup/global_popup_route.dart';
import '../../../../global_popup/global_popup_route_enum.dart';
import '../../../../global_popup/global_popups/err_mdc_not_change_setting_memory_over/err_mdc_not_change_setting_memory_over_view_model.dart';
import 'draw_region_model.dart';

//
/// リージョン情報選択方法
///
enum RegionSelectType {
  pre,
  next,
  same,
}

class ToolbarModel {
  ToolbarModel._internal();

  factory ToolbarModel() => _instance;
  static final ToolbarModel _instance = ToolbarModel._internal();

  ///
  /// 現在選択されているオブジェクトの次、または前または同質のオブジェクトを選択する
  ///
  void selectRegionInfo(RegionSelectType selType) {
    switch (selType) {
      case RegionSelectType.pre:
        DrawRegionModel().moveToPreRegion();
        DrawRegionModel().setOperation(Operation.single);
        DrawRegionModel().setStitchModelValues(false);
        break;

      case RegionSelectType.next:
        DrawRegionModel().moveToNextRegion();
        DrawRegionModel().setOperation(Operation.single);
        DrawRegionModel().setStitchModelValues(false);
        break;

      case RegionSelectType.same:
        if (DrawRegionModel().getOperation() == Operation.single) {
          DrawRegionModel().setOperation(Operation.multiLink);
          DrawRegionModel().setStitchModelValues(true);
        } else {
          DrawRegionModel().setOperation(Operation.single);
          DrawRegionModel().setStitchModelValues(false);
        }
        DrawRegionModel().changePatternSelectedState();
        break;

      default:
        break;
    }
  }

  ///
  /// mmからinchまでの単位変換
  ///
  static String getDisplayInchShowValue(double value) {
    double outData;
    final double data = value;

    /// 0.1mm単位の縫製データを0.0001mm単位に変換する
    outData = data * 10000;

    /// 0.0001mm単位の縫製データを0.001インチ単位に変換する
    outData /= 25;

    /// インチ単位の小数第2位までを有効にするため、0.001位の桁に四捨五入のための補正値を加える
    if (data >= 0) {
      ///  正の数
      outData += 5;
    } else {
      /// 負の数
      outData -= 5;
    }

    /// インチ単位の小数第2位まで縫製データ値を求める
    outData /= 10000;

    return outData
        .toString()
        .substring(0, outData.toString().split('.').first.length + 4);
  }

  ///
  /// 設定が変更できませんでした。縫い方設定の記憶領域が不足しています。
  ///
  void showNotChangeSettingMemoryOverError() {
    GlobalPopupRoute().updateErrorState(
      nextRoute: GlobalPopupRouteEnum.ERR_MDC_NOT_CHANGE_SETTING_MEMORY_OVER,
      arguments: ErrMdcNotChangeSettingMemoryOverArgument(
        onOKButtonClicked: () => GlobalPopupRoute().resetErrorState(),
      ),
    );
  }

  ///
  /// テキスト信号 'X'
  ///
  static final xCharCode = String.fromCharCode(0x00d7);
}
