import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_motif_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'motif_flip_popup_view_interface.dart';

final motifFlipViewModelProvider = StateNotifierProvider.autoDispose<
    MotifFlipPopupStateViewInterface,
    MotifFlipPopupState>((ref) => MotifFlipViewModel(ref));

class MotifFlipViewModel extends MotifFlipPopupStateViewInterface {
  MotifFlipViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const MotifFlipPopupState(
              isSelectInside: false,
              isSelectOutside: false,
            ),
            ref) {
    update();
  }

  @override
  void update() {
    state = state.copyWith(
      isSelectInside: _getInsideState(),
      isSelectOutside: _getOutsideState(),
    );
  }

  @override
  FlipSide get defaultValue => LineMotifModel().lineFlipTypes;

  ///
  /// フリップタイプが選択されています
  ///
  bool _hasSelectedFlipType =
      LineMotifModel().getLineFlip() != LineMotifModel.sideNotUpdating
          ? false
          : true;

  ///
  /// フリップタイプ値
  ///
  FlipSide _flipTypeValue = LineMotifModel().getLineFlip();

  @override
  void onInsideButtonClick() {
    if (state.isSelectInside) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    _hasSelectedFlipType = false;
    _flipTypeValue = FlipSide.flip_inside;

    /// view更新
    state = state.copyWith(
      isSelectInside: true,
      isSelectOutside: false,
    );
  }

  @override
  void onOutsideButtonClick() {
    if (state.isSelectOutside) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    _hasSelectedFlipType = false;
    _flipTypeValue = FlipSide.flip_outside;

    /// view更新
    state = state.copyWith(
      isSelectInside: false,
      isSelectOutside: true,
    );
  }

  ///
  /// ボタンの選択状態を取得
  ///
  bool _getInsideState() {
    if (_hasSelectedFlipType) {
      return false;
    }
    if (_flipTypeValue == defaultValue) {
      return true;
    } else {
      return false;
    }
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineMotifFlip.toString());
    if (_hasSelectedFlipType) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    FlipSide flipTypeValue = LineMotifModel().getLineFlip();

    /// Model 更新
    LineMotifModel().setLineFlip(_flipTypeValue);
    if (LineMotifModel().setMdcMotifLinePropertyValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (flipTypeValue != _flipTypeValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// ボタンの選択状態を取得
  ///
  bool _getOutsideState() {
    if (_hasSelectedFlipType) {
      return false;
    }
    if (_flipTypeValue == defaultValue) {
      return false;
    } else {
      return true;
    }
  }
}
