import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../../../model/device_info_model.dart';
import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/line_zigzag_model.dart';
import '../../../../../../model/stitch/toolbar_model.dart';
import '../../../stitch_page_view_model.dart';
import 'line_zigzag_view_interface.dart';

typedef LineZigzagViewModelProvider = AutoDisposeStateNotifierProvider<
    LineZigzagStateViewInterface, LineZigzagState>;

class LineZigzagViewModel extends LineZigzagStateViewInterface {
  LineZigzagViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineZigzagState(
              densitySettingPopup: false,
              widthSettingPopup: false,
              widthDisplayValue: "",
              densityDisplayValue: "",
              isWidthDefaultValue: false,
              isDensityDefaultValue: false,
            ),
            ref) {
    update();
  }

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  @override
  int get defaultDensityValueIndex =>
      LineZigzagModel().defaultDensityValue.index;
  @override
  int get defaultWidthValue => LineZigzagModel().defaultWidthValue;

  @override
  void update() {
    if (!mounted) {
      return;
    }
    state = state.copyWith(
      widthDisplayValue: _getWidthDisplayValue(),
      densityDisplayValue: _getDensityDisplayValue(),
      isWidthDefaultValue: _getWidthDisplayTextStyle(),
      isDensityDefaultValue: _getDensityDisplayTextStyle(),
    );
  }

  @override
  void openDensitySettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineZigzagDensity.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void openWidthSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineZigzagWidth.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// 幅の表示値を取得する
  ///
  String _getWidthDisplayValue() {
    int width = LineZigzagModel().getWidth();

    /// cmからmmへ
    double lineZigzagWidthValue = width / 10.0;

    if (width == LineZigzagModel.widthNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      }
      return "*.***";
    }

    if (currentSelectedUnit == Unit.mm) {
      return lineZigzagWidthValue.toStringAsFixed(1);
    }

    return ToolbarModel.getDisplayInchShowValue(lineZigzagWidthValue);
  }

  ///
  /// 密度表示値を取得します
  ///
  String _getDensityDisplayValue() {
    int densityIndex = LineZigzagModel().getDensityIndex();
    if (densityIndex == LineZigzagModel.densityNotUpdating.index) {
      return "*";
    }

    return LineZigzagModel.densityList[densityIndex].toString();
  }

  ///
  ///  幅表示テキストスタイルを取得します
  ///
  bool _getWidthDisplayTextStyle() {
    int lineZigzagWidthValue = LineZigzagModel().getWidth();
    if (lineZigzagWidthValue == defaultWidthValue ||
        lineZigzagWidthValue == LineZigzagModel.widthNotUpdating) {
      return true;
    }

    return false;
  }

  ///
  /// 密度表示テキストスタイルを取得します
  ///
  bool _getDensityDisplayTextStyle() {
    int densityIndex = LineZigzagModel().getDensityIndex();
    if (densityIndex == defaultDensityValueIndex ||
        densityIndex == LineZigzagModel.densityNotUpdating.index) {
      return true;
    }

    return false;
  }
}
