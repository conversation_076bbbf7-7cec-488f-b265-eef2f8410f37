import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/device_info_model.dart';
import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/line_e_stitch_model.dart';
import '../../../../../../model/stitch/toolbar_model.dart';
import '../../../stitch_page_view_model.dart';
import 'line_e_stitch_view_interface.dart';

typedef Unit = DisplayUnit;

/// 読み出したSizeデータの変換倍率 '読み出したデータの単位/10=mm'
const double _conversionRate = 10.0;

typedef LineEStitchViewModelProvider = AutoDisposeStateNotifierProvider<
    LineEStitchViewInterface, LineEStitchState>;

class LineEStitchViewModel extends LineEStitchViewInterface {
  LineEStitchViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineEStitchState(
              widthDisplayValue: "",
              isWidthDefaultValue: false,
              spaceDisplayValue: "",
              isSpaceDefaultValue: false,
              thicknessDisplayValue: "",
              isThicknessDefaultValue: false,
              flipDisplayValue: "",
              isFlipDefaultValue: false,
            ),
            ref) {
    update();
  }

  ///
  /// 単位取得する
  ///
  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  ///
  /// E ピン プロパティの既定値
  ///
  final _defaultWidthValue = LineEStitchModel().widthDefaultValue;
  final _defaultSpaceValue = LineEStitchModel().spaceDefaultValue;
  final _defaultThicknessValue = LineEStitchModel().thicknessDefaultValue;

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    FlipSide flip = LineEStitchModel().getFlip();
    state = state.copyWith(
      widthDisplayValue: _getWidthDisplayValue(),
      isWidthDefaultValue: _getWidthDisplayTextStyle(),
      spaceDisplayValue: _getSpaceDisplayValue(),
      isSpaceDefaultValue: _getSpaceDisplayTextStyle(),
      thicknessDisplayValue: _getThicknessDisplayValue(),
      isThicknessDefaultValue: _getThicknessDisplayTextStyle(),
      flipDisplayValue: _getFlipDisplayValue(),
      isFlipDefaultValue: _getFlipDisplayTextStyle(),
    );
    if (flip != FlipSide.flip_invalid) {
      state = state.copyWith(
          isFlipSelect: flip == FlipSide.flip_inside ? true : false);
    }
  }

  ///
  /// 幅設定ポップアップウィンドウを開きます
  ///
  @override
  void openWidthSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineEStitchWidth.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// 間隔設定ポップアップウィンドウを開く
  ///
  @override
  void openSpaceSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineEStitchSpace.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// 厚さ設定ポップアップウィンドウを開く
  ///
  @override
  void openThicknessSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineEStitchThickness.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  ///
  /// 方向設定ポップアップウィンドウを開きます
  ///
  @override
  void openFlipSettingPopup(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.lineEStitchFlip.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  String get textSignal => ToolbarModel.xCharCode;

  ///
  /// 幅の表示値を取得する
  ///
  String _getWidthDisplayValue() {
    int width = LineEStitchModel().getWidth();

    /// cmからmmへ
    double lineEStitchWidthValue = width / _conversionRate;

    if (width == LineEStitchModel.widthNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return lineEStitchWidthValue.toStringAsFixed(1);
    }

    return ToolbarModel.getDisplayInchShowValue(lineEStitchWidthValue);
  }

  ///
  /// 幅表示テキストスタイルを取得します
  ///
  bool _getWidthDisplayTextStyle() {
    int width = LineEStitchModel().getWidth();
    if (width == _defaultWidthValue ||
        width == LineEStitchModel.widthNotUpdating) {
      return true;
    }

    return false;
  }

  ///
  /// 間隔の表示値を取得します
  ///
  String _getSpaceDisplayValue() {
    int space = LineEStitchModel().getSpace();

    /// cmからmmへ
    double lineEStitchSpaceValue = space / _conversionRate;

    if (space == LineEStitchModel.spacingNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return lineEStitchSpaceValue.toStringAsFixed(1);
    }

    return ToolbarModel.getDisplayInchShowValue(lineEStitchSpaceValue);
  }

  ///
  /// 間隔表示テキスト スタイルを取得します
  ///
  bool _getSpaceDisplayTextStyle() {
    int space = LineEStitchModel().getSpace();
    if (space == _defaultSpaceValue ||
        space == LineEStitchModel.spacingNotUpdating) {
      return true;
    }

    return false;
  }

  ///
  /// 厚さの表示値を取得します
  ///
  String _getThicknessDisplayValue() {
    int thickNess = LineEStitchModel().getThickness();
    if (thickNess == LineEStitchModel.thickNessNotUpdating) {
      return "*";
    }
    return LineEStitchModel().getThickness().toString();
  }

  ///
  /// 太さ表示のテキスト スタイルを取得します
  ///
  bool _getThicknessDisplayTextStyle() {
    int thickNess = LineEStitchModel().getThickness();
    if (thickNess == _defaultThicknessValue ||
        thickNess == LineEStitchModel.thickNessNotUpdating) {
      return true;
    }

    return false;
  }

  ///
  /// 方向の表示値を取得します
  ///
  String _getFlipDisplayValue() {
    if (LineEStitchModel().getFlip() == LineEStitchModel.sideNotUpdating) {
      return "**";
    } else {
      return "";
    }
  }

  ///
  /// 方向表示のテキスト スタイルを取得します
  ///
  bool _getFlipDisplayTextStyle() =>
      LineEStitchModel().getFlip() == LineEStitchModel.sideNotUpdating
          ? true
          : false;
}
