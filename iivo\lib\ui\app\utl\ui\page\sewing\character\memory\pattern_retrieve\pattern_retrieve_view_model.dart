import 'dart:ffi';
import 'dart:io';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:path/path.dart';
import 'package:usb_manager/usb_manager.dart';

import '../../../../../../../../../memory/memory.dart';
import '../../../../../../../../../model/device_memory_model.dart';
import '../../../../../../../../../model/handel_model.dart';
import '../../../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../../global_popup/global_popups/err_emb_too_much_selected/err_emb_too_much_selected_view_model.dart';
import '../../../../../../../../global_popup/global_popups/err_myi_delete_ok_for_normal/err_myi_delete_ok_for_normal_view_model.dart';
import '../../../../../../../../global_popup/global_popups/err_usb_media_set/err_usb_media_set_view_model.dart';
import '../../../../../../model/character_memory_model.dart';
import '../../../../../../model/pattern_model.dart';
import '../../../../../../model/pattern_status_model.dart';
import '../../character_view_interface.dart';
import '../../character_view_model.dart';
import 'pattern_retrieve_view_interface.dart';

final patternRetrieveViewModelProvider = StateNotifierProvider.autoDispose<
    PatternRetrieveViewModelInterface,
    PatternRetrieveState>((ref) => PatternRetrieveViewModel(ref));

class PatternRetrieveViewModel extends PatternRetrieveViewModelInterface {
  PatternRetrieveViewModel(
    AutoDisposeStateNotifierProviderRef ref,
  ) : super(const PatternRetrieveState(), ref) {
    _deviceKind = DeviceKind.internalStorage;
    MemoryModel().isUsb1Selected = false;
    MemoryModel().loadFileInMachineList().then((_) => _updateItemStyle());

    ref.listen(
      fireImmediately: true,
      appDisplayUtlStateProvider.select((value) => (
            isConnectedDF: value.utlFuncSetting.ref.isConnectedDF,
            patternNum: value.utlFuncSetting.ref.patternNum
          )),
      (_, nextState) {
        _isConnectedDF = nextState.isConnectedDF;

        if (nextState.patternNum == 0) {
          _selectFilePath = '';
          _selectedPatternIndex = noTargetFound;
        } else {
          /// Do nothing
        }

        if (PatternDataModel().isFileOperationOn == false) {
          if (_isConnectedDF) {
            _patternsItemStyle = List.filled(
                MemoryModel().loadFileDataList.length, ButtonState.disable);
            state = state.copyWith(patternsStyle: _patternsItemStyle);
          } else {
            _patternsItemStyle = List.filled(
                MemoryModel().loadFileDataList.length, ButtonState.normal);

            if (File(_selectFilePath).existsSync()) {
              for (var i = 0; i < MemoryModel().loadFilePathList.length; i++) {
                if (MemoryModel().loadFilePathList[i] == _selectFilePath) {
                  _selectedPatternIndex = i;
                }
              }
            } else {
              _selectFilePath = '';
              _selectedPatternIndex = noTargetFound;
            }

            state = state.copyWith(
              patternsStyle: _patternsItemStyle,
              selectedPatternIndex: _selectedPatternIndex,
            );
          }
        } else {
          /// Do nothing
        }
      },
    );
    DeviceMemoryModel().registerAutoDisposeUsbChangeListener(ref, (value) {
      if (_deviceKind == DeviceKind.usb) {
        if (value.isNotEmpty) {
          state = state.copyWith(usbInfoList: value);
        } else {
          // no-op
        }
        DeviceMemoryModel().handleUsbChanges(_usbPath, value, _onUsbChanged);
      } else {
        // no-op
      }
    });
  }

  ///
  /// USBの抜き差しをリアルタイムで処理します。
  ///
  void _onUsbChanged(bool isUsb1Selected, bool isUsbSwitched) {
    final usbInfo = UsbManager().getUsbInfoListSync();
    if (isUsbSwitched) {
      if (usbInfo.isEmpty) {
        onMachineButtonClicked();
      } else {
        MemoryModel().isUsb1Selected = false;
        onUsbButtonClicked();
      }
    } else {
      if (usbInfo.isNotEmpty) {
        MemoryModel().isUsb1Selected = isUsb1Selected;
        state = state.copyWith(isUSB1: isUsb1Selected);
      } else {
        /// Do Nothing
      }
    }
  }

  String? _usbPath;

  @override
  int get getUsbButton1 => usbButton1;

  @override
  int get getUsbButton2 => usbButton2;

  ///
  /// USBディスプレイの最大数
  ///
  final int _maxUsbDisplayNumber = 2;

  ///
  /// 画面選択した模様パス
  ///
  String _selectFilePath = "";

  ///
  /// 画面選択した模様
  ///
  int _selectedPatternIndex = noTargetFound;

  ///
  /// メモリへ書き出し先の種類
  ///
  DeviceKind _deviceKind = DeviceKind.internalStorage;

  ///
  /// 表示用フォルダ名
  ///
  String _directoryName = "";

  ///
  /// 表示用フォルダ名
  ///
  bool _isConnectedDF = false;

  ///
  /// 模様の表示状態
  ///
  List<ButtonState> _patternsItemStyle =
      List.filled(MemoryModel().loadFileDataList.length, ButtonState.normal);

  @override
  Future<void> updateFileList(DeviceKind kind, {USBInfo? usbInfo}) async {
    if (state.deviceKind != kind) {
      return;
    }
    switch (kind) {
      case DeviceKind.internalStorage:
        await MemoryModel().loadFileInMachineList();
        _updateItemStyle();
        break;
      case DeviceKind.usb:
        if (usbInfo == null ||
            _usbPath != usbInfo.usbPath ||
            basename(_directoryName) != DeviceMemoryModel.usbSavedFolder) {
          // USBのパスまたはディレクトリ名が一致しない場合は、更新を行いません
          return;
        }

        await MemoryModel().loadSelectedDirectoryList(DeviceKind.usb,
            join(usbInfo.usbPath, DeviceMemoryModel.usbSavedFolder));
        _updateItemStyle();
        break;
      default:
        break;
    }
  }

  ///
  /// 更新模様の表示状態
  ///
  void _updateItemStyle() {
    Log.debugTrace(
        "MemoryModel().loadFileDataList.length=${MemoryModel().loadFileDataList.length}");
    _patternsItemStyle = List.filled(MemoryModel().loadFileDataList.length,
        _isConnectedDF ? ButtonState.disable : ButtonState.normal);
    if (PatternStatusModel().getCommonStatus().isEmpty) {
      PatternDataModel().currentSelectedMemoryPatternIndex = noTargetFound;
    }
    if (PatternDataModel().currentSelectedMemoryPatternIndex != -1) {
      _selectedPatternIndex =
          PatternDataModel().currentSelectedMemoryPatternIndex!;
    }
    update();
  }

  @override
  void update() {
    if (MemoryModel().isMemoryFileListEmpty()) {
      PatternDataModel().isFileOperationOn = false;
    }

    /// view 更新
    state = state.copyWith(
      isUSB1: MemoryModel().isUsb1Selected,
      isEnglish: DeviceLibrary().apiBinding.getLanguage().value ==
          Language.LANG_ENGLISH,
      patternsStyle: _patternsItemStyle,
      selectedPatternIndex: _selectedPatternIndex,
      directoryName: _directoryName,
      deviceKind: _deviceKind,
      loadFolderList: MemoryModel().loadFolderList,
      loadFileDataList: MemoryModel().loadFileDataList,
      fileOperationButtonState: _getFileOperationButtonState(),
      fileOperationPopup: PatternDataModel().isFileOperationOn,
      selectNoneButtonState: _getSelectNoneButtonState(),
      deleteButtonState: _getDeleteButtonState(),
      memoryButtonState: _getMemoryButtonState(),
      freeSpaceSize:
          _getDeviceFreeSpace(_deviceKind, MemoryModel().isUsb1Selected),
    );
  }

  @override
  void onMachineButtonClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (_deviceKind == DeviceKind.internalStorage) {
      return;
    }
    scrollController.dispose();
    scrollController = ScrollController();
    PatternDataModel().isEditMenuOn = false;
    PatternDataModel().isFileOperationOn = false;
    _showWaitPopup();

    /// Model更新
    MemoryModel().loadFileInMachineList().then((error) {
      if (error == AccessError.none) {
        /// View更新
        MemoryModel().isUsb1Selected = false;
        _deviceKind = UtlDeviceKind.internalStorage;
        _selectedPatternIndex = noTargetFound;
        _directoryName = "";
        MemoryModel().selectNoneFiles();
        _patternsItemStyle = List.filled(MemoryModel().loadFileDataList.length,
            _isConnectedDF ? ButtonState.disable : ButtonState.normal);
        update();
      } else {
        HandelModel.handleMemoryAccessError(error);
        return;
      }

      _resetWaitPopup();
    });

    ref
        .read(characterViewInfoProvider.notifier)
        .navigator
        .maybeRemoveRoute(routeName: PopupEnum.editMenu.toString());
    ref
        .read(characterViewModelProvider.notifier)
        .updateCharacterPageByChild(CharacterModuleType.memoryRetrieve);
  }

  @override
  void onUsbButtonClicked() {
    DeviceMemoryModel().getMemoryUSBInfo().then((value) {
      if (value.isEmpty) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_USB_MEDIA_SET,
          arguments: ErrUsbMediaSetArgument(
            onOkButtonClicked: () {
              DeviceErrorCode deviceError =
                  GlobalPopupRoute().resetErrorState();
              if (deviceError == DeviceErrorCode.devNoError) {
                /// do nothing
              } else {
                SystemSoundPlayer().play(SystemSoundEnum.invalid);
              }
            },
          ),
        );
        return;
      }
      state = state.copyWith(usbInfoList: value);
      onUsb1ButtonClicked();
    });
  }

  @override
  void onUsb1ButtonClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (MemoryModel().isUsb1Selected == true) {
      return;
    }
    MemoryModel().isUsb1Selected = true;

    scrollController.dispose();
    scrollController = ScrollController();
    PatternDataModel().isEditMenuOn = false;
    PatternDataModel().isFileOperationOn = false;

    _showWaitPopup();

    /// Model更新
    MemoryModel()
        .loadSelectedDirectoryList(
            DeviceKind.usb, "", state.usbInfoList[usbButton1].usbPath)
        .then((error) {
      if (error == AccessError.none) {
        /// View更新
        _deviceKind = UtlDeviceKind.usb;
        _selectedPatternIndex = noTargetFound;
        _directoryName = "";
        _usbPath = state.usbInfoList[usbButton1].usbPath;
        MemoryModel().selectNoneFiles();
        _patternsItemStyle = List.filled(MemoryModel().loadFileDataList.length,
            _isConnectedDF ? ButtonState.disable : ButtonState.normal);
        update();
      } else {
        HandelModel.handleMemoryAccessError(error);
        return;
      }
      _resetWaitPopup();
    });
    ref
        .readAutoNotifierIfExists(characterViewInfoProvider)
        ?.navigator
        .maybeRemoveRoute(routeName: PopupEnum.editMenu.toString());
    ref
        .read(characterViewModelProvider.notifier)
        .updateCharacterPageByChild(CharacterModuleType.memoryRetrieve);
  }

  @override
  void onUsb2ButtonClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (MemoryModel().isUsb1Selected == false) {
      return;
    }
    if (state.usbInfoList.length < _maxUsbDisplayNumber) {
      return;
    }
    MemoryModel().isUsb1Selected = false;

    scrollController.dispose();
    scrollController = ScrollController();
    PatternDataModel().isEditMenuOn = false;
    PatternDataModel().isFileOperationOn = false;

    _showWaitPopup();

    /// Model更新
    MemoryModel()
        .loadSelectedDirectoryList(
            DeviceKind.usb, "", state.usbInfoList[usbButton2].usbPath)
        .then((error) {
      if (error == AccessError.none) {
        /// View更新
        _deviceKind = UtlDeviceKind.usb;
        _selectedPatternIndex = noTargetFound;
        _directoryName = "";
        _usbPath = state.usbInfoList[usbButton2].usbPath;
        MemoryModel().selectNoneFiles();
        _patternsItemStyle = List.filled(MemoryModel().loadFileDataList.length,
            _isConnectedDF ? ButtonState.disable : ButtonState.normal);
        update();
      } else {
        HandelModel.handleMemoryAccessError(error);
        return;
      }
      _resetWaitPopup();
    });

    ref
        .readAutoNotifierIfExists(characterViewInfoProvider)
        ?.navigator
        .maybeRemoveRoute(routeName: PopupEnum.editMenu.toString());
    ref
        .read(characterViewModelProvider.notifier)
        .updateCharacterPageByChild(CharacterModuleType.memoryRetrieve);
  }

  @override
  void onwLanButtonClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (_deviceKind == DeviceKind.wLan) {
      return;
    }
    scrollController.dispose();
    scrollController = ScrollController();
    PatternDataModel().isEditMenuOn = false;
    PatternDataModel().isFileOperationOn = false;

    _showWaitPopup();

    Future.delayed(
      const Duration(milliseconds: 150),
    ).then((value) async {
      if (!mounted) {
        _resetWaitPopup();
        return;
      }

      /// Model更新
      MemoryModel().loadSelectedDirectoryList(DeviceKind.wLan).then((error) {
        if (error == AccessError.none) {
          /// View更新
          MemoryModel().isUsb1Selected = false;
          _deviceKind = UtlDeviceKind.wLan;
          _selectedPatternIndex = noTargetFound;
          _directoryName = "";
          MemoryModel().selectNoneFiles();
          _patternsItemStyle = List.filled(
              MemoryModel().loadFileDataList.length,
              _isConnectedDF ? ButtonState.disable : ButtonState.normal);
          update();
        } else {
          HandelModel.handleMemoryAccessError(error);
          return;
        }
        _resetWaitPopup();
      });
    });
    ref
        .readAutoNotifierIfExists(characterViewInfoProvider)
        ?.navigator
        .maybeRemoveRoute(routeName: PopupEnum.editMenu.toString());

    ref
        .read(characterViewModelProvider.notifier)
        .updateCharacterPageByChild(CharacterModuleType.memoryRetrieve);
  }

  @override
  Future<void> onPatternClick(int patternIndex) async {
    if (state.patternsStyle.elementAtOrNull(patternIndex) ==
        ButtonState.disable) {
      // ここではinvalid音を再生せず、下部のERRORポップアップが音を管理する
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    String filePath = MemoryModel().loadFilePathList[patternIndex];
    final response =
        await MemoryModel().catchUsbError(state.deviceKind, filePath);
    if (response != AccessError.none) {
      /// Model更新
      HandelModel.handleMemoryAccessError(response);
      return;
    }
    if (_deviceKind == DeviceKind.internalStorage) {
      PatternDataModel().currentSelectedMemoryPatternIndex = patternIndex;
    } else {
      PatternDataModel().currentSelectedMemoryPatternIndex = noTargetFound;
    }
    _selectedPatternIndex = patternIndex;

    /// ファイル操作キーがOFFの場合は、ログインパターンをクリックします
    if (PatternDataModel().isFileOperationOn == false) {
      _selectFilePath = filePath;

      final bool loginRet = await DeviceMemoryModel().beginUsbTransaction(
          filePath,
          () => PatternDataModel().loginMemoryPattern(
              loginCategoryIndexMemory, patternIndex, filePath));

      if (loginRet == false) {
        return;
      }
      ref
          .read(characterViewModelProvider.notifier)
          .updateCharacterPageByChild(CharacterModuleType.patternSelector);

      /// view 更新
      for (int i = 0; i < _patternsItemStyle.length; i++) {
        _patternsItemStyle[i] = ButtonState.normal;
      }
    } else {
      /// ファイル操作キーがONの場合
      for (int i = 0; i < _patternsItemStyle.length; i++) {
        if (_selectedPatternIndex == i) {
          if (_patternsItemStyle[i] == ButtonState.select) {
            _patternsItemStyle[i] = ButtonState.normal;

            /// 選択解除された模様
            MemoryModel().removeSelectedFile(MemoryModel().loadFilePathList[i]);
          } else if (_patternsItemStyle[i] == ButtonState.normal) {
            _patternsItemStyle[i] = ButtonState.select;

            /// 模様を選択します
            MemoryModel().selectFile(MemoryModel().loadFilePathList[i]);
          }
        }
      }
    }

    update();
  }

  @override
  void onFolderClicked(int index) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    String usbPath = state.deviceKind == UtlDeviceKind.usb
        ? state.isUSB1
            ? state.usbInfoList[usbButton1].usbPath
            : state.usbInfoList[usbButton2].usbPath
        : "";

    if (PatternDataModel().isFileOperationOn == true) {
      return;
    }

    scrollController.dispose();
    scrollController = ScrollController();

    if (index >= state.loadFolderList.length) {
      return;
    }

    final String displayDirectoryName =
        "${state.directoryName}/${state.loadFolderList[index]}";

    final String subPath =
        DeviceMemoryModel().displayPathToAccessPath(displayDirectoryName);

    _showWaitPopup();

    /// Model更新
    MemoryModel()
        .loadSelectedDirectoryList(state.deviceKind, subPath, usbPath)
        .then((error) {
      if (error == AccessError.none) {
        /// View更新
        _selectedPatternIndex = noTargetFound;
        _directoryName = displayDirectoryName;
        MemoryModel().selectNoneFiles();
        _patternsItemStyle = List.filled(MemoryModel().loadFileDataList.length,
            _isConnectedDF ? ButtonState.disable : ButtonState.normal);
        update();
      } else {
        /// Model更新
        MemoryModel().loadFileInMachineList().then((error) {
          if (error == AccessError.none) {
            /// View更新
            _deviceKind = UtlDeviceKind.internalStorage;
            _selectedPatternIndex = noTargetFound;
            _directoryName = "";
            _patternsItemStyle = List.filled(
                MemoryModel().loadFileDataList.length,
                _isConnectedDF ? ButtonState.disable : ButtonState.normal);
            update();
          }
        });
        HandelModel.handleMemoryAccessError(error);
        return;
      }
      _resetWaitPopup();
    });

    ref
        .read(characterViewModelProvider.notifier)
        .updateCharacterPageByChild(CharacterModuleType.memoryRetrieve);
  }

  @override
  void onFolderUpClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    String usbPath = state.deviceKind == UtlDeviceKind.usb
        ? state.isUSB1
            ? state.usbInfoList[usbButton1].usbPath
            : state.usbInfoList[usbButton2].usbPath
        : "";

    scrollController.dispose();
    scrollController = ScrollController();

    final String directoryName =
        DeviceMemoryModel().displayPathToAccessPath(state.directoryName);
    final List<String> directoryNameSplit = split(directoryName);
    if (directoryNameSplit.isNotEmpty) {
      directoryNameSplit.removeLast();
    }
    final displayDirectoryName = _toDisplayDirectoryName(directoryNameSplit);

    String subPath = "";
    for (var split in directoryNameSplit) {
      subPath = join(subPath, split);
    }

    /// LoadUsbDataWait ポップアップウィンドウを開ける
    _showWaitPopup();

    /// Model更新
    MemoryModel()
        .loadSelectedDirectoryList(state.deviceKind, subPath, usbPath)
        .then((error) {
      if (error == AccessError.none) {
        /// View更新
        _selectedPatternIndex = noTargetFound;
        _directoryName = displayDirectoryName;
        MemoryModel().selectNoneFiles();
        _patternsItemStyle = List.filled(MemoryModel().loadFileDataList.length,
            _isConnectedDF ? ButtonState.disable : ButtonState.normal);
        update();
      } else {
        HandelModel.handleMemoryAccessError(error);
        return;
      }

      /// LoadUsbDataWait ポップアップウィンドウを閉じる
      _resetWaitPopup();
    });

    ref
        .read(characterViewModelProvider.notifier)
        .updateCharacterPageByChild(CharacterModuleType.memoryRetrieve);
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// ファイル操作の関数群
  ///
  //////////////////////////////////////////////////////////////////

  @override
  Future<void> onFileOperationButtonClick(BuildContext context) async {
    if (state.fileOperationButtonState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// ポップアップ画面を閉じます
    PopupNavigator.closeAll(context: context);

    /// Model更新
    PatternDataModel().isEditMenuOn = false;
    PatternDataModel().isFileOperationOn =
        !PatternDataModel().isFileOperationOn;
    if (PatternDataModel().isFileOperationOn) {
      _patternsItemStyle = List.filled(
          MemoryModel().loadFileDataList.length, ButtonState.normal);
    } else {
      _patternsItemStyle = List.filled(MemoryModel().loadFileDataList.length,
          _isConnectedDF ? ButtonState.disable : ButtonState.normal);
    }

    if (PatternDataModel().isFileOperationOn == false) {
      MemoryModel().selectNoneFiles();
      if (PatternStatusModel().getCommonStatus().isConnectedDF) {
        _patternsItemStyle = List.filled(
            MemoryModel().loadFileDataList.length, ButtonState.disable);
      } else {
        await DeviceMemoryModel().beginUsbTransaction(_selectFilePath, () {
          if (File(_selectFilePath).existsSync()) {
            for (var i = 0; i < MemoryModel().loadFilePathList.length; i++) {
              if (MemoryModel().loadFilePathList[i] == _selectFilePath) {
                _selectedPatternIndex = i;
              }
            }
          } else {
            _selectFilePath = '';
            _selectedPatternIndex = noTargetFound;
          }
        });
      }
    } else {
      /// Do nothing
    }

    /// state 更新
    update();

    ref
        .read(characterViewModelProvider.notifier)
        .updateCharacterPageByChild(CharacterModuleType.memoryRetrieve);
  }

  @override
  void onSelectAllButtonClick() {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    ///Model更新
    MemoryModel().selectAllFiles();

    /// View更新
    _patternsItemStyle =
        List.filled(MemoryModel().loadFileDataList.length, ButtonState.select);

    update();
  }

  @override
  void onSelectNoneButtonClick() {
    if (MemoryModel().selectedFilesPath.isEmpty) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    ///Model更新
    MemoryModel().selectNoneFiles();

    /// View更新
    if (PatternDataModel().isFileOperationOn) {
      _patternsItemStyle = List.filled(
          MemoryModel().loadFileDataList.length, ButtonState.normal);
    } else {
      _patternsItemStyle = List.filled(MemoryModel().loadFileDataList.length,
          _isConnectedDF ? ButtonState.disable : ButtonState.normal);
    }

    update();
  }

  @override
  void onDeleteButtonClick() {
    String usbPath = state.deviceKind == UtlDeviceKind.usb
        ? state.isUSB1
            ? state.usbInfoList[usbButton1].usbPath
            : state.usbInfoList[usbButton2].usbPath
        : "";
    if (MemoryModel().selectedFilesPath.isEmpty) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    GlobalPopupRoute().updateErrorState(
      nextRoute: GlobalPopupRouteEnum.ERR_MYI_DELETE_OK_FOR_NORMAL,
      arguments: ErrMyiDeleteOkForNormalArgument(
        onCancelButtonClicked: (context) =>
            GlobalPopupRoute().resetErrorState(),
        onOKButtonClicked: (context) {
          _openDeletingPopup();
          MemoryModel().deleteSelectedFiles().then((error) async {
            if (error == AccessError.none) {
              /// 選択したファイルをクリアし
              MemoryModel().selectNoneFiles();

              /// ファイルリストを再取得します
              if (state.deviceKind == DeviceKind.internalStorage) {
                await MemoryModel().loadFileInMachineList();
              } else {
                await MemoryModel().loadSelectedDirectoryList(
                    state.deviceKind,
                    DeviceMemoryModel()
                        .displayPathToAccessPath(state.directoryName),
                    usbPath);
              }

              /// View更新
              scrollController.dispose();
              scrollController = ScrollController();
              _selectedPatternIndex = noTargetFound;
              if (PatternDataModel().isFileOperationOn) {
                _patternsItemStyle = List.filled(
                    MemoryModel().loadFileDataList.length, ButtonState.normal);
              } else {
                _patternsItemStyle = List.filled(
                    MemoryModel().loadFileDataList.length,
                    _isConnectedDF ? ButtonState.disable : ButtonState.normal);
              }

              update();

              ref
                  .read(characterViewModelProvider.notifier)
                  .updateCharacterPageByChild(
                      CharacterModuleType.memoryRetrieve);
            } else {
              HandelModel.handleMemoryAccessError(error);
              return;
            }
            GlobalPopupRoute().resetErrorState();
          });
        },
      ),
    );
  }

  @override
  void onMemoryButtonClick() {
    if (MemoryModel().selectedFilesPath.isEmpty) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    GlobalPopupRoute().updateErrorState(
      nextRoute: GlobalPopupRouteEnum.ERR_NOW_MOMORYING_FLASH,
      arguments: {GlobalPopupRoute.isStopSystemSound: true},
    );
    Future.delayed(const Duration(milliseconds: 150)).then((value) {
      MemoryModel().copySelectedFiles().then((error) {
        switch (error) {
          case AccessError.none:

            /// View更新
            scrollController.dispose();
            scrollController = ScrollController();
            update();
            GlobalPopupRoute().resetErrorState();
            break;
          case AccessError.memoryFull:
            GlobalPopupRoute().updateErrorState(
              nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED,
              arguments: EmbTooMuchSelectedArgument(
                onOKButtonClicked: GlobalPopupRoute().resetErrorState,
              ),
            );
            break;
          default:
            HandelModel.handleMemoryAccessError(error);
        }
      });
    });
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// 内部で使われる関数群
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// 端末の空き容量を取得する
  ///
  static String _getDeviceFreeSpace(
      DeviceKind deviceKind, bool isUsb1Selected) {
    int freeSpace;
    if (deviceKind == DeviceKind.usb) {
      final usbButton = isUsb1Selected ? usbButton1 : usbButton2;
      return DeviceMemoryModel().getAndConvertUsbFreeSpace(usbButton);
    } else {
      freeSpace = MemoryModel.getUtlMemFreeSpaceKb(deviceKind);
      final freeSpaceInfo =
          DeviceMemoryModel().covertKbNumFreeSpaceToShowString(freeSpace);

      return "${freeSpaceInfo.freeSpaceSize}".padLeft(3, ' ') +
          freeSpaceInfo.unitString;
    }
  }

  ///
  /// ファイル操作ボタンの状態取得
  ///
  ButtonState _getFileOperationButtonState() {
    if (MemoryModel().isMemoryFileListEmpty()) {
      return ButtonState.disable;
    }

    if (PatternDataModel().isFileOperationOn) {
      return ButtonState.select;
    }

    return ButtonState.normal;
  }

  ///
  /// SelectNoneボタンの状態取得
  ///
  ButtonState _getSelectNoneButtonState() {
    if (MemoryModel().selectedFilesPath.isNotEmpty) {
      return ButtonState.normal;
    }
    return ButtonState.disable;
  }

  ///
  /// Deleteボタンの状態取得
  ///
  ButtonState _getDeleteButtonState() {
    if (MemoryModel().selectedFilesPath.isNotEmpty) {
      return ButtonState.normal;
    }
    return ButtonState.disable;
  }

  ///
  /// Memoryボタンの状態取得
  ///
  ButtonState _getMemoryButtonState() {
    if (MemoryModel().selectedFilesPath.isNotEmpty) {
      return ButtonState.normal;
    }
    return ButtonState.disable;
  }

  ///
  /// フォルダアクサスの名前分けてのリストについて 表示のフォルダの名前を取得する
  ///
  /// フォルダアクサスの名前分けてのリスト : 　["a","b","c"]
  ///
  /// 表示のフォルダの名前 : /a/b/c
  ///
  String _toDisplayDirectoryName(List<String> directoryNames) {
    if (directoryNames.isEmpty) {
      return "";
    }

    String path = "";
    for (var directoryName in directoryNames) {
      path += "/$directoryName";
    }
    return path;
  }

  ///
  /// LoadUsbDataWait ポップアップウィンドウを開ける/閉じる
  ///
  void _showWaitPopup() => GlobalPopupRoute().showPleaseWaitPopup(
      arguments: {GlobalPopupRoute.isStopSystemSound: true});

  void _resetWaitPopup() => GlobalPopupRoute().resetPleaseWaitPopup();

  ///
  /// 「削除中です」というメッセージが表示されます。
  ///
  void _openDeletingPopup() => GlobalPopupRoute()
      .updateErrorState(nextRoute: GlobalPopupRouteEnum.ERR_NOW_DELETING);
}
