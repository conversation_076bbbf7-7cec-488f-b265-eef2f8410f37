import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/projector_model.dart';
import '../../../../../../model/sewing_global_model.dart';
import '../../../../../component/common_footer/common_footer_view_interface.dart';
import '../../../../../component/common_footer/common_footer_view_model.dart';
import '../../../../../component/real_preview/component/real_preview_controller.dart';
import '../../../../../component/real_preview/real_preview_view_interface.dart';
import '../../../../../component/real_preview/real_preview_view_model.dart';
import '../../../model/sewing_model.dart';
import '../../page/common_component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../../page/common_component/sew_connectsew/connect_sewing_view_model.dart'
    show connectSewingViewModelProvider;
import '../../page/pattern_edit/pattern_edit_view_model.dart' as pattern_edit;
import '../../page/sewing/sewing_view_interface.dart' as pattern_sewing;
import '../../page/sewing/sewing_view_model.dart' as pattern_sewing;
import '../emb_header/emb_header_view_model.dart';

/// view _modelに必要な構造
final embFooterViewModelProvider =
    AutoDisposeStateNotifierProvider<CommonFooterViewModel, CommonFooterState>(
        (ref) => EmbFooterViewModel(ref));

class EmbFooterViewModel extends CommonFooterViewModel {
  EmbFooterViewModel(ref) : super(ref);

  @override
  void onClockButtonClick(BuildContext context) {
    final provider = realPreviewViewModelProvider(RealPreviewType.emb);

    /// Real Preview再生中の場合、クリック不可
    if (ref.exists(provider)) {
      if (ref.read(provider).isBusyDrawing) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }
      if (ref.read(provider).animationPlayState == RealPreviewAction.play) {
        return;
      } else {
        ref.read(provider.notifier).onFrameTableButtonClick();
      }
    }

    /// 「つなぎ设定界面」ページをクリックした場合、無効になります
    if (ref.exists(connectSewingViewModelProvider)) {
      return;
    }

    /// プロジェクタを閉じます。
    final projectorRet = _maybeCloseProjector();
    if (projectorRet == true) {
      return;
    }

    /// ClockSetting画面遷移前にキー関数を先に実行する、エラー確認など
    final errCode = TpdLibrary().apiBinding.gotoClockSetting();
    if (errCode == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index ||
        errCode == DeviceErrorCode.devInvalidPanelError) {
      return;
    }

    if (errCode != DeviceErrorCode.devNoError) {
      return;
    }

    /// つなぎのプロジェクトを閉じる
    if (SewingGlobalModel().isInSewingPage == true &&
        SewingModel().isAutoOneDirection()) {
      ProjectorModel().embProjector.closeEmbProjector();
    } else {
      /// do nothing
    }

    ///
    /// W押えのプロジェクションがオンになっている場合は、プロジェクションをオフにします
    ///
    if (ProjectorModel().embProjector.isEmbWFooterProjectorOpen() == true) {
      ProjectorModel().embProjector.closeEmbProjector();
    } else {
      /// Do Nothing
    }

    /// LEDPointerを閉じます。
    DeviceLibrary().apiBinding.closeLedPtSetting();

    /// カメラ機能を閉じる
    if (ref.exists(embHeaderViewModelProvider)) {
      final headerFunction = ref.read(embHeaderViewModelProvider.notifier);
      final closeRet = headerFunction.maybeCloseCamera();
      if (closeRet == true) {
        headerFunction.updateCameraButtonToNormalByFooterCloseCamera();
      }
    } else {
      Log.e(tag: "Footer", description: "unknown header");
    }

    super.onClockButtonClick(context);
  }

  ///
  /// プロジェクト起動時、プロジェクト閉じる流れ処理
  /// 戻る値: true(閉じる処理が必要ですので、普通処理をしない)
  ///
  bool _maybeCloseProjector() {
    if (ProjectorModel().embProjector.isEmbProjectorViewOpen == true) {
      final embProjectorFunction =
          ref.read(embProjectorFunctionProvider.notifier);

      /// プロジェクト起動・停止前にエラーCheckをする
      /// エラーがあれば ポープアープを表示する
      final hasError = embProjectorFunction
          .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
      if (hasError) {
        return true;
      }

      embProjectorFunction.closeEmbProjectorView(
        closingHandleCallback: () {
          ProjectorModel().embProjector.isEmbProjectorViewOpen = false;
          if (SewingModel().isInSewingPage) {
            ref
                .read(pattern_sewing.sewingProvider.notifier)
                .updateSewingByChild(pattern_sewing.ModuleType.projector);
          } else {
            ref
                .read(pattern_edit.patternEditViewModelProvider.notifier)
                .updateEditPageByChild(pattern_edit.ModuleType.projector);
          }
          ref.read(embHeaderViewModelProvider.notifier).update();
          update();
        },
        afterClosedHandleCallback: () {},
      );

      return true;
    }

    return false;
  }
}
