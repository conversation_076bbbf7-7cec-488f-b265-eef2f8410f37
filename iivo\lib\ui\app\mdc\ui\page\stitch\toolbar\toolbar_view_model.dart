import 'dart:async';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import 'package:xd_component/xd_component.dart';
import '../../../../model/color_table.dart';
import '../../../../model/decorative_popup_model.dart';
import '../../../../model/motif_popup_model.dart';
import '../../../../model/stitch/creation_isolate.dart';
import '../../../../model/stitch/draw_region_model.dart';
import '../../../../model/stitch/line_candle_model.dart';
import '../../../../model/stitch/line_chain_model.dart';
import '../../../../model/stitch/line_e_stitch_model.dart';
import '../../../../model/stitch/line_motif_model.dart';
import '../../../../model/stitch/line_property_popup_model.dart';
import '../../../../model/stitch/line_rough_zigzag_model.dart';
import '../../../../model/stitch/line_running_model.dart';
import '../../../../model/stitch/line_triple_model.dart';
import '../../../../model/stitch/line_v_stitch_model.dart';
import '../../../../model/stitch/line_zigzag_model.dart';
import '../../../../model/stitch/magnification_model.dart';
import '../../../../model/stitch/surface_decorative_fill_model.dart';
import '../../../../model/stitch/surface_property_popup_model.dart';
import '../../../../model/stitch/surface_stipple_model.dart';
import '../../../../model/stitch/surface_tatami_model.dart';
import '../../../../model/stitch/toolbar_model.dart';
import '../../../../model/stitch/view_area_model.dart';
import '../stitch_page_view_model.dart';
import 'component/line_candle/line_candle.dart';
import 'component/line_candle/line_candle_view_model.dart';
import 'component/line_chain/line_chain.dart';
import 'component/line_chain/line_chain_view_model.dart';
import 'component/line_e_stitch/line_e_stitch.dart';
import 'component/line_e_stitch/line_e_stitch_view_model.dart';
import 'component/line_motif/line_motif.dart';
import 'component/line_motif/line_motif_view_model.dart';
import 'component/line_rough_zigzag/line_rough_zigzag.dart';
import 'component/line_rough_zigzag/line_rough_zigzag_view_model.dart';
import 'component/line_running/line_running.dart';
import 'component/line_running/line_running_view_model.dart';
import 'component/line_triple/line_triple.dart';
import 'component/line_triple/line_triple_view_model.dart';
import 'component/line_v_stitch/line_v_stitch.dart';
import 'component/line_v_stitch/line_v_stitch_view_model.dart';
import 'component/line_zigzag/line_zigzag.dart';
import 'component/line_zigzag/line_zigzag_view_model.dart';
import 'component/surface_decorative_fill/surface_decorative_fill.dart';
import 'component/surface_decorative_fill/surface_decorative_fill_view_model.dart';
import 'component/surface_stipple/surface_stipple.dart';
import 'component/surface_stipple/surface_stipple_view_model.dart';
import 'component/surface_tatami/surface_tatami.dart';
import 'component/surface_tatami/surface_tatami_view_model.dart';
import 'toolbar_view_interface.dart';

typedef SurfaceKind = MdcSewKindsSurface;
typedef LineKind = MdcSewingKindsLine;
typedef SewingMode = SewingModeType;

final toolBarViewModelProvider =
    StateNotifierProvider.autoDispose<ToolBarViewInterface, ToolbarState>(
        (ref) => ToolBarViewModel(ref));

class ToolBarViewModel extends ToolBarViewInterface {
  ToolBarViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            ToolbarState(
              lineZigzagViewModelProvider: LineZigzagViewModelProvider(
                (ref) => LineZigzagViewModel(ref),
              ),
              surfaceTatamiViewModelProvider: SurfaceTatamiViewModelProvider(
                (ref) => SurfaceTatamiViewModel(ref),
              ),
              surfaceDecorativeFillViewModelProvider:
                  SurfaceDecorativeFillViewModelProvider(
                (ref) => SurfaceDecoFillViewModel(ref),
              ),
              surfaceStippleViewModelProvider: SurfaceStippleViewModelProvider(
                (ref) => SurfaceStippleViewModel(ref),
              ),
              lineVStitchViewModelProvider: LineVStitchViewModelProvider(
                (ref) => LineVStitchViewModel(ref),
              ),
              lineChainViewModelProvider: LineChainViewModelProvider(
                (ref) => LineChainViewModel(ref),
              ),
              lineCandleViewModelProvider: LineCandleViewModelProvider(
                (ref) => LineCandleViewModel(ref),
              ),
              lineEStitchViewModelProvider: LineEStitchViewModelProvider(
                (ref) => LineEStitchViewModel(ref),
              ),
              lineTripleViewModelProvider: LineTripleViewModelProvider(
                (ref) => LineTripleViewModel(ref),
              ),
              lineRunningViewModelProvide: LineRunningViewModelProvider(
                (ref) => LineRunningViewModel(ref),
              ),
              lineMotifViewModelProvider: LineMotifViewModelProvider(
                (ref) => LineMotifViewModel(ref),
              ),
              lineRoughZigzagViewModelProvider:
                  LineRoughZigzagViewModelProvider(
                (ref) => LineRoughZigzagViewModel(ref),
              ),
            ),
            ref);

  @override
  void build() {
    super.build();
    update();
  }

  @override
  bool get isProcessImage => DrawRegionModel().isProcessImage;

  final List<Widget> _lineSewIconList = [
    /// invalid
    Container(),

    const ico_s0_linestitch_zigzag(),
    const ico_s1_linestitch_running(),
    const ico_s2_linestitch_triple(),
    const ico_s3_linestitch_candle(),
    const ico_s4_linestitch_chain(),
    const ico_s5_linestitch_estitch(),
    const ico_s6_linestitch_vsitich(),

    ///Motif index
    Container(),

    /// toDo:XDのイメージ名を変更したら、新しいファイル名に置き換える必要があります.jira：PHFIRMIIVO-3732
    const ico_s8_linestitch_zigzag(),

    ///noSew index
    Container(),
  ];

  final List<Widget> _surfaceSewIconList = [
    /// invalid
    Container(),
    const ico_s_regionsetting_satin(),
    const ico_s_regionsetting_stippling(),
    Container(),
  ];

  static const double decorativeThumbnailWidth = 152;
  static const double decorativeThumbnailHeight = 152;

  @override
  List<Widget> get lineSewIconList => _lineSewIconList;

  @override
  List<Widget> get surfaceSewIconList => _surfaceSewIconList;

  ///
  /// パレットの色のリスト
  ///
  @override
  List<Color> get paletteColors =>
      getPaletteColorsByThreadBrandType(getThreadBrandType());

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    MdcRegionInfo patternInfo = DrawRegionModel().getPatternInfo();

    if (patternInfo.attr == SewingModeType.line) {
      /// Model更新
      LinePropertyPopupModel().sewKindLine = patternInfo.stitch.sewMode;

      if (patternInfo.stitch.sewMode == MdcSewingKindsLine.motif) {
        int motifNoValue = (patternInfo.stitch as LineMotifType).motifNoValue;
        if ((patternInfo.stitch as LineMotifType).motifTypeValue) {
          motifNoValue =
              MotifPopupModel.startMotifCustomNo + (motifNoValue - 1);
        }

        /// View更新
        state = state.copyWith(
          selectedLineThumbnailData:
              MotifPopupModel.readSelectedMotifThumbnailData(motifNoValue),
        );
      }
    } else {
      /// Model更新
      SurfacePropertyPopupModel().sewKindsSurface = patternInfo.stitch.sewMode;

      if (patternInfo.stitch.sewMode == MdcSewKindsSurface.decorativeFill) {
        int decorativeNoValue =
            (patternInfo.stitch as SurfaceFillType).decorativeNoValue;
        if ((patternInfo.stitch as SurfaceFillType).decorativeTypeValue) {
          decorativeNoValue = DecorativePopupModel.startDecorativeCustomNo +
              (decorativeNoValue - 1);
        }
        SurfacePropertyPopupModel().currentSelectedDecorativeNo =
            decorativeNoValue;

        /// View更新
        state = state.copyWith(
          selectedSurfaceThumbnailData:
              DecorativePopupModel.readSelectedDecorativeThumbnailData(
            decorativeNoValue,
            thumbnailWidth: decorativeThumbnailWidth,
            thumbnailHeight: decorativeThumbnailHeight,
          ),
        );
      }
    }

    /// View更新
    final isMultiLink = DrawRegionModel().getOperation() == Operation.multiLink;
    final invalidStdCode =
        patternInfo.stitch.color.number == ColorCode.invalidStdCode.number;
    final colorIndex = patternInfo.stitch.color.index - 1 < 0
        ? 0
        : patternInfo.stitch.color.index - 1;
    state = state.copyWith(
      selectedIndex: DrawRegionModel().currentSelectedIndex,
      sewingModeTypeIndex: patternInfo.attr.index,
      surfaceKindIndex: patternInfo.stitch.sewMode.index,
      lineKineIndex: patternInfo.stitch.sewMode.index,
      isLeftRightButtonValid: isProcessImage
          ? false
          : DrawRegionModel().getRegionLength() > 1
              ? true
              : false,
      showColor:
          invalidStdCode ? Colors.transparent : paletteColors[colorIndex],
      isSameSewingTypeButtonSelected: isMultiLink,
      invalidStdCode: invalidStdCode,
    );

    ///
    /// 画面初期化時、他の画面の初期化も完了しない
    /// それで Futureを使って、後で更新する
    ///
    Future(
      () {
        _updateChildPage();
      },
    );
  }

  ///
  /// スプライトの更新
  ///
  void _updateChildPage() {
    MDCStitchType currentStitch = DrawRegionModel().getPatternInfo().stitch;
    switch (currentStitch.runtimeType) {
      case LineZigzagType:
        ref.read(state.lineZigzagViewModelProvider.notifier).update();
        break;
      case LineRoughZigzagType:
        ref.read(state.lineRoughZigzagViewModelProvider.notifier).update();
        break;
      case LineRunType:
        ref.read(state.lineRunningViewModelProvide.notifier).update();
        break;
      case LineTripleType:
        ref.read(state.lineTripleViewModelProvider.notifier).update();
        break;
      case LineCandleType:
        ref.read(state.lineCandleViewModelProvider.notifier).update();
        break;
      case LineChainType:
        ref.read(state.lineChainViewModelProvider.notifier).update();
        break;
      case LineMotifType:
        ref.read(state.lineMotifViewModelProvider.notifier).update();
        break;
      case LineEStitchType:
        ref.read(state.lineEStitchViewModelProvider.notifier).update();
        break;
      case LineVStitchType:
        ref.read(state.lineVStitchViewModelProvider.notifier).update();
        break;
      case SurfaceTatamiType:
        ref.read(state.surfaceTatamiViewModelProvider.notifier).update();
        break;
      case SurfaceStippleType:
        ref.read(state.surfaceStippleViewModelProvider.notifier).update();
        break;
      case SurfaceFillType:
        ref
            .read(state.surfaceDecorativeFillViewModelProvider.notifier)
            .update();
        break;
      default:
        break;
    }
  }

  ///
  ///  ステッチの左ボタンがクリックされました
  ///
  @override
  void onStitchLeftButtonClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ToolbarModel().selectRegionInfo(RegionSelectType.pre);
    update();
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updatePageByChild(ComponentType.toolbar);
  }

  ///
  /// ステッチの右ボタンがクリックされました
  ///
  @override
  void onStitchRightButtonClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    ToolbarModel().selectRegionInfo(RegionSelectType.next);
    update();
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updatePageByChild(ComponentType.toolbar);
  }

  ///
  /// 同種の縫い方一括選択ボタンがクリックされました
  ///
  @override
  void onSameSewingTypeButtonClicked() {
    if (isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ///  Model 更新
    ToolbarModel().selectRegionInfo(RegionSelectType.same);

    /// View更新
    update();
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updatePageByChild(ComponentType.toolbar);
  }

  ///
  /// 縫製設定表示取得する
  ///
  @override
  Widget getSelectedSewingSettingDisplay(WidgetRef ref) {
    MDCStitchType currentStitch = DrawRegionModel().getPatternInfo().stitch;
    switch (currentStitch.runtimeType) {
      case LineZigzagType:
        return LineZigzag(
            lineZigzagViewModelProvider: state.lineZigzagViewModelProvider);
      case LineRunType:
        return LineRunning(
            lineRunningViewModelProvider: state.lineRunningViewModelProvide);
      case LineTripleType:
        return LineTriple(
            lineTripleViewModelProvider: state.lineTripleViewModelProvider);
      case LineCandleType:
        return LineCandle(
            lineCandleViewModelProvider: state.lineCandleViewModelProvider);
      case LineChainType:
        return LineChain(
            lineChainViewModelProvider: state.lineChainViewModelProvider);
      case LineMotifType:
        return LineMotif(
            lineMotifViewModelProvider: state.lineMotifViewModelProvider);
      case LineEStitchType:
        return LineEStitch(
            lineEStitchViewModelProvider: state.lineEStitchViewModelProvider);
      case LineVStitchType:
        return LineVStitch(
            lineVStitchViewModelProvider: state.lineVStitchViewModelProvider);
      case LineRoughZigzagType:
        return LineRoughZigzag(
            lineRoughZigzagViewModelProvider:
                state.lineRoughZigzagViewModelProvider);
      case SurfaceTatamiType:
        return SurfaceTatami(
            surfaceTatamiViewModelProvider:
                state.surfaceTatamiViewModelProvider);
      case SurfaceStippleType:
        return SurfaceStipple(
            surfaceStippleViewModelProvider:
                state.surfaceStippleViewModelProvider);
      case SurfaceFillType:
        return SurfaceDecoFill(
            surfaceDecorativeFillViewModelProvider:
                state.surfaceDecorativeFillViewModelProvider);
      default:
        return Container();
    }
  }

  ///
  /// ラインプロパティポップアップを開く
  ///
  @override
  void onOpenLinePropertyPopup(context) {
    if (isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    MDCStitchType backStitch = DrawRegionModel().getPatternInfo().stitch;

    /// ラインプロパティポップアップを開く
    PopupNavigator.pushNamed(
      context: context,
      nextRouteName: PopupEnum.lineProperty,
    ).then((value) {
      if (!mounted) {
        return;
      }

      /// isChanged linePropertyページに変更があるかどうかを確認します
      if (value == false) {
        return;
      }
      MagnificationModel.magnificationLevel =
          MagnificationModel.magnification_100;
      ViewAreaModel().isDragMove = false;
      CreationModel().changeStitchCreation();
      MDCStitchType stitch = DrawRegionModel().getPatternInfo().stitch;
      if (backStitch.sewMode != stitch.sewMode) {
        state = state.copyWith(isSewModeChanged: true);
      } else {
        state = state.copyWith(isSewModeChanged: false);
      }

      /// view更新
      update();
    });
  }

  ///
  /// ラインプロパティポップアップを開く
  ///
  @override
  void onOpenSurfacePropertyPopup(context) {
    if (isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    MDCStitchType backStitch = DrawRegionModel().getPatternInfo().stitch;

    /// ラインプロパティポップアップを開く
    PopupNavigator.pushNamed(
            context: context, nextRouteName: PopupEnum.surfaceProperty)
        .then((value) {
      if (!mounted) {
        return;
      }

      /// view更新
      update();

      /// isChanged 表面プロパティが変更されたかどうかを判断します。
      if (value == false) {
        return;
      }
      MagnificationModel.magnificationLevel =
          MagnificationModel.magnification_100;
      ViewAreaModel().isDragMove = false;

      CreationModel().changeStitchCreation();
      MDCStitchType stitch = DrawRegionModel().getPatternInfo().stitch;
      if (backStitch.sewMode != stitch.sewMode) {
        state = state.copyWith(isSewModeChanged: true);
      } else {
        state = state.copyWith(isSewModeChanged: false);
      }
    });
  }
}
