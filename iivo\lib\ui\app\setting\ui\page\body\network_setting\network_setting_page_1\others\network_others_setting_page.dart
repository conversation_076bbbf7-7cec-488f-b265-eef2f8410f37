import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../component/setting_footer/setting_footer.dart';
import '../../../../../component/setting_header/setting_header.dart';
import 'network_others_setting_page_interface.dart';
import 'network_others_setting_page_view_model.dart';

class NetworkOthersSettingPage extends StatefulPage {
  const NetworkOthersSettingPage({super.key});

  @override
  PageState<NetworkOthersSettingPage> createState() =>
      _NetworkOthersSettingPageState();
}

class _NetworkOthersSettingPageState
    extends PageState<NetworkOthersSettingPage> {
  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;

    final NetworkOthersSettingPageInterface viewModel = ref.read(
      networkOthersSettingViewModelProvider.notifier,
    );
    return _buildScaffold(
      content: [
        Expanded(flex: 102, child: _buildTitle(l10n)),
        Expanded(flex: 187, child: _buildMacAddressItem(ref, l10n)),
        const Spacer(flex: 8),
        Expanded(
          flex: 230,
          child: grp_pre_network_diagnosis_tool_title(
            text: l10n.icon_nettool,
            buttonsText: l10n.icon_00208,
            onTap: () =>
                viewModel.onNetworkDiagnosisToolStartButtonClicked(context),
          ),
        ),
        const Spacer(flex: 541),
        Expanded(
          flex: 80,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              grp_btn_return(
                buttonText: l10n.icon_return,
                onTap: () => viewModel.onReturnButtonClicked(context),
              )
            ],
          ),
        ),
        const Spacer(flex: 12),
      ],
    );
  }

  ///
  /// タイトルを作成し、英語では【Others】と表示する
  ///
  static Widget _buildTitle(AppLocalizations l10n) {
    return Row(
      children: [
        Expanded(
          flex: 768,
          child: Text(
            l10n.icon_00399,
            style: const TextStyle(
              fontFamily: "Roboto",
              fontSize: 28,
              height: 1,
              color: Colors.black,
            ),
          ),
        ),
      ],
    );
  }

  ///
  /// MACアドレスを表示するためのアイテムを作成する
  ///
  static Widget _buildMacAddressItem(WidgetRef ref, AppLocalizations l10n) {
    final NetworkOthersSettingPageState state =
        ref.watch(networkOthersSettingViewModelProvider);
    return Stack(
      children: [
        const pre_settinglist2line(),
        Column(
          children: [
            const Spacer(flex: 13),
            Expanded(
              flex: 87,
              child: grp_custom_settinglist_info_network_diagnosis_tool_title(
                text: l10n.icon_00647,
              ),
            ),
            Expanded(
              flex: 87,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Expanded(
                    flex: 752,
                    child: Text(
                      textAlign: TextAlign.end,
                      state.macAddress,
                      style: const TextStyle(
                        fontFamily: "Roboto",
                        fontSize: 28,
                        height: 1,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const Spacer(flex: 16)
                ],
              ),
            ),
          ],
        )
      ],
    );
  }

  ///
  /// 現在のページの足場を作成する
  ///
  static Widget _buildScaffold({required List<Widget> content}) {
    return Scaffold(
      body: pre_base_gray(
        child: Column(
          children: [
            const Expanded(flex: 71, child: SettingHeader()),
            Expanded(
              flex: 1148,
              child: Row(
                children: [
                  const Spacer(flex: 16),
                  Expanded(flex: 768, child: Column(children: content)),
                  const Spacer(flex: 16)
                ],
              ),
            ),
            const Expanded(flex: 61, child: SettingFooter()),
          ],
        ),
      ),
    );
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup(_) => {};
}
