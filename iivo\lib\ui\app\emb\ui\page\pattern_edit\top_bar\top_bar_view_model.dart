import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../model/projector_model.dart';
import '../../../../model/area_model.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/preview_model.dart';
import '../../../../model/scan_model.dart';
import '../../../../model/thread_color_list_model.dart';
import '../../../page_route.dart';
import '../../common_component/background_scan/background_scan.dart';
import '../pattern_edit_view_model.dart';
import 'top_bar_view_interface.dart';

final topBarViewModelProvider =
    StateNotifierProvider.autoDispose<TopBarViewModelInterface, TopBarState>(
        (ref) => TopBarViewModel(ref));

class TopBarViewModel extends TopBarViewModelInterface {
  TopBarViewModel(Ref ref) : super(const TopBarState(), ref) {
    update();
  }

  ///
  /// 虫眼鏡による拡大画像iconList
  ///
  final List<Widget> _zoomIconList = [
    const ico_zoom100(),
    const ico_zoom125(),
    const ico_zoom150(),
    const ico_zoom200(),
    const ico_zoom400(),
  ];

  @override
  void update() {
    final PatternDispInfo patternDispInfo = PatternModel().getPatternInfo(
      isAllPattern: true,
      isNeedAllPatternSize: true,
    );

    /// 縫製の線色がないか判断する
    final bool isNoThreadToSewing = ThreadColorListModel().isNoThreadToSewing();

    final int frameIndex = AreaModel().getEmbTopBarFrameIndex(patternDispInfo);
    final String totalHeightValue = PatternModel().getAllPattern().isEmpty ||
            isNoThreadToSewing
        ? '----'
        : PatternModel().changeValueToDisplay(patternDispInfo.allPatternHeight);
    final String totalWidthValue = PatternModel().getAllPattern().isEmpty ||
            isNoThreadToSewing
        ? '----'
        : PatternModel().changeValueToDisplay(patternDispInfo.allPatternWidth);

    /// View更新
    state = state.copyWith(
      isInch: PatternModel().isUnitMm == false,
      frameIndex: frameIndex,
      totalHeightValue: totalHeightValue,
      totalWidthValue: totalWidthValue,
      selectedZoomScale: PatternModel().selectedZoomScaleInEditPage,
      isRealPreviewEnable: isNoThreadToSewing == true ? false : true,
      isHandleSelected: PreviewModel().isInDragPreviewMode,
      isProjectorON: ProjectorModel().embProjector.isEmbProjectorViewOpen,
    );

    /// 投影をオンにした後のズームをオフにする
    if (ProjectorModel().embProjector.isEmbProjectorViewOpen) {
      state = state.copyWith(
        isZoomPopupOn: false,
      );
    } else {
      /// do nothing
    }
  }

  @override
  List<int> get zoomDisplayList => zoomList;

  @override
  void onRealPreviewButtonClicked(context) {
    if (ThreadColorListModel().isNoThreadToSewing()) {
      return;
    }

    /// Model更新
    PatternModel().realPreviewDisplayType = RealPreviewDisplayType.patternEdit;

    /// View更新
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.embRealPreview);
  }

  @override
  void onZoomButtonClicked() {
    state = state.copyWith(isZoomPopupOn: true);
  }

  @override
  void onZoomPopupButtonClicked(int zoomValue) {
    /// Model更新
    PatternModel().selectedZoomScaleInEditPage = zoomValue;

    /// view更新
    state = state.copyWith(
        isZoomPopupOn: false,
        selectedZoomScale: PatternModel().selectedZoomScaleInEditPage);

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.topBar);
  }

  @override
  void closeZoomPopup() {
    state = state.copyWith(
        isZoomPopupOn: false,
        selectedZoomScale: PatternModel().selectedZoomScaleInEditPage);
  }

  @override
  void onHandleButtonClicked() {
    /// Model更新
    PreviewModel().isInDragPreviewMode = !PreviewModel().isInDragPreviewMode;

    /// View更新
    state =
        state.copyWith(isHandleSelected: PreviewModel().isInDragPreviewMode);

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.topBar);
  }

  ///
  /// 背景スキャンボタンをクリック関数
  ///
  @override
  void onBackgroundScanButtonClicked(BuildContext context) {
    if (ScanModel().isEditPageScanPopupOpen == true) {
      _closeBackgroundScanPopup(context);
    } else {
      _openBackgroundScanPopup(context);
    }
  }

  ///
  /// 背景スキャンメニューポップアップを開く
  ///
  void _openBackgroundScanPopup(BuildContext context) {
    if (!mounted) {
      return;
    }
    ScanModel().isEditPageScanPopupOpen = true;

    /// View更新
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => Column(
          children: [
            const Spacer(flex: 158),
            Expanded(
              flex: 873,
              child: Row(
                children: [
                  const Spacer(flex: 571),
                  Expanded(
                    flex: 229,
                    child: BackgroundScan(
                      cancelClick: () => _closeBackgroundScanPopup(context),
                      refreshOtherWidgets: () => ref
                          .read(patternEditViewModelProvider.notifier)
                          .updateEditPageByChild(ModuleType.scan),
                      isSewingMode: false,
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(flex: 239),
          ],
        ),
        barrier: false,
      ),
    );

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.scan);
  }

  ///
  /// 背景スキャンメニューポップアップを閉じる
  ///
  void _closeBackgroundScanPopup(BuildContext context) {
    ScanModel().isEditPageScanPopupOpen = false;

    /// View更新
    PopupNavigator.pop(context: context);

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.scan);
  }

  @override
  void dispose() {
    ScanModel().isEditPageScanPopupOpen = false;
    super.dispose();
  }

  ///
  /// 選択されているものを取得します index
  ///
  @override
  Widget getSelectedZoomIcon() {
    for (int i = 0; i < zoomList.length; i++) {
      if (state.selectedZoomScale == zoomList[i]) {
        return _zoomIconList[i];
      }
    }
    return Container();
  }

  @override
  Widget get thumbnailSize => [
        const ico_thumnail_size00(),
        const ico_thumnail_size01(),
        const ico_thumnail_size02(),
        const ico_thumnail_size03(),
      ][state.frameIndex];
}
