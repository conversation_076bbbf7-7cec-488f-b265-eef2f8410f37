import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:network_wifi/network_wifi.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../network/server/http/route/artspira/common.dart';
import '../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../global_popup/global_popups/err_delete_artspira/err_delete_artspira_view_model.dart';
import '../../../../../../../global_popup/global_popups/err_net_disable_wlan/err_net_disable_wlan_view_model.dart';
import '../../../../../model/network_setting_page1_model.dart';
import '../../../../../model/network_setting_page3_model.dart';
import '../../../../../model/setting_model.dart';
import '../../../setting_page_view_model.dart';
import 'component/agreement_popup.dart';
import 'component/guidance_popup.dart';
import 'component/pin_code_sacnf_popup/pin_code_scanf.dart';
import 'component/reset_processing_popup.dart';
import 'network_setting_page_3_interface.dart';

final networkSettingPage3ViewModelProvider = StateNotifierProvider.autoDispose<
    NetworkSettingPage3Interface,
    NetworkSettingPage3State>((ref) => NetworkSettingPage3ViewModel(ref));

class NetworkSettingPage3ViewModel extends NetworkSettingPage3Interface {
  NetworkSettingPage3ViewModel(Ref ref)
      : super(const NetworkSettingPage3State(), ref) {
    state = state.copyWith(
      registerSuccess: NetworkSettingPage1Mode3().canvasRegister,
    );
  }

  /// Wifiの開始ページ
  final int _networkPageStartPage = 10;

  /// PINコードを入力してください
  String pinCode = "";

  ///
  ///登録ボタン
  ///
  @override
  void onRegisterButtonClick(BuildContext context) {
    if (WifiManager().isEnabled() == true) {
      _openGuidancePopup(context);
    } else {
      _openWifiPopup();
    }
  }

  ///
  ///Guidance ポップアップ
  ///
  void _openGuidancePopup(BuildContext context) {
    final displayProductNo = _getProductNoWithCheckDigit();

    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => GuidancePopup(
          cancelClick: () => PopupNavigator.pop(context: context),
          nextClick: () {
            PopupNavigator.pop(context: context);
            _openInputPinCodePopup(context);
          },
          productNo: displayProductNo,
        ),
        barrier: false,
      ),
    );
  }

  ///
  /// 表示用のProductNo設定
  ///
  String _getProductNoWithCheckDigit() {
    final productNo =
        DeviceLibrary().apiBinding.getDeviceSettingInfo().settingInfo.productNo;

    /// MM
    int factor = 9;
    int checkData = 0;
    int retChkDigit = 0;

    checkData = int.parse(productNo.substring(0, 2));
    retChkDigit = checkData * factor;
    factor--;

    /// A ～ H
    List<int> intList = productNo.split('').map((s) => int.parse(s)).toList();
    for (var i = 2; i < productNo.length; i++) {
      retChkDigit += intList[i] * factor;
      factor--;
    }

    retChkDigit %= 9;
    retChkDigit = 9 - retChkDigit;

    String displayProductNo =
        '${productNo.substring(0, 5)} ${productNo.substring(5)}';

    final productNoWithCheckDigit = "$displayProductNo-$retChkDigit";

    return productNoWithCheckDigit;
  }

  ///
  /// ネットワークに接続されているポップアップをオンにします
  ///
  void _openWifiPopup() {
    GlobalPopupRoute().updateErrorState(
      nextRoute: GlobalPopupRouteEnum.ERR_NET_DISABLE_WLAN,
      arguments: ErrNetDisableWlanArgument(
        onOKButtonClicked: _onWifiOffPopupOkButtonClick,
        onCancelButtonClicked: GlobalPopupRoute().resetErrorState,
      ),
    );
  }

  ///
  ///WiFiエラー確認ボタン
  ///
  void _onWifiOffPopupOkButtonClick() {
    GlobalPopupRoute().resetErrorState();

    /// Model更新
    SettingModel().currentPageIndex = _networkPageStartPage;
    SettingModel().currentMode = SettingMode.network;

    /// View更新
    ref
        .read(settingPageViewModelProvider.notifier)
        .updateSettingPageByChild(ModuleType.register);
  }

  ///
  ///PIN Code 入力インターフェース
  ///
  void _openInputPinCodePopup(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => PinCodePopup(
          pinCodeCancelClick: () {
            PopupNavigator.pop(context: context);
            _openGuidancePopup(context);
          },
          pinCodeOKClick: (numberSelectString) {
            for (var element in numberSelectString) {
              pinCode = pinCode + element;
            }
            _openAgreementPopup(context);
          },
        ),
        barrier: false,
      ),
    );
  }

  ///
  ///规约画像
  ///
  void _openAgreementPopup(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => AgreementPopup(
          cancelClick: () {
            PopupNavigator.pop(context: context);
          },
          okClick: () {
            _onAgreementOKButtonClick(context);
          },
        ),
        barrier: false,
      ),
    );
  }

  void _onAgreementOKButtonClick(BuildContext context) {
    _openResetProcessingPopup(context);

    /// PINコード登録
    artspiraHttpRequestHandle(ArtspiraRequestKind.registerPinCode,
            parameter: pinCode)
        .then((registerRet) {
      if (registerRet == NetDomainError.netDomainErrorOk) {
        /// PINコード登録成功
        NetworkSettingPage1Mode3().canvasRegister = true;

        /// マシン名の登録
        final machineName = NetworkSettingPage1Model().getWlanMachineName();
        artspiraHttpRequestHandle(ArtspiraRequestKind.registerMachineName,
            parameter: machineName);

        /// すべてのポップアップを閉じる
        PopupNavigator.closeAll(context: context);

        /// view更新
        state = state.copyWith(
          registerSuccess: NetworkSettingPage1Mode3().canvasRegister,
        );

        /// PINコード登録済であればUser情報を送信
        /// データを送信するのは誤りで、しばらくデータを送信しません
        /// artspiraHttpRequestHandle(ArtspiraRequestKind.sendUserLog,
        ///     parameter: CommunicationType.powerOn);
      } else {
        /// PINコード登録失敗
        /// Errorのメッセージ情報更新を表示する
        _openRegisterErrorMessagePopup(context, registerRet);
      }
    });
  }

  ///
  ///ボタンを置き換える
  ///
  @override
  void onChangeButtonClick(BuildContext context) {
    if (WifiManager().isEnabled() == true) {
      _openGuidancePopup(context);
    } else {
      _openWifiPopup();
    }
  }

  ///
  ///削除ボタン
  ///
  @override
  void onDeleteButtonClick(BuildContext context) {
    if (WifiManager().isEnabled() == false) {
      _openWifiPopup();
    } else {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.errDeleteArtspira,
        arguments: DeleteArtspiraArgument(
          onRightButtonClicked: (context) {
            _onDeleteOkButtonClick(context);
          },
        ),
      );
    }
  }

  ///
  ///確認ボタンを削除する
  ///
  void _onDeleteOkButtonClick(BuildContext context) {
    _openResetProcessingPopup(context);

    artspiraHttpRequestHandle(ArtspiraRequestKind.unregisterPinCode)
        .then((unregisterRet) {
      if (unregisterRet == NetDomainError.netDomainErrorOk) {
        /// PINコード解除成功
        NetworkSettingPage1Mode3().canvasRegister = false;

        /// view更新
        state = state.copyWith(
          registerSuccess: NetworkSettingPage1Mode3().canvasRegister,
        );

        /// unregister確認Popup
        GlobalPopupRoute().resetErrorState();

        /// Processing Popup
        PopupNavigator.pop(context: context);
      } else {
        /// unregister確認Popup
        GlobalPopupRoute().resetErrorState();

        /// Processing Popup
        PopupNavigator.pop(context: context);

        ///  PINコード削除は失敗しても削除される
        NetworkSettingPage1Mode3().canvasRegister = false;

        /// view更新
        state = state.copyWith(
          registerSuccess: NetworkSettingPage1Mode3().canvasRegister,
        );

        /// PINコード解除失敗
        /// Errorのメッセージ情報更新を表示する
        _openDeleteErrorMessagePopup(context, unregisterRet);
      }

      /// 刺繍Senjuポケット保存領域をクリアする
      GetDataInfo().embMemorySenjuFileDelete();
    });
  }

  @override
  String getLoginId() {
    /// PINコード登録成功
    if (state.registerSuccess) {
      var (_, name, _, _) =
          DeviceLibrary().apiBinding.getArtspiraRegisterInfo();
      String? loginId = name;

      /// 読みますのloginIdはnull場合、空の文字列に設定
      return loginId;
    } else {
      /// PINコード未登録
      return "";
    }
  }

  ///
  /// 画面が進行中です
  ///
  void _openResetProcessingPopup(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => const ResetProcessingPopup(),
      ),
    );
  }

  ///
  /// PinCodeログインエラーメッセージ
  ///
  void _openRegisterErrorMessagePopup(
      BuildContext context, NetDomainError error) {
    /// PinCode入力ページに戻る
    PopupNavigator.pop(context: context);
    PopupNavigator.pop(context: context);

    final (nextRoute, arguments) =
        covertToErrorCode(ArtspiraRequestKind.registerPinCode, error);
    if (nextRoute != null) {
      GlobalPopupRoute()
          .updateErrorState(nextRoute: nextRoute, arguments: arguments);
    } else {
      /// do nothing
    }
  }

  ///
  /// PinCode キャンセル失敗情報
  ///
  void _openDeleteErrorMessagePopup(
      BuildContext context, NetDomainError error) {
    PopupNavigator.pop(context: context);

    final (nextRoute, arguments) =
        covertToErrorCode(ArtspiraRequestKind.unregisterPinCode, error);
    if (nextRoute != null) {
      GlobalPopupRoute()
          .updateErrorState(nextRoute: nextRoute, arguments: arguments);
    } else {
      /// do nothing
    }
  }
}
