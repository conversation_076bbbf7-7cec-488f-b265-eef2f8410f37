import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'upgrade_select_view_model.dart';

class UpgradeSelect extends StatefulPage {
  const UpgradeSelect({super.key});
  @override
  PageState<UpgradeSelect> createState() => _UpgradeSelectState();
}

class _UpgradeSelectState extends PageState<UpgradeSelect> {
  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final state = ref.watch(upgradePageViewModeProvider(context));
    final viewModel = ref.watch(upgradePageViewModeProvider(context).notifier);

    ///
    /// USBディスプレイの最大数
    ///
    const int maxUsbDisplayNumber = 2;
    return Scaffold(
      body: Stack(
        children: [
          const pre_base_gray(),
          Column(
            children: [
              viewModel.isLineUpdate
                  ? const Expanded(
                      flex: 30,
                      child: Row(
                        children: [
                          Spacer(flex: 100),
                          Expanded(
                            flex: 200,
                            child: Center(
                              child: grp_str_number1(
                                text: "PRODUCT LINE MODE",
                                alignment: Alignment.center,
                                textStyle: TextStyle(
                                  fontFamily: "Roboto",
                                  fontSize: 30,
                                  height: 1,
                                  color: Colors.white,
                                  backgroundColor: Colors.red,
                                ),
                              ),
                            ),
                          ),
                          Spacer(flex: 100),
                        ],
                      ),
                    )
                  : const Spacer(flex: 30),
              const Spacer(
                flex: 137,
              ),
              const Expanded(
                flex: 156,
                child: Center(
                  child: ico_mb_upgrade_l(),
                ),
              ),
              const Spacer(
                flex: 128,
              ),
              Expanded(
                flex: 70,
                child: Row(
                  children: [
                    const Spacer(
                      flex: 138,
                    ),
                    Expanded(
                      flex: 205,
                      child: grp_btn_usb1_h70(
                        onTap: () => viewModel.onUSB1ButtonClick(),
                        state: state.isUsbUpgrade
                            ? ButtonState.normal
                            : ButtonState.disable,
                        usbInfo: state.usbInfoList.isEmpty
                            ? null
                            : state.usbInfoList[viewModel.usbButton1],
                      ),
                    ),
                    const Spacer(
                      flex: 8,
                    ),
                    Expanded(
                      flex: 205,
                      child: state.usbInfoList.length < maxUsbDisplayNumber
                          ? Container()
                          : grp_btn_usb2_h70(
                              onTap: () => viewModel.onUSB2ButtonClick(),
                              state: state.isUsbUpgrade
                                  ? ButtonState.normal
                                  : ButtonState.disable,
                              usbInfo: state.usbInfoList[viewModel.usbButton2],
                            ),
                    ),
                    const Spacer(
                      flex: 8,
                    ),
                    Expanded(
                        flex: 98,
                        child: grp_btn_wifipocket(
                          onTap: () => viewModel.onWifiPocketButtonClick(),
                          state: state.isWifiConnect
                              ? ButtonState.normal
                              : ButtonState.disable,
                        )),
                    const Spacer(
                      flex: 138,
                    ),
                  ],
                ),
              ),
              const Spacer(flex: 115),
              Expanded(
                  flex: 207,
                  child: Row(
                    children: [
                      const Spacer(flex: 40),
                      Expanded(
                          flex: 720,
                          child: grp_str_two(
                            text: state.errorValue,
                          )),
                      const Spacer(flex: 40),
                    ],
                  )),
              const Spacer(flex: 304),
              Expanded(
                  flex: 45,
                  child: Row(
                    children: [
                      const Spacer(
                        flex: 420,
                      ),
                      Expanded(
                          flex: 340,
                          child: grp_str_five(
                            text: "UPG VERSION : ${state.upgVersion}",
                            align: Alignment.topRight,
                          )),
                      const Spacer(
                        flex: 40,
                      ),
                    ],
                  )),
              Expanded(
                flex: 80,
                child: Row(
                  children: [
                    const Spacer(flex: 420),
                    Expanded(
                        flex: 340,
                        child: MouseRegion(
                            cursor: SystemMouseCursors.basic,
                            child: GestureDetector(
                              onDoubleTap: viewModel.launchAndroidSettings,
                            )))
                  ],
                ),
              ),
              const Spacer(
                flex: 8,
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup(WidgetRef ref) => {};
}
