import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_v_stitch_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'line_v_stitch_thickness_popup_view_interface.dart';

typedef Unit = DisplayUnit;

final lineVStitchThicknessViewModelProvider = StateNotifierProvider.autoDispose<
    LineVStitchThicknessStateViewInterface,
    LineVStitchThicknessState>((ref) => LineVStitchThicknessViewModel(ref));

class LineVStitchThicknessViewModel
    extends LineVStitchThicknessStateViewInterface {
  LineVStitchThicknessViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const LineVStitchThicknessState(
                thicknessDisplayValue: "",
                isDefaultValue: false,
                plusButtonValid: false,
                minusButtonValid: false),
            ref) {
    update();
  }

  ///
  /// 最大幅値
  final int maxThicknessValue = LineVStitchModel.maxiThicknessValue;

  ///
  /// 最小幅値
  ///
  final int minThicknessValue = LineVStitchModel.miniThicknessValue;

  ///
  /// ステップ量
  ///
  final int _stepValue = 2;

  ///
  /// 単位取得する
  ///
  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  ///
  /// 厚さ値表示星
  ///
  bool _isThicknessValueDisplayStar =
      LineVStitchModel().getThickness() != LineVStitchModel.thicknessNotUpdating
          ? false
          : true;

  ///
  /// 厚さ値
  ///
  int _thicknessValue = LineVStitchModel().getThickness();

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    state = state.copyWith(
        thicknessDisplayValue: _getThicknessDisplayValue(),
        isDefaultValue: _getIsDefaultValue(),
        plusButtonValid: _getPlusButtonState(),
        minusButtonValid: _getMinusButtonState());
  }

  ///
  /// マイナスボタンをクリックする
  ///
  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isThicknessValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isThicknessValueDisplayStar = false;

      ///  Model 更新
      _thicknessValue = LineVStitchModel().thicknessDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_thicknessValue <= LineVStitchModel.miniThicknessValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _thicknessValue =
        _thicknessValue - _stepValue < LineVStitchModel.miniThicknessValue
            ? LineVStitchModel.miniThicknessValue
            : _thicknessValue - _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  ///
  /// プラスボタンをクリックする
  ///
  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_isThicknessValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isThicknessValueDisplayStar = false;

      ///  Model 更新
      _thicknessValue = LineVStitchModel().thicknessDefaultValue;

      /// View更新
      update();

      return false;
    }
    if (_thicknessValue >= LineVStitchModel.maxiThicknessValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _thicknessValue =
        _thicknessValue + _stepValue > LineVStitchModel.maxiThicknessValue
            ? LineVStitchModel.maxiThicknessValue
            : _thicknessValue + _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  ///
  /// OKボタンがクリックされました
  ///
  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineVStitchThickness.toString());
    if (_isThicknessValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int thicknessValue = LineVStitchModel().getThickness();

    /// Model 更新
    LineVStitchModel().setThickness(_thicknessValue);

    if (LineVStitchModel().setMdcVStitchLineSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (thicknessValue != _thicknessValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// 厚さの表示値を取得します
  ///
  String _getThicknessDisplayValue() {
    if (_isThicknessValueDisplayStar) {
      return "*";
    }

    return _thicknessValue.toString();
  }

  ///
  /// 太さ表示のテキスト スタイルを取得します
  ///
  bool _getIsDefaultValue() {
    if (_isThicknessValueDisplayStar) {
      return true;
    }

    if (_thicknessValue == LineVStitchModel().thicknessDefaultValue) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isThicknessValueDisplayStar) {
      return true;
    }

    if (_thicknessValue <= LineVStitchModel.miniThicknessValue) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isThicknessValueDisplayStar) {
      return true;
    }

    if (_thicknessValue >= LineVStitchModel.maxiThicknessValue) {
      return false;
    }
    return true;
  }
}
