// ignore_for_file: always_specify_types
// ignore_for_file: camel_case_types
// ignore_for_file: non_constant_identifier_names

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
import 'dart:ffi' as ffi;
import 'dart:ffi';

import 'package:ffi/ffi.dart' as ffi;

/// Bindings for `src/EEL_EmbEditIF.h`.
///
/// Regenerate bindings with `flutter pub run ffigen --config ffigen_emb.yaml`.
///
class EmbEditBindings {
  /// Holds the symbol lookup function.
  final ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
      _lookup;

  /// The symbols are looked up in [dynamicLibrary].
  EmbEditBindings(ffi.DynamicLibrary dynamicLibrary)
      : _lookup = dynamicLibrary.lookup {}

  /// The symbols are looked up with [lookup].
  EmbEditBindings.fromLookup(
      ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
          lookup)
      : _lookup = lookup;

  /// EmbMode
  int initEmb() {
    return _initEmb();
  }

  late final _initEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('initEmb');
  late final _initEmb = _initEmbPtr.asFunction<int Function()>();

  int openEmbMode() {
    return _openEmbMode();
  }

  late final _openEmbModePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('openEmbMode');
  late final _openEmbMode = _openEmbModePtr.asFunction<int Function()>();

  void closeEmbMode() {
    return _closeEmbMode();
  }

  late final _closeEmbModePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('closeEmbMode');
  late final _closeEmbMode = _closeEmbModePtr.asFunction<void Function()>();

  int gotoEmbFromOpening() {
    return _gotoEmbFromOpening();
  }

  late final _gotoEmbFromOpeningPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoEmbFromOpening');
  late final _gotoEmbFromOpening =
      _gotoEmbFromOpeningPtr.asFunction<int Function()>();

  /// Edit
  void setOutColorDepth(
    int colorDepth,
  ) {
    return _setOutColorDepth(
      colorDepth,
    );
  }

  late final _setOutColorDepthPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Int32)>>(
          'setOutColorDepth');
  late final _setOutColorDepth =
      _setOutColorDepthPtr.asFunction<void Function(int)>();

  int getOutColorDepth() {
    return _getOutColorDepth();
  }

  late final _getOutColorDepthPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('getOutColorDepth');
  late final _getOutColorDepth =
      _getOutColorDepthPtr.asFunction<int Function()>();

  int selectEmb(
    int embIndex,
    int category,
    int bh,
    ffi.Pointer<MemHandle_t> emb,
  ) {
    return _selectEmb(
      embIndex,
      category,
      bh,
      emb,
    );
  }

  late final _selectEmbPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Int32, ffi.Int32, ffi.Int32,
              ffi.Pointer<MemHandle_t>)>>('selectEmb');
  late final _selectEmb = _selectEmbPtr
      .asFunction<int Function(int, int, int, ffi.Pointer<MemHandle_t>)>();

  int selectEmbGroupNext() {
    return _selectEmbGroupNext();
  }

  late final _selectEmbGroupNextPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('selectEmbGroupNext');
  late final _selectEmbGroupNext =
      _selectEmbGroupNextPtr.asFunction<int Function()>();

  int selectEmbGroupPrev() {
    return _selectEmbGroupPrev();
  }

  late final _selectEmbGroupPrevPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('selectEmbGroupPrev');
  late final _selectEmbGroupPrev =
      _selectEmbGroupPrevPtr.asFunction<int Function()>();

  int getEmbInfo(
    MemHandle_t groupH,
    ffi.Pointer<embInfo_t> embInfo,
  ) {
    return _getEmbInfo(
      groupH,
      embInfo,
    );
  }

  late final _getEmbInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              MemHandle_t, ffi.Pointer<embInfo_t>)>>('getEmbInfo');
  late final _getEmbInfo = _getEmbInfoPtr
      .asFunction<int Function(MemHandle_t, ffi.Pointer<embInfo_t>)>();

  int getEmbGrpInfo(
    MemHandle_t groupH,
    ffi.Pointer<embInfo_t> embInfo,
    ffi.Pointer<embGrp_t> grpInfo,
    ffi.Pointer<embPtrnInfo_t> ptrnInfo,
  ) {
    return _getEmbGrpInfo(
      groupH,
      embInfo,
      grpInfo,
      ptrnInfo,
    );
  }

  late final _getEmbGrpInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              MemHandle_t,
              ffi.Pointer<embInfo_t>,
              ffi.Pointer<embGrp_t>,
              ffi.Pointer<embPtrnInfo_t>)>>('getEmbGrpInfo');
  late final _getEmbGrpInfo = _getEmbGrpInfoPtr.asFunction<
      int Function(MemHandle_t, ffi.Pointer<embInfo_t>, ffi.Pointer<embGrp_t>,
          ffi.Pointer<embPtrnInfo_t>)>();

  void deleteEmbGrpPatternInfo(
      ffi.Pointer<embPtrnInfo_t> embPatternInfo,
      ) {
    _deleteEmbGrpPatternInfo(
      embPatternInfo,
    );
  }

  late final _deleteEmbGrpPatternInfoPtr = _lookup<
      ffi.NativeFunction<ffi.Void Function(ffi.Pointer<embPtrnInfo_t>)>>(
      'deleteEmbGrpPatternInfo');
  late final _deleteEmbGrpPatternInfo = _deleteEmbGrpPatternInfoPtr
      .asFunction<void Function(ffi.Pointer<embPtrnInfo_t>)>();

  int deleteThreadInfo(
    ffi.Pointer<ffi.Pointer<threadInfo_t>> threadInfo,
  ) {
    return _deleteThreadInfo(threadInfo);
  }

  late final _deleteThreadInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Pointer<threadInfo_t>>)>>('deleteThreadInfo');
  late final _deleteThreadInfo = _deleteThreadInfoPtr
      .asFunction<int Function(ffi.Pointer<ffi.Pointer<threadInfo_t>>)>();

  /// embErrorCode_t getEmbGrpPatternInfo(MemHandle_t groupH, embPtrnInfo_t *embPatternInfo);
  int getEmbEditThreadInfo(
    MemHandle_t groupH,
    ffi.Pointer<ffi.Pointer<threadInfo_t>> threadInfo,
    ffi.Pointer<ffi.Int32> threadInfoNum,
  ) {
    return _getEmbEditThreadInfo(
      groupH,
      threadInfo,
      threadInfoNum,
    );
  }

  late final _getEmbEditThreadInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              MemHandle_t,
              ffi.Pointer<ffi.Pointer<threadInfo_t>>,
              ffi.Pointer<ffi.Int32>)>>('getEmbEditThreadInfo');
  late final _getEmbEditThreadInfo = _getEmbEditThreadInfoPtr.asFunction<
      int Function(MemHandle_t, ffi.Pointer<ffi.Pointer<threadInfo_t>>,
          ffi.Pointer<ffi.Int32>)>();

  int getEmbSewingThreadInfoAll(
    ffi.Pointer<ffi.Pointer<threadInfo_t>> threadInfo,
    ffi.Pointer<ffi.Int32> threadInfoNum,
    ffi.Pointer<ffi.Int32> threadInfoCurIdx,
  ) {
    return _getEmbSewingThreadInfoAll(
      threadInfo,
      threadInfoNum,
      threadInfoCurIdx,
    );
  }

  late final _getEmbSewingThreadInfoAllPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Pointer<threadInfo_t>>,
              ffi.Pointer<ffi.Int32>,
              ffi.Pointer<ffi.Int32>)>>('getEmbSewingThreadInfoAll');
  late final _getEmbSewingThreadInfoAll =
      _getEmbSewingThreadInfoAllPtr.asFunction<
          int Function(ffi.Pointer<ffi.Pointer<threadInfo_t>>,
              ffi.Pointer<ffi.Int32>, ffi.Pointer<ffi.Int32>)>();

  int selectEmbToEdit(
    MemHandle_t groupH,
  ) {
    return _selectEmbToEdit(
      groupH,
    );
  }

  late final _selectEmbToEditPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(MemHandle_t)>>(
          'selectEmbToEdit');
  late final _selectEmbToEdit =
      _selectEmbToEditPtr.asFunction<int Function(MemHandle_t)>();

  int prepareAddEmb() {
    return _prepareAddEmb();
  }

  late final _prepareAddEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('prepareAddEmb');
  late final _prepareAddEmb = _prepareAddEmbPtr.asFunction<int Function()>();

  int deleteEmb() {
    return _deleteEmb();
  }

  late final _deleteEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('deleteEmb');
  late final _deleteEmb = _deleteEmbPtr.asFunction<int Function()>();

  int changeColorByPalette(
    int brandCode,
    int index,
  ) {
    return _changeColorByPalette(
      brandCode,
      index,
    );
  }

  late final _changeColorByPalettePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Uint8, ffi.Int32)>>(
          'changeColorByPalette');
  late final _changeColorByPalette =
      _changeColorByPalettePtr.asFunction<int Function(int, int)>();

  int changeColor(
    int r,
    int g,
    int b,
  ) {
    return _changeColor(
      r,
      g,
      b,
    );
  }

  late final _changeColorPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint8, ffi.Uint8, ffi.Uint8)>>('changeColor');
  late final _changeColor =
      _changeColorPtr.asFunction<int Function(int, int, int)>();

  int getThreadColorTable(
    int brandCode,
    ffi.Pointer<ffi.Pointer<BrandColorTbl>> tcTable,
    ffi.Pointer<ffi.Uint32> recordNum,
  ) {
    return _getThreadColorTable(
      brandCode,
      tcTable,
      recordNum,
    );
  }

  late final _getThreadColorTablePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint8, ffi.Pointer<ffi.Pointer<BrandColorTbl>>,
              ffi.Pointer<ffi.Uint32>)>>('getThreadColorTable');
  late final _getThreadColorTable = _getThreadColorTablePtr.asFunction<
      int Function(int, ffi.Pointer<ffi.Pointer<BrandColorTbl>>,
          ffi.Pointer<ffi.Uint32>)>();

  int getThreadColor61Table(
    int brandCode,
    ffi.Pointer<ffi.Pointer<BrandColorTbl>> tcTable,
  ) {
    return _getThreadColor61Table(
      brandCode,
      tcTable,
    );
  }

  late final _getThreadColor61TablePtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  ffi.Uint8, ffi.Pointer<ffi.Pointer<BrandColorTbl>>)>>(
      'getThreadColor61Table');
  late final _getThreadColor61Table = _getThreadColor61TablePtr
      .asFunction<int Function(int, ffi.Pointer<ffi.Pointer<BrandColorTbl>>)>();

  int getThreadColorName(
    int brandCode,
    int threadCode,
    ffi.Pointer<ffi.UnsignedChar> pRGBCode,
    ffi.Pointer<ffi.Pointer<ffi.UnsignedShort>> colorName,
  ) {
    return _getThreadColorName(
      brandCode,
      threadCode,
      pRGBCode,
      colorName,
    );
  }

  late final _getThreadColorNamePtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  ffi.Uint8,
                  ffi.Uint16,
                  ffi.Pointer<ffi.UnsignedChar>,
                  ffi.Pointer<ffi.Pointer<ffi.UnsignedShort>>)>>(
      'getThreadColorName');
  late final _getThreadColorName = _getThreadColorNamePtr.asFunction<
      int Function(int, int, ffi.Pointer<ffi.UnsignedChar>,
          ffi.Pointer<ffi.Pointer<ffi.UnsignedShort>>)>();

  int saveEmbData(
    ffi.Pointer<ffi.Char> fileName,
  ) {
    return _saveEmbData(
      fileName,
    );
  }

  late final _saveEmbDataPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Char>)>>(
          'saveEmbData');
  late final _saveEmbData =
      _saveEmbDataPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int saveEmbResumeData(
    ffi.Pointer<ffi.Char> fileName,
  ) {
    return _saveEmbResumeData(
      fileName,
    );
  }

  late final _saveEmbResumeDataPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Char>)>>(
          'saveEmbResumeData');
  late final _saveEmbResumeData =
      _saveEmbResumeDataPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int checkEmbPatternType(
    MemHandle_t groupH,
    int embType,
    ffi.Pointer<ffi.Bool> result,
  ) {
    return _checkEmbPatternType(
      groupH,
      embType,
      result,
    );
  }

  late final _checkEmbPatternTypePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(MemHandle_t, ffi.Int32,
              ffi.Pointer<ffi.Bool>)>>('checkEmbPatternType');
  late final _checkEmbPatternType = _checkEmbPatternTypePtr
      .asFunction<int Function(MemHandle_t, int, ffi.Pointer<ffi.Bool>)>();

  /// Move
  int moveEmb(
    int dirX,
    int dirY,
  ) {
    return _moveEmb(
      dirX,
      dirY,
    );
  }

  late final _moveEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32, ffi.Int32)>>(
          'moveEmb');
  late final _moveEmb = _moveEmbPtr.asFunction<int Function(int, int)>();

  int moveEmbCenter() {
    return _moveEmbCenter();
  }

  late final _moveEmbCenterPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('moveEmbCenter');
  late final _moveEmbCenter = _moveEmbCenterPtr.asFunction<int Function()>();

  int moveEmbAll(
    int dirX,
    int dirY,
    bool flgLongPress,
  ) {
    return _moveEmbAll(
      dirX,
      dirY,
      flgLongPress,
    );
  }

  late final _moveEmbAllPtr = _lookup<
          ffi.NativeFunction<ffi.Int32 Function(ffi.Int, ffi.Int, ffi.Bool)>>(
      'moveEmbAll');
  late final _moveEmbAll =
      _moveEmbAllPtr.asFunction<int Function(int, int, bool)>();

  // int moveEmbAllFrameNoMove(
  //   int dirX,
  //   int dirY,
  // ) {
  //   return _moveEmbAllFrameNoMove(
  //     dirX,
  //     dirY,
  //   );
  // }

  // late final _moveEmbAllFrameNoMovePtr =
  //     _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32, ffi.Int32)>>(
  //         'moveEmbAllFrameNoMove');
  // late final _moveEmbAllFrameNoMove =
  //     _moveEmbAllFrameNoMovePtr.asFunction<int Function(int, int)>();

  int moveEmbAllCenter() {
    return _moveEmbAllCenter();
  }

  late final _moveEmbAllCenterPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('moveEmbAllCenter');
  late final _moveEmbAllCenter =
      _moveEmbAllCenterPtr.asFunction<int Function()>();

  // int moveEmbAllCenterFrameNoMove() {
  //   return _moveEmbAllCenterFrameNoMove();
  // }

  // late final _moveEmbAllCenterFrameNoMovePtr =
  //     _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
  //         'moveEmbAllCenterFrameNoMove');
  // late final _moveEmbAllCenterFrameNoMove =
  //     _moveEmbAllCenterFrameNoMovePtr.asFunction<int Function()>();

  int moveQuiltEmb(
    int dirX,
    int dirY,
    bool flgLongPress,
  ) {
    return _moveQuiltEmb(
      dirX,
      dirY,
      flgLongPress,
    );
  }

  late final _moveQuiltEmbPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Int32, ffi.Int32, ffi.Bool)>>('moveQuiltEmb');
  late final _moveQuiltEmb =
      _moveQuiltEmbPtr.asFunction<int Function(int, int, bool)>();

  /// Rotate *****/
  /// //bool rotateEmb(int16_t angle, std::vector<uint32_t> *embImage);
  int rotateEmb(
    int angle,
  ) {
    return _rotateEmb(
      angle,
    );
  }

  late final _rotateEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int16)>>('rotateEmb');
  late final _rotateEmb = _rotateEmbPtr.asFunction<int Function(int)>();

  int resetEmbRotate() {
    return _resetEmbRotate();
  }

  late final _resetEmbRotatePtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('resetEmbRotate');
  late final _resetEmbRotate =
  _resetEmbRotatePtr.asFunction<int Function()>();


  int rotateEmbAll(
    int angle,
  ) {
    return _rotateEmbAll(
      angle,
    );
  }

  late final _rotateEmbAllPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int16)>>(
          'rotateEmbAll');
  late final _rotateEmbAll = _rotateEmbAllPtr.asFunction<int Function(int)>();

  int rotateQuiltEmb(
    int angle,
  ) {
    return _rotateQuiltEmb(
      angle,
    );
  }

  late final _rotateQuiltEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int16)>>(
          'rotateQuiltEmb');
  late final _rotateQuiltEmb =
      _rotateQuiltEmbPtr.asFunction<int Function(int)>();

  int resetEmbAllRotate() {
    return _resetEmbAllRotate();
  }

  late final _resetEmbAllRotatePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('resetEmbAllRotate');
  late final _resetEmbAllRotate =
      _resetEmbAllRotatePtr.asFunction<int Function()>();

  /// ↓IIVO　刺繍キルトつなぎAPI
  int scalingQuiltEmb(
    int scale,
  ) {
    return _scalingQuiltEmb(
      scale,
    );
  }

  late final _scalingQuiltEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'scalingQuiltEmb');
  late final _scalingQuiltEmb =
      _scalingQuiltEmbPtr.asFunction<int Function(int)>();

  int startPointSetQuiltEmb(
    int point,
  ) {
    return _startPointSetQuiltEmb(
      point,
    );
  }

  late final _startPointSetQuiltEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'startPointSetQuiltEmb');
  late final _startPointSetQuiltEmb =
      _startPointSetQuiltEmbPtr.asFunction<int Function(int)>();

  int selectPartsQuitEmb(
    int select,
  ) {
    return _selectPartsQuitEmb(
      select,
    );
  }

  late final _selectPartsQuitEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'selectPartsQuitEmb');
  late final _selectPartsQuitEmb =
      _selectPartsQuitEmbPtr.asFunction<int Function(int)>();

  /// Density
  int setEmbDensity(
    MemHandle_t groupH,
    int density,
  ) {
    return _setEmbDensity(
      groupH,
      density,
    );
  }

  late final _setEmbDensityPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(MemHandle_t, ffi.Int32)>>(
          'setEmbDensity');
  late final _setEmbDensity =
      _setEmbDensityPtr.asFunction<int Function(MemHandle_t, int)>();

  /// Enlarge/Reduce
  int setStbMode() {
    return _setStbMode();
  }

  late final _setStbModePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setStbMode');
  late final _setStbMode = _setStbModePtr.asFunction<int Function()>();

  int resetStbMode() {
    return _resetStbMode();
  }

  late final _resetStbModePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('resetStbMode');
  late final _resetStbMode = _resetStbModePtr.asFunction<int Function()>();

  int changeSizeEmbGroup(
    int type,
    int step,
  ) {
    return _changeSizeEmbGroup(
      type,
      step,
    );
  }

  late final _changeSizeEmbGroupPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32, ffi.Int32)>>(
          'changeSizeEmbGroup');
  late final _changeSizeEmbGroup =
      _changeSizeEmbGroupPtr.asFunction<int Function(int, int)>();

  int changeSizeEmbGroupSTB(
    int type,
    int step,
  ) {
    return _changeSizeEmbGroupSTB(
      type,
      step,
    );
  }

  late final _changeSizeEmbGroupSTBPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32, ffi.Int32)>>(
          'changeSizeEmbGroupSTB');
  late final _changeSizeEmbGroupSTB =
      _changeSizeEmbGroupSTBPtr.asFunction<int Function(int, int)>();

  int changeSizeQuiltEmb(
    int type,
    int step,
  ) {
    return _changeSizeQuiltEmb(
      type,
      step,
    );
  }

  late final _changeSizeQuiltEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32, ffi.Int32)>>(
          'changeSizeQuiltEmb');
  late final _changeSizeQuiltEmb =
      _changeSizeQuiltEmbPtr.asFunction<int Function(int, int)>();

  /// Mirror
  int flipEmbHorizontal() {
    return _flipEmbHorizontal();
  }

  late final _flipEmbHorizontalPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('flipEmbHorizontal');
  late final _flipEmbHorizontal =
      _flipEmbHorizontalPtr.asFunction<int Function()>();

  /// Duplicate
  int duplicateEmb(
    ffi.Pointer<MemHandle_t> dupHArray,
    ffi.Pointer<ffi.Int32> handleNum,
  ) {
    return _duplicateEmb(
      dupHArray,
      handleNum,
    );
  }

  late final _duplicateEmbPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<MemHandle_t>,
              ffi.Pointer<ffi.Int32>)>>('duplicateEmb');
  late final _duplicateEmb = _duplicateEmbPtr.asFunction<
      int Function(ffi.Pointer<MemHandle_t>, ffi.Pointer<ffi.Int32>)>();

  /// Undo/Redo *****/
  /// // bool undoEdit(std::vector<uint32_t> *embImage);
  /// // bool redoEdit(std::vector<uint32_t> *embImage);
  int saveUndoRedoFile(
    ffi.Pointer<ffi.Char> fileName,
  ) {
    return _saveUndoRedoFile(
      fileName,
    );
  }

  late final _saveUndoRedoFilePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Char>)>>(
          'saveUndoRedoFile');
  late final _saveUndoRedoFile =
      _saveUndoRedoFilePtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int undoRedo(
    ffi.Pointer<ffi.Char> fileName,
  ) {
    return _undoRedo(
      fileName,
    );
  }

  late final _undoRedoPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Char>)>>(
          'undoRedo');
  late final _undoRedo =
      _undoRedoPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  /// Border
  int checkBeforeBorderProc() {
    return _checkBeforeBorderProc();
  }

  late final _checkBeforeBorderProcPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'checkBeforeBorderProc');
  late final _checkBeforeBorderProc =
      _checkBeforeBorderProcPtr.asFunction<int Function()>();

  int checkBeforeEasyStippleMakeProc() {
    return _checkBeforeEasyStippleMakeProc();
  }

  late final _checkBeforeEasyStippleMakeProcPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'checkBeforeEasyStippleMakeProc');
  late final _checkBeforeEasyStippleMakeProc =
      _checkBeforeEasyStippleMakeProcPtr.asFunction<int Function()>();

  int startEmbBorder() {
    return _startEmbBorder();
  }

  late final _startEmbBorderPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('startEmbBorder');
  late final _startEmbBorder = _startEmbBorderPtr.asFunction<int Function()>();

  int addBorder(
    int borderPos,
    ffi.Pointer<ffi.Pointer<EmbBorder_t>> borderH,
  ) {
    return _addBorder(
      borderPos,
      borderH,
    );
  }

  late final _addBorderPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Int32, ffi.Pointer<ffi.Pointer<EmbBorder_t>>)>>('addBorder');
  late final _addBorder = _addBorderPtr
      .asFunction<int Function(int, ffi.Pointer<ffi.Pointer<EmbBorder_t>>)>();

  int delBorder(
    int borderPos,
  ) {
    return _delBorder(
      borderPos,
    );
  }

  late final _delBorderPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>('delBorder');
  late final _delBorder = _delBorderPtr.asFunction<int Function(int)>();

  int divideBorder(
    int direction,
    int index,
  ) {
    return _divideBorder(
      direction,
      index,
    );
  }

  late final _divideBorderPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32, ffi.Uint16)>>(
          'divideBorder');
  late final _divideBorder =
      _divideBorderPtr.asFunction<int Function(int, int)>();

  int setBorderSpace(
    int direction,
    int space,
  ) {
    return _setBorderSpace(
      direction,
      space,
    );
  }

  late final _setBorderSpacePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32, ffi.Int16)>>(
          'setBorderSpace');
  late final _setBorderSpace =
      _setBorderSpacePtr.asFunction<int Function(int, int)>();

  int getBorderHandleAll(
    ffi.Pointer<MemHandle_t> borderHArray,
    ffi.Pointer<ffi.Int32> handleNum,
  ) {
    return _getBorderHandleAll(
      borderHArray,
      handleNum,
    );
  }

  late final _getBorderHandleAllPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<MemHandle_t>,
              ffi.Pointer<ffi.Int32>)>>('getBorderHandleAll');
  late final _getBorderHandleAll = _getBorderHandleAllPtr.asFunction<
      int Function(ffi.Pointer<MemHandle_t>, ffi.Pointer<ffi.Int32>)>();

  int getGroupHandleInBorder(
    ffi.Pointer<EmbBorder_t> borderHandle,
    ffi.Pointer<MemHandle_t> grpHArray,
    ffi.Pointer<ffi.Int32> handleNum,
  ) {
    return _getGroupHandleInBorder(
      borderHandle,
      grpHArray,
      handleNum,
    );
  }

  late final _getGroupHandleInBorderPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<EmbBorder_t>, ffi.Pointer<MemHandle_t>,
              ffi.Pointer<ffi.Int32>)>>('getGroupHandleInBorder');
  late final _getGroupHandleInBorder = _getGroupHandleInBorderPtr.asFunction<
      int Function(ffi.Pointer<EmbBorder_t>, ffi.Pointer<MemHandle_t>,
          ffi.Pointer<ffi.Int32>)>();

  int getBorderInfo(
    ffi.Pointer<EmbBorder_t> borderHandle,
    ffi.Pointer<borderInfo_t> borderInfo,
  ) {
    return _getBorderInfo(
      borderHandle,
      borderInfo,
    );
  }

  late final _getBorderInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<EmbBorder_t>,
              ffi.Pointer<borderInfo_t>)>>('getBorderInfo');
  late final _getBorderInfo = _getBorderInfoPtr.asFunction<
      int Function(ffi.Pointer<EmbBorder_t>, ffi.Pointer<borderInfo_t>)>();

  int getBorderHandleIncludeGroup(
    MemHandle_t group,
    ffi.Pointer<ffi.Pointer<EmbBorder_t>> borderHandle,
  ) {
    return _getBorderHandleIncludeGroup(
      group,
      borderHandle,
    );
  }

  late final _getBorderHandleIncludeGroupPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  MemHandle_t, ffi.Pointer<ffi.Pointer<EmbBorder_t>>)>>(
      'getBorderHandleIncludeGroup');
  late final _getBorderHandleIncludeGroup =
      _getBorderHandleIncludeGroupPtr.asFunction<
          int Function(MemHandle_t, ffi.Pointer<ffi.Pointer<EmbBorder_t>>)>();

  int asignThreadMark(
    int index,
    int setReset,
    int position,
  ) {
    return _asignThreadMark(
      index,
      setReset,
      position,
    );
  }

  late final _asignThreadMarkPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint32, ffi.Int32, ffi.Int32)>>('asignThreadMark');
  late final _asignThreadMark =
      _asignThreadMarkPtr.asFunction<int Function(int, int, int)>();

  int endEmbBorder() {
    return _endEmbBorder();
  }

  late final _endEmbBorderPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('endEmbBorder');
  late final _endEmbBorder = _endEmbBorderPtr.asFunction<int Function()>();

  int getBorderCompInfo(
    MemHandle_t borderH,
    int index,
    ffi.Pointer<RectanArea_t> elementPos,
    ffi.Pointer<threadMarkState_t> mark,
  ) {
    return _getBorderCompInfo(
      borderH,
      index,
      elementPos,
      mark,
    );
  }

  late final _getBorderCompInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(MemHandle_t, ffi.Uint32, ffi.Pointer<RectanArea_t>,
              ffi.Pointer<threadMarkState_t>)>>('getBorderCompInfo');
  late final _getBorderCompInfo = _getBorderCompInfoPtr.asFunction<
      int Function(MemHandle_t, int, ffi.Pointer<RectanArea_t>,
          ffi.Pointer<threadMarkState_t>)>();

  int setBorderCurIdx(
    int index,
  ) {
    return _setBorderCurIdx(
      index,
    );
  }

  late final _setBorderCurIdxPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Uint32)>>(
          'setBorderCurIdx');
  late final _setBorderCurIdx =
      _setBorderCurIdxPtr.asFunction<int Function(int)>();

  /// Applique
  int initAppliqueSelect() {
    return _initAppliqueSelect();
  }

  late final _initAppliqueSelectPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('initAppliqueSelect');
  late final _initAppliqueSelect =
      _initAppliqueSelectPtr.asFunction<int Function()>();

  int deleteMarkBeforeWappen() {
    return _deleteMarkBeforeWappen();
  }

  late final _deleteMarkBeforeWappenPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'deleteMarkBeforeWappen');
  late final _deleteMarkBeforeWappen =
      _deleteMarkBeforeWappenPtr.asFunction<int Function()>();

  int cancelBorderBeforeWappen(
    ffi.Pointer<ffi.Pointer<EmbBorder_t>> borderH,
  ) {
    return _cancelBorderBeforeWappen(
      borderH,
    );
  }

  late final _cancelBorderBeforeWappenPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<ffi.Pointer<EmbBorder_t>>)>>(
      'cancelBorderBeforeWappen');
  late final _cancelBorderBeforeWappen = _cancelBorderBeforeWappenPtr
      .asFunction<int Function(ffi.Pointer<ffi.Pointer<EmbBorder_t>>)>();

  int makeApplique(
    ffi.Pointer<MemHandle_t> grpH,
  ) {
    return _makeApplique(
      grpH,
    );
  }

  late final _makeAppliquePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<MemHandle_t>)>>(
          'makeApplique');
  late final _makeApplique =
      _makeAppliquePtr.asFunction<int Function(ffi.Pointer<MemHandle_t>)>();

  int finishApplique() {
    return _finishApplique();
  }

  late final _finishAppliquePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('finishApplique');
  late final _finishApplique = _finishAppliquePtr.asFunction<int Function()>();

  int setDistanceApplique(
    int distance,
    ffi.Pointer<MemHandle_t> grpH,
  ) {
    return _setDistanceApplique(
      distance,
      grpH,
    );
  }

  late final _setDistanceAppliquePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint8, ffi.Pointer<MemHandle_t>)>>('setDistanceApplique');
  late final _setDistanceApplique = _setDistanceAppliquePtr
      .asFunction<int Function(int, ffi.Pointer<MemHandle_t>)>();

  int cancelApplique() {
    return _cancelApplique();
  }

  late final _cancelAppliquePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('cancelApplique');
  late final _cancelApplique = _cancelAppliquePtr.asFunction<int Function()>();

  /// Applique selected colors
  int checkUsableAppliqueParts() {
    return _checkUsableAppliqueParts();
  }

  late final _checkUsableAppliquePartsPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'checkUsableAppliqueParts');
  late final _checkUsableAppliqueParts =
      _checkUsableAppliquePartsPtr.asFunction<int Function()>();

  int goAppliquePartsSelection() {
    return _goAppliquePartsSelection();
  }

  late final _goAppliquePartsSelectionPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'goAppliquePartsSelection');
  late final _goAppliquePartsSelection =
      _goAppliquePartsSelectionPtr.asFunction<int Function()>();

  int goAppliqueParameterSetting() {
    return _goAppliqueParameterSetting();
  }

  late final _goAppliqueParameterSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'goAppliqueParameterSetting');
  late final _goAppliqueParameterSetting =
      _goAppliqueParameterSettingPtr.asFunction<int Function()>();

  int selectAppliquePartsFile(
    MemHandle_t groupH,
    int index,
    bool selection,
    ffi.Pointer<ffi.Char> outFile,
  ) {
    return _selectAppliquePartsFile(
      groupH,
      index,
      selection,
      outFile,
    );
  }

  late final _selectAppliquePartsFilePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(MemHandle_t, ffi.Int16, ffi.Bool,
              ffi.Pointer<ffi.Char>)>>('selectAppliquePartsFile');
  late final _selectAppliquePartsFile = _selectAppliquePartsFilePtr.asFunction<
      int Function(MemHandle_t, int, bool, ffi.Pointer<ffi.Char>)>();

  int selectAppliqueParts(
    MemHandle_t groupH,
    int index,
    bool selection,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _selectAppliqueParts(
      groupH,
      index,
      selection,
      embImageInfo,
    );
  }

  late final _selectAppliquePartsPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(MemHandle_t, ffi.Int16, ffi.Bool,
              ffi.Pointer<embImageInfo_t>)>>('selectAppliqueParts');
  late final _selectAppliqueParts = _selectAppliquePartsPtr.asFunction<
      int Function(MemHandle_t, int, bool, ffi.Pointer<embImageInfo_t>)>();

  int selectAppliquePartsAll(
    MemHandle_t groupH,
    bool selection,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _selectAppliquePartsAll(
      groupH,
      selection,
      embImageInfo,
    );
  }

  late final _selectAppliquePartsAllPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(MemHandle_t, ffi.Bool,
              ffi.Pointer<embImageInfo_t>)>>('selectAppliquePartsAll');
  late final _selectAppliquePartsAll = _selectAppliquePartsAllPtr.asFunction<
      int Function(MemHandle_t, bool, ffi.Pointer<embImageInfo_t>)>();

  int makeAppliqueSelectedParts(
    ffi.Pointer<appliquePartsParam_t> param,
    ffi.Pointer<MemHandle_t> grpH,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _makeAppliqueSelectedParts(
      param,
      grpH,
      embImageInfo,
    );
  }

  late final _makeAppliqueSelectedPartsPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<appliquePartsParam_t>,
              ffi.Pointer<MemHandle_t>,
              ffi.Pointer<embImageInfo_t>)>>('makeAppliqueSelectedParts');
  late final _makeAppliqueSelectedParts =
      _makeAppliqueSelectedPartsPtr.asFunction<
          int Function(ffi.Pointer<appliquePartsParam_t>,
              ffi.Pointer<MemHandle_t>, ffi.Pointer<embImageInfo_t>)>();

  int editSelectedApplique() {
    return _editSelectedApplique();
  }

  late final _editSelectedAppliquePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('editSelectedApplique');
  late final _editSelectedApplique =
      _editSelectedAppliquePtr.asFunction<int Function()>();

  int getWappenPreviewTexture(
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getWappenPreviewTexture(
      embImageInfo,
    );
  }

  late final _getWappenPreviewTexturePtr = _lookup<
          ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<embImageInfo_t>)>>(
      'getWappenPreviewTexture');
  late final _getWappenPreviewTexture = _getWappenPreviewTexturePtr
      .asFunction<int Function(ffi.Pointer<embImageInfo_t>)>();

  int changeSettingsSweing(
    MemHandle_t groupH,
    int type,
    int index,
    bool notSewing,
  ) {
    return _changeSettingsSweing(
      groupH,
      type,
      index,
      notSewing,
    );
  }

  late final _changeSettingsSweingPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(MemHandle_t, ffi.Int32, ffi.Int32,
              ffi.Bool)>>('changeSettingsSweing');
  late final _changeSettingsSweing = _changeSettingsSweingPtr
      .asFunction<int Function(MemHandle_t, int, int, bool)>();

  int setTexture(
    bool texture,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _setTexture(
      texture,
      embImageInfo,
    );
  }

  late final _setTexturePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Bool, ffi.Pointer<embImageInfo_t>)>>('setTexture');
  late final _setTexture = _setTexturePtr
      .asFunction<int Function(bool, ffi.Pointer<embImageInfo_t>)>();

  int cancelPreviewApplique() {
    return _cancelPreviewApplique();
  }

  late final _cancelPreviewAppliquePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'cancelPreviewApplique');
  late final _cancelPreviewApplique =
      _cancelPreviewAppliquePtr.asFunction<int Function()>();

  int completeSelectedApplique() {
    return _completeSelectedApplique();
  }

  late final _completeSelectedAppliquePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'completeSelectedApplique');
  late final _completeSelectedApplique =
      _completeSelectedAppliquePtr.asFunction<int Function()>();

  int cancelSelectedApplique() {
    return _cancelSelectedApplique();
  }

  late final _cancelSelectedAppliquePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'cancelSelectedApplique');
  late final _cancelSelectedApplique =
      _cancelSelectedAppliquePtr.asFunction<int Function()>();

  int checkTextureDrawing() {
    return _checkTextureDrawing();
  }

  late final _checkTextureDrawingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('checkTextureDrawing');
  late final _checkTextureDrawing =
      _checkTextureDrawingPtr.asFunction<int Function()>();

  /// Stippling
  int checkBorderMarkStipple() {
    return _checkBorderMarkStipple();
  }

  late final _checkBorderMarkStipplePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'checkBorderMarkStipple');
  late final _checkBorderMarkStipple =
      _checkBorderMarkStipplePtr.asFunction<int Function()>();

  int initMakingEasyStipple() {
    return _initMakingEasyStipple();
  }

  late final _initMakingEasyStipplePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'initMakingEasyStipple');
  late final _initMakingEasyStipple =
      _initMakingEasyStipplePtr.asFunction<int Function()>();

  int makeStipple(
    int type,
    ffi.Pointer<MemHandle_t> grpH,
  ) {
    return _makeStipple(
      type,
      grpH,
    );
  }

  late final _makeStipplePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Int32, ffi.Pointer<MemHandle_t>)>>('makeStipple');
  late final _makeStipple =
      _makeStipplePtr.asFunction<int Function(int, ffi.Pointer<MemHandle_t>)>();

  int setStipple(
    int frSize,
    int distance,
    int space,
    ffi.Pointer<MemHandle_t> grpH,
  ) {
    return _setStipple(
      frSize,
      distance,
      space,
      grpH,
    );
  }

  late final _setStipplePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Int32, ffi.Int8, ffi.Int16,
              ffi.Pointer<MemHandle_t>)>>('setStipple');
  late final _setStipple = _setStipplePtr
      .asFunction<int Function(int, int, int, ffi.Pointer<MemHandle_t>)>();

  int setEchoStipple(
    int frSize,
    int distance,
    int space,
    ffi.Pointer<MemHandle_t> grpH,
  ) {
    return _setEchoStipple(
      frSize,
      distance,
      space,
      grpH,
    );
  }

  late final _setEchoStipplePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Int32, ffi.Int8, ffi.Int16,
              ffi.Pointer<MemHandle_t>)>>('setEchoStipple');
  late final _setEchoStipple = _setEchoStipplePtr
      .asFunction<int Function(int, int, int, ffi.Pointer<MemHandle_t>)>();

  /// ↓IIVO #288【刺繍アプリ】刺繍：setDecoFillStipple()引数の見直し H.Kawasaki
  /// PANEL_API embErrorCode_t setDecoFillStipple(MDC_ExpandType3_Fill_t pattern, FrameSize_t frSize, int8_t distance, int16_t fillsize, MemHandle_t *grpH);
  int setDecoFillStipple(
    bool decorativeType,
    int decorativeNo,
    int frSize,
    int distance,
    int fillsize,
    ffi.Pointer<MemHandle_t> grpH,
  ) {
    return _setDecoFillStipple(
      decorativeType,
      decorativeNo,
      frSize,
      distance,
      fillsize,
      grpH,
    );
  }

  late final _setDecoFillStipplePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Bool, ffi.Uint16, ffi.Int32, ffi.Int8,
              ffi.Int16, ffi.Pointer<MemHandle_t>)>>('setDecoFillStipple');
  late final _setDecoFillStipple = _setDecoFillStipplePtr.asFunction<
      int Function(bool, int, int, int, int, ffi.Pointer<MemHandle_t>)>();

  /// ↑IIVO #288【刺繍アプリ】刺繍：setDecoFillStipple()引数の見直し H.Kawasaki
  int cancelStipple() {
    return _cancelStipple();
  }

  late final _cancelStipplePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('cancelStipple');
  late final _cancelStipple = _cancelStipplePtr.asFunction<int Function()>();

  int failureCancelStipple() {
    return _failureCancelStipple();
  }

  late final _failureCancelStipplePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('failureCancelStipple');
  late final _failureCancelStipple = _failureCancelStipplePtr.asFunction<int Function()>();

  int completeStipple() {
    return _completeStipple();
  }

  late final _completeStipplePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('completeStipple');
  late final _completeStipple =
      _completeStipplePtr.asFunction<int Function()>();


  int deleteBorderMarkForStippleError() {
    return _deleteBorderMarkForStippleError();
  }

  late final _deleteBorderMarkForStippleErrorPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('deleteBorderMarkForStippleError');
  late final _deleteBorderMarkForStippleError =
  _deleteBorderMarkForStippleErrorPtr.asFunction<int Function()>();



  /// Multi select
  void enterMultiSelectMode() {
    return _enterMultiSelectMode();
  }

  late final _enterMultiSelectModePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('enterMultiSelectMode');
  late final _enterMultiSelectMode =
      _enterMultiSelectModePtr.asFunction<void Function()>();

  void exitMultiSelectMode() {
    return _exitMultiSelectMode();
  }

  late final _exitMultiSelectModePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('exitMultiSelectMode');
  late final _exitMultiSelectMode =
      _exitMultiSelectModePtr.asFunction<void Function()>();

  int selectMultiEmb(
    MemHandle_t srcGroup,
    int selectCondition,
  ) {
    return _selectMultiEmb(
      srcGroup,
      selectCondition,
    );
  }

  late final _selectMultiEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(MemHandle_t, ffi.Int32)>>(
          'selectMultiEmb');
  late final _selectMultiEmb =
      _selectMultiEmbPtr.asFunction<int Function(MemHandle_t, int)>();

  int groupingMultiplePatterns(
    ffi.Pointer<ffi.Pointer<EmbBorder_t>> borderH,
  ) {
    return _groupingMultiplePatterns(
      borderH,
    );
  }

  late final _groupingMultiplePatternsPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<ffi.Pointer<EmbBorder_t>>)>>(
      'groupingMultiplePatterns');
  late final _groupingMultiplePatterns = _groupingMultiplePatternsPtr
      .asFunction<int Function(ffi.Pointer<ffi.Pointer<EmbBorder_t>>)>();

  int ungroupingGroupedPatterns(
    ffi.Pointer<EmbBorder_t> borderH,
  ) {
    return _ungroupingGroupedPatterns(
      borderH,
    );
  }

  late final _ungroupingGroupedPatternsPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<EmbBorder_t>)>>(
          'ungroupingGroupedPatterns');
  late final _ungroupingGroupedPatterns = _ungroupingGroupedPatternsPtr
      .asFunction<int Function(ffi.Pointer<EmbBorder_t>)>();

  /// Alignment
  int alignEmb(
    int alighnment,
  ) {
    return _alignEmb(
      alighnment,
    );
  }

  late final _alignEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>('alignEmb');
  late final _alignEmb = _alignEmbPtr.asFunction<int Function(int)>();

  /// Outline
  int makeOutlineFile(
    int distance,
    bool inside,
    bool back,
    ffi.Pointer<ffi.Char> outFile,
  ) {
    return _makeOutlineFile(
      distance,
      inside,
      back,
      outFile,
    );
  }

  late final _makeOutlineFilePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Int8, ffi.Bool, ffi.Bool,
              ffi.Pointer<ffi.Char>)>>('makeOutlineFile');
  late final _makeOutlineFile = _makeOutlineFilePtr
      .asFunction<int Function(int, bool, bool, ffi.Pointer<ffi.Char>)>();

  int makeOutline(
    int distance,
    bool inside,
    bool back,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _makeOutline(
      distance,
      inside,
      back,
      embImageInfo,
    );
  }

  late final _makeOutlinePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Int8, ffi.Bool, ffi.Bool,
              ffi.Pointer<embImageInfo_t>)>>('makeOutline');
  late final _makeOutline = _makeOutlinePtr
      .asFunction<int Function(int, bool, bool, ffi.Pointer<embImageInfo_t>)>();

  int saveOutline(
    ffi.Pointer<ffi.Char> filePath,
  ) {
    return _saveOutline(
      filePath,
    );
  }

  late final _saveOutlinePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Char>)>>(
          'saveOutline');
  late final _saveOutline =
      _saveOutlinePtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int finishOutline() {
    return _finishOutline();
  }

  late final _finishOutlinePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('finishOutline');
  late final _finishOutline = _finishOutlinePtr.asFunction<int Function()>();

  /// Order
  int changeOrderNext() {
    return _changeOrderNext();
  }

  late final _changeOrderNextPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('changeOrderNext');
  late final _changeOrderNext =
      _changeOrderNextPtr.asFunction<int Function()>();

  int changeOrderPrevious() {
    return _changeOrderPrevious();
  }

  late final _changeOrderPreviousPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('changeOrderPrevious');
  late final _changeOrderPrevious =
      _changeOrderPreviousPtr.asFunction<int Function()>();

  int changeOrderLast() {
    return _changeOrderLast();
  }

  late final _changeOrderLastPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('changeOrderLast');
  late final _changeOrderLast =
      _changeOrderLastPtr.asFunction<int Function()>();

  int changeOrderFirst() {
    return _changeOrderFirst();
  }

  late final _changeOrderFirstPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('changeOrderFirst');
  late final _changeOrderFirst =
      _changeOrderFirstPtr.asFunction<int Function()>();

  /// Color Shufflue
  int getMaxNumColorShuffle(
    int brandCode,
    ffi.Pointer<ffi.Int16> colors,
  ) {
    return _getMaxNumColorShuffle(
      brandCode,
      colors,
    );
  }

  late final _getMaxNumColorShufflePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint8, ffi.Pointer<ffi.Int16>)>>('getMaxNumColorShuffle');
  late final _getMaxNumColorShuffle = _getMaxNumColorShufflePtr
      .asFunction<int Function(int, ffi.Pointer<ffi.Int16>)>();

  int shuffleVivid(
    int shuffuleColorNum,
    ffi.Pointer<ffi.Int32> thumbnail,
  ) {
    return _shuffleVivid(
      shuffuleColorNum,
      thumbnail,
    );
  }

  late final _shuffleVividPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint16, ffi.Pointer<ffi.Int32>)>>('shuffleVivid');
  late final _shuffleVivid =
      _shuffleVividPtr.asFunction<int Function(int, ffi.Pointer<ffi.Int32>)>();

  int shuffleSoft(
    int shuffuleColorNum,
    ffi.Pointer<ffi.Int32> thumbnail,
  ) {
    return _shuffleSoft(
      shuffuleColorNum,
      thumbnail,
    );
  }

  late final _shuffleSoftPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint16, ffi.Pointer<ffi.Int32>)>>('shuffleSoft');
  late final _shuffleSoft =
      _shuffleSoftPtr.asFunction<int Function(int, ffi.Pointer<ffi.Int32>)>();

  int shuffleRandom(
    int shuffuleColorNum,
    ffi.Pointer<shuffleBaseColor_t> shuffle,
    ffi.Pointer<ffi.Int32> thumbnail,
  ) {
    return _shuffleRandom(
      shuffuleColorNum,
      shuffle,
      thumbnail,
    );
  }

  late final _shuffleRandomPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint16, ffi.Pointer<shuffleBaseColor_t>,
              ffi.Pointer<ffi.Int32>)>>('shuffleRandom');
  late final _shuffleRandom = _shuffleRandomPtr.asFunction<
      int Function(
          int, ffi.Pointer<shuffleBaseColor_t>, ffi.Pointer<ffi.Int32>)>();

  int shuffleGradation(
    int shuffuleColorNum,
    ffi.Pointer<shuffleBaseColor_t> shuffle,
    ffi.Pointer<ffi.Int32> thumbnail,
  ) {
    return _shuffleGradation(
      shuffuleColorNum,
      shuffle,
      thumbnail,
    );
  }

  late final _shuffleGradationPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint16, ffi.Pointer<shuffleBaseColor_t>,
              ffi.Pointer<ffi.Int32>)>>('shuffleGradation');
  late final _shuffleGradation = _shuffleGradationPtr.asFunction<
      int Function(
          int, ffi.Pointer<shuffleBaseColor_t>, ffi.Pointer<ffi.Int32>)>();

  int addShufflePattern(
    ffi.Pointer<ffi.Int32> thumbnail,
  ) {
    return _addShufflePattern(
      thumbnail,
    );
  }

  late final _addShufflePatternPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int32>)>>(
          'addShufflePattern');
  late final _addShufflePattern =
      _addShufflePatternPtr.asFunction<int Function(ffi.Pointer<ffi.Int32>)>();

  int getShuffledImage(
    int thumbNum,
    int type,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getShuffledImage(
      thumbNum,
      type,
      embImageInfo,
    );
  }

  late final _getShuffledImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Uint16, ffi.Uint16,
              ffi.Pointer<embImageInfo_t>)>>('getShuffledImage');
  late final _getShuffledImage = _getShuffledImagePtr
      .asFunction<int Function(int, int, ffi.Pointer<embImageInfo_t>)>();

  int setShuffledNewColor(
    int thumbNum,
  ) {
    return _setShuffledNewColor(
      thumbNum,
    );
  }

  late final _setShuffledNewColorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Uint16)>>(
          'setShuffledNewColor');
  late final _setShuffledNewColor =
      _setShuffledNewColorPtr.asFunction<int Function(int)>();

  int checkColorShuffle() {
    return _checkColorShuffle();
  }

  late final _checkColorShufflePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('checkColorShuffle');
  late final _checkColorShuffle =
      _checkColorShufflePtr.asFunction<int Function()>();

  /// Sewing
  int addBasting(
    ffi.Pointer<MemHandle_t> groupH,
  ) {
    return _addBasting(
      groupH,
    );
  }

  late final _addBastingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<MemHandle_t>)>>(
          'addBasting');
  late final _addBasting =
      _addBastingPtr.asFunction<int Function(ffi.Pointer<MemHandle_t>)>();

  int deleteBasting() {
    return _deleteBasting();
  }

  late final _deleteBastingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('deleteBasting');
  late final _deleteBasting = _deleteBastingPtr.asFunction<int Function()>();

  int getNeedlePosition(
    int stitch,
    ffi.Pointer<SSPoint_t> position,
  ) {
    return _getNeedlePosition(
      stitch,
      position,
    );
  }

  late final _getNeedlePositionPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Int32, ffi.Pointer<SSPoint_t>)>>('getNeedlePosition');
  late final _getNeedlePosition = _getNeedlePositionPtr
      .asFunction<int Function(int, ffi.Pointer<SSPoint_t>)>();

  int getNeedleStartPosition(
    int position,
    ffi.Pointer<SSPoint_t> needlePos,
  ) {
    return _getNeedleStartPosition(
      position,
      needlePos,
    );
  }

  late final _getNeedleStartPositionPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Int32, ffi.Pointer<SSPoint_t>)>>('getNeedleStartPosition');
  late final _getNeedleStartPosition = _getNeedleStartPositionPtr
      .asFunction<int Function(int, ffi.Pointer<SSPoint_t>)>();

  int getCurrentNeedlePosition(
    ffi.Pointer<SSPoint_t> position,
  ) {
    return _getCurrentNeedlePosition(
      position,
    );
  }

  late final _getCurrentNeedlePositionPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<SSPoint_t>)>>(
          'getCurrentNeedlePosition');
  late final _getCurrentNeedlePosition = _getCurrentNeedlePositionPtr
      .asFunction<int Function(ffi.Pointer<SSPoint_t>)>();

  int getEmbSewInfo(
    ffi.Pointer<embSewingInfo_t> sewingInfo,
  ) {
    return _getEmbSewInfo(
      sewingInfo,
    );
  }

  late final _getEmbSewInfoPtr = _lookup<
          ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<embSewingInfo_t>)>>(
      'getEmbSewInfo');
  late final _getEmbSewInfo = _getEmbSewInfoPtr
      .asFunction<int Function(ffi.Pointer<embSewingInfo_t>)>();

  int prepareSewing() {
    return _prepareSewing();
  }

  late final _prepareSewingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('prepareSewing');
  late final _prepareSewing = _prepareSewingPtr.asFunction<int Function()>();

  /// Quilt
  int getQuiltThumbnailImage(
    int embQuiltIndex,
    int quiltCategory,
    ffi.Pointer<embImageInfo_t> embImageInfo,
    ffi.Pointer<ffi.Pointer<threadInfo_t>> threadInfo,
    ffi.Pointer<ffi.Int32> threadInfoNum,
  ) {
    return _getQuiltThumbnailImage(
      embQuiltIndex,
      quiltCategory,
      embImageInfo,
      threadInfo,
      threadInfoNum,
    );
  }

  late final _getQuiltThumbnailImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Int32,
              ffi.Int32,
              ffi.Pointer<embImageInfo_t>,
              ffi.Pointer<ffi.Pointer<threadInfo_t>>,
              ffi.Pointer<ffi.Int32>)>>('getQuiltThumbnailImage');
  late final _getQuiltThumbnailImage = _getQuiltThumbnailImagePtr.asFunction<
      int Function(int, int, ffi.Pointer<embImageInfo_t>,
          ffi.Pointer<ffi.Pointer<threadInfo_t>>, ffi.Pointer<ffi.Int32>)>();

  int getQuiltImage(
    int embQuiltIndex,
    int quiltCategory,
    int color,
    int flip,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getQuiltImage(
      embQuiltIndex,
      quiltCategory,
      color,
      flip,
      embImageInfo,
    );
  }

  late final _getQuiltImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Int32, ffi.Int32, ffi.Int32, ffi.Int32,
              ffi.Pointer<embImageInfo_t>)>>('getQuiltImage');
  late final _getQuiltImage = _getQuiltImagePtr.asFunction<
      int Function(int, int, int, int, ffi.Pointer<embImageInfo_t>)>();

  int getQuiltPreviewImage(
    bool dividingLine,
    ffi.Pointer<ffi.Int32> division,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getQuiltPreviewImage(
      dividingLine,
      division,
      embImageInfo,
    );
  }

  late final _getQuiltPreviewImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Bool, ffi.Pointer<ffi.Int32>,
              ffi.Pointer<embImageInfo_t>)>>('getQuiltPreviewImage');
  late final _getQuiltPreviewImage = _getQuiltPreviewImagePtr.asFunction<
      int Function(
          bool, ffi.Pointer<ffi.Int32>, ffi.Pointer<embImageInfo_t>)>();

  int getQuiltPreviewExtensionImage(
    bool frameBorder,
    ffi.Pointer<ffi.Int32> division,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getQuiltPreviewExtensionImage(
      frameBorder,
      division,
      embImageInfo,
    );
  }

  late final _getQuiltPreviewExtensionImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Bool, ffi.Pointer<ffi.Int32>,
              ffi.Pointer<embImageInfo_t>)>>('getQuiltPreviewExtensionImage');
  late final _getQuiltPreviewExtensionImage =
      _getQuiltPreviewExtensionImagePtr.asFunction<
          int Function(
              bool, ffi.Pointer<ffi.Int32>, ffi.Pointer<embImageInfo_t>)>();

  int getQuiltPreviewHexagonImage(
    bool frameBorder,
    ffi.Pointer<ffi.Int32> division,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getQuiltPreviewHexagonImage(
      frameBorder,
      division,
      embImageInfo,
    );
  }

  late final _getQuiltPreviewHexagonImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Bool, ffi.Pointer<ffi.Int32>,
              ffi.Pointer<embImageInfo_t>)>>('getQuiltPreviewHexagonImage');
  late final _getQuiltPreviewHexagonImage =
      _getQuiltPreviewHexagonImagePtr.asFunction<
          int Function(
              bool, ffi.Pointer<ffi.Int32>, ffi.Pointer<embImageInfo_t>)>();

  int getQuiltPreviewEdgeToEdgeImage(
    bool frameBorder,
    ffi.Pointer<ffi.Int32> division,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getQuiltPreviewEdgeToEdgeImage(
      frameBorder,
      division,
      embImageInfo,
    );
  }

  late final _getQuiltPreviewEdgeToEdgeImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Bool, ffi.Pointer<ffi.Int32>,
              ffi.Pointer<embImageInfo_t>)>>('getQuiltPreviewEdgeToEdgeImage');
  late final _getQuiltPreviewEdgeToEdgeImage =
      _getQuiltPreviewEdgeToEdgeImagePtr.asFunction<
          int Function(
              bool, ffi.Pointer<ffi.Int32>, ffi.Pointer<embImageInfo_t>)>();

  int setQuiltWidth(
    int wValue,
    ffi.Pointer<quiltSashesLimit_t> sizeLimit,
  ) {
    return _setQuiltWidth(
      wValue,
      sizeLimit,
    );
  }

  late final _setQuiltWidthPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Int32, ffi.Pointer<quiltSashesLimit_t>)>>('setQuiltWidth');
  late final _setQuiltWidth = _setQuiltWidthPtr
      .asFunction<int Function(int, ffi.Pointer<quiltSashesLimit_t>)>();

  int setQuiltHeight(
    int hValue,
    ffi.Pointer<quiltSashesLimit_t> sizeLimit,
  ) {
    return _setQuiltHeight(
      hValue,
      sizeLimit,
    );
  }

  late final _setQuiltHeightPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Int32, ffi.Pointer<quiltSashesLimit_t>)>>('setQuiltHeight');
  late final _setQuiltHeight = _setQuiltHeightPtr
      .asFunction<int Function(int, ffi.Pointer<quiltSashesLimit_t>)>();

  int setQuiltBand(
    int bValue,
    ffi.Pointer<quiltSashesLimit_t> sizeLimit,
  ) {
    return _setQuiltBand(
      bValue,
      sizeLimit,
    );
  }

  late final _setQuiltBandPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Int32, ffi.Pointer<quiltSashesLimit_t>)>>('setQuiltBand');
  late final _setQuiltBand = _setQuiltBandPtr
      .asFunction<int Function(int, ffi.Pointer<quiltSashesLimit_t>)>();

  int getQuiltSizeLimit(
    int quiltType,
    int frameType,
    ffi.Pointer<quiltSashesLimit_t> sizeLimit,
  ) {
    return _getQuiltSizeLimit(
      quiltType,
      frameType,
      sizeLimit,
    );
  }

  late final _getQuiltSizeLimitPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Int32, ffi.Int32,
              ffi.Pointer<quiltSashesLimit_t>)>>('getQuiltSizeLimit');
  late final _getQuiltSizeLimit = _getQuiltSizeLimitPtr
      .asFunction<int Function(int, int, ffi.Pointer<quiltSashesLimit_t>)>();

  int getSewingInfoEdgeToEdge(
    ffi.Pointer<edgeToEdgeInfo_t> edgToEdgInfo,
  ) {
    return _getSewingInfoEdgeToEdge(
      edgToEdgInfo,
    );
  }

  late final _getSewingInfoEdgeToEdgePtr = _lookup<
          ffi
          .NativeFunction<ffi.Int32 Function(ffi.Pointer<edgeToEdgeInfo_t>)>>(
      'getSewingInfoEdgeToEdge');
  late final _getSewingInfoEdgeToEdge = _getSewingInfoEdgeToEdgePtr
      .asFunction<int Function(ffi.Pointer<edgeToEdgeInfo_t>)>();

  int changeQuiltColorByPalette(
    int coloNum,
    int brand,
    int index,
  ) {
    return _changeQuiltColorByPalette(
      coloNum,
      brand,
      index,
    );
  }

  late final _changeQuiltColorByPalettePtr = _lookup<
          ffi
          .NativeFunction<ffi.Int32 Function(ffi.Int32, ffi.Uint8, ffi.Int32)>>(
      'changeQuiltColorByPalette');
  late final _changeQuiltColorByPalette =
      _changeQuiltColorByPalettePtr.asFunction<int Function(int, int, int)>();

  int changeQuiltColor(
    int coloNum,
    int r,
    int g,
    int b,
  ) {
    return _changeQuiltColor(
      coloNum,
      r,
      g,
      b,
    );
  }

  late final _changeQuiltColorPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Int32, ffi.Uint8, ffi.Uint8, ffi.Uint8)>>('changeQuiltColor');
  late final _changeQuiltColor =
      _changeQuiltColorPtr.asFunction<int Function(int, int, int, int)>();

  int getQuiltColorInfo(
    int colorNum,
    ffi.Pointer<EmbColorGr_t> colorInfo,
  ) {
    return _getQuiltColorInfo(
      colorNum,
      colorInfo,
    );
  }

  late final _getQuiltColorInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Int32, ffi.Pointer<EmbColorGr_t>)>>('getQuiltColorInfo');
  late final _getQuiltColorInfo = _getQuiltColorInfoPtr
      .asFunction<int Function(int, ffi.Pointer<EmbColorGr_t>)>();

  int saveQuiltData(
    int type,
    int sewType,
    ffi.Pointer<ffi.Char> fileName,
  ) {
    return _saveQuiltData(
      type,
      sewType,
      fileName,
    );
  }

  late final _saveQuiltDataPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Int32, ffi.Int32, ffi.Pointer<ffi.Char>)>>('saveQuiltData');
  late final _saveQuiltData = _saveQuiltDataPtr
      .asFunction<int Function(int, int, ffi.Pointer<ffi.Char>)>();

  int getFlipPart(
    int embQuiltIndex,
    int flip,
    bool rect,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getFlipPart(
      embQuiltIndex,
      flip,
      rect,
      embImageInfo,
    );
  }

  late final _getFlipPartPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Int32, ffi.Int32, ffi.Bool,
              ffi.Pointer<embImageInfo_t>)>>('getFlipPart');
  late final _getFlipPart = _getFlipPartPtr
      .asFunction<int Function(int, int, bool, ffi.Pointer<embImageInfo_t>)>();

  /// Pocket
  int getThumbnailFileSavedFile(
    ffi.Pointer<ffi.Char> file,
    int size,
    ffi.Pointer<savedFileInfo_t> param,
    ffi.Pointer<ffi.Char> outFile,
  ) {
    return _getThumbnailFileSavedFile(
      file,
      size,
      param,
      outFile,
    );
  }

  late final _getThumbnailFileSavedFilePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Char>,
              ffi.Uint8,
              ffi.Pointer<savedFileInfo_t>,
              ffi.Pointer<ffi.Char>)>>('getThumbnailFileSavedFile');
  late final _getThumbnailFileSavedFile =
      _getThumbnailFileSavedFilePtr.asFunction<
          int Function(ffi.Pointer<ffi.Char>, int, ffi.Pointer<savedFileInfo_t>,
              ffi.Pointer<ffi.Char>)>();

  int getThumbnailSavedFile(
    ffi.Pointer<ffi.Char> file,
    int size,
    bool transparent,
    ffi.Pointer<savedFileInfo_t> param,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getThumbnailSavedFile(
      file,
      size,
      transparent,
      param,
      embImageInfo,
    );
  }

  late final _getThumbnailSavedFilePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Char>,
              ffi.Uint8,
              ffi.Bool,
              ffi.Pointer<savedFileInfo_t>,
              ffi.Pointer<embImageInfo_t>)>>('getThumbnailSavedFile');
  late final _getThumbnailSavedFile = _getThumbnailSavedFilePtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, int, bool,
          ffi.Pointer<savedFileInfo_t>, ffi.Pointer<embImageInfo_t>)>();

  int getThumbnailPartsFileSavedFile(
    ffi.Pointer<ffi.Char> file,
    int size,
    int index,
    ffi.Pointer<savedFileInfo_t> param,
    ffi.Pointer<ffi.Char> outFile,
  ) {
    return _getThumbnailPartsFileSavedFile(
      file,
      size,
      index,
      param,
      outFile,
    );
  }

  late final _getThumbnailPartsFileSavedFilePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Char>,
              ffi.Uint8,
              ffi.Int32,
              ffi.Pointer<savedFileInfo_t>,
              ffi.Pointer<ffi.Char>)>>('getThumbnailPartsFileSavedFile');
  late final _getThumbnailPartsFileSavedFile =
      _getThumbnailPartsFileSavedFilePtr.asFunction<
          int Function(ffi.Pointer<ffi.Char>, int, int,
              ffi.Pointer<savedFileInfo_t>, ffi.Pointer<ffi.Char>)>();

  int getThumbnailPartsSavedFile(
    ffi.Pointer<ffi.Char> file,
    int size,
    int index,
    bool transparent,
    ffi.Pointer<savedFileInfo_t> param,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getThumbnailPartsSavedFile(
      file,
      size,
      index,
      transparent,
      param,
      embImageInfo,
    );
  }

  late final _getThumbnailPartsSavedFilePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Char>,
              ffi.Uint8,
              ffi.Int32,
              ffi.Bool,
              ffi.Pointer<savedFileInfo_t>,
              ffi.Pointer<embImageInfo_t>)>>('getThumbnailPartsSavedFile');
  late final _getThumbnailPartsSavedFile =
      _getThumbnailPartsSavedFilePtr.asFunction<
          int Function(ffi.Pointer<ffi.Char>, int, int, bool,
              ffi.Pointer<savedFileInfo_t>, ffi.Pointer<embImageInfo_t>)>();

  int getPartsNumSavedFile(
    ffi.Pointer<ffi.Char> file,
    ffi.Pointer<ffi.Int32> parts,
  ) {
    return _getPartsNumSavedFile(
      file,
      parts,
    );
  }

  late final _getPartsNumSavedFilePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Char>,
              ffi.Pointer<ffi.Int32>)>>('getPartsNumSavedFile');
  late final _getPartsNumSavedFile = _getPartsNumSavedFilePtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Int32>)>();

  int selectEditFile(
    ffi.Pointer<ffi.Char> file,
    int dataSource,
    int restriction,
    int partsNo,
    ffi.Pointer<MemHandle_t> grpHArray,
    ffi.Pointer<ffi.Int32> handleNum,
  ) {
    return _selectEditFile(
      file,
      dataSource,
      restriction,
      partsNo,
      grpHArray,
      handleNum,
    );
  }

  late final _selectEditFilePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Char>,
              ffi.Uint8,
              ffi.Uint8,
              ffi.Int32,
              ffi.Pointer<MemHandle_t>,
              ffi.Pointer<ffi.Int32>)>>('selectEditFile');
  late final _selectEditFile = _selectEditFilePtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, int, int, int,
          ffi.Pointer<MemHandle_t>, ffi.Pointer<ffi.Int32>)>();

  // NSS No.576 メモリリーク対応
  void selectEditFileEmbHFree(
    ffi.Pointer<MemHandle_t> grpHArray,
  ) {
    return _selectEditFileEmbHFree(
      grpHArray,
    );
  }

  late final _selectEditFileEmbHFreePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<MemHandle_t>)>>(
          'selectEditFileEmbHFree');
  late final _selectEditFileEmbHFree =
      _selectEditFileEmbHFreePtr.asFunction<void Function(ffi.Pointer<MemHandle_t>)>();

  /// グループハンドルの取得
  int getGroupHandleAll(
    ffi.Pointer<MemHandle_t> grpHArray,
    ffi.Pointer<ffi.Int32> handleNum,
  ) {
    return _getGroupHandleAll(
      grpHArray,
      handleNum,
    );
  }

  late final _getGroupHandleAllPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<MemHandle_t>,
              ffi.Pointer<ffi.Int32>)>>('getGroupHandleAll');
  late final _getGroupHandleAll = _getGroupHandleAllPtr.asFunction<
      int Function(ffi.Pointer<MemHandle_t>, ffi.Pointer<ffi.Int32>)>();

  // NSS No.576 メモリリーク修正対応 //
  /// メモリハンドルの開放
  void freeMemHandle(
      ffi.Pointer<MemHandle_t> memHandelList,
      ) {
    return _freeMemHandle(
      memHandelList,
    );
  }

  late final _freeMemHandlePtr =
  _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<MemHandle_t>)>>(
      'freeMemHandle');
  late final _freeMemHandle =
  _freeMemHandlePtr.asFunction<void Function(ffi.Pointer<MemHandle_t>)>();


  int getCurrentGroupHandle(
    ffi.Pointer<MemHandle_t> currentG,
  ) {
    return _getCurrentGroupHandle(
      currentG,
    );
  }

  late final _getCurrentGroupHandlePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<MemHandle_t>)>>(
          'getCurrentGroupHandle');
  late final _getCurrentGroupHandle = _getCurrentGroupHandlePtr
      .asFunction<int Function(ffi.Pointer<MemHandle_t>)>();

  /// イメージの取得、廃棄
  int getSelectedGroupImageFile(
    MemHandle_t groupH,
    int centerType,
    int scale,
    bool imageType,
    int backColor,
    ffi.Pointer<ffi.Int32> pageNum,
    ffi.Pointer<ffi.Char> outFile,
  ) {
    return _getSelectedGroupImageFile(
      groupH,
      centerType,
      scale,
      imageType,
      backColor,
      pageNum,
      outFile,
    );
  }

  late final _getSelectedGroupImageFilePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              MemHandle_t,
              ffi.Int32,
              ffi.Int32,
              ffi.Bool,
              ffi.Uint32,
              ffi.Pointer<ffi.Int32>,
              ffi.Pointer<ffi.Char>)>>('getSelectedGroupImageFile');
  late final _getSelectedGroupImageFile =
      _getSelectedGroupImageFilePtr.asFunction<
          int Function(MemHandle_t, int, int, bool, int, ffi.Pointer<ffi.Int32>,
              ffi.Pointer<ffi.Char>)>();

  int getSelectedGroupARGBImageFile(
    MemHandle_t groupH,
    int centerType,
    int scale,
    bool imageType,
    ffi.Pointer<ffi.Uint32> size_x,
    ffi.Pointer<ffi.Uint32> size_y,
    ffi.Pointer<ffi.Int32> pageNum,
    ffi.Pointer<ffi.Char> outFile,
  ) {
    return _getSelectedGroupARGBImageFile(
      groupH,
      centerType,
      scale,
      imageType,
      size_x,
      size_y,
      pageNum,
      outFile,
    );
  }

  late final _getSelectedGroupARGBImageFilePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              MemHandle_t,
              ffi.Int32,
              ffi.Int32,
              ffi.Bool,
              ffi.Pointer<ffi.Uint32>,
              ffi.Pointer<ffi.Uint32>,
              ffi.Pointer<ffi.Int32>,
              ffi.Pointer<ffi.Char>)>>('getSelectedGroupARGBImageFile');
  late final _getSelectedGroupARGBImageFile =
      _getSelectedGroupARGBImageFilePtr.asFunction<
          int Function(
              MemHandle_t,
              int,
              int,
              bool,
              ffi.Pointer<ffi.Uint32>,
              ffi.Pointer<ffi.Uint32>,
              ffi.Pointer<ffi.Int32>,
              ffi.Pointer<ffi.Char>)>();

  int getSelectedGroupRGBImage(
    MemHandle_t groupH,
    int centerType,
    int scale,
    bool imageType,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getSelectedGroupRGBImage(
      groupH,
      centerType,
      scale,
      imageType,
      embImageInfo,
    );
  }

  late final _getSelectedGroupRGBImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(MemHandle_t, ffi.Int32, ffi.Int32, ffi.Bool,
              ffi.Pointer<embImageInfo_t>)>>('getSelectedGroupRGBImage');
  late final _getSelectedGroupRGBImage =
      _getSelectedGroupRGBImagePtr.asFunction<
          int Function(
              MemHandle_t, int, int, bool, ffi.Pointer<embImageInfo_t>)>();

  int getSelectedGroupARGBImage(
    MemHandle_t groupH,
    int centerType,
    int scale,
    bool imageType,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getSelectedGroupARGBImage(
      groupH,
      centerType,
      scale,
      imageType,
      embImageInfo,
    );
  }

  late final _getSelectedGroupARGBImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(MemHandle_t, ffi.Int32, ffi.Int32, ffi.Bool,
              ffi.Pointer<embImageInfo_t>)>>('getSelectedGroupARGBImage');
  late final _getSelectedGroupARGBImage =
      _getSelectedGroupARGBImagePtr.asFunction<
          int Function(
              MemHandle_t, int, int, bool, ffi.Pointer<embImageInfo_t>)>();

  int delSelectedGroupImage(
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _delSelectedGroupImage(
      embImageInfo,
    );
  }

  late final _delSelectedGroupImagePtr = _lookup<
          ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<embImageInfo_t>)>>(
      'delSelectedGroupImage');
  late final _delSelectedGroupImage = _delSelectedGroupImagePtr
      .asFunction<int Function(ffi.Pointer<embImageInfo_t>)>();

  int getRealImage(
    bool emb,
    bool backDisplay,
    bool displayPosition,
    int scale,
    ffi.Pointer<realImageInfo_t> realImageInfo,
  ) {
    return _getRealImage(
      emb,
      backDisplay,
      displayPosition,
      scale,
      realImageInfo,
    );
  }

  late final _getRealImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Bool, ffi.Bool, ffi.Bool, ffi.Int32,
              ffi.Pointer<realImageInfo_t>)>>('getRealImage');
  late final _getRealImage = _getRealImagePtr.asFunction<
      int Function(bool, bool, bool, int, ffi.Pointer<realImageInfo_t>)>();

  int getInfoImage(
    bool backDisplay,
    int backColor,
    ffi.Pointer<embImg_t> infoImage,
  ) {
    return _getInfoImage(
      backDisplay,
      backColor,
      infoImage,
    );
  }

  late final _getInfoImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Bool, ffi.Uint32, ffi.Pointer<embImg_t>)>>('getInfoImage');
  late final _getInfoImage = _getInfoImagePtr
      .asFunction<int Function(bool, int, ffi.Pointer<embImg_t>)>();

  int makeBmpFromPCE(
    ffi.Pointer<ffi.Char> pceFile,
    ffi.Pointer<ffi.Char> outFile,
  ) {
    return _makeBmpFromPCE(
      pceFile,
      outFile,
    );
  }

  late final _makeBmpFromPCEPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>>('makeBmpFromPCE');
  late final _makeBmpFromPCE = _makeBmpFromPCEPtr
      .asFunction<int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>)>();

  void getBmpEmbImageInfo(
    ffi.Pointer<ffi.Void> bmpData,
    ffi.Pointer<embImageInfo_t> embImageInfo,
  ) {
    return _getBmpEmbImageInfo(
      bmpData,
      embImageInfo,
    );
  }

  late final _getBmpEmbImageInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Void>,
              ffi.Pointer<embImageInfo_t>)>>('getBmpEmbImageInfo');
  late final _getBmpEmbImageInfo = _getBmpEmbImageInfoPtr.asFunction<
      void Function(ffi.Pointer<ffi.Void>, ffi.Pointer<embImageInfo_t>)>();

  int delInfoImage(
    embImg_t infoImage,
  ) {
    return _delInfoImage(
      infoImage,
    );
  }

  late final _delInfoImagePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(embImg_t)>>('delInfoImage');
  late final _delInfoImage =
      _delInfoImagePtr.asFunction<int Function(embImg_t)>();

  int delRealImage(
    realImageInfo_t realImageInfo,
  ) {
    return _delRealImage(
      realImageInfo,
    );
  }

  late final _delRealImagePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(realImageInfo_t)>>(
          'delRealImage');
  late final _delRealImage =
      _delRealImagePtr.asFunction<int Function(realImageInfo_t)>();

  int notPatternAreaSelectWork() {
    return _notPatternAreaSelectWork();
  }

  late final _notPatternAreaSelectWorkPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'notPatternAreaSelectWork');
  late final _notPatternAreaSelectWork =
      _notPatternAreaSelectWorkPtr.asFunction<int Function()>();

  /// 刺繍全領域情報
  int getEmbArea(
    ffi.Pointer<RectanArea_t> area,
  ) {
    return _getEmbArea(
      area,
    );
  }

  late final _getEmbAreaPtr = _lookup<
          ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<RectanArea_t>)>>(
      'getEmbArea');
  late final _getEmbArea =
      _getEmbAreaPtr.asFunction<int Function(ffi.Pointer<RectanArea_t>)>();

  /// 短冊の選択
  int selectPartsInGroupColorChange(
    MemHandle_t gHandle,
    int index,
  ) {
    return _selectPartsInGroupColorChange(
      gHandle,
      index,
    );
  }

  late final _selectPartsInGroupColorChangePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(MemHandle_t, ffi.Int16)>>(
          'selectPartsInGroupColorChange');
  late final _selectPartsInGroupColorChange = _selectPartsInGroupColorChangePtr
      .asFunction<int Function(MemHandle_t, int)>();

  /// ----------- ↓追加IF↓ -----------
  int isEmbCustomDesign(
    ffi.Pointer<ffi.Bool> pResult,
  ) {
    return _isEmbCustomDesign(
      pResult,
    );
  }

  late final _isEmbCustomDesignPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'isEmbCustomDesign');
  late final _isEmbCustomDesign =
      _isEmbCustomDesignPtr.asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getEmbMarkPatCntAutoKind() {
    return _getEmbMarkPatCntAutoKind();
  }

  late final _getEmbMarkPatCntAutoKindPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'getEmbMarkPatCntAutoKind');
  late final _getEmbMarkPatCntAutoKind =
      _getEmbMarkPatCntAutoKindPtr.asFunction<int Function()>();

  bool getEmbMarkPatCntAutoCustom() {
    return _getEmbMarkPatCntAutoCustom();
  }

  late final _getEmbMarkPatCntAutoCustomPtr =
      _lookup<ffi.NativeFunction<ffi.Bool Function()>>(
          'getEmbMarkPatCntAutoCustom');
  late final _getEmbMarkPatCntAutoCustom =
      _getEmbMarkPatCntAutoCustomPtr.asFunction<bool Function()>();

  int getEmbDensity(
    ffi.Pointer<ffi.Uint8> pDensity,
  ) {
    return _getEmbDensity(
      pDensity,
    );
  }

  late final _getEmbDensityPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Uint8>)>>(
          'getEmbDensity');
  late final _getEmbDensity =
      _getEmbDensityPtr.asFunction<int Function(ffi.Pointer<ffi.Uint8>)>();

  int resetEmbBorderMark() {
    return _resetEmbBorderMark();
  }

  late final _resetEmbBorderMarkPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('resetEmbBorderMark');
  late final _resetEmbBorderMark =
      _resetEmbBorderMarkPtr.asFunction<int Function()>();

  int setEmbBorderMark(
    int markPos,
    bool orderOnOff,
  ) {
    return _setEmbBorderMark(
      markPos,
      orderOnOff,
    );
  }

  late final _setEmbBorderMarkPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Uint8, ffi.Bool)>>(
          'setEmbBorderMark');
  late final _setEmbBorderMark =
      _setEmbBorderMarkPtr.asFunction<int Function(int, bool)>();

  int getEmbBorderMark(
    ffi.Pointer<ffi.Uint8> pMarkPos,
  ) {
    return _getEmbBorderMark(
      pMarkPos,
    );
  }

  late final _getEmbBorderMarkPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Uint8>)>>(
          'getEmbBorderMark');
  late final _getEmbBorderMark =
      _getEmbBorderMarkPtr.asFunction<int Function(ffi.Pointer<ffi.Uint8>)>();

  bool isEmbMarkPatCntMode() {
    return _isEmbMarkPatCntMode();
  }

  late final _isEmbMarkPatCntModePtr =
      _lookup<ffi.NativeFunction<ffi.Bool Function()>>('isEmbMarkPatCntMode');
  late final _isEmbMarkPatCntMode =
      _isEmbMarkPatCntModePtr.asFunction<bool Function()>();

  int getCurrentStitchNumber() {
    return _getCurrentStitchNumber();
  }

  late final _getCurrentStitchNumberPtr =
      _lookup<ffi.NativeFunction<ffi.Uint32 Function()>>(
          'getCurrentStitchNumber');
  late final _getCurrentStitchNumber =
      _getCurrentStitchNumberPtr.asFunction<int Function()>();

  int setCurrentStitchNumber(
    int NeedleCount,
  ) {
    return _setCurrentStitchNumber(
      NeedleCount,
    );
  }

  late final _setCurrentStitchNumberPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Uint32)>>(
          'setCurrentStitchNumber');
  late final _setCurrentStitchNumber =
      _setCurrentStitchNumberPtr.asFunction<int Function(int)>();

  void setEmbSewNonStopSewing(
    bool setData,
  ) {
    return _setEmbSewNonStopSewing(
      setData,
    );
  }

  late final _setEmbSewNonStopSewingPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Bool)>>(
          'setEmbSewNonStopSewing');
  late final _setEmbSewNonStopSewing =
      _setEmbSewNonStopSewingPtr.asFunction<void Function(bool)>();

  bool isEmbSewOneColorNG() {
    return _isEmbSewOneColorNG();
  }

  late final _isEmbSewOneColorNGPtr =
      _lookup<ffi.NativeFunction<ffi.Bool Function()>>('isEmbSewOneColorNG');
  late final _isEmbSewOneColorNG =
      _isEmbSewOneColorNGPtr.asFunction<bool Function()>();

  bool isEmbSewOneColorON() {
    return _isEmbSewOneColorON();
  }

  late final _isEmbSewOneColorONPtr =
      _lookup<ffi.NativeFunction<ffi.Bool Function()>>('isEmbSewOneColorON');
  late final _isEmbSewOneColorON =
      _isEmbSewOneColorONPtr.asFunction<bool Function()>();

  int setEmbSewPatternConnectReserve() {
    return _setEmbSewPatternConnectReserve();
  }

  late final _setEmbSewPatternConnectReservePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'setEmbSewPatternConnectReserve');
  late final _setEmbSewPatternConnectReserve =
      _setEmbSewPatternConnectReservePtr.asFunction<int Function()>();

  int getEmbSewPatternConnect() {
    return _getEmbSewPatternConnect();
  }

  late final _getEmbSewPatternConnectPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'getEmbSewPatternConnect');
  late final _getEmbSewPatternConnect =
      _getEmbSewPatternConnectPtr.asFunction<int Function()>();

  int setColorSort(
    bool flg,
  ) {
    return _setColorSort(
      flg,
    );
  }

  late final _setColorSortPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>('setColorSort');
  late final _setColorSort = _setColorSortPtr.asFunction<int Function(bool)>();

  bool embEditPositionNG() {
    return _embEditPositionNG();
  }

  late final _embEditPositionNGPtr =
      _lookup<ffi.NativeFunction<ffi.Bool Function()>>('embEditPositionNG');
  late final _embEditPositionNG =
      _embEditPositionNGPtr.asFunction<bool Function()>();

  int checkPatternNoMirrorNoCharacter(
    MemHandle_t patternH,
    ffi.Pointer<ffi.Bool> mirror,
  ) {
    return _checkPatternNoMirrorNoCharacter(
      patternH,
      mirror,
    );
  }

  late final _checkPatternNoMirrorNoCharacterPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(MemHandle_t,
              ffi.Pointer<ffi.Bool>)>>('checkPatternNoMirrorNoCharacter');
  late final _checkPatternNoMirrorNoCharacter =
      _checkPatternNoMirrorNoCharacterPtr
          .asFunction<int Function(MemHandle_t, ffi.Pointer<ffi.Bool>)>();

  int curGroupCharArrayBack() {
    return _curGroupCharArrayBack();
  }

  late final _curGroupCharArrayBackPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'curGroupCharArrayBack');
  late final _curGroupCharArrayBack =
      _curGroupCharArrayBackPtr.asFunction<int Function()>();

  int getRealImageLight(
    bool allPattern,
    ffi.Pointer<ffi.Pointer<embstitchPos_t>> needlePos,
    ffi.Pointer<ffi.Int32> pointNum,
  ) {
    return _getRealImageLight(
      allPattern,
      needlePos,
      pointNum,
    );
  }

  late final _getRealImageLightPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Bool, ffi.Pointer<ffi.Pointer<embstitchPos_t>>,
              ffi.Pointer<ffi.Int32>)>>('getRealImageLight');
  late final _getRealImageLight = _getRealImageLightPtr.asFunction<
      int Function(bool, ffi.Pointer<ffi.Pointer<embstitchPos_t>>,
          ffi.Pointer<ffi.Int32>)>();

  int delRealImageLight(
    ffi.Pointer<ffi.Pointer<embstitchPos_t>> needlePos,
  ) {
    return _delRealImageLight(
      needlePos,
    );
  }

  late final _delRealImageLightPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<ffi.Pointer<embstitchPos_t>>)>>('delRealImageLight');
  late final _delRealImageLight = _delRealImageLightPtr
      .asFunction<int Function(ffi.Pointer<ffi.Pointer<embstitchPos_t>>)>();

  /// 文字I/F ↑
  int exchangeErrCode(
    int errCode,
  ) {
    return _exchangeErrCode(
      errCode,
    );
  }

  late final _exchangeErrCodePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'exchangeErrCode');
  late final _exchangeErrCode =
      _exchangeErrCodePtr.asFunction<int Function(int)>();

  /// 刺しゅう機移動
  int removeEmbUnit() {
    return _removeEmbUnit();
  }

  late final _removeEmbUnitPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('removeEmbUnit');
  late final _removeEmbUnit = _removeEmbUnitPtr.asFunction<int Function()>();

  /// LEDポインタON制御
  int setLedPointerON() {
    return _setLedPointerON();
  }

  late final _setLedPointerONPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setLedPointerON');
  late final _setLedPointerON =
      _setLedPointerONPtr.asFunction<int Function()>();

  /// 刺繍縫製設定
  /// stepSize*0.2ずつ増えます。1に0.2ずつ、2にすると0．4ずつになります
  int setEmbTensionMinus(
    int stepSize,
  ) {
    return _setEmbTensionMinus(
      stepSize,
    );
  }

  late final _setEmbTensionMinusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Uint8)>>(
          'setEmbTensionMinus');
  late final _setEmbTensionMinus =
      _setEmbTensionMinusPtr.asFunction<int Function(int)>();

  int setEmbTensionPlus(
    int stepSize,
  ) {
    return _setEmbTensionPlus(
      stepSize,
    );
  }

  late final _setEmbTensionPlusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Uint8)>>(
          'setEmbTensionPlus');
  late final _setEmbTensionPlus =
      _setEmbTensionPlusPtr.asFunction<int Function(int)>();

  int setEndColorTrimOn() {
    return _setEndColorTrimOn();
  }

  late final _setEndColorTrimOnPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setEndColorTrimOn');
  late final _setEndColorTrimOn =
      _setEndColorTrimOnPtr.asFunction<int Function()>();

  int setEndColorTrimOff() {
    return _setEndColorTrimOff();
  }

  late final _setEndColorTrimOffPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setEndColorTrimOff');
  late final _setEndColorTrimOff =
      _setEndColorTrimOffPtr.asFunction<int Function()>();

  int setJumpStitchTrimOn() {
    return _setJumpStitchTrimOn();
  }

  late final _setJumpStitchTrimOnPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setJumpStitchTrimOn');
  late final _setJumpStitchTrimOn =
      _setJumpStitchTrimOnPtr.asFunction<int Function()>();

  int setJumpStitchTrimOff() {
    return _setJumpStitchTrimOff();
  }

  late final _setJumpStitchTrimOffPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setJumpStitchTrimOff');
  late final _setJumpStitchTrimOff =
      _setJumpStitchTrimOffPtr.asFunction<int Function()>();

  int setJumpStitchLengthMinus() {
    return _setJumpStitchLengthMinus();
  }

  late final _setJumpStitchLengthMinusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'setJumpStitchLengthMinus');
  late final _setJumpStitchLengthMinus =
      _setJumpStitchLengthMinusPtr.asFunction<int Function()>();

  int setJumpStitchLengthPlus() {
    return _setJumpStitchLengthPlus();
  }

  late final _setJumpStitchLengthPlusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'setJumpStitchLengthPlus');
  late final _setJumpStitchLengthPlus =
      _setJumpStitchLengthPlusPtr.asFunction<int Function()>();

  /// 刺しゅう 編集から模様選択状態への遷移
  int embEditReturnSelect() {
    return _embEditReturnSelect();
  }

  late final _embEditReturnSelectPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embEditReturnSelect');
  late final _embEditReturnSelect =
      _embEditReturnSelectPtr.asFunction<int Function()>();

  int embEditReturnSelectGray() {
    return _embEditReturnSelectGray();
  }

  late final _embEditReturnSelectGrayPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embEditReturnSelectGray');
  late final _embEditReturnSelectGray =
      _embEditReturnSelectGrayPtr.asFunction<int Function()>();

  /// 刺しゅう 編集状態への遷移
  int embGotoEdit() {
    return _embGotoEdit();
  }

  late final _embGotoEditPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embGotoEdit');
  late final _embGotoEdit = _embGotoEditPtr.asFunction<int Function()>();

  int embGotoResumeOk() {
    return _embGotoResumeOk();
  }

  late final _embGotoResumeOkPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embGotoResumeOk');
  late final _embGotoResumeOk =
      _embGotoResumeOkPtr.asFunction<int Function()>();

  int embSelectReturnEdit() {
    return _embSelectReturnEdit();
  }

  late final _embSelectReturnEditPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embSelectReturnEdit');
  late final _embSelectReturnEdit =
      _embSelectReturnEditPtr.asFunction<int Function()>();

  /// 刺しゅう 編集状態への遷移
  int embGotoSewing() {
    return _embGotoSewing();
  }

  late final _embGotoSewingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embGotoSewing');
  late final _embGotoSewing = _embGotoSewingPtr.asFunction<int Function()>();

  /// 刺しゅう 縫製画面から編集状態への遷移
  int embSewingReturnEdit() {
    return _embSewingReturnEdit();
  }

  late final _embSewingReturnEditPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embSewingReturnEdit');
  late final _embSewingReturnEdit =
      _embSewingReturnEditPtr.asFunction<int Function()>();

  /// Emb Select→MDC Paintへの遷移
  int embGotoMDCPaint() {
    return _embGotoMDCPaint();
  }

  late final _embGotoMDCPaintPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embGotoMDCPaint');
  late final _embGotoMDCPaint =
      _embGotoMDCPaintPtr.asFunction<int Function()>();

  /// 刺繍画面でRotateとMove専用画面に遷移する　つなぎ関連で必要になる
  int embSewingGotoRotate() {
    return _embSewingGotoRotate();
  }

  late final _embSewingGotoRotatePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embSewingGotoRotate');
  late final _embSewingGotoRotate =
      _embSewingGotoRotatePtr.asFunction<int Function()>();

  int embSewingGotoRotateQuilt() {
    return _embSewingGotoRotateQuilt();
  }

  late final _embSewingGotoRotateQuiltPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingGotoRotateQuilt');
  late final _embSewingGotoRotateQuilt =
      _embSewingGotoRotateQuiltPtr.asFunction<int Function()>();

  int embSewingGotoMove() {
    return _embSewingGotoMove();
  }

  late final _embSewingGotoMovePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embSewingGotoMove');
  late final _embSewingGotoMove =
      _embSewingGotoMovePtr.asFunction<int Function()>();

  int embSewingGotoMoveQuilt() {
    return _embSewingGotoMoveQuilt();
  }

  late final _embSewingGotoMoveQuiltPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingGotoMoveQuilt');
  late final _embSewingGotoMoveQuilt =
      _embSewingGotoMoveQuiltPtr.asFunction<int Function()>();

  int embSewingReturnMoveOrRotate() {
    return _embSewingReturnMoveOrRotate();
  }

  late final _embSewingReturnMoveOrRotatePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingReturnMoveOrRotate');
  late final _embSewingReturnMoveOrRotate =
      _embSewingReturnMoveOrRotatePtr.asFunction<int Function()>();

  ///********************************************************** */
  ///                                                           */
  /// スノーマン関連API一覧                                       */
  ///                                                           */
  ///********************************************************** */
  /// スノーマンモードを選択する
  int embSewingSelectSnowman() {
    return _embSewingSelectSnowman();
  }

  late final _embSewingSelectSnowmanPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingSelectSnowman');
  late final _embSewingSelectSnowman =
      _embSewingSelectSnowmanPtr.asFunction<int Function()>();

  /// スノーマンモードを選択する
  int embSewingSnowmanStartOk() {
    return _embSewingSnowmanStartOk();
  }

  late final _embSewingSnowmanStartOkPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingSnowmanStartOk');
  late final _embSewingSnowmanStartOk =
      _embSewingSnowmanStartOkPtr.asFunction<int Function()>();

  /// スノーマンモードをキャンセルする
  int embSewingSnowmanStartCancel() {
    return _embSewingSnowmanStartCancel();
  }

  late final _embSewingSnowmanStartCancelPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingSnowmanStartCancel');
  late final _embSewingSnowmanStartCancel =
      _embSewingSnowmanStartCancelPtr.asFunction<int Function()>();

  /// スノーマンスキャンを開始する
  int embSewingSnowmanScanOk() {
    return _embSewingSnowmanScanOk();
  }

  late final _embSewingSnowmanScanOkPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingSnowmanScanOk');
  late final _embSewingSnowmanScanOk =
      _embSewingSnowmanScanOkPtr.asFunction<int Function()>();

  /// スノーマンスキャンをキャンセルする
  int embSewingSnowmanScanCancel() {
    return _embSewingSnowmanScanCancel();
  }

  late final _embSewingSnowmanScanCancelPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingSnowmanScanCancel');
  late final _embSewingSnowmanScanCancel =
      _embSewingSnowmanScanCancelPtr.asFunction<int Function()>();

  int embSewingSnowmanScanCancelFrameMove() {
    return _embSewingSnowmanScanCancelFrameMove();
  }

  late final _embSewingSnowmanScanCancelFrameMovePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingSnowmanScanCancelFrameMove');
  late final _embSewingSnowmanScanCancelFrameMove =
      _embSewingSnowmanScanCancelFrameMovePtr.asFunction<int Function()>();

  /// スノーマンスキャンを開始する
  int embSewingSnowmanDirSelect(
    int dir,
  ) {
    return _embSewingSnowmanDirSelect(
      dir,
    );
  }

  late final _embSewingSnowmanDirSelectPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'embSewingSnowmanDirSelect');
  late final _embSewingSnowmanDirSelect =
      _embSewingSnowmanDirSelectPtr.asFunction<int Function(int)>();

  /// スノーマン枠移動する
  int embSewingSnowmanFrameMoveOk() {
    return _embSewingSnowmanFrameMoveOk();
  }

  late final _embSewingSnowmanFrameMoveOkPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingSnowmanFrameMoveOk');
  late final _embSewingSnowmanFrameMoveOk =
      _embSewingSnowmanFrameMoveOkPtr.asFunction<int Function()>();

  /// スノーマン枠移動キャンセルする
  int embSewingSnowmanFrameMoveCancel() {
    return _embSewingSnowmanFrameMoveCancel();
  }

  late final _embSewingSnowmanFrameMoveCancelPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingSnowmanFrameMoveCancel');
  late final _embSewingSnowmanFrameMoveCancel =
      _embSewingSnowmanFrameMoveCancelPtr.asFunction<int Function()>();

  /// スノーマンマックを取り出す
  int embSewingSnowmanEmbMachineRemove() {
    return _embSewingSnowmanEmbMachineRemove();
  }

  late final _embSewingSnowmanEmbMachineRemovePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingSnowmanEmbMachineRemove');
  late final _embSewingSnowmanEmbMachineRemove =
      _embSewingSnowmanEmbMachineRemovePtr.asFunction<int Function()>();

  /// スノーマン機能を結束する
  int embSewingSnowmanEnd() {
    return _embSewingSnowmanEnd();
  }

  late final _embSewingSnowmanEndPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embSewingSnowmanEnd');
  late final _embSewingSnowmanEnd =
      _embSewingSnowmanEndPtr.asFunction<int Function()>();

  /// 背景スキャン
  int embSewingSelectBackgroundScan() {
    return _embSewingSelectBackgroundScan();
  }

  late final _embSewingSelectBackgroundScanPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingSelectBackgroundScan');
  late final _embSewingSelectBackgroundScan =
      _embSewingSelectBackgroundScanPtr.asFunction<int Function()>();

  int embSewingBackgroundScanStart() {
    return _embSewingBackgroundScanStart();
  }

  late final _embSewingBackgroundScanStartPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingBackgroundScanStart');
  late final _embSewingBackgroundScanStart =
      _embSewingBackgroundScanStartPtr.asFunction<int Function()>();

  int embSewingBackgroundScanCancel() {
    return _embSewingBackgroundScanCancel();
  }

  late final _embSewingBackgroundScanCancelPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingBackgroundScanCancel');
  late final _embSewingBackgroundScanCancel =
      _embSewingBackgroundScanCancelPtr.asFunction<int Function()>();

  int embSewingBackgroundScanEnd() {
    return _embSewingBackgroundScanEnd();
  }

  late final _embSewingBackgroundScanEndPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingBackgroundScanEnd');
  late final _embSewingBackgroundScanEnd =
      _embSewingBackgroundScanEndPtr.asFunction<int Function()>();

  /// MDCの背景スキャン
  /// embSewingSelectBackgroundScanに繋がる
  int mdcSelectBackgroundScan() {
    return _mdcSelectBackgroundScan();
  }

  late final _mdcSelectBackgroundScanPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'mdcSelectBackgroundScan');
  late final _mdcSelectBackgroundScan =
      _mdcSelectBackgroundScanPtr.asFunction<int Function()>();

  /// FB
  int embSewingSelectFb() {
    return _embSewingSelectFb();
  }

  late final _embSewingSelectFbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embSewingSelectFb');
  late final _embSewingSelectFb =
      _embSewingSelectFbPtr.asFunction<int Function()>();

  int embSewingFbStitchMove(
    int moveType,
  ) {
    return _embSewingFbStitchMove(
      moveType,
    );
  }

  late final _embSewingFbStitchMovePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'embSewingFbStitchMove');
  late final _embSewingFbStitchMove =
      _embSewingFbStitchMovePtr.asFunction<int Function(int)>();

  int embSewingFbCameraView() {
    return _embSewingFbCameraView();
  }

  late final _embSewingFbCameraViewPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingFbCameraView');
  late final _embSewingFbCameraView =
      _embSewingFbCameraViewPtr.asFunction<int Function()>();

  int embSewingFbEnd() {
    return _embSewingFbEnd();
  }

  late final _embSewingFbEndPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embSewingFbEnd');
  late final _embSewingFbEnd = _embSewingFbEndPtr.asFunction<int Function()>();

  /// カメラFB
  int embSewingStartCameraFb() {
    return _embSewingStartCameraFb();
  }

  late final _embSewingStartCameraFbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingStartCameraFb');
  late final _embSewingStartCameraFb =
      _embSewingStartCameraFbPtr.asFunction<int Function()>();

  int embSewingCloseCameraFb() {
    return _embSewingCloseCameraFb();
  }

  late final _embSewingCloseCameraFbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingCloseCameraFb');
  late final _embSewingCloseCameraFb =
      _embSewingCloseCameraFbPtr.asFunction<int Function()>();

  int embSewingChangeNeedleViewCameraFb() {
    return _embSewingChangeNeedleViewCameraFb();
  }

  late final _embSewingChangeNeedleViewCameraFbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingChangeNeedleViewCameraFb');
  late final _embSewingChangeNeedleViewCameraFb =
      _embSewingChangeNeedleViewCameraFbPtr.asFunction<int Function()>();

  int embSewingZoomCameraFb() {
    return _embSewingZoomCameraFb();
  }

  late final _embSewingZoomCameraFbPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingZoomCameraFb');
  late final _embSewingZoomCameraFb =
      _embSewingZoomCameraFbPtr.asFunction<int Function()>();

  /// つなぎ
  void embSewOverMarkPatCnct(
    bool flg,
  ) {
    return _embSewOverMarkPatCnct(
      flg,
    );
  }

  late final _embSewOverMarkPatCnctPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Bool)>>(
          'embSewOverMarkPatCnct');
  late final _embSewOverMarkPatCnct =
      _embSewOverMarkPatCnctPtr.asFunction<void Function(bool)>();

  void embNextPatternSelMarkPatCnct() {
    return _embNextPatternSelMarkPatCnct();
  }

  late final _embNextPatternSelMarkPatCnctPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>(
          'embNextPatternSelMarkPatCnct');
  late final _embNextPatternSelMarkPatCnct =
      _embNextPatternSelMarkPatCnctPtr.asFunction<void Function()>();

  void embMarkPatCnctMove(
    int dir,
  ) {
    return _embMarkPatCnctMove(
      dir,
    );
  }

  late final _embMarkPatCnctMovePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Int32)>>(
          'embMarkPatCnctMove');
  late final _embMarkPatCnctMove =
      _embMarkPatCnctMovePtr.asFunction<void Function(int)>();

  int embMarkPatCnctRotateLeft() {
    return _embMarkPatCnctRotateLeft();
  }

  late final _embMarkPatCnctRotateLeftPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embMarkPatCnctRotateLeft');
  late final _embMarkPatCnctRotateLeft =
      _embMarkPatCnctRotateLeftPtr.asFunction<int Function()>();

  int embMarkPatCnctRotateRight() {
    return _embMarkPatCnctRotateRight();
  }

  late final _embMarkPatCnctRotateRightPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embMarkPatCnctRotateRight');
  late final _embMarkPatCnctRotateRight =
      _embMarkPatCnctRotateRightPtr.asFunction<int Function()>();

  int embMarkPatCnctRotateRight90() {
    return _embMarkPatCnctRotateRight90();
  }

  late final _embMarkPatCnctRotateRight90Ptr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embMarkPatCnctRotateRight90');
  late final _embMarkPatCnctRotateRight90 =
      _embMarkPatCnctRotateRight90Ptr.asFunction<int Function()>();

  void embMarkPatCnctScrSetting(
    int posX,
    int posY,
  ) {
    return _embMarkPatCnctScrSetting(
      posX,
      posY,
    );
  }

  late final _embMarkPatCnctScrSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Int32, ffi.Int32)>>(
          'embMarkPatCnctScrSetting');
  late final _embMarkPatCnctScrSetting =
      _embMarkPatCnctScrSettingPtr.asFunction<void Function(int, int)>();

  void embSetMarkPatCnctStdCancelBefore() {
    return _embSetMarkPatCnctStdCancelBefore();
  }

  late final _embSetMarkPatCnctStdCancelBeforePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>(
          'embSetMarkPatCnctStdCancelBefore');
  late final _embSetMarkPatCnctStdCancelBefore =
      _embSetMarkPatCnctStdCancelBeforePtr.asFunction<void Function()>();

  void embSetBefore1stMarkPatCnctOK() {
    return _embSetBefore1stMarkPatCnctOK();
  }

  late final _embSetBefore1stMarkPatCnctOKPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>(
          'embSetBefore1stMarkPatCnctOK');
  late final _embSetBefore1stMarkPatCnctOK =
      _embSetBefore1stMarkPatCnctOKPtr.asFunction<void Function()>();

  void embSetBefore2ndMarkPatCnctOK() {
    return _embSetBefore2ndMarkPatCnctOK();
  }

  late final _embSetBefore2ndMarkPatCnctOKPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>(
          'embSetBefore2ndMarkPatCnctOK');
  late final _embSetBefore2ndMarkPatCnctOK =
      _embSetBefore2ndMarkPatCnctOKPtr.asFunction<void Function()>();

  void embMarkPatCnctEditParamSet(
    ffi.Pointer<SSPoint_t> pos,
    int cnctSetting,
    int angle,
  ) {
    return _embMarkPatCnctEditParamSet(
      pos,
      cnctSetting,
      angle,
    );
  }

  late final _embMarkPatCnctEditParamSetPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<SSPoint_t>, ffi.Int8,
              ffi.Int16)>>('embMarkPatCnctEditParamSet');
  late final _embMarkPatCnctEditParamSet = _embMarkPatCnctEditParamSetPtr
      .asFunction<void Function(ffi.Pointer<SSPoint_t>, int, int)>();

  void embMarkPatCnctDistanceGet(
    ffi.Pointer<ffi.Double> dis,
  ) {
    return _embMarkPatCnctDistanceGet(
      dis,
    );
  }

  late final _embMarkPatCnctDistanceGetPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Double>)>>(
          'embMarkPatCnctDistanceGet');
  late final _embMarkPatCnctDistanceGet = _embMarkPatCnctDistanceGetPtr
      .asFunction<void Function(ffi.Pointer<ffi.Double>)>();

  void embMarkPatCnctImgDrawPrmGet(
    int markNo,
    ffi.Pointer<EmbMarkPatImgDrawParam_t> param,
  ) {
    return _embMarkPatCnctImgDrawPrmGet(
      markNo,
      param,
    );
  }

  late final _embMarkPatCnctImgDrawPrmGetPtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Uint16, ffi.Pointer<EmbMarkPatImgDrawParam_t>)>>(
      'embMarkPatCnctImgDrawPrmGet');
  late final _embMarkPatCnctImgDrawPrmGet = _embMarkPatCnctImgDrawPrmGetPtr
      .asFunction<void Function(int, ffi.Pointer<EmbMarkPatImgDrawParam_t>)>();

  void embMarkPatCnctSnowmanDrawPrmGet(
    int markNo,
    ffi.Pointer<EmbMarkPatSnowmanDrawParam_t> param,
  ) {
    return _embMarkPatCnctSnowmanDrawPrmGet(
      markNo,
      param,
    );
  }

  late final _embMarkPatCnctSnowmanDrawPrmGetPtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Uint16, ffi.Pointer<EmbMarkPatSnowmanDrawParam_t>)>>(
      'embMarkPatCnctSnowmanDrawPrmGet');
  late final _embMarkPatCnctSnowmanDrawPrmGet =
      _embMarkPatCnctSnowmanDrawPrmGetPtr.asFunction<
          void Function(int, ffi.Pointer<EmbMarkPatSnowmanDrawParam_t>)>();

  void embMarkPatCnctSetScanResultImage(
    ffi.Pointer<ffi.Uint8> lastImgData,
    int lastImgWidth,
    int lastImgHeight,
    ffi.Pointer<ffi.Uint8> nowImgData,
    int nowImgWidht,
    int nowImgHeight,
    int r,
    int g,
    int b,
  ) {
    return _embMarkPatCnctSetScanResultImage(
      lastImgData,
      lastImgWidth,
      lastImgHeight,
      nowImgData,
      nowImgWidht,
      nowImgHeight,
      r,
      g,
      b,
    );
  }

  late final _embMarkPatCnctSetScanResultImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<ffi.Uint8>,
              ffi.Uint16,
              ffi.Uint16,
              ffi.Pointer<ffi.Uint8>,
              ffi.Uint16,
              ffi.Uint16,
              ffi.Uint8,
              ffi.Uint8,
              ffi.Uint8)>>('embMarkPatCnctSetScanResultImage');
  late final _embMarkPatCnctSetScanResultImage =
      _embMarkPatCnctSetScanResultImagePtr.asFunction<
          void Function(ffi.Pointer<ffi.Uint8>, int, int,
              ffi.Pointer<ffi.Uint8>, int, int, int, int, int)>();

  bool embMarkPatCnctGetScanResultImage(
    ffi.Pointer<ffi.Uint8> resultImage,
  ) {
    return _embMarkPatCnctGetScanResultImage(
      resultImage,
    );
  }

  late final _embMarkPatCnctGetScanResultImagePtr =
      _lookup<ffi.NativeFunction<ffi.Bool Function(ffi.Pointer<ffi.Uint8>)>>(
          'embMarkPatCnctGetScanResultImage');
  late final _embMarkPatCnctGetScanResultImage =
      _embMarkPatCnctGetScanResultImagePtr
          .asFunction<bool Function(ffi.Pointer<ffi.Uint8>)>();

  void embMarkPatCnctDelScanResultImage() {
    return _embMarkPatCnctDelScanResultImage();
  }

  late final _embMarkPatCnctDelScanResultImagePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>(
          'embMarkPatCnctDelScanResultImage');
  late final _embMarkPatCnctDelScanResultImage =
      _embMarkPatCnctDelScanResultImagePtr.asFunction<void Function()>();

  int embMarkPatCnctSettingGet() {
    return _embMarkPatCnctSettingGet();
  }

  late final _embMarkPatCnctSettingGetPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embMarkPatCnctSettingGet');
  late final _embMarkPatCnctSettingGet =
      _embMarkPatCnctSettingGetPtr.asFunction<int Function()>();

  void embMarkPatCnctSettingOffsetGet(ffi.Pointer<SSPoint_t> offset) {
    return _embMarkPatCnctSettingOffsetGet(offset);
  }

  late final _embMarkPatCnctSettingOffsetGetPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<SSPoint_t>)>>(
          'embMarkPatCnctSettingOffsetGet');
  late final _embMarkPatCnctSettingOffsetGet =
      _embMarkPatCnctSettingOffsetGetPtr
          .asFunction<void Function(ffi.Pointer<SSPoint_t>)>();

  /// 大型分割
  int emb1stAutoMarkPatCnctOK() {
    return _emb1stAutoMarkPatCnctOK();
  }

  late final _emb1stAutoMarkPatCnctOKPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'emb1stAutoMarkPatCnctOK');
  late final _emb1stAutoMarkPatCnctOK =
      _emb1stAutoMarkPatCnctOKPtr.asFunction<int Function()>();

  int emb1stAutoMarkPatCnctCancel() {
    return _emb1stAutoMarkPatCnctCancel();
  }

  late final _emb1stAutoMarkPatCnctCancelPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'emb1stAutoMarkPatCnctCancel');
  late final _emb1stAutoMarkPatCnctCancel =
      _emb1stAutoMarkPatCnctCancelPtr.asFunction<int Function()>();

  int emb1stAutoConfirmMarkPatCnctCancel() {
    return _emb1stAutoConfirmMarkPatCnctCancel();
  }

  late final _emb1stAutoConfirmMarkPatCnctCancelPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'emb1stAutoConfirmMarkPatCnctCancel');
  late final _emb1stAutoConfirmMarkPatCnctCancel =
      _emb1stAutoConfirmMarkPatCnctCancelPtr.asFunction<int Function()>();

  int emb1stAutoConfirmMarkPatCnctOk() {
    return _emb1stAutoConfirmMarkPatCnctOk();
  }

  late final _emb1stAutoConfirmMarkPatCnctOkPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'emb1stAutoConfirmMarkPatCnctOk');
  late final _emb1stAutoConfirmMarkPatCnctOk =
      _emb1stAutoConfirmMarkPatCnctOkPtr.asFunction<int Function()>();

  /// マスクトレース
  int embSewingSelectMaskTrace() {
    return _embSewingSelectMaskTrace();
  }

  late final _embSewingSelectMaskTracePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingSelectMaskTrace');
  late final _embSewingSelectMaskTrace =
      _embSewingSelectMaskTracePtr.asFunction<int Function()>();

  int embSewingMaskTraceExe() {
    return _embSewingMaskTraceExe();
  }

  late final _embSewingMaskTraceExePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingMaskTraceExe');
  late final _embSewingMaskTraceExe =
      _embSewingMaskTraceExePtr.asFunction<int Function()>();

  int embSewingMaskTraceEnd() {
    return _embSewingMaskTraceEnd();
  }

  late final _embSewingMaskTraceEndPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingMaskTraceEnd');
  late final _embSewingMaskTraceEnd =
      _embSewingMaskTraceEndPtr.asFunction<int Function()>();

  int embSewingMaskTracePosSet(
    int pos,
  ) {
    return _embSewingMaskTracePosSet(
      pos,
    );
  }

  late final _embSewingMaskTracePosSetPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'embSewingMaskTracePosSet');
  late final _embSewingMaskTracePosSet =
      _embSewingMaskTracePosSetPtr.asFunction<int Function(int)>();

  /// 縫い始め
  int embSewingSelectStartPosition() {
    return _embSewingSelectStartPosition();
  }

  late final _embSewingSelectStartPositionPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingSelectStartPosition');
  late final _embSewingSelectStartPosition =
      _embSewingSelectStartPositionPtr.asFunction<int Function()>();

  int embSewingStartPositionSet() {
    return _embSewingStartPositionSet();
  }

  late final _embSewingStartPositionSetPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingStartPositionSet');
  late final _embSewingStartPositionSet =
      _embSewingStartPositionSetPtr.asFunction<int Function()>();

  int embSewingStartPositionClear() {
    return _embSewingStartPositionClear();
  }

  late final _embSewingStartPositionClearPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingStartPositionClear');
  late final _embSewingStartPositionClear =
      _embSewingStartPositionClearPtr.asFunction<int Function()>();

  int embSewingStartPositionEnd() {
    return _embSewingStartPositionEnd();
  }

  late final _embSewingStartPositionEndPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingStartPositionEnd');
  late final _embSewingStartPositionEnd =
      _embSewingStartPositionEndPtr.asFunction<int Function()>();

  /// 縫製-枠退避
  int embSewingEscUnit() {
    return _embSewingEscUnit();
  }

  late final _embSewingEscUnitPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embSewingEscUnit');
  late final _embSewingEscUnit =
      _embSewingEscUnitPtr.asFunction<int Function()>();

  int embSewingMoveEscUnit() {
    return _embSewingMoveEscUnit();
  }

  late final _embSewingMoveEscUnitPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('embSewingMoveEscUnit');
  late final _embSewingMoveEscUnit =
      _embSewingMoveEscUnitPtr.asFunction<int Function()>();

  int embSewingReturnEscUnit() {
    return _embSewingReturnEscUnit();
  }

  late final _embSewingReturnEscUnitPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'embSewingReturnEscUnit');
  late final _embSewingReturnEscUnit =
      _embSewingReturnEscUnitPtr.asFunction<int Function()>();

  bool isEmbSewingMoveEscUnit() {
    return _isEmbSewingMoveEscUnit();
  }

  late final _isEmbSewingMoveEscUnitPtr =
      _lookup<ffi.NativeFunction<ffi.Bool Function()>>(
          'isEmbSewingMoveEscUnit');
  late final _isEmbSewingMoveEscUnit =
      _isEmbSewingMoveEscUnitPtr.asFunction<bool Function()>();

  /// レジューム
  int embResumeDataRead(
    ffi.Pointer<ffi.Char> fpath,
  ) {
    return _embResumeDataRead(
      fpath,
    );
  }

  late final _embResumeDataReadPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Char>)>>(
          'embResumeDataRead');
  late final _embResumeDataRead =
      _embResumeDataReadPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  /// カメラスキャンしたデータを取得する
  int getCameraScanInfoAndData(
    int kind,
    ffi.Pointer<CameraScanInfoAndData_t> infoAndData,
  ) {
    return _getCameraScanInfoAndData(
      kind,
      infoAndData,
    );
  }

  late final _getCameraScanInfoAndDataPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(
                  ffi.Int32, ffi.Pointer<CameraScanInfoAndData_t>)>>(
      'getCameraScanInfoAndData');
  late final _getCameraScanInfoAndData = _getCameraScanInfoAndDataPtr
      .asFunction<int Function(int, ffi.Pointer<CameraScanInfoAndData_t>)>();

  /// スキャン画像を取得する
  int getCameraScanImage(
    ffi.Pointer<ffi.Char> cameraScanInfoPath,
    ffi.Pointer<ffi.Char> cameraScanDataPath,
    int scale,
    ffi.Pointer<embImg_t> outputImage,
  ) {
    return _getCameraScanImage(
      cameraScanInfoPath,
      cameraScanDataPath,
      scale,
      outputImage,
    );
  }

  late final _getCameraScanImagePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>,
              ffi.Int32, ffi.Pointer<embImg_t>)>>('getCameraScanImage');
  late final _getCameraScanImage = _getCameraScanImagePtr.asFunction<
      int Function(ffi.Pointer<ffi.Char>, ffi.Pointer<ffi.Char>, int,
          ffi.Pointer<embImg_t>)>();

  /// スキャン画像を取得する ( テストモード64. Scan Check 専用 )
  int getCameraScanTestModeImage(
    int kind,
    ffi.Pointer<embImg_t> outputImage,
  ) {
    return _getCameraScanTestModeImage(
      kind,
      outputImage,
    );
  }

  late final _getCameraScanTestModeImagePtr = _lookup<
          ffi
          .NativeFunction<ffi.Int32 Function(ffi.Int, ffi.Pointer<embImg_t>)>>(
      'getCameraScanTestModeImage');
  late final _getCameraScanTestModeImage = _getCameraScanTestModeImagePtr
      .asFunction<int Function(int, ffi.Pointer<embImg_t>)>();

  /// データ保存できるかどうか
  int isEnabledSevedToExternalMemory() {
    return _isEnabledSevedToExternalMemory();
  }

  late final _isEnabledSevedToExternalMemoryPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'isEnabledSevedToExternalMemory');
  late final _isEnabledSevedToExternalMemory =
      _isEnabledSevedToExternalMemoryPtr.asFunction<int Function()>();

  int isEnabledSevedToInternalMemory() {
    return _isEnabledSevedToInternalMemory();
  }

  late final _isEnabledSevedToInternalMemoryPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'isEnabledSevedToInternalMemory');
  late final _isEnabledSevedToInternalMemory =
      _isEnabledSevedToInternalMemoryPtr.asFunction<int Function()>();

  int checkMemoryForEmbSewing() {
    return _checkMemoryForEmbSewing();
  }

  late final _checkMemoryForEmbSewingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'checkMemoryForEmbSewing');
  late final _checkMemoryForEmbSewing =
      _checkMemoryForEmbSewingPtr.asFunction<int Function()>();

  int checkMemoryForEmbEdit() {
    return _checkMemoryForEmbEdit();
  }

  late final _checkMemoryForEmbEditPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'checkMemoryForEmbEdit');
  late final _checkMemoryForEmbEdit =
      _checkMemoryForEmbEditPtr.asFunction<int Function()>();

  /// 大カテゴリの選択準備
  int checkEmbLargeCategorySelection(
    int cate,
  ) {
    return _checkEmbLargeCategorySelection(
      cate,
    );
  }

  late final _checkEmbLargeCategorySelectionPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'checkEmbLargeCategorySelection');
  late final _checkEmbLargeCategorySelection =
      _checkEmbLargeCategorySelectionPtr.asFunction<int Function(int)>();

  /// Volatile設定
  bool isVolatileEmb() {
    return _isVolatileEmb();
  }

  late final _isVolatileEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Bool Function()>>('isVolatileEmb');
  late final _isVolatileEmb = _isVolatileEmbPtr.asFunction<bool Function()>();

  void setVolatileCurEmbGroup(
    bool isVolatile,
  ) {
    return _setVolatileCurEmbGroup(
      isVolatile,
    );
  }

  late final _setVolatileCurEmbGroupPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Bool)>>(
          'setVolatileCurEmbGroup');
  late final _setVolatileCurEmbGroup =
      _setVolatileCurEmbGroupPtr.asFunction<void Function(bool)>();

  /// Restriction設定
  bool isRestrictionEmb() {
    return _isRestrictionEmb();
  }

  late final _isRestrictionEmbPtr =
      _lookup<ffi.NativeFunction<ffi.Bool Function()>>('isRestrictionEmb');
  late final _isRestrictionEmb =
      _isRestrictionEmbPtr.asFunction<bool Function()>();

  void setRestrictionCurEmbGroup(
    int restriction,
  ) {
    return _setRestrictionCurEmbGroup(
      restriction,
    );
  }

  late final _setRestrictionCurEmbGroupPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Uint16)>>(
          'setRestrictionCurEmbGroup');
  late final _setRestrictionCurEmbGroup =
      _setRestrictionCurEmbGroupPtr.asFunction<void Function(int)>();
}

abstract class Bool_t {
  static const int F = 0;
  static const int T = 1;
}

final class BrandColorTbl extends ffi.Struct {
  /// userthread
  @ffi.Int32()
  external int code;

  @ffi.Uint8()
  external int r;

  @ffi.Uint8()
  external int g;

  @ffi.Uint8()
  external int b;

  @ffi.Uint16()
  external int index300;

  /// StandardCodeに置き換えた場合のindex
  @ffi.Uint8()
  external int stdCode;
}

/// TYPEDEFS **************************************************************/
/// //輪郭種類
abstract class AppliqueOutlineMode_t {
  /// 外側　デフォルト
  static const int APP_OUTLINE_MODE_EX = 0;

  /// 外側+内側
  static const int APP_OUTLINE_MODE_EX_IN = 1;

  /// 内側
  static const int APP_OUTLINE_MODE_IN = 2;
  static const int APP_OUTLINE_MODE_MAX = 3;
}

/// サテンステッチ種類
abstract class AppliqueSatinKind_t {
  /// 仮止め　デフォルト
  static const int APP_SATIN_KIND_TACDOWN = 0;

  /// 仮止め+サテン
  static const int APP_SATIN_KIND_COVERINGSTITCH = 1;

  /// OFF
  static const int APP_SATIN_KIND_OFF = 2;
  static const int APP_SATIN_KIND_MAX = 3;
}

/// 走りステッチ種類
abstract class AppliqueRunKind_t {
  /// 走り縫い　デフォルト
  static const int APP_RUN_KIND_RUN = 0;

  /// OFF
  static const int APP_RUN_KIND_OFF = 1;
  static const int APP_RUN_KIND_MAX = 2;
}

/// ステッチ幅
abstract class AppliqueStitchWidth_t {
  /// 2.0mm
  static const int APP_STITCH_WIDTH_20 = 0;

  /// 2.5mm
  static const int APP_STITCH_WIDTH_25 = 1;

  /// 3.0mm
  static const int APP_STITCH_WIDTH_30 = 2;

  /// 3.5mm　デフォルト
  static const int APP_STITCH_WIDTH_35 = 3;

  /// 4.0mm
  static const int APP_STITCH_WIDTH_40 = 4;

  /// 4.5mm
  static const int APP_STITCH_WIDTH_45 = 5;

  /// 5.0mm
  static const int APP_STITCH_WIDTH_50 = 6;

  /// 5.5mm
  static const int APP_STITCH_WIDTH_55 = 7;

  /// 6.0mm
  static const int APP_STITCH_WIDTH_60 = 8;
  static const int APP_STITCH_WIDTH_MAX = 9;
}

/// ステッチ密度
abstract class AppliqueStitchDens_t {
  /// 0.25本/mm
  static const int APP_STITCH_DENS_025 = 0;

  /// 0.5本/mm
  static const int APP_STITCH_DENS_050 = 1;

  /// 0.75本/mm
  static const int APP_STITCH_DENS_075 = 2;

  /// 1.0本/mm
  static const int APP_STITCH_DENS_100 = 3;

  /// 1.25本/mm
  static const int APP_STITCH_DENS_125 = 4;

  /// 1.5本/mm
  static const int APP_STITCH_DENS_150 = 5;

  /// 1.75本/mm
  static const int APP_STITCH_DENS_175 = 6;

  /// 2.0本/mm
  static const int APP_STITCH_DENS_200 = 7;

  /// 2.5本/mm
  static const int APP_STITCH_DENS_250 = 8;

  /// 3.0本/mm
  static const int APP_STITCH_DENS_300 = 9;

  /// 3.5本/mm
  static const int APP_STITCH_DENS_350 = 10;

  /// 4.0本/mm
  static const int APP_STITCH_DENS_400 = 11;

  /// 4.5本/mm
  static const int APP_STITCH_DENS_450 = 12;

  /// 5.0本/mm
  static const int APP_STITCH_DENS_500 = 13;

  /// 5.5本/mm
  static const int APP_STITCH_DENS_550 = 14;
  static const int APP_STITCH_DENS_MAX = 15;
}

/// ステッチ長さ
abstract class AppliqueStitchLength_t {
  /// 1.0mm
  static const int APP_STITCH_LENGTH_10 = 0;

  /// 1.5mm
  static const int APP_STITCH_LENGTH_15 = 1;

  /// 2.0mm
  static const int APP_STITCH_LENGTH_20 = 2;

  /// 2.5mm
  static const int APP_STITCH_LENGTH_25 = 3;

  /// 3.0mm
  static const int APP_STITCH_LENGTH_30 = 4;

  /// 3.5mm
  static const int APP_STITCH_LENGTH_35 = 5;

  /// 4.0mm
  static const int APP_STITCH_LENGTH_40 = 6;
  static const int APP_STITCH_LENGTH_MAX = 7;
}

abstract class MagType_t {
  static const int X_ONLY = 0;
  static const int Y_ONLY = 1;
  static const int XY_ALL = 2;
  static const int XY_RESET = 3;
}

abstract class ThreadColorDrawType_t {
  static const int DRAW_BLANK = 0;
  static const int THREAD_COLOR_EMB_EDIT = 1;
  static const int THREAD_COLOR_EMB_INFO = 2;
  static const int THREAD_COLOR_EMB_COL_CHANGE = 3;
  static const int THREAD_COLOR_EMB_SEWING = 4;
  static const int THREAD_COLOR_EMB_APPLIQUE = 5;

  /// 新アップリケ プレビュー縫わない設定画面用
  static const int THREAD_COLOR_EMB_APPLIQUE_PREVIEW = 6;
  static const int THREAD_COLOR_EMB_NOTSEWING = 7;
  static const int DRAW_THREAD_COLOR_DRAW_TYPE_MAX = 8;
}

/// 刺繍データの種類
abstract class ETL_kind {
  /// 通常の模様
  static const int ETL_KIND_NORMAL = 0;

  /// ボビンワーク用の模様
  static const int ETL_KIND_BW = 1;

  /// 大型分割模様
  static const int ETL_KIND_LARGE = 2;

  /// マルチポジション枠用模様
  static const int ETL_KIND_MULTI = 3;

  /// 反転枠用模様
  static const int ETL_KIND_REVERSAL = 4;

  /// 拡張キルトサッシ模様
  static const int ETL_KIND_EXTQUILT = 5;

  /// 多角形キルトサッシ模様
  static const int ETL_KIND_POLYGON_QUILT = 6;

  /// カウチング模様
  static const int ETL_KIND_COUTING = 7;

  /// Edge模様
  static const int ETL_KIND_EDGE = 8;
}

final class Link_t extends ffi.Struct {
  external MemHandle_t Magic; /* ポカヨケ				*/
  /// 次の要素
  external MemHandle_t Next;

  /// 前の要素
  external MemHandle_t Prev;
}

typedef MemHandle_t = ffi.Pointer<ffi.Void>;

final class quiltSashesLimit_t extends ffi.Struct {
  /// 最大
  @ffi.Int32()
  external int widthMax;

  /// 最少
  @ffi.Int32()
  external int widthMin;

  /// 最大
  @ffi.Int32()
  external int heightMax;

  /// 最少
  @ffi.Int32()
  external int heightMin;

  /// 最大
  @ffi.Int32()
  external int bandMax;

  /// 最少
  @ffi.Int32()
  external int bandMin;

  static ffi.Pointer<quiltSashesLimit_t> allocate() {
    final pointer = ffi.calloc<quiltSashesLimit_t>();
    pointer.ref.widthMax = 0;
    pointer.ref.widthMin = 0;
    pointer.ref.heightMax = 0;
    pointer.ref.heightMin = 0;
    pointer.ref.bandMax = 0;
    pointer.ref.bandMin = 0;
    return pointer;
  }
}

abstract class ThumbnailSize_t {
  static const int THUMB_SMALL = 0;
  static const int THUMB_MID = 1;
  static const int THUMB_LARGE = 2;
  static const int THUMB_MAX = 3;
}

abstract class ScrollCenterType_t {
  /// 0
  static const int IMAGE_SELECT = 0;

  /// 1 プレビュー画面
  static const int IMAGE_INFO = 1;

  /// 2 刺しゅう編集画面
  static const int IMAGE_EDITING = 2;

  /// 3 刺しゅう縫製画面
  static const int IMAGE_SEWING = 3;

  /// 4 色替え画面
  static const int IMAGE_EDIT_COLOR_CNG = 4;

  /// 5 フォント選択画面
  static const int IMAGE_SELECT_FONT = 5;

  /// 6 簡単ステップリング作成画面、スタンプ作成画面
  static const int IMAGE_EASYSTIPPLE = 6;

  /// 7 MDC縫製設定画面
  static const int IMAGE_MDC_STITCH = 7;

  /// 8
  static const int IMAGE_SEWING_STITCH = 8;

  /// 9 プロジェクター刺繍位置合わせ画面
  static const int IMAGE_SEWING_PROJECTOR = 9;

  /// 10 刺繍-つなぎ設定画面
  static const int IMAGE_CNCT = 10;

  /// 11刺繍-縫製順入れ替え画面 イメージエリアに対する移動機能を無効
  static const int IMAGE_EDIT_CHANGE_ORDER = 11;

  /// ScrollCenterType総数
  static const int IMAGE_SCROLL_CENTER_TYPE_MAX = 12;
}

/// 使用枠種別
abstract class EmbQuiltSashesFrameType_t {
  /*	4分割キルト	1方向キルト	 6角形キルト  EdgeToEdge	*/
  static const int EMBQUILTSASHES_FRAME_297_465 = 0; /*		○			○			○			○			*/
  static const int EMBQUILTSASHES_FRAME_272_408 = 1; /*		○			○			○			○			*/
  static const int EMBQUILTSASHES_FRAME_272_272 = 2; /*		○			○			○			〇			*/
  /*** XP3　254x254新マグネット枠対応-7-1　2022/01/12　H.Kawasaki ***/
//	EMBQUILTSASHES_FRAME_254_254,		/*		×			×			×			〇			*/
  static const int EMBQUILTSASHES_FRAME_254_254 = 3; /*		○			○			○			〇			*/
  /*** XP3　254x254新マグネット枠対応-7-1　2022/01/12　H.Kawasaki ***/
  static const int EMBQUILTSASHES_FRAME_240_360 = 4; /*		○			○			○			〇			*/
  static const int EMBQUILTSASHES_FRAME_240_240 = 5; /*		○			○			○			〇			*/
  static const int EMBQUILTSASHES_FRAME_200_300 = 6; /*		×			○			○			〇			*/
  static const int EMBQUILTSASHES_FRAME_180_360 =
      7; /*		×			○			○			〇			*/ // マグネット枠
/* ↓PHFIRMXP-2658 */
  static const int EMBQUILTSASHES_FRAME_180_300 = 8; /*		×			○			○			〇			*/
/* ↑PHFIRMXP-2658 */
  static const int EMBQUILTSASHES_FRAME_100_300 =
      9; /*		×			○			○			×			*/ // ジャンボボーダー枠
  static const int EMBQUILTSASHES_FRAME_MAX = 10;
}

/// Signed Short Point or Vector
final class SSPoint_t extends ffi.Struct {
  @ffi.Short()
  external int X;

  @ffi.Short()
  external int Y;

  set() {
    this.X = 0;
    this.Y = 0;
  }

  static ffi.Pointer<SSPoint_t> allocate() {
    final pointer = ffi.calloc<SSPoint_t>();
    pointer.ref.X = 0;
    pointer.ref.Y = 0;
    return pointer;
  }
}

final class RectanArea_t extends ffi.Struct {
  /// Left ≦ Right	X座標の範囲
  @ffi.Short()
  external int Left;

  @ffi.Short()
  external int Right;

  /// Top	 ≦ Bottom	Y座標の範囲
  @ffi.Short()
  external int Top;

  @ffi.Short()
  external int Bottom;

  set() {
    this.Left = 0;
    this.Right = 0;
    this.Top = 0;
    this.Bottom = 0;
  }
}

final class Rectangle_t extends ffi.Struct {
  /// Top-Left		左上の頂点
  external SSPoint_t Ptl;

  /// Top-Right		右上の頂点
  external SSPoint_t Ptr;

  /// Bottom-Left		左下の頂点
  external SSPoint_t Pbl;

  /// Bottom-Right	右下の頂点
  external SSPoint_t Pbr;

  set() {
    this.Ptl.set();
    this.Ptr.set();
    this.Pbl.set();
    this.Pbl.set();
  }
}

final class Magnitude_t extends ffi.Struct {
  /// 掛ける倍率、X横方向、Y縦方向
  @ffi.Short()
  external int X;

  @ffi.Short()
  external int Y;
  set() {
    this.X = 0;
    this.Y = 0;
  }

  static ffi.Pointer<Magnitude_t> allocate() {
    final pointer = ffi.calloc<Magnitude_t>();
    pointer.ref.X = 0;
    pointer.ref.Y = 0;
    return pointer;
  }
}

final class embImg_t extends ffi.Struct {
  @ffi.Int32()
  external int imageWidth;

  @ffi.Int32()
  external int imageHeight;

  @ffi.Int32()
  external int imageSize;

  external ffi.Pointer<ffi.Uint8> imageData;

  static ffi.Pointer<embImg_t> allocate() {
    final pointer = ffi.calloc<embImg_t>();
    pointer.ref.imageWidth = 0;
    pointer.ref.imageHeight = 0;
    pointer.ref.imageSize = 0;
    pointer.ref.imageData = ffi.calloc<ffi.Uint8>();
    pointer.ref.imageData.value = 0;
    return pointer;
  }
}

final class EmbBorder_t extends ffi.Struct {
  /// 自分自身の双方向リンクリストポインタ
  external Link_t Link;

  /// PHX_OUTPUT_PARAM!!!//形状　XサイズとYサイズ
  external SSPoint_t Style;

  /// PHX_OUTPUT_PARAM!!!//模様間
  external SSPoint_t Space;

  /// PHX_OUTPUT_PARAM!!!//回転するときに必要な中心地（ただし、親の中心地)
  external SSPoint_t ScrollOffset;

  /// PHX_OUTPUT_PARAM!!!//ボーダー模様内の１つの単位内のグループ数
  @ffi.Uint16()
  external int GroupNum;

  /// PHX_OUTPUT_PARAM!!!//Embroidery全体の最初から数えて何番目のグループが親なのか?
  @ffi.Uint16()
  external int ParentIndex;

  /// PHX_OUTPUT_PARAM!!!//回転角度
  @ffi.Uint16()
  external int Angle;

  static ffi.Pointer<EmbBorder_t> allocate() {
    final pointer = ffi.calloc<EmbBorder_t>();
    // pointer.ref.Link.Magic = ffi.calloc<ffi.Void>();
    // pointer.ref.Link.Next = ffi.calloc<ffi.Void>();
    // pointer.ref.Link.Prev = ffi.calloc<ffi.Void>();
// ↓ マージでエラー出たので取り合えず通る様にした
    pointer.ref.Style.set();
    pointer.ref.Space.set();
    pointer.ref.ScrollOffset.set();
    // pointer.ref.Style.set(0, 0);
    // pointer.ref.Space.set(0, 0);
    // pointer.ref.ScrollOffset.set(0, 0);
// ↑ マージでエラー出たので取り合えず通る様にした
    pointer.ref.GroupNum = 0;
    pointer.ref.ParentIndex = 0;
    pointer.ref.Angle = 0;
    return pointer;
  }

  static ffi.Pointer<ffi.Pointer<EmbBorder_t>> allocate_p() {
    final pointer = ffi.calloc<ffi.Pointer<EmbBorder_t>>();
    return pointer;
  }
}

final class embGrp_t extends ffi.Struct {
  /// 模様枠中心(0,0)から刺繍の中心までの距離(x,y　0.1mm単位)
  external SSPoint_t position;

  /// 右回転角度 0.1度単位 0[0度]-3599[359.9度]
  @ffi.Int16()
  external int angle;

  /// 選択されているかどうか
  @ffi.Uint16()
  external int selected;

  /// 文字配列パターン
  @ffi.Uint16()
  external int patternLayOutMethod;

  /// 回転原点
  external SSPoint_t grpRotateOrigine;

  static ffi.Pointer<embGrp_t> allocate() {
    final pointer = ffi.calloc<embGrp_t>();
    return pointer;
  }
}

final class embPtrn_t extends ffi.Struct {
  /// グループ内オフセット(中心の座標)
  external SSPoint_t inGrpOffset;

  /// サイズ(模様中心から上下左右のサイズ 0.1mm単位)
  external RectanArea_t size;

  /// 回転変換したマスク 模様の外接枠座標(0.1mm単位)
  external Rectangle_t rotatedMask;

  /// 実際に針落ち点が存在する座標の範囲
  external RectanArea_t packedMask;

  /// ミラー属性 true：ミラー状態 false：定常状態
  @ffi.Bool()
  external bool mirror;

  /// STB変換　true：する false：しない
  @ffi.Bool()
  external bool doSTB;

  /// 展開倍率ステップ(int16_t X, int16_t Y)
  external Magnitude_t magStep;

  /// 文字データの入力サイズは　magStep.Yを用いている
  /// 書体番号(文字データのみ）
  @ffi.Int16()
  external int fontNumber;

  /// 選択されているかどうか（文字データのみ）
  @ffi.Bool()
  external bool selected;

  /// ユニコード(文字データのみ）
  @ffi.Uint32()
  external int unicode;

  /// キルトサッシ表示用6角マスクのポイント数
  @ffi.Uint32()
  external int quiltRectanglePointNum;

  /// キルトサッシ表示用6角マスク格納バッファ
  @ffi.Array.multi([6])
  external ffi.Array<SSPoint_t> quiltRectanglePoint;

  /// ユーザーログ送信の呼び出し元の定義
  @ffi.Int16()
  external int dataSource;

  /// ユーザーログ模様の拡張子
  @ffi.Int16()
  external int embExtension;

  /// 拡大値
  @ffi.Int16()
  external int MagMax;

  /// デフォルト値
  @ffi.Int16()
  external int MagDefault;

  /// 縮小値
  @ffi.Int16()
  external int MagMin;

  static ffi.Pointer<embPtrn_t> allocate() {
    final pointer = ffi.calloc<embPtrn_t>();
    return pointer;
  }
}

final class embPtrnInfo_t extends ffi.Struct {
  external ffi.Pointer<embPtrn_t> embPattern;
  @ffi.Int32()
  external int patternNum;

  static ffi.Pointer<embPtrnInfo_t> allocate() {
    final pointer = ffi.calloc<embPtrnInfo_t>();
    return pointer;
  }
}

final class threadMarkState_t extends ffi.Struct {
  @ffi.Uint8()
  external int topLeft;
  @ffi.Uint8()
  external int top;
  @ffi.Uint8()
  external int topRight;
  @ffi.Uint8()
  external int left;

  @ffi.Uint8()
  external int right;

  @ffi.Uint8()
  external int bottomLeft;
  @ffi.Uint8()
  external int bottom;
  @ffi.Uint8()
  external int bottomRight;

  static ffi.Pointer<threadMarkState_t> allocate() {
    final pointer = ffi.calloc<threadMarkState_t>();
    pointer.ref.top = 0;
    pointer.ref.topLeft = 0;
    pointer.ref.topRight = 0;
    pointer.ref.left = 0;
    pointer.ref.right = 0;
    pointer.ref.bottomLeft = 0;
    pointer.ref.bottomRight = 0;
    pointer.ref.bottom = 0;
    return pointer;
  }
}

final class EmbColorGr_t extends ffi.Struct {
  /// 基本糸色コード *//* XP-UGK2 5-02 XDDP ２色糸色-1-2
  @ffi.Uint8()
  external int StdThreadCode;

  /// 糸色ブランド
  @ffi.Uint8()
  external int paletteBrand;

  /// 糸色コード *//* XP-UGK2 5-02 XDDP ２色糸色-1-2
  @ffi.Uint16()
  external int paletteCode;

  @ffi.Uint32()
  external int ColorRGB; /* 糸色（RGB） */

  static ffi.Pointer<EmbColorGr_t> allocate() {
    final pointer = ffi.calloc<EmbColorGr_t>();
    pointer.ref.StdThreadCode = 0;
    pointer.ref.paletteBrand = 0;
    pointer.ref.paletteCode = 0;
    pointer.ref.ColorRGB = 0;
    return pointer;
  }
}

/// グループの選択状態の操作方法
abstract class GroupSelectCondition_t {
  /// 選択状態にする
  static const int GROUP_SELECTED = 0;

  /// 未選択状態にする
  static const int GROUP_UNSELECTED = 1;

  /// 反転させる（未選択なら選択、選択なら未選択状態にする）
  static const int GROUP_INVERT = 2;
}

abstract class magnify_t {
  /// 100%, Reset
  static const int MAG100 = 0;

  /// 125%
  static const int MAG125 = 1;

  /// 150%
  static const int MAG150 = 2;

  /// 200%
  static const int MAG200 = 3;

  /// 400%
  static const int MAG400 = 4;
}

abstract class align_t {
  static const int ALIGN_LEFT = 0;
  static const int ALIGN_RIGHT = 1;
  static const int ALIGN_TOP = 2;
  static const int ALIGN_BOTTOM = 3;
  static const int ALIGN_CENTER_H = 4;
  static const int ALIGN_CENTER_V = 5;
}

final class embInfo_t extends ffi.Struct {
  //右回転角度 0.1度単位 0[0度]?3559[355.9度]
  @ffi.Int16()
  external int allAngle;

  //枠移動オフセット
  external SSPoint_t frameOffset;

  //  全体サイズ高さ
  @ffi.Uint16()
  external int height;

  //  全体サイズ幅
  @ffi.Uint16()
  external int wide;

  // 全体回転原点
  external SSPoint_t embRotateOrigine;

  static ffi.Pointer<embInfo_t> allocate() {
    final pointer = ffi.calloc<embInfo_t>();
    pointer.ref.allAngle = 0;
    pointer.ref.frameOffset.X = 0;
    pointer.ref.frameOffset.Y = 0;
    pointer.ref.height = 0;
    pointer.ref.wide = 0;
    pointer.ref.embRotateOrigine.X = 0;
    pointer.ref.embRotateOrigine.Y = 0;
    return pointer;
  }
}

final class EmbAttribParam_t extends ffi.Struct {
  /// 表示用の枠サイズ Emb_Frame_Type_t
  @ffi.Int32()
  external int dispFrameType;

  /// 装着されている枠サイズ FrameSize_t
  @ffi.Int32()
  external int frameSize;

  /// W/Wplus/Y押さえのいずれを表示するか？ PresserFoot_t
  @ffi.Int32()
  external int presserFoot;

  /// カメラFBのZoomのOn/Off
  @ffi.Bool()
  external bool isFbZoom;

  /// プログレッシブバーのアイコン状態を取得する
  @ffi.Bool()
  external bool isModeEmbSewingPopupSewingHelp;

  /// カメラペンUI投影ON/OFF/ONのDisable/OFFのDisable設定値
  @ffi.Int8()
  external int embCameraUiProjectionOnOffState;

  ///縫製中か？
  @ffi.Bool()
  external bool isEmbSewing;

  /// Embの編集モード ScrollCenterType_t
  @ffi.Int32()
  external int mode;

  static ffi.Pointer<EmbAttribParam_t> allocate() {
    final pointer = ffi.calloc<EmbAttribParam_t>();

    pointer.ref.dispFrameType = 0;
    pointer.ref.frameSize = 0;
    pointer.ref.presserFoot = 0;
    pointer.ref.isFbZoom = false;
    pointer.ref.isEmbSewing = false;

    return pointer;
  }
}

final class EmbSewingAttribParam_t extends ffi.Struct {
  /// 1色縫製メニューが有効か
  @ffi.Int32()
  external int nonStopSewState;

  /// 糸替えなし状態か？
  @ffi.Bool()
  external bool nonStopSew;

  /// カラーソートメニューが有効か
  @ffi.Int32()
  external int colorSortState;

  /// 色変えの状態か？
  @ffi.Bool()
  external bool colorSort;

  /// つなぎメニューが有効か
  @ffi.Int32()
  external int connectSewState;

  /// つなぎ状態か？
  @ffi.Bool()
  external bool connectSew;

  /// つなぎマークを表示するか？
  @ffi.Bool()
  external bool connectSewMark;

  /// スノーマン位置合わせメニューが有効か
  @ffi.Int32()
  external int snowmanState;

  /// しつけメニューが有効か
  @ffi.Int32()
  external int bastingState;

  /// しつけ状態か？
  @ffi.Bool()
  external bool basting;

  /// 回転メニューが有効か
  @ffi.Int32()
  external int rotateState;

  /// XPでは回転メニュー表示時に表示される値 10.0で割った値を表示する
  @ffi.Int32()
  external int rotAngle;

  /// 回転メニューが有効か
  @ffi.Int32()
  external int subRotateState;

  @ffi.Int32()
  external int subRotateResetState;

  @ffi.Int32()
  external int subSmallRotateState;

  /// 移動メニューが有効か
  @ffi.Int32()
  external int moveState;

  /// XPでは移動メニュー表示時に表示される値 10.0で割るとmmになる
  @ffi.Int32()
  external int disVertical;

  /// XPでは移動メニュー表示時に表示される値 10.0で割るとmmになる
  @ffi.Int32()
  external int disHorizon;

  /// サイズ変更や回転がキルトモードか？
  @ffi.Bool()
  external bool isQuiltExt;

  /// 移動メニューが有効か
  @ffi.Int32()
  external int subMoveState;

  /// スタート位置メニューが有効か
  @ffi.Int32()
  external int startPositionState;

  /// マスクトレースメニューが有効か
  @ffi.Int32()
  external int maskTraceState;

  /// 保存メニューが有効か
  @ffi.Int32()
  external int memoryState;

  /// 糸切設定ができるかどうか
  @ffi.Int32()
  external int threadCutState;

  /// 渡り糸カット設定ができるかどうか
  @ffi.Int32()
  external int jumpStitchTrimCutState;

  /// テンションの値 10.0で割って表示する
  @ffi.Int8()
  external int embTension;

  /// 糸切設定
  @ffi.Bool()
  external bool threadCut;

  /// 渡り糸カット設定
  @ffi.Bool()
  external bool jumpStitchTrim;

  /// 渡り糸カット設定　mm単位
  @ffi.Int8()
  external int jumpStitchLength;

  /// 縫い始めキーが有効か　通常用
  @ffi.Int32()
  external int startSewingState;

  /// 縫い始めが設定されているかどうか？　通常用
  @ffi.Bool()
  external bool startSewing;

  /// 9点設定キー　　通常、キルト兼用
  @ffi.Int32()
  external int startPositionMoveState;

  /// キルト用の縫い始めキーが有効か
  @ffi.Int32()
  external int startSewingQuiltState;

  /// キルトの場合はStartとEndの設定がある
  @ffi.Int32()
  external int startQuiltSewing;

  /// 六角キルトのコーナか　上の真ん中のと右の真ん中がグレーになる
  @ffi.Bool()
  external bool startPositionMovetQuiltHexagon;

  /// コーナ用か？　キルト用
  @ffi.Bool()
  external bool startPositionMovetQuiltConer;

  /// 選択されている通常模様の針落ち点位置 EmbTrialStartPosition_t
  @ffi.Int32()
  external int embSewingStartPosition;

  /// 選択されているキルトの針落ち点位置
  @ffi.Int32()
  external int embSewingQultStartPosition;

  /// お試しキー
  @ffi.Int32()
  external int trialAbleState;

  /// キルトつなぎのトータル数
  @ffi.Int16()
  external int QuiltExtCurrentNum;

  /// キルトつなぎの現在の値
  @ffi.Int16()
  external int QuiltExtTotalNum;

  /// 前のキルトに移動できるか？
  @ffi.Int32()
  external int quitShashesPartsLeftState;

  /// 後ろのキルトに移動できるか？
  @ffi.Int32()
  external int quitShashesPartsRightState;

  ///キルトの編集モード
  @ffi.Int32()
  external int quirEditMode;

  ///プロジェクター刺しゅう模様投影キーが有効か
  @ffi.Int32()
  external int projectorState;

  static ffi.Pointer<EmbSewingAttribParam_t> allocate() {
    final pointer = ffi.calloc<EmbSewingAttribParam_t>();
    pointer.ref.nonStopSewState = 0;
    pointer.ref.nonStopSew = false;
    pointer.ref.colorSortState = 0;
    pointer.ref.colorSort = false;
    pointer.ref.connectSewState = 0;
    pointer.ref.connectSew = false;
    pointer.ref.snowmanState = 0;
    pointer.ref.bastingState = 0;

    pointer.ref.basting = false;
    pointer.ref.rotateState = 0;
    pointer.ref.moveState = 0;

    pointer.ref.startPositionState = 0;
    pointer.ref.maskTraceState = 0;
    pointer.ref.memoryState = 0;

    pointer.ref.threadCutState = 0;
    pointer.ref.jumpStitchTrimCutState = 0;
    pointer.ref.embTension = 0;
    pointer.ref.threadCut = false;
    pointer.ref.jumpStitchTrim = false;
    pointer.ref.jumpStitchLength = 0;
    pointer.ref.startSewingState = 0;
    pointer.ref.startSewing = false;
    pointer.ref.startPositionMoveState = 0;
    pointer.ref.embSewingQultStartPosition = 0;

    pointer.ref.embSewingStartPosition = 0;
    pointer.ref.trialAbleState = 0;

    pointer.ref.startSewingQuiltState = 0;
    pointer.ref.subMoveState = 0;
    pointer.ref.startQuiltSewing = 0;
    pointer.ref.startPositionMovetQuiltHexagon = false;
    pointer.ref.startPositionMovetQuiltConer = false;
    pointer.ref.connectSewMark = false;
    pointer.ref.subRotateState = 0;
    pointer.ref.subRotateResetState = 0;
    pointer.ref.subSmallRotateState = 0;
    pointer.ref.quirEditMode = 6;

    pointer.ref.projectorState = 0;

    return pointer;
  }
}

/// 編集画面の機能の有効無効
final class EmbEditAttribParam_t extends ffi.Struct {
  /// アウトラインメニューが有効か
  @ffi.Int32()
  external int outlineState;

  /// 簡単ステップリングメニューが有効か
  @ffi.Int32()
  external int stipplingState;

  /// アライメントメニューが有効か
  @ffi.Int32()
  external int alignmentState;

  /// JudgeSeiretsuOK	JudgeSeiretsuNG
  @ffi.Int32()
  external int subAlignmentState;

  /// テキスト編集メニューが有効か
  @ffi.Int32()
  external int textEditState;

  /// アプリッケメニューが有効か
  @ffi.Int32()
  external int appliqueState;

  /// サブメニュー
  @ffi.Int32()
  external int subOldWappenState;

  ///サブメニュー テキスチャのON/OFF　編集ライブラリ側にしか情報がない
  @ffi.Int32()
  external int subNewWappenTextureState;

  /// 色変えメニューが有効か
  @ffi.Int32()
  external int changeColorState;

  /// 密度メニューが有効か
  @ffi.Int32()
  external int densityState;

  /// サブメニュー
  @ffi.Int32()
  external int subDensityState;

  /// ボーダメニューが有効か
  @ffi.Int32()
  external int borderState;

  /// サブメニュー 縦方向のカット
  @ffi.Int32()
  external int subCutVerticalState;

  /// サブメニュー 横方向のカット
  @ffi.Int32()
  external int subCutHorizontalState;

  /// サブメニュー
  @ffi.Int32()
  external int subBorderSelectedGroupState;

  /// 反転メニューが有効か
  @ffi.Int32()
  external int editFlipState;

  /// 反転しているか？
  @ffi.Bool()
  external bool editFlip;

  /// コピーメニューが有効か
  @ffi.Int32()
  external int copyState;

  /// グループメニューが有効か
  @ffi.Int32()
  external int groupState;

  /// グループ化されているか？
  @ffi.Bool()
  external bool group;

  /// 回転メニューが有効か
  @ffi.Int32()
  external int rotateState;

  /// XPでは回転メニュー表示時に表示される値 10.0で割った値を表示する
  @ffi.Int32()
  external int rotAngle;

  /// SmallRotateIsOK	SmallRotateIsNG
  @ffi.Int32()
  external int subSmallRotateState;

  /// サイズメニューが有効か
  @ffi.Int32()
  external int sizeState;

  /// XPではサイズメニュー表示時に表示される値 10.0で割るとmmになる
  @ffi.Int32()
  external int sizeWidth;

  /// XPではサイズメニュー表示時に表示される値 10.0で割るとmmになる
  @ffi.Int32()
  external int sizeHeight;

  /// サイズ計算中
  @ffi.Bool()
  external bool sizeRunCalc;

  /// サブメニュー
  @ffi.Int32()
  external int subMagnificationState;

  /// サブメニュー 通常拡大が有効か
  @ffi.Int32()
  external int subBlockState;

  /// サブメニュー STBモードが有効か
  @ffi.Int32()
  external int subSTBState;

  /// 移動メニューが有効か
  @ffi.Int32()
  external int moveState;

  /// XPでは移動メニュー表示時に表示される値 10.0で割るとmmになる
  @ffi.Int32()
  external int disVertical;

  /// XPでは移動メニュー表示時に表示される値 10.0で割るとmmになる
  @ffi.Int32()
  external int disHorizon;

  /// 入れ替えメニューが有効か
  @ffi.Int32()
  external int orderState;

  /// サブメニュー　後方移動が可能か？
  @ffi.Int32()
  external int subBackOrderState;

  /// サブメニュー　前方移動が可能か？
  @ffi.Int32()
  external int subFrontOrderState;

  /// 縫わないメニューが有効か
  @ffi.Int32()
  external int notSewingState;

  /// 追加メニューが有効か
  @ffi.Int32()
  external int addState;

  /// 削除メニューが有効か
  @ffi.Int32()
  external int deleteState;

  /// 選択メニューが有効か
  @ffi.Int32()
  external int selectState;

  /// 選択中か？
  @ffi.Bool()
  external bool select;

  /// 保存メニューが有効か
  @ffi.Int32()
  external int memoryState;

  /// 刺繍メニューが有効か
  @ffi.Int32()
  external int embroideryState;

  /// Embroideryボタンかつなぎボタンか？　つなぎボタンの場合、true
  @ffi.Bool()
  external bool isEmbroideryConnect;

  /// 複数メニューが有効か
  @ffi.Int32()
  external int multipleSelectionState;

  /// プレビューが有効か
  @ffi.Int32()
  external int previewState;

  /// サブメニューで表示される模様切り替え
  @ffi.Int32()
  external int subEditGroupSelectNext;

  /// プロジェクター刺しゅう模様投影キーが有効か
  @ffi.Int32()
  external int projectorState;

  static ffi.Pointer<EmbEditAttribParam_t> allocate() {
    final pointer = ffi.calloc<EmbEditAttribParam_t>();

    pointer.ref.outlineState = 0;
    pointer.ref.stipplingState = 0;
    pointer.ref.alignmentState = 0;
    pointer.ref.textEditState = 0;
    pointer.ref.appliqueState = 0;
    pointer.ref.changeColorState = 0;
    pointer.ref.densityState = 0;
    pointer.ref.borderState = 0;
    pointer.ref.editFlipState = 0;
    pointer.ref.editFlip = false;
    pointer.ref.copyState = 0;
    pointer.ref.groupState = 0;
    pointer.ref.group = false;
    pointer.ref.rotateState = 0;
    pointer.ref.sizeState = 0;
    pointer.ref.moveState = 0;
    pointer.ref.orderState = 0;
    pointer.ref.notSewingState = 0;
    pointer.ref.addState = 0;
    pointer.ref.deleteState = 0;
    pointer.ref.selectState = 0;
    pointer.ref.select = false;
    pointer.ref.memoryState = 0;
    pointer.ref.embroideryState = 0;
    pointer.ref.multipleSelectionState = 0;

    pointer.ref.subEditGroupSelectNext = 0;
    pointer.ref.previewState = 0;
    pointer.ref.subBackOrderState = 0;
    pointer.ref.subFrontOrderState = 0;
    pointer.ref.subMagnificationState = 0;

    pointer.ref.subBlockState = 0;
    pointer.ref.subSTBState = 0;
    pointer.ref.subSmallRotateState = 0;
    pointer.ref.subCutVerticalState = 0;
    pointer.ref.subCutHorizontalState = 0;
    pointer.ref.subBorderSelectedGroupState = 0;
    pointer.ref.subDensityState = 0;
    pointer.ref.subOldWappenState = 0;
    pointer.ref.subNewWappenTextureState = 0;
    pointer.ref.subAlignmentState = 0;

    pointer.ref.projectorState = 0;

    return pointer;
  }
}

/// つなぎ 画像表示用構造体
final class EmbMarkPatImgDrawParam_t extends ffi.Struct {
  @ffi.Double()
  external double angle;

  @ffi.Double()
  external double x1;

  @ffi.Double()
  external double y1;

  @ffi.Double()
  external double x2;

  @ffi.Double()
  external double y2;

  @ffi.Double()
  external double offsetX;

  @ffi.Double()
  external double offsetY;

  @ffi.Double()
  external double scale;

  @ffi.Int32()
  external int bPale;

  external RectanArea_t rect;

  @ffi.Int32()
  external int draw;

  static ffi.Pointer<EmbMarkPatImgDrawParam_t> allocate() {
    final pointer = ffi.calloc<EmbMarkPatImgDrawParam_t>();
    pointer.ref.angle = 0.0;
    pointer.ref.x1 = 0.0;
    pointer.ref.y1 = 0.0;
    pointer.ref.x2 = 0.0;
    pointer.ref.y2 = 0.0;
    pointer.ref.offsetX = 0.0;
    pointer.ref.offsetY = 0.0;
    pointer.ref.scale = 0.0;
    pointer.ref.bPale = 0;
    pointer.ref.rect.set();
    pointer.ref.draw = 0;
    return pointer;
  }
}

typedef EmbMarkPatImgDrawParam = EmbMarkPatImgDrawParam_t;

/// つなぎ スノーマンマーク表示用構造体
final class EmbMarkPatSnowmanDrawParam_t extends ffi.Struct {
  @ffi.Double()
  external double x;

  @ffi.Double()
  external double y;

  @ffi.Double()
  external double angleMark;

  @ffi.Double()
  external double offsetX;

  @ffi.Double()
  external double offsetY;

  @ffi.Double()
  external double scale;

  static ffi.Pointer<EmbMarkPatSnowmanDrawParam_t> allocate() {
    final pointer = ffi.calloc<EmbMarkPatSnowmanDrawParam_t>();
    pointer.ref.x = 0.0;
    pointer.ref.y = 0.0;
    pointer.ref.angleMark = 0.0;
    pointer.ref.offsetX = 0.0;
    pointer.ref.offsetY = 0.0;
    pointer.ref.scale = 0.0;
    return pointer;
  }
}

typedef EmbMarkPatSnowmanDrawParam = EmbMarkPatSnowmanDrawParam_t;

final class embSewingInfo_t extends ffi.Struct {
  @ffi.Int32()
  external int width;

  @ffi.Int32()
  external int height;

  /// 現在の針数
  @ffi.Uint32()
  external int needleCurrent;

  /// 全針数
  @ffi.Uint32()
  external int needleAll;

  /// 縫っている色の場所
  @ffi.Uint32()
  external int threadCurrent;

  /// 全色数
  @ffi.Uint32()
  external int threadAll;

  @ffi.Uint32()
  external int sewTimeCurrent;

  @ffi.Uint32()
  external int sewTimeAll;

  @ffi.Int32()
  external int positionX;

  @ffi.Int32()
  external int positionY;

  @ffi.Int32()
  external int angle;

  static ffi.Pointer<embSewingInfo_t> allocate() {
    final pointer = ffi.calloc<embSewingInfo_t>();
    pointer.ref.width = 0;
    pointer.ref.height = 0;
    pointer.ref.needleCurrent = 0;
    pointer.ref.needleAll = 0;
    pointer.ref.threadCurrent = 0;
    pointer.ref.threadAll = 0;
    pointer.ref.sewTimeCurrent = 0;
    pointer.ref.sewTimeAll = 0;
    pointer.ref.positionX = 0;
    pointer.ref.positionY = 0;
    pointer.ref.angle = 0;
    return pointer;
  }

  static ffi.Pointer<ffi.Pointer<embSewingInfo_t>> allocate_p() {
    final pointer = ffi.calloc<ffi.Pointer<embSewingInfo_t>>();
    return pointer;
  }
}

final class threadInfo_t extends ffi.Struct {
  /// 針数
  @ffi.Uint32()
  external int stitchNumber;

  /// 縫製時間
  @ffi.Uint32()
  external int sewingTime;

  /// 糸色のR･G･B値
  @ffi.Uint32()
  external int colorRGB;

  /// 糸色コード
  @ffi.Uint32()
  external int threadCode;

  /// 300色index
  @ffi.Uint32()
  external int index300;

  /// 糸色コード桁数
  @ffi.Uint32()
  external int threadCodeDigit;

  /// ブランドコード
  @ffi.Uint32()
  external int brandCode;

  /// 標準コード
  @ffi.Uint32()
  external int stdCode;

  /// 縫わない
  @ffi.Bool()
  external bool notSewing;

  ///
  external MemHandle_t groupH;

  ///
  external MemHandle_t patternH;

  /// 何色目か
  @ffi.Uint32()
  external int colIdx;

  static ffi.Pointer<threadInfo_t> allocate() {
    final pointer = ffi.calloc<threadInfo_t>();
    pointer.ref.stitchNumber = 0;
    pointer.ref.sewingTime = 0;
    pointer.ref.colorRGB = 0;
    pointer.ref.threadCode = 0;
    pointer.ref.index300 = 0;
    pointer.ref.threadCodeDigit = 0;
    pointer.ref.brandCode = 0;
    pointer.ref.stdCode = 0;
    pointer.ref.notSewing = false;
    pointer.ref.colIdx = 0;
    return pointer;
  }

  static ffi.Pointer<ffi.Pointer<threadInfo_t>> allocate_p() {
    final pointer = ffi.calloc<ffi.Pointer<threadInfo_t>>();
    return pointer;
  }
}

final class borderInfo_t extends ffi.Struct {
  /// 形状　XサイズとYサイズ
  external SSPoint_t style;

  ///ボーダー全体の上下左右の位置(サイズは上下、左右の差)
  external RectanArea_t mask;

  /// 模様間
  external SSPoint_t space;

  /// 回転するときに必要な中心値（ただし、親の中心値)
  external SSPoint_t position;

  /// ボーダーの親となるグループのハンドル
  external MemHandle_t parentGroup;

  /// 回転角度
  @ffi.Uint16()
  external int angle;

  /// 1ボーダーに含まれるグループ数
  @ffi.Uint16()
  external int groupNum;

  static ffi.Pointer<borderInfo_t> allocate() {
    final pointer = ffi.calloc<borderInfo_t>();
    pointer.ref.style.set();
    pointer.ref.mask.set();
    pointer.ref.space.set();
    pointer.ref.position.set();
    //pointer.ref.parentGroup;
    pointer.ref.angle = 0;
    pointer.ref.groupNum = 0;
    return pointer;
  }
}

final class baseColor_t extends ffi.Struct {
  /// 0:SHUFFLE_VALID 1:invalid
  @ffi.Uint8()
  external int valid;

  /// ThreadBrandName_t
  @ffi.Uint8()
  external int brandCode;

  @ffi.Uint16()
  external int tableIdx;

  void set() {
    valid = 0;
    brandCode = 0;
    tableIdx = 0;
  }
}

final class shuffleBaseColor_t extends ffi.Struct {
  /// MANUAL_SHUFFLE, AUTO_SHUFFLE
  @ffi.Bool()
  external bool mode;

  @ffi.Array.multi([6])
  external ffi.Array<baseColor_t> color;

  static ffi.Pointer<shuffleBaseColor_t> allocate() {
    final pointer = ffi.calloc<shuffleBaseColor_t>();
    pointer.ref.mode = false;
    for (int i = 0; i < 6; i++) {
      pointer.ref.color[i].set();
    }
    return pointer;
  }
}

final class appliquePartsParam_t extends ffi.Struct {
  @ffi.Int32()
  external int outlineType;

  @ffi.Int32()
  external int satinType;

  @ffi.Int32()
  external int runType;

  @ffi.Int32()
  external int stitchWidth;

  @ffi.Int32()
  external int stitchDensity;

  @ffi.Int32()
  external int distance;

  @ffi.Bool()
  external bool texture;

  static ffi.Pointer<appliquePartsParam_t> allocate() {
    final pointer = ffi.calloc<appliquePartsParam_t>();
    pointer.ref.outlineType = 0;
    pointer.ref.satinType = 0;
    pointer.ref.runType = 0;
    pointer.ref.stitchWidth = 0;
    pointer.ref.stitchDensity = 0;
    pointer.ref.distance = 0;
    pointer.ref.texture = false;
    return pointer;
  }
}

final class length_t extends ffi.Struct {
  /// 横方向長さ
  @ffi.Int32()
  external int x;

  /// 縦方向長さ
  @ffi.Int32()
  external int y;

  void set() {
    this.x = 0;
    this.y = 0;
  }
}

final class position_t extends ffi.Struct {
  @ffi.Int32()
  external int x;

  @ffi.Int32()
  external int y;

  void set() {
    this.x = 0;
    this.y = 0;
  }
}

final class savedFileInfo_t extends ffi.Struct {
  @ffi.Int32()
  external int partsNum;

  @ffi.Int32()
  external int allParts;

  @ffi.Int32()
  external int partsX;

  @ffi.Int32()
  external int partsY;

  @ffi.Int32()
  external int kind;

  external length_t thumbnailSize;

  external length_t maskSize;

  external position_t partsPosition;

  static ffi.Pointer<savedFileInfo_t> allocate() {
    final pointer = ffi.calloc<savedFileInfo_t>();
    pointer.ref.partsNum = 0;
    pointer.ref.allParts = 0;
    pointer.ref.kind = 0;
    pointer.ref.partsX = 0;
    pointer.ref.partsY = 0;
    pointer.ref.thumbnailSize.set();
    pointer.ref.maskSize.set();
    pointer.ref.partsPosition.set();
    return pointer;
  }
}

final class realImg_t extends ffi.Struct {
  @ffi.Int32()
  external int x;

  @ffi.Int32()
  external int y;

  @ffi.Uint32()
  external int pixRGB;

  static ffi.Pointer<realImg_t> allocate() {
    final pointer = ffi.calloc<realImg_t>();
    pointer.ref.x = 0;
    pointer.ref.y = 0;
    pointer.ref.pixRGB = 0;
    return pointer;
  }

  static ffi.Pointer<ffi.Pointer<realImg_t>> allocate_p() {
    final pointer = ffi.calloc<ffi.Pointer<realImg_t>>();
    return pointer;
  }
}

final class realImageInfo_t extends ffi.Struct {
  external embImg_t realImage;

  external ffi.Pointer<realImg_t> pix;

  @ffi.Int32()
  external int pixNum;

  static ffi.Pointer<realImageInfo_t> allocate() {
    final pointer = ffi.calloc<realImageInfo_t>();
    pointer.ref.pix = realImg_t.allocate();
    pointer.ref.pixNum = 0;
    return pointer;
  }
}

final class embImageInfo_t extends ffi.Struct {
  external ffi.Pointer<embImg_t> embImage;

  @ffi.Int32()
  external int imageNum;

  static ffi.Pointer<embImageInfo_t> allocate() {
    final pointer = ffi.calloc<embImageInfo_t>();
    pointer.ref.embImage = embImg_t.allocate();
    pointer.ref.imageNum = 0;
    return pointer;
  }

  static ffi.Pointer<ffi.Pointer<embImageInfo_t>> allocate_p() {
    final pointer = ffi.calloc<ffi.Pointer<embImageInfo_t>>();
    return pointer;
  }
}

final class embstitchPos_t extends ffi.Union {
  external SSPoint_t pos;

  /// 現状RGBデータしかない
  @ffi.Uint32()
  external int code;

  static ffi.Pointer<embstitchPos_t> allocate() {
    final pointer = ffi.calloc<embstitchPos_t>();
    pointer.ref.pos.X = 0;
    pointer.ref.pos.X = 0;
    pointer.ref.code = 0;
    return pointer;
  }

  static ffi.Pointer<ffi.Pointer<embstitchPos_t>> allocate_p() {
    final pointer = ffi.calloc<ffi.Pointer<embstitchPos_t>>();
    return pointer;
  }
}

final class edgeToEdgeInfo_t extends ffi.Struct {
  @ffi.Uint32()
  external int piece;

  @ffi.Uint32()
  external int row;

  /// 1 == 0.1mm
  @ffi.Uint32()
  external int heightMM;

  /// 1 == 0.1mm
  @ffi.Uint32()
  external int widthMM;

  static ffi.Pointer<edgeToEdgeInfo_t> allocate() {
    final pointer = ffi.calloc<edgeToEdgeInfo_t>();
    pointer.ref.piece = 0;
    pointer.ref.row = 0;
    pointer.ref.heightMM = 0;
    pointer.ref.widthMM = 0;
    return pointer;
  }
}

/// カメラスキャンのデータ
final class CameraScanInfoAndData_t extends ffi.Struct {
  /// Infoサイズ
  @ffi.Int32()
  external int infoSize;

  /// Info
  external ffi.Pointer<ffi.Uint8> info;

  /// Dataサイズ
  @ffi.Int32()
  external int dataSize;

  /// Data
  external ffi.Pointer<ffi.Uint8> data;

  static ffi.Pointer<CameraScanInfoAndData_t> allocate() {
    final pointer = ffi.calloc<CameraScanInfoAndData_t>();
    pointer.ref.infoSize = 0;
    pointer.ref.info = ffi.calloc<ffi.Uint8>();
    pointer.ref.info.value = 0;
    pointer.ref.dataSize = 0;
    pointer.ref.data = ffi.calloc<ffi.Uint8>();
    pointer.ref.data.value = 0;
    return pointer;
  }
}

/// 刺繍つなぎ予約設定の実行結果定義
abstract class EmbSewPatCnctRsrvResult_t {
  /// つなぎ予約済み
  static const int EMB_SEW_PAT_CNCT_RSRV_SETTING = 0;

  /// つなぎ予約解除
  static const int EMB_SEW_PAT_CNCT_RSRV_CANCEL = 1;

  /// つなぎ予約変更不可
  static const int EMB_SEW_PAT_CNCT_RSRV_NOT_CHANGE = 2;
}

/// カメラスキャンのデータ種別
abstract class CameraScanDataKind_t {
  /// 刺繍背景スキャンデータ
  static const int cameraScanDataKind_Embroidery = 0;

  /// スノーマン認識データ
  static const int cameraScanDataKind_Snowman = 1;

  /// MDC背景スキャンデータ
  static const int cameraScanDataKind_MDC = 2;

  /// MDC選択画像データ
  static const int cameraScanDataKind_MDCSelect = 3;
}

/// カメラスキャン画像の表示スケール指定
abstract class CameraScanImageScale_t {
  /// 刺繍イメージエリア
  static const int cameraScanImageScale_Embroidery = 0;

  /// 刺繍文字編集イメージエリア
  static const int cameraScanImageScale_Embroidery_CharacterEdit = 1;

  /// MDCイメージエリア
  static const int cameraScanImageScale_MDC = 2;

  /// リアルプレビュー
  static const int cameraScanImageScale_RealPreview = 3;

  /// リアルプレビュー拡大
  static const int cameraScanImageScale_RealPreview_Large = 4;
}

abstract class addDelBorder_t {
  static const int ADD_BORDER = 0;
  static const int DEL_BORDER = 1;
}

abstract class borderPosition_t {
  static const int TOP_BORDER = 0;
  static const int BOTTOM_BORDER = 1;
  static const int LEFT_BORDER = 2;
  static const int RIGHT_BORDER = 3;
}

abstract class divBorder_t {
  static const int HORIZONTAL_BORDER = 0;
  static const int VERTICAL_BORDER = 1;
}

abstract class threadMark_t {
  static const int RESET_MARK = 0;
  static const int SET_MARK = 1;
}

abstract class threadPosition_t {
  static const int TOP = 0;
  static const int TOP_LEFT = 1;
  static const int TOP_RIGHT = 2;
  static const int CENTER_LEFT = 3;
  static const int CENTER_RIGHT = 4;
  static const int BOTTOM_LEFT = 5;
  static const int BOTTOM_RIGHT = 6;
  static const int BOTTOM = 7;
  static const int ALL = 8;
}

abstract class quiltSplitType_t {
  static const int SPLIT4 = 0;
  static const int ONE_WAY = 1;
  static const int HEXAGON = 2;
  static const int EDGE_TO_EDGE = 3;
}

abstract class scalingQuilt_t {
  /// キルトつなぎサイズ拡縮：X方向拡大
  static const int scalingQuilt_X_Magnification = 0;

  /// キルトつなぎサイズ拡縮：X方向縮小
  static const int scalingQuilt_X_Reduction = 1;

  /// キルトつなぎサイズ拡縮：Y方向拡大
  static const int scalingQuilt_Y_Magnification = 2;

  /// キルトつなぎサイズ拡縮：Y方向縮小
  static const int scalingQuilt_Y_Reduction = 3;
}

abstract class startPointQuilt_t {
  /// キルトつなぎ始点位置：始点
  static const int startPointQuilt_Start = 0;

  /// キルトつなぎ始点位置：終点
  static const int startPointQuilt_End = 1;

  /// キルトつなぎ始点位置：左上
  static const int startPointQuilt_UpLeft = 2;

  /// キルトつなぎ始点位置：上
  static const int startPointQuilt_Up = 3;

  /// キルトつなぎ始点位置：右上
  static const int startPointQuilt_UpRight = 4;

  /// キルトつなぎ始点位置：左
  static const int startPointQuilt_Left = 5;

  /// キルトつなぎ始点位置：右
  static const int startPointQuilt_Right = 6;

  /// キルトつなぎ始点位置：左下
  static const int startPointQuilt_DownLeft = 7;

  /// キルトつなぎ始点位置：下
  static const int startPointQuilt_Down = 8;

  /// キルトつなぎ始点位置：右下
  static const int startPointQuilt_DownRight = 9;

  /// キルトつなぎ始点位置：コーナー左側
  static const int startPointQuilt_CornerEntrance = 10;

  /// キルトつなぎ始点位置：コーナー内側
  static const int startPointQuilt_CornerCenter = 11;

  /// キルトつなぎ始点位置：コーナー下側
  static const int startPointQuilt_CornerExit = 12;

  /// (0,0)座標　セットには使わない
  static const int startPointQuilt_Base = 13;
}

abstract class selectPartsQuilt_t {
  /// キルトつなぎパーツ選択：次パーツ
  static const int selectPartsQuilt_Next = 0;

  /// キルトつなぎパーツ選択：前パーツ
  static const int selectPartsQuilt_Previous = 1;
}

/// ブランド名
abstract class ThreadBrandName_t {
  /// 0
  static const int BRAND_NAME_COUNTRY = 0;

  /// 1
  static const int BRAND_NAME_EMBROIDERY = 1;

  /// 2
  static const int BRAND_NAME_GUTERMANN = 2;

  /// 3
  static const int BRAND_NAME_ISACORD = 3;

  /// 4
  static const int BRAND_NAME_MA_POLY = 4;

  /// 5
  static const int BRAND_NAME_MA_RAYON = 5;

  /// 6
  static const int BRAND_NAME_RA_POLY = 6;

  /// 7
  static const int BRAND_NAME_RA_RAYON = 7;

  /// 8
  static const int BRAND_NAME_SU_RAYON = 8;

  /// 9	未使用
  static const int BRAND_NAME_MADEIRA = 9;

  /// 10	未使用
  static const int BRAND_NAME_SULKY = 10;

  /// 11
  static const int BRAND_NAME_FLESH = 11;

  /// 12
  static const int BRAND_NAME_COUNTRY_CUSTOM = 12;

  /// 13
  static const int BRAND_NAME_EMBROIDERY_CUSTOM = 13;

  /// 14
  static const int BRAND_NAME_UNKNOWN = 14;

  /// 15
  static const int BRAND_NAME_STANDARD = 15;

  /// 16	/* XPV300M0068対応：MOD 2020/03/23 T.Maehashi */
  static const int BRAND_NAME_PACESETTER_PRO = 16;

  /// 17
  static const int BRAND_NAME_POLYFAST = 17;

  /// 18
  static const int BRAND_NAME_IRIS = 18;

  /// 19
  static const int BRAND_NAME_FLORIANI = 19;
  static const int BRAND_NAME_LIST_MAX = 20;
  static const int BRAND_NAME_ORIGINAL = 200;
  static const int BRAND_NAME_AP_MATERIAL = 250;
  static const int BRAND_NAME_AP_POSITION = 251;
  static const int BRAND_NAME_AP_APPLIQUE = 252;
  static const int BRAND_NAME_NOT_DEFINED = 253;
  static const int BRAND_NAME_USER_DEFINED = 254;

  /// 不正
  static const int BRAND_NAME_INVALID = 255;
}

abstract class EmbTrialStartPosition_t {
  /// 左上
  static const int EMB_TRIAL_SP_LEFT_UP = 0;

  /// 上
  static const int EMB_TRIAL_SP_UP = 1;

  /// 右上
  static const int EMB_TRIAL_SP_RIGHT_UP = 2;

  /// 左
  static const int EMB_TRIAL_SP_LEFT = 3;

  /// センター
  static const int EMB_TRIAL_SP_CENTER = 4;

  /// 右
  static const int EMB_TRIAL_SP_RIGHT = 5;

  /// 左下
  static const int EMB_TRIAL_SP_LEFT_DOWN = 6;

  /// 下
  static const int EMB_TRIAL_SP_DOWN = 7;

  /// 右下
  static const int EMB_TRIAL_SP_RIGHT_DOWN = 8;

  /// 左コーナー位置
  static const int EMB_TRIAL_SP_CORNER_ENTRANCE = 9;

  /// コーナー内側位置
  static const int EMB_TRIAL_SP_CORNER_CENTER = 10;

  /// 下コーナー側位置
  static const int EMB_TRIAL_SP_CORNER_EXIT = 11;

  /// 針位置
  static const int EMB_TRIAL_SP_NEEDLE_POS = 12;
}

enum embPatAutoKind_t {
  EMB_PATAUTOKIND_QUILTEDGE,
  /*   EdgeToEdgeキルトサッシ*/
  EMB_PATAUTOKIND_QUILTEXTENSION,
  /*  四角 1方向キルトサッシ*/
  EMB_PATAUTOKIND_QUILTEXTENSION_POLYGON,
  /*  六角 1方向キルトサッシ*/
  EMB_PATAUTOKIND_QUILTSASHES,
  /*4分割キルトサッシ*/
  EMB_PATAUTOKIND_CUSTOMDESIGN,
  /*大型分割*/
  EMB_PATAUTOKIND_NONE, /*   選択なし*/
}

enum embErrorCode_t{
  EMB_NO_ERR,			//エラーなし
//システムエラー　復帰不可
  EMB_GRPHANDLE_ERR,			//グループハンドルエラー
  EMB_PATTERNHANDLE_ERR,		//パターンハンドルエラー
  EMB_BORDERHANDLE_ERR,		//ボーダーハンドルエラー
  EMB_EMBNODAT_ERR,			//編集対象とするデータが存在しない
  EMB_FILEOBJ_ERR,			//ファイルオブジェクト生成エラー
  EMB_EMBDATGET_ERR,			//刺繍データ取得エラー
  EMB_QUILTPARAM_ERR,			//キルトパラメータ初期化エラー
  EMB_QUILTIMGGET_ERR,		//キルトイメージ取得エラー
  EMB_FILEIMGGET_ERR,			//ファイルイメージ取得エラー
  EMB_RGBCONVERT_ERR,			//RGB変換エラー
  EMB_PARTSCOLOR_ERR,			//パーツ色変更エラー
  EMB_ALIGNMENT_ERR,			//アライメント指定エラー
//システムエラー　復帰可
  EMB_MEMGET_ERR,				//メモリ取得エラー
  EMB_NEWGROP_ERR,			//新グループ作成エラー
  EMB_NEWPATTERN_ERR,			//新パターン作成エラー
//ユーザー操作エラー　復帰不可
//ユーザー操作エラー　復帰可
  EMB_MEMOVER_ERR,			//メモリオーバーエラー ERR_EMB_TOO_MUCH_SELECTED
  EMB_MEMORYFULL_ERR,			//保存先メモリフル ERR_DATA_MOMORY_FULL ERR_FLASH_MEMORY_FULL
  EMB_DATAOVER_ERR,			//刺繍メモリフル
  EMB_NOSELECT_ERR,			//データが非選択状態
  EMB_AREA_OVER_ERR,			//エリアオーバーエラー
  EMB_FRAMESIZE_ERR,			//フレームサイズオーバーエラー
  EMB_FLAMENOTUSE_ERR,		//この枠は使えませんエラー
  EMB_THREADCOLINFO_ERR,		//糸色情報エラー
  EMB_THREADCOLGET_ERR,		//糸色情報取得エラー
  EMB_THREADBRAND_ERR,		//糸ブランドエラー
  EMB_THREADCOLORMAX_ERR,		//糸色数オーバーエラー
  EMB_COLORPALETTE_ERR,		//カラーパレットエラー
  EMB_MOVGRP_ERR,				//グループ移動エラー
  EMB_MOVMULGRP_ERR,			//複数グループ移動エラー
  EMB_MOVBORDER_ERR,			//ボーダー移動エラー
  EMB_ANGLEOVER_ERR,			//角度オーバーエラー
  EMB_ROTATE_ERR,				//回転エラー
  EMB_ROTATEBORDER_ERR,		//ボーダー回転エラー
  EMB_CHANGESIZE_ERR,			//サイズ変更エラー
  EMB_CONVERTSTB_ERR,			//STB変換エラー
  EMB_MIRROR_ERR,				//左右反転エラー
  EMB_MULDUP_ERR,				//複数複製エラー
  EMB_REDSQUAREOVER_ERR,		//赤枠オーバー
  EMB_BORDERINIT_ERR,			//ボーダー初期化エラー
  EMB_BORDER_ERR,				//ボーダー作成/削除エラー INVALIDと同等
  EMB_BORDERDIRECT_ERR,		//ボーダー方向エラー
  EMB_BORDER_SETTING,			//ボーダー設定中
  EMB_BORDERMARK_SETTING,		//ボーダー糸印設定中
  EMB_APPLIQUE_ERR,			//アップリケ作成エラー
  EMB_APPLIQUEPARTS_ERR,		//アップリケパーツ作成エラー
  EMB_DISTANCE_ERR,			//模様間距離エラー ERR_APPLIQUE_NG_DISTANCE
  EMP_SPACE_ERR,				//間隔エラー
  EMB_SELECTOVER_ERR,			//選択数オーバーエラー
  EMB_UNUSABLEPATTERN_ERR,	//ERR_CANNOT_USE_SPECIAL_PATTERN 使用不可模様
  EMB_CHANGEORDER_ERR,		//並びを変更できない
  EMB_SHUFFLEOVER_ERR,		//シャッフル回数オーバー
  EMB_INVALID_ERR,			// キー無効(無効音)
  EMB_INVALID_ERR_PANEL,      //アプリのエラーをPanelに通知した状態のエラー
  EMB_INVALIDTHUMBNAIL_ERR,	//サムネイル指定間違い
  EMB_INVALIDQUILT_ERR,		//キルト種別の異常指定
  EMB_INVALIDVALUE_ERR,		//入力値エラー
  EMB_INVALIDPARAM_ERR,		//入力パラメータエラー
  EMB_INVALIDFILETYPE_ERR,	//ファイルタイプエラー
  EMB_INVALIDFILE_ERR,		//ファイル異常 ERR_FD_ERROR
  EMB_FILEOPEN_ERR,			//ファイルオープンエラー
  EMB_FILEREAD_ERR,			//ファイルリードエラー
  EMB_FILEWRITE_ERR,			//ファイルライトエラー
  EMB_UNDOREDOFILE_ERR,		//Undo/Redoファイルエラー
  EMB_TOOCOMPLEX_ERR,			//ERR_THIS_PATTERN_TOO_COMPLEX 複雑すぎエラー
  EMB_NOMORECOMBINATION_ERR,	//ERR_NO_MORE_SELECT 組み合わせ不可
  EMB_NOCOLORNAME_ERR,		//糸色名なし
  EMB_FILEPARTS_ERR,			//ファイルパーツ情報エラー
  EMB_MAKEOUTLINE_ERR,		//アウトライン作成エラー
  EMB_OVERLAP_ERR,			//ERR_APPLIQUE_NG_EX_IN_OVERLAP 内側アウトラインと外側のアウトラインが重なる
  EMB_MDCEDIT_ERR,			//MDCデータから編集用データの作成に失敗
  EMB_NEWLINE_ERR,			//改行できない
  EMB_CANNOTINPUT_ERR,		//これ以上入力できない
  EMB_CNG_PF1_OK_ERR,			// PF1に変換できる旧フォーマットの文字あり
  EMB_CNG_PF1_NG_ERR,         // PF1に変換できない旧フォーマットの文字あり
  EMB_NEEDL_DOWN_ERR,			//針下エラー
  EMB_NEEDL_DATA_NON_ERR,		//1針データなしエラー
  EMB_COLOR_SORT_ERR,			// カラーソート失敗
  EMB_CURSOR_ERR,				// カーソル移動不可（先頭か末尾）　
  EMB_OTHER_ERR,				//その他のエラー

  EMB_CHAR_ROTATE90_ERR,		//  文字入力時９０度回転しますかエラー
  EMB_CHAR_NO_MORE_IPT_ERR,	// これ以上入力できない（配列オーバー）
  EMB_OSAE_DOWN_ERR,			//押さえ下エラー
  EMB_SEW_ESC_UNIT_ERR,		//縫製-枠退避済エラー
  EMB_PATTERN_EXCEEDED,		//ERR_EMB_PATTERN_EXCEEDED 枠をはみ出します。
  EMB_TOO_MUCH_SELECTED,		//ERR_EMB_TOO_MUCH_SELECTED データ容量の制限を越えました、選べません(刺繍メモリフル)
  EMB_MCD_NOT_EXCHANGE_AREA_OVER,//ERR_MCD_NOT_EXCHANGE_AREA_OVER  最大縫製エリアからはみ出るため、変換できませんでした。
  EMB_APPLIQUE_NG_MEM_OVER, 	//ERR_APPLIQUE_NG_MEM_OVER 量が不足しているので、保存できません。
  EMB_APPLIQUE_NG_COMPLEX,	//ERR_APPLIQUE_NG_COMPLEX 複雑な形状または適さない形状のため、アップリケラインを生成できませんでした。
  //警告
  EMB_CURDELETEBORDER_WARN,	//ERR_CUR_DELETE_BORDER_OK(ボーダー模様の組み合わせが解除されます)
  EMB_DELBORDERMARK_WARN,		//ERR_DELETE_BORDER_MARK_OK_WAPPEN(糸印が消えます)
  EMB_APPLIQUE_TEXTURE_WARN,	//ERR_APPLIQUE_SOME_PARTS_NOT_TEXTURE(データ構造上、テクスチャを表示できません)
  EMB_ALL_DELETE_BORDER_WARN,	//ボーダー模様の組み合わせが解除されます。よろしいですか
  EMB_NEEDLE_UP_WARN,				//
  EMB_POSITIONING_RESET_WARN,		// 移動や回転が元に戻りますがよろしいですか？
  EMB_DELETE_BORDERMARK_WARN,	//糸印が消えますがよろしいですか？
  EMB_CHANGE_ORDER_WARN,		//刺しゅう順が変更されます。
  EMB_PATTERN_CANNOT_SAVE,    //ERR_PATTERN_CANNOT_SAVE　記憶できない模様が含まれています。
  EMB_NO_SAVE_PATTERN,    //ERR_NO_SAVE_PATTERN 記憶できない模様が含まれています。ミシンの内臓メモリに記憶くださ
  EMB_OUTSIDE_OF_EMB_FRM_NOUSE,//枠をはみ出します。 ERR_OUTSIDE_OF_EMB_FRM_NOUSE

  EMB_SELECT_PATTERN_ROTATE90_WITH_ALL_DELETE_CLOSE,//選択した模様を９０°回転し、模様選択を続行する ERR_EMB_SELECT_PATTERN_ROTATE90_WITH_ALL_DELETE_CLOSE
  EMB_BAD_EMBROIDERY_DATA,//この刺しゅうデータは糸色情報が不足しているため、糸色を近似して表示します。正しい糸色情報を表示するには、糸色変更画面で、糸色番号を入力してください。 ERR_BAD_EMBROIDERY_DATA
  EMB_SELECT_PATTERN_ROTATE90,//選択した模様を９０°回転し、模様選択を続行する ERR_EMB_SELECT_PATTERN_ROTATE90
  EMB_COUTING_THIS_PATTERN_NOT_USE,//他のカテゴリーの模様と組み合わせることができません ERR_EMB_COUTING_THIS_PATTERN_NOT_USE
  EMB_SELECT_PATTERN_ROTATE90_F,//選択した模様を９０°回転し、模様選択を続行する キャンセル時にファイル選択をリセット ERR_EMB_SELECT_PATTERN_ROTATE90_F
  EMB_THIS_PATTERN_NOT_USE,//ERR_EMB_THIS_PATTERN_NOT_USE この模様はつかえません
  EMB_ANGOU_NOT_USE,//ERR_ANGOU_NOT_USE この模様はつかえません
  EMB_TROUBLE_OCCORED_POWER_OFF,//ERR_TROUBLE_OCCORED_POWER_OFF
  EMB_FD_WRONG_FORMAT,//ERR_FD_WRONG_FORMAT
  EMB_QUILT_THIS_PATTERN_NOT_USE,//ERR_EMB_COUTING_THIS_PATTERN_NOT_USE
  EMB_MDC_TOO_ALLOC_FAILURE,//ERR_MDC_TOO_ALLOC_FAILURE
  EMB_MDC_CANCEL_B,//ERR_MDC_CANCEL_B

  // 通知
  EMB_GOTO_SEWING,				// 縫製画面への遷移
  EMB_GOTO_SEWING_PTN_CONNECT,	// つなぎ縫製画面への遷移
  EMB_GOTO_RESUME,				// レジューム処理への遷移
  EMB_GOTO_EDIT,					// 編集画面への遷移
  EMB_ALL_GROUP_MOVE_TO_LIMIT,	// 模様移動で全グループの模様をLimit位置まで移動した

  EMB_MEMOVER_OK_ERR,			//メモリオーバーエラー EMB_MEMOVER_ERRと同じメッセージでOKキーに変更

}

// つなぎ設定
enum MarkPatCnctSetting_t {
  NO_PAT_CNCTS, // 0  設定無し
  PAT_CNCTS_TOP_L_NC, // 1  上辺左点 次の模様中点
  PAT_CNCTS_TOP_L_NL, // 2  上辺左点 次の模様左点
  PAT_CNCTS_TOP_C_NR, // 3  上辺中点 次の模様右点
  PAT_CNCTS_TOP_C_NC, // 4  上辺中点 次の模様中点
  PAT_CNCTS_TOP_C_NL, // 5  上辺中点 次の模様左点
  PAT_CNCTS_TOP_R_NR, // 6  上辺右点 次の模様右点
  PAT_CNCTS_TOP_R_NC, // 7  上辺右点 次の模様中点
  PAT_CNCTS_TOP_RIGHT, // 8  上辺右辺角
  PAT_CNCTS_RIGHT_T_NC, // 9  右辺上点 次の模様中点
  PAT_CNCTS_RIGHT_T_NT, // 10 右辺上点 次の模様上点
  PAT_CNCTS_RIGHT_C_NB, // 11 右辺中点 次の模様下点
  PAT_CNCTS_RIGHT_C_NC, // 12 右辺中点 次の模様中点
  PAT_CNCTS_RIGHT_C_NT, // 13 右辺中点 次の模様上点
  PAT_CNCTS_RIGHT_B_NB, // 14 右辺下点 次の模様下点
  PAT_CNCTS_RIGHT_B_NC, // 15 右辺下点 次の模様中点
  PAT_CNCTS_RIGHT_BOTTOM, // 16 右辺下辺角
  PAT_CNCTS_BOTTOM_R_NC, // 17 下辺右点 次の模様中点
  PAT_CNCTS_BOTTOM_R_NR, // 18 下辺右点 次の模様右点
  PAT_CNCTS_BOTTOM_C_NL, // 19 下辺中点 次の模様左点
  PAT_CNCTS_BOTTOM_C_NC, // 20 下辺中点 次の模様中点
  PAT_CNCTS_BOTTOM_C_NR, // 21 下辺中点 次の模様右点
  PAT_CNCTS_BOTTOM_L_NL, // 22 下辺左点 次の模様左点
  PAT_CNCTS_BOTTOM_L_NC, // 23 下辺左点 次の模様中点
  PAT_CNCTS_BOTTOM_LEFT, // 24 下辺左辺角
  PAT_CNCTS_LEFT_B_NC, // 25 左辺下点 次の模様中点
  PAT_CNCTS_LEFT_B_NB, // 26 左辺下点 次の模様下点
  PAT_CNCTS_LEFT_C_NT, // 27 左辺中点 次の模様上点
  PAT_CNCTS_LEFT_C_NC, // 28 左辺中点 次の模様中点
  PAT_CNCTS_LEFT_C_NB, // 29 左辺中点 次の模様下点
  PAT_CNCTS_LEFT_T_NT, // 30 左辺上点 次の模様上点
  PAT_CNCTS_LEFT_T_NC, // 31 左辺上点 次の模様中点
  PAT_CNCTS_LEFT_TOP, // 32 左辺上辺角
  PAT_CNCTS_STD_MAX,
}

enum embLargeCategory_t {
  EMB_LARGE_CAT_CREATE_QUILT, //キルト作成
  EMB_LARGE_CAT_COUTING, //カウチング
  EMB_LARGE_CAT_LONGSTITCH, //ロングステッチ
  EMB_LARGE_CAT_CUSTOMDESIGN, //大型分割
  EMB_LARGE_CAT_MEMORY, //保存データ
  EMB_LARGE_CAT_PHOTO, //写真刺繍
  EMB_LARGE_CAT_OTHER, //通常模様をふくむその他
}

enum MarkPatCnctImgType_t {
  PAT_CNCT_IMG_TYPE_SETTING, // つなぎ設定画面
  PAT_CNCT_IMG_TYPE_RESET, // 布張り位置指示画面
  PAT_CNCT_IMG_TYPE_AREA_OVER, // エリアオーバー画面
}

/// アトリビュート
abstract class PresserFoot_t {
  /// W+押さえ(LED)
  static const int EMB_PRESSER_FOOT_WPLUS = 0;

  /// W押さえ(通常)
  static const int EMB_PRESSER_FOOT_W = 1;

  /// カウチング用
  static const int EMB_PRESSER_FOOT_Y = 2;
}

/// 定義
abstract class FrameSize_t {
  /// IIVO LL枠
  static const int FRAME_297_465 = 0;

  /// XP LL枠
  static const int FRAME_272_408 = 1;

  /// XV LL						*/	/* 20140123 Naka
  static const int FRAME_240_360 = 2;

  /// X1 LL
  static const int FRAME_200_300 = 3;

  /// XV SQ						*/	/* UGK 240×240枠対応 ZZPBA528
  static const int FRAME_240_240 = 4;

  /// QUILT
  static const int FRAME_200_200 = 5;
  static const int FRAME_180_300 = 6;

  /// D6High LL					*/	// BIG
  static const int FRAME_160_260 = 7;

  /// D6Mid LL						*/	// MID_LL
  static const int FRAME_130_180 = 8;

  /// V SQR
  static const int FRAME_150_150 = 9;

  /// 中枠							*/	// MIDDLE
  /// /*↓XP追加枠
  static const int FRAME_100_100 = 10;

  /// 中枠							*/	// MIDDLE
  /// /*↑XP追加枠
  static const int FRAME_272_272 = 11;
  static const int FRAME_30_40 = 12;

  /// 小枠							*/	// SMALL
  static const int FRAME_BORDER = 13;

  /// ボーダー(繋ぎ刺繍)枠	100*180	*/	// BORDER
  static const int FRAME_BH = 14;

  /// ジャンボボーダー枠	100*300
  static const int FRAME_JUMBO_BORDER = 15;

  /// スキャン枠
  static const int FRAME_SCAN = 16;

  /// マグネット枠			180*360
  static const int FRAME_MAGNET = 17;

  /// XP UGK2　MAGNET_FRAME-1-1　2019/12/06　HAL
  static const int FRAME_ALL_MIGHTY = 18;

  /// マグネット枠			130*180
  static const int FRAME_MAGNET_130_180 = 19;

  /// マグネット枠			254*254
  static const int FRAME_MAGNET_254_254 = 20;

  /// マグネット枠			180*300	*/	/* PHFIRMXP-2654
  static const int FRAME_MAGNET_180_300 = 21;
  static const int FRAME_NOTHING = 22;

  /// 枠なし						*/	// NOTHING
  static const int FRAME_NOT_USE = 23;
}

/// */
/// /* 刺繍模様の枠内判定用定義
abstract class Emb_Frame_Type_t {
  /// 最小枠
  static const int EMB_FRAME_S = 0;

  /// 小枠
  static const int EMB_FRAME_M = 1;

  /// 中枠
  static const int EMB_FRAME_L = 2;

  /// 最大枠
  static const int EMB_FRAME_LL = 3;
  static const int EMB_FRAME_LIST_MAX = 4;
}

enum StartPositionQuilt_t {
  EMB_SP_QUILT_START_SLELCT, //Startが選択
  EMB_SP_QUILT_END_SLELCT, //Endが選択
  EMB_SP_QUILT_START, //Start未選択状態
  EMB_SP_QUILT_END, //End未選択状態
  EMB_SP_QUILT, //その他
}

enum EmbDirSelect_t {
  EMB_DIR_SELECT_TOP_LEFT, // 左上
  EMB_DIR_SELECT_TOP, // 上
  EMB_DIR_SELECT_TOP_RIGHT, // 右上
  EMB_DIR_SELECT_LEFT, // 左
  EMB_DIR_SELECT_CENTER, // 中央
  EMB_DIR_SELECT_RIGHT, // 右
  EMB_DIR_SELECT_BOTTOM_LEFT, // 左下
  EMB_DIR_SELECT_BOTTOM, // 下
  EMB_DIR_SELECT_BOTTOM_RIGHT, // 右下
  EMB_DIR_SELECT_MAX,
}

enum EmbSewingScr_t {
  DistanceScr,
  RotateScr,
  DistanceQuilt,
  RotateQuilt,
  DistanceEdgeQuilt, //EdgeToEdgeキルト自動つなぎ縫製移動画面
  RotateEdgeQuilt, //EdgeToEdgeキルト自動つなぎ縫製回転画面
  OtherScr,
}

const int BRO_EX_HAD = 0;

const int BRO_EX_ANNA = 1;

const int BRO_EX_ROSE = 2;

const int BRO_EX_HIGH_FASHION = 3;

const int BRO_EX_ZENTANGLE = 4;

const int BRO_EX_HOME_GB = 5;

const int BRO_EX_HOME_OTHERS = 6;

const int BRO_EX_FOOD = 7;

const int BRO_EX_SEA_SIDE = 8;

const int BRO_EX_VIN_FOLK = 9;

const int BRO_EX_KIDS = 10;

const int BRO_EX_REFRESH = 11;

const int BRO_EXIST_DESIGN = 12;

const int BRO_EXIST_UGK = 13;

const int BRO_EXIST_UGK2 = 14;

const int BRO_EXIST_UGK3 = 15;

const int BRO_ALP_FLORAL_GOLD = 16;

const int BRO_ALP_STRIPE = 17;

const int BRO_ALP_DOUBLE_OUTLINE = 18;

const int BRO_ALP_MONOGRAM_VINTAGE = 19;

const int BRO_ALP_GREEK = 20;

const int BRO_ALP_APP_GREEK = 21;

const int BRO_ALP_CALLIGRAPHY = 22;

const int BRO_ALP_NAWAMOJI = 23;

const int BRO_ALP_SPLITMOJI = 24;

const int BRO_EMB_BH = 25;

const int BRO_EMB_BH_DECO = 26;

const int BRO_UTILITY = 27;

const int DIS_MODERN_MIC = 28;

const int DIS_VINTAGE_MIC = 29;

const int DIS_POOH = 30;

const int DIS_PRINCESS = 31;

const int DIS_FILMS = 32;

const int DIS_PIXER = 33;

const int DIS_UTILITY = 34;

const int TAC_UTILITY = 35;

const int TAC_EXIST_DESIGN = 36;

const int TAC_ANIMALS = 37;

const int TAC_CELEBRATE = 38;

const int TAC_COASTAL = 39;

const int TAC_DELICATE = 40;

const int TAC_FLORAL = 41;

const int TAC_HEIRLOOM = 42;

const int TAC_HERITAGE = 43;

const int TAC_HOLIDAYS = 44;

const int TAC_HOME_ACCENTS = 45;

const int TAC_KIDS_CORNER = 46;

const int TAC_MONOGRAMS = 47;

const int TAC_NOVELTY = 48;

const int TAC_OCEANLIFE = 49;

const int TAC_QUILTING = 50;

const int TAC_SEWLEBRITIES = 51;

const int TAC_VINTAGE = 52;

const int BRO_EMB_COUTING = 53;

const int BRO_EMB_COUTING_KIT3 = 54;

const int BRO_EMB_LONGSTITCH = 55;

const int FRAME_01 = 56;

const int FRAME_02 = 57;

const int FRAME_03 = 58;

const int FRAME_04 = 59;

const int FRAME_05 = 60;

const int FRAME_06 = 61;

const int FRAME_07 = 62;

const int FRAME_08 = 63;

const int FRAME_09 = 64;

const int FRAME_10 = 65;

const int BH_SIZE_XS = 0;

const int BH_SIZE_SS = 1;

const int BH_SIZE_S = 2;

const int BH_SIZE_M = 3;

const int BH_SIZE_L = 4;

const int SHUFFLE_NOTHING = 0;

const int SHUFFLE_RANDOM = 1;

const int SHUFFLE_VIVID = 2;

const int SHUFFLE_SOFT = 3;

const int SHUFFLE_GRADATION = 4;

const int SHUFFLE_VALID = 0;

const int SHUFFLE_INVALID = 255;

const int MAIN_IMAGE = 1;

const int SUB_IMAGE = 0;

const int CENTER_FIX = 1;

const int CENTER_AUTO = 0;

const int BMP24BIT = 0;

const int BMP16BIT = 1;

const int INSIDE_ON = 1;

const int INSIDE_OFF = 0;

const int SELECT = 1;

const int UNSELECT = 0;

const int TEXTURE_ON = 1;

const int TEXTURE_OFF = 0;

const int NOT_SEW = 1;

const int SEW = 0;

const int MANUAL_SHUFFLE = 1;

const int AUTO_SHUFFLE = 0;

const int DRAW_LINE = 1;

const int NOT_DRAW_LINE = 0;

const int DRAW_FRAME = 1;

const int NOT_DRAW_FRAMW = 0;

const int SHUFFLE_THUMBNAIL = 0;

const int SHUFFLE_PREVIEW = 1;

const int BACK_DRAW = 1;

const int BACK_NOTDRAW = 0;

const int REALIMG_SELECTED = 1;

const int REALIMG_ALLEMB = 0;

const int EMB_BODER_MARK_POS_TOP = 1;

const int EMB_BODER_MARK_POS_TOP_LEFT = 2;

const int EMB_BODER_MARK_POS_TOP_RIGHT = 4;

const int EMB_BODER_MARK_POS_CENTER_LEFT = 8;

const int EMB_BODER_MARK_POS_CENTER_RIGHT = 16;

const int EMB_BODER_MARK_POS_BOTTOM_LEFT = 32;

const int EMB_BODER_MARK_POS_BOTTOM_RIGHT = 64;

const int EMB_BODER_MARK_POS_BOTTOM = 128;

const int EMB_BODER_MARK_POS_ALL = 255;

const int ALLCOLOR_TRANS = 4294967295;

const int ESTI_NEXT_RGB = 20000;

const int ESTI_TC_CODE = 30000;
