import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../common_component/preview_custom_painter.dart'
    show
        <PERSON><PERSON><PERSON><PERSON>,
        RectPainter,
        GridPainter,
        PointsPainter,
        PathPainter,
        ThreadMarkPainter;
import 'component/projector_frame_drag_area/projector_frame_drag_area.dart';
import 'component/transparent_image_gesture.dart';
import 'preview_view_model.dart';

///
/// 刺しゅうパターン編集画面 の 展示エリア
///
class Preview extends ConsumerStatefulWidget {
  const Preview({super.key});
  @override
  ConsumerState<Preview> createState() => _PreviewState();
}

class _PreviewState extends ConsumerState<Preview> {
  /// Patternセンターを選択するためのGlobalKey
  final GlobalKey _centerKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(previewViewModelProvider);
    final viewModel = ref.read(previewViewModelProvider.notifier);

    return Column(
      children: [
        const Spacer(flex: 159),
        Expanded(
          flex: viewModel.getPreviewSize.dy.toInt(),
          child: Row(
            children: [
              Expanded(
                flex: 564,
                child: pic_embroidery_mainpreview(
                  child: Row(
                    children: [
                      const Spacer(flex: 1),
                      Expanded(
                        flex: viewModel.getPreviewSize.dx.toInt(),
                        child: ClipRect(
                          clipBehavior: Clip.antiAlias,
                          child: Transform.scale(
                            scale: viewModel.getMagnification,
                            origin: state.zoomOutPoint,
                            child: ColoredBox(
                              color: state.backgroundColor,
                              child: Stack(
                                children: [
                                  /// 背景スキャンの画像
                                  IgnorePointer(
                                    child: Container(
                                      alignment: Alignment.center,
                                      width: viewModel.getPreviewSize.dx,
                                      height: viewModel.getPreviewSize.dy,
                                      child:
                                          state.backgroundImage ?? Container(),
                                    ),
                                  ),

                                  /// Preview空白のクリック領域
                                  SizedBox(
                                    width: viewModel.getPreviewSize.dx,
                                    height: viewModel.getPreviewSize.dy,
                                    child: HitTestBlocker(
                                      child: Listener(
                                        behavior: HitTestBehavior.translucent,
                                        onPointerDown: (_) => viewModel
                                            .onPreviewSpaceAreaClicked(),
                                      ),
                                    ),
                                  ),

                                  /// すべてのpatternを生成
                                  ...() {
                                    List<Widget> patternWidget = [];
                                    state.patternDisplayInfoList
                                        .asMap()
                                        .entries
                                        .forEach((pattern) {
                                      PatternViewDisplayInfo patternInfo =
                                          pattern.value;

                                      patternWidget.add(
                                        Positioned(
                                          top: patternInfo.top,
                                          left: patternInfo.left,
                                          width: patternInfo.width,
                                          height: patternInfo.height,
                                          child: Stack(
                                            children: [
                                              /// すべてのBorderを生成
                                              ...() {
                                                List<Widget> borderWidget = [];
                                                patternInfo
                                                    .borderDisplayInfoList
                                                    .asMap()
                                                    .entries
                                                    .forEach((border) {
                                                  EmbBorderViewDisplayInfo
                                                      borderInfo = border.value;

                                                  borderWidget.add(
                                                    Positioned(
                                                      top: borderInfo.top,
                                                      left: borderInfo.left,
                                                      width: borderInfo.width,
                                                      height: borderInfo.height,
                                                      child: Transform.rotate(
                                                        angle: borderInfo
                                                                    .isCurrentPattern ==
                                                                false
                                                            ? 0
                                                            : pi /
                                                                180 *
                                                                (state.dragAngle ??
                                                                    0),
                                                        child: Stack(
                                                          children: [
                                                            /// クリックされたイベント
                                                            Positioned(
                                                              top: viewModel
                                                                  .getMaskOutArea,
                                                              left: viewModel
                                                                  .getMaskOutArea,
                                                              width: borderInfo
                                                                      .width -
                                                                  viewModel
                                                                          .getMaskOutArea *
                                                                      2,
                                                              height: borderInfo
                                                                      .height -
                                                                  viewModel
                                                                          .getMaskOutArea *
                                                                      2,
                                                              child:
                                                                  HitTestBlocker(
                                                                child: Listener(
                                                                  behavior:
                                                                      HitTestBehavior
                                                                          .translucent,
                                                                  onPointerDown:
                                                                      (_) => viewModel
                                                                          .onPatternAllAreaTapDown(
                                                                    sewingIndex:
                                                                        borderInfo
                                                                            .sewingIndex,
                                                                    displayIndex:
                                                                        pattern
                                                                            .key,
                                                                  ),
                                                                  onPointerUp: (_) =>
                                                                      viewModel
                                                                          .onPatternAllAreaTapUp(),
                                                                  child:
                                                                      GestureDetector(
                                                                    onScaleStart:
                                                                        (details) =>
                                                                            viewModel.dargPatternStart(details),
                                                                    onScaleUpdate:
                                                                        (details) =>
                                                                            viewModel.dargPattern(details),
                                                                    onScaleEnd:
                                                                        (details) =>
                                                                            viewModel.dargPatternEnd(),
                                                                    child:
                                                                        Container(
                                                                      color: Colors
                                                                          .transparent,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ),

                                                            /// すべてのgroupを生成
                                                            ...() {
                                                              List<Widget>
                                                                  groupWidget =
                                                                  [];
                                                              borderInfo
                                                                  .groupDisplayInfoList
                                                                  .asMap()
                                                                  .entries
                                                                  .forEach(
                                                                      (group) {
                                                                EmbGroupViewDisplayInfo
                                                                    groupInfo =
                                                                    group.value;

                                                                groupWidget.add(
                                                                  /// patternの表示領域(Maskと赤いドットを含む)
                                                                  Positioned(
                                                                    top: groupInfo
                                                                        .top,
                                                                    left: groupInfo
                                                                        .left,
                                                                    width: groupInfo
                                                                        .width,
                                                                    height: groupInfo
                                                                        .height,

                                                                    /// group
                                                                    child:
                                                                        Stack(
                                                                      children: [
                                                                        /// すべてのEmbPatternを生成
                                                                        ...() {
                                                                          List<Widget>
                                                                              widget =
                                                                              [];
                                                                          groupInfo
                                                                              .embPatternDisplayInfoList
                                                                              .asMap()
                                                                              .entries
                                                                              .forEach((embPattern) {
                                                                            EmbPatternViewDisplayInfo
                                                                                embPatternInfo =
                                                                                embPattern.value;

                                                                            /// EmbPatternの画像
                                                                            if (embPatternInfo.displayImage !=
                                                                                null) {
                                                                              widget.add(
                                                                                Positioned(
                                                                                  top: embPatternInfo.imageTop,
                                                                                  left: embPatternInfo.imageLeft,
                                                                                  width: embPatternInfo.imageWidth,
                                                                                  height: embPatternInfo.imageHeight,
                                                                                  child: TransparentImageGesture(
                                                                                    image: embPatternInfo.displayImage!,
                                                                                    onPatternOpacityAreaClicked: () => viewModel.onPatternNoOpacityAreaClicked(
                                                                                      sewingIndex: borderInfo.sewingIndex,
                                                                                      displayIndex: pattern.key,
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                              );
                                                                            } else {
                                                                              /// do noting
                                                                            }

                                                                            /// EmbPattern Mask
                                                                            bool
                                                                                isDisplayEmbPatternMask =
                                                                                false;
                                                                            bool
                                                                                isDashedLine =
                                                                                false;
                                                                            if (groupInfo.isAllNotSewing) {
                                                                              isDisplayEmbPatternMask = true;
                                                                              if (patternInfo.isGroup && patternInfo.isCurrentPattern && patternInfo.isSelected == false && state.popupType != PopupType.multipleSelection && state.popupType != PopupType.alignment && state.popupType != PopupType.order) {
                                                                                isDashedLine = false;
                                                                              } else {
                                                                                isDashedLine = groupInfo.isAllNotSewing;
                                                                              }
                                                                            } else {
                                                                              switch (state.popupType) {
                                                                                case PopupType.alignment:
                                                                                case PopupType.multipleSelection:
                                                                                case PopupType.order:
                                                                                  isDisplayEmbPatternMask = false;
                                                                                  break;
                                                                                default:
                                                                                  isDisplayEmbPatternMask = patternInfo.isSelected == false && (patternInfo.isGroup && patternInfo.isCurrentPattern) || (state.dragAngle != null && patternInfo.isCurrentPattern);
                                                                                  break;
                                                                              }
                                                                            }
                                                                            if (isDisplayEmbPatternMask) {
                                                                              widget.add(
                                                                                Positioned(
                                                                                  top: embPatternInfo.top,
                                                                                  left: embPatternInfo.left,
                                                                                  width: embPatternInfo.width,
                                                                                  height: embPatternInfo.height,
                                                                                  child: IgnorePointer(
                                                                                    child: CustomPaint(
                                                                                      painter: MaskPainter(
                                                                                        isDashedLine: isDashedLine,
                                                                                        maskColor: patternInfo.isCurrentPattern ? state.maskColor : const Color.fromARGB(255, 235, 0, 0),
                                                                                        strokeWidth: 1 / viewModel.getMagnification,
                                                                                        maskTopLeft: embPatternInfo.mask.topLeft,
                                                                                        maskTopRight: embPatternInfo.mask.topRight,
                                                                                        maskBottomLeft: embPatternInfo.mask.bottomLeft,
                                                                                        maskBottomRight: embPatternInfo.mask.bottomRight,
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                ),
                                                                              );
                                                                            } else {
                                                                              /// Do nothing
                                                                            }
                                                                          });
                                                                          return widget;
                                                                        }(),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                );
                                                              });
                                                              return groupWidget;
                                                            }(),

                                                            /// 現在のPatternの中心点
                                                            IgnorePointer(
                                                              child: Center(
                                                                child: SizedBox(
                                                                  key: borderInfo
                                                                              .isCurrentPattern &&
                                                                          borderInfo
                                                                              .isBaseBorder
                                                                      ? _centerKey
                                                                      : null,
                                                                  width: 0.1,
                                                                  height: 0.1,
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                });

                                                return borderWidget;
                                              }(),

                                              /// ピンクマスク
                                              IgnorePointer(
                                                child: patternInfo.isSelected &&
                                                        (state.popupType ==
                                                                PopupType
                                                                    .multipleSelection ||
                                                            state.popupType ==
                                                                PopupType
                                                                    .alignment)
                                                    ? Center(
                                                        child: Container(
                                                          width: patternInfo
                                                                  .width -
                                                              viewModel
                                                                      .getMaskOutArea *
                                                                  2,
                                                          height: patternInfo
                                                                  .height -
                                                              viewModel
                                                                      .getMaskOutArea *
                                                                  2,
                                                          color: const Color
                                                                  .fromARGB(255,
                                                                  230, 144, 176)
                                                              .withOpacity(0.3),
                                                        ),
                                                      )
                                                    : Container(),
                                              ),
                                            ],
                                          ),
                                        ),
                                      );

                                      patternWidget.add(
                                        IgnorePointer(
                                          ignoring: true,
                                          child: SizedBox(
                                            width: viewModel.getPreviewSize.dx,
                                            height: viewModel.getPreviewSize.dy,
                                            child: Stack(
                                              children: [
                                                /// すべてのBorderを生成
                                                ...() {
                                                  List<Widget> borderWidget =
                                                      [];
                                                  patternInfo
                                                      .borderDisplayInfoList
                                                      .asMap()
                                                      .entries
                                                      .forEach(
                                                    (border) {
                                                      EmbBorderViewDisplayInfo
                                                          borderInfo =
                                                          border.value;
                                                      borderWidget.add(
                                                        SizedBox(
                                                          width: viewModel
                                                              .getPreviewSize
                                                              .dx,
                                                          height: viewModel
                                                              .getPreviewSize
                                                              .dy,
                                                          child: Stack(
                                                              alignment:
                                                                  Alignment
                                                                      .center,
                                                              children: [
                                                                /// すべてのgroupを生成
                                                                ...() {
                                                                  List<Widget>
                                                                      groupWidget =
                                                                      [];
                                                                  borderInfo
                                                                      .groupDisplayInfoList
                                                                      .asMap()
                                                                      .entries
                                                                      .forEach(
                                                                          (group) {
                                                                    EmbGroupViewDisplayInfo
                                                                        groupInfo =
                                                                        group
                                                                            .value;
                                                                    if (groupInfo
                                                                            .arcImage !=
                                                                        null) {
                                                                      groupWidget
                                                                          .add(
                                                                        Positioned(
                                                                          top: groupInfo
                                                                              .arcImageOffset
                                                                              .dy,
                                                                          left: groupInfo
                                                                              .arcImageOffset
                                                                              .dx,
                                                                          width: viewModel
                                                                              .getPreviewSize
                                                                              .dx,
                                                                          height: viewModel
                                                                              .getPreviewSize
                                                                              .dy,
                                                                          child:
                                                                              Image.memory(
                                                                            fit:
                                                                                BoxFit.contain,
                                                                            gaplessPlayback:
                                                                                false,
                                                                            groupInfo.arcImage!,
                                                                          ),
                                                                        ),
                                                                      );
                                                                    } else {
                                                                      /// do nothing
                                                                    }
                                                                  });

                                                                  return groupWidget;
                                                                }(),
                                                              ]),
                                                        ),
                                                      );
                                                    },
                                                  );

                                                  return borderWidget;
                                                }(),
                                              ],
                                            ),
                                          ),
                                        ),
                                      );

                                      /// Border Mask
                                      bool isDisplayBorderMask = false;
                                      switch (state.popupType) {
                                        case PopupType.alignment:
                                          isDisplayBorderMask = false;
                                          break;
                                        case PopupType.multipleSelection:
                                        case PopupType.order:
                                          isDisplayBorderMask =
                                              patternInfo.isCurrentPattern;
                                          break;
                                        default:
                                          isDisplayBorderMask = state
                                                      .borderMarkMaskRect ==
                                                  null &&
                                              (patternInfo.isSelected ||
                                                  (patternInfo.isBorder &&
                                                      patternInfo
                                                          .isCurrentPattern));
                                          break;
                                      }
                                      if (isDisplayBorderMask) {
                                        patternWidget.add(
                                          IgnorePointer(
                                            child: CustomPaint(
                                              painter: MaskPainter(
                                                isDashedLine: state.popupType ==
                                                    PopupType.multipleSelection,
                                                maskColor: const Color.fromARGB(
                                                    255, 235, 0, 0),
                                                strokeWidth: 1 /
                                                    viewModel.getMagnification,
                                                maskTopLeft: patternInfo
                                                    .maskDisplayInfo.topLeft,
                                                maskTopRight: patternInfo
                                                    .maskDisplayInfo.topRight,
                                                maskBottomLeft: patternInfo
                                                    .maskDisplayInfo.bottomLeft,
                                                maskBottomRight: patternInfo
                                                    .maskDisplayInfo
                                                    .bottomRight,
                                              ),
                                            ),
                                          ),
                                        );
                                      } else {
                                        /// Do nothing
                                      }
                                    });

                                    return patternWidget;
                                  }(),

                                  /// Rotateの赤いドット
                                  state.redPointInfo == null ||
                                          state.isRedPointOverPreview == true
                                      ? Container()
                                      : Positioned(
                                          left: state.redPointInfo!.topLeft.dx,
                                          top: state.redPointInfo!.topLeft.dy,
                                          width: state.redPointInfo!.width,
                                          height: state.redPointInfo!.height,
                                          child: Transform.rotate(
                                            angle: pi /
                                                180 *
                                                (state.dragAngle ?? 0),
                                            child: Stack(
                                              children: [
                                                /// すべてのEmbPatternを生成
                                                ...() {
                                                  List<Widget> widget = [];

                                                  state.redPointInfo!
                                                      .redPointInfos
                                                      .asMap()
                                                      .entries
                                                      .forEach((element) {
                                                    if (element
                                                        .value.isRotatePoint) {
                                                      /// Rotate赤色ドット
                                                      widget.add(
                                                        Positioned(
                                                          left: element
                                                              .value.topLeft.dx,
                                                          top: element
                                                              .value.topLeft.dy,
                                                          child:
                                                              GestureDetector(
                                                            onPanStart:
                                                                (details) {
                                                              RenderBox
                                                                  renderBox =
                                                                  _centerKey
                                                                          .currentContext!
                                                                          .findRenderObject()
                                                                      as RenderBox;
                                                              viewModel.dargRotatePointStart(
                                                                  renderBox.localToGlobal(
                                                                      Offset
                                                                          .zero),
                                                                  state
                                                                      .redPointInfo!
                                                                      .angle,
                                                                  details
                                                                      .globalPosition);
                                                            },
                                                            onPanUpdate:
                                                                (details) =>
                                                                    viewModel
                                                                        .dargRotatePoint(
                                                              details
                                                                  .globalPosition,
                                                            ),
                                                            onPanEnd: (details) =>
                                                                viewModel
                                                                    .dargRotatePointEnd(),
                                                            behavior:
                                                                HitTestBehavior
                                                                    .translucent,
                                                            child: Container(
                                                              padding: EdgeInsets
                                                                  .all(state
                                                                      .redPointInfo!
                                                                      .redPointTouchOutArea),
                                                              child: Container(
                                                                alignment:
                                                                    Alignment
                                                                        .center,
                                                                width: state
                                                                    .redPointInfo!
                                                                    .redPointImageSize,
                                                                height: state
                                                                    .redPointInfo!
                                                                    .redPointImageSize,
                                                                child: Image.asset(
                                                                    "assets/images/PIC_drag_Rotate.png"),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      );
                                                    } else {
                                                      /// Size赤色ドット
                                                      widget.add(
                                                        Positioned(
                                                          left: element
                                                              .value.topLeft.dx,
                                                          top: element
                                                              .value.topLeft.dy,
                                                          child:
                                                              GestureDetector(
                                                            onPanStart:
                                                                (details) {
                                                              RenderBox
                                                                  renderBox =
                                                                  _centerKey
                                                                          .currentContext!
                                                                          .findRenderObject()
                                                                      as RenderBox;
                                                              viewModel.dargSizePointStart(
                                                                  renderBox
                                                                      .localToGlobal(
                                                                          Offset
                                                                              .zero));
                                                            },
                                                            onPanUpdate: (details) =>
                                                                viewModel.dargSizePoint(
                                                                    details
                                                                        .delta,
                                                                    element
                                                                        .value
                                                                        .redPointPositionType),
                                                            onPanEnd: (details) =>
                                                                viewModel
                                                                    .dargSizePointEnd(),
                                                            behavior:
                                                                HitTestBehavior
                                                                    .translucent,
                                                            child: Container(
                                                              padding: EdgeInsets
                                                                  .all(state
                                                                      .redPointInfo!
                                                                      .redPointTouchOutArea),
                                                              child: Container(
                                                                alignment:
                                                                    Alignment
                                                                        .center,
                                                                width: state
                                                                    .redPointInfo!
                                                                    .redPointImageSize,
                                                                height: state
                                                                    .redPointInfo!
                                                                    .redPointImageSize,
                                                                child: Image.asset(
                                                                    "assets/images/PIC_drag.png"),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      );
                                                    }
                                                  });

                                                  return widget;
                                                }(),
                                              ],
                                            ),
                                          ),
                                        ),

                                  /// Borderのmarkページでは、 mask表示されます
                                  state.borderMarkMaskRect == null
                                      ? Container()
                                      : IgnorePointer(
                                          child: CustomPaint(
                                            painter: RectPainter(
                                              color: const Color.fromARGB(
                                                  255, 235, 0, 0),
                                              strokeWidth: 1 /
                                                  viewModel.getMagnification,
                                              rect: state.borderMarkMaskRect!,
                                            ),
                                          ),
                                        ),

                                  /// Grid
                                  IgnorePointer(
                                    child: SizedBox(
                                      width: viewModel.getPreviewSize.dx,
                                      height: viewModel.getPreviewSize.dy,
                                      child: CustomPaint(
                                        foregroundPainter: GridPainter(
                                          gridColor: state.gridColor,
                                          zoomValue: viewModel.getMagnification,
                                          gridTypeIndex: state.gridTypeIndex,
                                          verticalList:
                                              viewModel.getGridVerticalList(),
                                          horizontalList:
                                              viewModel.getGridHorizontalList(),
                                        ),
                                        painter: PointsPainter(
                                          pathColor: const Color.fromARGB(
                                              255, 0, 0, 0),
                                          strokeWidth:
                                              1 / viewModel.getMagnification,
                                          points: state.blackPoints,
                                        ),
                                      ),
                                    ),
                                  ),

                                  /// frame
                                  IgnorePointer(
                                    child: SizedBox(
                                      width: viewModel.getPreviewSize.dx,
                                      height: viewModel.getPreviewSize.dy,
                                      child: CustomPaint(
                                        painter: PathPainter(
                                          color: state.frameColor,
                                          strokeWidth:
                                              1 / viewModel.getMagnification,
                                          path: state.frameDrawPath,
                                        ),
                                      ),
                                    ),
                                  ),

                                  /// 矩形選択ツールのドラッグ領域
                                  IgnorePointer(
                                    ignoring:
                                        state.isRectangularMarqueeEnable ==
                                            false,
                                    child: SizedBox(
                                      width: viewModel.getPreviewSize.dx,
                                      height: viewModel.getPreviewSize.dy,
                                      child: GestureDetector(
                                        behavior: HitTestBehavior.opaque,
                                        onPanStart: (details) => viewModel
                                            .useRectangularMarqueeStart(
                                                details.localPosition),
                                        onPanUpdate: (details) =>
                                            viewModel.useRectangularMarquee(
                                                details.localPosition),
                                        onPanEnd: (_) => viewModel
                                            .useRectangularMarqueeEnd(),
                                      ),
                                    ),
                                  ),

                                  /// Move機能無効の場合、 previewは使用できません。
                                  IgnorePointer(
                                    ignoring: state.isPreviewClickEnable,
                                    child: Listener(
                                      onPointerDown: (_) =>
                                          viewModel.onPreviewDisableClicked(),
                                      behavior: HitTestBehavior.opaque,
                                      child: SizedBox(
                                        width: viewModel.getPreviewSize.dx,
                                        height: viewModel.getPreviewSize.dy,
                                      ),
                                    ),
                                  ),

                                  /// 拡大後、Previewのドラッグ領域
                                  IgnorePointer(
                                    ignoring:
                                        state.isDragPreviewEnable == false,
                                    child: SizedBox(
                                      width: viewModel.getPreviewSize.dx,
                                      height: viewModel.getPreviewSize.dy,
                                      child: GestureDetector(
                                        behavior: HitTestBehavior.opaque,
                                        onScaleStart: (details) =>
                                            viewModel.dargPreviewStart(details),
                                        onScaleUpdate: (details) =>
                                            viewModel.dargPreview(details),
                                      ),
                                    ),
                                  ),

                                  /// 矩形選択ツールの表示領域
                                  IgnorePointer(
                                    child: state.rectangularMarqueeArea == null
                                        ? Container()
                                        : CustomPaint(
                                            painter: RectPainter(
                                              isDashedLine: false,
                                              strokeWidth: 1 /
                                                  viewModel.getMagnification,
                                              color: const Color.fromARGB(
                                                  255, 235, 0, 0),
                                              rect:
                                                  state.rectangularMarqueeArea!,
                                            ),
                                          ),
                                  ),

                                  /// 分割線
                                  IgnorePointer(
                                    child: SizedBox(
                                      width: viewModel.getPreviewSize.dx,
                                      height: viewModel.getPreviewSize.dy,
                                      child: CustomPaint(
                                        painter: PathPainter(
                                          color: Colors.black,
                                          strokeWidth:
                                              1 / viewModel.getMagnification,
                                          path: state.cutLinePath,
                                          isDashedLine: true,
                                        ),
                                      ),
                                    ),
                                  ),

                                  /// ナイフ
                                  state.knifeDisplayWidget ?? Container(),

                                  /// ページ上の糸印の表示情報
                                  IgnorePointer(
                                    child: SizedBox(
                                      width: viewModel.getPreviewSize.dx,
                                      height: viewModel.getPreviewSize.dy,
                                      child: CustomPaint(
                                        painter: ThreadMarkPainter(
                                          zoomValue: viewModel.getMagnification,
                                          threadMarkInfoList:
                                              state.threadMarkInfoList,
                                        ),
                                      ),
                                    ),
                                  ),

                                  /// 投影範囲
                                  state.isProjectorON
                                      ? const ProjectorFrameDragArea()
                                      : Container(),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      const Spacer(flex: 1)
                    ],
                  ),
                ),
              ),
              const Spacer(flex: 236),
            ],
          ),
        ),
        const Spacer(flex: 241),
      ],
    );
  }
}
