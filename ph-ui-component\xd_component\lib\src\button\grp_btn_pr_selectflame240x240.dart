// ignore_for_file: camel_case_types

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';

import '../icon/ico_pr_selectflame240x240.dart';
import 'theme_button.dart';

class grp_btn_pr_selectflame240x240 extends StatelessWidget {
  const grp_btn_pr_selectflame240x240({
    super.key,
    this.onTap,
    this.state = ButtonState.normal,
    this.feedBackControl = const FeedBackControl(),
  });

  final void Function()? onTap;
  final ButtonState state;
  final FeedBackControl feedBackControl;

  @override
  Widget build(BuildContext context) => FeedBackButton(
        feedBackControl: feedBackControl,
        onTap: state == ButtonState.disable ? null : onTap,
        state: state,
        style: ThemeButton.btn_n_size98x70_theme1,
        child: const Center(child: ico_pr_selectflame240x240()),
      );
}
