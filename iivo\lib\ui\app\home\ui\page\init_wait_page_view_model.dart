import 'dart:async';
import 'dart:io';

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:path/path.dart';
import 'package:system_config/system_config.dart';
import 'package:usb_manager/usb_manager.dart';

import '../../../../../../model/app_locale.dart';
import '../../../../../../model/machine_config_model.dart';
import '../../../../../../model/user_data/user_data.dart';
import '../../../../../../network/network.dart';
import '../../../../../../network/server/http/route/artspira/common.dart';
import '../../../../../../network/server/http/route/my_stitch_monitoring/common.dart';
import '../../../../../../network/server/http/route/my_stitch_monitoring/push_notify.dart';
import '../../../../../iivo.dart';
import '../../../../../memory/memory.dart';
import '../../../../../model/device_memory_model.dart';
import '../../../../../model/limited_edition_model.dart';
import '../../../../../model/projector_model.dart';
import '../../../../../model/provider/listen/sound_listen.dart';
import '../../../../../model/write_debug_log_task_queue.dart.dart';
import '../../../../page_route/page_route.dart';
import '../../../debug_monitor/debug_log.dart';
import '../../../emb/model/color_change_model.dart';
import '../../../setting/model/network_setting_page3_model.dart';
import '../../../testmode/model/testmode_init.dart';
import '../../../testmode/page/page_route.dart';
import '../../model/demo_mode_model.dart';
import 'init_wait_page_view_interface.dart';

/// view _modelに必要な構造
final initWaitPageViewModeProvider = StateNotifierProvider.autoDispose
    .family<InitWaitPageViewInterface, InitWaitPageState, BuildContext>(
        (ref, context) => InitWaitPageViewMode(ref, context));

class InitWaitPageViewMode extends InitWaitPageViewInterface
    with DeviceLibraryEventObserver {
  InitWaitPageViewMode(
    ref,
    context,
  ) : super(const InitWaitPageState(), ref, context);

  @override
  void build() {
    super.build();
    Log.w(tag: "Machine Init Start", description: "");

    MethodChannel mainChannel = const MethodChannel("main");
    mainChannel.invokeMethod("notifyPanelAppLaunched");

    /// Debugモードなら
    /// DebugLog画面を表示する
    Future(() {
      if (DeviceLibrary().apiBinding.isDebugInfoEnabled().value) {
        showDebugViewPopup();
      }
    });

    /// 時刻情報を喪失したミシン起動時（時計設定ONのとき。OFFの場合は非表示）に、
    /// 確認メッセージを表示する
    if (DeviceLibrary().apiBinding.getClockDisplayStatus() &&
        DeviceLibrary().apiBinding.getRTCReadErrorStatus() ==
            RTCReadStatus.rtcReadError) {
      DeviceLibrary().apiBinding.setIsSetTime(false);
    }

    const String appFlavor = String.fromEnvironment('FLUTTER_APP_FLAVOR');

    /// 画面が常に明るくなるように設定
    SystemConfig.disableSystemSleep();

    /// ミシン初期化
    Log.d(
        tag: "Machine Init Start", description: "ミシン初期化 appFlavor:$appFlavor");
    final result1st = TpdLibrary().apiBinding.bpIFInit1st();
    if (result1st != BpIfInitError.ok) {
      _gotoInitFailPage(result1st);
      return;
    }

    /// 「home」APKが返されるかどうかを確認します
    if (_shouldReturnHome()) {
      mainChannel.invokeMethod("launchHome");
      mainChannel.invokeMethod("finishSelf");
      return;
    } else {
      final result2nd = TpdLibrary().apiBinding.bpIFInit2nd();
      if (result2nd != BpIfInitError.ok) {
        _gotoInitFailPage(result2nd);
        return;
      }

      /// 動きの時に　250秒待つ
      /// オープニング画面を選択する
      _startupInitCheck().then((isInitSuccess) async {
        if (isInitSuccess == false) {
          return;
        }

        await _goToStartupPage();
      });
    }
  }

  ///
  /// lib の初期化結果をチークします
  ///
  void _gotoInitFailPage(BpIfInitError error) {
    switch (error) {
      case BpIfInitError.ng:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('Internal error -7');
        break;
      case BpIfInitError.sockCreateError:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('Internal error -4');
        break;
      case BpIfInitError.mapCreateError:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('Internal error -5');
        break;
      case BpIfInitError.allDataError:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('Internal error -6');
        break;
      case BpIfInitError.ok:
      default:
        break;
    }
  }

  ///
  /// lib の初期化結果を待ちます
  ///
  Future<(InitState, int)> _waitInitFinish() async {
    Log.d(tag: "waitInitFinish", description: "start init");

    final Completer<InitState> initCompleter = Completer<InitState>();

    /// 起動状態を取得する
    Future.doWhile(() async {
      /// currentState更新
      final InitState initState = TpdLibrary().apiBinding.getInitState();
      if (initState == InitState.INITSTATE_PANDING) {
        /// Do noting
      } else {
        if (initCompleter.isCompleted == false) {
          initCompleter.complete(initState);
          Log.d(
              tag: "Machine Init",
              description: "getInitState の初期化が完了しました。 initState : $initState");
          return false;
        }
      }
      await Future.delayed(const Duration(milliseconds: 50));
      return true;
    });

    final InitState initState = await initCompleter.future;
    Log.i(tag: "waitInitFinish", description: "init Finish");
    final errorCode = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    return (initState, errorCode);
  }

  ///
  /// 起動時の初期化チェック
  ///
  Future<bool> _startupInitCheck() async {
    final (initState, errorCode) = await _waitInitFinish();

    /// DeviceLibrary 初期化
    DeviceLibrary().init();

    UsbManager().init();

    /// 音声の初期化
    await MachineConfigModel().initSoundPlayerVolume();

    /// beep sound制御する
    startBeepSoundEventPolling();

    if (initState == InitState.INITSTATE_NORMAL_START) {
      TpdLibrary().apiBinding.bpIFSetTestMode(/*normal_mode*/ 0);

      ///
      /// libpanel.soとbrother_panelのver番号間違いを検出する
      ///
      final deviceInfo = DeviceLibrary().apiBinding.getDeviceSettingInfo();
      if ((deviceInfo.settingInfo.libPanelMajorVer !=
              deviceInfo.settingInfo.panelMajorVer) ||
          (deviceInfo.settingInfo.libPanelMinorVer !=
              deviceInfo.settingInfo.panelMinorVer) ||
          (deviceInfo.settingInfo.libPanelPatchVer !=
              deviceInfo.settingInfo.panelPatchVer)) {
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage(
            'The combination of built-in programs is inappropriate.\nPlease redo the update.');
        return false;
      } else {
        /// Do Nothing
      }
    } else if (initState == InitState.INITSTATE_TEST_MODE_START) {
      // 通常のテストモード
      TpdLibrary().apiBinding.bpIFSetTestMode(/*test_mode*/ 1);
    } else {
      TpdLibrary().apiBinding.bpIFSetTestMode(0);
    }

    switch (initState) {
      /// 通常のテストモード
      case InitState.INITSTATE_TEST_MODE_START:
        await TestModeInitModel().startupInit();

        /// shutoffに入るかどうかを判断する
        final goToShutOff = TpdLibrary().apiBinding.isInShutOffModel();
        if (goToShutOff == true) {
          /// シャットオフ画面を表示する
          PagesRoute().pushNamed(nextRoute: PageRouteEnum.shutOff);
          return false;
        }

        /// test modeに入るかどうかを判断する
        if (Platform.isWindows) {
          /// dllファイルがなしため、do nothing
        } else {
          Future(() => PagesRoute()
              .pushReplacement(nextRoute: PageRouteEnum.testModePage));
        }
        return false;

      /// テストモードの異常起動
      case InitState.INITSTATE_TEST_MODE_SERVICE_MODE_XY_MOTOR_ADJUST:
        Future(() => TestModePagesRoute()
            .pushReplacement(nextRoute: TestModePageRouteEnum.tm62Screen));
        return false;
      case InitState.INITSTATE_TEST_MODE_EEPROM_COPY:
        Future(() => TestModePagesRoute()
            .pushReplacement(nextRoute: TestModePageRouteEnum.eepromCopy));
        return false;
      case InitState.INITSTATE_TEST_PM_PHASE:
        Future(() => TestModePagesRoute()
            .pushReplacement(nextRoute: TestModePageRouteEnum.pmPhase));
        return false;
      case InitState.INITSTATE_TEST_MODE_NORMAL_MODE:
        Future(() => TestModePagesRoute()
            .pushReplacement(nextRoute: TestModePageRouteEnum.normalModeOk));
        return false;
      case InitState.INITSTATE_TEST_MODE_NORMAL_MODE_SPEC_ERR:
      case InitState.INITSTATE_TEST_MODE_NORMAL_MODE_SPEC_PHASE_ERR:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        Log.d(
            tag: "INITSTATE_TEST_MODE_NORMAL_MODE_SPEC_ERR",
            description: "SPEC DIFFERENCE and\nPANEL EEPROM FF");
        _gotoInitFailedPage('SPEC DIFFERENCE and\nPANEL EEPROM FF');
        return false;
      case InitState.INITSTATE_TEST_MODE_NORMAL_MODE_PHASE_ERR:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('F, Z Initial Phase Not Set\nSet Test Mode No47');
        Future(() => TestModePagesRoute().pushReplacement(
            nextRoute: TestModePageRouteEnum.normalModeSpecPhaseError));
        return false;

      /// 通常起動の異常起動
      case InitState.INITSTATE_ALLDATA_ERROR:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('Internal error -8');
        return false;
      case InitState.INITSTATE_DATA_VERSION_DIFFERENCE:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('DATA VERSION DIFFERENCE');
        return false;
      case InitState.INITSTATE_PCB_POWER_OFF_NMI:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('Machine PCB power off NMI.');
        return false;
      case InitState.INITSTATE_PCB_POWER_OFF:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('Machine PCB power off.');
        return false;
      case InitState.INITSTATE_PANEL_NO_DATA:
        _gotoInitCopyCopyPage(InitState.INITSTATE_PANEL_NO_DATA);
        return false;
      case InitState.INITSTATE_MAIN_NO_DATA:
        _gotoInitCopyCopyPage(InitState.INITSTATE_MAIN_NO_DATA);
        return false;
      case InitState.INITSTATE_PANEL_AND_MAIN_NO_DATA:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('PANEL AND MACHINE NON DATA');
        return false;
      case InitState.INITSTATE_PANEL_SPEC_EMPTY:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('SPEC DIFFERENCE and PANEL EEPROM FF');
        return false;
      case InitState.INITSTATE_SPEC_DIFFERENCE:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('SPEC DIFFERENCE');
        return false;
      case InitState.INITSTATE_PROGRAM_SPEC_DIFFERENCE:
        SystemSoundPlayer().play(SystemSoundEnum.error);
        _gotoInitFailedPage('PROGRAM SPEC DIFFERENCE');
        return false;
      case InitState.INITSTATE_EEPROM_ERROR:
        _gotoInitFailedPage('Internal error -1');
        return false;
      case InitState.INITSTATE_UNSPPORT_ERROR:
        _gotoInitFailedPage('Entering unexpected mode.');
        return false;
      case InitState.INITSTATE_UNSPPORTED_MODEL_ERROR:
        _gotoInitFailedPage('Internal error -2');
        return false;
      case InitState.INITSTATE_INTERNAL_ERROR:
        _gotoInitFailedPage('Internal error -3');
        return false;
      case InitState.INITSTATE_RESET:

        /// 前回起動時、User情報Reset中に電源が切られていたら削除処理から再開する
        Log.d(
            tag: "Machine Init Finish", description: "got to User Info Reset");

        /// SharedPreferences制御
        await UserData().init();

        AppLocale().initLocale(DeviceLibrary().apiBinding.getLanguage().value);
        PagesRoute()
            .pushReplacement(nextRoute: PageRouteEnum.userResetProcessing);

        return false;

      /// 使用しないので、スタートしない。
      case InitState.INITSTATE_TEST_MODE_ERROR_SIMUKE_MASKROM:
      case InitState.INITSTATE_TEST_LCD_CONTRAST_ADJUST:
      case InitState.INITSTATE_TEST_TOUCH_PANEL_ADJUST:
      case InitState.INITSTATE_TEST_NO_TOUCH_PANEL:
        return false;

      /// 通常起動
      case InitState.INITSTATE_NORMAL_START:
      default:
        break;
    }

    if (errorCode == ErrCode_t.ERR_FAIL_SAFE.index) {
      _gotoInitFailSafePage();
      return false;
    }

    /// 動きの時に初期化処理
    await startupInit();
    await _preloadHomeImage();
    return true;
  }

  ///
  /// ホームページで使用される画像をプリロードする
  ///
  Future<void> _preloadHomeImage() async {
    /// home画面のImageをキャッシュ
    /// メイン画像のキャッシュ
    if (MachineConfigModel().isBrother == true) {
      String homeLanguage = "en";
      switch (AppLocale().getCurrentLocale().languageCode) {
        case 'en':
          homeLanguage = "EN";
        case 'de':
          homeLanguage = "DEU";
        case 'fr':
          homeLanguage = "Fr";
        case 'it':
          homeLanguage = "ITA";
        case 'nl':
          homeLanguage = "NLD";
        case 'es':
          homeLanguage = "SPA";
        case 'ja':
          homeLanguage = "JPN";
      }
      LimitedEditionModel().isLimitedEdition
          ? precacheImage(
              AssetImage("assets/images/HOME_LE_$homeLanguage.png'"),
              initContext)
          : precacheImage(AssetImage("assets/images/HOME_NE_$homeLanguage.png"),
              initContext);
    } else {
      LimitedEditionModel().isLimitedEdition
          ? precacheImage(
              const AssetImage("assets/images/page_home_tacony_le.png"),
              initContext)
          : precacheImage(
              const AssetImage("assets/images/page_home_tacony_ne.png"),
              initContext);
    }

    /// footerのImageをキャッシュ
    precacheImage(
        const AssetImage("assets/images/BTN_N_Teaching_PDF.png"), initContext);

    /// headerとFootのSVGはUnit8Listへ変換
    await TpdLibrary().apiBinding.changHeaderSvgToByte();
    await TpdLibrary().apiBinding.changFooterSvgToByte();
  }

  ///
  /// 起動Page設定
  ///
  Future<void> _goToStartupPage() async {
    /// すべてのブランドの色情報を取得する
    ColorChangeModel().getAllCurrentBrandColor();

    ////////////////////////////////////////////
    /// 起動画面を選択する(オープニングとか)
    ////////////////////////////////////////////
    /// shutoffに入るかどうかを判断する
    final goToShutOff = TpdLibrary().apiBinding.isInShutOffModel();
    if (goToShutOff == true) {
      /// シャットオフ画面を表示する
      PagesRoute().pushNamed(nextRoute: PageRouteEnum.shutOff);
      return;
    } else {
      /// brightness 初期化
      /// ShutOffモードではパネル⇔メインで通信のやり取りをしないため初期化をしておらず、
      /// その状態でCD輝度調整の通信コマンドを送信するとパネルでエラーが出てしまうため
      await MachineConfigModel().initBrightness();
    }

    /// 起動時 TEMPファイル削除します
    if (Platform.isAndroid) {
      await DeviceMemoryModel().delTempFile();
    } else {
      /// do nothing
    }

    if (TpdLibrary().apiBinding.shouldEnterAutoTestMode()) {
      PagesRoute().pushReplacement(nextRoute: PageRouteEnum.autoTestMode);
      return;
    }

    /// IIVOStateのログ保存機能を設定。呼び出し時に以下の非同期操作を実行：
    /// 1. DeviceLibraryのapiBindingメソッドを呼び出し、デバッグログをファイルに書き込む。
    /// 2. 3秒間待機。
    /// 3. WriteDebugLogTaskQueueのインスタンスメソッドを呼び出し、デバッグログをUSBに保存。
    IIVOState.setSaveLogFunction(() async {
      DeviceLibrary().apiBinding.writeDebugLogToFile();
      await Future.delayed(const Duration(seconds: 3));
      WriteDebugLogTaskQueue.getInstance().saveDebugLogToUSB();
    });

    /// 通常起動画面を選択する
    Log.d(tag: "Machine Init Start", description: "起動画面を選択する");
    switch (DeviceLibrary().apiBinding.getInitialScreen().value) {
      case InitialScreenEnum.openingScreen:
        if (await _gotoDemoModeIfReady()) {
          PagesRoute().pushReplacement(nextRoute: PageRouteEnum.demoMode);
        } else {
          MachineConfigModel().baseMode = SettingBaseMode.home;
          PagesRoute().pushReplacement(nextRoute: PageRouteEnum.opening);
        }
        break;
      case InitialScreenEnum.homePage:
        MachineConfigModel().baseMode = SettingBaseMode.home;
        PagesRoute().pushReplacement(nextRoute: PageRouteEnum.home);
        break;
      case InitialScreenEnum.sewingOrEmbroidery:
        PagesRoute().pushReplacement(nextRoute: PageRouteEnum.home);

        /// 起動時に刺繍機がついているなら刺繍モード刺繍カテゴリー選択画面が表示される
        if (TpdLibrary().apiBinding.isEmbUnitConnect()) {
          final error = EmbLibrary().apiBinding.gotoEmbFromOpening();
          if (error != EmbLibraryError.EMB_NO_ERR) {
            return;
          }
          MachineConfigModel().machineBaseModel =
              MachineBaseModeEnum.embroidery;
          MachineConfigModel().baseMode = SettingBaseMode.emb;
          PagesRoute().pushNamedAndRemoveUntil(
            nextRoute: PageRouteEnum.patternSelect,
            untilRoute: PageRouteEnum.home,
          );
        }

        /// 起動時に刺繍機がついていなければ実用模様モード模様選択画面が表示され
        else {
          UtlLibrary().apiBinding.openUtlMode();
          MachineConfigModel().machineBaseModel = MachineBaseModeEnum.sewing;
          MachineConfigModel().baseMode = SettingBaseMode.utl;
          PagesRoute().pushNamedAndRemoveUntil(
            nextRoute: PageRouteEnum.sewingUtility,
            untilRoute: PageRouteEnum.home,
          );
        }

        break;
    }
  }

  ///
  /// USB demo_movieの下にファイルがあるかどうかを確認します
  ///
  Future<bool> _gotoDemoModeIfReady() async {
    List<USBInfo> list = await UsbManager().getUsbInfoList();
    if (list.isEmpty) {
      return false;
    }

    String usbPath = list.first.usbPath;

    DemoModeModel().usbPath = usbPath;
    await UsbManager().mountUsbDevice(usbPath);

    try {
      final demoMoviePath = join(usbPath, DemoModeModel().demoMovie);
      if (DemoModeModel().getFileImageList(demoMoviePath).isNotEmpty ||
          DemoModeModel().getFileMp4List(demoMoviePath).isNotEmpty) {
        DeviceErrorCode deviceError = TpdLibrary().apiBinding.startDemoMode();
        if (deviceError == DeviceErrorCode.devNoError) {
          return true;
        } else {
          Log.assertLog(tag: "Machine Init Start", description: "デモモード起動異常");
          return false;
        }
      } else {
        await UsbManager().unmountUsbDevice(usbPath);
        return false;
      }
    } catch (e) {
      await UsbManager().unmountUsbDevice(usbPath);
      return false;
    }
  }

  ///
  /// 起動エラーページに移動します
  ///
  void _gotoInitFailedPage(String message) {
    Future(() => PagesRoute().pushReplacement(
          nextRoute: PageRouteEnum.initFailed,
          arguments: message,
        ));
  }

  ///
  /// 起動エラーページに移動します
  ///
  void _gotoInitFailSafePage() {
    Future(() =>
        PagesRoute().pushReplacement(nextRoute: PageRouteEnum.initFailSafe));
  }

  ///
  /// 起動エラーページに移動します
  ///
  void _gotoInitCopyCopyPage(InitState initState) {
    Future(() => PagesRoute().pushReplacement(
          nextRoute: PageRouteEnum.initCopyCopy,
          arguments: initState,
        ));
  }

  ///
  /// 「home」APKを返すかどうか
  ///
  bool _shouldReturnHome() {
    final iivoStartFile = FileEntity(
        join(memorySector.brother_dir.absolutePath, "IIVO_START_UPDATE"));
    if (iivoStartFile.existsSync()) {
      iivoStartFile.deleteSync();
      return true;
    } else {
      if (TpdLibrary().apiBinding.getInitState() ==
          InitState.INITSTATE_UPGRADE_MODE) {
        return true;
      } else {
        return false;
      }
    }
  }
}

///
/// 動きの時に初期化処理
///
Future<void> startupInit() async {
  /// 動きの時に　250秒待つ
  /// オープニング画面を選択する
  await Future.delayed(const Duration());

  /// SharedPreferences制御
  await UserData().init();

  AppLocale().initLocale(DeviceLibrary().apiBinding.getLanguage().value);

  /// CameraProjectLibrary 初期化
  CameraProjectLibrary().init();

  /// プロジェクト初期化
  if (await ProjectorModel().initProjectorDevice()) {
    /// バックグラウンド音声の初期化
    BackgroundAudioPlayer.initialize();
    await _initBackgroundSoundPlayerVolume();
  } else {
    for (var i = 0; i < 5; i++) {
      Log.errorTrace(
          "Projector init failed, background audio player is not enabled!");
      sleep(const Duration(milliseconds: 200));
    }
  }

  /// SNC連携認証済か確認
  MachineConfigModel().initKitSNCValid();

  /// upgrade 初期化
  Log.d(tag: "Machine Init Start", description: "upgrade");
  await Upgrade().init();

  ////////////////////////////////////////////
  /// network関する初期化
  ////////////////////////////////////////////
  Log.d(tag: "Machine Init Start", description: "network関する初期化");

  /// artspira情報初期化、
  /// Network接続場合sendUserLog送信時AgreedToTelemetryの値を使用するため、network設定初期化前に実施必要
  NetworkSettingPage1Mode3().initNetworkSettingRegisterInfo();

  /// artspira共通ヘッダ初期化
  /// Network接続場合sendUserLog送信時共通headerを使用するため、network設定初期化前に実施必要
  ArtspiraRequestCommonHeader().initReqCommonHeader();

  /// network 初期化
  await initNetWork();

  /// モニター・イベント処理の初期化
  initMonitoringEventCtrl();

  /// パワーオン時の起動モードは通常モード設定、非通常モード場合送信できない
  PushSendSingleton().setPowerOnMode(true);

  /// 電源ON、収集情報アップロード
  /// 製品情報 初期化
  initProductInfoRequestBody();

  Log.d(tag: "Machine Init Finish", description: "normal start");
}

///
/// バックグラウンド音声のボリュームを初期化する
///
Future<void> _initBackgroundSoundPlayerVolume() async {
  /// sr volume
  final srVolumeLevel = DeviceLibrary().apiBinding.getSRVolume().value;
  await SrSoundPlayer().setVolume(srVolumeLevel / maxVolume);

  /// message volume
  final messageVolumeLevel = DeviceLibrary().apiBinding.getVoiceVolume().value;
  await MessageSoundPlayer().setVolume(messageVolumeLevel / maxVolume);

  /// system volume
  final systemVolumeLevel =
      DeviceLibrary().apiBinding.getMachineSpeakerVolume().value;
  await SystemSoundPlayer().setVolume(systemVolumeLevel / maxVolume);
}
