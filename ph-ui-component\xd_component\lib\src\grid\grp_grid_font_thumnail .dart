﻿// ignore_for_file: camel_case_types, file_names

import 'package:flutter/material.dart';

import '../button/grp_btn_thumbnail.dart';

class grp_grid_font_thumnail extends StatelessWidget {
  const grp_grid_font_thumnail({
    super.key,
    this.controller,
    required this.isExclusiveScriptType,
    required this.allCharacterFontImagesInfo,
    required this.isCharacterFontName,
    required this.characterFontImagesLength,
    required this.onItemClicked,
    required this.checkboxSelected,
    required this.getCharacterFontName,
    required this.getCharacterFontImage,
    required this.exclusiveScriptButton,
  });

  final ScrollController? controller;
  final bool isExclusiveScriptType;
  final List allCharacterFontImagesInfo;
  final bool Function(int index) isCharacterFontName;
  final int characterFontImagesLength;
  final Function(
    BuildContext context,
    int index,
    bool checkboxSelected,
  ) onItemClicked;
  final bool checkboxSelected;
  final String Function(int index) getCharacterFontName;
  final Widget Function(int index) getCharacterFontImage;
  final Widget exclusiveScriptButton;
  @override
  Widget build(BuildContext context) => ListView.builder(
        controller: controller,
        itemCount: 1,
        itemBuilder: (context, index) => Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 20),
            if (isExclusiveScriptType)
              ...() {
                List<Widget> widget = [];
                widget.addAll(
                  [
                    SizedBox(
                      height: 101,
                      width: 717,
                      child: exclusiveScriptButton,
                    ),
                    const SizedBox(height: 5)
                  ],
                );
                return widget;
              }(),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: isExclusiveScriptType
                  ? characterFontImagesLength - 1
                  : characterFontImagesLength,
              cacheExtent: 524,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: 5,
                mainAxisSpacing: 5,
                mainAxisExtent: 101,
              ),
              itemBuilder: (context, index) {
                if (isCharacterFontName(index)) {
                  return Container();
                }

                ///
                /// 上部の選択ボックスにチェックを入れてPOPを入力し、
                /// それ以外の場合は次のインターフェイスに直接移動します
                ///
                return grp_btn_thumbnail(
                  feedBackControl: null,
                  onTap: () => onItemClicked(
                    context,
                    index,
                    checkboxSelected,
                  ),
                  child: Stack(
                    children: [
                      Center(child: getCharacterFontImage(index)),
                      Column(
                        children: [
                          const Spacer(flex: 4),
                          Expanded(
                            flex: 22,
                            child: Row(
                              children: [
                                const Spacer(flex: 4),
                                Expanded(
                                  flex: 85,
                                  child: Text(
                                    getCharacterFontName(index),
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontFamily: "Roboto",
                                      fontSize: 24,
                                      height: 1,
                                      decoration: TextDecoration.none,
                                    ),
                                    textAlign: TextAlign.left,
                                  ),
                                ),
                                const Spacer(flex: 85),
                              ],
                            ),
                          ),
                          const Spacer(flex: 75),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      );
}
