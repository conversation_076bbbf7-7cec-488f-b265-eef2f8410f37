import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_e_stitch_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'line_e_stitch_flip_popup_view_interface.dart';

final lineEStitchFlipViewModelProvider = StateNotifierProvider.autoDispose<
    LineEStitchFlipStateViewInterface,
    LineEStitchFlipState>((ref) => LineEStitchFlipViewModel(ref));

class LineEStitchFlipViewModel extends LineEStitchFlipStateViewInterface {
  LineEStitchFlipViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            LineEStitchFlipState(
              flipInsideState: LineEStitchModel().getFlip() ==
                      LineEStitchModel.sideNotUpdating
                  ? false
                  : LineEStitchModel().getFlip() == FlipSide.flip_inside
                      ? true
                      : false,
              flipOutsideState: LineEStitchModel().getFlip() ==
                      LineEStitchModel.sideNotUpdating
                  ? false
                  : LineEStitchModel().getFlip() == FlipSide.flip_outside
                      ? true
                      : false,
            ),
            ref);

  ///
  /// 向き値
  ///
  FlipSide _flipValue = LineEStitchModel().getFlip();

  ///
  /// 選択した側があります
  ///
  bool _hasSelectedSide =
      LineEStitchModel().getFlip() == LineEStitchModel.sideNotUpdating;

  @override
  void onInsideClicked() {
    if (state.flipInsideState) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    _hasSelectedSide = true;
    _flipValue = FlipSide.flip_inside;
    state = state.copyWith(flipInsideState: true, flipOutsideState: false);
  }

  @override
  void onOutsideClicked() {
    if (state.flipOutsideState) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    _hasSelectedSide = true;
    _flipValue = FlipSide.flip_outside;
    state = state.copyWith(flipInsideState: false, flipOutsideState: true);
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineEStitchFlip.toString());
    if (_hasSelectedSide == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    FlipSide flipValue = LineEStitchModel().getFlip();

    /// Model 更新
    LineEStitchModel().setFlip(_flipValue);
    if (LineEStitchModel().setMdcEStitchSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (flipValue != _flipValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }
}
