package com.example.customscreensaver

import PathAdapter
import android.annotation.SuppressLint
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.RemoteException
import android.text.TextUtils
import android.util.Log
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import androidx.activity.ComponentActivity
import androidx.annotation.RequiresApi
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.brother.ph.iivo.screensaver.BuildConfig
import com.brother.ph.iivo.screensaver.R


class MainActivity : ComponentActivity() {

  private companion object {

    /// スクリーンセーバーが中断されたことを通知するブロードキャストのアクション
    const val ACTION_SCREEN_SAVER_INTERRUPTED =
      "${BuildConfig.APPLICATION_ID}.action.SCREEN_SAVER_INTERRUPTED"

    /// スクリーンセーバーが準備完了したことを通知するブロードキャストのアクション
    const val ACTION_SCREEN_SAVER_PREPARED =
      "${BuildConfig.APPLICATION_ID}.action.SCREEN_SAVER_PREPARED"

    ///  ログ識別のための TAG 定数の定義
    private const val TAG = "ScreenSaverActivity"
  }

  private lateinit var recyclerView: RecyclerView

  private val mReceiver = MyReceiver()

  private var pathAdapter: PathAdapter? = null

  @RequiresApi(Build.VERSION_CODES.TIRAMISU)
  @SuppressLint("DiscouragedPrivateApi")
  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    val intentFilter = IntentFilter()
    intentFilter.addAction("finish_app")

    registerReceiver(mReceiver, intentFilter, RECEIVER_EXPORTED)

    /// ステータスバーとナビゲーションバーを隠す
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
      /// API レベル 30 以上では WindowInsetsController を使用できます
      window.insetsController?.let {
        it.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
        it.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
      }
    } else {
      // 下位の API レベルでは、古いメソッドが使用されます
      @Suppress("DEPRECATION")
      window.decorView.systemUiVisibility = (
        View.SYSTEM_UI_FLAG_FULLSCREEN
          or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
          or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
          or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
          or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
          or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        )
    }

    val intent = intent
    val action = intent.action
    val data: Uri? = intent.data

    ///  データが setData を通過しているかどうかを確認する
    if (Intent.ACTION_VIEW == action && data != null) {
      /// 受信したデータURIを出力する
      Log.d(TAG, "Received Data Uri: $data")
      /// ... 受信したデータの処理
    }

    /// データがputextraを介して渡されるかどうかを確認します
    val extraData: String? = intent.getStringExtra("message")

    val items = extraData?.split(",")
    val itemList = items?.toList()

/// リストの内容を印刷する
    if (itemList != null) {
      for (item in itemList) {
        Log.d(TAG, "$item")
      }
    }

    if (!TextUtils.isEmpty(extraData)) {
      // 受信した追加データを印刷します
      Log.d(TAG, "Received Extra Data: $extraData")
      // ... 受信した追加データを処理する
    }

    // 追加のインテント情報も印刷する必要がある場合は、印刷できます
    val extras = intent.extras
    extras?.let { bundle ->
      for (key in bundle.keySet()) {
        val value = bundle[key]
        Log.d(TAG, "Extra Data: $key = $value")
      }
    }
    setContentView(R.layout.activity_main)

    recyclerView = findViewById(R.id.recyclerView)
    recyclerView.layoutManager = LinearLayoutManager(this)
    pathAdapter = itemList?.let { paths ->
      PathAdapter(paths, object : PathAdapter.OnPathClickListener {
        override fun onPathClick(position: Int) {

        }
      })
    } ?: run {

      recyclerView.setBackgroundColor(Color.BLACK)
      null
    }
    recyclerView.adapter = pathAdapter
    pathAdapter?.startImageLoop()

    runCatching {
      sendBroadcast(Intent(ACTION_SCREEN_SAVER_PREPARED))
    }
  }

  /**
   * 現状のアプリだとSSキーとかをキーボードのキーと同じ扱いをするようになっているので、
   * として、SS/TH/NP等のキーを無効化して
   * https://developer.android.com/reference/android/view/KeyEvent#KEYCODE_BUTTON_3
   */
  @SuppressLint("RestrictedApi")
  override fun dispatchKeyEvent(event: KeyEvent): Boolean {
    if (event.keyCode in 189..195) {
      return true
    }
    return super.dispatchKeyEvent(event)
  }

  override fun onDestroy() {
    super.onDestroy()
    unregisterReceiver(mReceiver)
    pathAdapter?.dispose()
  }

  /**
   * マウスイベントをリッスンする
   */
  override fun dispatchGenericMotionEvent(event: MotionEvent): Boolean {
    try {
      sendBroadcast(Intent(ACTION_SCREEN_SAVER_INTERRUPTED))
    } catch (t: RemoteException) {
      // ignored
    } catch (t: Throwable) {
      Log.e(TAG, "Failed to send broadcast: ACTION_SCREEN_SAVER_INTERRUPTED", t)
    }
    return super.dispatchGenericMotionEvent(event)
  }

}


