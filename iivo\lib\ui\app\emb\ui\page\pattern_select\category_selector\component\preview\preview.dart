import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../common_component/preview_custom_painter.dart'
    show GridPainter, PointsPainter, PathPainter, MaskPainter;
import 'preview_view_model.dart';

///
/// 刺しゅうパターン選択画面 の 展示エリア
///
class Preview extends ConsumerStatefulWidget {
  const Preview({
    super.key,
    this.isQuiltPattern = false,
    this.quiltImage,
    required this.patternDisplayInfoList,
    required this.temporaryGroupDisplayInfoList,
  });
  final List<PatternViewDisplayInfo> patternDisplayInfoList;
  final List<PatternViewDisplayInfo> temporaryGroupDisplayInfoList;
  final Widget? quiltImage;
  final bool isQuiltPattern;

  @override
  ConsumerState<Preview> createState() => _PreviewState();
}

class _PreviewState extends ConsumerState<Preview> {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(previewViewModelProvider);
    final viewModel = ref.read(previewViewModelProvider.notifier);

    return ClipRect(
      clipBehavior: Clip.antiAlias,
      child: Transform.scale(
        scale: viewModel.getMagnification,
        child: ColoredBox(
          color: state.backgroundColor,
          child: Stack(
            children: [
              /// 背景スキャンの画像
              widget.isQuiltPattern
                  ? Container()
                  : IgnorePointer(
                      child: Container(
                        alignment: Alignment.center,
                        width: viewModel.getPreviewSize.dx,
                        height: viewModel.getPreviewSize.dy,
                        child: state.backgroundImage ?? Container(),
                      ),
                    ),

              /// すべてのpatternを生成
              ...() {
                List<Widget> patternWidget = [];

                ///  quiltPattern見せる
                if (widget.isQuiltPattern == true) {
                  patternWidget.add(Center(child: widget.quiltImage));
                  return patternWidget;
                }

                widget.patternDisplayInfoList
                    .asMap()
                    .entries
                    .forEach((pattern) {
                  PatternViewDisplayInfo patternInfo = pattern.value;
                  patternWidget.add(
                    Positioned(
                      top: patternInfo.top,
                      left: patternInfo.left,
                      width: patternInfo.width,
                      height: patternInfo.height,
                      child: Stack(
                        children: [
                          /// すべてのBorderを生成
                          ...() {
                            List<Widget> borderWidget = [];
                            patternInfo.borderDisplayInfoList
                                .asMap()
                                .entries
                                .forEach((border) {
                              EmbBorderViewDisplayInfo borderInfo =
                                  border.value;
                              borderWidget.add(
                                Positioned(
                                  top: borderInfo.top,
                                  left: borderInfo.left,
                                  width: borderInfo.width,
                                  height: borderInfo.height,
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      /// すべてのgroupを生成
                                      ...() {
                                        List<Widget> groupWidget = [];
                                        borderInfo.groupDisplayInfoList
                                            .asMap()
                                            .entries
                                            .forEach((group) {
                                          EmbGroupViewDisplayInfo groupInfo =
                                              group.value;
                                          String groupKey =
                                              group.key.toString();

                                          groupWidget.add(
                                            /// patternの表示領域(Maskと赤いドットを含む)
                                            Positioned(
                                              top: groupInfo.top,
                                              left: groupInfo.left,
                                              width: groupInfo.width,
                                              height: groupInfo.height,

                                              /// group
                                              child: Stack(
                                                children: [
                                                  /// すべてのEmbPatternを生成
                                                  ...() {
                                                    List<Widget> widget = [];
                                                    groupInfo
                                                        .embPatternDisplayInfoList
                                                        .asMap()
                                                        .entries
                                                        .forEach((embPattern) {
                                                      EmbPatternViewDisplayInfo
                                                          embPatternInfo =
                                                          embPattern.value;
                                                      String embPatternKey =
                                                          embPattern.key
                                                              .toString();

                                                      /// EmbPatternの画像
                                                      if (embPatternInfo
                                                              .displayImage !=
                                                          null) {
                                                        widget.add(
                                                          Positioned(
                                                            key: Key(
                                                                "group${groupKey}embPattern$embPatternKey"),
                                                            top: embPatternInfo
                                                                .imageTop,
                                                            left: embPatternInfo
                                                                .imageLeft,
                                                            width:
                                                                embPatternInfo
                                                                    .imageWidth,
                                                            height:
                                                                embPatternInfo
                                                                    .imageHeight,
                                                            child: Image.memory(
                                                                embPatternInfo
                                                                    .displayImage!),
                                                          ),
                                                        );
                                                      } else {
                                                        /// do noting
                                                      }

                                                      if (groupInfo
                                                          .isAllNotSewing) {
                                                        widget.add(
                                                          Positioned(
                                                            top: embPatternInfo
                                                                .top,
                                                            left: embPatternInfo
                                                                .left,
                                                            width:
                                                                embPatternInfo
                                                                    .width,
                                                            height:
                                                                embPatternInfo
                                                                    .height,
                                                            child:
                                                                IgnorePointer(
                                                              child:
                                                                  CustomPaint(
                                                                painter:
                                                                    MaskPainter(
                                                                  isDashedLine:
                                                                      groupInfo
                                                                          .isAllNotSewing,
                                                                  maskColor:
                                                                      const Color
                                                                          .fromARGB(
                                                                          255,
                                                                          235,
                                                                          0,
                                                                          0),
                                                                  strokeWidth: 1 /
                                                                      viewModel
                                                                          .getMagnification,
                                                                  maskTopLeft:
                                                                      embPatternInfo
                                                                          .mask
                                                                          .topLeft,
                                                                  maskTopRight:
                                                                      embPatternInfo
                                                                          .mask
                                                                          .topRight,
                                                                  maskBottomLeft:
                                                                      embPatternInfo
                                                                          .mask
                                                                          .bottomLeft,
                                                                  maskBottomRight:
                                                                      embPatternInfo
                                                                          .mask
                                                                          .bottomRight,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        );
                                                      } else {
                                                        /// Do nothing
                                                      }
                                                    });
                                                    return widget;
                                                  }(),
                                                ],
                                              ),
                                            ),
                                          );
                                        });
                                        return groupWidget;
                                      }(),
                                    ],
                                  ),
                                ),
                              );
                            });
                            return borderWidget;
                          }(),
                        ],
                      ),
                    ),
                  );

                  /// 文字の円弧下線イメージ
                  patternWidget.add(
                    IgnorePointer(
                      ignoring: true,
                      child: Stack(
                        children: [
                          /// すべてのBorderを生成
                          ...() {
                            List<Widget> borderWidget = [];
                            patternInfo.borderDisplayInfoList
                                .asMap()
                                .entries
                                .forEach(
                              (border) {
                                EmbBorderViewDisplayInfo borderInfo =
                                    border.value;
                                borderWidget.add(
                                  Stack(alignment: Alignment.center, children: [
                                    /// すべてのgroupを生成
                                    ...() {
                                      List<Widget> groupWidget = [];
                                      borderInfo.groupDisplayInfoList
                                          .asMap()
                                          .entries
                                          .forEach((group) {
                                        EmbGroupViewDisplayInfo groupInfo =
                                            group.value;

                                        /// 文字の円弧下線イメージ
                                        if (groupInfo.arcImage != null) {
                                          groupWidget.add(
                                            Image.memory(
                                              groupInfo.arcImage!,
                                              width:
                                                  viewModel.getPreviewSize.dx,
                                              height:
                                                  viewModel.getPreviewSize.dy,
                                              fit: BoxFit.cover,
                                              alignment: Alignment.center,
                                              gaplessPlayback: false,
                                            ),
                                          );
                                        } else {
                                          /// do nothing
                                        }
                                      });

                                      return groupWidget;
                                    }(),
                                  ]),
                                );
                              },
                            );

                            return borderWidget;
                          }(),
                        ],
                      ),
                    ),
                  );
                });

                widget.temporaryGroupDisplayInfoList
                    .asMap()
                    .entries
                    .forEach((pattern) {
                  PatternViewDisplayInfo patternInfo = pattern.value;

                  patternWidget.add(
                    Positioned(
                      top: patternInfo.top,
                      left: patternInfo.left,
                      width: patternInfo.width,
                      height: patternInfo.height,
                      child: Stack(
                        children: [
                          /// すべてのBorderを生成
                          ...() {
                            List<Widget> borderWidget = [];
                            patternInfo.borderDisplayInfoList
                                .asMap()
                                .entries
                                .forEach((border) {
                              EmbBorderViewDisplayInfo borderInfo =
                                  border.value;
                              borderWidget.add(
                                Positioned(
                                  top: borderInfo.top,
                                  left: borderInfo.left,
                                  width: borderInfo.width,
                                  height: borderInfo.height,
                                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      /// すべてのgroupを生成
                                      ...() {
                                        List<Widget> groupWidget = [];
                                        borderInfo.groupDisplayInfoList
                                            .asMap()
                                            .entries
                                            .forEach((group) {
                                          EmbGroupViewDisplayInfo groupInfo =
                                              group.value;
                                          String groupKey =
                                              group.key.toString();

                                          groupWidget.add(
                                            /// patternの表示領域(Maskと赤いドットを含む)
                                            Positioned(
                                              top: groupInfo.top,
                                              left: groupInfo.left,
                                              width: groupInfo.width,
                                              height: groupInfo.height,

                                              /// group
                                              child: Stack(
                                                children: [
                                                  /// すべてのEmbPatternを生成
                                                  ...() {
                                                    List<Widget> widget = [];
                                                    groupInfo
                                                        .embPatternDisplayInfoList
                                                        .asMap()
                                                        .entries
                                                        .forEach((embPattern) {
                                                      EmbPatternViewDisplayInfo
                                                          embPatternInfo =
                                                          embPattern.value;
                                                      String embPatternKey =
                                                          embPattern.key
                                                              .toString();

                                                      /// EmbPatternの画像
                                                      if (embPatternInfo
                                                              .displayImage !=
                                                          null) {
                                                        widget.add(
                                                          Positioned(
                                                            key: Key(
                                                                "temporaryGroup${groupKey}embPattern$embPatternKey"),
                                                            top: embPatternInfo
                                                                .imageTop,
                                                            left: embPatternInfo
                                                                .imageLeft,
                                                            width:
                                                                embPatternInfo
                                                                    .imageWidth,
                                                            height:
                                                                embPatternInfo
                                                                    .imageHeight,
                                                            child: Image.memory(
                                                                embPatternInfo
                                                                    .displayImage!),
                                                          ),
                                                        );
                                                      } else {
                                                        /// do noting
                                                      }

                                                      /// EmbPattern Mask
                                                      bool
                                                          isDisplayEmbPatternMask =
                                                          false;
                                                      bool isDashedLine = false;
                                                      if (groupInfo
                                                          .isAllNotSewing) {
                                                        isDisplayEmbPatternMask =
                                                            true;
                                                        if (patternInfo
                                                                .isGroup &&
                                                            patternInfo
                                                                .isCurrentPattern &&
                                                            patternInfo
                                                                    .isSelected ==
                                                                false) {
                                                          isDashedLine = false;
                                                        } else {
                                                          isDashedLine = groupInfo
                                                              .isAllNotSewing;
                                                        }
                                                      } else {
                                                        isDisplayEmbPatternMask =
                                                            patternInfo.isSelected ==
                                                                    false &&
                                                                (patternInfo
                                                                        .isGroup &&
                                                                    patternInfo
                                                                        .isCurrentPattern);
                                                      }

                                                      if (isDisplayEmbPatternMask) {
                                                        widget.add(
                                                          Positioned(
                                                            top: embPatternInfo
                                                                .top,
                                                            left: embPatternInfo
                                                                .left,
                                                            width:
                                                                embPatternInfo
                                                                    .width,
                                                            height:
                                                                embPatternInfo
                                                                    .height,
                                                            child:
                                                                IgnorePointer(
                                                              child:
                                                                  CustomPaint(
                                                                painter:
                                                                    MaskPainter(
                                                                  isDashedLine:
                                                                      isDashedLine,
                                                                  maskColor:
                                                                      const Color
                                                                          .fromARGB(
                                                                          255,
                                                                          235,
                                                                          0,
                                                                          0),
                                                                  strokeWidth: 1 /
                                                                      viewModel
                                                                          .getMagnification,
                                                                  maskTopLeft:
                                                                      embPatternInfo
                                                                          .mask
                                                                          .topLeft,
                                                                  maskTopRight:
                                                                      embPatternInfo
                                                                          .mask
                                                                          .topRight,
                                                                  maskBottomLeft:
                                                                      embPatternInfo
                                                                          .mask
                                                                          .bottomLeft,
                                                                  maskBottomRight:
                                                                      embPatternInfo
                                                                          .mask
                                                                          .bottomRight,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        );
                                                      } else {
                                                        /// Do nothing
                                                      }
                                                    });
                                                    return widget;
                                                  }(),
                                                ],
                                              ),
                                            ),
                                          );
                                        });
                                        return groupWidget;
                                      }(),
                                    ],
                                  ),
                                ),
                              );
                            });

                            return borderWidget;
                          }(),
                        ],
                      ),
                    ),
                  );

                  /// 文字の円弧下線イメージ
                  patternWidget.add(
                    IgnorePointer(
                      ignoring: true,
                      child: Stack(
                        children: [
                          /// すべてのBorderを生成
                          ...() {
                            List<Widget> borderWidget = [];
                            patternInfo.borderDisplayInfoList
                                .asMap()
                                .entries
                                .forEach(
                              (border) {
                                EmbBorderViewDisplayInfo borderInfo =
                                    border.value;
                                borderWidget.add(
                                  Stack(alignment: Alignment.center, children: [
                                    /// すべてのgroupを生成
                                    ...() {
                                      List<Widget> groupWidget = [];
                                      borderInfo.groupDisplayInfoList
                                          .asMap()
                                          .entries
                                          .forEach((group) {
                                        EmbGroupViewDisplayInfo groupInfo =
                                            group.value;

                                        /// 文字の円弧下線イメージ
                                        if (groupInfo.arcImage != null) {
                                          groupWidget.add(
                                            Image.memory(
                                              groupInfo.arcImage!,
                                              width:
                                                  viewModel.getPreviewSize.dx,
                                              height:
                                                  viewModel.getPreviewSize.dy,
                                              fit: BoxFit.cover,
                                              alignment: Alignment.center,
                                              gaplessPlayback: false,
                                            ),
                                          );
                                        } else {
                                          /// do nothing
                                        }
                                      });

                                      return groupWidget;
                                    }(),
                                  ]),
                                );
                              },
                            );

                            return borderWidget;
                          }(),
                        ],
                      ),
                    ),
                  );

                  /// Mask
                  if (patternInfo.isBorder && patternInfo.isCurrentPattern) {
                    patternWidget.add(
                      IgnorePointer(
                        child: SizedBox(
                          width: patternInfo.width,
                          height: patternInfo.height,
                          child: CustomPaint(
                            painter: MaskPainter(
                              maskColor: state.maskColor,
                              strokeWidth: 1 / viewModel.getMagnification,
                              maskTopLeft: patternInfo.maskDisplayInfo.topLeft,
                              maskTopRight:
                                  patternInfo.maskDisplayInfo.topRight,
                              maskBottomLeft:
                                  patternInfo.maskDisplayInfo.bottomLeft,
                              maskBottomRight:
                                  patternInfo.maskDisplayInfo.bottomRight,
                            ),
                          ),
                        ),
                      ),
                    );
                  } else {
                    /// Do nothing
                  }
                });

                return patternWidget;
              }(),

              /// Grid
              widget.isQuiltPattern
                  ? Container()
                  : IgnorePointer(
                      child: SizedBox(
                        width: viewModel.getPreviewSize.dx,
                        height: viewModel.getPreviewSize.dy,
                        child: CustomPaint(
                          foregroundPainter: GridPainter(
                            gridColor: state.gridColor,
                            zoomValue: viewModel.getMagnification,
                            gridTypeIndex: state.gridTypeIndex,
                            verticalList: viewModel.getGridVerticalList(),
                            horizontalList: viewModel.getGridHorizontalList(),
                          ),
                          painter: PointsPainter(
                            pathColor: const Color.fromARGB(255, 0, 0, 0),
                            strokeWidth: 1 / viewModel.getMagnification,
                            points: state.blackPoints,
                          ),
                        ),
                      ),
                    ),

              /// frame
              widget.isQuiltPattern
                  ? Container()
                  : IgnorePointer(
                      child: SizedBox(
                        width: viewModel.getPreviewSize.dx,
                        height: viewModel.getPreviewSize.dy,
                        child: CustomPaint(
                          painter: PathPainter(
                            color: state.frameColor,
                            strokeWidth: 1 / viewModel.getMagnification,
                            path: state.frameDrawPath,
                          ),
                        ),
                      ),
                    ),

              /// 拡大後、Previewのドラッグ領域
              SizedBox(
                width: viewModel.getPreviewSize.dx,
                height: viewModel.getPreviewSize.dy,
                child: GestureDetector(
                  onScaleStart: (details) =>
                      viewModel.dargPreviewStart(details),
                  onScaleUpdate: (details) => viewModel.dargPreview(details),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
