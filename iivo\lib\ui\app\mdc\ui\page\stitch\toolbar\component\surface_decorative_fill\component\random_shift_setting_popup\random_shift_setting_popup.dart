import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'random_shift_setting_popup_view_model.dart';

class RandomShiftSettingPopup extends ConsumerStatefulWidget {
  const RandomShiftSettingPopup({super.key});

  @override
  ConsumerState<RandomShiftSettingPopup> createState() =>
      _RandomShiftSettingPopupState();
}

class _RandomShiftSettingPopupState
    extends ConsumerState<RandomShiftSettingPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(randomShiftSettingPopupViewModelProvider);
    final viewModel =
        ref.read(randomShiftSettingPopupViewModelProvider.notifier);

    return Column(
      children: [
        const Spacer(flex: 159),
        Expanded(
          flex: 1052,
          child: Material(
            color: Colors.transparent,
            child: Row(
              children: [
                const Spacer(flex: 571),
                Expanded(
                  flex: 229,
                  child: Stack(
                    children: [
                      const pre_edit_toolbar(),
                      Column(
                        children: [
                          const Spacer(flex: 37),
                          const Expanded(
                            flex: 68,
                            child: Row(
                              children: [
                                Spacer(flex: 52),
                                Expanded(
                                  flex: 126,
                                  child: ico_randamshift(),
                                ),
                                Spacer(flex: 51),
                              ],
                            ),
                          ),
                          const Spacer(flex: 24),
                          Expanded(
                            flex: 69,
                            child: Row(
                              children: [
                                const Spacer(flex: 12),
                                Expanded(
                                  flex: 205,
                                  child: grp_str_randomshift_mdc(
                                    text: l10n.icon_00565,
                                  ),
                                ),
                                const Spacer(flex: 12),
                              ],
                            ),
                          ),
                          const Spacer(flex: 8),
                          Expanded(
                            flex: 69,
                            child: Row(
                              children: [
                                const Spacer(flex: 12),

                                /// ゆらぎ程度値表示
                                Expanded(
                                  flex: 205,
                                  child: grp_str_parameter_01(
                                    text: state.shiftValue,
                                    isDefaultValue:
                                        viewModel.isDefaultShiftValue,
                                  ),
                                ),
                                const Spacer(flex: 12),
                              ],
                            ),
                          ),
                          const Spacer(flex: 12),
                          Expanded(
                            flex: 63,
                            child: Row(
                              children: [
                                const Spacer(flex: 48),

                                /// 「-」ボタン
                                Expanded(
                                  flex: 63,
                                  child: grp_btn_minus_01(
                                    onTap: () => viewModel
                                        .onShiftMinusButtonClicked(false),
                                    onLongPress: () => viewModel
                                        .onShiftMinusButtonClicked(true),
                                    state: state.isShiftMinButtonGrey
                                        ? ButtonState.disable
                                        : ButtonState.normal,
                                    feedBackControl: null,
                                  ),
                                ),
                                const Spacer(flex: 8),

                                /// 「+」ボタン
                                Expanded(
                                  flex: 63,
                                  child: grp_btn_plus_01(
                                    onTap: () => viewModel
                                        .onShiftPlusButtonClicked(false),
                                    onLongPress: () => viewModel
                                        .onShiftPlusButtonClicked(true),
                                    state: state.isShiftPlusButtonGrey
                                        ? ButtonState.disable
                                        : ButtonState.normal,
                                    feedBackControl: null,
                                  ),
                                ),
                                const Spacer(flex: 47),
                              ],
                            ),
                          ),
                          const Spacer(flex: 60),
                          Expanded(
                            flex: 69,
                            child: Row(
                              children: [
                                const Spacer(flex: 12),

                                /// ゆらぎ程度値表示
                                Expanded(
                                  flex: 205,
                                  child: grp_str_parameter_01(
                                    text: viewModel.getTypeString(context),
                                    textColor: viewModel.needGrayTextColor
                                        ? const Color(0xFF999999)
                                        : null,
                                    isDefaultValue:
                                        viewModel.isDefaultTypeValue,
                                  ),
                                ),
                                const Spacer(flex: 12),
                              ],
                            ),
                          ),
                          const Spacer(flex: 12),
                          Expanded(
                            flex: 63,
                            child: Row(
                              children: [
                                const Spacer(flex: 48),

                                /// 「<」ボタン
                                Expanded(
                                  flex: 63,
                                  child: grp_btn_move_center_left_02(
                                    onTap: () => viewModel
                                        .onTypeLeftButtonClicked(false),
                                    onLongPress: () =>
                                        viewModel.onTypeLeftButtonClicked(true),
                                    state: state.isTypeLeftButtonGrey
                                        ? ButtonState.disable
                                        : ButtonState.normal,
                                    feedBackControl: null,
                                  ),
                                ),
                                const Spacer(flex: 8),

                                /// 「>」ボタン
                                Expanded(
                                  flex: 63,
                                  child: grp_btn_move_center_right_02(
                                    onTap: () => viewModel
                                        .onTypeRightButtonClicked(false),
                                    onLongPress: () => viewModel
                                        .onTypeRightButtonClicked(true),
                                    state: state.isTypeRightButtonGrey
                                        ? ButtonState.disable
                                        : ButtonState.normal,
                                    feedBackControl: null,
                                  ),
                                ),
                                const Spacer(flex: 47),
                              ],
                            ),
                          ),
                          const Spacer(flex: 416),
                          Expanded(
                            flex: 70,
                            child: Row(
                              children: [
                                const Spacer(flex: 12),

                                /// 「OK」ボタン
                                Expanded(
                                  flex: 205,
                                  child: grp_btn_positive(
                                    style: ThemeButton.btn_n_size205x70_theme1,
                                    onTap: viewModel.onOkButtonClicked,
                                    text: l10n.icon_ok,
                                    feedBackControl: null,
                                  ),
                                ),
                                const Spacer(flex: 12),
                              ],
                            ),
                          ),
                          const Spacer(flex: 12),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        const Spacer(flex: 69),
      ],
    );
  }
}
