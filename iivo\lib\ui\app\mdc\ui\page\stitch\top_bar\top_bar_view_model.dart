import 'dart:async';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../model/projector_model.dart';
import '../../../../../../page_route/page_route.dart';
import '../../../../model/paint/scan_model.dart';
import '../../../../model/paint/top_bar_model.dart' as paint;
import '../../../../model/stitch/creation_isolate.dart';
import '../../../../model/stitch/draw_region_model.dart';
import '../../../../model/stitch/magnification_model.dart';
import '../../../../model/stitch/view_area_model.dart';
import '../../../component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../../../component/mdc_header/mdc_header_view_model.dart';
import '../projector/camera_pen/camera_pen_view_model.dart';
import '../projector/projector_setting_popup/projector_settings_popup.dart';
import '../stitch_page_view_model.dart';
import 'component/information_popup/information_popup.dart';
import 'top_bar_view_interface.dart';

final topBarViewModelProvider =
    StateNotifierProvider.autoDispose<TopBarViewInterface, TopBarState>(
        (ref) => TopBarViewModel(ref));

class TopBarViewModel extends TopBarViewInterface {
  TopBarViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const TopBarState(), ref);

  @override
  void build() {
    super.build();

    /// view更新
    state = state.copyWith(
      informationButtonState: isProcessImage || CreationModel().hasErrorCode
          ? ButtonState.disable
          : ButtonState.normal,
      projectorButtonState: _getProjectorButtonState(),
      densityLevelIndex: paint.TopBarModel().densityLevelIndex,
    );
  }

  @override
  bool get isProcessImage => DrawRegionModel().isProcessImage;

  ///
  /// 下絵を取り込むかどうかの判断
  ///
  @override
  bool get isDensityShow => ScanModel().isLoadScanImage;

  ///
  /// 拡大レベルの取得
  ///
  final List<Widget> _getMagnificationLevel = [
    const ico_zoom100(),
    const ico_zoom200(),
    const ico_zoom400(),
  ];
  @override
  Widget get getMagnificationLevel => _getMagnificationLevel[
      MagnificationModel().valueToIndex(MagnificationModel.magnificationLevel)];

  ///
  /// View更新
  ///
  @override
  void update() {
    state = state.copyWith(
      dragMoveButtonState: _getDragMoveState(),
      isDensityShow: ScanModel().isLoadScanImage,
      densityLevelIndex: paint.TopBarModel().densityLevelIndex,
      isProjectorON: ProjectorModel().mdcProjector.isMdcProjectorViewOpen,
      informationButtonState: isProcessImage || CreationModel().hasErrorCode
          ? ButtonState.disable
          : ButtonState.normal,
      projectorButtonState: _getProjectorButtonState(),
    );
  }

  ///
  /// projectorButton状態更新
  ///
  @override
  void updateProjectorButtonState() {
    state = state.copyWith(
      projectorButtonState: _getProjectorButtonState(),
    );
  }

  ///
  /// インフォメーションボタンをクリックする
  ///
  @override
  void onInformationButtonClicked(BuildContext context) {
    if (isProcessImage || CreationModel().hasErrorCode) {
      state = state.copyWith(informationButtonState: ButtonState.disable);
      return;
    }
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    state = state.copyWith(informationButtonState: ButtonState.normal);
    _openInformationPopup(context);
  }

  ///
  /// 下絵　濃いのクリック関数
  ///
  @override
  void onDensityAddButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    if (isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 最大時にクリックが無効です
    if (state.densityLevelIndex == DensityLevel.mdc0BackGround100.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    int densityLevelIndex = state.densityLevelIndex;
    (++densityLevelIndex).clamp(0, DensityLevel.values.length - 1);

    /// Model更新
    paint.TopBarModel().densityLevelIndex = densityLevelIndex;
    MdcLibrary().apiBinding.setMdcImageDrawingDensity(
        DensityLevel.values[densityLevelIndex].number);
    ScanModel().updateSketchesImage();

    /// View更新
    state = state.copyWith(densityLevelIndex: densityLevelIndex);

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updatePageByChild(ComponentType.topBar);
  }

  ///
  /// 下絵　薄いのクリック関数
  ///
  @override
  void onDensityReduceButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    if (isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 最小時にクリックが無効です
    if (state.densityLevelIndex == DensityLevel.mdc100BackGround0.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    int densityLevelIndex = state.densityLevelIndex;
    (--densityLevelIndex).clamp(0, DensityLevel.values.length - 1);

    /// Model更新
    paint.TopBarModel().densityLevelIndex = densityLevelIndex;
    MdcLibrary().apiBinding.setMdcImageDrawingDensity(
        DensityLevel.values[densityLevelIndex].number);
    ScanModel().updateSketchesImage();

    /// View更新
    state = state.copyWith(densityLevelIndex: densityLevelIndex);

    ref
        .read(stitchPageViewModelProvider.notifier)
        .updatePageByChild(ComponentType.topBar);
  }

  ///
  /// 拡大倍率ボタンのクリック関数
  ///
  @override
  void onMagnificationButtonClicked(BuildContext context) {
    if (isProcessImage || CreationModel().hasErrorCode) {
      return;
    }
    String? name = ref
        .readAutoNotifierIfExists(stitchPageViewInfoProvider)
        ?.navigator
        .getTopPopupName();

    if (name != "PopupEnum.magnification" && name != null) {
      ref
          .readAutoNotifierIfExists(stitchPageViewInfoProvider)
          ?.navigator
          .maybeRemoveRoute(routeName: name);
    }
    _openMagnificationPopup(context);
  }

  ///
  /// ドラッグ移動ボタンがクリックされました
  ///
  @override
  void onDragMoveButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    if (isProcessImage || CreationModel().hasErrorCode) {
      state = state.copyWith(dragMoveButtonState: ButtonState.disable);
      return;
    }

    if (MagnificationModel.magnificationLevel ==
        MagnificationModel.magnification_100) {
      ViewAreaModel().isDragMove = false;
      return;
    }

    /// View更新
    if (state.dragMoveButtonState == ButtonState.normal) {
      state = state.copyWith(dragMoveButtonState: ButtonState.select);
      ViewAreaModel().isDragMove = true;
    } else {
      state = state.copyWith(dragMoveButtonState: ButtonState.normal);
      ViewAreaModel().isDragMove = false;
    }

    /// 他の画面を更新する
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updatePageByChild(ComponentType.topBar);
  }

  @override
  void onProjectorButtonClick(BuildContext context) {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    if (isProcessImage ||
        CreationModel().hasErrorCode ||
        state.projectorButtonState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// プロジェクト起動・停止前にエラーCheckをする
    /// エラーがあれば ポープアープを表示する
    final projectorFunction = ref.read(mdcProjectorFunctionProvider.notifier);
    final hasError = projectorFunction
        .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
    if (hasError) {
      return;
    }

    if (ProjectorModel().mdcProjector.isMdcProjectorViewOpen == false) {
      _openProjectorView(context);
    } else {
      /// 投影画面をオフにする
      _closeProjectorView();
    }
  }

  @override
  void onProjectorSettingButtonClick(BuildContext context) {
    if (isProcessImage || CreationModel().hasErrorCode) {
      return;
    }

    /// 投影設定ポップアップウィンドウを開く
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (_) => const ProjectorSettingPopup(),
        barrier: true,
      ),
    ).then(
      (_) {
        /// 他の画面を更新する
        ref
            .read(stitchPageViewModelProvider.notifier)
            .updatePageByChild(ComponentType.projector);
      },
    );
  }

  @override
  void onRealPreviewButtonClicked() {
    if (isProcessImage || CreationModel().hasErrorCode) {
      return;
    }
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.stitchRealPreview);
  }

  ///
  /// 投影中の画面に遷移
  ///
  Future<void> _openProjectorView(BuildContext context) async {
    /// Model更新
    MagnificationModel().reset();
    DrawRegionModel().updateDisplayImageInfo(DrawRegionModel().handle);
    ProjectorModel().mdcProjector.isMdcProjectorViewOpen = true;

    /// view更新
    state = state.copyWith(
      magnificationIndex: 0,
      isProjectorON: ProjectorModel().mdcProjector.isMdcProjectorViewOpen,
    );

    ///　プロジェクト投影起動
    final mdcProjectorFunction =
        ref.read(mdcProjectorFunctionProvider.notifier);
    mdcProjectorFunction.startMdcProjectorView(
      beforeFrameMoveCallback: () {
        ViewAreaModel().isDragMove = false;
        ref.read(mdcHeaderViewModelProvider.notifier).update();

        /// 他の画面を更新するt
        ref
            .read(stitchPageViewModelProvider.notifier)
            .updatePageByChild(ComponentType.projector);
      },
      removedFrameCallback: () {
        if (ProjectorModel().mdcProjector.isMdcBackgroundColorPopOpen) {
          /// 複数選択ポップアップウィンドウを閉じます
          PopupNavigator.pop(context: context);
        }
        if (ProjectorModel().mdcProjector.isMdcProjectorSettingsPopOpen) {
          /// 複数選択ポップアップウィンドウを閉じます
          PopupNavigator.pop(context: context);
        }
        if (ProjectorModel().mdcProjector.isMdcMemoryPopOpen) {
          /// 複数選択ポップアップウィンドウを閉じます
          PopupNavigator.pop(context: context);
        }

        /// Model更新
        ProjectorModel().mdcProjector.isMdcProjectorViewOpen = false;

        _updateByCloseProjector();
      },
    );
  }

  ///
  /// 投影中の専用画面に遷移
  ///
  Future<void> _closeProjectorView() async {
    final projectorFunction = ref.read(mdcProjectorFunctionProvider.notifier);
    projectorFunction.closeMdcProjectorView(
      closingHandleCallback: () {
        /// Model更新
        ProjectorModel().mdcProjector.isMdcProjectorViewOpen = false;

        _updateByCloseProjector();
      },
      afterClosedHandleCallback: () {},
    );
  }

  ///
  /// 拡大表示ポップアップを開く
  ///
  void _openMagnificationPopup(BuildContext context) {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .openPopupRoute(PopupEnum.magnification.toString(), () {});
  }

  ///
  /// パンツール状態取得する
  ///
  ButtonState _getDragMoveState() {
    if (isProcessImage || CreationModel().hasErrorCode) {
      return ButtonState.disable;
    }

    ButtonState dragMoveState;
    if (MagnificationModel.magnificationLevel ==
        MagnificationModel.magnification_100) {
      dragMoveState = ButtonState.disable;
    } else {
      if (ViewAreaModel().isDragMove == true) {
        dragMoveState = ButtonState.select;
      } else {
        dragMoveState = ButtonState.normal;
      }
    }
    return dragMoveState;
  }

  ///
  /// インフォメーションポップアップを開く
  ///
  void _openInformationPopup(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => const InformationPopup(),
        barrier: false,
      ),
    );
  }

  ///
  /// 画面更新（プロジェクト閉じる）
  ///
  void _updateByCloseProjector() {
    /// view更新
    /// カメラマン投影用画像更新
    update();
    ref.read(mdcCameraPenViewModelProvider.notifier).update();
    ref.read(mdcHeaderViewModelProvider.notifier).update();

    /// 他の画面を更新するt
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updatePageByChild(ComponentType.projector);
  }

  ///
  /// projector状態取得する
  ///
  ButtonState _getProjectorButtonState() => isProcessImage ||
          CreationModel().hasErrorCode ||
          ref.read(stitchPageViewModelProvider.notifier).hasPop == true
      ? ButtonState.disable
      : ButtonState.normal;
}
