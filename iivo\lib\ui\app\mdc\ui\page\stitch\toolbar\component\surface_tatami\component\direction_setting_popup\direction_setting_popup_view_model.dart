import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_tatami_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'direction_setting_popup_interface.dart';

final directionSettingPopupViewModelProvider =
    StateNotifierProvider.autoDispose<DirectionSettingPopupViewInterface,
            DirectionSettingPopupState>(
        (ref) => DirectionSettingPopupViewModel(ref));

class DirectionSettingPopupViewModel
    extends DirectionSettingPopupViewInterface {
  DirectionSettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const DirectionSettingPopupState(), ref) {
    _directionState = SurfaceTatamiModel().getDirection() !=
            SurfaceTatamiModel.directionNotUpdating
        ? TatamiSettingState.settingCompleted
        : TatamiSettingState.unknown;

    /// Directionの初期値
    _oldDirectionValue = SurfaceTatamiModel().getDirection();

    /// タタミ縫いの方向の初期値
    _oldDirectionType = _getDirectionAutoState();

    /// view更新
    update();
  }

  ///
  /// 最大幅値
  final int _maxDirectionValue = 165;

  ///
  /// 最小幅値
  ///
  final int _minDirectionValue = 0;

  ///
  /// タタミ縫いの方向値の状態
  ///
  TatamiSettingState _directionState = TatamiSettingState.settingCompleted;

  ///
  /// タタミ縫いの方向値
  ///
  int _directionValue = SurfaceTatamiModel().getDirection();

  ///
  /// Directionの初期値
  ///
  late final int _oldDirectionValue;

  ///
  /// タタミ縫いの方向ボタンの種類
  ///
  MDCTatamiDirKind _directionType = MDCTatamiDirKind.mdc_tatami_dir_kind_auto;

  ///
  /// タタミ縫いの方向の初期値
  ///
  late final MDCTatamiDirKind _oldDirectionType;

  ///
  /// ステップ量
  ///
  final int _stepValue = 15;

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    state = state.copyWith(
      directionValue: _getDirectionValue(),
      autoButtonState: _getDirectionAutoState(),
      isMainValue: _isMinValue(),
      isMaxValue: _isMaxValue(),
    );
  }

  ///
  /// 自動ボタンをクリックする
  ///
  @override
  void onAutoButtonClicked() {
    if (state.autoButtonState == MDCTatamiDirKind.mdc_tatami_dir_kind_auto) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    state = state.copyWith(
      autoButtonState: MDCTatamiDirKind.mdc_tatami_dir_kind_auto,
      directionValue: "",
    );
    _directionState = TatamiSettingState.change;
    _directionType = MDCTatamiDirKind.mdc_tatami_dir_kind_auto;
  }

  ///
  /// マニュアル設定ボタンをクリックする
  ///
  @override
  void onManualButtonClicked() {
    if (state.autoButtonState == MDCTatamiDirKind.mdc_tatami_dir_kind_manual) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    if (_directionState == TatamiSettingState.unknown) {
      _directionValue = SurfaceTatamiModel().directionDefaultValue;
    }

    if (_directionValue == SurfaceTatamiModel.autoDirectionValue) {
      _directionValue = SurfaceTatamiModel().directionDefaultValue;
    }

    state = state.copyWith(
      autoButtonState: MDCTatamiDirKind.mdc_tatami_dir_kind_manual,
      directionValue: _directionValue.toString(),
    );
    _directionState = TatamiSettingState.change;
    _directionType = MDCTatamiDirKind.mdc_tatami_dir_kind_manual;
  }

  ///
  /// マイナスボタンをクリックする
  ///
  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_directionState == TatamiSettingState.unknown) {
      _directionValue = SurfaceTatamiModel().directionDefaultValue;
    } else {
      _directionValue = _directionValue - _stepValue;
      if (_directionValue < _minDirectionValue) {
        _directionValue = _minDirectionValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }
    _directionState = TatamiSettingState.change;

    /// view更新
    state = state.copyWith(
      directionValue: _getDirectionValue(),
      isMaxValue: false,
      isMainValue: _directionValue == _minDirectionValue ? true : false,
    );

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }
    return true;
  }

  ///
  /// プラスボタンをクリックする
  ///
  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_directionState == TatamiSettingState.unknown) {
      _directionValue = SurfaceTatamiModel().directionDefaultValue;
    } else {
      _directionValue = _directionValue + _stepValue;
      if (_directionValue > _maxDirectionValue) {
        _directionValue = _maxDirectionValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }
    _directionState = TatamiSettingState.change;

    /// view更新
    state = state.copyWith(
      directionValue: _getDirectionValue(),
      isMaxValue: _directionValue == _maxDirectionValue ? true : false,
      isMainValue: false,
    );

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }
    return true;
  }

  ///
  /// OKボタンがクリックされました
  ///
  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.surfaceTatamiDirection.toString());
    if (_directionType == MDCTatamiDirKind.mdc_tatami_dir_kind_invalid) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    if (state.autoButtonState == MDCTatamiDirKind.mdc_tatami_dir_kind_auto) {
      /// Model更新
      SurfaceTatamiModel().setDirection(
          _directionValue, MDCTatamiDirKind.mdc_tatami_dir_kind_auto);
      _directionValue = SurfaceTatamiModel.autoDirectionValue;
    } else {
      /// Model更新
      SurfaceTatamiModel().setDirection(
          _directionValue, MDCTatamiDirKind.mdc_tatami_dir_kind_manual);
    }

    /// Lib更新
    if (SurfaceTatamiModel().setTatamiSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (_directionValue != _oldDirectionValue ||
        _directionType != _oldDirectionType) {
      ResumeHistoryModel().backSnapshot();
    } else {
      /// do nothing
    }

    _directionState = TatamiSettingState.settingCompleted;
    CreationModel().changeStitchCreation();
  }

  ///
  /// 画面表示Style Colorを取得する
  ///
  @override
  bool getDirectionTextStyle() {
    if (_directionState == TatamiSettingState.unknown) {
      return true;
    }

    if (_directionValue == SurfaceTatamiModel().directionDefaultValue) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// タミ縫いの方向値を取得する
  ///
  String _getDirectionValue() {
    if (_directionState == TatamiSettingState.unknown) {
      return SurfaceTatamiModel.differentDirectionValue;
    } else {
      return _directionValue.toString();
    }
  }

  ///
  /// タタミ縫いの方向ボタン情報を取得する
  ///
  MDCTatamiDirKind _getDirectionAutoState() {
    int direction = SurfaceTatamiModel().getDirection();
    MDCTatamiDirKind dirKind = SurfaceTatamiModel().getDirKind();
    if (dirKind == MDCTatamiDirKind.mdc_tatami_dir_kind_invalid) {
      return MDCTatamiDirKind.mdc_tatami_dir_kind_invalid;
    }
    if (direction != SurfaceTatamiModel.directionNotUpdating) {
      if (dirKind == MDCTatamiDirKind.mdc_tatami_dir_kind_auto) {
        return MDCTatamiDirKind.mdc_tatami_dir_kind_auto;
      } else {
        return MDCTatamiDirKind.mdc_tatami_dir_kind_manual;
      }
    } else {
      return MDCTatamiDirKind.mdc_tatami_dir_kind_invalid;
    }
  }

  ///
  /// 最大タタミ縫いの方向値の判断
  ///
  bool _isMaxValue() {
    if (_directionState == TatamiSettingState.unknown) {
      return false;
    } else {
      return _directionValue == _maxDirectionValue ? true : false;
    }
  }

  ///
  /// 最小タタミ縫いの方向値の判断
  ///
  bool _isMinValue() {
    if (_directionState == TatamiSettingState.unknown) {
      return false;
    } else {
      return _directionValue == _minDirectionValue ? true : false;
    }
  }
}
