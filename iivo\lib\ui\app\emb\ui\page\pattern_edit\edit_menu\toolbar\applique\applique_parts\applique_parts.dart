import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../common_component/preview_custom_painter.dart'
    show MaskPainter;
import 'applique_parts_view_model.dart';

class AppliquePartsPopup extends ConsumerStatefulWidget {
  const AppliquePartsPopup({super.key});

  @override
  ConsumerState<AppliquePartsPopup> createState() => _AppliquePartsPopupState();
}

class _AppliquePartsPopupState extends ConsumerState<AppliquePartsPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final viewModel = ref.read(appliquePartsViewModelProvider.notifier);
    final state = ref.watch(appliquePartsViewModelProvider);
    return Column(
      children: [
        const Spacer(flex: 71),
        Expanded(
          flex: 1148,
          child: Material(
            color: Colors.transparent,
            child: Column(
              children: [
                const Spacer(flex: 8),
                Expanded(
                  flex: 1132,
                  child: Row(
                    children: [
                      const Spacer(flex: 6),
                      Expanded(
                        flex: 788,
                        child: Stack(
                          children: [
                            const pic_popup_size788x1132(),
                            Column(
                              children: [
                                const Spacer(flex: 23),
                                Expanded(
                                  flex: 37,
                                  child: Row(
                                    children: [
                                      const Spacer(flex: 20),
                                      Expanded(
                                        flex: 646,
                                        child: grp_str_texture(
                                          text: l10n.icon_app_selectcolor,
                                        ),
                                      ),
                                      const Spacer(flex: 122),
                                    ],
                                  ),
                                ),
                                const Spacer(flex: 20),
                                Expanded(
                                  flex: 880,
                                  child: Row(
                                    children: [
                                      Expanded(
                                        flex: 564,
                                        child: pic_embroidery_mainpreview(
                                          child: Row(
                                            children: [
                                              const Spacer(flex: 1),
                                              Expanded(
                                                flex: 562,
                                                child: Stack(
                                                  children: [
                                                    /// Pattern
                                                    state.currentPatternInfo ==
                                                            null
                                                        ? Container()
                                                        : Stack(
                                                            children: [
                                                              Positioned(
                                                                left: state
                                                                    .currentPatternInfo!
                                                                    .left,
                                                                top: state
                                                                    .currentPatternInfo!
                                                                    .top,
                                                                width: state
                                                                    .currentPatternInfo!
                                                                    .width,
                                                                height: state
                                                                    .currentPatternInfo!
                                                                    .height,
                                                                child: Opacity(
                                                                  opacity: 0.1,
                                                                  child: Stack(
                                                                    children: [
                                                                      /// すべてのBorderを生成
                                                                      ...() {
                                                                        List<Widget>
                                                                            borderWidget =
                                                                            [];
                                                                        state
                                                                            .currentPatternInfo!
                                                                            .borderDisplayInfoList
                                                                            .asMap()
                                                                            .entries
                                                                            .forEach((border) {
                                                                          EmbBorderViewDisplayInfo
                                                                              borderInfo =
                                                                              border.value;

                                                                          borderWidget
                                                                              .add(
                                                                            Positioned(
                                                                              top: borderInfo.top,
                                                                              left: borderInfo.left,
                                                                              width: borderInfo.width,
                                                                              height: borderInfo.height,
                                                                              child: Stack(
                                                                                children: [
                                                                                  /// すべてのgroupを生成
                                                                                  ...() {
                                                                                    List<Widget> groupWidget = [];
                                                                                    borderInfo.groupDisplayInfoList.asMap().entries.forEach((group) {
                                                                                      EmbGroupViewDisplayInfo groupInfo = group.value;
                                                                                      String groupKey = group.key.toString();

                                                                                      groupWidget.add(
                                                                                        /// patternの表示領域(Maskと赤いドットを含む)
                                                                                        Positioned(
                                                                                          top: groupInfo.top,
                                                                                          left: groupInfo.left,
                                                                                          width: groupInfo.width,
                                                                                          height: groupInfo.height,

                                                                                          /// group
                                                                                          child: Stack(
                                                                                            children: [
                                                                                              /// すべてのEmbPatternを生成
                                                                                              ...() {
                                                                                                List<Widget> widget = [];
                                                                                                groupInfo.embPatternDisplayInfoList.asMap().entries.forEach((embPattern) {
                                                                                                  EmbPatternViewDisplayInfo embPatternInfo = embPattern.value;

                                                                                                  String embPatternKey = embPattern.key.toString();

                                                                                                  /// EmbPatternの画像
                                                                                                  if (embPatternInfo.displayImage != null) {
                                                                                                    widget.add(
                                                                                                      Positioned(
                                                                                                        key: Key("group${groupKey}embPattern$embPatternKey"),
                                                                                                        top: embPatternInfo.imageTop,
                                                                                                        left: embPatternInfo.imageLeft,
                                                                                                        width: embPatternInfo.imageWidth,
                                                                                                        height: embPatternInfo.imageHeight,
                                                                                                        child: Image.memory(embPatternInfo.displayImage!),
                                                                                                      ),
                                                                                                    );
                                                                                                  } else {
                                                                                                    /// do noting
                                                                                                  }

                                                                                                  widget.add(
                                                                                                    Positioned(
                                                                                                      top: embPatternInfo.top,
                                                                                                      left: embPatternInfo.left,
                                                                                                      width: embPatternInfo.width,
                                                                                                      height: embPatternInfo.height,
                                                                                                      child:

                                                                                                          /// 縫ってないの赤い点線のボックス
                                                                                                          groupInfo.isAllNotSewing == false
                                                                                                              ? Container()
                                                                                                              : IgnorePointer(
                                                                                                                  child: CustomPaint(
                                                                                                                    painter: MaskPainter(
                                                                                                                      isDashedLine: true,
                                                                                                                      maskColor: const Color.fromARGB(255, 235, 0, 0),
                                                                                                                      strokeWidth: 1,
                                                                                                                      maskTopLeft: embPatternInfo.mask.topLeft,
                                                                                                                      maskTopRight: embPatternInfo.mask.topRight,
                                                                                                                      maskBottomLeft: embPatternInfo.mask.bottomLeft,
                                                                                                                      maskBottomRight: embPatternInfo.mask.bottomRight,
                                                                                                                    ),
                                                                                                                  ),
                                                                                                                ),
                                                                                                    ),
                                                                                                  );
                                                                                                });
                                                                                                return widget;
                                                                                              }(),
                                                                                            ],
                                                                                          ),
                                                                                        ),
                                                                                      );
                                                                                    });
                                                                                    return groupWidget;
                                                                                  }(),
                                                                                ],
                                                                              ),
                                                                            ),
                                                                          );
                                                                        });

                                                                        return borderWidget;
                                                                      }(),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),

                                                              /// 文字の円弧下線イメージ
                                                              Opacity(
                                                                opacity: 0.1,
                                                                child: Stack(
                                                                  children: [
                                                                    /// すべてのBorderを生成
                                                                    ...() {
                                                                      List<Widget>
                                                                          borderWidget =
                                                                          [];
                                                                      state
                                                                          .currentPatternInfo!
                                                                          .borderDisplayInfoList
                                                                          .asMap()
                                                                          .entries
                                                                          .forEach(
                                                                              (border) {
                                                                        EmbBorderViewDisplayInfo
                                                                            borderInfo =
                                                                            border.value;

                                                                        borderWidget
                                                                            .add(
                                                                          Stack(
                                                                            children: [
                                                                              /// すべてのgroupを生成
                                                                              ...() {
                                                                                List<Widget> groupWidget = [];
                                                                                borderInfo.groupDisplayInfoList.asMap().entries.forEach((group) {
                                                                                  EmbGroupViewDisplayInfo groupInfo = group.value;

                                                                                  /// 文字の円弧下線イメージ
                                                                                  if (groupInfo.arcImage != null) {
                                                                                    groupWidget.add(
                                                                                      Image.memory(
                                                                                        groupInfo.arcImage!,
                                                                                        fit: BoxFit.cover,
                                                                                        alignment: Alignment.center,
                                                                                        gaplessPlayback: false,
                                                                                      ),
                                                                                    );
                                                                                  } else {
                                                                                    /// do nothing
                                                                                  }
                                                                                });
                                                                                return groupWidget;
                                                                              }(),
                                                                            ],
                                                                          ),
                                                                        );
                                                                      });

                                                                      return borderWidget;
                                                                    }(),
                                                                  ],
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                    state.partsImage == null
                                                        ? Container()
                                                        : state.partsImage!,
                                                  ],
                                                ),
                                              ),
                                              const Spacer(flex: 1)
                                            ],
                                          ),
                                        ),
                                      ),
                                      const Spacer(flex: 10),
                                      Expanded(
                                        flex: 194,
                                        child: grp_lst_colorthread(
                                          isNeedDisplayArrow: true,
                                          controller: state.scrollController,
                                          displayList: state.displayList,
                                          onColorInfoClick:
                                              viewModel.onColorItemClicked,
                                          onPatternClick:
                                              viewModel.onPatternItemClicked,
                                        ),
                                      ),
                                      const Spacer(flex: 10),
                                      Expanded(
                                        flex: 8,
                                        child: CustomScrollbar(
                                          key: const Key(
                                              "Applique_Parts_CustomScrollbar"),
                                          controller: state.scrollController,
                                          visibilityWhenScrollFull: false,
                                        ),
                                      ),
                                      const Spacer(flex: 2),
                                    ],
                                  ),
                                ),
                                const Spacer(flex: 80),
                                Expanded(
                                  flex: 80,
                                  child: Row(
                                    children: [
                                      const Spacer(flex: 12),
                                      Expanded(
                                        flex: 152,
                                        child: grp_btn_negative(
                                          text: l10n.icon_return,
                                          onTap: () => viewModel
                                              .onReturnButtonClick(context),
                                        ),
                                      ),
                                      const Spacer(flex: 74),

                                      /// TODO:仕様不明
                                      /// \\APBSHSH05\SoftDev2\02_Project\2022年度\397_軟二_P&H開発_IIVO開発\04.工程作業\21_デザイン指示書問題一覧\AdobeXD 仕様問題.xlsx\line:295,297
                                      /// TODO:XDでボタンの順番は間違う
                                      /// https://brothergroup.atlassian.net/browse/PHFIRMIIVODES-1055
                                      Expanded(
                                          flex: 152,
                                          child: CustomTooltip(
                                            message: l10n
                                                .tt_emb_newapplique_color_selectall,
                                            child: grp_btn_selectall(
                                              onTap: viewModel
                                                  .onSelectAllButtonClick,
                                            ),
                                          )),
                                      const Spacer(flex: 8),
                                      Expanded(
                                        flex: 152,
                                        child: CustomTooltip(
                                          message: l10n
                                              .tt_emb_newapplique_color_selectnone,
                                          child: grp_btn_selectnone(
                                            onTap: viewModel
                                                .onSelectNoneButtonClick,
                                          ),
                                        ),
                                      ),
                                      const Spacer(flex: 74),
                                      Expanded(
                                        flex: 152,
                                        child: grp_btn_positive(
                                          state: state.isNext
                                              ? ButtonState.normal
                                              : ButtonState.disable,
                                          text: l10n.icon_00080,
                                          feedBackControl: null,
                                          onTap: () => viewModel
                                              .onNextButtonClick(context),
                                        ),
                                      ),
                                      const Spacer(flex: 12),
                                    ],
                                  ),
                                ),
                                const Spacer(flex: 12),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const Spacer(flex: 6),
                    ],
                  ),
                ),
                const Spacer(flex: 8),
              ],
            ),
          ),
        ),
        const Spacer(flex: 61),
      ],
    );
  }
}
