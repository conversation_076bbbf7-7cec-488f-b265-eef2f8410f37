import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_osae_down_view_interface.dart';

final errOsaeDownViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrOsaeDownViewInterface, ErrOsaeDownState, BuildContext>(
        (ref, context) => ErrOsaeDownViewModel(ref, context));

class ErrOsaeDownViewModel extends ErrOsaeDownViewInterface {
  ErrOsaeDownViewModel(Ref ref, BuildContext context)
      : super(const ErrOsaeDownState(), ref, context);

  @override
  void onOKButtonClicked() {
    final int errCode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERROSAEDOWN);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }
}
