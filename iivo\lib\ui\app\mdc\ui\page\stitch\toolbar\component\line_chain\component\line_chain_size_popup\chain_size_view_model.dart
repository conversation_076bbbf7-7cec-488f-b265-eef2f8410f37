import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_chain_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'chain_size_view_interface.dart';

/// 読み出したSizeデータの変換倍率 '読み出したデータの単位/10=mm'
const double _conversionRate = 10.0;

final chainSizeViewModelProvider = StateNotifierProvider.autoDispose<
    ChainSizeStateSizeStateViewInterface,
    ChainSizeState>((ref) => ChainSizeViewModel(ref));

class ChainSizeViewModel extends ChainSizeStateSizeStateViewInterface {
  ChainSizeViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const ChainSizeState(
              isDefaultStyle: false,
              sizeInputValue: "",
              minusButtonValid: false,
              plusButtonValid: false,
            ),
            ref) {
    update();
  }

  ///
  /// サイズ表示星
  ///
  bool _isSizeValueDisplayStar =
      LineChainModel().getSize() != LineChainModel.sizeNotUpdating
          ? false
          : true;

  ///
  /// サイズ値
  ///
  int _sizeValue = LineChainModel().getSize();

  @override
  void update() {
    state = state.copyWith(
      sizeInputValue: _getSizeDisplayValue(),
      isDefaultStyle: _isDefaultStyle(),
      minusButtonValid: _getMinusButtonState(),
      plusButtonValid: _getPlusButtonState(),
    );
  }

  /// ボタンクリック状態
  bool isSizeValueChanged = false;

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  @override
  int get defaultValue => LineChainModel().sizeDefaultValue;

  @override
  bool plusLineChainSize(bool isLongPress) {
    if (_isSizeValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSizeValueDisplayStar = false;

      ///  Model 更新
      _sizeValue = LineChainModel().sizeDefaultValue;

      /// View更新
      update();

      return false;
    }

    if (_sizeValue >= LineChainModel.maxiSizeValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _sizeValue++;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  bool miniLineChainSize(bool isLongPress) {
    if (_isSizeValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSizeValueDisplayStar = false;

      ///  Model 更新
      _sizeValue = LineChainModel().sizeDefaultValue;

      /// View更新
      update();

      return false;
    }

    if (_sizeValue <= LineChainModel.miniSizeValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _sizeValue--;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineChainSize.toString());
    if (_isSizeValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int sizeValue = LineChainModel().getSize();

    /// Model 更新
    LineChainModel().setSize(_sizeValue);
    if (LineChainModel().setMdcChainStitchSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (sizeValue != _sizeValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// Sizeの表示値を取得する
  ///
  String _getSizeDisplayValue() {
    /// cmからmmへ
    double lineZigzagWidthValue = _sizeValue / _conversionRate;
    if (_isSizeValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return lineZigzagWidthValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(lineZigzagWidthValue);
  }

  ///
  /// 表示サイズのテキストスタイルを取得する
  ///
  bool _isDefaultStyle() {
    if (_isSizeValueDisplayStar) {
      return true;
    }

    if (_sizeValue == defaultValue) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isSizeValueDisplayStar) {
      return true;
    }

    if (_sizeValue <= LineChainModel.miniSizeValue) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isSizeValueDisplayStar) {
      return true;
    }

    if (_sizeValue >= LineChainModel.maxiSizeValue) {
      return false;
    }
    return true;
  }
}
