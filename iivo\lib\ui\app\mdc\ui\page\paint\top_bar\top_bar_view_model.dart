import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../memory/memory.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../model/paint/magnification_model.dart';
import '../../../../model/paint/memory_import_model.dart';
import '../../../../model/paint/paint_model.dart';
import '../../../../model/paint/scan_model.dart';
import '../../../../model/paint/top_bar_model.dart';
import '../../../page_route.dart';
import '../paint_page_view_model.dart';
import 'component/scan_popup.dart';
import 'memory_import/memory_import_view_model.dart';
import 'top_bar_view_interface.dart';

final topBarViewModelProvider =
    StateNotifierProvider.autoDispose<TopBarViewInterface, TopBarState>(
        (ref) => TopBarViewModel(ref));

class TopBarViewModel extends TopBarViewInterface {
  TopBarViewModel(this._ref) : super(const TopBarState());

  @override
  void build() {
    super.build();
    update();
  }

  ///
  /// providerのref
  ///
  final Ref _ref;

  ///
  /// 拡大レベルの取得
  ///
  final List<Widget> _getMagnificationLevel = [
    const ico_zoom100(),
    const ico_zoom200(),
    const ico_zoom400(),
    const ico_zoom800(),
    const ico_zoom1600(),
  ];
  @override
  Widget get getMagnificationLevel =>
      _getMagnificationLevel[MagnificationModel().valueToIndex()];

  @override
  void update() {
    if (MagnificationModel.magnificationLevel ==
        MagnificationModel.magnification_100) {
      state = state.copyWith(
        dragMoveButtonState: ButtonState.disable,
        isDensityShow: ScanModel().isLoadScanImage,
        densityLevelIndex: TopBarModel().densityLevelIndex,
      );
    } else {
      state = state.copyWith(
        dragMoveButtonState: TopBarModel().getDragMoveFlg()
            ? ButtonState.select
            : ButtonState.normal,
        isDensityShow: ScanModel().isLoadScanImage,
        densityLevelIndex: TopBarModel().densityLevelIndex,
      );
    }
  }

  ///
  /// 拡大倍率ボタンのクリック関数
  ///
  @override
  void onMagnificationButtonClicked(BuildContext context) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      PaintModel().closeAllPopup(context);
      PaintModel().setPopupType(PopupType.magnification);
      updateMagnificationByChild?.call();

      _ref
          .read(paintPageProvider.notifier)
          .updatePaintPageByChild(ModuleType.topBar);
    });
  }

  ///
  /// ドラッグ移動ボタンがクリックされました
  ///
  @override
  void onDragMoveButtonClicked(BuildContext context) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      switch (state.dragMoveButtonState) {
        case ButtonState.normal:
          TopBarModel().setDragMoveFlg(true);

          state = state.copyWith(dragMoveButtonState: ButtonState.select);
          break;

        case ButtonState.select:
          TopBarModel().setDragMoveFlg(false);

          state = state.copyWith(dragMoveButtonState: ButtonState.normal);
          break;
        default:
          break;
      }

      /// 他の画面を更新する
      PaintModel().closeAllPopup(context);
      updateMagnificationByChild?.call();
      _ref
          .read(paintPageProvider.notifier)
          .updatePaintPageByChild(ModuleType.topBar);
    });
  }

  ///
  /// スキャン
  /// 下絵スキャン、ラインスキャン、イラストスキャンの3種類を選択する。
  ///
  @override
  void onMdcImageButtonClicked(BuildContext context) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      /// スキャン画面デフォルトクリア処理
      ScanModel().reset();

      /// 画面遷移
      _openScanPopup(context);
    });
  }

  ///
  /// 下絵　濃いのクリック関数
  ///
  @override
  void onDensityAddButtonClicked(BuildContext context) {
    if (PaintModel().isMagnificationPopupOpened()) {
      PaintModel().clearPopupType();
      updateMagnificationByChild?.call();
    }

    /// 最大時にクリックが無効です
    if (state.densityLevelIndex == DensityLevel.mdc0BackGround100.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      int densityLevelIndex = state.densityLevelIndex;
      (++densityLevelIndex).clamp(0, DensityLevel.values.length - 1);

      /// Model更新
      TopBarModel().densityLevelIndex = densityLevelIndex;
      MdcLibrary().apiBinding.setMdcImageDrawingDensity(
          DensityLevel.values[densityLevelIndex].number);
      ScanModel().updateSketchesImage();

      /// View更新
      state = state.copyWith(densityLevelIndex: densityLevelIndex);

      _ref
          .read(paintPageProvider.notifier)
          .updatePaintPageByChild(ModuleType.topBar);
    });
  }

  ///
  /// 下絵　薄いのクリック関数
  ///
  @override
  void onDensityReduceButtonClicked(BuildContext context) {
    if (PaintModel().isMagnificationPopupOpened()) {
      PaintModel().clearPopupType();
      updateMagnificationByChild?.call();
    }

    /// 最小時にクリックが無効です
    if (state.densityLevelIndex == DensityLevel.mdc100BackGround0.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      int densityLevelIndex = state.densityLevelIndex;
      (--densityLevelIndex).clamp(0, DensityLevel.values.length - 1);

      /// Model更新
      TopBarModel().densityLevelIndex = densityLevelIndex;
      MdcLibrary().apiBinding.setMdcImageDrawingDensity(
          DensityLevel.values[densityLevelIndex].number);
      ScanModel().updateSketchesImage();

      /// View更新
      state = state.copyWith(densityLevelIndex: densityLevelIndex);

      _ref
          .read(paintPageProvider.notifier)
          .updatePaintPageByChild(ModuleType.topBar);
    });
  }

  ///
  /// データインポートボタンをクリックする
  ///
  @override
  void onMemoryImportButtonClicked(BuildContext context) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    _ref.read(paintPageProvider.notifier).lockEditProcWhenProcessing(() {
      _showLoadDataWaitPopup();

      MemoryImportModel.loadDirectoryList(DeviceKind.internalStorage)
          .then((response) {
        GlobalPopupRoute().resetErrorState();

        if (response.error != AccessError.none) {
          return Log.e(
              tag: memoryAccessErrorLogTag,
              description: "unException: access internalStorage fail");
        }

        PopupNavigator.pushNamedAndRemoveAll(
          context: context,
          nextRouteName: PopupEnum.topBarMemoryImport,
          arguments: MemoryImportParams(
            memoryEntity: response.data,
          ),
        ).then((value) {
          PaintModel().closeAllPopup(context);
          updateMagnificationByChild?.call();
        });
      });
    });
  }

  ///
  /// 下絵スキャン関数処理
  ///
  void _onImageScanButtonClicked(BuildContext context) {
    /// Model更新
    ScanModel().scanPageType = ScanPageType.imageScan;

    /// View更新（ページ遷移）
    PopupNavigator.pop(context: context);
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.scan).then((value) {
      /// 他の画面を更新する
      _ref
          .read(paintPageProvider.notifier)
          .updatePaintPageByChild(ModuleType.topBar);
    });
  }

  ///
  /// ラインスキャン関数処理
  ///
  void _onLineDesignButtonClicked(BuildContext context) {
    /// Model更新
    ScanModel().scanPageType = ScanPageType.lineDesign;

    /// View更新（ページ遷移）
    PopupNavigator.pop(context: context);
    if (ScanModel().isLoadScanImage) {
      PagesRoute().pushNamed(nextRoute: PageRouteEnum.lineDesign);
    } else {
      PagesRoute().pushNamed(nextRoute: PageRouteEnum.scan);
    }
  }

  ///
  /// イラストスキャン関数処理
  ///
  void _onIllustrationDesignButtonClicked(BuildContext context) {
    /// Model更新
    ScanModel().scanPageType = ScanPageType.illustrationDesign;

    /// View更新（ページ遷移）
    PopupNavigator.pop(context: context);
    if (ScanModel().isLoadScanImage) {
      PagesRoute().pushNamed(nextRoute: PageRouteEnum.illustrationDesign);
    } else {
      PagesRoute().pushNamed(nextRoute: PageRouteEnum.scan);
    }
  }

  ///
  /// LoadUsbDataWait ポップアップウィンドウを開ける/閉じる
  ///
  void _showLoadDataWaitPopup() => GlobalPopupRoute().updateErrorState(
      nextRoute: GlobalPopupRouteEnum.ERR_PLEASE_WAIT,
      arguments: {GlobalPopupRoute.isStopSystemSound: true});

  void _closeSelf(context) {
    PopupNavigator.pop(context: context);
  }

  ///
  /// Scan Popup
  ///
  void _openScanPopup(BuildContext context) {
    PaintModel().closeAllPopup(context);
    updateMagnificationByChild?.call();
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => ScanPopup(
          onImageScanClick: () => _onImageScanButtonClicked(context),
          onLineDesignClick: () => _onLineDesignButtonClicked(context),
          onIllustrationDesignClick: () =>
              _onIllustrationDesignButtonClicked(context),
          onCancelClick: () => _closeSelf(context),
        ),
        barrier: true,
      ),
    );
  }
}
