import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../model/device_info_model.dart';
import '../../../../../../model/stitch/draw_region_model.dart';
import '../../../../../../model/stitch/surface_decorative_fill_model.dart';
import '../../../../../../model/stitch/toolbar_model.dart';
import '../../../stitch_page_view_model.dart';
import 'surface_decorative_fill_view_interface.dart';

typedef Unit = DisplayUnit;

typedef SurfaceDecorativeFillViewModelProvider
    = AutoDisposeStateNotifierProvider<SurfaceDecoFillStateViewInterface,
        SurfaceDecoFillState>;

class SurfaceDecoFillViewModel extends SurfaceDecoFillStateViewInterface {
  SurfaceDecoFillViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const SurfaceDecoFillState(), ref) {
    update();
  }

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;
  @override
  void update() {
    state = state.copyWith(
      sizeSettingValue: _getSizeValue(),
      directionSettingValue: _getDirectionValue(),
      randomShiftSettingValue: _getRandomShiftValue(),
      positionOffsetSettingValue: _getPositionOffsetValue(),
      thicknessSettingValue: _getThicknessDisplayValue(),
    );
    if (SurfaceDecoFillModel().getThickness() ==
        MdcDecoFillThickness.mdc_decofill_thickness_invalid.index) {
      return;
    }
    state = state.copyWith(
        isThickSelected: SurfaceDecoFillModel().getThickness() ==
                SurfaceDecoFillModel().thicknessDefaultValue.index
            ? true
            : false);
  }

  @override
  void onSizeButtonClicked(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceDecorativeFillSize.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void onDirectionButtonClicked(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceDecorativeFillDirection.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void onOutlineButtonClicked(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceDecorativeFillOutline.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void onRandomShiftSettingClicked(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceDecorativeFillRandomShift.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void onPositionOffsetSettingClicked(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceDecorativeFillPositionOffset.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  void onThicknessSettingClicked(context) {
    if (DrawRegionModel().isProcessImage) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    ref.read(stitchPageViewModelProvider.notifier).openPopupRoute(
        PopupEnum.surfaceDecorativeFillThickness.toString(),
        () => ref
            .read(stitchPageViewModelProvider.notifier)
            .updateRelatedInStitchPage(update));
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updateRelatedInStitchPage(update);
  }

  @override
  bool getSizeDefault() => SurfaceDecoFillModel().getDisplayDefault(
      SurfaceDecoFillModel().getSize() != SurfaceDecoFillModel.sizeNotUpdating
          ? DecoFillSettingState.settingCompleted
          : DecoFillSettingState.unknown,
      SurfaceDecoFillModel().getSize(),
      SurfaceDecoFillModel().sizeDefaultValue);
  @override
  bool getDirectionDefault() => SurfaceDecoFillModel().getDisplayDefault(
      SurfaceDecoFillModel().getDirection() !=
              SurfaceDecoFillModel.directionNotUpdating
          ? DecoFillSettingState.settingCompleted
          : DecoFillSettingState.unknown,
      SurfaceDecoFillModel().getDirection(),
      SurfaceDecoFillModel().directionDefaultValue);

  @override
  bool getOutlineDefault() => SurfaceDecoFillModel().getDisplayDefault(
      SurfaceDecoFillModel().getOutline() !=
              SurfaceDecoFillModel.outlineNotUpdating
          ? DecoFillSettingState.settingCompleted
          : DecoFillSettingState.unknown,
      SurfaceDecoFillModel().getOutline(),
      SurfaceDecoFillModel().outlineDefaultValue);

  @override
  bool getRandomShiftDefault() => SurfaceDecoFillModel().getDisplayDefault(
      SurfaceDecoFillModel().getRandomShift() !=
                  SurfaceDecoFillModel.randomShiftNotUpdating &&
              SurfaceDecoFillModel().getRandomShiftType() !=
                  SurfaceDecoFillModel.randomShiftTypeNotUpdating.index
          ? DecoFillSettingState.settingCompleted
          : DecoFillSettingState.unknown,
      SurfaceDecoFillModel().getRandomShift(),
      SurfaceDecoFillModel().randomShiftDefaultValue);

  @override
  bool getPositionOffsetDefault() {
    if (SurfaceDecoFillModel().getPositionOffsetX() !=
            SurfaceDecoFillModel.positionOffsetXNotUpdating &&
        SurfaceDecoFillModel().getPositionOffsetY() !=
            SurfaceDecoFillModel.positionOffsetYNotUpdating) {
      if ((SurfaceDecoFillModel().getPositionOffsetX() ==
              SurfaceDecoFillModel.positionOffsetDefaultValue) &&
          (SurfaceDecoFillModel().getPositionOffsetY() ==
              SurfaceDecoFillModel.positionOffsetDefaultValue)) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  @override
  String getOutlineText(BuildContext context) {
    MDCIsOnOff outLine = SurfaceDecoFillModel().getOutline();
    final AppLocalizations l10n = AppLocalizations.of(context)!;

    if (outLine != SurfaceDecoFillModel.outlineNotUpdating) {
      return outLine == MDCIsOnOff.mdcIs_off ? l10n.icon_off : l10n.icon_on;
    } else {
      return SurfaceDecoFillModel.towStarValue;
    }
  }

  @override
  double? getPositionOffsetTextWith() {
    ///
    /// 単位mmの場合、基点位置オフセット表示幅
    ///
    const double positionOffsetMmTextWidth = 101;

    ///
    /// 単位inchの場合、基点位置オフセット表示幅
    ///
    const double positionOffsetInchTextWidth = 168;

    if (SurfaceDecoFillModel().getPositionOffsetX() !=
            SurfaceDecoFillModel.positionOffsetXNotUpdating &&
        SurfaceDecoFillModel().getPositionOffsetY() !=
            SurfaceDecoFillModel.positionOffsetYNotUpdating) {
      return null;
    } else {
      if (currentSelectedUnit == Unit.mm) {
        return positionOffsetMmTextWidth;
      } else {
        return positionOffsetInchTextWidth;
      }
    }
  }

  ///
  /// 倍率/サイズ表示用テキストを取得する
  ///
  String _getSizeValue() {
    int size = SurfaceDecoFillModel().getSize();
    if (size != SurfaceDecoFillModel.sizeNotUpdating) {
      return size.toString();
    } else {
      return SurfaceDecoFillModel.oneStarValue;
    }
  }

  ///
  /// 傾き方向表示用テキストを取得する
  ///
  String _getDirectionValue() {
    int direction = SurfaceDecoFillModel().getDirection();
    if (direction != SurfaceDecoFillModel.directionNotUpdating) {
      return direction.toString();
    } else {
      return SurfaceDecoFillModel.oneStarValue;
    }
  }

  ///
  /// ゆらぎ表示用テキストを取得する
  ///
  String _getRandomShiftValue() {
    int randomShift = SurfaceDecoFillModel().getRandomShift();
    int randomShiftType = SurfaceDecoFillModel().getRandomShiftType();
    final List<String> typeList = ["A", "B", "C"];
    if (randomShift != SurfaceDecoFillModel.randomShiftNotUpdating &&
        randomShiftType !=
            SurfaceDecoFillModel.randomShiftTypeNotUpdating.index) {
      if (randomShift == SurfaceDecoFillModel().randomShiftDefaultValue) {
        return randomShift.toString();
      } else {
        return "$randomShift-${typeList[randomShiftType]}";
      }
    } else {
      return SurfaceDecoFillModel.oneStarValue;
    }
  }

  ///
  /// 基点位置オフセット表示用テキストを取得する
  ///
  String _getPositionOffsetValue() {
    int positionOffsetX = SurfaceDecoFillModel().getPositionOffsetX();
    int positionOffsetY = SurfaceDecoFillModel().getPositionOffsetY();

    /// cmからmmへ
    double conversionRate = 10.0;
    double positionOffsetXValue = positionOffsetX / conversionRate;
    double positionOffsetYValue = positionOffsetY / conversionRate;

    if (positionOffsetX != SurfaceDecoFillModel.positionOffsetXNotUpdating &&
        positionOffsetY != SurfaceDecoFillModel.positionOffsetYNotUpdating) {
      if (currentSelectedUnit == Unit.mm) {
        return "${positionOffsetYValue.toStringAsFixed(1)}/${positionOffsetXValue.toStringAsFixed(1)}";
      } else {
        return "${ToolbarModel.getDisplayInchShowValue(positionOffsetYValue)}/${ToolbarModel.getDisplayInchShowValue(positionOffsetXValue)}";
      }
    } else {
      if (currentSelectedUnit == Unit.mm) {
        return "${SurfaceDecoFillModel.differentMmValue}/${SurfaceDecoFillModel.differentMmValue}";
      } else {
        return "${SurfaceDecoFillModel.differentInchValue}/${SurfaceDecoFillModel.differentInchValue}";
      }
    }
  }

  ///
  /// Thicknessの値を取得します
  ///
  String _getThicknessDisplayValue() {
    if (SurfaceDecoFillModel().getThickness() ==
        SurfaceDecoFillModel.thicknessNotUpdating.index) {
      return "**";
    } else {
      return "";
    }
  }
}
