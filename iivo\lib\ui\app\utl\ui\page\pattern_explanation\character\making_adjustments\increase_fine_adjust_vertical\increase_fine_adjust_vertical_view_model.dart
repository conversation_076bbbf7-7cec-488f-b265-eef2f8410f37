import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../model/pattern_explanation_model.dart';
import '../../../../../page_route.dart';
import 'increase_fine_adjust_vertical_view_interface.dart';

final increaseFineAdjustVerticalViewModelProvider =
    StateNotifierProvider.autoDispose<IncreaseFineAdjustVerticalViewModel,
            IncreaseFineAdjustVerticalState>(
        (ref) => IncreaseFineAdjustVerticalViewModel(ref));

class IncreaseFineAdjustVerticalViewModel
    extends ViewModel<IncreaseFineAdjustVerticalState> {
  IncreaseFineAdjustVerticalViewModel(this._ref)
      : super(const IncreaseFineAdjustVerticalState()) {
    _ref.listen(
      appDisplayUtlStateProvider
          .select((value) => value.utlFuncSetting.ref.isUtlSewing),
      (previous, nextState) {
        update();
      },
    );
    update();
  }
  final AutoDisposeStateNotifierProviderRef _ref;

  ///
  /// fine adjust verticalの値
  ///

  ///
  /// ViewModel更新
  ///
  @override
  void update() {
    state = state.copyWith(
      fineAdjustVerticalDisplayValue: PatternExplanationModel()
          .changeValueToDisplay(
              DeviceLibrary().apiBinding.getFineAdjustVerti().value),
      isStopingMotor: TpdLibrary()
              .apiBinding
              .bpIFGetAppDisplayUtl()
              .utlFuncSetting
              .ref
              .isUtlSewing ==
          false,
    );
  }

  ///
  /// vertical値が減少するクリック関数
  ///
  bool onVerticalDecreaseButtonClicked(bool isLongPress) {
    int fineAdjustVertical =
        DeviceLibrary().apiBinding.getFineAdjustVerti().value;

    if (fineAdjustVertical <= PatternExplanationModel.fineAdjustMinVertical) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    } else {
      if (!isLongPress) {
        SystemSoundPlayer().play(SystemSoundEnum.accept);
      }
      fineAdjustVertical--;

      /// Libに通知
      DeviceLibrary().apiBinding.setFineAdjustVerti(fineAdjustVertical);

      /// View更新
      update();

      return true;
    }
  }

  ///
  /// vertical値が増加したクリック関数
  ///
  bool onVerticalIncreaseButtonClick(bool isLongPress) {
    int fineAdjustVertical =
        DeviceLibrary().apiBinding.getFineAdjustVerti().value;
    if (fineAdjustVertical >= PatternExplanationModel.fineAdjustMaxVertical) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    } else {
      if (!isLongPress) {
        SystemSoundPlayer().play(SystemSoundEnum.accept);
      }

      fineAdjustVertical++;

      /// Libに通知
      DeviceLibrary().apiBinding.setFineAdjustVerti(fineAdjustVertical);

      /// View更新
      update();

      return true;
    }
  }

  ///
  /// Returnボタンをクリック
  ///
  void onReturnButtonClick() {
    PagesRoute().pop();
  }
}
