import 'dart:async';
import 'dart:developer';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../model/pattern_image_capture_model.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../global_popup/global_popups/err_emb_too_much_selected_go_menu/err_emb_too_much_selected_go_menu_view_model.dart';
import '../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../model/pattern_data_reader/character_pattern_reader.dart';
import '../../../../model/pattern_data_reader/pattern_data_base.dart';
import '../../../../model/pattern_filter_model.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/redo_undo_model.dart';
import '../../../../model/select_information_model.dart';
import '../../../../model/select_model.dart';
import '../../../../model/thread_color_list_model.dart';
import '../../../page_route.dart';
import '../category_selector/category_selector_view_interface.dart';
import '../category_selector/category_selector_view_model.dart';
import 'character_pattern_selector_state.dart';

final characterPatternSelectorViewModelProvider =
    StateNotifierProvider.autoDispose<CharacterPatternSelectorViewModel,
            CharacterPatternSelectorState>(
        (ref) => CharacterPatternSelectorViewModel(ref));

class CharacterPatternSelectorViewModel
    extends ViewModel<CharacterPatternSelectorState> {
  CharacterPatternSelectorViewModel(
    this._ref,
  ) : super(const CharacterPatternSelectorState()) {
    Timeline.startSync("CharacterPatternSelectorViewModel");
    _prePatternSize = PatternModel().thumbnailSize;

    /// view更新
    update();
    Timeline.finishSync();
  }
  final AutoDisposeStateNotifierProviderRef<CharacterPatternSelectorViewModel,
      CharacterPatternSelectorState> _ref;

  static const invalidNum = -1;

  ScrollController scrollController = ScrollController();

  ThumbnailSizeType _prePatternSize = ThumbnailSizeType.M;

  ///
  /// ひれい
  ///
  final double _scale = 1.0;

  ///
  /// タッチスクリーンハンドインデックス
  ///
  static const int touchScreenFingerCount = 2;

  ///
  /// 安定化用タイマー
  ///
  Timer? _debounceTimer;

  ///
  /// 全ての中カテゴリのイコンデータを取得する
  ///
  /// ##@return
  /// - List<CharacterPatternGroup>: 順番保存されているの模様イコンデータ
  ///
  List<CharacterPatternGroup> getAllCategoryImagesInfo() =>
      CharacterPatternImageReader().getAllCharacterPatternsInfo();

  @override
  void update() {
    state = state.copyWith(
        patternSize: PatternModel().thumbnailSize,
        gridColor: PatternModel().getThumbnailBackgroundColor(),
        patternListHeight: patternListHeightDefault);
    if (_prePatternSize != state.patternSize) {
      _prePatternSize = state.patternSize;
      scrollController.jumpTo(0.0);
    } else {}
  }

  ///
  /// カテゴリーを選択します
  ///
  void onCategoryClick(int index) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    scrollController = ScrollController();

    /// view更新
    state = state.copyWith(selectCategoryIndex: index);
  }

  ///
  /// 模様を選択します
  /// @Param
  /// index:模様のpatternIndex(0~XX)
  /// index:模様のcategoryType
  ///
  void onPatternClick(int patternIndex, int categoryType) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// Model更新
    SelectModel().selectedPatternIndex =
        getAllCategoryImagesInfo()[state.selectCategoryIndex]
            .patternNumGroup[patternIndex];
    SelectModel().selectedPatternIndex =
        SelectModel().selectedPatternIndex == invalidNum
            ? patternIndex
            : SelectModel().selectedPatternIndex;
    SelectModel().selectedCategoryType = categoryType;

    assert(SelectModel().selectedCategoryType != null, '割り当てに失敗しました');
    assert(SelectModel().selectedPatternIndex != null, '割り当てに失敗しました');
    EmbLibraryError error = PatternModel().selectEmb(
      SelectModel().selectedPatternIndex!,
      SelectModel().selectedCategoryType!,
    );

    /// メモリーフル時の対応
    if (error == EmbLibraryError.EMB_DATAOVER_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
          arguments: ErrEmbTooMuchSelectedGoMenuArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
      return;
    }

    /// view更新
    state = state.copyWith(setButton: true);

    _ref
        .read(categorySelectorViewModelProvider.notifier)
        .updateTopPageByChild(CategorySelectorModuleType.patternSelector);
  }

  ///
  /// Setのクリック関数
  ///
  void onSetButtonClick(BuildContext context) {
    EmbLibraryError error = EmbLibrary().apiBinding.embGotoEdit();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return;
    }
    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }
    scrollController = ScrollController();

    /// Model更新
    PatternModel()
      ..reloadAllPattern()
      ..clearTemporaryPatternList();
    SelectModel()
      ..selectedCategoryType = null
      ..selectedPatternIndex = null;
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }

    PatternFilter().setFilterHide();
    SelectInformationModel()
        .setTemporaryPatternAutoKind(CategoryType.unknownPattern);

    PagesRoute().pushNamedAndRemoveUntil(
        nextRoute: PageRouteEnum.patternEdit, untilRoute: PageRouteEnum.home);
  }

  ///
  /// Returnのクリック関数
  ///
  void onReturnButtonClicked(BuildContext context) {
    scrollController = ScrollController();

    /// Model更新
    PatternModel().deleteTemporaryPatternList();
    SelectInformationModel()
        .setTemporaryPatternAutoKind(CategoryType.unknownPattern);
    SelectModel()
      ..selectedCategoryType = null
      ..selectedPatternIndex = null;
    PatternFilter().patternData = null;
    PatternFilter().setFilterHide();
    _closeSelf(context);
  }

  ///
  /// Embroideryキー時のクリック関数
  ///
  void onEmbroideryButtonClicked(BuildContext context) {
    if (ThreadColorListModel().isNoThreadToSewing()) {
      return;
    }
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.sewing);
  }

  ///
  /// 自分を閉じる
  ///
  void _closeSelf(BuildContext context) {
    PopupNavigator.pop(context: context);
  }

  ///
  /// プレビューのKey定義
  ///
  final GlobalKey<State<StatefulWidget>> _patternViewKey = GlobalKey();
  GlobalKey<State<StatefulWidget>> get patternViewKey => _patternViewKey;

  static const double patternListHeightDefault = 367;

  ///
  /// ファイルハンドリング関数
  ///
  Future<void> handleKeyPress(KeyEvent event) async {
    /// パターンイメージキャプチャモデルのインスタンス作成
    final model = PatternImageCaptureModel();

    /// キーイベントを処理
    model.handleKeyPress(event, () async {
      /// カテゴリイメージデータを取得
      final categoryImageData =
          getAllCategoryImagesInfo()[state.selectCategoryIndex];

      final height = await model.getPatternListHeight(
          state.patternSize.index, categoryImageData.iconsGroup);

      /// アプリケーションの状態を更新
      state = state.copyWith(
        patternListHeight: height,
        fourSFunctionOn: true,
      );

      /// 1秒待つ
      await Future.delayed(const Duration(seconds: 1));

      /// パターンリストを画像として保存
      await model.savePatternListToImage(_patternViewKey);

      List<PatternFilterGroup>? patternFilterInfo;
      // ignore: unnecessary_type_check
      if (categoryImageData is PatternInfoGroupImplement) {
        patternFilterInfo =
            (categoryImageData as PatternInfoGroupImplement).patternFilterInfo;
      } else {
        patternFilterInfo = null;
      }

      /// スレッド情報をHTMLとして保存
      await model.saveThreadInfoToHtml(
        state.selectCategoryIndex,
        categoryImageData.categoryType,
        categoryImageData.iconsGroup,
        patternFilterInfo,
      );

      /// アプリケーションの状態を元に戻す
      state = state.copyWith(
        patternListHeight: patternListHeightDefault,
        fourSFunctionOn: false,
      );
    });
  }

  ///
  /// 2本指による拡縮
  ///
  void onScaleUpdate(ScaleUpdateDetails details) {
    if (details.pointerCount == touchScreenFingerCount) {
      _debounceTimer?.cancel();

      /// 新しいタイマーを開始し、遅延実行の時間を 300 ミリ秒に設定します
      _debounceTimer = Timer(const Duration(milliseconds: 300), () {
        _handleScaleStop(details);
      });
    }
  }

  void _handleScaleStop(ScaleUpdateDetails details) {
    PatternModel().scaleUpdate(details, _scale);
    state = state.copyWith(patternSize: PatternModel().thumbnailSize);
  }

  ///
  /// カテゴリの画像の幅を取得します
  ///
  double getCategoryImageWidth(int index) =>
      index == getAllCategoryImagesInfo().length - 1 ? 128 : 121;

  ///
  /// カントール対応関数
  /// パラメータ
  /// [in]:
  ///   x: 二つの整数のうちの一つで、ペアリングを行う自然数を表します
  ///   y: xとペアリングするもう一つの整数で、ペアリングを行うもう一つの自然数を表します
  /// [out]:
  ///   カントール対応関数は、異なる自然数のペアが一意の結果を生成することを保証します
  ///   与えられた自然数のペアを表す一意の整数を返します
  ///
  int cantorPairing(int x, int y) => (x + y) * (x + y + 1) ~/ 2 + y;
}
