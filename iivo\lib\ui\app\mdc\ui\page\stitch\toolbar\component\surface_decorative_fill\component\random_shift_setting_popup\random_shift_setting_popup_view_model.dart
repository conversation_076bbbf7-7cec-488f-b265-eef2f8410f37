import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_decorative_fill_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'random_shift_setting_popup_view_interface.dart';

final randomShiftSettingPopupViewModelProvider =
    StateNotifierProvider.autoDispose<RandomShiftSettingPopupStateViewInterface,
            RandomShiftSettingPopupState>(
        (ref) => RandomShiftSettingPopupViewModel(ref));

class RandomShiftSettingPopupViewModel
    extends RandomShiftSettingPopupStateViewInterface {
  RandomShiftSettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const RandomShiftSettingPopupState(), ref) {
    _shiftState = SurfaceDecoFillModel().getRandomShift() !=
            SurfaceDecoFillModel.randomShiftNotUpdating
        ? DecoFillSettingState.settingCompleted
        : DecoFillSettingState.unknown;

    _typeState = SurfaceDecoFillModel().getRandomShiftType() !=
            SurfaceDecoFillModel.randomShiftTypeNotUpdating.index
        ? DecoFillSettingState.settingCompleted
        : DecoFillSettingState.unknown;
    update();
  }

  ///
  /// 最大ゆらぎ程度値
  final int _maxShiftValue = 6;

  ///
  /// 最大ゆらぎタイプ値
  final int _maxTypeValue = 2;

  ///
  /// 最小ゆらぎ程度値
  ///
  final int _minShiftValue = 0;

  ///
  /// 最小ゆらぎタイプ値
  ///
  final int _minTypeValue = 0;

  ///
  /// ステップ量
  ///
  final int _stepValue = 1;

  ///
  /// ゆらぎ程度の状態
  ///
  DecoFillSettingState _shiftState = DecoFillSettingState.settingCompleted;

  ///
  /// ゆらぎタイプvの状態
  ///
  DecoFillSettingState _typeState = DecoFillSettingState.settingCompleted;

  ///
  /// ゆらぎ程度値
  ///
  int _shiftValue = SurfaceDecoFillModel().getRandomShift();

  ///
  /// ゆらぎタイプ値
  ///
  int _typeValue = SurfaceDecoFillModel().getRandomShiftType();

  ///
  /// ゆらぎタイプはデフォルト値かどか
  ///
  bool _isDefaultTypeValue = SurfaceDecoFillModel().getRandomShiftType() ==
      SurfaceDecoFillModel().randomShiftTypeDefaultValue.index;

  ///
  /// ゆらぎ程度はデフォルト値かどか
  ///
  @override
  bool get isDefaultShiftValue =>
      _shiftValue == SurfaceDecoFillModel.randomShiftNotUpdating
          ? true
          : _shiftValue == SurfaceDecoFillModel().randomShiftDefaultValue;

  ///
  /// ゆらぎタイプはデフォルト値かどか
  ///
  @override
  bool get isDefaultTypeValue =>
      _typeValue == SurfaceDecoFillModel.randomShiftTypeNotUpdating.index
          ? true
          : _isDefaultTypeValue;

  ///
  /// ゆらぎタイプの文字色を取得する
  ///
  @override
  bool get needGrayTextColor =>
      isDefaultTypeValue ? false : _shiftValue == _minShiftValue;

  ///
  /// ViewModel 更新
  ///
  @override
  void update() {
    if (_shiftValue == SurfaceDecoFillModel().randomShiftDefaultValue) {
      _isDefaultTypeValue = false;
    } else {
      _isDefaultTypeValue = _typeValue ==
          SurfaceDecoFillModel().randomShiftTypeDefaultValue.index;
    }

    state = state.copyWith(
      shiftValue: _getShiftValue(),
      typeValue: _typeValue,
      isShiftPlusButtonGrey: _isShiftPlusButtonGrey(),
      isShiftMinButtonGrey: _isShiftMinButtonGrey(),
      isTypeRightButtonGrey: _isTypeRightButtonGrey(),
      isTypeLeftButtonGrey: _isTypeLeftButtonGrey(),
    );
  }

  ///
  /// ゆらぎ程度マイナスボタンをクリックする
  ///
  @override
  bool onShiftMinusButtonClicked(bool isLongPress) {
    if (_shiftState == DecoFillSettingState.unknown) {
      _shiftValue = SurfaceDecoFillModel().randomShiftDefaultValue;
    } else {
      _shiftValue = _shiftValue - _stepValue;
      if (_shiftValue < _minShiftValue) {
        _shiftValue = _minShiftValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }

    _shiftState = DecoFillSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    update();
    return true;
  }

  ///
  /// ゆらぎ程度プラスボタンをクリックする
  ///
  @override
  bool onShiftPlusButtonClicked(bool isLongPress) {
    if (_shiftState == DecoFillSettingState.unknown) {
      _shiftValue = SurfaceDecoFillModel().randomShiftDefaultValue;
    } else {
      _shiftValue = _shiftValue + _stepValue;
      if (_shiftValue > _maxShiftValue) {
        _shiftValue = _maxShiftValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }

    _shiftState = DecoFillSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    update();
    return true;
  }

  ///
  /// ゆらぎタイプ左ボタンをクリックする
  ///
  @override
  bool onTypeLeftButtonClicked(bool isLongPress) {
    if (state.isTypeLeftButtonGrey == true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    if (_typeState == DecoFillSettingState.unknown) {
      _typeValue = SurfaceDecoFillModel().randomShiftTypeDefaultValue.index;
    } else {
      _typeValue = _typeValue - _stepValue;
      if (_typeValue < _minTypeValue) {
        _typeValue = _minTypeValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }

    _typeState = DecoFillSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    update();
    return true;
  }

  ///
  /// ゆらぎタイプ右ボタンをクリックする
  ///
  @override
  bool onTypeRightButtonClicked(bool isLongPress) {
    if (state.isTypeRightButtonGrey == true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    if (_typeState == DecoFillSettingState.unknown) {
      _typeValue = SurfaceDecoFillModel().randomShiftTypeDefaultValue.index;
    } else {
      _typeValue = _typeValue + _stepValue;
      if (_typeValue > _maxTypeValue) {
        _typeValue = _maxTypeValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }

    _typeState = DecoFillSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    update();
    return true;
  }

  ///
  /// OKボタンがクリックされました
  ///
  @override
  void onOkButtonClicked() {
    ref.read(stitchPageViewModelProvider.notifier).maybeRemovePopupRoute(
        PopupEnum.surfaceDecorativeFillRandomShift.toString());
    if (_shiftState == DecoFillSettingState.unknown &&
        _typeState == DecoFillSettingState.unknown) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    /// RandomShiftの初期値
    int oldShiftValue = SurfaceDecoFillModel().getRandomShift();

    /// RandomShiftTypeの初期値
    int oldTypeValue = SurfaceDecoFillModel().getRandomShiftType();

    if (_typeState != DecoFillSettingState.unknown ||
        _shiftState != DecoFillSettingState.unknown) {
      SurfaceDecoFillModel().setRandomShift(_shiftValue, _typeValue);
      _typeState = DecoFillSettingState.settingCompleted;
    } else {
      /// do nothing
    }

    if (SurfaceDecoFillModel().setDecorativeFillSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (oldShiftValue != _shiftValue || oldTypeValue != _typeValue) {
      ResumeHistoryModel().backSnapshot();
    } else {
      /// do nothing
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// ゆらぎタイプの表示テキストを取得する
  ///
  @override
  String getTypeString(context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;

    if (_typeState == DecoFillSettingState.unknown) {
      return SurfaceDecoFillModel.oneStarValue;
    } else {
      return [
        l10n.icon_typea,
        l10n.icon_typeb,
        l10n.icon_typec,
      ][state.typeValue];
    }
  }

  ///
  /// ゆらぎ程度を取得する
  ///
  String _getShiftValue() {
    if (_shiftState == DecoFillSettingState.unknown) {
      return SurfaceDecoFillModel.oneStarValue;
    } else {
      return _shiftValue.toString();
    }
  }

  ///
  /// グレー文字を表示する
  ///
  bool _isTypeGreyState() {
    if (_shiftState == DecoFillSettingState.unknown) {
      return false;
    }

    if (_typeState == DecoFillSettingState.unknown) {
      return false;
    } else {
      return _shiftValue == _minShiftValue ? true : false;
    }
  }

  ///
  /// ゆらぎ程度「＞」グレーボタンの判断
  ///
  bool _isShiftPlusButtonGrey() {
    if (_shiftState == DecoFillSettingState.unknown) {
      return false;
    } else {
      return _shiftValue == _maxShiftValue ? true : false;
    }
  }

  ///
  /// ゆらぎ程度「＜」グレーボタンの判断
  ///
  bool _isShiftMinButtonGrey() {
    if (_shiftState == DecoFillSettingState.unknown) {
      return false;
    } else {
      return _shiftValue == _minShiftValue ? true : false;
    }
  }

  ///
  /// ゆらぎタイプ「＞」グレーボタンの判断
  ///
  bool _isTypeRightButtonGrey() {
    if (_typeState == DecoFillSettingState.unknown) {
      return false;
    }

    if (_isTypeGreyState()) {
      return true;
    } else {
      return _typeValue == _maxTypeValue ? true : false;
    }
  }

  ///
  /// ゆらぎタイプ「＜」グレーボタンの判断
  ///
  bool _isTypeLeftButtonGrey() {
    if (_typeState == DecoFillSettingState.unknown) {
      return false;
    }

    if (_isTypeGreyState()) {
      return true;
    } else {
      return _typeValue == _minTypeValue ? true : false;
    }
  }
}
