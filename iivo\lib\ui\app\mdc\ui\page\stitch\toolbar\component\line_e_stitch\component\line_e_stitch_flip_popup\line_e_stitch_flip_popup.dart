import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'line_e_stitch_flip_popup_view_model.dart';

class FlipPopup extends ConsumerStatefulWidget {
  const FlipPopup({super.key});

  @override
  ConsumerState<FlipPopup> createState() => _FlipSettingPopupState();
}

class _FlipSettingPopupState extends ConsumerState<FlipPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(lineEStitchFlipViewModelProvider);
    final viewModel = ref.read(lineEStitchFlipViewModelProvider.notifier);

    return Row(
      children: [
        const Spacer(
          flex: 571,
        ),
        Expanded(
          flex: 229,
          child: Column(
            children: [
              const Spacer(
                flex: 159,
              ),
              Expanded(
                flex: 1052,
                child: Scaffold(
                  backgroundColor: Colors.transparent,
                  body: pre_edit_toolbar_mdc(
                    child: Column(
                      children: [
                        const Spacer(
                          flex: 129,
                        ),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_str_flip(
                                  text: l10n.icon_00567,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 89,
                        ),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 98,
                                child: grp_btn_mdc_inside(
                                  onTap: viewModel.onInsideClicked,
                                  state: state.flipInsideState
                                      ? ButtonState.select
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 9,
                              ),
                              Expanded(
                                flex: 98,
                                child: grp_btn_mdc_outside(
                                  onTap: viewModel.onOutsideClicked,
                                  state: state.flipOutsideState
                                      ? ButtonState.select
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 613,
                        ),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_btn_positive_mdc(
                                  onTap: viewModel.onOkButtonClicked,
                                  text: l10n.icon_ok,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 12,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const Spacer(
                flex: 69,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
