import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'paint_page_view_model.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'paint_page_view_interface.freezed.dart';

@freezed
class PaintPageState with _$PaintPageState {
  const factory PaintPageState({
    @Default(false) bool isMagnificationShow,
  }) = _PaintPageState;
}

abstract class PaintPageViewInterface extends ViewModel<PaintPageState> {
  PaintPageViewInterface(super.state, this.ref, this.context);

  ///
  /// providerのref
  ///
  final Ref ref;

  ///
  /// コンテキスト
  ///
  final BuildContext context;

  ///
  /// 子画面のView更新
  ///
  void updatePaintPageByChild(ModuleType vm);

  ///
  /// ComponentのView更新
  ///
  void updatePaintComponentByChild(ComponentType type);

  ///
  /// 名前付きルートの登録
  ///
  Map<String, PopupRouteBuilder> registerNamedPopup();

  ///
  /// 同期中のボタンをクリックした時のコールバック関数
  ///
  /// 戻り値：処理が中断された場合はfalseを返し、処理が終了した場合はtrueを返す
  ///
  Future<bool> lockEditProcWhenProcessing(Function() clickCallback);
}
