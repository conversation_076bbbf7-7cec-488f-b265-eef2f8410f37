import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../memory/memory.dart';
import '../../../../../../../model/app_locale.dart';
import '../../../../../../../model/handel_model.dart';
import '../../../../../../../model/machine_config_model.dart';
import '../../../../../../../model/projector_model.dart';
import '../../../../../../global_popup/global_popup_export.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../global_popup/global_popups/err_data_momory_full/err_data_momory_full_view_model.dart';
import '../../../../../../global_popup/global_popups/err_mdc_import_custom_pattern_change_after_saving/err_mdc_import_custom_pattern_change_after_saving_view_model.dart';
import '../../../../../../global_popup/global_popups/err_mdc_import_custom_pattern_emb_data_saved/err_mdc_import_custom_pattern_emb_data_saved_view_model.dart';
import '../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../../emb/model/redo_undo_model.dart';
import '../../../../model/decorative_popup_model.dart';
import '../../../../model/mdc_reset_model.dart';
import '../../../../model/memory_model.dart';
import '../../../../model/motif_popup_model.dart';
import '../../../../model/paint/drawing_type_model.dart';
import '../../../../model/paint/edit_object_model.dart';
import '../../../../model/paint/library_isolate.dart';
import '../../../../model/paint/line_property_popup_model.dart';
import '../../../../model/paint/magnification_model.dart'
    as paint_magnification;
import '../../../../model/paint/surface_property_popup_model.dart';
import '../../../../model/resume_history_model.dart';
import '../../../../model/stitch/creation_isolate.dart';
import '../../../../model/stitch/draw_region_model.dart';
import '../../../../model/stitch/magnification_model.dart'
    as stitch_magnification;
import '../../../../model/stitch/view_area_model.dart';
import '../../../component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../../../component/mdc_header/mdc_header_view_model.dart';
import '../../../page_route.dart';
import '../stitch_page_view_model.dart';
import 'bottom_view_interface.dart';
import 'component/save_popup.dart';

final bottomProvider =
    StateNotifierProvider.autoDispose<BottomViewInterface, BottomState>(
        (ref) => BottomViewModel(ref));

class BottomViewModel extends BottomViewInterface {
  BottomViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const BottomState(), ref);

  @override
  void build() {
    super.build();
    update();
  }

  @override
  bool get isProcessImage => DrawRegionModel().isProcessImage;

  @override
  void update() {
    if (!mounted) {
      return;
    }
    state = state.copyWith(
      isEnglish: AppLocale().isEnglish,
      redoButtonState: _getRedoState(),
      undoButtonState: _getUndoState(),
      isShowUndoRedoButton:
          !ProjectorModel().mdcProjector.isMdcProjectorViewOpen,
    );
  }

  ///
  /// Memoryボタンのクリック関数
  ///
  @override
  void onMemoryButtonClick(BuildContext context) {
    if (isProcessImage || CreationModel().hasErrorCode) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());

    /// Memoryポップアップを開く
    _openSavePopup(context);
  }

  ///
  /// Returnボタンをクリックする
  ///
  @override
  void onReturnButtonClicked() {
    /// プロジェクト起動中にプロジェクトを閉じる
    if (ProjectorModel().mdcProjector.isMdcProjectorViewOpen == true) {
      final mdcProjectorFunction =
          ref.read(mdcProjectorFunctionProvider.notifier);

      /// プロジェクト起動・停止前にエラーCheckをする
      /// エラーがあれば ポープアープを表示する
      final hasError = mdcProjectorFunction
          .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
      if (hasError) {
        return;
      }

      mdcProjectorFunction.closeMdcProjectorView(
        afterClosedHandleCallback: () {},
        closingHandleCallback: () {
          /// 投影画面がオフになっています
          ProjectorModel().mdcProjector.isMdcProjectorViewOpen = false;

          /// 他のページへの更新の通知
          ref
              .read(stitchPageViewModelProvider.notifier)
              .updatePageByChild(ComponentType.projector);
          ref.read(mdcHeaderViewModelProvider.notifier).update();
        },
      );
      return;
    }

    final DirErrorCode error = TpdLibrary()
        .apiBinding
        .setMatrixEnableList(MachineKeyState.machineKeyEnableAllNG);
    if (error == DirErrorCode.dirMotorError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else if (error != DirErrorCode.dirNoError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      Log.errorTrace("Undefined Error Handle");
      return;
    }

    /// CallBack関数初期化
    /// CallBack終了後、メガキーのロック状態を解除する必要があります。
    gotoMDCDetailSetToMDCPaint = () {
      _gotoMDCDetailSetToMDCPaint();
      TpdLibrary()
          .apiBinding
          .setMatrixEnableList(MachineKeyState.machineKeyEnableAll);
    };

    SystemSoundPlayer().play(SystemSoundEnum.accept);

    if (isProcessImage) {
      CreationModel().cancelStitchCreation();
    } else {
      gotoMDCDetailSetToMDCPaint?.call();
    }
  }

  ///
  /// UndoButtonクリック
  ///
  @override
  void onUndoButtonClick() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    if (isProcessImage) {
      state = state.copyWith(
        redoButtonState: false,
        undoButtonState: false,
      );
      return;
    }
    ResumeHistoryModel().undo();

    /// プロジェクター刺繍模様を再描画する
    if (ProjectorModel().mdcProjector.isMdcProjectorOpen() == true) {
      ProjectorModel().mdcProjector.refreshMdcProjector();
    } else {
      /// do nothing
    }

    /// view更新
    update();

    /// 他の画面を更新する
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updatePageByChild(ComponentType.bottom);
  }

  ///
  /// RedoButtonクリック
  ///
  @override
  void onRedoButtonClick() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.magnification.toString());
    if (isProcessImage) {
      state = state.copyWith(
        redoButtonState: false,
        undoButtonState: false,
      );
      return;
    }
    ResumeHistoryModel().redo();

    /// プロジェクター刺繍模様を再描画する
    if (ProjectorModel().mdcProjector.isMdcProjectorOpen() == true) {
      ProjectorModel().mdcProjector.refreshMdcProjector();
    } else {
      /// do nothing
    }

    /// view更新
    update();

    /// 他の画面を更新する
    ref
        .read(stitchPageViewModelProvider.notifier)
        .updatePageByChild(ComponentType.bottom);
  }

  ///
  /// SetButtonクリック
  ///
  @override
  void onSetButtonClick(BuildContext context) {
    if (isProcessImage || CreationModel().hasErrorCode) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);

      return;
    }

    if (ProjectorModel().mdcProjector.isMdcProjectorViewOpen == true) {
      final mdcProjectorFunction =
          ref.read(mdcProjectorFunctionProvider.notifier);

      /// プロジェクト起動・停止前にエラーCheckをする
      /// エラーがあれば ポープアープを表示する
      final hasError = mdcProjectorFunction
          .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
      if (hasError) {
        return;
      }

      mdcProjectorFunction.closeMdcProjectorView(
        afterClosedHandleCallback: () {},
        closingHandleCallback: () {
          /// 投影画面がオフになっています
          ProjectorModel().mdcProjector.isMdcProjectorViewOpen = false;

          /// 他のページへの更新の通知
          ref
              .read(stitchPageViewModelProvider.notifier)
              .updatePageByChild(ComponentType.projector);
          ref.read(mdcHeaderViewModelProvider.notifier).update();
        },
      );

      return;
    }

    var errorCode = MdcLibrary().apiBinding.checkMDCDetailSetToEmbEdit();
    if (errorCode == MdcLibraryError.mdcNoError) {
      /// 有効音を鳴らす
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      if (MachineConfigModel().isBrother) {
        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_MDC_FINISH_CHECK_B);
        errMdcFinishCheckBOKFunc = _errMdcFinishCheckOKFunc;
        errMdcFinishCheckBCancelFunc = _errMdcFinishCheckCancelFunc;
      } else {
        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_MDC_FINISH_CHECK_T);
        errMdcFinishCheckTOKFunc = _errMdcFinishCheckOKFunc;
        errMdcFinishCheckTCancelFunc = _errMdcFinishCheckCancelFunc;
      }
    }

    /// エラー表示
    if (errorCode == MdcLibraryError.mdcErrorInvalidPanel) {
      final tpdErrCode = TpdLibrary().apiBinding.bpIFGetError().errorCode;
      if (tpdErrCode == ErrCode_t.ERR_NO_MORE_SELECT.index) {
        return;
      }
    }

    /// 無効音を鳴らす
    if (errorCode == MdcLibraryError.mdcErrorInvalid) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }
  }

  ///
  /// saveポップアップを表示する
  ///
  void _openSavePopup(BuildContext context) {
    MdcImportDataUsingNumber checkResult =
        MdcLibrary().apiBinding.isMdcUsingImportDataNumber(true).usingNumBuff;
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (_) => SavePopup(
            onCancelClick: () => _closeSavePopup(context),
            onUSB1Click: (usbInfo) {
              if (usbInfo == null) {
                SystemSoundPlayer().play(SystemSoundEnum.invalid);
                return;
              }

              /// ユーザーが作成したイメージを外部ストレージに取り込みます
              if (checkResult.decoSetNum != 0 || checkResult.motifSetNum != 0) {
                if (MemoryModel.isFirstSaveCustomPatternToUsb == true) {
                  MemoryModel.isFirstSaveCustomPatternToUsb = false;
                  GlobalPopupRoute().updateErrorState(
                    nextRoute: GlobalPopupRouteEnum
                        .ERR_MDC_IMPORT_CUSTOM_PATTERN_EMB_DATA_SAVED,
                    arguments: ErrMdcImportCustomPatternEmbDataSavedArgument(
                        onOKButtonClicked: () {
                      _savePhxToUsb(
                        context,
                        DeviceKind.usb,
                        usbPath: usbInfo.usbPath,
                      );
                    }),
                  );
                } else {
                  SystemSoundPlayer().play(SystemSoundEnum.accept);
                  _savePhxToUsb(
                    context,
                    DeviceKind.usb,
                    usbPath: usbInfo.usbPath,
                  );
                }
              } else {
                SystemSoundPlayer().play(SystemSoundEnum.accept);
                _saveMemory(context, DeviceKind.usb, usbPath: usbInfo.usbPath);
              }
            },
            onUSB2Click: (usbInfo) {
              if (usbInfo == null) {
                SystemSoundPlayer().play(SystemSoundEnum.invalid);
                return;
              }

              /// ユーザーが作成したイメージを外部ストレージに取り込みます
              if (checkResult.decoSetNum != 0 || checkResult.motifSetNum != 0) {
                if (MemoryModel.isFirstSaveCustomPatternToUsb == true) {
                  MemoryModel.isFirstSaveCustomPatternToUsb = false;
                  GlobalPopupRoute().updateErrorState(
                    nextRoute: GlobalPopupRouteEnum
                        .ERR_MDC_IMPORT_CUSTOM_PATTERN_EMB_DATA_SAVED,
                    arguments: ErrMdcImportCustomPatternEmbDataSavedArgument(
                        onOKButtonClicked: () {
                      _savePhxToUsb(
                        context,
                        DeviceKind.usb,
                        usbPath: usbInfo.usbPath,
                      );
                    }),
                  );
                } else {
                  SystemSoundPlayer().play(SystemSoundEnum.accept);
                  _savePhxToUsb(
                    context,
                    DeviceKind.usb,
                    usbPath: usbInfo.usbPath,
                  );
                }
              } else {
                SystemSoundPlayer().play(SystemSoundEnum.accept);
                _saveMemory(context, DeviceKind.usb, usbPath: usbInfo.usbPath);
              }
            },
            onInternalStorageClick: () {
              if (checkResult.decoSetNum != 0 || checkResult.motifSetNum != 0) {
                if (MemoryModel.importPatternSavingNotifyFlag) {
                  MemoryModel.importPatternSavingNotifyFlag = false;
                  GlobalPopupRoute().updateErrorState(
                    nextRoute: GlobalPopupRouteEnum
                        .ERR_MDC_IMPORT_CUSTOM_PATTERN_CHANGE_AFTER_SAVING,
                    arguments:
                        ErrMdcImportCustomPatternChangeAfterSavingArgument(
                      onOKButtonClicked: (_) => _saveMemory(
                        context,
                        DeviceKind.internalStorage,
                      ),
                    ),
                  );
                  return;
                }
              }

              SystemSoundPlayer().play(SystemSoundEnum.accept);
              _saveMemory(context, DeviceKind.internalStorage);
            }),
        barrier: false,
      ),
    );
  }

  ///
  /// saveポップアップを閉じる￥
  ///
  void _closeSavePopup(BuildContext context) {
    PopupNavigator.pop(context: context);
  }

  ///
  /// SaveポップアップのUSB1ボタンのクリック関数
  ///
  void _saveMemory(BuildContext context, DeviceKind deviceKind,
      {String usbPath = ""}) {
    /// 保存中のポップアップを開く
    _openSavingPopup(deviceKind);

    /// ストレージファイル
    StitchMemoryModel.savePm9AndPhx(deviceKind, usingUsbPath: usbPath)
        .then((value) {
      /// エラー確認
      if (value.accessError != AccessError.none) {
        if (value.mdcLibraryError == MdcLibraryError.mdcErrorFileSaveFailed) {
          GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_DATA_MOMORY_FULL,
            arguments: DataMomoryFullArgument(
              onOKButtonClicked: GlobalPopupRoute().resetErrorState,
            ),
          );
          return;
        } else {
          return HandelModel.handleMemoryAccessError(value.accessError);
        }
      }
      GlobalPopupRoute().resetErrorState();
    });

    /// 画面遷移
    /// 自分を閉じる、前の画面に戻る
    _closeSavePopup(context);
  }

  ///
  /// SaveポップアップのUSBボタンのクリック関数
  ///
  void _savePhxToUsb(BuildContext context, DeviceKind deviceKind,
      {String usbPath = ""}) {
    /// 保存中のポップアップを開く
    _openSavingPopup(deviceKind);

    /// ストレージファイル
    StitchMemoryModel.savePhxToUsb(deviceKind, usbPath).then((value) {
      /// エラー確認
      if (value.accessError != AccessError.none) {
        if (value.mdcLibraryError == MdcLibraryError.mdcErrorFileSaveFailed) {
          GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_DATA_MOMORY_FULL,
            arguments: DataMomoryFullArgument(
              onOKButtonClicked: GlobalPopupRoute().resetErrorState,
            ),
          );
          return;
        } else {
          return HandelModel.handleMemoryAccessError(value.accessError);
        }
      }
      GlobalPopupRoute().resetErrorState();
    });

    /// 画面遷移
    /// 自分を閉じる、前の画面に戻る
    _closeSavePopup(context);
  }

  ///
  /// Saving中ポップアップ　開ける
  ///
  /// デバイス種類より、ポップアップのアイコンが変わる
  ///
  void _openSavingPopup(DeviceKind deviceKind) {
    switch (deviceKind) {
      case DeviceKind.internalStorage:
        GlobalPopupRoute().openPcSavingPopup();
        break;
      case DeviceKind.usb:
        GlobalPopupRoute().openUsbSavingPopup();
        break;
      default:
        Log.e(
            tag: memoryAccessErrorLogTag,
            description: "Unexpected deviceKind:$deviceKind");
        break;
    }
  }

  ///
  /// 刺繍編集画面への遷移
  ///
  void _errMdcFinishCheckOKFunc() {
    DeviceErrorCode deviceError = GlobalPopupRoute().resetErrorState();
    if (deviceError == DeviceErrorCode.devNoError) {
      mdcModelResetAll();
      MdcLibrary().apiBinding.mdcReleaseImageInfo();
      MdcLibrary().apiBinding.gotoMDCDetailSetToEmbEdit();
      EmbLibrary().apiBinding.initEmb();

      /// 刺しゅう編集画面に遷移してからコールするようにします。
      DrawRegionModel().setHandleToEmb();

      PagesRoute().pushNamedAndRemoveUntil(
          nextRoute: PageRouteEnum.patternEdit, untilRoute: PageRouteEnum.home);
      if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
        GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
          arguments: TroubleOccoredPowerOffArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ),
        );
      } else {
        /// Do Nothing
      }
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    }
  }

  ///
  /// ポップアップ状態解除
  ///
  void _errMdcFinishCheckCancelFunc() {
    DeviceErrorCode deviceError = GlobalPopupRoute().resetErrorState();
    if (deviceError != DeviceErrorCode.devNoError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }

  ///
  /// Undoの状態取得する
  ///
  ButtonState _getUndoState() => DrawRegionModel().isProcessImage
      ? ButtonState.disable
      : ResumeHistoryModel().mdcUndoSize <= 1
          ? ButtonState.disable
          : ButtonState.normal;

  ///
  /// Redoの状態取得する
  ///
  ButtonState _getRedoState() => DrawRegionModel().isProcessImage
      ? ButtonState.disable
      : ResumeHistoryModel().mdcUndoSize >= ResumeHistoryModel().mdcSnapSize
          ? ButtonState.disable
          : ButtonState.normal;

  ///
  /// 画面遷移
  ///
  void _gotoMDCDetailSetToMDCPaint() {
    /// brother_panel遷移
    var errorCode = MdcLibrary().apiBinding.gotoMDCDetailSetToMDCPaint();
    if (errorCode != MdcLibraryError.mdcNoError) {
      return;
    }

    stitch_magnification.MagnificationModel.magnificationLevel =
        stitch_magnification.MagnificationModel.magnification_100;
    paint_magnification.MagnificationModel.magnificationLevel =
        paint_magnification.MagnificationModel.magnification_100;
    ViewAreaModel().isDragMove = false;
    LibraryIsolate().initLibIsolate();
    DrawRegionModel().setOperation(Operation.single);

    /// MDC縫製設置画面の初回履歴を記憶
    ResumeHistoryModel().clearAllHistory();
    ResumeHistoryModel().backSnapshot();
    ResumeHistoryModel().restoreCanvasNib();

    /// お絵描き画面画像更新
    /// Stitch画面にライブラリ側のMdcDrawingTypeを変更されたため、Paint画面のMdcDrawingTypeに戻る
    /// 現在のお絵描きイメージを取得する
    MdcLibrary().apiBinding.setMdcDrawingType(DrawingTypeModel.mdcDrawingType);
    EditObjectModel().updateCanvasImageWithoutParts();

    /// 線また面の表示戻る
    _setPaintLineProperty();
    _setPaintSurfaceProperty();

    /// paint画面を更新する
    PagesRoute().pushReplacement(nextRoute: PageRouteEnum.paint);
  }

  ///
  /// 描画画面の線また面の戻る
  ///
  void _setPaintLineProperty() {
    var property =
        MdcLibrary().apiBinding.getMdcEditLineProperty().editLineProperty;
    LinePropertyPopupModel().sewKindLine = property.lineKind;
    LinePropertyPopupModel().lineToolType = property.lineType;
    if (property.lineKind == MdcSewingKindsLine.noSew &&
        property.color == ColorCode.clear) {
      LinePropertyPopupModel().colorCode =
          LinePropertyPopupModel().colorCodeWithNoSew;
    } else {
      LinePropertyPopupModel().colorCode = property.color;
    }

    int motifNo = MotifPopupModel.startMotifBuildInNo;
    if (property.lineKind != MdcSewingKindsLine.motif) {
      LinePropertyPopupModel().currentSelectedMotifNo = motifNo;
      MotifPopupModel().currentSelectedMotifNo = motifNo;
    } else {
      if (property.motifType) {
        motifNo = MotifPopupModel.startMotifCustomNo + property.motifNo - 1;
      } else {
        motifNo = property.motifNo;
      }
      MotifPopupModel().currentSelectedMotifNo = motifNo;
      LinePropertyPopupModel().currentSelectedMotifNo = motifNo;
    }
  }

  ///
  /// 描画画面の線また面の戻る
  ///
  void _setPaintSurfaceProperty() {
    var property =
        MdcLibrary().apiBinding.getMdcEditSurfaceProperty().editSurfaceProperty;
    SurfacePropertyPopupModel().sewKindsSurface = property.kind;
    SurfacePropertyPopupModel().brushToolTypes = property.brushType;
    SurfacePropertyPopupModel().mdcBrushSize = property.brushSize;
    if (property.kind == MdcSewKindsSurface.noSew &&
        property.color == ColorCode.clear) {
      SurfacePropertyPopupModel().colorCode =
          SurfacePropertyPopupModel().colorCodeWithNoSew;
    } else {
      SurfacePropertyPopupModel().colorCode = property.color;
    }

    int decorativeNo = DecorativePopupModel.startDecorativeBuildInNo;
    if (property.kind != MdcSewKindsSurface.decorativeFill) {
      SurfacePropertyPopupModel().currentSelectedDecorativeNo = decorativeNo;
      DecorativePopupModel().currentSelectedDecorativeNo = decorativeNo;
    } else {
      if (property.decorativeType) {
        decorativeNo = (property.decorativeNo - 1) +
            DecorativePopupModel.startDecorativeCustomNo;
      } else {
        decorativeNo = property.decorativeNo;
      }
      SurfacePropertyPopupModel().currentSelectedDecorativeNo = decorativeNo;
      DecorativePopupModel().currentSelectedDecorativeNo = decorativeNo;
    }
  }
}
