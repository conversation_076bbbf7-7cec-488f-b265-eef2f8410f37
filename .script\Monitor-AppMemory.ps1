﻿# 定义输出文件路径
$outputFile = "memory_log.csv"

# 获取当前组件
function Get-CurrentComponent {
  $componentInfo = & adb.exe shell dumpsys window 2>$null | Select-String -Pattern "mCurrentFocus"
  if ($componentInfo) {
    $match = [regex]::Match($componentInfo, "([a-zA-Z0-9\._]+)\/([a-zA-Z0-9\._]+)")
    if ($match.Success) {
      $package = $match.Groups[1].Value
      $activity = $match.Groups[2].Value
    }
  }
  return $package, $activity
}

# 如果文件不存在，添加表头
if (-not (Test-Path $outputFile)) {
  "Time,IsScreenSaver,total,used,free,shared,buffers" | Out-File -FilePath $outputFile -Encoding utf8
}

while ($true) {
  # 获取当前时间
  $time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

  # 获取前台应用信息（包名和活动）
  $currentPackage, $currentActivity = Get-CurrentComponent

  # 检查是否处于ScreenSaver应用中
  $isScreenSaver = $false
  if ($currentPackage -eq "com.brother.ph.iivo.screensaver") {
    $isScreenSaver = $true
  }

  # 获取内存信息
  $freeOutput = adb shell free -m

  # 从输出中提取内存行
  $memLine = ($freeOutput | Select-String "^Mem:").ToString() -replace "\s+", ","
  $memValues = $memLine -split ","

  # 提取字段（注意索引从1开始跳过"Mem:"字段）
  $total = $memValues[1]
  $used = $memValues[2]
  $free = $memValues[3]
  $shared = $memValues[4]
  $buffers = $memValues[5]

  # 格式化并写入CSV
  "$time,$isScreenSaver,$total,$used,$free,$shared,$buffers" | Out-File -FilePath $outputFile -Append -Encoding utf8

  # 等待5秒
  Start-Sleep -Seconds 10
  & adb.exe shell input touchscreen tap 10 1270
}
