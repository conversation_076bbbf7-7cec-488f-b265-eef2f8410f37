import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../common_global_popup_view_model.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'err_sr_connected_view_interface.freezed.dart';

@freezed
class ErrSrConnectedState with _$ErrSrConnectedState {
  const factory ErrSrConnectedState() = _ErrSrConnectedState;
}

abstract class ErrSrConnectedViewInterface
    extends CommonGlobalPopupViewModel<ErrSrConnectedState> {
  ErrSrConnectedViewInterface(super.state, this.ref, super.context);

  ///
  /// providerのref
  ///
  final AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンクリク関数
  ///
  void onOKButtonClicked();
}
