import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import 'package:xd_component/xd_component.dart';
import 'direction_setting_popup_view_model.dart';

class DirectionSettingPopupPopup extends ConsumerStatefulWidget {
  const DirectionSettingPopupPopup({super.key});

  @override
  ConsumerState<DirectionSettingPopupPopup> createState() =>
      _DirectionSettingPopupState();
}

class _DirectionSettingPopupState
    extends ConsumerState<DirectionSettingPopupPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final viewModel = ref.read(directionSettingPopupViewModelProvider.notifier);
    final state = ref.watch(directionSettingPopupViewModelProvider);

    return Row(
      children: [
        const Spacer(flex: 571),
        Expanded(
            flex: 229,
            child: Column(
              children: [
                const Spacer(flex: 159),
                Expanded(
                    flex: 1052,
                    child: Material(
                      color: Colors.transparent,
                      child: pre_edit_toolbar_mdc(
                        child: Column(
                          children: [
                            const Spacer(flex: 37),
                            const Expanded(
                                flex: 68,
                                child: Row(
                                  children: [
                                    Spacer(flex: 52),
                                    Expanded(
                                        flex: 126,
                                        child: ico_mdcstitch_decofill_direct()),
                                    Spacer(flex: 51),
                                  ],
                                )),
                            const Spacer(flex: 24),
                            Expanded(
                                flex: 69,
                                child: Center(
                                  child: grp_str_direction(
                                    text: l10n.icon_00541,
                                  ),
                                )),
                            const Spacer(flex: 89),
                            Expanded(
                                flex: 70,
                                child: Row(
                                  children: [
                                    const Spacer(flex: 12),
                                    Expanded(
                                      flex: 98,
                                      child: grp_btn_auto_mdc(
                                        onTap: viewModel.onAutoButtonClicked,
                                        buttonText: l10n.icon_00549_2,
                                        state: state.autoButtonState ==
                                                MDCTatamiDirKind
                                                    .mdc_tatami_dir_kind_auto
                                            ? ButtonState.select
                                            : ButtonState.normal,
                                        feedBackControl: null,
                                      ),
                                    ),
                                    const Spacer(flex: 9),
                                    Expanded(
                                      flex: 98,
                                      child: grp_btn_manual_mdc(
                                        onTap: viewModel.onManualButtonClicked,
                                        buttonText: l10n.icon_00548_2,
                                        state: state.autoButtonState ==
                                                MDCTatamiDirKind
                                                    .mdc_tatami_dir_kind_manual
                                            ? ButtonState.select
                                            : ButtonState.normal,
                                        feedBackControl: null,
                                      ),
                                    ),
                                    const Spacer(flex: 12),
                                  ],
                                )),
                            state.autoButtonState ==
                                    MDCTatamiDirKind.mdc_tatami_dir_kind_manual
                                ? Expanded(
                                    flex: 613,
                                    child: Column(
                                      children: [
                                        const Spacer(flex: 24),
                                        Expanded(
                                            flex: 69,
                                            child: Stack(
                                              children: [
                                                const Row(
                                                  children: [
                                                    Spacer(flex: 17),
                                                    Expanded(
                                                        flex: 38,
                                                        child: Column(
                                                          children: [
                                                            Spacer(flex: 7),
                                                            Expanded(
                                                                flex: 26,
                                                                child:
                                                                    ico_rotate_hyouji()),
                                                            Spacer(flex: 36),
                                                          ],
                                                        )),
                                                    Spacer(flex: 174),
                                                  ],
                                                ),
                                                Center(
                                                  child: grp_str_parameter(
                                                    text: state.directionValue,
                                                    isDefault: viewModel
                                                        .getDirectionTextStyle(),
                                                    displayText: "°",
                                                  ),
                                                ),
                                              ],
                                            )),
                                        const Spacer(flex: 12),
                                        Expanded(
                                            flex: 63,
                                            child: Row(
                                              children: [
                                                const Spacer(flex: 48),
                                                Expanded(
                                                  flex: 63,
                                                  child: grp_btn_minus_01(
                                                    state: state.isMainValue
                                                        ? ButtonState.disable
                                                        : ButtonState.normal,
                                                    onTap: () => viewModel
                                                        .onMinusButtonClicked(
                                                            false),
                                                    onLongPress: () => viewModel
                                                        .onMinusButtonClicked(
                                                            true),
                                                    feedBackControl: null,
                                                  ),
                                                ),
                                                const Spacer(flex: 7),
                                                Expanded(
                                                  flex: 63,
                                                  child: grp_btn_plus_01(
                                                    state: state.isMaxValue
                                                        ? ButtonState.disable
                                                        : ButtonState.normal,
                                                    onTap: () => viewModel
                                                        .onPlusButtonClicked(
                                                            false),
                                                    onLongPress: () => viewModel
                                                        .onPlusButtonClicked(
                                                            true),
                                                    feedBackControl: null,
                                                  ),
                                                ),
                                                const Spacer(flex: 47),
                                              ],
                                            )),
                                        const Spacer(flex: 445),
                                      ],
                                    ))
                                : const Spacer(flex: 613),
                            Expanded(
                              flex: 70,
                              child: grp_btn_positive_mdc(
                                onTap: viewModel.onOkButtonClicked,
                                text: l10n.icon_ok,
                                feedBackControl: null,
                              ),
                            ),
                            const Spacer(flex: 12),
                          ],
                        ),
                      ),
                    )),
                const Spacer(flex: 69),
              ],
            )),
      ],
    );
  }
}
