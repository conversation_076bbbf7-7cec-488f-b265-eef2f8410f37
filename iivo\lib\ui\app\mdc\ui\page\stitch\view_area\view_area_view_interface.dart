import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../model/stitch/draw_region_model.dart'
    show MdcRegionInfo, DisplayImageInfo;

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'view_area_view_interface.freezed.dart';

@freezed
class ViewAreaState with _$ViewAreaState {
  const factory ViewAreaState({
    @Default(0.5) double scale,
    @Default(564) double canvasWidth,
    @Default(880) double canvasHeight,
    @Default(564) double imageWidth,
    @Default(880) double imageHeight,
    @Default(null) Uint8List? backgroundImage,
    @Default(0.0) double backgroundDensityLevel,
    @Default(1.0) double mdcDensityLevel,
    @Default(EmbGridType.embGridNot) EmbGridType gridType,
    @Default(Colors.transparent) Color gridColor,
    @Default(Offset.zero) Offset centerOffset,
    @Default(Colors.transparent) Color frameColor,
    required Path frameDrawPath,
    required DisplayImageInfo displayImageInfo,
    @Default(false) bool isProjectorON,
    @Default(true) bool isProcessImage,
    @Default([]) List<MdcRegionInfo> patternInfoList,
    required Uint8List dspImgBeforeProcessed,
  }) = _ViewAreaState;
}

abstract class ViewAreaViewInterface extends ViewModel<ViewAreaState> {
  ViewAreaViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// キャンバス幅
  ///
  double get canvasWidth;

  ///
  /// Y方向のスクロールコントローラ
  ///
  ScrollController get scrollControllerY;

  ///
  /// X方向のスクロールコントローラ
  ///
  ScrollController get scrollControllerX;

  ///
  /// Y方向のスクロールコントローラの幅
  ///
  double get scrollControllerYWidth;

  ///
  /// X方向のスクロールコントローラの高さ
  ///
  double get scrollControllerXHeight;

  ///
  /// ドラッグ移動
  ///
  void onDragMove(Offset offset);

  ///
  /// オンスケールスタート
  ///
  void onScaleStart(ScaleStartDetails details);

  ///
  /// オンスケール更新
  ///
  void onScaleUpdate(ScaleUpdateDetails details);

  ///
  /// オンタップダウン
  ///
  void onTapDown(TapDownDetails details);

  ///
  /// View 更新
  ///
  @override
  void update();

  ///
  /// 縦線リストの取得
  ///
  List<double> getGridVerticalList();

  ///
  /// 横線リストの取得
  ///
  List<double> getGridHorizontalList();

  ///
  /// 枠種類を取得する
  ///
  EmbFrameDispType get frameType;

  ///
  /// エラー発生
  ///
  bool get hasErrorCode;
}
