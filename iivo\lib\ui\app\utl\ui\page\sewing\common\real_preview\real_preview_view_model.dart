import 'dart:async';
import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/machine_config_model.dart';
import '../../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../model/pattern_data_type/pattern_property.dart';
import '../../../../../model/pattern_model.dart';
import '../../../../../model/preview_model.dart';
import '../../../../page_route.dart';
import '../../../component/stop_sewing/stop_sewing_view_model.dart';
import 'real_preview_view_interface.dart';

///
/// 長押し使用タイマ
///
Timer? _timer;

///
/// 100ms
///
const int _timeTick = 100;

///
/// 1tick = 100ms
///
const int _shortPressTimeTick = 5;

final realPreviewViewModelProvider =
    StateNotifierProvider.autoDispose<RealPreviewViewModel, RealPreviewState>(
        (ref) => RealPreviewViewModel(ref));

class RealPreviewViewModel extends RealPreviewViewModelInterface {
  RealPreviewViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const RealPreviewState(), ref) {
    PreviewDataModel().isInRealPreview = true;

    ref.listen(
      appDisplayUtlStateProvider
          .select((value) => value.utlFuncSetting.ref.isUtlSewing),
      (previous, nextState) {
        if (previous == false &&
            nextState == true &&
            MachineConfigModel().currentMode == SettingBaseMode.utl) {
          /// 縫製が始まったら、ストップマスクを開けます
          ref
              .read(stopSewingPopupViewModelProvider.notifier)
              .openStopSewingPopup(isAllClickDisable: true);
        } else if (previous == true &&
            nextState == false &&
            MachineConfigModel().currentMode == SettingBaseMode.utl) {
          /// 縫製停止、クローズ停止マスキング
          ref
              .read(stopSewingPopupViewModelProvider.notifier)
              .closeStopSewingPopup();
        }
      },
    );

    /// Lib更新
    var (
      errorCode: _,
      imageInfo: imageInfo,
      isUpMovable: isUpMovable,
      isDownMovable: isDownMovable,
      isLeftMovable: isLeftMovable,
      isRightMovable: isRightMovable,
    ) = UtlLibrary().apiBinding.openPreviewScreen();

    state = state.copyWith(
      previewImage: Image.memory(
        imageInfo.imageData,
        gaplessPlayback: true,
      ),
      threadColor: threadColorList[PreviewDataModel().getThreadColorsIndex()],
      shiftUpButton: isUpMovable ? ButtonState.normal : ButtonState.disable,
      shiftDownButton: isDownMovable ? ButtonState.normal : ButtonState.disable,
      shiftLeftButton: isLeftMovable ? ButtonState.normal : ButtonState.disable,
      shiftRightButton:
          isRightMovable ? ButtonState.normal : ButtonState.disable,
    );
  }

  ///
  /// 拡大縮小の倍率
  ///
  final int _zoomOutMagnification = 100;
  final int _zoomInMagnification = 200;
  int _magnification = 100;

  ///
  /// 画像移動のオフセットのステップ値
  ///
  final int _zoomOutUDOffsetStep = 40;
  final int _zoomInUDOffsetStep = 80;
  final int _zoomOutLROffsetStep = 20;
  final int _zoomInLROffsetStep = 40;

  ///
  /// 画像移動のオフセット
  ///
  int _shiftUD = 0;
  int _shiftLR = 0;

  ///
  /// 表示糸色ボタンクリク
  ///
  @override
  void onThreadColorButtonClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    var threadColorIndex = PreviewDataModel().getThreadColorsIndex();

    threadColorIndex++;
    if (threadColorIndex >= threadColorList.length) {
      threadColorIndex = 0;
    }

    /// Modelデータ更新
    PreviewDataModel().setThreadColorsIndex(threadColorIndex);
    UtlImageInfo image = UtlLibrary()
        .apiBinding
        .operatePreviewImage(
          OperatePreviewParam(
            shiftUD: _shiftUD,
            shiftLR: _shiftLR,
            magnification: _magnification,
          ),
        )
        .imageInfo;

    /// View更新
    state = state.copyWith(
      previewImage: Image.memory(
        image.imageData,
        gaplessPlayback: true,
      ),
      threadColor: threadColorList[threadColorIndex],
    );
  }

  ///
  /// ZoomDownボタンクリク
  ///
  @override
  void onZoomDownButtonClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    if (!PatternDataModel().getRepeatStatus()) {
      _shiftLR = 0;
      _shiftUD = 0;
    }

    _magnification = _zoomOutMagnification;
    var (
      errorCode: _,
      imageInfo: imageInfo,
      isUpMovable: isUpMovable,
      isDownMovable: isDownMovable,
      isLeftMovable: isLeftMovable,
      isRightMovable: isRightMovable,
    ) = UtlLibrary().apiBinding.operatePreviewImage(
          OperatePreviewParam(
            shiftUD: _shiftUD,
            shiftLR: _shiftLR,
            magnification: _magnification,
          ),
        );

    /// View更新
    state = state.copyWith(
      previewImage: Image.memory(
        imageInfo.imageData,
        gaplessPlayback: true,
      ),
      zoomDownButton: ButtonState.select,
      zoomUpButton: ButtonState.normal,
      shiftUpButton: isUpMovable ? ButtonState.normal : ButtonState.disable,
      shiftDownButton: isDownMovable ? ButtonState.normal : ButtonState.disable,
      shiftLeftButton: isLeftMovable ? ButtonState.normal : ButtonState.disable,
      shiftRightButton:
          isRightMovable ? ButtonState.normal : ButtonState.disable,
    );
  }

  ///
  /// ZoomUpボタンクリク
  ///
  @override
  void onZoomUpButtonClicked() {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    if (!PatternDataModel().getRepeatStatus()) {
      _shiftLR = 0;
      _shiftUD = 0;
    }
    _magnification = _zoomInMagnification;
    var (
      errorCode: _,
      imageInfo: imageInfo,
      isUpMovable: isUpMovable,
      isDownMovable: isDownMovable,
      isLeftMovable: isLeftMovable,
      isRightMovable: isRightMovable,
    ) = UtlLibrary().apiBinding.operatePreviewImage(
          OperatePreviewParam(
            shiftUD: _shiftUD,
            shiftLR: _shiftLR,
            magnification: _magnification,
          ),
        );

    /// View更新
    state = state.copyWith(
      previewImage: Image.memory(
        imageInfo.imageData,
        gaplessPlayback: true,
      ),
      zoomDownButton: ButtonState.normal,
      zoomUpButton: ButtonState.select,
      shiftUpButton: isUpMovable ? ButtonState.normal : ButtonState.disable,
      shiftDownButton: isDownMovable ? ButtonState.normal : ButtonState.disable,
      shiftLeftButton: isLeftMovable ? ButtonState.normal : ButtonState.disable,
      shiftRightButton:
          isRightMovable ? ButtonState.normal : ButtonState.disable,
    );
  }

  @override
  void onShiftUpButtonClicked() {
    if (state.shiftUpButton == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    _operatePreviewImage(true, _zoomOutUDOffsetStep, _zoomInUDOffsetStep);

    _timer?.cancel();
    _timer = null;
    _timer = Timer.periodic(const Duration(milliseconds: _timeTick), (timer) {
      if (state.shiftUpButton == ButtonState.disable) {
        return;
      }
      if (timer.tick >= _shortPressTimeTick) {
        _operatePreviewImage(true, _zoomOutUDOffsetStep, _zoomInUDOffsetStep);
      }
    });
  }

  @override
  void onShiftDownButtonClicked() {
    if (state.shiftDownButton == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    _operatePreviewImage(true, -_zoomOutUDOffsetStep, -_zoomInUDOffsetStep);

    _timer?.cancel();
    _timer = null;
    _timer = Timer.periodic(const Duration(milliseconds: _timeTick), (timer) {
      if (state.shiftDownButton == ButtonState.disable) {
        return;
      }
      if (timer.tick >= _shortPressTimeTick) {
        _operatePreviewImage(true, -_zoomOutUDOffsetStep, -_zoomInUDOffsetStep);
      }
    });
  }

  @override
  void onShiftLeftButtonClicked() {
    if (state.shiftLeftButton == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    _operatePreviewImage(false, _zoomOutLROffsetStep, _zoomInLROffsetStep);

    _timer?.cancel();
    _timer = null;
    _timer = Timer.periodic(const Duration(milliseconds: _timeTick), (timer) {
      if (state.shiftLeftButton == ButtonState.disable) {
        return;
      }
      if (timer.tick >= _shortPressTimeTick) {
        _operatePreviewImage(false, _zoomOutLROffsetStep, _zoomInLROffsetStep);
      }
    });
  }

  @override
  void onShiftRightButtonClicked() {
    if (state.shiftRightButton == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    _operatePreviewImage(false, -_zoomOutLROffsetStep, -_zoomInLROffsetStep);

    _timer?.cancel();
    _timer = null;
    _timer = Timer.periodic(const Duration(milliseconds: _timeTick), (timer) {
      if (state.shiftRightButton == ButtonState.disable) {
        return;
      }

      if (timer.tick >= _shortPressTimeTick) {
        _operatePreviewImage(
            false, -_zoomOutLROffsetStep, -_zoomInLROffsetStep);
      }
    });
  }

  @override
  void onCloseButtonClicked() {
    final DirErrorCode dirError = TpdLibrary()
        .apiBinding
        .setMatrixEnableList(MachineKeyState.machineKeyEnableAllNG);
    if (dirError == DirErrorCode.dirMotorError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else if (dirError != DirErrorCode.dirNoError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      Log.errorTrace("Undefined Error Handle");
      return;
    }

    /// Lib更新
    UtlLibraryError error =
        UtlLibrary().apiBinding.closePreviewScreen().errorCode;
    if (error != UtlLibraryError.utlNoError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);

      TpdLibrary()
          .apiBinding
          .setMatrixEnableList(MachineKeyState.machineKeyEnableAll);
      return;
    }
    if (PatternDataModel().getPatternMode() == PatternMode.custom) {
      TpdLibrary().apiBinding.gotoUtlPreview();
    } else {
      /// DoNothing
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    PagesRoute().pop();

    TpdLibrary()
        .apiBinding
        .setMatrixEnableList(MachineKeyState.machineKeyEnableAll);
  }

  @override
  void timerCancel() {
    _timer?.cancel();
  }

  @override
  void dispose() {
    PreviewDataModel().isInRealPreview = false;
    _timer?.cancel();
    super.dispose();
  }

  ///
  /// 画面上の上下左右移動操作
  ///
  void _operatePreviewImage(bool isUpDown, int zoomOutStep, int zoomInStep) {
    if (state.zoomUpButton == ButtonState.select) {
      if (isUpDown) {
        _shiftUD += zoomInStep;
      } else {
        _shiftLR += zoomInStep;
      }
    } else {
      if (isUpDown) {
        _shiftUD += zoomOutStep;
      } else {
        _shiftLR += zoomOutStep;
      }
    }

    var (
      errorCode: _,
      imageInfo: imageInfo,
      isUpMovable: isUpMovable,
      isDownMovable: isDownMovable,
      isLeftMovable: isLeftMovable,
      isRightMovable: isRightMovable,
    ) = UtlLibrary().apiBinding.operatePreviewImage(
          OperatePreviewParam(
            shiftUD: _shiftUD,
            shiftLR: _shiftLR,
            magnification: _magnification,
          ),
        );

    state = state.copyWith(
      previewImage: Image.memory(
        imageInfo.imageData,
        gaplessPlayback: true,
      ),
      shiftUpButton: isUpMovable ? ButtonState.normal : ButtonState.disable,
      shiftDownButton: isDownMovable ? ButtonState.normal : ButtonState.disable,
      shiftLeftButton: isLeftMovable ? ButtonState.normal : ButtonState.disable,
      shiftRightButton:
          isRightMovable ? ButtonState.normal : ButtonState.disable,
    );
  }
}
