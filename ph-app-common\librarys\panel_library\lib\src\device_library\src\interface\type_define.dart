// ignore_for_file: constant_identifier_names

import 'dart:ffi';
import 'dart:typed_data';
import 'dart:ui';

import 'package:ffi/ffi.dart';
import 'package:meta/meta.dart';
import 'package:ph_eel_plugin/devedit_bindings_generated.dart' as dev_gen;
import 'package:ph_eel_plugin/embedit_bindings_generated.dart';

import '../device_library_log.dart';

///
/// マシンモデル
///
enum StartMode {
  normal,
  testMode,
}

///
/// マシンモデル
///
enum MachineModel {
  brother,
  tacony,
}

enum NeedlePlateType { nothing, normal, straight }

enum SpeedControlLevel { low, middle, high }

enum ShowTimeType { am, pm, hour }

enum MyIllustMode {
  /// 編集
  edit,

  /// テースト（プレビュー）
  test,

  /// MyIllustでわなく
  none
}

enum FeedDogStatus {
  up,
  down,
}

///
/// カメラペンUI揃え
///
enum CameraPenAlignType {
  /// bottom left
  left,

  /// bottom right
  right,

  /// bottom
  bottom,
}

///  投影UIの配置状態
enum CameraUiArrangeState {
  bottom,
  right,
  left,
  miniAndBottom,
  miniAndRight,
  miniAndRightLeft,
  ;

  ///
  /// int を CameraUiArrangeState に変換します
  ///
  @internal
  static CameraUiArrangeState fromFfiApiIntValue(int value) {
    if (value < 0 || value >= CameraUiArrangeState.values.length) {
      DeviceLibraryLog.e("invalid value");
      return CameraUiArrangeState.bottom;
    } else {
      return CameraUiArrangeState.values[value];
    }
  }

  ///
  /// int を toFfiApiIntValue に変換します
  ///
  @internal
  static int toFfiApiIntValue(CameraUiArrangeState value) {
    return value.index;
  }

  ///
  /// CameraPenAlignTypeに変換します
  ///
  @internal
  CameraPenAlignType toCameraPenAlignType() {
    return switch (this) {
      CameraUiArrangeState.bottom => CameraPenAlignType.bottom,
      CameraUiArrangeState.right => CameraPenAlignType.right,
      CameraUiArrangeState.left => CameraPenAlignType.left,
      CameraUiArrangeState.miniAndBottom => CameraPenAlignType.bottom,
      CameraUiArrangeState.miniAndRight => CameraPenAlignType.right,
      CameraUiArrangeState.miniAndRightLeft => CameraPenAlignType.left,
    };
  }

  ///
  /// Minimumに変換します
  ///
  @internal
  bool toMinimum() {
    return switch (this) {
      CameraUiArrangeState.bottom ||
      CameraUiArrangeState.right ||
      CameraUiArrangeState.left =>
        false,
      CameraUiArrangeState.miniAndBottom ||
      CameraUiArrangeState.miniAndRight ||
      CameraUiArrangeState.miniAndRightLeft =>
        true,
    };
  }

  ///
  /// int を CameraUiArrangeState に変換します
  ///
  @internal
  static CameraUiArrangeState toCameraUiArrangeState(
      CameraPenAlignType cameraPenAlignType, bool isMinimum) {
    final cameraUiArrangeState = isMinimum == true
        ? switch (cameraPenAlignType) {
            CameraPenAlignType.bottom => CameraUiArrangeState.miniAndBottom,
            CameraPenAlignType.right => CameraUiArrangeState.miniAndRight,
            CameraPenAlignType.left => CameraUiArrangeState.miniAndRightLeft,
          }
        : switch (cameraPenAlignType) {
            CameraPenAlignType.bottom => CameraUiArrangeState.bottom,
            CameraPenAlignType.right => CameraUiArrangeState.right,
            CameraPenAlignType.left => CameraUiArrangeState.left,
          };

    return cameraUiArrangeState;
  }
}

typedef FrameSizeType = FrameSize_t;
typedef FrameSize = int;
typedef ModeSpec = dev_gen.ModeSpec_t;
typedef BoardType = dev_gen.BOARD_TYPE;

///
/// 枠サイズ種類 UGK後の刺繍枠表示状態 グリッド設定新設により枠のみ設定に変更
/// EmbFrameDispType_tと参照
///
enum EmbFrameDispType {
  /// 240_360
  embFrameSize_36_24,

  /// 200_300
  embFrameSize_30_20,

  /// 240_240
  embFrameSize_24_24,

  /// 200_200
  embFrameSize_20_20,

  /// 160_260
  embFrameSize_26_16,

  /// 従来枠＋SNC連携時マグネット枠
  embFrameSize_18_13,

  /// ボーダー枠
  embFrameSize_18_10,

  /// 150_150
  embFrameSize_15_15,

  /// 100_100
  embFrameSize_10_10,

  /// 60_20
  embFrameSize_2_6,

  /// ジャンボボーダー枠
  embFrameSize_30_10,

  /// 272_408
  embFrameSize_408_272,

  /// （KIT1同梱）
  embFrameSize_27_27,

  /// KIT2時別売りマグネット枠
  embFrameSize_36_18,

  /// 254_254
  embFrameSize_254_254,

  /// 180_300
  embFrameSize_300_180,

  /// （デフォルト）
  embFrameSize_465_297;
}

enum FootControlConnectType {
  ///未接続
  disconnect,

  ///Normal接続
  normal,

  ///MFFC接続
  heelSwitchOnly,

  ///MFFC＋SidePedal接続
  heelSwitchAndSidePedal;

  int get number => index;
  static FootControlConnectType getValuesByNumber(int number) =>
      FootControlConnectType.values.firstWhere(
        (element) => element.number == number,
      );
}

typedef HeelSwitch = dev_gen.FotHeelKick_FuncNum_t;
typedef SidePedal = dev_gen.FotSideSw_FuncNum_t;
typedef CameraUISetting = dev_gen.EMBCAMERAUI_PROJECTION_E;

///
/// 表示単位
///
enum DisplayUnit {
  mm,
  inch,
}

enum BaseLineType {
  /// 中基線 false
  T,

  /// 左基線 true
  B;

  int get number => index;
  static BaseLineType getValuesByBool(bool value) =>
      value ? BaseLineType.B : BaseLineType.T;

  static bool getValueByType(BaseLineType value) =>
      value == BaseLineType.B ? true : false;
}

/// スキャン品質のタイプ
enum ScanQualityType {
  /// きれい （ＸＰでは、画像の品質のため、「きれい」を初期値にした）
  fine,

  /// 標準
  standard;

  ///
  /// int を ScanQualityType に変換します
  ///
  static ScanQualityType fromFfiApiIntValue(int value) {
    ScanQualityType quality = ScanQualityType.fine;
    if (value == 0x02) {
      quality = ScanQualityType.standard;
    } else {
      quality = ScanQualityType.fine;
    }

    return quality;
  }

  ///
  /// ScanQualityType を boolに変換します
  ///
  static bool toFfiApiBoolValue(ScanQualityType value) {
    bool isQuality = false;
    if (value == ScanQualityType.standard) {
      isQuality = false;
    } else if (value == ScanQualityType.fine) {
      isQuality = true;
    } else {
      isQuality = true;
      DeviceLibraryLog.e("invalid value");
    }

    return isQuality;
  }
}

class Adjustment {
  const Adjustment(
    this.positionValue,
    this.pointBrightness,
  );

  final int positionValue;
  final int pointBrightness;
}

///
/// グリッド種類
///
enum EmbGridType {
  // 無し　（デフォルト）
  embGridNot,

  /// 10mm格子
  embGridGridLine10,

  /// 25mm格子
  embGridGridLine_25,

  /// センターカーソル
  embGridCenterLine,

  /// 中央赤十字
  embGridMark;
}

/// 対応言語（国別） 定数定義（言語 番号定義）
typedef Language = dev_gen.LangNum_t;

///
/// システム言語の種類
///
enum SystemLanguageEnum {
  english(languageCode: "en", countryCode: "US"),

  german(languageCode: "de", countryCode: "DE"),

  french(languageCode: "fr", countryCode: "FR"),

  italian(languageCode: "it", countryCode: "IT"),

  dutch(languageCode: "nl", countryCode: "NL"),

  spanish(languageCode: "es", countryCode: "ES"),

  japanese(languageCode: "ja", countryCode: "JP");

  const SystemLanguageEnum({
    required this.languageCode,
    required this.countryCode,
  });

  final String languageCode;
  final String countryCode;

  static SystemLanguageEnum getSystemFromLanguage(Language language) =>
      switch (language) {
        Language.LANG_ENGLISH => SystemLanguageEnum.english,
        Language.LANG_GERMAN => SystemLanguageEnum.german,
        Language.LANG_FRENCH => SystemLanguageEnum.french,
        Language.LANG_ITALIAN => SystemLanguageEnum.italian,
        Language.LANG_DUTCH => SystemLanguageEnum.dutch,
        Language.LANG_SPANISH => SystemLanguageEnum.spanish,
        Language.LANG_JAPAN => SystemLanguageEnum.japanese,
      };
}

/// 読み上げ言語種類
enum MessageSoundLanguage {
  englishA,
  englishB,
  germanA,
  germanB,
  frenchA,
  frenchB,
  italianA,
  italianB,
  dutchA,
  dutchB,
  spanishA,
  spanishB,
  japaneseA,
  japaneseB,
}

///
/// バックグラウンドカラー
///
class ProjectorBackgroundColor {
  ProjectorBackgroundColor(this.red, this.green, this.blue);
  int red;
  int green;
  int blue;
}

///
/// アウトライン
///
enum ProjectorPatternOutlineState {
  off,
  on,
  ;

  static ProjectorPatternOutlineState fromFfiApiBoolValue(bool value) =>
      value == true
          ? ProjectorPatternOutlineState.on
          : ProjectorPatternOutlineState.off;

  static bool toFfiApiBoolValue(ProjectorPatternOutlineState state) =>
      state == ProjectorPatternOutlineState.on ? true : false;
}

///
/// ポインターカラー
///
class ProjectorPointerColor {
  ProjectorPointerColor(this.red, this.green, this.blue);
  int red;
  int green;
  int blue;
}

///
/// ポインターShape
///
enum ProjectorPointerShapeType {
  cross, // 十字（十）
  dot, // ドット（・）
  ;

  ///
  /// int を ProjectorPointerShapeType に変換します
  ///
  static ProjectorPointerShapeType fromFfiApiIntValue(int value) {
    ProjectorPointerShapeType shape = ProjectorPointerShapeType.cross;
    if (value == dev_gen.PRJ_POINTERSHAPE_E.PRJ_POINTERSHAPE_CROSS) {
      shape = ProjectorPointerShapeType.cross;
    } else if (value == dev_gen.PRJ_POINTERSHAPE_E.PRJ_POINTERSHAPE_DOT) {
      shape = ProjectorPointerShapeType.dot;
    } else {
      shape = ProjectorPointerShapeType.cross;
      DeviceLibraryLog.e("invalid value");
    }

    return shape;
  }

  ///
  /// int を ProjectorPointerShapeType に変換します
  ///
  static int toFfiApiIntValue(ProjectorPointerShapeType value) {
    int shape = dev_gen.PRJ_POINTERSHAPE_E.PRJ_POINTERSHAPE_CROSS;
    if (value == ProjectorPointerShapeType.cross) {
      shape = dev_gen.PRJ_POINTERSHAPE_E.PRJ_POINTERSHAPE_CROSS;
    } else if (value == ProjectorPointerShapeType.dot) {
      shape = dev_gen.PRJ_POINTERSHAPE_E.PRJ_POINTERSHAPE_DOT;
    } else {
      shape = dev_gen.PRJ_POINTERSHAPE_E.PRJ_POINTERSHAPE_CROSS;
      DeviceLibraryLog.e("invalid value");
    }

    return shape;
  }
}

typedef CameraNDPMessage = dev_gen.CameraNDPEMessage_t;

///
/// カメラペンUIの表示/非表示
///
enum ProjectorPointerStylusState {
  on,
  off,
}

///
/// プロジェクターの設定
///
class ProjectorSetting {
  ProjectorSetting(
      this.brightness,
      this.backgroundColorIndex,
      this.backgroundColor,
      this.patternOutline,
      this.pointerShape,
      this.pointerColorIndex,
      this.pointerColor,
      this.stylusFunction);
  int brightness;
  int backgroundColorIndex;
  ProjectorBackgroundColor backgroundColor;
  ProjectorPatternOutlineState patternOutline;
  int pointerColorIndex;
  ProjectorPointerColor pointerColor;
  ProjectorPointerShapeType pointerShape;
  ProjectorPointerStylusState stylusFunction;
}

///
/// 起動時の表示画面
///
class InitialScreenEnum {
  static const int openingScreen = 0;
  static const int homePage = 1;
  static const int sewingOrEmbroidery = 2;
}

typedef DeviceError = int;

///音量の最大値
const int maxVolume = 5;
const double offVolume = 0;

///
/// ※針落ち前の警告音は、音量OFFであっても音量1で鳴らす
///
const int srVolumeOffNeedleDownVolume = 1;

///
/// スナップショット保存数の初期値
///
const int mdcSnapDefaultSize = 0;

///
/// Undo可能な回数の初期値
///
const int mdcUndoDefaultSize = 0;

///
/// インデックスの初期値を保存します
///
const int mdcSnapDefaultPoint = -1;

@Deprecated("use ErrCode_t to instead")
class DeviceErrors {
  /// no error
  static const DeviceError noError = 0;

  //////////////////////////////////////////////////////////////////
  ///
  /// TODO: 以下は新しい仕様のerrorです。XPでは対応していません。
  /// コードの詳細はBILが最終決定します
  ///
  //////////////////////////////////////////////////////////////////

  /// この画面ではステッチレギュレータの駆動開始できません。
  static const DeviceError errSRDriveCannotStart = 444;

  /// モード選択をしてから縫製開始して下さい
  static const DeviceError errSRSelectModeStart = 445;

  ///
  static const DeviceError errSample = 447;

  ///
  static const DeviceError errEula = 448;

  /// 針が動きます。針本から手を離して下さい。
  static const DeviceError needleBarWillMove = 449;

  /// プロジェクタ投影エラー002
  static DeviceError errProjEmb002 = 450;

  /// プロジェクタ投影エラー003
  static DeviceError errProjEmb003 = 451;

  /// プロジェクタ投影エラー04
  static DeviceError errProjEmb04 = 452;

  /// プロジェクタ投影エラー05
  static DeviceError errProjEmb05 = 453;

  /// 刺繍模様投影強制終了時の枠外し待ち
  static DeviceError errEmbProjForceQuitFrameoffWaiting = 454;

  /// プロジェクタ投影 刺繍キャリッジが動きます、手や物を離してください
  static DeviceError errProjEmbCarryMovingFrameMove = 455;

  /// 7秒以上布送り動作が無い、SRモードを自動解除され
  static DeviceError errSRModeAutoCancel = 456;

  /// 針上下スイッチで針を上にあげてください
  static DeviceError errSRModeNeedleUp = 457;

  /// ステッチレギュレータユニットとの通信ができなくなったり
  static DeviceError errSRUnableToCommunicate = 458;
}

///
/// 画面baseモード
///
enum BaseMode {
  utl,
  emb,
  mdc,
}

///
/// 縫製状況
///
enum SewingStatus {
  noSewing,
  start,
  pause,
  resume,
  finish,
}

///
/// ユーザー設定項目の値
///
class UserSettingItemValue {
  const UserSettingItemValue(
    this.maxValue,
    this.minValue,
    this.defaultValue,
    this.stepValue,
  );

  final int maxValue;
  final int minValue;
  final int defaultValue;
  final int stepValue;

  static UserSettingItemValue fromPointer(
          Pointer<dev_gen.UserSettingItemValue> pointer) =>
      UserSettingItemValue(
        pointer.ref.maxValue,
        pointer.ref.minValue,
        pointer.ref.defaultValue,
        pointer.ref.stepValue,
      );
}

class DisplayValue<T> {
  const DisplayValue(
    this.value,
    this.display,
  );

  final T value;
  final String display;

  @override
  String toString() => "value:$value,display:$display\n";
}

///
///　[defaultValue]は valueList中に必ず一つ[value]と同じです、valueListのindexではないです
///
class UserSettingItemValueList<T> {
  const UserSettingItemValueList(
    this.defaultValue,
    this.listSize,
    this.valueList,
  );

  final T defaultValue;
  final int listSize;
  final List<DisplayValue<T>> valueList;

  static UserSettingItemValueList<V> fromPointer<V>(
    Pointer<dev_gen.UserSettingItemValueList> pointer,
    V Function(int value) change,
  ) {
    final List<DisplayValue<V>> displayValueList = [];

    for (var i = 0; i < pointer.ref.listSize; i++) {
      final dev_gen.DisplayValue displayValue =
          (pointer.ref.valueLiset + i).ref;
      displayValueList.add(
        DisplayValue<V>(
          change(displayValue.value),
          displayValue.display.cast<Utf8>().toDartString(),
        ),
      );
    }

    return UserSettingItemValueList<V>(
      change(pointer.ref.defaultValue),
      pointer.ref.listSize,
      displayValueList,
    );
  }

  @override
  String toString() => """
defaultValue:$defaultValue,listSize:$listSize,valueList.length:${valueList.length}
valueList:$valueList
""";
}

class UtlUserSettingInfo {
  const UtlUserSettingInfo(
      {required this.isWidthControl,
      required this.fineAdjustHorizValue,
      required this.presserFootHeightValue,
      required this.presserFootPressureValue,
      required this.isAutomaticFabricSensorSystem,
      required this.isBaseLine,
      required this.pivotingHeightValue,
      required this.freeMotionFootHeighValue,
      required this.dualFeedFeedAdjustmentValue,
      required this.isAutoDown,
      required this.isPressToTrim,
      required this.isInitialStitchPage,
      required this.isReinforcementPriority,
      required this.heelSwitchValue,
      required this.sidePedalValue,
      required this.isEndPointSettingTemporaryStop});

  /// スピードコントロールレバーを使用してWIDTH調節を行う。
  final bool isWidthControl;

  /// 送り調整（模様調整－縦方向）
  final int fineAdjustHorizValue;

  /// 押え高さ調整
  final int presserFootHeightValue;

  /// 押えの圧力を調節する
  final int presserFootPressureValue;

  /// センサーシステム（自動押え圧補整）
  final bool isAutomaticFabricSensorSystem;

  /// 初期針位置（基線切り替え）
  final bool isBaseLine;

  /// ピボット押え高さ
  final int pivotingHeightValue;

  /// フリーモーション押え高さ
  final int freeMotionFootHeighValue;

  /// デュアルフィード送り調整
  final int dualFeedFeedAdjustmentValue;

  /// 自動下げの値を設定する
  final bool isAutoDown;

  /// 自動押え上下
  final bool isPressToTrim;

  /// 実用のデフォルトカテゴリーを実用カテゴリーにするかキルトカテゴリーにするか設定する
  final bool isInitialStitchPage;

  /// 止め縫い優先
  final bool isReinforcementPriority;

  /// マルチファンクションフットコントローラー
  final int heelSwitchValue;

  /// マルチファンクションフットコントローラー
  final int sidePedalValue;

  /// 終点設定直前停止
  final bool isEndPointSettingTemporaryStop;
}

class EmbUserSettingInfo {
  const EmbUserSettingInfo({
    required this.embroideryFrameDisplay,
    required this.grid,
    required this.maxEmbroiderySpeed,
    required this.embroideryTension,
    required this.embroideryFootHeight,
    required this.embroideryNeedleStopPosition,
    required this.isAutoDown,
    required this.displayUnit,
    required this.threadColor,
    required this.threadBrand,
    required this.embroideryBackgroundColor,
    required this.thumbnailBackgroundColor,
    required this.thumbnailSize,
    required this.embroideryBastingDistance,
    required this.scanQuality,
    required this.fabricThicknessSensor,
    required this.isScanBackImgView,
  });

  /// 刺繍縫製エリア表示設定
  final int embroideryFrameDisplay;

  /// グリッドの種類を選択する
  final int grid;

  /// 刺繍最高回転数設定
  final int maxEmbroiderySpeed;

  /// 刺繍糸調子設定
  final int embroideryTension;

  /// 刺繍の押えの高さを設定する
  final int embroideryFootHeight;

  /// 刺繍時針停止位置（上下）
  final bool embroideryNeedleStopPosition;

  /// 自動下げの値を設定する
  final bool isAutoDown;

  /// インチ/ミリ切り替え
  final int displayUnit;

  /// 糸番号表示か、糸色名表示か選択する
  final int threadColor;

  /// 糸ブランド
  final int threadBrand;

  /// 刺繍イメージ表示エリア　背景色変更
  final int embroideryBackgroundColor;

  /// サムネイル背景色変更
  final int thumbnailBackgroundColor;

  /// サムネイルサイズ設定
  final int thumbnailSize;

  /// 刺繍しつけの大きさを模様に対して調整する
  final int embroideryBastingDistance;

  /// 背景画像表示スキャン品質
  final int scanQuality;

  /// 布厚センサー
  final bool fabricThicknessSensor;

  /// 背景画像の削除、登録、登録状況
  final bool isScanBackImgView;
}

class CommonUserSettingInfo {
  const CommonUserSettingInfo({
    required this.light,
    required this.screenDisplayBrightness,
    required this.isUpperAndBobbinThreadSensor,
    required this.machineSpeakerVolume,
    required this.isNeedlePositionStitchPlacement,
    required this.initialScreen,
    required this.ecoMode,
    required this.shutoffSupportMode,
    required this.screenSaverTime,
    required this.screenSaverType,
    required this.mousePointer,
    required this.projectorBrightness,
    required this.projectorBackgroundColor,
    required this.isProjectorPatternOutline,
    required this.pointerColor,
    required this.pointerSharpe,
    required this.language,
  });

  /// 言語を切り替える
  final int language;

  /// ライトの明るさ調整
  final int light;

  /// 液晶画面の明るさを調整する
  final int screenDisplayBrightness;

  /// 上糸/下糸センサー
  final bool isUpperAndBobbinThreadSensor;

  /// 効果音ボリューム
  final int machineSpeakerVolume;

  /// ３段階針上下針上下スイッチ押下時の動作を変更する。
  final bool isNeedlePositionStitchPlacement;

  /// 起動時の表示画面を選択する。
  final int initialScreen;

  /// "スリープ機能 Eco Mode"
  final int ecoMode;

  /// Shutoff Support Modeに入る時間を設定する。
  final int shutoffSupportMode;

  /// スクリーンセーバー画面を起動する時間と、表示する画像を設定する。
  final int screenSaverTime;

  final int screenSaverType;

  /// マウスポインター
  final int mousePointer;

  /// プロジェクターの明るさ
  final int projectorBrightness;

  /// プロジェクターの背景色
  final int projectorBackgroundColor;

  /// プロジェクターのアウトライン
  final bool isProjectorPatternOutline;

  /// プロジェクターのポインタ色
  final int pointerColor;

  final int pointerSharpe;
}

class DeviceSettingInfo {
  const DeviceSettingInfo({
    required this.serviceCount,
    required this.totalCount,
    required this.productNo,
    required this.serialNumber,
    required this.isReplacedSerialNumber,
    required this.panelMajorVer,
    required this.panelMinorVer,
    required this.panelPatchVer,
    required this.spec,
    required this.isKit,
    required this.ModelNo,
    required this.libPanelMajorVer,
    required this.libPanelMinorVer,
    required this.libPanelPatchVer,
    required this.modeCode,
    required this.isProductsJapan,
  });

  /// サービスカウント
  final int serviceCount;

  /// トータルカウント
  final int totalCount;

  /// プロダクトNo
  final String productNo;

  /// シリアルナンバー
  final String serialNumber;

  /// serial number置き換えの有無 : 0=なし, 1=あり(おかしなコードがあり'?'に置き換えた)
  final bool isReplacedSerialNumber;

  /// パネル プログラム Ver.
  final int panelMajorVer;

  /// パネル プログラム Ver.
  final int panelMinorVer;

  /// パネル プログラム Ver.
  final int panelPatchVer;

  /// パ仕向け
  @Deprecated('Use `getSpec()` method instead')
  final int spec;

  final List<bool> isKit;

  /// Limited：0x80,ノーマル：その他
  final int ModelNo;

  /// ライブラリ プログラム Ver
  final int libPanelMajorVer;

  /// ライブラリ プログラム Ver
  final int libPanelMinorVer;

  /// ライブラリ プログラム Ver.
  final int libPanelPatchVer;

  /// 機種コード
  final String modeCode;

  /// 日本仕向けか
  final bool isProductsJapan;

  @override
  String toString() => """
serviceCount:$serviceCount,totalCount:$totalCount,productNo:$productNo,
serialNumber:$serialNumber,
isReplacedSerialNumber:$isReplacedSerialNumber,
panelMajorVer:$panelMajorVer,
panelMinorVer:$panelMinorVer,
panelPatchVer:$panelPatchVer,
spec:$spec,0x${spec.toRadixString(16)},
isKit:$isKit,
ModelNo:$ModelNo,
libPanelMajorVer:$libPanelMajorVer,
libPanelMinorVer:$libPanelMinorVer,
libPanelPatchVer:$libPanelPatchVer,
""";
}

///
/// 設定ページ特殊機能を設定できるかどうか
///
class UserSettingEnabledInfo {
  const UserSettingEnabledInfo({
    required this.heelSwitch,
    required this.sidePedal,
    required this.presserFootHeight,
    required this.presserFootPressure,
    required this.baseLine,
    required this.ledStart,
  });

  final bool heelSwitch;

  final bool sidePedal;

  final bool presserFootHeight;

  final bool presserFootPressure;

  final bool baseLine;

  final bool ledStart;

  @override
  String toString() => """
heelSwitch:$heelSwitch,
sidePedal:$sidePedal,
presserFootHeight:$presserFootHeight
presserFootPressure:$presserFootPressure,
baseLine:$baseLine,
ledStart:$ledStart,
""";
}

///
/// ソフトVer.を取得する
///
class ModelSoftVersionInfo {
  const ModelSoftVersionInfo({
    required this.serialNumber,
    required this.isReplacedSerialNumber,
    required this.productId,
    required this.panelVersion,
    required this.isPanelSubVersion,
    required this.panelSubVersion,
    required this.mainVersion,
    required this.bootVersion,
    required this.masterVersion,
    required this.fpgaVersion,
    required this.xyCpuVersion,
    required this.boardType,
  });

  /// serial number ※9桁 (バッファに余裕を持たせている)
  final List<String> serialNumber;

  /// serial number置き換えの有無 : 0=なし, 1=あり(おかしなコードがあり'?'に置き換えた)
  final int isReplacedSerialNumber;

  /// PRODUCT ID ※10桁 (バッファに余裕を持たせている)
  final Uint8List productId;

  /// パネル プログラム Ver.
  final int panelVersion;

  /// パネル プログラム Sub Ver.の有無 : 0=なし, 1=あり
  final int isPanelSubVersion;

  /// パネル プログラム Sub Ver.
  final int panelSubVersion;

  /// メイン プログラム Ver.
  final int mainVersion;

  /// BOOT プログラム Ver.
  final int bootVersion;

  /// マスター Ver.
  final int masterVersion;

  /// FPGA Ver.
  final int fpgaVersion;

  /// XY_CPU Ver.
  final int xyCpuVersion;

  /// BOARD TYPE : 0=TYPE-A, 1=TYPE-B, 2=TYPE-C, 3=TYPE-D ※brother_panel側 SOC_TYPE_Eを参照
  final int boardType;
}

class UserLogProductInfo {
  const UserLogProductInfo({
    required this.modeCode,
    required this.modelSerial,
    required this.backupState,
    required this.panelMajorVer,
    required this.panelMinorVer,
    required this.panelPatchVer,
    required this.libMajorVer,
    required this.libMinorVer,
    required this.libPatchVer,
    required this.mainVersion,
    required this.bootVersion,
    required this.masterVersion,
    required this.fpgaVersion,
    required this.xyCpuVersion,
    required this.boardType,
    required this.embSpeed,
    required this.embServiceStitchCnt,
    required this.embTotalStitchCnt,
    required this.utlServiceStitchCnt,
    required this.utlTotalStitchCnt,
    required this.serviceStitchCnt,
    required this.totalStitchCnt,
    required this.totalThreadCnt,
    required this.errorListCode,
    required this.errorListType,
  });

  final String modeCode;

  final String modelSerial;

  /// 本体状態のバックアップ
  final int backupState;

  /// パネル プログラム Ver.
  final int panelMajorVer;

  /// パネル プログラム Ver.
  final int panelMinorVer;

  /// パネル プログラム Ver.
  final int panelPatchVer;

  /// 編集ライブラリ　プログラム Ver.
  final int libMajorVer;

  /// 編集ライブラリ　プログラム Ver.
  final int libMinorVer;

  /// 編集ライブラリ　プログラム Ver.
  final int libPatchVer;

  /// メイン プログラム Ver.
  final int mainVersion;

  /// BOOT プログラム Ver.
  final int bootVersion;

  /// マスター Ver.
  final int masterVersion;

  /// FPGA Ver.
  final int fpgaVersion;

  /// XY_CPU Ver.
  final int xyCpuVersion;

  final String boardType;

  /// !< 刺繍最高回転数（spm）
  final int embSpeed;

  /// !< 刺繍縫製でのサービスステッチ数（針）
  final int embServiceStitchCnt;

  /// !< 刺繍縫製でのステッチ数（針）
  final int embTotalStitchCnt;

  /// !< 実用縫製でのサービスステッチ数（針）
  final int utlServiceStitchCnt;

  /// !< 実用縫製でのステッチ数（針）
  final int utlTotalStitchCnt;

  /// !< サービスステッチ数の合計（針）
  final int serviceStitchCnt;

  /// !< ステッチ数の合計（針）
  final int totalStitchCnt;

  /// !< 糸通し実施回数（回）
  final int totalThreadCnt;

  ///発生したエラーコード
  final List<int> errorListCode;

  ///発生したエラーの種類
  final List<int> errorListType;

  static UserLogProductInfo fromPointer(
      Pointer<dev_gen.UserLogProductInfo_t> pointer) {
    /// modeCode @ffi.Array.multi([10])
    /// ArrayからStringに変換
    final modeCodeArray = pointer.ref.modeCode;
    String modeCodeStr = "";
    for (int i = 0; i < 10; i++) {
      var currentChar = modeCodeArray[i].toString();
      modeCodeStr += currentChar;
    }

    /// modelSerial  @ffi.Array.multi([12])
    /// ArrayからStringに変換
    final modelSerialArray = pointer.ref.modelSerial;
    String modelSerialStr = "";
    for (int i = 0; i < 12; i++) {
      var currentChar = modelSerialArray[i].toString();
      modelSerialStr += currentChar;
    }

    /// boardType @ffi.Array.multi([31])
    /// ArrayからStringに変換
    final boardTypeArray = pointer.ref.boardType;
    String boardTypeStr = "";
    for (int i = 0; i < 31; i++) {
      var currentChar = boardTypeArray[i].toString();
      boardTypeStr += currentChar;
    }

    /// errorListCodeとerrorListType： @ffi.Array.multi([30])
    /// ArrayからListに変換
    final devErrorListCode = pointer.ref.errorListCode;
    final devErrorListType = pointer.ref.errorListCode;
    List<int> errorListCode = [];
    List<int> errorListType = [];

    for (int i = 0; i < 30; i++) {
      errorListCode.add(devErrorListCode[i]);
      errorListType.add(devErrorListType[i]);
    }

    final productInfo = UserLogProductInfo(
        modeCode: modeCodeStr,
        modelSerial: modelSerialStr,
        backupState: pointer.ref.backupState,
        panelMajorVer: pointer.ref.panelMajorVer,
        panelMinorVer: pointer.ref.panelMinorVer,
        panelPatchVer: pointer.ref.panelPatchVer,
        libMajorVer: pointer.ref.libMajorVer,
        libMinorVer: pointer.ref.libMinorVer,
        libPatchVer: pointer.ref.libPatchVer,
        mainVersion: pointer.ref.main_version,
        bootVersion: pointer.ref.boot_version,
        masterVersion: pointer.ref.master_version,
        fpgaVersion: pointer.ref.fpga_version,
        xyCpuVersion: pointer.ref.xy_cpu_version,
        boardType: boardTypeStr,
        embSpeed: pointer.ref.embSpeed,
        embServiceStitchCnt: pointer.ref.embServiceStitchCnt,
        embTotalStitchCnt: pointer.ref.embTotalStitchCnt,
        utlServiceStitchCnt: pointer.ref.utlServiceStitchCnt,
        utlTotalStitchCnt: pointer.ref.utlTotalStitchCnt,
        serviceStitchCnt: pointer.ref.serviceStitchCnt,
        totalStitchCnt: pointer.ref.totalStitchCnt,
        totalThreadCnt: pointer.ref.totalThreadCnt,
        errorListCode: errorListCode,
        errorListType: errorListType);

    return productInfo;
  }

  @override
  String toString() => """
    modeCode:$modeCode,
    modelSerial:$modelSerial,
    modelSerial:$modelSerial,
    modelSerial:$modelSerial,
    panelMinorVer:$panelMinorVer,
    panelPatchVer:$panelPatchVer,
    libMajorVer:$libMajorVer,
    libMinorVer:$libMinorVer,
    libPatchVer:$libPatchVer,
   mainVersion :$mainVersion,
    bootVersion:$bootVersion,
    masterVersion:$masterVersion,
    fpgaVersion:$fpgaVersion,
    xyCpuVersion:$xyCpuVersion,
    boardType:$boardType,
    embSpeed:$embSpeed,
    embServiceStitchCnt:$embServiceStitchCnt,
    embTotalStitchCnt:$embTotalStitchCnt,
    utlServiceStitchCnt:$utlServiceStitchCnt,
    utlTotalStitchCnt:$utlTotalStitchCnt,
    serviceStitchCnt:$serviceStitchCnt,
    totalStitchCnt:$totalStitchCnt,
    totalThreadCnt:$totalThreadCnt,
    errorListCode:$errorListCode,
    errorListType:$errorListType,
""";
}

/// エラーリスト
class ErrorListInfo {
  const ErrorListInfo({
    required this.errorCode,
    required this.errorType,
  });

  /// エラーコード
  final int errorCode;

  /// エラーの種類(モータとフェイルセーフのみ)
  final int errorType;
}

/// 機器状態
class ModelStatusInfo {
  const ModelStatusInfo({
    required this.embServiceStitchCount,
    required this.embTotalStitchCount,
    required this.utlServiceStitchCount,
    required this.utlTotalStitchCount,
    required this.serviceStitchCount,
    required this.totalStitchCount,
    required this.totalThreadCount,
  });

  /// 刺繍サービスステッチカウンタ
  final int embServiceStitchCount;

  /// 刺繍トータルステッチカウンタ
  final int embTotalStitchCount;

  /// 実用サービスステッチカウンタ
  final int utlServiceStitchCount;

  /// 実用トータルステッチカウンタ
  final int utlTotalStitchCount;

  /// サービスステッチカウンタ
  final int serviceStitchCount;

  /// トータルステッチカウンタ
  final int totalStitchCount;

  /// 糸通し回数
  final int totalThreadCount;
}

///
///サムネイルのサーズの種類
///
enum ThumbnailSizeType {
  S,
  M,
  L;

  ///
  /// libAPI [DeviceApiFunction.getThumbnailSize]機能API戻る値を転換します
  ///
  @internal
  static ThumbnailSizeType formFFIApiIndex(int value) {
    if (value < ThumbnailSizeType.S.index ||
        value >= ThumbnailSizeType.values.length) {
      DeviceLibraryLog.e(
          "invalid ThumbnailSizeType($value), change to default");
      return ThumbnailSizeType.M;
    } else {
      return ThumbnailSizeType.values[value];
    }
  }
}

///
/// 設定のプロジェクターの色
///
enum DeviceProjectorColor {
  /// プロジェクター色01
  color_01(Color.fromARGB(255, 230, 0, 18)),

  /// プロジェクター色02
  color_02(Color.fromARGB(255, 250, 246, 0)),

  /// プロジェクター色03
  color_03(Color.fromARGB(255, 0, 255, 0)),

  /// プロジェクター色04
  color_04(Color.fromARGB(255, 0, 0, 255)),

  /// プロジェクター色05
  color_05(Color.fromARGB(255, 255, 153, 0)),

  /// プロジェクター色06
  color_06(Color.fromARGB(255, 255, 3, 201)),

  /// プロジェクター色07
  color_07(Color.fromARGB(255, 102, 255, 255)),

  /// プロジェクター色08
  color_08(Color.fromARGB(255, 255, 255, 255)),

  /// プロジェクター色09
  color_09(Color.fromARGB(255, 255, 209, 209)),

  /// プロジェクター色10
  color_10(Color.fromARGB(255, 250, 255, 153)),

  /// プロジェクター色11
  color_11(Color.fromARGB(255, 203, 242, 102)),

  /// プロジェクター色12
  color_12(Color.fromARGB(255, 180, 235, 250)),

  /// プロジェクター色13
  color_13(Color.fromARGB(255, 237, 197, 143)),

  /// プロジェクター色14
  color_14(Color.fromARGB(255, 204, 156, 194)),

  /// プロジェクター色15
  color_15(Color.fromARGB(255, 200, 200, 203)),

  /// プロジェクター色16
  color_16(Color.fromARGB(255, 0, 0, 0)),

  /// プロジェクター色デフォルト
  colorDefault(Color.fromARGB(255, 0, 0, 0));

  const DeviceProjectorColor(
    this.color,
  );

  final Color color;

  ///lib 内の色の順序
  static const List<DeviceProjectorColor> _libColorIndexList = [
    DeviceProjectorColor.color_16,
    DeviceProjectorColor.color_08,
    DeviceProjectorColor.color_15,
    DeviceProjectorColor.color_07,
    DeviceProjectorColor.color_14,
    DeviceProjectorColor.color_06,
    DeviceProjectorColor.color_13,
    DeviceProjectorColor.color_05,
    DeviceProjectorColor.color_12,
    DeviceProjectorColor.color_04,
    DeviceProjectorColor.color_11,
    DeviceProjectorColor.color_03,
    DeviceProjectorColor.color_10,
    DeviceProjectorColor.color_02,
    DeviceProjectorColor.color_09,
    DeviceProjectorColor.color_01,
    DeviceProjectorColor.colorDefault,
  ];

  @internal
  static DeviceProjectorColor fromFfiApiIntValue(int value) {
    if (value < 0 || value > _libColorIndexList.length) {
      DeviceLibraryLog.e("invalid value, use colorDefault instead");
      return DeviceProjectorColor.colorDefault;
    }
    DeviceLibraryLog.d("value:$value");
    return _libColorIndexList[value];
  }

  @internal
  static int toFfiApiIntValue(DeviceProjectorColor color) {
    DeviceLibraryLog.d("DeviceProjectorColor :$color");
    final intValue = _libColorIndexList.indexOf(color);
    DeviceLibraryLog.d("DeviceProjectorColor change to :$intValue");
    return intValue;
  }
}

///
/// UGK識別定義
///
enum UgkType {
  UpgKITNON,
  UpgKIT1,
  UpgKIT2,
  UpgKIT3,
  UpgKIT4,
  UpgKITSNC,
  UpgKITMAX,
}

///
/// この値は、書き込みプロセス中に発生したエラーを識別するために使用できます。
///
enum UpgError {
  /// エラー: なし
  UpgErrorNone,

  /// エラー: no USB medium
  UpgErrorMedium,

  /// エラー: read file error
  UpgErrorRead,

  /// エラー: find file error
  UpgErrorFind,

  /// エラー: check sum error
  UpgErrorSum,

  /// エラー: save file error`
  UpgErrorSave,

  /// エラー: address error
  UpgErrorAddr,

  /// エラー: under version
  UpgErrorUnderVersion
}

///
/// アプリケーション状態
///
enum UpgradeState {
  upgradeStateInitIalizing,

  /// Upgradeファイルのドライブ選択状態
  upgradeStateSelecting,

  /// 更新開始待ち
  upgradeStateWaiting,

  /// 更新準備中
  upgradeStatePreparing,

  /// 更新実行中
  upgradeStateExecuting,

  /// 更新完了
  upgradeStateSucceeded
}

///
/// 最新情報を入手する
///
class UpgradeControl {
  UpgradeControl(
      {required this.upgError,
      required this.upgErrorKind,
      required this.upgVersionLow,
      required this.upgradeState});
  final UpgError upgError;

  final int upgErrorKind;

  final double upgVersionLow;

  final UpgradeState upgradeState;
}

///
/// ミシン本体の状態
/// MyStitchMonitor App接続の場合
/// "/status" , "/embstatus"用
///
class MachineStatus {
  /// ミシン本体の状態:Initial前
  static const int beforeInitial = 0;

  /// ミシン本体の状態:エラーなし停止中 = 緑)
  static const int noErrStopGreen = 10;

  /// ミシン本体の状態:エラーなし停止中 = 赤)
  static const int noErrStopRed = 11;

  /// ミシン本体の状態:実用縫製中
  static const int utlSewing = 20;

  /// ミシン本体の状態:刺繍縫製中
  static const int embSewing = 30;

  /// ミシン本体の状態:刺繍の糸替え待ち
  static const int embChangeThread = 40;

  /// ミシン本体の状態:縫製完了
  static const int sewingComplete = 50;

  /// ミシン本体の状態:上糸エラー
  static const int upperThreadErr = 60;

  /// ミシン本体の状態:下糸エラー
  static const int lowerThreadErr = 61;

  /// ミシン本体の状態:ワイパーエラー
  static const int wiperErr = 62;

  /// ミシン本体の状態:ミシンで針数更新できない状態 = 設定画面時など)
  static const int nonUpdate = 98;

  /// ミシン本体の状態:そのほかのerror停止中
  static const int otherErr = 99;
}

const int inValidIntParam = -1;

enum ScreenBrightnessLevel {
  /* 消灯*/
  /// Ui は使用されず、 ui のライティングに影響するので、まずブロックしてください。
  /// defaultValue = LEVEL4
  OFF,

  LEVEL1,
  LEVEL2,
  LEVEL3,
  LEVEL4,
  LEVEL5,
  LEVEL6,
  ;

  ///
  /// libAPI [DeviceApiFunction.screenDisplayBrightness]機能API戻る値を転換します
  ///
  @internal
  static ScreenBrightnessLevel formFFIApiIndex(int levelIndex) {
    levelIndex = levelIndex;
    if (levelIndex < ScreenBrightnessLevel.OFF.index ||
        levelIndex >= ScreenBrightnessLevel.values.length) {
      DeviceLibraryLog.e("invalid levelIndex($levelIndex), change to default");
      return ScreenBrightnessLevel.LEVEL4;
    } else {
      return ScreenBrightnessLevel.values[levelIndex];
    }
  }

  ///
  /// libAPI [DeviceApiFunction.screenDisplayBrightness]機能API戻る値を転換します
  ///
  @internal
  static int toFFIApiIndex(ScreenBrightnessLevel level) => level.index;
}

///
/// スクリーンセーバーの種類
///
enum ScreenSaverType {
  defaultType,
  customType,
}

///
/// Wlan設定状態
///
enum WlanGuideSettingStatusEnum {
  /// 未初期化(初回起動)
  unInit,

  /// 初期化完了
  initFinish;

  ///
  /// libAPI [DeviceApiFunction.getWLanGuideSettingStatus]機能API戻る値を転換します
  ///
  @internal
  static WlanGuideSettingStatusEnum formFFIApiIndex(int value) {
    if (value < WlanGuideSettingStatusEnum.unInit.index ||
        value >= WlanGuideSettingStatusEnum.values.length) {
      DeviceLibraryLog.e("invalid levelIndex($value), change to default");
      return WlanGuideSettingStatusEnum.unInit;
    } else {
      return WlanGuideSettingStatusEnum.values[value];
    }
  }
}

///
/// EULA表示状態
///
enum EULAInitStatusEnum {
  /// 未初期化(初回起動)
  unInit,

  /// 初期化完了
  initFinish;

  ///
  /// libAPI [DeviceApiFunction.getEulaInitStatus]機能API戻る値を転換します
  ///
  @internal
  static EULAInitStatusEnum formFFIApiIndex(int value) {
    if (value < EULAInitStatusEnum.unInit.index ||
        value >= EULAInitStatusEnum.values.length) {
      DeviceLibraryLog.e("invalid levelIndex($value), change to default");
      return EULAInitStatusEnum.unInit;
    } else {
      return EULAInitStatusEnum.values[value];
    }
  }
}

///
/// RTCの読み取り状態
///
enum RTCReadStatus {
  /// RTC読み込み成功 (0)
  rtcReadSuccess,

  /// RTC読み込み失敗 (1)
  rtcReadError;

  ///
  /// libAPI [getRTCReadErrorStatus]戻る値を転換します
  ///
  static RTCReadStatus formFFIApiIndex(int index) {
    if (index < RTCReadStatus.rtcReadSuccess.index ||
        index >= RTCReadStatus.values.length) {
      DeviceLibraryLog.e("invalid levelIndex($index), change to default");
      return RTCReadStatus.rtcReadSuccess;
    } else {
      return RTCReadStatus.values[index];
    }
  }
}

enum MouseCursorType {
  mouseType1,
  mouseType2,
  mouseType3,
}

int get settingColorsTableMax => settingColorsTable.length;
const List<Color> settingColorsTable = [
  Color.fromARGB(255, 255, 255, 255),
  Color.fromARGB(255, 255, 255, 201),
  Color.fromARGB(255, 255, 248, 155),
  Color.fromARGB(255, 240, 255, 156),
  Color.fromARGB(255, 212, 255, 212),
  Color.fromARGB(255, 206, 248, 255),
  Color.fromARGB(255, 209, 232, 255),
  Color.fromARGB(255, 233, 217, 255),
  Color.fromARGB(255, 247, 207, 255),
  Color.fromARGB(255, 255, 209, 236),
  Color.fromARGB(255, 255, 219, 219),
  Color.fromARGB(255, 204, 204, 204),
  Color.fromARGB(255, 255, 191, 143),
  Color.fromARGB(255, 255, 245, 100),
  Color.fromARGB(255, 216, 255, 0),
  Color.fromARGB(255, 161, 255, 161),
  Color.fromARGB(255, 152, 239, 255),
  Color.fromARGB(255, 163, 209, 255),
  Color.fromARGB(255, 210, 178, 255),
  Color.fromARGB(255, 241, 168, 255),
  Color.fromARGB(255, 255, 168, 219),
  Color.fromARGB(255, 255, 160, 160),
  Color.fromARGB(255, 153, 153, 153),
  Color.fromARGB(255, 255, 158, 83),
  Color.fromARGB(255, 244, 227, 0),
  Color.fromARGB(255, 197, 235, 0),
  Color.fromARGB(255, 89, 240, 89),
  Color.fromARGB(255, 14, 209, 244),
  Color.fromARGB(255, 97, 176, 255),
  Color.fromARGB(255, 182, 130, 255),
  Color.fromARGB(255, 230, 107, 255),
  Color.fromARGB(255, 255, 102, 186),
  Color.fromARGB(255, 255, 88, 88),
  Color.fromARGB(255, 102, 102, 102),
  Color.fromARGB(255, 252, 114, 0),
  Color.fromARGB(255, 212, 196, 0),
  Color.fromARGB(255, 150, 198, 0),
  Color.fromARGB(255, 20, 200, 20),
  Color.fromARGB(255, 0, 161, 192),
  Color.fromARGB(255, 13, 131, 249),
  Color.fromARGB(255, 129, 61, 255),
  Color.fromARGB(255, 189, 0, 227),
  Color.fromARGB(255, 237, 26, 156),
  Color.fromARGB(255, 244, 26, 26),
  Color.fromARGB(255, 51, 51, 51),
  Color.fromARGB(255, 159, 69, 0),
  Color.fromARGB(255, 142, 131, 0),
  Color.fromARGB(255, 113, 144, 0),
  Color.fromARGB(255, 8, 127, 8),
  Color.fromARGB(255, 0, 105, 123),
  Color.fromARGB(255, 0, 77, 153),
  Color.fromARGB(255, 74, 0, 179),
  Color.fromARGB(255, 123, 0, 148),
  Color.fromARGB(255, 167, 0, 84),
  Color.fromARGB(255, 191, 10, 10),
  Color.fromARGB(255, 0, 0, 0),
  Color.fromARGB(255, 88, 39, 0),
  Color.fromARGB(255, 79, 72, 0),
  Color.fromARGB(255, 63, 79, 0),
  Color.fromARGB(255, 0, 71, 0),
  Color.fromARGB(255, 0, 72, 89),
  Color.fromARGB(255, 0, 48, 95),
  Color.fromARGB(255, 47, 7, 102),
  Color.fromARGB(255, 68, 0, 82),
  Color.fromARGB(255, 81, 0, 40),
  Color.fromARGB(255, 94, 0, 0),
];
