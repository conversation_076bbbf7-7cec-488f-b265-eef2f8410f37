import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../../../model/machine_config_model.dart';
import '../../../../../../../../../../model/preview_area_size_model.dart';
import '../../../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../../../../model/applique_model.dart';
import '../../../../../../../model/edit_model.dart';
import '../../../../../../../model/pattern_model.dart';
import '../../../../../../../model/preview_model.dart';
import '../../../../../../../model/redo_undo_model.dart';
import '../../../../pattern_edit_view_model.dart';
import 'applique_outline_interface.dart';

typedef PatternViewDisplayInfo = PatternDisplayInfo;
typedef EmbBorderViewDisplayInfo = EmbBorderDisplayInfo;
typedef EmbGroupViewDisplayInfo = EmbGroupDisplayInfo;
typedef EmbPatternViewDisplayInfo = EmbPatternDisplayInfo;

/// 間隔変化の量
const int _distanceStep = 5;

/// 最大間隔値
const int _distanceValueMax = 100;

/// 最小間隔値
const int _distanceValueMin = 0;

///
/// ステップ量
///
const _stepValue = 10;

final appliqueOutlineViewModelProvider = StateNotifierProvider.autoDispose<
    AppliqueOutlineViewModelInterface, AppliqueOutlineState>(
  (ref) => AppliqueOutlineViewModel(ref),
);

class AppliqueOutlineViewModel extends AppliqueOutlineViewModelInterface {
  AppliqueOutlineViewModel(Ref ref)
      : super(
            const AppliqueOutlineState(
              isUnitMm: true,
              isArtspiraValid: false,
              distanceTextStyle: false,
              distanceValue: "",
              patternDisplayInfoList: [],
            ),
            ref) {
    /// View更新
    update();
  }

  /// 間隔インプット値
  int _distanceInputValue = AppliqueModel().getAppliqueDistanceValue() ??
      AppliqueModel().outLineDistanceDefaultValue;

  @override
  void update() {
    state = state.copyWith(
      isArtspiraValid: MachineConfigModel().isArtspiraValid,
      patternDisplayInfoList: _getPatternDisplayInfoList(),
      isUnitMm: PatternModel().isUnitMm,
      distanceValue: _getDistanceDisplayValue(),
      distanceTextStyle:
          _distanceInputValue == AppliqueModel().outLineDistanceDefaultValue
              ? true
              : false,
    );
  }

  @override
  void onMinusButtonClick() {
    if (_distanceInputValue == _distanceValueMin) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    if ((_distanceInputValue - _distanceStep) < _distanceValueMin) {
      _distanceInputValue = _distanceValueMin;
    } else {
      _distanceInputValue -= _distanceStep;
    }

    /// Lib更新
    AppliqueModel().setDistanceApplique(_distanceInputValue);

    /// View更新
    update();
  }

  @override
  void onPlusButtonClick() {
    if (_distanceInputValue == _distanceValueMax) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    if ((_distanceInputValue + _distanceStep) > _distanceValueMax) {
      _distanceInputValue = _distanceValueMax;
    } else {
      _distanceInputValue += _distanceStep;
    }

    /// Lib更新
    AppliqueModel().setDistanceApplique(_distanceInputValue);

    /// View更新
    update();
  }

  @override
  void onOKButtonClick(BuildContext context) {
    AppliqueModel().completeSelectedApplique();
    EmbLibrary().apiBinding.finishApplique();
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }

    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);

    /// view更新
    PopupNavigator.popUntil(context: context, nextRouteName: PopupEnum.toolBar);
  }

  @override
  void onReturnButtonClick(BuildContext context) {
    /// Model更新
    PatternModel().clearTemporaryPatternList();
    EmbLibrary().apiBinding.cancelApplique();
    PopupNavigator.maybePop(context: context);
  }

  ///
  /// Distanceの表示値を取得する
  ///
  String _getDistanceDisplayValue() {
    double distanceValue = _distanceInputValue / _stepValue;

    if (PatternModel().isUnitMm) {
      return distanceValue.toStringAsFixed(1);
    }
    return EditModel.getDisplayInchShowValue(distanceValue);
  }

  ///
  /// 選択パターンの表示情報の取得
  ///
  List<PatternDisplayInfo> _getPatternDisplayInfoList() {
    List<PatternDisplayInfo> patternDisplayInfoList = [];

    /// プレビュー中心点
    final Offset originalCenter = embPreviewSizeDot / 2;

    /// 1mmに対応する画素数
    final double pixelOfOneMm = embPreviewSizeDot.dx / frame297x465MmSize.dx;

    /// カレントグループのハンドル
    final currentGroupHandle = PatternModel().getCurrentGroupHandle();

    final Pattern currentPattern = AppliqueModel().getEditPattern.editPattern;

    /// 選択パターンの表示情報の取得
    if (currentPattern is EmbGroup) {
      patternDisplayInfoList.add(PreviewModel().getGroupPatternDisplayInfo(
        scrollType: ScrollCenterType.IMAGE_EDITING,
        group: currentPattern,
        centerPoint: originalCenter,
        pixelOfOneMm: pixelOfOneMm,
        sewingIndex: 0,
        currentGroupHandle: currentGroupHandle,
        zoomScale: zoomList.first,
      ));
    } else if (currentPattern is EmbBorder) {
      patternDisplayInfoList.add(PreviewModel().getBorderPatternDisplayInfo(
        scrollType: ScrollCenterType.IMAGE_EDITING,
        border: currentPattern,
        centerPoint: originalCenter,
        pixelOfOneMm: pixelOfOneMm,
        sewingIndex: 0,
        currentGroupHandle: currentGroupHandle,
        zoomScale: zoomList.first,
      ));
    } else {
      /// Do noting
    }

    /// 追加された新規追加の模様の表示情報
    if (PatternModel().temporaryGroupList.firstOrNull != null) {
      patternDisplayInfoList.add(PreviewModel().getGroupPatternDisplayInfo(
        scrollType: ScrollCenterType.IMAGE_EDITING,
        group: PatternModel().temporaryGroupList.first,
        centerPoint: originalCenter,
        pixelOfOneMm: pixelOfOneMm,
        sewingIndex: 0,
        currentGroupHandle: currentGroupHandle,
        zoomScale: zoomList.first,
      ));
    } else {
      /// Do noting
    }

    return patternDisplayInfoList;
  }
}
