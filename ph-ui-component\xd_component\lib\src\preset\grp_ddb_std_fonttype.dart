// ignore_for_file: camel_case_types

import 'package:flutter/material.dart';

import 'bk_btnbase.dart';

class grp_ddb_std_fonttype extends StatelessWidget {
  const grp_ddb_std_fonttype({
    super.key,
    this.text,
    this.image,
  });

  final String? text;
  final Widget? image;
  @override
  Widget build(BuildContext context) => Stack(
        fit: StackFit.expand,
        children: [
          const bk_btnbase(),
          text == null
              ? Row(
                  children: [
                    const Spacer(flex: 30),
                    Expanded(
                      flex: 247,
                      child: Column(
                        children: [
                          const Spacer(flex: 16),
                          Expanded(
                            flex: 38,
                            child: Center(child: image),
                          ),
                          const Spacer(flex: 16),
                        ],
                      ),
                    ),
                    const Spacer(flex: 20),
                  ],
                )
              : Row(
                  children: [
                    const Spacer(flex: 20),
                    Expanded(
                      flex: 51,
                      child: Column(
                        children: [
                          const Spacer(flex: 15),
                          Expanded(
                            flex: 39,
                            child: Text(
                              text ?? '',
                              style: const TextStyle(
                                fontSize: 28,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          const Spacer(flex: 16)
                        ],
                      ),
                    ),
                    const Spacer(flex: 44),
                    Expanded(
                      flex: 110,
                      child: Column(
                        children: [
                          const Spacer(flex: 15),
                          Expanded(
                            flex: 40,
                            child: image ?? Container(),
                          ),
                          const Spacer(flex: 15),
                        ],
                      ),
                    ),
                    const Spacer(flex: 72),
                  ],
                ),
        ],
      );
}
