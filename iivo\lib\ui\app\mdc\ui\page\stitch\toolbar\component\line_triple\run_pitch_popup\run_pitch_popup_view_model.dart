import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../model/device_info_model.dart';
import '../../../../../../../model/resume_history_model.dart';
import '../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../model/stitch/line_triple_model.dart';
import '../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../stitch_page_view_model.dart';
import 'run_pitch_popup_view_interface.dart';

///
/// 単位
///
typedef Unit = DisplayUnit;

///
/// 白い背景に黒いテキスト
///
const TextStyle blackTextWhiteBackground =
    TextStyle(color: Colors.black, backgroundColor: Colors.white);

///
/// 黒い背景に白いテキスト
///
const TextStyle whiteTextBlackBackground =
    TextStyle(color: Colors.white, backgroundColor: Colors.black);

final runPitchPopupViewModelProvider = StateNotifierProvider.autoDispose<
    RunPitchPopupViewModel,
    RunPitchPopupState>((ref) => RunPitchPopupViewModel(ref));

class RunPitchPopupViewModel extends ViewModel<RunPitchPopupState> {
  RunPitchPopupViewModel(this.ref)
      : super(const RunPitchPopupState(
          runPitchDisplayValue: "",
          isDefaultValue: false,
          isUnitMm: true,
          isMinusButtonValid: true,
          isPlusButtonValid: true,
        )) {
    update();
  }

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// 長押しすべステップ量
  ///
  static const int _stepValueLongPress = 10;

  ///
  /// ステップ量
  ///
  static int _stepValue = 1;

  ///
  /// ピッチ値ディスプレイスター
  ///
  bool _isRunPitchValueDisplayStar =
      LineTripleModel().getRunPitch() != LineTripleModel.pitchNotUpdating
          ? false
          : true;

  ///
  /// ピッチ値
  ///
  int _runPitchValue = LineTripleModel().getRunPitch();

  ///
  /// View更新
  ///
  @override
  void update() {
    state = state.copyWith(
      isUnitMm: DeviceLibrary().apiBinding.getDisplayUnit().displayUnit ==
              DisplayUnit.mm
          ? true
          : false,
      runPitchDisplayValue: _getRunPitchDisplayValue(),
      isDefaultValue: _getRunPitchDefaultState(),
      isMinusButtonValid: _getMinusButtonState(),
      isPlusButtonValid: _getPlusButtonState(),
    );
  }

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isRunPitchValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isRunPitchValueDisplayStar = false;

      ///  Model 更新
      _runPitchValue = LineTripleModel().runPitchDefaultValue;

      /// View更新
      update();

      return false;
    }
    isLongPress == false ? _stepValue = 1 : _stepValue = _stepValueLongPress;
    if (_runPitchValue == LineTripleModel().runPitchValueMin) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress == true) {
      _runPitchValue -= _runPitchValue % LineTripleModel.conversionRate;
    }

    if (_runPitchValue > LineTripleModel().runPitchValueMin) {
      ///  Model 更新
      _runPitchValue -= _stepValue;
    }

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress) {
    if (_isRunPitchValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isRunPitchValueDisplayStar = false;

      ///  Model 更新
      _runPitchValue = LineTripleModel().runPitchDefaultValue;

      /// View更新
      update();

      return false;
    }
    isLongPress == false ? _stepValue = 1 : _stepValue = _stepValueLongPress;
    if (_runPitchValue == LineTripleModel().runPitchValueMax) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    if (isLongPress == true) {
      _runPitchValue -= _runPitchValue % LineTripleModel.conversionRate;
    }

    ///  Model 更新
    _runPitchValue += _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineTripleRunPitch.toString());
    if (_isRunPitchValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int runPitchValue = LineTripleModel().getRunPitch();

    /// Model 更新
    LineTripleModel().setRunPitch(_runPitchValue);
    if (LineTripleModel().setMdcTripleStitchSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (runPitchValue != _runPitchValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// ランピッチの表示値を取得する
  ///
  String _getRunPitchDisplayValue() {
    double runPitchValue = _runPitchValue / LineTripleModel.conversionRate;

    if (_isRunPitchValueDisplayStar) {
      if (DeviceInfoModel().displayUnitType == DisplayUnit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }
    if (DeviceInfoModel().displayUnitType == DisplayUnit.mm) {
      return runPitchValue.toStringAsFixed(1);
    } else {
      return ToolbarModel.getDisplayInchShowValue(runPitchValue);
    }
  }

  ///
  /// ランピッチのデフォルト値状態を取得します
  ///
  bool _getRunPitchDefaultState() {
    if (_isRunPitchValueDisplayStar) {
      return true;
    }

    if (_runPitchValue == LineTripleModel().runPitchDefaultValue) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isRunPitchValueDisplayStar) {
      return true;
    }

    if (_runPitchValue <= LineTripleModel().runPitchValueMin) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isRunPitchValueDisplayStar) {
      return true;
    }

    if (_runPitchValue >= LineTripleModel().runPitchValueMax) {
      return false;
    }
    return true;
  }
}
