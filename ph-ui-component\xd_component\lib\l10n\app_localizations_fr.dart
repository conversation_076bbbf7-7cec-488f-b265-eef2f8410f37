import 'app_localizations.dart';

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get color_001 => 'ROSE';

  @override
  String get color_002 => 'VIEUX ROSE';

  @override
  String get color_003 => 'ROSE PÉTALE';

  @override
  String get color_004 => 'ROSE CLAIR';

  @override
  String get color_005 => 'CORAIL CLAIR';

  @override
  String get color_006 => 'POT POURRI';

  @override
  String get color_007 => 'BRUME DE\nBRUYÈRE';

  @override
  String get color_008 => 'CHAMPAGNE';

  @override
  String get color_009 => 'MAUVE FONCÉ';

  @override
  String get color_010 => 'BRUYÈRE';

  @override
  String get color_011 => 'ROSE\nRÉCONFORT';

  @override
  String get color_012 => 'ROSE\nMONTAGNE';

  @override
  String get color_013 => 'ROSE CERISE';

  @override
  String get color_014 => 'CHAIR';

  @override
  String get color_015 => 'SAUMON';

  @override
  String get color_016 => 'ROSE\nCREVETTE';

  @override
  String get color_017 => 'CORAIL FONCÉ';

  @override
  String get color_018 => 'ROUGE\nORANGE';

  @override
  String get color_019 => 'BORDEAUX';

  @override
  String get color_020 => 'LIE DE VIN';

  @override
  String get color_021 => 'ROUX';

  @override
  String get color_022 => 'PRUNE';

  @override
  String get color_023 => 'MARRON';

  @override
  String get color_024 => 'ROUGE ROYAL';

  @override
  String get color_025 => 'ROSE INDIEN';

  @override
  String get color_026 => 'RUBIS';

  @override
  String get color_027 => 'FUCHSIA\nFONCÉ';

  @override
  String get color_028 => 'CARMIN';

  @override
  String get color_029 => 'ROSE FONCÉ';

  @override
  String get color_030 => 'BÉGONIA';

  @override
  String get color_031 => 'AZALÉE';

  @override
  String get color_032 => 'ROUGE RUBIS';

  @override
  String get color_033 => 'FRAISE';

  @override
  String get color_034 => 'ROUGE LUCIFER';

  @override
  String get color_035 => 'ROUGE POMME\nD\'AMOUR';

  @override
  String get color_036 => 'ROSE\nTRÉMIÈRE';

  @override
  String get color_037 => 'ROUGE GRILL';

  @override
  String get color_038 => 'FEU SAUVAGE';

  @override
  String get color_039 => 'ROUGE';

  @override
  String get color_040 => 'ROUGE\nJOCKEY';

  @override
  String get color_041 => 'ROUGE\nRADIANT';

  @override
  String get color_042 => 'BAIE ROUGE';

  @override
  String get color_043 => 'ROUGE RENARD';

  @override
  String get color_044 => 'ROUGE À\nLÈVRES';

  @override
  String get color_045 => 'ROUGE NOËL';

  @override
  String get color_046 => 'ÉCARLATE';

  @override
  String get color_047 => 'ÉCARLATE\nSOMBRE';

  @override
  String get color_048 => 'ROUGE AIRELLE';

  @override
  String get color_049 => 'ROSE CHAIR';

  @override
  String get color_050 => 'BISQUE';

  @override
  String get color_051 => 'FLAMANT\nROSE';

  @override
  String get color_052 => 'MELON';

  @override
  String get color_053 => 'PÊCHE MELBA';

  @override
  String get color_054 => 'CHÈVREFEUILLE';

  @override
  String get color_055 => 'ORANGE FONCÉ';

  @override
  String get color_056 => 'BLEU LÉGER';

  @override
  String get color_057 => 'BLEU JOYEUX';

  @override
  String get color_058 => 'BLEU GLACIER';

  @override
  String get color_059 => 'BLEU MOYEN';

  @override
  String get color_060 => 'BLEU DE\nROCKPORT';

  @override
  String get color_061 => 'BLEU PASTEL';

  @override
  String get color_062 => 'BLEU LAYETTE';

  @override
  String get color_063 => 'BLEU CIEL';

  @override
  String get color_064 => 'BLEU CLAIR';

  @override
  String get color_065 => 'BLEU LAC';

  @override
  String get color_066 => 'BLEU VIF';

  @override
  String get color_067 => 'BLEU ULTRA';

  @override
  String get color_068 => 'BLEU TROPICAL';

  @override
  String get color_069 => 'BLEU\nCENTAUREE';

  @override
  String get color_070 => 'BLEU LUNE';

  @override
  String get color_071 => 'BLEU SAPHIR';

  @override
  String get color_072 => 'BLEU ARDOISE';

  @override
  String get color_073 => 'BLEU FONCÉ';

  @override
  String get color_074 => 'BLEU NUIT';

  @override
  String get color_075 => 'BLEU\nMERVEILLEUX';

  @override
  String get color_076 => 'BLEU BALTIQUE';

  @override
  String get color_077 => 'BLEU\nCALIFORNIEN';

  @override
  String get color_078 => 'BLEU AZUR';

  @override
  String get color_079 => 'BLEU SOLAIRE';

  @override
  String get color_080 => 'BLEU\nÉLECTRIQUE';

  @override
  String get color_081 => 'BLEU\nPACIFIQUE';

  @override
  String get color_082 => 'BLEU';

  @override
  String get color_083 => 'BLEU IMPÉRIAL';

  @override
  String get color_084 => 'BLEU MARINE';

  @override
  String get color_085 => 'RUBAN BLEU';

  @override
  String get color_086 => 'BLEU MARINE\nCLAIR';

  @override
  String get color_087 => 'BLEU MARINE\nMOYEN';

  @override
  String get color_088 => 'BLEU EMPIRE';

  @override
  String get color_089 => 'BLEU MARINE\nNOCTURNE';

  @override
  String get color_090 => 'BLEU SAPHIR\nFONCÉ';

  @override
  String get color_091 => 'BLEU SUÉDOIS';

  @override
  String get color_092 => 'OUTREMER';

  @override
  String get color_093 => 'BLEU ROI';

  @override
  String get color_094 => 'BLEU DE\nCOBALT';

  @override
  String get color_095 => 'BLEU DE\nPRUSSE';

  @override
  String get color_096 => 'BLEU NASSAU';

  @override
  String get color_097 => 'BLEU VIOLET';

  @override
  String get color_098 => 'BLEU PARISIEN';

  @override
  String get color_099 => 'BLEU LAVANDE';

  @override
  String get color_100 => 'BLEU LAVANDE\nMOYEN';

  @override
  String get color_101 => 'LAVANDE';

  @override
  String get color_102 => 'LAVANDE\nTULIPE';

  @override
  String get color_103 => 'VIOLET\nGLYCINE';

  @override
  String get color_104 => 'POURPRE REINE';

  @override
  String get color_105 => 'LAVANDE\nLIVIDE';

  @override
  String get color_106 => 'POURPRE\nCLAIR';

  @override
  String get color_107 => 'POURPRE\nPROFOND';

  @override
  String get color_108 => 'POURPRE\nSOMBRE';

  @override
  String get color_109 => 'TOUCHE\nPOURPRE';

  @override
  String get color_110 => 'POURPRE ROI';

  @override
  String get color_111 => 'POURPRE\nROYAL';

  @override
  String get color_112 => 'POURPRE\nMOYEN';

  @override
  String get color_113 => 'POURPRE';

  @override
  String get color_114 => 'VIOLET';

  @override
  String get color_115 => 'POURPRE PÂLE';

  @override
  String get color_116 => 'MAGENTA';

  @override
  String get color_117 => 'LILAS CLAIR';

  @override
  String get color_118 => 'LILAS';

  @override
  String get color_119 => 'IRIS DE SIBÉRIE';

  @override
  String get color_120 => 'ROSE\nFANTAISIE';

  @override
  String get color_121 => 'ROSE DE RÊVE';

  @override
  String get color_122 => 'ROSE\nEXCLUSIF';

  @override
  String get color_123 => 'SPLENDEUR\nROSÉE';

  @override
  String get color_124 => 'ROSE\nSAUVAGE';

  @override
  String get color_125 => 'ROUGE AMBRE';

  @override
  String get color_126 => 'GLACE\nFRAMBOISE';

  @override
  String get color_127 => 'AUBERGINE';

  @override
  String get color_128 => 'BLEU BAMBINO';

  @override
  String get color_129 => 'FRANGE BLEUE';

  @override
  String get color_130 => 'EAU MARINE';

  @override
  String get color_131 => 'BLEU GLYCINE';

  @override
  String get color_132 => 'BLEU\nANGÉLIQUE';

  @override
  String get color_133 => 'BLEU COLVERT';

  @override
  String get color_134 => 'BLEU PAON';

  @override
  String get color_135 => 'JADE FONCÉ';

  @override
  String get color_136 => 'VERT\nPARADISIAQUE';

  @override
  String get color_137 => 'BLEU SARCELLE\nÉTERNEL';

  @override
  String get color_138 => 'BLEU DE VÉNUS';

  @override
  String get color_139 => 'BLEU PLUME';

  @override
  String get color_140 => 'MENTHE A\nL\'EAU';

  @override
  String get color_141 => 'VERT DE MER';

  @override
  String get color_142 => 'TURQUOISE';

  @override
  String get color_143 => 'BLEU SARCELLE\nMYSTIQUE';

  @override
  String get color_144 => 'EAU';

  @override
  String get color_145 => 'VERT OCÉAN';

  @override
  String get color_146 => 'TURQUOISE\nTEMPÊTE';

  @override
  String get color_147 => 'VERT MÉDICAL';

  @override
  String get color_148 => 'BLEU SARCELLE\nFONCÉ';

  @override
  String get color_149 => 'VERT D\'EAU';

  @override
  String get color_150 => 'EPINETTE DU\nCOLORADO';

  @override
  String get color_151 => 'BLEU NEWPORT';

  @override
  String get color_152 => 'LAURIER VERT';

  @override
  String get color_153 => 'VERT PALE';

  @override
  String get color_154 => 'VERT KIWI';

  @override
  String get color_155 => 'VERT OLIVE';

  @override
  String get color_156 => 'VERT\nPORTUAIRE';

  @override
  String get color_157 => 'VERT SPÉCIAL';

  @override
  String get color_158 => 'KAKI FONCÉ';

  @override
  String get color_159 => 'VERT LOURD';

  @override
  String get color_160 => 'VERT ALPIN';

  @override
  String get color_161 => 'VERT PRAIRIE';

  @override
  String get color_162 => 'VOILE VERTE';

  @override
  String get color_163 => 'BLEU SARCELLE\nNOCTURNE';

  @override
  String get color_164 => 'BRUME MARINE';

  @override
  String get color_165 => 'VERT SAULE';

  @override
  String get color_166 => 'FEUILLE DE THÉ';

  @override
  String get color_167 => 'VERT DES ÎLES';

  @override
  String get color_168 => 'PIN DES\nFORÊTS';

  @override
  String get color_169 => 'JADE';

  @override
  String get color_170 => 'MENTHE\nPOIVRÉE';

  @override
  String get color_171 => 'VERT\nSARCELLE';

  @override
  String get color_172 => 'VERT FONCÉ';

  @override
  String get color_173 => 'VERT\nCLASSIQUE';

  @override
  String get color_174 => 'VERT PIN\nFONCÉ';

  @override
  String get color_175 => 'VERT';

  @override
  String get color_176 => 'VERT IRLANDE';

  @override
  String get color_177 => 'VERT\nEMERAUDE';

  @override
  String get color_178 => 'TRÈFLE';

  @override
  String get color_179 => 'VERT MOUSSE';

  @override
  String get color_180 => 'VERT IRL.\nCLAIR';

  @override
  String get color_181 => 'VERT\nIRLANDAIS';

  @override
  String get color_182 => 'VERT FEUILLE';

  @override
  String get color_183 => 'VERT LÉGER';

  @override
  String get color_184 => 'CHÊNE VERT';

  @override
  String get color_185 => 'VERT MENTHE';

  @override
  String get color_186 => 'VERT FRAIS';

  @override
  String get color_187 => 'COSSE DE POIS';

  @override
  String get color_188 => 'VERT\nPASTORAL';

  @override
  String get color_189 => 'AVOCAT CLAIR';

  @override
  String get color_190 => 'VERT MOISSON';

  @override
  String get color_191 => 'POUSSIÈRE\nVERTE';

  @override
  String get color_192 => 'VERT TILLEUL';

  @override
  String get color_193 => 'VERT D\'EIRE';

  @override
  String get color_194 => 'VERT\nFEUILLAGE';

  @override
  String get color_195 => 'TOURNESOL';

  @override
  String get color_196 => 'DORÉ';

  @override
  String get color_197 => 'VERT\nAUTOMNAL';

  @override
  String get color_198 => 'OLIVÂTRE';

  @override
  String get color_199 => 'VERT PRAIRIE';

  @override
  String get color_200 => 'VERT CENDRÉ';

  @override
  String get color_201 => 'OLIVE FONCÉ';

  @override
  String get color_202 => 'VERT TREILLIS';

  @override
  String get color_203 => 'OR ROYAL';

  @override
  String get color_204 => 'JAUNE CITRON';

  @override
  String get color_205 => 'JAUNE VIF';

  @override
  String get color_206 => 'JAUNE';

  @override
  String get color_207 => 'OR AMBRÉ';

  @override
  String get color_208 => 'JAUNE\nMANILLE';

  @override
  String get color_209 => 'MORDORÉ';

  @override
  String get color_210 => 'SOLEIL CHAUD';

  @override
  String get color_211 => 'POLLEN DORÉ';

  @override
  String get color_212 => 'LYS DE JOUR';

  @override
  String get color_213 => 'ETOILE DORÉE';

  @override
  String get color_214 => 'SOLEIL DORÉ';

  @override
  String get color_215 => 'CUIVRE';

  @override
  String get color_216 => 'ORANGE';

  @override
  String get color_217 => 'OR FONCÉ';

  @override
  String get color_218 => 'OR BLE';

  @override
  String get color_219 => 'BRUME JAUNE';

  @override
  String get color_220 => 'MOUTARDE';

  @override
  String get color_221 => 'LAITON';

  @override
  String get color_222 => 'ORANGE\nGRILLÉ';

  @override
  String get color_223 => 'ORANGEADE';

  @override
  String get color_224 => 'PAPRIKA';

  @override
  String get color_225 => 'VERMILLON';

  @override
  String get color_226 => 'SAFRAN';

  @override
  String get color_227 => 'AUBURN';

  @override
  String get color_228 => 'TERRE CUITE';

  @override
  String get color_229 => 'ROUILLE FONCÉ';

  @override
  String get color_230 => 'MELON FONCÉ';

  @override
  String get color_231 => 'FAUVE';

  @override
  String get color_232 => 'PÊCHE DOUCE';

  @override
  String get color_233 => 'ROUILLE';

  @override
  String get color_234 => 'ABRICOT\nFONCÉ';

  @override
  String get color_235 => 'TANGERINE';

  @override
  String get color_236 => 'CITROUILLE';

  @override
  String get color_237 => 'SOLEIL LEVANT';

  @override
  String get color_238 => 'EPICE DORÉ';

  @override
  String get color_239 => 'MIEL';

  @override
  String get color_240 => 'AMANDE';

  @override
  String get color_241 => 'BRUN\nROUGEATRE';

  @override
  String get color_242 => 'BRUN ARGILE';

  @override
  String get color_243 => 'BRUN\nROUSSATRE';

  @override
  String get color_244 => 'BRUN CRÈME';

  @override
  String get color_245 => 'JAUNE CRÈME';

  @override
  String get color_246 => 'ARGILE';

  @override
  String get color_247 => 'PISTACHE';

  @override
  String get color_248 => 'BRUN DORÉ';

  @override
  String get color_249 => 'GINGEMBRE';

  @override
  String get color_250 => 'TEMPLE DORÉ';

  @override
  String get color_251 => 'MARRON CLAIR';

  @override
  String get color_252 => 'HÂLÉ';

  @override
  String get color_253 => 'BRUN CANDI';

  @override
  String get color_254 => 'BEIGE';

  @override
  String get color_255 => 'ROTIN';

  @override
  String get color_256 => 'BEIGE FONCÉ';

  @override
  String get color_257 => 'BRONZE';

  @override
  String get color_258 => 'CAFÉ';

  @override
  String get color_259 => 'DRAP';

  @override
  String get color_260 => 'COQUILLAGE';

  @override
  String get color_261 => 'ECRU';

  @override
  String get color_262 => 'ROSE SAUMON';

  @override
  String get color_263 => 'CACAO CLAIR';

  @override
  String get color_264 => 'PÊCHE POUDRÉ';

  @override
  String get color_265 => 'BRUN CLAIR';

  @override
  String get color_266 => 'KAKI';

  @override
  String get color_267 => 'GRAIN DE CAFÉ';

  @override
  String get color_268 => 'GRÈS BRUN';

  @override
  String get color_269 => 'BRUN PROFOND';

  @override
  String get color_270 => 'BRUN SOMBRE';

  @override
  String get color_271 => 'BRUN';

  @override
  String get color_272 => 'TERRACOTTA';

  @override
  String get color_273 => 'TAUPE FONCÉ';

  @override
  String get color_274 => 'GRIS VIF';

  @override
  String get color_275 => 'GRIS PROFOND';

  @override
  String get color_276 => 'MÉTAL';

  @override
  String get color_277 => 'CHROME NOIR';

  @override
  String get color_278 => 'CHARBON DE\nBOIS';

  @override
  String get color_279 => 'GRIS MOYEN';

  @override
  String get color_280 => 'GRIS FROID';

  @override
  String get color_281 => 'GRIS FUMÉE';

  @override
  String get color_282 => 'ETAIN';

  @override
  String get color_283 => 'GRIS SOMBRE';

  @override
  String get color_284 => 'GRIS';

  @override
  String get color_285 => 'GRIS CLAIR';

  @override
  String get color_286 => 'CHROME';

  @override
  String get color_287 => 'VIEIL OR';

  @override
  String get color_288 => 'ARGENT';

  @override
  String get color_289 => 'NOIR';

  @override
  String get color_290 => 'BLANC\nNATUREL';

  @override
  String get color_291 => 'BLANC';

  @override
  String get color_292 => 'ROSE NÉON';

  @override
  String get color_293 => 'ROSE PROFOND';

  @override
  String get color_294 => 'PASTÈQUE';

  @override
  String get color_295 => 'ROSE TENDRE';

  @override
  String get color_296 => 'MELON CLAIR';

  @override
  String get color_297 => 'BRUME DE\nPÊCHE';

  @override
  String get color_298 => 'JAUNE HAVANE';

  @override
  String get color_299 => 'CERISE FONCÉ';

  @override
  String get color_300 => 'ENCRE';

  @override
  String get color_301 => 'PIÈCE\nD\'APPLIQUÉ';

  @override
  String get color_302 => 'POSITION\nD\'APPLIQUÉ';

  @override
  String get color_303 => 'APPLIQUÉ';

  @override
  String get id_icon_test00001 => '\$\$\$\$\$';

  @override
  String get icon_00002 => 'Couture';

  @override
  String get icon_00003_1 => 'Broderie';

  @override
  String get icon_00006_3 => 'Point de\ncouture\nutilitaire';

  @override
  String get icon_00007_3 => 'Point\ndécoratif/\nde caractère';

  @override
  String get icon_stitch => 'Point';

  @override
  String get icon_close_1 => 'Fermer';

  @override
  String get icon_cancel => 'Annuler';

  @override
  String get icon_ok => 'OK';

  @override
  String get icon_00011_zz => '%%%icon%%%';

  @override
  String get icon_00011_zz_s => 'Supp-\nrimer';

  @override
  String get icon_00011 => 'Supprimer';

  @override
  String get icon_00012_zz => '%%%icon%%%';

  @override
  String get icon_00012_zz_s => 'Supp.\ntout';

  @override
  String get icon_reset_zz => '%%%icon%%%';

  @override
  String get icon_reset_zz_s => 'Réinit.';

  @override
  String get icon_reset => 'Réinit.';

  @override
  String get icon_reset_v => 'Réinit.';

  @override
  String get icon_00014_zz => '%%%icon%%%';

  @override
  String get icon_00014_zz_s => 'Mémoire';

  @override
  String get icon_00014 => 'Mémoire';

  @override
  String get icon_save => 'Enregistrer';

  @override
  String get icon_00015_zz => '%%%icon%%%';

  @override
  String get icon_00015_zz_s => 'Récupérer';

  @override
  String get icon_util_mem_retrieve => 'Récupérer';

  @override
  String get icon_util_mem_memory => 'Mémoire';

  @override
  String get icon_util_mem_reset => 'Réinit.';

  @override
  String get icon_util_mem_delete => 'Supprimer';

  @override
  String get icon_util_mem_alldelete => 'Supp. tout';

  @override
  String get icon_00017_zz => '%%%icon%%%';

  @override
  String get icon_00017_zz_s => 'Larg.';

  @override
  String get icon_00018_zz => '%%%icon%%%';

  @override
  String get icon_00018_zz_s => 'Long.';

  @override
  String get icon_00019_zz => '%%%icon%%%';

  @override
  String get icon_00019_zz_s => 'Tension';

  @override
  String get icon_00020_zz => '%%%icon%%%';

  @override
  String get icon_00020_zz_s => 'Sélect.G/D';

  @override
  String get icon_util_width => 'Larg.';

  @override
  String get icon_util_length => 'Long.';

  @override
  String get icon_util_lrshift => 'Sélect.G/D';

  @override
  String get icon_util_tension => 'Tension';

  @override
  String get icon_util_slitlength => 'Fente';

  @override
  String get icon_00021_zz => '%%%icon%%%';

  @override
  String get icon_00021_zz_s => 'Aiguille\njumelée';

  @override
  String get icon_00022_zz => '%%%icon%%%';

  @override
  String get icon_00022_zz_s => 'Élongation';

  @override
  String get icon_00027_zz => '%%%icon%%%';

  @override
  String get icon_00027_zz_s => 'Eff.\nPoint';

  @override
  String get icon_00028_zz => '%%%icon%%%';

  @override
  String get icon_00028_zz_s => 'Dépl.\nbloc';

  @override
  String get icon_00029_zz => '%%%icon%%%';

  @override
  String get icon_00029_zz_s => 'Insé-\nrer';

  @override
  String get icon_00038_zz => '%%%icon%%%';

  @override
  String get icon_00038_zz_s => 'Ré-\ngler';

  @override
  String get icon_00030_1 => 'Test';

  @override
  String get icon_guidel_guideline => 'Directive';

  @override
  String get icon_guidel_main => 'Principal';

  @override
  String get icon_guidel_sub => 'Secondaire';

  @override
  String get icon_guidel_mainline => 'Ligne principale';

  @override
  String get icon_guidel_subline => 'Ligne secondaire';

  @override
  String get icon_guidel_linelength => 'Longueur de ligne';

  @override
  String get icon_guidel_line_l => 'L';

  @override
  String get icon_guidel_line_m => 'M';

  @override
  String get icon_guidel_line_s => 'S';

  @override
  String get icon_guidel_color => 'Couleur';

  @override
  String get icon_guidel_position => 'Position';

  @override
  String get icon_guidel_main_pos => 'Position de ligne principale';

  @override
  String get icon_guidel_sub_pos => 'Position de ligne secondaire';

  @override
  String get icon__guidel_sub_frommain => 'Distance de la ligne principale';

  @override
  String get icon_guidel_gridsize => 'Taille de grille';

  @override
  String get icon_guidel_angle => 'Angle';

  @override
  String get icon_guidel_seamallowance => 'Rabat';

  @override
  String get icon_guidel_spacing => 'Espacement';

  @override
  String get icon_guidel_lengthl_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengthl_zz_s => 'G';

  @override
  String get icon_guidel_lengthm_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengthm_zz_s => 'M';

  @override
  String get icon_guidel_lengths_zz => '%%%icon%%%';

  @override
  String get icon_guidel_lengths_zz_s => 'P';

  @override
  String get icon_position => 'Position';

  @override
  String get icon_00031_2 => 'Édition';

  @override
  String get icon_00033_1 => 'Ajouter';

  @override
  String get icon_00035 => 'Broderie';

  @override
  String get icon_return => 'Retour';

  @override
  String get icon_00038_1 => 'Régler';

  @override
  String get icon_00038_2 => 'Régler';

  @override
  String get icon_00039 => 'min';

  @override
  String get icon_00041_1 => 'Sélection';

  @override
  String get icon_select => 'Sélection';

  @override
  String get icon_select_2 => 'Sélect';

  @override
  String get icon_00041_2 => 'Sélect';

  @override
  String get icon_00042 => 'Toujours appuyer pour\nretirer l\'unité de broderie.';

  @override
  String get icon_00046_zz => '%%%icon%%%';

  @override
  String get icon_00046_zz_s => 'Dupliquer';

  @override
  String get icon_00048 => 'Numéro';

  @override
  String get icon_00049 => 'Liste';

  @override
  String get icon_00050 => 'Charger';

  @override
  String get icon_00051_zz => '%%%icon%%%';

  @override
  String get icon_00051_zz_s => 'Eff-\nacer';

  @override
  String get icon_00052_zz => '%%%icon%%%';

  @override
  String get icon_00052_zz_s => 'Eff.\ntout';

  @override
  String get icon_00053_b1 => '%%%none%%%';

  @override
  String get icon_00053_b2 => '%%%none%%%';

  @override
  String get icon_00053_t1 => 'Color Visualizer';

  @override
  String get icon_00053_t2 => 'Color\nVisualizer';

  @override
  String get icon_00055_1 => 'Aléatoire';

  @override
  String get icon_00055_2 => 'Aléatoire';

  @override
  String get icon_00056_1 => 'Dégradé';

  @override
  String get icon_00056_2 => 'Dégradé';

  @override
  String get icon_00057 => 'Vif';

  @override
  String get icon_00054 => 'Doux';

  @override
  String get icon_00058_1 => 'Nombre de couleurs';

  @override
  String get icon_00059 => 'Actualiser';

  @override
  String get icon_00060 => 'Aucune couleur spécifiée';

  @override
  String get icon_emb_tension => 'Tension';

  @override
  String get icon_emb_threadcutting => 'Coupe-fil';

  @override
  String get icon_00063_a => 'Coupe du fil en fin de couleur';

  @override
  String get icon_00064_a => 'Excédent de fil à couper';

  @override
  String get icon_00065 => 'Densité';

  @override
  String get icon_00066 => 'Taille';

  @override
  String get icon_00067_zz => '%%%icon%%%';

  @override
  String get icon_00067_zz_s => 'Rotat.';

  @override
  String get icon_00068_zz => '%%%icon%%%';

  @override
  String get icon_00068_zz_s => 'Déplac';

  @override
  String get icon_00070_zz => '%%%icon%%%';

  @override
  String get icon_00070_zz_s => 'Multi\ncoul.';

  @override
  String get icon_00071_zz => '%%%icon%%%';

  @override
  String get icon_00071_zz_s => 'Vérif.';

  @override
  String get icon_00072 => 'Disposition';

  @override
  String get icon_00075_zz => '%%%icon%%%';

  @override
  String get icon_00075_zz_s => 'Assem-\nblage';

  @override
  String get icon_00076_zz => '%%%icon%%%';

  @override
  String get icon_00076_zz_s => 'Espace-\nment';

  @override
  String get icon_00077_zz => '%%%icon%%%';

  @override
  String get icon_00077_zz_s => 'Modif.\npolice';

  @override
  String get icon_00079 => 'Point de chute de l\'aiguille';

  @override
  String get icon_00080 => 'Suivant';

  @override
  String get icon_prev => 'Précédent';

  @override
  String get icon_segment => 'Segment';

  @override
  String get icon_00083 => 'Réglage du point final';

  @override
  String get icon_00084 => 'Définition de la longueur';

  @override
  String get icon_00085 => 'Réglage du point final\nArrêt temporaire';

  @override
  String get icon_00088 => 'Num.';

  @override
  String get icon_00089 => 'Vidéo';

  @override
  String get icon_00090 => 'Rejouer la vidéo';

  @override
  String get icon_00091_1 => 'Sélection multiple';

  @override
  String get icon_00091_2 => 'Sélection\nmultiple';

  @override
  String get icon_00093_zz => '%%%icon%%%';

  @override
  String get icon_00093_zz_s => 'Sélect.\ntout';

  @override
  String get icon_00094_zz => '%%%icon%%%';

  @override
  String get icon_00094_zz_s => 'Sélect.\naucun';

  @override
  String get icon_00095 => 'Quitter';

  @override
  String get icon_00096 => 'Fractionnement auto des écharpes en quilting (courtepointe)';

  @override
  String get icon_resettodef => 'Rétablir les réglages par défaut';

  @override
  String get icon_resettodefall => 'Rétablir les valeurs par défaut';

  @override
  String get icon_resettodefall_2 => 'Rétablir les valeurs\npar défaut';

  @override
  String get icon_00100 => 'Langue';

  @override
  String get icon_00101_a => 'Français';

  @override
  String get icon_00101_b => 'French';

  @override
  String get icon_00102 => 'Luminosité';

  @override
  String get icon_00103 => 'Luminosité de\nl\'affichage de l\'écran';

  @override
  String get icon_00104 => 'Économiseur d\'écran';

  @override
  String get icon_00105 => 'Par défaut';

  @override
  String get icon_00106 => 'Personnaliser';

  @override
  String get icon_00107 => 'Mode Éco';

  @override
  String get icon_00108 => 'Mode de prise en charge\nde l\'arrêt';

  @override
  String get icon_00109 => 'Luminosité';

  @override
  String get icon_00112 => 'Volume du haut-parleur\nde la machine';

  @override
  String get icon_00114 => 'Volume';

  @override
  String get icon_00115 => 'Curseur';

  @override
  String get icon_00116 => 'mm / \"(inch)';

  @override
  String get icon_00118 => 'Page d\'accueil';

  @override
  String get icon_00119 => 'Ecran d\'accueil\nde couture/\nbroderie';

  @override
  String get icon_00192 => 'Écran initial';

  @override
  String get icon_00121 => 'Écran d\'ouverture';

  @override
  String get icon_00122 => 'Page initiale des points';

  @override
  String get icon_00123 => 'Voyant lumineux du support de bobine';

  @override
  String get icon_00124 => 'Contrôle de la largeur';

  @override
  String get icon_00125_1 => 'Ajust.fin.verti';

  @override
  String get icon_00126_1 => 'Ajust.fin.horiz';

  @override
  String get icon_00127_1 => 'Hauteur du pied-\nde-biche';

  @override
  String get icon_00128_1 => 'Pression du pied-de-biche';

  @override
  String get icon_00129 => 'Position initiale';

  @override
  String get icon_00130_1 => 'Hauteur de pivotement';

  @override
  String get icon_00131_1 => 'Hauteur du pied-de-\nbiche pour la couture\nen mouvement libre';

  @override
  String get icon_00134 => 'Système auto\ncapt. tissu ';

  @override
  String get icon_00135 => 'Capteur d\'épaisseur\ndu tissu';

  @override
  String get icon_00136_2 => 'Positionnement aiguille -\nHaut/Bas';

  @override
  String get icon_00137 => 'Positionnement aiguille -\nDisposition des points';

  @override
  String get icon_00138 => 'Capteur de fil supérieur\net de canette';

  @override
  String get icon_00140 => 'Réglage de\nl\'entraînement du pied\nmotorisé. ';

  @override
  String get icon_00141 => 'Pédale multifonction';

  @override
  String get icon_00142 => 'Appui du talon';

  @override
  String get icon_00143 => 'Pédale latérale';

  @override
  String get icon_00144_a => 'Positionnement\naiguille\nHaut/Bas';

  @override
  String get icon_00145 => 'Coupe du fil';

  @override
  String get icon_00146 => 'Point unique';

  @override
  String get icon_00147 => 'Point inverse';

  @override
  String get icon_00243 => 'Relever/Abaisser\nle pied-de-biche';

  @override
  String get icon_00244 => 'Pas de réglage';

  @override
  String get icon_00249 => 'Point de renfort';

  @override
  String get icon_00148 => 'Longueur de fente';

  @override
  String get icon_00148_zz => '%%%icon%%%';

  @override
  String get icon_00148_zz_s => 'Long.\nde fente';

  @override
  String get icon_00150 => 'Priorité du renfort';

  @override
  String get icon_00152_1 => 'Visualisation du cadre\nà broder';

  @override
  String get icon_00155_1 => 'Taille d\'onglet';

  @override
  String get icon_00157 => 'Couleur d\'arrière-plan\nde la zone de broderie';

  @override
  String get icon_00159 => 'Couleur d\'arrière-plan\nde la zone d\'onglets';

  @override
  String get icon_00163_a => 'Affichage de l\'image\nen arrière-plan';

  @override
  String get icon_00163 => 'Image\nd\'arrière-plan';

  @override
  String get icon_00164 => 'Numériser l’image';

  @override
  String get icon_00165 => 'Standard';

  @override
  String get icon_00166 => 'Haute';

  @override
  String get icon_00167 => 'Qualité de numérisation';

  @override
  String get icon_00168 => 'Voyant lumineux du support de bobine';

  @override
  String get icon_00178 => 'Distance du motif au\npoint de faufilage';

  @override
  String get icon_00180 => 'Distance de l\'appliqué au contour';

  @override
  String get icon_00182_1 => 'Vitesse de broderie max';

  @override
  String get icon_00183_1 => 'Tension de la broderie';

  @override
  String get icon_00184_1 => 'Hauteur du pied\nde broderie';

  @override
  String get icon_00185 => 'Taille de cadre';

  @override
  String get icon_00186 => 'Grille';

  @override
  String get icon_00187 => 'Modifier';

  @override
  String get icon_00188 => 'Supprimer';

  @override
  String get icon_00191 => 'Couleur';

  @override
  String get icon_00193 => 'Affichage de l\'heure';

  @override
  String get icon_00194 => 'AM';

  @override
  String get icon_00195 => 'PM';

  @override
  String get icon_00196 => '24h';

  @override
  String get icon_clock_msg1 => 'Définissez la date correcte pour la connexion réseau.';

  @override
  String get icon_00197 => 'Calibrage de\nla fonction capteur';

  @override
  String get icon_00199 => 'Réglage du traçage de la\nligne de guidage';

  @override
  String get icon_00200 => 'Luminosité du traçage de la ligne de guidage';

  @override
  String get icon_00201_1 => 'Réglage\ndu pied de broderie\navec pointeur lumineux';

  @override
  String get icon_00202_p => 'Luminosité';

  @override
  String get icon_00206_1 => 'Certific.';

  @override
  String get icon_00207_a => 'Kit';

  @override
  String get icon_00208 => 'Démarrer';

  @override
  String get icon_00209 => 'Arreter';

  @override
  String get icon_00211 => 'Compteur de\nmaintenance';

  @override
  String get icon_00212 => 'SCS';

  @override
  String get icon_00214 => 'Compteur total ';

  @override
  String get icon_00218 => 'No.';

  @override
  String get icon_00220 => 'Version';

  @override
  String get icon_00222 => 'YYYY';

  @override
  String get icon_00223 => 'MM';

  @override
  String get icon_00224 => 'DD';

  @override
  String get icon_00225 => 'mm';

  @override
  String get icon_00226 => '\"';

  @override
  String get icon_on => 'ON';

  @override
  String get icon_off => 'OFF';

  @override
  String get icon_00229 => 'KB';

  @override
  String get icon_00230 => 'bPocket';

  @override
  String get icon_00231 => '1';

  @override
  String get icon_00232 => '2';

  @override
  String get icon_00233 => '3';

  @override
  String get icon_00234 => '4';

  @override
  String get icon_00235 => '5';

  @override
  String get icon_00236 => '6';

  @override
  String get icon_00237 => '7';

  @override
  String get icon_00238 => '8';

  @override
  String get icon_00239 => '9';

  @override
  String get icon_00240 => '0';

  @override
  String get icon_00241 => 'C';

  @override
  String get icon_00242 => '%';

  @override
  String get icon_00245 => 'Le pied-de-biche\ns’abaisse/se relève\nautomatiquement';

  @override
  String get icon_00246 => 'Abaissement';

  @override
  String get icon_00247 => 'Relèvement';

  @override
  String get icon_00248_zz => '%%%icon%%%';

  @override
  String get icon_00248_zz_s => 'Appuyer sur la touche\npour couper';

  @override
  String get icon_00248 => 'Appuyer sur la touche\npour couper';

  @override
  String get icon_00251 => 'Projecteur';

  @override
  String get icon_00253 => 'Couleur d\'arrière-plan';

  @override
  String get icon_00254 => 'Couture:\nContour du motif';

  @override
  String get icon_00255 => 'Broderie:\nCouleur du pointeur';

  @override
  String get icon_pointershape => 'Forme du pointeur';

  @override
  String get icon_00256 => 'Caméra';

  @override
  String get icon_00257 => 'Calibrage d\'aiguille pour\nla caméra/le projecteur';

  @override
  String get icon_recog_ok => 'OK';

  @override
  String get icon_recog_ng => 'NG';

  @override
  String get icon_00258 => 'Broderie\nPosition\nd\'arrêt de l\'aiguille';

  @override
  String get icon_00259 => 'Unité';

  @override
  String get icon_00260 => 'Couleur de fil';

  @override
  String get icon_00261 => 'Marque du fil';

  @override
  String get icon_00264 => 'Nom de la\ncouleur';

  @override
  String get icon_00265 => '# 123';

  @override
  String get icon_00266 => 'Durée';

  @override
  String get icon_00268 => 'Original';

  @override
  String get icon_00269 => 'Embroidery';

  @override
  String get icon_00269_t => 'Embroidery';

  @override
  String get icon_00270 => 'Country';

  @override
  String get icon_00270_t => 'Country';

  @override
  String get icon_00271 => 'Madeira\nPoly';

  @override
  String get icon_00272 => 'Madeira\nRayon';

  @override
  String get icon_00273 => 'Sulky';

  @override
  String get icon_00274 => 'Robison-Anton';

  @override
  String get icon_00275 => 'Robison-Anton\nPoly';

  @override
  String get icon_00276 => 'Robison-Anton\nRayon';

  @override
  String get icon_00277 => 'Isacord';

  @override
  String get icon_00278 => 'Gütermann';

  @override
  String get icon_00279 => 'Simplicity Pro';

  @override
  String get icon_00279_p => 'Pacesetter Pro';

  @override
  String get icon_00280 => 'Floriani';

  @override
  String get icon_00281 => 'Iris';

  @override
  String get icon_00282 => 'Aurifil';

  @override
  String get icon_00283 => 'WonderFil ';

  @override
  String get icon_00284 => 'Polyfast';

  @override
  String get icon_00290 => 'Si vous avez acheté le kit de mise à niveau\net souhaitez certifier votre machine,\nappuyez sur [CERTIFIC.].';

  @override
  String get icon_00291 => 'KIT I';

  @override
  String get icon_00292 => 'KIT II';

  @override
  String get icon_00293 => 'KIT III';

  @override
  String get icon_00294 => 'KIT IV';

  @override
  String get icon_00295 => 'KIT V';

  @override
  String get icon_00296 => 'KIT VI';

  @override
  String get icon_00297 => 'KIT VII';

  @override
  String get icon_00298 => 'KIT VIII';

  @override
  String get icon_00299 => 'KIT IX';

  @override
  String get icon_00300 => 'KIT X';

  @override
  String get icon_00643_s => 'Aucun';

  @override
  String get icon_00301 => 'Guide de fonctionnement';

  @override
  String get icon_00302 => 'Guide de couture';

  @override
  String get icon_00303 => 'Explication des motifs';

  @override
  String get icon_manuals => 'Manuels';

  @override
  String get icon_operariong_b => 'Manuel d’instructions';

  @override
  String get icon_operariong_t => 'Guide d’instructions et de référence';

  @override
  String get icon_pdf => 'Manuel au format PDF';

  @override
  String get icon_supportsite => 'Site d\'assistance';

  @override
  String get icon_pdf_eula => 'Accords de licence de\nl\'utilisateur final (EULA)';

  @override
  String get icon_pdf_sewing => 'Couture';

  @override
  String get icon_pdf_emb => 'Broderie';

  @override
  String get icon_pdf_sewing_ef => 'Couture';

  @override
  String get icon_pdf_emb_ef => 'Broderie';

  @override
  String get icon_pdf_sewing_t => 'Couture (English)';

  @override
  String get icon_pdf_emb_t => 'Broderie (English)';

  @override
  String get icon_f_omadendum => 'Addendum';

  @override
  String get icon_f_omadendum_ef => 'Addendum';

  @override
  String get icon_f_omadendum_l => 'Manuel d\'instructions -\nAddendum';

  @override
  String get icon_f_om_kit1 => 'KIT I';

  @override
  String get icon_f_om_kit2 => 'KIT II';

  @override
  String get icon_f_om_kit3 => 'KIT III';

  @override
  String get icon_f_om_kit1_l => 'Manuel d\'instructions\nKIT I';

  @override
  String get icon_f_omadendum_t => 'Addendum (English)';

  @override
  String get icon_f_om_kit1_t => 'KIT I (English)';

  @override
  String get icon_f_om_kit2_t => 'KIT II (English)';

  @override
  String get icon_f_om_kit3_t => 'KIT III (English)';

  @override
  String get icon_t_pdf_iivo_url_b => 'Si vous souhaitez voir les manuels sur votre appareil mobile ou votre ordinateur, veuillez consulter\nhttps://s.brother/fmraa.';

  @override
  String get icon_t_pdf_iivo_url_t => 'Si vous souhaitez voir le manuel sur votre appareil mobile ou votre ordinateur, veuillez consulter\nhttps://babylock.com/radiance-instruction-and-reference-guide.';

  @override
  String get icon_t_video_iivo_url_b => 'Rendez-vous sur\n https://s.brother/fvraa\npour consulter les tutoriels vidéo sur ce modèle.';

  @override
  String get icon_t_video_iivo_url_t => 'Rendez-vous sur\n https://babylock.com/radiance-training\npour consulter les tutoriels vidéo sur ce modèle.';

  @override
  String get icon_pdf_url_qr_t => 'www.babylock.com';

  @override
  String get icon_nettool => 'Outil de diagnostic réseau';

  @override
  String get icon_iagree => 'J\'accepte';

  @override
  String get icon_terms_cancel => 'Annuler';

  @override
  String get icon_confirm => 'Confirmer';

  @override
  String get icon_00304 => 'Pièces principales';

  @override
  String get icon_00305 => 'Touches principales';

  @override
  String get icon_00306 => 'Opération de base';

  @override
  String get icon_00307 => 'Opération de base\nde broderie';

  @override
  String get icon_00308 => 'Dépannage';

  @override
  String get icon_00309 => 'Maintenance';

  @override
  String get icon_00310 => 'Le fil est emmêlé sur\nl\'envers du tissu';

  @override
  String get icon_00311 => 'Impossible d\'enfiler l\'aiguille';

  @override
  String get icon_00312 => 'Impossible d’utiliser\nl’enfileur d’aiguille';

  @override
  String get icon_00313 => 'La tension du fil\nn\'est pas correcte';

  @override
  String get icon_00314 => 'Le fil supérieur casse';

  @override
  String get icon_00315 => 'Le fil de la canette casse';

  @override
  String get icon_00316 => 'Des points ont été sautés';

  @override
  String get icon_00317 => 'L\'aiguille casse';

  @override
  String get icon_00318 => 'La machine ne fonctionne pas';

  @override
  String get icon_00320 => 'Le motif de caractère\nn\'est pas correct';

  @override
  String get icon_00321 => 'Le tissu n\'est pas\nentraîné dans la machine';

  @override
  String get icon_00322 => 'Le tissu fronce';

  @override
  String get icon_00323 => 'La machine est bruyante';

  @override
  String get icon_00325 => 'Le motif de broderie n\'est\npas cousu correctement';

  @override
  String get icon_00326 => 'L\'unité de broderie\nne fonctionne pas';

  @override
  String get icon_00331 => 'Point d\'arrêt';

  @override
  String get icon_00332 => 'Point invisible';

  @override
  String get icon_00333 => 'Boutonnière';

  @override
  String get icon_00334 => 'Boutons';

  @override
  String get icon_00335 => 'Pince';

  @override
  String get icon_00336 => 'Couture\nrabattue';

  @override
  String get icon_00337 => 'Fronçage';

  @override
  String get icon_00338 => 'Surfilage';

  @override
  String get icon_00339 => 'Pli cousu';

  @override
  String get icon_00340 => 'Feston';

  @override
  String get icon_00341 => 'Point droit';

  @override
  String get icon_00342 => 'Insertion fermeture\nà glissière';

  @override
  String get icon_00343 => 'Assemblage';

  @override
  String get icon_00344 => 'Courtepointe en\nmouvement libre';

  @override
  String get icon_00345 => 'Courtepointe';

  @override
  String get icon_00346 => 'Courtepointe en écho';

  @override
  String get icon_00347 => 'Appliqué 1';

  @override
  String get icon_00348 => 'Appliqué 2';

  @override
  String get icon_search => 'Recherche';

  @override
  String get icon_00353 => 'Enfilage supérieur de la machine';

  @override
  String get icon_00354 => 'Bobinage de la canette';

  @override
  String get icon_00355 => 'Changement de l’aiguille';

  @override
  String get icon_00356 => 'Changement du pied-de-biche';

  @override
  String get icon_00357 => 'Mise en place de la canette';

  @override
  String get icon_00358 => 'Fonction de couture';

  @override
  String get icon_00359 => 'Utilisation de la fonction de coupure de fil';

  @override
  String get icon_00360 => 'Utilisation d’un tournevis spécial';

  @override
  String get icon_00361 => 'Utilisation de la fonction de pivotement';

  @override
  String get icon_00362 => 'Réglage de la largeur et de la longueur du point';

  @override
  String get icon_00363 => 'Utilisation d’un tournevis polyvalent';

  @override
  String get icon_00364 => 'Utilisation du système de capteur de tissu automatique (pression automatique du pied-de-biche)';

  @override
  String get icon_00365 => 'Utilisation de My Custom Stitch (Mon point personnel)';

  @override
  String get icon_00366 => 'Utilisation de la fonction de couture en bordure';

  @override
  String get icon_00367 => 'Création d’un travail à la canette (couture)';

  @override
  String get icon_00368 => 'Création d’un travail à la canette (broderie)';

  @override
  String get icon_00369 => 'Préparation d’un travail à la canette';

  @override
  String get icon_00370 => 'Préparation d’un travail à la canette inverse';

  @override
  String get icon_00371 => 'Création d’un travail à la canette inverse (couture)';

  @override
  String get icon_00372 => 'Utilisation de la caméra intégrée dans le mode de couture';

  @override
  String get icon_00373 => 'Réglage du point de chute de l’aiguille avec le traçage de la ligne de guidage sur l’écran de réglages';

  @override
  String get icon_00374 => 'Réglage de la luminosité du traçage de la ligne de guidage sur l’écran de réglages';

  @override
  String get icon_00375 => 'Réglage de la tension du fil';

  @override
  String get icon_00376 => 'Application de renforts thermocollants';

  @override
  String get icon_00377 => 'Mise en place du tissu dans le cadre de broderie';

  @override
  String get icon_00378 => 'Mise en place/retrait du cadre de broderie';

  @override
  String get icon_00379 => 'Mise en place/retrait de l’unité de broderie/du plateau';

  @override
  String get icon_00380 => 'Mise en place/retrait du support du pied-de-biche';

  @override
  String get icon_00381 => 'Fonction de broderie';

  @override
  String get icon_00382 => 'Utilisation de la fonction Imprimer et broder';

  @override
  String get icon_00383 => 'Utilisation de la fonction Couleurs aléatoires';

  @override
  String get icon_00384 => 'Utilisation de Mon centre de motifs';

  @override
  String get icon_00385 => 'Numérisation de lignes de tracé';

  @override
  String get icon_00386 => 'Numérisation d’illustrations';

  @override
  String get icon_00387 => 'Utilisation du cadre de numérisation';

  @override
  String get icon_00388 => 'Affichage du tissu sur l’écran à cristaux liquides (numérisation à l’aide de la caméra intégrée)';

  @override
  String get icon_00389 => 'Alignement du positionnement de la broderie à l’aide de la vignette de positionnement';

  @override
  String get icon_00390 => 'Connexion de motifs à l’aide de la caméra intégrée';

  @override
  String get icon_00391 => 'Alignement du positionnement de broderie à l’aide de la caméra intégrée';

  @override
  String get icon_00392 => '';

  @override
  String get icon_00393 => 'Réglages';

  @override
  String get icon_00394 => 'Réglage du positionnement de l’aiguille sur la caméra';

  @override
  String get icon_00395 => 'Mise à niveau du logiciel de la machine';

  @override
  String get icon_00396 => 'Réglage du traçage de la ligne de guidage sur l’écran de réglages';

  @override
  String get icon_00397 => 'Réglage de l’heure/la date';

  @override
  String get icon_00398 => 'Utilisation de la couture automatique de points de renfort';

  @override
  String get icon_00399 => 'Autres';

  @override
  String get icon_00400 => 'Visionnement/enregistrement de vidéos';

  @override
  String get icon_00401 => 'Stylet';

  @override
  String get icon_00402 => 'Connexion du stylet';

  @override
  String get icon_00403 => 'Calibrage du stylet';

  @override
  String get icon_00404 => 'Spécification de la position de traçage de la ligne de guidage à l’aide du stylet';

  @override
  String get icon_00405 => 'Spécification du point de chute de l’aiguille à l’aide du stylet';

  @override
  String get icon_00406 => 'Spécification de la largeur/position du point à l’aide du stylet';

  @override
  String get icon_00407 => 'Spécification du point final de couture à l’aide du stylet';

  @override
  String get icon_00408 => 'Spécification du positionnement de la broderie à l’aide du stylet';

  @override
  String get icon_00409 => 'Accessoires';

  @override
  String get icon_00410 => 'Utilisation de la genouillère';

  @override
  String get icon_00411 => 'Utilisation du tournevis à têtes multiples';

  @override
  String get icon_00412 => 'Utilisation d’un tournevis polyvalent';

  @override
  String get icon_00416 => 'Installation de la pédale multifonction';

  @override
  String get icon_00417 => 'Attribution de fonctions à la pédale multifonction';

  @override
  String get icon_00418 => 'Fixation/retrait du pied de broderie avec pointeur lumineux';

  @override
  String get icon_00419 => 'Réglage du pied de broderie avec pointeur lumineux';

  @override
  String get icon_00420 => 'Création de motifs de broderie en pointillés à l’aide de la caméra intégrée';

  @override
  String get icon_00421 => 'Fixation du pied-de-biche avec l’adaptateur fourni';

  @override
  String get icon_00422 => 'Utilisation de la boîte à accessoires';

  @override
  String get icon_00423 => 'Entretien (nettoyage de la coursière)';

  @override
  String get icon_00500 => 'Mon centre de motifs';

  @override
  String get icon_00500_2 => 'Mon centre\nde motifs';

  @override
  String get icon_iqdesigner => 'IQ Designer';

  @override
  String get icon_00501 => 'Numérisation de lignes';

  @override
  String get icon_00503_zz => '%%%icon%%%';

  @override
  String get icon_00503_zz_s => 'Ligne';

  @override
  String get icon_00505 => 'Numérisation d’illustrations';

  @override
  String get icon_imagescan => 'Numérisation d\'image';

  @override
  String get icon_linedesign => 'Dessin au trait';

  @override
  String get icon_illustrationdesign => 'Création\nd\'une illustration';

  @override
  String get icon_00509_zz => '%%%icon%%%';

  @override
  String get icon_00510 => 'Reconnaissance';

  @override
  String get icon_00511_1 => 'Aperçu';

  @override
  String get icon_00511_2 => 'Aperçu';

  @override
  String get icon_showpreview => 'Voir aperçu';

  @override
  String get icon_00512 => 'Réessayer';

  @override
  String get icon_00514 => 'Ignorer la taille de l’objet';

  @override
  String get icon_00516 => 'Niveau de détection de l\'échelle de gris';

  @override
  String get icon_00503 => 'Ligne';

  @override
  String get icon_00518 => 'Gomme';

  @override
  String get icon_00520 => 'Aperçu de\nl’original';

  @override
  String get icon_00521 => 'Aperçu du\nrésultat';

  @override
  String get icon_00522 => 'Aperçu du résultat';

  @override
  String get icon_00523 => 'Nombre max.\nde couleurs';

  @override
  String get icon_00525 => 'Supprimer\nl\'arrière-plan';

  @override
  String get icon_00526 => 'Reconnaissance';

  @override
  String get icon_00528 => 'Réglages de broderie';

  @override
  String get icon_00529 => 'Propriétés de la ligne';

  @override
  String get icon_00530 => 'Propriétés de la zone';

  @override
  String get icon_00533 => 'Taille';

  @override
  String get icon_00537 => 'Larg. zigzag';

  @override
  String get icon_00538 => 'Densité';

  @override
  String get icon_00539 => 'Long. point';

  @override
  String get icon_00540 => 'Point remplissage';

  @override
  String get icon_00541 => 'Direction';

  @override
  String get icon_00544 => 'Compensation\nd\'étirement';

  @override
  String get icon_00545 => 'Réduire\nétirement';

  @override
  String get icon_00547 => 'Espacement';

  @override
  String get icon_00548_1 => 'Manuelle';

  @override
  String get icon_00548_2 => 'Man.';

  @override
  String get icon_00549_1 => 'Automatique';

  @override
  String get icon_00549_2 => 'Auto';

  @override
  String get icon_00550 => 'Coudre';

  @override
  String get icon_00551 => 'Cadrage de l’image';

  @override
  String get icon_00552 => 'Caractéristiques des couleurs';

  @override
  String get icon_00553 => 'Suivant';

  @override
  String get icon_00554 => 'Distance';

  @override
  String get icon_00555 => 'Enregistrement des contours';

  @override
  String get icon_00556 => 'Formes fermées';

  @override
  String get icon_00557 => 'Formes ouvertes';

  @override
  String get icon_00558 => 'Contours enregistrés';

  @override
  String get icon_00559 => 'Zones de broderie du cadre';

  @override
  String get icon_00562 => 'Contour';

  @override
  String get icon_00564 => 'Épaisseur';

  @override
  String get icon_00565 => 'Décalage\naléatoire';

  @override
  String get icon_00566 => 'Décalage de\nposition';

  @override
  String get icon_inside => 'Intérieur';

  @override
  String get icon_outside => 'Extérieur';

  @override
  String get icon_00567 => 'Inverser';

  @override
  String get icon_00568 => 'Largeur\ndu point';

  @override
  String get icon_00569 => 'Actuel';

  @override
  String get icon_00570 => 'Nouveau';

  @override
  String get icon_frame_297_465_mm => '297 × 465 mm';

  @override
  String get icon_frame_297_465_inch => '11-5/8\"× 18-1/4\"';

  @override
  String get icon_frame_272_408_mm => '272 × 408 mm';

  @override
  String get icon_frame_272_408_inch => '10-5/8\"× 16\"';

  @override
  String get icon_frame_254_254_mm => '254 × 254 mm';

  @override
  String get icon_frame_254_254_inch => '10\"× 10\"';

  @override
  String get icon_frame_240_360_mm => '240 × 360 mm';

  @override
  String get icon_frame_240_360_inch => '9-1/2\"× 14\"';

  @override
  String get icon_frame_180_360_mm => '180 × 360 mm';

  @override
  String get icon_frame_180_360_inch => ' 7\" × 14\"';

  @override
  String get icon_frame_180_300_mm => '180 × 300 mm';

  @override
  String get icon_frame_180_300_inch => ' 7\" × 12\"';

  @override
  String get icon_frame_200_300_mm => '200 × 300 mm';

  @override
  String get icon_frame_200_300_inch => '8\"×12\"';

  @override
  String get icon_frame_100_300_mm => '100 × 300 mm';

  @override
  String get icon_frame_100_300_inch => '4\"× 12\"';

  @override
  String get icon_frame_160_260_mm => '160 × 260 mm';

  @override
  String get icon_frame_160_260_inch => '6-1/4\"× 10-1/4\"';

  @override
  String get icon_frame_240_240_mm => '240 × 240 mm';

  @override
  String get icon_frame_240_240_inch => '9-1/2\"× 9-1/2\"';

  @override
  String get icon_frame_200_200_mm => '200 × 200 mm';

  @override
  String get icon_frame_200_200_inch => '8\"× 8\"';

  @override
  String get icon_frame_130_180_mm => '130 × 180 mm';

  @override
  String get icon_frame_130_180_inch => '5\"× 7\"';

  @override
  String get icon_frame_100_180_mm => '100 × 180 mm';

  @override
  String get icon_frame_100_180_inch => '4\"× 7\"';

  @override
  String get icon_frame_150_150_mm => '150 × 150 mm';

  @override
  String get icon_frame_150_150_inch => '6\"× 6\"';

  @override
  String get icon_frame_100_100_mm => '100 × 100 mm';

  @override
  String get icon_frame_100_100_inch => '4\"× 4\"';

  @override
  String get icon_frame_60_20_mm => '60 × 20 mm';

  @override
  String get icon_frame_60_20_inch => '2-3/8\"× 3/4\"';

  @override
  String get icon_zoom_50 => '50';

  @override
  String get icon_zoom_100 => '100';

  @override
  String get icon_zoom_125 => '125';

  @override
  String get icon_zoom_150 => '150';

  @override
  String get icon_zoom_200 => '200';

  @override
  String get icon_zoom_400 => '400';

  @override
  String get icon_zoom_800 => '800';

  @override
  String get icon_zoom_120 => '120';

  @override
  String get icon_zoom_240 => '240';

  @override
  String get icon_zoom_480 => '480';

  @override
  String get icon_zoom_960 => '960';

  @override
  String get icon_00600 => 'Activer le réseau local sans fil';

  @override
  String get icon_00600_1 => 'WLAN activé';

  @override
  String get icon_00601 => 'SSID';

  @override
  String get icon_00602 => 'Sélectionner SSID...';

  @override
  String get icon_00603 => 'Nom de la machine';

  @override
  String get icon_00604 => 'WPS (Push)';

  @override
  String get icon_00605 => 'WPS (Pin)';

  @override
  String get icon_00606 => 'Autres';

  @override
  String get icon_00608 => 'État du réseau local sans fil';

  @override
  String get icon_00608_1 => 'Etat WLAN';

  @override
  String get icon_00609 => 'SSID\nenregistré';

  @override
  String get icon_00609_1 => 'SSID enregistré';

  @override
  String get icon_00610 => 'Nouveau\nSSID';

  @override
  String get icon_wlan_title => 'Réseau local LAN sans fil';

  @override
  String get icon_wlan_connection => 'Connexion au réseau local LAN sans fil';

  @override
  String get icon_wlan_networks => 'Réseaux LAN sans fil';

  @override
  String get icon_wlan_enable => 'Activer le réseau local sans fil';

  @override
  String get icon_wlan_setinfo_01 => 'Pour voir les réseaux disponibles, activez  Activer le réseau local sans fil.';

  @override
  String get icon_wlan_setinfo_02 => 'Recherche de réseaux LAN sans fil…';

  @override
  String get icon_wlan_setinfo_03 => 'Connexion au réseau local LAN sans fil en cours...';

  @override
  String get icon_wlan_setinfo_05 => 'Activation du réseau local LAN sans fil en cours…';

  @override
  String get icon_wlan_setinfo_06 => 'Réseau local LAN sans fil activé';

  @override
  String get icon_wlan_setinfo_04 => 'Désactivation du réseau local LAN sans fil en cours...';

  @override
  String get icon_wlan_setinfo_07 => 'Tous les paramètres réseau seront réinitialisés, y compris les suivants :·Réseau local LAN sans fil';

  @override
  String get icon_wlan_networkreset => 'Réinitialisation du réseau';

  @override
  String get icon_wlan_limitedconnect => 'Impossible de se connecter au réseau. Veuillez vérifier les paramètres de l\'horloge.';

  @override
  String get icon_00630 => 'Réseau';

  @override
  String get icon_00631 => 'Assistant de configuration du réseau\nlocal sans fil';

  @override
  String get icon_00631_1 => 'Assis. config.';

  @override
  String get icon_00632 => 'Détail';

  @override
  String get icon_00633 => 'Etat';

  @override
  String get icon_00634 => 'Signal';

  @override
  String get icon_00635 => 'Mode de commu.';

  @override
  String get icon_00636 => 'Activé(11b)';

  @override
  String get icon_00637 => 'Activé(11g)';

  @override
  String get icon_00638 => 'Activé (11n)';

  @override
  String get icon_00639 => 'Echec de la connexion';

  @override
  String get icon_00640 => 'Fort';

  @override
  String get icon_00641 => 'Moyen';

  @override
  String get icon_00642 => 'Faible';

  @override
  String get icon_00643 => 'Aucun';

  @override
  String get icon_00644 => 'Ad-hoc';

  @override
  String get icon_00645 => 'Infrastructure';

  @override
  String get icon_00646 => 'TCP/IP';

  @override
  String get icon_00647 => 'Adresse MAC';

  @override
  String get icon_00648 => 'Paramètres proxy';

  @override
  String get icon_00649 => 'Méthode BOOT';

  @override
  String get icon_00650 => 'Adresse IP';

  @override
  String get icon_00651 => 'Masq.SS.réseau';

  @override
  String get icon_00652 => 'Passerelle';

  @override
  String get icon_00653 => 'Nom du nœud';

  @override
  String get icon_00654 => 'Config.WINS';

  @override
  String get icon_00655 => 'Serveur WINS';

  @override
  String get icon_00656 => 'Serveur DNS';

  @override
  String get icon_00656_p => 'Serveur DNS Primaire';

  @override
  String get icon_00656_s => 'Serveur DNS Secondaire';

  @override
  String get icon_00657 => 'APIPA';

  @override
  String get icon_00658 => 'Connexion proxy';

  @override
  String get icon_00659 => 'Adresse';

  @override
  String get icon_00660 => 'Port';

  @override
  String get icon_00661 => 'Nom d\'util.';

  @override
  String get icon_00662 => 'Mot Passe';

  @override
  String get icon_00663 => 'Primaire';

  @override
  String get icon_00664 => 'Secondaire';

  @override
  String get icon_00665 => 'Recherche SSID...';

  @override
  String get icon_00666 => 'SSID du point d\'accès';

  @override
  String get icon_00667 => 'Clé réseau';

  @override
  String get icon_00668 => 'Oui';

  @override
  String get icon_00669 => 'Non';

  @override
  String get icon_00670 => 'Sélection auth.';

  @override
  String get icon_00671 => 'Système ouvert';

  @override
  String get icon_00672 => 'Touche partagée';

  @override
  String get icon_00673 => 'WPA/WPA2-PSK';

  @override
  String get icon_00674 => 'Type chiffrage';

  @override
  String get icon_00674_a => 'Type chiffrage (Système ouvert)';

  @override
  String get icon_00674_c => 'Type chiffrage (WPA/WPA2-PSK)';

  @override
  String get icon_00675 => 'WEP';

  @override
  String get icon_00676 => 'AES';

  @override
  String get icon_00677 => 'TKIP';

  @override
  String get icon_00678 => 'Désactivé';

  @override
  String get icon_00679 => 'Statique';

  @override
  String get icon_00680 => 'Auto';

  @override
  String get icon_00681 => 'WPA';

  @override
  String get icon_00682 => 'Date';

  @override
  String get icon_cert_key => 'Certification normale';

  @override
  String get icon_cert_web => 'Certification de\nla machine en ligne';

  @override
  String get icon_status_t => 'État';

  @override
  String get icon_status_a1 => 'Non vérifié(e)';

  @override
  String get icon_status_a2 => 'Vérification';

  @override
  String get icon_status_a3 => 'Vérifié(e) : Déjà mis(e) à jour';

  @override
  String get icon_status_a4 => 'Nouvelle mise à jour sur le serveur';

  @override
  String get icon_status_b1 => 'Non téléchargé(e)';

  @override
  String get icon_status_b2 => 'Téléchargement';

  @override
  String get icon_status_b3 => 'Téléchargé(e)';

  @override
  String get icon_cancel_downloading => 'Annuler le téléchargement';

  @override
  String get icon_pause_downloading2 => 'Suspendre le téléchargement\nAppuyez sur la touche Reprise pour continuer le téléchargement';

  @override
  String get icon_status_c1 => 'La nouvelle mise à jour n\'a pas encore été installée.';

  @override
  String get icon_status_c2 => 'La nouvelle mise à jour est installée.';

  @override
  String get icon_app_dl_moniter => 'Télécharger l\'application de contrôle';

  @override
  String get icon_shape => 'Forme';

  @override
  String get icon_favorite => 'Favoris';

  @override
  String get icon_sash_4section => '4 sections (2 × 2)';

  @override
  String get icon_sash_1direction => 'Un seul sens';

  @override
  String get icon_sash_1dtotal => 'Toutes les pièces';

  @override
  String get icon_offset => 'Décalage';

  @override
  String get icon_startpoint => 'Point de départ';

  @override
  String get icon_endpoint => 'Point final';

  @override
  String get icon_embfootdwn => 'Pied de broderie- \nAuto Bas';

  @override
  String get icon_frame_272_272_mm => '272 × 272 mm';

  @override
  String get icon_frame_272_272_inch => '10-5/8\"× 10-5/8\"';

  @override
  String get icon_appguide_w => 'Guide de \nl\'application';

  @override
  String get icon_appguide => 'Guide de \nl\'application';

  @override
  String get icon_mobileapp => 'Application mobile';

  @override
  String get icon_app => 'Application';

  @override
  String get icon_emb1 => 'Broderie 1';

  @override
  String get icon_emb2 => 'Broderie 2';

  @override
  String get icon_00185_2 => 'Taille de cadre';

  @override
  String get icon_type => 'Type';

  @override
  String get icon_typea => 'Type A';

  @override
  String get icon_typeb => 'Type B';

  @override
  String get icon_typec => 'Type C';

  @override
  String get icon_sash_typesplit => 'Type de fractionnement';

  @override
  String get icon_mystitchmonitor => 'My Stitch Monitor';

  @override
  String get icon_mydesignsnap => 'My Design Snap';

  @override
  String get icon_mystitchmonitor_t => 'IQ Intuition Monitoring';

  @override
  String get icon_mydesignsnap_t => 'IQ Intuition Positioning';

  @override
  String get icon_actcode => 'Code d\'activation';

  @override
  String get icon_machineno => 'Numéro de la machine (No.)';

  @override
  String get icon_autodl => 'Téléchargement automatique';

  @override
  String get icon_updatemanu => 'Mettre à jour manuellement';

  @override
  String get icon_dl_updateprogram => 'Télécharger le programme de mise à jour';

  @override
  String get icon_dl_updateprogram_2 => 'Télécharger le programme de mise à\njour';

  @override
  String get icon_chk_update => 'Rechercher des mises à jour';

  @override
  String get icon_pause => 'Pause';

  @override
  String get icon_resume => 'Reprise';

  @override
  String get icon_cert_method => 'Méthode de certification';

  @override
  String get icon_latestver => 'Dernière version';

  @override
  String get icon_latestveravail => 'Dernière version disponible';

  @override
  String get icon_device_ios => 'Pour les appareils\niOS';

  @override
  String get icon_device_android => 'Pour les appareils\nAndroid™';

  @override
  String get icon_f_ios => 'Pour iOS';

  @override
  String get icon_f_android => 'Pour Android™';

  @override
  String get icon_cws_myconnection => 'CanvasWorkspace\n (Ma Connexion)';

  @override
  String get icon_step1 => 'ÉTAPE1 :';

  @override
  String get icon_step2 => 'ÉTAPE2 :';

  @override
  String get icon_step3 => 'ÉTAPE3 :';

  @override
  String get icon_step4 => 'ÉTAPE4 :';

  @override
  String get icon_step5 => 'ÉTAPE5 :';

  @override
  String get icon_register => 'S\'inscrire';

  @override
  String get icon_loginid => 'Nom d\'utilisateur :';

  @override
  String get icon_id => 'Identifiant :';

  @override
  String get icon_appq1 => 'Écusson appliqué\n (Normal)';

  @override
  String get icon_appq2 => 'Écusson appliqué\npour couleurs\nsélectionnées';

  @override
  String get icon_original_img => 'Image originale';

  @override
  String get icon_appq_stitch_1 => 'Point zigzag';

  @override
  String get icon_appq_stitch_2 => 'Point plumetis';

  @override
  String get icon_appq_stitch_3 => 'Contour piqué';

  @override
  String get icon_stamp_web => 'Contours de découpe';

  @override
  String get icon_cws_rgs_title => 'Obtenez le code PIN pour enregistrer votre machine.';

  @override
  String get icon_cws_rgs_s1 => 'Connectez-vous à CanvasWorkspace.\nhttp://CanvasWorkspace.Brother.com';

  @override
  String get icon_cws_rgs_s2 => 'Appuyez sur [Paramètres du compte].';

  @override
  String get icon_pincode => 'Code PIN';

  @override
  String get icon_kitsnc => 'ScanNCut (Ma Connexion)';

  @override
  String get icon_snc1 => 'ScanNCut';

  @override
  String get icon_f_om_kitsnc => 'ScanNCut (Ma Connexion)';

  @override
  String get icon_density_mm => 'point/mm';

  @override
  String get icon_density_inch => 'point/inch';

  @override
  String get icon_machineregist => 'Enregistrement de la machine';

  @override
  String get icon_snj_myconnection => 'Artspira / Ma Connexion';

  @override
  String get icon_snj_rgs_title => 'Obtenez le code PIN pour enregistrer votre machine.';

  @override
  String get icon_snj_rgs_s1_iivo1 => 'Connectez-vous à Artspira.\nhttps://s.brother/snjumq4211';

  @override
  String get icon_snj_rgs_s2 => 'Appuyez sur [Réglages de la machine] et appuyez sur [S\'inscrire] dans la machine à broder, puis sélectionnez [Modèle à réseau local LAN sans fil].';

  @override
  String get icon_snj_rgs_s3 => 'Saisissez le numéro suivant dans Artspira et obtenez le code PIN.';

  @override
  String get icon_snj_rgs_pin => 'Saisissez le code PIN sur l\'écran suivant.';

  @override
  String get icon_cws_rgs_s3 => 'Appuyez sur [Enregistrement de la machine] et sélectionnez [Enregistrer une nouvelle machine à coudre].';

  @override
  String get icon_cws_rgs_s4 => 'Saisissez le numéro suivant sur l\'écran du Web et obtenez le code PIN.';

  @override
  String get icon_cws_rgs_pin => 'Saisissez le code PIN sur l\'écran suivant.';

  @override
  String get icon_transfer => 'Transférer';

  @override
  String get icon_app_selectcolor => 'Couleurs sélectionnées pour l\'appliqué de pièce';

  @override
  String get icon_texture => 'Texture';

  @override
  String get icon_userthread => 'Fil utilisateur';

  @override
  String get icon_senju => 'Artspira';

  @override
  String get icon_notnow => 'Pas\nmaintenant';

  @override
  String get icon_builtin => 'Intégré';

  @override
  String get icon_user => 'Personnalisé';

  @override
  String get icon_clearall => 'Effacer tout';

  @override
  String get icon_taperingtitle => 'Biseau';

  @override
  String get icon_tapering01 => 'Début';

  @override
  String get icon_tapering02 => 'Fin';

  @override
  String get icon_tapering03 => 'Style de fin';

  @override
  String get icon_tapering03_2 => 'Style de fin';

  @override
  String get icon_tapering04 => 'Angle de départ';

  @override
  String get icon_tapering05 => 'Angle final';

  @override
  String get icon_tapering06 => 'Répétition du motif';

  @override
  String get icon_tapering06_s => 'Répétition    ';

  @override
  String get icon_times => 'fois';

  @override
  String get icon_approx_s => 'Environ';

  @override
  String get icon_e2etitle => 'Edge-To-Edge Quilt';

  @override
  String get icon_e2e01 => 'Option miroir';

  @override
  String get icon_e2e01_2 => 'Option miroir';

  @override
  String get icon_e2e02 => 'rangée(s)';

  @override
  String get icon_e2e03 => 'pièce(s)';

  @override
  String get icon_sr_title => 'Régulateur de point';

  @override
  String get icon_sr_mode_title => 'Mode';

  @override
  String get icon_sr_mode_00exp => 'Étape 1 - Sélectionnez un mode.\nÉtape 2 - Sélectionnez un point.\n  *Le point de faufilage est sélectionné automatiquement en mode 3.\nÉtape 3 - Veuillez commencer à coudre.';

  @override
  String get icon_sr_mode01exp => 'Mode intermittent\n\nEn l\'absence de mouvement du tissu, l\'aiguille s\'arrête en haut et l\'aiguille s\'abaisse après déplacement de la longueur spécifiée. Attention à ne pas mettre votre main sous l\'aiguille.';

  @override
  String get icon_sr_mode02exp => 'Mode continu\n\nEn l\'absence de mouvement du tissu, l\'aiguille s\'abaisse lentement à la même position que pour un point noué ou pour une longueur de point plus courte que celle spécifiée, comme dans les coins d\'un motif.';

  @override
  String get icon_sr_mode03exp => 'Mode de faufilage\n\nL\'aiguille s\'abaisse à intervalles plus longs pour le faufilage. Attention à ne pas mettre votre main sous l\'aiguille.';

  @override
  String get icon_sr_mode04exp => 'Mode de mouvement libre\n\nCoudre à la vitesse spécifiée';

  @override
  String get icon_sr_mem_mode01 => 'Intermittent';

  @override
  String get icon_sr_mem_mode02 => 'Continu';

  @override
  String get icon_sr_modemem_03 => 'Faufilage';

  @override
  String get icon_sr_mem_mode04 => 'Mouvement\nlibre';

  @override
  String get icon_sr_sensingline => 'Ligne de détection';

  @override
  String get icon_sr_footheight => 'Hauteur SR';

  @override
  String get icon_unselect => 'Désélectionner';

  @override
  String get icon_filter => 'Filtre';

  @override
  String get icon_filterapplied => 'Filtre appliqué';

  @override
  String get icon_apply => 'Appliquer';

  @override
  String get icon_upperlimit => 'Limite supérieure';

  @override
  String get icon_lowerlimit => 'Limite inférieure';

  @override
  String get icon_all => 'Tous';

  @override
  String get icon_bh_guide01 => 'Guide de boutonnière';

  @override
  String get icon_bh_guide02 => 'Assemblage';

  @override
  String get icon_bh_guide03 => 'Espacement';

  @override
  String get icon_bh_guide04 => 'Guide de bord de tissu';

  @override
  String get icon_bh_guide05 => 'Distance du bord';

  @override
  String get icon_colorchanges => 'Changements couleur';

  @override
  String get icon_voiceguidance_title => 'Guidage vocal';

  @override
  String get icon_voicevolume => 'Volume vocal';

  @override
  String get icon_voice_01eng_a => 'English-A';

  @override
  String get icon_voice_01eng_b => 'English-B';

  @override
  String get icon_voice_02deu_a => 'Deutsch-A';

  @override
  String get icon_voice_02deu_b => 'Deutsch-B';

  @override
  String get icon_voice_03fra_a => 'Français-A';

  @override
  String get icon_voice_03fra_b => 'Français-B';

  @override
  String get icon_voice_04ita_a => 'Italiano-A';

  @override
  String get icon_voice_04ita_b => 'Italiano-B';

  @override
  String get icon_voice_05nld_a => 'Nederlands-A';

  @override
  String get icon_voice_05nld_b => 'Nederlands-B';

  @override
  String get icon_voice_06esp_a => 'Español-A';

  @override
  String get icon_voice_06esp_b => 'Español-B';

  @override
  String get icon_voice_07jpn_a => '日本語-A';

  @override
  String get icon_voice_07jpn_b => '日本語-B';

  @override
  String get icon_embcate_photostitch => 'Générateur de broderie';

  @override
  String get icon_photos_title => 'Fonction de générateur d\'images en broderie';

  @override
  String get icon_photos_01 => 'Sélectionnez un fichier image (JPG, BMP, PNG).';

  @override
  String get icon_photos_02 => 'Réglage de la taille';

  @override
  String get icon_photos_03 => 'Élimination de l\'arrière-plan';

  @override
  String get icon_photos_04 => 'Cadrage de l’image';

  @override
  String get icon_photos_05 => 'Adapter au cadre';

  @override
  String get icon_photos_06 => 'Auto (AI)';

  @override
  String get icon_photos_07 => 'Manuelle';

  @override
  String get icon_photos_08 => 'Sélectionner un style à convertir par IA.';

  @override
  String get icon_photos_09 => 'Réglage de la couleur';

  @override
  String get icon_photos_10 => 'Bords accentués';

  @override
  String get icon_photos_11 => 'Luminosité';

  @override
  String get icon_photos_12 => 'Contraste';

  @override
  String get icon_photos_13 => 'Saturation';

  @override
  String get icon_photos_14 => 'Importation d\'un fichier image (JPG, BMP, PNG)';

  @override
  String get icon_photos_15 => 'Réglages de broderie';

  @override
  String get icon_style0 => 'Original';

  @override
  String get icon_style1 => 'Style 1';

  @override
  String get icon_style2 => 'Style 2';

  @override
  String get icon_style3 => 'Style 3';

  @override
  String get icon_style4 => 'Style 4';

  @override
  String get icon_style5 => 'Style 5';

  @override
  String get icon_style6 => 'Style 6';

  @override
  String get icon_style7 => 'Style 7';

  @override
  String get icon_style8 => 'Style 8';

  @override
  String get icon_style9 => 'Style 9';

  @override
  String get icon_style10 => 'Style 10';

  @override
  String get icon_style1_name => 'Art icône';

  @override
  String get icon_style2_name => 'Art déco';

  @override
  String get icon_style3_name => 'Dessin au crayon';

  @override
  String get icon_style4_name => 'Pastel gras';

  @override
  String get icon_style5_name => 'Enseigne néon';

  @override
  String get icon_style6_name => 'Art nouveau';

  @override
  String get icon_style7_name => 'Vif et audacieux';

  @override
  String get icon_style8_name => 'Vitraux';

  @override
  String get icon_style9_name => 'Art géo';

  @override
  String get icon_style10_name => 'Dessin animé';

  @override
  String get icon_stylusedit => 'Modification du projecteur avec le stylet';

  @override
  String get icon_projectorsettings => 'Réglages du projecteur';

  @override
  String get icon_setting_srvolume => 'Volume du buzzer SR';

  @override
  String get icon_embcate_bt_01 => 'Quilt';

  @override
  String get icon_embcate_bt_02 => 'Appliqué';

  @override
  String get icon_embcate_bt_03 => 'Flore';

  @override
  String get icon_embcate_bt_04 => 'Faune';

  @override
  String get icon_embcate_bt_05 => 'Lettres';

  @override
  String get icon_embcate_bt_06 => 'Décoration';

  @override
  String get icon_embcate_bt_07 => 'Saisons';

  @override
  String get icon_embcate_bt_08 => 'Dentelle 3D';

  @override
  String get icon_embcate_bt_09 => 'Dentelle au crochet';

  @override
  String get icon_embcate_bt_10 => 'Tout-dans-le-cadre';

  @override
  String get icon_embcate_b_01 => 'Quilt 2';

  @override
  String get icon_embcate_b_02 => 'Motifs de quilt Anna Aldmon';

  @override
  String get icon_embcate_b_03 => 'Appliqué 2';

  @override
  String get icon_embcate_b_04 => 'Flore 2';

  @override
  String get icon_embcate_b_05 => 'Roses de Pierre-Joseph Redouté';

  @override
  String get icon_embcate_b_06 => 'Motifs Zündt';

  @override
  String get icon_embcate_b_07 => 'Zentangle';

  @override
  String get icon_embcate_b_08 => 'Faune 2';

  @override
  String get icon_embcate_b_09 => 'Lettres 2';

  @override
  String get icon_embcate_b_10 => 'Sports';

  @override
  String get icon_embcate_b_11 => 'Marine';

  @override
  String get icon_embcate_b_12 => 'Alimentation ';

  @override
  String get icon_embcate_b_13 => 'Enfants';

  @override
  String get icon_embcate_b_14 => 'Décoration 2';

  @override
  String get icon_embcate_b_15 => 'Dentelle 3D 2';

  @override
  String get icon_legalinfo => 'Information légale';

  @override
  String get icon_legal_opensource => 'Open Source Licensing Remarks\n(Remarques sur les licences Open Source)';

  @override
  String get icon_legal_thirdpartysoft => 'Third-Party Software\n(Logiciels tiers)';

  @override
  String get icon_nousb => '－－－－－－';

  @override
  String get icon_randomfill => 'Remplissage aléatoire';

  @override
  String get icon_selarea => 'Sélectionnez une zone';

  @override
  String get icon_maxnumber_patt => 'Distance min.';

  @override
  String get t_err01 => 'Le système de sécurité a été activé.\nLe fil est-il emmêlé?\nL\'aiguille est-elle tordue? ';

  @override
  String get t_err02 => 'Vérifiez l\'enfilage supérieur et procédez de nouveau à l\'enfilage.';

  @override
  String get t_err02_emb => 'Vérifiez l\'enfilage supérieur et procédez de nouveau à l\'enfilage.\n\n* Appuyez sur la touche de déplacement de cadre sur l\'écran de broderie pour déplacer le cadre jusqu\'au centre.';

  @override
  String get t_err03 => 'Relevez le levier du pied-de-biche.';

  @override
  String get t_err04 => 'Abaissez le levier du pied-de-biche.';

  @override
  String get t_err05 => 'Aucune carte de broderie dans l\'emplacement prévu à cet effet.\nInsérez une carte de broderie.';

  @override
  String get t_err06 => 'Cette carte de broderie est inutilisable. Les cartes inutilisables comprennent les cartes autorisées à la vente dans d\'autres pays, sans motif de broderie, etc.';

  @override
  String get t_err07 => 'Impossible d\'ajouter d\'autres motifs à cette combinaison.';

  @override
  String get t_err07_u => 'Aucun autre point ne peut être combiné.';

  @override
  String get t_err08 => 'Cette touche ne fonctionne pas lorsque l\'unité de broderie n\'est pas installée. Mettez la machine hors tension et installez l\'unité de broderie.';

  @override
  String get t_err09 => 'Cette touche ne fonctionne pas lorsque l\'unité de broderie est installée.';

  @override
  String get t_err10 => 'Cette touche ne fonctionne pas lorsque l\'unité de broderie est installée. Mettez la machine hors tension et retirez l\'unité de broderie.';

  @override
  String get t_err11 => 'Vous ne pouvez pas utiliser la pédale lorsque l\'unité de broderie est installée.\nRetirez la pédale.';

  @override
  String get t_err_corrupteddataturnoff => 'Impossible de reconnaître les données. Les données sont peut-être corrompues.\n\nVeuillez éteindre la machine et la rallumer.';

  @override
  String get t_err12 => 'Volume de données trop important pour ce motif.';

  @override
  String get t_err13 => 'Cette touche ne fonctionne pas lorsque l\'aiguille est abaissée. Soulevez l\'aiguille et appuyez de nouveau sur la touche.';

  @override
  String get t_err15 => 'La touche \"Marche/arrêt\" ne fonctionne pas lorsque la pédale est installée.\nRetirez la pédale.';

  @override
  String get t_err16 => 'Terminez d\'éditer le motif avant la couture des points.';

  @override
  String get t_err16_e => 'Terminez de modifier les motifs avant la broderie.';

  @override
  String get t_err16_u => 'Terminez de modifier les données de point avant la couture.';

  @override
  String get t_err17 => 'Relevez le levier boutonnières.';

  @override
  String get t_err18 => 'Abaissez le levier boutonnières.';

  @override
  String get t_err19 => 'Impossible de changer la configuration des caractères.';

  @override
  String get t_err22 => 'Sélectionnez un motif.';

  @override
  String get t_err22_u => 'Sélectionnez un point.';

  @override
  String get t_err23 => 'Enregistrement…';

  @override
  String get t_err24 => 'La canette est presque vide.';

  @override
  String get t_err25 => 'Le chariot de l\'unité de broderie va se déplacer.\nÉloignez vos mains, etc. du chariot.';

  @override
  String get t_err26 => 'Suppression…';

  @override
  String get t_err27 => 'Espace mémoire insuffisant pour enregistrer le motif.\nSupprimer un autre motif?';

  @override
  String get t_err27_d => 'Espace mémoire insuffisant pour enregistrer le motif.\nSupprimer d\'autres données?';

  @override
  String get t_err61 => 'Espace mémoire insuffisant pour enregistrer le motif.';

  @override
  String get t_err61_d => 'Espace mémoire insuffisant pour enregistrer le motif.\nSupprimez certains motifs ou utilisez un autre support.';

  @override
  String get t_err61_dd => 'Espace mémoire insuffisant pour enregistrer le motif.\nSupprimez certaines données ou utilisez un autre support.';

  @override
  String get t_err28 => 'Ouverture du motif.\nVeuillez patienter.';

  @override
  String get t_err28_d => 'Ouverture du motif.\nVeuillez patienter.';

  @override
  String get t_err29 => 'Souhaitez-vous supprimer le motif sélectionné?';

  @override
  String get t_err65 => 'Souhaitez-vous supprimer le motif sélectionné?';

  @override
  String get t_err29_d => 'Souhaitez-vous supprimer les fichiers de données sélectionnés ?';

  @override
  String get t_err30 => 'Souhaitez-vous enregistrer les paramètres en cours?';

  @override
  String get t_err33 => 'Retirez le cadre à broder.';

  @override
  String get t_err34 => 'Fixez le cadre à broder.';

  @override
  String get t_err36 => 'Lorsque la commande de vitesse est réglée pour contrôler la largeur du point zigzag, la touche \"Marche/arrêt\" ne fonctionne pas.';

  @override
  String get t_err37 => 'Le système de sécurité du bobineur de canette a été activé.\nLe fil est-il emmêlé?';

  @override
  String get t_err38 => 'Cette fonction ne peut être utilisée lorsque la machine est en mode Aiguille jumelée.\nAnnulez le mode Aiguille jumelée et sélectionnez de nouveau la fonction.';

  @override
  String get t_err_twinn_10 => 'La plaque à aiguille pour point droit ne peut pas être utilisée en mode Aiguille jumelée.\nRetirez l\'aiguille jumelée et annulez le mode correspondant.';

  @override
  String get t_err_twinn_11 => 'Le mode Aiguille jumelée a été annulé.\nVeuillez retirer l\'aiguille jumelée.';

  @override
  String get t_err_twinn_12 => 'Veuillez vérifier que l\'aiguille jumelée est retirée.';

  @override
  String get t_err42 => 'Vérifiez le résultat, puis appuyez sur OK.';

  @override
  String get t_err48 => 'Impossible de reconnaître les données du motif sélectionné. Les données sont peut-être corrompues.';

  @override
  String get t_err50 => 'Placez le support de canette sur la gauche.';

  @override
  String get t_err53 => 'L\'aiguille est abaissée.\nAppuyez sur la touche \"Positionnement aiguille\" pour relever l\'aiguille.';

  @override
  String get t_err55 => 'Mettez le pied pour boutonnières « A＋ ».\nLa caméra intégrée détecte le pied pour boutonnières « A＋ » grâce au repère « A＋ » et aux trois points.';

  @override
  String get t_err56 => 'Impossible d\'identifier la taille du bouton.\nVérifiez que les trois points sont bien visibles. Sinon, indiquez les valeurs correspondant à la longueur de la fente, puis réessayez.';

  @override
  String get t_err63 => 'Cette fonction d\'édition ne peut être utilisée lorsque le motif est en dehors de la ligne rouge.\nUtilisez cette fonction après avoir déplacé le motif.';

  @override
  String get t_err64 => 'Inclut un motif spécial qui ne peut pas être enregistré dans la mémoire externe.\nEnregistrez le motif dans la mémoire de la machine.';

  @override
  String get t_err69 => 'Un des motifs inclus ne peut pas être enregistré.';

  @override
  String get t_err76 => 'Utilisez un cadre à broder plus grand.';

  @override
  String get t_err77 => 'Vous ne pouvez pas utiliser ce cadre à broder avec ce motif.\nRemplacez-le par le cadre approprié.';

  @override
  String get t_err82 => 'Souhaitez-vous revenir aux changements de couleur précédents?';

  @override
  String get t_err83 => 'Souhaitez-vous écraser les données?';

  @override
  String get t_err84 => 'Souhaitez-vous rappeler et réutiliser la mémoire précédente?';

  @override
  String get t_err88 => 'Impossible de broder car l\'unité de broderie n\'est pas installée. Mettez la machine hors tension, puis installez l\'unité de broderie.';

  @override
  String get t_err89 => 'Le support USB n\'est pas mis en place.\nPlacez-le.';

  @override
  String get t_err90 => 'Impossible d\'utiliser ce support USB.';

  @override
  String get t_err_usb_notcompati => 'Le support USB connecté n\'est pas compatible avec l\'appareil.\nVeuillez utiliser un autre support USB.';

  @override
  String get t_err93 => 'Le support USB est peut-être endommagé.\nUtilisez un autre support, puis réessayez d\'enregistrer.';

  @override
  String get t_err94 => 'Espace insuffisant.\nSupprimez certains motifs ou utilisez un autre support USB.';

  @override
  String get t_err94_cmn => 'Espace insuffisant.\nSupprimez certains motifs ou utilisez un autre support.';

  @override
  String get t_err95 => 'Le support USB a été changé. Ne le changez pas en cours de lecture.';

  @override
  String get t_err95_cmn => 'Le support a été changé.\nNe changez pas de support en cours de lecture.';

  @override
  String get t_err96 => 'Le support USB est protégé en écriture. Il est donc impossible d\'enregistrer les données. Annulez la protection en écriture avant d\'essayer de les enregistrer.';

  @override
  String get t_err96_cmn => 'Le support est protégé en écriture, il est donc impossible d\'enregistrer les données.\nAnnulez la protection en écriture avant d’essayer de les enregistrer.';

  @override
  String get t_err97 => 'Le support USB est protégé en écriture. Il est donc impossible de supprimer les données.\nAnnulez la protection en écriture avant d\'essayer de les supprimer.';

  @override
  String get t_err97_cmn => 'Le support est protégé en écriture, il est donc impossible de supprimer les données.\nAnnulez la protection en écriture avant d’essayer de les supprimer.';

  @override
  String get t_err98 => 'Erreur de support USB';

  @override
  String get t_err98_cmn => 'Erreur de support';

  @override
  String get t_err99 => 'Impossible de lire le support USB.\nLe support USB est peut-être endommagé.';

  @override
  String get t_err100 => 'Support USB en cours de formatage';

  @override
  String get t_err101 => 'Transmission par USB';

  @override
  String get t_err101_cmn => 'Accès au support en cours';

  @override
  String get t_err103 => 'Veuillez patienter.';

  @override
  String get t_err104 => 'Impossible d\'utiliser la machine lorsque les motifs dépassent du cadre bleu.';

  @override
  String get t_err106 => 'Voulez-vous broder le prochain segment?';

  @override
  String get t_err107 => 'Broderie terminée.';

  @override
  String get t_err108 => 'Les poches sont pleines.';

  @override
  String get t_err109 => 'Utilisez le releveur du pied-de-biche pour relever ce dernier.';

  @override
  String get t_err110 => 'Utilisez le releveur du pied-de-biche pour abaisser ce dernier.';

  @override
  String get t_err111 => 'Tension des fils incorrecte. Appuyez de nouveau sur la touche d\'enfilage automatique.';

  @override
  String get t_err113 => 'Le programme va être mis à jour.\nChargez-le sur la machine via USB.';

  @override
  String get t_err116 => 'Erreur de données';

  @override
  String get t_err117 => 'Erreur Flash ROM';

  @override
  String get t_err118 => 'Un dysfonctionnement s\'est produit.\nMettez la machine hors puis sous tension.';

  @override
  String get t_err119 => 'Mettez la machine hors tension avant de fixer ou de retirer la plaque d\'aiguille.';

  @override
  String get t_err120 => 'Souhaitez-vous remettre le chariot de broderie dans sa position précédente?';

  @override
  String get t_err120_2 => 'Retirez le cadre à broder et remplacez la canette.\nEnsuite, fixez le cadre et appuyez sur OK pour le remettre à sa position précédente.';

  @override
  String get t_err121 => 'Souhaitez-vous séparer le motif d\'encadrement combiné?';

  @override
  String get t_err122 => 'Ce support USB n\'est pas compatible.';

  @override
  String get t_err122_cmn => 'Ce support est incompatible.';

  @override
  String get t_err123 => 'Le support USB a été retiré ou le connecteur est débranché.';

  @override
  String get t_err124 => 'Un dysfonctionnement s\'est produit.\nMettez la machine hors puis sous tension.';

  @override
  String get t_err125 => 'L\'enfilage du fil supérieur est peut-être incorrect.\nRecommencez l\'enfilage de celui-ci.';

  @override
  String get t_err126 => 'Souhaitez-vous abaisser le pied-de-biche automatiquement?';

  @override
  String get t_err127 => 'En mode aiguille jumelée, vous ne pouvez pas utiliser la touche d\'enfilage automatique de l\'aiguille.';

  @override
  String get t_err128 => 'Veillez à ce que le cadre à broder soit positionné le plus en arrière possible. VERROUILLEZ LE LEVIER DE SÉCURITÉ DU CADRE.';

  @override
  String get t_err129 => 'Placez le support de canette sur la droite.';

  @override
  String get t_err130 => 'Le pied-de-biche effectue des mouvements de bas en haut. Maintenez vos mains éloignées du pied-de-biche.';

  @override
  String get t_err131 => 'Vous ne pouvez pas utiliser ce motif.';

  @override
  String get t_err132 => 'Avant de broder, vérifiez que le cadre est positionné le plus en arrière possible et que son levier de sécurité est abaissé. Puis appuyez sur \"Marche/arrêt\" pour broder.';

  @override
  String get t_err133 => 'Ce cadre à broder ne peut pas être utilisé.';

  @override
  String get t_err134 => 'Ce motif ne peut pas être utilisé car la limite de capacité des données est atteinte.';

  @override
  String get t_err136 => 'Une maintenance préventive est recommandée.';

  @override
  String get t_err137 => 'Une maintenance préventive est recommandée.\nDélai dépassé de 1000 heures.';

  @override
  String get t_err208 => 'Calcul en cours…';

  @override
  String get t_err209 => 'Le chariot de l\'unité de broderie va se déplacer.';

  @override
  String get t_err210 => 'Reconnaissance en cours…';

  @override
  String get t_err213 => 'Échec de reconnaissance du repère de positionnement de broderie.';

  @override
  String get t_err215 => 'Retirez le repère de positionnement de broderie.';

  @override
  String get t_err224 => 'Poussière ou tache sur le papier blanc/le tissu. Remplacez par un papier blanc/tissu propre.';

  @override
  String get t_err227 => 'Echec du réglage/de la reconnaissance.';

  @override
  String get t_err228 => 'Impossible d\'utiliser ce fichier car il dépasse les capacités de données.\nUtilisez un fichier de taille adaptée.';

  @override
  String get t_err229 => 'Impossible d\'utiliser ce fichier.';

  @override
  String get t_err229_b => 'Impossible de lire la version de ce fichier.';

  @override
  String get t_err239 => 'Connecté à un PC.\nNe déconnectez pas le câble USB.';

  @override
  String get t_err241 => 'Échec de lecture du fichier.';

  @override
  String get t_err242 => 'Échec de sauvegarde du fichier.';

  @override
  String get t_err244 => 'Supprimer l\'image sélectionnée? ';

  @override
  String get t_err245 => 'Touche non disponible actuellement.';

  @override
  String get t_err246 => 'Le motif s\'étend en dehors de la zone de motif.\nChangez la position du motif et numérisez la nouvelle zone.';

  @override
  String get t_err247 => 'Espace mémoire insuffisant pour l\'enregistrement.';

  @override
  String get t_err248 => 'Souhaitez-vous supprimer le réglage?';

  @override
  String get t_err249 => 'Bord du tissu non détecté.';

  @override
  String get t_err250 => 'Impossible de convertir ce motif.';

  @override
  String get t_err251 => 'OK pour rétablir la taille et la position de la broderie?';

  @override
  String get t_err252 => 'OK pour rétablir la taille de la broderie?';

  @override
  String get t_err253 => 'Détectez l\'épaisseur du tissu.\nApposez la vignette de positionnement sur la ligne rouge. ';

  @override
  String get t_err254 => 'Détection réussie.\nSupprimez le repère de positionnement de la broderie. Appuyez sur OK pour lancer la capture de l\'arrière-plan.';

  @override
  String get t_err255 => 'Appuyez sur OK. Le cadre à broder va se déplacer et lancer la capture de l\'arrière-plan.';

  @override
  String get t_err256 => 'Détection échouée.\nVoulez-vous recommencer?';

  @override
  String get t_err257 => 'Certification réussie.\nRedémarrez la machine.';

  @override
  String get t_err257_1 => 'Certification réussie.\nRedémarrez la machine à coudre.';

  @override
  String get t_err257_2 => 'Certification réussie.\nRedémarrez la machine.';

  @override
  String get t_err259 => 'Certification du kit de mise à niveau.\n\nAppuyez sur le n° du kit à certifier.';

  @override
  String get t_err260 => 'Entrez le code de certification, puis appuyez sur [REGLER].';

  @override
  String get t_err261 => 'Certification en cours…';

  @override
  String get t_err262 => 'Le code de certification est incorrect.\nVérifiez le code, puis entrez-le de nouveau.';

  @override
  String get t_err263 => 'KIT';

  @override
  String get t_err264 => 'D\'accord pour supprimer l\'image de fond?';

  @override
  String get t_err265 => 'D\'accord pour réinitialiser le motif à sa taille, son angle et sa position d\'origine?';

  @override
  String get t_err343 => 'Voulez-vous rétablir l\'angle et/ou la position d\'origine?';

  @override
  String get t_err266 => 'Kit de mise à niveau du certificat.';

  @override
  String get t_err270 => 'Apposez la première marque de positionnement de broderie fermement sur le tissu de sorte à ce que la marque se trouve à l\'intérieur du cadre rouge. Le chariot de l\'unité de broderie se déplace après avoir appuyé sur la touche de numérisation.';

  @override
  String get t_err271 => 'Apposez la seconde marque de positionnement de broderie fermement sur le tissu de sorte à ce que la marque se trouve à l\'intérieur du cadre rouge. Le chariot de l\'unité de broderie se déplace après avoir appuyé sur la touche de numérisation.';

  @override
  String get t_err273 => 'Le repère de positionnement de la broderie n\'est pas fixé correctement.\nRetirez-le et fixez-le de nouveau.';

  @override
  String get t_err274 => 'Les repères de positionnement ont été détectés.\nLaissez les repères de positionnement de la broderie fixés et déplacez le tissu dans le cadre. Positionnez le centre des repères de positionnement  dans la zone de broderie, puis sélectionnez un motif.';

  @override
  String get t_err276 => 'Les repères de positionnement de la broderie ont été détectés.\nRetirez-les.';

  @override
  String get t_err277 => 'Voulez-vous vraiment \"Annuler\" la liaison de motifs?';

  @override
  String get t_err278 => 'Le motif de la section suivante ne peut pas être brodé si vous quittez. Êtes-vous sûr de vouloir terminer la connexion du motif?';

  @override
  String get t_err279 => 'La broderie est terminée.\nOK pour relier le motif suivant?\n\n* Ne retirez pas le tissu du cadre.\n* Si vous souhaitez continuer plus tard, sélectionnez et définissez la section suivante du motif. Elle pourra être reprise si la machine l\'a lue.';

  @override
  String get t_err282 => 'Impossible d\'insérer plus de données.';

  @override
  String get t_err283 => 'Cette application sera fermée.';

  @override
  String get t_err284 => 'Ces données sont trop compliquées et ne peuvent pas étre converties.';

  @override
  String get t_err286 => 'Fin d\'édition de la ligne OK?';

  @override
  String get t_err288 => 'Les repères de positionnement de la broderie sont détectés. Retirez-les et refixez-les sur de nouveaux emplacements.';

  @override
  String get t_err290 => 'Impossible de détecter les repères de positionnement de la broderie.\nRetirez-les et fixez-les de nouveau.\nPositionnez le centre des repères de positionnement de la broderie dans la zone de broderie.';

  @override
  String get t_err291 => 'Espace insuffisant. Utilisez un support USB différent.';

  @override
  String get t_err291_cmn => 'Espace insuffisant.\nUtilisez un autre support.';

  @override
  String get t_err292 => 'Les couleurs sont insuffisantes dans le tableau de fils pour le mode sélectionné.';

  @override
  String get t_err292_s => 'Le nombre de couleurs est insuffisant dans la palette actuelle pour le mode sélectionné.';

  @override
  String get t_err297 => 'Répétez les étapes 3 et 4 du processus.';

  @override
  String get t_err298 => 'La vignette est sale.';

  @override
  String get t_err_cameracalib_ng_msg => 'Reconnaissance incorrecte.\nApposez un nouvel autocollant blanc.';

  @override
  String get t_err_cameracalib_ok_msg => 'Appuyez sur la touche OK pour mémoriser la position de chute de l\'aiguille.';

  @override
  String get t_err299 => 'Si après plusieurs essais, le réglage n\'est toujours pas correct, contactez votre revendeur le plus proche.';

  @override
  String get t_err300 => 'Au besoin, reportez-vous au manuel d\'instructions et au guide de référence pour des informations sur l\'aiguille recommandée.';

  @override
  String get t_err_cameracalib_title => 'Calibrage d\'aiguille pour la caméra/le projecteur';

  @override
  String get t_err_cameracalib_1_4 => '1. Appuyez sur la touche \"Positionnement aiguille\" pour\n relever l\'aiguille.\n2. Après avoir enlevé l\'aiguille et le pied-de-biche,\n apposez l\'autocollant blanc sur le point de chute\n de l\'aiguille.\n3. Insérez l\'aiguille (taille standard 75/11 ou 90/14).\n4. Appuyez sur DEMARRER pour lancer le processus de\n calibrage.\nPour des raisons de sécurité, vérifiez que la zone\n entourant l\'aiguille est dégagée avant d\'appuyer\n sur \"DEMARRER\".\n\n* Ne placez pas vos mains ni d’autres éléments\n près de l’aiguille, sinon vous risqueriez de vous blesser.';

  @override
  String get t_err303 => 'La broderie est terminée.\nOK pour relier le motif suivant?';

  @override
  String get t_err304 => 'Ne retirez pas le tissu du cadre.\nAppuyez sur OK pour sélectionner le motif suivant.';

  @override
  String get t_err307 => 'Ne retirez pas les repères de positionnement de la broderie.\nReplacez le tissu de façon à ce que le prochain motif et le centre des deux repères de positionnement de la broderie se situent dans la zone de broderie.';

  @override
  String get t_err308 => 'Le motif suivant dépasse de la zone de broderie.\nReplacez le tissu de façon à ce que le prochain motif et le centre des deux repères de positionnement de la broderie se situent dans la zone de broderie.';

  @override
  String get t_err309 => 'Impossible de détecter le repère de positionnement de la broderie.\nReplacez le tissu de façon à ce que le prochain motif et le centre des deux repères de positionnement de la broderie se situent dans la zone de broderie.';

  @override
  String get t_err310 => 'Le positionnement des repères de la broderie a été modifié.\nReplacez le tissu de façon à ce que le prochain motif et le centre des deux repères de positionnement de la broderie se situent dans la zone de broderie.';

  @override
  String get t_err311 => 'Les repères de positionnement de la broderie sont détectés.\nRetirez-les et brodez le motif.';

  @override
  String get t_err311_size => 'Les repères de positionnement de la broderie sont détectés.\nRetirez-les et brodez le motif.\n\n* La taille du motif suivant a subi un réglage fin automatiquement car la distance entre les marques a changé pendant la remise en place du tissu dans le cadre.';

  @override
  String get t_err311_rehoop => 'La distance entre les marques est hors position suite à la remise en place du tissu dans le cadre.\nVeuillez remettre le tissu en place dans le cadre de sorte que la distance entre les marques soit de la longueur suivante.';

  @override
  String get t_err312 => 'Ne retirez pas les repères de positionnement de la broderie.\nVous devez les refixer.\nReplacez le tissu.';

  @override
  String get t_err313 => 'Impossible de détecter le repère de positionnement de la broderie.\nReplacez le tissu.';

  @override
  String get t_err314 => 'Les repères de positionnement de la broderie sont détectés.\nRetirez-les.';

  @override
  String get t_err354 => 'Le mode de prise en charge de l\'arrêt a été activé.\nMettez la machine hors tension.';

  @override
  String get t_err356 => 'Ce point n\'est pas compatible avec le mode à double entraînement.';

  @override
  String get t_err359 => 'Retirez le pied à double entraînement de la machine.';

  @override
  String get t_err360 => 'Réglez l\'heure.';

  @override
  String get t_err361 => 'Sélectionnez votre langue.';

  @override
  String get t_err362 => 'Retirez le pied de broderie avec pointeur lumineux de la machine.';

  @override
  String get t_err364 => 'Erreur du module';

  @override
  String get t_err368 => 'Voulez-vous rétablir les paramètres d\'encadrement du motif, sa position et/ou l\'angle?';

  @override
  String get t_err373 => 'Le cadre à broder a été changé, remplacez-le par le cadre d\'origine.';

  @override
  String get t_err380 => 'Pour enfiler l\'aiguille, retirez le tissu du dessous du pied-de-biche.';

  @override
  String get t_err381 => 'Voulez-vous annuler le réglage du point final?';

  @override
  String get t_err382 => 'Le mode de réglage du point final de la couture n\'est pas disponible pour le point sélectionné. \nSélectionnez un autre point, ou modifiez la longueur du point.';

  @override
  String get t_err383 => 'Après avoir supprimé la vignette du point final, continuez à coudre.';

  @override
  String get t_err384 => 'Le mode de réglage du point final n\'a pas pu être utilisé. \nLe réglage du point final sera supprimé.';

  @override
  String get t_err385 => 'Cette distance est trop courte pour utiliser le paramètre de point final.\nVous pouvez l\'utiliser si la distance est plus longue ou si le paramètre « Arrêt temporaire » est réglé sur OFF.';

  @override
  String get t_err386 => 'Cette fonction ne peut pas être utilisée tant que le Réglage du point final est activé.';

  @override
  String get t_err390 => 'Souhaitez-vous effacer toutes les données modifiées et revenir à l\'écran d\'accueil?';

  @override
  String get t_err390_old => 'Souhaitez-vous supprimer tous les motifs et revenir à l\'écran d\'accueil?';

  @override
  String get t_err391 => 'Voulez-vous annuler la sélection du motif en \ncours ?';

  @override
  String get t_err391_u => 'Voulez-vous annuler la sélection du point en cours?';

  @override
  String get t_err392 => 'Transmission par carte de broderie.';

  @override
  String get t_err393 => 'Ce motif ne peut pas être combiné.';

  @override
  String get t_err394 => 'Mémoire disponible insuffisante pour enregistrer le motif. \nSupprimer un motif enregistré?';

  @override
  String get t_err395 => 'Ce motif ne peut pas être chargé, car il dépasse de la zone modifiable.';

  @override
  String get t_err396 => 'Ce motif ne peut pas être chargé, car il dépasse de la zone modifiable. \nÀ l’aide des curseurs, déplacez le point pour qu’il se trouve dans la zone modifiable.';

  @override
  String get t_err397 => 'Voulez-vous supprimer le point en cours?';

  @override
  String get t_err398 => 'Enregistrer en tant que nouveau fichier…';

  @override
  String get t_err400 => 'Le motif dépasse le cadre à broder.\nSi vous souhaitez ajouter d\'autres motifs, faites pivoter la combinaison de motifs.';

  @override
  String get t_err401 => 'Le motif dépasse du cadre à broder.';

  @override
  String get t_err402 => 'Le motif dépasse du cadre à broder.\nN’ajoutez aucun autre caractère.';

  @override
  String get t_err403 => 'Le motif dépasse du cadre à broder.\nCette fonction n’est pas disponible actuellement.';

  @override
  String get t_err404 => 'Impossible de changer la police étant donné que certaines lettres ne sont pas incluses dans la police sélectionnée.';

  @override
  String get t_err405 => 'La zone du motif sélectionnée dépasse du cadre à broder.';

  @override
  String get t_err406 => 'Le motif dépasse du cadre à broder sélectionné. \nVoulez-vous annuler le motif sélectionné?';

  @override
  String get t_err408 => 'Vous ne pouvez pas utiliser cette fonction lorsque des motifs se chevauchent.';

  @override
  String get t_err410 => 'Le motif peut être brodé avec son centre ou un angle aligné sur le repère de positionnement de broderie. Placez le repère de positionnement de broderie à l’emplacement de votre choix.';

  @override
  String get t_err411 => 'Une fois les préparatifs terminés, appuyez sur la touche [Scan].';

  @override
  String get t_err412 => 'Impossible de trouver le repère de positionnement de broderie dans la zone de détection.';

  @override
  String get t_err413 => 'Utilisez le repère de positionnement de broderie pour relier les motifs.';

  @override
  String get t_err414 => 'Sélectionnez l’emplacement de liaison\ndu motif suivant.';

  @override
  String get t_err415 => 'Impossible de lire les données.';

  @override
  String get t_err416 => 'Les données ont été enregistrées.\nNom du fichier:';

  @override
  String get t_err417 => 'Les données sont en cours de lecture.\nVeuillez patienter.';

  @override
  String get t_err418 => 'Ce type de fichier ne peut pas être utilisé.';

  @override
  String get t_err419 => 'Ce fichier ne peut pas être utilisé, car il est trop volumineux.';

  @override
  String get t_err420 => 'Impossible de tracer l\'image. ';

  @override
  String get t_err421 => 'Le nombre de couleurs de l\'image sera réduit au nombre spécifié ici, puis le contour sera extrait.';

  @override
  String get t_err422 => 'Placez le papier avec l’illustration ou les lignes de tracé sur le cadre de numérisation.';

  @override
  String get t_err423 => 'Impossible d’utiliser un cadre à broder. Veillez à utiliser le cadre de numérisation.';

  @override
  String get t_err424 => 'La détection peut prendre quelques minutes.';

  @override
  String get t_err425 => 'Voulez-vous passer à l’écran « Mon centre de motifs » ?';

  @override
  String get t_err426 => 'Voulez-vous passer à l’écran « IQ Designer » ?';

  @override
  String get t_err428 => 'Les données d’image créées dans « Mon centre de motifs » ne seront pas enregistrées. Voulez-vous continuer?';

  @override
  String get t_err429 => 'Les données « IQ Designer » ne seront pas enregistrées. Voulez-vous continuer?';

  @override
  String get t_err430 => 'Impossible d’utiliser le cadre de numérisation pour la broderie.\nRemplacez-le par le cadre à broder.';

  @override
  String get t_err432 => 'Fixez le cadre contenant l’image à numériser sur la machine.';

  @override
  String get t_err433 => 'Lorsque vous convertissez une image en tracés\nd’images ou en images de remplissage,\nutilisez le cadre de numérisation pour récupérer\nles informations relatives aux couleurs de fil appropriées.';

  @override
  String get t_err440 => 'Sélectionnez le fichier d\'image.';

  @override
  String get t_err445 => 'Impossible d\'utiliser ce fichier image.';

  @override
  String get t_err446 => 'Numérisation…';

  @override
  String get t_err447 => 'Reconnaissance en cours…';

  @override
  String get t_err448 => 'Traitement en cours…';

  @override
  String get t_err451 => 'Souhaitez-vous effacer toutes les données d’image éditées?';

  @override
  String get t_err452 => 'Les formats d\'image pouvant être utilisés sont les fichiers JPG, PNG ou BMP de moins de 5Mo, 1,2 million de pixels.';

  @override
  String get t_err453 => 'Voulez-vous réinitialiser les paramètres à leurs valeurs par défaut sur cette page?';

  @override
  String get t_err454 => 'La combinaison de motifs est trop grande pour le cadre à broderidentifié. Si vous souhaitez ajouter d’autres motifs, faites pivoter la combinaison de motifs.';

  @override
  String get t_err455 => 'La combinaison de motifs est trop grande pour le cadre à broder identifié.';

  @override
  String get t_err457 => 'Définissez la vue d’identification du cadre à broder sur OFF.';

  @override
  String get t_err458 => 'Importation d’un fichier image.';

  @override
  String get t_err459 => 'Impossible d’utiliser cette unité de broderie.';

  @override
  String get t_err460 => 'Les motifs affichés dans la zone d’image peuvent être convertis.';

  @override
  String get t_err461 => 'Le cadre va se déplacer pour être numérisé à l’aide de la caméra intégrée.';

  @override
  String get t_err462_pp => 'Impossible d’utiliser ce fichier image.\nLes fichiers d\'image pouvant être utilisés sont des fichiers JPG, PNG ou BMP de moins de 6 Mo et jusqu\'à 16 millions de pixels.';

  @override
  String get t_err463 => 'Retirez le cadre à broder ou le cadre de numérisation.';

  @override
  String get t_err464 => 'Le motif dépasse de la zone de broderie et ne peut pas être converti.';

  @override
  String get t_err465 => 'Conversion en motif de broderie. Mon centre de motifs va se fermer.\nVoulez-vous passer à l’écran de modification de la broderie?';

  @override
  String get t_err466 => 'Voulez-vous quitter Mon centre de motifs?';

  @override
  String get t_err467 => 'Impossible d’utiliser cette fonction avec le mode d’association de motifs.';

  @override
  String get t_err468 => 'Carte circuit imprimé désactivée';

  @override
  String get t_err469 => 'Conversion en motif de broderie. IQ Designer va se fermer. Voulez-vous passer à l’écran de modification de la broderie?';

  @override
  String get t_err470 => 'Voulez-vous quitter IQ Designer?';

  @override
  String get t_err471 => 'Souhaitez-vous supprimer les données sélectionnées?';

  @override
  String get t_err472 => 'Sélectionnez plusieurs motifs.';

  @override
  String get t_err473 => 'Le(s) motif(s) disposé(s) ont été enregistrés sous la forme d’image(s).';

  @override
  String get t_err474 => 'Le contour du ou des motifs disposés a été enregistré.';

  @override
  String get t_err475 => 'Spécifier la distance de décalage autour du motif.';

  @override
  String get t_err478 => 'Rappel depuis la liste de motifs d\'estampille Mon Centre de Motifs (My Design Center).';

  @override
  String get t_err478_tc => 'Rappel depuis la liste de motifs d\'estampille \"IQ Designer\".';

  @override
  String get t_err479 => 'Il est impossible de combiner un motif de travail à la canette à un motif d’une autre catégorie.';

  @override
  String get t_err480 => 'Remet l’aiguille dans sa position initiale.';

  @override
  String get t_err481 => 'La couture d’un motif de travail à la canette est terminée.';

  @override
  String get t_err482 => 'La couture de tous les motifs de travail à la canette est terminée.';

  @override
  String get t_err483 => 'Coupez les fils.';

  @override
  String get t_err484_old => 'Avant de broder les motifs suivants, vérifiez la quantité et le type de fil de la canette installé.';

  @override
  String get t_err484 => 'Remplacez le fil de la canette, puis installez le cadre à broder.\nLe chariot de l\'unité de broderie se déplace après avoir appuyé sur OK.';

  @override
  String get t_err485 => 'Le motif de travail à la canette suivant sera brodé.';

  @override
  String get t_err486 => 'Tournez le volant pour abaisser l’aiguille dans le tissu, puis sortez le fil de la canette.';

  @override
  String get t_err489 => 'Aucune donnée à convertir.';

  @override
  String get t_err496 => 'Voulez-vous appliquer ce réglage à toutes les zones?';

  @override
  String get t_err497 => 'Contour';

  @override
  String get t_err501 => 'Les réglages ne peuvent pas être appliqués. Mémoire disponible insuffisante pour enregistrer les caractéristiques.';

  @override
  String get t_err502 => 'La distance entre les motifs sélectionnés est trop longue.';

  @override
  String get t_err503 => 'La forme est trop complexe pour la ligne de l\'appliqué.';

  @override
  String get t_err503_new => 'La forme est trop complexe ou inadaptée pour la ligne de l\'appliqué.\nModifiez les réglages d\'appliqué ou sélectionnez un motif différent.\n* Le résultat peut varier en fonction de la position et de l\'angle de direction.';

  @override
  String get t_err504 => 'Le format est trop important pour ajouter la ligne de l\'appliqué.';

  @override
  String get t_err509 => 'Une partie de la texture ne peut pas être affichée à cause de la structure des données.';

  @override
  String get t_err505 => 'Vous ne pouvez pas utiliser cette fonction pendant le tri des couleurs.';

  @override
  String get t_err506 => 'Voulez-vous supprimer le repère de fil ?';

  @override
  String get t_err508 => 'La fonction de repère de fil ne peut pas être utilisée quand vous désélectionnez la dernière zone de couleur.\nVoulez-vous supprimer le repère de fil ?';

  @override
  String get t_err507 => 'Auto';

  @override
  String get t_err510 => 'Utiliser l\'image actuelle?';

  @override
  String get t_err511 => 'Les données ont été enregistrées dans la mémoire de la machine.\nBroder les données?';

  @override
  String get t_err515 => 'Éteignez la machine pour laisser refroidir le projecteur intégré.';

  @override
  String get t_err516 => 'Ce point ne peut pas être utilisé avec la plaque à aiguille actuelle.';

  @override
  String get t_err517 => 'Changez de plaque à aiguille pour coudre ce point.';

  @override
  String get t_err518 => 'Le cadre à broder a été changé. Le chariot de l\'unité de broderie se déplace.\n';

  @override
  String get t_err519 => 'Le projecteur sera désactivé.';

  @override
  String get t_err520 => 'Éloignez vos mains, etc. du chariot.';

  @override
  String get t_err_521 => 'Il est possible de télécharger des didacticiels vidéos.';

  @override
  String get t_err574 => 'Un dysfonctionnement s\'est produit.\n Accès à l\'EEPROM impossible.';

  @override
  String get t_err575 => 'La canette est presque vide.\n\n* Appuyez sur la touche « Point de renfort » pour coudre une succession de points uniques ainsi que des points de maintien.\n* Appuyez sur la touche de déplacement du cadre pour déplacer le cadre à broder et pouvoir le retirer ou le fixer. Ensuite, le chariot revient dans sa position d\'origine.';

  @override
  String get t_err577 => 'Il est impossible de combiner un motif de points couchés à un motif d\'une autre catégorie.';

  @override
  String get t_err578 => 'Aucune autre combinaison de couleurs ne peut être sélectionnée en tant que favori.';

  @override
  String get t_err581 => 'Commencez la broderie par le coin supérieur droit du tissu.\nFixez le cadre à broder dans la position initiale de broderie.';

  @override
  String get t_err581_b => 'Commencez à broder depuis le coin supérieur gauche du tissu.\n Fixez le cadre à broder dans la position initiale de broderie.';

  @override
  String get t_err582 => 'Un côté a été brodé. Tournez le tissu à 90° dans le sens inverse des aiguilles d\'une montre, puis replacez-le dans le cadre à broder.';

  @override
  String get t_err582_n => 'Un côté a été cousu. Tournez le tissu dans le sens inverse des aiguilles d\'une montre et remettez le tissu en place pour le coin suivant.';

  @override
  String get t_err582_e => 'Une ligne est terminée. Pour démarrer la ligne suivante, remettez le tissu en place dans le cadre à broder en fonction du bord gauche de la ligne suivante, tout en incluant le repère de fil du motif supérieur dans le cadre à broder.';

  @override
  String get t_err583 => 'À l\'aide des touches de déplacement de motif, réglez le coin intérieur du motif.';

  @override
  String get t_err583_e => 'Utilisez les touches de déplacement du motif pour aligner le coin supérieur gauche de la zone du motif avec le coin supérieur gauche (repère) de la zone à broder.';

  @override
  String get t_err584 => 'À l\'aide des touches de déplacement de motif, alignez le point de départ et le point final du motif précédent.';

  @override
  String get t_err584_e => 'Utilisez les touches de déplacement du motif pour aligner le coin supérieur gauche de la zone du motif avec le repère de fil inférieur gauche du motif supérieur.';

  @override
  String get t_err585 => 'À l\'aide des touches de rotation, réglez l\'angle du motif tout en surveillant les points autour du motif.';

  @override
  String get t_err586 => 'Réglez la taille du motif de sorte que le point inférieur gauche corresponde au coin intérieur du motif suivant.';

  @override
  String get t_err586_b => 'Réglez la taille du motif de sorte que le point inférieur droit corresponde au coin intérieur du motif suivant.';

  @override
  String get t_err586_e => 'Utilisez les touches de pivotement et les touches de taille pour régler l\'angle et la taille du motif tout en surveillant les bords du motif.';

  @override
  String get t_err587 => 'À l\'aide des touches de rotation et des touches de taille, alignez le point final sur le point de départ du premier motif.';

  @override
  String get t_err588 => 'Replacez le tissu.';

  @override
  String get t_err588_e => 'Veuillez remettre en place le tissu sur le cadre à broder du côté droit, en incluant le bord droit du motif sur la gauche du cadre à broder.';

  @override
  String get t_err588_e_2 => 'Remettez en place dans le cadre à broder vers la droite, tout en incluant le bord droit du motif sur la droite et le repère de fil du motif en haut.';

  @override
  String get t_err590 => 'Appuyez sur CHARGER pour installer le fichier de mise à jour.';

  @override
  String get t_err591 => 'Une nouvelle mise à jour est disponible. Pour installer la mise à jour, mettez la machine hors tension, puis maintenez la touche « Enfilage automatique » enfoncée et remettez la machine sous tension.';

  @override
  String get t_err_dl_updateprogram2 => 'Le nouveau programme de mise à jour est prêt.\nPour installer la mise à jour, mettez la machine hors tension, puis maintenez la touche « Enfilage automatique » enfoncée et remettez la machine sous tension.';

  @override
  String get t_err592 => 'Appuyez sur CHARGER pour enregistrer le fichier de mise à jour.';

  @override
  String get t_err593 => 'Sélectionnez le périphérique sur lequel le fichier de mise à jour sera enregistré.';

  @override
  String get t_err594 => 'Sélectionnez le périphérique sur lequel le fichier de mise à jour est enregistré.';

  @override
  String get t_err_dl_updateprogram => 'Appuyez sur la touche Démarrer pour télécharger le programme de mise à jour.';

  @override
  String get t_err_dl_fail => 'Échec du téléchargement : le stockage interne est plein.';

  @override
  String get t_err_networkconnectionerr => 'Connexion au réseau perdue.\nVérifiez que la machine est connectée à un réseau sans fil.';

  @override
  String get t_err_not_turnoff => 'Ne mettez pas la machine hors tension.';

  @override
  String get t_err_pressresume_continuedl => 'Appuyez sur la touche Reprise pour continuer le téléchargement.';

  @override
  String get t_err_updateformovie => 'Veuillez mettre à jour de nouveau pour installer les vidéos.';

  @override
  String get t_err595 => 'Saisissez le code d\'activation à 16 chiffres.';

  @override
  String get t_err596 => 'Saisissez le code d\'activation à 16 chiffres, puis appuyez sur la touche [Régler].';

  @override
  String get t_err597 => 'Le numéro de la machine et le code d\'activation seront envoyés au serveur.';

  @override
  String get t_err598 => 'La « certification de la machine en ligne » est recommandée pour les machines connectées au réseau local LAN sans fil.';

  @override
  String get t_err599 => 'Le code d\'activation est incorrect.\nVérifiez-le, puis saisissez-le à nouveau.';

  @override
  String get t_err599_used => 'Le code entré a déjà été enregistré pour une autre machine.';

  @override
  String get t_err601 => 'Activer le réseau local sans fil?';

  @override
  String get t_err602 => 'Recherche SSID…';

  @override
  String get t_err603 => 'Appliquer réglage?';

  @override
  String get t_err604 => 'Connecté au réseau local sans fil.';

  @override
  String get t_err605 => 'Connexion au réseau local sans fil.';

  @override
  String get t_err606 => 'Non connecté au réseau.\nVérifiez le paramètre de réseau local sans fil.';

  @override
  String get t_err607 => 'Échec de la connexion au serveur.\nVérifiez les param. réseau.';

  @override
  String get t_err608 => 'Échec de l\'authentification lors de connexion serveur.\nVérif. param. serveur proxy.';

  @override
  String get t_err609 => 'Une erreur réseau s\'est produite.';

  @override
  String get t_err611 => 'Désactivé';

  @override
  String get t_err612 => 'Des erreurs se sont produites au niveau de la fonction réseau.';

  @override
  String get t_err613 => 'Les données n\'ont pas pu être importées.\nRecommencez.';

  @override
  String get t_err614 => 'Des informations de point d\'accès sont enregistrées.\nSouhaitez-vous vous connecter à l\'aide de ces informations?';

  @override
  String get t_err615 => 'Erreur de connexion 01';

  @override
  String get t_err616 => 'Mauvaise clé réseau.';

  @override
  String get t_err617 => 'Mauvaise clé réseau.';

  @override
  String get t_err618 => 'Réinitialiser réseau?';

  @override
  String get t_err620 => 'Mettez la machine hors puis sous tension. ';

  @override
  String get t_err621 => 'Échec de l\'authentification lors de connexion serveur.\nVérif. param. serveur proxy.';

  @override
  String get t_err622 => 'Echec de l\'authentification.\nVérifiez nom utilisateur ou MdP.';

  @override
  String get t_err623 => 'Annulation';

  @override
  String get t_err624 => 'Erreur de communication';

  @override
  String get t_err625 => 'Terminé';

  @override
  String get t_err626 => 'Confirmez l\'interface';

  @override
  String get t_err627 => 'Connexion NR';

  @override
  String get t_err628 => 'Échec de la connexion au serveur.\nVérifiez les param. réseau.';

  @override
  String get t_err629 => 'Échec de la connexion au serveur.\nRéessayez ultérieurement.';

  @override
  String get t_err630 => 'Téléchargement.\nPatientez.';

  @override
  String get t_err631 => 'Une erreur s\'est produite lors du téléchargement.\nRecommencez.';

  @override
  String get t_err632 => 'Aucun point d\'accès.';

  @override
  String get t_err633 => 'Pas de données!';

  @override
  String get t_err634 => 'Voir le chapitre Dépannage du Guide de l\'utilisateur.';

  @override
  String get t_err636 => 'Serveur introuvable, vérifiez le nom ou l\'adresse ou entrez un autre serveur LDAP.';

  @override
  String get t_err637 => 'Délai d\'attente du serveur expiré.\nRéessayez ultérieurement.';

  @override
  String get t_err638 => 'Transfert en cours…';

  @override
  String get t_err697 => 'Aucun transfert de données n\'est disponible via le câble USB.';

  @override
  String get t_err84_mdc => 'Souhaitez-vous rappeler et réutiliser la mémoire précédente?\n(Mon centre de motifs).';

  @override
  String get t_err84_iqd => 'Souhaitez-vous rappeler et réutiliser la mémoire précédente?\n(IQ Designer).';

  @override
  String get t_err703_b => 'Installez \"My Stitch Monitor\" pour contrôler la broderie.';

  @override
  String get t_err703_t => 'Installez \"IQ Intuition Monitoring\" pour contrôler la broderie.';

  @override
  String get t_err704_b => 'Installez l\'application « My Stitch Monitor » pour contrôler la broderie depuis votre appareil mobile (smartphone, tablette).\n\nVous pouvez contrôler l\'état d\'avancement de votre broderie sur votre appareil mobile.\nVous pouvez également vérifier les informations relatives à la couleur de tous les fils utilisés pour la broderie.';

  @override
  String get t_err704_t => 'Installez l\'application « IQ Intuition Monitoring » pour contrôler la broderie depuis votre appareil mobile (smartphone, tablette).\n\nVous pouvez contrôler l\'état d\'avancement de votre broderie sur votre appareil mobile.\nVous pouvez également vérifier les informations relatives à la couleur de tous les fils utilisés pour la broderie.';

  @override
  String get t_err705_b => 'Installez l\'application « My Design Snap » pour envoyer des images à votre machine depuis votre appareil mobile (smartphone, tablette).\n\nVous pouvez facilement créer des motifs de broderie à partir de ces images dans Mon Centre de Motifs.';

  @override
  String get t_err705_t => 'Installez l\'application « IQ Intuition Positioning » pour envoyer des images à votre machine depuis votre appareil mobile (smartphone, tablette).\n\nVous pouvez facilement créer des motifs de broderie à partir de ces images dans \"IQ Designer\".';

  @override
  String get t_err708 => 'KIT1 est nécessaire pour utiliser cette application.';

  @override
  String get t_err709 => 'KIT2 est nécessaire pour utiliser cette application.';

  @override
  String get t_err711 => 'Ces données de broderie n\'incluent pas suffisamment d\'informations d\'enfilage.\nPour afficher les informations d\'enfilage correctes, veuillez entrer le numéro de couleur de fil dans l\'écran de modification de la couleur de fil.';

  @override
  String get t_err713 => 'Veuillez consulter le Contrat de licence de l\'utilisateur final (EULA) avant d\'utiliser la machine.';

  @override
  String get t_err715 => 'J\'accepte les termes du contrat de licence.';

  @override
  String get t_err01_heated => 'Le système de sécurité a été activé, car le moteur de l\'axe principal a surchauffé. Le fil est-il emmêlé?';

  @override
  String get t_err01_motor => 'Le système de sécurité a été activé, car le moteur de l\'axe principal est grippé. Le fil est-il emmêlé?';

  @override
  String get t_err01_npsensor => 'Le système de sécurité a été activé en raison d\'un dysfonctionnement du capteur de position de l\'aiguille.';

  @override
  String get t_err734 => 'Pour iOS';

  @override
  String get t_err735 => 'Pour Android™';

  @override
  String get t_err738 => 'L\'ordre de broderie sera modifié.';

  @override
  String get t_err739 => 'Cette fonction ne peut pas être utilisée quand vous sélectionnez un motif spécial.';

  @override
  String get t_err740 => 'Le point va chevaucher les points de l\'autre côté. Veuillez diminuer la largeur du point ou augmenter la distance de décalage.';

  @override
  String get t_err741 => 'Les données ont été réduites à une taille compatible.';

  @override
  String get t_err742 => 'Souhaitez-vous aller aux réglages pour connecter votre machine à un réseau sans fil?';

  @override
  String get t_err743 => 'Il est impossible d\'utiliser cette fonction car les services CanvasWorkspace sont actuellement inaccessibles pour cause de maintenance. Veuillez patienter jusqu\'à la fin de la restauration.';

  @override
  String get t_err743_s => 'Il est impossible d\'utiliser cette fonction car les services Artspira sont actuellement inaccessibles pour cause de maintenance. Veuillez patienter jusqu\'à la fin de la restauration.';

  @override
  String get t_err744 => 'Votre machine n\'a pas pu être enregistrée sur CanvasWorkspace.\nRecommencez.';

  @override
  String get t_err744_s => 'Votre machine n\'a pas pu être enregistrée sur Artspira.\nRecommencez.';

  @override
  String get t_err745 => 'Le code PIN n\'est pas correct. Veuillez saisir de nouveau le code PIN.';

  @override
  String get t_err746 => 'Votre machine n\'est pas connectée à Internet.';

  @override
  String get t_err747 => 'Non connecté au serveur Internet.\nVérifiez le réglage proxy.';

  @override
  String get t_err748 => 'Délai du serveur dépassé.\nEssayez de nouveau plus tard.';

  @override
  String get t_err749 => 'Souhaitez-vous vous déconnecter de CanvasWorkspace?';

  @override
  String get t_err749_s => 'Souhaitez-vous vous déconnecter de Artspira?';

  @override
  String get t_err750 => 'Votre machine n\'a pas pu être déconnectée de CanvasWorkspace. Veuillez la déconnecter manuellement de la page Web.';

  @override
  String get t_err750_s => 'Votre machine n\'a pas pu être déconnectée de Artspira.\nVeuillez la déconnecter manuellement depuis l\'application.';

  @override
  String get t_err751 => 'Votre machine à coudre doit être enregistrée sur CanvasWorkspace pour recevoir les données de coupe (enregistrement du code PIN).\nSouhaitez-vous accéder à l\'écran de réglages?';

  @override
  String get t_err751_s => 'Votre machine à coudre doit être enregistrée sur Artspira pour recevoir les données de coupe (enregistrement du code PIN).\nSouhaitez-vous accéder à l\'écran de réglages?';

  @override
  String get t_err752 => 'Il est possible que votre machine ne soit pas enregistrée. Veuillez vérifier sur CanvasWorkspace.';

  @override
  String get t_err752_s => 'Il est possible que votre machine ne soit pas enregistrée. Veuillez vérifier sur Artspira.';

  @override
  String get t_err753 => 'Impossible de télécharger les données.\nRecommencez.';

  @override
  String get t_err754 => 'Le téléchargement des données a échoué.\nRecommencez.';

  @override
  String get t_err755 => 'Certification réussie.\nRedémarrez la machine.\n\nPour envoyer des données à votre machine ScanNCut, redémarrez votre machine et enregistrez votre machine sur le serveur CanvasWorkspace à partir de la page 13 des Paramètres.';

  @override
  String get t_err755_s => 'Certification réussie.\nRedémarrez la machine.';

  @override
  String get t_err756 => 'Souhaitez-vous remplacer les données existantes par de nouvelles données?\n* Les données qui se trouvent dans la Poche de données temporaires seront supprimées automatiquement après un certain temps.';

  @override
  String get t_err757 => 'Souhaitez-vous envoyer des données à la Poche de données temporaires du serveur?\n* Les données qui se trouvent dans la Poche de données temporaires seront supprimées automatiquement après un certain temps.';

  @override
  String get t_err761 => 'Aucune donnée dans la poche de données temporaires.';

  @override
  String get t_err762 => 'Aucune donnée lisible ne se trouvait dans la Poche de données temporaires.';

  @override
  String get t_err763 => 'Votre ScanNCut et votre machine à coudre doivent être enregistrées sur CanvasWorkspace pour recevoir les données de coupe (enregistrement du code PIN).\nSouhaitez-vous accéder à l\'écran de réglages?';

  @override
  String get t_err763_s => 'Enregistrez votre machine à coudre pour recevoir des données avec Artspira. (enregistrement par code PIN) \nSouhaitez-vous accéder à l\'écran de réglages afin de procéder à l\'enregistrement?';

  @override
  String get t_err764 => 'Veuillez installer « Artspira » sur votre appareil mobile (smartphone, tablette). \nEnrichissez votre expérience de broderie en créant votre compte Artspira. \nVeuillez scanner le code QR pour de plus amples détails.';

  @override
  String get t_err765 => 'Le nom de la machine n\'a pas pu être modifié.';

  @override
  String get t_err766 => 'Activer le réseau local LAN sans fil pour changer le nom de la machine.';

  @override
  String get t_err770 => 'Échec de la suppression.';

  @override
  String get t_err771 => 'Si vous supprimez tous les motifs personnalisés, les motifs qui sont utilisés pour l\'édition seront remplacés par un autre motif.\nD\'accord pour supprimer tous les motifs personnalisés?';

  @override
  String get t_err772 => 'Si vous supprimez ou modifiez le motif personnalisé importé après l\'enregistrement, il est possible que les données changent par rapport à l\'original.';

  @override
  String get t_err773 => 'Les données qui comportent des motifs personnalisés importés ne peuvent pas être enregistrées dans une mémoire externe.';

  @override
  String get t_err774 => 'Seules les données de point de broderie seront enregistrées. Les données d\'édition, y compris les motifs personnalisés, ne peuvent pas être enregistrées dans une mémoire externe. Veuillez les enregistrer dans la mémoire interne.';

  @override
  String get t_err775 => 'La mémoire de stockage de données est pleine.\nChoisissez un motif personnalisé pour le remplacer par le nouveau motif.';

  @override
  String get t_err776 => 'Si vous remplacez le motif personnalisé, il pourrait changer pendant que vous utilisez le motif précédent. OK pour continuer?';

  @override
  String get t_err_taper01 => 'Impossible de définir le point en biseau. Veuillez augmenter la distance ou l\'angle.';

  @override
  String get t_err_taper02 => 'Terminez la configuration avant de coudre.';

  @override
  String get t_err_taper03 => 'OK d\'annuler le réglage du point en biseau?';

  @override
  String get t_err_taper04 => 'OK d\'annuler l\'état actuel du point en biseau?';

  @override
  String get t_err_taper05 => 'Cette fonction ne peut pas être utilisée avec le point en biseau.';

  @override
  String get t_err_tapering07 => 'Veuillez appuyer sur la touche Point inverse pour commencer à broder l\'extrémité en biseau.';

  @override
  String get t_err_tapering08 => 'Le biseau s\'arrêtera après le nombre de répétitions spécifié.';

  @override
  String get t_err_tapering09 => 'Le biseau s\'arrêtera à l\'emplacement de l\'autocollant fin de couture.';

  @override
  String get t_err785 => 'Assurez-vous que la quantité de fil supérieur et de fil de la canette est suffisante pour broder complètement le motif, car il est impossible d\'obtenir des résultats satisfaisants si un des fils tombe à court.';

  @override
  String get t_err790 => 'Toutes les valeurs par défaut des données enregistrées, des réglages et des informations de réseau seront réinitialisées. \nSouhaitez-vous continuer?';

  @override
  String get t_err791 => 'Suppression en cours…\nNe mettez pas la machine hors tension.';

  @override
  String get t_err792 => 'La réinitialisation est terminée.\nVeuillez éteindre la machine.';

  @override
  String get t_err_paidcont_update => 'Pour utiliser ces données, il faut mettre le\nlogiciel de cette machine à jour à la version la plus récente.';

  @override
  String get t_err_embcarriageevacuate => 'Appuyez sur OK pour déplacer le chariot de l\'unité de broderie vers sa position d\'origine.';

  @override
  String get t_err_sr_01 => 'Enlevez le module régulateur de point de la machine.';

  @override
  String get t_err_sr_02 => 'L\'état de la couture a été annulé car il n\'y a pas eu d\'opération d\'entraînement de tissu pendant plusieurs secondes.';

  @override
  String get t_err_sr_03 => 'Vous pouvez démarrer le quilting (courtepointe)/faufilage en mouvement libre avec le régulateur de point.\n\nFaites attention de ne pas trop tendre le tissu, ceci pourrait casser l\'aiguille.';

  @override
  String get t_err_sr_04 => 'Veuillez démarrer la couture après avoir sélectionné le mode.';

  @override
  String get t_err_sr_05 => 'L\'aiguille bouge. Éloignez votre main de l\'aiguille.';

  @override
  String get t_err_sr_06 => 'Le module régulateur de point a été débranché. L\'écran dédié au régulateur de point va se fermer.   \nPour revenir à cet écran, branchez-le.';

  @override
  String get t_err_sr_08 => 'Faites attention de ne pas casser l\'aiguille en tirant trop fort sur le tissu dans les cas où vous réglez la tension du fil sur un réglage plus tendu.';

  @override
  String get t_err_sr_09 => 'Le mode aiguille jumelée ne peut pas être utilisé avec cette fonction.\nDébranchez le connecteur du module régulateur de point, puis désactivez le mode aiguille jumelée pour réessayer.';

  @override
  String get t_err_sr_10 => 'Quand vous utilisez le point de faufilage, n\'utilisez pas le pied quilting (courtepointe) ouvert pour le régulateur de point. Sinon, l\'aiguille pourrait se casser et provoquer des blessures.';

  @override
  String get t_err_manual_01_b => 'Si vous souhaitez voir les manuels sur votre appareil mobile ou votre ordinateur, veuillez consulter XXX(URL).';

  @override
  String get t_err_manual_02_t => 'Si vous souhaitez voir le manuel sur votre appareil mobile ou votre ordinateur, veuillez consulter XXX(URL).';

  @override
  String get t_err_proj_emb_001 => 'La fonctionnalité du projecteur est limitée du fait de la petite taille du cadre à broder. La fonction « Modification du projecteur avec le stylet » n\'est pas prise en charge, mais les motifs de broderie sont projetés.\n\n*Le chariot de l\'unité de broderie se déplace après avoir appuyé sur OK.';

  @override
  String get t_err_proj_emb_002 => 'La fonctionnalité du projecteur est limitée du fait de la petite taille du cadre à broder. La fonction « Modification du projecteur avec le stylet » n\'est pas prise en charge, mais les motifs de broderie sont projetés.';

  @override
  String get t_err_proj_emb_003 => 'Le projecteur se met hors tension.';

  @override
  String get t_err_proj_emb_004 => 'Le projecteur se met hors tension car le cadre à broder a été retiré.\n\n* Le chariot de l\'unité de broderie se déplace après avoir appuyé sur OK.';

  @override
  String get t_err_proj_emb_005 => 'Le projecteur se met hors tension car le cadre à broder a été retiré.';

  @override
  String get t_err_proj_emb_006 => 'Le projecteur se met hors tension.\n\n* Le chariot de l\'unité de broderie se déplace après avoir appuyé sur OK.';

  @override
  String get t_err_proj_emb_007 => 'Voulez-vous annuler la sélection du motif en cours ?\n\n* Le chariot de l\'unité de broderie se déplace après avoir appuyé sur OK.';

  @override
  String get t_err_proj_emb_008 => 'Conversion en motif de broderie. Mon centre de motifs va se fermer.\nVoulez-vous passer à l’écran de modification de la broderie?\n\n* Le projecteur se met hors tension et le chariot de l\'unité de broderie se déplace après une pression sur OK.';

  @override
  String get t_err_proj_emb_009 => 'Conversion en motif de broderie. IQ Designer va se fermer. \nVoulez-vous passer à l’écran de modification de la broderie?\n\n* Le projecteur se met hors tension et le chariot de l\'unité de broderie se déplace après une pression sur OK.';

  @override
  String get t_err_proj_emb_010 => 'Traitement en cours…';

  @override
  String get t_err_proj_emb_011 => 'Fermeture en cours...';

  @override
  String get t_err_proj_emb_012 => 'Le projecteur se met sous tension.\n\n* Le chariot de l\'unité de broderie se déplace après avoir appuyé sur OK.';

  @override
  String get t_err_proj_emb_013 => 'Le projecteur se met sous tension.';

  @override
  String get t_err_proj_emb_014 => 'Cette fonction ne peut pas être utilisée lorsque le projecteur est en fonctionnement.';

  @override
  String get t_err_proj_smallframe => 'Non disponible du fait de la petite taille du cadre à broder.';

  @override
  String get t_err_mdc_import_01 => 'Veillez à adapter les données car la taille change pendant le chargement.';

  @override
  String get t_err_voiceg_01 => 'Vérification en cours pour les données de guidage vocal...';

  @override
  String get t_err_voiceg_02 => 'Le guidage vocal est prêt et le réglage est activé.';

  @override
  String get t_err_photos_01 => 'Séparer le masque de l\'image.';

  @override
  String get t_err_photos_02 => 'OK de réinitialiser le réglage de la taille de l\'image ?';

  @override
  String get t_err_photos_03 => 'OK d\'annuler l\'élimination de l\'arrière-plan ?';

  @override
  String get t_err_photos_04 => 'Convertir en broderie.';

  @override
  String get t_err_photos_05 => 'Conversion en motif de broderie. La fonction de générateur d\'images en broderie va se fermer.\nVoulez-vous passer à l’écran de modification de la broderie?';

  @override
  String get t_err_photos_06 => 'Veuillez patienter.\nLa connexion LAN sans fil est temporairement désactivée pendant la conversion.';

  @override
  String get t_err_photos_exit => 'Voulez-vous quitter la fonction de générateur d\'images en broderie ?';

  @override
  String get t_err_font_old_new => 'OK de convertir le fichier au format de données plus récent que celui de l\'ancien format utilisé ?';

  @override
  String get t_err_font_old_lomited => 'La fonction de modification est limitée en raison de l\'ancien format de données.';

  @override
  String get t_err_firstset_wlan => 'Définissez un réseau local LAN sans fil.\nVoulez-vous accéder aux paramètres afin de connecter votre machine au réseau sans fil ?';

  @override
  String get t_err_firstset_voiceguidance => 'Définissez la fonction de guidage vocal.\nSouhaitez-vous accéder aux paramètres du guidage vocal ?';

  @override
  String get t_err_wlan_function_01 => 'Pour utiliser cette fonction, le paramètre de réseau local LAN sans fil doit être activé sur la machine, et la machine doit être connectée à un réseau sans fil.\nVoulez-vous accéder aux paramètres afin de connecter votre machine à un réseau sans fil ?';

  @override
  String get t_err_teachingimage => 'Les images sont fournies à titre d\'illustration seulement, certaines images peuvent présenter des différences par rapport au modèle.';

  @override
  String get t_err_photo_disclaimers => 'En utilisant cette fonctionnalité, vous acceptez qu\'aucune partie du contenu ne sera utilisée :\n• dans des buts contraires à toutes les lois et réglementations applicables (notamment et particulièrement tout contenu raciste, discriminatoire, incitant la haine, pornographique ou pédopornographique, et/ou toutes déclarations à l\'encontre de l\'ordre public ou de la décence publique);\n• pour violer le droit à la vie privée d\'une personne ou à la publicité;\n• pour enfreindre tout droit d\'auteur, marque déposée ou autre droit de propriété intellectuelle détenu par un tiers;\n• pour inclure une URL ou un mot-clé qui dirige les utilisateurs vers des sites malveillants.\nL\'utilisateur accepte et reconnaît être seul responsable du contenu utilisé.\nVeuillez vous reporter aux Conditions d’utilisation pour plus de détails.\n\nEn utilisant le contenu, je certifie avoir lu et compris les conditions d\'utilisation et les lignes directrices.';

  @override
  String get t_err_framemovekey => '* Appuyez sur la touche de déplacement de cadre sur l\'écran de broderie pour déplacer le cadre jusqu\'au centre.';

  @override
  String get speech_colorchangeinfo => 'Réalisation des points terminée.\nVeuillez définir la prochaine couleur de fil.';

  @override
  String get t_err_updateinfo_01 => 'Mise à jour importante disponible.\nMettez votre machine à jour en téléchargeant le fichier de mise à jour depuis [Télécharger le programme de mise à jour] dans les « Réglages de la machine ».';

  @override
  String get t_err_updateinfo_02 => 'Veuillez connecter votre machine à un réseau sans fil pour recevoir des notifications sur le logiciel le plus récent. \nOu veuillez visiter le site internet Brother support (Site Web de soutien Brother) pour les dernières mises à jour du logiciel.';

  @override
  String get t_err_removecarriage => 'Mettez la machine hors tension avant de fixer ou de retirer l\'unité de broderie.';

  @override
  String get t_err_filter_removed => 'Le filtre a été effacé car la catégorie n\'est pas prise en charge.';

  @override
  String get t_err_filter_cleared => 'Effacer le filtre est OK car la fonction de filtre n\'est pas applicable dans cette catégorie.';

  @override
  String get t_principal07 => '[Levier du pied-de-biche]';

  @override
  String get t_principal07_01 => '\nPour relever et abaisser le pied-de-biche, relevez et abaissez le levier correspondant.';

  @override
  String get t_principal07_02 => '(a) Pied-de-biche\n(b) Levier du pied-de-biche';

  @override
  String get t_principal03 => '[Commande de vitesse de couture]';

  @override
  String get t_principal03_01 => '\nCette commande permet de régler la vitesse de couture.\nFaites glisser le curseur vers la gauche pour coudre à vitesse réduite.\nFaites-le glisser vers la droite pour coudre à vitesse élevée.';

  @override
  String get t_principal03_02 => '(a) Levier\n(b) Vitesse réduite\n(c) Vitesse élevée';

  @override
  String get t_principal12 => '[Volant]';

  @override
  String get t_principal12_01 => 'Faites tourner le volant vers vous pour relever ou abaisser l\'aiguille.\nVous devez toujours faire tourner le volant vers l\'avant de la machine.';

  @override
  String get t_principal08 => '[Plateau et compartiment d\'accessoires]';

  @override
  String get t_principal08_01 => 'Rangez les pieds-de-biche et les canettes dans le compartiment d\'accessoires du plateau.\nLorsque vous cousez des pièces de tissu cylindriques, retirez le plateau.';

  @override
  String get t_principal10 => '[Genouillère]';

  @override
  String get t_principal10_00 => '(a) Genouillère';

  @override
  String get t_principal10_01 => '\nA l\'aide de la genouillère, vous pouvez relever et abaisser le pied-de-biche avec le genou, libérant ainsi vos mains pour diriger le tissu.\n\n1. Alignez les ergots de la genouillère sur les encoches de la prise, puis insérez-la au maximum.';

  @override
  String get t_principal10_03_00 => '(a) Pied-de-biche';

  @override
  String get t_principal10_03 => '\n2. Pour relever le pied-de-biche, poussez la genouillère vers la droite à l\'aide du genou.\nPour abaisser le pied-de-biche, laissez revenir la genouillère.';

  @override
  String get t_principal11 => '[Pédale]';

  @override
  String get t_principal11_00 => '\nVous pouvez également démarrer et arrêter le travail de couture à l\'aide de la pédale.\n\n1. Insérez la fiche de la pédale dans la prise de la machine.';

  @override
  String get t_principal11_02 => '2. Appuyez lentement sur la pédale pour commencer à coudre.\nPour arrêter la machine, relâchez la pression exercée sur la pédale.\n\n*La vitesse réglée à l\'aide de la commande de vitesse de couture correspond à la vitesse maximale de la pédale.';

  @override
  String get t_xv_principal11_01 => 'Grâce à la pédale multifonction, outre le démarrage et l\'arrêt de la couture, vous pouvez spécifier diverses opérations à exécuter sur la machine à coudre, telles que la coupe de fil et la couture de points inverses.';

  @override
  String get t_xv_principal11_02 => '1. Alignez le bord large de la plaque d\'assemblage sur l\'encoche en dessous de la pédale principale, puis fixez-les ensemble à l\'aide d\'une vis.';

  @override
  String get t_xv_principal11_03 => '2. Alignez l\'autre côté de la plaque d\'assemblage dans l\'encoche en dessous de la pédale latérale, puis fixez-les ensemble à l\'aide d\'une vis.';

  @override
  String get t_xv_principal11_04 => '3. Insérez la fiche de la pédale latérale dans la prise à l\'arrière de la pédale principale.';

  @override
  String get t_xv_principal11_05 => '4. Insérez la fiche arrondie de la pédale principale dans la prise de la pédale à droite de la machine.';

  @override
  String get t_xv_principal11_06 => '5. Appuyez lentement sur la pédale pour commencer à coudre. Pour arrêter la machine, relâchez la pression exercée sur la pédale.\n\n*La vitesse réglée à l\'aide de la commande de vitesse de couture correspond à la vitesse maximale de la pédale.';

  @override
  String get t_principal11_01_02 => '(a) Pédale\n(b) Prise de pédale';

  @override
  String get t_principal09 => '[Sélecteur de réglage des griffes d\'entraînement]';

  @override
  String get t_principal09_01 => '\nUtilisez le sélecteur de réglage des griffes d\'entraînement afin de les abaisser.';

  @override
  String get t_principal09_02 => '(a) Sélecteur de réglage des griffes d\'entraînement';

  @override
  String get t_principal_buttons_01 => '[Touche \"Positionnement aiguille\"]';

  @override
  String get t_principal_buttons_01_01 => 'Utilisez cette touche pour changer le sens de couture ou pour les coutures détaillées sur de petites zones.\nAppuyez sur cette touche pour relever ou abaisser l\'aiguille.\nAppuyez deux fois sur cette touche pour coudre un seul point.';

  @override
  String get t_principal_buttons_02 => '[Touche \"Coupe-fil\"]';

  @override
  String get t_principal_buttons_02_01 => 'Lorsque vous avez terminé de coudre, appuyez sur cette touche pour couper automatiquement le fil qui dépasse.';

  @override
  String get t_principal_buttons_06 => '[Touche \"Releveur du pied-de-biche\"]';

  @override
  String get t_principal_buttons_06_01 => 'Cette touche permet d\'abaisser le pide-de-biche et d\'exercer une pression sur le tissu.\nAppuyez de nouveau sur cette touche pour relever le pied-de-biche.\nVous pouvez relever le pied-de-biche au maximum en maintenant enfoncée la touche située sur le pied-de-biche.';

  @override
  String get t_principal_buttons_05 => '[Touche \"Enfilage automatique\"]';

  @override
  String get t_principal_buttons_05_01 => 'Cette touche permet d\'enfiler automatiquement l\'aiguille.';

  @override
  String get t_principal_buttons_04 => '[Touche \"Marche/arrêt\"]';

  @override
  String get t_principal_buttons_04_01 => 'Lorsque vous appuyez sur cette touche, la machine coud quelques points à vitesse réduite et commence ensuite à coudre à la vitesse réglée par la commande de vitesse de couture.\nPour arrêter la machine, appuyez de nouveau sur cette touche.\nPour coudre à la vitesse la plus lente de la machine, maintenez la touche enfoncée.\nLa touche change de couleur en fonction du mode de fonctionnement de la machine.\n\nVerte : la machine est prête à coudre ou est en train de coudre.\nRouge : la machine ne peut pas coudre.';

  @override
  String get t_principal_buttons_03 => '[Touche \"Point inverse\"]';

  @override
  String get t_principal_buttons_03_01 => 'Ce bouton permet de coudre des points inverses au début et à la fin du travail de couture.\nPour les motifs de points droits et zigzag, la machine coud des points inverses à faible vitesse si vous maintenez le bouton \"Inverse\" enfoncé. (Les points sont cousus dans le sens opposé). \nPour les autres motifs de points, la machine coud des points de renfort. Lorsque vous appuyez sur ce bouton, la machine coud entre 3 et 5 points au même endroit et s\'arrête automatiquement.';

  @override
  String get t_principal_buttons_07 => '[Touche \"Renfort/maintien\"]';

  @override
  String get t_principal_buttons_07_01 => 'Ce bouton permet de coudre des points de renfort au début et à la fin du travail de couture.\nPour les motifs de points de couture courants, appuyez sur ce bouton et la machine coud entre 3 et 5 points au même endroit, puis s\'arrête automatiquement.\nPour les motifs de caractères/points décoratifs, la machine coud des points de renfort après avoir cousu un seul motif.';

  @override
  String get t_basic13 => '[Enfilage supérieur]';

  @override
  String get t_basic13_01_02 => '(a) Touche \"Releveur du pied-de-biche\"\n';

  @override
  String get t_basic13_01 => 'Appuyez sur la touche correspondant à la vidéo permettant de regarder les instructions affichées.\n1. Appuyez sur la touche \"Releveur du pied-de-biche\" pour relever le pied-de-biche. ';

  @override
  String get t_basic13_02_00 => '(a) Touche \"Positionnement aiguille\"';

  @override
  String get t_basic13_02 => '\n2. Appuyez sur la touche \"Positionnement aiguille\" pour relever l\'aiguille. ';

  @override
  String get t_basic13_03_02 => '(a) Porte-bobine\n(b) Couvercle de bobine\n(c) Bobine de fil ';

  @override
  String get t_basic13_03 => '\n3. Faites pivoter le porte-bobine vers le haut.\nPlacez la bobine de fil sur le porte-bobine de façon à ce que le fil se déroule à partir de l\'avant de la bobine.\nEnfoncez le couvercle de bobine au maximum sur le porte-bobine et remettez ensuite ce dernier dans sa position d\'origine. ';

  @override
  String get t_basic13_11_02 => '(a) Couvercle de bobine (petit)  \n(b) Bobine de fil (fil croisée)  \n(c) Espace  ';

  @override
  String get t_basic13_11 => '\nLorsque vous cousez avec du fil fin et bobiné de façon croisée, utilisez le petit couvercle de bobine et laissez un petit espace entre le couvercle et la bobine de fil. ';

  @override
  String get t_basic13_04_02 => '(a) Plaque du guide-fil ';

  @override
  String get t_basic13_04 => '\n4. Tout en maintenant le fil de la bobine des deux mains, tirez le fil vers le bas puis sur le côté gauche de la plaque du guide-fil.';

  @override
  String get t_basic13_05 => '5. Tout en maintenant le fil de la bobine de la main droite, tenez l\'extrémité du fil de la main gauche, puis passez le fil vers le bas, vers le haut et de nouveau vers le bas dans les rainures.';

  @override
  String get t_basic13_06_02 => '(a) Guide-fil de la barre à aiguille';

  @override
  String get t_basic13_06 => '\n6. Passez le fil derrière le guide-fil de la barre à aiguille. \n';

  @override
  String get t_basic13_07 => '7. Appuyez sur la touche \"Releveur du pied-de-biche\" pour abaisser le pied-de-biche. ';

  @override
  String get t_basic13_08_02 => '(a) Disques du guide-fil\n';

  @override
  String get t_basic13_08 => '8. Passez le fil dans les disques du guide-fil. Veillez à ce que le fil passe dans la rainure du guide-fil. Le fil doit être bien inséré dans les disques du guide-fil. L\'aiguille risque de ne pas être enfilée. \n';

  @override
  String get t_basic13_09_02 => '(b) Coupe-fil';

  @override
  String get t_basic13_09 => '\n9. Tirez le fil vers le haut depuis le dessous du coupe-fil pour couper le fil. ';

  @override
  String get t_basic13_10_02 => '(a) Touche \"Enfilage automatique\"';

  @override
  String get t_basic13_10 => '\n10. Appuyez sur la touche \"Enfilage automatique\" pour que l\'aiguille soit automatiquement enfilée. ';

  @override
  String get t_basic14 => '[Bobinage de la canette]';

  @override
  String get t_basic14_01_02 => '(a) Rainure de la canette\n(b) Ressort de l\'axe ';

  @override
  String get t_basic14_00 => '\nAppuyez sur la touche correspondant à la vidéo permettant de regarder les instructions affichées.\n\n1. Alignez la rainure de la canette sur le ressort de l\'axe du bobineur et placez la canette sur l\'axe. ';

  @override
  String get t_basic14_02 => '\n2. Placez le porte-bobine supplémentaire en position \"haute\". ';

  @override
  String get t_basic14_02_02 => '(a) Porte-bobine supplémentaire ';

  @override
  String get t_basic14_03 => '\n3. Placez la bobine de fil sur le porte-bobine supplémentaire de façon à ce que le fil se déroule par l\'avant de la bobine. Enfoncez ensuite au maximum le couvercle de bobine sur le porte-bobine afin de fixer la bobine de fil. ';

  @override
  String get t_basic14_03_02 => '(a) Couvercle de bobine\n(b) Porte-bobine\n(c) Bobine de fil ';

  @override
  String get t_basic14_04 => '\n4. De votre main droite, tenez le fil près de la bobine de fil. De votre main gauche, tenez l\'extrémité du fil et, à l\'aide de vos deux mains, faites passer le fil autour des guide-fils. ';

  @override
  String get t_basic14_04_02 => '(a) Guide-fil';

  @override
  String get t_basic14_05 => '\n5. Faites passer le fil autour du disque de pré-tension en veillant à ce qu\'il passe bien en dessous du disque.  ';

  @override
  String get t_basic14_05_02 => '(a)  Disque de pré-tension';

  @override
  String get t_basic14_06 => '6. Enroulez 5 ou 6 fois le fil dans le sens des aiguilles d\'une montre autour de la canette. ';

  @override
  String get t_basic14_07 => '(a) Fente du guide (avec coupe-fil intégré)\n(b) Base du bobineur de canette\n\n7. Faites passer l\'extrémité du fil dans la fente du guide de la base du bobineur de canette et tirez le fil vers la droite pour le couper avec le coupe-fil. ';

  @override
  String get t_basic14_08_02 => '\n8. Poussez la commande de bobinage de canette vers la gauche, jusqu\'à ce qu\'elle soit en place (vous devez entendre un déclic).  La fenêtre de bobinage de canette s\'affiche sur l\'écran LCD.';

  @override
  String get t_basic14_08_03 => '(a) Commande de bobinage de canette';

  @override
  String get t_basic14_08_04 => '\n* Utilisez le curseur de bobinage de canette pour régler la quantité de fil bobiné sur la canette sur l\'un des cinq niveaux.';

  @override
  String get t_basic14_08_05 => '(a) Curseur de bobinage de canette\n(b) Plus\n(c) Moins';

  @override
  String get t_basic14_09 => '9. Appuyez sur la touche \"Bobinage de la canette Marche/arrêt\". La canette commence à tourner automatiquement.\n';

  @override
  String get t_basic14_10 => '10. Pour modifier la vitesse de bobinage, appuyez sur - ou + sur la fenêtre de bobinage de canette. Appuyez sur \"OK\" pour minimiser la fenêtre de bobinage de canette.';

  @override
  String get t_basic14_101 => '11. La canette arrête de tourner une fois le bobinage terminé. La commande de bobinage de canette se remet alors dans sa position d\'origine.';

  @override
  String get t_basic14_102 => '12. Coupez le fil avec le coupe-fil et retirez la canette. ';

  @override
  String get t_basic14_11 => '\n*Utilisation du porte-bobine\n\n\nVous pouvez bobiner la canette avant de coudre à l\'aide du porte-bobine principal.\nNote: Vous ne pouvez pas utiliser ce porte-bobine pour bobiner la canette tout en cousant.\n\n1. Alignez la rainure de la canette sur le ressort de l\'axe du bobineur et placez la canette sur l\'axe.';

  @override
  String get t_basic14_11_02 => '(a) Rainure de la canette\n(b) Ressort de l\'axe ';

  @override
  String get t_basic14_12 => '\n2. Faites pivoter le porte-bobine vers le haut.\nPlacez la bobine de fil sur le porte-bobine de façon à ce que le fil se déroule à partir de l\'avant de la bobine.\nEnfoncez le couvercle de bobine au maximum sur le porte-bobine et remettez ensuite ce dernier dans sa position d\'origine. ';

  @override
  String get t_basic14_12_02 => '(a) Porte-bobine\n(b) Couvercle de bobine\n(c) Bobine de fil ';

  @override
  String get t_basic14_13 => '\n3. En maintenant le fil, faites glisser le fil dans les rainures de la plaque du guide-fil.\nFaites passer le fil autour du guide-fil. ';

  @override
  String get t_basic14_13_02 => '(a) Plaque du guide-fil\n(b) Guide-fil ';

  @override
  String get t_basic14_14 => '\n4. Faites passer le fil autour du disque de pré-tension en veillant à ce qu\'il passe bien en dessous du disque. ';

  @override
  String get t_basic14_15_02 => '(a) Disque de pré-tension ';

  @override
  String get t_basic14_16 => '5. Enroulez 5 ou 6 fois le fil dans le sens des aiguilles d\'une montre autour de la canette. ';

  @override
  String get t_basic14_17 => '(a) Fente du guide (avec coupe-fil intégré)\n(b) Base du bobineur de canette\n\n6. Faites passer l\'extrémité du fil dans la fente du guide de la base du bobineur de canette et tirez le fil vers la droite pour le couper avec le coupe-fil. ';

  @override
  String get t_basic14_18 => '\n7. Poussez la commande de bobinage de canette vers la gauche, jusqu\'à ce qu\'elle soit en place (vous devez entendre un déclic).  La fenêtre de bobinage de canette s\'affiche sur l\'écran LCD.';

  @override
  String get t_basic14_18_02 => '(a) Commande de bobinage de canette';

  @override
  String get t_basic14_20 => '8. Appuyez sur la touche \"Bobinage de la canette Marche/arrêt\". La canette commence à tourner automatiquement.\n';

  @override
  String get t_basic14_201 => '9. Pour modifier la vitesse de bobinage, appuyez sur - ou + sur la fenêtre de bobinage de canette. Appuyez sur \"OK\" pour minimiser la fenêtre de bobinage de canette.';

  @override
  String get t_basic14_202 => '10. La canette arrête de tourner une fois le bobinage terminé. La commande de bobinage de canette se remet alors dans sa position d\'origine.';

  @override
  String get t_basic14_203 => '11. Coupez le fil avec le coupe-fil et retirez la canette. ';

  @override
  String get t_basic14_21_02 => '\nLorsque vous cousez avec du fil fin et bobiné de façon croisée, utilisez le petit couvercle de bobine et laissez un petit espace entre le couvercle et la bobine de fil. ';

  @override
  String get t_basic14_21_03 => '(a) Couvercle de bobine (petit) \n(b) Bobine de fil (fil croisé) \n(c) Espace ';

  @override
  String get t_basic15 => '[Remplacement de l\'aiguille]';

  @override
  String get t_basic15_00 => '\nPour contrôler correctement une aiguille :\nPour contrôler l\'aiguille, placez le côté plat de l\'aiguille sur une surface plane.\nVérifiez l\'aiguille à partir du haut et des côtés.\nJetez toutes les aiguilles tordues.';

  @override
  String get t_basic15_00_01 => '(a) Espace parallèle\n(b) Surface plane (capot du compartiment à canette, plaque de verre, etc.) ';

  @override
  String get t_basic15_01 => '1. Appuyez sur la touche \"Positionnement aiguille\" pour relever l\'aiguille.';

  @override
  String get t_basic15_02 => '2. Appuyez sur la touche de remplacement du pied-de-biche/d\'aiguille.';

  @override
  String get t_basic15_03 => '3. A l\'aide du tournevis, dévissez légèrement la vis en la faisant tourner vers l\'avant de la machine et retirez l\'aiguille.';

  @override
  String get t_basic15_04 => '\n4. Le côté plat de l\'aiguille tourné vers l\'arrière, enfoncez la nouvelle aiguille au maximum jusqu\'à la butée d\'aiguille (fenêtre de contrôle) du support d\'aiguille.\nA l\'aide d\'un tournevis, fixez soigneusement la vis du support d\'aiguille.';

  @override
  String get t_basic15_04_02 => '(a) Butée d\'aiguille\n(b) Trou de fixation de l\'aiguille\n(c) Côté plat de l\'aiguille';

  @override
  String get t_basic15_05 => '5. Appuyez sur la touche de remplacement du pied-de-biche/d\'aiguille pour déverrouiller toutes les touches de fonctionnement.';

  @override
  String get t_basic16 => '[Changement du pied-de-biche]';

  @override
  String get t_basic16_01 => '*Retrait du pied-de-biche\n\n1. Appuyez sur la touche \"Positionnement aiguille\" pour relever l\'aiguille.';

  @override
  String get t_basic16_02 => '2. Appuyez sur la touche de remplacement du pied-de-biche/d\'aiguille.';

  @override
  String get t_basic16_03 => '\n3. Relevez le levier du pied-de-biche.';

  @override
  String get t_basic16_03_02 => '(a) Pied-de-biche\n(b) Levier du pied-de-biche';

  @override
  String get t_basic16_04 => '\n4. Appuyez sur le bouton noir situé à l\'arrière du support du pied-de-biche et retirez le pied-de-biche.';

  @override
  String get t_basic16_04_02 => '(a) Bouton noir\n(b) Support du pied-de-biche';

  @override
  String get t_basic16_05 => '\n*Fixation du pied-de-biche\n\n1. Placez le nouveau pied-de-biche au-dessous du support, en alignant le crochet avec l\'encoche du support.\nAbaissez le levier du pied-de-biche afin que le crochet s\'enclenche dans l\'encoche du support.';

  @override
  String get t_basic16_05_02 => '(a) Encoche\n(b) Crochet';

  @override
  String get t_basic16_06 => '2. Appuyez sur la touche de remplacement du pied-de-biche/d\'aiguille pour déverrouiller toutes les touches de fonctionnement.';

  @override
  String get t_basic16_07 => '(a) Pied-de-biche\n(b) Levier du pied-de-biche\n\n3. Relevez le levier du pied-de-biche.';

  @override
  String get t_basic17 => '[Mise en place de la canette]';

  @override
  String get t_basic17_01 => 'Appuyez sur la touche correspondant à la vidéo permettant de regarder les instructions affichées.\n\n1. Appuyez sur la touche \"Releveur du pied-de-biche\" pour relever le pied-de-biche.';

  @override
  String get t_basic17_02 => '(a) Capot du compartiment à canette\n(b) Bouton d\'ouverture\n\n2. Faites glisser le bouton d\'ouverture du capot du compartiment à canette vers la droite. Le capot du compartiment à canette s\'ouvre.\nRetirez-le.';

  @override
  String get t_basic17_03 => '3. Tenez la canette de votre main droite et, de l\'autre, maintenez l\'extrémité du fil.';

  @override
  String get t_basic17_04 => '4. Insérez la canette dans le logement de façon à ce que le fil se déroule vers la gauche.\nMaintenez légèrement la canette vers le bas à l\'aide de votre main droite et guidez de la main gauche le fil comme indiqué.';

  @override
  String get t_basic17_05 => '\n5. Faites passer le fil par le guide-fil, puis tirez le fil vers l\'avant.\nLe coupe-fil coupe le fil.';

  @override
  String get t_basic17_05_02 => '(a) Coupe-fil';

  @override
  String get t_basic17_06 => '6. Placez le taquet dans l\'angle inférieur gauche du capot du compartiment à canette, puis appuyez légèrement sur le côté droit pour fermer le capot.';

  @override
  String get t_embbasic17 => '[Application du renfort thermocollant (support) sur le tissu]';

  @override
  String get t_embbasic17_00 => '\nPour des résultats optimaux lors de la réalisation de vos travaux de broderie, utilisez toujours un renfort de broderie.\nSuivez les instructions qui figurent sur l\'emballage du renfort.';

  @override
  String get t_embbasic17_01 => '\n1. Utilisez un morceau de renfort de broderie plus grand que le cadre de broderie.';

  @override
  String get t_embbasic17_01_02 => '(a) Taille du cadre de broderie\n(b) Renfort thermocollant (support) ';

  @override
  String get t_embbasic17_02 => '\n2. Repassez le renfort thermocollant sur l\'envers du tissu.\n\n*Lorsque vous utilisez des tissus qui ne peuvent pas être repassés (tels que les serviettes de bain ou les tissus avec des boucles qui s\'élargissent lors du repassage) ou à des endroits difficiles à repasser, placez le renfort de broderie sous le tissu sans le fixer, placez ensuite le tissu et le renfort dans le cadre de broderie ou demandez à votre revendeur agréé de vous conseiller sur le renfort à utiliser.';

  @override
  String get t_embbasic17_02_02 => '(a) Côté thermofusible du renfort\n(b) Tissu (envers) ';

  @override
  String get t_embbasic17_03 => '\n*Lorsque vous brodez sur des tissus fins, tels que l\'organdi ou la batiste, ou sur des tissus duvetés, tels que les serviettes de bain ou le velours, utilisez un renfort de broderie soluble (vendu séparément) pour des résultats optimaux.\nUne fois le renfort dissout dans l\'eau, la finition de votre ouvrage sera parfaite.';

  @override
  String get t_embbasic18 => '[Mise en place du tissu]';

  @override
  String get t_embbasic18_01 => '1. Soulevez et dévissez légèrement la vis de réglage du cadre de broderie et retirez le cadre intérieur.';

  @override
  String get t_embbasic18_02 => '\n2. Placez le tissu sur le dessus du cadre extérieur, l\'endroit vers le haut.\nRéinsérez le cadre intérieur en veillant à bien aligner son repère (Δ) avec celui (Δ) du cadre extérieur.';

  @override
  String get t_embbasic18_02_02 => '(a) Cadre intérieur\n(b) Cadre extérieur\n(c) Vis de réglage';

  @override
  String get t_embbasic18_03 => '3. Serrez doucement la vis de réglage du cadre et tendez bien le tissu en tirant sur les bords et sur les coins.\nNe desserrez pas la vis.';

  @override
  String get t_embbasic18_04 => '\n4. Tendez doucement le tissu, puis serrez la vis de réglage du cadre afin d\'éviter que le tissu se détende après l\'avoir tendu.\n\n* Après avoir étiré le tissu, vérifiez qu\'il est bien tendu.\n\n* Avant de commencer à broder, assurez-vous que les cadres intérieur et extérieur sont au même niveau.\n\n*Remarque\nEtirez le tissu en tirant sur les quatre coins et les quatre côtés. Tout en étirant le tissu, serrez la vis de réglage du cadre.';

  @override
  String get t_embbasic18_04_02 => '(a) Cadre extérieur\n(b) Cadre intérieur\n(c) Tissu';

  @override
  String get t_embbasic18_04_11 => '*Lorsque vous utilisez un cadre de broderie avec un levier.\n';

  @override
  String get t_embbasic18_04_12 => '\n1. Abaissez le levier.';

  @override
  String get t_embbasic18_04_13 => '2. Dévissez la vis de réglage du cadre de broderie à la main et retirez le cadre intérieur.';

  @override
  String get t_embbasic18_04_14 => '\n3. Mettez le tissu en place.';

  @override
  String get t_embbasic18_04_15 => '4．Serrez la vis de réglage du cadre à la main.';

  @override
  String get t_embbasic18_04_16 => '5. Remettez le levier dans sa position d\'origine.';

  @override
  String get t_embbasic18_05 => '\n[Utilisation du gabarit]';

  @override
  String get t_embbasic18_05_01 => 'Si vous souhaitez broder le motif à un endroit particulier, utilisez un gabarit avec le cadre.\n\n1. A l\'aide d\'une craie, marquez la zone que vous souhaitez broder sur le tissu.';

  @override
  String get t_embbasic18_05_02 => '(a) Motif de broderie\n(b) Repère';

  @override
  String get t_embbasic18_06 => '\n2. Placez le gabarit sur le cadre intérieur. Alignez les lignes directrices du gabarit avec les repères tracés sur le tissu.';

  @override
  String get t_embbasic18_06_02 => '(a) Cadre intérieur\n(b) Lignes directrices';

  @override
  String get t_embbasic18_07 => '\n3. Etirez doucement le tissu afin qu\'il ne reste aucun pli et insérez le cadre intérieur dans le cadre extérieur.';

  @override
  String get t_embbasic18_07_02 => '(a) Cadre intérieur\n(b) Cadre extérieur';

  @override
  String get t_embbasic18_08 => '4. Retirez le gabarit.';

  @override
  String get t_embbasic19 => '[Fixation du cadre de broderie]';

  @override
  String get t_embbasic19_01 => '*Bobinez et installez la canette avant de fixer le cadre de broderie.\n\n1. Appuyez sur la touche \"Releveur du pied-de-biche\" pour relever le pied-de-biche.';

  @override
  String get t_embbasic19_02 => '\n2. Relevez le levier de sécurité du cadre.';

  @override
  String get t_embbasic19_03 => '\n3. Alignez le guide du cadre de broderie avec le côté droit du porte-cadre.';

  @override
  String get t_embbasic19_03_02 => '(a) Porte-cadre de broderie\n(b) Guide du cadre de broderie';

  @override
  String get t_embbasic19_04 => '4. Faites glisser le cadre de broderie dans le porte-cadre, en veillant à aligner le repère (Δ) du cadre de broderie avec celui du porte-cadre (Δ) .';

  @override
  String get t_embbasic19_05 => '\n5. Abaissez le levier de sécurité du cadre pour maintenir le cadre de broderie dans le porte-cadre.\n*Si le levier n\'est pas abaissé, le motif de broderie risque de ne pas être cousu correctement ou le pied-de-biche risque de heurter le cadre de broderie et vous pourriez vous blesser.';

  @override
  String get t_embbasic19_05_02 => '(a) Levier de sécurité du cadre';

  @override
  String get t_embbasic19_06 => '[Retrait du cadre de broderie]\n\n1. Relevez le levier de sécurité du cadre.';

  @override
  String get t_embbasic19_07 => '2. Tirez le cadre de broderie vers vous.';

  @override
  String get t_embbasic20 => '[Fixation de l\'unité de broderie]';

  @override
  String get t_embbasic20_01 => 'Avant de mettre la machine hors tension, consultez la procédure décrite ci-dessous.\n\n1. Mettez la machine hors tension et retirez le plateau (si votre machine est équipée de cet élément).';

  @override
  String get t_embbasic20_03 => '\n2. Insérez le connecteur d\'unité de broderie dans le port de connexion de la machine. Enfoncez doucement l\'unité jusqu\'à ce que vous entendiez un déclic.';

  @override
  String get t_embbasic20_03_02 => '(a) Connecteur d\'unité de broderie\n(b) Port de connexion de la machine';

  @override
  String get t_embbasic20_04 => '(a) Marche\n(b) Arrêt\n\n3. Mettez la machine sous tension.';

  @override
  String get t_embbasic20_05 => '4. Appuyez sur la touche OK. Le chariot se déplace pour se mettre en position de départ.';

  @override
  String get t_embbasic20_06 => '[Retrait de l\'unité de broderie]';

  @override
  String get t_embbasic20_06_02 => '\n(a) Marche\n(b) Arrêt\n\nAvant de mettre la machine hors tension, consultez la procédure décrite ci-dessous.\n\n1. Mettez la machine hors tension.';

  @override
  String get t_embbasic20_07 => '(a) Bouton de dégagement (situé sous l\'unité de broderie)\n\n2. Maintenez le bouton de dégagement enfoncé, puis tirez pour éloigner l\'unité de broderie de la machine.';

  @override
  String get t_xp_embbasic21 => '[Fixation du pied de broderie \"W\"]';

  @override
  String get t_xp_embbasic21_01 => '1. Appuyez sur la touche \"Positionnement aiguille\" pour relever l\'aiguille.';

  @override
  String get t_xp_embbasic21_02 => '2. Appuyez sur  la touche de remplacement du pied-de-biche/d\'aiguille.';

  @override
  String get t_xp_embbasic21_03 => '\n3. Relevez le levier du pied-de-biche.';

  @override
  String get t_xp_embbasic21_04 => '\n4. Appuyez sur le bouton noir situé sur le support du pied-de-biche et retirez le pied-de-biche.';

  @override
  String get t_xp_embbasic21_04_02 => '(a) Bouton noir\n(b) Support du pied-de-biche';

  @override
  String get t_xp_embbasic21_05 => '\n5. A l\'aide du tournevis livré avec la machine, dévissez la vis du support du pied-de-biche et retirez-le.';

  @override
  String get t_xp_embbasic21_05_02 => '(a) Tournevis\n(b) Support du pied-de-biche\n(c) Vis du support du pied-de-biche';

  @override
  String get t_xp_embbasic21_06 => '(a) Levier du pied-de-biche\n\n6. Abaissez le levier du pied-de-biche.';

  @override
  String get t_xp_embbasic21_07_01 => '(a) Barre de pied-de-biche\n';

  @override
  String get t_xp_embbasic21_07_02 => '7. Placez le pied de broderie \"W\" sur la barre du pied-de-biche par l\'arrière.';

  @override
  String get t_xp_embbasic21_08_01 => '(a) Vis du support du pied-de-biche\n';

  @override
  String get t_xp_embbasic21_08_02 => '8. Maintenez le pied de broderie en place avec votre main droite, et serrez fermement la vis de support du pied-de-biche à l\'aide du tournevis fourni.';

  @override
  String get t_xp_embbasic21_09 => '9. Appuyez sur la touche de remplacement du pied-de-biche/d\'aiguille pour déverrouiller toutes les touches de fonctionnement.';

  @override
  String get t_embbasic21 => '[Fixation du pied de broderie \"W\"]';

  @override
  String get t_embbasic21_01 => '1. Appuyez sur la touche \"Positionnement aiguille\" pour relever l\'aiguille.';

  @override
  String get t_embbasic21_02 => '2. Appuyez sur  la touche de remplacement du pied-de-biche/d\'aiguille.';

  @override
  String get t_embbasic21_03 => '\n3. Relevez le levier du pied-de-biche.';

  @override
  String get t_embbasic21_04 => '\n4. Appuyez sur le bouton noir situé sur le support du pied-de-biche et retirez le pied-de-biche.';

  @override
  String get t_embbasic21_04_02 => '(a) Bouton noir\n(b) Support du pied-de-biche';

  @override
  String get t_embbasic21_05 => '\n5. A l\'aide du tournevis livré avec la machine, dévissez la vis du support du pied-de-biche et retirez-le.';

  @override
  String get t_embbasic21_05_02 => '(a) Tournevis\n(b) Support du pied-de-biche\n(c) Vis du support du pied-de-biche';

  @override
  String get t_embbasic21_06 => '(a) Levier du pied-de-biche\n\n6. Abaissez le levier du pied-de-biche.';

  @override
  String get t_embbasic21_07 => '(a) Bras\n(b) Support d\'aiguille\n(c) Vis du support du pied-de-biche\n(d) Curseur\n\n7. Placez le pied de broderie \"W\" sur la barre du pied-de-biche, afin que le bras du pied de broderie \"W\" entre en contact avec l\'arrière du support d\'aiguille.';

  @override
  String get t_embbasic21_08 => '8. Utilisez le tournevis livré avec la machine pour visser à fond la vis du support du pied-de-biche.';

  @override
  String get t_embbasic21_09 => '9. Appuyez sur la touche de remplacement du pied-de-biche/d\'aiguille pour déverrouiller toutes les touches de fonctionnement.';

  @override
  String get t_embbasic21_emb_07 => '(a) Bras\n(b) Support d\'aiguille\n(c) Vis du support du pied-de-biche\n(d) Curseur\n\n3. Placez le pied de broderie \"W\" sur la barre du pied-de-biche, afin que le bras du pied de broderie \"W\" entre en contact avec l\'arrière du support d\'aiguille.';

  @override
  String get t_embbasic21_emb_08 => '4. Utilisez le tournevis livré avec la machine pour visser à fond la vis du support du pied-de-biche.';

  @override
  String get t_embbasic21_emb_09 => '5. Appuyez sur la touche de remplacement du pied-de-biche/d\'aiguille pour déverrouiller toutes les touches de fonctionnement.';

  @override
  String get t_xv_embbasic21 => '[Fixation du pied de broderie \"W+\"]';

  @override
  String get t_xv_embbasic21_05 => '\n5. À l\'aide du tournevis livré avec la machine, retirez la vis du support du pied-de-biche et déposez celui-ci.';

  @override
  String get t_xv_embbasic21_07_01 => '(a) Barre de pied-de-biche\n';

  @override
  String get t_xv_embbasic21_07_02 => '7. Placez le pied de broderie \"W+\" sur la barre du pied-de-biche par l\'arrière.';

  @override
  String get t_xv_embbasic21_08_01 => '(a) Vis du support du pied-de-biche\n';

  @override
  String get t_xv_embbasic21_08_02 => '8. Maintenez le pied de broderie en place avec votre main droite, et serrez fermement la vis de support du pied-de-biche à l\'aide du tournevis fourni.';

  @override
  String get t_xv_embbasic21_09 => '9. Branchez le connecteur du pied de broderie \"W+\" avec pointeur lumineux dans la prise à gauche de la machine.';

  @override
  String get t_xv_embbasic21_10 => '10. Appuyez sur la touche de remplacement du pied-de-biche/d\'aiguille pour déverrouiller toutes les touches de fonctionnement.';

  @override
  String get t_embbasic22 => '[Renfort correct à utiliser]';

  @override
  String get t_embbasic22_00_01 => '1. Tissus pouvant être repassés';

  @override
  String get t_embbasic22_00_02 => '2. Tissus ne pouvant pas être repassés';

  @override
  String get t_embbasic22_00_03 => '3. Tissus fins';

  @override
  String get t_embbasic22_00_04 => '4. Tissus duvetés';

  @override
  String get t_embbasic22_00_05 => '\nPour des résultats optimaux lors de la réalisation de vos travaux de broderie, utilisez toujours un renfort de broderie. Suivez les instructions qui figurent sur l\'emballage du renfort.';

  @override
  String get t_embbasic22_01 => '\n[1. Tissus pouvant être repassés]';

  @override
  String get t_embbasic22_01_02 => '\nRepassez le renfort thermocollant sur l\'envers du tissu. Utilisez un morceau de renfort de broderie plus grand que le cadre de broderie.';

  @override
  String get t_embbasic22_01_03 => '(a) Taille du cadre de broderie\n(b) Renfort thermocollant (support) ';

  @override
  String get t_embbasic22_02 => '[2. Tissus ne pouvant pas être repassés]';

  @override
  String get t_embbasic22_02_02 => '\nPlacez le renfort de broderie sous le tissu sans le fixer, placez ensuite le tissu et le renfort dans le cadre de broderie.';

  @override
  String get t_embbasic22_03 => '[3. Tissus fins]';

  @override
  String get t_embbasic22_03_02 => '\nUtilisez un renfort de broderie soluble (vendu séparément) pour des résultats optimaux. Une fois le renfort dissout dans l\'eau, la finition de votre ouvrage sera parfaite.';

  @override
  String get t_embbasic22_04 => '[4. Tissus duvetés]';

  @override
  String get t_embbasic22_04_02 => '\nLorsque vous utilisez des tissus qui ne peuvent pas être repassés (tels que les serviettes de bain ou les tissus avec des boucles qui s\'élargissent lors du repassage) , placez le renfort de broderie sous le tissu sans le fixer, placez ensuite le tissu et le renfort dans le cadre de broderie, ou utilisez un renfort de broderie soluble (vendu séparément) .';

  @override
  String get t_embbasic23 => '[Réglage de la tension du fil]';

  @override
  String get t_embbasic23_01 => 'Lorsque vous brodez, la tension des fils doit être réglée de sorte à pouvoir voir légèrement le fil supérieur à l\'envers du tissu.';

  @override
  String get t_embbasic23_01_01 => '1. Tension des fils correcte';

  @override
  String get t_embbasic23_01_02 => '2. La tension du fil supérieur est trop importante';

  @override
  String get t_embbasic23_01_03 => '3. La tension du fil supérieur est insuffisante';

  @override
  String get t_embbasic23_02 => '[1. Tension des fils correcte]';

  @override
  String get t_embbasic23_02_02 => '\nLe motif est visible à l\'envers du tissu. Si la tension des fils n\'est pas réglée correctement, le motif sera mal fini. Le tissu risque de froncer ou le fil pourrait casser.';

  @override
  String get t_embbasic23_03 => '[2. La tension du fil supérieur est trop importante]';

  @override
  String get t_embbasic23_03_02 => '\nSi la tension du fil supérieur est trop importante, le fil de la canette est visible sur l\'endroit du tissu.';

  @override
  String get t_embbasic23_03_03 => '\nAppuyez sur - pour relâcher la tension du fil supérieur.';

  @override
  String get t_embbasic23_04 => '[3. La tension du fil supérieur est insuffisante]';

  @override
  String get t_embbasic23_04_02 => '\nSi la tension du fil supérieur est insuffisante, le fil supérieur se défera, les nœuds de fils seront lâches ou des boucles apparaîtront à l\'endroit du tissu.';

  @override
  String get t_embbasic23_04_03 => '\nAppuyez sur + pour augmenter la tension des fils.';

  @override
  String get t_trouble22 => '[Le fil supérieur casse]';

  @override
  String get t_trouble22_01 => '*Cause 1\nL\'enfilage n\'est pas correct sur la machine (vous n\'avez pas utilisé le bon couvercle de bobine ou il n\'est pas bien enfoncé, le fil n\'a pas été pris dans l\'enfileur de barre d\'aiguille, etc.). \n\n*Solution\nEnfilez de nouveau correctement le fil dans la machine.';

  @override
  String get t_trouble22_02 => '*Cause 2\nIl y a un nœud dans le fil ou il est emmêlé.\n\n*Solution\nEnfilez de nouveau les fils supérieur et inférieur.';

  @override
  String get t_trouble22_03 => '*Cause 3\nLe fil est trop épais pour l\'aiguille.\n\n*Solution\nVérifiez les combinaisons d\'aiguille et de fil.';

  @override
  String get t_trouble22_04 => '*Cause 4\nLa tension du fil supérieur est trop importante.\n\n*Solution\nRéglez la tension du fil.';

  @override
  String get t_trouble22_05 => '*Cause 5\nLe fil est emmêlé.\n\n*Solution\nA l\'aide, par exemple, d\'une paire de ciseaux, coupez le fil emmêlé et retirez-le du boîtier de la canette, etc.';

  @override
  String get t_trouble22_06 => '*Cause 6\nL\'aiguille est de travers, tordue ou la pointe est émoussée.\n\n*Solution\nRemplacez l\'aiguille.';

  @override
  String get t_trouble22_07 => '*Cause 7\nL\'aiguille n\'est pas installée correctement.\n\n*Solution\nRéinstallez correctement l\'aiguille.';

  @override
  String get t_trouble22_08 => '*Cause 8\nDes rayures sont présentes auprès du trou de la plaque d\'aiguille.\n\n*Solution\nRemplacez la plaque d\'aiguille ou consultez votre revendeur le plus proche.';

  @override
  String get t_trouble22_09 => '*Cause 9\nDes rayures sont présentes auprès du trou du pied-de-biche.\n\n*Solution\nRemplacez le pied-de-biche ou consultez votre revendeur le plus proche.';

  @override
  String get t_trouble22_10 => '*Cause 10\nDes rayures sont présentes sur le boîtier de la canette.\n\n*Solution\nRemplacez le boîtier ou consultez votre revendeur le plus proche.';

  @override
  String get t_trouble23 => '[Le fil de la canette casse]';

  @override
  String get t_trouble23_01 => '*Cause 1\nLa canette n\'est pas placée correctement.\n\n*Solution\nRéinstallez correctement le fil de la canette.';

  @override
  String get t_trouble23_02 => '*Cause 2\nDes rayures sont présentes sur la canette ou elle ne tourne pas correctement.\n\n*Solution\nRemplacez la canette.';

  @override
  String get t_trouble23_03 => '*Cause 3\nLe fil est emmêlé.\n\n*Solution\nA l\'aide, par exemple, d\'une paire de ciseaux, coupez le fil emmêlé et retirez-le du boîtier de la canette, etc.';

  @override
  String get t_trouble24 => '[Des points ont été sautés]';

  @override
  String get t_trouble24_01 => '*Cause 1\nL\'enfilage est incorrect dans la machine.\n\n*Solution\nRelisez la procédure d\'enfilage de la machine et enfilez de nouveau le fil correctement.';

  @override
  String get t_trouble24_02 => '*Cause 2\nVous utilisez le mauvais type d\'aiguille ou de fil pour le tissu sélectionné.\n\n*Solution\nConsultez le tableau \"Combinaison Tissu/Fil/Aiguille\".';

  @override
  String get t_trouble24_03 => '*Cause 3\nL\'aiguille est de travers, tordue ou la pointe est émoussée.\n\n*Solution\nRemplacez l\'aiguille.';

  @override
  String get t_trouble24_04 => '*Cause 4\nL\'aiguille n\'est pas installée correctement.\n\n*Solution\nRéinstallez correctement l\'aiguille.';

  @override
  String get t_trouble24_05 => '*Cause 5\nL\'aiguille est défectueuse.\n\n*Solution\nRemplacez l\'aiguille.';

  @override
  String get t_trouble24_06 => '*Cause 6\nDe la poussière ou des peluches se sont accumulées sous la plaque d\'aiguille.\n\n*Solution\nRetirez la poussière ou les peluches à l\'aide de la brosse.';

  @override
  String get t_trouble25 => '[Le tissu fronce]';

  @override
  String get t_trouble25_01 => '*Cause 1\nL\'enfilage du fil supérieur ou de la canette est incorrect.\n\n*Solution\nRelisez la procédure d\'enfilage de la machine et enfilez de nouveau le fil correctement.';

  @override
  String get t_trouble25_02 => '*Cause 2\nLe couvercle de bobine n\'est pas installé correctement.\n\n*Solution\nRelisez la procédure de fixation du couvercle de bobine et fixez-le de nouveau.';

  @override
  String get t_trouble25_03 => '*Cause 3\nVous utilisez le mauvais type d\'aiguille ou de fil pour le tissu sélectionné.\n\n*Solution\nConsultez le tableau \"Combinaison Tissu/Fil/Aiguille\".';

  @override
  String get t_trouble25_04 => '*Cause 4\nL\'aiguille est de travers, tordue ou la pointe est émoussée.\n\n*Solution\nRemplacez l\'aiguille.';

  @override
  String get t_trouble25_05 => '*Cause 5\nLes points sont trop longs lorsque vous cousez sur des tissus fins.\n\n*Solution\nDiminuez la longueur de point.';

  @override
  String get t_trouble25_06 => '*Cause 6\nLa tension du fil n\'est pas réglée correctement.\n\n*Solution\nRéglez la tension du fil.';

  @override
  String get t_trouble25_07 => '*Cause 7\nMauvais pied-de-biche\n\n*Solution\nUtilisez le bon pied-de-biche.';

  @override
  String get t_trouble26 => '[La machine est bruyante]';

  @override
  String get t_trouble26_01 => '*Cause 1\nIl y a de la poussière ou des peluches dans les griffes d\'entraînement.\n\n*Solution\nRetirez la poussière ou les peluches.';

  @override
  String get t_trouble26_02 => '*Cause 2\nDes bouts de fil sont pris dans le boîtier de la canette.\n\n*Solution\nNettoyez le boîtier de la canette.';

  @override
  String get t_trouble26_03 => '*Cause 3\nL\'enfilage supérieur n\'est pas correct.\n\n*Solution\nRelisez la procédure d\'enfilage de la machine et enfilez-la de nouveau.';

  @override
  String get t_trouble26_04 => '*Cause 4\nDes rayures sont présentes sur le boîtier de la canette.\n\n*Solution\nRemplacez le boîtier ou consultez votre revendeur le plus proche.';

  @override
  String get t_trouble27 => '[Impossible d’utiliser l’enfileur d’aiguille]';

  @override
  String get t_trouble27_01 => '*Cause 1\nL\'aiguille ne se trouve pas dans la position correcte.\n\n*Solution\nAppuyez sur la touche \"Positionnement aiguille\" pour relever l\'aiguille.';

  @override
  String get t_trouble27_02 => '*Cause 2\nLe crochet d\'enfilage ne passe pas par le chas de l\'aiguille.\n\n*Solution\nAppuyez sur la touche \"Positionnement aiguille\" pour relever l\'aiguille.';

  @override
  String get t_trouble27_03 => '*Cause 3\nL\'aiguille n\'est pas installée correctement.\n\n*Solution\nRéinstallez correctement l\'aiguille.';

  @override
  String get t_trouble28 => '[La tension du fil n\'est pas correcte]';

  @override
  String get t_trouble28_01 => '*Cause 1\nL\'enfilage supérieur n\'est pas correct.\n\n*Solution\nRelisez la procédure d\'enfilage de la machine et enfilez-la de nouveau.';

  @override
  String get t_trouble28_02 => '*Cause 2\nLa canette n\'est pas placée correctement.\n\n*Solution\nRéinstallez la canette.';

  @override
  String get t_trouble28_03 => '*Cause 3\nVous utilisez le mauvais type d\'aiguille ou de fil pour le tissu sélectionné.\n\n*Solution\nConsultez le tableau \"Combinaison Tissu/Fil/Aiguille\".';

  @override
  String get t_trouble28_04 => '*Cause 4\nLe support du pied-de-biche n\'est pas fixé correctement.\n\n*Solution\nRéinstallez correctement le support du pied-de-biche.';

  @override
  String get t_trouble28_05 => '*Cause 5\nLa tension du fil n\'est pas réglée correctement.\n\n*Solution\nRéglez la tension du fil.';

  @override
  String get t_trouble29 => '[Le motif de caractères n\'est pas correct]';

  @override
  String get t_trouble29_01 => '*Cause 1\nMauvais pied-de-biche.\n\n*Solution\nFixez le bon pied-de-biche.';

  @override
  String get t_trouble29_02 => '*Cause 2\nLes réglages de motifs ont été mal entrés.\n\n*Solution\nModifiez les réglages du motif.';

  @override
  String get t_trouble29_03 => '*Cause 3\nVous n\'avez pas utilisé un renfort de broderie sur du tissu fin ou élastique.\n\n*Solution\nFixez un renfort de broderie.';

  @override
  String get t_trouble29_04 => '*Cause 4\nLa tension du fil n\'est pas réglée correctement.\n\n*Solution\nRéglez la tension du fil.';

  @override
  String get t_trouble30 => '[Le motif de broderie n\'est pas cousu correctement]';

  @override
  String get t_trouble30_01 => '*Cause 1\nLe fil est emmêlé.\n\n*Solution\nA l\'aide, par exemple, d\'une paire de ciseaux, coupez le fil emmêlé et retirez-le du boîtier de la canette, etc.';

  @override
  String get t_trouble30_02 => '*Cause 2\nLe tissu n\'a pas été inséré correctement dans le cadre (le tissu n\'était pas tendu, etc.) .\n\n*Solution\nSi le tissu n\'est pas suffisamment tendu dans le cadre, la qualité du motif risque de ne pas être satisfaisante ou il se peut qu\'il rétrécisse. Fixez correctement le tissu dans le cadre.';

  @override
  String get t_trouble30_03 => '*Cause 3\nVous n\'avez pas utilisé de renfort de broderie.\n\n*Solution\nLorsque vous brodez sur des tissus élastiques, légers, à grosse trame ou sur des tissus susceptibles de provoquer un rétrécissement des motifs, utilisez toujours un renfort de broderie. Pour savoir quel renfort utiliser, consultez votre revendeur le plus proche.';

  @override
  String get t_trouble30_04 => '*Cause 4\nUn objet se trouvait près de la machine et le chariot ou le cadre de broderie a touché l\'objet pendant la couture.\n\n*Solution\nSi le cadre heurte quelque chose pendant la couture, la qualité du motif risque d\'être médiocre. Ne placez aucun objet dans la zone de déplacement du cadre pendant l\'exécution du travail de couture.';

  @override
  String get t_trouble30_05 => '*Cause 5\nLe tissu à l\'extérieur des bords du cadre interfère avec le bras de couture et empêche l\'unité de broderie de se déplacer.\n\n*Solution\nFixez de nouveau le tissu dans le cadre de broderie afin que le tissu qui dépasse se trouve de l\'autre côté du bras de couture et faites pivoter le motif sur 180 degrés.';

  @override
  String get t_trouble30_06 => '*Cause 6\nLe tissu est trop lourd et l\'unité de broderie ne peut pas se déplacer librement.\n\n*Solution\nPlacez un gros livre épais ou un objet de ce type sous la tête du bras pour soulever légèrement le côté le plus lourd et pour qu\'il reste horizontal.';

  @override
  String get t_trouble30_07 => '*Cause 7\nLe tissu pend sur le bord de la table.\n\n*Solution\nSi le tissu pend sur le bord de la table pendant la broderie, l\'unité de broderie ne pourra pas se déplacer librement. Placez le tissu de façon à ce qu\'il ne pende pas sur le bord de la table (ou maintenez le tissu pour éviter qu\'il traîne) .';

  @override
  String get t_trouble30_08 => '*Cause 8\nLe tissu est accroché quelque part.\n\n*Solution\nArrêtez la machine et positionnez le tissu de façon qu\'il ne puisse pas être accroché.';

  @override
  String get t_trouble30_09 => '*Cause 9\nLe cadre de broderie a été retiré pendant le travail de couture (par exemple, pour changer la canette) . Le pied-de-biche a été heurté ou déplacé pendant le retrait ou la fixation du cadre de broderie, ou l\'unité de broderie a été déplacée.\n\n*Solution\nSi vous heurtez le pied-de-biche ou si l\'unité de broderie est déplacée pendant la couture, le motif ne sera pas effectué correctement. Faites attention lorsque vous retirez ou remettez le cadre de broderie pendant la couture.';

  @override
  String get t_trouble31 => '[L\'aiguille casse]';

  @override
  String get t_trouble31_01 => '*Cause 1\nL\'aiguille n\'est pas installée correctement.\n\n*Solution\nRéinstallez correctement l\'aiguille.';

  @override
  String get t_trouble31_02 => '*Cause 2\nLa vis du support de l\'aiguille n\'est pas serrée.\n\n*Solution\nResserrez-la.';

  @override
  String get t_trouble31_03 => '*Cause 3\nL\'aiguille est de travers ou elle est tordue.\n\n*Solution\nRemplacez l\'aiguille.';

  @override
  String get t_trouble31_04 => '*Cause 4\nVous utilisez le mauvais type d\'aiguille ou de fil pour le tissu sélectionné.\n\n*Solution\nConsultez le tableau \"Combinaison Tissu/Fil/Aiguille\".';

  @override
  String get t_trouble31_05 => '*Cause 5\nMauvais pied-de-biche.\n\n*Solution\nUtilisez le pied-de-biche conseillé.';

  @override
  String get t_trouble31_06 => '*Cause 6\nLa tension du fil supérieur est trop importante.\n\n*Solution\nRéglez la tension du fil.';

  @override
  String get t_trouble31_07 => '*Cause 7\nVous avez tiré sur le tissu pendant la couture.\n\n*Solution\nNe tirez pas sur le tissu pendant la couture.';

  @override
  String get t_trouble31_08 => '*Cause 8\nLe couvercle de bobine n\'est pas installé correctement.\n\n*Solution\nRelisez la procédure de fixation du couvercle de bobine et fixez-le de nouveau.';

  @override
  String get t_trouble31_09 => '*Cause 9\nDes rayures sont présentes autour des trous de la plaque d\'aiguille.\n\n*Solution\nRemplacez la plaque d\'aiguille ou consultez votre revendeur le plus proche.';

  @override
  String get t_trouble31_10 => '*Cause 10\nDes rayures sont présentes autour des trous du pied-de-biche.\n\n*Solution\nRemplacez le pied-de-biche ou consultez votre revendeur le plus proche.';

  @override
  String get t_trouble31_11 => '*Cause 11\nDes rayures sont présentes sur le boîtier de la canette.\n\n*Solution\nRemplacez le boîtier ou consultez votre revendeur le plus proche.';

  @override
  String get t_trouble31_12 => '*Cause 12\nL\'aiguille est défectueuse.\n\n*Solution\nRemplacez l\'aiguille.';

  @override
  String get t_trouble32 => '[Le tissu n\'est pas entraîné dans la machine]';

  @override
  String get t_trouble32_01 => '*Cause 1\nLes griffes d\'entraînement sont en position basse.\n\n*Solution\nAppuyez sur la touche de mode en mouvement libre, puis tournez le volant pour relever les griffes d\'entraînement.';

  @override
  String get t_trouble32_02 => '*Cause 2\nLes points sont trop près les uns des autres.\n\n*Solution\nAugmentez le réglage de longueur des points.';

  @override
  String get t_trouble32_03 => '*Cause 3\nMauvais pied-de-biche.\n\n*Solution\nUtilisez le bon pied-de-biche.';

  @override
  String get t_trouble32_04 => '*Cause 4\nL\'aiguille est de travers, tordue ou la pointe est émoussée.\n\n*Solution\nRemplacez l\'aiguille.';

  @override
  String get t_trouble32_05 => '*Cause 5\nLe fil est emmêlé.\n\n*Solution\nCoupez le fil emmêlé et retirez-le du boîtier de la canette.';

  @override
  String get t_trouble33 => '[La machine ne fonctionne pas]';

  @override
  String get t_trouble33_01 => '*Cause 1\nAucun motif n\'a été sélectionné.\n\n*Solution\nSélectionnez un motif.';

  @override
  String get t_trouble33_02 => '*Cause 2\nLa touche \"Marche/arrêt\" n\'a pas été enfoncée.\n\n*Solution\nAppuyez sur la touche \"Marche/arrêt\".';

  @override
  String get t_trouble33_03 => '*Cause 3\nLa machine n\'est pas sous tension.\n\n*Solution\nMettez la machine sous tension à l\'aide de l\'interrupteur d\'alimentation principal.';

  @override
  String get t_trouble33_04 => '*Cause 4\nLe pied-de-biche n\'a pas été abaissé.\n\n*Solution\nAbaissez le pied-de-biche.';

  @override
  String get t_trouble33_05 => '*Cause 5\nLa touche \"Marche/arrêt\" a été enfoncée alors que la pédale était connectée.\n\n*Solution\nRetirez la pédale ou utilisez-la pour activer la machine.';

  @override
  String get t_trouble33_06 => '*Cause 6\nLe levier de contrôle de la vitesse est réglé pour contrôler la largeur du point zigzag.\n\n*Solution\nUtilisez la pédale plutôt que la touche \"Marche/arrêt\" pour activer la machine.';

  @override
  String get t_trouble34 => '[L\'unité de broderie ne fonctionne pas]';

  @override
  String get t_trouble34_01 => '*Cause 1\nAucun motif n\'a été sélectionné.\n\n*Solution\nSélectionnez un motif.';

  @override
  String get t_trouble34_02 => '*Cause 2\nLa machine n\'est pas sous tension.\n\n*Solution\nMettez la machine sous tension à l\'aide de l\'interrupteur d\'alimentation principal.';

  @override
  String get t_trouble34_03 => '*Cause 3\nL\'unité de broderie n\'est pas installée correctement.\n\n*Solution\nFixez correctement l\'unité de broderie.';

  @override
  String get t_trouble34_04 => '*Cause 4\nLe cadre de broderie a été installé avant l\'initialisation de l\'unité.\n\n*Solution\nEffectuez correctement le processus d\'initialisation.';

  @override
  String get t_trouble35 => ' [Le fil est emmêlé sur l\'envers du tissu]';

  @override
  String get t_trouble35_01 => ' *Cause 1\nLe contraste de l\'écran LCD n\'est pas réglé correctement.\n\n*Solution\nRéglez le contraste de l\'écran LCD.';

  @override
  String get t_maintenance36 => '[Nettoyage du logement et du boîtier de la canette]';

  @override
  String get t_maintenance36_00 => 'Si de la poussière ou des salissures s\'accumulent dans le logement ou le boîtier de la canette, la machine risque de ne pas fonctionner correctement et il la fonction de détection du fil de canette risque de ne plus fonctionner. Pour des résultats optimaux, nettoyez régulièrement votre machine.\nAvant de mettre la machine hors tension, consultez la procédure décrite ci-dessous.';

  @override
  String get t_maintenance36_01 => '\n1. Appuyez sur la touche \"Positionnement aiguille\" pour relever l\'aiguille.';

  @override
  String get t_maintenance36_02 => '2. Abaissez le pied-de-biche.';

  @override
  String get t_maintenance36_03 => '(a) Marche\n(b) Arrêt\n\n3. Mettez la machine hors tension.';

  @override
  String get t_maintenance36_04 => '4. Retirez le support du pied-de-biche et l\'aiguille.';

  @override
  String get t_maintenance36_05_11 => '5. Retirez le plateau et compartiment d\'accessoires ou l\'unité de broderie si l\'un d\'eux est fixé.\nFaites coulisser le levier de la plaque à aiguille vers vous.\nLa plaque à aiguille s\'ouvre.';

  @override
  String get t_maintenance36_05_12 => '(a)Faites coulisser vers vous.\n';

  @override
  String get t_maintenance36_05_13 => '6. Faites glisser la plaque à aiguille avec votre main droite pour la retirer.';

  @override
  String get t_maintenance36_05_14 => '(a) Capot de la plaque de l\'aiguille\n';

  @override
  String get t_maintenance36_05_15 => '\n7. Saisissez le boîtier de la canette, puis soulevez-le légèrement vers l\'extérieur.';

  @override
  String get t_maintenance36_07_02 => '(a) Logement de la canette';

  @override
  String get t_maintenance36_08 => '\n8. Utilisez la brosse de nettoyage ou un aspirateur pour retirer les peluches et la poussière du boîtier et de la zone environnante.\n\n* N\'appliquez pas d\'huile sur le logement de la canette.';

  @override
  String get t_maintenance36_08_02 => '(a) Brosse de nettoyage\n(b) Boîtier';

  @override
  String get t_embbasic18_04_21 => '\n9. Insérez le boîtier de la canette de sorte que la marque ▲ du boîtier soit alignée avec la marque ● de la machine.';

  @override
  String get t_embbasic18_04_22 => '(a)  Marque ▲ sur le boîtier de la canette\n(b)  Marque ● sur la machine';

  @override
  String get t_embbasic18_04_23 => '\n10. Insérez les ergots de la plaque à aiguille dans les encoches de la machine.';

  @override
  String get t_embbasic18_04_24 => '(a) Ergots\n(b) Encoches';

  @override
  String get t_embbasic18_04_25 => '11. Appuyez sur le côté droit de la plaque à aiguille pour la maintenir en place.';

  @override
  String get t_sewing01_00 => 'Sélection du type de couture';

  @override
  String get t_sewing01_00_01 => '1-01:couture normale\n1-05:couture de renfort\n1-06:couture de tissus élastiques';

  @override
  String get t_sewing01_00_01_s_normal => 'Couture normale';

  @override
  String get t_sewing01_00_01_s_reinforced => 'Couture de renfort';

  @override
  String get t_sewing01_00_01_s_stretch => 'Couture de tissus élastiques';

  @override
  String get t_sewing01 => '[Points droits]';

  @override
  String get t_sewing01_01 => '\n1. Fixez le pied-de-biche \"J\".\nTenez l\'extrémité du fil et le tissu de votre main gauche et faites tourner le volant de votre main droite pour introduire l\'aiguille dans le tissu.';

  @override
  String get t_sewing01_01_02 => '(a) Point de démarrage de la couture';

  @override
  String get t_sewing01_02 => '\n2. Abaissez le pied-de-biche et maintenez la touche \"Point inverse/renfort\" enfoncée pour coudre 3 ou 4 points. La machine coud des points inverses (ou points de renfort) .\nAppuyez sur la touche \"Marche/arrêt\" pour commencer à coudre. La machine commence à coudre lentement.';

  @override
  String get t_sewing01_03 => '(a) Points inverses';

  @override
  String get t_sewing01_04 => '3. A la fin de l\'ouvrage, maintenez la touche \"Point inverse/renfort\" enfoncée pour coudre 3 ou 4 points inverses (ou de renfort) à la fin de la couture.';

  @override
  String get t_sewing01_05 => '4. Une fois les points cousus, appuyez sur la touche \"Coupe-fil\" pour couper les fils.\n\n* Lorsque les touches coupe-fil automatique et de point de renfort automatique sont sélectionnées à l\'écran, la machine exécute des points inverses (ou de renfort) au début de la couture lorsque vous appuyez sur la touche \"Marche/arrêt\".Appuyez sur la touche \"Point inverse/renfort\" pour coudre des points inverses (ou de renfort) et pour couper automatiquement le fil à la fin du travail de couture.';

  @override
  String get t_sewing01_06 => '\n * Modification de la position de l\'aiguille \n Lorsque vous sélectionnez des motifs de points avec aiguille à gauche ou au milieu, vous pouvez utiliser les touches « + » et « - » dans l\'écran « L/R Shift » pour modifier la position de l\'aiguille. Réglez la distance entre le bord droit du pied-de-biche et l\'aiguille en fonction du rabat et, pendant la couture, alignez le bord du pied-de-biche avec le bord du tissu pour obtenir une finition parfaite.';

  @override
  String get t_sewing01_07 => '(a) Rabat';

  @override
  String get t_sewing02 => '[Surfilage]';

  @override
  String get t_sewing02_00 => 'Sélection du type de couture';

  @override
  String get t_sewing02_00_01 => '1-16:Tissu fin et moyen\n1-17:Tissu épais\n1-18:Tissus moyens, lourds et facilement effilochables\n1-19:Tissu élastique\n1-20:Tissu élastique épais et moyen\n1-21:Tissu élastique';

  @override
  String get t_sewing02_00_01_f_lightandmedium => 'Tissu fin et moyen';

  @override
  String get t_sewing02_00_01_f_heavy => 'Tissu épais';

  @override
  String get t_sewing02_00_01_f_mediumstretch => 'Tissus moyens, lourds et facilement effilochables';

  @override
  String get t_sewing02_00_01_f_stretch1 => 'Tissu élastique';

  @override
  String get t_sewing02_00_01_f_thickandmediumstretch => 'Tissu élastique épais et moyen';

  @override
  String get t_sewing02_00_01_f_stretch2 => 'Tissu élastique';

  @override
  String get t_sewing02_01 => '1. Fixez le pied-de-biche \"G\". Abaissez le pied-de-biche afin que son guide se trouve exactement contre le bord du tissu.';

  @override
  String get t_sewing02_02 => '\n2. Cousez le long du guide du pied-de-biche.';

  @override
  String get t_sewing02_02_02 => '(a) Guide';

  @override
  String get t_sewing02_03 => '\n1. Fixez le pied-de-biche \"J\". Cousez avec l\'aiguille dépassant légèrement du tissu.';

  @override
  String get t_sewing02_04 => '(a) Point de chute de l\'aiguille';

  @override
  String get t_sewing02_05 => '\n*Une fois la largeur de point réglée, tournez le volant vers vous à la main et vérifiez que l\'aiguille ne touche pas le pied-de-biche. Sinon, elle risque de se casser et vous pourriez vous blesser.';

  @override
  String get t_sewing02_05_02 => '(a) L\'aiguille ne doit pas le toucher';

  @override
  String get t_sewing04 => '[Points feston]';

  @override
  String get t_sewing04_01 => 'Ce point plumetis en forme de vagues s’appelle « Point feston ». Vous pouvez utiliser ce motif de point pour décorer les bords de vos cols de chemisier et de vos mouchoirs, ou l’utiliser pour accentuer un ourlet.\nUn spray adhésif temporaire est parfois nécessaire pour les tissus légers. Effectuez un test de couture sur le tissu avant de commencer la couture de votre ouvrage.';

  @override
  String get t_sewing04_02 => '1. Fixez le pied-de-biche \"N+\". Cousez des points feston le long du bord du tissu. Ne cousez pas directement sur le bord du tissu.';

  @override
  String get t_sewing04_03 => '2. Coupez les bords de la couture en veillant à ne pas couper les points.';

  @override
  String get t_sewing05_00 => 'Sélection du type de couture';

  @override
  String get t_sewing05_00_01 => '4-01:Tissu fin et moyen (pour les trous horizontaux)\n4-07:Tissu fin ou moyen\n4-10:Tissu élastique à grosse trame\n4-11:Tissu élastique\n4-13:Costumes, manteau\n4-14:Jean, pantalons\n4-15:Manteaux épais';

  @override
  String get t_sewing05_00_01_f_lighttomediumhorizhole => 'Tissu fin et moyen (pour les trous horizontaux)';

  @override
  String get t_sewing05_00_01_f_lighttomedium => 'Tissu fin ou moyen';

  @override
  String get t_sewing05_00_01_f_stretchweaves => 'Tissu élastique à grosse trame';

  @override
  String get t_sewing05_00_01_f_stretch => 'Tissu élastique';

  @override
  String get t_sewing05_00_01_f_suitsandovercoat => 'Costumes, manteau';

  @override
  String get t_sewing05_00_01_f_jeansandtrousers => 'Jean, pantalons';

  @override
  String get t_sewing05_00_01_f_thickcoats => 'Manteaux épais';

  @override
  String get t_sewing05 => '[Boutonnières]';

  @override
  String get t_sewing05_02 => '1. Faites des repères pour marquer la position et la longueur de la boutonnière sur le tissu.';

  @override
  String get t_sewing05_03 => '\n2. Fixez le pied pour boutonnières \"A+\". Tirez sur la plaque du support de bouton du pied-de-biche et insérez le bouton qui devra passer par la boutonnière. Repoussez la plaque pour bloquer le bouton.\n\n* La taille de la boutonnière est déterminée par celle du bouton inséré dans la plaque du support de bouton.';

  @override
  String get t_sewing05_04 => '(a) Plaque du support de bouton';

  @override
  String get t_sewing05_05 => '\n3. Alignez le pied-de-biche sur le repère tracé sur le tissu et abaissez le pied-de-biche.';

  @override
  String get t_sewing05_06 => '(a) Repère tracé sur le tissu\n(b) Repères sur le pied-de-biche';

  @override
  String get t_sewing05_07 => '\n4. Abaissez le levier boutonnières et placez-le derrière le crochet métallique du pied pour boutonnière.';

  @override
  String get t_sewing05_08 => '(a) Support métallique';

  @override
  String get t_sewing05_09 => '4. Tenez légèrement l\'extrémité du fil supérieur et commencez à coudre. Pendant la couture de la boutonnière, faites avancer doucement le tissu à la main.\nUne fois le travail de couture terminé, la machine coud automatiquement des points de renfort puis s\'arrête.';

  @override
  String get t_sewing05_10 => '\n5. Insérez une épingle à l\'intérieur d\'un des deux points d\'arrêt. Insérez ensuite le découd-vite au centre de la boutonnière et coupez le tissu en direction de l\'épingle.';

  @override
  String get t_sewing05_11 => '(a) Epingle de faufilage\n(b) Découd-vite';

  @override
  String get t_sewing05_12 => '\n[Pour les boutonnières Trou de serrure]\nUtilisez le perce-œillet pour faire un trou dans la partie arrondie de la boutonnière. Insérez ensuite une épingle à l\'intérieur de l\'un des deux points d\'arrêt, insérez un découd-vite dans le trou réalisé avec le perce-œillet et coupez le tissu en direction de l\'épingle.';

  @override
  String get t_sewing05_13 => '(a) Perce œillet\n(b) Epingle de faufilage';

  @override
  String get t_sewing05_14 => '*Couture de tissus élastiques\nLorsque vous cousez des tissus élastiques avec 4-10 ou 4-11, cousez les points de boutonnière sur un fil de guipage.';

  @override
  String get t_sewing05_16 => '\n1. Accrochez le fil de guipage à l\'arrière du pied-de-biche \"A+\". Faites passer les extrémités des fils dans les rainures situées à l\'avant du pied-de-biche et nouez-les momentanément à cet emplacement.';

  @override
  String get t_sewing05_17 => '(a) Fil supérieur';

  @override
  String get t_sewing05_18 => '2. Abaissez le pied-de-biche et commencez à coudre.';

  @override
  String get t_sewing05_19 => '3. Une fois le travail de couture terminé, tirez doucement sur le fil de guipage afin qu\'il soit bien tendu et coupez les extrémités qui dépassent.';

  @override
  String get t_sewing05_20 => '\n*Boutons à forme spéciale ou qui n\'entrent pas dans la plaque de support de bouton\nUtilisez les repères de l\'échelle du pied-de-biche pour régler la taille de la boutonnière. Chaque repère de l\'échelle du pied-de-biche correspond à 5 mm (3/16 de pouce environ) . Ajoutez le diamètre du bouton et son épaisseur, puis réglez la plaque sur la valeur ainsi obtenue.';

  @override
  String get t_sewing05_21 => '(a) Echelle du pied-de-biche\n(b) Plaque du support de bouton\n(c) Dimension totale diamètre + épaisseur\n(d) 5 mm (3/16 de pouce environ) ';

  @override
  String get t_sewing05_22 => '\nExemple :\nPour un bouton d\'un diamètre de 15 mm (9/16 de pouce) et d\'une épaisseur de 10 mm (3/8 de pouce) , l\'échelle doit être réglée sur 25 mm (1 pouce environ) .';

  @override
  String get t_sewing05_23 => '(a) 10 mm (3/8 de pouce environ) \n(b) 15 mm (9/16 de pouce environ) ';

  @override
  String get t_sewing06 => '[Couture bouton]';

  @override
  String get t_sewing06_01 => 'N\'utilisez pas la fonction de coupure de fil automatique lorsque vous cousez des boutons. Sinon, vous perdrez les extrémités des fils. \n\n1. Fixez le pied de couture bouton \"M\", faites glisser le bouton le long de la plaque métallique, placez-le dans le pied-de-biche et abaissez le pied-de-biche.';

  @override
  String get t_sewing06_01_02 => '(a) Bouton \n(b) Plaque métallique\n';

  @override
  String get t_sewing06_02 => '2. Tournez le volant pour vérifier si l\'aiguille passe bien dans chaque trou du bouton. Ensuite, tenez légèrement l\'extrémité du fil supérieur et commencez à coudre. Une fois le travail de couture terminé, la machine s\'arrête automatiquement.';

  @override
  String get t_sewing06_03 => '3. Sur l\'envers du tissu, tirez l\'extrémité du fil de la canette afin de tirer le fil supérieur vers l\'envers du tissu. Nouez les extrémités des deux fils et coupez-les.';

  @override
  String get t_sewing06_04 => '*Fixation de boutons à 4 trous';

  @override
  String get t_sewing06_05 => 'Cousez les deux trous qui se trouvent le plus près de vous. Relevez ensuite le pied-de-biche et déplacez le tissu afin que l\'aiguille passe dans les deux trous suivants et cousez-les de la même manière.';

  @override
  String get t_sewing06_06 => '*Ajout d\'un talon sur le bouton\n\n1. Tirez le levier de talon vers vous avant de commencer à coudre.';

  @override
  String get t_sewing06_07 => '(a) Levier de talon\n';

  @override
  String get t_sewing06_08 => '2. Tirez les deux extrémités du fil supérieur entre le bouton et le tissu, enroulez-les autour du talon et nouez-les ensuite solidement.\nSur l\'envers du tissu, nouez les extrémités du fil de la canette qui dépassent au début et à la fin du travail de couture.';

  @override
  String get t_sewing07 => '[Points d\'arrêt]';

  @override
  String get t_sewing07_01 => 'Les points d\'arrêt vous permettent de renforcer les zones soumises à d\'importantes tractions, comme les coins des poches.';

  @override
  String get t_sewing07_02 => '\n1. Fixez le pied pour boutonnière \"A+\" et réglez l\'échelle sur la longueur du point d\'arrêt que vous souhaitez coudre.';

  @override
  String get t_sewing07_03 => '(a) Echelle du pied-de-biche\n(b) Dimension totale\n(c) 5 mm (3/16 de pouce environ) ';

  @override
  String get t_sewing07_04 => '2. Positionnez le tissu de façon à ce que la poche avance vers vous pendant le travail de couture.';

  @override
  String get t_sewing07_05 => '\n3. Vérifiez le premier point de chute de l\'aiguille et abaissez le pied-de-biche.';

  @override
  String get t_sewing07_06 => '(a) 2 mm (1/16 de pouce environ) ';

  @override
  String get t_sewing07_09 => '4. Tenez légèrement l\'extrémité du fil supérieur et commencez à coudre. Une fois le travail de couture terminé, la machine coud des points de renfort puis s\'arrête automatiquement.';

  @override
  String get t_sewing07_10 => '\n*Points d\'arrêt sur des tissus épais\nPlacez un morceau de tissu ou de carton plié à côté du tissu cousu, afin que le pied pour boutonnière soit horizontal et pour faciliter l\'entraînement du tissu.';

  @override
  String get t_sewing07_11 => '(a) Pied-de-biche\n(b) Papier épais\n(c) Tissu';

  @override
  String get t_sewing08 => '[Fixation d\'une fermeture à glissière]';

  @override
  String get t_sewing08_00 => '\n*Fermeture à glissière centrée\nA utiliser pour les sacs et autres applications du même genre.\n\n1. Fixez le pied-de-biche \"J\" et cousez des points droits jusqu\'à l\'ouverture de la fermeture à glissière. Passez au point faufilage et cousez jusqu\'en haut du tissu.';

  @override
  String get t_sewing08_02 => '(a) Points faufilage\n(b) Points inverses\n(c) Extrémité de l\'ouverture de la fermeture à glissière\n(d) Envers';

  @override
  String get t_sewing08_03 => '\n2. Maintenez le rabat ouvert et fixez la fermeture à glissière avec des points faufilage au milieu de chaque côté du ruban de la fermeture à glissière.';

  @override
  String get t_sewing08_04 => '(a) Points faufilage\n(b) Fermeture à glissière\n(c) Envers';

  @override
  String get t_sewing08_05 => '\n3. Retirez le pied-de-biche \"J\". Alignez le côté droit du crochet du pied pour fermeture à glissière \"I\" avec le support du pied-de-biche et fixez le pied pour fermeture à glissière.';

  @override
  String get t_sewing08_06 => '(a) Droite\n(b) Gauche\n(c) Point de chute de l\'aiguille';

  @override
  String get t_sewing08_07 => '4. Effectuez une couture de renfort entre 7 et 10 mm (1/4 à 3/8 de pouce environ) du bord cousu du tissu et retirez le faufilage.';

  @override
  String get t_sewing08_08 => '\n*Fixation d\'une fermeture sur le côté\nPermet de fixer des fermetures à glissière sur des jupes ou des robes.\n1. Fixez le pied-de-biche \"J\" et cousez des points droits jusqu\'à l\'ouverture de la fermeture à glissière. Passez au point faufilage et cousez jusqu\'en haut du tissu.';

  @override
  String get t_sewing08_11 => '(a) Points inverses\n(b) Envers du tissu\n(c) Points faufilage\n(d) Extrémité de l\'ouverture de la fermeture à glissière';

  @override
  String get t_sewing08_12 => '\n2. Maintenez le rabat ouvert et alignez l\'ourlet plié le long des mailles de la fermeture à glissière, tout en conservant 3 mm (1/8 de pouce environ) d\'espace pour la couture.';

  @override
  String get t_sewing08_13 => '(a) Patte d\'ouverture de la fermeture à glissière\n(b) Envers du tissu\n(c) Mailles de la fermeture à glissière\n(d) Extrémité de l\'ouverture de la fermeture à glissière\n(e) 3 mm (1/8 de pouce) ';

  @override
  String get t_sewing08_14 => '\n3. Retirez le pied-de-biche \"J\". Alignez le côté droit du crochet du pied pour fermetures à glissière \"I\" avec le support du pied-de-biche et fixez le pied-de-biche.';

  @override
  String get t_sewing08_15 => '(a) Droite\n(b) Gauche\n(c) Point de chute de l\'aiguille';

  @override
  String get t_sewing08_16 => '\n4. Positionnez le pied-de-biche dans la marge de 3 mm (1/8 de pouce) . Commencez à coudre à partir de l\'extrémité de l\'ouverture de la fermeture à glissière. Cousez jusqu\'à environ 50 mm (2 pouces environ) du bord du tissu et arrêtez la machine. Refermez la fermeture à glissière et continuez à coudre jusqu\'au bord du tissu.';

  @override
  String get t_sewing08_17 => '(a) 50 mm (2 pouces environ) \n(b) 3 mm (1/8 de pouce environ) ';

  @override
  String get t_sewing08_18 => '\n5. Fermez la fermeture à glissière, retournez le tissu et cousez un point faufilage.';

  @override
  String get t_sewing08_19 => '(a) Avant de la jupe (envers du tissu) \n(b) Points faufilage\n(c) Avant de la jupe (endroit du tissu) \n(d) Arrière de la jupe (endroit du tissu) ';

  @override
  String get t_sewing08_20 => '\n6. Retirez le pied-de-biche et remettez-le de façon à ce que le côté gauche du crochet soit fixé au support du pied-de-biche.\n\n* Lorsque vous cousez le côté gauche de la fermeture à glissière, l\'aiguille doit tomber du côté droit du pied-de-biche. Lorsque vous cousez le côté droit de la fermeture à glissière, l\'aiguille doit tomber du côté gauche du pied-de-biche.';

  @override
  String get t_sewing08_21 => '(a) Droite\n(b) Gauche\n(c) Point de chute de l\'aiguille';

  @override
  String get t_sewing08_22 => '\n7. Positionnez le tissu de façon à ce que le côté gauche du pied-de-biche touche le bord des mailles de la fermeture à glissière. Cousez des points inverses en haut de la fermeture à glissière et continuez à coudre. Arrêtez de coudre à environ 50 mm (2 pouces environ) du bord du tissu, laissez l\'aiguille dans le tissu et retirez les points faufilage. Ouvrez la fermeture à glissière et terminez le travail de couture.';

  @override
  String get t_sewing08_23 => '(a) Points faufilage\n(b) 7 à 10 mm (1/4 à 3/8 de pouce environ) \n(c) Points inverses\n(d) 50 mm (2 pouces environ) ';

  @override
  String get t_sewing09_00 => 'Sélection du type de couture';

  @override
  String get t_sewing09_00_01 => 'Sélectionnez un de ces points pour coudre les ourlets ou les manchettes de vos robes, chemisiers, pantalons ou jupes.';

  @override
  String get t_sewing09_00_02 => '2-01:Autre tissu\n2-02:Tissu élastique';

  @override
  String get t_sewing09_00_02_f_other => 'Autre tissu';

  @override
  String get t_sewing09_00_02_f_stretch => 'Tissu élastique';

  @override
  String get t_sewing09 => '[Points invisibles]';

  @override
  String get t_sewing09_01 => '\n1. Placez l\'envers du tissu vers le haut, puis pliez et faufilez le tissu.';

  @override
  String get t_sewing09_02 => '(a) 5 mm (3/16 de pouce environ) \n(b) Points faufilage\n(c) Envers du tissu';

  @override
  String get t_sewing09_03 => '\n2. Fixez le pied pour point invisible \"R\" et abaissez le pied-de-biche. Positionnez le tissu de façon à ce que le bord plié touche le guide du pied-de-biche.';

  @override
  String get t_sewing09_04 => '(a) Guide\n(b) Pli';

  @override
  String get t_sewing09_05 => '\n3. Cousez le tissu en veillant à ce que le bord plié reste en contact avec le pied-de-biche.';

  @override
  String get t_sewing09_06 => '(a) Positionnement de l\'aiguille';

  @override
  String get t_sewing09_07 => '\n4. Retirez les points faufilage et retournez le tissu.';

  @override
  String get t_sewing09_08 => '(a) Envers du tissu\n(b) Endroit du tissu';

  @override
  String get t_sewing10 => '[Appliqué]';

  @override
  String get t_sewing10_01 => '\n1. Utilisez un spray adhésif temporaire, de la colle pour tissu ou un point faufilage pour fixer l\'appliqué sur le tissu. Le tissu ne bougera donc plus pendant la couture.';

  @override
  String get t_sewing10_02 => '(a) Appliqué\n(b) Colle pour tissu';

  @override
  String get t_sewing10_03 => '\n2. Fixez le pied-de-biche \"J\". Vérifiez que l\'aiguille s\'enfonce légèrement à l\'extérieur du bord de l\'appliqué et commencez ensuite à coudre.';

  @override
  String get t_sewing10_04 => '(a) Pièce d\'appliqué\n(b) Point de chute de l\'aiguille';

  @override
  String get t_sewing10_06 => '*Couture de courbes marquées\n';

  @override
  String get t_sewing10_07 => 'Arrêtez la machine avec l\'aiguille dans le tissu à l\'extérieur de l\'appliqué. Relevez le pied-de-biche et tournez progressivement le tissu tout en cousant, afin d\'obtenir une bonne finition au niveau de la couture.';

  @override
  String get t_sewing11 => '[Plis]';

  @override
  String get t_sewing11_01 => '\n1. Marquez les plis sur l\'envers du tissu.';

  @override
  String get t_sewing11_01_02 => '(a) Envers';

  @override
  String get t_sewing11_02 => '\n2. Retournez le tissu et ne repassez que les zones pliées.';

  @override
  String get t_sewing11_02_02 => '(a) Surface';

  @override
  String get t_sewing11_03 => '\n3.Fixez le pied-de-biche \"I\".\nCousez un point droit le long du pli.';

  @override
  String get t_sewing11_04_02 => '(a) Largeur du pli\n(b) Envers\n(c) Surface';

  @override
  String get t_sewing11_05 => '4. Repassez les plis dans le même sens.';

  @override
  String get t_sewing12 => '[Création de fronces]';

  @override
  String get t_sewing12_00 => '\nA utiliser sur la taille des jupes, les manches de chemises, etc.';

  @override
  String get t_sewing12_01 => '\n1. Tirez le fil de la canette et le fil supérieur de 50 mm (1-15/16 pouce environ) .';

  @override
  String get t_sewing12_01_02 => '(a) Fil supérieur\n(b) Fil de la canette\n(c) Environ 50 mm (1-15/16 pouce) ';

  @override
  String get t_sewing12_02 => '\n2. Cousez deux rangées de points droits parallèles à la ligne de couture, puis coupez l\'excédent de fil en laissant 50 mm (1-15/16 pouce environ) .';

  @override
  String get t_sewing12_02_02 => '(a) Ligne de couture\n(b) 10 à 15 mm (3/8 à 9/16 de pouce environ) \n(c) Environ 50 mm (1-15/16 pouce) ';

  @override
  String get t_sewing12_03 => '3. Tirez sur les fils de canette pour obtenir la quantité nécessaire pour les fronces, puis nouez les fils.';

  @override
  String get t_sewing12_04 => '4. Repassez les fronces pour les aplanir.\n';

  @override
  String get t_sewing12_05 => '5. Cousez sur la ligne de couture et retirez le point faufilage.';

  @override
  String get t_sewing13 => '[Création de pinces]';

  @override
  String get t_sewing13_01 => '\n1. Cousez un point inverse au début de la pince, puis cousez en partant de l\'extrémité la plus large vers l\'autre sans tirer sur le tissu.';

  @override
  String get t_sewing13_01_02 => '(a) Point faufilage\n(b) Surface\n(c) Envers';

  @override
  String get t_sewing13_02 => '2. Coupez le fil à la fin de la couture en laissant 50 mm (1-15/16 pouce environ) , puis nouez les deux extrémités.\n\n* Ne cousez pas de point inverse à la fin de la couture.';

  @override
  String get t_sewing13_03 => '3. Insérez les extrémités du fil dans la pince à l\'aide d\'une aiguille pour couture à la main.';

  @override
  String get t_sewing13_04 => '4. Repassez la pince sur un côté pour l\'aplanir.';

  @override
  String get t_sewing14 => '[Couture rabattue]';

  @override
  String get t_sewing14_00 => '\nPermet le renforcement des coutures et une finition nette des bords.\n\n1. Cousez la ligne de finition, puis coupez la moitié du rabat du côté du rabattement de la couture.';

  @override
  String get t_sewing14_01_02 => '(a) Envers\n(b) Environ 12 mm (1/2 de pouce) ';

  @override
  String get t_sewing14_02 => '\n2. Rabattez le tissu le long de la ligne de finition.';

  @override
  String get t_sewing14_02_02 => '(a) Envers\n(b) Ligne de finition';

  @override
  String get t_sewing14_03 => '\n3. Posez les deux rabats du côté du rabat le plus court (coupé) et repassez-les.';

  @override
  String get t_sewing14_03_02 => '(a) Envers';

  @override
  String get t_sewing14_04 => '\n4. Pliez le rabat le plus long autour du plus court et cousez le bord du pli.';

  @override
  String get t_sewing14_04_01 => '(a) Envers';

  @override
  String get t_sewing15_00 => 'Sélection du type de couture';

  @override
  String get t_sewing15_00_01 => 'Q-01:Point d\'assemblage(Milieu)\nQ-02:Point d\'assemblage(Droite)\nQ-03:Point d\'assemblage(Gauche)';

  @override
  String get t_sewing15_00_01_s_piecingmiddle => 'Point d\'assemblage(Milieu)';

  @override
  String get t_sewing15_00_01_s_piecingright => 'Point d\'assemblage(Droite)';

  @override
  String get t_sewing15_00_01_s_piecingleft => 'Point d\'assemblage(Gauche)';

  @override
  String get t_sewing15 => '[Assemblage]';

  @override
  String get t_sewing15_01 => '(a) Rabat: 6,5mm(env.1/4 de pouce)\n       (lorsque Q-02 est sélectionné)\n(b) Aligner avec le côté droit du pied-de-biche.\n\n1. Fixez le pied-de-biche \"J\".\nCousez en prenant soin d\'aligner le bord du tissu avec le côté du pied-de-biche.';

  @override
  String get t_sewing15_012 => '(a) Rabat: 7mm\n       (lorsque Q-02 est sélectionné)\n(b) Aligner avec le côté droit du pied-de-biche.\n\n1. Fixez le pied-de-biche \"J\".\nCousez en prenant soin d\'aligner le bord du tissu avec le côté du pied-de-biche.';

  @override
  String get t_sewing15_01_02 => '(a) Rabat: 6,5mm(env.1/4 de pouce)\n       (lorsque Q-03 est sélectionné)\n(b) Aligner avec le côté gauche du pied-de-biche.\n\n1. Fixez le pied-de-biche \"J\".\nCousez en prenant soin d\'aligner le bord du tissu avec le côté du pied-de-biche.';

  @override
  String get t_sewing15_01_022 => '(a) Rabat: 7mm\n       (lorsque Q-03 est sélectionné)\n(b) Aligner avec le côté gauche du pied-de-biche.\n\n1. Fixez le pied-de-biche \"J\".\nCousez en prenant soin d\'aligner le bord du tissu avec le côté du pied-de-biche.';

  @override
  String get t_sewing15_02 => '(a) Guide\n\nCe pied quilting 1/4\" avec guide peut coudre un rabat précis de 1/4 ou 1/8 de pouce.Il peut être utilisé pour assembler des tissus pour le point piqué ou la surpiqûre.\n\n1. Sélectionnez Q-01, puis fixez le pied quilting 1/4\" avec guide.';

  @override
  String get t_sewing15_03 => '(a) Guide\n(b) 6,4mm (1/4 de pouce)\n\n2. Utilisez le guide du pied-de-biche et les répères pour coudre les rabats avec précision.\n\n\"Assemblage d\'un rabat de 6,4mm (1/4 de pouce)\"\nCousez en maintenant chaque bord du tissu contre le guide.';

  @override
  String get t_sewing15_04 => '(a) Aligner ce repère avec le bord du tissu pour commencer\n(b) Début de la couture\n(c) Fin de la couture\n(d) Bord opposé du tissu pour terminer ou faire pivoter\n(e) 6,4mm (1/4 de pouce)\n\n\"Création d\'un rabat précis\"\nUtilisez le repère sur le pied pour commencer, finir ou pivoter de 1/4 de pouce du bord du tissu.';

  @override
  String get t_sewing15_05 => '(a) Surface du tissu\n(b) Couture\n(c) 3,2mm (1/8 de pouce)\n\n\"Quilting de surpiqûres, 3,2mm (1/8 de pouce)\"\nCousez en prenant soin d\'aligner le bord du tissu avec le côté gauche de l\'extrémité du pied-de-biche.';

  @override
  String get t_sewing16 => '[Quilting]';

  @override
  String get t_sewing16_01 => '1. Retirez le pied-de-biche, desserrez la vis du support de pied-de-biche, puis retirez le support de pied-de-biche.\n\nPlacez l’adaptateur sur la barre du pied-debiche en alignant le côté plat de l’ouverture de l’adaptateur sur le côté plat de la barre du pied-de-biche. Relevez-le au maximum, puis serrez fermement la vis à l’aide du tournevis.';

  @override
  String get t_sewing16_02 => '(a) Levier de fonctionnement\n(b) Vis de pince-aiguille\n(c) Fourche\n(d) Barre de pied-de-biche\n\n2. Réglez le levier de fonctionnement du pied de sorte que la vis de pince-aiguille soit entre la fourche et positionnez le pied d\'entraînement sur la barre du pied-de-biche.';

  @override
  String get t_sewing16_03 => '3. Abaissez le levier de pied-de-biche. Serrez fermement la vis avec le tournevis.';

  @override
  String get t_sewing16_04 => '4. Cousez à l\'aide des deux mains pour guider fermement le tissu des deux côtés du pied-de-biche.';

  @override
  String get t_sewing16_05 => '* Si le \"Système de détection automatique de tissu\" est sur \"ON\" sur l\'écran de réglages de la machine, le tissu avancera régulièrement et la couture sera facilitée.';

  @override
  String get t_sewing17 => '[Quilting en mouvement libre]';

  @override
  String get t_sewing17_00 => '(a) Pied quilting en mouvement libre \"C\"\n(b) Pied quilting à pointe ouverte \"O\" en mouvement libre\n\nPour le quilting en mouvement libre, utilisez le pied quilting en mouvement libre \"C\" ainsi que le pied quilting à pointe ouverte \"O\" en mouvement libre selon le motif choisi.';

  @override
  String get t_sewing17_01 => '1. Appuyez sur la touche d\'entraînement par griffe pour régler la machine sur le mode couture en mouvement libre.';

  @override
  String get t_sewing17_02_01 => '(a) Vis de support de pied-de-biche\n(b) Encoche\n\n2. Fixez le pied quilting en mouvement libre \"C\" sur le devant avec la vis du support du pied-de-biche alignée sur l\'encoche du pied-de-biche.\nPuis serrez la vis du dupport du pied-de-biche.';

  @override
  String get t_sewing17_02_02 => '(a) Epingle\n(b) Vis de pince-aiguille\n(c) Barre de pied-de-biche\n\nFixez le pied quilting à pointe ouverte \"O\" en mouvement libre en plaçant l\'épingle du pied-de-biche au-dessus de la vis du support d\'aiguille et en alignant la partie inférieure gauche du pied-de-biche et de la barre du pied-de-biche.\nPuis serrez la vis du dupport du pied-de-biche.';

  @override
  String get t_sewing17_03 => '(a) Point\n\n3. Utilisez les deux mains pour tendre le tissu, cousez en déplaçant le tissu à une vitesse égale pour coudre des points de 2 à 2,5 mm (approx, 1/16 - 3/32 pouces) de longueur.\n\n* Il est conseillé de fixer le contrôleur de pied et de coudre à vitesse constante.';

  @override
  String get t_sewing18 => '[Quilting en écho]';

  @override
  String get t_sewing18_00 => '(a) 6,4mm(env.1/4 de pouce)\n(b) 9,5mm(env.3/8 de pouce)\n\nPied en écho \"E\" à mouvement libre.';

  @override
  String get t_sewing18_01 => '2. Retirez le pied-de-biche, desserrez la vis du support de pied-de-biche, puis retirez la vis et le support de pied-de-biche.\n\nPlacez l’adaptateur sur la barre du pied-debiche en alignant le côté plat de l’ouverture de l’adaptateur sur le côté plat de la barre du pied-de-biche. Relevez-le au maximum, puis serrez fermement la vis à l’aide du tournevis.';

  @override
  String get t_sewing18_02 => '3. Positionnez le pied en écho \"E\" à mouvement libre sur le côté gauche de la barre du pied-de-biche en alignant les orifices du pied en écho et de la barre du ppied-de-biche.\n\nTournez l’une des petites vis fournies de 2 ou 3 tours à la main.';

  @override
  String get t_sewing18_03 => '4. Serrez la vis.';

  @override
  String get t_sewing18_04 => '(a) 6,4mm(env.1/4 de pouce)\n\n5. Utilisez la réglette sur le pied en écho comme guide et cousez autour du motif.';

  @override
  String get t_sewing18_05 => 'Travail terminé';

  @override
  String get t_sewing19 => '[Appliqué]';

  @override
  String get t_sewing19_01 => '(a) Rabat : 3 à 5 mm\n\n1. Tracez le motif sur du tissu d\'appliqué, puis découpez-le.';

  @override
  String get t_sewing19_02 => '2. Coupez un morceau de papier épais ou de renfort de broderie de la taille du dessin de l\'appliqué, placez-le sur l\'envers du tissu, puis pliez le rabat à l\'aide d\'un fer à repasser.';

  @override
  String get t_sewing19_03 => '3. Faites tourner l\'appliqué et retirez le renfort ou le papier de broderie. Fixez sur le tissu à l\'aide d\'épingles de faufilage ou d\'un point de faufilage.';

  @override
  String get t_sewing19_04 => '(a) Point de chute de l\'aiguille\n\n4. Fixez le pied-de-biche \"J\".\nVérifiez le point de chute de l\'aiguille, puis cousez le long du bord de l\'appliqué tout en veillant à ce que l\'aiguille dépasse légèrement à l\'extérieur du tissu.';

  @override
  String get t_explain_use => '[Utilisation]';

  @override
  String get t_explain01_01 => 'Couture générale, fronces, plis, etc. Lorsque vous appuyez sur la touche \"Point inverse/renfort\", la machine effectue des points inverses.';

  @override
  String get t_explain01_01_01 => '\n * Modification de la position de l\'aiguille \n Lorsque vous sélectionnez des motifs de points avec aiguille à gauche ou au milieu, vous pouvez utiliser les touches « + » et « - » dans l\'écran « L/R Shift » pour modifier la position de l\'aiguille. Réglez la distance entre le bord droit du pied-de-biche et l\'aiguille en fonction du rabat et, pendant la couture, alignez le bord du pied-de-biche avec le bord du tissu pour obtenir une finition parfaite.';

  @override
  String get t_explain01_02 => 'Couture générale, fronces, plis, etc. Lorsque vous appuyez sur la touche \"Point inverse/renfort\", la machine effectue des points de renfort.';

  @override
  String get t_explain01_03 => 'Couture générale, fronces, plis, etc. Lorsque vous appuyez sur la touche \"Point inverse/renfort\", la machine effectue des points inverses.';

  @override
  String get t_explain01_04 => 'Couture générale, fronces, plis, etc. Lorsque vous appuyez sur la touche \"Point inverse/renfort\", la machine effectue des points de renfort.';

  @override
  String get t_explain01_05 => 'Couture générale pour les coutures de renfort et décoratives.';

  @override
  String get t_explain01_06 => 'Points de renfort, couture et applications décoratives.';

  @override
  String get t_explain01_07 => 'Points décoratifs, couture de renfort.';

  @override
  String get t_explain01_08 => 'Point faufilage.';

  @override
  String get t_explain01_09 => 'Pour le surfilage et le raccommodage. Lorsque vous appuyez sur la touche \"Point inverse/renfort\", la machine effectue des points inverses.';

  @override
  String get t_explain01_10 => 'Pour le surfilage et le raccommodage. Lorsque vous appuyez sur la touche \"Point inverse/de renfort\", la machine effectue des points de renfort.';

  @override
  String get t_explain01_11 => 'Commencez à partir de la position droite de l\'aiguille, cousez en zigzag sur la gauche.';

  @override
  String get t_explain01_12 => 'Commencez à partir de la position gauche de l\'aiguille, cousez en zigzag sur la droite.';

  @override
  String get t_explain01_13 => 'Surfilage (tissus moyens et élastiques) , ruban et élastique.';

  @override
  String get t_explain01_14 => 'Surfilage (tissus moyens et élastiques) , ruban et élastique.';

  @override
  String get t_explain01_14a => 'Surfilage (tissus moyens et élastiques) , ruban et élastique.';

  @override
  String get t_explain01_15 => 'Renfort de tissus légers et moyens.';

  @override
  String get t_explain01_16 => 'Renfort de tissus lourds.';

  @override
  String get t_explain01_17 => 'Renfort de tissus moyens, lourds ou facilement effilochables ou de points décoratifs.';

  @override
  String get t_explain01_18 => 'Couture de renfort de tissus élastiques.';

  @override
  String get t_explain01_19 => 'Renfort de tissus élastiques moyens et de tissus épais, points décoratifs.';

  @override
  String get t_explain01_20 => 'Renfort de tissus élastiques ou de points décoratifs.';

  @override
  String get t_explain01_21 => 'Couture de tricot élastique.';

  @override
  String get t_explain01_22 => 'Renfort et couture de tissus élastiques.';

  @override
  String get t_explain01_23 => 'Renfort de tissus élastiques.';

  @override
  String get t_explain01_24 => 'Point droit tout en coupant des tissus.';

  @override
  String get t_explain01_25 => 'Point zigzag tout en coupant des tissus.';

  @override
  String get t_explain01_26 => 'Surfilage tout en coupant des tissus.';

  @override
  String get t_explain01_27 => 'Surfilage tout en coupant des tissus.';

  @override
  String get t_explain01_28 => 'Surfilage tout en coupant des tissus.';

  @override
  String get t_explain01_29 => 'Assemblage/patchwork  Rabat droit de 6,5 mm (1/4 de pouce) .';

  @override
  String get t_explain01_292 => 'Assemblage/patchwork  Rabat droit de 7 mm .';

  @override
  String get t_explain01_29a => 'Assemblage/patchwork';

  @override
  String get t_explain01_30 => 'Assemblage/patchwork  Rabat gauche de 6,5 mm (1/4 de pouce) .';

  @override
  String get t_explain01_302 => 'Assemblage/patchwork  Rabat gauche de 7 mm .';

  @override
  String get t_explain01_31 => 'Point quilting style \"point à la main\".';

  @override
  String get t_explain01_32 => 'Point zigzag pour le quilting et la couture des morceaux de tissus piqués.';

  @override
  String get t_explain01_33 => 'Point quilting pour les appliqués invisibles ou la fixation de bordures.';

  @override
  String get t_explain01_34 => 'Quilting arrière-plan.';

  @override
  String get t_explain02_01 => 'Point d\'ourlet pour les tissus à fils entrelacés.';

  @override
  String get t_explain02_02 => 'Point d\'ourlet pour les tissus élastiques.';

  @override
  String get t_explain02_03 => 'Appliqués, point de languette décoratif.';

  @override
  String get t_explain02_04 => 'Finition de point bordure replié sur les tissus. Augmentez la tension du fil supérieur pour une finition parfaite en feston aux points bordure replié.';

  @override
  String get t_explain02_05 => 'Décorer le col d\'un chemisier ou le bord d\'un mouchoir.';

  @override
  String get t_explain02_06 => 'Décorer le col d\'un chemisier ou le bord d\'un mouchoir.';

  @override
  String get t_explain02_07 => 'Points patchwork, points décoratifs.';

  @override
  String get t_explain02_08 => 'Points patchwork, points décoratifs.';

  @override
  String get t_explain02_09 => 'Points décoratifs, fixation de ganses et broderie sur fils couchés.';

  @override
  String get t_explain02_10 => 'Points smock, points décoratifs.';

  @override
  String get t_explain02_11 => 'Points fagot, points décoratifs.';

  @override
  String get t_explain02_12 => 'Points fagot, d\'assemblage et décoratifs.';

  @override
  String get t_explain02_13 => 'Fixation du ruban à la couture du tissu élastique.';

  @override
  String get t_explain02_14 => 'Points décoratifs.';

  @override
  String get t_explain02_15 => 'Couture de renfort décorative.';

  @override
  String get t_explain02_15a => 'Points décoratifs.';

  @override
  String get t_explain02_16 => 'Points décoratifs.';

  @override
  String get t_explain02_17 => 'Points décoratifs et fixation de l\'élastique.';

  @override
  String get t_explain02_18 => 'Points décoratifs et appliqué.';

  @override
  String get t_explain02_19 => 'Points décoratifs.';

  @override
  String get t_explain03_01 => 'Ourlets décoratifs, point droit triple à gauche.';

  @override
  String get t_explain03_02 => 'Ourlets décoratifs, point droit triple au centre.';

  @override
  String get t_explain03_03 => 'Ourlets décoratifs, couture de renfort.';

  @override
  String get t_explain03_04 => 'Ourlets décoratifs, point turc pour fixation de la dentelle.';

  @override
  String get t_explain03_05 => 'Ourlets décoratifs.';

  @override
  String get t_explain03_06 => 'Point marguerite pour ourlets décoratifs.';

  @override
  String get t_explain03_07 => 'Ourlets décoratifs à l\'ancienne.';

  @override
  String get t_explain03_08 => 'Ourlets décoratifs à l\'ancienne.';

  @override
  String get t_explain03_09 => 'Ourlets décoratifs à l\'ancienne.';

  @override
  String get t_explain03_10 => 'Ourlets décoratifs à l\'ancienne.';

  @override
  String get t_explain03_11 => 'Ourlets décoratifs à l\'ancienne.';

  @override
  String get t_explain03_12 => 'Ourlets décoratifs à l\'ancienne.';

  @override
  String get t_explain03_13 => 'Ourlets décoratifs à l\'ancienne.';

  @override
  String get t_explain03_14 => 'Ourlets décoratifs à l\'ancienne.';

  @override
  String get t_explain03_15 => 'Ourlets décoratifs à l\'ancienne.';

  @override
  String get t_explain03_16 => 'Ourlets décoratifs à l\'ancienne.';

  @override
  String get t_explain03_17 => 'Ourlets décoratifs à l\'ancienne.';

  @override
  String get t_explain03_18 => 'Ourlets décoratifs à l\'ancienne.';

  @override
  String get t_explain03_19 => 'Ourlets décoratifs et point d\'assemblage.';

  @override
  String get t_explain03_20 => 'Ourlets décoratifs. Points fagot, fixation d\'un ruban.';

  @override
  String get t_explain03_21 => 'Ourlets décoratifs, points smock.';

  @override
  String get t_explain03_22 => 'Ourlets décoratifs, points smock.';

  @override
  String get t_explain03_23 => 'Ourlets décoratifs, points smock.';

  @override
  String get t_explain03_24 => 'Ourlets décoratifs.';

  @override
  String get t_explain03_25 => 'Points décoratifs.';

  @override
  String get t_explain04_01 => 'Boutonnière sur tissus légers ou moyens.';

  @override
  String get t_explain04_02 => 'Boutonnière plus large pour les gros boutons.';

  @override
  String get t_explain04_03 => 'Boutonnières en pointe à taille renforcée.';

  @override
  String get t_explain04_04 => 'Boutonnières avec points d\'arrêt verticaux sur tissus lourds.';

  @override
  String get t_explain04_05 => 'Boutonnières avec points d\'arrêt.';

  @override
  String get t_explain04_06 => 'Boutonnière pour tissus fins, moyens et lourds.';

  @override
  String get t_explain04_07 => 'Boutonnières pour tissus légers et moyens.';

  @override
  String get t_explain04_08 => 'Boutonnières plus larges pour des boutons décoratifs plus gros.';

  @override
  String get t_explain04_09 => 'Boutonnières renforcées avec points d\'arrêt verticaux.';

  @override
  String get t_explain04_10 => 'Boutonnières pour tissus élastiques ou à fils entrelacés.';

  @override
  String get t_explain04_11 => 'Boutonnières pour tissus à l\'ancienne et élastiques.';

  @override
  String get t_explain04_12 => 'La première étape lors de l\'exécution de boutonnières cousues.';

  @override
  String get t_explain04_13 => 'Boutonnières dans des tissus lourds ou épais pour des boutons plats et larges.';

  @override
  String get t_explain04_14 => 'Boutonnières dans des tissus moyens ou lourds pour des boutons plats et larges.';

  @override
  String get t_explain04_15 => 'Boutonnières avec points d\'arrêt verticaux en guise de renfort dans les tissus lourds ou épais.';

  @override
  String get t_explain04_15a => 'Côté gauche de la boutonnière 4 étapes.';

  @override
  String get t_explain04_15b => 'Points d\'arrêt de la boutonnière 4 étapes.';

  @override
  String get t_explain04_15c => 'Côté droit de la boutonnière 4 étapes.';

  @override
  String get t_explain04_15d => 'Points d\'arrêt de la boutonnière 4 étapes.';

  @override
  String get t_explain04_16 => 'Reprisage de tissus moyens.';

  @override
  String get t_explain04_17 => 'Reprisage de tissus lourds.';

  @override
  String get t_explain04_18 => 'Renfort de l\'ouverture d\'une poche, etc.';

  @override
  String get t_explain04_19 => 'Fixation de boutons.';

  @override
  String get t_explain04_20 => 'Pour l\'exécution d\'œillets, de trous de ceinture, etc. Si votre couture n\'est pas satisfaisante, réglez le motif de point.';

  @override
  String get t_explain04_21 => 'Pour la création d\'œillets ou de trous en forme d\'étoile. Si votre couture n\'est pas satisfaisante, réglez le motif de point.';

  @override
  String get t_explain05_01 => 'Pour la fixation d\'un appliqué sur les parties tubulaires d\'un tissu et biseauter des coins.';

  @override
  String get t_explain05_02 => 'Pour la fixation d\'un appliqué sur les parties tubulaires d\'un tissu et biseauter des coins.';

  @override
  String get t_explain05_03 => 'Pour la fixation d\'un appliqué sur les parties tubulaires d\'un tissu et biseauter des coins.';

  @override
  String get t_explain05_04 => 'Pour la fixation d\'un appliqué sur les parties tubulaires d\'un tissu.';

  @override
  String get t_explain05_05 => 'Pour la fixation d\'un appliqué sur les parties tubulaires d\'un tissu.';

  @override
  String get t_explain05_06 => 'Pour la fixation d\'un appliqué sur les parties tubulaires d\'un tissu et biseauter des coins.';

  @override
  String get t_explain05_07 => 'Pour la fixation d\'un appliqué sur les parties tubulaires d\'un tissu et biseauter des coins.';

  @override
  String get t_explain05_08 => 'Pour la fixation d\'un appliqué sur les parties tubulaires d\'un tissu et biseauter des coins.';

  @override
  String get t_explain05_09 => 'Pour la fixation d\'un appliqué sur les parties tubulaires d\'un tissu.';

  @override
  String get t_explain05_10 => 'Pour la fixation d\'un appliqué sur les parties tubulaires d\'un tissu.';

  @override
  String get t_explain05_11 => 'Pour la fixation d\'un appliqué sur les parties tubulaires d\'un tissu et biseauter des coins.';

  @override
  String get t_explain05_12 => 'Pour la fixation d\'un appliqué sur les parties tubulaires d\'un tissu et biseauter des coins.';

  @override
  String get t_explain06_01 => 'Pour la couture d\'appliqués avec des fils, ou autre, et pour créer des ornements de fils décoratifs (couture en mouvement libre).';

  @override
  String get t_explain06_02 => 'Faufilage en mouvement libre\nLorsque les griffes d’entraînement sont baissées, il est possible de coudre le faufilage en déplaçant librement le tissu.';

  @override
  String get t_explain06_03a => 'Ce motif de points est constitué de plusieurs points courts.\nCousez ce motif de points à l\'aide d\'un fil de nylon transparent ou d\'un fil léger d\'une couleur correspondant au tissu pour le fil supérieur pour donner à votre projet l\'aspect d\'une couture à la main. Si le fil de la canette est d\'une couleur différente de celle du tissu, le motif de points ressortira.';

  @override
  String get t_explain07_01 => 'Appliqués, point décoratif.';

  @override
  String get t_explain07_02 => 'Le motif de points peut être brodé en biseau au début ou à la fin de la couture.';

  @override
  String get t_explaindeco00_01 => 'Couture de finitions parfaites';

  @override
  String get t_explaindeco00_02 => 'Réglages';

  @override
  String get t_explaindeco01_00 => '[Couture de finitions parfaites]';

  @override
  String get t_explaindeco01_01 => 'Pour coudre des finitions parfaites de vos points de caractères/décoratifs, vérifiez les combinaisons tissu/aiguille/fil. D\'autres facteurs (épaisseur du tissu, renfort de broderie, etc.) influent également sur le point. Nous vous conseillons donc de toujours coudre quelques points d\'essai avant d\'entamer votre travail de couture.';

  @override
  String get t_explaindeco01_02 => '(a) Tissu\n(b) Renfort de broderie\n(c) Papier fin\n\n*Tissu Lorsque vous cousez sur des tissus élastiques, légers ou à grosse trame, vous pouvez fixer un renfort de broderie. Si vous ne souhaitez pas utiliser cette solution, placez le tissu sur du papier fin, comme du papier-calque.\n\n*Fil\nn° 50 - n° 60\n\n*Aiguille\nAvec les tissus légers, normaux ou élastiques : Aiguille à pointe boule (dorée) \nAvec les tissus lourds : Aiguille de machine à coudre à usage domestique 90/14\n\n*Pied-de biche\nPied pour monogrammes \"N+\". Si vous utilisez un autre pied-de-biche, vous risquez d\'obtenir des résultats moins satisfaisants.';

  @override
  String get t_explaindeco02_00 => '[Réglages]';

  @override
  String get t_explaindeco02_01 => 'Il se peut que votre motif de point ne soit pas réussi ; tout dépend du type ou de l\'épaisseur du tissu, du renfort de broderie utilisé, de la vitesse de couture, etc. Si votre couture n\'est pas satisfaisante, faites quelques points d\'essai dans les mêmes conditions qu\'une vraie couture et modifiez le motif de point comme indiqué ci-dessous. Si le motif n\'est pas satisfaisant même après les réglages selon le motif 6-120, effectuez des réglages pour chaque motif individuellement.';

  @override
  String get t_explaindeco02_02 => '1. Sélectionnez 6-120. Fixez le pied pour monogrammes \"N+\" et cousez le motif.';

  @override
  String get t_explaindeco02_03_00 => '\n2. Comparez le motif fini à l\'illustration du motif ci-dessous.';

  @override
  String get t_explaindeco02_04_00 => '[1. Si le motif se tasse]';

  @override
  String get t_explaindeco02_04_01 => 'Appuyez sur + dans AJUST.FIN.VERTI. Effectuez de nouveau votre motif. Si le motif de points n\'est toujours pas satisfaisant, effectuez de nouveaux réglages, jusqu\'à ce que le motif de point soit correct.';

  @override
  String get t_explaindeco02_05_00 => '[2. Si le motif présente des espaces]';

  @override
  String get t_explaindeco02_05_01 => 'Appuyez sur - dans AJUST.FIN.VERTI. Effectuez de nouveau votre motif. Si le motif de points n\'est toujours pas satisfaisant, effectuez de nouveaux réglages, jusqu\'à ce que le motif de point soit correct.';

  @override
  String get t_explaindeco02_06_00 => '[3. Si le motif biaise vers la gauche]';

  @override
  String get t_explaindeco02_06_01 => 'Appuyez sur + dans AJUST.FIN.HORIZ. Si le motif de points n\'est toujours pas satisfaisant, effectuez de nouveaux réglages, jusqu\'à ce que le motif de point soit correct.';

  @override
  String get t_explaindeco02_07_00 => '[4. Si le modèle biaise vers la droite]';

  @override
  String get t_explaindeco02_07_01 => 'Appuyez sur - dans AJUST.FIN.HORIZ. Si le motif de points n\'est toujours pas satisfaisant, effectuez de nouveaux réglages, jusqu\'à ce que le motif de point soit correct.';

  @override
  String get t_terms_read => 'Veuillez lire attentivement les conditions suivantes.';

  @override
  String get t_terms_cert_read => 'Veuillez lire attentivement les conditions suivantes.';

  @override
  String get t_terms_cert_01_00 => 'Certification du KIT de mise à niveau';

  @override
  String get t_terms_cert_01_01 => 'Conditions sur la certification du KIT de mise à niveau\n';

  @override
  String get t_terms_cert_01_02 => '  Lorsque vous activez toute fonction facultative du présent logiciel (le « Logiciel »), y compris, sans s\'y limiter, des licences payantes, des manuels, des documents et tout autre matériel, ainsi que leurs mises à jour (collectivement les « Outils »), il se peut que vous soyez amené, directement ou indirectement, à fournir certains codes de licence, numéros de produit, numéros de série, et autres informations associées (les « Données utilisateur ») afin d\'utiliser les Outils.\n';

  @override
  String get t_terms_cert_01_03 => '  Certaines des informations contenues dans les Données utilisateur peuvent être associées aux données que vous pouvez enregistrer sur le site Web d\'enregistrement de produit de Brother Industries, Ltd. (la « Société ») ou de ses filiales.  Toutefois, la Société n\'utilisera pas les Données utilisateur pour vous identifier ou à tout autre fin que d\'activer les Outils. Les Données utilisateur peuvent être transmises au serveur administratif de la Société ou à des serveurs de services cloud comme Microsoft et Amazon, qui peuvent se situer dans des pays n\'offrant pas un niveau de protection des données personnelles aussi adéquat que celui de votre pays. Toutefois, la Société protègera vos Données utilisateur conformément à la loi en vigueur au moyen de mesures de sécurité appropriées afin d\'empêcher une utilisation ou une divulgation non autorisée.';

  @override
  String get t_terms_nettool_read => 'Veuillez lire attentivement les conditions suivantes.';

  @override
  String get t_terms_nettool_01_00 => 'Outil de diagnostic réseau';

  @override
  String get t_terms_nettool_01_01 => 'Conditions de l\'Outil de diagnostic réseau\n';

  @override
  String get t_terms_nettool_01_02 => '  En cas de problème de connexion réseau, vous pouvez être amené à exécuter l\'outil de diagnostic réseau du présent logiciel (le « Logiciel »).  Lorsque vous effectuez un diagnostic réseau, des informations provenant de produits de couture ou manuels (le « Produit de la société ») et d\'appareils connectés au Produit de la société, y compris, sans s\'y limiter, l\'adresse IP (Internet Protocol) ou MAC (Media Access Control), les informations de connexion proxy, le masque de sous-réseau, la passerelle, les serveurs DNS, et toute autre information associée (les « Informations réseau ») s\'afficheront à l\'écran.  ';

  @override
  String get t_terms_nettool_01_03 => '  Si vous avez des problèmes avec la connexion Internet du Produit de la société et souhaitez obtenir de l\'aide de la part du support technique, vous serez peut-être invité à communiquer vos Informations réseau à vos fournisseurs ou revendeurs locaux, ou à Brother Industries, Ltd. (la « Société ») et/ou ses filiales en personne, par téléphone, par e-mail, par fax ou via Internet.  Si vous décidez de fournir vos Informations réseau, vous acceptez et reconnaissez que ces dernières peuvent être transférées à la Société ou ses filiales exclusivement à des fins d\'analyse ou de résolution de votre problème réseau, et que vos informations seront protégées conformément à la loi en vigueur.\n  À l\'exception de celles indiquées dans le présent document, vos Informations réseau ne doivent être ni collectées ni stockées par la Société ou ses filiales, à moins que la Société et/ou ses filiales obtiennent votre approbation préalable séparément.';

  @override
  String get t_terms_cert_read_t => 'Veuillez lire attentivement les conditions suivantes.';

  @override
  String get t_terms_cert_01_01_t => 'Conditions sur la certification du KIT de mise à niveau\n';

  @override
  String get t_terms_cert_01_02_t => '  Lorsque vous activez toute fonction facultative du présent logiciel (le « Logiciel »), y compris, sans s\'y limiter, des licences payantes, des manuels, des documents et tout autre matériel, ainsi que leurs mises à jour (collectivement les « Outils »), il se peut que vous soyez amené, directement ou indirectement, à fournir certains codes de licence, numéros de produit et numéros de série (les « Données utilisateur ») afin d\'utiliser les Outils.\n';

  @override
  String get t_terms_cert_01_03_t => '  Certaines des informations contenues dans les Données utilisateur peuvent être associées aux données que vous pouvez enregistrer sur le site Web d\'enregistrement de produit de Tacony Corporation d/b/a Baby Lock (la « Société »).  Toutefois, La Société n\'utilisera pas les Données utilisateur pour vous identifier ou à toute autre fin que d\'activer les Outils. Les Données utilisateur peuvent être transmises au serveur administratif de la Société ou à des serveurs de services cloud comme Microsoft et AWS, qui peuvent se situer dans des pays ne bénéficiant pas d\'un niveau adéquat de protection des données personnelles comparé à celui de votre pays.  Toutefois, la Société protègera vos Données utilisateur conformément à la loi en vigueur au moyen de mesures de sécurité appropriées afin d\'empêcher une utilisation ou une divulgation non autorisée.';

  @override
  String get t_terms_nettool_read_t => 'Veuillez lire attentivement les conditions suivantes.';

  @override
  String get t_terms_nettool_01_01_t => 'Conditions relatives à l\'Outil de diagnostic réseau\n';

  @override
  String get t_terms_nettool_01_02_t => '  En cas de problème de connexion réseau, vous pouvez être amené à exécuter l\'Outil de diagnostic réseau du présent logiciel (le « Logiciel »).  Lorsque vous effectuez un diagnostic réseau, des informations provenant de produits de couture (le « Produit de la société ») et d\'appareils connectés au Produit de la société, y compris, sans s\'y limiter, l\'adresse IP (Internet Protocol) ou MAC (Media Access Control), les informations de connexion proxy, le masque de sous-réseau, la passerelle, le serveur DNS, et toute autre information associée (les « Informations réseau ») s\'afficheront à l\'écran.  ';

  @override
  String get t_terms_nettool_01_03_t => '  Si vous avez des problèmes avec la connexion Internet du Produit de la société et souhaitez obtenir de l\'aide de la part du support technique, vous serez peut-être invité à communiquer vos Informations réseau à votre revendeur local ou à Tacony Corporation d/b/a Baby Lock (la « Société ») en personne, par téléphone, par e-mail, par fax ou via Internet.  Si vous décidez de fournir vos Informations réseau, vous acceptez et reconnaissez que ces dernières peuvent être transférées à la Société exclusivement à des fins d\'analyse ou de résolution de votre problème réseau, et que vos informations seront protégées conformément à la loi en vigueur.\n  À l\'exception de celles indiquées dans le présent document, vos Informations réseau ne doivent être ni collectées ni stockées par la Société à moins que la Société obtienne votre approbation préalable séparément.';

  @override
  String get t_terms_mnmpinmac_01_b => 'En cliquant sur « OK », votre code PIN, adresse MAC et numéro de machine seront envoyés au serveur de Brother pour associer votre machine à coudre à votre ScanNCut et à vos autres machines à coudre.\nLes informations fournies ne seront pas utilisées dans un objectif autre que ceux décrits ci-dessus.';

  @override
  String get t_terms_snj_pair_01 => 'Lorsque vous cliquez sur « OK », votre code PIN, votre adresse MAC, le nom de la machine et le numéro de la machine sont envoyés au serveur de Brother afin de coupler votre machine à coudre avec vos équipements/services Brother.\nLes informations fournies ne seront pas utilisées dans un objectif autre que ceux décrits ci-dessus. ';

  @override
  String get upg_01 => 'Connectez le support USB.';

  @override
  String get upg_02 => 'Échec de lecture du fichier.';

  @override
  String get upg_03 => 'Échec de localisation du bon fichier.';

  @override
  String get upg_04 => 'Vérifiez l\'absence d\'erreur de calcul';

  @override
  String get upg_05 => 'Échec de sauvegarde du fichier.';

  @override
  String get upg_06 => 'Adresse du fichier incorrecte.';

  @override
  String get upg_07 => 'Connecté à un PC. Ne déconnectez pas le câble USB.';

  @override
  String get upg_08 => 'Enregistrement du fichier de mise à jour. \nNe mettez pas hors tension.';

  @override
  String get update_08 => 'Enregistrement du fichier de mise à jour. \nNe mettez pas hors tension.';

  @override
  String get upg_09 => 'Mise à jour terminée.';

  @override
  String get update_09 => 'Mise à jour terminée.';

  @override
  String get upg_10 => 'Appuyez sur CHARGER après avoir connecté le support USB contenant le fichier de mise à jour.';

  @override
  String get update_10 => 'Appuyez sur CHARGER après avoir connecté le support USB contenant le fichier de mise à jour.';

  @override
  String get upg_12 => 'Appuyez sur CHARGER pour installer le fichier de mise à jour.';

  @override
  String get update_13 => 'Impossible de mettre à jour à la nouvelle version directement à partir de la version actuelle.';

  @override
  String get update_14 => 'Mettez le logiciel à jour à la version ci-dessous en éteignant la machine, puis mettez à jour avec le support USB contenant le fichier de mise à jour.';

  @override
  String get update_15 => 'Éteignez la machine, puis mettez à jour avec le support USB contenant le fichier de mise à jour.';

  @override
  String get icon_00037 => 'Retour';

  @override
  String get icon_00008_u => 'Fermer';

  @override
  String get icon_00009_u => 'Annuler';

  @override
  String get icon_00010_u => 'ＯＫ';

  @override
  String get icon_00050_u => 'Charger';

  @override
  String get upg_16 => 'Échec de la mise à jour. \nVeuillez réessayer d\'installer le programme de mise à jour. \n* Si le problème persiste, veuillez télécharger de nouveau et réinstaller le programme.';

  @override
  String get upg_17 => 'Échec de la mise à jour. \nVeuillez télécharger et installer de nouveau le programme de mise à jour.';

  @override
  String get upg_18 => 'ERR_UPEND';

  @override
  String get upg_19 => 'Veuillez redémarrer la machine.\nLe démarrage initial de la machine peut prendre un certain temps. L\'écran peut devenir temporairement noir.';

  @override
  String get upg_20 => 'N\'éteignez pas la machine même si l\'écran devient noir.';

  @override
  String get upg_21 => 'La détection de corruption du système de fichiers a échoué.\nVeuillez éteindre la machine et la rallumer.';

  @override
  String get upg_22 => 'Échec de la réparation des fichiers système corrompus.\nVeuillez éteindre la machine et la rallumer.';

  @override
  String get upg_23 => 'Échec de la mise à jour. \nAprès avoir démarré la machine normalement, éteignez-la, puis essayez à nouveau d\'installer le programme de mise à jour.';

  @override
  String get t_name_01_01 => 'Point droit (Gauche)';

  @override
  String get t_name_01_02 => 'Point droit (Gauche)';

  @override
  String get t_name_01_03 => 'Point droit (Milieu)';

  @override
  String get t_name_01_04 => 'Point droit (Milieu)';

  @override
  String get t_name_01_05 => 'Triple point élastique';

  @override
  String get t_name_01_06 => 'Point élastique';

  @override
  String get t_name_01_07 => 'Point décoratif';

  @override
  String get t_name_01_08 => 'Point faufilage';

  @override
  String get t_name_01_09 => 'Point zigzag';

  @override
  String get t_name_01_10 => 'Point zigzag';

  @override
  String get t_name_01_11 => 'Point zigzag (Droite)';

  @override
  String get t_name_01_12 => 'Point zigzag (Gauche)';

  @override
  String get t_name_01_13 => 'Zigzag élastique 2 étapes';

  @override
  String get t_name_01_14 => 'Zigzag élastique 2 étapes';

  @override
  String get t_name_01_14a => 'Zigzag élastique 3 étapes';

  @override
  String get t_name_01_15 => 'Point surfilage';

  @override
  String get t_name_01_16 => 'Point surfilage';

  @override
  String get t_name_01_17 => 'Point surfilage';

  @override
  String get t_name_01_18 => 'Point surfilage';

  @override
  String get t_name_01_19 => 'Point surfilage';

  @override
  String get t_name_01_20 => 'Point surfilage';

  @override
  String get t_name_01_21 => 'Point surfilage';

  @override
  String get t_name_01_22 => 'Faufilage diamant simple';

  @override
  String get t_name_01_23 => 'Faufilage diamant simple';

  @override
  String get t_name_01_24 => 'Avec couteau raseur';

  @override
  String get t_name_01_25 => 'Avec couteau raseur';

  @override
  String get t_name_01_26 => 'Avec couteau raseur';

  @override
  String get t_name_01_27 => 'Avec couteau raseur';

  @override
  String get t_name_01_28 => 'Avec couteau raseur';

  @override
  String get t_name_01_29 => 'Point d\'assemblage (Droite)';

  @override
  String get t_name_01_29a => 'Point d\'assemblage (Milieu)';

  @override
  String get t_name_01_30 => 'Point d\'assemblage (Gauche)';

  @override
  String get t_name_01_31 => 'Quilting style \"à la main\"';

  @override
  String get t_name_01_32 => 'Point zigzag quilting appliqué';

  @override
  String get t_name_01_33 => 'Point quilting appliqué';

  @override
  String get t_name_01_34 => 'Quilting pointillé';

  @override
  String get t_name_02_01 => 'Point invisible';

  @override
  String get t_name_02_02 => 'Point invisible élastique';

  @override
  String get t_name_02_03 => 'Point couverture';

  @override
  String get t_name_02_03a => 'Point couverture';

  @override
  String get t_name_02_04 => 'Point bordure replié';

  @override
  String get t_name_02_05 => 'Point feston satin';

  @override
  String get t_name_02_06 => 'Point feston';

  @override
  String get t_name_02_07 => 'Point patchwork';

  @override
  String get t_name_02_08 => 'Point patchwork double';

  @override
  String get t_name_02_09 => 'Point couché';

  @override
  String get t_name_02_10 => 'Point smock';

  @override
  String get t_name_02_11 => 'Point plume';

  @override
  String get t_name_02_12 => 'Point fagot croisé';

  @override
  String get t_name_02_13 => 'Fixation passepoil';

  @override
  String get t_name_02_14 => 'Point échelle';

  @override
  String get t_name_02_15 => 'Point Rick-Rack';

  @override
  String get t_name_02_15a => 'Point décoratif';

  @override
  String get t_name_02_16 => 'Point décoratif';

  @override
  String get t_name_02_17 => 'Point serpent';

  @override
  String get t_name_02_18 => 'Point décoratif';

  @override
  String get t_name_02_19 => 'Point décoratif pointillé';

  @override
  String get t_name_03_01 => 'Couture ourlet';

  @override
  String get t_name_03_02 => 'Couture ourlet';

  @override
  String get t_name_03_03 => 'Couture ourlet zigzag';

  @override
  String get t_name_03_04 => 'Couture ourlet';

  @override
  String get t_name_03_05 => 'Couture ourlet';

  @override
  String get t_name_03_06 => 'Couture ourlet';

  @override
  String get t_name_03_07 => 'Couture ourlet';

  @override
  String get t_name_03_08 => 'Couture ourlet';

  @override
  String get t_name_03_09 => 'Couture ourlet';

  @override
  String get t_name_03_10 => 'Couture ourlet';

  @override
  String get t_name_03_11 => 'Couture ourlet';

  @override
  String get t_name_03_12 => 'Point nid d\'abeilles';

  @override
  String get t_name_03_13 => 'Point nid d\'abeilles';

  @override
  String get t_name_03_14 => 'Couture ourlet';

  @override
  String get t_name_03_15 => 'Couture ourlet';

  @override
  String get t_name_03_16 => 'Couture ourlet';

  @override
  String get t_name_03_17 => 'Couture ourlet';

  @override
  String get t_name_03_18 => 'Couture ourlet';

  @override
  String get t_name_03_19 => 'Couture ourlet';

  @override
  String get t_name_03_20 => 'Couture ourlet';

  @override
  String get t_name_03_21 => 'Couture ourlet';

  @override
  String get t_name_03_22 => 'Couture ourlet';

  @override
  String get t_name_03_23 => 'Couture ourlet';

  @override
  String get t_name_03_24 => 'Couture ourlet';

  @override
  String get t_name_03_25 => 'Point échelle';

  @override
  String get t_name_04_01 => 'Boutonnière étroite ronde';

  @override
  String get t_name_04_02 => 'Boutonnière large à bout rond';

  @override
  String get t_name_04_03 => 'Boutonnière en pointe à bouts ronds';

  @override
  String get t_name_04_04 => 'Boutonnière à bout rond';

  @override
  String get t_name_04_05 => 'Boutonnière à bout rond';

  @override
  String get t_name_04_06 => 'Boutonnières à double bout rond';

  @override
  String get t_name_04_07 => 'Boutonnière rectangulaire étroite';

  @override
  String get t_name_04_08 => 'Boutonnière rectangulaire large';

  @override
  String get t_name_04_09 => 'Boutonnière rectangulaire';

  @override
  String get t_name_04_10 => 'Boutonnière élastique';

  @override
  String get t_name_04_11 => 'Boutonnière à l\'ancienne';

  @override
  String get t_name_04_12 => 'Boutonnière cousue';

  @override
  String get t_name_04_13 => 'Boutonnière Trou de serrure';

  @override
  String get t_name_04_14 => 'Boutonnière Trou de serrure en pointe';

  @override
  String get t_name_04_15 => 'Boutonnière Trou de serrure';

  @override
  String get t_name_04_15a => 'Boutonnière 4 étapes 1';

  @override
  String get t_name_04_15b => 'Boutonnière 4 étapes 2';

  @override
  String get t_name_04_15c => 'Boutonnière 4 étapes 3';

  @override
  String get t_name_04_15d => 'Boutonnière 4 étapes 4';

  @override
  String get t_name_04_16 => 'Reprisage';

  @override
  String get t_name_04_17 => 'Reprisage';

  @override
  String get t_name_04_18 => 'Points d\'arrêt';

  @override
  String get t_name_04_19 => 'Couture bouton';

  @override
  String get t_name_04_20 => 'Œillet';

  @override
  String get t_name_04_21 => 'Œillet en forme d\'étoile';

  @override
  String get t_name_05_01 => 'Diagonale gauche haut (Droit)';

  @override
  String get t_name_05_02 => 'En arrière (Droit)';

  @override
  String get t_name_05_03 => 'Diagonale droite haut (Droit)';

  @override
  String get t_name_05_04 => 'Latéral vers la gauche (Droit)';

  @override
  String get t_name_05_05 => 'Latéral vers la droite (Droit)';

  @override
  String get t_name_05_06 => 'Diagonale gauche bas (Droit)';

  @override
  String get t_name_05_07 => 'En avant (Droit)';

  @override
  String get t_name_05_08 => 'Diagonale droite bas (Droit)';

  @override
  String get t_name_05_09 => 'Latéral vers la gauche (Zigzag)';

  @override
  String get t_name_05_10 => 'Latéral vers la droite (Zigzag)';

  @override
  String get t_name_05_11 => 'En avant (Zigzag)';

  @override
  String get t_name_05_12 => 'En arrière (Zigzag)';

  @override
  String get t_name_06_01 => 'Point couché en mouvement libre';

  @override
  String get t_name_06_02 => 'Faufilage en mouvement libre';

  @override
  String get t_name_06_03 => 'Quilting style \"à la main\"';

  @override
  String get t_name_06_04 => 'Quilting style \"à la main\"';

  @override
  String get t_name_06_05 => 'Quilting style \"à la main\"';

  @override
  String get t_name_06_06 => 'Point de feutrage à aiguilles';

  @override
  String get t_name_07_01 => 'Point d\'appliqué';

  @override
  String get t_name_07_02 => 'Point en biseau';

  @override
  String get t_name_sr_01 => 'Point droit (Milieu)';

  @override
  String get t_name_sr_02 => 'Point zigzag';

  @override
  String get t_name_sr_03 => 'Faufilage en mouvement libre';

  @override
  String get tt_head_wifi => 'Réglages du réseau local LAN sans fil';

  @override
  String get tt_head_camera => 'Vue caméra';

  @override
  String get tt_head_setting => 'Réglages de la machine';

  @override
  String get tt_head_teaching => 'Aide de la machine';

  @override
  String get tt_head_osae => 'Remplacement du pied-de-biche/de l\'aiguille';

  @override
  String get tt_head_lock => 'Verrouillage de l\'écran';

  @override
  String get tt_head_home => 'Page d\'accueil';

  @override
  String get tt_foot_clock => 'Réglages de l\'heure/de la date';

  @override
  String get tt_tch_og_principal_parts1 => '[Levier du pied-de-biche]';

  @override
  String get tt_tch_og_principal_parts2 => '[Commande de vitesse de couture]';

  @override
  String get tt_tch_og_principal_parts3 => '[Volant]';

  @override
  String get tt_tch_og_principal_parts4 => '[Plateau et compartiment d\'accessoires]';

  @override
  String get tt_tch_og_mb_knee_lifter => '[Genouillère]';

  @override
  String get tt_tch_og_principal_parts6 => '[Pédale rhéostat]';

  @override
  String get tt_tch_og_principalbuttons1 => '[Touche \"Position d\'aiguille\"]';

  @override
  String get tt_tch_og_principalbuttons2 => '[Touche \"Coupe-fil\"]';

  @override
  String get tt_tch_og_principalbuttons3 => '[Touche \"Releveur du pied-de-biche\"]';

  @override
  String get tt_tch_og_principalbuttons4 => '[Touche \"Enfilage automatique\"]';

  @override
  String get tt_tch_og_principalbuttons5 => '[Touche \"Marche/arrêt\"]';

  @override
  String get tt_tch_og_principalbuttons6 => '[Touche \"Point inverse\"]';

  @override
  String get tt_tch_og_principalbuttons7 => '[Touche \"Renfort/maintien\"]';

  @override
  String get tt_tch_og_basic_operation1 => '[Enfilage supérieur]';

  @override
  String get tt_tch_og_basic_operation2 => '[Bobinage de la canette]';

  @override
  String get tt_tch_og_basic_operation3 => '[Remplacement de l\'aiguille]';

  @override
  String get tt_tch_og_basic_operation4 => '[Changement du pied-de-biche]';

  @override
  String get tt_tch_og_basic_operation5 => '[Mise en place de la canette]';

  @override
  String get tt_tch_og_emb_basic_operation1 => '[Réglage de la tension du fil]';

  @override
  String get tt_tch_og_emb_basic_operation2 => '[Application du renfort thermocollant (support) sur le tissu]';

  @override
  String get tt_tch_og_emb_basic_operation3 => '[Mise en place du tissu]';

  @override
  String get tt_tch_og_emb_basic_operation4 => '[Fixation du cadre à broder]';

  @override
  String get tt_tch_og_emb_basic_operation5 => '[Fixation de l\'unité de broderie]';

  @override
  String get tt_tch_og_emb_basic_operation6 => '[Fixation du pied de broderie \"W\"]';

  @override
  String get tt_tch_og_emb_basic_operation7 => '[Renfort correct à utiliser]';

  @override
  String get tt_tch_maintenance1 => '[Nettoyage du logement et du boîtier de la canette]';

  @override
  String get tt_utl_category01 => 'Points droits/surfilage';

  @override
  String get tt_utl_category02 => 'Points décoratifs';

  @override
  String get tt_utl_category03 => 'Points à l\'ancienne';

  @override
  String get tt_utl_category04 => 'Boutonnières/Points d\'arrêt';

  @override
  String get tt_utl_category05 => 'Couture multidirectionnelle';

  @override
  String get tt_utl_category_q => 'Points quilting (courtepointe)';

  @override
  String get tt_utl_category_s => 'Autres points';

  @override
  String get tt_utl_category_t => 'Finition des points en biseau (Tapering)';

  @override
  String get tt_utl_stitchpreview => 'Aperçu';

  @override
  String get tt_utl_projecter => 'Fonctions du projecteur';

  @override
  String get tt_utl_guideline => 'Ligne de guidage';

  @override
  String get tt_utl_editmenu => 'Menu Modifier';

  @override
  String get tt_utl_freemotion => 'Mode piqué-libre';

  @override
  String get tt_utl_repeat_stitch_atamadashi => 'Retour au début';

  @override
  String get tt_utl_alone_repeat => 'Couture de points uniques/en continu';

  @override
  String get tt_utl_utilityflipvertical => 'Image en miroir';

  @override
  String get tt_utl_twinneedle => 'Aiguille simple/ aiguille jumelée';

  @override
  String get tt_utl_buttonholemanual => 'Longueur de fente de boutonnière';

  @override
  String get tt_utl_endpointsetting => 'Réglage du point final';

  @override
  String get tt_utl_tapering => 'Finition des points en biseau (Tapering)';

  @override
  String get tt_utl_autoreverse => 'Point de renfort automatique';

  @override
  String get tt_utl_scissor => 'Coupe-fil automatique';

  @override
  String get tt_utl_needlestopposition => 'Réglage de la position d\'arrêt de l\'aiguille';

  @override
  String get tt_utl_pivot => 'Pivotement/Haut automatique';

  @override
  String get tt_utl_threadcolor => 'Modification de la couleur de fil';

  @override
  String get tt_utl_category06 => 'Large et varié';

  @override
  String get tt_utl_category07 => 'Botanique large';

  @override
  String get tt_utl_category08 => 'Motifs et messages larges';

  @override
  String get tt_utl_category09 => 'Étroit et varié';

  @override
  String get tt_utl_category10 => 'Botanique étroit';

  @override
  String get tt_utl_category11 => 'Broderie chenille/mèche (Candlewicking)';

  @override
  String get tt_utl_category12 => 'Plumetis/Satin large';

  @override
  String get tt_utl_category13 => 'Feston';

  @override
  String get tt_utl_category14 => 'Points de croix';

  @override
  String get tt_utl_category15 => 'Point utilitaire combinable';

  @override
  String get tt_utl_category16 => 'Disney';

  @override
  String get tt_utl_category17 => 'Police gothique';

  @override
  String get tt_utl_category18 => 'Police manuscrite';

  @override
  String get tt_utl_category19 => 'Police contour';

  @override
  String get tt_utl_category20 => 'Police cyrillique';

  @override
  String get tt_deco_category_pocket => 'Poche(Mémoire externe/de la machine)';

  @override
  String get tt_deco_mycustomsititch => 'Fonction « MY CUSTOM STITCH (MON POINT PERSONNALISÉ) »';

  @override
  String get tt_deco_stitchpreview => 'Aperçu';

  @override
  String get tt_deco_projecter => 'Fonctions du projecteur';

  @override
  String get tt_deco_guidline => 'Ligne de guidage';

  @override
  String get tt_deco_editmenu => 'Menu Modifier';

  @override
  String get tt_deco_memory => 'Enregistrer les données du motif de points';

  @override
  String get tt_deco_threadcolor => 'Modification de la couleur de fil';

  @override
  String get tt_deco_stitchplus => 'Ajouter un motif de points';

  @override
  String get tt_deco_stitchselectall => 'Activation/désactivation de la sélection Tout';

  @override
  String get tt_deco_pivot => 'Pivotement/Haut automatique';

  @override
  String get tt_deco_needlestopposition => 'Réglage de la position d\'arrêt de l\'aiguille';

  @override
  String get tt_deco_scissor => 'Coupe-fil automatique';

  @override
  String get tt_deco_autoreverse => 'Point de renfort automatique';

  @override
  String get tt_deco_stitchstep1 => 'Effet dégradé';

  @override
  String get tt_deco_stitchstep2 => 'Effet dégradé';

  @override
  String get tt_deco_filemanager => 'Gestionnaire de fichiers';

  @override
  String get tt_deco_filemanager_selectall => 'Sélectionner tout ';

  @override
  String get tt_deco_filemanager_selectnone => 'Désélectionner tout';

  @override
  String get tt_deco_filemanager_delete => 'Supprimer';

  @override
  String get tt_deco_filemanager_memory => 'Enregistrer les motifs de points sélectionnés dans la mémoire de la machine.';

  @override
  String get tt_deco_freemotion => 'Mode piqué-libre';

  @override
  String get tt_deco_repeat_stitch_atamadashi => 'Retour au début';

  @override
  String get tt_deco_alone_repeat => 'Couture de points uniques/en continu';

  @override
  String get tt_deco_utilityfliphorizon => 'Image en miroir horizontale';

  @override
  String get tt_deco_utilityflipvertical => 'Image en miroir verticale';

  @override
  String get tt_deco_alone_single => 'Aiguille simple/ aiguille jumelée';

  @override
  String get tt_deco_delete => 'Supprimer';

  @override
  String get tt_deco_density => 'Densité du fil';

  @override
  String get tt_deco_elongator => 'Élongation';

  @override
  String get tt_deco_spacing => 'Espacement des caractères';

  @override
  String get tt_deco_stitchsizelink => 'Conserver les proportions';

  @override
  String get tt_deco_endpointsetting => 'Réglage du point final';

  @override
  String get tt_mcs_triplesewing => 'Points uniques/triples';

  @override
  String get tt_mcs_pointdelete => 'Effacer point';

  @override
  String get tt_mcs_blockmove => 'Déplacer bloc';

  @override
  String get tt_mcs_insert => 'Insérer';

  @override
  String get tt_utl_mcspointset => 'Régler';

  @override
  String get tt_mcs_contents => 'Importer des motifs de points';

  @override
  String get tt_mcs_memory => 'Enregistrer les données du motif de points';

  @override
  String get tt_utl_sr_guideline => 'Ligne de guidage';

  @override
  String get tt_utl_sr_sensingline => 'Ligne de détection';

  @override
  String get tt_utl_sr_srstatus => 'État du régulateur de point';

  @override
  String get tt_embcate_embpatterns => 'Motifs de broderie';

  @override
  String get tt_embcate_character => 'Motifs de caractères';

  @override
  String get tt_embcate_decoalphabet => 'Motifs alphabet décoratif';

  @override
  String get tt_embcate_frame => 'Motifs d\'encadrement';

  @override
  String get tt_embcate_utility => 'Motifs de boutonnières/ Motifs de broderie courants';

  @override
  String get tt_embcate_split => 'Motifs de broderie fractionnés';

  @override
  String get tt_embcate_long_stitch => 'Motifs de broderie en point long';

  @override
  String get tt_embcate_quilt => 'Motifs pour fractionnement de bordure de Quilt (courtepointe), et Quilt (courtepointe) de bord à bord';

  @override
  String get tt_embcate_b_disney => 'Motifs Disney';

  @override
  String get tt_embcate_couching => 'Motifs pour fil couché';

  @override
  String get tt_embcate_t_exclusives => 'Exclusivités';

  @override
  String get tt_embcate_memory => 'Motifs enregistrés dans la mémoire de la machine, un support USB, etc...';

  @override
  String get tt_emb_pantool => 'Outil Main';

  @override
  String get tt_emb_backgroundscan => 'Numérisation du tissu';

  @override
  String get tt_emb_realpreview => 'Aperçu';

  @override
  String get tt_emb_memory => 'Mémoire';

  @override
  String get tt_emb_redo => 'Refaire';

  @override
  String get tt_emb_undo => 'Annuler';

  @override
  String get tt_emb_delete => 'Supprimer';

  @override
  String get tt_emb_select => 'Sélection';

  @override
  String get tt_emb_multipleselect => 'Sélection multiple';

  @override
  String get tt_emb_editsize => 'Taille';

  @override
  String get tt_emb_editmove => 'Déplacer';

  @override
  String get tt_emb_editgroup => 'Grouper/dégrouper';

  @override
  String get tt_emb_editrotate => 'Pivoter';

  @override
  String get tt_emb_editflip => 'Miroir horizontal';

  @override
  String get tt_emb_editduplicate => 'Dupliquer';

  @override
  String get tt_emb_editdensity => 'Densité';

  @override
  String get tt_emb_editborder => 'Fonction de bordure (Conception de motifs répétés)';

  @override
  String get tt_emb_editapplique => 'Pièce d\'appliqué';

  @override
  String get tt_emb_editchangecolor => 'Palette de couleurs des fils';

  @override
  String get tt_emb_edittextedit => 'Modifier les motifs de caractères';

  @override
  String get tt_emb_editalign => 'Alignement';

  @override
  String get tt_emb_editstippling => 'Piqué libre';

  @override
  String get tt_emb_editoutline => 'Extraction de contour';

  @override
  String get tt_emb_editorder => 'Ordre de broderie';

  @override
  String get tt_emb_editnotsew => 'Fonction non brodé';

  @override
  String get tt_emb_textsize => 'Taille';

  @override
  String get tt_emb_textarray => 'Arrangement';

  @override
  String get tt_emb_textspacing => 'Espacement des caractères';

  @override
  String get tt_emb_textalign => 'Alignement';

  @override
  String get tt_emb_embfootw => 'Vérification du point de chute de l\'aiguille';

  @override
  String get tt_emb_emb_projectorsetting => 'Réglages du projecteur';

  @override
  String get tt_emb_embprojector => 'Projecteur';

  @override
  String get tt_emb_embmove => 'Déplacer';

  @override
  String get tt_emb_embrotate => 'Pivoter';

  @override
  String get tt_emb_embbasting => 'Faufilage';

  @override
  String get tt_emb_embsnowman => 'Positionnement de la broderie';

  @override
  String get tt_emb_embonecolorsew => 'Broderie ininterrompue';

  @override
  String get tt_emb_embcolorsorting => 'Tri des couleurs';

  @override
  String get tt_emb_embconnectsew => 'Liaison de motif';

  @override
  String get tt_emb_embframemove => 'Déplacement du cadre: Le cadre se déplacera temporairement vers le centre.';

  @override
  String get tt_emb_embmemory => 'Mémoire';

  @override
  String get tt_emb_embmasktrace => 'Zone de trace';

  @override
  String get tt_emb_embstartposition => 'Point de départ';

  @override
  String get tt_emb_embneedlenumber => 'Avancer/Reculer';

  @override
  String get tt_emb_embfbcamera => 'Vue caméra';

  @override
  String get tt_emb_embthreadcutting => 'Coupure/Tension';

  @override
  String get tt_emb_embcolorbar => 'Une couleur/ toutes les couleurs pour la barre de progression';

  @override
  String get tt_emb_patterninfo => 'Informations du motif';

  @override
  String get tt_emb_previewsim => 'Simulateur de point';

  @override
  String get tt_emb_sewtrim_endcolor => 'Coupe du fil en fin de couleur';

  @override
  String get tt_emb_sewtrim_jumpstitch => 'Excédent de fil à couper';

  @override
  String get tt_emb_previewframe => 'Aperçu du cadre à broder';

  @override
  String get tt_emb_size_normalstb => 'Changer la taille du motif tout en conservant le nombre de points/ la densité du fil';

  @override
  String get tt_emb_edit_border_vert => 'Répétition/ suppression de motif verticalement';

  @override
  String get tt_emb_edit_border_horiz => 'Répétition/ suppression de motif horizontalement';

  @override
  String get tt_emb_edit_border_dividervert => 'Coupe de motif verticalement';

  @override
  String get tt_emb_edit_border_dividehoriz => 'Coupe de motif horizontalement';

  @override
  String get tt_emb_edit_border_threadmark => 'Repères de fils';

  @override
  String get tt_emb_edit_border_reset => 'Réinitialiser';

  @override
  String get tt_emb_emb_rotate_reset => 'Réinitialiser';

  @override
  String get tt_emb_edit_rotate_reset => 'Réinitialiser';

  @override
  String get tt_emb_camera_rotate_reset => 'Réinitialiser';

  @override
  String get tt_emb_edit_font_spacing_reset => 'Réinitialiser';

  @override
  String get tt_emb_edit_align_reset => 'Réinitialiser';

  @override
  String get tt_emb_edit_size_reset => 'Réinitialiser';

  @override
  String get tt_emb_edit_order_reset => 'Réinitialiser';

  @override
  String get tt_emb_quiltborder_color_reset => 'Réinitialiser';

  @override
  String get tt_emb_edit_color_reset => 'Réinitialiser';

  @override
  String get tt_emb_photositich_size_change_reset => 'Réinitialiser';

  @override
  String get tt_emb_edit_projlcd_switch_fb_reset => 'Réinitialiser';

  @override
  String get tt_emb_edit_projlcd_align_reset => 'Réinitialiser';

  @override
  String get tt_emb_edit_projlcd_border_reset => 'Réinitialiser';

  @override
  String get tt_emb_edit_projlcd_rotate_reset => 'Réinitialiser';

  @override
  String get tt_emb_edit_projlcd_size_reset => 'Réinitialiser';

  @override
  String get tt_mdc_paint_rotate_reset => 'Réinitialiser';

  @override
  String get tt_mdc_paint_size_input_reset => 'Réinitialiser';

  @override
  String get tt_mdc_paint_size_reset => 'Réinitialiser';

  @override
  String get tt_emb_newapplique_color_selectall => 'Sélectionner tout ';

  @override
  String get tt_emb_newapplique_color_selectnone => 'Désélectionner tout';

  @override
  String get tt_emb_color_selectall => 'Sélectionner une seule couleur/ toutes les couleurs';

  @override
  String get tt_emb_colorcolorshuffling => 'Color Shuffling(Couleurs aléatoires)';

  @override
  String get tt_emb_colorvisualizer => 'Color Visualizer (Visualiseur Couleurs)';

  @override
  String get tt_emb_editselectall => 'Sélectionner tout';

  @override
  String get tt_emb_editdeselectall => 'Désélectionner tout';

  @override
  String get tt_emb_infoprintimage => 'Combiné avec l\'image à imprimer';

  @override
  String get tt_emb_infooutputfiles => '3 fichiers PDF (pour tissu imprimable/ transfert au fer à repasser/ positionnement) sont copiés dans le support USB.';

  @override
  String get tt_emb_filemanager => 'Gestionnaire de fichiers';

  @override
  String get tt_emb_filemanager_selectall => 'Sélectionner tout ';

  @override
  String get tt_emb_filemanager_selectnone => 'Désélectionner tout';

  @override
  String get tt_emb_filemanager_delete => 'Supprimer';

  @override
  String get tt_emb_filemanager_memory => 'Enregistrer les motifs sélectionnés dans la mémoire de la machine.';

  @override
  String get tt_emb_easystippling_stippling => 'Motif de piqué libre';

  @override
  String get tt_emb_easystippling_echo => 'Motif de quilting (courtepointe) en écho';

  @override
  String get tt_emb_easystippling_decorativefill => 'Motif de remplissage décoratif';

  @override
  String get tt_emb_quitlsash_startpoint => 'Projeter le point de départ';

  @override
  String get tt_emb_quitlsash_endtpoint => 'Projeter le point final';

  @override
  String get tt_emb_connect_migimawari => 'La position du deuxième motif change dans le sens horaire';

  @override
  String get tt_emb_connect_hidarimawari => 'La position du deuxième motif change dans le sens antihoraire';

  @override
  String get tt_emb_connect_rotate => 'Pivoter';

  @override
  String get tt_emb_quiltborder_save => 'Mémoire';

  @override
  String get tt_mdc_pantool => 'Outil Main';

  @override
  String get tt_mdc_scanmenu => 'Des motifs personnalisés peuvent être créés à l\'aide d\'images numérisées ou de fichiers de données d\'image.';

  @override
  String get tt_mdc_datacall => 'Rappeler des données de tracé de motif (.pm9)';

  @override
  String get tt_mdc_linetool => 'Outil Trait';

  @override
  String get tt_mdc_lineproperty => 'Propriétés de la ligne';

  @override
  String get tt_mdc_linespoit => 'Outil Pipette pour trait';

  @override
  String get tt_mdc_linepouring => 'Outil Pot de peinture pour trait';

  @override
  String get tt_mdc_brushtool => 'Outil Pinceau';

  @override
  String get tt_mdc_brushproperty => 'Propriétés de la zone';

  @override
  String get tt_mdc_brushspoit => 'Outil Pipette pour partie';

  @override
  String get tt_mdc_brushpouring => 'Outil Pot de peinture pour partie';

  @override
  String get tt_mdc_painteraser => 'Gomme';

  @override
  String get tt_mdc_paintstamp => 'Formes d\'estampille';

  @override
  String get tt_mdc_paintsize => 'Taille';

  @override
  String get tt_mdc_paintrotate => 'Pivoter';

  @override
  String get tt_mdc_paintflip => 'Image en miroir';

  @override
  String get tt_mdc_paintduplicate => 'Dupliquer';

  @override
  String get tt_mdc_paintcut => 'Couper';

  @override
  String get tt_mdc_paintpaste => 'Coller';

  @override
  String get tt_mdc_memory => 'Enregistrez les données de tracé de motif (.pm9)';

  @override
  String get tt_mdc_select => 'Sélection';

  @override
  String get tt_mdc_redo => 'Refaire';

  @override
  String get tt_mdc_undo => 'Annuler';

  @override
  String get tt_mdc_allclear => 'Effacer tout';

  @override
  String get tt_mdc_lineopen => 'Trait à main levée avec l\'extrémité ouverte';

  @override
  String get tt_mdc_lineclose => 'Trait à main levée avec fermeture automatique de l\'extrémité';

  @override
  String get tt_mdc_lineline => 'Trait droit d\'un trait';

  @override
  String get tt_mdc_linepolygonal => 'Forme polygonale';

  @override
  String get tt_mdc_stitchzigzag => 'Point zigzag';

  @override
  String get tt_mdc_stitchrunning => 'Contour piqué';

  @override
  String get tt_mdc_stitchtriple => 'Point triple';

  @override
  String get tt_mdc_stitchcandle => 'Point de broderie chenille/mèche (Candlewicking)';

  @override
  String get tt_mdc_stitchchain => 'Point de chaînette';

  @override
  String get tt_mdc_stitchestitch => 'Point en E';

  @override
  String get tt_mdc_stitchvsitich => 'Point en V';

  @override
  String get tt_mdc_stitchmotif => 'Points de motif';

  @override
  String get tt_mdc_stitchnnotsew => 'Trait sans point';

  @override
  String get tt_mdc_stitchzigzaglowdensity => 'Point zigzag d\'appliqué';

  @override
  String get tt_mdc_regiontatami => 'Motif de points de remplissage';

  @override
  String get tt_mdc_regionstippling => 'Motif de piqué libre';

  @override
  String get tt_mdc_regiondecorativefill => 'Motifs de remplissage décoratifs';

  @override
  String get tt_mdc_regionnotsew => 'Pas de points';

  @override
  String get tt_mdc_stamp1 => 'Formes de base';

  @override
  String get tt_mdc_stamp2 => 'Formes fermées';

  @override
  String get tt_mdc_stamp3 => 'Formes ouvertes';

  @override
  String get tt_mdc_stamp4 => 'Contours enregistrés';

  @override
  String get tt_mdc_stamp5 => 'Zones de broderie du cadre';

  @override
  String get tt_mdc_stamp6 => 'Contours de découpe';

  @override
  String get tt_mdc_select_rectangle => 'Sélection par cadre';

  @override
  String get tt_mdc_select_continuousrectangle => 'Sélection par polygone';

  @override
  String get tt_mdc_select_free => 'Sélection par courbe libre';

  @override
  String get tt_mdc_select_auto => 'Sélection automatique';

  @override
  String get tt_mdc_select_all => 'Sélectionner tout';

  @override
  String get tt_mdc_memory_drawemb => 'Enregistrez les données de tracé de motif (.pm9) et les données de broderie (.phx).';

  @override
  String get tt_mdc_embset_pantool => 'Outil Main';

  @override
  String get tt_mdc_embset_patterninfo => 'Informations du motif';

  @override
  String get tt_mdc_embset_realpreview => 'Aperçu';

  @override
  String get tt_mdc_embset_projector => 'Projecteur';

  @override
  String get tt_mdc_embset_projectorsetting => 'Réglages du projecteur';

  @override
  String get tt_mdc_zigzagwidth => 'Largeur du point zigzag';

  @override
  String get tt_mdc_zigzagdensity => 'Densité';

  @override
  String get tt_mdc_runpitch => 'Longueur du point';

  @override
  String get tt_mdc_running_undersew => 'Réduire étirement';

  @override
  String get tt_mdc_candlewicksize => 'Taille';

  @override
  String get tt_mdc_candlewickspacing => 'Espacement';

  @override
  String get tt_mdc_chainsize => 'Taille';

  @override
  String get tt_mdc_chainthickness => 'Épaisseur';

  @override
  String get tt_mdc_estitchwidth => 'Largeur du point';

  @override
  String get tt_mdc_estitchspacing => 'Espacement';

  @override
  String get tt_mdc_estitchthickness => 'Épaisseur';

  @override
  String get tt_mdc_estitchflip => 'Inverser';

  @override
  String get tt_mdc_vstitchwidth => 'Largeur du point';

  @override
  String get tt_mdc_vstitchspacing => 'Espacement';

  @override
  String get tt_mdc_vstitchthickness => 'Épaisseur';

  @override
  String get tt_mdc_vstitchflip => 'Inverser';

  @override
  String get tt_mdc_motifstitchsize => 'Taille';

  @override
  String get tt_mdc_motifstitchspacing => 'Espacement';

  @override
  String get tt_mdc_motifstitchflip => 'Inverser';

  @override
  String get tt_mdc_zigzagwidth_2 => 'Largeur du point zigzag';

  @override
  String get tt_mdc_zigzagdensity_2 => 'Densité';

  @override
  String get tt_mdc_tatamiderection => 'Direction';

  @override
  String get tt_mdc_tatamidensity => 'Densité';

  @override
  String get tt_mdc_tatamipullconpen => 'Compensation d\'étirement';

  @override
  String get tt_mdc_tatamiundersewing => 'Réduire étirement';

  @override
  String get tt_mdc_stiprunpitch => 'Longueur du point';

  @override
  String get tt_mdc_stipspacing => 'Espacement';

  @override
  String get tt_mdc_stipdistance => 'Distance';

  @override
  String get tt_mdc_stipsingletriple => 'Point unique/triple';

  @override
  String get tt_mdc_decofillsize => 'Taille';

  @override
  String get tt_mdc_decofilldirection => 'Direction';

  @override
  String get tt_mdc_decofilloutline => 'Contour pour réduire la coupure de fils';

  @override
  String get tt_mdc_decofillrandomshift => 'Décalage aléatoire';

  @override
  String get tt_mdc_decofillpositionoffset => 'Décalage de position';

  @override
  String get tt_mdc_decofillthickness1 => 'Épaisseur';

  @override
  String get tt_mdc_decofillthickness3 => 'Épaisseur';

  @override
  String get tt_mdc_decofillthickness1_2 => 'Simple-double';

  @override
  String get tt_mdc_decofillthickness2_3 => 'Double-triple';

  @override
  String get tt_mdc_stitchlink => 'Sélectionner des objets avec les mêmes réglages de point en même temps';

  @override
  String get tt_mdc_fill_linereading => 'Les zones et périmètres linéaires sont convertis en contours. Choisir l\'épaisseur du contour.';

  @override
  String get tt_mdc_fill_linecolor => 'Les contours extraits avec la couleur spécifiée sont convertis en attributs de trait.';

  @override
  String get tt_emb_photostitch_backremoval => 'Élimination de l\'arrière-plan';

  @override
  String get tt_emb_photostitch_framing => 'Cadrage de l’image';

  @override
  String get tt_emb_photostitch_fittoframe => 'Adapter au cadre';

  @override
  String get tt_emb_photostitch_backremoval_scopeplus => 'Ajouter une nouvelle zone à rogner :Marquer la zone à rogner d\'une ligne.';

  @override
  String get tt_emb_photostitch_backremoval_scopeminus => 'Effacer la zone rognée :Marquer la zone à ne pas rogner d\'une ligne.';

  @override
  String get tt_emb_photostitch_backremoval_erase => 'Effacer l\'exécution de trait spécifiée.';

  @override
  String get tt_emb_photostitch_backremoval_trash => 'Effacer toute l\'exécution de traits.';

  @override
  String get tt_emb_photostitch_backremoval_blind => 'Afficher/Masquer toutes les lignes tracées au crayon.';

  @override
  String get tt_emb_photostitch_styleusecolor => 'ON : utilisez les couleurs de l\'image de style / OFF : utilisez les couleurs de la photo originale';

  @override
  String get tt_emb_photostitch_colortune => 'Réglage de la couleur';

  @override
  String get tt_emb_photostitch_colorlis_allselect => 'Conserver / Effacer toutes les couleurs de fils dans la liste des couleurs';

  @override
  String get tt_emb_photostitch_colorlis_add => 'Ajoutez une couleur à la liste de couleurs';

  @override
  String get tt_emb_photostitch_colorlis_remove => 'Supprimer la couleur sélectionnée de la liste des couleurs';

  @override
  String get tt_emb_photostitch_pantool => 'Outil Main';

  @override
  String get tt_emb_photostitch_memory => 'Mémoire';

  @override
  String get tt_emb_edit_projectorsetting => 'Réglages du projecteur';

  @override
  String get tt_emb_edit_projector => 'Projecteur';

  @override
  String get tt_settings_reset_3type => 'Réinitialiser les réglages (de couture/généraux/de broderie)';

  @override
  String get tt_settings_screenimage_usb => 'Enregistrer une image de l\'écran de réglages sur un support USB';

  @override
  String get tt_camera_emb_screenshot => 'Enregistrez une image de la caméra sur le support USB.';

  @override
  String get tt_camera_emb_grid => 'Afficher/Masquer la grille';

  @override
  String get tt_camera_emb_needlepoint => 'Afficher/Masquer le point de chute de l\'aiguille';

  @override
  String get tt_camera_util_screenshot => 'Enregistrez une image de la caméra sur le support USB.';

  @override
  String get tt_camera_util_grid => 'Afficher/Masquer la grille';

  @override
  String get tt_camera_util_needlepoint => 'Afficher/Masquer le point de chute de l\'aiguille';
}
