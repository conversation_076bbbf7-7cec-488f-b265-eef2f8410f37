// ignore_for_file: camel_case_types, constant_identifier_names

import 'dart:ffi' as ffi;
import 'dart:ffi';

import 'package:ffi/ffi.dart' as ffi;

/// Bindings for `src/EEL_UtlEditIF.h`.
///
/// Regenerate bindings with `flutter pub run ffigen --config ffigen.yaml`.
///
class DevEditBindings {
  /// Holds the symbol lookup function.
  final ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
      _lookup;

  /// The symbols are looked up in [dynamicLibrary].
  DevEditBindings(ffi.DynamicLibrary dynamicLibrary)
      : _lookup = dynamicLibrary.lookup;

  /// The symbols are looked up with [lookup].
  DevEditBindings.fromLookup(
      ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
          lookup)
      : _lookup = lookup;

  /// ////////////////実用////////////////////////////////////////
  /// スピードコントロールレバーを使用してWIDTH調節を行う。
  int setWidthControl(
    bool isOn,
  ) {
    return _setWidthControl(
      isOn,
    );
  }

  late final _setWidthControlPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setWidthControl');
  late final _setWidthControl =
      _setWidthControlPtr.asFunction<int Function(bool)>();

  int getWidthControl(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getWidthControl(
      isOn,
    );
  }

  late final _getWidthControlPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getWidthControl');
  late final _getWidthControl =
      _getWidthControlPtr.asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getWidthControlDefault(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getWidthControlDefault(
      isOn,
    );
  }

  late final _getWidthControlDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getWidthControlDefault');
  late final _getWidthControlDefault = _getWidthControlDefaultPtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// 送り調整（模様調整－縦方向）
  int setFineAdjustVerti(
    int value,
  ) {
    return _setFineAdjustVerti(
      value,
    );
  }

  late final _setFineAdjustVertiPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int16)>>(
          'setFineAdjustVerti');
  late final _setFineAdjustVerti =
      _setFineAdjustVertiPtr.asFunction<int Function(int)>();

  int getFineAdjustVerti(
    ffi.Pointer<ffi.Int16> value,
  ) {
    return _getFineAdjustVerti(
      value,
    );
  }

  late final _getFineAdjustVertiPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int16>)>>(
          'getFineAdjustVerti');
  late final _getFineAdjustVerti =
      _getFineAdjustVertiPtr.asFunction<int Function(ffi.Pointer<ffi.Int16>)>();

  int getFineAdjustVertiValueList(
    ffi.Pointer<UserSettingItemValue> valueInfo,
  ) {
    return _getFineAdjustVertiValueList(
      valueInfo,
    );
  }

  late final _getFineAdjustVertiValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValue>)>>(
      'getFineAdjustVertiValueList');
  late final _getFineAdjustVertiValueList = _getFineAdjustVertiValueListPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValue>)>();

  /// 横送り調整（模様調整－横方向）
  int setFineAdjustHoriz(
    int value,
  ) {
    return _setFineAdjustHoriz(
      value,
    );
  }

  late final _setFineAdjustHorizPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int16)>>(
          'setFineAdjustHoriz');
  late final _setFineAdjustHoriz =
      _setFineAdjustHorizPtr.asFunction<int Function(int)>();

  int getFineAdjustHoriz(
    ffi.Pointer<ffi.Int16> value,
  ) {
    return _getFineAdjustHoriz(
      value,
    );
  }

  late final _getFineAdjustHorizPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int16>)>>(
          'getFineAdjustHoriz');
  late final _getFineAdjustHoriz =
      _getFineAdjustHorizPtr.asFunction<int Function(ffi.Pointer<ffi.Int16>)>();

  int getFineAdjustHorizValueList(
    ffi.Pointer<UserSettingItemValue> valueInfo,
  ) {
    return _getFineAdjustHorizValueList(
      valueInfo,
    );
  }

  late final _getFineAdjustHorizValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValue>)>>(
      'getFineAdjustHorizValueList');
  late final _getFineAdjustHorizValueList = _getFineAdjustHorizValueListPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValue>)>();

  /// 押え高さ調整
  int setPresserFootHeight(
    int value,
  ) {
    return _setPresserFootHeight(
      value,
    );
  }

  late final _setPresserFootHeightPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int16)>>(
          'setPresserFootHeight');
  late final _setPresserFootHeight =
      _setPresserFootHeightPtr.asFunction<int Function(int)>();

  int getPresserFootHeight(
    ffi.Pointer<ffi.Int16> value,
  ) {
    return _getPresserFootHeight(
      value,
    );
  }

  late final _getPresserFootHeightPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int16>)>>(
          'getPresserFootHeight');
  late final _getPresserFootHeight = _getPresserFootHeightPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int16>)>();

  int getPresserFootHeightValueList(
    ffi.Pointer<UserSettingItemValueList> valueInfo,
  ) {
    return _getPresserFootHeightValueList(
      valueInfo,
    );
  }

  late final _getPresserFootHeightValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'getPresserFootHeightValueList');
  late final _getPresserFootHeightValueList = _getPresserFootHeightValueListPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// 押えの圧力を調節する
  int setPresserFootPressure(
    int value,
  ) {
    return _setPresserFootPressure(
      value,
    );
  }

  late final _setPresserFootPressurePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int16)>>(
          'setPresserFootPressure');
  late final _setPresserFootPressure =
      _setPresserFootPressurePtr.asFunction<int Function(int)>();

  int getPresserFootPressure(
    ffi.Pointer<ffi.Int16> value,
  ) {
    return _getPresserFootPressure(
      value,
    );
  }

  late final _getPresserFootPressurePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int16>)>>(
          'getPresserFootPressure');
  late final _getPresserFootPressure = _getPresserFootPressurePtr
      .asFunction<int Function(ffi.Pointer<ffi.Int16>)>();

  int getPresserFootPressureValueList(
    ffi.Pointer<UserSettingItemValueList> valueInfo,
  ) {
    return _getPresserFootPressureValueList(
      valueInfo,
    );
  }

  late final _getPresserFootPressureValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'getPresserFootPressureValueList');
  late final _getPresserFootPressureValueList =
      _getPresserFootPressureValueListPtr
          .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// センサーシステム（自動押え圧補整）
  int setAutomaticFabricSensorSystem(
    bool isOn,
  ) {
    return _setAutomaticFabricSensorSystem(
      isOn,
    );
  }

  late final _setAutomaticFabricSensorSystemPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setAutomaticFabricSensorSystem');
  late final _setAutomaticFabricSensorSystem =
      _setAutomaticFabricSensorSystemPtr.asFunction<int Function(bool)>();

  int getAutomaticFabricSensorSystem(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getAutomaticFabricSensorSystem(
      isOn,
    );
  }

  late final _getAutomaticFabricSensorSystemPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getAutomaticFabricSensorSystem');
  late final _getAutomaticFabricSensorSystem =
      _getAutomaticFabricSensorSystemPtr
          .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getAutomaticFabricSensorSystemDefault(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getAutomaticFabricSensorSystemDefault(
      isOn,
    );
  }

  late final _getAutomaticFabricSensorSystemDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getAutomaticFabricSensorSystemDefault');
  late final _getAutomaticFabricSensorSystemDefault =
      _getAutomaticFabricSensorSystemDefaultPtr
          .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// 初期針位置（基線切り替え）
  int setBaseLine(
    bool isCenter,
  ) {
    return _setBaseLine(
      isCenter,
    );
  }

  late final _setBaseLinePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>('setBaseLine');
  late final _setBaseLine = _setBaseLinePtr.asFunction<int Function(bool)>();

  int getBaseLine(
    ffi.Pointer<ffi.Bool> isCenter,
  ) {
    return _getBaseLine(
      isCenter,
    );
  }

  late final _getBaseLinePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getBaseLine');
  late final _getBaseLine =
      _getBaseLinePtr.asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getBaseLineDefault(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getBaseLineDefault(
      isOn,
    );
  }

  late final _getBaseLineDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getBaseLineDefault');
  late final _getBaseLineDefault =
      _getBaseLineDefaultPtr.asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// ピボット押え高さ
  int setPivotingHeight(
    int value,
  ) {
    return _setPivotingHeight(
      value,
    );
  }

  late final _setPivotingHeightPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int16)>>(
          'setPivotingHeight');
  late final _setPivotingHeight =
      _setPivotingHeightPtr.asFunction<int Function(int)>();

  int getPivotingHeight(
    ffi.Pointer<ffi.Int16> value,
  ) {
    return _getPivotingHeight(
      value,
    );
  }

  late final _getPivotingHeightPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int16>)>>(
          'getPivotingHeight');
  late final _getPivotingHeight =
      _getPivotingHeightPtr.asFunction<int Function(ffi.Pointer<ffi.Int16>)>();

  int getPivotingHeightValueList(
    ffi.Pointer<UserSettingItemValueList> valueInfo,
  ) {
    return _getPivotingHeightValueList(
      valueInfo,
    );
  }

  late final _getPivotingHeightValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'getPivotingHeightValueList');
  late final _getPivotingHeightValueList = _getPivotingHeightValueListPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// フリーモーション押え高さ
  int setFreeMotionFootHeight(
    int value,
  ) {
    return _setFreeMotionFootHeight(
      value,
    );
  }

  late final _setFreeMotionFootHeightPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setFreeMotionFootHeight');
  late final _setFreeMotionFootHeight =
      _setFreeMotionFootHeightPtr.asFunction<int Function(int)>();

  int getFreeMotionFootHeight(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getFreeMotionFootHeight(
      value,
    );
  }

  late final _getFreeMotionFootHeightPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getFreeMotionFootHeight');
  late final _getFreeMotionFootHeight = _getFreeMotionFootHeightPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  /// PANEL_API DeviceErrorCode_t getDualFeedFeedAdjustmentValueList(UserSettingItemValue *valueInfo);
  int getFreeMotionFootHeightValueList(
    ffi.Pointer<UserSettingItemValueList> value,
  ) {
    return _getFreeMotionFootHeightValueList(
      value,
    );
  }

  late final _getFreeMotionFootHeightValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'getFreeMotionFootHeightValueList');
  late final _getFreeMotionFootHeightValueList =
      _getFreeMotionFootHeightValueListPtr
          .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// デュアルフィード送り調整
  int setDualFeedFeedAdjustment(
    int value,
  ) {
    return _setDualFeedFeedAdjustment(
      value,
    );
  }

  late final _setDualFeedFeedAdjustmentPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setDualFeedFeedAdjustment');
  late final _setDualFeedFeedAdjustment =
      _setDualFeedFeedAdjustmentPtr.asFunction<int Function(int)>();

  int getDualFeedFeedAdjustment(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getDualFeedFeedAdjustment(
      value,
    );
  }

  late final _getDualFeedFeedAdjustmentPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getDualFeedFeedAdjustment');
  late final _getDualFeedFeedAdjustment = _getDualFeedFeedAdjustmentPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getDualFeedFeedAdjustmentValueList(
    ffi.Pointer<UserSettingItemValue> valueInfo,
  ) {
    return _getDualFeedFeedAdjustmentValueList(
      valueInfo,
    );
  }

  late final _getDualFeedFeedAdjustmentValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValue>)>>(
      'getDualFeedFeedAdjustmentValueList');
  late final _getDualFeedFeedAdjustmentValueList =
      _getDualFeedFeedAdjustmentValueListPtr
          .asFunction<int Function(ffi.Pointer<UserSettingItemValue>)>();

  /// 自動下げの値を設定する
  int setUtlAutoDown(
    bool isOn,
  ) {
    return _setUtlAutoDown(
      isOn,
    );
  }

  late final _setUtlAutoDownPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setUtlAutoDown');
  late final _setUtlAutoDown =
      _setUtlAutoDownPtr.asFunction<int Function(bool)>();

  int getUtlAutoDown(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getUtlAutoDown(
      isOn,
    );
  }

  late final _getUtlAutoDownPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getUtlAutoDown');
  late final _getUtlAutoDown =
      _getUtlAutoDownPtr.asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getUtlAutoDownDefault(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getUtlAutoDownDefault(
      isOn,
    );
  }

  late final _getUtlAutoDownDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getUtlAutoDownDefault');
  late final _getUtlAutoDownDefault = _getUtlAutoDownDefaultPtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// 自動押え上下
  int setPressToTrim(
    bool isOn,
  ) {
    return _setPressToTrim(
      isOn,
    );
  }

  late final _setPressToTrimPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setPressToTrim');
  late final _setPressToTrim =
      _setPressToTrimPtr.asFunction<int Function(bool)>();

  int getPressToTrim(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getPressToTrim(
      isOn,
    );
  }

  late final _getPressToTrimPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getPressToTrim');
  late final _getPressToTrim =
      _getPressToTrimPtr.asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getPressToTrimDefault(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getPressToTrimDefault(
      isOn,
    );
  }

  late final _getPressToTrimDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getPressToTrimDefault');
  late final _getPressToTrimDefault = _getPressToTrimDefaultPtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// 実用のデフォルトカテゴリーを実用カテゴリーにするかキルトカテゴリーにするか設定する
  int setInitialStitchPage(
    bool isQuilt,
  ) {
    return _setInitialStitchPage(
      isQuilt,
    );
  }

  late final _setInitialStitchPagePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setInitialStitchPage');
  late final _setInitialStitchPage =
      _setInitialStitchPagePtr.asFunction<int Function(bool)>();

  int getInitialStitchPage(
    ffi.Pointer<ffi.Bool> isQuilt,
  ) {
    return _getInitialStitchPage(
      isQuilt,
    );
  }

  late final _getInitialStitchPagePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getInitialStitchPage');
  late final _getInitialStitchPage = _getInitialStitchPagePtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getInitialStitchPageDefault(
    ffi.Pointer<ffi.Bool> isQuilt,
  ) {
    return _getInitialStitchPageDefault(
      isQuilt,
    );
  }

  late final _getInitialStitchPageDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getInitialStitchPageDefault');
  late final _getInitialStitchPageDefault = _getInitialStitchPageDefaultPtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// 止め縫い優先
  int setReinforcementPriority(
    bool isOn,
  ) {
    return _setReinforcementPriority(
      isOn,
    );
  }

  late final _setReinforcementPriorityPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setReinforcementPriority');
  late final _setReinforcementPriority =
      _setReinforcementPriorityPtr.asFunction<int Function(bool)>();

  int getReinforcementPriority(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getReinforcementPriority(
      isOn,
    );
  }

  late final _getReinforcementPriorityPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getReinforcementPriority');
  late final _getReinforcementPriority = _getReinforcementPriorityPtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getReinforcementPriorityDefault(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getReinforcementPriorityDefault(
      isOn,
    );
  }

  late final _getReinforcementPriorityDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getReinforcementPriorityDefault');
  late final _getReinforcementPriorityDefault =
      _getReinforcementPriorityDefaultPtr
          .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// マルチファンクションフットコントローラー HeelSw
  int setHeelSwitch(
    int value,
  ) {
    return _setHeelSwitch(
      value,
    );
  }

  late final _setHeelSwitchPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setHeelSwitch');
  late final _setHeelSwitch = _setHeelSwitchPtr.asFunction<int Function(int)>();

  int getHeelSwitch(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getHeelSwitch(
      value,
    );
  }

  late final _getHeelSwitchPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getHeelSwitch');
  late final _getHeelSwitch =
      _getHeelSwitchPtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getHeelSwitchValueList(
    ffi.Pointer<UserSettingItemValueList> valueInfo,
  ) {
    return _getHeelSwitchValueList(
      valueInfo,
    );
  }

  late final _getHeelSwitchValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'getHeelSwitchValueList');
  late final _getHeelSwitchValueList = _getHeelSwitchValueListPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// マルチファンクションフットコントローラー
  int setSidePedal(
    int value,
  ) {
    return _setSidePedal(
      value,
    );
  }

  late final _setSidePedalPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>('setSidePedal');
  late final _setSidePedal = _setSidePedalPtr.asFunction<int Function(int)>();

  int getSidePedal(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getSidePedal(
      value,
    );
  }

  late final _getSidePedalPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getSidePedal');
  late final _getSidePedal =
      _getSidePedalPtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getSidePedalValueList(ffi.Pointer<UserSettingItemValueList> valueInfo) {
    return _getSidePedalValueList(valueInfo);
  }

  late final _getSidePedalValueListPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<UserSettingItemValueList>)>>('getSidePedalValueList');
  late final _getSidePedalValueList = _getSidePedalValueListPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  int setEndPointSettingTemporaryStop(bool isOn) {
    return _setEndPointSettingTemporaryStop(isOn);
  }

  late final _setEndPointSettingTemporaryStopPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setEndPointSettingTemporaryStop');
  late final _setEndPointSettingTemporaryStop =
      _setEndPointSettingTemporaryStopPtr.asFunction<int Function(bool)>();

  int getEndPointSettingTemporaryStop(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getEndPointSettingTemporaryStop(
      isOn,
    );
  }

  late final _getEndPointSettingTemporaryStopPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getEndPointSettingTemporaryStop');
  late final _getEndPointSettingTemporaryStop =
      _getEndPointSettingTemporaryStopPtr
          .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getEndPointSettingTemporaryStopDefault(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getEndPointSettingTemporaryStopDefault(
      isOn,
    );
  }

  late final _getEndPointSettingTemporaryStopDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getEndPointSettingTemporaryStopDefault');
  late final _getEndPointSettingTemporaryStopDefault =
      _getEndPointSettingTemporaryStopDefaultPtr
          .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// ////////////////共通////////////////////////////////////////
  /// 言語を切り替える
  int saveLanguage(
    int value,
  ) {
    return _saveLanguage(
      value,
    );
  }

  late final _saveLanguagePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>('saveLanguage');
  late final _saveLanguage = _saveLanguagePtr.asFunction<int Function(int)>();

  int getLanguage(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getLanguage(
      value,
    );
  }

  late final _getLanguagePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getLanguage');
  late final _getLanguage =
      _getLanguagePtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  /// ライトの明るさ調整
  int setLight(
    int value,
  ) {
    return _setLight(
      value,
    );
  }

  late final _setLightPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>('setLight');
  late final _setLight = _setLightPtr.asFunction<int Function(int)>();

  int getLight(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getLight(
      value,
    );
  }

  late final _getLightPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getLight');
  late final _getLight =
      _getLightPtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getLightValueList(
    ffi.Pointer<UserSettingItemValueList> valueInfo,
  ) {
    return _getLightValueList(
      valueInfo,
    );
  }

  late final _getLightValueListPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<UserSettingItemValueList>)>>('getLightValueList');
  late final _getLightValueList = _getLightValueListPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// 液晶画面の明るさを調整する
  int setScreenDisplayBrightness(
    int value,
  ) {
    return _setScreenDisplayBrightness(
      value,
    );
  }

  late final _setScreenDisplayBrightnessPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setScreenDisplayBrightness');
  late final _setScreenDisplayBrightness =
      _setScreenDisplayBrightnessPtr.asFunction<int Function(int)>();

  int getScreenDisplayBrightness(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getScreenDisplayBrightness(
      value,
    );
  }

  late final _getScreenDisplayBrightnessPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getScreenDisplayBrightness');
  late final _getScreenDisplayBrightness = _getScreenDisplayBrightnessPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getScreenDisplayBrightnessValueList(
    ffi.Pointer<UserSettingItemValueList> value,
  ) {
    return _getScreenDisplayBrightnessValueList(
      value,
    );
  }

  late final _getScreenDisplayBrightnessValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'getScreenDisplayBrightnessValueList');
  late final _getScreenDisplayBrightnessValueList =
      _getScreenDisplayBrightnessValueListPtr
          .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// 上糸/下糸センサー
  int setUpperAndBobbinThreadSensor(
    bool isDisabled,
  ) {
    return _setUpperAndBobbinThreadSensor(
      isDisabled,
    );
  }

  late final _setUpperAndBobbinThreadSensorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setUpperAndBobbinThreadSensor');
  late final _setUpperAndBobbinThreadSensor =
      _setUpperAndBobbinThreadSensorPtr.asFunction<int Function(bool)>();

  int getUpperAndBobbinThreadSensor(
    ffi.Pointer<ffi.Bool> isDisabled,
  ) {
    return _getUpperAndBobbinThreadSensor(
      isDisabled,
    );
  }

  late final _getUpperAndBobbinThreadSensorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getUpperAndBobbinThreadSensor');
  late final _getUpperAndBobbinThreadSensor = _getUpperAndBobbinThreadSensorPtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getUpperAndBobbinThreadSensorDefault(
    ffi.Pointer<ffi.Bool> isDisabled,
  ) {
    return _getUpperAndBobbinThreadSensorDefault(
      isDisabled,
    );
  }

  late final _getUpperAndBobbinThreadSensorDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getUpperAndBobbinThreadSensorDefault');
  late final _getUpperAndBobbinThreadSensorDefault =
      _getUpperAndBobbinThreadSensorDefaultPtr
          .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// 効果音ボリューム
  int saveMachineSpeakerVolume(
    int value,
  ) {
    return _saveMachineSpeakerVolume(
      value,
    );
  }

  late final _saveMachineSpeakerVolumePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'saveMachineSpeakerVolume');
  late final _saveMachineSpeakerVolume =
      _saveMachineSpeakerVolumePtr.asFunction<int Function(int)>();

  int getMachineSpeakerVolume(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getMachineSpeakerVolume(
      value,
    );
  }

  late final _getMachineSpeakerVolumePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getMachineSpeakerVolume');
  late final _getMachineSpeakerVolume = _getMachineSpeakerVolumePtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  //ボイスボリューム
  int saveVoiceVolume(
    int value,
  ) {
    return _saveVoiceVolume(
      value,
    );
  }

  late final _saveVoiceVolumePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'saveVoiceVolume');
  late final _saveVoiceVolume =
      _saveVoiceVolumePtr.asFunction<int Function(int)>();

  int getVoiceVolume(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getVoiceVolume(
      value,
    );
  }

  late final _getVoiceVolumePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getVoiceVolume');
  late final _getVoiceVolume =
      _getVoiceVolumePtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  //音声ガイダンスのON/OFF
  int saveVoiceGuidanceOnOff(
    bool value,
  ) {
    return _saveVoiceGuidanceOnOff(
      value,
    );
  }

  late final _saveVoiceGuidanceOnOffPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'saveVoiceGuidanceOnOff');
  late final _saveVoiceGuidanceOnOff =
      _saveVoiceGuidanceOnOffPtr.asFunction<int Function(bool)>();

  int getVoiceGuidanceOnOff(
    ffi.Pointer<ffi.Bool> value,
  ) {
    return _getVoiceGuidanceOnOff(
      value,
    );
  }

  late final _getVoiceGuidanceOnOffPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          '_getVoiceGuidanceOnOff');
  late final _getVoiceGuidanceOnOff = _getVoiceGuidanceOnOffPtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  //SR Buzzer volume
  int saveSRVolume(
    int value,
  ) {
    return _saveSRVolume(
      value,
    );
  }

  late final _saveSRVolumePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>('saveSRVolume');
  late final _saveSRVolume = _saveSRVolumePtr.asFunction<int Function(int)>();

  int getSRVolume(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getSRVolume(
      value,
    );
  }

  late final _getSRVolumePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getSRVolume');
  late final _getSRVolume =
      _getSRVolumePtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  /// "３段階針上下針上下スイッチ押下時の動作を変更する。"
  int setNeedlePositionStitchPlacement(
    bool isOn,
  ) {
    return _setNeedlePositionStitchPlacement(
      isOn,
    );
  }

  late final _setNeedlePositionStitchPlacementPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setNeedlePositionStitchPlacement');
  late final _setNeedlePositionStitchPlacement =
      _setNeedlePositionStitchPlacementPtr.asFunction<int Function(bool)>();

  int getNeedlePositionStitchPlacement(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getNeedlePositionStitchPlacement(
      isOn,
    );
  }

  late final _getNeedlePositionStitchPlacementPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getNeedlePositionStitchPlacement');
  late final _getNeedlePositionStitchPlacement =
      _getNeedlePositionStitchPlacementPtr
          .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getNeedlePositionStitchPlacementDefault(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getNeedlePositionStitchPlacementDefault(
      isOn,
    );
  }

  late final _getNeedlePositionStitchPlacementDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getNeedlePositionStitchPlacementDefault');
  late final _getNeedlePositionStitchPlacementDefault =
      _getNeedlePositionStitchPlacementDefaultPtr
          .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// 起動時の表示画面を選択する。
  int setInitialScreen(
    int value,
  ) {
    return _setInitialScreen(
      value,
    );
  }

  late final _setInitialScreenPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setInitialScreen');
  late final _setInitialScreen =
      _setInitialScreenPtr.asFunction<int Function(int)>();

  int getInitialScreen(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getInitialScreen(
      value,
    );
  }

  late final _getInitialScreenPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getInitialScreen');
  late final _getInitialScreen =
      _getInitialScreenPtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getInitialScreenValueList(
    ffi.Pointer<UserSettingItemValueList> value,
  ) {
    return _getInitialScreenValueList(
      value,
    );
  }

  late final _getInitialScreenValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'getInitialScreenValueList');
  late final _getInitialScreenValueList = _getInitialScreenValueListPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// 起動時の表示画面を選択する。
  int saveInitialScreen(
    int value,
  ) {
    return _saveInitialScreen(
      value,
    );
  }

  late final _saveInitialScreenPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'saveInitialScreen');
  late final _saveInitialScreen =
      _saveInitialScreenPtr.asFunction<int Function(int)>();

  /// "スリープ機能 Eco Mode"
  int saveEcoMode(
    int value,
  ) {
    return _saveEcoMode(
      value,
    );
  }

  late final _saveEcoModePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>('saveEcoMode');
  late final _saveEcoMode = _saveEcoModePtr.asFunction<int Function(int)>();

  int getEcoMode(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getEcoMode(
      value,
    );
  }

  late final _getEcoModePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getEcoMode');
  late final _getEcoMode =
      _getEcoModePtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  /// Shutoff Support Modeに入る時間を設定する。
  int saveShutoffSupportMode(
    int value,
  ) {
    return _saveShutoffSupportMode(
      value,
    );
  }

  late final _saveShutoffSupportModePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'saveShutoffSupportMode');
  late final _saveShutoffSupportMode =
      _saveShutoffSupportModePtr.asFunction<int Function(int)>();

  int getShutoffSupportMode(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getShutoffSupportMode(
      value,
    );
  }

  late final _getShutoffSupportModePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getShutoffSupportMode');
  late final _getShutoffSupportMode = _getShutoffSupportModePtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int saveScreenSaverTime(int value) {
    return _saveScreenSaverTime(value);
  }

  late final _saveScreenSaverTimePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'saveScreenSaverTime');
  late final _saveScreenSaverTime =
      _saveScreenSaverTimePtr.asFunction<int Function(int)>();

  int getScreenSaverTime(ffi.Pointer<ffi.Int8> value) {
    return _getScreenSaverTime(value);
  }

  late final _getScreenSaverTimePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getScreenSaverTime');
  late final _getScreenSaverTime =
      _getScreenSaverTimePtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int saveSettingsScreenSaverType(int value) {
    return _saveSettingsScreenSaverType(value);
  }

  late final _saveSettingsScreenSaverTypePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'saveSettingsScreenSaverType');
  late final _saveSettingsScreenSaverType =
      _saveSettingsScreenSaverTypePtr.asFunction<int Function(int)>();

  int getSettingsScreenSaverType(ffi.Pointer<ffi.Int8> value) {
    return _getSettingsScreenSaverType(value);
  }

  late final _getSettingsScreenSaverTypePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getSettingsScreenSaverType');
  late final _getSettingsScreenSaverType = _getSettingsScreenSaverTypePtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  /// マウスポインター
  int saveMousePointer(
    int value,
  ) {
    return _saveMousePointer(
      value,
    );
  }

  late final _saveMousePointerPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'saveMousePointer');
  late final _saveMousePointer =
      _saveMousePointerPtr.asFunction<int Function(int)>();

  int getMousePointer(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getMousePointer(
      value,
    );
  }

  late final _getMousePointerPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getMousePointer');
  late final _getMousePointer =
      _getMousePointerPtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  /// プロジェクターの明るさ
  /// PANEL_API DeviceErrorCode_t setProjectorBrightness(int8_t value);
  int setProjectorBrightnessMinus() {
    return _setProjectorBrightnessMinus();
  }

  late final _setProjectorBrightnessMinusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'setProjectorBrightnessMinus');
  late final _setProjectorBrightnessMinus =
      _setProjectorBrightnessMinusPtr.asFunction<int Function()>();

  int setProjectorBrightnessPlus() {
    return _setProjectorBrightnessPlus();
  }

  late final _setProjectorBrightnessPlusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'setProjectorBrightnessPlus');
  late final _setProjectorBrightnessPlus =
      _setProjectorBrightnessPlusPtr.asFunction<int Function()>();

  int getProjectorBrightness(ffi.Pointer<ffi.Int8> value) {
    return _getProjectorBrightness(value);
  }

  late final _getProjectorBrightnessPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getProjectorBrightness');
  late final _getProjectorBrightness = _getProjectorBrightnessPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getProjectorBrightnessValueList(
    ffi.Pointer<UserSettingItemValue> value,
  ) {
    return _getProjectorBrightnessValueList(
      value,
    );
  }

  late final _getProjectorBrightnessValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValue>)>>(
      'getProjectorBrightnessValueList');
  late final _getProjectorBrightnessValueList =
      _getProjectorBrightnessValueListPtr
          .asFunction<int Function(ffi.Pointer<UserSettingItemValue>)>();

  /// プロジェクターの背景色
  int setProjectorBackbroundColor(
    int value,
  ) {
    return _setProjectorBackbroundColor(
      value,
    );
  }

  late final _setProjectorBackbroundColorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setProjectorBackbroundColor');
  late final _setProjectorBackbroundColor =
      _setProjectorBackbroundColorPtr.asFunction<int Function(int)>();

  int getProjectorBackbroundColor(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getProjectorBackbroundColor(
      value,
    );
  }

  late final _getProjectorBackbroundColorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getProjectorBackbroundColor');
  late final _getProjectorBackbroundColor = _getProjectorBackbroundColorPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getProjectorBackbroundColorValueList(
      ffi.Pointer<UserSettingItemValue> value) {
    return _getProjectorBackbroundColorValueList(value);
  }

  late final _getProjectorBackbroundColorValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValue>)>>(
      'getProjectorBackbroundColorValueList');
  late final _getProjectorBackbroundColorValueList =
      _getProjectorBackbroundColorValueListPtr
          .asFunction<int Function(ffi.Pointer<UserSettingItemValue>)>();

  /// プロジェクターのアウトライン
  int setProjectorPatternOutline(
    bool isOn,
  ) {
    return _setProjectorPatternOutline(
      isOn,
    );
  }

  late final _setProjectorPatternOutlinePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setProjectorPatternOutline');
  late final _setProjectorPatternOutline =
      _setProjectorPatternOutlinePtr.asFunction<int Function(bool)>();

  int getProjectorPatternOutline(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getProjectorPatternOutline(
      isOn,
    );
  }

  late final _getProjectorPatternOutlinePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getProjectorPatternOutline');
  late final _getProjectorPatternOutline = _getProjectorPatternOutlinePtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getProjectorPatternOutlineDefault(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getProjectorPatternOutlineDefault(
      isOn,
    );
  }

  late final _getProjectorPatternOutlineDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getProjectorPatternOutlineDefault');
  late final _getProjectorPatternOutlineDefault =
      _getProjectorPatternOutlineDefaultPtr
          .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// プロジェクターのポインタ色
  int setPointerColor(
    int value,
  ) {
    return _setPointerColor(
      value,
    );
  }

  late final _setPointerColorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setPointerColor');
  late final _setPointerColor =
      _setPointerColorPtr.asFunction<int Function(int)>();

  int getPointerColor(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getPointerColor(
      value,
    );
  }

  late final _getPointerColorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getPointerColor');
  late final _getPointerColor =
      _getPointerColorPtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getPointerColorValueList(ffi.Pointer<UserSettingItemValue> value) {
    return _getPointerColorValueList(value);
  }

  late final _getPointerColorValueListPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<UserSettingItemValue>)>>('getPointerColorValueList');
  late final _getPointerColorValueList = _getPointerColorValueListPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValue>)>();

  /// プロジェクターのポインタ形状
  int setPointerSharpe(
    int value,
  ) {
    return _setPointerSharpe(
      value,
    );
  }

  late final _setPointerSharpePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setPointerSharpe');
  late final _setPointerSharpe =
      _setPointerSharpePtr.asFunction<int Function(int)>();

  int getPointerSharpe(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getPointerSharpe(
      value,
    );
  }

  late final _getPointerSharpePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getPointerSharpe');
  late final _getPointerSharpe =
      _getPointerSharpePtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getPointerSharpeValueList(
    ffi.Pointer<UserSettingItemValue> value,
  ) {
    return _getPointerSharpeValueList(
      value,
    );
  }

  late final _getPointerSharpeValueListPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<UserSettingItemValue>)>>('getPointerSharpeValueList');
  late final _getPointerSharpeValueList = _getPointerSharpeValueListPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValue>)>();

  /// // カメラ針落ち点設定
  int startCalibrationAdjustment() {
    return _startCalibrationAdjustment();
  }

  late final _startCalibrationAdjustmentPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'startCalibrationAdjustment');
  late final _startCalibrationAdjustment =
      _startCalibrationAdjustmentPtr.asFunction<int Function()>();

  int cancelCalibrationAdjustment() {
    return _cancelCalibrationAdjustment();
  }

  late final _cancelCalibrationAdjustmentPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'cancelCalibrationAdjustment');
  late final _cancelCalibrationAdjustment =
      _cancelCalibrationAdjustmentPtr.asFunction<int Function()>();

  int doneCalibrationAdjustment() {
    return _doneCalibrationAdjustment();
  }

  late final _doneCalibrationAdjustmentPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'doneCalibrationAdjustment');
  late final _doneCalibrationAdjustment =
      _doneCalibrationAdjustmentPtr.asFunction<int Function()>();

  int doCalibrationAdjustment() {
    return _dotCalibrationAdjustment();
  }

  late final _dotCalibrationAdjustmentPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'doCalibrationAdjustment');
  late final _dotCalibrationAdjustment =
      _dotCalibrationAdjustmentPtr.asFunction<int Function()>();

  int retryCalibrationAdjustment() {
    return _retryCalibrationAdjustment();
  }

  late final _retryCalibrationAdjustmentPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'retryCalibrationAdjustment');
  late final _retryCalibrationAdjustment =
      _retryCalibrationAdjustmentPtr.asFunction<int Function()>();

  int getCameraNDPMessage() {
    return _getCameraNDPMessage();
  }

  late final _getCameraNDPMessagePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('getCameraNDPMessage');
  late final _getCameraNDPMessage =
      _getCameraNDPMessagePtr.asFunction<int Function()>();


  int startCalibrationProjector() {
    return _startCalibrationProjector();
  }

  late final _startCalibrationProjectorPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
      'startCalibrationProjector');
  late final _startCalibrationProjector =
  _startCalibrationProjectorPtr.asFunction<int Function()>();


  /// サービスカウント
  int getServiceCount(
    ffi.Pointer<ffi.Int64> value,
  ) {
    return _getServiceCount(
      value,
    );
  }

  late final _getServiceCountPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int64>)>>(
          'getServiceCount');
  late final _getServiceCount =
      _getServiceCountPtr.asFunction<int Function(ffi.Pointer<ffi.Int64>)>();

  /// トータルカウント
  int getTotalCount(
    ffi.Pointer<ffi.Int64> value,
  ) {
    return _getTotalCount(
      value,
    );
  }

  late final _getTotalCountPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int64>)>>(
          'getTotalCount');
  late final _getTotalCount =
      _getTotalCountPtr.asFunction<int Function(ffi.Pointer<ffi.Int64>)>();

  /// sncアクティベーションコードの設定
  /// PANEL_API DeviceErrorCode_t getSncState(); →　起動時に一度渡せばよいのでDeviceSettingInfoで取得する
  int setKitActivationCode(
    int kitID,
    int CertificKey,
  ) {
    return _setKitActivationCode(
      kitID,
      CertificKey,
    );
  }

  late final _setKitActivationCodePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32, ffi.Uint32)>>(
          'setKitActivationCode');
  late final _setKitActivationCode =
      _setKitActivationCodePtr.asFunction<int Function(int, int)>();
  //
  // /// マシーン名
  // int getMachineName(
  //     ffi.Pointer<ffi.Char> MachineName,
  //     ) {
  //   return _getMachineName(
  //     MachineName,
  //   );
  // }
  //
  // late final _getMachineNamePtr =
  // _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Char>)>>(
  //     'getMachineName');
  // late final _getMachineName =
  // _getMachineNamePtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();
  //
  // int setMachineName(
  //     ffi.Pointer<ffi.Char> MachineName,
  //     ) {
  //   return _setMachineName(
  //     MachineName,
  //   );
  // }
  //
  // late final _setMachineNamePtr =
  // _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Char>)>>(
  //     'setMachineName');
  // late final _setMachineName =
  // _setMachineNamePtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  /// ////////////////刺繍////////////////////////////////////////
  /// 刺繍縫製エリア表示設定
  int setEmbroideryFrameDisplay(
    int value,
  ) {
    return _setEmbroideryFrameDisplay(
      value,
    );
  }

  late final _setEmbroideryFrameDisplayPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'setEmbroideryFrameDisplay');
  late final _setEmbroideryFrameDisplay =
      _setEmbroideryFrameDisplayPtr.asFunction<int Function(int)>();

  int getEmbroideryFrameDisplay(
    ffi.Pointer<ffi.Int32> value,
  ) {
    return _getEmbroideryFrameDisplay(
      value,
    );
  }

  late final _getEmbroideryFrameDisplayPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int32>)>>(
          'getEmbroideryFrameDisplay');
  late final _getEmbroideryFrameDisplay = _getEmbroideryFrameDisplayPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int32>)>();

  int getEmbroideryFrameDisplayValueLis(
    ffi.Pointer<UserSettingItemValueList> value,
  ) {
    return _getEmbroideryFrameDisplayValueLis(
      value,
    );
  }

  late final _getEmbroideryFrameDisplayValueLisPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'getEmbroideryFrameDisplayValueLis');
  late final _getEmbroideryFrameDisplayValueLis =
      _getEmbroideryFrameDisplayValueLisPtr
          .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// グリッドの種類を選択する
  int saveGrid(
    int value,
  ) {
    return _saveGrid(
      value,
    );
  }

  late final _saveGridPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>('saveGrid');
  late final _saveGrid = _saveGridPtr.asFunction<int Function(int)>();

  int getGrid(
    ffi.Pointer<ffi.Int32> value,
  ) {
    return _getGrid(
      value,
    );
  }

  late final _getGridPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int32>)>>(
          '_getGrid');
  late final _getGrid =
      _getGridPtr.asFunction<int Function(ffi.Pointer<ffi.Int32>)>();

  /// 刺繍最高回転数設定
  int setMaxEmbroiderySpeed(
    int value,
  ) {
    return _setMaxEmbroiderySpeed(
      value,
    );
  }

  late final _setMaxEmbroiderySpeedPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setMaxEmbroiderySpeed');
  late final _setMaxEmbroiderySpeed =
      _setMaxEmbroiderySpeedPtr.asFunction<int Function(int)>();

  int getMaxEmbroiderySpeed(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getMaxEmbroiderySpeed(
      value,
    );
  }

  late final _getMaxEmbroiderySpeedPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getMaxEmbroiderySpeed');
  late final _getMaxEmbroiderySpeed = _getMaxEmbroiderySpeedPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  void UserSettingItemValueListFree(
    ffi.Pointer<UserSettingItemValueList> value,
  ) {
      _UserSettingItemValueListFree(
        value,
      );
  }

  late final _UserSettingItemValueListFreePtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'UserSettingItemValueListFree');
  late final _UserSettingItemValueListFree =
      _UserSettingItemValueListFreePtr
          .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  int getMaxEmbroiderySpeedValueList(
    ffi.Pointer<UserSettingItemValueList> value,
  ) {
    return _getMaxEmbroiderySpeedValueList(
      value,
    );
  }

  late final _getMaxEmbroiderySpeedValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'getMaxEmbroiderySpeedValueList');
  late final _getMaxEmbroiderySpeedValueList =
      _getMaxEmbroiderySpeedValueListPtr
          .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  int getMaxEmbroiderySpeedValueAllList(
    ffi.Pointer<UserSettingItemValueList> value,
  ) {
    return _getMaxEmbroiderySpeedValueAllList(
      value,
    );
  }

  late final _getMaxEmbroiderySpeedValueAllListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'getMaxEmbroiderySpeedValueAllList');
  late final _getMaxEmbroiderySpeedValueAllList =
      _getMaxEmbroiderySpeedValueAllListPtr
          .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// 刺繍糸調子設定
  int setEmbroideryTension(
    int value,
  ) {
    return _setEmbroideryTension(
      value,
    );
  }

  late final _setEmbroideryTensionPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setEmbroideryTension');
  late final _setEmbroideryTension =
      _setEmbroideryTensionPtr.asFunction<int Function(int)>();

  int getEmbroideryTension(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getEmbroideryTension(
      value,
    );
  }

  late final _getEmbroideryTensionPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getEmbroideryTension');
  late final _getEmbroideryTension = _getEmbroideryTensionPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getEmbroideryTensionValueList(
    ffi.Pointer<UserSettingItemValueList> value,
  ) {
    return _getEmbroideryTensionValueList(
      value,
    );
  }

  late final _getEmbroideryTensionValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'getEmbroideryTensionValueList');
  late final _getEmbroideryTensionValueList = _getEmbroideryTensionValueListPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// 刺繍の押えの高さを設定する
  int setEmbroideryFootHeight(
    int value,
  ) {
    return _setEmbroideryFootHeight(
      value,
    );
  }

  late final _setEmbroideryFootHeightPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setEmbroideryFootHeight');
  late final _setEmbroideryFootHeight =
      _setEmbroideryFootHeightPtr.asFunction<int Function(int)>();

  int getEmbroideryFootHeight(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getEmbroideryFootHeight(
      value,
    );
  }

  late final _getEmbroideryFootHeightPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getEmbroideryFootHeight');
  late final _getEmbroideryFootHeight = _getEmbroideryFootHeightPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getEmbroideryFootHeightValueList(
    ffi.Pointer<UserSettingItemValue> valueInfo,
  ) {
    return _getEmbroideryFootHeightValueList(
      valueInfo,
    );
  }

  late final _getEmbroideryFootHeightValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValue>)>>(
      'getEmbroideryFootHeightValueList');
  late final _getEmbroideryFootHeightValueList =
      _getEmbroideryFootHeightValueListPtr
          .asFunction<int Function(ffi.Pointer<UserSettingItemValue>)>();

  /// 刺繍時針停止位置（上下）
  int setEmbroideryNeedleStopPosition(
    bool isUP,
  ) {
    return _setEmbroideryNeedleStopPosition(
      isUP,
    );
  }

  late final _setEmbroideryNeedleStopPositionPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setEmbroideryNeedleStopPosition');
  late final _setEmbroideryNeedleStopPosition =
      _setEmbroideryNeedleStopPositionPtr.asFunction<int Function(bool)>();

  int getEmbroideryNeedleStopPosition(
    ffi.Pointer<ffi.Bool> isUP,
  ) {
    return _getEmbroideryNeedleStopPosition(
      isUP,
    );
  }

  late final _getEmbroideryNeedleStopPositionPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getEmbroideryNeedleStopPosition');
  late final _getEmbroideryNeedleStopPosition =
      _getEmbroideryNeedleStopPositionPtr
          .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getEmbroideryNeedleStopPositionDefault(
    ffi.Pointer<ffi.Bool> isUP,
  ) {
    return _getEmbroideryNeedleStopPositionDefault(
      isUP,
    );
  }

  late final _getEmbroideryNeedleStopPositionDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getEmbroideryNeedleStopPositionDefault');
  late final _getEmbroideryNeedleStopPositionDefault =
      _getEmbroideryNeedleStopPositionDefaultPtr
          .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// 自動押さえ下げ
  int setEmbAutoDown(
    bool isOn,
  ) {
    return _setEmbAutoDown(
      isOn,
    );
  }

  late final _setEmbAutoDownPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setEmbAutoDown');
  late final _setEmbAutoDown =
      _setEmbAutoDownPtr.asFunction<int Function(bool)>();

  int getEmbAutoDown(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getEmbAutoDown(
      isOn,
    );
  }

  late final _getEmbAutoDownPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getEmbAutoDown');
  late final _getEmbAutoDown =
      _getEmbAutoDownPtr.asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getEmbAutoDownDefault(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getEmbAutoDownDefault(
      isOn,
    );
  }

  late final _getEmbAutoDownDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getEmbAutoDownDefault');
  late final _getEmbAutoDownDefault = _getEmbAutoDownDefaultPtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// インチ/ミリ切り替え
  int saveDisplayUnit(
    bool isMM,
  ) {
    return _saveDisplayUnit(
      isMM,
    );
  }

  late final _saveDisplayUnitPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'saveDisplayUnit');
  late final _saveDisplayUnit =
      _saveDisplayUnitPtr.asFunction<int Function(bool)>();

  int getDisplayUnit(ffi.Pointer<ffi.Int8> isMM) {
    return _getDisplayUnit(isMM);
  }

  late final _getDisplayUnitPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getDisplayUnit');
  late final _getDisplayUnit =
      _getDisplayUnitPtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  /// 糸番号表示か、糸色名表示か選択する
  int saveThreadColor(
    bool isThreadCode,
  ) {
    return _saveThreadColor(
      isThreadCode,
    );
  }

  late final _saveThreadColorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'saveThreadColor');
  late final _saveThreadColor =
      _saveThreadColorPtr.asFunction<int Function(bool)>();

  int getThreadColor(
    ffi.Pointer<ffi.Int8> isThreadCode,
  ) {
    return _getThreadColor(
      isThreadCode,
    );
  }

  late final _getThreadColorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getThreadColor');
  late final _getThreadColor =
      _getThreadColorPtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  /// 糸ブランド
  int setThreadBrand(
    int value,
  ) {
    return _setThreadBrand(
      value,
    );
  }

  late final _setThreadBrandPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setThreadBrand');
  late final _setThreadBrand =
      _setThreadBrandPtr.asFunction<int Function(int)>();

  int getThreadBrand(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getThreadBrand(
      value,
    );
  }

  late final _getThreadBrandPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getThreadBrand');
  late final _getThreadBrand =
      _getThreadBrandPtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getThreadBrandValueList(
    ffi.Pointer<UserSettingItemValueList> value,
  ) {
    return _getThreadBrandValueList(
      value,
    );
  }

  late final _getThreadBrandValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValueList>)>>(
      'getThreadBrandValueList');
  late final _getThreadBrandValueList = _getThreadBrandValueListPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// 刺繍イメージ表示エリア　背景色変更
  int saveEmbroideryBackgroundColor(
    int value,
  ) {
    return _saveEmbroideryBackgroundColor(
      value,
    );
  }

  late final _saveEmbroideryBackgroundColorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'saveEmbroideryBackgroundColor');
  late final _saveEmbroideryBackgroundColor =
      _saveEmbroideryBackgroundColorPtr.asFunction<int Function(int)>();

  int getEmbroideryBackgroundColor(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getEmbroideryBackgroundColor(
      value,
    );
  }

  late final _getEmbroideryBackgroundColorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getEmbroideryBackgroundColor');
  late final _getEmbroideryBackgroundColor = _getEmbroideryBackgroundColorPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  /// サムネイル背景色変更
  int saveThumbnailBackgroundColor(
    int value,
  ) {
    return _saveThumbnailBackgroundColor(
      value,
    );
  }

  late final _saveThumbnailBackgroundColorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'saveThumbnailBackgroundColor');
  late final _saveThumbnailBackgroundColor =
      _saveThumbnailBackgroundColorPtr.asFunction<int Function(int)>();

  int getThumbnailBackgroundColor(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getThumbnailBackgroundColor(
      value,
    );
  }

  late final _getThumbnailBackgroundColorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getThumbnailBackgroundColor');
  late final _getThumbnailBackgroundColor = _getThumbnailBackgroundColorPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  /// サムネイルサイズ設定
  int saveThumbnailSize(
    int value,
  ) {
    return _saveThumbnailSize(
      value,
    );
  }

  late final _saveThumbnailSizePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'saveThumbnailSize');
  late final _saveThumbnailSize =
      _saveThumbnailSizePtr.asFunction<int Function(int)>();

  int getThumbnailSize(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getThumbnailSize(
      value,
    );
  }

  late final _getThumbnailSizePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          '_getThumbnailSize');
  late final _getThumbnailSize =
      _getThumbnailSizePtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  /// サムネイルサイズ設定
  int setThumbnailSize(
    int value,
  ) {
    return _setThumbnailSize(
      value,
    );
  }

  late final _setThumbnailSizePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setThumbnailSize');
  late final _setThumbnailSize =
      _setThumbnailSizePtr.asFunction<int Function(int)>();

  int getThumbnailSizeLis(
    ffi.Pointer<UserSettingItemValueList> value,
  ) {
    return _getThumbnailSizeLis(
      value,
    );
  }

  late final _getThumbnailSizeLisPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<UserSettingItemValueList>)>>('getThumbnailSizeLis');
  late final _getThumbnailSizeLis = _getThumbnailSizeLisPtr
      .asFunction<int Function(ffi.Pointer<UserSettingItemValueList>)>();

  /// 刺繍しつけの大きさを模様に対して調整する
  int setEmbroideryBastingDistance(
    int value,
  ) {
    return _setEmbroideryBastingDistance(
      value,
    );
  }

  late final _setEmbroideryBastingDistancePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'setEmbroideryBastingDistance');
  late final _setEmbroideryBastingDistance =
      _setEmbroideryBastingDistancePtr.asFunction<int Function(int)>();

  int getEmbroideryBastingDistance(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getEmbroideryBastingDistance(
      value,
    );
  }

  late final _getEmbroideryBastingDistancePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getEmbroideryBastingDistance');
  late final _getEmbroideryBastingDistance = _getEmbroideryBastingDistancePtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getEmbroideryBastingDistanceValueList(
    ffi.Pointer<UserSettingItemValue> value,
  ) {
    return _getEmbroideryBastingDistanceValueList(
      value,
    );
  }

  late final _getEmbroideryBastingDistanceValueListPtr = _lookup<
          ffi.NativeFunction<
              ffi.Int32 Function(ffi.Pointer<UserSettingItemValue>)>>(
      'getEmbroideryBastingDistanceValueList');
  late final _getEmbroideryBastingDistanceValueList =
      _getEmbroideryBastingDistanceValueListPtr
          .asFunction<int Function(ffi.Pointer<UserSettingItemValue>)>();

  /// 背景画像表示スキャン品質
  int setScanQuality(
    bool isQuality,
  ) {
    return _setScanQuality(
      isQuality,
    );
  }

  late final _setScanQualityPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setScanQuality');
  late final _setScanQuality =
      _setScanQualityPtr.asFunction<int Function(bool)>();

  int getScanQuality(
    ffi.Pointer<ffi.Int8> isQuality,
  ) {
    return _getScanQuality(
      isQuality,
    );
  }

  late final _getScanQualityPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getScanQuality');
  late final _getScanQuality =
      _getScanQualityPtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getScanQualityDefault(
    ffi.Pointer<ffi.Bool> isQuality,
  ) {
    return _getScanQualityDefault(
      isQuality,
    );
  }

  late final _getScanQualityDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getScanQualityDefault');
  late final _getScanQualityDefault = _getScanQualityDefaultPtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// 布厚センサー
  int setFabricThicknessSensor(
    bool value,
  ) {
    return _setFabricThicknessSensor(
      value,
    );
  }

  late final _setFabricThicknessSensorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setFabricThicknessSensor');
  late final _setFabricThicknessSensor =
      _setFabricThicknessSensorPtr.asFunction<int Function(bool)>();

  int getFabricThicknessSensor(
    ffi.Pointer<ffi.Bool> value,
  ) {
    return _getFabricThicknessSensor(
      value,
    );
  }

  late final _getFabricThicknessSensorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getFabricThicknessSensor');
  late final _getFabricThicknessSensor = _getFabricThicknessSensorPtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int getFabricThicknessSensorDefault(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getFabricThicknessSensorDefault(
      isOn,
    );
  }

  late final _getFabricThicknessSensorDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getFabricThicknessSensorDefault');
  late final _getFabricThicknessSensorDefault =
      _getFabricThicknessSensorDefaultPtr
          .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// LEDポインター調整
  int startLedPtSetting() {
    return _startLedPtSetting();
  }

  late final _startLedPtSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('startLedPtSetting');
  late final _startLedPtSetting =
      _startLedPtSettingPtr.asFunction<int Function()>();

  int closeLedPtSetting() {
    return _closeLedPtSetting();
  }

  late final _closeLedPtSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('closeLedPtSetting');
  late final _closeLedPtSetting =
      _closeLedPtSettingPtr.asFunction<int Function()>();

  int setLedPtHeightMinus() {
    return _setLedPtHeightMinus();
  }

  late final _setLedPtHeightMinusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setLedPtHeightMinus');
  late final _setLedPtHeightMinus =
      _setLedPtHeightMinusPtr.asFunction<int Function()>();

  int setLedPtHeightPlus() {
    return _setLedPtHeightPlus();
  }

  late final _setLedPtHeightPlusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setLedPtHeightPlus');
  late final _setLedPtHeightPlus =
      _setLedPtHeightPlusPtr.asFunction<int Function()>();

  int getLedPtHeightPlus(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getLedPtHeightPlus(
      value,
    );
  }

  late final _getLedPtHeightPlusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getLedPtHeightPlus');
  late final _getLedPtHeightPlus =
      _getLedPtHeightPlusPtr.asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getLedPtHeightPlusDefault(
    ffi.Pointer<ffi.Int8> isOn,
  ) {
    return _getLedPtHeightPlusDefault(
      isOn,
    );
  }

  late final _getLedPtHeightPlusDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getLedPtHeightPlusDefault');
  late final _getLedPtHeightPlusDefault = _getLedPtHeightPlusDefaultPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int setLedBrightnessMinus() {
    return _setLedBrightnessMinus();
  }

  late final _setLedBrightnessMinusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'setLedBrightnessMinus');
  late final _setLedBrightnessMinus =
      _setLedBrightnessMinusPtr.asFunction<int Function()>();

  int setLedPtBrightnessPlus() {
    return _setLedPtBrightnessPlus();
  }

  late final _setLedPtBrightnessPlusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'setLedPtBrightnessPlus');
  late final _setLedPtBrightnessPlus =
      _setLedPtBrightnessPlusPtr.asFunction<int Function()>();

  int getLedPtBrightnessPlus(
    ffi.Pointer<ffi.Int8> value,
  ) {
    return _getLedPtBrightnessPlus(
      value,
    );
  }

  late final _getLedPtBrightnessPlusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getLedPtBrightnessPlus');
  late final _getLedPtBrightnessPlus = _getLedPtBrightnessPlusPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int getLedPtBrightnessPlusDefault(
    ffi.Pointer<ffi.Int8> isOn,
  ) {
    return _getLedPtBrightnessPlusDefault(
      isOn,
    );
  }

  late final _getLedPtBrightnessPlusDefaultPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getLedPtBrightnessPlusDefault');
  late final _getLedPtBrightnessPlusDefault = _getLedPtBrightnessPlusDefaultPtr
      .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  /// 背景画像の削除、登録、登録状況
  int deleteCompFrameScanBackImgView() {
    return _deleteCompFrameScanBackImgView();
  }

  late final _deleteCompFrameScanBackImgViewPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'deleteCompFrameScanBackImgView');
  late final _deleteCompFrameScanBackImgView =
      _deleteCompFrameScanBackImgViewPtr.asFunction<int Function()>();

  int getCompFrameScanBackImgView(
    ffi.Pointer<ffi.Bool> isOn,
  ) {
    return _getCompFrameScanBackImgView(
      isOn,
    );
  }

  late final _getCompFrameScanBackImgViewPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getCompFrameScanBackImgView');
  late final _getCompFrameScanBackImgView = _getCompFrameScanBackImgViewPtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// //////////////////////////////////////////////////////
  /// 起動時に取得するデバイス情報
  int getDeviceSettingInfo(
    ffi.Pointer<DeviceSettingInfo> value,
  ) {
    return _getDeviceSettingInfo(
      value,
    );
  }

  late final _getDeviceSettingInfoPtr = _lookup<
          ffi
          .NativeFunction<ffi.Int32 Function(ffi.Pointer<DeviceSettingInfo>)>>(
      'getDeviceSettingInfo');
  late final _getDeviceSettingInfo = _getDeviceSettingInfoPtr
      .asFunction<int Function(ffi.Pointer<DeviceSettingInfo>)>();

  int getCommonUserSettingInfo(
    ffi.Pointer<CommonUserSettingInfo> value,
  ) {
    return _getCommonUserSettingInfo(
      value,
    );
  }

  late final _getCommonUserSettingInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<CommonUserSettingInfo>)>>('getCommonUserSettingInfo');
  late final _getCommonUserSettingInfo = _getCommonUserSettingInfoPtr
      .asFunction<int Function(ffi.Pointer<CommonUserSettingInfo>)>();

  int getEmbUserSettingInfo(
    ffi.Pointer<EmbUserSettingInfo> value,
  ) {
    return _getEmbUserSettingInfo(
      value,
    );
  }

  late final _getEmbUserSettingInfoPtr = _lookup<
          ffi
          .NativeFunction<ffi.Int32 Function(ffi.Pointer<EmbUserSettingInfo>)>>(
      'getEmbUserSettingInfo');
  late final _getEmbUserSettingInfo = _getEmbUserSettingInfoPtr
      .asFunction<int Function(ffi.Pointer<EmbUserSettingInfo>)>();

  int getUtlUserSettingInfo(
    ffi.Pointer<UtlUserSettingInfo> value,
  ) {
    return _getUtlUserSettingInfo(
      value,
    );
  }

  late final _getUtlUserSettingInfoPtr = _lookup<
          ffi
          .NativeFunction<ffi.Int32 Function(ffi.Pointer<UtlUserSettingInfo>)>>(
      'getUtlUserSettingInfo');
  late final _getUtlUserSettingInfo = _getUtlUserSettingInfoPtr
      .asFunction<int Function(ffi.Pointer<UtlUserSettingInfo>)>();

  /// 設定値を復元する必要があるか？
  bool checkRecoveryRequirement() {
    return _checkRecoveryRequirement();
  }

  late final _checkRecoveryRequirementPtr =
      _lookup<ffi.NativeFunction<ffi.Bool Function()>>(
          'checkRecoveryRequirement');
  late final _checkRecoveryRequirement =
      _checkRecoveryRequirementPtr.asFunction<bool Function()>();

  bool clearRecoveryRequirement() {
    return _clearRecoveryRequirement();
  }

  late final _clearRecoveryRequirementPtr =
      _lookup<ffi.NativeFunction<ffi.Bool Function()>>(
          'clearRecoveryRequirement');
  late final _clearRecoveryRequirement =
      _clearRecoveryRequirementPtr.asFunction<bool Function()>();

  void resetUtlUserSetting() {
    return _resetUtlUserSetting();
  }

  late final _resetUtlUserSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('resetUtlUserSetting');
  late final _resetUtlUserSetting =
      _resetUtlUserSettingPtr.asFunction<void Function()>();

  void resetCommonUserSetting() {
    return _resetCommonUserSetting();
  }

  late final _resetCommonUserSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>(
          'resetCommonUserSetting');
  late final _resetCommonUserSetting =
      _resetCommonUserSettingPtr.asFunction<void Function()>();

  void resetEmbUserSetting() {
    return _resetEmbUserSetting();
  }

  late final _resetEmbUserSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('resetEmbUserSetting');
  late final _resetEmbUserSetting =
      _resetEmbUserSettingPtr.asFunction<void Function()>();



  int startAllUserSetting() {
    return _startAllUserSetting();
  }

  late final _startAllUserSettingPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('startAllUserSetting');
  late final _startAllUserSetting = _startAllUserSettingPtr.asFunction<int Function()>();

  int finishAllUserSetting() {
    return _finishAllUserSetting();
  }

  late final _finishAllUserSettingPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('finishAllUserSetting');
  late final _finishAllUserSetting = _finishAllUserSettingPtr.asFunction<int Function()>();

  void resetAllUserSetting() {
    return _resetAllUserSetting();
  }

  late final _resetAllUserSettingPtr =
  _lookup<ffi.NativeFunction<ffi.Void Function()>>('resetAllUserSetting');
  late final _resetAllUserSetting =
  _resetAllUserSettingPtr.asFunction<void Function()>();


  /// キーの有効無効状態を取得する
  void getUserSettingEnableInfo(
    ffi.Pointer<UserSettingEnabledInfo> info,
  ) {
    return _getUserSettingEnableInfo(
      info,
    );
  }

  late final _getUserSettingEnableInfoPtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(ffi.Pointer<UserSettingEnabledInfo>)>>(
      'getUserSettingEnableInfo');
  late final _getUserSettingEnableInfo = _getUserSettingEnableInfoPtr
      .asFunction<void Function(ffi.Pointer<UserSettingEnabledInfo>)>();

  int getEmbCameraUIProjectionSetting(
      ffi.Pointer<ffi.Int8> camera_ui_projection) {
    return _getEmbCameraUIProjectionSetting(camera_ui_projection);
  }

  late final _getEmbCameraUIProjectionSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int8>)>>(
          'getEmbCameraUIProjectionSetting');
  late final _getEmbCameraUIProjectionSetting =
      _getEmbCameraUIProjectionSettingPtr
          .asFunction<int Function(ffi.Pointer<ffi.Int8>)>();

  int saveEmbCameraUIProjectionSetting(int camera_ui_projection) {
    return _saveEmbCameraUIProjectionSetting(camera_ui_projection);
  }

  late final _saveEmbCameraUIProjectionSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int8)>>(
          'saveEmbCameraUIProjectionSetting');
  late final _saveEmbCameraUIProjectionSetting =
      _saveEmbCameraUIProjectionSettingPtr.asFunction<int Function(int)>();

  /// 投影UIの配置状態をの設定値を取得する
  int getCameraPenUIArrangeState(
    ffi.Pointer<ffi.Int> camera_ui_arrange_state,
  ) {
    return _getCameraPenUIArrangeState(
      camera_ui_arrange_state,
    );
  }

  late final _getCameraPenUIArrangeStatePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Int>)>>(
          'getCameraPenUIArrangeState');
  late final _getCameraPenUIArrangeState = _getCameraPenUIArrangeStatePtr
      .asFunction<int Function(ffi.Pointer<ffi.Int>)>();

  /// 投影UIの配置状態をの設定値を保存する
  int saveEmbCameraUIArrangeStateSetting(
    int camera_ui_arrange_state,
  ) {
    return _saveEmbCameraUIArrangeStateSetting(
      camera_ui_arrange_state,
    );
  }

  late final _saveEmbCameraUIArrangeStateSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int)>>(
          'saveEmbCameraUIArrangeStateSetting');
  late final _saveEmbCameraUIArrangeStateSetting =
      _saveEmbCameraUIArrangeStateSettingPtr.asFunction<int Function(int)>();

  int getSetupLangFlag(
    ffi.Pointer<ffi.Bool> isSet,
  ) {
    return _getSetupLangFlag(
      isSet,
    );
  }

  late final _getSetupLangFlagPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
          'getSetupLangFlag');
  late final _getSetupLangFlag =
      _getSetupLangFlagPtr.asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  int setSetupLangFlag() {
    return _setSetupLangFlag();
  }

  late final _setSetupLangFlagPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setSetupLangFlag');
  late final _setSetupLangFlag =
      _setSetupLangFlagPtr.asFunction<int Function()>();

  int resetSetupLang() {
    return _resetSetupLang();
  }

  late final _resetSetupLangPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('resetSetupLang');
  late final _resetSetupLang = _resetSetupLangPtr.asFunction<int Function()>();


  ///起動時のVoiceガイダンス設定フラグが設定済か
  ///isSet=true:設定済
  int getSetupVoiceGuideFlag(
      ffi.Pointer<ffi.Bool> isSet,
      ) {
    return _getSetupVoiceGuideFlag(
      isSet,
    );
  }

  late final _getSetupVoiceGuideFlagPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Bool>)>>(
      'getSetupVoiceGuideFlag');
  late final _getSetupVoiceGuideFlag = _getSetupVoiceGuideFlagPtr
      .asFunction<int Function(ffi.Pointer<ffi.Bool>)>();

  /// 設定済
  int setSetupVoiceGuideFlag() {
    return _setSetupVoiceGuideFlag();
  }

  late final _setSetupVoiceGuideFlagPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
      'setSetupVoiceGuideFlag');
  late final _setSetupVoiceGuideFlag =
  _setSetupVoiceGuideFlagPtr.asFunction<int Function()>();

  /// 未設定
  int resetSetupVoiceGuideFlag() {
    return _resetSetupVoiceGuideFlag();
  }

  late final _resetSetupVoiceGuideFlagPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
      'resetSetupVoiceGuideFlag');
  late final _resetSetupVoiceGuideFlag =
  _resetSetupVoiceGuideFlagPtr.asFunction<int Function()>();

  int getUserLogProductInfo(
      ffi.Pointer<UserLogProductInfo_t> oData,
      ) {
    return _getUserLogProductInfo(
      oData,
    );
  }

  late final _getUserLogProductInfoPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Pointer<UserLogProductInfo_t>)>>('getUserLogProductInfo');
  late final _getUserLogProductInfo = _getUserLogProductInfoPtr
      .asFunction<int Function(ffi.Pointer<UserLogProductInfo_t>)>();


  /// EEPのRead/Wite関数　マルチスレッドで同時に呼び出すと正常動かない
  /// EEP PageWrite関数は非公開関数のまま(動作確認が不十分なため)
  /// BpifEepWrite(uint16_t adr, uint16_t size, uint16_t data, Eeprom::BOARD_TYPE type)
  int BpifEepWrite(
      int adr,
      int size,
      int data,
      int arg3,
      ) {
    return _BpifEepWrite(
      adr,
      size,
      data,
      arg3,
    );
  }

  late final _BpifEepWritePtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint16, ffi.Uint16, ffi.Uint16, ffi.Int)>>('BpifEepWrite');
  late final _BpifEepWrite =
  _BpifEepWritePtr.asFunction<int Function(int, int, int, int)>();

  /// BpifEepRead(uint16_t adr, uint16_t size, uint16_t* buf)
  int BpifEepRead(
      int adr,
      int size,
      ffi.Pointer<ffi.Uint16> buf,
      ) {
    return _BpifEepRead(
      adr,
      size,
      buf,
    );
  }

  late final _BpifEepReadPtr = _lookup<
      ffi.NativeFunction<
          ffi.Int32 Function(
              ffi.Uint16, ffi.Uint16, ffi.Pointer<ffi.Uint16>)>>('BpifEepRead');
  late final _BpifEepRead = _BpifEepReadPtr.asFunction<
      int Function(int, int, ffi.Pointer<ffi.Uint16>)>();

}

final class UserSettingItemValue extends ffi.Struct {
  @ffi.Int32()
  external int maxValue;

  @ffi.Int32()
  external int minValue;

  @ffi.Int32()
  external int defaultValue;

  @ffi.Int32()
  external int stepValue;

  static ffi.Pointer<UserSettingItemValue> allocate() {
    final pointer = ffi.calloc<UserSettingItemValue>();
    pointer.ref.maxValue = 0;
    pointer.ref.minValue = 0;
    pointer.ref.defaultValue = 0;
    pointer.ref.stepValue = 0;
    return pointer;
  }
}

final class UserSettingItemValueList extends ffi.Struct {
  @ffi.Int32()
  external int defaultValue;

  @ffi.Int32()
  external int listSize;

  external ffi.Pointer<DisplayValue> valueLiset;

  static ffi.Pointer<UserSettingItemValueList> allocate() {
    final pointer = ffi.calloc<UserSettingItemValueList>();
    pointer.ref.defaultValue = 0;
    pointer.ref.listSize = 0;
    pointer.ref.valueLiset = DisplayValue.allocate();
    return pointer;
  }
}

final class DisplayValue extends ffi.Struct {
  external ffi.Pointer<ffi.Char> display;

  @ffi.Int32()
  external int value;

  static ffi.Pointer<DisplayValue> allocate() {
    final pointer = ffi.calloc<DisplayValue>();
    pointer.ref.display = ffi.calloc<ffi.Char>();
    pointer.ref.value = 0;
    return pointer;
  }
}

abstract class ModeSpec_t {
  /// ディズニーUS
  static const int SPEC_DISNEY_US = 0;

  /// ノンキャラUS
  static const int SPEC_EXPORT_US = 1;

  /// ノンキャラ欧州
  static const int SPEC_EXPORT_EU = 2;

  /// ディズニーCANADA
  static const int SPEC_DISNEY_CANADA = 3;

  /// タコニー
  static const int SPEC_EXPORT_TACONY = 4;

  /// 欧州ディズニー
  static const int SPEC_DISNEY_EU = 10;

  /// ディズニーCHINA
  static const int SPEC_DISNEY_CHN = 12;

  /// エラー（必ず最後）
  static const int SPEC_ERROR = 14;
}

/// カメラ針落ち点設定１
enum CameraNDPEMessage_t {
  /// メッセージなし
  NO_CNDP_EM,

  /// 針上 　メッセージ(XP)：STR_40CSP_Z_Setting_CameraCalibration_Result
  NUP_CNDP_EM,

  /// シールが汚い 　メッセージ(XP)：STR_40CSP_Z_Setting_CameraCalibration_Result
  DIRTY_CNDP_EM,

  /// 処理中 　メッセージ(XP)：T_ERR448
  PROCESSING_CNDP_EM,

  /// メッセージ(XP)：T_ERR_CameraCalib_OK_MSG
  OK_CNDP_EM,

  /// メッセージ(XP)：STR_40CSP_Z_Setting_CameraCalibration_Result
  NG_CNDP_EM,

  /// プロジェクタキャリブレーション開始待ち
  WAITING_PRO_START,
}

final class DeviceSettingInfo extends ffi.Struct {
  /// サービスカウント
  @ffi.Int64()
  external int serviceCount;

  /// トータルカウント
  @ffi.Int64()
  external int totalCount;

  @ffi.Array.multi([10])
  external ffi.Array<ffi.UnsignedChar> productID;

  @ffi.Array.multi([10])
  external ffi.Array<ffi.Char> serialNumber;

  /// serial number置き換えの有無 : 0=なし, 1=あり(おかしなコードがあり'?'に置き換えた)
  @ffi.Bool()
  external bool isReplacedSerialNumber;

  /// パネル プログラム Ver.
  @ffi.Int32()
  external int panelMajorVer;

  /// パネル プログラム Ver.
  @ffi.Int32()
  external int panelMinorVer;

  /// パネル プログラム Ver.
  @ffi.Int32()
  external int panelPatchVer;

  /// 仕向け
  @ffi.Uint8()
  external int Spec;

  @ffi.Array.multi([10])
  external ffi.Array<ffi.Bool> isKit;

  /// Limited：0x80,ノーマル：その他
  @ffi.Uint8()
  external int ModelNo;

  /// ライブラリ プログラム Ver.
  @ffi.Int32()
  external int libPanelMajorVer;

  /// ライブラリ プログラム Ver.
  @ffi.Int32()
  external int libPanelMinorVer;

  /// ライブラリ プログラム Ver.
  @ffi.Int32()
  external int libPanelPatchVer;

  /// 機種コード
  @ffi.Array.multi([11])
  external ffi.Array<ffi.UnsignedChar> modeCode;

  /// 日本仕向けか
  @ffi.Bool()
  external bool isProductsJapan;

  /// Array<Uint8> productId(10桁) to String
  /// e.g. [4,7,9,1,7,1,0,6,3,1] to "4791710631"
  String productIdToString() {
    final stringList = <int>[];
    var i = 0;
    for (i = 0; i < 10; i++) {
      stringList.add(this.productID[i] + 0x30);
    }
    return String.fromCharCodes(stringList);
  }

  static ffi.Pointer<DeviceSettingInfo> allocate() {
    final pointer = ffi.calloc<DeviceSettingInfo>();
    return pointer;
  }
}

final class CommonUserSettingInfo extends ffi.Struct {
  @ffi.Int8()
  external int language;

  @ffi.Int8()
  external int light;

  @ffi.Int8()
  external int screenDisplayBrightness;

  @ffi.Bool()
  external bool isUpperAndBobbinThreadSensor;

  @ffi.Int8()
  external int machineSpeakerVolume;

  @ffi.Int8()
  external int voiceVolume;

  @ffi.Bool()
  external bool isVoiceOnOff;

  @ffi.Int8()
  external int SRVolume;

  @ffi.Bool()
  external bool isNeedlePositionStitchPlacement;

  @ffi.Int8()
  external int initialScreen;

  @ffi.Int8()
  external int ecoMode;

  @ffi.Int8()
  external int shutoffSupportMode;

  @ffi.Int8()
  external int screenSaverTime;

  @ffi.Int8()
  external int screenSaverType;

  @ffi.Int8()
  external int mousePointer;

  @ffi.Int8()
  external int projectorBrightness;

  @ffi.Int8()
  external int projectorBackbroundColor;

  @ffi.Bool()
  external bool isProjectorPatternOutline;

  @ffi.Int8()
  external int pointerColor;

  @ffi.Int8()
  external int pointerSharpe;

  static ffi.Pointer<CommonUserSettingInfo> allocate() {
    final pointer = ffi.calloc<CommonUserSettingInfo>();
    pointer.ref.language = 0;
    pointer.ref.light = 0;
    pointer.ref.screenDisplayBrightness = 0;
    pointer.ref.isUpperAndBobbinThreadSensor = true;
    pointer.ref.machineSpeakerVolume = 0;
    pointer.ref.voiceVolume = 0;
    pointer.ref.isVoiceOnOff = false;
    pointer.ref.SRVolume = 0;

    pointer.ref.isNeedlePositionStitchPlacement = true;
    pointer.ref.initialScreen = 0;
    pointer.ref.ecoMode = 0;
    pointer.ref.shutoffSupportMode = 0;
    pointer.ref.screenSaverTime = 0;
    pointer.ref.screenSaverType = 0;
    pointer.ref.mousePointer = 0;
    pointer.ref.projectorBrightness = 0;
    pointer.ref.projectorBackbroundColor = 0;
    pointer.ref.isProjectorPatternOutline = true;
    pointer.ref.pointerColor = 0;
    pointer.ref.pointerSharpe = 0;
    return pointer;
  }
}

final class EmbUserSettingInfo extends ffi.Struct {
  @ffi.Int32()
  external int embroideryFrameDisplay;

  @ffi.Int32()
  external int grid;

  @ffi.Int8()
  external int maxEmbroiderySpeed;

  @ffi.Int8()
  external int embroideryTension;

  @ffi.Int8()
  external int embroideryFootHeight;

  @ffi.Bool()
  external bool embroideryNeedleStopPosition;

  @ffi.Bool()
  external bool isAutoDown;

  @ffi.Int8()
  external int displayUnit;

  @ffi.Int8()
  external int threadColor;

  @ffi.Int8()
  external int threadBrand;

  @ffi.Int8()
  external int embroideryBackgroundColor;

  @ffi.Int8()
  external int thumbnailBackgroundColor;

  @ffi.Int8()
  external int thumbnailSize;

  @ffi.Int8()
  external int embroideryBastingDistance;

  @ffi.Int8()
  external int scanQuality;

  @ffi.Bool()
  external bool fabricThicknessSensor;

  @ffi.Bool()
  external bool isScanBackImgView;

  static ffi.Pointer<EmbUserSettingInfo> allocate() {
    final pointer = ffi.calloc<EmbUserSettingInfo>();

    return pointer;
  }
}

final class UtlUserSettingInfo extends ffi.Struct {
  /// スピードコントロールレバーを使用してWIDTH調節を行う。
  @ffi.Bool()
  external bool isWidthControl;

  /// 送り調整（模様調整－縦方向）
  @ffi.Int16()
  external int fineAdjustHorizValue;

  /// 押え高さ調整
  @ffi.Int16()
  external int presserFootHeightValue;

  /// 押えの圧力を調節する
  @ffi.Int16()
  external int presserFootPressureValue;

  /// センサーシステム（自動押え圧補整）
  @ffi.Bool()
  external bool isAutomaticFabricSensorSystem;

  /// 初期針位置（基線切り替え）
  @ffi.Bool()
  external bool isBaseLine;

  /// ピボット押え高さ
  @ffi.Int16()
  external int pivotingHeightValue;

  /// フリーモーション押え高さ
  @ffi.Int8()
  external int freeMotionFootHeighValue;

  /// デュアルフィード送り調整
  @ffi.Int8()
  external int dualFeedFeedAdjustmentValue;

  /// 自動下げの値を設定する
  @ffi.Bool()
  external bool isAutoDown;

  /// 自動押え上下
  @ffi.Bool()
  external bool isPressToTrim;

  /// 実用のデフォルトカテゴリーを実用カテゴリーにするかキルトカテゴリーにするか設定する
  @ffi.Bool()
  external bool isInitialStitchPage;

  /// 止め縫い優先
  @ffi.Bool()
  external bool isReinforcementPriority;

  /// マルチファンクションフットコントローラー
  @ffi.Int8()
  external int heelSwitchValue;

  /// マルチファンクションフットコントローラー
  @ffi.Int8()
  external int sidePedalVlue;

  /// 終点設定直前停止
  @ffi.Bool()
  external bool isEndPointSettingTemporaryStop;

  static ffi.Pointer<UtlUserSettingInfo> allocate() {
    final pointer = ffi.calloc<UtlUserSettingInfo>();
    return pointer;
  }
}

final class UserSettingEnabledInfo extends ffi.Struct {
  @ffi.Bool()
  external bool heelSwitch;

  @ffi.Bool()
  external bool sidePedal;

  @ffi.Bool()
  external bool presserFootHeight;

  @ffi.Bool()
  external bool presserFootPressure;

  @ffi.Bool()
  external bool baseLine;

  @ffi.Bool()
  external bool ledStart;

  static ffi.Pointer<UserSettingEnabledInfo> allocate() {
    final pointer = ffi.calloc<UserSettingEnabledInfo>();
    pointer.ref.heelSwitch = false;
    pointer.ref.sidePedal = false;
    pointer.ref.presserFootHeight = false;
    pointer.ref.presserFootPressure = false;
    pointer.ref.baseLine = false;
    pointer.ref.ledStart = false;
    return pointer;
  }
}

/// !
/// @brief 機器の動作状態
final class UserLogModelStatus_t extends ffi.Struct {
  /// !< 刺繍縫製でのサービスステッチ数（針）
  @ffi.Uint32()
  external int embServiceStitchCnt;

  /// !< 刺繍縫製でのステッチ数（針）
  @ffi.Uint32()
  external int embTotalStitchCnt;

  /// !< 実用縫製でのサービスステッチ数（針）
  @ffi.Uint32()
  external int utlServiceStitchCnt;

  /// !< 実用縫製でのステッチ数（針）
  @ffi.Uint32()
  external int utlTotalStitchCnt;

  /// !< サービスステッチ数の合計（針）
  @ffi.Uint32()
  external int serviceStitchCnt;

  /// !< ステッチ数の合計（針）
  @ffi.Uint32()
  external int totalStitchCnt;

  /// !< 糸通し実施回数（回）
  @ffi.Uint32()
  external int totalThreadCnt;

  ///!< 発生したエラーコード
  @ffi.Array.multi([30])
  external ffi.Array<ffi.Uint16> errorListCode;

  ///!< 発生したエラーの種類
  @ffi.Array.multi([30])
  external ffi.Array<ffi.Uint16> errorListType;

  static ffi.Pointer<UserLogModelStatus_t> allocate() {
    final pointer = ffi.calloc<UserLogModelStatus_t>();
    return pointer;
  }

  static deallocate(ffi.Pointer<UserLogModelStatus_t> pointer) {
    ffi.calloc.free(pointer);
  }
}

/// !
/// @brief ユーザー設定項目
final class UserLogUserSetting_t extends ffi.Struct {
  /// !< 刺繍最高回転数（spm）
  @ffi.Int32()
  external int embSpeed;
  static ffi.Pointer<UserLogUserSetting_t> allocate() {
    final pointer = ffi.calloc<UserLogUserSetting_t>();
    return pointer;
  }

  static deallocate(ffi.Pointer<UserLogUserSetting_t> pointer) {
    ffi.calloc.free(pointer);
  }
}

/// !
/// @brief ソフトウェアバージョン
final class UserLogSoftVersion_t extends ffi.Struct {
  /// パネル プログラム Ver.
  @ffi.Int32()
  external int panelMajorVer;

  /// パネル プログラム Ver.
  @ffi.Int32()
  external int panelMinorVer;

  /// パネル プログラム Ver.
  @ffi.Int32()
  external int panelPatchVer;

  /// 編集ライブラリ　プログラム Ver.
  @ffi.Int32()
  external int libMajorVer;

  /// 編集ライブラリ　プログラム Ver.
  @ffi.Int32()
  external int libMinorVer;

  /// 編集ライブラリ　プログラム Ver.
  @ffi.Int32()
  external int libPatchVer;

  /// メイン プログラム Ver.
  @ffi.Uint16()
  external int main_version;

  /// BOOT プログラム Ver.
  @ffi.Uint16()
  external int boot_version;

  /// マスター Ver.
  @ffi.Uint16()
  external int master_version;

  /// FPGA Ver.
  @ffi.Uint16()
  external int fpga_version;

  /// XY_CPU Ver.
  @ffi.Uint16()
  external int xy_cpu_version;

  @ffi.Array.multi([31])
  external ffi.Array<ffi.Char> boardType;


  static ffi.Pointer<UserLogSoftVersion_t> allocate() {
    final pointer = ffi.calloc<UserLogSoftVersion_t>();
    return pointer;
  }

  static deallocate(ffi.Pointer<UserLogSoftVersion_t> pointer) {
    ffi.calloc.free(pointer);
  }
}

final class UserLogProductInfo_t extends ffi.Struct {
  @ffi.Array.multi([12])
  external ffi.Array<ffi.Char> modeCode;

  @ffi.Array.multi([10])
  external ffi.Array<ffi.Char> modelSerial;

  /// !< 本体状態のバックアップ
  @ffi.Int8()
  external int backupState;

  /// パネル プログラム Ver.
  @ffi.Int32()
  external int panelMajorVer;

  /// パネル プログラム Ver.
  @ffi.Int32()
  external int panelMinorVer;

  /// パネル プログラム Ver.
  @ffi.Int32()
  external int panelPatchVer;

  /// 編集ライブラリ　プログラム Ver.
  @ffi.Int32()
  external int libMajorVer;

  /// 編集ライブラリ　プログラム Ver.
  @ffi.Int32()
  external int libMinorVer;

  /// 編集ライブラリ　プログラム Ver.
  @ffi.Int32()
  external int libPatchVer;

  /// メイン プログラム Ver.
  @ffi.Uint16()
  external int main_version;

  /// BOOT プログラム Ver.
  @ffi.Uint16()
  external int boot_version;

  /// マスター Ver.
  @ffi.Uint16()
  external int master_version;

  /// FPGA Ver.
  @ffi.Uint16()
  external int fpga_version;

  /// XY_CPU Ver.
  @ffi.Uint16()
  external int xy_cpu_version;

  @ffi.Array.multi([31])
  external ffi.Array<ffi.Char> boardType;

  /// !< 刺繍最高回転数（spm）
  @ffi.Int32()
  external int embSpeed;

  /// !< 刺繍縫製でのサービスステッチ数（針）
  @ffi.Uint32()
  external int embServiceStitchCnt;

  /// !< 刺繍縫製でのステッチ数（針）
  @ffi.Uint32()
  external int embTotalStitchCnt;

  /// !< 実用縫製でのサービスステッチ数（針）
  @ffi.Uint32()
  external int utlServiceStitchCnt;

  /// !< 実用縫製でのステッチ数（針）
  @ffi.Uint32()
  external int utlTotalStitchCnt;

  /// !< サービスステッチ数の合計（針）
  @ffi.Uint32()
  external int serviceStitchCnt;

  /// !< ステッチ数の合計（針）
  @ffi.Uint32()
  external int totalStitchCnt;

  /// !< 糸通し実施回数（回）
  @ffi.Uint32()
  external int totalThreadCnt;

  @ffi.Array.multi([30])
  external ffi.Array<ffi.Uint16> errorListCode;

  @ffi.Array.multi([30])
  external ffi.Array<ffi.Uint16> errorListType;


  static ffi.Pointer<UserLogProductInfo_t> allocate() {
    final pointer = ffi.calloc<UserLogProductInfo_t>();
    return pointer;
  }

  static deallocate(ffi.Pointer<UserLogProductInfo_t> pointer) {
    ffi.calloc.free(pointer);
  }

}

///
/// ポインターShape 設定値
///
class PRJ_POINTERSHAPE_E {
  static const int PRJ_POINTERSHAPE_T = 1;
//↓IIVO試作　刺繍プロジェクター背景色拡張対応
  static const int PRJ_POINTERSHAPE_CROSS = 2;
//↑IIVO試作　刺繍プロジェクター背景色拡張対応
  static const int PRJ_POINTERSHAPE_DOT = 3;
  static const int PRJ_POINTERSHAPE_MAX = 4;
}

/// フットコントローラ ヒールキック 機能情報定義
enum FotHeelKick_FuncNum_t {
  /* Thread Cutting */
  FHK_FUNC_TC,
  /* Needle Position */
  FHK_FUNC_NP,
  /* One Stitch */
  FHK_FUNC_ONES,
  /* Reverse Stitch */
  FHK_FUNC_REV,
  /* PfUp UpDown */
  FHK_FUNC_PFTUP,
  /* Reinforcement Stitch */
  FHK_FUNC_REINFORCEMENT,
  /* No Setting */
  FHK_FUNC_NOSET,
  FHK_FUNC_MAX,
}

enum FotSideSw_FuncNum_t {
  /* Thread Cutting */
  FSSW_FUNC_TC,
  /* Needle Position */
  FSSW_FUNC_NP,
  /* One Stitch */
  FSSW_FUNC_ONES,
  /* Reverse Stitch */
  FSSW_FUNC_REV,
  /* PfUp UpDown */
  FSSW_FUNC_PFTUP,
  /* No Setting */
  FSSW_FUNC_NOSET,

  FSSW_FUNC_MAX,
}

/*	対応言語（国別） 定数定義（言語 番号定義）	*/
enum LangNum_t {
  /*	英語			*/
  LANG_ENGLISH,
  /*	ドイツ語		*/
  LANG_GERMAN,
/*	フランス語		*/
  LANG_FRENCH,
  /*	イタリア語		*/
  LANG_ITALIAN,
  /*	オランダ語		*/
  LANG_DUTCH,
  /*	スペイン語		*/
  LANG_SPANISH,
  /*	日本語			*/
  LANG_JAPAN,
  // /*	デンマーク語	*/
  // LANG_DANISH,
  // /*	ノルウェー語	*/
  // LANG_NORWEGIAN,
  // /*	フィンランド語	*/
  // LANG_FINNISH,
  // /*	スウェーデン語	*/
  // LANG_SWEDISH,
  // /*	ポルトガル語	*/
  // LANG_PORTUGUESE,
  // /*	ロシア語		*/
  // LANG_RUSSIAN,
  // /*	中国語			*/
  // LANG_CHINESE,

  // LANG_MAX
}

enum EMBCAMERAUI_PROJECTION_E {
  EMBCAMERAUI_PROJECTION_ON, //カメラペンUI投影ON
  EMBCAMERAUI_PROJECTION_OFF, //カメラペンUI投影OFF
  EMBCAMERAUI_PROJECTION_ON_DISABLE, //カメラペンUI投影ON状態で設定機能無効状態
  EMBCAMERAUI_PROJECTION_OFF_DISABLE, //カメラペンUI投影OFF状態で設定機能無効状態
  EMBCAMERAUI_PROJECTION_MAX
}

// EEP読み書き先識別子
enum BOARD_TYPE{
  NON,		//0x00,				// なし
  PANEL,	//0x01,				// 対パネル基板
  MAIN,		// 0x02,				// 対メイン基板
  PANEL_AND_MAIN,		//( PANEL | MAIN ),	// パネル&メイン基板の両方
}

//ユーザ情報のbackupState用
//ミシン本体の状態:Initial前
const int BRNETGATEWAY_MONITORING_STATUS_BEFOREINITIAL = 0;
//ミシン本体の状態:エラーなし停止中(緑)
const int BRNETGATEWAY_MONITORING_STATUS_NOERRSTOP_GREEN = 10;
//ミシン本体の状態:エラーなし停止中(赤)
const int BRNETGATEWAY_MONITORING_STATUS_NOERRSTOP_RED = 11;
//ミシン本体の状態:実用縫製中
const int BRNETGATEWAY_MONITORING_STATUS_UTLSEWING = 20;
//ミシン本体の状態:刺繍縫製中
const int BRNETGATEWAY_MONITORING_STATUS_EMBSEWING = 30;
//ミシン本体の状態:刺繍の糸替え待ち
const int BRNETGATEWAY_MONITORING_STATUS_EMBCHANGETHERAD = 40;
//ミシン本体の状態:縫製完了
const int BRNETGATEWAY_MONITORING_STATUS_SEWINGCOMPLETE = 50;
//ミシン本体の状態:上糸エラー
const int BRNETGATEWAY_MONITORING_STATUS_UPPERTHREAD_ERR = 60;
//ミシン本体の状態:下糸エラー
const int BRNETGATEWAY_MONITORING_STATUS_LOWERTHREAD_ERR = 61;
//ミシン本体の状態:ワイパーエラー
const int BRNETGATEWAY_MONITORING_STATUS_WIPER_ERR = 62;
//ミシン本体の状態:ミシンで針数更新できない状態(設定画面時など)
const int BRNETGATEWAY_MONITORING_STATUS_NON_UPDATE = 98;
//ミシン本体の状態:そのほかのerror停止中
const int BRNETGATEWAY_MONITORING_STATUS_OTHER_ERR = 99;
//刺繍機あり
const int BRNETGATEWAY_MONITORING_STATUS_EMB_ATTACHED = 1;
//刺繍機なし
const int BRNETGATEWAY_MONITORING_STATUS_EMB_NOTON = 0;
//無効値
const int BRNETGATEWAY_MONITORING_STATUS_INVALID = 0;
