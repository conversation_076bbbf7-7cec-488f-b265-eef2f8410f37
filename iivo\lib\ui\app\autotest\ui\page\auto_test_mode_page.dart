import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'auto_test_mode_interface.dart';
import 'auto_test_mode_view_model.dart';

final _autoTestModeProvider = StateNotifierProvider.autoDispose<
    AutoTestModeViewModelInterface,
    AutoTestModeState>((ref) => AutoTestModeViewModel(ref));

class AutoTestModePage extends StatefulPage {
  const AutoTestModePage({super.key});

  @override
  PageState<StatefulPage> createState() {
    return _AutoTestPageState();
  }
}

class _AutoTestPageState extends PageState<AutoTestModePage> {
  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final AutoTestModeState state = ref.watch(_autoTestModeProvider);
    return Scaffold(
      body: SafeArea(
          child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
        child: <PERSON>umn(
          children: [
            const Padding(
              padding: EdgeInsets.only(bottom: 16),
              child: Text(
                "Auto Test Mode",
                style: TextStyle(fontSize: 25, fontWeight: FontWeight.bold),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Text(
                state.isPerformingActions
                    ? "Performing auto test setup, do not turn off the machine."
                    : "Auto test setup finished.",
                textAlign: TextAlign.left,
                style: const TextStyle(fontSize: 20),
              ),
            ),
            Text(
              state.currentActionDesc ?? "",
              textAlign: TextAlign.left,
              style: const TextStyle(fontSize: 16),
            )
          ],
        ),
      )),
    );
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup(WidgetRef ref) {
    return {};
  }
}
