import 'dart:async';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../model/pattern_image_capture_model.dart';
import '../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../global_popup/global_popups/err_emb_too_much_selected_go_menu/err_emb_too_much_selected_go_menu_view_model.dart';
import '../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../model/pattern_data_reader/bh_pattern_reader.dart';
import '../../../../model/pattern_data_reader/pattern_data_base.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/redo_undo_model.dart';
import '../../../../model/select_information_model.dart';
import '../../../../model/select_model.dart';
import '../../../../model/thread_color_list_model.dart';
import '../../../page_route.dart';
import '../category_selector/category_selector_view_interface.dart';
import '../category_selector/category_selector_view_model.dart';
import 'bh_pattern_selector_interface.dart';

final bhPatternSelectorViewModelProvider = StateNotifierProvider.autoDispose<
    BhPatternSelectorViewModel,
    BhPatternSelectorState>((ref) => BhPatternSelectorViewModel(ref));

class BhPatternSelectorViewModel extends BhPatternSelectorInterface {
  BhPatternSelectorViewModel(Ref ref)
      : super(
            BhPatternSelectorState(
              patternSize: PatternModel().thumbnailSize,
              bhSize: EmbBHSizeKind.bhSizeL,
              isSizeImageVisible: false,
              isRightButtonDisable: true,
              isLeftButtonDisable: false,
              scrollController: ScrollController(),
              selectCategoryIndex: 0,
              isSetButtonEnable: false,
              gridColor: PatternModel().getThumbnailBackgroundColor(),
              categoryImageData: BhPatternImageReader().getAllBhPatternsInfo(),
            ),
            ref) {
    _prePatternSize = PatternModel().thumbnailSize;

    update();
  }
  static const invalidNum = -1;

  ///
  /// 安定化用タイマー
  ///
  Timer? _debounceTimer;

  ///
  /// タッチスクリーンハンドインデックス
  ///
  static const int touchScreenFingerCount = 2;

  ///
  /// ひれい
  ///
  final double _scale = 1.0;

  @override
  void onCategoryClick(int index) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    state = state.copyWith(
      selectCategoryIndex: index,
      scrollController: ScrollController(),
    );
  }

  ThumbnailSizeType _prePatternSize = ThumbnailSizeType.M;

  @override
  void update() {
    state = state.copyWith(
        patternSize: PatternModel().thumbnailSize,
        gridColor: PatternModel().getThumbnailBackgroundColor(),
        patternListHeight: patternListHeightDefault);
    if (_prePatternSize != state.patternSize) {
      _prePatternSize = state.patternSize;
      state.scrollController.jumpTo(0.0);
    } else {}
  }

  @override
  void onPatternClick(int patternIndex, int categoryType) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    EmbBHSizeKind bhSize = state.bhSize;
    bool isSizeImageVisible = state.isSizeImageVisible;
    bool isRightButtonDisable = state.isRightButtonDisable;
    bool isLeftButtonDisable = state.isLeftButtonDisable;

    /// BHカテゴリ1はSizeを調整できる
    if (state.selectCategoryIndex == 0) {
      if (SelectModel().selectedPatternIndex != patternIndex) {
        bhSize = EmbBHSizeKind.bhSizeL;
        isSizeImageVisible = true;
        isRightButtonDisable = true;
        isLeftButtonDisable = false;
      } else {
        bhSize = state.bhSize;
        isSizeImageVisible = true;
      }
    } else {
      bhSize = EmbBHSizeKind.bhSizeL;
      isSizeImageVisible = false;
      isRightButtonDisable = true;
      isLeftButtonDisable = false;
    }

    /// Model更新
    SelectModel().selectedPatternIndex = BhPatternImageReader()
        .getAllBhPatternsInfo()[state.selectCategoryIndex]
        .patternNumGroup[patternIndex];
    SelectModel().selectedPatternIndex =
        SelectModel().selectedPatternIndex == invalidNum
            ? patternIndex
            : SelectModel().selectedPatternIndex;
    SelectModel().selectedCategoryType = categoryType;

    assert(SelectModel().selectedCategoryType != null, '割り当てに失敗しました');
    assert(SelectModel().selectedPatternIndex != null, '割り当てに失敗しました');
    EmbLibraryError error = PatternModel().selectEmb(
      SelectModel().selectedPatternIndex!,
      SelectModel().selectedCategoryType!,
      bhSize: bhSize,
    );

    /// メモリーフル時の対応
    if (error == EmbLibraryError.EMB_DATAOVER_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
          arguments: ErrEmbTooMuchSelectedGoMenuArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
      return;
    }

    /// View更新
    state = state.copyWith(
      isSetButtonEnable: true,
      bhSize: bhSize,
      isSizeImageVisible: isSizeImageVisible,
      isRightButtonDisable: isRightButtonDisable,
      isLeftButtonDisable: isLeftButtonDisable,
      isEyeletPattern: _isEyeletPattern(patternIndex),
    );
    ref
        .read(categorySelectorViewModelProvider.notifier)
        .updateTopPageByChild(CategorySelectorModuleType.patternSelector);
  }

  @override
  void onSetButtonClick(BuildContext context) {
    EmbLibraryError error = EmbLibrary().apiBinding.embGotoEdit();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return;
    }
    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }
    state = state.copyWith(scrollController: ScrollController());

    /// Model更新
    PatternModel()
      ..reloadAllPattern()
      ..clearTemporaryPatternList();

    SelectModel()
      ..selectedCategoryType = null
      ..selectedPatternIndex = null;
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }
    SelectInformationModel()
        .setTemporaryPatternAutoKind(CategoryType.unknownPattern);

    PagesRoute().pushNamedAndRemoveUntil(
        nextRoute: PageRouteEnum.patternEdit, untilRoute: PageRouteEnum.home);
  }

  @override
  void onReturnButtonClicked(BuildContext context) {
    state = state.copyWith(scrollController: ScrollController());

    /// Model更新
    PatternModel().deleteTemporaryPatternList();
    SelectInformationModel()
        .setTemporaryPatternAutoKind(CategoryType.unknownPattern);
    SelectModel()
      ..selectedCategoryType = null
      ..selectedPatternIndex = null;

    _closeSelf(context);
  }

  void _closeSelf(BuildContext context) {
    PopupNavigator.pop(context: context);
  }

  @override
  void onBhSizeLeftButtonClicked() {
    if (state.bhSize == EmbBHSizeKind.bhSizeXs) {
      return;
    }

    EmbBHSizeKind bhSize = EmbBHSizeKind.values[state.bhSize.index - 1];
    bool isLeftButtonDisable = bhSize == EmbBHSizeKind.bhSizeXs ? true : false;

    /// Model更新
    assert(SelectModel().selectedCategoryType != null, '割り当てに失敗しました');
    assert(SelectModel().selectedPatternIndex != null, '割り当てに失敗しました');
    EmbLibraryError error = PatternModel().selectEmb(
      SelectModel().selectedPatternIndex!,
      SelectModel().selectedCategoryType!,
      bhSize: bhSize,
    );

    /// メモリーフル時の対応
    if (error == EmbLibraryError.EMB_DATAOVER_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
          arguments: ErrEmbTooMuchSelectedGoMenuArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
      return;
    }

    /// View更新
    state = state.copyWith(
      bhSize: bhSize,
      isRightButtonDisable: false,
      isLeftButtonDisable: isLeftButtonDisable,
    );
    ref
        .read(categorySelectorViewModelProvider.notifier)
        .updateTopPageByChild(CategorySelectorModuleType.patternSelector);
  }

  @override
  void onBhSizeRightButtonClicked() {
    if (state.bhSize == EmbBHSizeKind.bhSizeL) {
      return;
    }

    EmbBHSizeKind bhSize = EmbBHSizeKind.values[state.bhSize.index + 1];
    bool isRightButtonDisable = bhSize == EmbBHSizeKind.bhSizeL ? true : false;

    /// Model更新
    assert(SelectModel().selectedCategoryType != null, '割り当てに失敗しました');
    assert(SelectModel().selectedPatternIndex != null, '割り当てに失敗しました');
    EmbLibraryError error = PatternModel().selectEmb(
      SelectModel().selectedPatternIndex!,
      SelectModel().selectedCategoryType!,
      bhSize: bhSize,
    );

    /// メモリーフル時の対応
    if (error == EmbLibraryError.EMB_DATAOVER_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
          arguments: ErrEmbTooMuchSelectedGoMenuArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
      return;
    }

    /// View更新
    state = state.copyWith(
      bhSize: bhSize,
      isRightButtonDisable: isRightButtonDisable,
      isLeftButtonDisable: false,
    );
    ref
        .read(categorySelectorViewModelProvider.notifier)
        .updateTopPageByChild(CategorySelectorModuleType.patternSelector);
  }

  @override
  void onEmbroideryButtonClicked(BuildContext context) {
    if (ThreadColorListModel().isNoThreadToSewing()) {
      return;
    }
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.sewing);
  }

  ///
  /// プレビューのKey定義
  ///
  final GlobalKey<State<StatefulWidget>> _patternViewKey = GlobalKey();
  @override
  GlobalKey<State<StatefulWidget>> get patternViewKey => _patternViewKey;

  static const double patternListHeightDefault = 367;

  ///
  /// ファイルハンドリング関数
  ///
  @override
  Future<void> handleKeyPress(KeyEvent event) async {
    /// パターンイメージキャプチャモデルのインスタンス作成
    final model = PatternImageCaptureModel();

    /// キーイベントを処理
    model.handleKeyPress(event, () async {
      /// カテゴリイメージデータを取得
      final categoryImageData =
          state.categoryImageData[state.selectCategoryIndex];
      final height = await model.getPatternListHeight(
          state.patternSize.index, categoryImageData.iconsGroup);

      /// アプリケーションの状態を更新
      state = state.copyWith(
        patternListHeight: height,
        fourSFunctionOn: true,
      );

      /// 1秒待つ
      await Future.delayed(const Duration(seconds: 1));

      /// パターンリストを画像として保存
      await model.savePatternListToImage(_patternViewKey);

      List<PatternFilterGroup>? patternFilterInfo;
      if (categoryImageData is PatternInfoGroupImplement) {
        patternFilterInfo =
            (categoryImageData as PatternInfoGroupImplement).patternFilterInfo;
      } else {
        patternFilterInfo = null;
      }

      /// スレッド情報をHTMLとして保存
      await model.saveThreadInfoToHtml(
        state.selectCategoryIndex,
        categoryImageData.categoryType,
        categoryImageData.iconsGroup,
        patternFilterInfo,
      );

      /// アプリケーションの状態を元に戻す
      state = state.copyWith(
        patternListHeight: patternListHeightDefault,
        fourSFunctionOn: false,
      );
    });
  }

  ///
  /// 2本指による拡縮
  ///
  @override
  void onScaleUpdate(ScaleUpdateDetails details) {
    if (details.pointerCount == touchScreenFingerCount) {
      _debounceTimer?.cancel();

      /// 新しいタイマーを開始し、遅延実行の時間を 300 ミリ秒に設定します
      _debounceTimer = Timer(const Duration(milliseconds: 300), () {
        _handleScaleStop(details);
      });
    }
  }

  void _handleScaleStop(ScaleUpdateDetails details) {
    PatternModel().scaleUpdate(details, _scale);
    state = state.copyWith(patternSize: PatternModel().thumbnailSize);
  }

  ///
  /// カテゴリの画像の幅を取得します
  ///
  @override
  double getCategoryImageWidth(int index) =>
      index == state.categoryImageData.length - 1 ? 128 : 121;

  ///
  /// BHの線径の全体像
  ///
  @override
  Widget getSelectedBhSizeIcon() {
    if (state.bhSize.index < 0 ||
        state.bhSize.index > EmbBHSizeKind.values.length) {
      Log.assertTrace("現在選択の模様，インデックスが範囲外です ！");
    }
    return [
      const ico_s0_embselect_bhsize(),
      const ico_s1_embselect_bhsize(),
      const ico_s2_embselect_bhsizes(),
      const ico_s3_embselect_bhsize(),
      const ico_s4_embselect_bhsize(),
    ][state.bhSize.index];
  }

  ///
  /// BHのEyeletのSize
  ///
  @override
  Widget getSelectedEyeletSizeIcon() {
    if (state.bhSize.index < 0 ||
        state.bhSize.index > EmbBHSizeKind.values.length) {
      Log.assertTrace("現在選択の模様，インデックスが範囲外です ！");
    }
    return [
      const ico_s0_embselect_bhsize1(),
      const ico_s1_embselect_bhsize2(),
      const ico_s2_embselect_bhsizes3(),
      const ico_s3_embselect_bhsize4(),
      const ico_s4_embselect_bhsize5(),
    ][state.bhSize.index];
  }

  ///
  /// カントール対応関数
  /// パラメータ
  /// [in]:
  ///   x: 二つの整数のうちの一つで、ペアリングを行う自然数を表します
  ///   y: xとペアリングするもう一つの整数で、ペアリングを行うもう一つの自然数を表します
  /// [out]:
  ///   カントール対応関数は、異なる自然数のペアが一意の結果を生成することを保証します
  ///   与えられた自然数のペアを表す一意の整数を返します
  ///
  @override
  int cantorPairing(int x, int y) => (x + y) * (x + y + 1) ~/ 2 + y;

  ///
  /// Eyeletタイプかどうかを判定します
  /// データソース : bh_pattern_image_brother.json/bh_pattern_image_tacony.json
  ///
  bool _isEyeletPattern(int patternIndex) {
    /// BHPatternの型です
    const int bhCategoryType = 36;

    /// Eyelet01のPatternNumです
    const int eyelet01PatternNum = 0;

    /// Eyelet02のPatternNumです
    const int eyelet02PatternNum = 1;

    /// 現在選択されているカテゴリはBHタイプです
    const int categoryBhTypeIndex = 0;

    if (state.selectCategoryIndex != categoryBhTypeIndex) {
      return false;
    }

    final currentCategoryDate =
        state.categoryImageData[state.selectCategoryIndex];
    final patternNumGroup = currentCategoryDate.patternNumGroup[patternIndex];
    if (currentCategoryDate.categoryType == bhCategoryType) {
      if (patternNumGroup == eyelet01PatternNum ||
          patternNumGroup == eyelet02PatternNum) {
        return true;
      }
    }
    return false;
  }
}
