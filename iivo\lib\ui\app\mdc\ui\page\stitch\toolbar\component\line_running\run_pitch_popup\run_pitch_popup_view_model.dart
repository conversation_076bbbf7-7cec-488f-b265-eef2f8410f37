import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../model/device_info_model.dart';
import '../../../../../../../model/resume_history_model.dart';
import '../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../model/stitch/line_running_model.dart';
import '../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../stitch_page_view_model.dart';
import 'run_pitch_popup_view_interface.dart';

final runPitchPopupViewModelProvider = StateNotifierProvider.autoDispose<
    RunPitchPopupStateViewInterface,
    RunPitchPopupState>((ref) => RunPitchPopupViewModel(ref));

class RunPitchPopupViewModel extends RunPitchPopupStateViewInterface {
  RunPitchPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const RunPitchPopupState(
              runPitchDisplayValue: "",
              isDefaultStyle: false,
              isUnitMm: true,
              isMinusButtonValid: true,
              isPlusButtonValid: true,
            ),
            ref) {
    update();
  }

  ///
  /// 長押しすべステップ量
  ///
  static const int _stepValueLongPress = 10;

  ///
  /// ステップ量
  ///
  static int _stepValue = 1;

  ///
  /// ピッチ値ディスプレイスター
  ///
  bool _isRunPitchValueDisplayStar =
      LineRunningModel().getRunPitch() != LineRunningModel.pitchNotUpdating
          ? false
          : true;

  ///
  /// ピッチ値
  ///
  int _runPitchValue = LineRunningModel().getRunPitch();

  @override
  void update() {
    state = state.copyWith(
      isUnitMm:
          DeviceInfoModel().displayUnitType == DisplayUnit.mm ? true : false,
      runPitchDisplayValue: _getRunPitchDisplayValue(),
      isDefaultStyle: _isDefaultStyle(),
      isMinusButtonValid: _getMinusButtonState(),
      isPlusButtonValid: _getPlusButtonState(),
    );
  }

  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_isRunPitchValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isRunPitchValueDisplayStar = false;

      ///  Model 更新
      _runPitchValue = LineRunningModel().runPitchDefaultValue;

      /// View更新
      update();

      if (isLongPress == false) {
        return false;
      } else {
        return true;
      }
    }
    isLongPress == false ? _stepValue = 1 : _stepValue = _stepValueLongPress;
    if (_runPitchValue == LineRunningModel().runPitchValueMin) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress == true) {
      _runPitchValue -= _runPitchValue % LineRunningModel.conversionRate;
    }

    if (_runPitchValue > LineRunningModel().runPitchValueMin) {
      ///  Model 更新
      _runPitchValue -= _stepValue;
    }

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_isRunPitchValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isRunPitchValueDisplayStar = false;

      ///  Model 更新
      _runPitchValue = LineRunningModel().runPitchDefaultValue;

      /// View更新
      update();

      if (isLongPress == false) {
        return false;
      } else {
        return true;
      }
    }
    isLongPress == false ? _stepValue = 1 : _stepValue = _stepValueLongPress;
    if (_runPitchValue == LineRunningModel().runPitchValueMax) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    if (isLongPress == true) {
      _runPitchValue -= _runPitchValue % LineRunningModel.conversionRate;
    }

    ///  Model 更新
    _runPitchValue += _stepValue;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineRunningRunPitch.toString());
    if (_isRunPitchValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int runPitchValue = LineRunningModel().getRunPitch();

    /// Model 更新
    LineRunningModel().setRunPitch(_runPitchValue);
    if (LineRunningModel().setMdcRunningStitchSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (runPitchValue != _runPitchValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// ランピッチの表示値を取得する
  ///
  String _getRunPitchDisplayValue() {
    double runPitchValue = _runPitchValue / LineRunningModel.conversionRate;

    if (_isRunPitchValueDisplayStar) {
      if (DeviceInfoModel().displayUnitType == DisplayUnit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }
    if (DeviceInfoModel().displayUnitType == DisplayUnit.mm) {
      return runPitchValue.toStringAsFixed(1);
    } else {
      return ToolbarModel.getDisplayInchShowValue(runPitchValue);
    }
  }

  ///
  /// ランピッチ表示テキストスタイルを取得します
  ///
  bool _isDefaultStyle() {
    if (_isRunPitchValueDisplayStar) {
      return true;
    }

    if (_runPitchValue == LineRunningModel().runPitchDefaultValue) {
      return true;
    }

    return false;
  }

  ///
  /// マイナス ボタンの状態を取得します
  ///
  bool _getMinusButtonState() {
    if (_isRunPitchValueDisplayStar) {
      return true;
    }

    if (_runPitchValue <= LineRunningModel().runPitchValueMin) {
      return false;
    }
    return true;
  }

  ///
  /// プラス ボタンの状態を取得します
  ///
  bool _getPlusButtonState() {
    if (_isRunPitchValueDisplayStar) {
      return true;
    }

    if (_runPitchValue >= LineRunningModel().runPitchValueMax) {
      return false;
    }
    return true;
  }
}
