import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:network_wifi/network_wifi.dart';

import '../../../../../../../emb/ui/page_route.dart';
import '../component/wireless _lan_status/wlan_agreement_popup/wlan_agreement_popup.dart';
import 'network_others_setting_page_interface.dart';

final networkOthersSettingViewModelProvider = StateNotifierProvider.autoDispose<
        NetworkOthersSettingPageInterface, NetworkOthersSettingPageState>(
    (ref) => NetworkOthersSettingPageViewModel(ref));

class NetworkOthersSettingPageViewModel
    extends NetworkOthersSettingPageInterface {
  NetworkOthersSettingPageViewModel(Ref ref)
      : super(const NetworkOthersSettingPageState()) {
    _init(ref);
  }

  ///
  /// 初期化関数：Wi-Fiの変更イベントを監視し、MACアドレスを初期化する
  ///
  void _init(Ref ref) {
    // hotパラメータをtrueに設定すると、
    // 最近のWi-Fiイベントがすぐに1回送信されるため、
    // MACアドレスを手動で初期化する必要がなくなります
    final Function() unsubscribe =
        WifiManager().wifiEventBroadcast.addSubscriber(hot: true, (_) {
      _updateMacAddress();
    });
    // refが破棄される際にリスナーを解除し、メモリリークを防ぎます
    ref.onDispose(unsubscribe);
  }

  ///
  /// 現在のMACアドレスを取得し、UIを更新する
  ///
  void _updateMacAddress() {
    final String macAddress = WifiManager().getMacAddress() ??
        NetworkOthersSettingPageState.defaultMacAddress;
    state = state.copyWith(macAddress: macAddress);
  }

  @override
  void onNetworkDiagnosisToolStartButtonClicked(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => const WLanAgreementPopup(),
      ),
    );
  }

  @override
  void onReturnButtonClicked(BuildContext context) {
    PagesRoute().pop();
  }
}
