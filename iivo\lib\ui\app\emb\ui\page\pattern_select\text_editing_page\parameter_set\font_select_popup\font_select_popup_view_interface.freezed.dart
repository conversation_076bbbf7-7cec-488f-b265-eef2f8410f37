// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'font_select_popup_view_interface.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$FontSelectPopupState {
  bool get hasExclusiveScriptFont => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $FontSelectPopupStateCopyWith<FontSelectPopupState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FontSelectPopupStateCopyWith<$Res> {
  factory $FontSelectPopupStateCopyWith(FontSelectPopupState value,
          $Res Function(FontSelectPopupState) then) =
      _$FontSelectPopupStateCopyWithImpl<$Res, FontSelectPopupState>;
  @useResult
  $Res call({bool hasExclusiveScriptFont});
}

/// @nodoc
class _$FontSelectPopupStateCopyWithImpl<$Res,
        $Val extends FontSelectPopupState>
    implements $FontSelectPopupStateCopyWith<$Res> {
  _$FontSelectPopupStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasExclusiveScriptFont = null,
  }) {
    return _then(_value.copyWith(
      hasExclusiveScriptFont: null == hasExclusiveScriptFont
          ? _value.hasExclusiveScriptFont
          : hasExclusiveScriptFont // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_FontSelectPopupStateCopyWith<$Res>
    implements $FontSelectPopupStateCopyWith<$Res> {
  factory _$$_FontSelectPopupStateCopyWith(_$_FontSelectPopupState value,
          $Res Function(_$_FontSelectPopupState) then) =
      __$$_FontSelectPopupStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool hasExclusiveScriptFont});
}

/// @nodoc
class __$$_FontSelectPopupStateCopyWithImpl<$Res>
    extends _$FontSelectPopupStateCopyWithImpl<$Res, _$_FontSelectPopupState>
    implements _$$_FontSelectPopupStateCopyWith<$Res> {
  __$$_FontSelectPopupStateCopyWithImpl(_$_FontSelectPopupState _value,
      $Res Function(_$_FontSelectPopupState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasExclusiveScriptFont = null,
  }) {
    return _then(_$_FontSelectPopupState(
      hasExclusiveScriptFont: null == hasExclusiveScriptFont
          ? _value.hasExclusiveScriptFont
          : hasExclusiveScriptFont // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_FontSelectPopupState implements _FontSelectPopupState {
  const _$_FontSelectPopupState({this.hasExclusiveScriptFont = false});

  @override
  @JsonKey()
  final bool hasExclusiveScriptFont;

  @override
  String toString() {
    return 'FontSelectPopupState(hasExclusiveScriptFont: $hasExclusiveScriptFont)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FontSelectPopupState &&
            (identical(other.hasExclusiveScriptFont, hasExclusiveScriptFont) ||
                other.hasExclusiveScriptFont == hasExclusiveScriptFont));
  }

  @override
  int get hashCode => Object.hash(runtimeType, hasExclusiveScriptFont);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FontSelectPopupStateCopyWith<_$_FontSelectPopupState> get copyWith =>
      __$$_FontSelectPopupStateCopyWithImpl<_$_FontSelectPopupState>(
          this, _$identity);
}

abstract class _FontSelectPopupState implements FontSelectPopupState {
  const factory _FontSelectPopupState({final bool hasExclusiveScriptFont}) =
      _$_FontSelectPopupState;

  @override
  bool get hasExclusiveScriptFont;
  @override
  @JsonKey(ignore: true)
  _$$_FontSelectPopupStateCopyWith<_$_FontSelectPopupState> get copyWith =>
      throw _privateConstructorUsedError;
}
