import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../component/mdc_footer/mdc_footer.dart';
import '../../component/mdc_header/mdc_header.dart';
import 'bottom/bottom.dart';
import 'projector/camera_pen/camera_pan.dart';
import 'stitch_page_view_model.dart';
import 'toolbar/toolbar.dart';
import 'top_bar/top_bar.dart';
import 'view_area/view_area.dart';

class StitchPage extends StatefulPage {
  const StitchPage({
    super.key,
    super.needMultipleClick = true,
  });

  @override
  PageState<StitchPage> createState() => StitchPageState();
}

class StitchPageState extends PageState<StitchPage> {
  @override
  void initPageState(BuildContext context, WidgetRef ref) {
    super.initPageState(context, ref);
    ref.read(stitchPageViewInfoProvider.notifier).context = context;
  }

  @override
  void initSubRouteState(SubRoutePopupNavigatorState navigator, WidgetRef ref) {
    ref.watch(stitchPageViewInfoProvider.notifier).navigator = navigator;
  }

  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    ref.watch(stitchPageViewInfoProvider);
    final state = ref.watch(stitchPageViewModelProvider);
    return Scaffold(
      body: Stack(
        children: [
          const MdcCameraPen(),
          pre_base_mydesigncenter(
            child: MultiTouchBlocker(
              child: Column(
                children: [
                  const Expanded(
                    flex: 71,
                    child: MdcHeader(),
                  ),
                  const Expanded(
                    flex: 89,
                    child: TopBar(),
                  ),
                  Expanded(
                    flex: 880,
                    child: pre_base_white_mdc(
                      child: Row(
                        children: [
                          const Spacer(flex: 564),
                          Expanded(
                            flex: 236,
                            child: Visibility(
                              visible: state.isDisplayToolBar,
                              maintainState: true,
                              child: const Toolbar(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 179,
                    child: Visibility(
                      visible: state.isDisplayBottom,
                      maintainState: true,
                      child: const Bottom(),
                    ),
                  ),
                  const Expanded(
                    flex: 61,
                    child: MdcFooter(),
                  ),
                ],
              ),
            ),
          ),
          const Column(
            children: [
              Spacer(flex: 160),
              Expanded(
                flex: 880,
                child: Row(
                  children: [
                    Expanded(
                      flex: 564,
                      child: Stack(
                        children: [
                          pic_mdc_mainpreview(),
                          ViewArea(),
                        ],
                      ),
                    ),
                    Spacer(flex: 236),
                  ],
                ),
              ),
              Spacer(flex: 240),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup(WidgetRef ref) =>
      ref.read(stitchPageViewModelProvider.notifier).registerNamedPopup();
}
