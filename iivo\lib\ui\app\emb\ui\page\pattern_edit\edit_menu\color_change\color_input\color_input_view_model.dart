import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../../../model/machine_config_model.dart';
import '../../../../../../../../../model/thread_color_model.dart';
import '../../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../../global_popup/global_popups/err_color_change_back_ok/err_color_change_back_ok_view_model.dart';
import '../../../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../../../model/color_change_model.dart';
import '../../../../../../model/pattern_model.dart';
import '../../../../../../model/redo_undo_model.dart';
import '../../../../../page_route.dart';
import '../color_change_view_model.dart';
import 'color_input_interface.dart';

final colorInputViewInfoProvider =
    AutoDisposeNotifierProvider<_ColorInputViewInfo, void>(
        () => _ColorInputViewInfo());

class _ColorInputViewInfo extends AutoDisposeNotifier<void> {
  @override
  void build() {}

  BuildContext? _context;
  BuildContext get context => _context!;
  set context(value) => _context = value;
}

final colorInputViewModelProvider = StateNotifierProvider.autoDispose<
    ColorInputViewModelInterface, ColorInputState>(
  (ref) => ColorInputViewModel(ref),
);

class ColorInputViewModel extends ColorInputViewModelInterface {
  ColorInputViewModel(Ref ref) : super(const ColorInputState(), ref) {
    _initialization();
  }

  ///
  /// スクロールコントローラのスクロール補償値
  ///
  double _scrollControllerOffset = 0.0;

  ///
  /// 言語取得する
  ///
  AppLocalizations get l10n => AppLocalizations.of(
      ref.read(colorInputViewInfoProvider.notifier).context)!;

  ///
  /// 関数 Marple を使用して、Palett Koloer Lister から Kologb 配列を抽出します
  ///
  @override
  List<Color> getColorRGB(List<BrandColorInfo> list) =>
      list.map((colorInfo) => colorInfo.colorRGB).toList();

  ///
  /// map 関数を使用して、paletteColorList から index300 配列を抽出します
  ///
  @override
  List<int> getIndex(List<BrandColorInfo> list) =>
      list.map((colorInfo) => colorInfo.index300).toList();

  ///
  /// 初期化関数
  ///
  void _initialization() {
    if (DeviceLibrary().apiBinding.getThreadBrand().value == 0) {
      ///設定がoriginの場合、brotherとtaconyの初期ブランドは異なります
      ColorChangeModel().threadBrandName = MachineConfigModel().isBrother
          ? ThreadBrandName.embroideryCustom
          : ThreadBrandName.madeiraPoly;
    } else {
      ColorChangeModel().threadBrandName =
          ColorChangeModel().threadBrandNameList[
              DeviceLibrary().apiBinding.getThreadBrand().value - 1];
    }

    /// Model更新
    ColorChangeModel().updateBrandColorInfoList();

    /// stateの更新
    state = state.copyWith(
      threadBrandName: ColorChangeModel().threadBrandName,
      paletteColorList: ColorChangeModel().currentBrandColorList,
      scrollController: ScrollController(),
    );
  }

  @override
  void update() {
    if (ColorChangeModel().isColorShuffling) {
      if (state.scrollController!.hasClients) {
        state.scrollController?.jumpTo(0.0);
      } else {
        _scrollControllerOffset = 0.0;
      }
    }

    int? selectIndex = ColorChangeModel().selectedColorPositionInfo?.index;
    if (selectIndex == null) {
      state = state.copyWith(
        threadCode: "",
        threadColor: null,
        threadName: "",
      );
    } else {
      BrandColorInfo threadColor =
          ColorChangeModel().currentBrandColorList[selectIndex];
      state = state.copyWith(
        threadCode: threadColor.threadCode.toString().padLeft(4, '0'),
        threadColor: threadColor.colorRGB,
        threadName: ThreadColorModel()
            .getThreadColorNameWithIndex300(threadColor.index300),
      );
    }

    state = state.copyWith(
      isEnglish: PatternModel().isEnglish,
      isTapColorSelected: ColorChangeModel().isTapColorSelected,
      threadBrandName: ColorChangeModel().threadBrandName,
      paletteColorList: ColorChangeModel().currentBrandColorList,
      selectedColorPositionInfo: ColorChangeModel().selectedColorPositionInfo,
      scrollController: ScrollController(
        initialScrollOffset: _scrollControllerOffset,
      ),
    );

    /// スクロールコントローラのリスニングの設定
    state.scrollController!.addListener(() {
      if (state.scrollController!.hasClients) {
        _scrollControllerOffset = state.scrollController!.offset;
      }
    });
  }

  @override
  void onTapColorClicked() {
    if (state.isTapColorSelected == false) {
      /// Model更新
      ColorChangeModel().isTapColorSelected = true;

      /// state更新
      state = state.copyWith(
        isTapColorSelected: true,
        scrollController: ScrollController(
          initialScrollOffset: _scrollControllerOffset,
        ),
      );

      /// スクロールコントローラのリスニングの設定
      state.scrollController!.addListener(() {
        if (state.scrollController!.hasClients) {
          _scrollControllerOffset = state.scrollController!.offset;
        }
      });
    }
  }

  @override
  void onTapNumberClicked() {
    if (state.isTapColorSelected == true) {
      /// Model更新
      ColorChangeModel().isTapColorSelected = false;

      /// state更新
      state = state.copyWith(isTapColorSelected: false);
    }
  }

  @override
  void onLeftButtonClicked() {
    int index =
        ColorChangeModel().threadBrandNameList.indexOf(state.threadBrandName);
    state.scrollController!.dispose();

    /// 現在のブランドが最初で、クリックすると最後のブランドに切り替わります
    if (index == 0) {
      index = ColorChangeModel().threadBrandNameList.length - 1;
    } else {
      index = index - 1;
    }
    _scrollControllerOffset = 0;

    /// model更新
    ColorChangeModel().threadBrandName =
        ColorChangeModel().threadBrandNameList[index];
    ColorChangeModel().updateBrandColorInfoList();
    ColorChangeModel().selectedColorPositionInfo = null;

    /// state更新
    update();
  }

  @override
  void onRightButtonClicked() {
    int index =
        ColorChangeModel().threadBrandNameList.indexOf(state.threadBrandName);
    state.scrollController!.dispose();

    /// 現在のブランドが最後で、クリックして最初のブランドを切り替えます
    if (index == ColorChangeModel().threadBrandNameList.length - 1) {
      index = 0;
    } else {
      index = index + 1;
    }

    _scrollControllerOffset = 0;

    /// modelの更新
    ColorChangeModel().threadBrandName =
        ColorChangeModel().threadBrandNameList[index];
    ColorChangeModel().updateBrandColorInfoList();
    ColorChangeModel().selectedColorPositionInfo = null;

    /// stateの更新
    update();
  }

  @override
  void onTenKeyClicked(String value) {
    String threadCode = state.threadCode;
    String threadName = "";
    int threadCodeMaxLength = 4;

    threadCode += value;

    /// 入力した数字の桁数が最大桁数より大きい場合の処理
    if (threadCode.length > threadCodeMaxLength) {
      threadCode = threadCode.substring(
          threadCode.length - threadCodeMaxLength, threadCode.length);
    } else if (threadCode.length < threadCodeMaxLength) {
      /// 入力した数字の桁数が最大桁数未満の場合は、「0」を補足します
      threadCode = threadCode.padLeft(threadCodeMaxLength, '0');
    }

    BrandColorInfo? threadColor = ColorChangeModel().getThreadColor(threadCode);

    /// appliqueの線色を除外する
    if ([301, 302, 303].contains(threadColor?.index300)) {
      threadColor = null;
    }

    /// 入力されたThreadCodeには対応する色データがあります
    if (threadColor != null) {
      ColorChangeModel().isColorChanged = true;
      threadName = ThreadColorModel()
          .getThreadColorNameWithIndex300(threadColor.index300);

      ColorChangeModel().changeColorByPalette(
        ColorChangeModel().threadBrandName,
        ColorChangeModel().currentBrandColorList.indexOf(threadColor),
      );
      ColorChangeModel().selectedColorPositionInfo = SelectedColorPositionInfo(
          index: ColorChangeModel().currentBrandColorList.indexOf(threadColor));

      /// 他のページへの更新の通知
      ref
          .read(colorChangeViewModelProvider.notifier)
          .updateColorChangeByChild(ModuleType.colorChangeColorInput);
    }

    /// state更新
    state = state.copyWith(
      threadCode: threadCode,
      threadColor: threadColor?.colorRGB,
      threadName: threadName,
      selectedColorPositionInfo: ColorChangeModel().selectedColorPositionInfo,
    );
  }

  @override
  void onClearButtonClicked() {
    /// state更新
    update();
  }

  @override
  void onOKButtonClicked() {
    PatternModel().selectedZoomScaleInEditPage = zoomList.first;
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }
    PagesRoute().pop();
  }

  @override
  void onResetButtonClicked() {
    /// 色に変更がある場合は、ポップアップ確認ポップアップ窓
    if (ColorChangeModel().isColorChanged) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_COLOR_CHANGE_BACK_OK,
        arguments: ErrColorChangeBackOkArgument(
          onOKButtonClicked: (_) {
            /// スプールカラー修正リセット
            ColorChangeModel().resetEmbThreadColor();
            GlobalPopupRoute().resetErrorState();

            /// カラー修正リセット、フラグビット設定false
            ColorChangeModel().isColorChanged = false;

            update();

            /// 他のページへの更新の通知
            ref
                .read(colorChangeViewModelProvider.notifier)
                .updateColorChangeByChild(ModuleType.colorChangeColorInput);
          },
          onCancelButtonClicked: (_) {
            GlobalPopupRoute().resetErrorState();
          },
        ),
      );
    }
  }

  @override
  void onColorBlockClicked(int index) {
    ColorChangeModel().isColorChanged = true;

    BrandColorInfo threadColor =
        ColorChangeModel().currentBrandColorList[index];

    /// model更新
    ColorChangeModel().selectedColorPositionInfo =
        SelectedColorPositionInfo(index: index);
    ColorChangeModel()
        .changeColorByPalette(ColorChangeModel().threadBrandName, index);

    /// 他のページへの更新の通知
    ref
        .read(colorChangeViewModelProvider.notifier)
        .updateColorChangeByChild(ModuleType.colorChangeColorInput);

    /// State更新
    state = state.copyWith(
      threadCode: threadColor.threadCode.toString().padLeft(4, '0'),
      threadColor: threadColor.colorRGB,
      threadName: ThreadColorModel()
          .getThreadColorNameWithIndex300(threadColor.index300),
      selectedColorPositionInfo: ColorChangeModel().selectedColorPositionInfo,
    );
  }

  @override
  String getThreadBrandName() => ColorChangeModel().getThreadBrandName();

  @override
  int getSelectIndex300() {
    if (state.selectedColorPositionInfo == null) {
      return -1;
    } else {
      BrandColorInfo threadColor = ColorChangeModel()
          .currentBrandColorList[state.selectedColorPositionInfo!.index];
      return threadColor.index300 - 301;
    }
  }

  @override
  void clearColorInfoDisplay() {
    state = state.copyWith(
      threadCode: "",
      threadColor: null,
      threadName: "",
    );
  }
}
