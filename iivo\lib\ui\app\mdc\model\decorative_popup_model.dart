import 'dart:io' show IOException;

import 'package:flutter/services.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:path/path.dart';

import '../../../../../memory/memory.dart';
import 'memory_model.dart';

enum DecorativePatternType { buildIn, custom }

class DecorativePopupModel {
  DecorativePopupModel._internal();

  factory DecorativePopupModel() => _instance;
  static final DecorativePopupModel _instance =
      DecorativePopupModel._internal();

  ///
  /// モチーフのサムネイル幅/高
  ///
  static const double decorativeThumbnailWidth = 152;
  static const double decorativeThumbnailHeight = 152;

  ///
  /// 選択したモチーフサムネイル幅/高
  ///
  static const double displayThumbnailWidth = 132;
  static const double displayThumbnailHeight = 51;

  ///
  ///  モチーフビルドインファイル数
  ///
  static int get decorativeBuildFileNum =>
      MdcLibrary().apiBinding.getMdcBuiltInDecorativeFillTotalNum().totalNum;

  ///
  /// 開始ビルドインモチーフ番号
  ///
  static const int startDecorativeBuildInNo = 1;
  static const int invalidDecorativeNo = 0;

  ///
  /// 開始モチーフカスタム番号
  ///
  static int get startDecorativeCustomNo => 0x80;

  ///
  /// 選択したのモチーフカスタム番号
  ///
  int currentSelectedDecorativeNo = defaultDecorativeNo;
  static const defaultDecorativeNo = 1;

  ///
  /// 使用中のユーザー作成模様番号情報
  ///
  int usingDecoFillNum = 0;
  List<int> usingDecoFillList = [];

  ///
  /// 選択されたモチーフタイプ
  ///
  static DecorativePatternType getSelectedDecorativePatternType(
      int currentSelectedDecorativeNo) {
    if (currentSelectedDecorativeNo >= startDecorativeCustomNo) {
      return DecorativePatternType.custom;
    }
    return DecorativePatternType.buildIn;
  }

  ///
  /// モチーフカスタムパターンフルチェック
  ///
  static bool checkDecorativeCustomPatternFull() {
    try {
      for (int i = 0; i < _CustomFileInfoList.maxFileCount; i++) {
        final path = _CustomFileInfoList().getFilePathByIndex(i);
        final FileEntity decorativeImportFile = FileEntity(path);
        if (!decorativeImportFile.existsSync()) {
          return false;
        }
      }
    } on Exception {
      Log.errorTrace("check decorative custom pattern full failed");
      return false;
    }

    return true;
  }

  ///
  /// モチーフビルドインPMFデータリスト
  ///
  final List<Uint8List> decorativeBuildInThumbnailDataList = [];

  ///
  /// モチーフカスタムパターンを追加
  ///
  static Future<AccessError> addDecorativeCustomPattern(
      FileEntity plfFile) async {
    AccessError accessError = AccessError.none;
    try {
      if (MdcLibrary().apiBinding.isMdcPlfFileValid(plfFile.path).result ==
          false) {
        return AccessError.other;
      }

      for (int i = 0; i < _CustomFileInfoList.maxFileCount; i++) {
        final String path = _CustomFileInfoList().getFilePathByIndex(i);
        final FileEntity decorativeImportFile = FileEntity(path);
        if (decorativeImportFile.existsSync()) {
          continue;
        } else {
          plfFile.copySync(decorativeImportFile.path);
          break;
        }
      }
    } on IOException catch (e) {
      Log.errorTrace("add decorative custom pattern failed");
      accessError = memoryExceptionToAccessError(e);
    }

    return accessError;
  }

  ///
  /// モチーフカスタムパターン合計数を取得
  ///
  static int getDecorativeCustomPatternTotalNum() {
    int totalNum = 0;
    try {
      for (int i = 0; i < _CustomFileInfoList.maxFileCount; i++) {
        final String path = _CustomFileInfoList().getFilePathByIndex(i);
        final FileEntity decorativeImportFile = FileEntity(path);
        if (decorativeImportFile.existsSync()) {
          totalNum++;
        } else {
          break;
        }
      }
    } on Exception {
      Log.e(
          tag: memoryAccessErrorLogTag,
          description: "get decorative custom pattern total num failed");
    }
    return totalNum;
  }

  ///
  /// カスタムパターンの使用チェック
  ///
  bool checkCustomPatternUse(int decorativeNo) {
    for (int i = 0; i < usingDecoFillNum; i++) {
      if (usingDecoFillList[i] == decorativeNo) {
        return true;
      }
    }
    return false;
  }

  ///
  /// 使用中のユーザー作成模様番号情報を取得する
  ///
  void _getUsingDataInfo() {
    var info =
        MdcLibrary().apiBinding.isMdcUsingImportDataNumber(true).usingNumBuff;
    usingDecoFillNum = info.decoSetNum;

    usingDecoFillList = [];
    usingDecoFillList = info.decoUsingData;
  }

  ///
  /// モチーフカスタムサムネイルリストを読み込む
  ///
  List<Uint8List> loadDecorativeCustomThumbnailDataList() {
    List<Uint8List> decorativeCustomThumbnailDataList = [];

    for (int i = 0; i < _CustomFileInfoList.maxFileCount; i++) {
      final String path = _CustomFileInfoList().getFilePathByIndex(i);
      final FileEntity decorativeImportFile = FileEntity(path);

      if (decorativeImportFile.existsSync()) {
        Uint8List imageData = MdcLibrary()
            .apiBinding
            .getMdcCustomDecorativeFillThumbnail(
              decorativeImportFile.path,
              decorativeThumbnailWidth.toInt(),
              decorativeThumbnailHeight.toInt(),
              const Color(_lineColor),
            )
            .imageInfo
            .imageData;
        decorativeCustomThumbnailDataList.add(imageData);
        MdcLibrary().apiBinding.delMdcThumbImage();
      } else {
        break;
      }
    }

    setMdcImportFileList();
    return decorativeCustomThumbnailDataList;
  }

  ///
  /// モチーフビルドインサムネイルリストを読み込む
  ///
  List<Uint8List> loadDecorativeBuildInThumbnailDataList() {
    List<Uint8List> decorativeBuildInThumbnailDataList = [];

    for (int i = 1; i <= decorativeBuildFileNum; i++) {
      Uint8List thumbnailData = MdcLibrary()
          .apiBinding
          .getMdcBuiltInDecorativeFillThumbnail(
            i,
            decorativeThumbnailWidth.toInt(),
            decorativeThumbnailHeight.toInt(),
            const Color(_lineColor),
          )
          .imageInfo
          .imageData;

      decorativeBuildInThumbnailDataList.add(thumbnailData);
    }

    return decorativeBuildInThumbnailDataList;
  }

  ///
  /// 選択したサムネイルを取得する
  ///
  static Uint8List getSelectedPatternThumbnailData(
      int currentSelectedDecorativeNo) {
    if (getSelectedDecorativePatternType(currentSelectedDecorativeNo) ==
        DecorativePatternType.buildIn) {
      var imageData = MdcLibrary()
          .apiBinding
          .getMdcBuiltInDecorativeFillThumbnail(
            currentSelectedDecorativeNo,
            decorativeThumbnailWidth.toInt(),
            decorativeThumbnailHeight.toInt(),
            const Color(_lineColor),
          )
          .imageInfo
          .imageData;
      return imageData;
    } else {
      final int index = currentSelectedDecorativeNo - startDecorativeCustomNo;
      final String path = _CustomFileInfoList().getFilePathByIndex(index);

      var imageData = MdcLibrary()
          .apiBinding
          .getMdcCustomDecorativeFillThumbnail(
            path,
            decorativeThumbnailWidth.toInt(),
            decorativeThumbnailHeight.toInt(),
            const Color(_lineColor),
          )
          .imageInfo
          .imageData;
      MdcLibrary().apiBinding.delMdcThumbImage();
      return imageData;
    }
  }

  ///
  /// すべてのモチーフカスタムパターンを削除
  ///
  static bool deleteAllDecorativeCustomPattern() {
    try {
      for (int i = 0; i < _CustomFileInfoList.maxFileCount; i++) {
        final String path = _CustomFileInfoList().getFilePathByIndex(i);
        final FileEntity decorativeImportFile = FileEntity(path);
        if (decorativeImportFile.existsSync()) {
          decorativeImportFile.deleteSync();
        } else {
          break;
        }
      }
    } on Exception {
      Log.e(
          tag: memoryAccessErrorLogTag,
          description: "delete all decorative custom pattern failed");
      return false;
    }

    return true;
  }

  ///
  /// モチーフ画像データを読み取る
  ///
  static Uint8List readSelectedDecorativeThumbnailData(
      int currentSelectedDecorativeNo,
      {double thumbnailWidth = decorativeThumbnailWidth,
      double thumbnailHeight = decorativeThumbnailHeight}) {
    Uint8List selectedDecorativeThumbnailData = Uint8List.fromList([]);

    if (getSelectedDecorativePatternType(currentSelectedDecorativeNo) ==
        DecorativePatternType.custom) {
      final index = currentSelectedDecorativeNo - startDecorativeCustomNo;
      final String path = _CustomFileInfoList().getFilePathByIndex(index);

      selectedDecorativeThumbnailData = MdcLibrary()
          .apiBinding
          .getMdcCustomDecorativeFillThumbnail(
            path,
            thumbnailWidth.toInt(),
            thumbnailHeight.toInt(),
            const Color(_lineColor),
          )
          .imageInfo
          .imageData;
      MdcLibrary().apiBinding.delMdcThumbImage();
    } else {
      selectedDecorativeThumbnailData = MdcLibrary()
          .apiBinding
          .getMdcBuiltInDecorativeFillThumbnail(
            currentSelectedDecorativeNo,
            thumbnailWidth.toInt(),
            thumbnailHeight.toInt(),
            const Color(_lineColor),
          )
          .imageInfo
          .imageData;
    }
    return selectedDecorativeThumbnailData;
  }

  ///
  /// モチーフカスタムパターンの置換
  /// - [customPatternIndex] : 切り替え番号
  /// - [newDecorativeFile] : 切り替えDecorativeFile
  ///
  static AccessError replaceDecorativeCustomPattern(
    int customPatternIndex,
    FileEntity newDecorativeFile,
  ) {
    AccessError accessError = AccessError.none;

    try {
      if (MdcLibrary()
              .apiBinding
              .isMdcPlfFileValid(newDecorativeFile.path)
              .result ==
          false) {
        return AccessError.other;
      }
      newDecorativeFile.copySync(join(
        memorySector.mdcImp.absolutePath,
        _CustomFileInfoList().getFileNameByIndex(customPatternIndex),
      ));
    } on IOException catch (e) {
      accessError = memoryExceptionToAccessError(e);
    }

    return accessError;
  }

  ///
  /// インポートしたユーザー作成模様(デコフィル)の格納場所、件数をセットする
  ///
  void setMdcImportFileList() {
    MdcImportFileList fileListInfo = MemoryModel().fileListInfo;
    List<String> fileList = [];

    for (int i = 0; i < _CustomFileInfoList.maxFileCount; i++) {
      final String path = _CustomFileInfoList().getFilePathByIndex(i);
      final FileEntity importFile = FileEntity(path);
      if (importFile.existsSync()) {
        fileList.add(importFile.path);
      }
    }

    fileListInfo = fileListInfo.copyWith(
      decoImportNum: fileList.length,
      plfFileList: fileList,
    );

    /// model更新
    MemoryModel().fileListInfo = fileListInfo;

    MdcLibrary().apiBinding.setMdcImportFileList(fileListInfo);
    _getUsingDataInfo();
  }

  ///
  /// 指定IndexのフラグをTrueになります
  ///
  void markFileChangedByIndex(int index) =>
      _CustomFileInfoList().markFileChangedByIndex(index);

  ///
  /// 指定Indexのフラグ取得する
  ///
  bool hasAnyFileChanged() => _CustomFileInfoList().hasAnyFileChanged();

  ///
  /// 全部フラグをfalseになります
  ///
  void clearFileChangedMark() => _CustomFileInfoList().clearFileChangedMark();

  ///
  /// 線の色
  ///
  static const int _lineColor = 0x00102070;
}

///
/// ユーザー作成ファイル情報
///
class _CustomFileInfoList {
  _CustomFileInfoList._internal();

  factory _CustomFileInfoList() => _instance;
  static final _CustomFileInfoList _instance = _CustomFileInfoList._internal();

  ///
  ///  モチーフカスタムインポートファイル名リスト
  ///
  final List<Map<String, String>> _fileInfoList = [
    {'fileName': 'Import_DecoFill_001.plf', 'isChanged': 'false'},
    {'fileName': 'Import_DecoFill_002.plf', 'isChanged': 'false'},
    {'fileName': 'Import_DecoFill_003.plf', 'isChanged': 'false'},
    {'fileName': 'Import_DecoFill_004.plf', 'isChanged': 'false'},
    {'fileName': 'Import_DecoFill_005.plf', 'isChanged': 'false'},
    {'fileName': 'Import_DecoFill_006.plf', 'isChanged': 'false'},
    {'fileName': 'Import_DecoFill_007.plf', 'isChanged': 'false'},
    {'fileName': 'Import_DecoFill_008.plf', 'isChanged': 'false'},
    {'fileName': 'Import_DecoFill_009.plf', 'isChanged': 'false'},
    {'fileName': 'Import_DecoFill_010.plf', 'isChanged': 'false'},
    {'fileName': 'Import_DecoFill_011.plf', 'isChanged': 'false'},
    {'fileName': 'Import_DecoFill_012.plf', 'isChanged': 'false'},
  ];

  ///
  /// 最大12個
  ///
  static const int maxFileCount = 12;

  ///
  /// 差し替えられる模様は使用中かどうか
  ///
  bool hasAnyFileChanged() {
    for (int i = 0; i < maxFileCount; i++) {
      final String changedString = _fileInfoList[i]['isChanged'] ?? 'false';
      final isChanged = bool.tryParse(changedString) ?? false;
      if (isChanged == false) {
        continue;
      }

      final int decoNo = i + 1;
      final bool patternUsing =
          DecorativePopupModel().checkCustomPatternUse(decoNo);
      if (patternUsing) {
        return true;
      }
    }

    return false;
  }

  ///
  /// 指定IndexのフラグをTrueになります
  ///
  void markFileChangedByIndex(int index) {
    if (index < 0 || index > maxFileCount) {
      Log.assertTrace('Unknown index');
      return;
    }

    _fileInfoList[index]['isChanged'] = 'true';
  }

  ///
  /// 全部フラグをfalseになります
  ///
  void clearFileChangedMark() {
    for (int i = 0; i < maxFileCount; i++) {
      _fileInfoList[i]['isChanged'] = 'false';
    }
  }

  ///
  /// 指定Indexのファイルのパスを取得する
  ///
  String getFilePathByIndex(int index) {
    if (index < 0 || index > maxFileCount) {
      Log.assertTrace('Unknown file');
      return '';
    }

    final String? fileName = _fileInfoList[index]['fileName'];
    if (fileName == null) {
      Log.assertTrace('Unknown file');
      return '';
    }

    return join(memorySector.mdcImp.absolutePath, fileName);
  }

  ///
  /// 指定Indexのファイル名を取得する
  ///
  String getFileNameByIndex(int index) {
    if (index < 0 || index > maxFileCount) {
      Log.assertTrace('Unknown file');
      return '';
    }

    final String? fileName = _fileInfoList[index]['fileName'];
    if (fileName == null) {
      Log.assertTrace('Unknown file');
      return '';
    }

    return fileName;
  }
}
