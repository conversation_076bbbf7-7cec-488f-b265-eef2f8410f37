import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'spacing_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class SpacingPopupState with _$SpacingPopupState {
  const factory SpacingPopupState({
    required bool isSpacingMinusToLimit,
    required bool isSpacingPlusToLimit,
    required String spacingInputValue,
    required bool spacingDisplayTextStyle,
  }) = _SpacingPopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class SpacingPopupStateViewInterface
    extends ViewModel<SpacingPopupState> {
  SpacingPopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool miniSpacing(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool plusSpacing(bool isLongPress);

  ///
  /// チェーンステッチのデフォルト値
  ///
  int get defaultValue;

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
