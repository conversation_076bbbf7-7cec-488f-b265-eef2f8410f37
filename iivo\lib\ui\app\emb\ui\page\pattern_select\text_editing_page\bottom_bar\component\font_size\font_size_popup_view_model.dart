import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../../../global_popup/global_popups/err_emb_too_much_selected_go_menu/err_emb_too_much_selected_go_menu_view_model.dart';
import '../../../../../../../model/key_board_font_model.dart';
import '../../../text_editing_page_view_model.dart';
import 'font_size_popup_view_interface.dart';

final fontSizePopupViewModelProvider = StateNotifierProvider.autoDispose<
    FontSizePopupViewInterface, FontSizePopupState>(
  (ref) => FontSizePopupViewModel(ref),
);

class FontSizePopupViewModel extends FontSizePopupViewInterface {
  FontSizePopupViewModel(Ref ref) : super(const FontSizePopupState(), ref) {
    /// View更新
    update();
  }
  @override
  void onOkButtonClicked(BuildContext context) {
    /// view更新
    PopupNavigator.pop(context: context);
  }

  @override
  bool onSizeEnlargeButtonClick(bool isLongPress) {
    final EmbCharLibraryError error = KeyBoardFontModel().enlargeCharSize();
    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      if (error == EmbCharLibraryError.EMB_DATAOVER_ERR) {
        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
            arguments: ErrEmbTooMuchSelectedGoMenuArgument(
              onOKButtonClicked: GlobalPopupRoute().resetErrorState,
            ));
        return false;
      }
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    } else {
      /// do nothing
    }

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.sizePopup);
    return true;
  }

  @override
  bool onSizeReduceButtonClick(bool isLongPress) {
    final EmbCharLibraryError error = KeyBoardFontModel().reduceCharSize();
    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      if (error == EmbCharLibraryError.EMB_DATAOVER_ERR) {
        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
            arguments: ErrEmbTooMuchSelectedGoMenuArgument(
              onOKButtonClicked: GlobalPopupRoute().resetErrorState,
            ));
        return false;
      }
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    } else {
      /// do nothing
    }

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.sizePopup);
    return true;
  }

  @override
  bool onHightEnlargeButtonClick(bool isLongPress) {
    final EmbCharLibraryError error = KeyBoardFontModel().enlargeCharHight();
    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      if (error == EmbCharLibraryError.EMB_DATAOVER_ERR) {
        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
            arguments: ErrEmbTooMuchSelectedGoMenuArgument(
              onOKButtonClicked: GlobalPopupRoute().resetErrorState,
            ));
        return false;
      }
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    } else {
      /// do nothing
    }

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.sizePopup);
    return true;
  }

  @override
  bool onHightReduceButtonClick(bool isLongPress) {
    final EmbCharLibraryError error = KeyBoardFontModel().reduceCharHight();
    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      if (error == EmbCharLibraryError.EMB_DATAOVER_ERR) {
        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
            arguments: ErrEmbTooMuchSelectedGoMenuArgument(
              onOKButtonClicked: GlobalPopupRoute().resetErrorState,
            ));
        return false;
      }
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    } else {
      /// do nothing
    }

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.sizePopup);
    return true;
  }

  @override
  bool onWidthEnlargeButtonClick(bool isLongPress) {
    final EmbCharLibraryError error = KeyBoardFontModel().enlargeCharWidth();
    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      if (error == EmbCharLibraryError.EMB_DATAOVER_ERR) {
        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
            arguments: ErrEmbTooMuchSelectedGoMenuArgument(
              onOKButtonClicked: GlobalPopupRoute().resetErrorState,
            ));
        return false;
      }
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    } else {
      /// do nothing
    }

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.sizePopup);
    return true;
  }

  @override
  bool onWidthReduceButtonClick(bool isLongPress) {
    final EmbCharLibraryError error = KeyBoardFontModel().reduceCharWidth();
    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      if (error == EmbCharLibraryError.EMB_DATAOVER_ERR) {
        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
            arguments: ErrEmbTooMuchSelectedGoMenuArgument(
              onOKButtonClicked: GlobalPopupRoute().resetErrorState,
            ));
        return false;
      }
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    } else {
      /// do nothing
    }

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.sizePopup);
    return true;
  }

  @override
  void onSizeResetButtonClick() {
    final EmbCharLibraryError error = KeyBoardFontModel().resetCharSize();
    if (error != EmbCharLibraryError.EMB_NO_ERR) {
      if (error == EmbCharLibraryError.EMB_DATAOVER_ERR) {
        GlobalPopupRoute().updateErrorState(
            nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
            arguments: ErrEmbTooMuchSelectedGoMenuArgument(
              onOKButtonClicked: GlobalPopupRoute().resetErrorState,
            ));
        return;
      }
      return;
    }

    /// 他の画面を更新する
    ref
        .read(textEditingPageViewModelProvider.notifier)
        .updateTextEditingPageByChild(TextEditingType.sizePopup);
  }
}
