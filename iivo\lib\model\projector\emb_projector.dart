part of '../projector_model.dart';

typedef ProjectorInitType = _ProjectorInitType;

class EmbProjector {
  ///
  /// プロジェクト起動しましたが？
  ///
  bool isEmbProjectorOpened() => _ProjectorFunction().isProjectorOpen();

  ///
  ///  W押え金のプロジェクターを開く
  ///
  bool isEmbWFooterProjectorOpen() =>
      _ProjectorFunction().isWFooterProjectorOpen();

  ///
  /// プロジェクターを開く
  /// ★★★プロジェクト起動/停止は非同期です
  ///
  Future<void> openEmbProjector({required ProjectorInitType initType}) async =>
      await _ProjectorFunction().openProjector(initType);

  ///
  /// Embプロジェクション UI がオンになっているかどうか
  ///
  bool get isEmbCameraPenUiProjectorOn {
    final int cameraUiState = TpdLibrary()
        .apiBinding
        .bPIFGetAppDisplayEmb()
        .embSetting
        .ref
        .embCameraUiProjectionOnOffState;
    if (cameraUiState == CameraUISetting.EMBCAMERAUI_PROJECTION_ON.index ||
        cameraUiState ==
            CameraUISetting.EMBCAMERAUI_PROJECTION_ON_DISABLE.index) {
      return true;
    }

    return false;
  }

  ///
  /// EmbカメラペンUIが使用できる枠が付いているか確認する
  ///
  bool isEmbCameraPenUiProjectorValid() {
    final int cameraUiState = TpdLibrary()
        .apiBinding
        .bPIFGetAppDisplayEmb()
        .embSetting
        .ref
        .embCameraUiProjectionOnOffState;
    if (cameraUiState ==
            CameraUISetting.EMBCAMERAUI_PROJECTION_OFF_DISABLE.index ||
        cameraUiState ==
            CameraUISetting.EMBCAMERAUI_PROJECTION_ON_DISABLE.index) {
      return false;
    }

    return true;
  }

  ///
  /// Projectorの更新を一時停止開始
  ///
  void temporaryStopProjectorStart() {
    Log.hello("temporaryStopProjectorStart");
    if (isEmbProjectorOpened()) {
      CameraProjectLibrary().apiBinding.projectorOff();
    } else {
      Log.i(
          tag: "temporaryStopProjector",
          description: "EmbProjector is not Open");
    }
  }

  ///
  /// Projectorの更新を一時停止終了
  ///
  void temporaryStopProjectorStop() {
    Log.hello("temporaryStopProjectorStop");
    if (isEmbProjectorOpened()) {
      ProjectorModel().embProjector.refreshEmbProjector(autoBacklight: true);
    } else {
      Log.i(
          tag: "temporaryStopProjector",
          description: "EmbProjector is already Close");
    }
  }

  ///
  /// プロジェクターを閉じる
  /// ★★★プロジェクト起動/停止は非同期です
  ///
  /// 戻る値は起動閉じるを判断できます
  ///
  Future<void> closeEmbProjector() async =>
      await _ProjectorFunction().closeProjector();

  ///
  /// プロジェクト画面描画内容更新
  ///
  /// - [redrawEmbPattern] : true 模様投影された時に再描画（再描画が性能が悪い）
  /// - [onlyBottom] : true カメラマンUI描画します(できればカメラマン起動の時にのみ)
  /// - [onlyRefresh] : true 何もしない、画面更新だけです（プロジェクト画面内容別ところで準備の時に使う）
  /// - [autoBacklight] : true 画面描画更新の時に画面バックライトがオフの場合はオンになる
  /// - [isQuilt] : true quilt模様の投影画面更新(カメラマン起動した間にのみ)
  ///
  Future<void> refreshEmbProjector({
    bool redrawEmbPattern = false,
    bool onlyBottom = false,
    bool onlyRefresh = false,
    bool autoBacklight = true,
    bool isQuilt = false,
    bool isWFoot = false,
  }) async =>
      await _ProjectorFunction().refreshProjector(
        projectorMode: isQuilt
            ? ProjectorMode.projectorModeEmbQuilt
            : ProjectorMode.projectorModeEmb,
        redrawEmbPattern: redrawEmbPattern,
        onlyBottom: onlyBottom,
        onlyRefresh: onlyRefresh,
        autoBacklight: autoBacklight,
        isWFoot: isWFoot,
      );

  ///
  /// 画面プロジェクトボタン表示の状態
  ///
  /// false-未選択
  /// true-選択しました
  ///
  bool isEmbProjectorViewOpen = false;

  /// 画面表示状態：メモリポップアップは開いていますか
  bool isMemoryPopOpen = false;

  /// 画面表示状態：Projector Settingsポップアップは開いていますか
  bool isProjectorSettingsPopOpen = false;

  /// 画面表示状态：Background Colorポップアップは開いていますか
  bool isBackgroundColorPopOpen = false;

  /// 取得現在のProjectorの種類
  ProjectorInitType? get getProjectorInitType =>
      _ProjectorFunction()._projectorCurrentType;
}
