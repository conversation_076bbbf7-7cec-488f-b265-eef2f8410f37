﻿import 'package:panel_library/panel_library.dart';

import '../../../../network/network.dart';
import '../../../../network/upgrade/upgrade_info/serialize_new/upgrade_info.dart';
import '../../updateapp/model/upgrade_model.dart';

///
///ダウンロードステータスクラス
///
enum UpdateType {
  ///最新のインストールパッケージがインストールされました
  installed,

  ///新しいバージョンが検出されました
  pressLoad,

  ///ダウンロード中
  downloading,

  ///ダウンロード完了
  available,
}

///
/// アップグレードファイルのダウンロード状況
///
enum WlanUpgradeDownloadState {
  //不明、デフォルト状態
  unknown,

  /// 新しいバージョンなし
  idleNoUpdate,

  /// ダウンロードが開始されていません、新しいバージョンがあります
  idleHasUpdate,

  /// ダウンロードしている
  downloading,

  /// ダウンロードが開始されましたが一時停止しています
  paused,

  /// ダウンロード完了
  completed,

  /// ダウンロード失敗
  failed,

  /// ストレージ容量不足のため、ダウンロードに失敗しました
  failedMemoryFull;
}

class NetworkSettingPage2Model {
  NetworkSettingPage2Model._internal();

  factory NetworkSettingPage2Model() => _instance;
  static final NetworkSettingPage2Model _instance =
      NetworkSettingPage2Model._internal();

  ///
  /// 現在のダウンロード状況
  ///
  WlanUpgradeDownloadState downloadState = WlanUpgradeDownloadState.unknown;

  ///
  /// 現在のダウンロードの進行状況値
  ///
  int downloadingProgress = 0;

  ///
  /// ネットワーク取得バージョン
  /// - 戻る：　"4.0.0"
  ///
  String nowVersion = UpgradeModel().version;

  ///
  /// ネットワーク取得バージョン
  /// - 戻る：　"x.xx.x"
  ///
  String get upgradeVersion =>
      DeviceLibrary().apiBinding.getUpgradeVersion().value;

  ///
  /// アップグレード版
  /// [value] :upgradeVersion値を設定する
  ///
  void setUpgradeVersion(String upgradeVersion) =>
      DeviceLibrary().apiBinding.setUpgradeVersion(upgradeVersion);

  ///
  /// モデルに一致するネットワーク上の最新データ情報
  ///
  Future<UpgradeInfo?> getUpgradeInfo() =>
      Upgrade().getUpgradeVersionInfoInServer();
}
