import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'line_e_stitch_spacing_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class LineEStitchSpaceState with _$LineEStitchSpaceState {
  const factory LineEStitchSpaceState({
    required String spaceDisplayValue,
    required bool isDefaultStyle,
    required bool plusButtonValid,
    required bool minusButtonValid,
  }) = _LineEStitchSpaceState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class LineEStitchSpaceStateViewInterface
    extends ViewModel<LineEStitchSpaceState> {
  LineEStitchSpaceStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// マイナスボタンをクリックする
  ///
  bool onMinusButtonClicked(bool isLongPress);

  ///
  /// プラスボタンをクリックする
  ///
  bool onPlusButtonClicked(bool isLongPress);

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;

  ///
  /// 最大幅値
  ///
  int get maxSpaceValue;

  ///
  /// 最大幅値
  ///
  int get minSpaceValue;
}
