import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../component/emb_footer/emb_footer.dart';
import '../../../../../../component/emb_header/emb_header.dart';
import 'component/snowman_pattern.dart';
import 'snowman_interface.dart';
import 'snowman_view_model.dart';

class Snowman extends ConsumerStatefulWidget {
  const Snowman({super.key});

  @override
  ConsumerState<Snowman> createState() => _SnowmanState();
}

class _SnowmanState extends ConsumerState<Snowman> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(snowmanViewModelProvider);
    final viewModel = ref.read(snowmanViewModelProvider.notifier);
    const double redBorderWidth = 1;
    const double snowmanMarkWidth = 59;

    return Stack(children: [
      Column(
        children: [
          const Expanded(flex: 71, child: EmbHeader()),
          const Spacer(
            flex: 8,
          ),
          Expanded(
              flex: 1132,
              child: Row(
                children: [
                  const Spacer(
                    flex: 6,
                  ),
                  Expanded(
                      flex: 788,
                      child: Stack(
                        children: [
                          const pic_popup_size788x1132(),
                          Column(
                            children: [
                              const Spacer(
                                flex: 163,
                              ),
                              Expanded(
                                  flex: 80,
                                  child: Row(
                                    children: [
                                      const Spacer(
                                        flex: 37,
                                      ),
                                      Expanded(
                                          flex: 84,
                                          child: grp_btn_embsnowman01(
                                              onTap: () => viewModel
                                                  .onMarkPositionButtonClicked(
                                                      MarkDirection.leftUp),
                                              state: state.markDirection ==
                                                      MarkDirection.leftUp
                                                  ? ButtonState.select
                                                  : ButtonState.normal)),
                                      const Spacer(
                                        flex: 231,
                                      ),
                                      Expanded(
                                          flex: 84,
                                          child: grp_btn_embsnowman02(
                                              onTap: () => viewModel
                                                  .onMarkPositionButtonClicked(
                                                      MarkDirection.up),
                                              state: state.markDirection ==
                                                      MarkDirection.up
                                                  ? ButtonState.select
                                                  : ButtonState.normal)),
                                      const Spacer(
                                        flex: 231,
                                      ),
                                      Expanded(
                                          flex: 84,
                                          child: grp_btn_embsnowman03(
                                              onTap: () => viewModel
                                                  .onMarkPositionButtonClicked(
                                                      MarkDirection.rightUp),
                                              state: state.markDirection ==
                                                      MarkDirection.rightUp
                                                  ? ButtonState.select
                                                  : ButtonState.normal)),
                                      const Spacer(
                                        flex: 37,
                                      )
                                    ],
                                  )),
                              const Spacer(
                                flex: 228,
                              ),
                              Expanded(
                                  flex: 80,
                                  child: Row(
                                    children: [
                                      const Spacer(
                                        flex: 37,
                                      ),
                                      Expanded(
                                          flex: 84,
                                          child: grp_btn_embsnowman04(
                                              onTap: () => viewModel
                                                  .onMarkPositionButtonClicked(
                                                      MarkDirection.left),
                                              state: state.markDirection ==
                                                      MarkDirection.left
                                                  ? ButtonState.select
                                                  : ButtonState.normal)),
                                      const Spacer(
                                        flex: 231,
                                      ),
                                      const Spacer(
                                        flex: 84,
                                      ),
                                      const Spacer(
                                        flex: 231,
                                      ),
                                      Expanded(
                                          flex: 84,
                                          child: grp_btn_embsnowman06(
                                              onTap: () => viewModel
                                                  .onMarkPositionButtonClicked(
                                                      MarkDirection.right),
                                              state: state.markDirection ==
                                                      MarkDirection.right
                                                  ? ButtonState.select
                                                  : ButtonState.normal)),
                                      const Spacer(
                                        flex: 37,
                                      )
                                    ],
                                  )),
                              const Spacer(
                                flex: 228,
                              ),
                              Expanded(
                                  flex: 80,
                                  child: Row(
                                    children: [
                                      const Spacer(
                                        flex: 37,
                                      ),
                                      Expanded(
                                          flex: 84,
                                          child: grp_btn_embsnowman07(
                                              onTap: () => viewModel
                                                  .onMarkPositionButtonClicked(
                                                      MarkDirection.leftDown),
                                              state: state.markDirection ==
                                                      MarkDirection.leftDown
                                                  ? ButtonState.select
                                                  : ButtonState.normal)),
                                      const Spacer(
                                        flex: 231,
                                      ),
                                      Expanded(
                                          flex: 84,
                                          child: grp_btn_embsnowman08(
                                              onTap: () => viewModel
                                                  .onMarkPositionButtonClicked(
                                                      MarkDirection.down),
                                              state: state.markDirection ==
                                                      MarkDirection.down
                                                  ? ButtonState.select
                                                  : ButtonState.normal)),
                                      const Spacer(
                                        flex: 231,
                                      ),
                                      Expanded(
                                          flex: 84,
                                          child: grp_btn_embsnowman09(
                                              onTap: () => viewModel
                                                  .onMarkPositionButtonClicked(
                                                      MarkDirection.rightDown),
                                              state: state.markDirection ==
                                                      MarkDirection.rightDown
                                                  ? ButtonState.select
                                                  : ButtonState.normal)),
                                      const Spacer(
                                        flex: 37,
                                      )
                                    ],
                                  )),
                              const Spacer(
                                flex: 32,
                              ),
                              Expanded(
                                  flex: 80,
                                  child: Row(
                                    children: [
                                      const Spacer(
                                        flex: 352,
                                      ),
                                      Expanded(
                                          flex: 84,
                                          child: grp_btn_embsnowman05(
                                              onTap: () => viewModel
                                                  .onMarkPositionButtonClicked(
                                                      MarkDirection.center),
                                              state: state.markDirection ==
                                                      MarkDirection.center
                                                  ? ButtonState.select
                                                  : ButtonState.normal)),
                                      const Spacer(
                                        flex: 352,
                                      )
                                    ],
                                  )),
                              const Spacer(
                                flex: 69,
                              ),
                              Expanded(
                                  flex: 80,
                                  child: Row(
                                    children: [
                                      const Spacer(
                                        flex: 18,
                                      ),
                                      Expanded(
                                          flex: 152,
                                          child: Stack(
                                            fit: StackFit.expand,
                                            children: [
                                              grp_btn_negative(
                                                text: l10n.icon_cancel,
                                                onTap: () => viewModel
                                                    .onCancelButtonClicked(
                                                        context),
                                              )
                                            ],
                                          )),
                                      const Spacer(
                                        flex: 460,
                                      ),
                                      Expanded(
                                          flex: 152,
                                          child: Stack(
                                            fit: StackFit.expand,
                                            children: [
                                              grp_btn_positive(
                                                feedBackControl: null,
                                                text: l10n.icon_00088,
                                                onTap: () => viewModel
                                                    .onScanButtonClicked(),
                                              )
                                            ],
                                          )),
                                      const Spacer(
                                        flex: 12,
                                      ),
                                    ],
                                  )),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ],
                      )),
                  const Spacer(
                    flex: 6,
                  )
                ],
              )),
          const Spacer(
            flex: 8,
          ),
          const Expanded(flex: 61, child: EmbFooter()),
        ],
      ),

      /// 画像の枠線表示
      state.patternWidth == null || state.patternHeight == null
          ? Container()
          : Column(
              children: [
                const Spacer(
                  flex: 334,
                ),
                Expanded(
                    flex: 521,
                    child: Row(
                      children: [
                        const Spacer(
                          flex: 139,
                        ),
                        Expanded(
                          flex: 521,
                          child: Center(
                            child: Center(
                              child: Container(
                                width: state.patternWidth! + redBorderWidth,
                                height: state.patternHeight! + redBorderWidth,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      width: redBorderWidth,
                                      color:
                                          const Color.fromARGB(255, 240, 0, 0)),
                                ),
                                child: Container(),
                              ),
                            ),
                          ),
                        ),
                        const Spacer(
                          flex: 140,
                        ),
                      ],
                    )),
                const Spacer(
                  flex: 425,
                ),
              ],
            ),

      /// 画像表示
      IgnorePointer(
        child: SnowmanPattern(
          controller: viewModel.snowmanController,
        ),
      ),

      /// スノーマンマーク
      state.patternWidth == null || state.patternHeight == null
          ? Container()
          : Column(
              children: [
                const Spacer(
                  flex: 334,
                ),
                Expanded(
                    flex: 521,
                    child: Row(
                      children: [
                        const Spacer(
                          flex: 139,
                        ),
                        Expanded(
                          flex: 521,
                          child: Center(
                            child: Container(
                              width: state.patternWidth! + snowmanMarkWidth,
                              height: state.patternHeight! + snowmanMarkWidth,
                              alignment: [
                                Alignment.center,
                                Alignment.topLeft,
                                Alignment.topCenter,
                                Alignment.topRight,
                                Alignment.centerLeft,
                                Alignment.centerRight,
                                Alignment.bottomLeft,
                                Alignment.bottomCenter,
                                Alignment.bottomRight,
                              ][state.markDirection.index],
                              child: const ico_snowmanposition(),
                            ),
                          ),
                        ),
                        const Spacer(
                          flex: 140,
                        ),
                      ],
                    )),
                const Spacer(
                  flex: 425,
                ),
              ],
            ),
    ]);
  }
}
