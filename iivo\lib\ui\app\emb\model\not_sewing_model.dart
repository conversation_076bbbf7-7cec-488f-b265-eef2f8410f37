import 'dart:math';

import 'package:flutter/material.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../model/thread_color_model.dart';
import 'pattern_model.dart';
import 'thread_color_list_model.dart';
import 'thread_model.dart';

///
/// patternの位置とサイズ
///
class PositionInfo {
  PositionInfo({
    required this.topLeft,
    required this.topRight,
    required this.bottomLeft,
    required this.bottomRight,
  });
  Point<double> topLeft, topRight, bottomLeft, bottomRight;

  List<double> get _xList =>
      [topLeft.x, topRight.x, bottomLeft.x, bottomRight.x];
  List<double> get _yList =>
      [topLeft.y, topRight.y, bottomLeft.y, bottomRight.y];

  /// 画像の最上部の座標を取得する
  double get topBorder {
    _xList.sort();
    return _xList.first;
  }

  /// 画像の一番下の座標を取得
  double get bottomBorder {
    _xList.sort();
    return _xList.last;
  }

  /// 画像の左端の座標を取得
  double get leftBorder {
    _yList.sort();
    return _yList.first;
  }

  /// 画像の右端の座標を取得
  double get rightBorder {
    _yList.sort();
    return _yList.last;
  }
}

///
/// 「NoSewing」の画面情報を設定する
///
class NotSewingModel {
  NotSewingModel._internal();
  factory NotSewingModel() {
    if (_instance._isInitialized == false) {
      _instance._isInitialized = true;
    }
    return _instance;
  }
  bool _isInitialized = false;
  static final NotSewingModel _instance = NotSewingModel._internal();

  ///
  /// サムネイルの取得
  ///
  Image getThumbnailImage({required int selectedPatternIndex}) {
    List<EmbGroup> groupList = PatternModel().getAllGroupInCurrentPattern();

    return Image.memory(groupList[selectedPatternIndex]
        .subImage(ScrollCenterType.IMAGE_EDITING, 100));
  }

  ///
  /// Colorをクリックしたときにlibが選択したPatternと線色を通知する
  ///
  void selectColor(
      {required int selectedPatternIndex,
      required int selectedColorItemIndex}) {
    List<EmbGroup> groupList = PatternModel().getAllGroupInCurrentPattern();

    /// Lib更新
    EmbLibrary().apiBinding.selectPartsInGroupColorChange(
        groupList[selectedPatternIndex].handle, selectedColorItemIndex);
  }

  ///
  ///「NoSewing」設定の変更
  ///
  void changeSettingsSewing(
      {required int patternIndex,
      required int colorIndex,
      required bool notSewing}) {
    Pattern currentPattern = PatternModel().getCurrentPattern();

    EmbGroup pattern =
        PatternModel().getAllGroupInCurrentPattern()[patternIndex];

    EmbLibrary().apiBinding.changeSettingsSewing(
          (currentPattern is EmbBorder) ? null : pattern.handle,
          colorIndex,
          notSewing,
        );

    /// ThreadInfoを変更し、キャッシュされたデータを消去し、再取得します
    if (currentPattern is EmbBorder && currentPattern.isRepeatBorder) {
      currentPattern.clearThreadInfoCache();
      currentPattern.clearGroupImageCache();
      currentPattern.clearGroupInfoCache();
    } else {
      pattern.clearThreadInfoCache();
      pattern.clearMainImageCache();
      pattern.clearGroupInfoCache();
    }
  }

  ///
  /// 表示用の線色情報と画像の取得
  ///
  List<ColorListDisplayInfo> getDisplayList({
    required int selectedPatternIndex,
    required int selectedColorItemIndex,
    required int zoomScale,
  }) {
    List<EmbGroup> groupList = PatternModel().getAllGroupInCurrentPattern();
    List<ColorListDisplayInfo> displayList = [];

    for (int index = 0; index < groupList.length; index++) {
      List<ThreadColorDispInfo> displayThreadInfoList =
          _getThreadInfoDisplayList(
        threadInfoList: groupList[index].threadInfo,
        isSelectedPattern: selectedPatternIndex == index,
        selectedColorItemIndex: selectedColorItemIndex,
      );

      /// 線の色リストのサムネイル画像
      final Widget patternImage =
          ThreadColorListModel().getEmbGroupThreadImageList(
        groupList[index],
        ScrollCenterType.IMAGE_EDITING,
        zoomScale: zoomScale,
      );

      displayList.add(ColorListDisplayInfo(
          patternImage: patternImage,
          threadInfoDisplayList: displayThreadInfoList));
    }

    return displayList;
  }

  ///
  /// 表示用の線色情報の取得
  ///
  List<ThreadColorDispInfo> _getThreadInfoDisplayList(
      {required List<ThreadInfo> threadInfoList,
      required bool isSelectedPattern,
      required int selectedColorItemIndex}) {
    List<ThreadColorDispInfo> displayThreadInfoList = [];
    bool isThreadColorDefault = ThreadModel.getThreadColor();

    for (int index = 0; index < threadInfoList.length; index++) {
      displayThreadInfoList.add(ThreadColorDispInfo(
        isSelected: isSelectedPattern && selectedColorItemIndex == index,
        isThreadNotSewing: threadInfoList[index].notSewing,
        treadColor: threadInfoList[index].colorRGB,
        threadCode: ThreadModel.getThreadCode(threadInfoList[index].threadCode,
            threadInfoList[index].threadCodeDigit, isThreadColorDefault),
        threadBrandName: _getThreadBrandName(threadInfoList[index].brandCode,
            threadInfoList[index].threadCode, isThreadColorDefault),
        threadColorName: _getThreadColorName(threadInfoList[index].index300,
            threadInfoList[index].threadCode, isThreadColorDefault),
        threadSewingTime: PatternModel()
            .changeLibSewingTimeToUiSewingTime(threadInfoList[index].sewingTime)
            .toString(),
        appliqueIcon: ThreadModel.getAppliqueIcon(
          threadInfoList[index].threadCode,
          false,
          isThreadColorDefault,
        ),
      ));
    }

    return displayThreadInfoList;
  }

  ///
  /// ブランド名の取得
  ///
  String _getThreadBrandName(
      int brandCode, int threadCode, bool isThreadColorDefault) {
    if (isThreadColorDefault == false ||
        ThreadModel.isAppliqueThreadCode(threadCode)) {
      return '';
    }

    ThreadBrandName threadBrandName =
        ThreadBrandName.getValuesByNumber(brandCode);

    return ThreadColorModel().getThreadBrandName(threadBrandName);
  }

  ///
  /// カラー名の取得
  ///
  String _getThreadColorName(
      int index300, int threadCode, bool isThreadColorDefault) {
    if (isThreadColorDefault == true) {
      return '';
    }

    return ThreadColorModel().getThreadColorNameWithIndex300(index300);
  }
}
