import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'spacing_popup_view_interface.dart';
import 'spacing_popup_view_model.dart';

class SpacingPopup extends ConsumerStatefulWidget {
  const SpacingPopup({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState<SpacingPopup> createState() => _SpacingState();
}

class _SpacingState extends ConsumerState<SpacingPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(spacingViewModelProvider);
    final viewModel = ref.read(spacingViewModelProvider.notifier);

    return Row(
      children: [
        const Spacer(
          flex: 571,
        ),
        Expanded(
          flex: 229,
          child: Column(
            children: [
              const Spacer(
                flex: 159,
              ),
              Expanded(
                flex: 1052,
                child: Scaffold(
                  backgroundColor: Colors.transparent,
                  body: pre_edit_toolbar_mdc(
                    child: Column(
                      children: [
                        const Spacer(
                          flex: 37,
                        ),
                        const Expanded(
                          flex: 68,
                          child: Row(
                            children: [
                              Spacer(
                                flex: 52,
                              ),
                              Expanded(
                                flex: 126,
                                child: ico_mdcstitch_stip_spacing(),
                              ),
                              Spacer(
                                flex: 51,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 24,
                        ),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_str_spacing(
                                  text: l10n.icon_00547,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 8,
                        ),
                        Expanded(
                          flex: 69,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_str_parameter(
                                  text: state.spacingInputValue,
                                  isDefault: state.spacingDisplayTextStyle,
                                  displayText:
                                      viewModel.currentSelectedUnit == Unit.mm
                                          ? l10n.icon_00225
                                          : l10n.icon_00226,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 12,
                        ),
                        Expanded(
                          flex: 63,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 48,
                              ),
                              Expanded(
                                flex: 63,
                                child: grp_btn_minus_01(
                                  onTap: () => viewModel.miniSpacing(false),
                                  onLongPress: () =>
                                      viewModel.miniSpacing(true),
                                  state: state.isSpacingMinusToLimit
                                      ? ButtonState.disable
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 10,
                              ),
                              Expanded(
                                flex: 63,
                                child: grp_btn_plus_01(
                                  onTap: () => viewModel.plusSpacing(false),
                                  onLongPress: () =>
                                      viewModel.plusSpacing(true),
                                  state: state.isSpacingPlusToLimit
                                      ? ButtonState.disable
                                      : ButtonState.normal,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 47,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 620,
                        ),
                        Expanded(
                          flex: 70,
                          child: Row(
                            children: [
                              const Spacer(
                                flex: 12,
                              ),
                              Expanded(
                                flex: 205,
                                child: grp_btn_positive_mdc(
                                  onTap: viewModel.onOkButtonClicked,
                                  text: l10n.icon_ok,
                                  feedBackControl: null,
                                ),
                              ),
                              const Spacer(
                                flex: 12,
                              ),
                            ],
                          ),
                        ),
                        const Spacer(
                          flex: 12,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const Spacer(
                flex: 69,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
