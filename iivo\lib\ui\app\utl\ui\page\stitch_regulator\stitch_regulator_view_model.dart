import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/device_memory_model.dart';
import '../../../../../../model/machine_config_model.dart';
import '../../../../../../model/projector/camera_pen/camera_pen_type.dart';
import '../../../../../../model/projector_model.dart';
import '../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../global_popup/global_popup_export.dart';
import '../../../../../global_popup/global_popup_route.dart';
import '../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../global_popup/global_popups/err_pocket_full/err_pocket_full_view_model.dart';
import '../../../../../global_popup/panel_popup_route.dart';
import '../../../model/guide_line_model.dart';
import '../../../model/pattern_data_reader/stitch_regulator/stitch_regulator_reader.dart';
import '../../../model/pattern_model.dart';
import '../../../model/preview_model.dart';
import '../../../model/sr_manual_memory_model.dart';
import '../../../model/stitch_regulator_model.dart';
import '../../component/utl_header/utl_header_view_model.dart';
import '../component/stop_sewing/stop_sewing_view_model.dart';
import '../sewing/common/guide_line/guide_line.dart';
import '../sewing/common/guide_line/guide_line_view_model.dart';
import '../sewing/common/sewing_setting_buttons/sewing_setting_buttons_view_model.dart';
import '../sewing/utility/parameter_adjustment/parameter_adjustment_view_model.dart';
import '../sewing/utility/preview/preview_view_model.dart';
import 'component/color_select_popup/color_select_popup.dart';
import 'component/manual_memory_popup/manual_memory_popup.dart';
import 'stitch_regulator_view_interface.dart';

enum SRModuleType {
  stitchRegulator,
  parameterAdjustment,
  manualMemory,
  guideLine,
  header,
  sensingLineOnOff,
}

final stitchRegulatorViewInfoProvider =
    AutoDisposeNotifierProvider<_StitchRegulatorViewInfo, void>(
        () => _StitchRegulatorViewInfo());

class _StitchRegulatorViewInfo extends AutoDisposeNotifier<void> {
  @override
  void build() {}

  BuildContext? _context;
  BuildContext get context => _context!;
  set context(value) => _context = value;
}

/// view _modelに必要な構造
final stitchRegulatorViewModeProvider = StateNotifierProvider.autoDispose<
    StitchRegulatorViewModel, StitchRegulatorState>((ref) {
  final context = ref.read(stitchRegulatorViewInfoProvider.notifier).context;
  return StitchRegulatorViewModel(ref, context);
});

class StitchRegulatorViewModel extends StitchRegulatorViewModelInterface {
  StitchRegulatorViewModel(
      AutoDisposeStateNotifierProviderRef ref, BuildContext context)
      : super(const StitchRegulatorState(), ref, context);

  @override
  void build() {
    super.build();

    /// Model更新
    PatternDataModel().stitchRegulatorPatternInit();

    ref.listen(
      appDisplayUtlStateProvider
          .select((value) => value.utlFuncSetting.ref.isUtlSewing),
      (previous, nextState) {
        if (previous == false &&
            nextState == true &&
            MachineConfigModel().currentMode == SettingBaseMode.utl) {
          /// 縫製が始まったら、ストップマスクを開けます
          if (state.srOnOff == true) {
            ref
                .read(stopSewingPopupViewModelProvider.notifier)
                .openStopSewingPopup(
                    isAllClickDisable: true,
                    mode: StopSewingPopupType.stitchRegulator);
          } else {
            ref
                .read(stopSewingPopupViewModelProvider.notifier)
                .openStopSewingPopup(
                    isAllClickDisable: false,
                    mode: StopSewingPopupType.stitchRegulator);
          }
        } else if (previous == true &&
            nextState == false &&
            MachineConfigModel().currentMode == SettingBaseMode.utl) {
          /// 縫製停止、クローズ停止マスキング
          ref
              .read(stopSewingPopupViewModelProvider.notifier)
              .closeStopSewingPopup();
        }
      },
    );

    /// needleNum | isConnectedDF
    ref.listen(
      appDisplayUtlStateProvider.select((value) => (
            value.utlAttribParam.ref.needleNum,
            value.utlFuncSetting.ref.isConnectedDF
          )),
      (previous, nextState) {
        Log.debugTrace("needleNum or isConnectedDF Change");
        state = state.copyWith(
          stitchName: StitchRegulatorModel().getSRPatternName(),
          patternsState: _getDisplayPatternsState(),
          patternList: _getPatternList(),
        );
      },
    );

    /// srPresserFootHeight
    ref.listen(
        fireImmediately: true,
        appDisplayUtlStateProvider.select(
            (value) => value.utlAttribParam.ref.srPressorFootHeight), (_, __) {
      state = state.copyWith(
        srPresserFootHeight: (StitchRegulatorModel().getSrPresserFootHeight() /
                StitchRegulatorModel.conversionRate)
            .toStringAsFixed(1),
      );
    });

    /// isSrPressorFootHeightDefault
    ref.listen(
        fireImmediately: true,
        appDisplayUtlStateProvider.select(
            (value) => value.utlAttribParam.ref.isSrPressorFootHeightDefault),
        (previous, nextState) {
      state = state.copyWith(
        isSrPressorFootHeightDefault: nextState,
      );
    });

    /// isSRModeOn
    ref.listen(
        fireImmediately: true,
        appDisplayUtlStateProvider
            .select((value) => value.utlAttribParam.ref.srOnOff), (_, __) {
      state = state.copyWith(
        srOnOff: StitchRegulatorModel().srOnOff,
        isMemoryEnable: StitchRegulatorModel().srMode == SR_Mode.SR_MODE_NONE &&
                StitchRegulatorModel().srOnOff
            ? false
            : true,
        isResetEnable: StitchRegulatorModel().srMode == SR_Mode.SR_MODE_NONE &&
                StitchRegulatorModel().srOnOff
            ? false
            : true,
      );
    });

    /// srMode
    ref.listen(
        fireImmediately: true,
        appDisplayUtlStateProvider
            .select((value) => value.utlAttribParam.ref.srMode), (_, __) {
      state = state.copyWith(
        srMode: StitchRegulatorModel().srMode,
        isMemoryEnable: StitchRegulatorModel().srMode == SR_Mode.SR_MODE_NONE &&
                StitchRegulatorModel().srOnOff
            ? false
            : true,
        isResetEnable: StitchRegulatorModel().srMode == SR_Mode.SR_MODE_NONE &&
                StitchRegulatorModel().srOnOff
            ? false
            : true,
      );
    });

    /// view更新
    update();

    /// 他の画面を更新します
    Future(() => updateSRModePageByChild(SRModuleType.stitchRegulator));
  }

  @override
  void update() {
    state = state.copyWith(
      guideLineButtonState: _getGuideLineState(),
      recognitionLineButtonState: _getSensingLineState(),
      srStatusButtonState: _getSrStatusState(),
      stitchName: StitchRegulatorModel().getSRPatternName(),
      patternsState: _getDisplayPatternsState(),
      patternList: _getPatternList(),
    );
  }

  @override
  void updatePreview() {
    ref.read(utilityPreviewViewModeProvider.notifier).update();
  }

  @override
  void onSRSwitchButtonClick(bool value) {
    /// 多次クリックによる重複トリガーの問題を解決する
    if (state.srOnOff == value) {
      return;
    }
    if (StitchRegulatorModel().srOnOff == value) {
      return;
    }

    ///SrOff バックアップ
    if (value == false) {
      StitchRegulatorModel().backUpSrMode = StitchRegulatorModel().srMode;
      ProjectorModel().temporaryStopSRSensingAndSRStatusStart();
    } else {
      /// DoNothing
    }

    /// Model更新
    StitchRegulatorModel().srOnOff = value;

    ///SrOff バックアップ
    if (value == true) {
      Log.debugTrace(
          "StitchRegulatorModel().backUpSrMode:${StitchRegulatorModel().backUpSrMode}");
      switch (StitchRegulatorModel().backUpSrMode) {
        case SR_Mode_t.SR_MODE_INTERMITTENT:
          StitchRegulatorModel().setSRModeIntermit();
          break;
        case SR_Mode_t.SR_MODE_CONTINUOUS:
          StitchRegulatorModel().setSRModeContinuous();
          break;
        case SR_Mode_t.SR_MODE_BASTING:
          errWarnDontUseOpenToeFootSrAfterLibFunc = () {
            StitchRegulatorModel().setSRModeBasting();
            _updateUiDisplayPatternSrModeBySrMode(SR_Mode_t.SR_MODE_BASTING);
          };
          StitchRegulatorModel().gotoSRBastingWarning();
          break;
        case SR_Mode_t.SR_MODE_NONE:
          _updateUiDisplayPatternSrModeBySrMode(SR_Mode.SR_MODE_NONE);
          break;
      }
      ProjectorModel().temporaryStopSRSensingAndSRStatusStop();
    } else {
      switch (StitchRegulatorModel().backUpSrMode) {
        case SR_Mode_t.SR_MODE_INTERMITTENT:
        case SR_Mode_t.SR_MODE_CONTINUOUS:
          break;
        case SR_Mode_t.SR_MODE_BASTING:
        case SR_Mode_t.SR_MODE_NONE:
          _updateUiDisplayPatternSrModeBySrMode(SR_Mode.SR_MODE_NONE);
          break;
      }
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// view更新
    update();
  }

  @override
  void onModeButtonClick(SR_Mode srMode) {
    if (StitchRegulatorModel().srOnOff == false ||
        StitchRegulatorModel().srMode == srMode) {
      return;
    }

    /// SR_MODE_BASTING設定する前にSRBastingWarning Popup提示が必要です
    if (srMode == SR_Mode.SR_MODE_BASTING) {
      final gotoSRBastingWarningRet =
          StitchRegulatorModel().gotoSRBastingWarning();
      if (gotoSRBastingWarningRet != DeviceErrorCode.devNoError &&
          gotoSRBastingWarningRet != DeviceErrorCode.devInvalidPanelError) {
        Log.errorTrace("change mode failed[0]");
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return;
      }

      errWarnDontUseOpenToeFootSrAfterLibFunc = () {
        StitchRegulatorModel().setSRModeBasting();
        final currentSrMode = StitchRegulatorModel().srMode;
        if (currentSrMode != SR_Mode.SR_MODE_BASTING) {
          Log.errorTrace("change mode failed[1]");
          return;
        }

        /// 模様登録
        /// 画面更新
        _updateUiDisplayPatternSrModeBySrMode(currentSrMode);
      };
    } else {
      /// Do Nothing
    }

    /// SR_MODE_BASTING以外の設定
    final ret = switch (srMode) {
      SR_Mode_t.SR_MODE_INTERMITTENT =>
        StitchRegulatorModel().setSRModeIntermit(),
      SR_Mode_t.SR_MODE_CONTINUOUS =>
        StitchRegulatorModel().setSRModeContinuous(),
      SR_Mode_t.SR_MODE_BASTING ||
      SR_Mode_t.SR_MODE_NONE =>
        UtlLibraryError.utlNoError,
    };

    if (ret != UtlLibraryError.utlNoError &&
        ret != UtlLibraryError.utlErrorInvalidPanel) {
      Log.errorTrace("change mode failed[2]");
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final currentSrMode = StitchRegulatorModel().srMode;
    if (currentSrMode != srMode) {
      Log.errorTrace("change mode failed[3]");
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 模様登録
    /// 画面更新
    _updateUiDisplayPatternSrModeBySrMode(currentSrMode);
  }

  @override
  void onPatternClick(int index) {
    if (state.patternsState[index] == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// Model更新
    PatternDataModel()
        .loginSRModePattern(state.srMode, index, adjustParam: null);

    /// errorの場合なら
    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    PatternDataModel().isNotPatternFirstTime = true;
    PatternDataModel().currentSelectedPatternIndex = index;

    /// view更新
    state = state.copyWith(
      stitchName: StitchRegulatorModel().getSRPatternName(),
      patternsState: _getDisplayPatternsState(),
    );

    /// 他の画面を更新します
    updateSRModePageByChild(SRModuleType.stitchRegulator);
  }

  @override
  void onGuideLineButtonClick(BuildContext context) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    final bool isGuideLineProjectorOpened =
        ProjectorModel().isUtlProjectorOpened(UtlProjectorType.guideline);

    /// ガイドラインpopup表示中、プロジェクト表示していない
    /// 何もしない
    if ((state.guideLinePopupState == true) &&
        (isGuideLineProjectorOpened == false)) {
      return;
    }

    /// ガイドラインpopup表示中、ガイドラインが投射中場合、
    /// トップバーのガイドラインキーでクリックすると、設定画面をクローズできるようにする
    if ((state.guideLinePopupState == true) &&
        (isGuideLineProjectorOpened == true)) {
      _closeGuideLinePopup(context);
      return;
    }

    /// ガイドラインpopup表示していない、ガイドラインが投射中場合、
    /// ガイドラインpopupを表示する
    if ((state.guideLinePopupState == false) &&
        (isGuideLineProjectorOpened == true)) {
      _openGuideLinePopup(context);
      return;
    }

    /// ガイドラインを表示します

    _openGuideLineProjector(context).then((_) {
      _openGuideLinePopup(context);
      final headerFunction = ref.read(utlHeaderViewModelProvider.notifier);
      headerFunction.maybeCloseCamera();

      /// view更新
      state = state.copyWith(
        guideLineButtonState: _getGuideLineState(),
      );
      headerFunction.update();
    });
  }

  @override
  void onSensingLineButtonClick(BuildContext context) {
    if (state.recognitionLineButtonState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }
    final sensingLine =
        StitchRegulatorModel().getProjectorSRSensingLineDisplay();
    if (sensingLine == false) {
      /// カメラ機能閉じる
      final headerFunction = ref.read(utlHeaderViewModelProvider.notifier);
      headerFunction.maybeCloseCamera();

      /// Camera状態の変更 isAspectViewやisChangeViewは0になります。
      TpdLibrary().apiBinding.stopCameraCapture();

      /// プロジェクションの起動
      _openOrCloseSrSensingProjector(true).then((_) {
        StitchRegulatorModel().setProjectorSRSensingLineDisplay(true);
        _showColorSelectPopup(context);

        /// 画面表示更新
        state = state.copyWith(
          recognitionLineButtonState: _getSensingLineState(),
        );
        headerFunction.update();
      });
    } else {
      _showColorSelectPopup(context);
    }
  }

  @override
  void onStatusButtonClick() {
    if (state.srStatusButtonState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// カメラ機能閉じる
    final headerFunction = ref.read(utlHeaderViewModelProvider.notifier);
    headerFunction.maybeCloseCamera();

    /// Camera状態の変更 isAspectViewやisChangeViewは0になります。
    TpdLibrary().apiBinding.stopCameraCapture();

    bool srModuleStatus = StitchRegulatorModel().getSrModuleStatus();
    srModuleStatus = !srModuleStatus;

    /// プロジェクションの起動
    _openOrCloseSrStatusProjector(srModuleStatus).then((_) {
      StitchRegulatorModel().setProjectorSRStatusDisplay(srModuleStatus);

      state = state.copyWith(
        srStatusButtonState: _getSrStatusState(),
      );
      headerFunction.update();
    });
  }

  @override
  bool onSRPresserFootMinusButtonClick(bool isLongPress) {
    final UtlLibraryError error =
        UtlLibrary().apiBinding.setSRPfPosMinusWrite();
    if (error != UtlLibraryError.utlNoError) {
      if (error == UtlLibraryError.utlErrorInvalid) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return true;
      } else {
        Log.e(
            tag: "onSRPresserFootMinusButtonClick",
            description: "unknown error");
        return true;
      }
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    return true;
  }

  @override
  bool onSRPresserFootPlusButtonClick(bool isLongPress) {
    final UtlLibraryError error = UtlLibrary().apiBinding.setSRPfPosPlusWrite();
    if (error != UtlLibraryError.utlNoError) {
      if (error == UtlLibraryError.utlErrorInvalid) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return true;
      } else {
        Log.e(
            tag: "onSRPresserFootPlusButtonClick",
            description: "unknown error");
        return true;
      }
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    return true;
  }

  @override
  void onRetrieveButtonClick(BuildContext context) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (_) => const SRManualMemoryPopup(),
        barrier: false,
      ),
    );
  }

  @override
  void onMemoryButtonClick() {
    if (state.isMemoryEnable == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// Waitポップアップ画面を表示するpage
    bool isSucceed = SRManualMemoryModel().saveManualMemory();
    if (isSucceed == false) {
      /// メモリ不足
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_POCKET_FULL,
        arguments: ErrPocketFullArgument(
          onOKButtonClicked: (_) => GlobalPopupRoute().resetErrorState(),
        ),
      );
    } else {
      /// Waitポップアップ画面を表示するpage
      PanelPopupRoute().openPleaseWaitPopup();

      Future.delayed(pleaseWaitTime)
          .then((value) => PanelPopupRoute().closePleaseWaitPopup());

      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }
  }

  @override
  void onResetButtonClicked() {
    if (state.isResetEnable == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    UtlLibrary().apiBinding.resetManualMemory();
    UtlLibrary().apiBinding.setSRPfPosDefault();
  }

  ///
  /// ColorSelectPopup表示
  ///
  void _showColorSelectPopup(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (_) => const ColorSelectPopup(),
        barrier: false,
      ),
    );
  }

  ///
  /// StitchRegulatorPage画面のView更新
  ///
  void updateSRModePageByChild(SRModuleType vm) {
    switch (vm) {
      /// Stitch Regulator
      case SRModuleType.stitchRegulator:
        ref.read(utilityParameterAdjustmentViewModelProvider.notifier).update();
        ref.read(utilityPreviewViewModeProvider.notifier).update();
        ref.read(sewingSettingButtonsViewModelProvider.notifier).update();
        break;
      case SRModuleType.parameterAdjustment:
        ref.read(utilityPreviewViewModeProvider.notifier).update();
        ref.read(utilityParameterAdjustmentViewModelProvider.notifier).update();
        break;
      case SRModuleType.manualMemory:
        ref.read(utilityParameterAdjustmentViewModelProvider.notifier).update();
        ref.read(utilityPreviewViewModeProvider.notifier).update();
        ref.read(sewingSettingButtonsViewModelProvider.notifier).update();
        update();
        break;
      case SRModuleType.guideLine:
        ref.read(guideLineViewModelProvider.notifier).update();
        break;
      case SRModuleType.header:
        ref.read(utilityPreviewViewModeProvider.notifier).update();
        update();
        break;
      case SRModuleType.sensingLineOnOff:
        state = state.copyWith(
          recognitionLineButtonState: _getSensingLineState(),
        );
        break;
      default:
        break;
    }
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// 内部で使われる関数群
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// 画面表示の模様とSrModeのを変更します
  ///
  void _updateUiDisplayPatternSrModeBySrMode(SR_Mode srMode) {
    int? currentPatternIndex = PatternDataModel().currentSelectedPatternIndex;

    if (srMode != StitchRegulatorModel().srMode) {
      Log.errorTrace(
          "updateUiDisplayPatternSrMode, current${StitchRegulatorModel().srMode}:,set:$srMode");
    } else {
      Log.debugTrace("updateUiDisplayPatternSrMode $srMode");
    }

    /// 非選択
    if (srMode == SR_Mode_t.SR_MODE_NONE &&
        StitchRegulatorModel().srOnOff == true) {
      PatternDataModel().patternList.clearPattern();
      PatternDataModel().currentLoginPatternIndex = null;
      PatternDataModel().currentSelectedPatternIndex =
          PatternDataModel().currentLoginPatternIndex;
      PreviewDataModel().updatePreviewImage(null);
      PatternDataModel().isNotPatternFirstTime = false;

      /// view更新
      state = state.copyWith(
        patternsState: _getDisplayPatternsState(),
        patternList: _getPatternList(),
        stitchName: StitchRegulatorModel().getSRPatternName(),
      );

      /// 他の画面を更新します
      updateSRModePageByChild(SRModuleType.stitchRegulator);
      return;
    }

    int selectedPatternIndex = 0;

    /// 現在のgroupを取得する
    final List<StitchRegulatorIconData> currentGroup =
        srMode == SR_Mode_t.SR_MODE_BASTING
            ? StitchRegulatorReader().getSRMode3Pattern()
            : StitchRegulatorReader().getSRMode12Pattern();

    /// 現在のpatternは現在のgroupに存在しますか
    for (var element in currentGroup) {
      if (currentPatternIndex != null &&
          element.patternNum ==
              PatternDataModel()
                  .getLoginSRModePattern(state.srMode, currentPatternIndex)
                  .patternNum) {
        selectedPatternIndex = PatternDataModel().currentSelectedPatternIndex!;
      }
    }

    /// 模様登録
    PatternDataModel().loginSRModePattern(srMode, selectedPatternIndex);

    /// エラーがある場合
    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      Log.debugTrace(" bPIFError: ${bPIFErrorPointer.errorCode}");
      return;
    }
    PatternDataModel().currentSelectedPatternIndex =
        PatternDataModel().currentLoginPatternIndex;

    /// view更新
    state = state.copyWith(
      patternsState: _getDisplayPatternsState(),
      patternList: _getPatternList(),
      stitchName: StitchRegulatorModel().getSRPatternName(),
    );

    /// 他の画面を更新します
    updateSRModePageByChild(SRModuleType.stitchRegulator);
  }

  ///
  /// 表示用模様データを更新する
  ///
  List<PatternDisplayData> _getPatternList() {
    List<PatternDisplayData> patternDisplayData = [];
    final patternList = StitchRegulatorModel().getAllDisplayPattern();

    for (var pattern in patternList) {
      patternDisplayData.add(PatternDisplayData(
          "SR-${pattern.patternNum.toString().padLeft(2, "0")}",
          pattern.image,
          pattern.imageFilePath));
    }
    return patternDisplayData;
  }

  ///
  /// 模様表示状態取得する
  ///
  List<ButtonState> _getDisplayPatternsState() {
    final List<int> disableDataList =
        UtlLibrary().apiBinding.getDisableUtlDataList(groupSr).disableDataList;
    final srPatterns = StitchRegulatorModel().getAllDisplayPattern();
    final patternLength = srPatterns.length;
    List<ButtonState> patternsItemState =
        List.filled(patternLength, ButtonState.normal);

    for (int i = 0; i < patternLength; i++) {
      if (PatternDataModel().currentSelectedPatternIndex == i) {
        patternsItemState[i] = ButtonState.select;
      } else {
        if (disableDataList.contains(srPatterns[i].patternNum)) {
          patternsItemState[i] = ButtonState.disable;
        } else {
          patternsItemState[i] = ButtonState.normal;
        }
      }
    }

    return patternsItemState;
  }

  ///
  /// プロジェクトガイドラインを表示します
  ///
  Future<void> _openGuideLineProjector(BuildContext context) async {
    GlobalPopupRoute().showPleaseWaitPopup(
        arguments: {GlobalPopupRoute.isStopSystemSound: true});
    final Future delayFuture = Future.delayed(
        const Duration(milliseconds: ProjectorModel.projectorStartStopDelayMS));
    GuideLineModel().guideLineShow = GuideLineShow.guideLineShowON;

    /// 布厚を押えで測ったう
    UtlLibrary().apiBinding.calibUtlLineMaker();
    final Future openOrCloseFuture =
        GuideLineModel.openOrCloseProjectorGuideLine(
      GuideLineShow.guideLineShowON,
    );
    await Future.wait([openOrCloseFuture, delayFuture]);
    await ProjectorModel().refreshUtlProjector(autoBacklight: false);
    await ProjectorModel().refreshUtlProjector();
    GlobalPopupRoute().resetPleaseWaitPopup();
    return;
  }

  ///
  /// SrSensing起動と終了
  ///
  Future<void> _openOrCloseSrSensingProjector(bool tryToOpen) async {
    GlobalPopupRoute().showPleaseWaitPopup(
        arguments: {GlobalPopupRoute.isStopSystemSound: true});
    final Future delayFuture = Future.delayed(
        const Duration(milliseconds: ProjectorModel.projectorStartStopDelayMS));
    final Future openOrCloseFuture;
    if (tryToOpen) {
      openOrCloseFuture =
          ProjectorModel().openUtlProjector(UtlProjectorType.srSensing);
    } else {
      openOrCloseFuture =
          ProjectorModel().closeUtlProjector(UtlProjectorType.srSensing);
    }
    await Future.wait([openOrCloseFuture, delayFuture]);
    await ProjectorModel().refreshUtlProjector();
    GlobalPopupRoute().resetPleaseWaitPopup();
  }

  ///
  /// プロジェクトSrStatusを表示します
  ///
  Future<void> _openOrCloseSrStatusProjector(bool tryToOpen) async {
    GlobalPopupRoute().showPleaseWaitPopup(
        arguments: {GlobalPopupRoute.isStopSystemSound: true});
    final Future delayFuture = Future.delayed(
        const Duration(milliseconds: ProjectorModel.projectorStartStopDelayMS));
    final Future openOrCloseFuture;
    if (tryToOpen) {
      openOrCloseFuture =
          ProjectorModel().openUtlProjector(UtlProjectorType.srStatus);
    } else {
      openOrCloseFuture =
          ProjectorModel().closeUtlProjector(UtlProjectorType.srStatus);
    }
    await Future.wait([openOrCloseFuture, delayFuture]);
    await ProjectorModel().refreshUtlProjector(autoBacklight: true);
    GlobalPopupRoute().resetPleaseWaitPopup();
    return;
  }

  ///
  /// guideLine popupを表示する
  ///
  void _openGuideLinePopup(BuildContext context) {
    if (!context.mounted) {
      Log.e(
        tag: "openProjectorAndShowGuideLinePop",
        description: "page is disposed",
      );
      return;
    }
    state = state.copyWith(
      guideLinePopupState: true,
    );
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (_) => GuideLine(
          onGuideLineProjectorClosedOrOpened: (switchStatus) {
            state = state.copyWith(guideLineButtonState: _getGuideLineState());
          },
        ),
        barrier: false,
      ),
    ).then((_) => state = state.copyWith(
          guideLinePopupState: false,
        ));
  }

  ///
  /// guideLine popupを閉じる
  ///
  void _closeGuideLinePopup(BuildContext context) {
    state = state.copyWith(
      guideLinePopupState: false,
    );
    PopupNavigator.pop(context: context);
  }

  ButtonState _getGuideLineState() =>
      ProjectorModel().isUtlProjectorOpened(UtlProjectorType.guideline) == true
          ? ButtonState.select
          : ButtonState.normal;

  ButtonState _getSensingLineState() => StitchRegulatorModel().srOnOff
      ? StitchRegulatorModel().getProjectorSRSensingLineDisplay()
          ? ButtonState.select
          : ButtonState.normal
      : ButtonState.disable;

  ButtonState _getSrStatusState() => StitchRegulatorModel().srOnOff
      ? StitchRegulatorModel().getSrModuleStatus()
          ? ButtonState.select
          : ButtonState.normal
      : ButtonState.disable;
}
