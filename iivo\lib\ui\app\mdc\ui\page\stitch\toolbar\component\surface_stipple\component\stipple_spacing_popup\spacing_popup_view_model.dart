import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_stipple_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'spacing_popup_view_interface.dart';

final spacingViewModelProvider = StateNotifierProvider.autoDispose<
    SpacingPopupStateViewInterface,
    SpacingPopupState>((ref) => SpacingViewModel(ref));

class SpacingViewModel extends SpacingPopupStateViewInterface {
  SpacingViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const SpacingPopupState(
              isSpacingMinusToLimit: false,
              isSpacingPlusToLimit: false,
              spacingInputValue: "",
              spacingDisplayTextStyle: false,
            ),
            ref) {
    update();
  }

  @override
  void update() {
    state = state.copyWith(
      isSpacingMinusToLimit: _isMinusToLimit(),
      isSpacingPlusToLimit: _isPlusToLimit(),
      spacingInputValue: _getSpacingDisplayValue(),
      spacingDisplayTextStyle: _getSpacingDisplayTextStyle(),
    );
  }

  ///
  /// ステップ量
  ///
  final int _stepValue = 10;

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  @override
  int defaultValue = SurfaceStippleModel().spacingDefaultValue;

  ///
  /// Spacing値ディスプレイスター
  ///
  bool _isSpacingValueDisplayStar = SurfaceStippleModel().getSpacing() !=
          SurfaceStippleModel.spacingNotUpdating
      ? false
      : true;

  ///
  /// Spacing値
  ///
  int _spacingValue = SurfaceStippleModel().getSpacing();

  @override
  bool plusSpacing(bool isLongPress) {
    if (_isSpacingValueDisplayStar) {
      _isSpacingValueDisplayStar = false;

      ///  Model 更新
      _spacingValue = defaultValue;

      /// View更新
      update();
      return false;
    }
    if (_spacingValue >= SurfaceStippleModel.maxiSpacingValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    if (isLongPress == true) {
      _spacingValue -= _spacingValue % _stepValue;
      if (_spacingValue != SurfaceStippleModel.maxiSpacingValue) {
        _spacingValue += _stepValue;
      }
    } else {
      _spacingValue++;
    }
    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// View 更新
    update();
    return true;
  }

  @override
  bool miniSpacing(bool isLongPress) {
    if (_isSpacingValueDisplayStar) {
      _isSpacingValueDisplayStar = false;

      ///  Model 更新
      _spacingValue = defaultValue;

      /// View更新
      update();
      return false;
    }
    if (_spacingValue <= SurfaceStippleModel.miniSpacingValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    if (isLongPress == true) {
      _spacingValue -= _spacingValue % _stepValue;
      if (_spacingValue != SurfaceStippleModel.miniSpacingValue) {
        _spacingValue -= _stepValue;
      }
    } else {
      _spacingValue--;
    }
    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// View 更新
    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.surfaceStippleSpacing.toString());
    if (_isSpacingValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int spacingValue = SurfaceStippleModel().getSpacing();

    /// Model更新
    SurfaceStippleModel().setSpacing(_spacingValue);
    if (SurfaceStippleModel().setStippleSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (spacingValue != _spacingValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// Spacingの表示値を取得する
  ///
  String _getSpacingDisplayValue() {
    /// cmからmmへ
    double surfaceStippleSpacingValue = _spacingValue / _stepValue;
    if (_isSpacingValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return surfaceStippleSpacingValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(surfaceStippleSpacingValue);
  }

  ///
  /// 縮小ボタンの状態の取得
  ///
  bool _isMinusToLimit() {
    if (_spacingValue <= SurfaceStippleModel.miniSpacingValue) {
      return true;
    }
    return false;
  }

  ///
  /// 増大ボタン状態の取得
  ///
  bool _isPlusToLimit() {
    if (_spacingValue >= SurfaceStippleModel.maxiSpacingValue) {
      return true;
    }
    return false;
  }

  ///
  /// テキストスタイルの取得
  ///
  bool _getSpacingDisplayTextStyle() {
    if (_isSpacingValueDisplayStar) {
      return true;
    }

    if (_spacingValue == defaultValue) {
      return true;
    }

    return false;
  }
}
