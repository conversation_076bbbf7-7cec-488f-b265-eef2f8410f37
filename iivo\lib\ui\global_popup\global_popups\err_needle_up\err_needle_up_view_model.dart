import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import 'err_needle_up_view_interface.dart';

void Function()? errNeedleUpFunc;

final errNeedleUpViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrNeedleUpViewInterface, ErrNeedleUpState, BuildContext>(
        (ref, context) => ErrNeedleUpViewModel(ref, context));

class ErrNeedleUpViewModel extends ErrNeedleUpViewInterface {
  ErrNeedleUpViewModel(Ref ref, BuildContext context)
      : super(const ErrNeedleUpState(), ref, context);

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    if (errNeedleUpFunc == null) {
      TpdLibrary()
          .apiBinding
          .bpIFSendDisplayDataSync(BPIFSendKey.KEYERRNEEDLEDOWN);
    } else {
      errNeedleUpFunc?.call();
      errNeedleUpFunc = null;
    }
  }
}
