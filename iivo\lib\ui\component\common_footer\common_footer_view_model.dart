// ignore: implementation_imports
import 'dart:async';

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../model/app_locale.dart';
import '../../../model/screenshot_model.dart';
import '../../app/emb/ui/component/emb_footer/emb_footer_view_model.dart';
import '../../app/home/<USER>/component/home_footer/home_footer_view_model.dart';
import '../../app/mdc/ui/component/mdc_footer/mdc_footer_view_model.dart';
import '../../app/setting/model/clock_setting_model.dart';
import '../../app/setting/ui/component/setting_footer/setting_footer_view_model.dart';
import '../../app/utl/ui/component/utl_footer/utl_footer_view_model.dart';
import '../../page_route/page_route.dart';
import 'common_footer_view_interface.dart';
import 'digital_clock/digital_clock_view_model.dart';

class CommonFooterViewModel extends CommonFooterViewInterface {
  CommonFooterViewModel(ref)
      : super(
            CommonFooterState(
                isScreenShot:
                    DeviceLibrary().apiBinding.getSettingScreenShot().value ==
                        0),
            ref);

  @override
  void build() {
    super.build();
    final footerSvgInfo = TpdLibrary().apiBinding.getFooterSvgInfo();
    final showTimeTypeIndex = DeviceLibrary().apiBinding.getShowTimeTypeIndex();
    ClockSettingModel.showTimeInputType =
        ShowTimeType.values[showTimeTypeIndex];
    state = state.copyWith(
      clockSvgData:
          footerSvgInfo[FooterListIndex.footerDigitalClock.index].bytes,
    );
    update();
  }

  @override
  void update() {
    Locale locale = AppLocale().getCurrentLocale();
    state = state.copyWith(
      footerTipMessageString: lookupAppLocalizations(locale).tt_foot_clock,
      isClockDisplay: DeviceLibrary().apiBinding.getClockDisplayStatus(),
    );
  }

  @override
  Future<void> onScreenshotButtonClick(BuildContext context) async {
    if (DeviceLibrary().apiBinding.getSettingScreenShot().value == 1) {
      await ScreenshotModel().automaticScreenCaptureAndSave();
    } else if (DeviceLibrary().apiBinding.getSettingScreenShot().value == 2) {
      await ScreenshotModel().automaticScreenCaptureAndSaveAllLanguage();
    }
  }

  @override
  void onClockButtonClick(BuildContext context) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.clockSetting);
  }

  @override
  void onPdfButtonClick(BuildContext context) {}

  ///
  /// TopPage画面のView更新
  ///
  void updateCommonFooterPageByChild() {
    if (ref.exists(settingFooterViewModelProvider)) {
      ref.read(settingFooterViewModelProvider.notifier).update();
    }
    if (ref.exists(homeFooterViewModelProvider)) {
      ref.read(homeFooterViewModelProvider.notifier).update();
    }
    if (ref.exists(embFooterViewModelProvider)) {
      ref.read(embFooterViewModelProvider.notifier).update();
    }
    if (ref.exists(mdcFooterViewModelProvider)) {
      ref.read(mdcFooterViewModelProvider.notifier).update();
    }
    if (ref.exists(utlFooterViewModelProvider)) {
      ref.read(utlFooterViewModelProvider.notifier).update();
    }
    if (ref.exists(digitalClockViewModelProvider)) {
      ref.read(digitalClockViewModelProvider.notifier).update();
    }
  }
}
