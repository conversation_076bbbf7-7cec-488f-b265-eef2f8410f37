import 'dart:ui' as ui;

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../model/paint/clip_board_model.dart'
    show ClipBoardProperty, ClipBoardState;
import '../../../../model/paint/draw_canvas_model.dart'
    show CanvasPenState, CanvasController;
import '../../../../model/paint/library_isolate.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'mdc_canvas_view_interface.freezed.dart';

@freezed
class MDCCanvasState with _$MDCCanvasState {
  const factory MDCCanvasState({
    ///
    /// 倍率
    /// scale : 0.5(100%)、1.0(200%)、2.0(400%)、4.0(800%)、8.0(1600%)
    /// scrollBarWidth : scaleは100％以上の場合、scrollBarの幅
    /// scrollBarHeight : scaleは100％以上の場合、scrollBarの長さ
    /// scaleImageWidth : 設定したの倍率に従って画像の幅を算出する
    /// scaleImageHeight : 設定したの倍率に従って画像の長さを算出する
    ///
    @Default(1) double scale,
    @Default(0) double scrollBarWidth,
    @Default(0) double scrollBarHeight,
    @Default(0) double scaleImageWidth,
    @Default(0) double scaleImageHeight,

    ///
    /// 描画イメージ
    /// imgInfoBackGroundData : 全体描画イメージ
    /// imgInfoPartsData : 選択した部分模様描画イメージ
    /// paintImageState : Paint充填状態
    ///
    @Default(null) MdcImageInfo? imgInfoBackGround,
    @Default(null) MdcImageInfo? imgInfoParts,
    Path? partsFramePath,

    ///
    /// お絵描き画像
    /// sketchesImage : お絵描き画像
    /// sketchesDensityLevel : お絵描き画像の描画濃度
    /// mdcDensityLevel : 線の描画濃度
    ///
    @Default(null) ui.Image? sketchesImage,
    @Default(0.0) double sketchesDensityLevel,
    @Default(1.0) double mdcDensityLevel,

    /// 範囲選択
    /// isStartAnimation : 破線アニメーションを表すかどうか
    ///
    @Default(false) bool isStartAnimation,

    ///
    /// 部分模様画像
    ///
    @Default(null) PartsImage? partsImage,
    @Default(null) ui.Image? backgroundImage,
    @Default(ui.Rect.zero) ui.Rect partsFrameRect,
    @Default(0) double rotate,

    ///
    /// Frame & Grid
    ///
    @Default(null) MDCFrameMetrics? frameMetrics,

    ///
    ///  が必要ですか
    ///
    @Default(false) bool isNeedProcessing,
  }) = _MDCCanvasState;
}

class PartsImage {
  PartsImage(this.hash, this.image);
  final int hash;
  final ui.Image? image;
}

///
/// 刺しゅう枠とグリッドのメトリクス
///
class MDCFrameMetrics {
  MDCFrameMetrics({
    required this.frameType,
    required this.gridType,
    required this.scale,
  });

  /// 刺しゅう枠の種類
  EmbFrameDispType frameType;

  /// グリッドの種類
  EmbGridType gridType;

  /// 拡大率
  double scale;

  @override
  bool operator ==(Object other) =>
      other is MDCFrameMetrics &&
      other.frameType == frameType &&
      other.gridType == gridType &&
      other.scale == scale;

  @override
  int get hashCode => Object.hash(frameType, gridType, scale);
}

abstract class MDCCanvasViewInterface extends ViewModel<MDCCanvasState> {
  MDCCanvasViewInterface(super.state);

  ///
  /// Parts模様の情報
  ///
  MdcUnsettledObjectInfo get objectInfo;

  ///
  /// Objectの初期データ
  ///
  MdcUnsettledObjectInfo get originObjectInfo;

  ///
  /// UI描画中の情報
  ///
  CanvasPenState get currentPenState;

  ///
  /// Y方向のスクロールコントローラ
  ///
  ScrollController scrollControllerY = ScrollController();

  ///
  /// X方向のスクロールコントローラ
  ///
  ScrollController scrollControllerX = ScrollController();

  ///
  /// キャンバス幅
  ///
  double get canvasWidth;

  ///
  /// キャンバスの高さ
  ///
  double get canvasHeight;

  ///
  /// 回転ページを開くかどうか
  ///
  bool get isRotate;

  ///
  /// 長押ししているかどうか
  ///
  bool get isLongPress;

  ///
  /// 描画プロパティー取得する
  ///
  ClipBoardProperty get clipBoardProperty;

  ///
  /// 画面描画状態
  ///
  ClipBoardState get clipPenState;

  ///
  /// 画面描画座標リスト
  ///
  List<Offset> get clipPenPositionList;

  ///
  /// Y方向のスクロールコントローラの幅
  ///
  double get scrollControllerYWidth;

  ///
  /// X方向のスクロールコントローラの高さ
  ///
  double get scrollControllerXHeight;

  set canvasController(CanvasController controller);

  ///
  /// ViewModel 更新
  ///
  @override
  void update();

  ///
  /// オンスケールスタート
  ///
  void onScaleStart(ScaleStartDetails details);

  ///
  /// オンスケール更新
  ///
  void onScaleUpdateAndPointerMove(ScaleUpdateDetails details);

  ///
  /// 線描画開始
  ///
  void onPointerDown(PointerDownEvent event);

  ///
  /// 線描画完了
  ///
  void onPointerUp(PointerUpEvent event);

  ///
  /// 刺しゅう枠とグリッドを描画する
  ///
  void drawEmbFrameAndGrid(Canvas canvas, MDCFrameMetrics metrics);

  ///
  /// 同期中のボタンをクリックした時のコールバック関数
  ///
  /// 戻り値：処理が中断された場合はfalseを返し、処理が終了した場合はtrueを返す
  ///
  Future<bool> lockEditProcWhenProcessing(
      void Function() afterProcessingFinish);
}

///
/// 処理待ち時間
///
Duration get processingWaitTime => LibraryIsolate().processingWaitTime;
