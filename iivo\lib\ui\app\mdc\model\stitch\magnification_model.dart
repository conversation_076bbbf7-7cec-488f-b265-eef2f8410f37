import 'dart:ui';
import '../paint/pen_model.dart';
import '../scroll_control_model.dart';

class MagnificationModel {
  MagnificationModel._internal();

  factory MagnificationModel() => _instance;
  static final MagnificationModel _instance = MagnificationModel._internal();

  /// 100%
  static const int magnification_100 = 100;

  /// 200%
  static const int magnification_200 = 200;

  /// 400%
  static const int magnification_400 = 400;

  ///
  /// LCD幅
  ///
  static const double _canvasWidth = PenModel.canvasWidth;

  ///
  /// LCD長さ
  ///
  static const double _canvasHeight = PenModel.canvasHeight;

  ///s
  /// 倍率タイプ
  ///
  static int magnificationLevel = magnification_100;

  ///
  /// 倍率リスト
  ///
  static const List<int> _magnificationLevelList = [
    MagnificationModel.magnification_100,
    MagnificationModel.magnification_200,
    MagnificationModel.magnification_400,
  ];

  ///
  /// 広大中
  ///
  EditZoom zoomEdit = EditZoom();

  ///
  /// 倍率の値をindexに変換
  ///
  int valueToIndex(int value) => _magnificationLevelList.indexOf(value);

  ///
  ///
  ///
  int indexToValue(int index) => _magnificationLevelList[index];

  ///
  ///
  ///
  int magnificationLevelNumber() => _magnificationLevelList.length;

  ///
  /// すべての可変変数をリセット
  ///
  void reset() {
    magnificationLevel = magnification_100;
    zoomEdit = EditZoom();
  }

  ///
  /// ズームの倍率をセットする関数
  ///
  void zoomInfoSet(int dspMagnification) {
    if (dspMagnification == magnification_100) {
      /// 選択倍率を設定する
      _zoomInfoSet(dspMagnification);

      /// リセット
      zoomEdit = EditZoom.reset();
      zoomEdit.detailCTPos = const Offset(_canvasWidth / 2, _canvasHeight / 2);
      return;
    }

    /// 選択倍率を設定する
    _zoomInfoSet(dspMagnification);

    /// 使用する部分の中心の計算
    ZoomMagnification magnification = zoomEdit.magnification;
    double x = (_canvasWidth * magnification.shi) / (magnification.bo * 2);
    double y = (_canvasHeight * magnification.shi) / (magnification.bo * 2);
    zoomEdit.detailCTPos = Offset(x, y);
  }

  ///
  /// 拡縮倍率（実際の倍率）小数ではなく分母と分子で持つ
  /// 小数点の計算をしたくないので、整数で計算できるように分子と分母に分ける
  ///
  void _zoomInfoSet(int magnification) {
    int shi = 1;
    int bo = 1;

    switch (magnification) {
      case magnification_100:
        shi = 1;
        bo = 1;
        break;
      case magnification_200:
        shi = 2;
        bo = 1;
        break;
      case magnification_400:
        shi = 4;
        bo = 1;
        break;
      default:
        shi = 1;
        bo = 1;
        break;
    }
    zoomEdit.magnification
      ..bo = bo
      ..shi = shi;
  }

  ///
  /// 整数割り算	あまり 切り上げ
  ///
  dynamic divideAbyBWithRoundUp(double a, int b) {
    return ((a > 0) ? ((a + (b - 1)) / b) : ((a - (b - 1)) / b));
  }
}
