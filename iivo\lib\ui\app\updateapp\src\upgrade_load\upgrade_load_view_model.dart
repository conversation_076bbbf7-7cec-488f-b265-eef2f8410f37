import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart';
import 'package:system_config/system_config.dart';
import 'package:update_grade/update_grade.dart';
import 'package:usb_manager/usb_manager.dart';
import 'package:xd_component/l10n/app_localizations.dart';

import '../../../../../model/upf_verify_signature_model.dart';
import '../../iivo_export.dart';
import '../../model/upgrade_model.dart';
import '../../page/page_route.dart';
import '../../update_app_log.dart';
import 'upgrade_load_view_interface.dart';

/// view _modelに必要な構造
final upgradeUpdateViewModeProvider = StateNotifierProvider.autoDispose
    .family<UpgradeLoadViewInterface, UpgradeLoadState, BuildContext>(
        (ref, context) => UpgradeLoadViewMode(ref, context));

class UpgradeLoadViewMode extends UpgradeLoadViewInterface {
  UpgradeLoadViewMode(
    ref,
    context,
  ) : super(const UpgradeLoadState(), ref, context) {
    if (isLineUpdate) {
      checkExistsUpgJudge();
    }

    state = state.copyWith(
      upgVersion: UpgradeModel().upgVersion,
      isWlanUpgrade: UpgradeModel().deviceKind == DeviceKind.wLan,
    );
  }

  final _l10n = lookupAppLocalizations(AppLocale().getCurrentLocale());

  /// UPFヘッダ情報
  final String _upfHeaderName = "UPFHeader.json";

  /// このUPFをインストール可能な仕向け番号一覧
  final String _specListName = "speclist";

  /// フォーマットVer
  final String _formatVersion = "formatversion";
  final int _formatVersionValue = 20;

  /// 意匠
  final String _copyRight = "copyright";
  final String _copyRightValue = "Copyright by brother industries LTD.";

  /// 機種コード
  final String _modelCode = "modelcode";
  final int _modelCodeValue = 62;

  /// UPFファイル単位のバージョン管理番号
  final String _upfVerSion = "upfversion";

  /// EEPROM(アドレス未定)に保存されているUPFファイルVer.が、この数値以下または初期値(0xFFFF)の場合のみ、インストール可能。
  final String _upperLimitVersion = "upperlimitversion";
  final int _defaultLimitVersion255 = 255;
  final int _defaultLimitVersion0 = 0;

  /// EEPROM(アドレス未定)に保存されているUPFファイルVer.が、この数値以上または初期値(0xFFFF)の場合のみ、インストール可能。
  final String _lowerLimitVersion = "lowerlimitversion";

  /// 全仕向けた適用対応
  final String _allSpecKey = "0xFF";

  /// 特殊仕向け番号
  final String _specialSpec = "0xFE";

  /// タイマー
  late Timer _timer;

  /// 初期値
  final int _initialValue = 0;

  final String _iivoStartUpdateKey = "IIVO_START_UPDATE";

  @override
  Future<void> onLoadButtonClick() async {
    final iivoStartFile = FileEntity(
        join(memorySector.brother_dir.absolutePath, _iivoStartUpdateKey));
    try {
      if (iivoStartFile.existsSync()) {
        iivoStartFile.deleteSync();
      }
    } catch (e) {
      UpdateAppLog.e("$_iivoStartUpdateKey delete fail:$e");
      _upgradeError(_l10n.upg_17, UpgradeError.errorCode24);
      return;
    }

    /// コミュニケーションチャネルを開く
    UpdateGrade().updateOpenConnection();

    if (UpgradeModel().isInUsbTransaction) {
      UpdateAppLog.i("Block onLoadButtonClick(), still in a usb transaction.");
      return;
    }

    List<FolderType> folderTypes = [];
    UpgradeError upgradeError = UpgradeError.errorCodeNone;
    await UpgradeModel().beginUsbTransactionIfNeeded();

    if (isLineUpdate) {
      folderTypes = _readUpgJudge();
      upgradeError = UpgradeError.errorCode5;
    } else {
      folderTypes = _getFileUPFList(UpgradeModel().usbPath);
      upgradeError = UpgradeError.errorCode7;
    }

    if (folderTypes.isEmpty) {
      _upgradeError(_l10n.upg_17, upgradeError);
      return;
    }

    if (UpgradeModel().deviceKind == DeviceKind.wLan) {
      UpgradeModel().downloadedUpfCollection =
          _sortDownloadedUpfFiles(folderTypes);

      if (await _canUpdateWithFileBasedOnFreeMemory(folderTypes) == false) {
        _upgradeError(_l10n.upg_17, UpgradeError.errorCode29);
        return;
      }

      UpdateAppPagesRoute()
          .pushNamed(nextRoute: UpdateAppPageRouteEnum.upgradeLoadProgress);
      return;
    }

    if (await _chooseUfp(folderTypes)) {
      /// USBアップデートを開始したら、WLAN DL用のファイルは破棄する
      _clearWlanDownLoadUpf();

      if (await _canUpdateWithFileBasedOnFreeMemory(
              [UpgradeModel().chooseUpf]) ==
          false) {
        _upgradeError(_l10n.upg_17, UpgradeError.errorCode29);
        return;
      }

      UpdateAppPagesRoute()
          .pushNamed(nextRoute: UpdateAppPageRouteEnum.upgradeLoadProgress);
    } else {
      _upgradeError(_l10n.upg_17, UpgradeError.errorCode6);
      await UpgradeModel().endUsbTransactionIfNeeded();
    }
  }

  @override
  void onReturnButtonClick() => UpdateAppPagesRoute().pop();

  ///
  /// UPGJUDGE.IIVO 読む
  ///
  List<FolderType> _readUpgJudge() {
    UpdateAppLog.d("read UPGJUDGE.IIVO start");
    List<FolderType> folderTypes = [];

    try {
      String upgJudge =
          join(memorySector.line_update.absolutePath, "UPGJUDGE.IIVO");

      FileEntity fileEntity = FileEntity(upgJudge);
      String upgJudgeString = fileEntity.readAsStringSync();
      List<String> list = upgJudgeString.split("\n");

      if (upgJudgeString.isEmpty) {
        folderTypes = [];
        return folderTypes;
      }

      for (var element in list) {
        folderTypes.add(
          FolderType(
            fileName: element,
            path: join(UpgradeModel().usbPath, element),
            isDirectory: false,
          ),
        );
      }
    } catch (e) {
      folderTypes = [];
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode4);
      return folderTypes;
    }
    UpdateAppLog.d("read UPGJUDGE.IIVO end:${folderTypes.first.path}");
    return folderTypes;
  }

  ///
  /// UPFファイルを読み取る
  ///
  List<FolderType> _getFileUPFList(String directoryPath) {
    UpdateAppLog.d("find UPF start");
    List<FolderType> fileList = [];

    try {
      final List<MemoryEntity> loadedList = DeviceMemoryModel().directoryWalk(
        DirectoryEntity(directoryPath),
        WalkFiler.fileNameRegExpWithAnyFolder(
          RegExp(r'\.upf$', caseSensitive: false),
        ),
      );

      for (var element in loadedList) {
        if (element is DirectoryEntity) {
          // do nothing
        } else {
          fileList.add(
            FolderType(
              fileName: basename(element.path),
              path: element.path,
              isDirectory: false,
            ),
          );
        }
      }
    } catch (e) {
      fileList = [];
      UpdateAppLog.e("getFileUPFList is Error : $e");
      _upgradeError(_l10n.upg_17, UpgradeError.errorCode7);
      return fileList;
    }

    UpdateAppLog.d("find UPF end : ${fileList.length}");
    return fileList;
  }

  static const int _defaultUpfVersion = 0;

  ///
  /// 前回の更新以降にローカルに保存されたUpf versionを読み取ります
  /// TODO: UPFHeader.jsonの3つのフィールドは、upfversion、upperlimitversion、lowerlimitversionであり、
  /// 整数と見なしますが、設定値の例は[1.00 ※リリース時は1.00]ですので、これらのフィールドの値に小数点[が含まれるかお問い合わせください?
  ///
  Future<int> _readLocalUpfVersion() async {
    final targetFile = FileEntity(UpgradeModel().upfHeaderSavePath);

    if (targetFile.existsSync()) {
      try {
        Map<String, dynamic> header = jsonDecode(targetFile.readAsStringSync());
        return header[_upfVerSion];
      } catch (e) {
        UpdateAppLog.e('Reading UPF version failed: $e');
        return _defaultUpfVersion;
      }
    } else {
      return _defaultUpfVersion;
    }
  }

  ///
  /// アップデート対象としてUPFを選択
  /// true:使用可能なUPFがあります   false:使用可能なUPFがありません
  ///
  Future<bool> _chooseUfp(List<FolderType> folderTypes) async {
    /// 前のUPFファイル単位のバージョン管理番号
    int lastUpfVerSion = _initialValue;
    final localUpfVersion = await _readLocalUpfVersion();

    for (int i = 0; i < folderTypes.length; i++) {
      UpdateAppLog.d("Read UPF Filename: ${folderTypes[i].fileName}");
      ({bool isSuccess, Map<String, dynamic> json}) getUpfHeaderJson =
          await _getUpfHeaderJson(folderTypes[i].path);

      if (getUpfHeaderJson.isSuccess == false) {
        UpdateAppLog.d("Read ${folderTypes[i].fileName} $_upfHeaderName Error");
        continue;
      }

      final Map<String, dynamic> json = getUpfHeaderJson.json;

      ///
      /// 仕向け番号一覧に自身の仕向け番号が含まれていること
      ///
      if (json.containsKey(_specListName) == false) {
        UpdateAppLog.e("仕向け番号一覧に自身の仕向け番号が含まれていること ERROR");
        continue;
      }

      final List<dynamic> specList = json[_specListName];

      ///
      /// specListには16進数のstring型データが格納されている
      ///
      if (_checkInclusionOfSpec(specList) == false) {
        if (UpgradeModel().destSpec == null) {
          UpdateAppLog.e("IIVO_DESTの読み取りに失敗し、speclistに0xFEが見つかりませんでした");
          continue;
        } else {
          UpdateAppLog.e("仕向け番号一覧に自身の仕向け番号が含まれていること ERROR");
          continue;
        }
      }

      ///
      ///  formatversionが”20”であること
      ///
      if (json.containsKey(_formatVersion) == false ||
          json[_formatVersion] != _formatVersionValue) {
        UpdateAppLog.e("formatversionが”20”であること ERROR");
        continue;
      }

      ///
      /// copyrightが"Copyright by brother industries LTD."であること
      ///
      if (json.containsKey(_copyRight) == false ||
          json[_copyRight] != _copyRightValue) {
        UpdateAppLog.e("copyright ERROR");
        continue;
      }

      ///
      /// 機種コードが"62"であること
      ///
      if (json.containsKey(_modelCode) && json[_modelCode] == _modelCodeValue) {
        if (json.containsKey(_lowerLimitVersion) &&
            json.containsKey(_upperLimitVersion)) {
          final lowerLimitVersion = json[_lowerLimitVersion];
          final upperLimitVersion = json[_upperLimitVersion];

          ///
          /// UPFVer、許可上限Verのどちらか1つでも0 or 0xFFならスキップ
          ///
          if (lowerLimitVersion != _defaultLimitVersion0 &&
              lowerLimitVersion != _defaultLimitVersion255 &&
              upperLimitVersion != _defaultLimitVersion0 &&
              upperLimitVersion != _defaultLimitVersion255 &&
              localUpfVersion != _defaultLimitVersion0 &&
              localUpfVersion != _defaultLimitVersion255) {
            ///
            /// EEPROMに保存されているUPFVerが、「アップグレード許可上限Ver」以下であること
            /// EEPROMに保存されているUPFVerが、「アップグレード許可下限Ver」以上であること
            ///
            if (localUpfVersion >= lowerLimitVersion &&
                localUpfVersion <= upperLimitVersion) {
              ///
              /// 選択済みUPFより新しい
              ///
              if (json.containsKey(_upfVerSion)) {
                final upfVerSion = json[_upfVerSion];
                if (upfVerSion > lastUpfVerSion) {
                  ///
                  /// ミシンとUPFのバージョンが同じかUPFの方が大きい　→UPFのインストールを開始する
                  /// UPFのバージョンの方が小さい　→UPFファイルが見つかりませんでした。のエラーを表示する。
                  ///
                  if (upfVerSion >= localUpfVersion) {
                    lastUpfVerSion = upfVerSion;
                    UpgradeModel().chooseUpf = folderTypes[i];
                  }
                } else {
                  /// do nothing
                }
              } else {
                UpdateAppLog.e("アップグレード許可 ERROR ");
                continue;
              }
            } else {
              UpdateAppLog.e(
                  "lowerLimitVersion is null  and upperLimitVersion is null");
              continue;
            }
          } else {
            ///
            /// 選択済みUPFより新しい
            ///
            if (json.containsKey(_upfVerSion)) {
              final upfVerSion = json[_upfVerSion];
              if (upfVerSion > lastUpfVerSion) {
                ///
                /// ミシンとUPFのバージョンが同じかUPFの方が大きい　→UPFのインストールを開始する
                /// UPFのバージョンの方が小さい　→UPFファイルが見つかりませんでした。のエラーを表示する。
                ///
                if (upfVerSion >= localUpfVersion) {
                  lastUpfVerSion = upfVerSion;
                  UpgradeModel().chooseUpf = folderTypes[i];
                }
              } else {
                /// do nothing
              }
            } else {
              UpdateAppLog.e("アップグレード許可 ERROR ");
              continue;
            }
          }
        } else {
          UpdateAppLog.e(
              "lowerLimitVersion is null  and upperLimitVersion is null");
          continue;
        }
      } else {
        UpdateAppLog.e("機種コードが62であること ERROR");
        continue;
      }
    }

    return UpgradeModel().chooseUpf.path.isNotEmpty;
  }

  ///
  /// jsonファイルを完全に解凍せずに読み取る
  ///
  Future<({bool isSuccess, Map<String, dynamic> json})> _getUpfHeaderJson(
      String zipFilePath) async {
    return await UsbManager().readJsonFromZip(
      zipFilePath,
      _upfHeaderName,
      password: UPFVerifySignatureModel.password,
    );
  }

  @override
  bool get isLineUpdate => FileEntity(
          join(memorySector.brother_dir.absolutePath, "IIVO_PRODUCTMODE"))
      .existsSync();

  @override
  void checkExistsUpgJudge() {
    UpdateAppLog.d("get UPGJUDGE.IIVO");
    try {
      const Duration pollInterval = Duration(seconds: 1);
      _timer = Timer.periodic(pollInterval, (timer) {
        String upgJudgePath =
            join(memorySector.line_update.absolutePath, "UPGJUDGE.IIVO");
        if (FileEntity(upgJudgePath).existsSync()) {
          state = state.copyWith(isExistsUpgJudge: true);
          _timer.cancel();
        }
      });
    } catch (e) {
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode3);
    }
  }

  ///
  /// アップグレードエラー
  ///
  void _upgradeError(String errorTile, UpgradeError upgradeError) {
    if (state.literal.isNotEmpty) {
      return;
    }

    String errorCodeMessage = upgradeError.errorCodeMessage;
    UpdateAppLog.e(errorTile);
    UpgradeModel().saveUpdateLogCat();
    state = state.copyWith(literal: "$errorTile\n$errorCodeMessage");
  }

  ///
  /// ダウンロードしたUpfファイルを並べ替える
  ///
  List<FolderType> _sortDownloadedUpfFiles(List<FolderType> folderTypes) {
    List<int> sortOrder = [
      UpgradeType.ota,
      UpgradeType.panel,
      UpgradeType.main,
      UpgradeType.manual,
      UpgradeType.eula,
      UpgradeType.panelData,
      UpgradeType.panelLib,
      UpgradeType.home,
    ];

    folderTypes.sort((a, b) {
      int getType(String fileName) {
        var match = RegExp(r'T(\d+)').firstMatch(fileName);
        return match != null ? int.parse(match.group(1)!) : -1;
      }

      int typeA = getType(a.fileName);
      int typeB = getType(b.fileName);
      int indexA = sortOrder.indexOf(typeA);
      int indexB = sortOrder.indexOf(typeB);

      if (indexA == -1) indexA = sortOrder.length;
      if (indexB == -1) indexB = sortOrder.length;

      return indexA.compareTo(indexB);
    });

    return folderTypes;
  }

  ///
  /// 仕向け番号一覧に自身の仕向け番号が含まれているかどうか
  ///
  bool _checkInclusionOfSpec(List<dynamic> specList) {
    final srcSpecList = specList.map((e) => e.toString().toUpperCase());
    final destSpec = UpgradeModel().destSpec?.toUpperCase();
    if (destSpec == null) {
      // 目的の仕様がない場合は、特殊仕様(0xFE)を確認します。
      UpdateAppLog.i("The dest spec is null, check special spec(0xFE)");
      return srcSpecList.contains(_specialSpec.toUpperCase());
    } else {
      UpdateAppLog.i("The dest spec is not null, check $destSpec or 0xFF");
      return srcSpecList.contains(destSpec) ||
          srcSpecList.contains(_allSpecKey.toUpperCase());
    }
  }

  ///
  /// USBアップデートを開始したら、WLAN DL用のファイルは破棄する
  ///
  void _clearWlanDownLoadUpf() {
    try {
      String wlanDownLoadUpfPath = UpgradeModel().upgradeSavePath;

      List<FolderType> wlanDownLoadUpfList =
          _sortDownloadedUpfFiles(_getFileUPFList(wlanDownLoadUpfPath));
      for (var folderType in wlanDownLoadUpfList) {
        if (FileEntity(folderType.path).existsSync()) {
          FileEntity(folderType.path).deleteSync();
        }
      }
    } catch (e) {
      UpdateAppLog.e("clearWlanDownLoadUpf error:$e");
      _upgradeError(_l10n.upg_16, UpgradeError.errorCode8);
    }
  }

  ///
  /// 指定されたフォルダー内のファイルの合計サイズと空き容量を比較し、
  /// 更新可能かどうかを判断します。
  ///
  /// folderTypes: フォルダーのタイプリスト
  /// return: 更新可能ならtrue、不可能ならfalseを返します。
  Future<bool> _canUpdateWithFileBasedOnFreeMemory(
      List<FolderType> folderTypes) async {
    try {
      int totalUpdateFileSize = 0;
      for (var element in folderTypes) {
        if (element.isDirectory == false) {
          final FileEntity file = FileEntity(element.path);
          totalUpdateFileSize = totalUpdateFileSize + file.lengthSync();
        } else {
          UpdateAppLog.e("${element.path} is Directory");
        }
      }

      final freeSpace = await SystemConfig.getFreeSpace(
          memorySector.brother_dir.absolutePath);

      UpdateAppLog.d(
          "totalUpdateFileSize:$totalUpdateFileSize  freeSpace:$freeSpace");

      return freeSpace > totalUpdateFileSize;
    } catch (e) {
      UpdateAppLog.e("canUpdateWithFileBasedOnFreeMemory error:$e");
      return false;
    }
  }

  @override
  void dispose() {
    super.dispose();
    _timer.cancel();
  }
}
