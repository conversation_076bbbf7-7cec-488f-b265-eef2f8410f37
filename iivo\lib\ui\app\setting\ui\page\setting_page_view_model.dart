import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';

import '../../../../../model/machine_config_model.dart';
import '../../../../../model/provider/app_display_state_provider.dart';
import '../../../emb/ui/component/emb_header/emb_header_view_model.dart';
import '../../../home/<USER>/component/home_header/home_header_view_model.dart';
import '../../../mdc/ui/component/mdc_header/mdc_header_view_model.dart';
import '../../../teaching/ui/component/teaching_header/teaching_header_view_model.dart';
import '../../../utl/ui/component/utl_header/utl_header_view_model.dart';
import '../../model/setting_model.dart';
import '../component/setting_footer/setting_footer_view_model.dart';
import '../component/setting_header/setting_header_view_model.dart';
import '../component/stop_sewing/stop_sewing_view_model.dart';
import 'body/body_view_model.dart';
import 'body/emb_setting/emb_setting_page_1/emb_setting_page_1_view_model.dart';
import 'body/emb_setting/emb_setting_page_2/emb_setting_page_2_view_model.dart';
import 'body/emb_setting/emb_setting_page_3/emb_setting_page_3_view_model.dart';
import 'body/general_setting/general_setting_page_1/general_setting_page_1_view_model.dart';
import 'body/general_setting/general_setting_page_2/general_setting_page_2_view_model.dart';
import 'body/general_setting/general_setting_page_3/general_setting_page_3_view_model.dart';
import 'body/network_setting/network_setting_page_1/network_setting_page_1_view_model.dart';
import 'body/sewing_setting/sewing_setting_1/sewing_setting_page_1_view_mode.dart';
import 'body/sewing_setting/sewing_setting_2/sewing_setting_page_2_view_model.dart';
import 'body/sewing_setting/sewing_setting_3/sewing_setting_page_3_view_model.dart';
import 'bottom/bottom_view_model.dart';
import 'clock/clock_setting.dart';
import 'setting_page_view_interface.dart';
import 'top_bar/top_bar_view_model.dart';

final settingPageViewModelProvider =
    StateNotifierProvider.autoDispose<SettingPageViewModel, SettingPageState>(
        (ref) => SettingPageViewModel(ref));

enum ModuleType {
  topBar,
  bottom,
  scanNCut,
  register,
  changeMachineName,
  changeSsidName,
  downLoading,
  connectInternet,
  refreshHeader
}

enum PopupEnum {
  clockSetting,
}

class SettingPageViewModel extends SettingViewInterface {
  SettingPageViewModel(this._ref) : super(const SettingPageState());

  @override
  void build() {
    super.build();
    _ref.listen(
      appDisplayGlobalStateProvider.select((value) => value.isStopingMotor),
      (previous, nextState) {
        Log.debugTrace('isSettingSewing $nextState');
        if (previous == true &&
            nextState == false &&
            MachineConfigModel().currentMode == SettingBaseMode.setting) {
          /// 縫製が始まったら、ストップマスクを開けます
          _ref
              .read(stopSewingPopupViewModelProvider.notifier)
              .openStopSewingPopup();
        } else if (previous == false &&
            nextState == true &&
            MachineConfigModel().currentMode == SettingBaseMode.setting) {
          /// 縫製停止、クローズ停止マスキング
          _ref
              .read(stopSewingPopupViewModelProvider.notifier)
              .closeStopSewingPopup();
        }
      },
    );

    /// 画面を閉じる際、StopSewingが確実に閉じるようにする
    _ref.onDispose(() => _ref
        .read(stopSewingPopupViewModelProvider.notifier)
        .closeStopSewingPopup());
    update();
  }

  final AutoDisposeStateNotifierProviderRef<SettingPageViewModel,
      SettingPageState> _ref;

  @override
  void updateSettingPageByChild(ModuleType vm) {
    switch (vm) {
      case ModuleType.topBar:
        _ref.read(bottomViewModeProvider.notifier).update();
        _ref.read(bodyViewModelProvider.notifier).update();
        break;
      case ModuleType.bottom:
        _ref.read(topBarViewModeProvider.notifier).update();
        _ref.read(bodyViewModelProvider.notifier).update();
        break;
      case ModuleType.register:
        _ref.read(topBarViewModeProvider.notifier).update();
        _ref.read(bodyViewModelProvider.notifier).update();
        _ref.read(bottomViewModeProvider.notifier).update();
        break;
      case ModuleType.scanNCut:
        _ref.read(topBarViewModeProvider.notifier).update();
        _ref.read(bottomViewModeProvider.notifier).update();
        _ref.read(bodyViewModelProvider.notifier).update();
        break;
      case ModuleType.changeMachineName:
        _ref.read(networkPage1ViewModelProvider.notifier).update();
        break;
      case ModuleType.changeSsidName:
        _ref.read(networkPage1ViewModelProvider.notifier).update();
        break;
      case ModuleType.downLoading:
        _ref.read(settingHeaderViewModelProvider.notifier).update();
        _ref.read(settingFooterViewModelProvider.notifier).update();
        break;
      case ModuleType.connectInternet:
        _ref.read(topBarViewModeProvider.notifier).update();
        _ref.read(bottomViewModeProvider.notifier).update();
        _ref.read(bodyViewModelProvider.notifier).update();
        break;
      case ModuleType.refreshHeader:
        if (_ref.exists(settingHeaderViewModelProvider)) {
          _ref.read(settingHeaderViewModelProvider.notifier).update();
        }
        if (_ref.exists(homeHeaderViewModelProvider)) {
          _ref.read(homeHeaderViewModelProvider.notifier).update();
        }
        if (_ref.exists(embHeaderViewModelProvider)) {
          _ref.read(embHeaderViewModelProvider.notifier).update();
        }
        if (_ref.exists(mdcHeaderViewModelProvider)) {
          _ref.read(mdcHeaderViewModelProvider.notifier).update();
        }
        if (_ref.exists(teachingHeaderViewModelProvider)) {
          _ref.read(teachingHeaderViewModelProvider.notifier).update();
        }
        if (_ref.exists(utlHeaderViewModelProvider)) {
          _ref.read(utlHeaderViewModelProvider.notifier).update();
        }
        break;
    }
  }

  ///
  /// [provider] が存在する場合は更新します
  ///
  void _updateProviderIfExists(AutoDisposeStateNotifierProvider provider) {
    if (_ref.exists(provider)) {
      (_ref.read(provider.notifier) as ViewModel).update();
    }
  }

  @override
  void resetSettingPage() {
    switch (SettingModel().currentPageIndex) {
      case 0:
        _updateProviderIfExists(sewingSettingPage1ViewModelProvider);
        break;
      case 1:
        _updateProviderIfExists(sewingSettingPage2ViewModelProvider);
        break;
      case 2:
        _updateProviderIfExists(sewingSettingPage3ViewModelProvider);
        break;
      case 3:
        _updateProviderIfExists(generalSettingPage1ViewModelProvider);
        break;
      case 4:
        _updateProviderIfExists(generalSettingPage2ViewModelProvider);
        break;
      case 5:
        _updateProviderIfExists(generalSettingPage3ViewModelProvider);
        break;
      case 7:
        _updateProviderIfExists(embSettingPage1ViewModeProvider);
        break;
      case 8:
        _updateProviderIfExists(embSettingPage2ViewModelProvider);
        break;
      case 9:
        _updateProviderIfExists(embSettingPage3ViewModelProvider);
        break;
    }
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup() => {
        PopupEnum.clockSetting.toString(): PopupRouteBuilder(
          builder: (context) => const ClockSetting(),
          barrier: false,
        ),
      };
}
