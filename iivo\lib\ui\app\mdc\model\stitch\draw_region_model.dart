import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:ui';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../model/preview_area_size_model.dart';
import '../../../emb/model/pattern_model.dart';
import '../../../emb/model/select_model.dart';
import '../paint/edit_object_model.dart';
import '../paint/pen_model.dart';
import 'creation_isolate.dart';
import 'line_candle_model.dart';
import 'line_chain_model.dart';
import 'line_e_stitch_model.dart';
import 'line_motif_model.dart';
import 'line_rough_zigzag_model.dart';

import 'line_running_model.dart';
import 'line_triple_model.dart';
import 'line_v_stitch_model.dart';
import 'line_zigzag_model.dart';
import 'magnification_model.dart';
import 'surface_decorative_fill_model.dart';
import 'surface_stipple_model.dart';
import 'surface_tatami_model.dart';

enum Operation {
  single,
  multiLink,
}

class MdcRegionInfo {
  const MdcRegionInfo({
    required this.attr,
    required this.stitch,
    this.x = 0,
    this.y = 0,
    this.w = 0,
    this.h = 0,
    required this.regionNo,
    this.isSelected = false,
  });

  final SewingModeType attr;
  final MDCStitchType stitch;
  final double x;
  final double y;
  final double w;
  final double h;
  final int regionNo;
  final bool isSelected;

  MdcRegionInfo copyWith({
    SewingModeType? attr,
    MDCStitchType? stitch,
    double? x,
    double? y,
    double? w,
    double? h,
    int? regionNo,
    bool? isSelected,
  }) =>
      MdcRegionInfo(
        attr: attr ?? this.attr,
        stitch: stitch ?? this.stitch,
        x: x ?? this.x,
        y: y ?? this.y,
        w: w ?? this.w,
        h: h ?? this.h,
        regionNo: regionNo ?? this.regionNo,
        isSelected: isSelected ?? this.isSelected,
      );
}

///
/// 模様イメージ情報
///
class DisplayImageInfo {
  const DisplayImageInfo({
    this.image,
    this.centerOffset = ui.Offset.zero,
  });
  final ui.Image? image;
  final ui.Offset centerOffset;

  DisplayImageInfo copyWith({
    ui.Image? image,
    ui.Offset? centerOffset,
  }) =>
      DisplayImageInfo(
        image: image ?? this.image,
        centerOffset: centerOffset ?? this.centerOffset,
      );
}

class DrawRegionModel {
  DrawRegionModel._internal();

  factory DrawRegionModel() => _instance;
  static final DrawRegionModel _instance = DrawRegionModel._internal();

  ///
  /// リージョン情報リスト
  ///
  // ignore: prefer_final_fields
  List<MdcRegionInfo> _regionInfoList = [];

  ///
  /// 現在のリージョン情報添え字
  ///
  int _currentIndex = 0;

  ///
  /// アクション
  ///
  Operation _operation = Operation.single;

  ///
  /// ステッチ展開処理かどうか
  ///
  bool isProcessImage = false;

  ///
  /// sewMode は同じ属性のflag
  ///
  int isMultiLink = 255;

  ///
  /// お絵描きイメージ
  ///
  Uint8List dspImgBeforeProcessed = Uint8List.fromList([]);
  ui.Rect dspRectBeforeProcessed = ui.Rect.zero;

  MemHandle handle = 0;
  int regionNum = 0;

  ///
  /// 次のリージョン情報へ
  ///
  MDCStitchType moveToNextRegion() {
    _currentIndex++;

    if (_currentIndex >= _regionInfoList.length) {
      _currentIndex = 0;
    }

    for (int i = 0; i < _regionInfoList.length; i++) {
      _changeSingleSelectedState(i, false);
    }
    _changeSingleSelectedState(_currentIndex, true);
    return _regionInfoList[_currentIndex].stitch;
  }

  ///
  /// 前のリージョン情報へ
  ///
  MDCStitchType moveToPreRegion() {
    _currentIndex--;

    if (_currentIndex < 0) {
      _currentIndex = _regionInfoList.length - 1;
    }

    for (int i = 0; i < _regionInfoList.length; i++) {
      _changeSingleSelectedState(i, false);
    }
    _changeSingleSelectedState(_currentIndex, true);
    return _regionInfoList[_currentIndex].stitch;
  }

  ///
  /// 現在選択状態の領域の糸色情報を取得する
  ///
  ColorCode getColor() => _regionInfoList[_currentIndex].stitch.color;

  ///
  /// 指定位置へ
  ///
  void changeCurrentIndex(int index) {
    for (int i = 0; i < _regionInfoList.length; i++) {
      _changeSingleSelectedState(i, false);
    }

    _currentIndex = index;
    _operation = Operation.single;
    DrawRegionModel().setStitchModelValues(false);
    _changeSingleSelectedState(_currentIndex, true);
  }

  ///
  /// 現在選択状態の領域の糸色情報を設定する
  ///
  void setColor(ColorCode color) {
    var currentStitch = _regionInfoList[_currentIndex].stitch;
    currentStitch = currentStitch.copyWith(color: color);
  }

  ///
  /// resume初めてスティッチ画面の更新に入ります
  ///
  void stitchPageRefresh(List<MdcRegionInfo> regionList) {
    /// Mdcフェーズ詳細の変更
    dspImgBeforeProcessed =
        EditObjectModel().getMdcBackGroundImageInfo().imageData;

    /// 線の位置に赤い枠を作ります
    dspRectBeforeProcessed = Rect.fromLTWH(
      regionList.first.x,
      regionList.first.y,
      regionList.first.w,
      regionList.first.h,
    );
    startMdcStitchCreation();
  }

  ///
  /// 現在選択した模様情報を取得する
  ///
  MdcRegionInfo getPatternInfo() => _regionInfoList[_currentIndex];

  ///
  /// 線の縫い種の縫製パラメータデフォルト値
  ///
  late MdcSewLineAllParam lineParamDefault;

  ///
  /// 面の縫い種の縫製パラメータデフォルト値
  ///
  late MdcSewSurfaceAllParam surfParamDefault;

  ///
  /// 表示用模様
  ///
  DisplayImageInfo displayImageInfo = const DisplayImageInfo();

  ///
  /// 現在選択状態の領域のぬい方属性情報を更新する
  ///
  void updateStitch(MDCStitchType newStitch) {
    var currentStitch = _regionInfoList[_currentIndex];
    currentStitch = currentStitch.copyWith(stitch: newStitch);
    _regionInfoList[_currentIndex] = currentStitch;
  }

  ///
  /// 操作モードを取得する
  ///
  Operation getOperation() => _operation;

  ///
  ///  操作モードを設定する
  ///
  void setOperation(Operation operation) {
    _operation = operation;
  }

  ///
  ///  領域数を取得する
  ///
  int getRegionLength() => _regionInfoList.length;

  ///
  /// 現在選択されているフィールドの添え字取得
  ///
  int get currentSelectedIndex => _currentIndex;

  ///
  /// Embにジャンプする刺しゅうデータを設定する
  ///
  void setHandleToEmb() {
    PatternModel()
      ..reloadAllPattern()
      ..clearTemporaryPatternList();
    SelectModel()
      ..selectedCategoryType = null
      ..selectedPatternIndex = null;
  }

  ///
  /// 各パラメータ値初期化またはデフォルト値取得
  ///
  void initRegionInfoAndParamDefault() {
    isProcessImage = true;
    _getRegionInfoList();
    _getMdcSewParamDefaultData();

    /// かく模様の設定値を取得します
    List<MdcRegionInfo> regionList = getRegionInfoList(refresh: false);
    if (regionList.isNotEmpty) {
      /// 線の位置に赤い枠を作ります
      dspRectBeforeProcessed = Rect.fromLTWH(
        regionList.first.x,
        regionList.first.y,
        regionList.first.w,
        regionList.first.h,
      );
    } else {
      Log.assertTrace("getRegionInfoList empty");
    }
  }

  ///
  /// リスト取得する
  ///
  List<MdcRegionInfo> getRegionInfoList({bool refresh = true}) {
    if (refresh || _regionInfoList.isEmpty) {
      return _getRegionInfoList();
    }

    return _regionInfoList;
  }

  ///
  /// 複数模様または単独模様選択状態を変更する
  ///
  void changePatternSelectedState() {
    var selectedStitch = _regionInfoList[_currentIndex].stitch;
    var selectedSewMode = selectedStitch.sewMode;

    if (_operation == Operation.multiLink) {
      for (int i = 0; i < _regionInfoList.length; i++) {
        MDCStitchType stitch = _regionInfoList[i].stitch;
        int noValue = 0;
        int selectedNoValue = 0;

        if (selectedSewMode == stitch.sewMode) {
          if (selectedSewMode == MdcSewingKindsLine.motif) {
            noValue = (stitch as LineMotifType).motifNoValue;
            selectedNoValue = (selectedStitch as LineMotifType).motifNoValue;
          } else if (selectedSewMode == MdcSewKindsSurface.decorativeFill) {
            noValue = (stitch as SurfaceFillType).decorativeNoValue;
            selectedNoValue =
                (selectedStitch as SurfaceFillType).decorativeNoValue;
          } else {
            /// Do Nothing
          }

          if (noValue == selectedNoValue) {
            _changeSingleSelectedState(i, true);
          }
        }
      }
    } else {
      for (int i = 0; i < _regionInfoList.length; i++) {
        if (i == _currentIndex) {
          _changeSingleSelectedState(i, true);
        } else {
          _changeSingleSelectedState(i, false);
        }
      }
    }
  }

  ///
  /// 画面表示用Image取得する
  ///
  Future<void> updateDisplayImageInfo(MemHandle handle) async {
    if (_regionInfoList.isNotEmpty) {
      final result = MdcLibrary()
          .apiBinding
          .getSelectedGroupARGBImage(
            handle,
            ScrollCenterType.IMAGE_MDC_STITCH,
            MagnificationModel.magnificationLevel,
            true,
          )
          .imageInfo;
      await _updateDisplayImage(result.embImage);
      _updateDisplayRect(handle);
    }
  }

  ///
  /// Libから取得データは各Stitch設定する
  ///
  MDCStitchType convertLineStitchValues(
      MdcSewingKindsLine sew, int regionNo, bool isMiltLink) {
    dynamic data;
    ColorCode colorCode;

    switch (sew) {
      case MdcSewingKindsLine.roughZigzag:
        (color: colorCode, errorCode: _, prm: data) = MdcLibrary()
            .apiBinding
            .getMdcRoughZigzagLineParamInfo(regionNo, isMiltLink);

        return LineRoughZigzagType(
          density: data.density,
          roughZigzagWidth: data.width,
          sewMode: MdcSewingKindsLine.roughZigzag,
          color: colorCode,
        );
      case MdcSewingKindsLine.running:
        (color: colorCode, errCode: _, prm: data) = MdcLibrary()
            .apiBinding
            .getMdcRunningStitchLineParamInfo(regionNo, isMiltLink);
        return LineRunType(
          runPitch: data.pitch,
          underSewing: data.underSewing,
          sewMode: MdcSewingKindsLine.running,
          color: colorCode,
        );
      case MdcSewingKindsLine.triple:
        (color: colorCode, errCode: _, prm: data) = MdcLibrary()
            .apiBinding
            .getMdcTripleStitchLineParamInfo(regionNo, isMiltLink);
        return LineTripleType(
          runPitch: data.pitch,
          underSewing: data.underSewing,
          sewMode: MdcSewingKindsLine.triple,
          color: colorCode,
        );
      case MdcSewingKindsLine.candleWicking:
        (color: colorCode, errCode: _, prm: data) = MdcLibrary()
            .apiBinding
            .getMdcCandlwickingLineParamInfo(regionNo, isMiltLink);
        return LineCandleType(
          motifSize: data.size,
          motifSpace: data.spacing,
          sewMode: MdcSewingKindsLine.candleWicking,
          color: colorCode,
        );
      case MdcSewingKindsLine.chain:
        (color: colorCode, errCode: _, prm: data) = MdcLibrary()
            .apiBinding
            .getMdcChainStitchLineParamInfo(regionNo, isMiltLink);
        return LineChainType(
          times: data.thickness,
          motifSize: data.size,
          sewMode: MdcSewingKindsLine.chain,
          color: colorCode,
        );
      case MdcSewingKindsLine.eStitch:
        (color: colorCode, errCode: _, prm: data) = MdcLibrary()
            .apiBinding
            .getMdcEStitchLineParamInfo(regionNo, isMiltLink);
        return LineEStitchType(
          eVStitchSize: data.width,
          eVStitchSpace: data.spacing,
          eVStitchTimes: data.thickness,
          eVStitchDirection: data.side,
          sewMode: MdcSewingKindsLine.eStitch,
          color: colorCode,
        );
      case MdcSewingKindsLine.vStitch:
        (color: colorCode, errCode: _, prm: data) = MdcLibrary()
            .apiBinding
            .getMdcVStitchLineParamInfo(regionNo, isMiltLink);
        return LineVStitchType(
          eVStitchSize: data.width,
          eVStitchSpace: data.spacing,
          eVStitchTimes: data.thickness,
          eVStitchDirection: data.side,
          sewMode: MdcSewingKindsLine.vStitch,
          color: colorCode,
        );
      case MdcSewingKindsLine.motif:
        int motifNoValue;
        bool motifTypeValue;
        (
          color: colorCode,
          prm: data,
          motifNo: motifNoValue,
          motifType: motifTypeValue,
          errCode: _
        ) = MdcLibrary()
            .apiBinding
            .getMdcMotifLineParamInfo(regionNo, isMiltLink);
        return LineMotifType(
          motifSize: data.size,
          motifSpace: data.spacing,
          color: colorCode,
          motifHArrange: data.side,
          sewMode: MdcSewingKindsLine.motif,
          motifNoValue: motifNoValue,
          motifTypeValue: motifTypeValue,
        );
      case MdcSewingKindsLine.zigzag:
        var (color: colorCode, errCode: _, value: data) = MdcLibrary()
            .apiBinding
            .getMdcZigzagLineParamInfo(regionNo, isMiltLink);
        return LineZigzagType(
          density: data.density,
          zigzagWidth: data.width,
          sewMode: MdcSewingKindsLine.zigzag,
          color: colorCode,
        );
      default:
        assert(sew != MdcSewingKindsLine.invalid, "無効データ");
        return LineZigzagType(
          density: MdcZigzagTatamiDensity.mdc_zigzag_tatami_density_090,
          zigzagWidth: 0,
          sewMode: MdcSewingKindsLine.zigzag,
          color: ColorCode.getValuesByNumber(1),
        );
    }
  }

  ///
  /// Libから取得データは各Stitch設定する
  ///
  MDCStitchType convertSurfaceStitchValues(
      MdcSewKindsSurface sew, int regionNo, bool isMiltLink) {
    dynamic data;
    dynamic colorCode;

    switch (sew) {
      case MdcSewKindsSurface.tatami:
        (color: colorCode, errorCode: _, prm: data) = MdcLibrary()
            .apiBinding
            .getMdcTatamiSurfaceParamInfo(regionNo, isMiltLink);
        return SurfaceTatamiType(
          dirKind: data.dirKind,
          direction: data.direction,
          density: data.density,
          pullCompensation: data.pullCompensation,
          underSewing: data.underSewing,
          sewMode: MdcSewKindsSurface.tatami,
          color: colorCode,
        );
      case MdcSewKindsSurface.stipring:
        (color: colorCode, errorCode: _, prm: data) = MdcLibrary()
            .apiBinding
            .getMdcStipplingSurfaceParamInfo(regionNo, isMiltLink);
        return SurfaceStippleType(
          runPitch: data.runPitch,
          spacing: data.spacing,
          distance: data.distance,
          stitch: data.stitch,
          sewMode: MdcSewKindsSurface.stipring,
          color: colorCode,
        );
      case MdcSewKindsSurface.decorativeFill:
        int decorativeNoValue;
        bool decorativeTypeValue;
        (
          prm: data,
          codeColor: colorCode,
          decorativeNoValue: decorativeNoValue,
          decorativeTypeValue: decorativeTypeValue,
          errCode: _
        ) = MdcLibrary()
            .apiBinding
            .getMdcDecorativeFillSurfaceParamInfo(regionNo, isMiltLink);
        return SurfaceFillType(
          size: data.size,
          direction: data.direction,
          outline: data.outline,
          runPitch: 0,
          randomShift: data.randomShift,
          randomShiftType: data.randomShiftType,
          positionOffsetX: data.positionOffsetX,
          positionOffsetY: data.positionOffsetY,
          thickness: data.thickness,
          sewMode: MdcSewKindsSurface.decorativeFill,
          color: colorCode,
          decorativeNoValue: decorativeNoValue,
          decorativeTypeValue: decorativeTypeValue,
        );
      default:
        assert(sew != MdcSewKindsSurface.invalid, "無効データ");
        return SurfaceTatamiType(
          dirKind: MDCTatamiDirKind.mdc_tatami_dir_kind_auto,
          direction: 0,
          density: MdcZigzagTatamiDensity.mdc_zigzag_tatami_density_090,
          pullCompensation: 0,
          underSewing: MDCIsOnOff.values[0],
          sewMode: MdcSewKindsSurface.tatami,
          color: ColorCode.getValuesByNumber(1),
        );
    }
  }

  ///
  /// ステッチ展開処理
  ///
  void startMdcStitchCreation() {
    isProcessImage = true;
    CreationModel().startStitchCreation();
  }

  ///
  /// 選択した模様情報変更する
  /// 選択しない：false, 選択する：true
  ///
  void _changeSingleSelectedState(int index, bool value) {
    MdcRegionInfo patternInfo = _regionInfoList[index];
    patternInfo = patternInfo.copyWith(isSelected: value);
    _regionInfoList[index] = patternInfo;
  }

  ///
  /// 表示用Image更新する
  ///
  Future<void> _updateDisplayImage(EmbImg embImageInfo) async {
    final ui.Codec codec = await ui.instantiateImageCodec(
      embImageInfo.imageData,
      targetWidth: embImageInfo.imageWidth,
      targetHeight: embImageInfo.imageHeight,
    );
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    displayImageInfo = displayImageInfo.copyWith(image: frameInfo.image);
  }

  ///
  /// 表示用Image範囲を更新する
  ///
  void _updateDisplayRect(MemHandle handle) {
    final grpInfo = EmbLibrary().apiBinding.getEmbGrpInfo(handle);
    final EmbGrp grp = grpInfo.embGrp;

    /// position:模様枠中心(0,0)から刺繍の中心までの距離(x,y　0.1mm単位)
    double leftDistance = (grp.position.X / conversionRate) / PenModel.ratio;
    double topDistance = (grp.position.Y / conversionRate) / PenModel.ratio;

    /// 「100％」画像の中心点座標を取得する
    Offset patternCenter = Offset(
      (mdcStitchPreviewCenter.dx + leftDistance) / 2,
      (mdcStitchPreviewCenter.dy + topDistance) / 2,
    );

    displayImageInfo = displayImageInfo.copyWith(centerOffset: patternCenter);
  }

  ///
  /// リスト取得する
  ///
  List<MdcRegionInfo> _getRegionInfoList() {
    var (
      bufNum: bufNum,
      errorCode: errorCode,
      regionInfoBuf: regionInfoBuf,
      curRegionNo: curRegionNo
    ) = MdcLibrary().apiBinding.getMdcEditRegionInfoAllData(regionNum);
    if (errorCode != MdcLibraryError.mdcNoError) {
      Log.errorTrace("getMdcEditRegionInfoAllData error");
      return _regionInfoList;
    }

    _currentIndex = curRegionNo;
    _regionInfoList.clear();
    for (int i = 0; i < bufNum; i++) {
      var regionInfo = regionInfoBuf[i].regionInfo;
      var regionNo = regionInfoBuf[i].regionNo;
      bool isMultiLink =
          DrawRegionModel().getOperation() == Operation.multiLink;
      if (regionInfo.sew != MdcSewingKindsLine.invalid.index) {
        MdcRegionInfo mdcRegionInfo = MdcRegionInfo(
          attr: regionInfo.attr,
          stitch: regionInfo.attr == SewingModeType.surface
              ? convertSurfaceStitchValues(
                  MdcSewKindsSurface.getValuesByNumber(regionInfo.sew),
                  regionNo,
                  isMultiLink)
              : convertLineStitchValues(
                  MdcSewingKindsLine.getValuesByNumber(regionInfo.sew),
                  regionNo,
                  isMultiLink),
          x: regionInfo.x / 2,
          y: regionInfo.y / 2,
          w: regionInfo.w / 2,
          h: regionInfo.h / 2,
          regionNo: regionNo,
          isSelected: false,
        );
        _regionInfoList.add(mdcRegionInfo);
      }
    }
    changePatternSelectedState();

    return _regionInfoList;
  }

  ///
  /// 全縫製パラメータのデフォルト値を取得する
  ///
  void _getMdcSewParamDefaultData() {
    var result = MdcLibrary().apiBinding.getMdcSewParamDefaultData();
    lineParamDefault = result.lineParam;
    surfParamDefault = result.surfParam;
  }

  ///
  /// リセット
  ///
  void reset() {
    _currentIndex = 0;
    _regionInfoList.clear();
    _operation = Operation.single;
    displayImageInfo = const DisplayImageInfo();
    isProcessImage = false;
    dspImgBeforeProcessed = Uint8List.fromList([]);
    handle = 0;
  }

  ///
  /// お絵描きイメージ取得する
  ///
  void updateCanvasImage() {
    MdcReqProc reqProc = EditObjectModel().reqProc;
    reqProc = reqProc.copyWith(status: InstructionProgressStatus.single);

    var (
      errorCode: _,
      reqProc: outReqProc,
      backGroundImageInfo: backGroundImageInfo
    ) = MdcLibrary().apiBinding.getMdcCurrentDrawingImage(reqProc);
    EditObjectModel().reqProc = outReqProc;
    dspImgBeforeProcessed = backGroundImageInfo.imageData;
    EditObjectModel().setMdcBothImageInfo(
        updateParts: MdcImageInfo.empty(),
        updateBackground: backGroundImageInfo,
        needUpdateUI: false);
  }

  void setStitchModelValues(bool isMiltLink) {
    var patternInfo = getPatternInfo();
    switch (patternInfo.stitch.sewMode) {
      case MdcSewingKindsLine.roughZigzag:
        var result = MdcLibrary()
            .apiBinding
            .getMdcRoughZigzagLineParamInfo(patternInfo.regionNo, isMiltLink);
        LineRoughZigzagModel().setColor(result.color);
        LineRoughZigzagModel().setWidth(
          result.prm.width,
          isValueChange: false,
        );
        LineRoughZigzagModel().setDensityIndex(
          result.prm.density.index,
          isValueChange: false,
        );
        return;
      case MdcSewingKindsLine.running:
        var result = MdcLibrary()
            .apiBinding
            .getMdcRunningStitchLineParamInfo(patternInfo.regionNo, isMiltLink);
        LineRunningModel().setColor(result.color);
        LineRunningModel().setRunPitch(
          result.prm.pitch,
          isValueChange: false,
        );
        LineRunningModel().setUnderSewing(
          result.prm.underSewing,
          isValueChange: false,
        );
        return;
      case MdcSewingKindsLine.triple:
        var result = MdcLibrary()
            .apiBinding
            .getMdcTripleStitchLineParamInfo(patternInfo.regionNo, isMiltLink);
        LineTripleModel().setColor(result.color);
        LineTripleModel().setRunPitch(
          result.prm.pitch,
          isValueChange: false,
        );
        LineTripleModel().setUnderSewing(
          result.prm.underSewing,
          isValueChange: false,
        );
        return;
      case MdcSewingKindsLine.candleWicking:
        var result = MdcLibrary()
            .apiBinding
            .getMdcCandlwickingLineParamInfo(patternInfo.regionNo, isMiltLink);
        LineCandleModel().setColor(result.color);
        LineCandleModel().setSize(
          result.prm.size,
          isValueChange: false,
        );
        LineCandleModel().setSpace(
          result.prm.spacing,
          isValueChange: false,
        );
        return;
      case MdcSewingKindsLine.chain:
        var result = MdcLibrary()
            .apiBinding
            .getMdcChainStitchLineParamInfo(patternInfo.regionNo, isMiltLink);
        LineChainModel().setColor(result.color);
        LineChainModel().setSize(
          result.prm.size,
          isValueChange: false,
        );
        LineChainModel().setThickness(
          result.prm.thickness,
          isValueChange: false,
        );
        return;
      case MdcSewingKindsLine.eStitch:
        var result = MdcLibrary()
            .apiBinding
            .getMdcEStitchLineParamInfo(patternInfo.regionNo, isMiltLink);
        LineEStitchModel().setColor(result.color);
        LineEStitchModel().setWidth(
          result.prm.width,
          isValueChange: false,
        );
        LineEStitchModel().setSpace(
          result.prm.spacing,
          isValueChange: false,
        );
        LineEStitchModel().setThickness(
          result.prm.thickness,
          isValueChange: false,
        );
        LineEStitchModel().setFlip(
          result.prm.side,
          isValueChange: false,
        );
        return;
      case MdcSewingKindsLine.vStitch:
        var result = MdcLibrary()
            .apiBinding
            .getMdcVStitchLineParamInfo(patternInfo.regionNo, isMiltLink);
        LineVStitchModel().setColor(result.color);
        LineVStitchModel().setWidth(
          result.prm.width,
          isValueChange: false,
        );
        LineVStitchModel().setSpace(
          result.prm.spacing,
          isValueChange: false,
        );
        LineVStitchModel().setThickness(
          result.prm.thickness,
          isValueChange: false,
        );
        LineVStitchModel().setFlip(
          result.prm.side,
          isValueChange: false,
        );
        return;
      case MdcSewingKindsLine.motif:
        var result = MdcLibrary()
            .apiBinding
            .getMdcMotifLineParamInfo(patternInfo.regionNo, isMiltLink);
        LineMotifModel().setColor(result.color);
        LineMotifModel().setSize(
          result.prm.size,
          isValueChange: false,
        );
        LineMotifModel().setSpacing(
          result.prm.spacing,
          isValueChange: false,
        );
        LineMotifModel().setLineFlip(
          result.prm.side,
          isValueChange: false,
        );
        return;
      case MdcSewingKindsLine.zigzag:
        var result = MdcLibrary()
            .apiBinding
            .getMdcZigzagLineParamInfo(patternInfo.regionNo, isMiltLink);
        LineZigzagModel().setColor(result.color);
        LineZigzagModel().setWidth(
          result.value.width,
          isValueChange: false,
        );
        LineZigzagModel().setDensityIndex(
          result.value.density.index,
          isValueChange: false,
        );
        return;
      case MdcSewKindsSurface.tatami:
        var result = MdcLibrary()
            .apiBinding
            .getMdcTatamiSurfaceParamInfo(patternInfo.regionNo, isMiltLink);
        SurfaceTatamiModel().setColor(result.color);
        SurfaceTatamiModel().setDirection(
          result.prm.direction,
          result.prm.dirKind,
          isValueChange: false,
        );
        SurfaceTatamiModel().setDensityIndex(
          result.prm.density.index,
          isValueChange: false,
        );
        SurfaceTatamiModel().setPullCompensation(
          result.prm.pullCompensation,
          isValueChange: false,
        );
        SurfaceTatamiModel().setUnderSewing(
          result.prm.underSewing,
          isValueChange: false,
        );
        return;
      case MdcSewKindsSurface.stipring:
        var result = MdcLibrary()
            .apiBinding
            .getMdcStipplingSurfaceParamInfo(patternInfo.regionNo, isMiltLink);
        SurfaceStippleModel().setColor(result.color);
        SurfaceStippleModel().setRunPitch(
          result.prm.runPitch,
          isValueChange: false,
        );
        SurfaceStippleModel().setSpacing(
          result.prm.spacing,
          isValueChange: false,
        );
        SurfaceStippleModel().setDistance(
          result.prm.distance,
          isValueChange: false,
        );
        SurfaceStippleModel().setStitchLine(
          result.prm.stitch,
          isValueChange: false,
        );
        return;
      case MdcSewKindsSurface.decorativeFill:
        var result = MdcLibrary()
            .apiBinding
            .getMdcDecorativeFillSurfaceParamInfo(
                patternInfo.regionNo, isMiltLink);
        SurfaceDecoFillModel().setColor(result.codeColor);
        SurfaceDecoFillModel().setSize(
          result.prm.size,
          isValueChange: false,
        );
        SurfaceDecoFillModel().setDirection(
          result.prm.direction,
          isValueChange: false,
        );
        SurfaceDecoFillModel().setOutline(
          result.prm.outline,
          isValueChange: false,
        );
        SurfaceDecoFillModel().setRandomShift(
          result.prm.randomShift,
          result.prm.randomShiftType.index,
          isValueChange: false,
        );
        SurfaceDecoFillModel().setPositionOffset(
          result.prm.positionOffsetX,
          result.prm.positionOffsetY,
          isValueChange: false,
        );
        SurfaceDecoFillModel().setThickness(
          result.prm.thickness.index,
          isValueChange: false,
        );
        return;
      default:
        return;
    }
  }

  ///
  /// 一括指定 かつ 色が複数リージョンで一致しない場合、指定リージョンの色を取得する
  ///
  ColorCode getEditRegionColor() {
    var patternInfo = getPatternInfo();
    switch (patternInfo.stitch.sewMode) {
      case MdcSewingKindsLine.roughZigzag:
        final result = MdcLibrary()
            .apiBinding
            .getMdcRoughZigzagLineParamInfo(patternInfo.regionNo, false);
        return result.color;
      case MdcSewingKindsLine.running:
        final result = MdcLibrary()
            .apiBinding
            .getMdcRunningStitchLineParamInfo(patternInfo.regionNo, false);
        return result.color;
      case MdcSewingKindsLine.triple:
        final result = MdcLibrary()
            .apiBinding
            .getMdcTripleStitchLineParamInfo(patternInfo.regionNo, false);
        return result.color;
      case MdcSewingKindsLine.candleWicking:
        final result = MdcLibrary()
            .apiBinding
            .getMdcCandlwickingLineParamInfo(patternInfo.regionNo, false);
        return result.color;
      case MdcSewingKindsLine.chain:
        final result = MdcLibrary()
            .apiBinding
            .getMdcChainStitchLineParamInfo(patternInfo.regionNo, false);
        return result.color;
      case MdcSewingKindsLine.eStitch:
        final result = MdcLibrary()
            .apiBinding
            .getMdcEStitchLineParamInfo(patternInfo.regionNo, false);
        return result.color;
      case MdcSewingKindsLine.vStitch:
        final result = MdcLibrary()
            .apiBinding
            .getMdcVStitchLineParamInfo(patternInfo.regionNo, false);
        return result.color;
      case MdcSewingKindsLine.motif:
        final result = MdcLibrary()
            .apiBinding
            .getMdcMotifLineParamInfo(patternInfo.regionNo, false);
        return result.color;
      case MdcSewingKindsLine.zigzag:
        final result = MdcLibrary()
            .apiBinding
            .getMdcZigzagLineParamInfo(patternInfo.regionNo, false);
        return result.color;
      case MdcSewKindsSurface.tatami:
        final result = MdcLibrary()
            .apiBinding
            .getMdcTatamiSurfaceParamInfo(patternInfo.regionNo, false);
        return result.color;
      case MdcSewKindsSurface.stipring:
        final result = MdcLibrary()
            .apiBinding
            .getMdcStipplingSurfaceParamInfo(patternInfo.regionNo, false);
        return result.color;
      case MdcSewKindsSurface.decorativeFill:
        final result = MdcLibrary()
            .apiBinding
            .getMdcDecorativeFillSurfaceParamInfo(patternInfo.regionNo, false);
        return result.codeColor;
      default:
        return ColorCode.black;
    }
  }
}
