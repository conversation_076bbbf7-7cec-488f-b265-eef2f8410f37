import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:xd_component/xd_component.dart';
import 'select_information_popup_view_model.dart';

///
/// インフォメーション画面
///
class SelectInformationPopup extends ConsumerStatefulWidget {
  const SelectInformationPopup({super.key});

  @override
  ConsumerState<SelectInformationPopup> createState() =>
      _SelectInformationPopupState();
}

class _SelectInformationPopupState
    extends ConsumerState<SelectInformationPopup> {
  @override
  Widget build(BuildContext context) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(selectInformationPopupViewModelProvider);
    final viewModel =
        ref.read(selectInformationPopupViewModelProvider.notifier);

    return Column(
      children: [
        const Spacer(flex: 79),
        Expanded(
          flex: 1132,
          child: Row(
            children: [
              const Spacer(flex: 6),
              Expanded(
                flex: 788,
                child: Material(
                  color: Colors.transparent,
                  child: Stack(
                    children: [
                      const pic_popup_size788x1132(),

                      /// "Preview"
                      Column(
                        children: [
                          const Spacer(flex: 96),
                          Expanded(
                            flex: 525,
                            child: Row(
                              children: [
                                const Spacer(flex: 10),
                                Expanded(
                                  flex: 768,
                                  child: Stack(
                                    children: [
                                      /// 背景色
                                      Container(
                                        color: state.isBackGroundColorOn
                                            ? blackBackgroundColor
                                            : Colors.white,
                                      ),

                                      /// 背景画像
                                      state.isImageDisplayOn &&
                                              state.backGroundImage != null
                                          ? Row(
                                              children: [
                                                const Spacer(flex: 232),
                                                Expanded(
                                                  flex: 303,
                                                  child: Center(
                                                    child:
                                                        state.backGroundImage!,
                                                  ),
                                                ),
                                                const Spacer(flex: 233),
                                              ],
                                            )
                                          : Container(),

                                      /// 画像
                                      state.image ?? Container(),
                                    ],
                                  ),
                                ),
                                const Spacer(flex: 10),
                              ],
                            ),
                          ),
                          const Spacer(flex: 511),
                        ],
                      ),

                      Column(
                        children: [
                          Expanded(
                            flex: 90,
                            child: Stack(
                              children: [
                                Row(
                                  children: [
                                    const Spacer(flex: 18),
                                    const Expanded(
                                      flex: 37,
                                      child: Column(
                                        children: [
                                          Spacer(flex: 18),
                                          Expanded(
                                            flex: 31,
                                            child: ico_zukei_size_black(),
                                          ),
                                          Spacer(flex: 41),
                                        ],
                                      ),
                                    ),
                                    const Spacer(flex: 10),
                                    Expanded(
                                      flex: 80,
                                      child: Column(
                                        children: [
                                          const Spacer(flex: 11),
                                          Expanded(
                                            flex: 39,
                                            child: grp_str_number1_01(
                                              text: state.heightValue,
                                            ),
                                          ),
                                          const Spacer(flex: 1),
                                          Expanded(
                                            flex: 39,
                                            child: grp_str_number2_01(
                                              text: state.widthValue,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Spacer(flex: 1),
                                    state.isInch == false
                                        ? Expanded(
                                            flex: 36,
                                            child: Column(
                                              children: [
                                                const Spacer(flex: 22),
                                                Expanded(
                                                  flex: 26,
                                                  child: grp_str_mm1_01(
                                                    text: l10n.icon_00225,
                                                  ),
                                                ),
                                                const Spacer(flex: 14),
                                                Expanded(
                                                  flex: 26,
                                                  child: grp_str_mm2_01(
                                                    text: l10n.icon_00225,
                                                  ),
                                                ),
                                                const Spacer(flex: 2),
                                              ],
                                            ),
                                          )
                                        : Expanded(
                                            flex: 36,
                                            child: Column(
                                              children: [
                                                const Spacer(flex: 14),
                                                Expanded(
                                                  flex: 26,
                                                  child: Container(
                                                    alignment:
                                                        Alignment.topLeft,
                                                    child: Text(
                                                      l10n.icon_00226,
                                                      textAlign: TextAlign.left,
                                                      style: const TextStyle(
                                                        fontFamily: "Roboto",
                                                        fontSize: 20,
                                                        height: 1,
                                                        color: Colors.black,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                const Spacer(flex: 14),
                                                Expanded(
                                                  flex: 26,
                                                  child: Container(
                                                    alignment:
                                                        Alignment.topLeft,
                                                    child: Text(
                                                      l10n.icon_00226,
                                                      textAlign: TextAlign.left,
                                                      style: const TextStyle(
                                                        fontFamily: "Roboto",
                                                        fontSize: 20,
                                                        height: 1,
                                                        color: Colors.black,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                const Spacer(flex: 10),
                                              ],
                                            ),
                                          ),
                                    const Spacer(flex: 18),
                                    const Expanded(
                                      flex: 20,
                                      child: Column(
                                        children: [
                                          Spacer(flex: 18),
                                          Expanded(
                                            flex: 63,
                                            child: ico_needle_counts_icon(),
                                          ),
                                          Spacer(flex: 9),
                                        ],
                                      ),
                                    ),
                                    const Spacer(flex: 7),
                                    Expanded(
                                      flex: 106,
                                      child: Column(
                                        children: [
                                          const Spacer(flex: 11),
                                          const Expanded(
                                            flex: 39,
                                            child: grp_str_needlenumber1(
                                              text: '0',
                                            ),
                                          ),
                                          const Spacer(flex: 1),
                                          Expanded(
                                            flex: 39,
                                            child: grp_str_needlenumber2(
                                              text: state.totalNeedleCountValue,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Spacer(flex: 20),
                                    const Expanded(
                                      flex: 35,
                                      child: Column(
                                        children: [
                                          Spacer(flex: 18),
                                          Expanded(
                                            flex: 63,
                                            child: ico_thread_counts_icon(),
                                          ),
                                          Spacer(flex: 9),
                                        ],
                                      ),
                                    ),
                                    const Spacer(flex: 7),
                                    Expanded(
                                      flex: 54,
                                      child: Column(
                                        children: [
                                          const Spacer(flex: 11),
                                          const Expanded(
                                            flex: 39,
                                            child: grp_str_threadnumber1_01(
                                              text: '0',
                                              color: Colors.black,
                                            ),
                                          ),
                                          const Spacer(flex: 1),
                                          Expanded(
                                            flex: 39,
                                            child: grp_str_threadnumber2(
                                              text: state.totalThreadValue,
                                              color: Colors.black,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Spacer(flex: 20),
                                    const Expanded(
                                      flex: 35,
                                      child: Column(
                                        children: [
                                          Spacer(flex: 18),
                                          Expanded(
                                            flex: 63,
                                            child: ico_time_counts_icon_black(),
                                          ),
                                          Spacer(flex: 9),
                                        ],
                                      ),
                                    ),
                                    const Spacer(flex: 7),
                                    Expanded(
                                      flex: 54,
                                      child: Column(
                                        children: [
                                          const Spacer(flex: 11),
                                          const Expanded(
                                            flex: 39,
                                            child: grp_str_timenumber1(
                                              text: '0',
                                              color: Colors.black,
                                            ),
                                          ),
                                          const Spacer(flex: 1),
                                          Expanded(
                                            flex: 39,
                                            child: grp_str_timenumber2(
                                              text: state.totalTimeValue,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const Spacer(flex: 1),
                                    Expanded(
                                      flex: 45,
                                      child: Column(
                                        children: [
                                          const Spacer(flex: 22),
                                          Expanded(
                                            flex: 26,
                                            child: grp_str_mm1_01(
                                              text: l10n.icon_00039,
                                            ),
                                          ),
                                          const Spacer(flex: 14),
                                          Expanded(
                                            flex: 26,
                                            child: grp_str_mm1_01(
                                              text: l10n.icon_00039,
                                            ),
                                          ),
                                          const Spacer(flex: 2),
                                        ],
                                      ),
                                    ),
                                    const Spacer(flex: 117),
                                    Expanded(
                                      flex: 63,
                                      child: Column(
                                        children: [
                                          const Spacer(flex: 15),
                                          Expanded(
                                            flex: 63,
                                            child: state.isPrintAndStitchPattern
                                                ? CustomTooltip(
                                                    message:
                                                        l10n.tt_emb_realpreview,
                                                    child:
                                                        grp_btn_emb_realpreview_01(
                                                      state: state
                                                              .isImageDisplayOn
                                                          ? ButtonState.normal
                                                          : ButtonState.disable,
                                                      onTap: () => viewModel
                                                          .onRealPreviewClick(),
                                                    ))
                                                : Container(),
                                          ),
                                          const Spacer(flex: 12),
                                        ],
                                      ),
                                    ),
                                    const Spacer(flex: 9),
                                  ],
                                ),
                                Row(
                                  children: [
                                    const Spacer(flex: 224),
                                    Expanded(
                                      flex: 106,
                                      child: Column(
                                        children: [
                                          const Spacer(flex: 49),
                                          Expanded(
                                            flex: 1,
                                            child: Container(
                                              color: const Color(0xFF737373),
                                            ),
                                          ),
                                          const Spacer(flex: 40),
                                        ],
                                      ),
                                    ),
                                    const Spacer(flex: 178),
                                    Expanded(
                                      flex: 100,
                                      child: Column(
                                        children: [
                                          const Spacer(flex: 49),
                                          Expanded(
                                            flex: 1,
                                            child: Container(
                                              color: const Color(0xFF737373),
                                            ),
                                          ),
                                          const Spacer(flex: 40),
                                        ],
                                      ),
                                    ),
                                    const Spacer(flex: 180),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const Spacer(flex: 297),
                          Expanded(
                            flex: 70,
                            child: Row(
                              children: [
                                const Spacer(flex: 677),
                                Expanded(
                                  flex: 98,
                                  child: state.isPrintAndStitchPattern
                                      ? CustomTooltip(
                                          message: l10n.tt_emb_infoprintimage,
                                          child: grp_btn_imagedisplay(
                                            state: state.isImageDisplayOn
                                                ? ButtonState.select
                                                : ButtonState.normal,
                                            onTap:
                                                viewModel.onImageDisplayClick,
                                          ),
                                        )
                                      : Container(),
                                ),
                                const Spacer(flex: 13),
                              ],
                            ),
                          ),
                          const Spacer(flex: 8),
                          Expanded(
                            flex: 70,
                            child: Row(
                              children: [
                                const Spacer(flex: 677),
                                Expanded(
                                  flex: 98,
                                  child: state.isPrintAndStitchPattern
                                      ? CustomTooltip(
                                          message: l10n.tt_emb_infooutputfiles,
                                          child: grp_btn_imagesave(
                                            onTap: () => viewModel
                                                .onImageSaveClick(context),
                                          ),
                                        )
                                      : Container(),
                                ),
                                const Spacer(flex: 13),
                              ],
                            ),
                          ),
                          const Spacer(flex: 8),
                          Expanded(
                            flex: 70,
                            child: Row(
                              children: [
                                const Spacer(flex: 677),
                                Expanded(
                                  flex: 98,
                                  child: grp_stbtn_sewutilityneedl_select(
                                    isSelect: state.isBackGroundColorOn,
                                    onTap: viewModel.onBackgroundClick,
                                  ),
                                ),
                                const Spacer(flex: 13),
                              ],
                            ),
                          ),
                          const Spacer(flex: 16),
                          Expanded(
                            flex: 402,
                            child: Row(
                              children: [
                                const Spacer(flex: 10),
                                Expanded(
                                  flex: 767,
                                  child: Stack(
                                    children: [
                                      const pic_base_information(),
                                      Column(
                                        children: [
                                          const Spacer(flex: 12),
                                          Expanded(
                                            flex: 355,
                                            child: Row(
                                              children: [
                                                const Spacer(flex: 12),
                                                Expanded(
                                                  flex: 743,
                                                  child: state
                                                          .threadInfoDisplayList
                                                          .isNotEmpty
                                                      ? grp_grid_threadlist(
                                                          controller: state
                                                              .scrollController,
                                                          displayList: state
                                                              .threadInfoDisplayList,
                                                        )
                                                      : Container(),
                                                ),
                                                const Spacer(flex: 12),
                                              ],
                                            ),
                                          ),
                                          const Spacer(flex: 11),
                                          Expanded(
                                            flex: 8,
                                            child: Row(
                                              children: [
                                                const Spacer(flex: 12),
                                                Expanded(
                                                  flex: 728,
                                                  child: state
                                                          .threadInfoDisplayList
                                                          .isNotEmpty
                                                      ? CustomScrollbar(
                                                          controller: state
                                                              .scrollController,
                                                          visibilityWhenScrollFull:
                                                              false,
                                                          direction:
                                                              Axis.horizontal)
                                                      : Container(),
                                                ),
                                                const Spacer(flex: 12),
                                              ],
                                            ),
                                          ),
                                          const Spacer(flex: 16),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                const Spacer(flex: 11),
                              ],
                            ),
                          ),
                          const Spacer(flex: 9),
                          Expanded(
                            flex: 80,
                            child: Row(
                              children: [
                                const Spacer(flex: 12),
                                Expanded(
                                  flex: 152,
                                  child: grp_btn_negative(
                                    text: l10n.icon_close_1,
                                    onTap: () =>
                                        viewModel.onCloseButtonClick(context),
                                  ),
                                ),
                                const Spacer(flex: 459),
                                Expanded(
                                  flex: 152,
                                  child: grp_btn_embroidery_01(
                                    text: l10n.icon_00038_1,
                                    onTap: () =>
                                        viewModel.onSetButtonClick(context),
                                  ),
                                ),
                                const Spacer(flex: 13),
                              ],
                            ),
                          ),
                          const Spacer(flex: 12),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const Spacer(flex: 6),
            ],
          ),
        ),
        const Spacer(flex: 69),
      ],
    );
  }
}
