import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../model/edit_model.dart' show ToolbarPopupId;
import '../../../../model/preview_model.dart'
    show
        PatternDisplayInfo,
        PointPositionType,
        ThreadMarkInfo,
        RedPointDisplayInfo;

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'preview_view_interface.freezed.dart';

@freezed
class PreviewState with _$PreviewState {
  const factory PreviewState({
    /// Pattern表示情報
    required List<PatternDisplayInfo> patternDisplayInfoList,

    /// 矩形選択ツールを使用できるかどうか
    required bool isRectangularMarqueeEnable,

    /// Previewの表示領域をドラッグできますか
    required bool isDragPreviewEnable,

    /// maskボックスがPreview表示領域を超えていないか
    /// 超えた場合、赤いドットは表示されません
    required bool isRedPointOverPreview,

    /// セッティング画面　刺しゅう２　（ページ：9）
    /// 背景色
    required Color backgroundColor,

    /// 赤枠の色
    required Color maskColor,

    /// セッティング画面　刺しゅう１　（ページ：8）
    /// グリッド
    required int gridTypeIndex,

    /// グリッドグリッド
    required Color gridColor,

    /// 枠サイズ
    required Path frameDrawPath,

    /// 枠サイズ色
    required Color frameColor,

    /// 黒点行列
    required List<Offset> blackPoints,

    /// 拡大すると、これを中心に周囲に広がります
    /// この点に疑問がある場合は、この変数をViewに描画することができます(_viewCenterとの結合理解)
    required Offset zoomOutPoint,

    /// 分割線
    required Path cutLinePath,

    /// ページ上の糸印の表示情報
    required List<ThreadMarkInfo> threadMarkInfoList,

    /// 投影がオンになっているかどうか
    required bool isProjectorON,

    /// 現在表示しているページ
    required ToolbarPopupId popupType,

    /// preview無効かどうか
    required bool isPreviewClickEnable,

    /// 長方形選択ツールの領域
    Rect? rectangularMarqueeArea,

    /// 背景スキャンの画像
    Widget? backgroundImage,

    /// Borderのmarkページでは、 mask表示されます
    Rect? borderMarkMaskRect,

    /// ナイフの表示
    Widget? knifeDisplayWidget,

    /// ドラッグまたは長押しで設定された角度
    double? dragAngle,

    /// 赤い点の表示情報。
    RedPointDisplayInfo? redPointInfo,
  }) = _PreviewState;
}

abstract class PreviewViewModelInterface extends ViewModel<PreviewState> {
  PreviewViewModelInterface(
    super.state,
    this.ref,
  );

  ///
  /// providerのref
  ///
  Ref ref;

  ///
  /// Preview領域の画面幅と高さを取得する
  ///
  Offset get getPreviewSize;

  ///
  /// Zoomの拡大倍率を取得
  ///
  double get getMagnification;

  ///
  /// maskの追加タッチ領域サイズ
  ///
  double get getMaskOutArea;

  ///
  /// Rotateの前に、Rotate中にUIで使用する必要があるデータをバックアップします
  ///
  void backupPatternDisplayInfoInRotate();

  ///
  /// キーを長押しするとドットを使って「rotate」操作をするときの更新関数
  ///
  void updateByRotateLongPress();

  ///
  /// Preview空白領域のクリック関数
  ///
  void onPreviewSpaceAreaClicked();

  ///
  /// preview無効のクリック関数
  ///
  void onPreviewDisableClicked();

  ///
  /// パターンの開始情報をバックアップする
  ///
  void backupPatternDisplayInfoInMove();

  ///
  /// ドラッグ開始時に呼び出す
  ///
  void dargPatternStart(ScaleStartDetails details);

  ///
  /// Patternを選択してドラッグ
  ///
  void dargPattern(ScaleUpdateDetails details);

  ///
  /// ドラッグ終了時に呼び出す
  ///
  void dargPatternEnd();

  ///
  /// 矢印キーを使用してパターンを移動します
  ///
  /// [bool] : 移動時にエッジに到達しているかどうか
  ///          true:端に移動されました false:まだエッジに移行していません
  ///
  bool moveByArrowKey();

  ///
  /// 模様非透明領域のクリックイベント
  ///
  void onPatternNoOpacityAreaClicked(
      {required int displayIndex, required int sewingIndex});

  ///
  /// 模様の押下事件
  ///
  void onPatternAllAreaTapDown(
      {required int displayIndex, required int sewingIndex});

  ///
  /// 模様の持ち上げ事件
  ///
  void onPatternAllAreaTapUp();

  ///
  /// Rotateドットのドラッグ開始時の関数
  ///
  void dargRotatePointStart(Offset center, double angle, Offset startPoint);

  ///
  /// Rotateドットドラッグ時の関数
  ///
  void dargRotatePoint(Offset currentPoint);

  ///
  /// Rotateドットのドラッグ終了時の関数
  ///
  void dargRotatePointEnd();

  ///
  /// Sizeドットのドラッグ開始時の関数
  ///
  void dargSizePointStart(Offset centerPoint);

  ///
  /// Sizeドットドラッグ時の関数
  ///
  void dargSizePoint(Offset offset, PointPositionType type);

  ///
  /// Sizeドットのドラッグ終了時の関数
  ///
  void dargSizePointEnd();

  ///
  /// 縦線リストの取得
  ///
  List<double> getGridVerticalList();

  ///
  /// 横線リストの取得
  ///
  List<double> getGridHorizontalList();

  ///
  /// ドラッグして矩形選択を使用して開始するとき
  ///
  void useRectangularMarqueeStart(Offset startPoint);

  ///
  /// ドラッグして矩形選択を使用します。
  ///
  void useRectangularMarquee(Offset point);

  ///
  /// ドラッグして矩形選択を使用して終了する場合
  ///
  void useRectangularMarqueeEnd();

  ///
  /// Preview領域の移動開始時
  ///
  void dargPreviewStart(ScaleStartDetails details);

  ///
  /// Preview領域を移動する場合
  ///
  void dargPreview(ScaleUpdateDetails details);
}
