import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_decorative_fill_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'direction_setting_popup_view_interface.dart';

final directionSettingPopupViewModelProvider =
    StateNotifierProvider.autoDispose<DirectionSettingPopupStateViewInterface,
            DirectionSettingPopupState>(
        (ref) => DirectionSettingPopupViewModel(ref));

class DirectionSettingPopupViewModel
    extends DirectionSettingPopupStateViewInterface {
  DirectionSettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const DirectionSettingPopupState(), ref) {
    _directionState = SurfaceDecoFillModel().getDirection() !=
            SurfaceDecoFillModel.directionNotUpdating
        ? DecoFillSettingState.settingCompleted
        : DecoFillSettingState.unknown;

    /// view更新
    update();
  }

  ///
  /// 最大値
  ///
  final int _maxDirectionValue = 345;

  ///
  /// 最小値
  ///
  final int _minDirectionValue = 0;

  ///
  /// ステップ量
  ///
  final int _stepValue = 15;

  ///
  /// 傾き方向の状態
  ///
  DecoFillSettingState _directionState = DecoFillSettingState.settingCompleted;

  ///
  /// 傾き方向値
  ///
  int _directionValue = SurfaceDecoFillModel().getDirection();

  @override
  void update() {
    state = state.copyWith(settingValue: _getDirectionValue());
  }

  @override
  bool onMinusButtonClicked(bool isLongPress) {
    if (_directionState == DecoFillSettingState.unknown) {
      _directionValue = SurfaceDecoFillModel().directionDefaultValue;
    } else {
      _directionValue = _directionValue - _stepValue;
      if (_directionValue < _minDirectionValue) {
        _directionValue = _maxDirectionValue;
      }
    }

    _directionState = DecoFillSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    state = state.copyWith(settingValue: _getDirectionValue());

    return true;
  }

  @override
  bool onPlusButtonClicked(bool isLongPress) {
    if (_directionState == DecoFillSettingState.unknown) {
      _directionValue = SurfaceDecoFillModel().directionDefaultValue;
    } else {
      _directionValue = _directionValue + _stepValue;
      if (_directionValue > _maxDirectionValue) {
        _directionValue = _minDirectionValue;
      }
    }

    _directionState = DecoFillSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    state = state.copyWith(settingValue: _getDirectionValue());

    return true;
  }

  @override
  void onOkButtonClicked() {
    ref.read(stitchPageViewModelProvider.notifier).maybeRemovePopupRoute(
        PopupEnum.surfaceDecorativeFillDirection.toString());
    if (_directionState == DecoFillSettingState.unknown) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    /// directionの初期値
    int oldDirectionValue = SurfaceDecoFillModel().getDirection();

    /// Model 更新
    SurfaceDecoFillModel().setDirection(_directionValue);
    if (SurfaceDecoFillModel().setDecorativeFillSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (oldDirectionValue != _directionValue) {
      ResumeHistoryModel().backSnapshot();
    } else {
      /// do nothing
    }

    _directionState = DecoFillSettingState.settingCompleted;

    CreationModel().changeStitchCreation();
  }

  @override
  bool getDirectionTextStyle() {
    if (_directionState == DecoFillSettingState.unknown) {
      return true;
    }

    if (_directionValue == SurfaceDecoFillModel().directionDefaultValue) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 傾き方向表示値を取得する
  ///
  String _getDirectionValue() {
    if (_directionState == DecoFillSettingState.unknown) {
      return SurfaceDecoFillModel.oneStarValue;
    } else {
      return _directionValue.toString();
    }
  }
}
