// ignore_for_file: always_specify_types
// ignore_for_file: camel_case_types
// ignore_for_file: non_constant_identifier_names

// AUTO GENERATED FILE, DO NOT EDIT.
//
// Generated by `package:ffigen`.
// ignore_for_file: type=lint
import 'dart:convert';
import 'dart:ffi' as ffi;
import 'dart:ffi';
import 'dart:io';
import 'dart:typed_data';

import 'package:ffi/ffi.dart' as ffi;
import 'package:flutter/material.dart';
import 'package:ph_eel_plugin/utledit_bindings_generated.dart';
import 'package:ph_eel_plugin/embedit_bindings_generated.dart';
import 'package:synchronized/synchronized.dart';

class MediaFileExtension {
  /// media_rw にマウントされているメディアがあるか確認
  /// 最初に見つかったメディアへのパスを返す
  ///
  /// return: e.g. `"/mnt/media_rw/04FE-0305"`
  static String findRootPath() {
    final mediaPath = "/mnt/media_rw/";
    // media_rw内を検索して最初に見つかったメディアへのパスを取得
    Directory dir = Directory(mediaPath);
    debugPrint("search $mediaPath ...");
    String foundMediaPath = "";
    var list = dir.listSync();
    for (var p in list) {
      foundMediaPath = p.path;
      if (foundMediaPath.isEmpty == false) {
        break;
      }
      foundMediaPath = "";
    }
    if (foundMediaPath.isEmpty) {
      // メディアが存在しない
      debugPrint("mount media is not found.");
      return foundMediaPath;
    }
    return foundMediaPath;
  }

  /// 最初に見つかったメディアのDirectory内に指定した拡張子のファイルが存在するか検索, 最初に見つかったファイルのフルパスを返す
  ///
  /// directory: e.g. `"/"`
  ///
  /// fileExtension: e.g. `".EEP"`
  ///
  /// return: e.g. `/mnt/media_rw/04FE-0305/IIVO_EEP.EEP`
  static String find(String directory, String fileExtension) {
    final mediaPath = "/mnt/media_rw/";
    // media_rw内を検索して最初に見つかったメディアへのパスを取得
    Directory dir = Directory(mediaPath);
    debugPrint("search $mediaPath ...");
    String foundMediaPath = "";
    var list = dir.listSync();
    for (var p in list) {
      foundMediaPath = p.path;
      if (foundMediaPath.isEmpty == false) {
        break;
      }
      foundMediaPath = "";
    }
    if (foundMediaPath.isEmpty) {
      // メディアが存在しない
      debugPrint("mount media is not found.");
      return "";
    }
    debugPrint("found: $foundMediaPath");
    String directoryPath = "$foundMediaPath$directory";
    String foundFilePath = "";
    dir = Directory(directoryPath);
    var fileList = dir.listSync();
    for (var p in fileList) {
      foundFilePath = p.path;
      if (foundFilePath.isEmpty == false) {
        if (foundFilePath.toUpperCase().endsWith(fileExtension)) {
          break;
        }
      }
      foundFilePath = "";
    }
    if (foundFilePath.isEmpty) {
      // ファイルが存在しない
      debugPrint("$directoryPath*$fileExtension is not found.");
      return "";
    }
    debugPrint("found: $foundFilePath");

    // ファイルが存在するか念のため再確認
    if (File(foundFilePath).existsSync()) {
      return foundFilePath;
    } else {
      return "";
    }
  }
}

extension ArrayCharBlobConversion on Array<Char> {
  /// Array<Char> to String
  /// e.g. ['E','3','B','5','3','0','1','9','4'] to "E3B530194"
  String join() {
    final stringList = <int>[];
    var i = 0;
    while (this[i] != 0) {
      stringList.add(this[i]);
      i++;
    }
    return String.fromCharCodes(stringList);
  }
}

extension StringBlobConversion on String {
  /// String to Uint8List
  Uint8List toAsciiEncode() {
    const asciiEncoder = AsciiEncoder();
    final Uint8List stringList = asciiEncoder.convert(this);
    return stringList;
  }

  /// String to Pointer<Uint8>
  Pointer<Uint8> toUint8Pointer() {
    final Uint8List stringList = this.toAsciiEncode();
    final blob = ffi.calloc<Uint8>(stringList.length);
    final blobBytes = blob.asTypedList(stringList.length);
    blobBytes.setAll(0, stringList);
    return blob;
  }
}

extension Uint8ListBlobConversion on Uint8List {
  /// Allocates a pointer filled with the Uint8List data.
  Pointer<Uint8> allocatePointer() {
    final blob = ffi.calloc<Uint8>(length);
    final blobBytes = blob.asTypedList(length);
    blobBytes.setAll(0, this);
    return blob;
  }
}

/// Bindings for `src/TpDgbRes.h`.
///
/// Regenerate bindings with `flutter pub run ffigen --config ffigen.yaml`.
///
class TpDgbBindings {
  /// Holds the symbol lookup function.
  final ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
      _lookup;

  /// The symbols are looked up in [dynamicLibrary].
  TpDgbBindings(ffi.DynamicLibrary dynamicLibrary)
      : _lookup = dynamicLibrary.lookup;

  /// The symbols are looked up with [lookup].
  TpDgbBindings.fromLookup(
      ffi.Pointer<T> Function<T extends ffi.NativeType>(String symbolName)
          lookup)
      : _lookup = lookup;

////////////////////////////////////
//   //画面毎の状態通知関数
//   int BPIFRegisterStateCallback(
//       Pointer<NativeFunction<StateCallback>> callback) {
//     return _BPIFRegisterStateCallback(
//       callback,
//     );
//   }
//
//   late final _BPIFRegisterStateCallbackPtr = _lookup<
//           ffi.NativeFunction<
//               ffi.Int Function(Pointer<NativeFunction<StateCallback>>)>>(
//       'BPIFRegisterStateCallback');
//   late final _BPIFRegisterStateCallback = _BPIFRegisterStateCallbackPtr
//       .asFunction<int Function(Pointer<NativeFunction<StateCallback>>)>();
//
//   //エラー通知のコールバック関数
//   int BPIFRegisterErrorCallback(
//       Pointer<NativeFunction<ErrorCallback>> callback) {
//     return _BPIFRegisterErrorCallback(
//       callback,
//     );
//   }
//
//   late final _BPIFRegisterErrorCallbackPtr = _lookup<
//           ffi.NativeFunction<
//               ffi.Int Function(Pointer<NativeFunction<ErrorCallback>>)>>(
//       'BPIFRegisterErrorCallback');
//   late final _BPIFRegisterErrorCallback = _BPIFRegisterErrorCallbackPtr
//       .asFunction<int Function(Pointer<NativeFunction<ErrorCallback>>)>();
//
//   //Beep音通知委
//   int BPIFRegisterBeepCallback(Pointer<NativeFunction<BeepCallback>> callback) {
//     return _BPIFRegisterBeepCallback(
//       callback,
//     );
//   }
//
//   late final _BPIFRegisterBeepCallbackPtr = _lookup<
//           ffi.NativeFunction<
//               ffi.Int Function(Pointer<NativeFunction<BeepCallback>>)>>(
//       'BPIFRegisterBeepCallback');
//   late final _BPIFRegisterBeepCallback = _BPIFRegisterBeepCallbackPtr
//       .asFunction<int Function(Pointer<NativeFunction<BeepCallback>>)>();
//
//   //状態通知用のコールバックを停止する
//   void BPIFUnregisterStateCallback() {
//     return _BPIFUnregisterStateCallback();
//   }
//
//   late final _BPIFUnregisterStateCallbackPtr =
//       _lookup<ffi.NativeFunction<ffi.Void Function()>>(
//           'BPIFUnregisterStateCallback');
//   late final _BPIFUnregisterStateCallback =
//       _BPIFUnregisterStateCallbackPtr.asFunction<void Function()>();
//
//   //エラー状態通知用のコールバックを停止する
//   void BPIFUnregisterErrorCallback() {
//     return _BPIFUnregisterErrorCallback();
//   }
//
//   late final _BPIFUnregisterErrorCallbackPtr =
//       _lookup<ffi.NativeFunction<ffi.Void Function()>>(
//           'BPIFUnregisterErrorCallback');
//   late final _BPIFUnregisterErrorCallback =
//       _BPIFUnregisterErrorCallbackPtr.asFunction<void Function()>();
//
//   //Beep音エ通知用のコールバックを停止する
//   void BPIFUnregisterBeepCallback() {
//     return _BPIFUnregisterBeepCallback();
//   }
//
//   late final _BPIFUnregisterBeepCallbackPtr =
//       _lookup<ffi.NativeFunction<ffi.Void Function()>>(
//           'BPIFUnregisterBeepCallback');
//   late final _BPIFUnregisterBeepCallback =
//       _BPIFUnregisterBeepCallbackPtr.asFunction<void Function()>();

  int BPIFSendDisplayData(
    int send_msg_id,
  ) {
    return _BPIFSendDisplayData(
      send_msg_id,
    );
  }

  late final _BPIFSendDisplayDataPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Int)>>(
          'BPIFSendDisplayData');
  late final _BPIFSendDisplayData =
      _BPIFSendDisplayDataPtr.asFunction<int Function(int)>();

  int BPIFSendDisplayDataSync(
    int send_msg_id,
  ) {
    return _BPIFSendDisplayDataSync(
      send_msg_id,
    );
  }

  late final _BPIFSendDisplayDataSyncPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Int)>>(
          'BPIFSendDisplayDataSync');
  late final _BPIFSendDisplayDataSync =
      _BPIFSendDisplayDataSyncPtr.asFunction<int Function(int)>();

  int BPIFInit(
    ffi.Pointer<ffi.Char> work_folder_path,
  ) {
    return _BPIFInit(
      work_folder_path,
    );
  }

  late final _BPIFInitPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.Char>)>>(
          'BPIFInit');
  late final _BPIFInit =
      _BPIFInitPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int BPIFInitMinimum(
    ffi.Pointer<ffi.Char> work_folder_path,
  ) {
    return _BPIFInitMinimum(
      work_folder_path,
    );
  }

  late final _BPIFInitMinimumPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.Char>)>>(
          'BPIFInitMinimum');
  late final _BPIFInitMinimum =
      _BPIFInitMinimumPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  int BPIFInit1st() {
    return _BPIFInit1st();
  }

  late final _BPIFInit1stPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function()>>('BPIFInit1st');
  late final _BPIFInit1st = _BPIFInit1stPtr.asFunction<int Function()>();

  int BPIFInit2nd(
    ffi.Pointer<ffi.Char> work_folder_path,
  ) {
    return _BPIFInit2nd(
      work_folder_path,
    );
  }

  late final _BPIFInit2ndPtr =
      _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Pointer<ffi.Char>)>>(
          'BPIFInit2nd');
  late final _BPIFInit2nd =
      _BPIFInit2ndPtr.asFunction<int Function(ffi.Pointer<ffi.Char>)>();

  void BPIFSetTestMode(int mode) {
    _BPIFSetTestMode(mode);
  }

  late final _BPIFSetTestModePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Int32)>>(
          'BPIFSetTestMode');
  late final _BPIFSetTestMode =
      _BPIFSetTestModePtr.asFunction<void Function(int)>();

  void BPIFGetError(
    ffi.Pointer<ErrorInfo_t> tmpError,
  ) {
    return _BPIFGetError(
      tmpError,
    );
  }

  late final _BPIFGetErrorPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ErrorInfo_t>)>>(
          'BPIFGetError');
  late final _BPIFGetError =
      _BPIFGetErrorPtr.asFunction<void Function(ffi.Pointer<ErrorInfo_t>)>();

  // void BPIFGetState(
  //   ffi.Pointer<ffi.Void> tmpState,
  // ) {
  //   return _BPIFGetState(
  //     tmpState,
  //   );
  // }
  //
  // late final _BPIFGetStatePtr =
  //     _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Pointer<ffi.Void>)>>(
  //         'BPIFGetState');
  // late final _BPIFGetState =
  //     _BPIFGetStatePtr.asFunction<void Function(ffi.Pointer<ffi.Void>)>();

  int BPIFGetBeep() {
    return _BPIFGetBeep();
  }

  late final _BPIFGetBeepPtr =
      _lookup<ffi.NativeFunction<ffi.Uint8 Function()>>('BPIFGetBeep');
  late final _BPIFGetBeep = _BPIFGetBeepPtr.asFunction<int Function()>();

  /// テストモードのグローバル共有メモリの排他制御
  final _BPIFTestModeDataLock = new Lock();

  /// テストモードのグローバル共有メモリのポインタを取得
  Future<ffi.Pointer<ffi.Uint8>> BPIFGetTestModeData_buffer() async {
    ffi.Pointer<ffi.Uint8> buffer;
    buffer = await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tmBuffer();
    });
    return buffer;
  }

  late final _BPIFGetTestModeData_tmBufferPtr =
      _lookup<ffi.NativeFunction<ffi.Pointer<ffi.Uint8> Function()>>(
          'BPIFGetTestModeData_buffer');
  late final _BPIFGetTestModeData_tmBuffer = _BPIFGetTestModeData_tmBufferPtr
      .asFunction<ffi.Pointer<ffi.Uint8> Function()>();

  /// テストモードのグローバル共有メモリの最大サイズを取得
  Future<int> BPIFGetTestModeData_bufferSize() async {
    int bufferSize;
    bufferSize = await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tmBufferSize();
    });
    return bufferSize;
  }

  late final _BPIFGetTestModeData_tmBufferSizePtr =
      _lookup<ffi.NativeFunction<ffi.Uint32 Function()>>(
          'BPIFGetTestModeData_bufferSize');
  late final _BPIFGetTestModeData_tmBufferSize =
      _BPIFGetTestModeData_tmBufferSizePtr.asFunction<int Function()>();

  /// テストモードのグローバル共有メモリ領域を 0 初期化
  void BPIFClearTestModeData() async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFClearTestModeData();
    });
  }

  late final _BPIFClearTestModeDataPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function()>>('BPIFClearTestModeData');
  late final _BPIFClearTestModeData =
      _BPIFClearTestModeDataPtr.asFunction<void Function()>();

  /// テストモードdummyのグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tmDummy(
      ffi.Pointer<BPIFTestModeDummy_t> tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tmDummy(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tmDummyPtr = _lookup<
          ffi
          .NativeFunction<ffi.Void Function(ffi.Pointer<BPIFTestModeDummy_t>)>>(
      'BPIFGetTestModeData_tmDummy');
  late final _BPIFGetTestModeData_tmDummy = _BPIFGetTestModeData_tmDummyPtr
      .asFunction<void Function(ffi.Pointer<BPIFTestModeDummy_t>)>();

  /// テストモードdummyのグローバル共有メモリの値を設定
  void BPIFSetTestModeData_tmDummy(
      ffi.Pointer<BPIFTestModeDummy_t> tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tmDummy(
        tmpData,
      );
    });
  }

  late final _BPIFSetTestModeData_tmDummyPtr = _lookup<
          ffi
          .NativeFunction<ffi.Void Function(ffi.Pointer<BPIFTestModeDummy_t>)>>(
      'BPIFSetTestModeData_tmDummy');
  late final _BPIFSetTestModeData_tmDummy = _BPIFSetTestModeData_tmDummyPtr
      .asFunction<void Function(ffi.Pointer<BPIFTestModeDummy_t>)>();

  /// テストモードdummyのグローバル共有メモリのfile値を設定
  void BPIFSetTestModeData_tmDummy_file(String tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tmDummy_file(
        tmpData.toUint8Pointer(),
        tmpData.length,
      );
    });
  }

  late final _BPIFSetTestModeData_tmDummy_filePtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Uint8>,
              ffi.Uint32)>>('BPIFSetTestModeData_tmDummy_file');
  late final _BPIFSetTestModeData_tmDummy_file =
      _BPIFSetTestModeData_tmDummy_filePtr.asFunction<
          void Function(Pointer<Uint8>, int)>();

  /// テストモードメニューのグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm00(
      ffi.Pointer<BPIFTestMode00Param_t> tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm00(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm00Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode00Param_t>)>>('BPIFGetTestModeData_tm00');
  late final _BPIFGetTestModeData_tm00 = _BPIFGetTestModeData_tm00Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode00Param_t>)>();

  /// テストモード01のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm01(
      ffi.Pointer<BPIFTestMode01Param_t> tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm01(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm01Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode01Param_t>)>>('BPIFGetTestModeData_tm01');
  late final _BPIFGetTestModeData_tm01 = _BPIFGetTestModeData_tm01Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode01Param_t>)>();

  /// テストモード02のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm02(
      ffi.Pointer<BPIFTestMode02Param_t> tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm02(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm02Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode02Param_t>)>>('BPIFGetTestModeData_tm02');
  late final _BPIFGetTestModeData_tm02 = _BPIFGetTestModeData_tm02Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode02Param_t>)>();

  /// テストモード02のグローバル共有メモリの値を設定
  void BPIFSetTestModeData_tm02(
    ffi.Pointer<BPIFTestMode02Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm02(
        tmpData,
      );
    });
  }

  late final _BPIFSetTestModeData_tm02Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode02Param_t>)>>('BPIFSetTestModeData_tm02');
  late final _BPIFSetTestModeData_tm02 = _BPIFSetTestModeData_tm02Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode02Param_t>)>();

  /// テストモード03のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm03(
    ffi.Pointer<BPIFTestMode03Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm03(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm03Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode03Param_t>)>>('BPIFGetTestModeData_tm03');
  late final _BPIFGetTestModeData_tm03 = _BPIFGetTestModeData_tm03Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode03Param_t>)>();

  /// テストモード03のグローバル共有メモリの値を設定
  void BPIFSetTestModeData_tm03(
    ffi.Pointer<BPIFTestMode03Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm03(
        tmpData,
      );
    });
  }

  late final _BPIFSetTestModeData_tm03Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode03Param_t>)>>('BPIFSetTestModeData_tm03');
  late final _BPIFSetTestModeData_tm03 = _BPIFSetTestModeData_tm03Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode03Param_t>)>();

  /// テストモード06のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm06(
    ffi.Pointer<BPIFTestMode06Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm06(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm06Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode06Param_t>)>>('BPIFGetTestModeData_tm06');
  late final _BPIFGetTestModeData_tm06 = _BPIFGetTestModeData_tm06Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode06Param_t>)>();

  /// テストモード07のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm07(
      ffi.Pointer<BPIFTestMode07Param_t> tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm07(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm07Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode07Param_t>)>>('BPIFGetTestModeData_tm07');
  late final _BPIFGetTestModeData_tm07 = _BPIFGetTestModeData_tm07Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode07Param_t>)>();

  /// テストモード10のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm10(
    ffi.Pointer<BPIFTestMode10Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm10(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm10Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode10Param_t>)>>('BPIFGetTestModeData_tm10');
  late final _BPIFGetTestModeData_tm10 = _BPIFGetTestModeData_tm10Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode10Param_t>)>();

  /// テストモード11のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm11(
    ffi.Pointer<BPIFTestMode11Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm11(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm11Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode11Param_t>)>>('BPIFGetTestModeData_tm11');
  late final _BPIFGetTestModeData_tm11 = _BPIFGetTestModeData_tm11Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode11Param_t>)>();

  /// テストモード13のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm13(
    ffi.Pointer<BPIFTestMode13Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm13(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm13Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode13Param_t>)>>('BPIFGetTestModeData_tm13');
  late final _BPIFGetTestModeData_tm13 = _BPIFGetTestModeData_tm13Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode13Param_t>)>();

  /// テストモード14のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm14(
    ffi.Pointer<BPIFTestMode14Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm14(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm14Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode14Param_t>)>>('BPIFGetTestModeData_tm14');
  late final _BPIFGetTestModeData_tm14 = _BPIFGetTestModeData_tm14Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode14Param_t>)>();

  /// テストモード17のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm17(
      ffi.Pointer<BPIFTestMode17Param_t> tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm17(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm17Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode17Param_t>)>>('BPIFGetTestModeData_tm17');
  late final _BPIFGetTestModeData_tm17 = _BPIFGetTestModeData_tm17Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode17Param_t>)>();

  /// テストモード17のグローバル共有メモリの値を設定
  void BPIFSetTestModeData_tm17(
      ffi.Pointer<BPIFTestMode17Param_t> tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm17(
        tmpData,
      );
    });
  }

  late final _BPIFSetTestModeData_tm17Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode17Param_t>)>>('BPIFSetTestModeData_tm17');
  late final _BPIFSetTestModeData_tm17 = _BPIFSetTestModeData_tm17Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode17Param_t>)>();

  /// テストモード17のグローバル共有メモリのmode値を設定
  void BPIFSetTestModeData_tm17_mode(int tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm17_mode(tmpData);
    });
  }

  late final _BPIFSetTestModeData_tm17_modePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Uint8)>>(
          'BPIFSetTestModeData_tm17_mode');
  late final _BPIFSetTestModeData_tm17_mode =
      _BPIFSetTestModeData_tm17_modePtr.asFunction<void Function(int)>();

  /// テストモード17のグローバル共有メモリのstate値を設定
  void BPIFSetTestModeData_tm17_state(int tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm17_state(tmpData);
    });
  }

  late final _BPIFSetTestModeData_tm17_statePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Uint8)>>(
          'BPIFSetTestModeData_tm17_state');
  late final _BPIFSetTestModeData_tm17_state =
      _BPIFSetTestModeData_tm17_statePtr.asFunction<void Function(int)>();

  /// テストモード17のグローバル共有メモリのpanel.address値を設定
  void BPIFSetTestModeData_tm17_panel_address(int tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm17_panel_address(tmpData);
    });
  }

  late final _BPIFSetTestModeData_tm17_panel_addressPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Uint16)>>(
          'BPIFSetTestModeData_tm17_panel_address');
  late final _BPIFSetTestModeData_tm17_panel_address =
      _BPIFSetTestModeData_tm17_panel_addressPtr.asFunction<
          void Function(int)>();

  /// テストモード17のグローバル共有メモリのpanel.writeValue値を設定
  void BPIFSetTestModeData_tm17_panel_writeValue(int tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm17_panel_writeValue(tmpData);
    });
  }

  late final _BPIFSetTestModeData_tm17_panel_writeValuePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Uint16)>>(
          'BPIFSetTestModeData_tm17_panel_writeValue');
  late final _BPIFSetTestModeData_tm17_panel_writeValue =
      _BPIFSetTestModeData_tm17_panel_writeValuePtr.asFunction<
          void Function(int)>();

  /// テストモード17のグローバル共有メモリのmachine.address値を設定
  void BPIFSetTestModeData_tm17_machine_address(int tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm17_machine_address(tmpData);
    });
  }

  late final _BPIFSetTestModeData_tm17_machine_addressPtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Uint16)>>(
          'BPIFSetTestModeData_tm17_machine_address');
  late final _BPIFSetTestModeData_tm17_machine_address =
      _BPIFSetTestModeData_tm17_machine_addressPtr.asFunction<
          void Function(int)>();

  /// テストモード17のグローバル共有メモリのmachine.writeValue値を設定
  void BPIFSetTestModeData_tm17_machine_writeValue(int tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm17_machine_writeValue(tmpData);
    });
  }

  late final _BPIFSetTestModeData_tm17_machine_writeValuePtr =
      _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Uint16)>>(
          'BPIFSetTestModeData_tm17_machine_writeValue');
  late final _BPIFSetTestModeData_tm17_machine_writeValue =
      _BPIFSetTestModeData_tm17_machine_writeValuePtr.asFunction<
          void Function(int)>();

  /// テストモード19のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm19(
    ffi.Pointer<BPIFTestMode19Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm19(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm19Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode19Param_t>)>>('BPIFGetTestModeData_tm19');
  late final _BPIFGetTestModeData_tm19 = _BPIFGetTestModeData_tm19Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode19Param_t>)>();

  /// テストモード22のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm22(
    ffi.Pointer<BPIFTestMode22Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm22(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm22Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode22Param_t>)>>('BPIFGetTestModeData_tm22');
  late final _BPIFGetTestModeData_tm22 = _BPIFGetTestModeData_tm22Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode22Param_t>)>();

  /// テストモード23のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm23(
    ffi.Pointer<BPIFTestMode23Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm23(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm23Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode23Param_t>)>>('BPIFGetTestModeData_tm23');
  late final _BPIFGetTestModeData_tm23 = _BPIFGetTestModeData_tm23Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode23Param_t>)>();

  /// テストモード25のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm25(
    ffi.Pointer<BPIFTestMode25Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm25(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm25Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode25Param_t>)>>('BPIFGetTestModeData_tm25');
  late final _BPIFGetTestModeData_tm25 = _BPIFGetTestModeData_tm25Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode25Param_t>)>();

  void BPIFSetTestModeData_tm25(
    ffi.Pointer<BPIFTestMode25Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm25(
        tmpData,
      );
    });
  }

  late final _BPIFSetTestModeData_tm25Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode25Param_t>)>>('BPIFSetTestModeData_tm25');
  late final _BPIFSetTestModeData_tm25 = _BPIFSetTestModeData_tm25Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode25Param_t>)>();

  /// テストモード26のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm26(
    ffi.Pointer<BPIFTestMode26Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm26(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm26Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode26Param_t>)>>('BPIFGetTestModeData_tm26');
  late final _BPIFGetTestModeData_tm26 = _BPIFGetTestModeData_tm26Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode26Param_t>)>();

  /// テストモード27のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm27(
    ffi.Pointer<BPIFTestMode27Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm27(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm27Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode27Param_t>)>>('BPIFGetTestModeData_tm27');
  late final _BPIFGetTestModeData_tm27 = _BPIFGetTestModeData_tm27Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode27Param_t>)>();

  /// テストモード28のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm28(
    ffi.Pointer<BPIFTestMode28Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm28(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm28Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode28Param_t>)>>('BPIFGetTestModeData_tm28');
  late final _BPIFGetTestModeData_tm28 = _BPIFGetTestModeData_tm28Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode28Param_t>)>();

  /// テストモード29のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm29(
    ffi.Pointer<BPIFTestMode29Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm29(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm29Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode29Param_t>)>>('BPIFGetTestModeData_tm29');
  late final _BPIFGetTestModeData_tm29 = _BPIFGetTestModeData_tm29Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode29Param_t>)>();

  /// テストモード30のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm30(
      ffi.Pointer<BPIFTestMode30Param_t> tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm30(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm30Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode30Param_t>)>>('BPIFGetTestModeData_tm30');
  late final _BPIFGetTestModeData_tm30 = _BPIFGetTestModeData_tm30Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode30Param_t>)>();

  /// テストモード30のグローバル共有メモリの値を設定
  void BPIFSetTestModeData_tm30(
      ffi.Pointer<BPIFTestMode30Param_t> tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm30(
        tmpData,
      );
    });
  }

  late final _BPIFSetTestModeData_tm30Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode30Param_t>)>>('BPIFSetTestModeData_tm30');
  late final _BPIFSetTestModeData_tm30 = _BPIFSetTestModeData_tm30Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode30Param_t>)>();

  /// テストモード30のグローバル共有メモリのeepFilePath値を設定
  void BPIFSetTestModeData_tm30_eepFilePath(String tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm30_eepFilePath(
        tmpData.toUint8Pointer(),
        tmpData.length,
      );
    });
  }

  late final _BPIFSetTestModeData_tm30_eepFilePathPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Uint8>,
              ffi.Uint32)>>('BPIFSetTestModeData_tm30_eepFilePath');
  late final _BPIFSetTestModeData_tm30_eepFilePath =
      _BPIFSetTestModeData_tm30_eepFilePathPtr.asFunction<
          void Function(Pointer<Uint8>, int)>();

  /// テストモード31のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm31(
    ffi.Pointer<BPIFTestMode31Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm31(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm31Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode31Param_t>)>>('BPIFGetTestModeData_tm31');
  late final _BPIFGetTestModeData_tm31 = _BPIFGetTestModeData_tm31Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode31Param_t>)>();

  /// テストモード32のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm32(
    ffi.Pointer<BPIFTestMode32Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm32(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm32Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode32Param_t>)>>('BPIFGetTestModeData_tm32');
  late final _BPIFGetTestModeData_tm32 = _BPIFGetTestModeData_tm32Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode32Param_t>)>();

  /// テストモード34のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm34(
    ffi.Pointer<BPIFTestMode34Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm34(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm34Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode34Param_t>)>>('BPIFGetTestModeData_tm34');
  late final _BPIFGetTestModeData_tm34 = _BPIFGetTestModeData_tm34Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode34Param_t>)>();

  /// テストモード35のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm35(
    ffi.Pointer<BPIFTestMode35Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm35(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm35Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode35Param_t>)>>('BPIFGetTestModeData_tm35');
  late final _BPIFGetTestModeData_tm35 = _BPIFGetTestModeData_tm35Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode35Param_t>)>();

  /// テストモード36のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm36(
    ffi.Pointer<BPIFTestMode36Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm36(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm36Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode36Param_t>)>>('BPIFGetTestModeData_tm36');
  late final _BPIFGetTestModeData_tm36 = _BPIFGetTestModeData_tm36Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode36Param_t>)>();

  void BPIFSetTestModeData_tm36(
    ffi.Pointer<BPIFTestMode36Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm36(
        tmpData,
      );
    });
  }

  late final _BPIFSetTestModeData_tm36Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode36Param_t>)>>('BPIFSetTestModeData_tm36');
  late final _BPIFSetTestModeData_tm36 = _BPIFSetTestModeData_tm36Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode36Param_t>)>();

  /// テストモード37のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm37(
    ffi.Pointer<BPIFTestMode37Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm37(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm37Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode37Param_t>)>>('BPIFGetTestModeData_tm37');
  late final _BPIFGetTestModeData_tm37 = _BPIFGetTestModeData_tm37Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode37Param_t>)>();

  void BPIFSetTestModeData_tm37(
    ffi.Pointer<BPIFTestMode37Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm37(
        tmpData,
      );
    });
  }

  late final _BPIFSetTestModeData_tm37Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode37Param_t>)>>('BPIFSetTestModeData_tm37');
  late final _BPIFSetTestModeData_tm37 = _BPIFSetTestModeData_tm37Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode37Param_t>)>();

  /// テストモード39のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm39(
    ffi.Pointer<BPIFTestMode39Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm39(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm39Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode39Param_t>)>>('BPIFGetTestModeData_tm39');
  late final _BPIFGetTestModeData_tm39 = _BPIFGetTestModeData_tm39Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode39Param_t>)>();

  void BPIFSetTestModeData_tm39(
    ffi.Pointer<BPIFTestMode39Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm39(
        tmpData,
      );
    });
  }

  late final _BPIFSetTestModeData_tm39Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode39Param_t>)>>('BPIFSetTestModeData_tm39');
  late final _BPIFSetTestModeData_tm39 = _BPIFSetTestModeData_tm39Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode39Param_t>)>();

  /// テストモード42のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm42(
    ffi.Pointer<BPIFTestMode42Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm42(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm42Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode42Param_t>)>>('BPIFGetTestModeData_tm42');
  late final _BPIFGetTestModeData_tm42 = _BPIFGetTestModeData_tm42Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode42Param_t>)>();

  void BPIFSetTestModeData_tm42(
    ffi.Pointer<BPIFTestMode42Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm42(
        tmpData,
      );
    });
  }

  late final _BPIFSetTestModeData_tm42Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode42Param_t>)>>('BPIFSetTestModeData_tm42');
  late final _BPIFSetTestModeData_tm42 = _BPIFSetTestModeData_tm42Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode42Param_t>)>();

  /// テストモード44のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm44(
    ffi.Pointer<BPIFTestMode44Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm44(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm44Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode44Param_t>)>>('BPIFGetTestModeData_tm44');
  late final _BPIFGetTestModeData_tm44 = _BPIFGetTestModeData_tm44Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode44Param_t>)>();

  /// テストモード47のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm47(
    ffi.Pointer<BPIFTestMode47Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm47(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm47Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode47Param_t>)>>('BPIFGetTestModeData_tm47');
  late final _BPIFGetTestModeData_tm47 = _BPIFGetTestModeData_tm47Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode47Param_t>)>();

  /// テストモード48のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm48(
    ffi.Pointer<BPIFTestMode48Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm48(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm48Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode48Param_t>)>>('BPIFGetTestModeData_tm48');
  late final _BPIFGetTestModeData_tm48 = _BPIFGetTestModeData_tm48Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode48Param_t>)>();

  /// テストモード49のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm49(
    ffi.Pointer<BPIFTestMode49Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm49(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm49Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode49Param_t>)>>('BPIFGetTestModeData_tm49');
  late final _BPIFGetTestModeData_tm49 = _BPIFGetTestModeData_tm49Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode49Param_t>)>();

  /// テストモード50のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm50(
    ffi.Pointer<BPIFTestMode50Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm50(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm50Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode50Param_t>)>>('BPIFGetTestModeData_tm50');
  late final _BPIFGetTestModeData_tm50 = _BPIFGetTestModeData_tm50Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode50Param_t>)>();

  /// テストモード51のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm51(
    ffi.Pointer<BPIFTestMode51Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm51(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm51Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode51Param_t>)>>('BPIFGetTestModeData_tm51');
  late final _BPIFGetTestModeData_tm51 = _BPIFGetTestModeData_tm51Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode51Param_t>)>();

  /// テストモード52のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm52(
    ffi.Pointer<BPIFTestMode52Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm52(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm52Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode52Param_t>)>>('BPIFGetTestModeData_tm52');
  late final _BPIFGetTestModeData_tm52 = _BPIFGetTestModeData_tm52Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode52Param_t>)>();

  /// テストモード53のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm53(
    ffi.Pointer<BPIFTestMode53Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm53(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm53Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode53Param_t>)>>('BPIFGetTestModeData_tm53');
  late final _BPIFGetTestModeData_tm53 = _BPIFGetTestModeData_tm53Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode53Param_t>)>();

  /// テストモード60のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm60(
      ffi.Pointer<BPIFTestMode60Param_t> tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm60(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm60Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode60Param_t>)>>('BPIFGetTestModeData_tm60');
  late final _BPIFGetTestModeData_tm60 = _BPIFGetTestModeData_tm60Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode60Param_t>)>();

  /// テストモード60のグローバル共有メモリの値を設定
  void BPIFSetTestModeData_tm60(
      ffi.Pointer<BPIFTestMode60Param_t> tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm60(
        tmpData,
      );
    });
  }

  late final _BPIFSetTestModeData_tm60Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode60Param_t>)>>('BPIFSetTestModeData_tm60');
  late final _BPIFSetTestModeData_tm60 = _BPIFSetTestModeData_tm60Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode60Param_t>)>();

  /// テストモード60のグローバル共有メモリのeepToFilePanelPath値を設定
  void BPIFSetTestModeData_tm60_eepToFilePanelPath(String tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm60_eepToFilePanelPath(
        tmpData.toUint8Pointer(),
        tmpData.length,
      );
    });
  }

  late final _BPIFSetTestModeData_tm60_eepToFilePanelPathPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Uint8>,
              ffi.Uint32)>>('BPIFSetTestModeData_tm60_eepToFilePanelPath');
  late final _BPIFSetTestModeData_tm60_eepToFilePanelPath =
      _BPIFSetTestModeData_tm60_eepToFilePanelPathPtr.asFunction<
          void Function(Pointer<Uint8>, int)>();

  /// テストモード60のグローバル共有メモリのeepToFileMachinePath値を設定
  void BPIFSetTestModeData_tm60_eepToFileMachinePath(String tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm60_eepToFileMachinePath(
        tmpData.toUint8Pointer(),
        tmpData.length,
      );
    });
  }

  late final _BPIFSetTestModeData_tm60_eepToFileMachinePathPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Uint8>,
              ffi.Uint32)>>('BPIFSetTestModeData_tm60_eepToFileMachinePath');
  late final _BPIFSetTestModeData_tm60_eepToFileMachinePath =
      _BPIFSetTestModeData_tm60_eepToFileMachinePathPtr.asFunction<
          void Function(Pointer<Uint8>, int)>();

  /// テストモード60のグローバル共有メモリのeepFilePath値を設定
  void BPIFSetTestModeData_tm60_eepFilePath(String tmpData) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFSetTestModeData_tm60_eepFilePath(
        tmpData.toUint8Pointer(),
        tmpData.length,
      );
    });
  }

  late final _BPIFSetTestModeData_tm60_eepFilePathPtr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(ffi.Pointer<ffi.Uint8>,
              ffi.Uint32)>>('BPIFSetTestModeData_tm60_eepFilePath');
  late final _BPIFSetTestModeData_tm60_eepFilePath =
      _BPIFSetTestModeData_tm60_eepFilePathPtr.asFunction<
          void Function(Pointer<Uint8>, int)>();

  /// テストモード62のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm62(
    ffi.Pointer<BPIFTestMode62Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm62(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm62Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode62Param_t>)>>('BPIFGetTestModeData_tm62');
  late final _BPIFGetTestModeData_tm62 = _BPIFGetTestModeData_tm62Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode62Param_t>)>();

  /// テストモード64のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm64(
    ffi.Pointer<BPIFTestMode64Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm64(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm64Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode64Param_t>)>>('BPIFGetTestModeData_tm64');
  late final _BPIFGetTestModeData_tm64 = _BPIFGetTestModeData_tm64Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode64Param_t>)>();

  /// テストモード65のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm65(
    ffi.Pointer<BPIFTestMode65Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm65(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm65Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode65Param_t>)>>('BPIFGetTestModeData_tm65');
  late final _BPIFGetTestModeData_tm65 = _BPIFGetTestModeData_tm65Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode65Param_t>)>();

  /// テストモード69のグローバル共有メモリの値を取得
  void BPIFGetTestModeData_tm69(
    ffi.Pointer<BPIFTestMode69Param_t> tmpData,
  ) async {
    await _BPIFTestModeDataLock.synchronized(() {
      return _BPIFGetTestModeData_tm69(
        tmpData,
      );
    });
  }

  late final _BPIFGetTestModeData_tm69Ptr = _lookup<
      ffi.NativeFunction<
          ffi.Void Function(
              ffi.Pointer<BPIFTestMode69Param_t>)>>('BPIFGetTestModeData_tm69');
  late final _BPIFGetTestModeData_tm69 = _BPIFGetTestModeData_tm69Ptr
      .asFunction<void Function(ffi.Pointer<BPIFTestMode69Param_t>)>();

  ////////////////通常画面////////////////////////
  void BPIFGetAppDisplay_utl(
    ffi.Pointer<BPIFUtlAppDisplayInfoPublicDef_t> utlData,
  ) {
    return _BPIFGetAppDisplay_utl(
      utlData,
    );
  }

  late final _BPIFGetAppDisplay_utlPtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Pointer<BPIFUtlAppDisplayInfoPublicDef_t>)>>(
      'BPIFGetAppDisplay_utl');
  late final _BPIFGetAppDisplay_utl = _BPIFGetAppDisplay_utlPtr.asFunction<
      void Function(ffi.Pointer<BPIFUtlAppDisplayInfoPublicDef_t>)>();

  void BPIFGetAppDisplay_global(
    ffi.Pointer<BPIFGlobalAppInfo_t> tmpData,
  ) {
    return _BPIFGetAppDisplay_global(
      tmpData,
    );
  }

  late final _BPIFGetAppDisplay_globalPtr = _lookup<
          ffi
          .NativeFunction<ffi.Void Function(ffi.Pointer<BPIFGlobalAppInfo_t>)>>(
      'BPIFGetAppDisplay_global');
  late final _BPIFGetAppDisplay_global = _BPIFGetAppDisplay_globalPtr
      .asFunction<void Function(ffi.Pointer<BPIFGlobalAppInfo_t>)>();

  void BPIFGetAppDisplay_emb(
    ffi.Pointer<BPIFEmbAppDisplayInfoPublicDef_t> embData,
  ) {
    return _BPIFGetAppDisplay_emb(
      embData,
    );
  }

  late final _BPIFGetAppDisplay_embPtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Pointer<BPIFEmbAppDisplayInfoPublicDef_t>)>>(
      'BPIFGetAppDisplay_emb');
  late final _BPIFGetAppDisplay_emb = _BPIFGetAppDisplay_embPtr.asFunction<
      void Function(ffi.Pointer<BPIFEmbAppDisplayInfoPublicDef_t>)>();

  /// MDCの状態取得
  void BPIFGetAppDisplay_mdc(
    ffi.Pointer<BPIFMDCAppDisplayInfoPublicDef_t> mdcData,
  ) {
    return _BPIFGetAppDisplay_mdc(
      mdcData,
    );
  }

  late final _BPIFGetAppDisplay_mdcPtr = _lookup<
          ffi.NativeFunction<
              ffi.Void Function(
                  ffi.Pointer<BPIFMDCAppDisplayInfoPublicDef_t>)>>(
      'BPIFGetAppDisplay_mdc');
  late final _BPIFGetAppDisplay_mdc = _BPIFGetAppDisplay_mdcPtr.asFunction<
      void Function(ffi.Pointer<BPIFMDCAppDisplayInfoPublicDef_t>)>();

  int hasMechaKeySousaBetweenLastAndCurrentCheck() {
    return _hasMechaKeySousaBetweenLastAndCurrentCheck();
  }

  late final _hasMechaKeySousaBetweenLastAndCurrentCheckPtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
      'hasMechaKeySousaBetweenLastAndCurrentCheck');
  late final _hasMechaKeySousaBetweenLastAndCurrentCheck =
  _hasMechaKeySousaBetweenLastAndCurrentCheckPtr.asFunction<
      int Function()>();

////////////////画面遷移関数////////////////////////

  /// 共通キー
  int gotoHome() {
    return _gotoHome();
  }

  late final _gotoHomePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoHome');
  late final _gotoHome = _gotoHomePtr.asFunction<int Function()>();

  int gotoHowToUse() {
    return _gotoHowToUse();
  }

  late final _gotoHowToUsePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoHowToUse');
  late final _gotoHowToUse = _gotoHowToUsePtr.asFunction<int Function()>();

  int returnHowToUse() {
    return _returnHowToUse();
  }

  late final _returnHowToUsePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('returnHowToUse');
  late final _returnHowToUse = _returnHowToUsePtr.asFunction<int Function()>();

  int gotoSettings() {
    return _gotoSettings();
  }

  late final _gotoSettingsPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoSettings');
  late final _gotoSettings = _gotoSettingsPtr.asFunction<int Function()>();

  int returnSettings() {
    return _returnSettings();
  }

  late final _returnSettingsPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('returnSettings');
  late final _returnSettings = _returnSettingsPtr.asFunction<int Function()>();

  int gotoWlanSetting() {
    return _gotoWlanSetting();
  }

  late final _gotoWlanSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoWlanSetting');
  late final _gotoWlanSetting =
      _gotoWlanSettingPtr.asFunction<int Function()>();

  int returnWlanSetting() {
    return _returnWlanSetting();
  }

  late final _returnWlanSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('returnWlanSetting');
  late final _returnWlanSetting =
      _returnWlanSettingPtr.asFunction<int Function()>();

  int gotoClockSetting() {
    return _gotoClockSetting();
  }

  late final _gotoClockSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoClockSetting');
  late final _gotoClockSetting =
      _gotoClockSettingPtr.asFunction<int Function()>();

  int returnClockSetting() {
    return _returnClockSetting();
  }

  late final _returnClockSettingPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('returnClockSetting');
  late final _returnClockSetting =
      _returnClockSettingPtr.asFunction<int Function()>();

  int showChangeView() {
    return _showChangeView();
  }

  late final _showChangeViewPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('showChangeView');
  late final _showChangeView = _showChangeViewPtr.asFunction<int Function()>();

  int hideChangeView() {
    return _hideChangeView();
  }

  late final _hideChangeViewPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('hideChangeView');
  late final _hideChangeView = _hideChangeViewPtr.asFunction<int Function()>();

  int closeChangeView() {
    return _closeChangeView();
  }

  late final _closeChangeViewPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('closeChangeView');
  late final _closeChangeView =
      _closeChangeViewPtr.asFunction<int Function()>();

  //Opening
  int gotoHomeFromOpening() {
    return _gotoHomeFromOpening();
  }

  late final _gotoHomeFromOpeningPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoHomeFromOpening');
  late final _gotoHomeFromOpening =
      _gotoHomeFromOpeningPtr.asFunction<int Function()>();

  int startDemoMode() {
    return _startDemoMode();
  }

  late final _startDemoModePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('startDemoMode');
  late final _startDemoMode = _startDemoModePtr.asFunction<int Function()>();

  /// /デバイスの状態
  /// タッチイベントを通知する
  int DisplayTouchEvent() {
    return _DisplayTouchEvent();
  }

  late final _DisplayTouchEventPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('DisplayTouchEvent');
  late final _DisplayTouchEvent =
      _DisplayTouchEventPtr.asFunction<int Function()>();

  /// タッチパネル操作をロックする
  int lockTouch() {
    return _lockTouch();
  }

  late final _lockTouchPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('lockTouch');
  late final _lockTouch = _lockTouchPtr.asFunction<int Function()>();

  int clearLockTouch() {
    return _clearLockTouch();
  }

  late final _clearLockTouchPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('clearLockTouch');
  late final _clearLockTouch = _clearLockTouchPtr.asFunction<int Function()>();

  /// メカキーも含めてロックする
  int lockAll() {
    return _lockAll();
  }

  late final _lockAllPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('lockAll');
  late final _lockAll = _lockAllPtr.asFunction<int Function()>();

  int clearLockAll() {
    return _clearLockAll();
  }

  late final _clearLockAllPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('clearLockAll');
  late final _clearLockAll = _clearLockAllPtr.asFunction<int Function()>();

  /// ActionMatrix_Task 周期処理を一時停止/再開 (スタイル変換を動かす前にsuspend、終わったらresumeで再開すること)
  int suspendActionMatrixTask() {
    return _suspendActionMatrixTask();
  }

  late final _suspendActionMatrixTaskPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'suspendActionMatrixTask');
  late final _suspendActionMatrixTask =
      _suspendActionMatrixTaskPtr.asFunction<int Function()>();

  int resumeActionMatrixTask() {
    return _resumeActionMatrixTask();
  }

  late final _resumeActionMatrixTaskPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'resumeActionMatrixTask');
  late final _resumeActionMatrixTask =
      _resumeActionMatrixTaskPtr.asFunction<int Function()>();

  /// PHFIRMIIVO-6983 アプリと起動タイミングをあわせる
  /// 初回ガイダンスのチェック開始
  int initSettingGuidanceStart() {
    return _initSettingGuidanceStart();
  }

  late final _initSettingGuidanceStartPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'initSettingGuidanceStart');
  late final _initSettingGuidanceStart =
      _initSettingGuidanceStartPtr.asFunction<int Function()>();

  /// 初回ガイダンスのチェック完了
  int initSettingGuidanceComplete() {
    return _initSettingGuidanceComplete();
  }

  late final _initSettingGuidanceCompletePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'initSettingGuidanceComplete');
  late final _initSettingGuidanceComplete =
      _initSettingGuidanceCompletePtr.asFunction<int Function()>();

  /// アプリのBPIF_MAX値を通知する
  int AppBpifMaxNotification(int idMax) {
    return _AppBpifMaxNotification(idMax);
  }

  late final _AppBpifMaxNotificationPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'AppBpifMaxNotification');
  late final _AppBpifMaxNotification =
      _AppBpifMaxNotificationPtr.asFunction<int Function(int)>();

  /// //アプリで動画再生状態になっているかどうか
  /// PANEL_API DeviceErrorCode_t playMovie(void);
  /// PANEL_API DeviceErrorCode_t stopMovie(void);

  //アプリがエラー状態か

  int setErrorState(
    int error,
  ) {
    return _setErrorState(
      error,
    );
  }

  late final _setErrorStatePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'setErrorState');
  late final _setErrorState = _setErrorStatePtr.asFunction<int Function(int)>();

  int setAppErrorState(int errAct) {
    return _setAppErrorState(errAct);
  }

  late final _setAppErrorStatePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Int32)>>(
          'setAppErrorState');
  late final _setAppErrorState =
      _setAppErrorStatePtr.asFunction<int Function(int)>();

  int clearAppErrorState() {
    return _clearAppErrorState();
  }

  late final _clearAppErrorStatePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('clearAppErrorState');
  late final _clearAppErrorState =
      _clearAppErrorStatePtr.asFunction<int Function()>();

  /// DeepSleep
  int startSleep() {
    return _startSleep();
  }

  late final _startSleepPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('startSleep');
  late final _startSleep = _startSleepPtr.asFunction<int Function()>();

  int stopSleep() {
    return _stopSleep();
  }

  late final _stopSleepPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('stopSleep');
  late final _stopSleep = _stopSleepPtr.asFunction<int Function()>();

  /// Screren Server
  int startScreenSaver() {
    return _startScreenSaver();
  }

  late final _startScreenSaverPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('startScreenSaver');
  late final _startScreenSaver =
      _startScreenSaverPtr.asFunction<int Function()>();

  int stopScreenSaver() {
    return _stopScreenSaver();
  }

  late final _stopScreenSaverPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('stopScreenSaver');
  late final _stopScreenSaver =
      _stopScreenSaverPtr.asFunction<int Function()>();

  /// Ecomode LCDオフ
  int startEco() {
    return _startEco();
  }

  late final _startEcoPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('startEco');
  late final _startEco = _startEcoPtr.asFunction<int Function()>();

  int stopEco() {
    return _stopEco();
  }

  late final _stopEcoPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('stopEco');
  late final _stopEco = _stopEcoPtr.asFunction<int Function()>();

  /// Home画面から遷移する
  int gotoUtlFromHome() {
    return _gotoUtlFromHome();
  }

  late final _gotoUtlFromHomePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoUtlFromHome');
  late final _gotoUtlFromHome =
      _gotoUtlFromHomePtr.asFunction<int Function()>();

  int gotoEmbFromHome() {
    return _gotoEmbFromHome();
  }

  late final _gotoEmbFromHomePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoEmbFromHome');
  late final _gotoEmbFromHome =
      _gotoEmbFromHomePtr.asFunction<int Function()>();

  int gotoDisneyFromHome() {
    return _gotoDisneyFromHome();
  }

  late final _gotoDisneyFromHomePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoDisneyFromHome');
  late final _gotoDisneyFromHome =
      _gotoDisneyFromHomePtr.asFunction<int Function()>();

  int gotoMDCFromHome() {
    return _gotoMDCFromHome();
  }

  late final _gotoMDCFromHomePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoMDCFromHome');
  late final _gotoMDCFromHome =
      _gotoMDCFromHomePtr.asFunction<int Function()>();

  /// Utlの画面遷移
  int gotoUtlModeFromDeco() {
    return _gotoUtlModeFromDeco();
  }

  late final _gotoUtlModeFromDecoPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoUtlModeFromDeco');
  late final _gotoUtlModeFromDeco =
      _gotoUtlModeFromDecoPtr.asFunction<int Function()>();

  int gotoDecoFromUtl() {
    return _gotoDecoFromUtl();
  }

  late final _gotoDecoFromUtlPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoDecoFromUtl');
  late final _gotoDecoFromUtl =
      _gotoDecoFromUtlPtr.asFunction<int Function()>();

  int gotoDecoFromMyIl() {
    return _gotoDecoFromMyIl();
  }

  late final _gotoDecoFromMyIlPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoDecoFromMyIl');
  late final _gotoDecoFromMyIl =
      _gotoDecoFromMyIlPtr.asFunction<int Function()>();

  int gotoMyIlFromDeco() {
    return _gotoMyIlFromDeco();
  }

  late final _gotoMyIlFromDecoPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoMyIlFromDeco');
  late final _gotoMyIlFromDeco =
      _gotoMyIlFromDecoPtr.asFunction<int Function()>();

  int gotoMyIlEditFromMyIlTest() {
    return _gotoMyIlEditFromMyIlTest();
  }

  late final _gotoMyIlEditFromMyIlTestPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'gotoMyIlEditFromMyIlTest');
  late final _gotoMyIlEditFromMyIlTest =
      _gotoMyIlEditFromMyIlTestPtr.asFunction<int Function()>();

  int gotoMyIlTestFromMyIlEdit() {
    return _gotoMyIlTestFromMyIlEdit();
  }

  late final _gotoMyIlTestFromMyIlEditPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'gotoMyIlTestFromMyIlEdit');
  late final _gotoMyIlTestFromMyIlEdit =
      _gotoMyIlTestFromMyIlEditPtr.asFunction<int Function()>();

  int gotoUtlPreviewFromMyIlEdit() {
    return _gotoUtlPreviewFromMyIlEdit();
  }

  late final _gotoUtlPreviewFromMyIlEditPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>(
          'gotoUtlPreviewFromMyIlEdit');
  late final _gotoUtlPreviewFromMyIlEdit =
      _gotoUtlPreviewFromMyIlEditPtr.asFunction<int Function()>();

  int gotoUtlPreview() {
    return _gotoUtlPreview();
  }

  late final _gotoUtlPreviewPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoUtlPreview');
  late final _gotoUtlPreview = _gotoUtlPreviewPtr.asFunction<int Function()>();

  int gotoSR() {
    return _gotoSR();
  }

  late final _gotoSRPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoSR');
  late final _gotoSR = _gotoSRPtr.asFunction<int Function()>();

  int exitSR() {
    return _exitSR();
  }

  late final _exitSRPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('exitSR');
  late final _exitSR = _exitSRPtr.asFunction<int Function()>();

  int gotoSRBastingWarning() {
    return _gotoSRBastingWarning();
  }

  late final _gotoSRBastingWarningPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('gotoSRBastingWarning');
  late final _gotoSRBastingWarning =
      _gotoSRBastingWarningPtr.asFunction<int Function()>();

  int exitSRBastingWarning() {
    return _exitSRBastingWarning();
  }

  late final _exitSRBastingWarningPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('exitSRBastingWarning');
  late final _exitSRBastingWarning =
      _exitSRBastingWarningPtr.asFunction<int Function()>();

  /// 針落ち点表示
  int setChangeViewNeedlePosition(
    bool isOn,
  ) {
    return _setChangeViewNeedlePosition(
      isOn,
    );
  }

  late final _setChangeViewNeedlePositionPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setChangeViewNeedlePosition');
  late final _setChangeViewNeedlePosition =
      _setChangeViewNeedlePositionPtr.asFunction<int Function(bool)>();

  int seChangeViewGrid(
    bool isOn,
  ) {
    return _setChangeViewGrid(
      isOn,
    );
  }

  late final _setChangeViewGridPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setChangeViewGrid');
  late final _setChangeViewGrid =
      _setChangeViewGridPtr.asFunction<int Function(bool)>();

  int seChangeViewZoom(
    bool isOn,
  ) {
    return _setChangeViewZoom(
      isOn,
    );
  }

  late final _setChangeViewZoomPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Bool)>>(
          'setChangeViewZoom');
  late final _setChangeViewZoom =
      _setChangeViewZoomPtr.asFunction<int Function(bool)>();

  /// 糸巻
  int setBwdPopOff() {
    return _setBwdPopOff();
  }

  late final _setBwdPopOffPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setBwdPopOff');
  late final _setBwdPopOff = _setBwdPopOffPtr.asFunction<int Function()>();

  int setBwdPopOn() {
    return _setBwdPopOn();
  }

  late final _setBwdPopOnPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setBwdPopOn');
  late final _setBwdPopOn = _setBwdPopOnPtr.asFunction<int Function()>();

  int startBwd() {
    return _startBwd();
  }

  late final _startBwdPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('startBwd');
  late final _startBwd = _startBwdPtr.asFunction<int Function()>();

  int stopBwd() {
    return _stopBwd();
  }

  late final _stopBwdPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('stopBwd');
  late final _stopBwd = _stopBwdPtr.asFunction<int Function()>();

  int setBwdSpeedPlus() {
    return _setBwdSpeedPlus();
  }

  late final _setBwdSpeedPlusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setBwdSpeedPlus');
  late final _setBwdSpeedPlus =
      _setBwdSpeedPlusPtr.asFunction<int Function()>();

  int setBwdSpeedMinus() {
    return _setBwdSpeedMinus();
  }

  late final _setBwdSpeedMinusPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('setBwdSpeedMinus');
  late final _setBwdSpeedMinus =
      _setBwdSpeedMinusPtr.asFunction<int Function()>();

  int getInitState() {
    return _getInitState();
  }

  late final _getInitStatePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('getInitState');
  late final _getInitState = _getInitStatePtr.asFunction<int Function()>();

  int isDisplayPopup() {
    return _isDisplayPopup();
  }

  late final _isDisplayPopupPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('isDisplayPopup');
  late final _isDisplayPopup = _isDisplayPopupPtr.asFunction<int Function()>();

  int getSSLedColor(ffi.Pointer<ffi.Uint8> color) {
    return _getSSLedColor(color);
  }

  late final _getSSLedColorPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function(ffi.Pointer<ffi.Uint8>)>>(
          'getSSLedColor');
  late final _getSSLedColor =
      _getSSLedColorPtr.asFunction<int Function(ffi.Pointer<ffi.Uint8>)>();

  int checkGotoHome() {
    return _checkGotoHome();
  }

  late final _checkGotoHomePtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('checkGotoHome');
  late final _checkGotoHome = _checkGotoHomePtr.asFunction<int Function()>();

  int checkGotoMyIl() {
    return _checkGotoMyIl();
  }

  late final _checkGotoMyIlPtr =
      _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('checkGotoMyIl');
  late final _checkGotoMyIl = _checkGotoMyIlPtr.asFunction<int Function()>();


  int setMatrixEnableList(
      int ActivatePattern,
      ) {
    return _setMatrixEnableList(
      ActivatePattern,
    );
  }

  late final _setMatrixEnableListPtr =
  _lookup<ffi.NativeFunction<ffi.Int Function(ffi.Uint8)>>(
      'setMatrixEnableList');
  late final _setMatrixEnableList =
  _setMatrixEnableListPtr.asFunction<int Function(int)>();

//エラーなどで上書きしない状態でメカキーロックを設定する
  bool setMatrixEnableListNotOverwrited(
      int ActivatePattern,
      ) {
    return _setMatrixEnableListNotOverwritten(
      ActivatePattern,
    );
  }

  late final _setMatrixEnableListNotOverwrittenPtr =
  _lookup<ffi.NativeFunction<ffi.Bool Function(ffi.Uint8)>>(
      'setMatrixEnableListNotOverwritten');
  late final _setMatrixEnableListNotOverwritten =
  _setMatrixEnableListNotOverwrittenPtr.asFunction<bool Function(int)>();

//setMatrixEnableListNotOverwritedとセットで使用する
//上書き禁止状態を解除する
  void clearMatrixEnableListNotOverwrited(
      int ActivatePattern,
      ) {
    return _clearMatrixEnableListNotOverwritten(
      ActivatePattern,
    );
  }

  late final _clearMatrixEnableListNotOverwrittenPtr =
  _lookup<ffi.NativeFunction<ffi.Void Function(ffi.Uint8)>>(
      'clearMatrixEnableListNotOverwritten');
  late final _clearMatrixEnableListNotOverwritten =
  _clearMatrixEnableListNotOverwrittenPtr.asFunction<void Function(int)>();
  /// カメラを停止する
  int stopCameraCapture() {
    return _stopCameraCapture();
  }

  late final _stopCameraCapturePtr =
  _lookup<ffi.NativeFunction<ffi.Int32 Function()>>('stopCameraCapture');
  late final _stopCameraCapture = _stopCameraCapturePtr.asFunction<int Function()>();


}

/// キー関数のテーブル index (br_if.h の定義 BPIFSendKey と同じ並びとなっていること)
///
/// const int の定義名だと、テーブルの途中にキー関数を追加する毎に、
/// 追加した位置以降の定義名と値の関連付けの修正が必要で煩雑なので enum に変更
///
/// e.g.
///
/// `int index = BPIFSendKey.TTK_TM1_PUSH.index;`
/// `debugPrint('$index'); // '68'`
///
/// `BPIFSendKey key = BPIFSendKey.values[index];
/// `debugPrint('$key.name'); // 'TTK_TM1_PUSH'`
///
enum BeepRequest_t {
  BEEP_NON,
  KEY_INVALID,
  KEY_ACCEPT,
  KEY_MODE_CHANGE,
  KEY_SELECT,
  MACHINE_OPENING,
  SEWING_OVER,
  SEWING_OVER2,
  MEM_FULL_AT_DATA_EXP,
  MEM_FULL_AT_SELECT,
  ACTION_ERROR,
  WHEN_MOTOR_LOCK,
  SW_ON_WHEN_POWER_ON,
  WHEN_SPEC_INVALID,
  WHEN_TP_INIT_INVALID,
  //2012-04-09 ZZS620 VcombL AUDIO ins start
  KEY_ACCEPT2, //受付音(超音波センサー)
  KEY_ACCEPT_LONG, //受付音ロング(超音波センサー)
  KEY_REFUSE, //拒否音(超音波センサー)
//2012-04-09 ZZS620 VcombL AUDIO ins end
/*** ↓ IIVO_V100　IIVO_SR-6-1　2024/03/23　minemayo ***/
  SR_BEEP_SPEED_OVER, // SR速度超過の警告音
  SR_BEEP_NEEDLE_DOWN, // SR針落ち前の警告音
/*** ↑ IIVO_V100　IIVO_SR-6-1　2024/03/23　minemayo ***/
  BEEP_REQUEST_MAX
}

enum BPIFSendKey
{
  BPIF_SEND_DUMMY,

  /////////////テストモード////////////////////////////////
  TTK_TM_START,

  // testmode 01. Serial numbers
  TTK_TM01_PUSH,
  TTK_TM01_POP,

  // testmode 02. Fablic Thickness Setting
  TTK_TM02_PUSH,
  TTK_TM02_FABRIC_0MM,
  TTK_TM02_FABRIC_3MM,
  TTK_TM02_ADJUST_PF_OFFSET,
  TTK_TM02_PF_POS_MINUS,
  TTK_TM02_PF_POS_PLUS,
  TTK_TM02_KL_MIN,
  TTK_TM02_KL_MAX_MIN,
  TTK_TM02_POP,

  // testmode 03. Pattern Adjustment
  TTK_TM03_PUSH,
  TTK_TM03_ASP_V_MINUS,
  TTK_TM03_ASP_V_PLUS,
  TTK_TM03_ASP_H_MINUS,
  TTK_TM03_ASP_H_PLUS,
  TTK_TM03_POP,

  // testmode 04. 3-Point needle drop *
  TTK_TM04_PUSH,
  TTK_TM04_3POINT_LEFT,
  TTK_TM04_3POINT_CENTER,
  TTK_TM04_3POINT_RIGHT,
  TTK_TM04_POP,

  // testmode 05. Needle clearance *
  TTK_TM05_PUSH,
  TTK_TM05_POP,

  // testmode 06. Feed Dog Position *
  TTK_TM06_PUSH,
  TTK_TM06_FEED_DOG_LEFT,
  TTK_TM06_FEED_DOG_CENTER,
  TTK_TM06_FEED_DOG_RIGHT,
  TTK_TM06_FEED_DOG_FRONT,
  TTK_TM06_FEED_DOG_DOWN,
  TTK_TM06_FEED_DOG_UP,
  TTK_TM06_POP,

  // testmode 07. Presser height *
  TTK_TM07_PUSH,
  TTK_TM07_PUSH_PIVOT_32MM,
  TTK_TM07_PUSH_PIVOT_75MM,
  TTK_TM07_POP,
  // testmode 08. Whip stitch pattern
  TTK_TM08_PUSH,
  TTK_TM08_POP,

  // testmode 09. Speaker check
  TTK_TM09_PUSH,
  // testmode 52に定義と同じものを使用
  TTK_TM09_POP,

  // testmode 10. Lower thread display
  TTK_TM10_PUSH,
  TTK_TM10_POP,

  // testmode 11. Upper thread display
  TTK_TM11_PUSH,
  TTK_TM11_POP,

  // testmode 12. Thread sensor cancel
  TTK_TM12_PUSH,
  TTK_TM12_POP,

  // testmode 13. Forward and Reverse Feed
  TTK_TM13_PUSH,
  TTK_TM13_FFP_ADJUST_MINUS,
  TTK_TM13_FFP_ADJUST_PLUS,
  TTK_TM13_FRP_ADJUST_MINUS,
  TTK_TM13_FRP_ADJUST_PLUS,
  TTK_TM13_ADJUST_PARAM_LOCK,
  TTK_TM13_POP,

  // testmode 14. Speed
  TTK_TM14_PUSH,
  TTK_TM14_POP,

  // testmode 15. Power
  TTK_TM15_PUSH,
  TTK_TM15_ECO,
  TTK_TM15_ECO_TOUCH_RETURN,
  TTK_TM15_SHUTOFF,
  TTK_TM15_POP,

  // testmode 16. Switch Monitoring
  TTK_TM16_PUSH,
  TTK_TM16_POP,

  // testmode 17. Eeprom write
  TTK_TM17_PUSH,
  TTK_TM17_EEP_READ,
  TTK_TM17_EEP_WRITE_PANEL,
  TTK_TM17_EEP_WRITE_MACHINE,
  TTK_TM17_POP,

  // testmode 19. Clearing flash
  TTK_TM19_PUSH,
  TTK_TM19_CLEAR_MEMORY,
  TTK_TM19_CLEAR_STICH,
  TTK_TM19_CLEAR_ALL_STICH,
  TTK_TM19_POP,

  // testmode 22. NP ensor
  TTK_TM22_PUSH,
  TTK_TM22_ON,
  TTK_TM22_OFF,
  TTK_TM22_POP,

  // testmode 23. Embroidery max position
  TTK_TM23_PUSH,
  TTK_TM23_MAX_TRIAL,
  TTK_TM23_EMB_UNIT_REMOVE,
  TTK_TM23_POP,

  // testmode 24. LCD check
  TTK_TM24_PUSH,
  TTK_TM24_LCD1,
  TTK_TM24_LCD2,
  TTK_TM24_LCD3,
  TTK_TM24_LCD4,
  TTK_TM24_LCD5,
  TTK_TM24_LCD6,
  TTK_TM24_LCD7,
  TTK_TM24_LCD8,
  TTK_TM24_LCD9,
  TTK_TM24_LCD10,
  TTK_TM24_LCD11,
  TTK_TM24_LCD12,
  TTK_TM24_LCD13,
  TTK_TM24_LCD14,
  TTK_TM24_LCD15,
  TTK_TM24_BRIGHT10,
  TTK_TM24_BRIGHT50,
  TTK_TM24_BRIGHT100,
  TTK_TM24_POP,

  // testmode 25. Tension adjustment
  TTK_TM25_PUSH,
  TTK_TM25_STRAIGHT_STITCH_SEL,
  TTK_TM25_UTL_ADJUST_MINUS,
  TTK_TM25_UTL_ADJUST_PLUS,
  TTK_TM25_NOT_STRAIGHT_STITCH_SEL,
  TTK_TM25_ETC_ADJUST_MINUS,
  TTK_TM25_ETC_ADJUST_PLUS,
  TTK_TM25_EMB_ADJUST_MINUS,
  TTK_TM25_EMB_ADJUST_PLUS,
  TTK_TM25_STRAIGHT_STITCH1_SEL,
  TTK_TM25_STRAIGHT_STITCH2_SEL,
  TTK_TM25_DEF_OFF,
  TTK_TM25_DEF_ON,
  TTK_TM25_POP,

  // testmode 26. DPP check
  TTK_TM26_PUSH,
  TTK_TM26_PROJ_CALIB_0,
  TTK_TM26_POP,

  // testmode 27. Projector calibration *
  TTK_TM27_PUSH,
  TTK_TM27_PROJ_CALIB_0,
  TTK_TM27_PROJ_CALIB_1,
  TTK_TM27_PROJ_CALIB_2,
  TTK_TM27_PROJ_CALIB_3,
  TTK_TM27_PROJ_BRIGHTNESS_DOWN,
  TTK_TM27_PROJ_BRIGHTNESS_UP,
  TTK_TM27_EMB_UNIT_REMOVE,
  TTK_TM27_POP,

  // testmode 28. Threading check
  TTK_TM28_PUSH,
  TTK_TM28_THPM_HI,
  TTK_TM28_THPM_LOW,
  TTK_TM28_THPM_HI_ADJUST,
  TTK_TM28_THPM_LOW_ADJUST,
  TTK_TM28_THPM_OFF,
  TTK_TM28_THPM_DUTY70,
  TTK_TM28_THPM_DUTY100,
  TTK_TM28_AUTO_THREAD_START,
  TTK_TM28_POP,

  // testmode 29. Input check
  TTK_TM29_PUSH,
  TTK_TM29_CLEAR,
  TTK_TM29_POP,

  // testmode 30. Eeprom USB write LN
  TTK_TM30_PUSH,
  TTK_TM30_EEP_WRITE,
  TTK_TM30_POP,

  // testmode 31. Projector check *
  TTK_TM31_PUSH,
  TTK_TM31_CAMERA,
  TTK_TM31_PROJ_10,
  TTK_TM31_PROJ_13,
  TTK_TM31_PROJ_07,
  TTK_TM31_PROJ_01,
  TTK_TM31_PROJ_04,
  TTK_TM31_PROJ0_BRIGHT_10,
  TTK_TM31_PROJ0_BRIGHT_50,
  TTK_TM31_PROJ0_BRIGHT_100,
  TTK_TM31_PROJ_3X3,
  TTK_TM31_PROJ_CROSS,
  TTK_TM31_PROJ_8DOT,
  TTK_TM31_PROJ_4DOT,
  TTK_TM31_PROJ_2DOT,
  TTK_TM31_PROJ_1DOT,
  TTK_TM31_PROJ_VIEW1,
  TTK_TM31_PROJ_VIEW2,
  TTK_TM31_LAMP,
  TTK_TM31_EMB_UNIT_REMOVE,
  TTK_TM31_POP,

  // testmode 32. AD
  TTK_TM32_PUSH,
  TTK_TM32_POP,

  // testmode 34. X Y Wave table check *
  TTK_TM34_PUSH,
  TTK_TM34_SINGLE,
  TTK_TM34_STOP,
  TTK_TM34_POS_MOVE_X,
  TTK_TM34_X_HHIGH_UP,
  TTK_TM34_X_HIGH_UP,
  TTK_TM34_X_MID_UP,
  TTK_TM34_X_LOW_UP,
  TTK_TM34_X_HHIGH_DOWN,
  TTK_TM34_X_HIGH_DOWN,
  TTK_TM34_X_MID_DOWN,
  TTK_TM34_X_LOW_DOWN,
  TTK_TM34_POS_MOVE_Y,
  TTK_TM34_Y_HHIGH_UP,
  TTK_TM34_Y_HIGH_UP,
  TTK_TM34_Y_MID_UP,
  TTK_TM34_Y_LOW_UP,
  TTK_TM34_Y_HHIGH_DOWN,
  TTK_TM34_Y_HIGH_DOWN,
  TTK_TM34_Y_MID_DOWN,
  TTK_TM34_Y_LOW_DOWN,
  TTK_TM34_POP,

  // testmode 35. Button hole
  TTK_TM35_PUSH,
  TTK_TM35_FWD_ADJUST_MINUS,
  TTK_TM35_FWD_ADJUST_PLUS,
  TTK_TM35_REV_ADJUST_MINUS,
  TTK_TM35_REV_ADJUST_PLUS,
  TTK_TM35_ADJUST_PARAM_LOCK,
  TTK_TM35_POP,

  // testmode 36. Side feed adjustment
  TTK_TM36_PUSH,
  TTK_TM36_SIDE_ADJ_DOWN,
  TTK_TM36_SIDE_ADJ_UP,
  TTK_TM36_FLP_ADJ_MINUS,
  TTK_TM36_FLP_ADJ_PLUS,
  TTK_TM36_FRP_ADJ_MINUS,
  TTK_TM36_FRP_ADJ_PLUS,
  TTK_TM36_CORRECTION,
  TTK_TM36_COFIRMATION,
  TTK_TM36_ADJUST_PARAM_LOCK,
  TTK_TM36_POP,

  // testmode 37. LED pointer adjustment
  TTK_TM37_PUSH,
  TTK_TM37_BRIGHT_OFF,
  TTK_TM37_BRIGHT_MIN,
  TTK_TM37_BRIGHT_MAX,
  TTK_TM37_HEIGHT_MINUS,
  TTK_TM37_HEIGHT_PLUS,
  TTK_TM37_POP,

  // testmode 38. Teaching data viewer
  TTK_TM38_PUSH,
  TTK_TM38_POP,

  // testmode 39. Dual feed check
  TTK_TM39_PUSH,
  TTK_TM39_FORWARD_MINUS,
  TTK_TM39_FORWARD_PLUS,
  TTK_TM39_BACK_MINUS,
  TTK_TM39_BACK_PLUS,
  TTK_TM39_MARGIN_70,
  TTK_TM39_MARGIN_100,
  TTK_TM39_FD_DOWN,
  TTK_TM39_FD_UP,
  TTK_TM39_POP,

  // testmode 40. WLAN　PJL
  TTK_TM40_PUSH,
  TTK_TM40_CONNECT,
  TTK_TM40_POP,

  // testmode 41. WLAN　Firm Version
  TTK_TM41_PUSH,
  TTK_TM41_POP,

  // testmode 42. Emb position adjustment
  TTK_TM42_PUSH,
  TTK_TM42_EMB_LEFT,
  TTK_TM42_EMB_RIGHT,
  TTK_TM42_EMB_UP,
  TTK_TM42_EMB_DOWN,
  TTK_TM42_POP,

  // testmode 43. Touch panel check
  TTK_TM43_PUSH,
  TTK_TM43_POP,

  // testmode 44. Parameter default check
  TTK_TM44_PUSH,
  TTK_TM44_DEF_SET,
  TTK_TM44_POP,

  // testmode 45. Error display
  TTK_TM45_PUSH,
  TTK_TM45_POP,

  // testmode 46. Error list display
  TTK_TM46_PUSH,
  TTK_TM46_POP,

  // testmode 47. PM Initial phase check
  TTK_TM47_PUSH,
  TTK_TM47_PHASE_SET_Z,
  TTK_TM47_PHASE_SET_ZG,
  TTK_TM47_PHASE_CHK_Z,
  TTK_TM47_PHASE_SET_F,
  TTK_TM47_PHASE_SET_FG,
  TTK_TM47_PHASE_CHK_F,
  TTK_TM47_POP,

  // testmode 48. Process check
  TTK_TM48_PUSH,
  TTK_TM48_CHK_WRITE,
  TTK_TM48_POP,

  // testmode 49. Camera calibration*
  TTK_TM49_PUSH,
  TTK_TM49_MOVE_ND_LEFT,
  TTK_TM49_MOVE_ND_RIGHT,
  TTK_TM49_MOVE_ND_UP,
  TTK_TM49_MOVE_ND_DOWN,
  TTK_TM49_SHADING_ADJ,
  TTK_TM49_CAM_CALIB_0,
  TTK_TM49_CAM_CALIB_1,
  TTK_TM49_CAM_CALIB_2,
  TTK_TM49_CAM_CALIB_3,
  TTK_TM49_CAM_CHK_1,
  TTK_TM49_EMB_UNIT_REMOVE,
  TTK_TM49_POP,

  // testmode 50. Utility stitch test
  TTK_TM50_PUSH,
  TTK_TM50_SELECT_1,
  TTK_TM50_SELECT_2,
  TTK_TM50_SELECT_3,
  TTK_TM50_SELECT_4,
  TTK_TM50_SELECT_4_MIN,
  TTK_TM50_SELECT_4_MAX,
  TTK_TM50_SELECT_5,
  TTK_TM50_SELECT_5_MIN,
  TTK_TM50_SELECT_5_MAX,
  TTK_TM50_MODE_ON_OFF,
  TTK_TM50_LENGTH_MIN,
  TTK_TM50_LENGTH_MAX,
  TTK_TM50_POP,

  // testmode 51. Pulse motor Margin check
  TTK_TM51_PUSH,
  TTK_TM51_ZPM_70,
  TTK_TM51_ZPM_100,
  TTK_TM51_FPM_70,
  TTK_TM51_FPM_100,
  TTK_TM51_SPM_70,
  TTK_TM51_SPM_100,
  TTK_TM51_TPM_70,
  TTK_TM51_TPM_100,
  TTK_TM51_ZPM_START,
  TTK_TM51_FPM_START,
  TTK_TM51_SPM_START,
  TTK_TM51_TPM_START_L,
  TTK_TM51_TPM_START_R,
  TTK_TM51_INIT_POS,
  TTK_TM51_TC_MOVE_CHK_POS,
  TTK_TM51_TC_MOVE_MINUS,
  TTK_TM51_TC_MOVE_PLUS,
  TTK_TM51_POP,

  // testmode 52. LED / Speaker check
  TTK_TM52_PUSH,
  TTK_TM52_LED1_OFF,
  TTK_TM52_LED1_MIN,
  TTK_TM52_LED1_MAX,
  TTK_TM52_LED2_OFF,
  TTK_TM52_LED2_MIN,
  TTK_TM52_LED2_MAX,
  TTK_TM52_SPK_MIN,
  TTK_TM52_SPK_MAX,
  TTK_TM52_AT_DEF,
  TTK_TM52_POP,

  // testmode 53. Extended unit check
  TTK_TM53_PUSH,
  TTK_TM53_NORMAL_UP,
  TTK_TM53_CURRENT_UP,
  TTK_TM53_NORMAL_DOWN,
  TTK_TM53_CURRENT_DOWN,
  TTK_TM53_POP,

  // testmode 54. Camera Calibration For Service-Man*
  TTK_TM54_PUSH,
  // testmode 49に定義と同じものを使用
  TTK_TM54_POP,

  // testmode 55. USB Host /SD card check
  TTK_TM55_PUSH,
  TTK_TM55_CHK,
  TTK_TM55_POP,

  // testmode 56. BWD check
  TTK_TM56_PUSH,
  TTK_TM56_OFF,
  TTK_TM56_MIN,
  TTK_TM56_MAX,
  TTK_TM56_POP,

  // testmode 57. Camera calibration check for PL*
  TTK_TM57_PUSH,
  // testmode 49に定義と同じものを使用
  TTK_TM57_POP,

  // testmode 58. Cleaning counter LN
  TTK_TM58_PUSH,
  // testmode 19に定義と同じものを使用
  TTK_TM58_POP,

  // 59. Camera calibration check for SM*
  TTK_TM59_PUSH,
  // testmode 49に定義と同じものを使用
  TTK_TM59_POP,

  // testmode 60. Eeprom USB write
  TTK_TM60_PUSH,
  TTK_TM60_EEP_TO_FILE_PANEL,
  TTK_TM60_EEP_TO_FILE_MACHINE,
  TTK_TM60_EEP_WRITE,
  TTK_TM60_POP,

  // testmode 61. Connection Server
  TTK_TM61_PUSH,
  TTK_TM61_POP,

  // testmode 62. X/Y check
  TTK_TM62_PUSH,
  TTK_TM62_X_MOTOR,
  TTK_TM62_Y_MOTOR,
  TTK_TM62_POP,

  // testmode 63. Projector Calibration For Service-Man*
  TTK_TM63_PUSH,
  TTK_TM63_POP,

  // testmode 64. Scan check
  TTK_TM64_PUSH,
  TTK_TM64_SCAN_CHECK_0,
  TTK_TM64_SCAN_CHECK_1,
  TTK_TM64_POP,

  // testmode 65. SR Unit Check
  TTK_TM65_PUSH,
  TTK_TM65_SR_LASER_ON,
  TTK_TM65_SR_LASER_OFF,
  TTK_TM65_POP,

  // testmode 69. AT pulse motor drive check
  TTK_TM69_PUSH,
  TTK_TM69_CHK_START,
  TTK_TM69_TENSION4_START,
  TTK_TM69_TENSION9_START,
  TTK_TM69_OPEN_START,
  TTK_TM69_POP,

  //特殊モード
  TTK_EEPROM_COPY_PANEL_TO_MACHINE,
  TTK_EEP_COPY_MACHINE_TO_PANEL,
  TTK_PMPHASE_NO_TSET_CONFIRM,
  TTK_NORMALMODE_OK,

  TTK_TM_END,

  /////////////// エラー通知////////////////////////////////

//	EMBFLASHRESUMESELFUN	,
  ERRMYCUSTOMDESIGNCANCELOK	,
  ERRMYCUSTOMDESIGNFINISHOK	,
  ERRMYCUSTOMDESIGNHOMEOK	,
//	HOMERESUMESELFUN	,
  KEY1STCORNERSETTINGRETURNBYPOP	,
  KEYALLLOCK	,
  KEYBORDERALLDELETEFUN	,
  KEYBORDERALLDELETEFUNCANCEL	,
  KEYBORDERALLDELETEFUNFORMARKPATCNCT	,
  KEYBORDERALLDELETEFUNFORPOSITIONING	,
  KEYBORDERCURDELETEANDUNDOSAVE	,
  KEYBORDERMARKDELETEANDEASYSTTIPLEOK	,
  KEYBORDERMARKDELETEANDGROUP	,
  KEYBORDERMARKDELETEANDUNGROUP	,
  KEYBORDERMARKDELETEANDWAPPENMAKE	,
  KEYBWDRIGHTERRCLOSE	,
  KEYBWDROCKCLOSE	,
  KEYCAMERAIMAGETOFD	,
  KEYCANCEL	,
  KEYCANCELANDCAMERASTOP	,
  KEYCANCELANDOUTMODEBACK	,
  KEYCANCELSTARTFABRICHEIGHTSCAN	,
  KEYCANCELUTLFULL	,
  KEYCHANGEORDERMSG	,
  KEYCHECKTWINNEEDLEREMOVEDOK	,
  KEYCMB_MEMORYDELETEOK	,
  KEYCMB_MEMORYDELETESAVEOK	,
  KEYCOLORCHANGECANCELFUN	,
  KEYCOLORCHANGERESETFUN	,
  KEYCONFIRMMARKPATCNCTCANCEL	,
  KEYCONFIRMMARKPATCNCTOK	,
  KEYEDGEQUILTGUIDANCE10OK	,
  KEYEDGEQUILTGUIDANCE11OK	,
  KEYEDGEQUILTGUIDANCE12OK	,
  KEYEDGEQUILTGUIDANCE13OK	,
  KEYEDGEQUILTGUIDANCE14OK	,
  KEYEDGEQUILTGUIDANCE15OK	,
  KEYEDGEQUILTGUIDANCE16OK	,
  KEYEDGEQUILTGUIDANCE17OK	,
  KEYEDGEQUILTGUIDANCE1OK	,
  KEYEDGEQUILTGUIDANCE2OK	,
  KEYEDGEQUILTGUIDANCE3OK	,
  KEYEDGEQUILTGUIDANCE4OK	,
  KEYEDGEQUILTGUIDANCE5OK	,
  KEYEDGEQUILTGUIDANCE6OK	,
  KEYEDGEQUILTGUIDANCE7OK	,
  KEYEDGEQUILTGUIDANCE8OK	,
  KEYEDGEQUILTGUIDANCE9OK	,
  KEYEMBCOMPFRAMESCANSTART	,
  KEYEMBDOWNTHREADRETURN	,
//	KEYEMBFRAMEMOVEFORMARKREMOVE	,
  KEYEMBGROUPDELETE	,
//	KEYEMBPOSITIONINGOK	,
  KEYEMBUNITREMOVEBW	,
  KEYEMBUNITREMOVEDOWNTHREAD	,

  KEYERRALLLOCKCANCEL	,
  KEYERRQUILTSASHES1STMOVETOROTATEOK	,
  KEYERRQUILTSASHES1STTOMOVEOK	,
  KEYERRQUILTSASHESCANCEL	,
  KEYERRQUILTSASHESCONERMOVETOROTATEOK	,
  KEYERRQUILTSASHESLASTMOVETOROTATEOK	,
  KEYERRQUILTSASHESMOVETOROTATEOK	,
  KEYERRQUILTSASHESOK	,
  KEYERRQUILTSASHESTOMOVEOK	,
  KEYEULACONFOK	,
  KEYEXECSETTINGSDEFAULT	,
  KEYFLASHPATTERNDELETEFORSELECTSCREEN	,
  KEYFRAMEMOVE	,
  KEYFRAMEMOVEBW	,
  KEYFRAMEMOVEBW2	,
  KEYFRAMEMOVEMARKPATCNCTKEYOK	,
  KEYGODELETEFORDELETESCREENMCD	,
  KEYGODELETEFORSELECTSCREENMCD	,
  KEYGOFDDELETEFORDELETESCREEN	,
  KEYGOFDDELETEFORSELECTSCREEN	,
  KEYGOHOSTDELETEFORDELETESCREEN	,
  KEYGOHOSTDELETEFORSELECTSCREEN	,
  KEYGOTOCLOCKSETTING	,
  KEYGOTOCWSSETTING	,


  KEYGOTOEDITSEWINGFORLONGSTITCH	,
  KEYGOTOEDITSEWINGWITHMARKALLDEL	,
  KEYGOTOEMBDONOMOYOU	,
  KEYGOTOEMBEDITKIOKUFLOPPYFORDELETESCREEN	,
  KEYGOTOEMBEDITKIOKUHOSTFORDELETESCREEN	,
  KEYGOTONETRETURN	,
  KEYGOTOUNREGISTPINCODE	,
  KEYGOTOUPLOADING	,
  KEYGOTOWLANSETTIN	,
  KEYJPGDELETE	,
  KEYMAINTENANCEOK	,
  KEYMANMEMALLDELETE	,
  KEYMANMEMDELETE	,
  KEYMARKPATEXSITCANCEL	,
  KEYMARKRESETMARKPATCNCTOK	,
  KEYMDCBGDELETE	,
  KEYMDCCUSTOMPATTERNCHGAFTERSAVING	,
  KEYMDCCUSTOMPATTERNCHOOSEREPLACE	,
  KEYMDCCUSTOMPATTERNDELETEFAIL	,
  KEYMDCCUSTOMPATTERNDELETEOK	,
  KEYMDCCUSTOMPATTERNEMBDATASAVED	,
  KEYMDCCUSTOMPATTERNREPLACEOK	,
  KEYMODEDEPENDRETURN	,
  KEYMYCUSTOMDESIGNNEWOK	,
  KEYMYDESIGNCENTERCANCELOK	,
  KEYMYDESIGNCENTERERRCLOSEOK	,
  KEYMYDESIGNCENTERFINISHOK	,
  KEYMYDESIGNCENTERHOMEOK	,
  KEYMYDESIGNCENTERMEMORYFULL	,
  KEYMYDESIGNCENTERNEWOK	,
  KEYMYI_DECOSEL_CANCELSUB	,
  KEYMYI_MEMORYDELETEOK	,
  KEYMYI_MEMORYDELETEOKSELECTSAVEDFILE	,
  KEYMYI_MEMORYDELETESAVEOK	,
  KEYNETDIAGCANCEL	,
  KEYNOMARKAFTERMARKPATCNCTOK	,
  KEYOKANDOUTMODEBACK	,
  KEYOKKEYSLOWTH	,
  KEYOKTHUNITHITFRAME	,
  KEYPFTMOVE	,
  KEYPOP2WITHINIT	,
  KEYREMOVECOMPFRAMESCANORGIMGDATAFILE	,
  KEYREMOVEMARKRESETMARKPATCNCTOK	,
  KEYREPOSITIONSETTINGRETURNBYPOP	,
  KEYRESETNEW1STMARKPATCNCTOK	,
  KEYRESETNEW2NDMARKPATCNCTOK	,
  KEYRESTARTFABRICHEIGHTSCANOK	,
  KEYRESUMEDELETEANDRETURNBYPOP	,
  KEYRETRYSEARCHBEFOREANDNEWMARKPATCNCTOK	,
  KEYRETURNBYPOP	,
  KEYRETURNBYPOPANDPATTERNSIZESETPREV	,
  KEYRETURNBYPOPEMBROIDERYORGROUPRIGHTTURNBIG	,
  KEYRETURNBYPOPFORCLOCK	,
  KEYRETURNBYPOPFORCOMPLEX	,
  KEYRETURNBYPOPFROMUSB	,
  KEYRETURNBYPOPGROUPRIGHTTURNBIG	,
  KEYRETURNBYPOPNEEDLECHANGE	,
  KEYRETURNBYPOPPOP	,
//	KEYRETURNBYPOPPOPMARKREMOVE	,
  KEYRETURNBYPOPWITHCHANGENEEDLEBOARD	,
  KEYRETURNBYPOPWITHGOTOEDITSELECTPREVIEW	,
  KEYRETURNBYPOPWITHPATTERNDELETE	,
  KEYRETURNBYPOPWITHPATTERNDELETEANDFILESELECTRESET	,
  KEYRETURNBYPOPWITHREARRAY	,
  KEYRETURNBYPOPWITHSCANFILESELECT	,
  KEYRETURNBYPOPWITHUPDATEMONITORINGSTATUS	,
  KEYRETURNBYPOPWITHUPDATEMONITORINGSTATUSANDSASEND	,
  KEYRETURNCANCEL	,
  KEYRETURNEMBGROUPFLUSHORBYPOP	,
  KEYRETURNERRSEWINGOVER	,
  KEYRETURNNOACT	,
  KEYRETURNOK	,
  KEYRETURNTOCONTINUATION	,
  KEYRETURNTOEMBCATEGORY	,
  KEYSCANSHADINGUPDATECANCEL	,
  KEYSCANSHADINGUPDATESCAN	,
//	KEYSETPOP, // KeySetPop
  KEYSETMARKPATCNCTSTDCANCEL	,
  KEYSETPOPTOBROTHERFORCOMPLEX	,
  KEYSEWAUTOSTOPJUSTBEFORESTOPCLOSE	,
  KEYSEWAUTOSTOPOFF	,
  KEYSEWAUTOSTOPRESETCORRECTERROR	,
  KEYSEWAUTOSTOPRESETLENGTHERROR	,
  KEYSEWAUTOSTOPRESETMSG	,
  KEYSEWAUTOSTOPRESETOPROJECTORVIEWMSG	,
  KEYSEWNEXTPARTSFUN	,
  KEYSEWTAPERINGCURRENTRESETMSG	,
  KEYSEWTAPERINGRESETAUTOSTOPMSG	,
  KEYSEWTAPERINGRESETBUTTONMSG	,
  KEYSEWTAPERINGRESETCUETOPMSG	,
  KEYSEWTAPERINGRESETCYCLENUMMSG	,
  KEYSEWTAPERINGRESETDECREASENUMMSG	,
  KEYSEWTAPERINGRESETENDANGLEMSG	,
  KEYSEWTAPERINGRESETFLIPOFFMSG	,
  KEYSEWTAPERINGRESETFLIPONMSG	,
  KEYSEWTAPERINGRESETINCREASENUMMSG	,
  KEYSEWTAPERINGRESETMSG	,
  KEYSEWTAPERINGRESETRETRIEVEMSG	,
  KEYSEWTAPERINGRESETSTARTANGLEMSG	,
  KEYSEWTAPERINGRESETTOPROJECTORVIEWMSG	,
  KEYSTARTFABRICHEIGHTSCAN	,
  KEYSTBMODECHANGE	,
  KEYSUCCESSBEFOREMARKPATCNCTOK	,
  KEYSUCCESSMARKPATCNCTOK	,
  KEYUTLDELETEANDKAZARI1PTNSELFUN	,
  KEYVERUPFORINSTALLMOVIEOK	,
  KEYWLANSETUPCONFIRMATIONNOTNOW	,
  KEYWLANSETUPCONFIRMATIONOK	,

  //自動解除用の
  KEYERRNEEDLEDOWN	,
  KEYERROSAEUP	,
  KEYERROSAEDOWN	,
  KEYERRFRAMEOFF	,
  KEYERRFRAMESET	,
  KEYERREMBPRJCLOSENOFRAME	,
  KEYERREXCEPTSFRAMESET	,
  KEYERRBHRECG	,
  KEYERRORFOTJACKOUT	,
  KEYERRORSSWHENFOOTON	,
  KEYERRORTHREADRESET	,
  KEYERROREMBFRAMEHOLDLEVERDOWNPOWERFORMONITORING	,
  KEYERROREMBFRAMEHOLDLEVERDOWNPOWERON	,
  KEYERRORLEDPOINTERREMOVEWHENUTL	,
  KEYERRORDFREMOVEWHENEMB	,
  KEYCLEARAPPERRORSTATE,

  // KEYFRAMEMOVESTARTPROJECTOR	,
  // KEYFRAMEMOVECLOSEPROJECTOR	,
  // KEYFRAMEOFFWAITINGFORCEQUIT	,

  // 刺しゅうレジューム
  EMBFLASHRESUMESELFUN,
  HOMERESUMESELFUN,
  KEYGOTOEMBRESUMEOK,			// KeyGotoEmbResumeOk

  /////////////テストコード////////////////////////////////
  /* ↓刺繍テスト */
//	BPIF_SEND_EMB_1, // OutKeyGoToEditFromHome
//	BPIF_SEND_EMB_4, // KeyEmbSelFun
//	BPIF_SEND_EMB_5, // KeyGoToEditEditingFromChar
//	BPIF_SEND_EMB_8, // gETD_IN_KeySelect
//	BPIF_SEND_EMB_9, // gETD_IN_KeySelect

  /* ↓Circle 12~15 */
//	BPIF_SEND_CIRCLE_2, // X1EmbMarkSelFun

  /* ↓UTL 16~18*/
  BPIF_SEND_UTL_START,  // OutKeyGoToUtlFromHome  ホームから実用画面
  BPIF_SEND_UTL_SEL,	  // UtlPtnSelFun　製品では共有メモリで対応

  // 終点設定のPopup 21
  BPIF_SEND_UTL_AUTO_STOP, // KeyUtlSewAotoStopChangePopUpSts

  // キャリブレーションモード 22~24
  BPIF_SEND_TEST_NEXT_PAGE,  // KeyGoTstSelNext テストモードで次のページ
  BPIF_SEND_TEST5,		   // KeySetTestMode5 テストモード5　
  BPIF_SEND_TEST_CAM_RETURN, // KeyReturnCameraTestModeMenu

  // キャリブレーションモード  25~28   test_CameraCalibration.cpp */
  BPIF_SEND_TEST_CAM_CALB0, // KeyTestCameraCalib_0
  BPIF_SEND_TEST_CAM_CALB1, // KeyTestCameraCalib_0
  BPIF_SEND_TEST_CAM_CALB2, // KeyTestCameraCalib_2
  BPIF_SEND_TEST_CAM_CALB3, // KeyTestCameraCalib_3
  /* 29~30  */
  BPIF_SEND_TEST_CAM_CHECK, // KeyTestCameraCheck_1
  BPIF_SEND_TEST_CAM_SHAD,  // KeyGetShadingAdj
  /* 31~34  */
  BPIF_SEND_TEST_CAM_SEARCH_LEFT,	 // KeyMoveNdlImgSearchPosLeft
  BPIF_SEND_TEST_CAM_SEARCH_RIGHT, // KeyMoveNdlImgSearchPosRight
  BPIF_SEND_TEST_CAM_SEARCH_UP,	 // KeyMoveNdlImgSearchPosUp
  BPIF_SEND_TEST_CAM_SEARCH_DOWN,	 // KeyMoveNdlImgSearchPosDown

  /* 35~38  */
  BPIF_SEND_TEST14, // KeySetTestMode14

  BPIF_SEND_SR_FOOT_HEIGHT_PLUS,	// 42 KeySRPfPosPlusWrite   /*** IIVO_V100　IIVO_SR-4-2　2024/03/22　minemayo ***/
  BPIF_SEND_SR_FOOT_HEIGHT_MINUS, // 43 KeySRPfPosMinusWrite  /*** IIVO_V100　IIVO_SR-4-2　2024/03/22　minemayo ***/

  /* SR用　44~48  */
  /* 44　実用初期関数 */
  BPIF_SEND_UTL_INIT, // UtlPtnSelFun　製品では共有メモリで対応
  // SR選択 45
  BPIF_SEND_SR_ON,  // NULL SR選択 ON　製品では共有メモリで対応
  BPIF_SEND_SR_OFF, // NULL SR選択 OFF 製品では共有メモリで対応

  // 47
  BPIF_SEND_UTL_END, // OutKeyGoToHome　実用画面からホームへ

  // SR選択 48～50
  BPIF_SEND_SR_MODE_I, // 48　SR選択 KeySRMode_Intermit 　製品では共有メモリで対応
  BPIF_SEND_SR_MODE_C, // 49　SR選択 KeySRMode_Continuous 　製品では共有メモリで対応
  BPIF_SEND_SR_MODE_S, // 50　SR選択 KeySRMode_Syncronous 　製品では共有メモリで対応

  /* 終点設定 51～52 */
  BPIF_SEND_UTL_AUTO_STOP_ON,	 // KeySewingAutoStopSetON
  BPIF_SEND_UTL_AUTO_STOP_OFF, // KeySewingAutoStopSetOFF

  // BG SCAN 53～56
  BPIF_SEND_BG_SCAN_1, // 53 :
  BPIF_SEND_BG_SCAN_2, // 54 :
  BPIF_SEND_BG_SCAN_3, // 55 :
  BPIF_SEND_BG_SCAN_4, // 56 :
  // カメラBH 57～64
  BPIF_SEND_CAMBH_5,	// 57 : 1-01: 0
//	BPIF_SEND_CAMBH_6,	// 58 : カテゴリ選択 4
//	BPIF_SEND_CAMBH_7,	// 59 : カテゴリ選択 1
  BPIF_SEND_CAMBH_8,	// 60 : 4-01: 82
  BPIF_SEND_CAMBH_9,	// 61 : 4-07: 88
  BPIF_SEND_CAMBH_10, // 62 : 4-13: 94
  BPIF_SEND_CAMBH_11, // 63 : 4-22: 103
  BPIF_SEND_CAMBH_12, // 64 : 1-01: 0

  // EEPROM main firm upgrade flag 65～66
  BPIF_UPGRADE_EEP_ON,  /* 65 : debug EEPROM main firm upgrade flag on */
  BPIF_UPGRADE_EEP_OFF, /* 66 : debug EEPROM main firm upgrade flag off */

  // 22 KeyGoTstSelNext テストモードで次のページ
  BPIF_SEND_TEST_PREV_PAGE, // 67 : KeyGoTstSelPrev テストモードで前のページ

  /////////////ここまでテストコード////////////////////////////////
  /////////////IIVOアプリ////////////////////////////////
  //------------------------------- 共通-----------------------
  //Opening
  BPIF_INIT_EXITOPENING,
  BPIF_INIT_STARTDEMO,

  BPIF_GLOBAL_OUTKEYGOTOHOME,
  BPIF_GLOBAL_KEYKEYROCKSET,
  BPIF_GLOBAL_KEYKEYROCKCLEAR,
  BPIF_GLOBAL_OUTKEYALLLOCKONOFF,
  BPIF_GLOBAL_OUTKEYPUSHTOHOWTOUSE,//How to Use
  BPIF_GLOBAL_RETURNHOWTOUSE,
  BPIF_GLOBAL_OUTKEYPUSHTOSETTINGS,//設定画面
  BPIF_GLOBAL_KEYRETURNBYPOPFORSETTING,
  BPIF_GLOBAL_OUTKEYPUSHTOCHANGEVIEW,//針落ち点表示
  BPIF_GLOBAL_KEYRETURNBYPOPCHANGEVIEW,
  BPIF_GLOBAL_OUTKEYPUSHTOSETTINGSMODEWLAN,//WLAN画面
  BPIF_GLOBAL_RETURNWLANSETTING,
  BPIF_GLOBAL_OUTKEYPUSHTOCLOCKSETTING,//時計画面
  BPIF_GLOBAL_RETURNCLOCKSETTING,
  BPIF_GLOBAL_SET_ERROR_STATUS,	// エラー状態をセット
  BPIF_GLOBAL_CLEAR_ERROR_STATUS, // エラー状態を解除

  BPIF_GLOBAL_START_SCREEN_SAVER, // スクリーンセーバ開始
  BPIF_GLOBAL_STOP_SCREEN_SAVER,	// スクリーンセーバ停止
  BPIF_GLOBAL_START_SLEEP,		// Sleep状態を開始
  BPIF_GLOBAL_STOP_SLEEP,			// Sleep状態を停止
  BPIF_GLOBAL_START_ECO,			// ECO状態を開始
  BPIF_GLOBAL_STOP_ECO,			// ECO状態を停止

  BPIF_GLOBAL_DSP_REFRESH, // Draw関数の実行
  BPIF_GLOBAL_DO_INIT,	 // Init関数の実行

  BPIF_GLOBAL_KEYSTOPCAMERAVIEW,//カメラを強制終了する

  //------------------------------- BWD-----------------------
  BPIF_BWD_KEYBWDPOPON,
  BPIF_BWD_KEYBWDPOPOFF,
  BPIF_BWD_KEYBWDSPEEDMINUS,
  BPIF_BWD_KEYBWDSPEEDPLUS,
  BPIF_BWD_KEYBWDSTART,
  BPIF_BWD_KEYBWDSTOP,
  //------------------------------- 針落ち点表示-----------------------
  BPIF_VIEWCHANGE_KEYNEEDLEVIEWCHANGE,
  BPIF_VIEWCHANGE_KEYLATTICEVIEWCHANGE,
  BPIF_VIEWCHANGE_KEYVIEWDETAILCHANGE,
  BPIF_VIEWCHANGE_KEYLATTICEVIEWCHANGEEMBSEWING,

  //------------------------------- Home-----------------------
  BPIF_HOME_OUTKEYGOTOUTLFROMHOME,
  BPIF_HOME_OUTKEYGOTOEDITFROMHOME,
  BPIF_HOME_OUTKEYGOTODISNEYFROMHOME,
  BPIF_HOME_OUTKEYGOTOMYCUSTOMDESIGNFROMHOME,

  //------------------------------- Utl-----------------------
  BPIF_UTL_UTLPTNSELFUN,
  BPIF_UTL_OUTKEYGOTOUTL,
  BPIF_UTL_OUTKEYGOTOCHARDECO,
  BPIF_UTL_PUSHTOMYILLUSTSELECT,
  BPIF_UTL_KEYMYI_CLOSE,
  BPIF_UTL_KEYMYI_SEWING,
  BPIF_UTL_KEYMYI_KEYMYI_RE_EDIT,
  BPIF_UTL_KEYMYI_KEYSELECTGOTOMYICHECK,

  // BPIF_UTL_KEYGOTOUTLMANUALRETRIEVE,
  BPIF_UTL_KEYUTLCHANGEDISABLE,

  // BPIF_UTL_KEYUTLMEMORY,
  // BPIF_UTL_KEYUTLMEMORYAUTO,
  BPIF_UTL_KEYPUSHTOASPECTVIEW,
  BPIF_UTL_KEYRETURNBYPOPCHANGEVIEW,

  // プレビュー
  BPIF_UTL_KEYGOTOIMAGE_JITU,
  BPIF_UTL_KEYRETURNBYPOPFORUTLCHECKPAGE,

  // プロジェクタ終了
  BPIF_UTL_KEYPROJECTORVIEWWAITSEWCONTROL,
//	BPIF_UTL_KEYPROJECTORVIEWSEWINGCONTROLCLOSE,
//	BPIF_UTL_KEYPROJECTORVIEWSEWINGLINEMAKERCLOSE,
  BPIF_UTL_KEYPROJECTORVIEWWAITLINEMAKER,

  // 終点設定
  BPIF_UTL_KEYUTLSEWAOTOSTOPCHANGEPOPUPSTS,
  BPIF_UTL_KEYSEWINGAUTOSTOPSETON,
  BPIF_UTL_KEYSEWINGAUTOSTOPSETOFF,
  BPIF_UTL_KEYSEWINGAUTOSTOPCLOSE,

  // テーパリング
  BPIF_UTL_KEYUTLSEWTAPERINGCHANGEPOPUPSTS,
  BPIF_UTL_KEYSEWINGTAPERINGSETON,
  BPIF_UTL_KEYSEWINGTAPERINGSETOFF,
  BPIF_UTL_KEYSEWINGTAPERINGSETTINGBUTTONON,
  BPIF_UTL_KEYSEWINGTAPERINGSETTINGREPEATON,
  BPIF_UTL_KEYSEWINGTAPERINGSETTINGAUTOSTOPON,
  BPIF_UTL_KEYSEWINGTAPERINGCLOSE,
  BPIF_UTL_KEYSEWINGTAPERINGRESTART,
  BPIF_UTL_KEYSEWINGTAPERINGSETTINGSTARTENDCLOSE,
  /* 自動押え上げ */
  BPIF_UTL_KEYAUTOPFLIFTUPTOON,
  BPIF_UTL_KEYAUTOPFLIFTUPTOOFF,
  /* 針上下 */
  BPIF_UTL_KEYNEEDLETODOWN,
  BPIF_UTL_KEYNEEDLETOUP,

  /* フリーモーション */
  BPIF_KEYFEEDDOGDOWNSET,
  BPIF_KEYFEEDDOGDOWNCLEAR,
  /* ピボット　*/
  BPIF_KEYPIVOTMODEONSET,
  BPIF_KEYPIVOTMODEOFFSET,
  /* 糸切り */
  BPIF_KEYUTLCUTTOON,
  BPIF_KEYUTLCUTTOOFF,
  /* 返し縫い */
  BPIF_KEYUTLSTOPSEWTOON,
  BPIF_KEYUTLSTOPSEWTOOFF,

  KEY_GOTO_SR_DIR, // GotoSRDir
  KEY_EXIT_SR_DIR, // ExitSRDir
  BPIF_KEY_SR_MODE_BASTING_WARN,	// KeySRMode_Basting_warn
  BPIF_KEY_SR_BASTING_OK,	// KeySRBastingOK

  // BHスリット長さ自動検出
  BPIF_UTL_KEYBHSLITLENGTHAUTOUPDATE,	// KeyUtlBHSlitLengthAutoUpdate

  //------------------------------- Emb-----------------------
  // ↓刺繍編集プロジェクターUT向け対応
  DUMMY680,//KEYPRJ_EMBFRAMEMOVE_LEFTUP_UT,
  DUMMY681,//KEYPRJ_EMBFRAMEMOVE_UP_UT,
  DUMMY682,//KEYPRJ_EMBFRAMEMOVE_RIGHTUP_UT,
  DUMMY683,//KEYPRJ_EMBFRAMEMOVE_LEFT_UT,
  DUMMY684,//KEYPRJ_EMBFRAMEMOVE_CENTER_UT,
  DUMMY685,//KEYPRJ_EMBFRAMEMOVE_RIGHT_UT,
  DUMMY686,//KEYPRJ_EMBFRAMEMOVE_LEFTDOWN_UT,
  DUMMY687,//KEYPRJ_EMBFRAMEMOVE_DOWN_UT,
  DUMMY688,//KEYPRJ_EMBFRAMEMOVE_RIGHTDOWN_UT,
  // ↑刺繍編集プロジェクターUT向け対応

  //刺繍枠移動
  BPIF_SEND_EMB_KEYEMBUNITREMOVE,

  // 刺しゅう縫製
  BPIF_SEND_EMBSEW_MOVE_SELECT,		// KeyGoToEmbMove
  BPIF_SEND_EMBSEW_ROTATE_SELECT,		// KeyGoToAllRotate
  DUMMY692,	// KeyEmbBastingAdd / KeyEmbBastingAddReset
  DUMMY693,	// KeyNonStopSewOn / KeyNonStopSewOff
  DUMMY694,	// KeyEmbColorSortOn / KeyEmbColorSortOff
  DUMMY695,	// KeyEmbSetMarkPatCnct
  DUMMY696,	// KeyGoToEmbEditSewingTool / KeyReturnWhenAllGroupImageIsReady
  BPIF_SEND_EMB_KEYEMBEDITLEDPOINTERON,//KeyEmbEditLedPointerON
  BPIF_SEND_KEYLEDPOINTEROFF,	// KeyLedPointerOFF
  BPIF_SEND_EMB_KEYGOTOEMBMOVEQUILT,//KeyGoToEmbMoveQuilt
  BPIF_SEND_EMB_KEYGOTOALLROTATEQUILT,//KeyGoToAllRotateQuilt
  //縫製画面の枠退避
  BPIF_SEND_EMBSEW_KEYFRAMEREMOVESUB,	//KeyFrameRemoveSub
  KEYFRAMEREMOVEOKSUB,				//KeyFrameRemoveOkSub
  KEYFRAMEREMOVERETURNOKSUB,			//KeyFrameRemoveReturnOkSub

  /* プロジェクター刺繍模様投影位置に関するパラメータを初期化する */
  BPIF_EMB_KEYPRJEMBAREAPARAMRESET,	// KeyPrjEmbAreaParamReset
  /* プロジェクター投影用布厚を測定する */
  BPIF_EMB_KEYPRJSTARTEMBMEASUREPRESSERFOOT, // KeyPrjStartEmbMeasurePresserFoot
  /* 刺繍投影開始時枠駆動量を取得する */
  BPIF_EMB_KEYPRJSTARTBEFOREFRAMEMOVEAMOUNT, // KeyPrjStartBeforeFrameMoveAmount
  /* 刺繍投影開始終了時事前エラーチェック */
  BPIF_EMB_KEYCHECKERRBEFOREEMBPROJECTIONSTARTCLOSE,	// KeyCheckErrBeforeEmbProjectionStartClose
  /* 刺繍投影開始の枠駆動時メッセージ表示 */
  BPIF_EMB_KEYPRJSTARTBEFOREFRAMEMOVEMSG, // KeyPrjStartBeforeFrameMoveMsg
  /* 投影開始・終了時PleaseWaitメッセージ表示 */
  BPIF_EMB_KEYPRJPLEASEWAITMSGDISP, // KeyPrjPleaseWaitMsgDisp
  /* 投影開始時PleaseWaitメッセージ表示 */
  BPIF_EMB_KEYPRJSTARTPLEASEWAITMSGDISP, // KeyPrjStartPleaseWaitMsgDisp
  /* 刺繍プロジェクタ投影時の枠駆動 */
  BPIF_EMB_KEYFRAMEMOVEEMBPROJECTOR,  // KeyFrameMoveEmbProjector
  /* 投影開始時PleaseWaitメッセージ表示終了 */
  BPIF_EMB_KEYPRJSTARTPLEASEWAItMSGCLOSE,  // KeyPrjStartPleaseWaitMsgClose
  /* 刺繍投影終了時枠駆動量を取得する */
  BPIF_EMB_KEYPRJCLOSEBEFOREFRAMEMOVEAMOUNT,  // KeyPrjCloseBeforeFrameMoveAmount
  /* 刺繍投影終了の枠駆動時メッセージ表示 */
  BPIF_EMB_KEYPRJCLOSEBEFOREFRAMEMOVEMSG,  // KeyPrjCloseBeforeFrameMoveMsg
  /* 投影終了時PleaseWaitメッセージ表示 */
  BPIF_EMB_KEYPRJCLOSEPLEASEWAITMSGDISP,  // KeyPrjClosePleaseWaitMsgDisp
  /* 投影終了時PleaseWaitメッセージ表示終了 */
  BPIF_EMB_KEYPRJCLOSEPLEASEWAItMSGCLOSE,  // KeyPrjClosePleaseWaitMsgClose
  /* 刺繍投影範囲移動時枠駆動 */
  BPIF_EMB_KEYEMBROIDERYFRAMEMOVE,  // KeyEmbroideryFrameMove
  /* 刺繍プロジェクタ投影中に枠外しの強制投影終了時の枠駆動前チェック */
  BPIF_EMB_KEYCHECKERRBEFOREPROJECTIONCLOSENOFRAME,	// KeyCheckErrBeforeProjectionCloseNoFrame

//************************* API化済み(数値に依存しないもの)******************************

  // 刺しゅう状態遷移
  KEYGOTOEDITEDITING,			// KeyGoToEditEditing
  KEYGOTOEDITSEWING,			// KeyGotoEditSewing
  KEYRETURNEDIT,				// KeyReturnEdit
  KEYEDITADD,                 //KeyEditAdd
  KEYEDITADDGRAY,                 //KeyEditAddGray
  KEYPUSHTOMYDESIGNCENTERFROMEMB,//KeyPushToMyDesignCenterFromEmb
  KEYGOTOEDITEDITINGFROMSELECTRETURN,//KeyGoToEditEditingFromSelectReturn;
  GOTOEMBFROMOPENING,		// GotoEmbFromOpening

  // スノーマン
  KEYEMBEDITPOSITION,						// KeyEmbEditPosition
  KEYEMBULTRSNICPOSITIONPTNRESETOK,		// KeyEmbUltrSnicPositionPtnResetOK
  KEYEMBULTRSNICPOSITIONPTNRESETCANCEL,	// KeyEmbUltrSnicPositionPtnResetCancel
  KEYEMBICHISCAN,							// KeyEmbIchiScan
  KEYEMBICHICLOSE,						// KeyEmbIchiClose
  KEYEMBULTRSNICPOSITIONNOTOUCHFRAMEMOVEOK,// KeyEmbUltrSnicPositionNoTouchFrameMoveOK
  KEYEMBPOSITIONINGOK,					// KeyEmbPositioningOK(ポップアップ)
  KEYRETURNBYPOPPOPMARKREMOVE,			// KeyReturnByPopPopMarkRemove(ポップアップ)
  KEYEMBFRAMEMOVEFORMARKREMOVE,			// KeyEmbFrameMoveForMarkRemove(ポップアップ)

  // 背景スキャン
  KEYEMBCOMPFRAMESCANBACKGROUNDIMG,			// KeyEmbCompFrameScanBackgroundImg
  KEYEMBCOMPFRAMESCANIMGOK,					// KeyEmbCompFrameScanImgOK(ポップアップ)
  KEYEMBCOMPFRAMESCANIMGCANCEL,				// KeyEmbCompFrameScanImgCancel(ポップアップ)
  KEYEMBMARKFABRICHEIGHTSCANBACKGROUND,		// KeyEmbMarkFabricHeightScanBackground
  KEYEMBMARKFABRICHEIGHTSCANEXCHANGEPATTERN,	// KeyEmbMarkFabricHeightScanExchangePattern

  // FB
  KEYGOTOEMBFB,						// KeyGoToEmbFB
  KEYEMBSTITCHPREV1000,				// KeyEmbStitchPrev1000
  KEYEMBSTITCHPREV100,				// KeyEmbStitchPrev100
  KEYEMBSTITCHPREV10,					// KeyEmbStitchPrev10
  KEYEMBSTITCHPREV,					// KeyEmbStitchPrev
  KEYEMBSTITCHATAMADASI,				// KeyEmbStitchATaMaDaSi
  KEYEMBSTITCHNEXT,					// KeyEmbStitchNext
  KEYEMBSTITCHNEXT10,					// KeyEmbStitchNext10
  KEYEMBSTITCHNEXT100,				// KeyEmbStitchNext100
  KEYEMBSTITCHNEXT1000,				// KeyEmbStitchNext1000
  KEYEMBTHREADCOLORNEXT,				// KeyEmbThreadColorNext
  KEYEMBTHREADCOLORPREV,				// KeyEmbThreadColorPrev
  KEYEMBINDICATOR,					// KeyEmbIndicator
  KEYGOTOEMBSEWINGFBCAMERAVIEW,		// KeyGoToEmbSewingFBCameraView
  KEYGOTOEMBFBFORCHAGEVIEW,		// KeyGoToEmbFBForChageView
  KEYEMBSEWINGFBRETURN,				// KeyEmbSewingFBReturn

  // つなぎ
  KEYSEWOVERMARKPATCNCTOK,			// KeySewOverMarkPatCnctOK(ポップアップ)
  KEYSEWOVERMARKPATCNCTCANCEL,		// KeySewOverMarkPatCnctCancel(ポップアップ)
  KEYNEXTPATTERNSELMARKPATCNCTOK,		// KeyNextPatternSelMarkPatCnctOK(ポップアップ)
  KEYSETMARKPATCNCTSETTINGOK,			// KeySetMarkPatCnctSettingOK
  KEYSETMARKPATCNCTSTDCANCELBEFORE,	// KeySetMarkPatCnctStdCancelBefore(ポップアップ)
  KEYSETBEFORE1STMARKPATCNCTOK,		// KeySetBefore1stMarkPatCnctOK
  KEYSETBEFORE2NDMARKPATCNCTOK,		// KeySetBefore2ndMarkPatCnctOK
  //自動つなぎ(大型分割、キルトつなぎ)
  KEYSEWOVERAUTOMARKPATCNCTOK,		// KeySewOverAutoMarkPatCnctOK(ポップアップ)
  KEYSEWOVERAUTOMARKPATCNCTCANCEL,	// KeySewOverAutoMarkPatCnctCancel(ポップアップ)
  KEYEMBROIDERYMOVEQUILTRETURN,//KeyEmbroideryMoveQuiltReturn
  KEYEMBROIDERYROTATEQUILTRETURN,//KeyEmbroideryRotateQuiltReturn
  KEYCORNERSETTINGRETURNBYPOP,//KeyCornerSettingReturnByPop

  // マスクトレース
  KEYGOTOEMBEDITSEWINGTRIAL,			// KeyGoToEmbEditSewingTrial
  KEYEMBTRIALABLE,					// KeyEmbTrialAble
  KEYEMBSEWINGSTARTPOINT_LEFTUP,		// KeyEmbSewingStartPoint_LeftUp
  KEYEMBSEWINGSTARTPOINT_UP,			// KeyEmbSewingStartPoint_Up
  KEYEMBSEWINGSTARTPOINT_RIGHTUP,		// KeyEmbSewingStartPoint_RightUp
  KEYEMBSEWINGSTARTPOINT_LEFT,		// KeyEmbSewingStartPoint_Left
  KEYEMBSEWINGSTARTPOINT_CENTER,		// KeyEmbSewingStartPoint_Center
  KEYEMBSEWINGSTARTPOINT_RIGHT,		// KeyEmbSewingStartPoint_Right
  KEYEMBSEWINGSTARTPOINT_LEFTDOWN,	// KeyEmbSewingStartPoint_LeftDown
  KEYEMBSEWINGSTARTPOINT_DOWN,		// KeyEmbSewingStartPoint_Down
  KEYEMBSEWINGSTARTPOINT_RIGHTDOWN,	// KeyEmbSewingStartPoint_RightDown

  // 縫い始め設定
  KEYGOTOEMBEDITSEWINGSTARTPOINT,		// KeyGoToEmbEditSewingStartPoint
  KEYEMBNEEDLEPOSITIONTOSTART,		// KeyEmbNeedlePositionToStart
  KEYEMBNEEDLEPOSITIONTOCENTER,   //KeyEmbNeedlePositionToCenter
  //キルトつなぎ
  BPIF_SEND_QUIL_KEYEMBROIDERYROTATEQUILT,//KeyEmbroideryRotateQuiltRight001～KeyEmbroideryRotateQuiltLeft10
  BPIF_SEND_QUIL_KEYEMBFRAMEMOVEQUILT,//KeyEmbFrameMoveQuilt_###
  BPIF_EMB_QUILT_SCALING_MAGNIFICATION,//KeyEmbSewMagnificationX,KeyEmbSewReductionX,KeyEmbSewMagnificationY,KeyEmbSewReductionY
  //カメラ画像表示ヘルプ画面
  BPIF_SEND_EMBSEW_KEYNEEDLEVIEWCHANGEHELP,//KeyNeedleViewChangeHelp

  //キルトつなぎ拡大縮小
  BPIF_EMB_QUILT_SCALING_X_MAGNIFICATION,			// KeyEmbSewMagnificationX
  BPIF_EMB_QUILT_SCALING_X_REDUCTION,				// KeyEmbSewReductionX
  BPIF_EMB_QUILT_SCALING_Y_MAGNIFICATION,			// KeyEmbSewMagnificationY
  BPIF_EMB_QUILT_SCALING_Y_REDUCTION,				// KeyEmbSewReductionY

  //キルトつなぎ始点設定
  BPIF_EMB_QUILT_START_POINT_SET_START,			// KeyEmbSewingStartPointQuilt_Center
  BPIF_EMB_QUILT_START_POINT_SET_END,				// KeyEmbSewingStartPointQuilt_Center
  BPIF_EMB_QUILT_START_POINT_SET_UP_LEFT,			// KeyEmbSewingStartPointQuilt_LeftUp
  BPIF_EMB_QUILT_START_POINT_SET_UP,				// KeyEmbSewingStartPointQuilt_Up
  BPIF_EMB_QUILT_START_POINT_SET_UP_RIGHT,		// KeyEmbSewingStartPointQuilt_RightUp
  BPIF_EMB_QUILT_START_POINT_SET_LEFT,			// KeyEmbSewingStartPointQuilt_Left
  BPIF_EMB_QUILT_START_POINT_SET_RIGHT,			// KeyEmbSewingStartPointQuilt_Right
  BPIF_EMB_QUILT_START_POINT_SET_DOWN_LEFT,		// KeyEmbSewingStartPointQuilt_LeftDown
  BPIF_EMB_QUILT_START_POINT_SET_DOWN,			// KeyEmbSewingStartPointQuilt_Down
  BPIF_EMB_QUILT_START_POINT_SET_DOWN_RIGHT,		// KeyEmbSewingStartPointQuilt_RightDown
  BPIF_EMB_QUILT_START_POINT_SET_CORNER_ENTRANCE,	// KeyEmbSewingStartPointQuilt_LeftConer
  BPIF_EMB_QUILT_START_POINT_SET_CORNER_CENTER,	// KeyEmbSewingStartPointQuilt_Coner
  BPIF_EMB_QUILT_START_POINT_SET_CORNER_EXIT,		// KeyEmbSewingStartPointQuilt_DownConer

  //キルトつなぎパーツ選択
  BPIF_EMB_QUILT_SELECT_PARTS_NEXT,				// KeyNextMarkPatCnct
  BPIF_EMB_QUILT_SELECT_PARTS_PREVIOUS,			// KeyReturnMarkPatCnct

  //キルトつなぎ針落ち点再取得
  BPIF_EMB_QUILT_FRAME_POSITION_REACQUISITION,	// KeyEmbQuiltFramePositionReacquisition

  //縫製設定画面での回転
  BPIF_SEND_EMBSEW_KEYEMBALLROTATERESET,			//	KeyEmbAllRotateReset
  BPIF_SEND_EMBSEW_KEYEMBALLROTATE,			//	KeyEmbAllRotate

  //------------------------------- MDC-----------------------
  BPIF_MDC_KEY_MDC_MAKECANCEL,//MDC Paint→Emb Selectに遷移
  BPIF_MDC_KEY_MDC_MAKENEXT,//MDC Paint→MDC 縫製設定
  BPIF_MDC_KEYEMBMYDESIGNCENTERDETAILSETTINGRETURN,//MDC 縫製設定→MDC Paint
  BPIF_MDC_KEYEMBMYDESIGNCENTERDETAILSETTINGPREVIEW,//MDC 縫製設定→Emb Edit

  // 背景スキャン
  BPIF_MDC_KEYEMBMYDESIGNCENTERSCANSELECTGOTOFLAMEREC,

  //------------------------------- Setting-----------------------
  //LEDポインタ
  BPIF_SETTING_KEYGOTOLEDPTSETTING,
  BPIF_SETTING_KEYLEDPT_HEIGHT_MINUS,
  BPIF_SETTING_KEYLEDPT_HEIGHT_PLUS,
  BPIF_SETTING_KEYLEDPT_BRIGHTNESS_MINUS,
  BPIF_SETTING_KEYLEDPT_BRIGHTNESS_PLUS,
  BPIF_SETTING_KEYRETURNBYPOPFORSETTINGLEDPOINT,
  //Lamp
  BPIF_SETTING_KEYSETSETTINGSSETLAMP,
  //LCDの輝度
  BPIF_SETTING_KEYSETTINGSLCDBRIGHTNESS,

  //糸切れキャンセル
  BPIF_SETTING_KEYUDTHREADSENSORCANCEL,
  BPIF_SETTING_KEYUDTHREADSENSORNOTCANCEL,
  //針落ち点表示
  BPIF_CAMCALIB_KEYCALIBRATIONADJUSTMENT,
  BPIF_CAMCALIB_1ST_KEYCAMERACALIBRATIONFAST_START,
  BPIF_CAMCALIB_2ND_KEYCAMERACALIBRATIONOK,
  BPIF_CAMCALIB_2ND_KEYCAMERACALIBRATIONRETRY,
  BPIF_CAMCALIB_KEYCAMERACALIBRATIONRETURN,

  //Reset
  BPIF_SETTING_RESETUTLUSERSETTING,
  BPIF_SETTING_RESETCOMMONUSERSETTING,
  BPIF_SETTING_RESETEMBUSERSETTING,
  BPIF_SETTING_USERRESET,
  BPIF_SETTING_STARTALLUSERSETTING,
  BPIF_SETTING_FINISHALLUSERSETTING,
  //ユーザログ
  BPIF_SETTING_GETPRODUCTINFO,
  //DFの設定値
  BPIF_SETTING_KEYDFCORRECTPULSE,

  // 刺しゅうレジューム
  BPIF_EMBRESUMEDATAREAD,

  //MatrixEnableList操作
  BPIF_TEACHING_SETMATRIXENABLELIST,

  // 定義の数
  BPIF_MAX

}
enum ErrCode_t {
  ERR_DUMMY //	0	ダミー エラー番号０は、通常画面
  ,
  ERR_ANZENSOUTI //	1	安全装置が働きました。糸が絡んでいませんか？針が曲がっていませんか？
  ,
  ERR_UPPER_THREAD //	2	上糸が切れていないか確かめてください
  ,
  ERR_OSAE_LEVER_UP //	3	押えレバーを上げてください
  ,
  ERR_OSAE_LEVER_DOWN //	4	押えレバーを下げてください
  ,
  ERR_NO_MEMORY_CARD //	5	カードが入っていませんカードをいれてください
  ,
  ERR_CANNOT_USE_CARD //	6	この刺しゅうカードは使用できません
  ,
  ERR_NO_MORE_SELECT //	7	これ以上組み合わせできません
  ,
  ERR_EMBUNIT_NOT_ATT //	8	刺しゅう機がついていない時はこの釦は使えません
  ,
  ERR_EMBUNIT_IS_ATT //	9	刺しゅう機がついている時はこの釦は使えません
  ,
  ERR_EMBUNIT_IS_ATT2 //	10	刺しゅう機がついている時はこの釦は使えません 電源を入れなおしてください
  ,
  ERR_FOOT_CONTROLER_NOT_USE //	11	刺しゅう機がついている時はフットコントローラーは使えませんのではずしてください
  ,
  ERR_EMB_TOO_MUCH_SELECTED //	12	データ容量の制限を越えました、選べません(刺繍メモリフル)
  ,
  ERR_NEEDLE_UP //	13	針を上にあげてください
  ,
  ERR_BORDER //	14	Border その他エラー
  ,
  ERR_NOT_SS_BOTTON_BY_FOOT_CONTROLER //	15	フットコントローラーが付いているときはスタートストップボタンは使えません
  ,
  ERR_EMB_EDIT_END_YET //	16	編集模様を最後まで入力し、編集終了を押してください　刺繍編
  ,
  ERR_UTL_EDIT_END_YET //	17	編集模様を最後まで入力し、編集終了を押してください　実用編
  ,
  ERR_SELECT_STITCH //	18	模様を選んでください　実用
  ,
  ERR_EMB_PATTERN_EXCEEDED_FOR_SELECT_ARRAY //	19	模様が枠からはみだしました。前の配列に変更する（文字の配列ができません）
  ,
  ERR_EMB_PATTERN_EXCEEDED_FOR_SELECT_PATTERN //	20	模様が枠からはみだしました。選択した模様を削除する
  ,
  ERR_EMB_SELECT_PATTERN_ROTATE90 //	21	選択した模様を９０°回転し、模様選択を続行する
  ,
  ERR_SELECT_PATTERN //	22	模様を選んでください　刺繍
  ,
  ERR_NOW_MOMORYING_FLASH //	23	 記憶中 (内蔵メモリ)
  ,
  ERR_SD_NOW_WRITING //	24	記憶中 (SDカード)
  ,
  ERR_USB_HOST_NOW_WRITING //	25	記憶中 (USB(マウス無し))
  ,
  ERR_USB_HOST2_NOW_WRITING //	26	記憶中 (USB(マウスあり))
  ,
  ERR_USB_FUNCTION_NOW_WRITING //	27	記憶中 (PC)
  ,
  ERR_LOWER_THREAD_DECREASED //	28	下糸が少なくなってきました
  ,
  ERR_EMB_CARRY_MOVING //	29	刺繍キャリッジが動きます、手や物を離してください
  ,
  ERR_EMB_KIOKU_MEMORY_DELETE //	30	消去します（記憶データ）
  ,
  ERR_SELECTED_STITCH_CANCEL_OK //	31	模様の選択をキャンセルします 実用
  ,
  ERR_NOW_DATA_LOADING //	32	模様の呼び出し中です、しばらくお待ちください
  ,
  ERR_SELECTED_PATTERN_CANCEL_OK //	33	模様の選択をキャンセルします
  ,
  ERR_UTL_MEMORY_OK //	34	設定条件を記憶します。よろしいですか? 《キャンセル》 《OK》
  ,
  ERR_CHANGE_L_FRAME //	35	大枠に変更ください 《OK》
  ,
  ERR_CHANGE_L_OR_LM_FRAME //	36	大枠または、中枠2に変更ください 《OK》
  ,
  ERR_EMB_FRAME_OFF //	37	枠をはずしてください 《OK》
  ,
  ERR_EMB_FRAME_SET //	38	枠を付けてください 《OK》
  ,
  ERR_USE_FOOT_CONTROLER_WHEN_WIDTH //	39	WIDTH CONTLOR モードでは、スタート/ストップキーは使えません。フットコントローラを使用ください
  ,
  ERR_THREAD_ANZZENSOUTI //	40	糸巻きの安全装置が働きました。糸がからんでいませんか？
  ,
  ERR_TWIN_NEEDLE_NOT_SELECT_PATTERN //	41	2本針モードでは、この模様は選べません。2本針モードを解除して、選んでください。
  ,
  ERR_DATA_MOMORY_FULL //	42	容量不足で模様が記憶できません。模様を消去するか、FDを交換してください 《キャンセル》 《消去実行》
  ,
  ERR_FD_ERROR //	43	FDエラー 《OK》
  ,
  ERR_THREAD_HOLDER_LEFT //	44	糸巻きホルダーを左にしてください
  ,
  ERR_OSAE_LEVER_UP_NOCLOSE //	45	押えレバーを上げてください（Ｃｌｏｓｅなし）
/*** ↓ XP SNC連携 22032:直線針板装着時に、2本針モードがONになった場合のメッセージ修正　2021/4/13 H.Kawasaki ***/
  ,
  ERR_TWIN_NEEDLE_NOT_STRAIGHT_NEEDLE_PLATE //	46	2本針モードでは、直線針板を使用できません。2本針を取り外し、2本針モードを解除してください。
/*** ↑ XP SNC連携 22032:直線針板装着時に、2本針モードがONになった場合のメッセージ修正　2021/4/13 H.Kawasaki ***/
  ,
  ERR_NEEDLE_UP_EMB_NOCLOSE //	47	針上下スイッチで針を上にあげてください（Ｃｌｏｓｅなし）
  ,
  ERR_PATTERN_DELETE_OK_FD_FOR_SELECT_SCREEN //	48	選んだ模様が消えます。よろしいですか?（ＦＤ選択画面用） 《キャンセル》 《OK》
  ,
  ERR_PATTERN_DELETE_OK_FD_FOR_DELETE_SCREEN //	49	選んだ模様が消えます。よろしいですか?（ＦＤ消去画面用） 《キャンセル》 《OK》
  ,
  ERR_PATTERN_DELETE_OK_FLASH_FOR_SELECT_SCREEN //	50	選んだ模様が消えます。よろしいですか?（ＦＬＡＳＨ選択画面用） 《キャンセル》 《OK》
  ,
  ERR_PATTERN_DELETE_OK_FLASH_FOR_DELETE_SCREEN //	51	選んだ模様が消えます。よろしいですか?（ＦＬＡＳＨ消去画面用） 《キャンセル》 《OK》
  ,
  ERR_FLASH_MEMORY_FULL //	52	フラッシュ用。容量不足で模様が記憶できません。模様を消去して下さい 《キャンセル》 《OK》
  ,
  ERR_UTL_DELETE_OK_FOR_NORMAL //	53	「文字・模様」選んだ模様が消えます。よろしいですか?（ＦＬＡＳＨ選択画面用） 《キャンセル》 《OK》
  ,
  ERR_UTL_DELETE_OK_FOR_DELETE //	54	「文字・模様」選んだ模様が消えます。よろしいですか?（ＦＬＡＳＨ選択画面用） 《キャンセル》 《OK》
  ,
  ERR_SCAN_NORMAL_END //	55	スキャン通常終了
  ,
  ERR_NOW_DELETING //	56	消去中
  ,
  ERR_RED_SQUARE_OVER //	57	赤いラインがはみ出しているときは操作できません。
  ,
  ERR_NO_SAVE_PATTERN //	58	記憶できない模様が含まれています。ミシンの内臓メモリに記憶くださ。
  ,
  ERR_MYI_DELETE_OK_FOR_NORMAL //	59	「マイイラスト」選んだ模様が消えます。よろしいですか?（ＦＬＡＳＨ選択画面用） 《キャンセル》 《OK》
  ,
  ERR_MYI_DELETE_OK_FOR_DELETE //	60	「マイイラスト」選んだ模様が消えます。よろしいですか?（ＦＬＡＳＨ選択画面用） 《キャンセル》 《OK》
  ,
  ERR_FD_WRONG_FORMAT //	61	識別できるフォーマットではありません。《OK》
  ,
  ERR_EMB_PATTERN_EXCEEDED //	62	模様が枠からはみだしました。
  ,
  ERR_EMB_TOO_MUCH_SELECTED_GO_MENU //	63	データ容量の制限を越えました、選べません(刺繍メモリフル)
  ,
  ERR_EMB_FRAME_OFF_NOCLOSE //	64	枠をはずしてください（Ｃｌｏｓｅなし）
  ,
  ERR_NO_MORE_SELECT_FOR_EMB //	65	これ以上組み合わせできません（刺繍用）
  ,
  ERR_EMB_SELECT_PATTERN_ROTATE90_NOT_DELETE //	66	選択した模様を９０°回転し、模様選択を続行する キャンセルしても模様は消去しない
  ,
  ERR_CHANGE_L_OR_LM_OR_M_FRAME //	67	大枠または、中枠2にまたは中枠に変更ください 《OK》
  ,
  ERR_THIS_PATTARN_NOT_THIS_FRAME //	68	この模様は専用枠に変えてください 《OK》
  ,
  ERR_EMB_THIS_PATTERN_NOT_USE //	69	この模様はつかえません
  ,
  ERR_CUSTOM_THREAD_CHANGE_OK //	70	カスタムスレッドを書き換えます。よろしいですか？
  ,
  ERR_CUSTOM_THREAD_ALL_DELETE_OK //	71	カスタムスレッドが全部消えます。よろしいですか？
  ,
  ERR_COLOR_CHANGE_BACK_OK //	72	色の変更が元に戻ります。よろしいですか？
  ,
  ERR_EEPROM_DATA_CHANGE_OK //	73	データを書き換えます。よろしいですか？ＥＥＰＲＯＭデータの書き換え
  ,
  ERR_RESUME_OK //	74	レジューム記憶を呼び出しますか？
  ,
  ERR_SR_READY_TIMEOUT //	75	SR 待機中のタイムアウト
  ,
  ERR_SR_CONNECTED //	76	SR画面に遷移しますか？
  ,
  ERR_SR_DISCONNECTED //	77	SR画面を抜けますか？
  ,
  ERR_EMBUNIT_SET //	78	刺しゅう機がついていないので縫えません。電源スイッチを切ってから刺しゅう機を取り付けてください。
  ,
  ERR_USB_MEDIA_SET //	79	ＵＳＢメディアが入っていません。ＵＳＢメディアを入れてください。
  ,
  ERR_SD_MEDIA_SET //	80	SDカードが入っていません。SDカードを入れてください。
  ,
  ERR_CHANGE_EXCEPT_S_FRAME //	81	大枠に変更ください 《OK》
  ,
  ERR_USB_MEDIA_NOT_USE //	82	このＵＳＢメディアは使用できません。
  ,
  ERR_SD_MEDIA_NOT_USE //	83	このSDカードは使用できません。
  ,
  DMY_ERR_084 //	84	空き
  ,
  DMY_ERR_085 //	85	空き
  ,
  DMY_ERR_086 //	86	空き
  ,
  ERR_USB_MEDIA_BROKEN //	87	ＵＳＢメディアのフォーマットができません。ＵＳＢメディアの種類が違うか、壊れている可能性があります。ＵＳＢメディアを交換してください。
  ,
  ERR_SD_MEDIA_BROKEN //	88	SDカードが壊れている可能性があります。SDカードを交換して、もう一度記憶してください。
  ,
  DMY_ERR_089 //	89	空き
  ,
  DMY_ERR_090 //	90	空き
  ,
  DMY_ERR_091 //	91	空き
  ,
  DMY_ERR_092 //	92	空き
  ,
  ERR_USB_MEDIA_CHANGED //	93	ＵＳＢメディアが交換されました。読み込んでいる最中に交換しないでください。
  ,
  ERR_SD_MEDIA_CHANGED //	94	SDカードが交換されました。読み込んでいる最中に交換しないでください。
  ,
  ERR_CMN_MEDIA_CHANGED //	95	CMN メディアが交換されました。読み込んでいる最中に交換しないでください。
  ,
  ERR_USB_MEDIA_CANNOT_WRITE_BY_PROTECTED //	96	ＵＳＢメディアがライトプロテクトされていて記憶できません。ライトプロテクトを解除して記憶してください。
  ,
  ERR_SD_MEDIA_CANNOT_WRITE_BY_PROTECTED //	97	SDカードがライトプロテクトされていて記憶できません。ライトプロテクトを解除して記憶してください。
  ,
  ERR_CMN_MEDIA_CANNOT_WRITE_BY_PROTECTED //	98	CMN メディアがライトプロテクトされていて記憶できません。ライトプロテクトを解除して記憶してください。
  ,
  ERR_USB_MEDIA_CANNOT_DELETE_BY_PROTECTED //	99	ＵＳＢメディアがライトプロテクトされていて消去できません。ライトプロテクトを解除して消去してください。
  ,
  ERR_SD_MEDIA_CANNOT_DELETE_BY_PROTECTED //	100	SDカードがライトプロテクトされていて消去できません。ライトプロテクトを解除して消去してください。
  ,
  ERR_CMN_MEDIA_CANNOT_DELETE_BY_PROTECTED //	101	CMN メディアがライトプロテクトされていて消去できません。ライトプロテクトを解除して消去してください。
  ,
  ERR_USB_MEDIA_ERROR //	102	ＵＳＢメディアエラー
  ,
  ERR_SD_MEDIA_ERROR //	103	SDカードエラー
  ,
  ERR_CMN_MEDIA_ERROR //	104	CMN メディアエラー
  ,
  ERR_USB_MEDIA_CANNOT_READ //	105	ＵＳＢメディアが読めません。ＵＳＢメディアが壊れている可能性があります。
  ,
  ERR_SD_MEDIA_CANNOT_READ //	106	SDカードが読めません。SDカードが壊れている可能性があります。
  ,
  ERR_CMN_MEDIA_CANNOT_READ //	107	CMN メディアが読めません。メディアが壊れている可能性があります。
  ,
  ERR_USB_MEDIA_NOW_FORMATTING //	108	ＵＳＢメディアのフォーマット中です。
  ,
  ERR_USB_NOW_COMMUNICATING //	109	ＵＳＢ通信中
  ,
  ERR_SD_NOW_COMMUNICATING //	110	SDカード通信中
  ,
  ERR_CMN_NOW_COMMUNICATING //	111	CMN メディア通信中
  ,
  ERR_PLEASE_WAIT //	112	しばらくお待ち下さい。
  ,
  ERR_BLUE_LINE_IS_OVER //	113	青い枠がはみ出しているときは操作できません。
  ,
  ERR_SEW_NEXT_PARTS_OK //	114	次のパーツを縫いますか？
  ,
  ERR_SEWING_OVER //	115	縫い終わりました。
  ,
  ERR_POCKET_FULL //	116	ポケットがいっぱいです。模様を消去してください。
  ,
  ERR_OSAE_UP //	117	押えスイッチで押えを上げください。
  ,
  ERR_OSAE_DOWN //	118	押えスイッチで押えを下げてください。
  ,
  ERR_THREAD_THROUGH_FAILED //	119	糸掛けがうまくできません。もう一度糸掛けスイッチを押してください。
  ,
  ERR_USB_UPGRADE_START //	120	プログラムのアップグレードを行います。ＵＳＢでプログラムをミシンに入れてください。
  ,
  ERR_USB_UPGRADE_FINISH //	121	アップグレードが終わりました。電源を切ってください。
  ,
  ERR_USB_UPGRADE_FILE_IS_ERROR //	122	正しいアップグレードファイルではありません。
  ,
  ERR_DATA_ERROR //	123	ＤＡＴＡエラー
  ,
  ERR_FLASH_ROM_ERROR //	124	ＦＬＡＳＨ ＲＯＭエラー
  ,
  ERR_TROUBLE_OCCORED_POWER_OFF //	125	不具合が生じました。電源をいったんＯＦＦしてから再度やり直してください。
  ,
  ERR_CHANGE_NEEDLE_BOARD //	126	針板を交換してください。
  ,
  ERR_EMB_FLAME_RETURN_OK //	127	刺しゅう枠を元の位置に戻します。よろしいですか？
  ,
  ERR_ALL_DELETE_BORDER_OK //	128	ボーダー模様の組み合わせが解除されます。よろしいですか？  ※全てのボーダー模様 解除用
  ,
  ERR_THIS_USB_MEDIA_NOT_USE //	129	このＵＳＢメディアには対応していません。
  ,
  ERR_THIS_SD_MEDIA_NOT_USE //	130	このSDカードには対応していません。
  ,
  ERR_THIS_CMN_MEDIA_NOT_USE //	131	このメディアには対応していません。
  ,
  ERR_USB_LINE_BROKEN //	132	ＵＳＢメディア/コネクターが引き抜かれました。
  ,
  ERR_TROUBLE_OCCORED_POWER_OFF2 //	133	不具合が生じました。電源をいったんＯＦＦしてから再度やり直してください。
  ,
  ERR_PATTERN_DELETE_OK_HOST_FOR_SELECT_SCREEN //	134	選んだ模様が消えます。よろしいですか?（ＨＯＳＴ選択画面用） 《キャンセル》 《OK》
  ,
  ERR_PATTERN_DELETE_OK_HOST_FOR_DELETE_SCREEN //	135	選んだ模様が消えます。よろしいですか?（ＨＯＳＴ消去画面用） 《キャンセル》 《OK》
  ,
  ERR_ALL_LOCK_OK //	136	ロックキー
  ,
  ERR_PATTERN_CANCEL_OK_BY_DELETE_KEYS //	137	選んだ模様が消えます。よろしいですか?
  ,
  ERR_RIGHT_BOBBIN_HOLDER //	138	ボビン押えを右に戻してください。
  ,
  ERR_PFT_MOVE //	139	押えが上下します。手を押えに近づけないでください。
  ,
  ERR_CHANGE_NEEDLE_BOARD_NOCLOSE //	140	針板を交換してください。
  ,
  ERR_CUR_DELETE_BORDER_OK //	141	ボーダー模様の組み合わせが解除されます。よろしいですか？  ※カレントボーダー模様 解除用(カレント以外は解除しない)
  ,
  ERR_FAIL_PM_INITIAL //	142	不具合が生じました。電源をいったんＯＦＦしてから再度やり直してください。
  ,
  ERR_EMB_CARRY_MOVING_NOT_OVER_WRITE //	143	刺繍キャリッジが動きます、手や物を離してください
  ,
  ERR_EMB_CARRY_MOVING_FRAME_MOVE //	144	刺繍キャリッジが動きます、手や物を離してください
  ,
  ERR_NEEDLE_UP_FRAME_MOVE //	145	針を上にあげてください
  ,
  ERR_BOTTON_ANAKAGARI_LEVER_UP_FRAME_MOVE //	146	ボタン穴かがりレバーを上げてください
  ,
  ERR_PFT_MOVE_FRAME_MOVE //	147	押えが上下します。手を押えに近づけないでください。
  ,
  ERR_EMB_FRAME_OFF_FRAME_MOVE //	148	枠をはずしてください 《OK》
  ,
  ERR_EMB_FRAME_HOLD_LEVER_DOWN //	149	刺繍枠固定レバーを下げてください。
  ,
  ERR_EMB_LOWER_THREAD_DECREASED //	150	下糸が少なくなってきました。（刺繍）
  ,
  ERR_EMB_PATTERN_EXCEEDED_FOR_SELECT_PATTERN_NOT_DELETE //	151	模様が枠からはみだしました。
  ,
  ERR_EMB_SELECT_PATTERN_ROTATE90_WITH_ALL_DELETE_CLOSE //	152	選択した模様を９０°回転し、模様選択を続行する
  ,
  ERR_FAIL_SAFE //	153	フェイルセーフ
  ,
  ERR_SELECTED_STITCH_CANCEL_OK_FOR_ADVICE //	154	選んだ模様が消えます。よろしいですか? 《キャンセル》 《OK》
  ,
  ERR_TWIN_NEEDLE_AUTO_THREAD_NOT_USED //	155	2本針モードでは、自動糸通しは使えません
  ,
  ERR_THREAD_RESET //	156	糸を掛けなおしてください
  ,
  DMY_ERR_157 //	157	空き
  ,
  ERR_EMB_POSITIONING_RESET //	158	移動や回転が元に戻りますがよろしいですか？
  ,
  ERR_EMB_POSITIONING_BORDER_RESET //	159	ボーダー模様の組み合わせが解除されます。よろしいですか？
  ,
  ERR_EMB_POSITIONING_SCAN_CANCEL_FRAME_MOVE //	160	刺繍キャリッジが動きます、手や物を離してください
  ,
  ERR_ANGOU_NOT_USE //	161	この模様はつかえません
  ,
  ERR_EMB_FRAME_HOLD_LEVER_DOWN_POWER_ON //	162	刺しゅうをするときは、枠固定レバーが倒れているのを確認してからスタート／ストップスイッチを押してください。
  ,
  ERR_THIS_FLAME_NOT_USE //	163	この刺しゅう枠は使えません
  ,
  ERR_THIS_PATTERN_MEMORY_OVER //	164	この模様は容量オーバーです
  ,
  ERR_MAINTENANCE //	165	メンテナンス時期になりましたのでミシンのメンテナンスをおすすめします
  ,
  ERR_NEEDLE_UP_THREADER_NOCLOSE //	166	針上下スイッチで針を上にあげてください（糸通し時、Ｃｌｏｓｅなし） THPM03-00-52
/* ↓ IIVO PHFIRMIIVO-1702 刺しゅう模様プロジェクタ投影の開始・終了・強制終了 2023/12/12 nakanota */
  ,
  ERR_EMB_PRJ_START_CARRY_MOVING //	167	プロジェクタ投影を開始します。刺しゅうキャリッジが動きます。刺しゅうキャリッジの近くから物を離し、手を近づけないようにしてください。T_ERR_Proj_Emb_012
  ,
  ERR_EMB_PRJ_START_NO_CAMERAUI_CARRY_MOVING //	168	刺しゅう枠が小さいため、プロジェクターによる値変更機能には対応していません。刺しゅう模様の投影のみおこないます。刺繍キャリッジが動きます、手や物を離してください T_ERR_Proj_Emb_001
  ,
  ERR_EMB_PRJ_START_NO_CAMERAUI //	169	刺しゅう枠が小さいため、プロジェクターによる値変更機能には対応していません。刺しゅう模様の投影のみおこないます。T_ERR_Proj_Emb_002
/* ↑ IIVO PHFIRMIIVO-1702 刺しゅう模様プロジェクタ投影の開始・終了・強制終了 2023/12/12 nakanota */
  ,
  ERR_EMB_POSITIONING_WARNING //	170	刺繍キャリッジが動きます、手や物を離してください
  ,
  ERR_RECOGNIZING //	171	認識中
  ,
  ERR_NO_POSITION_MARK //	172	位置あわせマークを認識する事が出来ませんでした
  ,
  ERR_REMOVE_POSITION_MARK //	173	位置あわせマークを取って下さい
  ,
  ERR_CHANGE_NEEDLE_BOARD2_NOCLOSE //	174	針板２を装着してください PLT204-01
  ,
  ERR_THIS_PICTURE_DELETE_OK //	175	この画像を消してもいいですか？
  ,
  ERR_IMG_FILE_SIZE //	176	ファイルサイズが大きすぎます
  ,
  ERR_IMG_FILE_FORMAT //	177	対応したフォーマットではありません
  ,
  ERR_HOME_RESUME_OK //	178	レジューム記憶を呼び出しますか？(ＨＯＭＥ画面用)
  ,
  ERR_MAINTENANCE_1000H //	179	メンテナンス時期になりましたのでミシンのメンテナンスをおすすめします
  ,
  ERR_POSITIONING_AREA_OVER //	180	模様がはみ出します（刺繍位置あわせ用）
  ,
  ERR_RECOGNIZING_SMALL //	181	認識中 ちび
  ,
  ERR_MAN_MEM_DELETE_OK //	182	消してもいいですか？（マニュアルメモリ削除確認）
  ,
  ERR_MAN_MEM_ALL_DELETE_OK //	183	消してもいいですか？（マニュアルメモリ全削除確認）
  ,
  ERR_EMB_SELECT_PATTERN_ROTATE90_F //	184	選択した模様を９０°回転し、模様選択を続行する キャンセル時にファイル選択をリセット
  ,
  ERR_THIS_KEY_CANNOT_USED //	185	このキーは今は使えません
  ,
  ERR_THUNIT_HIT_FRAME //	186	刺繍キャリッジが動きます (糸通しが刺繍枠にぶつかるとき) THPM07-00-04
/* ↓ IIVO PHFIRMIIVO-1702 刺しゅう模様プロジェクタ投影の開始・終了・強制終了 2023/12/12 nakanota */
  ,
  ERR_EMB_PROJ_FORCE_QUIT_FRAMEOFF_WAITING //	187	IIVO用　アプリにエラー状態通知するためのエラーコード追加 刺繍模様投影強制終了待ち
/* ↑ IIVO PHFIRMIIVO-1702 刺しゅう模様プロジェクタ投影の開始・終了・強制終了 2023/12/12 nakanota */
  ,
  CONNECTING_TO_PC_NOW //	188	PC接続中 * /X1V1.00M-866
  ,
  ERR_NEEDLE_PLATE_EMB_CARRY_INITIAL //	189	針板を装着してください(刺繍機イニシャル用) エラーの優先順位を上げため追加 X1V1.00M-1220
  ,
  ERR_NEEDLE_PLATE2_EMB_CARRY_INITIAL //	190	針板２を装着してください(刺繍機イニシャル用) エラーの優先順位を上げため追加 X1V1.00M-1220
  ,
  ERR_ALL_DELETE_BORDER_OK_FOR_POSITIONING //	191	ボーダー模様の組み合わせが解除されます。よろしいですか？（刺繍位置合わせ用） X1V1.10M-84
  ,
  ERR_EMB_TOO_MUCH_DATA_EXPAND_USB_HOST //	192	データ容量の制限を越えました、選べません(刺繍メモリフル)(ＵＳＢホストデータ展開時) X1V1.10M-131
  ,
  ERR_RECOGNIZING_SCAN //	193	認識中 スキャン
  ,
  ERR_THIS_PATTERN_CANNOT_CONVERTED //	194	この模様を変換する事は出来ません
  ,
  ERR_RESET_EMB_SIZE_AND_POSITION //	195	模様の大きさと位置がリセットされます。良いですか
  ,
  ERR_RESET_EMB_SIZE_POSITION //	196	模様の大きさがリセットされます。良いですか
  ,
  ERR_DETECT_FABRIC_THICKNESS //	197	布厚を検出します。赤い線の中に位置あわせマークを貼ってください X1V200 SCAN-02-02 toukurma
  ,
  ERR_DETECTION_SUCCESS //	198	布厚検出に成功しました。位置あわせマークを剥がして下さい X1V200 SCAN-02-05 toukurma
  ,
  ERR_OK_MOVE_AND_START_CAPTURE //	199	OKキーを押すと枠が動きスキャン動作を開始します X1V200 SCAN-01-02 toukurma
  ,
  ERR_DETECTION_FAILURE_AGAIN //	200	布厚検出に失敗しました。もう一度やり直しますか？ X1V200 SCAN-02-06 toukurma
  ,
  ERR_CERTIFICATION_SUCCESS //	201	認証に成功しました。ミシンを再起動して下さい
  ,
  ERR_PRESS_CERTIFICATION_KEY //	202	キットを購入された方で認証する場合は認証キーを押して下さい
  ,
  ERR_CERTIFICATE_UPGRADE_KIT_START //	203	キットの認証を行います。認証後、ミシンの再起動が必要です
  ,
  ERR_ENTER_CERTIFICATION_KEY_AND_PRESS_SET //	204	認証キーを入力してからSETキーを押して下さい
  ,
  ERR_NOW_CERTIFICATING //	205	認証中
  ,
  ERR_CERTIFICATION_KEY_INCORRECT //	206	認証キーが正しくありません。確認して再度入力してください
  ,
  ERR_REMOVWE_COMP_FRAME_SCAN_ORG_IMG_DATA //	207	NANDに記憶した合成画像ファイルの削除
  ,
  ERR_FAILED_TO_SAVE_FILE //	208	 Failed to save file X1V200 CAP-01-13/14 Naka
  ,
  ERR_SEWING_OVER_MARK_PAT_CNCT //	209	縫製終了。OK後ミシンが動きます。模様つなぎ用 X2V300 PTCN-2-1 toukurma
  ,
  ERR_SET_BEFORE_1ST_MARK_PAT_CNCT //	210	マークを赤い線の中に貼ってください。模様つなぎ布張替え前の１つ目のマーク用 X2V300 PTCN-2-3 toukurma
  ,
  ERR_RECOGNIZING_MARK_PAT_CNCT //	211	認識中 模様つなぎ用* * X2V300 PTCN-2-5 toukurma
  ,
  ERR_NO_MARK_BEFORE_MARK_PAT_CNCT //	212	マークを見つけることが出来ませんでした。模様つなぎの布張替え前用* * X2V300 PTCN-2-6 toukurma
  ,
  ERR_MISS_MARK_BEFORE_MARK_PAT_CNCT //	213	マークの貼り間違い。模様つなぎの布張替え前用* * X2V300 PTCN-2-7 toukurma
  ,
  ERR_SET_BEFORE_2ND_MARK_PAT_CNCT //	214	マークを赤い線の中に貼ってください。模様つなぎ布張替え前の2つ目のマーク用 * * X2V300 PTCN-2-8 toukurma
  ,
  ERR_SUCCESS_BEFORE_MARK_PAT_CNCT //	215	マークを認識しました。模様つなぎの布張替え前のマーク認識成功* * X2V300 PTCN-2-13 toukurma
  ,
  ERR_ALL_DELETE_BORDER_OK_MARK_PAT_CNCT //	216	ボーダー模様の組み合わせが解除されます。よろしいですか？ 模様つなぎ用* * X2V300 PTCN-3-2 toukurma
  ,
  ERR_NO_MARK_AFTER_MARK_PAT_CNCT //	217	マークを見つけることが出来ませんでした。模様つなぎの布張替え後用* * X2V300 PTCN-3-12 toukurma
  ,
  ERR_POSITIONING_AREA_OVER_MARK_PAT_CNCT //	218	模様がはみ出します 模様つなぎ用* * X2V300 PTCN-3-13 toukurma
  ,
  ERR_NEXT_PATTERN_SEL_MARK_PAT_CNCT //	219	次の模様を選択してください。模様つなぎ用 X2V310 PTCN2-2-5 toukurma
  ,
  ERR_REMOVE_MARK_PAT_CNCT //	220	位置あわせマークを取って下さい 模様つなぎ用* * X2V300 PTCN-3-15 toukurma
  ,
  ERR_MARK_RESET_MARK_PAT_CNCT //	221	マークの貼り直し処理に進みます。* * X2V310 PTCN2-5-1 toukurma
  ,
  ERR_REMOVE_MARK_PAT_CNCT_RESET //	222	位置あわせマークを取って下さい 模様つなぎ貼り直し用* * X2V300 PTCN toukurma
  ,
  ERR_EMB_FRAME_MOVE_MARK_PAT_CNCT //	223	刺繍キャリッジが動きます、手や物を離してください 模様つなぎ用X2V310 PTCN2-2-16 toukurma
  ,
  ERR_RESET_NEW_1ST_MARK_PAT_CNCT //	224	マークを赤い線の中に貼ってください。模様つなぎ貼り直し１つ目のマーク用 * * X2V310 PTCN2 toukurma
  ,
  ERR_RESET_NEW_2ND_MARK_PAT_CNCT //	225	マークを赤い線の中に貼ってください。模様つなぎ貼り直し２つ目のマーク用 * * X2V310 PTCN2 toukurma
  ,
  ERR_CANCEL_MARK_PAT_CNCT //	226	つなぎキャンセル確認
  ,
  ERR_SUCCESS_RESET_NEW_MARK_PAT_CNCT //	227	刺繍位置合わせマークの位置が変更されました。* * X2V310 PTCN2 toukurma
  ,
  ERR_THIS_PATTERN_TOO_COMPLEX //	228	このデータは複雑すぎて変換できません X2V300 MCD toukurma
  ,
  ERR_MCD_CANCEL_B //	229	MCD CANCEL ブラザー XVV100 MCD toukurma
  ,
  ERR_MCD_HOME_B //	230	MCD HOME ブラザー XVV100 MCD toukurma
  ,
  DMY_ERR_231 //	231	空き
  ,
  DMY_ERR_232 //	232	空き
  ,
  ERR_PATTERN_DELETE_OK_HOST_FOR_SELECT_SCREEN_MCD //	【未使用】233	選んだ模様が消えます。よろしいですか?（ＨＯＳＴ選択画面用） X2V300 MCD toukurma
  ,
  ERR_PATTERN_DELETE_OK_HOST_FOR_DELETE_SCREEN_MCD //	【未使用】234	選んだ模様が消えます。よろしいですか?（ＨＯＳＴ消去画面用） X2V300 MCD toukurma
  ,
  ERR_MCD_NEW //	235	MCD NEW XVV100 MCD toukurma
  ,
  ERR_EMB_ALL_DELETE_CUSTOM_PALETTE_OK //	236	不要　　カスタムパレットテーブル情報が消えます。よろしいですか？ X2V300 SHUFFLE CUSTOM-1-2 makiry
  ,
  ERR_EMB_NO_AVAILABLE_COLOR_FOR_SHUF_IN_CUSTOM //	237	選択されたモード用に利用できる色がカスタムスレッドテーブルにありません X2V300 SHUFFLE SHUFFLE-2-9 makiry
  ,
  ERR_EMB_NO_AVAILABLE_COLOR_FOR_SHUF_IN_ORG //	238	選択されたモード用に利用できる色がカスタムスレッドテーブルにありません X2V300 SHUFFLE SHUFFLE-2-9 makiry
  ,
  ERR_USB_MEDIA_MEMORY_FULL_MEMORY_CHANGE //	239	容量がたりません。ＵＳＢメディアを交換してください。 X2V300 toukurma
  ,
  ERR_SD_MEDIA_MEMORY_FULL_MEMORY_CHANGE //	240	SD容量がたりません。SDカードを交換してください。
  ,
  ERR_CMN_MEDIA_MEMORY_FULL_MEMORY_CHANGE //	241	CMN 容量がたりません。メディアを交換してください。
  ,
  ERR_NO_MARK_RESET_OLD_MARK_PAT_CNCT //	242	マークを見つけることが出来ませんでした。貼り直し前のマーク探索用 X2V310 PTCN2-5-3 toukurma
  ,
  ERR_CAMERA_SET_WHEN_NEEDLE_CHANGE //	243	針交換時はカメラ針位置セットを行うことを推奨します
  ,
  ERR_SEWING_OVER_MARK_PAT_CNCT_RESUME //	244	縫製終了。OK後ミシンが動きます。模様つなぎレジューム用 X2V310 M-25 hirata
  ,
  ERR_EMBDATA //	245	模様のデータが読めません。データが壊れている可能性があります。電源を入れ直して下さい。VcomboL V100 M-4
  ,
  ERR_VIDEO_NOT_USED //	246	ビデオ機能無効
/*** XP3　DecorativeFillインポート機能-1-8,9,12、3-5、5-1,2,3、7-1,2,4　2021/12/28　H.Kawasaki ***/
  ,
  ERR_NOT_ENOUGH_AVAILABLE_MEMORY_TO_SAVE //	247	容量が不足しているので、保存できません。
  ,
  ERR_MDC_IMPORT_CUSTOM_PATTERN_DELETE_FAILURE //	248	(MDCカスタム模様の) 削除に失敗しました。
  ,
  ERR_MDC_IMPORT_CUSTOM_PATTERN_ALL_DELETE_OK //	249	カスタム模様をすべて削除すると、編集に使われているその模様が別の模様に変わります。すべてのカスタム模様を削除してよろしいですか？ ※カスタム模様・・デコフィル模様またはモチーフ模様
  ,
  ERR_MDC_IMPORT_CUSTOM_PATTERN_CHANGE_AFTER_SAVING //	250	データ保存後にインポートしたカスタム模様の削除・変更を行うと、次回読み込み時にデータがオリジナルから変化します。
  ,
  ERR_MDC_IMPORT_CUSTOM_PATTERN_NOT_SAVED_EXT_MEMORY //	251	インポートしたカスタム模様を含むデータは外部メモリに保存できません。 ※データ・・・お絵描きデータ
  ,
  ERR_MDC_IMPORT_CUSTOM_PATTERN_EMB_DATA_SAVED //	252	刺繍データのみ保存します。カスタム模様を含む編集データは外部メモリに保存できません。内部メモリに保存してください。
  ,
  ERR_MDC_IMPORT_CUSTOM_PATTERN_CHOOSE_REPLACE_DATA //	253	データ記憶領域がいっぱいです。カスタム模様を１つ選択して、新しい模様に入れ換えてください。
  ,
  ERR_MDC_IMPORT_CUSTOM_PATTERN_REPLACE_OK //	254	カスタム模様を入れ換えると、入れ換え前の模様を使用したデータでも連動して模様が変化します。本当に入れ替えますか？
/*** XP3　DecorativeFillインポート機能-1-8,9,12、3-5、5-1,2,3、7-1,2,4　2021/12/28　H.Kawasaki ***/
  ,
  ERR_EMB_PRJ_START_PLEASE_WAIT //	255	プロジェクタ起動中です。しばらくお待ちください。T_ERR_Proj_Emb_10
  ,
  ERR_EMB_PRJ_CLOSE_PLEASE_WAIT //	256	プロジェクタ終了中です。しばらくお待ちください。T_ERR_Proj_Emb_11
  ,
  ERR_EMB_PRJ_CLOSE_CARRY_MOVING //	257	プロジェクタ投影を終了します。* OKキーを押すと刺しゅうキャリッジが動きます。T_ERR_Proj_Emb_006
  ,
  ERR_EMB_PRJ_START_NO_CARRY_MOVING //	258	プロジェクタ投影を開始します。T_ERR_Proj_Emb_013
  ,
  ERR_EMB_ULTR_SNIC_POS_CHANGE_FRAME //	259	刺繍枠が変更されました 超音波刺繍位置合わせ Vpro_SENSOR EPOS toukurma
  ,
  ERR_UTL_THIS_PATTERN_NOT_SEW_WHEN_USE_DF //	260	の模様はデュアルフィードでは縫えません VMain VER100 DFPM-2-5 2012.04.13 HAL
  ,
  ERR_UTL_REMOVE_FABRIC_FROM_PF //	261	布を取り除いてから、糸通ししてください。
  ,
  ERR_EMB_PRJ_CLOSE_NO_CARRY_MOVING //	262	プロジェクタ投影を終了します。T_ERR_Proj_Emb_003
  ,
  ERR_SET_RTC //	263	時刻を設定してください(仮)
  ,
  ERR_IIC_COMMUNICATION //	264	IIC通信に不具合が生じました。電源をいったんＯＦＦしてから再度やり直してください。VMain VER100 IIC-4-7 HAL
  ,
  ERR_EMB_PROJECTOR_CLOSE_NOFRAME //	265	刺しゅう枠が外されたので、プロジェクタ投影を終了します。T_ERR_Proj_Emb_005
  ,
  ERR_EMB_PROJECTOR_CLOSE_NOFRAME_EMB_CARRY_MOVING //	266	刺しゅう枠が外されたので、プロジェクタ投影を終了します。* OKキーを押すと刺しゅうキャリッジが動きます。T_ERR_Proj_Emb_004
  ,
  DMY_ERR_267 //	267	空き
  ,
  ERR_LEDPT_CONNECT_WHEN_UTL //	268	LedPointerを外してください。(実用縫製時は使用不可) VMain VER100 LED_PT_REMOVE_UTL-1-1 2012/06/14 HAL
  ,
  ERR_DF_CONNECT //	269	DFを外してください。 VMain VER100 DF_REMOVE_EMB-1-1 2012/06/14 HAL
/***↓ XP UGK1　10411 MDC Resume-1-3　2019/02/12　H.Kawasaki ***/
  ,
  ERR_MDC_RESUME_OK_B //	270	MyDesignCenter レジュームするかの確認(ブラザー)
  ,
  ERR_MDC_RESUME_OK_T //	271	MyDesignCenter レジュームするかの確認(タコニー)
/***↑ XP UGK1　10411 MDC Resume-1-3　2019/02/12　H.Kawasaki ***/
  ,
  ERR_OSAE_UP_EMB //	272	押えスイッチで押えを上げください。 EMB用
  ,
  ERR_OSAE_DOWN_EMB //	273	押えスイッチで押えを下げてください。 EMB用
  ,
  ERR_EMB_FRAME_HOLD_LEVER_DOWN_ULTR_SNIC //	274	刺繍枠固定レバーを下げてください。刺繍位置合わせ中にレバーを上げられた場合 ver1.04 M14 Makir
  ,
  DMY_ERR_275 //	275	空き
  ,
  ERR_SET_SETTINGS_DEFAULT //	276	設定項目をデフォルトにする時のエラー XV Setting_Default-1-1 2014/05/08 Nagai
  ,
  ERR_EMB_UNIT_NOT_USE //	277	この刺繍機は使えません XV EmbFrame_Panel-3-3 2014/06/17 Nagai
  ,
  ERR_MCD_FINISH_CHECK_B //	278	MCD 終了確認 ブラザー
  ,
  ERR_MYI_DECO_CANCEL_PTN_OK //	279	模様の選択をキャンセルします。よろしいですか？
  ,
  ERR_MYI_DECO_SELPTN_AREA_OVER //	280	エリアをはみ出すのでその模様は読み込めません。
  ,
  ERR_SCAN_FRAME_NOT_USE //	281	スキャン枠は使えません
  ,
  ERR_IMG_PIXEL_OVER //	282	画素数が多すぎます
  ,
  ERR_MYI_CANNOT_INPUT //	283	これ以上入力できません。
  ,
  ERR_ATTATCH_SCAN_FRAME //	284	読み込む画像をセットした枠をミシンに取り付けてください。
  ,
  ERR_MCD_SCAN_IMG_TRIMMING //	285	表示されているエリアが変換できます
  ,
  ERR_NO_USE_FUNCTION_CNCT //	286	つなぎモード中は、この機能は使用できません
  ,
  ERR_MCD_NOT_EXCHANGE_AREA_OVER //	287	最大縫製エリアからはみ出るため、変換できませんでした。
  ,
  ERR_MAIN_BOARD_POWER_OFF //	288	メイン基板電源OFF XV V100 M-477 Nagai 2014/07/17
  ,
  ERR_MAIN_BOARD_POWER_OFF_NMI //	289	メイン基板電源OFF(NMI) XV V100 M-477 Nagai 2014/07/17
  ,
  ERR_SELECTED_PATTERN_DELETE_OK_POPUP //	290	選んだ模様が消えます。よろしいですか? 《キャンセル》 《OK》※No.29「ERR_SELECTED_PATTERN_DELETE_OK」と同じメッセージ。return時のMessageにERR_POPUP_RETURN_CANCELまたはERR_POPUP_RETURN_OKが返る 20140722 Aoki Add
  ,
  ERR_OUTSIDE_OF_EMB_FRM_NOADD //	291	枠をはみ出します。これ以上は入力できません。
  ,
  ERR_OUTSIDE_OF_EMB_FRM_NOUSE //	292	枠をはみ出します。この機能は使用できません。
  ,
  ERR_NO_CHG_FONT //	293	選択した書体にない文字があるため変更できません。
  ,
  ERR_MCD_CANCEL_T //	294	MCD CANCEL タコニー
  ,
  ERR_MCD_HOME_T //	295	MCD HOME タコニー
  ,
  ERR_MCD_FINISH_CHECK_T //	296	MCD 終了確認 タコニー
/***↓ XP UGK1　10411 MDC Resume-1-3　2019/02/12　H.Kawasaki ***/
  ,
  ERR_APPLIQUE_NG_EX_IN_OVERLAP //	297	内側アウトラインと外側のアウトラインのステッチが重なります。ステッチ幅を小さくしてください。
/***↑ XP UGK1　10411 MDC Resume-1-3　2019/02/12　H.Kawasaki ***/
  ,
  ERR_MDC_CREATE_NEW //	298	MyDesignCenter [All Clear] 描画を消しますか？ 旧MDCのID：ERR_MCD_NEW
  ,
  ERR_MDC_CANCEL_B //	299	MyDesignCenter [CANCEL] 刺繍選択に戻りますか？(ブラザー) 旧MDCのID：ERR_MCD_CANCEL_B
  ,
  ERR_MDC_CANCEL_T //	300	MyDesignCenter [CANCEL] 刺繍選択に戻りますか？(タコニー) 旧MDCのID：ERR_MCD_CANCEL_T
  ,
  ERR_MDC_HOME_B //	301	MyDesignCenter [HOME] HOMEに戻りますか？(ブラザー) 旧MDCのID：ERR_MCD_HOME_B
  ,
  ERR_MDC_HOME_T //	302	MyDesignCenter [HOME] HOMEに戻りますか？(タコニー) 旧MDCのID：ERR_MCD_HOME_T
  ,
  UNUSED_MESSAGE_303 //	303	2018.3.19 Aoki 無効化、XPでは不要なメッセージ
  ,
  UNUSED_MESSAGE_304 //	304	2018.3.19 Aoki 無効化、XPでは不要なメッセージ
  ,
  ERR_MDC_FINISH_CHECK_B //	305	MyDesignCenter [終了確認] Setしますか？(ブラザー) 旧MDCのID：ERR_MCD_FINISH_CHECK_B
  ,
  ERR_MDC_FINISH_CHECK_T //	306	MyDesignCenter [終了確認] Setしますか？(タコニー) 旧MDCのID：ERR_MCD_FINISH_CHECK_T
  ,
  ERR_MDC_NOT_EXCHANGE_AREA_OVER //	307	MyDesignCenter [刺繍変換]結果 最大縫製エリアからはみ出るため、変換できませんでした。 旧MDCのID：ERR_MCD_NOT_EXCHANGE_AREA_OVER
  ,
  ERR_MDC_TOO_MUCH_SELECTED_GO_MENU //	308	MyDesignCenter (MDCメモリエラー用)データ容量の制限を越えました、選べません(刺繍メモリフル) 旧MDCのID：ERR_EMB_TOO_MUCH_SELECTED_GO_MENU
  ,
  ERR_MDC_TOO_ALLOC_FAILURE //	309	MyDesignCenter (MDCメモリエラー用)データ容量の制限を越えました、選べません(刺繍メモリフル)
  ,
  UNUSED_MESSAGE_310 //	310	2018.3.19 Aoki 無効化、XPでは不要なメッセージ
  ,
  ERR_COL_SORT_NG //	311	カラーソート不可
  ,
  ERR_EMB_BW_SEWING_FINISH //	312	BW終了しました。
  ,
  ERR_EMB_CARRY_MOVING_FRAME_MOVE_BW //	313	刺繍キャリッジが動きます、手や物を離してください
  ,
  ERR_EMB_FLAME_RETURN_OK_BW //	314	刺しゅう枠を元の位置に戻します。よろしいですか？
  ,
  ERR_EMB_FRAME_SET_BW //	315	枠を付けてください
  ,
  ERR_THIS_FRAME_NOT_USE_BW //	316	この刺しゅう枠は使えません
  ,
  ERR_EMB_BW_SEWING_OVER //	317	BW終了しました。
  ,
  ERR_EMB_BW_THIS_PATTERN_NOT_USE //	318	他のカテゴリーの模様と組み合わせることができません
  ,
  ERR_APPLIQUE_NG_DISTANCE //	319	模様が離れすぎている
  ,
  ERR_APPLIQUE_NG_SIZE_OVER //	320	枠からはみ出す場合
  ,
  ERR_APPLIQUE_NG_COMPLEX //	321	複雑な形状または適さない形状のため、アップリケラインを生成できませんでした。アップリケ設定を変更するか、別の模様を選択してください。
  ,
  ERR_APPLIQUE_NG_MEM_OVER //	322	複雑な形状ではないが容量オーバー
  ,
  ERR_SELECTED_DATA_DELETE_OK_POPUP //	323	選んだデータが消えます。よろしいですか? 《キャンセル》 《OK》
  ,
  ERR_DELETE_BORDER_MARK_OK_GROUP //	324	糸印が消えますがよろしいですか？グループ化時用
  ,
  ERR_DELETE_BORDER_MARK_OK_UNGROUP //	325	糸印が消えますがよろしいですか？アングループ時用
  ,
  ERR_DELETE_BORDER_MARK_OK_WAPPEN //	326	糸印が消えますがよろしいですか？アップリケ遷移時用
/***↓ XP UGK1　XPV200M0053 刺繍模様追加のMDC内でのホームキー押下メッセージが不適切　2019/04/12　H.Kawasaki ***/
  ,
  ERR_MDC_HOME_CLEAR_ALL_EDITING_DATA_AND_MOVE //	327	MyDesignCenter [HOME] すべての模様を消去し、ホーム画面に移りますが、よろしいですか? 《キャンセル》 《OK》
/***↑ XP UGK1　XPV200M0053 刺繍模様追加のMDC内でのホームキー押下メッセージが不適切　2019/04/12　H.Kawasaki ***/
  ,
  ERR_MDC_BACKGROUND_DELETE //	328	OK to delete the background image?
  ,
  ERR_DELETE_BORDER_MARK_OK_EASYSTTIPLE //	329	糸印が消えますがよろしいですか？簡単ステップリング遷移時用
  ,
  ERR_MDC_NOT_CHANGE_SETTING_MEMORY_OVER //	330	設定が変更できませんでした。縫い方設定の記憶領域が不足しています。
  ,
  ERR_THIS_PATTERN_TOO_COMPLEX_FOR_ILLUST_SCAN //	331	データが複雑なため、変換できませんでした。 イラストスキャン メモリエラー修正 PR1000II Ver110 M-59 imaizuka
  ,
  ERR_CAMERA_BH_NG //	332	製品同梱のA＋押えを取り付けてください。
  ,
  ERR_EMB_STAMP_MAKE //	333	My Design Center"のスタンプ模様リストから呼び出せます。
  ,
  ERR_EMB_STAMP_MAKE_TC //	334	IQ Designerのスタンプ模様リストから呼び出せます。
  ,
  ERR_XY_ZPHASE //	335 刺しゅうキャリッジの近くから物を離し、手を近づけないようにしてください。/*** XP VER100　XYInitialMsg-1-2　2018/04/13　HAL ***/
  ,
  ERR_EMB_QUILT_SASHES_CONTINUE //  336 本体メモリーにデータを保存しました。続けて縫製しますか
  ,
  ERR_WAIT_COMPLETE_CGI //	337 CGI終わるまで待機
  ,
  ERR_MYI_DELETE_OK_FOR_SAVEDFILE_SELECT //	338	「マイイラスト」選んだ模様が消えます。よろしいですか?（保存ファイル選択時用） 《キャンセル》 《OK》	/* XPV110M0544 */
  ,
  ERR_EMB_QUILT_THIS_PATTERN_NOT_USE //	339	他のカテゴリーの模様と組み合わせることができません
  ,
  ERR_EMB_QUILT_SASHES_1ST_SETTING //	340	 キルトサッシ関連：最初の模様の位置を促す。
  ,
  ERR_EMB_QUILT_SASHES_CORNER_SETTING //	341	 キルトサッシ関連：コーナーの模様の位置を促す。
  ,
  ERR_EMB_QUILT_SASHES_1ST_CORNER_OF_MOVE //	342	 キルトサッシ関連：最初のコーナー配置ガイダンス。Moveへ促す。①
  ,
  ERR_EMB_QUILT_SASHES_1ST_CORNER_OF_ROTATE //	343	 キルトサッシ関連：最初のコーナー方向決めガイダンス。Rotateへ促す。④
  ,
  ERR_EMB_QUILT_SASHES_STRAIGHT_OF_MOVE //	344	 キルトサッシ関連：端部品の配置ガイダンス。Moveへ促す。②
  ,
  ERR_EMB_QUILT_SASHES_MOVE_TO_ROTATE //	345	 キルトサッシ関連：Move設定後のRotateへ促す。⑤
  ,
  ERR_EMB_QUILT_SASHES_BEFORE_CORNER_OF_ROTATE //	346	 キルトサッシ関連：コーナー前方向決めガイダンス。Rotateへ促す。⑥
  ,
  ERR_EMB_QUILT_SASHES_CORNER_OF_MOVE //	347	 キルトサッシ関連：コーナー配置ガイダンス。Moveへ促す。③
  ,
  ERR_EMB_QUILT_SASHES_CORNER_OF_ROTATE //	348	 キルトサッシ関連：コーナー方向決めガイダンス。Rotateへ促す。⑧
  ,
  ERR_EMB_QUILT_SASHES_LAST_OF_ROTATE //	349	 キルトサッシ関連：最後の部品の配置ガイダンス。Rotateへ促す。⑦
  ,
  ERR_SCAN_SHADING_UPDATE //	350	 スキャン枠を使って背景スキャンをしてください。白と黒の帯を読み取ることで、補正値が更新され、スキャン画像の精度が向上します。（OK）
  ,
  ERR_EMB_QUILT_SASHES_REPOSITION_SETTING //	351	 キルトサッシ関連：模様の張替えを促す。
  ,
  ERR_EMB_QUILT_SASHES_1ST_SETTING2 //	352	 キルトサッシ関連：最初の模様の位置を促す。
  ,
  ERR_LEDPT_CONNECT_WHEN_COUTING //	353	 カウチング関連：LEDポインタを外してください。
  ,
  ERR_USB_DEVICE_CANNOT_USED //  354	USBケーブルを使ってComputerとの接続はできません
  ,
  ERR_EMB_COUTING_THIS_PATTERN_NOT_USE //	355	 他のカテゴリーの模様と組み合わせることができません
  ,
  ERR_BAD_EMBROIDERY_DATA //	356	この刺しゅうデータは糸色情報が不足しているため、糸色を近似して表示します。正しい糸色情報を表示するには、糸色変更画面で、糸色番号を入力してください。
  ,
  ERR_SCAN_SHADING_UPDATE_SCAN //	357	 補正値読取スキャン
  ,
  ERR_UPGRADE_FINISH_B //	358
  ,
  ERR_EMB_FLAME_RETURN_OK_BOBIN_CHANGE //	359  下糸を交換してください。刺しゅう枠を元の位置に戻します。よろしいですか？
  ,
  ERR_EULA_CONFIRMATION //	360 ご利用前にミシンに記載されているEULAを読んでください。
  ,
  ERR_PLEASE_WAIT_NETDIAG //  361 しばらくお待ちください（CANCELボタン付き）
  ,
  ERR_MANUAL_MEMORY_CORRECTION //  362 しばらくお待ちください(マニュアルメモリ消去)
  ,
  ERR_EPS_FINISH //  363 終点設定を終わりますよろしいですか？
  ,
  ERR_EPS_MARK_TOO_CLOSE //  364 シールが近すぎて終点設定が行えません
  ,
  ERR_EPS_STOP_MSG //  365 設定により一時停止しました
  ,
  ERR_EPS_CORRECT_OVER //  366 終点設定を終わります（規定よりも送り補正値がオーバーした場合）			/*** XPUGK2　EPS-7-12　2020/01/09　HAL ***/
  ,
  ERR_EPS_FINISH_PROJECTOR //  367 終点設定を終わりますよろしいですか？ OKの場合プロジェクター表示へ移動 	/*** XPUGK2　EPS-1-15　2020/01/30　HAL ***/
  ,
  ERR_EPS_CORRECT_OVER_SSKEY //  368 指定された距離では、模様長さ調整ができません。					/*** XPUGK2　EPS-7-13　2020/01/31　HAL ***/
  ,
  ERR_EMB_FRAME_HOLD_LEVER_DOWN_MATRIX //	369	刺繍枠固定レバーを下げてください。	モニタリングエラー用：Matrixからくる枠レバーエラー
/*** ↓ XP(ver3.50)M-032 アップリケプレビュー画面の仕様変更　2021/5/13 H.Kawasaki ***/
  ,
  ERR_APPLIQUE_SOME_PARTS_NOT_TEXTURE //  370 データ構造上、テクスチャを表示できません。
/*** ↑ XP(ver3.50)M-032 アップリケプレビュー画面の仕様変更　2021/5/13 H.Kawasaki ***/
  ,
  ERR_EMB_CHANGE_ORDER //	371 刺しゅう順が変更されます。
  ,
  ERR_MDC_BG_FILE_DELETE //	372 選択したデータファイルを削除します。よろしいですか？
  ,
  ERR_MDC_USB_MEDIA_CANNOT_DELETE_BY_PROTECTED //	373	ＵＳＢメディアがライトプロテクトされていて消去できません。ライトプロテクトを解除して消去してください。
  ,
  ERR_MDC_SD_MEDIA_CANNOT_DELETE_BY_PROTECTED //	374	SDカードがライトプロテクトされていて消去できません。ライトプロテクトを解除して消去してください。
  ,
  ERR_MDC_CMN_MEDIA_CANNOT_DELETE_BY_PROTECTED //	375	CMN メディアがライトプロテクトされていて消去できません。ライトプロテクトを解除して消去してください。
  ,
  ERR_CHECK_TWIN_NEEDLE_REMOVED //  376 2本針モードが解除されました。2本針が取り外されているか確認してください。  /*** XPver2.05 M-8　-1-2　2020/03/24　makiry ***/
  ,
  ERR_EMB_CHANGE_TO_SCAN_FRAME //  377 刺しゅう枠は使用できません。スキャン専用枠をご使用ください。
  ,
  ERR_EPS_LENGTH_SSKEY //  378 終点距離まで短すぎます。縫製開始位置をもっと終点から遠くするか直前停止をOFFにしてください
  ,
  ERR_EPS_DISABLE_KEY //  379 終点設定中は、このキーは使えません							/*** XPUGK2　EPS-1-26　2020/07/07　HAL ***/
  ,
  ERR_NET_DISABLE_WLAN //  380 ネットワークに接続されていません。無線LANの設定画面を表示しますか？
  ,
  ERR_NET_MAINTENANCE //  381 メンテナンスのためCanvasWorkspaceサービスが停止しており、本機能はご利用いただけません。復旧まで今しばらくお待ちください。
  ,
  ERR_REGIST_PINCODE_FAILURE //  382 CanvasWorkspaceにマシン登録できませんでした。<BR>最初から操作を行ってください
  ,
  ERR_REGIST_PINCODE_WRONG //  383 PINコードが不正です。もう一度入力してください。
  ,
  ERR_NET_NOT_CONNECT //  384 マシンがインターネットに接続されていません。
  ,
  ERR_NET_PROXY_WRONG //  385 サーバーへの接続に失敗しました。<BR>プロキシ等の設定を確認してください。"
  ,
  ERR_NET_TIMEOUT //  386 サーバーとの通信がタイムアウトしました。<BR>時間をおいて再試行してください。
  ,
  ERR_NET_ACCESSTOKEN_WRONG //  387 マシン登録が解除された可能性があります。CanvasWorkspaceで確認してください。
  ,
  ERR_UPLOAD_FAILURE //  388 "データのダウンロードに失敗しました。<BR>最初から操作を行ってください。"
  ,
  ERR_UPLOAD_CONFIRM //  389 サーバー上のテンポラリデータポケットにデータを送信しますが、よろしいですか？<BR>＊テンポラリデータポケットのデータは、一定期間後自動で削除されます。
  ,
  ERR_UPLOAD_OVERRIDE_CONFIRM //  390 テンポラリデータポケットには既にデータがありますが、新しいデータに置き換えてよろしいですか？<BR>＊テンポラリデータポケットのデータは、一定期間後自動で削除されます。
  ,
  ERR_DOWNLOAD_FAILURE //  391 ダウンロード中にエラーが発生しましたもう一度 最初から操作を行ってください
  ,
  ERR_DOWNLOAD_SIZE_CHANGE //  392 データを取り込み可能な大きさに縮小しました
  ,
  ERR_DOWNLOAD_UNREADABLE_DATA //  393 テンポラリーデータポケットには読み込み可能なデータがありません。
  ,
  ERR_UNREGIST_PINCODE //  394 CanvasWorkspace登録を解除してよろしいですか？
  ,
  ERR_UNREGIST_PINCODE_FAILURE //  395 CanvasWorkspaceからマシン登録を削除できませんでした。ウェブサイトから手動で登録解除してください。
  ,
  ERR_DOWNLOAD_DISABLE_PINCODE //  396 カットデータを受信するには、CanvasWorkspaceサーバーに、ScanNCut及びこのミシンのマシン登録が必要です。（PINコード登録）。<BR>登録用の設定画面を表示しますか？
  ,
  ERR_UPLOAD_DISABLE_PINCODE //  397 データを送信するには、CanvasWorkspaceサーバーに、このミシンの登録が必要です。登録用の設定画面を表示しますか？
  ,
  ERR_NET_SERVER_FAILURE //  398 マシン登録が解除された可能性があります。CanvasWorkspaceで確認してください。
  ,
  ERR_CANNOT_USE_SPECIAL_PATTERN //  399 この機能は選択されている特別な模様では使用できません。
  ,
  ERR_MARK_DELETE_OK_FOR_NUWANAI //  400 縫わない設定により糸印の設定が無効になります。よろしいですか？
  ,
  ERR_RECOMEND_TOMENUI //  401 ステッチの長さが長い模様です。縫いはじめに糸を抜けにくくするため、止め縫いキーで止め縫いしてから、縫製を開始してください。
  ,
  ERR_WLAN_SETUP_CONFIRMATION //	402 無線LAN接続を設定してください。
  ,
  ERR_CONFIRM_CANCEL_AUTOCNCT //  403 終了してもいいですか？（内蔵大型つなぎの自動つなぎ終了確認）
  ,
  ERR_SEWING_OVER_INTERNAL_LARGECONNECT //  404 続きを縫製する予定があれば、次の模様を呼び出してください。その後電源を切っても再開できます。
  ,
  ERR_SEWING_OVER_INTERNAL_LARGECONNECT_SOUND //  405 403	続きを縫製する予定があれば、次の模様を呼び出してください。その後電源を切っても再開できます。の音が違うバージョン
  ,
  ERR_RECOMMEND_UPDATE //	406	バージョンアップしてください
  ,
  ERR_VERUP_FOR_INSTALL_MOVIE //	407 動画をインストールするために再度アップデートをしてください
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_1 //	408	EdgeToEdgeキルトガイダンス1画面 先頭模様専用のガイダンス 左上に枠を設置
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_2 //	409	EdgeToEdgeキルトガイダンス2画面 先頭模様専用のガイダンス 位置合わせ指示 [マスク左上角]の位置をチャコ印を目安に移動キーで配置
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_3 //	410 EdgeToEdgeキルトガイダンス3画面 先頭模様専用のガイダンス 回転指示 [マスク右上角]の位置をチャコ印を目安に角度キーで配置　[マスク左上角]を基準に回転する
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_4 //	411	EdgeToEdgeキルトガイダンス4画面 1行目の2列目以降用のガイダンス 位置合わせ指示 前模様の[終点]に[始点]が合うように移動キーで配置
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_5 //	412	EdgeToEdgeキルトガイダンス5画面 1行目の2列目以降最終列以外用のガイダンス 回転指示 [マスク右上角]の位置をチャコ印を目安に角度キーで配置
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_6 //	413	EdgeToEdgeキルトガイダンス6画面 1行目の最終列模様のガイダンス 回転と拡縮指示
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_7 //	414	EdgeToEdgeキルトガイダンス7画面 2行目以降1列目模様のガイダンス 位置合わせ指示 上模様の[マスク左下角]位置に[マスク左上角]が合うように移動キーで配置
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_8 //	415	EdgeToEdgeキルトガイダンス8画面 2行目以降かつ最終列以外の1列目模様のガイダンス 回転指示と拡縮指示
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_10 //	416	EdgeToEdgeキルトガイダンス10画面 2行目以降2列目以降模様のガイダンス 位置合わせ指示 前模様の[終点]に[始点]が合うように移動キーで配置
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_11 //	417	EdgeToEdgeキルトガイダンス11画面 2行目以降かつ最終行以外かつ2列目以降最終列未満の模様のガイダンス 回転＆拡縮指示
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_12 //	418	EdgeToEdgeキルトガイダンス12画面 2行目以降かつ最終行以外かつ最終列の模様のガイダンス 回転＆拡縮指示
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_14 //	419	EdgeToEdgeキルトガイダンス14画面 1行目の2列目以降の模様用の布張替ガイダンス
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_15 //	420	EdgeToEdgeキルトガイダンス15画面 2行目以降の先頭の模様用の布張替ガイダンス
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_16 //	421	EdgeToEdgeキルトガイダンス16画面 2行目以降の2列目以降の模様の布張替ガイダンス
  ,
  ERR_REMOVE_MARK_PAT_CNCT_PATTERN_MAG //	422	位置あわせマークを取って下さい 模様つなぎ用 模様を拡縮
  ,
  ERR_AFTER_MARK_PAT_CNCT_MARK_DIV //	423	マーク間距離が布張替え前後で違った場合のエラー
  ,
  ERR_TAPERING_FINISH_PROJECTOR //  424 テーパリング設定を終わりますよろしいですか？ OKの場合プロジェクター表示へ移動
  ,
  ERR_TAPERING_FINISH //  425 テーパリング設定を終わりますよろしいですか？
  ,
  ERR_TAPERING_MARK_TOO_CLOSE //  426 シールが近すぎて終点設定が行えません
  ,
  ERR_TAPERING_CURRENT_FINISH_CUETOP //  427 現在のテーパリング縫製を終了しますよろしいですか？(頭出しキー押下)
  ,
  ERR_TAPERING_CURRENT_FINISH_FLIP_ON //  428 現在のテーパリング縫製を終了しますよろしいですか？(反転キー押下)
  ,
  ERR_TAPERING_CURRENT_FINISH_FLIP_OFF //  429 現在のテーパリング縫製を終了しますよろしいですか？(反転キー押下)
  ,
  ERR_TAPERING_CURRENT_FINISH_RETRIEVE //  430 現在のテーパリング縫製を終了しますよろしいですか？(リトリーブキー押下)
  ,
  ERR_TAPERING_CURRENT_FINISH_BUTTON //  431 現在のテーパリング縫製を終了しますよろしいですか？(終了方法：ボタンキー押下)
  ,
  ERR_TAPERING_CURRENT_FINISH_CYCLENUM //  432 現在のテーパリング縫製を終了しますよろしいですか？(終了方法：回数指定キー押下)
  ,
  ERR_TAPERING_CURRENT_FINISH_ENDPOINT //  433 現在のテーパリング縫製を終了しますよろしいですか？(終了方法：終点設定キー押下)
  ,
  ERR_TAPERING_CURRENT_FINISH_STARTANGLE //  434 現在のテーパリング縫製を終了しますよろしいですか？(終了角度キー押下)
  ,
  ERR_TAPERING_CURRENT_FINISH_ENDANGLE //  435 現在のテーパリング縫製を終了しますよろしいですか？(終了角度キー押下)
  ,
  ERR_TAPERING_CURRENT_FINISH_INCREASENUM //  436 現在のテーパリング縫製を終了しますよろしいですか？(終了角度キー押下)
  ,
  ERR_TAPERING_CURRENT_FINISH_DECREASENUM //  437 現在のテーパリング縫製を終了しますよろしいですか？(終了角度キー押下)
  ,
  ERR_TAPERING_CURRENT_FINISH //  438 現在のテーパリング縫製を終了しますよろしいですか？
  ,
  ERR_TAPERING_DISABLE_KEY //  439 テーパリング縫製中は、このキーは使えません
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_9 //	440	EdgeToEdgeキルトガイダンス9画面 最終行かつ1列目の模様用のガイダンス 回転、拡縮指示
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_13 //	441	EdgeToEdgeキルトガイダンス13画面 最終行かつ2列目以降最終列未満の模様のガイダンス 回転、拡縮指示
  ,
  ERR_EMB_EDGE_QUILT_SASHES_GUIDE_17 //	442	EdgeToEdgeキルトガイダンス17画面 最終行かつ最終列の模様のガイダンス 回転、拡縮指示
  ,
  ERR_DATA_MOMORY_FULL_DELETE_OTHER_DATA //	443 内蔵ポケットにしか保存できない時の容量上限による保存エラー
  ,
  ERR_MACHINENAME_ENABLE_WLAN //  444 マシン名を変更する場合は無線LANをONしてください。
  ,
  ERR_REGIST_MACHINE_NAME_FAILURE //  445 マシン名の変更ができませんでした。
  ,
  ERR_PATTERN_CANNOT_SAVE //  446 記憶できない模様が含まれています。
  ,
  ERR_DOWNLOAD_PAIDCONT_UPDATE //  447 このデータを使用するためには、ミシンのソフトウェアを最新のバージョンにアップデートする必要があります。
  ,
  ERR_WLAN_SETTING_IMPROPER //	448 WLAN電波設定が規程の値以外の時に表示するエラー	PHFIRMXP-2204 WLAN電波設定異常チェック makiry
  ,
  ERR_FONT_CNG_PF1_OK //  449 新しいフォントデータ形式に変換しますか？					PF1_1103
  ,
  ERR_FONT_CNG_NG_FORMAT //  450 古い文字データ形式なので編集に制限がかかります            PF1_1103
  ,
  ERR_APP //  451 アプリ側でエラー発生状態　操作ロック用
  ,
  ERR_SEWING_PATTERN_OVER //  452 1色分縫製完了
  ,
  ERR_DF_CONNECT_NOCLOSE //  453 DFを外してください。 ( Closeなし )
  ,
  ERR_REMOVE_SR_NOCLOSE //  454 SR押さえを外してください。 ( Closeなし )
  ,
  ERR_EMB_CARRY_MOVING_FRAME_REMOVE //  455 刺しゅうキャリッジが動きます。刺しゅうキャリッジの近くから物を離し、手を近づけないようにしてください。
  ,
  ERR_EMB_FRAME_REMOVE_RERUEN //  456 枠を元に戻すにはOKキーを押してください。
  ,
  ERR_APP_ACT_NON //  457 アプリ側でエラー発生状態　操作ロック用 ※ErrorAct：ERROR_ACT_NON
  ,
  ERR_APP_ACT_NON_WITH_PUSH //  458 アプリ側でエラー発生状態　操作ロック用 ※ErrorAct：ERROR_ACT_NON_WITH_PUSH
  ,
  ERR_APP_ALL_LOCK //  459 アプリ側でエラー発生状態　上書き、メカ操作すべてLOCK
  ,
  ERR_CODE_MAX //
}

typedef StateCallback = ffi.Void Function(ffi.Pointer<ffi.Void>);
typedef ErrorCallback = ffi.Void Function(ffi.Pointer<ffi.Void>);
typedef BeepCallback = ffi.Void Function(ffi.Int8);

final class BPIFPacket_t extends ffi.Struct {
  @ffi.Int()
  external int keyId;

  @ffi.Array.multi([256])
  external ffi.Array<ffi.UnsignedChar> data;
}

abstract class BPIFRcvKey {
  static const int BR_RCV_KEY_NOTIFY = 1;
  static const int BR_RCV_KEY_ERROR = 2;

  /// ステータス通知
  static const int BR_RCV_KEY_STATE = 3;

  /// キー操作を反映
  static const int BR_RCV_KEY_UPDATED = 4;

  /// 画面遷移完了
  static const int BR_RCV_KEY_WAIT_FINISH = 5;
}

/// テストモード共通のグローバル共有メモリ TestModeSharedData のダミー構造体
final class BPIFTestModeDummy_t extends ffi.Struct {
  @ffi.Uint8()
  external int mode;

  @ffi.Uint8()
  external int state;

  @ffi.Uint8()
  external int param1;

  @ffi.Uint8()
  external int param2;

  @ffi.Array(256)
  external Array<Char> file;
}

// TestMode menu 共有メモリ 構造体
final class BPIFTestMode00Param_t extends ffi.Struct {
  //  MotorSwPort_tのメンバからメカキー状態を通知
  //Lockキー
  @ffi.Bool()
  external bool Lock;

  //SSキー
  @ffi.Bool()
  external bool Start;

  //REVキー
  @ffi.Bool()
  external bool Reverse;

  //NPキー
  @ffi.Bool()
  external bool Np;

  //TCキー
  @ffi.Bool()
  external bool ThreadCut;

  //THキー
  @ffi.Bool()
  external bool AutoThreadSet;

  //PFキー
  @ffi.Bool()
  external bool AutoPf;

  //LedPointer接続状態
  @ffi.Bool()
  external bool LedPointer;

  //メニューロック状態（false：画面遷移不可）：TestMode項目の数だけ遷移判定を通知：ホーム画面+項目数(1 + 72)
  @ffi.Array.multi([73])
  external ffi.Array<ffi.Bool> isTestModeSelectValid;
}

/// TestMode 01 共有メモリ 構造体
final class BPIFTestMode01Param_t extends ffi.Struct {
  // serial number ※9桁 (バッファに余裕を持たせている)
  @ffi.Array(12)
  external Array<Char> serialNumber;

  // serial number置き換えの有無 : 0=なし, 1=あり(おかしなコードがあり'?'に置き換えた)
  @ffi.Uint8()
  external int isReplacedSerialNumber;

  // PRODUCT ID ※10桁 (バッファに余裕を持たせている)
  @ffi.Array(12)
  external Array<Uint8> productId;

  @ffi.Int32()
  external int panelMajorVer;

  @ffi.Int32()
  external int panelMinorVer;

  @ffi.Int32()
  external int panelPatchVer;

  @ffi.Int32()
  external int libMajorVer;

  @ffi.Int32()
  external int libMinorVer;

  @ffi.Int32()
  external int libPatchVer;

  // メイン プログラム Ver.
  @ffi.Uint16()
  external int mainVersion;

  // BOOT プログラム Ver.
  @ffi.Uint16()
  external int bootVersion;

  // マスター Ver.
  @ffi.Uint16()
  external int masterVersion;

  // FPGA Ver.
  @ffi.Uint16()
  external int fpgaVersion;

  // XY_CPU Ver.
  @ffi.Uint16()
  external int xyCpuVersion;

  // BOARD TYPE : 0=TYPE-A, 1=TYPE-B, 2=TYPE-C, 3=TYPE-D ※brother_panel側 SOC_TYPE_Eを参照
  @ffi.Uint8()
  external int boardType;

  ///ADBが有効かどうか
  @ffi.Bool()
  external bool isEnabledADB;

  /// Array<Uint8> productId(10桁) to String
  /// e.g. [4,7,9,1,7,1,0,6,3,1] to "4791710631"
  String productIdToString() {
    final stringList = <int>[];
    var i = 0;
    for (i = 0; i < 10; i++) {
      stringList.add(this.productId[i] + 0x30);
    }
    return String.fromCharCodes(stringList);
  }

  /// Uint8 boardType to String
  String boardTypeToString() {
    switch (this.boardType) {
      case 0:
        return "TYPE-A";
      case 1:
        return "TYPE-B";
      case 2:
        return "TYPE-C";
      case 3:
        return "TYPE-D";
      default:
        return "Unknown";
    }
  }
}

/// TestMode 02 公開定義
/// TestModeFabricThickness
final class BPIFTestMode02Param_t extends ffi.Struct {
  @ffi.Double()
  external double pftPosition;

  @ffi.Int32()
  external int pFOffset;

  @ffi.Bool()
  external bool isValidMainPfPos;

  @ffi.Int32()
  external int mainPfPos;

  /// 刻み
  @ffi.Uint8()
  external int posTicks;
}

/// TestMode 03 公開定義
/// TestModePatternAdjust
final class BPIFTestMode03Param_t extends ffi.Struct {
  /// 刻み幅
  @ffi.Uint16()
  external int fTicks;

  /// 刻み幅
  @ffi.Uint16()
  external int sTicks;

  @ffi.Int32()
  external int utlFPlusCorrect;

  @ffi.Int32()
  external int utlSPlusCorrect;

  @ffi.Int32()
  external int utlCorrectMultiF;

  @ffi.Int32()
  external int utlCorrectMultiS;

  @ffi.Int32()
  external int utlCorrectFforSMulti;
}

/// TestMode 06 公開定義
/// TestModeFeedDogPosition
final class BPIFTestMode06Param_t extends ffi.Struct {
  /// 関数判定用
  @ffi.Bool()
  external bool IconDrawWhenFeedDogIsUp;

  @ffi.Bool()
  external bool IconDrawWhenFeedDogIsDown;
}

/// TestMode 07 公開定義
/// TestModePresserFootHeight
final class BPIFTestMode07Param_t extends ffi.Struct {
  @ffi.Float()
  external double PftPosition;

  @ffi.Int()
  external int PftPositionAD;

  @ffi.Int()
  external int KlPositionAD;
}

/// TestMode 10 公開定義
/// TestModeLowerThread
final class BPIFTestMode10Param_t extends ffi.Struct {
  @ffi.Bool()
  external bool isDownThreadState;
}

/// TestMode 11 公開定義
/// TestModeUpperThread
final class BPIFTestMode11Param_t extends ffi.Struct {
  @ffi.Bool()
  external bool isUpperThreadState;
}

/// TestMode 13 公開定義
/// TestModeNormalReverseFeed
final class BPIFTestMode13Param_t extends ffi.Struct {
  @ffi.Int()
  external int feedForwardPulseAdjVal;

  @ffi.Bool()
  external bool isFeedForwardPulseAdjDef;

  @ffi.Int()
  external int feedReversePulseAdjVal;

  @ffi.Bool()
  external bool isFeedReversePulseAdjDef;

  /// 関数判定用
  @ffi.Bool()
  external bool IsNotAdjParameterLock;

  @ffi.Bool()
  external bool IsAdjParameterLock;
}

/// TestMode 14 公開定義
/// TestModeSpeed
final class BPIFTestMode14Param_t extends ffi.Struct {
  @ffi.Int32()
  external int targetSpeed;

  @ffi.Int32()
  external int currentSpeed;

  /// 関数判定用
  @ffi.Bool()
  external bool Is_MdfCountNotErr;

  @ffi.Bool()
  external bool Is_MdfCountErr;
}

/// TestMode 15 共有メモリ 構造体
final class BPIFTestMode15Param_t extends ffi.Struct {
  @ffi.Uint8()
  external int mode;

  @ffi.Int8()
  external int WlanSts;

  @ffi.Int8()
  external int ProjectorSts;

  @ffi.Int8()
  external int CameraCaptureSts;

  @ffi.Int8()
  external int LedSts;

  @ffi.Int8()
  external int LampSts;

  @ffi.Int8()
  external int UsbHostSts;

  @ffi.Int8()
  external int MFFCSts;

  @ffi.Int8()
  external int MFFCFotCtrl;

  @ffi.Int8()
  external int MFFCHealKick;

  @ffi.Int8()
  external int MFFCFotCtrlConnect;

  @ffi.Int8()
  external int MFFCFotCtrlType;

  @ffi.Int8()
  external int MFFCSideSwitch;

  @ffi.Int8()
  external int SpeedVR;
}

/// TestMode 17 の動作モード
enum TestMode17_Mode {
  /// Panel and Machine
  PANEL_AND_MACHINE(value: 0),

  /// Panel
  PANEL(value: 1),

  /// Machine
  MACHINE(value: 2);

  final int value;

  const TestMode17_Mode({
    required this.value,
  });
}

/// TestMode 17 の状態
enum TestMode17_State {
  /// デフォルト
  DEFAULT(value: 0),

  /// 待機
  IDLE(value: 1),

  /// 処理中
  PROCESSING(value: 2),

  /// 完了
  DONE(value: 3);

  final int value;

  const TestMode17_State({
    required this.value,
  });
}

/// TestMode 17 共有メモリ Sub構造体
final class BPIFTestMode17EepRom_t extends ffi.Struct {
  @ffi.Uint16()
  external int address;

  @ffi.Uint8()
  external int readValue;

  @ffi.Uint8()
  external int writeValue;
}

/// TestMode 17 共有メモリ 構造体
final class BPIFTestMode17Param_t extends ffi.Struct {
  @ffi.Uint8()
  external int mode;

  @ffi.Uint8()
  external int state;

  @ffi.Uint8()
  external int spec;

  @ffi.Uint8()
  external int card_spec;

  external BPIFTestMode17EepRom_t panel;

  external BPIFTestMode17EepRom_t machine;

  @ffi.Array(32)
  external Array<Char> model_name;

  @ffi.Array(32)
  external Array<Char> spec_name;

  @ffi.Array(32)
  external Array<Char> card_spec_name;
}

/// TestMode 19 公開定義
/// TestModeClearFlash
/// TestMode 58 公開定義
/// TestModeClearFlashPro
final class BPIFTestMode19Param_t extends ffi.Struct {
  @ffi.Int()
  external int embServiceStitchCount;

  @ffi.Int()
  external int embTotalStitchCount;

  @ffi.Int()
  external int utlServiceStitchCount;

  @ffi.Int()
  external int utlTotalStitchCount;

  @ffi.Int()
  external int serviceStitchTime;

  @ffi.Int()
  external int totalStitchTime;

  @ffi.Int()
  external int totalThreadCount;
}

/// TestMode 22 公開定義
/// TestModeNPSensor
final class BPIFTestMode22Param_t extends ffi.Struct {
  @ffi.Bool()
  external bool np1Hi;

  @ffi.Bool()
  external bool np2Hi;

  @ffi.Bool()
  external bool np3Hi;

  /// TimingSenserState_t
  @ffi.Uint8()
  external int timingSenserState;

  @ffi.Int32()
  external int shaftDegree;

  @ffi.Bool()
  external bool isValidNP;

  /// 関数判定用
  @ffi.Bool()
  external bool IconDrawWhenBuzzerOff_TestModeNP;

  @ffi.Bool()
  external bool IconDrawWhenBuzzerOn_TestModeNP;
}

/// TestMode 23 公開定義
/// TestModeEmbroideryMaxPos
final class BPIFTestMode23Param_t extends ffi.Struct {
  @ffi.Int()
  external int s_FrameSize;
}

/// TestMode 25 公開定義
/// TestModeTensionAdjustment
final class BPIFTestMode25Param_t extends ffi.Struct {
  @ffi.Int32()
  external int utlStraightAtPmWidth;

  @ffi.Bool()
  external bool isUtlStraightAtPmWidthDef;

  @ffi.Int32()
  external int utlEtcAtPmWidth;

  @ffi.Bool()
  external bool isUtlEtcAtPmWidthDef;

  @ffi.Int32()
  external int embAtPmWidth;

  @ffi.Bool()
  external bool isEmbAtPmWidthDef;

  /// 刻み
  @ffi.Uint8()
  external int straightAtPmWidthTicks;

  @ffi.Uint8()
  external int etcAtPmWidthTicks;

  @ffi.Uint8()
  external int embAtPmWidthTicks;

  /// 関数判定用
  @ffi.Bool()
  external bool IsTensionAdjustStraightStitchNotSel;

  @ffi.Bool()
  external bool IsTensionAdjustStraightStitchSel;

  @ffi.Bool()
  external bool IsTensionAdjustNotStraightStitchNotSel;

  @ffi.Bool()
  external bool IsTensionAdjustNotStraightStitchSel;

  @ffi.Bool()
  external bool IsTensionAdjustUtlStitch1NotSel;

  @ffi.Bool()
  external bool IsTensionAdjustUtlStitch1Sel;

  @ffi.Bool()
  external bool IsTensionAdjustUtlStitch2NotSel;

  @ffi.Bool()
  external bool IsTensionAdjustUtlStitch2Sel;

  @ffi.Bool()
  external bool IsTensionAdjust_UtlStitchSel_Gray;

  @ffi.Bool()
  external bool IsTensionAdjustDefaultModeOff;

  @ffi.Bool()
  external bool IsTensionAdjustDefaultModeOn;
}

/// TestMode 26 公開定義
/// TestModeDppCheck
final class BPIFTestMode26Param_t extends ffi.Struct {
  @ffi.Bool()
  external bool DppCheck1_Visible;

  @ffi.Bool()
  external bool DppCheck1_Result;

  @ffi.Float()
  external double DppCheck1_Rate;

  @ffi.Int()
  external int DppCheck1_Total;

  @ffi.Bool()
  external bool DppCheck2_Visible;

  @ffi.Bool()
  external bool DppCheck2_Result;

  @ffi.Float()
  external double DppCheck2_Rate;

  @ffi.Int()
  external int DppCheck2_Total;

  @ffi.Bool()
  external bool Debug_Visible;

  @ffi.Int()
  external int Debug_Detected_Point_X;

  @ffi.Int()
  external int Debug_Detected_Point_Y;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat3;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat4;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat5;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat6;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat7;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat8;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes3;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes4;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes5;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes6;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes7;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes8;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes9;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes10;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes11;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes12;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes13;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorRotVec0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorRotVec1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorRotVec2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorTransVec0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorTransVec1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorTransVec2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorUserTransVec0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorUserTransVec1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorUserTransVec2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorNdpOffsetX;

  @ffi.Float()
  external double Calib_FabricHeight;
}

// TestMode 27 公開定義
// testmode 27. Projector calibration for LM
// testmode 63. Projector Calibration For Service-Man
abstract class PrjCalibErr_t {
  static const int NON_ERR = 0;
  static const int ERR_0 = 1;
  static const int ERR_1 = 2;
  static const int ERR_2 = 3;
  static const int ERR_3 = 4;
  static const int ERR_4 = 5;
}

final class BPIFTestMode27Param_t extends ffi.Struct {
  @ffi.Int()
  external int s_CalibStep;

  @ffi.Bool()
  external bool s_ProCalibTskFlag;

  @ffi.Int()
  external int s_ProCalibResult;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat3;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat4;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat5;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat6;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat7;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat8;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes3;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes4;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes5;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes6;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes7;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes8;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes9;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes10;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes11;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes12;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes13;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorRotVec0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorRotVec1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorRotVec2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorTransVec0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorTransVec1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorTransVec2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorUserTransVec0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorUserTransVec1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorUserTransVec2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorNdpOffsetX;

  @ffi.Float()
  external double Calib_FabricHeight;

  @ffi.Float()
  external double Calib_CheckFabricHeight;

  @ffi.Float()
  external double s_left_x;

  @ffi.Float()
  external double s_left_y;

  @ffi.Float()
  external double s_right_x;

  @ffi.Float()
  external double s_right_y;

  @ffi.Float()
  external double s_offset;

  @ffi.Float()
  external double ndpCrLength;

  @ffi.Int()
  external int brightnessValue;
}

/// TestMode 28 公開定義
/// TestModeThreadingCheck
final class BPIFTestMode28Param_t extends ffi.Struct {
  @ffi.Int()
  external int thPmPos;

  @ffi.Bool()
  external bool isTHStateOK;

  @ffi.Int()
  external int thState;

  /// 関数判定用
  @ffi.Bool()
  external bool IconDrawWhenTHpmDuty100;

  @ffi.Bool()
  external bool IconDrawWhenTHpmDuty70;
}

/// TestMode 29 公開定義
/// TestModeInputCheck
final class BPIFTestMode29Param_t extends ffi.Struct {
  @ffi.Uint8()
  external int FootJackState;

  @ffi.Uint8()
  external int FootJackRecord;

  @ffi.Uint8()
  external int BWDState;

  @ffi.Uint8()
  external int BWDRecord;

  @ffi.Uint8()
  external int PFTState;

  @ffi.Uint8()
  external int PFTRecord;

  @ffi.Uint8()
  external int PLATE1State;

  @ffi.Uint8()
  external int PLATE1Record;

  @ffi.Uint8()
  external int PLATE2State;

  @ffi.Uint8()
  external int PLATE2Record;

  @ffi.Uint8()
  external int PLATE3State;

  @ffi.Uint8()
  external int PLATE3Record;

  @ffi.Uint8()
  external int FInitSensorState;

  @ffi.Uint8()
  external int FInitSensorRecord;

  @ffi.Uint8()
  external int ZInitSensorState;

  @ffi.Uint8()
  external int ZInitSensorRecord;

  @ffi.Uint8()
  external int SInitSensorState;

  @ffi.Uint8()
  external int SInitSensorRecord;

  @ffi.Uint8()
  external int PFInitSensorState;

  @ffi.Uint8()
  external int PFInitSensorRecord;

  @ffi.Uint8()
  external int THInitSensorState;

  @ffi.Uint8()
  external int THInitSensorRecord;

  @ffi.Uint8()
  external int TCInitSensorState;

  @ffi.Uint8()
  external int TCInitSensorRecord;

  @ffi.Uint8()
  external int ATInitSensorState;

  @ffi.Uint8()
  external int ATInitSensorRecord;

  @ffi.Uint8()
  external int XInitSensorState;

  @ffi.Uint8()
  external int XInitSensorRecord;

  @ffi.Uint8()
  external int YInitSensorState;

  @ffi.Uint8()
  external int YInitSensorRecord;

  @ffi.Uint8()
  external int EmbMachineState;

  @ffi.Uint8()
  external int EmbMachineRecord;

  @ffi.Uint8()
  external int FrameSW1State;

  @ffi.Uint8()
  external int FrameSW1Record;

  @ffi.Uint8()
  external int FrameSW2State;

  @ffi.Uint8()
  external int FrameSW2Record;

  @ffi.Uint8()
  external int FrameSW3State;

  @ffi.Uint8()
  external int FrameSW3Record;

  @ffi.Uint8()
  external int FrameSW4State;

  @ffi.Uint8()
  external int FrameSW4Record;

  @ffi.Uint8()
  external int FrameSW5State;

  @ffi.Uint8()
  external int FrameSW5Record;

  @ffi.Uint8()
  external int FrameLockLeverState;

  @ffi.Uint8()
  external int FrameLockLeverRecord;

  @ffi.Uint8()
  external int EmbMachineLeverState;

  @ffi.Uint8()
  external int EmbMachineLeverRecord;
}

/// TestMode 30 状態 check のbit定義
/// Default
final int TEST_MODE_EEP_CHECK_STATE_DEFAULT = 0x00000000;

/// SPEC異常
final int TEST_MODE_EEP_CHECK_STATE_SPEC_INVALID = 0x00000001;

/// ラインアップグレード開始用のファイルオープン失敗
final int TEST_MODE_EEP_CHECK_STATE_LINE_UPG_OPEN_FAILED = 0x00000002;

/// XP番外M0015 : ADD 2019/07/12 T.Maehashi
final int TEST_MODE_EEP_CHECK_STATE_PPASS_ERR_FAILED = 0x00000020;

/// XP番外M0015 : ADD 2019/07/12 T.Maehashi
final int TEST_MODE_EEP_CHECK_STATE_MPASS_ERR_FAILED = 0x00000040;

/// XP番外M0015 : ADD 2019/07/12 T.Maehashi
final int TEST_MODE_EEP_CHECK_STATE_BPASS_ERR_FAILED =
    (TEST_MODE_EEP_CHECK_STATE_MPASS_ERR_FAILED |
        TEST_MODE_EEP_CHECK_STATE_PPASS_ERR_FAILED);

/// TestMode 30 EEP Write の状態
enum TestMode30_EepWriteState {
  /// デフォルト
  EEP_WRITE_STATE_DEFAULT,

  /// 成功
  EEP_WRITE_STATE_SUCCESS,

  /// 処理開始
  EEP_WRITE_STATE_START,

  /// 書き込み中
  EEP_WRITE_STATE_WRITING,

  /// EEPファイルが存在しない
  EEP_WRITE_STATE_FILE_NOT_FOUND,

  /// EEP書き込みエラー
  EEP_WRITE_STATE_ERROR
}

/// TestMode 30 共有メモリ 構造体
final class BPIFTestMode30Param_t extends ffi.Struct {
  /// TestMode 30 の状態
  @ffi.Int32()
  external int check;

  /// EEP Write の状態 (IIVO_EEP.EEPを読み込んでEEPROMに書き込む機能): brother_panel が設定して App が参照
  @ffi.Int32()
  external int state;

  /// 機能実行中の進捗率: brother_panel が設定して App が参照
  @ffi.Uint8()
  external int progress;

  @ffi.Uint8()
  external int dummy1;

  @ffi.Uint8()
  external int dummy2;

  @ffi.Uint8()
  external int dummy3;

  /// IIVO_EEP.EEP の Full Path: App が設定して brother_panel が参照
  @ffi.Array(520)
  external Array<Char> eepFilePath;
}

/// TestMode 31 公開定義
/// TestModeProjectorCheck
final class BPIFTestMode31Param_t extends ffi.Struct {
  @ffi.Bool()
  external bool isLed;

  @ffi.Bool()
  external bool isUsedCamera;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat3;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat4;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat5;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat6;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat7;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorIntrinsticMat8;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes3;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes4;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes5;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes6;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes7;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes8;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes9;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes10;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes11;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes12;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorDistortionCoeffes13;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorRotVec0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorRotVec1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorRotVec2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorTransVec0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorTransVec1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorTransVec2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorUserTransVec0;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorUserTransVec1;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorUserTransVec2;

  @ffi.Array.multi([4])
  external ffi.Array<ffi.Short> Calib_ProjectorNdpOffsetX;
}

/// TestMode 32 公開定義
/// TestModeAD
final class BPIFTestMode32Param_t extends ffi.Struct {
  @ffi.Int32()
  external int SpeedVR;

  @ffi.Bool()
  external bool FotJack;

  @ffi.Int32()
  external int ADvalue;

  @ffi.Int32()
  external int ConnectionADvalue;

  @ffi.Int32()
  external int HeelKickADvalue;

  @ffi.Bool()
  external bool FotHkickState;

  @ffi.Bool()
  external bool FotHkick_onoff;

  @ffi.Int32()
  external int SideSwADvalue;

  @ffi.Bool()
  external bool FotSideSwState;

  @ffi.Bool()
  external bool FotSideSw_onoff;

  @ffi.Int32()
  external int FcTypeADvalue;

  /// FotControllerType_t
  @ffi.Uint8()
  external int FotType;

  @ffi.Int32()
  external int TargetSpeed;

  @ffi.Int32()
  external int CurrentSpeed;
}

/// TestMode 34 公開定義
/// TestModeXYRate
final class BPIFTestMode34Param_t extends ffi.Struct {
  @ffi.Bool()
  external bool isSendXyWaveOK;

  @ffi.Int16()
  external int WaveNumberX;

  @ffi.Int16()
  external int WaveNumberY;

  @ffi.Int16()
  external int XYRatePosX;

  @ffi.Int16()
  external int XYRateMoveX;

  @ffi.Int16()
  external int XYRatePosY;

  @ffi.Int16()
  external int XYRateMoveY;

  /// 関数判定用
  @ffi.Bool()
  external bool TestXYRateIs_Single;

  @ffi.Bool()
  external bool TestXYRateRepeat;

  @ffi.Bool()
  external bool TestXYRateIsNotStop;

  @ffi.Bool()
  external bool TestXYRateIs_Stop;

  @ffi.Bool()
  external bool TestXYRateIs_PosX;

  @ffi.Bool()
  external bool TextXRateIsMove;

  @ffi.Bool()
  external bool TestXYRateIs_PosY;

  @ffi.Bool()
  external bool TextYRateIsMove;
}

/// TestMode 35 公開定義
/// TestModeBH
final class BPIFTestMode35Param_t extends ffi.Struct {
  /// 前進カウント
  @ffi.Uint16()
  external int frontCnt;

  /// 後進カウント
  @ffi.Uint16()
  external int backCnt;

  /// 前進送り補正値
  @ffi.Int()
  external int forwardPulseAdjVal;

  /// 後進送り補正値
  @ffi.Int()
  external int reversePulseAdjVal;

  //関数判定用

  /// Lock/Unlock
  @ffi.Bool()
  external bool isAdjParameterLock;

  /// パラメータ異常
  @ffi.Bool()
  external bool isAdjParameterError;

  /// Forward Pulse Adjust Error
  @ffi.Bool()
  external bool isForwardPulseAdjError;

  /// Reverse Pulse Adjust Error
  @ffi.Bool()
  external bool isReversePulseAdjError;
}

/// TestMode 36 公開定義
/// TestModeSideFeedAdjust
final class BPIFTestMode36Param_t extends ffi.Struct {
  @ffi.Int()
  external int UtlFforSide;

  @ffi.Bool()
  external bool iscorrectLeftDef;

  @ffi.Int()
  external int correctLeft;

  @ffi.Bool()
  external bool iscorrectRightDef;

  @ffi.Int()
  external int correctRight;

  @ffi.Int()
  external int MainMotorTargetSpeed;

  /// 刻み
  @ffi.Uint8()
  external int feedRightwardPulseTicks;

  @ffi.Uint8()
  external int feedLeftwardPulseTicks;

  @ffi.Uint8()
  external int ptnFforSideTicks;

  /// 関数判定用
  @ffi.Bool()
  external bool IsNotAdjParameterLock;

  @ffi.Bool()
  external bool IsAdjParameterLock;

  @ffi.Bool()
  external bool Is_Correction_Pattern_Selected;

  @ffi.Bool()
  external bool IsNot_Correction_Pattern_Selected;

  @ffi.Bool()
  external bool Is_Confirmation_Pattern_Selected;

  @ffi.Bool()
  external bool IsNot_Confirmation_Pattern_Selected;
}

/// TestMode 37 公開定義
/// TestModeLedPointerAdjust
final class BPIFTestMode37Param_t extends ffi.Struct {
  @ffi.Bool()
  external bool HeightNumDef;

  ///高さ表示　0のとき、00を表示し、その他の時は符号と数字で表示(例 +1
  @ffi.Int32()
  external int HeightNum;

  /// 刻み
  @ffi.Uint8()
  external int HTicks;

  /// 関数判定用
  @ffi.Bool()
  external bool IsLedPt_NotOprate;

  @ffi.Bool()
  external bool IsLedPt_Brightness_Min;

  @ffi.Bool()
  external bool IsNotLedPt_Brightness_Min;

  @ffi.Bool()
  external bool IsLedPt_Brightness_Max;

  @ffi.Bool()
  external bool IsNotLedPt_Brightness_Max;

  @ffi.Bool()
  external bool IsLedPt_Oprate;
}

/// TestMode 39 公開定義
/// TestModeDF
final class BPIFTestMode39Param_t extends ffi.Struct {
  @ffi.Int()
  external int ForwardDFCorrectParam;

  @ffi.Int()
  external int BackDFCorrectParam;

  /// 刻み
  @ffi.Uint8()
  external int correctForwardTicks;

  @ffi.Uint8()
  external int correctBackTicks;

  /// 関数判定用
  @ffi.Bool()
  external bool IsTestPmMarginDFpmCurrent100;

  @ffi.Bool()
  external bool IsTestPmMarginDFpmCurrent70;

  @ffi.Bool()
  external bool IconDrawWhenFeedDogIsUp_DF;

  @ffi.Bool()
  external bool IconDrawWhenFeedDogIsDown_DF;

  @ffi.Bool()
  external bool IconDrawWhenRollerIs_Off;

  @ffi.Bool()
  external bool IconDrawWhenRollerIs_On;
}

/// TestMode 42 公開定義
/// TestModeEmbroideryPosition
final class BPIFTestMode42Param_t extends ffi.Struct {
  @ffi.Int16()
  external int XpmOffset;

  @ffi.Int16()
  external int YpmOffset;

  /// 刻み
  @ffi.Uint8()
  external int XpmOffsetTicks;

  @ffi.Uint8()
  external int YpmOffsetTicks;
}

/// 項目番号(項目番号テーブルアクセス用)
abstract class TESTMODE44_PRM_DEF_CHECK_NUM {
  /// Width Control
  static const int EN_PRM_DEF_CHECK_NUM_WIDTH_CONTROL = 0;

  /// Fine Adjust Verti
  static const int EN_PRM_DEF_CHECK_NUM_FINE_ADJUST_VERTI = 1;

  /// Fine Adjust Horiz
  static const int EN_PRM_DEF_CHECK_NUM_FINE_ADJUST_HORIZ = 2;

  /// Presser Foot Height
  static const int EN_PRM_DEF_CHECK_NUM_PRESSER_FOOT_HEIGHT = 3;

  /// Presser Foot Pressure
  static const int EN_PRM_DEF_CHECK_NUM_PRESSER_FOOT_PRESSURE = 4;

  /// Dual Feed Feed Adjustment
  static const int EN_PRM_DEF_CHECK_NUM_DUAL_FEED_FEED_ADJUSTMENT = 5;

  /// Dual Feed Feed Adjustment Setting Enable.
  static const int
      EN_PRM_DEF_CHECK_NUM_DUAL_FEED_FEED_ADJUSTMENT_SETTING_ENABLE = 6;

  /// Initial Position(Left or Center)
  static const int EN_PRM_DEF_CHECK_NUM_INITIAL_POSITION = 7;

  /// Initial Stitch Page(Quilt Mode)
  static const int EN_PRM_DEF_CHECK_NUM_INITIAL_STITCH_PAGE = 8;

  /// Multi Function Foot Controller HeelKick function info
  static const int EN_PRM_DEF_CHECK_NUM_MFFC_HEELKICK_FUNCTION = 9;

  /// Multi Fucntion Foot Controller SidePedal function info
  static const int EN_PRM_DEF_CHECK_NUM_MFFC_SIDEPEDAL_FUNCTION = 10;

  /// Pivoting Height
  static const int EN_PRM_DEF_CHECK_NUM_PIVOTING_HEIGHT = 11;

  /// Free Motion Foot Height
  static const int EN_PRM_DEF_CHECK_NUM_FREE_MOTION_FOOT_HEIGHT = 12;

  /// Automatic Fabric Sensor System
  static const int EN_PRM_DEF_CHECK_NUM_AUTOMATCI_FABRIC_SENSOR_SYSTEM = 13;

  /// Reinforcement Priority(止め縫い優先)    XV 追加
  static const int EN_PRM_DEF_CHECK_NUM_REINFORCEMENT_PRIORITY = 14;

  /// MDC Resume
  static const int EN_PRM_DEF_CHECK_NUM_MDC_RESUME_FLAG = 15;

  /// resume data flag
  static const int EN_PRM_DEF_CHECK_NUM_RESUME_EXIST = 16;

  /// WLAN setup confirmation
  static const int EN_PRM_DEF_CHECK_NUM_WLAN_SETUP_CONFIRMATION = 17;

  /// SR_Foot_Height  IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_SR_FOOT_HEIGHT = 18;

  /// Memory          IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_MEMORY = 19;

  /// Needle Up/Down Position // XV 名前変更 EN_PRM_DEF_CHECK_NUM_NEEDLE_POSITION
  static const int EN_PRM_DEF_CHECK_NUM_NEEDLE_UP_DOWN_POSITION = 20;

  /// Needle Position Stitch Placement
  static const int EN_PRM_DEF_CHECK_NUM_POINTING_NEEDLE_MODE = 21;

  /// Mouse Cursor
  static const int EN_PRM_DEF_CHECK_NUM_MOUSE_CURSOR = 22;

  /// Upper and Bobbin Thread Sensor
  static const int EN_PRM_DEF_CHECK_NUM_UPPER_AND_BOBBIN_THREAD_SENSOR = 23;

  /// Machine Speaker Volume
  static const int EN_PRM_DEF_CHECK_NUM_MACHINE_SPEAKER_VOLUME = 24;

  /// Light1
  static const int EN_PRM_DEF_CHECK_NUM_LIGHT_1 = 25;

  /// Screen Display Brightness   IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_SCREEN_DISPLAY_BRIGHTNESS = 26;

  /// Sleep function Eco Mode
  static const int EN_PRM_DEF_CHECK_NUM_ECO_MODE = 27;

  /// Sleep Function Shutoff SuppotMode
  static const int EN_PRM_DEF_CHECK_NUM_SHUTOFF_SUPPORT_MODE = 28;

  /// Screen Saver        // XV 復活
  static const int EN_PRM_DEF_CHECK_NUM_SCREEN_SAVER = 29;

  /// Opening Display
  static const int EN_PRM_DEF_CHECK_NUM_OPENING_DISPLAY = 30;

  /// Embroidery Frame Display
  static const int EN_PRM_DEF_CHECK_NUM_EMBROIDERY_FRAME_DISPLAY = 31;

  /// Thread Number Display
  static const int EN_PRM_DEF_CHECK_NUM_THREAD_NUMBER_DISPLAY = 32;

  /// Thread Name
  static const int EN_PRM_DEF_CHECK_NUM_THREAD_NAME = 33;

  /// Max Embroidery Speed
  static const int EN_PRM_DEF_CHECK_NUM_MAX_EMBROIDERY_SPEED = 34;

  /// Embroidery Tension
  static const int EN_PRM_DEF_CHECK_NUM_EMBROIDERY_TENSION = 35;

  /// Embroidery Foot Height
  static const int EN_PRM_DEF_CHECK_NUM_EMBROIDERY_FOOT_HEIGHT = 36;

  /// Couching Embroidery Foot Height
  static const int EN_PRM_DEF_CHECK_NUM_PF_EMB_COUTING_POS_OFFSET = 37;

  /// LongStitch Embroidery Foot Height
  static const int EN_PRM_DEF_CHECK_NUM_PF_EMB_LONG_STITCH_POS_OFFSET = 38;

  /// Bobbin Thread Speed
  static const int EN_PRM_DEF_CHECK_NUM_BWD_SPEED = 39;

  /// テンション仕様変更  縫製画面のテンション設定（設定画面とは違う）
  static const int EN_PRM_DEF_CHECK_NUM_EMBROIDERY_TENSION2 = 40;

  /// SR Buzzer Volume    IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_SR_BUZZER_VOLUME = 41;

  /// Voice Guidance      IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_VOICE_GUIDANCE = 42;

  /// Voice Language      IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_VOICE_LANGUAGE = 43;

  /// Voice Volume        IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_VOICE_VOLUME = 44;

  /// mm/inch
  static const int EN_PRM_DEF_CHECK_NUM_MM_INCH = 45;

  /// Embroidery Background Color
  static const int EN_PRM_DEF_CHECK_NUM_EMBROIDERY_BACKGROUND_COLOR = 46;

  /// Thumbnail Background Color
  static const int EN_PRM_DEF_CHECK_NUM_THUMBNAIL_BACKGROUND_COLOR = 47;

  /// Thumbnail Size
  static const int EN_PRM_DEF_CHECK_NUM_THUMBNAIL_SIZE = 48;

  /// Embroidery Basting Distance
  static const int EN_PRM_DEF_CHECK_NUM_EMBROIDERY_BASTING_DISTANCE = 49;

  /// Emboridery Foot With LED Pointer Brightness
  static const int
      EN_PRM_DEF_CHECK_NUM_EMBROIDERY_FOOT_WITH_LED_POINTER_BRIGHTNESS = 50;

  /// Emboridery Foot With LED Pointer Adjust
  static const int
      EN_PRM_DEF_CHECK_NUM_EMBROIDERY_FOOT_WITH_LED_POINTER_ADJUST = 51;

  /// Embroidery Foot with LED Pointer Setting Enable.
  static const int
      EN_PRM_DEF_CHECK_NUM_EMBROIDERY_FOOT_WITH_LED_POINTER_SETTING_ENABLE = 52;

  /// Background Image Display                // XV 復活 カメラ関係
  static const int EN_PRM_DEF_CHECK_NUM_IMAGE_SCAN_DISPLAY = 53;

  /// Scan Quality                            // XV 追加 カメラ関係
  static const int EN_PRM_DEF_CHECK_NUM_SCAN_QUALITY = 54;

  /// Fabric Thickness Sensor // XV 復活 カメラ関係
  static const int EN_PRM_DEF_CHECK_NUM_IMAGE_SCAN_DISPLAY_FABLIC_HEIGHT = 55;

  /// Double Needle
  static const int EN_PRM_DEF_CHECK_NUM_DOUBLE_NEEDLE = 56;

  /// Clock Function
  static const int EN_PRM_DEF_CHECK_NUM_CLOCK_FUNCTION = 57;

  /// Setting Startup Language
  static const int EN_PRM_DEF_CHECK_NUM_SETTING_STARTUP_LANGUAGE = 58;

  /// Utl Spacing
  static const int EN_PRM_DEF_CHECK_NUM_UTL_CHAR_SPACE = 59;

  /// Projector Background Color
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_BACKGROUND_COLOR = 60;

  /// Projector Pattern Outline
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_PATTERN_OUTLINE = 61;

  /// Projector Pointer Color
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_POINTER_COLOR = 62;

  /// Projector Pointer Shape
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_POINTER_SHAPE = 63;

  /// Stylue Function For Embroidery                  IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_STYLUS_FUNCTION_FOR_EMBROIDERY = 64;

  /// Placement of Stylue Function For Embroidery     IIVO 追加
  static const int
      EN_PRM_DEF_CHECK_NUM_PLACEMENT_OF_STYLUE_FUNCTION_FOR_EMBROIDERY = 65;

  /// Embroidery Needle Stop Position
  static const int EN_PRM_DEF_CHECK_NUM_EMB_NEEDLE_STOP_POSITION = 66;

  /// Embroidery Auto PF
  static const int EN_PRM_DEF_CHECK_NUM_EMB_AUTOPF = 67;

  /// Image Scan File         IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_IMAGE_SCAN_FILE = 68;

  /// WLAN Information        IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_WLAN_INFORMATION = 69;

  /// WLAN Machine Name       IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_WLAN_MACHINE_NAME = 70;

  /// UtlSewingAutoStop JustStopEnable
  static const int EN_PRM_DEF_CHECK_NUM_SEWINGAUTOSTOP_JUST_STOP_ENABLE = 71;

  /// Twin Needle Removed Check
  static const int EN_PRM_DEF_CHECK_NUM_TWIN_NEEDLE_REMOVED_CHECK = 72;

  /// Connection Server(CanvasServer)
  static const int EN_PRM_DEF_CHECK_NUM_CONNECTION_SERVER1 = 73;

  /// Connection Server(Kit認証サーバー)
  static const int EN_PRM_DEF_CHECK_NUM_CONNECTION_SERVER2 = 74;

  /// Senju Badge
  static const int EN_PRM_DEF_CHECK_NUM_SENJU_BADGE = 75;

  /// Stylus function for embroidery
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_STYLUS_FUNCTION_FOR_EMBROIDERY = 76;

  /// Projector Guideline Main Shape
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_MAIN_LINE_SHAPE =
      77;

  /// Projector Guideline Line Length
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_MAIN_LINE_LEN = 78;

  /// Projector Guideline Main Color
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_MAIN_LINE_COLOR =
      79;

  /// Projector Guideline Main PosV
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_MAIN_LINE_POS_VERTICAL = 80;

  /// Projector Guideline Main PosH
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_MAIN_LINE_POS_HORIZ = 81;

  /// Projector Guideline Sub Pattern
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SUB_LINE_PTN = 82;

  /// Projector Guideline Sub Line Length
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SUB_LINE_LEN = 83;

  /// Projector Guideline Sub Color
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SUB_LINE_COLOR = 84;

  /// Projector Guideline Sub PosV
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SUB_LINE_POS_VERTICAL = 85;

  /// Projector Guideline Sub PosH
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SUB_LINE_POS_HORIZ =
      86;

  /// Projector Guideline Sub Grid Pitch
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SUB_LINE_GRID_PITCH = 87;

  /// Projector Guideline Sub Angle
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SUB_LINE_ANGLE = 88;

  /// Projector Guideline Sub Seam Allowrance
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SUB_LINE_SEAM_ALLOW = 89;

  /// Projector Guideline Sub EX PosV                 IIVO 追加
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SUB_LINE_EX_VERTICAL = 90;

  /// Projector Guideline Sub EX PosH                 IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SUB_LINE_EX_HORIZ =
      91;

  /// Projector Guideline Sub EX Circle Radius Size   IIVO 追加
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SUB_LINE_EX_CIRCLE_RADIUS_SIZE =
      92;

  /// Projector Guideline Sub EX 3Line Grid Size      IIVO 追加
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SUB_LINE_EX_3LINE_GRID_SIZE = 93;

  /// Projector Guideline BH Main Color               IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_BH_MAIN_COLOR = 94;

  /// Projector Guideline BH Sub Pattern              IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_BH_SUB_PATTERN = 95;

  /// Projector Guideline BH Sub V or H               IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_BH_SUB_V_OR_H = 96;

  /// Projector Guideline BH Sub Interval Color       IIVO 追加
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_BH_SUB_INTERCAL_COLOR = 97;

  /// Projector Guideline BH Sub Edge Color           IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_BH_SUB_EDGE_COLOR =
      98;

  /// Projector Guideline BH Sub Distance Length      IIVO 追加
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_BH_SUB_DISTANCE_LENGTH = 99;

  /// Projector Guideline BH Sub Edge Length          IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_BH_SUB_EDGE_LENGTH =
      100;

  /// Projector Guideline SR Status                   IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SR_STATUS = 101;

  /// Projector Guideline SR Recognition Line         IIVO 追加
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SR_RECOGNITION_LINE = 102;

  /// Projector Guideline SR Recognition Line Color   IIVO 追加
  static const int
      EN_PRM_DEF_CHECK_NUM_PROJECTOR_GUIDELINE_SR_RECOGNITION_LINE_COLOR = 103;

  /// Couching Speed   IIVO 追加
  static const int EN_PRM_DEF_CHECK_NUM_COUCHING_SPEED = 104;

  // ----------
  // 以下、非表示項目
  // ----------
  /// Heelkick Regist
  static const int EN_PRM_DEF_CHECK_NUM_HEELKICK_REGIST = 105;

  /// Sidesw Regist
  static const int EN_PRM_DEF_CHECK_NUM_SIDESW_REGIST = 106;

  /// Utl Autopf Lift Trim
  static const int EN_PRM_DEF_CHECK_NUM_UTL_AUTOPF_LIFT_TRIM = 107;

  /// Utl Autopf Lift Down
  static const int EN_PRM_DEF_CHECK_NUM_UTL_AUTOPF_LIFT_DOWN = 108;

  /// Utl Autopf Lift Up
  static const int EN_PRM_DEF_CHECK_NUM_UTL_AUTOPF_LIFT_UP = 109;

  /// Utl Pivot Save
  static const int EN_PRM_DEF_CHECK_NUM_UTL_PIVOT_SAVE = 110;

  /// Ss Cur Type
  static const int EN_PRM_DEF_CHECK_NUM_SS_CUR_TYPE = 111;

  /// Emb Grid Display
  static const int EN_PRM_DEF_CHECK_NUM_EMB_GRID_DISPLAY = 112;

  /// Jump Stitch Length
  static const int EN_PRM_DEF_CHECK_NUM_JUMP_STITCH_LENGTH = 113;

  /// Jump Stitch Trim
  static const int EN_PRM_DEF_CHECK_NUM_JUMP_STITCH_TRIM = 114;

  /// Emb Wappen Offset
  static const int EN_PRM_DEF_CHECK_NUM_EMB_WAPPEN_OFFSET = 115;

  /// 終端
  static const int EN_PRM_DEF_CHECK_NUM_MAX = 116;
}

abstract class TESTMODE44_SERIAL_NO_ERR {
  /// エラーなし
  static const int SERIAL_NO_ERR_NON = 0;

  /// パネル側エラー
  static const int SERIAL_NO_ERR_PANEL_BOARD = 1;

  /// メイン側エラー
  static const int SERIAL_NO_ERR_MAIN_BOARD = 2;

  /// パネル、メイン両方エラー
  static const int SERIAL_NO_ERR_PANEL_AND_MAIN = 3;

  /// シリアルナンバーが違う
  static const int SERIAL_NO_ERR_DIFFERENCE = 4;
}

abstract class TESTMODE44_PRODUCT_ID_ERR {
  /// エラーなし
  static const int PRODUCT_ID_ERR_NON = 0;

  /// パネル側エラー
  static const int PRODUCT_ID_ERR_PANEL_BOARD = 1;

  /// メイン側エラー
  static const int PRODUCT_ID_ERR_MAIN_BOARD = 2;

  /// パネル、メイン両方エラー
  static const int PRODUCT_ID_ERR_PANEL_AND_MAIN = 3;

  /// プロダクトＩＤが違う
  static const int PRODUCT_ID_ERR_DIFFERENCE = 4;
}

/// TestMode 44 公開定義
/// TestModeParameterDefaultCheck
final class BPIFTestMode44Param_t extends ffi.Struct {
  @ffi.Int32()
  external int serialNumberErr;

  @ffi.Int32()
  external int productIdErr;

  @ffi.Bool()
  external bool utlManualMemErr;

  @ffi.Bool()
  external bool compFrameScanImgDataErr;

  @ffi.Int32()
  external int panelMajorVer;

  @ffi.Int32()
  external int panelMinorVer;

  @ffi.Int32()
  external int panelPatchVer;

  @ffi.Int32()
  external int libMajorVer;

  @ffi.Int32()
  external int libMinorVer;

  @ffi.Int32()
  external int libPatchVer;

  @ffi.Int32()
  external int mainVersion;

  @ffi.Int32()
  external int bootVersion;

  @ffi.Array.multi([12])
  external ffi.Array<ffi.Uint8> strKisyu;

  @ffi.Array.multi([10])
  external ffi.Array<ffi.Uint8> strSerial;

  @ffi.Array.multi([TESTMODE44_PRM_DEF_CHECK_NUM.EN_PRM_DEF_CHECK_NUM_MAX])
  external ffi.Array<ffi.Bool> isDefault;

  @ffi.Bool()
  external bool strSerialErr;

  /// 関数判定用
  @ffi.Bool()
  external bool IsParameterDefaultOK;

  @ffi.Bool()
  external bool IsParameterDefaultNG;
}

/// TestMode 47 公開定義
/// TestModePmInitialPhaseSet
final class BPIFTestMode47Param_t extends ffi.Struct {
  @ffi.Int()
  external int phaseNumberF;

  @ffi.Int()
  external int phaseNumberZ;

  /// 関数判定用
  @ffi.Bool()
  external bool IsZpmIniPhaseSetDraw;

  @ffi.Bool()
  external bool IsZpmIniPhaseSetNotDraw;

  @ffi.Bool()
  external bool IsZpmIniPhaseCheckDraw;

  @ffi.Bool()
  external bool IsZpmIniPhaseCheckNotDraw;

  @ffi.Bool()
  external bool IsFpmIniPhaseSetDraw;

  @ffi.Bool()
  external bool IsFpmIniPhaseSetNotDraw;

  @ffi.Bool()
  external bool IsFpmIniPhaseCheckDraw;

  @ffi.Bool()
  external bool IsFpmIniPhaseCheckNotDraw;
}

/// TestMode 48 公開定義
/// TestModeProcessCheck
final class BPIFTestMode48Param_t extends ffi.Struct {
  /// 判定結果
  @ffi.Bool()
  external bool isProcessCheckOK;

  /// PRCS_SideFeed_ParamDraw
  @ffi.Int()
  external int utlFSide;

  /// PRCS_SideFeed_ParamDraw
  @ffi.Bool()
  external bool isUtlFSideDef;

  /// PRCS_Speed_ParamDraw
  @ffi.Int()
  external int speedTestMax;

  /// PRCS_Pattern_ParamDraw
  @ffi.Int()
  external int utlFPlusCorrect;

  /// PRCS_Pattern_ParamDraw
  @ffi.Bool()
  external bool isUtlFPlusCorrectDef;

  /// PRCS_SideFeed_ParamDraw,PRCS_Pattern_ParamDraw
  @ffi.Int()
  external int utlSPlusCorrect;

  /// PRCS_SideFeed_ParamDraw,PRCS_Pattern_ParamDraw
  @ffi.Bool()
  external bool isUtlSPlusCorrectDef;

  /// メインのEEPROMをクリア
  @ffi.Bool()
  external bool isMainBoardEEPClr;

  @ffi.Array.multi([56])
  external ffi.Array<ffi.Bool> isProcessCheckItem;

  /// 関数判定用
  @ffi.Bool()
  external bool IsProcessCheckAllOK;

  @ffi.Bool()
  external bool IsProcessCheckNotAllOK;

  @ffi.Bool()
  external bool IconDrawWhenSaveEnable;

  @ffi.Bool()
  external bool IsClearMainBoardEepDraw;
}

/// TestMode 49 公開定義
/// TestModeCameraCalibrationForPL
/// TestMode 54 公開定義
/// TestModeCameraCalibrationForSM
/// TestMode 57 公開定義
/// TestModeCameraCalibrationResultCheckForSM
/// TestMode 59 公開定義
/// TestModeCameraCalibrationResultCheckForPL
abstract class TestCalibErr_t {
  static const int TEST_CALIB_NO_ERR = 0;
  static const int TEST_CALIB_NDP_ERR = 1;
  static const int TEST_CALIB_DOT_ERR = 2;
  static const int TEST_CALIB_CAMERA_ANGLE_ERR = 3;
  static const int TEST_CALIB_PRINT_ERR = 4;
  static const int TEST_CALIB_EMB_FRAME_ERR = 5;
  static const int TEST_CALIB_NEEDLE_UP_ERR = 6;
  static const int TEST_CALIB_KEY1_ERR = 7;
  static const int TEST_CALIB_KEY2_ERR = 8;
}

abstract class CalibProcRslt_t {
  static const int CALIB_NG = 0;
  static const int CALIB_OK = 1;
  static const int CALIB_ON_GO = 2;
  static const int CALIB_NON = 3;
}

final class BPIFTestMode49Param_t extends ffi.Struct {
  @ffi.Int()
  external int Points;

  @ffi.Float()
  external double div;

  @ffi.Float()
  external double Sew;

  @ffi.Float()
  external double Cam;

  @ffi.Float()
  external double Ang;

  @ffi.Float()
  external double Ndl_X;

  @ffi.Float()
  external double Ndl_Y;

  @ffi.Bool()
  external bool isAttachedEmbUnit;

  @ffi.Int32()
  external int s_TestCalibErr;

  @ffi.Int()
  external int s_CameraProcNum;

  @ffi.Int()
  external int s_cameraCapCnt;

  @ffi.Bool()
  external bool s_ndlSetPosResult;

  @ffi.Int()
  external int s_calibProcRslt; //CalibProcRslt_t
}

/// TestMode 50 公開定義
/// TestModeUtilityStitch
final class BPIFTestMode50Param_t extends ffi.Struct {
  @ffi.Bool()
  external bool isBHmode;

  @ffi.Int()
  external int BHLength;

  /// 関数判定用
  @ffi.Bool()
  external bool IsTestUtilityStitch_Pattern_1;

  @ffi.Bool()
  external bool IsNotTestUtilityStitch_Pattern_1;

  @ffi.Bool()
  external bool IsTestUtilityStitch_Pattern_2;

  @ffi.Bool()
  external bool IsNotTestUtilityStitch_Pattern_2;

  @ffi.Bool()
  external bool IsTestUtilityStitch_Pattern_3;

  @ffi.Bool()
  external bool IsNotTestUtilityStitch_Pattern_3;

  @ffi.Bool()
  external bool IsTestUtilityStitch_Pattern_4;

  @ffi.Bool()
  external bool IsNotTestUtilityStitch_Pattern_4;

  @ffi.Bool()
  external bool IsTestUtilityStitch_Pattern_4_min;

  @ffi.Bool()
  external bool IsNotTestUtilityStitch_Pattern_4_min;

  @ffi.Bool()
  external bool IsTestUtilityStitch_Pattern_4_max;

  @ffi.Bool()
  external bool IsNotTestUtilityStitch_Pattern_4_max;

  @ffi.Bool()
  external bool IsTestUtilityStitch_Pattern_5;

  @ffi.Bool()
  external bool IsNotTestUtilityStitch_Pattern_5;

  @ffi.Bool()
  external bool IsTestUtilityStitch_Pattern_5_min;

  @ffi.Bool()
  external bool IsNotTestUtilityStitch_Pattern_5_min;

  @ffi.Bool()
  external bool IsTestUtilityStitch_Pattern_5_max;

  @ffi.Bool()
  external bool IsNotTestUtilityStitch_Pattern_5_max;

  @ffi.Bool()
  external bool IsBHModeOff;

  @ffi.Bool()
  external bool IsBHModeOn;
}

/// TestMode 51 公開定義
/// TestModePmMargin
final class BPIFTestMode51Param_t extends ffi.Struct {
  @ffi.Int()
  external int TCpmPosition;

  @ffi.Bool()
  external bool isReadyBR;

  @ffi.Bool()
  external bool isReadyD30;

  /// 関数判定用
  @ffi.Bool()
  external bool IsTestPmMarginZpmCurrent100;

  @ffi.Bool()
  external bool IsTestPmMarginZpmCurrent70;

  @ffi.Bool()
  external bool IsTestPmMarginFpmCurrent100;

  @ffi.Bool()
  external bool IsTestPmMarginFpmCurrent70;

  @ffi.Bool()
  external bool IsTestPmMarginSpmCurrent100;

  @ffi.Bool()
  external bool IsTestPmMarginSpmCurrent70;

  @ffi.Bool()
  external bool IsTestPmMarginTCpmCurrent100;

  @ffi.Bool()
  external bool IsTestPmMarginTCpmCurrent70;
}

/// TestMode 52公開定義
/// TestModeLedSpeaker
final class BPIFTestMode52Param_t extends ffi.Struct {
  /// 関数判定用
  @ffi.Bool()
  external bool IsTestModeLed_1_OFF;

  @ffi.Bool()
  external bool IsNotTestModeLed_1_OFF;

  @ffi.Bool()
  external bool IsTestModeLed_1_MIN;

  @ffi.Bool()
  external bool IsNotTestModeLed_1_MIN;

  @ffi.Bool()
  external bool IsTestModeLed_1_MAX;

  @ffi.Bool()
  external bool IsNotTestModeLed_1_MAX;

  @ffi.Bool()
  external bool IsTestModeLed_2_OFF;

  @ffi.Bool()
  external bool IsNotTestModeLed_2_OFF;

  @ffi.Bool()
  external bool IsTestModeLed_2_MIN;

  @ffi.Bool()
  external bool IsNotTestModeLed_2_MIN;

  @ffi.Bool()
  external bool IsTestModeLed_2_MAX;

  @ffi.Bool()
  external bool IsNotTestModeLed_2_MAX;
}

/// TestMode 53 公開定義
/// TestModeExUnitCheck
final class BPIFTestMode53Param_t extends ffi.Struct {
  @ffi.Bool()
  external bool isTestModeExUnit_Normal_OverCurrent_UP;

  @ffi.Bool()
  external bool isTestModeExUnit_EepOK_UP;

  @ffi.Bool()
  external bool isTestModeExUnit_Checked_Over_UP;

  @ffi.Bool()
  external bool isTestModeExUnit_Over_OverCurrent_UP;

  @ffi.Bool()
  external bool isTestModeExUnit_Normal_OverCurrent_DOWN;

  @ffi.Bool()
  external bool isTestModeExUnit_EepOK_DOWN;

  @ffi.Bool()
  external bool isTestModeExUnit_Checked_Over_DOWN;

  @ffi.Bool()
  external bool isTestModeExUnit_Over_OverCurrent_DOWN;

  /// 関数判定用
  @ffi.Bool()
  external bool IsCheckedTestModeExUnitNormal_UP;

  @ffi.Bool()
  external bool IsNotCheckedTestModeExUnitNormal_UP;

  @ffi.Bool()
  external bool IsCheckedTestModeExUnitNormal_DOWN;

  @ffi.Bool()
  external bool IsNotCheckedTestModeExUnitNormal_DOWN;
}

/// TestMode 60 EEP To File (Panel) の状態
enum TestMode60_EepToFilePanelState {
  /// デフォルト
  EEP_TO_FILE_PANEL_STATE_DEFAULT,

  /// 成功
  EEP_TO_FILE_PANEL_STATE_SUCCESS,

  /// 処理開始
  EEP_TO_FILE_PANEL_STATE_START,

  /// 保存処理中
  EEP_TO_FILE_PANEL_STATE_SAVING,

  /// 保存エラー
  EEP_TO_FILE_PANEL_STATE_ERROR
}

/// TestMode 60 EEP To File (Machine) の状態
enum TestMode60_EepToFileMachineState {
  /// デフォルト
  EEP_TO_FILE_MACHINE_STATE_DEFAULT,

  /// 成功
  EEP_TO_FILE_MACHINE_STATE_SUCCESS,

  /// 処理開始
  EEP_TO_FILE_MACHINE_STATE_START,

  /// 保存処理中
  EEP_TO_FILE_MACHINE_STATE_SAVING,

  /// 保存エラー
  EEP_TO_FILE_MACHINE_STATE_ERROR
}

/// TestMode 60 EEP Write の状態
enum TestMode60_EepWriteState {
  /// デフォルト
  EEP_WRITE_STATE_DEFAULT,

  /// 成功
  EEP_WRITE_STATE_SUCCESS,

  /// 処理開始
  EEP_WRITE_STATE_START,

  /// 書き込み中
  EEP_WRITE_STATE_WRITING,

  /// EEPファイルが存在しない
  EEP_WRITE_STATE_FILE_NOT_FOUND,

  /// EEP書き込みエラー
  EEP_WRITE_STATE_ERROR
}

/// TestMode 60 共有メモリ 構造体
final class BPIFTestMode60Param_t extends ffi.Struct {
  /// EEP To File (Panel) の状態 (Panel FirmのEEPROMを読み込んで、IIVO_EEP.bin で出力する機能): brother_panel が設定して App が参照
  @ffi.Int32()
  external int state1;

  /// EEP To File (Machine) の状態 (Main FirmのEEPROMを読み込んで、IIVO_EEP_Main.bin で出力する機能): brother_panel が設定して App が参照
  @ffi.Int32()
  external int state2;

  /// EEP Write の状態 (IIVO_EEP.EEPを読み込んでEEPROMに書き込む機能): brother_panel が設定して App が参照
  @ffi.Int32()
  external int state3;

  /// 機能実行中の進捗率: brother_panel が設定して App が参照
  @ffi.Uint8()
  external int progress;

  @ffi.Uint8()
  external int dummy1;

  @ffi.Uint8()
  external int dummy2;

  @ffi.Uint8()
  external int dummy3;

  /// IIVO_EEP.bin の Full Path: App が設定して brother_panel が参照
  @ffi.Array(520)
  external Array<Char> eepToFilePanelPath;

  /// IIVO_EEP_Main.bin の Full Path: App が設定して brother_panel が参照
  @ffi.Array(520)
  external Array<Char> eepToFileMachinePath;

  /// IIVO_EEP.EEP の Full Path: App が設定して brother_panel が参照
  @ffi.Array(520)
  external Array<Char> eepFilePath;
}

/// TestMode 62 公開定義
/// TestModeEncoderOffsetCheck
final class BPIFTestMode62Param_t extends ffi.Struct {
  @ffi.Int()
  external int testErrorStatus;

  @ffi.Bool()
  external bool isYAdjustFinished;

  @ffi.Bool()
  external bool isXAdjustFinished;

  /// 関数判定用
  @ffi.Bool()
  external bool XmotorSaveOk;

  @ffi.Bool()
  external bool YmotorSaveOk;

  //刺繍機内EEPデータ表示用
  @ffi.Array.multi([9])
  external ffi.Array<ffi.Uint8> xyEepData; //PHFIRMIIVO-7790
}

/// TestMode 64 共有メモリ 構造体
/// TestMode64State_t
final class BPIFTestMode64Param_t extends ffi.Struct {
  @ffi.Bool()
  external bool isEmbMachine;

  @ffi.Int()
  external int scanState;
}

/// TestMode 65 共有メモリ 構造体
/// TestMode65State_t
final class BPIFTestMode65Param_t extends ffi.Struct {
  @ffi.Bool()
  external bool onoff;

  @ffi.Int()
  external int cpi;

  @ffi.Int()
  external int srcpuver;
}

/// TestMode 69 公開定義
/// TestModeATPMCheck
final class BPIFTestMode69Param_t extends ffi.Struct {
  /// 脱調したかどうか
  @ffi.Bool()
  external bool isATInitialSenserState;

  /// 関数判定用
  @ffi.Bool()
  external bool IsNotCheckedTestModeATInitialSenserState;

  @ffi.Bool()
  external bool IsCheckedTestModeATPMStartKeyGray;

  @ffi.Bool()
  external bool IsNotCheckedTestMode100PerStartKeyGray;

  @ffi.Bool()
  external bool IsCheckedTestMode100PerStartKeyGray;
}

/// 画面baseモード 定数定義		画面baseモード = DirNum_t.Num[0]の値
enum BaseMode_t {
  /*	オープニング				*/
  BMODE_OPENNING,
  /*	ホーム				*/
  BMODE_HOME,
  /*	実用縫い					*/
  BMODE_UTL,
  /*	(実用)文字・模様			*/
  BMODE_MOJIMOYOU,

/*	テストモード				*/
  BMODE_TEST,

/*	(刺しゅう)編集				*/
  BMODE_EDIT,

  /*	使い方						*/
  BMODE_HOW_TO_USE,

/*	マイイラスト				*/
  BMODE_MYILLUST,

  /*	セッティング				*/
  BMODE_SETTINGS,

  //2012-03-29 ZZS620 VcombL LANG ins start
  /*  言語設定					*/
  BMODE_SETTING_LANG,

  BMODE_SETTING_RTC, //時計設定

/*	D5とは別のプログラム起動	*/
  BMODE_RAM_PROGRAM,

/*	視点変更*/
  BMODE_CHANGE_VIEW,
  /*	アニメーション*/
  BMODE_MOVIE,
  /*	X2V300 MCD toukurma */
  BMODE_EMB_MCD,
  /*	エラー(全モード共用)		*/
  BMODE_ERROR,
  /*	ワーニング(全モード共用)	*/
  BMODE_WARNING,
  /*	各モード共用の画面			*/
  BMODE_COMMON,

/* マイデザインセンター２画面 */ /*** XV V200 M-174 20160115 zzpba469 ***/
  BMODE_EMB_MDC,
  /* まだほかにもあるはず？	*/
  BMODE_ETC,

  BMODE_MAX,
}

enum FrameMoveDownThreadState_t {
  FMDT_Not,
  FMDT_Start,
  FMDT_ThreadCut,
  FMDT_Return,
  FMDT_End,
  FMDT_BW_Return,
  /* 複数BW ZZPBA528 */
  FMDT_BW_ReturnOK /* 複数BW ZZPBA528 */
}

enum InitState_t {
  INITSTATE_PANDING,
  INITSTATE_ALLDATA_ERROR, //ALLDATAエラー
  INITSTATE_DATA_VERSION_DIFFERENCE, //DATA VERSION DIFFERENCE
  INITSTATE_PCB_POWER_OFF_NMI, //メイン基板電源OFF(NMI)
  INITSTATE_PCB_POWER_OFF, //メイン基板電源OFF
  INITSTATE_PANEL_NO_DATA, //COPY COPY COPY COPY COPY COPY MACHINE TO PANEL
  INITSTATE_PANEL_NO_DATA_COMPLETED,
  INITSTATE_MAIN_NO_DATA, //COPY COPY COPY COPY COPY COPY PANEL TO MACHINE
  INITSTATE_MAIN_NO_DATA_COMPLETED, //COPY COPY COPY COPY COPY COPY PANEL TO MACHINE
  INITSTATE_PANEL_AND_MAIN_NO_DATA, //PANEL AND MACHINE NON DATA
  INITSTATE_PANEL_SPEC_EMPTY, //Panel基板の仕向け値が0xFFだった場合
  INITSTATE_SPEC_DIFFERENCE, //パネル基板 メイン基板 プロダクトID違う時の処理
  INITSTATE_PROGRAM_SPEC_DIFFERENCE, //ALLDATAとEEPROMの値が違う
  INITSTATE_EEPROM_ERROR, //上記以外のエラー
  INITSTATE_UNSPPORT_ERROR, //非対応モード
  INITSTATE_UNSPPORTED_MODEL_ERROR, //非対応モデル
  INITSTATE_INTERNAL_ERROR, //メモリなどの内部エラー
  INITSTATE_NORMAL_START, //通常起動で起動完了
  INITSTATE_TEST_MODE_START, //通常のテストモード
  INITSTATE_TEST_MODE_NORMAL_MODE, //ラインテストモードで起動　TestModeNormalModeOk
  INITSTATE_TEST_MODE_EEPROM_COPY, //ＥＥＰＲＯＭコピーモード　TestModeEepRomCopy
  INITSTATE_TEST_MODE_EEPROM_COPY_FINISH, //ＥＥＰＲＯＭコピーモード終了　TestModeEepRomCopy

  INITSTATE_TEST_MODE_ERROR_SIMUKE_MASKROM, //仕向けとマスクロムの不一致エラー　TestModeSimukeMaskRomError　未使用
  INITSTATE_TEST_LCD_CONTRAST_ADJUST, //液晶コントラスト調整　TestModeLcdContrastAdjust
  INITSTATE_TEST_TOUCH_PANEL_ADJUST, //タッチパネル調整画面　TestModeTouchPanelAdjust
  INITSTATE_TEST_NO_TOUCH_PANEL, //タッチパネルなしモード　TestModeNoTouchPanel
  INITSTATE_TEST_PM_PHASE, //ＦＺイニシャル相未設定確認

  INITSTATE_RESET, //ユーザ情報削除状態

  INITSTATE_TEST_MODE_NORMAL_MODE_SPEC_ERR, //ラインテストモード　仕向け違い
  INITSTATE_TEST_MODE_NORMAL_MODE_PHASE_ERR, //ラインテストモード 位相設定なし
  INITSTATE_TEST_MODE_NORMAL_MODE_SPEC_PHASE_ERR, //ラインテストモード　仕向け違い＆位相設定なし
  INITSTATE_TEST_MODE_NORMAL_MODE_OK, //ラインテストモードで起動完了

  INITSTATE_UPGRADE_MODE, //ラインテストモードで起動完了

  INITSTATE_TEST_MODE_SERVICE_MODE_XY_MOTOR_ADJUST, //サービスマン用XY調整モード //PHFIRMIIVO-7790
}

enum PopupState_t {
  POPUPSTATE_PANDING, //起動処理中
  POPUPSTATE_ON, //POPUP ON
  POPUPSTATE_OFF, //POPUP OFF
}

final class ErrorInfo_t extends ffi.Struct {
  @ffi.Int32()
  external int errorCode;

  @ffi.Int32()
  external int pmError;

  @ffi.Int32()
  external int falesafe;

  @ffi.Int32()
  external int iicError;

  @ffi.Int32()
  external int CurFMDT;

  @ffi.Int32()
  external int panelError;

  static ffi.Pointer<ErrorInfo_t> allocate() {
    final pointer = ffi.calloc<ErrorInfo_t>();
    pointer.ref.errorCode = 0;
    pointer.ref.pmError = 0;
    pointer.ref.falesafe = 0;
    pointer.ref.iicError = 0;
    pointer.ref.CurFMDT = FrameMoveDownThreadState_t.FMDT_Not.index;
    pointer.ref.panelError = 0;
    return pointer;
  }
}

final class BwdPanelState_t extends ffi.Struct {
  @ffi.Int32()
  external int IsPop;

  @ffi.Int32()
  external int IsStop;

  @ffi.Int32()
  external int IsOn;

  /// 下糸巻き関係の表示中	X1V1.10M-108
  @ffi.Int32()
  external int IsDisp;

  @Uint8()
  external int BwdSpeed;

  static ffi.Pointer<BwdPanelState_t> allocate() {
    final pointer = ffi.calloc<BwdPanelState_t>();
    pointer.ref.IsPop = 0;
    pointer.ref.IsStop = 0;
    pointer.ref.IsOn = 0;
    pointer.ref.IsDisp = 0;
    return pointer;
  }
}

final class EcoSleepPanelState_t extends ffi.Struct {
  /// T:ScreenSaver
  @ffi.Int32()
  external int isScreenSaver;

  /// T:EcoMode
  @ffi.Int32()
  external int isEco;

  /// T:SleepMode
  @ffi.Int32()
  external int isSleep;

  /// T:isMechaKeySousa
  @ffi.Int32()
  external int isMechaKeySousa;

  static ffi.Pointer<EcoSleepPanelState_t> allocate() {
    final pointer = ffi.calloc<EcoSleepPanelState_t>();
    pointer.ref.isScreenSaver = 0;
    pointer.ref.isEco = 0;
    pointer.ref.isSleep = 0;
    pointer.ref.isMechaKeySousa = 0;

    return pointer;
  }
}

// SSキーの色
enum SSLightingColor_t {
  SS_LIGHT_OFF, //消灯
  SS_LIGHT_RED, //赤色
  SS_LIGHT_GREEN, //緑色
  SS_LIGHT_ORANGE //オレンジ
}

final class BPIFChangeView_t extends ffi.Struct {
  /// //////////////アプリでも利用する/////////////////////
  /// カメラ画像表示状態か
  @ffi.Bool()
  external bool isChangeView;

  /// 刺繍縫製画面用　スクロール量 X方向　mm単位
  @ffi.Int16()
  external int offsetX;

  /// 刺繍縫製画面用　スクロール量 Y方向　mm単位
  @ffi.Int16()
  external int offsetY;

  /// 刺繍縫製画面用　角度量の表示
  @ffi.Int16()
  external int angle;

  /// ズーム状態か
  @ffi.Bool()
  external bool isZoom;

  /// 針落ち点表示が有効か？
  @ffi.Bool()
  external bool isNeedlePosition;

  /// 針落ち点表示が使用できるか？ KeyNeedleViewChange
  @ffi.Int32()
  external int needlePositionState;

  /// Grid表示が使用できるか？ KeyLatticeViewChange
  @ffi.Int32()
  external int viewGridState;

  /// ズームを使用できるか
  @ffi.Int32()
  external int zoomState;

  /// 刺繍縫製画面用　回転が可能か
  @ffi.Int32()
  external int rotateState;

  /// 刺繍縫製画面用　枠移動が可能か
  @ffi.Int32()
  external int moveState;

  static ffi.Pointer<BPIFChangeView_t> allocate() {
    final pointer = ffi.calloc<BPIFChangeView_t>();
    pointer.ref.isChangeView = false;
    pointer.ref.offsetX = 0;
    pointer.ref.offsetY = 0;
    pointer.ref.angle = 0;
    pointer.ref.isZoom = false;
    pointer.ref.isNeedlePosition = false;
    pointer.ref.needlePositionState = 0;
    pointer.ref.isNeedlePosition = false;
    pointer.ref.viewGridState = 0;

    pointer.ref.zoomState = 0;
    pointer.ref.rotateState = 0;
    pointer.ref.moveState = 0;

    return pointer;
  }
}

/// 共通のカテゴリ
final class BPIFGlobalAppInfo_t extends ffi.Struct {
  /// brother_panelの状態
  @ffi.Int32()
  external int mode;

  /// 処理中か？ MotorMoveSignalGet()
  @ffi.Bool()
  external bool isKeyProcessing;

  /// 刺繍機接続状態　 (EmbMachineStateGetFromMemory() == SW_STS_IN)
  @ffi.Bool()
  external bool isConnectedEmbMachine;

  /// 刺繍機停止中
  @ffi.Bool()
  external bool isStopingFrame;

  @ffi.Bool()
  external bool isStopingMotor;

  /// 刺繍枠ロックレバー下か？
  @ffi.Bool()
  external bool isFrameHoldLeverDown;

  /// 糸巻状態
  external ffi.Pointer<BwdPanelState_t> bwdPanelState;

  /// エラー情報
  external ffi.Pointer<ErrorInfo_t> errorInfo;

  /// スリープ関連
  external ffi.Pointer<EcoSleepPanelState_t> ecoSleepPanel;

  /// キーロック状態か？
  @ffi.Bool()
  external bool isKeyLock;

  /// オールロック状態か ？
  @ffi.Bool()
  external bool isAllLock;

  /// カメラ画像表示状態か
  external ffi.Pointer<BPIFChangeView_t> changeViewState;

  /// SS LEDの色
  @ffi.Int32()
  external int ssLightColor;

  ///押さえレバーが下がっている
  @ffi.Bool()
  external bool isPressFootLeverDown;

  //参照XP関数：ActionModeGet()
  @ffi.Int8()
  external int actionMode;

  static ffi.Pointer<BPIFGlobalAppInfo_t> allocate() {
    final pointer = ffi.calloc<BPIFGlobalAppInfo_t>();

    pointer.ref.mode = BaseMode_t.BMODE_MAX.index;
    pointer.ref.isKeyProcessing = false;
    pointer.ref.isConnectedEmbMachine = false;
    pointer.ref.isStopingFrame = true;
    pointer.ref.isStopingMotor = true;
    pointer.ref.isFrameHoldLeverDown = true;

    pointer.ref.errorInfo = ErrorInfo_t.allocate();
    pointer.ref.bwdPanelState = BwdPanelState_t.allocate();
    pointer.ref.ecoSleepPanel = EcoSleepPanelState_t.allocate();
    pointer.ref.changeViewState = BPIFChangeView_t.allocate();

    pointer.ref.ssLightColor = SSLightingColor_t.SS_LIGHT_OFF.index;
    pointer.ref.isPressFootLeverDown = false;
    pointer.ref.actionMode = 0;
    return pointer;
  }

  static deallocate(ffi.Pointer<BPIFGlobalAppInfo_t> pointer) {
    ffi.calloc.free(pointer.ref.errorInfo);
    ffi.calloc.free(pointer.ref.bwdPanelState);
    ffi.calloc.free(pointer.ref.ecoSleepPanel);
    ffi.calloc.free(pointer.ref.changeViewState);
    ffi.calloc.free(pointer);
  }
}

/// Utlカテゴリ
final class BPIFUtlAppDisplayInfoPublicDef_t extends ffi.Struct {
  external ffi.Pointer<BPIFGlobalAppInfo_t> globalAppInfo;
  external ffi.Pointer<UtlManualSetting_t> utlManualSetting;
  external ffi.Pointer<UtlAttribParam_t> utlAttribParam;
  external ffi.Pointer<UtlStateInfo_t> utlFuncSetting;

  static ffi.Pointer<BPIFUtlAppDisplayInfoPublicDef_t> allocate() {
    final pointer = ffi.calloc<BPIFUtlAppDisplayInfoPublicDef_t>();
    pointer.ref.globalAppInfo = BPIFGlobalAppInfo_t.allocate();
    pointer.ref.utlManualSetting = UtlManualSetting_t.allocate();
    pointer.ref.utlAttribParam = UtlAttribParam_t.allocate();
    pointer.ref.utlFuncSetting = UtlStateInfo_t.allocate();
    return pointer;
  }

  static deallocate(ffi.Pointer<BPIFUtlAppDisplayInfoPublicDef_t> pointer) {
    ffi.calloc.free(pointer.ref.globalAppInfo);
    ffi.calloc.free(pointer.ref.utlManualSetting);
    ffi.calloc.free(pointer.ref.utlAttribParam);
    ffi.calloc.free(pointer.ref.utlFuncSetting);
    ffi.calloc.free(pointer);
  }
}

/// Embカテゴリ
final class BPIFEmbAppDisplayInfoPublicDef_t extends ffi.Struct {
  external ffi.Pointer<BPIFGlobalAppInfo_t> globalAppInfo;

  external ffi.Pointer<EmbAttribParam_t> embSetting;

  external ffi.Pointer<EmbEditAttribParam_t> embEditAttrib;

  external ffi.Pointer<EmbSewingAttribParam_t> embSewingAttrib;

  external ffi.Pointer<embSewingInfo_t> sewingInfo;

  static ffi.Pointer<BPIFEmbAppDisplayInfoPublicDef_t> allocate() {
    final pointer = ffi.calloc<BPIFEmbAppDisplayInfoPublicDef_t>();
    pointer.ref.globalAppInfo = BPIFGlobalAppInfo_t.allocate();
    pointer.ref.embSetting = EmbAttribParam_t.allocate();
    pointer.ref.embEditAttrib = EmbEditAttribParam_t.allocate();
    pointer.ref.embSewingAttrib = EmbSewingAttribParam_t.allocate();
    pointer.ref.sewingInfo = embSewingInfo_t.allocate();
    return pointer;
  }

  static deallocate(ffi.Pointer<BPIFEmbAppDisplayInfoPublicDef_t> pointer) {
    ffi.calloc.free(pointer.ref.globalAppInfo);
    ffi.calloc.free(pointer.ref.embSetting);
    ffi.calloc.free(pointer.ref.embEditAttrib);
    ffi.calloc.free(pointer.ref.embSewingAttrib);
    ffi.calloc.free(pointer.ref.sewingInfo);
    ffi.calloc.free(pointer);
  }
}

final class MDCAttribParam_t extends ffi.Struct {
  /// 表示用の枠サイズ
  @ffi.Int32()
  external int dispFrameType;

  /// 装着されている枠サイズ
  @ffi.Int32()
  external int frameSize;

  /// カメラペンUI投影ON/OFF/ONのDisable/OFFのDisable設定値
  @ffi.Int8()
  external int embCameraUiProjectionOnOffState;

  static ffi.Pointer<MDCAttribParam_t> allocate() {
    final pointer = ffi.calloc<MDCAttribParam_t>();
    pointer.ref.dispFrameType = 0;
    pointer.ref.frameSize = 0;
    pointer.ref.embCameraUiProjectionOnOffState = 0;
    return pointer;
  }

  static ffi.Pointer<ffi.Pointer<MDCAttribParam_t>> allocate_p() {
    final pointer = ffi.calloc<ffi.Pointer<MDCAttribParam_t>>();
    return pointer;
  }
}

final class MDCAppDisplayInfoPublicDef_t extends ffi.Struct {
  external MDCAttribParam_t mdcAttribParam;
}

final class BPIFMDCAppDisplayInfoPublicDef_t extends ffi.Struct {
  external ffi.Pointer<BPIFGlobalAppInfo_t> globalAppInfo;

  external ffi.Pointer<MDCAttribParam_t> mdcAttribParam;
  static ffi.Pointer<BPIFMDCAppDisplayInfoPublicDef_t> allocate() {
    final pointer = ffi.calloc<BPIFMDCAppDisplayInfoPublicDef_t>();
    pointer.ref.globalAppInfo = BPIFGlobalAppInfo_t.allocate();
    pointer.ref.mdcAttribParam = MDCAttribParam_t.allocate();
    return pointer;
  }

  static deallocate(ffi.Pointer<BPIFMDCAppDisplayInfoPublicDef_t> pointer) {
    ffi.calloc.free(pointer.ref.globalAppInfo);
    ffi.calloc.free(pointer.ref.mdcAttribParam);
    ffi.calloc.free(pointer);
  }
}

abstract class ActionMode_t {
  /// 縫製中
  static const int SEWING = 0;

  /// 糸切り中
  static const int THREAD_CUTTING = 1;

  /// 停止処理中
  static const int BEFORE_STOP = 2;

  /// フットコントローラ停止処理中
  static const int BEFORE_STOP_FOR_FOT = 3;

  /// 始めの自動止め／返し
  static const int START_AUTO_REVSTOP_SEW = 4;

  /// 終わりの自動止め／返し
  static const int END_AUTO_REVSTOP_SEW = 5;

  /// 糸巻き中
  static const int WINDING_BOBBIN = 6;

  /// 再起動前
  static const int BEFORE_RESTART = 7;

  /// 糸通し中
  static const int THREADING = 8;

  /// 縫製前処理
  static const int BEFORE_SEWING = 9;

  /// 押え上下処理
  static const int PFT_UP_DOWN_MOVE = 10;

  /// 糸通しレバーで糸通し中 THPM03-00-11
  static const int THREADING_LEVER = 11;

  /// 動作中モードの数
  static const int MOVESTATE = 12;

  /// 停止中
  static const int STOPPING = 12;

  /// パルスモータ動作中
  static const int CHECKER_PM_DRIVING = 13;
  static const int FEED_UP_DOWN_MOVE = 14;

  /// ミシンに送信中
  static const int TRANSMIT_TO_MACHINE = 15;

  /// ミシンから受信中
  static const int RECEIVE_FROM_MACHINE = 16;

  /// アクションモード要求無し
  static const int NON_ACTION_MODE_REQ = 17;
}

enum DeviceErrorCode_t {
  devNoError,		// エラーなし
  devParamError, //パラメータエラー
  devInvalidError,//使用できない関数
  devInvalidPanelError,//エラーをパネルで処理をする
  devInternalError,//システムに問題が発生
  devEEPWriteError,		// EEP書き込みエラー
}

enum DirErrorCode_t {
  dirTransitionOK, //ポップアップ確認が不要 遷移可能
  dirRequiresConfirmation, //ポップアップ確認が必要 遷移可能
  dirInvalidError,//無効音　遷移不可
  dirInvalidPanelError, //エラー発生　遷移不可
  dirMotorError,//モータ駆動中
  dirNoError,//エラーなし
}

enum BPIFSendError_t {
  bpifNoError, // エラーなし
  bpifParamError, //パラメータエラー
  bpifInvalidError, //使用できない関数
  bpifInvalidPanelError, //エラーをパネルで処理をする
  bpifInternalError, // システムに問題が発生
  bpifCheckKeyResult, // キー関数の処理結果を確認
}

enum FaleSafeKind_t {
  FALE_SAFE_NON							/*	フェイルセーフ無し					*/
  , FALE_SAFE_KIND_ABNORMAL_SPEED			/*	回転異常							*/
  , FALE_SAFE_KIND_SS_KEY_PUSH_POWER_ON		/*	電源ＯＮ時、Ｓ／Ｓ	キー押しつづけ（動作系）*/
  , FALE_SAFE_KIND_NP_KEY_PUSH_POWER_ON		/*	電源ＯＮ時、ＮＰ	キー押しつづけ（動作系）*/
  , FALE_SAFE_KIND_REV_KEY_PUSH_POWER_ON	/*	電源ＯＮ時、ＲＥＶ	キー押しつづけ（動作系）*/
  , FALE_SAFE_KIND_TC_KEY_PUSH_POWER_ON		/*	電源ＯＮ時、ＴＣ	キー押しつづけ（動作系）*/
  , FALE_SAFE_KIND_PFT_KEY_PUSH_POWER_ON	/*	電源ＯＮ時、ＰＦＴ	キー押しつづけ（動作系）*/
  , FALE_SAFE_KIND_TH_KEY_PUSH_POWER_ON		/*	電源ＯＮ時、ＴＨ	キー押しつづけ（動作系）*/
  //2012-06-08 ZZS620 VcombL M-70 ins start
  , FALE_SAFE_KIND_LOCK_KEY_PUSH_POWER_ON	/*  電源ON時、LOCKキー押し続け(動作系) */
  //2012-06-08 ZZS620 VcombL M-70 ins end
  , FALE_SAFE_KIND_KEY_PUSH_POWER_ON_SELECT /*	電源ＯＮ時のキー押しつづけ（操作系）*/
  , FALE_SAFE_KIND_FOT_DISCONECT			/*	ＦＯＴ断線							*/
  , FALE_SAFE_KIND_SPEED_SLIT_STAIN			/*	スピードセンサ汚れ					*/
  , FALE_SAFE_KIND_NP_DISCONECT				/*	ＮＰ断線							*/
  , FALE_SAFE_KIND_SPEED_VR_DISCONECT		/*	スピードＶＲ断線					*/
  , FALE_SAFE_KIND_CAMERA_MODULE_DISCONECT	/*	カメラモジュール断線				*/
  , FALE_SAFE_KIND_AD_CONVERTER_MALFUNCTION_SVV	/*	ADC機能不全(Speed Volume value)			*/
  , FALE_SAFE_KIND_AD_CONVERTER_MALFUNCTION_FCV	/*	ADC機能不全(Foot Controller value)		*/
  , FALE_SAFE_KIND_AD_CONVERTER_MALFUNCTION_FCC	/*	ADC機能不全(Foot Controller conection)	*/
  , FALE_SAFE_KIND_AD_CONVERTER_MALFUNCTION_FCM	/*	ADC機能不全(Foot Controller Mode change)*/
  , FALE_SAFE_KIND_AD_CONVERTER_MALFUNCTION_TSK	/*	ADC機能不全(ADC Task)					*/
  , FALE_SAFE_KIND_SPVR_DEVICE_OPNE_ERR			/*	SPVRデバイスオープンエラー				*/
  , FALE_SAFE_KIND_SPVR_DEVICE_I2C_CON_ERR		/*	SPVRI2Cエラー							*/
  , FALE_SAFE_KIND_PROJECTOR_DEVICE_OPNE_ERR		/*	プロジェクターデバイスオープンエラー	*/
  , FALE_SAFE_KIND_PROJECTOR_DEVICE_I2C_CON_ERR	/*	プロジェクターI2Cエラー					*/
  , FALE_SAFE_KIND_RTC_DEVICE_OPNE_ERR			/*	RTCデバイスオープンエラー				*/
  , FALE_SAFE_KIND_RTC_DEVICE_I2C_CON_ERR			/*	RTCI2Cエラー							*/
  , FALE_SAFE_KIND_CAMERA_CONNECT_ERR				/*	カメラ接続エラー						*/
  , FALE_SAFE_KIND_CAMERA_I2C_ERR					/*	カメラI2Cエラー							*/
  , FALE_SAFE_KIND_CAMERA_MIPI_ERR				/*	カメラMIPIエラー						*/
  , FALE_SAFE_KIND_MAIN_VERSION					/*	非対応のMainファームを利用				*/
  , FALE_SAFE_KIND_MAX
}

enum ErrAct_t {
  ERROR_ACT_NON,
  ERROR_ACT_KEY,
  ERROR_ACT_MACHINE,
  ERROR_ACT_EDGE
  //, ERROR_ACT_ANGLE
  ,
  ERROR_ACT_TIME,
  ERROR_ACT_SS,
  ERROR_ACT_MAC_SS,
  ERROR_ACT_NON_WITH_PUSH,
  ERROR_ACT_ALL_LOCK,
  ERROR_ACT_MAX
}

//I2Cエラー
const int IIC_SENSOR_UP_UNIT = 1;

const int IIC_SENSOR_EMB_INITIAL_UNIT = 2;

const int IIC_SENSOR_EMB_FRAME_UNIT = 3;

const int IIC_EXP_DF_UNIT = 4;

const int IIC_EXP_LEDPT_UNIT = 5;

const int IIC_EXP_SR_UNIT = 6; //PHFIRMIIVO-6891

const int EXP_CURRENT = 7; //PHFIRMIIVO-6891

//PMエラー

const int PFPM_ERROR = 0;

const int THPM_ERROR = 1;

const int DPM_ERROR = 2;

const int TCPM_ERROR = 3;

const int ZPM_ERROR = 4;

const int FPM_ERROR = 5;

const int SPM_ERROR = 6;

const int ATPM_ERROR = 7;

const int XPM_ERROR = 8;

const int YPM_ERROR = 9;

const int LMPM_ERROR = 10;

const int XY_CPU1 = 20;

const int XY_CPU2 = 21;

const int XY_CPU3 = 22;

const int XY_CPU4 = 23;

const int XY_CPU5 = 24;

const int XY_CPU6 = 25;

const int ERR_OV_VDC_X = 26;

const int ERR_OV_VDC_Y = 27;

const int XY_CPU_X_Z_ENCODER_CHK_Z_ERROR = 28;

const int XY_CPU_Y_Z_ENCODER_CHK_Z_ERROR = 29;

const int XY_CPU_X_Z_ENCODER_CHK_ENC_ERROR = 30;

const int XY_CPU_Y_Z_ENCODER_CHK_ENC_ERROR = 31;

const int X_MOVE = 32;

const int Y_MOVE = 33;

const int XY_CPU_SERVO = 34;

const int XY_CPU_EEP_FCT = 35;

const int XY_CPU_EEP_DIF = 36;

const int XY_CPU_EEP_OFS = 37;

const int XY_CPU_ZDIF_X = 38;

const int XY_CPU_ZDIF_Y = 39;

const int XY_CPU_ZDIF_EEP_X = 41;

const int XY_CPU_ZDIF_EEP_Y = 42;

const int XY_CPU_Z_OFS_X = 43;

const int XY_CPU_Z_OFS_Y = 44;

const int FAIL_RPM_X = 45;

const int FAIL_RPM_Y = 46;

const int ERR_UV_VDC_X = 47;

const int ERR_UV_VDC_Y = 48;

const int ERR_OC_IU_X = 49;

const int ERR_OC_IU_Y = 50;

const int ERR_OC_IV_X = 51;

const int ERR_OC_IV_Y = 52;

const int ERR_OC_IW_X = 53;

const int ERR_OC_IW_Y = 54;

const int ERR_HW_OC_X = 55;

const int ERR_HW_OC_Y = 56;

const int ERR_CTRL_SPEED_X = 57;

const int ERR_CTRL_SPEED_Y = 58;

const int ERR_CTRL_THETA_X = 59;

const int ERR_CTRL_THETA_Y = 60;

const int ERR_OS_SPEED_X = 61;

const int ERR_OS_SPEED_Y = 62;

const int ERR_OTR_OS_SPEED_X = 63;

const int ERR_OTR_OS_SPEED_Y = 64;

const int ERR_OTR_CTRL_SPEED_X = 65;

const int ERR_OTR_CTRL_SPEED_Y = 66;

const int ERR_OH_MOT_X = 67;

const int ERR_OH_MOT_Y = 68;

const int ERR_ENC_OFFSET_EEP_X = 73;

const int ERR_ENC_OFFSET_EEP_Y = 74;

const int ERR_OC_IQ_X = 75;

const int ERR_OC_IQ_Y = 76;

const int OVC_PJ = 40;

const int XY_CPU_UNDEF = 100;

const int XYCPU_X_Z_ENCODER_CHK_COMP = 224;

const int XYCPU_Y_Z_ENCODER_CHK_COMP = 225;

const int XYCPU_EEPROM_WRITE_FINISHED = 226;

const int NO_ERROR = 255;
