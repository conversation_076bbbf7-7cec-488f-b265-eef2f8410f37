import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../../model/frame_model.dart';
import '../../../../../../../../../model/preview_area_size_model.dart';
import '../../../../../../model/key_board_font_model.dart';
import '../../../../../../model/pattern_model.dart';
import '../../../../../../model/preview_model.dart';
import '../../../../../../model/scan_model.dart';
import 'background_view_interface.dart';

typedef PatternViewDisplayInfo = PatternDisplayInfo;
typedef EmbBorderViewDisplayInfo = EmbBorderDisplayInfo;
typedef EmbGroupViewDisplayInfo = EmbGroupDisplayInfo;
typedef EmbPatternViewDisplayInfo = EmbPatternDisplayInfo;

/// 10mm定義
const int _gridLine10MmValue = 10;

/// 25mm定義
const int _gridLine25MmValue = 25;

/// フレームの破線の一段の長さ
const double _dashedLineLength = 2.0;

/// フレームの破線の間隔
const double _dashedLineSpace = 2.0;

/// 黒点行列の間隔
const double _blackPointsSpace = 10.0;

/// 1mmに対応する画素数
double _pixelOfOneMm = charInputPreviewSize.dx / frame297x465MmSize.dx;

/// プレビュー中心点
final Offset _originalCenter = charInputPreviewSize / 2;

/// 背景画像の不透明度
const double backgroundOpacity = 0.3725490196078431;

final previewBackgroundViewModelProvider = StateNotifierProvider.autoDispose<
    PreviewBackgroundViewModelInterface, PreviewBackgroundState>(
  (ref) => PreviewBackgroundViewModel(ref),
);

class PreviewBackgroundViewModel extends PreviewBackgroundViewModelInterface {
  PreviewBackgroundViewModel(Ref ref)
      : super(
          PreviewBackgroundState(
            otherPatternDisplayInfoList: [],
            backgroundColor: Colors.transparent,
            gridTypeIndex: 0,
            gridColor: Colors.transparent,
            frameDrawPath: Path(),
            frameColor: Colors.transparent,
            blackPoints: [],
            backgroundImage: null,
            isFirstInput: true,
          ),
          ref,
        ) {
    update();
  }

  @override
  void update() {
    /// view更新
    state = state.copyWith(
      otherPatternDisplayInfoList: _getOtherPatternDisplayInfoList(),
      backgroundColor: PreviewModel().getEmbroideryBackgroundColor(),
      gridTypeIndex: DeviceLibrary().apiBinding.getGrid().gridType.index,
      frameDrawPath: _getFrameDrawPath(),
      gridColor: PreviewModel().getEmbroideryGridColor(),
      frameColor: PreviewModel().getEmbroideryFrameColor(),
      blackPoints: _getBlackPoints(),
      backgroundImage: _getScanBackGroundImage(),
      isFirstInput: KeyBoardFontModel().isReEditMode == false,
    );
  }

  @override
  Offset get getPreviewSize => charInputPreviewSize;

  @override
  double get getMagnification =>
      PatternModel().selectedZoomScaleInSelectPage / zoomList.first;

  @override
  List<double> getGridVerticalList() {
    List<double> verticalList = [];
    double xOffset = _originalCenter.dx;
    int mmValue = state.gridTypeIndex == EmbGridType.embGridGridLine10.index
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    /// 中心点の左端の線の位置を計算するには
    xOffset -= _pixelOfOneMm * mmValue;
    while (xOffset >= 0) {
      verticalList.add(xOffset);
      xOffset -= _pixelOfOneMm * mmValue;
    }

    xOffset = _originalCenter.dx;

    /// 中心点の右端の線の位置を計算するには
    xOffset += _pixelOfOneMm * mmValue;
    while (xOffset <= charInputPreviewSize.dx) {
      verticalList.add(xOffset);
      xOffset += _pixelOfOneMm * mmValue;
    }

    return verticalList;
  }

  @override
  List<double> getGridHorizontalList() {
    List<double> horizontalList = [];
    double yOffset = _originalCenter.dy;
    int mmValue = state.gridTypeIndex == EmbGridType.embGridGridLine10.index
        ? _gridLine10MmValue
        : _gridLine25MmValue;

    /// 中心点上端の線の位置を計算するには
    yOffset -= _pixelOfOneMm * mmValue;
    while (yOffset >= 0) {
      horizontalList.add(yOffset);
      yOffset -= _pixelOfOneMm * mmValue;
    }

    yOffset = _originalCenter.dy;

    /// 中心点の下端の線の位置を計算するには
    yOffset += _pixelOfOneMm * mmValue;
    while (yOffset <= charInputPreviewSize.dy) {
      horizontalList.add(yOffset);
      yOffset += _pixelOfOneMm * mmValue;
    }

    return horizontalList;
  }

  ///
  /// 選択した枠のプレビュー表示データを取得します
  ///
  Path _getFrameDrawPath() {
    List<FrameSizeAndArea>? frameSizeAndAreaList = getFrameDisplaySizeAndArea(
        DeviceLibrary().apiBinding.getEmbroideryFrameDisplay().frameDispType);
    assert(frameSizeAndAreaList != null, "対応するサイズのボックスが見つかりません");

    Path drawPath = Path();
    Path basePath = Path();
    double magnification = getMagnification;

    if (frameSizeAndAreaList!.length > 1) {
      basePath =
          _get60x20FrameBasePath(frameSizeAndAreaList: frameSizeAndAreaList);
    } else {
      double width =
          frameSizeAndAreaList.first.width * _pixelOfOneMm / conversionRate;
      double height =
          frameSizeAndAreaList.first.height * _pixelOfOneMm / conversionRate;
      double startX =
          frameSizeAndAreaList.first.left * _pixelOfOneMm / conversionRate +
              _originalCenter.dx;
      double startY =
          frameSizeAndAreaList.first.top * _pixelOfOneMm / conversionRate +
              _originalCenter.dy;

      Rect rect = Rect.fromLTWH(startX, startY, width, height);
      basePath.moveTo(rect.left, rect.top);
      basePath.lineTo(rect.left, rect.bottom);
      basePath.lineTo(rect.right, rect.bottom);
      basePath.lineTo(rect.right, rect.top);
      basePath.lineTo(rect.left, rect.top);
    }

    /// フレームの破線の一段の長さ
    double dashWidth = _dashedLineLength / magnification;
    double dashSpace = _dashedLineSpace / magnification;

    /// 描画Pathを計算する
    double distance = 0.0;
    for (PathMetric pathMetric in basePath.computeMetrics()) {
      while (distance < pathMetric.length) {
        drawPath.addPath(
          pathMetric.extractPath(distance, distance + dashWidth),
          Offset.zero,
        );
        distance += dashWidth;
        distance += dashSpace;
      }
    }
    return drawPath;
  }

  ///
  /// 60*20枠のプレビューにはデータが表示されま
  ///
  Path _get60x20FrameBasePath(
      {required List<FrameSizeAndArea> frameSizeAndAreaList}) {
    Path basePath = Path();

    /// List順：60*20 mm、50*30 mm、30*40 mm
    List<Rect> rectList = List.generate(
      frameSizeAndAreaList.length,
      (index) => Rect.fromLTWH(
          frameSizeAndAreaList[index].left * _pixelOfOneMm / conversionRate +
              _originalCenter.dx,
          frameSizeAndAreaList[index].top * _pixelOfOneMm / conversionRate +
              _originalCenter.dy,
          frameSizeAndAreaList[index].width * _pixelOfOneMm / conversionRate,
          frameSizeAndAreaList[index].height * _pixelOfOneMm / conversionRate),
    );

    /// 1番目と2番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*40 mm
    rectList.insert(
      1,
      Rect.fromPoints(
        Offset(rectList[1].left, rectList.first.top),
        Offset(rectList[1].right, rectList.first.bottom),
      ),
    );

    /// 2番目と3番目のRectを重ねて作成したRectを挿入します。
    /// List順：60*20 mm、50*20 mm、50*30 mm、30*30 mm、30*40 mm
    rectList.insert(
      3,
      Rect.fromPoints(
        Offset(rectList.last.left, rectList[2].top),
        Offset(rectList.last.right, rectList[2].bottom),
      ),
    );

    /// 60*20 mmの左上点から描画
    basePath.moveTo(rectList.first.left, rectList.first.top);

    /// Rectの左上点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].left, rectList[index].top);
    }

    /// Rectの右上点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].right, rectList[index].top);
    }

    /// Rectの右下点を順次描画する
    for (int index = 0; index < rectList.length; index++) {
      basePath.lineTo(rectList[index].right, rectList[index].bottom);
    }

    /// Rectの左下点を尾から頭に向かって順次描画する
    for (int index = rectList.length - 1; index >= 0; index--) {
      basePath.lineTo(rectList[index].left, rectList[index].bottom);
    }

    /// 接続開始点
    basePath.lineTo(rectList.first.left, rectList.first.top);

    return basePath;
  }

  ///
  /// 黒点行列の表示データを取得します
  ///
  List<Offset> _getBlackPoints() {
    double space = _blackPointsSpace / getMagnification;
    List<Offset> points = [];

    /// 黒点行列は以下の場合には表示しない
    if (PatternModel().selectedZoomScaleInSelectPage == zoomList.first ||
        [
          EmbGridType.embGridCenterLine,
          EmbGridType.embGridGridLine10,
          EmbGridType.embGridGridLine_25
        ].contains(DeviceLibrary().apiBinding.getGrid())) {
      return points;
    } else {
      for (double y = space; y < charInputPreviewSize.dy; y += space) {
        for (double x = space; x < charInputPreviewSize.dx; x += space) {
          points.add(Offset(x, y));
        }
      }
    }

    return points;
  }

  ///
  /// Viewは最初にPattern情報変換を使用する
  ///
  List<PatternDisplayInfo> _getOtherPatternDisplayInfoList() {
    final List<PatternDisplayInfo> patternDisplayInfoList = [];

    final List<Pattern> patternList = PatternModel().getAllPattern();
    if (patternList.isEmpty) {
      return [];
    }

    /// カレントグループのハンドル
    final currentGroupHandle = PatternModel().getCurrentGroupHandle();

    for (int sewingIndex = 0; sewingIndex < patternList.length; sewingIndex++) {
      Pattern pattern = patternList[sewingIndex];

      if (pattern is EmbGroup) {
        if (KeyBoardFontModel().isReEditMode == true &&
            pattern.handle == currentGroupHandle) {
          /// do nothing
        } else {
          patternDisplayInfoList.add(PreviewModel().getGroupPatternDisplayInfo(
            scrollType: ScrollCenterType.IMAGE_SELECT_FONT,
            group: pattern,
            centerPoint: _originalCenter,
            pixelOfOneMm: _pixelOfOneMm,
            sewingIndex: sewingIndex,
            currentGroupHandle: currentGroupHandle,
            zoomScale: PatternModel().selectedZoomScaleInSelectPage,
          ));
        }
      } else if (pattern is EmbBorder) {
        patternDisplayInfoList.add(PreviewModel().getBorderPatternDisplayInfo(
          scrollType: ScrollCenterType.IMAGE_SELECT_FONT,
          border: pattern,
          centerPoint: _originalCenter,
          pixelOfOneMm: _pixelOfOneMm,
          sewingIndex: sewingIndex,
          currentGroupHandle: currentGroupHandle,
          zoomScale: PatternModel().selectedZoomScaleInSelectPage,
        ));
      } else {
        /// Do noting
      }
    }

    return patternDisplayInfoList;
  }

  ///
  /// スキャンした背景画像を取得する
  ///
  Widget? _getScanBackGroundImage() {
    if (ScanModel().hasBackgroundImage == false ||
        ScanModel().getBackgroundShowStatus() == false) {
      return null;
    }

    if (ScanModel.createSketchesImage() == null) {
      return null;
    }

    return Opacity(
      opacity: ScanModel().backgroundDensityValue,
      child: Image.memory(
        ScanModel.createSketchesImage()!,
        scale: embPreviewSizeDot.dx / charInputPreviewSize.dx,
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    KeyBoardFontModel().reset();
  }
}
