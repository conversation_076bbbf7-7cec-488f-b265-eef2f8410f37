import 'dart:async';
import 'dart:ffi' as dart_ffi;
import 'dart:ffi';
import 'dart:io';
import 'dart:typed_data';

import 'package:ffi/ffi.dart' as ffi;
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:memory/memory_interface.dart';
import 'package:path/path.dart';
import 'package:ph_eel_plugin/tpd_bindings.dart' show getTpDgbBindings;
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';
import 'package:system_config/system_config.dart';

import '../../../../../panel_library.dart';
import '../../interface/api_interface.dart';
import '../../interface/test_api_interface.dart';

final TpDgbBindings _bindings = getTpDgbBindings();

class TpdLibraryMockImplementAndroid implements TpdLibraryAPIInterface {
  void init(Ref ref) {}

  final Pointer<ErrorInfo_t> _tmpError = ErrorInfo_t.allocate();
  @override
  BPIFError bpIFGetError() {
    // TpdLibraryLog.d("BPIFGetError start");
    _bindings.BPIFGetError(_tmpError);

    if (TpdLibraryTestApi().testBpifError != null) {
      final BPIFError bpifError = TpdLibraryTestApi().testBpifError!;
      return bpifError;
    }

    final BPIFError bpifError = BPIFError(
      errorCode: _tmpError.ref.errorCode,
      falesafe: FaleSafeKindExtension.formFFIApiIndex(_tmpError.ref.falesafe),
      iicError: _tmpError.ref.iicError,
      pmError: _tmpError.ref.pmError,
    );
    // TpdLibraryLog.d("BPIFGetError finish,error:$bpifError");
    return bpifError;
  }

  @override
  BpIfInitError bpIFInit() {
    Pointer<Char> charPath = ffi.calloc<Char>(255);

    String strFolderPath = join(getRootPath(), 'alldata');
    final strCode = strFolderPath.runes.toList();

    int i = 0;
    for (i = 0; i < strCode.length; i++) {
      charPath[i] = strCode[i];
    }
    charPath[i] = 0;
    TpdLibraryLog.hello("bpIFInit");
    final errIndex = _bindings.BPIFInit(charPath);
    final error = BpIfInitError.formFFIApiIndex(errIndex);
    ffi.calloc.free(charPath);
    TpdLibraryLog.byeBye("bpIFInit");
    return error;
  }

  @override
  int bpIFSendDisplayData(BPIFSendKey messageId) {
    TpdLibraryLog.d(
        "bpIFSendDisplayData start, send_msg_id:${messageId.index},name:${messageId.name}");
    final ret = _bindings.BPIFSendDisplayData(messageId.index);
    TpdLibraryLog.d("bpIFSendDisplayData finish,ret:$ret");
    return ret;
  }

  @override
  int bpIFSendDisplayDataSync(BPIFSendKey messageId) {
    TpdLibraryLog.d(
        "bpIFSendDisplayDataSync start, send_msg_id:${messageId.index},name:${messageId.name}");
    final ret = _bindings.BPIFSendDisplayDataSync(messageId.index);
    TpdLibraryLog.d("bpIFSendDisplayDataSync finish  $ret");
    return ret;
  }

  @override
  BeepRequest bpIFGetBeep() {
    if (getPollingLogFlagForDebug() == true) {
      TpdLibraryLog.d("bpIFGetBeep start");
    }

    if (isAndroidSimApp()) {
      return BeepRequest_t.BEEP_NON;
    }

    final beepIndex = _bindings.BPIFGetBeep();
    final beepRequest = BeepRequestExtension.getValuesByIndex(beepIndex);

    if (getPollingLogFlagForDebug() == true) {
      TpdLibraryLog.d("bpIFGetBeep finish,beepRequest:$beepRequest");
    }

    return beepRequest;
  }

  final _bpIFGlobalAppInfo = BPIFGlobalAppInfo_t.allocate();
  @override
  BPIFGlobalAppInfo bpIFGetAppDisplayGlobal() {
    _bindings.BPIFGetAppDisplay_global(_bpIFGlobalAppInfo);

    return _bpIFGlobalAppInfo.ref;
  }

  final bpIFUtlAppDisplayInfo = BPIFUtlAppDisplayInfoPublicDef_t.allocate();
  @override
  BPIFUtlAppDisplayInfoPublicDef bpIFGetAppDisplayUtl() {
    if (UtlLibrary().apiBinding.isUtlModeOpened()) {
      _bindings.BPIFGetAppDisplay_utl(bpIFUtlAppDisplayInfo);
    } else {
      TpdLibraryLog.errorTrace("an unexpected error is occurred");
    }

    return bpIFUtlAppDisplayInfo.ref;
  }

  ///
  /// Home画面から遷移する
  ///
  @override
  DeviceErrorCode gotoUtlFromHome() {
    TpdLibraryLog.hello("gotoUtlFromHome");
    final errIndex = _bindings.gotoUtlFromHome();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoUtlFromHome | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoClockSetting() {
    TpdLibraryLog.hello("gotoClockSetting");
    final errIndex = _bindings.gotoClockSetting();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoClockSetting | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode returnClockSetting() {
    TpdLibraryLog.hello("returnClockSetting");
    int error = _bindings.returnClockSetting();
    TpdLibraryLog.byeBye("returnClockSetting");
    return DeviceErrorCodeExtension.getValuesByIndex(error);
  }

  @override
  DeviceErrorCode gotoDisneyFromHome() {
    TpdLibraryLog.hello("gotoDisneyFromHome");
    final errIndex = _bindings.gotoDisneyFromHome();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoDisneyFromHome | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoEmbFromHome() {
    TpdLibraryLog.hello("gotoEmbFromHome");
    final errIndex = _bindings.gotoEmbFromHome();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoEmbFromHome | errCode:$errCode");
    return errCode;
  }

  final _bPIFGetAppDisplayMdc = BPIFMDCAppDisplayInfoPublicDef.allocate();
  @override
  BPIFMDCAppDisplayInfoPublicDef bPIFGetAppDisplayMdc() {
    _bindings.BPIFGetAppDisplay_mdc(_bPIFGetAppDisplayMdc);
    return _bPIFGetAppDisplayMdc.ref;
  }

  @override
  DeviceErrorCode gotoHowToUse() {
    TpdLibraryLog.hello("gotoHowToUse");
    final errIndex = _bindings.gotoHowToUse();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoHowToUse | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode returnHowToUse() {
    TpdLibraryLog.hello("returnHowToUse");
    final errIndex = _bindings.returnHowToUse();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("returnHowToUse | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoMDCFromHome() {
    TpdLibraryLog.hello("gotoMDCFromHome");
    final errIndex = _bindings.gotoMDCFromHome();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoMDCFromHome | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoSettings() {
    TpdLibraryLog.hello("gotoSettings");
    final ret = _bindings.gotoSettings();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(ret);
    TpdLibraryLog.byeBye("gotoSettings | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoWlanSetting() {
    TpdLibraryLog.hello("gotoWlanSetting");
    final errIndex = _bindings.gotoWlanSetting();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoWlanSetting | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode startEco() {
    TpdLibraryLog.hello("startEco");
    final errIndex = _bindings.startEco();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);

    /// isEcoをtrueまでに待ちます
    while (
        bpIFGetAppDisplayGlobal().ecoSleepPanel.ref.isEco == ENUM_LIB.FALSE) {}

    TpdLibraryLog.byeBye(
        "startEco,isEco:${bpIFGetAppDisplayGlobal().ecoSleepPanel.ref.isEco}");
    return errCode;
  }

  @override
  DeviceErrorCode stopEco() {
    TpdLibraryLog.hello("stopEco");
    final errIndex = _bindings.stopEco();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceErrorCodeExtension.logWhenDeviceError(errIndex);

    /// isEcoをfalseまでに待ちます
    while (
        bpIFGetAppDisplayGlobal().ecoSleepPanel.ref.isEco == ENUM_LIB.TRUE) {}
    TpdLibraryLog.byeBye(
        "stopEco,isEco:${bpIFGetAppDisplayGlobal().ecoSleepPanel.ref.isEco}");
    return errCode;
  }

  @override
  DeviceErrorCode startScreenSaver() {
    TpdLibraryLog.hello("startScreenSaver");
    final errIndex = _bindings.startScreenSaver();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceErrorCodeExtension.logWhenDeviceError(errIndex);

    /// isScreenSaverをfalseまでに待ちます
    while (bpIFGetAppDisplayGlobal().ecoSleepPanel.ref.isScreenSaver ==
        ENUM_LIB.FALSE) {}
    TpdLibraryLog.byeBye(
        "startScreenSaver,isScreenSaver:${bpIFGetAppDisplayGlobal().ecoSleepPanel.ref.isScreenSaver}");
    return errCode;
  }

  @override
  DeviceErrorCode stopScreenSaver() {
    TpdLibraryLog.hello("stopScreenSaver");
    final errIndex = _bindings.stopScreenSaver();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceErrorCodeExtension.logWhenDeviceError(errIndex);

    /// isScreenSaverをfalseまでに待ちます
    while (bpIFGetAppDisplayGlobal().ecoSleepPanel.ref.isScreenSaver ==
        ENUM_LIB.TRUE) {}
    TpdLibraryLog.byeBye(
        "stopScreenSaver,isScreenSaver:${bpIFGetAppDisplayGlobal().ecoSleepPanel.ref.isScreenSaver}");
    return errCode;
  }

  @override
  DeviceErrorCode startSleep() {
    TpdLibraryLog.hello("startSleep");
    final errIndex = _bindings.startSleep();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceErrorCodeExtension.logWhenDeviceError(errIndex);
    TpdLibraryLog.byeBye("startSleep");
    return errCode;
  }

  @override
  DeviceErrorCode stopSleep() {
    TpdLibraryLog.hello("stopSleep");
    final errIndex = _bindings.stopSleep();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    DeviceErrorCodeExtension.logWhenDeviceError(errIndex);
    TpdLibraryLog.byeBye("stopSleep");
    return errCode;
  }

  @override
  bool hasMechaKeySousaBetweenLastAndCurrentCheck() {
    if (isAndroidSimApp()) {
      // この API は Android エミュレータ (x86) では一時的に利用できません
      return false;
    }
    bool result =
        _bindings.hasMechaKeySousaBetweenLastAndCurrentCheck() == ENUM_LIB.TRUE;
    return result;
  }

  @override
  bool isInShutOffModel() {
    final value =
        bpIFGetAppDisplayGlobal().ecoSleepPanel.ref.isSleep == ENUM_LIB.FALSE
            ? false
            : true;
    return value;
  }

  @override
  bool requestExitEcoModel() {
    final value =
        bpIFGetAppDisplayGlobal().ecoSleepPanel.ref.isEco == ENUM_LIB.FALSE
            ? false
            : true;
    return !value;
  }

  @override
  bool requestExitScreenSaver() {
    final value = bpIFGetAppDisplayGlobal().ecoSleepPanel.ref.isScreenSaver ==
            ENUM_LIB.FALSE
        ? false
        : true;
    return !value;
  }

  @override
  BwdPanelState getBwdState() {
    final value = bpIFGetAppDisplayGlobal().bwdPanelState.ref;
    return BwdPanelState(
      isPop: value.IsPop == ENUM_LIB.FALSE ? false : true,
      isStop: value.IsStop == ENUM_LIB.FALSE ? false : true,
      isOn: value.IsOn == ENUM_LIB.FALSE ? false : true,
      isDisp: value.IsDisp == ENUM_LIB.FALSE ? false : true,
      speed: value.BwdSpeed,
    );
  }

  @override
  BaseMode_t getBaseMode() {
    TpdLibraryLog.hello("getBaseMode");
    int valueIndex = bpIFGetAppDisplayGlobal().mode;
    if (valueIndex < 0 || valueIndex >= BaseMode_t.values.length) {
      TpdLibraryLog.e("invalid value");
      valueIndex = BaseMode_t.BMODE_HOME.index;
    } else {
      /// Do Nothing
    }
    final baseMode = BaseMode_t.values[valueIndex];
    TpdLibraryLog.byeBye("getBaseMode,$baseMode");
    return baseMode;
  }

  @override
  bool isNotFrameMoveDownThreadState() => true;

  @override
  bool isPulseMotorDriving() => false;

  @override
  InitState getInitState() {
    int index = _bindings.getInitState();

    if (TpdLibraryTestApi().testInitState != null) {
      final InitState initState = TpdLibraryTestApi().testInitState!;
      return initState;
    } else if (isAndroidSimApp() || Platform.isWindows) {
      return InitState.INITSTATE_NORMAL_START;
    }

    return InitStateExtension.formFFIApiIndex(index);
  }

  @override
  SSLightingColor getSSLedColor() {
    if (getPollingLogFlagForDebug() == true) {
      TpdLibraryLog.hello("getSSLedColor");
    }
    final int ssLightColorIndex = bpIFGetAppDisplayGlobal().ssLightColor;
    SSLightingColor color =
        SSLedColorExtension.formFFIApiIndex(ssLightColorIndex);

    if (getPollingLogFlagForDebug() == true) {
      TpdLibraryLog.byeBye("getSSLedColor,$color");
    }
    return color;
  }

  ///
  /// headerのSVGはUnit8Listへ変換
  ///
  @override
  Future<void> changHeaderSvgToByte() async {
    for (var svg in HeaderFooterSvgInfo.headerSvgInfoList) {
      String svgPath = svg.path;
      try {
        ByteData svgByteDate = await rootBundle.load(svgPath);
        svg.bytes = svgByteDate.buffer.asUint8List();
      } catch (e) {
        TpdLibraryLog.e("cant not fined svg: $svgPath");
      }
    }
  }

  ///
  /// headerのSVG情報を読みます
  ///
  @override
  List<HeaderFooterSvgInfo> getHeaderSvgInfo() =>
      HeaderFooterSvgInfo.headerSvgInfoList;

  ///
  /// footerのSVGはUnit8Listへ変換
  ///
  @override
  Future<void> changFooterSvgToByte() async {
    for (var svg in HeaderFooterSvgInfo.footerSvgInfoList) {
      String svgPath = svg.path;
      try {
        ByteData svgByteDate = await rootBundle.load(svgPath);
        svg.bytes = svgByteDate.buffer.asUint8List();
      } catch (e) {
        TpdLibraryLog.e("cant not fined svg: $svgPath");
      }
    }
  }

  ///
  /// footerのSVG情報を読みます
  ///
  @override
  List<HeaderFooterSvgInfo> getFooterSvgInfo() =>
      HeaderFooterSvgInfo.footerSvgInfoList;
  @override
  DeviceErrorCode showChangeView() {
    TpdLibraryLog.hello("showChangeView");
    int error = _bindings.showChangeView();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(error);
    TpdLibraryLog.byeBye("showChangeView | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode hideChangeView() {
    TpdLibraryLog.hello("hideChangeView");
    final errIndex = _bindings.hideChangeView();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("hideChangeView | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode closeChangeView() {
    TpdLibraryLog.hello("closeChangeView");
    final errIndex = _bindings.closeChangeView();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("closeChangeView | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode returnSettings() {
    TpdLibraryLog.hello("returnSettings");

    final int errorIndex = _bindings.returnSettings();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errorIndex);

    TpdLibraryLog.byeBye("returnSettings");
    return errCode;
  }

  @override
  bool isEmbUnitConnect() => bpIFGetAppDisplayGlobal().isConnectedEmbMachine;

  final _bPIFGetAppDisplayEmb = BPIFEmbAppDisplayInfoPublicDef_t.allocate();

  @override
  BPIFEmbAppDisplayInfoPublicDef bPIFGetAppDisplayEmb() {
    if (EmbLibrary().apiBinding.isEmbModeOpened()) {
      _bindings.BPIFGetAppDisplay_emb(_bPIFGetAppDisplayEmb);
    } else {
      TpdLibraryLog.errorTrace("an unexpected error is occurred");
    }
    return _bPIFGetAppDisplayEmb.ref;
  }

  @override
  DeviceErrorCode gotoUtlPreview() {
    TpdLibraryLog.hello("gotoUtlPreview");
    final errIndex = _bindings.gotoUtlPreview();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoUtlPreview | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoUtlPreviewFromMyIlEdit() {
    TpdLibraryLog.hello("gotoUtlPreviewFromMyIlEdit");
    final errIndex = _bindings.gotoUtlPreviewFromMyIlEdit();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoUtlPreviewFromMyIlEdit | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode lockAll() {
    TpdLibraryLog.hello("lockAll");
    int error = _bindings.lockAll();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(error);
    TpdLibraryLog.byeBye("lockAll | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode clearLockAll() {
    TpdLibraryLog.hello("clearLockAll");
    int error = _bindings.clearLockAll();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(error);
    TpdLibraryLog.byeBye("clearLockAll | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode suspendActionMatrixTask() {
    TpdLibraryLog.hello("suspendActionMatrixTask");
    int errorIndex = _bindings.suspendActionMatrixTask();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errorIndex);
    TpdLibraryLog.byeBye("suspendActionMatrixTask | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode resumeActionMatrixTask() {
    TpdLibraryLog.hello("resumeActionMatrixTask");
    int errorIndex = _bindings.resumeActionMatrixTask();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errorIndex);
    TpdLibraryLog.byeBye("resumeActionMatrixTask | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode clearAppErrorState() {
    TpdLibraryLog.hello("clearAppErrorState");
    int errorIndex = _bindings.clearAppErrorState();
    final isAndroidSim = const String.fromEnvironment('FLUTTER_APP_FLAVOR')
        .contains("androidSim");
    if (isAndroidSim) {
      TpdLibraryTestApi().testBpifError = BPIFError(
        errorCode: ErrCode_t.ERR_DUMMY.index,
        curFMDT: 0,
        falesafe: FaleSafeKind_t.FALE_SAFE_NON,
        iicError: 0,
        pmError: 0,
      );
    } else {
      /// Do Nothing
    }
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errorIndex);
    TpdLibraryLog.byeBye("clearAppErrorState | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode setAppErrorState(ErrAct_t errAct) {
    TpdLibraryLog.hello("setAppErrorState | $errAct");
    int errorIndex = _bindings.setAppErrorState(errAct.index);
    final isAndroidSim = const String.fromEnvironment('FLUTTER_APP_FLAVOR')
        .contains("androidSim");
    if (isAndroidSim) {
      TpdLibraryTestApi().testBpifError = BPIFError(
        errorCode: ErrCode_t.ERR_APP.index,
        curFMDT: 0,
        falesafe: FaleSafeKind_t.FALE_SAFE_NON,
        iicError: 0,
        pmError: 0,
      );
    } else {
      /// Do Nothing
    }
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errorIndex);
    TpdLibraryLog.byeBye("setAppErrorState | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode setErrorState(int error) {
    TpdLibraryLog.hello("setErrorState | $error");
    int errorIndex = _bindings.setErrorState(error);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errorIndex);
    TpdLibraryLog.byeBye("setErrorState | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode seChangeViewGrid(bool isOn) {
    TpdLibraryLog.hello("seChangeViewGrid | isOn");
    int error = _bindings.seChangeViewGrid(isOn);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(error);
    TpdLibraryLog.byeBye("seChangeViewGrid | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode seChangeViewZoom(bool isOn) {
    TpdLibraryLog.hello("seChangeViewZoom | $isOn");
    int error = _bindings.seChangeViewZoom(isOn);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(error);
    TpdLibraryLog.byeBye("seChangeViewZoom | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode setChangeViewNeedlePosition(bool isOn) {
    TpdLibraryLog.hello("setChangeViewNeedlePosition | $isOn");
    int error = _bindings.setChangeViewNeedlePosition(isOn);
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(error);
    TpdLibraryLog.byeBye("setChangeViewNeedlePosition | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoHomeFromOpening() {
    TpdLibraryLog.hello("gotoHomeFromOpening");
    final int errIndex;
    if (isAndroidSimApp()) {
      errIndex = DeviceErrorCode.devNoError.index;
    } else {
      errIndex = _bindings.gotoHomeFromOpening();
    }

    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoHomeFromOpening, errCode: $errCode");
    return errCode;
  }

  @override
  DeviceErrorCode startDemoMode() {
    TpdLibraryLog.hello("startDemoMode");
    final errIndex = _bindings.startDemoMode();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("startDemoMode | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode displayTouchEvent() {
    TpdLibraryLog.hello("displayTouchEvent");
    final errIndex = _bindings.DisplayTouchEvent();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("displayTouchEvent | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoDecoFromMyIl() {
    TpdLibraryLog.hello("gotoDecoFromMyIl");
    final errIndex = _bindings.gotoDecoFromMyIl();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoDecoFromMyIl | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoDecoFromUtl() {
    TpdLibraryLog.hello("gotoDecoFromUtl");
    final errIndex = _bindings.gotoDecoFromUtl();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoDecoFromUtl | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoMyIlEditFromMyIlTest() {
    TpdLibraryLog.hello("gotoMyIlEditFromMyIlTest");
    final errIndex = _bindings.gotoMyIlEditFromMyIlTest();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoMyIlEditFromMyIlTest | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoMyIlFromDeco() {
    TpdLibraryLog.hello("gotoMyIlFromDeco");
    final errIndex = _bindings.gotoMyIlFromDeco();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoMyIlFromDeco | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoMyIlTestFromMyIlEdit() {
    TpdLibraryLog.hello("gotoMyIlTestFromMyIlEdit");
    final errIndex = _bindings.gotoMyIlTestFromMyIlEdit();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoMyIlTestFromMyIlEdit | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoUtlModeFromDeco() {
    TpdLibraryLog.hello("gotoUtlModeFromDeco");
    final errIndex = _bindings.gotoUtlModeFromDeco();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoUtlModeFromDeco | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode keyMDCMakeCancel() => DeviceErrorCode.devNoError;

  @override
  DeviceErrorCode gotoSR() {
    TpdLibraryLog.hello("gotoSR");
    final errIndex = _bindings.gotoSR();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoSR");
    return errCode;
  }

  @override
  DeviceErrorCode exitSR() {
    TpdLibraryLog.hello("exitSR");
    final errIndex = _bindings.exitSR();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("exitSR");
    return errCode;
  }

  @override
  DeviceErrorCode exitSRBastingWarning() {
    TpdLibraryLog.hello("exitSRBastingWarning");
    final errIndex = _bindings.exitSRBastingWarning();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("exitSRBastingWarning");
    return errCode;
  }

  @override
  DeviceErrorCode gotoSRBastingWarning() {
    TpdLibraryLog.hello("gotoSRBastingWarning");
    final errIndex = _bindings.gotoSRBastingWarning();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoSRBastingWarning");
    return errCode;
  }

  @override
  DirErrorCode checkGotoHome() {
    TpdLibraryLog.hello("checkGotoHome");
    final errIndex = _bindings.checkGotoHome();
    final errCode = DirErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("checkGotoHome | errCode:$errCode");
    return errCode;
  }

  @override
  DeviceErrorCode gotoHome() {
    TpdLibraryLog.hello("gotoHome");
    final int errIndex = _bindings.gotoHome();
    final DeviceErrorCode errCode =
        DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("gotoHome | errCode: $errCode");
    return errCode;
  }

  @override
  DirErrorCode checkGotoMyIl() {
    TpdLibraryLog.hello("checkGotoMyIl");
    final errIndex = _bindings.checkGotoMyIl();
    final errCode = DirErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("checkGotoMyIl");
    return errCode;
  }

  @override
  int bpIFInitMinimum(String workFolderPath) {
    TpdLibraryLog.hello("bpIFInitMinimum");
    final pathNative = workFolderPath.toNativeUtf8();
    final result = _bindings.BPIFInitMinimum(pathNative.cast<dart_ffi.Char>());
    ffi.calloc.free(pathNative);
    TpdLibraryLog.byeBye("bpIFInitMinimum");
    return result;
  }

  @override
  BpIfInitError bpIFInit1st() {
    TpdLibraryLog.hello("bpIFInit1st");

    final result = _bindings.BPIFInit1st();
    final error = BpIfInitError.formFFIApiIndex(result);

    TpdLibraryLog.byeBye("bpIFInit1st");
    return error;
  }

  @override
  BpIfInitError bpIFInit2nd() {
    TpdLibraryLog.hello("bpIFInit2nd");
    String strFolderPath = join(getRootPath(), 'alldata');
    final pathNative = strFolderPath.toNativeUtf8();
    Pointer<Char> charPath = pathNative.cast<Char>();

    try {
      final result = _bindings.BPIFInit2nd(charPath);
      final error = BpIfInitError.formFFIApiIndex(result);
      return error;
    } finally {
      // Always free the allocated memory
      ffi.calloc.free(pathNative);
      TpdLibraryLog.byeBye("bpIFInit2nd");
    }
  }

  @override
  void bpIFSetTestMode(int mode) {
    if (isAndroidSimApp()) {
      return;
    }
    TpdLibraryLog.hello("bpIFSetTestMode");
    _bindings.BPIFSetTestMode(mode);
    TpdLibraryLog.byeBye("bpIFSetTestMode");
  }

  @override
  PopupState isDisplayPopup() {
    TpdLibraryLog.hello("isDisplayPopup");
    final libResult = _bindings.isDisplayPopup();
    final result = PopupState_t.values[libResult];
    TpdLibraryLog.byeBye("isDisplayPopup");
    return result;
  }

  @override
  DeviceErrorCode initSettingGuidanceComplete() {
    TpdLibraryLog.hello("initSettingGuidanceComplete");
    final errIndex = _bindings.initSettingGuidanceComplete();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("initSettingGuidanceComplete");
    return errCode;
  }

  @override
  DeviceErrorCode initSettingGuidanceStart() {
    TpdLibraryLog.hello("initSettingGuidanceStart");
    final errIndex = _bindings.initSettingGuidanceStart();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("initSettingGuidanceStart");
    return errCode;
  }

  @override
  DirErrorCode setMatrixEnableList(int activatePattern) {
    TpdLibraryLog.hello("setMatrixEnableList $activatePattern");
    final errIndex = _bindings.setMatrixEnableList(activatePattern);
    final errCode = DirErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("setMatrixEnableList $errCode");
    return errCode;
  }

  @override
  int appBpifMaxNotification(int idMax) {
    TpdLibraryLog.hello("AppBpifMaxNotification");
    final errIndex = _bindings.AppBpifMaxNotification(idMax);
    TpdLibraryLog.byeBye("AppBpifMaxNotification");
    return errIndex;
  }

  @override
  DeviceErrorCode stopCameraCapture() {
    TpdLibraryLog.hello("stopCameraCapture");
    final errIndex = _bindings.stopCameraCapture();
    final errCode = DeviceErrorCodeExtension.getValuesByIndex(errIndex);
    TpdLibraryLog.byeBye("stopCameraCapture | errCode:$errCode");
    return errCode;
  }

  @override
  void clearMatrixEnableListNotOverwrited(int activatePattern) {
    TpdLibraryLog.hello("clearMatrixEnableListNotOverwrited");
    _bindings.clearMatrixEnableListNotOverwrited(activatePattern);
    TpdLibraryLog.byeBye("clearMatrixEnableListNotOverwrited");
  }

  @override
  bool setMatrixEnableListNotOverwrited(int activatePattern) {
    TpdLibraryLog.hello("setMatrixEnableListNotOverwrited");
    bool result = _bindings.setMatrixEnableListNotOverwrited(activatePattern);
    TpdLibraryLog.byeBye("setMatrixEnableListNotOverwrited");
    return result;
  }

  @override
  bool shouldEnterAutoTestMode() {
    const String autoTestWorkspaceDirPath = "/data/brother_dir/auto_test";
    const String autoTestSetupJsonFilename = "auto_test_setup.json";
    try {
      return FileEntity(
        join(autoTestWorkspaceDirPath, autoTestSetupJsonFilename),
      ).existsSync();
    } catch (e) {
      TpdLibraryLog.e("Failed to check auto test mode: $e");
      return false;
    }
  }
}
