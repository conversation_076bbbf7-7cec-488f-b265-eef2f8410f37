import 'dart:ffi';
import 'dart:math';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../model/edit_model.dart';
import '../../../../../model/pattern_model.dart';
import 'size_function_interface.dart';

final sizeFunctionProvider =
    StateNotifierProvider.autoDispose<SizeFunctionInterface, SizeFunctionState>(
        (ref) => SizeButtonViewModel(ref));

class SizeButtonViewModel extends SizeFunctionInterface {
  SizeButtonViewModel(ref) : super(const SizeFunctionState(), ref) {
    ///  size ボタンの状態
    ref.listen(
      fireImmediately: true,
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.subMagnificationState),
      (previous, nextState) {
        state = state.copyWith(
          isSubSizeButtonDisable: _isSizeFunctionDisable(nextState),
        );
      },
    );

    update();
  }

  @override
  void update() {
    state = state.copyWith(
      isSizeFunctionDisable: EditModel().isEmbEditSizeDisable(),
    );
  }

  @override
  bool isMagStepOverRange(
    EmbGroup currentEmbGroup,
    MagType magType,
    ChangeType changeType,
  ) {
    bool isEnlargeMagStepOverRange(
      Magnitude magStep,
      MagType magType,
      EmbGroup currentEmbGroup,
    ) {
      return switch (magType) {
        MagType.xOnly => magStep.X >= _enlargeMax(currentEmbGroup),
        MagType.yOnly => magStep.Y >= _enlargeMax(currentEmbGroup),
        MagType.xyAll => magStep.X >= _enlargeMax(currentEmbGroup) ||
            magStep.Y >= _enlargeMax(currentEmbGroup),
        MagType.xyReset => () {
            Log.assertTrace(
                "MagType.xyResetを渡さないでください。パターンのサイズをリセットするためには、他の専用の関数があります。");
            return false;
          }(),
      };
    }

    bool isReduceMagStepOverRange(
      Magnitude magStep,
      MagType magType,
      EmbGroup currentEmbGroup,
    ) {
      return switch (magType) {
        MagType.xOnly => magStep.X <= _reduceMin(currentEmbGroup),
        MagType.yOnly => magStep.Y <= _reduceMin(currentEmbGroup),
        MagType.xyAll => magStep.X <= _reduceMin(currentEmbGroup) ||
            magStep.Y <= _reduceMin(currentEmbGroup),
        MagType.xyReset => () {
            Log.assertTrace(
                "MagType.xyResetを渡さないでください。パターンのサイズをリセットするためには、他の専用の関数があります。");
            return false;
          }(),
      };
    }

    /// 模様のMagStepデータを取得する。
    Magnitude magStep =
        currentEmbGroup.embGroupInfo.embPatternInfo.embPatterns.first.magStep;
    if (currentEmbGroup.isFont) {
      /// 文字模様に対応する最大または最小のMagStepデータを取得する。
      if (changeType == ChangeType.enlarge) {
        Magnitude max = currentEmbGroup.embGroupInfo.embPatternInfo.embPatterns
            .reduce((pre, next) => pre.magStep.X > next.magStep.X ? pre : next)
            .magStep;
        magStep = max;
      } else {
        Magnitude min = currentEmbGroup.embGroupInfo.embPatternInfo.embPatterns
            .reduce((pre, next) => pre.magStep.X < next.magStep.X ? pre : next)
            .magStep;
        magStep = min;
      }
    } else {
      /// do nothing
    }

    return switch (changeType) {
      ChangeType.enlarge =>
        isEnlargeMagStepOverRange(magStep, magType, currentEmbGroup),
      ChangeType.reduce =>
        isReduceMagStepOverRange(magStep, magType, currentEmbGroup),
    };
  }

  @override
  (int min, int max) getChangeStepSpeedRange(EmbGroup currentEmbGroup,
      Magnitude magStep, MagType magType, ChangeType changeType) {
    int changeSpeedMax = changeSpeedMaxDefault(currentEmbGroup);

    /// 残ったの調整回数取得する
    final int remainStepTimes = changeType == ChangeType.reduce
        ? switch (magType) {
            MagType.xOnly => magStep.X - _reduceMin(currentEmbGroup),
            MagType.yOnly => magStep.Y - _reduceMin(currentEmbGroup),
            MagType.xyAll =>
              min(magStep.X, magStep.Y) - _reduceMin(currentEmbGroup),
            MagType.xyReset => () {
                Log.assertTrace(
                    "MagType.xyResetを渡さないでください。パターンのサイズをリセットするためには、他の専用の関数があります。");
                return 0;
              }(),
          }
        : switch (magType) {
            MagType.xOnly => _enlargeMax(currentEmbGroup) - magStep.X,
            MagType.yOnly => _enlargeMax(currentEmbGroup) - magStep.Y,
            MagType.xyAll =>
              _enlargeMax(currentEmbGroup) - max(magStep.X, magStep.Y),
            MagType.xyReset => () {
                Log.assertTrace(
                    "MagType.xyResetを渡さないでください。パターンのサイズをリセットするためには、他の専用の関数があります。");
                return 0;
              }(),
          };

    /// 残ったの調整回数ついて、今回変更の最大量に取得する
    if (remainStepTimes <= changeSpeedMax) {
      changeSpeedMax = remainStepTimes;
    } else {
      /// Do nothing
    }
    return (changeSpeedMinDefault, changeSpeedMax);
  }

  /// 文字模様サイズ変更のステップサイズ
  static const int _fontSizeChangeStep = 1;

  ///
  /// 模様サイズを変更する
  /// 混合パターンを選択した場合、UI の制限によりサイズを調整することはできません。
  ///
  @override
  EmbLibraryError changePatternSize(
    EmbGroup currentEmbGroup,
    MagType type,
    int step,
    ChangeType changeType,
  ) {
    if (step < 0) {
      Log.e(tag: "changePatternSize", description: "step must >= 0");
      return EmbLibraryError.EMB_INVALIDPARAM_ERR;
    }

    if (step == 0) {
      return EmbLibraryError.EMB_NO_ERR;
    }
    EmbLibraryError embErrorCode = EmbLibraryError.EMB_NO_ERR;

    /// 模様サイズを変更する
    if (currentEmbGroup.isFont) {
      embErrorCode = currentEmbGroup.changeSizeEmbGroup(
        type: type,
        step: changeType == ChangeType.enlarge
            ? _fontSizeChangeStep
            : -_fontSizeChangeStep,
      );
    } else {
      embErrorCode = currentEmbGroup.changeSizeEmbGroup(
        type: type,
        step: changeType == ChangeType.enlarge ? step : -step,
      );
    }

    final Pattern currentPattern = PatternModel()
        .getCurrentPattern(curGroupHandle: currentEmbGroup.handle);
    if (currentPattern is EmbBorder) {
      currentPattern
        ..clearGroupInfoCache()
        ..clearBorderCompInfoCache()
        ..clearBorderInfoCache()
        ..clearGroupImageCache();
    } else if (currentPattern is EmbGroup) {
      currentPattern
        ..clearGroupInfoCache()
        ..clearMainImageCache();
    } else {
      /// Do nothing
    }

    return embErrorCode;
  }

  int get changeSpeedMinDefault => 1;
  int changeSpeedMaxDefault(EmbGroup currentEmbGroup) =>
      _isPatternDoSTB(currentEmbGroup) ? 2 : 5;

  ///
  /// 模様縮小のMIN値
  /// STB:20回
  ///
  static const int _reduceSTBMin = -20;
  int _reduceMin(EmbGroup currentEmbGroup) {
    if (currentEmbGroup.isFont) {
      return _reduceFontMin(currentEmbGroup);
    } else {
      return _isPatternDoSTB(currentEmbGroup)
          ? _reduceSTBMin
          : currentEmbGroup
                  .embGroupInfo.embPatternInfo.embPatterns.first.magMin -
              currentEmbGroup
                  .embGroupInfo.embPatternInfo.embPatterns.first.magDefault;
    }
  }

  ///
  /// 模様拡大のMAX値
  /// STB:50回
  ///
  static const int _enlargeSTBMax = 50;
  int _enlargeMax(EmbGroup currentEmbGroup) {
    if (currentEmbGroup.isFont) {
      return _enlargeFontMax(currentEmbGroup);
    } else {
      return _isPatternDoSTB(currentEmbGroup)
          ? _enlargeSTBMax
          : currentEmbGroup
                  .embGroupInfo.embPatternInfo.embPatterns.first.magMax -
              currentEmbGroup
                  .embGroupInfo.embPatternInfo.embPatterns.first.magDefault;
    }
  }

  ///
  /// 現在選択されている模様のdoSTB情報
  ///
  bool _isPatternDoSTB(EmbGroup currentEmbGroup) =>
      currentEmbGroup.embGroupInfo.embPatternInfo.embPatterns.first.doSTB ||
      currentEmbGroup.isFont;

  ///
  /// sizeのボタンはありますか?
  ///
  bool _isSizeFunctionDisable(int subMagnificationState) {
    if (AppInfoFuncState.getValueByNumber(subMagnificationState) ==
        AppInfoFuncState.disable) {
      return true;
    } else {
      return false;
    }
  }

  ///
  /// 小さいフォントのフォント番号
  ///
  final List<int> smallFontNumbers = [92, 93, 94];

  ///
  /// 小さいフォントの文字パターンでしょうか？
  ///
  bool _isSmallFontPattern(EmbGroup currentEmbGroup) =>
      smallFontNumbers.contains(currentEmbGroup
          .embGroupInfo.embPatternInfo.embPatterns.first.fontNumber);

  ///
  /// 文字の模様縮小のMIN値
  /// この最小値はもともと縦サイズの最小の基準を示す値だが、
  /// アルファベットの「l」等の文字にこの値を適用すると、
  /// 横サイズがもともと小さいため、
  /// 想定よりも大きなサイズでガードされてしまうため。
  ///
  static const int _reduceSmallFontMin = 10;
  static const int _reduceNormalFontMin = 20;
  int _reduceFontMin(EmbGroup currentEmbGroup) =>
      _isSmallFontPattern(currentEmbGroup)
          ? _reduceSmallFontMin
          : _reduceNormalFontMin;

  ///
  /// 文字の模様拡大のMAX値
  ///
  static const int _enlargeSmallFontMax = 100;
  static const int _enlargeNormalFontMax = 1000;
  int _enlargeFontMax(EmbGroup currentEmbGroup) =>
      _isSmallFontPattern(currentEmbGroup)
          ? _enlargeSmallFontMax
          : _enlargeNormalFontMax;
}
