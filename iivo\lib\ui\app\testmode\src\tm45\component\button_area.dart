import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/write_debug_log_task_queue.dart.dart';

class ButtonArea extends ConsumerWidget {
  const ButtonArea({
    super.key,
    this.returnTestModeMenu,
    this.pressedSelectLanguageMinus,
    this.pressedSelectLanguagePlus,
    this.pressedErrorMessageNoPlus100,
    this.pressedErrorMessageNoPlus10,
    this.pressedErrorMessageNoPlus1,
    this.pressedErrorMessageNoMinus100,
    this.pressedErrorMessageNoMinus10,
    this.pressedErrorMessageNoMinus1,
  });

  final void Function()? returnTestModeMenu;
  final void Function()? pressedSelectLanguageMinus;
  final void Function()? pressedSelectLanguagePlus;
  final void Function()? pressedErrorMessageNoPlus100;
  final void Function()? pressedErrorMessageNoPlus10;
  final void Function()? pressedErrorMessageNoPlus1;
  final void Function()? pressedErrorMessageNoMinus100;
  final void Function()? pressedErrorMessageNoMinus10;
  final void Function()? pressedErrorMessageNoMinus1;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      children: [
        Column(
          children: [
            const Spacer(flex: 34),
            Expanded(
              flex: 80,
              child: Row(
                children: [
                  const Spacer(flex: 30),
                  Expanded(
                    flex: 80,
                    child: GestureDetector(onTap: () async {
                      SystemSoundPlayer().play(SystemSoundEnum.accept);
                      DeviceLibrary().apiBinding.writeDebugLogToFile();
                      await Future.delayed(const Duration(seconds: 1));
                      WriteDebugLogTaskQueue.getInstance().saveDebugLogToUSB();
                    }),
                  ),
                  const Spacer(flex: 690),
                ],
              ),
            ),
            const Spacer(flex: 45),
            Expanded(
              flex: 80,
              child: Row(
                children: [
                  const Spacer(flex: 30),
                  Expanded(
                    flex: 80,
                    child: GestureDetector(
                      onTap: pressedSelectLanguageMinus,
                    ),
                  ),
                  const Spacer(flex: 20),
                  Expanded(
                    flex: 80,
                    child: GestureDetector(
                      onTap: pressedSelectLanguagePlus,
                    ),
                  ),
                  const Spacer(flex: 590),
                ],
              ),
            ),
            const Spacer(flex: 20),
            Expanded(
              flex: 80,
              child: Row(
                children: [
                  const Spacer(flex: 30),
                  Expanded(
                    flex: 200,
                    child: GestureDetector(
                      onTap: returnTestModeMenu,
                    ),
                  ),
                  const Spacer(flex: 570),
                ],
              ),
            ),
            const Spacer(flex: 941),
          ],
        ),
        Column(
          children: [
            const Spacer(flex: 140),
            Expanded(
              flex: 80,
              child: Row(
                children: [
                  const Spacer(flex: 530),
                  Expanded(
                    flex: 80,
                    child: GestureDetector(
                      onTap: pressedErrorMessageNoPlus100,
                    ),
                  ),
                  Expanded(
                    flex: 80,
                    child: GestureDetector(
                      onTap: pressedErrorMessageNoPlus10,
                    ),
                  ),
                  Expanded(
                    flex: 80,
                    child: GestureDetector(
                      onTap: pressedErrorMessageNoPlus1,
                    ),
                  ),
                  const Spacer(flex: 30),
                ],
              ),
            ),
            Expanded(
              flex: 80,
              child: Row(
                children: [
                  const Spacer(flex: 530),
                  Expanded(
                    flex: 80,
                    child: GestureDetector(
                      onTap: pressedErrorMessageNoMinus100,
                    ),
                  ),
                  Expanded(
                    flex: 80,
                    child: GestureDetector(
                      onTap: pressedErrorMessageNoMinus10,
                    ),
                  ),
                  Expanded(
                    flex: 80,
                    child: GestureDetector(
                      onTap: pressedErrorMessageNoMinus1,
                    ),
                  ),
                  const Spacer(flex: 30),
                ],
              ),
            ),
            const Spacer(flex: 980),
          ],
        ),
        Column(
          children: [
            const Spacer(flex: 1119),
            Expanded(
              flex: 80,
              child: GestureDetector(
                onTap: () {},
              ),
            ),
            const Spacer(flex: 81)
          ],
        ),
      ],
    );
  }
}
