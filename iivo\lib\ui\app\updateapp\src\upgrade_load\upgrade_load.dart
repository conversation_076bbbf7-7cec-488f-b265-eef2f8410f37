import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';
import 'upgrade_load_view_model.dart';

class UpgradeLoad extends StatefulPage {
  const UpgradeLoad({super.key});
  @override
  PageState<UpgradeLoad> createState() => _UpgradePageState();
}

class _UpgradePageState extends PageState<UpgradeLoad> {
  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final state = ref.watch(upgradeUpdateViewModeProvider(context));
    final viewModel = ref.read(upgradeUpdateViewModeProvider(context).notifier);
    return Scaffold(
      body: Stack(
        children: [
          const pre_base_gray(),
          Column(
            children: [
              viewModel.isLineUpdate
                  ? const Expanded(
                      flex: 30,
                      child: Row(
                        children: [
                          Spacer(flex: 100),
                          Expanded(
                            flex: 200,
                            child: Center(
                              child: grp_str_number1(
                                text: "PRODUCT LINE MODE",
                                alignment: Alignment.center,
                                textStyle: TextStyle(
                                  fontFamily: "Roboto",
                                  fontSize: 30,
                                  height: 1,
                                  color: Colors.white,
                                  backgroundColor: Colors.red,
                                ),
                              ),
                            ),
                          ),
                          Spacer(flex: 100),
                        ],
                      ),
                    )
                  : const Spacer(flex: 30),
              const Spacer(
                flex: 137,
              ),
              const Expanded(
                flex: 156,
                child: Center(
                  child: ico_mb_upgrade_l(),
                ),
              ),
              const Spacer(
                flex: 96,
              ),
              Expanded(
                flex: 133,
                child: Row(
                  children: [
                    const Spacer(
                      flex: 40,
                    ),
                    Expanded(
                      flex: 720,
                      child: grp_str_one(
                        text: state.isWlanUpgrade ? l10n.upg_12 : l10n.upg_10,
                      ),
                    ),
                    const Spacer(
                      flex: 40,
                    )
                  ],
                ),
              ),
              const Spacer(
                flex: 44,
              ),
              Expanded(
                flex: 207,
                child: Row(
                  children: [
                    const Spacer(
                      flex: 40,
                    ),
                    Expanded(
                      flex: 720,
                      child: grp_str_two(
                        text: state.literal,
                      ),
                    ),
                    const Spacer(
                      flex: 40,
                    )
                  ],
                ),
              ),
              const Spacer(
                flex: 27,
              ),
              Expanded(
                flex: 80,
                child: Center(
                  child: viewModel.isLineUpdate
                      ? state.isExistsUpgJudge
                          ? grp_btn_load(
                              buttonText: l10n.icon_00050,
                              onTap: () => viewModel.onLoadButtonClick(),
                            )
                          : Container()
                      : grp_btn_load(
                          buttonText: l10n.icon_00050,
                          onTap: () => viewModel.onLoadButtonClick(),
                        ),
                ),
              ),
              const Spacer(
                flex: 24,
              ),
              Expanded(
                  flex: 195,
                  child: Stack(
                    children: [
                      Column(children: [
                        state.isTurnOff
                            ? Expanded(
                                flex: 45,
                                child: Row(children: [
                                  const Spacer(
                                    flex: 290,
                                  ),
                                  Expanded(
                                    flex: 220,
                                    child: grp_str_three(
                                      text: l10n.update_15,
                                      alignment: Alignment.topCenter,
                                    ),
                                  ),
                                  const Spacer(
                                    flex: 290,
                                  ),
                                ]),
                              )
                            : const Spacer(
                                flex: 45,
                              ),
                        const Spacer(
                          flex: 150,
                        ),
                      ]),
                      const Column(
                        children: [
                          Expanded(
                            flex: 134,
                            child: grp_str_three(),
                          ),
                          Spacer(
                            flex: 61,
                          )
                        ],
                      )
                    ],
                  )),
              Expanded(
                flex: 80,
                child: Row(
                  children: [
                    const Spacer(
                      flex: 12,
                    ),
                    Expanded(
                        flex: 152,
                        child: grp_btn_return(
                          buttonText: l10n.icon_return,
                          onTap: () => viewModel.onReturnButtonClick(),
                        )),
                    const Spacer(
                      flex: 256,
                    ),
                    Expanded(
                      flex: 340,
                      child: Column(
                        children: [
                          const Spacer(
                            flex: 18,
                          ),
                          Expanded(
                            flex: 45,
                            child: grp_str_five(
                              text: "UPG VERSION : ${state.upgVersion}",
                              align: Alignment.topRight,
                            ),
                          ),
                          const Spacer(
                            flex: 17,
                          ),
                        ],
                      ),
                    ),
                    const Spacer(
                      flex: 40,
                    ),
                  ],
                ),
              ),
              const Spacer(
                flex: 71,
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup(WidgetRef ref) => {};
}
