import 'dart:convert' show jsonDecode;

import 'package:flutter/material.dart';
import 'package:log/log.dart';
import 'package:path/path.dart';

import '../../../../../memory/extension/svg.dart';
import '../../../../../memory/memory.dart';
import '../../../../../model/app_locale.dart';
import '../../../../../model/limited_edition_model.dart';
import '../../../../../model/machine_config_model.dart';
import 'image_data_serialize/exclusives_pattern/exclusives_pattern.dart'
    as exclusives_pattern_image;
import 'pattern_data_base.dart';

///
/// 一つ組合の模様イコンデータの構造
///
class ExclusivesPatternGroup extends PatternDataBase
    implements PatternInfoGroupImplement, PatternIconGroupImplement {
  ExclusivesPatternGroup({
    required this.categoryType,
    required this.groupName,
    required this.iconsGroup,
    required this.patternFilterInfo,
    required this.patternNumGroup,
    required this.categoryImage,
  });

  final int categoryType;
  final String groupName;

  ///
  /// カテゴリに模様のindex
  /// 現在、小カテゴリごとに1つのタイプしかありません
  ///
  final List<int> patternNumGroup;
  @override
  final List<Image> iconsGroup;
  @override
  final List<PatternFilterGroup> patternFilterInfo;
  final List<Widget> categoryImage;
}

///
/// 模様のイコンデータを制御する
///
class ExclusivesPatternReader {
  factory ExclusivesPatternReader() => _instance;

  ExclusivesPatternReader._internal() {
    _initExclusivesPatternData();
  }

  ///
  /// 模様のイコンデータ制御クラス
  ///
  static final ExclusivesPatternReader _instance =
      ExclusivesPatternReader._internal();
  static const invalidNum = -1;

  ///
  /// iconのPathを制御する
  ///
  static final String exclusivesPatternDir =
      join(memorySector.emb.absolutePath, "exclusives_pattern_data");

  static final String exclusivesPatternInfoFile =
      join(exclusivesPatternDir, "tacony_exclusives_pattern.json");

  static final String exclusivesPatternLimitedEditionInfoFile =
      join(exclusivesPatternDir, "tacony_exclusives_pattern_le.json");

  static final String exclusivesPatternDataDir =
      join(exclusivesPatternDir, "png");

  late List<ExclusivesPatternGroup> _allExclusivesPatternImageInfoCache;

  ///
  /// Json ファイルに保存されているのIconイマジンとメッセージデータを読み取ります
  ///
  void _initExclusivesPatternData() {
    try {
      ///  Androidの模様のIconデータファイルを取得する
      FileEntity exclusivesPatternFile;
      if (LimitedEditionModel().isLimitedEdition) {
        exclusivesPatternFile =
            FileEntity(exclusivesPatternLimitedEditionInfoFile);
      } else {
        exclusivesPatternFile = FileEntity(exclusivesPatternInfoFile);
      }

      /// 模様のIconデータを取得する
      if (exclusivesPatternFile.existsSync()) {
        /// Iconイマジンjsonファイル解析結果を保存する
        exclusives_pattern_image.ExclusivesPattern imageJsonData;
        imageJsonData = exclusives_pattern_image.ExclusivesPattern.fromJson(
            jsonDecode(exclusivesPatternFile.readAsStringSync()));

        _allExclusivesPatternImageInfoCache =
            _getAllExclusivesPatternsInfo(imageJsonData);
      }
    } catch (e) {
      Log.errorTrace(
          "Initialization fails, and the model data does not match!,errInfo:${e.toString()}");
    }
  }

  ///
  /// 全ての中カテゴリーのイコンデータを取得する
  ///
  /// ##@return
  /// - List<ExclusivesPatternGroup>: 順番保存されているの模様イコンデータ
  ///
  List<ExclusivesPatternGroup> getAllExclusivesPatternsInfo() =>
      _allExclusivesPatternImageInfoCache;

  ///
  /// 画像情報キャッシュを更新
  ///
  void updateExclusivesImagesInfoCache() {
    if (MachineConfigModel().getCurrentModel() == AppModelEnum.brother) {
      Log.errorTrace("Brother Model data cannot be initialized!");
      return;
    }
    _initExclusivesPatternData();
  }

  ///
  /// 全ての中カテゴリーのイコンデータを取得する
  ///
  /// ##@return
  /// - List<ExclusivesPatternGroup>: 順番保存されているの模様イコンデータ
  ///
  List<ExclusivesPatternGroup> _getAllExclusivesPatternsInfo(
    exclusives_pattern_image.ExclusivesPattern exclusivesImageJsonData,
  ) {
    List<ExclusivesPatternGroup> exclusivesPatternIconData = [];
    List<Widget> image = [];
    for (var i = 0; i < exclusivesImageJsonData.category.length; i++) {
      String imageFileName = exclusivesImageJsonData.category[i].image;
      if (AppLocale().isFrench) {
        imageFileName = imageFileName.replaceAll('_en', '_fr');
      } else {
        /// Do Nothing
      }
      image.add(SvgPictureExtension.fileEntity(
          FileEntity("$exclusivesPatternDataDir/$imageFileName")));
    }

    exclusivesImageJsonData.category.asMap().entries.forEach((element) {
      List<Image> icons = [];
      List<PatternFilterGroup> patternFilterInfo = [];
      List<int> patternNumList = [];

      /// 模様のイコンデータを模様のイコンデータグループに追加する
      for (var pattern in element.value.patterns.asMap().entries) {
        icons.add(ImageExtension.fileEntity(
          FileEntity(join(exclusivesPatternDataDir, pattern.value.image)),
        ));

        patternNumList.add(pattern.value.patternNum);

        patternFilterInfo.add(
          PatternFilterGroup(
            groupNo: element.key,
            patternIndex: pattern.key,
            categoryType: element.value.categoryType,
            patternNum: pattern.value.patternNum,
            icon: ImageExtension.fileEntity(
              FileEntity(join(exclusivesPatternDataDir, pattern.value.image)),
            ),
            width: pattern.value.filter.width,
            height: pattern.value.filter.height,
            colorNumber: pattern.value.filter.numberOfColor,
          ),
        );
      }

      /// 模様イコンデータグループを模様模様イコンデータグループリストに追加する
      exclusivesPatternIconData.add(
        ExclusivesPatternGroup(
          groupName: element.value.name,
          categoryType: element.value.categoryType,
          iconsGroup: icons,
          patternFilterInfo: patternFilterInfo,
          patternNumGroup: patternNumList,
          categoryImage: image,
        ),
      );
    });
    return exclusivesPatternIconData;
  }
}
