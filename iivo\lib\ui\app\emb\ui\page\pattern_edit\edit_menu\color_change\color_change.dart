import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../../model/machine_config_model.dart';
import '../../../../component/emb_footer/emb_footer.dart';
import '../../../../component/emb_header/emb_header.dart';
import 'color_change_view_model.dart';
import 'color_input/color_input.dart';
import 'preview/preview.dart';
import 'thread_color_list/thread_color_list.dart';

class ColorChange extends StatefulPage {
  const ColorChange({super.key});
  @override
  PageState<ColorChange> createState() => _ColorChangeState();
}

class _ColorChangeState extends PageState<ColorChange> {
  @override
  Widget buildPage(BuildContext context, WidgetRef ref) {
    final AppLocalizations l10n = AppLocalizations.of(context)!;
    final viewModel = ref.read(colorChangeViewModelProvider.notifier);
    final state = ref.watch(colorChangeViewModelProvider);

    return Scaffold(
      body: pre_base_white(
        child: Stack(
          children: [
            Column(
              children: [
                const Expanded(
                  flex: 71,
                  child: EmbHeader(),
                ),
                Expanded(
                  flex: 1148,
                  child: Material(
                    color: Colors.transparent,
                    child: Stack(
                      children: [
                        /// pic_text_preview
                        const Column(
                          children: [
                            Spacer(flex: 9),
                            Expanded(
                              flex: 691,
                              child: Row(
                                children: [
                                  Spacer(flex: 7),
                                  Expanded(
                                    flex: 441,
                                    child: pic_text_preview(),
                                  ),
                                  Spacer(flex: 352),
                                ],
                              ),
                            ),
                            Spacer(flex: 448),
                          ],
                        ),

                        ///"RealPreview"ボタン
                        Column(
                          children: [
                            const Spacer(flex: 9),
                            Expanded(
                              flex: 63,
                              child: Row(
                                children: [
                                  const Spacer(flex: 458),
                                  Expanded(
                                    flex: 63,
                                    child: CustomTooltip(
                                      message: l10n.tt_emb_realpreview,
                                      child: grp_btn_emb_realpreview(
                                        state: state.isRealPreviewEnable
                                            ? ButtonState.normal
                                            : ButtonState.disable,
                                        onTap: () => viewModel
                                            .onRealPreviewButtonClicked(
                                                context),
                                      ),
                                    ),
                                  ),
                                  const Spacer(flex: 279),
                                ],
                              ),
                            ),
                            const Spacer(flex: 1076),
                          ],
                        ),

                        ///"ThreadList"リスト
                        const ThreadColorList(),

                        ///"ColorShuffling"ボタン
                        Column(
                          children: [
                            const Spacer(flex: 707),
                            Expanded(
                              flex: 63,
                              child: Row(
                                children: [
                                  const Spacer(flex: 263),
                                  Expanded(
                                    flex: 185,
                                    child: CustomTooltip(
                                      message: MachineConfigModel().isBrother
                                          ? l10n.tt_emb_colorcolorshuffling
                                          : l10n.tt_emb_colorvisualizer,
                                      child: MachineConfigModel().isBrother
                                          ? grp_btn_colorshuffling(
                                              feedBackControl: null,
                                              isEnglish: state.isEnglish,
                                              onTap: () => viewModel
                                                  .onColorShufflingButtonClicked(
                                                      context),
                                            )
                                          : grp_btn_colorvisualizer(
                                              feedBackControl: null,
                                              onTap: () => viewModel
                                                  .onColorShufflingButtonClicked(
                                                      context),
                                            ),
                                    ),
                                  ),
                                  const Spacer(flex: 352),
                                ],
                              ),
                            ),
                            const Spacer(flex: 378),
                          ],
                        ),

                        ///"ColorInput"リスト
                        const ColorInput(),
                      ],
                    ),
                  ),
                ),
                const Expanded(
                  flex: 61,
                  child: EmbFooter(),
                ),
              ],
            ),

            ///Preview
            const Preview(),
          ],
        ),
      ),
    );
  }

  @override
  Map<String, PopupRouteBuilder> registerNamedPopup(WidgetRef ref) =>
      ref.read(colorChangeViewModelProvider.notifier).registerNamedPopup();
}
