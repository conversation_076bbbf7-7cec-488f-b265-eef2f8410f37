import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'line_e_stitch_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class LineEStitchState with _$LineEStitchState {
  const factory LineEStitchState({
    @Default(false) bool isWidthPopupOn,
    @Default(false) bool isSpacePopupOn,
    @Default(false) bool isThicknessPopupOn,
    @Default(false) bool isFlipPopupOn,
    @Default(true) bool isFlipSelect,
    required String widthDisplayValue,
    required bool isWidthDefaultValue,
    required String spaceDisplayValue,
    required bool isSpaceDefaultValue,
    required String thicknessDisplayValue,
    required bool isThicknessDefaultValue,
    required String flipDisplayValue,
    required bool isFlipDefaultValue,
  }) = _LineEStitchState;
}

abstract class LineEStitchViewInterface extends ViewModel<LineEStitchState> {
  LineEStitchViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// 幅設定ポップアップウィンドウを開きます
  ///
  void openWidthSettingPopup(context);

  ///
  /// 間隔設定ポップアップウィンドウを開く
  ///
  void openSpaceSettingPopup(context);

  ///
  /// 厚さ設定ポップアップウィンドウを開く
  ///
  void openThicknessSettingPopup(context);

  ///
  /// 方向設定ポップアップウィンドウを開きます
  ///
  void openFlipSettingPopup(context);

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;

  ///
  /// テキスト信号
  ///
  String get textSignal;
}
