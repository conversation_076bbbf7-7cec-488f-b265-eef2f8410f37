// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'network_others_setting_page_interface.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$NetworkOthersSettingPageState {
  String get macAddress => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $NetworkOthersSettingPageStateCopyWith<NetworkOthersSettingPageState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NetworkOthersSettingPageStateCopyWith<$Res> {
  factory $NetworkOthersSettingPageStateCopyWith(
          NetworkOthersSettingPageState value,
          $Res Function(NetworkOthersSettingPageState) then) =
      _$NetworkOthersSettingPageStateCopyWithImpl<$Res,
          NetworkOthersSettingPageState>;
  @useResult
  $Res call({String macAddress});
}

/// @nodoc
class _$NetworkOthersSettingPageStateCopyWithImpl<$Res,
        $Val extends NetworkOthersSettingPageState>
    implements $NetworkOthersSettingPageStateCopyWith<$Res> {
  _$NetworkOthersSettingPageStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? macAddress = null,
  }) {
    return _then(_value.copyWith(
      macAddress: null == macAddress
          ? _value.macAddress
          : macAddress // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NetworkOthersSettingPageStateImplCopyWith<$Res>
    implements $NetworkOthersSettingPageStateCopyWith<$Res> {
  factory _$$NetworkOthersSettingPageStateImplCopyWith(
          _$NetworkOthersSettingPageStateImpl value,
          $Res Function(_$NetworkOthersSettingPageStateImpl) then) =
      __$$NetworkOthersSettingPageStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String macAddress});
}

/// @nodoc
class __$$NetworkOthersSettingPageStateImplCopyWithImpl<$Res>
    extends _$NetworkOthersSettingPageStateCopyWithImpl<$Res,
        _$NetworkOthersSettingPageStateImpl>
    implements _$$NetworkOthersSettingPageStateImplCopyWith<$Res> {
  __$$NetworkOthersSettingPageStateImplCopyWithImpl(
      _$NetworkOthersSettingPageStateImpl _value,
      $Res Function(_$NetworkOthersSettingPageStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? macAddress = null,
  }) {
    return _then(_$NetworkOthersSettingPageStateImpl(
      macAddress: null == macAddress
          ? _value.macAddress
          : macAddress // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$NetworkOthersSettingPageStateImpl
    implements _NetworkOthersSettingPageState {
  const _$NetworkOthersSettingPageStateImpl(
      {this.macAddress = "00-00-00-00-00"});

  @override
  @JsonKey()
  final String macAddress;

  @override
  String toString() {
    return 'NetworkOthersSettingPageState(macAddress: $macAddress)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkOthersSettingPageStateImpl &&
            (identical(other.macAddress, macAddress) ||
                other.macAddress == macAddress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, macAddress);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkOthersSettingPageStateImplCopyWith<
          _$NetworkOthersSettingPageStateImpl>
      get copyWith => __$$NetworkOthersSettingPageStateImplCopyWithImpl<
          _$NetworkOthersSettingPageStateImpl>(this, _$identity);
}

abstract class _NetworkOthersSettingPageState
    implements NetworkOthersSettingPageState {
  const factory _NetworkOthersSettingPageState({final String macAddress}) =
      _$NetworkOthersSettingPageStateImpl;

  @override
  String get macAddress;
  @override
  @JsonKey(ignore: true)
  _$$NetworkOthersSettingPageStateImplCopyWith<
          _$NetworkOthersSettingPageStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
