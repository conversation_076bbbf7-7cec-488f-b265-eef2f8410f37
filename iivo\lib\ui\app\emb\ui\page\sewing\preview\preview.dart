import 'dart:collection';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xd_component/xd_component.dart';

import '../../common_component/preview_custom_painter.dart'
    show
        PathPainter,
        ThreadMarkPainter,
        NeedlePointerPainter,
        MaskPainter,
        GridPainter;
import '../../pattern_edit/preview/component/projector_frame_drag_area/projector_frame_drag_area.dart';
import 'preview_view_model.dart';

///
/// 刺しゅうパターン縫製画面 の 展示エリア
///
class Preview extends ConsumerStatefulWidget {
  const Preview({super.key});

  ///
  /// 最新のGlobalKeyを取得します
  ///
  static GlobalKey? getLatestGlobalKey() =>
      _PreviewState._previewGlobalKeyQueue.lastOrNull;

  @override
  ConsumerState<Preview> createState() => _PreviewState();
}

class _PreviewState extends ConsumerState<Preview> {
  static final List<GlobalKey<_PreviewState>> _previewGlobalKeyQueue = [];

  /// 中心点のGlobalKey
  final GlobalKey _centerKey = GlobalKey();

  /// プレビュー領域のGlobalKey
  final GlobalKey<_PreviewState> _previewGlobalKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _previewGlobalKeyQueue.add(_previewGlobalKey);
  }

  @override
  void dispose() {
    super.dispose();
    _previewGlobalKeyQueue.remove(_previewGlobalKey);
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(previewViewModelProvider);
    final viewModel = ref.read(previewViewModelProvider.notifier);
    return Column(
      children: [
        const Spacer(flex: 233),
        Expanded(
          flex: viewModel.getPreviewSize.dy.toInt(),
          child: Row(
            children: [
              Expanded(
                flex: 564,
                child: pic_embroidery_mainpreview(
                  child: Row(
                    children: [
                      const Spacer(flex: 1),
                      Expanded(
                        flex: viewModel.getPreviewSize.dx.toInt(),
                        child: ColoredBox(
                          color: state.backgroundColor,
                          child: Stack(
                            children: [
                              /// 背景スキャンの画像
                              IgnorePointer(
                                child: Container(
                                  alignment: Alignment.center,
                                  width: viewModel.getPreviewSize.dx,
                                  height: viewModel.getPreviewSize.dy,
                                  child: state.backgroundImage ?? Container(),
                                ),
                              ),
                              RepaintBoundary(
                                key: _previewGlobalKey,
                                child: Transform(
                                  origin: state.rotateCenter,
                                  transform: Matrix4.rotationZ(
                                      (math.pi / 180) * (state.dragAngle ?? 0)),
                                  child: Stack(
                                    children: [
                                      /// すべてのpatternを生成
                                      ...() {
                                        List<Widget> patternWidget = [];
                                        state.patternDisplayInfoList
                                            .asMap()
                                            .entries
                                            .forEach((pattern) {
                                          PatternViewDisplayInfo patternInfo =
                                              pattern.value;

                                          patternWidget.add(
                                            Positioned(
                                              top: patternInfo.top,
                                              left: patternInfo.left,
                                              width: patternInfo.width,
                                              height: patternInfo.height,
                                              child: Stack(
                                                children: [
                                                  /// すべてのBorderを生成
                                                  ...() {
                                                    List<Widget> borderWidget =
                                                        [];
                                                    patternInfo
                                                        .borderDisplayInfoList
                                                        .asMap()
                                                        .entries
                                                        .forEach((border) {
                                                      EmbBorderViewDisplayInfo
                                                          borderInfo =
                                                          border.value;

                                                      borderWidget.add(
                                                        Positioned(
                                                          top: borderInfo.top,
                                                          left: borderInfo.left,
                                                          width:
                                                              borderInfo.width,
                                                          height:
                                                              borderInfo.height,
                                                          child: Stack(
                                                            children: [
                                                              /// すべてのEmbGroupを生成
                                                              ...() {
                                                                List<Widget>
                                                                    groupWidget =
                                                                    [];
                                                                borderInfo
                                                                    .groupDisplayInfoList
                                                                    .asMap()
                                                                    .entries
                                                                    .forEach(
                                                                        (group) {
                                                                  EmbGroupViewDisplayInfo
                                                                      groupInfo =
                                                                      group
                                                                          .value;
                                                                  String
                                                                      groupKey =
                                                                      group.key
                                                                          .toString();

                                                                  groupWidget
                                                                      .add(
                                                                    /// patternの表示領域(Maskと赤いドットを含む)
                                                                    Positioned(
                                                                      top: groupInfo
                                                                          .top,
                                                                      left: groupInfo
                                                                          .left,
                                                                      width: groupInfo
                                                                          .width,
                                                                      height: groupInfo
                                                                          .height,
                                                                      child:
                                                                          Stack(
                                                                        children: [
                                                                          /// すべてのEmbPatternを生成
                                                                          ...() {
                                                                            List<Widget>
                                                                                embPatternWidget =
                                                                                [];
                                                                            groupInfo.embPatternDisplayInfoList.asMap().entries.forEach((embPattern) {
                                                                              EmbPatternViewDisplayInfo embPatternInfo = embPattern.value;
                                                                              String embPatternKey = embPattern.key.toString();

                                                                              /// EmbPatternの画像
                                                                              if (embPatternInfo.displayImage != null) {
                                                                                embPatternWidget.add(
                                                                                  Positioned(
                                                                                    key: Key("group${groupKey}embPatternKey$embPatternKey"),
                                                                                    top: embPatternInfo.imageTop,
                                                                                    left: embPatternInfo.imageLeft,
                                                                                    width: embPatternInfo.imageWidth,
                                                                                    height: embPatternInfo.imageHeight,
                                                                                    child: GestureDetector(
                                                                                      onScaleStart: (details) => viewModel.dargPatternStart(details),
                                                                                      onScaleUpdate: (details) => viewModel.dargPattern(details),
                                                                                      onScaleEnd: (details) => viewModel.dargPatternEnd(),
                                                                                      child: Image.memory(embPatternInfo.displayImage!),
                                                                                    ),
                                                                                  ),
                                                                                );
                                                                              } else {
                                                                                /// do noting
                                                                              }

                                                                              if (groupInfo.isAllNotSewing || state.dragAngle != null) {
                                                                                embPatternWidget.add(
                                                                                  Positioned(
                                                                                    top: embPatternInfo.top,
                                                                                    left: embPatternInfo.left,
                                                                                    width: embPatternInfo.width,
                                                                                    height: embPatternInfo.height,
                                                                                    child:

                                                                                        /// 縫ってないの赤い点線のボックス
                                                                                        IgnorePointer(
                                                                                      child: CustomPaint(
                                                                                        painter: MaskPainter(
                                                                                          isDashedLine: groupInfo.isAllNotSewing,
                                                                                          maskColor: const Color.fromARGB(255, 235, 0, 0),
                                                                                          strokeWidth: 1,
                                                                                          maskTopLeft: embPatternInfo.mask.topLeft,
                                                                                          maskTopRight: embPatternInfo.mask.topRight,
                                                                                          maskBottomLeft: embPatternInfo.mask.bottomLeft,
                                                                                          maskBottomRight: embPatternInfo.mask.bottomRight,
                                                                                        ),
                                                                                      ),
                                                                                    ),
                                                                                  ),
                                                                                );
                                                                              } else {
                                                                                /// Do nothing
                                                                              }
                                                                            });
                                                                            return embPatternWidget;
                                                                          }(),
                                                                        ],
                                                                      ),
                                                                    ),
                                                                  );
                                                                });
                                                                return groupWidget;
                                                              }(),
                                                            ],
                                                          ),
                                                        ),
                                                      );
                                                    });

                                                    return borderWidget;
                                                  }(),
                                                ],
                                              ),
                                            ),
                                          );
                                        });

                                        return patternWidget;
                                      }(),
                                    ],
                                  ),
                                ),
                              ),

                              /// Mask
                              IgnorePointer(
                                child: state.maskDisplayInfo == null
                                    ? Container()
                                    : SizedBox(
                                        width: viewModel.getPreviewSize.dx,
                                        height: viewModel.getPreviewSize.dy,
                                        child: CustomPaint(
                                          painter: MaskPainter(
                                            maskColor: state.maskColor,
                                            strokeWidth: 1,
                                            maskTopLeft:
                                                state.maskDisplayInfo!.topLeft,
                                            maskTopRight:
                                                state.maskDisplayInfo!.topRight,
                                            maskBottomLeft: state
                                                .maskDisplayInfo!.bottomLeft,
                                            maskBottomRight: state
                                                .maskDisplayInfo!.bottomRight,
                                          ),
                                        ),
                                      ),
                              ),

                              /// Grid
                              IgnorePointer(
                                child: SizedBox(
                                  width: viewModel.getPreviewSize.dx,
                                  height: viewModel.getPreviewSize.dy,
                                  child: CustomPaint(
                                    foregroundPainter: GridPainter(
                                      gridColor: state.gridColor,
                                      zoomValue: 1,
                                      gridTypeIndex: state.gridTypeIndex,
                                      verticalList:
                                          viewModel.getGridVerticalList(),
                                      horizontalList:
                                          viewModel.getGridHorizontalList(),
                                    ),
                                  ),
                                ),
                              ),

                              /// frame
                              IgnorePointer(
                                child: SizedBox(
                                  width: viewModel.getPreviewSize.dx,
                                  height: viewModel.getPreviewSize.dy,
                                  child: CustomPaint(
                                    painter: PathPainter(
                                      color: state.frameColor,
                                      strokeWidth: 1,
                                      path: state.frameDrawPath,
                                    ),
                                  ),
                                ),
                              ),

                              /// ページ上の糸印の表示情報
                              IgnorePointer(
                                child: SizedBox(
                                  width: viewModel.getPreviewSize.dx,
                                  height: viewModel.getPreviewSize.dy,
                                  child: CustomPaint(
                                    painter: ThreadMarkPainter(
                                      zoomValue: 1,
                                      threadMarkInfoList:
                                          state.threadMarkInfoList,
                                    ),
                                  ),
                                ),
                              ),

                              /// 針数から針位置
                              IgnorePointer(
                                child: state.needlePointerCenterOffset == null
                                    ? Container()
                                    : SizedBox(
                                        width: viewModel.getPreviewSize.dx,
                                        height: viewModel.getPreviewSize.dy,
                                        child: CustomPaint(
                                          painter: NeedlePointerPainter(
                                            centerOffset: state
                                                .needlePointerCenterOffset!,
                                          ),
                                        ),
                                      ),
                              ),

                              /// Rotateの赤いドット
                              state.rotateRedPoint == null
                                  ? Container()
                                  : Positioned(
                                      left: state.rotateRedPoint!.topLeft.dx,
                                      top: state.rotateRedPoint!.topLeft.dy,
                                      width: state.rotateRedPoint!.width,
                                      height: state.rotateRedPoint!.height,
                                      child: Transform.rotate(
                                        angle: math.pi /
                                            180 *
                                            (state.dragAngle ?? 0),
                                        child: Stack(
                                          children: [
                                            ...() {
                                              List<Widget> widget = [];

                                              state
                                                  .rotateRedPoint!.redPointInfos
                                                  .asMap()
                                                  .entries
                                                  .forEach((element) {
                                                if (element
                                                    .value.isRotatePoint) {
                                                  /// Rotate赤色ドット
                                                  widget.add(
                                                    Positioned(
                                                      left: element
                                                          .value.topLeft.dx,
                                                      top: element
                                                          .value.topLeft.dy,
                                                      child: GestureDetector(
                                                        onPanStart: (details) {
                                                          RenderBox renderBox =
                                                              _centerKey
                                                                      .currentContext!
                                                                      .findRenderObject()
                                                                  as RenderBox;
                                                          viewModel
                                                              .dargRotatePointStart(
                                                            renderBox
                                                                .localToGlobal(
                                                                    Offset
                                                                        .zero),
                                                            details
                                                                .globalPosition,
                                                          );
                                                        },
                                                        onPanUpdate: (details) =>
                                                            viewModel
                                                                .dargRotatePoint(
                                                          details
                                                              .globalPosition,
                                                        ),
                                                        onPanEnd: (details) =>
                                                            viewModel
                                                                .dargRotatePointEnd(),
                                                        behavior:
                                                            HitTestBehavior
                                                                .translucent,
                                                        child: Visibility(
                                                          visible: state
                                                                  .isRedPointOverPreview ==
                                                              false,
                                                          child: Container(
                                                            padding: EdgeInsets
                                                                .all(state
                                                                    .rotateRedPoint!
                                                                    .redPointTouchOutArea),
                                                            child: Container(
                                                              alignment:
                                                                  Alignment
                                                                      .center,
                                                              width: state
                                                                  .rotateRedPoint!
                                                                  .redPointImageSize,
                                                              height: state
                                                                  .rotateRedPoint!
                                                                  .redPointImageSize,
                                                              child: Image.asset(
                                                                  "assets/images/PIC_drag_Rotate.png"),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  );
                                                } else {
                                                  /// Do nothing
                                                }
                                              });

                                              return widget;
                                            }(),
                                          ],
                                        ),
                                      ),
                                    ),

                              /// 赤いドットを使用して回転した中心点
                              state.rotateRedPoint == null
                                  ? Container()
                                  : Positioned(
                                      left: state.rotateRedPoint!.topLeft.dx,
                                      top: state.rotateRedPoint!.topLeft.dy,
                                      width: state.rotateRedPoint!.width,
                                      height: state.rotateRedPoint!.height,
                                      child: IgnorePointer(
                                        child: Align(
                                          alignment: Alignment.center,
                                          child: SizedBox(
                                            key: _centerKey,
                                            width: 0.1,
                                            height: 0.1,
                                          ),
                                        ),
                                      ),
                                    ),

                              /// キルトサッシのmask
                              state.quiltMask == null
                                  ? Container()
                                  : IgnorePointer(
                                      child: CustomPaint(
                                        painter: PathPainter(
                                          path: state.quiltMask!,
                                          color: Colors.red,
                                          strokeWidth: 1,
                                        ),
                                      ),
                                    ),

                              /// 投影範囲
                              state.isProjectorON
                                  ? const ProjectorFrameDragArea()
                                  : Container(),
                            ],
                          ),
                        ),
                      ),
                      const Spacer(flex: 1)
                    ],
                  ),
                ),
              ),
              const Spacer(flex: 236),
            ],
          ),
        ),
        const Spacer(flex: 167),
      ],
    );
  }
}
