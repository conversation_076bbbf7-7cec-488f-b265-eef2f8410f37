import 'dart:async';

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../model/device_memory_model.dart';
import '../../../../model/projector/camera_pen/camera_pen_type.dart';
import '../../../../model/projector_model.dart';
import '../../../../model/provider/task/device_error_polling_task.dart';
import '../../../app/debug_monitor/export_from_iivo.dart';
import '../../../app/utl/model/guide_line_model.dart';
import '../../../app/utl/model/pattern_data_type/pattern_property.dart';
import '../../../app/utl/model/pattern_model.dart';
import '../../../app/utl/ui/component/utl_header/utl_header_view_model.dart';
import '../../../app/utl/ui/page/sewing/common/camera_pen/camera_pen_view_model.dart';
import '../../../app/utl/ui/page/sewing/common/guide_line/guide_line_view_model.dart';
import '../../panel_popup_route.dart';
import '../err_please_wait/err_please_wait_view_model.dart';
import 'err_sr_connected_view_interface.dart';

final errSrConnectedViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrSrConnectedViewInterface, ErrSrConnectedState,
        BuildContext>((ref, context) => ErrSrConnectedViewModel(ref, context));

class ErrSrConnectedViewModel extends ErrSrConnectedViewInterface {
  ErrSrConnectedViewModel(
      AutoDisposeStateNotifierProviderRef ref, BuildContext context)
      : super(const ErrSrConnectedState(), ref, context) {
    /// GuideLinePopupを開いてもCameraが起動せず、またCameraボタンの状態も更新されない
    if (!ref.exists(guideLineViewModelProvider)) {
      /// カメラ状態を更新します
      ref
          .readAutoNotifierIfExists(utlHeaderViewModelProvider)
          ?.maybeCloseCamera();

      /// カメラボタン状態更新
      ref.readAutoNotifierIfExists(utlHeaderViewModelProvider)?.update();
    } else {
      /// DoNothing
    }
  }

  bool _markOkButtonClicked = false;

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    _markOkButtonClicked = true;
    _pushToNextPage();
  }

  ///
  /// 次のページに移行
  /// utl -> sr/sr -> utl
  ///
  Future<void> _pushToNextPage() async {
    if (PatternDataModel().getPatternMode() == PatternMode.stitchRegulator) {
      Log.e(
        tag: "ErrSrConnectedViewModel",
        description: "current page is stitch regulator,but try to go to sr",
      );
    } else {
      /// Libに通知
      TpdLibrary().apiBinding.gotoSR();
    }
  }

  ///
  /// SR画面入る前にプロジェクトを確認します
  ///
  Future<void> _checkAndHandleProjector() async {
    final bool sensingLineOnOff = ProjectorLibrary()
            .apiBinding
            .getProjectorSRSensingLineDisplay()
            .display ==
        ProjectorSRSensingLineDisp.projectorSRSensingLineDispON;
    final bool statusOnOff =
        ProjectorLibrary().apiBinding.getProjectorSRStatusDisplay().display ==
            ProjectorSRStatusDisp.projectorSRStatusDispON;

    /// GuideLine 閉じる
    if (GuideLineModel().guideLineShow == GuideLineShow.guideLineShowON) {
      await ProjectorModel().closeUtlProjector(UtlProjectorType.guideline);
      GuideLineModel().guideLineShow = GuideLineShow.guideLineShowOFF;
    }

    /// srSensingプロジェクト起動
    if (sensingLineOnOff) {
      await ProjectorModel().openUtlProjector(UtlProjectorType.srSensing);
    } else {
      /// Do Nothing
    }

    /// srStatusプロジェクト起動
    if (statusOnOff) {
      await ProjectorModel().openUtlProjector(UtlProjectorType.srStatus);
    } else {
      /// Do Nothing
    }

    /// endpointプロジェクト閉じる
    if (ProjectorModel().isUtlProjectorOpened(UtlProjectorType.endpoint)) {
      await ProjectorModel().closeUtlProjector(UtlProjectorType.endpoint);
    } else {
      /// Do Nothing
    }

    /// projectorプロジェクト閉じる
    if (ProjectorModel().isUtlProjectorOpened(UtlProjectorType.sewingPattern)) {
      await ref
          .read(cameraPenViewModelProvider.notifier)
          .closeCameraPenAndCameraPenUI(needUpdateProjectorUI: false);
      await ProjectorModel().closeUtlProjector(UtlProjectorType.sewingPattern);
    } else {
      /// Do Nothing
    }

    /// Projector更新
    if (!ProjectorModel().isUtlProjectorAllClosed()) {
      await ProjectorModel().refreshUtlProjector();
    } else {
      /// Do Nothing
    }

    return;
  }

  @override
  void dispose() {
    super.dispose();

    /// SR押さえ外出した場合なら、何もしない
    if (_markOkButtonClicked == false) {
      return;
    }

    requestLockErrorPollingTask();

    /// 必要pleaseを待ちます
    PanelPopupRoute().pushNamed(
      nextRoute: GlobalPopupRouteEnum.ERR_PLEASE_WAIT,
      arguments: ErrPleaseWaitArgument(
        build: () {
          /// 終わるまでに待ちます
          Future.wait([
            _checkAndHandleProjector(),
            Future.delayed(pleaseWaitTime),
          ]).then((_) {
            /// カメラボタン状態更新
            final utlHeaderFunction =
                ref.read(utlHeaderViewModelProvider.notifier);
            utlHeaderFunction.update();
            PanelPopupRoute()
                .maybeRemoveRoute(route: GlobalPopupRouteEnum.ERR_PLEASE_WAIT);

            /// 画面遷移
            PagesRoute().pushNamedAndRemoveUntil(
                untilRoute: PageRouteEnum.home,
                nextRoute: PageRouteEnum.stitchRegulator);

            unLockErrorPollingTask();
          });
        },
      ),
    );
  }
}
