﻿# 填写设备存储目录
$remoteDir = "/data/brother_dir/fill"
# 本地临时文件名
$localFile = "fill.bin"


function Start-Fill {
  param (
    [int]$FileSizeMB
  )
  $fileSizeBytes = $FileSizeMB * 1MB
  # 生成本地填充文件（如果文件已存在则跳过）
  if ((Test-Path $localFile)) {
    Remove-Item $localFile
  }
  fsutil file createnew $localFile $fileSizeBytes

  # 循环推送直到失败
  $index = 0
  while ($true) {
    $uuid = [guid]::NewGuid().ToString()
    $remoteFile = "$remoteDir/${uuid}.bin"
    Write-Host "推送 $FileSizeMB MB到设备： $remoteFile"
    $push = & adb push $localFile $remoteFile 2>&1

    if ($LASTEXITCODE -eq 0) {
      Write-Host "✅ 成功推送第 ${index+1} 个"
      & adb shell sync
      $index++
    }
    else {
      Write-Host "❌ 推送失败，可能空间已满"
      Write-Host $push
      return
    }
  }
}

Start-Fill 100
Start-Fill 10
Start-Fill 1

Write-Host "脚本结束。"
 
