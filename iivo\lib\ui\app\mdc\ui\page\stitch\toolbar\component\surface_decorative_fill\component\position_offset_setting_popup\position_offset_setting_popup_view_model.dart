import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_decorative_fill_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'position_offset_setting_popup_view_interface.dart';

typedef Unit = DisplayUnit;

final positionOffsetSettingPopupViewModelProvider =
    StateNotifierProvider.autoDispose<
            PositionOffsetSettingPopupStateViewInterface,
            PositionOffsetSettingPopupState>(
        (ref) => PositionOffsetSettingPopupViewModel(ref));

class PositionOffsetSettingPopupViewModel
    extends PositionOffsetSettingPopupStateViewInterface {
  PositionOffsetSettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const PositionOffsetSettingPopupState(), ref) {
    _positionOffsetState = SurfaceDecoFillModel().getPositionOffsetX() !=
                SurfaceDecoFillModel.positionOffsetXNotUpdating &&
            SurfaceDecoFillModel().getPositionOffsetY() !=
                SurfaceDecoFillModel.positionOffsetYNotUpdating
        ? DecoFillSettingState.settingCompleted
        : DecoFillSettingState.unknown;

    /// view更新
    update();
  }

  ///
  /// 最大値
  ///
  final int _maxOffsetValue = 1000;

  ///
  /// 最小値
  ///
  final int _minOffsetValue = -1000;

  ///
  /// ステップ量
  ///
  final int _stepValue = 5;

  ///
  /// 単位変換値
  ///
  final double _conversionRate = 10.0;

  ///
  /// 基点位置オフセットの状態
  ///
  DecoFillSettingState _positionOffsetState =
      DecoFillSettingState.settingCompleted;

  @override
  Unit currentSelectedUnit = DeviceInfoModel().displayUnitType;

  ///
  /// 基点位置Y値
  ///
  int _positionOffsetYValue = SurfaceDecoFillModel().getPositionOffsetY();

  ///
  /// 基点位置X値
  ///
  int _positionOffsetXValue = SurfaceDecoFillModel().getPositionOffsetX();

  @override
  void update() {
    state = state.copyWith(
      offsetYValue: _getYOffsetValue(),
      offsetXValue: _getXOffsetValue(),
      isMinYOffsetValue: _isMinYOffsetValue(),
      isMaxYOffsetValue: _isMaxYOffsetValue(),
      isMinXOffsetValue: _isMinXOffsetValue(),
      isMaxXOffsetValue: _isMaxXOffsetValue(),
    );
  }

  @override
  bool onUpButtonClicked(bool isLongPress) {
    if (_positionOffsetState == DecoFillSettingState.unknown) {
      _positionOffsetXValue =
          SurfaceDecoFillModel().positionOffsetXDefaultValue;
      _positionOffsetYValue =
          SurfaceDecoFillModel().positionOffsetYDefaultValue;
    } else {
      _positionOffsetYValue = _positionOffsetYValue - _stepValue;
      if (_positionOffsetYValue < _minOffsetValue) {
        _positionOffsetYValue = _minOffsetValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }

    ///  Model 更新
    _positionOffsetState = DecoFillSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    update();

    return true;
  }

  @override
  bool onDownButtonClicked(bool isLongPress) {
    if (_positionOffsetState == DecoFillSettingState.unknown) {
      _positionOffsetXValue =
          SurfaceDecoFillModel().positionOffsetXDefaultValue;
      _positionOffsetYValue =
          SurfaceDecoFillModel().positionOffsetYDefaultValue;
    } else {
      _positionOffsetYValue = _positionOffsetYValue + _stepValue;
      if (_positionOffsetYValue > _maxOffsetValue) {
        _positionOffsetYValue = _maxOffsetValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }

    _positionOffsetState = DecoFillSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    update();
    return true;
  }

  @override
  bool onLeftButtonClicked(bool isLongPress) {
    if (_positionOffsetState == DecoFillSettingState.unknown) {
      _positionOffsetXValue =
          SurfaceDecoFillModel().positionOffsetXDefaultValue;
      _positionOffsetYValue =
          SurfaceDecoFillModel().positionOffsetYDefaultValue;
    } else {
      _positionOffsetXValue = _positionOffsetXValue - _stepValue;

      if (_positionOffsetXValue < _minOffsetValue) {
        _positionOffsetXValue = _minOffsetValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }

    ///  Model 更新

    _positionOffsetState = DecoFillSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    update();
    return true;
  }

  @override
  bool onRightButtonClicked(bool isLongPress) {
    if (_positionOffsetState == DecoFillSettingState.unknown) {
      _positionOffsetXValue =
          SurfaceDecoFillModel().positionOffsetXDefaultValue;
      _positionOffsetYValue =
          SurfaceDecoFillModel().positionOffsetYDefaultValue;
    } else {
      _positionOffsetXValue = _positionOffsetXValue + _stepValue;

      if (_positionOffsetXValue > _maxOffsetValue) {
        _positionOffsetXValue = _maxOffsetValue;
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }
    }

    _positionOffsetState = DecoFillSettingState.change;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// view更新
    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref.read(stitchPageViewModelProvider.notifier).maybeRemovePopupRoute(
        PopupEnum.surfaceDecorativeFillPositionOffset.toString());
    if (_positionOffsetState == DecoFillSettingState.unknown) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    /// PositionOffsetXの初期値
    int oldPositionOffsetXValue = SurfaceDecoFillModel().getPositionOffsetX();

    /// PositionOffsetYの初期値
    int oldPositionOffsetYValue = SurfaceDecoFillModel().getPositionOffsetY();

    /// Model 更新
    SurfaceDecoFillModel()
        .setPositionOffset(_positionOffsetYValue, _positionOffsetXValue);
    if (SurfaceDecoFillModel().setDecorativeFillSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (oldPositionOffsetXValue != _positionOffsetXValue ||
        oldPositionOffsetYValue != _positionOffsetYValue) {
      ResumeHistoryModel().backSnapshot();
    } else {
      /// do nothing
    }

    _positionOffsetState = DecoFillSettingState.settingCompleted;
    CreationModel().changeStitchCreation();
  }

  @override
  bool getOffsetYDefault() => SurfaceDecoFillModel().getDisplayDefault(
      _positionOffsetState,
      _positionOffsetYValue,
      SurfaceDecoFillModel().positionOffsetYDefaultValue);

  @override
  bool getOffsetXDefault() => SurfaceDecoFillModel().getDisplayDefault(
      _positionOffsetState,
      _positionOffsetXValue,
      SurfaceDecoFillModel().positionOffsetXDefaultValue);

  ///
  /// 基点位置オフセット：縦方向の値を取得する
  ///
  String _getYOffsetValue() {
    /// cmからmmへ
    double value = _positionOffsetYValue / _conversionRate;

    if (_positionOffsetState == DecoFillSettingState.unknown) {
      if (currentSelectedUnit == Unit.mm) {
        return SurfaceDecoFillModel.differentMmValue;
      } else {
        return SurfaceDecoFillModel.differentInchValue;
      }
    } else {
      if (currentSelectedUnit == Unit.mm) {
        return value.toStringAsFixed(1);
      }
      return ToolbarModel.getDisplayInchShowValue(value);
    }
  }

  ///
  /// 基点位置オフセット：横方向の値を取得する
  ///
  String _getXOffsetValue() {
    /// cmからmmへ
    double value = _positionOffsetXValue / _conversionRate;

    if (_positionOffsetState == DecoFillSettingState.unknown) {
      if (currentSelectedUnit == Unit.mm) {
        return SurfaceDecoFillModel.differentMmValue;
      } else {
        return SurfaceDecoFillModel.differentInchValue;
      }
    } else {
      if (currentSelectedUnit == Unit.mm) {
        return value.toStringAsFixed(1);
      }
      return ToolbarModel.getDisplayInchShowValue(value);
    }
  }

  ///
  /// 最大縦方向オフセット値の判断
  ///
  bool _isMaxYOffsetValue() {
    if (_positionOffsetYValue ==
        SurfaceDecoFillModel.positionOffsetYNotUpdating) {
      return true;
    }
    if (_positionOffsetState == DecoFillSettingState.unknown) {
      return false;
    } else {
      return _positionOffsetYValue == _maxOffsetValue ? false : true;
    }
  }

  ///
  /// 最小縦方向オフセット値の判断
  ///
  bool _isMinYOffsetValue() {
    if (_positionOffsetYValue ==
        SurfaceDecoFillModel.positionOffsetYNotUpdating) {
      return true;
    }
    if (_positionOffsetState == DecoFillSettingState.unknown) {
      return false;
    } else {
      return _positionOffsetYValue == _minOffsetValue ? false : true;
    }
  }

  ///
  /// 最大横方向オフセット値の判断
  ///
  bool _isMaxXOffsetValue() {
    if (_positionOffsetXValue ==
        SurfaceDecoFillModel.positionOffsetXNotUpdating) {
      return true;
    }
    if (_positionOffsetState == DecoFillSettingState.unknown) {
      return false;
    } else {
      return _positionOffsetXValue == _maxOffsetValue ? false : true;
    }
  }

  ///
  /// 最小横方向オフセット値の判断
  ///
  bool _isMinXOffsetValue() {
    if (_positionOffsetXValue ==
        SurfaceDecoFillModel.positionOffsetXNotUpdating) {
      return true;
    }
    if (_positionOffsetState == DecoFillSettingState.unknown) {
      return false;
    } else {
      return _positionOffsetXValue == _minOffsetValue ? false : true;
    }
  }
}
