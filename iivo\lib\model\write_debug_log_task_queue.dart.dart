import 'dart:io';

import 'package:log/log.dart';
import 'package:path/path.dart';
import 'package:synchronized/synchronized.dart';
import 'package:usb_manager/usb_manager.dart';
import 'package:uuid/uuid.dart';

import '../memory/memory.dart';
import 'device_memory_model.dart';

class WriteDebugLogTaskQueue {
  ///
  /// task(ログをUSBにコピーする)実行する
  /// (今の追加したタスクは、前のtaskまだ完成した後に実行する)
  /// task: 実行する必要がある関数
  ///
  Future<void> saveDebugLogToUSB() async {
    final String taskId = _taskUUID.v4();

    if (_writeDebugLogTaskInfo.length >= _runningTaskMax) {
      _writeDebugLogTaskInfo.remove(_writeDebugLogTaskInfo.last);
    }

    _writeDebugLogTaskInfo.add(taskId);

    final Future future = _writeLogLock.synchronized(() async {
      try {
        if (_writeDebugLogTaskInfo.any((element) => element == taskId) ==
            false) {
          Log.d(
              tag: _tag,
              description: "skip writeDebugLog frame when skip $taskId");
          return;
        }

        await _saveDebugLogToUSB();
      } catch (e, m) {
        Log.exception(error: e.toString(), message: m.toString());
        Log.e(
            tag: _tag,
            description: "skip writeDebugLog frame when exception $taskId");
      }
      _writeDebugLogTaskInfo.removeWhere((element) => element == taskId);
      Log.d(
          tag: _tag,
          description:
              "writeDebugLog frame task($taskId) finish,taskList:${_writeDebugLogTaskInfo.map((e) => e).toSet()}");
    });
    return await future;
  }

  ///
  /// ログをUSBにコピーする
  ///
  Future<void> _saveDebugLogToUSB() async {
    Log.d(tag: _tag, description: 'saveDebugLogToUSB start');

    try {
      final List<USBInfo> usbList = await UsbManager().getUsbInfoList();

      if (usbList.isEmpty) {
        Log.e(tag: _tag, description: 'USB media is not loaded');
        return;
      }

      final saveUsbPath = usbList.first.usbPath;
      await DeviceMemoryModel().beginUsbTransaction(saveUsbPath, () async {
        /// 機械内に日誌を保存するフォルダ
        final Directory sourceDirectory = Directory(_debugLogSavePath);
        if (sourceDirectory.existsSync() == false) {
          Log.e(tag: _tag, description: '${sourceDirectory.path} is not exist');
          return;
        }

        /// 圧縮を必要とするフォルダ集合
        final List<String> sourceFolderPathList = [];

        /// 一時ファイルのパス
        final String tempFilePath = join(saveUsbPath, _tempFileName);

        /// 出力先のZIPファイルパス（任意のディレクトリ）
        final String destinationPath = join(saveUsbPath, _logName);

        /// USB内に存在する同名のファイルと同名のフォルダを削除
        _deleteSameNameFilesAndFolders(tempFilePath);
        _deleteSameNameFilesAndFolders(destinationPath);

        for (var element in sourceDirectory.listSync()) {
          if (element is Directory) {
            sourceFolderPathList.add(element.path);
          } else {
            /// do nothing
          }
        }

        if (sourceFolderPathList.isEmpty) {
          Log.e(tag: _tag, description: 'sourceFolderPathList is isEmpty');
          return;
        }

        bool isSuccess = await UsbManager()
            .compressFolder(sourceFolderPathList, tempFilePath, _zipPassword);

        if (isSuccess) {
          Log.d(tag: _tag, description: 'saveDebugLogToUSB end:$isSuccess');
          File tempFile = File(tempFilePath);
          tempFile.renameSync(destinationPath);
          Log.d(tag: _tag, description: 'tempFile rename end');
        } else {
          Log.e(tag: _tag, description: 'saveDebugLogToUSB end:$isSuccess');
        }
      });
    } catch (e) {
      Log.e(tag: _tag, description: 'saveDebugLogToUSB error:$e');
    }
  }

  ///
  /// USB内に存在する同名のファイルと同名のフォルダを削除
  ///
  void _deleteSameNameFilesAndFolders(String path) {
    final FileSystemEntityType type = FileSystemEntity.typeSync(path);

    if (type == FileSystemEntityType.directory) {
      final Directory destinationDirectory = Directory(path);
      if (destinationDirectory.existsSync()) {
        destinationDirectory.deleteSync(recursive: true);
      } else {
        /// do nothing
      }
    } else {
      final FileEntity destinationFile = FileEntity(path);
      if (destinationFile.existsSync()) {
        destinationFile.deleteSync(recursive: true);
      } else {
        /// do nothing
      }
    }
  }

  ///
  /// USBメモリへのログ出力機能
  ///
  static WriteDebugLogTaskQueue getInstance() => _instance;
  static final _instance = WriteDebugLogTaskQueue();

  /// task一覧
  final Set<String> _writeDebugLogTaskInfo = {};
  final Lock _writeLogLock = Lock();
  final Uuid _taskUUID = const Uuid();

  /// 実行可能な最大タスク数
  final int _runningTaskMax = 2;

  ///
  /// ログを保存するフォルダ名
  ///
  static const String _logName = "log";

  ///
  /// 一時ファイル名
  ///
  static const String _tempFileName = "logTemp";

  ///
  /// パスワード
  ///
  static const String _zipPassword = "iM7dgrOwHIuytt0wTTAB0LNlJ4mLIo0t";

  ///
  /// ログの保存パス
  ///
  final String _debugLogSavePath =
      join(memorySector.brother_dir.absolutePath, "dbglog");

  ///
  /// tag
  ///
  static const String _tag = "writeDebugLogTag";
}
