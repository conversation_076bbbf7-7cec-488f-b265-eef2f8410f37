import 'package:common_component/common_component.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'parameter_set_view_model.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'parameter_set_interface.freezed.dart';

@freezed
abstract class ParameterSetState with _$ParameterSetState {
  factory ParameterSetState({
    @Default(true) bool isMultiCharSelectMode,
    @Default(null) Offset? redLinePosition,
    @Default(null) Rect? dashedBoxRect,
    @Default(ButtonState.normal) ButtonState leftMoveButtonState,
    @Default(ButtonState.normal) ButtonState rightMoveButtonState,
    @Default(ButtonState.normal) ButtonState realPreviewButtonState,
    @Default([]) List<ShowDataList> disPlayText,
    required String fontType,
    @Default(null) Widget? fontTypeImage,
    @Default("") String fontSize,
    @Default(0) int frameIndex,
    @Default("----") String patternWidth,
    @Default("----") String patternHight,
    @Default(0) int zoomScaleIndex,
    @Default(false) bool isZoomPopupOn,
    @Default(false) bool isHandleSelected,
    @Default(false) bool isInch,
    @Default(false) bool isExclusiveType,
  }) = _ParameterSetState;
}

abstract class ParameterSetViewInterface extends ViewModel<ParameterSetState> {
  ParameterSetViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  Ref ref;

  ///
  /// 表示倍率値リスト
  ///
  List<int> get zoomDisplayList;

  ///
  /// SelectAllクリックイベント
  ///
  void onSelectAllButtonClicked();

  ///
  /// クリックイベント
  ///
  void onOneLetterButtonClicked();

  ///
  /// 予約は更新のために外部に返却されます
  ///
  @override
  void update();

  ///
  /// フォント選択ポップアップを開く
  ///
  void onOpenFontPopupClicked(BuildContext context);

  ///
  /// カーソルの移動位置を取得する(左)
  ///
  void onLeftButtonOnClicked();

  ///
  /// カーソルの移動位置を取得する（右）
  ///
  void onRightButtonOnClicked();

  ///
  /// マイナスボタンクリックイベント(-)
  ///
  bool onMinusButtonOnClicked(bool isLongPress);

  ///
  ///  プラスボタンのクリックイベント(+)
  ///
  bool onPlusButtonOnClicked(bool isLongPress);

  ///
  /// 倍率選択ポップアップのZoom Buttonのクリック関数
  ///
  void onZoomPopupButtonClicked(int zoomValue);

  ///
  /// 倍率選択ポップアップを開くためのクリック関数
  ///
  void onZoomButtonClicked();

  ///
  /// 倍率選択ポップアップを閉じるためのクリック関数
  ///
  void closeZoomPopup();

  ///
  /// パンツール（ハンド）のクリック関数
  ///
  void onHandleButtonClicked();

  ///
  /// リアルプレビューのクリック関数
  ///
  void onRealPreviewClicked();
}
