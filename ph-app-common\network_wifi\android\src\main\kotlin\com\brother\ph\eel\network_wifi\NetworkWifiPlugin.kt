package com.brother.ph.eel.network_wifi


import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.annotation.UiThread
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import org.json.JSONObject


/** NetworkWifiPlugin */
@RequiresApi(Build.VERSION_CODES.M)
class NetworkWifiPlugin : FlutterPlugin, ActivityAware, MethodCallHandler,
  EventChannel.StreamHandler {
  private val debugLogTag = "NetworkWifiPlugin"

  private lateinit var methodChannel: MethodChannel
  private lateinit var eventChannel: EventChannel
  private lateinit var applicationContext: Context

  private lateinit var activityPluginBinding: ActivityPluginBinding
  private var eventSink: EventChannel.EventSink? = null
  private lateinit var wifiControl: WifiControl
  private lateinit var wifiScan: WifiScan
  private lateinit var wifiInfo: WifiInfo

  override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
    applicationContext = flutterPluginBinding.applicationContext
    wifiScan = WifiScan(applicationContext)
    wifiControl = WifiControl(applicationContext)
    wifiInfo = WifiInfo(applicationContext)

    // method channel
    methodChannel =
      MethodChannel(flutterPluginBinding.binaryMessenger, "network_wifi_method_channel")
    methodChannel.setMethodCallHandler(this)

    // event channel
    eventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "network_wifi_event_channel")
    eventChannel.setStreamHandler(this)
  }

  override fun onAttachedToActivity(binding: ActivityPluginBinding) {
    activityPluginBinding = binding
  }

  override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
    methodChannel.setMethodCallHandler(null)
    eventChannel.setStreamHandler(null)
    eventSink?.endOfStream()
    eventSink = null
    wifiScan.dispose()
    wifiControl.dispose()
  }

  override fun onDetachedFromActivityForConfigChanges() {}
  override fun onDetachedFromActivity() {}

  override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
    activityPluginBinding = binding
  }

  override fun onMethodCall(call: MethodCall, result: Result) {
    val debugLogTag = "onMethodCall"
    when (call.method) {
      // Page
      "startActivityForResult" -> {
        if (call.arguments !is String) {
          Log.e(debugLogTag, "incorrect arguments")
          result.success(null)
          return
        }

        startActivityForResult(call.arguments as String) {
          result.success(null)
        }
      }
      // wifi Control
      "setWifiEnable" -> {
        if (call.arguments !is Boolean) {
          Log.e(debugLogTag, "incorrect arguments")
          result.success(false)
          return
        }
        val enable = call.arguments as Boolean
        result.success(wifiControl.setWifiEnable(enable))
      }

      "connect" -> {
        try {
          val ssid: String = call.argument<String>("ssid")!!
          val bssid: String? = call.argument<String>("bssid")
          val password: String = call.argument<String>("password")!!
          val security: String = call.argument<String>("security")!!
          call.argument<Boolean>("joinOnce")!!
          val isHidden: Boolean = call.argument<Boolean>("isHidden")!!
          val timeoutInSeconds: Int = call.argument<Int>("timeoutInSeconds")!!
          wifiControl.connect(
            ssid,
            bssid,
            password,
            security,
            isHidden,
            timeoutInSeconds
          ) { connectResult: Boolean ->
            val handler = Handler(Looper.getMainLooper())
            handler.post {
              result.success(connectResult)
            }
          }
        } catch (e: Exception) {
          Log.e(debugLogTag, "incorrect arguments")
          result.success(false)
        }
      }

      "startWifiScanAndGetResult" -> {
        if (call.arguments is Int) {
          val timeOutInSecond: Int = call.arguments as Int
          wifiScan.startWifiScanAndGetResult(timeOutInSecond) { scanResult ->
            val handler = Handler(Looper.getMainLooper())
            handler.post {
              result.success(scanResult)
            }
          }
        } else {
          Log.e(debugLogTag, "incorrect arguments")
          result.success(null)
        }
      }

      "cancelWifiScan" -> {
        result.success(wifiScan.cancelWifiScan())
      }

      "disConnect" -> {
        result.success(wifiControl.disConnect())
      }

      "isWifiEnable" -> {
        result.success(wifiInfo.isWifiEnable())
      }

      "getSsid" -> {
        result.success(wifiInfo.getSsid())
      }

      "getRssi" -> {
        result.success(wifiInfo.getRssi())
      }

      "getIpAddress" -> {
        result.success(wifiInfo.getIpAddress())
      }

      "getLinkSpeed" -> {
        result.success(wifiInfo.getLinkSpeed())
      }

      "getAuth" -> {
        result.success(wifiInfo.getAuth())
      }

      "getEncryptionMode" -> {
        result.success(wifiInfo.getEncryptionMode())
      }

      "getFrequency" -> {
        result.success(wifiInfo.getFrequency())
      }

      "getBssid" -> {
        result.success(wifiInfo.getBssid())
      }

      "getMacAddress" -> {
        result.success(wifiInfo.getMacAddress())
      }

      "getWifiInfo" -> {
        result.success(JSONObject(wifiInfo.getConnectionInfo()).toString())
      }

      "getDhcpInfo" -> {
        result.success(JSONObject(wifiInfo.getDhcpInfo()).toString())
      }

      "isConnected" -> {
        result.success(wifiInfo.isConnected())
      }

      "checkIpState" -> {
        result.success(wifiInfo.checkIpState())
      }

      "getBootMethod" -> {
        result.success(wifiInfo.getBootMethod())
      }

      "isProxyEnabled" -> {
        result.success(wifiInfo.isProxyEnabled(applicationContext))
      }

      "getProxyAddress" -> {
        result.success(wifiInfo.getProxyAddress(applicationContext))
      }

      "getProxyPort" -> {
        result.success(wifiInfo.getProxyPort(applicationContext))
      }

      "clearNetWorkSetting" -> {
        result.success(wifiInfo.clearNetWorkSetting(applicationContext))
      }

      "getDeviceMacAddress" -> {
        result.success(NetworkUtil.getDeviceMacAddress())
      }

      else -> result.notImplemented()
    }
  }

  // EventChannel
  override fun onListen(arguments: Any?, events: EventChannel.EventSink) {
    eventSink = events
    wifiControl.setWifiEventCallBack(WifiEvent(events))
    Log.d("Wifi Event channel", "onListen")
  }

  override fun onCancel(arguments: Any?) {
    wifiControl.setWifiEventCallBack(null)
    eventSink = null
    Log.d("Wifi Event channel", "onCancel")
  }


  private fun startActivityForResult(action: String, callback: () -> Unit) {
    try {
      val intent = Intent()
      intent.action = action

      activityPluginBinding.addActivityResultListener(object :
        io.flutter.plugin.common.PluginRegistry.ActivityResultListener {
        override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean {
          activityPluginBinding.removeActivityResultListener(this)
          callback()
          return false
        }
      })
      activityPluginBinding.activity.startActivityForResult(intent, 0)
    } catch (e: Exception) {
      Log.e(debugLogTag, "openWifiSettings,Exception:$e")
      return
    }
  }


  private class WifiEvent(val eventSink: EventChannel.EventSink) : WifiControl.WifiEventCallBack {
    @UiThread
    override fun onWifiEnabled() {
      val handler = Handler(Looper.getMainLooper())
      handler.post {
        eventSink.success(
          JSONObject(
            mapOf(
              "event" to "onWifiEnabled",
            ),
          ).toString()
        )
      }
    }

    @UiThread
    override fun onWifiDisabled() {
      val handler = Handler(Looper.getMainLooper())
      handler.post {
        eventSink.success(
          JSONObject(
            mapOf(
              "event" to "onWifiDisabled",
            ),
          ).toString()
        )
      }
    }

    @UiThread
    override fun onWifiAvailable(wifiEventInfo: WifiEventInfo) {
      val handler = Handler(Looper.getMainLooper())
      handler.post {
        eventSink.success(
          JSONObject(
            mapOf(
              "event" to "onWifiAvailable",
              "parameter" to wifiEventInfo.toMap()
            ),
          ).toString(),
        )
      }
    }

    @UiThread
    override fun onWifiLost() {
      val handler = Handler(Looper.getMainLooper())
      handler.post {
        eventSink.success(
          JSONObject(
            mapOf(
              "event" to "onWifiLost",
            ),
          ).toString()
        )
      }
    }

    @UiThread
    override fun onWifiUnavailable() {
      val handler = Handler(Looper.getMainLooper())
      handler.post {
        eventSink.success(
          JSONObject(
            mapOf(
              "event" to "onWifiAvailable",
            ),
          ).toString()
        )
      }
    }

    override fun onWifiCapabilitiesChanged(wifiEventInfo: WifiEventInfo) {
      val handler = Handler(Looper.getMainLooper())
      handler.post {
        eventSink.success(
          JSONObject(
            mapOf(
              "event" to "onWifiCapabilitiesChanged",
              "parameter" to wifiEventInfo.toMap()
            ),
          ).toString()
        )
      }
    }

    override fun onWifiLinkPropertiesChanged(wifiEventInfo: WifiEventInfo) {
      val handler = Handler(Looper.getMainLooper())
      handler.post {
        eventSink.success(
          JSONObject(
            mapOf(
              "event" to "onWifiLinkPropertiesChanged",
              "parameter" to wifiEventInfo.toMap()
            ),
          ).toString()
        )
      }
    }
  }
}
