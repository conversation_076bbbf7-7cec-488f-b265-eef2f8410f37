import 'dart:io';

import 'package:network_wifi/network_wifi.dart';
import 'package:panel_library/panel_library.dart';
import 'package:path/path.dart';

import '../../../../memory/memory.dart';
import '../../../../model/device_memory_model.dart';
import '../../../../model/handel_model.dart';
import '../../../../model/machine_config_model.dart';
import '../../../../model/setting_model.dart';
import '../../../../model/user_data/user_data.dart';
import '../../../../model/user_data/user_data_keys.dart';
import '../../../../network/network_ctrl.dart';
import '../../../../network/server/http/certificate_manager.dart';
import '../../../../network/server/http/route/artspira/common.dart';
import '../../../../network/upgrade/upgrade.dart';
import 'emb_setting_page1_model.dart';
import 'emb_setting_page2_model.dart';
import 'emb_setting_page3_model.dart';
import 'general_setting_page1_model.dart';
import 'general_setting_page2_model.dart';
import 'general_setting_page3_model.dart';
import 'sewing_setting_page1_model.dart';
import 'sewing_setting_page2_model.dart';
import 'sewing_setting_page3_model.dart';

class GeneralSettingPage4Model {
  GeneralSettingPage4Model._internal();

  factory GeneralSettingPage4Model() => _instance;
  static final GeneralSettingPage4Model _instance =
      GeneralSettingPage4Model._internal();

  ///
  /// 数値入力のlist
  ///
  static final List digitInputList =
      List.generate(10, (index) => ((index + 1) % 10).toString());

  ///
  /// 英語入力のlist
  ///
  static const List englishInputList = [
    "Q",
    "W",
    "E",
    "R",
    "T",
    "Y",
    "U",
    "I",
    "O",
    "P",
    "A",
    "S",
    "D",
    "F",
    "G",
    "H",
    "J",
    "K",
    "L",
    "Z",
    "X",
    "C",
    "V",
    "B",
    "N",
    "M",
  ];

  static String activationCodeValue = "";

  ///*******************************************************************
  /// utlのデータのデフォルト値
  ///*******************************************************************

  ///
  /// [buttonhole] Interval Distance
  ///
  static const int guideLineBHIntervalDistanceDefault = 184;

  ///
  /// [buttonhole] Cloth Edge Distance
  ///
  static const int guideLineBHClothEdgeDistanceDefault = 138;

  ///
  /// CLine/3Line Position Horizontal（←→） [mm]
  ///
  static const int guideLineExLinePositionHorizontalDefault = 69;

  ///
  /// Circle lines Radius Size
  ///
  static const int guideLineCLinesRadiusSizeDefault = 80;

  ///
  /// 3 lines Grid Size
  ///
  static const int guideLine3LinesGridSizeDefault = 92;

  ///
  /// ステッチレギュレータの押え高さのデフォルト値
  ///
  static const int srPresserFootHeightDefault = 15;

  ///
  /// アクティブコードの検証状態を取得
  ///
  ActivationCodeConfirmStatus activationCodeConfirmStatus =
      ActivationCodeConfirmStatus.activateSuccess;

  ///
  /// ユーザー情報のリセット開始
  ///
  void startAllUserSetting() =>
      DeviceLibrary().apiBinding.startAllUserSetting();

  ///
  /// ユーザー情報のリセット完了
  ///
  void finishAllUserSetting() =>
      DeviceLibrary().apiBinding.finishAllUserSetting();

  DeviceSettingInfo getDeviceSettingInfo() =>
      DeviceLibrary().apiBinding.getDeviceSettingInfo().settingInfo;

  ///
  /// User情報初期アクション実行フラグ設定
  /// 動作実行後各機能を無効にする
  /// [value] : true(User情報初期アクション実行) , false(User情報初期アクション実行ない)
  ///
  void setUserInfoResetExecFlag() {
    MachineConfigModel().isResetUserInfoExec = true;
  }

  ///
  /// すべてのデータをリセット
  ///
  void resetAllData() {
    ///*******************************************************************
    /// utlのデータをリセット
    ///*******************************************************************

    ///  BH Intervalの距離をデフォルト値に設定します
    ProjectorLibrary().apiBinding.setProjectorUtlBHSubLineBHIntervalDistance(
        guideLineBHIntervalDistanceDefault);

    /// BH Cloth Edge Distanceの距離をデフォルト値に設定します
    ProjectorLibrary().apiBinding.setProjectorUtlBHSubLineBHClothEdgeDistance(
        guideLineBHClothEdgeDistanceDefault);

    /// Circle、3Lineの横方向ポジションをデフォルト値に設定します
    ProjectorLibrary()
        .apiBinding
        .setProjectorUtlSubLineExLinePositionHorizontal(
            guideLineExLinePositionHorizontalDefault);

    /// Circleの内円半径サイズをデフォルト値に設定します
    ProjectorLibrary().apiBinding.setProjectorUtlSubLineCircleRadiusSize(
        guideLineCLinesRadiusSizeDefault);

    /// 3Lineのグリッドサイズをデフォルト値に設定します
    ProjectorLibrary()
        .apiBinding
        .setProjectorUtlSubLine3LineGridSize(guideLine3LinesGridSizeDefault);

    /// ステッチレギュレータ押え高さのデフォルト値に設定します
    DeviceLibrary()
        .apiBinding
        .setSrPresserFootHeight(srPresserFootHeightDefault);
    UserData().setInt(
        UserDataKeys.utlSRPresserFootHeight, srPresserFootHeightDefault);
  }

  ///
  /// ユーザー情報初期化
  ///
  Future<void> userReset() async {
    /// 初期化中フラグをSet
    GeneralSettingPage4Model().startAllUserSetting();

    GeneralSettingPage4Model().setUserInfoResetExecFlag();

    await Future.delayed(const Duration(microseconds: 100));

    ///
    /// 各種機能の無効化
    /// TBD
    /// スクリーンセーバーOFF
    /// スリープ機能OFF
    /// シャットオフサポートモードOFF
    ///
    /// メカキーを拒否

    ///
    /// WLAN無効
    ///
    await closeNetWork();

    /// 証明書を削除
    CertificateManager.deleteCertificate();
    await Future.delayed(const Duration(microseconds: 100));

    ///
    /// ミシン管理情報管理タスクを止める
    /// NetDomainタスクを止める
    ///
    /// "sda3/tmp.pmp"の削除
    ///

    DeviceLibrary().apiBinding.resetAllUserSetting();
    await Future.delayed(const Duration(microseconds: 100));

    /// テストモード44の初期化処理の実行
    SewingSettingPage1Model().resetSettingsUtilityPage1();
    SewingSettingPage2Model().resetSettingsUtilityPage2();
    SewingSettingPage3Model().resetSettingsUtilityPage3();

    GeneralSettingPage1Model().resetSettingsGeneralPage1();
    GeneralSettingPage2Model().resetSettingsGeneralPage2();
    GeneralSettingPage3Model().resetSettingsGeneralPage3();
    GeneralSettingPage4Model().resetAllData();

    /// TBD  Couching Speed/Max Embroidery Speed/Foot Height/Tension Height reset
    /// ParameterDefaultChkTableを参照
    EmbSettingPage1Model().resetSettingsEmbPage1();
    EmbSettingPage2Model().resetSettingsEmbPage2();
    EmbSettingPage3Model().resetSettingsEmbPage3();
    await Future.delayed(const Duration(microseconds: 100));
    //////////////////////////////////////////////////////////////
    //////////////////////Emb //////////////////////////////
    //////////////////////////////////////////////////////////////
    SettingModel().resetEmbUserInfo();
    await Future.delayed(const Duration(microseconds: 100));

    //////////////////////////////////////////////////////////////
    //////////////////////Utl //////////////////////////////
    //////////////////////////////////////////////////////////////
    /// Double Needle
    /// Utl Spacing
    /// Projector Brightness
    SettingModel().resetUtlUserInfo();
    await Future.delayed(const Duration(microseconds: 100));

    //////////////////////////////////////////////////////////////
    //////////////////////MDC //////////////////////////////
    //////////////////////////////////////////////////////////////
    SettingModel().resetMDCUserInfo();

    //////////////////////////////////////////////////////////////
    //////////////////////マシン設定関連 //////////////////////////
    //////////////////////////////////////////////////////////////
    ///
    /// TBD
    /// VerUp for install movie
    /// Clock初期化
    ///
    SettingModel().resetInitialPopupSetting();

    /// ネットワーク設定初期化
    /// 設備名称の初期化
    Upgrade().resetWlanUpdateStatus(isUpdated: false);
    _resetWlanSetting();
    _resetWlanSetupConfirmation();
    _clearManualMemoryData();

    DeviceLibrary().apiBinding.setEulaInitStatus(EULAInitStatusEnum.unInit);

    /// Connection Server1
    /// Connection Server2
    /// Connection Server3
    _resetConnectionServerSetting();

    /// PINコード登録情報をリセット
    clearPinCodeRegisterInfo();

    _clearMemoryFile();

    await Future.delayed(const Duration(seconds: 1));

    /// 初期化完了のフラグをセット
    GeneralSettingPage4Model().finishAllUserSetting();
  }

  // --------------------------------
  // manual memory data clear operate
  // --------------------------------
  void _clearManualMemoryData() => UserData().clear();

  ///
  /// 既定のWlan Settingの値をリセットする
  ///
  void _resetWlanSetting() {
    String productNo =
        DeviceLibrary().apiBinding.getDeviceSettingInfo().settingInfo.productNo;
    String machineName =
        "SewingMachine${productNo.substring(productNo.length - 3, productNo.length)}";
    DeviceLibrary().apiBinding.setWlanMachineName(machineName);
    DeviceLibrary().apiBinding.setFirmDownloadTimestamp("00000000");
    DeviceLibrary().apiBinding.setAppBadgeSenju(1);
  }

  ///
  /// WlanSetupConfirmationのデータを消去する
  ///
  void _resetWlanSetupConfirmation() {
    DeviceLibrary()
        .apiBinding
        .setWLanGuideSettingStatus(WlanGuideSettingStatusEnum.unInit);

    /// Androidシステムに保存されているWi-Fi情報をクリアする
    WifiInfo().clearNetWorkSetting();
  }

  ///
  /// 既定のServer Settingの値をリセットする
  ///
  void _resetConnectionServerSetting() {
    DeviceLibrary().apiBinding.setCanvasServer(0);
    DeviceLibrary().apiBinding.setKitServer(0);
    DeviceLibrary().apiBinding.setSncKitServer(0);
  }

  ///
  /// 保存しているファイルを削除
  ///
  void _clearMemoryFile() {
    //////////////////////////////////////////////////////////////
    //////////////////////Emb //////////////////////////////
    //////////////////////////////////////////////////////////////
    /// 刺繍模様の外形スタンプ
    _deleteFile(memorySector.emb_MDC.absolutePath);

    /// 刺繍の保存データデータ
    _deleteFile(memorySector.embMem.absolutePath);

    /// 刺繍模様編集のundo/redoのデータ
    _deleteFile(memorySector.embUdo.absolutePath);

    /// 刺繍のレジュームデータ
    _deleteFile(memorySector.resume.absolutePath);

    //////////////////////////////////////////////////////////////
    //////////////////////MDC //////////////////////////////
    //////////////////////////////////////////////////////////////
    /// CWSからダウンロードした刺繍模様の外形スタンプ
    _deleteFile(memorySector.mdcCws.absolutePath);

    /// MDCユーザー作成模様データ
    _deleteFile(memorySector.mdcImp.absolutePath);

    /// MDCの保存データ
    _deleteFile(memorySector.mdcMem.absolutePath);

    /// ＭＤＣのスキャン画像
    _deleteFile(memorySector.mdcScn.absolutePath);

    /// MDCのundo/redoのデータ
    _deleteFile(memorySector.mdcUdo.absolutePath);

    //////////////////////////////////////////////////////////////
    //////////////////////マシン設定関連 //////////////////////////
    //////////////////////////////////////////////////////////////
    /// スキャン画像
    _deleteFile(memorySector.scnImg.absolutePath);

    /// ユーザーが保存したスクリーンセーバ
    _deleteFile(memorySector.usrSs.absolutePath);

    //////////////////////////////////////////////////////////////
    //////////////////////Utl //////////////////////////////
    //////////////////////////////////////////////////////////////
    /// Character Decorative Stitchのデータ
    _deleteFile(memorySector.utlMem.absolutePath);

    //////////////////////////////////////////////////////////////
    //////////////////////その他 //////////////////////////////
    //////////////////////////////////////////////////////////////

    /// WLAN通信用データ
    _deleteFile(memorySector.net.absolutePath);

    /// WLAN通信用データ
    _deleteFile(join(memorySector.net.absolutePath, "app"));

    /// WLAN通信用データ
    _deleteFile(join(memorySector.net.absolutePath, "senju"));

    /// JSONファイル
    _deleteFile(memorySector.sim_memory_emb.absolutePath);
    _deleteFile(memorySector.sim_memory_projector.absolutePath);
    _deleteFile(memorySector.sim_memory_setting.absolutePath);
    _deleteFile(memorySector.sim_memory_utl.absolutePath);
  }

  ///
  /// 指定されたフォルダ内のすべてのファイルを削除する
  ///
  void _deleteFile(String directoryPath) {
    try {
      final memoryEntity = DeviceMemoryModel().directoryWalk(
        DirectoryEntity(directoryPath),
        null,
      );

      if (memoryEntity.isNotEmpty) {
        for (var entity in memoryEntity) {
          if (entity is FileEntity) {
            entity.deleteSync();
          } else {
            /// Do Nothing
          }
        }
      }
    } on IOException catch (e) {
      HandelModel.handleMemoryAccessError(memoryExceptionToAccessError(e));
    }
  }
}
