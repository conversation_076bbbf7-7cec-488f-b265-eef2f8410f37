import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_emb_frame_hold_lever_down_power_on_view_interface.dart';

final errEmbFrameHoldLeverDownPowerOnViewModelProvider =
    StateNotifierProvider.family.autoDispose<
            ErrEmbFrameHoldLeverDownPowerOnViewInterface,
            ErrEmbFrameHoldLeverDownPowerOnState,
            BuildContext>(
        (ref, context) =>
            ErrEmbFrameHoldLeverDownPowerOnViewModel(ref, context));

class ErrEmbFrameHoldLeverDownPowerOnViewModel
    extends ErrEmbFrameHoldLeverDownPowerOnViewInterface {
  ErrEmbFrameHoldLeverDownPowerOnViewModel(Ref ref, BuildContext context)
      : super(const ErrEmbFrameHoldLeverDownPowerOnState(), ref, context);

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    final int errCode = TpdLibrary()
        .apiBinding
        .bpIFSendDisplayDataSync(BPIFSendKey.KEYERROREMBFRAMEHOLDLEVERDOWNPOWERON);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }
}
