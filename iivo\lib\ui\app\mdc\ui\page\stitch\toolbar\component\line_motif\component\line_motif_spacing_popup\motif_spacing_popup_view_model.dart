import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/line_motif_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'motif_spacing_popup_view_interface.dart';

final motifSpacingViewModelProvider = StateNotifierProvider.autoDispose<
    MotifSpacingPopupStateViewInterface,
    MotifSpacingPopupState>((ref) => MotifSpacingViewModel(ref));

class MotifSpacingViewModel extends MotifSpacingPopupStateViewInterface {
  MotifSpacingViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const MotifSpacingPopupState(
              isSpacingMinusToLimit: false,
              isSpacingPlusToLimit: false,
              spacingInputValue: "",
              isDefaultStyle: false,
            ),
            ref) {
    update();
  }

  @override
  void update() {
    state = state.copyWith(
      isSpacingMinusToLimit: _isMinusToLimit(),
      isSpacingPlusToLimit: _isPlusToLimit(),
      spacingInputValue: _getSpacingDisplayValue(),
      isDefaultStyle: _isDefaultStyle(),
    );
  }

  ///
  /// ステップ量
  ///
  final int _stepValue = 10;

  ///
  /// ステップ量
  ///
  final int _stepValue2 = 5;
  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  @override
  int defaultValue = LineMotifModel().defaultSpacingValue;

  ///
  /// Spacingの値ディスプレイスター
  ///
  bool _isSpacingValueDisplayStar =
      LineMotifModel().getSpacing() != LineMotifModel.spacingNotUpdating
          ? false
          : true;

  ///
  /// Spacingの値
  ///
  int _spacingValue = LineMotifModel().getSpacing();

  @override
  bool plusSpacing(bool isLongPress) {
    if (_isSpacingValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSpacingValueDisplayStar = false;

      ///  Model 更新
      _spacingValue = defaultValue;

      /// View更新
      update();

      return false;
    }
    if (_spacingValue >= LineMotifModel.maxiSpacingValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _spacingValue =
        _spacingValue + _stepValue2 > LineMotifModel.maxiSpacingValue
            ? LineMotifModel.maxiSpacingValue
            : _spacingValue + _stepValue2;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  bool miniSpacing(bool isLongPress) {
    if (_isSpacingValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);

      _isSpacingValueDisplayStar = false;

      ///  Model 更新
      _spacingValue = defaultValue;

      /// View更新
      update();

      return false;
    }
    if (_spacingValue <= LineMotifModel.miniSpacingValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _spacingValue =
        _spacingValue - _stepValue2 < LineMotifModel.miniSpacingValue
            ? LineMotifModel.miniSpacingValue
            : _spacingValue - _stepValue2;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.lineMotifSpacing.toString());
    if (_isSpacingValueDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int spacingValue = LineMotifModel().getSpacing();

    /// Model 更新
    LineMotifModel().setSpacing(_spacingValue);
    if (LineMotifModel().setMdcMotifLinePropertyValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (spacingValue != _spacingValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// Spacingの表示値を取得する
  ///
  String _getSpacingDisplayValue() {
    /// cmからmmへ
    double motifSpacingValue = _spacingValue / _stepValue;
    if (_isSpacingValueDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return motifSpacingValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(motifSpacingValue);
  }

  ///
  /// 縮小ボタンの状態の取得
  ///
  bool _isMinusToLimit() {
    if (_isSpacingValueDisplayStar) {
      return false;
    }

    if (_spacingValue <= LineMotifModel.miniSpacingValue) {
      return true;
    }
    return false;
  }

  ///
  /// 増大ボタン状態の取得
  ///
  bool _isPlusToLimit() {
    if (_isSpacingValueDisplayStar) {
      return false;
    }

    if (_spacingValue >= LineMotifModel.maxiSpacingValue) {
      return true;
    }
    return false;
  }

  ///
  /// テキストスタイルの取得
  ///
  bool _isDefaultStyle() {
    if (_isSpacingValueDisplayStar) {
      return true;
    }

    if (_spacingValue == defaultValue) {
      return true;
    }

    return false;
  }
}
