import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/device_info_model.dart';
import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_stipple_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'distance_popup_view_interface.dart';

final distanceViewModelProvider = StateNotifierProvider.autoDispose<
    DistancePopupStateViewInterface,
    DistancePopupState>((ref) => DistanceViewModel(ref));

class DistanceViewModel extends DistancePopupStateViewInterface {
  DistanceViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(
            const DistancePopupState(
              isDistanceMinusToLimit: false,
              isDistancePlusToLimit: false,
              distanceInputValue: "",
              distanceDisplayTextStyle: false,
            ),
            ref) {
    update();
  }

  @override
  void update() {
    state = state.copyWith(
      isDistanceMinusToLimit: _isMinusToLimit(),
      isDistancePlusToLimit: _isPlusToLimit(),
      distanceInputValue: _getDistanceDisplayValue(),
      distanceDisplayTextStyle: _getDistanceDisplayTextStyle(),
    );
  }

  ///
  /// ステップ量
  ///
  final int _stepValue = 10;

  @override
  Unit get currentSelectedUnit => DeviceInfoModel().displayUnitType;

  @override
  int defaultValue = SurfaceStippleModel().distanceDefaultValue;

  ///
  /// Distance値ディスプレイスター
  ///
  bool _isDistanceDisplayStar = SurfaceStippleModel().getDistance() !=
          SurfaceStippleModel.distanceNotUpdating
      ? false
      : true;

  ///
  /// Distance値
  ///
  int _distanceValue = SurfaceStippleModel().getDistance();

  @override
  bool plusDistance(bool isLongPress) {
    if (_isDistanceDisplayStar) {
      _isDistanceDisplayStar = false;

      ///  Model 更新
      _distanceValue = defaultValue;

      /// View更新
      update();
      return false;
    }
    if (_distanceValue >= SurfaceStippleModel.maxiDistanceValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    _distanceValue++;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// View 更新
    update();
    return true;
  }

  @override
  bool miniDistance(bool isLongPress) {
    if (_isDistanceDisplayStar) {
      _isDistanceDisplayStar = false;

      _distanceValue = defaultValue;

      /// View更新
      update();
      return false;
    }

    if (_distanceValue <= SurfaceStippleModel.miniDistanceValue) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    _distanceValue--;

    if (isLongPress == false) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    /// View 更新
    update();
    return true;
  }

  @override
  void onOkButtonClicked() {
    ref
        .read(stitchPageViewModelProvider.notifier)
        .maybeRemovePopupRoute(PopupEnum.surfaceStippleDistance.toString());
    if (_isDistanceDisplayStar) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    int distanceValue = SurfaceStippleModel().getDistance();

    /// Model更新
    SurfaceStippleModel().setDistance(_distanceValue);
    if (SurfaceStippleModel().setStippleSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (distanceValue != _distanceValue) {
      ResumeHistoryModel().backSnapshot();
    }

    CreationModel().changeStitchCreation();
  }

  ///
  /// Distanceの表示値を取得する
  ///
  String _getDistanceDisplayValue() {
    /// cmからmmへ
    double surfaceStippleDistanceValue = _distanceValue / _stepValue;
    if (_isDistanceDisplayStar) {
      if (currentSelectedUnit == Unit.mm) {
        return "*.*";
      } else {
        return "*.***";
      }
    }

    if (currentSelectedUnit == Unit.mm) {
      return surfaceStippleDistanceValue.toStringAsFixed(1);
    }
    return ToolbarModel.getDisplayInchShowValue(surfaceStippleDistanceValue);
  }

  ///
  /// 縮小ボタンの状態の取得
  ///
  bool _isMinusToLimit() {
    if (_distanceValue <= SurfaceStippleModel.miniDistanceValue) {
      return true;
    }
    return false;
  }

  ///
  /// 増大ボタン状態の取得
  ///
  bool _isPlusToLimit() {
    if (_distanceValue >= SurfaceStippleModel.maxiDistanceValue) {
      return true;
    }
    return false;
  }

  ///
  /// テキストスタイルの取得
  ///
  bool _getDistanceDisplayTextStyle() {
    if (_isDistanceDisplayStar) {
      return true;
    }

    if (_distanceValue == defaultValue) {
      return true;
    }

    return false;
  }
}
