import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../setting/model/general_setting_page4_model.dart';
import '../../model/auto_test_model.dart';
import 'auto_test_mode_interface.dart';

class AutoTestModeViewModel extends AutoTestModeViewModelInterface
    implements AutoTestActionRunner {
  AutoTestModeViewModel(Ref ref) : super(const AutoTestModeState(), ref) {
    performAutoTestSetup();
  }

  @override
  Future<void> performAutoTestSetup() async {
    final Stream<String> logStream =
        AutoTestModel().performAutoTestActions(this);
    final StreamSubscription subscription = logStream.listen((log) {
      _logBuffer.writeln(log);
      Log.i(tag: "AutoTestMode", description: log);
      state = state.copyWith(currentActionDesc: _logBuffer.toString());
    });
    ref.onDispose(subscription.cancel);
  }

  @override
  FutureOr<bool> doAction(AutoTestAction action) async {
    switch (action.name) {
      case "changeSpec":
        final String targetSpec = action.getParam("target");
        return _performSpecChange(targetSpec);
      case "userReset":
        await GeneralSettingPage4Model().userReset();
        return true;
      case "languagePopup":
        final bool showLanguagePopup = action.getParam("show");
        return _controlLanguagePopup(showLanguagePopup);
      case "voiceGuidancePopup":
        final bool showVoiceGuidancePopup = action.getParam("show");
        return _controlVoiceGuidancePopup(showVoiceGuidancePopup);
      default:
        throw Exception("Unsupported action: $action");
    }
  }

  ///
  /// デバイスを指定された機種に切り替えます。
  ///
  /// - param: [targetSpec] 対象機種。`brother` または `tacony` のいずれかである必要があります。
  ///
  bool _performSpecChange(String targetSpec) {
    const specEepRomAddress = 0x0009;
    const specEepRomSize = 1;
    switch (targetSpec) {
      case "brother":
        if (DeviceLibrary().apiBinding.getSpec() == ModeSpec.SPEC_DISNEY_US) {
          return false;
        }
        final DeviceErrorCode errorCode =
            DeviceLibrary().apiBinding.bpifEepWrite(
                  specEepRomAddress,
                  specEepRomSize,
                  ModeSpec.SPEC_DISNEY_US,
                  BoardType.PANEL_AND_MAIN,
                );
        assert(errorCode == DeviceErrorCode.devNoError);
        return true;
      case "tacony":
        if (DeviceLibrary().apiBinding.getSpec() ==
            ModeSpec.SPEC_EXPORT_TACONY) {
          return false;
        }
        final DeviceErrorCode errorCode =
            DeviceLibrary().apiBinding.bpifEepWrite(
                  specEepRomAddress,
                  specEepRomSize,
                  ModeSpec.SPEC_EXPORT_TACONY,
                  BoardType.PANEL_AND_MAIN,
                );
        assert(errorCode == DeviceErrorCode.devNoError);
        return true;
      default:
        throw Exception("Unsupported spec: $targetSpec");
    }
  }

  ///
  /// 起動後の言語選択ダイアログの表示を制御します。
  ///
  bool _controlLanguagePopup(bool toShow) {
    final ({DeviceErrorCode errorCode, bool value}) result =
        DeviceLibrary().apiBinding.getSetupLangFlag();
    assert(result.errorCode == DeviceErrorCode.devNoError);
    if (toShow != result.value) {
      return false;
    }
    final DeviceErrorCode errorCode;
    if (toShow) {
      errorCode = DeviceLibrary().apiBinding.resetSetupLang();
    } else {
      errorCode = DeviceLibrary().apiBinding.setSetupLangFlag();
    }
    assert(errorCode == DeviceErrorCode.devNoError);
    return true;
  }

  ///
  /// 起動後の音声ガイダンスダイアログの表示を制御します。
  ///
  bool _controlVoiceGuidancePopup(bool toShow) {
    final ({DeviceErrorCode errCode, bool isSet}) result =
        DeviceLibrary().apiBinding.getSetupVoiceGuideFlag();
    assert(result.errCode == DeviceErrorCode.devNoError);
    if (toShow != result.isSet) {
      return false;
    }
    final DeviceErrorCode errorCode;
    if (toShow) {
      errorCode = DeviceLibrary().apiBinding.resetSetupVoiceGuideFlag();
    } else {
      errorCode = DeviceLibrary().apiBinding.setSetupVoiceGuideFlag();
    }
    assert(errorCode == DeviceErrorCode.devNoError);
    return true;
  }

  final StringBuffer _logBuffer = StringBuffer();
}
