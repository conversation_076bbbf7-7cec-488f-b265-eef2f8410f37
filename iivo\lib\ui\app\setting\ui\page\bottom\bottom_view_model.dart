import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../model/machine_config_model.dart';
import '../../../../../../network/network.dart';
import '../../../../../component/app_guide/app_guide.dart';
import '../../../../../component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../../../../home/<USER>/home_model.dart';
import '../../../model/setting_model.dart';
import '../../page_route.dart';
import '../setting_page_view_model.dart';
import 'bottom_view_interface.dart';

/// view _modelに必要な構造
final bottomViewModeProvider =
    StateNotifierProvider.autoDispose<BottomViewMode, BottomState>(
        (ref) => BottomViewMode(ref));

class BottomViewMode extends ViewModel<BottomState> {
  BottomViewMode(this._ref) : super(const BottomState()) {
    /// View更新
    update();
  }
  final AutoDisposeStateNotifierProviderRef<BottomViewMode, BottomState> _ref;

  int get totalPages => SettingModel().getTotalPages();

  ///
  ///「<」ボタンを押しのクリック処理関数
  ///
  void onLeftPress() {
    if (HomeModel().isHomeInitCompleted == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    int currentPageIndex = SettingModel().currentPageIndex;
    currentPageIndex -= 1;
    if (currentPageIndex < 0) {
      currentPageIndex = SettingModel().getTotalPages() - 1;
    }

    /// 現在のページ更新
    SettingModel().currentPageIndex = currentPageIndex;

    /// View更新
    update();

    _ref
        .read(settingPageViewModelProvider.notifier)
        .updateSettingPageByChild(ModuleType.bottom);
  }

  ///
  ///「>」ボタンを押しのクリック処理関数
  ///
  void onRightPress() {
    if (HomeModel().isHomeInitCompleted == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    int currentPageIndex = SettingModel().currentPageIndex;
    currentPageIndex += 1;
    if (currentPageIndex > SettingModel().getTotalPages() - 1) {
      currentPageIndex = 0;
    }

    /// 現在のページ更新
    SettingModel().currentPageIndex = currentPageIndex;

    /// View更新
    update();

    _ref
        .read(settingPageViewModelProvider.notifier)
        .updateSettingPageByChild(ModuleType.bottom);
  }

  ///
  ///「OK」ボタンを押しのクリック処理関数
  ///
  void onOkButtonClicked() {
    /// 強制更新状態では、このボタンのクリックイベントを無効にします
    if (Upgrade().isInForcedUpdateMode) {
      return;
    }

    if (MachineConfigModel().baseMode == SettingBaseMode.utl) {
      MachineConfigModel().nextMode = SettingBaseMode.utl;
    }

    if (HomeModel().isHomeInitCompleted == false) {
      PagesRoute().pop();
      return;
    }

    if (MachineConfigModel().baseMode != SettingBaseMode.mdc) {
      updateAllViewMode();
    } else {
      /// Mdcからの場合、値を更新後でView更新する
      /// do nothing
    }

    /// 動作中の場合は無効な音を鳴らし、動作中でない場合はメカキーを無効にする
    if (!TpdLibrary().apiBinding.setMatrixEnableListNotOverwrited(
        MachineKeyState.machineKeyEnableAllNG)) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// プロジェクト投影画面を開ける
    final commonProjectorFunction =
        _ref.read(commonProjectorFunctionProvider.notifier);
    commonProjectorFunction.reopenProjector().then((_) {
      TpdLibrary().apiBinding.returnSettings();
      TpdLibrary().apiBinding.clearMatrixEnableListNotOverwrited(
          MachineKeyState.machineKeyEnableAll);
      PagesRoute().pop();
    });
  }

  void onAppGuideClicked(BuildContext context) {
    if (HomeModel().isHomeInitCompleted == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    const int appBadgeSenjuDisabled = 0;
    DeviceLibrary().apiBinding.setAppBadgeSenju(appBadgeSenjuDisabled);
    _ref
        .read(settingPageViewModelProvider.notifier)
        .updateSettingPageByChild(ModuleType.refreshHeader);
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => AppGuidePage(
          onClick: () {
            PopupNavigator.pop(context: context);
          },
        ),
        barrier: false,
      ),
    ).then((_) {
      update();
    });
  }

  ///
  /// ページ更新
  ///
  @override
  void update() {
    /// ＞Taconyでなく、かつ日本仕向でない場合
    /// Senjuアプリバッジ表示有無（0：表示なし、以外：表示あり）
    bool isBadgeDisplay = false;
    if (MachineConfigModel().isBrother) {
      isBadgeDisplay = DeviceLibrary().apiBinding.getAppBadgeSenju() != 0;
    }
    state = state.copyWith(
      currentPageIndex: SettingModel().currentPageIndex,
      settingMode: SettingModel().getSettingModeState(),
      isUpdateHasNewVersion: isBadgeDisplay,
    );
  }
}
