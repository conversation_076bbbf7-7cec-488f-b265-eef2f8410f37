import 'dart:async';
import 'dart:math';
import 'dart:ui' as ui;
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../painter/solid_effect_painter.dart';
import 'real_preview_controller.dart';

///
/// 針のアニメーション再生
///
class RealPreviewPattern extends ConsumerStatefulWidget {
  const RealPreviewPattern({
    super.key,
    required this.controller,
  });

  final RealPreviewController controller;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _RealPreviewPatternState();
}

class _RealPreviewPatternState extends ConsumerState<RealPreviewPattern>
    with SingleTickerProviderStateMixin {
  late ChangeNotifier _notifier;

  @override
  void initState() {
    super.initState();
    _notifier = widget.controller.createPatternNotifier(this);
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: CustomPaint(
        size: const Size(
          RealPreviewController.canvasWidth,
          RealPreviewController.canvasHeight,
        ),
        painter: _PatternPainter(
          repaint: _notifier,
          controller: widget.controller,
        ),
      ),
    );
  }
}

class _PatternPainter extends CustomPainter {
  _PatternPainter({
    required ChangeNotifier repaint,
    required this.controller,
  }) : super(repaint: repaint);

  final RealPreviewController controller;

  /// 刺繍ステッチの位置情報リスト
  List<EmbStitchPos> get points => controller.pattenStitchList;

  /// 立体効果の描画
  final SolidEffectPainter solidEffectPainter = SolidEffectPainter();

  ///
  /// 1つのポイントを処理し、必要に応じて描画ラインを生成する
  ///
  /// [index] 処理するポイントのインデックス
  ///
  /// 戻り値:
  /// - 描画ラインが必要な場合は_PatternLineオブジェクトを返す
  /// - 描画ラインが不要な場合はnullを返す
  ///
  /// 処理内容:
  /// - 色変更フラグがtrueの場合は新しい色を設定
  /// - 特別なコード(_estiNextRgb)の場合は次の色変更を準備
  /// - 特別なコード(_estiTcCode)の場合はスレッドカットを実行
  /// - 通常のポイントの場合は前のポイントから現在のポイントまでのラインを生成
  ///
  PatternLine? _processPoint(int index) {
    final point = points[index];
    if (controller.shouldChangeColor) {
      // 色変更が必要な場合、新しい色を設定
      controller.paint.color = Color(_colorBase + point.code);
      controller.shouldChangeColor = false;
    } else {
      final x = point.pos.X;
      final y = point.pos.Y;
      if (x == _estiNextRgb && y == _estiNextRgb) {
        // 次の色変更を準備
        controller.shouldChangeColor = true;
        controller.previousPoint = null;
      } else if (x == _estiTcCode && y == _estiTcCode) {
        // スレッドカット、前の位置をリセット
        controller.previousPoint = null;
      } else {
        // 通常の点の処理
        final currentPoint = controller.transformPatternCoordinate(x, y);
        final previousPoint = controller.previousPoint;

        final PatternLine? line;
        if (previousPoint != null) {
          line = PatternLine(
            color: controller.paint.color,
            start: previousPoint,
            end: currentPoint,
          );
        } else {
          line = null;
        }
        controller.previousPoint = currentPoint;
        return line;
      }
    }
    return null;
  }

  ///
  /// [start]（含む）から線分の描画を開始し、
  /// 毎回[eachPartLineCount]本の線分を描画します。
  /// [end]（含まない）に達するまで繰り返します。
  ///
  /// 描画が完了した[ui.Image]を返します。
  ///
  /// - params:
  ///   - [solidify] 立体化効果を描きますか？
  ///
  Future<ui.Image?> _paintLinesByPart(int start, int end, int eachPartLineCount,
      {required bool solidify}) async {
    final int partCount = ((end - start) / eachPartLineCount.toDouble()).ceil();
    ui.Image? recordingImage;
    final sw = Stopwatch()..start();
    for (var currentPart = 0; currentPart < partCount; currentPart++) {
      if (controller.isDisposed()) {
        return null;
      }
      final PictureRecorder recorder = PictureRecorder();
      final Canvas recordingCanvas = Canvas(recorder);

      if (recordingImage != null) {
        // 前回の画像を描画します。
        recordingCanvas.drawImage(
            recordingImage, Offset.zero, controller.paint);
        recordingImage.dispose();
      } else {
        // Do nothing
      }

      recordingCanvas.translate(
        controller.patternTranslateX,
        controller.patternTranslateY,
      );
      final int partStartIndex = start + currentPart * eachPartLineCount;
      final int partEndIndex = min(partStartIndex + eachPartLineCount, end);

      // 線分をインクリメンタルに描画します。
      for (var i = partStartIndex; i < partEndIndex; i++) {
        if (controller.isDisposed()) {
          return null;
        }
        final PatternLine? line = _processPoint(i);
        if (line == null) {
          continue;
        }
        if (solidify) {
          solidEffectPainter.paintLine(recordingCanvas, controller.paint, line);
        } else {
          recordingCanvas.drawLine(line.start, line.end, controller.paint);
        }
      }

      // 今回描画した画像をラスタライズします。
      final Picture recordingPicture = recorder.endRecording();
      final ui.Image image = await recordingPicture.toImage(
        controller.scaledPatternWidth.toInt(),
        controller.scaledPatternHeight.toInt(),
      );
      recordingImage = image;
      recordingPicture.dispose();
    }

    if (recordingImage == null) {
      Log.assertTrace('_paintAllLinesByPart: `recordingImage` is null');
      throw Exception();
    }

    sw.stop();
    if (solidify) {
      print("=====> ${sw.elapsedMilliseconds}");
    }

    return recordingImage;
  }

  ///
  /// 指定された範囲内のパスをキャンバスに描画します
  ///
  /// [canvas] - 描画対象のキャンバス
  /// [startIndex] - 描画を開始するパスのインデックス
  /// [endIndex] - 描画を終了するパスのインデックス
  ///
  void _paintLines(Canvas canvas, int startIndex, int endIndex) {
    canvas.translate(
      controller.patternTranslateX,
      controller.patternTranslateY,
    );
    for (var i = startIndex; i < endIndex; i++) {
      if (controller.isDisposed()) {
        return;
      }
      final PatternLine? line = _processPoint(i);
      if (line != null) {
        solidEffectPainter.paintLine(canvas, controller.paint, line);
      }
    }
  }

  ///
  /// 最初のフレームのRealPreview画面を素早く描画するために、
  /// まず立体化されていない図柄を描画・表示し、
  /// その後に立体化された図柄を描画・表示する方式を採用します。
  /// 立体化を行わない描画は処理が速いため、ユーザーに迅速に画面を提示することが可能です。
  ///
  /// これは以下のIssueに対する暫定的な対策です:
  /// https://brothergroup.atlassian.net/browse/PHFIRMIIVO-7939
  ///
  void _paintFirstFrameQuickly(Canvas canvas, bool isZoomOut) {
    // [Future.async]を使用して実行するのは、先にRealPreviewページへ遷移した後、
    // 次のフレームで描画を開始することを保証するためです。
    // そうしないと、描画処理が原因でUIがRealPreviewページに入る前にフリーズしてしまいます。
    _setBusyAndDelay(() async {
      controller.resetPaint();
      final ui.Image? result = await _paintLinesByPart(
        0,
        points.length,
        _maxLineCountPerDraw,
        // 立体化を行わない
        solidify: false,
      );
      if (controller.isDisposed()) {
        return;
      }
      controller.setFullImage(isZoomOut, result);

      // 次のフレームで実行することで、
      // 最初のフレームの図柄が正常にUIにレンダリングされることを保証します。
      SchedulerBinding.instance.addPostFrameCallback(
        (_) async {
          controller.resetPaint();
          final ui.Image? result = await _paintLinesByPart(
            0,
            points.length,
            _maxLineCountPerDraw,
            // 立体化を行う
            solidify: true,
          );
          if (controller.isDisposed()) {
            return;
          }
          controller.setFullImage(isZoomOut, result);
          controller.setNotBusy();
        },
      );
    });
  }

  ///
  /// 全ての描画を行う
  ///
  void _paintAll(Canvas canvas) {
    final isZoomOut = controller.isZoomOut;
    final image = isZoomOut ? controller.zoomOutImage : controller.zoomInImage;
    if (image != null) {
      canvas.drawImage(image, Offset.zero, controller.paint);
    } else {
      _paintFirstFrameQuickly(canvas, isZoomOut);
    }
  }

  ///
  /// 新しいスケールで部分的な描画を行う
  ///
  void _paintPartialWithNewScale(Canvas canvas) {
    final zoomOut = controller.isZoomOut;
    final currentImage = zoomOut
        ? controller.currentZoomOutImage
        : controller.currentZoomInImage;
    if (currentImage != null) {
      canvas.drawImage(currentImage, Offset.zero, controller.paint);
    } else {
      _setBusyAndDelay(() async {
        controller.resetPaint();
        final ui.Image? result = await _paintLinesByPart(
          0,
          controller.currentCount,
          _maxLineCountPerDraw,
          solidify: true,
        );
        if (controller.isDisposed()) {
          return;
        }
        controller.setCurrentImage(zoomOut, false, result);
        controller.setNotBusy();
      });
    }
  }

  ///
  /// アニメーションの部分的な描画を行います
  ///
  /// [canvas] - 描画対象のキャンバス
  /// [animating] - アニメーション中かどうかを示すフラグ
  ///
  void _paintPartial(Canvas canvas, bool animating) {
    final isZoomOut = controller.isZoomOut;
    final recorder = PictureRecorder();
    final recordingCanvas = Canvas(recorder);
    final currentImage = isZoomOut
        ? controller.currentZoomOutImage
        : controller.currentZoomInImage;

    if (currentImage != null) {
      recordingCanvas.drawImage(currentImage, Offset.zero, controller.paint);
      canvas.drawImage(currentImage, Offset.zero, controller.paint);
    } else {
      // キャッシュされた画像がない場合は、何も描画しません
      // これは通常、最初のフレームや画像がリセットされた直後に発生します
    }

    if (animating == false || controller.isRasterizing) {
      return;
    }

    _paintLines(
      recordingCanvas,
      controller.previousPaintIndex + 1,
      controller.currentCount,
    );

    // 描画結果を新しいPictureとして保存
    final Picture picture = recorder.endRecording();
    picture
        .toImage(
      controller.scaledPatternWidth.toInt(),
      controller.scaledPatternHeight.toInt(),
    )
        .then((value) {
      picture.dispose();
      if (controller.isDisposed()) {
        return;
      }
      if (controller.currentCount >= points.length) {
        controller.setFullImage(isZoomOut, value);
      } else {
        controller.setCurrentImage(isZoomOut, true, value);
      }
      controller.isRasterizing = false;
    });

    controller.isRasterizing = true;
    // 描画位置を更新
    controller.previousPaintIndex = controller.currentCount - 1;
  }

  @override
  void paint(Canvas canvas, Size size) {
    canvas.translate(controller.canvasTranslateX, controller.canvasTranslateY);
    switch (controller.playerState) {
      case RealPreviewAction.stop:
        _paintAll(canvas);
        break;
      case RealPreviewAction.play:
        controller.currentCount += controller.speed.linePerTick;
        if (controller.currentCount > points.length) {
          controller.currentCount = points.length;
        }
        if (controller.previousPaintIndex >= points.length - 1) {
          SchedulerBinding.instance.addPostFrameCallback((_) {
            controller.stop();
            controller.shouldRepaintPattern = false;
          });
        } else {
          // アニメーションがまだ終了していない場合は、特に何もする必要はありません
          // 次のフレームで続きを描画します
        }
        _paintPartial(canvas, true);
        break;
      case RealPreviewAction.pause:
        if (controller.isScaleChanged) {
          controller.isScaleChanged = false;
          _paintPartialWithNewScale(canvas);
        } else {
          _paintPartial(canvas, false);
        }
        break;
    }
  }

  @override
  bool shouldRepaint(covariant _PatternPainter oldDelegate) {
    final shouldRepaint = controller.shouldRepaintPattern;
    controller.shouldRepaintBackground = false;
    return shouldRepaint;
  }

  /// 色変更を示す特別なコード
  static const int _estiNextRgb = 20000;

  /// スレッドカットを示す特別なコード
  static const int _estiTcCode = 30000;

  /// RGB色計算のためのベース値
  static const int _colorBase = 0xFF000000;

  ///
  /// この値は、(画像を作成)[Picture.toImage]するたびに最大で何本の線分を描画するかを示します。
  /// 一度に描画する線分が多すぎると、ラスタライズスレッドでメモリオーバーフローが発生し、
  /// アプリがクラッシュする可能性があります:
  /// pthread_create failed: couldn't allocate 544768-bytes mapped space: Out of memory
  /// ../../third_party/dart/runtime/vm/thread_pool.cc: 299: error: Could not start worker thread: result = 11.
  ///
  /// 20回のテストを行った結果、50000本の線を描画する際の各描画ごとの線数（Line/Draw）
  /// と1本あたりの描画時間(Time/Line)のデータが以下のように得られました。
  /// その中でも特に5000と12500で最も良いパフォーマンスが見られました。
  /// 5000の場合、【Close】ボタンの応答遅延がより低くなるため（5000 * 142 / 1000000 = 0.71秒）、
  /// 1回に5000本の線を描画するのが最適です。
  ///
  /// | Line/Draw    | Time/Line (microsecond)  |
  /// | ------------ | ------------------------ |
  /// | 1000         | 146.55                   |
  /// | 2000         | 143.69                   |
  /// | 2500         | 144.95                   |
  /// | 5000         | 142.17                   |
  /// | 6250         | 144.62                   |
  /// | 8333         | 143.53                   |
  /// | 10000        | 142.43                   |
  /// | 12500        | 139.82                   |
  /// | 25000        | 147.49                   |
  ///
  /// @see https://brothergroup.atlassian.net/browse/PHFIRMIIVO-7954
  ///
  static const int _maxLineCountPerDraw = 5000;

  ///
  /// 現在をビジー状態としてマークし、一定の遅延後に[doAfterDelayed]関数を実行します。
  ///
  /// ビジー状態に入るとUIスレッドがブロックされるため、
  /// 遅延を提供することでブロック前に必要なUI更新を保証できます。
  ///
  Future<void> _setBusyAndDelay(
      FutureOr<void> Function() doAfterDelayed) async {
    if (controller.isBusy()) {
      Log.errorTrace("Try to set busy when already busy");
      return;
    }
    const int delayAfterBusyMillisecond = 64;
    controller.setIsBusy();
    return Future.delayed(
      const Duration(milliseconds: delayAfterBusyMillisecond),
      doAfterDelayed,
    );
  }
}
