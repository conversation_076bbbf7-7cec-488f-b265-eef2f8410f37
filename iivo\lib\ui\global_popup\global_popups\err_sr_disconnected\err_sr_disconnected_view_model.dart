import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../model/projector/camera_pen/camera_pen_type.dart';
import '../../../../model/projector_model.dart';
import '../../../app/utl/model/guide_line_model.dart';
import '../../../app/utl/model/pattern_data_type/pattern_property.dart';
import '../../../app/utl/model/pattern_model.dart';
import '../../../app/utl/model/preview_model.dart';
import '../../../app/utl/ui/component/utl_header/utl_header_view_model.dart';
import '../../../app/utl/ui/page/sewing/common/guide_line/guide_line_view_model.dart';
import '../../../app/utl/ui/page/sewing/common/sewing_setting_buttons/sewing_setting_buttons_view_model.dart';
import '../../../app/utl/ui/page/sewing/utility/parameter_adjustment/parameter_adjustment_view_model.dart';
import '../../../app/utl/ui/page_route.dart';
import 'err_sr_disconnected_view_interface.dart';

final errSrDisconnectedViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrSrDisconnectedViewInterface, ErrSrDisconnectedState,
            BuildContext>(
        (ref, context) => ErrSrDisconnectedViewModel(ref, context));

class ErrSrDisconnectedViewModel extends ErrSrDisconnectedViewInterface {
  ErrSrDisconnectedViewModel(
      AutoDisposeStateNotifierProviderRef ref, BuildContext context)
      : super(const ErrSrDisconnectedState(), ref, context) {
    /// GuideLinePopupを開いてもCameraが起動せず、またCameraボタンの状態も更新されない
    if (!ref.exists(guideLineViewModelProvider)) {
      /// カメラ状態を更新します
      ref
          .readAutoNotifierIfExists(utlHeaderViewModelProvider)
          ?.maybeCloseCamera();

      /// カメラボタン状態更新
      ref.readAutoNotifierIfExists(utlHeaderViewModelProvider)?.update();
    } else {
      /// DoNothing
    }
  }

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() => _pushToNextPage();

  ///
  /// 次のページに移行
  /// utl -> sr/sr -> utl
  ///
  Future<void> _pushToNextPage() async {
    if (PatternDataModel().getPatternMode() == PatternMode.stitchRegulator) {
      await _checkAndHandleProjector();

      /// カメラボタン状態更新
      final utlHeaderFunction = ref.read(utlHeaderViewModelProvider.notifier);
      utlHeaderFunction.update();

      /// Model更新、画面遷移
      PatternDataModel().utilityPatternInit();
      PreviewDataModel().updatePreviewImage(null);

      PatternDataModel().getUtilityInitPattern();

      /// 模様登録
      PatternDataModel().loginUtilityPattern(
        PatternDataModel().currentSelectedCategoryIndex,
        PatternDataModel().currentSelectedPatternIndex,
      );
      TpdLibrary().apiBinding.exitSR();

      /// ページ遷移中、parameterとsewingbuttonは共通のコンポーネントとして常に存在します。
      /// また、ログインのパターンがないため、監視状態は更新されないので、手動で状態を更新する必要があります。

      ref
          .readAutoNotifierIfExists(utilityParameterAdjustmentViewModelProvider)
          ?.update();
      ref
          .readAutoNotifierIfExists(sewingSettingButtonsViewModelProvider)
          ?.update();

      PagesRoute().pushReplacement(nextRoute: PageRouteEnum.sewingUtility);
    } else {
      Log.e(
        tag: "ErrSrDisconnectedViewModel",
        description: "current page is not stitch regulator,but try to exit sr",
      );
    }
  }

  ///
  /// SR画面閉じる前にプロジェクトを確認します
  ///
  Future<void> _checkAndHandleProjector() async {
    /// GuideLine 閉じる
    if (GuideLineModel().guideLineShow == GuideLineShow.guideLineShowON) {
      await ProjectorModel().closeUtlProjector(UtlProjectorType.guideline);
      GuideLineModel().guideLineShow = GuideLineShow.guideLineShowOFF;
    }

    /// srSensingプロジェクト停止
    if (ProjectorModel().isUtlProjectorOpened(UtlProjectorType.srSensing)) {
      await ProjectorModel().closeUtlProjector(UtlProjectorType.srSensing);
    } else {
      /// Do Nothing
    }

    /// srStatusプロジェクト停止
    if (ProjectorModel().isUtlProjectorOpened(UtlProjectorType.srStatus)) {
      await ProjectorModel().closeUtlProjector(UtlProjectorType.srStatus);
    } else {
      /// Do Nothing
    }

    return;
  }
}
