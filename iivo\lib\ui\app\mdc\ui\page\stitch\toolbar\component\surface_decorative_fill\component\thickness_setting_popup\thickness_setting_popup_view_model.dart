import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/resume_history_model.dart';
import '../../../../../../../../model/stitch/creation_isolate.dart';
import '../../../../../../../../model/stitch/surface_decorative_fill_model.dart';
import '../../../../../../../../model/stitch/toolbar_model.dart';
import '../../../../../stitch_page_view_model.dart';
import 'thickness_setting_popup_view_interface.dart';

final thicknessSettingPopupViewModelProvider =
    StateNotifierProvider.autoDispose<ThicknessSettingPopupStateViewInterface,
            ThicknessSettingPopupState>(
        (ref) => ThicknessSettingPopupViewModel(ref));

class ThicknessSettingPopupViewModel
    extends ThicknessSettingPopupStateViewInterface {
  ThicknessSettingPopupViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const ThicknessSettingPopupState(), ref) {
    update();
  }

  ///
  /// 縫い回数の状態
  ///
  DecoFillSettingState _thicknessState = DecoFillSettingState.settingCompleted;

  ///
  /// 縫い回数の設定値
  ///
  int _thicknessValue = SurfaceDecoFillModel().getThickness();

  @override
  void update() {
    state = state.copyWith(
      isThickButtonSelected: _thicknessValue ==
          MdcDecoFillThickness.mdc_decofill_thickness_2or3.index,
      isNarrowButtonSelected: _thicknessValue ==
          MdcDecoFillThickness.mdc_decofill_thickness_1or2.index,
    );
  }

  @override
  void onOkButtonClicked() {
    ref.read(stitchPageViewModelProvider.notifier).maybeRemovePopupRoute(
        PopupEnum.surfaceDecorativeFillThickness.toString());
    if (_thicknessState == DecoFillSettingState.unknown) {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
      return;
    }

    /// Thicknessの初期値
    int oldThicknessValue = SurfaceDecoFillModel().getThickness();

    /// Model更新
    SurfaceDecoFillModel().setThickness(_thicknessValue);
    if (SurfaceDecoFillModel().setDecorativeFillSurfaceSettingValue() !=
        MdcLibraryError.mdcNoError) {
      ToolbarModel().showNotChangeSettingMemoryOverError();
      return;
    }

    SystemSoundPlayer().play(SystemSoundEnum.accept);
    if (_thicknessValue != oldThicknessValue) {
      ResumeHistoryModel().backSnapshot();
    } else {
      /// do nothing
    }

    _thicknessState = DecoFillSettingState.settingCompleted;
    CreationModel().changeStitchCreation();
  }

  @override
  void onNarrowButtonClicked() {
    if (state.isNarrowButtonSelected) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    _thicknessValue = MdcDecoFillThickness.mdc_decofill_thickness_1or2.index;
    _thicknessState = DecoFillSettingState.change;

    /// view更新
    state = state.copyWith(
      isNarrowButtonSelected: true,
      isThickButtonSelected: false,
    );
  }

  @override
  void onThickButtonClicked() {
    if (state.isThickButtonSelected) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
    } else {
      SystemSoundPlayer().play(SystemSoundEnum.accept);
    }

    _thicknessValue = MdcDecoFillThickness.mdc_decofill_thickness_2or3.index;
    _thicknessState = DecoFillSettingState.change;

    /// view更新
    state = state.copyWith(
      isNarrowButtonSelected: false,
      isThickButtonSelected: true,
    );
  }
}
