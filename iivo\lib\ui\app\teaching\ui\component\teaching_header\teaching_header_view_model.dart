import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../model/app_locale.dart';
import '../../../../../../model/header_model.dart';
import '../../../../../../model/machine_config_model.dart';
import '../../../../../component/common_header/common_header_view_interface.dart';
import '../../../../../component/common_header/common_header_view_model.dart';
import '../../../../../component/common_header/header_view_model.dart';
import '../../../../../page_route/page_route.dart';
import '../../../../setting/model/setting_model.dart';
import '../../../../setting/ui/page/body/body_view_model.dart';
import '../../../../setting/ui/page/bottom/bottom_view_model.dart';
import '../../../../setting/ui/page/top_bar/top_bar_view_model.dart';
import '../../../../utl/model/pattern_model.dart';
import '../../../model/pdf_model.dart';

/// view _modelに必要な構造
final teachingHeaderViewModelProvider =
    AutoDisposeStateNotifierProvider<CommonHeaderViewModel, CommonHeaderState>(
        (ref) => AttachingEmbFootViewModel(ref));

class AttachingEmbFootViewModel extends CommonHeaderViewModel
    with DeviceLibraryEventObserver, HeaderViewModel {
  AttachingEmbFootViewModel(
    ref,
  ) : super(ref);

  @override
  void build() {
    super.build();
    state = state.copyWith(
      teachingButtonState: ButtonState.select,
      cameraButtonState: _getCameraButtonState(),
    );

    /// Model更新
    MachineConfigModel().currentMode = SettingBaseMode.teaching;
  }

  @override
  void update() {
    Locale locale = AppLocale().getCurrentLocale();

    /// View更新
    state = state.copyWith(
      cameraButtonState: _getCameraButtonState(),
      cameraTipMessageString: lookupAppLocalizations(locale).tt_head_camera,
      homeTipMessageString: lookupAppLocalizations(locale).tt_head_home,
      lockTipMessageString: lookupAppLocalizations(locale).tt_head_lock,
      osaeTipMessageString: lookupAppLocalizations(locale).tt_head_osae,
      settingTipMessageString: lookupAppLocalizations(locale).tt_head_setting,
      teachingTipMessageString: lookupAppLocalizations(locale).tt_head_teaching,
      wifiTipMessageString: lookupAppLocalizations(locale).tt_head_wifi,
      isAppBadgeSenju: HeaderModel().getAppBadgeSenju(),
    );
  }

  ///CameraButtonのボタン状態を取得する
  ButtonState _getCameraButtonState() {
    ButtonState cameraButtonState = ButtonState.normal;
    if (PatternDataModel().isNotSupportCameraPattern() ||
        PatternDataModel().isEndPointOn ||
        PatternDataModel().isEndPointPopupOn) {
      cameraButtonState = ButtonState.disable;
    }

    return cameraButtonState;
  }

  @override
  void onCameraButtonClicked(BuildContext context) {
    SystemSoundPlayer().play(SystemSoundEnum.invalid);
  }

  @override
  void onHomeButtonClicked(BuildContext context) {
    /// Home を確認すると lib にエラーが表示される
    checkGotoHome();
  }

  @override
  void onSettingButtonClicked(BuildContext context) {
    final deviceError = TpdLibrary().apiBinding.gotoSettings();

    if (deviceError == DeviceErrorCode.devInvalidError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    final bPIFError = TpdLibrary().apiBinding.bpIFGetError();
    if (deviceError == DeviceErrorCode.devInvalidPanelError ||
        bPIFError.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }
    if (deviceError != DeviceErrorCode.devNoError) {
      Log.e(tag: "header", description: "An unexpected error occurred");
      return;
    }

    _closeTeachingPdf();
    SystemSoundPlayer().play(SystemSoundEnum.accept);
    MachineConfigModel().nextMode = SettingBaseMode.setting;
    switch (MachineConfigModel().baseMode) {
      case SettingBaseMode.utl:
        SettingModel().setSettingPageIndexWithOtherPage(SettingMode.utility);
        break;
      case SettingBaseMode.home:
        SettingModel().setSettingPageIndexWithOtherPage(SettingMode.general);
        break;
      case SettingBaseMode.emb:
      case SettingBaseMode.mdc:
        SettingModel().setSettingPageIndexWithOtherPage(SettingMode.embroidery);
        break;

      default:

      /// DO Nohting
    }

     /// view更新
    ref.read(topBarViewModeProvider.notifier).update();
    ref.read(bottomViewModeProvider.notifier).update();
    ref.read(bodyViewModelProvider.notifier).update();

    PagesRoute().pushNamed(nextRoute: PageRouteEnum.setting).then((value) {
      if (MachineConfigModel().baseMode == SettingBaseMode.utl &&
          MachineConfigModel().nextMode == SettingBaseMode.utl) {
        MachineConfigModel().currentMode = SettingBaseMode.utl;
      }
    });
  }

  @override
  void onTeachingButtonClicked(BuildContext context) {
    SystemSoundPlayer().play(SystemSoundEnum.invalid);
  }

  @override
  void onWifiButtonClicked(BuildContext context) {
    final deviceError = TpdLibrary().apiBinding.gotoWlanSetting();
    if (deviceError != DeviceErrorCode.devNoError) {
      return;
    }
    _closeTeachingPdf();
    super.onWifiButtonClicked(context);
  }

  /// シャットダウンTeachingPDF
  void _closeTeachingPdf() {
    if (PdfModel().isTeachingFrom) {
      PagesRoute().popUntil(nextRoute: PageRouteEnum.teaching);
      PagesRoute().pop();
    } else if (PdfModel().isSettingFrom) {
      PdfModel().reset();
      PagesRoute().pop();
      PagesRoute().pop();
    } else if (PdfModel().isHomeFrom) {
      //do nothing
    } else {
      PdfModel().reset();
      PagesRoute().pop();
      PagesRoute().pop();
    }
  }
}
