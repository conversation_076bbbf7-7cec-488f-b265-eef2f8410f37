import 'dart:async';
import 'dart:ui';

import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../model/projector_model.dart';
import '../../../../../../../../model/provider/task/device_error_polling_task.dart';
import '../../../../../../../global_popup/global_popup_export.dart'
    show
        errEmbFrameHoldLeverDownFunc,
        errEmbPrjClosePleaseWaitInitFunc,
        errEmbPrjStartPleaseWaitDisposeFunc,
        errEmbPrjStartPleaseWaitInitFunc;
import '../../../../../../../global_popup/panel_popup_route.dart';
import '../../../../../model/border_model.dart';
import '../../../../../model/edit_model.dart' as edit;
import '../../../../../model/sewing_model.dart';
import '../../../camera_pen/camera_pen_view_model.dart';
import 'projector_function_interface.dart';

final embProjectorFunctionProvider =
    StateNotifierProvider<ProjectorFunctionInterface, ProjectorFunctionState>(
        (ref) => EmbProjectorFunctionViewModel(ref));

class EmbProjectorFunctionViewModel extends ProjectorFunctionInterface {
  EmbProjectorFunctionViewModel(this._ref)
      : super(const ProjectorFunctionState());

  ///
  /// プロジェクト起動・停止前にエラーCheckをする
  /// エラーがあれば ポープアープを表示する
  ///
  /// true(エラーあり)
  ///
  @override
  bool checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup() {
    ProjectorLibraryError projectorLibraryError =
        ProjectorLibrary().apiBinding.checkErrorBeforeEmbProjectorStartClose();
    final int errCode = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    if (projectorLibraryError != ProjectorErrorCode_t.projectorNoError ||
        errCode != ErrCode_t.ERR_DUMMY.index) {
      Log.i(
        tag: "checkErrorBeforeEmbProjectorStartClose",
        description:
            "[*0]デバイスエラー(ret:$projectorLibraryError,errorCode:$errCode)",
      );

      /// エラー処理は エラーぽーりんぐに任せて　ここはエラー表示前にエラー処理追加だけです
      return true;
    }
    return false;
  }

  ///
  /// プロジェクト投影をオンにする
  ///
  @override
  Future<void> startEmbProjectorView({
    required void Function() beforeFrameMoveCallback,
    required void Function() removedFrameCallback,
  }) async {
    errEmbFrameHoldLeverDownFunc = removedFrameCallback;

    /// 異常閉じる（枠交換）
    errEmbPrjClosePleaseWaitInitFunc = () async {
      ProjectorModel()
          .cameraProjectorFrameMove
          .setProjectorCenterAreaPointOffsetMm(Offset.zero);

      /// libに枠移動を要求
      await _ref
          .read(cameraPenViewModelProvider.notifier)
          .closeCameraPen(waitAndRefreshCameraPenUI: false);
      await ProjectorModel().embProjector.closeEmbProjector();

      removedFrameCallback();

      /// 完成しましたら、Lib通知
      /// ★★★ このpopupは　Lib通知で閉じる（エラーぽーりんぐで実現する）
      ProjectorLibrary().apiBinding.notifyEmbProjectorCloseComplete();
    };

    /// エラーコールバック関数セットします
    errEmbPrjStartPleaseWaitInitFunc = () async {
      errEmbFrameHoldLeverDownFunc = null;

      /// projectorの初期化タイプ
      ProjectorInitType initType = ProjectorInitType.embEdit;
      if (SewingModel().isInSewingPage == true) {
        initType = ProjectorInitType.embSewing;
      } else if (edit.EditModel().toolbarPopupId == edit.ToolbarPopupId.order) {
        initType = ProjectorInitType.embEditOrder;
      } else if (BorderModel().borderActionState ==
          BorderActionState.borderMark) {
        initType = ProjectorInitType.embBorderMark;
      } else {
        /// do nothing
      }

      /// Projector表示起動
      await ProjectorModel().embProjector.openEmbProjector(initType: initType);

      await _ref
          .read(cameraPenViewModelProvider.notifier)
          .initCameraPen(waitMakeCameraPenUI: true);

      /// 完成しましたら、Lib通知
      /// ★★★ このpopupは　Lib通知で閉じる（エラーぽーりんぐで実現する）
      ProjectorLibrary().apiBinding.notifyEmbProjectorStartComplete();
    };
    errEmbPrjStartPleaseWaitDisposeFunc = () {
      /// プロジェクトバックライト起動
      CameraProjectLibrary().apiBinding.projectorOn();
    };

    /// メカキーをロックする
    final error = TpdLibrary()
        .apiBinding
        .setMatrixEnableList(MachineKeyState.machineKeyEnableAllNG);
    if (error == DirErrorCode.dirInvalidError) {
      Log.assertLog(
          tag: "setMatrixEnableList",
          description: "already setMatrixEnableListNotOverwrited");
      return;
    }
    if (error == DirErrorCode.dirMotorError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    if (error != DirErrorCode.dirNoError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      Log.errorTrace("Not Defined ErrorTrace");
      return;
    }
    PanelPopupRoute().openPleaseWaitPopup(playPopupSound: false);

    /// openPleaseWaitPopup確保すること
    await Future.delayed(
        const Duration(milliseconds: ProjectorModel.projectorStartStopDelayMS));

    /// LIBと打ち合わせ（プロジェクター刺繍模様投影を開始する）
    /// その間にLibからの発行したのエラーを無視して。
    /// 画面描画途中にエラーコードがクリアされました。
    /// 原因は調査難しいので、ここはアプリでエラーコードを発行する
    requestLockErrorPollingTask();
    final resetProjectorError =
        ProjectorLibrary().apiBinding.resetProjectorEmbPatternProjectionArea();
    if (resetProjectorError == ProjectorErrorCode_t.projectorErrorInitial) {
      ProjectorModel().restartProjectorDevice();
      ProjectorLibrary().apiBinding.resetProjectorEmbPatternProjectionArea();
    } else {
      /// DoNothing
    }
    final startProjectorError =
        ProjectorLibrary().apiBinding.startProjectorEmbPatternProjection();
    if (startProjectorError == ProjectorErrorCode_t.projectorErrorInitial) {
      /// 同じ種類のエラーは既に前の関数で処理されています
    } else if (startProjectorError ==
        ProjectorErrorCode_t.projectorErrorSharedMemorySetFailure) {
      Log.assertLog(
          tag: "Projector",
          description:
              "startProjectorEmbPatternProjection projectorErrorSharedMemorySetFailure error");
    } else {
      /// DoNothing
    }
    final startErrCode = TpdLibrary().apiBinding.bpIFGetError().errorCode;

    /// UI側保存してデータを取得します
    await ProjectorModel().embCameraPenViewInfo.initEmbCameraPenUISetting();

    /// LCD上枠位置初期化,必ず「startProjectorEmbPatternProjection」完了した後に実行
    final defaultOffsetMM = ProjectorLibrary()
        .apiBinding
        .getEmbProjectorAreaCenterPositionDefaultOffsetMM();
    ProjectorModel()
        .cameraProjectorFrameMove
        .setProjectorCenterAreaPointOffsetMm(
            Offset(defaultOffsetMM.dirX, defaultOffsetMM.dirY));

    /// LCD上枠サイズ初期化
    final projectorAreaSize =
        ProjectorLibrary().apiBinding.getProjectorAreaSize();
    ProjectorModel()
        .cameraProjectorFrameMove
        .setProjectorAreaSize(projectorAreaSize.hight, projectorAreaSize.width);

    beforeFrameMoveCallback();
    unLockErrorPollingTask();
    PanelPopupRoute().closePleaseWaitPopup();
    TpdLibrary().apiBinding.setErrorState(startErrCode);
  }

  ///
  /// 投影画面をオフにする
  ///
  @override
  void closeEmbProjectorView({
    required void Function() afterClosedHandleCallback,
    required void Function() closingHandleCallback,
  }) {
    errEmbPrjClosePleaseWaitInitFunc = () async {
      ProjectorModel()
          .cameraProjectorFrameMove
          .setProjectorCenterAreaPointOffsetMm(Offset.zero);

      /// libに枠移動を要求
      await _ref
          .read(cameraPenViewModelProvider.notifier)
          .closeCameraPen(waitAndRefreshCameraPenUI: false);
      await ProjectorModel().embProjector.closeEmbProjector();

      closingHandleCallback();

      /// 完成しましたら、Lib通知
      /// ★★★ このpopupは　Lib通知で閉じる（エラーぽーりんぐで実現する）
      ProjectorLibrary().apiBinding.notifyEmbProjectorCloseComplete();

      Future(
        () {
          afterClosedHandleCallback();
        },
      );
    };

    /// プロジェクター刺繍模様投影を終了する
    final ret = ProjectorLibrary().apiBinding.endProjectorEmbPatternProjection(
          SewingModel().isInSewingPage == true
              ? ProjectorQuitFrameMovePosition.projectorFrameMoveStitchPtnTop
              : ProjectorQuitFrameMovePosition
                  .projectorFrameMoveBeforeProjection,
        );
    final errCode = TpdLibrary().apiBinding.bpIFGetError().errorCode;
    if (ret == ProjectorErrorCode_t.projectorErrorInvalidPanel ||
        errCode != ErrCode_t.ERR_DUMMY.index) {
      Log.i(
        tag: "closeProjector",
        description: "[*1]デバイスエラー(ret:$ret,errorCode:$errCode)",
      );

      /// エラー処理は エラーぽーりんぐに任せて　ここはエラー表示前にエラー処理追加だけです
    } else {
      /// libに枠移動を要求
      ProjectorLibrary().apiBinding.closeEmbProjectorPleaseWait();

      /// Projector表示停止
      /// ★★★
      /// プロジェクト停止 [GlobalPopupRouteEnum.ERR_EMB_PRJ_CLOSE_PLEASE_WAIT]で実現する
    }
  }

  ///
  /// プロジェクト画面描画内容更新
  ///
  /// - [redrawEmbPattern] : true 模様投影された時に再描画（再描画が性能が悪い）
  /// - [isQuilt] : true quilt模様の投影画面更新(カメラマン起動した間にのみ)
  /// - [drawCameraPen] : true trueカメラマンUI描画します(できればカメラマン動きの時にのみ)
  /// - [onlyRefresh] : 何もしない、画面更新だけです（プロジェクト画面内容別ところで準備の時に使う）
  ///
  @override
  Future<void> maybeUpdateProjectorScreen({
    required bool redrawEmbPattern,
    bool isQuilt = false,
    bool drawCameraPen = true,
    bool onlyRefresh = false,
    bool isWFoot = false,
  }) async {
    if (ProjectorModel().embProjector.isEmbProjectorOpened() == false) {
      return;
    }
    return ProjectorModel().embProjector.refreshEmbProjector(
          redrawEmbPattern: redrawEmbPattern,
          autoBacklight: true,
          onlyBottom: !drawCameraPen,
          onlyRefresh: onlyRefresh,
          isQuilt: isQuilt,
          isWFoot: isWFoot,
        );
  }

  ///
  /// 他のproviderアクセスcontext
  ///
  final StateNotifierProviderRef _ref;
}
