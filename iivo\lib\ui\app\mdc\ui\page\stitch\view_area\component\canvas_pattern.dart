import 'package:flutter/material.dart';

import '../../../../../model/stitch/draw_region_model.dart'
    show DisplayImageInfo, MdcRegionInfo;

class PatternPainter extends CustomPainter {
  PatternPainter({
    required this.patternList,
    required this.imageInfo,
    required this.scale,
    this.isProjectorOn = false,
  });
  List<MdcRegionInfo> patternList;
  DisplayImageInfo imageInfo;
  double scale;
  bool isProjectorOn;

  @override
  void paint(Canvas canvas, Size size) {
    if (imageInfo.image == null) {
      return;
    }

    Rect centerRect = Rect.fromCenter(
      center: Offset(
        imageInfo.centerOffset.dx * scale,
        imageInfo.centerOffset.dy * scale,
      ),
      width: imageInfo.image!.width.toDouble(),
      height: imageInfo.image!.height.toDouble(),
    );

    /// 模様描画
    ///
    /// アンチエイリアスを使用せずに描画すると、画像座標の計算結果が小数になるため、
    /// 1ピクセル幅の線が描画時にちょうど2つのピクセルの間に挟まって描画されない可能性があります。
    /// 座標を軽微に移動させ（小数点以下を四捨五入:roundToDouble）ピクセルに合わせることで、画像の各ピクセルが描画されるようにします。
    canvas.drawImage(
      imageInfo.image!,
      Offset(
        centerRect.left.roundToDouble(),
        centerRect.top.roundToDouble(),
      ),
      Paint(),
    );

    if (isProjectorOn) {
      return;
    }

    /// 選択した模様の枠描画
    for (var pattern in patternList) {
      if (pattern.isSelected) {
        Rect rect = Rect.fromLTWH(
          pattern.x * scale,
          pattern.y * scale,
          pattern.w * scale,
          pattern.h * scale,
        );
        canvas.drawRect(
          rect,
          Paint()
            ..strokeWidth = 1
            ..style = PaintingStyle.stroke
            ..isAntiAlias = true
            ..color = const Color.fromARGB(255, 235, 65, 65),
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant PatternPainter oldDelegate) => true;
}
