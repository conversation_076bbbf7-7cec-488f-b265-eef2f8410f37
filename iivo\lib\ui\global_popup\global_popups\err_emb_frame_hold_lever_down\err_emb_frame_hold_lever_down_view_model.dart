import 'package:audio_player/audio_player_interface.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:ph_eel_plugin/tpd_bindings_generated.dart';

import 'err_emb_frame_hold_lever_down_view_interface.dart';

void Function()? errEmbFrameHoldLeverDownFunc;

final errEmbFrameHoldLeverDownViewModelProvider = StateNotifierProvider.family
    .autoDispose<ErrEmbFrameHoldLeverDownViewInterface,
            ErrEmbFrameHoldLeverDownState, BuildContext>(
        (ref, context) => ErrEmbFrameHoldLeverDownViewModel(ref, context));

class ErrEmbFrameHoldLeverDownViewModel
    extends ErrEmbFrameHoldLeverDownViewInterface {
  ErrEmbFrameHoldLeverDownViewModel(Ref ref, BuildContext context)
      : super(const ErrEmbFrameHoldLeverDownState(), ref, context);

  ///
  /// okボタンクリク関数
  ///
  @override
  void onOKButtonClicked() {
    final int errCode = TpdLibrary().apiBinding.bpIFSendDisplayDataSync(
        BPIFSendKey.KEYERROREMBFRAMEHOLDLEVERDOWNPOWERFORMONITORING);
    if (errCode != BPIFSendError_t.bpifNoError.index) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
  }

  @override
  void dispose() {
    super.dispose();
    errEmbFrameHoldLeverDownFunc?.call();
    errEmbFrameHoldLeverDownFunc = null;
  }
}
