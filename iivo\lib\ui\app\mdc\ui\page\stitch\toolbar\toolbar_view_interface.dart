import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// // もしstate中に変数はtypedef使ったら、freezed自動生成したのもの中にtypeは戻に戻だ。
// // ビルドエラーになりました。それでここに「xxxx_view_interface.dart」を追加しました。
import 'component/line_candle/line_candle_view_model.dart';
import 'component/line_chain/line_chain_view_model.dart';
import 'component/line_e_stitch/line_e_stitch_view_model.dart';
import 'component/line_motif/line_motif_view_model.dart';
import 'component/line_rough_zigzag/line_rough_zigzag_view_model.dart';
import 'component/line_running/line_running_view_model.dart';
import 'component/line_triple/line_triple_view_model.dart';
import 'component/line_v_stitch/line_v_stitch_view_model.dart';
import 'component/line_zigzag/line_zigzag_view_model.dart';
import 'component/surface_decorative_fill/surface_decorative_fill_view_model.dart';
import 'component/surface_stipple/surface_stipple_view_model.dart';
import 'component/surface_tatami/surface_tatami_view_model.dart';

part 'toolbar_view_interface.freezed.dart';

@freezed
class ToolbarState with _$ToolbarState {
  const factory ToolbarState({
    @Default(0) int sewingModeTypeIndex,
    @Default(0) int surfaceKindIndex,
    @Default(0) int lineKineIndex,
    @Default(0) int selectedIndex,
    @Default(false) bool isLeftRightButtonValid,
    @Default(Color(0x00000000)) Color showColor,
    @Default("") String lineStitchImagePath,
    @Default(false) bool isSameSewingTypeButtonSelected,
    @Default(null) Uint8List? selectedLineThumbnailData,
    @Default(null) Uint8List? selectedSurfaceThumbnailData,
    @Default(false) bool isSewModeChanged,
    @Default(false) bool invalidStdCode,
    required LineZigzagViewModelProvider lineZigzagViewModelProvider,
    required SurfaceStippleViewModelProvider surfaceStippleViewModelProvider,
    required LineVStitchViewModelProvider lineVStitchViewModelProvider,
    required LineChainViewModelProvider lineChainViewModelProvider,
    required LineCandleViewModelProvider lineCandleViewModelProvider,
    required LineEStitchViewModelProvider lineEStitchViewModelProvider,
    required LineTripleViewModelProvider lineTripleViewModelProvider,
    required LineRunningViewModelProvider lineRunningViewModelProvide,
    required SurfaceTatamiViewModelProvider surfaceTatamiViewModelProvider,
    required SurfaceDecorativeFillViewModelProvider
        surfaceDecorativeFillViewModelProvider,
    required LineMotifViewModelProvider lineMotifViewModelProvider,
    required LineRoughZigzagViewModelProvider lineRoughZigzagViewModelProvider,
  }) = _ToolbarState;
}

abstract class ToolBarViewInterface extends ViewModel<ToolbarState> {
  ToolBarViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// ステッチ展開処理かどうか
  ///
  bool get isProcessImage;

  ///
  /// パレットの色のリスト
  ///
  List<Color> get paletteColors;

  ///
  /// 線のアイコンリストを取得
  ///
  List<Widget> get lineSewIconList;

  ///
  /// 面のアイコンリストを取得
  ///
  List<Widget> get surfaceSewIconList;

  ///
  /// ViewModel 更新
  ///
  @override
  void update();

  ///
  ///  ステッチの左ボタンがクリックされました
  ///
  void onStitchLeftButtonClicked();

  ///
  /// ステッチの右ボタンがクリックされました
  ///
  void onStitchRightButtonClicked();

  ///
  /// 同種の縫い方一括選択ボタンがクリックされました
  ///
  void onSameSewingTypeButtonClicked();

  ///
  /// 縫製設定表示取得する
  ///
  Widget getSelectedSewingSettingDisplay(WidgetRef ref);

  ///
  /// ラインプロパティポップアップを開く
  ///
  void onOpenLinePropertyPopup(context);

  ///
  /// ラインプロパティポップアップを開く
  ///
  void onOpenSurfacePropertyPopup(context);
}
