import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../../global_popup/global_popups/err_reset_emb_size_and_position/err_reset_emb_size_and_position_view_model.dart';
import '../../../../../../model/edit_model.dart';
import '../../../../../../model/pattern_model.dart';
import '../../../../../../model/scan_model.dart';
import '../../../../common_component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../../../../common_component/function_provider/size_function_provider/size_function_interface.dart';
import '../../../../common_component/function_provider/size_function_provider/size_function_provider.dart';
import '../../../pattern_edit_view_model.dart';
import 'size_adjustment_view_interface.dart';

///
/// 模様拡大のステップ値
///
int _step = 1;

final sizeAdjustmentViewModelProvider = StateNotifierProvider.autoDispose<
    SizeAdjustmentViewModelInterface,
    SizeAdjustmentState>((ref) => SizeAdjustmentViewModel(ref));

class SizeAdjustmentViewModel extends SizeAdjustmentViewModelInterface {
  SizeAdjustmentViewModel(AutoDisposeStateNotifierProviderRef ref)
      : super(const SizeAdjustmentState(), ref) {
    /// View更新
    update();

    ref.listen(
      fireImmediately: true,
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.sizeWidth),
      (previous, nextState) {
        state = state.copyWith(
          widthValue: _changeValueToDisplay(nextState),
        );
      },
    );

    ref.listen(
      fireImmediately: true,
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.sizeHeight),
      (previous, nextState) {
        state = state.copyWith(
          heightValue: _changeValueToDisplay(nextState),
        );
      },
    );

    ref.listen(
      fireImmediately: true,
      appDisplayEmbStateProvider.select((value) => (
            value.embEditAttrib.ref.subBlockState,
            value.embEditAttrib.ref.subSTBState
          )),
      (previous, nextState) {
        _updateTabButtonState(nextState.$1, nextState.$2);
      },
    );
  }

  @override
  void update() {
    PatternModel patternModel = PatternModel();

    /// View更新
    state = state.copyWith(
      isInch: patternModel.isUnitMm == false,
      isEnglish: patternModel.isEnglish,
      isScanPopupOpen: ScanModel().isEditPageScanPopupOpen,
    );
  }

  @override
  void updateByScanPopupChange() {
    state =
        state.copyWith(isScanPopupOpen: ScanModel().isEditPageScanPopupOpen);
  }

  @override
  void onSTBChangeButtonClicked() {
    if (state.isTabDisable == true) {
      return;
    }

    GlobalPopupRoute().updateErrorState(
      nextRoute: GlobalPopupRouteEnum.ERR_RESET_EMB_SIZE_AND_POSITION,
      arguments: ErrResetEmbSizeAndPositionArgument(
        onCancelButtonClicked: () => GlobalPopupRoute().resetErrorState(),
        onOKButtonClicked: () {
          state.isPatternDoSTB == true
              ? EmbLibrary().apiBinding.resetStbMode()
              : EmbLibrary().apiBinding.setStbMode();

          PatternModel()
            ..clearCurrentGroupInfoCache()
            ..clearCurrentPatternImageCache();

          /// View更新
          GlobalPopupRoute().resetErrorState();

          update();
          ref
              .read(patternEditViewModelProvider.notifier)
              .updateEditPageByChild(ModuleType.toolBar);
        },
      ),
    );
  }

  @override
  void onResetButtonClicked() {
    if (ref.read(sizeFunctionProvider).isSizeFunctionDisable == true) {
      return;
    }

    /// 模様サイズをリストアする
    final EmbGroup currentGroup = PatternModel().getCurrentGroup();
    currentGroup.resetSizeEmbGroup(isChangeDoSTB: state.isPatternDoSTB);

    final Pattern currentPattern =
        PatternModel().getCurrentPattern(curGroupHandle: currentGroup.handle);
    if (currentPattern is EmbBorder) {
      currentPattern
        ..clearGroupInfoCache()
        ..clearBorderCompInfoCache()
        ..clearBorderInfoCache()
        ..clearGroupImageCache();
    } else if (currentPattern is EmbGroup) {
      currentPattern
        ..clearGroupInfoCache()
        ..clearMainImageCache();
    } else {
      /// Do nothing
    }

    /// View更新
    update();

    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  @override
  void sizeAdjustmentCancel() {
    /// 画像キャッシュをクリアする
    PatternModel().clearCurrentPatternImageCache();

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);

    /// プロジェクト投影画面を更新する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.maybeUpdateProjectorScreen(redrawEmbPattern: true);
  }

  ///
  /// サイズ調整
  ///
  bool _sizeAdjustment(
      bool isLongPress, MagType magType, ChangeType changeType) {
    final currentEmbGroup = PatternModel().getCurrentGroup();
    final Magnitude magStep = _magStep(currentEmbGroup);
    final sizeFunction = ref.read(sizeFunctionProvider.notifier);
    if (sizeFunction.state.isSizeFunctionDisable == true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    if (sizeFunction.isMagStepOverRange(currentEmbGroup, magType, changeType) ==
        true) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }

    if (isLongPress) {
      final (int minSpeed, int maxSpeed) = sizeFunction.getChangeStepSpeedRange(
          currentEmbGroup, magStep, magType, changeType);
      _step++;
      _step = _step.clamp(minSpeed, maxSpeed);

      /// 模様サイズを変更する
      /// 必要ならエラーの確認を追加します
      EmbLibraryError error = sizeFunction.changePatternSize(
          currentEmbGroup, magType, _step, changeType);
      if (error != EmbLibraryError.EMB_NO_ERR) {
        SystemSoundPlayer().play(SystemSoundEnum.invalid);
        return false;
      }

      /// Lcd画面表示更新
      /// View更新
      update();

      /// 他のページへの更新の通知
      ref
          .read(patternEditViewModelProvider.notifier)
          .updateEditPageByChild(ModuleType.toolBar);

      return true;
    }
    _step = 1;

    /// 模様サイズを変更する
    /// 必要ならエラーの確認を追加します
    EmbLibraryError error = sizeFunction.changePatternSize(
        currentEmbGroup, magType, _step, changeType);
    if (error != EmbLibraryError.EMB_NO_ERR) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return false;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// Lcd画面表示更新
    /// View更新
    update();

    /// 他のページへの更新の通知
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);

    return true;
  }

  @override
  void onOKButtonClick(BuildContext context) {
    PopupNavigator.pop(context: context);

    /// Model更新
    EditModel().toolbarPopupId = ToolbarPopupId.edit;
  }

  Magnitude _magStep(EmbGroup currentEmbGroup) =>
      currentEmbGroup.embGroupInfo.embPatternInfo.embPatterns.first.magStep;

  String _changeValueToDisplay(int value) {
    final bool isSizeRunCalc = TpdLibrary()
        .apiBinding
        .bPIFGetAppDisplayEmb()
        .embEditAttrib
        .ref
        .sizeRunCalc;
    return isSizeRunCalc ? "----" : PatternModel().changeValueToDisplay(value);
  }

  ///
  /// タブボタンの状態を更新します。
  ///
  void _updateTabButtonState(int subBlockState, int subSTBState) {
    AppInfoFuncState subBlockButtonState =
        AppInfoFuncState.getValueByNumber(subBlockState);
    AppInfoFuncState subSTBButtonState =
        AppInfoFuncState.getValueByNumber(subSTBState);
    state = state.copyWith(
      isTabDisable: subBlockButtonState == AppInfoFuncState.disable &&
          subSTBButtonState == AppInfoFuncState.disable,
      isPatternDoSTB: subBlockButtonState != AppInfoFuncState.disable &&
          subSTBButtonState != AppInfoFuncState.disable &&
          subSTBButtonState == AppInfoFuncState.enable,
    );
  }

  ///
  /// 横方向縮小拡大ボタンが関数をクリックします。
  ///
  @override
  bool onSizeTabWidthEnlargeButton(bool isLongPress) =>
      _sizeAdjustment(isLongPress, MagType.xOnly, ChangeType.enlarge);

  @override
  bool onSizeTabWidthReduceButton(bool isLongPress) =>
      _sizeAdjustment(isLongPress, MagType.xOnly, ChangeType.reduce);

  ///
  /// 縦方向縮小拡大ボタンが関数をクリックします。
  ///
  @override
  bool onSizeTabHeightEnlargeButton(bool isLongPress) =>
      _sizeAdjustment(isLongPress, MagType.yOnly, ChangeType.enlarge);

  @override
  bool onSizeTabHeightReduceButton(bool isLongPress) =>
      _sizeAdjustment(isLongPress, MagType.yOnly, ChangeType.reduce);

  ///
  /// 横縦方向縮小拡大ボタンが関数をクリックします。
  ///
  @override
  bool onSizeTabEnlargeButton(bool isLongPress) =>
      _sizeAdjustment(isLongPress, MagType.xyAll, ChangeType.enlarge);

  @override
  bool onSizeTabReduceButton(bool isLongPress) =>
      _sizeAdjustment(isLongPress, MagType.xyAll, ChangeType.reduce);
}
