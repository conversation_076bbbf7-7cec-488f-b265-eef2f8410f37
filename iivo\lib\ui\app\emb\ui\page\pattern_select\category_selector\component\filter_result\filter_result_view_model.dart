import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/xd_component.dart';

import '../../../../../../../../../iivo.dart';
import '../../../../../../../../../model/app_locale.dart';
import '../../../../../../../../../model/const_def_model.dart';
import '../../../../../../../../../model/preview_area_size_model.dart';
import '../../../../../../../../global_popup/global_popup_route.dart';
import '../../../../../../../../global_popup/global_popup_route_enum.dart';
import '../../../../../../../../global_popup/global_popups/err_emb_too_much_selected_go_menu/err_emb_too_much_selected_go_menu_view_model.dart';
import '../../../../../../../../global_popup/global_popups/err_trouble_occored_power_off/err_trouble_occored_power_off_view_model.dart';
import '../../../../../../model/area_model.dart';
import '../../../../../../model/pattern_filter_model.dart';
import '../../../../../../model/pattern_model.dart';
import '../../../../../../model/preview_model.dart';
import '../../../../../../model/redo_undo_model.dart';
import '../../../../../../model/select_information_model.dart';
import '../../../../../../model/select_model.dart';
import '../../../../../../model/thread_color_list_model.dart';
import '../../../../../page_route.dart';
import '../filter/filter_navigator.dart';
import '../preview/preview_view_model.dart';
import '../select_information_popup/select_information_popup.dart';
import 'component/processing.dart';
import 'filter_result_view_interface.dart';

final filterResultViewModelProvider =
    StateNotifierProvider.autoDispose<FilterResultViewModel, FilterResultState>(
  (ref) => FilterResultViewModel(ref),
);

class FilterResultViewModel extends FilterResultViewInterface {
  FilterResultViewModel(Ref ref) : super(const FilterResultState(), ref) {
    update();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _openProcessingPopup();

      PatternFilter().updateFilterPatternInfo().then((value) {
        if (!mounted) {
          return;
        }

        _closeProcessingPopup();

        state = state.copyWith(imageGroup: PatternFilter().iconList);
      });
    });
  }

  ///
  /// 虫眼鏡による拡大画像iconList
  ///
  final List<Widget> _zoomIconList = [
    const ico_zoom100(),
    const ico_zoom125(),
    const ico_zoom150(),
    const ico_zoom200(),
    const ico_zoom400(),
  ];

  ScrollController scrollController = ScrollController();

  ///
  /// 読み取中のポップアップ
  ///
  OverlayEntry? _processingPopup;

  ///
  /// 読み取中にポップアップウを開く
  ///
  void _openProcessingPopup() {
    _processingPopup = OverlayEntry(
      builder: (context) => const Processing(),
    );
    if (_processingPopup != null) {
      globalPopupNavigatorKey.currentState?.overlay?.insert(_processingPopup!);
    }
  }

  ///
  /// 読み取中にポップアップウを開く
  ///
  void _closeProcessingPopup() {
    _processingPopup?.remove();
    _processingPopup?.dispose();
    _processingPopup = null;
  }

  /// 表示倍率値リスト
  List<int> get zoomDisplayList => zoomList;

  ///
  /// ViewModel更新
  ///
  @override
  void update() {
    // TODO:画像展開中に数値表示がないのを待つ
    // http://apngoap306.ap.brothergroup.net:8080/jira/browse/PHBSH-1228
    PatternDispInfo patternDispInfo = PatternModel().getPatternInfo(
      isAllPattern: true,
      isNeedTemporaryGroup: true,
      isNeedAllPatternSize: true,
    );
    final AppLocalizations l10n =
        lookupAppLocalizations(AppLocale().getCurrentLocale());
    int frameIndex = AreaModel().getEmbTopBarFrameIndex(patternDispInfo);
    String heightValue = '----';
    String widthValue = '----';
    String totalTimeValue = '----';
    String numberOfColorsValue = '----';
    String colorChangesValue = '----';
    bool isUnitMm = PatternModel().isUnitMm;
    if ((PatternModel().getAllPattern().isNotEmpty &&
            ThreadColorListModel().isNoThreadToSewing() == false) ||
        PatternModel().temporaryPatternList.isNotEmpty) {
      heightValue = isUnitMm
          ? PatternModel()
                  .changeValueToDisplay(patternDispInfo.allPatternHeight) +
              l10n.icon_00225
          : PatternModel()
                  .changeValueToDisplay(patternDispInfo.allPatternHeight) +
              l10n.icon_00226;
      widthValue = isUnitMm
          ? PatternModel()
                  .changeValueToDisplay(patternDispInfo.allPatternWidth) +
              l10n.icon_00225
          : PatternModel()
                  .changeValueToDisplay(patternDispInfo.allPatternWidth) +
              l10n.icon_00226;
      totalTimeValue = patternDispInfo.sewingTime
              .clamp(sewingTimeDisplayLimitMin, sewingTimeDisplayLimitMax)
              .toString() +
          l10n.icon_00039;
      numberOfColorsValue = patternDispInfo.colorNum.toString();
      colorChangesValue = patternDispInfo.threadNum.toString();
    } else {
      /// Do noting
    }
    ButtonState realPreviewButtonState =
        PatternModel().getAllPattern().isNotEmpty ||
                PatternModel().temporaryPatternList.isNotEmpty
            ? ButtonState.normal
            : ButtonState.disable;
    ButtonState formationButtonState =
        PatternModel().temporaryPatternList.isNotEmpty
            ? ButtonState.normal
            : ButtonState.disable;

    /// View更新
    state = state.copyWith(
      frameIndex: frameIndex,
      heightValue: heightValue,
      widthValue: widthValue,
      totalTimeValue: totalTimeValue,
      numberOfColorsValue: numberOfColorsValue,
      colorChangesValue: colorChangesValue,
      isSelectedPattern:
          SelectModel().selectedPatternIndex == null ? false : true,
      imageGroup: PatternFilter().iconList,
      thumbnailSize: DeviceLibrary().apiBinding.getThumbnailSize().value.index,
      isFilterApplied: PatternFilter().filterStatus == FilterType.filtered,
      showFilterButton: PatternFilter().filterStatus != FilterType.hide,
      patternDisplayInfoList: _getPatternDisplayInfoList(),
      temporaryGroupDisplayInfoList: _getTemporaryGroupDisplayInfoList(),
      selectedZoomScale: PatternModel().selectedZoomScaleInSelectPage,
      formationButtonState: formationButtonState,
      realPreviewButtonState: realPreviewButtonState,
      isSelectSortLeft: !PatternFilter().isFilterReverse,
      setButtonEnable: PatternModel().temporaryGroupList.isNotEmpty,
    );
  }

  @override
  void onZoomButtonClicked() {
    state = state.copyWith(showZoomList: true);
  }

  ///
  /// 倍率値リストを閉じる
  ///
  @override
  void closeZoomPopup() {
    state = state.copyWith(
        showZoomList: false,
        selectedZoomScale: PatternModel().selectedZoomScaleInSelectPage);
  }

  ///
  /// 倍率値変更
  ///
  @override
  void onZoomPopupListClick(int zoomValue) {
    /// Model更新
    PatternModel().selectedZoomScaleInSelectPage = zoomValue;

    /// view更新
    state = state.copyWith(
        showZoomList: false,
        selectedZoomScale: PatternModel().selectedZoomScaleInSelectPage);

    /// 他のページへの更新の通知
    update();
    ref.read(previewViewModelProvider.notifier).update();
  }

  ///
  /// 選択されているものを取得します index
  ///
  @override
  Widget getSelectedZoomIcon() {
    for (int i = 0; i < zoomList.length; i++) {
      if (state.selectedZoomScale == zoomList[i]) {
        return _zoomIconList[i];
      }
    }
    return Container();
  }

  @override
  void onInformationButtonClicked(BuildContext context) => PopupNavigator.push(
        context: context,
        popup: PopupRouteBuilder(
          builder: (_) => const SelectInformationPopup(),
          barrier: false,
        ),
      );

  @override
  void onRealPreviewButtonClicked() {
    /// Model更新
    PatternModel().realPreviewDisplayType =
        RealPreviewDisplayType.patternSelect;

    /// View更新
    PagesRoute().pushNamed(nextRoute: PageRouteEnum.embRealPreview);
  }

  @override
  void onFilterButtonClicked(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        barrier: false,
        builder: (context) => const FilterNavigator(),
      ),
    ).then((_) {
      _openProcessingPopup();

      PatternFilter().updateFilterPatternInfo().then((value) {
        if (!mounted) {
          return;
        }

        _closeProcessingPopup();

        state = state.copyWith(imageGroup: PatternFilter().iconList);
      });
    });
  }

  @override
  void onFilterCloseButtonClicked() {
    /// Model更新
    PatternModel().deleteTemporaryPatternList();
    SelectInformationModel()
        .setTemporaryPatternAutoKind(CategoryType.unknownPattern);
    SelectModel()
      ..selectedCategoryType = null
      ..selectedPatternIndex = null;
    PatternFilter()
      ..patternData = null
      ..resetFilterSetting();

    PagesRoute().pushReplacement(nextRoute: PageRouteEnum.patternSelect);
  }

  @override
  void onFilterReverseButtonClick() {
    /// Model更新
    PatternFilter().isFilterReverse = true;

    /// View更新
    update();

    _openProcessingPopup();

    PatternFilter().updateFilterPatternInfo().then((value) {
      if (!mounted) {
        return;
      }

      _closeProcessingPopup();

      state = state.copyWith(imageGroup: PatternFilter().iconList);
    });
  }

  @override
  void onFilterOrderButtonClick() {
    /// Model更新
    PatternFilter().isFilterReverse = false;

    /// View更新
    update();

    _openProcessingPopup();

    PatternFilter().updateFilterPatternInfo().then((value) {
      if (!mounted) {
        return;
      }

      _closeProcessingPopup();

      state = state.copyWith(imageGroup: PatternFilter().iconList);
    });
  }

  ///
  /// 模様を選択します
  /// @Param
  /// index:模様のcategoryType
  ///
  @override
  void onPatternClick(int patternIndex) {
    SelectModel().selectedCategoryType =
        PatternFilter().filterPatternInfo[patternIndex].categoryType;

    SelectModel().selectedPatternIndex =
        PatternFilter().filterPatternInfo[patternIndex].patternNum;

    assert(SelectModel().selectedCategoryType != null, '割り当てに失敗しました');
    assert(SelectModel().selectedPatternIndex != null, '割り当てに失敗しました');
    EmbLibraryError error = PatternModel().selectEmb(
        SelectModel().selectedPatternIndex!,
        SelectModel().selectedCategoryType!);

    /// メモリーフル時の対応
    if (error == EmbLibraryError.EMB_DATAOVER_ERR) {
      GlobalPopupRoute().updateErrorState(
          nextRoute: GlobalPopupRouteEnum.ERR_EMB_TOO_MUCH_SELECTED_GO_MENU,
          arguments: ErrEmbTooMuchSelectedGoMenuArgument(
            onOKButtonClicked: GlobalPopupRoute().resetErrorState,
          ));
      return;
    }

    update();
  }

  @override
  void onSetButtonClicked() {
    assert(PatternModel().temporaryPatternList.firstOrNull != null, 'データ異常');
    EmbLibraryError error = EmbLibrary().apiBinding.embGotoEdit();
    if (error != EmbLibraryError.EMB_NO_ERR) {
      return;
    }
    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return;
    }

    /// Model更新
    PatternModel()
      ..reloadAllPattern()
      ..clearTemporaryPatternList();
    SelectModel()
      ..selectedCategoryType = null
      ..selectedPatternIndex = null;
    if (RedoUndoModel().saveUndoRedoFile() != EmbLibraryError.EMB_NO_ERR) {
      GlobalPopupRoute().updateErrorState(
        nextRoute: GlobalPopupRouteEnum.ERR_TROUBLE_OCCORED_POWER_OFF,
        arguments: TroubleOccoredPowerOffArgument(
          onOKButtonClicked: GlobalPopupRoute().resetErrorState,
        ),
      );
      return;
    }
    SelectInformationModel()
        .setTemporaryPatternAutoKind(CategoryType.unknownPattern);

    PagesRoute().pushNamedAndRemoveUntil(
      nextRoute: PageRouteEnum.patternEdit,
      untilRoute: PageRouteEnum.home,
    );
  }

  @override
  void onReturnButtonClicked() {
    /// Model更新
    PatternModel().deleteTemporaryPatternList();
    SelectInformationModel()
        .setTemporaryPatternAutoKind(CategoryType.unknownPattern);
    SelectModel()
      ..selectedCategoryType = null
      ..selectedPatternIndex = null;

    PagesRoute().pushReplacement(nextRoute: PageRouteEnum.patternSelect);
  }

  ///
  /// Viewは最初にPattern情報変換を使用する
  ///
  List<PatternDisplayInfo> _getTemporaryGroupDisplayInfoList() {
    if (PatternModel().temporaryPatternList.isEmpty) {
      return [];
    }

    List<PatternDisplayInfo> temporaryGroupDisplayInfoList = [];

    /// プレビュー中心点
    final Offset originalCenter = selectPreviewSize / 2;

    /// 1mmに対応する画素数
    double pixelOfOneMm = selectPreviewSize.dx / frame297x465MmSize.dx;

    /// カレントグループのハンドル
    final currentGroupHandle =
        EmbLibrary().apiBinding.getCurrentGroupHandle().handle;

    PatternModel().temporaryPatternList.asMap().forEach((sewingIndex, pattern) {
      if (pattern is EmbGroup) {
        temporaryGroupDisplayInfoList.add(
          PreviewModel().getGroupPatternDisplayInfo(
            scrollType: ScrollCenterType.IMAGE_SELECT,
            group: pattern,
            centerPoint: originalCenter,
            pixelOfOneMm: pixelOfOneMm,
            sewingIndex: 0,
            currentGroupHandle: currentGroupHandle,
            zoomScale: PatternModel().selectedZoomScaleInSelectPage,
          ),
        );
      } else if (pattern is EmbBorder) {
        temporaryGroupDisplayInfoList.add(
          PreviewModel().getBorderPatternDisplayInfo(
            scrollType: ScrollCenterType.IMAGE_SELECT,
            border: pattern,
            centerPoint: originalCenter,
            pixelOfOneMm: pixelOfOneMm,
            sewingIndex: 0,
            currentGroupHandle: currentGroupHandle,
            zoomScale: PatternModel().selectedZoomScaleInSelectPage,
          ),
        );
      } else {
        /// Do noting
      }
    });

    return temporaryGroupDisplayInfoList;
  }

  ///
  /// Viewは最初にPattern情報変換を使用する
  ///
  List<PatternDisplayInfo> _getPatternDisplayInfoList() {
    if (PatternModel().getAllPattern().isEmpty) {
      return [];
    }

    List<PatternDisplayInfo> patternDisplayInfoList = [];

    /// プレビュー中心点
    final Offset originalCenter = selectPreviewSize / 2;

    /// 1mmに対応する画素数
    double pixelOfOneMm = selectPreviewSize.dx / frame297x465MmSize.dx;

    /// カレントグループのハンドル
    final currentGroupHandle =
        EmbLibrary().apiBinding.getCurrentGroupHandle().handle;

    PatternModel().getAllPattern().asMap().forEach((sewingIndex, pattern) {
      if (pattern is EmbGroup) {
        patternDisplayInfoList.add(
          PreviewModel().getGroupPatternDisplayInfo(
            scrollType: ScrollCenterType.IMAGE_SELECT,
            group: pattern,
            centerPoint: originalCenter,
            pixelOfOneMm: pixelOfOneMm,
            sewingIndex: sewingIndex,
            currentGroupHandle: currentGroupHandle,
            zoomScale: PatternModel().selectedZoomScaleInSelectPage,
          ),
        );
      } else if (pattern is EmbBorder) {
        patternDisplayInfoList.add(
          PreviewModel().getBorderPatternDisplayInfo(
            scrollType: ScrollCenterType.IMAGE_SELECT,
            border: pattern,
            centerPoint: originalCenter,
            pixelOfOneMm: pixelOfOneMm,
            sewingIndex: sewingIndex,
            currentGroupHandle: currentGroupHandle,
            zoomScale: PatternModel().selectedZoomScaleInSelectPage,
          ),
        );
      } else {
        /// Do noting
      }
    });

    return patternDisplayInfoList;
  }
}
