import 'dart:typed_data';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'common_footer_view_interface.freezed.dart';

@freezed
class CommonFooterState with _$CommonFooterState {
  const factory CommonFooterState({
    @Default(ButtonState.normal) ButtonState footerButtonState,
    @Default(false) bool isPdfButtonVisible,
    @Default(false) bool isScreenShot,
    @Default(false) bool isClockDisplay,
    @Default('') String footerTipMessageString,
    @Default(null) Uint8List? clockSvgData,
  }) = _CommonFooterState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class CommonFooterViewInterface extends ViewModel<CommonFooterState> {
  CommonFooterViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  Ref ref;

  void onPdfButtonClick(BuildContext context);
  void onClockButtonClick(BuildContext context);
  void onScreenshotButtonClick(BuildContext context);
}
