import 'dart:math';
import 'dart:ui' as ui;
import 'dart:ui';

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../model/preview_area_size_model.dart';
import '../../../app/emb/model/key_board_font_model.dart';
import '../../../app/emb/model/pattern_model.dart';
import '../../../app/emb/model/scan_model.dart';
import '../../../app/emb/model/select_information_model.dart';
import '../real_preview_view_interface.dart';

///
/// 再生状態
///
enum RealPreviewAction {
  pause,
  play,
  stop,
}

///
/// 再生速度
///
enum RealPreviewSpeed {
  low(linePerTick: 50),
  middle(linePerTick: 100),
  high(linePerTick: 300);

  const RealPreviewSpeed({
    required this.linePerTick,
  });

  /// 1回の描画で処理されるステッチの数
  final int linePerTick;
}

///
/// アニメーションコントローラ
///
class RealPreviewController extends ViewModel {
  RealPreviewController({
    required this.type,
    required List<EmbStitchPos> stitchData,
    void Function()? onStop,
  })  : _onStop = onStop,
        super(null) {
    _speed = RealPreviewSpeed.low;
    pattenStitchList = stitchData;
    currentCount = stitchData.length;

    final allGroup = _getAllEmbGroupByRealPreviewType(type);

    patternRectArea = _calculatePatternGroupLeftRightTopBottom(allGroup);

    _initPatternSize(patternRectArea);

    currentScale = _scaleZoomOut;
    patternOffset = _calculatePatternGroupsCenterOffset(
        patternRectArea.left, patternRectArea.top);
    _initAllTranslate();
    _decodeBackgroundImage();
    _initAllTranslate();
  }

  /// アニメーションの種類
  final RealPreviewType type;

  /// キャンバスの幅
  static const double canvasWidth = 470;

  /// キャンバスの高さ
  static const double canvasHeight = 772;

  /// 最大スケールのマージン
  static const double maxScaleMargin = 25;

  /// ピクセルソースデータと現在の表示位置
  List<EmbStitchPos> pattenStitchList = [];

  /// 現在のカウント
  int currentCount = 0;

  /// 現在の画像
  ui.Image? currentZoomInImage;

  ui.Image? currentZoomOutImage;

  /// 背景画像
  ui.Image? backgroundImage;

  /// パターンの再描画が必要かどうか
  bool shouldRepaintPattern = false;

  /// 背景の再描画が必要かどうか
  bool shouldRepaintBackground = false;

  /// 針の再描画が必要かどうか
  bool shouldRepaintNeedle = false;

  /// スケールが変更されたかどうか
  bool isScaleChanged = false;

  /// ラスタライズ中かどうか
  bool isRasterizing = false;

  /// 最後に描画されたインデックス
  int previousPaintIndex = -1;

  /// 色を変更する必要があるかどうかのフラグ
  bool shouldChangeColor = false;

  /// 前回の描画位置
  ui.Offset? previousPoint;

  /// 描画用のPaintオブジェクトの線幅
  static const double _paintStrokeWidth = 2.0;

  /// 描画用のPaintオブジェクト
  final Paint paint = Paint()
    ..strokeWidth = _paintStrokeWidth
    ..style = PaintingStyle.stroke
    ..isAntiAlias = true;

  /// 現在のスケール
  double currentScale = 0;

  /// 背景のクリッピング幅
  double backgroundClipWidth = 0;

  /// 背景のクリッピング高さ
  double backgroundClipHeight = 0;

  /// パターンの幅
  double _patternWidth = 0;

  /// パターンの高さ
  double _patternHeight = 0;

  /// スケールされたパターンの幅
  double get scaledPatternWidth => _patternWidth * currentScale;

  /// スケールされたパターンの高さ
  double get scaledPatternHeight => _patternHeight * currentScale;

  /// パターンのX軸方向の移動量
  double patternTranslateX = 0;

  /// パターンのY軸方向の移動量
  double patternTranslateY = 0;

  // Y軸方向の平行移動量：パターンを垂直方向に少し上に移動する
  static const double _baseCanvasTranslateY = 34;

  /// 座標調整用の係数。Libが返す長さは単位が0.1mmなので、10で割る必要があります。
  static const double _mmFactor = 10.0;

  /// キャンバス変換X
  double canvasTranslateX = 0;

  /// キャンバス変換Y
  double canvasTranslateY = 0;

  /// 背景の変換X
  double backgroundTranslateX = 0;

  /// 背景の変換Y
  double backgroundTranslateY = 0;

  /// 座標極値
  RectanArea patternRectArea = RectanArea.maxArea;

  /// パターンのオフセット
  late Offset patternOffset;

  /// 縮小時かどうか
  bool get isZoomOut => currentScale < _scaleZoomIn;

  ///
  /// ズームアウト時のスケール
  ///
  double get _scaleZoomOut => min(
      canvasWidth / embPreviewSizeDot.dx, canvasHeight / embPreviewSizeDot.dy);

  ///
  /// ズームイン時のスケール
  ///
  double get _scaleZoomIn {
    final widthRatio = (imageWidth - maxScaleMargin * 2) / _patternWidth;
    final heightRatio = imageHeight / _patternHeight;
    return min(widthRatio, heightRatio);
  }

  /// ズームイン時の画像
  ui.Image? zoomInImage;

  /// ズームアウト時の画像
  ui.Image? zoomOutImage;

  bool isPrintBackgroundImage = false;

  ///
  /// パターンの変更通知を作成する
  ///
  ChangeNotifier createPatternNotifier(TickerProvider tickerProvider) =>
      _patternNotifier = _RealPreviewChangeNotifier(tickerProvider, this);

  ///
  /// 背景の変更通知を作成する
  ///
  ChangeNotifier createBackgroundNotifier(TickerProvider tickerProvider) =>
      _backgroundNotifier = _RealPreviewChangeNotifier(tickerProvider, this);

  ///
  /// RealPreview がビジー状態かどうかを示すフラグ。[true]の場合、
  /// ユーザーインタラクションイベントがブロックされ、 ユーザーによる状態の変更が防止され、
  /// RealPreviewパターンのレンダリングエラーが防止されます。
  ///
  final ValueNotifier<bool> _isBusy = ValueNotifier(false);

  ///
  /// RealPreview をビジー状態としてマーク
  ///
  void setIsBusy() => _isBusy.value = true;

  ///
  /// RealPreview を非ビジーとしてマーク
  ///
  void setNotBusy() => _isBusy.value = false;

  ///
  /// @see [_isBusy]
  ///
  bool isBusy() => _isBusy.value;

  ///
  /// RealPreview の ビジー状態の監視
  ///
  /// - return: このメソッドが返す関数は、リスニングを解除出来ます。
  ///
  void Function() addIsBusyListener(Function(bool isBusyNow) listener) {
    void realListener() {
      listener.call(_isBusy.value);
    }

    _isBusy.addListener(realListener);
    return () => _isBusy.removeListener(realListener);
  }

  ///
  /// RealPreview がキャンセルされたかどうかを示すフラグ。[true]の場合、
  /// すべての操作がキャンセルされ、RealPreview が閉じられます。
  ///
  bool _isDisposed = false;

  ///
  /// @see [_isDisposed]
  ///
  bool isDisposed() => _isDisposed;

  ///
  /// すべてのパターングループの中心位置を計算します
  ///
  /// 戻り値:
  /// [Offset] - すべてのパターングループの中心位置を表すオフセット
  ///
  Offset _calculatePatternGroupsCenterOffset(int left, int top) {
    // ミリメートルからピクセルに変換
    final leftPx = left / _mmFactor * embPreviewSize_dx_pixelOfOneMm;
    final topPx = top / _mmFactor * embPreviewSize_dy_pixelOfOneMm;

    // パターンの中心位置を計算し、現在のスケールを適用
    return Offset(leftPx + _patternWidth / 2, topPx + _patternHeight / 2);
  }

  ///
  /// 指定されたパターングループの座標極値を計算します
  /// パターン情報とグループのオフセット位置から、グループ全体の座標極値を求めます
  ///
  RectanArea _calculatePatternGroupLeftRightTopBottom(List<EmbGroup> allGroup) {
    // 各グループのの座標極値を取得
    final RectanArea result = allGroup.map((group) {
      final List<EmbPtrn> patternInfos =
          group.embGroupInfo.embPatternInfo.embPatterns;
      final SSPoint groupOffset = group.embGroupInfo.embGrp.position;
      final SSPoint frameOffset = group.embGroupInfo.embInfo.frameOffset;

      final result = patternInfos.map((e) {
        final width = e.packedMask.right - e.packedMask.left;
        final height = e.packedMask.bottom - e.packedMask.top;
        final xOffset = ((e.packedMask.right + e.packedMask.left) / 2).round() +
            e.inGrpOffset.X +
            groupOffset.X +
            frameOffset.X;
        final yOffset = ((e.packedMask.bottom + e.packedMask.top) / 2).round() +
            e.inGrpOffset.Y +
            groupOffset.Y +
            frameOffset.Y;
        return RectanArea(
          left: xOffset - (width / 2).round(),
          right: xOffset + (width / 2).round(),
          top: yOffset - (height / 2).round(),
          bottom: yOffset + (height / 2).round(),
        );
      }).reduce((a, b) => RectanArea(
            left: min(a.left, b.left),
            right: max(a.right, b.right),
            top: min(a.top, b.top),
            bottom: max(a.bottom, b.bottom),
          ));

      return result;
    }).reduce((a, b) => RectanArea(
          left: min(a.left, b.left),
          right: max(a.right, b.right),
          top: min(a.top, b.top),
          bottom: max(a.bottom, b.bottom),
        ));

    return result;
  }

  ///
  /// realpreviewのタイプに基づいてEmbGroupデータを取得する
  ///
  List<EmbGroup> _getAllEmbGroupByRealPreviewType(RealPreviewType type) {
    final List<EmbGroup> allGroup = [];
    if (type == RealPreviewType.emb) {
      allGroup.addAll(PatternModel().getAllGroup());
      allGroup.addAll(PatternModel().temporaryGroupList);
      allGroup.addAll(KeyBoardFontModel().getAllGroup());
    } else {
      final List<MemHandle> handleList =
          EmbLibrary().apiBinding.getGroupHandleAll().handleList;
      for (var element in handleList) {
        allGroup.add(EmbGroup(element));
      }
    }
    return allGroup;
  }

  ///
  /// パターンのサイズを初期化する
  ///
  void _initPatternSize(RectanArea rectArea) {
    _patternWidth = (rectArea.right - rectArea.left) /
        _mmFactor *
        embPreviewSize_dx_pixelOfOneMm;
    _patternHeight = (rectArea.bottom - rectArea.top) /
        _mmFactor *
        embPreviewSize_dy_pixelOfOneMm;
  }

  ///
  /// 変換を初期化する
  ///
  void _initAllTranslate() {
    final scaledPatternOffset = patternOffset * currentScale;

    patternTranslateX =
        (_patternWidth / 2 - embPreviewSizeDot.dx / 2) * currentScale -
            scaledPatternOffset.dx;
    patternTranslateY =
        (_patternHeight / 2 - embPreviewSizeDot.dy / 2) * currentScale -
            scaledPatternOffset.dy;
    canvasTranslateX = imageWidth / 2 - scaledPatternWidth / 2;
    canvasTranslateY =
        _baseCanvasTranslateY + canvasHeight / 2 - scaledPatternHeight / 2;

    if (isZoomOut) {
      canvasTranslateX += scaledPatternOffset.dx;
      canvasTranslateY += scaledPatternOffset.dy;
      _initBackgroundTranslate(Offset.zero);
    } else {
      _initBackgroundTranslate(scaledPatternOffset);
    }
  }

  /// 背景のスケール
  double printBackgroundScale = 0;

  ///
  /// 背景の変換を初期化する
  ///
  void _initBackgroundTranslate(Offset patternOffset) {
    if (backgroundImage == null) return;

    final double scaleBackgroundWidth;
    final double scaleBackgroundHeight;

    final margin = getPrintBackgroundMargin();

    if (isPrintBackgroundImage) {
      printBackgroundScale =
          currentScale * (_patternWidth + margin.$1) / backgroundImage!.width;
      scaleBackgroundWidth = backgroundImage!.width * printBackgroundScale;
      scaleBackgroundHeight = backgroundImage!.height * printBackgroundScale;
      backgroundClipWidth += margin.$3;
    } else {
      scaleBackgroundWidth = backgroundImage!.width * currentScale;
      scaleBackgroundHeight = backgroundImage!.height * currentScale;
    }
    backgroundTranslateX = imageWidth / 2 -
        scaleBackgroundWidth / 2 -
        patternOffset.dx +
        margin.$3;
    backgroundTranslateY = _baseCanvasTranslateY +
        margin.$2 +
        canvasHeight / 2 -
        scaleBackgroundHeight / 2 -
        patternOffset.dy;
  }

  ///
  /// 背景のマージンを取得する
  ///
  (int, int, int) getPrintBackgroundMargin() {
    return switch (SelectInformationModel().printAndStitchType) {
      PrintAndStitchType.whale => isZoomOut ? (4, 5, 0) : (4, 7, 0),
      PrintAndStitchType.bambi => isZoomOut ? (40, 8, 5) : (45, 18, 14),
      PrintAndStitchType.normal => (0, 0, 0)
    };
  }

  ///
  /// パターンの座標を変換する
  ///
  Offset transformPatternCoordinate(int x, int y) {
    final adjustedX =
        (x / _mmFactor) * embPreviewSize_dx_pixelOfOneMm * currentScale;
    final adjustedY =
        (y / _mmFactor) * embPreviewSize_dy_pixelOfOneMm * currentScale;
    return Offset(adjustedX, adjustedY);
  }

  ///
  /// 針の座標を変換する
  ///
  Offset transformNeedleCoordinate(Offset offset) {
    final x = offset.dx * currentScale + patternTranslateX + canvasTranslateX;
    final y = offset.dy * currentScale + patternTranslateY + canvasTranslateY;
    return Offset(x, y);
  }

  ///
  /// すべての縫製が完了した後、背景画像を閉じてください
  ///
  void hideBackgroundImage() {
    backgroundImage = null;
    shouldRepaintBackground = true;
    _backgroundNotifier?.repaint();
  }

  ///
  /// 画像デコード後の処理
  ///
  void _doAfterImageDecoded(ui.Image? image) {
    backgroundImage = image;
    _initBackgroundTranslate(Offset.zero);
    shouldRepaintBackground = true;
    _backgroundNotifier?.repaint();
  }

  ///
  /// 背景画像をデコードする
  ///
  void _decodeBackgroundImage() {
    if (type == RealPreviewType.mdc) {
      // MDCはスキャンした背景を表示しない
      return;
    }

    final ui.Image? printBackgroundImage =
        SelectInformationModel().printBackgroundImage;
    if (printBackgroundImage != null) {
      isPrintBackgroundImage = true;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _doAfterImageDecoded(printBackgroundImage);
      });
      return;
    }
    if (ScanModel().hasBackgroundImage == false ||
        ScanModel().getBackgroundShowStatus() == false) return;
    ScanModel().decodeBackgroundImageAsync().then(_doAfterImageDecoded);
  }

  ///
  /// フルイメージを設定する
  ///
  void setCurrentImage(bool isZoomOut, bool disposeAll, ui.Image? image) {
    if (isZoomOut) {
      currentZoomOutImage?.dispose();
      currentZoomOutImage = null;
      if (disposeAll) {
        currentZoomInImage?.dispose();
        currentZoomInImage = null;
      }
      currentZoomOutImage = image;
    } else {
      currentZoomInImage?.dispose();
      currentZoomInImage = null;
      if (disposeAll) {
        currentZoomOutImage?.dispose();
        currentZoomOutImage = null;
      }
      currentZoomInImage = image;
    }
    if (this.isZoomOut != isZoomOut) return;
    if (_requirePatternNotifier().isTicking) {
      return;
    }
    shouldRepaintPattern = true;
    _requirePatternNotifier().repaint();
  }

  ///
  /// フルイメージを設定する
  ///
  void setFullImage(bool isZoomOut, ui.Image? image) {
    if (isZoomOut) {
      zoomOutImage = image;
    } else {
      zoomInImage = image;
    }
    if (this.isZoomOut != isZoomOut) return;
    if (_requirePatternNotifier().isTicking) {
      return;
    }
    shouldRepaintPattern = true;
    _requirePatternNotifier().repaint();
  }

  ///
  /// フレームサイズの設定
  ///
  void changeFrameSize(RealPreviewFrameType embFrameType) {
    final (frameWidth, frameHeight) = switch (embFrameType) {
      RealPreviewFrameType.frame297x465 => (297, 465),
      RealPreviewFrameType.frame272x272 => (272, 272),
      RealPreviewFrameType.frame130x180 => (130, 180),
      RealPreviewFrameType.frame100x100 => (100, 100),
    };

    final frameWidthInPx = frameWidth * embPreviewSize_dx_pixelOfOneMm;

    backgroundClipWidth =
        max(frameWidthInPx - maxScaleMargin * 2, scaledPatternWidth);

    backgroundClipHeight = frameHeight / frameWidth * backgroundClipWidth;

    shouldRepaintBackground = true;
  }

  ///
  /// 描画を開始する
  ///
  void play() {
    if (playerState != RealPreviewAction.pause) {
      _reset();
    }
    playerState = RealPreviewAction.play;
    _requirePatternNotifier().startTicking();
  }

  ///
  /// 拡大
  ///
  void zoomIn() {
    currentScale = _scaleZoomIn;
    _initAllTranslate();
    isScaleChanged = true;
    shouldRepaintPattern = true;
    shouldRepaintBackground = true;
    shouldRepaintNeedle = true;
    _transformPreviousPointWithNewScale();
  }

  ///
  /// 縮小
  ///
  void zoomOut() {
    currentScale = _scaleZoomOut;
    _initAllTranslate();
    isScaleChanged = true;
    shouldRepaintPattern = true;
    shouldRepaintBackground = true;
    shouldRepaintNeedle = true;
    _transformPreviousPointWithNewScale();
  }

  ///
  /// 一時停止
  ///
  void pause() {
    _requirePatternNotifier().stopTicking();
    playerState = RealPreviewAction.pause;
  }

  ///
  /// 次の新しい描画の準備をするためにブラシパラメータをリセットします。
  ///
  void resetPaint() {
    shouldChangeColor = false;
    previousPoint = null;
    paint.shader?.dispose();
    paint.shader = null;
  }

  ///
  /// ズームの変更時、直前に描画した点の座標を現在のズームサイズに対応する座標に変換する。
  ///
  void _transformPreviousPointWithNewScale() {
    final Offset? point = previousPoint;
    if (point == null) {
      return;
    }
    final double previousScale;
    if (isZoomOut) {
      previousScale = _scaleZoomIn;
    } else {
      previousScale = _scaleZoomOut;
    }
    final Offset newPoint = Offset(point.dx / previousScale * currentScale,
        point.dy / previousScale * currentScale);
    previousPoint = newPoint;
  }

  ///
  /// 描画アニメーションをリセットする
  ///
  void _reset() {
    resetPaint();
    currentCount = 0;
    previousPaintIndex = -1;
    currentZoomInImage?.dispose();
    currentZoomInImage = null;
    currentZoomOutImage?.dispose();
    currentZoomOutImage = null;
  }

  ///
  /// 描画アニメーションを終了し、完全なフレームを直接描画する
  ///
  void stop() {
    _reset();
    shouldRepaintPattern = playerState != RealPreviewAction.stop;
    playerState = RealPreviewAction.stop;
    _requirePatternNotifier().stopTicking();
    _onStop?.call();
  }

  @override
  void dispose() {
    if (isDisposed()) {
      return;
    }
    _isDisposed = true;
    currentZoomInImage?.dispose();
    currentZoomOutImage?.dispose();
    pattenStitchList.clear();
    _requirePatternNotifier().dispose();
    _backgroundNotifier?.dispose();
    zoomInImage?.dispose();
    zoomOutImage?.dispose();
    super.dispose();
  }

  ///
  /// 再生速度の設定
  ///
  void setSpeed(RealPreviewSpeed speed) => _speed = speed;

  ///
  /// パターンの変更通知を取得する
  ///
  _RealPreviewChangeNotifier _requirePatternNotifier() {
    if (_patternNotifier == null) {
      Log.assertTrace('Pattern notifier is not initialized');
    }
    return _patternNotifier!;
  }

  ///
  /// 再生停止時のコールバック関数
  ///
  final void Function()? _onStop;

  ///
  /// 再生速度
  ///
  RealPreviewSpeed get speed => _speed;
  RealPreviewSpeed _speed = RealPreviewSpeed.low;

  ///
  /// 再生状態
  ///
  RealPreviewAction get animationPlayState => playerState;
  RealPreviewAction playerState = RealPreviewAction.stop;

  ///
  /// ベースマップのサイズ
  ///
  static const int imageWidth = 800;
  static const int imageHeight = 838;

  /// パターンの変更通知
  _RealPreviewChangeNotifier? _patternNotifier;

  /// 背景の変更通知
  _RealPreviewChangeNotifier? _backgroundNotifier;
}

class _RealPreviewChangeNotifier extends ChangeNotifier {
  _RealPreviewChangeNotifier(TickerProvider tickerProvider, this._controller) {
    _ticker = tickerProvider.createTicker(_onTick);
  }

  late Ticker _ticker;

  final RealPreviewController _controller;

  ///
  /// アニメーション中かどうか
  ///
  bool get isTicking => _ticker.isTicking;

  ///
  /// 破棄する
  ///
  @override
  void dispose() {
    super.dispose();
    _ticker.dispose();
  }

  ///
  /// 描画を更新する
  ///
  void repaint() {
    notifyListeners();
  }

  ///
  /// 開始する
  ///
  void startTicking() {
    if (_ticker.isActive) {
      return;
    }
    _ticker.start();
  }

  ///
  /// 停止する
  ///
  void stopTicking() {
    if (_ticker.isActive == false) {
      return;
    }
    _ticker.stop();
  }

  void _onTick(Duration elapsed) {
    if (_controller.isRasterizing) {
      // ラスタライズ中は描画しない
      return;
    }
    notifyListeners();
  }
}
