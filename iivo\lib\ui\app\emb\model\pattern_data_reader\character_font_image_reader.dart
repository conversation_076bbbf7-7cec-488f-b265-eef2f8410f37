import 'dart:convert' show jsonDecode;

import 'package:flutter/material.dart';
import 'package:log/log.dart';
import 'package:path/path.dart';

import '../../../../../memory/extension/svg.dart';
import '../../../../../memory/memory.dart';
import '../../../../../model/app_locale.dart';
import '../../../../../model/machine_config_model.dart';
import 'image_data_serialize/character_font_image/character_font_image.dart';
import 'pattern_data_base.dart';

///
/// 文字フォント画像データの構造
///
class CharacterFontImageGroup extends PatternDataBase {
  CharacterFontImageGroup({
    required this.fontNumber,
    required this.name,
    required this.type,
    required this.pf1FileName,
    required this.image,
    required this.previewImage,
  });
  final String fontNumber;
  final String name;
  final String type;
  final String pf1FileName;
  final Widget image;
  final Image previewImage;
}

///
/// 文字フォントの画像データを制御する
///
class CharacterFontImageReader {
  factory CharacterFontImageReader() {
    _instance._initCategoryData();
    return _instance;
  }
  CharacterFontImageReader._internal();

  ///
  /// 文字フォントの画像データ制御クラス
  ///
  static final CharacterFontImageReader _instance =
      CharacterFontImageReader._internal();
  bool isInitialized = false;

  ///
  /// iconのPathを制御する
  ///
  static final String characterFontImageDir =
      join(memorySector.emb.absolutePath, "character_font_image_data");

  static final String characterFontImageDataDir =
      join(characterFontImageDir, "png");

  /// 画像ファイルのjsonのパス
  late String characterFontImageInfoFile;

  late List<CharacterFontImageGroup> allCharacterFontImageInfoCache;

  ///
  /// Iconイマジンjsonファイル解析結果を保存する
  ///
  late CharacterFontImage _imageJsonData;

  ///
  /// Json ファイルに保存されているのIconイマジンとメッセージデータを読み取ります
  ///
  void _initCategoryData() {
    if (!isInitialized) {
      isInitialized = true;
      _updateImageJsonData();
    }
  }

  ///
  /// 画像情報キャッシュを更新
  ///
  void updateCharacterFontImagesInfoCache() => _updateImageJsonData();

  ///
  /// 画像のデータソースを更新する
  ///
  void _updateImageJsonData() {
    characterFontImageInfoFile = getCharacterFontImageInfoFile();

    ///  Androidの模様のIconデータファイルを取得する
    FileEntity characterFontImageFile = FileEntity(characterFontImageInfoFile);

    /// 模様のIconデータを取得する
    if (characterFontImageFile.existsSync()) {
      _imageJsonData = CharacterFontImage.fromJson(
        jsonDecode(characterFontImageFile.readAsStringSync()),
      );

      allCharacterFontImageInfoCache =
          _getAllCharacterFontImagesInfo(_imageJsonData);
    } else {
      Log.assertTrace("characterFontImageFile is not exist !!!");
    }
  }

  ///
  /// 画像ファイルのJSONパスを取得する
  ///
  String getCharacterFontImageInfoFile() =>
      MachineConfigModel().getCurrentModel() == AppModelEnum.brother
          ? join(characterFontImageDir, "character_font_image_brother.json")
          : join(
              characterFontImageDir,
              AppLocale().isFrench
                  ? "character_font_image_tacony_fr.json"
                  : "character_font_image_tacony_en.json",
            );

  ///
  /// フォント番号に基づいて具体的なフォント情報を取得します。
  ///
  CharacterFontImageGroup getCharacterFontImagesInfoByFontNumber(
      int fontNumber) {
    for (CharacterFontImageGroup info in allCharacterFontImageInfoCache) {
      if (info.fontNumber == fontNumber.toString()) {
        return info;
      }
    }

    Log.assertTrace("フォントが誤っており、そのフォントファイルは存在しません。");

    return allCharacterFontImageInfoCache.first;
  }

  ///
  /// 全ての文字フォントの画像データを取得する
  ///
  /// ##@return
  /// - List<CharacterFontImageGroup>: 順番保存されているの模様イコンデータ
  ///
  List<CharacterFontImageGroup> getAllCharacterFontImagesInfo() =>
      allCharacterFontImageInfoCache;

  List<CharacterFontImageGroup> _getAllCharacterFontImagesInfo(
      CharacterFontImage imageJsonData) {
    List<CharacterFontImageGroup> characterFontImageData = [];

    imageJsonData.category.asMap().entries.forEach((element) {
      characterFontImageData.add(
        CharacterFontImageGroup(
          fontNumber: element.value.fontNumber,
          name: element.value.name,
          type: element.value.type,
          pf1FileName: element.value.pf1FileName,
          image: SvgPictureExtension.fileEntity(
            FileEntity(join(characterFontImageDataDir, element.value.image)),
          ),
          previewImage: ImageExtension.fileEntity(
            FileEntity(join(
                characterFontImageDataDir, "${element.value.pf1FileName}.png")),
          ),
        ),
      );
    });

    return characterFontImageData;
  }
}
