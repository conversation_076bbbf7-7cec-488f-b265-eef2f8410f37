import 'dart:async';
import 'dart:ffi';

import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';

import '../../../../../../../model/projector_model.dart';
import '../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../model/edit_model.dart';
import '../../../../model/pattern_model.dart';
import '../../../../model/preview_model.dart';
import '../../../../model/scan_model.dart';
import '../../../component/emb_header/emb_header_view_model.dart';
import '../../common_component/function_provider/projector_function_provider/projector_function_provider.dart';
import '../../common_component/projector_setting_popup/projector_settings_popup.dart';
import '../bottom/bottom_view_model.dart';
import '../pattern_edit_view_model.dart';
import '../preview/preview_view_model.dart';
import '../top_bar/top_bar_view_model.dart';
import 'edit_menu_view_interface.dart';

final editMenuViewModelProvider =
    StateNotifierProvider.autoDispose<EditMenuViewInterface, EditMenuState>(
        (ref) => EditMenuViewModel(ref));

class EditMenuViewModel extends EditMenuViewInterface {
  EditMenuViewModel(Ref ref)
      : super(
            const EditMenuState(
              isEditPopupOn: false,
              isColorListDisplay: false,
              isProjectorON: false,
              isProjectorEnable: false,
            ),
            ref) {
    update();

    _listenStatus();
  }

  @override
  void update() {
    bool isEditPopupOn = EditModel().toolbarPopupId != ToolbarPopupId.none;

    state = state.copyWith(
      isEditPopupOn: isEditPopupOn,
      isColorListDisplay: ScanModel().isEditPageScanPopupOpen == false &&
          isEditPopupOn == false,
      isProjectorON: ProjectorModel().embProjector.isEmbProjectorViewOpen,
    );
  }

  @override
  void onEditButtonClicked(BuildContext context) {
    if (state.isEditPopupOn == true) {
      closeEditPopup(context);
    } else {
      openEditPopup(context);
    }
  }

  @override
  void openEditPopup(BuildContext context) {
    /// toolbarPopupIdフラグを変更
    EditModel().toolbarPopupId = ToolbarPopupId.edit;

    /// toolBar開ける
    PopupNavigator.pushNamed(
        context: context, nextRouteName: PopupEnum.toolBar);

    /// 画面更新
    ///
    /// 自分を更新する
    /// 画面に他のWidgetのUIを更新すＲ
    update();
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);
  }

  @override
  void closeEditPopup(BuildContext context) {
    /// 他の機能弾窓が開いているか
    if (EditModel().toolbarPopupId != ToolbarPopupId.edit) {
      PopupNavigator.pop(context: context);

      /// Model更新
      EditModel().toolbarPopupId =
          EditModel().toolbarPopupId == ToolbarPopupId.multipleSelection
              ? EditModel().multipleBackupToolbarPopupId
              : ToolbarPopupId.edit;
    } else {
      /// View更新
      EditModel().toolbarPopupId = ToolbarPopupId.none;
      update();
      PopupNavigator.pop(context: context);
    }

    /// 他の画面を更新する
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);
  }

  @override
  void onProjectorButtonClick(BuildContext context) {
    /// プロジェクト起動・停止前にエラーCheckをする
    /// エラーがあれば ポープアープを表示する
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    final hasError = embProjectorFunction
        .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
    if (hasError) {
      return;
    }

    Log.d(
      tag: "onProjectorButtonClick",
      description:
          "isEmbProjectorViewOpen:${ProjectorModel().embProjector.isEmbProjectorViewOpen}",
    );

    if (ProjectorModel().embProjector.isEmbProjectorViewOpen == false) {
      _openProjectorView(context);
    } else {
      _closeProjectorView(context);
    }
  }

  @override
  void onProjectorSettingButtonClick(BuildContext context) {
    PopupNavigator.push(
      context: context,
      popup: PopupRouteBuilder(
        builder: (context) => const ProjectorSettingPopup(),
        barrier: true,
      ),
    ).then((value) {
      /// 他の画面を更新する
      ref
          .read(patternEditViewModelProvider.notifier)
          .updateEditPageByChild(ModuleType.toolBar);
    });
  }

  ///
  /// 投影画面に遷移
  ///
  Future<void> _openProjectorView(BuildContext context) async {
    /// 投影画面が開きます 表示用データ設定する
    ///
    /// isEmbProjectorViewOpen
    /// isProjectorON
    ProjectorModel().embProjector.isEmbProjectorViewOpen = true;
    state = state.copyWith(
      isProjectorON: ProjectorModel().embProjector.isEmbProjectorViewOpen,
    );

    /// プロジェクト起動
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);
    embProjectorFunction.startEmbProjectorView(
      beforeFrameMoveCallback: () {
        /// ズームが100%にリセットされる
        PatternModel().selectedZoomScaleInEditPage = zoomList.first;

        /// ドラッグプレビューモードかどうか
        PreviewModel().isInDragPreviewMode = false;

        /// 複数選択ポップアップウィンドウが開いている場合

        if (state.isEditPopupOn == true &&
            EditModel().toolbarPopupId == ToolbarPopupId.multipleSelection) {
          /// 複数選択ポップアップウィンドウを閉じます
          PopupNavigator.pop(context: context);

          /// toolbarPopupIdフラグを変更
          EditModel().toolbarPopupId = ToolbarPopupId.edit;

          /// toolBar開ける
          PopupNavigator.pushNamed(
              context: context, nextRouteName: PopupEnum.toolBar);

          /// 自分を更新する
          update();
        } else if (state.isEditPopupOn == false) {
          /// toolbarPopupIdフラグを変更
          EditModel().toolbarPopupId = ToolbarPopupId.edit;

          /// toolBar開ける
          PopupNavigator.pushNamed(
              context: context, nextRouteName: PopupEnum.toolBar);

          /// 自分を更新する
          update();
        } else if (state.isEditPopupOn == true &&
            EditModel().toolbarPopupId == ToolbarPopupId.edit) {
          /// toolbarPopupIdフラグを変更
          EditModel().toolbarPopupId = ToolbarPopupId.edit;

          ref
              .read(patternEditViewModelProvider.notifier)
              .updateEditPageByChild(ModuleType.projector);
        } else {
          /// Do Nothing
        }

        ref.read(embHeaderViewModelProvider.notifier).update();
        ref.read(bottomViewModelProvider.notifier).update();
        ref.read(topBarViewModelProvider.notifier).update();
        ref.read(previewViewModelProvider.notifier).update();
      },
      removedFrameCallback: () {
        if (ProjectorModel().embProjector.isBackgroundColorPopOpen) {
          /// 複数選択ポップアップウィンドウを閉じます
          PopupNavigator.pop(context: context);
        }
        if (ProjectorModel().embProjector.isProjectorSettingsPopOpen) {
          /// 複数選択ポップアップウィンドウを閉じます
          PopupNavigator.pop(context: context);
        }
        if (ProjectorModel().embProjector.isMemoryPopOpen) {
          /// 複数選択ポップアップウィンドウを閉じます
          PopupNavigator.pop(context: context);
        }

        ProjectorModel().embProjector.isEmbProjectorViewOpen = false;
        _updateByCloseProjector();
      },
    );
  }

  ///
  /// 投影画面をオフにする
  ///
  Future<void> _closeProjectorView(BuildContext context) async {
    final embProjectorFunction =
        ref.read(embProjectorFunctionProvider.notifier);

    /// プロジェクト起動・停止前にエラーCheckをする
    /// エラーがあれば ポープアープを表示する
    final hasError = embProjectorFunction
        .checkErrorBeforeEmbProjectorStartCloseAndPushErrorPopup();
    if (hasError) {
      return;
    }

    embProjectorFunction.closeEmbProjectorView(
      closingHandleCallback: () {
        ProjectorModel().embProjector.isEmbProjectorViewOpen = false;
        _updateByCloseProjector();
      },
      afterClosedHandleCallback: () {},
    );
  }

  ///
  /// 画面更新（プロジェクト閉じる）
  ///
  void _updateByCloseProjector() {
    update();
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.toolBar);
    ref
        .read(patternEditViewModelProvider.notifier)
        .updateEditPageByChild(ModuleType.projector);
    ref.read(embHeaderViewModelProvider.notifier).update();
    ref.read(patternEditViewModelProvider.notifier).update();
  }

  ///
  /// 状態をリッスンします
  ///
  void _listenStatus() {
    ref.listen(
      fireImmediately: true,
      appDisplayEmbStateProvider
          .select((value) => value.embEditAttrib.ref.projectorState),
      (previous, nextState) {
        state = state.copyWith(
          isProjectorEnable: AppInfoFuncState.getValueByNumber(nextState) ==
              AppInfoFuncState.enable,
        );
      },
    );
  }
}
