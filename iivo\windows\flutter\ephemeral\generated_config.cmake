# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\fvm\\versions\\3.19.0" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\GitlabProjects\\PH_IIVO_PROJ\\ph-iivo-app\\iivo" PROJECT_DIR)

set(FLUTTER_VERSION "1.3.11+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 3 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 11 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\fvm\\versions\\3.19.0"
  "PROJECT_DIR=C:\\Users\\<USER>\\GitlabProjects\\PH_IIVO_PROJ\\ph-iivo-app\\iivo"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\fvm\\versions\\3.19.0"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\GitlabProjects\\PH_IIVO_PROJ\\ph-iivo-app\\iivo\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\GitlabProjects\\PH_IIVO_PROJ\\ph-iivo-app\\iivo"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\GitlabProjects\\PH_IIVO_PROJ\\ph-iivo-app\\iivo\\lib\\panel.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC8wNDgxN2M5OWM5ZmQ0OTU2ZjI3NTA1MjA0ZjdlMzQ0MzM1ODEwYWVkLw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\GitlabProjects\\PH_IIVO_PROJ\\ph-iivo-app\\iivo\\.dart_tool\\package_config.json"
)
