import 'dart:async';
import 'dart:ffi';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:system_config/system_config.dart';

import '../ui/app/setting/model/general_setting_page2_model.dart';
import '../ui/app/utl/model/pattern_data_type/pattern_property.dart';
import '../ui/app/utl/model/pattern_model.dart';
import '../ui/global_popup/global_popup_route.dart';
import 'input_monitor_model.dart';
import 'machine_config_model.dart';
import 'out_key_lock_model.dart';

class PanelEcoModel with _ScreenSaverController {
  PanelEcoModel._internal();

  factory PanelEcoModel() => _instance;
  static final PanelEcoModel _instance = PanelEcoModel._internal();

  static const _customScreenPackage = "com.brother.ph.iivo.screensaver";
  static const _logTag = "PanelEcoModel";

  /// タイマーの時間間隔 200ms
  static const int _intervalTime = 200;

  /// Shut offの最大値は12h  _timerRoundsCountMax = 12 * 60 * 60 * 1000 / _intervalTime + 1
  static const int _timerRoundsCountMax = 216001;

  /// 設定値をMinuteを変換
  int _screenSaverTimeMinute = 0;
  int _ecoTimeMinute = 0;
  int _shutOffTimeMinute = 0;

  /// タイマー
  Timer? _timer;

  /// Screen Saverモードに入る, タイマーの実行回数
  int _screenSaverTimerRounds = 0;

  /// Ecoモードに入る, タイマーの実行回数
  int _ecoTimerRounds = 0;

  /// Shut offモードに入る, タイマーの実行回数
  int _shutOffTimerRounds = 0;

  /// Shut offモードに入る, タイマーの実行回数
  int _timerRoundsCount = 0;

  /// Screen Saver モード,Eco モード,Shut off モードに入るフラグ
  EcoModelState _currentEcoModelState = EcoModelState.stateNull;

  /// GlobalPopupが表示されているかどうか
  bool get _isGlobalPopupShowing =>
      GlobalPopupRoute().isPopupStackEmpty() == false;

  /// UTL縫製中かどうか
  bool get _isUtlSewing =>
      UtlLibrary().apiBinding.isUtlModeOpened() &&
      TpdLibrary()
          .apiBinding
          .bpIFGetAppDisplayUtl()
          .utlFuncSetting
          .ref
          .isUtlSewing;

  /// EMB縫製中かどうか
  bool get _isEmbSewing =>
      EmbLibrary().apiBinding.isEmbModeOpened() &&
      TpdLibrary().apiBinding.bPIFGetAppDisplayEmb().embSetting.ref.isEmbSewing;

  ///
  /// 初期化処理
  ///
  void panelEcoInit() {
    /// shut offモートに再起動IIVO
    if (TpdLibrary().apiBinding.isInShutOffModel()) {
      /// do nothing
    } else {
      if (TpdLibrary().apiBinding.getBaseMode() != BaseMode_t.BMODE_TEST) {
        /// 入力イベントの監視を開始する
        InputMonitorModel().init();

        /// ローカル変数の初期化
        /// [value] : 0(OFF) , 1~60(ON) 単位：min
        _screenSaverTimeMinute =
            DeviceLibrary().apiBinding.getScreenSaverTime().value;
        if (_screenSaverTimeMinute != 0) {
          _screenSaverTimerRounds =
              _screenSaverTimeMinute * 60 * 1000 ~/ _intervalTime;
        }

        /// [value] : 0(OFF) , 10 ~ 120(ON) 単位：min
        _ecoTimeMinute = DeviceLibrary().apiBinding.getEcoMode().value;
        if (_ecoTimeMinute != 0) {
          _ecoTimerRounds = _ecoTimeMinute * 60 * 1000 ~/ _intervalTime;
        }

        /// [value] : 0(OFF) , 1~12(ON) 単位：hour
        _shutOffTimeMinute =
            DeviceLibrary().apiBinding.getShutoffSupportMode().value;
        if (_shutOffTimeMinute != 0) {
          _shutOffTimerRounds =
              _shutOffTimeMinute * 60 * 60 * 1000 ~/ _intervalTime;
        }

        /// Timerを開始する
        if (_screenSaverTimeMinute != 0 ||
            _ecoTimeMinute != 0 ||
            _shutOffTimeMinute != 0) {
          _startTimer();
        }
      } else {
        Log.d(
          tag: _logTag,
          description:
              "panelEcoInit | current mode is TestMode, PanelEco Handle Timer Was Stopped",
        );

        /// DoNothing
      }

      /// Event Streamを開始する
      _openStream();
      _stream.listen((event) {
        _handleEvent(event);
      });
    }
  }

  ///
  /// WM用途を提供する
  /// 1.Libへ値を設定する
  /// 2.Screen Saverモードに入る, タイマーの実行回数更新
  ///
  void setScreenSaverTime(int minute) {
    Log.d(tag: _logTag, description: "set screen saver Time:$minute");

    /// lib更新
    DeviceLibrary().apiBinding.saveScreenSaverTime(minute);

    /// classにの値を更新
    _screenSaverTimeMinute = minute;

    if (minute != 0) {
      _screenSaverTimerRounds = minute * 60 * 1000 ~/ _intervalTime;

      /// タイマーを再起動します
      _restartTimer();
    } else {
      _screenSaverTimerRounds = 0;

      /// 全てOff場合、Timerを停止する
      if (_ecoTimeMinute == 0 && _shutOffTimeMinute == 0) {
        _stopTimer();
      }
    }
  }

  ///
  /// WM用途を提供する
  /// 1.Libへ値を設定する
  /// 2.Ecoモードに入る, タイマーの実行回数更新
  ///
  void setEcoTime(int minute) {
    Log.d(tag: _logTag, description: "set eco Time:$minute");

    /// libの値を更新
    DeviceLibrary().apiBinding.saveEcoMode(minute);

    /// classにの値を更新
    _ecoTimeMinute = minute;

    if (minute != 0) {
      _ecoTimerRounds = minute * 60 * 1000 ~/ _intervalTime;

      /// タイマーを再起動します
      _restartTimer();
    } else {
      _ecoTimerRounds = 0;
      if (_screenSaverTimeMinute == 0 && _shutOffTimeMinute == 0) {
        /// 全てOff場合、Timerを停止する
        _stopTimer();
      }
    }
  }

  ///
  /// WM用途を提供する
  /// 1.Libへ値を設定する
  /// 2.Shut Offモードに入る, タイマーの実行回数更新
  ///
  void setShutOffTime(int hour) {
    Log.d(tag: _logTag, description: "set shut off Time:$hour");

    /// libの値を更新
    DeviceLibrary().apiBinding.saveShutoffSupportMode(hour);

    /// classにの値を更新
    _shutOffTimeMinute = hour * 60;
    if (hour != 0) {
      _shutOffTimerRounds = _shutOffTimeMinute * 60 * 1000 ~/ _intervalTime;

      /// タイマーを再起動します
      _restartTimer();
    } else {
      _shutOffTimerRounds = 0;

      /// 全てOff場合、Timerを停止する
      if (_screenSaverTimeMinute == 0 && _ecoTimeMinute == 0) {
        _stopTimer();
      }
    }
  }

  ///
  /// テストモードからScreen Saver モード,Eco モード,Shut off モードに入る
  ///
  void updateCurrentEcoModelStateFromTest(EcoModelState state) {
    /// 状態更新

    if (state == EcoModelState.eco) {
      Log.d(
          tag: _logTag,
          description: "updateCurrentEcoModelStateFromTest start");
      _currentEcoModelState = EcoModelState.eco;
      TpdLibrary().apiBinding.startEco();
      _ecoTimerRounds = -1;

      /// accessibility serviceセカンダリ サービスを開始する
      InputMonitorModel().init().then((_) {
        /// 定時器を起動する
        _startTimer();
      });
    } else if (state == EcoModelState.shutOff) {
      _currentEcoModelState = EcoModelState.shutOff;
      _shutOffTimerRounds = -1;
      TpdLibrary().apiBinding.startSleep();
    }
  }

  /// ===============================================
  /// Streamのfunction
  /// Panel EcoのEntryとExit制御
  /// ===============================================
  ///
  StreamController<_PanelEcoTaskEvent> _controller =
      StreamController<_PanelEcoTaskEvent>.broadcast();

  Stream<_PanelEcoTaskEvent> get _stream => _controller.stream;

  void _fireEvent(_PanelEcoTaskEvent event) {
    _controller.add(event);
  }

  void _openStream() {
    _controller = StreamController<_PanelEcoTaskEvent>.broadcast();
  }

  void _handleEvent(_PanelEcoTaskEvent event) => _handelPanelTaskEvent(event);

  ///
  ///　イベント ID に基づいて、対応するExit/Entry関数を呼び出す
  ///
  ///
  void _handelPanelTaskEvent(_PanelEcoTaskEvent eventKind) {
    Log.d(tag: _logTag, description: "panel eco tsk event:$eventKind");
    switch (eventKind) {
      case _PanelEcoTaskEvent.screenSaverEntry:
        _screenSaverModelEntry();
        break;
      case _PanelEcoTaskEvent.screenSaverExit:
        _screenSaverModelExit();
        break;
      case _PanelEcoTaskEvent.ecoEntry:

        /// 最初にScreenSaverに入る、次にEcoを入る
        /// ScreenSaver Exit必要,
        if (_currentEcoModelState == EcoModelState.screenSaver) {
          _screenSaverModelExit();
        }
        _ecoModelEntry();
        break;
      case _PanelEcoTaskEvent.ecoExit:
        _ecoModelExit();

        break;
      case _PanelEcoTaskEvent.shutOffEntry:
        _shutOffModelEntry();
        break;
      case _PanelEcoTaskEvent.shutOffExit:

        /// 終了するには、電源ボタンを押す必要があります
        /// do nothing
        break;
    }
  }

  /// ===============================================
  /// timerのfunction
  /// ===============================================

  ///
  /// タイマーを開始する
  /// 使用場合：
  /// 1.Power On場合。
  /// 2. Screen Saver、Eco、Shut off モードの場合。
  ///
  void _startTimer() {
    Log.d(tag: _logTag, description: "start Timer");
    if (_timer != null && _timer!.isActive) {
      _timer!.cancel();
    }
    _resetMachineKeyOperationState();
    _timer =
        Timer.periodic(const Duration(milliseconds: _intervalTime), (timer) {
      _ecoModelTimerEventCtl();
    });
  }

  ///
  /// タイマーを終わります
  /// 使用場合：
  /// 1.Screen Saverモードに入る
  /// 2.Screen Saver値再設定場合（その他値ー＞Offへ変更）
  ///
  void _stopTimer() {
    /// タイマーが既に存在する場合は、前のタイマーをキャンセルします
    if (_timer != null && _timer!.isActive) {
      Log.d(tag: _logTag, description: "stop Timer");
      _timer!.cancel();
    }
  }

  void _restartTimer() {
    /// タイマーが既に存在する場合は、前のタイマーをキャンセルします
    if (_timer != null && _timer!.isActive) {
      _timer!.cancel();
    }

    Log.d(tag: _logTag, description: "restart Timer");

    /// 一定時間後に _openOtherApp メソッドを実行する新しいタイマーを作成します
    _timer =
        Timer.periodic(const Duration(milliseconds: _intervalTime), (timer) {
      _ecoModelTimerEventCtl();
    });

    _timerRoundsCount = 0;
  }

  ///
  /// 繰り返し回数が到来すると、entry eventを送信する
  /// 停止が検出されたときに exit event を送信する
  /// timerRoundsCountは最初に +1 を実行し、次に判断するため、Offの場合entry event送信できない
  ///
  void _ecoModelTimerEventCtl() {
    _timerRoundsCount = _timerRoundsCount + 1;

    /// Entry 判断
    if (_timerRoundsCount == _shutOffTimerRounds) {
      /// ShutOff modeに入る
      _fireEvent(_PanelEcoTaskEvent.shutOffEntry);
      return;
    } else if (_timerRoundsCount == _ecoTimerRounds) {
      /// Eco modeに入る
      _fireEvent(_PanelEcoTaskEvent.ecoEntry);
      return;
    } else if (_timerRoundsCount == _screenSaverTimerRounds) {
      /// Screen Saverに入る
      _fireEvent(_PanelEcoTaskEvent.screenSaverEntry);
      return;
    } else {
      /// do nothing
    }

    if (_isInterrupted()) {
      _timerRoundsCount = 0;

      switch (_currentEcoModelState) {
        case EcoModelState.shutOff:

          /// ShutOff modeにExit
          /// do nothing
          break;
        case EcoModelState.eco:

          /// Eco modeにExit
          _fireEvent(_PanelEcoTaskEvent.ecoExit);

          break;
        case EcoModelState.screenSaver:

          /// screenSaver modeにExit
          _fireEvent(_PanelEcoTaskEvent.screenSaverExit);
          break;
        case EcoModelState.stateNull:

          /// do nothing
          break;
      }
    } else {
      // Do nothing
    }

    /// データオーバーフローの防止
    if (_timerRoundsCount > _timerRoundsCountMax) {
      _timerRoundsCount = 0;
    }
  }

  ///
  /// ScreenSaver／ECOモードが中断されたかどうかを判断する：
  /// 現在ScreenSaver／ECOモード中であれば、現在のモードを終了し、
  /// モード中でなければタイマーをリセットします。
  ///
  /// 中断条件には以下が含まれます：
  /// 1. パネル画面が操作された（手指またはマウス）
  /// 2. USBの抜き差し
  /// 3. グローバルポップアップの表示
  /// 4. Lib側がエラー状態であること
  /// 5. 縫製中であること
  /// 6. 刺繍中であること
  /// 7. BWDが有効であること
  /// 8. メカニカルキーが操作されたこと
  ///
  bool _isInterrupted() {
    // パネル画面が操作された（手指またはマウス) / USBの抜き差し
    if (InputMonitorModel().getInputEventInterrupt()) {
      _debugLog("Interrupted: input event");
      return true;
    }
    // グローバルポップアップの表示
    if (_isGlobalPopupShowing) {
      _debugLog("Interrupted: global popup");
      return true;
    }
    // 縫製中であること
    if (_isUtlSewing) {
      _debugLog("Interrupted: utl sewing");
      return true;
    }
    // 刺繍中であること
    if (_isEmbSewing) {
      _debugLog("Interrupted: emb sewing");
      return true;
    }
    // Lib側がエラー状態であること
    if (TpdLibrary().apiBinding.bpIFGetError().errorCode !=
        ErrCode_t.ERR_DUMMY.index) {
      _debugLog("Interrupted: error code");
      return true;
    }

    // メカニカルキーが操作されたこと
    final bool isMachineKeyOperated = switch (_currentEcoModelState) {
      EcoModelState.eco => TpdLibrary().apiBinding.requestExitEcoModel(),
      EcoModelState.screenSaver =>
        TpdLibrary().apiBinding.requestExitScreenSaver(),

      // ScreenSaver／ECOモードに入る前に、
      // 以下のAPIを使用してメカニカルキーが操作されたかどうかを判断します。
      EcoModelState.stateNull =>
        TpdLibrary().apiBinding.hasMechaKeySousaBetweenLastAndCurrentCheck(),

      // ShutOffモード中は、メカニカルキーの操作は無視されます。
      EcoModelState.shutOff => false
    };

    if (isMachineKeyOperated) {
      _debugLog("Interrupted: machine key");
      return true;
    }

    // BWDが有効であること
    if (TpdLibrary().apiBinding.getBwdState().isOn) {
      _debugLog("Interrupted: bwd");
      return true;
    }

    return false;
  }

  /// ==============================================
  /// Scree SaverのFunction
  /// ==============================================

  ///
  /// タイマーが指定されたラウンド数まで実行されると、Screen Saverモードになります
  /// 1.Screen Saverアプリを起動する
  /// 2.Screen Saverステータスフラグを設定する
  /// 3.Libへ通知する
  ///
  Future<void> _screenSaverModelEntry() async {
    final isEntry = await _isEntryScreenSaver();

    /// Screen Saverモードになる
    if (isEntry == true) {
      List<String> pathList =
          await GeneralSettingPage2Model().readScreenSaverImagePath();

      /// カスタム画像を設定していない場合,非表示
      List<String> newPathList = [];
      for (var item in pathList) {
        File file = File(item);
        if (file.existsSync() == true) {
          newPathList.add(item);
        }
      }

      /// ScreenSaverアプリ起動
      if (newPathList.isNotEmpty) {
        Log.d(tag: _logTag, description: "screen saver app start");

        /// main側を通知します
        TpdLibrary().apiBinding.startScreenSaver();

        await InputMonitorModel().setBlockAllInputEvent(true);
        _stopTimer();

        await _launchAndWaitForScreenSaver(pathList);

        /// 状態更新
        _currentEcoModelState = EcoModelState.screenSaver;

        _startTimer();
        await InputMonitorModel().setBlockAllInputEvent(false);
      } else {
        Log.d(
            tag: _logTag,
            description: "screen saver image is none, app not start");
      }
    }
  }

  ///
  ///　Screen Saverモードを終了します
  /// 1.Screen Saverアプリを終了する
  /// 2.Libへ通知する
  ///
  void _screenSaverModelExit() {
    if (_currentEcoModelState == EcoModelState.screenSaver) {
      Log.d(tag: _logTag, description: "screen saver app finish");

      /// main側を通知します
      TpdLibrary().apiBinding.stopScreenSaver();

      /// Screen Saverを停止する
      _finishScreenSaver();

      /// 状態更新
      _currentEcoModelState = EcoModelState.stateNull;
    }
  }

  ///
  /// Screen Saver起動前、判断する
  /// False:起動しない
  /// True:起動する
  ///
  Future<bool> _isEntryScreenSaver() async {
    /// apk installed check
    if (await _isScreenSaverInstalled() == false) {
      Log.e(tag: _logTag, description: "screen saver app not installed");
      return false;
    }

    /// 縫製中の場合、Screen Saverモードを起動できません
    if (_isUtlSewing || _isEmbSewing) {
      return false;
    }

    /// ユーザー情報初期化実行に各機能を無効にする
    if (MachineConfigModel().isResetUserInfoExec == true) {
      return false;
    }

    /// DemoMode判断、Testモード判断、Openningモード判断：XPがある。
    /// IIVOが初期化時タイマーが開始しません。判断不要

    /// TODO: ReStart禁止かチェック、Lib Apiがなし
    /// https://brothergroup.atlassian.net/browse/PHBSHIIVO-32

    // XV V100 M-733 Nagai 2014/08/09 WidthControl ONの時はスクリーンセーバー起動しないように修正
    // いろいろ不具合起こるのでXからこの仕様
    if (MachineConfigModel().currentMode == SettingBaseMode.utl) {
      final widthControl = DeviceLibrary().apiBinding.getWidthControl().value;
      if (widthControl) {
        return false;
      }
    }

    /// 下糸巻きがONの時は行わない
    final isBwdOn = TpdLibrary().apiBinding.getBwdState().isOn;
    if (isBwdOn) {
      return false;
    }

    /// マイカスタムデザイン
    if (MachineConfigModel().currentMode == SettingBaseMode.utl &&
        PatternDataModel().getPatternMode() == PatternMode.stitchRegulator) {
      return false;
    }

    /// ロック中でない かつ、
    if (OutKeyLockModel().isAllLock) {
      return false;
    }

    /// エラーなし かつ、
    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return false;
    }

    /// 起動時間がOFF以外かつ
    /// 値が等しい場合は、最初に Eco/ShutOff モードに入ります
    if ((_screenSaverTimeMinute == 0) ||
        (_screenSaverTimeMinute == _ecoTimeMinute) ||
        (_screenSaverTimeMinute == _shutOffTimeMinute)) {
      return false;
    }

    /// 移行可能状態
    if (_currentEcoModelState != EcoModelState.stateNull) {
      return false;
    }

    /// 動作中
    if (TpdLibrary().apiBinding.bpIFGetAppDisplayGlobal().isStopingMotor ==
            false ||
        TpdLibrary().apiBinding.bpIFGetAppDisplayGlobal().isStopingFrame ==
            false) {
      return false;
    }

    /// GlobalPopupが表示されていない場合、Screen Saverを起動する
    return _isGlobalPopupShowing == false;
  }

  /// ==============================================
  /// EcoのFunction
  /// ==============================================

  ///
  /// タイマーが指定されたラウンド数まで実行されると、Ecoモードになります
  /// 1.Eco Modelステータスフラグを設定する
  /// 2.Libへ通知する
  ///
  void _ecoModelEntry() {
    if (_isEntryEcoChk() == true) {
      Log.d(tag: _logTag, description: "Eco entry");

      /// main側を通知します
      TpdLibrary().apiBinding.startEco();

      /// 状態更新
      _currentEcoModelState = EcoModelState.eco;

      /// ECOを終了すると、MDCなどに入るの問題解決ため、ScreenSaverを起動します
      SystemConfig.openSystemPage(
          const SystemPageEnum.customPage(_customScreenPackage),
          message: "");
    }
  }

  ///
  ///　Ecoモードを終了します
  /// 1.Eco Modelステータスフラグを設定する
  /// 2.Libへ通知する
  ///
  void _ecoModelExit() {
    if (_currentEcoModelState == EcoModelState.eco) {
      Log.d(tag: _logTag, description: "Eco exit");

      /// Screen Saverを停止する
      _finishScreenSaver();

      // /// main側を通知します
      TpdLibrary().apiBinding.stopEco();

      /// モードに入るクリック操作が終了するクリック操作として捕捉されないようにするために、AccessibilityServiceを停止します。
      if (TpdLibrary().apiBinding.getBaseMode() == BaseMode_t.BMODE_TEST) {
        _stopTimer();
        InputMonitorModel().stopInputMonitor();
      }

      /// 状態更新
      _currentEcoModelState = EcoModelState.stateNull;
    } else {
      /// stateNull場合、do nothing
      /// shutOff場合、再起動する必要があります。do nothing
    }
  }

  bool _isEntryEcoChk() {
    /// 縫製中の場合、Ecoモードを起動できません
    if (_isUtlSewing || _isEmbSewing) {
      return false;
    }

    /// DemoMode判断、Testモード判断、Openningモード判断：XPがある。
    /// IIVOが初期化時タイマーが開始しません。判断不要

    /// TODO: ReStart禁止かチェック

    /// マイカスタムデザイン
    if (MachineConfigModel().currentMode == SettingBaseMode.utl &&
        PatternDataModel().getPatternMode() == PatternMode.stitchRegulator) {
      return false;
    }

    /// ユーザー情報初期化実行に各機能を無効にする
    if (MachineConfigModel().isResetUserInfoExec == true) {
      return false;
    }

    /// エラーなし かつ
    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      return false;
    }

    /// 起動時間がOFF以外かつ
    if (_ecoTimeMinute == 0) {
      return false;
    }

    /// ECOモードにタスクが移行できるか状態を判別する
    if (_currentEcoModelState == EcoModelState.eco ||
        _currentEcoModelState == EcoModelState.shutOff) {
      return false;
    }

    /// 動作中
    if (TpdLibrary().apiBinding.bpIFGetAppDisplayGlobal().isStopingMotor ==
            false ||
        TpdLibrary().apiBinding.bpIFGetAppDisplayGlobal().isStopingFrame ==
            false) {
      return false;
    }

    /// GlobalPopupが表示されていない場合、Ecoを起動する
    return _isGlobalPopupShowing == false;
  }

  /// ==============================================
  /// Shut OffのFunction
  /// ==============================================

  void _shutOffModelEntry() {
    if (_isEntryShutOffChk()) {
      Log.d(tag: _logTag, description: "go to shut off");

      /// 状態更新
      _currentEcoModelState = EcoModelState.shutOff;

      /// main側を通知します
      TpdLibrary().apiBinding.startSleep();

      _stopTimer();
    }
  }

  bool _isEntryShutOffChk() {
    /// 縫製中の場合、Shut Offモードを起動できません
    if (_isUtlSewing || _isEmbSewing) {
      return false;
    }

    /// TODO: ReStart禁止かチェック
    /// https://brothergroup.atlassian.net/browse/PHBSHIIVO-32

    // 起動時間がOFF以外かつ
    if (_shutOffTimeMinute == 0) {
      return false;
    }

    /// ユーザー情報初期化実行前に各機能を無効にする
    if (MachineConfigModel().isResetUserInfoExec == true) {
      return false;
    }

    /// Shut Offモードにタスクが移行できるか状態を判別する
    if (_currentEcoModelState == EcoModelState.shutOff) {
      return false;
    }

    /// GlobalPopupが表示されていない場合、Shut Offを起動する
    return _isGlobalPopupShowing == false;
  }

  ///
  /// 古いキー押下状態をリセットする
  ///
  /// @see [TpdLibrary.apiBinding.hasMechaKeySousaBetweenLastAndCurrentCheck]
  ///
  void _resetMachineKeyOperationState() {
    TpdLibrary().apiBinding.hasMechaKeySousaBetweenLastAndCurrentCheck();
    if (TpdLibrary().apiBinding.hasMechaKeySousaBetweenLastAndCurrentCheck()) {
      Log.errorTrace("Failed to reset machine key operation state");
    } else {
      Log.i(tag: _logTag, description: "Machine key operation state is reset");
    }
  }

  void _debugLog(String msg) => Log.d(tag: _logTag, description: msg);
}

enum EcoModelState {
  /// NULL
  stateNull,

  /// Screen Saverモード
  screenSaver,

  /// ECOモード
  eco,

  /// ShutOffモード
  shutOff,
}

/// Eco Tsk制御のEvent
enum _PanelEcoTaskEvent {
  screenSaverEntry,
  screenSaverExit,
  ecoEntry,
  ecoExit,
  shutOffEntry,
  shutOffExit,
}

/// スクリーンセーバー制御のミックスイン
mixin _ScreenSaverController {
  /// スクリーンセーバーの起動完了を待つCompleter
  Completer? _prepareCompleter;

  /// スクリーンセーバー制御のチャンネル
  MethodChannel? _channel;

  ///
  /// スクリーンセーバーを起動し、完了を待つ
  ///
  Future<void> _launchAndWaitForScreenSaver(List<String> pathList) async {
    if (_prepareCompleter != null) {
      Log.assertTrace("Screen saver completer is still in use!");
      return;
    }

    if (_channel == null) {
      _channel = const MethodChannel("screen_saver");
      _channel!.setMethodCallHandler((call) async {
        if (call.method == "onScreenSaverPrepared") {
          if (_prepareCompleter?.isCompleted == true) {
            Log.assertTrace("Screen saver completer is already completed!");
            return;
          }
          _prepareCompleter?.complete();
        } else {
          Log.assertTrace("Unknown method call: ${call.method}");
        }
      });
    } else {
      // Do nothing
    }

    _prepareCompleter = Completer();

    SystemConfig.openSystemPage(
      const SystemPageEnum.customPage(PanelEcoModel._customScreenPackage),
      message: pathList.join(','),
    );

    await _prepareCompleter?.future;
    _prepareCompleter = null;
  }

  ///
  /// スクリーンセーバーを終了する
  ///
  Future<void> _finishScreenSaver() {
    return SystemConfig.finishOtherApp(
        const SystemPageEnum.customPage(PanelEcoModel._customScreenPackage));
  }

  ///
  /// スクリーンセーバーがインストールされているかどうかを返します
  ///
  Future<bool> _isScreenSaverInstalled() {
    return SystemConfig.isInstalledApp(
        const SystemPageEnum.customPage(PanelEcoModel._customScreenPackage));
  }
}
