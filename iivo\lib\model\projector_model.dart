import 'dart:async';
import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:log/log.dart';
import 'package:memory/memory_interface.dart';
import 'package:panel_library/panel_library.dart';
import 'package:path/path.dart';
import 'package:ph_eel_plugin/projector_bindings_generated.dart';
import 'package:presentation_displays/presentation_displays.dart';
import 'package:synchronized/synchronized.dart';

import '../ui/page_route/page_route.dart';
import 'projector/auto_stop/auto_stop_second_display_control.dart';
import 'projector/camera_pen/camera_pen.dart';
import 'projector/camera_pen/camera_pen_type.dart';
import 'projector/camera_pen/extension_function/extensin_frame_move.dart';
import 'projector/camera_pen/view_info/emb_camera_pen_view_info.dart';
import 'projector/camera_pen/view_info/mdc_camera_pen_view_info.dart';
import 'projector/camera_pen/view_info/utl_camera_pen_view_info.dart';
import 'projector/projector_image_util.dart';
import 'projector/projector_refresh_task_queue.dart';
import 'projector/sr/sr_status_color.dart';

part 'projector/emb_projector.dart';
part 'projector/mdc_projector.dart';
part 'projector/projector_function.dart';
part 'projector/projector_parameters.dart';
part 'projector/utl_projector.dart';
part 'projector/needle_position_calibration_projector.dart';

/// ドラッグが停止した際のデバウンス用のピクセル閾値。
/// ユーザーがドラッグを終了した後に発生する意図しない動きを防ぐために使用されます。
const int dragDebounceThresholdProjector = 2;

class ProjectorModel extends ProjectorCommonParameters with UtlProjector {
  ProjectorModel._internal();

  factory ProjectorModel() => _instance;
  static final ProjectorModel _instance = ProjectorModel._internal();

  final EmbCameraPen cameraPen = EmbCameraPen();
  final CameraProjectorFrameMove cameraProjectorFrameMove =
      CameraProjectorFrameMove();
  final EmbCameraPenViewInfo embCameraPenViewInfo = EmbCameraPenViewInfo();
  final MdcCameraPenViewInfo mdcCameraPenViewInfo = MdcCameraPenViewInfo();
  final UtlCameraPenViewInfo utlCameraPenViewInfo = UtlCameraPenViewInfo();
  final EmbProjector embProjector = EmbProjector();
  final MdcProjector mdcProjector = MdcProjector();
  final NeedlePositionCalibrationProjector needlePositionCalibrationProjector =
      NeedlePositionCalibrationProjector();

  ///
  /// プロジェクター刺繍模様投影を開始する
  /// プロジェクト起動と閉じる前に 適度なdelayが必要です。
  ///
  /// 主には please waitのpopupです。
  ///
  /// もし初期化速いなら、違和感があります
  ///
  static const int projectorStartStopDelayMS = 500;

  ///
  /// プロジェクションが更新される遅延時間
  ///
  static const int projectorRefreshDelayMS = 100;

  ///
  /// プロジェクト設備を初期化する
  ///
  Future<bool> initProjectorDevice() async {
    CameraProjectLibrary().apiBinding.projectorOff();
    CameraProjectLibrary().apiBinding.projectorInit();
    ProjectorLibrary().apiBinding.createProjector();

    /// hotRestart、異常閉じる再開時、先に一回resetします
    await DisplayManager.getInstance().reset();

    /// プロジェクト画面(second display)を開けて
    return PagesRoute().initProjectorScreen();
  }

  ///
  /// プロジェクト設備を再起動する
  ///
  Future<void> restartProjectorDevice() async {
    CameraProjectLibrary().apiBinding.projectorOff();
    CameraProjectLibrary().apiBinding.projectorFinish();
    CameraProjectLibrary().apiBinding.projectorInit();
    ProjectorLibrary().apiBinding.destroyProjector();
    ProjectorLibrary().apiBinding.createProjector();

    /// hotRestart、異常閉じる再開時、先に一回resetします
    await DisplayManager.getInstance().reset();

    /// プロジェクト画面(second display)を開けて
    PagesRoute().initProjectorScreen();
  }
}
