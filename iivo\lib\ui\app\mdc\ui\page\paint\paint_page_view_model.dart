import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../model/paint/paint_model.dart';
import '../../../model/resume_history_model.dart';
import 'bottom/bottom_view_model.dart';
import 'bottom/select/select.dart';
import 'canvas/mdc_canvas_view_mode.dart';
import 'paint_page_view_interface.dart';
import 'toolbar/eraser/eraser.dart';
import 'toolbar/line_property/line_property_popup.dart';
import 'toolbar/rotate/rotate.dart';
import 'toolbar/rotate/rotate_view_model.dart';
import 'toolbar/size/component/scaling_tab/scaling_tab_view_model.dart';
import 'toolbar/size/component/tenKey_tab/ten_key_tab_view_model.dart';
import 'toolbar/size/size_adjustment.dart';
import 'toolbar/stamp/stamp.dart';
import 'toolbar/surface_property/surface_property_popup.dart';
import 'toolbar/toolbar_view_model.dart';
import 'top_bar/component/image_adjustment/image_adjustment_view_model.dart';
import 'top_bar/component/magnification_popup/magnification_popup.dart';
import 'top_bar/component/preview_enlarged/preview_enlarged_view_model.dart';
import 'top_bar/dropper_tool_page/dropper_tool_page_view_model.dart';
import 'top_bar/illustration_design_page/illustration_design_page_view_model.dart';
import 'top_bar/illustration_design_retry_page/illustration_design_retry_page_view_model.dart';
import 'top_bar/memory_import/memory_import.dart';
import 'top_bar/top_bar_view_model.dart';

enum ModuleType { topBar, toolbar, bottom, canvas, stitch, scanImage }

enum ComponentType { previewMoveButton, droopTool, illusAdjust }

enum PopupEnum {
  topBarMagnification,
  topBarMemoryImport,
  lineProperty,
  surfaceProperty,
  toolBarResize,
  toolBarRotate,
  toolBarErase,
  toolBarStamp,
  bottomAreaSelect,
}

void Function()? updateMagnificationByChild;

final paintPageViewInfoProvider =
    AutoDisposeNotifierProvider<_PaintPageViewInfo, void>(
        () => _PaintPageViewInfo());

class _PaintPageViewInfo extends AutoDisposeNotifier<void> {
  @override
  void build() {}

  BuildContext? _context;
  BuildContext get context => _context!;
  set context(value) => _context = value;
}

final paintPageProvider =
    StateNotifierProvider.autoDispose<PaintPageViewModel, PaintPageState>(
        (ref) {
  final context = ref.read(paintPageViewInfoProvider.notifier).context;
  return PaintPageViewModel(ref, context);
});

class PaintPageViewModel extends PaintPageViewInterface {
  PaintPageViewModel(Ref ref, BuildContext context)
      : super(const PaintPageState(), ref, context);

  @override
  void build() {
    super.build();
    ResumeHistoryModel().currentPageType = ResumePageType.paint;
    updateMagnificationByChild = _updateMagnificationByChild;
  }

  ///
  /// MagnificationのView更新
  ///
  void _updateMagnificationByChild() {
    state = state.copyWith(
      isMagnificationShow: PaintModel().isMagnificationPopupOpened(),
    );
  }

  ///
  /// 子画面のView更新
  ///
  @override
  void updatePaintPageByChild(ModuleType vm) {
    switch (vm) {
      case ModuleType.topBar:
        ref.read(topBarViewModelProvider.notifier).update();
        ref.read(toolBarViewModelProvider.notifier).update();
        ref.read(bottomProvider.notifier).update();
        ref.read(mdcCanvasViewModelProvider.notifier).update();
        break;
      case ModuleType.toolbar:
        ref.read(topBarViewModelProvider.notifier).update();
        ref.read(toolBarViewModelProvider.notifier).update();
        ref.read(bottomProvider.notifier).update();
        ref.read(mdcCanvasViewModelProvider.notifier).update();
        break;

      case ModuleType.bottom:
        ref.read(mdcCanvasViewModelProvider.notifier).update();
        ref.read(toolBarViewModelProvider.notifier).update();
        ref.read(topBarViewModelProvider.notifier).update();

        if (ref.exists(tenKeyViewModelProvider)) {
          ref.read(tenKeyViewModelProvider.notifier).update();
        }
        if (ref.exists(scalingTabViewModelProvider)) {
          ref.read(scalingTabViewModelProvider.notifier).update();
        }
        if (ref.exists(mdcRotateViewModelProvider)) {
          ref.read(mdcRotateViewModelProvider.notifier).update();
        }
        break;
      case ModuleType.canvas:
        ref.read(toolBarViewModelProvider.notifier).update();
        if (ref.exists(scalingTabViewModelProvider)) {
          ref.read(scalingTabViewModelProvider.notifier).update();
        }
        if (ref.exists(tenKeyViewModelProvider)) {
          ref.read(tenKeyViewModelProvider.notifier).update();
        }
        if (ref.exists(mdcRotateViewModelProvider)) {
          ref.read(mdcRotateViewModelProvider.notifier).update();
        }
        ref.read(bottomProvider.notifier).update();
        ref.read(topBarViewModelProvider.notifier).update();
        break;
      case ModuleType.stitch:
        ref.read(mdcCanvasViewModelProvider.notifier).update();
        ref.read(toolBarViewModelProvider.notifier).update();
        ref.read(bottomProvider.notifier).update();
        ref.read(topBarViewModelProvider.notifier).update();
        break;
      default:
        break;
    }
  }

  ///
  /// ComponentのView更新
  ///
  @override
  void updatePaintComponentByChild(ComponentType type) {
    switch (type) {
      case ComponentType.previewMoveButton:
        ref.read(previewEnlargedViewModelProvider.notifier).update();
        ref.read(dropperToolPageViewModelProvider.notifier).updateLineColor();
        break;
      case ComponentType.droopTool:
        ref.read(imageAdjustmentViewModelProvider(null).notifier).update();
        ref
            .read(illustrationDesignRetryPageViewModelProvider.notifier)
            .update();
        break;
      case ComponentType.illusAdjust:
        ref.read(imageAdjustmentViewModelProvider(null).notifier).update();
        ref.read(illustrationDesignPageViewModelProvider.notifier).update();
      default:
        break;
    }
  }

  ///
  /// 名前付きルートの登録
  ///
  @override
  Map<String, PopupRouteBuilder> registerNamedPopup() => {
        PopupEnum.lineProperty.toString(): PopupRouteBuilder(
          builder: (context) => const LinePropertyPopup(),
          barrier: false,
        ),
        PopupEnum.surfaceProperty.toString(): PopupRouteBuilder(
          builder: (context) => const SurfacePropertyPopup(),
          barrier: false,
        ),
        PopupEnum.topBarMemoryImport.toString(): PopupRouteBuilder(
          builder: (context) => const MemoryImport(),
          barrier: false,
        ),
        PopupEnum.toolBarResize.toString(): PopupRouteBuilder(
          builder: (context) => const SizeAdjustment(),
          barrier: false,
        ),
        PopupEnum.toolBarRotate.toString(): PopupRouteBuilder(
          builder: (context) => const Rotate(),
          barrier: false,
        ),
        PopupEnum.toolBarErase.toString(): PopupRouteBuilder(
          builder: (context) => const Eraser(),
          barrier: false,
        ),
        PopupEnum.bottomAreaSelect.toString(): PopupRouteBuilder(
          builder: (context) => const Select(),
          barrier: false,
        ),
        PopupEnum.topBarMagnification.toString(): PopupRouteBuilder(
          builder: (context) => const MagnificationPopup(),
          barrier: false,
        ),
        PopupEnum.toolBarStamp.toString(): PopupRouteBuilder(
          builder: (context) => const Stamp(),
          barrier: false,
        ),
      };

  @override
  Future<bool> lockEditProcWhenProcessing(Function() clickCallback) {
    return ref
        .read(mdcCanvasViewModelProvider.notifier)
        .lockEditProcWhenProcessing(clickCallback);
  }
}
