import 'dart:ffi';

import 'package:audio_player/audio_player_interface.dart';
import 'package:common_component/common_component.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:log/log.dart';
import 'package:panel_library/panel_library.dart';
import 'package:xd_component/l10n/app_localizations.dart';

import '../../../../../../../../model/app_locale.dart';
import '../../../../../../../../model/projector/camera_pen/camera_pen_type.dart';
import '../../../../../../../../model/projector_model.dart';
import '../../../../../../../../model/provider/app_display_state_provider.dart';
import '../../../../../../../global_popup/global_popup_export.dart';
import '../../../../../model/pattern_data_reader/character/pattern_data_reader.dart';
import '../../../../../model/pattern_model.dart';
import '../../../../../model/pattern_status_model.dart';
import '../../../../../model/preview_model.dart';
import '../alphabet_selector/alphabet_selector_view_model.dart';
import '../character_view_interface.dart';
import '../character_view_model.dart';
import 'pattern_selector_view_interface.dart';

final characterPatternSelectorViewModeProvider =
    StateNotifierProvider.autoDispose<CharacterPatternSelectorViewMode,
        CharacterPatternSelectorState>(
  (ref) => CharacterPatternSelectorViewMode(ref),
);

class CharacterPatternSelectorViewMode
    extends CharacterPatternSelectorViewModelInterface {
  ///
  /// 構造関数、初期化処理
  ///
  CharacterPatternSelectorViewMode(AutoDisposeStateNotifierProviderRef ref)
      : super(const CharacterPatternSelectorState(), ref) {
    /// 最初に画面に入った時です、カテゴリー6を選択します
    PatternDataModel().currentSelectedCategoryIndex = 0;
    ref.listen(
      appDisplayUtlStateProvider.select((value) => (
            value.utlAttribParam.ref.needleNum,
            value.utlFuncSetting.ref.isConnectedDF
          )),
      (previous, nextState) {
        state = state = state.copyWith(
          patternsStyle: _updateItemStyle(
            PatternDataModel().currentSelectedCategoryIndex,
            PatternDataModel().currentSelectedPatternIndex,
          ),
        );
      },
    );

    /// ViewModel更新
    update();
  }

  @override
  void update() {
    /// View更新
    state = state.copyWith(
      currentSelectedCategoryIndex:
          PatternDataModel().currentSelectedCategoryIndex,
      patternsStyle: _updateItemStyle(
        PatternDataModel().currentSelectedCategoryIndex,
        PatternDataModel().currentSelectedPatternIndex,
      ),
      stepPatternState: _getStepPatternState(),
    );

    /// 以下理解でここは　Futureを使って、他のViewModelを後に更新する
    /// Providers are not allowed to modify other providers during their initialization.
    Future(() {
      ref
          .read(characterViewModelProvider.notifier)
          .updateCharacterPageByChild(CharacterModuleType.patternSelector);
    });
  }

  @override
  List<PatternGroup> getAllCharacterPatternIconsInfo() =>
      PatternIconReader().getAllPatternIconsInfo();

  @override
  bool get isSelectAlphabetCategory => [
        groupBlock,
        groupItalic,
        groupOutline,
        groupCyril
      ].contains(getAllCharacterPatternIconsInfo()[
              PatternDataModel().currentSelectedCategoryIndex]
          .groupNum);

  @override
  bool get isSelectMemoryCategory =>
      getAllCharacterPatternIconsInfo()[
              PatternDataModel().currentSelectedCategoryIndex]
          .groupName ==
      "Pocket_FONT";

  @override
  void onSelectCategory(int index) {
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    wishPatternIndex = null;
    wishCategoryIndex = index;

    /// ポップアップ画面を閉じます
    closeAllPopup();

    /// カテゴリーと模様を選択します
    if (index == PatternDataModel().currentSelectedCategoryIndex) {
      return;
    } else {
      PatternDataModel().currentSelectedCategoryIndex = index;
    }

    if (index == PatternDataModel().currentLoginCategoryIndex) {
      PatternDataModel().currentSelectedPatternIndex =
          PatternDataModel().currentLoginPatternIndex;
    } else {
      PatternDataModel().currentSelectedPatternIndex = null;
    }

    if (getAllCharacterPatternIconsInfo()[
                PatternDataModel().currentSelectedCategoryIndex]
            .groupName !=
        "Pocket_FONT") {
      /// Model更新
      PatternDataModel().isFileOperationOn = false;
    }

    /// state更新
    state = state.copyWith(
      currentSelectedCategoryIndex:
          PatternDataModel().currentSelectedCategoryIndex,
      patternsStyle: _updateItemStyle(
        PatternDataModel().currentSelectedCategoryIndex,
        PatternDataModel().currentSelectedPatternIndex,
      ),
    );

    /// 文字列から入力を始めるときは組み合わせモード
    if ([groupBlock, groupItalic, groupOutline, groupCyril].contains(
        getAllCharacterPatternIconsInfo()[
                PatternDataModel().currentSelectedCategoryIndex]
            .groupNum)) {
      PatternDataModel().setAddMode(true);

      ref
          .read(characterViewModelProvider.notifier)
          .updateCharacterPageByChild(CharacterModuleType.patternSelector);
      ref.read(alphabetSelectorViewModeProvider.notifier).init();
    }
  }

  @override
  void onSelectPattern(int patternIndex) {
    final DirErrorCode dirError = TpdLibrary()
        .apiBinding
        .setMatrixEnableList(MachineKeyState.machineKeyEnableAllNG);
    if (dirError == DirErrorCode.dirMotorError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    } else if (dirError != DirErrorCode.dirNoError) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      Log.errorTrace("Undefined Error Handle");
      return;
    }

    wishPatternIndex = patternIndex;

    PatternDataModel().isNotPatternFirstTime = true;

    /// 文字列から入力を始めるときは組み合わせモード
    if ([groupBlock, groupItalic, groupOutline, groupCyril].contains(
        getAllCharacterPatternIconsInfo()[
                PatternDataModel().currentSelectedCategoryIndex]
            .groupNum)) {
      PatternDataModel().loginAlphabetPattern(
          PatternDataModel().currentSelectedCategoryIndex, wishPatternIndex!);
    } else {
      PatternDataModel().loginCharacterPattern(
          PatternDataModel().currentSelectedCategoryIndex, wishPatternIndex!);
    }

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      if (bPIFErrorPointer.errorCode == ErrCode_t.ERR_EPS_FINISH.index) {
        errEpsFinishFunc = _onCancelEndPointConfirmPopupOKButtonClick;
      }
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    PatternDataModel().currentSelectedMemoryPatternIndex = noTargetFound;

    /// Model更新
    PatternDataModel().currentSelectedPatternIndex = wishPatternIndex;

    /// View更新
    ref
        .read(characterViewModelProvider.notifier)
        .updateCharacterPageByChild(CharacterModuleType.patternSelector);

    state = state.copyWith(
      patternsStyle: _updateItemStyle(
        PatternDataModel().currentSelectedCategoryIndex,
        PatternDataModel().currentSelectedPatternIndex,
      ),
      stepPatternState: _getStepPatternState(),
    );

    TpdLibrary()
        .apiBinding
        .setMatrixEnableList(MachineKeyState.machineKeyEnableAll);
  }

  ///
  /// 模様表示状態取得する
  ///
  /// ## @param
  /// - currentSelectedCategoryIndex : 画面選択した模様のカテゴリーインデックス
  /// - currentSelectedPatternIndex : 画面選択した模様のパターンインデックス
  ///
  List<ButtonState> _updateItemStyle(
    int currentSelectedCategoryIndex,
    int? currentSelectedPatternIndex,
  ) {
    final patternGroup =
        getAllCharacterPatternIconsInfo()[currentSelectedCategoryIndex]
            .iconsGroup;

    List<ButtonState> patternsItemStyle =
        List.filled(patternGroup.length, ButtonState.normal);
    int groupNum =
        getAllCharacterPatternIconsInfo()[currentSelectedCategoryIndex]
            .groupNum;
    List<int> disableDataList = [];

    if (([groupBlock, groupItalic, groupOutline, groupCyril]
                .contains(groupNum)) ==
            false &&
        getAllCharacterPatternIconsInfo()[currentSelectedCategoryIndex]
                .groupName !=
            "Pocket_FONT") {
      disableDataList = UtlLibrary()
          .apiBinding
          .getDisableUtlDataList(
              getAllCharacterPatternIconsInfo()[currentSelectedCategoryIndex]
                  .groupNum)
          .disableDataList;
    }

    for (int i = 0; i < patternGroup.length; i++) {
      if (disableDataList.contains(patternGroup[i].patternNum)) {
        patternsItemStyle[i] = ButtonState.disable;
      } else {
        patternsItemStyle[i] = ButtonState.normal;
      }
    }
    return patternsItemStyle;
  }

  @override
  void onStepPatternClick(int patternIndex) {
    wishCategoryIndex = stepCategoryIndex;
    wishPatternIndex = patternIndex;

    if (state.stepPatternState == ButtonState.disable) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }

    /// 切り替えモードの時は無効
    if (PatternStatusModel().getEditMenuStatus().addMode == false) {
      SystemSoundPlayer().play(SystemSoundEnum.invalid);
      return;
    }
    SystemSoundPlayer().play(SystemSoundEnum.accept);

    /// ステップ模様登録関数
    UtlImageInfo utlImageInfo = UtlLibrary()
        .apiBinding
        .setStepStitch(patternIndex == 0 ? IsShiftX.toLeft : IsShiftX.toRight)
        .imageInfo;

    final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
    if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
      if (bPIFErrorPointer.errorCode == ErrCode_t.ERR_EPS_FINISH.index) {
        errEpsFinishFunc = _onCancelEndPointConfirmPopupOKButtonClick;
      }
      return;
    }

    /// Model更新
    PatternDataModel().loginStepPattern(wishPatternIndex!);
    PreviewDataModel().updatePreviewImage(utlImageInfo);

    /// View更新
    ref
        .read(characterViewModelProvider.notifier)
        .updateCharacterPageByChild(CharacterModuleType.patternSelector);
  }

  @override
  void closeAllPopup() {
    /// ポップアップ画面を閉じます
    ref
        .readAutoNotifierIfExists(characterViewInfoProvider)
        ?.navigator
        .maybeRemoveRoute(routeName: PopupEnum.editMenu.toString());

    /// Model更新
    PatternDataModel().isEditMenuOn = false;

    /// view更新
    Future(
      () => ref
          .read(characterViewModelProvider.notifier)
          .updateCharacterPageByChild(CharacterModuleType.closeAllPopup),
    );
  }

  @override
  void dispose() {
    super.dispose();
    controller.dispose();
  }

  //////////////////////////////////////////////////////////////////
  ///
  /// 内部で使われる関数群
  ///
  //////////////////////////////////////////////////////////////////

  ///
  /// ステップ模様の状態取得
  ///
  ButtonState _getStepPatternState() {
    if (PatternStatusModel().getCommonStatus().isEmpty) {
      return ButtonState.disable;
    }

    return ButtonState.normal;
  }

  ///
  /// ポップアップのOKボタンのクリック関数
  /// ぬい終わり位置設定を解除しますが、よろしいですか？
  ///
  void _onCancelEndPointConfirmPopupOKButtonClick() {
    PatternDataModel().setEndPointOnOff(false);
    ProjectorModel().closeUtlProjector(UtlProjectorType.endpoint).then((value) {
      if (wishCategoryIndex == stepCategoryIndex) {
        /// ステップ模様登録関数
        UtlImageInfo utlImageInfo = UtlLibrary()
            .apiBinding
            .setStepStitch(
                wishPatternIndex == 0 ? IsShiftX.toLeft : IsShiftX.toRight)
            .imageInfo;
        final bPIFErrorPointer = TpdLibrary().apiBinding.bpIFGetError();
        if (bPIFErrorPointer.errorCode != ErrCode_t.ERR_DUMMY.index) {
          if (bPIFErrorPointer.errorCode == ErrCode_t.ERR_EPS_FINISH.index) {
            errEpsFinishFunc = _onCancelEndPointConfirmPopupOKButtonClick;
          }
          return;
        }
        PatternDataModel().loginStepPattern(wishPatternIndex!);
        PreviewDataModel().updatePreviewImage(utlImageInfo);
      } else {
        /// Do nothing
      }

      /// View更新
      ref
          .read(characterViewModelProvider.notifier)
          .updateCharacterPageByChild(CharacterModuleType.patternSelector);
      state = state.copyWith(
        patternsStyle: _updateItemStyle(
          PatternDataModel().currentSelectedCategoryIndex,
          PatternDataModel().currentSelectedPatternIndex,
        ),
      );
    });
  }

  @override
  void moveToLoginSelectPattern() {
    if (PatternDataModel().currentLoginCategoryIndex == null ||
        PatternDataModel().currentLoginPatternIndex == null) {
      Log.e(tag: "moveToLoginSelectPattern", description: " invalid value[1]");
      return;
    }

    final int currentLoginCategoryIndex =
        PatternDataModel().currentLoginCategoryIndex!;
    final int currentLoginPatternIndex =
        PatternDataModel().currentLoginPatternIndex!;

    /// 情報更新
    PatternDataModel().currentSelectedCategoryIndex = currentLoginCategoryIndex;
    PatternDataModel().currentSelectedPatternIndex = currentLoginPatternIndex;

    /// 画像更新
    state = state.copyWith(
      currentSelectedCategoryIndex: currentLoginCategoryIndex,
      patternsStyle: _updateItemStyle(
        currentLoginCategoryIndex,
        currentLoginPatternIndex,
      ),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      double jumpValue = _getJumpPosition(
          currentLoginPatternIndex, controller.position.maxScrollExtent);

      if (controller.offset <= controller.position.maxScrollExtent) {
        controller.jumpTo(jumpValue);
      }
    });
  }

  @override
  String getCategoryTooltip(int categoryNum) {
    Locale locale = AppLocale().getCurrentLocale();
    AppLocalizations l10n = lookupAppLocalizations(locale);
    final String value;
    switch (categoryNum) {
      case group6:
        value = l10n.tt_utl_category06;
        break;
      case group7:
        value = l10n.tt_utl_category07;
        break;
      case group8:
        value = l10n.tt_utl_category08;
        break;
      case group9:
        value = l10n.tt_utl_category09;
        break;
      case group10:
        value = l10n.tt_utl_category10;
        break;
      case group11:
        value = l10n.tt_utl_category11;
        break;
      case group12:
        value = l10n.tt_utl_category12;
        break;
      case group13:
        value = l10n.tt_utl_category13;
        break;
      case group14:
        value = l10n.tt_utl_category14;
        break;
      case group15:
        value = l10n.tt_utl_category15;
        break;
      case groupD:
        value = l10n.tt_utl_category16;
        break;
      case groupBlock:
        value = l10n.tt_utl_category17;
        break;
      case groupItalic:
        value = l10n.tt_utl_category18;
        break;
      case groupOutline:
        value = l10n.tt_utl_category19;
        break;
      case groupCyril:
        value = l10n.tt_utl_category20;
        break;
      case groupMemory:
        value = l10n.tt_deco_category_pocket;
        break;
      default:
        value = "";
    }

    return value;
  }

  ///
  /// リスト内でジャンプ可能な座標の計算
  ///
  double _getJumpPosition(int patternCount, double maxOffset) {
    ///  Itemの高さ
    const double itemHeight = 168.0;

    ///  行の数
    int rowCount = patternCount ~/ 5;

    /// ジャンプ可能な最大座標
    double maxJumpPositioned = rowCount * itemHeight;

    if (maxJumpPositioned < 0) {
      maxJumpPositioned = 0;
    } else if (maxJumpPositioned > maxOffset) {
      maxJumpPositioned = maxOffset;
    }
    return maxJumpPositioned;
  }
}
