import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

part 'line_motif_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class LineMotifState with _$LineMotifState {
  const factory LineMotifState({
    required bool lineMotifSizePopup,
    required bool lineMotifSpacingPopup,
    required bool lineMotifFlipPopup,
    required FlipSide flipSide,
    required String sizeDisplayValue,
    required String spacingDisplayValue,
    required String flipDisplayValue,
    required bool isSizeDefaultValue,
    required bool isSpacingDefaultValue,
    required bool isFlipDefaultValue,
  }) = _LineMotifState;
}

abstract class LineMotifViewInterface extends ViewModel<LineMotifState> {
  LineMotifViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// Sizeダンパボタン
  ///
  void onCandleSizeClick(context);

  ///
  /// Spacingダンパボタン
  ///
  void onCandleSpacingClick(context);

  ///
  /// Flipダンパボタン
  ///
  void onCandleFlipClick(context);

  ///
  /// Flipタイプの取得
  ///
  bool getMdcFlipLineType();

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;
}
