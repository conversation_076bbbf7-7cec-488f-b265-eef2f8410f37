import 'dart:typed_data';

import 'package:common_component/common_component.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:panel_library/panel_library.dart';

// 生成されるファイル名を指定する（ `生成元ファイル名.freezed.dart` ）
part 'motif_size_popup_view_interface.freezed.dart';

typedef Unit = DisplayUnit;

@freezed
class MotifSizePopupState with _$MotifSizePopupState {
  const factory MotifSizePopupState({
    required bool isSizeMinusToLimit,
    required bool isSizePlusToLimit,
    required String sizeInputValue,
    required bool isDefaultStyle,
  }) = _MotifSizePopupState;
}

///
/// viewとviewModelのabstractクラス
///
abstract class MotifSizePopupStateViewInterface
    extends ViewModel<MotifSizePopupState> {
  MotifSizePopupStateViewInterface(super.state, this.ref);

  ///
  /// providerのref
  ///
  AutoDisposeStateNotifierProviderRef ref;

  ///
  /// OKボタンがクリックされました
  ///
  void onOkButtonClicked();

  ///
  /// チェーンステッチのデフォルト値
  ///
  int get defaultValue;

  ///
  /// 単位取得する
  ///
  Unit get currentSelectedUnit;

  ///
  /// Size増大操作
  ///
  bool plusSize(bool isLongPress);

  ///
  /// Size縮小操作
  ///
  bool miniSize(bool isLongPress);

  ///
  /// 選択されたモチーフサムネイルデータ
  ///
  Uint8List selectedThumbnailData();
}
